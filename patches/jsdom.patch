diff --git a/lib/jsdom/browser/Window.js b/lib/jsdom/browser/Window.js
index 52d011cae61c3688ec64baa5cec411d55edbda9d..5b29f5af81331e49903514000959ea6e15b07ce6 100644
--- a/lib/jsdom/browser/Window.js
+++ b/lib/jsdom/browser/Window.js
@@ -507,7 +507,7 @@ function installOwnProperties(window, options) {
     // [LegacyUnforgeable]:
     window: { configurable: false },
     document: { configurable: false },
-    location: { configurable: false },
+    location: { configurable: true },
     top: { configurable: false }
   });
 
