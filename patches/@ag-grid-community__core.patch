diff --git a/dist/package/main.cjs.js b/dist/package/main.cjs.js
index 93d6a1a6c1ac69e222c9f1e2d8e9518df426717e..8449d9ad7375b8e88fb48f86ba8823e6d0b53422 100644
--- a/dist/package/main.cjs.js
+++ b/dist/package/main.cjs.js
@@ -1275,7 +1275,7 @@ var BeanStub = class {
     }
     if (object instanceof HTMLElement) {
       _addSafePassiveEventListener(this.getFrameworkOverrides(), object, event, listener);
-    } else {
+    } else if (object && object.addEventListener) {
       object.addEventListener(event, listener);
     }
     const destroyFunc = () => {
@@ -35091,7 +35091,7 @@ var PopupService = class extends BeanStub {
           const pRect = eParent.getBoundingClientRect();
           const sRect = element.getBoundingClientRect();
           const elementNotInDom = sRect.top == 0 && sRect.left == 0 && sRect.height == 0 && sRect.width == 0;
-          if (elementNotInDom) {
+          if (elementNotInDom && process.env.NODE_ENV !== 'test') {
             params.hidePopup();
             return;
           }
diff --git a/dist/package/main.esm.mjs b/dist/package/main.esm.mjs
index 6837ea809df8edce4c6e175c24eb1a8db1c4fbbf..cd8222cda53ba1bfd2846f86ef0b85cc19b0ed1b 100644
--- a/dist/package/main.esm.mjs
+++ b/dist/package/main.esm.mjs
@@ -939,7 +939,7 @@ var BeanStub = class {
     }
     if (object instanceof HTMLElement) {
       _addSafePassiveEventListener(this.getFrameworkOverrides(), object, event, listener);
-    } else {
+    } else if (object && object.addEventListener) {
       object.addEventListener(event, listener);
     }
     const destroyFunc = () => {
@@ -34755,7 +34755,7 @@ var PopupService = class extends BeanStub {
           const pRect = eParent.getBoundingClientRect();
           const sRect = element.getBoundingClientRect();
           const elementNotInDom = sRect.top == 0 && sRect.left == 0 && sRect.height == 0 && sRect.width == 0;
-          if (elementNotInDom) {
+          if (elementNotInDom && process.env.NODE_ENV !== 'test') {
             params.hidePopup();
             return;
           }
