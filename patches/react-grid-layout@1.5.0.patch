diff --git a/build/ReactGridLayout.js b/build/ReactGridLayout.js
index 21a03ff2b8077c46fff2bca550439dd822bce12c..09faa473724e1c6c17542635890c5cecac5c4aec 100644
--- a/build/ReactGridLayout.js
+++ b/build/ReactGridLayout.js
@@ -204,7 +204,7 @@ class ReactGridLayout extends React.Component /*:: <Props, State>*/{
         oldDragItem: null,
         oldLayout: null
       });
-      this.onLayoutMaybeChanged(newLayout, oldLayout);
+      this.onLayoutMaybeChanged(newLayout, oldLayout, 'drag');
     });
     _defineProperty(this, "onResizeStart", (i, w, h, _ref4) => {
       let {
@@ -344,7 +344,7 @@ class ReactGridLayout extends React.Component /*:: <Props, State>*/{
         oldLayout: null,
         resizing: false
       });
-      this.onLayoutMaybeChanged(newLayout, oldLayout);
+      this.onLayoutMaybeChanged(newLayout, oldLayout, 'resize');
     });
     // Called while dragging an element. Part of browser native drag/drop API.
     // Native event target might be the layout itself, or an element within the layout.
@@ -493,7 +493,7 @@ class ReactGridLayout extends React.Component /*:: <Props, State>*/{
     });
     // Possibly call back with layout on mount. This should be done after correcting the layout width
     // to ensure we don't rerender with the wrong width.
-    this.onLayoutMaybeChanged(this.state.layout, this.props.layout);
+    this.onLayoutMaybeChanged(this.state.layout, this.props.layout, 'fix');
   }
   static getDerivedStateFromProps(nextProps /*: Props*/, prevState /*: State*/) /*: $Shape<State> | null*/{
     let newLayoutBase;
@@ -538,7 +538,7 @@ class ReactGridLayout extends React.Component /*:: <Props, State>*/{
     if (!this.state.activeDrag) {
       const newLayout = this.state.layout;
       const oldLayout = prevState.layout;
-      this.onLayoutMaybeChanged(newLayout, oldLayout);
+      this.onLayoutMaybeChanged(newLayout, oldLayout, 'update');
     }
   }
 
@@ -552,10 +552,10 @@ class ReactGridLayout extends React.Component /*:: <Props, State>*/{
     const containerPaddingY = this.props.containerPadding ? this.props.containerPadding[1] : this.props.margin[1];
     return nbRow * this.props.rowHeight + (nbRow - 1) * this.props.margin[1] + containerPaddingY * 2 + "px";
   }
-  onLayoutMaybeChanged(newLayout /*: Layout*/, oldLayout /*: ?Layout*/) {
+  onLayoutMaybeChanged(newLayout /*: Layout*/, oldLayout /*: ?Layout*/, type) {
     if (!oldLayout) oldLayout = this.state.layout;
     if (!(0, _fastEquals.deepEqual)(oldLayout, newLayout)) {
-      this.props.onLayoutChange(newLayout);
+      this.props.onLayoutChange(newLayout, type);
     }
   }
   /**
diff --git a/build/ResponsiveReactGridLayout.js b/build/ResponsiveReactGridLayout.js
index 4b342da9fd3ae214387403226fcf2816c36b6c7f..79defab62091ab4c9ca0108a2b167345a06484bc 100644
--- a/build/ResponsiveReactGridLayout.js
+++ b/build/ResponsiveReactGridLayout.js
@@ -86,11 +86,11 @@ class ResponsiveReactGridLayout extends React.Component
     super(...arguments);
     _defineProperty(this, "state", this.generateInitialState());
     // wrap layouts so we do not need to pass layouts to child
-    _defineProperty(this, "onLayoutChange", (layout /*: Layout*/) => {
+    _defineProperty(this, "onLayoutChange", (layout /*: Layout*/, type) => {
       this.props.onLayoutChange(layout, {
         ...this.props.layouts,
         [this.state.breakpoint]: layout
-      });
+      }, type);
     });
   }
   generateInitialState() /*: State*/{
@@ -171,7 +171,7 @@ class ResponsiveReactGridLayout extends React.Component
 
       // callbacks
       this.props.onBreakpointChange(newBreakpoint, newCols);
-      this.props.onLayoutChange(layout, newLayouts);
+      this.props.onLayoutChange(layout, newLayouts, 'fix');
       this.setState({
         breakpoint: newBreakpoint,
         layout: layout,
