diff --git a/index.d.ts b/index.d.ts
index b09063fe9defcee03f130b4a7eac145a28651f75..677edea8615872676e545bbe2b306bd181b72ea2 100644
--- a/index.d.ts
+++ b/index.d.ts
@@ -388,7 +388,7 @@ declare namespace ReactGridLayout {
          * Callback so you can save the layout.
          * Calls back with (currentLayout, allLayouts). allLayouts are keyed by breakpoint.
          */
-        onLayoutChange?(currentLayout: Layout[], allLayouts: Layouts): void;
+        onLayoutChange?(currentLayout: Layout[], allLayouts: Layouts, type: 'drag' | 'resize' | 'fix' | 'update'): void;
 
         /**
          * Callback that triggers when the width changes, so you can modify the layout as needed.
