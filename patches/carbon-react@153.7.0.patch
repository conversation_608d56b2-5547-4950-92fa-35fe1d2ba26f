diff --git a/esm/components/button-minor/button-minor.style.d.ts b/esm/components/button-minor/button-minor.style.d.ts
index 7eaada3a6b4766174623f6f145fac33f27edf0a0..24fbeba0d2d787f074d1e0de212617c636436a9d 100644
--- a/esm/components/button-minor/button-minor.style.d.ts
+++ b/esm/components/button-minor/button-minor.style.d.ts
@@ -1,4 +1,11 @@
 /// <reference types="react" />
-import { ButtonMinorProps } from "./button-minor.component";
-declare const StyledButtonMinor: import("styled-components").StyledComponent<import("react").ForwardRefExoticComponent<import("../button").ButtonProps & import("react").RefAttributes<HTMLAnchorElement | HTMLButtonElement>>, any, ButtonMinorProps, never>;
+import { ButtonMinorProps } from './button-minor.component';
+declare const StyledButtonMinor: import('styled-components').StyledComponent<
+    import('react').ForwardRefExoticComponent<
+        import('../button').ButtonProps & import('react').RefAttributes<HTMLButtonElement>
+    >,
+    any,
+    ButtonMinorProps,
+    never
+>;
 export default StyledButtonMinor;
diff --git a/esm/components/date/date.component.js b/esm/components/date/date.component.js
index 71df086a6d4e7250094110786a5a28594f2ecebf..b0c6d524171ad3e52a5ea4f4b46ad7255c7abff4 100644
--- a/esm/components/date/date.component.js
+++ b/esm/components/date/date.component.js
@@ -65,10 +65,10 @@ export const DateInput = /*#__PURE__*/React.forwardRef(({
   } = useContext(DateRangeContext);
   const [open, setOpen] = useState(false);
   const [selectedDays, setSelectedDays] = useState(() => {
-    const isValidDate = isValidLocaleDate(value, dateFnsLocale());
-    if (!isValidDate) {
-      return undefined;
-    }
+    // const isValidDate = isValidLocaleDate(value, dateFnsLocale());
+    // if (!isValidDate) {
+    //   return undefined;
+    // }
     return checkISOFormatAndLength(value) ? parseISODate(value) : parseDate(format, value);
   });
   const isInitialValue = useRef(true);
diff --git a/esm/components/flat-table/flat-table-body-draggable/flat-table-body-draggable.component.js b/esm/components/flat-table/flat-table-body-draggable/flat-table-body-draggable.component.js
index f74f5128fc0d3340c1a381b08e1674a9731ec07e..7ae3a7cb3fd587192c861c567f19554f1f79b405 100644
--- a/esm/components/flat-table/flat-table-body-draggable/flat-table-body-draggable.component.js
+++ b/esm/components/flat-table/flat-table-body-draggable/flat-table-body-draggable.component.js
@@ -16,9 +16,9 @@ const DropTarget = ({
     hover: (_, monitor) => {
       if (!isDragging && monitor.isOver()) setIsDragging(true);
     },
-    drop() {
+    drop(details) {
       setIsDragging(false);
-      getOrder?.();
+      getOrder?.(details);
     }
   });
   return /*#__PURE__*/React.createElement(StyledFlatTableBodyDraggable, _extends({
@@ -60,12 +60,12 @@ export const FlatTableBodyDraggable = ({
     copyOfDraggableItems.splice(atIndex, 0, draggableItem);
     setDraggableItems(copyOfDraggableItems);
   };
-  const getItemsId = () => {
+  const getItemsId = (details) => {
     if (!getOrder) {
       return;
     }
     const draggableItemIds = draggableItems.map(draggableItem => /*#__PURE__*/React.isValidElement(draggableItem) && draggableItem.props.id);
-    getOrder(draggableItemIds);
+    getOrder(draggableItemIds, details);
   };
   return /*#__PURE__*/React.createElement(DndProvider, {
     backend: HTML5Backend
diff --git a/esm/components/flat-table/flat-table.component.js b/esm/components/flat-table/flat-table.component.js
index 7f3f71ff44be937dc933922c4af749fed1373dba..0503d72cddf361a9e6eedca7272c611f8f4b7e57 100644
--- a/esm/components/flat-table/flat-table.component.js
+++ b/esm/components/flat-table/flat-table.component.js
@@ -158,8 +158,8 @@ export const FlatTable = ({
     flexDirection: "column",
     justifyContent: hasStickyFooter || height ? "space-between" : undefined,
     role: "region",
-    overflowX: width ? "hidden" : undefined,
-    width: width,
+    overflowX: "hidden",
+    width: "unset",
     hasStickyFooter: hasStickyFooter,
     hasVerticalScrollbar: hasVerticalScrollbar,
     hasHorizontalScrollbar: hasHorizontalScrollbar,
diff --git a/esm/components/icon-button/icon-button.component.d.ts b/esm/components/icon-button/icon-button.component.d.ts
index 845f6bd33bb52fd167ebff53028360d6d2e90139..d01d7eb81a118e84484f5b1536f4000e636afead 100644
--- a/esm/components/icon-button/icon-button.component.d.ts
+++ b/esm/components/icon-button/icon-button.component.d.ts
@@ -7,6 +7,8 @@ export interface IconButtonProps extends SpaceProps, TagProps {
     "aria-label"?: string;
     /** Icon meant to be rendered, should be an Icon component */
     children: React.ReactElement<IconProps>;
+    /** Callback triggered on key down */
+    onKeyDown?: (ev: React.KeyboardEvent<HTMLButtonElement>) => void;
     /** Callback triggered on blur */
     onBlur?: (ev: React.FocusEvent<HTMLButtonElement>) => void;
     /** Callback triggered on focus */
diff --git a/esm/components/icon-button/icon-button.component.js b/esm/components/icon-button/icon-button.component.js
index a7b2114c064d6279c9d226372e4f227d6f61da1a..3a52e7f99de60cc785b878a9f8a247a65ed1ff8e 100644
--- a/esm/components/icon-button/icon-button.component.js
+++ b/esm/components/icon-button/icon-button.component.js
@@ -8,6 +8,7 @@ import BatchSelectionContext from "../batch-selection/__internal__/batch-selecti
 const IconButton = /*#__PURE__*/React.forwardRef(({
   "aria-label": ariaLabel,
   onClick,
+  onKeyDown,
   children,
   disabled = false,
   ...rest
@@ -22,6 +23,8 @@ const IconButton = /*#__PURE__*/React.forwardRef(({
     if (Events.isEnterKey(e) || Events.isSpaceKey(e)) {
       e.preventDefault();
       onClick?.(e);
+    } else {
+      onKeyDown?.(e);
     }
   };
   const setRefs = useCallback(reference => {
diff --git a/esm/components/select/__internal__/select-list/select-list.component.js b/esm/components/select/__internal__/select-list/select-list.component.js
index f11eb03d9219c023f14782bc16187aa48e10b7f5..6ba39190b1901fc1efa8e15206e6cd01cf0fff83 100644
--- a/esm/components/select/__internal__/select-list/select-list.component.js
+++ b/esm/components/select/__internal__/select-list/select-list.component.js
@@ -13,6 +13,7 @@ import getNextChildByText from "../utils/get-next-child-by-text";
 import getNextIndexByKey from "../utils/get-next-index-by-key";
 import isNavigationKey from "../utils/is-navigation-key";
 import ListActionButton from "../list-action-button";
+import { isEqual } from "lodash";
 import Loader from "../../../loader";
 import Option from "../../option";
 import SelectListContext from "./select-list.context";
@@ -89,8 +90,11 @@ const SelectList = /*#__PURE__*/React.forwardRef(({
 
   // check if object values are equal
   function shallowEqual(objA, objB) {
-    const keysA = Object.keys(objA);
-    return keysA.every(key => objA[key] === objB[key]);
+    if (objA && objB && typeof objA === "object" && typeof objB === "object") {
+      const keysA = Object.keys(objA);
+      return keysA.every(key => objA[key] === objB[key]);
+    }
+    return isEqual(objA, objB);
   }
   const getIndexOfMatch = useCallback(valueToMatch => {
     return childrenList.findIndex(child => {
diff --git a/esm/components/select/filterable-select/filterable-select.component.js b/esm/components/select/filterable-select/filterable-select.component.js
index 5aad7b280d859a35d55689ac497bacf94a85e99e..3fb2b14cea4c70dfe9d005b14ec266984f627a8b 100644
--- a/esm/components/select/filterable-select/filterable-select.component.js
+++ b/esm/components/select/filterable-select/filterable-select.component.js
@@ -5,6 +5,7 @@ import { filterOutStyledSystemSpacingProps, filterStyledSystemMarginProps } from
 import SelectTextbox from "../__internal__/select-textbox";
 import guid from "../../../__internal__/utils/helpers/guid";
 import withFilter from "../__internal__/utils/with-filter.hoc";
+import isEqual from "lodash/isEqual";
 import StyledSelect from "../select.style";
 import SelectList from "../__internal__/select-list/select-list.component";
 import isExpectedOption from "../__internal__/utils/is-expected-option";
@@ -110,7 +111,7 @@ export const FilterableSelect = /*#__PURE__*/React.forwardRef(({
       const {
         text
       } = child.props;
-      return text?.toLowerCase().indexOf(textToMatch?.toLowerCase()) !== -1;
+      return text && text.toLowerCase() === textToMatch.toLowerCase();
     });
   }
   const updateValues = useCallback((newFilterText, isDeleteEvent) => {
@@ -118,11 +119,15 @@ export const FilterableSelect = /*#__PURE__*/React.forwardRef(({
       const trimmed = newFilterText.trimStart();
       const match = findElementWithMatchingText(trimmed, children);
       const isFilterCleared = isDeleteEvent && !newFilterText.length;
-      if (!match || isFilterCleared || match.props.disabled) {
+      if (isFilterCleared || (match && match.props && match.props.disabled)) {
         setTextValue(newFilterText);
         triggerChange("", false);
         return "";
       }
+      if (match === undefined) {
+        setTextValue(newFilterText);
+        return previousValue;
+      }
       if (trimmed.length) {
         triggerChange(match.props.value, false);
       }
@@ -205,7 +210,7 @@ export const FilterableSelect = /*#__PURE__*/React.forwardRef(({
     !(!isControlled.current || isControlled.current && onChange) ? process.env.NODE_ENV !== "production" ? invariant(false, onChangeMissingMessage) : invariant(false) : void 0;
     if (isControlled.current) {
       setSelectedValue(prevValue => {
-        if (value && prevValue !== value) {
+        if (value && !isEqual(prevValue, value)) {
           setMatchingText(value);
         }
         return value;
diff --git a/lib/components/button-minor/button-minor.style.d.ts b/lib/components/button-minor/button-minor.style.d.ts
index 7eaada3a6b4766174623f6f145fac33f27edf0a0..24fbeba0d2d787f074d1e0de212617c636436a9d 100644
--- a/lib/components/button-minor/button-minor.style.d.ts
+++ b/lib/components/button-minor/button-minor.style.d.ts
@@ -1,4 +1,11 @@
 /// <reference types="react" />
-import { ButtonMinorProps } from "./button-minor.component";
-declare const StyledButtonMinor: import("styled-components").StyledComponent<import("react").ForwardRefExoticComponent<import("../button").ButtonProps & import("react").RefAttributes<HTMLAnchorElement | HTMLButtonElement>>, any, ButtonMinorProps, never>;
+import { ButtonMinorProps } from './button-minor.component';
+declare const StyledButtonMinor: import('styled-components').StyledComponent<
+    import('react').ForwardRefExoticComponent<
+        import('../button').ButtonProps & import('react').RefAttributes<HTMLButtonElement>
+    >,
+    any,
+    ButtonMinorProps,
+    never
+>;
 export default StyledButtonMinor;
