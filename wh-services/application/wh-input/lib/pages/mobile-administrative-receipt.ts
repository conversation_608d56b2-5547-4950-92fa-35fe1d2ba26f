import {
    AdministrativeReceipt,
    AdministrativeReceiptLineInProgress,
    AdministrativeReceiptLineInProgressBinding,
    AdministrativeReceiptLineValidated,
    AdministrativeReceiptMovementInProgressBinding,
    GraphApi,
} from '@sage/wh-input-api';
import { StockNature } from '@sage/wh-master-data-api';
import { receipt } from '@sage/wh-master-data/build/lib/menu-items/receipt';
import { dialogConfirmation, dialogMessage } from '@sage/wh-master-data/lib/client-functions/dialogs';
import {
    AuthorizedOperation,
    getAuthorizedOperator,
} from '@sage/wh-master-data/lib/client-functions/get-authorized-operator';
import {
    DepositorConfiguration,
    getDepositorConfiguration,
} from '@sage/wh-master-data/lib/client-functions/get-depositor-configuration';
import { getCurrentSiteDepositor } from '@sage/wh-master-data/lib/client-functions/get-selected-site-depositor';
import {
    getSiteConfiguration,
    SiteConfiguration,
} from '@sage/wh-master-data/lib/client-functions/get-site-configuration';
import { hightestContainerLevel } from '@sage/wh-master-data/lib/interfaces';
import { Product, ProductContainer, SupplierUpc } from '@sage/wh-product-data-api';
import { FifoManagement } from '@sage/wh-product-data/lib/client-functions/fifo-management';
import { getConsumptionUnit } from '@sage/wh-product-data/lib/client-functions/get-consumption-unit';
import { getProductConfiguration } from '@sage/wh-product-data/lib/client-functions/get-product-configuration';
import { ProductPalettizationPlan } from '@sage/wh-product-data/lib/client-functions/product-palettization-plan';
import {
    ConsumptionUnit,
    ContainerUnit,
    FifoManagementErrorMessages,
    ProductConfiguration,
} from '@sage/wh-product-data/lib/interfaces';
import { setApplicativePageCrudActions } from '@sage/wh-system/lib/client-functions/applicative-crud-actions';
import { decimal, Edges, ExtractEdges, extractEdges, ExtractEdgesPartial, Filter } from '@sage/xtrem-client';
import * as ui from '@sage/xtrem-ui';
import { PartialCollectionValueWithIds } from '@sage/xtrem-ui/build/lib/component/types';

const _fifoErrorMessages: FifoManagementErrorMessages = {
    // manufacturedDateError: undefined,
    // detentionDateError: undefined,
    sellByDateError: ui.localize(
        '@sage/wh-input/validate-error-sell-by-date-need-to-be-earlier-than-detention-date',
        'The Sell by date needs to be earlier than the detention date.',
    ),
    useByDateError: ui.localize(
        '@sage/wh-input/validate-error-use-by-date-need-to-be-earlier-than-detention-date',
        'The Use by date needs to be earlier than the detention date.',
    ),
    shipByDateError: ui.localize(
        '@sage/wh-input/validate-error-ship-by-date-need-to-be-earlier-than-detention-date',
        'The Ship by date needs to be earlier than the detention date.',
    ),
    // fifoDateError: undefined,
};

enum SelectionSection {
    disableAll = 'disableAll',
    main = 'main',
    selectLine = 'selectLine',
    enterLine = 'enterLine',
    enterMovement = 'enterMovement',
}

interface SectionParameters {
    isCreate?: boolean;
    isUpdate?: boolean;
    isDelete?: boolean;
    isSaved?: boolean;
    isReinitialize?: boolean;
}

interface ProductWithSuppliers {
    productCode: string;
    suppliersCode: string[];
}

type ProductsWithSuppliers = ProductWithSuppliers[];

interface CommonProductsWithSuppliers {
    productsCode: string[];
    suppliersCode: string[];
}

type GroupedCommonProductsWithSuppliers = CommonProductsWithSuppliers[];

// Same as import template used by the mutation
interface ImportAdministrativeReceipt {
    code: string;
    site: string;
    operatorCode: string;
    inProgressLines: ImportAdministrativeReceiptLineInProgress[];
}

interface ImportAdministrativeReceiptLineInProgress {
    lineNumber: number;
    expectedInput: string;
    expectedInputLine: number;
    receivedQuantityInConsumptionUnit: number;
    isDispute: boolean;
    isPutaway: boolean;
    stockStatus: string;
    store: string;
    isExclusiveStore: boolean;
    inputContainer: string;
    source: string;
    lotNumber: string;
    supportNumber: string;
    reservationNumber: string;
    manufacturedDate: string | null;
    sellByDate: string | null;
    detentionDate: string | null;
    useByDate: string | null;
    shipByDate: string | null;
    fifoDate: string | null;
    actionImport: string;
    administrativeReceiptMovements: ImportAdministrativeReceiptMovementInProgress[];
}
interface ImportAdministrativeReceiptMovementInProgress {
    container: string;
    numberOfContainers: number;
    homogeneousContainer: string;
    homogeneousQuantity: number;
}

@ui.decorators.page<MobileAdministrativeReceipt>({
    title: 'Receipt',
    isTitleHidden: true,
    mode: 'default',
    menuItem: receipt,
    priority: 100,
    node: '@sage/wh-input/AdministrativeReceipt',
    createAction: undefined,
    authorizationCode: 'CWSRCL',
    access: { node: '@sage/wh-input/AdministrativeReceipt' },
    skipDirtyCheck: true,
    idField() {
        return this.administrativeReceipt.value?._id;
    },
    // idField() {
    //     return this.code;
    // },
    headerCard() {
        switch (this._getActiveSection()) {
            case SelectionSection.main:
                return undefined;
            case SelectionSection.selectLine:
                return {
                    title: this.administrativeReceiptCodeHeader,
                    titleRight: this.administrativeReceiptStatusCodeHeader,
                    line2: this.supplierBarCodeModeHeader,
                    line2Right: this.supplierBarCodeValueHeader,
                };
            case SelectionSection.enterLine:
                return {
                    title: this.productCodeHeader,
                    line2: this.productLocalizedDescriptionHeader,
                    line2Right: this.expectedInputCodeHeader,
                    line3: this.remainingQuantityHeader,
                };
            case SelectionSection.enterMovement:
                return {
                    title: this.productCodeHeader,
                    line2: this.productLocalizedDescriptionHeader,
                    line2Right: this.expectedInputCodeHeader,
                    line3: this.receivedQuantityHeader,
                    line3Right: this.packedQuantityHeader,
                };
            default:
                return undefined;
        }
    },
    headerDropDownActions() {
        return [
            // this.$standardSaveAction,
            // this.$standardCancelAction,
            // this.$standardOpenRecordHistoryAction,
        ];
    },
    async onLoad() {
        if (!(await this._initialize())) {
            this.disablePage();
            // Close the page
            this.$.finish();
        }
    },
    async onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: undefined, // this.$standardSaveAction,
            cancel: undefined, //this.$standardCancelAction,
            remove: undefined, //this.$standardDeleteAction,
        });
    },
    businessActions() {
        return this._getBusinessActions();
    },
    navigationPanel: undefined,
})
export class MobileAdministrativeReceipt extends ui.Page<GraphApi> {
    /** internal properties */
    private _siteCodeSelected?: string;
    private _depositorCodeSelected?: string;
    private _siteConfiguration?: SiteConfiguration;
    private _depositorConfiguration?: DepositorConfiguration;
    private _productConfiguration?: ProductConfiguration;
    private _receiptLines: ExtractEdges<AdministrativeReceiptLineInProgressBinding>[] = [];
    private _productPalettizationPlan: ProductPalettizationPlan;
    private _containerConsumptionOrUpcUnit?: ContainerUnit;
    private _receiptMovementNumber = 1;
    private _receiptLineNumber = 1;
    private _isNewLine = false;
    private _temporaryReceivedMovements: PartialCollectionValueWithIds<AdministrativeReceiptMovementInProgressBinding>[] =
        [];
    private _productsWithSuppliers: ProductsWithSuppliers = [];
    private _groupedProductsWithSuppliers: GroupedCommonProductsWithSuppliers = [];
    private _fifoManagement?: FifoManagement;

    /*
     *
     *  Page Actions
     *
     */

    @ui.decorators.pageAction<MobileAdministrativeReceipt>({
        title: 'Create',
        buttonType: 'primary',
        shortcut: ['f2'],
        isDisabled: true,
        async onError(e) {
            await this._onError_createButton(e);
        },
        async onClick() {
            if (this._siteCodeSelected) {
                const options: ui.dialogs.DialogOptions = {
                    acceptButton: {
                        text: ui.localize('@sage/wh-input/button-accept-ok', 'OK'),
                    },
                    fullScreen: true,
                };

                let _result: any = undefined;

                // Check if the administrative receipt to update and validate contains at least one valid line
                if (!this.administrativeReceiptLines.value.length) {
                    this.$.showToast(
                        ui.localize(
                            '@sage/wh-input/notification-error-administrative-receipt-no-products',
                            `Enter at least one product.`,
                        ),
                        { type: 'error', timeout: 5000 },
                    );
                    return;
                }

                this.createButton.isDisabled = true;

                // Check if operator is authorized
                const _operatorCode = await getAuthorizedOperator(
                    this,
                    this._siteCodeSelected ?? '',
                    AuthorizedOperation.receipt,
                );

                if (_operatorCode === undefined) {
                    this.$.showToast(
                        ui.localize(
                            '@sage/wh-input/notification-error-administrative-receipt-no-operator',
                            `Operator not authorized.`,
                        ),
                        { type: 'error', timeout: 5000 },
                    );
                    this.createButton.isDisabled = false;
                    return;
                }

                if (_operatorCode instanceof Error) {
                    _result = _operatorCode;
                } else {
                    this.$.loader.isHidden = false;

                    //await this.$standardSaveAction.execute(false);
                    _result = await this._callCreationAPI(_operatorCode);

                    this.$.loader.isHidden = true;
                }

                if (_result instanceof Error) {
                    await this._onError_createButton(_result);
                } else {
                    await this.$.sound.success();

                    await dialogMessage(
                        this,
                        'success',
                        ui.localize('@sage/wh-input/dialog-success-title', 'Success'),
                        ui.localize(
                            '@sage/wh-input/dialog-success-administrative-receipt-update',
                            'Document updated: {{receiptId}}.',
                            { receiptId: _result ?? '' },
                        ),
                        options,
                    );
                    await this._setActiveSection(SelectionSection.main, <SectionParameters>{
                        isReinitialize: true,
                    });
                }
            }
        },
    })
    createButton: ui.PageAction;

    private async _onError_createButton(e: any) {
        if (e instanceof Error) {
            let _parameters: SectionParameters | undefined;
            this.$.loader.isHidden = true;
            const options: ui.dialogs.DialogOptions = {
                acceptButton: {
                    text: ui.localize('@sage/wh-input/button-goback', 'Go back'),
                },
                cancelButton: {
                    text: ui.localize('@sage/wh-input/button-cancel', 'Cancel'),
                },
                size: 'small',
                mdContent: true,
            };

            let message = '';
            const _diagnoses = (<any>e)?.errors[0]?.extensions?.diagnoses ?? (<any>e)?.errors;

            if (_diagnoses?.length || e?.message) {
                const _messages = <string[]>[];
                _diagnoses
                    ?.filter((d: { severity: number; message: any }) => (d?.severity ?? 3) > 2 && d.message)
                    ?.forEach((d: { message: any }) => {
                        const _message = d.message.split(`\n`);
                        _messages.push(..._message);
                    });
                const _result = _messages.length ? <string[]>_messages : <string[]>e.message.split(`\n`);
                message = `**${ui.localize(
                    '@sage/wh-input/dialog-error-administrative-receipt-creation',
                    'An error has occurred',
                )}**\n\n`;
                if (_result.length === 1) {
                    message += `${_result[0]}`;
                } else {
                    message += _result.map(item => `* ${item}`).join('\n');
                }
            } else {
                message = `${ui.localize(
                    '@sage/wh-input/pages_creation_error_a-connection-or-webservice-error-has-occurred',
                    'A connection or webservice error error has occurred. Contact your administrator.',
                )}`;
            }
            await this.$.sound.error();
            if (
                !(await dialogConfirmation(
                    this,
                    'error',
                    ui.localize('@sage/wh-input/dialog-error-title', 'Error'),
                    message,
                    options,
                ))
            ) {
                _parameters = <SectionParameters>{ isReinitialize: true };
            }
            await this._setActiveSection(SelectionSection.main, _parameters);
        }
    }

    @ui.decorators.pageAction<MobileAdministrativeReceipt>({
        title: 'Cancel',
        shortcut: ['f4'],
        buttonType: 'secondary',
        isHidden: true,
        async onClick() {
            if (
                await this._dialogConfirmDelete(
                    ui.localize(
                        '@sage/wh-input/dialog__confirm_cancel_action__administrative-receipt_cancelButton',
                        'You will cancel the entry of this level. This action cannot be undone. Do you want to continue?',
                    ),
                )
            ) {
                if (this._getActiveSection() === SelectionSection.enterMovement) {
                    await this._setActiveSection(SelectionSection.enterLine);
                } else {
                    await this._setActiveSection(SelectionSection.main);
                }
            }
        },
    })
    cancelButton: ui.PageAction;

    @ui.decorators.pageAction<MobileAdministrativeReceipt>({
        title: 'Next',
        shortcut: ['f3'],
        buttonType: 'primary',
        isHidden: true,
        async onClick() {
            // Validate page
            const _errors = await this.$.page.validate();
            if (!_errors.length) {
                switch (this._getActiveSection()) {
                    case SelectionSection.enterLine:
                        if (!this.pendingAdministrativeReceiptLines.value.length) {
                            await dialogMessage(
                                this,
                                'error',
                                ui.localize('@sage/wh-input/dialog-error-title', 'Error'),
                                ui.localize(
                                    '@sage/wh-input/pages__mobile_administrative-receipt__notification__no_receipt_error',
                                    `Select at least one receipt line.`,
                                ),
                            );
                            return;
                        }

                        // Replicate or replace all lines to the grid
                        this.pendingAdministrativeReceiptLines.value.forEach(_savedLine => {
                            const _oldLine = this.administrativeReceiptLines.getRecordValue(_savedLine?._id, 0);
                            if (_oldLine) {
                                this.administrativeReceiptLines.removeRecord(_savedLine?._id, 0);
                            }

                            this.administrativeReceiptLines.addRecord(_savedLine, 0, undefined);
                        });

                        await this._setActiveSection(SelectionSection.main);
                        break;

                    case SelectionSection.enterMovement:
                        const _receivedMovementQuantity = this._getMovementReceivedQuantityInConsumptionUnit();
                        // if there are movements, the sum of their quantities must be identical to that of the line
                        if (
                            !_receivedMovementQuantity ||
                            this._getReceivedQuantityInConsumptionUnit() !== _receivedMovementQuantity
                        ) {
                            await dialogMessage(
                                this,
                                'error',
                                ui.localize('@sage/wh-input/dialog-error-title', 'Error'),
                                ui.localize(
                                    '@sage/wh-input/dialog-error-quantity_received_different_than_quantity_of_movements_received',
                                    'Quantity received <> Quantity of movements received.',
                                ),
                            );
                            return;
                        }
                        /* The movements are copied if they are valid in the restoration environment,
                         * which will allow the current state to be recovered when returning to the line
                         * rather than restoring the previous values.
                         */
                        await this._setActiveSection(SelectionSection.enterLine, <SectionParameters>{ isSaved: true });
                        break;

                    default:
                        break;
                }
            }
        },
    })
    receiptButton: ui.PageAction;

    @ui.decorators.pageAction<MobileAdministrativeReceipt>({
        icon: 'three_boxes',
        title: 'Receipt mvts',
        buttonType: 'secondary',
        async onClick() {
            const _line = this.selectedAdministrativeReceiptLines.value[0];

            if ((await this._beforeValidatePageForLine(_line)) && (this.receivedQuantity.value ?? 0 > 0)) {
                const _errors = await this.$.page.validate();
                if (!_errors.length) {
                    await this._setActiveSection(SelectionSection.enterMovement);
                }
            }
        },
    })
    receiptMovementSectionButton: ui.PageAction;

    @ui.decorators.pageAction<MobileAdministrativeReceipt>({
        icon: 'add',
        title: 'Add...',
        buttonType: 'secondary',
        async onClick() {
            if (this._getActiveSection() === SelectionSection.enterMovement) {
                // Store current movement in cache
                const _line = this.selectedAdministrativeReceiptLines.value[0];
                const _product = _line?.product;
                if (_product?.code) {
                    const _errors = await this.$.page.validate();
                    if (!_errors.length) {
                        const _administrativeReceiptMovement = <
                            PartialCollectionValueWithIds<AdministrativeReceiptMovementInProgressBinding>
                        >{
                            site: _line.site,
                            depositor: _line.depositor,
                            receipt: this.administrativeReceipt.value,
                            lineNumber: _line.lineNumber,
                            receiptMovementNumber: this._receiptMovementNumber,
                            product: { ..._product },
                            numberOfConsumptionUnit: String(this._getMovementPackedQuantity()),
                            container: this._productPalettizationPlan.getProductContainer(this.inputContainer.value),
                            numberOfContainers: String(this.numberOfContainers.value),
                            homogeneousContainer: this._productPalettizationPlan.getProductContainer(
                                this.homogeneousContainer.value,
                            ),
                            homogeneousQuantity: String(this.homogeneousQuantity.value),
                            _id: `${this._siteCodeSelected}|${this.administrativeReceipt.value?.code}|${_line.lineNumber}|${this._receiptMovementNumber}`,
                        };

                        this.administrativeReceiptMovements.addRecord(_administrativeReceiptMovement);
                        await this._setActiveSection(SelectionSection.enterMovement);
                        this._receiptMovementNumber++;
                    }
                }
            } else if (this._getActiveSection() === SelectionSection.enterLine) {
                // Store current line in cache
                const _line = this._getAdministrativeReceiptLineInProgressBinding();

                if (await this._beforeValidatePageForLine(_line)) {
                    const _errors = await this.$.page.validate();
                    if (!_errors.length && _line) {
                        // If there are movements, we must have the same number of UCs on the line.
                        const _receivedQuantityInConsumptionUnit = this._getMovementReceivedQuantityInConsumptionUnit();
                        if (
                            _receivedQuantityInConsumptionUnit &&
                            this._getReceivedQuantityInConsumptionUnit(Number(this.receivedQuantity.value ?? 0)) !==
                                _receivedQuantityInConsumptionUnit
                        ) {
                            this.$.showToast(
                                ui.localize(
                                    '@sage/wh-input/dialog-error-quantity_received_different_than_quantity_of_movements_received',
                                    'Quantity received <> Quantity of movements received.',
                                ),
                                { type: 'error', timeout: 5000 },
                            );
                            return;
                        }

                        let _administrativeReceiptLine = <
                            PartialCollectionValueWithIds<AdministrativeReceiptLineInProgressBinding>
                        >{};
                        /*
                         * In order for the update of the line and its movements to be done at once,
                         * it is first necessary to delete the latter from the grid if it exists...
                         * which allows the movements to be deleted at one go as well.
                         */
                        if (!this._isNewLine) {
                            const _oldLine = this.pendingAdministrativeReceiptLines.getRecordValue(_line?._id);
                            if (_oldLine) {
                                this.pendingAdministrativeReceiptLines.removeRecord(_line?._id);
                            }
                            _administrativeReceiptLine = _line;
                        } else {
                            const _newLineNumber = this._receiptLineNumber;
                            const _newId = `${this._siteCodeSelected}|${this.administrativeReceipt.value?.code}|${_line?.expectedInput?.code}|${_line?.expectedInputLine?.lineNumber}|${_newLineNumber}`;

                            _administrativeReceiptLine = <
                                PartialCollectionValueWithIds<AdministrativeReceiptLineInProgressBinding>
                            >{
                                ..._line,
                                lineNumber: _newLineNumber,
                                actionImport: 'C',
                                _id: _newId,
                            };
                            this._receiptLineNumber++;
                        }

                        this.pendingAdministrativeReceiptLines.addRecord(_administrativeReceiptLine);

                        await this._setActiveSection(SelectionSection.enterLine, <SectionParameters>{
                            isCreate: true,
                        });
                    }
                }
            }
        },
    })
    addToReceiptButton: ui.PageAction;

    /**
     * Before validating the page, checks other related fields.
     * @param line The administrative receipt line to validate.
     * @returns True if the validation passes, false otherwise.
     */
    async _beforeValidatePageForLine(
        line: PartialCollectionValueWithIds<AdministrativeReceiptLineInProgressBinding> | undefined,
    ): Promise<boolean> {
        const _product = line?.product;
        if (_product?.code) {
            if (
                this._depositorConfiguration?.isLotNumber &&
                _product?.isKeyInLotNumber &&
                !this.lotNumber.value?.trim()
            ) {
                this.$.showToast(
                    ui.localize('@sage/wh-input/dialog-error-lot_number_required', 'You need to enter a lot number.'),
                    { type: 'error', timeout: 5000 },
                );
                this.lotNumber.focus();
                return false;
            }

            if (
                this._productPalettizationPlan?.productConsumptionUnit?.isKeyInSupportNumbers &&
                !this.supportNumber.value?.trim()
            ) {
                this.$.showToast(
                    ui.localize(
                        '@sage/wh-input/dialog-error-support_number_required',
                        'You need to enter a support number.',
                    ),
                    { type: 'error', timeout: 5000 },
                );
                this.supportNumber.focus();
                return false;
            }
            return true;
        }
        return false;
    }

    /*
     *
     *  Sections
     *
     */

    @ui.decorators.section<MobileAdministrativeReceipt>({
        isTitleHidden: true,
        isHidden: false,
    })
    mainSection: ui.containers.Section;

    @ui.decorators.section<MobileAdministrativeReceipt>({
        isTitleHidden: true,
        isHidden: true,
    })
    selectLineSection: ui.containers.Section;

    @ui.decorators.section<MobileAdministrativeReceipt>({
        isTitleHidden: true,
        isHidden: true,
    })
    enterLineSection: ui.containers.Section;

    @ui.decorators.section<MobileAdministrativeReceipt>({
        isTitleHidden: true,
        isHidden: true,
    })
    enterMovementSection: ui.containers.Section;

    @ui.decorators.section<MobileAdministrativeReceipt>({
        isTitleHidden: true,
        isHidden: true,
    })
    stockHeaderSection: ui.containers.Section;

    /*
     *
     *  Blocks
     *
     */

    @ui.decorators.block<MobileAdministrativeReceipt>({
        parent() {
            return this.mainSection;
        },
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.block<MobileAdministrativeReceipt>({
        parent() {
            return this.mainSection;
        },
    })
    receiptBlock: ui.containers.Block;

    @ui.decorators.gridRowBlock<MobileAdministrativeReceipt>({
        isTitleHidden: true,
        parent() {
            return this.mainSection;
        },
        boundTo() {
            return this.administrativeReceiptLines;
        },
        fieldFilter(columnId: string) {
            return false;
        },
        readOnlyOverride(columnId: string) {
            return undefined;
        },
    })
    gridBlock: ui.containers.GridRowBlock;

    @ui.decorators.block<MobileAdministrativeReceipt>({
        parent() {
            return this.mainSection;
        },
    })
    gridLinesBlock: ui.containers.Block;

    @ui.decorators.block<MobileAdministrativeReceipt>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        isTitleHidden: true,
    })
    customizationAdministrativeReceiptLinesBlock: ui.containers.Block;

    @ui.decorators.block<MobileAdministrativeReceipt>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        isTitleHidden: true,
    })
    customizationAdministrativeReceiptMovementsBlock: ui.containers.Block;

    @ui.decorators.block<MobileAdministrativeReceipt>({
        parent() {
            return this.selectLineSection;
        },
    })
    selectLineBlock: ui.containers.Block;

    @ui.decorators.block<MobileAdministrativeReceipt>({
        parent() {
            return this.enterLineSection;
        },
    })
    enterLineBlock: ui.containers.Block;

    @ui.decorators.block<MobileAdministrativeReceipt>({
        parent() {
            return this.enterMovementSection;
        },
    })
    enterMovementBlock: ui.containers.Block;

    @ui.decorators.block<MobileAdministrativeReceipt>({
        isTitleHidden: true,
        parent() {
            return this.stockHeaderSection;
        },
    })
    stockLineBlock: ui.containers.Block;

    /*
     *
     *  Page properties
     *
     */
    // -----------------------------

    /*
     *
     *  Technical Fields
     *
     */

    /**
     * Case of line selection
     */

    @ui.decorators.textField<MobileAdministrativeReceipt>({
        isTransient: true,
        isFullWidth: true,
        isTitleHidden: true,
    })
    administrativeReceiptCodeHeader: ui.fields.Text<MobileAdministrativeReceipt>;

    @ui.decorators.dropdownListField<MobileAdministrativeReceipt>({
        isTransient: true,
        isFullWidth: true,
        isTitleHidden: true,
        optionType: '@sage/wh-master-data/ReceiptStatus',
    })
    administrativeReceiptStatusCodeHeader: ui.fields.DropdownList;

    @ui.decorators.dropdownListField<MobileAdministrativeReceipt>({
        isTransient: true,
        isFullWidth: true,
        isTitleHidden: true,
        optionType: '@sage/wh-master-data/DefaultMode',
    })
    supplierBarCodeModeHeader: ui.fields.DropdownList;

    @ui.decorators.textField<MobileAdministrativeReceipt>({
        isTransient: true,
        isFullWidth: true,
        isTitleHidden: true,
    })
    supplierBarCodeValueHeader: ui.fields.Text<MobileAdministrativeReceipt>;

    /**
     * * Case of product selection
     */
    @ui.decorators.textField<MobileAdministrativeReceipt>({
        isTransient: true,
        isFullWidth: true,
        isTitleHidden: true,
    })
    productCodeHeader: ui.fields.Text<MobileAdministrativeReceipt>;

    @ui.decorators.textField<MobileAdministrativeReceipt>({
        isTransient: true,
        isFullWidth: true,
        isTitleHidden: true,
    })
    productLocalizedDescriptionHeader: ui.fields.Text<MobileAdministrativeReceipt>;

    @ui.decorators.textField<MobileAdministrativeReceipt>({
        isTransient: true,
        isFullWidth: true,
        isTitleHidden: true,
    })
    expectedInputCodeHeader: ui.fields.Text<MobileAdministrativeReceipt>;

    @ui.decorators.numericField<MobileAdministrativeReceipt>({
        isTransient: true,
        isFullWidth: true,
        isTitleHidden: true,
        prefix() {
            return ui.localize('@sage/wh-input/label-remaining-quantity', 'Remaining quantity :');
        },
    })
    remainingQuantityHeader: ui.fields.Numeric<MobileAdministrativeReceipt>;

    @ui.decorators.numericField<MobileAdministrativeReceipt>({
        isTransient: true,
        isFullWidth: true,
        isTitleHidden: true,
        prefix() {
            return ui.localize('@sage/wh-input/label-received-quantity', 'Rcpt. qty :');
        },
    })
    receivedQuantityHeader: ui.fields.Numeric<MobileAdministrativeReceipt>;

    @ui.decorators.numericField<MobileAdministrativeReceipt>({
        isTransient: true,
        isFullWidth: true,
        isTitleHidden: true,
        prefix() {
            return ui.localize('@sage/wh-input/label-packed-quantity', 'Pkd. qty :');
        },
    })
    packedQuantityHeader: ui.fields.Numeric<MobileAdministrativeReceipt>;

    /*
     *
     *  Fields
     *
     */

    // -----------------------------------------------
    // Block: Main block
    // -----------------------------------------------

    @ui.decorators.referenceField<MobileAdministrativeReceipt, AdministrativeReceipt>({
        parent() {
            return this.mainBlock;
        },
        title: 'Receipt no.',
        placeholder: 'Scan or select...',
        node: '@sage/wh-input/AdministrativeReceipt',
        valueField: 'code',
        isTransient: true,
        isFullWidth: true,
        isHelperTextHidden: true,
        isAutoSelectEnabled: true,
        canFilter: false,
        filter() {
            const _filter: Filter<AdministrativeReceipt> = {
                site: { code: this._siteCodeSelected },
                status: { _ne: 'validated' },
                inProgressLines: {
                    _atLeast: 1,
                    status: { _nin: ['dripAndDropValidated', 'validated'] },
                },
            };

            return _filter;
        },
        async onChange() {
            const _administrativeReceipt = this.administrativeReceipt.value;
            if (_administrativeReceipt?._id) {
                // We start by searching the list of each product and the suppliers attached to it.
                this._productsWithSuppliers = await this._getProductsOnAdministrativeReceipt();
                // Then we create a list of product groups with the same supplier.
                this._groupedProductsWithSuppliers = this._groupProductsByCommonSuppliers();
            } else {
                this._productsWithSuppliers = [];
                this._groupedProductsWithSuppliers = [];
            }

            this._showWorkflowLayout(!!_administrativeReceipt);
            await this.$.commitValueAndPropertyChanges();
        },
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'ID',
                isReadOnly: true,
                isHidden: true,
            }),
            ui.nestedFields.text({
                bind: 'code',
                title: 'Receipt no.',
                isReadOnly: true,
            }),
            ui.nestedFields.select({
                bind: 'status',
                title: 'Status',
                isReadOnly: true,
                optionType: '@sage/wh-master-data/ReceiptStatus',
            }),
            ui.nestedFields.date({
                bind: 'date',
                title: 'Date',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'time',
                title: 'Time',
                isReadOnly: true,
            }),
            ui.nestedFields.reference({
                bind: 'carrierAppointment',
                title: 'Appoint. no.',
                isReadOnly: true,
                node: '@sage/wh-master-data/AppointmentManagement',
                valueField: 'code',
            }),
            ui.nestedFields.reference({
                bind: 'dock',
                title: 'Location',
                isReadOnly: true,
                node: '@sage/wh-master-data/Location',
                valueField: 'code',
            }),
        ],
    })
    administrativeReceipt: ui.fields.Reference;

    // -----------------------------------------------
    // Block: filter Lines block
    // -----------------------------------------------

    @ui.decorators.checkboxField<MobileAdministrativeReceipt>({
        parent() {
            return this.receiptBlock;
        },
        title: 'Drip and Drop',
        isTransient: true,
        isReadOnly: true,
    })
    isDripAndDropAllowed: ui.fields.Checkbox;

    @ui.decorators.dropdownListField<MobileAdministrativeReceipt>({
        parent() {
            return this.receiptBlock;
        },
        title: 'Default mode',
        optionType: '@sage/wh-master-data/DefaultMode',
        isMandatory: true,
        isTransient: true,
        async onChange() {
            if (this.defaultMode.value) {
                this._ChangeSelectionMode(this.defaultMode.value);
            }
        },
    })
    defaultMode: ui.fields.DropdownList;

    @ui.decorators.referenceField<MobileAdministrativeReceipt, AdministrativeReceiptLineInProgress>({
        parent() {
            return this.receiptBlock;
        },
        title: 'Supplier barcode',
        placeholder: 'Scan or select...',
        isTransient: true,
        isDisabled: false,
        isFullWidth: true,
        isHelperTextHidden: true,
        isAutoSelectEnabled: true,
        node: '@sage/wh-input/AdministrativeReceiptLineInProgress',
        valueField: { expectedInput: { referenceNumber: true } },
        isMandatory(value, rowValue) {
            return this.defaultMode.value === 'supplierBc' && !this.administrativeReceiptLines.value.length;
        },
        filter() {
            const filter: Filter<AdministrativeReceiptLineInProgress> = {
                site: { code: this._siteCodeSelected },
                depositor: { code: this._depositorCodeSelected },
                receipt: { code: this.administrativeReceipt.value?.code },
                expectedInput: { referenceNumber: { _ne: ' ' } },
                expectedInputLine: { referenceNumber: { _ne: ' ' } },
            };
            return filter;
        },
        columns: [
            ui.nestedFields.text({
                // bind: 'supplierBarCode',
                bind: { expectedInput: { referenceNumber: true } },
                isTransient: true,
                isHidden: true,
                isReadOnly: true,
                // TODO: Must be implementer after platform feature delivery
                // getValue() {
                //     const _siteConfiguration = this._currentSiteConfiguration;
                //     const _orderReference = this.supplierBarCode.value?.expectedInput?.referenceNumber?.trim();
                //     const _lineReference = this.supplierBarCode.value?.expectedInputLine?.referenceNumber?.trim();

                //     return !_siteConfiguration || !_orderReference || !_lineReference
                //         ? ''
                //         : _orderReference?.padEnd(
                //               1 +
                //                   _siteConfiguration.supplierBarCodeRange1[1] -
                //                   _siteConfiguration.supplierBarCodeRange1[0],
                //               ' ',
                //           ) +
                //               _lineReference?.padEnd(
                //                   1 +
                //                       _siteConfiguration.supplierBarCodeRange2[1] -
                //                       _siteConfiguration.supplierBarCodeRange2[0],
                //                   ' ',
                //               );
                // },
            }),
            ui.nestedFields.reference({
                title: 'Reference expected input',
                bind: 'expectedInput',
                node: '@sage/wh-input/ExpectedInput',
                valueField: 'referenceNumber',
                isReadOnly: true,
            }),
            ui.nestedFields.reference({
                title: 'Reference line',
                bind: 'expectedInputLine',
                node: '@sage/wh-input/ExpectedInputLine',
                valueField: 'referenceNumber',
                isReadOnly: true,
            }),
            ui.nestedFields.reference({
                title: 'Product',
                bind: 'product',
                node: '@sage/wh-product-data/Product',
                valueField: 'code',
                isReadOnly: true,
            }),
            ui.nestedFields.numeric({
                title: 'Expected quantity',
                bind: { expectedInputLine: { expectedQuantityInConsumptionUnit: true } },
                isReadOnly: true,
            }),
            ui.nestedFields.date({
                title: 'Date',
                bind: { expectedInput: { date: true } },
                isReadOnly: true,
            }),
        ],
        async onChange() {
            if (this.supplierBarCode.value) {
                const _siteConfiguration = this._siteConfiguration;
                const _orderReference = this.supplierBarCode.value?.expectedInput?.referenceNumber?.trim();
                const _lineReference = this.supplierBarCode.value?.expectedInputLine?.referenceNumber?.trim();
                if (
                    !_siteConfiguration ||
                    !_orderReference ||
                    !_lineReference ||
                    _orderReference.length < _siteConfiguration.supplierBarCodeRange1[1] ||
                    _lineReference.length <
                        Math.min(
                            _siteConfiguration.supplierBarCodeRange2[0] - _siteConfiguration.supplierBarCodeRange1[1],
                            0,
                        )
                ) {
                    this.$.showToast(
                        ui.localize(
                            '@sage/wh-input/dialog-error-the-ei-reference-and-the-supplier-barcode-definition-need-to-be-the-same-length',
                            'The EI reference and the supplier barcode definition need to be the same length.',
                        ),
                        { type: 'error', timeout: 5000 },
                    );
                    this.supplierBarCode.focus();
                    return;
                }

                // TODO: Must be implementer after platform feature delivery
                // Example of supplier barcode
                // const _supplierBarCode =
                //     _orderReference?.padEnd(
                //         1 + _siteConfiguration.supplierBarCodeRange1[1] - _siteConfiguration.supplierBarCodeRange1[0],
                //         ' ',
                //     ) +
                //     _lineReference?.padEnd(
                //         1 + _siteConfiguration.supplierBarCodeRange2[1] - _siteConfiguration.supplierBarCodeRange2[0],
                //         ' ',
                //     );

                //  Extract order and line reference from supplier barcode
                // const _supplierBarCode = this.supplierBarCode.value ?? '';
                // const _orderReference = _supplierBarCode
                //     .slice(_siteConfiguration.supplierBarCodeRange1[0] - 1, _siteConfiguration.supplierBarCodeRange1[1])
                //     .trim();
                // const _lineReference = _supplierBarCode
                //     .slice(_siteConfiguration.supplierBarCodeRange2[0] - 1, _siteConfiguration.supplierBarCodeRange2[1])
                //     .trim();
                // Get administrative lines
                this._receiptLines = await this._getAdministrativeLinesBySupplierBarCode(
                    _orderReference,
                    _lineReference,
                );
            }
            this._updateProductState();
        },
    })
    supplierBarCode: ui.fields.Reference;

    @ui.decorators.referenceField<MobileAdministrativeReceipt, SupplierUpc>({
        parent() {
            return this.receiptBlock;
        },
        title: 'Supplier EAN code',
        placeholder: 'Scan or select...',
        isTransient: true,
        isDisabled: false,
        isFullWidth: true,
        isHelperTextHidden: true,
        isAutoSelectEnabled: true,
        node: '@sage/wh-product-data/SupplierUpc',
        valueField: 'upcCode',
        isMandatory(value, rowValue) {
            return this.defaultMode.value === 'eanCode' && !this.administrativeReceiptLines.value.length;
        },
        filter() {
            const filter: Filter<SupplierUpc> = {
                site: { code: this._siteCodeSelected },
                depositor: { code: this._depositorCodeSelected },
                ...this._getFilterSupplierProductUpc(),
            };
            return filter;
        },
        columns: [
            ui.nestedFields.text({
                bind: 'upcCode',
                title: 'Product',
                isReadOnly: true,
            }),
            ui.nestedFields.reference({
                title: 'Product',
                bind: 'product',
                node: '@sage/wh-product-data/Product',
                valueField: 'code',
                isReadOnly: true,
            }),
            ui.nestedFields.reference({
                title: 'Container',
                bind: 'container',
                node: '@sage/wh-product-data/Container',
                valueField: 'code',
                isReadOnly: true,
            }),
            ui.nestedFields.reference({
                title: 'Supplier',
                bind: 'supplier',
                node: '@sage/wh-master-data/Supplier',
                valueField: 'code',
                isReadOnly: true,
            }),
        ],
        async onChange() {
            if (this.supplierEanCode.value) {
                // Get administrative lines
                this._receiptLines = await this._getAdministrativeLinesBySupplierUpcCode(this.supplierEanCode.value);
                if (!this._receiptLines.length) {
                    this.$.showToast(
                        ui.localize(
                            '@sage/wh-input/dialog-error-the-upc-code-is-missing-from-this-receipt',
                            'The UPC code is missing from this receipt.',
                        ),
                        { type: 'error', timeout: 5000 },
                    );
                    this.product.value = null;
                    this.supplierEanCode.focus();
                    return;
                }
            } else {
                this._receiptLines = [];
            }
            this.product.value = this.supplierEanCode.value?.product ?? null;
            await this.$.commitValueAndPropertyChanges();
            await this.product.executeOnChange();
        },
    })
    supplierEanCode: ui.fields.Reference;

    @ui.decorators.referenceField<MobileAdministrativeReceipt, AdministrativeReceiptLineInProgress>({
        parent() {
            return this.receiptBlock;
        },
        title: 'Support no.',
        placeholder: 'Scan or select...',
        isTransient: true,
        isDisabled: false,
        isFullWidth: true,
        isHelperTextHidden: true,
        isAutoSelectEnabled: true,
        node: '@sage/wh-input/AdministrativeReceiptLineInProgress',
        valueField: 'supportNumber',
        isMandatory(value, rowValue) {
            return this.defaultMode.value === 'support' && !this.administrativeReceiptLines.value.length;
        },
        filter() {
            const filter: Filter<AdministrativeReceiptLineInProgress> = {
                site: { code: this._siteCodeSelected },
                receipt: { code: this.administrativeReceipt.value?.code },
                depositor: { code: this._depositorCodeSelected },
                supportNumber: { _ne: ' ' },
            };
            return filter;
        },
        columns: [
            ui.nestedFields.text({
                bind: 'supportNumber',
                title: 'Support no.',
                isReadOnly: true,
            }),
            ui.nestedFields.reference({
                title: 'Reference expected input',
                bind: 'expectedInput',
                node: '@sage/wh-input/ExpectedInput',
                valueField: 'referenceNumber',
                isReadOnly: true,
            }),
            ui.nestedFields.reference({
                title: 'Reference line',
                bind: 'expectedInputLine',
                node: '@sage/wh-input/ExpectedInputLine',
                valueField: 'referenceNumber',
                isReadOnly: true,
            }),
            ui.nestedFields.reference({
                title: 'Product',
                bind: 'product',
                node: '@sage/wh-product-data/Product',
                valueField: 'code',
                isReadOnly: true,
            }),
            ui.nestedFields.numeric({
                title: 'Expected quantity',
                bind: { expectedInputLine: { expectedQuantityInConsumptionUnit: true } },
                isReadOnly: true,
            }),
            ui.nestedFields.date({
                title: 'Date',
                bind: { expectedInput: { date: true } },
                isReadOnly: true,
            }),
        ],
        async onChange() {
            if (this.supportNumberCode.value?.supportNumber) {
                this._receiptLines = await this._getAdministrativeLinesBySupportNumber(
                    this.supportNumberCode.value?.supportNumber,
                );
                if (!this._receiptLines.length) {
                    this.$.showToast(
                        ui.localize(
                            '@sage/wh-input/dialog-error-support-number-is-missing-from-this-receipt',
                            'The support number is missing from this receipt.',
                        ),
                        { type: 'error', timeout: 5000 },
                    );
                    this.supportNumberCode.focus();
                    return;
                }
            } else {
                this._receiptLines = [];
            }
            this._updateProductState();
        },
    })
    supportNumberCode: ui.fields.Reference;

    @ui.decorators.referenceField<MobileAdministrativeReceipt, Product>({
        parent() {
            return this.receiptBlock;
        },
        title: 'Product code',
        placeholder: 'Scan or select...',
        isTransient: true,
        isFullWidth: true,
        isHelperTextHidden: true,
        isAutoSelectEnabled: true,
        node: '@sage/wh-product-data/Product',
        valueField: 'code',
        isDisabled: false,
        isMandatory: false,
        columns: [
            ui.nestedFields.text({
                bind: 'code',
                title: 'Product',
                isReadOnly: true,
            }),
        ],
        filter() {
            const _filter: Filter<Product> = {
                site: { code: this._siteCodeSelected },
                depositor: { code: this._depositorCodeSelected },
            };
            return _filter;
        },
        async onChange() {
            this.selectedAdministrativeReceiptLines.value = [];

            do {
                if (this.product.value?.code) {
                    const _productConfiguration = await getProductConfiguration(
                        this,
                        this._siteCodeSelected,
                        this._depositorCodeSelected,
                        this.product.value?.code,
                    );

                    this._productConfiguration = _productConfiguration;

                    if (!_productConfiguration) {
                        this.$.showToast(
                            ui.localize(
                                '@sage/wh-input/dialog-error-product-configuration-not-found',
                                'Product configuration not found.',
                            ),
                            { type: 'error', timeout: 5000 },
                        );
                        break;
                    }

                    this._fifoManagement?.Initialize(_productConfiguration);

                    /**
                     * The lines obtained are filtered by the requested article
                     */
                    const _currentLines =
                        this._receiptLines?.filter(_ => _.product?.code === this.product.value?.code) ?? [];
                    if (!_currentLines.length) {
                        this.$.showToast(
                            ui.localize(
                                '@sage/wh-input/dialog-error-the-product-code-is-missing-from-this-receipt',
                                'The product code is missing from this receipt.',
                            ),
                            { type: 'error', timeout: 5000 },
                        );
                        this.product.focus();
                        break;
                    }

                    this.selectedAdministrativeReceiptLines.value = _currentLines;
                    await this._setActiveSection(SelectionSection.selectLine);
                    return;
                }
            } while (false);

            this._productConfiguration = undefined;

            await this.$.commitValueAndPropertyChanges();
        },
    })
    product: ui.fields.Reference;

    // -----------------------------------------------
    // Block: grid lines block
    // -----------------------------------------------

    @ui.decorators.nestedGridField<
        MobileAdministrativeReceipt,
        [AdministrativeReceiptLineInProgressBinding, AdministrativeReceiptMovementInProgressBinding]
    >({
        parent() {
            return this.gridLinesBlock;
        },
        bind: 'inProgressLines',
        canFilter: true,
        canActivate: true,
        isChangeIndicatorDisabled: true,
        canSelect: false,
        isTitleHidden: true,
        levels: [
            {
                node: '@sage/wh-input/AdministrativeReceiptLineInProgress',
                orderBy: { _id: 1 },
                childProperty: 'administrativeReceiptMovements',
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        title: 'ID',
                        isHiddenDesktop: false,
                        isHidden: true,
                    }),
                    ui.nestedFields.text({
                        title: 'Action import',
                        bind: 'actionImport',
                        isHidden: true,
                    }),
                    ui.nestedFields.reference<
                        MobileAdministrativeReceipt,
                        AdministrativeReceiptLineInProgressBinding,
                        Product
                    >({
                        bind: 'product',
                        title: 'Product',
                        node: '@sage/wh-product-data/Product',
                        valueField: { code: true },
                        isReadOnly: true,
                        columns: [
                            ui.nestedFields.text({
                                bind: '_id',
                                isHidden: true,
                            }),
                            ui.nestedFields.text({
                                bind: 'code',
                                title: 'Code',
                            }),
                        ],
                    }),
                    ui.nestedFields.numeric({
                        bind: 'receivedQuantityInConsumptionUnit',
                        title: 'Quantity',
                        postfix(value, rowValue) {
                            return (<ConsumptionUnit>(<any>rowValue?.product)?.consumptionUnit)?.code ?? '';
                        },
                        scale(value, rowValue?: any) {
                            return rowValue?.product?.stockUnit?.numberOfDecimals ?? 0;
                        },
                    }),
                    ui.nestedFields.reference<MobileAdministrativeReceipt, AdministrativeReceiptLineInProgressBinding>({
                        bind: 'receipt',
                        title: 'Receipt',
                        node: '@sage/wh-input/AdministrativeReceiptLineInProgress',
                        valueField: { code: true },
                        isReadOnly: true,
                        columns: [
                            ui.nestedFields.text({
                                bind: '_id',
                                isHidden: true,
                            }),
                            ui.nestedFields.text({
                                bind: 'code',
                                title: 'Code',
                            }),
                        ],
                    }),
                    ui.nestedFields.numeric({
                        bind: 'lineNumber',
                        title: 'Line number',
                    }),
                    ui.nestedFields.reference<MobileAdministrativeReceipt, AdministrativeReceiptLineInProgressBinding>({
                        bind: 'expectedInput',
                        title: 'Expected input',
                        node: '@sage/wh-input/ExpectedInput',
                        valueField: { code: true },
                        isReadOnly: true,
                        columns: [
                            ui.nestedFields.text({
                                bind: '_id',
                                isHidden: true,
                            }),
                            ui.nestedFields.text({
                                bind: 'code',
                                title: 'Code',
                            }),
                        ],
                    }),
                    ui.nestedFields.reference<
                        MobileAdministrativeReceipt,
                        AdministrativeReceiptLineInProgressBinding,
                        ProductContainer
                    >({
                        bind: 'inputContainer',
                        title: 'Input container',
                        isReadOnly: true,
                        node: '@sage/wh-product-data/ProductContainer',
                        valueField: { container: { code: true } },
                    }),
                ],
                dropdownActions: [
                    {
                        icon: 'edit',
                        title: 'Edit line',
                        async onClick(recordId: string) {
                            const _line = this.administrativeReceiptLines.getRecordValue(recordId, 0);
                            if (_line) {
                                this.selectedAdministrativeReceiptLines.value = [_line];
                                await this._setActiveSection(SelectionSection.enterLine, <SectionParameters>{
                                    isUpdate: true,
                                });
                            }
                        },
                    },
                    {
                        icon: 'duplicate',
                        title: 'Duplicate',
                        isDisabled(recordId, rowItem, level, parentIds) {
                            const _line = this.administrativeReceiptLines.value.length
                                ? this.administrativeReceiptLines.getRecordValue(recordId, 0)
                                : undefined;
                            return (
                                !_line ||
                                ((_line?.expectedInput?.receiptMode?.isExceedQuantityControl ?? false) &&
                                    !this._getInitialRemainingQuantityInConsumptionUnitForExpectedLine(_line))
                            );
                        },
                        async onClick(recordId: string) {
                            const _line = this.administrativeReceiptLines.getRecordValue(recordId, 0);
                            if (_line) {
                                this.selectedAdministrativeReceiptLines.value = [_line];
                                await this._setActiveSection(SelectionSection.enterLine, <SectionParameters>{
                                    isCreate: true,
                                });
                            }
                        },
                    },
                    {
                        icon: 'delete',
                        title: 'Delete',
                        isDisabled(recordId: string) {
                            // We cannot delete a line when exists linked lines.
                            const _line = this.administrativeReceiptLines.getRecordValue(recordId, 0);
                            return (
                                !_line ||
                                (this.administrativeReceiptLines.value.length > 1 &&
                                    _line?.actionImport !== 'C' &&
                                    this.administrativeReceiptLines.value.findIndex(
                                        _ =>
                                            _.expectedInput?.code === _line.expectedInput?.code &&
                                            _.expectedInputLine?.lineNumber === _line.expectedInputLine?.lineNumber &&
                                            _.actionImport === 'C',
                                    ) !== -1)
                            );
                        },
                        async onClick(recordId: string) {
                            if (
                                await this._dialogConfirmDelete(
                                    ui.localize(
                                        '@sage/wh-input/dialog__confirm_delete_action__administrative-receipt__line',
                                        'You are going to delete this line. This action cannot be undone. Do you want to continue?',
                                    ),
                                )
                            ) {
                                this.administrativeReceiptLines.removeRecord(recordId, 0);
                                if (!(await this._updateMainSectionInformation())) {
                                    this.$.setPageClean();
                                }
                            }
                        },
                    },
                ],
                // sidebar: {
                //     layout() {
                //         return {
                //             mainSection: {
                //                 title: 'Administrative receipt lines',
                //                 blocks: {
                //                     mainBlock: {
                //                         fields: this._getSidebarAdministrativeReceiptLine(),
                //                     },
                //                 },
                //             },
                //         };
                //     },
                // },
            },
            {
                node: '@sage/wh-input/AdministrativeReceiptMovementInProgress',
                orderBy: { _id: 1 },
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        title: 'ID',
                        isHidden: true,
                    }),
                    ui.nestedFields.reference({
                        title: 'Site',
                        node: '@sage/wh-system/Site',
                        bind: 'site',
                        isReadOnly: true,
                        isHidden: true,
                        valueField: { code: true },
                        columns: [
                            ui.nestedFields.text({
                                bind: '_id',
                                isHidden: true,
                            }),
                            ui.nestedFields.text({
                                bind: 'code',
                                title: 'Code',
                            }),
                        ],
                    }),
                    ui.nestedFields.reference({
                        title: 'Depositor',
                        node: '@sage/wh-master-data/Depositor',
                        bind: 'site',
                        isReadOnly: true,
                        isHidden: true,
                        valueField: { code: true },
                        columns: [
                            ui.nestedFields.text({
                                bind: '_id',
                                isHidden: true,
                            }),
                            ui.nestedFields.text({
                                bind: 'code',
                                title: 'Code',
                            }),
                        ],
                    }),
                    ui.nestedFields.reference({
                        title: 'Administrative receipt',
                        node: '@sage/wh-input/AdministrativeReceipt',
                        bind: 'receipt',
                        isReadOnly: true,
                        isHidden: true,
                        valueField: { code: true },
                        columns: [
                            ui.nestedFields.text({
                                bind: '_id',
                                isHidden: true,
                            }),
                            ui.nestedFields.text({
                                bind: 'code',
                                title: 'Code',
                            }),
                        ],
                    }),
                    ui.nestedFields.numeric({
                        title: 'Line number',
                        bind: 'lineNumber',
                        isReadOnly: true,
                        isHidden: true,
                    }),
                    ui.nestedFields.numeric({
                        title: 'Movement number',
                        bind: 'receiptMovementNumber',
                        isReadOnly: true,
                        isHidden: true,
                    }),
                    ui.nestedFields.numeric({
                        title: 'Quantity',
                        bind: 'numberOfConsumptionUnit',
                        isReadOnly: true,
                        postfix(value, rowValue) {
                            return (<ConsumptionUnit>(<any>rowValue?.product)?.consumptionUnit)?.code ?? '';
                        },
                        scale(value, rowValue?: any) {
                            return rowValue?.product?.stockUnit?.numberOfDecimals ?? 0;
                        },
                    }),
                    ui.nestedFields.text({
                        bind: '_emptyField' as any,
                        isReadOnly: true,
                        isTransient: true,
                    }),
                    ui.nestedFields.reference({
                        title: 'Container',
                        node: '@sage/wh-product-data/ProductContainer',
                        bind: 'container',
                        isReadOnly: true,
                        isHidden: true,
                        valueField: { container: { code: true } },
                        columns: [
                            ui.nestedFields.text({
                                bind: '_id',
                                isHidden: true,
                            }),
                        ],
                    }),
                    ui.nestedFields.numeric({
                        title: 'Number',
                        bind: 'numberOfContainers',
                        isReadOnly: true,
                        scale(value, rowValue?: any) {
                            return rowValue?.container?.container?.code ===
                                (<ConsumptionUnit>rowValue?.product?.consumptionUnit)?.code
                                ? (rowValue?.product?.stockUnit?.numberOfDecimals ?? 0)
                                : 0;
                        },
                        postfix(value, rowValue) {
                            const _homogeneousQuantity = ui.formatNumberToCurrentLocale(
                                Number(rowValue?.homogeneousQuantity ?? 0),
                                rowValue?.homogeneousContainer?.container?.code ===
                                    (<ConsumptionUnit>rowValue?.product?.consumptionUnit)?.code
                                    ? (rowValue?.product?.stockUnit?.numberOfDecimals ?? 0)
                                    : 0,
                            );
                            return `${rowValue?.container?.container.code ?? ''} of ${_homogeneousQuantity} ${rowValue?.homogeneousContainer?.container?.code ?? ''}`;
                        },
                    }),
                    ui.nestedFields.reference({
                        title: 'Homogeneous container',
                        node: '@sage/wh-product-data/ProductContainer',
                        bind: 'homogeneousContainer',
                        isReadOnly: true,
                        isHidden: true,
                        valueField: { container: { code: true } },
                        columns: [
                            ui.nestedFields.text({
                                bind: '_id',
                                isHidden: true,
                            }),
                        ],
                    }),
                    ui.nestedFields.numeric({
                        title: 'Number',
                        bind: 'numberOfContainers',
                        isReadOnly: true,
                        isHidden: true,
                        postfix(value, rowValue) {
                            return (<any>rowValue)?.homogeneousContainer ?? '';
                        },
                        scale(value, rowValue?: any) {
                            return rowValue?.homogeneousContainer?.container?.code ===
                                (<ConsumptionUnit>rowValue?.product?.consumptionUnit)?.code
                                ? (rowValue?.product?.stockUnit?.numberOfDecimals ?? 0)
                                : 0;
                        },
                    }),
                    ui.nestedFields.text({
                        title: 'ID',
                        bind: '_id',
                        isReadOnly: true,
                        isHidden: true,
                    }),
                ],
                // dropdownActions: [
                //     {
                //         icon: 'box_arrow_left',
                //         title: 'Edit movement',
                //         isDisabled() {
                //             return false;
                //         },
                //         async onClick(rowId) {
                //             this.administrativeReceiptLines.openSidebar(rowId, 1);
                //         },
                //     },
                // ],
                // sidebar: {
                //     layout() {
                //         return {
                //             mainSection: {
                //                 title: 'Administrative receipt movements',
                //                 blocks: {
                //                     mainBlock: {
                //                         fields: this._getSidebarAdministrativeReceiptMovement(),
                //                     },
                //                 },
                //             },
                //         };
                //     },
                // },
            },
        ],
    })
    administrativeReceiptLines: ui.fields.NestedGrid<
        [AdministrativeReceiptLineInProgressBinding, AdministrativeReceiptMovementInProgressBinding]
    >;

    // -----------------------------------------------
    // Block: Select line block
    // -----------------------------------------------

    @ui.decorators.tableField<MobileAdministrativeReceipt, AdministrativeReceiptLineInProgressBinding>({
        parent() {
            return this.selectLineBlock;
        },
        node: '@sage/wh-input/AdministrativeReceiptLineInProgress',
        isTransient: true,
        isFullWidth: true,
        isTitleHidden: true,
        canFilter: false,
        canSelect: false,
        canExport: false,
        canUserHideColumns: false,
        displayMode: ui.fields.TableDisplayMode.comfortable,
        mobileCard: undefined,
        orderBy: {
            expectedInput: { code: 1 },
            expectedInputLine: { lineNumber: 1 },
            lineNumber: 1,
        },
        columns: [
            ui.nestedFields.reference({
                title: 'Expected input',
                node: '@sage/wh-input/ExpectedInput',
                bind: 'expectedInput',
                isReadOnly: true,
                valueField: { code: true },
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        isHidden: true,
                    }),
                    ui.nestedFields.text({
                        bind: 'code',
                        title: 'Code',
                    }),
                ],
            }),
            ui.nestedFields.reference({
                title: 'Expected input line',
                node: '@sage/wh-input/ExpectedInputLine',
                bind: 'expectedInputLine',
                isReadOnly: true,
                isHidden: true,
                valueField: { lineNumber: true },
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        isHidden: true,
                    }),
                    ui.nestedFields.text({
                        bind: 'code',
                        title: 'Code',
                    }),
                ],
            }),
            ui.nestedFields.text({
                title: 'Reference number',
                bind: { expectedInput: { referenceNumber: true } },
                isReadOnly: true,
            }),
            ui.nestedFields.reference({
                title: 'Product',
                node: '@sage/wh-product-data/Product',
                bind: 'product',
                isReadOnly: true,
                valueField: { code: true },
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        isHidden: true,
                    }),
                    ui.nestedFields.text({
                        bind: 'code',
                        title: 'Code',
                    }),
                ],
            }),
            ui.nestedFields.numeric({
                title: 'Expected quantity of CU',
                bind: { expectedInputLine: { expectedQuantityInConsumptionUnit: true } },
                isReadOnly: true,
                postfix(value, rowValue) {
                    return (<ConsumptionUnit>(<any>rowValue?.product)?.consumptionUnit)?.code ?? '';
                },
                scale(value, rowValue?: any) {
                    return Number((<ConsumptionUnit>rowValue?.product?.consumptionUnit)?.numberOfDecimals ?? 0);
                },
            }),
            ui.nestedFields.text({
                title: '',
                bind: '_emptyField' as any,
                isReadOnly: true,
                isTransient: true,
            }),
            ui.nestedFields.reference<
                MobileAdministrativeReceipt,
                AdministrativeReceiptLineInProgressBinding,
                ProductContainer
            >({
                bind: 'inputContainer',
                title: 'Input container',
                isReadOnly: true,
                node: '@sage/wh-product-data/ProductContainer',
                valueField: { container: { code: true } },
            }),
            ui.nestedFields.numeric({
                title: 'Line no.',
                bind: 'lineNumber',
                isReadOnly: true,
                isHidden: true,
            }),
            ui.nestedFields.text({
                title: 'Support number',
                bind: 'supportNumber',
                isReadOnly: true,
                isTransient: true,
                isHidden: true,
            }),
            ui.nestedFields.text({
                title: 'ID',
                bind: '_id',
                isReadOnly: true,
                isHidden: true,
            }),
            ui.nestedFields.text({
                title: 'Action import',
                bind: 'actionImport',
                isReadOnly: true,
                isHidden: true,
            }),
        ],
        async onRowClick(recordId: string, rowItem: AdministrativeReceiptLineInProgressBinding) {
            await this._onRowClick_Selected(recordId, rowItem);
        },
    })
    selectedAdministrativeReceiptLines: ui.fields.Table<AdministrativeReceiptLineInProgressBinding>;

    // -----------------------------------------------
    // Block: Enter line block
    // -----------------------------------------------

    /*
     * The quantity entered here is either in consumption units or in the unit of the imposed UPC container.
     *  The quantity entered in the field is then converted to the consumption unit to check the quantity received.
     */

    @ui.decorators.numericField<MobileAdministrativeReceipt>({
        parent() {
            return this.enterLineBlock;
        },
        title: 'Quantity',
        placeholder: 'Enter...',
        width: 'small',
        isTransient: true,
        isMandatory: true,
        min: 0,
        isNotZero() {
            return !this.pendingAdministrativeReceiptLines.value.length;
        },
        // isNotZero: this.pendingQuantityInConsumptionUnit.value.length,
        postfix(value, rowValue) {
            return this._containerConsumptionOrUpcUnit?.code ?? '';
        },
        async validation(value: number) {
            const selectedLines = this.selectedAdministrativeReceiptLines.value;
            if (selectedLines.length) {
                const regex = /\d*/; // reg ex for any positive numbers (integers or decimals) including 0
                const _receiptMode = selectedLines[0]?.expectedInput?.receiptMode;

                if (
                    (value.toString().match(regex)?.length ?? 0) === 0 ||
                    (!value && !this._getPendingQuantityInConsumptionUnit())
                ) {
                    return ui.localize('@sage/wh-input/validate-error-invalid-value', 'Invalid value');
                }

                if (value) {
                    if (
                        this._containerConsumptionOrUpcUnit?.containerLevel !== 'level5' &&
                        _receiptMode?.isExceedQuantityControl &&
                        this._getInitialRemainingQuantityInConsumptionUnit() <
                            Number(this._containerConsumptionOrUpcUnit?.numberOfConsumptionUnit ?? 0)
                    ) {
                        return ui.localize(
                            '@sage/wh-input/dialog-error-line-quantity-does-not-fill-the-container',
                            'The line quantity does not fill the container.',
                        );
                    }

                    if (
                        _receiptMode?.isExceedQuantityControl &&
                        this._getReceivedQuantityInConsumptionUnit(value) >
                            this._getInitialRemainingQuantityInConsumptionUnit()
                    ) {
                        return ui.localize(
                            '@sage/wh-input/dialog-error-quantity_received_greater_than_expected',
                            'Quantity received > Quantity expected.',
                        );
                    }
                }
            }

            return undefined;
        },
        scale(value, rowValue?: any) {
            return Number(this._containerConsumptionOrUpcUnit?.numberOfDecimals ?? 0);
        },
        async onChange() {
            this._updateReceiptButtonsState();
            await this.$.commitValueAndPropertyChanges();
            const _receivedQuantityInConsumptionUnit = this._getMovementReceivedQuantityInConsumptionUnit();
            if (
                _receivedQuantityInConsumptionUnit &&
                this._getReceivedQuantityInConsumptionUnit(Number(this.receivedQuantity.value ?? 0)) !==
                    _receivedQuantityInConsumptionUnit
            ) {
                this.$.showToast(
                    ui.localize(
                        '@sage/wh-input/dialog-error-quantity_received_different_than_quantity_of_movements_received',
                        'Quantity received <> Quantity of movements received.',
                    ),
                    { type: 'error', timeout: 5000 },
                );
            }
        },
    })
    receivedQuantity: ui.fields.Numeric;

    @ui.decorators.dropdownListField<MobileAdministrativeReceipt>({
        parent() {
            return this.enterLineBlock;
        },
        title: 'Input container',
        placeholder: 'Scan or select...',
        isMandatory: true,
        isTransient: true,
    })
    inputContainerUnit: ui.fields.DropdownList;

    @ui.decorators.referenceField<MobileAdministrativeReceipt, StockNature>({
        parent() {
            return this.enterLineBlock;
        },
        title: 'Stock nature',
        node: '@sage/wh-master-data/StockNature',
        valueField: 'code',
        placeholder: 'Scan or select...',
        isTransient: true,
        canFilter: false,
        isAutoSelectEnabled: true,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                isHidden: true,
            }),
            ui.nestedFields.text({
                title: 'Stock nature',
                isReadOnly: true,
                bind: 'code',
            }),
            ui.nestedFields.text({
                title: 'Description',
                isReadOnly: true,
                bind: 'localizedDescription',
            }),
            ui.nestedFields.text({
                title: 'Type',
                isReadOnly: true,
                bind: { type: true },
            }),
        ],
        filter() {
            return {
                site: { code: this._siteCodeSelected },
                type: 'general',
            };
        },
        async onChange() {
            this._updateStoreValues(this.selectedAdministrativeReceiptLines.value[0]);
        },
    })
    stockStatus: ui.fields.Reference;

    @ui.decorators.checkboxField<MobileAdministrativeReceipt>({
        parent() {
            return this.enterLineBlock;
        },
        title: 'Dispute',
        isTransient: true,
        isDisabled: true,
        isHidden: true,
    })
    isDispute: ui.fields.Checkbox;

    @ui.decorators.checkboxField<MobileAdministrativeReceipt>({
        parent() {
            return this.enterLineBlock;
        },
        title: 'Putaway',
        isTransient: true,
        isDisabled: true,
        isHidden: true,
    })
    isPutaway: ui.fields.Checkbox;

    @ui.decorators.referenceField<MobileAdministrativeReceipt>({
        parent() {
            return this.enterLineBlock;
        },
        title: 'Store',
        node: '@sage/wh-master-data/Store',
        valueField: 'code',
        isTransient: true,
        isDisabled: true,
        isHidden: true,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                isHidden: true,
            }),
            ui.nestedFields.reference({
                bind: 'site',
                isHidden: true,
                valueField: 'code',
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        isHidden: true,
                    }),
                    ui.nestedFields.text({
                        bind: 'code',
                        title: 'Code',
                    }),
                ],
            }),
            ui.nestedFields.text({
                title: 'Store',
                isReadOnly: true,
                bind: 'code',
            }),
            ui.nestedFields.text({
                title: 'Description',
                isReadOnly: true,
                bind: 'localizedDescription',
            }),
        ],
    })
    store: ui.fields.Reference;

    @ui.decorators.checkboxField<MobileAdministrativeReceipt>({
        parent() {
            return this.enterLineBlock;
        },
        title: 'Exclusive',
        isTransient: true,
        isDisabled: true,
        isHidden: true,
    })
    isExclusiveStore: ui.fields.Checkbox;

    @ui.decorators.textField<MobileAdministrativeReceipt>({
        parent() {
            return this.enterLineBlock;
        },
        title: 'Lot number',
        placeholder: 'Scan...',
        isTransient: true,
    })
    lotNumber: ui.fields.Text;

    @ui.decorators.textField<MobileAdministrativeReceipt>({
        parent() {
            return this.enterLineBlock;
        },
        title: 'Reservation number',
        placeholder: 'Scan...',
        isTransient: true,
    })
    reservationNumber: ui.fields.Text;

    @ui.decorators.textField<MobileAdministrativeReceipt>({
        parent() {
            return this.enterLineBlock;
        },
        title: 'Support number',
        placeholder: 'Scan...',
        isTransient: true,
    })
    supportNumber: ui.fields.Text;

    @ui.decorators.dateField<MobileAdministrativeReceipt>({
        parent() {
            return this.enterLineBlock;
        },
        title: 'Manufacturing date',
        //     placeholder: 'Select...',
        isTransient: true,
        async onChange() {
            await this._fifoManagement?.onChangeManufacturedDate(this);
        },
    })
    manufacturedDate: ui.fields.Date;

    @ui.decorators.dateField<MobileAdministrativeReceipt>({
        parent() {
            return this.enterLineBlock;
        },
        title: 'Sell by date',
        //     placeholder: 'Select...',
        isTransient: true,
        validation(value: string) {
            return this._fifoManagement?.onValidationSellByDate(value);
        },
        async onChange() {
            await this._fifoManagement?.onChangeSellByDate(this);
        },
    })
    sellByDate: ui.fields.Date;

    @ui.decorators.dateField<MobileAdministrativeReceipt>({
        parent() {
            return this.enterLineBlock;
        },
        title: 'Detention date',
        //     placeholder: 'Select...',
        isTransient: true,
        async onChange() {
            await this._fifoManagement?.onChangeDetentionDate(this);
        },
    })
    detentionDate: ui.fields.Date;

    @ui.decorators.dateField<MobileAdministrativeReceipt>({
        parent() {
            return this.enterLineBlock;
        },
        title: 'Use by date',
        //     placeholder: 'Select...',
        isTransient: true,
        validation(value: string) {
            return this._fifoManagement?.onValidationUseByDate(value);
        },
        async onChange() {
            await this._fifoManagement?.onChangeUseByDate(this);
        },
    })
    useByDate: ui.fields.Date;

    @ui.decorators.dateField<MobileAdministrativeReceipt>({
        parent() {
            return this.enterLineBlock;
        },
        title: 'Send by date',
        //     placeholder: 'Select...',
        isTransient: true,
        validation(value: string) {
            return this._fifoManagement?.onValidationShipByDate(value);
        },
        async onChange() {
            await this._fifoManagement?.onChangeShipByDate(this);
        },
    })
    shipByDate: ui.fields.Date;

    @ui.decorators.dateField<MobileAdministrativeReceipt>({
        parent() {
            return this.enterLineBlock;
        },
        title: 'FIFO date',
        //     placeholder: 'Select...',
        isTransient: true,
        async onChange() {
            await this._fifoManagement?.onChangeFifoDate(this);
        },
    })
    fifoDate: ui.fields.Date;

    @ui.decorators.tableField<MobileAdministrativeReceipt, AdministrativeReceiptLineInProgressBinding>({
        parent() {
            return this.enterLineBlock;
        },
        node: '@sage/wh-input/AdministrativeReceiptLineInProgress',
        isTransient: true,
        isFullWidth: true,
        isTitleHidden: true,
        canFilter: false,
        canSelect: false,
        canExport: false,
        canUserHideColumns: false,
        displayMode: ui.fields.TableDisplayMode.comfortable,
        mobileCard: undefined,
        orderBy: {
            expectedInput: { code: 1 },
            expectedInputLine: { lineNumber: 1 },
            lineNumber: 1,
        },
        columns: [
            ui.nestedFields.reference({
                title: 'Expected input',
                node: '@sage/wh-input/ExpectedInput',
                bind: 'expectedInput',
                isReadOnly: true,
                valueField: { code: true },
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        isHidden: true,
                    }),
                    ui.nestedFields.text({
                        bind: 'code',
                        title: 'Code',
                    }),
                ],
            }),
            ui.nestedFields.reference({
                title: 'Expected input line',
                node: '@sage/wh-input/ExpectedInputLine',
                bind: 'expectedInputLine',
                isReadOnly: true,
                isHidden: true,
                valueField: { lineNumber: true },
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        isHidden: true,
                    }),
                    ui.nestedFields.text({
                        bind: 'code',
                        title: 'Code',
                    }),
                ],
            }),
            ui.nestedFields.text({
                title: 'Reference number',
                bind: { expectedInput: { referenceNumber: true } },
                isReadOnly: true,
            }),
            ui.nestedFields.reference({
                title: 'Product',
                node: '@sage/wh-product-data/Product',
                bind: 'product',
                isReadOnly: true,
                valueField: { code: true },
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        isHidden: true,
                    }),
                    ui.nestedFields.text({
                        bind: 'code',
                        title: 'Code',
                    }),
                ],
            }),
            ui.nestedFields.numeric({
                title: 'Quantity',
                bind: 'receivedQuantityInConsumptionUnit',
                isReadOnly: true,
                postfix(value, rowValue) {
                    return (<ConsumptionUnit>(<any>rowValue?.product)?.consumptionUnit)?.code ?? '';
                },
                scale(value, rowValue?: any) {
                    return Number((<ConsumptionUnit>rowValue?.product?.consumptionUnit)?.numberOfDecimals ?? 0);
                },
            }),
            ui.nestedFields.numeric({
                title: 'Line no.',
                bind: 'lineNumber',
                isReadOnly: true,
                isHidden: true,
            }),
            ui.nestedFields.text({
                title: 'SupportNumber',
                bind: 'supportNumber',
                isReadOnly: true,
                isTransient: true,
                isHidden: true,
            }),
            ui.nestedFields.text({
                title: 'ID',
                bind: '_id',
                isReadOnly: true,
                isHidden: true,
            }),
            ui.nestedFields.text({
                title: 'Action import',
                bind: 'actionImport',
                isHidden: true,
            }),
        ],
        // async onRowClick(recordId: string, rowItem: AdministrativeReceiptLineInProgressBinding) {
        //     await this._onRowClick_Selected(recordId, rowItem);
        // },
        dropdownActions: [
            {
                icon: 'bin',
                title: 'Delete',
                isDestructive: true,
                isDisabled(recordId: string) {
                    // We cannot delete a line when exists linked lines.
                    const _line = this.pendingAdministrativeReceiptLines.getRecordValue(recordId);
                    return (
                        !_line ||
                        (_line.actionImport !== 'C' && this.pendingAdministrativeReceiptLines.value.length > 1)
                    );
                },
                async onClick(recordId: string) {
                    if (
                        await this._dialogConfirmDelete(
                            ui.localize(
                                '@sage/wh-input/dialog__confirm_delete_action__administrative-receipt__line',
                                'You are going to delete this line. This action cannot be undone. Do you want to continue?',
                            ),
                        )
                    ) {
                        this.pendingAdministrativeReceiptLines.removeRecord(recordId);
                        // let this field know that the value has changed (to trigger a refresh)
                        this.pendingAdministrativeReceiptLines.value = this.pendingAdministrativeReceiptLines.value;
                        await this._updateReceivedQuantityAfterDelete();
                    }
                },
            },
        ],
        fieldActions() {
            return [this.receiptMovementSectionButton, this.addToReceiptButton];
        },
    })
    pendingAdministrativeReceiptLines: ui.fields.Table<AdministrativeReceiptLineInProgressBinding>;

    // -----------------------------------------------
    // Block: Enter Movement block
    // -----------------------------------------------

    @ui.decorators.dropdownListField<MobileAdministrativeReceipt>({
        parent() {
            return this.enterMovementBlock;
        },
        title: 'Container',
        placeholder: 'Scan or select...',
        isMandatory: true,
        isTransient: true,
        options(value, rowValue) {
            return this._productPalettizationPlan.getProductContainerUnitOptions();
        },
        async onChange() {
            if (this.inputContainer.value) {
                await this._productPalettizationPlan?.onChangeInputContainer(
                    this,
                    this._getMovementRemainingQuantityInConsumptionUnit(),
                );
                this.packedQuantity.value = this._getMovementPackedQuantity();
            }
        },
    })
    inputContainer: ui.fields.DropdownList;

    @ui.decorators.numericField<MobileAdministrativeReceipt>({
        parent() {
            return this.enterMovementBlock;
        },
        title: 'Quantity',
        placeholder: 'Enter...',
        width: 'small',
        isTransient: true,
        isMandatory: true,
        min: 0,
        scale(value, rowValue) {
            return this._productPalettizationPlan.getProductContainerUnitPrecision(this.inputContainer.value);
        },
        async validation(value: number) {
            if (this._getMovementRemainingQuantityInConsumptionUnit()) {
                const regex = /[[1-9]\d*/; // reg ex for any positive numbers (integers or decimals) excluding 0
                if ((value.toString().match(regex)?.length ?? 0) === 0) {
                    return ui.localize('@sage/wh-input/validate-error-invalid-value', 'Invalid value');
                }
            }
            return undefined;
        },
        async onChange() {
            if (this.inputContainer.value && this.homogeneousContainer.value) {
                await this._productPalettizationPlan?.onChangeNumberOfContainers(
                    this,
                    this._getMovementRemainingQuantityInConsumptionUnit(),
                );
            }
            this.packedQuantity.value = this._getMovementPackedQuantity();
        },
    })
    numberOfContainers: ui.fields.Numeric;

    @ui.decorators.dropdownListField<MobileAdministrativeReceipt>({
        parent() {
            return this.enterMovementBlock;
        },
        title: 'Homogeneous container',
        placeholder: 'Scan or select...',
        isMandatory: true,
        isTransient: true,
        options(value, rowValue) {
            return this._productPalettizationPlan.getProductContainerUnitOptions();
        },
        async validation(value: string) {
            if (
                value &&
                this.inputContainer.value &&
                !(await this._productPalettizationPlan.validateContainerUnit(this.inputContainer.value, value))
            ) {
                return ui.localize(
                    '@sage/wh-input/validate-error-container-the-container-is-missing-from-the-palletization-plan',
                    'The container is missing from the palletization plan: {{ inputContainerCode }}.',
                    { inputContainerCode: this.inputContainer.value },
                );
            }
            return undefined;
        },
        async onChange() {
            if (
                this.inputContainer.value &&
                this.homogeneousContainer.value &&
                !this.homogeneousContainer.isDisabled &&
                !this.homogeneousQuantity.isDisabled
            ) {
                await this._productPalettizationPlan.onChangeHomogeneousContainer(
                    this,
                    this._getMovementRemainingQuantityInConsumptionUnit(),
                );

                this.packedQuantity.value = this._getMovementPackedQuantity();
            }
        },
    })
    homogeneousContainer: ui.fields.DropdownList;

    @ui.decorators.numericField<MobileAdministrativeReceipt>({
        parent() {
            return this.enterMovementBlock;
        },
        title: 'Number',
        placeholder: 'Enter...',
        width: 'small',
        isTransient: true,
        isMandatory: true,
        min: 0,
        async validation(value: number) {
            if (this._getMovementRemainingQuantityInConsumptionUnit()) {
                const regex = /[[1-9]\d*/; // reg ex for any positive numbers (integers or decimals) excluding 0
                if ((value.toString().match(regex)?.length ?? 0) === 0) {
                    return ui.localize('@sage/wh-input/validate-error-invalid-value', 'Invalid value');
                }
                if (this._getMovementPackedQuantity() > this._getMovementRemainingQuantityInConsumptionUnit()) {
                    return ui.localize(
                        '@sage/wh-input/dialog-error-quantity_received_greater_than_expected',
                        'Quantity received > Quantity expected.',
                    );
                }
            }

            return undefined;
        },
        async onChange() {
            this.packedQuantity.value = this._getMovementPackedQuantity();
        },
        scale(value, rowValue?) {
            return this._productPalettizationPlan.getProductContainerUnitPrecision(this.homogeneousContainer.value);
        },
    })
    homogeneousQuantity: ui.fields.Numeric;

    @ui.decorators.numericField<MobileAdministrativeReceipt>({
        parent() {
            return this.enterMovementBlock;
        },
        title: 'Pack quantity',
        width: 'small',
        isTransient: true,
        isDisabled: true,
        scale(value, rowValue?) {
            return this._productPalettizationPlan.getProductContainerUnitPrecision();
        },
    })
    packedQuantity: ui.fields.Numeric;

    @ui.decorators.tableField<MobileAdministrativeReceipt, AdministrativeReceiptMovementInProgressBinding>({
        parent() {
            return this.enterMovementBlock;
        },
        node: '@sage/wh-input/AdministrativeReceiptMovementInProgress',
        title: 'Receipt movements to add',
        canFilter: false,
        canSelect: false,
        canExport: false,
        canResizeColumns: false,
        canUserHideColumns: false,
        isTitleHidden: false,
        isTransient: true,
        isFullWidth: true,
        isDisabled: false,
        mobileCard: undefined,
        orderBy: { site: { code: 1 }, receipt: { code: 1 }, lineNumber: 1, receiptMovementNumber: 1 },
        columns: [
            ui.nestedFields.reference({
                title: 'Site',
                node: '@sage/wh-system/Site',
                bind: 'site',
                isReadOnly: true,
                isHidden: true,
                valueField: { code: true },
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        isHidden: true,
                    }),
                    ui.nestedFields.text({
                        bind: 'code',
                        title: 'Code',
                    }),
                ],
            }),
            ui.nestedFields.reference({
                title: 'Depositor',
                node: '@sage/wh-master-data/Depositor',
                bind: 'site',
                isReadOnly: true,
                isHidden: true,
                valueField: { code: true },
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        isHidden: true,
                    }),
                    ui.nestedFields.text({
                        bind: 'code',
                        title: 'Code',
                    }),
                ],
            }),
            ui.nestedFields.reference({
                title: 'Administrative receipt',
                node: '@sage/wh-input/AdministrativeReceipt',
                bind: 'receipt',
                isReadOnly: true,
                isHidden: true,
                valueField: { code: true },
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        isHidden: true,
                    }),
                    ui.nestedFields.text({
                        bind: 'code',
                        title: 'Code',
                    }),
                ],
            }),
            ui.nestedFields.numeric({
                title: 'Line number',
                bind: 'lineNumber',
                isReadOnly: true,
                isHidden: true,
            }),
            ui.nestedFields.numeric({
                title: 'Movement number',
                bind: 'receiptMovementNumber',
                isReadOnly: true,
                isHidden: true,
            }),
            ui.nestedFields.numeric({
                title: 'Quantity',
                bind: 'numberOfConsumptionUnit',
                isReadOnly: true,
                postfix(value, rowValue) {
                    return (<ConsumptionUnit>(<any>rowValue?.product)?.consumptionUnit)?.code ?? '';
                },
                scale(value, rowValue?) {
                    return this._productPalettizationPlan.getProductContainerUnitPrecision();
                },
            }),
            ui.nestedFields.text({
                bind: '_emptyField' as any,
                isReadOnly: true,
                isTransient: true,
            }),
            ui.nestedFields.reference({
                title: 'Container',
                node: '@sage/wh-product-data/ProductContainer',
                bind: 'container',
                isReadOnly: true,
                isHidden: true,
                valueField: { container: { code: true } },
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        isHidden: true,
                    }),
                    ui.nestedFields.text({
                        bind: 'code',
                        title: 'Code',
                    }),
                ],
            }),
            ui.nestedFields.numeric({
                title: 'Number',
                bind: 'numberOfContainers',
                isReadOnly: true,
                postfix(value, rowValue) {
                    const _homogeneousQuantity = ui.formatNumberToCurrentLocale(
                        Number(rowValue?.homogeneousQuantity ?? 0),
                        this._productPalettizationPlan.getProductContainerUnitPrecision(
                            rowValue?.homogeneousContainer?.container?.code,
                        ),
                    );

                    return `${rowValue?.container?.container.code ?? ''} of ${_homogeneousQuantity} ${rowValue?.homogeneousContainer?.container?.code ?? ''}`;
                },
                scale(value, rowValue?) {
                    return this._productPalettizationPlan.getProductContainerUnitPrecision(
                        rowValue?.container?.container?.code,
                    );
                },
            }),
            ui.nestedFields.reference({
                title: 'Homogeneous container',
                node: '@sage/wh-product-data/ProductContainer',
                bind: 'homogeneousContainer',
                valueField: { container: { code: true } },
                isReadOnly: true,
                isHidden: true,
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        isHidden: true,
                    }),
                    ui.nestedFields.text({
                        bind: 'code',
                        title: 'Code',
                    }),
                ],
            }),
            ui.nestedFields.numeric({
                title: 'Number',
                bind: 'numberOfContainers',
                isReadOnly: true,
                isHidden: true,
                postfix(value, rowValue) {
                    return (<any>rowValue)?.homogeneousContainer ?? '';
                },
                scale(value, rowValue?) {
                    return this._productPalettizationPlan.getProductContainerUnitPrecision(
                        rowValue?.homogeneousContainer?.container?.code,
                    );
                },
            }),
            ui.nestedFields.text({
                title: 'ID',
                bind: '_id',
                isReadOnly: true,
                isHidden: true,
            }),
        ],
        dropdownActions: [
            {
                icon: 'bin',
                title: 'Delete',
                isDestructive: true,
                async onClick(recordId: string) {
                    if (
                        await this._dialogConfirmDelete(
                            ui.localize(
                                '@sage/wh-input/dialog__confirm_delete_action__administrative-receipt__movement',
                                'You are going to delete this movement. This action cannot be undone. Do you want to continue?',
                            ),
                        )
                    ) {
                        this.administrativeReceiptMovements.removeRecord(recordId);
                        // let this field know that the value has changed (to trigger a refresh)
                        this.administrativeReceiptMovements.value = this.administrativeReceiptMovements.value;
                        await this._updateReceivedAndPackedQuantityAfterDelete();
                    }
                },
            },
        ],
        fieldActions() {
            return [this.addToReceiptButton];
        },
    })
    administrativeReceiptMovements: ui.fields.Table<AdministrativeReceiptMovementInProgressBinding>;
    // -----------------------------------------------
    // Code section
    // -----------------------------------------------

    /**
     * Create a new Sidebar for administrative receipt lines
     * @returns
     */
    private _getSidebarAdministrativeReceiptLine(): ui.SidebarFieldDefinition<AdministrativeReceiptLineInProgressBinding>[] {
        const _FieldsSidebar: ui.SidebarFieldDefinition<AdministrativeReceiptLineInProgressBinding>[] = [];
        this.customizationAdministrativeReceiptLinesBlock.layout.$layout?.$items.forEach(item => {
            if (item.$bind) {
                _FieldsSidebar.push(
                    item.$bind as ui.SidebarFieldDefinition<AdministrativeReceiptLineInProgressBinding>,
                );
            }
        });
        return _FieldsSidebar;
    }

    /**
     *  Create a new Sidebar for administrative receipt movements
     * @returns
     */
    private _getSidebarAdministrativeReceiptMovement(): ui.SidebarFieldDefinition<AdministrativeReceiptMovementInProgressBinding>[] {
        const _FieldsSidebar: ui.SidebarFieldDefinition<AdministrativeReceiptMovementInProgressBinding>[] = [];
        this.customizationAdministrativeReceiptMovementsBlock.layout.$layout?.$items.forEach(item => {
            if (item.$bind) {
                _FieldsSidebar.push(
                    item.$bind as ui.SidebarFieldDefinition<AdministrativeReceiptMovementInProgressBinding>,
                );
            }
        });
        return _FieldsSidebar;
    }

    /**
     * Initialize the business actions for the page
     * @returns
     */
    private _getBusinessActions(): ui.PageAction<MobileAdministrativeReceipt>[] {
        switch (this._getActiveSection()) {
            case SelectionSection.main:
                return [this.cancelButton, this.createButton];

            case SelectionSection.enterLine:
                return [this.cancelButton, this.receiptButton];

            case SelectionSection.enterMovement:
                return [this.cancelButton, this.receiptButton];

            default:
                /**
                 * The order in which the buttons are displayed is defined by the list,
                 * knowing that the first will be on the right, the second on the left...
                 */
                return [
                    // this.$standardSaveAction,
                    // this.$standardCancelAction,
                    // this.$standardNewAction,
                    // this.$standardDeleteAction,
                    this.cancelButton,
                    this.receiptButton,
                    this.createButton,
                ];
        }
    }

    private async _initialize(): Promise<boolean> {
        if (!(await this._initSiteDepositor())) {
            return false;
        }

        this._productPalettizationPlan = new ProductPalettizationPlan(
            this.inputContainer,
            this.numberOfContainers,
            this.homogeneousContainer,
            this.homogeneousQuantity,
        );

        this._fifoManagement = new FifoManagement(
            this.manufacturedDate,
            this.detentionDate,
            this.sellByDate,
            this.useByDate,
            this.shipByDate,
            this.fifoDate,
            _fifoErrorMessages,
        );

        await this._setActiveSection(SelectionSection.main);

        if (!(await this._initDefaultMode())) {
            return false;
        }
        return this._initFieldsDestination();
    }

    private async _initSiteDepositor(): Promise<boolean> {
        const siteDepositor = await getCurrentSiteDepositor(
            this,
            ui.localize('@sage/wh-input/dialog-error-title', 'Error'),
            ui.localize(
                '@sage/wh-input/dialog-error-location-inquiry-set-site-depositor',
                'Define a default site and depositor on the user function profile.',
            ),
        );
        if (siteDepositor && siteDepositor?.site && siteDepositor?.depositor) {
            this._siteCodeSelected = siteDepositor?.site;
            this._depositorCodeSelected = siteDepositor?.depositor;
            return true;
        }
        return false;
    }

    /**
     * Load current site configuration, assign default mode and full list
     */
    private async _initDefaultMode(): Promise<boolean> {
        if ((await this._getSiteAndDepositorConfiguration()) && this._siteConfiguration) {
            this.isDripAndDropAllowed.value = this._siteConfiguration.isDripAndDropAllowed;
            this.defaultMode.value = this._siteConfiguration.eanDefaultMethod;
            await this._ChangeSelectionMode(this._siteConfiguration.eanDefaultMethod);
            return true;
        }
        return false;
    }

    /**
     * Load current site and depositor configuration
     *  @returns  true when done
     */
    private async _getSiteAndDepositorConfiguration(): Promise<boolean> {
        const _siteConfiguration = await getSiteConfiguration(this, this._siteCodeSelected);
        this._siteConfiguration = _siteConfiguration;

        if (!_siteConfiguration) {
            this._depositorConfiguration = undefined;
            this.$.showToast(
                ui.localize(
                    '@sage/wh-input/dialog-error-site-configuration-not-found',
                    'Site configuration not found.',
                ),
                { type: 'error', timeout: 5000 },
            );
            return false;
        }

        const _depositorConfiguration = await getDepositorConfiguration(
            this,
            this._siteCodeSelected,
            this._depositorCodeSelected,
        );
        this._depositorConfiguration = _depositorConfiguration;
        if (!_depositorConfiguration) {
            this.$.showToast(
                ui.localize(
                    '@sage/wh-input/dialog-error-depositor-configuration-not-found',
                    'Depositor configuration not found.',
                ),
                { type: 'error', timeout: 5000 },
            );
            return false;
        }

        return true;
    }

    /** Initialize primary page */
    private async _initFieldsDestination(): Promise<boolean> {
        return !!this._siteCodeSelected && !!this._depositorCodeSelected && (await this._initEnterMainBlock());
    }

    /**
     * Reinitialize fields for main block filter
     * @param newDefaultValue or empty string to reset all fields
     * @returns true when done
     */
    private async _initEnterMainBlock(newDefaultValue: string = ''): Promise<boolean> {
        this._reinitializedWorkingLineAndMovements();
        switch (newDefaultValue) {
            case 'supplierBc':
                this.supplierBarCode.value = null;
                break;

            case 'eanCode':
                this.supplierEanCode.value = null;
                break;

            case 'support':
                this.supportNumberCode.value = null;
                break;

            default:
                this.supplierBarCode.value = null;
                this.supplierEanCode.value = null;
                this.supportNumberCode.value = null;
                break;
        }

        /** Change main field */
        this.administrativeReceipt.isDisabled = !!this.administrativeReceiptLines.value.length;

        this.product.value = null;
        this._updateProductState();
        return true;
    }

    /**
     *  Update the state of the product field
     */
    private _updateProductState() {
        this.product.isDisabled =
            this.defaultMode.value === 'eanCode' ||
            !this._receiptLines.length ||
            (this.defaultMode.value === 'supplierBc' && !this.supplierBarCode.value) ||
            (this.defaultMode.value === 'support' && !this.supportNumberCode.value);
        this.product.isMandatory =
            this.defaultMode.value !== 'eanCode' && !this.administrativeReceiptLines.value.length;
    }

    /**
     *
     * @returns a filter for the supplier product upc
     */
    private _getFilterSupplierProductUpc(): Filter<SupplierUpc> | undefined {
        const _segmentFilter = <any>[];
        // If there is no product, the filter must be defined to return an empty result
        if (!this._groupedProductsWithSuppliers.length) {
            return { _id: { _eq: '' } };
        }

        for (const product of this._groupedProductsWithSuppliers) {
            _segmentFilter.push(<any>{
                product: { code: { _in: product.productsCode } },
                supplier: { code: { _in: product.suppliersCode } },
            });
        }
        if (_segmentFilter.length === 1) {
            return <Filter<SupplierUpc>>_segmentFilter[0];
        } else {
            return <Filter<SupplierUpc>>{
                _and: [{ _or: _segmentFilter }],
            };
        }
    }

    /**
     * All products are grouped with their common suppliers, and there will be as many entries of the same product as needed.
     * @returns
     */
    private _groupProductsByCommonSuppliers(): GroupedCommonProductsWithSuppliers {
        const supplierGroups: { [key: string]: string[] } = {};

        this._productsWithSuppliers.forEach(product => {
            const suppliersKey = product.suppliersCode.sort().join(',');
            if (!supplierGroups[suppliersKey]) {
                supplierGroups[suppliersKey] = [];
            }
            supplierGroups[suppliersKey].push(product.productCode);
        });

        return Object.entries(supplierGroups)
            .map(([suppliersKey, productsCode]) => ({
                productsCode: productsCode.sort(),
                suppliersCode: suppliersKey.split(',').sort(),
            }))
            .sort((a, b) => a.productsCode[0].localeCompare(b.productsCode[0]));
    }

    private _reinitializedWorkingLineAndMovements(isIncludeAdministrativeReceiptLines = false) {
        this._receiptMovementNumber = 1;
        this._receiptLineNumber = 1;
        this._receiptLines = [];
        this._isNewLine = false;
        this._temporaryReceivedMovements = [];
        this.selectedAdministrativeReceiptLines.value = [];
        this.pendingAdministrativeReceiptLines.value = [];
        this.administrativeReceiptMovements.value = [];
        if (isIncludeAdministrativeReceiptLines) {
            this.administrativeReceiptLines.value = [];
        }
    }

    /**
     * Set the line selection header
     */
    private _setLineSelectionHeader() {
        this.administrativeReceiptCodeHeader.value = this.administrativeReceipt.value?.code;
        this.administrativeReceiptStatusCodeHeader.value = this.administrativeReceipt.value?.status;
        this.supplierBarCodeModeHeader.value = this.defaultMode.value;
        switch (this.defaultMode.value) {
            case 'supplierBc':
                this.supplierBarCodeValueHeader.value = this.supplierBarCode.value?.expectedInput?.referenceNumber;
                break;
            case 'eanCode':
                this.supplierBarCodeValueHeader.value = this.supplierEanCode.value?.upcCode;
                break;
            case 'support':
                this.supplierBarCodeValueHeader.value = this.supportNumberCode.value?.supportNumber;
                break;
            default:
                this.supplierBarCodeValueHeader.value = '';
        }
    }

    /**
     * Set the enter line header
     */
    private _setEnterLineHeader(previousSection?: SelectionSection) {
        const _line = this.selectedAdministrativeReceiptLines.value[0];
        const _product = _line?.product;
        this.productCodeHeader.value = _product?.code ?? null;
        this.productLocalizedDescriptionHeader.value = (<any>(<unknown>_product))?.localizedDescription;
        this.expectedInputCodeHeader.value = _line?.expectedInput?.referenceNumber ?? null;
        this.remainingQuantityHeader.scale = (<any>(<unknown>_product))?.stockUnit?.numberOfDecimals ?? 0;
        this.remainingQuantityHeader.postfix =
            (<ConsumptionUnit>(<any>(<unknown>_product))?.consumptionUnit)?.code ?? '';
        this._updateRemainingQuantityHeader();
    }

    /**
     * Update the value of a given property with the initial remaining quantity in consumption unit.
     * This helper method is used to avoid duplication in updating different properties.
     * @param property - The property to update (e.g., remainingQuantityHeader or receivedQuantity).
     */
    private _updateQuantityValue(property: ui.fields.Numeric) {
        property.value = this._getInitialRemainingQuantityInConsumptionUnit();
    }

    /**
     *  Update current remaining quantity for header
     */
    private _updateRemainingQuantityHeader() {
        this._updateQuantityValue(this.remainingQuantityHeader);
    }

    /**
     *  Update current remaining quantity after deletion
     */
    private _updateRemainingQuantityLineAfterDelete() {
        this._updateQuantityValue(this.receivedQuantity);
    }
    /**
     * Add a new line in administrative receipt line grid
     * @returns true when done
     */
    private async _initEnterLineBlock(
        previousSection?: SelectionSection,
        parameters?: SectionParameters,
    ): Promise<boolean> {
        const _line = this.selectedAdministrativeReceiptLines.value[0];
        let _currentLine: PartialCollectionValueWithIds<AdministrativeReceiptLineInProgressBinding> | undefined;
        const _productCode = _line?.product?.code;
        let _administrativeReceiptMovementInProgress: PartialCollectionValueWithIds<AdministrativeReceiptMovementInProgressBinding>[];

        /*
         * The fields are initialized according to the selected product :
         * - when entering the fist time, the fields are initialized with the values of the selected product, we entering in update mode.
         * - when we come back to the section in cancellation, the fields are initialized with the values of the selected product only when
         *   canceling the movements.
         * - when we come back to the section with saved movements, we entering in create mode.
         */

        // Check if it is necessary to reset the movements
        if (
            !_productCode ||
            (previousSection !== SelectionSection.enterLine && previousSection !== SelectionSection.enterMovement)
        ) {
            this.administrativeReceiptMovements.value = [];
            this._temporaryReceivedMovements = [];
            if (_productCode) {
                await this._initContainerUnitFields(_line);
                this.lotNumber.isDisabled = !_line?.product?.isKeyInLotNumber;
                this.supportNumber.isDisabled = !this._depositorConfiguration?.isSupportNumber;
                this.fifoDate.isMandatory = (this._productConfiguration?.fifoDateNature ?? 'input') !== 'input';

                this._isNewLine = !!parameters?.isCreate;

                if (this._isNewLine) {
                    _currentLine = this._getWorkingAdministrativeReceiptLineInProgress(_line);
                } else {
                    // If the line has pending in cache, it is retrieved (level 1)
                    _currentLine = this._getPendingAdministrativeReceiptLineInProgress(_line);

                    // If the line has already been stored (level 0), we starting in new line mode
                    if (!_currentLine) {
                        const _currentGridLine = this._getWorkingAdministrativeReceiptLineInProgress(_line);
                        if (parameters?.isUpdate && _currentGridLine) {
                            _currentLine = _currentGridLine;
                        } else {
                            this._isNewLine = !!_currentGridLine || !!parameters?.isCreate;
                        }
                    }
                }

                // Case of initial edit
                if (!this._isNewLine) {
                    /**
                     * Retrieve the movements in progress
                     */

                    // If the line has been processed, it also contains any movements,
                    // otherwise they must be read from the database.
                    if (_currentLine) {
                        this.selectedAdministrativeReceiptLines.value = [_currentLine];
                        _administrativeReceiptMovementInProgress =
                            <any>_currentLine?.administrativeReceiptMovements ?? [];
                    } else {
                        _currentLine = await this._getReceiptLineDetails(_line);
                        this.selectedAdministrativeReceiptLines.value = [_currentLine];
                        _administrativeReceiptMovementInProgress =
                            await this._getAdministrativeReceiptMovementInProgress();
                    }

                    // The movements are stored in a temporary variable for possible cancellation
                    this._temporaryReceivedMovements = _administrativeReceiptMovementInProgress;

                    // Store the movements in the temporary table for edit
                    this.administrativeReceiptMovements.value = _administrativeReceiptMovementInProgress;

                    //The next move number for inserts is determined from temporary table
                    this._updateReceiptMovementNumberCounter();
                }
            } else {
                this._receiptMovementNumber = 1;
                this.lotNumber.isMandatory = false;
                this.fifoDate.isMandatory = false;
                this.inputContainerUnit.isDisabled = true;
                this.stockStatus.isDisabled = true;
                this.lotNumber.isDisabled = true;
                this.reservationNumber.isDisabled = true;
                this.supportNumber.isDisabled = true;
                this.manufacturedDate.isDisabled = true;
                this.sellByDate.isDisabled = true;
                this.detentionDate.isDisabled = true;
                this.useByDate.isDisabled = true;
                this.fifoDate.isDisabled = true;
                this.shipByDate.isDisabled = true;
            }

            if (this._isNewLine) {
                await this._initializeNewLine(<SectionParameters>{ isCreate: true });
            } else {
                this._loadReceiptLineData(_currentLine, parameters);
            }
        } else {
            await this._initializeNewLine(parameters);
        }

        return !!_productCode;
    }

    /**
     *  Initialize the new line or restore the movements
     * @param parameters
     */
    private async _initializeNewLine(parameters?: SectionParameters) {
        if (!parameters?.isSaved) {
            // Restore movements
            this.administrativeReceiptMovements.value = this._temporaryReceivedMovements;
        }

        if (parameters?.isCreate) {
            const _line = this.selectedAdministrativeReceiptLines.value[0];
            const _expectedInputLineNumber = _line.expectedInputLine;

            this.administrativeReceiptMovements.value = [];
            this._temporaryReceivedMovements = [];
            this._isNewLine = true;

            //The next move number for inserts is determined from temporary table
            this._updateReceiptMovementNumberCounter();

            // Prepare only the necessary fields without movements, no _id or key fields updated
            const _newReceiptLine = <PartialCollectionValueWithIds<AdministrativeReceiptLineInProgressBinding>>{
                ..._line,
                inputContainer: _expectedInputLineNumber?.inputContainer,
                source: _expectedInputLineNumber?.source,
                stockStatus: _expectedInputLineNumber?.stockStatus,
                store: _expectedInputLineNumber?.store,
                isExclusiveStore: _expectedInputLineNumber?.isExclusiveStore,
                lotNumber: _expectedInputLineNumber?.lotNumber,
                reservationNumber: _expectedInputLineNumber?.reservationNumber,
                supportNumber: _expectedInputLineNumber?.supportNumber,
                manufacturedDate: _expectedInputLineNumber?.manufacturedDate,
                sellByDate: _expectedInputLineNumber?.sellByDate,
                detentionDate: _expectedInputLineNumber?.detentionDate,
                useByDate: _expectedInputLineNumber?.useByDate,
                fifoDate: _expectedInputLineNumber?.fifoDate,
                shipByDate: _expectedInputLineNumber?.shipByDate,
                actionImport: 'C',
                administrativeReceiptMovements: [],
            };
            this.selectedAdministrativeReceiptLines.value = [_newReceiptLine];
            this._loadReceiptLineData(_newReceiptLine, parameters);
        }
    }

    /**
     * Initialize the container unit fields
     * @param currentLine
     */
    private _loadReceiptLineData(
        currentLine: PartialCollectionValueWithIds<AdministrativeReceiptLineInProgressBinding> | undefined,
        parameters?: SectionParameters,
    ) {
        if (this.defaultMode.value !== 'eanCode') {
            this.inputContainerUnit.value = currentLine?.inputContainer?.container?.code ?? '';
        }
        // const _receivedQuantity = this._getInitialReceivedQuantityInConsumptionUnit();
        this.receivedQuantity.value = this._getReceivedQuantityInContainerUnit(
            parameters?.isCreate
                ? this._getInitialRemainingQuantityInConsumptionUnit()
                : this._getInitialReceivedQuantityInConsumptionUnit(),
        );
        this.stockStatus.value = currentLine?.stockStatus?.code ? { code: currentLine?.stockStatus?.code } : null;
        this.lotNumber.value = currentLine?.lotNumber ?? '';
        this.reservationNumber.value = currentLine?.reservationNumber ?? '';
        this.supportNumber.value = currentLine?.supportNumber ?? '';
        this.manufacturedDate.value = currentLine?.manufacturedDate ?? null;
        this.sellByDate.value = currentLine?.sellByDate ?? null;
        this.detentionDate.value = currentLine?.detentionDate ?? null;
        this.useByDate.value = currentLine?.useByDate ?? null;
        this.fifoDate.value = currentLine?.fifoDate ?? null;
        this.shipByDate.value = currentLine?.shipByDate ?? null;

        this._updateStoreValues(currentLine);
        // Update line counter : missing parameters or update perform a full update
        this._updateReceiptLineNumberCounter(parameters);
        this._updateRemainingQuantityHeader();
    }

    /**
     * Update store values after updating stock status
     * @param _line
     */
    private _updateStoreValues(
        _line: PartialCollectionValueWithIds<AdministrativeReceiptLineInProgressBinding> | undefined,
    ) {
        if (_line) {
            if (!this.stockStatus.value) {
                this.isDispute.value = false;
                this.isPutaway.value = true;
                this.store.value = _line?.store ?? null;
                this.isExclusiveStore.value = _line?.isExclusiveStore ?? false;
            } else {
                this.isDispute.value = true;
                this.isPutaway.value = true;
                // Since the store is not entered, the default value applies.
                this.store.value = _line?.store ?? null;
                this.isExclusiveStore.value = _line?.isExclusiveStore ?? false;
            }
        }
    }

    /**
     *  Update administrative receipt line counter evaluated from :
     * - the base lines (only when is necessary to create a new line)
     * - the pending lines (level 1 = current editing)
     * - the final grid lines (level 0 = stored)
     * Only when is necessary to create a new line
     *  @param parameters optional parameters for the new line
     * @returns the next spit line number
     */
    private async _updateReceiptLineNumberCounter(parameters?: SectionParameters): Promise<number> {
        const _line = this.selectedAdministrativeReceiptLines.value[0];
        let _receiptLineNumber = 0;
        if (_line) {
            const _expectedInputCode = _line.expectedInput?.code;
            const _expectedInputLineNumber = _line.expectedInputLine?.lineNumber;
            // Reading from the base is only done if there is a new line on first initialization.
            // The parameter will be provided in this specific case.
            _receiptLineNumber = !parameters?.isCreate ? await this._getMaximumAdministrativeReceiptLineNumber() : 0;

            // From the grid
            _receiptLineNumber =
                this.administrativeReceiptLines.value
                    .filter(
                        line =>
                            line.expectedInput?.code === _expectedInputCode &&
                            line.expectedInputLine?.lineNumber === _expectedInputLineNumber,
                    )
                    .reduce<decimal>((_acc, _currentValue) => {
                        return Math.max(_acc, Number(_currentValue.lineNumber ?? 0));
                    }, _receiptLineNumber) ?? 0;

            // From the pending
            _receiptLineNumber =
                (this.pendingAdministrativeReceiptLines.value.reduce<decimal>((_acc, _currentValue) => {
                    return Math.max(_acc, Number(_currentValue.lineNumber ?? 0));
                }, _receiptLineNumber) ?? 0) + 1;

            // Recalculate the movements too
            this._updateReceiptMovementNumberCounter();
        }

        _receiptLineNumber++;

        this._receiptLineNumber = _receiptLineNumber;
        return _receiptLineNumber;
    }

    /**
     *  Update administrative receipt movements counter
     */
    private _updateReceiptMovementNumberCounter(): number {
        let _receiptMovementNumber = 0;
        const _line = this.selectedAdministrativeReceiptLines.value[0];
        if (_line) {
            const _administrativeReceiptMovementInProgress = this.administrativeReceiptMovements.value;
            const _expectedInputCode = _line.expectedInput?.code;
            const _expectedInputLineNumber = _line.expectedInputLine?.lineNumber;

            // Movements in progress
            _receiptMovementNumber = Math.max(
                0,
                ..._administrativeReceiptMovementInProgress.map(line => Number(line?.receiptMovementNumber ?? 0)),
            );

            // Movements in the grid
            this.administrativeReceiptLines.value
                .filter(
                    line =>
                        line.expectedInput?.code === _expectedInputCode &&
                        line.expectedInputLine?.lineNumber === _expectedInputLineNumber,
                )
                .forEach(line => {
                    _receiptMovementNumber =
                        line?.administrativeReceiptMovements?.reduce<decimal>((_acc, _currentValue) => {
                            return Math.max(_acc, Number(_currentValue.receiptMovementNumber ?? 0));
                        }, _receiptMovementNumber) ?? 0;
                });

            // Pending movements
            this.pendingAdministrativeReceiptLines.value
                .filter(
                    line =>
                        line.expectedInput?.code === _expectedInputCode &&
                        line.expectedInputLine?.lineNumber === _expectedInputLineNumber,
                )
                .forEach(line => {
                    _receiptMovementNumber =
                        line?.administrativeReceiptMovements?.reduce<decimal>((_acc, _currentValue) => {
                            return Math.max(_acc, Number(_currentValue.receiptMovementNumber ?? 0));
                        }, _receiptMovementNumber) ?? 0;
                });
        }

        // Next value
        _receiptMovementNumber++;

        this._receiptMovementNumber = _receiptMovementNumber;
        return _receiptMovementNumber;
    }

    /**
     * Set the enter movement header
     */
    private _setEnterMovementHeader(previousSection?: SelectionSection) {
        const _line = this.selectedAdministrativeReceiptLines.value[0];
        const _product = _line?.product;
        this.productCodeHeader.value = _product?.code ?? null;
        this.productLocalizedDescriptionHeader.value = (<any>(<unknown>_product))?.localizedDescription;
        this.expectedInputCodeHeader.value = _line?.expectedInput?.referenceNumber ?? null;
        this.receivedQuantityHeader.postfix = this._productPalettizationPlan.productConsumptionUnitCode;
        this.receivedQuantityHeader.scale = this._productPalettizationPlan.getProductContainerUnitPrecision();
        this.receivedQuantityHeader.value = this._getReceivedQuantityInConsumptionUnitForMovement();
        this.packedQuantityHeader.postfix = this._productPalettizationPlan.productConsumptionUnitCode;
        this.packedQuantityHeader.scale = this._productPalettizationPlan.getProductContainerUnitPrecision();
        this.packedQuantityHeader.value = this._getMovementsPackedQuantity();
    }

    /**
     * Add a new movement in administrative receipt line grid
     * @returns true when done
     */
    private async _initMovementBlock(previousSection?: SelectionSection): Promise<boolean> {
        if (this.selectedAdministrativeReceiptLines.value.length > 0) {
            let _inputContainerCode = this.inputContainerUnit.value;
            if (previousSection !== SelectionSection.enterMovement || !this.inputContainer.value) {
                this.inputContainer.value = _inputContainerCode;
                this.homogeneousContainer.value = null;
            }
            this.homogeneousContainer.isDisabled = false;
            this.homogeneousQuantity.isDisabled = false;
            this.packedQuantity.postfix = this._productPalettizationPlan.productConsumptionUnitCode;
            return await this._recalculateMovementPlan(this.inputContainer.value);
        } else {
            this.inputContainer.value = null;
            this.numberOfContainers.value = null;
            this.homogeneousContainer.value = null;
            this.homogeneousQuantity.value = null;
            this._updateReceiptButtonsState();
            return false;
        }
    }

    /**
     * Recalculate possible movement plan
     * @returns
     */
    private async _recalculateMovementPlan(inputContainerCode: string | null | undefined): Promise<boolean> {
        let _numberOfConsumptionUnit = 0;
        if (this.selectedAdministrativeReceiptLines.value.length > 0) {
            _numberOfConsumptionUnit = this._getMovementRemainingQuantityInConsumptionUnit();
            let _inputContainerCode = inputContainerCode;
            let _numberOfContainers = 0;
            let _homogeneousContainerCode = this.homogeneousContainer.value ?? undefined;
            let _numberOfHomogeneousContainers = 0;

            const _possibleProductPlan = this._productPalettizationPlan.calculatePossibleMovementPlan(
                _numberOfConsumptionUnit,
                _inputContainerCode,
                _numberOfContainers,
                _homogeneousContainerCode,
            );

            if (_possibleProductPlan) {
                _inputContainerCode = _possibleProductPlan.inputContainerCode;
                _numberOfContainers = _possibleProductPlan.numberOfContainers;
                _homogeneousContainerCode = _possibleProductPlan.homogeneousContainerCode;
                _numberOfHomogeneousContainers = _possibleProductPlan.numberOfHomogeneousContainers;
            }

            this.inputContainer.value = _inputContainerCode ?? null;
            this.numberOfContainers.value = _numberOfContainers;

            if (
                _inputContainerCode == this._containerConsumptionOrUpcUnit?.code &&
                this._containerConsumptionOrUpcUnit?.containerLevel === hightestContainerLevel
            ) {
                this.homogeneousContainer.isDisabled = true;
                this.homogeneousQuantity.isDisabled = true;
                _homogeneousContainerCode = _inputContainerCode;
                _numberOfHomogeneousContainers = 1;
            } else {
                this.homogeneousContainer.isDisabled = false;
                this.homogeneousQuantity.isDisabled = false;
            }

            this.homogeneousContainer.value =
                _homogeneousContainerCode ?? this._productPalettizationPlan.productConsumptionUnitCode;
            this.homogeneousQuantity.value = _numberOfHomogeneousContainers;
            this.packedQuantity.value = this._getMovementPackedQuantity();
        }
        this._updateReceiptButtonsState();
        return this.selectedAdministrativeReceiptLines.value.length > 0;
    }

    /**
     * Change current block for valid selection and reset edit field
     * @param newDefaultValue
     */
    private async _ChangeSelectionMode(newDefaultValue: string) {
        const _emptyAdministrativeReceipt = !this.administrativeReceipt.value;
        this.receiptBlock.isDisabled = _emptyAdministrativeReceipt;
        this.defaultMode.isDisabled = _emptyAdministrativeReceipt;
        this.supplierBarCode.isHidden = newDefaultValue !== 'supplierBc';
        this.supplierEanCode.isHidden = newDefaultValue !== 'eanCode';
        this.supportNumberCode.isHidden = newDefaultValue !== 'support';
        this._initEnterMainBlock(newDefaultValue);
    }

    private async _showWorkflowLayout(showPanel: boolean) {
        this.createButton.isDisabled =
            !this.administrativeReceipt.value || !this.administrativeReceiptLines.value.length;
        await this._updateMainSectionInformation();
        this._ChangeSelectionMode(this.defaultMode.value ?? 'supplierBc');
    }

    private async disablePage() {
        await this._setActiveSection(SelectionSection.main);
        this.mainSection.isDisabled = true;
        this.selectLineSection.isDisabled = true;
        this.enterLineSection.isDisabled = true;
        this.mainBlock.isDisabled = true;
        this.receiptBlock.isDisabled = true;
        this.gridLinesBlock.isDisabled = true;
    }

    /**
     * Get administrative lines by supplier barcode
     * @param orderReference
     * @param lineReference
     * @returns
     */
    private async _getAdministrativeLinesBySupplierBarCode(
        orderReference: string,
        lineReference: string,
    ): Promise<ExtractEdges<AdministrativeReceiptLineInProgressBinding>[]> {
        const _filter: Filter<AdministrativeReceiptLineInProgress> = {
            expectedInput: { referenceNumber: orderReference },
            expectedInputLine: { referenceNumber: lineReference },
        };
        return await this._getAdministrativeLinesInternal(_filter);
    }

    /**
     *  Get administrative lines by supplier upc code
     * @param supplierUpcCode
     * @returns
     */
    private async _getAdministrativeLinesBySupplierUpcCode(
        supplierUpcCode: ExtractEdgesPartial<SupplierUpc>,
    ): Promise<ExtractEdges<AdministrativeReceiptLineInProgressBinding>[]> {
        const _filter: Filter<AdministrativeReceiptLineInProgress> = {
            expectedInput: { supplier: { code: supplierUpcCode.supplier?.code } },
            expectedInputLine: { product: { code: supplierUpcCode.product?.code } },
            product: { code: supplierUpcCode.product?.code },
        };
        return await this._getAdministrativeLinesInternal(_filter);
    }

    /**
     * Get administrative lines by support number
     * @param supportNumber
     * @returns
     */
    private async _getAdministrativeLinesBySupportNumber(
        supportNumber: string,
    ): Promise<ExtractEdges<AdministrativeReceiptLineInProgressBinding>[]> {
        const _filter: Filter<AdministrativeReceiptLineInProgress> = {
            supportNumber: supportNumber,
        };
        return await this._getAdministrativeLinesInternal(_filter);
    }

    /**
     * Get administrative line details with current values
     */
    private async _getReceiptLineDetails(
        line: PartialCollectionValueWithIds<AdministrativeReceiptLineInProgressBinding>,
    ): Promise<PartialCollectionValueWithIds<AdministrativeReceiptLineInProgressBinding>> {
        const _filter: Filter<AdministrativeReceiptLineInProgress> = {
            expectedInput: { code: line?.expectedInput?.code },
            expectedInputLine: {
                lineNumber: line?.expectedInputLine?.lineNumber,
                product: { code: line?.product?.code },
            },
            lineNumber: line?.lineNumber,
        };

        return (
            this._getWorkingAdministrativeReceiptLineInProgress(line) ??
            (await this._getAdministrativeLinesInternal(_filter))[0]
        );
    }

    /**
     * Get working administrative receipt line stored in the grid (level 0)
     * @param _line
     * @returns
     */
    private _getWorkingAdministrativeReceiptLineInProgress(
        _line: PartialCollectionValueWithIds<AdministrativeReceiptLineInProgressBinding>,
    ): PartialCollectionValueWithIds<AdministrativeReceiptLineInProgressBinding> | undefined {
        return _line?._id && this.administrativeReceiptLines.value.length
            ? (this.administrativeReceiptLines.getRecordValue(_line?._id, 0) ?? undefined)
            : undefined;
    }

    /**
     * Get pending administrative receipt line in table (current editing level 1)
     *  @param _line line to check
     * @returns
     * */
    private _getPendingAdministrativeReceiptLineInProgress(
        _line: PartialCollectionValueWithIds<AdministrativeReceiptLineInProgressBinding>,
    ): PartialCollectionValueWithIds<AdministrativeReceiptLineInProgressBinding> | undefined {
        return _line?._id && this.administrativeReceiptLines.value.length
            ? (this.pendingAdministrativeReceiptLines.getRecordValue(_line?._id) ?? undefined)
            : undefined;
    }

    /**
     * This function is used to get administrative lines by custom functions
     * @param _localFilter
     * @returns
     */
    private async _getAdministrativeLinesInternal(
        _localFilter: Filter<AdministrativeReceiptLineInProgress> = {},
    ): Promise<ExtractEdges<AdministrativeReceiptLineInProgressBinding>[]> {
        try {
            const _response = extractEdges<any>(
                await this.$.graph
                    .node('@sage/wh-input/AdministrativeReceiptLineInProgress')
                    .query(
                        ui.queryUtils.edgesSelector<AdministrativeReceiptLineInProgress>(
                            {
                                _id: true,
                                site: { code: true, _id: true },
                                depositor: { code: true, _id: true },
                                expectedInput: {
                                    _id: true,
                                    code: true,
                                    referenceNumber: true,
                                    supplier: { code: true, _id: true },
                                    receiptMode: { isExceedQuantityControl: true },
                                },
                                expectedInputLine: {
                                    _id: true,
                                    site: { code: true, _id: true },
                                    depositor: { code: true, _id: true },
                                    lineNumber: true,
                                    referenceNumber: true,
                                    product: { code: true, _id: true },
                                    inputContainer: { container: { code: true, _id: true }, _id: true },
                                    supportNumber: true,
                                    reservationNumber: true,
                                    lotNumber: true,
                                    source: { code: true, _id: true },
                                    stockStatus: { code: true, _id: true },
                                    store: { code: true, _id: true },
                                    isExclusiveStore: true,
                                    receiptDate: true,
                                    manufacturedDate: true,
                                    detentionDate: true,
                                    sellByDate: true,
                                    useByDate: true,
                                    shipByDate: true,
                                    fifoDate: true,
                                    expectedQuantityInConsumptionUnit: true,
                                    remainingQuantityInConsumptionUnit: true,
                                    receivedQuantityInConsumptionUnit: true,
                                    disputedQuantityInConsumptionUnit: true,
                                },
                                receipt: { code: true, status: true, _id: true },
                                lineNumber: true,
                                product: {
                                    _id: true,
                                    code: true,
                                    localizedDescription: true,
                                    isKeyInLotNumber: true,
                                    isStockUnitManagement: true,
                                    stockUnit: {
                                        code: true,
                                        numberOfDecimals: true,
                                        _id: true,
                                    },
                                },
                                receivedQuantityInConsumptionUnit: true,
                                inputContainer: { container: { code: true, _id: true }, _id: true },
                                lotNumber: true,
                                supportNumber: true,
                                reservationNumber: true,
                                stockStatus: { code: true, _id: true },
                                store: { code: true, _id: true },
                                isExclusiveStore: true,
                                isDispute: true,
                                isPutaway: true,
                                source: { code: true, _id: true },
                                manufacturedDate: true,
                                detentionDate: true,
                                sellByDate: true,
                                useByDate: true,
                                shipByDate: true,
                                fifoDate: true,
                            },
                            {
                                filter: {
                                    site: { code: this._siteCodeSelected },
                                    depositor: { code: this._depositorCodeSelected },
                                    receipt: { code: this.administrativeReceipt.value?.code },
                                    ..._localFilter,
                                },
                            },
                        ),
                    )
                    .execute(),
            );

            // Force consumption unit in product
            if (_response.length) {
                await this._addProductsConsumptionUnitsToReceiptLines(_response);
            }

            return _response;
        } catch (error) {
            ui.console.error('Error reading product container:\n', JSON.stringify(error));
            return [];
        }
    }

    /**
     * Add product consumption unit to all receipt lines
     * @param _receiptLines
     */
    private async _addProductsConsumptionUnitsToReceiptLines(
        _receiptLines: ExtractEdges<AdministrativeReceiptLineInProgressBinding>[],
    ) {
        if (_receiptLines.length) {
            // Using a product dictionary to avoid multiple calls for the same product
            const _consumptionUnits: { [key: string]: ConsumptionUnit | null } = {};
            for (const _line of _receiptLines) {
                const _productCode = _line.product?.code;
                if (_productCode) {
                    if (_consumptionUnits[_productCode] === undefined) {
                        _consumptionUnits[_productCode] = await getConsumptionUnit(
                            this,
                            this._siteCodeSelected ?? '',
                            this._depositorCodeSelected ?? '',
                            _productCode,
                        );
                    }
                    if (_consumptionUnits[_productCode]) {
                        // force consumption unit in product
                        const _consumptionUnit = _consumptionUnits[_productCode];
                        (<ConsumptionUnit>(<any>(<unknown>_line.product)).consumptionUnit) = _consumptionUnit;
                    }
                }
            }
        }
    }

    /**
     * Set the active section
     * The section will only be made visible at the end, so as not to have the various
     * changes made to the section.
     * @param section target section
     * @param parameters optional parameters
     * @returns activated section
     */
    private async _setActiveSection(
        section: SelectionSection,
        parameters?: SectionParameters,
    ): Promise<SelectionSection> {
        const _previousSection = this._getActiveSection();
        let _section = section;
        let _numberOfSelectedLines = this.selectedAdministrativeReceiptLines.value.length;
        let _numberOfReceiptLines = this.administrativeReceiptLines.value.length;

        /**
         * Evaluate the right section to display
         */
        switch (section) {
            case SelectionSection.disableAll:
                parameters = <SectionParameters>{ isReinitialize: true };

            case SelectionSection.main:
                _section = section;
                if (parameters?.isReinitialize) {
                    await this.$.router.emptyPage(true);
                    this._reinitializedWorkingLineAndMovements(true);
                    _numberOfSelectedLines = 0;
                    _numberOfReceiptLines = 0;
                }
                break;

            case SelectionSection.selectLine:
                if (
                    _numberOfSelectedLines &&
                    (this.selectedAdministrativeReceiptLines.value[0].expectedInput?.receiptMode
                        ?.isExceedQuantityControl ??
                        false) &&
                    !this._getInitialRemainingQuantityInConsumptionUnitForExpectedLine()
                ) {
                    _section = SelectionSection.main;
                    this.$.showToast(
                        ui.localize(
                            '@sage/wh-input/notification-error-administrative-receipt-no-quantity-available-to-receive',
                            `No quantity available to receive.`,
                        ),
                        { type: 'error', timeout: 5000 },
                    );
                    break;
                }
                switch (_numberOfSelectedLines) {
                    case 0:
                        _section = SelectionSection.main;
                    case 1:
                        _section = SelectionSection.enterLine;
                    default:
                        section = section;
                        break;
                }
                break;

            case SelectionSection.enterLine:
                switch (_numberOfSelectedLines) {
                    case 0:
                        _section = SelectionSection.main;
                    case 1:
                        _section = section;
                    default:
                        _section = SelectionSection.enterLine;
                }
                break;
            case SelectionSection.enterMovement:
                break;

            default:
                _section = SelectionSection.main;
                break;
        }

        /**
         *  Set the header
         */

        this._setSubTitleForSection(_section);

        switch (_section) {
            case SelectionSection.disableAll:
                break;
            case SelectionSection.main:
                await this._initEnterMainBlock();
                await this._updateMainSectionInformation(false);
                break;
            case SelectionSection.selectLine:
                this.selectLineBlock.title = ui.localize(
                    '@sage/wh-input/number-of-lines-selected',
                    'Lines: {{ numberOfLinesSelected }}',
                    {
                        numberOfLinesSelected: _numberOfSelectedLines,
                    },
                );

                this._setLineSelectionHeader();
                break;
            case SelectionSection.enterLine:
                this._setEnterLineHeader();
                await this._initEnterLineBlock(_previousSection, parameters);
                break;

            case SelectionSection.enterMovement:
                this._setEnterMovementHeader();
                await this._initMovementBlock(_previousSection);
                break;
        }

        /**
         * Change buttons visibility
         * Only useful buttons will be active in the section displayed (business actions)
         */
        this.addToReceiptButton.isHidden =
            _section !== SelectionSection.enterLine && _section !== SelectionSection.enterMovement;
        this.receiptButton.isHidden =
            _section !== SelectionSection.enterLine && _section !== SelectionSection.enterMovement;

        this.receiptMovementSectionButton.isHidden = _section !== SelectionSection.enterLine;

        this.cancelButton.isHidden = ![
            SelectionSection.selectLine,
            SelectionSection.enterLine,
            SelectionSection.enterMovement,
        ].includes(_section);

        this.createButton.isHidden = _section !== SelectionSection.main;
        this.createButton.isDisabled = !_numberOfReceiptLines;

        /**
         * Change display section
         */
        this.mainSection.isHidden = _section !== SelectionSection.main;
        this.selectLineSection.isHidden = _section !== SelectionSection.selectLine;
        this.enterLineSection.isHidden = _section !== SelectionSection.enterLine;
        this.enterMovementSection.isHidden = _section !== SelectionSection.enterMovement;

        /**
         * Apply changes based on new section, and update the page after all changes
         */

        this._updateReceiptButtonsState(_section);

        await this.$.commitValueAndPropertyChanges();

        // The page is revalidated to cancel any errors that existed before the section reload,
        // except for those that remain.

        if (_section !== SelectionSection.disableAll && _section !== SelectionSection.main) {
            await this.$.page.validate();
        }

        return _section;
    }

    /**
     * Update receipt button disabled state :
     *
     * Acceptance of the sequence of movements phase is:
     * - Either we're not entering movements.
     * - Either there are no more quantities to receive.
     * - or there are saved movements, which have been completely deleted because the user no longer wants the movements entered.
     *
     */
    private _updateReceiptButtonsState(_activeSection = this._getActiveSection()) {
        switch (_activeSection) {
            case SelectionSection.enterLine:
                this.addToReceiptButton.isDisabled = !this.receivedQuantity.value;
                this.receiptMovementSectionButton.isDisabled = !this.receivedQuantity.value;
                this.receiptButton.isDisabled = !this.pendingAdministrativeReceiptLines.value.length;
                break;

            case SelectionSection.enterMovement:
                const _remainingQuantityInConsumptionUnit = this._getMovementRemainingQuantityInConsumptionUnit();
                // quantities exists to receive
                this.addToReceiptButton.isDisabled = !_remainingQuantityInConsumptionUnit;
                // receipt is possible
                this.receiptButton.isDisabled = !(
                    !_remainingQuantityInConsumptionUnit ||
                    (!!_remainingQuantityInConsumptionUnit &&
                        !!this._temporaryReceivedMovements.length &&
                        !this.administrativeReceiptMovements.value.length)
                );
                break;

            default:
                this.addToReceiptButton.isDisabled = true;
                this.receiptMovementSectionButton.isDisabled = true;
                this.receiptButton.isDisabled = true;
                break;
        }
    }

    /**
     * Get the active section
     * @returns active section
     */
    private _getActiveSection(): SelectionSection {
        if (!this.mainSection.isHidden) {
            return SelectionSection.main;
        } else if (!this.selectLineSection.isHidden) {
            return SelectionSection.selectLine;
        } else if (!this.enterLineSection.isHidden) {
            return SelectionSection.enterLine;
        } else if (!this.enterMovementSection.isHidden) {
            return SelectionSection.enterMovement;
        }
        return SelectionSection.disableAll;
    }

    /**
     * Set subtitle for active section
     * @returns true when subtitle has changed
     */
    private _setSubTitleForSection(_activeSection = this._getActiveSection()): boolean {
        let subTitle: string;
        switch (_activeSection) {
            case SelectionSection.main:
            // fallthrough
            default:
                subTitle = '';
                break;
            case SelectionSection.selectLine:
                subTitle = ui.localize(
                    '@sage/wh-input/pages__mobile_administrative-receipt__subtitle__select_an_administrative_receipt_line',
                    'Select an administrative receipt line',
                );
                break;
            case SelectionSection.enterLine:
                subTitle = ui.localize(
                    '@sage/wh-input/pages__mobile_administrative-receipt__subtitle__enter_an_administrative_line',
                    'Enter an administrative line',
                );
                break;
            case SelectionSection.enterMovement:
                subTitle = ui.localize(
                    '@sage/wh-input/pages__mobile_administrative-receipt__subtitle__enter_receipt_movements_details',
                    'Enter receipt movements details',
                );
                break;
        }
        this.$.page.subtitle = subTitle;
        return subTitle !== '';
    }

    /**
     * Get the received quantity in consumption unit or pending received quantities
     *  @returns {number} The received quantity in consumption unit reduced of pending received quantities
     */
    private _getInitialReceivedQuantityInConsumptionUnit(
        line?: PartialCollectionValueWithIds<AdministrativeReceiptLineInProgressBinding>,
    ): number {
        const _line = line ?? this.selectedAdministrativeReceiptLines.value[0];
        return (
            Number(_line?.receivedQuantityInConsumptionUnit ?? 0) ||
            this._getInitialRemainingQuantityInConsumptionUnit(_line)
        );
    }

    /**
     * Get the remaining quantity in consumption unit
     * @returns {number} The remaining quantity in consumption unit
     */
    private _getInitialRemainingQuantityInConsumptionUnit(
        line?: PartialCollectionValueWithIds<AdministrativeReceiptLineInProgressBinding>,
    ): number {
        const _line = line ?? this.selectedAdministrativeReceiptLines.value[0];
        return Math.max(
            Number(_line?.expectedInputLine?.remainingQuantityInConsumptionUnit ?? 0) -
                this._getStoredAndPendingReceivedQuantityInConsumptionUnit(_line),
            0,
        );
    }

    /**
     * Get the remaining quantity in consumption unit for expected line
     * @param line optional line to get the remaining quantity in consumption unit (if not provided,
     *             the first selected line will be used)
     * @returns {number} The remaining quantity in consumption unit
     */
    private _getInitialRemainingQuantityInConsumptionUnitForExpectedLine(
        line?: PartialCollectionValueWithIds<AdministrativeReceiptLineInProgressBinding>,
    ): number {
        const _line = line ?? this.selectedAdministrativeReceiptLines.value[0];
        return Math.max(
            Number(_line?.expectedInputLine?.remainingQuantityInConsumptionUnit ?? 0) -
                this._getStoredReceivedQuantityInConsumptionUnitForExpectedLine(_line),
            0,
        );
    }

    /**
     *  Get the stored received quantity in consumption unit for expected line
     * @param line optional line to get the stored received quantity in consumption unit (if not provided,
     *             the first selected line will be used)
     * @returns {number} The stored received quantity in consumption unit for expected line
     */
    private _getStoredReceivedQuantityInConsumptionUnitForExpectedLine(
        line?: PartialCollectionValueWithIds<AdministrativeReceiptLineInProgressBinding>,
    ): number {
        const _line = line ?? this.selectedAdministrativeReceiptLines.value[0];
        if (_line) {
            const _expectedInputCode = _line.expectedInput?.code;
            const _expectedInputLineNumber = _line.expectedInputLine?.lineNumber;

            // The current stored line being edited must be deducted to have a correct remaining quantity,
            // but must not include the pending line existing already stored (old values).
            return (
                this.administrativeReceiptLines.value
                    .filter(
                        line =>
                            line.expectedInput?.code === _expectedInputCode &&
                            line.expectedInputLine?.lineNumber === _expectedInputLineNumber,
                    )
                    .reduce<decimal>((_acc, _currentValue) => {
                        return _acc + Number(_currentValue.receivedQuantityInConsumptionUnit ?? 0);
                    }, 0) ?? 0
            );
        }
        return 0;
    }

    /**
     * Get the stored and pending received quantities in consumption unit
     * @returns {number} The stored and pending received quantities in consumption unit
     */
    private _getStoredAndPendingReceivedQuantityInConsumptionUnit(
        line?: PartialCollectionValueWithIds<AdministrativeReceiptLineInProgressBinding>,
    ): number {
        const _line = line ?? this.selectedAdministrativeReceiptLines.value[0];
        if (_line) {
            const _expectedInputCode = _line.expectedInput?.code;
            const _expectedInputLineNumber = _line.expectedInputLine?.lineNumber;

            // The current stored line being edited must be deducted to have a correct remaining quantity,
            // but must not include the pending line existing already stored (old values).
            const _storedReceivedQuantity =
                this.administrativeReceiptLines.value
                    .filter(
                        line =>
                            line.expectedInput?.code === _expectedInputCode &&
                            line.expectedInputLine?.lineNumber === _expectedInputLineNumber &&
                            (this._isNewLine || line._id != _line._id) &&
                            (!this.pendingAdministrativeReceiptLines.value.length ||
                                this.pendingAdministrativeReceiptLines.value.findIndex(
                                    pendingLine => pendingLine._id === line._id,
                                ) < 0),
                    )
                    .reduce<decimal>((_acc, _currentValue) => {
                        return _acc + Number(_currentValue.receivedQuantityInConsumptionUnit ?? 0);
                    }, 0) ?? 0;

            return _storedReceivedQuantity + this._getPendingQuantityInConsumptionUnit();
        }
        return 0;
    }

    /**
     * Get the pending received quantities in consumption unit
     * @returns {number} The pending received quantities in consumption unit
     */
    private _getPendingQuantityInConsumptionUnit(): number {
        // The current line in pending state must be taken into account.
        return (
            this.pendingAdministrativeReceiptLines.value.reduce<decimal>((_acc, _currentValue) => {
                return _acc + Number(_currentValue.receivedQuantityInConsumptionUnit ?? 0);
            }, 0) ?? 0
        );
    }

    /**
     *  Return from the received quantity in consumption / container unit
     *  @param receivedQuantity The received quantity when it is not in the field
     * @returns {number} The received quantity in consumption unit
     */
    private _getReceivedQuantityInConsumptionUnit(receivedQuantity?: number): number {
        const _receivedQuantity = receivedQuantity ?? Number(this.receivedQuantity.value ?? 0);
        if (this._containerConsumptionOrUpcUnit?.containerLevel === 'level5') {
            return _receivedQuantity;
        }
        return this._productPalettizationPlan.convertContainerQuantityInConsumptionUnit(
            _receivedQuantity,
            this._containerConsumptionOrUpcUnit?.code,
        );
    }

    /**
     * Get the received quantity in container unit
     * @param receivedQuantity The received quantity in consumption unit
     * @returns {number} The received quantity in container unit
     */
    private _getReceivedQuantityInContainerUnit(receivedQuantity: number): number {
        if (this._containerConsumptionOrUpcUnit?.containerLevel === 'level5') {
            return receivedQuantity;
        }
        return this._productPalettizationPlan.convertQuantityInConsumptionUnitToContainer(
            receivedQuantity,
            this._containerConsumptionOrUpcUnit?.code,
        );
    }

    /*
     *  Update the received quantity after delete
     */
    private async _updateReceivedQuantityAfterDelete() {
        this._updateRemainingQuantityHeader();
        this._updateRemainingQuantityLineAfterDelete();
        this._updateReceiptButtonsState();
        await this._updateReceiptLineNumberCounter();
        await this.$.commitValueAndPropertyChanges();
    }

    /**
     *  Get the received quantity in consumption unit for movement
     * @returns {number} The received quantity in consumption unit for movement
     */
    private _getReceivedQuantityInConsumptionUnitForMovement(): number {
        return this._getReceivedQuantityInConsumptionUnit();
    }

    /**
     *  Get the movement received quantity in consumption unit
     * @returns {number} The pending received quantities movements in consumption unit
     */
    private _getMovementReceivedQuantityInConsumptionUnit(): number {
        return this.selectedAdministrativeReceiptLines.value.length > 0
            ? (this._productPalettizationPlan.truncateConsumptionQuantity(
                  this.administrativeReceiptMovements.value?.reduce<decimal>((_acc, _currentValue) => {
                      return _acc + Number(_currentValue.numberOfConsumptionUnit ?? 0);
                  }, 0),
              ) ?? 0)
            : 0;
    }

    /**
     * Get the movement remaining quantity in consumption unit
     * @returns {number} The remaining quantity in consumption unit
     */
    private _getMovementRemainingQuantityInConsumptionUnit(): number {
        return Number(
            Math.max(
                this._getReceivedQuantityInConsumptionUnitForMovement() -
                    this._getMovementReceivedQuantityInConsumptionUnit(),
                0,
            ),
        );
    }

    /**
     * Get the packed quantity
     * @returns {number} The packed quantity
     */
    private _getMovementsPackedQuantity(): number {
        return this._getMovementReceivedQuantityInConsumptionUnit();
    }

    /**
     * Get the packed quantity
     * @returns {number} The packed quantity
     */
    private _getMovementPackedQuantity(): number {
        return this._productPalettizationPlan.convertContainerPlanToConsumptionQuantity(
            this.inputContainer.value,
            Number(this.numberOfContainers.value ?? 0),
            this.homogeneousContainer.value,
            Number(this.homogeneousQuantity.value ?? 0),
        );
    }

    /**
     *  Update main section information
     *  @param refresh
     *  @returns true when exists receipt lines
     */
    private async _updateMainSectionInformation(refresh: boolean = true): Promise<boolean> {
        const _numberOfReceiptLines = this.administrativeReceiptLines.value.length;
        this.administrativeReceipt.isDisabled = !!_numberOfReceiptLines;
        this.createButton.isDisabled = !_numberOfReceiptLines;
        this.gridLinesBlock.title = ui.localize(
            '@sage/wh-input/number-of-receipt-lines',
            'Receipt lines: {{ numberOfReceiptLines }}',
            {
                numberOfReceiptLines: _numberOfReceiptLines,
            },
        );

        if (refresh) {
            await this.$.commitValueAndPropertyChanges();
        }
        return !!_numberOfReceiptLines;
    }

    /**
     * Update received and packed quantity
     */
    private async _updateReceivedAndPackedQuantityAfterDelete() {
        this.packedQuantityHeader.value = this._getMovementsPackedQuantity();
        this.packedQuantity.value = this._getMovementPackedQuantity();
        this._updateReceiptButtonsState();
        this._updateReceiptMovementNumberCounter();
        await this._recalculateMovementPlan(this.inputContainer.value);
        await this.$.commitValueAndPropertyChanges();
    }

    /**
     * Init container units
     */
    private async _initContainerUnitFields(
        currentLine: PartialCollectionValueWithIds<AdministrativeReceiptLineInProgressBinding>,
    ): Promise<boolean> {
        const productCode = currentLine?.product?.code;
        if (
            this.defaultMode.value &&
            (await this._productPalettizationPlan.initialize(
                this,
                this._siteCodeSelected,
                this._depositorCodeSelected,
                productCode,
            ))
        ) {
            const _containerUnitAndOptions = this._productPalettizationPlan.getProductContainerUnitDependingBarCode(
                this.defaultMode.value,
                this.supplierEanCode.value?.container?.code ?? currentLine?.inputContainer?.container?.code,
            );
            this._containerConsumptionOrUpcUnit = _containerUnitAndOptions?.containerUnit;
            this.inputContainerUnit.options = _containerUnitAndOptions?.containerOptions ?? [];
            this.inputContainerUnit.value = this._containerConsumptionOrUpcUnit?.code ?? null;
            this.inputContainerUnit.isDisabled =
                this.inputContainerUnit.options.length < 2 || this.defaultMode.value === 'eanCode';
        } else {
            this._productPalettizationPlan.reinitialize();
            this.inputContainerUnit.options = [];
            this.inputContainerUnit.value = null;
            this.inputContainerUnit.isDisabled = true;
        }
        return this.inputContainerUnit.options.length > 0;
    }

    /**
     * Select single line and hide the block
     * @param recordId
     * @param rowItem
     */
    private async _onRowClick_Selected(recordId: string, rowItem: AdministrativeReceiptLineInProgressBinding) {
        const _administrativeReceiptLine = this.selectedAdministrativeReceiptLines.getRecordValue(recordId);
        if (_administrativeReceiptLine) {
            this.selectedAdministrativeReceiptLines.value = [_administrativeReceiptLine];
            await this._setActiveSection(SelectionSection.enterLine);
        }
    }

    /**
     * Add a new movement in administrative receipt line grid (_id not reinitialized)
     * @param includeMovements
     * @returns
     */
    private _getAdministrativeReceiptLineInProgressBinding():
        | PartialCollectionValueWithIds<AdministrativeReceiptLineInProgressBinding>
        | undefined {
        const _includeMovements = !!this.administrativeReceiptMovements.value.length;
        if (this.selectedAdministrativeReceiptLines.value.length) {
            // keep the current base line
            const _line = this.selectedAdministrativeReceiptLines.value[0];
            return <PartialCollectionValueWithIds<AdministrativeReceiptLineInProgressBinding>>{
                ..._line,
                receivedQuantityInConsumptionUnit: String(this._getReceivedQuantityInConsumptionUnit()),
                inputContainer: this._productPalettizationPlan.getProductContainer(this.inputContainerUnit.value),
                stockStatus: this.stockStatus.value ?? undefined,
                isDispute: this.isDispute.value ?? false,
                isPutaway: this.isPutaway.value ?? false,
                store: this.store.value ?? undefined,
                isExclusiveStore: this.isExclusiveStore.value ?? false,
                lotNumber: this.lotNumber.value ?? undefined,
                reservationNumber: this.reservationNumber.value ?? undefined,
                supportNumber: this.supportNumber.value ?? undefined,
                manufacturedDate: this.manufacturedDate.value ?? undefined,
                sellByDate: this.sellByDate.value ?? undefined,
                detentionDate: this.detentionDate.value ?? undefined,
                useByDate: this.useByDate.value ?? undefined,
                shipByDate: this.shipByDate.value ?? undefined,
                fifoDate: this.fifoDate.value ?? undefined,
                ...(!_includeMovements && {
                    administrativeReceiptMovements: [],
                }),
                ...(_includeMovements && {
                    administrativeReceiptMovements: this._getAdministrativeReceiptMovementInProgressBinding(),
                }),
            };
        }

        return undefined;
    }

    /**
     * Add a new movements in administrative receipt line grid
     * @returns
     */
    private _getAdministrativeReceiptMovementInProgressBinding(): PartialCollectionValueWithIds<AdministrativeReceiptMovementInProgressBinding>[] {
        return this.administrativeReceiptMovements.value;
    }

    /**
     * Add a new movement in administrative receipt movement in progress
     * @returns
     */
    private async _getAdministrativeReceiptMovementInProgress(): Promise<
        PartialCollectionValueWithIds<AdministrativeReceiptMovementInProgressBinding>[]
    > {
        try {
            if (this.selectedAdministrativeReceiptLines.value.length && !this._isNewLine) {
                const _line = this.selectedAdministrativeReceiptLines.value[0];
                if (_line?.product?.code) {
                    const _product = <ExtractEdges<any>>_line.product;
                    const _response = extractEdges(
                        await this.$.graph
                            .node('@sage/wh-input/AdministrativeReceiptMovementInProgress')
                            .query(
                                ui.queryUtils.edgesSelector<AdministrativeReceiptMovementInProgressBinding>(
                                    {
                                        _id: true,
                                        site: { code: true, _id: true },
                                        receipt: { code: true, _id: true },
                                        lineNumber: true,
                                        receiptMovementNumber: true,
                                        depositor: { code: true, _id: true },
                                        product: { code: true, _id: true },
                                        numberOfContainers: true,
                                        container: { container: { code: true, _id: true }, _id: true },
                                        numberOfConsumptionUnit: true,
                                        homogeneousQuantity: true,
                                        homogeneousContainer: { container: { code: true, _id: true }, _id: true },
                                    },
                                    {
                                        filter: {
                                            site: { code: this._siteCodeSelected },
                                            receipt: { code: this.administrativeReceipt.value?.code },
                                            lineNumber: _line?.lineNumber,
                                        },
                                    },
                                ),
                            )
                            .execute(),
                    );

                    /**
                     * Update product information in binding movements (consumption unit)
                     */
                    if (_response.length) {
                        for (let index = 0; index < _response.length; index++) {
                            _response[index] = { ..._response[index], product: _product };
                        }
                    }

                    return <PartialCollectionValueWithIds<AdministrativeReceiptMovementInProgressBinding>[]>(
                        (<unknown>_response)
                    );
                }
            }
        } catch (error) {
            console.error(`Error reading administrative receipt movements :\n${error}`);
        }
        return [];
    }

    /**
     * Get highest administrative receipt line number
     * @returns
     */
    private async _getMaximumAdministrativeReceiptLineNumber(): Promise<number> {
        try {
            if (this.selectedAdministrativeReceiptLines.value.length) {
                const _line = this.selectedAdministrativeReceiptLines.value[0];
                if (_line?.product?.code) {
                    const _filter: Filter<any> = {
                        site: { code: this._siteCodeSelected },
                        receipt: { code: this.administrativeReceipt.value?.code },
                        expectedInput: { code: _line.expectedInput?.code },
                        expectedInputLine: { lineNumber: _line.expectedInputLine?.lineNumber },
                    };

                    const _lineNumberInProgress =
                        extractEdges(
                            await this.$.graph
                                .node('@sage/wh-input/AdministrativeReceiptLineInProgress')
                                .query(
                                    ui.queryUtils.edgesSelector<AdministrativeReceiptLineInProgressBinding>(
                                        {
                                            _id: true,
                                            receipt: { code: true, _id: true },
                                            lineNumber: true,
                                            depositor: { code: true, _id: true },
                                            product: { code: true, _id: true },
                                        },
                                        {
                                            filter: _filter,
                                            first: 1,
                                            orderBy: { lineNumber: -1 },
                                        },
                                    ),
                                )
                                .execute(),
                        )[0]?.lineNumber ?? 0;

                    const _lineNumberValidated =
                        extractEdges(
                            await this.$.graph
                                .node('@sage/wh-input/AdministrativeReceiptLineValidated')
                                .query(
                                    ui.queryUtils.edgesSelector<AdministrativeReceiptLineValidated>(
                                        {
                                            _id: true,
                                            receipt: { code: true, _id: true },
                                            lineNumber: true,
                                            depositor: { code: true, _id: true },
                                            product: { code: true, _id: true },
                                        },
                                        {
                                            filter: _filter,
                                            first: 1,
                                            orderBy: { lineNumber: -1 },
                                        },
                                    ),
                                )
                                .execute(),
                        )[0]?.lineNumber ?? 0;

                    return Math.max(_lineNumberInProgress, _lineNumberValidated, _line?.lineNumber ?? 0);
                }
            }
        } catch (error) {
            console.error(`Error reading administrative receipt movements :\n${error}`);
        }
        return 0;
    }

    /**
     *  Get products on administrative receipt
     * @returns
     */
    private async _getProductsOnAdministrativeReceipt(includeNoSupplier = false): Promise<ProductsWithSuppliers> {
        interface AggregatedProductsOnAdministrativeReceipt {
            group: {
                product: {
                    code: string;
                };
                expectedInput: {
                    supplier: {
                        code: string;
                    };
                };
            };
        }

        const _productsWithSuppliers = <ProductsWithSuppliers>[];

        try {
            if (this.administrativeReceipt.value?.code) {
                const _response = extractEdges<AggregatedProductsOnAdministrativeReceipt>(
                    (await this.$.graph
                        .node('@sage/wh-input/AdministrativeReceiptLineInProgress')
                        .aggregate.query(
                            ui.queryUtils.edgesSelector(
                                {
                                    group: {
                                        product: {
                                            code: {
                                                _by: 'value',
                                            },
                                        },
                                        expectedInput: {
                                            supplier: {
                                                code: {
                                                    _by: 'value',
                                                },
                                            },
                                        },
                                    },
                                },
                                {
                                    filter: {
                                        site: { code: this._siteCodeSelected },
                                        depositor: { code: this._depositorCodeSelected },
                                        receipt: { code: this.administrativeReceipt.value?.code },
                                        ...(!includeNoSupplier && {
                                            expectedInput: { supplier: { code: { _ne: null } } },
                                        }),
                                    },
                                    orderBy: { product: { code: 1 }, expectedInput: { supplier: { code: 1 } } },
                                    first: 1000,
                                },
                            ),
                        )
                        .execute()) as Edges<AggregatedProductsOnAdministrativeReceipt>,
                );

                /**
                 * Update each product and their suppliers
                 */

                for (const _line of _response) {
                    const _supplierCode = _line.group.expectedInput.supplier.code;

                    if (!includeNoSupplier && !_supplierCode) {
                        continue;
                    }

                    const _productCode = _line.group.product.code;
                    const _product = _productsWithSuppliers.find(_ => _.productCode === _line.group.product.code);
                    if (_product) {
                        if (!_product.suppliersCode.includes(_supplierCode)) {
                            _product.suppliersCode.push(_supplierCode);
                        }
                    } else {
                        _productsWithSuppliers.push({
                            productCode: _productCode,
                            suppliersCode: [_supplierCode],
                        });
                    }
                }

                return _productsWithSuppliers;
            }
        } catch (error) {
            console.error(`Error reading administrative receipt movements :\n${error}`);
        }
        return [];
    }

    /* Confirm delete dialog
     *   @param optional message
     *   @returns true if the user confirms the deletion
     */
    private async _dialogConfirmDelete(
        message: string = ui.localize(
            '@sage/wh-input/dialog__confirm_delete_action__administrative-receipt__message',
            'You are going to make a deletion. This action cannot be undone. Do you want to continue?',
        ),
    ): Promise<boolean> {
        const options: ui.dialogs.DialogOptions = {
            acceptButton: {
                text: ui.localize('@sage/wh-input/button-accept-continue', 'Continue'),
            },
            cancelButton: {
                text: ui.localize('@sage/wh-input/button-cancel', 'Cancel'),
            },
            fullScreen: true,
        };
        return await dialogConfirmation(
            this,
            'warn',
            ui.localize('@sage/wh-input/dialog__delete_action_title', 'Confirm delete'),
            message,
            options,
        );
    }

    /**
     * Prepare the import payload
     * @param operatorCode The operator code
     * @returns {ImportAdministrativeReceipt} The import payload
     */
    private prepareDataMutation(operatorCode: string = ''): ImportAdministrativeReceipt {
        const _administrativeReceiptLines =
            this.administrativeReceiptLines.value
                ?.map<ImportAdministrativeReceiptLineInProgress>(line => {
                    const _administrativeReceiptMovements =
                        line.administrativeReceiptMovements?.map<ImportAdministrativeReceiptMovementInProgress>(
                            movement => ({
                                numberOfContainers: Number(movement.numberOfContainers ?? 0),
                                container: movement.container?.container?.code ?? '',
                                homogeneousQuantity: Number(movement.homogeneousQuantity ?? 0),
                                homogeneousContainer: movement.homogeneousContainer?.container?.code ?? '',
                            }),
                        );
                    return <ImportAdministrativeReceiptLineInProgress>{
                        lineNumber: line.lineNumber ?? 0,
                        expectedInput: line.expectedInput?.code ?? '',
                        expectedInputLine: line.expectedInputLine?.lineNumber ?? 0,
                        receivedQuantityInConsumptionUnit: Number(line.receivedQuantityInConsumptionUnit ?? 0),
                        inputContainer: line.inputContainer?.container?.code ?? '',
                        stockStatus: line.stockStatus?.code ?? '',
                        isDispute: line.isDispute ?? false,
                        isPutaway: line.isPutaway ?? true,
                        store: line.store?.code ?? '',
                        isExclusiveStore: line.isExclusiveStore ?? false,
                        lotNumber: line.lotNumber ?? '',
                        reservationNumber: line.reservationNumber ?? '',
                        supportNumber: line.supportNumber ?? '',
                        manufacturedDate: line.manufacturedDate ?? null,
                        sellByDate: line.sellByDate ?? null,
                        detentionDate: line.detentionDate ?? null,
                        useByDate: line.useByDate ?? null,
                        shipByDate: line.shipByDate ?? null,
                        fifoDate: line.fifoDate ?? null,
                        actionImport: line.actionImport ?? '',
                        ...(!!_administrativeReceiptMovements && {
                            administrativeReceiptMovements: _administrativeReceiptMovements,
                        }),
                    };
                })
                .sort((a, b) => {
                    if (a.expectedInput !== b.expectedInput) {
                        return a.expectedInput < b.expectedInput ? -1 : 1;
                    }
                    if (a.expectedInputLine !== b.expectedInputLine) {
                        return a.expectedInputLine - b.expectedInputLine;
                    }
                    if (a.lineNumber - b.lineNumber) {
                        return a.lineNumber - b.lineNumber;
                    }
                    if (a.actionImport !== b.actionImport) {
                        return !a.actionImport ? -1 : 1;
                    }
                    return 0;
                }) ?? [];

        return <ImportAdministrativeReceipt>{
            code: this.administrativeReceipt.value?.code ?? '',
            operatorCode: operatorCode,
            ...(!!_administrativeReceiptLines && { inProgressLines: _administrativeReceiptLines }),
        };
    }

    /**
     * Call the creation API
     * @param operatorCode The operator code
     * @returns {any} The result of the API call
     */
    private async _callCreationAPI(operatorCode: string = ''): Promise<any> {
        const _args = <any>this.prepareDataMutation(operatorCode);

        let _result: any;
        try {
            _result = (
                await this.$.graph
                    .node('@sage/wh-input/AdministrativeReceipt')
                    .mutations.validateReceipt(
                        {
                            code: true,
                        },
                        {
                            parameter: _args,
                        },
                    )
                    .execute()
            )?.code;
            if (!_result) {
                throw Error(
                    ui.localize(
                        '@sage/wh-input/pages__mobile_administrative_receipt__notification__no-receipts-were-created',
                        'No receipts were created.',
                    ),
                );
            }
        } catch (error) {
            return error;
        }
        return _result;
    }
}
