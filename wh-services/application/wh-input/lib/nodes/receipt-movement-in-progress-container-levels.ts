import * as sageWhProductData from '@sage/wh-product-data';
import { decorators, Node, integer, Reference, decimal, Context } from '@sage/xtrem-core';
import { X3StorageManager, Joins, Denormalized } from '@sage/xtrem-x3-gateway';
import * as sageXtremX3SystemUtils from '@sage/xtrem-x3-system-utils';
import * as sageWhInput from '..';

const denormalized: Denormalized = {
    maxRepeat: (context: Context) => sageXtremX3SystemUtils.getSizingFromActivityCode(context, 'CTR'),
};

const joins: Joins<ReceiptMovementInProgressContainerLevels> = {
    referenceJoins: {
        _denormalizedParent: {
            site: 'site',
            depositor: 'depositor',
            directInput: 'directInput',
            directInputLine: 'directInputLine',
            code: 'code',
        },
        containerLevel: {
            site: 'site',
            depositor: 'depositor',
            product: 'product',
            container: 'containerLevel',
        },
    },
};

@decorators.node<ReceiptMovementInProgressContainerLevels>({
    storage: 'external',
    tableName: 'INPUTM',
    keyPropertyNames: ['denormalizedIndex', 'site', 'depositor', 'directInput', 'directInputLine', 'code'],
    indexes: [],
    externalStorageManager: new X3StorageManager({
        joins,
        isDenormalized: true,
        denormalized,
    }),
    isPublished: true,
    canRead: true,
    canSearch: true,
    isVitalCollectionChild: true,
})
export class ReceiptMovementInProgressContainerLevels extends Node {
    @decorators.integerProperty<ReceiptMovementInProgressContainerLevels, 'denormalizedIndex'>({
        isPublished: true,
    })
    readonly denormalizedIndex: Promise<integer>;

    @decorators.stringProperty<ReceiptMovementInProgressContainerLevels, 'site'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'FCY',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly site: Promise<string>;

    @decorators.stringProperty<ReceiptMovementInProgressContainerLevels, 'depositor'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'DEP',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly depositor: Promise<string>;

    @decorators.stringProperty<ReceiptMovementInProgressContainerLevels, 'directInput'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'INPNUM',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly directInput: Promise<string>;

    @decorators.integerProperty<ReceiptMovementInProgressContainerLevels, 'directInputLine'>({
        isPublished: true,
        isStored: true,
        columnName: 'INPLIN',
    })
    readonly directInputLine: Promise<integer>;

    @decorators.stringProperty<ReceiptMovementInProgressContainerLevels, 'code'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'MVTNUM',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<ReceiptMovementInProgressContainerLevels, '_denormalizedParent'>({
        isStored: true,
        isVitalParent: true,
        columnType: 'string',
        node: () => sageWhInput.nodes.ReceiptMovementInProgress,
    })
    readonly _denormalizedParent: Reference<sageWhInput.nodes.ReceiptMovementInProgress>;

    @decorators.referenceProperty<ReceiptMovementInProgressContainerLevels, 'containerLevel'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'CTRLVL',
        columnType: 'string',
        node: () => sageWhProductData.nodes.ProductContainer,
    })
    readonly containerLevel: Reference<sageWhProductData.nodes.ProductContainer | null>;

    @decorators.decimalProperty<ReceiptMovementInProgressContainerLevels, 'quantityOfConsumptionUnitPerLevel'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'CUQLVL',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.decimalDatatype,
    })
    readonly quantityOfConsumptionUnitPerLevel: Promise<decimal | null>;
}
