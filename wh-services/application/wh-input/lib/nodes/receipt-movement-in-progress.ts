import * as sageWhMasterData from '@sage/wh-master-data';
import * as sageWhProductData from '@sage/wh-product-data';
import * as sageWhStockData from '@sage/wh-stock-data';
import * as sageWhSystem from '@sage/wh-system';
import { decorators, Node, Reference, decimal, date, integer, Collection } from '@sage/xtrem-core';
import { X3StorageManager, Joins } from '@sage/xtrem-x3-gateway';
import * as sageXtremX3SystemUtils from '@sage/xtrem-x3-system-utils';
import * as sageWhInput from '..';

const joins: Joins<ReceiptMovementInProgress> = {
    referenceJoins: {
        site: {
            code: 'site',
        },
        depositor: {
            site: 'site',
            code: 'depositor',
        },
        directInput: {
            site: 'site',
            depositor: 'depositor',
            code: 'directInput',
        },
        directInputLine: {
            site: 'site',
            depositor: 'depositor',
            directInput: 'directInput',
            lineNumber: 'directInputLine',
        },
        store: {
            site: 'site',
            code: 'store',
        },
        imposedStore: {
            site: 'site',
            code: 'imposedStore',
        },
        location: {
            site: 'site',
            store: 'store',
            code: 'location',
        },
        stockObject: {
            site: 'site',
            code: 'stockObject',
        },
        product: {
            site: 'site',
            depositor: 'depositor',
            code: 'product',
        },
        stockStatus: {
            site: 'site',
            code: 'stockStatus',
        },
        source: {
            site: 'site',
            depositor: 'depositor',
            code: 'source',
        },
        container: {
            site: 'site',
            depositor: 'depositor',
            product: 'product',
            container: 'container',
        },
        homogeneousContainer: {
            site: 'site',
            depositor: 'depositor',
            product: 'product',
            container: 'homogeneousContainer',
        },
        movementCode: {
            site: 'site',
            code: 'movementCode',
        },
        storingList: {
            site: 'site',
            depositor: 'depositor',
            code: 'storingList',
        },
    },
};

@decorators.node<ReceiptMovementInProgress>({
    storage: 'external',
    tableName: 'INPUTM',
    keyPropertyNames: ['site', 'depositor', 'directInput', 'directInputLine', 'code'],
    indexes: [
        {
            orderBy: {
                site: 1,
                depositor: 1,
                directInput: 1,
                directInputLine: 1,
                code: 1,
            },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    externalStorageManager: new X3StorageManager({
        joins,
    }),
    getFilters() {
        return [{ status: { _nin: ['beingCreated', 'addressed', 'storedClosed'] } }];
    },
    isPublished: true,
    canRead: true,
    canSearch: true,
})
export class ReceiptMovementInProgress extends Node {
    @decorators.referenceProperty<ReceiptMovementInProgress, 'site'>({
        isPublished: true,
        isStored: true,
        columnName: 'FCY',
        columnType: 'string',
        node: () => sageWhSystem.nodes.Site,
    })
    readonly site: Reference<sageWhSystem.nodes.Site>;

    @decorators.referenceProperty<ReceiptMovementInProgress, 'depositor'>({
        isPublished: true,
        isStored: true,
        columnName: 'DEP',
        columnType: 'string',
        node: () => sageWhMasterData.nodes.Depositor,
    })
    readonly depositor: Reference<sageWhMasterData.nodes.Depositor>;

    @decorators.referenceProperty<ReceiptMovementInProgress, 'directInput'>({
        isPublished: true,
        isStored: true,
        columnName: 'INPNUM',
        columnType: 'string',
        node: () => sageWhInput.nodes.DirectInput,
    })
    readonly directInput: Reference<sageWhInput.nodes.DirectInput>;

    @decorators.referenceProperty<ReceiptMovementInProgress, 'directInputLine'>({
        isPublished: true,
        isStored: true,
        columnName: 'INPLIN',
        columnType: 'integer',
        node: () => sageWhInput.nodes.DirectInputLine,
    })
    readonly directInputLine: Reference<sageWhInput.nodes.DirectInputLine>;

    @decorators.stringProperty<ReceiptMovementInProgress, 'code'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'MVTNUM',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<ReceiptMovementInProgress, 'store'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'STO',
        columnType: 'string',
        node: () => sageWhMasterData.nodes.Store,
    })
    readonly store: Reference<sageWhMasterData.nodes.Store | null>;

    @decorators.referenceProperty<ReceiptMovementInProgress, 'imposedStore'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'IPSSTO',
        columnType: 'string',
        node: () => sageWhMasterData.nodes.Store,
    })
    readonly imposedStore: Reference<sageWhMasterData.nodes.Store | null>;

    @decorators.booleanProperty<ReceiptMovementInProgress, 'isExclusiveStore'>({
        isPublished: true,
        isStored: true,
        columnName: 'STOXCL',
    })
    readonly isExclusiveStore: Promise<boolean>;

    @decorators.referenceProperty<ReceiptMovementInProgress, 'location'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'ADD',
        columnType: 'string',
        node: () => sageWhMasterData.nodes.Location,
    })
    readonly location: Reference<sageWhMasterData.nodes.Location | null>;

    @decorators.enumProperty<ReceiptMovementInProgress, 'locationType'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'SLOTYP',
        dataType: () => sageWhMasterData.enums.locationTypeDatatype,
    })
    readonly locationType: Promise<sageWhMasterData.enums.LocationType | null>;

    @decorators.referenceProperty<ReceiptMovementInProgress, 'stockObject'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'SKONUM',
        columnType: 'string',
        node: () => sageWhStockData.nodes.StockObject,
    })
    readonly stockObject: Reference<sageWhStockData.nodes.StockObject | null>;

    @decorators.stringProperty<ReceiptMovementInProgress, 'containerNumber'>({
        isPublished: true,
        isStored: true,
        columnName: 'CTRLIKNUM',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly containerNumber: Promise<string>;

    @decorators.referenceProperty<ReceiptMovementInProgress, 'product'>({
        isPublished: true,
        isStored: true,
        columnName: 'ITM',
        columnType: 'string',
        node: () => sageWhProductData.nodes.Product,
    })
    readonly product: Reference<sageWhProductData.nodes.Product>;

    @decorators.referenceProperty<ReceiptMovementInProgress, 'stockStatus'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'SKN',
        columnType: 'string',
        node: () => sageWhMasterData.nodes.StockNature,
    })
    readonly stockStatus: Reference<sageWhMasterData.nodes.StockNature | null>;

    @decorators.referenceProperty<ReceiptMovementInProgress, 'source'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'ITMORI',
        columnType: 'string',
        node: () => sageWhProductData.nodes.Origin,
    })
    readonly source: Reference<sageWhProductData.nodes.Origin | null>;

    @decorators.stringProperty<ReceiptMovementInProgress, 'lotNumber'>({
        isPublished: true,
        isStored: true,
        columnName: 'LOT',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly lotNumber: Promise<string>;

    @decorators.stringProperty<ReceiptMovementInProgress, 'reservationNumber'>({
        isPublished: true,
        isStored: true,
        columnName: 'RSA',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly reservationNumber: Promise<string>;

    @decorators.stringProperty<ReceiptMovementInProgress, 'supportNumber'>({
        isPublished: true,
        isStored: true,
        columnName: 'SPT',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly supportNumber: Promise<string>;

    @decorators.decimalProperty<ReceiptMovementInProgress, 'numberOfContainers'>({
        isPublished: true,
        isStored: true,
        columnName: 'CTRQTY',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.decimalDatatype,
    })
    readonly numberOfContainers: Promise<decimal>;

    @decorators.referenceProperty<ReceiptMovementInProgress, 'container'>({
        isPublished: true,
        isStored: true,
        columnName: 'CTR',
        columnType: 'string',
        node: () => sageWhProductData.nodes.ProductContainer,
    })
    readonly container: Reference<sageWhProductData.nodes.ProductContainer>;

    @decorators.decimalProperty<ReceiptMovementInProgress, 'quantityInConsumptionUnit'>({
        isPublished: true,
        isStored: true,
        columnName: 'CSUQTY',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.decimalDatatype,
    })
    readonly quantityInConsumptionUnit: Promise<decimal>;

    @decorators.enumProperty<ReceiptMovementInProgress, 'level'>({
        isPublished: true,
        isStored: true,
        columnName: 'LVL',
        dataType: () => sageWhMasterData.enums.containerLevelDatatype,
    })
    readonly level: Promise<sageWhMasterData.enums.ContainerLevel>;

    @decorators.decimalProperty<ReceiptMovementInProgress, 'standardizedQuantity'>({
        isPublished: true,
        isStored: true,
        columnName: 'HMGQTY',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.decimalDatatype,
    })
    readonly standardizedQuantity: Promise<decimal>;

    @decorators.referenceProperty<ReceiptMovementInProgress, 'homogeneousContainer'>({
        isPublished: true,
        isStored: true,
        columnName: 'HMGCTR',
        columnType: 'string',
        node: () => sageWhProductData.nodes.ProductContainer,
    })
    readonly homogeneousContainer: Reference<sageWhProductData.nodes.ProductContainer>;

    @decorators.decimalProperty<ReceiptMovementInProgress, 'homogeneousContainerCoefficient'>({
        isPublished: true,
        isStored: true,
        columnName: 'HMGCOE',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.decimalDatatype,
    })
    readonly homogeneousContainerCoefficient: Promise<decimal>;

    @decorators.referenceProperty<ReceiptMovementInProgress, 'movementCode'>({
        isPublished: true,
        isStored: true,
        columnName: 'MVTCOD',
        columnType: 'string',
        node: () => sageWhMasterData.nodes.MovementCode,
    })
    readonly movementCode: Reference<sageWhMasterData.nodes.MovementCode>;

    @decorators.enumProperty<ReceiptMovementInProgress, 'status'>({
        isPublished: true,
        isStored: true,
        columnName: 'INMSTA',
        dataType: () => sageWhProductData.enums.inputMovementStatusDatatype,
    })
    readonly status: Promise<sageWhProductData.enums.InputMovementStatus>;

    @decorators.dateProperty<ReceiptMovementInProgress, 'fifoDate'>({
        isPublished: true,
        isStored: true,
        columnName: 'FIFDAT',
        defaultValue() {
            return X3StorageManager.getDateDefaultValue(this);
        },
    })
    readonly fifoDate: Promise<date>;

    @decorators.dateProperty<ReceiptMovementInProgress, 'receiptDate'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'INMDAT',
        defaultValue() {
            return X3StorageManager.getDateDefaultValue(this);
        },
    })
    readonly receiptDate: Promise<date | null>;

    @decorators.dateProperty<ReceiptMovementInProgress, 'manufacturedDate'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'MND',
        defaultValue() {
            return X3StorageManager.getDateDefaultValue(this);
        },
    })
    readonly manufacturedDate: Promise<date | null>;

    @decorators.dateProperty<ReceiptMovementInProgress, 'detentionDate'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'RTD',
        defaultValue() {
            return X3StorageManager.getDateDefaultValue(this);
        },
    })
    readonly detentionDate: Promise<date | null>;

    @decorators.dateProperty<ReceiptMovementInProgress, 'sellByDate'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'SLD',
        defaultValue() {
            return X3StorageManager.getDateDefaultValue(this);
        },
    })
    readonly sellByDate: Promise<date | null>;

    @decorators.dateProperty<ReceiptMovementInProgress, 'useByDate'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'USD',
        defaultValue() {
            return X3StorageManager.getDateDefaultValue(this);
        },
    })
    readonly useByDate: Promise<date | null>;

    @decorators.dateProperty<ReceiptMovementInProgress, 'shipByDate'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'SND',
        defaultValue() {
            return X3StorageManager.getDateDefaultValue(this);
        },
    })
    readonly shipByDate: Promise<date | null>;

    @decorators.referenceProperty<ReceiptMovementInProgress, 'storingList'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'STLNUM',
        columnType: 'string',
        node: () => sageWhInput.nodes.StoringList,
    })
    readonly storingList: Reference<sageWhInput.nodes.StoringList | null>;

    @decorators.integerProperty<ReceiptMovementInProgress, 'storingListLineNumber'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'STLLIN',
    })
    readonly storingListLineNumber: Promise<integer | null>;

    @decorators.enumProperty<ReceiptMovementInProgress, 'movementType'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'MVTTYP',
        dataType: () => sageWhProductData.enums.movementTypeDatatype,
    })
    readonly movementType: Promise<sageWhProductData.enums.MovementType | null>;

    @decorators.collectionProperty<ReceiptMovementInProgress, 'containerLevels'>({
        isPublished: true,
        isVital: true,
        reverseReference: '_denormalizedParent',
        node: () => sageWhInput.nodes.ReceiptMovementInProgressContainerLevels,
    })
    readonly containerLevels: Collection<sageWhInput.nodes.ReceiptMovementInProgressContainerLevels>;
}
