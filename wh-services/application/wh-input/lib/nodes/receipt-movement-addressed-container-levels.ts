import * as sageWhProductData from '@sage/wh-product-data';
import { decorators, Node, integer, Reference, decimal, Context } from '@sage/xtrem-core';
import { X3StorageManager, Joins, Denormalized } from '@sage/xtrem-x3-gateway';
import * as sageXtremX3SystemUtils from '@sage/xtrem-x3-system-utils';
import * as sageWhInput from '..';

const denormalized: Denormalized = {
    maxRepeat: (context: Context) => sageXtremX3SystemUtils.getSizingFromActivityCode(context, 'CTR'),
};

const joins: Joins<ReceiptMovementAddressedContainerLevels> = {
    referenceJoins: {
        _denormalizedParent: {
            site: 'site',
            depositor: 'depositor',
            directInput: 'directInput',
            directInputLine: 'directInputLine',
            code: 'code',
        },
        containerLevel: {
            site: 'site',
            depositor: 'depositor',
            product: 'product',
            container: 'containerLevel',
        },
    },
};

@decorators.node<ReceiptMovementAddressedContainerLevels>({
    storage: 'external',
    tableName: 'INPUTM',
    keyPropertyNames: ['denormalizedIndex', 'site', 'depositor', 'directInput', 'directInputLine', 'code'],
    indexes: [],
    externalStorageManager: new X3StorageManager({
        joins,
        isDenormalized: true,
        denormalized,
    }),
    isPublished: true,
    canRead: true,
    canSearch: true,
    isVitalCollectionChild: true,
})
export class ReceiptMovementAddressedContainerLevels extends Node {
    @decorators.integerProperty<ReceiptMovementAddressedContainerLevels, 'denormalizedIndex'>({
        isPublished: true,
    })
    readonly denormalizedIndex: Promise<integer>;

    @decorators.stringProperty<ReceiptMovementAddressedContainerLevels, 'site'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'FCY',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly site: Promise<string>;

    @decorators.stringProperty<ReceiptMovementAddressedContainerLevels, 'depositor'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'DEP',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly depositor: Promise<string>;

    @decorators.stringProperty<ReceiptMovementAddressedContainerLevels, 'directInput'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'INPNUM',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly directInput: Promise<string>;

    @decorators.integerProperty<ReceiptMovementAddressedContainerLevels, 'directInputLine'>({
        isPublished: true,
        isStored: true,
        columnName: 'INPLIN',
    })
    readonly directInputLine: Promise<integer>;

    @decorators.stringProperty<ReceiptMovementAddressedContainerLevels, 'code'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'MVTNUM',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<ReceiptMovementAddressedContainerLevels, '_denormalizedParent'>({
        isStored: true,
        isVitalParent: true,
        columnType: 'string',
        node: () => sageWhInput.nodes.ReceiptMovementAddressed,
    })
    readonly _denormalizedParent: Reference<sageWhInput.nodes.ReceiptMovementAddressed>;

    @decorators.referenceProperty<ReceiptMovementAddressedContainerLevels, 'containerLevel'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'CTRLVL',
        columnType: 'string',
        node: () => sageWhProductData.nodes.ProductContainer,
    })
    readonly containerLevel: Reference<sageWhProductData.nodes.ProductContainer | null>;

    @decorators.decimalProperty<ReceiptMovementAddressedContainerLevels, 'quantityOfConsumptionUnitPerLevel'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'CUQLVL',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.decimalDatatype,
    })
    readonly quantityOfConsumptionUnitPerLevel: Promise<decimal | null>;
}
