{
    whInput {
        receiptMovementInProgress {
            query(
                filter: "{ site: { code: '01'}, depositor: { code: 'ALPHA'}, directInput: {code:'0000001'}, directInputLine: {lineNumber: 1}, code:'0000000001' }"
            ) {
                edges {
                    node {
                        site {
                            code
                        }
                        directInput {
                            code
                        }
                        directInputLine {
                            lineNumber
                        }
                        code
                        store {
                            code
                        }
                        imposedStore {
                            code
                        }
                        isExclusiveStore
                        locationType
                        stockObject {
                            code
                        }
                        containerNumber
                        depositor {
                            code
                        }
                        product {
                            code
                        }
                        stockStatus {
                            code
                        }
                        source {
                            code
                        }
                        lotNumber
                        reservationNumber
                        supportNumber
                        numberOfContainers
                        container {
                            container {
                                code
                            }
                        }
                        quantityInConsumptionUnit
                        level
                        standardizedQuantity
                        homogeneousContainer {
                            container {
                                code
                            }
                        }
                        homogeneousContainerCoefficient
                        containerLevels {
                            query {
                                edges {
                                    node {
                                        denormalizedIndex
                                        containerLevel {
                                            container {
                                                code
                                                containerLevel
                                            }
                                        }
                                        quantityOfConsumptionUnitPerLevel
                                    }
                                }
                            }
                        }
                        status
                        fifoDate
                        receiptDate
                        manufacturedDate
                        detentionDate
                        sellByDate
                        useByDate
                        shipByDate
                        storingList {
                            code
                        }
                        storingListLineNumber
                        movementType
                    }
                }
            }
        }
    }
}
