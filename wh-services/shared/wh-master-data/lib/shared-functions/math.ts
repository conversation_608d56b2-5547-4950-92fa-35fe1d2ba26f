/**
 * Truncate quantity to a number of decimals
 * @param quantity  quantity to truncate
 * @param precision number of decimals or undefined (truncate to integer)
 * @returns truncated quantity
 */
export function truncate(quantity: number, precision?: number): number {
    if (!precision) {
        return Math.trunc(quantity);
    }
    const _precision = 10 ** (precision ?? 0);
    return Math.trunc(quantity * _precision) / _precision;
}
