import { WhSite } from '@sage/wh-master-data-api';
import { extractEdges } from '@sage/xtrem-client';
import * as ui from '@sage/xtrem-ui';

/**
 *  Site configuration interface must be located in the same folder as the client function
 *  Duplicated in environnement interfaces
 */
export interface SiteConfiguration {
    eanDefaultMethod: string;
    isDripAndDropAllowed: boolean;
    supplierBarCodeRange1: [number, number];
    supplierBarCodeRange2: [number, number];
}

/**
 * Load current site configuration, assign default mode and full list
 *  @returns  SiteConfiguration
 */
export async function getSiteConfiguration(
    pageInstance: ui.Page,
    siteCode: string | undefined,
): Promise<SiteConfiguration | undefined> {
    interface _ExtractEdgedSite {
        eanDefaultMethod: string;
        isDripAndDropAllowed: boolean;
        supplierBarCodes: {
            supplierBarCodeStart: string;
            supplierBarCodeEnd: string;
        }[];
    }
    try {
        if (siteCode) {
            const _response = extractEdges<_ExtractEdgedSite>(
                await pageInstance.$.graph
                    .node('@sage/wh-master-data/WhSite')
                    .query(
                        ui.queryUtils.edgesSelector<WhSite>(
                            {
                                eanDefaultMethod: true,
                                isDripAndDropAllowed: true,
                                supplierBarCodes: {
                                    query: {
                                        edges: {
                                            node: {
                                                supplierBarCodeStart: true,
                                                supplierBarCodeEnd: true,
                                            },
                                        },
                                    },
                                },
                            },
                            {
                                filter: {
                                    site: { code: siteCode },
                                },
                            },
                        ),
                    )
                    .execute(),
            )[0];

            if (_response) {
                return <SiteConfiguration>{
                    eanDefaultMethod: _response.eanDefaultMethod ?? 'supplierBC',
                    isDripAndDropAllowed: _response.isDripAndDropAllowed,
                    supplierBarCodeRange1: [
                        Number(_response.supplierBarCodes[0].supplierBarCodeStart),
                        Number(_response.supplierBarCodes[0].supplierBarCodeEnd),
                    ],
                    supplierBarCodeRange2: [
                        Number(_response.supplierBarCodes[1].supplierBarCodeStart),
                        Number(_response.supplierBarCodes[1].supplierBarCodeEnd),
                    ],
                };
            }
        }
    } catch (error) {
        console.error(`Error reading site configuration :\n${error}`);
    }
    return undefined;
}
