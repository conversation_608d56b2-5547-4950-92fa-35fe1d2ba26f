import { Depositor } from '@sage/wh-master-data-api';
import { extractEdges } from '@sage/xtrem-client';
import * as ui from '@sage/xtrem-ui';

/**
 *  Depositor configuration interface must be located in the same folder as the client function
 *  Duplicated in environnement interfaces
 */
export interface DepositorConfiguration {
    isLotNumber: boolean;
    isSupportNumber: boolean;
}

/**
 *  Load current depositor configuration
 *  @returns
 */
export async function getDepositorConfiguration(
    pageInstance: ui.Page,
    siteCode: string | undefined,
    depositorCode: string | undefined,
): Promise<DepositorConfiguration | undefined> {
    interface _ExtractEdgesDepositor {
        code: string;
        isLotNumber: boolean;
        isSupportNumber: boolean;
        site: { code: string };
    }
    try {
        if (siteCode && depositorCode) {
            const _response = extractEdges<_ExtractEdgesDepositor>(
                await pageInstance.$.graph
                    .node('@sage/wh-master-data/Depositor')
                    .query(
                        ui.queryUtils.edgesSelector<Depositor>(
                            {
                                code: true,
                                isLotNumber: true,
                                isSupportNumber: true,
                            },
                            {
                                filter: {
                                    site: { code: siteCode },
                                    code: depositorCode,
                                },
                            },
                        ),
                    )
                    .execute(),
            )[0];

            if (_response) {
                return <DepositorConfiguration>{
                    isLotNumber: _response.isLotNumber,
                    isSupportNumber: _response.isSupportNumber,
                };
            }
        }
    } catch (error) {
        console.error(`Error reading depositor configuration :\n${error}`);
    }
    return undefined;
}
