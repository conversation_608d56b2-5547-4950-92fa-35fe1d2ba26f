import { Operand, WhSite } from '@sage/wh-master-data-api';
import { extractEdges } from '@sage/xtrem-client';
import * as ui from '@sage/xtrem-ui';
import { GetOperatorArgs } from '../pages/mobile-get-operator';

/**
 * @description: This enum is used to define the authorized operation for the current site.
 * @example: AuthorizedOperation.receipt
 */
export enum AuthorizedOperation {
    adjustment = 'adjustment',
    carrierEdi = 'carrierEdi',
    consumableInput = 'consumableInput',
    consumableOutput = 'consumableOutput',
    picking = 'picking',
    receipt = 'receipt',
    repacking = 'repacking',
    simplifiedStockCount = 'simplifiedStockCount',
    transfer = 'transfer',
}

/**
 *  @description: This function is used to get the authorized operator for current site:
 *                she returns an operator code when required, otherwise an empty string, and undefined when not applicable.
 *  @example: getAuthorizedOperator(AuthorizedOperation.receipt)
 *  @param pageInstance: ui.Page,
 *  @param siteCode : string
 *  @param operatorCode : string
 *  @param operationCode : AuthorizedOperation
 *  @returns: Promise<string | undefined | Error>
 */
export async function getAuthorizedOperator(
    pageInstance: ui.Page,
    siteCode: string,
    operationCode: AuthorizedOperation,
): Promise<string | undefined | Error> {
    // Check if the operator is required for the current site and operation
    const _isOperatorRequired = await isOperatorRequired(pageInstance, siteCode, operationCode);
    if (!_isOperatorRequired || _isOperatorRequired instanceof Error) {
        return _isOperatorRequired instanceof Error ? _isOperatorRequired : '';
    }

    let _operatorCode: string | undefined = undefined;

    try {
        const options: ui.dialogs.DialogOptions = {
            fullScreen: true,
        };

        // If the window is closed without validation, we catch the standard exception and return undefined
        _operatorCode = await pageInstance.$.dialog
            .page(
                '@sage/wh-master-data/MobileGetOperator',
                { getOperatorArgs: _getOperatorParameters(siteCode, operationCode) },
                options,
            )
            .catch(() => '');
    } catch (error) {
        ui.console.error('Error reading operator:\n', JSON.stringify(error));
    }

    if (!_operatorCode) {
        return undefined;
    }

    // Check if the operator is authorized for the current site and operation
    const _operatorAuthorized = await isAuthorizedOperator(pageInstance, siteCode, _operatorCode, operationCode);
    // Return undefined if the operator is not authorized
    return _operatorAuthorized instanceof Error ? _operatorAuthorized : _operatorAuthorized ? _operatorCode : undefined;
}

/**
 *  @description: This function is used to get the operator parameters for the current site and operation.
 *  @example: _getOperatorParameters(this._siteSelectedCode, AuthorizedOperation.receipt)
 *  @param siteCode : string
 *  @param operationCode : AuthorizedOperation
 *  @returns: getOperatorArgs: string
 */
function _getOperatorParameters(siteCode: string, operationCode: AuthorizedOperation): string {
    return JSON.stringify(<GetOperatorArgs>{ operationCode: operationCode, siteCode: siteCode });
}

/**
 *  @description: This function is used to get the authorized operator for current site :
 *                she valid an operator code, and undefined when not applicable (no standard).
 *  @example: isOperatorRequired(this, this._siteSelectedCode, AuthorizedOperation.receipt)
 *  @param pageInstance: ui.Page,
 *  @param siteCode : string
 *  @param operationCode : AuthorizedOperation
 *  @returns: Promise<boolean | error>
 */
export async function isOperatorRequired(
    pageInstance: ui.Page,
    siteCode: string,
    operationCode: AuthorizedOperation,
): Promise<boolean | Error> {
    try {
        const _response = extractEdges<WhSite>(
            await pageInstance.$.graph
                .node('@sage/whMasterData/whSite')
                .query(
                    ui.queryUtils.edgesSelector<WhSite>(
                        {
                            isAdjustementOperationCodeAllowed: true,
                            isConsumableInputControlCodeAllowed: true,
                            isConsumableOutputControlCodeAllowed: true,
                            isEdiOperationCode: true,
                            isPickingOperationCodeAllowed: true,
                            isReceiptOperationCodeAllowed: true,
                            isSimplifiedStockCountCodeAllowed: true,
                            isRepackingOperationCodeAllowed: true,
                            isTransferOperationCodeAllowed: true,
                        },
                        {
                            filter: {
                                site: { code: siteCode },
                            },
                        },
                    ),
                )
                .execute(),
        );

        if (_response.length) {
            const _site = _response[0];
            switch (operationCode) {
                case AuthorizedOperation.adjustment:
                    return _site.isAdjustementOperationCodeAllowed;
                case AuthorizedOperation.carrierEdi:
                    return _site.isEdiOperationCode;
                case AuthorizedOperation.consumableInput:
                    return _site.isConsumableInputControlCodeAllowed;
                case AuthorizedOperation.consumableOutput:
                    return _site.isConsumableOutputControlCodeAllowed;
                case AuthorizedOperation.picking:
                    return _site.isPickingOperationCodeAllowed;
                case AuthorizedOperation.receipt:
                    return _site.isReceiptOperationCodeAllowed;
                case AuthorizedOperation.repacking:
                    return _site.isRepackingOperationCodeAllowed;
                case AuthorizedOperation.simplifiedStockCount:
                    return _site.isSimplifiedStockCountCodeAllowed;
                case AuthorizedOperation.transfer:
                    return _site.isTransferOperationCodeAllowed;
                default:
                    return false;
            }
        }
    } catch (error) {
        ui.console.error('Error reading product container:\n', JSON.stringify(error));
        return error;
    }

    return false;
}

/**
 *  @description: This function is used to get the authorized operator for current site :
 *                she valid an operator code, and undefined when not applicable (no standard).
 *  @example: isAuthorizedOperator(this, this._siteSelectedCode, _operatorCode, AuthorizedOperation.receipt)
 *  @param pageInstance: ui.Page,
 *  @param siteCode : string
 *  @param operatorCode : string
 *  @param operationCode : AuthorizedOperation
 *  @returns: Promise<boolean | error>
 */
export async function isAuthorizedOperator(
    pageInstance: ui.Page,
    siteCode: string,
    operatorCode: string,
    operationCode: AuthorizedOperation,
): Promise<boolean | Error> {
    try {
        const _response = extractEdges<Operand>(
            await pageInstance.$.graph
                .node('@sage/whMasterData/Operand')
                .query(
                    ui.queryUtils.edgesSelector<Operand>(
                        {
                            isCarrierEdiAuthorized: true,
                            isConsumableInputAuthorized: true,
                            isConsumableOuputAuthorized: true,
                            isManualAdjustmentAuthorized: true,
                            isPickingAuthorized: true,
                            isReceiptAuthorized: true,
                            isRepackingAuthorized: true,
                            isSimplifiedStockCountAuthorized: true,
                            isTransferAuthorized: true,
                        },
                        {
                            filter: {
                                site: { code: siteCode },
                                code: operatorCode,
                            },
                        },
                    ),
                )
                .execute(),
        );

        if (_response.length) {
            const _operator = _response[0];
            switch (operationCode) {
                case AuthorizedOperation.adjustment:
                    return _operator.isManualAdjustmentAuthorized;
                case AuthorizedOperation.carrierEdi:
                    return _operator.isCarrierEdiAuthorized;
                case AuthorizedOperation.consumableInput:
                    return _operator.isConsumableInputAuthorized;
                case AuthorizedOperation.consumableOutput:
                    return _operator.isConsumableOuputAuthorized;
                case AuthorizedOperation.picking:
                    return _operator.isPickingAuthorized;
                case AuthorizedOperation.receipt:
                    return _operator.isReceiptAuthorized;
                case AuthorizedOperation.repacking:
                    return _operator.isRepackingAuthorized;
                case AuthorizedOperation.simplifiedStockCount:
                    return _operator.isSimplifiedStockCountAuthorized;
                case AuthorizedOperation.transfer:
                    return _operator.isTransferAuthorized;
                default:
                    return false;
            }
        }
    } catch (error) {
        ui.console.error('Error reading product container:\n', JSON.stringify(error));
        return error;
    }

    return false;
}
