import { Graph<PERSON><PERSON> } from '@sage/wh-master-data-api';
import * as ui from '@sage/xtrem-ui';
import { AuthorizedOperation } from '../client-functions/get-authorized-operator';
import { getCurrentSiteDepositor } from '../client-functions/get-selected-site-depositor';

export interface GetOperatorArgs {
    operationCode: AuthorizedOperation;
    siteSelected?: string;
}

@ui.decorators.page<MobileGetOperator>({
    title: 'Operator',
    subtitle: 'Enter operator code',
    node: '@sage/wh-master-data/Operand',
    mode: 'default',
    isTransient: true,
    skipDirtyCheck: true,
    navigationPanel: undefined,
    businessActions() {
        return [this.acceptButton];
    },
    async onLoad() {
        if (!(await this._initialize())) {
            this.disablePage();
            // Close the page
            this.$.finish(undefined);
        }
    },
})
export class MobileGetOperator extends ui.Page<GraphApi> {
    /**
     * Internal properties
     */
    private _siteCodeSelected?: string;
    private _depositorCodeSelected?: string;

    private _parameters: GetOperatorArgs | undefined;
    /**
     * sections
     **/

    @ui.decorators.section<MobileGetOperator>({
        isTitleHidden: true,
        // title: 'Serial Numbers',
    })
    mainSection: ui.containers.Section;

    /**
     * blocks
     **/

    @ui.decorators.block<MobileGetOperator>({
        parent() {
            return this.mainSection;
        },
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.textField<MobileGetOperator>({
        parent() {
            return this.mainBlock;
        },
        title: 'Operator',
        //    placeholder: 'Scan or select...',
        isTransient: true,
        isFullWidth: true,
        isHelperTextHidden: true,
        async onChange() {
            const _operatorCode = this.operatorCode.value?.trim()?.toUpperCase();
            this.operatorCode.value = _operatorCode ?? null;
            await this._checkOperatorCode(_operatorCode);
        },
    })
    operatorCode: ui.fields.Text;

    @ui.decorators.pageAction<MobileGetOperator>({
        title: 'OK',
        buttonType: 'primary',
        shortcut: ['f2'],
        isDisabled: true,
        async onError(e) {},
        async onClick() {
            if (await this._checkOperatorCode(this.operatorCode.value)) {
                this.acceptButton.isDisabled = true;
                this.$.finish(this.operatorCode.value);
            }
        },
    })
    acceptButton: ui.PageAction;

    /**
     * @description: This function is used to initialize the page
     * @returns: Promise<boolean>
     */
    /** @internal */
    private async _initialize(): Promise<boolean> {
        if (!(await this._initSiteDepositor())) {
            return false;
        }

        // Requires a selected product in the query parameters.  This should not occur unless user manually
        // directs themselves to this page
        const _parameters = this._getQueryParameters();
        //      this._setTitle(_parameters);

        this._parameters = _parameters;

        // Requires product and stock id
        if (!_parameters?.operationCode) {
            this.$.showToast(
                ui.localize('@sage/wh-master-data/notification-error-missing-params', 'Missing required parameters'),
                { type: 'error', timeout: 5000 },
            );
            return false;
        }
        return true;
    }

    /**
     * Disable the page
     */
    /** @internal */
    private async disablePage() {
        this.mainSection.isDisabled = true;
        this.mainBlock.isDisabled = true;
    }

    /**
     * @description: This function is used to initialize the site and depositor
     * @returns: Promise<boolean>
     */
    /** @internal */
    private async _initSiteDepositor(): Promise<boolean> {
        const siteDepositor = await getCurrentSiteDepositor(
            this,
            ui.localize('@sage/wh-master-data/dialog-error-title', 'Error'),
            ui.localize(
                '@sage/wh-master-data/dialog-error-location-inquiry-set-site-depositor',
                'Define a default site and depositor on the user function profile.',
            ),
        );
        if (siteDepositor && siteDepositor?.site && siteDepositor?.depositor) {
            this._siteCodeSelected = siteDepositor?.site;
            this._depositorCodeSelected = siteDepositor?.depositor;
            return true;
        }
        return false;
    }

    /**
     * @description: This function is used to check the operator code and change the accept button state
     * @param operatorCode: string | undefined | null
     * @returns: Promise<boolean>
     */
    /** @internal */
    private async _checkOperatorCode(operatorCode: string | undefined | null): Promise<boolean> {
        const _operatorCode = operatorCode?.trim()?.toUpperCase();
        this.acceptButton.isDisabled = true;
        if (_operatorCode) {
            if (await this._getOperator(_operatorCode)) {
                this.acceptButton.isDisabled = false;
            } else {
                this.$.showToast(
                    ui.localize(
                        '@sage/wh-master-data/notification-error-operator-not-found',
                        'Operator not found: {{ operatorCode }}.',
                        { operatorCode: _operatorCode },
                    ),
                    { type: 'error', timeout: 5000 },
                );
                return false;
            }
        }
        return !!_operatorCode;
    }

    /**
     * @description: This function is used to get the operator code
     * @param _operatorCode: string | undefined
     * @returns: Promise<string | undefined>
     */
    /** @internal */
    private async _getOperator(_operatorCode: string | undefined): Promise<string | undefined> {
        if (_operatorCode) {
            try {
                const _result = await this.$.graph
                    .node('@sage/wh-master-data/Operand')
                    .read({ _id: true, code: true }, `${this._siteCodeSelected}|${_operatorCode?.toUpperCase()}`)
                    .execute();
                return _result?.code;
            } catch (error) {
                console.error(`Error reading operator:\n${error}`);
            }
        }
        return undefined;
    }

    /**
     * get query parameters
     * @returns expected parameters or undefined
     */
    /** @internal */
    private _getQueryParameters(): GetOperatorArgs | undefined {
        try {
            return JSON.parse(String(this.$.queryParameters['getOperatorArgs'])) as GetOperatorArgs;
        } catch (error) {
            ui.console.error(`Error retrieving query parameters:\n${JSON.stringify(error)}`);
            return undefined;
        }
    }
}
