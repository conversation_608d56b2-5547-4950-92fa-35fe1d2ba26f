import { UnitLength } from '@sage/wh-product-data-api';
import { DateValue } from '@sage/xtrem-date-time';
import * as ui from '@sage/xtrem-ui';
import { FifoManagementErrorMessages, ProductConfiguration } from '../interfaces/environnement';

export class FifoManagement {
    private _productConfiguration: ProductConfiguration | undefined;
    constructor(
        private _manufacturedDate: ui.fields.Date,
        private _detentionDate: ui.fields.Date,
        private _sellByDate: ui.fields.Date,
        private _useByDate: ui.fields.Date,
        private _shipByDate: ui.fields.Date,
        private _fifoDate: ui.fields.Date,
        private _errorMessages: FifoManagementErrorMessages,
    ) {}
    public Initialize(_productConfiguration: ProductConfiguration | undefined) {
        this._productConfiguration = _productConfiguration;
    }

    // public onValidationManufacturedDate(value: string): string | undefined {
    //     return undefined;
    // }

    public async onChangeManufacturedDate(pageInstance: ui.Page) {
        if (this._manufacturedDate.value) {
            this._setStandardDate(this._manufacturedDate.value);
            pageInstance.$.commitValueAndPropertyChanges();
        }
    }

    // public onValidationDetentionDate(value: string): string | undefined {
    //     return undefined;
    // }

    public async onChangeDetentionDate(pageInstance: ui.Page) {
        if (this._detentionDate.value) {
            this._setManufacturedDate(this._detentionDate.value, 'detentionDate');
            pageInstance.$.commitValueAndPropertyChanges();
        }
    }

    public onValidationSellByDate(value: string): string | undefined {
        if (
            value &&
            this._detentionDate.value &&
            this._sellByDate.value &&
            this._detentionDate.value >= this._sellByDate.value
        ) {
            return this._errorMessages.sellByDateError;
        }

        return undefined;
    }

    public async onChangeSellByDate(pageInstance: ui.Page) {
        if (this._sellByDate.value) {
            this._setManufacturedDate(this._sellByDate.value, 'sellByDate');
            pageInstance.$.commitValueAndPropertyChanges();
        }
    }

    public onValidationUseByDate(value: string): string | undefined {
        if (
            value &&
            this._detentionDate.value &&
            this._useByDate.value &&
            this._detentionDate.value >= this._useByDate.value
        ) {
            return this._errorMessages.useByDateError;
        }
        return undefined;
    }

    public async onChangeUseByDate(pageInstance: ui.Page) {
        if (this._useByDate.value) {
            this._setManufacturedDate(this._useByDate.value, 'useByDate');
        }
    }

    public onValidationShipByDate(value: string): string | undefined {
        if (
            value &&
            this._detentionDate.value &&
            this._shipByDate.value &&
            this._detentionDate.value >= this._shipByDate.value
        ) {
            return this._errorMessages.shipByDateError;
        }
        return undefined;
    }

    public async onChangeShipByDate(pageInstance: ui.Page) {
        if (this._shipByDate.value) {
            this._setManufacturedDate(this._shipByDate.value, 'shipByDate');
            pageInstance.$.commitValueAndPropertyChanges();
        }
    }

    public onValidationFifoDate(value: string): string | undefined {
        return undefined;
    }

    public async onChangeFifoDate(pageInstance: ui.Page) {
        if (this._fifoDate.value) {
            switch (this._productConfiguration?.fifoDateNature) {
                case 'input':
                    // nothing to do in this case
                    break;

                case 'manufacturing':
                    if (!this._manufacturedDate.value) {
                        this._setStandardDate(this._fifoDate.value);
                    }
                    break;

                case 'sellByDate':
                    if (this._sellByDate.value) {
                        this._setManufacturedDate(this._sellByDate.value, 'sellByDate');
                    }
                    break;

                case 'maximumConservationLimit':
                    if (this._useByDate.value) {
                        this._setManufacturedDate(this._useByDate.value, 'useByDate');
                    }
                    break;

                case 'sendByDateLimit':
                    if (this._shipByDate.value) {
                        this._setManufacturedDate(this._shipByDate.value, 'shipByDate');
                    }
                    break;

                default:
                    return;
            }
            pageInstance.$.commitValueAndPropertyChanges();
        }
    }

    /**
     * This function assign the FIFO date based on the current date and the product configuration.
     * @param currentDate
     * @param fifoDateNature
     * @returns
     */
    private _setFifoDate(currentDate: string, fifoDateNature?: string): boolean {
        if (!this._fifoDate.value && fifoDateNature && fifoDateNature === this._productConfiguration?.fifoDateNature) {
            this._fifoDate.value = currentDate;
            return true;
        }
        return false;
    }

    /**
     * This function calculates the manufacturing date and then readjusts the fifo date.
     * @param currentDate
     * @param fieldName
     * @returns
     */
    private _setManufacturedDate(currentDate: string, fieldName: string): boolean {
        const _currentDate = currentDate ? DateValue.parse(currentDate) : undefined;
        if (!this._manufacturedDate.value && _currentDate) {
            const _getManufacturedDate = this._dateCalculation(_currentDate, fieldName, this._productConfiguration);
            if (_getManufacturedDate) {
                this._manufacturedDate.value = _getManufacturedDate;
                this._setFifoDate(_getManufacturedDate, 'manufacturing');
                return this._setStandardDate(_getManufacturedDate);
            }
        }
        return false;
    }

    /**
     * Assign all date after change date
     * @param currentDate
     * @returns
     */
    private _setStandardDate(currentDate: string): boolean {
        const _currentDate = currentDate ? DateValue.parse(currentDate) : undefined;
        const _productConfiguration = this._productConfiguration;
        if (!_productConfiguration || !_currentDate) {
            return false;
        }
        // initial line empty, but not edited
        for (const _dateTarget of ['detentionDate', 'sellByDate', 'useByDate', 'shipByDate']) {
            let _fifoDateNature: string | undefined;
            let field: ui.fields.Date;

            switch (_dateTarget) {
                case 'detentionDate':
                    field = this._detentionDate;
                    // detention date not used for fifo date
                    break;

                case 'sellByDate':
                    field = this._sellByDate;
                    _fifoDateNature = 'sellByDate';
                    break;

                case 'useByDate':
                    field = this._useByDate;
                    _fifoDateNature = 'maximumConservationLimit';
                    break;

                case 'shipByDate':
                    field = this._shipByDate;
                    _fifoDateNature = 'sendByDateLimit';
                    break;
                default:
                    continue;
            }

            if (!field.value) {
                // extract date field parameters
                const _dateCalculated = this._dateCalculation(_currentDate, _dateTarget, _productConfiguration);
                if (_dateCalculated) {
                    // set date value
                    field.value = _dateCalculated;
                    this._setFifoDate(_dateCalculated, _fifoDateNature);
                }
            }
        }
        return true;
    }

    /**
     * This function calculates the date based on the current date and the product configuration.
     *  @param currentDate
     *  @param dateTarget
     *  @param productConfiguration
     *  @returns
     */
    private _dateCalculation(
        currentDate: DateValue,
        dateTarget: string,
        productConfiguration: ProductConfiguration | undefined,
    ): string | undefined {
        if (productConfiguration && currentDate) {
            let _dateLength = 0;
            let _dateUnit = <UnitLength>'no';
            switch (dateTarget) {
                case 'detentionDate':
                    _dateLength = productConfiguration.detentionLength;
                    _dateUnit = <UnitLength>(productConfiguration.detentionUnit ?? 'no');
                    break;
                case 'sellByDate':
                    _dateLength = productConfiguration.sellByDateLength;
                    _dateUnit = <UnitLength>(productConfiguration.sellByDateUnit ?? 'no');
                    break;
                case 'useByDate':
                    _dateLength = productConfiguration.useByDateLength;
                    _dateUnit = <UnitLength>(productConfiguration.useByDateUnit ?? 'no');
                    break;
                case 'shipByDate':
                    _dateLength = productConfiguration.sendByDateLength;
                    _dateUnit = <UnitLength>(productConfiguration.sendByDateUnit ?? 'no');
                    break;
                default:
                    return undefined;
            }

            // Compute date value
            switch (_dateUnit) {
                case 'days':
                    return currentDate.addDays(_dateLength).toString();

                case 'month':
                    return currentDate.addMonths(_dateLength).toString();
            }
        }

        return undefined;
    }
}
