import { Product } from '@sage/wh-product-data-api';
import { extractEdges } from '@sage/xtrem-client';
import * as ui from '@sage/xtrem-ui';
import { ProductConfiguration } from '../interfaces';

/**
 * Read the product container units list
 * @param pageInstance
 * @param siteCode
 * @param depositorCode
 * @param productCode
 * @param numberOfRecords
 * @returns
 */
export async function getProductConfiguration(
    pageInstance: any,
    siteCode: string | undefined | null,
    depositorCode: string | undefined | null,
    productCode: string | undefined | null,
): Promise<ProductConfiguration | undefined> {
    if (siteCode && depositorCode && productCode) {
        interface _ResultProductConfiguration {
            code: string;
            localizedDescription: string;
            isKeyInLotNumber: boolean;
            isStockUnitManagement: boolean;
            stockUnit: { code: string; numberOfDecimals: number };
            fifoDateNature: string;
            detentionLength: number;
            detentionUnit: string;
            useByDateLength: number;
            useByDateUnit: string;
            sendByDateLength: number;
            sendByDateUnit: string;
            sellByDateLength: number;
            sellByDateUnit: string;
        }
        try {
            const _result = extractEdges<_ResultProductConfiguration>(
                await pageInstance.$.graph
                    .node('@sage/wh-product-data/Product')
                    .query(
                        ui.queryUtils.edgesSelector<Product>(
                            {
                                code: true,
                                localizedDescription: true,
                                isKeyInLotNumber: true,
                                isStockUnitManagement: true,
                                fifoDateNature: true,
                                detentionLength: true,
                                detentionUnit: true,
                                useByDateLength: true,
                                useByDateUnit: true,
                                sendByDateLength: true,
                                sendByDateUnit: true,
                                sellByDateLength: true,
                                sellByDateUnit: true,
                                stockUnit: {
                                    code: true,
                                    numberOfDecimals: true,
                                },
                            },
                            {
                                filter: {
                                    site: {
                                        code: siteCode,
                                    },
                                    depositor: {
                                        code: depositorCode,
                                    },
                                    code: productCode,
                                    isActive: true,
                                },
                            },
                        ),
                    )
                    .execute(),
            )[0];

            if (_result) {
                return <ProductConfiguration>{
                    code: _result.code,
                    localizedDescription: _result.localizedDescription,
                    isStockUnitManagement: _result.isStockUnitManagement,
                    stockUnitCode: _result.stockUnit?.code,
                    numberOfDecimals: _result.stockUnit?.numberOfDecimals,
                    fifoDateNature: _result.fifoDateNature,
                    detentionLength: _result.detentionLength,
                    detentionUnit: _result.detentionUnit,
                    useByDateLength: _result.useByDateLength,
                    useByDateUnit: _result.useByDateUnit,
                    sendByDateLength: _result.sendByDateLength,
                    sendByDateUnit: _result.sendByDateUnit,
                    sellByDateLength: _result.sellByDateLength,
                    sellByDateUnit: _result.sellByDateUnit,
                };
            }
        } catch (error) {
            ui.console.error(`Error reading product configuration :\n${JSON.stringify(error)}`);
        }
    }
    return undefined;
}
