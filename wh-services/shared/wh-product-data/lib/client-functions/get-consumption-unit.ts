import { ProductContainer } from '@sage/wh-product-data-api';
import { extractEdges } from '@sage/xtrem-client';
import * as ui from '@sage/xtrem-ui';
import { ConsumptionUnit, ContainerUnit, ContainerUnits } from '../interfaces';

/**
 * Read the product container units list
 * @param pageInstance
 * @param siteCode
 * @param depositorCode
 * @param productCode
 * @param numberOfRecords
 * @returns
 */
export async function getProductContainersUnits(
    pageInstance: any,
    siteCode: string,
    depositorCode: string,
    productCode: string,
    numberOfRecords: number = 10,
): Promise<ContainerUnits> {
    if (siteCode && productCode && depositorCode) {
        interface ResultProductContainer {
            _id: string;
            containerLevel: string;
            homogeneousLevel: string;
            numberOfConsumptionUnit: string;
            container?: {
                _id: string;
                code: string;
                containerManagementType: string;
            };
            isKeyInSupportNumbers: boolean;
            inferiorContainer?: {
                _id: string;
                containerLevel: string;
                container: { _id: string; code: string; containerManagementType: string };
            };
            product: {
                _id: string;
                code: string;
                isStockUnitManagement: boolean;
                fifoDateNature: string;
                stockUnit: { _id: string; code: string; numberOfDecimals: string };
            };
        }
        try {
            return (
                extractEdges<ResultProductContainer>(
                    await pageInstance.$.graph
                        .node('@sage/wh-product-data/ProductContainer')
                        .query(
                            ui.queryUtils.edgesSelector<ProductContainer>(
                                {
                                    product: {
                                        _id: true,
                                        code: true,
                                        isStockUnitManagement: true,
                                        stockUnit: {
                                            _id: true,
                                            code: true,
                                            numberOfDecimals: true,
                                        },
                                    },
                                    container: {
                                        _id: true,
                                        code: true,
                                        containerManagementType: true,
                                    },
                                    containerLevel: true,
                                    homogeneousLevel: true,
                                    numberOfConsumptionUnit: true,
                                    inferiorContainer: {
                                        _id: true,
                                        containerLevel: true,
                                        container: { _id: true, code: true, containerManagementType: true },
                                    },
                                    isKeyInSupportNumbers: true,
                                    _id: true,
                                },
                                {
                                    filter: {
                                        site: {
                                            code: siteCode,
                                        },
                                        depositor: {
                                            code: depositorCode,
                                        },
                                        product: { code: productCode, isActive: true },
                                        isActive: true,
                                    },
                                    first: numberOfRecords > 10 ? numberOfRecords : 10,
                                    orderBy: { containerLevel: -1, container: { code: 1 } },
                                },
                            ),
                        )
                        .execute(),
                )?.map<ContainerUnit>(
                    _ =>
                        <ContainerUnit>{
                            _id: _._id,
                            code: _.container?.code,
                            containerLevel: _.containerLevel,
                            container: {
                                _id: _.container?._id,
                                code: _.container?.code,
                                containerManagementType: _.container?.containerManagementType,
                            },
                            homogeneousLevel: _.homogeneousLevel,
                            numberOfConsumptionUnit: Number(_.numberOfConsumptionUnit),
                            isKeyInSupportNumbers: _.isKeyInSupportNumbers,
                            ...(_.inferiorContainer?.container.code && {
                                inferiorContainer: {
                                    _id: _.inferiorContainer._id,
                                    containerLevel: _.inferiorContainer?.containerLevel,
                                    container: {
                                        _id: _.inferiorContainer.container._id,
                                        code: _.inferiorContainer?.container.code,
                                        containerManagementType:
                                            _.inferiorContainer?.container?.containerManagementType,
                                    },
                                },
                            }),
                            ...(_.containerLevel === 'level5' &&
                                _.product.isStockUnitManagement &&
                                _.product?.stockUnit?.code &&
                                _.container?.containerManagementType === 'unit' && {
                                    _id: _.product.stockUnit._id,
                                    stockUnit: _.product.stockUnit.code,
                                    numberOfDecimals: Number(_.product.stockUnit.numberOfDecimals),
                                }),
                        },
                ) ?? []
            );
        } catch (error) {
            ui.console.error(`Error reading product container:\n${JSON.stringify(error)}`);
            return [];
        }
    }
    return [];
}

/**
 *  Read the product consumption unit
 *  @param pageInstance
 *  @param siteCode
 *  @param depositorCode
 *  @param productCode
 *  @returns
 */
export async function getConsumptionUnit(
    pageInstance: any,
    siteCode: string,
    depositorCode: string,
    productCode: string,
): Promise<ConsumptionUnit | null> {
    const _containerUnits = await getProductContainersUnits(pageInstance, siteCode, depositorCode, productCode);
    if (_containerUnits.length > 0) {
        const _containerUnit = _containerUnits[0];
        return <ConsumptionUnit>{
            code: _containerUnit.code,
            numberOfConsumptionUnit: _containerUnit.numberOfConsumptionUnit,
        };
    }
    return null;
}

/**
 *  Read the product consumption unit Code
 *  @param pageInstance
 *  @param siteCode
 *  @param depositorCode
 *  @param productCode
 *  @returns
 */
export async function getConsumptionUnitCode(
    pageInstance: any,
    siteCode: string,
    depositorCode: string,
    productCode: string,
): Promise<string | null> {
    return (await getConsumptionUnit(pageInstance, siteCode, depositorCode, productCode))?.code ?? null;
}
