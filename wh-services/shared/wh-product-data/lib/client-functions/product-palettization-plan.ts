import { hightestContainerLevel } from '@sage/wh-master-data/lib/interfaces';
import { truncate } from '@sage/wh-master-data/lib/shared-functions/math';
import { GraphApi } from '@sage/wh-product-data-api';
import * as ui from '@sage/xtrem-ui';
import {
    ContainerProductUnit,
    ContainerUnit,
    ContainerUnitAndOptions,
    ContainerUnits,
    MovementPlan,
} from '../interfaces/environnement';
import { getProductContainersUnits } from './get-consumption-unit';

export class ProductPalettizationPlan {
    constructor(
        private _inputContainer: ui.fields.DropdownList,
        private _numberOfContainers: ui.fields.Numeric,
        private _homogeneousContainer: ui.fields.DropdownList,
        private _homogeneousQuantity: ui.fields.Numeric,
    ) {}
    private _siteCode?: string;
    private _depositorCode?: string;
    private _productCode?: string;

    // Product palettization plan
    private _productPalletisationPlanUnits: ContainerUnits = [];
    private _productConsumptionUnit?: ContainerUnit;

    /**
     * Reinitialize all values to undefined or empty array
     */
    public reinitialize(): void {
        this._siteCode = undefined;
        this._depositorCode = undefined;
        this._productCode = undefined;
        this._productPalletisationPlanUnits = [];
        this._productConsumptionUnit = undefined;
    }

    /**
     * Initialize container units : undefined values for reset values
     * @param pageInstance
     * @param siteCode
     * @param depositorCode
     * @param productCode
     * @param maxRecords
     * @returns
     */
    public async initialize(
        pageInstance: ui.Page<GraphApi>,
        siteCode: string | undefined | null,
        depositorCode: string | undefined | null,
        productCode: string | undefined | null,
        maxRecords: number = 100,
    ): Promise<boolean> {
        if (siteCode && depositorCode && productCode) {
            this._siteCode = siteCode;
            this._depositorCode = depositorCode;
            this._productCode = productCode;

            const _productContainerUnitList = await getProductContainersUnits(
                pageInstance,
                siteCode,
                depositorCode,
                productCode,
                maxRecords < 100 ? 100 : maxRecords,
            );

            this._productPalletisationPlanUnits = _productContainerUnitList;
            this._productConsumptionUnit = _productContainerUnitList.find(
                _ => _.containerLevel === hightestContainerLevel,
            );
        } else {
            this.reinitialize();
        }
        return this._productPalletisationPlanUnits.length > 0;
    }

    /**
     * Get site code
     */
    public get siteCode(): string {
        return this._siteCode ?? '';
    }

    /**
     * Get depositor code
     */
    public get depositorCode(): string {
        return this._depositorCode ?? '';
    }

    /**
     * Get product code
     */
    public get productCode(): string {
        return this._productCode ?? '';
    }

    /**
     * Get product palletisation plan units
     */
    public get productPalletisationPlanUnits(): ContainerUnits {
        return this._productPalletisationPlanUnits;
    }

    /**
     * Get product consumption unit code
     * @returns consumption unit
     */

    public get productConsumptionUnit(): ContainerUnit | undefined {
        return this._productConsumptionUnit;
    }

    /**
     * Get product consumption unit code
     */
    public get productConsumptionUnitCode(): string {
        return this._productConsumptionUnit?.code ?? '';
    }

    /**
     *  Get product consumption unit stock unit
     */
    public get productConsumptionUnitStockUnit(): string | undefined {
        return this._productConsumptionUnit?.stockUnit;
    }

    /**
     * Get product consumption unit precision
     */
    public get productConsumptionUnitPrecision(): number | undefined {
        return this._productConsumptionUnit?.numberOfDecimals;
    }

    /**
     * truncate consumption quantity
     * @param quantity
     * @returns
     */
    public truncateConsumptionQuantity(quantity: number): number {
        return truncate(quantity, this.productConsumptionUnitPrecision ?? 0);
    }

    /**
     * Get product container unit options
     * @returns array of sorted container units codes
     */
    public getProductContainerUnitOptions(): string[] {
        return this._productPalletisationPlanUnits
            .map(productContainerUnit => {
                return `${productContainerUnit.code}`;
            })
            .sort();
    }

    /**
     * Get container unit
     * @param containerCode
     * @returns container unit or undefined
     */
    public getProductContainerUnit(containerCode: string | undefined | null): ContainerUnit | undefined {
        return this._productPalletisationPlanUnits?.find(_ => _.code === containerCode);
    }

    /**
     * Get product container (without any reference and number of consumption unit as string)
     * @param containerCode
     * @returns product container unit or undefined
     * */
    public getProductContainer(
        containerCode: string | undefined | null,
    ): (ContainerProductUnit & { numberOfConsumptionUnit: string }) | undefined {
        const _containerUnit = this.getProductContainerUnit(containerCode);
        return <ContainerProductUnit>{
            ..._containerUnit,
            numberOfConsumptionUnit: String(_containerUnit?.numberOfConsumptionUnit),
        };
    }

    /**
     * Get number of consumption unit
     * @param containerCode
     * @returns number of consumption unit or 0
     */
    public getProductContainerNumberOfConsumptionUnit(containerCode: string | undefined | null): number {
        return Number(this.getProductContainerUnit(containerCode)?.numberOfConsumptionUnit);
    }

    /**
     * Get container unit depending on barcode and
     * @param defaultMode
     * @param inputContainerCode
     * @returns container unit and options
     */
    public getProductContainerUnitDependingBarCode(
        defaultMode: string,
        inputContainerCode: string | undefined | null,
    ): ContainerUnitAndOptions | undefined {
        let _containerUnit: ContainerUnit | undefined | null;
        let _containerOptions: string[] = [];

        if (defaultMode === 'eanCode') {
            _containerUnit = this._productPalletisationPlanUnits.find(_ => _.code === inputContainerCode);
            _containerOptions = _containerUnit?.code ? [_containerUnit?.code] : [];
        } else {
            _containerUnit = this.productConsumptionUnit;
            _containerOptions = this.getProductContainerUnitOptions();
        }
        return _containerUnit
            ? <ContainerUnitAndOptions>{ containerUnit: _containerUnit, containerOptions: _containerOptions }
            : undefined;
    }

    public getContainerUnit(quantityInConsumptionUnit: number): ContainerUnit | undefined {
        let _containerUnit: ContainerUnit | undefined = undefined;
        if (this._productPalletisationPlanUnits.length > 0) {
            let _containerUnits = this._productPalletisationPlanUnits;
            for (let i = 0; i < _containerUnits.length; i++) {
                if (quantityInConsumptionUnit >= _containerUnits[i].numberOfConsumptionUnit) {
                    _containerUnit = _containerUnits[i];
                    break;
                }
            }
        }
        return _containerUnit;
    }

    /**
     *  Indicates whether the product is managed in stock unit
     * @returns boolean
     */
    public getIsProductByStockUnit(): boolean {
        return !!this.productConsumptionUnitStockUnit;
    }

    /**
     * Get product container unit precision
     * Only an item managed in stock unit and whose container is level 5 (consumption unit)
     * sees the quantity of the latter with a precision which can be different from zero.
     * @param containerCode default is product consumption unit code
     * @returns precision or 0
     */
    public getProductContainerUnitPrecision(containerCode?: string | undefined | null): number {
        const _containerCode = containerCode ?? this.productConsumptionUnitCode;
        return this.getIsProductByStockUnit() && _containerCode === this.productConsumptionUnitCode
            ? (this.productConsumptionUnitPrecision ?? 0)
            : 0;
    }

    /**
     * Search the palettization plan for the most suitable container for the proposed quantity.
     * @param quantityInConsumptionUnit
     * @returns container unit or undefined
     */
    public getHighestProductContainerUnit(quantityInConsumptionUnit: number): ContainerUnit | undefined {
        try {
            for (const _level of ['level1', 'level2', 'level3', 'level4', 'level5']) {
                const _quantityInConsumptionUnit = quantityInConsumptionUnit;
                const _productPalletisationPlanUnits = this._productPalletisationPlanUnits.filter(
                    _ => _.containerLevel === _level && _.numberOfConsumptionUnit >= _quantityInConsumptionUnit,
                );

                if (!_productPalletisationPlanUnits.length) {
                    continue;
                }

                const _containerUnit = _productPalletisationPlanUnits.reduce<ContainerUnit | undefined>(
                    (prev, current) => {
                        return prev && prev.numberOfConsumptionUnit > current.numberOfConsumptionUnit ? prev : current;
                    },
                    undefined,
                );

                if (_containerUnit) {
                    return _containerUnit;
                }
            }

            /**
             * If no container is found, we return the first topmost container
             */
            return this._productPalletisationPlanUnits.reduce<ContainerUnit | undefined>((prev, current) => {
                return prev && prev.numberOfConsumptionUnit > current.numberOfConsumptionUnit ? prev : current;
            }, undefined);
        } catch (error) {
            console.error(`Error in getHighestProductContainerUnit:\n${error}`);
        }
        return undefined;
    }

    /**
     *  Get the container unit near the quantity in consumption unit
     *  It must be specified whether the parent should also be considered in the search.
     * @param quantityInConsumptionUnit
     * @param inputContainerCode
     * @param includesInputContainer
     * @returns
     */
    public getNearProductContainerUnit(
        quantityInConsumptionUnit: number,
        inputContainerCode: string,
        includesInputContainer?: boolean,
    ): ContainerUnit | undefined {
        let _containerUnit = this.getProductContainerUnit(inputContainerCode);
        do {
            if (
                !_containerUnit ||
                _containerUnit.containerLevel === hightestContainerLevel ||
                (_containerUnit.numberOfConsumptionUnit <= quantityInConsumptionUnit &&
                    (inputContainerCode !== _containerUnit.code || includesInputContainer))
            ) {
                return _containerUnit ?? this.productConsumptionUnit;
            }

            _containerUnit = this._productPalletisationPlanUnits.find(
                _ => _.code === _containerUnit?.inferiorContainer?.container?.code,
            );
        } while (true);
    }

    /**
     * Get the number of containers for a given quantity in consumption unit
     * @param containerUnitCode
     * @param quantityInConsumptionUnit
     * @returns number of containers
     */
    public getNumberOfContainers(
        quantityInConsumptionUnit: number,
        containerUnitCode: string | undefined | null,
    ): number {
        if (quantityInConsumptionUnit > 0) {
            const _containerUnit = containerUnitCode
                ? this.getProductContainerUnit(containerUnitCode)
                : this.getHighestProductContainerUnit(quantityInConsumptionUnit);
            if (_containerUnit) {
                if (_containerUnit.containerLevel === hightestContainerLevel) {
                    if (_containerUnit?.stockUnit) {
                        return Math.max(this.truncateConsumptionQuantity(quantityInConsumptionUnit), 1);
                    } else {
                        return Math.max(Math.trunc(quantityInConsumptionUnit), 1);
                    }
                } else {
                    return Math.max(Math.trunc(quantityInConsumptionUnit / _containerUnit.numberOfConsumptionUnit), 1);
                }
            }
        }
        return 0;
    }

    /**
     * Get the number of homogeneous containers for a given quantity in consumption unit
     * @param containerUnitCode
     * @param numberOfContainers
     * @param quantityInConsumptionUnit
     * @param homogeneousContainerCode
     * @returns number of homogeneous containers
     */
    public getNumberOfHomogeneousContainers(
        quantityInConsumptionUnit: number,
        containerUnitCode: string,
        numberOfContainers: number,
        homogeneousContainerCode: string | null | undefined,
    ): number {
        if (numberOfContainers > 0 && quantityInConsumptionUnit > 0) {
            const _containerUnit = this.getProductContainerUnit(containerUnitCode);
            const _homogeneousContainerUnit =
                containerUnitCode == homogeneousContainerCode
                    ? _containerUnit
                    : this.getProductContainerUnit(homogeneousContainerCode);
            if (_homogeneousContainerUnit && _containerUnit) {
                if (_containerUnit.containerLevel === hightestContainerLevel) {
                    if (containerUnitCode !== homogeneousContainerCode) {
                        return 0;
                    } else if (_containerUnit?.stockUnit) {
                        return this.truncateConsumptionQuantity(quantityInConsumptionUnit / numberOfContainers);
                    } else {
                        return Math.trunc(quantityInConsumptionUnit / numberOfContainers);
                    }
                } else if (_homogeneousContainerUnit.containerLevel === hightestContainerLevel) {
                    if (_homogeneousContainerUnit?.stockUnit) {
                        return this.truncateConsumptionQuantity(quantityInConsumptionUnit / numberOfContainers);
                    } else {
                        return Math.min(
                            Math.trunc(quantityInConsumptionUnit / numberOfContainers),
                            _containerUnit.numberOfConsumptionUnit,
                        );
                    }
                } else {
                    return Math.trunc(
                        quantityInConsumptionUnit /
                            (_homogeneousContainerUnit.numberOfConsumptionUnit * numberOfContainers),
                    );
                }
            }
        }
        return 0;
    }

    /**
     * Calculate the possible movement plan :
     *  - if no inputContainerCode is provided, we search for the most appropriate container and we calculate the complete plan.
     *  - if the number of containers is empty, we determine it.
     *  - if the homogeneous container is not provided, we take the lower container of the input container or itself if level 5.
     *
     * The calculation will be done from these elements.
     * @param numberOfConsumptionUnit
     * @param inputContainerCode
     * @param numberOfContainers
     * @param homogeneousContainerCode
     * @param allowChangeHomogeneousContainer optional to allow change homogeneous container when no valid plan is found
     * @returns movement plan or undefined
     */
    public calculatePossibleMovementPlan(
        numberOfConsumptionUnit: number,
        inputContainerCode: string | undefined | null,
        numberOfContainers: number,
        homogeneousContainerCode: string | undefined | null,
        allowChangeHomogeneousContainer = true,
    ): MovementPlan | undefined {
        let _inputContainerCode;
        let _numberOfContainers = numberOfContainers;
        let _homogeneousContainerCode = _inputContainerCode ? homogeneousContainerCode : undefined;
        const _containerUnit =
            this.getProductContainerUnit(inputContainerCode) ??
            this.getHighestProductContainerUnit(numberOfConsumptionUnit);
        if (_containerUnit) {
            _inputContainerCode = _containerUnit.code;

            _homogeneousContainerCode =
                _containerUnit.containerLevel === hightestContainerLevel
                    ? _inputContainerCode
                    : homogeneousContainerCode;

            if (!_homogeneousContainerCode) {
                const _homogeneousContainerUnit = this.getNearProductContainerUnit(
                    numberOfConsumptionUnit,
                    _inputContainerCode,
                );
                _homogeneousContainerCode = _homogeneousContainerUnit?.code ?? _inputContainerCode;
            }

            _numberOfContainers =
                inputContainerCode && numberOfContainers
                    ? numberOfContainers
                    : this.getNumberOfContainers(numberOfConsumptionUnit, _inputContainerCode);

            const _numberOfHomogeneousContainers =
                _containerUnit.containerLevel === hightestContainerLevel
                    ? 1
                    : this.getNumberOfHomogeneousContainers(
                          numberOfConsumptionUnit,
                          _inputContainerCode,
                          _numberOfContainers,
                          _homogeneousContainerCode ?? '',
                      );

            /**
             * If the provided homogeneous container cannot be used, but the search for an alternative
             * has been authorized, an attempt is made to find the closest possible one.
             */
            if (!_numberOfHomogeneousContainers && allowChangeHomogeneousContainer && homogeneousContainerCode) {
                return this.calculatePossibleMovementPlan(
                    numberOfConsumptionUnit,
                    _inputContainerCode,
                    _numberOfContainers,
                    undefined,
                    false,
                );
            }

            return <MovementPlan>{
                inputContainerCode: _inputContainerCode,
                homogeneousContainerCode: _homogeneousContainerCode,
                numberOfContainers: _numberOfContainers,
                numberOfHomogeneousContainers: _numberOfHomogeneousContainers,
            };
        }

        return undefined;
    }

    /**
     * Convert container to consumption quantity
     * @param containerCode
     * @param numberOfContainer
     * @param homogeneousContainerCode
     * @param homogeneousQuantity
     * @returns consumption quantity
     * */
    public convertContainerPlanToConsumptionQuantity(
        containerCode: string | undefined | null,
        numberOfContainer: number,
        homogeneousContainerCode: string | undefined | null,
        homogeneousQuantity: number,
    ): number {
        if (containerCode && homogeneousContainerCode) {
            const _productConsumptionUnit = this.productConsumptionUnit;
            if (_productConsumptionUnit) {
                /**
                 * When the container is of the consumption unit (level 5), we do not need
                 * to take into account the homogeneous container because it is identical.
                 */
                if (_productConsumptionUnit.code === containerCode) {
                    return truncate(numberOfContainer, _productConsumptionUnit.numberOfDecimals);
                } else {
                    const _homogeneousContainerUnit = this.getProductContainerUnit(homogeneousContainerCode);
                    if (_homogeneousContainerUnit) {
                        return truncate(
                            numberOfContainer * homogeneousQuantity * _homogeneousContainerUnit.numberOfConsumptionUnit,
                            _productConsumptionUnit.numberOfDecimals,
                        );
                    }
                }
            }
        }
        return 0;
    }

    /**
     *  Convert quantity in consumption unit
     * @param containerQuantity
     * @param containerCode
     * @returns
     */
    public convertContainerQuantityInConsumptionUnit(
        containerQuantity: number,
        containerCode: string | undefined,
    ): number {
        return this.truncateConsumptionQuantity(
            containerQuantity * this.getProductContainerNumberOfConsumptionUnit(containerCode),
        );
    }

    /**
     *  Convert quantity from consumption unit
     * @param quantityInConsumptionUnit
     * @param containerCode
     * @returns
     */
    public convertQuantityInConsumptionUnitToContainer(
        quantityInConsumptionUnit: number,
        containerCode: string | undefined,
    ): number {
        const _containerUnit = this.getProductContainerNumberOfConsumptionUnit(containerCode);
        return _containerUnit ? this.truncateConsumptionQuantity(quantityInConsumptionUnit / _containerUnit) : 0;
    }

    /**
     * Validate container unit
     * @param inputContainerCode
     * @param homogeneousContainerCode
     * @returns boolean
     */
    public async validateContainerUnit(
        inputContainerCode: string | undefined | null,
        homogeneousContainerCode: string | undefined | null,
    ): Promise<boolean> {
        if (homogeneousContainerCode && inputContainerCode) {
            const _inputContainerUnit = this.getProductContainerUnit(inputContainerCode);
            if (_inputContainerUnit) {
                // Check if the input container is the highest container level
                if (_inputContainerUnit.containerLevel === hightestContainerLevel) {
                    if (homogeneousContainerCode !== inputContainerCode) {
                        return false;
                    }
                } else {
                    //  Check if the homogeneous container is higher than the input container
                    const _homogeneousContainerUnit = this.getProductContainerUnit(homogeneousContainerCode);
                    if (_homogeneousContainerUnit) {
                        if (_inputContainerUnit?.containerLevel >= _homogeneousContainerUnit?.containerLevel) {
                            return false;
                        }
                        /**
                         * Check that the homogeneous container is indeed the child of the
                         * containing input container in the palettization plan
                         */
                        if (_homogeneousContainerUnit?.containerLevel !== hightestContainerLevel) {
                            let _inferiorContainerUnit: ContainerUnit | undefined = _inputContainerUnit;
                            do {
                                const _inferiorContainerUnitCode =
                                    _inferiorContainerUnit?.inferiorContainer?.container?.code;
                                // Check if the homogeneous container is the child of the inferior container
                                if (_inferiorContainerUnitCode === homogeneousContainerCode) {
                                    break;
                                }
                                _inferiorContainerUnit = this.getProductContainerUnit(_inferiorContainerUnitCode);

                                if (
                                    !_inferiorContainerUnit ||
                                    _homogeneousContainerUnit?.containerLevel < _inferiorContainerUnit?.containerLevel
                                ) {
                                    return false;
                                }
                            } while (true);
                        }
                    }
                }
            }
        }
        return true;
    }

    /**
     * On change input container event
     * @param pageInstance
     * @param numberOfConsumptionUnit
     */
    public async onChangeInputContainer(
        pageInstance: ui.Page<GraphApi>,
        numberOfConsumptionUnit: number,
    ): Promise<void> {
        // inputContainer, numberOfContainers, homogeneousContainer, homogeneousQuantity, packedQuantity
        if (this._inputContainer.value) {
            const _containerUnit = this.getProductContainerUnit(this._inputContainer.value);
            if (_containerUnit?.code) {
                const _numberOfConsumptionUnit = numberOfConsumptionUnit;
                this._numberOfContainers.value = this.getNumberOfContainers(
                    _numberOfConsumptionUnit,
                    _containerUnit.code,
                );
                if (_containerUnit.containerLevel === hightestContainerLevel) {
                    this._homogeneousContainer.value = this._inputContainer.value;
                    this._homogeneousContainer.isDisabled = true;
                    this._homogeneousQuantity.value = 1;
                    this._homogeneousQuantity.isDisabled = true;
                } else {
                    // near the lowest container level
                    const _inferiorContainerUnitCode =
                        this.getNearProductContainerUnit(_numberOfConsumptionUnit, _containerUnit.code)?.code ?? null;

                    this._homogeneousContainer.isDisabled = false;
                    this._homogeneousQuantity.isDisabled = false;
                    this._homogeneousContainer.value = _inferiorContainerUnitCode;
                    this._homogeneousQuantity.value = this.getNumberOfHomogeneousContainers(
                        _numberOfConsumptionUnit,
                        _containerUnit.code,
                        Number(this._numberOfContainers.value),
                        this._homogeneousContainer.value,
                    );
                }
                pageInstance.$.commitValueAndPropertyChanges();
            }
        }
    }

    /**
     * On change number of containers event
     * @param pageInstance
     * @param numberOfConsumptionUnit
     */
    public async onChangeNumberOfContainers(
        pageInstance: ui.Page<GraphApi>,
        numberOfConsumptionUnit: number,
    ): Promise<void> {
        await this.onChangeHomogeneousContainer(pageInstance, numberOfConsumptionUnit);
    }

    /**
     * On change homogeneous container event
     * @param pageInstance
     * @param numberOfConsumptionUnit
     */
    public async onChangeHomogeneousContainer(
        pageInstance: ui.Page<GraphApi>,
        numberOfConsumptionUnit: number,
    ): Promise<void> {
        if (
            this._inputContainer.value &&
            this._homogeneousContainer.value &&
            !this._homogeneousContainer.isDisabled &&
            !this._homogeneousQuantity.isDisabled
        ) {
            this._homogeneousQuantity.value = this.getNumberOfHomogeneousContainers(
                numberOfConsumptionUnit,
                this._inputContainer.value,
                Number(this._numberOfContainers.value),
                this._homogeneousContainer.value,
            );
            pageInstance.$.commitValueAndPropertyChanges();
        }
    }
}
