const dotenv = require('dotenv');
const fs = require('fs');
const path = require('path');
const filePath = path.resolve(__dirname, '../../platform/cli/xtrem-cli-atp/parameters-atp');
const allure = require('allure-commandline');

if (fs.existsSync(filePath)) {
    const parameters = dotenv.config({ override: true, path: filePath });
    if (parameters.error) throw parameters.error;
}
let portNumber;
if (process.env.portNumber === undefined || process.env.portNumber === '' || process.env.portNumber === '80') {
    portNumber = 5000;
} else {
    portNumber = process.env.portNumber;
}

let reportFolder = 'test-report';
reportFolder = path.resolve(process.cwd(), reportFolder);
const relativeReportFolder = path.relative(process.cwd(), reportFolder);
try {
    if (!fs.existsSync(`${reportFolder}/allure-results`)) {
        throw new Error('Allure-results folder does not exist.');
    }
    allure(['serve', `${relativeReportFolder}/allure-results`, '-p', `${portNumber}`]);
    const url = `http://localhost:${portNumber}`;
    const start = process.platform == 'darwin' ? 'open' : process.platform == 'win32' ? 'start' : 'xdg-open';
    require('child_process').exec(start + ' ' + url);
} catch (error) {
    console.log(error);
}
