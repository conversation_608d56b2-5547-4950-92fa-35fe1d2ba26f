#!/usr/bin/env bash

# This script build a multi-stage docker image

set -e

while (( "$#" )); do
  case "$1" in
    --file)
        DOCKER_FILE="$2"
        shift 2
      ;;
    --node-tag)
        NODE_TAG="$2"
        shift 2
      ;;
    --image-name)
        IMAGE_NAME="$2"
        shift 2
      ;;
    --tag)
        TAG_NAME="$2"
        shift 2
      ;;
    --env)
        DOCKER_ENV_SOURCE="$2"
        shift 2
      ;;
    *)
      shift
      ;;
  esac
done

DOCKER_FILE=${DOCKER_FILE:="Dockerfile"}
REPO_ROOT_PATH=$(git rev-parse --show-toplevel)
NODE_VERSION=$(cat "${REPO_ROOT_PATH}"/.nvmrc)
NODE_MAJOR_MINOR=$(echo "${NODE_VERSION}" | awk -F '.' '{ print $1"."$2 }')
NODE_TAG=${NODE_TAG:=$NODE_MAJOR_MINOR}

if [ -z "${DOCKER_ENV_SOURCE}" ] && [ ! -f .env ]; then
  echo "Missing .env file or set DOCKER_ENV_SOURCE variable to the source path"
  exit 1
fi
if [ -z "${IMAGE_NAME}" ]; then
  echo "Please set IMAGE_NAME variable to docker image name"
  exit 1
fi
if [ -z "${TAG_NAME}" ]; then
  echo "Please set TAG_NAME variable to docker image main tag"
  exit 1
fi

if [ -n "${DOCKER_ENV_SOURCE}" ]; then
  ln -s "${DOCKER_ENV_SOURCE}" .env
fi

# copy the json schema file to the build context
if [ -f "${REPO_ROOT_PATH}"/xtrem-config-json-schema.json ]; then
  cp "${REPO_ROOT_PATH}"/xtrem-config-json-schema.json .
fi

# copy or create the patches directory to have the patches in the build context
if [ -d "${REPO_ROOT_PATH}"/patches ]; then
  cp -r "${REPO_ROOT_PATH}"/patches .
else
  mkdir patches
fi

PUPPETEER_VERSION=$(grep -E 'puppeteer@[0-9.]+:' pnpm-lock.yaml | cut -d: -f1 | cut -d@ -f2)
if [ -z "${NODE_TAG}" ]; then
  echo "Missing node version in .nvmrc"
  exit 1
fi

echo "PUPPETEER_VERSION=$PUPPETEER_VERSION"
echo "NODE_TAG=$NODE_TAG"
echo "IMAGE_NAME=$IMAGE_NAME"
echo "TAG_NAME=$TAG_NAME"
echo ""

# compute the docker base image that will be used in the Dockerfile
dockerBaseImage=$(grep -i " as base" "${DOCKER_FILE}" 2> /dev/null | awk '{print $2}')
# if the base image contains the ${PUPPETEER_VERSION}, replace it with the actual version
if [[ "${dockerBaseImage}" == *"\${PUPPETEER_VERSION}"* ]]; then
  if [ -z "${PUPPETEER_VERSION}" ]; then
    echo "Missing puppeteer version in pnpm-lock.yaml"
    exit 1
  fi
  dockerBaseImage="${dockerBaseImage//\$\{PUPPETEER_VERSION\}/$PUPPETEER_VERSION}"
fi
dockerBaseImage="${dockerBaseImage//\$\{NODE_VERSION\}/$NODE_TAG}"
if [ -z "${dockerBaseImage}" ]; then
  echo "Missing base image in ${DOCKER_FILE}"
  exit 1
fi

# check if the base image is available
echo "Base image: ${dockerBaseImage}"

if ! docker pull "${dockerBaseImage}"; then
  echo "Base image ${dockerBaseImage} not found"
  exit 1
fi
# check if the node version is correct
ACTUAL_NODE=$(docker run --rm "${dockerBaseImage}" node -v)
ACTUAL_NODE_MAJOR_MINOR=$(echo "${ACTUAL_NODE}" | awk -F '.' '{ print $1"."$2 }' | cut -d'v' -f2)
if [ "${NODE_MAJOR_MINOR}" != "${ACTUAL_NODE_MAJOR_MINOR}" ]; then
  echo "Node version mismatch: expected ${NODE_MAJOR_MINOR}.x, got ${ACTUAL_NODE}"
  exit 1
else
  echo "Node version ${ACTUAL_NODE} is compliant with expected ${NODE_VERSION}"
fi

nexusImageName=ghcr.io/sage-erp-x3/${IMAGE_NAME}:${TAG_NAME}
echo "Build image with tag ${nexusImageName} for node ${NODE_TAG}"
DOCKER_BUILDKIT=1 docker build --pull --no-cache --progress=plain \
  --build-arg="PUPPETEER_VERSION=$PUPPETEER_VERSION" \
  --build-arg="NODE_VERSION=$NODE_TAG" \
  --secret id=xtrem-env,src=.env \
  -t "${nexusImageName}" \
  -f "${DOCKER_FILE}" .

# we no longer need this env
if [ -n "${DOCKER_ENV_SOURCE}" ]; then
  rm -f .env
fi

# we no longer need the json schema file
if [ -f ./xtrem-config-json-schema.json ]; then
  rm ./xtrem-config-json-schema.json
fi

if [ -d ./patches ]; then
  rm -rf ./patches
fi

# tag without nexus repository path is required for pushing on AWS and for creating local container
echo "Re-tag image ${nexusImageName} to ${IMAGE_NAME}:${TAG_NAME}"
docker tag "${nexusImageName}" "${IMAGE_NAME}":"${TAG_NAME}"

if [ -n "${TAG_MINOR}" ]; then
  echo "Re-tag image ${nexusImageName} to ghcr.io/sage-erp-x3/${IMAGE_NAME}:${TAG_MINOR}"
  docker tag "${nexusImageName}" ghcr.io/sage-erp-x3/"${IMAGE_NAME}":"${TAG_MINOR}"
fi
if [ -n "${TAG_MAJOR}" ]; then
  echo "Re-tag image ${nexusImageName} to ghcr.io/sage-erp-x3/${IMAGE_NAME}:${TAG_MAJOR}"
  docker tag "${nexusImageName}" ghcr.io/sage-erp-x3/"${IMAGE_NAME}":"${TAG_MAJOR}"
fi
