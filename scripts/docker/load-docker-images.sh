#!/usr/bin/env bash

set -e

REPO_ROOT_PATH=$(git rev-parse --show-toplevel)

source ${REPO_ROOT_PATH}/scripts/docker/docker-image-helper.sh

PG_VERSION=$(cat "$REPO_ROOT_PATH"/.pgdbrc)

downloadAndLoadImage "postgres:${PG_VERSION}-alpine"
downloadAndLoadImage "softwaremill/elasticmq-native:1.5.4"

echo "========================================"
echo "== Local images"
echo "========================================"
docker images --filter reference=node --filter reference=postgres --filter reference=softwaremill/elasticmq-native
