#!/usr/bin/env bash

set -e

REPO_ROOT_PATH=$(git rev-parse --show-toplevel)
TOOLS_TMP_DIR="$REPO_ROOT_PATH"/tmp
mkdir -p "$TOOLS_TMP_DIR"

downloadAndLoadImage() {
    IMAGE_NAME="$1"
    echo "##[group] Downloading and loading image $IMAGE_NAME"
    LOCAL_IMAGE=$(docker images --quiet "$IMAGE_NAME")
    if [ -z "$LOCAL_IMAGE" ]; then
        IMAGE_TAR_NAME=$(echo -n "$IMAGE_NAME" | tr '/:' '-').tar
        IMAGE_TAR="${TOOLS_TMP_DIR}/${IMAGE_TAR_NAME}"
        if [ -f "${IMAGE_TAR}" ] || aws s3 cp s3://xtrem-developers-utility/tools/"${IMAGE_TAR_NAME}" "${TOOLS_TMP_DIR}" ; then
            docker load --input "${IMAGE_TAR}"
        fi
    fi
    echo "##[endgroup]"
}

copyImageToS3() {
    IMAGE_NAME="$1"
    forceUpdate="$2"
    IMAGE_TAR_NAME=$(echo -n "$IMAGE_NAME" | tr '/:' '-').tar
    if [ "${forceUpdate}" == "--force" ] || ! aws s3 cp s3://xtrem-developers-utility/tools/"${IMAGE_TAR_NAME}" "${TOOLS_TMP_DIR}" ; then
        IMAGE_TAR="${TOOLS_TMP_DIR}/${IMAGE_TAR_NAME}"
        docker pull "$IMAGE_NAME"
        docker save --output "${IMAGE_TAR}" "${IMAGE_NAME}"
        aws s3 cp "${IMAGE_TAR}" s3://xtrem-developers-utility/tools/
        rm -f "${IMAGE_TAR}"
    fi
}
