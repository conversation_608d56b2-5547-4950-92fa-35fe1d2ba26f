const https = require('https');

const repository = process.argv[2];
const prHead = process.argv[3];
const prTitle = process.argv[4];
const prBody = process.argv[5];
const username = process.argv[6];
const password = process.argv[7];

const token = Buffer.from(`${username}:${password}`, 'utf8').toString('base64');

const post = (url, content) =>
    new Promise((resolve, reject) => {
        const postData = JSON.stringify(content);

        const options = {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': postData.length,
                'User-Agent': 'Azure DevOps Pipelines',
                Authorization: `Basic ${token}`,
            },
        };

        const request = https.request(url, options, (res) => {
            res.setEncoding('utf8');
            let body = '';

            res.on('data', (data) => {
                body += data;
            });

            res.on('end', () => {
                body = JSON.parse(body);
                resolve(body);
            });

            res.on('error', reject);
        });

        request.write(postData);
        request.end();
    });

const run = async () => {
    const response = await post(`https://api.github.com/repos/${repository}/pulls`, {
        base: 'master',
        head: prHead,
        title: prTitle,
        body: prBody,
    });
    console.log(response);
};

run().catch((e) => {
    console.log(e);
    process.exit(1);
});
