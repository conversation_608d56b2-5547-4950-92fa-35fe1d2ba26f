const fs = require('fs');
const path = require('path');
const { spawnSync } = require('node:child_process');

const result = spawnSync('npm', ['config', 'list', '--json']);
if (!result.stdout) {
    return;
}
const output = JSON.parse(result.stdout.toString('utf-8'));
if (!output) {
    return;
}

if (output['@sageai:registry']) {
    console.log('Sage AI registry already set');
    // If the sage AI registry is already set, we don't need to do anything
    return;
}

const userConfigPath = output.userconfig;
if (!userConfigPath) {
    console.log('No userconfig path found');
    return;
}

console.log('Adding Sage AI registry...');
fs.appendFileSync(userConfigPath, '\n@sageai:registry=https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/\n');
console.log('Adding Sage AI registry added');
