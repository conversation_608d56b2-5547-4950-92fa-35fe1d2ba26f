#!/usr/bin/env bash

#########################################################
##  FOR TEST ONLY: Generate CA and server certificate  ##
#########################################################

if [ -z "$XTREM_TEST_CA_PASS" ]; then
    echo "You must set the env variable XTREM_TEST_CA_PASS with the CA passphrase to use"
    exit 1
fi

if [ -z "$1" ]; then
    SSL_DIR=$(pwd)
else
    SSL_DIR=$1
fi

openssl version

# Generate CA Private Key for tests
openssl genrsa -aes256 -passout pass:${XTREM_TEST_CA_PASS} -out ${SSL_DIR}/ca.key 4096

# Create Certificate Authority Certificate for tests
openssl req -new -x509 -days 365 -key ${SSL_DIR}/ca.key -out ${SSL_DIR}/ca.crt -passin pass:${XTREM_TEST_CA_PASS} \
    -subj "/O=Sage/CN=Xtrem Test CA"

# Generate Server Private Key for tests
openssl genrsa -out ${SSL_DIR}/server.key 4096

# Create Certificate Signing Request (CSR) using Server Key for tests
openssl req -new -key ${SSL_DIR}/server.key -out ${SSL_DIR}/server.csr -subj "/O=Sage/CN=localhost"

# Create Server Certificate for tests
openssl x509 -req -in ${SSL_DIR}/server.csr -passin pass:${XTREM_TEST_CA_PASS} \
    -CA ${SSL_DIR}/ca.crt -CAkey ${SSL_DIR}/ca.key -out ${SSL_DIR}/server.crt -CAcreateserial -days 365 -sha256
#  -extfile server_cert_ext.cnf
