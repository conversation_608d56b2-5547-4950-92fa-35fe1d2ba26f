import axios from 'axios';
import * as fs from 'fs';
import inquirer from 'inquirer';
import * as os from 'os';
import * as path from 'path';
import { withUi } from './ui';


const npmrcPath = path.join(os.homedir(), '.npmrc');
const npmrcBackupPath = path.join(os.homedir(), '.npmrc.bak');

const main = withUi(async ({ logFail, logSuccess, ui }) => {

    try {
        console.log("First we need to create a new access token to the Azure DevOps repository. Please follow the steps below:\n");
        console.log(`1. Go to the following page: https://dev.azure.com/Sage-LiveServices/_usersSettings/tokens\n`);
        console.log(`2. Click the "+ New Token" button in the top right corner. A sidebar will appear.\n`);
        console.log(`3. Set a name in the "Name" input box for your new token, for example "npm-token".\n`);
        console.log(`4. Set the "Expiration" to a date well in the future. After expiry, you need to rerun this process.\n`);
        console.log(`4. In the "Scopes" section of the sidebar, scroll to the "Packaging" block which is at the end of the list.\n`);
        console.log(`5. Check the "Read" box within the "Packaging" block.\n`);
        console.log(`6. Click the "Create" action button in the footer of the sidebar.\n`);
        console.log(`7. The new token is now displayed in an input box. Copy the token and paste it below:\n`);
        const result = await inquirer.prompt<{ token: string, email: string }>([
            {
                type: 'input',
                name: 'token',
                async validate(input: string) {
                    const trimmedToken = input.trim();
                    if (trimmedToken.length < 84) {
                        return 'Token is invalid. Please try again.';
                    }
                    try {
                        const response = await axios.get(`https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/@sage/xtrem-shared`,
                            { headers: { Authorization: `Bearer ${trimmedToken}` } }
                        );
                        if (response.status !== 200) {
                            return 'Token is invalid. Please try again.';
                        }
                    } catch (err) {
                        console.log(err);
                        return 'Token is invalid. Please try again.';

                    }
                    return true
                },
                message: 'Your token:',
            },

        ]);

        const trimmedToken = result.token.trim();
        console.log(`Creating .npmrc backup...\n`)
        fs.cpSync(npmrcPath, npmrcBackupPath);
        console.log(`Your .npmrc file has been backed up to ${npmrcBackupPath}.\n`);
        console.log(`Creating new .npmrc file...\n`);
        const npmrcContent = `
@sage:registry=https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/
//pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/:_authToken=${trimmedToken}
always-auth=true`;
        fs.writeFileSync(npmrcPath, npmrcContent.trim());
        console.log(`Your .npmrc file has been updated with the new token. You are good to go.`);
        process.exit(1);

    } catch (err) {
        console.error(`Failed due to the following error: ${err.message}. Please try again.`);
        process.exit(1);
    }
});

main().catch(err => {
    console.error(err);
})
