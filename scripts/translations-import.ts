import fs from 'fs';
import * as glob from 'glob';
import path from 'path';

const filenamePattern = /([a-z0-9\-]+)_([a-z0-9\-]+)_([a-z]{2}-[A-Z]{2})\.json/;

const importFiles = () => {
    const translationFolder = path.resolve(process.cwd(), 'translations');
    const filesToImport = glob
        .sync(`${translationFolder}/*.json`)
        .filter((fileName) => fileName.match(filenamePattern));

    for (const filePath of filesToImport) {
        console.log(`Importing ${filePath}...`);
        const fileNameComponents = filePath.match(filenamePattern);
        const domain = fileNameComponents![1];
        const packageName = fileNameComponents![2];
        const locale = fileNameComponents![3];
        const translatedFileContent = require(filePath);
        const patternMatch = glob.sync(`${process.cwd()}${path.sep}${domain}/*/${packageName}/lib/i18n/${locale}.json`);
        if (patternMatch.length === 0) {
            console.log(`Couldn't find target file for ${filePath}.`);
            break;
        }
        const [targetFilePath] = patternMatch;
        const targetFileContent = require(targetFilePath);
        let counter = 0;
        for (const targetFileContentKey of Object.keys(translatedFileContent)) {
            if (
                !targetFileContentKey.startsWith('_') &&
                !targetFileContentKey.endsWith('.comment') &&
                !targetFileContentKey.startsWith('@sage/xtrem-ui/number-format') &&
                !targetFileContentKey.startsWith('@sage/xtrem-ui/date-format') &&
                !targetFileContentKey.startsWith('@sage/xtrem-date-time/date-format')
            ) {
                if (targetFileContent[targetFileContentKey] !== translatedFileContent[targetFileContentKey]) {
                    targetFileContent[targetFileContentKey] = translatedFileContent[targetFileContentKey];
                    counter++;
                }
            }
        }
        fs.writeFileSync(targetFilePath, JSON.stringify(targetFileContent, null, 4));
        console.log(`${counter} new entries imported to ${targetFilePath},`);
    }
};

importFiles();
