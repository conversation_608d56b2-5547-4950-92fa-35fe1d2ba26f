import * as chalk from 'chalk';
import fs from 'fs';
import { camelCase, kebabCase, orderBy } from 'lodash';
import path from 'path';
import {
    InterfaceDeclaration,
    Project,
    PropertyAssignment,
    PropertyDeclaration,
    SpreadAssignment,
    SyntaxKind,
    TypeFormatFlags,
} from 'ts-morph';
import util from 'util';
import { markdownTable } from './markdown-table';

const XTREM_UI_PATH = [__dirname, '..', 'platform', 'front-end', 'xtrem-ui'];

type FieldProperty = {
    name: string;
    type: string;
    isOptional: boolean;
    default?: any;
    description?: string;
};

const excludedFields = ['HelperPanel'];

function getDefaultsRecursive(prop: PropertyDeclaration | undefined) {
    if (!prop) {
        return {};
    }
    return prop
        .getInitializerIfKindOrThrow(SyntaxKind.ObjectLiteralExpression)
        .getProperties()
        .reduce<any>((acc, p) => {
            if (p instanceof SpreadAssignment) {
                const next = (p as SpreadAssignment)
                    .getExpression()
                    .getChildAtIndex(0)
                    .getType()
                    .getSymbol()
                    ?.getDeclarations()[0]
                    ?.getSourceFile()
                    .getClasses()[0]
                    .getProperty('defaultUiProperties');
                const props = getDefaultsRecursive(next);
                acc = { ...props, ...acc };
            } else {
                const initializer = (p as PropertyAssignment).getInitializer();
                acc[(p as PropertyAssignment).getName()] = initializer?.getText();
            }
            return acc;
        }, {});
}

class Box {
    value: any;
    constructor(value: any) {
        (this as any).value = value;
    }

    [util.inspect.custom]() {
        return typeof this.value === 'function'
            ? String(this.value).replace(/^function anonymous/, 'function')
            : this.value;
    }
}

function getDefaultFieldValue(field: FieldProperty): boolean | string | number | undefined | Function | Object {
    const fallback = (type: string): any => {
        if (type === 'string') {
            return '';
        }
        if (type === 'number') {
            return 0;
        }
        if (type === 'boolean') {
            return false;
        }
        if (type.match(/.*\[\]/)) {
            return [];
        }
        if (type.match(/\(this: CT\) => void/)) {
            return new Box(() => {});
        }
        if (type.match(/FieldWidth/)) {
            return 'medium'; // 'large' | 'medium' | 'small'
        }
        const callbackMatch = type.match(/\(this: CT, (.*)\) => (.*)/);
        if (callbackMatch) {
            const args = (callbackMatch[1].split(',') || []).map((s) => s.split(/\?*:/)[0]).flat();
            const returnValue = fallback(callbackMatch[2]);
            return new Box(new Function(...args, returnValue ? `return ${returnValue}` : ''));
        }
        if (type.match(/AccessConfiguration/)) {
            return {
                node: '<node>',
                bind: '<bind>',
            };
        }
        if (type.match(/ErrorHandlerFunction<CT>/)) {
            return new Box(
                new Function('error', 'screenId', 'elementId', `console.error({ error, screenId, elementId })`),
            );
        }
        const valueOrCallback = type.match(/ValueOrCallback(?:WithFieldValue)?<.*?,\s*(.*?),/);
        if (valueOrCallback) {
            return fallback(valueOrCallback[1]);
        }
        return undefined;
    };

    return field.default ? new Box(field.default) : fallback(field.type);
}

function generateDocs({
    fieldMap,
    typeMap,
}: {
    fieldMap: Map<string, Record<string, FieldProperty>>;
    typeMap: Map<string, 'field' | 'container' | undefined>;
}) {
    const docs = Array.from(fieldMap.keys())
        .sort()
        .map((field) => {
            const properties = fieldMap.get(field)!;
            const docs = orderBy(Object.values(properties), ['isOptional', 'name'])
                .map((k) => k.name)
                .reduce<Record<string, FieldProperty & { default: any }>>((acc, prop) => {
                    const { description, isOptional, default: def, type } = properties[prop];
                    acc[prop] = {
                        description,
                        isOptional,
                        default: def,
                        type,
                        name: prop,
                    };
                    return acc;
                }, {});
            return { fieldName: field, docs };
        });
    const fields = docs.map((field) => {
        const headers = ['name', 'optional', 'description', 'type', 'default'];
        return {
            ...field,
            type: typeMap.get(field.fieldName)!,
            docs: {
                props: field.docs,
                markdown: markdownTable([
                    headers,
                    ...Object.keys(field.docs).map((key) => {
                        const { description, isOptional, default: def, type, name } = field.docs[key];
                        return [
                            name,
                            String(isOptional),
                            description,
                            toCodeSnippet(type),
                            def && toCodeSnippet(String(def)),
                        ];
                    }),
                ]),
            },
        };
    });
    fs.writeFileSync(path.join(...XTREM_UI_PATH, 'docs.raw'), JSON.stringify(fields));
    fields.forEach((field) => {
        const name = kebabCase(field.fieldName);
        fs.writeFileSync(
            path.join(...[...XTREM_UI_PATH, 'lib', 'component', field.type, name, `docs-${name}.md`].filter(Boolean)),
            `## Documentation - ${field.fieldName}\n${field.docs.markdown}`,
        );
    });
}

function toCodeSnippet(str: string) {
    return `<pre lang="javascript">${str
        .replace(/ /g, '&nbsp;')
        .replace(/\t/g, '&nbsp;&nbsp;&nbsp;&nbsp;')
        .replace(/\n/g, '<br>')}</pre>`;
}

function processInterface({
    map,
    interfaceDeclaration,
    name,
}: {
    map: Map<string, Record<string, FieldProperty>>;
    interfaceDeclaration: InterfaceDeclaration;
    name: string;
}): void {
    const interfaceName = interfaceDeclaration.getName();
    const decoratorPropertiesMatcher = /(?<field>.*)(?<!Extension)DecoratorProperties/;
    const isDecoratorProperty = interfaceName.match(decoratorPropertiesMatcher);
    if (isDecoratorProperty) {
        const decoratorProps = interfaceDeclaration
            .getType()
            .getProperties()
            .reduce<Record<string, FieldProperty>>((acc, p) => {
                const propertyName = p.getName();
                const declaration = p.getDeclarations()[0];
                if (!declaration) {
                    console.log(chalk.red(`Could not find declaration of property "${name} => ${propertyName}".`));
                    process.exit(1);
                }
                const description = declaration
                    .getLeadingCommentRanges()
                    .map((comment) => {
                        return comment.getText();
                    })
                    .join(' ')
                    .replace(/^\W*(.*?)\W*$/gm, '$1')
                    .replace(/\s+/g, ' ')
                    .trim();
                if (description === '') {
                    console.log(chalk.blue(`Could not find description for property "${name} => ${propertyName}".`));
                }
                const isOptional = declaration.getType().isNullable();
                acc[propertyName] = {
                    name: propertyName,
                    type: declaration.getType().getNonNullableType().getText(undefined, TypeFormatFlags.InElementType),
                    isOptional,
                    description,
                };
                if (isOptional) {
                    const def = getDefaultFieldValue(acc[propertyName]);
                    acc[propertyName].default = util.inspect(def, false, null);
                }
                return acc;
            }, {});
        map.set(name, decoratorProps);
    }
}

function capitalizeFirstLetter(st: string) {
    return st.charAt(0).toUpperCase() + st.slice(1);
}

function main() {
    const project = new Project({
        tsConfigFilePath: path.join(...XTREM_UI_PATH, 'tsconfig.json'),
    });

    const fieldMap: Map<string, Record<string, FieldProperty>> = new Map();
    const typeMap: Map<string, 'field' | 'container' | undefined> = new Map();

    project.getSourceFiles().forEach((sourceFile) => {
        const fileName = sourceFile.getBaseNameWithoutExtension();
        const [name, parentFolder] = sourceFile.getDirectoryPath().split(path.sep).reverse();
        const isField = parentFolder === 'field';
        const isContainer = parentFolder === 'container';
        const isComponent = parentFolder === 'component';
        const nameKey = capitalizeFirstLetter(camelCase(name));
        if (
            (fileName.endsWith('-decorator') || fileName.endsWith('-types')) &&
            (isField || isContainer || isComponent)
        ) {
            typeMap.set(nameKey, isField || isContainer ? (parentFolder as 'field' | 'container') : undefined);
            const decoratorInterfaces = sourceFile.getInterfaces();
            decoratorInterfaces.forEach((interfaceDeclaration) =>
                processInterface({ interfaceDeclaration, map: fieldMap, name: nameKey }),
            );
        }
    });

    console.log(
        `Docs will be generated for the following: ${util.inspect(
            Array.from(fieldMap.keys()).sort(),
            false,
            null,
            true,
        )}.`,
    );

    project.getSourceFiles().forEach((sourceFile) => {
        const fileName = sourceFile.getBaseNameWithoutExtension();
        if (fileName.endsWith('-control-object')) {
            const clss = sourceFile.getClasses()[0];
            const defaultUiProps = clss.getProperty('defaultUiProperties');
            const className = clss.getName()!;
            const [name] = className.split('ControlObject');
            if (defaultUiProps === undefined) {
                console.log(chalk.cyan(`Could not find default properties for the following field: ${name}.`));
                // continue
                return;
            }
            if (clss.isAbstract() || excludedFields.includes(name)) {
                // continue
                return;
            }
            const defaultProps = getDefaultsRecursive(defaultUiProps);
            const field = fieldMap.get(name);
            if (!field) {
                console.log(chalk.red(`Could not find the following field: ${name}.`));
                // continue
                process.exit(2);
            }
            Object.entries(defaultProps).forEach(([key, value]) => {
                if (field[key]) {
                    field[key].default = value;
                } else {
                    console.log(chalk.yellow(`Could not find default value for property "${key}" of "${name}" field.`));
                }
            });
        }
    });
    generateDocs({ fieldMap, typeMap });
}

main();
