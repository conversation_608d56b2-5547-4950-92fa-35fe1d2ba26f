import axios from 'axios';
import { spawnSync } from 'child_process';
import { waiter } from './documentation/documentation-utils';

const JIRA_API = 'https://jira.sage.com/rest/api';
const username = process.argv[2];
const password = process.argv[3];

const packageNameRegexp =
    /(platform|services|shopfloor|tools|x3-services|wh-services)\/([a-z\-]*)\/([a-z\-]*)\/CHANGELOG.md/;
const versionRegexp = /## ([0-9]*\.[0-9]*\.[0-9]*)/;
const ticketNumberRegexp = /(X(3|3TRADOC|T)\-[0-9]*)/;
interface Dict<T> {
    [key: string]: T;
}

interface UpdateInfo {
    packageName?: string;
    version?: string;
}

const jiraTicketsToUpdate: Dict<UpdateInfo[]> = {};

const result = spawnSync('git', ['diff', '-U0', 'HEAD^', 'HEAD'], { encoding: 'utf-8', timeout: 2000 });

let currentPackage: UpdateInfo = {};
result.stdout.split('\n').forEach((line) => {
    const packageNameMatch = line.match(packageNameRegexp);
    if (packageNameMatch && packageNameMatch[1]) {
        currentPackage.packageName = packageNameMatch[1];
        currentPackage.version = undefined;
    }

    const packageVersionMatch = line.match(versionRegexp);
    if (packageVersionMatch && packageVersionMatch[1]) {
        currentPackage.version = packageVersionMatch[1];
    }

    const ticketNumberMatch = line.toUpperCase().match(ticketNumberRegexp);
    if (ticketNumberMatch && ticketNumberMatch[1]) {
        const ticketNumber = ticketNumberMatch[1];
        const updateInfos = jiraTicketsToUpdate[ticketNumber] || [];
        updateInfos.push({ ...currentPackage });
        jiraTicketsToUpdate[ticketNumber] = updateInfos;
    }
});

Object.keys(jiraTicketsToUpdate).forEach(async (ticketNumber) => {
    console.log(`Commenting on ${ticketNumber}...`);
    const packages = jiraTicketsToUpdate[ticketNumber];
    const commentContent = `The changes related to this ticket were published in the following packages:\n${packages
        .map((p) => ` - ${p.packageName}: ${p.version}`)
        .join('\n')}`;
    try {
        await axios({
            method: 'POST',
            auth: {
                username,
                password,
            },
            url: `${JIRA_API}/2/issue/${ticketNumber}/comment`,
            data: {
                body: commentContent,
            },
        });
    } catch (e) {
        console.log(`Failed to update ${ticketNumber}.`);
        console.log(e);
    }
    await waiter(2000);
});
