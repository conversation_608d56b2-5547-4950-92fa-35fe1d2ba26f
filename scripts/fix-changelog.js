const fs = require('fs');
const { platform } = require('os');

function cleanupChangelog(dir) {
    const file = `${dir}/CHANGELOG.md`;
    const lines = fs.readFileSync(file, 'utf8').split('\n');
    const linesMap = {};
    console.log(`${dir}: ${lines.length} lines read`);

    let line;
    for (let i = lines.length - 1; i >= 0; i--) {
        line = lines[i];
        if (line.startsWith('* ') && linesMap[line]) {
            lines[i] = '{----TO-REMOVE----}';
        } else {
            linesMap[line] = true;
        }
    }

    fs.writeFileSync(file, lines.filter((l) => l !== '{----TO-REMOVE----}').join('\n'), 'utf8');
}

['platform', 'services', 'x3-services', 'wh-services'].forEach(cleanupChangelog);
