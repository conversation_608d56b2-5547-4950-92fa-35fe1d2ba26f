import inquirer from 'inquirer';
import * as autocomplete from 'inquirer-checkbox-autocomplete-prompt';
import { withUi } from './ui';
import { exec, getPackages, searchForAutocomplete } from './utils';

interface Answers {
    dependency: string;
    isDev: boolean;
    scopes: string[];
}

const npmPackageExists = async (name: string): Promise<boolean> => {
    try {
        await exec(`pnpm show ${name}`, { inherit: false });
        return true;
    } catch (e) {
        return false;
    }
};

const main = withUi(async ({ logFail, logSuccess, ui }) => {
    inquirer.registerPrompt('checkbox-autocomplete', autocomplete);
    inquirer.ui.BottomBar = ui as any;
    const packages = await getPackages();
    const answers: Answers = await inquirer.prompt<Answers>([
        {
            type: 'input',
            name: 'dependency',
            message: 'Which dependency do you want to install?',
            transformer: (dependency) => dependency.toLowerCase().trim(),
            validate: (input?: string) => {
                if (!input || input.length === 0) {
                    return 'Please type the name of a dependency.';
                }
                const dependency = input.toLowerCase().trim();
                if (dependency.length === 0) {
                    return 'Please type the name of a dependency.';
                }
                if (dependency.split(' ').length > 1) {
                    return 'Please add one dependency at a time.';
                }
                return npmPackageExists(input)
                    .then((exists) => {
                        return exists ? true : `Package "${input}" does not exist.`;
                    })
                    .catch((err) => {
                        return err.message;
                    });
            },
        },
        {
            type: 'confirm',
            name: 'isDev',
            message: 'Is it as a dev dependency?',
            default: false,
        },
        {
            type: 'checkbox',
            name: 'scopes',
            source: searchForAutocomplete(packages),
            pageSize: 15,
            message: 'Which Sage packages do you want to add it to?',
            choices: packages,
            validate: (input: any) => {
                if (!input || input.length === 0) {
                    return 'Please select one or more packages.';
                }
                return true;
            },
        } as any,
    ]);
    const { dependency, isDev, scopes } = answers;
    const scopesString = `--scope=@sage/${scopes.join(' --scope=@sage/')}`;
    const devString = isDev ? ' --dev' : '';
    try {
        await exec(`pnpm run lerna add ${dependency} ${scopesString}${devString} --no-bootstrap`);
        await exec(`pnpm run bootstrap`);
        logSuccess('All done.');
    } catch (err) {
        logFail(`Installation failed due to the following error: ${err.message}`);
    }
});

// main();

console.log('This script is no longer supported with npm workspace!');
