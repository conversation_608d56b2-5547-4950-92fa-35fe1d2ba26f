#!/usr/bin/env bash

docker run --rm -d -e CLIENT_ID=FOq1MBQM4VBWutTk2PQES7RoVN6lYMLm \
-e CLIENT_SECRET=0cZboJUzFr_dz72QeRhSikQ2HFFOY9PaRTuue69Y4tBNO2rx8pR14Z_V9Hxad3rB \
-e ACCESS_TOKEN_LIFETIME=15  -e CUSTOMER_CLIENT_ID=xx -e CUSTOMER_CLIENT_SECRET=xx \
-e LOCAL_DEV=true \
-e XTREM_PORT=8240 \
-e MULTI_LOCAL_DEV=true \
-e LOCAL_TENANTS="777777777777777777777" \
-e BASE_URL=http://connect.localhost.dev-sagextrem.com:8080 \
-e ISSUER=connect.localhost.dev-sagextrem.com \
-e KNOWN_DOMAINS="*.dev-sagextrem.com" \
-e AWS_REGION=eu-west-1 \
-e LOCAL_SESSION=true \
-p "8080:8080" \
--name xtrem_auth \
ghcr.io/sage-erp-x3/xtrem-deployment-authentication:master
