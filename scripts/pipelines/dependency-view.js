'use stricts';

const fs = require('node:fs');
const fsp = require('node:path');
const pipeline = process.argv[2];

const root = {
    file: pipeline,
    dependsOn: [],
};

/**
 *
 * @param {string} file
 * @param {*} node
 */
function computedependencies(parentPath, node) {
    const file = fsp.join(parentPath, node.file);
    const pipelineLines = fs.readFileSync(file, 'utf-8').split('\n');
    const nodeDir = fsp.dirname(file);
    const map = {};
    for (const line of pipelineLines) {
        const m = /^\s*- template: ([^# ]+)/.exec(line);
        if (m?.[1]) {
            const templatePath = fsp.join(nodeDir, m?.[1]);
            if (!map[templatePath]) {
                // console.log(templatePath);
                map[templatePath] = true;
                node.dependsOn.push({ file: m?.[1], dependsOn: [] });
            }
        }
    }
    for (const dep of node.dependsOn) {
        computedependencies(nodeDir, dep);
    }
}

function displayGraph(node, level = 0) {
    console.log(`${' '.repeat(level * 2)} ${node.file}`);
    for (const dep of node.dependsOn) {
        displayGraph(dep, level + 1);
    }
}

computedependencies('.', root);

displayGraph(root);
// console.log(JSON.stringify(root, null, 2));
