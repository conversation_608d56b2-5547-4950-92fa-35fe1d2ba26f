#!/usr/bin/env bash

update_shellrc () {
  if [ -f ~/"$1" ]; then
    if ! grep -q "export NODE_EXTRA_CA_CERTS=" ~/"$1"; then
      echo -e "\nAdding NODE_EXTRA_CA_CERTS to ~/$1..."
      echo -e "\n# Cisco Umbrella Root CA certificate for node.js app\nexport NODE_EXTRA_CA_CERTS=\$HOME/ssl/ca-certificates/cisco-umbrella-root-ca.crt" >> ~/"$1"
    else
      echo "NODE_EXTRA_CA_CERTS already exists in ~/$1"
      # extract the path from the NODE_EXTRA_CA_CERTS and check if the file exists
    fi
    CA_CERTS_PATH=$(grep "export NODE_EXTRA_CA_CERTS=" ~/"$1" | awk -F '=' '{print $2}')
    CA_CERTS_PATH=$(eval echo "$CA_CERTS_PATH")
    if [ ! -f "$CA_CERTS_PATH" ]; then
      echo -e "\nThe file $CA_CERTS_PATH does not exist"
      echo "Please, be sure the path is correct and the file includes the Cisco Umbrella Root CA certificate"
    else
      echo -e "\nThe file $CA_CERTS_PATH exists"
      echo "The file type is '$(file -b "$CA_CERTS_PATH")'"
      echo "If HTTPS requests initiated by node does not work, be sure the file includes the Cisco Umbrella Root CA certificate"
    fi
  fi
}

while (( "$#" )); do
  case "$1" in
    --system-ca)
        systemCA="true"
        shift 1
      ;;
    *) # preserve positional arguments
      shift
      ;;
  esac
done

case "$OSTYPE" in
  darwin*) PLATFORM="darwin" ;;
  linux*) PLATFORM="linux" ;;
  msys*) PLATFORM="win32" ;;
  *)
    echo "Unsupported platform $OSTYPE"
    exit 1
    ;;
esac

# check platform and sudo user depending on the system-ca flag
if [ -n "$systemCA" ]; then
  if [ "$PLATFORM" != "linux" ]; then
    echo "The --system-ca flag is only supported on Linux"
    exit 1
  fi
  if [ -z "$SUDO_USER" ]; then
    echo "This script must be run with sudo to install the certificate to the system CA certificates"
    exit 1
  fi
else
  if [ -n "$SUDO_USER" ]; then
    echo "This script must be run without sudo update the shell configuration"
    exit 1
  fi
fi

if [ -z "$systemCA" ]; then
  # create ssl/ca-certificates dir in the home dir if not exists
  mkdir -p ~/ssl/ca-certificates

  # copy the umbrella cert to the ssl dir from s3 if it does not already exist
  if [ ! -f ~/ssl/ca-certificates/cisco-umbrella-root-ca.crt ]; then
    echo "Copying the Cisco Umbrella Root CA certificate to the ~/ssl/ca-certificates dir..."
    aws s3 cp s3://xtrem-developers-utility/certs/cisco-umbrella-root-ca.crt ~/ssl/ca-certificates/
  fi

  # add the NODE_EXTRA_CA_CERTS to the .bashrc and .zshrc
  if [ -f ~/.bashrc ]; then
    update_shellrc .bashrc
  fi
  if [ -f ~/.zshrc ]; then
    update_shellrc .zshrc
  fi
  echo -e "\nTo use the Cisco Umbrella Root CA certificate from node js application in this shell session, please, run the following command:"
  case "$SHELL" in
    */bash)
      echo "source ~/.bashrc"
      ;;
    */zsh)
      echo "source ~/.zshrc"
      ;;
  esac
  if [ "$PLATFORM" == "linux" ]; then
    echo -e "\nIn addition, we may want to install the Cisco Umbrella Root CA certificate to the system CA certificates."
    echo "To do it, run again this script with the --system-ca flag with sudo to install the certificate to the system CA certificates:"
    echo "sudo $0 --system-ca"
  fi
else
  # update the ca-certificates
  apt-get update
  apt-get install -y ca-certificates
  cp ~/ssl/ca-certificates/cisco-umbrella-root-ca.crt /usr/local/share/ca-certificates/
  update-ca-certificates
fi
