#!/usr/bin/env bash

rmOption="--rm"
dockerOptions=()
dataDir=""

set -e
while (( "$#" )); do
  case "$1" in
    --install-client)
        clientInstallRequested="true"
        shift 1
      ;;
    --skip-client-check)
        skipClientCheck="true"
        shift 1
      ;;
    --restart)
        dockerOptions+=('--restart' "$2")
        rmOption=""
        shift 2
      ;;
    --data)
        dataDir="$2"
        dockerOptions+=('-v' "$dataDir:/var/lib/postgresql/data")
        shift 2
      ;;
    *) # preserve positional arguments
      shift
      ;;
  esac
done

if [ -n "${rmOption}" ]; then
  dockerOptions+=("${rmOption}")
fi

if [ -n "${dataDir}" ]; then
  mkdir -p "${dataDir}"
  echo "Using data directory: ${dataDir}"
else
  echo "Using default data directory"
fi

tmpDir=$(dirname "$(mktemp -u)")

pgClientVersion=$(psql --version | awk '{print $3}')

echo "PG_VERSION: ${PG_VERSION}"
if [ -z "$PG_VERSION" ]; then
  REPO_ROOT_PATH=$(git rev-parse --show-toplevel)
  PG_VERSION="$(cat "$REPO_ROOT_PATH"/.pgdbrc)"
  if [[ ${TF_BUILD,,} == "true"  || -n $BUILD_BUILDID ]]; then
    echo "##vso[task.setvariable variable=PG_VERSION]${PG_VERSION}"
    echo "PG_VERSION updated: ${PG_VERSION}"
  fi
fi

installPostgresClient () {
  case "$OSTYPE" in
    darwin*) PLATFORM="darwin" ;;
    linux*) PLATFORM="linux" ;;
    msys*) PLATFORM="win32" ;;
    *)
      echo "Unsupported platform $OSTYPE"
      exit 1
      ;;
  esac

  "${PLATFORM}InstallPostgresClient"
}

linuxInstallPostgresClient () {
  echo "##[group] Uninstalling PostgreSQL-14 ..."
  dpkg -l | grep postgres || true
  apt-get --purge remove postgresql-client-14 postgresql-client-common postgresql-14 postgresql
  dpkg -l | grep postgres || true
  echo "##[endgroup]"

  if [[ "$EUID" != 0 ]]; then
    echo "Install of PostgreSQL client tools requires to be root. Please, run this script with:"
    echo "sudo $0 --install-client"
    exit 1
  fi
  echo "##[group] Installing PostgreSQL client tools..."
  if ! cat /etc/apt/sources.list.d/pgdg.list; then
    echo "apt repo is required to get the latest $(lsb_release -cs) version"
    echo "deb http://apt.postgresql.org/pub/repos/apt $(lsb_release -cs)-pgdg main" > /etc/apt/sources.list.d/pgdg.list
    # use curl because wget exit with non zero exit code and do not redirect output to terminal because of CI
    curl -s https://www.postgresql.org/media/keys/ACCC4CF8.asc -o "${tmpDir}/ACCC4CF8.asc"
    apt-key add "${tmpDir}/ACCC4CF8.asc"
    rm "${tmpDir}/ACCC4CF8.asc"
  fi
  apt update
  if ! apt install postgresql-client-16; then
    echo "failed to install postgresql-client-16, try to run:"
    echo "sudo apt upgrade"
    echo "sudo $0 --install-client"
    exit 1
  fi
  dpkg -l | grep postgres
  echo "##[endgroup]"
}

darwinInstallPostgresClient () {
  brew install libpq
}

win32InstallPostgresClient () {
  echo "Install of PostgreSQL client tools is not yet supported on ${PLATFORM} platform"
  exit 1
}

checkPostgresClient () {
  if [ "${skipClientCheck}" == "true" ]; then
    return
  fi
  echo "##[group] Check installation of postgres client"

  echo "Current version of PostgreSQL client tools is ${pgClientVersion}"
  echo "Expecting PostgreSQL client tools version ${PG_VERSION} or greater"

  if [ "$(echo "${pgClientVersion} < ${PG_VERSION}" | bc)" -eq 1 ]; then
    if [ "${clientInstallRequested}" == "true" ]; then
      installPostgresClient
    else
      echo "Please, install the postgresql-client tools version ${PG_VERSION} or greater."
      echo "on Ubuntu systems you can do it by running this script with:"
      echo "sudo $0 --install-client"
      exit 1
    fi
  fi

  echo "Using $(psql --version)"
  echo "Using $(pg_restore --version)"
  echo "Using $(pg_dump --version)"
  echo "##[endgroup]"
}

checkPostgresClient

if nc -zvw3 127.0.0.1 5432; then
    echo "A postgres server is already running."
    exit 0
fi

if [ -z "$POSTGRES_IMAGE" ]; then
  POSTGRES_IMAGE="postgres:${PG_VERSION}-alpine"
fi

if [ -z "$POSTGRES_USER" ]; then
  POSTGRES_USER=postgres
fi

if [ -z "$POSTGRES_PASSWORD" ]; then
  POSTGRES_PASSWORD=postgres
fi

IMAGE_NAME=$(docker images "ghcr.io/sage-erp-x3/$POSTGRES_IMAGE" --format "{{.Repository}}:{{.Tag}}")
if [ -z "$IMAGE_NAME" ]; then
  IMAGE_NAME="$POSTGRES_IMAGE"
fi

echo "##[group] Starting docker container using $IMAGE_NAME image and data directory ${dataDir:-"<internal>"}"

docker run "${dockerOptions[@]}" --shm-size=1g --name xtrem_postgres -e POSTGRES_USER="$POSTGRES_USER" -e POSTGRES_PASSWORD="$POSTGRES_PASSWORD" -d \
  -p 5432:5432 "$IMAGE_NAME" -c max_locks_per_transaction=256

nc -zvw3 127.0.0.1 5432
echo "##[endgroup]"
