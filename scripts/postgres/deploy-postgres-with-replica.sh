#!/bin/bash


set -e
while (( "$#" )); do
  case "$1" in
    --install-client)
        clientInstallRequested="true"
        shift 1
      ;;
    --skip-client-check)
        skipClientCheck="true"
        shift 1
      ;;
    *) # preserve positional arguments
      shift
      ;;
  esac
done

tmpDir=$(dirname "$(mktemp -u)")

pgClientVersion=$(psql --version | awk '{print $3}')

# Implemented using https://github.com/marcel-dempers/docker-development-youtube-series/blob/master/storage/databases/postgresql/3-replication/README.md

PG_VERSION=$(cat "$(git rev-parse --show-toplevel)"/.pgdbrc)

export PG_VERSION

installPostgresClient () {
  case "$OSTYPE" in
    darwin*) PLATFORM="darwin" ;;
    linux*) PLATFORM="linux" ;;
    msys*) PLATFORM="win32" ;;
    *)
      echo "Unsupported platform $OSTYPE"
      exit 1
      ;;
  esac

  "${PLATFORM}InstallPostgresClient"
}

linuxInstallPostgresClient () {
  echo "##[group] Uninstalling PostgreSQL-14 ..."
  dpkg -l | grep postgres || true
  apt-get --purge remove postgresql-client-14 postgresql-client-common postgresql-14 postgresql
  dpkg -l | grep postgres || true
  echo "##[endgroup]"

  if [[ "$EUID" != 0 ]]; then
    echo "Install of PostgreSQL client tools requires to be root. Please, run this script with:"
    echo "sudo $0 --install-client"
    exit 1
  fi
  echo "##[group] Installing PostgreSQL client tools..."
  if ! cat /etc/apt/sources.list.d/pgdg.list; then
    echo "apt repo is required to get the latest $(lsb_release -cs) version"
    echo "deb http://apt.postgresql.org/pub/repos/apt $(lsb_release -cs)-pgdg main" > /etc/apt/sources.list.d/pgdg.list
    # use curl because wget exit with non zero exit code and do not redirect output to terminal because of CI
    curl -s https://www.postgresql.org/media/keys/ACCC4CF8.asc -o "${tmpDir}/ACCC4CF8.asc"
    apt-key add "${tmpDir}/ACCC4CF8.asc"
    rm "${tmpDir}/ACCC4CF8.asc"
  fi
  apt update
  if ! apt install postgresql-client-16; then
    echo "failed to install postgresql-client-16, try to run:"
    echo "sudo apt upgrade"
    echo "sudo $0 --install-client"
    exit 1
  fi
  dpkg -l | grep postgres
  echo "##[endgroup]"
}

darwinInstallPostgresClient () {
  brew install libpq
}

win32InstallPostgresClient () {
  echo "Install of PostgreSQL client tools is not yet supported on ${PLATFORM} platform"
  exit 1
}

checkPostgresClient () {
  if [ "${skipClientCheck}" == "true" ]; then
    return
  fi
  echo "##[group] Check installation of postgres client"

  echo "Current version of PostgreSQL client tools is ${pgClientVersion}"
  echo "Expecting PostgreSQL client tools version ${PG_VERSION} or greater"

  if [ "$(echo "${pgClientVersion} < ${PG_VERSION}" | bc)" -eq 1 ]; then
    if [ "${clientInstallRequested}" == "true" ]; then
      installPostgresClient
    else
      echo "Please, install the postgresql-client tools version ${PG_VERSION} or greater."
      echo "on Ubuntu systems you can do it by running this script with:"
      echo "sudo $0 --install-client"
      exit 1
    fi
  fi

  echo "Using $(psql --version)"
  echo "Using $(pg_restore --version)"
  echo "Using $(pg_dump --version)"
  echo "##[endgroup]"
}

checkPostgresClient

SCRIPT_DIR=$(dirname "$(realpath "$0")")

# Set default values if not provided
export POSTGRES_IMAGE=${POSTGRES_IMAGE:-"postgres:${PG_VERSION}-alpine"}
export POSTGRES_USER=${POSTGRES_USER:-"postgres"}
export POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-"postgres"}
export POSTGRES_DB=${POSTGRES_DB:-"postgres"}
export NETWORK_NAME="postgres_network"
export MAX_LOCKS_PER_TRANSACTION=${MAX_LOCKS_PER_TRANSACTION:-"256"}
export MAX_WAL_SENDERS=${MAX_WAL_SENDERS:-"10"}

            # -c hot_standby_feedback=on
# Docker compose constructed using https://medium.com/@eremeykin/how-to-setup-single-primary-postgresql-replication-with-docker-compose-98c48f233bbf
docker compose  -f "$SCRIPT_DIR"/docker-compose-postgres-with-replica.yml up -d xtrem_postgres_primary xtrem_postgres_replica
