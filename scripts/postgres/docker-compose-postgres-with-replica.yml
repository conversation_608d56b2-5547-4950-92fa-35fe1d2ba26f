version: '3.8'
x-postgres-common: &postgres-common
    image: ${POSTGRES_IMAGE}
    user: ${POSTGRES_USER}
    restart: always
    shm_size: 1g
    healthcheck:
        test: 'pg_isready -U ${POSTGRES_USER} --dbname=${POSTGRES_DB}'
        interval: 10s
        timeout: 5s
        retries: 5

services:
    xtrem_postgres_primary:
        <<: *postgres-common
        ports:
            - 5432:5432
        environment:
            POSTGRES_USER: ${POSTGRES_USER}
            POSTGRES_DB: ${POSTGRES_DB}
            POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
            POSTGRES_HOST_AUTH_METHOD: "scram-sha-256\nhost replication all 0.0.0.0/0 md5"
            POSTGRES_INITDB_ARGS: '--auth-host=scram-sha-256'
        command: |
            postgres
            -c wal_level=replica
            -c hot_standby=on
            -c max_wal_senders=${MAX_WAL_SENDERS}
            -c max_replication_slots=10
            -c max_locks_per_transaction=${MAX_LOCKS_PER_TRANSACTION}
        volumes:
            - ./00_init.sql:/docker-entrypoint-initdb.d/00_init.sql

    xtrem_postgres_replica:
        <<: *postgres-common
        ports:
            - 5435:5432
        environment:
            PGUSER: replicator
            PGPASSWORD: replicator_password
        command: |
            bash -c "
            until pg_basebackup --pgdata=/var/lib/postgresql/data -R --slot=replication_slot --host=xtrem_postgres_primary --port=5432
            do
            echo 'Waiting for primary to connect...'
            sleep 1s
            done
            echo 'Backup done, starting replica...'
            chmod 0700 /var/lib/postgresql/data
            postgres -c max_locks_per_transaction=${MAX_LOCKS_PER_TRANSACTION} -c max_wal_senders=${MAX_WAL_SENDERS}
            "
        depends_on:
            - xtrem_postgres_primary
