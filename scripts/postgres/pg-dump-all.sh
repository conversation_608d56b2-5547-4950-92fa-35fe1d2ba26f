#!/usr/bin/env bash

PG_DATA=
PG_USER=postgres
PG_PASS=postgres
PG_TAG=10.14-alpine

usage()
{
  echo "Usage:  $0 -d <path> [ -u <postgres user> ] [ -p <postgres password> ] [ -t <image tag> ]"
  echo ""
  echo "Dump all databases of a PostgreSQL server running in a container."
  echo "This can be used to migrate your data from one version to a new one (ex: 10.14 to 13.6)"
  echo ""
  echo "Options:"
  echo " -d   local path of the data volume of PostgresSQL docker container"
  echo " -u   user to use for PostgresSQL connection. Default to '"$PG_USER"'"
  echo " -p   password to use for PostgresSQL connection. Default to '"$PG_PASS"'"
  echo " -t   docker image tag to use for PostgresSQL. Default to '"$PG_TAG"'"
  echo " -h   display this help"
  echo ""
  echo "Examples:"
  echo " $0 -d $HOME/pgdata/10"
  echo " $0 -d $HOME/pgdata/10 -p secret"
}

options=':hd:u:p:t:'
while getopts $options option
do
    case "$option" in
        d  ) PG_DATA="$OPTARG";;
        u  ) PG_USER="$OPTARG";;
        p  ) PG_PASS="$OPTARG";;
        t  ) PG_TAG="$OPTARG";;
        h  ) usage; exit;;
        \? ) echo "Unknown option: -$OPTARG" >&2; exit 1;;
        :  ) echo "Missing option argument for -$OPTARG" >&2; exit 1;;
        *  ) echo "Unimplemented option: -$option" >&2; exit 1;;
    esac
done

if [ -z $PG_DATA ]; then 
    echo "-d option is mandatory"
    usage
    exit 1
fi

echo "starting PostgreSQL server using tag $PG_TAG ..."

docker run --rm --name xtrem_postgres -e POSTGRES_USER=$PG_USER -e POSTGRES_PASSWORD=$PG_PASS -d -p 5432:5432 \
    -v $PG_DATA:/var/lib/postgresql/data -v /tmp:/tmp \
    postgres:$PG_TAG -c max_locks_per_transaction=256

nc -zvw3 127.0.0.1 5432
echo "server is listening, waiting 5 seconds for the server to be ready..."

sleep 5

echo "starting backup all databases"

docker exec -it xtrem_postgres sh -c 'pg_dumpall --host=localhost --username=$POSTGRES_USER --no-password > /tmp/pgdb.out'

echo "backup done in /tmp/pgdb.out"
ls -l /tmp/pgdb.out

echo "stopping the server"
docker stop xtrem_postgres
