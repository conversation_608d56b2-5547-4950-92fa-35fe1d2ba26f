import * as fs from 'fs';
import * as json5 from 'json5';

const args = process.argv.slice(2);
const mainArgs = args.filter((n) => !/^\-\-\w/.test(n));
const opts = args.filter((n) => /^\-\-\w/.test(n));

const [csvFile] = mainArgs;
const keepColors = opts.includes('--colors');

function transformLine(line: string, lineIdx: number) {
    if (!line || line.length == 0) return undefined;
    try {
        const match = matchLine(line);
        if (match == null) {
            return `UNPARSED ${line}`;
        }
        const transformed = match.replace(/\\t/g, '\t').replace(/\\""/g, '"').replace(/\\\\/g, '\\');
        if (keepColors) {
            return transformed.replace(/\\u001B/g, '\x1b');
        }
        return transformed.replace(/(?:\\u001B)?\[\d+m/g, '');
    } catch (err) {
        throw new Error(`Could not parse [${lineIdx}] : ${line} ${err.message}`);
    }
}

function padLeft(str: string, pad: number): string {
    return str.padStart(pad, '0').substring(0, pad);
}

function padRight(str: string, pad: number): string {
    return str.padEnd(pad).substring(0, pad);
}

function matchLine(line: string): string | undefined {
    const matchNewFormat = line.match(/""log"":({.*}).*}"$/);
    if (matchNewFormat) {
        const transformed = matchNewFormat[1].replace(/\"\"/g, '"');
        const parsed = json5.parse(transformed);
        const result = [
            parsed.tenantId ? parsed.tenantId.slice(-5) : '-----',
            '--', // place holder for the pid
            padLeft(parsed.eventId, 6),
            parsed.datetime,
            padRight(parsed.logLevel, 7),
            padRight(parsed.domain, 30),
            parsed.message,
        ].join(' | ');
        if (!keepColors) {
            return result;
        }
        switch (parsed.logLevel) {
            case 'ERROR':
                return `\x1b[31m${result}\x1b[39m`;
            case 'WARN':
                return `\x1b[33m${result}\x1b[39m`;
            case 'VERBOSE':
            case 'DEBUG':
                return `\x1b[94m${result}\x1b[39m`;
            default:
                return result;
        }
    }
    const matchingRegex = [/""log"":(?:""(.*)""|null),""stream""/, /.*?"",""(.*)""";;;;;;/, /"(.*)"/];
    for (const re of matchingRegex) {
        const match = (line.match(re) ?? [])[1];
        if (match) return match;
    }
}

if (!csvFile) {
    console.log(`Usage: transform-logs {csv filename} [--colors]`);
    console.log(`\t {csv filename}  the CSV file to tranform`);
    console.log(`\t --colors        keep colors`);
    process.exit(1);
}

const lines = fs.readFileSync(csvFile).toString().split('\n');
lines.shift(); // The first line of the CSV contains the columns
lines.reverse().forEach((line, idx) => {
    const transformed = transformLine(line, idx);
    if (transformed != null) console.log(transformed);
});
