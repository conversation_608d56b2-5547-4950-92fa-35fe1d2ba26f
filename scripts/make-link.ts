import * as fs from 'fs';
import { difference } from 'lodash';
import * as path from 'path';
import * as rimraf from 'rimraf';
import { withUi } from './ui';
import { exec, getPackages, getSymbolicLinks } from './utils';

const isPackageLinkable = (p: string) => !/^xtrem-(show-case|upgrade-test)/.test(p);

const getTsConfigPackages = (): string[] => {
    try {
        const rootTsConfigPath = path.join(__dirname, '../@sage/tsconfig.json');
        const tsconfig = fs.readFileSync(rootTsConfigPath, 'utf8');
        const parsedTsconfig = JSON.parse(tsconfig);

        return parsedTsconfig.references.map((r: any) => r.path.replace('etna-', 'xtrem-'));
    } catch (err) {
        return [];
    }
};

const main = withUi(async ({ logBottom, logFail, logSuccess }) => {
    const response = await exec(`npm -g root`, {});
    if (!response) {
        logFail('Could not find root path of npm.');
        process.exit(1);
    }

    const expectedPackages = getTsConfigPackages().filter(isPackageLinkable);
    if (expectedPackages.length === 0) {
        logFail(
            `Could not find any package, please check all references inside '${path.join(
                __dirname,
                '../@sage/tsconfig.json',
            )}'.`,
        );
        process.exit(2);
    }
    const actualPackages = getPackages().filter(isPackageLinkable);
    const missingPackages = difference(expectedPackages, actualPackages);
    if (missingPackages.length > 0) {
        logFail(
            `You are missing the following packages: ${JSON.stringify(missingPackages)}.
            
Please make sure you have the latest version of Xtrem.
If these packages have actually been deleted then '${path.join(
                __dirname,
                '../@sage/tsconfig.json',
            )}' needs to be updated.`,
        );
        process.exit(3);
    }
    const extraPackages = difference(actualPackages, expectedPackages);
    if (extraPackages.length > 0) {
        logFail(
            `The following packages are untracked and should be removed: ${JSON.stringify(
                extraPackages,
            )}. To fix this simply follow these steps:
            
1. Take a backup of '${path.join(__dirname, '../../xtrem-config.yml')}'
2. Pull latest ('git pull origin master')
3. Run 'git clean -d -x -f' (THIS WILL DELETE ANY UNTRACKED FILE/FOLDER, run 'git clean -d -x -f -n' to see what will be deleted)
4. Restore your 'xtrem-config.yml'
5. Run 'pnpm i'
6. Re-run 'pnpm run link'.


If these packages are new then '${path.join(__dirname, '../@sage/tsconfig.json')}' needs to be updated.`,
        );
        process.exit(4);
    }
    const globalModulesDir = response.join('');
    const globalSageDir = path.join(globalModulesDir, '@sage');
    if (!fs.existsSync(globalSageDir)) {
        fs.mkdirSync(globalSageDir);
    }

    // delete obsolete packages
    const otherPlatformPkgs = difference(getSymbolicLinks(globalSageDir, 'xtrem-'), expectedPackages);

    if (otherPlatformPkgs.length === 0) {
        logFail(`You need to link xtrem-platform before linking xtrem-x3-platform`);
        process.exit(3);
    }
    // obsoletePkgs.forEach((p: string) => {
    //     const toBeDeleted = path.join(globalSageDir, p);
    //     rimraf.sync(toBeDeleted);
    //     logSuccess(`Deleted '${toBeDeleted}' as it is obsolete.`);
    // });

    expectedPackages.forEach((p: string) => {
        let packDir = path.resolve(path.join(__dirname, '../@sage'), p.replace('xtrem-', 'etna-'));
        if (!fs.existsSync(packDir)) {
            packDir = path.resolve(path.join(__dirname, '../@sage'), p);
        }
        const globalDstDir = path.join(globalModulesDir, `@sage/${p}`);
        if (fs.existsSync(globalDstDir)) {
            rimraf.sync(globalDstDir);
        }
        // Note : on windows, we need to make sure that the packDir will look like C:\xxxx (uppercased driveletter)
        // and not c:\xxxx (lowercased driveletter) : some node function (process.cwd, ....) will always return a
        // upperCase drive letter.
        packDir = /^[a-z]:/.test(packDir) ? packDir[0].toUpperCase() + packDir.substring(1) : packDir;
        fs.symlinkSync(packDir, globalDstDir, 'junction');
        logSuccess(`Successfully linked '${p}'.`);
    });

    logBottom('');

    // Don't build any more here - we assume the build was already done.
    if (!1) {
        logBottom('Building packages...\n');
        try {
            await exec(`pnpm run lerna run build --concurrency=1 --stream`);
        } catch (err) {
            logBottom('');
            logFail(`Could not build linked packages due to the following error: ${err.message}`);
            process.exit(5);
        }
        logBottom('');
    }
    logSuccess('All done.');
});

main();
