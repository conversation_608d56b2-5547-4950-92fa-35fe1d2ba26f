import inquirer from 'inquirer';
import * as autocomplete from 'inquirer-checkbox-autocomplete-prompt';
import { withUi } from './ui';
import { exec, getPackages, searchForAutocomplete } from './utils';

interface Answers {
    packages: string[];
}

const main = withUi(async ({ logFail, ui }) => {
    inquirer.registerPrompt('checkbox-autocomplete', autocomplete);
    inquirer.ui.BottomBar = ui as any;
    const allPackages = await getPackages();
    const answers: Answers = await inquirer.prompt([
        {
            type: 'checkbox-autocomplete',
            name: 'packages',
            source: searchForAutocomplete(allPackages),
            pageSize: 15,
            message: 'Which Sage packages do you want to release?',
            choices: allPackages,
            validate: (input: any) => {
                if (!input || input.length === 0) {
                    return 'Please select one or more packages.';
                }
                return true;
            },
        } as any,
    ]);
    const packages = answers.packages;
    const packagesString = packages.map(p => `@sage/${p}`).join(',');
    try {
        await exec(`pnpm run lerna version --conventional-commits --conventional-prerelease=${packagesString}`);
    } catch (err) {
        logFail(`Release failed due to the following error: ${err.message}`);
    }
});

main();
