import { spawn, SpawnOptions } from 'child_process';
import * as fs from 'fs';
import * as fuzzy from 'fuzzy';
import * as path from 'path';

export interface Dict<T> {
    [key: string]: T;
}

/*
Minimal description of package.json files: 
*/
export interface Package {
    path: string;
    name: string;
    version?: string;
    newVersion?: string;
    content?: string;
    tag?: string;
    repository: {
        type: string;
        url: string;
    };
}

/** 
Safely read a package.json file 
@param jsonPath package.json file's path
@returns an instance of Package 
*/
export const safeReadPackage = (jsonPath: string): Package | undefined => {
    try {
        const content = fs.readFileSync(jsonPath, 'utf-8');
        const json = JSON.parse(content);

        return {
            ...{
                path: jsonPath,
                content: content,
                repository: {
                    type: 'git',
                    url: 'git://github.com/Sage-ERP-X3/xtrem.git',
                },
            },
            ...json,
        } as Package;
    } catch (err) {
        console.log(err.message);
        console.log(`'${jsonPath}' is not a valid JSON file.`);
    }
};

interface Options {
    inherit: boolean;
    streamData: boolean;
    cwd: string;
}

export const asyncForEach = async (
    array: any[],
    callback: (element: any, index?: number, array?: any[]) => Promise<void>,
) => {
    for (let index = 0; index < array.length; index++) {
        await callback(array[index], index, array);
    }
};

export const exec = async (
    command: string | string[],
    execOptions: Partial<Options> = { inherit: true, streamData: false, cwd: process.cwd() },
) => {
    let spawnOptions: SpawnOptions;
    if (execOptions.inherit) {
        spawnOptions = { stdio: 'inherit', cwd: execOptions.cwd, shell: true };
    } else {
        spawnOptions = { stdio: ['ignore', 'pipe', 'ignore'], cwd: execOptions.cwd, shell: true };
    }
    const streamData = !execOptions.inherit && execOptions.streamData;
    const [first, ...rest] = typeof command === 'string' ? command.split(' ') : command;
    return new Promise<string[] | null>((resolve, reject) => {
        const ls = spawn(first, rest, spawnOptions);
        let result = '';
        let error = '';
        if (!execOptions.inherit) {
            ls.stdout &&
                ls.stdout.on('data', (data) => {
                    result = `${result}${data}`;
                    streamData && console.log(`${data}`);
                });

            ls.stderr &&
                ls.stderr.on('data', (data) => {
                    result = `${result}${data}`;
                    error = `${error}${data}`;
                    streamData && console.log(`${data}`);
                });
        }

        ls.on('close', (code) => {
            if (code !== 0) {
                return reject({ code, message: error !== '' ? error : `process exited with error code ${code}!` });
            }
            if (!execOptions.inherit) {
                return resolve(result.split('\n').filter((line) => line !== ''));
            } else {
                return resolve(null);
            }
        });
    });
};

export const searchForAutocomplete =
    (list: string[]) =>
    (answers: any, input: string = '') => {
        return new Promise((resolve) => {
            const fuzzyResult = fuzzy.filter(input, list);
            resolve(fuzzyResult.map((el) => el.original));
        });
    };

export const getDirectoriesFromPath = (p: string) =>
    fs.readdirSync(p).filter((f) => fs.statSync(path.join(p, f)).isDirectory());

export const getFoldersUnderAtSage = () => getDirectoriesFromPath(path.join(__dirname, '../@sage'));

export const getPackages = (): string[] => {
    try {
        const rootTsConfigPath = path.join(__dirname, '../@sage/tsconfig.json');
        const tsconfig = fs.readFileSync(rootTsConfigPath, 'utf8');
        const parsedTsconfig = JSON.parse(tsconfig);
        return parsedTsconfig.references.map((r: any) => r.path.replace('etna-', 'xtrem-'));
    } catch (err) {
        return [];
    }
};

export const getSymbolicLinks = (schemaName: string, prefix = '') =>
    fs
        .readdirSync(schemaName)
        .filter((f) => fs.lstatSync(path.join(schemaName, f)).isSymbolicLink() && f.startsWith(prefix));
