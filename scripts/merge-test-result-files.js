'use strict';

const fs = require('node:fs');
const path = require('node:path');
const targetDir = process.argv[2];
let finalContent = [];

console.log(`Target directory: ${targetDir}`);

const jsonFiles = fs
    .readdirSync(targetDir)
    .filter((f) => fs.lstatSync(path.join(targetDir, f)).isFile() && path.extname(f) === '.json');

console.log(`Number of files: ${jsonFiles.length}`);

jsonFiles.forEach((f) => {
    const filePath = path.join(targetDir, f);
    console.log(`${f} => ${filePath}`);
    const fileContent = JSON.parse(fs.readFileSync(filePath, 'utf8'));
    finalContent.push(fileContent);
});

const targetPath = path.join(targetDir, 'merged.json');
const data = JSON.stringify(finalContent, null, 4);
console.log(`Final merged content: ${data}`);
fs.writeFileSync(targetPath, data, 'utf-8');
