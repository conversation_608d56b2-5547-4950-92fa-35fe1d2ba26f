#!/usr/bin/env bash

# Set the color variables
LIGHT_RED='\033[1;31m'
LIGHT_CYAN='\033[1;36m'
RESET='\033[0m'

SED_OPT=(-i)
if [[ "$OSTYPE" == "darwin"* ]]; then
    SED_OPT=(-i '')
fi

XTREM_ROOT_DIR=${XTREM_ROOT_DIR:="$(git rev-parse --show-toplevel)"}
XTREM_MAIN_APP_DIR=${XTREM_MAIN_APP_DIR:="$XTREM_ROOT_DIR"/services/main/xtrem-services-main}
XTREM_CONFIG_FILE="${XTREM_ROOT_DIR}"/xtrem-config.yml

startGroup () {
    if [[ -n $BUILD_BUILDID ]]; then
        echo "##[group] $1"
    else
        echo -e "${LIGHT_CYAN}***** [START] $1${RESET}"
    fi
}

endGroup() {
    if [[ -n $BUILD_BUILDID ]]; then
        echo "##[endgroup]"
    else
        echo -e "${LIGHT_CYAN}***** [--END] $1${RESET}"
    fi
}

updateConfig() {
    sed "${SED_OPT[@]}" "s/^user:/_disabled_by_import_csv_user:/g" "${XTREM_CONFIG_FILE}"
    sed "${SED_OPT[@]}" "s/^auth:/_disabled_by_import_csv_auth:/g" "${XTREM_CONFIG_FILE}"
}

restoreConfig() {
    sed "${SED_OPT[@]}" "s/^_disabled_by_import_csv_user:/user:/g" "${XTREM_CONFIG_FILE}"
    sed "${SED_OPT[@]}" "s/^_disabled_by_import_csv_auth:/auth:/g" "${XTREM_CONFIG_FILE}"
}

EXIT_CODE=0

cd "$XTREM_MAIN_APP_DIR"

if [[ -z $XTREM_SCHEMA_NAME ]]; then
    export XTREM_SCHEMA_NAME=xtrem_test_import_csv
fi

# Exit immediately if a command exits with a non-zero status
set -e

IMPORT_DIR=${XTREM_ROOT_DIR}/platform/performance-tests/tools/import/test/data/minimal
POLLING_MILLIS=300
TIMEOUT_SECONDS=60

while (( "$#" )); do
  case "$1" in
    --reset)
        RESET="true"
        shift
      ;;
    --import-dir)
        IMPORT_DIR=$2
        shift 2
      ;;
    --polling-millis)
        POLLING_MILLIS=$2
        shift 2
      ;;
    --timeout-seconds)
        TIMEOUT_SECONDS=$2
        shift 2
      ;;
    *)
      shift
      ;;
  esac
done

updateConfig

if [[ "$RESET" == "true" || "$XTREM_SCHEMA_NAME" == "xtrem_test_import_csv" ]]; then
    startGroup "reset schema $XTREM_SCHEMA_NAME"
    if ! pnpm run xtrem schema --create --reset-schema --skip-tables; then
        restoreConfig
        exit 1
    fi
    endGroup "reset schema $XTREM_SCHEMA_NAME"
fi

startGroup "load setup data"
if ! pnpm run xtrem layers --load setup; then
    restoreConfig
    exit 1
fi
endGroup "load setup data"

startGroup "setup ElasticMQ SQS"
cd "$XTREM_MAIN_APP_DIR"/../..
if ! pnpm run -sw sqs:reset; then
    restoreConfig
    exit 1
fi
cd -
endGroup "setup ElasticMQ SQS"

# Start as background job with unsecure dev login to allow uploading in dev mode
startGroup "start service in background and wait until ready"
UNSECURE_DEV_LOGIN=1 ./node_modules/.bin/xtrem start &

# Wait the server to be ready
curl --retry-connrefused --connect-timeout 5 --max-time 10 --retry 12 --retry-delay 0 \
    --retry-max-time 180 'http://localhost:8240/ready' || EXIT_CODE=$?
endGroup "start service in background and wait until ready"

if [[ $EXIT_CODE -ne 0 ]]; then
    restoreConfig
    exit 1
fi

startGroup "import csv test files"
"${XTREM_ROOT_DIR}"/platform/performance-tests/tools/import.sh \
    --email <EMAIL> \
    --import-dir "${IMPORT_DIR}" \
    --polling-millis "${POLLING_MILLIS}" \
    --timeout-seconds "${TIMEOUT_SECONDS}" \
    || EXIT_CODE=$?
endGroup "import csv test files"

startGroup "stop the xtrem service and clean up"
# Stop the xtrem service
kill -9 "$(jobs -p)" || _=$?

restoreConfig

endGroup "stop the xtrem service and clean up"

PID=$(pgrep -f "xtrem start" || true)
if [[ -n $PID ]]; then
    echo -e "\n${LIGHT_RED}The service is still running!${RESET}"
    echo -e "${LIGHT_RED}Please, run the command: kill -9 $PID${RESET}"
fi

exit $EXIT_CODE
