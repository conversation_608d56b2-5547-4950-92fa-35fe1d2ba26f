import * as fs from 'fs';
import * as path from 'path';

const args = process.argv.slice(2);
const [reportsPath] = args;
if (!reportsPath) console.log('usage: ./scan-reports ./reports');

const results = fs
    .readdirSync(reportsPath)
    .sort()
    .filter((entry) => path.extname(entry) === '.json')
    .map((entry) => {
        const report = JSON.parse(fs.readFileSync(path.join(reportsPath, entry), 'utf-8'));
        const counters = report.aggregate.counters;
        const metricsPath = Object.keys(counters).find((key) => key.startsWith('plugins.metrics-by-endpoint'));
        if (metricsPath) {
            const parts = metricsPath.split('.');
            const key = `plugins.metrics-by-endpoint.response_time.${parts[2]}`;
            const endPointMetrics = report.aggregate.summaries[key];
            return {
                'vusers.created': counters['vusers.created'],
                'vusers.completed': counters['vusers.completed'],
                'vusers.failed': counters['vusers.failed'],
                'http.codes.200': counters['http.codes.200'],
                min: Math.floor(endPointMetrics.min),
                median: Math.floor(endPointMetrics.median),
                p95: Math.floor(endPointMetrics.p95),
                max: Math.floor(endPointMetrics.max),
            };
        } else {
            console.log('no data found for report', path.join(reportsPath, entry));
        }
    });

const sep = ';';

const outputLines = [];

results
    .filter((result) => result !== null && result !== undefined)
    .forEach((result, idx) => {
        if (!idx) outputLines.push(Object.keys(result).join(sep));
        outputLines.push(Object.values(result).join(sep));
    });

fs.writeFileSync(path.join(reportsPath, 'results.csv'), outputLines.join('\n'));
