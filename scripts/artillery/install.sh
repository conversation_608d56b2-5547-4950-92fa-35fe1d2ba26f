#!/usr/bin/env bash
export PNPM_HOME=~/.local/share/pnpm
export PATH="$PNPM_HOME:$PATH"

echo "##vso[task.setvariable variable=PNPM_HOME]$PNPM_HOME"
echo "##vso[task.setvariable variable=PATH]$PATH"

if ! pnpm i -g artillery artillery-plugin-ensure@1.15.0 \
    artillery-plugin-expect \
    artillery-plugin-fake-data \
    artillery-plugin-metrics-by-endpoint;
then
    echo "Could not install artillery globally"
    exit 1
fi
pnpm --filter="@sage/artillery-plugin-xtrem" --ignore-scripts install
