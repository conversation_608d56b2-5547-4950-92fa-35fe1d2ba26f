#!/usr/bin/env bash

set -e

# Usage function
display_usage() {
  echo -e "\n**************** Usage Tutorial *************************"
  echo -e "To run xtrem server and load demo data (localy)"
  echo -e "Make sure xtrem project is built and xtrem is started with 'pnpm run start:unsecuredevlogin'"
  echo "For testing purpose locally, load test data:"
  echo -e "\tcmd: pnpm run load:test:data"
  echo -e "\nTo install artillery and all the used plugins in the script:"
  echo -e "\tcmd: npm i -g artillery artillery-plugin-ensure@1.15.0 artillery-plugin-expect artillery-plugin-metrics-by-endpoint"
	echo -e "\nTo run a simple scenario with default settings:"
	echo -e "\tcmd: $0 \n"
  echo "To run a specific scenario: sales-order/create for example"
	echo -e "\tcmd: $0 --scenario sales-order/create --total-users 100 --ramp-up 15 --duration 60 \n"
  echo -e "\tWhere:
\t-   scenario is the name of the scenario
\t-   ramp-up is the amount of time it will take Artillery to add all users
\t-   total-users is the total number of  users
\t-   duration is the value of the duration of the execution phase\n"
  echo "**************************************************************"
	}

#default values;
SCENARIO=sales-order/create
export TOTAL_USERS=500
export RAMP_UP=10
export DURATION=60
export ENVIRONMENT=local
export TENANT_ID=''
export EXECUTION_TYPE='manual'
export SCENARIO_VARIABLES=''
export USER_THINK_TIME=0

# check whether user had supplied -h or --help . If yes display usage
for value in "$@"; do
  if [ "$value" == "--help" ] || [ "$value" == "-h" ]; then
    display_usage
    exit 0
  fi
done

while (( "$#" )); do
  case "$1" in
    --scenario)
        SCENARIO=$2
        shift 2
      ;;
    --total-users)
        export TOTAL_USERS=$2
        shift 2
      ;;
    --ramp-up)
        export RAMP_UP=$2
        shift 2
      ;;
    --duration)
        export DURATION=$2
        shift 2
      ;;
    --user-think-time)
        export USER_THINK_TIME=$2
        shift 2
      ;;
    --cluster)
        export CLUSTER_URL="https://sdmo-$2-api.eu.dev-sagextrem.com"
        export CLUSTER=$2
        export ENVIRONMENT=devops
        shift 2
      ;;
    --tenant)
        TENANT_ID=$2
        shift 2
      ;;
    --execution-type)
        EXECUTION_TYPE=$2
        shift 2
      ;;
    --reports-dir)
        REPORTS_DIR=$2
        mkdir -p "$REPORTS_DIR"
        shift 2
      ;;
    --html-reports-dir)
        HTML_REPORTS_DIR=$2
        shift 2
      ;;
    --variables)
        export SCENARIO_VARIABLES=$2
        shift 2
      ;;
    *) # preserve positional arguments
      echo "ignore parameter $1"
      shift
      ;;
  esac
done

REPORTS_DIR=${REPORTS_DIR:="./reports"}
HTML_REPORTS_DIR=${HTML_REPORTS_DIR:=$REPORTS_DIR}

echo "scenario:$SCENARIO"
echo "total-users:$TOTAL_USERS"
echo "ramp-up:$RAMP_UP"
echo "duration:$DURATION"
echo "REPORTS_DIR:$REPORTS_DIR"
echo "HTML_REPORTS_DIR:$HTML_REPORTS_DIR"
echo "USER_THINK_TIME:$USER_THINK_TIME"
echo "ENVIRONMENT:$ENVIRONMENT"
echo "CLUSTER:$CLUSTER"
echo "TENANT:$TENANT_ID"
echo "EXECUTION_TYPE:$EXECUTION_TYPE"
echo "SCENARIO_VARIABLES:$SCENARIO_VARIABLES"

# get the current directory
CURRENT_DIRECTORY=$(pwd)

# Get the repo root path
XTREM_PATH=$(git rev-parse --show-toplevel)

# build artillery xtrem plugin
if ! pnpm -r --filter="@sage/artillery-plugin-xtrem" build; then
    echo "build failed"
    exit 1
fi

# path to artillery plugin (if not installed globaly)
export ARTILLERY_PLUGIN_PATH=$XTREM_PATH/platform/back-end/artillery-plugin-xtrem/build
echo "ARTILLERY_PLUGIN_PATH:$ARTILLERY_PLUGIN_PATH"

# SCENARIO_PATH is used in the scenario and the config to resolve paths
export SCENARIO_PATH=$CURRENT_DIRECTORY/scenarios/$SCENARIO
export SCENARIO_CONFIG=$SCENARIO_PATH/scenario-config.yml


# replace characters / by _
export SCENARIO_NAME=${SCENARIO//[\/]/_}
REPORT_NAME="$EXECUTION_TYPE-$SCENARIO_NAME-$(date +"%Y_%m_%dT%H_%M_%S")-TOTAL_USERS-$TOTAL_USERS-RAMP_UP-$RAMP_UP-DURATION-$DURATION-USER_THINK_TIME-$USER_THINK_TIME.json"
export REPORT="$REPORTS_DIR/$REPORT_NAME"

# Creates a reports directory if it doesn't exist
if [ ! -d "reports" ]
then
    echo "Reports folder doesn't exist. Creating a new one now"
    mkdir ./reports
    echo "New reports folder created"
fi

# run the  scenario
echo "artillery run $SCENARIO_PATH/scenario.yml \
    --config $SCENARIO_CONFIG \
     -e $ENVIRONMENT \
     -o \"$REPORT\""

# Temporary workaround to avoid the warning "(node:19765) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead."
NODE_NO_WARNINGS=1 artillery run "$SCENARIO_PATH"/scenario.yml \
  --config "$SCENARIO_CONFIG" \
    -e $ENVIRONMENT \
    -o "$REPORT"

EXIT_CODE=$?
if [ $EXIT_CODE -ne 0 ]; then
  RUN_FAILED=1
  echo "##vso[task.logissue type=warning]The Artillery test exited with code $EXIT_CODE"
fi

echo "Report name: $REPORT"

REPORT_OPTS=
if [[ -n ${HTML_REPORTS_DIR} ]]; then
  mkdir -p "${HTML_REPORTS_DIR}"
  REPORT_OPTS="--output=${HTML_REPORTS_DIR}/${REPORT_NAME}.html"
fi

# shellcheck disable=SC2086 # solving the SC2086 warning on $REPORT_OPTS will break command args
artillery report "$REPORT" $REPORT_OPTS
EXIT_CODE=$?
if [ $EXIT_CODE -ne 0 ]; then
    echo "##vso[task.logissue type=warning]The Artillery report exited with code $EXIT_CODE"
    exit 1
fi

if [ "$RUN_FAILED" == "1" ]; then
    exit 1
fi
