#!/usr/bin/env bash

# This script can be used to fix CSV files
# It will load and extract the data for each layer and check if there are any changes

# If there are changes, it will display the status and exit with an error code
# You can then check the differences and commit the changes.

# Usage: scripts/layers/fix-csv-files.sh
# Note: This script should be run from the root of the repository

set -e

layers=( "setup" "test" "demo" "qa")
folders=( "services/main/xtrem-services-main" "tools/glossary/xtrem-glossary" "showcase-sales/main/showcase-sales-main" "showcase-stock/main/showcase-stock-main" "shopfloor/main/shopfloor-main" "x3-connector/main/x3-connector-main" )

for folder in "${folders[@]}"
do
    cd "$folder"
    for layer in "${layers[@]}"
    do
        echo "**************************************************************************************"
        echo "*******"
        echo "*******"
        echo "*******     Fixing $layer CSV files for $folder"
        echo "*******"
        echo "*******"
        echo "**************************************************************************************"
        pnpm load:"$layer":data
        pnpm extract:"$layer":data
        # pnpm xtrem layers --load "$layer"
        # pnpm xtrem layers --extract "$layer"
    done
    cd -
done

# Revert changes to sys-changelog.csv
git diff --name-only | grep "/data/layers/setup/sys-changelog.csv" | xargs git checkout

if [ -n "$(git status --porcelain)" ]; then
    echo "========================================================================================"
    echo "Here is the current status of your local repository"
    echo "If some CSV files were updated, please check the differences and commit them"
    git status
    exit 1
else
    echo "========================================================================================"
    echo "All your CSV files are OK"
    exit 0
fi
