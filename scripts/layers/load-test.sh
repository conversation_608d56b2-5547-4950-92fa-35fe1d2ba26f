set -e
LOG_FILE="$(pwd)"/load-test-time.log
echo > $LOG_FILE
pushd "$(dirname "$0")"/../../platform/show-case/xtrem-show-case
pnpm run xtrem schema --create --reset-database --skip-tables
echo >> $LOG_FILE
echo "xtrem-show-case [setup] layers" >> $LOG_FILE
{ time pnpm run xtrem layers --load setup ; } 2>> $LOG_FILE
cd -
cd "$(dirname "$0")"/../../platform/show-case/xtrem-show-case-bundle
echo >> $LOG_FILE
echo "xtrem-show-case-bundle [setup] layers" >> $LOG_FILE
{ time pnpm run xtrem layers --load setup ; } 2>> $LOG_FILE

cd -
cd "$(dirname "$0")"/../../services/main/xtrem-services-main/
pnpm run xtrem schema --create --reset-database --skip-tables
echo >> $LOG_FILE
echo "xtrem-services-main [test] layers" >> $LOG_FILE
{ time pnpm run xtrem layers --load setup,test ; } 2>> $LOG_FILE
pnpm run xtrem schema --create --reset-database --skip-tables
echo >> $LOG_FILE
echo "xtrem-services-main [setup,demo] layers" >> $LOG_FILE
{ time pnpm run xtrem layers --load setup,demo ; } 2>> $LOG_FILE
pnpm run xtrem schema --create --reset-database --skip-tables
echo >> $LOG_FILE
echo "xtrem-services-main [setup,qa] layers" >> $LOG_FILE
{ time pnpm run xtrem layers --load setup,qa ; } 2>> $LOG_FILE

cd -
cd "$(dirname "$0")"/../../tools/glossary/xtrem-glossary
pnpm run xtrem schema --create --reset-database --skip-tables
echo >> $LOG_FILE
echo "xtrem-glossary [setup,demo] layers" >> $LOG_FILE
{ time pnpm run xtrem layers --load setup,demo ; } 2>> $LOG_FILE

cd -
cd "$(dirname "$0")"/../../shopfloor/main/shopfloor-main/
pnpm run xtrem schema --create --reset-database --skip-tables
echo >> $LOG_FILE
echo "xtrem-shofloor-main [test] layers" >> $LOG_FILE
{ time pnpm run xtrem layers --load setup,test ; } 2>> $LOG_FILE
pnpm run xtrem schema --create --reset-database --skip-tables
echo >> $LOG_FILE
echo "shopfloor-main [setup,demo] layers" >> $LOG_FILE
{ time pnpm run xtrem layers --load setup,demo ; } 2>> $LOG_FILE

echo
echo "==== Timing results ===="
cat $LOG_FILE
rm $LOG_FILE

popd > /dev/null
