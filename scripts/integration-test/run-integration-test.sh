#!/usr/bin/env bash

# execute an integration test

# The following env variables are required:
#
# - TEMP_FOLDER: the temp folder
# - ROOT_REPO_FOLDER: the folder of the repo's root
# - XTREM_TEST_MAX_INSTANCES
# - XTREM_UPDATE_SNAPSHOTS: Update visual snapshots ? ('true' / 'false')
# - PATTERN: feature file pattern
# - SERVICE_OPTIONS: Service options to activate
# - XTREM_SCOPES:
# - timeout: Timeout for cucumber steps
# - loginUserName: login for the xtrem_auth container
# - loginPassword: password for the xtrem_auth container

# - in production mode, TARGET_URL: 'http://sdmo-xtrem.localhost.dev-sagextrem.com:8240'
# - XTREM_SCOPES

mkdir "$TEMP_FOLDER"/cucumber-json
mkdir "$TEMP_FOLDER"/cucumber-results
mkdir "$TEMP_FOLDER"/cucumber-junit/
mkdir "$TEMP_FOLDER"/allure-results

set -e
echo "Running integration tests with:"
echo "  TEMP_FOLDER: $TEMP_FOLDER"
echo "  XTREM_TEST_MAX_INSTANCES: $XTREM_TEST_MAX_INSTANCES"
echo "  XTREM_UPDATE_SNAPSHOTS: $XTREM_UPDATE_SNAPSHOTS"
echo "  PATTERN: $PATTERN"
echo "  SERVICE_OPTIONS: $SERVICE_OPTIONS"
echo "  XTREM_SCOPES: $XTREM_SCOPES"
echo "  ATP_TIMEOUT: $ATP_TIMEOUT"
echo "  ATP_TIMEOUT_WAIT_FOR: $ATP_TIMEOUT_WAIT_FOR"
echo "  TARGET_URL: $TARGET_URL"

# run the integration tests from the pnpm root folder
pnpm -sw run test:ci:integration
