#!/usr/bin/env bash

if nc -zvw3 127.0.0.1 9324; then
    echo "An ElasticMQ server is already running."
    exit 0
fi

if [ -z "$ELASTICMQ_IMAGE" ]; then
  ELASTICMQ_IMAGE="softwaremill/elasticmq-native:1.5.4"
fi

CONFIG_DIR="$(pwd)"
RESTART_OPT=()

while (( "$#" )); do
  case "$1" in
    --config-dir)
        CONFIG_DIR="$2"
        shift 2
      ;;
    --restart)
        RESTART_OPT=(--restart "$2")
        shift 2
      ;;
    *)
      shift
      ;;
  esac
done

# do not quote
echo docker run --name xtrem_sqs -d -p 9324:9324 -p 9325:9325 "${RESTART_OPT[@]}" -v "$CONFIG_DIR"/elasticmq.conf:/opt/elasticmq.conf "$ELASTICMQ_IMAGE"
docker run --name xtrem_sqs -d -p 9324:9324 -p 9325:9325 "${RESTART_OPT[@]}" -v "$CONFIG_DIR"/elasticmq.conf:/opt/elasticmq.conf "$ELASTICMQ_IMAGE"

nc -zvw3 127.0.0.1 9324
