import fs from 'node:fs';
import * as fsp from 'node:path';

import { default as cargs } from './lib/cmd-args.mjs';
import { readJsonFile } from './lib/utils.mjs';

function generateSummary() {
    const summaryFile = cargs.summary;
    const scanResult = processScanResult();
    const markdownSummary = `
${getBuildSummary()}

${scanResult.markdownSummary}
`;

    fs.writeFileSync(summaryFile, markdownSummary);
    const summaryFileSize = Buffer.byteLength(markdownSummary, 'utf8');
    delete scanResult.markdownSummary;
    return { ...scanResult, summaryFileSize, summaryFile };
}

function getBuildSummary() {
    const packageJson = readJsonFile(fsp.join(cargs.dockerFolder, 'package.json'));
    return `# Image build summary

* Image name: **${cargs.imageName}**
* Version: **${packageJson.version}**`;
}

function processScanResult() {
    let aquaScanResults = readJsonFile(fsp.join(cargs.aquaScanDir, 'out.json'));

    if (Array.isArray(aquaScanResults)) {
        aquaScanResults = aquaScanResults[0];
    }
    if (aquaScanResults.results) {
        aquaScanResults = aquaScanResults.results;
    }
    const findings = aquaScanResults.resources ? Object.values(aquaScanResults.resources) : [];
    const acknowledgedFindings = findings.filter((finding) =>
        finding.vulnerabilities.some((vuln) => vuln.already_acknowledged),
    );
    // vulnerabilities that are acknowledged are not considered as relevant
    const nonAcknowledgedFindings = findings.filter((finding) =>
        finding.vulnerabilities.some((vuln) => !vuln.already_acknowledged),
    );
    const findingsWithFix = getFindingsBySeverity(
        nonAcknowledgedFindings.filter((finding) => finding.vulnerabilities.some((vuln) => vuln.fix_version != null)),
    );
    const findingsWithoutFix = getFindingsBySeverity(
        nonAcknowledgedFindings.filter((finding) => finding.vulnerabilities.some((vuln) => vuln.fix_version == null)),
    );

    const isElevateCompliant =
        findingsWithFix.critical.count == 0 && findingsWithFix.high.count == 0 && findingsWithFix.medium.cwesCount <= 4;

    return {
        markdownSummary: getScanSummary(
            isElevateCompliant,
            aquaScanResults,
            findingsWithFix,
            findingsWithoutFix,
            getFindingsBySeverity(acknowledgedFindings),
        ),
        critical: findingsWithFix.critical.count,
        high: findingsWithFix.high.count,
        medium: findingsWithFix.medium.count,
        mediumCwes: findingsWithFix.medium.cwesCount,
        isElevateCompliant,
    };
}

function getScanSummary(
    isElevateCompliant,
    aquaScanResults,
    findingsWithFix,
    findingsWithoutFix,
    acknowledgedFindings,
) {
    return `# Image scan summary

**${isElevateCompliant ? '✅ Compliant' : '❌ Non compliant'} with Sage Elevate security requirements**

## Vulnerability summary

${Object.entries(aquaScanResults.vulnerability_summary)
    .map(([k, v]) => `* ${k}: ${v}`)
    .join('\n')}

## Vulnerabilities with available fix

${genVulnerabilitiesMarkdown(findingsWithFix)}

## Vulnerabilities without available fix

${genVulnerabilitiesMarkdown(findingsWithoutFix)}

## Acknowledged vulnerabilities

${genVulnerabilitiesMarkdown(acknowledgedFindings)}
`;
}

function getFindingsBySeverity(resources) {
    const findingsBySeverity = { critical: {}, high: {}, medium: {}, low: {} };
    resources.forEach((entry) =>
        entry.vulnerabilities.forEach((v) => {
            const severity = v.aqua_severity;
            const bySeverity = findingsBySeverity[severity];
            bySeverity.cwesById = bySeverity.cwesById ?? {};
            bySeverity.vulnerabilities = bySeverity.vulnerabilities ?? [];
            const cwesById = bySeverity.cwesById;
            v.cwe_info = v.cwe_info ?? [];
            v.cwe_info.forEach((cwe) => {
                cwesById[cwe.Id] = cwesById[cwe.Id] ?? cwe;
            });
            bySeverity.vulnerabilities.push({ resource: entry.resource, details: v });
        }),
    );
    Object.values(findingsBySeverity).forEach((bySeverity) => {
        bySeverity.cwesById = bySeverity.cwesById || {};
        bySeverity.cwesCount = Object.keys(bySeverity.cwesById).length;
        bySeverity.vulnerabilities = bySeverity.vulnerabilities ?? [];
        bySeverity.count = bySeverity.vulnerabilities.length;
    });
    return findingsBySeverity;
}

function genVulnerabilitiesMarkdown(findingsBySeverity) {
    return Object.entries(findingsBySeverity)
        .map(
            ([severity, finding]) =>
                `### ${severity.toUpperCase()} - ${finding.cwesCount} different types of vulnerability (CWE)
${(finding.vulnerabilities || []).map((vuln) => getVulnerabilityDetail(vuln)).join('\n')}`,
        )
        .join('\n');
}

function getVulnerabilityDetail(vuln) {
    return `
> **${vuln.resource.cpe}**${vuln.resource.path ? `\n> in ${vuln.resource.path}` : ''}
> ${getCveDetail(vuln.details)}
> ${getCweDetail(vuln.details.cwe_info)}
`;
}

function getCveDetail(cve) {
    const description = cve.description.replace(/\n/g, '');
    return `[${cve.name}](https://cve.mitre.org/cgi-bin/cvename.cgi?name=${cve.name}): ${description}`;
}

function getCweDetail(cwes) {
    return `${cwes
        .map((cwe) => `[${cwe.Id}](https://cwe.mitre.org/data/definitions/${cwe.Id.split('-')[1]}.html): ${cwe.name}`)
        .join('\n> ')}`;
}

try {
    const scanResult = generateSummary();
    console.log(JSON.stringify(scanResult, null, 2));
} catch (e) {
    console.log(`Failed to generate image scan summary: ${e.message}`);
    process.exit(1);
}
