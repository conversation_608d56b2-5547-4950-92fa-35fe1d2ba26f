// @ts-check
import https from 'node:https';
import { URL } from 'node:url';
import { logger } from '../logger.mjs';

export class ApiClient {
    #baseUrl;
    #basePath;

    constructor(baseUrl) {
        const url = new URL(baseUrl);
        this.#baseUrl = url.origin;
        this.#basePath = url.pathname;
    }

    setup() {
        return this;
    }

    getUrl(path) {
        if (/http(s)?:\/\//.test(path)) {
            return path;
        }
        const baseUrl = path.startsWith('/') ? this.#baseUrl : `${this.#baseUrl}${this.#basePath}/`;
        return `${baseUrl}${path}`;
    }

    getHeaders() {
        return {
            'User-Agent': 'Azure DevOps Pipelines',
        };
    }

    async request(method, path, payload, headers) {
        const options = {
            method,
            headers: { ...this.getHeaders(), ...headers },
        };

        return new Promise((resolve, reject) => {
            const url = this.getUrl(path);
            logger.info(`${options.method} ${url}`);
            const req = https.request(url, options, (res) => {
                res.setEncoding('utf8');
                let body = '';

                res.on('data', (data) => {
                    body += data;
                });

                res.on('end', () => {
                    if (/^application\/json;/.test(res.headers['content-type'] ?? '')) {
                        body = JSON.parse(body);
                    }
                    resolve({
                        body,
                        headers: res.headers,
                        statusCode: res.statusCode,
                        statusMessage: res.statusMessage,
                    });
                });

                res.on('error', (err) => {
                    logger.error(`Response error ${options.method} ${url}`, err);
                    reject(err);
                });
            });
            req.on('error', (err) => {
                logger.error(`Request error ${options.method} ${url}`, err);
                reject(err);
            });

            if (payload != null) {
                req.write(typeof payload !== 'string' ? JSON.stringify(payload) : payload);
            }
            req.end();
        });
    }

    async get(path, headers) {
        return this.request('GET', path, null, headers);
    }

    async post(path, data, headers) {
        return this.request('POST', path, data, headers);
    }

    async put(path, data, headers) {
        return this.request('PUT', path, data, headers);
    }

    async delete(path, headers) {
        return this.request('DELETE', path, headers);
    }
}
