// @ts-check
import { ApiClient } from './api-client.mjs';
import { logger } from '../logger.mjs';
import { URLSearchParams } from 'node:url';

/**
 * @typedef PullBranch
 * @property {string} ref
 * @property {string} sha
 *
 * @typedef PullRequestOptions
 * @property {string} [state]
 * @property {string[]} [includes]
 * @property {boolean} [pollMergeable]
 *
 * @typedef SanitizedPullRequest
 * @property {string} html_url
 * @property {string} url
 * @property {string} statuses_url
 * @property {number} number
 * @property {string} state
 * @property {string} title
 * @property {string} user
 * @property {string} created_at
 * @property {string} updated_at
 * @property {string|null} closed_at
 * @property {string|null} merged_at
 * @property {string} merge_commit_sha
 * @property {boolean} draft
 * @property {string} [body]
 * @property {string[]} labels
 * @property {PullBranch} head
 * @property {PullBranch} base
 * @property {boolean} [merged]
 * @property {boolean} [mergeable]
 * @property {boolean} [rebaseable]
 * @property {string} [mergeable_state]
 * @property {string} [merged_by]
 * @property {string[]} [comments]
 * @property {string[]} [review_comments]
 * @property {boolean} [maintainer_can_modify]
 * @property {*} [commits]
 * @property {*} [additions]
 * @property {*} [deletions]
 * @property {string[]} [changed_files]
 *
 * @typedef RepositoryContent
 * @property {string} name
 * @property {string} path
 * @property {string} type
 * @property {number} size
 * @property {string} encoding
 * @property {string} content
 * @property {string} sha
 * @property {string} url
 * @property {string} git_url
 * @property {string} html_url
 * @property {string} download_url
 *
 */

const defaultGitCredentials = {
    username: process.env.GITHUB_USERNAME,
    password: process.env.GITHUB_PASSWORD,
};

export class GithubApi extends ApiClient {
    #repository;
    #credentials;
    #token;
    #dryRun;

    constructor(repository, credentials = defaultGitCredentials, options = {}) {
        super(`https://api.github.com/repos/${repository}`);
        this.#repository = repository;
        this.#credentials = { ...credentials };
        this.#dryRun = options.dryRun;
    }

    setup() {
        super.setup();
        if (!this.#repository) {
            throw new Error('missing repository');
        }
        if (!this.#credentials.username) {
            throw new Error('missing username');
        }
        this.#token = this.#credentials.password?.startsWith('ghp_')
            ? this.#credentials.password
            : Buffer.from(`${this.#credentials.username}:${this.#credentials.password}`, 'utf8').toString('base64');
        return this;
    }

    getHeaders() {
        const token = this.#token;
        return {
            ...super.getHeaders(),
            accept: 'application/vnd.github+json',
            'X-GitHub-Api-Version': '2022-11-28',
            Authorization: token?.startsWith('ghp_') ? `Bearer ${token}` : `Basic ${token}`,
        };
    }

    /**
     *
     * @param {*} pr
     * @param {PullRequestOptions} [options]
     * @returns {SanitizedPullRequest}
     */
    sanitizePullRequestResult(pr, options = {}) {
        const sanitized = {
            html_url: pr.html_url,
            url: pr.url,
            statuses_url: pr.statuses_url,
            number: pr.number,
            state: pr.state,
            title: pr.title,
            user: pr.user.login,
            created_at: pr.created_at,
            updated_at: pr.updated_at,
            closed_at: pr.closed_at,
            merged_at: pr.merged_at,
            merge_commit_sha: pr.merge_commit_sha,
            draft: pr.draft,
            labels: pr.labels.map((l) => l.name),
            head: {
                ref: pr.head.ref,
                sha: pr.head.sha,
            },
            base: {
                ref: pr.base.ref,
                sha: pr.base.sha,
            },
            merged: pr.merged,
            mergeable: pr.mergeable,
            rebaseable: pr.rebaseable,
            mergeable_state: pr.mergeable_state,
            merged_by: pr.merged_by,
            comments: pr.comments,
            review_comments: pr.review_comments,
            maintainer_can_modify: pr.maintainer_can_modify,
            commits_url: pr.commits_url,
            additions: pr.additions,
            deletions: pr.deletions,
            changed_files: pr.changed_files,
        };
        const { includes = [] } = options;

        const propertyNames = Object.getOwnPropertyNames(pr);
        for (const key of includes) {
            if (propertyNames.includes(key)) {
                sanitized[key] = pr[key];
            }
        }
        return sanitized;
    }

    sanitizeStatusResult(st) {
        return {
            url: st.url,
            state: st.state,
            description: st.description,
            target_url: st.target_url,
            context: st.context,
            created_at: st.created_at,
        };
    }

    async getRateLimit() {
        const { body } = await this.get('/rate_limit');

        if (body.message) {
            console.error(body.message);
            if (body.documentation_url) {
                console.error('see:', body.documentation_url);
            }
            process.exit(1);
        }
        return body;
    }

    async getAllPages(path, result) {
        const page = await this.get(path);
        if (Array.isArray(page.body)) {
            const body = result?.body ?? [];
            page.body = [...body, ...page.body];
        } else {
            const body = result?.body ?? {};
            page.body = { ...body, ...page.body };
        }
        const link = page.headers?.link;
        if (link) {
            const next = /<([^<>]+)>;\s*rel="next"/.exec(link);
            const nextUrl = next?.[1];
            if (next) {
                return this.getAllPages(nextUrl, page);
            }
        }
        return page;
    }

    /**
     * Gets a pull request by its number
     * @param {string} prNumber
     * @param {PullRequestOptions} [options]
     * @returns {Promise<SanitizedPullRequest>}
     */
    async getPullRequest(prNumber, options) {
        const { body: prBody } = await this.get(`pulls/${prNumber}`);
        if (prBody.message) {
            console.error(prBody.message);
            if (prBody.documentation_url) {
                console.error('see:', prBody.documentation_url);
            }
            process.exit(1);
        }
        // We have to poll the mergeable state because it is not in the payload when requesting once
        // see https://docs.github.com/en/rest/guides/using-the-rest-api-to-interact-with-your-git-database?apiVersion=2022-11-28#checking-mergeability-of-pull-requests
        if (options?.pollMergeable && prBody.mergeable === null) {
            await new Promise((resolve) => setTimeout(resolve, 1000));
            return this.getPullRequest(prNumber, options);
        }
        return this.sanitizePullRequestResult(prBody, options);
    }

    // we cannot filter on the mergeable state because the property is not in the payload when requesting the list of PR
    /**
     * Gets a pull request by its number
     * @param {string|null} [base]
     * @param {PullRequestOptions} [options]
     * @returns {Promise<SanitizedPullRequest[]>}
     */
    async getPullRequests(base, options = {}) {
        const qs = new URLSearchParams();
        const { state = 'open' } = options;
        if (state) {
            qs.append('state', state);
        }
        if (base) {
            qs.append('base', base);
        }
        // 100 is the max according to GitHub doc https://docs.github.com/en/rest/pulls/pulls?apiVersion=2022-11-28#list-pull-requests
        // Manage paging: see https://docs.github.com/en/rest/guides/using-pagination-in-the-rest-api?apiVersion=2022-11-28
        qs.append('per_page', '100');
        const { body: prListResult } = await this.getAllPages(`pulls?${qs}`);
        if (prListResult.message) {
            console.error(prListResult.message);
            if (prListResult.documentation_url) {
                console.error('see:', prListResult.documentation_url);
            }
            process.exit(1);
        }
        return prListResult.map((pr) => this.sanitizePullRequestResult(pr, options));
    }

    async mergePullRequest(pr, data = {}) {
        const prefix = this.#dryRun ? '[DRY-RUN] ' : '';
        // see https://docs.github.com/en/rest/pulls/pulls?apiVersion=2022-11-28#merge-a-pull-request
        data.merge_method = 'squash';
        data.commit_title = data.commit_title || `${pr.title} (#${pr.number})`;
        if (this.#dryRun) {
            logger.info(`${prefix}merge PR ${pr.number} ${pr.html_url} ${pr.title}`);
            return Promise.resolve({ statusCode: 200, statusMessage: 'OK', url: pr.html_url });
        }
        return this.put(`pulls/${pr.number}/merge`, data);
    }

    async rerequestChecks(check) {
        const prefix = this.#dryRun ? '[DRY-RUN] ' : '';
        const { id, name } = check;
        logger.info(`${prefix}rerequest checks for check run ${id} ${name}`);
        if (this.#dryRun) {
            return Promise.resolve({ statusCode: 200, statusMessage: 'OK' });
        }
        return this.post(`check-runs/${id}/rerequest`);
    }

    getPullRequestDiff(path) {
        return this.get(path, { Accept: 'application/vnd.github.v3.diff' });
    }

    /**
     *
     * @param {number} pr
     * @param {SanitizedPullRequest} [prDetails]
     */
    async prUpdateBranch(pr, prDetails) {
        // see https://docs.github.com/en/rest/pulls/pulls?apiVersion=2022-11-28#update-a-pull-request-branch
        if (!prDetails) {
            const prFullDetails = await this.get(`pulls/${pr}`);
            if (prFullDetails.statusCode !== 200) {
                return {
                    statusCode: prFullDetails.statusCode,
                    statusMessage: prFullDetails.statusMessage,
                    url: `https://github.com/${this.#repository}/pull/${pr}`,
                };
            }
            prDetails = this.sanitizePullRequestResult(prFullDetails.body);
        }
        const prUpdate = await this.put(`pulls/${pr}/update-branch`, {
            expected_head_sha: prDetails.head.sha,
        });
        if (prUpdate.statusCode === 202) {
            logger.success(`${prUpdate.body.message} ${prUpdate.body.url}`);
        }
        return {
            statusCode: prUpdate.statusCode,
            statusMessage: prUpdate.statusMessage,
            url: prUpdate.body.url || prDetails.html_url,
        };
    }

    async getStatuses(pullrequests, { context }) {
        const statuses = [];
        for (const pr of pullrequests) {
            const { body: prStatuses } = await this.get(pr.statuses_url);
            prStatuses.filter((s) => s.context === context).forEach((s) => statuses.push(s));
        }
        return statuses;
    }

    async setStatuses(pullrequests, { state, target_url, description, context }) {
        const prefix = this.#dryRun ? '[DRY-RUN] ' : '';
        logger.info(`${prefix}set status '${state}' with context '${context}' to ${pullrequests.length} pull requests`);
        for (const pr of pullrequests) {
            logger.info(`${prefix}process PR ${pr.number} ${pr.html_url} ${pr.title}`);
            if (!this.#dryRun) {
                const {
                    body: statuses,
                    statusCode,
                    statusMessage,
                } = await this.post(`statuses/${pr.head.sha}`, {
                    state,
                    target_url,
                    description,
                    context,
                });
                console.log(statusCode, statusMessage);
                switch (statusCode) {
                    // created
                    case 201:
                        console.log(this.sanitizeStatusResult(statuses));
                        break;
                    case 422:
                        logger.warn(`Cannot set status (not mergeable): PR ${pr.number} ${pr.html_url}`);
                        break;
                    default:
                        logger.warn(`Cannot set status`);
                        break;
                }
            }
        }
    }

    async getCkeckRuns(sha) {
        const qs = new URLSearchParams();
        qs.append('per_page', '100');
        const { body: checks } = await this.get(`commits/${sha}/check-runs?${qs}`);
        return checks;
    }

    deleteIssueLabel(issueNumber, labelName) {
        return this.delete(`issues/${issueNumber}/labels/${labelName}`);
    }

    addIssueLabels(issueNumber, labels) {
        return this.put(`issues/${issueNumber}/labels`, {
            labels,
        });
    }

    createPullRequest(data) {
        return this.post('pulls', data);
    }

    /**
     * Get the content of a file from the repository
     * @param {string} path The path to the file
     * @param {string} ref The branch or commit ref
     * @returns {Promise<RepositoryContent>}
     */
    async getContents(path, ref) {
        const qs = new URLSearchParams();
        qs.append('ref', ref);
        const res = await this.get(`contents/${path}?${qs}`);
        const { body } = res;
        if (body.encoding === 'base64') {
            body.content = Buffer.from(body.content, 'base64').toString('utf8');
        }
        return body;
    }

    /**
     * Get the commit details
     * @param {string} sha The commit sha
     * @returns
     */
    async getCommit(sha) {
        const { body } = await this.get(`commits/${sha}`);
        return body;
    }

    /**
     * Get the commit list
     * @param {string} path The path to the file
     * @param {string} shaOrBranch The branch or commit sha to start from
     * @param {any} [options] Additional options (author, since, until, per_page)
     * @returns
     */
    async getCommitList(path, shaOrBranch, options) {
        const qs = new URLSearchParams();
        qs.append('path', path);
        qs.append('sha', shaOrBranch);
        if (options?.author) {
            qs.append('author', options.author);
        }
        if (options?.since) {
            qs.append('since', options.since);
        }
        if (options?.until) {
            qs.append('until', options.until);
        }
        if (options?.per_page) {
            qs.append('per_page', options.per_page);
        }
        const { body } = await this.get(`commits?${qs}`);
        return body;
    }

    async getBranchProtection(branch) {
        // see https://docs.github.com/en/rest/branches/branch-protection?apiVersion=2022-11-28#get-branch-protection
        const { body } = await this.get(`branches/${branch}/protection`);
        return body;
    }
}
