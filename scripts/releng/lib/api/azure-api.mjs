// @ts-check
import { ApiClient } from './api-client.mjs';

const azureDevOps = {
    collectionUri: process.env.SYSTEM_COLLECTIONURI || 'https://sage-liveservices.visualstudio.com/',
    project: process.env.SYSTEM_TEAMPROJECTID || 'X3 Xtrem',
};

export class AzureApi extends ApiClient {
    #token;
    #apiVersion;
    #authMethod;

    static getProjectUrl() {
        const { collectionUri, project } = azureDevOps;
        return `${collectionUri}${encodeURIComponent(decodeURIComponent(project))}`;
    }

    constructor(apiVersion) {
        super(`${AzureApi.getProjectUrl()}/_apis`);

        if (process.env.SYSTEM_ACCESSTOKEN) {
            // on azure ci, use the SYSTEM_ACCESSTOKEN env variable
            this.#authMethod = 'Bearer';
            this.#token = process.env.SYSTEM_ACCESSTOKEN;
        } else if (process.env.AZURE_PAT) {
            // for local test, use the AZURE_PAT env variable
            this.#authMethod = 'Basic';
            this.#token = Buffer.from(`.:${process.env.AZURE_PAT}`).toString('base64');
        } else {
            throw new Error('no valid azure token for authentication');
        }

        this.#apiVersion = apiVersion || '7.1-preview.7';
    }

    setup() {
        super.setup();
        return this;
    }

    getHeaders() {
        return {
            ...super.getHeaders(),
            Authorization: `${this.#authMethod} ${this.#token}`,
        };
    }

    async getBuilds({ definitions, statusFilter, branchName, queryOrder = 'startTimeDescending', top = 1 }) {
        const qs = new URLSearchParams();
        qs.append('definitions', definitions);
        if (statusFilter) {
            qs.append('statusFilter', statusFilter);
        }
        if (branchName) {
            qs.append('branchName', `refs/heads/${branchName}`);
        }
        qs.append('QueryOrder', queryOrder);
        qs.append('$top', `${top}`);
        qs.append('api-version', this.#apiVersion);
        // see https://learn.microsoft.com/en-us/rest/api/azure/devops/build/builds/list?view=azure-devops-rest-7.1
        const { body: builds } = await this.get(`build/builds?${qs}`);

        return builds;
    }
}
