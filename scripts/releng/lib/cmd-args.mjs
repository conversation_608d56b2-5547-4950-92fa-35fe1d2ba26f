// @ts-check

/**
 * @typedef {Record<string,*>} CommandArgs
 * @property {string[]} $command
 * @property {string[]} $unflagged
 */

const args = process.argv.slice(2);

/** @type {CommandArgs} */
export default args.reduce(
    (/** @type {CommandArgs} */ r, arg) => {
        if (/^\-\-\w/.test(arg)) {
            arg = arg.substring(2);
            const camelName = arg.replace(/-(\w)/g, (m, p1) => p1.toUpperCase());
            r[camelName] = true;
            r.$currentOpt = camelName;
        } else if (r.$currentOpt) {
            r[r.$currentOpt] = arg;
            delete r.$currentOpt;
        } else if (r.$command == null) {
            r.$command = arg;
        } else {
            r.$unflagged.push(arg);
        }
        return r;
    },
    { $command: null, $unflagged: [] },
);
