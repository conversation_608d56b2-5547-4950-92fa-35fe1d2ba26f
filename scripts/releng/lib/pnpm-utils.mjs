// @ts-check
'use stricts';
import fs from 'node:fs/promises';
import { posix as fsp } from 'node:path';

import { execCmdSync, logStep, readJsonFile, readYamlFile, writeJsonFile, rootDir } from './utils.mjs';
import { logger } from './logger.mjs';

/**
 * @typedef MissingPackage
 * @property {string} key
 * @property {string} name
 * @property {string} version
 * @property {string} pnpmKey
 * @property {string} [fix]
 **/

let pnpmLockYaml;

export async function getPnpmLock() {
    if (!pnpmLockYaml) {
        const rootPnpmLockLocation = fsp.resolve(fsp.join(rootDir, 'pnpm-lock.yaml'));
        pnpmLockYaml = await readYamlFile(rootPnpmLockLocation);
    }
    return pnpmLockYaml;
}

/**
 * Generates the package-lock.json from an application that use pnpm-lock.yaml
 * @param {string} targetDir the folder of the app
 * @param {string=} sourceDir the folder from which package.json and pnpm-lock.yaml should be copied
 */
export async function convertPnpmInstallToNpm(targetDir, sourceDir) {
    const preservePnpm = !!sourceDir;

    logStep(`Generate package-lock.json from ${sourceDir} to ${targetDir}`);

    const targetPackageJsonFile = fsp.join(targetDir, 'package.json');
    const sourcePackageJsonFile = sourceDir ? fsp.join(sourceDir, 'package.json') : targetPackageJsonFile;
    const targetPnpmLockFile = fsp.join(targetDir, 'pnpm-lock.yaml');
    logStep(`Copy required artifacts from ${sourceDir} to ${targetDir}`);
    if (sourceDir) {
        await fs.copyFile(sourcePackageJsonFile, targetPackageJsonFile);
        await fs.copyFile(fsp.join(sourceDir, 'pnpm-lock.yaml'), targetPnpmLockFile);
    }
    // TODO: might be better to get this from the package.json
    await fs.cp(fsp.join(rootDir, 'patches'), fsp.join(targetDir, 'patches'), { recursive: true });

    logStep(`Enable corepack into ${targetDir}`);
    execCmdSync('corepack enable', { cwd: targetDir, logCommand: true });

    logStep(`Run pnpm install into ${targetDir}`);
    execCmdSync('pnpm -v', { cwd: targetDir, logCommand: true });

    // first install with pnpm to have the hoisted node_modules
    // TO INVESTIGATE: for some reason exec from utils does not work, use spawnSync
    execCmdSync('pnpm install --prod --frozen-lockfile --config.node-linker=hoisted', {
        cwd: targetDir,
        logCommand: true,
    });

    logger.success(`Install done in ${targetDir}`);

    // copy the overrides section and tranform it to a npm format
    // pnpm use something like :
    //      "jsdom@22>tough-cookie": "^4.1.3"
    // then it needs to be transformed to:
    //      "jsdom@22": {
    //           "tough-cookie": "^4.1.3"
    //      },
    const packageJson = readJsonFile(targetPackageJsonFile);
    const pnpmOverrides = packageJson.pnpm?.overrides;
    if (!preservePnpm) {
        // we no longer need pnpm section
        delete packageJson.pnpm;
    }

    if (pnpmOverrides) {
        packageJson.overrides = {};
        for (const key of Object.keys(pnpmOverrides)) {
            const version = pnpmOverrides[key];
            if (key.includes('>')) {
                const keys = key.split('>').reverse();
                const packName = keys.pop();
                if (packName) {
                    packageJson.overrides[packName] = keys.reduce((dep, k, i) => {
                        if (i === 0) {
                            dep[k] = version;
                            return dep;
                        }
                        return { [k]: dep };
                    }, {});
                }
            } else {
                if (packageJson.dependencies[key]) {
                    // the override must use the same version of the package dependencies to avoid the npm error:
                    // npm ERR! Override for ... conflicts with direct dependency
                    packageJson.overrides[key] = packageJson.dependencies[key];
                } else {
                    packageJson.overrides[key] = version;
                }
            }
        }
    }

    if (await convertPatches(targetDir)) {
        packageJson.scripts = packageJson.scripts ?? {};
        if (packageJson.scripts.postinstall != null) {
            throw new Error('non empty postinstall is not yet supported!');
        }
        packageJson.scripts.postinstall = 'npx --yes patch-package';
    }
    // delete the pnpm package manager, we will use the default npm
    delete packageJson.packageManager;

    logStep(`Write modified package.json into ${targetDir}`);
    writeJsonFile(targetPackageJsonFile, packageJson);

    logStep(`Generate package-lock.json from existing node_modules`);
    // now we can generate the package-lock.json file
    // npm use the version found in node_modules which helps to have the same locked versions
    execCmdSync('npm install --omit=dev --package-lock-only', { cwd: targetDir, logCommand: true });
    await fs.rm(fsp.join(targetDir, 'node_modules'), { recursive: true });

    // Because of transitive dependencies, we might have some missing packages but this is not a strong issue except for sage packages.
    // All sage packages must have a matching version because they won't be resolved by npm from the registry
    const missingPackages = await compareLockVersion(targetDir);
    const sagePackages = missingPackages.filter((p) => p.name.startsWith('@sage'));
    if (sagePackages.length > 0) {
        const missingMessages = sagePackages.map((p) => `${p.key}: ${p.name}@${p.version} (${p.pnpmKey})`);
        throw new Error(`Verification failed, missing:\n  - ${missingMessages.join('\n  - ')}`);
    }
    const nonSagePackages = missingPackages.filter((p) => !p.name.startsWith('@sage'));
    if (nonSagePackages.length > 0) {
        // we have recurrent issues with peer dependencies but npm is able to resolve them
        // so we can ignore them for now but we need to investigate why we have them.
        // One reason could the way pnpm handle peer dependencies compared to npm
        const missingMessages = nonSagePackages.map(
            (p) => `${p.key}: ${p.name}@${p.version} (${p.pnpmKey})${p.fix ? ` (${p.fix})` : ''}`,
        );
        logger.warn(`Verification failed, missing:\n  - ${missingMessages.join('\n  - ')}`);
    }

    if (sourceDir) {
        writeJsonFile(sourcePackageJsonFile, packageJson);
        await fs.copyFile(
            targetPackageJsonFile.replace(/\/package.json$/, '/package-lock.json'),
            sourcePackageJsonFile.replace(/\/package.json$/, '/package-lock.json'),
        );
    }
    if (!preservePnpm) {
        // we no longer need the pnpm-lock.yaml file
        await fs.rm(targetPnpmLockFile);
    }
}

/**
 * Compare the version of package in a package-lock.json with the one in the pnpm-lock.yaml
 *
 * @param {string} targetDir
 * @returns {Promise<MissingPackage[]>} list of errors
 */
export async function compareLockVersion(targetDir) {
    const targetPackageLockJsonFile = fsp.join(targetDir, 'package-lock.json');
    const targetPnpmLockFile = fsp.join(targetDir, 'pnpm-lock.yaml');
    const packageLock = readJsonFile(targetPackageLockJsonFile);
    const pnpmLock = await readYamlFile(targetPnpmLockFile);
    const missing = [];
    for (const [key, npmDep] of Object.entries(packageLock.packages)) {
        if (key) {
            const search = 'node_modules/';
            const packName = npmDep.name ?? key.substring(key.lastIndexOf(search) + search.length);
            const pnpmKey = `${packName}@${npmDep.version}`;
            let pnpmMatchingKeys = Object.keys(pnpmLock.packages).filter((k) => k.startsWith(pnpmKey));
            // We need at least one but it is not an issue if we have more.
            if (pnpmMatchingKeys.length === 0) {
                // second chance with name, ex:
                //  sage-xtrem-cli-atp@file:sage-packages/sage-xtrem-cli-atp-37.0.33.tgz(@ckeditor/ckeditor5-watchdog@39.0.2)(@codemirror/language@6.0.0)(@cucumber/message-streams@4.0.1)(@sage/xtrem-async-helper@37.0.33)(@sage/xtrem-bytenode@37.0.33)(@sage/xtrem-cli-lib@37.0.33)(@sage/xtrem-cli-main@37.0.33)(@sage/xtrem-core@37.0.33)(@sage/xtrem-infrastructure-adapter@37.0.33)(@sage/xtrem-service@37.0.33)(@sage/xtrem-shared@37.0.33)(@types/react@17.0.55)(ag-charts-community@1.2.0)(draft-js@0.11.7)(pg@8.11.3)(playwright-core@1.37.0)(prop-types@15.8.1)(react-is@18.2.0)(webpack@5.77.0):
                //      resolution: {integrity: sha512-X5fSJ9n6fmZ525zn/P/bvheh/14YCHEIgDLrxPHmejh4+gPXog/ju61V11rL4LlLles2ZMYNrbaehd6dHvnfjQ==, tarball: file:sage-packages/sage-xtrem-cli-atp-37.0.33.tgz}
                //      id: file:sage-packages/sage-xtrem-cli-atp-37.0.33.tgz
                //      name: '@sage/xtrem-cli-atp'
                //      version: 37.0.33

                const foundKey = Object.keys(pnpmLock.packages).find((k) => k.startsWith(`${packName}@file:`));
                if (!foundKey) {
                    let fix;
                    // ajv is a special case, the version might not be correctly resolved.
                    // it requires to run 'pnpm -r up ajv@<version>'
                    if (packName === 'ajv') {
                        fix = `run 'pnpm -r up ${packName}@${npmDep.version}' to fix it`;
                    }
                    missing.push({
                        key,
                        name: packName,
                        version: npmDep.version,
                        pnpmKey,
                        fix,
                    });
                }
            }
        }
    }
    return missing;
}

/**
 * Converts the patches file name to legacy and return true if some patches exist
 * @param {string} targetDir
 * @returns {Promise<boolean>}
 */
async function convertPatches(targetDir) {
    const patchesDir = fsp.join(targetDir, 'patches');
    logStep(`Convert patches file in ${patchesDir}`);
    try {
        const files = await fs.readdir(patchesDir);
        for (const file of files) {
            // ex: @<EMAIL> => @ag-grid-community+core+27.0.0.patch
            const legacyName = file.replace(/__/g, '+').replace(/@(\d)/g, '+$1');
            const packPath = legacyName.split('+').slice(0, -1).join('/');
            const from = fsp.join(patchesDir, file);
            const to = fsp.join(patchesDir, legacyName);
            console.log(`  - ${from} => ${to}`);
            await fs.rename(from, to);
            const content = await fs.readFile(to, 'utf-8');
            // we also need to replace the path inside the patch diff
            const lines = content.split('\n');
            for (let i = 0; i < lines.length; i++) {
                if (/(---|\+\+\+|diff --git) (a|b)\//.test(lines[i])) {
                    lines[i] = lines[i].replace(/ (a|b)\//g, ` $1/node_modules/${packPath}/`);
                }
            }
            await fs.writeFile(to, lines.join('\n'));
        }
        return files.length > 0;
    } catch (e) {
        console.error(`cannot convert patches: ${e.message}`);
    }
    return false;
}
