// @ts-check
// use javascript to not have to install ts-node, typescript,... and save transpilation
import { fileURLToPath } from 'node:url';
import { spawn, spawnSync } from 'node:child_process';
import fs from 'node:fs';
import { posix as fsp } from 'node:path';
import { logger } from './logger.mjs';
/**
 * @typedef PnpmDependency
 * @property {string} [name]
 * @property {string} [from]
 * @property {string} version
 * @property {string} [resolved]
 * @property {string} [path]
 * @property {Record<string,PnpmDependency>} [dependencies]
 */

/**
 * @typedef DependencyGraphNode
 * @property {string} name
 * @property {string} version
 * @property {string} [path]
 * @property {boolean} isExternal
 * @property {DependencyGraphNode[]} [dependencies]
 */

/**
 * @callback DependencyFilter
 * @param {string} name
 * @returns {boolean}
 */

const __filename = fileURLToPath(import.meta.url);
const __dirname = fsp.dirname(__filename);

export const rootDir = fsp.join(__dirname, '..', '..', '..');

export function logStep(message) {
    logger.info(message, '\n');
}

export function flatten(arr, depth) {
    return arr?.flat(depth ?? 9);
}

export function uniqArrayFilter(value, index, self) {
    return self.indexOf(value) === index;
}

export function uniq(arr) {
    return arr.filter(uniqArrayFilter);
}

export function execCmdSync(cmd, options) {
    if (options.logCommand) {
        console.log(`> ${cmd}`);
    }
    cmd = cmd.split(/\s/);
    spawnSync(cmd[0], cmd.slice(1), { stdio: 'inherit', ...options });
}

export async function exec(command, execOptions) {
    if (execOptions.logCommand) {
        console.log(`> ${command}`);
    }
    let spawnOptions;
    if (execOptions.inherit) {
        spawnOptions = {
            cwd: execOptions.cwd,
            shell: execOptions.shell === false ? undefined : (execOptions.shell ?? '/bin/bash'),
        };
    } else {
        spawnOptions = {
            stdio: ['ignore', 'pipe', 'ignore'],
            cwd: execOptions.cwd,
            shell: execOptions.noShell ? undefined : '/bin/bash',
        };
    }
    const streamData = !execOptions.inherit && execOptions.streamData;
    const [first, ...rest] = typeof command === 'string' ? command.split(' ') : command;
    return new Promise((resolve, reject) => {
        const ls = spawn(first, rest, spawnOptions);
        let result = '';
        let error = '';
        if (!execOptions.inherit) {
            ls.stdout &&
                ls.stdout.on('data', (data) => {
                    result = `${result}${data}`;
                    streamData && console.log(`${data}`);
                });

            ls.stderr &&
                ls.stderr.on('data', (data) => {
                    result = `${result}${data}`;
                    error = `${error}${data}`;
                    streamData && console.log(`${data}`);
                });
        }

        ls.on('close', (code) => {
            if (code !== 0) {
                return reject({ code, message: error !== '' ? error : `process exited with error code ${code}!` });
            }
            if (!execOptions.inherit) {
                return resolve(result.split('\n').filter((line) => line !== ''));
            } else {
                return resolve(null);
            }
        });
    });
}

class DependencyGraph {
    /** @type {Record<string,DependencyGraphNode>} */
    nodes = {};

    constructor() {}

    /**
     * Add a PnpmDependency node to the graph
     * @param {PnpmDependency} pnpmNode
     */
    addNode(pnpmNode) {
        if (pnpmNode == null) {
            throw new Error('Cannot add null node');
        }
        const name = pnpmNode.name ?? pnpmNode.from ?? '';
        let graphNode = this.nodes[name];
        if (!graphNode) {
            const isLink = pnpmNode.version.startsWith('link:');
            const path = pnpmNode.path;
            if (isLink && path == null) {
                throw new Error(`dependency ${name} is a link but does not have path`);
            }
            const version = isLink ? readJsonFile(fsp.join(path ?? '', 'package.json')).version : pnpmNode.version;
            const isExternal = !isLink && !!pnpmNode.from;
            graphNode = { name, version, path, isExternal };
            this.nodes[name] = graphNode;
        }
        if (graphNode.dependencies == null && pnpmNode.dependencies != null) {
            const dependencies = pnpmNode.dependencies;
            if (dependencies) {
                graphNode.dependencies = [];
                Object.values(dependencies).map((dependency) => {
                    const dep = this.addNode(dependency);
                    graphNode.dependencies?.push(dep);
                });
            }
        }
        return graphNode;
    }

    /**
     *
     * @param {DependencyGraphNode} fromNode
     * @param {DependencyGraphNode} toNode
     */
    addEdge(fromNode, toNode) {
        if (!this.nodes[fromNode.name] || !this.nodes[toNode.name]) {
            throw new Error(`Both ${fromNode.name} and ${toNode.name} must be part of the graph`);
        }
        fromNode.dependencies = fromNode.dependencies ?? [];
        fromNode.dependencies.push(toNode);
    }

    /**
     * Gets the dependency name list
     * @param {string|DependencyGraphNode} node
     */
    getDependencyNames(node) {
        if (typeof node === 'string') {
            node = this.nodes[node];
        }
        return node?.dependencies?.map((n) => n.name) ?? [];
    }

    /**
     *
     * @param {string[]} packageNames
     * @param {DependencyFilter} filter
     * @param {Record<string,DependencyGraphNode>} [packageMap]
     * @param {string[]} [parentPath]
     * @returns {Record<string,DependencyGraphNode>}
     */
    getDeepDependencies(packageNames, filter, parentPath, packageMap) {
        const collector = packageMap ?? {};

        const path = parentPath || [];
        const indent = ' '.repeat(path.length * 2);

        const addDependency = (name) => {
            const found = collector[name];
            if (found) {
                console.info(`${indent}- ${name} (dedup)`);
                return;
            }

            const dep = this.nodes[name];
            if (dep) {
                if (path.includes(name)) {
                    console.error(
                        `${indent}** found circular dependency ${name} in package ${dep.name} path:[${path}] deps:[${collector}]`,
                    );
                    return;
                }
                if (path.length === 0) {
                    console.info(`\n${name}`);
                } else {
                    console.info(`${indent}- ${name}`);
                }

                collector[name] = dep;
                if (dep.dependencies) {
                    this.getDeepDependencies(this.getDependencyNames(dep), filter, [...path, name], collector);
                }
            }
        };
        packageNames.filter(filter).forEach((name) => addDependency(name));

        return collector;
    }

    /**
     * Add transitive dependencies
     * @param {*} pnpmLockYaml
     * @param {DependencyFilter} filter
     */
    addTransitiveDependencies(pnpmLockYaml, filter) {
        const { packages: lockPackages = {}, snapshots } = pnpmLockYaml;
        const { nodes } = this;
        const getLockEntry = (name, version) => {
            const key = `${name}@${version}`;
            const snapshotKeys =
                snapshots && Object.keys(snapshots).filter((k) => k === key || k.startsWith(`${key}(`));
            const packageEntry = lockPackages?.[key];
            const snapshot = snapshotToPackageEntry(snapshots?.[snapshotKeys[0]]);
            return packageEntry ? { ...packageEntry, ...snapshot } : null;
        };

        const walkNodes = (node) => {
            const depName = node.name;
            const lockEntry = getLockEntry(depName, node.version);
            const lockEntryDeps = lockEntry?.dependencies;
            if (!lockEntryDeps) return;
            const transitiveNodes = Object.keys(lockEntryDeps)
                .filter(filter)
                .map((k) => {
                    let transitiveNode = nodes[k];
                    if (transitiveNode) {
                        return transitiveNode;
                    }
                    const version = lockEntryDeps[k];
                    // use from and not name because it is an external dependency
                    const /** @type {PnpmDependency} */ transitive = { from: k, version };
                    logStep(`Add transitive dependency of ${depName}: ${transitive.from}@${transitive.version}`);
                    transitiveNode = this.addNode(transitive);
                    this.addEdge(node, transitiveNode);
                    return transitiveNode;
                });
            transitiveNodes.forEach((n) => walkNodes(n));
        };

        Object.values(this.nodes)
            .filter((n) => filter(n.name))
            .forEach((d) => {
                walkNodes(d);
            });
    }
}

function snapshotToPackageEntry(snapshot) {
    if (!snapshot) return {};
    return {
        dependencies:
            snapshot.dependencies &&
            Object.fromEntries(Object.entries(snapshot.dependencies).map(([k, v]) => [k, v.split('(')[0]])),
        optionalDependencies:
            snapshot.optionalDependencies &&
            Object.fromEntries(Object.entries(snapshot.optionalDependencies).map(([k, v]) => [k, v.split('(')[0]])),
        transitivePeerDependencies: snapshot.transitivePeerDependencies,
    };
}
/**
 * Gets the dependency graph
 * @returns {Promise<DependencyGraph>}
 */
export async function getDependencyGraph() {
    logStep('Getting package graph info');
    const res = await exec('pnpm ls -r --prod --json', {
        inherit: false,
        streamData: false,
        logCommand: true,
    });
    /** @type {PnpmDependency[]} */
    const packageListDetails = JSON.parse((res || []).join(''));
    const graph = new DependencyGraph();
    packageListDetails.forEach((node) => graph.addNode(node));
    return graph;
}

/**
 * Reads a json file and return its content as a parsed json object
 *
 * @param {string} file
 */
export function readJsonFile(file) {
    return JSON.parse(fs.readFileSync(file, 'utf8'));
}

/**
 * Writes data in JSON format to the given file
 * @param {string} file
 * @param {*} data
 */
export function writeJsonFile(file, data) {
    fs.writeFileSync(file, `${JSON.stringify(data, null, 4)}\n`, 'utf8');
}

/**
 * Reads a yaml file and return its content as a parsed json object
 *
 * @param {string} file
 */
export async function readYamlFile(file) {
    const yaml = await import('js-yaml');
    return yaml.load(fs.readFileSync(file, 'utf8'));
}

/**
 * Writes a object to a yaml formatted file
 *
 * @param {string} file
 * @param {object} data
 * @returns {Promise<void>}
 */
async function writeYamlFile(file, data) {
    const yaml = await import('js-yaml');
    fs.writeFileSync(file, yaml.dump(data));
}

const expectedPnpmLockVersion = '9.0';

export async function fixPnpmLock(dir) {
    const pnpmLockFile = fsp.join(dir, 'pnpm-lock.yaml');
    const pnpmLockYaml = await readYamlFile(pnpmLockFile);
    const { importers } = pnpmLockYaml;

    if (pnpmLockYaml.lockfileVersion !== expectedPnpmLockVersion) {
        throw new Error(
            `wrong pnpm-lock version: got ${pnpmLockYaml.lockfileVersion}, expected ${expectedPnpmLockVersion}`,
        );
    }
    if (!importers) {
        throw new Error(`wrong pnpm-lock file '${pnpmLockFile}': no importers property found`);
    }
    Object.keys(importers).forEach((key) => {
        // importers that are not '.' are workspace dependencies we don't want to fix the final pnpm-lock file
        if (key !== '.') {
            delete importers[key];
        }
    });
    await writeYamlFile(pnpmLockFile, pnpmLockYaml);
}

export function fixPackageJson(targetPackageJsonFile) {
    const rootPackageJsonFile = fsp.join(rootDir, 'package.json');
    const rootPackageJson = readJsonFile(rootPackageJsonFile);
    const packageJson = readJsonFile(targetPackageJsonFile);
    fixPackageJsonContent(rootPackageJson, packageJson);
    writeJsonFile(targetPackageJsonFile, packageJson);
}

export function fixPackageJsonContent(rootPackageJson, packageJson) {
    packageJson.pnpm = rootPackageJson.pnpm ?? {};
    delete packageJson.overrides;
    // ensure we have the same package manager as the root package.json
    if (rootPackageJson.packageManager) {
        packageJson.packageManager = rootPackageJson.packageManager;
    }
    if (packageJson.pnpm.patchedDependencies) {
        packageJson.pnpm.patchedDependencies = Object.fromEntries(
            Object.entries(packageJson.pnpm.patchedDependencies).filter(([_k, v]) => !v.endsWith('.dev.patch')),
        );
    }
}

function findDependency(importers, name) {
    const importerKeys = Object.keys(importers).filter((key) => key !== '.');
    const importerKey = importerKeys.find((key) => {
        const importer = importers[key];
        return !!(importer.dependencies?.[name] ?? importer.devDependencies?.[name]);
    });
    if (!importerKey)
        throw new Error(
            `dependency ${name} not found in pnpm-lock.yaml, add it to relevant package.json or remove it from dependency list`,
        );
    return importers[importerKey].dependencies[name] ?? importers[importerKey].devDependencies[name];
}

export async function findDependencyVersionsFromLock(dependencyList) {
    const pnpmLock = await readYamlFile(fsp.join(rootDir, 'pnpm-lock.yaml'));

    const importers = pnpmLock.importers || {};
    return dependencyList.reduce((r, k) => {
        const dependency = findDependency(importers, k);
        if (dependency) {
            r[k] = dependency.specifier;
        }
        return r;
    }, {});
}
