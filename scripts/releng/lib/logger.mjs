// @ts-check

/**
 * @callback LogMessage
 * @param {string} name
 * @returns {void}
 *
 * @callback LogMessageWithPrefix
 * @param {string} name
 * @param {string} [prefix]
 * @returns {void}
 *
 * @callback LogMessageWithError
 * @param {string} message
 * @param {Error} [error]
 * @returns {void}
 *
 * @typedef Logger
 * @property {LogMessageWithPrefix} info
 * @property {LogMessage} success
 * @property {LogMessage} warn
 * @property {LogMessage} debug
 * @property {LogMessageWithError} error
 * @property {LogMessageWithError} fatal
 */

/** @type {Logger} */
export const logger = {
    info(message, prefix = '') {
        console.log(`${prefix} 📢 ${message}`);
    },
    success(message) {
        console.log(` ✅ ${message}`);
    },
    warn(message) {
        console.log(` ⚠️ ${message}`);
    },
    error(message, err) {
        console.error(` ❌ ${message}${err ? `: ${err.message}` : ''}`);
    },
    debug(message) {
        console.log(` 🐛 ${message}`);
    },
    fatal(message, err) {
        this.error(message, err);
        process.exit(1);
    },
};
