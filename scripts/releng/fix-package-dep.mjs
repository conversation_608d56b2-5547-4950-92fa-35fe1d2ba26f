import { existsSync, writeFileSync } from 'node:fs';
import fsp from 'node:path';
import { sync } from 'glob';
import _ from 'lodash';
import { readJsonFile } from './lib/utils.mjs';
import depcheck from 'depcheck';

const cwd = process.cwd();
const rootPackagePath = `${cwd}/package.json`;
const rootPackage = readJsonFile(rootPackagePath);

const depcheckOptions = {
    ignoreBinPackage: false, // ignore the packages with bin entry
    skipMissing: false, // skip calculation of missing dependencies
    ignorePatterns: [
        // files matching these patterns will be ignored
        'sandbox',
        'dist',
        'build',
        'bower_components',
    ],
    ignoreMatches: [
        // ignore dependencies that matches these globs
        'grunt-*',
    ],
    parsers: {
        // the target parsers
        '**/*.js': depcheck.parser.es6,
        '**/*.jsx': depcheck.parser.jsx,
        '**/*.ts': depcheck.parser.typescript,
        '**/*.tsx': depcheck.parser.typescript,
    },
    detectors: [
        // the target detectors
        depcheck.detector.requireCallExpression,
        depcheck.detector.importDeclaration,
    ],
    specials: [
        // the target special parsers
        depcheck.special.eslint,
        depcheck.special.webpack,
    ],
};

async function checkDeps(path, options) {
    console.log(`Checking dependencies in ${path}`);
    return depcheck(path, options).then((unused) => {
        unused.packageDir = path;
        unused.packagePath = `${path}/package.json`;
        return unused;
    });
}

function addDependencies(json, deps) {
    let dirty = false;
    Object.entries(deps.dependencies).forEach(([name, version]) => {
        if (json.dependencies?.[name] === version) {
            dirty = true;
            return;
        }
        if (json.devDependencies?.[name] === version) {
            console.warn(
                `  Package '${json.name}' has '${name}' as a devDependency, but it should be a dependency. Consider moving it to dependencies.`,
            );
            dirty = true;
            return;
        }
        console.log(`  Adding '${name}' version '${version}' to '${json.name}' package`);
        json.dependencies = json.dependencies ?? {};
        json.dependencies[name] = version;
        const typeName = `@types/${name}`;
        const typeVersion = deps.devDependencies[typeName];
        if (typeVersion && json.devDependencies?.[typeName] !== typeVersion) {
            console.log(`  Adding '${typeName}' version '${typeVersion}' to devDependencies of '${json.name}' package`);
            json.devDependencies = json.devDependencies || {};
            json.devDependencies[typeName] = typeVersion;
        }
        sortDependencies(json);
        sortDependencies(json, 'devDependencies');
        dirty = true;
    });
    return dirty;
}

function sortDependencies(json, depsKey = 'dependencies') {
    const deps = json[depsKey];
    if (!deps) {
        return;
    }
    json[depsKey] = Object.keys(deps)
        .sort()
        .reduce((acc, key) => {
            acc[key] = deps[key];
            return acc;
        }, {});
}

if (!rootPackage.workspaces?.length) {
    console.log(
        'This monorepo only supports the use of pnpm workspaces, thus the package.json must include a "workspaces" property',
    );
    process.exit(1);
}

const packageGlobs = rootPackage.workspaces;

const packages = _.flatten(packageGlobs.map((pat) => sync(pat)))
    .filter((f) => /* /^(platform|services)\//.test(f) && */ !f.endsWith('/api'))
    .map((f) => `${cwd}/${f}/package.json`)
    .filter((f) => existsSync(f))
    .map((f) => ({ path: f, json: readJsonFile(f) }));

const [depName] = process.argv.slice(2);
const depTypeName = `@types/${depName}`;

const rootDepVersion = rootPackage.dependencies?.[depName] ?? rootPackage.devDependencies?.[depName];
const rootTypeDepVersion = rootPackage.devDependencies[depTypeName];

const depVersion =
    rootDepVersion ??
    (packages.find((p) => p.json.dependencies?.[depName] ?? p.json.devDependencies?.[depName]) ?? { json: {} }).json
        .dependencies[depName];
const typeDepVersion =
    rootTypeDepVersion ??
    (packages.find((p) => p.json.devDependencies?.[depTypeName]) ?? { json: {} }).json.devDependencies?.[depTypeName];

let rootDirty = false;
console.log(`Remove '${depName}' in root package`);
// remove from root package.json
if (rootDepVersion) {
    delete rootPackage.dependencies[depName];
    delete rootPackage.devDependencies[depName];
    rootDirty = true;
}
if (rootTypeDepVersion) {
    console.log(`Remove '${depTypeName}' in root package`);
    delete rootPackage.devDependencies[depTypeName];
    rootDirty = true;
}
if (rootDirty) {
    writeFileSync(rootPackagePath, `${JSON.stringify(rootPackage, null, 4)}\n`);
}

const unusedEntries = [];
for (const { path } of packages) {
    unusedEntries.push(await checkDeps(fsp.dirname(path), depcheckOptions));
}
//  = await Promise.all(
//     packages.map(async ({ path }) => checkDeps(fsp.dirname(path), depcheckOptions)),
// );

console.log(`Processing ${unusedEntries.length} packages for missing dependencies`);
unusedEntries.forEach((unused) => {
    if (unused.missing[depName]) {
        const { json } = packages.find((p) => p.path === unused.packagePath);
        console.log(`Missing ${depName} in ${json.name}`);
        if (
            addDependencies(json, {
                dependencies: { [depName]: depVersion },
                devDependencies: { [depTypeName]: typeDepVersion },
            })
        ) {
            console.log(
                `  Writing '${json.name}' package`,
                json.dependencies[depName],
                json.devDependencies[depTypeName],
            );
            writeFileSync(unused.packagePath, `${JSON.stringify(json, null, 4)}\n`);
        }
    }
});
