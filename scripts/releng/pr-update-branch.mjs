// @ts-check
import { logger } from './lib/logger.mjs';
import { GithubApi, AzureApi } from './lib/api/index.mjs';

/** @typedef {import('./lib/cmd-args.mjs').CommandArgs} CommandArgs */

/** @type {CommandArgs} */
import { default as cargs } from './lib/cmd-args.mjs';

const gh = new GithubApi(cargs.repository || 'Sage-ERP-X3/xtrem').setup();

function usage() {
    console.log(`
    usage:
        node ${process.argv[1]} [--pr <number>] [--all <branch>] [--repository <repository>]

    Options:
        --repository  (Optional) the repository to consider in the operation (default: Sage-ERP-X3/xtrem)
        --pr          Update branch of the given PR number
        --all         Update branch of all PR having <branch> as base branch

    `);
    process.exit(1);
}

/**
 *
 * @param {number} pr
 */
async function prUpdateBranch(pr) {
    const prNumber = +pr;
    if (!Number.isInteger(prNumber)) {
        logger.fatal(`--pr argument ${pr} must be an integer`);
    }
    const status = await gh.prUpdateBranch(pr);
    if (status.statusCode !== 200) {
        logger.fatal(`${status.statusMessage} ${status.url}`);
    }
}

/**
 *
 * @param {string} baseBranch
 */
async function allPrUpdateBranch(baseBranch) {
    if (typeof baseBranch !== 'string') {
        logger.fatal(`--all argument ${baseBranch} must be a base branch name`);
    }
    const pullrequests = await gh.getPullRequests(baseBranch);
    const statuses = await Promise.all(pullrequests.map((pr) => gh.prUpdateBranch(pr.number, pr)));
    statuses.forEach((status) => {
        if (status.statusCode !== 200) {
            logger.error(`${status.statusMessage} ${status.url}`);
        }
    });
}

if (cargs.pr) {
    await prUpdateBranch(cargs.pr);
} else if (cargs.all) {
    await allPrUpdateBranch(cargs.all);
} else {
    usage();
}
