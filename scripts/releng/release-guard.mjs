// @ts-check
import { logger } from './lib/logger.mjs';
import { GithubApi, AzureApi } from './lib/api/index.mjs';

/** @typedef {import('./lib/cmd-args.mjs').CommandArgs} CommandArgs */
/** @typedef {import('./lib/api/github-api.mjs').SanitizedPullRequest} SanitizedPullRequest */
/** @typedef {import('./lib/api/github-api.mjs').RepositoryContent} RepositoryContent */

/** @type {CommandArgs} */
import { default as cargs } from './lib/cmd-args.mjs';

/**
 * @typedef ReleaseGuardOptions
 * @property {string} baseBranch
 * @property {string} [repository]
 * @property {number} [definitionId]
 * @property {string} [context]
 * @property {boolean} [test]
 * @property {boolean} [dryRun]
 * @property {boolean|string} [renovate]
 *
 * @typedef UpdateStatusAndRenovateOptions
 * @property {string} buildStatus
 * @property {string} [baseBranch]
 * @property {SanitizedPullRequest[]} [branchPullrequests]
 * @property {string} [guardType]
 * @property {boolean} [allowAutoMerge]
 *
 * @typedef PackageJsonInfo
 * @property {Object} json
 * @property {string} commitDate
 * @property {number} commitTimestamp
 */

// see https://learn.microsoft.com/en-us/rest/api/azure/devops/build/builds/list?view=azure-devops-rest-7.0#buildstatus for build status below
const buildStatuses = {
    all: 'all',
    cancelling: 'cancelling',
    completed: 'completed',
    inProgress: 'inProgress',
    none: 'none',
    notStarted: 'notStarted',
    postponed: 'postponed',
};
const guardTypes = {
    release: 'release',
    cutOff: 'cut-off',
};
const validOperations = ['lock', 'unlock', 'refresh', 'freeze', 'unfreeze', 'list', 'outdated', 'protection'];
const gitHubApiCallsThreshold = 3500;

const gitCredential = {
    username: process.env.GITHUB_USERNAME,
    password: process.env.GITHUB_PASSWORD,
};

function usage() {
    console.log(`
    usage:
        node ${process.argv[1]} <lock|unlock|refresh|freeze|unfreeze|list|outdated> <baseBranch> [--pr prNumber] [--test] [--repository repository] [--definition-id definitionId] [--context context]

    Operations:
        lock          set the release-guard status to "in progress" on the all PR created on top of <baseBranch>
        unlock        set the release-guard status to "success" on the all PR created on top of <baseBranch>
        refresh       refresh the lock status all PR created on top of <baseBranch>
        freeze        set the cut-off status to "in progress" on all PR created on top of <baseBranch>
        unfreeze      set the cut-off status to "success" on all PR created on top of <baseBranch>
        list          list all PR created on top of <baseBranch>
        outdated      check if a PR is outdated compared to its base branch

    Arguments:
        baseBranch    The base branch to consider in the operation (ex: master, release/29.0, ...)

    Options:
        --repository     (Optional) the repository to consider in the operation (default: Sage-ERP-X3/xtrem)
        --definition-id  (Optional) the definitionId of the azure pipeline that run the release job (default: 1286)
        --context        (Optional) the context of the azure pipeline that run the release job (default: xtrem-release-guard)
        --renovate       (Optional) perform additional operation for renovate PRs. values: true|false|strict (default: false)
        --pr             (Optional) the PR number to check if it is outdated compared to its base branch
        --test           (Optional) use test status instead of the actual one
        --dry-run        (Optional) just print the operation that would be done without actually doing it

    `);
    process.exit(1);
}

class ReleaseGuard {
    #gh;
    #azure;
    #baseBranch;
    #repository;
    #definitionId;
    #options;
    #releaseGuardContext;
    #cutOffGuardContext;

    /**
     * Create a new ReleaseGuard instance
     * @param {ReleaseGuardOptions} options
     */
    constructor(options) {
        const _context = options.context;

        this.#releaseGuardContext = _context || `xtrem-${guardTypes.release}-guard`;
        this.#cutOffGuardContext = _context || `xtrem-${guardTypes.cutOff}-guard`;

        this.#baseBranch = options.baseBranch;
        this.#repository = options.repository || 'Sage-ERP-X3/xtrem';
        this.#definitionId = +(options.definitionId || 1286);
        this.#options = options;
    }

    get baseBranch() {
        return this.#baseBranch;
    }

    get repository() {
        return this.#repository;
    }

    get definitionId() {
        return this.#definitionId;
    }

    get isTestMode() {
        return this.#options.test;
    }

    get isDryRun() {
        return this.#options.dryRun;
    }

    get gh() {
        if (!this.#gh) {
            this.#gh = new GithubApi(this.#repository, gitCredential, { dryRun: this.isDryRun }).setup();
        }
        return this.#gh;
    }

    get azure() {
        if (!this.#azure) {
            this.#azure = new AzureApi().setup();
        }
        return this.#azure;
    }

    /**
     * Set the status of the pull requests
     * @param {SanitizedPullRequest[]} pullrequests
     * @param {boolean} isInProgress
     * @param {string|null} targetUrl
     * @param {string} shortDescription
     * @param {string} baseBranch
     * @param {string} context
     * @returns {Promise<void>}
     * @memberof ReleaseGuard
     */
    async setPullRequestsGuard(pullrequests, isInProgress, targetUrl, shortDescription, baseBranch, context) {
        context = this.isTestMode ? `test-${context}` : context;
        const state = isInProgress ? 'pending' : 'success';
        // LATER: if we have too many errors with max statuses reached
        // const statuses = this.gh.getStatuses(pullrequests, { context });

        // const filteredPR = [];
        // const prCount = pullrequests.length;

        // for (let i = 0; i < prCount; i++) {
        //     if (statuses[i][0].state === state) {
        //         filteredPR.push(pullrequests[i]);
        //     }
        // }
        if (isInProgress) {
            this.gh.setStatuses(pullrequests, {
                state,
                target_url: targetUrl,
                description: `${this.isTestMode ? 'Test ' : ''}${shortDescription} is in progress on ${baseBranch}...`,
                context,
            });
            return;
        }

        const outdatedPullRequests = [];
        const upToDatePullRequests = [];
        const basePackageJsonInfo = await this.getBasePackageJsonInfo(baseBranch);

        for (const pr of pullrequests) {
            if (await this.isBranchOutdated(pr, basePackageJsonInfo)) {
                outdatedPullRequests.push(pr);
            } else {
                upToDatePullRequests.push(pr);
            }
        }

        this.gh.setStatuses(outdatedPullRequests, {
            state: 'pending',
            target_url: targetUrl,
            description: `${this.isTestMode ? 'Test ' : ''}The branch is outdated, please merge ${baseBranch}.`,
            context,
        });

        this.gh.setStatuses(upToDatePullRequests, {
            state: 'success',
            target_url: targetUrl,
            description: `${this.isTestMode ? 'Test ' : ''}${shortDescription} is not in progress on ${baseBranch}`,
            context,
        });

        return;
    }

    async setReleaseGuard(pullrequests, isInProgress, targetUrl, branchName) {
        return this.setPullRequestsGuard(
            pullrequests,
            isInProgress,
            targetUrl,
            'Release',
            branchName,
            this.#releaseGuardContext,
        );
    }

    async setCutOffGuard(pullrequests, isInProgress, branchName) {
        return this.setPullRequestsGuard(
            pullrequests,
            isInProgress,
            null,
            `Release ${guardTypes.cutOff}`,
            branchName,
            this.#cutOffGuardContext,
        );
    }

    async processRenovatePullRequests(pullrequests, isInProgress) {
        const prs = pullrequests.filter((pr) => pr.user === 'sage-erp-renovate[bot]' && !pr.labels.includes('failing'));
        if (prs.length > 0) {
            logger.info(`Found ${prs.length} renovate PRs without 'failing' label`);
            for (const pr of prs) {
                await this.reRunOrMerge(pr.number, isInProgress);
            }
        }
    }

    async reRunOrMerge(prNumber, isInProgress) {
        const pr = await this.gh.getPullRequest(prNumber, { pollMergeable: true });
        logger.info(`Try to re-run or merge renovate PR ${prNumber} ${pr.title}`);

        if (!pr.mergeable) {
            logger.warn(`PR ${pr.html_url} is not mergeable, state: ${pr.mergeable_state}`);
            return;
        }
        const checks = await this.gh.getCkeckRuns(pr.head.sha);
        const azureChecks = checks.check_runs.filter((c) => c.app.slug === 'azure-pipelines');
        logger.info(
            `Found ${azureChecks.length} Azure checks out of ${checks.total_count} total checks for PR ${pr.number} ${pr.html_url}`,
        );

        // if non of the azure checks have been started yet, we will try later
        if (azureChecks.length === 0) {
            logger.warn(`PR ${pr.number} ${pr.html_url} no Azure check has been started yet, try later`);
            return;
        }

        // if the PR has pending checks, we will try later
        if (azureChecks.some((c) => c.status !== 'completed')) {
            logger.warn(`PR ${pr.number} ${pr.html_url} has pending checks, cannot re-run now`);
            return;
        }
        const failedChecks = azureChecks.filter((c) => c.conclusion === 'failure');
        // no failed checks, we can merge except if the release is in progress
        if (failedChecks.length === 0) {
            if (isInProgress) {
                logger.warn(`Release is in progress, cannot merge PR ${pr.html_url}`);
                return;
            }
            logger.info(
                `PR ${pr.html_url} is mergeable with state ${pr.mergeable_state} and no failed checks, merging...`,
            );
            const resp = await this.gh.mergePullRequest(pr);
            if (resp.statusCode === 200) {
                logger.success(`PR ${pr.html_url} merged`);
            } else {
                logger.error(`PR ${pr.html_url} merge failed with status ${resp.status}`);
            }
            return;
        } else {
            // Re run only test failures
            const testFailedChecks = failedChecks.filter((c) => /(Integration smoke|Unit) test(s)?/i.test(c.name));
            if (testFailedChecks.length > 0) {
                await Promise.all(testFailedChecks.map((c) => this.gh.rerequestChecks(c)));
            }
        }
    }

    /**
     * Get the file content of the given path in the given branch
     * @param {string} path The path to the file
     * @param {string} branch The branch to get the file info from
     * @returns {Promise<RepositoryContent>} The file content
     */
    getBranchFileContent(path, branch) {
        return this.gh.getContents(path, branch);
    }

    /**
     * Get the commit list of the given path in the given branch
     * @param {string} path The path to the file
     * @param {string} branch The branch to get the file info from
     * @returns The commit list
     */
    getCommitList(path, branch) {
        return this.gh.getCommitList(path, branch, { author: '<EMAIL>', per_page: 10 });
    }

    /**
     * Get the package.json info of the given branch including the commit date of the last commit or the first commit that match the filter
     * @param {string} branch The branch to get the package.json info from
     */
    async getPackageJsonContent(branch) {
        const contents = await this.getBranchFileContent('package.json', branch);
        try {
            return JSON.parse(contents.content);
        } catch (e) {
            return { version: '0.0.0' };
        }
    }

    /**
     * Get the package.json info of the given branch including the commit date of the last bump version commit
     * @param {string} branch
     * @returns {Promise<PackageJsonInfo>}
     */
    async getBasePackageJsonInfo(branch) {
        const packageJsonContent = await this.getPackageJsonContent(branch);
        const commitDates = await this.getCommitDates('package.json', branch, (c) =>
            /^chore: bump (major|minor|patch) version$/.test(c.commit.message),
        );
        const packageJsonInfo = {
            json: packageJsonContent,
            commitDate: commitDates.commitDate,
            commitTimestamp: commitDates.commitTimestamp,
        };
        return packageJsonInfo;
    }

    /**
     * Get the commit date of the last commit or the first commit that match the filter
     * @param {string} path The path to the file
     * @param {string} branch The branch to get the file info from
     * @param {*} [filter] The filter to apply to the commits
     * @returns The commit dates
     */
    async getCommitDates(path, branch, filter) {
        const commits = await this.getCommitList(path, branch);
        const nullDate = {
            commitDate: '',
            commitTimestamp: 0,
        };
        if (commits.message) {
            logger.error(
                `No commit found for ${path} from ${branch}: ${commits.status} ${commits.message} - ${commits[0]}`,
            );
            return nullDate;
        }
        const match = filter ? commits.find(filter) : commits[0];
        const date = match?.commit?.author?.date;
        if (date == null) {
            return nullDate;
        }
        return {
            commitDate: date,
            commitTimestamp: Date.parse(date),
        };
    }

    /**
     * Check if the branch of the PR is outdated compared to the base branch
     * The branch is considered outdated if the major version of the package.json is different
     * or the commit date of the last commit is older than the base branch with a tolerance of 2 days
     * @param {SanitizedPullRequest} pr
     * @param {PackageJsonInfo} basePackageJsonInfo
     * @returns {Promise<boolean>}
     * @memberof ReleaseGuard
     */
    async isBranchOutdated(pr, basePackageJsonInfo) {
        const headBranch = pr.head.ref;

        try {
            // Get package.json info including content and commit date
            const headPackageJsonContent = await this.getPackageJsonContent(headBranch);

            // Compare the version fields
            const fullHeadVersion = headPackageJsonContent.version;
            const fullBaseVersion = basePackageJsonInfo.json.version;
            const baseVersion = fullBaseVersion.split('.');
            const headVersion = fullHeadVersion.split('.');

            console.log(`    Base branch version: '${fullBaseVersion}' vs. Head branch version: '${fullHeadVersion}'`);
            if (fullBaseVersion === fullHeadVersion) {
                console.log('    Version in base branch is the same as head branch.');
                return false;
            }
            // If the major version is different, the branch is outdated
            if (baseVersion[0] !== headVersion[0]) {
                console.log('    Version in base branch is different from head branch.');
                return true;
            }
            const baseDate = basePackageJsonInfo.commitDate;
            const baseTimestamp = basePackageJsonInfo.commitTimestamp;
            let headDate = '';
            // default tolerance of is 5 days, but it can be changed with the environment variable XTREM_RELEASE_GUARD_OUTDATED_DAYS
            const toleranceDays = +(process.env.XTREM_RELEASE_GUARD_OUTDATED_DAYS ?? '5');
            // If the tolerance is 0 or less, we apply a very high tolerance
            const tolerance = toleranceDays <= 0 ? Number.MAX_VALUE : toleranceDays * 24 * 60 * 60 * 1000;
            const prChecks = await this.gh.getCkeckRuns(pr.head.sha);
            const azureChecks = Array.isArray(prChecks.check_runs)
                ? prChecks.check_runs.filter(
                      (c) => c.app.slug === 'azure-pipelines' && /Checkout and install/i.test(c.name),
                  )
                : null;
            if (azureChecks == null || azureChecks.length === 0) {
                console.log(
                    `    No 'Checkout and install' Azure check found for PR ${pr.html_url}, use commit date ${headDate}`,
                );
            } else if (azureChecks.length > 1) {
                console.log(
                    `    Too many 'Checkout and install' Azure check found for PR ${pr.html_url}, use commit date ${headDate}`,
                );
            } else {
                headDate = azureChecks[0].started_at;
                console.log(`    PR ${pr.html_url} started at ${headDate}`);
            }
            if (headDate === '') {
                const commitDates = await this.getCommitDates('package.json', headBranch);
                headDate = commitDates.commitDate;
            }
            if (!headDate) {
                console.log(`    Cannot determine commit date for PR ${pr.html_url}, assume outdated`);
                return true;
            }

            const headTimestamp = Date.parse(headDate);
            const outdated = headTimestamp > baseTimestamp + tolerance;
            console.log(
                `    Base date: ${baseDate} (${baseTimestamp}) vs. Head date: ${headDate} (${headTimestamp}) ${outdated ? 'outdated' : 'up to date'} with a tolerance of ${toleranceDays} days`,
            );
            return outdated;
        } catch (error) {
            console.error('Error checking version difference:', error);
            process.exit(1);
        }
    }

    /**
     * Update the status of the release guard and renovate PRs
     * @param {UpdateStatusAndRenovateOptions} options
     * @returns {Promise<void>}
     * @memberof ReleaseGuard
     */
    async updateStatusAndRenovate({ buildStatus, guardType, baseBranch, branchPullrequests, allowAutoMerge }) {
        const branchName = baseBranch ?? this.#baseBranch;
        logger.info(`Update status from build status '${buildStatus}' for base branch ${branchName}`);
        let pullrequests = branchPullrequests != null ? branchPullrequests : await this.gh.getPullRequests(branchName);
        if (branchPullrequests == null) {
            console.log(JSON.stringify(pullrequests, null, 2));
            logger.success(`Found ${pullrequests.length} Pull Requests with base branch ${branchName}`);
        }
        if (this.#options.renovate === 'strict') {
            pullrequests = pullrequests.filter((pr) => pr.user === 'sage-erp-renovate[bot]');
            logger.success(
                `Running in renovate strict mode, only ${pullrequests.length} Pull Requests will be processed`,
            );
        }

        const isInProgress = buildStatus.trim() === buildStatuses.inProgress;
        if (guardType === guardTypes.release) {
            const releaseUrl = `${AzureApi.getProjectUrl()}/_build?definitionId=${this.#definitionId}`;
            await this.setReleaseGuard(pullrequests, isInProgress, releaseUrl, branchName);
            if (this.#options.renovate && allowAutoMerge) {
                await this.processRenovatePullRequests(pullrequests, isInProgress);
            }
        } else if (guardType === guardTypes.cutOff) {
            await this.setCutOffGuard(pullrequests, isInProgress, branchName);
        }
    }

    /**
     * Update the status of the release guard and renovate PRs
     * @param {UpdateStatusAndRenovateOptions} options
     * @returns {Promise<void>}
     * @memberof ReleaseGuard
     */
    async updateReleaseGuardStatusAndRenovate({ buildStatus, baseBranch, branchPullrequests, allowAutoMerge }) {
        return this.updateStatusAndRenovate({
            buildStatus,
            guardType: guardTypes.release,
            baseBranch,
            branchPullrequests,
            allowAutoMerge,
        });
    }

    /**
     * Update the status of the cut-off guard
     * @param {string} buildStatus
     * @returns {Promise<void>}
     * @memberof ReleaseGuard
     */
    async updateReleaseCutOffStatus(buildStatus) {
        return this.updateStatusAndRenovate({
            buildStatus,
            guardType: guardTypes.cutOff,
        });
    }

    async checkGitHubRateLimit(maxUsed) {
        const rateLimit = await this.gh.getRateLimit();
        rateLimit.rate.reset *= 1000;
        if ((maxUsed | 0) > 0 && rateLimit.rate.used > maxUsed) {
            logger.error(
                `used api calls too high (> ${maxUsed}): ${rateLimit.rate.used} used, ${
                    rateLimit.rate.remaining
                } remaining, reset at ${new Date(rateLimit.rate.reset).toISOString()}`,
            );
            process.exit(1);
        }
        logger.success(
            `GitHub rate limit: ${rateLimit.rate.used} used, ${rateLimit.rate.remaining} remaining, reset at ${new Date(
                rateLimit.rate.reset,
            ).toISOString()}`,
        );
        return rateLimit;
    }

    async lock() {
        const branchName = this.#baseBranch;
        const builds = await this.azure.getBuilds({
            definitions: this.#definitionId,
            statusFilter: 'inProgress,notStarted',
            branchName,
        });
        if (builds?.count > 0) {
            const otherBuilds = builds.value.filter((b) => `${b.id}` !== process.env.BUILD_BUILDID);
            if (otherBuilds.length > 0) {
                const build = otherBuilds[0];
                logger.fatal(
                    `The build ${build.id} for '${branchName}' with status '${build.status}' is already in progress.
see: ${build._links?.web?.href}`,
                );
            }
        }
        await this.updateReleaseGuardStatusAndRenovate({
            buildStatus: buildStatuses.inProgress,
            baseBranch: branchName,
        });
    }

    async unlock() {
        await this.updateReleaseGuardStatusAndRenovate({ buildStatus: buildStatuses.completed });
    }

    async refreshStatusesAndRenovate() {
        const baseBranch = this.#baseBranch === '*' ? '' : this.#baseBranch;
        const pullrequests = await this.gh.getPullRequests(baseBranch);
        const pullrequestsByBase = {};

        for (const pr of pullrequests) {
            let prList = pullrequestsByBase[pr.base.ref];
            if (!prList) {
                prList = [];
                pullrequestsByBase[pr.base.ref] = prList;
            }
            prList.push(pr);
        }
        console.log(JSON.stringify(pullrequestsByBase, null, 2));
        logger.success(`Found ${pullrequests.length} Pull Requests with base branch ${this.#baseBranch}\n`);

        // get the last build of each base branch
        for (const branchName of Object.keys(pullrequestsByBase)) {
            const branchPullrequests = pullrequestsByBase[branchName];
            console.log('');
            logger.info(`${branchPullrequests.length} Pull Requests with base branch ${branchName}`);
            const builds = await this.azure.getBuilds({
                definitions: this.#definitionId,
                branchName,
            });
            const protection = await this.gh.getBranchProtection(branchName);
            const allowAutoMerge = protection.required_pull_request_reviews?.required_approving_review_count === 1;
            if (builds.count > 0) {
                for (const build of builds.value) {
                    await this.updateReleaseGuardStatusAndRenovate({
                        buildStatus: build.status,
                        baseBranch: branchName,
                        branchPullrequests,
                        allowAutoMerge,
                    });
                }
            } else {
                logger.warn(`No recent build found for '${branchName}', PR status will be updated to 'success'`);
                await this.updateReleaseGuardStatusAndRenovate({
                    buildStatus: buildStatuses.completed,
                    baseBranch: branchName,
                    branchPullrequests,
                    allowAutoMerge,
                });
            }
        }
    }

    async list() {
        const baseBranch = this.#baseBranch === '*' ? '' : this.#baseBranch;
        const pullrequests = await this.gh.getPullRequests(baseBranch);

        console.log(
            JSON.stringify(
                pullrequests.map((pr) => `${pr.html_url} (${pr.base.ref})`),
                null,
                2,
            ),
        );
        logger.success(`Found ${pullrequests.length} Pull Requests with base branch ${this.#baseBranch}\n`);
    }
}

// Main entry, runs the various operation:
// - filter PR per branch
// - check api rate limit before and after the operation
// - do not run the operation if we do not have enough remaining calls (used > 3000)
// - (NYI) get current statuses of the PR to not create a new status if it is already in the good state (we are limited to 100 statuses)
//   but this will consume more api calls so not sure it is really necessary with the previous filters in place
const run = async () => {
    // [lock|unlock|refresh|...] see usage
    const operation = cargs.$command;
    // [master, release/29.0, ...]
    const baseBranch = cargs.$unflagged[0];

    if (!validOperations.includes(operation)) {
        console.error(`unknown operation '${operation}', expecting ${validOperations}`);
        usage();
    }
    if (!baseBranch) {
        console.error('missing base branch filter, expecting master or release/*');
        usage();
    }
    const isTestMode = cargs.test;

    if (
        operation !== 'list' &&
        !(operation === 'refresh' && baseBranch === '*') &&
        !isTestMode &&
        !/^(master|release\/\d+\.\d+)$/.test(baseBranch)
    ) {
        console.error(`expecting master or release/* branch name, got ${baseBranch}`);
        usage();
    }

    const releaseGuard = new ReleaseGuard({ ...cargs, baseBranch });
    logger.info(
        `running ${operation} ${releaseGuard.baseBranch} ${releaseGuard.repository} ${releaseGuard.definitionId}`,
    );

    await releaseGuard.checkGitHubRateLimit(gitHubApiCallsThreshold);
    switch (operation) {
        case 'lock': {
            await releaseGuard.lock();
            break;
        }
        case 'unlock': {
            await releaseGuard.unlock();
            break;
        }

        // Get all PR, then get the status of the last build of the source branch
        case 'refresh': {
            await releaseGuard.refreshStatusesAndRenovate();
            break;
        }

        case 'freeze': {
            await releaseGuard.updateReleaseCutOffStatus(buildStatuses.inProgress);
            break;
        }

        case 'unfreeze': {
            await releaseGuard.updateReleaseCutOffStatus(buildStatuses.completed);
            break;
        }

        case 'list': {
            await releaseGuard.list();
            break;
        }

        case 'outdated': {
            const prNumber = cargs.pr;
            const basePackageJsonInfo = await releaseGuard.getBasePackageJsonInfo(baseBranch);

            const prList = prNumber
                ? [await releaseGuard.gh.getPullRequest(prNumber, { pollMergeable: true })]
                : await releaseGuard.gh.getPullRequests(baseBranch);
            for (const pr of prList) {
                const outdated = await releaseGuard.isBranchOutdated(pr, basePackageJsonInfo);
                if (outdated) {
                    logger.warn(`PR ${pr.number} '${pr.title}' is outdated`);
                } else {
                    logger.success(`PR ${pr.number} '${pr.title}' is up-to-date`);
                }
            }
            break;
        }

        case 'protection': {
            const protection = await releaseGuard.gh.getBranchProtection(baseBranch);
            const approvers = protection.required_pull_request_reviews?.required_approving_review_count;
            const inRelease = approvers !== 1;
            console.log(
                `\n${inRelease ? 'WARN: Currently in a' : 'INFO: Not in'} major release period! Approvers set to ${approvers}.\n`,
            );
            break;
        }

        default:
            // should not happen, we test at the beginning
            logger.fatal(`unexpected operation '${operation}'`);
    }
    await releaseGuard.checkGitHubRateLimit();
};

run().catch((e) => {
    console.error(e.stack);
    process.exit(1);
});
