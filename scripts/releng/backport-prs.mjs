// @ts-check
import { spawnSync } from 'node:child_process';
import os from 'node:os';
import path from 'node:path';
import fs from 'node:fs';
import { GithubApi } from './lib/api/index.mjs';

/** @typedef {import('./lib/cmd-args.mjs').CommandArgs} CommandArgs */

/** @type {CommandArgs} */
import { default as cargs } from './lib/cmd-args.mjs';

const repository = cargs.$unflagged[0] || 'Sage-ERP-X3/xtrem';

const gitCredential = {
    username: process.env.GITHUB_USERNAME || 'azureci',
    password: cargs.$unflagged[1] || process.env.GITHUB_PASSWORD,
};

const backportMap = {};

// initialize the backport map
for (let i = 21; i <= 99; i++) {
    backportMap[`auto-backport-${i}`] = `release/${i}.0`;
}

const runOnAzureCI = /^true$/i.test(process.env.TF_BUILD || '');

const gh = new GithubApi(repository, gitCredential).setup();

function gitCommand(args) {
    const p = spawnSync('git', args, { stdio: 'inherit' });
    return p.status === 0;
}

async function runInGroup(message, cb) {
    console.log(`${runOnAzureCI ? '##[group]' : ''}${message}`);
    await cb();
    console.log(`${runOnAzureCI ? '##[endgroup]' : ''}`);
}

function logError(msg) {
    console.error(`Error: ${msg}`);
    if (runOnAzureCI) {
        console.log(`##vso[task.logissue type=error]${msg}`);
    }
}

async function backport(targetBranch, pr, label, patchFilePath, prBody) {
    console.log(`\n\n`);
    const version = targetBranch.replace('release/', '');
    const prDiff = await gh.getPullRequestDiff(pr.url);
    const patchContent = prDiff.body;
    const newBranchName = `${label}/${pr.number}`;
    fs.writeFileSync(patchFilePath, patchContent, 'utf-8');
    // we need to stop on the first error to not create a branch that will never be merged
    const failed = [
        ['checkout', targetBranch],
        ['pull'],
        ['checkout', '-b', newBranchName],
        ['apply', patchFilePath],
        ['add', '.'],
        ['commit', '-m', pr.title],
        ['push', '--set-upstream', 'origin', newBranchName],
    ].some((args) => !gitCommand(args));
    if (failed) {
        // clean up for subsequent PR to create
        if (!gitCommand(['reset', '--hard', 'HEAD']) || !gitCommand(['clean', '-ffd'])) {
            console.error('Cannot reset and clean on failure');
            process.exit(1);
        }
        console.log(`\n\nFailed to patch #${pr.number} for ${targetBranch}, removing label ${label}\n\n`);
        // remove the label
        await gh.deleteIssueLabel(pr.number, label);
    } else {
        const prResponse = await gh.createPullRequest({
            base: targetBranch,
            head: newBranchName,
            title: pr.title,
            body: `${prBody}\n\n${pr.body}`,
        });
        const newPrNumber = prResponse.body?.number;
        await gh.addIssueLabels(newPrNumber, [`bot-${version}`]);
        console.log(`\n\n#${pr.number} is successfully backported for ${targetBranch}!\n\n`);
    }
}

const run = async () => {
    if (
        !gitCommand(['config', 'user.email', '<EMAIL>']) ||
        !gitCommand(['config', 'user.name', 'Sage Azure CI'])
    ) {
        console.error('Failed to set user config');
        process.exit(1);
    }

    const prNumber = cargs.prNumber;
    const includes = ['body'];
    const allPrListResult = prNumber
        ? [await gh.getPullRequest(prNumber, { includes })]
        : await gh.getPullRequests(null, { includes });

    if (!Array.isArray(allPrListResult)) {
        logError('Failed to get the list of Pull Requests');
        process.exit(1);
    }

    const filteredPrList = allPrListResult.filter(
        (pr) => pr.base.ref === 'master' && pr.labels.length > 0 && pr.labels.some((label) => !!backportMap[label]),
    );
    console.log(
        `Found ${filteredPrList.length} out of ${allPrListResult.length} pull requests having 'auto-backport-*' labels`,
    );
    for (const pr of filteredPrList) {
        if (pr.base.ref === 'master' && pr.labels.length > 0) {
            for (const label of pr.labels) {
                const targetBranch = backportMap[label];
                if (targetBranch) {
                    const prBody = `Automatic backport of ${pr.html_url}`;
                    const existingPr = filteredPrList.find(
                        (p) => p.base.ref === targetBranch && p.body?.startsWith(prBody),
                    );
                    if (!existingPr) {
                        const patchFilePath = path.resolve(os.tmpdir(), `${label}-${pr.number}.patch`);
                        await runInGroup(`Backporting #${pr.number} for ${targetBranch}`, () =>
                            backport(targetBranch, pr, label, patchFilePath, prBody),
                        );
                    } else {
                        console.log(`\n\nSkipping #${pr.number} for ${targetBranch}\n\n`);
                    }
                }
            }
        }
    }
};

run().catch((e) => {
    console.log(e);
    process.exit(1);
});
