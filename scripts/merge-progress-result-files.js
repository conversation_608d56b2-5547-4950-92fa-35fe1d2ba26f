'use strict';

const fs = require('node:fs');
const path = require('node:path');
const targetDir = process.argv[2];
let finalContent = [];

console.log(`targetDir ${targetDir}`);
fs.readdirSync(targetDir)
    .filter((f) => fs.lstatSync(path.join(targetDir, f)).isFile())
    .forEach((f) => {
        const filePath = path.join(targetDir, f);
        console.log(`${f} => ${filePath}`);
        const fileContent = JSON.parse(fs.readFileSync(filePath, 'utf8'));
        finalContent = finalContent.concat(fileContent);
        fs.unlinkSync(filePath);
    });

if (finalContent.length > 0) {
    const targetPath = path.join(targetDir, `merged-cucumber-progress-results.json`);
    const data = JSON.stringify(finalContent, null, 4);
    console.log(data);
    fs.writeFileSync(targetPath, data, 'utf-8');
}
