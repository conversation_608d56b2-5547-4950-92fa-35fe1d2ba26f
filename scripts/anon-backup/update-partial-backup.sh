#!/usr/bin/env bash

set -e

COLOR_BLUE='\033[0;34m'
COLOR_GREEN='\033[0;32m'
COLOR_GRAY='\033[0;90m'
COLOR_RED='\033[0;31m'
COLOR_ORANGE='\033[0;33m'
COLOR_DEFAULT='\033[0m'

error () {
    printf "\n%b${1}%b\n" >&2 "$COLOR_RED" "$COLOR_DEFAULT"
}

info () {
    printf "%b${1}%b\n" >&2 "$COLOR_BLUE" "$COLOR_DEFAULT"
}

verbose () {
    printf "%b${1}%b\n" >&2 "$COLOR_GRAY" "$COLOR_DEFAULT"
}

success () {
    printf "%b${1}%b\n" >&2 "$COLOR_GREEN" "$COLOR_DEFAULT"
}

warn () {
    printf "%b${1}%b\n" >&2 "$COLOR_ORANGE" "$COLOR_DEFAULT"
}

SCRIPT_DIR=$(dirname "$(readlink -f "${BASH_SOURCE[0]:-$0}")")

bucket_path="s3://xtrem-developers-utility/dump-anon/prod"
tmpdir="$1"
dry_run=${2:-false}

if [[ -z "$tmpdir" ]]; then
    echo "Usage: $0 <tmpdir> [dry_run]"
    exit 1
fi

updatePartialBackup() {
    local full_backup=$1
    local schema_name=$2
    local full_backup_date
    local partial_backup
    local partial_backup_date
    local regen_partial_backup
    local keep_list

    # The keep list is expected to be in the same directory as the script
    # and named after the full backup, with -keep.txt appended
    # e.g., if the full backup is anon-eu-prd-latest.zip,
    # the keep list will be scripts/anon-backup/anon-eu-prd-latest-keep.txt
    keep_list="$SCRIPT_DIR/${full_backup%.zip}-keep.txt"
    full_backup_date=$(grep "$full_backup" "$tmpdir/anon-backup-list.txt" | awk '{print $1 " " $2}')
    # Remove the .zip extension and replace it with -partial.zip
    partial_backup="${full_backup%.zip}-partial.zip"
    partial_backup_date=$(grep "${partial_backup}" "$tmpdir/anon-backup-list.txt" | awk '{print $1 " " $2}')
    regen_partial_backup=false
    if [[ -n "$partial_backup_date" ]]; then
        info "Partial backup for $full_backup exists"
        # Check if the partial backup is more recent than the full backup
        if [[ "$partial_backup_date" > "$full_backup_date" ]]; then
            verbose "Partial backup is more recent than the full backup"
        else
            verbose "Partial backup is older than the full backup"
            regen_partial_backup=true
        fi
    else
        warn "No partial backup for $full_backup found"
        regen_partial_backup=true
    fi
    if [[ "$regen_partial_backup" == true ]]; then
        if [[ ! -f "$keep_list" ]]; then
            error "Keep list $keep_list does not exist!"
            exit 1
        fi
        info "Regenerating partial backup for $full_backup"

        verbose "Downloading latest full backup: $full_backup"
        aws s3 cp "$bucket_path/$full_backup" "$tmpdir/$full_backup" --no-progress

        verbose "Unzipping latest full backup: $full_backup"
        unzip -d $"$tmpdir" "$tmpdir/$full_backup"

        verbose "Creating partial backup for $full_backup"
        node scripts/anon-backup/create-partial-backup.mjs "$tmpdir/${full_backup%.zip}.sql" "$keep_list"

        verbose "Zipping partial backup: $partial_backup"
        zip "$tmpdir/$partial_backup" "$tmpdir/${partial_backup%.zip}.sql"

        verbose "Testing partial backup: $partial_backup"
        XTREM_SCHEMA_NAME="$schema_name" pnpm --dir services/main/xtrem-services-main xtrem schema --restore-from-s3 "file://$tmpdir/$partial_backup"

        verbose "Uploading partial backup: $partial_backup"
        if [[ "${dry_run,,}" == "true" ]]; then
            echo "[Dry run] would upload $partial_backup to $bucket_path/$partial_backup"
            return
        fi
        verbose "Uploading partial backup to S3: $partial_backup"
        aws s3 cp "$tmpdir/$partial_backup" "$bucket_path/$partial_backup" --no-progress
        success "Partial backup $partial_backup uploaded successfully"
    fi
}

# Ensure the tmp directory exists
mkdir -p "$tmpdir"
aws s3 ls s3://xtrem-developers-utility/dump-anon/prod/ | grep -e 'anon-.*-prd-' > "$tmpdir/anon-backup-list.txt"

# Check if the partial backup exists and if the date is more recent than the latest full backup
# we need to ensure that the schema name is the same as the one used in the full backup
updatePartialBackup "anon-eu-prd-latest.zip" "sdmo"
