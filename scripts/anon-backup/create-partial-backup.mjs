import { createReadStream, createWriteStream, readFileSync } from 'fs';
import { Transform, pipeline } from 'stream';
import readline from 'readline';
import { styleText } from 'util';

if (process.argv.length < 4) {
    console.error('Usage: node pg-dump-filter.mjs <input-file> <tenant-ids-keep-file>');
    process.exit(1);
}

function timingInSeconds(t) {
    return (t / 1000).toFixed(2);
}

const log = {
    info: (message) => console.log(styleText('blue', message)),
    debug: (message) => console.log(styleText('cyan', message)),
    warn: (message) => console.warn(styleText('yellow', message)),
    error: (message) => console.error(styleText('red', message)),
    success: (message) => console.log(styleText('green', message)),
    verbose: (message) => console.log(styleText('gray', message)),
};

async function collectExcludedTenantIds(includes) {
    return new Promise((resolve, reject) => {
        const rl = readline.createInterface({
            input: createReadStream(inputFile),
            crlfDelay: Infinity,
        });

        const sysTenantCopyRegex = /^COPY sdmo\.sys_tenant FROM STDIN WITH CSV;/;
        const sysTenantCopyEndRegex = /^\\\./;
        let sysTenantCopyStarted = false;
        const filteredTenantIds = [];

        rl.on('line', (line) => {
            if (sysTenantCopyRegex.test(line)) {
                sysTenantCopyStarted = true;
                return; // Skip the COPY line
            }

            if (sysTenantCopyStarted) {
                if (sysTenantCopyEndRegex.test(line)) {
                    sysTenantCopyStarted = false;
                    rl.close();
                    return; // Skip the end of COPY line
                }

                const [id, tenantId, name] = line.split(',');
                if (!includes.includes(tenantId)) {
                    filteredTenantIds.push(tenantId);
                }
            }
        });

        rl.on('error', (err) => {
            reject(err);
        });

        rl.on('close', () => {
            if (sysTenantCopyStarted) {
                log.warn('Warning: sys_tenant COPY section did not end properly.');
            }
            resolve(filteredTenantIds);
        });
        rl.on('SIGINT', () => {
            log.error('Process interrupted. Exiting...');
            resolve(filteredTenantIds); // Resolve with the collected tenant IDs
        });
    });
}

function createTenantRegex(tenantIds) {
    const escapedIds = tenantIds.map((id) => id.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'));
    return new RegExp(`(${escapedIds.join('|')}),`);
}

async function createFilteredBackupFile(inputFile, includes) {
    log.info(`Creating filtered backup file from ${inputFile}...`);
    log.verbose(`Keeping tenant IDs:\n${includes.join(' ')}`);
    const t0 = Date.now();
    const excludes = await collectExcludedTenantIds(includes);
    const t1 = Date.now();
    log.verbose(`Excluded tenant IDs collection took ${timingInSeconds(t1 - t0)} seconds.`);
    if (excludes.length === 0) {
        console.log('No tenant IDs to exclude. Exiting.');
        return;
    }
    log.verbose(`Excluding tenant IDs:\n${excludes.join(' ')}`);
    const excludeTenantRegex = createTenantRegex(excludes);

    // Re-create the readline interface for the input file
    const rl = readline.createInterface({
        input: createReadStream(inputFile),
        crlfDelay: Infinity,
    });

    let quotes = 0;
    let continuing = false;

    // Transform stream to filter out lines containing tenant IDs
    const filterTransform = new Transform({
        writableObjectMode: true,
        readableObjectMode: false,
        transform(line, encoding, callback) {
            if (continuing) {
                quotes += line.split('"').length - 1;
                continuing = quotes % 2 !== 0;
                return callback();
            } else {
                quotes = 0;
            }
            // remove line that contains the tenantIds
            if (excludeTenantRegex.test(line)) {
                quotes += line.split('"').length - 1;
                continuing = quotes % 2 !== 0;
                return callback();
            }

            this.push(line + '\n');
            callback();
        },
    });

    const output = createWriteStream(outputFile);

    return new Promise((resolve, reject) => {
        output.on('error', (err) => {
            log.error('Error writing output file:', err);
            reject(err);
        });
        output.on('finish', () => {
            const t2 = Date.now();
            log.verbose(`Filtering took ${timingInSeconds(t2 - t1)} seconds.`);
            log.verbose(`Total time taken: ${timingInSeconds(t2 - t0)} seconds.`);
            log.info(`Output written to ${outputFile}`);
            resolve();
        });
        output.on('close', () => {
            log.verbose('Output stream closed.');
        });

        pipeline(rl, filterTransform, output, (err) => {
            if (err) {
                log.error('Filtering failed:', err);
            } else {
                log.success('Filtering succeeded.');
            }
        });
    });
}

const inputFile = process.argv[2];
const ext = inputFile.split('.').pop();
const outputFile = inputFile.replace(`.${ext}`, `-partial.${ext}`);
const keepListFile = process.argv[3];

const includeTenantIds = readFileSync(keepListFile, 'utf8')
    .split('\n')
    .map((id) => id.trim())
    .filter((id) => id.length > 0);

await createFilteredBackupFile(inputFile, includeTenantIds);
