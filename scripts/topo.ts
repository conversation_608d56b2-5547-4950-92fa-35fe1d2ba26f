import * as path from 'path';
import { exec } from './utils';

const asyncForEach = async (array: any[], callback: (element: any, index?: number, array?: any[]) => Promise<void>) => {
    for (let index = 0; index < array.length; index++) {
        await callback(array[index], index, array);
    }
};

const main = async () => {
    const [_, __, script] = process.argv;
    const orderedPackages = await exec(`pnpm run lerna list --toposort --loglevel error`, {
        inherit: false,
        streamData: false,
    });

    if (!orderedPackages) {
        return;
    }

    const failed: string[] = [];

    await asyncForEach(
        orderedPackages.filter(p => !p.startsWith('>')),
        async p => {
            const packageDir = path.resolve(path.join(__dirname, '..', p));
            console.log(`cd ${packageDir}; pnpm run ${script}`);
            try {
                await exec(`cd ${packageDir} && pnpm run ${script}`);
            } catch (err) {
                failed.push(packageDir);
            }
        },
    );
};

main();
