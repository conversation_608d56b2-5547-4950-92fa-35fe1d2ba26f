#!/usr/bin/env bash

#=========================================================================================
#
# WARNING: This is still work in progress and the script does not test a full release process
#
# To run this script you need to set the MXX_NEXUS_USER and MXX_NEXUS_PWD env var
# with the credential of a nexus user having write access the @sage npm registry
#=========================================================================================

# TODO: instead of using nexus, we can start a verdaccio server locally and keep the actual version number

REPO_ROOT_PATH=$(git rev-parse --show-toplevel)

cd "$REPO_ROOT_PATH"

if ! mxx -v >/dev/null; then
    pnpm install -g @sage/mxx
fi

echo -e "\n>>> mxx version is $(mxx -v)"

TMPDIR=$(mktemp -u)
mkdir -p "$TMPDIR"

while (( "$#" )); do
  case "$1" in
    --major)
        PUBLISH_MAJOR="$2"
        shift 2
      ;;
    --image-name)
        IMAGE_NAME="$2"
        shift 2
      ;;
    *)
      shift
      ;;
  esac
done

if ((PUBLISH_MAJOR < 90)); then
    echo -e "\n*** major cannot be lower than 90 ***"
    exit 1
fi

PUBLISH_MAJOR=${PUBLISH_MAJOR:="99"}
PUBLISH_VER=${PUBLISH_VER:="$PUBLISH_MAJOR.0.*"}

echo -e "\n>>> created tmp dir $TMPDIR"

cleanupAssets () {
    echo -e "\n>>> looking for xtrem-core packages with version '$PUBLISH_VER'"
    mxx nexus asset --name 'xtrem-core' --version "$PUBLISH_VER" --type npm > "$TMPDIR"/npm-asset-99.log

    if grep "No matching assets have been found" "$TMPDIR"/npm-asset-99.log >/dev/null; then
        echo "    - no asset found"
    else
        echo "    - found assets"
        echo "    - deleting them..."
        # we need to delete all 99.0 packages published before
        # unluckily we no longer have a common xtrem-* prefix
        mxx nexus asset --name 'xtrem-*' --version '99.0.*' --type npm --delete
        mxx nexus asset --name 'x3-*' --version '99.0.*' --type npm --delete
        mxx nexus asset --name 'shopfloor-*' --version '99.0.*' --type npm --delete
        mxx nexus asset --name 'sim-*' --version '99.0.*' --type npm --delete
    fi
}

cleanupAssets

# run version patch with a major test
pnpm run prepare:release:version patch --no-tag --test "$PUBLISH_MAJOR"

# publish to npm
PUB_VER=$(jq -r '.version' package.json)
echo -e "\n>>> publishing version '$PUB_VER' to npm"
pnpm --verbose run publish:from:package --ignore='@sage/xtrem-upgrade-test*' --ignore='@sage/*~*' --concurrency=2

# run generation of pnpm-lock for images
echo -e "\n>>> run generation of pnpm-lock for images"
pnpm run generate:docker:pnpm:lock:files

# TODO: build images
# cd docker/service-images
# IMAGE_NAME=xtrem TAG_NAME=test ../../scripts/docker/docker-build-image.sh

rm -rf "$TMPDIR"
