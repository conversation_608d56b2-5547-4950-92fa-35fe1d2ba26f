import { exec } from 'child_process';
import { closeSync, existsSync, openSync, readFileSync, writeFileSync, writeSync } from 'fs';

interface ProjectDetails {
    path: string;
    name: string;
    version: string;
}

interface CommitDetails {
    commitHash: string;
    commitDate: string;
    nameAndEmail: string;
    commitType: string;
    description: string;
    packageName?: string;
    prNumber?: string;
    jiraCodes?: string[];
}

interface ChangelogSection {
    features: CommitDetails[];
    bugs: CommitDetails[];
}

interface VersionedSection {
    section: ChangelogSection;
    versionCommit: VersionCommit;
}

interface HistoryChangelog {
    project: ProjectDetails;
    versions: VersionedSection[];
}

interface HistoryMap {
    [key: string]: HistoryChangelog;
}

interface VersionCommit {
    date: string;
    version: string;
    sha1: string;
}

/**
 * Run a command in the terminal and resolve it as a promise
 *
 * @param cmd the command to be executed
 * @returns Result of the command as a string
 */
function execPromise(cmd: string): Promise<string> {
    return new Promise((resolve, reject) => {
        // Allow 200 kb of buffer
        exec(cmd, { maxBuffer: 1024 * 200 }, (error, stdout, stderr) => {
            if (error) {
                reject(error);
            } else {
                resolve(stdout ? stdout : stderr);
            }
        });
    });
}

/**
 * Read the lerna.json file content in order to extract the projects names from the packages expression
 */
function readProjectsFromWorkspaces(): ProjectDetails[] {
    try {
        const mainPackageJson = JSON.parse(readFileSync('package.json', 'utf8'));
        const projectsLine: string | undefined = /{(.*?)}/.exec(mainPackageJson.workspaces)?.[1];
        // The extracted projects will look like this: platform, services, shopfloor, tools, x3, wms
        const projects: string[] = projectsLine?.split(',') || [];

        if (!projects.length) {
            console.log('No projects defined in the "workspaces" property of the main package.json file.');
        }

        // We read each project name from its package.json file (platform -> @sage/xtrem~platform)
        return projects.map((project: string): ProjectDetails => {
            const packageJson = JSON.parse(readFileSync(`${project}/package.json`, 'utf8'));
            return {
                path: project,
                name: packageJson.name,
                version: packageJson.version,
            };
        });
    } catch (e) {
        console.log(e);
        return [];
    }
}

/**
 * Create the message, with "Features" and "Bug Fixes", that will be added to the changelog.
 *
 * @param map contains lists with all the changes done for a section
 * @returns
 */
function createChangelogMessage(map: ChangelogSection[]): string {
    const lines: string[] = ['### Bug Fixes'];

    for (let i = 0; i < map.length; i++) {
        createChangelogMessageEntries(map[i].bugs, lines);
    }
    lines.push('\n### Features');

    for (let i = 0; i < map.length; i++) {
        createChangelogMessageEntries(map[i].features, lines);
    }

    lines.push('\n');
    return lines.join('\n');
}

function createChangelogMessageEntries(list: CommitDetails[], lines: string[]) {
    list.forEach((item: CommitDetails) => {
        lines.push(
            `*${item.packageName ? ` **${item.packageName}:**` : ''} ${item.description} ${
                item.jiraCodes?.length ? createChangelogMessageForJiraCodes(item.jiraCodes) : ' '
            } ([#${item.prNumber}](https://github.com/Sage-ERP-X3/xtrem/issues/${
                item.prNumber
            }))   ([${item.commitHash.substring(0, 7)}](https://github.com/Sage-ERP-X3/xtrem/commit/${
                item.commitHash
            }))`,
        );
    });
}

function createChangelogMessageForJiraCodes(jiraCodes: string[]) {
    let message = '(';

    for (let i = 0; i < jiraCodes.length; i++) {
        message += `[${jiraCodes[i]}](https://jira.sage.com/browse/${jiraCodes[i]})${
            i < jiraCodes.length - 1 ? '; ' : ''
        }`;
    }

    return `${message})`;
}

/**
 * Create the header on top of the changelog file
 *
 * @returns
 */
function getChangelogHeader(): string {
    return `# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.\n\n`;
}

/**
 * Get the title for the current changelog section
 *
 * @param project contains the name (platform, x3, services, etc.) and the latest version
 * @returns
 */
function createLatestVersionHeader(project: ProjectDetails): string {
    const date = getCurrentDateAsString();
    return `## [${project.version}](https://github.com/compare/...${project.name}@${project.version}) (${date})\n\n`;
}

/**
 * Get the title of a changelog version, with the provided version, date and project
 *
 * @param project contains the name (platform, x3, services, etc.) and the latest version
 * @param version contains the version of the created section
 * @param date contains the date displayed on the created section
 * @returns
 */
function createVersionHeader(project: ProjectDetails, version: string, date: string): string {
    const literalVersion = version === '' ? project.version : version;
    return `## [${literalVersion}](https://github.com/compare/...${project.name}@${literalVersion}) (${date})\n\n`;
}

async function writeChangelogToMasterFile(changelogSections: ChangelogSection[], version: string) {
    const filePath = 'CHANGELOG.md';
    const standardHeader = getChangelogHeader();
    let features: CommitDetails[] = [];
    let bugs: CommitDetails[] = [];

    for (let i = 0; i < changelogSections.length; i++) {
        features = features.concat(changelogSections[i].features);
        bugs = bugs.concat(changelogSections[i].bugs);
    }

    const newData = createChangelogMessage([{ bugs, features }]);
    const newVersionHeader = createLatestVersionHeader({
        name: 'xtrem-root',
        version,
        path: '',
    });
    let existingStringData: string | undefined;

    if (existsSync(filePath)) {
        existingStringData = readFileSync(filePath, 'utf-8');

        const logHeaderIndex = existingStringData.indexOf('##') - 1;
        existingStringData = existingStringData.substring(logHeaderIndex, existingStringData.length);
    } else {
        writeFileSync(filePath, '', 'utf-8');
    }

    const updatedOutput = openSync(filePath, 'w+');
    writeSync(updatedOutput, standardHeader);
    writeSync(updatedOutput, newVersionHeader);
    writeSync(updatedOutput, newData);
    existingStringData && writeSync(updatedOutput, existingStringData);
    closeSync(updatedOutput);
}

/**
 * Write the latest changelog section to it's own file
 *
 * @param map contains details about the commits, grouped in features and bug fixes
 * @param project contains the name (platform, x3, services, etc.) and the latest version
 */
async function writeChangelogToFile(map: ChangelogSection, project: ProjectDetails) {
    const standardHeader = getChangelogHeader();
    const newData = createChangelogMessage([map]);
    const newVersionHeader = createLatestVersionHeader(project);
    let existingStringData: string | undefined;

    if (existsSync(`${project.path}/CHANGELOG.md`)) {
        existingStringData = readFileSync(`${project.path}/CHANGELOG.md`, 'utf-8');

        const logHeaderIndex = existingStringData.indexOf('##') - 1;
        existingStringData = existingStringData.substring(logHeaderIndex, existingStringData.length);
    } else {
        writeFileSync(`${project.path}/CHANGELOG.md`, '', 'utf-8');
    }

    const updatedOutput = openSync(`${project.path}/CHANGELOG.md`, 'w+');
    writeSync(updatedOutput, standardHeader);
    writeSync(updatedOutput, newVersionHeader);
    writeSync(updatedOutput, newData);
    existingStringData && writeSync(updatedOutput, existingStringData);
    closeSync(updatedOutput);
}

async function writeHistoryChangelogToMasterFile(historyMap: HistoryMap) {
    const projectNames = Object.keys(historyMap);

    if (projectNames.length) {
        const standardHeader = getChangelogHeader();

        let newData = '';
        let existingStringData: string | undefined;

        if (existsSync('CHANGELOG.md')) {
            existingStringData = readFileSync('CHANGELOG.md', 'utf-8');
            const logHeaderIndex = existingStringData.indexOf('##');

            existingStringData = existingStringData.substring(logHeaderIndex, existingStringData.length);
        }

        const versions = historyMap[projectNames[0]].versions.reverse();

        for (let i = 0; i < versions.length; i++) {
            const version = versions[i].versionCommit.version;
            const date = versions[i].versionCommit.date;

            newData += createVersionHeader(
                {
                    name: 'xtrem-root',
                    path: '',
                    version,
                },
                version,
                date,
            );

            const lines: string[] = ['### Bug Fixes'];

            for (let j = 0; j < projectNames.length; j++) {
                const item: VersionedSection = historyMap[projectNames[j]].versions.reverse()[i];
                createChangelogMessageEntries(item.section.bugs, lines);
            }

            lines.push('\n### Features');

            for (let j = 0; j < projectNames.length; j++) {
                const item: VersionedSection = historyMap[projectNames[j]].versions.reverse()[i];
                createChangelogMessageEntries(item.section.features, lines);
            }

            lines.push('\n');
            newData += lines.join('\n');
        }

        const updatedOutput = openSync(`CHANGELOG.md`, 'w+');
        writeSync(updatedOutput, standardHeader);
        writeSync(updatedOutput, newData);
        existingStringData && writeSync(updatedOutput, existingStringData);
        closeSync(updatedOutput);
    }
}

/**
 * Write the entire (missing) history in the changelog file, with each version in it's own section
 *
 * @param historyChangelog contains history of the provided project, structured in versions
 */
async function writeHistoryChangelogToFile(historyChangelog: HistoryChangelog) {
    const standardHeader = getChangelogHeader();
    let newData = '';
    let existingStringData: string | undefined;

    if (existsSync(`${historyChangelog.project.path}/CHANGELOG.md`)) {
        existingStringData = readFileSync(`${historyChangelog.project.path}/CHANGELOG.md`, 'utf-8');
        const logHeaderIndex = existingStringData.indexOf('##');

        existingStringData = existingStringData.substring(logHeaderIndex, existingStringData.length);
    }

    historyChangelog.versions.reverse().forEach((item: VersionedSection) => {
        newData += createVersionHeader(historyChangelog.project, item.versionCommit.version, item.versionCommit.date);
        newData += createChangelogMessage([item.section]);
    });

    const updatedOutput = openSync(`${historyChangelog.project.path}/CHANGELOG.md`, 'w+');
    writeSync(updatedOutput, standardHeader);
    writeSync(updatedOutput, newData);
    existingStringData && writeSync(updatedOutput, existingStringData);
    closeSync(updatedOutput);
}

/**
 * Get the current date as string in the format YYYY-MM-DD
 *
 * @returns current date as string
 */
function getCurrentDateAsString(): string {
    return new Date().toLocaleString('fr-CA', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
    });
}

/**
 * Get the commit hash of the last bump patch.
 *
 * @returns the hash as Promise<string>
 */
function getLatestBumpHash(): Promise<string> {
    return execPromise(`git log -1 --pretty=format:'%h' --no-merges --grep "chore: bump patch version"`);
}

/**
 * @param list
 * @param commitHash
 * @param commitDate
 * @param nameAndEmail
 * @param commitType
 * @param packageName
 * @param description
 * @param prNumber
 */
function addToCommitDetailsList(
    list: CommitDetails[],
    commitHash: string,
    commitDate: string,
    nameAndEmail: string,
    commitType: string,
    description: string,
    packageName?: string,
    prNumber?: string,
    jiraCodes?: string[],
) {
    list.push({
        commitHash,
        commitDate,
        nameAndEmail,
        commitType,
        packageName,
        description,
        prNumber,
        jiraCodes,
    });
}

/**
 * Get all the tags with versions
 */
async function getAllVersions(startChangelogHash: string): Promise<VersionCommit[]> {
    try {
        const res = (
            await execPromise(
                `git log ${startChangelogHash}..HEAD --date=short --pretty=format:'%h <%ae> %s' --abbrev-commit --grep "chore: bump patch version" -p -- package.json`,
            )
        ).split(/\r?\n/);

        const versions = [];
        for (const line of res) {
            const matchSha1 = /([a-f0-9]{11}) <<EMAIL>>/.exec(line);
            if (matchSha1) {
                versions.unshift({ sha1: matchSha1[1], version: '', date: '' });
                continue;
            }
            const matchVersion = /\+\s*"version": "(\d+\.\d+\.\d+)"/.exec(line);
            if (matchVersion) {
                versions[0].version = matchVersion[1];
                continue;
            }

            const matchDate = /\+\s*"buildStamp": "(\d+-\d+-\d+)/.exec(line);
            if (matchDate) {
                versions[0].date = matchDate[1];
            }
        }

        return versions.reverse();
    } catch (e) {
        console.log(e);
        throw e;
    }
}

/**
 * Compare to versions, if the first parameter is greater than the 2nd one. Versions are compared by major, minor and patch
 *
 * @param version contains the version to be checked if it's greater
 * @param comparableVersion contains the version to be compared to
 * @returns
 */
function isVersionGreaterThan(version: string, comparableVersion: string): boolean {
    const versionKeys: string[] = version.split('.');
    const comparableVersionKeys: string[] = comparableVersion.split('.');

    if (+versionKeys[0] > +comparableVersionKeys[0]) {
        return true;
    } else if (+versionKeys[0] === +comparableVersionKeys[0]) {
        if (+versionKeys[1] > +comparableVersionKeys[1]) {
            return true;
        } else if (+versionKeys[1] === +comparableVersionKeys[1]) {
            return +versionKeys[2] > +comparableVersionKeys[2];
        } else {
            return false;
        }
    } else {
        return false;
    }
}

/**
 * Get a list of versions that are greater than the provided one, and existing on git
 *
 * @param startChangelogVersion contains the version to start from
 * @returns
 */
async function getVersionsSince(startChangelogVersion: string, startCommitHash: string): Promise<VersionCommit[]> {
    try {
        const allTags: VersionCommit[] = await getAllVersions(startCommitHash);
        const resultVersions: VersionCommit[] = [];

        for (let i = 0; i < allTags.length; i++) {
            if (allTags[i]) {
                if (isVersionGreaterThan(allTags[i].version, startChangelogVersion)) {
                    resultVersions.push(allTags[i]);
                }
            }
        }
        return resultVersions.sort((a: VersionCommit, b: VersionCommit) => {
            return isVersionGreaterThan(a.version, b.version) ? 1 : -1;
        });
    } catch (e) {
        throw e;
    }
}

/**
 * Check if the string provided is a valid version. Valid versions must have a major, minor and patch value. E.g. 24.0.7
 *
 * @param version to be validated
 * @returns
 */
function isValidVersion(version: string) {
    const keys: string[] = version.split('.');
    return keys.length === 3 && Number.isInteger(+keys[0]) && Number.isInteger(+keys[1]) && Number.isInteger(+keys[2]);
}

/**
 * Retrieve all the commits between 2 hashes
 *
 * @param project
 * @param versionedSection
 * @param startHash
 * @param endHash
 */
async function updateChangelogHistory(
    project: ProjectDetails,
    versionedSection: VersionedSection,
    startHash: string,
    endHash: string,
) {
    try {
        const latestCommits: string = await execPromise(
            `git log --no-merges --date=short --pretty=format:'%H [%cd] <%an %ae> %s' --abbrev-commit ${startHash}..${endHash} -- ${project.path}`,
        );
        const lines = latestCommits.split('\n');
        parseCommitLines(lines, versionedSection.section);
    } catch (e) {
        throw e;
    }
}

/**
 * Parse a commit line and organize it
 *
 * @param lines
 * @param map
 */
function parseCommitLines(lines: string[], map: ChangelogSection) {
    const jiraCodesRegexp = /(XT|X3)-\d+/g;

    lines.forEach((line) => {
        /**
         * Regexp to extract details from a commit message:
         * (^.{0,40})                   - first 40 characters are the full hash of the commit
         * (\d{4}[\-]\d{2}[\-]\d{2})    - commit date in the format YYYY-MM-DD
         * [\<](.*)[\>]                 - name and email
         * (.+?(?=\(|\:))               - commit type (feat and fix are the only ones acceptable)
         * (.+?)?                       - package name (optional)
         * (.+?(?=\(#|\n|$))            - commit description
         * (\d{4,5})?[\)                - PR number (optional)
         */
        const commitRegexp =
            /(^.{0,40})[ ][\[](\d{4}[\-]\d{2}[\-]\d{2})[\]][ ][\<](.*)[\>][ ](.+?(?=\(|\:))[\(]?(.+?)?[\)]?[\:][ ](.+?(?=\(#|\n|$))[ ]?[\(]?[#]?(\d{4,5})?[\)]?/.exec(
                line,
            );

        if (commitRegexp) {
            const commitHash: string = commitRegexp[1];
            const commitDate: string = commitRegexp[2];
            const nameAndEmail: string = commitRegexp[3];
            const commitType: string = commitRegexp[4];
            const packageName: string | undefined = commitRegexp[5];
            const description: string = commitRegexp[6].trim();
            const jiraCodes: string[] = [];
            const prNumber: string | undefined = commitRegexp[7];

            let jiraCodesMatch = jiraCodesRegexp.exec(description);
            while (jiraCodesMatch != null) {
                jiraCodes.push(jiraCodesMatch[0]);
                jiraCodesMatch = jiraCodesRegexp.exec(description);
            }

            switch (commitType.toLowerCase()) {
                case 'feat': {
                    // This is a feature
                    addToCommitDetailsList(
                        map.features,
                        commitHash,
                        commitDate,
                        nameAndEmail,
                        commitType,
                        description,
                        packageName,
                        prNumber,
                        jiraCodes,
                    );
                    break;
                }
                case 'fix': {
                    // This is a bug fix
                    addToCommitDetailsList(
                        map.bugs,
                        commitHash,
                        commitDate,
                        nameAndEmail,
                        commitType,
                        description,
                        packageName,
                        prNumber,
                        jiraCodes,
                    );
                    break;
                }
                default: {
                    // Other types available that are excluded: ci, refactor, chore, docs, test, perf
                    console.log(`Commit type ${commitType} ignored from changelog.`);
                }
            }
        }
    });
}

/**
 * Update the projects' changelog.md file
 */
export async function updateLernaProjectsChangelog() {
    try {
        const projects = readProjectsFromWorkspaces();
        const prevBumpHash = await getLatestBumpHash();
        const totalLogs: ChangelogSection[] = [];

        for (let i = 0; i < projects.length; i++) {
            const project = projects[i];
            const map: ChangelogSection = { features: [], bugs: [] };
            totalLogs.push(map);
            const latestCommits: string = await execPromise(
                `git log --no-merges --date=short --pretty=format:'%H [%cd] <%an %ae> %s' --abbrev-commit ${prevBumpHash}..HEAD -- ${project.path}`,
            );

            const lines = latestCommits.split('\n');

            parseCommitLines(lines, map);
            await writeChangelogToFile(map, project);
        }

        if (projects.length) {
            await writeChangelogToMasterFile(totalLogs, projects[0].version);
        }
    } catch (err) {
        console.log(`Error during changelog update:`, err);
        throw err;
    }
}

async function getCommitHashWithVersion(startChangelogVersion: string): string {
    const commitWithVersion: string = await execPromise(`git log -S${startChangelogVersion} -- package.json`);
    const regex = /commit\s([0-9a-f]+)/g;
    let match = regex.exec(commitWithVersion);
    let versionedCommit = '';

    while (match != null) {
        versionedCommit = match[1];
        match = regex.exec(commitWithVersion);
    }

    return versionedCommit;
}

/**
 * Update the changelog with all the versions, for each project, starting (but EXCLUDING) the provided version.
 *
 * @param startChangelogVersion the version to start generating the changelog, but it will NOT be included in the algorithm
 * @returns
 */
export async function updateLernaProjectsChangelogFromVersion(startChangelogVersion: string) {
    if (startChangelogVersion) {
        if (!isValidVersion(startChangelogVersion)) {
            console.log(`The provided version ${startChangelogVersion} is not valid. E.g. 24.0.7`);
            return;
        }

        const projects = readProjectsFromWorkspaces();
        const historyMap: HistoryMap = {};

        projects.forEach((project: ProjectDetails) => {
            historyMap[project.name] = {
                project: project,
                versions: [],
            };
        });

        try {
            const versionedCommit = getCommitHashWithVersion(startChangelogVersion);

            if (versionedCommit) {
                const startCommitHash = versionedCommit.split(' ')[0];
                const gapVersions: VersionCommit[] = await getVersionsSince(startChangelogVersion, startCommitHash);
                if (gapVersions.length) {
                    let index = 0;
                    for (const gapVersion of gapVersions) {
                        const prevVersionCommit: string = index === 0 ? startCommitHash : gapVersions[index - 1].sha1;

                        for (const project of projects) {
                            const versionedSection: VersionedSection = {
                                section: {
                                    bugs: [],
                                    features: [],
                                },
                                versionCommit: {
                                    ...gapVersion,
                                },
                            };
                            historyMap[project.name].versions.push(versionedSection);
                            await updateChangelogHistory(project, versionedSection, prevVersionCommit, gapVersion.sha1);
                        }

                        index++;
                    }

                    for (const projectName of projects) {
                        await writeHistoryChangelogToFile(historyMap[projectName.name]);
                    }

                    await writeHistoryChangelogToMasterFile(historyMap);
                } else {
                    console.log(`No commits have been found from the provided version ${startChangelogVersion}`);
                    return;
                }
            } else {
                console.log(`No commits have been found from the provided version ${startChangelogVersion}`);
                return;
            }
        } catch (e) {
            throw e;
        }
    }
}
