import * as chalk from 'chalk';
import * as fs from 'fs';
import * as path from 'path';
import * as semver from 'semver';
import { exec } from '../utils';

/**
 * Add an "empty" SQL file to jump from the previous to the new release.
 */

async function execGitCommand(cwd: string, command: string) {
    try {
        console.log(command);
        return await exec(command, { inherit: false, streamData: false, cwd: cwd });
    } catch (err) {
        console.log(`${command} failed with error: ${err.message}`);
    }
}

const quitWithError = (error: string): void => {
    console.error(`${chalk.red('   ❌    ${error}   ')}

${chalk.bold.blue('Usage: create-empty-sql-file [major | minor | patch] <service> <folder>')}

This scripts adds an "empty" SQL file to jump from the previous to the new release.

options:
    major | minor | patch :  kind of version change to apply
    <service>  : the service for which the SQL file will be generated (xtrem-services-main, xtrem-glossary, ...)
    <folder>   : the folder where the SQL file should be written
`);
    process.exit(1);
};

const main = async () => {
    const [_, __, _bumpKind, _fileName, _sqlDir] = process.argv;

    const bumpKinds = ['major', 'minor', 'patch'];

    if (!_bumpKind) {
        quitWithError(`Please define bump kind : ${bumpKinds.join('|')}`);
    }

    if (!bumpKinds.includes(_bumpKind)) {
        quitWithError(`${_bumpKind} is not a valid bump kind. Please select one in [${bumpKinds.join(', ')}]`);
    }

    if (_bumpKind !== 'major') return;

    const rootDir = path.join(__dirname, '..', '..');
    const cwd = path.join(rootDir, _sqlDir);

    // 1. Generate the file
    const files = await execGitCommand(cwd, 'git ls-files');
    if (files) {
        let fromVersion = '0.0.0';
        files.forEach((file) => {
            const matches = file.match(/-(\d+\.\d+\.\d+)\.json/);
            if (matches && matches.length >= 2) {
                const version = matches[1];
                if (semver.gt(version, fromVersion)) {
                    fromVersion = version;
                }
            }
        });
        const toVersion = `${1 + semver.major(fromVersion)}.0.0`;
        console.log('from version:', fromVersion);
        console.log('to version:', toVersion);
        const comment = `${_fileName}: manual empty file to jump from ${fromVersion} to ${toVersion}`;

        const fileName = `sqlCommands-sage-${_fileName}@${fromVersion}-${toVersion}.json`;

        fs.writeFileSync(
            path.join(cwd, fileName),
            JSON.stringify(
                {
                    fromVersion,
                    toVersion,
                    comment,
                    commands: [],
                },
                null,
                '\t',
            ),
            'utf8',
        );

        // 2. Add the file:
        await execGitCommand(rootDir, `git add ${path.join(_sqlDir, fileName)}`);

        // 3. Commit it
        await execGitCommand(rootDir, `git commit -am "${comment}"`);
    }
};

try {
    main();
} catch (e) {
    console.error('The creation of an empty sql file failed:', e.stack);
}
