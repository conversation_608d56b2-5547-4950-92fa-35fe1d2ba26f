import * as chalk from 'chalk';
import * as fs from 'node:fs';
import * as path from 'node:path';
import { asyncForEach, Dict, exec, Package, safeReadPackage } from '../utils';
import { updateLernaProjectsChangelog, updateLernaProjectsChangelogFromVersion } from './changelog';

const testPackageRegex = /\/(test|upgrade-test|examples)\//;

const args = process.argv.slice(2);
const cargs = args.reduce(
    (r, arg) => {
        if (/^\-\-\w/.test(arg)) {
            arg = arg.substring(2);
            const camelName = arg.replace(/-(\w)/, (m, p1) => p1.toUpperCase());
            r[camelName] = true;
            r.$currentOpt = camelName;
        } else if (r.$currentOpt) {
            r[r.$currentOpt] = arg;
            delete r.$currentOpt;
        } else {
            r.$main.push(arg);
        }
        return r;
    },
    { $main: [] } as any,
);

const bumpKinds = ['major', 'minor', 'patch'];

class VersionPatcher {
    failed: any[] = [];

    bumpKindIndex: number;

    testMajor?: number;

    constructor(bumpKind: string) {
        this.testMajor = +cargs.test;
        this.bumpKindIndex = bumpKinds.indexOf(bumpKind);
        if (this.bumpKindIndex < 0) {
            quitWithError(`${bumpKind} is not a valid bump kind. Please select one in [${bumpKinds.join(', ')}]`);
        }
    }

    async exec(command: string | string[], silent = false) {
        !silent && console.log(typeof command === 'string' ? command : command.join(' '));
        try {
            const rootDir = path.resolve(path.join(__dirname, '../..'));
            return await exec(command, { inherit: !silent, streamData: !silent, cwd: rootDir });
        } catch (err) {
            this.failed.push({ source: command, error: err.message });
        }
    }

    async updateVersion(pack: Package) {
        // Do not increment version of test and example packages, only fix their dependencies
        if (!pack.version || testPackageRegex.test(pack.path)) return;
        const { bumpKindIndex } = this;
        const newVersion = pack
            .version!.split('.')
            .map((p, i) => {
                if (this.testMajor && i === 0) {
                    return this.testMajor;
                }
                return i == bumpKindIndex ? +p + 1 : i > bumpKindIndex ? 0 : p;
            })
            .join('.');
        console.log(` - ${pack.name} ${pack.version} => ${newVersion} (${pack.path})`);
        pack.newVersion = newVersion;
        if (!/^@(\[|sage\/test-|sage\/xtrem~)/.test(pack.name) && !/\/test\//.test(pack.path)) {
            pack.tag = `${pack.name}@${pack.newVersion}`;
        }
    }

    async patchPackage(pack: Package, packages: Dict<Package>) {
        console.log(`Patching ${pack?.name}`);
        // replace version
        if (pack?.newVersion)
            pack.content = pack.content!.replace(/"version":\s+"\d+\.\d+\.\d+.*"/, `"version": "${pack.newVersion}"`);

        // replace date if present
        const today = new Date();
        pack.content = pack.content!.replace(/"buildStamp":\s+".*"/g, `"buildStamp": "${today.toISOString()}"`);

        fs.writeFileSync(
            pack.path,
            pack.content
                // replace dependencies
                .replace(/"@sage\/([\w-]+)":\s+"[\^~]?\d+\.\d+\.\d+.*"/g, (s, name) => {
                    const packName = `@sage/${name}`;
                    const newVersion = packages[packName]?.newVersion;
                    return newVersion ? `"@sage/${name}": "^${newVersion}"` : s;
                }),
            'utf8',
        );
    }

    async tagPackage(pack: Package) {
        if (pack.tag !== undefined) {
            await this.exec(['git', 'tag', '-af', pack.tag, '-m', `"chore create git tag ${pack.tag}"`]);
        }
    }
}

const fatal = (error: string): void => {
    console.error(
        `${chalk.red(`
   ❌    ${error}
`)}`,
    );
    process.exit(1);
};

const quitWithError = (error: string): never => {
    console.error(`${chalk.red(`   ❌    ${error}   `)}

${chalk.bold.blue(
    'Usage: version { major | minor | patch } [--no-commit] [--start-changelog-version version] [--test version]',
)}

This script bumps the version of package.json files

options:
    major | minor | patch :  kind of version change to apply
    --no-commit               : changes are not commited
    --start-changelog-version : start generating the changelog at the given version
    --test                    : set the major version number to use for testing the release
`);
    process.exit(1);
};

function getPackageMapFromArray(packages?: Package[]): Dict<Package> {
    return packages
        ? packages.reduce((result, pack) => {
              const currentPack = result[pack?.name];
              if (currentPack) {
                  fatal(`Duplicate package ${pack?.name}: Exists in ${pack?.path} and ${currentPack.path}`);
              }
              if (pack) {
                  result[pack.name] = pack;
              }
              return result;
          }, {} as Dict<Package>)
        : {};
}

/*
Version.ts performs the following operations:
    1. Get the list of package.json files stored in github the version of which has to be updated
    2. Compute packages' versions
    3. Replace "version" in package.json and version of @sage/(xtrem|eslint)-.* "dependencies" in package.json
    4. Update changelog files
    5. Commit the changes
*/
const main = async () => {
    const bumpKind = cargs.$main[0];
    const { noCommit, noTag, startChangelogVersion } = cargs;

    if (!bumpKind) {
        quitWithError(`Please define bump kind : ${bumpKinds.join('|')}`);
    }

    const patcher = new VersionPatcher(bumpKind);

    // 1. Get the list of package.json files stored in github the version of which has to be updated:
    const lernaOutput = (await patcher.exec(`pnpm run lerna ls --all --json`, true))?.slice(2);
    if (!lernaOutput) {
        quitWithError(`no output from lerna ls --json`);
    }
    const cwd = process.cwd();
    const lernaPackages = getPackageMapFromArray(
        (JSON.parse((lernaOutput || ['{}']).join('')) as { location: string; name: string }[]).map((entry) =>
            safeReadPackage(path.join(path.relative(cwd, entry.location), 'package.json')),
        ) as Package[],
    );

    const extraPaths = [
        'package.json',
        '*/package.json',
        'docker/*/package.json',
        'platform/front-end/xtrem-client/examples/*/package.json',
        'platform/cli/*/*/package.json',
        'platform/cli/*/resources/*/package.json',
        'platform/*/*/test/fixtures/*/package.json',
        // this list is empty - 'platform/*/*/test/fixtures/*/*/package.json',
        'platform/*/*/test/fixtures/*/*/*/package.json',
        'platform/*/*/test/fixtures/*/*/*/*/package.json',
    ];

    const pathsOutput = (await patcher.exec(`echo ${extraPaths.join(' ')}`, true))?.[0];
    console.log(pathsOutput);
    if (!pathsOutput) {
        quitWithError(`no output from echo`);
    }
    const extraPackages = getPackageMapFromArray(
        (pathsOutput || '')
            .split(' ')
            .filter((path) => !path.includes('/tmp/') && !path.includes('/node_modules/'))
            .map((path) => safeReadPackage(path))
            .filter((pack) => pack?.version) as Package[],
    );
    delete extraPackages['@sage/xtrem~scripts'];

    Object.keys(extraPackages).forEach((k) => {
        if (lernaPackages[k]) {
            if (lernaPackages[k].path !== extraPackages[k].path) {
                fatal(`Duplicate package ${k}: Exists in ${lernaPackages[k]?.path} and ${extraPackages[k].path}`);
            }
            delete extraPackages[k];
        }
    });

    const packages = { ...lernaPackages, ...extraPackages };

    // 2. Compute packages' versions:
    console.log('\nUpdate versions...');
    await asyncForEach(Object.values(packages), async (pack: Package) => await patcher.updateVersion(pack));

    // 3. Replace "version" in package.json and version of @sage/(xtrem|eslint)-.* "dependencies" in package.json
    console.log('\nReplace version in package dependencies...');
    await asyncForEach(Object.values(packages), async (pack: Package) => await patcher.patchPackage(pack, packages));

    // 4. Update changelog files
    if (!noCommit) {
        if (startChangelogVersion) {
            // A start version has been provided
            await updateLernaProjectsChangelogFromVersion(startChangelogVersion);
        } else {
            // No start version provided so updating only the latest bump
            await updateLernaProjectsChangelog();
        }
    }

    // 5. Commit the changes:
    if (noCommit) {
        console.log("Changes won't be commited");
        return;
    }
    await patcher.exec(['git', 'commit', '-am', `"chore: bump ${bumpKind} version"`]);

    // 6. Set tags on the commit for main packages:
    if (!noTag) {
        await asyncForEach(
            Object.values(lernaPackages).filter((pack: Package) => (pack as any)?.xtrem?.isMain),
            async (pack: Package) => await patcher.tagPackage(pack),
        );
    }
};

try {
    main();
} catch (e) {
    console.error('Bump failed:', e.stack);
    process.exit(1);
}
