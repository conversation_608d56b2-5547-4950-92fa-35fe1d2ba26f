// Note: even if parameter.skipPostInstallBuild is declared as a number,
// here, we get process.env.SKIP_POSTINSTALL_BUILD as a string
// So, we can't test with only 'if (process.env.SKIP_POSTINSTALL_BUILD)'
// because !!'0' === true. Instead, we have to test againt '1' (and even 1 as
// we don"t use '==' and not '===')
if (process.env.SKIP_POSTINSTALL_BUILD == '1') {
    console.log('Note: the build step has been skipped');
    process.exit(0);
} else {
    console.log("The following error is EXPECTED, DON'T WORRY OR REPORT IT!");
    process.exit(1);
}
