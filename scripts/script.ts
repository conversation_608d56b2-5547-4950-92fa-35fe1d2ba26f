import inquirer from 'inquirer';
import * as autocomplete from 'inquirer-autocomplete-prompt';
import * as checkBoxAutocomplete from 'inquirer-checkbox-autocomplete-prompt';
import { withUi } from './ui';
import { exec, getPackages, searchForAutocomplete } from './utils';

interface Answers {
    command: string;
    isParallel: string;
    scopes: string[];
}

const popularCommands = ['build', 'build:changed', 'clean', 'test', 'test:ci'];

const main = withUi(async ({ logSuccess, logFail, ui }) => {
    inquirer.registerPrompt('autocomplete', autocomplete);
    inquirer.registerPrompt('checkbox-autocomplete', checkBoxAutocomplete);
    inquirer.ui.BottomBar = ui as any;
    const packages = await getPackages();
    const answers: Answers = await inquirer.prompt<Answers>([
        {
            type: 'autocomplete',
            name: 'command',
            suggestOnly: true,
            source: searchForAutocomplete(popularCommands),
            pageSize: 15,
            message: 'Which command do you want to run?',
            transformer: (command: string) => command.toLowerCase().trim(),
            validate: (input: string) => {
                if (!input) {
                    return 'Please type the name of a command.';
                }
                const command = input.toLowerCase().trim();
                if (!command) {
                    return 'Please type the name of a command.';
                }
                if (command === 'add' || command === 'install') {
                    logFail("\nPlease run 'pnpm run add' instead!");
                    process.exit(0);
                }
                return true;
            },
        } as any,
        {
            type: 'confirm',
            name: 'isParallel',
            message: 'Do you want to run it in parallel?',
            default: false,
        },
        {
            type: 'checkbox-autocomplete',
            name: 'scopes',
            source: searchForAutocomplete(packages),
            pageSize: 15,
            message: 'Which Sage packages do you want to run it into?',
            choices: packages,
            validate: (input: any) => {
                if (!input || input.length === 0) {
                    return 'Please select one or more packages.';
                }
                return true;
            },
        } as any,
    ]);
    const { command, scopes, isParallel } = answers;
    const concurrencyOption = isParallel ? '--parallel' : '--concurrency=1 --stream';
    const scopesString = `--scope=@sage/${scopes.join(' --scope=@sage/')}`;
    try {
        await exec(`pnpm run lerna run ${command} ${scopesString} ${concurrencyOption}`);
        logSuccess('All done.');
    } catch (err) {
        logFail(`Command failed due to the following error: ${err.message}`);
    }
});

main();
