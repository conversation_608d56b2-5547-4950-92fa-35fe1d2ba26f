import * as fs from 'fs';
import * as path from 'path';

const args = process.argv.slice(2);
const mainArgs = args.filter((n) => !/^\-\-\w/.test(n));

const [logFile] = mainArgs;

const safeTrim = (item: string | undefined) => (item === undefined ? '' : item.trim());

const previousEvent = {
    event: 0,
    log: '',
    message: '',
};

function lineToJson(line: string, lineIdx: number) {
    if (!line || line.length == 0) return undefined;
    try {
        const items = line.split('|');
        const event =
            items.length >= 6
                ? {
                      event: Number(items[1]) || 0,
                      log: safeTrim(items[4]),
                      message: safeTrim(items[5]),
                  }
                : {
                      event: previousEvent.event,
                      log: previousEvent.log,
                      message: `${previousEvent.message} ${line.trimLeft()}`,
                  };
        previousEvent = event;
        return event;
    } catch (err) {
        throw new Error(`Could not parse [${lineIdx}] : ${line} ${err.message}`);
    }
}

if (!logFile) {
    console.log(`Usage: explain-sql-log {log filename} `);
    console.log(`\t {log filename}  the log file to explain`);
    process.exit(1);
}

const events = {};

const lines = fs.readFileSync(logFile).toString().split('\n');
lines
    .map((line, idx) => lineToJson(line, idx))
    .filter((json) => json?.log === 'sage/xtrem-core/sql')
    .forEach((json) => {
        if (json.message.startsWith('[postgres]') || json.message.includes('TRANSACTION')) {
            let sql = json.message;
            ['[postgres]', 'Reader', 'Execute'].forEach((key) => {
                if (sql.startsWith(key)) sql = sql.substring(1 + key.length);
            });
            sql = sql.replace(', args=[]', '');

            let table = '';
            const matchFrom = sql.match(/(?:FROM|INTO|UPDATE)\s[\w\d_]*.([\w\d_]*)\s/);
            if (matchFrom) {
                table = matchFrom[1];
            }

            const type = ['SELECT', 'INSERT', 'UPDATE', 'DELETE'].find((key) => sql.startsWith(key));

            events[json.event] = {
                event: json.event,
                time: 0,
                type,
                table,
                sql,
            };
        } else if (json.message.startsWith('Event #') && events[json.event] !== undefined) {
            const matchEvent = json.message.match(/\((\d+)/);
            if (matchEvent) {
                events[json.event].time = Number(matchEvent[1]);
            }
        }
    });

const sep = ';';

const outputLines = [];
Object.keys(events).forEach((event, idx) => {
    if (!idx) outputLines.push(Object.keys(events[event]).join(sep));
    outputLines.push(Object.values(events[event]).join(sep));
});

const csvFile = `${logFile.substring(0, logFile.length - path.extname(logFile).length)}-sql.csv`;
console.log(csvFile);

fs.writeFileSync(csvFile, outputLines.join('\n'));
