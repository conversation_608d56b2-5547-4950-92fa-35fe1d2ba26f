import fs from 'fs';
import * as glob from 'glob';
import path from 'path';

const foldersToSearch = ['platform', 'services', 'tools', 'x3-services', 'x3-connector', 'wh-services', 'shopfloor'];

const extract = () => {
    const translationFolder = path.resolve(process.cwd(), 'translations');

    if (fs.existsSync(translationFolder)) {
        fs.rmSync(translationFolder, { recursive: true, force: true });
    }

    fs.mkdirSync(translationFolder);

    for (const folder of foldersToSearch) {
        const folderDir = path.resolve(`${process.cwd()}/${folder}`);
        const i18nDirs = glob.sync(`${folderDir}/*/*/lib/i18n`).filter(fs.existsSync);
        for (const i18nDir of i18nDirs) {
            const pathSegments = i18nDir.split(path.sep);
            const packageName = pathSegments[pathSegments.length - 3];
            const baseFile = path.resolve(i18nDir, 'base.json');
            if (fs.existsSync(baseFile)) {
                const packageFile: any = {};
                const enUsFile = path.resolve(i18nDir, 'en-US.json');
                const baseContent = JSON.parse(fs.readFileSync(baseFile, 'utf-8'));
                const enUsContent = JSON.parse(fs.readFileSync(enUsFile, 'utf-8'));
                Object.keys(baseContent).forEach((key) => {
                    if (
                        key.startsWith('@sage/xtrem-show-case') ||
                        key.startsWith('@sage/xtrem-x3-show-case') ||
                        key.startsWith('@sage/xtrem-restaurant')
                    ) {
                        // Skip all show case modules
                        return;
                    }

                    if (!enUsContent[key]) {
                        console.log(`New key found ${key}`);
                        packageFile[`_${key}.comment`] = 'New entry.';
                    }
                    packageFile[key] = enUsContent[key] || baseContent[key];
                });

                if (Object.keys(packageFile).length > 0) {
                    fs.writeFileSync(
                        path.resolve(translationFolder, `${folder}_${packageName}_SOURCE.json`),
                        JSON.stringify(packageFile, null, 4),
                    );
                } else {
                    console.log(`No strings found in ${folder}/${packageName}`);
                }
            }
        }
    }
};

extract();
