import path from 'node:path';
import fs from 'fs/promises';
import _ from 'lodash';

const __dirname = import.meta.dirname;
const rootDir = path.join(__dirname, '..');
const schema = JSON.parse(await fs.readFile(path.join(rootDir, 'xtrem-config-json-schema.json')));

const builtinTypes = {
    TlsOptions: { link: 'https://nodejs.org/docs/latest-v20.x/api/tls.html#tlscreatesecurecontextoptions' },
    TlsConnectionOptions: {
        name: 'TlsOptions',
        link: 'https://nodejs.org/docs/latest-v20.x/api/tls.html#tlscreatesecurecontextoptions',
    },
    ArrayBuffer: { name: 'Buffer', link: 'https://nodejs.org/docs/latest-v20.x/api/buffer.html#buffer_class_buffer' },
    ArrayBufferLike: {
        name: 'Buffer',
        link: 'https://nodejs.org/docs/latest-v20.x/api/buffer.html#buffer_class_buffer',
    },
};

const markdown = jsonschema2md(schema);

await writeMarkdownFile(markdown);

function jsonschema2md(schema) {
    const sectionMap = {
        '#root': {
            name: 'xtrem-config.yml',
            description: 'The main config is a YAML file with the following properties.',
            properties: {},
        },
    };
    traverseJsonSchema(schema, sectionMap, '#root');

    return buildMarkdown(sectionMap);
}

async function writeMarkdownFile(markdown) {
    const docName = '6-xtrem-config-json-schema';
    const dest = path.join(
        rootDir,
        `documentation/platform/01-global-architecture-of-the-xtreem-project/${docName}.md`,
    );
    const schemaUrl = 'https://github.com/Sage-ERP-X3/xtrem/blob/master/xtrem-config-json-schema.json';
    console.log(`Writing xtrem config documentation to ${dest}`);

    const fullMarkdown = `PATH: XTREEM/01+Global+architecture+of+the+XtreeM+project/${docName.replace('-', '+')}

_Auto-generated doc from [JSON schema](${schemaUrl})_

${markdown}`;

    await fs.writeFile(dest, fullMarkdown, 'utf-8');
}

function getAnchorId(name) {
    return `${_.kebabCase(name)}-id`;
}

function buildMarkdown(sectionMap) {
    return Object.values(sectionMap)
        .map((section) => {
            const sectionAnchor = section.refAnchorId ? `<a id="${section.refAnchorId}"></a>\n` : '';
            const sectionTitle = `${sectionAnchor}## ${section.name}`;
            const description = section.description ? `${section.description}\n\n` : '';
            const properties = Object.values(section.properties)
                .map((prop) => {
                    const type = prop.link ? `[${prop.type}](${prop.link})` : `\`${prop.type}\``;
                    const inlineProperties = Object.values(prop.properties ?? {});
                    const mainDetails = `### ${prop.name}\n\ntype: ${type}\n\n${prop.description}\n`;
                    const subProperties =
                        inlineProperties.length > 0
                            ? inlineProperties
                                  .map((subProp) => {
                                      const subType = subProp.link
                                          ? `[${subProp.type}](${subProp.link})`
                                          : `\`${subProp.type}\``;
                                      return `\n#### ${subProp.name}\n\ntype: ${subType}\n\n${subProp.description}`;
                                  })
                                  .join('\n')
                            : '';
                    return `${mainDetails}${subProperties}`;
                })
                .join('\n');
            return `${sectionTitle}\n\n${description}${properties}\n`;
        })
        .join('\n\n');
}

function traverseJsonSchema(schema, sectionMap, sectionName) {
    const section = sectionMap[sectionName];
    traverseProperties(schema.definitions, schema.properties, sectionMap, section, []);
}

function traverseProperties(definitions, properties, sectionMap, section, propertyPath) {
    if (!properties) {
        return;
    }
    for (const [key, value] of Object.entries(properties)) {
        // console.log('[traverseProperties]', key, value.type, value.$ref);
        // console.log('    section:', section.name, section.type, section.ref);
        const prop = addMdProperty(definitions, section, key, value);
        // console.log('    prop:', prop);

        if (prop.ref != null) {
            const name = prop.ref;
            const ref = definitions[name];
            if (ref && sectionMap[name] == null) {
                sectionMap[name] = {
                    name,
                    description: ref.description ?? '',
                    properties: {},
                    refAnchorId: getAnchorId(name),
                };
                traverseProperties(definitions, ref.properties, sectionMap, sectionMap[name], []);
            }
        } else if (prop.type === 'Object') {
            prop.properties ??= {};
            traverseProperties(definitions, value.properties, sectionMap, prop, [...propertyPath, key]);
            // console.log('    ', prop);
        }
    }
}

function addMdProperty(definitions, md, key, value) {
    md.properties[key] ??= {};
    const prop = md.properties[key];
    prop.name = key;
    prop.description = value.description ?? '';
    if (value.$ref) {
        // remove '#/definitions/' prefix
        const ref = value.$ref.slice(14);
        prop.type = ref;
        const builtin = builtinTypes[ref];
        if (!builtin) {
            const dictType = /^Dict<(.+)>$/.exec(ref);
            if (dictType) {
                prop.type = `Record<string, ${dictType[1]}>`;
            } else {
                prop.ref = ref;
                prop.link = `#${getAnchorId(ref)}`;
            }
        } else {
            prop.type = builtin.name ?? ref;
            prop.link = builtin.link;
        }
        return prop;
    }
    prop.type = value.type === 'object' ? 'Object' : value.type;
    if (value.type === 'array') {
        const itemType = value.items.type;
        prop.ref = definitions[value.items.type] == null ? null : value.items.type;
        prop.type = `${itemType}[]`;
    }

    return prop;
}
