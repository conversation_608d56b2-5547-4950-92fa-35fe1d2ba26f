#!/usr/bin/env bash

target_file="./platform/system/xtrem-system/data/layers/setup/sys-changelog.csv"

if [ -e $target_file ]; then
    # Delete the file
    rm "$target_file"
fi

# Capture the output of the command into a variable
output=$(git log --pretty='format:%h####%s####%aI' -1500)

# print header
echo '"hash";"message";"change_date"' > $target_file

# Iterate through the variable line by line
while IFS= read -r line; do
    # Process each line and print the line index
    escaped_line=$(echo $line | sed 's/"/\""/g'| sed 's/####/\";"/g')
    echo "\"$escaped_line\"" >> $target_file
done <<< "$output"
