const https = require('https');

const repository = process.argv[2];
const buildStatus = process.argv[3];
const username = process.argv[4];
const password = process.argv[5];
const definitionId = process.argv[6];
const context = process.argv[7];
const isInProgress = buildStatus.trim() === 'inProgress';

console.log(buildStatus, isInProgress);

const token = Buffer.from(`${username}:${password}`, 'utf8').toString('base64');

const get = (url) =>
    new Promise((resolve, reject) => {
        https.get(
            url,
            {
                headers: {
                    'User-Agent': 'Azure DevOps Pipelines',
                    Authorization: `Basic ${token}`,
                },
            },
            (res) => {
                res.setEncoding('utf8');
                let body = '';

                res.on('data', (data) => {
                    body += data;
                });

                res.on('end', () => {
                    body = JSON.parse(body);
                    resolve(body);
                });

                res.on('error', reject);
            },
        );
    });

const post = (url, content) =>
    new Promise((resolve, reject) => {
        const postData = JSON.stringify(content);

        const options = {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': postData.length,
                'User-Agent': 'Azure DevOps Pipelines',
                Authorization: `Basic ${token}`,
            },
        };

        const request = https.request(url, options, (res) => {
            res.setEncoding('utf8');
            let body = '';

            res.on('data', (data) => {
                body += data;
            });

            res.on('end', () => {
                body = JSON.parse(body);
                resolve(body);
            });

            res.on('error', reject);
        });

        request.write(postData);
        request.end();
    });

const run = async () => {
    const prListResult = await get(`https://api.github.com/repos/${repository}/pulls?per_page=100`);
    console.log('********************************************************************************');
    if (!Array.isArray(prListResult)) {
        console.log('', prListResult);
        process.exit(1);
    }
    console.log('PRs list');
    // Warning: do not log the description of the PR (if the description contains some 'echo "##vso[...]...' they will be interpreted by Azure
    console.log(prListResult.map((pr) => pr.url).join('\n'));

    console.log('********************************************************************************');
    console.log('Set PRs statuses');

    for (const d of prListResult) {
        console.log(
            await post(`https://api.github.com/repos/${repository}/statuses/${d.head.sha}`, {
                state: isInProgress ? 'pending' : 'success',
                target_url: `https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=${definitionId}`,
                description: isInProgress ? 'Release is in progress...' : 'The release job is not in progress',
                context,
            }),
        );
    }
    console.log('********************************************************************************');
};

run().catch((e) => {
    console.log(e.stack);
    process.exit(1);
});
