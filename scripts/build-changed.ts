import { exec } from './utils';
import { withUi } from './ui';

const main = withUi(async ({ logSuccess, logFail }) => {
    let packages: string[] | null = null;
    try {
        packages = await exec(`pnpm run lerna changed --loglevel error`, {
            inherit: false,
            streamData: false,
        });
    } catch (err) {
        if (err.code === 1) {
            logSuccess('No packages to be built.');
            process.exit(0);
        }
        logFail(`Could not get the list of changed packages from Lerna: ${err}`);
        process.exit(1);
    }
    if (!packages) {
        logSuccess('No packages to be built.');
        process.exit(0);
    }
    packages = packages!.filter((line: string) => line.startsWith('@sage'));
    const scopesString = `--scope=${packages!.join(' --scope=')}`;
    await exec(`pnpm run lerna run build ${scopesString} --concurrency=1 --stream`);
    logSuccess('All done.');
});

main();
