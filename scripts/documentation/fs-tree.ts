import fs, { Stats } from 'fs';
import { minimatch } from 'minimatch';
import gpath from 'path';

/**
 * Transforms a path into a system independant path
 * @param  {string} path
 * @return {FsNode} the normalized path
 */
export function normalizePath(path: string): string {
    // Ensure forward slashes
    path = path.replace(/\\/g, '/');

    // Remove all surrounding quotes
    path = path.replace(/^["']+|["']+$/g, '');

    // Make Windows drive letters lower case
    return path.replace(/^([^:]+):\//, (_m, m1) => m1.toUpperCase() + ':/');
}

export enum FileType {
    DIRECTORY = 'directory',
    FILE = 'file',
}

export interface Options {
    startWith?: string[];
    exclude?: string[];
    extensions?: string[];
    folders?: string[];
    squash?: { level: number; folder: string };
    nodeFn?: (node: FsNode) => FsNode;
}

export interface FsNode {
    path: string;
    name: string;
    fullName: string;
    size: number;
    type: string;
    modulep: boolean;
    extension?: string;
    children?: FsNode[];
}

function safeReadDirSync(path: string): string[] | null {
    let dirData = [] as string[];
    try {
        dirData = fs.readdirSync(path);
    } catch (ex) {
        if (ex.code == 'EACCES' || ex.code == 'EPERM') {
            //User does not have permissions, ignore directory
            return null;
        } else throw ex;
    }
    return dirData;
}

/**
 * Collects the files and folders for a directory path into a Tree structure, subject
 * to the options supplied
 * @param  {string} path
 * @param  {Options} options
 * @return {FsNode} the tree root node
 */
export function folderTree(path: string, options: Options = {}): FsNode | null {
    return generateFolderTree(path, options, 0, '');
}

function generateFolderTree(path: string, options: Options, level: number, parentName: string): FsNode | null {
    const name = gpath.basename(path);
    const fullName = parentName ? parentName + '_' + name : name;
    path = normalizePath(path);
    let stats: Stats;
    let lstat: Stats;

    const childLevel = level + 1;

    try {
        stats = fs.statSync(path);
        lstat = fs.lstatSync(path);
    } catch (e) {
        return null;
    }

    const nodeFn = options.nodeFn ? options.nodeFn : (node: FsNode) => node;

    if (options.exclude && options.exclude.some((pattern) => minimatch(path, pattern))) return null;
    if (options.folders && level === 1 && !options.folders.includes(name)) return null;
    if (lstat.isSymbolicLink()) return null;

    let modulep = false;
    if (stats.isFile()) {
        const ext = gpath.extname(path).toLowerCase();

        // Skip if it does not match the extension regex
        if (options.extensions && !options.extensions.includes(ext)) return null;
        return nodeFn({
            path,
            name,
            fullName,
            size: stats.size,
            modulep,
            extension: ext,
            type: FileType.FILE,
        });
    }
    if (stats.isDirectory()) {
        if (options.squash && options.squash.level === childLevel) {
            const squashPath = normalizePath(gpath.join(path, options.squash.folder));
            if (fs.existsSync(squashPath)) path = squashPath;
            modulep = true;
        }
        let dirData = safeReadDirSync(path);
        if (dirData === null) return null;

        const children = dirData
            .map((child) => generateFolderTree(gpath.join(path, child), options, childLevel, fullName))
            .filter((e) => !!e) as FsNode[];

        if (children.length === 0) return null;

        return nodeFn({
            path,
            name,
            fullName,
            size: children.reduce((prev, cur) => prev + cur!.size, 0),
            type: FileType.DIRECTORY,
            modulep,
            children,
        });
    }
    return null;
}
