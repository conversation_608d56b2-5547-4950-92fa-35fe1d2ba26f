import axios from 'axios';
import * as crypto from 'crypto';
import * as fs from 'fs';
import * as glob from 'glob';
import * as path from 'path';
import * as showdown from 'showdown';

const FormData = require('form-data');
// literalMidWordUnderscores to stop interpreting underscores in the middle of words as <em> and <strong>
const converter = new showdown.Converter({ tables: true, literalMidWordUnderscores: true });

export const token = process.argv[3];

const authHeaders = {
    Authorization: `Bearer ${token}`,
};

export const confluenceApi = 'https://confluence.sage.com/rest/api';
const GITHUB = 'https://github.com/Sage-ERP-X3/xtrem/blob/master';

export interface ConfluencePage {
    id: string;
    spaceKey: string;
    title: string;
    version: number;
    versionHash: string;
    content?: string;
}

export interface DocumentationGeneratorOptions {
    isServicesDocumentation?: boolean;
}

export interface GeneratorState {
    created: number;
    updated: number;
    skipped: number;
    errors: number;
}
export class DocumentationGenerator {
    constructor(
        readonly globs: string[],
        readonly options: DocumentationGeneratorOptions,
    ) {}

    async processDocuments(): Promise<GeneratorState> {
        const documents = [] as string[];
        this.globs.forEach((g) => {
            glob.sync(g).forEach((file) => {
                // avoid duplicates
                if (!documents.includes(file)) {
                    documents.push(file);
                }
            });
        });

        const documentCount = documents.length;
        const state: GeneratorState = { created: 0, updated: 0, skipped: 0, errors: 0 };
        console.log(`Processing ${documentCount} documents`);
        await asyncForEach(documents.sort(), async (filePath, index) => {
            const md = new MarkdownDocument(filePath, index || 0, this.options);
            console.log(`Processing (${md.index + 1}/${documentCount}) ${filePath}...`);
            try {
                await md.processDocument(state);
            } catch (e) {
                state.errors++;
                console.error(`Error processing ${filePath}: ${e.message}`);
                if (e.response) {
                    console.log(e.response.data);
                    console.log('content:\n', md.content);
                } else {
                    // console.log(e);
                }
            }
        });
        console.log(
            `${state.created} pages created, ${state.updated} pages updated, ${state.skipped} skipped, ${state.errors} errors.`,
        );
        return state;
    }
}

export class MarkdownDocument {
    attachmentDir: string;
    spaceKey: string;
    title: string;
    parentPageKey: string;
    content: string;

    constructor(
        readonly filePath: string,
        readonly index: number,
        readonly options?: DocumentationGeneratorOptions,
    ) {
        this.attachmentDir = path.dirname(filePath) + '/assets/';
    }

    async processDocument(state: GeneratorState): Promise<void> {
        const { filePath } = this;
        const fileContent = fs.readFileSync(filePath, 'utf-8');
        const lines = fileContent.split('\n');
        const matchPath = /^\s*PATH:\s+(.+)$/.exec(lines[0]);
        if (!matchPath) {
            if (filePath.split('/').pop() === 'README.md') {
                state.skipped++;
                return;
            }
            throw new Error(`Couldn't find the PATH in ${filePath}`);
        }
        const pagePath = matchPath[1];
        const parts = pagePath.split('/');

        if (parts.length < 3) {
            throw new Error(`Invalid key entry for ${filePath}: ${pagePath} `);
        }

        this.spaceKey = parts[0];
        this.title = parts.pop()!;
        this.parentPageKey = parts.pop()!;

        const footnote = `\n<hr />*IT IS AUTOMATICALLY GENERATED PAGE BASED ON [THIS FILE](${GITHUB}/${filePath}). ALL MANUALLY CHANGES WILL BE DISCARDED.*`;
        const content = converter
            .makeHtml(lines.slice(1).join('\n') + footnote)
            .replace(/<br>/g, '<br/>')
            .replace(/<a href="(.*)">/g, (s, $1) => `<a href="${$1.replace(/&/g, '&amp;')}">`);
        this.content = content;

        await this.processSingleDocument(state);
    }

    async processSingleDocument(state: GeneratorState): Promise<void> {
        if (this.options?.isServicesDocumentation) {
            this.content = this.content.replace(/<package>/g, '&lt;package&gt;');
        }
        const { attachmentDir, spaceKey, parentPageKey, title, content } = this;
        const space = new ConfluenceSpace(spaceKey);
        const pageId = await space.getPageId(title);

        if (!pageId) {
            // This is a new page
            await space.createPage(state, parentPageKey, title, content, attachmentDir);
        } else {
            const currentPage = await space.getPageMeta(pageId);
            await space.updatePage(state, currentPage, content, attachmentDir);
        }
    }
}

export const processArgs = (): { rootDir: string; filter: string } => {
    return {
        rootDir: process.argv[2],
        filter: process.argv[4],
    };
};

const asyncForEach = async (array: any[], callback: (element: any, index?: number, array?: any[]) => Promise<void>) => {
    for (let index = 0; index < array.length; index++) {
        await callback(array[index], index, array);
    }
};

export const waiter = (timeout?: number) => new Promise<void>((resolve) => setTimeout(resolve, timeout || 1500));

async function withWaiter(body: () => Promise<any>, timeout?: number): Promise<any> {
    try {
        return await body();
    } finally {
        await waiter(timeout);
    }
}

export const makeContentUrl = (subPath: string) =>
    `${confluenceApi}/content${['?', '/', undefined].includes(subPath[0]) ? subPath : `/${subPath}`}`;

export class ConfluenceSpace {
    constructor(readonly spaceKey: string) {}

    async getPageId(title: string): Promise<string | null> {
        return await withWaiter(async () => {
            const { spaceKey } = this;
            const url = makeContentUrl('');
            console.log(`  getPageId ${url}?spaceKey=${spaceKey}&title=${title}`);

            const result = await axios({
                method: 'GET',
                headers: authHeaders,
                url,
                params: {
                    spaceKey,
                    title: title.replace(/\+/g, ' '),
                },
            });
            if (result.status === 200) {
                if (result.data.results.length > 0) {
                    console.log(`  => id=${result.data.results[0].id}`);
                    return result.data.results[0].id;
                } else {
                    console.log(`  => null`);
                    return null;
                }
            } else {
                throw new Error(`Couldn't find page id for ${spaceKey}/${title}`);
            }
        });
    }

    async getPageMeta(pageId: string): Promise<ConfluencePage> {
        return await withWaiter(async () => {
            const url = makeContentUrl(pageId);
            console.log(`  getPageMeta ${url}`);
            const result = await axios({
                method: 'GET',
                headers: authHeaders,
                url,
                params: {
                    expand: 'version,space',
                },
            });
            console.log(`  => version=${result.data.version?.number} hash=${result.data.version?.message}`);
            if (result.status === 200 && result.data.version?.number) {
                return {
                    id: result.data.id,
                    spaceKey: result.data.space.key,
                    title: result.data.title,
                    version: result.data.version.number,
                    versionHash: result.data.version.message,
                };
            } else {
                throw new Error(`Couldn't find page with id: ${pageId}`);
            }
        });
    }

    async createPage(
        state: GeneratorState,
        parentPageKey: string,
        title: string,
        content: string,
        attachmentDir: string,
    ) {
        console.log(`  createPage '${title}' in ${parentPageKey}`);
        const parentPageId = await this.getPageId(parentPageKey);
        if (!parentPageId) {
            throw new Error(`Parent page ${parentPageKey} couldn't be identified`);
        }

        const { spaceKey } = this;
        const requestBody: any = {
            type: 'page',
            title: title.split('+').join(' '),
            space: {
                key: spaceKey,
            },
            ancestors: [{ id: parentPageId }],
            body: {
                storage: {
                    value: content,
                    representation: 'storage',
                },
            },
        };

        // Send it as a POST request given that we create a new page
        const resp = await axios({
            method: 'POST',
            headers: authHeaders,

            url: makeContentUrl(''),
            data: requestBody,
        });
        // We need to introduce a timeout in order not to overflow confluence
        await waiter();

        const page = resp.data;
        const versionHash = crypto.createHash('sha1').update(content).digest('base64');
        if (fs.existsSync(attachmentDir + 'images'))
            await uploadAttachmentsNewPage(attachmentDir, 'images', page.id, requestBody, content, versionHash);
        if (fs.existsSync(attachmentDir + 'drawio'))
            await uploadAttachmentsNewPage(attachmentDir, 'drawio', page.id, requestBody, content, versionHash);
        state.created++;
        console.log(`  => Created '${title}' count=${state.created}`);
    }

    async updatePage(
        state: GeneratorState,
        currentPage: ConfluencePage,
        content: string,
        attachmentDir: string,
    ): Promise<void> {
        const versionHash = crypto.createHash('sha1').update(content).digest('base64');

        if (currentPage.versionHash === versionHash) {
            state.skipped++;
            return;
        }

        const pageId = currentPage.id;
        const requestBody: any = {
            type: 'page',
            title: currentPage.title,
            space: {
                key: currentPage.spaceKey,
            },
            id: pageId,
            version: {
                number: currentPage.version + 1,
                message: versionHash,
            },
            body: {
                storage: {
                    value: content,
                    representation: 'storage',
                },
            },
        };

        console.log(`  updatePage ${pageId} '${currentPage.title}'`);
        requestBody.body.storage.value = content
            .replace(/src=\"assets\/images/g, 'src="/download/attachments/' + pageId)
            .replace(/src=\"assets\/drawio/g, 'src="/download/attachments/' + pageId);

        // Send it as a PUT request given that we update an existing page
        await axios({
            method: 'PUT',
            headers: authHeaders,
            url: makeContentUrl(pageId),
            data: requestBody,
        });
        // We need to introduce a timeout in order not to overflow confluence
        await waiter();

        state.updated++;
        if (fs.existsSync(attachmentDir + 'images'))
            await uploadAttachmentsExistingPage(attachmentDir, 'images', pageId);
        if (fs.existsSync(attachmentDir + 'drawio'))
            await uploadAttachmentsExistingPage(attachmentDir, 'drawio', pageId);
        console.log(`  => Updated '${currentPage.title}' count=${state.updated}`);
    }
}

const getAttachmentId = async (url: string, fileName: string): Promise<any | false> => {
    const result = await axios({
        method: 'GET',
        headers: authHeaders,
        url: url + `?filename=${fileName}`,
    });
    if (result.status === 200) {
        // We need to introduce a timeout in order not to overflow confluence
        await waiter();
        if (result.data.results.length > 0) {
            return result.data.results[0];
        } else {
            return false;
        }
    } else {
        throw new Error(`Couldn't find attachment id for ${fileName}`);
    }
};

const uploadAttachmentsExistingPage = async (attachmentPath: string, folder: string, pageId: string) => {
    asyncForEach(fs.readdirSync(attachmentPath + folder), async (name: string) => {
        const attachment = await getAttachmentId(makeContentUrl(`${pageId}/child/attachment`), name);

        let formData = new FormData();
        let stream = fs.createReadStream(`${attachmentPath + folder}/${name}`);
        formData.append('file', stream);
        let formHeaders = formData.getHeaders();
        if (!attachment) {
            await axios({
                method: 'POST',
                headers: {
                    Accept: 'application/json',
                    'X-Atlassian-Token': 'nocheck',
                    ...authHeaders,
                    ...formHeaders,
                },
                url: makeContentUrl(`${pageId}/child/attachment`),
                data: formData,
            }).catch((error: any) => {
                console.log(error);
            });
            // We need to introduce a timeout in order not to overflow confluence
            await waiter(3000);
        } else {
            formData['version'] = {
                number: attachment.version + 1,
            };
            await axios({
                method: 'POST',
                headers: {
                    Accept: 'application/json',
                    'X-Atlassian-Token': 'nocheck',
                    ...authHeaders,
                    ...formHeaders,
                },
                url: makeContentUrl(`${pageId}/child/attachment/${attachment.id}/data`),
                data: formData,
            }).catch((error: any) => {
                console.log(error);
            });
            // We need to introduce a timeout in order not to overflow confluence
            await waiter();
        }
    });
};

const uploadAttachmentsNewPage = async (
    attachmentPath: string,
    folder: string,
    pageId: string,
    requestBody: any,
    content: any,
    versionHash: string,
) => {
    asyncForEach(fs.readdirSync(attachmentPath + folder), async (name: string) => {
        let formData = new FormData();
        let stream = fs.createReadStream(`${attachmentPath + folder}/${name}`);
        formData.append('file', stream);
        let formHeaders = formData.getHeaders();

        await axios({
            method: 'POST',
            headers: {
                Accept: 'application/json',
                'X-Atlassian-Token': 'nocheck',
                ...formHeaders,
                ...authHeaders,
            },
            url: makeContentUrl(`${pageId}/child/attachment`),
            data: formData,
        }).catch((error: any) => {
            console.log(error);
        });
        // We need to introduce a timeout in order not to overflow confluence
        await waiter(3000);
    });
    // then we have to update the link for all images
    requestBody.id = pageId;
    requestBody.version = {
        number: 2,
        message: versionHash,
    };

    requestBody.body.storage.value = content
        .replace(/src=\"assets\/images/g, 'src="/download/attachments/' + pageId)
        .replace(/src=\"assets\/drawio/g, 'src="/download/attachments/' + pageId);

    await axios({
        method: 'PUT',
        headers: authHeaders,
        url: makeContentUrl(pageId),
        data: requestBody,
    });
    // We need to introduce a timeout in order not to overflow confluence
    await waiter();
};
