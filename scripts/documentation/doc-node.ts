console.error(
    `
********************************************************************************
** This tool is broken and needs to be fixed!
** This is potentially because of obsolete dependencies like 'skyrta'
** All dependencies have been removed from the package.json in the meantime.
********************************************************************************
`,
);
process.exit(1);

import fs, { existsSync, mkdirSync, readFileSync } from 'fs';
import path, { dirname, join } from 'path';
import skyrta from 'skyrta';
import { Digraph, Edge, Node, Subgraph, toDot } from 'ts-graphviz';
import ts, { FileReference } from 'typescript';
import { FileType, FsNode, Options, folderTree, normalizePath } from './fs-tree';

/**
 * see documentation/platform/19-development-tools/4-package-dependencies.md
 */
function getImportsFromFile(file: string) {
    const fileInfo = ts.preProcessFile(readFileSync(file).toString());
    return fileInfo.importedFiles
        .map((importedFile: FileReference) => importedFile.fileName)
        .filter((x: string) => x.startsWith('.') || x.startsWith('@sage')) // only xtrem paths allowed
        .map((fileName: string) => {
            if (fileName.startsWith('.')) {
                return join(dirname(file), fileName);
            }
            const moduleName = fileName.substring(6);
            return DocNode.getModuleByName(moduleName);
        })
        .map((fileName: string) => {
            if (existsSync(`${fileName}.ts`)) {
                return `${fileName}.ts`;
            }
            if (existsSync(`${fileName}.tsx`)) {
                return `${fileName}.tsx`;
            }
            if (existsSync(`${fileName}`)) {
                return `${fileName}`;
            }
            return undefined;
        })
        .filter((e) => !!e)
        .map(normalizePath);
}

function removeDrive(path: string): string {
    return path.replace(/^([^:]+):/, '');
}

function header(): string {
    return `<!DOCTYPE html>
  <html>
  <head>
  <style>
  a {
    font-size: 13pt;
  }
  </style>
  </head>
  <body>`;
}

function footer(): string {
    return `
  </body>
  </html>`;
}

function htmlBreadcrumb(links: string): string {
    return `<div >
    ${links}
    </div>
    `;
}

function link(text: string, fullname: string) {
    return `<a href="${fullname}.html">${text}</a>`;
}

/**
 * Extends FsNode to create a hierarchy of modules (group of packages, packages and group of files)
 */
export class DocNode implements FsNode {
    private static nodes: Map<string, DocNode> = new Map();
    private static modules: Map<string, string> = new Map();
    path: string;
    name: string;
    fullName: string;
    size: number;
    type: string;
    modulep: boolean;
    extension?: string;
    children?: DocNode[];
    parent?: DocNode;
    links?: DocNode[];

    private constructor(node: FsNode) {
        this.path = node.path;
        this.name = node.name;
        this.fullName = node.fullName;
        this.size = node.size;
        this.type = node.type;
        this.extension = node.extension;
        this.modulep = node.modulep;
        this.children = node.children
            ?.map((child) => child as DocNode)
            .sort((node1, node2) => (node1.name > node2.name ? -1 : 1));
        this.children?.map((child) => (child.parent = this));
        DocNode.nodes.set(removeDrive(this.path), this);
        if (this.modulep) {
            DocNode.modules.set(this.name, this.path);
        }
    }

    private static create(node: FsNode): DocNode {
        return new DocNode(node);
    }

    private static getNodeByPath(path: string) {
        return DocNode.nodes.get(removeDrive(path));
    }

    /**
     * Gets a DocNode giving the name of a Sage package
     * @param  {string} name
     * @return {DocNode} return the selected node of undefined if not found
     */
    public static getModuleByName(name: string) {
        return DocNode.modules.get(name);
    }

    /**
     * Create a tree of modules with their dependencies
     * @param  {string} path
     * @param {Options} options to configure the tree generation
     * @return {DocNode} the tree root node
     */
    public static createTree(path: string, options: Options = {}): DocNode | null {
        const tree = folderTree(path, { ...options, nodeFn: DocNode.create }) as DocNode;
        tree.computeDependencies();
        return tree;
    }

    private getParentModule() {
        if (this.modulep) return this;
        return this.parent?.getParentModule();
    }

    private createLevelGraph(): Digraph {
        const graph = new Digraph();
        graph.attributes.node.set('shape', 'box');
        const subgraph = new Subgraph('cluster_' + this.name, {
            label: this.name,
        });
        subgraph.attributes.node.set('shape', 'box');
        graph.addSubgraph(subgraph);
        const nodeMap: Map<string, Node> = new Map();
        this.children?.forEach((child) => {
            const node1 = this.createGraphNode(child);
            subgraph.addNode(node1);
            nodeMap.set(child.fullName, node1);
            child.links?.forEach((link) => {
                if (!this.children?.includes(link) && !nodeMap.has(link.fullName)) {
                    const node2 = this.createGraphNode(link);
                    graph.addNode(node2);
                    nodeMap.set(link.fullName, node2);
                }
            });
        });
        this.children?.forEach((child) => {
            child.links?.forEach((link) => {
                const edge = new Edge([nodeMap.get(child.fullName)!, nodeMap.get(link.fullName)!]);
                if (!this.children?.includes(link)) {
                    edge.attributes.set('color', 'gray');
                    if (!link.modulep) edge.attributes.set('style', 'dashed');
                }
                graph.addEdge(edge);
            });
        });
        return graph;
    }

    private githubLink(path: string) {
        return path.replace(/(.*?)\/xtrem/, 'https://github.com/Sage-ERP-X3/xtrem/blob/master');
    }

    private createGraphNode(node: DocNode) {
        const graphNode = new Node(node.fullName, {
            label: node.name,
            href:
                node.name.endsWith('.ts') || node.name.endsWith('.tsx')
                    ? this.githubLink(node.path)
                    : node.fullName + '.html',
            tooltip: node.path,
        });
        if (node.modulep) {
            graphNode.attributes.set('color', 'blue');
        }
        return graphNode;
    }

    private generateDot(): string {
        return toDot(this.createLevelGraph());
    }
    private breadcrumb(): string {
        if (!this.parent) return link(this.name, 'index');
        return this.parent.breadcrumb() + ' / ' + link(this.name, this.fullName);
    }

    private generateFile(rootpath: string) {
        const name = this.parent ? this.fullName : 'index';
        const filename = path.join(rootpath, name + '.html');
        let writeStream = fs.createWriteStream(filename);
        writeStream.write(header());
        writeStream.write(htmlBreadcrumb(this.breadcrumb()));
        writeStream.write(skyrta.generate('dot', this.generateDot()).toEmbed());
        writeStream.write(footer());
        writeStream.end();
    }

    /**
     * Generate an html file for each node of the module tree
     * @param rootpath target folder for the generated files
     */
    public generateFiles(rootpath: string) {
        this.generateFile(rootpath);
        this.children?.forEach((child) => {
            if (child.children && child.children.length > 0) {
                child.generateFiles(rootpath);
            }
        });
    }

    private computeDependencies() {
        this.children?.forEach((child) => {
            if (child.type === FileType.FILE) {
                const imps = getImportsFromFile(child.path);
                const links = imps.map((imp) => DocNode.getNodeByPath(imp)).filter((e) => !!e) as DocNode[];
                const set = new Set<DocNode>();
                links.forEach((link) => {
                    if (this.children?.includes(link)) {
                        set.add(link);
                    } else {
                        const module = link.getParentModule();
                        if (module !== this.getParentModule()) {
                            set.add(link.getParentModule());
                        } else if (link.parent) {
                            set.add(link.parent);
                        }
                    }
                    child.links = Array.from(set);
                });
            } else {
                child.computeDependencies();
            }
        });
        const moduleSet = new Set<DocNode>();
        if (this.getParentModule()) {
            this.children?.forEach((child) => {
                child.links?.forEach((link) => {
                    if (link.modulep && link !== this) moduleSet.add(link);
                });
            });
        } else {
            this.children?.forEach((child) => {
                child.links?.forEach((link) => {
                    if (link.parent && link.parent !== this) moduleSet.add(link.parent);
                });
            });
        }
        this.links = Array.from(moduleSet);
    }
}

function generateDependencyDocumentation() {
    const outDir = process.argv[2];
    if (!existsSync(outDir)) {
        console.log(`Creating non existing target dir ${outDir}`);
        mkdirSync(outDir, { recursive: true });
    }
    const tree = DocNode.createTree(process.cwd(), {
        extensions: ['.ts', '.tsx'],
        exclude: [
            '**/node_modules/**',
            '**/bin/**',
            '**/build/**',
            '**/test/**',
            '**/*index.ts',
            '**/tests/**',
            '**/__tests__/**',
            '**/api/**',
            '**/upgrade-test/**',
            '**/*.d.ts',
            '**/functional-tests/**',
            '**/xtrem-services-main/**',
        ],
        squash: { level: 4, folder: 'lib' },
        folders: ['platform', 'services', 'tools', 'x3-services', 'wh-services'],
    });
    if (tree) {
        tree.generateFiles(outDir);
    }
    console.log('Files generated to ', process.argv[2]);
}
generateDependencyDocumentation();
