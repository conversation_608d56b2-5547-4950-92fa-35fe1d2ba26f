import { DocumentationGenerator, processArgs } from './documentation-utils';

const { rootDir, filter } = processArgs();

const updateDocumentation = async (): Promise<number> => {
    const isServicesDocumentation = /^documentation\/services\/?$/.test(filter);
    const globsFromArg = filter?.endsWith('.md')
        ? [filter]
        : filter && [`${filter.endsWith('/') ? filter.slice(0, -1) : filter}/**/*.md`];
    const globs = globsFromArg || [
        'documentation/platform/**/*.md',
        'platform/*/*/@(src|lib)/**/doc@(-*|).md',
        'platform/*/*/documentation/**/*.md',
        'x3-services/*/*/@(documentation)/**/*.md',
        'x3-services/platform/*/@(src|lib)/**/doc@(-*|).md',
    ];
    const generator = new DocumentationGenerator(globs, { isServicesDocumentation });
    const state = await generator.processDocuments();
    return state.errors;
};

process.chdir(rootDir);

(async () => {
    if ((await updateDocumentation()) > 0) {
        process.exit(1);
    }
})();
