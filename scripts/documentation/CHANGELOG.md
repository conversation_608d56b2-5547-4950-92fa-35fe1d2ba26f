# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [20.0.22](https://github.com/compare/...@sage/xtrem~scripts~documentation@20.0.22) (2022-06-14)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* package version number fix ([#7344](https://github.com/issues/7344))  ([517e460](https://github.com/commit/517e460cde4848f3559f14bfd79a77835cd78223))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* publishing of xtrem-reporting doc (XT-4758) ([#7268](https://github.com/issues/7268))  ([ef06b16](https://github.com/commit/ef06b16f52e8ec96099d8e3068b1727c05e77c41))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [20.0.21](https://github.com/compare/...@sage/xtrem~scripts~documentation@20.0.21) (2022-06-14)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* package version number fix ([#7344](https://github.com/issues/7344))  ([517e460](https://github.com/commit/517e460cde4848f3559f14bfd79a77835cd78223))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* publishing of xtrem-reporting doc (XT-4758) ([#7268](https://github.com/issues/7268))  ([ef06b16](https://github.com/commit/ef06b16f52e8ec96099d8e3068b1727c05e77c41))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [20.0.20](https://github.com/compare/...@sage/xtrem~scripts~documentation@20.0.20) (2022-06-13)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* package version number fix ([#7344](https://github.com/issues/7344))  ([517e460](https://github.com/commit/517e460cde4848f3559f14bfd79a77835cd78223))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* publishing of xtrem-reporting doc (XT-4758) ([#7268](https://github.com/issues/7268))  ([ef06b16](https://github.com/commit/ef06b16f52e8ec96099d8e3068b1727c05e77c41))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [20.0.19](https://github.com/compare/...@sage/xtrem~scripts~documentation@20.0.19) (2022-06-12)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* package version number fix ([#7344](https://github.com/issues/7344))  ([517e460](https://github.com/commit/517e460cde4848f3559f14bfd79a77835cd78223))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* publishing of xtrem-reporting doc (XT-4758) ([#7268](https://github.com/issues/7268))  ([ef06b16](https://github.com/commit/ef06b16f52e8ec96099d8e3068b1727c05e77c41))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [20.0.18](https://github.com/compare/...@sage/xtrem~scripts~documentation@20.0.18) (2022-06-11)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* package version number fix ([#7344](https://github.com/issues/7344))  ([517e460](https://github.com/commit/517e460cde4848f3559f14bfd79a77835cd78223))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* publishing of xtrem-reporting doc (XT-4758) ([#7268](https://github.com/issues/7268))  ([ef06b16](https://github.com/commit/ef06b16f52e8ec96099d8e3068b1727c05e77c41))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [20.0.17](https://github.com/compare/...@sage/xtrem~scripts~documentation@20.0.17) (2022-06-10)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* package version number fix ([#7344](https://github.com/issues/7344))  ([517e460](https://github.com/commit/517e460cde4848f3559f14bfd79a77835cd78223))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* publishing of xtrem-reporting doc (XT-4758) ([#7268](https://github.com/issues/7268))  ([ef06b16](https://github.com/commit/ef06b16f52e8ec96099d8e3068b1727c05e77c41))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [20.0.16](https://github.com/compare/...@sage/xtrem~scripts~documentation@20.0.16) (2022-06-10)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* package version number fix ([#7344](https://github.com/issues/7344))  ([517e460](https://github.com/commit/517e460cde4848f3559f14bfd79a77835cd78223))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* publishing of xtrem-reporting doc (XT-4758) ([#7268](https://github.com/issues/7268))  ([ef06b16](https://github.com/commit/ef06b16f52e8ec96099d8e3068b1727c05e77c41))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [20.0.15](https://github.com/compare/...@sage/xtrem~scripts~documentation@20.0.15) (2022-06-08)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* package version number fix ([#7344](https://github.com/issues/7344))  ([517e460](https://github.com/commit/517e460cde4848f3559f14bfd79a77835cd78223))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* publishing of xtrem-reporting doc (XT-4758) ([#7268](https://github.com/issues/7268))  ([ef06b16](https://github.com/commit/ef06b16f52e8ec96099d8e3068b1727c05e77c41))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [20.0.14](https://github.com/compare/...@sage/xtrem~scripts~documentation@20.0.14) (2022-06-07)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* package version number fix ([#7344](https://github.com/issues/7344))  ([517e460](https://github.com/commit/517e460cde4848f3559f14bfd79a77835cd78223))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* publishing of xtrem-reporting doc (XT-4758) ([#7268](https://github.com/issues/7268))  ([ef06b16](https://github.com/commit/ef06b16f52e8ec96099d8e3068b1727c05e77c41))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [20.0.13](https://github.com/compare/...@sage/xtrem~scripts~documentation@20.0.13) (2022-06-06)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* package version number fix ([#7344](https://github.com/issues/7344))  ([517e460](https://github.com/commit/517e460cde4848f3559f14bfd79a77835cd78223))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* publishing of xtrem-reporting doc (XT-4758) ([#7268](https://github.com/issues/7268))  ([ef06b16](https://github.com/commit/ef06b16f52e8ec96099d8e3068b1727c05e77c41))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [20.0.12](https://github.com/compare/...@sage/xtrem~scripts~documentation@20.0.12) (2022-06-05)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* package version number fix ([#7344](https://github.com/issues/7344))  ([517e460](https://github.com/commit/517e460cde4848f3559f14bfd79a77835cd78223))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* publishing of xtrem-reporting doc (XT-4758) ([#7268](https://github.com/issues/7268))  ([ef06b16](https://github.com/commit/ef06b16f52e8ec96099d8e3068b1727c05e77c41))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [20.0.11](https://github.com/compare/...@sage/xtrem~scripts~documentation@20.0.11) (2022-06-04)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* package version number fix ([#7344](https://github.com/issues/7344))  ([517e460](https://github.com/commit/517e460cde4848f3559f14bfd79a77835cd78223))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* publishing of xtrem-reporting doc (XT-4758) ([#7268](https://github.com/issues/7268))  ([ef06b16](https://github.com/commit/ef06b16f52e8ec96099d8e3068b1727c05e77c41))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [20.0.10](https://github.com/compare/...@sage/xtrem~scripts~documentation@20.0.10) (2022-06-03)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* package version number fix ([#7344](https://github.com/issues/7344))  ([517e460](https://github.com/commit/517e460cde4848f3559f14bfd79a77835cd78223))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* publishing of xtrem-reporting doc (XT-4758) ([#7268](https://github.com/issues/7268))  ([ef06b16](https://github.com/commit/ef06b16f52e8ec96099d8e3068b1727c05e77c41))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [20.0.9](https://github.com/compare/...@sage/xtrem~scripts~documentation@20.0.9) (2022-06-03)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* package version number fix ([#7344](https://github.com/issues/7344))  ([517e460](https://github.com/commit/517e460cde4848f3559f14bfd79a77835cd78223))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* publishing of xtrem-reporting doc (XT-4758) ([#7268](https://github.com/issues/7268))  ([ef06b16](https://github.com/commit/ef06b16f52e8ec96099d8e3068b1727c05e77c41))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [20.0.8](https://github.com/compare/...@sage/xtrem~scripts~documentation@20.0.8) (2022-06-02)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* package version number fix ([#7344](https://github.com/issues/7344))  ([517e460](https://github.com/commit/517e460cde4848f3559f14bfd79a77835cd78223))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* publishing of xtrem-reporting doc (XT-4758) ([#7268](https://github.com/issues/7268))  ([ef06b16](https://github.com/commit/ef06b16f52e8ec96099d8e3068b1727c05e77c41))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [20.0.5](https://github.com/compare/...@sage/xtrem~scripts~documentation@20.0.5) (2022-06-02)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* publishing of xtrem-reporting doc (XT-4758) ([#7268](https://github.com/issues/7268))  ([ef06b16](https://github.com/commit/ef06b16f52e8ec96099d8e3068b1727c05e77c41))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [20.0.4](https://github.com/compare/...@sage/xtrem~scripts~documentation@20.0.4) (2022-06-01)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* publishing of xtrem-reporting doc (XT-4758) ([#7268](https://github.com/issues/7268))  ([ef06b16](https://github.com/commit/ef06b16f52e8ec96099d8e3068b1727c05e77c41))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [20.0.3](https://github.com/compare/...@sage/xtrem~scripts~documentation@20.0.3) (2022-05-29)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [20.0.2](https://github.com/compare/...@sage/xtrem~scripts~documentation@20.0.2) (2022-05-28)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [20.0.1](https://github.com/compare/...@sage/xtrem~scripts~documentation@20.0.1) (2022-05-27)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [20.0.0](https://github.com/compare/...@sage/xtrem~scripts~documentation@20.0.0) (2022-05-26)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [19.0.33](https://github.com/compare/...@sage/xtrem~scripts~documentation@19.0.33) (2022-05-26)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [19.0.32](https://github.com/compare/...@sage/xtrem~scripts~documentation@19.0.32) (2022-05-26)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [19.0.31](https://github.com/compare/...@sage/xtrem~scripts~documentation@19.0.31) (2022-05-24)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [19.0.30](https://github.com/compare/...@sage/xtrem~scripts~documentation@19.0.30) (2022-05-24)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [19.0.29](https://github.com/compare/...@sage/xtrem~scripts~documentation@19.0.29) (2022-05-23)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [19.0.28](https://github.com/compare/...@sage/xtrem~scripts~documentation@19.0.28) (2022-05-23)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [19.0.27](https://github.com/compare/...@sage/xtrem~scripts~documentation@19.0.27) (2022-05-22)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [19.0.26](https://github.com/compare/...@sage/xtrem~scripts~documentation@19.0.26) (2022-05-21)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [19.0.25](https://github.com/compare/...@sage/xtrem~scripts~documentation@19.0.25) (2022-05-20)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [19.0.24](https://github.com/compare/...@sage/xtrem~scripts~documentation@19.0.24) (2022-05-20)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [19.0.23](https://github.com/compare/...@sage/xtrem~scripts~documentation@19.0.23) (2022-05-18)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [19.0.22](https://github.com/compare/...@sage/xtrem~scripts~documentation@19.0.22) (2022-05-17)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [19.0.21](https://github.com/compare/...@sage/xtrem~scripts~documentation@19.0.21) (2022-05-16)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [19.0.20](https://github.com/compare/...@sage/xtrem~scripts~documentation@19.0.20) (2022-05-15)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [19.0.19](https://github.com/compare/...@sage/xtrem~scripts~documentation@19.0.19) (2022-05-14)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [19.0.18](https://github.com/compare/...@sage/xtrem~scripts~documentation@19.0.18) (2022-05-13)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [19.0.17](https://github.com/compare/...@sage/xtrem~scripts~documentation@19.0.17) (2022-05-13)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [19.0.16](https://github.com/compare/...@sage/xtrem~scripts~documentation@19.0.16) (2022-05-12)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [19.0.15](https://github.com/compare/...@sage/xtrem~scripts~documentation@19.0.15) (2022-05-11)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [19.0.14](https://github.com/compare/...@sage/xtrem~scripts~documentation@19.0.14) (2022-05-10)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [19.0.13](https://github.com/compare/...@sage/xtrem~scripts~documentation@19.0.13) (2022-05-10)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [19.0.12](https://github.com/compare/...@sage/xtrem~scripts~documentation@19.0.12) (2022-05-09)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [19.0.11](https://github.com/compare/...@sage/xtrem~scripts~documentation@19.0.11) (2022-05-08)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [19.0.10](https://github.com/compare/...@sage/xtrem~scripts~documentation@19.0.10) (2022-05-07)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [19.0.9](https://github.com/compare/...@sage/xtrem~scripts~documentation@19.0.9) (2022-05-06)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [19.0.8](https://github.com/compare/...@sage/xtrem~scripts~documentation@19.0.8) (2022-05-06)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [19.0.7](https://github.com/compare/...@sage/xtrem~scripts~documentation@19.0.7) (2022-05-04)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [19.0.6](https://github.com/compare/...@sage/xtrem~scripts~documentation@19.0.6) (2022-05-03)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [19.0.5](https://github.com/compare/...@sage/xtrem~scripts~documentation@19.0.5) (2022-05-02)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [19.0.4](https://github.com/compare/...@sage/xtrem~scripts~documentation@19.0.4) (2022-05-01)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [19.0.3](https://github.com/compare/...@sage/xtrem~scripts~documentation@19.0.3) (2022-04-30)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [19.0.2](https://github.com/compare/...@sage/xtrem~scripts~documentation@19.0.2) (2022-04-29)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [19.0.1](https://github.com/compare/...@sage/xtrem~scripts~documentation@19.0.1) (2022-04-28)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [19.0.0](https://github.com/compare/...@sage/xtrem~scripts~documentation@19.0.0) (2022-04-28)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [18.0.37](https://github.com/compare/...@sage/xtrem~scripts~documentation@18.0.37) (2022-04-28)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [18.0.36](https://github.com/compare/...@sage/xtrem~scripts~documentation@18.0.36) (2022-04-27)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [18.0.35](https://github.com/compare/...@sage/xtrem~scripts~documentation@18.0.35) (2022-04-27)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [18.0.34](https://github.com/compare/...@sage/xtrem~scripts~documentation@18.0.34) (2022-04-26)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [18.0.33](https://github.com/compare/...@sage/xtrem~scripts~documentation@18.0.33) (2022-04-26)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [18.0.32](https://github.com/compare/...@sage/xtrem~scripts~documentation@18.0.32) (2022-04-25)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [18.0.31](https://github.com/compare/...@sage/xtrem~scripts~documentation@18.0.31) (2022-04-25)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [18.0.30](https://github.com/compare/...@sage/xtrem~scripts~documentation@18.0.30) (2022-04-21)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [18.0.29](https://github.com/compare/...@sage/xtrem~scripts~documentation@18.0.29) (2022-04-21)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [18.0.28](https://github.com/compare/...@sage/xtrem~scripts~documentation@18.0.28) (2022-04-20)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [18.0.27](https://github.com/compare/...@sage/xtrem~scripts~documentation@18.0.27) (2022-04-20)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [18.0.26](https://github.com/compare/...@sage/xtrem~scripts~documentation@18.0.26) (2022-04-18)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [18.0.25](https://github.com/compare/...@sage/xtrem~scripts~documentation@18.0.25) (2022-04-18)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [18.0.24](https://github.com/compare/...@sage/xtrem~scripts~documentation@18.0.24) (2022-04-16)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [18.0.23](https://github.com/compare/...@sage/xtrem~scripts~documentation@18.0.23) (2022-04-15)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [18.0.22](https://github.com/compare/...@sage/xtrem~scripts~documentation@18.0.22) (2022-04-14)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [18.0.21](https://github.com/compare/...@sage/xtrem~scripts~documentation@18.0.21) (2022-04-13)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [18.0.20](https://github.com/compare/...@sage/xtrem~scripts~documentation@18.0.20) (2022-04-12)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [18.0.19](https://github.com/compare/...@sage/xtrem~scripts~documentation@18.0.19) (2022-04-11)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [18.0.18](https://github.com/compare/...@sage/xtrem~scripts~documentation@18.0.18) (2022-04-10)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [18.0.17](https://github.com/compare/...@sage/xtrem~scripts~documentation@18.0.17) (2022-04-09)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [18.0.16](https://github.com/compare/...@sage/xtrem~scripts~documentation@18.0.16) (2022-04-08)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [18.0.15](https://github.com/compare/...@sage/xtrem~scripts~documentation@18.0.15) (2022-04-07)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [18.0.14](https://github.com/compare/...@sage/xtrem~scripts~documentation@18.0.14) (2022-04-06)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [18.0.13](https://github.com/compare/...@sage/xtrem~scripts~documentation@18.0.13) (2022-04-05)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [18.0.12](https://github.com/compare/...@sage/xtrem~scripts~documentation@18.0.12) (2022-04-04)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [18.0.11](https://github.com/compare/...@sage/xtrem~scripts~documentation@18.0.11) (2022-04-03)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [18.0.10](https://github.com/compare/...@sage/xtrem~scripts~documentation@18.0.10) (2022-04-02)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [18.0.9](https://github.com/compare/...@sage/xtrem~scripts~documentation@18.0.9) (2022-04-01)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [18.0.8](https://github.com/compare/...@sage/xtrem~scripts~documentation@18.0.8) (2022-03-31)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [18.0.7](https://github.com/compare/...@sage/xtrem~scripts~documentation@18.0.7) (2022-03-31)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [18.0.6](https://github.com/compare/...@sage/xtrem~scripts~documentation@18.0.6) (2022-03-30)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [18.0.5](https://github.com/compare/...@sage/xtrem~scripts~documentation@18.0.5) (2022-03-29)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [18.0.4](https://github.com/compare/...@sage/xtrem~scripts~documentation@18.0.4) (2022-03-28)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [18.0.3](https://github.com/compare/...@sage/xtrem~scripts~documentation@18.0.3) (2022-03-27)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [18.0.2](https://github.com/compare/...@sage/xtrem~scripts~documentation@18.0.2) (2022-03-26)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [18.0.1](https://github.com/compare/...@sage/xtrem~scripts~documentation@18.0.1) (2022-03-25)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [18.0.0](https://github.com/compare/...@sage/xtrem~scripts~documentation@18.0.0) (2022-03-24)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [17.0.20](https://github.com/compare/...@sage/xtrem~scripts~documentation@17.0.20) (2022-03-24)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [17.0.19](https://github.com/compare/...@sage/xtrem~scripts~documentation@17.0.19) (2022-03-24)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [17.0.18](https://github.com/compare/...@sage/xtrem~scripts~documentation@17.0.18) (2022-03-23)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [17.0.17](https://github.com/compare/...@sage/xtrem~scripts~documentation@17.0.17) (2022-03-22)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [17.0.16](https://github.com/compare/...@sage/xtrem~scripts~documentation@17.0.16) (2022-03-21)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [17.0.15](https://github.com/compare/...@sage/xtrem~scripts~documentation@17.0.15) (2022-03-20)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [17.0.14](https://github.com/compare/...@sage/xtrem~scripts~documentation@17.0.14) (2022-03-20)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [17.0.13](https://github.com/compare/...@sage/xtrem~scripts~documentation@17.0.13) (2022-03-19)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [17.0.12](https://github.com/compare/...@sage/xtrem~scripts~documentation@17.0.12) (2022-03-19)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [17.0.11](https://github.com/compare/...@sage/xtrem~scripts~documentation@17.0.11) (2022-03-18)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [17.0.10](https://github.com/compare/...@sage/xtrem~scripts~documentation@17.0.10) (2022-03-18)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [17.0.9](https://github.com/compare/...@sage/xtrem~scripts~documentation@17.0.9) (2022-03-18)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [17.0.8](https://github.com/compare/...@sage/xtrem~scripts~documentation@17.0.8) (2022-03-17)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

# [17.0.7](https://github.com/compare/...@sage/xtrem~scripts~documentation@17.0.7) (2022-03-17)

### Bug Fixes

* always update docs when pipeline run XT-4901 ([#850](https://github.com/issues/850))  ([96812c0](https://github.com/commit/96812c016522e43198d91a50501475386b504af2))
* docs not updated (XT-20587) ([#5704](https://github.com/issues/5704))  ([e2d93de](https://github.com/commit/e2d93deff8de5e51d14fb1e3a2a1a25f2abb4f04))
* documentation publishing solution XT-5544 ([#1058](https://github.com/issues/1058))  ([eb65c45](https://github.com/commit/eb65c454cecab8a85ca776ad90ff69862657bfc7))
* increase delay and get page meta only (XT-20587) ([#5867](https://github.com/issues/5867))  ([cea5600](https://github.com/commit/cea5600779ef4e7fc16f899b8acea4bdaf3f91e3))
* publish doc only if content is different XT-5544 ([#1040](https://github.com/issues/1040))  ([72809e1](https://github.com/commit/72809e1c688a9ee20bf41bb5cf82d6b4071750a6))
* XT-13112 Package dependency tool: config and doc ([#3475](https://github.com/issues/3475))  ([e18539b](https://github.com/commit/e18539bf48042916404b7f57f668172233675973))

### Features

* XT-3945 move doc directory ([#731](https://github.com/issues/731))  ([949d506](https://github.com/commit/949d50673ecccaa70b0c80c704d0bd2cfcc63a6a))

