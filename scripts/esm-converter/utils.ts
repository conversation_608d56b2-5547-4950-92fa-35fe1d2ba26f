import { promises as fs, Stats } from 'fs';
import path from 'node:path';
import * as prettier from 'prettier';

export async function fileStat(filePath: string): Promise<Stats | undefined> {
    try {
        return await fs.stat(filePath);
    } catch {
        return undefined;
    }
}

export async function prettifyFile(filepath: string, content: string) {
    const prettierConfig = await prettier.resolveConfig(filepath);
    let formatted = content;
    if (prettierConfig) {
        formatted = await prettier.format(content, {
            ...prettierConfig,
            filepath,
        });
    }
    return fs.writeFile(filepath, formatted, 'utf8');
}

export async function prettifyJsonFile(filepath: string, json: Record<string, any>) {
    return prettifyFile(filepath, JSON.stringify(json, null, 2));
}

export async function renameFiles(packageDir: string, subDir: string, from: string, to: string) {
    const dir = path.join(packageDir, `lib/${subDir}`);
    if (await fileStat(dir)) {
        await renameAllFiles(dir, from, to);
    }
}

async function renameAllFiles(dir: string, from: string, to: string) {
    const files = await fs.readdir(dir);
    const fromRegex = new RegExp(`${from}$`);
    for (const file of files) {
        const filePath = path.join(dir, file);
        const stat = await fs.stat(filePath);
        if (stat.isDirectory() && file !== 'node_modules' && !file.startsWith('.')) {
            await renameAllFiles(filePath, from, to);
        } else if (file.endsWith(from)) {
            await fs.rename(filePath, filePath.replace(fromRegex, to));
        }
    }
}

export function reorderPackageJson(packageJson: Record<string, any>): Record<string, any> {
    const fieldOrder = [
        'name',
        'version',
        'description',
        'buildStamp',
        'xtrem',
        'keywords',
        'homepage',
        'bugs',
        'license',
        'author',
        'contributors',
        'funding',
        'repository',
        'main',
        'module',
        'types',
        'typings',
        'files',
        'bin',
        'man',
        'directories',
        'scripts',
        'config',
        'dependencies',
        'devDependencies',
        'peerDependencies',
        'optionalDependencies',
        'bundledDependencies',
        'engines',
        'os',
        'cpu',
        'private',
        'publishConfig',
        'husky',
        'lint-staged',
        'prettier',
        'xo',
        'jest',
        'eslintConfig',
        'stylelint',
        'commitlint',
    ];

    const orderedPackageJson: Record<string, any> = {};
    for (const field of fieldOrder) {
        if (packageJson[field] != null) {
            orderedPackageJson[field] = packageJson[field];
        }
    }
    for (const field of Object.keys(packageJson)) {
        if (!fieldOrder.includes(field)) {
            orderedPackageJson[field] = packageJson[field];
        }
    }
    return orderedPackageJson;
}
