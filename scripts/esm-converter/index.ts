import * as yaml from 'js-yaml';
import { camelCase, uniq } from 'lodash';
import { promises as fs } from 'node:fs';
import path from 'node:path';
import {
    RenameEslintConfigAction,
    RenameFilesAction,
    RewritePackageJsonAction,
    RewriteTsConfigAction,
    ScanTsFilesAction,
} from './actions/index';
import { Package } from './package';
import { fileStat, prettifyJsonFile } from './utils';

interface DependingItem<This = any, ValT = any> extends Package {
    name: string;
    type?: string;
    path: string;
    dependsOn?: string[];
}

interface PnpmDependency {
    version: string;
    specifier: string;
}

type PnpmDependencies = { [key: string]: PnpmDependency };

interface PnpmLock {
    importers: { [key: string]: { dependencies: PnpmDependencies; devDependencies: PnpmDependencies } };
}

interface CmdArgs {
    $unflagged: string[];
    [key: string]: string | boolean | string[] | null;
}

const cache = new Map<string, any>();
const rootDir = path.join(__dirname, '..', '..');

console.log(`\n\nStarting ESM converter in ${rootDir}\n\n`);

const root = { name: '???root???' } as DependingItem;
const packageExclusionList = ['@sage/artillery-plugin-xtrem', '@sage/xtrem-dts-bundle', '@sage/xtrem-minify'];

const args = process.argv.slice(2).reduce(
    (r: CmdArgs, arg) => {
        if (/^\-\-\w/.test(arg)) {
            arg = arg.substring(2);
            const camelName = camelCase(arg);
            r[camelName] = true;
            r.$opt = camelName;
        } else if (typeof r.$opt === 'string') {
            r[r.$opt] = arg;
            delete r.$opt;
        } else {
            r.$unflagged.push(arg);
        }
        return r;
    },
    { $unflagged: [] },
);

(async () => {
    if (typeof args.single === 'string') {
        const pack = await Package.createPackage(args.single);
        await convertToEsm(pack, !!args.revert);
    } else if (typeof args.count === 'string') {
        const stopAt = typeof args.stopAt === 'string' ? args.stopAt : '';
        await convertAll(+(args.count || '10'), stopAt);
    } else if (args.fix) {
        console.log('\nRunning in fix mode');
        await convertAll();
    }
})();

async function convertToEsm(pack: Package, revert: boolean): Promise<boolean> {
    const dir = pack.path;
    console.log(`${revert ? 'Reverting' : 'Converting'} ${dir}`);

    if (!(await isConvertible(pack))) {
        console.log(`Skipping ${dir}: not convertible`);
        return false;
    }
    const actions = [
        new RewritePackageJsonAction(pack),
        new RenameEslintConfigAction(pack),
        new RewriteTsConfigAction(pack),
        new RenameFilesAction(pack, 'shared-functions', '.ts', '.cts'),
        new RenameFilesAction(pack, 'client-functions', '.ts', '.cts'),
        new ScanTsFilesAction(pack),
    ];
    for (const action of actions) {
        if (args.revert) {
            await action.undo();
        } else {
            await action.do();
        }
    }
    console.log(`${revert ? 'Reverted' : 'Converted'} ${dir}`);
    return true;
}

async function isConvertible(pack: Package): Promise<boolean> {
    const { dir } = pack;
    return (
        // pack.packageJson.xtrem?.isMain ||
        !packageExclusionList.includes(pack.name) &&
        !!(await fileStat(path.join(dir, 'tsconfig.json'))) &&
        !!(await fileStat(path.join(dir, 'index.ts'))) &&
        !!(await fileStat(path.join(dir, 'lib')))
    );
}

async function convertAll(count: number = 0, stopAt: string = '') {
    const pnpmLockFile = await loadPnpmLockFile(rootDir);
    if (!pnpmLockFile) {
        throw new Error('Cannot find pnpm-lock.yaml');
    }
    const pnpmLock = yaml.load(pnpmLockFile) as PnpmLock;
    const { importers } = pnpmLock;

    const dependencyMap = Object.create(null);
    const items = await Promise.all(
        Object.keys(importers).map(async (key) => {
            const pack = (await Package.createPackage(key)) as DependingItem;

            const { name } = pack;
            const definition = pnpmLock.importers[key];
            const deps = getDependencies(definition.dependencies);
            const devDeps = getDependencies(definition.devDependencies);
            pack.dependsOn = uniq([...deps, ...devDeps]);

            dependencyMap[name] = pack;
            return pack;
        }),
    );

    // depth-first search to find all dependencies
    const sorted = topoSort(items.filter((item) => item !== undefined));

    const modifiedPackages = {} as Record<string, DependingItem>;
    const sortItemDependencies = (item: DependingItem) => {
        const packageJson = item.packageJson as Record<string, any>;
        ['dependencies', 'devDependencies'].forEach((key) => {
            if (packageJson[key]) {
                packageJson[key] = Object.fromEntries(
                    Object.entries(packageJson[key]).sort(([a], [b]) => a.localeCompare(b)),
                );
            }
        });
    };
    const replaceBytenode = (item: DependingItem, key: 'dependencies' | 'devDependencies') => {
        const packageJson = item.packageJson as Record<string, any>;
        if (packageJson[key]?.['@sage/xtrem-bytenode']) {
            packageJson[key]['@sage/xtrem-minify'] = packageJson[key]['@sage/xtrem-bytenode'];
            delete packageJson[key]['@sage/xtrem-bytenode'];
            modifiedPackages[item.name] = item;
        }
    };

    console.log(`\nFound ${sorted.length} packages in the workspace`);
    // pre-process each package to replace xtrem-bytenode by xtrem-minify package
    const requiredInDependencies = ['@sage/xtrem-cli-compile'];

    for (const item of sorted) {
        if (item.packageJson.dependencies?.['@sage/xtrem-bytenode']) {
            replaceBytenode(item, 'dependencies');
        }
        if (item.packageJson.devDependencies?.['@sage/xtrem-bytenode']) {
            replaceBytenode(item, 'devDependencies');
        }
        if (item.packageJson.scripts?.['xtrem-bytenode']) {
            // replace xtrem-bytenode with xtrem-minify in scripts
            delete item.packageJson.scripts['xtrem-bytenode'];
            modifiedPackages[item.name] = item;
        }
        if (
            item.packageJson.scripts?.['build:binary'] &&
            item.packageJson.scripts['build:binary'].includes('xtrem-bytenode')
        ) {
            // replace build:binary content
            item.packageJson.scripts['build:binary'] = item.packageJson.scripts['build:binary'].replace(
                /(pnpm\s+)xtrem-bytenode(\s+--\s+)?(-c )(-d )?(-z)/g,
                'xtrem-minify -c -z',
            );
            modifiedPackages[item.name] = item;
        }

        // @sage/xtrem-cli-compile is the only package that should keep xtrem-minify in dependencies
        if (item.name !== '@sage/xtrem-cli-compile') {
            const hasMinifyBuild = !!item.packageJson.scripts?.['build:binary']?.includes('xtrem-minify');
            if (item.packageJson.dependencies?.['@sage/xtrem-minify']) {
                if (hasMinifyBuild) {
                    // move xtrem-minify to devDependencies if it is used in build:binary script
                    if (!item.packageJson.devDependencies) {
                        item.packageJson.devDependencies = {};
                    }
                    item.packageJson.devDependencies['@sage/xtrem-minify'] =
                        item.packageJson.dependencies['@sage/xtrem-minify'];
                }
                delete item.packageJson.dependencies['@sage/xtrem-minify'];
                modifiedPackages[item.name] = item;
            }
            if (item.packageJson.devDependencies?.['@sage/xtrem-minify'] && !hasMinifyBuild) {
                // remove xtrem-minify from devDependencies if it is not used in build:binary script
                delete item.packageJson.devDependencies['@sage/xtrem-minify'];
                modifiedPackages[item.name] = item;
            }
        }
    }

    // if we modified some packages, write them back
    if (Object.keys(modifiedPackages).length > 0) {
        console.log(`\nModified packages: ${Object.keys(modifiedPackages).length}`);
        for (const item of Object.values(modifiedPackages)) {
            console.log(`  ${item.name} from ${item.path}`);
            const packageFile = path.join(rootDir, item.path, 'package.json');
            sortItemDependencies(item);
            await prettifyJsonFile(packageFile, item.packageJson);
        }
    }
    const summary = {
        traversed: 0,
        processed: [] as DependingItem[],
        notConvertible: [] as DependingItem[],
        alreadyModule: [] as DependingItem[],
    };
    for (const item of sorted) {
        if (count && summary.processed.length >= count) {
            console.log(`\nStopping at ${count} processed`);
            break;
        }
        if (stopAt && item.path?.startsWith(stopAt)) {
            console.log(`\nStopping at ${stopAt} path`);
            break;
        }
        summary.traversed++;
        console.log(`\nProcessing ${item.path} processed=${summary.processed.length} max=${count}`);
        if (item.type === 'module' && !args.fix) {
            console.log(`Skipping ${item.path}: already a module`);
            summary.alreadyModule.push(item);
            continue;
        }
        // in fix mode, stop at the first converted item that is not a module
        if (args.fix && item.type !== 'module' && item.isConvertible) {
            console.log(`\nStopping (fix mode) at ${item.path} ${summary.processed.length} processed`);
            break;
        }

        if (await convertToEsm(item, !!args.revert)) {
            summary.processed.push(item);
        } else {
            summary.notConvertible.push(item);
        }
    }

    console.log('\n========================================');
    console.log(`\nSummary: ${summary.traversed} traversed, ${summary.processed.length} processed`);
    console.log(`\nNot convertible: ${summary.notConvertible.length}`);
    summary.notConvertible.forEach((item) => console.log(`  ${item.name} from ${item.path}`));
    console.log(`\nAlready a ESM module: ${summary.alreadyModule.length}`);
    summary.alreadyModule.forEach((item) => console.log(`  ${item.name} from ${item.path}`));
    console.log(`\nProcessed: ${summary.processed.length}`);
    summary.processed.forEach((item) => console.log(`  ${item.name} from ${item.path}`));
    console.log(`\nDone!`);
    console.log('\n========================================');
}

function getDependencies(deps: PnpmDependencies) {
    return !deps ? [] : Object.keys(deps).filter((dep) => deps[dep].specifier === 'workspace:*');
}

async function loadPnpmLockFile(dir: string) {
    const pnpmLockFile = path.join(dir, 'pnpm-lock.yaml');
    if (await fileStat(pnpmLockFile)) {
        return await fs.readFile(pnpmLockFile, 'utf8');
    }
    return undefined;
}

const pathNames = (path: string) => (typeof path === 'string' ? [path] : Object.keys(path));
const getDependencyNames = (dependsOn: string[] | undefined) =>
    dependsOn ? dependsOn.map(pathNames).flat() : [root.name];

function topoSort<T extends DependingItem>(items: T[]): T[] {
    // this is a toposort but we need stable sort (order preserved on ties)
    // so we use a custom algorithm.
    const result: T[] = [];
    let todo = [...(items || []), root as T];
    const acceptedNames = new Map<string, boolean>();

    while (todo.length > 0) {
        const next = [] as typeof items;
        todo.forEach((item) => {
            if (
                getDependencyNames(item.dependsOn).every((dep: string) => acceptedNames.get(dep) || dep === item.name)
            ) {
                if (item !== root) result.push(item);
                acceptedNames.set(item.name, true);
            } else {
                next.push(item);
            }
        });
        if (next.length === todo.length) {
            // throwToposortError(todo, items);
            throw new Error('toposort failed');
        }
        todo = next;
    }
    return result;
}
