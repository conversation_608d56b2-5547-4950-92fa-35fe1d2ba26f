import { promises as fs } from 'fs';
import { Package } from '../package';
import { fileStat } from '../utils';
import { EsmConverterActions } from './esm-converter-actions';

export class RenameEslintConfigAction extends EsmConverterActions {
    constructor(pack: Package) {
        super(pack);
    }

    async do(): Promise<void> {
        const { pack } = this;
        const { dir } = pack;
        await Promise.all(
            ['.eslintrc', '.eslintrc-filename'].map(async (filename) => {
                if (await fileStat(`${dir}/${filename}.js`)) {
                    await fs.rename(`${dir}/${filename}.js`, `${dir}/${filename}.cjs`);
                }
            }),
        );
    }

    async undo(): Promise<void> {
        // no need to undo
    }
}
