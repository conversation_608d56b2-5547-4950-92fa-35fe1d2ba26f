import { Package } from '../package';
import { renameFiles } from '../utils';
import { EsmConverterActions } from './esm-converter-actions';

export class RenameFilesAction extends EsmConverterActions {
    constructor(
        pack: Package,
        private subDir: string,
        private from: string,
        private to: string,
    ) {
        super(pack);
    }

    async do(): Promise<void> {
        const { pack, subDir, from, to } = this;
        await renameFiles(pack.dir, subDir, from, to);
    }

    async undo(): Promise<void> {
        const { pack, subDir, from, to } = this;
        await renameFiles(pack.dir, subDir, to, from);
    }
}
