import path from 'node:path';
import { Package } from '../package';
import { fileStat, prettifyFile } from '../utils';
import { EsmConverterActions } from './esm-converter-actions';

export class RewriteTsConfigAction extends EsmConverterActions {
    private filepath: string;

    constructor(pack: Package) {
        super(pack);
        this.filepath = `${pack.dir}/tsconfig.json`;
    }

    async checkFile(): Promise<boolean> {
        const fileStatResult = await fileStat(this.filepath);
        if (!fileStatResult) {
            console.log(`No tsconfig.json found in ${this.pack.dir}`);
        }
        return !!fileStatResult;
    }

    async do(): Promise<void> {
        const { pack } = this;
        const packageDir = pack.dir;

        if (!(await this.checkFile())) return;

        const tsConfig = require(this.filepath);
        tsConfig.compilerOptions = {
            ...tsConfig.compilerOptions,
            target: 'ESNext',
            module: 'NodeNext',
            moduleResolution: 'NodeNext',
        };
        tsConfig.include = tsConfig.include.map((include: string) => include.replace(/^lib\/\*\*\/\*$/, 'lib/**/*.ts'));
        for (const subDir in ['shared-functions', 'client-functions']) {
            if (!tsConfig.include.includes(`lib/${subDir}/**/*.cts`)) {
                const subDirStat = await fileStat(path.join(packageDir, `lib/${subDir}`));
                if (subDirStat && subDirStat.isDirectory()) {
                    tsConfig.include.push(`lib/${subDir}/**/*.cts`);
                }
            }
        }

        const excludes = tsConfig.exclude || [];
        if (!excludes.includes('.eslintrc*.cjs')) {
            excludes.splice(0, 0, '.eslintrc*.cjs');
            tsConfig.exclude = excludes;
        }
        await prettifyFile(`${packageDir}/tsconfig.json`, JSON.stringify(tsConfig));
    }

    async undo(): Promise<void> {
        const { pack } = this;
        const { dir } = pack;
        const tsConfigPath = path.join(dir, 'tsconfig.json');
        const tsConfig = require(tsConfigPath);
        tsConfig.compilerOptions = {
            ...tsConfig.compilerOptions,
            target: 'es2022', // ESNext ?
            module: 'CommonJS',
            moduleResolution: 'node',
        };
        tsConfig.include = tsConfig.include.filter((include: string) => !include.endsWith('*.cts'));
        tsConfig.include = tsConfig.include.map((include: string) =>
            include.replace(/^lib\/\*\*\/\*\.ts$/, 'lib/**/*'),
        );
        await prettifyFile(tsConfigPath, JSON.stringify(tsConfig));
    }
}
