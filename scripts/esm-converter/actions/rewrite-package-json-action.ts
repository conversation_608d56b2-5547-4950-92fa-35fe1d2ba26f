import path from 'path';
import { fileStat, prettifyJsonFile, reorderPackageJson } from '../utils';
import { EsmConverterActions } from './esm-converter-actions';

export class RewritePackageJsonAction extends EsmConverterActions {
    async do(): Promise<void> {
        const { pack } = this;
        const { dir, packageJson } = pack;

        const clienfFunctionsDir = path.join(dir, 'lib/client-functions');
        const clientDirStat = await fileStat(clienfFunctionsDir);
        const exports = {
            '.': {
                types: './build/package-definition.d.ts',
                import: './build/index.js',
                default: './build/index.js',
            },
            './package.json': './package.json',
        } as Record<string, any>;
        if (clientDirStat && clientDirStat.isDirectory()) {
            exports['./lib/client-functions'] = {
                types: './build/lib/client-functions/index.d.ts',
                require: './build/lib/client-functions/index.cjs',
                default: './build/lib/client-functions/index.cjs',
            };
            exports['./lib/client-functions/*.cjs'] = {
                types: './build/lib/client-functions/index.d.ts',
                require: './build/lib/client-functions/*.cjs',
                default: './build/lib/client-functions/*.cjs',
            };
        }

        const newPackageFileContent = {
            ...packageJson,
            type: 'module',
            exports,
        };
        delete newPackageFileContent.main;
        delete newPackageFileContent.typings;
        await prettifyJsonFile(`${dir}/package.json`, reorderPackageJson(newPackageFileContent));
    }

    async undo(): Promise<void> {
        const { pack } = this;
        const { dir, packageJson } = pack;
        const newPackageFileContent = {
            ...packageJson,
            main: './build/index.js',
            typings: './build/package-definition.d.ts',
        };
        delete newPackageFileContent.type;
        delete newPackageFileContent.exports;
        await prettifyJsonFile(`${dir}/package.json`, reorderPackageJson(newPackageFileContent));
    }
}
