import { promises as fs } from 'fs';
import path from 'path';
import { fileStat, prettifyFile } from '../utils';
import { EsmConverterActions } from './esm-converter-actions';

export class ScanTsFilesAction extends EsmConverterActions {
    async do(): Promise<void> {
        await scanTsFiles(this.pack.dir);
    }

    async undo(): Promise<void> {
        // revert is not fully implemented
        await scanTsFiles(this.pack.dir, [], true);
    }
}

async function scanTsFiles(dir: string, dirPath: string[] = [], revert = false) {
    const files = await fs.readdir(dir);
    console.log(`Scanning .../${dirPath.join('/')} (${files.length} files)`);
    for (const file of files) {
        const filePath = path.join(dir, file);
        const stat = await fs.stat(filePath);
        if (stat.isDirectory() && file !== 'node_modules' && !file.startsWith('.')) {
            if (
                (dirPath.length === 0 && ['lib', 'test'].includes(file)) ||
                (dirPath.length === 1 && file !== 'i18n') ||
                dirPath.length > 1
            ) {
                await scanTsFiles(filePath, [...dirPath, file], revert);
            }
        } else if (file.endsWith('.ts')) {
            await convertTsFile(filePath, dirPath, revert);
        } else if (file.endsWith('.cts')) {
            await convertTsFile(filePath, dirPath, revert);
        }
    }
}

async function convertTsFile(filePath: string, dirSegments: string[], revert = false) {
    const relativeDir = dirSegments.join('/');
    console.log(`  Converting .../${relativeDir}/${path.basename(filePath)}`);
    const dirPath = path.dirname(filePath);
    const content = await fs.readFile(filePath, 'utf8');
    const importMatches = content.matchAll(/(import|export) ([\w_,{}*\s]*?) from '(.*)';/g);
    let newContent = content;
    let offest = 0;
    let lastImportEndIndex = 0;
    const declarations = [] as string[];
    const [, libDir] = dirSegments;
    const insideUiCode = ['client-functions', 'pages', 'page-extensions', 'page-fragments', 'stickers'].includes(
        libDir,
    );
    const isLibIndex = relativeDir === 'lib' && path.basename(filePath) === 'index.ts';

    for (const match of importMatches) {
        const [fullMatch, importExport, importSpecifiers, importPath] = match;
        let replacement = '';
        if (insideUiCode) {
            if (importPath.startsWith('.')) {
                const importFullPath = path.join(dirPath, importPath);
                // in ui code, we are importing from client-functions or shared-functions using the .cjs extension
                if (/\/lib\/(client|shared)-functions(\/|$)/.test(importFullPath)) {
                    if (revert) {
                        replacement = `${importExport} ${importSpecifiers} from '${importPath.replace(/\.c?js$/, '').replace(/\/_?index$/, '')}';`;
                    } else if (!/\.c?js$/.test(importPath)) {
                        const jsFile = await getJsFileRelativePath(dirPath, importPath);
                        replacement = `${importExport} ${importSpecifiers} from '${jsFile}';`;
                    }
                }
            }
        }
        // if we have not already changed it to .js (or .cjs) and it is a relative path
        else if (importPath.startsWith('.')) {
            const hasExtension = /\.c?js$/.test(importPath);
            if (revert && hasExtension) {
                replacement = `${importExport} ${importSpecifiers} from '${importPath.replace(/\.c?js$/, '')}';`;
            } else if (!hasExtension) {
                // lib/index.ts must not export menu-items
                if (isLibIndex && importExport === 'export' && importPath === './menu-items') {
                    replacement = '#TO_DELETE#';
                } else {
                    const jsFile = await getJsFileRelativePath(dirPath, importPath);
                    replacement = `${importExport} ${importSpecifiers} from '${jsFile}';`;
                }
            }
        } else if (importExport === 'import') {
            // ui code is importing from build/lib
            if (importPath.startsWith('@sage/') && importPath.includes('/build/lib/')) {
                if (!importPath.endsWith('.js')) {
                    replacement = `// eslint-disable-next-line import/extensions\n${importExport} ${importSpecifiers} from '${importPath}.js';`;
                } else if (revert) {
                    replacement = `${importExport} ${importSpecifiers} from '${importPath.replace(/\.js$/, '')}';`;
                }
            } else {
                // replace "import * as lodash from 'lodash';" with "import _ from 'lodash';"
                if (['lodash', 'chai-as-promised'].includes(importPath) && importSpecifiers.startsWith('* as ')) {
                    replacement = `import ${importSpecifiers.replace('* as ', '')} from '${importPath}';`;
                } else if (importPath === 'lodash' && importSpecifiers.startsWith('{')) {
                    replacement = `import _ from 'lodash';`;
                    declarations.push(`const ${importSpecifiers} = _;`);
                }
            }
        }
        if (replacement) {
            if (replacement === '#TO_DELETE#') {
                // length + 1 to remove the line break
                const length = fullMatch.length + 1;
                const index = match.index + offest;
                newContent = `${newContent.slice(0, index)}${newContent.slice(index + length)}`;
                offest -= length;
                console.log(`    [Del]: ${fullMatch}`);
            } else {
                offest += replacement.length - fullMatch.length;
                newContent = newContent.replace(fullMatch, replacement);
                console.log(`    [Mod]: ${fullMatch} => ${replacement}`);
            }
        }
        lastImportEndIndex = match.index + fullMatch.length;
    }
    lastImportEndIndex += offest;
    if (isLibIndex && revert) {
        if (!/export \* from '\.\/menu-items';/.test(newContent)) {
            const menuItemsStat = await fileStat(path.join(dirPath, 'menu-items'));
            if (menuItemsStat?.isDirectory()) {
                const menuItemsIndexStat = await fileStat(path.join(dirPath, 'menu-items/index.ts'));
                // exports the menu-items directory
                newContent = `${newContent.slice(0, lastImportEndIndex)}\n\nexport * from './menu-items/${menuItemsIndexStat?.isFile ? 'index.js' : '_index.js'}';${newContent.slice(lastImportEndIndex)}`;
                lastImportEndIndex += 30; // length of "export * from './menu-items';" plus newline
            }
        }
    }
    if (/\b__filename\b/.test(newContent)) {
        if (revert) {
            newContent = newContent.replace(/const __filename = import\.meta\.filename;\n/, '');
        } else if (!/const __filename = import\.meta\.filename;/.test(newContent)) {
            declarations.push('const __filename = import.meta.filename;');
        }
    }
    if (/\b__dirname\b/.test(newContent)) {
        if (revert) {
            newContent = newContent.replace(/const __dirname = import\.meta\.dirname;\n/, '');
        } else if (!/const __dirname = import\.meta\.dirname;/.test(newContent)) {
            declarations.push('const __dirname = import.meta.dirname;');
        }
    }
    if (declarations.length > 0) {
        declarations.forEach((declaration) => {
            console.log(`    [Add]: ${declaration}`);
        });
        newContent = `${newContent.slice(0, lastImportEndIndex)}\n\n${declarations.join('\n')}${newContent.slice(lastImportEndIndex)}`;
    }

    await prettifyFile(filePath, newContent);
}

async function getJsFileRelativePath(dirPath: string, relativeFile: string): Promise<string> {
    const filePath = path.join(dirPath, relativeFile);
    // console.log(`    Checking .../${relativeFile} ${filePath}`);
    const stat = await fileStat(filePath);
    if (stat && (await stat.isDirectory())) {
        // console.log(`    Found directory ..${relativeFile}`);
        const indexFiles = ['index.ts', '_index.ts', 'index.cts'];
        for (const file of indexFiles) {
            const indexFilePath = path.join(filePath, file);
            const indexStat = await fileStat(indexFilePath);
            if (indexStat && (await indexStat.isFile())) {
                console.log(`    Found ${file} in ..${relativeFile}`);
                return `${relativeFile}/${file.replace(/\.(.?)ts$/, '.$1js')}`;
            }
        }
        throw new Error(`Cannot find any ${indexFiles} in .../${relativeFile}`);
    }
    if (await fileStat(`${filePath}.ts`)) {
        // console.log(`    Found ...${relativeFile}`);
        return `${relativeFile}.js`;
    }
    if (await fileStat(`${filePath}.cts`)) {
        // console.log(`    Found ...${relativeFile}`);
        return `${relativeFile}.cjs`;
    }
    throw new Error(`Cannot find ${relativeFile}.ts nor ${relativeFile}.cts`);
}
