import path from 'node:path';
import { fileStat } from './utils';

export const rootDir = path.join(__dirname, '../..');

export interface Package {
    dir: string;
    name: string;
    type?: string;
    path: string;
    relativeDir: string;
    packageJson: any;
    isConvertible?: boolean;
}

export class Package implements Package {
    dir: string;
    name: string;
    type?: string;
    path: string;
    relativeDir: string;
    packageJson: any;
    isConvertible?: boolean;

    private constructor(dir: string) {
        const packageRootDir = dir.startsWith('/') ? dir : path.join(rootDir, dir);
        const relativeDir = dir.startsWith('/') ? path.relative(rootDir, dir) : dir;
        const packageJson = require(path.join(packageRootDir, 'package.json'));

        this.name = packageJson.name;
        this.type = packageJson.type;
        this.packageJson = packageJson;
        this.isConvertible = false;
        this.dir = packageRootDir;
        this.relativeDir = relativeDir;
        this.path = relativeDir;
    }

    static async createPackage(dir: string): Promise<Package> {
        const pack = new Package(dir);

        pack.isConvertible = await this.isConvertible(pack);

        return pack;
    }

    static async isConvertible(pack: Package): Promise<boolean> {
        const { dir } = pack;
        return (
            // pack.packageJson.xtrem?.isMain ||
            !!(await fileStat(path.join(dir, 'tsconfig.json'))) &&
            !!(await fileStat(path.join(dir, 'index.ts'))) &&
            !!(await fileStat(path.join(dir, 'lib')))
        );
    }
}
