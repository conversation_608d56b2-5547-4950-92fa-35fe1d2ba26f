#!/usr/bin/env bash

# This script build jar installer for Windows

# WIN_ARCHIVE - The name of the Windows archive
# SCOPE - x3-services or wh-services
# PROJECT_NAME - X3Services or WHServices
# DELIVERY_VERSION - The package version and build id
# IZPACK_VERSION - The version of the izpack

echo "=== Running IZPack in container ==="
echo "WIN_ARCHIVE: $WIN_ARCHIVE"
echo "SCOPE: $SCOPE"
echo "PROJECT_NAME: $PROJECT_NAME"
echo "DELIVERY_VERSION: $DELIVERY_VERSION"
echo "IZPACK_VERSION: $IZPACK_VERSION"
echo "XTREM_ROOT: $XTREM_ROOT"

set -e

java -version
pwsh -version

# SETUP_NAME = "${WIN_ARCHIVE}".replaceAll("\\.zip\$", '')
SETUP_NAME=$(echo $WIN_ARCHIVE|sed s/\.zip$//g)

echo archive should be delivered in /xtrem/scripts/x3-izpack/$SCOPE/project/base/$WIN_ARCHIVE
cd /xtrem/scripts/x3-izpack/${SCOPE}/project/

sed -e s/2\\.999\\.99\\.99/${DELIVERY_VERSION}/g ${PROJECT_NAME}-template.5-2.xml > ${PROJECT_NAME}00.xml
sed -e s/${SCOPE}-999-win-zip/${WIN_ARCHIVE}/g ${PROJECT_NAME}00.xml > ${PROJECT_NAME}01.xml
sed -e s/${SCOPE}-999-linux-zip/${WIN_ARCHIVE}/g ${PROJECT_NAME}01.xml > ${PROJECT_NAME}.xml
cp ProcessPanelSpec-template.5-2.xml  ProcessPanelSpec.xml
cp UserInputSpec-template.5-2.xml  UserInputSpec.xml

rm -f ${SETUP_NAME}.jar

rm -rf /xtrem/scripts/x3-izpack/${SCOPE}/project/izPackCustomActions

mkdir /xtrem/scripts/x3-izpack/${SCOPE}/project/izPackCustomActions


cp /izpack/izPackCustomActions/target/com.sage.izpack-${IZPACK_VERSION}.jar /xtrem/scripts/x3-izpack/${SCOPE}/project/izPackCustomActions/

chmod -R 755 /izpack/bin/compile.sh

/izpack/bin/compile.sh ${PROJECT_NAME}.xml -b . -o ${SETUP_NAME}.jar -k standard -l 9
