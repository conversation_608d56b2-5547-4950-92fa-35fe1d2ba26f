#!/usr/bin/env bash

set -e

scope=$1
zipName=$2
deliveryVersion=$3 # Major version or staging
tagName=$4 # full version or staging
dryRun=$5
sourceRoot=$(pwd)
projectRoot=${sourceRoot}/scripts/x3-izpack/${scope}/project

targetRoot=s3://adc-dev-packaging

# shellcheck disable=SC2001
# shellcheck disable=SC2086
baseName=$(echo ${zipName}|sed s/\.zip$//g)

if [[ "${dryRun}" != "True" ]]; then
    dryRun="False"
fi

# We will generate and upload the txt file here in a future step
txtFileName=${scope}-${deliveryVersion}-win.txt
txtFilePath=${projectRoot}/${txtFileName}
jarFileName=${baseName}.jar
jarFilePath=${projectRoot}/${jarFileName}

echo "=== ${scope}(${tagName}) in ${projectRoot} content (dryRun=${dryRun}) ==="
# shellcheck disable=SC2086
ls -l ${projectRoot}

echo "=== JAR delivery file ==="
# shellcheck disable=SC2086
ls -l ${jarFilePath}

subFolder=$(date  +%Y%-m)

s3path="${targetRoot}/${scope}/${subFolder}/${jarFileName}"
txtS3path="${targetRoot}/${scope}/${txtFileName}"

# shellcheck disable=SC2086
echo "${scope}/${subFolder}/${jarFileName}" > ${txtFilePath}

echo "=== TXT delivery file ==="
# shellcheck disable=SC2086
ls -l ${txtFilePath}

if [[ "${dryRun}" == "True" ]]; then
    echo "=== DRY RUN ==="
    echo "productName: ${scope}"
    echo "deliveryVersion: ${deliveryVersion}"
    echo "tagName: ${tagName}"
    echo "sourceRoot: ${sourceRoot}"
    echo "projectRoot: ${projectRoot}"
    echo "targetRoot: ${targetRoot}"
    echo "baseName: ${baseName}"
    echo "jarFilePath: ${jarFilePath}"
    echo "s3path: ${s3path}"
    echo "txtFilePath: ${txtFilePath}"
    echo "txtS3path: ${txtS3path}"

    echo "Content of ${txtFilePath}"
    # shellcheck disable=SC2086
    cat ${txtFilePath}

    exit 0
fi

# The resulting tree in the S3 bucket is something like
# $(productName)
#   |- $(productName)-staging-win.txt
#   |- $(productName)-1.4-win.txt
#   \- yyyyMM
#        |- $(productName)-staging.623430-win.jar
#
# Copy the jar delivery then the index file

echo "=== copy to S3 ==="
echo "copy ${jarFilePath} to ${s3path}"
# shellcheck disable=SC2086
aws s3 cp ${jarFilePath} ${s3path}

echo "copy text file ${txtFilePath} to ${targetRoot}/${txtFilePath}"
# shellcheck disable=SC2086
aws s3 cp ${txtFilePath} ${txtS3path}
