#!/usr/bin/env bash

echo "=== Running IZPack ==="
echo "WIN_ARCHIVE: $WIN_ARCHIVE"
echo "SCOPE: $SCOPE"
echo "PROJECT_NAME: $PROJECT_NAME"
echo "DELIVERY_VERSION: $DELIVERY_VERSION"
echo "IZPACK_VERSION: $IZPACK_VERSION"
echo "XTREM_ROOT: $XTREM_ROOT"

IMAGE=ghcr.io/sage-erp-x3/sage-erp-izpack:$IZPACK_VERSION
echo "=== Running image $IMAGE ==="

if docker run --rm \
-e WIN_ARCHIVE="$WIN_ARCHIVE" \
-e SCOPE="$SCOPE"  \
-e PROJECT_NAME="$PROJECT_NAME" \
-e DELIVERY_VERSION="$DELIVERY_VERSION" \
-e IZPACK_VERSION="$IZPACK_VERSION" \
-v "$XTREM_ROOT":/xtrem \
--name xtrem_izpack \
$IMAGE \
sh /xtrem/scripts/x3-izpack/izpack.sh; then
    echo "JAR for $WIN_ARCHIVE created"
else
    echo "Issue with creating JAR for $WIN_ARCHIVE"
    exit 1
fi
