Sage X3 Services / Warehouse
==========

<img alt="X3-Services" height="360" src="resources/ADC-01.png" /> <br>

"Sage X3-Services" and "Sage X3-Warehouse" are the new products to support Mobile devices: 
they will replace the <a href="https://github.com/Sage-ERP-X3/Java-Web">"Sage ADC VT100 JavaWeb Server"</a> and other former implementations and API made in Syracuse (Syracuse tablet, Syracuse mobile). 

<br>

## Documentation: <br/>

How to install X3-services:<br>
https://confluence.sage.com/display/X3GBD/how+to+install+X3-services <br>

Installation Mobile Automation X3-SERVICES, Security: <br>
https://confluence.sage.com/display/X3DEVOPS/Installation+Mobile+Automation+X3-SERVICES <br>

Sage X3 Services developer studio installation: <br>
https://online-help.sagex3.com/erp/12/en-us/Content/V7DEV/getting-started_Sage-X3-Services-dev-studio-installation.htm <br/>

Creating a connected application to authenticate requests:<br>
https://developer.sage.com/x3/graphql/quick-start/create-connected-app/ <br/>
<br>



### Connecting X3-Services to Syracuse running in https:

<img alt="X3-Services / X3-Warehouse" width="640" src="resources/syracuseHttps.JPG" /> <br>

Ex C:\Sage\x3-services-48.0.41.1477624-win\xtrem-security.yml :<br>

```xml
loginUrl: http://:IRIS:8124
syracuse: 
  clientId: create-your-own-client-id-uuid
  secret: change-to-use-a-strong-secret-for-your-client-id

tls: 
  extraCaFiles:
    # Syraucse CA to connect X3-Services to Syracuse running on https 
    - 'C:\Sage\X3Services\certs\frpo402328\syracuse-ca.cacrt'
```
    
Running X3-Services in secured Mode: <br>

```xml
logs: 
  disabledForTests: True
  domains: 
    sage/xtrem-core/graphql: 
      level: info
    sage/xtrem-service/http: 
      level: info
    sage/xtrem-x3-gateway/api-helper: 
      level: info
    sage/xtrem-x3-sql-manager/sql: 
      level: info
server: 
  port: 8240
  ssl:
    ca: 'C:\Sage\X3Services\certs\frpo402328\x3-services-ca.cacrt'
    cert: 'C:\Sage\X3Services\certs\frpo402328\x3-services.crt'
    key: 'C:\Sage\X3Services\certs\frpo402328\x3-services.key'
    passphrase: 'x3service'

storage: 
  managedExternal: True
```
After this, you need to updload the CA certificate in Syracuse and specify this CA in Solution/X3-Service/CA


<br>




## X3Services Version 1.1 - 2025.01 R2

- IzPack 5.2.4.0: https://github.com/Sage-ERP-X3/sage-erp-izpack

- X3-337909: Problems specifying https URL in X3 Services jar installer

<br>

## X3Services Version 1.0 - 2025.01 R1

- IzPack *******: https://github.com/Sage-ERP-X3/sage-erp-izpack

- X3-293750: New IZPACK 5.2.3 setup <br>
<img alt="X3-Services / X3-Warehouse" width="640" src="resources/setup.png" /> <br>
<br/>

- "Client ID" and "Secret Id" are automatically generated and config files `xtrem-config.yml` and `xtrem-security.yml` are created.

- MS Windows "Sage X3-Services" is created from WinSw Wrapper https://github.com/winsw/winsw <br>
  This tool is called and initialized by the the PowerShell script `install-tools.ps1`, which calling the javascript `[InstallPath]\service.js` with `[InstallPath]\nodejs\win32-x64\node.exe` <br>
MS Windows 'X3 Service' is running with a specific user/password, implement recovery rules. <br>


- The Setup is able to detect an existing version of "Sage x3-services" installed with the previous zip archive, while detecting the MS Windows service `sagex3services.exe` or `sagewarehouseservices.exe`. 
For every update, the setup is deleting the folders `[InstallPath]\nodejs` and `[InstallPath]\node_modules` before unpacking every files. <br>
Yml Configuration files are not updated or changed during the update process in this version 1.0. <br>

- Response file <br>
auto-install.xml example: <br> 

```xml
<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<AutomatedInstallation langpack="eng">
    <com.sage.izpack.CheckedHelloNewPanel id="HelloPanelWindows"/>
    <com.izforge.izpack.panels.htmllicence.HTMLLicencePanel id="HtmlLicence"/>
    <com.sage.izpack.TargetNewPanel id="Target">
        <installpath>C:\Sage\X3Services</installpath>
    </com.sage.izpack.TargetNewPanel>
    <com.sage.izpack.PacksNewPanel id="Packs">
        <pack index="0" name="Sage X3 Services" selected="true"/>
        <pack index="1" name="Base files for MS Windows x86_64" selected="true"/>
    </com.sage.izpack.PacksNewPanel>
    <com.izforge.izpack.panels.userinput.UserInputPanel id="ServiceConfiguration">
        <entry key="winservice.password" value=""/>
        <entry key="x3services.service.port" value="8240"/>
        <entry key="winservice.username" value="sagex3rd.local\QualityAdmin"/>
    </com.izforge.izpack.panels.userinput.UserInputPanel>
    <com.izforge.izpack.panels.userinput.UserInputPanel id="ServiceXtremSyracuseConnection">
        <entry key="custom.syracuse.guids" value="1"/>
        <entry key="custom.syracuse.secret" value="1"/>
        <entry key="syracuse.secret" value="sh9:_b{i^:5gsEB%+eh*MaVux9LdPf.&lt;)Y"/>
        <entry key="syracuse.clientid" value="00c7dbac-3b67-a689-ebb8-25b116007eab"/>
        <entry key="syracuse.service.url" value="http://matrixw19sql.sagex3rd.local:8124"/>
    </com.izforge.izpack.panels.userinput.UserInputPanel>
    <com.izforge.izpack.panels.summary.SummaryPanel id="Summary"/>
    <com.izforge.izpack.panels.install.InstallPanel id="Install"/>
    <com.izforge.izpack.panels.process.ProcessPanel id="Process"/>
    <com.sage.izpack.FinishNewPanel id="Finish"/>
</AutomatedInstallation>
```


<br/>
<br/>


