﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<izpack:langpack version="5.0"
    xmlns:izpack="http://izpack.org/schema/langpack"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://izpack.org/schema/langpack http://izpack.org/schema/5.0/izpack-langpack-5.0.xsd">


    <str id="userinput.serviceconfiguration.title" txt="Configuration du service 'Sage Warehouse Services'" />
    <str id="winserviceuser" txt="Attention : Vous devez fournir un compte utilisateur et un mot de passe pour la création du service MS Windows. Le compte utilisateur doit impérativement être valide et posséder le droit 'se connecter en tant que service'." />
    <str id="userinput.x3services.port" txt="Numéro de port du service" />
    <str id="invalidport" txt="Port invalide (ex: 8124, ...)" />
    <str id="userinput.x3services.username" txt="Utilisateur du service" />
    <str id="userinput.x3services.winservice.password" txt="Mot de passe du compte" />
    <str id="allmandatoryfields" txt="(*) Tous les champs sont obligatoires." />

    <str id="usermandatory" txt="L'utilisateur est obligatoire !" />
    <str id="groupmandatory" txt="Le groupe est obligatoire !" />
    <str id="passwordmandatory" txt="Le mot de passe est obligatoire !" />
    <str id="userinput.guid.clientid" txt="Client ID" />
    <str id="userinput.guid.clientsecret" txt="Client secret" />
    <str id="syracuse.generateclientid" txt="Générer Client ID" />
    <str id="syracuse.generatesecret" txt="Générer code secret" />
    <str id="userinput.syracuse.usesslencryptedconnection" txt="Utiliser une connexion cryptée SSL" />
    <str id="errwinsrvaccount" txt="L'utilisateur ou le mot de passe est incorrect."/>
    <str id="warnwinsrvaccount" txt="Cet utilisateur n'a pas l'autorisation de se connecter en tant que service !"/>

    <str id="userinput.syracuse.connection" txt="Connexion à 'Sage X3 Syracuse'" />
    <str id="syracuse.url.text" txt="Veuillez indiquer l'adresse http(s) de votre application Sage X3 Syracuse. Ex: http://x3.mydomain.com:8124" />
    <str id="userinput.syracuse.url.hostname" txt="Nom d'hôte de Sage X3 Syracuse" />
    <str id="userinput.syracuse.url.port" txt="Port d'URL Syracuse" />
    <str id="userinput.syracuse.url" txt="URL http serveur Sage X3 Syracuse" />	
    <str id="servicehosterror" txt="L'adresse http(s) de 'Sage X3 Syracuse' semble incorrect" />
    <str id="syracusehostmandatory" txt="L'adresse http(s) de Sage X3 Syracuse est obligatoire" />
    <str id="nodeportmandatory" txt="Le port de Sage X3 Syracuse est obligatoire. Ex: 8124" />
	<str id="syracuse.copyclientid" txt="Copier dans presse-papiers" /> 
	<str id="syracuse.copyclientsecret" txt="Copier dans presse-papiers" /> 

    <str id="syracuse.guids" txt="Les éléments suivants seront générés dans les fichiers de configuration yml. Copiez-les dans les paramètres globaux de Sage X3 Syracuse." />
    <str id="syracuse.ssl.text" txt="Le fichier de configuration 'xtrem-security.yml' sera configuré avec ce certificat et cette clé, mais vous devrez lire le 'Guide pratique' pour terminer la configuration de la communication TLS avec Syracuse." />
    <str id="copyclientid.ok" txt="ClientId copié dans le presse-papiers" />
    <str id="copyclientsecret.ok" txt="'Client secret' copié dans le presse-papiers" />
    <str id="userinput.ssl.crtfile" txt="Fichier de certificat client(*.crt)" />
    <str id="userinput.ssl.keyfile" txt="Fichier de clé privée du client (*.key)" />

    <str id="userinput.syracuse.sslconnection" txt="Connexion sécurisée" />
</izpack:langpack>