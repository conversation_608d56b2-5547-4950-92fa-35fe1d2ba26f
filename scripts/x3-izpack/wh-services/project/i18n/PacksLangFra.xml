﻿<izpack:langpack version="5.0"
	xmlns:izpack="http://izpack.org/schema/langpack"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://izpack.org/schema/langpack http://izpack.org/schema/5.0/izpack-langpack-5.0.xsd">

	<str id="UserInputPanel.summaryCaption" txt="Création du service"/>
	<!-- packs i18n -->
	<str id="base" txt="Sage Warehouse Services"/>
	<str id="base.description" txt="Fichiers de base pour l'installation de Sage Warehouse Services"/>
	<str id="PacksPanel.installedpacks.summarycaption" txt="Core files"/>
	<str id="ProcessPanel.dotNet472OrHigherInstalled" txt="MS DotNet framework 4.7.2 déjà installé"/>
	<str id="ProcessPanel.dotNet80rHigherInstalled" txt="MS DotNet framework 4.8 déjà installé"/>	
	<str id="CheckedHelloPanel.productAlreadyExist0" txt="Ce logiciel a déjà été installé sur cet ordinateur depuis le chemin" />
	<str id="CheckedHelloPanel.productAlreadyExist1" txt="Voulez-vous mettre à jour cette installation ?" />
</izpack:langpack>