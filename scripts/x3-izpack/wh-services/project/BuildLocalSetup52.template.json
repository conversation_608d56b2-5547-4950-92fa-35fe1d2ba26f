{"compilePathRootFolder": "C:\\Dev\\X3\\izpack5", "compilePath": "C:\\Dev\\X3\\izpack5\\bin\\compile.bat", "runtimeInstallXmlSrc": "WHServices-template.5-2.xml", "runtimeInstallXmlDest": "WHServices.xml", "processPanelSpecSrc": "ProcessPanelSpec-template.5-2.xml", "processPanelSpecDest": "ProcessPanelSpec.xml", "userInputSpecSrc": "UserInputSpec-template.5-2.xml", "userInputSpecDest": "UserInputSpec.xml", "workspace": "C:\\Dev\\X3\\installersX3Services\\izpack\\WHServices\\project", "javaHome": "C:\\Program Files\\Zulu\\zulu-11", "javaVersion": "11", "setupName": "WHServices-1-0.jar", "x3Services999WinZip": "wh-services-48.0.41.1477625-win.zip", "x3Services999LinuxZip": "WH-services-staging.1445843-linux.zip", "componentVersion": "0.0.11"}