@echo off
REM UPDATE SCRIPT

cd /d "${INSTALL_PATH}"

Powershell.exe -Executionpolicy remotesigned -File  "${BEFORE_UPDATE_SCRIPT_PS_PATH}" -InstallPath  "${INSTALL_PATH}"  -LogPath "${INSTALL_PATH}${FILE_SEPARATOR}logs" -Action Uninstall


@IF %ERRORLEVEL% EQU 0 GOTO NOERROR1
echo An error occured while launching beforepacks.ps1  (${BEFORE_UPDATE_SCRIPT_PS})
REM EXIT %ERRORLEVEL%
:NOERROR1
exit 0
REM pause

exit 0
