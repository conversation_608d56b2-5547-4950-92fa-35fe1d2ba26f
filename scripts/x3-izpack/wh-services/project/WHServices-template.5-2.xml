﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<izpack:installation version="5.0"
  xmlns:izpack="http://izpack.org/schema/installation"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://izpack.org/schema/installation http://izpack.org/schema/5.0/izpack-installation-5.0.xsd">
  <info>
    <appname>Sage Warehouse Services</appname>
    <appversion>2.999.99.99</appversion>
    <authors>
      <author email="" name="Sage - www.sage.com" />
    </authors>
    <javaversion>11</javaversion>
    <requiresjdk>no</requiresjdk>
    <run-privileged condition="izpack.windowsinstall" />
    <readinstallationinformation>no</readinstallationinformation>
    <writeinstallationinformation>no</writeinstallationinformation>
    <tempdir />
    <uninstaller write="yes" />
    <summarylogfilepath>${INSTALL_PATH}${FILE_SEPARATOR}Summary.htm</summarylogfilepath>
    <url>http://www.sage.com</url>
  </info>
  <variables>
    <variable name="Publisher" value="Sage" />
    <variable name="app-version" value="2.999.99.99" />
    <variable name="x3services.service.creation" value="true" />
    <variable name="component.service.port" value="" />
    <variable name="component.service.username" value="" />
    <variable name="component.service.groupname" value="" />
    <variable name="x3services.service.port" value="8240" />
    <variable name="x3service.name" value="Sage Warehouse Services" />
    <variable name="x3service.displayname" value="Sage Warehouse Services" />
    <variable name="TargetPanel.dir.windows" value="$DEFAULT_INSTALL_DRIVE\Sage\WHServices" />
    <variable name="TargetPanel.dir.unix" value="/Sage/WHServices" />
    <variable name="CheckedHelloNewPanel.disallowMultipleInstance" value="true" />
    <variable name="CheckedHelloPanel.disallowMultipleInstance" value="true" />
    <variable name="osversion" value="${SYSTEM[os.version]}" />
    <!-- Update detected from previous ZIP method -->
    <variable name="update.from.previouszip" value="false" />
    <variable name="winservice.username" value="" />
    <variable name="winservice.password" value="" />
    <variable name="winservice.passwordbase64" value="" />
    <variable name="syracuse.service.url" value="" />
    <variable name="syracuse.service.hostname" value="" />
    <variable name="syracuse.service.port" value="" />
    <variable name="syracuse.clientid" value="create-your-own-client-id-uuid" />
    <variable name="syracuse.secret" value="change-to-use-a-strong-secret-for-your-client-id" />
  </variables>

  <dynamicvariables>
    <variable name="current.username" value="${USER_NAME}" />
    <variable name="current.groupname" value="${GROUP_NAME}" />
    <variable name="default.component.service.username" value="${USER_NAME}" />
    <variable name="default.component.service.groupname" value="${GROUP_NAME}" />
    <variable name="shell.ext" value="sh" condition="!installonwindows" />
    <variable name="shell.ext" value="cmd" condition="installonwindows" />
    <variable name="default.x3services.dir.configpath" value="${INSTALL_PATH}" />
    <variable name="syracuse.ssl.crtfile" value="" />
    <variable name="syracuse.ssl.keyfile" value="" />
    <variable name="x3services.service.description" value="${x3service.displayname} - Port ${x3services.service.port}"/>
  </dynamicvariables>
  <conditions>
    <condition type="variable" id="installer_console">
      <name>INSTALLER</name>
      <value>console</value>
    </condition>
    <condition type="compareversions" id="win2k8r2min">
      <arg1>${osversion}</arg1>
      <arg2>6.1</arg2>
      <operator>geq</operator>
    </condition>
    <condition type="java" id="redhat8minimum">
      <java>
        <class>com.sage.izpack.OsVersionHelper</class>
        <field>IS_REDHAT_8_MIN</field>
      </java>
      <returnvalue type="boolean">true</returnvalue>
    </condition>
    <condition type="java" id="linux8min">
      <java>
        <class>com.sage.izpack.OsVersionHelper</class>
        <field>IS_LINUX_8_MIN</field>
      </java>
      <returnvalue type="boolean">true</returnvalue>
    </condition>
    <condition type="java" id="linux9min">
      <java>
        <class>com.sage.izpack.OsVersionHelper</class>
        <field>IS_LINUX_9_MIN</field>
      </java>
      <returnvalue type="boolean">true</returnvalue>
    </condition>
    <condition type="variable" id="isroot">
      <name>current.username</name>
      <value>root</value>
    </condition>
    <condition type="not" id="notroot">
      <condition type="ref" refid="isroot" />
    </condition>
    <condition type="variable" id="updatemode">
      <!-- <name>MODIFY.IZPACK.INSTALL</name> -->
      <name>modify.izpack.install</name>
      <value>true</value>
    </condition>

    <condition type="variable" id="createconfig">
      <name>x3services.service.creation</name>
      <value>true</value>
    </condition>
    <condition type="not" id="notcreateconfig">
      <condition type="ref" refid="createconfig" />
    </condition>
    <condition type="not" id="notwin2k8r2min">
      <condition type="ref" refid="win2k8r2min" />
    </condition>
    <condition type="and" id="condwin2k8">
      <condition type="ref" refid="notwin2k8r2min" />
      <condition type="ref" refid="izpack.windowsinstall" />
    </condition>
    <condition type="and" id="condwin2k8r2">
      <condition type="ref" refid="win2k8r2min" />
      <condition type="ref" refid="izpack.windowsinstall" />
    </condition>
    <condition type="java" id="installonwindows">
      <java>
        <class>com.izforge.izpack.util.OsVersion</class>
        <field>IS_WINDOWS</field>
      </java>
      <returnvalue type="boolean">true</returnvalue>
    </condition>
    <condition type="java" id="installonlinux">
      <java>
        <class>com.izforge.izpack.util.OsVersion</class>
        <field>IS_LINUX</field>
      </java>
      <returnvalue type="boolean">true</returnvalue>
    </condition>
    <condition type="java" id="installonoracle">
      <java>
        <class>com.sage.izpack.OsVersionHelper</class>
        <field>IS_ORACLE_LINUX</field>
      </java>
      <returnvalue type="boolean">true</returnvalue>
    </condition>
    <condition type="java" id="installonredhat">
      <java>
        <class>com.izforge.izpack.util.OsVersion</class>
        <field>IS_REDHAT_LINUX</field>
      </java>
      <returnvalue type="boolean">true</returnvalue>
    </condition>
    <condition type="java" id="installoncentos">
      <java>
        <class>com.sage.izpack.OsVersionHelper</class>
        <field>IS_CENTOS_LINUX</field>
      </java>
      <returnvalue type="boolean">true</returnvalue>
    </condition>
    <condition type="java" id="installonami">
      <java>
        <class>com.sage.izpack.OsVersionHelper</class>
        <field>IS_AMI_LINUX</field>
      </java>
      <returnvalue type="boolean">true</returnvalue>
    </condition>
    <condition type="or" id="redhatororacle">
      <condition type="ref" refid="installonredhat" />
      <condition type="ref" refid="installonoracle" />
    </condition>
    <condition type="or" id="supportedlinuxstage1">
      <condition type="ref" refid="installonredhat" />
      <condition type="ref" refid="installonoracle" />
    </condition>
    <condition type="or" id="supportedlinuxstage2">
      <condition type="ref" refid="installoncentos" />
      <condition type="ref" refid="installonami" />
    </condition>
    <condition type="or" id="supportedlinux">
      <condition type="ref" refid="supportedlinuxstage1" />
      <condition type="ref" refid="supportedlinuxstage2" />
    </condition>
    <condition type="and" id="unixuser">
      <condition type="ref" refid="installonlinux" />
      <condition type="ref" refid="notroot" />
    </condition>
    <condition type="and" id="windowsandnotupdate">
      <condition type="ref" refid="installonwindows" />
      <condition type="ref" refid="!updatemode" />
    </condition>
    <condition type="java" id="izpack.x86_64install">
      <java>
        <class>com.izforge.izpack.util.OsVersion</class>
        <field>IS_X64</field>
      </java>
      <returnvalue type="boolean">true</returnvalue>
    </condition>
  </conditions>
  <installerrequirements>
    <installerrequirement condition="izpack.windowsinstall" message="osrequirementfailed" />
    <installerrequirement condition="izpack.x86_64install" message="x86requirementfailed" />
    <installerrequirement condition="win2k8r2min|!installonwindows" message="osrequirementfailed" />
    <!-- 
    <installerrequirement condition="izpack.windowsinstall|installonlinux" message="osrequirementfailed" />
    <installerrequirement condition="linux8min|!installonoracle" message="osrequirementfailed" />
    <installerrequirement condition="linux8min|!installonredhat" message="osrequirementfailed" />
    <installerrequirement condition="linux8min|!installoncentos" message="osrequirementfailed" />
    <installerrequirement condition="linux8min|!installonami" message="osrequirementfailed" /> 
    -->
  </installerrequirements>
  <!--
        The gui preferences indication.
        Sets the installer window to 800x600. It will not be able to change the size.
    -->
  <guiprefs width="800" height="600" resizable="no">
    <modifier value="true" key="showDebugWindow" />
    <modifier key="useButtonIcons" value="no" />
    <modifier key="useLabelIcons" value="no" />
    <modifier key="useFlags" value="yes" />
    <modifier key="langDisplayType" value="native" />
    <modifier key="useHeadingPanel" value="yes" />
    <modifier key="headingImageOnLeft" value="yes" />
    <modifier key="labelGap" value="2" />
    <modifier key="headingLineCount" value="1" />
    <modifier key="headingBackgroundColor" value="0x00ffffff" />
    <modifier key="headingPanelCounter" value="text" />
    <modifier key="headingPanelCounterPos" value="inHeading" />
  </guiprefs>
  <!--
        The locale section.
        Asks here to include the English and French langpacks.
    -->
  <locale>
    <langpack iso3="eng" />
    <langpack iso3="fra" />
  </locale>
  <!--
        The resources section.
        The ids must be these ones if you want to use the LicencePanel and/or the InfoPanel.
    -->
  <resources>
    <res id="LicencePanel.licence_fra" src="SAGE X3 EULA FR.txt" />
    <res id="LicencePanel.licence_eng" src="SAGE X3 EULA EN.txt" />
    <res id="ReadMeNewPanel.readme_fra" src="prerequisite_fra.htm" />
    <res id="ReadMeNewPanel.readme_eng" src="prerequisite_eng.htm" />
    <res id="HTMLLicencePanel.licence_fra" src="SAGE X3 EULA FR.htm" />
    <res id="HTMLLicencePanel.licence_eng" src="SAGE X3 EULA EN.htm" />
    <res id="InfoPanel.info" src="Readme.txt" />

    <res id="CustomLangpack.xml_eng" src="i18n/PacksLangEng.xml" />
    <res id="CustomLangpack.xml_fra" src="i18n/PacksLangFra.xml" />
    <res id="packsLang.xml_eng" src="i18n/PacksLangEng.xml" />
    <res id="packsLang.xml_fra" src="i18n/PacksLangFra.xml" />

    <res id="userInputSpec.xml" src="UserInputSpec.xml" />
    <res id="userInputLang.xml_eng" src="i18n/UserInputLangEng.xml" />
    <res id="userInputLang.xml_fra" src="i18n/UserInputLangFra.xml" />

    <res id="Heading.image" src="images/sage.png" />
    <res id="installer.langsel.img" src="images/splash.png" />
    <res id="UninstallerIcon" src="images/UninstallerIcon.ico" />

    <!-- GENERAL UPDATE SCRIPTS -->
    <res id="BeforeInstallScript_unix" src="installscripts/beforepacks.sh" />
    <res id="BeforeInstallScript_windows" src="installscripts/beforepacks.cmd" />
    <res id="BeforeInstallScriptPs_unix" src="installscripts/beforepacks.ps1" />
    <res id="BeforeInstallScriptPs_windows" src="installscripts/beforepacks.ps1" />

    <res id="BeforeUpdateScript_unix" src="updatescripts/beforepacks.sh" />
    <res id="BeforeUpdateScript_windows" src="updatescripts/beforepacks.cmd" />
    <res id="BeforeUpdateScriptPs_unix" src="updatescripts/beforepacks.ps1" />
    <res id="BeforeUpdateScriptPs_windows" src="updatescripts/beforepacks.ps1" />

    <res id="AfterUpdateScript_unix" src="updatescripts/afterpacks.sh" />
    <res id="AfterUpdateScript_windows" src="updatescripts/afterpacks.cmd" />

    <res id="productsSpec.txt" src="config/oldversions.txt" />
    <res id="ProcessPanel.Spec.xml" src="ProcessPanelSpec.xml" />

    <res id="customicons.xml" src="CustomIcons.xml" />

    <res id="AntTasks.xml" src="updatescripts/AntTasks.xml" />
    <res id="AntActionsSpec.xml" src="AntActionsSpec.xml" />
  </resources>
  <!--
        The panels section.
        We indicate here which panels we want to use. The order will be respected.
    -->
  <panels>
    <panel classname="com.sage.izpack.CheckedHelloNewPanel" id="HelloPanelWindows">
      <actions>
        <!-- preconstruct / preactivate / prevalidate / postvalidate /-->
        <action stage="preactivate" classname="com.sage.izpack.PreValidateXtremCheckedAlreadyInstalled">
          <param name="servicename" value="sagewarehouseservices.exe" />
        </action>
        <action stage="postvalidate" classname="com.sage.izpack.PostValidateXtremCheckedAlreadyInstalled"/>
      </actions>
      <!-- com.sage.izpack.CheckProductAlreadyInstalled : manage key productsSpec.txt and old application name -->
      <validator classname="com.sage.izpack.CheckProductAlreadyInstalled" />
      <os family="windows" />
    </panel>
    <panel classname="HelloPanel" id="HelloPanelUnix">
      <os family="unix" />
    </panel>

    <!-- PowerShell 7.2 requirement on Linux -->
    <panel classname="com.sage.izpack.ReadMeNewPanel" id="ReadMe" condition="installonlinux" />
    <panel classname="HTMLLicencePanel" condition="!installer_console" id="HtmlLicence" />
    <panel classname="LicencePanel" condition="installer_console" id="TxtLicence" />

    <panel classname="com.sage.izpack.TargetNewPanel" condition="!updatemode" id="Target">
      <validator classname="com.sage.izpack.TargetNewPanelValidator">
        <configuration>
          <!-- If true, the panel will show the "Target directory will be created" dialog if a directory does not exist. -->
          <ShowCreateDirectoryMessage>false</ShowCreateDirectoryMessage>
          <!-- Don't show warning if target directory already exists -->
          <ShowExistingDirectoryWarning>false</ShowExistingDirectoryWarning>
        </configuration>
      </validator>
    </panel>
    <panel classname="com.sage.izpack.PacksNewPanel" id="Packs">
      <validator classname="com.sage.izpack.PacksNewPanelValidator" />
    </panel>

    <!-- Service configuration -->
    <panel classname="UserInputPanel" id="ServiceConfiguration">
      <actions>
        <!-- preconstruct / preactivate / prevalidate / postvalidate /-->
        <action stage="preactivate" classname="com.sage.izpack.PreValidateXtremServiceConfiguration">
          <param name="servicename" value="sagewarehouseservices.exe" />
        </action>
        <action stage="postvalidate" classname="com.sage.izpack.PostValidateXtremServiceConfiguration"/>
      </actions>
      <validator classname="com.sage.izpack.CheckXtremServiceConfiguration" />
    </panel>

    <!-- Xtrem connection with Syracuse -->
    <panel classname="UserInputPanel" id="ServiceXtremSyracuseConnection" condition="!updatemode">
      <actions>
        <action stage="preactivate" classname="com.sage.izpack.PreValidateXtremSyracuseConnection" />
        <action stage="postvalidate" classname="com.sage.izpack.PostValidateXtremSyracuseConnection" />
      </actions>
      <validator classname="com.sage.izpack.CheckXtremSyracuseConnection" />
    </panel>

    <panel classname="SummaryPanel" id="Summary" />
    <panel classname="InstallPanel" id="Install" />
    <panel classname="ProcessPanel" id="Process" />
    <panel classname="com.sage.izpack.FinishNewPanel" id="Finish" />
  </panels>
  <listeners>
    <listener classname="ConfigurationInstallerListener" stage="install" />
    <listener classname="SummaryLoggerInstallerListener" stage="install" />

    <listener classname="com.sage.izpack.RegistryInstallerNewListener" stage="install">
      <os family="windows" />
    </listener>
    <listener classname="com.sage.izpack.RegistryUninstallerNewListener" stage="uninstall">
      <os family="windows" />
    </listener>
    <!--
			"BeforeUpdateScript" .sh/.cmd
			"BeforeInstallScript" .sh/.cmd  
			"AfterUpdateScript" .sh/.cmd 
			"AfterInstallScript" .sh/.cmd 
			-->
    <listener classname="com.sage.izpack.UpdateListener" stage="install" />
  </listeners>
  <!--
        The packs section.
        We specify here our packs.
    -->
  <packs>
    <pack id="base" name="Sage Warehouse Services" required="yes" hidden="false" preselected="yes">
      <description>Core files for Sage Warehouse Services Component</description>
      <file src="SAGE X3 EULA EN.txt" targetdir="${INSTALL_PATH}" override="true" />
      <file src="SAGE X3 EULA FR.txt" targetdir="${INSTALL_PATH}" override="true" />
      <file src="SAGE X3 EULA EN.htm" targetdir="${INSTALL_PATH}" override="true" />
      <file src="SAGE X3 EULA FR.htm" targetdir="${INSTALL_PATH}" override="true" />
    </pack>

    <pack id="basewindows" name="Base files for MS Windows x86_64" required="yes" condition="izpack.windowsinstall">
      <os family="windows" />
      <description>Core files for MS Windows x86_64</description>
      <file src="base/wh-services-999-win-zip" targetdir="$INSTALL_PATH" override="true" unpack="true" />
      <file src="prerequisite_fra.htm" targetdir="${INSTALL_PATH}" override="true" />
      <file src="prerequisite_eng.htm" targetdir="${INSTALL_PATH}" override="true" />
      <file src="service/install-tools.ps1" targetdir="${INSTALL_PATH}" override="true" />
      <file src="service/service-delete.cmd" targetdir="${INSTALL_PATH}" override="true" />
      <parsable targetfile="${INSTALL_PATH}${FILE_SEPARATOR}service-delete.cmd" />
      <executable targetfile="${INSTALL_PATH}${FILE_SEPARATOR}service-delete.cmd" stage="uninstall" failure="ask" />

      <file src="service/xtrem-config.template.json" targetdir="${INSTALL_PATH}" override="true" />
      <parsable targetfile="${INSTALL_PATH}${FILE_SEPARATOR}xtrem-config.template.json" />
      <file src="service/xtrem-security.template.json" targetdir="${INSTALL_PATH}" override="true" />
      <parsable targetfile="${INSTALL_PATH}${FILE_SEPARATOR}xtrem-security.template.json" />

    </pack>

    <pack id="baselinuxredhat8" name="Base files for Linux x86_64 - Redhat 8" required="yes" condition="izpack.linuxinstall+linux8min">
      <os family="unix" />
      <description>Core files for Linux x86_64</description>
      <!-- <file src="base/wh-services-999-linux-zip" targetdir="$INSTALL_PATH" override="true" unpack="true" /> -->
      <file src="prerequisite_fra.htm" targetdir="${INSTALL_PATH}" override="true" />
      <file src="prerequisite_eng.htm" targetdir="${INSTALL_PATH}" override="true" />
      <file src="service/install-tools.ps1" targetdir="${INSTALL_PATH}" override="true" />
      <!-- <file src="service/service-delete.sh" targetdir="${INSTALL_PATH}" override="true" /> -->
      <!-- <parsable targetfile="${INSTALL_PATH}${FILE_SEPARATOR}service-delete.sh" />
      <executable targetfile="${INSTALL_PATH}${FILE_SEPARATOR}service-delete.sh" stage="uninstall" failure="ask" /> -->
    </pack>

  </packs>

  <natives>
    <native type="izpack" name="ShellLink_x64.dll">
      <os family="windows" arch="x64" />
    </native>
    <native type="izpack" name="WinSetupAPI_x64.dll" uninstaller="true">
      <os family="windows" arch="x64" />
    </native>

    <native type="izpack" name="WinSetupAPI.dll" uninstaller="true">
      <os family="windows" arch="x86" />
    </native>

    <native type="3rdparty" name="COIOSHelper_x64.dll" uninstaller="true">
      <os family="windows" arch="x64" />
    </native>
  </natives>

  <jar src="lib/jna-5.17.0.jar" stage="both" />
  <jar src="lib/jna-platform-5.17.0.jar" stage="both" />
  <jar src="lib/commons-logging-1.1.1.jar" stage="both" />
  <jar src="lib/ant-1.10.13.jar" stage="both" />
  <jar src="lib/ant-launcher-1.10.13.jar" stage="both" />
  <jar src="lib/snakeyaml-2.3.jar" stage="both" />
  <!-- Sage IzPack customization 5.2 -->
  <jar src="izPackCustomActions/com.sage.izpack-5.2.4.0.jar" stage="both" />

</izpack:installation>