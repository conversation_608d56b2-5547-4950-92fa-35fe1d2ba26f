<izpack:antactions version="5.0"
    xmlns:izpack="http://izpack.org/schema/antactions"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://izpack.org/schema/antactions http://izpack.org/schema/5.0/izpack-antactions-5.0.xsd">

    <pack name="Base files for MS Windows x86_64">
        <antcall order="afterpack" uninstall_order="beforedeletion" quiet="no" buildresource="AntTasks.xml">
            <property name="install.dir" value="${INSTALL_PATH}" />
            <target name="nop" />
            <!-- needed to enable functionality, does nothing -->
            <uninstall_target name="cleanup-win" />
        </antcall>
    </pack>
</izpack:antactions>