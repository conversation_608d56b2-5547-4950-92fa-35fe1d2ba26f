#!/bin/sh

cd "${INSTALL_PATH}"

# SERVICE_NAME="safex3mongodb-mongo01"
SERVICE_NAME=`echo -n "${mongodb.service.name}" | tr [:space:] '_' | tr [:upper:] [:lower:]`

# Check if the service exists and serialize service content
if systemctl list-units --full -all | grep -Fq "$SERVICE_NAME.service"; then
    echo "Service $SERVICE_NAME exists."
	systemctl show $SERVICE_NAME -p ExecStart > $SERVICE_NAME.txt
else
    echo "Service $SERVICE_NAME does not exist."
fi

# echo "Preparing for migration (migration.ini) ..."
# pwsh -Executionpolicy remotesigned -File "${INSTALL_PATH}${FILE_SEPARATOR}migration-tools.ps1" -Action  "Initialize-Migration" -InstallPath "${INSTALL_PATH}" -ConfigPath "${mongodb.dir.configpath}" -ServiceName $SERVICE_NAME -MongoVersion "${APP-VERSION}" -ToolsVersion "${mongodb.tools.version}"

# $SHELL "${INSTALL_PATH}${FILE_SEPARATOR}mongodb.sh"
# $SHELL "${INSTALL_PATH}${FILE_SEPARATOR}servicedelete.sh"

exit 0