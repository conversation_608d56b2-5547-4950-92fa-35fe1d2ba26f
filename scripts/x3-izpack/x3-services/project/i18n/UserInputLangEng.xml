﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<izpack:langpack version="5.0"
    xmlns:izpack="http://izpack.org/schema/langpack"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://izpack.org/schema/langpack http://izpack.org/schema/5.0/izpack-langpack-5.0.xsd">

    <str id="userinput.serviceconfiguration.title" txt="'Sage X3 Services' service configuration" />
    <str id="winserviceuser"
        txt="Warning : You must provide an account and a password in order to create a service. This account must be valid and must have the &apos;log on as a service&apos; right." />
    <str id="userinput.x3services.port" txt="Service port number" />
    <str id="invalidport" txt="Invalid port: must be a number (ex: 8124, ..)" />
    <str id="userinput.x3services.username" txt="Service user" />
    <str id="userinput.x3services.winservice.password" txt="Password for this account" />
    <str id="allmandatoryfields" txt="(*) All fields are mandatory." />

    <str id="usermandatory" txt="The user name is mandatory !" />
    <str id="groupmandatory" txt="The group name is mandatory !" />
    <str id="passwordmandatory" txt="The password is mandatory !" />
    <str id="userinput.guid.clientid" txt="Client ID" />
    <str id="userinput.guid.clientsecret" txt="Client secret" />
    <str id="syracuse.generateclientid" txt="Generate Client ID" />
    <str id="syracuse.generatesecret" txt="Generate secret" />
    <str id="userinput.syracuse.usesslencryptedconnection" txt="Use SSL encrypted connection" />
    <str id="errwinsrvaccount" txt="The username or password you entered is incorrect."/>
    <str id="warnwinsrvaccount" txt="This account does not have the right to log on as a service !"/>

    <str id="userinput.syracuse.connection" txt="Connection to Sage X3 Syracuse" />
    <str id="syracuse.url.text" txt="Please indicate the http address of your 'Sage X3 Syracuse' application. Ex: http://x3.mydomain.com:8124" />
    <str id="userinput.syracuse.url.hostname" txt="Syracuse url hostname" />
    <str id="userinput.syracuse.url.port" txt="Syracuse url port" />
    <str id="userinput.syracuse.url" txt="Syracuse http(s) URL " />	
    <str id="servicehosterror" txt="Incorrect Syracuse hostname: This server doesn't answer. Check that URL and Service port are valid." />
    <str id="syracusehostmandatory" txt="Syracuse http(s) address is mandatory" />
    <str id="nodeportmandatory" txt="Syracuse port is mandatory. Ex: 8124" />
	<str id="syracuse.copyclientid" txt="Copy clipboard" /> 

    <str id="syracuse.guids" txt="The GUID and SecretId will be generated within the YAML configuration files of Sage X3 Services. Copy them in 'Sage X3 Syracuse', 'X3 services' chapter." />
    <str id="syracuse.ssl.text" txt="The configuration file 'xtrem-security.yml'  will be configured with these certificate and key, but you will need to read to 'howto guide' to complete the configuration of TLS communication." />
    <str id="copyclientid.ok" txt="ClientId copied in clipboard" />
    <str id="copyclientsecret.ok" txt="Secret copied in clipboard" />
    <str id="userinput.ssl.crtfile" txt="Client certificate file (*.crt)" />
    <str id="userinput.ssl.keyfile" txt="Client private key file (*.key)" />

</izpack:langpack>