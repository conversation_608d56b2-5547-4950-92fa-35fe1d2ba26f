﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<izpack:userinput version="5.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:izpack="http://izpack.org/schema/userinput" xsi:schemaLocation="http://izpack.org/schema/userinput http://izpack.org/schema/5.0/izpack-userinput-5.0.xsd">

    <panel id="ServiceConfiguration">
        <!-- Configure X3Services service -->
        <field id="userinput.serviceconfiguration.title" type="title" txt="Service configuration" align="left" bold="true" />
        <field type="divider" align="top" />
        <field type="staticText" align="left" id="allmandatoryfields" txt="(*)All fields are mandatory" />

        <!-- x3services.service.port -->
        <field type="text" align="left" variable="x3services.service.port" conditionid="!updatemode">
            <spec id="userinput.x3services.port" txt="Service port number" size="5" />
            <validator class="com.izforge.izpack.panels.userinput.validator.NotEmptyValidator" txt="serviceportmandatory" id="serviceportmandatory" />
            <validator class="com.izforge.izpack.panels.userinput.validator.IsPortValidator" txt="invalidport" id="invalidport" />
            <validator class="com.sage.izpack.PortValidator" txt="portinuse" id="portinuse">
                <configuration>
                    <excluded>${x3services.service.port}</excluded>
                </configuration>
            </validator>
        </field>
        <field type="space" />

        <!-- User / Password    -->
        <field type="staticText" align="left" id="winserviceuser" txt="..." conditionid="izpack.windowsinstall" />

        <field type="text" align="left" variable="winservice.username" conditionid="izpack.windowsinstall">
            <spec id="userinput.x3services.username" txt="username" size="30" />
            <validator class="com.izforge.izpack.panels.userinput.validator.NotEmptyValidator" txt="usermandatory" id="usermandatory" />
        </field>
        <field type="space" />
        <field type="password" align="left" variable="winservice.password" conditionid="izpack.windowsinstall">
            <spec txt="password">
                <pwd id="userinput.x3services.winservice.password" size="30" />
            </spec>
            <validator class="com.izforge.izpack.panels.userinput.validator.NotEmptyValidator" txt="passwordmandatory" id="passwordmandatory" />
        </field>
    </panel>


    <!-- GUID -->
    <!-- 
loginUrl: http://syracuse-server:8124

# The following clientId and secret must be set with the same value that is entered in the X3 Administration > Global settings
# Both this file and Global settings must be kept safe with restricted access to admin only.

syracuse:
  clientId: create-your-own-client-id-uuid
  secret: change-to-use-a-strong-secret-for-your-client-id 
-->
    <panel id="ServiceXtremSyracuseConnection">

        <field id="userinput.syracuse.connection" type="title" txt="Connection to Sage X3 Syracuse" align="left" bold="true" />
        <field type="divider" align="top" />
        <field type="staticText" align="left" id="syracuse.url.text" txt="Please indicate the http address of your Sage X3 Syracuse application" />

        <field type="text" align="left" variable="syracuse.service.url">
            <spec id="userinput.syracuse.url" txt="Syracuse http url" size="25" />
            <validator class="com.izforge.izpack.panels.userinput.validator.NotEmptyValidator" txt="syracusehostmandatory" id="syracusehostmandatory" />
            <validator class="com.sage.izpack.UrlHttpValidator" txt="servicehosterror" id="servicehosterror" />
        </field>

        <field type="space" />
        <field type="space" />

        <!-- GUID  "Client ID" et "Client secret" partout  -->
        <field type="staticText" align="left" id="syracuse.guids" txt="Following GUIDs have been generated in yml configuration files. Copy them in Syracuse Global settings." conditionid="izpack.windowsinstall" />

        <field type="text" align="right" readonly="true" variable="syracuse.clientid">
            <spec id="userinput.guid.clientid" size="30" txt="Client ID" />
            <validator class="com.izforge.izpack.panels.userinput.validator.NotEmptyValidator" txt="licensemandatory" id="licensemandatory" />
        </field>
        <field id="customButtonGenerate" type="custom" maxRow="1" variable="custom.syracuse.guids" conditionid="!izpack.console.installer">
            <spec>
                <!-- 
                https://izpack.atlassian.net/wiki/spaces/IZPACK/pages/491666/Button+Input+Field
                 -->
                <col>
                    <field type="button">
                        <spec id="syracuse.copyclientid" txt="Copy clipboard" successMsg="copyclientid.ok">
                            <run class="com.sage.izpack.XtremCopyClipBoardClientIdBtAction">
                                <msg id="copyclientid.error" name="error"/>
                            </run>
                        </spec>
                    </field>
                </col>
            </spec>
        </field>

        <field type="space" />
        <field type="text" align="right" readonly="true" variable="syracuse.secret">
            <spec id="userinput.guid.clientsecret" size="30" txt="Client secret" />
        </field>
        <field id="customBtGenerateSecret" type="custom" maxRow="1" variable="custom.syracuse.secret" conditionid="!izpack.console.installer">
            <spec>
                <col>
                    <field type="button">
                        <spec id="syracuse.copyclientsecret" txt="Copy clipboard" successMsg="copyclientsecret.ok">
                            <run class="com.sage.izpack.XtremCopyClipBoardClientSecretBtAction">
                                <msg id="copyclientsecret.error" name="error"/>
                            </run>
                        </spec>
                    </field>
                </col>
            </spec>
        </field>

    </panel>


</izpack:userinput>