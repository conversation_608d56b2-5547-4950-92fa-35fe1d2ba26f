#Requires -Version 5.1
<#PSScriptInfo

    .VERSION 0.1
    .GUID frdepo42-15b1-472d-a26c-388f4e0e2322
    .AUTHOR Sage X3 R&D
    .COMPANYNAME Sage
    .COPYRIGHT (c) Copyright SAGE 2006-2024. All Rights Reserved.
    .TAGS Windows
    .LICENSEURI
    .PROJECTURI
    .ICONURI
    .EXTERNALMODULEDEPENDENCIES
    .REQUIREDSCRIPTS 
    .EXTERNALSCRIPTDEPENDENCIES Powershell-5.1
    .RELEASENOTES
    .PRIVATEDATA
    .DESCRIPTION 
        Launch izPack 5 compilation

     #>
param( 
    [Parameter(Mandatory = $false)] [string] $init,
    [Parameter(Mandatory = $false)] [string] $configurationFile    
)

function Add-To-Log {
    param(
        [Parameter(Mandatory = $true)] [string] $line,
        [Parameter(Mandatory = $false)] [int] $errorLevel,
        [Parameter(Mandatory = $false)] [string] $file,
        [Parameter(Mandatory = $false)] [string] $prefixName
    )
    if ([string]::IsNullOrEmpty($file)) {
        $file = $LogFile
    }
    if ([string]::IsNullOrEmpty($ErrorFile)) {
        $ErrorFile = $LogFile
    }
    
    $prefix = ""
    if (-Not [string]::IsNullOrEmpty($prefixName)) {
        $prefix = "{0,16:n}" -f $prefixName + " - "
    }
    # Error    
    try {
        $timeStampString = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
    
        if (($errorLevel -eq 1)) {
            if (-Not ([string]::IsNullOrEmpty($ErrorFile))) {
                [System.IO.File]::AppendAllText( $ErrorFile, ($timeStampString + " - " + $prefix + $line + [Environment]::NewLine))
                elseif ( -Not ([string]::IsNullOrEmpty($file)) ) {
                    [System.IO.File]::AppendAllText($file, ($timeStampString + " - " + $prefix + $line + [Environment]::NewLine))
                }
            }
        } 
        if (($errorLevel -eq 0) -And -Not ([string]::IsNullOrEmpty($file)) ) {
            [System.IO.File]::AppendAllText($file, ($timeStampString + " - " + $prefix + $line + [Environment]::NewLine))
        }
    }
    catch {
        # Write-Host $_.Exception.Message -ForegroundColor Red
        # if (-Not ([string]::IsNullOrEmpty(($Error[0] | Out-String))) ) {    
        #     Write-Error ($Error[0] | Out-String)
        # }
    }
    
    if ($errorLevel -eq 1) {
        Write-Error "$prefix $line"
    } 
    else {
        Write-Host "$prefix $line"
    }
}

function Initialize-dependencies {
    Add-To-Log -line  "Init dependencies ... "
    $targetDir = Join-Path -Path  $PSScriptRoot -ChildPath "base"
    $targetDirLinux = Join-Path -Path  $targetDir -ChildPath "linux"
    if (!(Test-Path $targetDirLinux)) {
        New-Item -ItemType Directory -Path "$targetDirLinux"
    }
    $targetDirWin = Join-Path -Path  $targetDir -ChildPath "win"
    if (!(Test-Path $targetDirWin)) {
        New-Item -ItemType Directory -Path "$targetDirWin"
    }

    $dependencies = $hashConfig.dependencies;

    foreach ($dependencie in $dependencies) {

        if (-Not [string]::IsNullOrEmpty($dependencie.dir)) {
            $targetDir = Join-Path -Path  $PSScriptRoot -ChildPath "base"
            if (!(Test-Path $targetDir)) {
                Add-To-Log -line  "Creating dir $targetDir ... "
                New-Item -ItemType Directory -Path "$targetDir"
            }        
        }
        $dependencieDest = Join-Path -Path $targetDir -ChildPath $dependencie.name
        if (( -Not [string]::IsNullOrEmpty($dependencie.name)) -and (-not (Test-Path $dependencieDest)) -or ([string]::IsNullOrEmpty($dependencie.name))) {
            Add-To-Log -line  "$dependencieDest : NOOK "
            Add-To-Log -line  "Creating file $dependencieDest ... "

            if (-Not [string]::IsNullOrEmpty($dependencie.source)) {
                
                if ($($dependencie.source).StartsWith("http")) {
                    Add-To-Log -line  "Downloading file $($dependencie.source) ... "
                    Invoke-WebRequest -Uri $($dependencie.source) -OutFile $($dependencieDest)
                    Add-To-Log -line  "File $($dependencie.source) downloaded."    
                }
                else {
                    Add-To-Log -line  "Copy dir $($dependencie.source) to $($dependencieDest)... "
                    # Copy-Item -Path "C:\Logfiles\*" -Destination "C:\Drawings" -Recurse
                    Copy-Item -Path  "$($dependencie.source)" -Destination $($dependencieDest) -Recurse
                    Add-To-Log -line  "Dir $($dependencie.source) copied."    
                }
            }
            else {
                Add-To-Log -line  "Creating empty file $($dependencieDest) ... "
                New-Item -ItemType File -Path $dependencieDest
            }
        }
        else {
            Add-To-Log -line  "$dependencieDest : OK "
        }
    }
}

function Compile-izPackCustomActions {
    Add-To-Log -line  "Compile-izPackCustomActions. Compiling ... "

    $compileLocation = Join-Path -Path  $hashConfig.compilePathRootFolder -ChildPath "izPackCustomActions"
    Set-Location $compileLocation
    $antcompile = "ant"
    &$antcompile ("-buildfile", "build.xml")
    $customLibPath = Join-Path -Path $compileLocation -ChildPath "bin" | Join-Path -ChildPath "com.sage.izpack.jar"
    $targetDir = Join-Path -Path  $PSScriptRoot -ChildPath "izPackCustomActions"
    if (!(Test-Path $targetDir)) {
        New-Item -ItemType Directory -Path "$targetDir"
    }
    Copy-Item "$customLibPath"  "$targetDir\\com.sage.izpack.jar"
    Set-Location $PSScriptRoot
}

function Initialize-configurationFile {
    Add-To-Log -line  "Initialize-configurationFile. Create $configurationFile ... "
    $hashConfig = [PSCustomObject]@{
        compilePathRootFolder   = "C:\Dev\X3\izpack5"
        compilePath             = "C:\Dev\X3\izpack5\bin\compile.bat"
        runtimeInstallXmlSrc    = "RuntimeInstall-template.5-2-0.xml"
        runtimeInstallXmlDest   = "RuntimeInstall.xml"
        processPanelSpecSrc     = "ProcessPanelSpec-template.5-2-0.xml"
        userInputSpecSrc        = "UserInputSpec.5-2-0.xml"    
        workspace               = "C:\Dev\X3\runtime"
        javaHome                = "C:\Program Files\Zulu\zulu-11"
        javaVersion             = "11"
        setupName               = "X3RuntimeInstall.jar"
        MSCPPRedistMajorVersion = "14"
        MSCPPRedistMinorVersion = "30"
        componentVersion        = "*******"
    }

    # @REM SET REDIST_XX=14
    # @REM SET REDIST_YY=30


    $userInput = Read-Host "Where is your izPack5 root folder (ex: $($hashConfig.compilePathRootFolder))?"
    while ( [string]::IsNullOrEmpty($userInput) -or ((test-path  ($userInput.Trim())) -eq $false ) ) {
        Add-To-Log -line  "Directory $userInput not found. Please enter a valid directory."
        $userInput = Read-Host "Where is your izPack5 root folder (ex: $($hashConfig.compilePathRootFolder))?"
    }    
    $hashConfig.compilePathRootFolder = $userInput.Trim()
    $hashConfig.compilePath = $userInput.Trim() + "\bin\compile.bat"
    Add-To-Log -line  "Izpack compile: $($hashConfig.compilePath)" 

    $userInput = Read-Host "Where is your Runtime root folder (ex: $($hashConfig.workspace)) ?"
    while ( [string]::IsNullOrEmpty($userInput) -or ((test-path  ($userInput.Trim())) -eq $false ) ) {
        Add-To-Log -line  "Directory $userInput not found. Please enter a valid directory."
        $userInput = Read-Host "Where is your Runtime root folder (ex: $($hashConfig.workspace)) ?"
    }
    $hashConfig.workspace = $userInput.Trim()
    Add-To-Log -line  "Runtime root folder : $($hashConfig.workspace)" 


    $userInput = Read-Host "Where is your Java Home folder (ex: $($hashConfig.javaHome)) ?"
    while ( [string]::IsNullOrEmpty($userInput) -or ((test-path  ($userInput.Trim())) -eq $false ) ) {
        Add-To-Log -line  "Directory $userInput not found. Please enter a valid directory."
        $userInput = Read-Host "Where is your Java Home folder (ex: $($hashConfig.javaHome)) ?"
    }
    $hashConfig.javaHome = $userInput.Trim()
    Add-To-Log -line  "Java Home folder : $($hashConfig.javaHome)" 

    $userInput = Read-Host "What is the required Java minimum version (Default: $($hashConfig.javaVersion)) ?"
    if (-Not [string]::IsNullOrEmpty($userInput) ) {
        $hashConfig.javaVersion = $userInput
    }    


    $userInput = Read-Host "What is the name of your Xml file RuntimeInstall.xml (Default: $($hashConfig.runtimeInstallXmlSrc)) ?"
    if (-Not [string]::IsNullOrEmpty($userInput) ) {
        $hashConfig.runtimeInstallXmlSrc = $userInput
    }

    $userInput = Read-Host "What is the name of your Xml file ProcessPanelSpec.xml (Default: $($hashConfig.processPanelSpecSrc)) ?"
    if (-Not [string]::IsNullOrEmpty($userInput) ) {
        $hashConfig.processPanelSpecSrc = $userInput
    }

    $userInput = Read-Host "What is the name of your Xml file UserInputSpec.xml (Default: $($hashConfig.userInputSpecSrc)) ?"
    if (-Not [string]::IsNullOrEmpty($userInput) ) {
        $hashConfig.userInputSpecSrc = $userInput
    }    

    $userInput = Read-Host "What is the version of your Component (Default: $($hashConfig.componentVersion)) ?"
    if (-Not [string]::IsNullOrEmpty($userInput) ) {
        $hashConfig.componentVersion = $userInput
    }    

    $jsonString = ($hashConfig | ConvertTo-Json )
    Add-To-Log -line "Saving $configurationFile" -errorLevel 0
    $Utf8Encoding = New-Object System.Text.UTF8Encoding $False
    [System.IO.File]::WriteAllText( $configurationFile, $jsonString, $Utf8Encoding)
}

function Read-configurationFile {
    param( 
        [Parameter(Mandatory = $true)] [string] $ConfigurationFile
    ) 
    Add-To-Log -line  "Read-configurationFile $ConfigurationFile ... "

    $result = Get-Content $ConfigurationFile | ConvertFrom-Json
    Add-To-Log -line  "Config: $result"
    return $result
}

function Build-setup {

    param( 
        [Parameter(Mandatory = $true)] $hashConfig
    ) 

    Add-To-Log -line  "Building setup ..."
    $compile = $hashConfig.compilePath # "C:\Dev\X3\izpack5\bin\compile.bat"
    $setupName = $hashConfig.setupName # "X3RuntimeInstall.jar"
    $currentdir = $PSScriptRoot
    Set-Location $currentdir
    
    $Source = Join-Path -Path  $currentdir -ChildPath  $hashConfig.runtimeInstallXmlSrc
    $Destination = Join-Path -Path  $currentdir -ChildPath  $hashConfig.runtimeInstallXmlDest # "runtimeInstall.xml"
    # @REM sed -ie "s/XXX/%version%/g" "RuntimeInstall.xml"
    # @REM sed -ie "s/YYY/%JAVA_VERSION%/g" RuntimeInstall.xml 
    # @REM sed -ie "s/REDIST_XX/%REDIST_XX%/g" RuntimeInstall.xml 
    # @REM sed -ie "s/REDIST_YY/%REDIST_YY%/g" RuntimeInstall.xml 
    $occurencesToReplace = @(
        "XXX", 
        "YYY", 
        "2.999",
        "x3-services-999-win-zip",
        "x3-services-999-linux-zip") #     "x3-services-999-win-zip": "x3-services-staging.1314709-win.zip",

    $newOccurences = @( 
        $hashConfig.componentVersion, 
        $hashConfig.javaVersion, 
        $hashConfig.componentVersion,
        $hashConfig.x3Services999WinZip,
        $hashConfig.x3Services999LinuxZip
        )
    Replace-String -Source $Source  -Destination  $Destination -OccurencesToReplace $occurencesToReplace -NewOccurences $newOccurences -ErrorAction Stop
    Add-To-Log -line  "Xml file '$Destination' created"

    $Source = Join-Path -Path  $currentdir -ChildPath  $hashConfig.processPanelSpecSrc
    $Destination = Join-Path -Path  $currentdir -ChildPath  $hashConfig.processPanelSpecDest # "ProcessPanelSpec.xml"
    Replace-String -Source $Source  -Destination  $Destination -OccurencesToReplace $occurencesToReplace -NewOccurences $newOccurences  -ErrorAction Stop
    Add-To-Log -line  "Xml file '$Destination' created"

    $Source = Join-Path -Path  $currentdir -ChildPath  $hashConfig.userInputSpecSrc
    $Destination = Join-Path -Path  $currentdir -ChildPath  $hashConfig.userInputSpecDest # "UserInputSpec.xml"
    Replace-String -Source $Source  -Destination  $Destination -OccurencesToReplace $occurencesToReplace -NewOccurences $newOccurences -ErrorAction Stop
    Add-To-Log -line  "Xml file '$Destination' created"

    $args = "$($hashConfig.runtimeInstallXmlDest) -b . -o  $setupName -k  standard  -l  9"
    Add-To-Log -line  "Launching compilation:  $compile  $args"
    $exitCode = Process-Command -command "$compile"  -arguments "$args"
    # "C:\Source\X3\izpack5\bin\compile.bat"  "RuntimeInstall.xml"  -b . -o %SETUP_NAME%.jar -k standard -l 9 
    # &$compile ($hashConfig.runtimeInstallXmlDest, "-b", ".", "-o", $setupName, "-k", "standard", "-l", "9")
}



function Process-Command {

    param(
        [Parameter(Mandatory = $true, HelpMessage = "Command to execute")][string]$command,
        [Parameter(Mandatory = $true, HelpMessage = "Arguments")][string]$arguments
    )
    $proc = Start-Process -Wait -PassThru -NoNewWindow  -WorkingDirectory $PSScriptRoot -FilePath "$command" -ArgumentList "$arguments" -ErrorAction Stop
    $proc.WaitForExit()
    $proc.HasExited | out-null #  workaround to avoid $null value
    # Check if the command succeeded
    if ($proc.ExitCode -eq 0) {
        Add-To-Log -line  "command succeeded ($command $arguments)"
    }
    else {
        Add-To-Log -line "command failed with exit code $($proc.ExitCode)  ($command $arguments)" -errorLevel 1
        $errorMessage = $proc.StandardError
        if (-not ([string]::IsNullOrWhiteSpace($errorMessage))) {
            Write-Error "$errorMessage"
            Add-To-Log -line $errorMessage -errorLevel 1
        } 
    }

    return $($proc.ExitCode)
}


function Replace-String {
    param( 
        [Parameter(Mandatory = $true)] [string] $Source,
        [Parameter(Mandatory = $true)] [string] $Destination,
        [Parameter(Mandatory = $true)] [array] $OccurencesToReplace,
        [Parameter(Mandatory = $true)] [array] $NewOccurences
    ) 
    $lines = @(Get-Content $Source -Encoding utf8 )
    for ($i = 0; $i -lt $OccurencesToReplace.Length; $i++) {
        $occurenceToReplace = $OccurencesToReplace[$i]
        $newOccurence = $NewOccurences[$i]
        $lines | Out-String | ForEach-Object { $_.Replace($occurenceToReplace, $newOccurence) } |  Out-String | Set-Variable tmp
        $lines = $tmp
    }

    # -Encoding ASCII : to force encoding in UTF8 without BOM
    $Utf8NoBomEncoding = New-Object System.Text.UTF8Encoding $False
    [System.IO.File]::WriteAllText( $Destination, $lines, $Utf8NoBomEncoding)
}


#
# Begin main script
#
if (([string]::IsNullOrEmpty($configurationFile))) {   
    $configurationFile = ".\\BuildLocalSetup.json"
}
$hashConfig = [PSCustomObject]@{}
$currentdir = $PSScriptRoot
Set-Location $currentdir
$LogFile = "$( [io.path]::GetFileNameWithoutExtension($MyInvocation.MyCommand.Name) ).log"


if ((test-path $configurationFile) -eq $false) {
    Add-To-Log -line  "Error ConfigurationFile file $configurationFile doesn't exist"
    Add-To-Log -line  "Please init the configurationFile with the option  -init"

    $title = 'Start configurationFile'
    $question = 'Do you want to initialize your configurationFile ?'
    $choices = '&Yes', '&No'
    $decision = $Host.UI.PromptForChoice($title, $question, $choices, 0)
    if ($decision -eq 0) {
        Initialize-configurationFile
    }
    else {
        Add-To-Log -line  'cancelled'
        exit 1;    
    }
}
else {
    $hashConfig = Read-configurationFile -ConfigurationFile $configurationFile
}

if (-Not ([string]::IsNullOrEmpty($Init))) {

    if ($Init -eq "Config") {
        Add-To-Log -line  "Init Json configurationFile..."
        Initialize-configurationFile
    }

    if ($Init -eq "CustomLib") {
        Add-To-Log -line  "Compile izPackCustomActions\com.sage.izpack.jar ..."
        Compile-izPackCustomActions
        exit 0;
    }
    if ($Init -eq "Dependencies") {
        Add-To-Log -line  "Create Base directory with dependencies ..."
        Initialize-dependencies
        exit 0;
    }
}
 
$props = $hashConfig | Get-Member
if ((test-path $configurationFile) -and ($props.length -gt 4)) {
    Build-setup -hashConfig $hashConfig
}
else {
    Add-To-Log -line  "ConfigurationFile file $configurationFile doesn't exist or incorrect. Exit program."
    exit 2;    
}

