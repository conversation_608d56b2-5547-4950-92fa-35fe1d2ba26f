@echo off
REM 
REM Build X3Services setup with izPack 5.2 on Local Machine
REM You need to create BuildLocalSetup52.json from BuildLocalSetup52.template.json
REM 
set IZPACK_HOME=C:\Dev\X3\izpack5
set JAVA_HOME=C:\Program Files\Zulu\zulu-11

Powershell.exe -ExecutionPolicy remotesigned -File  .\BuildLocalSetup.ps1 -ConfigurationFile .\BuildLocalSetup52.json %*
@IF %ERRORLEVEL% EQU 0 GOTO NOERROR1
echo An error occured while building Setup

REM EXIT %ERRORLEVEL%
:NOERROR1
