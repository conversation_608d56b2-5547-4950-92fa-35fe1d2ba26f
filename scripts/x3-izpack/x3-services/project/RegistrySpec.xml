<izpack:registry version="5.0"
    xmlns:izpack="http://izpack.org/schema/registry"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://izpack.org/schema/registry http://izpack.org/schema/5.0/izpack-registry-5.0.xsd">

    <pack id="base" name="UninstallStuff">
        <!-- Special "pack", if not defined an uninstall key will be generated automatically -->
        <!-- The variable $APP_NAME can be only used if CheckedHelloPanel will be used
                   because there the variable will be declared. With that variabel it is possible
                   to install more as one instances of the product on one machine each with an
                   unique uninstall key. -->
        <!-- <value name="Name" keypath="SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\$APP_NAME" root="HKLM" string="$UNINSTALL_NAME" /> -->
        <!-- <value name="Version" keypath="SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\$APP_NAME" root="HKLM" string="$APP_VER" /> -->
        <value name="DisplayName" override="true" keypath="SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\$APP_NAME" root="HKLM" string="$APP_NAME" />
        <value name="DisplayVersion" override="true" keypath="SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\$APP_NAME" root="HKLM" string="$APP_VER" />
        <value name="Publisher" override="true" keypath="SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\$APP_NAME" root="HKLM" string="Sage" />
        <value name="DisplayIcon" override="true" keypath="SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\$APP_NAME" root="HKLM" string="$INSTALL_PATH\resources\icons\console32.ico" />
        <!-- <value name="HelpLink" keypath="SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\$APP_NAME" root="HKLM" string="$APP_URL"/> -->
        <value name="UninstallString" override="true" keypath="SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\$APP_NAME" root="HKLM" string="&quot;$JAVA_HOME\bin\javaw.exe&quot; -jar &quot;$INSTALL_PATH\uninstaller\uninstaller.jar&quot;" />
    </pack>

    <!-- <pack name="Documentation-PDF">
        <key keypath="SOFTWARE\IzForge\IzPack\$APP_NAME\Documentation\PDF" root="HKLM"/>
    </pack> -->
</izpack:registry>