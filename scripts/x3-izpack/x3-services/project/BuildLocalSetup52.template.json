{"compilePathRootFolder": "G:\\Sage\\Dev\\izpack5", "compilePath": "G:\\Sage\\Dev\\izpack5\\bin\\compile.bat", "runtimeInstallXmlSrc": "X3Services-template.5-2.xml", "runtimeInstallXmlDest": "X3Services.xml", "processPanelSpecSrc": "ProcessPanelSpec-template.5-2.xml", "processPanelSpecDest": "ProcessPanelSpec.xml", "userInputSpecSrc": "UserInputSpec-template.5-2.xml", "userInputSpecDest": "UserInputSpec.xml", "workspace": "G:\\Sage\\Dev\\installersX3Services\\izpack\\X3Services\\project", "javaHome": "C:\\Program Files\\Zulu\\zulu-11", "javaVersion": "11", "setupName": "X3Services-0-2.jar", "x3-services-999-win-zip": "x3-services-staging.1314709-win.zip", "x3-services-999-linux-zip": "x3-services-staging.1314709-linux.zip", "componentVersion": "7.0.11"}