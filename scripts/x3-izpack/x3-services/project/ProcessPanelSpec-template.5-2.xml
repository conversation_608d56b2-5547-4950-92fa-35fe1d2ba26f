<?xml version="1.0" encoding="utf-8" standalone="yes" ?>
<izpack:processing version="5.0"
  xmlns:izpack="http://izpack.org/schema/processing"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://izpack.org/schema/processing http://izpack.org/schema/5.0/izpack-processing-5.0.xsd">

  <logfiledir>$INSTALL_PATH</logfiledir>

  <!-- MS Windows -->
  <job name="Service installation - MS Windows">
    <os family="windows" />
    <executefile name="powershell.exe">
      <arg>-Executionpolicy</arg><arg>remotesigned</arg>
      <arg>-File</arg><arg>"${INSTALL_PATH}${FILE_SEPARATOR}install-tools.ps1"</arg>
      <arg>-InstallPath</arg><arg>${INSTALL_PATH}</arg>
      <arg>-LogPath</arg><arg>${INSTALL_PATH}${FILE_SEPARATOR}logs</arg>
      <arg>-Action</arg><arg>Install</arg>
      <arg>-ServiceDisplayName</arg><arg>${x3service.name}</arg>
      <arg>-ServiceDescription</arg><arg>${x3services.service.description}</arg>	  
      <arg>-UserName</arg><arg>${winservice.username}</arg>
      <arg>-Password</arg><arg>${winservice.password}</arg>
      <!-- <arg>-Password</arg><arg>${winservice.passwordbase64}</arg> -->
    </executefile>
  </job>
  <onFail previous="false" next="false"/>
  <onSuccess previous="false" next="true"/>

  <!-- Linux -->
  <job name="Service installation - Linux">
    <os family="unix"/>
    <executefile name="pwsh">
      <arg>-Executionpolicy</arg><arg>remotesigned</arg>
      <arg>-File</arg><arg>"${INSTALL_PATH}${FILE_SEPARATOR}install-tools.ps1"</arg>
      <arg>-InstallPath</arg><arg>${INSTALL_PATH}</arg>
      <arg>-LogPath</arg><arg>${INSTALL_PATH}${FILE_SEPARATOR}logs</arg>
      <arg>-Action</arg><arg>Install</arg>
      <arg>-ServiceDisplayName</arg><arg>${x3service.name}</arg>
      <arg>-UserName</arg><arg>${winservice.username}</arg>
      <arg>-Password</arg><arg>${winservice.passwordbase64}</arg>
    </executefile>
  </job>
  <onFail previous="false" next="false"/>
  <onSuccess previous="false" next="true"/>


</izpack:processing>
