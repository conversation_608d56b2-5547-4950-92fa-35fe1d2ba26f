{"storage": {"managedExternal": true}, "server": {"port": "${x3services.service.port}", "ssl": {"cert": "${syracuse.ssl.crtfile}", "key": "${syracuse.ssl.keyfile}"}}, "logs": {"disabledForTests": true, "domains": {"sage/xtrem-x3-sql-manager/sql": {"level": "info"}, "sage/xtrem-x3-gateway/api-helper": {"level": "info"}, "sage/xtrem-core/graphql": {"level": "info"}, "sage/xtrem-service/http": {"level": "info"}}}}