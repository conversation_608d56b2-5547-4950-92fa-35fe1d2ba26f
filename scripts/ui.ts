import * as chalk from 'chalk';
import inquirer from 'inquirer';
const ui = new inquirer.ui.BottomBar();

let bottomMessageUpdateId: any = null;
let bottomMessage = '';

const logBottom = (message = '') => {
    const chars = '/-\\|';
    let index = 0;

    if (message !== bottomMessage) {
        clearInterval(bottomMessageUpdateId);

        if (message !== '') {
            bottomMessageUpdateId = setInterval(() => {
                if (chars.charAt(index) === '') index = 0;

                ui.updateBottomBar(`${chars.charAt(index++)} ${message}`);
            }, 100);
        } else {
            ui.updateBottomBar('');
        }
    }

    bottomMessage = message;
};

const logSuccess = (message: string) => ui.log.write(chalk.green(`✓ ${message}`));
const logFail = (message: string) => ui.log.write(chalk.red(`✗ ${message}`));

interface UiProps {
    logSuccess: (message: string) => void;
    logFail: (message: string) => void;
    logBottom: (message: string) => void;
    ui: inquirer.ui.BottomBar;
}

export const withUi = (script: ({ logSuccess, logFail, logBottom, ui }: UiProps) => Promise<void>) => {
    return async () => {
        await script({ logSuccess, logFail, logBottom, ui });
        ui.close();
    };
};
