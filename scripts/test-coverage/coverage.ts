import fs from 'fs';
import * as glob from 'glob';
import istanbulLibCoverage from 'istanbul-lib-coverage';
import istanbulLibReport from 'istanbul-lib-report';
import istanbulReports from 'istanbul-reports';
import path from 'path';

const pattern = `${process.cwd()}${path.sep}@(platform|services|shopfloor|tools|x3-services|wh-services)${path.sep}*${
    path.sep
}*${path.sep}coverage${path.sep}coverage-final.json`;

const coverageConfFile = `${process.cwd()}${path.sep}scripts${path.sep}test-coverage${path.sep}coverage.conf`;

interface Dict<T> {
    [key: string]: T;
}

const coverageMaps: Dict<istanbulLibCoverage.CoverageMap> = {};

function tryLoadCoverageSummary(filename: string): any {
    try {
        return JSON.parse(fs.readFileSync(filename, 'utf-8'));
    } catch (e) {
        console.error(`failed to load ${filename} code coverage summary: ${e.message}`);
        return {};
    }
}

function tryLoadConfFile(filename: string): string[] {
    try {
        return fs.readFileSync(filename, 'utf-8').split('\n');
    } catch (e) {
        console.error(`failed to load ${filename} code coverage summary: ${e.message}`);
        return [];
    }
}

// for the moment, we suppose that all packages have diffrents names even in different domains
// if we have the same package name in different domains, we will have implement that case
const ignoredPackages = tryLoadConfFile(coverageConfFile);
console.log(`The following package will be excluded from code coverage summary: ${JSON.stringify(ignoredPackages)}`);
glob.sync(pattern).forEach((f) => {
    const pathComponents = f.split(path.sep);
    const packageName = pathComponents[pathComponents.length - 3];

    if (!ignoredPackages.includes(packageName)) {
        const domainName = pathComponents[pathComponents.length - 4];
        const productAreaName = pathComponents[pathComponents.length - 5];
        const productAreaKey = `product-area_${productAreaName}`;
        const domainKey = `domain_${productAreaName}_${domainName}`;
        const packageKey = `package_${productAreaName}_${domainName}_${packageName}`;
        const content = JSON.parse(fs.readFileSync(f, 'utf-8'));
        coverageMaps[packageKey] = istanbulLibCoverage.createCoverageMap({ ...content });

        if (coverageMaps[domainKey]) {
            coverageMaps[domainKey].merge({ ...content });
        } else {
            coverageMaps[domainKey] = istanbulLibCoverage.createCoverageMap({ ...content });
        }

        if (coverageMaps[productAreaKey]) {
            coverageMaps[productAreaKey].merge({ ...content });
        } else {
            coverageMaps[productAreaKey] = istanbulLibCoverage.createCoverageMap({ ...content });
        }
    }
});

const targetDirectory = path.resolve(process.cwd(), 'code-coverage');
if (!fs.existsSync(targetDirectory)) {
    fs.mkdirSync(targetDirectory);
}

let drops = 0;
Object.keys(coverageMaps).forEach((k) => {
    const cszProductArea = 'product-area_';
    if (k.startsWith(cszProductArea)) {
        const reportContext = istanbulLibReport.createContext({
            coverageMap: coverageMaps[k],
            defaultSummarizer: 'nested',
            dir: path.resolve(targetDirectory, `lcov_${k}`),
            sourceFinder: (file: string) => {
                const mappedFile = path.normalize(file.replace('build', '').replace('.js', '.ts'));
                return fs.readFileSync(mappedFile, 'utf-8');
            },
        });
        (istanbulReports.create('lcov') as any).execute(reportContext);
    }

    const metadata: any = { recordedAt: new Date().toISOString() };
    const components = k.split('_');
    if (components.length > 1) {
        metadata.productAreaName = components[1];
    }
    if (components.length > 2) {
        metadata.domainName = components[2];
    }
    if (components.length > 3) {
        metadata.packageName = components[3];
    }
    const newCoverageSummary = coverageMaps[k].getCoverageSummary().toJSON();
    const exportedFilePath = path.resolve(targetDirectory, `${k}.json`);
    const getStatementsPct = (coverageSummary: istanbulLibCoverage.CoverageSummaryData) =>
        coverageSummary.statements?.pct ?? 0;

    if (metadata.productAreaName === 'platform' && metadata.packageName) {
        if (fs.existsSync(exportedFilePath)) {
            const previousCoverageSummary = tryLoadCoverageSummary(exportedFilePath);
            const lastRecoredAt = previousCoverageSummary.metadata?.recordedAt;
            const previousPct = getStatementsPct(previousCoverageSummary);
            const newPct = getStatementsPct(newCoverageSummary);

            const drop = previousPct - newPct;

            // Let's take a 0.5% margin so not to block every Pull Request:
            if (drop > 0.5) {
                const upToDate = lastRecoredAt?.slice(0, 10) === metadata.recordedAt.slice(0, 10);
                console.log(
                    `Code coverage for package ${metadata.productAreaName}/${metadata.packageName} has dropped from ${previousPct}% to ${newPct}%`,
                );
                // only count the drop if the last published figures on S3 are from today
                if (upToDate) {
                    drops += 1;
                } else {
                    console.log(
                        `This drop will not count because it is uncertain! The last code coverage is outdated, got ${lastRecoredAt}, current date is ${metadata.recordedAt}`,
                    );
                }
            } else {
                console.log(
                    `Code coverage for package ${metadata.productAreaName}/${metadata.packageName} is of ${newPct}%`,
                );
            }
        } else {
            console.log(`File not found for package ${metadata.productAreaName}/${metadata.packageName}`);
        }
    }

    fs.writeFileSync(
        exportedFilePath,
        JSON.stringify(
            {
                metadata,
                ...newCoverageSummary,
            },
            null,
            4,
        ),
        'utf-8',
    );
});

if (drops > 0) {
    const failsIfCodeCoverageDrop = process.env.FAILS_IF_CODE_COVERAGE_DROP === 'TRUE';
    console.log('Unit tests have to be added. Fails if code coverage drop :', failsIfCodeCoverageDrop);
    if (failsIfCodeCoverageDrop) process.exit(1);
}
