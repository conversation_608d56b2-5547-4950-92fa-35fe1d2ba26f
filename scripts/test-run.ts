import { exec } from './utils';
import * as path from 'path';
import { withUi } from './ui';

const main = withUi(async ({ logFail }) => {
    if (!process.argv[2] || !process.argv[3] || !process.argv[4]) {
        logFail('Could not find required arguments!');
        process.exit(1);
    }
    const root = process.argv[2];
    const cwd = process.argv[3].split('test')[0];
    const file = process.argv[4];
    await exec(
        `node ${path.join(
            root,
            'node_modules/mocha/bin/_mocha',
        )} --timeout 999999 --watch-extensions ts --watch --colors "${file}"`,
        { inherit: true, cwd },
    );
});

main();
