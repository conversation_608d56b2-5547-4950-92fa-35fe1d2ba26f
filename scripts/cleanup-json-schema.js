const fs = require('fs');
const _ = require('lodash');

function readJsonFile(file) {
    return JSON.parse(fs.readFileSync(file, 'utf8'));
}

function writeJsonFile(file, obj) {
    fs.writeFileSync(file, `${JSON.stringify(obj, null, 4)}\n`, 'utf8');
}

function clean(obj) {
    _.forIn(obj, (val, key) => {
        if (_.isArray(val)) {
            val.forEach((el) => {
                if (_.isObject(el)) {
                    clean(el);
                }
            });
        } else if (_.isObject(val)) {
            if (key.startsWith('__@')) {
                delete obj[key];
            } else {
                clean(val);
            }
        }
    });
}

const file = process.argv[2];
const schema = readJsonFile(file);

clean(schema);

writeJsonFile(file, schema);
