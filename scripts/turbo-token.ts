import * as chalk from 'chalk';
import inquirer from 'inquirer';
import { existsSync, mkdirSync, readFileSync, writeFileSync } from 'node:fs';
import path from 'node:path';
import open from 'open';
import { withUi } from './ui';

const TEAM = 'team_xtrem';
const APIURL = 'https://turborepo.eu.dev-sagextrem.com';
const TURBO_CONFIG_FOLDER = path.join(__dirname, '..', '.turbo');
const TURBO_CONFIG_PATH = path.join(TURBO_CONFIG_FOLDER, 'config.json');
const GITHUB_TOKEN_REGEX = /^ghp_[a-zA-Z0-9]{36}$/;

type TurboConfig = {
    teamid: typeof TEAM;
    apiurl: typeof APIURL;
    token: string;
};

function safeParse<T>(input: string): T | null {
    try {
        return JSON.parse(input);
    } catch (err) {
        return null;
    }
}

const main = withUi(async ({ logFail, logSuccess, ui }) => {
    try {
        inquirer.ui.BottomBar = ui as any;

        const hasConfigFile = existsSync(TURBO_CONFIG_PATH);
        let hasValidConfigFile = false;
        if (hasConfigFile) {
            const turboFileContent = readFileSync(TURBO_CONFIG_PATH, 'utf-8');
            const turboConfig = safeParse<TurboConfig>(turboFileContent);
            hasValidConfigFile = Boolean(
                turboConfig !== null &&
                    turboConfig.teamid === TEAM &&
                    turboConfig.apiurl === APIURL &&
                    turboConfig.token &&
                    turboConfig.token.match(GITHUB_TOKEN_REGEX) !== null,
            );
            if (hasValidConfigFile) {
                ui.log.write(chalk.yellow(`You already have a valid config file: ${TURBO_CONFIG_PATH}.`));
            } else {
                ui.log.write(chalk.red(`Invalid config file found: ${TURBO_CONFIG_PATH}.`));
            }
        }

        const { confirm } = await inquirer.prompt<{ confirm: boolean }>([
            {
                type: 'confirm',
                name: 'confirm',
                message: 'Do you want to add a new token for Turborepo?',
                default: !hasValidConfigFile,
            },
        ]);

        if (!confirm) {
            return;
        }

        ui.log.write(
            chalk.blue(
                `Your browser will open in a few seconds.\n\nWhen it does please do the following:\n- Click on "Generate new token" and then on "Generate new token (classic)"\n- Fill in the note field with "Sage Turbo CLI"\n- Select "No expiration" under "Expiration"\n- Select the "repo" scope\n\nFinally click on "Generate token" and copy the token to your clipboard.`,
            ),
        );
        ui.log.write(
            chalk.green(
                `IMPORTANT: After creating the token click on "Configure SSO" and then "Authorize" for "Sage-ERP-X3".`,
            ),
        );
        await new Promise((res) => setTimeout(res, 5000));
        await open('https://github.com/settings/tokens');
        const { token } = await inquirer.prompt<{ token: string }>([
            {
                type: 'input',
                name: 'token',
                message: 'Please paste your token here:',
                transformer: (t) => t.trim(),
                validate: (input?: string) => {
                    if (!input?.match(GITHUB_TOKEN_REGEX)) {
                        return 'Please paste a valid Github token';
                    }
                    return true;
                },
            },
        ]);
        mkdirSync(TURBO_CONFIG_FOLDER, { recursive: true });
        writeFileSync(TURBO_CONFIG_PATH, JSON.stringify({ teamid: TEAM, apiurl: APIURL, token }, null, 2), 'utf-8');
        logSuccess(`Your configuration has been successfully saved to ${TURBO_CONFIG_PATH}.`);
    } catch (err) {
        logFail(`Failed due to the following error: ${err.message}. Please try again.`);
    }
});

main();
