'use strict';

// copy key from one package to another
// usage: node scripts/i18n/copy-from from-package-path to-package-path
const fs = require('node:fs');
const fsp = require('node:path');

const dirs = process.argv.slice(2, 4);

if (process.argv.length < 4) {
    console.log('usage: node scripts/i18n/copy-from from-package-path to-package-path');
    console.log('   ex: node scripts/i18n/copy-from platform/system/xtrem-system platform/system/xtrem-upload');
    process.exit(1);
}

function readFileContent(file) {
    try {
        return JSON.parse(fs.readFileSync(file));
    } catch (error) {
        return {};
    }
}

const i18n = Object.fromEntries(
    dirs.map((d, i) => {
        const dir = fsp.join(d, 'lib/i18n');
        return [
            i === 0 ? 'from' : 'to',
            {
                dir,
                package: readFileContent(fsp.join(d, 'package.json')),
            },
        ];
    }),
);

const fromFiles = fs.readdirSync(i18n.from.dir);

for (const file of fromFiles) {
    const toFile = fsp.join(i18n.to.dir, file);
    const i18nFrom = readFileContent(fsp.join(i18n.from.dir, file));
    const i18nTo = readFileContent(toFile);
    let dirty = false;
    Object.keys(i18nFrom).forEach((fromKey) => {
        const toKey = fromKey.replace(i18n.from.package.name, i18n.to.package.name);
        if (i18nTo[toKey] != null && i18nTo[toKey] === '' && i18nTo[toKey] !== i18nFrom[fromKey]) {
            console.log(`rewrite [${file.split('.')[0]}] ${toKey}: '${i18nFrom[fromKey]}'`);
            i18nTo[toKey] = i18nFrom[fromKey];
            dirty = true;
        }
    });
    if (dirty) {
        fs.writeFileSync(toFile, JSON.stringify(i18nTo, null, 4) + '\n');
    }
}
