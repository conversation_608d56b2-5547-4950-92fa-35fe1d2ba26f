#!/usr/bin/env node
'use strict';

// Syncs the entries of the i18n files with the ones present in the base.json file
// Missing entries will be added with an empty string value.
// Keys that don't match base.json keys will be removed.
//
// usage: node scripts/i18n/sync-entries-with-base from-package-path
//
// Q: Should we sort the keys? I did not do it because it would make the diff harder to read.
const fs = require('node:fs');
const fsp = require('node:path');
const _ = require('lodash');

if (process.argv.length < 3) {
    console.log('usage: node scripts/i18n/sync-entries-with-base from-package-path');
    console.log('   ex: node scripts/i18n/sync-entries-with-base platform/back-end/xtrem-core');
    process.exit(1);
}

const dir = process.argv[2];
const i18nDir = fsp.join(dir, 'lib/i18n');

function readFileContent(file) {
    return JSON.parse(fs.readFileSync(file));
}

function writeFileContent(path, content) {
    console.log('writing', path);
    fs.writeFileSync(path, JSON.stringify(content, null, 4) + '\n');
}

const paths = fs
    .readdirSync(i18nDir)
    .filter((file) => file !== 'base.json')
    .map((file) => fsp.join(i18nDir, file));

const baseEntries = readFileContent(fsp.join(i18nDir, 'base.json'));

paths.forEach((path) => {
    const entries = readFileContent(path);
    const missingKeys = _.difference(Object.keys(baseEntries), Object.keys(entries));
    const extraKeys = _.difference(Object.keys(entries), Object.keys(baseEntries));
    if (missingKeys.length === 0 && extraKeys.length === 0) return;

    missingKeys.forEach((key) => {
        entries[key] = '';
    });
    extraKeys.forEach((key) => {
        delete entries[key];
    });

    writeFileContent(path, entries);
});
