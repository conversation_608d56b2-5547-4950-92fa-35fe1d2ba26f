# User

To be able to access the system a user is required.

## General tab

The general tab contains the fields required to create a user record.

### Active flag

A user record can be set to either active or inactive. Only active users will have access to the system.

### First name

This field indicates the user's first name. This information is optional.

### Last name

This field indicates the user's last name. This information is mandatory.

### Email

This field indicates the user's email address. The structure of the value entered is checked to verify its conformity with the email address format. This field is required.

### Selected dashboard

### Authorisation group

This field provides a list of autorisation groups the user is a member of. According to the groups a user is a member of, several roles will be available for the user, thus defining the access rights for the user.

### Photo

A user's photo can be added or removed from the record.

### Is Demo Persona

Is this user a demo persona? A demo persona user cannot be used to log in. This field is availaible only on tenant where the `isDemoTenant` service option is active.

### Is API User

Is this user an API user? An API user can only be used by Third-Party application using a Bearer authorization header. The email of an API user must be in the form of `api-{developperId}@localhost.domain`, where `{developperId}` is the ID of the API client created for your app on the [Sage Developpers App Registry](https://app-registry.sage.com/developers).

**Restrictions**: An API user cannot be an admin or a demo persona.

## Associated roles and activities tab

This tab will display a list of the roles the user is linked to with activities defined for that role.

## Associated site group tab

This tab will display a list of the site groups associated to the user.
