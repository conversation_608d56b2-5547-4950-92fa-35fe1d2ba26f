PATH: XTREEM/Standards/Github+Standards

# Github Standards

## Branch naming standards

### Goal

The goal of this standard is to facilitate the use of **branches**, to retrieve easily a dedicated branch among others.

### Branch naming

In Xtrem, the branch naming is structured in three parts as follows:

`[release]/<type>/[team]/[jira us]-<title>` with `/` as separator character.

-   **release**: To mention when the branch is created from a release branch.

-   **type**: mandatory. Type of branch, according to the grid below.

-   **team**: optional in platform, mandatory for applicative teams. Name or abbreviation of your team on which you agree.

-   **jira us**: optional in platform, mandatory for applicative teams. User story code in Jira starting with XT-.

-   **title**: mandatory. Title of the user story. Must be short and clear.

#### Case

-   Use lowercase letters for the type and the team criteria, as 'Test folder' and 'test folder' are the same for Windows.
-   Use lowercase letters for the title except for proper noun and acronym (e.g. GraphQL, CLI )

#### Remarks

⚠️ Do not use "release" and "master" string for your branches to avoid confusion.
⚠️ User code is not needed anymore.

#### Example

feat/xtrem/X3-191608-add-lookups-on-references

#### List of types

| Type | Designation   | Description                                       |
| ---- | ------------- | ------------------------------------------------- |
| feat | Features      | A new feature                                     |
| fix  | Bug Fixes     | A bug Fix                                         |
| docs | Documentation | Documentation only changes                        |
| test | Tests         | Adding missing tests or correcting existing tests |
| tra  | Translation   | Terminology review / Translation                  |

### Branch deletion

The "delete branch after PR merge option" is activated. Thus, the branch is automatically deleted after the merge into the master branch.

Delete your branch locally using this syntax:
`git fetch --prune`

This removes all your local branches where the branch no longer exists on the remote.

## Commit message standards

### Presentation

The Conventional Commits provides an easy set of rules for creating an explicit commit history; which makes it easier to write automated tools on top of.

Adopting this convention allows to harmonize the commit messages for Xtrem.

Thus anyone will have a quick understanding of any commit message, and the generated change logs will be clearer and well organized.

### Xtrem Commit message Syntax

In Xtrem, the commit messages base on the conventional commits should be structured as follows:

`<type>[(package)]: <US code><commit title>`

-   **type**: mandatory. Type of the commit, following the grid above.

-   **package**: optional. Code of the package, when related to a package. Code of the main package, when related to several packages.

-   **US code**: mandatory. code of the user story. List of User stories, when related to several ones. This allows to have the link to the PR in Jira.

-   **commit title**: mandatory. title of the commit. Must be short and clear.

## Pull Request title standards

Please, **refer to the commit standards**, as Pull Request title and commit have the same Standards.
