PATH: XTREEM/Standards/Methodology+Documents

# Methodology documents

## Useful extensions

-   docs-markdown by Microsoft
-   Alt+M to open the Markdown Menu
-   Draw.io Integration by <PERSON><PERSON>
-   Markdown Checkboxes by <PERSON>
-   Markdown Emoji by <PERSON>
-   Markdown Preview GitHub Styling by <PERSON> by <PERSON>

## GitHub documentation

[Markdown Basic writing and formatting syntax on GitHub](https://help.github.com/en/github/writing-on-github/basic-writing-and-formatting-syntax)

## Extension

If you've installed the docs-markdown extension, note some things don't work

-   you cannot include an image in the cell of a grid
-   you cannot include an image in a column

## Line Break in a table

Useful in a table for e.g.

-   Add `<br>` before the line break

## Emoji

Navigate to [emoji markup list](https://gist.github.com/rxaviers/7360908)

## Checklist

-   [x] Getting started
-   [ ] Installation
