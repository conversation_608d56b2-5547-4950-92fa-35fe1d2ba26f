PATH: XTREEM/Standards/Naming+Standards

# Naming Standards

This page describes the naming standards that must be applied in the framework of the Xtrem project.

## General Naming Rules

> ⚠️ **[IMPORTANT]**
>
> -   Name should be short and follow the terminology defined by Tradoc team.
> -   Use only singular term and make sure the naming is consistent across the product.
> -   Write in **US English** only.

## Terminology

A **Glossary project** is in progress by the Tradoc team. [See here for more details.](https://teams.microsoft.com/l/team/19%3a31906b495c9148519fdcad47ccea024a%40thread.tacv2/conversations?groupId=3a286e15-1305-48fc-8cdf-eb5f5b3ef326&tenantId=3e32dd7c-41f6-492d-a1a3-c58eb02cf4f8)

The **Glossary Objective** is to create **one terminology resource** with definitions for the developers, PBAs, Customer Support, Professional Services etc. in order to ensure terminology consistency within the product.

So we need to use a shared and agreed terminology in what we showcase to the end users. A particular attention is needed for the **enums** and **titles** in pages, and for **messages** in nodes and pages.

The **PM** and **PO** define in the 'UX Consideration' of the specification, the titles to be displayed on the screen.
Use [this channel](https://teams.microsoft.com/l/channel/19%3a539d3adf1dd24395bdbdfe4f362f7af9%40thread.tacv2/terminology-support?groupId=46560869-b2b0-4805-bc3a-d031e2b373da&tenantId=3e32dd7c-41f6-492d-a1a3-c58eb02cf4f8) ** to **discuss\*\* anything that relates to terminology - PO, Development, PM, Tradoc being also member of the channel.

The **Tradoc** team reviews the **enums**, **page titles** and **messages** on a regular basis in GitHub for each package at this location: services/xtrem-<package>/lib/i18n/base.json

[See more detail in this process.](https://confluence.sage.com/x/3EZqDg)

## Naming Standards Details

Navigate to [this page](https://medium.com/better-programming/string-case-styles-camel-pascal-snake-and-kebab-case-981407998841) for details on kebab, camel, pascal case.

| **Object**                                                      | **Naming rule**                                                                                                                          | **Case Style** | **Detail**                                                                                                                                                                                                                                                                                                                                                                        |
| --------------------------------------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------- | -------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Package Name**                                                | **xtrem-package-name**                                                                                                                   | **kebab-case** | **xtrem**: Prefix of the package name <br>**package-name**: <br>- Choose a meaningful applicative **English** name <br>- The name can be composed of two names if it helps the understanding (e.g. xtrem-master-data) <br>- **Be consistent with the other packages naming**                                                                                                      |
| **Package Features Name**                                       | **xtrem-orignPackage-featureName-featureValue** <br>e.g. xtrem-finance-legislation-fr                                                    | **kebab-case** | **xtrem**: Prefix of the package <br>**originPackage**: Package name on which this package is based <br>**featureName**: feature name e.g. legislation <br>**featureValue**: feature value e.g. fr. when you when to group values, use the values: group1, group2 etc. In this case, comment which values are managed here.<br>- **Be consistent with the other packages naming** |
| **Folder Name**                                                 | **folder-name**                                                                                                                          | **kebab-case** | Follow the [standard architecture of package](https://confluence.sage.com/display/XTREEM/Package+Methodology)                                                                                                                                                                                                                                                                     |
| **File Name - General Rule**                                    | **file-name.extension**                                                                                                                  | **kebab-case** | **extension** <br>- .ts for scripts <br>- .csv for test data files <br>- .json <br>- .md for documentation                                                                                                                                                                                                                                                                        |
| **File Name - Enum**                                            | **enum.ts**<br> e.g. unit-type.ts                                                                                                        | **kebab-case** | Do not include "-enum" in the name.                                                                                                                                                                                                                                                                                                                                               |
| **File Name - Data type**                                       | **data-type.ts** <br>e.g. quantity.ts                                                                                                    | **kebab-case** | Do not include "-data-type" in the name.                                                                                                                                                                                                                                                                                                                                          |
| **File Name - Activity**                                        | **activity.ts** <br>                                                                                                                     | **kebab-case** | Do not include "-activity" in the name.                                                                                                                                                                                                                                                                                                                                           |
| **File Name - Node**                                            | **node.ts**<br> e.g. company.ts                                                                                                          | **kebab-case** | - The node file name MUST be **unique** within the Xtrem project.                                                                                                                                                                                                                                                                                                                 |
| **File Name - Node inheritance**                                | **base-node.ts**<br> e.g. base-resource.ts <br> **sub-node.ts**<br> e.g. labor-resource.ts                                               | **kebab-case** | - the **base node** is prefixed with **"base"**.<br>- The **sub-node** is suffixed with the main node.                                                                                                                                                                                                                                                                            |
| **File Name - Node extension**                                  | **node-extension.ts**<br> e.g. company-extension.ts                                                                                      | **kebab-case** | **node extension**:<br>- starts with the **main node name** <br>- suffixed with **"extension"**                                                                                                                                                                                                                                                                                   |
| **File Name - Page**                                            | **page.ts**<br> e.g. company.ts <br> e.g. work-order-inquiry.ts                                                                          | **kebab-case** | - The page name must be the **same as the node file** <br> - for inquiry pages use the suffix **"inquiry"**                                                                                                                                                                                                                                                                       |
| **File Name - Unit Test**                                       | [Naming rules](https://confluence.sage.com/display/XTREEM/Tests+Standards)                                                               | **kebab-case** | - The naming rules depend on the type of the unit test.                                                                                                                                                                                                                                                                                                                           |
| **File Name - Test Data**                                       | **test-data.csv** <br>For e.g. company.csv                                                                                               | **kebab-case** | - The name must be the **same as the node file** <br>- Suffixed by csv                                                                                                                                                                                                                                                                                                            |
| **Node Name - General Rule**                                    | **NodeName**                                                                                                                             | **PascalCase** | - The node name MUST be unique in the Xtrem project <br>- The node name must be the same as the node file<br>- Up to 55 characters <br>- Start with a letter <br>- Ascii letters and digits                                                                                                                                                                                       |
| **Node Name - Node with Vital References or Vital Collections** | **Node.ts <br>NodeComplement.ts** <br>e.g. <br>- Parent node: MiscellaneousStockReceipt<br> - Child Node: MiscellaneousStockReceiptLines | **PascalCase** | A vital reference describes a reference to a child object that can't be standalone. The child object is usually updated in the context of its parent object, rather than in isolation. <br>- The name of the child node is the concatenation of the parent node name and the role played by the child node". So the child node name is always prefixed by the parent node name    |
| **Class**                                                       | **ClassName**                                                                                                                            | **PascalCase** | - Class: same rules as nodes <br>- Sub class: same rules as sub nodes                                                                                                                                                                                                                                                                                                             |
| **Interface**                                                   | **InterfaceName**                                                                                                                        | **PascalCase** |                                                                                                                                                                                                                                                                                                                                                                                   |
| **Enum**                                                        | **EnumName** <br> e.g. UnitType                                                                                                          | **PascalCase** | Do not suffix the EnumName with the string "Enum"                                                                                                                                                                                                                                                                                                                                 |
| **Function**                                                    | **functionName <br>- functionParameter** <br>For e.g. fetchCurrency<br> (not currencyFetcher)                                            | **camelCase**  | - **Function name** must be a **verb**, not a noun <br>- **Note** that **unused parameters** should be prefixed by an underscore (for e.g. \_fooBar)                                                                                                                                                                                                                              |
| **Property <br>Method**                                         | **propertyName <br>methodName <br>methodParameter**                                                                                      | **camelCase**  | - For **property** naming use:<br> _ Up to 55 characters<br> _ Start with a letter<br> _ Ascii letter and digits<br> - The **underscore** \_ prefix is reserved for **system properties**<br> _ \_id and \_uid are system properties<br>**Note** that **unused parameters** should be prefixed by an underscore (for e.g. \_fooBar)                                               |
| **Variable**                                                    | **variableName** <br> For e.g. currentLegislationid                                                                                      | **camelCase**  |                                                                                                                                                                                                                                                                                                                                                                                   |
| **Enum Members**                                                | **variableName** <br> For e.g. productionDeclaration                                                                                     | **camelCase**  |                                                                                                                                                                                                                                                                                                                                                                                   |
| **Boolean properties**                                          | [Naming rules](https://confluence.sage.com/display/XTREEM/Boolean+properties)                                                            | **camelCase**  |                                                                                                                                                                                                                                                                                                                                                                                   |
| **Property naming according to its attribute or event**         | [Naming rules](https://confluence.sage.com/display/XTREEM/Property+naming+according+to+its+attribute+or+event)                           | **camelCase**  |                                                                                                                                                                                                                                                                                                                                                                                   |
| **Localized Message - Message Key**                             | **message-key** <br> For e.g. price-overlap                                                                                              | **kebab-case** |                                                                                                                                                                                                                                                                                                                                                                                   |
| **Notifications**                                               | [Naming rules](https://confluence.sage.com/display/XTREEM/Notifications)                                                                 |                |                                                                                                                                                                                                                                                                                                                                                                                   |
| **Reports**                                                     | [Naming rules](https://confluence.sage.com/display/XTREEM/Reports)                                                                       | **camelCase**  |                                                                                                                                                                                                                                                                                                                                                                                   |
| **Service Options**                                             | **can**+_Verb_                                                                                                                           | **camelCase**  |                                                                                                                                                                                                                                                                                                                                                                                   |
