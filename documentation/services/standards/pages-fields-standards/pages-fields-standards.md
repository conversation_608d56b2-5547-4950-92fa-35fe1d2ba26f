PATH: XTREEM/Standards/Pages+fields+Standards

# Pages Fields Standards

## Contextual label for field titles

The goal is to display a title on a page that is defined according to data. This title with variants can be set for a field, a column, a block, a tab, or a page title.

## How does it work?

Fill in the **title attribute** with the **ui.localiseEnumMember** function to display the contextual label. Indeed, this function returns the title according to the input data and the variant stored in an **enum**.

## Example

The title of the **Region** field in an address must be adapted as it has a different meaning according to the country:

-   If the country is the **USA**, the label must be **State**
-   If the country is **France**, the label must be **Division**
-   If the country is **Italy** or is not set, the label must be **State/County/Region**.

Firstly, the **region-label** enum in the **xtrem-structure** package contains the variants of this title.

```
import { EnumDataType } from '@sage/xtrem-core';

export enum RegionLabelEnum {
    state = 1,
    department = 2,
    land = 3,
    province = 4,
    county = 5,
    stateCountyRegion = 6,
}

export type RegionLabel = keyof typeof RegionLabelEnum;

export const regionLabelDataType = new EnumDataType<RegionLabel>({ enum: RegionLabelEnum, filename: __filename });
```

Secondly, the **regionLabel** property of the **country** allows to specify the variant of the title used for this country.

-   For USA, the regionLabel is state = 1
-   For France, the regionLabel is department = 2
-   For Italy, the regionLabel is stateCountyRegion = 6.

Thirdly, the **ui.localizeEnumMember** function returns the variant according to the country, in the **address** page from the **xtrem-data-base** package.

Title definition on page loading.

```
@ui.decorators.page<Address>({
    title: 'Address',
    category: 'SETTINGS',
    node: '@sage/xtrem-master-data/Address',
    module: 'master-data',
    onLoad() {
        this.deleteAddress.isDisabled = !this.$.values._id;
        this.$.page.title = `${ui.localize('@sage/xtrem-master-data/pages__address____title', 'Address')} ${
            this.$.values._id ? ' - ' + this.$.values.name : ''
        }`;
        this.loadRegionTitle();
    },

```

Title definition on country value change.

```
    @ui.decorators.referenceField<Address>({
        parent() {
            return this.mainBlock;
        },
        title: 'Country',
        width: 'medium',
        node: '@sage/xtrem-structure/Country',
        valueField: 'name',
        minLookupCharacters: 1,
        canFilter: true,
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Name', canFilter: true }),
            ui.nestedFields.text({ bind: 'id', title: 'ID', canFilter: true }),
            ui.nestedFields.text({ bind: 'continent', title: 'Continent' }),
            ui.nestedFields.select({ bind: 'regionLabel', isHidden: true }),
        ],
        onChange() {
            this.loadRegionTitle();
        },
```

Function to define the title according to the country.

```
    loadRegionTitle() {
        this.region.title = this.country.value?.regionLabel
            ? ui.localizeEnumMember('@sage/xtrem-structure/RegionLabel', this.country.value.regionLabel)
            : ui.localizeEnumMember(
                  '@sage/xtrem-structure/RegionLabel',
                  '6', //RegionLabel.stateCountyRegion /** '6' regionLabel.stateCountyRegion */,
              );
    }
```
