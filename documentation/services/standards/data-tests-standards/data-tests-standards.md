PATH: XTREEM/Standards/Data+Tests+Standards

# Data Tests Standards

## Data Files Construction

There is one data file per **node** and per **node extension**. <br>
There is one data file per **sub-node** including the columns of the **base-node**. <br>

## Data Files Storage

The **test** layer is used for to store test data.

A CSV file is located in the same package of the node it refers to, under the path: <br>
**xtrem-{package}/data/layers/test**

A CSV file is located in the same package of the node-extension it refers to, under the path: <br>
**xtrem-{package}/data/extension-layers/test**

## Data Files Naming

### Naming syntax

**`name.csv`** name being:

-   the name of the _node_ it refers to.
-   the name of the _node extension_ it refers to without the "extension" suffix.

[cf. the naming standards](https://confluence.sage.com/display/XTREEM/Naming+Standards)

## CSV File structure

When creating new nodes you **MUST** specify an **\_id column** in the unit test data to avoid issues when deploying to the cloud.

[Follow the standards for to assign a value to the \_id in sub-nodes.](https://confluence.sage.com/x/KSKUE)
