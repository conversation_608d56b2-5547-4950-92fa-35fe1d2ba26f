PATH: XTREEM/Standards/Intacct+Xtreem+Mapping

# Intacct Xtreem Mapping

## Mapping

The mapping between **Intacct** and **Xtreem** allows to updates data from Intacct to Xtreem and vice-versa.

## Packages organization

-   The **business logic** must remain in the **Xtreem** business packages (e.g. xtrem-purchase).
-   The **xtrem-intacct-gateway** package is dedicated to the mapping with Intacct without business logic.
-   Xtreem has to work properly with and without the xtrem-intacct-gateway package as it is linked or not to Intacct.

## How to extend your functions for the mapping?

### Node-extension and page-extension

In the xtrem-intacct-gateway package,

-   Create a node extension and a page extension to extend your function to the mapping, when needed.
-   Create a dedicated node only for an Intacct table (e.g. group-unit-of-measure.ts needed for the unit measure conversion).

### Mandatory property

Each node mapped with Intacct must have the **intacctId** property. This identifier is used to link records of both sides: Intacct and Xtreem.

### Property to map

-   If an Xtreem property exists, **DO NOT redeclare it** in the xtrem-intacct-gateway package.
-   If an Xtreem property does not exist, create it in the node extension in the xtrem-intacct-gateway package. Naming of this property:
    -   Use the intacct property name
    -   If this name is confusing with the Xtreem business logic, prefix it with "intacct".

## Compliancy

### General

-   Refer to the methodology.
-   Do not hesitate to ask Benoît, the referent on this topic.

### Purchasing and Sales

Pay attention to the consistency between the Purchasing and Sales packages, as they are functionally closed. Thus,

-   Use the same name for the same property, method, function in both packages.
-   Reuse code when possible.
