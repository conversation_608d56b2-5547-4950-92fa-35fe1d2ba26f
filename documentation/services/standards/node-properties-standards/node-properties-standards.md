PATH: XTREEM/Standards/Node+Properties+Standards

# Node Properties - Standards

## Generic Properties - Definition

Below are listed **node properties that may be found in any node:**

| **Property Name** | **Type (for the moment)** | **Generic Data Type** | **When is it needed?**                                                                                                                                                                                                                  |
| ----------------- | ------------------------- | --------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **\_id**          | Numeric                   |                       | [System Property](https://confluence.sage.com/x/5PMdDg). Automatically managed by the framework in all nodes <br>This is the primary key of all nodes. This property will be allocated to each record inserted using an auto-increment. |
| **name**          | String, max length = 80   | name                  | A brief one-line name of the object                                                                                                                                                                                                     |
| **id**            | String, max length = 30   | id                    | Optional. Unique identifier (user oriented) on setup or master data.<br> It contains the iso code in the country and devise nodes. In this case, its label is 'Iso code'.                                                               |
| **number**        | String, max length = 24   | documentNumber        | Optional. Unique identifier on documents                                                                                                                                                                                                |
| **description**   | String, max size = 4Ko    | description           | Optional. A detailed description of the object. Markdown like                                                                                                                                                                           |
| **isActive**      | Boolean                   |                       | Should be defined on all nodes for settings and master data                                                                                                                                                                             |

## Properties - Default Value

> ⚠️ **[CAUTION]**
>
> -   Do not initialize your properties by the default value according to the type, as it is automatically done, as follows:
> -   string: default value= ''
> -   numeric: default value= 0
> -   boolean: default value= 'false'

Use the **isNullable** attribute **only** when an applicative distinction is needed between the default value and null.

**isNullable** is typically used for an optional reference property.

When a property is declared nullable it has to be **typed as nullable**.
Example:

```ts
@decorators.referenceProperty<BaseResource, 'resourceGroup'>({
    isStored: true,
    isNullable: true,
    node: () => xtremMasterData.nodes.ResourceGroup,
})
readonly resourceGroup: Reference<xtremMasterData.nodes.ResourceGroup | null>;
```

> ⚠️ **[WARNING]**
> If you plan to use a nullable property in a unique index. See the [node indexes](https://confluence.sage.com/display/XTREEM/4+Indexes) page for more details on the limitations.
