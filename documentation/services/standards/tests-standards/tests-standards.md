PATH: XTREEM/Standards/Tests+Standards

# Unit Tests (UT) Standards

Please, find below the **naming standards** for the different types of tests, and then, their **storage standards**.

Definition for each types of tests comes for the ISTQB Glossary (International Software Testing Qualifications Board)
https://glossary.istqb.org/app/en/search/

## Unit testing definition (ISTQB)

A test level that focuses on individual hardware or software components.

## Mocha

### Naming convention

#### Files naming

`name[-detail][-legislation].ts`

-   **name**: node name or class name or function name.
-   **detail**: optional. purpose of the test.
-   **legislation**: optional. 3 char. legislation or a dedicated country team.
-   **extension**: .ts

### Examples

-   sales-quote-revision.ts
-   price-search-specific-context-fra.ts
-   sales-invoice-credit-memo.ts
-   sales-return.ts

## GraphQL

There is one folder for each GraphQL test. Le naming standards are to apply to the folder naming.

### Naming convention

#### Folders naming

`node-request[-method][-detail][-legislation]`

-   **node**: node name
-   **request**: query, create , update , read, delete , launch.
-   **method**: optional. method if used.
-   **detail**: optional. purpose of the test if needed. (e.g. `fail` for tests of errors)
-   **legislation**: optional. 3 char. legislation if used.

##### Examples

-   company-read
-   currency-update
-   sales-order-create
-   sales-order-create-fail (test of errors)
-   sales-shipment-confirm (function or method)

#### Files naming

Tests are organized into 2 or 3 files named as described below according the type of tests. Note that the difference between, the two types of tests is on the extensions, and the presence or not of the parameter.json file.

For GraphQL tests **with static input/output**, implement these two files:

1. parameter.json (only when using environment variable or mock)
1. request.graphql
1. response.json

For **parametric** GraphQL tests, implement these three files:

1. parameter.json
2. request.graphql.hbs
3. response.json.hbs

# Integration Tests (IT) Standards

## Integration testing Definition (ISTQB)

A test level that focuses on interactions between components or systems.

## Integration tests (Cucumber)

### Naming convention

#### Files naming

`page/identifier[-detail][-legislation].feature`

-   **page/identifier**: use a page title for a test focused on a page otherwise use a relevant identifier.
-   **detail**: optional. purpose of the test
-   **legislation**: optional. 3 char. legislation or a dedicated country team.
-   **extension**: .feature

##### Examples

-   sales-order.feature
-   sales-invoice-por.feature
-   sales-quote-revision-pol.feature

#### Feature codification

To ensure the unicity and to avoid reporting issue, make sure the Feature in the cucumber test, has the same name as the feature file name.

-   Feature: `page/identifier[-detail][-legislation]`

#### How to deactivate a feature file

When the feature file need to be deactivated, the following naming convention is required:

-   `page/identifier[-detail][-legislation].feature.deactivated`
-   **extension**: .feature.deactivated

# Functional Tests (FT) Standards

## Functional testing definition - (ISTQB)

Testing performed to evaluate if a component or system satisfies functional requirements.

## Functional tests (Cucumber)

### Naming convention

#### Files naming

`productarea-type[-detail][-legislation].feature`

-   **productarea**: it corresponds to the product area of the test package
-   **type**: CRUD or FLOW
-   **detail**: optional. purpose of the test
-   **legislation**: optional. 3 char. legislation or a dedicated country team.
-   **extension**: .feature

##### Examples

-   distribution-crud.feature
-   distribution-flow-invoiceposting-por.feature​
-   reference-data-crud-address-unit.feature

#### Feature codification

To ensure the unicity and to avoid reporting issue, make sure the Feature in the cucumber test, has the same name as the feature file name.

-   Feature: `productarea-type[-detail][-legislation]`

#### How to deactivate a feature file

When the feature file need to be deactivated, the following naming convention is required:

-   `productarea-type[-detail][-legislation].feature.deactivated`
-   **extension**: .feature.deactivated

## Prerequisites tests (Cucumber)

-   The prerequisites tests have to be created in the xtrem-0-prerequisites-test test package
-   Before the feature name, add the tag corresponding to the related product area
    -   @distribution
    -   @inventory
    -   @manufacturing
    -   @reference_data

### Naming convention

#### Files naming

`productarea-type[-detail][-legislation].feature`

##### Examples

-   prerequisites-crud-intacct-config.feature

#### Feature codification

To ensure the unicity and to avoid reporting issue, make sure the Feature in the cucumber test, has the same name as the feature file name.

-   Feature: `productarea-type[-detail][-legislation]`

#### How to deactivate a feature file

When the feature file need to be deactivated, the following naming convention is required:

-   `productarea-type[-detail][-legislation].feature.deactivated`
-   **extension**: .feature.deactivated

# Smoke Tests (ST) Standards

## Smoke testing definition - (ISTQB)

A test suite that covers the main functionality of a component or system to determine whether it works properly before planned testing begins.

## Smoke tests (Cucumber)

The smoke tests are aimed at testing all the pages of their relative applicative package.

-   smoke-test-static (sts): aimed at opening the XTreeM pages without loading any data.
-   smoke-test-data (std): aimed at opening the XTreeM pages of the applicative package for a specific record.

### Naming convention

#### Files naming

-   For smoke tests data: `smoke-test-data.feature`
-   For smoke tests static: `smoke-test-static.feature`

#### Feature codification

For reporting purpose and to ensure the unicity, make sure the Feature in the cucumber test, has the same name as the feature file name.

-   Feature: `smoke-test-data`
-   Feature: `smoke-test-static`

#### Examples

-   Please refer to the smoke-test-data.feature or smoke-test-static.feature located in the path services/applications/xtrem-purchasing/test/cucumber
-   Please respect the following naming conventions:

    -   1: Applicative package defined as cucumber tag, to facilitate the reexecution of a given smoke test. ( please use "\_" separator as the "-" separator doesn't work to filter the tests.)
    -   2 / 3: The scenario description is defined with the following naming convention:

        -   `smoke-test-type \ <Page> or applicative-package \ Execution mode \ Scenario description`

            -   Smoke test type: sts (smoke-test-static) or std (smoke-test-data).
            -   Page or applicative package: Either the "page" is defined as parameter or if the page is unknown, it is defined with the code of the applicative package
            -   Execution mode: Desktop or Tablet or Mobile.
            -   Scenario description: Short description explaning the purpose the scenario.

-   Example of smoke-test-static

    ![Smoke tests example](assets/images/smoke-tests-examples-01.png)

#### How to deactivate a feature

When the feature file need to be deactivated, the following naming convention is required:

-   `smoke-test-data.feature.deactivated`
-   `smoke-test-static.feature.deactivated`
-   **extension**: .feature.deactivated

# Tests storage

## Unit Tests storage

Please, store your unit tests according to this schema.

```@sage
     xtrem-{package}
         test
             mocha
                 classes
                     mocha tests
                 functions
                     mocha tests
                 nodes
                     mocha tests
                 fixtures
                     mocha tests
             graphql
                     GraphQL tests
                         parameter.json (mostly for parametric tests)
                         request.graphql(.hbs)
                         response.json(.hbs)
```

Unit tests are stored depending on the language used for their script (**mocha**, **graphQL** or **Cucumber**).

A mocha test is based on a **class**, **function** or a **node**. Thus, its storage is done accordingly.

There is the possibility to create a structure that contains a set of parameters that will be used by a test. This is interesting to make data persistent, for example. These scripts are stored in the **fixtures** folder.

Each **GraphQL test** is stored in its dedicated **folder**.

## Integration tests storage

Please, store your integration tests according to this schema.

```@sage
        xtrem-{package}
            test
                cucumber
                    `page/identifier[-detail][-legislation].feature`
```

## Functional tests storage

Please, store your functional tests according to this schema.

```@sage
        functional-tests
            xtrem-{productarea}-test
                test
                    `productarea-type[-detail][-legislation].feature`
```

## Smoke tests storage

Please, store your smoke tests according to this schema.

```@sage
        xtrem-{package}
            test
                cucumber
                    `smoke-test-data.feature`
                    `smoke-test-static.feature`
```
