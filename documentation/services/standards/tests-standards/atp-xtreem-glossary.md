PATH: XTREEM/Tests/ATP+XTreeM+Glossary

# ATP XTreeM Glossary

## Cloud-ops / devops Glossary

![environment v1 vs v2 architecture](assets/images/atp_environment-architecture.png)

-   Environment

    -   The environment corresponds to the localization where the user will be connected to access to the application. The environment is bound to a domain name (ex eu.sageintacctmanufacturing.com).
    -   An environment can have one or several clusters.

    | Environment | URL Suffix               |
    | ----------- | ------------------------ |
    | dev         | eu.dev-sagextrem.com     |
    | qa          | na.qa-sagextrem.com      |
    | pp          | eu.pp-sagextrem.com      |
    | pdeu        | internal.eu.erp.sage.com |
    | pdna        | internal.na.erp.sage.com |

-   Cluster

    -   A cluster is an isolated instance of XTreeM application including all its : graphQL api containers, web page, database, communication queue. When you run XTreeM locally on your machine you can consider you run your own local cluster.
    -   On the cloud on the same environment, we can host multiple clusters isolated from each other.
    -   A cluster can have one or several tenants.
    -

-   Tenant

    -   Single Tenant: A single instance of the software and supporting infrastructure serve a single customer. With single tenancy, each customer has his or her own independent database and instance of the software.
    -   Multi-Tenant: Multi-tenancy means that a single instance of the software and its supporting infrastructure serves multiple customers. Each customer shares the software application and also shares a single database. Each tenant’s data is isolated and remains invisible to other tenants.
    -   For XTreeM, multi-tenant infrastructure is used.
    -   In Environment V1, the tenant has only one Application ( e.g. one tenant with SDMO, one tenant with glossary etc..)
    -   In Multi App Environment v2, the tenant can contain several applications (SDMO, glossary, shopfloor, showcase etc.. )

-   Tenant Creation / Provisioning / Decommissioning (only for environment V1)

    -   When the tenant is created, it exists only on the multi-cluster database used by cloud ops, it’s a single row associated to a customer.
    -   When the tenant is provisioned: data from the required layer are loaded into his associated cluster.
    -   When the tenant is decommissioned: data from the tenant are deleted from the cluster, as well as users' association.

-   Application Provisioning / Decommissioning (only for environment V2)
    -   When the application is provisioned: data from the required layer are loaded into his associated cluster.
    -   When the application is decommissioned: data from the application are deleted from the cluster, as well as users' association.

## Tests glossary

-   Type of authentication

    -   SageId: sageId login / password credentials are required (user <EMAIL>).
    -   Unsecure: direct connection to the tenant (using tenantId), without credentials required / only in development environment
        -   user: <EMAIL>
        -   Example: https://login.eu.dev-sagextrem.com/unsecuredevlogin?cluster=[clusterName]]&tenant=[tenantId]&user=<EMAIL>
            -   Replace [clusterName] by the required cluster name.
            -   Replace [tenantId] by the required tenantId.

-   Type of tests

    -   Smoke tests

        -   Smoke tests data: - open an XTreeM Applicative page, load a record and check the page is properly displayed.
        -   Executed on tenant provisioned with test layer.

    -   Smoke tests static:

        -   open an XTreeM Applicative page without loading a record and check the page is properly displayed.
        -   Executed on tenant provisioned whatever the layer used.

    -   Functional tests
        -   CRUD of FLOW aimed at testing the application from a functional point of view.
        -   Executed on tenant provisioned with QA layer.

For further information, refer to the following pages:

-   [Tests-standards](https://confluence.sage.com/display/XTREEM/Tests+Standards/)
-   [Tests-type definitions](https://confluence.sage.com/display/XTREEM/Tests+type+definitions/)
