PATH: XTREEM/Pipelines/ATP+XTreeM+Cucumber+Reporting

# Cucumber reporting

## Access to ATP / XTreeM Smoke Test and Functional Test pipelines

-   The different pipelines for x3-services & xtrem-services are located [here](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionScope=%5Catp).

![list of ATP / XTreeM pipelines](assets/images/atp_xtrem_pipelines.png)

## Cucumber reporting in Azure devops pipeline

-   In the `Cucumber` tab you can see the cucumberHTML report of the current pipeline.

    -   On pull requests, there are multiple tabs corresponding to each pipeline that has executed the different smoke tests.

    ![ATP / XTreeM cucumber HTML report](assets/images/atp_xtrem_pipeline_reporting_09.png)

    -   Expand the Feature to display the scenario, step defintiions and screenshots taken on demand or on failure.
    -   You can click the screenshot to enlarge it.

    ![ATP / XTreeM cucumber HTML report](assets/images/atp_xtrem_pipeline_reporting_10.png)

-   In the `Tests` tab you can verify the overall results and check the tests that failed.

![ATP / XTreeM overal test results](assets/images/atp_xtrem_pipeline_reporting_01.png)

## Cucumber reporting detail execution

-   Enter in the build detail to see the different steps executed by the pipeline.

    -   The `Execute cucumber tests` step will show you the detailed execution of the cucumber tests.
        ![ATP / XTreeM cucumber execution detail](assets/images/atp_xtrem_pipeline_reporting_02.png)

## Download cucumber-html reporting

### Download cucumber-html reporting from Build radiator or Azure devops pipeline

-   This method replaces the method to download cucumber-html reporting from Amazon S3.
-   It is not required to type command anymore. You can directly download the cucumber-html report zip artifact from the pipeline.
    -   You can access the Artifacts page from [Build radiator - XT (st, ft) automated tests - Daily (overview)](https://app.powerbi.com/groups/me/apps/5303e10f-94b8-44e1-ac48-6778a00d8e7d/reports/21661764-9dff-40c2-8f7f-6bc7a7594f8d/ReportSection?ctid=3e32dd7c-41f6-492d-a1a3-c58eb02cf4f8).
        -   Click the `Outcomes Link`. you are redirected to the Artifacts page.
            ![atp_xtrem_build_radiator_01](assets/images/atp_xtrem_build_radiator_01.png)
    -   You can access the Artifacts page from the [ATP / XTreeM Azure Devops Pipelines](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionScope=%5Catp).
        -   Browse the required pipeline and open the required build.
        -   On the `Run_template_st task`, click the `artifacts link`. Then you are redirected to the Artifacts page.
            ![atp_xtrem_pipeline_artifacts_01](assets/images/atp_xtrem_pipeline_artifacts_01.png)
    -   Once you are redirected on the Artifacts page:
        -   Select the `Download artifacts` more action of `cucumberHtmlReport` artifact.
            -   You have to download the whole report, not only some elements otherwise the report won't be properly displayed.
                ![atp_xtrem_pipeline_artifacts_02](assets/images/atp_xtrem_pipeline_artifacts_02.png)
        -   The report is downloaded on you local machine.
            ![atp_xtrem_pipeline_artifacts_03](assets/images/atp_xtrem_pipeline_artifacts_03.png)
        -   Prior to unzip it, make sure no other folder with the same name exists, otherwise delete it to avoid mixing different reports.
        -   Unzip the `cucumberHtmlReport` zip.
            -   You have to unzip the whole report, not only some elements otherwise the report won't be properly displayed.
        -   Refer to the chapter Browse cucumber-html reporting for more details how to display the cucumber-html reporting.

### Download cucumber-html reporting from Amazon S3

-   Enter in the build detail to see the different steps executed by the pipeline.

    -   The `S3 upload info` step indicates the URL where the cucumber-html report has been uploaded.

        ![ATP / XTreeM S3 Upload info](assets/images/atp_xtrem_pipeline_reporting_03.png)

    -   Copy the link.
    -   Open a terminal to connect to the ubuntu VM. (e.g. windows terminal).
        -   To download and configure windows terminal, refer to the following [page:](https://confluence.sage.com/display/ATP/Configuring+XTreeM)
    -   Type the following command:

        ```ts
        aws s3 sync [URL] [Destination path]

        Replace [URL] by the URL you have copied.
        Replace [Destination_PATH] by the path you want the report to be downloaded.
        ```

        -   For instance for the followng link s3://xtrem-developers-utility/functional-tests/dev-ci-run-ft-xtrem-services-functional_test/503425/20220415_130257
            -   It will download the cucumber html report of the functional test pipeline "dev-ci-run-ft-xtrem-services-functional_test", build= "503425", created on "2022/04/15" at "13h02m57s" (server time).
            -   If you want the files for instance to be downloaded in the folder "20220413_133734" , copy the first part of the link without the date and time information. (e.g s3://xtrem-developers-utility/functional-tests/dev-ci-run-ft-xtrem-services-functional_test/503425/).

    -   Example
        ![Download cucumber-html report from Amazon S3](assets/images/atp_xtrem_pipeline_reporting_04.png)

    -   Access the report using your explorer.

        -   In this example I choose to download the report in the "Download/report" folder I previously created and located in my ubuntu VM user directory.
        -   You can access your ubuntu from windows by typing the URL `\\wsl$\ubuntu`. Navigate where you want to create your directory (e.g. `\\wsl$\Ubuntu\home\my_user\Download\Report`).

        -   To avoid typing the URL each time, you can create a shared directory in windows that access your ubuntu VM.
        -   To access the ubuntu VM in windows type in the explorer: `\\wsl$\ubuntu`

        ![ubuntu vm shared directory in windows](assets/images/ubuntu_vm_shared_directory.png)

-   Refer to the chapter Browse cucumber-html reporting for more details how to display the cucumber-html reporting.

## Browse cucumber-html reporting

-   Once the reporting has been downloaded (from the artifact or from Amazon S3), browse the different package to access the required report.
-   In each package you will find an `index.html` containing the cucumber-html report of each Package.
-   Click the `index.html` file to open the report
-   If you need to move the report to a different location, copy the whole `cucumberHtmlReport` folder.
    -   Don't copy only certain elements, otherwise the report won't be properly displayed.
    -   If you have issues to display the report after moving it, try to copy it in folders not synchronized with `OneDrive` or make sure your `OneDrive` folders are properly synchronized.

![cucumber-html index.hmtl](assets/images/atp_xtrem_pipeline_reporting_05.png)

![cucumber-html index.hmtl](assets/images/atp_xtrem_pipeline_reporting_06.png)

![cucumber-html index.hmtl](assets/images/atp_xtrem_pipeline_reporting_07.png)

![cucumber-html index.hmtl](assets/images/atp_xtrem_pipeline_reporting_08.png)
