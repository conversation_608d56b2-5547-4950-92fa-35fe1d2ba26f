PATH: XTREEM/XtreeM/Development+Methodology

# Development Methodology

## Development Methodology organization

-   [Onboarding](https://confluence.sage.com/display/XTREEM/Onboarding), if you are new in Xtreem.
-   [Prerequisites](https://confluence.sage.com/display/XTREEM/Prerequisites), if you are new in Xtreem.
-   [Methodology](https://confluence.sage.com/display/XTREEM/Methodology)
-   [Standards](https://confluence.sage.com/display/XTREEM/Standard)
-   [Process](https://confluence.sage.com/display/XTREEM/Process)

Particular attention is required for the following standards among others:

-   [UX and UI standards](https://confluence.sage.com/display/XTREEM/UX+and+UI+standards)
-   [Naming Standards](https://confluence.sage.com/display/XTREEM/Naming+Standards)
-   [Tests](https://confluence.sage.com/x/wQKUE)

## Methodology Knowledge transfer

-   New rules summary will be provided in the [Methodology News channel](https://teams.microsoft.com/l/channel/19%3a34b01f5f7d23411eb3f5bdc18d99a684%40thread.tacv2/Methodology%2520News?groupId=46560869-b2b0-4805-bc3a-d031e2b373da&tenantId=3e32dd7c-41f6-492d-a1a3-c58eb02cf4f8),

## How is the methodology designed?

-   Working with the Architecture Designers.
-   Working with Solution designer (started with Gerhard & will be extended very soon to other people to cover the finance).
-   Working with the Platform.
-   Working with the UX.
-   Working with the UI (Muriel’s team & Design System representative)

Note that all meetings are weekly, except with the UI.

## How to request a method?

-   Development Questions: Report to your Architecture Designers, then the topic will be discussed in a weekly meeting.
-   UX questions: Use the UX forum – Create an Enhancement request if no answer, with the criteria mentioned on [this page](https://confluence.sage.com/display/XTREEM/UX+and+UI+standards).
-   Questions and remarks on the existing methodology: Tag Elisabeth on the [applicative-support channel](https://teams.microsoft.com/l/channel/19%3aa1f3166b7b1c49489dc17ff859a6c60e%40thread.tacv2/applicative-support?groupId=46560869-b2b0-4805-bc3a-d031e2b373da&tenantId=3e32dd7c-41f6-492d-a1a3-c58eb02cf4f8)
-   Note that we avoid changing a rule as it would bring confusion and refactoring, but of course, we can complete a methodology rule.

## Sharing development knowledge

-   A [Knowledge transfer](https://teams.microsoft.com/l/meetup-join/19%3ameeting_YmY5OWZiMWItNDg1NS00MDBiLTgwZDQtZjE4Y2Q1OTI5OGZm%40thread.v2/0?context=%7b%22Tid%22%3a%223e32dd7c-41f6-492d-a1a3-c58eb02cf4f8%22%2c%22Oid%22%3a%22a5af5d96-4d74-49a5-b66a-0023cd41920e%22%7d) weekly meeting is dedicated for developers to share their expertise.

## Product development

The methodology helps us to build a product with compliance.<br>
Thus, use it at each step: the conception-analysis phase, development, development review, and QA tasks.

/!\ A special care is needed for pages, as there is a lot of refactoring to do.<br>
Do not copy and paste without taking a look at the UX methodology.
