PATH: XTREEM/Methodology/Node+Development+Methodology

# Node Development Methodology

There are 3 sections in a node definition:

-   Import
-   Decorators
-   Class Export

## Import

Link to TypeScript documentation [#Import](https://www.typescriptlang.org/docs/handbook/modules.html#import)

### Basic data types

Import the technical framework components (data types, decorators...) defined in Xtrem Platform you need to develop your node.

-   Importing `decorators` and `node` are mandatory, the others depend on your development.

`import { decorators, Node, StringDataType, decimal, Collection, BinaryStream } from '@sage/xtrem-core';`

### Xtrem Generic data types

[See the data type methodology.](https://confluence.sage.com/display/XTREEM/Data+Types+Methodology)

### Current Package

Import your current package as needed.

`import * as xtremMasterData from '../index';` if your current package is xtremMasterData

### Other packages

Import any other package as needed ; please keep in mind the [Package Hierarchy](https://confluence.sage.com/x/wEZqDgy) rules.

`import * as xtremTechnicalData from '@sage/xtrem-technical-data';` to import xtremTechnicalData in a node developed in xtremManufacturing for e.g.

## Decorators

The node attributes list is available [here](https://confluence.sage.com/x/4-NYE)

### Examples

**Basic Example** [Site](https://github.com/Sage-ERP-X3/xtrem/blob/master/platform/system/xtrem-system/lib/nodes/site.ts)

```ts
@decorators.node<Site>({
    package: 'xtrem-system',
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    indexes: [
        {
            orderBy: { id: +1 },
            isUnique: true,
        },
    ],
})
```

**Example with multiple index component** [Stock Journal](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-inventory/lib/nodes/stock-journal.ts)

```ts
@decorators.node<StockJournal>({
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canSearch: true,
    indexes: [
        {
            orderBy: { site: 1, isUpdate: 1, item: 1, effectiveDate: -1, sequence: 1 },
            isUnique: true,
        },
    ],
})
```

**Example with a vital relationship** [Misc Stock Receipt Lines](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-inventory/lib/nodes/miscellaneous-stock-receipt-lines.ts)

```ts
@decorators.node<MiscellaneousStockReceiptLine>({
    package: 'xtrem-inventory',
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canSearch: true,
    canCreate: true,
    canUpdate: true,
    isVitalCollectionChild: true,
    indexes: [
        {
            orderBy: {
                miscellaneousStockReceipt: 1,
                lineNumber: 1,
            },
            isUnique: true,
        },
    ],
    async saveEnd() {
        await xtremInventory.functions.stockLib.stockEngineUmbrella(this.$.context, this.adapters.stockUpdate);
    },
})
```

See also documentations for more details:

-   [Vital references](https://confluence.sage.com/x/GvRYE)
-   [Vital collections and references](https://confluence.sage.com/x/IfRYE)
-   [Subclassing](https://confluence.sage.com/x/lPNYE)

### Node Event

A number of node events are available:

-   control\*
-   create\*
-   delete\*
-   prepare\*
-   save\*

Find out more here on [Decorators - Node Event](https://confluence.sage.com/x/1-NYE)

**Example - setting an automatic number on a document**

```ts
@decorators.node<MiscellaneousStockReceipt>({
    package: 'xtrem-inventory',
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canSearch: true,
    canCreate: true,
    canUpdate: true,
    indexes: [
        {
            orderBy: { number: +1 },
            isUnique: true,
        },
    ],
    async createEnd() {
        if (!!(await this.number)) {
            this.$.context.logger.warn(() => 'The code already exists, and no sequence number will be allocated');
            return;
        }
        const documentNumberGenerator = await xtremMasterData.classes.DocumentNumberGenerator.create(this.$.context, {
            nodeInstance : this
        });

        if (!(await this.number)) {
            this.number = await documentNumberGenerator.allocate();
        }
    },
})

@decorators.stringProperty<SalesInvoice, 'number'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSales.dataTypes.propertyDataType,
        provides: ['sequenceNumber'],
    })
    readonly number: Promise<string>;

    @decorators.dateProperty<SalesInvoice, 'invoiceDate'>({
        isStored: true,
        isPublished: true,
        defaultValue: () => date.today(),
        provides: ['documentDate'],
    })
    readonly invoiceDate: Promise<date>;

    @decorators.referenceProperty<SalesInvoice, 'salesSite'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        node: () => xtremSystem.nodes.Site,
        provides: ['site'],
        filters: {
            control: {
                isSales: true,
            },
        },
    })
    readonly salesSite: Reference<xtremSystem.nodes.Site>;

If it's not possible do define all the variables with the provides you need to send the parameters mannualy:
Example:

this.number = await xtremMasterData.classes.DocumentNumberGenerator.create(this.$.context, {
    nodeInstance: this,
    currentDate: date.today(),
}).allocate();

```

**Example with ControlBegin event** [Location Type](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-master-data/lib/nodes/location-type.ts)

**Example with saveEnd** [Production Tracking](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/applications/xtrem-manufacturing/lib/nodes/production-tracking.ts)

## Class Export

Link to TypeScript documentation [#Export](https://www.typescriptlang.org/docs/handbook/modules.html#export)

### Generic data types

How to refer to an Xtrem generic data type? Navigate to the up to date list of [Xtrem generic data types](https://github.com/Sage-ERP-X3/xtrem/blob/master/platform/system/xtrem-system/lib/data-types/data-types.ts).

**Example with an [id data type](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-master-data/lib/nodes/allergen.ts)**

```ts
export class Allergen extends Node {
    @decorators.stringProperty<Allergen, 'id'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        dataType: () => dataTypes.id,
    })
    readonly id: Promise<string>;
```

**Example with a [decimal data type](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-master-data/lib/nodes/item.ts)**

```ts
@decorators.decimalProperty<Item, 'volume'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        control(cx, val) {
            cx.error.if(val).is.negative();
        },
        dataType: () => dataTypes.decimal,
    })
    readonly volume: Promise<decimal>;
```

### Main property types

Below follow examples related to the most used property types

#### String

Example: [User](https://github.com/Sage-ERP-X3/xtrem/blob/master/platform/system/xtrem-system/lib/nodes/user.ts)

```ts
    @decorators.stringProperty<User, 'firstName'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        dataType: () => new StringDataType({ maxLength: 30 }),
    })
    readonly firstName: Promise<string>;
```

#### Integer

Example: [Item site](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-master-data/lib/nodes/item-site.ts)

```ts
    @decorators.integerProperty<ItemSite, 'prodLeadTime'>({
        isStored: true,
        isPublished: true,
    })
    readonly prodLeadTime: Promise<integer>;
```

#### Boolean\*\*

Example: [Item](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-master-data/lib/nodes/item.ts)

```ts
    @decorators.booleanProperty<Item, 'isBought'>({
        isStored: true,
        isPublished: true,
        defaultValue: false,
    })
    readonly isBought: Promise<boolean>;
```

#### Enum

Example: [Item site](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-master-data/lib/nodes/item-site.ts)

```ts
    @decorators.enumProperty<ItemSite, 'preferredProcess'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.enums.preferredProcessDataType,
    })
    readonly preferredProcess: Property<xtremMasterData.enums.PreferredProcess>;
```

#### Reference Property

Example: [Item site](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-master-data/lib/nodes/item-site.ts)

```ts
    @decorators.referenceProperty<ItemSite, 'item'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMasterData.nodes.Item,
        isVitalParent: true,
    })
    readonly item: Reference<xtremMasterData.nodes.Item>;
```

#### Collection Property

Example: [Item site](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-master-data/lib/nodes/item-site.ts)

```ts
    @decorators.collectionProperty<ItemSite, 'suppliers'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.ItemSiteSupplier,
        reverseReference: 'itemSite',
    })
    readonly suppliers: Collection<xtremMasterData.nodes.ItemSiteSupplier>;
```

#### Text Stream

Example: [Formula](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-technical-data/lib/nodes/formula.ts)

```ts
    @decorators.textStreamProperty<Formula, 'formulaHierarchyChartContent'>({
        isPublished: true,

        getValue() {
            const formulaVersions = this.$.context.query(xtremTechnicalData.nodes.Formula, {
                filter: { id: this.id },
                orderBy: { version: +1 },
            });
            return TextStream.fromString(
                xtremSystem.functions.generateFormulaHierarchyChart(this.name, formulaVersions, this.version),
            );
        },
    })
    readonly formulaHierarchyChartContent: Promise<TextStream | null>;
```

#### Binary Stream

Example: [Item](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-master-data/lib/nodes/item.ts)

```ts
    @decorators.binaryStreamProperty<Item, 'image'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly image: Promise<BinaryStream | null>;
```

#### Query

Example: [Country](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-structure/lib/nodes/country.ts)

```ts
    @decorators.query<typeof Country, 'readCountryApi'>({
        isPublished: true,
        parameters: [{ name: 'base', type: 'string' }],
        return: 'string',
    })

```

### Control on properties

**Example - Controlling the unit is active and of volume type** ([Item](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-master-data/lib/nodes/item.ts))

```ts
    @decorators.referenceProperty<Item, 'volumeUnit'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        control(cx, val) {
            if (val) {
                xtremSystem.events.control.isActive(
                    this.$.status,
                    cx,
                    this.$.status === NodeStatus.modified ? this.$.old._id : null,
                    val,
                );
                if (val?.type !== xtremMasterData.enums.UnitType.volume) {
                    cx.error.add(`Unit of measure ${val?.id} is not a volume measure.`);
                }
            }
        },
    })
    readonly volumeUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;
```

**Example - Controlling a property of the node** ([Work Order Component](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/applications/xtrem-manufacturing/lib/nodes/work-order-component.ts))

```ts
 @decorators.referenceProperty<WorkOrderComponent, 'operation'>({
        isStored: true,
        isPublished: true,
        node: () => xtremManufacturing.nodes.WorkOrderOperation,
                isNullable: true,
        control(cx, val) {
            if (this.lineType === xtremTechnicalData.enums.BOMLineType.text && val) {
                cx.error.add('Operation must be null for text line type');
            }
        },
    })
    readonly operation: Reference<xtremManufacturing.nodes.WorkOrderOperation | null>;
```

### Mutation

Link to GraphQL documentation on [Mutation](https://graphql.org/learn/queries/#mutations)

Find out more here: [Decorators - Mutation](https://confluence.sage.com/x/_-NYE)

**Example - Updating Exchange rates**

```ts
    @decorators.mutation<typeof Currency, 'updateRate'>({
        isPublished: true,
        parameters: [{ name: 'base', type: 'string' }],
        return: 'boolean',
    })
    static async updateRate(context: Context, base: string): Promise<boolean> {
        const listCurrencies = await context.query(xtremMasterData.nodes.Currency, { filter: { isActive: true } });
        const baseCurrency = await listCurrencies.find(currency => await currency.id === base);
        if (baseCurrency) {
            const result = await xtremMasterData.functions.currencyFetcher(base);
            await listCurrencies
                .filter((value: { _id: any }) => value._id !== baseCurrency._id)
                .forEach(async destinationCurrency => {
                    Currency.addRate(
                        context,
                        baseCurrency,
                        destinationCurrency,
                        result.date,
                        result.rates[await destinationCurrency.id],
                    );
                });
            return true;
        }
        this.$.context.logger.debug(() => `updateRate : ${base} Currency not found`);
        return false;
    }
```

**Example with Mutation - Creation of formula component** [Formula Component](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-technical-data/lib/nodes/formula-component.ts)

**Example with Mutation - Loading data from Intacct** [Group of UoMs](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/adapters/xtrem-intacct-gateway/lib/nodes/group-unit-of-measure.ts)

Note that **transient properties** might come in handy in dealing with mutations, find out more in the [Server Framework - Transient Properties](https://confluence.sage.com/x/_-NYE).
