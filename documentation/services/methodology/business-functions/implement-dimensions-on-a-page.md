PATH: XTREEM/Business+functions/Dimensions

# Dimensions UI

## Presentation

-   To ease the implementation of the UI logic related to dimensions we created a set of functions that handles the management of a sidebar panel and the data it work with.
-   In this page we will dive deeper into those function and implement a simple use-case on a sales document: SalesOrder.
-   We took advantage of the client shared functions so the code lives only in one place which help us improve it easily and so maintain a good quality :muscle:

## Pre-requisites

-   A document line Node implementing the dimensions
-   A page for this document

## Restrictions

Be careful:

The functions related to dimensions have been written to work with document implementing the BaseDocumentLine subnoding. For different document/object type the implementation will substantially differ.

## Use cases

All our use cases requires the following grid columns to be defined:

```typescript
@ui.decorators.tableField<SalesOrder, SalesOrderLine>({
    //...
    columns: [
        //...
        ui.nestedFields.text({
            isHidden: true,
            bind: 'computedAttributes',
        }),
        ui.nestedFields.text({
            isHidden: true,
            bind: 'storedAttributes',
        }),
        ui.nestedFields.text({
            isHidden: true,
            bind: 'storedDimensions',
        }),
        //...
    ],
    //...
})
lines: ui.fields.Table<SalesOrderLine>;
```

and this import statement to be added on top of the page source file:

```typescript
import {
    dimensionPanelHelpers,
    interfaces as masterDataInterfaces,
    utils,
} from '@sage/xtrem-master-data/build/lib/client-functions/_index';
```

### Editing dimensions on a line:

In those examples we are going to implement a utility to provide dimensions editing/visualization for a document line.

### 1. Using a button on the edit/detail panels:

First as those panels uses a grid row block we'll add a dedicated block.

```typescript
    @ui.decorators.block<SalesOrder>({
        parent() {
            return this.detailPanelGeneralSection;
        },
    })
    detailPanelDimensionsBlock: ui.containers.Block;
```

After we add our new button to this block.

```typescript
    @ui.decorators.buttonField<SalesOrder>({
        parent() {
            return this.detailPanelDimensionsBlock;
        },
        isTransient: true,
        onClick() {
            this.$.showToast('Button clicked !');
        },
        map() {
            return 'Dimensions';
        }
    })
    dimensionsButton: ui.fields.Button;
```

For now this button does not know anything about dimensions... Fortunately we do and have a very easy to use function which handle that for us.

> Here replace the `onClick` function by this example:

```typescript
    @ui.decorators.buttonField<SalesOrder>({
        // ...
        async onClick() {
            // this will get the line using the selectedRecordId
            const rowItem = this.lines.getRecordValue(this.detailPanelQuantityStockShortageBlock.selectedRecordId);
            // this ensures the partial type is not a problem by casting to non-partial.
            const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(rowItem);
            // this wraps the panel call so we don't have to apply the modification on the line neither manage the cancel/exit buttons.
            await utils.applyPanelToLineIfChanged(
                this.lines,
                // this calls the dimension panel
                dimensionPanelHelpers.editDisplayDimensions(
                    this,
                    {
                        documentLine: rowData,
                    },
                    {
                        // this parameter as to be customized to your need.
                        editable: rowItem.status !== 'closed',
                    },
                ),
            );
        },
        // ...
    })
    dimensionsButton: ui.fields.Button;
```

Also we need to provide a translatable text to display on this button.

> Here replace the `map` function by this example:

```typescript
    @ui.decorators.buttonField<SalesOrder>({
        // ...
        map() {
            return ui.localize('@sage/xtrem-sales/pages__sales_order__dimensions_button_text', 'Dimensions');
        },
        // ...
    })
    dimensionsButton: ui.fields.Button;
```

### 2. Using a dropdown action:

For this use case we need to create first a new dropdown action on our document line grid.

> Here add this `Dimensions` dropdown action:

```typescript
@ui.decorators.tableField<SalesOrder, SalesOrderLine>({
    //...
    dropdownActions: [
        //...
        {
            icon: 'edit',
            title: 'Dimensions',
            onClick() {
                this.$.showToast('Row-action clicked !');
            }
        },
        //...
    ],
    //...
})
lines: ui.fields.Table<SalesOrderLine>;
```

This action is not really doing anything for now... We have to use here our dedicate function which will provides us the dimensions ui feature.

> Here replace the `onClick` function by this example:

```typescript
{
    //...
    title: 'Dimensions',
    //...
    async onClick(rowId: any, rowItem: ui.PartialCollectionValue<SalesOrderLine>) {
        // this ensure the partial type is not a problem by casting to non-partial.
        const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(rowItem);
        // this wraps the panel call so we don't have to apply the modification on the line neither manage the cancel/exit buttons.
        await utils.applyPanelToLineIfChanged(
            this.lines,
            // this calls the dimension panel
            dimensionPanelHelpers.editDisplayDimensions(
                this,
                {
                    documentLine: rowData,
                },
                {
                    // this parameter as to be customized to you need.
                    editable: rowItem.status !== 'closed',
                },
            ),
        );
    },
    //...
}
```

### Editing dimensions on multiple lines (defaults):

In the previous part we saw how to provide a line by line way to edit the dimensions,
As we don't manage yet the default values for the dimensions...
As temporary solution we have provided a utility to edit the dimensions for all available lines at once.

### Using a field action

For this use case we'll need to create first a new field action on our document line grid.

> Here add this `defaultDimensionAction` field action:

```typescript
@ui.decorators.tableField<SalesOrder, SalesOrderLine>({
    //...
    fieldActions: [
        //...
        this.defaultDimensionAction,
        //...
    ],
    //...
})
lines: ui.fields.Table<SalesOrderLine>;

//...
@ui.decorators.pageAction<SalesOrder>({
    icon: 'arrow_right',
    title: 'Set default dimensions',
    isDisabled() {
        return true;
    },
    onClick() {
        this.$.showToast('Field-action clicked !');
    },
})
defaultDimensionAction: ui.PageAction;
//...
```

Our utility requires you to implement the `PageWithDefaultDimensions` and create a new property to keep track of the dimensions entered by the user during his session.

> Here apply the modifications according to this example:

```typescript
//...
export class SalesOrder extends ui.Page<GraphApi> implements masterDataInterfaces.PageWithDefaultDimensions {
    //...
    _defaultDimensionsAttributes: masterDataInterfaces.DefaultDimensions;
    //...
}
```

At this step we need to provide the initial value for this property. This as to be populated during the page loading so will use the onLoad event for this. You might see very few code in this event and probably some initPage function call, in this case put it in this `initPage` function.

> Here implement the initDefaultDimensions function:

```typescript
//...
@ui.decorators.page<SalesOrder, SalesOrderNode>({
    title: 'Sales order',
    //...
    onLoad() {
        //...
        this.initPage();
        // (if initPage doesn't exits)
        // this._defaultDimensionsAttributes = dimensionPanelHelpers.initDefaultDimensions();
        //...
    },
    //...
})
export class SalesOrder extends ui.Page<GraphApi> implements masterDataInterfaces.PageWithDefaultDimensions {
    //...
}


// (if exists)
initPage() {
    //...
    this._defaultDimensionsAttributes = dimensionPanelHelpers.initDefaultDimensions();
    //...
}
```

Now we know how to edit the line's dimensions but here we want to manage all the lines at once. To do this we will use the default dimensions feature.

> Here replace the `onClick` function by this example:

```typescript
    @ui.decorators.pageAction<SalesOrder>({
        //...
        async onClick() {
            // call the default dimension panel, apply the modification on the lines and manages the cancel/exit buttons if needed.
            // It will also always return the default dimensions (modified or not).
            this._defaultDimensionsAttributes = await dimensionPanelHelpers.applyDefaultDimensionsIfNeeded(
                this,
                this.lines,
                this._defaultDimensionsAttributes,
            );
        },
        //...
    })
    defaultDimensionAction: ui.PageAction;
```

this action has to be disabled when the user is not supposed to be able to edit so we have to implement its `isDisabled` function.

> Here add this `isDisabled` function:

```typescript

    @ui.decorators.pageAction<SalesOrder>({
        //...
        isDisabled() {
            // this function is generic and check only the dirty state of the page as well as the availability of a record id.
            return !dimensionPanelHelpers.isDefaultDimensionActionActive(this.$.recordId, this.$.isDirty);
        },
        //...
    })
    defaultDimensionAction: ui.PageAction;
```

### Apply the default dimensions

Now that we have our default dimensions we need to apply them on the new line creation. To do so, find the function responsible to add line on the grid.
There are 2 use cases:

-   The Side panel used to edit a line

In the case of the sales order we use a side panel to create new lines. To make sure we have the same capabilities as the edition of a line we need to pass the default dimensions to the this page.

> Here add a parameter to te document object passed to the panel

```typescript
async addEditSalesOrderLine(salesOrderLineId: string, line: Dict<any>, rowId: string) {
    //...
    values._defaultDimensionsAttributes = this._defaultDimensionsAttributes;
    //...
}
```

Then we have to properly apply the data on the new line object from the panel.

> Here apply the default dimension to the line in 'setPageValues'.

```typescript
    private async setPageValues(line: any) {
        //...
        if (line) {
            //...
        } else {
            //...


            // this function applies the default dimensions on a line object only when they are set.
            // we have to use use 'as any' because partial type is used in the function definition but not for linedata
            this.lineData = dimensionPanelHelpers.applyDefaultDimensionsOnNewLineIfNeeded(
                this.lineData as any,
                this.document.value._defaultDimensionsAttributes,
            ) as any;
        }
        //...
    }
```

-   The line is created from a function within the document page.

In this case we will only have to apply the default dimension when the user clicks a button. As this is done on the main page no need to pass parameters elsewhere :smiley:

This will be done most of the time when a field action of a grid is clicked.

> Here apply the default dimension to the line in 'addFromSalesOrderLines'.

```typescript
    async addFromSalesOrderLines(
        //...
    ) {
        //...
        if (changedLine && changedLine.lines.length) {
             //...
            const salesShipmentLineData = this.updateSalesShipmentLines(
                dimensionPanelHelpers.applyDefaultDimensionsOnNewLineIfNeeded(
                    newLine,
                    this._defaultDimensionsAttributes,
                ),
            );
            //...
        } else {
            //...
        }
    }
```
