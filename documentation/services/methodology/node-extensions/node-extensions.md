PATH: XTREEM/Methodology/Node+Extensions

# Node extensions

## Presentation

A node extension is a class that extends a node class, adding the following elements:<br>
At the node level:

-   Properties
-   Indexes
-   Methods
-   Node events

At the property level:

-   Property events
-   Property dependencies.

[Framework - Definition](https://confluence.sage.com/display/XTREEM/1+Definition)

## When to use a node extension?

We use a node extension for adding elements such as properties or events which is only useful in a sub-part of the product that can even be optional.

This is the right way to structure the node in several parts according to its usage in several packages.

Thus,

-   the **node** contains elements that are available in its package and those below in its hierarchy.
-   the **node extension** contains elements dedicated to a sub-package of the node's one. Those elements are available in this sub-package and those below in its hierarchy.

Therefore, a node can have several node extensions, knowing that:

-   There is one node extension maximum per sub-package,
-   There is no node extension in the node's package.

[Framework - Package organization](https://confluence.sage.com/pages/viewpage.action?pageId=236835799)

[Framework - Package dependency](https://confluence.sage.com/x/OPRYE)

## Product Consistency Guarantee

To ensure product consistency, the goal of a node extension is **to extend** the node by new elements.
It does not change any node or property characteristics except for **defaultValue**, **getValue** that comes in cancellation, and replacement.

## How data is managed in the database?

-   For the **core development**, node extension properties are included in the table's node.
-   For **add-ons**, node extension properties are stored in a dedicated table. (near future)

## Node extension file

Node extension files are grouped in the **lib/node-extensions** folder in each package.

[More details in Product structure](https://confluence.sage.com/display/XTREEM/Product+Structure)

The naming pattern is: nodeName-**extension.ts**

[More details in Naming standards](https://confluence.sage.com/display/XTREEM/Naming+Standards)

## Extensions writing code

When working with a class hierarchy you need a bit of extra care when writing an extension; you have to pick a different base class for the extension at every level of your hierarchy.

### Node extension

Use the **NodeExtension** decorator for the base node.

[Framework - Simple examples](https://confluence.sage.com/display/XTREEM/2+Simple+examples)

[A simple example - Customer extension](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/adapters/xtrem-intacct-gateway/lib/node-extensions/customer-extension.ts)

### Subnode extension

Use the **SubNodeExtension1**... **SubNodeExtension5** decorators respectively for each subnodes level.

[Framework - Extend subnodes](https://confluence.sage.com/display/XTREEM/3+Extend+subNodes)

### Properties extension

-   Possibility to add properties.
-   Use a property name that does not exist yet in the node and its extensions.

#### Use case

The **supplier-extension.ts** node extension of the **xtrem-intacct-gateway** package extends the supplier.ts node located in the xtrem-master-data package. The xtrem-intacct-gateway is dedicated to managing the interface with Intacct.

This additional intacctId property is needed only for the interface with Intacct, so it has been added to this dedicated package.

```ts
    /**
     * Corresponding to intacct VENDORID
     */
    @decorators.stringProperty<SupplierExtension, 'intacctId'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremIntacctGateway.dataTypes.intacctPropertyDataType,
    })
    readonly intacctId: Promise<string>;
```

### Indexes extension

-   Possibility to add indexes.

#### Use case

From the **sales-order-extension.ts** node extension of the **xtrem-intacct-gateway**:

```ts
@decorators.nodeExtension<SalesOrderExtension>({
    extends: () => xtremSales.nodes.SalesOrder,
    indexes: [
        {
            orderBy: { intacctId: 1, customerNumber: 1, status: 1 },
            isUnique: false,
        },
    ],
})
```

### Methods extension

-   Possibility to add queries, mutations and other methods.

#### Use case

From the **supplier-extension.ts** node extension of the **xtrem-intacct-gateway**:

```ts
    /**
     * Get the list of Intacct Vendors , link it to xtrem Supplier
     * @param context
     */
    @decorators.query<typeof SupplierExtension, 'getList'>({
        isPublished: true,
        parameters: [{ name: 'isXtremSide', type: 'boolean', isMandatory: true }],
        return: {
            type: 'string',
            isMandatory: true,
        },
    })
```

```ts
    /**
     * Mutation to create Intacct Vendor from xtrem Supplier
     * @param context
     * @param supplierID
     */
    @decorators.mutation<typeof SupplierExtension, 'createIntacct'>({
        isPublished: true,
        return: 'boolean',
        parameters: [{ name: 'supplierID', type: 'string', isMandatory: true }],
    })
```

### Node events extension

-   Possibility to add an event knowing that all evens are supported in node extensions.
-   Events are executed **after** the node's one except the **deleteEnd** event that is executed **before**.

#### Use case

From the **supplier-extension.ts** node extension of the **xtrem-intacct-gateway**:

```ts
@decorators.nodeExtension<SupplierExtension>({
    extends: () => xtremMasterData.nodes.Supplier,
    saveEnd() {},
    async saveBegin() {
        this.$.context.logger.debug(() => `Status : ${this.$.status}`);
        if (!await xtremIntacctGateway.functions.isIntacctActive(this.$.context)) {
            return;
        }
        if (this.$.status === NodeStatus.modified) {
            this.intacctId = await xtremIntacctGateway.functions.updateVendor(this as xtremMasterData.nodes.Supplier);
        } else if (this.$.status === NodeStatus.added) {
            this.intacctId = await
                xtremIntacctGateway.functions.createIntacctVendorFromXtrem(this as xtremMasterData.nodes.Supplier);
        }
        this.$.context.logger.debug(() => `Intacct Id : ${JSON.stringify(await this.intacctId)}`);
    },
    controlBegin() {},
})
```

From the **item-extension.ts** node extension of the **xtrem-intacct-gateway**:

```ts
@decorators.nodeExtension<ItemExtension>({
    extends: () => xtremMasterData.nodes.Item,

    async saveEnd() {
        if (! await xtremIntacctGateway.functions.isIntacctActive(this.$.context)) {
            return;
        }
        if (this.$.status === NodeStatus.deleted) {
            /**
             * Must be refactoring for bruno for this one
             */
            await xtremIntacctGateway.functions.deleteItemByName(this.$.context, this.intacctItemId);
        }
    },
    async saveBegin() {
        if (!await xtremIntacctGateway.functions.isIntacctActive(this.$.context)) {
            return;
        }
        if (this.$.status === NodeStatus.modified) {
            this.intacctItemId = await
                xtremIntacctGateway.functions.updateIntacctItemFromXtrem(this as xtremMasterData.nodes.Item);
        } else if (this.$.status === NodeStatus.added) {
            this.intacctItemId = await
                xtremIntacctGateway.functions.createIntacctItemFromXtrem(this as xtremMasterData.nodes.Item);
        }
    },
    async controlBegin(cx) {
        if (await xtremIntacctGateway.functions.isIntacctActive(this.$.context)) {
            return;
        }
        if (this.$.status !== NodeStatus.added) {
            if ((this as xtremMasterData.nodes.Item).type !== (this as xtremMasterData.nodes.Item).$.old.type) {
                cx.warn.add('Not modified on intacct side !');
            }
        }
        if (
            (this as xtremMasterData.nodes.Item).status === xtremMasterData.enums.ItemStatus.active &&
            this.glGroupName === ''
        ) {
            cx.error.add('GL Group name is mandatory when Item status is active');
        }
    },
})
```

## Extensions at Property level

### Property events extension

-   An event that does not return a value (prepare, control, ...) is executed **after** the node's one.
-   An event that returns a value (defaultValue, updatedValue, getValue) **replaces** the node's one.

#### Use case

From the **test-animal-extension.ts** node extension of the **xtrem-core**:

```ts
    @decorators.stringPropertyOverride<TestAnimalExtension, 'strFromAnimal'>({
        defaultValue(): string {
            return 'Bundle-TestAnimal.strFromAnimal.defaultValue';
        },
    })
```

### Property dependencies extension

-   Possibility to add dependencies to a property.

#### Use case

No use case in the product for the moment.

## Test Data

Test data for the properties added in a node extension are stored in the folder **data/extension-layers** in the package of the node extension.

Test data file naming pattern: **node.csv**

Example:

**item.csv** in the **xtrem-intacct-gateway** package.

```
"_id";"intacct_item_id";"gl_group_name"
"2";"'37890";"Chemical items"
"3";"'76251";"Chemical items"
"4";"'34261";"Chemical items"
"5";"'65451";"Chemical items"
"6";"'54671";"Chemical items"
"7";"'85531";"Chemical items"
"8";"'13241";"Chemical items"
"9";"'35421";"Chemical items"
```
