PATH: XTREEM/Smoke+Tests/Smoke+Tests+Execution+Previous+Release

# How to execute smoke tests on previous release in QA or preprod environment

## Glossary

- Refer to the following page [ATP XTreeM Glossary](https://confluence.sage.com/display/XTREEM/ATP+XTreeM+Glossary/) to have more information on the terminology used in this page.

## Preliminary steps

- To execute the smoke tests in QA or preprod environments from VScode you have to execute them from the corresponding release branch.
- For instance if the cluster in QA or preprod environment is using xtrem version n, you will have to:

1. git checkout release/n (replace n by the corresponding version. e.g 23.0)
2. pnpm run clean:install
3. pnpm run build
    - ( for clean:install and build it is recommended to execute those commands outside of VScode. e.g with windows terminal or other shell).

## How to execute (xtrem-services) cucumber smoke tests on QA or preprod environment

Prior to execute the command to run the smoke tests, the following environment variables may have to be defined according to the authentication type chosen:

- TARGET_URL: remote URL to connect to.
- loginUserName: sageId login. (when connecting with sageId authentication)
- loginPassword: sageId password. (when connecting with sageId authentication)
- tenantName: name of tenant to connect to (when connecting with sageId authentication)
- tenantAppName: name of application to connect to (when connecting with sageId authentication)

With sageId authentication type, ATP/ XTreeM robot is connected to the required xtrem-services environment after entering the required sageId credentials and selecting the tenant and application.

Prior to run the command:

- Move into the required applicative package (eg. xtrem-sales).
  Make sure parameters-atp file is created in xtrem-cli-atp package and contains the required setup.
    - Follow example for QA environment in [parameters-atp-sdmo-sageid-qana-cr-template](https://github.com/Sage-ERP-X3/xtrem/blob/master/platform/cli/xtrem-cli-atp/parameters-atp-template/sdmo/parameters-atp-sdmo-sageid-qana-cr-template).
    - Follow example for preprod environment in [parameters-atp-sdmo-sageid-ppeu-cr-template](https://github.com/Sage-ERP-X3/xtrem/blob/master/platform/cli/xtrem-cli-atp/parameters-atp-template/sdmo/parameters-atp-sdmo-sageid-ppeu-cr-template).

Run the command:

```bash
pnpm run test:smoke --browser
or
pnpm run test:smoke:static --browser
or to execute specific smoke tests
pnpm run xtrem test [filter] --integration --browser

```

- Use [XT Tool (Cucumber)](<https://teams.microsoft.com/l/channel/19%3a286f797c8eb54a31a722b602f4798745%40thread.skype/XT%2520Tool%2520(Cucumber)?groupId=668c69fa-59b5-4e04-bde9-b6e733d2cda1&tenantId=3e32dd7c-41f6-492d-a1a3-c58eb02cf4f8>) teams channel to ask for the credentials.
