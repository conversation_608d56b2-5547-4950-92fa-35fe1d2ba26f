PATH: XTREEM/Smoke+Tests/Smoke+Tests+Execution+Latest+Version

# How to execute smoke tests on latest version

## Glossary

- Refer to the following page [ATP XTreeM Glossary](https://confluence.sage.com/display/XTREEM/ATP+XTreeM+Glossary/) to have more information on the terminology used in this page.

## Preliminary steps

To execute the smoke tests in dev environments or locally from VScode you have to execute them from master branch or your feature branch
you will have to:

1. git checkout master (or your feature branch)
2. pnpm run clean:install
3. pnpm run build
    - ( for clean:install and build it is recommended to execute those commands outside of VScode. e.g with windows terminal or other shell).

## How to execute (xtrem-services) cucumber smoke tests on localhost

1.  Load the data from the layer test

    - For smoke-test-data only.
    - Only for xtrem/service. Not necessary for xtrem/x3/

        ```bash
        pnpm run load:test:data
        ```

        Note:

        - For xtrem/service, the command can be executed at root level (e.g. xtrem/service) or inside an applicative package (e.g. xtrem-sales).

2.  Start the service

    ```bash
    pnpm run start
    ```

    Note:

    - For xtrem/service, the command can be executed at root level (e.g. xtrem/service) or inside an applicative package (eg. xtrem-sales).

    If the pnpm run start succeeds, you should be able to browse the following url: `http://localhost:8240/@sage`

3.  Split the terminal in two and move into the applicative package.

4.  run the command

    - To launch all the smoke tests (static + data)

        ```bash
        pnpm run test:smoke
        ```

    - To launch only the smoke tests static

        ```bash
        pnpm run test:smoke:static
        ```

    - To execute specific smoke tests

        ```bash
        pnpm run xtrem test [filter] --integration
        ```

    - To display the browser, the following argument need to be added to smoke test command: -- --browser

        ```bash
        pnpm run test:smoke --browser
        pnpm run test:smoke:static --browser
        pnpm run xtrem test [filter] --integration  --browser
        ```

## How to execute (xtrem-services) smoke tests on a required environment

Prior to execute the command to run the smoke tests, the following environment variables may have to be defined according to the authentication type chosen:

- TARGET_URL: remote URL to connect to.
- loginUserName: sageId login. (when connecting with sageId authentication)
- loginPassword: sageId password. (when connecting with sageId authentication)
- tenantName: name of tenant to connect to. (when connecting with sageId authentication)
- tenantAppName: name of application to connect to. (when connecting with sageId authentication)

### Example to execute (xtrem-services) smoke tests on development environment with unsecure authentication

With unsecure authentication type, ATP/ XTreeM robot is connected directly to the xtrem development environment, in the given tenant and application without requiring to enter sageId credentials or to select the tenant and application.
Unsecure Authentication is only activate in development environment.

```diff
+ Important:
+ prioritize this method to execute your smoke test in development environment
```

Prior to run the command:

- Move into the required applicative package (eg. xtrem-sales).
- Follow the same step as described in [functional-tests-execution-latest-version](https://confluence.sage.com/display/XTREEM/Functional+Tests+Execution+Latest+Version) using unsecure authentication.

Run the command:

```bash
pnpm run test:smoke --browser
or
pnpm run test:smoke:static --browser
or
pnpm run xtrem test [filter] --integration  --browser
```

- List of the smoke test tenants / tenantId in development environment:
    - Refer to the following page [Smoke Tests Pipelines](https://confluence.sage.com/display/XTREEM/Smoke+Tests+Pipelines/) to have the list of tenants and tenanId where the smoke tests are executed.
    - Look for those which have Authentication type = unsecure

### Example to execute (xtrem-services) smoke tests on a required environment with tenant selection and sageId authentication

With sageId authentication type, ATP/ XTreeM robot is connected to the required xtrem-services environment after entering the required sageId credentials and selecting the tenant and application.

```diff
- Important:
- To execute your smoke test in development environment, prioritize the method with unsecure authentication.
- sageid authentication method is required when you need to connect to preprod or prod environments.
```

Prior to run the command:

- Move into the required applicative package (eg. xtrem-sales)
- Make sure parameters-atp file is created in xtrem-cli-atp package and contains the required setup.
    - Follow example for Dev environment in [parameters-atp-sdmo-sageid-deveu-civ2-template](https://github.com/Sage-ERP-X3/xtrem/blob/master/platform/cli/xtrem-cli-atp/parameters-atp-template/sdmo/parameters-atp-sdmo-sageid-deveu-civ2-template).

Run the command:

```bash
pnpm run test:smoke --browser
or
pnpm run test:smoke:static --browser
or
pnpm run xtrem test [filter] --integration  --browser
```

- Use [XT Tool (Cucumber)](<https://teams.microsoft.com/l/channel/19%3a286f797c8eb54a31a722b602f4798745%40thread.skype/XT%2520Tool%2520(Cucumber)?groupId=668c69fa-59b5-4e04-bde9-b6e733d2cda1&tenantId=3e32dd7c-41f6-492d-a1a3-c58eb02cf4f8>) teams channel to ask for the credentials.

## Execution of (xtrem-services) cucumber smoke tests during pull request

- Smoke-test-static, smoke-test-data, smoke-test-pr are automatically executed on pull request.
- [Pipeline executed on PR](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=1405)
