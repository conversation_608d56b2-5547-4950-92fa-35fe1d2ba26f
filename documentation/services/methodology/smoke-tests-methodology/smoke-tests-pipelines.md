PATH: XTREEM/Smoke+Tests/Smoke+Tests+Pipelines

# Smoke Tests Pipelines

## ATP XTreeM Glossary

- Refer to the following page [ATP XTreeM Glossary](https://confluence.sage.com/display/XTREEM/ATP+XTreeM+Glossary/) to have more information on the terminology used in this page.

## Smoke tests pipelines (glossary)

### Smoke Tests Pipelines for development environment

| Pipeline                                                                                                                              | Environment URL                              | Cluster | Tenant             | tenantId                | Trigger                                                                                                         | Tests type                | Branch | Authentication Mode | Layer      |
| ------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------- | ------- | ------------------ | ----------------------- | --------------------------------------------------------------------------------------------------------------- | ------------------------- | ------ | ------------------- | ---------- |
| [`deveu-civ2-run-st-xtrem-glossary-smoke_tests_civ2`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=4925) | https://glossary-ci-v2.eu.dev-sagextrem.com/ | ci-v2   | `smoke_tests_civ2` | `QBoIQnD7GzsEeYnvcAMBz` | [`generic-upgrade-cluster-app`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=4785) | Smoke tests static & data | master | unsecure            | setup,test |

## Smoke tests pipelines (shopfloor)

### Smoke Tests Pipelines for development environment

| Pipeline                                                                                                                               | Environment URL                               | Cluster | Tenant             | tenantId                | Trigger                                                                                                         | Tests type                | Branch | Authentication Mode | Layer      |
| -------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------- | ------- | ------------------ | ----------------------- | --------------------------------------------------------------------------------------------------------------- | ------------------------- | ------ | ------------------- | ---------- |
| [`deveu-civ2-run-st-xtrem-shopfloor-smoke_tests_civ2`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=4926) | https://shopfloor-ci-v2.eu.dev-sagextrem.com/ | ci-v2   | `smoke_tests_civ2` | `QBoIQnD7GzsEeYnvcAMBz` | [`generic-upgrade-cluster-app`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=4785) | Smoke tests static & data | master | unsecure            | setup,test |

## Smoke tests pipelines (xtrem-services)

### Smoke Tests Pipelines for development environment

| Pipeline                                                                                                                                | Environment URL                                | Cluster     | Tenant                  | tenantId                | Trigger                                                                                                         | Tests type                | Branch    | Authentication Mode | Layer      |
| --------------------------------------------------------------------------------------------------------------------------------------- | ---------------------------------------------- | ----------- | ----------------------- | ----------------------- | --------------------------------------------------------------------------------------------------------------- | ------------------------- | --------- | ------------------- | ---------- |
| [`deveu-civ2-run-st-xtrem-sdmo-smoke_tests_civ2`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=4885)       | https://sdmo-ci-v2.eu.dev-sagextrem.com/       | ci-v2       | `smoke_tests_civ2`      | `QBoIQnD7GzsEeYnvcAMBz` | [`generic-upgrade-cluster-app`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=4785) | Smoke tests static & data | master    | unsecure            | setup,test |
| [`deveu-civ2-run-sts-xtrem-sdmo-functional_tests_civ2`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=4886) | https://sdmo-ci-v2.eu.dev-sagextrem.com/       | ci-v2       | `functional_tests_civ2` | `ssKIwYYk2r0rzUcQsS8HD` | [`generic-upgrade-cluster-app`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=4785) | Smoke tests static        | master    | unsecure            | setup,qa   |
| [`deveu-cr-run-sts-xtrem-sdmo-smoke_tests_static_cr`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=5267)   | https://sdmo-cls-release.eu.dev-sagextrem.com/ | cls-release | `smoke_tests_static_cr` | `S5s8jjNM8YUiu_nPN8kh2` | aws-lambda                                                                                                      | Smoke tests static        | release/n | unsecure            | setup,qa   |
| [`deveu-cr-run-st-xtrem-sdmo-smoke_tests_cr`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=4887)           | https://sdmo-cls-release.eu.dev-sagextrem.com/ | cls-release | `smoke_tests_cr`        | `E9F9BoUqyFE7f3stX45xO` | aws-lambda                                                                                                      | Smoke tests static & data | release/n | unsecure            | setup,test |
|                                                                                                                                         |

### Smoke Tests Pipelines for QA environment

- Prior to execute the pipeline, select the required release/n branch.

| Pipeline                                                                                                                                     | Environment URL                               | Cluster     | Tenant                          | tenantId                | Trigger    | Tests type                | Branch    | Authentication Mode | Layer      |
| -------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------- | ----------- | ------------------------------- | ----------------------- | ---------- | ------------------------- | --------- | ------------------- | ---------- |
| [`qana-cr-run-sts-xtrem-sdmo-smoke_tests_static_cr`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=5268)         | https://sdmo-cls-release.na.qa-sagextrem.com/ | cls-release | `smoke_tests_static_cr`         | `P6wl7LDAXDXem30Z87Ode` | aws-lambda | Smoke tests static        | release/n | unsecure            | setup,qa   |
| [`qana-cr-run-st-xtrem-sdmo-smoke_tests_cr`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=4903)                 | https://sdmo-cls-release.na.qa-sagextrem.com/ | cls-release | `smoke_tests_cr`                | `nxSG76TBdva96smPGD6_7` | aws-lambda | Smoke tests static & data | release/n | unsecure            | setup,test |
| [`qana-cp-run-sts-xtrem-sdmo-smoke_tests_static_cp_current`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=5712) | https://sdmo-curr-prd.na.qa-sagextrem.com/    | curr-prd    | `smoke_tests_static_cp_current` | `Eu4RlfFy5PB6gXkdZKbKH` | aws-lambda | Smoke tests static        | release/n | unsecure            | setup,qa   |
| [`qana-cp-run-st-xtrem-sdmo-smoke_tests_cp_current`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=5711)         | https://sdmo-curr-prd.na.qa-sagextrem.com/    | curr-prd    | `smoke_tests_cp_current`        | `W9QNdjbO3nd1J9d_qfftN` | aws-lambda | Smoke tests static & data | release/n | unsecure            | setup,test |

### Smoke Tests Pipelines for preprod environment

- Prior to execute the pipeline, select the required release/n branch.

| Pipeline                                                                                                                                | Environment URL                                | Cluster     | Tenant                   | tenantId                | Trigger    | Tests type                | Branch    | Authentication Mode | Layer      |
| --------------------------------------------------------------------------------------------------------------------------------------- | ---------------------------------------------- | ----------- | ------------------------ | ----------------------- | ---------- | ------------------------- | --------- | ------------------- | ---------- |
| [`ppeu1-cr-run-sts-xtrem-sdmo-smoke_tests_static_cr`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=5390)   | https://sdmo-cls-release.eu1.pp-sagextrem.com/ | cls-release | `smoke_tests_static_cr`  | `S6S0SwlueBMs2rzVrHxOw` | aws-lambda | Smoke tests static        | release/n | unesecure elevate   | setup,qa   |
| [`ppeu1-cr-run-st-xtrem-sdmo-smoke_tests_cr`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=4992)           | https://sdmo-cls-release.eu1.pp-sagextrem.com/ | cls-release | `smoke_tests_cr`         | `yIrVKwVRelfULWd1EvsKq` | aws-lambda | Smoke tests static & data | release/n | unesecure elevate   | setup,test |
| [`ppeu1-prd-run-sts-xtrem-sdmo-smoke_tests_static_prd`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=5391) | https://sdmo-pp-prd.eu1.pp-sagextrem.com/      | pp-prd      | `smoke_tests_static_prd` | `sZQDHxruLvG4CsGVcEsiN` | aws-lambda | Smoke tests static & data | release/n | unesecure elevate   | setup,qa   |

### Smoke Tests Pipelines for prod eu environment

- Prior to execute the pipeline, select the required release/n branch.

| Pipeline                                                                                                                                   | Environment URL                    | Cluster | Tenant                     | tenantId                | Trigger                                                                                                                                    | Tests type                | Branch    | Authentication Mode | Layer      |
| ------------------------------------------------------------------------------------------------------------------------------------------ | ---------------------------------- | ------- | -------------------------- | ----------------------- | ------------------------------------------------------------------------------------------------------------------------------------------ | ------------------------- | --------- | ------------------- | ---------- |
| [`pdeu-pilot-run-sts-xtrem-sdmo-smoke_tests_static_pilot`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=5481) | https://sdmo-pilot.eu.erp.sage.com | pilot   | `smoke_tests_static_pilot` | `VI35Ody0KocAPnUNWzrLN` | manual                                                                                                                                     | Smoke tests static        | release/n | sageId              | setup,qa   |
| [`pdeu-pilot-run-st-xtrem-sdmo-smoke_tests_pilot`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=5154)         | https://sdmo-pilot.eu.erp.sage.com | pilot   | `smoke_tests_pilot`        | `W6YHRy03Sue9PCV6MCCz9` | [`pdeu-pilot-run-sts-xtrem-sdmo-smoke_tests_static_pilot`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=5481) | Smoke tests static & data | release/n | sageId              | setup,test |

### Smoke Tests Pipelines for prod na environment

- Prior to execute the pipeline, select the required release/n branch.

| Pipeline                                                                                                                                   | Environment URL                    | Cluster | Tenant                     | tenantId                | Trigger                                                                                                                                    | Tests type                | Branch    | Authentication Mode | Layer      |
| ------------------------------------------------------------------------------------------------------------------------------------------ | ---------------------------------- | ------- | -------------------------- | ----------------------- | ------------------------------------------------------------------------------------------------------------------------------------------ | ------------------------- | --------- | ------------------- | ---------- |
| [`pdna-pilot-run-sts-xtrem-sdmo-smoke_tests_static_pilot`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=5482) | https://sdmo-pilot.na.erp.sage.com | pilot   | `smoke_tests_static_pilot` | `mD6ENzoCkc8bsFqi26dRV` | manual                                                                                                                                     | Smoke tests static        | release/n | sageId              | setup,qa   |
| [`pdna-pilot-run-st-xtrem-sdmo-smoke_tests_pilot`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=5156)         | https://sdmo-pilot.na.erp.sage.com | pilot   | `smoke_tests_pilot`        | `4xtBQWJPTlGBS8V3gdKK1` | [`pdna-pilot-run-sts-xtrem-sdmo-smoke_tests_static_pilot`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=5482) | Smoke tests static & data | release/n | sageId              | setup,test |

## Cucumber reporting

- Refer to the following page [ATP / XTreeM - reporting](https://confluence.sage.com/x/39sTGg) to know how to verify the tests results with allure report
