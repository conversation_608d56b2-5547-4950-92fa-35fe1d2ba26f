PATH: XTREEM/Smoke+Tests/Smoke+Tests+Creation

# How to create smoke tests

## Glossary

-   Refer to the following page [ATP XTreeM Glossary](https://confluence.sage.com/display/XTREEM/ATP+XTreeM+Glossary/) to have more information on the terminology used in this page.

## Where cucumber smoke tests should be created?

1. Cucumber feature should be created on your feature branch.

    - Cucumber feature tests are delivered into master by doing a pull request of feature branch.

2. To know where to create the cucumber smoke tests, please refer to the following page

    - Please refer to the following confluence page: [Tests-standards](https://confluence.sage.com/display/XTREEM/Tests+Standards/).

## Which codification should I use?

-   Please refer to the following confluence page: [Tests-standards](https://confluence.sage.com/display/XTREEM/Tests+Standards/).

## Which step definitions can I use?

-   List of the step definitions: [Step definitions](https://confluence.sage.com/pages/viewpage.action?pageId=*********).

## Which data layer should I use?

-   smoke-test-static: aimed at opening the XTreeM pages without loading any data.

    -   No data from a specific layer need to be loaded.

-   smoke-test-data: aimed at opening the XTreeM pages for a specific record.
    -   Only for xtrem/service. Not necessary for xtrem/x3.
        -   Data from the "test" layer are used to execute the smoke-test-data.

## Which sageidatp account is used on the smoke tests pipelines?

-   Refer to the following confluence page: [ATP / XTreeM - sageidatp accounts](https://confluence.sage.com/pages/viewpage.action?pageId=*********).
