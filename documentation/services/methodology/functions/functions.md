PATH: XTREEM/Methodology/Functions

# Functions

## Presentation

-   Functions were firstly available for nodes of different packages.
-   Functions are now also available for pages of different packages.

Please note that server code is obfuscated but the client code is not.
Therefore:

-   A function can import server or client package but not both to keep the compatibility.
-   Functions are split into 3 folders according to their context (server vs client)

## Structure

-   **functions**: storing functions available for the server only. They can use server packages, NOT client packages.
-   **shared-functions**: storing functions available for the server-side and the client-side. They CANNOT use server packages or client packages.
-   **client-functions**: storing functions available for the client only. They can use client packages, NOT server packages.

## Restrictions

Be careful:

-   Writing your function respecting the context (server vs client)
-   Creating your function in the right folder according to its context.
-   Using a function according to its context.

Shared or client functions CANNOT use server side definitions (such as nodes, enums, types) or server side packages (e.g. xtrem-core). So, depending on the complication there may need to be two versions.<br>

Example: decimal cannot be used from xtrem-core. So, you have to redefine it in your package, as it has been done in the [component-functions script](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-technical-data/lib/shared-functions/component-functions.ts).

Server functions CANNOT use client side package (e.g. xtrem-ui).

A single **interface** cannot be used for server and client. We will still have to define one for each. However, we can define one interface which can be imported from other packages.

## Use cases

Platform:

-   email-validation and password scripts in shared-functions in the xtrem-system package.

Services:

-   precision-round in client-functions in the xtrem-sales package.
-   component-functions in shared-functions in the xtrem-technical-data package.

## Links

Link to the platform documentation: https://confluence.sage.com/display/XTREEM/Shared+Functions
