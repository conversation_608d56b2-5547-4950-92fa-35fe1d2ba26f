PATH: XTREEM/Packages/Package+Methodology

# Package Methodology

## Packages in the architecture

> ⚠️ **[IMPORTANT]**<br>
> ⚠️ **This documentation is not up to date anymore**<br>
> ⚠️ **Developers community, Please, give me the new way to create a package.**<br>
> ⚠️ **I will then update the documentation.**<br>

### Package Principles

**What's a package?**

A Package can be defined as a grouping of related types (classes, interfaces, enumerations and annotations ) providing access protection and namespace management.
It is a good practice to group related nodes, classes implemented so that a developer can easily determine that the nodes, classes, interfaces, enums are related.

Packages being the basis of the structure of the product, a special care must be taken for the architecture of packages that includes all their characteristics.

> ⚠️ **[IMPORTANT]**
> If you have to create modify or delete a package, it's mandatory to work closely with the referent developers.

### Package Content

Please, follow those development rules for your packages:

-   Nodes and services must drive the content of the package around the business logic.
-   ⚠️ Conversely, the technical elements such as data types, enums or glossary SHOULD NOT drive the packages structure.
-   A common package must be defined when the business logic is common to several business logic.
-   Packages structure must be built according to the modularity of delivery.
-   Adopt a similar granularity of packages from a domain to another.
-   Applicative packages are organized by functional area, including business logic, UI and unit tests (scripts and data).
-   Applicative packages refer to platform packages such as xtrem-cli, xtrem-core and xtrem-system through the package.json file.

### Package Dependencies

Organize the package in order to get a completeness during their execution.
To specify the packages your project depends on, you must list them as "dependencies" or "devDependencies" in the package.json file in each package.

-   `dependencies`: Packages required by your application in production. All needed packages must be referred in dependencies.
-   `devDependencies`: Packages only needed for development and testing.

> ⚠️ **[IMPORTANT]**
> If you have to create or delete a dependency, please, work closely with the referent developers.

If you add a package or modify the dependencies of an existing package, you have to run `pnpm run clean:install` in the root directory of your repository. This command may modify the root `package.json` and `pnpm-lock.yaml` files. If these files are modified you must commit them into your branch.

### Package Hierarchy

All dependencies links are built respecting a hierarchy.

-   Package are structured from the highest to lower levels.
-   At execution, a package accesses all of above packages.
    A package can access to major one through its package.json file in the dependencies paragraph.

**Example:** xtrem-master-data

xtrem-master-data requires xtrem-system and xtrem-inventory requires xtrem-master data: thus it benefits from xtrem-system.

![package-hierarchy](assets/images/package-hierarchy.png)

**What is authorized and what is not authorized?**

Diamond links are authorized. B is dependent on A, C is dependent on A, D is dependent on B and C.

> ⚠️ **[IMPORTANT]** > **Circular** dependencies are forbidden between packages. An error is triggered during build if such case appears.

![package-hierarchy-dependencies](assets/images/package-hierarchy2.png)

### Extensions through packages

#### What is an extension?

We call extension the possibility to add elements to a **node** or a **page** in another package.

#### Why use extensions through packages?

To respect a business logic organization inside packages. Examples:

-   Common properties of a node in a common package, additional properties of this node in dedicated packages per domain.
-   Type of a property linked to a node declared in a sub-package.

In case of a reference type property based on a node from a lower-level package (the higher level package does not have access to the second one, hence the need for moving this property to the second package, in a extension)

#### What are the elements manageable through an extension?

-   **Properties** and **methods** added to a node-extension.
-   **Business rules** (default value, control, …) added to properties existing in a main node.
-   **Components** (sections, blocks, fields, table columns, …) and methods added to a node-extension.
-   **Event handlers** (on click, on change, …) added to components existing in a main page.

#### What is the naming extensions?

The naming convention of an extension is either:

-   **node-extension.ts** (node being the main node on which is the extension)
-   **page-extension.ts** (page being the main page on which is the extension)

### Package versioning

Each package gets its own versioning.
Xtrem use the **semantic versioning** to distinguish between fixes, new features and breaking changes. The main information about this are:

-   Semantic versioning : major. minor.patch
-   The ^ character prefixing the version refers to the last version corresponding to the major one. (ex ^4.0.0)

Please, find more details in site: [server.org](https://semver.org/)

### Xtrem packages and classes organization

[Please, find the diagram in this page.](https://confluence.sage.com/pages/viewpage.action?pageId=236835799)

### Data Repartition in package and layer

[Please find details in this page.](https://confluence.sage.com/x/mYvMDw)

## How to create an application package

**This page describes how to create an application package.**

Packages are the basis of the structure of the product and as such require thought.

It's also essential to work with the referent developers.

> ⚠️ **[IMPORTANT]**
> Before creating a packing be aware of how to define a package in the architecture, and ask the referent developers to work with.

### Create your package

Through VS Code, from a terminal, navigate to the the **services** directory.

![package01](assets/images/package01.PNG)

Run the **xtrem init** command to create your package (more on this command [here](https://confluence.sage.com/x/N7IdDg))

    `../node_modules/.bin/xtrem init`

Enter the following values to initialize your package:

1. Vendor name: **sage** (as we develop in the core at Sage)
2. Package name: **xtrem-name** (name must be defined according to the naming rule of the package.
3. Dependencies: select the **xtrem-ui**, **xtrem-cli** and **xtrem-core** that must be present in all packages.
4. Database driver: **postgres**

![application-package-creation02](assets/images/application-package-creation02.png)

Now wait for pnpm to finish the job

![application-package-creation03](assets/images/application-package-creation03.png)

### Modify tsconfig.json

Add a new path in **/tsconfig.json** in which level???

### Modify the package.json in your package

Edit the **package.json** file of your new package to complete its creation.

-   **Update the default description** that has been initialized by 'A Sage Xtrem Service Package'
-   Set the **package version to x.y.z** where **x.y is the same as in existing packages** (all packages should have the same x.y but maybe different z) and **set z to 0**.

-   **Add the required packages dependencies**, and

    -   Make sure **xtrem-ui** is listed under Dependencies
    -   Make sure **xtrem-system** is listed as it's the highest package in the hierarchy under devDependencies (and not under Dependencies where it comes by default)

-   **Align the package versions** under dependencies to the values of an existing package (for e.g. xtrem-master-data).
-   Remove **alasql** and **postgres** from both dependencies and devDependencies if present.

![application-package-creation05](assets/images/application-package-creation05.png)

-   Add the "nyc" section, at the end copying it from the xtrem-system package for example.

![application-package-creation06](assets/images/application-package-creation06.png)

### Modify the tsconfig.json and tsconfig-artifacts.json files in your package

Edit the package **tsconfig.json** to your needs.

-   This can be done by copying and adjusting the corresponding file from another package. (e.g. xtrem-structure)

![application-package-creation07](assets/images/application-package-creation07.png)

Edit **tsconfig-artifacts.json** to your needs.

-   This can be done by copying and adjusting the corresponding file from another package. (e.g. xtrem-structure).

![application-package-creation08](assets/images/application-package-creation08.png)

### Create the .eslintrc.js file in your package

In your new package directory create a **.eslintrc.js** file copying it from **xtrem-system** for example.

![application-package-creation09](assets/images/application-package-creation09.png)

### Create the typedoc.json file in your package

In your new package directory create a **typedoc.json** file copying it from xtrem-master-data for example.

![application-package-creation10](assets/images/application-package-creation10.png)

### Modify index.ts of the lib folder

Check the file **index.ts** is similar to this one. ???

![application-package-creation11](assets/images/application-package-creation11.png)

### Create your development

At this stage, you are able to develop in your package by creating a node for example.

### Launch the build

Navigate to the **xtrem** directory.

Run the following command to check if your package builds properly

`pnpm run clean:install`

Navigate to the **services** directory.

Run the following command to check if your package builds properly

`pnpm run build`

## Troubleshooting

### FATAL ERROR: 'node-factory loaded twice'

Check your package.json then run the following command: **pnpm run clean:install**
