PATH: XTREEM/Methodology/Node+Inheritance

# Node Inheritence

## Presentation

Node inheritance is the mechanism of deriving new nodes (**sub-nodes**) from an existing one (**base-node**) and thus forming them into a hierarchy of nodes. The sub-node acquires all the properties and behaviors of the base-node while extending them.

Xtrem supports multilevel inheritance, where a sub-node is inherited from another sub-node (**intermediate node**).

There are no constraints about location in packages. A base-node and its sub-nodes can be either on the same or on different packages depending on the business domain they belong to.

## When to use it?

We use node inheritance to avoid properties and method duplication among similar nodes.<br>
Please, note [the current limitations](https://confluence.sage.com/display/XTREEM/3+Current+limitations)

## Example

The base-resource base-node is derived in those following sub-nodes:

-   labor-resource
-   machine-resource
-   tool-resource

```
                       base-resource
                             |
          +-------------------------------------+
          |                  |                  |
labor-resource      machine-resource      tool-resource
```

The base-resource file contains all the common properties and methods that will be used to manage the labor, machine et tool resources.

You can see a [Framework example](https://confluence.sage.com/display/XTREEM/2+A+thorough+example)

## How data is managed in the database?

-   Each node is managed through dedicated tables: one table for the base-node and one per sub-node.
-   One instantiation updates the sub-node and its base-node.
-   The **\_id** property is in the base-node and the sub-nodes, thus it is used to links the records of those several tables.
-   The **\_constructor** in the base-node allows to identify the type of instance.

Let see an example at the end of this documentation.

## Base & Intermediate Node

### Characteristics

Base and intermediate nodes are managed the same way:

-   _Base_ and _intermediate_ nodes must be declared as **abstract**.
-   Properties, methods, events, indexes, dependencies are managed as a regular node.
-   About operations:
    -   **Query**, **Update** and **Delete** are available when you have to manage a group of records according values of the base-node.
    -   **Creation** is not allowed, as it is managed at the sub-node level only.
-   All their elements (properties, methods, operations,...) are inherited.

### Naming

Naming: **base-nodeIdentifier** <br>
with

-   **base**: constant
-   **nodeIdentifier**: common suffix for a group (base-node and sub-nodes)<br>

e.g. base-resource

### Syntax

```ts
@decorators.node<BaseNodeName>({
    isAbstract: true,
```

### Example

[base-resource.ts](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-master-data/lib/nodes/base-resource.ts)

```ts
@decorators.node<BaseResource>({
    storage: 'sql',
    isPublished: true,
    isAbstract: true,
    indexes: [
        {
            orderBy: { id: 1, site: 1 },
            isUnique: true,
        },
    ],
})
export class BaseResource extends Node {
```

## Sub-node

### Characteristics

-   A _sub-node_ is a non-abstract node.
-   **CRUD** operations are available.

Extension of a base node:

-   Adding their own properties and methods.

Extension of base properties:

-   Adding events, dependencies.
-   Overriding the defaultValue, getValue and UpdateValue.

### Naming

Naming: **subnode-nodeIdentifier** <br>
with

-   **subnode**: name of the subnode
-   **nodeIdentifier**: common suffix for a group (base-node and sub-nodes)<br>

e.g. labor-resource

### Syntax

Node extension declaration

```ts
@decorators.subNode<SubnodeName>({
    extends: () => BasenodeName,

export class SubnodeName extends BasenodeName {
```

Property extension declaration

```ts
@decorators.typePropertyOverride<SubnodeName, 'propertyname'>({
```

**type** being the type of property: string, decimal, date, integer, enum, reference, collection ...

### Examples

[labor-resource.ts](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-master-data/lib/nodes/labor-resource.ts) that extends the [base-resource.ts](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-master-data/lib/nodes/base-resource.ts).

```ts
@decorators.subNode<LaborResource>({
    extends: () => BaseResource,
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canSearch: true,
    canRead: true,
    async prepare() {
        const employee = await this.employee;
        if (employee) {
            await this.$.set({
                site: await employee.site;
                id: `${await employee.firstName} ${await employee.lastName}`;
                name: `${await employee.firstName} ${await employee.lastName}`;
            })
        }
    },
})
export class LaborResource extends BaseResource {
```

[work-in-progress-purchase-order-line.ts](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/applications/xtrem-purchasing/lib/nodes/work-in-progress-purchase-order-line.ts) that extends the item property of the [work-in-progress.ts](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-master-data/lib/nodes/work-in-progress.ts).

```ts
    @decorators.referencePropertyOverride<WorkInProgressPurchaseOrderLine, 'item'>({
        dependsOn: [{ purchaseOrderLine: ['item'] }],
        // FIXME: we cannot yet query on computed properties; for now, we set isStored on some and defaultValue to
        async defaultValue() {
            return (await this.purchaseOrderLine).item;
        },
        updatedValue: useDefaultValue,
    })
    readonly item: Reference<xtremMasterData.nodes.Item | null>;
```

The [work-in-progress-work-order-component.ts](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/applications/xtrem-manufacturing/lib/nodes/work-in-progress-work-order-component.ts) sub-node extends the documentNumber property of the [work-in-progress.ts](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-master-data/lib/nodes/work-in-progress.ts) base node.

```ts
    @decorators.stringPropertyOverride<WorkInProgressWorkOrderComponent, 'documentNumber'>({
        dependsOn: [{ workOrderComponent: [{ workOrder: ['number'] }] }],
        async getValue() {
            return (await (await this.workOrderComponent).workOrder).number;
        },
    })
    readonly documentNumber: Promise<string>;
```

## How to manage test data?

A **\*.csv** file is created for each sub-nodes and contains the **base**, **intermediate** and **sub-node** properties.

They are located in the folder **data/layers/test** in the package of their corresponding sub-node.

⚠️ Please, remind that you have to [maintain the list of sub-nodes here with the range of \_id to use.](https://confluence.sage.com/x/KSKUE)

### Example

Let see this through an example.

### Table structures and data

base-resource

| \_constructor   | \_id | Id               | name             | isActive |
| --------------- | ---- | ---------------- | ---------------- | -------- |
| MachineResource | 1000 | Cutting          | Cutting machine  | true     |
| MachineResource | 1001 | Sanding          | Sanding machine  | true     |
| MachineResource | 1003 | Treating         | Treating machine | true     |
| ToolResource    | 1300 | Cylinder         | 1 liter cylinder | true     |
| ToolResource    | 1301 | Beaker           | 2 liter beaker   | true     |
| LaborResource   | 1200 | Richard Anderson | Richard Anderson | true     |
| LaborResource   | 1201 | Joseph Beckett   | Joseph Beckett   | true     |

machine-resource

| \_id | serialNumber | model       |
| ---- | ------------ | ----------- |
| 1000 | AAAAA        | aaaaaaaaaaa |
| 1001 | BBBBB        | bbbbbbbbbbb |
| 1002 | CCCCC        | ccccccccccc |

tool-resource

| \_id | quantity |
| ---- | -------- |
| 1300 | 20       |
| 1301 | 13       |

labor-resource

| \_id | employee |
| ---- | -------- |
| 1200 | 1        |
| 1201 | 2        |

### CSV files

machine-resource.csv

```
"_id";"id";"name";"is_active";"serialnumber";"model"
"1000";"Cutting";"Cutting machine";"Y";"AAAAA";"aaaaaaaaaaa"
"1001";"Sanding";"Sanding machine";"Y";"BBBBB";"bbbbbbbbbbb"
"1002";"Treating";"Treating machine";"Y";"CCCCC";"ccccccccccc"
```

tool-resource.csv

```
"_id";"id";"name";"is_active";"quantity"
"1300";"Cylinder";"1 liter cylinder";"Y";20
"1301";"Beaker";"2 liter beaker";"Y";13
```

labor-resource.csv

```
"_id";"id";"name";"is_active";"employee"
"1200";"Richard Anderson";"Richard Anderson";"Y";"1"
"1201";"Joseph Beckett";"Joseph Beckett";"Y";"2"
```
