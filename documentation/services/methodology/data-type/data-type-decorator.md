PATH: XTREEM/Data+Types/Data+Type+Decorator

# How to declare a new data type?

## The right way to develop

1. Create a constant

```ts
export const intacctIdType = new StringDataType({ maxLength: 100 });
```

This declaration must be done in the lib/datatype folder of the current package (if specific) or of the xtrem-system package (if common).

2. Then, use that constant value in the node property

```ts
    @decorators.stringProperty<StockJournalExtension, 'intacctId'>({
        isStored: true,
        isPublished: true,
        dataType: () => intacctIdType,
    })
    readonly intacctId: Promise<string>;
```

## To BAN

**DO NOT develop** as below, as a new data type would be created each time the framework calls dataType().

```ts
dataType: () => new StringDataType({ maxLength: 100 }),
```
