PATH: XTREEM/Unit+Tests/GraphQL+Static-Test-Creation

# GraphQL Static Test Creation

## Presentation

The characteristics are the following:

-   The input and output are static. Thus tests are run only once. [More details](https://confluence.sage.com/display/XTREEM/1+Static+input+and+output)
-   Environment variables are available. [More details](https://confluence.sage.com/display/XTREEM/4+Provide+environmental+variables+to+GraphQL+tests)
-   Mocks are available. [More details](https://confluence.sage.com/display/XTREEM/6+Provide+mocks+to+GraphQL+tests)

The files to be created for a test are:

-   the `request.graphql.hbs` file,
-   the `response.json.hbs` file.
-   the `parameters.json` file when using environment variables or mocks.

## Advantages

Their advantages are:

-   The simplicity of writing and maintenance.
-   The only knowledge to have is our internal version of graphql
-   Low writing cost

## Creation Process

Firstly,

1. Start your **PostgreSQL Docker**
2. On VS Code, start the UI and GraphQL, with: `pnpm run start`
3. Go to the GraphiQL interface: **http://localhost:8240/explorer/**

Through the GraphiQL interface:

1. Enter your request
2. Run it
3. Check the response
4. Copy and paste the request, response, and parameters if exist, into your files according to the standards, described in the following paragraph.
   ![graphql1](assets/images/graphql1.png)

## Standards

### Tests Naming and Storage

A test is stored in a dedicated folder, using 2 or 3 files. Please, find the **naming and storage standards**, [on this dedicated page.](https://confluence.sage.com/x/dodYE)

## Syntaxes

In those syntaxes, `package` and `node` have to be replaced by the name of the ones managed by your tests. Properties and values mentioned here are examples.

### Request for a creation

```
mutation {
    package {
        node {
            create(
                data: {
                    id: "CHOC"
                    name: "Chocolate"
                    isActive: true
            ) {
                _id
                id
                name
                isActive
            }
        }
    }
}
```

### Request for a read

```
{
    package {
        node {
            read(_id: "41") {
                name
                isActive
            }
        }
    }
}

```

### Request for an update

```
mutation {
    package {
        node {
            update(data: { _id: "10", name: "Nuts" }) {
                _id
                id
                name
            }
        }
    }
}

```

### Request for a delete

```
mutation {
    package {
        node {
            delete(_id: "3")
        }
    }
}

```

### Request for a query

In this syntax, the second `node` is a constant.

```
query {
    package {
        node {
            query(filter: "{id: 'EGG'}") {
                edges {
                    node {
                        _id
                        id
                        name
                        isActive
                    }
                }
            }
        }
    }
}

```

## Examples

Find here some representative examples:

### CRUD operations and a query

-   [Allergen creation](https://github.com/Sage-ERP-X3/xtrem/tree/master/services/shared/xtrem-master-data/test/graphql/allergen-creation-complete)
-   [Allergen update](https://github.com/Sage-ERP-X3/xtrem/tree/master/services/shared/xtrem-master-data/test/graphql/allergen-update-success)
-   [Allergen deletion](https://github.com/Sage-ERP-X3/xtrem/tree/master/services/shared/xtrem-master-data/test/graphql/allergen-delete)
-   [Allergen query](https://github.com/Sage-ERP-X3/xtrem/tree/master/services/shared/xtrem-master-data/test/graphql/allergen-query)
-   [Labor resource read](https://github.com/Sage-ERP-X3/xtrem/tree/master/services/shared/xtrem-master-data/test/graphql/labor-resource-read)

### Creation of a document

This example includes the management of collections.

-   [Sales order creation](https://github.com/Sage-ERP-X3/xtrem/tree/master/services/applications/xtrem-sales/test/graphql/sales-order-create)

### Errors management

-   [Sale order deletion error](https://github.com/Sage-ERP-X3/xtrem/tree/master/services/applications/xtrem-sales/test/graphql/sales-order-delete-fail)

### Environment variables usage

Using the `today` variable allows to replace today's date, to enforce the test stabilization. In the following example, the date is used for the order date and for the order number containing the year and the month of today's date.

-   [Purchase order creation](https://github.com/Sage-ERP-X3/xtrem/tree/master/services/applications/xtrem-purchasing/test/graphql/purchase-order-create)

### Mocks - axios

-   [Currency update mock rate](https://github.com/Sage-ERP-X3/xtrem/tree/master/services/shared/xtrem-master-data/test/graphql/currency-update-mock-rate)
