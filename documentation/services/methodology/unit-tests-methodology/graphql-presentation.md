PATH: XTREEM/Unit+Tests/GraphQL+Presentation

# GraphQL Tests Presentation

We distinguish two kinds of tests:

-   [Static tests](https://confluence.sage.com/x/mq8NE), with static input/output,
-   [Parametric tests](https://confluence.sage.com/display/XTREEM/GraphQL+Parametric+Test+Creation), running the same query/mutation with different sets of input/output.

Cf. [The platform documentation](https://confluence.sage.com/display/XTREEM/3+GraphQL+unit+tests)
