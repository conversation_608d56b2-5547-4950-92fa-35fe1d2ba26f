PATH: XTREEM/Unit+Tests/Debug

How to debug your Mocha or GraphQL tests?

# Starting debugging

1. Start the postgres-test docker.
2. Start the debugger of **Visual Studio Code** to test yours Mocha & Graphql unit tests.

![debug1](assets/images/debug1.png)

3. Select your script you want to debug, opening it.
4. Chose the configuration (mocha or graphql)

![debug2](assets/images/debug2.png)

-   **Debug current test file**: for mocha tests
-   **Debug current graphql file**: for graphql tests

4. Press the start 'green arrow icon'.

# Breakpoints

Add breakPoints on your code when wanting to stop on a line.
As you can see, you can then manage your breakpoints.

![debug3](assets/images/debug3.png)

# Debug actions

You have the toolbar and keyboard shortcuts to run your script through the debug mode.

| Toolbar to manage the debug         | Keyboard shortcuts                                                                                                       |
| ----------------------------------- | ------------------------------------------------------------------------------------------------------------------------ |
| ![debug8](assets/images/debug8.png) | `F5` Continue <br>`F10` Step over <br>`F11` Step into <br>`F12` Step out <br>`ctrl shift F5` Restart <br>`shift F5` Stop |

# Variables

All variables available in the context are displayed.

![debug4](assets/images/debug4.png)

# Watch

Expression can be added and monitored here.

![debug5](assets/images/debug5.png)

# Debug console

When you are in debug mode, switch to the debug console, to follow the execution steps.

![debug6](assets/images/debug6.png)

# Internal technical details

The settings of the debugger is managed through the **settings.json** file located in xtrem-services / .vscode.

![debug7](assets/images/debug7.png)

The launch of the debugger is managed through the **launch.json** file located in xtrem-services / .vscode.

# Complementary resources

-   [Debugging introduction](https://code.visualstudio.com/docs/introvideos/debugging) \- Official documentation for VS Code debugging.
-   [Debugging](https://code.visualstudio.com/docs/editor/debugging) \- Official documentation for VS Code debugging.
