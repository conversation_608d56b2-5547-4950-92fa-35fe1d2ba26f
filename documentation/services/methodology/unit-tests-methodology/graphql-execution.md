PATH: XTREEM/Unit+Tests/GraphQL+Execution

# GraphQL Test Execution

## Prerequisites

Configure the log for tests in your **xtrem-config.yml** file with `disabledForTests: true` in the `logs` paragraph.

config.yml:

```
logs:
  disabledForTests: true
  domains:
    sage/xtrem-core/sql:
      level: info
```

## Test Execution Mode

The `executionMode` parameter manage the way to execute or not your tests with the values:

-   `skip`: to skip this test.
-   `only`: to execute only this test.
-   `normal`: to execute (default value)

[cf the platform documentation](https://confluence.sage.com/display/XTREEM/3+Execution+mode)

From now, do not move new tests in the "graphql-disable" folder which is the old method.

## Running your current test

1. Start your PostgreSQL Docker
2. `pnpm run clean:install`
3. `pnpm run load:test:data`
4. Goto the **package directory** of the unit test.
5. Run your unit test.

    - Run your test by opening either the request.graphql or response.json file in VsCode and run the task named `run currently opened Graphql test`.

## Verifying the test results

Example:

![graphql-tests-running](assets/images/graphql-tests-running.png)

Check if your test is passed.<br>

Note that the "Logs are disable by config" message is an information message according to the setting in the config.yml file described in the prerequisites paragraph.

## Running test commands

In each package, those commands are available:

-   **pnpm run test:graphql** run the graphql tests
-   **pnpm run test** run the graphql & mocha tests
-   **pnpm run test:ci** run the graphql & mocha tests and calculate the code coverage.

To execute a selection of graphql tests:

`pnpm run xtrem test %filter% --graphql`<br>
with `%filter%` a pattern or a test name.

## Debugging

Please, [refer to this page.](https://confluence.sage.com/x/PCKXDg)
