PATH: XTREEM/Unit+Tests/GraphQL+Coverage

# GraphQL Tests Coverage

What to test with GraphQL?
Firstly, the tests must be related to the functional scope of the product.

-   **CRUD operations** and **queries** on which the graphql tests are especially recommended.
-   **Methods** and **operations** would be managed through graphql, in addition to the mocha tests, in order to ensure the sustainability of the API.
-   **Failure cases** would be managed through graphql to complete your set of tests.

Finally, complete your set of tests, if needed, according to the code coverage results. [More details](https://confluence.sage.com/display/XTREEM/Source+Code+Analysis).
