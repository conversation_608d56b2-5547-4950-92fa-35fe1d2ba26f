PATH: XTREEM/Unit+Tests/Mocha+and+Chai

# Mocha-Chai Unit Tests

## Standards

Please, find the tests **naming standards** and **storage standards** in [this dedicated page.](https://confluence.sage.com/x/dodYE)

## Writing your unit test

### Importing the needed resources

-   Import the **Test** and **update** class from the **xtrem-core** module.
-   Import the **assert** class from the **chai** module.
-   Import the packages on which the current package depends.
-   Import the **index** file from the **lib** folder of the current package.

Examples:

[site-read.ts](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-structure/test/mocha/nodes/site-read.ts) from the xtrem-structure package.

![images](assets/images/mocha1.png)

[legislation-read-update.ts](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-structure/test/mocha/nodes/legislation-read-update.ts) from the xtrem-structure package.

![images](assets/images/mocha2.png)

### Writing the main part of the unit test

#### Mocha functions

Some Mocha functions are used to structure your unit test.

-   **describe()**: It is used to group, which you can nest as deep;
-   **it()**: It is the test case;
-   **before()**: It is a hook to run before the first it() or describe();
-   **beforeEach()**: It is a hook to run before each it() or describe();
-   **after()**: It is a hook to run after it() or describe();
-   **afterEach()**: It is a hook to run after each it() or describe();

Pattern example:

```
describe('describe 1', function() {
describe('describe 1.1', function(){
beforeEach(function(){
// some code
});
it('it 1', function(){
// some other code
});
});
describe('describe 1.2', function(){
before(function(){
// some code
});
it('it 2', function(){
// some other code
});
});
});
```

Examples:

[site-read.ts](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-structure/test/mocha/nodes/site-read.ts) from the xtrem-structure package.

[legislation-read-update.ts](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-structure/test/mocha/nodes/legislation-read-update.ts) from the xtrem-structure package.

[country-api-read.ts](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-structure/test/mocha/nodes/country-api-read.ts) from the xtrem-structure package.

#### Environment variables

It's very useful to use environment variable, especially `today`, when using the date of the day to format the document number.

[Example: Stock Journal](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/adapters/xtrem-intacct-gateway/test/mocha/nodes/stock-journal.ts)

#### Customization

[Xtrem test framework allows customization](https://confluence.sage.com/display/XTREEM/Mocha+tests+configuration)

## Running tests

### Running selected tests

You have to run it from the package directory of the unit test.

In each package, those commands are available:

-   **pnpm run test:unit** run the mocha tests
-   **pnpm run test** run the graphql & mocha tests
-   **pnpm run test:ci** run the graphql & mocha tests and calculate the code coverage.

To execute a selection of unit tests:

`pnpm run xtrem test %filter% --unit`<br>
with `%filter%` a pattern or a test name.

### Running tests command

Please [Refer to this page](https://confluence.sage.com/display/XTREEM/Test+command) to access to the test command of the xtrem platform.

### Test Execution Mode

By default a test is executed, however, you can bring this precision to manage the way of execution:

-   `it.skip`: to skip this test.
-   `it.only`: to execute only this test.

The `skip` can be used at the `describe` level depending on what you need to skip.

## Debugging

Please [Refer to this page.](https://confluence.sage.com/display/XTREEM/Debug)

## Complementary resources

-   [Mocha guide](https://mochajs.org/)
-   [Chai guide](https://www.chaijs.com/guide/styles/)
