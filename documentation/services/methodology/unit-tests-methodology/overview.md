PATH: XTREEM/Unit+Tests/Overview

The test data management set of tools and processes allows to create unit tests and their data.

# Data

Data for tests is organized in **CSV files** per node and node extension through **packages** and **layers** and **extension-layers**.

[More details.](https://confluence.sage.com/display/XTREEM/Data+Tests+Standards)

# Unit tests

## Unit test types

_Units tests_ are developed with **mocha-chai**, **GraphQL** and **Cucumber**, each of them being respectively dedicated to test: procedures, published nodes and UI.

![Drawio](assets/drawio/unit-test-types.drawio.png))

### Mocha / Chai

Goal: To test functions, utilities, libraries.

Mocha is a JavaScript testing framework that runs on Node.js and in the browser.

Chai is a BDD/TDD assertion library for Nodes and the browser. It can be used with any testing framework (like Mocha).

[More details.](https://confluence.sage.com/display/XTREEM/Mocha+and+Chai)

### GraphQL

Goal: To test queries and mutations on database.

GraphQL is a query language for your API, and a server-side runtime for executing queries by using a type system you define for your data.

[More details.](https://confluence.sage.com/display/XTREEM/GraphQL)

### Cucumber

Goal: To test the UI.

Cucumber is a software tool that supports behavior-driven development (BDD). Central to the Cucumber BDD approach is its plain language parser called Gherkin.

[More details.](https://confluence.sage.com/display/XTREEM/Cucumber)

## Test run environment

The environment to run the unit tests is defined in the **xtrem-config.yml** file. This file must be located directly on the main level of services repository.

If not yet created, help yourself with the xtrem-config-template.yml file with a copy and paste.

[More details.](https://confluence.sage.com/display/XTREEM/Test+With+Postgres)
