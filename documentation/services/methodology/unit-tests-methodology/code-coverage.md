PATH: XTREEM/Unit+Tests/Code+Coverage

# Code Coverage executed Locally

This tool allows you to check the percentage of code covered by unit tests for a package.

This has to be done locally in your development branch before creating a Pull Request to update the master branch.

As you may know, we deliver in a Pull Request developments and unit tests that have to cover a certain percentage, 80% actually.

This percentage will soon be an automatic criteria of acceptance for Pull Requests.

## How to run the code coverage analysis?

Firstly, note that as it runs locally on your feature branch, there is no need to commit and push your developments and tests right before.

1. Go to the higher level of your package. Example with the xtrem-system package.

![code-coverage1](assets/images/code-coverage1.png)

2. Start your Postgres database.
3. Execute the code coverage analysis entering: `pnpm run test:ci`<br>
   This runs all tests of the package.

4. At the end of the process,

-   The percentages of code coverage of your package are displayed,

    ![code-coverage3](assets/images/code-coverage3.png)

-   A **coverage** folder has been created.

    ![code-coverage2](assets/images/code-coverage2.png)

## How to use the results of the code coverage analysis?

With the results, you are able now to go deeper in the analysis.

1. Through your file explorer, go to the coverage then lcov-report folders.
2. Open the **index** file with your browser.

![code-coverage4](assets/images/code-coverage4.png)

3. Go deeper in the navigation where the percentage is low

![code-coverage5](assets/images/code-coverage5.png)

![code-coverage6](assets/images/code-coverage6.png)

4. Open a script with a low percentage.

You can see the lines in red not covered by unit tests.
Thus, you will be able to increase your tests accordingly.

![code-coverage7](assets/images/code-coverage7.png)
