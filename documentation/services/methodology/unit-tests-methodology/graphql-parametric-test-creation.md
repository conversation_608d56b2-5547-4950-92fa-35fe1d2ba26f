PATH: XTREEM/Unit+Tests/GraphQL+Parametric+Test+Creation

# GraphQL Parametric Test Creation

## Presentation

Parametric tests are using parameters allowing to test different scenarios in one test.<br>
To make the input and output variable, we are using `handlebars`.<br>
The files to be created for a test are:

-   the `parameters.json` file,
-   the `request.graphql.hbs` file,
-   the `response.json.hbs` file.

There are 4 kinds of tests with variables, according to what they use:

1. Variables
2. Input
3. Environment variables
4. Mocks

More details [on this dedicated page.](https://confluence.sage.com/display/XTREEM/2+Parametric+input+and+output)

## Advantages

They can provide good code coverage in one test.

## Standards

### Tests Naming and Storage

A test is stored in a dedicated folder, using 2 or 3 files. Please, find the **naming and storage standards**, [on this dedicated page.](https://confluence.sage.com/x/dodYE)

## Handlebars

### What is Handlebars?

Handlebars is a simple templating language.<br>
Handlebars templates look like regular text with embedded Handlebars expressions.<br>
Handlebars is essentially a combination of data and layout. Your data and how the data is presented are kept separate from each other. Thus, you can change:

-   either, the data,
-   or, the presentation lays out the data.
    Handlebars is usually used for taking repeated data records.<br>

[More details in the Handlebars guide](https://handlebarsjs.com/guide/)

### Main syntaxes

-   `{{data name field}}`: property
-   `{{#each}} ... {{/each}}`: array
-   `{{#if}} ...{{else}} ... {{/if}}`: condition

**Helpers**

-   `{{#with}} ... {{/with}}`: nested property
-   `{{#unless}} ... {{/unless}}`: result given if the condition is false
-   `{{function data}}` : function applied to your data (custom helper)

### Helpers in Xtreem

-   Using `{{inputParameters}}` in the **data key** of the **request.json.hbs** file will replace the placeholder with the whole content of the properties description from the parameters.json file.
-   Use `{{{output}}}` in the **response.json.hbs** file will replace the placeholder with the whole content of the **output key** from the parameters.json file.
-   Without using `{{inputParameters}}` or `{{{output}}}`, the framework will replace one by one each placeholder found in the file with handlebars standard replacement.

### Comments

Comments are recommended to help understanding and maintenance.

-   `{{! This comment will not show up in the output}}`
-   `{{!-- This comment may contain mustaches like }} --}}`

## Tests with variables

### Presentation

This is the first level of settings through variables.

1. Declare variables you need in the **variables key** in the **parameters.json** file.
2. Use the variables prefixed by the '$' character in the **request.json.hsb** file.

More details [on this dedicated page.](https://confluence.sage.com/display/XTREEM/1+Method+with+variables)

### Syntax

Each scenario in the **parameters.json** file is made of:

```
{
    "Scenario Title": {
        "variables": {},
        "output": {}
    }
}
```

### Examples

-   [Item query filter variable](https://github.com/Sage-ERP-X3/xtrem/tree/master/services/shared/xtrem-master-data/test/graphql/item-query-variable)

## Tests with input

### Presentation

The `parameters.json` file can contain several sets of input values allowing to run the request as many times as there are sets of values. More details [on this dedicated page.](https://confluence.sage.com/display/XTREEM/2+Method+with+input)

### Syntax

Each scenario in the **parameters.json** file is made of:

```
{
    "Scenario Title": {
        "input": {},
        "output": {}
    }
}
```

### Examples

-   Request using {{data field name}} and mock:
    [Country creation](https://github.com/Sage-ERP-X3/xtrem/tree/master/services/shared/xtrem-master-data/test/graphql/country-create)
-   Query demonstrating a variety of possibilities:
    [Stock status query](https://github.com/Sage-ERP-X3/xtrem/tree/master/services/shared/xtrem-inventory/test/graphql/stock-status-query)
-   Request using `{{inputParameters}}` and Response using `{{{output}}}`:
    [Business entity create](https://github.com/Sage-ERP-X3/xtrem/tree/master/services/shared/xtrem-master-data/test/graphql/business-entity-create)
-   Request using {{#each}}...{{#each}}, {{unless}}, `today` environment variable.
    [Sales shipment confirm](https://github.com/Sage-ERP-X3/xtrem/tree/master/services/applications/xtrem-sales/test/graphql/sales-shipment-confirm)

## Environment variables

### Presentation

-   Fill the **envConfigs** field with any or all supported variables in a scenario of the **parameters.json** file.
-   Thus, the framework will stub the normal behavior of the provided system functions and force them to return the values specified in parameters.json.
-   If the same value is expected in multiple scenarios, the **envConfigs** field needs to be passed to each scenario separately.

More details:

1. [Environment variables description](https://confluence.sage.com/display/XTREEM/2+Environmental+variables)
2. [Environment variables usage](https://confluence.sage.com/display/XTREEM/4+Provide+environmental+variables+to+GraphQL+tests)

### Syntax

```
{
    "Scenario Title": {
        "input": {},
        "output": {}
        "envConfigs":{}
    }
}
```

### Examples

-   [Sales shipment creation](https://github.com/Sage-ERP-X3/xtrem/tree/master/services/adapters/xtrem-intacct-gateway/test/graphql/create-sales-shipments)

## Mock plugins

### Presentation

The framework provides the possibility for a developer to mock given modules during a test's execution.

More details:

1. [Mocks description](https://confluence.sage.com/display/XTREEM/3+Mocks)
2. [Mocks usage](https://confluence.sage.com/display/XTREEM/6+Provide+mocks+to+GraphQL+tests)
3. [Mocks technical details](https://confluence.sage.com/display/XTREEM/Mock+-+Externals+Api)

### Syntax

```
{
    "Scenario Title": {
        "input": {},
        "output": {}
        "envConfigs":{
            "mocks": ["axios"]
        }
    }
}
```

### Examples

Using the `axios` plugin:<br>

-   [Country creation](https://github.com/Sage-ERP-X3/xtrem/tree/master/services/shared/xtrem-master-data/test/graphql/country-create)
-   [Currency creation](https://github.com/Sage-ERP-X3/xtrem/tree/master/services/shared/xtrem-master-data/test/graphql/currency-create)
