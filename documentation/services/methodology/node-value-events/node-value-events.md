PATH: XTREEM/Nodes/Node+Value+Events

# Node value events

## Presentation

The list of possible node events to be implemented by the applicative developer is detailed here.

## Events

### defaultValue()

This event is fired if no value is specified for this property when creating an instance of the node.<br>
This event should be used wherever possible.<br>
This event is not fired when creating an instance of a node from a query or read.

Example with the [purchase-order-line.ts](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/applications/xtrem-purchasing/lib/nodes/purchase-order-line.ts) node: <br>

![defaultvalue](assets/images/defaultvalue.png)

Example with the [purchase-order-create-test.ts](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/applications/xtrem-purchasing/test/mocha/nodes/purchase-order-create-test.ts) test:

When the purchase order line is created as part of the purchase order create, because there is no value provided for the quantityInStockUnit property the defaultValue event is fired.

![defaultvalue2](assets/images/defaultvalue2.png)

### getValue()

When a property is accessed this event is fired to return the value.<br>
This event **must not** be used with stored properties `isStored: true`.<br>
This event is used on computed properties.<br>
Currently, you cannot filter using a computed property.<br>

Example with the [purchase-order-line.ts](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/applications/xtrem-purchasing/lib/nodes/purchase-order-line.ts) node: <br>

![getvalue](assets/images/getvalue.png)

In a mocha test we have created a new purchase order line. Now we want to test that the origin property is set to a particular value. As we are accessing the origin property the getValue event is fired.

![getvalue2](assets/images/getvalue2.png)

### setValue() (Likely to be deprecated)

This event is fired when an assignment is made to the property.It is used to update other values based on this value.

**Pay attention!** This event should be avoided as it is likely to be deprecated. It should be possible to use updatedValue instead.

Example with the [item-site-supplier.ts](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-master-data/lib/nodes/item-site-supplier.ts) node:

![setvalue](assets/images/setvalue.png)

When the priority is assigned, as part of creating the new instance, the setValue event is fired where we set the value of the \_storedPriority. So, in the setValue event we can update other properties.

![setvalue2](assets/images/setvalue2.png)

### adaptValue()

This event is fired when an assignment is made to the property. <br>
This event is used to update or adapt the current property. <br>
This event is often used to limit the scope of the value.

Example with the adaptValue function defined in the [ValueDataType](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-master-data/lib/data-types/value-data-type.ts).

![adaptvalue](assets/images/adaptvalue.png)

The priceDataType is an instance of the [ValueDataType class](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-master-data/lib/data-types/value-data-type.ts).

![adaptvalue2](assets/images/adaptvalue2.png)

Is it used in the price property of the [PurchaseOrderLine](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/applications/xtrem-purchasing/lib/nodes/purchase-order-line.ts).

![adaptvalue3](assets/images/adaptvalue3.png)

When the price is changed, the adaptValue event is fired to set the value based on the decimal places determined by the data type or currency if it exists.

### updatedValue()

This event is fired when a property has a **dependsOn** assignment and one of the dependsOn properties changes. The event is not fired immediately, only when the property is accessed, this means the updatedValue event is fired only once regardless of the number of dependsOn properties changed.

Example with the [purchase-order-line.ts](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/applications/xtrem-purchasing/lib/nodes/purchase-order-line.ts) node: <br>

![defaultvalue](assets/images/defaultvalue.png)

This property depends on three other properties, `purchaseUnit`, `stockUnit` and `quantity`. So, if we change for example the quantity property the updatedValue event is fired.

Example with the [purchase-order-update-test.ts](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/applications/xtrem-purchasing/test/mocha/nodes/purchase-order-update-test.ts) test:

![updatevalue](assets/images/updatevalue.png)

### updatedValue() use vs getValue() use

-   The updatedValue calculates the value for the property and then stores it in the database when the dependsOn references change.
-   The getValue returns the computed property each time it is requested and it is not stored.
-   There are performance and accuracy considerations with both approaches. That is why it needs to be carefully considered whether a getValue should be used instead of updatedValue. For example, in purchaseOrderLine there is a property totalTaxExcludedAmount, this has an updatedValue of return this.quantity \* this.price - we could use getValue instead as we always want this calculation and so it would not need to be stored.
