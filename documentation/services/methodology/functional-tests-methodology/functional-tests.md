PATH: XTREEM/Methodology/Functional+Tests

# Methodology and process for functional tests

- [Functional Tests Creation](https://confluence.sage.com/display/XTREEM/Functional+Tests+Creation)
- [Functional Tests Execution Latest Version](https://confluence.sage.com/display/XTREEM/Functional+Tests+Execution+Latest+Version)
- [Functional Tests Execution Previous release](https://confluence.sage.com/display/XTREEM/Functional+Tests+Execution+Previous+release)
- [Functional Tests Execution with Another user](https://confluence.sage.com/display/XTREEM/Functional+Tests+Execution+With+Another+User)
- [Functional tests pipelines](https://confluence.sage.com/display/XTREEM/Functional+Tests+Pipelines)
