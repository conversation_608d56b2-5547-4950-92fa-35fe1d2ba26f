PATH: XTREEM/Functional+Tests/Functional+Tests+Execution+With+Another+User

# How to execute cucumber functional tests with another user

- This documentation aims at explaining how to execute a test with a secondary or third ATP / XTreeM user.
- This can be useful to execute tests with another user that has different habilitations than the main ATP / XTreeM user.

## Glossary

- Refer to the following page [ATP XTreeM Glossary](https://confluence.sage.com/display/XTREEM/ATP+XTreeM+Glossary/) to have more information on the terminology used in this page.

## Limitations

- Connection with a secondary or third user is sageId authentication or unsecure authentication.
- It is possible to execute those tests in local if prodUI=true.
- Sageid Authentication requires to use specific sageid email address.
    - Don't use your personal sageid account to execute the functional tests.
    - The sageid account used to execute the functional tests are the following ones:
        - [ATP / XTreeM - sageidatp accounts](https://confluence.sage.com/pages/viewpage.action?pageId=*********)
        - Use [XT Tool (Cucumber)](<https://teams.microsoft.com/l/channel/19%3a286f797c8eb54a31a722b602f4798745%40thread.skype/XT%2520Tool%2520(Cucumber)?groupId=668c69fa-59b5-4e04-bde9-b6e733d2cda1&tenantId=3e32dd7c-41f6-492d-a1a3-c58eb02cf4f8>) teams channel to ask for the credentials.

## Cucumber login / logout scenario

### Example of login / logout scenario for xtrem-services

- When executing a test with sageid authentication, the main ATP / XTreeM sageidatp user is automatically used when the robot browse a page.

    - The sageid account used to execute the functional tests are the following ones:
        - [ATP / XTreeM - sageidatp accounts](https://confluence.sage.com/pages/viewpage.action?pageId=*********)
        - Use [XT Tool (Cucumber)](<https://teams.microsoft.com/l/channel/19%3a286f797c8eb54a31a722b602f4798745%40thread.skype/XT%2520Tool%2520(Cucumber)?groupId=668c69fa-59b5-4e04-bde9-b6e733d2cda1&tenantId=3e32dd7c-41f6-492d-a1a3-c58eb02cf4f8>) teams channel to ask for the credentials.

- If you need to connect with another user then the main ATP / XTreeM user, you have to add the following step definition prior to browse the page:

    ```bash
    Scenario: Login
        the user is logged into the system in desktop mode using the "param:loginUserName2" user name and "param:loginPassword2" password
    ```

- If you were already logged into the application and need to log in with a different user, you have to disconnect first from the application.

    ```bash
       Scenario: Logout
        When the user logs out of the system
    ```

- Example: [prerequisites-flow-user-authentication-verification](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/functional-tests/xtrem-0-prerequisites-test/test/prerequisites-flow-user-authentication-verification.feature)

    ```bash
        Scenario: connect with a secondary user and verify it is possible to browse the page with that user
            When the user is logged into the system in desktop mode using the "param:loginUserName2" user name and "param:loginPassword2" password
            Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Allergen"
            Then the "Allergens" titled page is displayed
            And the user logs out of the system
    ```

### Example of login / logout scenario for ADC

- When executing a test with sageid authentication, the <NAME_EMAIL> automatically used when the robot browse a page.
- If you need to connect with another user then the main ATP / XTreeM user, you have to use the following login scenario:

    ```bash
    Scenario: Login
        When the user is logged into the system in mobile mode using the "param:loginUserName2" user name and "param:loginPassword2" password
        When the user selects the "param:endPointName1" endpoint
        Then the "param:endPointName1" endpoint is selected
        Given the user opens the application on a mobile
        ....
    ```

- If you were already logged into the application and need to log in with a different user, you have to disconnect first from the application.

    ```bash
       Scenario: Logout
        When the user logs out of the system
    ```

## How to execute functional tests with the required user

Prior to execute the command to run the functional tests, the following environment parameters have to be defined:

- TARGET_URL: remote URL to connect to.
- loginUserName: sageId login for the main user
- loginPassword: sageId password of the main user.
- loginUserName2: sageId login for the secondary user
- loginPassword2: sageId password of the secondary user.
- loginUserName3: sageId login for the third user
- loginPassword3: sageId password of the third user.
- tenantName: name of tenant to connect to ( only for xtrem-services when connecting with sageId authentication)
- endPointName1: name of endpoint to connect to ( only for adc when connecting with sageId authentication)

### Example to execute (xtrem-services) functional tests on a required environment with tenant selection and sageId authentication

- Prior to run the command:

    - Move into the required functional test package (eg. functional-tests/xtrem-distribution-test)
    - Make sure parameters-atp file is created in xtrem-cli-atp package and contains the required setup

        - Follow example for Dev environment in [parameters-atp-sdmo-sageid-deveu-civ2-template](https://github.com/Sage-ERP-X3/xtrem/blob/master/platform/cli/xtrem-cli-atp/parameters-atp-template/sdmo/parameters-atp-sdmo-sageid-deveu-civ2-template)
        - Follow example for QA environment in [parameters-atp-sdmo-sageid-qana-cr-template](platform/cli/xtrem-cli-atp/parameters-atp-template/sdmo/parameters-atp-sdmo-sageid-qana-cr-template)
        - Follow example for preprod environment in [parameters-atp-sdmo-sageid-ppeu-cr-template](https://github.com/Sage-ERP-X3/xtrem/blob/master/platform/cli/xtrem-cli-atp/parameters-atp-template/sdmo/parameters-atp-sdmo-sageid-ppeu-cr-template)

- Run the command:

```bash
pnpm run test:functional --browser -- [filter]

```

## How to execute (x3-services / wh-services) functional tests on QA environment

### Example to execute (x3-services / wh-services ) functional tests on QA environment with sageId authentication

Prior to run the command:

- Move into the required functional test package (eg. functional-tests/x3-distribution-test).
- Make sure parameters-atp file is created in xtrem-cli-atp package and contains the required setup.

    - Follow example for x3-services in to [parameters-atp-adc-x3-services-sageid-qa-ci-template](https://github.com/Sage-ERP-X3/xtrem/blob/master/platform/cli/xtrem-cli-atp/parameters-atp-template/mobile-automation/x3-services/parameters-atp-adc-x3-services-sageid-qa-ci-template).
    - Follow example for x3-services in to [parameters-atp-adc-wh-services-sageid-qa-ci-template](https://github.com/Sage-ERP-X3/xtrem/blob/master/platform/cli/xtrem-cli-atp/parameters-atp-template/mobile-automation/wh-services/parameters-atp-adc-wh-services-sageid-qa-ci-template).

Run the command:

```bash
pnpm run test:functional --browser -- [filter]

```
