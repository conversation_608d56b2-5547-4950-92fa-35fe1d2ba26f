PATH: XTREEM/Functional+Tests/Functional+Tests+Pipelines

# Functional Tests Pipelines

## Glossary

- Refer to the following page [ATP XTreeM Glossary](https://confluence.sage.com/display/XTREEM/ATP+XTreeM+Glossary/) to have more information on the terminology used in this page.

## Functional tests pipelines (xtrem-services)

### Functional Tests Pipelines for Development environment

| Pipeline                                                                                                                                             | Environment URL                                | Cluster     | Tenant                                | tenantId                | Trigger                                                                                                                                 | Tests type       | Branch    | Authentication Mode | Layer    |
| ---------------------------------------------------------------------------------------------------------------------------------------------------- | ---------------------------------------------- | ----------- | ------------------------------------- | ----------------------- | --------------------------------------------------------------------------------------------------------------------------------------- | ---------------- | --------- | ------------------- | -------- |
| [`deveu-civ2-run-ft-xtrem-sdmo-accessibility_tests_civ2`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=4881)            | https://sdmo-ci-v2.eu.dev-sagextrem.com/       | ci-v2       | `accessibility_tests_civ2`            | `JpoLYoyVlPlmiillKyNjq` | [`generic-upgrade-cluster-app`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=4785)                         | functional tests | master    | unsecure            | setup,qa |
| [`deveu-civ2-run-ft-xtrem-sdmo-functional_tests_civ2`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=4883)               | https://sdmo-ci-v2.eu.dev-sagextrem.com/       | ci-v2       | `functional_tests_civ2`               | `ssKIwYYk2r0rzUcQsS8HD` | [`deveu-civ2-run-sts-xtrem-sdmo-functional_tests_civ2`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=4886) | functional tests | master    | unsecure            | setup,qa |
| [`deveu-civ2-run-ft-xtrem-sdmo-functional_tests_authorization_civ2`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=5617) | https://sdmo-ci-v2.eu.dev-sagextrem.com/       | ci-v2       | `functional_tests_authorization_civ2` | `WKIZeaKO5739s4iPyTYXf` | [`generic-upgrade-cluster-app`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=4785)                         | functional tests | master    | unsecure            | setup,qa |
| [`deveu-cr-run-ft-xtrem-sdmo-functional_tests_cr`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=4884)                   | https://sdmo-cls-release.eu.dev-sagextrem.com/ | cls-release | `functional_tests_cr`                 | `VE1GAoE_oZZbStfG47XiY` | aws-lambda                                                                                                                              | functional tests | release/n | unsecure            | setup,qa |

- Use the following pipelines to refresh the manual tenant in dev environment

| Pipeline                                                                                                                                      | Environment URL                          | Cluster | Tenant                         | tenantId                | Trigger                                                                                                         | Tests type | Branch | Authentication Mode | Layer |
| --------------------------------------------------------------------------------------------------------------------------------------------- | ---------------------------------------- | ------- | ------------------------------ | ----------------------- | --------------------------------------------------------------------------------------------------------------- | ---------- | ------ | ------------------- | ----- |
| [`deveu-civ2-run-ft-xtrem-sdmo-functional_tests_civ2_manual`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=4882) | https://sdmo-ci-v2.eu.dev-sagextrem.com/ | ci-v2   | `functional_tests_civ2_manual` | `XQUHE4MaQkWZCwId6UZvp` | [`generic-upgrade-cluster-app`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=4785) | none       | master | unsecure            | QA    |

### Functional Tests Pipelines for QA environment

- Prior to execute the pipeline, select the required release/n branch.

| Pipeline                                                                                                                                  | Environment URL                               | Cluster     | Tenant                         | tenantId                | Trigger    | Tests type       | Branch    | Authentication Mode | Layer    |
| ----------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------- | ----------- | ------------------------------ | ----------------------- | ---------- | ---------------- | --------- | ------------------- | -------- |
| [`qana-cr-run-ft-xtrem-sdmo-functional_tests_cr`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=4901)         | https://sdmo-cls-release.na.qa-sagextrem.com/ | cls-release | `functional_tests_cr`          | `uPUd27T5QszNr7XZGmt3I` | aws-lambda | functional tests | release/n | unsecure            | setup,qa |
| [`qana-cp-run-ft-xtrem-sdmo-functional_tests_cp_current`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=5710) | https://sdmo-curr-prd.na.qa-sagextrem.com/    | curr-prd    | `functional_tests_cp_currentr` | `5nCViU3PM4Y0G_nQGnSOg` | aws-lambda | functional tests | release/n | unsecure            | setup,qa |

### Functional Tests Pipelines for preprod environment

- Prior to execute the pipeline, select the required release/n branch.

| Pipeline                                                                                                                           | Environment URL                                | Cluster     | Tenant                | tenantId                | Trigger                                                                                                                       | Tests type       | Branch    | Authentication Mode | Layer    |
| ---------------------------------------------------------------------------------------------------------------------------------- | ---------------------------------------------- | ----------- | --------------------- | ----------------------- | ----------------------------------------------------------------------------------------------------------------------------- | ---------------- | --------- | ------------------- | -------- |
| [`ppeu1-cr-run-ft-xtrem-sdmo-functional_tests_cr`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=4994) | https://sdmo-cls-release.eu1.pp-sagextrem.com/ | cls-release | `functional_tests_cr` | `bS2um2AiE3pQ2riaJYw8i` | [`ppeu1-cr-run-st-xtrem-sdmo-smoke_tests_cr`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=4992) | functional tests | release/n | unsecure elevate    | setup,qa |

## Functional Tests pipelines (x3-services)

### Functional Tests Pipelines for QA CI Latests environment

| Pipeline                                                                                                                                    | Environment URL                                    | Endpoint          | Trigger                                                                                                              | Tests type       | Branch    | Authentication Mode |
| ------------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------- | ----------------- | -------------------------------------------------------------------------------------------------------------------- | ---------------- | --------- | ------------------- |
| [`qa-ci_latest-run-ft-x3-services-distribution-x3rbtrun`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=4348)   | https://x3serv-latest.sageerpx3.com/handheld/      | X3SERV / X3RBTRUN | [`Pipeline_X3SERV-LATEST`](http://env-jenkins-tst.sagex3rd.local:8080/job/X3SERV-LATEST/job/Pipeline_X3SERV-LATEST/) | functional tests | master    | sageId              |
| [`qa-ci_latest-run-ft-x3-services-manufacturing-x3rbtrun`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=4349)  | https://x3serv-latest.sageerpx3.com/handheld/      | X3SERV / X3RBTRUN | [`Pipeline_X3SERV-LATEST`](http://env-jenkins-tst.sagex3rd.local:8080/job/X3SERV-LATEST/job/Pipeline_X3SERV-LATEST/) | functional tests | master    | sageId              |
| [`qa-ci_release-run-ft-x3-services-distribution-x3rbtrun`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=4350)  | http://x3serv-release.sagex3rd.local:8124/handheld | N/A               | On demand                                                                                                            | functional tests | release/n | sageId              |
| [`qa-ci_release-run-ft-x3-services-manufacturing-x3rbtrun`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=4351) | http://x3serv-release.sagex3rd.local:8124/handheld | N/A               | On demand                                                                                                            | functional tests | release/n | sageId              |

- for `qa-ci_release-run-ft-x3-services-distribution-x3rbtrun` and `qa-ci_release-run-ft-x3-services-manufacturing-x3rbtrun`
    - Prior to execute the pipeline, select the required release/n branch.
    - As the endpoint change for each release, define the required endpoint for the release (e.g. X3CLOUDV2 / X3RBTWRKR1). You can verify the required endpoint by connecting to (solution to be defined)), and identify the one with X3RBTRUN name)
    - Make sure the environment where the tests are executed is set in the correct language (en-US)

### Functional Tests Pipelines for QA CI DevopsVnext environment

| Pipeline                                                                                                                                        | Environment URL                                     | Endpoint | Trigger                                                                                                                            | Tests type       | Branch | Authentication Mode |
| ----------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------- | -------- | ---------------------------------------------------------------------------------------------------------------------------------- | ---------------- | ------ | ------------------- |
| [`qa-ci_devopsvnext-run-ft-x3-services-distribution-x3rbtrun`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=5412)  | http://devopsvnextdis.sagex3rd.local:8124/handheld/ | X3RBTRUN | [`DEVOPSVNEXTxxx_UPDATE_WEEK_DIS`](http://env-jenkins-tst.sagex3rd.local:8080/job/DEVOPSVNEXT/job/DEVOPSVNEXTxxx_UPDATE_WEEK_DIS/) | functional tests | master | sageId              |
| [`qa-ci_devopsvnext-run-ft-x3-services-manufacturing-x3rbtrun`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=5413) | http://devopsvnextmfg.sagex3rd.local:8124/handheld/ | X3RBTRUN | [`DEVOPSVNEXTxxx_UPDATE_WEEK_MFG`](http://env-jenkins-tst.sagex3rd.local:8080/job/DEVOPSVNEXT/job/DEVOPSVNEXTxxx_UPDATE_WEEK_MFG/) | functional tests | master | sageId              |

## Functional Tests pipelines (wh-services)

### Functional Tests Pipelines for QA CI Latest environment

| Pipeline                                                                                                                                  | Environment URL                                     | Endpoint         | Trigger                                                                                                              | Tests type       | Branch    | Authentication Mode |
| ----------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------- | ---------------- | -------------------------------------------------------------------------------------------------------------------- | ---------------- | --------- | ------------------- |
| [`qa-ci_latest-run-ft-wh-services-warehousing-whrbtrun`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=5018)  | https://whserv-latest.sageerpx3.com/handheld/       | GXPRD / WHRBTRUN | [`Pipeline_WHSERV-LATEST`](http://env-jenkins-tst.sagex3rd.local:8080/job/X3SERV-LATEST/job/Pipeline_X3SERV-LATEST/) | functional tests | master    | sageId              |
| [`qa-ci_release-run-ft-wh-services-warehousing-whrbtrun`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=5019) | http://whserv-release.sagex3rd.local:8124/handheld/ | N/A              | [`Pipeline_WHSERV-LATEST`](http://env-jenkins-tst.sagex3rd.local:8080/job/X3SERV-LATEST/job/Pipeline_X3SERV-LATEST/) | functional tests | release/n | sageId              |

- for `qa-ci_release-run-ft-wh-services-warehousing-whrbtrun`

    - Prior to execute the pipeline, select the required release/n branch.
    - As the endpoint change for each release, define the required endpoint for the release (e.g. WHCLOUDV2 / WHRBTWRKR1). You can verify the required endpoint by connecting to (solution to be defined)), and identify the one with WHRBTRUN name)
    - Make sure the environment where the tests are executed is set in the correct language (en-US)

### Functional Tests Pipelines for QA CI DevopsVnext environment

| Pipeline                                                                                                                                      | Environment URL                                    | Endpoint | Trigger                                                                                                                           | Tests type       | Branch | Authentication Mode |
| --------------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------- | -------- | --------------------------------------------------------------------------------------------------------------------------------- | ---------------- | ------ | ------------------- |
| [`qa-ci_devopsvnext-run-ft-wh-services-warehousing-whrbtrun`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=5414) | http://devopsvnextwh.sagex3rd.local:8134/handheld/ | WHRBTRUN | [`DEVOPSVNEXTxxx_UPDATE_WEEK_WH/`](http://env-jenkins-tst.sagex3rd.local:8080/job/DEVOPSVNEXT/job/DEVOPSVNEXTxxx_UPDATE_WEEK_WH/) | functional tests | master | sageId              |

## Functional Tests pipelines (shopfloor X3)

### Functional Tests Pipelines for Development environment

| Pipeline                                                                                                                                                    | Environment URL                               | Cluster | Tenant                                     | tenantId                | Trigger                                                                                                         | Tests type        | Branch | Authentication Mode | Layer    |
| ----------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------- | ------- | ------------------------------------------ | ----------------------- | --------------------------------------------------------------------------------------------------------------- | ----------------- | ------ | ------------------- | -------- |
| [`deveu-civ2-run-ft-x3-shopfloor-functional_tests_x3_bizapps_civ2_next`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=5650)    | https://shopfloor-ci-v2.eu.dev-sagextrem.com/ | ci-v2   | `functional_tests_x3_bizapps_civ2_next`    | `Yccjme69uvq7kcRgvONQK` | [`generic-upgrade-cluster-app`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=4785) | functional tests  | master | unsecure            | setup,qa |
| [`deveu-civ2-run-ft-x3-shopfloor-functional_tests_x3_bizapps_civ2_current`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=5651) | https://shopfloor-ci-v2.eu.dev-sagextrem.com/ | ci-v2   | `functional_tests_x3_bizapps_civ2_current` | `uixSK67V41S6quQRctkxG` | [`generic-upgrade-cluster-app`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=4785) | Integration tests | master | unsecure            | setup,qa |

- Use the following pipelines to refresh the manual tenant in dev environment

| Pipeline                                                                                                                                                   | Environment URL                               | Cluster | Tenant                                    | tenantId                | Trigger                                                                                                         | Tests type | Branch | Authentication Mode | Layer |
| ---------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------- | ------- | ----------------------------------------- | ----------------------- | --------------------------------------------------------------------------------------------------------------- | ---------- | ------ | ------------------- | ----- |
| [`deveu-civ2-run-ft-x3-shopfloor-functional_tests_x3_bizapps_civ2_manual`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=5224) | https://shopfloor-ci-v2.eu.dev-sagextrem.com/ | ci-v2   | `functional_tests_x3_bizapps_civ2_manual` | `tykzBbcrrWp18U0GqIs7x` | [`generic-upgrade-cluster-app`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=4785) | none       | master | sageId              | QA    |

### Functional Tests Pipelines for QA environment

| Pipeline                                                                                                                                                 | Environment URL                                    | Cluster     | Tenant                                     | tenantId                | Trigger   | Tests type        | Branch    | Authentication Mode | Layer    |
| -------------------------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------- | ----------- | ------------------------------------------ | ----------------------- | --------- | ----------------- | --------- | ------------------- | -------- |
| [`qana-cr-run-ft-x3-shopfloor-functional_tests_x3_bizapps_cr_current`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=5653)   | https://shopfloor-cls-release.na.qa-sagextrem.com/ | cls-release | `functional_tests_x3_bizapps_cr_current`   | `ckDp8qCYBJ1OSgk_b8s4j` | lambda    | Integration tests | release/n | unsecure            | setup,qa |
| [`qana-cr-run-ft-x3-shopfloor-functional_tests_x3_bizapps_cr_current-1`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=5654) | https://shopfloor-cls-release.na.qa-sagextrem.com/ | cls-release | `functional_tests_x3_bizapps_cr_current-1` | `cbiuIK0D6cOAScPJfk8XZ` | On demand | Integration tests | release/n | unsecure            | setup,qa |
| [`qana-cr-run-ft-x3-shopfloor-functional_tests_x3_bizapps_cr_current-2`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=5655) | https://shopfloor-cls-release.na.qa-sagextrem.com/ | cls-release | `functional_tests_x3_bizapps_cr_current-2` | `x8BGhKNA3QCuqAME9_Fxi` | On demand | Integration tests | release/n | unsecure            | setup,qa |

### Functional Tests Pipelines for Preprod environment

| Pipeline                                                                                                                                                | Environment URL                                     | Cluster     | Tenant                                   | tenantId                | Trigger | Tests type        | Branch    | Authentication Mode | Layer    |
| ------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------- | ----------- | ---------------------------------------- | ----------------------- | ------- | ----------------- | --------- | ------------------- | -------- |
| [`ppeu1-cr-run-ft-x3-shopfloor-functional_tests_x3_bizapps_cr_current`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=5652) | https://shopfloor-cls-release.eu1.pp-sagextrem.com/ | cls-release | `functional_tests_x3_bizapps_cr_current` | `mW0UdhG1CVlBGoBgFJwUD` | lambda  | Integration tests | release/n | unsecure            | setup,qa |

## Functional Tests pipelines (shopfloor SDMO)

### Functional Tests Pipelines for Development environment

| Pipeline                                                                                                                                                     | Environment URL                               | Cluster | Tenant                                    | tenantId                | Trigger                                                                                                         | Tests type       | Branch | Authentication Mode | Layer    |
| ------------------------------------------------------------------------------------------------------------------------------------------------------------ | --------------------------------------------- | ------- | ----------------------------------------- | ----------------------- | --------------------------------------------------------------------------------------------------------------- | ---------------- | ------ | ------------------- | -------- |
| [`deveu-civ2-run-ft-sdmo-shopfloor-functional_tests_sdmo_bizapps_civ2_next`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=5645) | https://shopfloor-ci-v2.eu.dev-sagextrem.com/ | ci-v2   | `functional_tests_sdmo_bizapps_civ2_next` | `LygS6hAmKeyv8DJuZjffs` | [`generic-upgrade-cluster-app`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=4785) | functional tests | master | unsecure            | setup,qa |

- Use the following pipelines to refresh the manual tenant in dev environment

| Pipeline                                                                                                                                                       | Environment URL                               | Cluster | Tenant                                      | tenantId                | Trigger                                                                                                         | Tests type | Branch | Authentication Mode | Layer |
| -------------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------- | ------- | ------------------------------------------- | ----------------------- | --------------------------------------------------------------------------------------------------------------- | ---------- | ------ | ------------------- | ----- |
| [`deveu-civ2-run-ft-sdmo-shopfloor-functional_tests_sdmo_bizapps_civ2_manual`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=5443) | https://shopfloor-ci-v2.eu.dev-sagextrem.com/ | ci-v2   | `functional_tests_sdmo_bizapps_civ2_manual` | `syk2rGP779vndkAN8ZBhi` | [`generic-upgrade-cluster-app`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=4785) | none       | master | sageId              | QA    |

### Functional Tests Pipelines for qa environment

| Pipeline                                                                                                                                                   | Environment URL                                    | Cluster     | Tenant                                     | tenantId                | Trigger    | Tests type        | Branch    | Authentication Mode | Layer    |
| ---------------------------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------- | ----------- | ------------------------------------------ | ----------------------- | ---------- | ----------------- | --------- | ------------------- | -------- |
| [`qana-cr-run-ft-sdmo-shopfloor-functional_tests_sdmo_bizapps_cr_current`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=5648) | https://shopfloor-cls-release.na.qa-sagextrem.com/ | cls-release | `functional_tests_sdmo_bizapps_cr_current` | `0nbzkf_v2PeSpP4jSsizE` | aws-lambda | Integration tests | release/n | unsecure            | setup,qa |

### Functional Tests Pipelines for preprod environment

| Pipeline                                                                                                                                                    | Environment URL                                     | Cluster     | Tenant                                     | tenantId                | Trigger    | Tests type        | Branch    | Authentication Mode | Layer    |
| ----------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------- | ----------- | ------------------------------------------ | ----------------------- | ---------- | ----------------- | --------- | ------------------- | -------- |
| [`ppeu1-cr-run-ft-sdmo-shopfloor-functional_tests_sdmo_bizapps_cr_current`](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=5647) | https://shopfloor-cls-release.eu1.pp-sagextrem.com/ | cls-release | `functional_tests_sdmo_bizapps_cr_current` | `r3jm5AgCwb4YE2WDPUPO6` | aws-lambda | Integration tests | release/n | unsecure            | setup,qa |

## Cucumber reporting

- Refer to the following page [ATP / XTreeM - reporting](https://confluence.sage.com/x/39sTGg) to know how to verify the tests results with allure report
