PATH: XTREEM/Functional+Tests/Functional+Tests+Execution+Latest+Version

# How to execute cucumber functional tests on latest version

## Glossary

- Refer to the following page [ATP XTreeM Glossary](https://confluence.sage.com/display/XTREEM/ATP+XTreeM+Glossary/) to have more information on the terminology used in this page.

## Preliminary steps

To execute the functional tests in dev environments or locally from VScode you have to execute them from master branch or your feature branch
you will have to:

1. git checkout master (or your feature branch)
2. pnpm run clean:install
3. pnpm run build
    - ( for clean:install and build it is recommended to execute those commands outside of VScode. e.g with windows terminal or other shell).

## How to execute cucumber functional tests on localhost

1.  Load the data from the QA layer
    (only for xtrem/services not necessary for xtrem/x3)

    ```bash
    pnpm run load:qa:data
    ```

    Note:

    - For xtrem/services, the command can be executed at root level (e.g. xtrem/services) or inside a test package (e.g. xtrem-reference-data-test).

2.  Move at root level (e.g. xtrem/services) and start the service

    ```bash
    pnpm run start
    ```

    Note:

    - For xtrem/services, the command can be executed at root level (e.g. xtrem/services) or inside a test package (eg. xtrem-reference-data-test).
    - For xtrem/x3, the command can be executed inside a test package (e.g. xtrem-x3-stock).

    If the pnpm run start succeeds, you should be able to browse the following url: `http://localhost:8240/`

3.  Split the terminal in two

4.  run the command

- At root level (e.g. xtrem/services or xtrem/x3): ​
  it executes all the functional tests in headless mode (no browser displayed).​

    ```bash
    pnpm run test:functional​
    ```

- At test package level (e.g. ../xtrem-reference-distribution-test)​

    - To execute all the functional tests of the current test package and with the browser displayed:

        ```bash
        pnpm run test:functional --browser
        ```

    - To execute specific functional tests of the current test package and with the browser displayed:

        ```bash
        pnpm run test:functional --browser -- [filter]
        ```

    - **%filter%:** feature name or pattern to filter the functional tests to execute.
    - **--browser:** to display the browser during the execution, otherwise the functional tests are executed in headless mode.

    Example:

    ![functional-tests-execution-01](assets/images/functional-tests-execution-01.png)

## How to execute (xtrem-services) functional tests on a required environment

Prior to execute the command to run the functional tests, the following environment variables may have to be defined according to the authentication type chosen:

- TARGET_URL: remote URL to connect to.
- loginUserName: sageId login. (when connecting with sageId authentication)
- loginPassword: sageId password. (when connecting with sageId authentication)
- tenantName: name of tenant to connect to. (when connecting with sageId authentication)
- tenantAppName: name of application to connect to. (when connecting with sageId authentication)

### Example to execute (xtrem-services) functional tests on development environment with unsecure authentication

With unsecure authentication type, ATP/ XTreeM robot is connected directly to the xtrem development environment, in the given tenant and application without requiring to enter sageId credentials or to select the tenant and application.
Unsecure Authentication is only activate in development / QA environment.

```diff
+ Important:
+ prioritize this method to execute your functional test in development environment
```

Prior to run the command:

- Move into the required functional-tests package (eg. xtrem-reference-data-tests).
- Make sure parameters-atp file is created in xtrem-cli-atp package and contains the TARGET_URL using the unsecure authentication.

    - unsecure URL: `https://login.eu.dev-sagextrem.com/unsecuredevlogin?cluster=[clusterName]&app=[App]&tenant=[tenantId]&user=<EMAIL>`

        - Example [parameters-atp-sdmo-unsecure-dev-functional-tests-civ2-manual-template](https://github.com/Sage-ERP-X3/xtrem/blob/master/platform/cli/xtrem-cli-atp/parameters-atp-template/sdmo/parameters-atp-sdmo-unsecure-deveu-functional-tests-civ2-manual-template/).

            - Replace [clusterName] by the required cluster name.
            - Replace [tenantId] by the required tenantId.
            - Replace [App] by the required application (eg. sdmo).

Run the command:

```bash
pnpm run test:functional --browser -- [filter]
```

- List of the functional test tenants / tenantId in development environment:
    - Refer to the following page [functional Tests Pipelines](https://confluence.sage.com/display/XTREEM/Functional+Tests+Pipelines/) to have the list of tenants and tenanId where the functional tests are executed.

### Example to execute (xtrem-services) functional tests on a required environment with tenant selection and sageId authentication

With sageId authentication type, ATP/ XTreeM robot is connected to the required xtrem-services environment after entering the required sageId credentials and selecting the tenant and application.

```diff
- Important:
- To execute your functional tests in development environment, prioritize the method with unsecure authentication.
- sageid authentication method is required when you need to connect to preprod or prod environments.
```

Prior to run the command:

- Move into the required functional test package (eg. functional-tests/xtrem-distribution-test).
- Make sure parameters-atp file is created in xtrem-cli-atp package and contains the required setup.
    - Follow example for Dev environment in [parameters-atp-sdmo-sageid-deveu-civ2-template](https://github.com/Sage-ERP-X3/xtrem/blob/master/platform/cli/xtrem-cli-atp/parameters-atp-template/sdmo/parameters-atp-sdmo-sageid-deveu-civ2-template).

Run the command:

```bash
pnpm run test:functional --browser -- [filter]
```

- Refer to the following documentation if you need to execute a test with a secondary user: [Functional Tests Execution with a secondary user](https://confluence.sage.com/display/XTREEM/Functional+Tests+Execution+With+Secondary+User).

- Use [XT Tool (Cucumber)](<https://teams.microsoft.com/l/channel/19%3a286f797c8eb54a31a722b602f4798745%40thread.skype/XT%2520Tool%2520(Cucumber)?groupId=668c69fa-59b5-4e04-bde9-b6e733d2cda1&tenantId=3e32dd7c-41f6-492d-a1a3-c58eb02cf4f8>) teams channel to ask for the credentials.

## How to execute (x3-services / wh-services) functional tests on QA environment

### Example to execute (x3-services / wh-services) functional tests on QA environment with sageId authentication

In this mode, the ATP/ XTreeM robot is connected to x3-services / wh-services QA environment after entering the sageId credentials.

- TARGET_URL: remote URL to connect to.
- loginUserName: sageId login.
- loginPassword: sageId password.
- endpointName: name of endpoint to connect to.

Prior to run the command:

- Make sure the environment where the tests are executed is set in the correct language (en-US).
- Move into the required functional test package
    - (eg for x3-services. functional-tests/x3-distribution-test).
    - (eg for wh-services. functional-tests/wh-warehousing-test).
- Make sure parameters-atp file is created in xtrem-cli-atp package and contains the required setup.
    - Follow example for x3-services in to [parameters-atp-adc-x3-services-sageid-qa-ci-template](https://github.com/Sage-ERP-X3/xtrem/blob/master/platform/cli/xtrem-cli-atp/parameters-atp-template/mobile-automation/x3-services/parameters-atp-adc-x3-services-sageid-qa-ci-template).
    - Follow example for wh-services in to [parameters-atp-adc-wh-services-sageid-qa-ci-template](https://github.com/Sage-ERP-X3/xtrem/blob/master/platform/cli/xtrem-cli-atp/parameters-atp-template/mobile-automation/wh-services/parameters-atp-adc-wh-services-sageid-qa-ci-template).

Run the command:

```bash
pnpm run test:functional --browser -- [filter]
```

- Refer to the following documentation if you need to execute a test with a secondary user: [Functional Tests Execution with a secondary user](https://confluence.sage.com/display/XTREEM/Functional+Tests+Execution+With+Secondary+User)

- Use [XT Tool (Cucumber)](<https://teams.microsoft.com/l/channel/19%3a286f797c8eb54a31a722b602f4798745%40thread.skype/XT%2520Tool%2520(Cucumber)?groupId=668c69fa-59b5-4e04-bde9-b6e733d2cda1&tenantId=3e32dd7c-41f6-492d-a1a3-c58eb02cf4f8>) teams channel to ask for the credentials.

## How to execute (shopfloor) functional tests on a required environment

Prior to execute the command to run the functional tests, the following environment variables may have to be defined according to the authentication type chosen:

- TARGET_URL: remote URL to connect to.
- loginUserName: sageId login. (when connecting with sageId authentication)
- loginPassword: sageId password. (when connecting with sageId authentication)
- tenantName: name of tenant to connect to (when connecting with sageId authentication)
- tenantAppName: name of application to connect to (when connecting with sageId authentication)

### Example to execute (shopfloor) functional tests on development environment with unsecure authentication

With unsecure authentication type, ATP/ XTreeM robot is connected directly to the xtrem development environment, in the given tenant and application without requiring to enter sageId credentials or to select the tenant and application
unsecure Authentication is only activate in development / QA environment.

```diff
+ Important:
+ prioritize this method to execute your functional test in development environment
```

Prior to run the command:

- Move into the required functional-tests package (eg. shopfloor-x3-test).
- Make sure parameters-atp file is created in xtrem-cli-atp package and contains the TARGET_URL using the unsecure authentication.

    - unsecure URL: `https://login.eu.dev-sagextrem.com/unsecuredevlogin?cluster=[clusterName]&app=[App]&tenant=[tenantId]&user=<EMAIL>`

        - Example for shoopfloor App connected to X3: [parameters-atp-bizapps-x3-unsecure-deveu-functional-tests-x3-bizapps-civ2-manual-template](https://github.com/Sage-ERP-X3/xtrem/blob/master/platform/cli/xtrem-cli-atp/parameters-atp-template/bizapps/bizapps-x3/parameters-atp-bizapps-x3-unsecure-deveu-functional-tests-x3-bizapps-civ2-manual-template).

            - Replace [clusterName] by the required cluster name.
            - Replace [tenantId] by the required tenantId.
            - Replace [App] by the required application (eg. sdmo).

Run the command:

```bash
pnpm run test:functional --browser -- [filter]
```

- List of the functional test tenants / tenantId in development environment:
    - Refer to the following page [functional Tests Pipelines](https://confluence.sage.com/display/XTREEM/Functional+Tests+Pipelines/) to have the list of tenants and tenanId where the functional tests are executed.
