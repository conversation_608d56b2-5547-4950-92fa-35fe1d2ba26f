PATH: XTREEM/Functional+Tests/Functional+Tests+Creation

# How to create functional tests

## Glossary

-   Refer to the following page [ATP XTreeM Glossary](https://confluence.sage.com/display/XTREEM/ATP+XTreeM+Glossary/) to have more information on the terminology used in this page.

## Where cucumber functional tests should be created?

1. Cucumber feature should be created on your feature branch.

    - Cucumber feature tests are delivered into master by doing a pull request of the feature branch.

2. To know where to create the cucumber functional tests, please refer to the following page

    - Refer to the following confluence page: [Tests-standards](https://confluence.sage.com/display/XTREEM/Tests+Standards/).

## Which codification should I use?

-   Refer to the following confluence page: [Tests-standards](https://confluence.sage.com/display/XTREEM/Tests+Standards/).

## Which Step definition can I use?

-   List of the step definitions: [Step definitions](https://confluence.sage.com/pages/viewpage.action?pageId=*********).

## Which data layer should I use?

-   For the functional tests, data from the "qa" layer are used.

## Which sageidatp account is used on the functional tests pipelines?

-   Refer to the following confluence page: [ATP / XTreeM - sageidatp accounts](https://confluence.sage.com/pages/viewpage.action?pageId=*********).
