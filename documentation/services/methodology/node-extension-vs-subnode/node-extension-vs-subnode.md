PATH: XTREEM/Nodes/Node+Extensions+vs+Subnode

# Node+Extensions+vs+Subnode

## Presentation

This page provides a quick description of node extension and subnode techniques allowing you to choose the right one in your development.

## Node extension

-   To manage functionalities of an optional package. (e.g xtrem-intacct-gateway)

[See more detail here.](https://confluence.sage.com/x/a9eQDw)

## Sub-node

-   To manage a common set of information and dedicated information per category.
    -   Thus, the common set of information is managed by the base-node
    -   and the dedicated information is managed through each sub-node.

That way, you can:

-   access common information in one place (through the base node)
-   manage information of a category (through a sub-node).

[See more detail here.](https://confluence.sage.com/x/R5mzDw)

⚠️ Please, remind that you have to [maintain the list of sub-nodes here.](https://confluence.sage.com/x/KSKUE)

Example:

-   base node: [base-resource](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-master-data/lib/nodes/base-resource.ts)
-   sub-nodes: [labor-resource](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-master-data/lib/nodes/labor-resource.ts), [machine-resource](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-master-data/lib/nodes/machine-resource.ts), [tool-resource](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-master-data/lib/nodes/tool-resource.ts).

⚠️ Note that you can encounter only one sub-node for a base node, when it's known that other categories will be developed. This is to avoid refactoring later. Current example:

-   base node: [base-capability](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-master-data/lib/nodes/base-capability.ts)
-   sub-node: [labor-capability](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-master-data/lib/nodes/labor-capability.ts).
