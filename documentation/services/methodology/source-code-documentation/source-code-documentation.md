PATH: XTREEM/Source+Code/Source+Code+Documentation

# Source Code Documentation

**This page provides a background of the source code documentation in the framework of the Xtrem project.**

## Introduction

### What is TypeDoc?

-   TypeDoc converts comments of the TypeScript source code into generated HTML documentation or a JSON model.
-   Navigate to website [TypeDoc](https://typedoc.org/).

### Why do we need this?

-   A source code documentation will help any developer understand what a function, method ... is about w/o having to read the source.

## Rules

### In which case is it required to write Source Code Documentation?

The following items must be documented:

-   **Package**
    -   What is it for
    -   See more details right after.
-   **Nodes**
    -   What is it for
    -   Properties, whether public and private.
-   **Functions** and **Methods**
    -   whether public or private
    -   What is it for
    -   Each parameter
    -   What it returns
-   **Data types**

    -   What is it for
    -   Each parameter and their default value.
    -   Variance of the result depending on parameters
    -   Take the quantity-data-type.ts of xtrem-master-data as an example.

-   See below for all the details about How to document your code with TypeDoc?

**Package**

The high level description of the package (what is it for?) must be done in the **README.md** file within each package. Don't forget to highlight the main properties.

Navigate to this page for more details on the syntax [GitHub Guides - Mastering Markdown](https://guides.github.com/features/mastering-markdown/#what)

Source code example:

![ReadMe Before](assets/images/sourcecodedoc2.png)

Generated documentation:

![ReadMe After](assets/images/sourcecodedoc1.png)

### Writing Guidelines

Please follow the writing guidelines to ensure your comment is understandable by anyone.

-   Write **short** sentences.
-   Use a **simple, concise and precise** language.
-   Functions and Methods: start each comment with a verb

    -   For e.g.: Use this parameter to specify the number of decimals

-   Start each sentence with a capital letter.
-   End each sentence with a period.
-   Add a **line break** to separate the parameters from what is returned.

-   **Comment while coding** - you won't have to go back after it's working to document every piece, so it's best to do it while it's still fresh in your mind
    -   **Note the source code documentation is one of the items in the code review checklist**

```/**
 * This function compute the available stock in a given site
 *
 * @param productRef This parameter is related to product reference.
 * @param siteRef The site where the product is stocked.
 * @param arg A Generic type for describing some arguments related to the quality of the stock.
 *
 * @typeparam T  The generic type T.
 *
 * @return The number of the available stock in the site.
 */
function computeAvailableStock<T>(productRef: string, siteRef: string, arg: T): number {
    return 1;
};
```

### Thing to note

Keep in mind that while for the time being the documentation is for internal use only, it will probably be shared externally in the near future.

## How to document your code with TypeDoc?

The full **standard TypeDoc documentation** is available here [#Document your code](https://typedoc.org/guides/doccomments/)

This page provides guidelines and examples as to how comment your source code in the framework of the Xtrem project.

TypeDoc is mainly based on the usage of **TAGS** in addition to comments.

### How to add comments?

The full standard TypeDoc documentation is available here [#comment-parsing](https://typedoc.org/guides/doccomments/#comment-parsing).
A comment must be placed immediately before the code being documented.

Each comment must start with a /\*\* sequence. Pressing the the **Enter key** generates a minimal documentation.

**Example for a function**

A simple example:

![Function basic example](assets/images/function.gif)

A more complete TypeDoc comment looks like this:

```
/**
 * This function compute the available stock in a given site
 *
 * @param productRef This parameter is related to product reference.
 * @param siteRef The site where the product is stocked.
 * @param arg A Generic type for describing some arguments related to the quality of the stock.
 *
 * @typeparam T  The generic type T.
 *
 * @return The number of the available stock in the site.
 */
function computeAvailableStock<T>(productRef: string, siteRef: string, arg: T): number {
    return 1;
};
```

The generated documentation is as follows:

![Generated Doc 1](assets/images/gen-doc1.png)

### How to format your comments in TypeDoc?

#### Markdown

Use the following [Markdown](https://typedoc.org/guides/doccomments/#markdown) syntax to format your comments (bold, italics...):

```
/**
 *
 * # This is an H1 Title
 * ## This is an H2 Title
 * ###### This is an H6 Title
 *
 * __This will also be bold__
 *
 * _This will also be italic_
 *
 * 1. Item 1
 * 2. Item 2
 * 3. Item 3
 *  * Item 3a
 *  * Item 3b
 *
 * Task list
 * - [x] this is a complete item
 * - [ ] this is an incomplete item
 * - [x] @mentions, #refs, [links](), **formatting**, and tags supported
 * - [x] list syntax required (any unordered or ordered list supported)
 *
 * [Sage](https://www.sage.com) This is the website of Sage
 *
 *
 * This function compute the available stock in a given site
 *
 * @param productRef This parameter is related to product reference.
 * @param siteRef The site where the product is stocked.
 * @param arg A Generic type for describing some arguments related to the quality of the stock.
 *

 * @typeparam T  The generic type T.
 *
 * @return The number of the available stock in the site.
 */
function computeAvailableStock<T>(productRef: string, siteRef: string, arg: T): number {
    return 1;
};
```

The generated doc is as follows:

![Generated Doc 2](assets/images/gen-doc2.png)

#### Code Blocks

The full standard TypeDoc documentation is available here [#Code Blocks](https://typedoc.org/guides/doccomments/#code-blocks).

````

/**
 * This function compute the available stock in a given site
 *
 * ```typescript
 * // You can call this function as follow
 * let stockAvailable = computeAvailableStock<number>("PRODUCT1", "SITE1", 12);
 * // or
 * let stockAvailable = computeAvailableStock<string>("PRODUCT1", "SITE1", "A");
 * ```
 *
 * @param productRef This parameter is related to product reference.
 * @param siteRef The site where the product is stocked.
 * @param arg A Generic type for describing some arguments related to the quality of the stock.
 *
 * @typeparam T  The generic type T.
 *
 * @return The number of the available stock in the site.
 */
function computeAvailableStock<T>(productRef: string, siteRef: string, arg: T): number {
    return 1;
};
````

The generated doc is as follows:

![Generated Doc 3](assets/images/gen-doc3.png)

#### Symbol References

The full standard TypeDoc documentation is available here [#symbol-references](https://typedoc.org/guides/doccomments/#symbol-references).

To link to other classes, interfaces, members or functions use the **double square brackets**.

```
/**
 * This function compute the available stock in a given site
 *
 * @param productRef This parameter is related to product reference.
 * @param siteRef The site where the product is stocked.
 * @param arg An interface related to the quality of the stock.
 *
 * @typeparam StockQualityInterfaceIs an Interface [[StockQualityInterface]] .
 *
 * @return The number of the available stock in the site.
 */
function computeAvailableStock<StockQualityInterface>(productRef: string, siteRef: string, arg: StockQualityInterface): number {
    return 1;
};


/** More details StockQualityInterface */
interface StockQualityInterface{}
```

The generated doc is as follows:

![Generated Doc 4](assets/images/gen-doc4.png)

### Supported Tags

The full standard TypeDoc documentation is available here [#supported-tags](https://typedoc.org/guides/doccomments/#supported-tags).

## Documentation and Generation

### What does it look like?

The global page provides an overview with the list of enums, classes, interfaces, variables, functions... for the selected package.
Any link in the page is clickable.
The right hand side panel provides a quick access to any item wherever you are on the page.

**Note** the different colors are depending on the type of item.

![Example](assets/images/example.png)

A page for a class looks like this:
The right hand side panel show where you are in the hierarchy wherever you are on the page.

![Class Example](assets/images/classexample.png)

### How is the documentation generated?

> ⚠️ **[IMPORTANT] Development is in progress: automatic generation through jenkins**

### How to generate the documentation on your branch?

If you're busy developing a new feature and you'd like to check how is the documentation, based on your source code comments will look like, do as follow:

-   In your package, run the documentation generation with `pnpm run doc`

It updates a **docs** folder in your package with the following sub-folders

![Folders](assets/images/gen-doc5.png)

You can then open documentation **index.html**

Note the output is setup in the typedoc.json file in each package.

![Output](assets/images/output.png)
