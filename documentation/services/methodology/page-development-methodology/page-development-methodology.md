PATH: XTREEM/Methodology/Page+Development+Methodology

# Page Development Methodology

## Prerequisites

Firstly, do not forget to comply with the [UX and UI Standards](https://confluence.sage.com/display/XTREEM/UX+and+UI+standards).

## Page structure

There are 3 sections in a page definition:

-   Import
-   UI decorators
-   Class Export

## Import

Link to the [GraphQL Client Library](https://confluence.sage.com/x/RrQdDg)

Import the technical framework components (data types, decorators...) defined in Xtrem Platform you need to develop your page

-   Import the ui base class from xtrem-ui <br>
    `import * as ui from '@sage/xtrem-ui';`

-   Import the types required for a GraphQl query - for e.g. for a page in the xtrem-master-data package <br>
    `import { GraphApi } from '@sage/xtrem-master-data-api';`

-   Import library definitions - for e.g. <br>
    `import { withoutEdges, decimal, integer } from '@sage/xtrem-client';`

## UI decorators

Note that in the Xtrem framework, the page validation is done w/o referring to the server except where business logic is required.

Link to the [Client Framework chapter - Page](https://confluence.sage.com/x/ZbQdDg).

**Basic example** [Country](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-master-data/lib/pages/country.ts)

```
@ui.decorators.page<Country>({
    title: 'Country',
    node: '@sage/xtrem-structure/Country',
    module: 'xtrem-master-data',
    menuItem: companyData,
    onLoad() {
        this.deleteCountry.isDisabled = !!!this.$.values._id;
    },
    navigationPanel: {
        listItem: {
            titleLine: ui.nestedFields.text({ bind: 'name' }),
            line2: ui.nestedFields.text({ bind: 'id', canFilter: true }),
        },
        optionsMenu: [
            {
                title: 'All',
                page: '@sage/xtrem-master-data/Country',
            },
        ],
    },
    crudActions() {
        return {
            create: [this.newCountry],
        };
    },
    businessActions() {
        return [this.saveCountry, this.deleteCountry];
    },
})
```

**Example with more complex navigation panel, onload function** [Work Order](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/applications/xtrem-manufacturing/lib/pages/work-order.ts)

## Class Export

There's at least one section, one block and one field in an Xtrem page.

### Section

Basic example [Country](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-master-data/lib/pages/country.ts)

```
    @ui.decorators.section<Country>({
        title: 'General',

    })
    generalSection: ui.containers.Section;
```

### Block

Each block has a parent, the section it's in.

Basic example [Country](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-master-data/lib/pages/country.ts)

```
    @ui.decorators.block<Country>({
        parent() {
            return this.generalSection;
        },
        title: 'Country information',
        width: 'large',
    })
    mainBlock: ui.containers.Block;
```

### Fields

Each field has a parent where block it's in.

As of today, if there are multiple sections, they'll appear as tabs on the page.

As a general rule, the field titles in pages should be the same or at least as close as possible to the property name:

-   They may differ to improve the user experience. See [UX and UI Standards](https://confluence.sage.com/display/XTREEM/UX+and+UI+standards) for more details
-   Note that you may have fields with a different name but you'll have to populate the values yourself.

Link to the [Client Framework chapter - UI widget fields](https://confluence.sage.com/x/Z7QdDg)

There are a number of decorators that define how the field is displayed and what validation there is.

#### Text

[Example: Country](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-master-data/lib/pages/country.ts)

```
    @ui.decorators.textField<Country>({
        parent() {
            return this.mainBlock;
        },
        title: 'ID',
        isMandatory: true,
    })
    id: ui.fields.Text;

```

#### Numeric

[Example: Item site](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-master-data/lib/pages/item-site.ts)

```
 @ui.decorators.numericField<ItemSite>({
        parent() {
            return this.mainBlock;
        },
        title: 'On order',
        width: 'medium',
        isReadOnly: true,
        scale() {
            return this.unitScale();
        },
        postfix() {
            return this.unitSymbol();
        },
    })
```

#### Switch

[Example: Allergen](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-master-data/lib/pages/allergen.ts)

```
    @ui.decorators.switchField<Allergen>({
        parent() {
            return this.allergenInformationBlock;
        },
        title: 'Active',
    })
    isActive: ui.fields.Switch;
```

`

#### Separator

[Example: Allergen](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-master-data/lib/pages/allergen.ts)

```
    @ui.decorators.separatorField<Allergen>({
        parent() {
            return this.allergenInformationBlock;
        },
        isFullWidth: true,
        isHidden: true,
    })
    separatorActive: ui.fields.Separator;
```

#### Reference

[Example: Item](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-master-data/lib/pages/company.ts)

```
    @ui.decorators.referenceField<Company, Currency>({
        parent() {
            return this.mainBlock;
        },
        title: 'Currency',
        node: '@sage/xtrem-master-data/Currency',
        valueField: 'name',
        canFilter: true,
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Name', canFilter: true }),
            ui.nestedFields.text({ bind: 'id', title: 'ID', canFilter: true }),
        ],
        filter() {
            return { isActive: true } as any;
        },
        minLookupCharacters: 1,
        placeholder: 'Select currency',
        width: 'small',
    })
    currency: ui.fields.Reference;
```

#### Table

[Example: Purchase Order](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/applications/xtrem-purchasing/lib/pages/purchase-order.ts)

```
    @ui.decorators.tableField<PurchaseOrder, PurchaseOrderLine>({
        bind: 'lines',
        canFilter: true,
        canSelect: false,
        pageSize: 10,
        displayMode: ui.fields.TableDisplayMode.compact,
        node: '@sage/xtrem-purchasing/PurchaseOrderLine',
        orderBy: {
            _sortValue: +1,
        },
        parent() {
            return this.linesBlock;
        },
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'Id',
                isHidden: true,
                isReadOnly: true,
            }),
```

#### Radio

[Example: Formula](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-technical-data/lib/pages/formula.ts)

```
    @ui.decorators.radioField<Formula>({
        title: 'Request approval for',
        isTransient: true,
        parent() {
            return this.requestApprovalBlock;
        },
        options: ['Pilot', 'Production'],
    })
    typeApprovalRequested: ui.fields.Radio;
```

#### Progress

[Example: Work Order](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/applications/xtrem-manufacturing/lib/pages/work-order.ts)

```
    @ui.decorators.progressField<WorkOrder>({
        parent() {
            return this.additionalInfoBlock;
        },
        title: 'Production',
        width: 'small',
    })
    productionCompletionPercentage: ui.fields.Progress;
```

#### Visual Process

[Example: Site](https://github.com/Sage-ERP-X3/xtrem/blob/master/services/shared/xtrem-master-data/lib/pages/site.ts)

```
    @ui.decorators.visualProcessField<Site>({
        parent() {
            return this.graphBlock;
        },
        title: 'Organization',
        isReadOnly: true,
    })
    hierarchyChartContent: ui.fields.VisualProcess;
```
