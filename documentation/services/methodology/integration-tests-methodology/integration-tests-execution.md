PATH: XTREEM/Integration+Tests/Integration+Tests+Execution

# How to execute cucumber integration tests

## How to execute cucumber integration tests on localhost

1.  Load the data from the layer test
    (only for xtrem/service or xtrem/platform. not necessary for xtrem/x3)

    ```ts
    pnpm run load:test:data
    ```

    Note:

    -   For xtrem/service, the command can be executed at root level (e.g. xtrem/service) or inside an applicative package (e.g. xtrem-sales).
    -   For xtrem/platform the command should be executed inside the package xtrem-show-case.

2.  Start the service

    ```ts
    pnpm run start
    ```

    Note:

    -   For xtrem/service, the command can be executed at root level (e.g. xtrem/service) or inside an applicative package (eg. xtrem-sales).
    -   For xtrem/x3, the command can be executed inside an applicative package (e.g. xtrem-x3-stock).
    -   For xtrem/platform, the command should be executed inside the package xtrem-show-case.

    If the pnpm run start succeeds, you should be able to browse the following url: `http://localhost:8240/@sage`

3.  Split the terminal in two and move into the applicative package.

4.  run the command

    ```ts
    pnpm run xtrem test %filter% --integration --browser
    ```

    -   **%filter%:** feature name or pattern to filter the integration tests to execute.
    -   **--browser:** to display the browser during the execution, otherwise the integration tests are executed in headless mode.

Example:

![integration-tests-execution-01](assets/images/integration-tests-execution-01.png)
