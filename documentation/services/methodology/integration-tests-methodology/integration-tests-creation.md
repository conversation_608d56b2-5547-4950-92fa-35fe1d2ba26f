PATH: XTREEM/Integration+Tests/Integration+Tests+Creation

# Where cucumber integration tests should be created?

1. Cucumber feature should be created on your feature branch.

    - Cucumber feature tests are delivered into master by doing a pull request of feature branch.

2. To know where to create the cucumber integration tests, please refer to the following page

    - Please refer to the following confluence page: [Tests-standards](https://confluence.sage.com/display/XTREEM/Tests+Standards/).

# Which codification should I use?

-   Please refer to the following confluence page: [Tests-standards](https://confluence.sage.com/display/XTREEM/Tests+Standards/).

# Which Step definition can I use?

-   The available step definition possible to use are listed in the following confluence page: [Available Cucumber steps](https://confluence.sage.com/display/XTREEM/Available+Cucumber+steps/).

-   List of the steps definition: [Step definition](https://confluence.sage.com/display/XTREEM/Automation+features+supported+by+Xtrem+Integration+tests/).

# Which data layer should I use?

-   For the integration tests, data from the "test" layer have to be used.
