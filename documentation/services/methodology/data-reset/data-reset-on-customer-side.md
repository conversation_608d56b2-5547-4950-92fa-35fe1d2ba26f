PATH: XTREEM/Nodes/Data+Reset+on+Customer+Side

# Data Reset on Customer Side

## Presentation of need

During the implementation process, the partner will configure the tenant provisioned with his own setup, master data and will also create transactions (sales, purchasing, stock, manufacturing transactions...) for test purpose.
Before going live it'll be necessary to be able to delete all transactions and keep all set up and master data (whatever their origin, Sage standard delivery or configured by the customer).
This includes deleting:

-   all records in transaction tables
-   resetting the sequence number value for some sequence number (depending on a flag on the sequence number)
-   resetting some fields in master data tables (the ones that contain data linked to transaction for eg avc of an item)

## Development Side

### Platform

-   A utility will be created to clean-up a tenant from tests done, keeping only the presetting.
-   A reset flag will be provided at node and property levels.
-   A reset event will be provided at the node level to manage special cases (ex. sequence numbers).

### Applicative Standards

Tables and Properties to reset have to be indicated in the US by POs, BPAs.

Structure the database with the following rules:

-   Table to reset
-   Properties to reset
    -   This must be done in a dedicated node extension containing only resettable properties.
    -   This extension must be stored in the same table to avoid performance issue.
