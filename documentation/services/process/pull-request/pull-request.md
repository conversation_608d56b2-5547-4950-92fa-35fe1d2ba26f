PATH: XTREEM/Process/Pull+Request

# Steps Before Pull Request

Your development is done, your tests are okay.
Now, what needs to be done before your Pull Request?
What is a Pull Request?

## What is a Pull Request?

As each one is developing in isolated environments through branches when your development is finished and qualified by your unit tests, you have to merge your development and tests in the master branch, as its name indicates, is the main and reference branch that contains the version of the product that will be delivered.

## Pull Request Scope

Please, respect the **scope** of the **user story** you are working on for your Pull Request.
This is very important to respect in order to facilitate the Pull Request review and the traceability of the updates of the master branch.

## Steps to be done before a Pull Request

### Goal

The goal of this process is to make verifications and fix errors if any. Thus,

-   Commands are to be launched from the root of your development branch.
-   For each step, verify the result of the operation, and **fix errors if any**.
-   When fixes are done, **stage, commit**, and **push**.

### Process

![Process](assets/drawio/pull-request-and-steps-before.drawio.png)

### Update your branch with the master branch

Firstly, as each one is developing in isolated environments through branches, you have to keep your branch up to date with the master branch of the product. This operation is to solve conflicts, if they are, in your development branch. Run the following command:

`git pull origin master`

### Install

To re-install all packages of the product, you have to be on the **xtrem repository** and enter:

`pnpm run clean:install`

### Build

To build the packages of services, you have to be on the **services repository** and enter:

`pnpm run build`

### Source code verification

Use the lint command to check automatically the source codes according to standards. We chose the ones used by [AirBnB](https://github.com/airbnb/javascript#translation).

`pnpm run lint`

### Running units tests

Run the following command, to check unit tests, and the code coverage for your package that has to reach 80% in any axis of analysis:

`pnpm run test:ci`

### loading test data

Load the test data in the xtrem-start database to be able to run the product in the following step with the test data up to date.

`pnpm run load:test:data`

### Manual tests

Verify your pages through this command:

`pnpm run start`

### loading demo data

Load the demo data in the xtrem-start database when

-   a CSV file is modified
-   a mandatory and not nullable property is added or updated,
-   a property is renamed

Thus, you will be able to check if CSV files are in concordance with schemas.

`pnpm run load:demo:data`

### update your branch on remote

If you had errors after one step or another, you have to fix, stage, and commit them.

The root package.json or pnpm-lock.yaml files may be modified if you had changed some package dependencies or added packages to the repository. If so, you must commit these files.

At the end, you have to push your branch to remote by running:

`git push`

## The Pull Request

When all those previous steps are ok, and all fixes are staged, committed, and pushed, you have to merge your development branch into the **master branch**. Thus,

1. Create **Pull Request** in **Github**, clicking the **"Compare &amp; pull request"** button available on your branch.

2. Follow the standards for the [title of the Pull Request](https://confluence.sage.com/display/XTREEM/Github+Standards), which is the same as the commit standards.

3. Follow the pattern description for your pull request.

4. Assign a team member for the development review,

5. Assign **Catherine GRIFFIN** and **Amélie Bresson** and add the **"Terminology review" label** for a terminology review when texts have been created or updated. [More details](https://confluence.sage.com/display/XTREEM/Name+and+Text).

6. The reviewer of the Pull Request checks the development according to the [checklist](https://confluence.sage.com/x/MIXMDw).

7. When the Pull Request is approved by the reviewer and is okay according to some automatic controls, the Pull Request has to be **squashed and merged**.

8. Finally, you can delete your development branch locally from VS code.
