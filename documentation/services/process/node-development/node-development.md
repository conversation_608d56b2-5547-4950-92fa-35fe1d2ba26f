PATH: XTREEM/Process/Node+Development

# Node Development Process

This page describes what needs to be done when a node has been developed.

![Process](assets/drawio/node-development.drawio.png)

## Building and generating Schema files

This is the step for the build and the generation of the schema files (or GraphQL API client) in the **data** folder of each package. Navigate to your package folder and run the following command:

`pnpm run build`

When the **build** is finished, there will be many changed files: this is because the **build** creates automatically the schema definition json files for all updated nodes, node-extensions under xtrem-{package}/**data/schema**.

-   You will have to fix errors if any.

When everything is ok, you have to **stage, commit** and **push** all the files re-generated by the build in each modified package.

![Building](assets/images/build.png)

## Lint Standards

The goal is to have a standard approach concerning the script writing in order to get quality and homogeneity of our development.

### Settings

In VS Code, remove the **tslint** plugin and install the **eslint** plugin.

### Development Methodology

Develop according these standards used by [AirBnB](https://github.com/airbnb/javascript#translation).

### Process

Use the lint command to check automatically the source codes of your package according to the standards before unit testing and a pull request.

`pnpm run lint`

Correct errors, if any, according to the standards.

## Test Data

-   Navigate to [this page](https://confluence.sage.com/x/mYvMDw) for more details as to how enter data.

## Running the units tests

To ensure a good quality of the package, you have to develop and run unit tests. Everyone must pay a special attention to the quality of one's development.

Steps are the followings:

-   Run your Mocha and Chai unit tests. [See more details](https://confluence.sage.com/display/XTREEM/Mocha+and+Chai).
-   Run your graphQl unit tests. [See more details](https://confluence.sage.com/display/XTREEM/GraphQL).
-   Run the unit tests of your package providing the code coverage: `pnpm run test:ci`. [See more details](https://confluence.sage.com/x/oHNqDg)

Note that those commands are available in each package:

-   **pnpm run test:unit** run the mocha tests
-   **pnpm run test** run the graphql & mocha tests
-   **pnpm run test:ci** run the graphql & mocha tests and calculate the code coverage.
-   **pnpm run test:graphql** run the graphql tests
-   **pnpm run test:smoke** run the cucumber tests. Only the ones that load the pages. No scenario de test.

Note that a development is qualified when:

-   All your unit tests are **ok**.
-   The package code coverage is equal or greater than 80%.

## Pull Request to Master Branch

When the development of the user story is finished and the unit tests are all ok and cover a large part of your code, you have to create a **Pull Request** in order to merge your development and tests in the master branch.

Address your pull resquest to a colleague developer of your team, and Catherine GRIFFIN and Amélie Bresson for the terminology review when there are text creations or modifications (base.json file in the i18n folder).
