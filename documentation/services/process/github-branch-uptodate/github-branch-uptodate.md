PATH: XTREEM/Process/Keep+your+local+Git+branch+up+to+date

# Keep your local branch up to date

![Process](assets/drawio/localbranch.drawio.png)

## Update the branch locally

As you work locally with VS code, you need to update your branch from the server.

If you need to update your master branch: `git pull`

If you need to update your feature branch directly: `git pull origin master`

## Install

To re-install all packages of the product, you have to be on the **xtrem repository** and enter:

`pnpm run clean:install`

## Build

To build the packages of services, you have to be on the **services repository** and enter:

`pnpm run build`

Note that you can build a dedicated package only, being positioned in that package.

Report to this page about [more options of the build command](https://github.com/Sage-ERP-X3/xtrem#readme).

The build run those operations among others:

-   Compile the \*.ts files to generate executable scripts in JavaScript.
-   Generate files such as :
    -   **\*.json** files in the **data/schema** folder of each package.
    -   **base.json** files containing texts to be translated afterwards, in the **lib/i18n** folder of each package.
