PATH: XTREEM/Process/Upgrade+And+Reset

# Introduction

The Upgrade and reset process makes it possible to create a new up-to-date schema which leverages the basic settings of an older schema: users, tenants and customers.

## Step 1: save Intacct settings

Intacct settings will be deleted by the reset (step 3). Be careful to save them before proceeding: these settings can be found in 'Intacct Configuration'. If you choose to backup the intacct table you'll have to mind about the possible changes on this table: the layer column, for instance, is no more present in version 12 of the schema.

## Step 2: upgrade the schema

The **upgrade** command has to be executed from /services/main/xtrem-services-main with options run and prod:

-   --run : upgrade all tables of the current application.
-   --prod : plays previously recorded SQL files (does not execute any upgrade action)

```bash
cd /services/main/xtrem-services-main
pnpm run xtrem upgrade --run --prod
```

## Step 3: reset the schema

By default the schema:reset command reloads the setup layer. To reload layers setup and demo the layers options has to be used like in the following example:

```bash
pnpm run schema:reset --layers setup,demo
```

## Step 4: restore Intacct settings

Restore the intacct settings saves at step 1. You can test them using the button 'synchronous test'.
