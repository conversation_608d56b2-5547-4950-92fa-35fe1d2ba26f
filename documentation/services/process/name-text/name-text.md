PATH: XTREEM/Process/Name+and+Text

# Names and Texts Process

Process to assign the right name and text to a property.

The goal is to be consistent in the whole product.

![Process](assets/drawio/name-text.drawio.png)

## Step 1

The PO provides the property names and texts for a node and pages according this form.

He can ask the translation team for advice on terminology through this Teams channel, if needed.

## Step 2

The developer continues the analysis and will complete this form according to additions or changes.

He can ask the translation team for advice on terminology through this Teams channel, if needed.

## Step 3

Before starting the development the PO or developer asks for a global review of names and texts through this Teams channel.

## Step 4

At this stage, all names and texts are generated in the **base.json** file located in the **i18n** folder of each package. Thus, this is the file to review.

Here are the actions to be carried out successively by developers and translation team at the time of the Pull Request, when texts have been created or updated.

### A. Developer action

-   Assign <PERSON> and <PERSON><PERSON><PERSON> and add the **"Terminology review"** label for a global review of names and texts before merging your development into the master branch. A notification will be send to them.

### B. Translation team

-   Open your notification, and click on the link to access the Pull Request.
-   Go to the **"Files changed"** tab and select the **base.json** file.
-   Propose suggestions when needed through the **« Insert a suggestion »** to facilitate their integration afterwards, click « **Review changes** » and select the « **Request changes** » option.
-   When the review is finished, add the **"Terminology validated"** label on the Pull Request.

### C. Developer actions

-   Apply the suggestions on the **base.json** file.
-   Modify the **source files** accordingly.
-   Adapt your unit tests when needed.
-   The reviewer approves the Pull Request, when everything is ok and when the **"Terminology validated"** label is present.

Example: texts in the xtrem-technical-data package

![Image 1](assets/images/name-and-text1.png)

# Resources: standards, guidelines, glossary

Please, help yourselves with those standards, guidelines and glossary:

-   Regarding namings, please follow the [standards](https://confluence.sage.com/x/40ZqDg).
-   For any text creation, follow the [writing guidelines](https://confluence.sage.com/x/ayeUE).
-   Regarding vocabulary, be compliant with the [glossary](https://glossary.qa-sagextrem.com).
-   To get access to the glossary [Glossary access request](https://forms.office.com/r/PvUFG58zmW)

# Template

## Class

| name |
| ---- |
|      |

| name | type | maxLength | isNullable |
| ---- | ---- | --------- | ---------- |
|      |      |           |            |

## Node

| name | package | kind | published | CRUD | indexes | unique |
| ---- | ------- | ---- | --------- | ---- | ------- | ------ |
|      |         |      |           |      |         |        |

## Page

| name | text | category | listItem title | dropDownMenu title |
| ---- | ---- | -------- | -------------- | ------------------ |
|      |      |          |                |                    |

### Tabs / Sections / Blocks / Actions

| tab | section title | block title | action title |
| --- | ------------- | ----------- | ------------ |
|     |               |             |              |

### Columns

| name | text | notEmpty | defaultValue | node | data type | control | display | hidden |
| ---- | ---- | -------- | ------------ | ---- | --------- | ------- | ------- | ------ |
|      |      |          |              |      |           |         |         |        |

### Enums

| enum | text |
| ---- | ---- |
|      |      |

### Messages

| context | text message |
| ------- | ------------ |
|         |              |

# Translation

The translation will be done by the dedicated team, afterwards.

This will be produced through a file per language in the **i18n** folder of each package.

The file naming is with this pattern: **xx-yy.json** where xx=language and yy=country

Example: files in the xtrem-technical-data package

![Image 2](assets/images/name-and-text2.png)
