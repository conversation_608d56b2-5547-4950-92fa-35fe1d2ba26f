PATH: XTREEM/Process/Integration+Tests/Run+integration+tests

# How to run integration tests locally

## Authentication container

Start an authentication container:

```bash
docker run --rm -d -e CLIENT_ID=FOq1MBQM4VBWutTk2PQES7RoVN6lYMLm \
	-e CLIENT_SECRET=0cZboJUzFr_dz72QeRhSikQ2HFFOY9PaRTuue69Y4tBNO2rx8pR14Z_V9Hxad3rB \
	-e ACCESS_TOKEN_LIFETIME=15  -e CUSTOMER_CLIENT_ID=xx -e CUSTOMER_CLIENT_SECRET=xx \
	-e LOCAL_DEV=true \
	-e XTREM_PORT=8240 \
	-e MULTI_LOCAL_DEV=true \
	-e LOCAL_TENANTS="777777777777777777777" \
	-e BASE_URL=http://connect.localhost.dev-sagextrem.com:8080 \
	-e KNOWN_DOMAINS="*.dev-sagextrem.com" \
	-e AWS_REGION=eu-west-1 \
	-e LOCAL_SESSION=true \
	-p "8080:8080" \
	--name xtrem_auth \
	ghcr.io/sage-erp-x3/xtrem-authentication-service:latest
```

This command is executed in the script https://github.com/Sage-ERP-X3/huracan/blob/master/pipelines/templates/tests/integration-tests.yml

## Configuration files

Copy the file https://github.com/Sage-ERP-X3/xtrem/blob/master/xtrem-security-azure-prod-ui.yml to your root folder and rename it to `xtrem-security.yml`

Make sure you have

```yml
prodUi: true
```

in your xtrem-config.yml file.

## Windows network redirection

On windows, edit your file `C:\Windows\System32\drivers\etc\hosts` and redirect both `connect.localhost.dev-sagextrem.com` and `xtrem.localhost.dev-sagextrem.com` to this IP address:

```text
AA.BB.CC.DD connect.localhost.dev-sagextrem.com
AA.BB.CC.DD xtrem.localhost.dev-sagextrem.com
```

where AA.BB.CC.DD stands for your WSL IP address.

To get it, run in a WSL console the following command:

```bash
ifconfig eth0 | grep -oP '(?<=inet\s)\d+(\.\d+){3}'
```

**WARNING**
In WSL, edit your /etc/wsl.config file and make sure it contains these 2 lines (otherwise your etc/hosts file will be automatically regenerated, based on the previous version of your old hosts files - from Windows)

```
[network]
generateHosts = false
```

Make sure your /etc/hosts file is OK and does not contain any redirection for `connect.localhost.dev-sagextrem.com` and/or `xtrem.localhost.dev-sagextrem.com`.

## Initialization of the application

Go to the folder of the application you want to test (show-case, show-case-bundle, ...) and initialize your application:

```bash
pnpm run xtrem schema --create --reset-schema
```

Then load the test data:

```bash
pnpm run load:test:data
```

Export some configuration variables:

```bash
export TARGET_URL=http://sdmo-xtrem.localhost.dev-sagextrem.com:8240
export loginUserName=<EMAIL>
export loginPassword=asdf1234
export XTREM_CUCUMBER_DEBUG=1
export timeout=60000
```

Note: you won't be able to run classic unit-tests if the TARGET_URL is set.

## Platform - 1 (A - N)

From the root folder:

```bash
# reset the schema
cd platform/show-case/xtrem-show-case && pnpm run xtrem schema --create --force && cd -
# make sure the test will not be skipped by a previous successful execution
rm /tmp/xtrem-cache-xtrem-@sage-xtrem-*
# set some configuration environment variables
export TARGET_URL=http://sdmo-xtrem.localhost.dev-sagextrem.com:8240
export loginUserName=<EMAIL>
export loginPassword=asdf1234
export XTREM_CUCUMBER_DEBUG=1
export timeout=60000
export XTREM_OFFLINE=1 # make sure we won't get any cached result from S3 and execute the test
# start the integration tests
export PATTERN='test/cucumber/[a-n]*.feature'
export XTREM_SCOPES='platform/show-case/xtrem-show-case$'
pnpm run test:ci:integration
```

## Platform - 2 (P - X)

From the root folder:

```bash
# reset the schema
cd platform/show-case/xtrem-show-case && pnpm run xtrem schema --create --force && cd -
# make sure the test will not be skipped by a previous successful execution
rm /tmp/xtrem-cache-xtrem-@sage-xtrem-*
# set some configuration environment variables
export TARGET_URL=http://sdmo-xtrem.localhost.dev-sagextrem.com:8240
export loginUserName=<EMAIL>
export loginPassword=asdf1234
export XTREM_CUCUMBER_DEBUG=1
export timeout=60000
export XTREM_OFFLINE=1 # make sure we won't get any cached result from S3 and execute the test
# start the integration tests
export PATTERN='test/cucumber/[p-x]*.feature'
export SERVICE_OPTIONS='showCaseWorkInProgressOption,showCaseDiscountOption'
export XTREM_SCOPES='platform/show-case/xtrem-show-case$'
pnpm run test:ci:integration
```

## Platform - 3 (Visual Regression)

From the root folder:

```bash
# reset the schema
cd platform/show-case/xtrem-show-case && pnpm run xtrem schema --create --force && cd -
# make sure the test will not be skipped by a previous successful execution
rm /tmp/xtrem-cache-xtrem-@sage-xtrem-*
# set some configuration environment variables
export TARGET_URL=http://sdmo-xtrem.localhost.dev-sagextrem.com:8240
export loginUserName=<EMAIL>
export loginPassword=asdf1234
export XTREM_CUCUMBER_DEBUG=1
export timeout=60000
export XTREM_OFFLINE=1 # make sure we won't get any cached result from S3 and execute the test
# start the integration tests
export PATTERN='test/cucumber/z-visual-regression.feature'
export XTREM_SCOPES='platform/show-case/xtrem-show-case$'
pnpm run test:ci:integration
```

## Platform - 4 (Extensions)

From the root folder:

```bash
# reset the schema
cd platform/show-case/xtrem-show-case && pnpm run xtrem schema --create --force && cd -
# make sure the test will not be skipped by a previous successful execution
rm /tmp/xtrem-cache-xtrem-@sage-xtrem-*
# set some configuration environment variables
export TARGET_URL=http://sdmo-xtrem.localhost.dev-sagextrem.com:8240
export loginUserName=<EMAIL>
export loginPassword=asdf1234
export XTREM_CUCUMBER_DEBUG=1
export timeout=60000
export XTREM_OFFLINE=1 # make sure we won't get any cached result from S3 and execute the test
# start the integration tests
export PATTERN=''
export XTREM_SCOPES='platform/show-case/xtrem-show-case-bundle$'
pnpm run test:ci:integration
```

## Platform - 5 (Prod mode)

From the root folder:

```bash
# reset the schema
cd platform/show-case/xtrem-show-case && pnpm run xtrem schema --create --force && cd -
# make sure the test will not be skipped by a previous successful execution
rm /tmp/xtrem-cache-xtrem-@sage-xtrem-*
# set some configuration environment variables
export TARGET_URL=http://sdmo-xtrem.localhost.dev-sagextrem.com:8240
export loginUserName=<EMAIL>
export loginPassword=asdf1234
export XTREM_CUCUMBER_DEBUG=1
export timeout=60000
export XTREM_OFFLINE=1 # make sure we won't get any cached result from S3 and execute the test
# start the integration tests
export PATTERN='test/cucumber/z-prod-smoke-test.feature'
export XTREM_SCOPES='platform/show-case/xtrem-show-case$'
export PROD_MODE=true
pnpm run test:ci:integration
```

## Services - 1 (Static page load)

From the root folder:

```bash
# reset the schema
cd platform/show-case/xtrem-show-case && pnpm run xtrem schema --create --force && cd -
# make sure the test will not be skipped by a previous successful execution
rm /tmp/xtrem-cache-xtrem-@sage-xtrem-*
# set some configuration environment variables
export TARGET_URL=http://sdmo-xtrem.localhost.dev-sagextrem.com:8240
export loginUserName=<EMAIL>
export loginPassword=asdf1234
export XTREM_CUCUMBER_DEBUG=1
export timeout=60000
export XTREM_OFFLINE=1 # make sure we won't get any cached result from S3 and execute the test
# start the integration tests
export PATTERN='smoke-test-static.feature'
export XTREM_SCOPES='services/'
pnpm run test:ci:integration
```

## Services - 2 (Pages with data)

From the root folder:

```bash
# reset the schema
cd platform/show-case/xtrem-show-case && pnpm run xtrem schema --create --force && cd -
# make sure the test will not be skipped by a previous successful execution
rm /tmp/xtrem-cache-xtrem-@sage-xtrem-*
# set some configuration environment variables
export TARGET_URL=http://sdmo-xtrem.localhost.dev-sagextrem.com:8240
export loginUserName=<EMAIL>
export loginPassword=asdf1234
export XTREM_CUCUMBER_DEBUG=1
export timeout=60000
export XTREM_OFFLINE=1 # make sure we won't get any cached result from S3 and execute the test
# start the integration tests
export PATTERN='smoke-test-data.feature'
export XTREM_SCOPES='services/'
pnpm run test:ci:integration
```
