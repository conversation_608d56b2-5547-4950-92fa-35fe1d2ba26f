PATH: XTREEM/Prerequisites/Product+Structure

# Product Structure

## GitHub main structure

### Introduction

You will have the same structure **locally** as in remote in **github**.

However, there are few exceptions managed in the **.gitignore** and **.npmignore** files to prevent some files of your packages to be tracked in github.

### Repository

There is one repository, named **xtrem** to manage the project.

The repository contains technical folders:

-   docker: To create the Xtrem docker image (for Jenkins unit testing etc.)
-   logs: Contains logs as its name suggests. This folder is not tracked.
-   pipelines: This is for Azure Cloud Deployment (image creation for cloud provisioning)
-   scripts: Contains technical files used by services.
-   .github: For technical purpose. (code owner, commit settings)
-   .husky: For technical purpose. (lint commit messages, run tests, lint code) ???
-   .vscode: For technical purpose. (settings of VScode for example)

The repository contains the development and documentation organized in those folders:

-   **platform** for the framework,
-   **services** for the application,
-   **tools** including the glossary,
-   **x3** for the ADC terminals, the finance APIs
-   **documentation** including the platform documentation and the methodology.

The repository contains major files such as:

-   **.gitignore**: list of elements not to track
-   **.nvmrc**
-   **.prettierrc**: prettier configuration file
-   **commit-validation-hook.js**: Used in the commit message validation
-   **commitlint.config.js**: Used in the commit message validation
-   **lerna.json**: lerna config file (tool for managing JavaScript projects with multiple packages)
-   **package.json**: It contains the development environment management with for example:
    -   Version
    -   Devdependencies: dependencies at development level (Alasql, Mocha, Chai, Rimraf for the build, TypeScript ...)
    -   Scripts: all scripts available in this repository
-   **pull_request_template.md**
-   **README.md**: The content of this file shows on the repository in GitHub
-   **xtrem-config.yml**: Database configuration, mail server configuration. Define which DB server to use when running unit tests locally or when running a graphiql locally (using pnpm run start)

![repository](assets/images/product-structure1.PNG)

#### vscode folder

The .vscode folder is composed of several files such as:

-   **extensions.json**: extension configurations
-   **launch.json**: for debugging
-   **settings.json**: contains editor parameters
-   **tasks.json**: to run debugger

#### platform folder

The platform folder is composed of several sub-folders:

-   **back-end** for the server,
-   **front-end** for the client,
-   **cli** for the command-line interface
-   **system** including authorization, communication, mailer, reporting, package activation, service option, user, bundle installation.
-   **show-case** for function demoing the front-end possibilities.

![platform](assets/images/product-structure2.png)

Each sub-folders contains their own packages.

The platform folder is composed of some major packages:

-   **xtrem-core**: Server framework. APIs are usable in em-core. Navigate to the [Server Framework Reference Documentation](https://confluence.sage.com/x/vLIdDg).
-   **xtrem-ui**: Client Framework. This is to manage the UI. Navigate to the [Client Framework Reference Documentation](https://confluence.sage.com/x/MLQdDg).
-   **xtrem-client**: client library for X3 GraphQl APIs.
-   **xtrem-graphiql**: API to launch requests. Navigate to the [GraphQL API Reference Documentation](https://confluence.sage.com/x/ibMdDg).
-   **xtrem-cli**: Command-line interface to compile, init, start, test packages. Navigate to the [Xtrem CLI Reference Documentation](https://confluence.sage.com/x/M7IdDg).

#### services folder

The services folder is composed of several sub-folders:

-   **adapters** for the interface with intacct
-   **application** for the manufacturing, purchasing and sales.
-   **shared** for the the inventory, master-data, structure and technical-data.
-   **functional-tests** for the automated tests.
-   **bundles** for developments external to Sage.
-   **upgrade-bundles** for the upgrade of developments external to Sage.

![services](assets/images/product-structure3.png)

Each sub-folders contains their own packages.

The services folder is composed of several files such as:

-   **\*.eslintrc-base.js**: contains the eslint linter options
-   **package.json**: It contains the development environment management
-   **README.md**: The content of this file shows on the repository in GitHub
-   **tsconfig-artifacts-base.json**
-   **tsconfig-base.json**: TypeScript compiler configuration

### Packages

**What's a package?**
A package can be defined as a grouping of related types (nodes, classes, interfaces, enums...) providing access protection and namespace management.

A package is a set of files in the pnpm approach and organized around functional themes.

A package name is uniq in the product and start with '**xtrem-**'.

The product is made of a set of packages managed through the **package.json** file.

Packages are always structured in the same way and consist of **files** and **sub-folders**.

Each package has its own version number.

Find out more on the [Package Methodology](https://confluence.sage.com/display/XTREEM/Package+Methodology).

#### Files in a package

In each package, we have an identical list of files, such as:

-   **.eslintrc-base.js**: contains the eslint linter options
-   **.eslintrc.js**
-   **.npmignore**
-   **index.ts**: to give access to the package
-   **package.json**: mandatory in each package. It contains information of the package such as the scripts to manage the package, dependencies.
-   **README.md**: The content of this file will show in the TypeDoc generated documentation
-   **tsconfig-artifacts.json**: extension of the one of xtrem-services. Mandatory.
-   **tsconfig.json**: extension of the one of xtrem-services - is meant to be removed as eslint now replaces tslint
-   **typedoc.json**: TypeDoc configuration file

#### Folders in a package

In each package, we have an identical **structure** of folder.

-   **api**: Publication of interfaces using Graphql APIs with nodes. APIs are built automatically to communicate with other products but also with Xtrem pages or Xtrem mobile apps
-   **data**: Architecture of the database and data for unit tests
-   **lib**: Folder where is located the source code of the package
-   **test**: Unit test scripts

![package](assets/images/product-structure4.png)

In the **lib** folder, we have an identical structure of folders for each package:

-   **activities**: authorizations on nodes.
-   **classes**: typescript classes not automatically created (as opposed to the ones from nodes that extends the node technical class).
-   **data-types**: common structure of data.
-   **enums**: predefined lists.
-   **events**: node events.
-   **functions**: functions usable in server side.
-   **client-functions**: functions usable in client side.
-   **shared-functions**: functions usable in server side and client side.
-   **i18n**: translation files containing all languages managed for Xtrem.
-   **interfaces**: interfaces developed in TypeScript.
-   **menu-items**: menu and sub-menus of the product.
-   **nodes**: business object (node) written in TypeScript that will be transformed in JavaScript during the build. Each node contains properties, services and methods.
-   **node-extensions**: add new functionality to a node in a dependency package.
-   **pages**: pages developed in TypeScript. The page accesses a node with published properties through a GraphQL request.
-   **page-extensions**: add new functionality to a page in a dependency package.
-   **queues**: communication with an external product as intacct.
-   **service-options**: options.
-   **upgrades**: scripts providing upgrades of the database, for each release.

![lib](assets/images/product-structure5.png)

In the **data** folder, we have an identical structure of folders for each package:

-   **schema**: it contains the architecture of the database through generated json files per tables description (automatically created during build process).
-   **layers**: values for nodes.
    -   demo: data for demos.
    -   qa: data for the functional cucumber tests.
    -   setup: data for a first customer installation.
    -   test: data for the mocha and graphql tests.
-   **extension-layers**: values for nodes-extensions.
    These two folders contain csv files used for tests or demos.

![data](assets/images/product-structure6.png)

In the **test** folder, we have an identical structure of folders for each package:

Scripts of tests written with mocha/chaï in ts files.

-   **graphql**: contains one folder named in kebab-case per request-response that contains:
    -   the request.graphql file
    -   the response.json file
    -   the parameters.json file if parameters are needed in input, output, for envconfigs or for layers. In this case the request and response files are named request.graphql.hbs and response.json.hbs
-   **mocha**: is used for functions, nodes when it cannot be tested through GraphQL
    -   functions - test for functions
    -   nodes - test for nodes
    -   classes - test for classes
-   **fixtures**: input and output data for mocha tests.

![test](assets/images/product-structure6.png)
