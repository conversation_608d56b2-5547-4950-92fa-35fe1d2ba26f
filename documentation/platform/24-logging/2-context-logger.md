PATH: XTREEM/24+Logging/2+Context+logger

The framework provides a logger as an attribute of the context. See [xtrem-log](https://github.com/Sage-ERP-X3/xtrem/tree/master/platform/back-end/xtrem-log) documentation.

```ts
...
context.logger.info('my info');
context.logger.warn('my warning');
context.logger.error('my error');
context.logger.debug(() => 'my debug');
context.logger.verbose(() => 'my verbose');
...
```

# Config

The domain will be `{main package name}/context`.

## Example:

```yml
logs:
  domains:
    sage/xtrem-intacct-gateway/context:
      level: verbose
    ...
```
