PATH: XTREEM/24+Logging/1-General-Structure-Of-Logs

The framework provides a logging feature configurable by domain and level. See [xtrem-log](https://github.com/Sage-ERP-X3/xtrem/tree/master/platform/back-end/xtrem-log) documentation.

## Configuration

The configuration is done in the `xtrem-config.yml` file using the `logs` config parameter, example:

```yml
logs:
  options:
    json: false
    noColor: false
  domains:
    sage/xtrem-service/http:
      level: verbose
    sage/xtrem-core/sql:
      level: verbose
    ...
```

## Log Formats

Based on the `deploymentMode` config parameter, we have 2 log formats, one for development and one for production.

When in development mode, it prints the log with the following attributes (except if you set `logs.options.json` to `true`):

```
tenantId(last 5 digits) | processPid | eventId  | time | logLevel | domain | message
```

When in production mode, it prints the log as a JSON object with additional info such as userId and originId.

Example production:

```
{"tenantId": "777777777777777777777","userId":6,"originId":"container:xyz-20496-54","eventId":"000013","datetime":"10:58:35.390","logLevel":"INFO","domain":"xtrem-config/config","message":"config.serviceOptions.level was set to 'released'"}
{"tenantId":"777777777777777777777","userId":2,"originId":"container:xyz-20496-54","eventId":"000063","datetime":"11:03:18.535","logLevel":"ERROR","domain":"xtrem-core/graphql","errorHint":"POST /api{/*path} > mutation > xtremCore > site > create","source":"internal","message":"graphql request failed: Cannot query field \"site\" on type \"xtremCoreMutation\".\n\nGraphQL request:1:24\n1 | mutation { xtremCore { site { create( data: { is: \"1\", mok: \"lex\" }) { id } } } }\n  |                        ^"}
```

In development mode, the text can be colorized if process.stdout.isTTY is true.

## Error hints

In case of errors, an error hint is added to the log to provide some contextual details.

In production mode a property errorHint is added to the JSON payload if the error occurs in the context of an HTTP request, this hint includes:

- a marker `[graphiql]` if the request has been initiated by the GraphQL explorer
- the HTTP verb
- the express route
- a breadcrumbs of the GraphQL query

In development mode the error hint is prepended to the message.
