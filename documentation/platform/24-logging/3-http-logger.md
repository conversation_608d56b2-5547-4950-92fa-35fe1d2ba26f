PATH: XTREEM/24+Logging/3+HTTP+logger

The HTTP logger allows you to control the level of details in HTTP logs.

```yml
logs:
  domains:
    sage/xtrem-service/http:
      level: verbose
    ...
```

## Info level (default)

At `info` level each HTTP request produces 2 log entries:

-   a request entry with the request method and its URL.
-   a response entry with the response status, the elapsed time between request and response and the request URL.

Each request is assigned an id from a counter.
This id is included in the response so that you can correlate the response with its request.

Example:

```
----- | 51401 | 000072 | 13:09:54.597 | INFO    | xtrem-service/http        | HTTP request  5 POST /api
----- | 51401 | 000073 | 13:09:54.637 | INFO    | xtrem-service/http        | HTTP request  6 POST /metadata
----- | 51401 | 000074 | 13:09:54.640 | INFO    | xtrem-service/http        | HTTP request  7 POST /api
----- | 51401 | 000076 | 13:09:54.835 | INFO    | xtrem-service/http        | HTTP response 7 200  195ms /api
----- | 51401 | 000077 | 13:09:54.945 | INFO    | xtrem-service/http        | HTTP response 5 200  348ms /api
----- | 51401 | 000078 | 13:09:54.980 | INFO    | xtrem-service/http        | HTTP request  8 POST /api
----- | 51401 | 000079 | 13:09:55.009 | INFO    | xtrem-service/http        | HTTP response 8 200  29ms /api
```

Notice the fact that request 5 ended after request 7, and request 6 even later.

## Verbose level

The `verbose` level adds two entries to each request:

-   a `body` entry with the request body
-   an `end` entry one with the response body

These entries are truncated if necessary, to fit on a single line.

The log will also contain `chunk` entries between `body` and `end` if the response is sent in multiple chunks.

Example:

```
----- | 51671 | 000093 | 13:14:10.027 | INFO    | xtrem-service/http        | HTTP request  8 POST /api
----- | 51671 | 000094 | 13:14:10.028 | VERBOSE | xtrem-service/http        | HTTP request  8 body: {"query":"query {\n    xtremMasterData {\n        businessEntity {\n            ... (len=935)
----- | 51671 | 000095 | 13:14:10.063 | VERBOSE | xtrem-service/http        | HTTP response 8 end: {"data":{"xtremMasterData":{"businessEntity":{"query":{"edges":[],"pageInfo":{"h... (len=216)
----- | 51671 | 000096 | 13:14:10.063 | INFO    | xtrem-service/http        | HTTP response 8 200  36ms /api
----- | 51671 | 000097 | 13:14:10.072 | INFO    | xtrem-service/http        | HTTP request  9 POST /api
----- | 51671 | 000098 | 13:14:10.073 | VERBOSE | xtrem-service/http        | HTTP request  9 body: {"query":"query {\n    xtremStructure {\n        country {\n            query(fi... (len=547)
----- | 51671 | 000099 | 13:14:10.103 | VERBOSE | xtrem-service/http        | HTTP response 9 end: {"data":{"xtremStructure":{"country":{"query":{"edges":[{"node":{"_id":"6","name... (len=761)
----- | 51671 | 000100 | 13:14:10.103 | INFO    | xtrem-service/http        | HTTP response 9 200  31ms /api
```

## Debug level

The `debug` level generates more entries. The bodies are written in extenso. The request and response headers are logged too.

Example:

```
----- | 52198 | 000112 | 13:20:35.026 | INFO    | xtrem-service/http        | HTTP request  9 POST /api
----- | 52198 | 000113 | 13:20:35.028 | DEBUG   | xtrem-service/http        | HTTP request  9 headers: {"accept-language":"fr,fr-FR;q=0.8,en-US;q=0.5,en;q=0.3","accept":"application/json","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0","accept-encoding":"gzip, deflate","referer":"http://xtrem.localhost.dev-sagextrem.com:8240/login_service","origin":"http://xtrem.localhost.dev-sagextrem.com:8240","host":"xtrem.localhost.dev-sagextrem.com:8240","content-type":"application/json","if-none-match":"W/\"d8-WWUtoxLaMEfOA+e4+8EJW9ya1jY\"","content-length":"547"}
----- | 52198 | 000114 | 13:20:35.028 | DEBUG   | xtrem-service/http        | HTTP request  9 body:
query: query {
    xtremStructure {
        country {
            query(first: 16, orderBy: "{ name: 1 }") {
                edges {
                    node {
                        _id
                        name
                        id
                    }
                    cursor
                }
                pageInfo {
                    startCursor
                    endCursor
                    hasPreviousPage
                    hasNextPage
                }
            }
        }
    }
}

----- | 52198 | 000115 | 13:20:35.057 | DEBUG   | xtrem-service/http        | HTTP response 9 end: {"data":{"xtremStructure":{"country":{"query":{"edges":[{"node":{"_id":"6","name":"Australia","id":"AU"},"cursor":"[\"Australia\",6]#93"},{"node":{"_id":"3","name":"Canada","id":"CA"},"cursor":"[\"Canada\",3]#16"},{"node":{"_id":"4","name":"France","id":"FR"},"cursor":"[\"France\",4]#42"},{"node":{"_id":"5","name":"South Africa","id":"ZA"},"cursor":"[\"South Africa\",5]#81"},{"node":{"_id":"2","name":"United Kingdom","id":"GB"},"cursor":"[\"United Kingdom\",2]#83"},{"node":{"_id":"1","name":"United States of America","id":"US"},"cursor":"[\"United States of America\",1]#76"}],"pageInfo":{"startCursor":"[\"Australia\",6]#93","endCursor":"[\"United States of America\",1]#76","hasPreviousPage":false,"hasNextPage":false}}}}},"extensions":{"diagnoses":[]}}
----- | 52198 | 000116 | 13:20:35.057 | DEBUG   | xtrem-service/http        | HTTP response 9 headers: {"x-dns-prefetch-control":"off","expect-ct":"max-age=0","x-frame-options":"SAMEORIGIN","x-download-options":"noopen","x-content-type-options":"nosniff","x-permitted-cross-domain-policies":"none","referrer-policy":"no-referrer","x-xss-protection":"0","x-powered-by":"Express","content-type":"application/json; charset=utf-8","content-length":"761","etag":"W/\"2f9-R0Oi5q5B3g0rr4+sAGZdvnGSq3w\""}
----- | 52198 | 000117 | 13:20:35.057 | INFO    | xtrem-service/http        | HTTP response 9 200  31ms /api
```

**Important**: The debug level is only available in development mode.
The service will log an error and exit if you try to set it in production.

## Gotchas

Performance is impacted when enabling verbose or debug logging.

This is not only due to the volume of logs generated. It also impacts the size of the HTTP responses because
compression is turned off in verbose and debug levels.

Compression is enabled or disabled when the service starts and it is not modified when the xtrem-config.yml file
is dynamically reloaded. So you may get garbled logs (binary) if you change the level while the service is running.
