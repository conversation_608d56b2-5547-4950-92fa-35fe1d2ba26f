PATH: XTREEM/21+Useful+CLI+commands/2+Compile

# Compile

The `compile` command compiles the source code into an executable codebase.

Aliases: `c`, `build`, `prepare`

## Compilation steps

The compilation of an Xtrem package consists of the following steps:

1. **Cleaning the build folder and other build results:** This step removes the `build`, `api`, `coverage` and `data/schema` folders given that these contain generated files which are produced during the compilation process.
2. **Transpiling server side artifacts:** Converts TypeScript source files of server side artifacts (nodes, enums, data types...) into executable JavaScript files. During the compilation process, the localizable strings are extracted to the `lib/i18n` folder and core JavaScript mathematical operators are replaced by [decimal.js](https://github.com/MikeMcl/decimal.js) operations to ensure precise calculation results.
3. **Creating single TypeScript declaration file:** Creates a single `.d.ts` file for the project that describes all externally available classes and functions. It makes easier integration with other Xtrem packages and TypeScript projects. The `.d.ts` file will be located in the `build` folder and be named after the package name. The Xtrem CLI uses the [dts-generator](https://github.com/SitePen/dts-generator) library to create this file.
4. **Generating an API package:** Creates a new NPM package that consists of only TypeScript interfaces that describe the packages **external GraphQL API** based on the node structure. This package can be later referred by other projects that depend on the exposed GraphQL API and can generate type-safe API requests. This package is created to the `api` folder.
5. **Generating table schema:** Based on the server-side artifacts, the compiler generates a set of JSON files that describes the database schema that is needed by to run the package. These files can be used to populate an empty database with all required tables. These JSON files are placed to the `data/schema` folder.
6. **Client compilation:** This step transpiles and compiles all client-side artifacts using [Webpack](https://webpack.js.org/). The Xtrem CLI creates micro bundles for every single page to optimize page loading time. Similarly to the server-side compilation step, the localizable strings are extracted to the `lib/i18n` folder and core JavaScript mathematical operators are replaced by [decimal.js](https://github.com/MikeMcl/decimal.js) operations to ensure precise calculation results.

## Usage

```ts
xtrem compile [--skip-client] [--skip-server] [--skip-dts] [--skip-clean] [--skip-api-client] [--instrumented] [--binary] [--references]
```

-   **--skip-clean:** Skips deleting existing build output files, which might results an ambiguous build result. (Build step 1)
-   **--skip-server:** Skips compiling the server side artifacts. (Build step 2)
-   **--skip-dts:** Skips the generations of a single-file TypeScript declaration file.
-   **--skip-api-client:** Skips the generation of the GraphQL client API package.
-   **--skip-client:** Skips the compilation of client-side artifacts.
-   **--instrumented:** Enables the collection of code coverage data, it instruments the code after compilation using [istanbul.js](https://istanbul.js.org/).
-   **--binary:**: Encodes the **server-side** artifacts into binary format, which prevents reverse-engineering of the `build` folder. In addition to that, it also removes all map files from the `build` folder.
-   **--references:**: Builds the package using local references.

## Notes

-   Client-side artifacts (pages and stickers) **cannot** be compiled into a binary format.
-   Xtrem packages **must** be built using the Xtrem CLI's `compile` command.
