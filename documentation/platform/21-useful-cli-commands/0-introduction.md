PATH: XTREEM/Server+Framework+Documentation/21+Useful+CLI+commands

# Useful CLI commands

In this section, we will describe the main Xtrem CLI commands used when developing a package.

## Xtrem CLI: a command line based application

Xtrem CLI is a command line based application that can be used to quickly and easily develop Xtrem applications.
It can be used throughout the development life cycle as it supports, amongst other things:

-   package bootstrapping,
-   text execution,
-   compilation,
-   and application deployment.

## Global / Local installation

The Xtrem CLI is a mandatory dependency of all applicative packages, which means that all applicative packages should include the Xtrem CLI package (`@sage/xtrem-cli`) in their `package.json` file's dependency list.

When running `pnpm i` at the root of your package or application, you will thus get a local installation of Xtrem CLI.

Xtrem CLI is also available via NPM and can be installed globally via `pnpm i -g @sage/xtrem-cli`, **but you should avoid it and prefer a local installation**.
A global installation may fail to identify all dependencies of the package, which may lead to unexpected side effects.

Whenever the `xtrem` command is executed, the application first looks for a local installation of the Xtrem CLI.
If found, the command is executed by the local version.
When a local version is not available for any reason, the command is executed by the global installation, and a warning message is displayed.
