PATH: XTREEM/21+Useful+CLI+commands/10+pg-anonymizer

# PG-Anonymizer

PostgreSQL Anonymizer is an extension to mask or replace personally identifiable information (PII) or commercially sensitive data from a PostgreSQL database.

There are various ways to put the extension in action, you can find more detail [here](https://postgresql-anonymizer.readthedocs.io/en/stable/). In xtrem development, we opt for the "Black box" mode approach to anonymize our database.

For security purpose, we rebuild an image for each postgres major version and tag it e.g. ghcr.io/sage-erp-x3/pg-anon:13

Basically, we need a database dump in [plain format](https://www.postgresql.org/docs/current/app-pgdump.html) (e.g. plain-db-dump.sql), a rules file (e.g. pg-anon-rules-v28.0.39.sql). Let's say our image tag is ghcr.io/sage-erp-x3/pg-anon:13, the command to launch is :

```sh
cat plain-db-dump.sql pg-anon-rules-v28.0.39.sql | docker run --rm -i ghcr.io/sage-erp-x3/pg-anon:13 /anon.sh > anon_dump.sql
```

The dump file anon_dump.sql is ready for import.

The following part will explain how to generate a rules file to feed to that command.

The rules generation is based on the decorators `anonymizeMethod` and `anonymizeValue` and creates a SQL script. The rules will anonymize data in a similar way as a tenant export (without a filter on tenant id, as we anonymize the whole database).

The `pg-anonymizer` command is used for generating pg-anonymizer.

## Usage

```sh
xtrem pg-anonymizer [--outputPath {path}]
```

### --outputPath:

If specified, the generated rules will be exported to that location if exists. The default location is 'data/exports/pg-anonymizer'.
The filename format is pg-anon-rules-v{application.version}.sql

    Examples:

```sh
xtrem pg-anonymizer -- --outputPath "nice/test"

xtrem pg-anonymizer

```

## Notes

- This command can be executed without options.
