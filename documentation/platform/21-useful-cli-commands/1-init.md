PATH: XTREEM/21+Useful+CLI+commands/1+Init

# Init

The `init` command creates an empty Xtrem package.
When executed, the application first connects to the NPM repository to collect information about the available Xtrem packages and their latest published versions.

## Usage

```ts
xtrem init
```

Instead of having parameters, this command uses an interactive wizard for user input.
The following questions are asked during the initiation process:

-   **Vendor name:** The name of the vendor who will develop the new package. The value must be in "kebab-case" format (lowercase letters, words separated by hyphens, for example `any-software-company`)
-   **Package name:** The name of the new package. The value must be in "kebab-case" format (lowercase letters, words separated by hyphens, for example `my-awesome-package`)
-   **Dependencies:** Xtrem packages that the new package will depend on. The options are presented by a selection list. Packages can be selected and unselected by the _Space_ key. If all desired packages are selected, the selection is submitted by the _Enter_ key
-   **Database driver**: As the final step, a database driver can be chosen. For utility packages that are not meant for end-user deployments, the "none" option should be selected.

After the steps of the wizard are completed, the Xtrem CLI creates a new directory in the current working directory using the selected package name and populates it with a sample package content and installs the selected dependencies.

## Notes

-   For finding application packages, the Xtrem CLI relies on the machine's configured NPM registries and saved authentication credentials.
-   The initial information collection and the final dependency installation can take a couple of minutes.
