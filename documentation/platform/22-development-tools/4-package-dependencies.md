PATH: XTREEM/22+Development+tools/4+Package+Dependencies

# Package Dependencies Tool

To view the dependencies between the XTreeM packages as a set of graphs, you can execute the following command from the 'xtrem' folder:

    pnpm run generate:dependencies <outDir>

This command generates a set of HTML files in the chosen \<outDir\> folder. Open the 'index.html' file in a browser to view the root level of the package hierarchy.

This command requires graphviz to be installed (see https://graphviz.org/download/)

# Packages hierarchy

The Typescript packages are displayed on the 3rd level of the hierarchy. Packages are displayed with a blue border.

The levels above correspond to the folder structure that groups the packages. Arrows between the boxes show the dependencies. One arrow from A to B indicates that A contains at least one package that depends on at least one package of B.

The levels below the package level show the subfolder structure of the packages and the Typescript files. Grey arrows show dependencies to other packages. Black arrows show dependencies to other files in the same package. Dotted arrows show dependencies to files in other folders of the same package.

A breadcrumb allows you to navigate into the hierarchy.

Clicking on a Typescript file opens the files on Github.
