PATH: XTREEM/22+Development+tools/2+Generate+Branch+And+Image+For+Cutoff

# Before generating Branch And Image For Cut-off

**Check that there are no issues with the following** (any issues must be resolved before proceeding)

- [ ] The xtrem-patch-release job ran successfully on the master branch for the previous evening.
- [ ] The cluster-cu and cluster-ci environments were successfully upgraded for the day.
- [ ] There were no commits to master post the nightly release patch and prior to the release that would create SQL upgrade actions.
- [ ] If there were other merges to master, excluding schema changes, rerun the release patch and image pipelines to verify that the process still works.

If all of the above checks pass, the Cirrus team must be notified, to update the latest backup of cluster-cu to the current upgraded instance so that the pipelines will run the upgrade tests correctly.

# Generate Branch And Image For Cut-off

To generate a release branch and create an image for the cut-off you'll have to go through the following steps:

- [ ] 2 days before release day, set the number of reviewers on the master branch to 6 in order to prevent merges [https://github.com/Sage-ERP-X3/xtrem/settings/branch_protection_rules/19459553](https://github.com/Sage-ERP-X3/xtrem/settings/branch_protection_rules/19459553)

- [ ] Post a notification about the code freeze on these channels: platform-support, applicative-support and Platform News

> ⚠️**Code freeze - Release N.0**
>
> The number of approvers has been set to 6 on master.
>
> Release **N.0** will be created **2 days from today** around XX PM French time.

- [ ] At least 2 hours before the creation of the branch, warn users of the upcoming creation of the release branch on channels platform-support, applicative-support and Release Delivery

> ⚠️**Release branch N.0**
>
> Branch release/N.0 will be created today at XX PM French time.
>
> PRs will be blocked for the time required to execute the process.

- [ ] Warn users of the creation of the release branch

> ⚠️**Release branch N.0**
>
> Release branch N.0 is in progress.
>
> Please, stay tuned and wait for the green light before merging your PRs

- [ ] Run release branch pipeline: [https://sage-liveservices.visualstudio.com/X3%20XTREM/\_build?definitionId=1385](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=1385)

- The following 2 steps can be proceeded in parallel:

    - [ ] Run release patch job on release/N.0 **selecting the release/N.0 branch and check all images to be created**: [https://sage-liveservices.visualstudio.com/X3%20XTREM/\_build?definitionId=1286](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=1286)

    - [ ] Kick off the release patch job once more, for **master** branch (this is to fix latest tag for packages on npm) [https://sage-liveservices.visualstudio.com/X3%20XTREM/\_build?definitionId=1286](https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=1286)

- [ ] Wait for the notification in the team channel [Release Delivery Notifications](https://teams.microsoft.com/l/channel/19%3A135dd65c08b2459794c5fd3303a3f122%40thread.tacv2/Release%20Delivery%20Notifications?groupId=46560869-b2b0-4805-bc3a-d031e2b373da&tenantId=) to get the version of the images built for **release/N.0**.

- [ ] Set the number of reviewers on the master branch back to 1 [https://github.com/Sage-ERP-X3/xtrem/settings/branch_protection_rules/19459553](https://github.com/Sage-ERP-X3/xtrem/settings/branch_protection_rules/19459553)

- [ ] Notify users on the above-mentioned channels, that the release branch was created and give them the tag of the new image

> ⚠️**Release Branch N.0**
> The branch release/N.0 has been created and the master branch is now updated to version N+1.
>
> This is the green light for merging your PRs to master.
>
> The new images were tagged:
>
> - services: ghcr.io/sage-erp-x3/xtrem:N.0.X
> - glossary: ghcr.io/sage-erp-x3/xtrem:glossary-N.0.X
