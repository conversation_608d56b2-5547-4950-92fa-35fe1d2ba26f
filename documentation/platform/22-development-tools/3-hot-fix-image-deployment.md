PATH: XTREEM/22+Development+tools/3+Hotfix+Image+Deployment

# Hotfix Image Deployment

To generate an image on release branch, the following steps need to be followed:

- [ ] Prior to starting the process, warn users on channels platform-support, applicative-support and Release delivery

> ⚠️**Hotfix -Release branch N.0**
>
> We are beginning the deployment of the hot fixes on release/N.0.
> Once complete we will advise the new image tag.

- [ ] Run release patch job (https://sage-liveservices.visualstudio.com/X3%20XTREM/\_build?definitionId=1286&\_a=summary) selecting the release/N.0 branch.

- [ ] Once release patch job has completed, manually run the required image pipeline for hotfix (xtrem-services-image, xtrem-x3-services-image or xtrem-glossary-image, ...), **selecting the release/N.0 branch**.. Ex. If the hotfix is on services only then do not run the image pipeline for xtrem x3.
    - xtrem-service-image https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=653
    - xtrem-x3-services-image https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=885
    - xtrem-glossary https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=852

These are the current image pipelines, if more applications are added, then that applications image pipeline must also be considered.

- [ ] Once the image pipeline has completed go to step **Push the image** and get the tag of the image pushed to GitHub container registry (example ghcr.io/sage-erp-x3/xtrem:8.0.51). Post the new tag number on "Release Delivery"/"Platform News"/"platform-support" channels on Teams.

- [ ] Kick off the release patch job once more, for master branch (this is to fix latest tag for packages on npm)

- Temporarily action prior to the restriction on SQL JSON file generation, migrate the new generated SQL json file generated when the patch release job was executed on the release/N.0 branch to master and fix json file names sequence on master.

**Example:**
We run the release patch job for release/9.0 branch, if xtrem-services-main was bump from 9.0.52 to 9.0.53, then currently a file (<EMAIL>) will be generated in xtrem-services-main/sql. This file will not exist on master. Therefore we need to copy this <NAME_EMAIL> to xtrem-services-main/sql <NAME_EMAIL> to <EMAIL> (to fix the version sequence)

This will be resolved once we stop the generation of this file on release branches, as SQL schema changes will not be allowed on minor or patch deployments.
