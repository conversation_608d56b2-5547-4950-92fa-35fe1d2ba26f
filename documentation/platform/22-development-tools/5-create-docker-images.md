PATH: XTREEM/22+Development+tools/5+create+docker+images

# Dockerfile rules

## 1 - Use multi-stage images to reduce the size of the final image and avoid disclosing secrets

A sample of a simplified xtrem app using multi-stage build:

```
# ==========================
# Base stage - system requirement
# ==========================
FROM node:18.15-alpine as base

ARG node_env="production"

ENV NODE_ENV=$node_env

RUN apk update && apk upgrade \
    && apk --no-cache --update add dumb-init

WORKDIR /xtrem/app

# make xtrem bin available in path
ENV PATH /xtrem/app/node_modules/.bin:$PATH

COPY docker-entrypoint.sh /usr/local/bin/
RUN chmod 755 /usr/local/bin/docker-entrypoint.sh

COPY --chown=node:node package.json .
COPY --chown=node:node pnpm-lock.yaml .
COPY --chown=node:node xtrem-config-json-schema.json .

# ==========================
# Build stage
# ==========================
FROM base as app-build

ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"

RUN corepack enable \
    && corepack prepare $(node -p 'require("./package.json").packageManager.split("+")[0]') --activate
RUN --mount=type=secret,id=xtrem-env source /run/secrets/xtrem-env \
    && export NPMRC_LOCATION="$(npm config get userconfig)" \
    && echo "Writing to path: ${NPMRC_LOCATION}" \
    && echo "@sage:registry=https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/" >> ${NPMRC_LOCATION} \
    && echo "//pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/:_authToken=${AZURE_TOKEN}" >> ${NPMRC_LOCATION} \
    && echo "always-auth=true" >> ${NPMRC_LOCATION} \
    && echo "progress=false"

RUN apk add --no-cache python3 make
RUN pnpm install --prod --frozen-lockfile --config.node-linker=hoisted

# ==========================
# Final stage - copy only the result of the build into the base image
# ==========================
FROM base as final

RUN chown -R node:node /xtrem/app

USER node

COPY --from=app-build --chown=node:node /xtrem/app/node_modules ./node_modules

VOLUME /xtrem/config

ENTRYPOINT ["/usr/bin/dumb-init", "--", "docker-entrypoint.sh"]

EXPOSE 8240

CMD ["xtrem"]

```

## 2 - Use the `--secret` option during the build of the image

To prevent disclosing secrets of a single-stage image or if for any reason an intermediate stage of a multi-stage image is pushed by mistake.

```sh
    DOCKER_BUILDKIT=1 docker build --secret id=build-env,src=.env -t image:tag .
```

## 3 - Install only production dependencies:

```
RUN pnpm install --prod --frozen-lockfile --config.node-linker=hoisted
```

## 4 - Optimize Node.js apps for production:

```
ENV NODE_ENV production
```

## 5 - Don’t run Node.js apps as root:

```
USER node

COPY --chown=node:node . /usr/src/app
```

## 6 - Properly handle events to safely terminate a Node.js application

```
CMD ["dumb init , node , server.js"]
```

# Credits

https://snyk.io/wp-content/uploads/NodeJS-CheatSheet.pdf
