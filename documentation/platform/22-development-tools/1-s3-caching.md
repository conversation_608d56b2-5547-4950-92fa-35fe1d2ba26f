PATH: XTREEM/22+Development+tools/1+S3+caching

The distributed cache used for `build`, `lint`, `test`, `install` commands is using the S3 bucket s3://xtrem-developers-utility/

# S3 bucket structure

```
s3://xtrem-developers-utility/
  |
  +-- cache
     |
     +-- build-binary           (archive) result of 'build:binary' commands
     |
     +-- build                  (archive) result of 'build' commands
     |
     +-- install                (archive) the result of 'install' commands
     |
     +-- lint-filename          (marker) were the filenames of a package successfuly linted ?
     |
     +-- lint                   (marker) was a package successfuly linted ?
     |
     +-- scope                  (content) cached scopes for a given package/hash (dependent packages)
     |
     +-- test-integration       (marker) were the integration tests successfull for a given hash ?
     |
     +-- test                   (marker) were the tests successfull for a given hash ?

```
