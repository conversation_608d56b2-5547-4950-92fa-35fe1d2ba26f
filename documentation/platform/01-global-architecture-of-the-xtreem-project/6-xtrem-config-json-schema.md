PATH: XTREEM/01+Global+architecture+of+the+XtreeM+project/6+xtrem-config-json-schema

_Auto-generated doc from [JSON schema](https://github.com/Sage-ERP-X3/xtrem/blob/master/xtrem-config-json-schema.json)_

## xtrem-config.yml

The main config is a YAML file with the following properties.

### addOns

type: [AddOnConfig](#add-on-config-id)



### agGridLicenceKey

type: `string`

Licence key for ag-grid

### app

type: `string`

App name for this container in a multi-app infrastructure

### applicationId

type: `string`

application id registered in api gateway

### applicationName

type: `string`

application name registered in api gateway

### apps

type: [AppsConfig](#apps-config-id)

apps, from apps.yml (next to xtrem-config.yml or under /infra)

### asyncContextTableName

type: `string`



### authentication

type: [AuthenticationServiceConfig](#authentication-service-config-id)



### authenticationContainer

type: [AuthenticationContainerConfig](#authentication-container-config-id)

The configuration to start the authentication service container in prod-ui mode

### aws

type: [AwsConfig](#aws-config-id)



### baseUrl

type: `string`



### binaryStreamContentTypes

type: `string[]`

Content types allowed for binary streams (regex list)

### cli

type: `Object`

CLI plugins

#### plugins

type: `string[]`


### cluster

type: [ClusterConfig](#cluster-config-id)



### clusterId

type: `string`



### copilot

type: [CopilotConfig](#copilot-config-id)

Configuration for copilot chat and insights

### debugMetrics

type: [DebugMetricsOptions](#debug-metrics-options-id)

Configuration of debug metrics

### deploymentMode

type: `string`



### disableUiTemplateCache

type: `boolean`

Disables caching the index.html template, it is practical when working on changes for xtrem-standalone in watch mode

### documentationServiceUrl

type: `string`

URL of the documentation service where user help pages are located

### email

type: `string`



### endpoint

type: `string`



### env

type: `Object`



#### isCI

type: `boolean`


### errorMonitoringInterval

type: `number`

interval of error monitoring in seconds (default to 3600)

### errorMonitoringThreshold

type: `number`

number of errors during the monitoring interval that will cause a process exit (default to 10)

### exportCsv

type: [ExportCsvOptions](#export-csv-options-id)



### extends

type: `string`

Path of config file name that we are extending

### extensionPath

type: `string`



### graphql

type: [GraphQlConfig](#graph-ql-config-id)



### ignoreVendorProtection

type: `boolean`

Ignore vendor protection rule on data update

### importCsv

type: [ImportCsvOptions](#import-csv-options-id)



### interop

type: [InteropConfig](#interop-config-id)



### login

type: `string`

Login is the EM login

### logs

type: [LogsConfig](#logs-config-id)



### newRelic

type: [NewRelicConfig](#new-relic-config-id)

Configuration properties used for client error reporting and benchmarking

### noUi

type: `boolean`



### operatorUserHashSecret

type: `string`



### originFolder

type: `string`

The folder where the config was loaded from

### packages

type: `Record<string, any>`



### pendo

type: [PendoOptions](#pendo-options-id)



### prodUi

type: `boolean`

Deploy the production UI application instead of the developer consumer mock

### productName

type: `string`

Visible product name, used in application header and metadata fields.

### publicAppUrl

type: `string`

Public url of the app on this container

### reportOptions

type: [ReportOptions](#report-options-id)



### s3

type: `Record<string, S3Config>`



### s3Storage

type: [S3StorageConfig](#s-3-storage-config-id)



### scope

type: `string`

scope of api call

### security

type: [SecurityConfig](#security-config-id)



### semVerCompatibilityLevel

type: `string`

The level at which we will be validating the package version differences

### server

type: [ServerConfig](#server-config-id)



### serviceOptions

type: [ServiceOptions](#service-options-id)

The level at which service options are displayed and enabled in the product
If this attribute is not present it is set to workInProgress in dev mode,
and to released in production mode.

### settings

type: `Record<string, any>`



### storage

type: [StorageConfig](#storage-config-id)



### system

type: [SystemConfig](#system-config-id)



### telemetrySalt

type: `string`

Salt that is used to create anonym unique user and tenant IDs

### tenantId

type: `string`



### textServerUrl

type: `string`



### textStreamContentTypes

type: `string[]`

Content types allowed for text streams (regex list)

### textStreamLazyLoadLength

type: `number`

text stream length that will be used to check if a text stream is lazy loaded

### ui

type: [UiConfig](#ui-config-id)



### uiBroadcastTimeout

type: `number`

Configuration for message broadcasting

### uiConfigPath

type: `string`



### upgrade

type: [UpgradeOptions](#upgrade-options-id)

Options for upgrade.

### user

type: `string`



### webSocket

type: `Record<string, WebSocketConfig>`



### workflow

type: [WorkflowOptions](#workflow-options-id)



### xtremDeploymentCoreTableName

type: `string`





<a id="add-on-config-id"></a>
## AddOnConfig

### folder

type: `string`





<a id="apps-config-id"></a>
## AppsConfig




<a id="authentication-service-config-id"></a>
## AuthenticationServiceConfig

### interopUrl

type: `string`



### ssl

type: [Pick<TlsOptions,"key"|"cert"|"ca">](#pick-tls-options-key-cert-ca-id)





<a id="pick-tls-options-key-cert-ca-id"></a>
## Pick<TlsOptions,"key"|"cert"|"ca">

### ca

type: `undefined`

Optionally override the trusted CA certificates. Default is to trust
the well-known CAs curated by Mozilla. Mozilla's CAs are completely
replaced when CAs are explicitly specified using this option.

### cert

type: `undefined`

Cert chains in PEM format. One cert chain should be provided per
private key. Each cert chain should consist of the PEM formatted
certificate for a provided private key, followed by the PEM
formatted intermediate certificates (if any), in order, and not
including the root CA (the root CA must be pre-known to the peer,
see ca). When providing multiple cert chains, they do not have to
be in the same order as their private keys in key. If the
intermediate certificates are not provided, the peer will not be
able to validate the certificate, and the handshake will fail.

### key

type: `undefined`

Private keys in PEM format. PEM allows the option of private keys
being encrypted. Encrypted keys will be decrypted with
options.passphrase. Multiple keys using different algorithms can be
provided either as an array of unencrypted key strings or buffers,
or an array of objects in the form {pem: <string|buffer>[,
passphrase: <string>]}. The object form can only occur in an array.
object.passphrase is optional. Encrypted keys will be decrypted with
object.passphrase if provided, or options.passphrase if it is not.



<a id="authentication-container-config-id"></a>
## AuthenticationContainerConfig

The config used to start the Authentication service container in prod-ui mode.

The values for <clientId> and <clientSecret> can be found in Keeper (Global XTreem/Authentication service/Authentication service)

### clientId

type: `string`

The CLIENT_ID to start the authentication service container

### clientSecret

type: `string`

The CLIENT_SECRET to start the authentication service container



<a id="aws-config-id"></a>
## AwsConfig

### region

type: `string`





<a id="cluster-config-id"></a>
## ClusterConfig

### numberOfForkedProcesses

type: `number`





<a id="copilot-config-id"></a>
## CopilotConfig

### accessCodeLifeTimeInMinutes

type: `number`

ttl for the access code in minutes

### audience

type: `string`

Copilot sage id audience

### clientId

type: `string`

Copilot sage id client id

### clientSecret

type: `string`

Copilot sage id client secret

### gmsClient

type: `string`

Client name passed to the GMS Service e.g. sdmo_v1

### oauthEndpointUrl

type: `string`

oauth url

### serviceUrl

type: `string`

Copilot service url



<a id="debug-metrics-options-id"></a>
## DebugMetricsOptions

Configuration of debug metrics




<a id="export-csv-options-id"></a>
## ExportCsvOptions

### chunkSize

type: `number`



### maxRetryUploadedFileCreation

type: `number`





<a id="graph-ql-config-id"></a>
## GraphQlConfig

### isReadonly

type: `boolean`



### keepLocalConfig

type: `boolean`



### maxNodesPerPage

type: `number`



### timeLimitInSeconds

type: `number`





<a id="import-csv-options-id"></a>
## ImportCsvOptions

### chunkSize

type: `number`





<a id="interop-config-id"></a>
## InteropConfig

### appsHealthCheckSeconds

type: `number`

interval of apps health check in seconds (default to 60)

### concurrentMessagesLimit

type: `number`

size of the receiving message funnel

### concurrentNotificationsLimit

type: `number`

size of the receiving notification funnel

### devEndpoint

type: `string`

elasticmq endpoint

### graphqlTimeLimitInSeconds

type: `number`

timeout in seconds for the interop graqhql request to be completed (default to 120)

### heartbeatSeconds

type: `number`

heartbeat rate to refresh list of live containers

### listenerMonitoringSeconds

type: `number`

interval of monitoring the listening queues in seconds (default to 1)

### messageVisibilitySeconds

type: `number`

time in seconds received message is made invisible on SQS (default 30 seconds)

### queueUrlDeadLetterTemplate

type: `string`

handlebars template for dead letter queue URL (cluster V2)

### queueUrlTemplate

type: `string`

handlebars template for queue URL (cluster V2)

### queues

type: `Record<string, QueueConfig>`



### receiveLowerBound

type: `number`

lower bound list size for the messages that are in progress (default to 6)

### receivePollingSeconds

type: `number`

interval to wait when polling receive queue(default to 1)

### receiveRetryCount

type: `number`

maximum number of retries trying to receive messages from SQS queue before error (default to 3)

### receiveRetrySeconds

type: `number`

interval of retries in seconds (default to 1)

### receiveUpperBound

type: `number`

upper bound list size for the messages that are in progress (default to 11)

### remoteQuery

type: [InteropRemoteQueryConfig](#interop-remote-query-config-id)

remote query configuration

### routingPollingSeconds

type: `number`

interval of notifications pooling in seconds (default to 1)

### routingReadCount

type: `number`

maximum number of notifications pooling (default to 3)

### runningJobListTtlInSeconds

type: `number`

The time in seconds used to refresh the cache of SysNotificationState

### sendRetryCount

type: `number`

maximum number of retries trying to receive messages from SQS queue before error (default to 3)

### sendRetrySeconds

type: `number`

interval of retries in seconds (default to 90)

### sqsGroupsPerTenantUser

type: `number`

Number of SQS groups per tenant user (default to 5).
A given tenant user cannot have more than this number of SQS messages being processed simultaneously



<a id="interop-remote-query-config-id"></a>
## InteropRemoteQueryConfig

### maxNodesPerPage

type: `number`

Maximum number of nodes per page

### maxNodesPerQuery

type: `number`

maximum number of nodes per query result



<a id="logs-config-id"></a>
## LogsConfig

### disabled

type: `boolean`

Are all logs disabled?

### disabledForTests

type: `boolean`

Are all logs disabled when running tests?

### domains

type: `Record<string, LogDomain>`



### filenamePrefix

type: `string`



### options

type: [LogOptions](#log-options-id)



### outputFolder

type: `string`





<a id="log-options-id"></a>
## LogOptions

### json

type: `boolean`



### noColor

type: `boolean`





<a id="new-relic-config-id"></a>
## NewRelicConfig

### accountId

type: `string`



### applicationId

type: `string`



### licenceKey

type: `string`



### trustKey

type: `string`





<a id="pendo-options-id"></a>
## PendoOptions

### apiKey

type: `string`

The API key

### clusterTag

type: `string`

pendo tag to identify cluster type

### subscriptionId

type: `string`

pendo subscription id



<a id="report-options-id"></a>
## ReportOptions

### browserTimeout

type: `number`

The maximum time in milliseconds to wait for the browser to start. Pass 0 to disable it. (default to 180000)

### maxTotalPages

type: `number`



### pageOpeningTimeout

type: `number`



### pdfTransformationTimeout

type: `number`

The maximum time in milliseconds for page PDF transformation. Pass 0 to disable it. (default to 120000)

### protocolTimeout

type: `number`

The maximum time for individual protocol calls in milliseconds (default to 600000)



<a id="s-3-storage-config-id"></a>
## S3StorageConfig

### localBasePath

type: `string`



### s3BucketUrlPrefix

type: `string`

For presigned URLs, we need this when adding an exception to CSP for uploading of files by the client
Example: https://xtrem-dev-eu-showcase.s3.eu-west-1.amazonaws.com

### s3ClusterBucket

type: `string`

Cluster's S3 bucket name used to pass to file storage instance, to upload/download tenant specific files

### s3UpgradeMetricsFolder

type: `string`

The (optional) folder (s3ClusterBucket) from where the upgrade metrics should be uploaded



<a id="security-config-id"></a>
## SecurityConfig

### audience

type: `string`



### enableAutoRefresh

type: `boolean`



### issuer

type: `string`



### jsEval

type: `Object`



#### timeoutInMillis

type: `number`


### jwksUrl

type: `string`



### loginUrl

type: `string`



### redirectUrl

type: `string`



### renewalThreshold

type: `number`



### renewalUrl

type: `string`



### services

type: [SecurityServicesConfig](#security-services-config-id)



### sizeLimits

type: [SizeLimits](#size-limits-id)



### tls

type: `Object`



#### extraCaFiles

type: `undefined`




<a id="security-services-config-id"></a>
## SecurityServicesConfig

### helmet

type: `Object`



#### csp

type: `Object`



#### disabledOptions

type: `Object`



#### hsts

type: `undefined`


### tokenInvalidation

type: `Object`



#### active

type: `boolean`


### tooBusy

type: `Object`



#### disabled

type: `boolean`



#### intervalInMillis

type: `number`



#### maxLagInMillis

type: `number`



#### retryAfterInSeconds

type: `number`



#### smoothingFactor

type: `number`




<a id="size-limits-id"></a>
## SizeLimits

### maxRequestSize

type: `string`



### maxStreamSize

type: `string`



### maxUploadSize

type: `string`





<a id="server-config-id"></a>
## ServerConfig

### interopPort

type: `number`



### metricsPort

type: `number`



### port

type: `number`



### requestFunnelSizeFactor

type: `number`

the factor to apply to the max value of the sql config in order to compute the final request funnel size

### ssl

type: [TlsOptions](https://nodejs.org/docs/latest-v20.x/api/tls.html#tlscreatesecurecontextoptions)



### worker

type: [WorkerConfig](#worker-config-id)





<a id="worker-config-id"></a>
## WorkerConfig

### workersPerRequestSource

type: `number`





<a id="service-options-id"></a>
## ServiceOptions

### level

type: [ServiceOptionStatus](#service-option-status-id)





<a id="service-option-status-id"></a>
## ServiceOptionStatus




<a id="storage-config-id"></a>
## StorageConfig

### managedExternal

type: `boolean`

Is storage managed by an external storage engine (not PostgreSQL)

### maxConnections

type: `number`

pool size, for external storage engines (postgres uses storage.sql.max)

### prefetch

type: [PrefetchConfig](#prefetch-config-id)

SQL prefetch configuration

### sql

type: [SqlConfig](#sql-config-id)

Postgres configuration



<a id="prefetch-config-id"></a>
## PrefetchConfig

Configuration for SQL prefetch

### isDisabled

type: `boolean`

Is prefetch disabled

### logCounters

type: `boolean`

Do we log the SQL counters?
Counters are collected for each transaction, and logged at the end of the transaction.
Counters are also collected globally, and logged every 5 (configurable) seconds.

### logCountersIntervalInSeconds

type: `number`

Number of seconds between two logs of the global SQL counters

### logStatements

type: `boolean`

Do we log SQL statements (only verb + node name)

### spiedNodeNames

type: `string[]`

Node names for which we are logging SQL sql statements
Entries prefixed by ~ are interpreted as regex



<a id="sql-config-id"></a>
## SqlConfig

### connectionMaxRetries

type: `number`

Number of times to retry SQL connection - default 3

### connectionRetryMillis

type: `number`

Milliseconds to wait before retrying connection - default 2000

### connectionTimeoutMillis

type: `number`

Number of milliseconds to wait before timing out when connecting a new client - default 5000

### database

type: `string`



### delayBeforeRetry

type: `number`

Delay before retrying SQL statement on error

### hostname

type: `string`



### idleTimeoutMillis

type: `number`

Number of milliseconds before a client is terminated if it is idle - default 10000

### mapArgsInLogs

type: `boolean`

Should all the $xx parameters be replaced by their value ?

### max

type: `number`

Pool size - default 20

### maxRetriesOnTransactionConflicts

type: `number`

Max number of retries on conflicts between transactions

### maxSubQueryDepth

type: `number`

Maximum subquery depth that the ts-to-sql converter accepts

### maxTries

type: `number`

Max number of attempts to execute a SQL statement error

### maxUses

type: `number`

Number of transactions per connection before closing to cycle connections - default 7500

### password

type: `string`



### poolMaxIdleSeconds

type: `number`



### port

type: `number`



### readonlyHostname

type: `string`

Read-Only db replica hostname

### readonlyPort

type: `number`

Read-Only db replica port

### sqlStatementCacheSize

type: `number`

Size of cache of SQL statements - default 1000

### ssl

type: `undefined`

SSL config

### statementTimeoutMillis

type: `number`

Number of milliseconds before a statement in query will time out, default is no timeout.
https://node-postgres.com/apis/client
https://stackoverflow.com/questions/59155572/how-to-set-query-timeout-in-relation-to-statement-timeout

### subscriber

type: [SubscriberConfig](#subscriber-config-id)

Subscriber config

### sysDatabase

type: `string`



### sysPassword

type: `string`



### sysUser

type: `string`



### user

type: `string`





<a id="subscriber-config-id"></a>
## SubscriberConfig

### paranoidChecking

type: `undefined`

Interval in ms to run a trivial query on the DB to see if
the database connection still works.
Defaults to 30s.

### retryInterval

type: `undefined`

How much time to wait between reconnection attempts (if failed).
Can also be a callback returning a delay in milliseconds.
Defaults to 500 ms.

### retryLimit

type: `number`

How many attempts to reconnect after connection loss.
Defaults to no limit, but a default retryTimeout is set.

### retryTimeout

type: `number`

Timeout in ms after which to stop retrying and just fail. Defaults to 3000 ms.



<a id="system-config-id"></a>
## SystemConfig

### dnsCache

type: `Object`

interval of dns cache in seconds (default to 30)

#### cachesize

type: `number`

maximum number of entries to store in the cache (default to 1000)

#### enable

type: `boolean`

enable dns cache (default to true)

#### ttl

type: `number`

time to live in seconds (default to 30)
### natIpAdresses

type: `string`

a comma separated list of nat IP addresses



<a id="ui-config-id"></a>
## UiConfig

### exclude

type: `string[]`



### include

type: `string[]`





<a id="upgrade-options-id"></a>
## UpgradeOptions

### activateNewPackages

type: `boolean`



### fullReloadOfSetupLayer

type: `boolean`

Temp hack for upgrade on showcase (https://jira.sage.com/browse/XT-23045)
Should all the CSV files from the SETUP layer be reloaded at the end of the upgrade ?
This will bypass the lookup from the git repo and reload all the **setup** CSV files

### upgradeOnly

type: `boolean`

associated with the CLI upgrade option --prod this option makes it possible to skip
the playing of recorded SQL files.



<a id="workflow-options-id"></a>
## WorkflowOptions

### captureFetchSize

type: `number`

Max number of processes that we try to resume at once

### capturePollingMaxSeconds

type: `number`

Maximum wait time when when capturing processes

### capturePollingMinSeconds

type: `number`

Minimum wait time when capturing processes

### unresponsiveDelayInSeconds

type: `number`

Delay after which we consider a process as killed if it has not updated its state

