PATH: XTREEM/28+Import+Export/2+Import+Export+Template

# Import Export templates

On the import export template page, a ready-to-use template can be automatically generated for a selected node.

These automatically generated templates will contain rows with helpful information
on how the data in the imported files should be completed.

Automatically generated templates include:

-   A headers line including:
    -   Mandatory properties,
    -   Natural keys,
    -   Vital references,
    -   Vital collections,
    -   A proposition of keys for references,
    -   Suffixed names when a collision is detected,
-   An ignored line that contains a short description of each data type:
    -   In the case of an enum, the description contains all possible values,
    -   These descriptions can be modified as long as the IGNORE key is preserved
-   An ignored line that contains a short description of each data type:
    -   In the case of a boolean, the first value is the default value,
    -   In the case of a date, the description contains a reminder of the format to be used for such data (YYYY-MM-DD)
    -   In the case of a datetime, the description contains a reminder of the format to be used for such data (YYYY-MM-DD[T]HH:mm:ssZ)
    -   These descriptions can be modified as long as the IGNORE key is preserved

# Default templates delivered with the setup layer

In order to help users, SDMO provides default templates by the setup layer. The application developers must maintain these templates because when a node change happens, the template could be un-synced, for example, if we add a new property named "customerType" which is mandatory to "Customer" node, if the template is not updated, when the user uses it to import, the import will fail because of this missing mandatory field.

## Steps to follow to update the default template

On your development branch, build the project and launch SDMO locally:

```sh
pnpm i && pnpm run build
cd services
pnpm run load:test:data
pnpm start
```

Goto http://localhost:8240/@sage/xtrem-system/ServiceOptionState
Deactivate all service options unless we need some given service options to be active

Goto http://localhost:8240/@sage/xtrem-import-export/ImportExportTemplate
Create new template, e.g "CustomerNew"
Choose the Customer node, then the grid will be filled with the default properties pulled from that node
Remove any unwanted property (if any), please check will your PO/PM
Then click Save

Run the export csv to get the new template (xtrem-services-main must be built but not started):

```sh
cd services/main/xtrem-services-main
pnpm run extract:setup:data
```

The new data will be generated in platform/system/xtrem-import-export/data/layers/setup/import-export-template.csv

Find your newly generated record "CustomerNew" then replace the old "Customer" csv_template column one with your new one. Actually, we spread different templates in different business packages to achieve clarity (as we can also combine all in a single file as it is the setup layer). However, if you do think that we need to record from one csv to another one, please check with you PO/PM and feel free to do so.

Create your PR with this change.

**Please remember that we only need to change the content of the template "csv_template" column, so if you replace the whole record, check other column values to make sure that we do not change them inadvertently**
