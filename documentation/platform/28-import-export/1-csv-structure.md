PATH: XTREEM/28+Import+Export/1+CSV+Structure

# CSV structure

Data is imported in CSV format.

-   The CSV files may contain as many records as required but the size of the file may not exceed 1Gb.

## Delimiter (';')

The column delimiter that should be used in the CSV file is a semicolon ';'.

## Header

The first row in the CSV file is the header of the nodes that will be imported. The header contains the name of all the properties of the node to be imported. The first columns in the header must always be the main node being imported. The header contains the property names of the nodes and not the column names (as these may differ). The rows following this header row will be the data to be imported for each node.

### Unique indexes ('!')

The property that represents the **natural key** of a record in the header, should be preceded by a '!'. This is however only necessary if the records in the import file will update records that already exist in the system (update). If all the records in the import must be created (inserted) into the system, it is not required to use the exclamation mark to indicate the key.

### Mandatory properties ('\*')

The properties in the header that are mandatory should be preceded by '\*'.

### Reference properties (ref(idx1|idx2|...|idxn))

When importing a property that references a different node, the column containing the property should include the **natural key** of the node being referenced. The **natural key** should be in parentheses. If the property that is being imported has a composite **natural key**, all the key properties of the target node should be included in the parenthesis separated by '|'. For example:

-   When importing a customer in a property the header column will contain customer(id).
-   When importing an item in a property, the header column will contain item(code).
-   When importing an invoice line in a property, the header column will contain invoiceLine(id|lineNumber).

### Ignored lines

Rows containing the value _IGNORE_ in the column just after the last column identified by a header will be ignored.

In automatically generated import templates, there will be two rows containing information on how the data should be completed in the import templates (for example, in which format dates should be and the available values for enums).

```csv
customer(id);item(id);isActive;fromQuantity;toQuantity;fromDate;toDate;itemPriceType;customerPriority;salesSite(id);shippingSite(id);customerUnitOfMeasure(id);*customerPrice;customerCurrency(id);customerDiscount;customerCharge;
reference;reference;boolean;decimal;decimal;date;date;enum(normal,specialOffer,discount);integer;reference;reference;reference;decimal;reference;decimal;decimal;IGNORE
customer;item;is active(true/false);from quantity;to quantity;from date(YYYY-MM-DD);to date(YYYY-MM-DD);item price type;customer priority;sales site;shipping site;customer unit of measure;customer price;customer currency;customer discount;customer charge;IGNORE
CUST03;Chemical A;true;1;100;2020-01-01;2020-12-31;normal;1;US001;;each;100;EUR;3;2;
CUST03;Chemical A;true;1;100;2021-01-01;2022-12-31;discount;2;US001;;each;95;EUR;1;0;

```

## Sub-levels

It is possible to import multiple nodes with one CSV. In the header column when the properties of the next node are defined, the first properties (of the node) should be preceded by either a '#' or a '/'.

-   The '#' is used when the node that is being imported is a collection.
-   The '/' is used when the node that is being imported is a basic object.

### Defining the levels of the nodes.

The main node requires no character to indicate the level. All subsequent levels will have an additional character to indicate the hierarchical level of the data to be imported. For example:

-   '#' start a sub-level.
-   '##' start a sub-sub-level (a sub-level of the current sub-level)
-   '###' start a sub-sub-sub-level, etc.

## Data types

It is possible to import all data types in a CSV.

### Dates

For dates, it is important to ensure when importing dates that the format is correct in the CSV file. The format should be YYYY-MM-DD.

### Datetimes

For datetimes, it is important to ensure when importing dates that the format is correct in the CSV file. The format should be YYYY-MM-DD[T]HH:mm:ssZ.

### Localized texts

The following rules apply for localized texts:

-   A CSV file can contain several columns for the same localized text,
-   Each column will contain text for the country language specified in the header,
-   The country language for which the text will be used is specified using an optional (language-country) suffix after the property.
-   If no country language is specified the text will be used to set the base text, the one that is used as a fallback.
-   When creating a localized text Xtreem will use the country language version of the text to create the language version,
-   When creating a localized text Xtreem will make sure that the base version of the text is created. The base text will be created using the value of the column without suffixes or the value of the first column found in the CSV file.
-   When updating a localized text the description for only the country language is updated
-   When updating a localized text, if the (language-country) suffix is omitted,the whole record will be replaced by { base: 'newvalue'}.
-   When updating a localized text, if the (language-country) suffix is omitted but there is the second column of the same localized text with (language-country) indicator,the column without (language-country) indicator will be considered as base, so only the country language and the base one are updated

**Insert examples**

-   **Scenario 1**

    -   In the import file Description, Description (en-US)
    -   Create in the table Description (base), Description (en-US), Description (en)

-   **Scenario 2**

    -   In import file Description (en-US)
    -   Create in the table Description (en-US) Description (en) Description (base)

-   **Scenario 3**

    -   In the import file Description
    -   Create in the table Description (base)

-   **Scenario 4**

    -   In import file Description (fr-FR)
    -   Create in the table Description (fr-FR) Description (fr) Description (base)

-   **Scenario 5**

    -   In import file Description (en-UK) Description (en-US)
    -   Create in the table Description (en-UK) Description (en) Description (en-US) Description (base)

-   **Scenario 6**

    -   In import file Description (en)
    -   Create in the table Description (en) Description (base)

**Update examples**

-   **Scenario 1**

    -   In the import file Description, Description (en-US)
    -   Existing in table Description (base), Description (en-US), Description (en)
    -   Update in the table Description (base), Description (en-US)

-   **Scenario 2**

    -   In import file Description (en-US)
    -   Existing in table Description (en-US) Description (en) Description (base)
    -   Update in the table Description (en-US)

-   **Scenario 3**

    -   In the import file Description
    -   Existing in table Description (base)
    -   Update in the table Description (base)

-   **Scenario 4**

    -   In import file Description (fr-FR)
    -   Existing in table Description (fr-FR) Description (base)
    -   Update in the table Description (fr-FR)

-   **Scenario 5**

    -   In import file Description (en-UK) Description (en-US)
    -   Existing in table Description (en-UK) Description (en) Description (en-US) Description (base)
    -   Update in the table Description (en-UK) Description (en-US)

-   **Scenario 6**

    -   In import file Description (en)
    -   Existing in table Description (en) Description (base)
    -   Update in the table Description (en)

-   **Scenario 7**

    -   In import file Description and Description (en-US)
    -   Existing in table Description (en) Description (en-US) Description (base)
    -   Update in the table the whole record Description

-   **Scenario 8**

    -   In import file Description and Description (en-US)
    -   Existing in table Description (en) Description (en-US) Description (base)
    -   Update in the table the whole record Description with empty value

-   **Scenario 9**

    -   In import file Description and Description (en-US)
    -   Existing in table Description (en) Description (en-US) Description (base)
    -   Update in the table Description(en-US)

-   **Scenario 10**
    -   In import file Description and Description (en-US)
    -   Existing in table Description (en) Description (en-US) Description (base)
    -   Update in the table Description and Description(en-US) (Description is implicitly Description(base))

### Arrays

Arrays, even empty ones, have to be delimited by brackets ( [ and ] ) and elements separated by commas,

### Duplicate properties

If there is more than one property with the same name (in different nodes), in the import file, a collision tag will be added to all duplicate properties following the first property. The collision tag that will be added is a '#' followed by a number. For example:

-   If there are three isActive properties in the file, they will be identified as isActive, isActive#1, isActive#3.

## Structure

In the following example:

```
Document 1:
level 1 record 1 level 2 record 1.1 sub-level 3 record 1.1.1
level 1 record 1 level 2 record 1.1 sub-level 3 record 1.1.2
level 1 record 1 level 2 record 1.2 empty       empty

Document 2:
level 1 record 2 level 2 record 2.1 sub-level 3 record 2.1.1
```

-   Several records of nested information (nodes) can be imported. For example, level 1, level 2 and sub-level 3 will all be different nodes that can be imported in the same file.
-   The data in the property column that is parenthesized with “#” (which defines the level of a nested record) is not imported (only the data for the following properties are imported). This property column must not be empty to indicate that the line includes a corresponding nested record. A good practice might be to add a line number in this column in the data row.
-   If we have 2 independent collections at the same level, in the same line of the csv file, only one collection can contain data to import, other collection (and its sub-level) must be empty
-   Several rows at the same level can be imported. In the above example, in the first document, information is imported into nodes at the same level (1.1 and 1.2 - both at level 2).
-   When nested levels are defined, the lowest levels might not be present for a record. For example, in the first document, record 1.1 has information in the sub-level, but record 1.2 does not.
-   It is not possible to import a sub-level if all preceding levels are not imported. For example, it is not possible to import the record at level 1.1.1 if nothing was imported for level 1.1.
-   It is not mandatory to create templates with nested levels if the sub-levels are arrays of nullable references or optional references (even vital) to a node that allows a creation operation. For example, if an array of nullable references or optional references vital addresses is present on a given document, you can either import the document with nested addresses in a single file or import the document without any address information and have a second file to import the addresses. The difference is that in the second file you will need to give the complete key (document reference + address key), whereas, in the first document, the address key is necessary, but the document reference is implicit.
-   In creation mode, when fields are imported with no value (empty), the default will be imported (even if such fields are nullable)
-   In update mode, when fields are imported with no value (empty), if a field is nullable, the null will be imported otherwise, we raise an error. If the column is omitted in update mode, the value remains unchanged in the database.

# Example:

Suppose we want to import data for the node Order, its generated interface is:

```ts
export interface Order extends VitalClientNode {
    customer: Customer;
    orderNumber: string;
    orderDate: string;
    invoices: ClientCollection<Invoice>;
}

export interface Invoice extends VitalClientNode {
    customer: Customer;
    purchaseDate: string;
    lines: ClientCollection<InvoiceLine>;
    totalProductQty: integer;
    order: Order;
}

export interface InvoiceLine extends VitalClientNode {
    invoice: Invoice;
    orderQuantity: integer;
    comments: string;
    product: Product;
    netPrice: string;
}
```

A simplified presentation of the Order node is:

```yml
Orders:
    customer: Customer;
    orderDate: string;
    orderNumber: string;
    invoices:
        purchaseDate: string;
        totalProductQty: integer;
        order: Order;
        lines:
            orderQuantity: integer;
            product: Product;
            netPrice: string;
            comments: string;
```

The CSV header will be:

```yml
    customer(id);
    !orderNumber;
    *orderDate;
    #invoices;
    purchaseDate;
    ##lines;
    description;
    *orderQuantity;
    product(id);
    *netPrice;
    note
```

In this example, the following will be created in the system:

-   2 orders, one with 1 invoice containing 3 lines and the other with 2 invoices containing 2 lines.

And the following will be updated:

-   1 line in an existing order.

The CSV should be:

| customer(id)    | !orderNumber | orderDate  | #invoices | purchaseDate | ##lines | orderQuantity | product(name) | netPrice | comments    |
| --------------- | ------------ | ---------- | --------- | ------------ | ------- | ------------- | ------------- | -------- | ----------- |
| Phone Home      |              | 2022-02-01 | 1         | 2022-02-01   | 1       | 10            | IPhone 13     | 150      | Sliver      |
| Phone Home      |              | 2022-02-01 | 1         | 2022-02-01   | 2       | 100           | AC/DC Adapter | 10       | 220v input  |
| Phone Home      |              | 2022-02-01 | 1         | 2022-02-01   | 3       | 50            | Battery       | 30       |             |
| ABC Electronics |              | 2022-02-03 | 1         | 2022-02-03   | 1       | 100           | AC/DC Adapter | 150      | 110v output |
| ABC Electronics |              | 2022-02-03 | 1         | 2022-02-03   | 2       | 5             | Mouse         | 20       | Green       |
| ABC Electronics |              | 2022-02-03 | 2         | 2022-01-03   | 1       | 200           | AC/DC Adapter | 150      | 110v input  |
| ABC Electronics |              | 2022-02-03 | 2         | 2022-01-03   | 2       | 20            | Keyboard      | 50       | AZERTY      |
| Spacial Indus   | ABC123       | 2021-12-03 | 1         | 2021-12-03   | 4       | 12            | RAM Memory    | 90       | DDR3        |

Note: The above table should be an actual CSV file format with ';' separators, but to improve readability it was written in table format.

The generated array of objects is
N.B: this is internal.

```json
[
    {
        "customer": {
            "name": "Phone Home"
        },
        "orderDate": "2022-02-01",
        "invoices": [
            {
                "purchaseDate": "2022-02-01",
                "lines": [
                    {
                        "product": {
                            "name": "IPhone 13"
                        },
                        "orderQuantity": "10",
                        "netPrice": "150",
                        "comments": "Silver"
                    },
                    {
                        "product": {
                            "name": "AC/DC Adapter"
                        },
                        "orderQuantity": "100",
                        "netPrice": "10",
                        "comments": "220v input"
                    },
                    {
                        "product": {
                            "name": "Battery"
                        },
                        "orderQuantity": "50",
                        "netPrice": "30",
                        "comments": "Silver"
                    }
                ]
            }
        ]
    },
    {
        "customer": {
            "name": "ABC Electronics Home"
        },
        "orderDate": "2022-02-03",
        "invoices": [
            {
                "purchaseDate": "2022-01-03",
                "lines": [
                    {
                        "product": {
                            "name": "AC/DC Adapter"
                        },
                        "orderQuantity": "100",
                        "netPrice": "150",
                        "comments": "Silver"
                    },
                    {
                        "product": {
                            "name": "Mouse"
                        },
                        "orderQuantity": "5",
                        "netPrice": "150",
                        "comments": "Silver"
                    }
                ]
            },
            {
                "purchaseDate": "2022-02-03",
                "lines": [
                    {
                        "product": {
                            "name": "AC/DC Adapter"
                        },
                        "orderQuantity": "200",
                        "netPrice": "150",
                        "comments": "110v input"
                    },
                    {
                        "product": {
                            "name": "Keyboard"
                        },
                        "orderQuantity": "20",
                        "netPrice": "50",
                        "comments": "AZERTY"
                    }
                ]
            }
        ]
    },
    {
        "customer": {
            "name": "Phone Home"
        },
        "orderNumber": "ABC123",
        "orderDate": "2021-12-03",
        "invoices": [
            {
                "purchaseDate": "2012-12-03",
                "lines": [
                    {
                        "product": {
                            "name": "RAM Memory"
                        },
                        "orderQuantity": "12",
                        "netPrice": "90",
                        "note": "DDR3"
                    }
                ]
            }
        ]
    }
]
```
