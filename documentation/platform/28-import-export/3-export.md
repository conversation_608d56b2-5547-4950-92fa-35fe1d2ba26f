PATH: XTREEM/28+Import+Export/3+Export

To export data from a node, the user needs:

-   A template with `Export only` or `Import and export` template defined for the given node
-   The read access right on that node

For a given node, we can have multiple templates defined for various purposes. However, we can have only one default template for that node with `Export only` or `Import and export` flag. This template will be automatically selected in the dialog box if we have more than one template available for exporting data. If only one template is available, the application will skip the template selection step.

# Important point before starting

The process escapes values that start with =, +, -, @, \t, or \r with ' and defend against CSV injection attacks.

For example, we have a difference value of -3.14 then the export will have the output "'-3.14" instead of "-3.14". If you are reimporting exported data, the import process will detect automatically this escape character and mange it accordingly.

# How to export data from navigation

When an user lands to the navigation panel of a node and select some or all records displayed, the button `Export` will show up if there is at least on import export template with `Export only` or `Import and export` flag available for the current node:

![export-with-select](assets/images/export-with-select.png)

If there are more than one template, the application will ask the user :

![export-with-select-template](assets/images/export-with-select-template.png)

Once the template is set, the user click on `Confirm` to start the export:

![export-confirmation](assets/images/export-confirmation.png)

The export process is an asynchronous operation and the user can follow the progressing task in `Batch task history`

![export-batch-task-history](assets/images/export-batch-task-history.png)

If the export is processed successfully, there is a link to download the output file

![export-batch-task-history-result](assets/images/export-batch-task-history-result.png)

# Localized string management

We have a special treatment for the case of localized strings. Let's examine the case of item's description which is a localized string:

![item-description](assets/images/item-description.png)

In the screenshot, the locale is `base` as the default value, then when the export of items is executed, we will have `base` value of item's description exported

![item-description-result](assets/images/item-description-result.png)

If the locale is left empty, the fallback scheme is ordered as following:

-   The export template field locale
-   The user locale value
-   The base value.

If my locale is `fr`, I will have this result:

![item-description-result-fr](assets/images/item-description-result-fr.png)

If my locale is `en-US`, I will have this result:

![item-description-result-en-US](assets/images/item-description-result-en-US.png)

# Custom data management

In this paragraph, we discuss about custom fields created in `Routing` node and `Operation` node:

We have the following custom fields:

![routing-custom-fields](assets/images/routing-custom-fields.png)

On the page, we have:

![routing-custom-fields-page](assets/images/routing-custom-fields-page.png)

And the full template will be:

![routing-custom-fields-template](assets/images/routing-custom-fields-template.png)

The result will be:

![routing-custom-fields-export-result](assets/images/routing-custom-fields-export-result.png)

# Non-vital reference management

For a non-vital reference, the user can choose a property of that node. For example. in sales order template, the user can have, for the item on a sales order line, its name and description.

In the template header, we will have item.name and item.description.

# Execute an export with the global namespace

For now, we have the possibility to export data with the async mutation **exportByTemplateId**. For example, if we have a template named "UserTemplate" for the User node, we can export "User" data with :

```
mutation {
  global {
    exportByTemplateId {
      start(templateId: "UserTemplate", filter: "{}") {
        trackingId
      }
    }
  }
}
```

then we can track the progress with:

```
{
  global{
    exportByTemplateId{
      track(trackingId:"yourTrackingId") {
        status
        result
        errorMessage
      }
    }
  }
}
```

We can also export data from a template definition with the async mutation **exportByTemplateDefinition** :

```
mutation {
  global {
    exportByTemplateDefinition {
      start(
        templateDefinition: [{path: "id", title:"ID"},
          {path: "name", title:"Name"},
          {path: "description", title:"Desciption" },
          {path: "isActive",title:"Active"},
          {path: "_customData.exportTest",title:"Export Test"},
          {path: "legalCompany",title:"Legal Company"},
          {path: "legalCompany.name",  title:"legal Company name"},
          {path: "legalCompany.siren", title:"legal Company Siren"},
          {path: "primaryAddress", title:"primary Address"},
          {path: "financialSite", title:"financial Site"}]
        nodeName: "Site"
        outputFormat: "csv"
        filter: "{id:{_regex:'US',_options:'i'},_id:{_nin:[]}}"
      ) {
        trackingId
      }
    }
  }
}
```

where templateDefinition is an array of header definitions. Please note, for a header definition (a column in the output file), we need the following information:

```
HeaderDefinition {
    path: string; // the path to the column value, for example: description of the site or legalCompany.siren is the property siren of the legal company of the site
    title: string; // title of the column in output file
}
```

then we can track the progress with:

```
{
  global{
    exportByTemplateDefinition{
      track(trackingId:"yourTrackingId") {
        status
        result
        errorMessage
      }
    }
  }
}
```
