PATH: XTREEM/Server+Framework+Documentation/30+Customization+Framework

# Quick overview of the customization framework

The customization framework allows users to add custom fields to the application.

Custom fields are added to a page via a wizard.

On the server side, they are managed by the CustomField persistent node defined by the `@sage/xtrem-customization` package,
and stored in the custom_field table.

The UI obtains the list of custom fields for a page through the page metadata query (/metadata endpoint) that returns information about all the artifacts used by the page (bindings, menu items, icons, localized strings, ...).
The UI framework uses this metadata to add components for the custom fields at the time it builds the page contents.

The values of custom fields are stored in a `_customData` JSON system property (\_custom_data column), which exists in every Node except platform nodes.

The values of custom fields are queried and modified with the `_customData` property which is exposed in the GraphQL API.
The custom fields are _not_ exposed individually like normal fields in the GraphQL API.
