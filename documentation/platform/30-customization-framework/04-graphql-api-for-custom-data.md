PATH: XTREEM/Server+Framework+Documentation/30+Customization+Framework/04+graphql+api+for+custom+data

# GraphQL API for `_customData`

The values of custom fields are exposed as a `_customData` string field in the GraphQL API.

The value of the `_customData` field is JSON string payload, both in queries and in mutations.
See the examples below.

## Query example

Let us assume we have added two custom fields to the Item node:

-   `customComment`: a string
-   `customFlag` a boolean

We can query these fields with:

```graphql
query {
    xtremMasterData {
        item {
            read(id: "5") {
                id
                name
                _customData
            }
        }
    }
}
```

The response will be:

```json
{
    "data": {
        "xtremMasterData": {
            "item": {
                "read" {
                    "id": "5",
                    "name": "Cool item",
                    "_customData": "{\"customComment\":\"really cool\",\"customFlag\":true}"
                }
            }
        }
    }
}
```

In the response, `_customData` is a string. You must parse it with `JSON.parse` to get the values of the custom fields:

```json
{
    "customComment": "really cool",
    "customFlag": true
}
```

## Mutation example

Similarly, we can mutate the values of custom fields by including `_customData` in the mutation payload. For example:

```graphql
mutation {
    xtremMasterData {
        item {
            update(data: { id: "5", _customData: "{\"customFlag\":false}" }) {
                _id
                _customData
            }
        }
    }
}
```

The response will be:

```json
{
    "data": {
        "xtremMasterData": {
            "item": {
                "update" {
                    "id": "5",
                    "_customData": "{\"customComment\":\"really cool\",\"customFlag\":false}"
                }
            }
        }
    }
}

```

Notice that the \_customData input value is a JSON string.
Also, notice that the the `customComment` is still there in response.
The update merges (shallow merge) the new `_customData` with the old one; it does not replace it.

## `selector` parameter

You can tune pass a `selector` parameter to the `_customData` GraphQL field, to get a value for a single custom field instead of
getting the values for all of them. For example:

```graphql
query {
    xtremMasterData {
        item {
            read(id: "5") {
                id
                name
                _customData(selector: "customComment")
            }
        }
    }
}
```

The response will be:

```json
{
    "data": {
        "xtremMasterData": {
            "item": {
                "read" {
                    "id": "5",
                    "_customData": "\"really cool\""
                }
            }
        }
    }
}
```

Notice that the response field is still called `_customData` and that its value must still be parsed with `JSON.parse`.

You can improve the field naming with aliases:

```graphql
query {
    xtremMasterData {
        item {
            read(id: "5") {
                id
                name
                customComment: _customData(selector: "customComment")
                customFlag: _customData(selector: "customFlag")
            }
        }
    }
}
```

The response will become:

```json
{
    "data": {
        "xtremMasterData": {
            "item": {
                "read" {
                    "id": "5",
                    "customComment": "\"really cool\"",
                    "customFlag": "true"
                }
            }
        }
    }
}
```

Gotcha: the feature is not yet supported by the xtrem-client package.
You can use it in verbatim GraphQL queries but not (yet) in pages, with the `node.$.graph` API.

## Aggregate queries

The `selector` parameter allows you to execute aggregate queries.

Let us assume that we also two more custom fields on our `Item` node: `customDate` and a `customPrice`.
Then we can write aggregate queries.
For example:

```graphql
query {
    xtremMasterData {
        item {
            queryAggregate {
                edges {
                    node {
                        group {
                            customDate: _customData(selector: "customComment", by: month)
                        }
                        values {
                            customPrice: _customData(selector: "customPrice") {
                                min
                                max
                                sum
                            }
                        }
                    }
                }
            }
        }
    }
}
```

The response will be:

```json
{
    "data": {
        "xtremMasterData": {
            "item": {
                "queryAggregate" {
                    "edges": [{
                        "node": {
                            "group": {
                                "customDate": "\"2023-05-01\""
                            },
                            "values": {
                                "customPrice": { "min": "100", "max": "500", "sum": "2300" }
                            }
                        }
                    }, {
                        "node": {
                            "group": {
                                "customDate": "\"2023-06-01\""
                            },
                            "values": {
                                "customPrice": { "min": "80", "max": "700", "sum": "4700" }
                            }
                        }
                    }]
                }
            }
        }
    }
}
```
