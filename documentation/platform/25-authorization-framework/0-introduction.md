PATH: XTREEM/Server+Framework+Documentation/25+Authorization+framework

# Authorization framework

The goal of the Authorization framework is to allow role based access control to graphQL operations.

To achieve this, we start by defining activities. An activity is defined to expose relevant graphQL operations for
a Node, to which permissions can be granted via roles.

A role is defined as a collection of activities, with access to either all the exposed permissions per Activity or a
subset thereof. A role can also contain a collection of other roles.

The idea is to group all operations required to perform a given function together in a role. The role will consist of a
set of permissions from activities. Assigning this role to a user will result in granting all of its permissions.

More detail on roles, grouping of roles and filtering based on Site to come later.
