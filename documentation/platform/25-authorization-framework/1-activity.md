PATH: XTREEM/25+Authorization+framework/1+Activity

# Activity

An Activity exposes operations from a Node which should be assignable as permissions to users.

Developers define available authorizations by using instances of the `Activity` class. Activities are defined per
package in the lib/activities directory, each activity in its own file with a unique name.

An `Activity` specifies the Node on which it grants permissions and lists all operations that are available. The list of
permissions can include the basic operations provided by the framework (create, read, update, delete, lookup), custom mutations and queries on a Node, as well as permissions that are granted access to operations of nodes or permissions on activities. Grants to operations on other nodes and permissions on other activities are also possible via an Activity.

## Cascading permissions

The framework will derive implicit permissions from the explicit permissions defined in the Activity.
More specifically the 4 following rules apply:

1.  When a user is granted a permission on a node, they will also get the same permission on that node's vital references.
2.  Read rights are implied on a node if the user has rights to create, update and delete a node.
3.  Being granted create or update rights on a node results in being granted lookup rights on all of that node's references, vital or not. This ensures that a user can select values for the references during creation or update of the node.
4.  Read rights on a node imply lookup rights on that node.

Note that in the case of subnodes, rights do not automatically apply to the super or other subnodes of the same super class, unless explicitly granted.

## Examples of an Activity declaration:

```ts
export const salesOrderManagement = new Activity({
    description: 'Sales Order Management',
    node: () => xtremSales.nodes.SalesOrder,
    permissions: ['read','create','update','delete','report']
    operationGrants: {
                create: [{ operations: ['create'], on: [() => xtremSales.nodes.Customer] },],
                report: [{ operations: ['reportA', 'reportB'] },],
    },
    permissionGrants: {
                update: [{ permissions: ['create'], on: [() => xtremSales.sctivities.salesReturnManagement] },],
    }
});
```

```ts
export const salesReturnManagement = new Activity({
    description: 'Sales Return Management',
    node: () => xtremSales.nodes.SalesReturn,
    permissions: ['read','create','update','delete','report']
    operationGrants: {
                create: [{ operations: ['update', 'returnSlip'] },],
    },
});
```

In the example, the salesOrderManagement Activity explicitly makes the following operations available:

-   on SalesOrder: lookup, read, create, update, delete, report, reportA, reportB
-   on Customer: create, read, lookup
-   `create` on activity salesReturnManagement

The SalesOrder node has the following relationship:

-   a vital collection of SalesOrderLines
-   a non-vital reference to Customer
-   a non-vital reference to Site

Let's analyse how `create` permission cascades to other nodes.

-   Initially, the Activity explicitly states that granting `create` rights on SalesOrder will grant `create` rights on Customer.

```ts
create: [{ permissions: ['create'], on: () => [Customer] },],
```

-   Rule 1: granting `create` rights on SalesOrder will grant `create` rights on vital collection SalesOrderLine.

-   Rule 2: `create` rights imply `read` rights. All the nodes being granted `create` rights as a result of granting `create` rights to SalesOrder and SalesOrderLine will also be granted `read` rights.

-   Rule 3: `read` rights imply `lookup` rights to SalesOrder and SalesOrderLine.

-   Rule 4: `create` rights imply `lookup` rights on the node's references, here Site, Customer.

Therefore, with this example if create permission is allocated to a user, they should have access to the following operations:

-   `create` on SalesOrder, SalesOrderLine, Customer
-   `read` on SalesOrder, SalesOrderLine, Customer
-   `lookup` on SalesOrder, SalesOrderLine, Customer, Site

Now, let's analyse how `update` permission cascades to other nodes and activities.

For the `update` permission, the salesOrderManagement Activity explicitly makes the following operations available:

-   on SalesOrder: lookup, read, update,
-   on Customer: lookup
-   on Site: lookup

However, we see that under `permissionGrants`, `update` grants the `create` permission on the salesReturnManagement activity.

For the `create` permission, followings the rules laid out above, the salesReturnManagement Activity makes the following operations available:

-   on SalesReturn: create, update, read, lookup, returnSlip

Therefore, the `update` permission, the salesOrderManagement Activity will be

-   on SalesOrder: lookup, read, update,
-   on Customer: lookup
-   on Site: lookup
-   on SalesReturn: create, update, read, lookup, returnSlip

Due to the allocation of the permissions from the salesReturnManagement Activity.
