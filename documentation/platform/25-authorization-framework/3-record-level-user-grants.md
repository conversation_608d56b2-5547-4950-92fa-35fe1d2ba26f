PATH: XTREEM/25+Authorization+framework/3+Record+level+user+grants

# Record-level user grants

In some domains, you have to finely control who can access a given record.
This is particularly important in Human Resources modules.
For example, in a recruitment module, interviewers may be restricted to seeing only the profiles of the candidates
whom they are interviewing.

The authorization package provides a simple framework to support these scenarios:

-   The nodes to which access must be restricted to specific users must be subclassed from the `RestrictedNode` node.
-   Access to instances of such nodes can be granted or revoked with the `grantAccessToObjects` or `revokeAccessToObjects` methods of the user instance.

## Defining a node that restricts access via user grants

To restrict access to individual records to selected users you must subclass your node class from `RestrictedNode`:

```ts
import { decorators } from '@sage/xtrem-core';
import * as xtremAuthorization from '@sage/xtrem-authorization';

@decorators.subNode<CandidateProfile>({
    extends: () => xtremAuthorization.nodes.RestrictedNode,
    isPublished: true,
    canRead: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    // ...
})
export class CandidateProfile extends xtremAuthorization.nodes.RestrictedNode {
    // ...
}
```

This is all you have to do.

## Granting access to restricted node instances

To grant user access to one or several restricted node instances you call the `grantAccessToObjects` method of the user.
For example, assuming that `Interview`, `Candidate` and `CandidateProfile` are all subclasses of `RestrictedNode`:

```ts
@decorators.mutation<typeof Interview, 'assignInterviewerToInterview', {
    parameters: [
        { name: 'interview', type: 'reference', node: () => Interview }
        { name: 'interviewer', type: 'reference', node: () => User }
    ],
    return: 'boolean',
}>
static async assignInterviewerToInterview(context: Context, interview: Interview, interviewer: User): Promise<boolean> {
    // assign the interviewer to the interview
    await interview.$.set({ interviewer });
    await interview.$.save();

    const grantingEvent = `assignInterviewer/${interview._id}`;

    // grant read and update access to the interview
    await interviewer.grantAccessToObjects([
        interview,
    ], grantingEvent, ['update']);

    // grant read access to the interview's candidate and his/her profile
    await interviewer.grantAccessToObjects([
        interview.candidate
        interview.candidate.profile
    ], grantingEvent);

    return true;
}
```

The `grantingEvent` string qualifies the grant.
It will allow you to selectively revoke grants, as we will see below.

The third parameter is the list of rights that are granted. Three operations can be specified:

-   update: the record can be updated
-   delete: the record can be deleted
-   \*: is a shortcut for both update and delete.

The _read_ access is implicit when you call `grantAccessToObjects`. If you omit the third parameter only `read` will be granted.

## Revoking access to restricted node instances

To revoke user access to one or several restricted node instances you call the `revokeAccessToObjects` method of the user.
For example, you can revoke the grants that were given previously when we close the interview, with:

```ts
@decorators.mutation<typeof Interview, 'closeInterview', {
    parameters: [
        { name: 'interview', type: 'reference', node: () => Interview }
    ],
    return: 'boolean',
}>
static async closeInterview(context: Context, interview: Interview): Promise<boolean> {
    // use the same grantingEvent as in the assignInterviewerToInterview method.
    const grantingEvent = `assignInterviewer/${interview._id}`;

    // revoke access to the interview, its candidate and his/her profile
    await interview.interviewer.revokeAccessToObjects([
        interview,
        interview.candidate
        interview.candidate.profile
    ], grantingEvent);
    return true;
}
```

The `revokeAccessToObjects` method takes an optional third parameter, like `grantAccessToObjects`. This optional parameter is a list of rights that will be revoked. If you omit it, all rights (including reading) will be revoked.

Note also that, in the code above, the accesses have been revoked even though the interviewer remained assigned to the interview.
Instead, we could have set the interviewer to `null`; it all depends on the logic that we want to use in this process.

## What is the purpose of the _granting event_

The `grantingEvent` parameter allows you to selectively revoke grants.
This is useful when the same user is granted access multiple times to the same object, for different reasons.

For example, when a hiring manager is assigned to recruit for a vacancy, he will be granted access to the vacancy.
Then he will create interviews and will grant access to himself to these interviews, with a grant event `assignHiringManager/${interview._id}`.

The hiring manager may also interview some candidates himself. He will then assign himself to these interviews,
which will grant him access to the interviews with a grant event `assignInterviewer/${interview._id}`.

When the interview is closed, the hiring manager will lose the grant that was given to him
with `assignInterviewer/${interview._id}` but he will retain his other grant, so he will still have access to the interview.
His second grant may be revoked later, for example when the vacancy has been fulfilled.

## Automatic grants

The user who creates a restricted node instance is automatically granted full access ('\*') to the node instance.

It is nevertheless possible to transfer his/her rights to another user with a combination of `grantAccessToObjects` and `revokeAccessToObjects` calls.

## Querying the grants to an object or a user

You can obtain the grants that have been given on a restricted node instance with:

```ts
const grants = await interview.userGrants.toArray();
console.log(
    `Interview ${interview._id} was granted access to: ${grants
        .map(grant => `\n\t- ${grant.user.email} with granting events: ${Object.keys(grant.accessMap)}`)
        .join(', ')}`,
);
```

You can also obtain the grants that have been given to a user with:

```ts
const grants = await user.objectGrants.toArray();
console.log(
    `User ${user.email} was granted access to: ${grants
        .map(grant => `\n\t- ${grant.object.toString()} with granting events: ${Object.keys(grant.accessMap)}`)
        .join(', ')}`,
);
```

## Gotchas

If you use this feature, you will have to subclass all your restricted classes from `RestrictedNode`.

This complicates a bit the setup of your test data:

-   You will have to provide a `data/layers/test/restricted_node.csv` file with the `_id` and `_constructor` of all your test records.
-   The `_id` values of your test records must not overlap. A good practice will be to start at a different multiple of 100 in every restricted node. For example `100` in `interview.csv`, `200` in `candidate.csv`, ...
