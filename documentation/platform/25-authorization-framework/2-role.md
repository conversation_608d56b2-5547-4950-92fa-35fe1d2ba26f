PATH: XTREEM/25+Authorization+framework/2+Role

# Role

Roles are used to define the access rights for specific functional roles. This is achieved by granting rights on
activities as well as combining other roles. For each linked activity, the role can grant rights to all permissions
from the activity or a subset thereof.

## Role permissions

When resolving the access rights for a role, the framework combines all implied and cascaded permissions from the
activities for the permissions granted by the role and any linked roles.
