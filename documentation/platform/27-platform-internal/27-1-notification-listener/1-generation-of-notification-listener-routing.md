PATH: XTREEM/Server+Framework+Documentation/27-1+Notification+Listener/1+Generation+Of+Notification+Listener+Routing

# Generation of Notification Listener Routing Data

## Introduction

At build time, a file `xtrem-services-main/routing.json` is generated.
This file contains, for each package, the topics and queues of the notification listeners in this package.

```
{
  "@sage/xtrem-inventory": [
    {
      "topic": "MiscellaneousStockIssue/accountingInterface",
      "queue": "xtremFinanceData.queues.accountingInterfaceQueue"
    },
    {
      "topic": "MiscellaneousStockReceipt/accountingInterface",
      "queue": "xtremFinanceData.queues.accountingInterfaceQueue"
    },
    {
      "topic": "StockAdjustment/accountingInterface",
      "queue": "xtremFinanceData.queues.accountingInterfaceQueue"
    }
  ],
  "@sage/xtrem-sales": [
    {
      "topic": "SalesInvoice/accountingInterface",
      "queue": "xtremFinanceData.queues.accountingInterfaceQueue"
    }
  ],
  ...
}
```

## Technical details

The file `xtrem-services-main/routing.json` is generated from the information in the decorators `notificationListener` using a TypeScript transformer.
The transformer is in file `notification-listener-transformer.ts`.
The generation of the file occurs in two steps:

-   During the build of each package, generation of the routing files for each package containing listened topics and queues
-   During the build of the `xtrem-services-main` package, aggregation of the routing information for all packages in `xtrem-services-main/routing.json`
    The transformer only processes files in directory `'services`

## Configuration

The paths of the included source files can be configured in file `notification-listener-routing/config.ts`.
Partial paths listed in the `include` property are compared to the full path of the source files.
