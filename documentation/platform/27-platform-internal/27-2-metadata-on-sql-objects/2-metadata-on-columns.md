PATH: XTREEM/Server+Framework+Documentation/27-2+Metadata+On+Sql+Objects/2+Metadata+On+Columns

# Metadata on columns

## Format

The JSON stored in columns' comment follows the format of the `ColumnJsonComment` interface.

(see platform/back-end/xtrem-postgres/lib/json-comment.ts)

## Possible usages

The JSON comment contains an `isEncrypted` boolean attribute that tell whether the string stored in the column contains clear of encrypted column.

This attribute will be used by the upgrade engine to detect that the property bound to a column was moved from a classic string to an encrypted string: the upgrade engine will then be able to run an automatic action to encrypt all the existing values in the table.
