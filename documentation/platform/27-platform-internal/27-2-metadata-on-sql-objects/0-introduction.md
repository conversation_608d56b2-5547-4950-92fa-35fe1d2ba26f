PATH: XTREEM/Server+Framework+Documentation/27+Platform+Internal/27-2+Metadata+On+Sql+Objects

# Metadata on SQL objects

Postgres allow to define a comment on SQL objects (tables, columns, foreign keys, ...).

This comment is now used to store a JSON object that describes the SQL object.

# Usage

These comments are written every time a SQL object is created.
They will be used by the upgrade engine to detect the actions required to upgrade a SQL schema.

Some attributes can't be detected when reading the SQL schema on a object.
