PATH: XTREEM/Server+Framework+Documentation/31+TypeScript+to+SQL+Conversions

# Quick overview of the TypeScript to SQL conversion feature

The XTreeM framework includes a powerful converter capable of transforming TypeScript functions or expressions to SQL.

This patented feature allows a developer to write filtering conditions, sorting or aggregation criteria as TypeScript functions.
These functions are automatically converted to SQL so that queries can be handled efficiently by the database engine.

The developers do not have to master two languages (TypeScript and SQL), they do all their development in TypeScript and they
benefit from all the tooling and advanced typing of this language.

Security is also enhanced because the converter adds filters that guarantee tenant isolation and enforce access rights.

This feature is quite powerful but it is limited to a small subset of the TypeScript language, and it only gives access to
a limited API, mostly math, date and string functions.

Our linter plugin (eslint-plugin-xtrem) helps the developer by detecting functions that must be convertible to SQL but use
language features that are off-limits.

The subsections describe:

-   [where the TypeScript to SQL conversion is used](https://confluence.sage.com/display/XTREEM/01+Where+is+the+TS+to+SQL+Converter+used),
-   [the subset of the TypeScript language supported by the converter](https://confluence.sage.com/display/XTREEM/02+TS+to+SQL+Language+subset),
-   [the TypeScript APIs supported by the converter](https://confluence.sage.com/display/XTREEM/03+TS+to+SQL+APIs).
-   [transforming function calls](https://confluence.sage.com/display/XTREEM/04+TS+to+SQL+Transforming+function+calls).
-   [the tooling to support TypeScript to SQL conversion](https://confluence.sage.com/display/XTREEM/05+TS+to+SQL+Tooling).
-   [tips and tricks](https://confluence.sage.com/display/XTREEM/06+TS+to+SQL+Tips).
