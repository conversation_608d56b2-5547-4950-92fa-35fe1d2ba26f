PATH: XTREEM/Server+Framework+Documentation/31+TypeScript+to+SQL+Conversions/01+Where+is+the+TS+to+SQL+Converter+used

# Where is the TypeScript to SQL converter used?

## `getValue` rules of property decorators

Properties that carry a `getValue` decorator are not stored in SQL columns but they can be used to filter and sort queries,
and also in aggregates queries because the XTreeM SQL mapper can translate the `getValue` rules written as TypeScript functions to SQL.

## `join` decorator attributes of reference properties

The `join` decorator attribute is a variant of the `getValue` feature for references that can be naturally defined as joins.
These joins may contain functions that will be converted to SQL when used in filters, order by clauses or aggregate queries.

## `filter` and `orderBy` options of query APIs

The `context.query`, `context.select` and `context.queryAggregate` methods take `filter` and `orderBy` options.
These options can be specified as functions that will be converted to SQL by the framework.

## `defaultValue` rules of property decorators

This is similar to the `getValue` rule above but this feature is not used by queries.
It is used by the upgrade engine, to generate SQL scripts that fill the values of the new columns that are added by the upgrade.

## `_fn` key in GraphQL filters

The `_fn` key enables complex filters in GraphQL queries. The value of this key is the text (a string) of the body of a function
that can be converted to SQL.
This feature allows the consumers of the GraphQL API to pass complex filters, like filters that compare several properties of a node or that
call date or string functions.

See https://confluence.sage.com/XTREM/2+GraphQL+queries/8+Advanced+filtering+with+functions for details.
