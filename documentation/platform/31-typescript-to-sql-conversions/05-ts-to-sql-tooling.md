PATH: XTREEM/Server+Framework+Documentation/31+TypeScript+to+SQL+Conversions/05
+TS+to+SQL+Tooling

# TypeScript to SQL Tooling

## Build verification

The `getValue` / `computeValue` rules are checked during the build.

The build pass fails with an error if a property is incorrectly decorated with `getValue` instead of `computeValue`, or vice versa. **You must correct these errors** to fix the build.

## Lint verification

Our lint plugin also checks if `getValue` / `computeValue` rules can be converted to SQL.

This check is less reliable than the build check because the plugin only checks the syntax.

It frequently gives false positives on `computeValue` rules.
It often incorrectly reports that a `computeValue` rule can be converted to SQL when the rule cannot be converted because it references another property with a `computeValue` rule.

To fix this you should disable the rule on this specific property with an `eslint-disable-next-line` comment.
