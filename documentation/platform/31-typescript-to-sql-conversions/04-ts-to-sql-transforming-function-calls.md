PATH: XTREEM/Server+Framework+Documentation/31+TypeScript+to+SQL+Conversions/04+TS+to+SQL+Transforming+function+calls

# Transforming function calls to SQL

The TypeScript to SQL converter can transform functions that contain calls to other functions, provided that the called functions are convertible to SQL and have been registered to the converter.

## Registering functions to the converter

Functions are registered to the converter with `registerSqlFunction`. For example:

```ts
import { registerSqlFunction } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';

export async function getFinancialSite(site: xtremSystem.nodes.Site): Promise<xtremSystem.nodes.Site> {
    return (await site.isFinance) ? site : ((await site.financialSite) ?? site);
}

registerSqlFunction('xtremFinanceData.functions.getFinancialSite', getFinancialSite);
```

The build command will verify that registered functions can be converted to SQL.

## Calling a registered function from another function

Functions that call registered functions will be convertible to SQL, provided that satisfy the other requirements to be convertible.

The only new requirement is that the call use the **full name** under which the function was registered.

For example:

```ts
        async getValue() {
            return xtremFinanceData.functions.getFinancialSite(await this.stockSite);
        },
```
