PATH: XTREEM/Server+Framework+Documentation/31+TypeScript+to+SQL+Conversions/03+TS+to+SQL+APIs

## Decimal API

The converter supports the following API on decimals

```ts
Decimal.roundAt(this.value, 3);
```

Note: the `Decimal` API will be extended and you should use it rather than the `Math` API when working with decimal values.

## Math computations

The converter supports the following subset of the JavaScript `Math` API:

```ts
Math.floor(this.value);
Math.abs(this.value);
Math.round(this.value, 3);
// These can take any number of arguments (at least 1)
Math.max(this.value1, this.value2, ...);
Math.min(this.value1, this.value2, ...);
```

## Array API

For now, the array API is limited to the `includes` method:

```ts
// Converted to SQL `IN` operator
[2, 3].includes(this.p1);
```

## Collection API

### Collection Properties

-   `collection.length`

### Collection Methods

You can test collections with the following methods:

-   `collection.some(elt => filter_on_elt)`
-   `collection.every(elt => filter_on_elt)`
-   `collection.sum(elt => elt_expression)`
-   `collection.where(elt => elt_condition).sum(elt => elt_expression)`
-   `collection.takeOne(elt => elt_condition)`

Some examples:

```ts
// Does this (a document) have at least one line with an amount >= 1000?
this.lines.some(line => line.amount >= 1000);

// The sum of the document line amounts
this.lines.sum(line => line.amount);

// The sum of the amounts of the lines that have been delivered
this.lines.where(line => line.isDelivered).sum(line => line.amount);
```

## Date API

### Date Properties

You can use the following date properties in filters:

-   `value`
-   `epoch`
-   `year`
-   `month`
-   `day`
-   `week`
-   `weekDay`
-   `yearDay`

The `value` property is the internal value field of our `DateValue` objects. It packs the year, month and day components into a single integer, as `year * 10_000 + month * 100 + day`, which allows very fast extraction of the year, month and day components. If you use it in a filter it will be converted to a SQL expression that computes the same value.

The `epoch` property is the internal value of a SQL date. It is the number of seconds since Jan 1st, 1970. In the `DateValue` TypeScript class, it is implemented with a getter that computes it from the `value` field.

For efficiency, you should use `value` when manipulating dates in TypeScript and `epoch` in `_fn` filters
that are converted to SQL.

### Date Methods

You can use the following date methods in filters:

-   `d1.compare(d2)`
-   `d1.equals(d2)`
-   `d1.isBetween(d2, d3)`
-   `d.isLeapYear()`
-   `d.isWorkDay()`
-   `d.daysInMonth()`
-   `d.addYears(years)`
-   `d.addMonths(months)`
-   `d.addDays(days)`
-   `d.addWeeks(weeks)`
-   `d.begOfYear()`
-   `d.endOfYear()`
-   `d.begOfQuarter()`
-   `d.endOfQuarter()`
-   `d.begOfMonth()`
-   `d.endOfMonth()`
-   `d.begOfWeek()`
-   `d.endOfWeek()`

### Date static methods

You can use the following static methods in filters:

-   `date.today()`
-   `date.parse(str)`

## String API

### String properties

You can use the following string properties in filters:

-   `str.length`

### String methods

You can use the following string methods in filters:

-   `str.trim()`

## Regexp API

## Regexp methods

You can use the following regexp method in filters:

-   `/regexp/.test(str)`

## Miscellaneous

### Non-null assertions

You cannot use the TypeScript non-null assertion operator (postfix `!`) because SonarCloud rejects it.
But you can import the `notNull` function from `@sage/xtrem-core` to achieve the same result.
This function is supported by the converter:

```ts
// Equivalent to `this.resource!.capacity`
notNull(this.resource).capacity;
```
