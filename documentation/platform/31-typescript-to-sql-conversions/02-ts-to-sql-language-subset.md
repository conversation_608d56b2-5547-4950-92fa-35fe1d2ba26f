PATH: XTREEM/Server+Framework+Documentation/31+TypeScript+to+SQL+Conversions/02+TS+to+SQL+Language+subset

## Supported TypeScript to SQL conversions

### Statements

To be convertible to SQL, TypeScript functions can only use the following statements:

-   `const varName = initializer` declarations. The initializer of the variable must be convertible to SQL.
-   `if (test) blockOrStatement`. The test expression and the block or statement must be convertible to SQL. The `else` clause is not allowed.
-   `console.xyz(message, ...)`. These statements are only for debugging. They are ignored by the converter and should be removed after debugging.
-   `return expression`. The expression must be convertible to SQL. The function must end with a `return` statement and all its `if` blocks (if any) must also end with a `return` statement.

### Expressions

#### Literals

String, number, boolean, regexp and null literals are supported. For example:

-   `'Hello'`
-   `5`
-   `true`
-   `/foo|bar/`
-   `null`

### property access

Expressions can access properties of objects, at any level.

For example:

For example, on a property of the SalesOrder node:

```ts
getValue() {
    return this.billToAddress.country.code;
}
```

But you can also refer to a `const` variable. For example:

```ts
getValue() {
    const country = this.billToAddress.country;
    return country.code;
}
```

Note that these rules, as written above, will be rejected by the TypeScript compiler because properties return _promises_.
So you have to write them as:

```ts
async getValue() {
    const country = await (await this.billToAddress).country;
    return country.code;
}
```

The converter ignores the `await` keywords so this code will compile in TypeScript and will be convertible to SQL.

The `await` keyword will be omitted in the examples below, to keep them simple.

### Arithmetic operators

The converter supports the usual arithmetic operators:

```ts
this.capacity + 2;

this.capacity - 2;

this.capacity * 2;

this.capacity / 2;

// Unary minus
-this.capacity;

// More complex expressions
this.p1 + this.p2 - (this.p3 * this.p4) / this.p5;
```

### Comparisons operators

The converter supports the usual comparison operators:

```ts
this.name === 'abc';

this.name !== 'abc';

this.name == null;

this.name != null;

this.volume === this.quantity * this.unit.value;

this.volume !== this.quantity * this.unit.value;

this.volume < this.quantity * this.unit.value;

this.volume >= this.quantity * this.unit.value;

this.volume <= this.quantity * this.unit.value;

this.volume > this.quantity * this.unit.value;
```

### Logical operators

The converter supports the usual logical (boolean) operators:

```ts
// And
this.isActive && this.name == null;

// Or
this.isActive || this.name != null;

// Not
!this.isActive;

// More complex expressions
this.p1.p2.p3 || (this.p1.p2.p4 && this.p5);
```

### Conditional (ternary) operator

The conditional operator is also supported. For example:

```ts
this.p1 === 'yes' ? this.p2 : this.p3;

this.p2 !== 0 ? this.p1 / this.p2 : 0;
```

### Optional chaining and null coalescing

These TypeScript features are handy for dealing with nullable properties. They are fully supported by the converter:

```ts
// Optional chaining followed by null coalescing
this.resource?.name ?? null;
```

Notes:

-   The converter does not accept `undefined` types, so optional chaining must be used to replace `undefined` with another value (`null`, `''`)
-   The converter is strict on logical operators. It only accepts boolean operands. So `this.resource?.name || null` is not allowed as its first operand is a string (or `undefined`). You should use `this.resource?.name ?? null` instead.

### String concatenation

Strings can be concatenated with _template literals_, surrounded by backticks (`` ` ``).
For example:

```ts
`${this.firstName} ${this.lastName}: age ${this.age}`;
```

The `+` operator is supported for string concatenation but its use is discouraged (and rejected by our standard lint rules).
So you should use template literals.

### \_id shortcut

The `_id` property can be omitted in most contexts. For example:

```ts
this.billToAddress === this.shipToAddress;
// equivalent to
this.billToAddress._id === this.shipToAddress._id;
```

### Self-referencing

Self-referencing is used in `defaultValue` rules to solve an edge case, i.e.; the insertion of records that have a non-nullable property that may reference the node itself.

For example, if you have a `CyclicChainElement` node with a `next` property, the following _self-referencing_ rule will let you
create the first element of the list (that references itself):

```ts
defaultValue() {
    return this;
}
```
