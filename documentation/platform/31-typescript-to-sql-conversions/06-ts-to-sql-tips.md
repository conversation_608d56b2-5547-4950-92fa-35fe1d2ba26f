PATH: XTREEM/Server+Framework+Documentation/31+TypeScript+to+SQL+Conversions/06
+TS+to+SQL+Tips

# Tips and tricks for TypeScript to SQL conversions

## Complex expressions in GraphQL queries

Filters are passed as GraphQL strings containing valid JSON.
If you write a filter with the `_fn` operator you end up embedding JavaScript code inside JSON inside GraphQL.

This can quickly become very challenging because you have to write your JavaScript code on a single line, and JavaScript strings must be escaped twice.

Fortunately, XTreeM supports [JSON5](https://json5.org/), a human-friendly variant of JSON, and both GraphQL and JSON5 have a syntax for multiline strings. So you can write complex expressions on multiple lines, with little escaping.

Here is an example:

```graphql
{
    xtremSales {
        salesOrder {
            query(
                filter: """
                {
                   _fn: 'this.lines \
                   		.where(line => line.doNotShipBeforeDate <= this.orderDate.addDays(5)) \
                   		.sum(line => line.quantity) > 10' \
                }
                """
            ) {
                edges {
                    node {
                        number
                        orderDate
                    }
                }
            }
        }
    }
}
```

Two syntax tricks are used here:

- `"""` [to start and end a GraphQL string](https://spec.graphql.org/June2018/#sec-String-Value),
- `\` [to break a JSON5 string on multiple lines](https://json5.org/).
