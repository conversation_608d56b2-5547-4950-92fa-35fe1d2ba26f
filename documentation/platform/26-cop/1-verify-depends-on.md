PATH: XTREEM/26+Cop/1+Verify+depends-on

# Verify dependsOn

## What does Verify dependsOn validate?

The `verify dependsOn` validation will inspect all concrete (non-abstract) factories present in the application and provide a warning during the build process if additional property dependencies exist in the `collected dependency paths` that are not specified in the `dependsOn` node property attribute.

## What are collected dependency paths?

`Collected dependency path` is a private context property which, if initialized, will be updated when TypeScript is converted to SQL.

The path of the ts-to-sql conversion result is stored in `Collected dependency path`.

Ultimately, we want to be able to compare the `Collected dependency path` with the dependency array specified on the node property attribute `dependsOn` to determine if there are additional implied dependencies.

## Verify dependsOn usage

The `Verify DependsOn` validation will run by default as part of the CLI compile function but can be omitted by passing the `--skip-cop` argument:

```
pnpm run build --skip-cop
```
