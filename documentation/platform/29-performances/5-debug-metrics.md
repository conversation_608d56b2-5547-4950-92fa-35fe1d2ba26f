PATH: XTREEM/29+Performances/5+Debug+metrics
In addition to all the already defined performance counters, you can easily define you own counters.

# Create a new debug metric

Simply update you code and surround the piece of code you want to instrument with:

```typescript
DebugMetrics.withMetrics('myDomain', 'my<PERSON>ey', () => {
    // the part of the code you want to instrument
});
```

-   _myDomain_: counters are groupped by domains. _myDomain_ can be whatever string you want.
-   _myKey_: id of your counter, whithin the _myDomain_ group.

# Use your debug metric

Every debug metric is defined by a domain.

You have to enable all the domains you want to be exposed to _Promeheus_/_Grafana_. In your xtrem-config.yml file, add:

```yml
debugMetrics:
    myDomain: true
```

replace _myDomain_ with the domain you used to create your debug metric.

Note: debug metrics can only be used in _development_ mode (in your xtrem-config.yml file)

# Start you _Xtrem_ server

You will have to define an environment variable:

-   set the environment variable XTREM_ALL_METRICS_ENABLED to 1 if you want to see all the performance counters (the framework counter AND the debug metrics that have been enabled in your configuration file)
-   or set the environment variable XTREM_DEBUG_METRICS_ENABLED to 1 if you are only interrested in the debug metrics that have been enabled in your configuration file
-   make sure your are in _development_ mode
-   start your _Xtrem_ server:

```sh
XTREM_ALL_METRICS_ENABLED=1 pnpm run start
```

# In _Grafana_

-   navigate to _Grafana_ (localhost:3060)
-   open the _XTreem Dashboard_
-   enter the instrumented function (navigate in the Xtrem UI to run your piece of code)

Some seconds later, in the debug section, you will see your debug metrics:
![grafana-debug-metrics](assets/images/grafana-debug-metrics.png)

If nothing appears, please refer to this [Page](https://confluence.sage.com/display/XTREEM/4+Get+Started).
