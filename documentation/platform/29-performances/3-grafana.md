PATH: XTREEM/29+Performances/3+Grafana

# Presentation

_Grafana_ is an application that displays some performance counters.
It will periodically contact the _Prometheus_ server to get the data to display.

# Installation

Locally, you can use a Docker container.
Run the script `platform/performance-tests/grafana/install.sh` to get _<PERSON>ana_ installed.

The script will start a new Docker container named _grafana_. The container will accept requests on the port **3060**.

## Check if Grafanais running

Browse the url _localhost:3060_ and make sure UI opens.
