PATH: XTREEM/29+Performances/2+Prometheus

# Presentation

_Prometheus_ is a service that will periodically contact the Xtrem server to gather its performance counters.

# Installation

Locally, you can use a Docker container.
Run the script `platform/performance-tests/prometheus/install.sh` to get _Prometheus_ installed.

The script will start a new Docker container named _prometheus_. The container will accept requests on the port **9090**.

## Check if Prometheus is running

Browse the url _localhost:9090_ and make sure the UI opens.
