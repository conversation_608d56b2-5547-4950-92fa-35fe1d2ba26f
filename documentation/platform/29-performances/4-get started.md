PATH: XTREEM/29+Performances/4+Get+Started

# Get started

## Start Xtrem server

Define the environment variable _XTREM_ALL_METRICS_ENABLED=1_ and start your xtrem server

`XTREM_ALL_METRICS_ENABLED=1 pnpm run start`

If you don't define the environment variable, no data will be collected by Prometheus and so Graf<PERSON> will not display anything.

## Start Prometheus & Grafana servers

Make sure _Prometheus_ server is started, if not, please refer to this [Page](https://confluence.sage.com/display/XTREEM/2+Prometheus).

Make sure _Grafana_ server is started, if not, please refer to this [Page](https://confluence.sage.com/display/XTREEM/3+Grafana).

## Grafana

Open the _localhost:3060_ URL. The welcome page should appear:

![grafana-welcome-page](assets/images/grafana-welcome-page.png)

Click on _XTreem Dashboard_ to open it.

There are already a bunch of graphs created in the dashboard (GraphQl, SQL, Node.js, ...)
![grafana-nodejs-counters](assets/images/grafana-nodejs-counters.png)

# Troubleshooting

If _grafana_ does not display anything:

-   make sure the _XTREM_ALL_METRICS_ENABLED_ environment variable is set
-   make sure that both _Prometheus_ and _Grafana_ servers are started (see dedicated documentation)
-   make sure that _Prometheus_ periodically contacts your _Xtrem_ server. (In your config.yml file, you can define sage/xtrem-core/metrics logs domain to _verbose_ and check the logs: _Prometheus_ should contact your _Xtrem_ server every 5s: you will get a _Received HTTP GET on /metrics endpoint_ trace in your logs)
-   _Grafana_ may need to be refreshed: click on _Last 5 minutes_ in the upper right of the page:
    ![grafana-show-last-5-minutes](assets/images/grafana-show-last-5-minutes.png)
