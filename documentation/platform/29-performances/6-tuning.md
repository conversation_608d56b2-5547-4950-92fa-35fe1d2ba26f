PATH: XTREEM/29+Performances/6+Tuning

There are some config parameters that shoudl be tune to have the best performance.

# Database connections and request funnel size

Those 2 parameters are tightly coupled.

## Default config

```
server:
    requestFunnelSizeFactor: 1

storage:
  sql:
    max: 20
```

-   `storage.sql.max`: the DB pool size (max number of connections), default to 20.
-   `server.requestFunnelSizeFactor`: the factor to apply to the DB pool size to determine the max number of concurrent requests on /api and /metadata, default to 1.

With de default values we then allow 40 concurrent requests.

## Benchmarks

### Machine used for the benchmark

```
$ lscpu
Architecture:                       x86_64
CPU op-mode(s):                     32-bit, 64-bit
Byte Order:                         Little Endian
Address sizes:                      39 bits physical, 48 bits virtual
CPU(s):                             12
On-line CPU(s) list:                0-11
Thread(s) per core:                 2
Core(s) per socket:                 6
Socket(s):                          1
Vendor ID:                          GenuineIntel
CPU family:                         6
Model:                              165
Model name:                         Intel(R) Core(TM) i7-10850H CPU @ 2.70GHz
Stepping:                           2
CPU MHz:                            2711.712
```

```
free -m
              total        used        free      shared  buff/cache   available
Mem:          17989        6520         774         159       10693       10970
Swap:          5120        1346        3774
```

### Results for Artillery Scenario: sales-order/create

20 users with a ramp up of 5 during 120 seconds

| factor | run | success | failed | errors | rate | min  | max  | mean   | median | p95    | p99    |
| ------ | --- | ------- | ------ | ------ | ---- | ---- | ---- | ------ | ------ | ------ | ------ |
| 1      | 1   | 840     | 0      | 3      | 9    | 1383 | 3612 | 2608.8 | 2618.1 | 2951.9 | 3072.4 |
| 1      | 2   | 972     | 0      | 1      | 11   | 1210 | 3707 | 2199.2 | 2143.5 | 2836.2 | 3197.8 |
| 1      | 3   | 996     | 0      | 2      | 11   | 776  | 3172 | 2100.9 | 2101.1 | 2465.6 | 2618.1 |
| 1.5    | 1   | 690     | 0      | 1      | 9    | 1541 | 4389 | 3196.9 | 3197.8 | 3678.4 | 3828.5 |
| 1.5    | 2   | 757     | 0      | 1      | 9    | 1328 | 4321 | 2899.5 | 2893.5 | 3395.5 | 3605.5 |
| 1.5    | 3   | 809     | 0      | 7      | 9    | 1493 | 3975 | 2717.5 | 2725   | 3134.5 | 3464.1 |
| 2      | 1   | 828     | 0      | 2      | 9    | 1318 | 3856 | 2638.5 | 2671   | 3011.6 | 3262.4 |
| 2      | 2   | 963     | 0      | ;      | 11   | 987  | 2906 | 2198.5 | 2186.8 | 2515.5 | 2671   |
| 2      | 3   | 741     | 0      | ;      | 5    | 1179 | 3795 | 2932.5 | 2951.9 | 3395.5 | 3605.5 |
| 2.5    | 1   | 700     | 0      | 5      | 9    | 1709 | 4913 | 3162.8 | 3197.8 | 3605.5 | 3828.5 |
| 2.5    | 2   | 986     | 0      | 3      | 9    | 984  | 2968 | 2126.9 | 2101.1 | 2515.5 | 2671   |
| 2.5    | 3   | 751     | 0      | 1      | 9    | 1257 | 3694 | 2944.3 | 2951.9 | 3328.3 | 3464.1 |

![request-factor-bench](assets/images/request-factor-bench-sales-orders-create-20-5-120.png)

### Conclusion

The best factor is 1 for 20 max SQL connections
