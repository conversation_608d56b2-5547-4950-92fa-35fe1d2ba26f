PATH: XTREEM/23+Security+And+Monitoring+Tools/8+Too+Busy

# Too Busy

## What is Too Busy?

The Too Busy service aims to monitor the node.js events loop. If this service detects some lags greater than the threshold the server will return a 503 HTTP response with a `Retry-After` header to indicate the client should not retry the request before that time.

## Configuration of the monitoring

All configuration keys are optional and the default will be applied if it is not specified.

```yml
loginUrl: https://login.dev-sagextrem.com/
redirectUrl: https://cluster-a.dev-sagextrem.com/home
jwksUrl: https://login.dev-sagextrem.com/.well-known/jwks.json
issuer: login.dev-sagextrem.com
audience: cluster-a.dev-sagextrem.com

services:
    tooBusy:
        disabled: false
        maxLagInMillis: 70
        intervalInMillis: 500
        smoothingFactor: 0.3333
        retryAfterInSeconds: 5
```

**maxLagInMillis** The max latency threshold, if it goes over the threshold, the more likely the process will be considered too busy and it a 503 will be returned. (default: 3000)

**intervalInMillis** The current check interval, a lower interval makes it more sensitive. (default: 1000)

**smoothingFactor** The smoothing factor per the standard exponential smoothing formula `αtn + (1-α)tn-1`. See [Exponential smoothing](https://en.wikipedia.org/wiki/Exponential_smoothing). You probably don't want to set that value and keep the default. (default: 0.333333....)

**retryAfterInSeconds** The number of seconds set in the `Retry-After`HTTP header. (default: 5)

**disabled** Disable the events loop monitoring (default: false)
