PATH: XTREEM/23+Security+And+Monitoring+Tools/7+TLS+Layer

# TLS

## What is TLS?

[TLS](https://en.wikipedia.org/wiki/Transport_Layer_Security) offers the encryption layer for securing communication between a client and a server. This security is based on the usage of certificates and private keys.

## Configuration for outgoing requests

When a client does a request against a server, it gets its certificate during the handshake, this certificate is either issued by a well-known CA (Certificate Authority) or could be issued by a self-signed authority in the case of internal usage. Node.js trusts the well-known CAs curated by [Mozilla](https://hg.mozilla.org/mozilla-central/raw-file/tip/security/nss/lib/ckfw/builtins/certdata.txt) at the time the binary has been built.

If the certificate is not issued by one of these well-known CA or if it is self-signed the request will fail except if you explicitly trust it. To do so, edit the `xtrem-security.yml` files and add or edit the `tls` section as follows:

```yml
loginUrl: https://login.dev-sagextrem.com/
redirectUrl: https://cluster-a.dev-sagextrem.com/home
jwksUrl: https://login.dev-sagextrem.com/.well-known/jwks.json
issuer: login.dev-sagextrem.com
audience: cluster-a.dev-sagextrem.com

tls:
    extraCaFiles:
        - '/path/to/first/ssl/caCert.pem'
        - '/path/to/second/ssl/caCert.pem'
```

**extraCaFiles** is an array of paths to the trusted CA (in [PEM](https://en.wikipedia.org/wiki/Privacy-Enhanced_Mail) format) to add.

## Configuration of HTTPS service

The Express routes can be served in HTTPS mode. This is configured by editing the `xtrem-config.yml` file and adding the following section:

```yaml
server:
    port: 8443
    ssl:
        cert: 'path/to/ssl/server/cert.pem'
        key: 'path/to/ssl/server/key.pem'
```

**port** set the listening port. The `port` parameter at the root has been deprecated in favor of this one.
**ssl** is an object used to configure the path to the certificate and key file. Other node.js [TLS options](https://nodejs.org/docs/latest-v14.x/api/all.html#tls_tls_createsecurecontext_options) can be set. **cert** and **key** can also be the PEM content as a string.

### Listening port

The common port number for HTTPS is 443. On Linux systems, non-root users cannot start a process to listen to ports lower than 1024. You can grant a specific process using the command:

```sh
sudo setcap CAP_NET_BIND_SERVICE=+eip /path/to/binary
```
