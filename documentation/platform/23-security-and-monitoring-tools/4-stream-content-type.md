PATH: XTREEM/23+Security+And+Monitoring+Tools/4+Stream+Content+Types

1 - Global configuration
The allowed text and binary stream content types by the application have to be defined in xtrem-config.xml

```yml
   ...,
   textStreamContentTypes: ['application/json', 'text/xml', 'text/plain'],
   binaryStreamContentTypes: ['image/jpg', 'application/octet-stream']
   ...
```

Wildcard character can be used in the definition of content types.

To allow all the images and text content types:

```yml
   ...,
   textStreamContentTypes: ['text/*'],
   binaryStreamContentTypes: ['image/*']
   ...
```

1 - Local configuration
The allowed content types in each textStream or binaryStream field have to be defined in allowedContentTypes option of the dataType.

for TextStream:

```ts
    @decorators.textStreamProperty<TestDatatypes, 'textStream'>({
        isPublished: true,
        isStored: true,
        dataType: () =>
            new TextStreamDataType({ maxLength: 1234, allowedContentTypes: ['text/xml', 'text/html', 'text/plain'] }),
    })
    readonly textStream: Promise<TextStream>;
```

for BinaryStream

```ts
    @decorators.binaryStreamProperty<TestDatatypes, 'binaryStream'>({
        isPublished: true,
        isStored: true,
        dataType: () =>
            new BinaryStreamDataType({ maxLength: 1234, allowedContentTypes: ['image/*'] }),
    })
    readonly binaryStream: Promise<BinaryStream>;
```
