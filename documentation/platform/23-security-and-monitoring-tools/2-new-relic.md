PATH: XTREEM/23+Security+And+Monitoring+Tools/2+New+Relic

# New Relic

## What is New Relic?

New Relic is an observability platform that helps monitoring systems.

Several systems are able to push data to New Relic and node.js application can easily do it using a [dedicated agent](https://docs.newrelic.com/docs/agents/nodejs-agent/installation-configuration/nodejs-agent-configuration/).

You can find more details by reading [Introduction to New Relic](https://docs.newrelic.com/docs/using-new-relic/welcome-new-relic/get-started/introduction-new-relic/)

## How to configure and enable it?

To configure New Relic, you need to create a `newrelic.js` file and to put it in your application folder.

The config file is like the following:

```js
'use strict';

exports.config = {
    /**
     * Array of application names.
     */
    app_name: ['sage-my-xtrem'],
    /**
     * Your New Relic license key.
     */
    license_key: 'fake1b9e3831420f5ddc46ea7e74bede9562fake',
};
```

As soon you have have a valid `newrelic.js` file, the New Relic agent will automatically be loaded by the `xtrem` cli when invoking the `start` command.

**Note** New Relic requires to have an account on https://newrelic.com/ to be able to get a valid `newrelic.js` config file and monitor the created applications.
