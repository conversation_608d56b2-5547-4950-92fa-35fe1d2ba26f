PATH: XTREEM/23+Security+And+Monitoring+Tools/3+Health+Checks

# Xtreem health endpoints

The Xtreem server provides endpoints to indicate the current status of the API server. This page describes these API endpoints and explains how you can use them.

## Liveness endpoint

Xtreem provides an endpoint ping to indicate the current status of the server. Machines that check the liveness of the server should rely on the HTTP status code. A status code 200 indicates the Xtreem server is alive.

## Readiness endpoint

Xtreem provides an endpoint ready to indicate the current status of the server. Machines that check the readiness of the server should rely on the HTTP status code:

-   a status code 502 indicates that the Xtreem server is being started.
-   a status code 200 indicates that the Xtreem server is ready.
