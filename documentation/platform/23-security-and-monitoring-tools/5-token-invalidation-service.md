PATH: XTREEM/23+Security+And+Monitoring+Tools/5+Token+Invalidation+Service

# Token Invalidation Service

## What is the Token Invalidation Service?

The Token Invalidation Service is responsible for periodically collecting (every 15 seconds) the access tokens that were invalidated for a given cluster. Each token has a `jti` unique identifier that is stored in a DynamoDB database table when the user logged out from a cluster.

This service is started when a GraphQL service is started and a login service is properly configured.

## Configuration

This service does not need special configuration, it relies on the **production** configuration of the `xtrem-config.yml` and `xtrem-security.yml` files.

The only specific configuration related to this service is if you want to disable it for any reason. In that case, you need to edit the `xtrem-security.yml` to add the following:

```yml
services:
    tokenInvalidation:
        active: false
```
