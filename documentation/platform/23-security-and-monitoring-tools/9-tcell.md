PATH: XTREEM/23+Security+And+Monitoring+Tools/9+tCell

# tCell

**Note**: tCell supersedes S<PERSON>reen tool but has been removed starting from Release 37.0

## What is tCell?

tCell by Rapid7 is an Application Security Management software that enables you to protect your applications, microservices, and APIs from malicious attacks.

tCell uses multiple layers to monitor the application, this includes:

-   Real-Time Application Monitoring
-   In-App Web Application Firewall (In-App WAF)
-   Runtime Application Self-Protection (RASP)

## How to configure and enable it?

To configure tCell, you need to create a `tcell_agent.config` configuration file and put it in the root of the application folder.

The tCell file is like the following:

```json
{
    "version": 1,
    "applications": [
        {
            "app_id": "your-tcell-app-id",
            "api_key": null,
            "tcell_api_url": "https://eu.agent.tcell.insight.rapid7.com/api/v1",
            "tcell_input_url": "https://eu.input.tcell.insight.rapid7.com/api/v1",
            "js_agent_api_base_url": "https://eu.agent.tcell.insight.rapid7.com/api/v1"
        }
    ]
}
```

Then to enable the loading of that config, the environment variable `TCELL_ENABLED` must be set to `true` or `1`.

**Note**: If for some reason you need to put the config file at a different location, then set the environment variable `TCELL_AGENT_CONFIG` to that file path.

```sh
export TCELL_ENABLED=true
export TCELL_AGENT_CONFIG=path/to/a/non/app/root/config/file
```

As soon you have a valid `tcell_agent.config` file and set the `TCELL_ENABLED` environment variable (and if necessary `TCELL_AGENT_CONFIG`), tCell will be automatically loaded by the `xtrem` CLI when invoking the `start` command.

**Note** tCell requires to have an account on https://eu.tcell.insight.rapid7.com/ to be able to get a valid `app_id` and `api_key` and to monitor the created applications.

## For further details

-   [tCell by Rapid7](https://www.rapid7.com/products/tcell/)
-   [Node.js agent installation documentation](https://docs.rapid7.com/tcell/nodejs-agent-install)
