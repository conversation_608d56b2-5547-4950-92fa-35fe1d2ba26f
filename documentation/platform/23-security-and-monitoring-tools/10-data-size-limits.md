PATH: XTREEM/23+Security+And+Monitoring+Tools/10+Data+Size+Limits

## Data Size Limits

## Configuration of the Data Size Limits (sizeLimits)

All configuration keys are optional and the default will be applied if it is not specified.

```yml
loginUrl: https://login.dev-sagextrem.com/
redirectUrl: https://cluster-a.dev-sagextrem.com/home
jwksUrl: https://login.dev-sagextrem.com/.well-known/jwks.json
issuer: login.dev-sagextrem.com
audience: cluster-a.dev-sagextrem.com

sizeLimits:
    maxStreamSize: 32Mb
    maxRequestSize: 100Mb
    maxUploadSize: 1Gb
```

**maxStreamSize** The maximum size that can be stored on text or binary stream property. The default value is `32Mb`.

**maxRequestSize** The maximum size of an HTTP request to the application. The default value is `100Mb`. If this is execeeded, code 413 is returned.

**maxUploadSize** The maximum size of files that can be uploaded by the user to S3. The default value is `1Gb`.
