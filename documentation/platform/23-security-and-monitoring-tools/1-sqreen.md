PATH: XTREEM/23+Security+And+Monitoring+Tools/1+<PERSON><PERSON>reen

# Sqreen

**Note**: sqreen has been replaced by tCell

## What is S<PERSON>reen?

Sqreen is Application Security Management software that enables you to protect your applications, microservices, and APIs from malicious attacks.

<PERSON><PERSON><PERSON> uses multiple layers to monitor the application, this includes:

-   In-App Web Application Firewall (In-App WAF)
-   In-App WAF custom rules
-   Security Headers rules
-   Content Security Policy
-   Runtime Application Self-Protection (RASP)
-   Core Protection and Smart Stack Detection

More details on [Protect your app with <PERSON><PERSON>reen](https://docs.sqreen.com/protection/introduction/)

## How to configure and enable it?

To configure <PERSON>qreen, you need to create a `sqreen.json` file and to put it in your application folder.

The sqreen file is like the following:

```json
{
    "app_name": "sage-my-xtrem",
    "token": "fake2177cc7b5f261bc4c4152e9bbe8804c163f84237daa3e5defake"
}
```

Then to enable the loading of that config the environment variable `SQREEN_DISABLE` must be set to `false`.

```sh
export SQREEN_DISABLE=false
```

As soon you have have a valid `sqreen.json` file and a set the `SQREEN_DISABLE` environment variable, Sqreen will be automacally loaded by the `xtrem` cli when invoking the `start` command.

**Note** Sqreen requires to have a account on https://my.sqreen.com/ to be able to get a valid `sqreen.json` file and to monitor the created applications.
