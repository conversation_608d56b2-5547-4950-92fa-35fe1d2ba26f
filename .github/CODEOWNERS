# This is a comment.

# Each line is a file pattern followed by one or more owners.

# see https://docs.github.com/en/free-pro-team@latest/github/creating-cloning-and-archiving-repositories/about-code-owners

# Current teams are:
    # Platform teams are: @Sage-ERP-X3/basalt @Sage-ERP-X3/bauxite @Sage-ERP-X3/atp
    # Sim applicative teams are: @Sage-ERP-X3/sim-contributors @Sage-ERP-X3/sim-stock-engine @Sage-ERP-X3/sim-accounting-engine @Sage-ERP-X3/sim-integration @Sage-ERP-X3/sim-compliance-service @Sage-ERP-X3/sim-experts
    # X3 applicative teams are: @Sage-ERP-X3/xtrem-x3 @Sage-ERP-X3/xtrem-x3-services @Sage-ERP-X3/xtrem-x3-finance @Sage-ERP-X3/xtrem-x3-distribution @Sage-ERP-X3/xtrem-x3-manufacturing
    # WH applicative teams are:  @Sage-ERP-X3/xtrem-geode
    # Glossary applicative teams are: @Sage-ERP-X3/xtrem-glossary
    # Documentation teams are: @Sage-ERP-X3/platform-doc-reviewer @Sage-ERP-X3/services-doc-reviewer
    # terminology teams are: @Sage-ERP-X3/xtrem-terminology

# These owners will be the default owners for everything in the repo.

# Unless a later match takes precedence.

*           @Sage-ERP-X3/basalt

# Order is important; the last matching pattern takes the most precedence.

########### Platform rules

/platform/front-end/xtrem-cli/ @Sage-ERP-X3/basalt @Sage-ERP-X3/bauxite
/platform/cli/xtrem-documentation/ @Sage-ERP-X3/basalt @Sage-ERP-X3/bauxite
/platform/front-end/xtrem-graphql-demo-page/ @Sage-ERP-X3/bauxite
/platform/shared/xtrem-i18n/ @Sage-ERP-X3/bauxite
/platform/shared/xtrem-shared/ @Sage-ERP-X3/basalt @Sage-ERP-X3/bauxite
/platform/show-case/xtrem-show-case/ @Sage-ERP-X3/basalt @Sage-ERP-X3/bauxite @Sage-ERP-X3/atp
/platform/show-case/xtrem-show-case-bundle/ @Sage-ERP-X3/basalt @Sage-ERP-X3/bauxite
/platform/show-case/xtrem-restaurant @Sage-ERP-X3/bauxite
/platform/front-end/xtrem-standalone/ @Sage-ERP-X3/bauxite
/platform/system/xtrem-system/ @Sage-ERP-X3/basalt @Sage-ERP-X3/bauxite
/platform/front-end/xtrem-ui/ @Sage-ERP-X3/bauxite
/platform/shared/xtrem-filter-utils/ @Sage-ERP-X3/bauxite
/platform/front-end/xtrem-ui-plugin-graphiql/ @Sage-ERP-X3/bauxite
/platform/front-end/xtrem-ui-plugin-monaco/ @Sage-ERP-X3/bauxite
/platform/front-end/xtrem-ui-plugin-pdf/ @Sage-ERP-X3/bauxite
/platform/front-end/xtrem-ui-components/ @Sage-ERP-X3/bauxite
/platform/front-end/xtrem-document-editor/ @Sage-ERP-X3/bauxite
/platform/system/xtrem-reporting @Sage-ERP-X3/basalt @Sage-ERP-X3/bauxite

    # Feature files rules
/platform/*/*/test/cucumber/ @Sage-ERP-X3/atp @Sage-ERP-X3/basalt @Sage-ERP-X3/bauxite

########### X3 rules

/x3-services/shared @Sage-ERP-X3/xtrem-x3
/x3-services/applications @Sage-ERP-X3/xtrem-x3

    # shared packages
/x3-services/shared/x3-finance-data @Sage-ERP-X3/xtrem-x3-finance
/x3-services/shared/x3-invoicing-data @Sage-ERP-X3/xtrem-x3-finance @Sage-ERP-X3/xtrem-x3-distribution
/x3-services/shared/x3-manufacturing-data @Sage-ERP-X3/xtrem-x3-manufacturing
/x3-services/shared/x3-master-data @Sage-ERP-X3/xtrem-x3
/x3-services/shared/x3-physical-flows-data @Sage-ERP-X3/xtrem-x3-finance @Sage-ERP-X3/xtrem-x3-distribution
/x3-services/shared/x3-project-management-data @Sage-ERP-X3/xtrem-x3-distribution
/x3-services/shared/x3-purchasing-data @Sage-ERP-X3/xtrem-x3-distribution
/x3-services/shared/x3-sales-data @Sage-ERP-X3/xtrem-x3-distribution
/x3-services/shared/x3-stock @Sage-ERP-X3/xtrem-x3
/x3-services/shared/x3-structure @Sage-ERP-X3/xtrem-x3

    # applications packages
/x3-services/applications/x3-finance @Sage-ERP-X3/xtrem-x3-finance
/x3-services/applications/x3-inventory @Sage-ERP-X3/xtrem-x3-distribution
/x3-services/applications/x3-manufacturing @Sage-ERP-X3/xtrem-x3-manufacturing
/x3-services/applications/x3-project-management @Sage-ERP-X3/xtrem-x3-distribution
/x3-services/applications/x3-project-management-manufacturing @Sage-ERP-X3/xtrem-x3-distribution @Sage-ERP-X3/xtrem-x3-manufacturing
/x3-services/applications/x3-purchasing @Sage-ERP-X3/xtrem-x3-distribution
/x3-services/applications/x3-purchasing-finance @Sage-ERP-X3/xtrem-x3-distribution @Sage-ERP-X3/xtrem-x3-finance
/x3-services/applications/x3-purchasing-manufacturing @Sage-ERP-X3/xtrem-x3-distribution @Sage-ERP-X3/xtrem-x3-manufacturing
/x3-services/applications/x3-purchasing-sales @Sage-ERP-X3/xtrem-x3-distribution
/x3-services/applications/x3-sales @Sage-ERP-X3/xtrem-x3-distribution
/x3-services/applications/x3-sales-finance @Sage-ERP-X3/xtrem-x3-distribution @Sage-ERP-X3/xtrem-x3-finance
/x3-services/applications/x3-sales-inventory @Sage-ERP-X3/xtrem-x3-distribution
/x3-services/applications/x3-sales-manufacturing @Sage-ERP-X3/xtrem-x3-distribution @Sage-ERP-X3/xtrem-x3-manufacturing
/x3-services/applications/x3-sales-project-management @Sage-ERP-X3/xtrem-x3-distribution



    # misc. packages
/x3-services/functional-tests  @Sage-ERP-X3/xtrem-x3 @Sage-ERP-X3/atp

    # x3 services
/x3-services @Sage-ERP-X3/xtrem-x3-services @Sage-ERP-X3/xtrem-x3 @Sage-ERP-X3/basalt
/x3-services/functional-tests  @Sage-ERP-X3/xtrem-x3-services @Sage-ERP-X3/xtrem-x3 @Sage-ERP-X3/atp
/x3-services/cli @Sage-ERP-X3/xtrem-x3-services @Sage-ERP-X3/basalt
/x3-services/platform/xtrem-x3-adc-ui @Sage-ERP-X3/bauxite

    # wh services
/wh-services  @Sage-ERP-X3/xtrem-geode @Sage-ERP-X3/basalt
/wh-services/functional-tests  @Sage-ERP-X3/xtrem-geode @Sage-ERP-X3/atp

########### Tools rules

/tools/glossary @Sage-ERP-X3/xtrem-glossary

    # Feature Files rules - glossary
/tools/glossary/*/test/cucumber @Sage-ERP-X3/xtrem-glossary @Sage-ERP-X3/atp

########### Doc reviewer

/documentation/platform @Sage-ERP-X3/platform-doc-reviewer
/documentation/services @Sage-ERP-X3/services-doc-reviewer

########### ATP Documentation

/documentation/services/methodology/functional-tests-methodology @Sage-ERP-X3/atp
/documentation/services/methodology/integration-tests-methodology @Sage-ERP-X3/atp
/documentation/services/methodology/smoke-tests-methodology @Sage-ERP-X3/atp
/documentation/services/standards/tests-standards @Sage-ERP-X3/atp

########### Repository scripts

/scripts @Sage-ERP-X3/basalt @Sage-ERP-X3/bauxite
/pipelines @Sage-ERP-X3/basalt @Sage-ERP-X3/bauxite
/docker @Sage-ERP-X3/basalt @Sage-ERP-X3/bauxite
/lerna.json @Sage-ERP-X3/basalt @Sage-ERP-X3/bauxite
/package.json @Sage-ERP-X3/basalt @Sage-ERP-X3/bauxite
/pnpm-lock.yaml @Sage-ERP-X3/basalt @Sage-ERP-X3/bauxite
/pipelines/cucumber-test/ @Sage-ERP-X3/atp
/pipelines/cucumber-test/templates/ @Sage-ERP-X3/atp
/pipelines/devops/ @Sage-ERP-X3/atp
/docker/test-image @Sage-ERP-X3/basalt @Sage-ERP-X3/bauxite @Sage-ERP-X3/sim-experts
/run-test-image.sh @Sage-ERP-X3/basalt @Sage-ERP-X3/bauxite @Sage-ERP-X3/sim-experts
/pipelines/azure-test-image.yml @Sage-ERP-X3/basalt @Sage-ERP-X3/bauxite @Sage-ERP-X3/sim-experts
/xtrem-config-test-image.yml @Sage-ERP-X3/basalt @Sage-ERP-X3/bauxite @Sage-ERP-X3/sim-experts
/xtrem-security-test-image.yml @Sage-ERP-X3/basalt @Sage-ERP-X3/bauxite @Sage-ERP-X3/sim-experts
/run-test-image.sh @Sage-ERP-X3/basalt @Sage-ERP-X3/bauxite @Sage-ERP-X3/sim-experts

########### ATP specific files
/scripts/allure/ @Sage-ERP-X3/atp @Sage-ERP-X3/basalt @Sage-ERP-X3/bauxite
/platform/cli/xtrem-cli-dev/lib/commands/handlers/test/step-definitions/ @Sage-ERP-X3/atp @Sage-ERP-X3/basalt @Sage-ERP-X3/bauxite
/platform/cli/xtrem-cli-atp/ @Sage-ERP-X3/atp @Sage-ERP-X3/basalt @Sage-ERP-X3/bauxite
/documentation/platform/12-test-framework/2-integration-tests/3-available-cucumber-steps.md @Sage-ERP-X3/atp @Sage-ERP-X3/basalt @Sage-ERP-X3/bauxite
/.vscode/*.code-snippets @Sage-ERP-X3/atp

########### SIM Rules

/services @Sage-ERP-X3/sim-contributors @Sage-ERP-X3/basalt @Sage-ERP-X3/bauxite

    # SIM Engines

        # SIM stock-engine
/services/shared/xtrem-stock-data/lib/classes @Sage-ERP-X3/sim-stock-engine @Sage-ERP-X3/sim-experts @Sage-ERP-X3/basalt
/services/shared/xtrem-stock-data/lib/functions @Sage-ERP-X3/sim-stock-engine @Sage-ERP-X3/sim-experts @Sage-ERP-X3/basalt
/services/shared/xtrem-stock-data/test/mocha/functions @Sage-ERP-X3/sim-stock-engine @Sage-ERP-X3/sim-experts @Sage-ERP-X3/basalt
/services/applications/xtrem-stock/lib/functions @Sage-ERP-X3/sim-stock-engine @Sage-ERP-X3/sim-experts @Sage-ERP-X3/basalt
/services/applications/xtrem-stock/lib/classes @Sage-ERP-X3/sim-stock-engine @Sage-ERP-X3/sim-experts @Sage-ERP-X3/basalt
/services/applications/xtrem-stock/lib/nodes/stock-update-listener.ts @Sage-ERP-X3/sim-stock-engine @Sage-ERP-X3/sim-experts @Sage-ERP-X3/basalt
/services/applications/xtrem-stock/test/mocha/functions @Sage-ERP-X3/sim-stock-engine @Sage-ERP-X3/sim-experts @Sage-ERP-X3/basalt

        # SIM accounting-engine
/services/shared/xtrem-finance-data/lib/functions @Sage-ERP-X3/sim-accounting-engine @Sage-ERP-X3/sim-experts @Sage-ERP-X3/basalt
/services/shared/xtrem-finance-data/test/mocha/functions @Sage-ERP-X3/sim-accounting-engine @Sage-ERP-X3/sim-experts @Sage-ERP-X3/basalt
/services/applications/xtrem-finance/lib/classes @Sage-ERP-X3/sim-accounting-engine @Sage-ERP-X3/sim-experts @Sage-ERP-X3/basalt
/services/applications/xtrem-finance/lib/functions @Sage-ERP-X3/sim-accounting-engine @Sage-ERP-X3/sim-experts @Sage-ERP-X3/basalt
/services/applications/xtrem-finance/lib/nodes/accounting-interface-listener.ts @Sage-ERP-X3/sim-accounting-engine @Sage-ERP-X3/sim-experts @Sage-ERP-X3/basalt
/services/applications/xtrem-finance/test/mocha/functions @Sage-ERP-X3/sim-accounting-engine @Sage-ERP-X3/sim-experts @Sage-ERP-X3/basalt
/services/applications/xtrem-finance/test/mocha/nodes/accounting-interface-listener.ts @Sage-ERP-X3/sim-accounting-engine @Sage-ERP-X3/sim-experts @Sage-ERP-X3/basalt

        # SIM integration-engine
/services/adapters/xtrem-frp-1000* @Sage-ERP-X3/sim-integration @Sage-ERP-X3/sim-experts @Sage-ERP-X3/basalt @Sage-ERP-X3/bauxite
/services/adapters/xtrem-Intacct-gateway @Sage-ERP-X3/sim-integration @Sage-ERP-X3/sim-experts @Sage-ERP-X3/basalt @Sage-ERP-X3/bauxite
/services/adapters/xtrem-Intacct @Sage-ERP-X3/sim-integration @Sage-ERP-X3/sim-experts @Sage-ERP-X3/basalt @Sage-ERP-X3/bauxite

        # SIM avalara-engine
/services/adapters/xtrem-avalara-gateway @Sage-ERP-X3/sim-compliance-service @Sage-ERP-X3/sim-experts @Sage-ERP-X3/basalt @Sage-ERP-X3/bauxite

        # SIM allocation-engine
        # TODO: add rules here (using @Sage-ERP-X3/sim-allocation-engine)

        # SIM functional-tests packages
services/functional-tests @Sage-ERP-X3/sim-contributors @Sage-ERP-X3/atp @Sage-ERP-X3/bauxite

        # SIM smoke-tests-minimum packages
services/smoke-tests-minimum @Sage-ERP-X3/sim-contributors @Sage-ERP-X3/atp @Sage-ERP-X3/bauxite

        # SIM Feature Files rules - service - applications packages
/services/applications/*/test/cucumber/ @Sage-ERP-X3/sim-contributors @Sage-ERP-X3/atp @Sage-ERP-X3/bauxite

        # SIM Feature Files rules - service - shared packages
/services/shared/*/test/cucumber/ @Sage-ERP-X3/sim-contributors @Sage-ERP-X3/atp @Sage-ERP-X3/bauxite

        # SIM Feature Files rules - service - adapters packages
/services/adapters/*/test/cucumber/ @Sage-ERP-X3/sim-compliance-service @Sage-ERP-X3/sim-experts @Sage-ERP-X3/atp @Sage-ERP-X3/basalt @Sage-ERP-X3/bauxite

########### Terminology Rules

/*/*/*/lib/i18n @Sage-ERP-X3/xtrem-terminology @Sage-ERP-X3/basalt
/platform/front-end/*/lib/i18n @Sage-ERP-X3/xtrem-terminology @Sage-ERP-X3/bauxite
/platform/show-case/*/lib/i18n @Sage-ERP-X3/xtrem-terminology @Sage-ERP-X3/bauxite


showcase-stock/*/*/lib/i18n @Sage-ERP-X3/xtrem-terminology @Sage-ERP-X3/sim-experts @Sage-ERP-X3/basalt @Sage-ERP-X3/bauxite
showcase-sales/*/*/lib/i18n @Sage-ERP-X3/xtrem-terminology @Sage-ERP-X3/sim-experts @Sage-ERP-X3/basalt @Sage-ERP-X3/bauxite

/services/*/*/lib/i18n @Sage-ERP-X3/xtrem-terminology @Sage-ERP-X3/sim-experts @Sage-ERP-X3/basalt
/shopfloor/*/*/lib/i18n @Sage-ERP-X3/xtrem-terminology @Sage-ERP-X3/sim-experts @Sage-ERP-X3/basalt

/tools/*/*/lib/i18n @Sage-ERP-X3/xtrem-terminology @Sage-ERP-X3/xtrem-glossary @Sage-ERP-X3/basalt

/x3/*/*/lib/i18n @Sage-ERP-X3/xtrem-terminology @Sage-ERP-X3/xtrem-x3-services @Sage-ERP-X3/basalt
/x3-services/*/*/lib/i18n @Sage-ERP-X3/xtrem-terminology @Sage-ERP-X3/xtrem-x3-services @Sage-ERP-X3/basalt
/wh-services/*/*/lib/i18n @Sage-ERP-X3/xtrem-terminology @Sage-ERP-X3/xtrem-x3-services @Sage-ERP-X3/basalt

########### System Rules

    # applicative
/platform/system/xtrem-system/lib/events @Sage-ERP-X3/sim-experts @Sage-ERP-X3/xtrem-glossary @Sage-ERP-X3/basalt
/platform/system/xtrem-system/lib/functions @Sage-ERP-X3/sim-experts @Sage-ERP-X3/xtrem-glossary @Sage-ERP-X3/basalt
/platform/system/xtrem-system/lib/client-functions @Sage-ERP-X3/sim-experts @Sage-ERP-X3/xtrem-glossary @Sage-ERP-X3/basalt @Sage-ERP-X3/bauxite
/platform/system/xtrem-system/lib/nodes/company.ts @Sage-ERP-X3/sim-experts @Sage-ERP-X3/xtrem-glossary @Sage-ERP-X3/basalt
/platform/system/xtrem-system/lib/nodes/site.ts @Sage-ERP-X3/sim-experts @Sage-ERP-X3/xtrem-glossary @Sage-ERP-X3/basalt

    # shared
/platform/system/xtrem-system/data @Sage-ERP-X3/sim-experts @Sage-ERP-X3/xtrem-glossary @Sage-ERP-X3/basalt @Sage-ERP-X3/bauxite
/platform/system/xtrem-system/api @Sage-ERP-X3/sim-experts @Sage-ERP-X3/xtrem-glossary @Sage-ERP-X3/basalt @Sage-ERP-X3/bauxite
/platform/system/xtrem-system/test @Sage-ERP-X3/sim-experts @Sage-ERP-X3/xtrem-glossary @Sage-ERP-X3/basalt @Sage-ERP-X3/bauxite
/platform/system/xtrem-authorization @Sage-ERP-X3/sim-experts @Sage-ERP-X3/basalt @Sage-ERP-X3/bauxite
/platform/system/xtrem-reporting/data @Sage-ERP-X3/sim-experts @Sage-ERP-X3/xtrem-distribution @Sage-ERP-X3/basalt @Sage-ERP-X3/bauxite
/platform/system/xtrem-mailer/data/ @Sage-ERP-X3/sim-experts @Sage-ERP-X3/xtrem-distribution @Sage-ERP-X3/basalt @Sage-ERP-X3/bauxite

########### SQL

/services/*/*/sql @Sage-ERP-X3/basalt
/tools/*/*/sql @Sage-ERP-X3/basalt

########## Shopfloor tracking application

/shopfloor @Sage-ERP-X3/xtrem-x3-manufacturing @Sage-ERP-X3/xtrem-x3-distribution @Sage-ERP-X3/sim-experts
/shopfloor/*/*/test/cucumber @Sage-ERP-X3/xtrem-x3-manufacturing @Sage-ERP-X3/xtrem-x3-distribution @Sage-ERP-X3/sim-experts @Sage-ERP-X3/atp
/shopfloor/functional-tests @Sage-ERP-X3/xtrem-x3-manufacturing @Sage-ERP-X3/xtrem-x3-distribution @Sage-ERP-X3/sim-experts @Sage-ERP-X3/atp

########## Showcase applications

/showcase-stock @Sage-ERP-X3/xtrem-x3-manufacturing @Sage-ERP-X3/xtrem-x3-distribution @Sage-ERP-X3/sim-experts @Sage-ERP-X3/basalt @Sage-ERP-X3/sim-contributors
/showcase-sales @Sage-ERP-X3/xtrem-x3-manufacturing @Sage-ERP-X3/xtrem-x3-distribution @Sage-ERP-X3/sim-experts @Sage-ERP-X3/basalt @Sage-ERP-X3/sim-contributors

########## Codespaces

/.devcontainer @Sage-ERP-X3/sim-experts @Sage-ERP-X3/basalt @Sage-ERP-X3/bauxite @Sage-ERP-X3/atp
/.github/workflows/codespace.yml @Sage-ERP-X3/sim-experts @Sage-ERP-X3/basalt @Sage-ERP-X3/bauxite @Sage-ERP-X3/atp
