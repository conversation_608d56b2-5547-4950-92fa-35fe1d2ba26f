name: Build and Publish Devcontainer
on:
  workflow_dispatch:
    inputs:
      image-tag:
        type: choice
        description: Build a normal (latest) image or a test image (during dev)
        options:
          - latest
          - test
          - custom
        default: latest
      manual-image-tag:
        type: string
        description: The custom tag to use for the image (only if "custom" is selected)
        required: false
  schedule:
    - cron: '0 6 * * 1' # At 6:00am on Monday
  push:
    branches:
      - 'release/*'

jobs:
  build_and_publish:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker
        uses: docker/setup-buildx-action@v3

      - name: Log in to GitHub Packages
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Compute image and branch names
        run: |
          REPO_NAME=$(echo "${{ github.repository }}" | tr '[:upper:]' '[:lower:]')
          IMAGE_TAG=${{ github.event.inputs.image-tag }}

          if [ "$IMAGE_TAG" == "custom" ]; then
            if [ -n "${{ github.event.inputs.manual-image-tag }}" ]; then
              IMAGE_TAG=${{ github.event.inputs.manual-image-tag }}
            else
              unset IMAGE_TAG
            fi
          fi

          BRANCH_NAME=${GITHUB_REF#refs/heads/}
          echo "BRANCH_NAME=$BRANCH_NAME" >> $GITHUB_ENV

          if [ -z "$IMAGE_TAG" ] || [ "$IMAGE_TAG" == "latest" ]; then
            if [[ "$BRANCH_NAME" == release/* ]]; then
              IMAGE_TAG=$(echo "$BRANCH_NAME" | tr '/' '-')
            fi
          fi

          IMAGE_NAME=ghcr.io/$REPO_NAME/base-devcontainer:${IMAGE_TAG:-latest}
          echo "IMAGE_NAME=$IMAGE_NAME" >> $GITHUB_ENV

      - name: Pull cache image
        continue-on-error: true
        run: docker pull $IMAGE_NAME

      - name: Build Devcontainer
        run: |
          echo '${{ toJson(secrets) }}' > secrets.json
          npm install -g @devcontainers/cli
          devcontainer up --config .devcontainer/src/devcontainer.json --prebuild --workspace-folder $(pwd) --remove-existing-container --id-label devcontainer.prebuild=xtrem-base --secrets-file secrets.json
          devcontainer run-user-commands --config .devcontainer/src/devcontainer.json --workspace-folder $(pwd) --id-label devcontainer.prebuild=xtrem-base --secrets-file secrets.json
          rm secrets.json

      - name: Update image field in devcontainer.json files
        if: startsWith(env.BRANCH_NAME, 'release/')
        run: |
          find .devcontainer -type f -name 'devcontainer.json' ! -path '.devcontainer/src/*' | while read -r file; do
            jq --arg image-name "$IMAGE_NAME" '.image = $image-name' "$file" > tmp.$$.json && mv tmp.$$.json "$file"
            git add "$file"
          done
          if ! git diff --exit-code; then
            echo "REQUIRES_GIT_PUSH=true" >> $GITHUB_ENV
          fi

      - name: Remove pre-build user commands
        run: |
          docker commit $(docker ps --filter "label=devcontainer.prebuild=xtrem-base" --format "{{.ID}}") tmp-base-devcontainer

          image_name="tmp-base-devcontainer:latest"

          current_labels=$(docker inspect ${image_name} --format '{{ json .Config.Labels }}')

          JS=$(cat << 'EOF'
            const currentLabels = JSON.parse(require('fs').readFileSync(0, 'utf-8'));
            const currentMetadata = JSON.parse(currentLabels['devcontainer.metadata']);
            const modifiedMetadata = currentMetadata.map(meta => {
              const { onCreateCommand, postCreateCommand, ...rest } = meta;
              return rest;
            });
            currentLabels['devcontainer.metadata'] = JSON.stringify(modifiedMetadata);
            currentLabels['devcontainer.metadata'];
          EOF
          )

          updated_metadata=$(echo "$current_labels" | node -p "$JS")

          # Set the Dockerfile content as a string
          dockerfile_content=$(cat <<-EOF
            FROM ${image_name}
          EOF
          )

          temp_image=$(echo "$dockerfile_content" | docker build -t "${{ env.IMAGE_NAME }}" --no-cache --label devcontainer.metadata="$updated_metadata" -q -)

      - name: Push Docker Image
        run: |
          docker push ${{ env.IMAGE_NAME }}

      - name: Commit and push changes
        if: startsWith(env.BRANCH_NAME, 'release/') && env.REQUIRES_GIT_PUSH == 'true'
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "Clément Pont"
          find .devcontainer -type f -name 'devcontainer.json' ! -path '.devcontainer/src/*' | while read -r file; do
            git add "$file"
          done
          git commit -m "Update image field in devcontainer.json files"
          git push
