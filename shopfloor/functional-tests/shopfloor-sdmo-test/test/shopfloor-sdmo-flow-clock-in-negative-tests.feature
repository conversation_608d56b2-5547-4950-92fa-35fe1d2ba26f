@shopfloor_sdmo

Feature: shopfloor-sdmo-flow-clock-in-negative-tests

    Scenario: Clock in/out: non existing Operator

        Given the user opens the application on a HD desktop
        Then the "Shopfloor Control main dashboard" titled dashboard is displayed

        #Refresh work orders widget
        And the user selects the "work orders" titled table widget field in the dashboard
        And the user clicks the "Refresh" more actions button in the header of the table widget field

        #Clock in
        And the user clicks the "Clock in" button of the table widget field
        #Select operator modal
        And the user selects the "Operator" labelled reference field on a modal
        When the user clicks the lookup button of the reference field

        And the user selects the "operator" bound table field on a modal
        #Operator doesn't exist
        And the user filters the "ID" labelled column in the table field with value "242424"
        #Checks empty
        And the table field is empty

        And the user clicks the Close button of the dialog on the main page

    Scenario: Clock in/out: non existing Sites

        Given the user opens the application on a HD desktop
        Then the "Shopfloor Control main dashboard" titled dashboard is displayed

        #Refresh work orders widget
        And the user selects the "work orders" titled table widget field in the dashboard
        And the user clicks the "Refresh" more actions button in the header of the table widget field

        #Clock in
        And the user clicks the "Clock in" button of the table widget field
        #Moves to Site modal
        And the user selects the "Site" labelled reference field on a modal
        When the user clicks the lookup button of the reference field
        #Selects production site modal and searches for site
        And the user selects the "productionSite" bound table field on a modal

        And the user filters the "ID" labelled column in the table field with value "M-DNE"
        #Checks empty
        And the table field is empty

        And the user clicks the Close button of the dialog on the main page

    Scenario: Clock in/out: Operator mandatory fields

        Given the user opens the application on a HD desktop
        Then the "Shopfloor Control main dashboard" titled dashboard is displayed

        #Refresh work orders widget
        And the user selects the "work orders" titled table widget field in the dashboard
        And the user clicks the "Refresh" more actions button in the header of the table widget field

        #Clock in
        And the user clicks the "Clock in" button of the table widget field

        #Operator Mandatory field
        And the user selects the "Site" labelled reference field on a modal
        When the user clicks the lookup button of the reference field
        #Selects production site modal and searches for site
        And the user selects the "productionSite" bound table field on a modal

        #Filters with value
        And the user filters the "ID" labelled column in the table field with value "501"
        And the user selects the row with text "501" in the "ID" labelled column header of the table field
        And the user clicks the "ID" labelled nested field of the selected row in the table field

        And the user clicks the "OK" labelled business action button on a modal

        Then a error toast containing text "You need to select or enter a value." is displayed

    Scenario: Clock in/out: Both mandatory fields

        Given the user opens the application on a HD desktop
        Then the "Shopfloor Control main dashboard" titled dashboard is displayed

        #Refresh work orders widget
        And the user selects the "work orders" titled table widget field in the dashboard
        And the user clicks the "Refresh" more actions button in the header of the table widget field

        #Clock in
        And the user clicks the "Clock in" button of the table widget field

        #Both Mandatory fields
        And the user clicks the "OK" labelled business action button on a modal
        Then a error toast containing text "You need to select or enter a value. You need to select or enter a value." is displayed
        Then the user clicks the "Cancel" labelled business action button on a modal
