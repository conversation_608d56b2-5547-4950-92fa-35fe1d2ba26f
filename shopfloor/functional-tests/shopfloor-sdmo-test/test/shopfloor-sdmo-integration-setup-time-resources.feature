@shopfloor_sdmo @integration
## Test setup time tracking from Resource Widget
Feature: shopfloor-sdmo-integration-setup-time-resources

    Scenario: Setup time for Resources

        Given the user opens the application on a HD desktop
        Then the "Shopfloor Control main dashboard" titled dashboard is displayed

        #Refresh work orders widget
        And the user selects the "Resources" titled table widget field in the dashboard
        And the user clicks the "Refresh" more actions button in the header of the table widget field

        #Clock in
        And the user clicks the "Clock in" button of the table widget field

        And the user selects the "Operator" labelled reference field on a modal
        And the user writes "<PERSON>" in the reference field
        And the user selects "<PERSON> Vieira" in the reference field

        And the user selects the "Site" labelled reference field on a modal
        And the value of the reference field is "Sandfeld"

        And the user clicks the "OK" labelled business action button on a modal

        Given the "Shopfloor Control main dashboard" titled dashboard is displayed

        #Get WO number
        And the user selects the "Work orders" titled table widget field in the dashboard

        And the user selects the "month" period type toggle button in the table widget
        And the user selects the "April 2025" date period in the table widget
        And takes a screenshot

        And the user selects the card 1 of the table widget field
        Then the value of the row 1 on the left of the card of the table widget field is "Shopfloor_W005"
        And the user stores the value of the row 1 on the left of the card of the table widget with key "[ENV_WORK_ORDER]"

        #Select Resource
        And the user selects the "Resources" titled table widget field in the dashboard

        #Select required period
        And the user selects the "month" period type toggle button in the table widget
        And the user selects the "April 2025" date period in the table widget
        And takes a screenshot

        #Select required card
        And the user selects the card 1 of the table widget field
        Then the value of the row 1 on the left of the card of the table widget field is "ETIQ1512-1550"
        And the user stores the value of the row 1 on the left of the card of the table widget with key "[ENV_RESOURCE]"
        And the user clicks the link of the row 1 on the left of the card of the table widget field
        And the user waits 5 seconds

        #Control the static-content field
        And the user selects the "Period" labelled static-content field on the main page
        Then the value of the static-content field is "April 2025"
        And takes a screenshot

        #Starting the operation
        And the user selects the "lines" bound table field on the main page
        And the value of the "operation.description" bound nested text field of the card 1 in the table field is "Labour"
        And the value of the "computedProgressStatus" bound nested label field of the card 1 in the table field is "Off"

        #And the value of the "workOrder" bound nested reference field of the card 5 in the table field is ""
        And the value of the "itemName" bound nested label field of the card 1 in the table field is "Shopfloor_W005 - Supply order planning Manufactured Item 001"

        And the user clicks the "Start setup time" inline action button of the card 1 in the table field
        #Check status = Setup started
        And the value of the "computedProgressStatus" bound nested label field of the card 1 in the table field is "Setup started"

        And the user clicks the "Open line panel" inline action button of the card 1 in the table field
        And the user waits 22 seconds

        #Using hardcoded value
        And the user selects the "Running time" labelled text field on the sidebar
        Then the time elapsed in the text field is greater than "00:00:20"

        #using stored value
        And the user stores the value of the text field with the key "[ENV_TIME_ELAPSED]"
        And takes a screenshot
        And the user waits 5 seconds
        And the time elapsed in the text field is greater than "[ENV_TIME_ELAPSED]"
        And takes a screenshot

        #Close the sidebar
        And the user clicks the Close button of the sidebar

        And the user selects the "lines" bound table field on the main page
        And the user clicks the "Pause" inline action button of the card 1 in the table field
        #Check status = Paused
        And the value of the "computedProgressStatus" bound nested label field of the card 1 in the table field is "Setup paused"

        And the user clicks the "Done" inline action button of the card 1 in the table field
        #Check status = Off

        And the value of the "computedProgressStatus" bound nested label field of the card 1 in the table field is "Off"

    Scenario: Clock out

        Given the user opens the application on a HD desktop
        Then the "Shopfloor Control main dashboard" titled dashboard is displayed

        And the user selects the "Resources" titled table widget field in the dashboard
        And the user clicks the "Clock out" button of the table widget field
        Then a toast with text "Operator clocked out successfully" is displayed

    Scenario: Verify data integration into SDMO for Resources

        Given the user opens the application on a HD desktop using the following link: "explorer/"
        And the user writes "./shopfloor-sdmo-flow-time-check-query.graphql" GraphQL request
        And the user clicks the "Run" button in the GraphQL page header
        And the user attaches the actual GraphQL response to allure report

        # full response validation will be possible only if the data doesn't change from one execution to another
        # For instance the start run can count down can be slightly different and generate different value.
        # And the "./shopfloor-x3-flow-template-response.json" GraphQL response is valid

        And the user selects the "data.shopfloorMasterData.workOrder.query.edges[1].node" GraphQL property
        And the selected GraphQL property value contains
            """
           "id": "[ENV_WORK_ORDER]",
            "status": "open",
            "progressStatus": "off",
            "startDate": "2025-04-02",
            "endDate": "2025-04-04",
            """

        And the user selects the "data.shopfloorMasterData.workOrder.query.edges[1].node.startDate" GraphQL property
        Then the selected GraphQL property value contains "2025"

        And the user selects the "data.shopfloorMasterData.workOrder.query.edges[1].node.id" GraphQL property
        Then the selected GraphQL property value is "[ENV_WORK_ORDER]"

        And the user selects the "data.shopfloorMasterData.workOrder.query.edges[1].node.status" GraphQL property

        Then the selected GraphQL property value is not "closed"

        And the user selects the "data.shopfloorMasterData.workOrder.query.edges[1].node.operations.query.edges[1].node.actualSetupTime" GraphQL property
        Then the selected GraphQL property value is greater than "0.008"

        Then the selected GraphQL property value is lower than "0.012"
