@shopfloor_sdmo
## Test clock in and clock out
Feature: shopfloor-sdmo-flow-clock-in-clock-out

    Scenario: Clock-in / clock out work order widget

        Given the user opens the application on a HD desktop
        Then the "Shopfloor Control main dashboard" titled dashboard is displayed

        #Refresh work orders widget
        And the user selects the "work orders" titled table widget field in the dashboard
        And the user clicks the "Refresh" more actions button in the header of the table widget field

        #Added wait time to give chance to sync data
        And the user waits for 10 seconds

        #Clock in
        And the user clicks the "Clock in" button of the table widget field

        #Moves to Site modal
        And the user selects the "Site" labelled reference field on a modal
        When the user clicks the lookup button of the reference field
        #Selects production site modal and searches for site
        And the user selects the "productionSite" bound table field on a modal

        And the user filters the "ID" labelled column in the table field with value "D1S"
        And the user selects the row with text "D1S" in the "ID" labelled column header of the table field
        And the user clicks the "ID" labelled nested field of the selected row in the table field

        #Select operator modal
        And the user selects the "Operator" labelled reference field on a modal
        When the user clicks the lookup button of the reference field

        And the user selects the "operator" bound table field on a modal

        And the user filters the "ID" labelled column in the table field with value "VVR"
        And the user selects the row with text "VVR" in the "ID" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field

        And the user clicks the "OK" labelled business action button on a modal

        And the user selects the "month" period type toggle button in the table widget
        And the user selects the "April 2025" date period in the table widget
        #Check to see we can see data once clocked in
        And the user selects the card 1 of the table widget field
        Then the value of the row 1 on the left of the card of the table widget field is "Shopfloor_W005"

        #Clock out
        Given the "Shopfloor Control main dashboard" titled dashboard is displayed
        And the user clicks the "Clock out" button of the table widget field
        Then a toast with text "Operator clocked out successfully" is displayed

    Scenario: Clock-in / clock out resources widget

        Given the user opens the application on a HD desktop
        Then the "Shopfloor Control main dashboard" titled dashboard is displayed

        #Refresh resources widget
        And the user selects the "Resources" titled table widget field in the dashboard
        And the user clicks the "Refresh" more actions button in the header of the table widget field

        #Clock in
        And the user clicks the "Clock in" button of the table widget field

        #Moves to Site modal
        And the user selects the "Site" labelled reference field on a modal
        When the user clicks the lookup button of the reference field
        #Selects production site modal and searches for site
        And the user selects the "productionSite" bound table field on a modal

        #Filters with value
        And the user filters the "ID" labelled column in the table field with value "D1S"
        And the user selects the row with text "D1S" in the "ID" labelled column header of the table field
        And the user clicks the "ID" labelled nested field of the selected row in the table field

        #Select operator modal
        And the user selects the "Operator" labelled reference field on a modal
        When the user clicks the lookup button of the reference field
        And the user selects the "operator" bound table field on a modal

        And the user filters the "ID" labelled column in the table field with value "VVR"
        And the user selects the row with text "VVR" in the "ID" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field

        And the user clicks the "OK" labelled business action button on a modal

        #Check to see we can see data once clocked in
        And the user selects the "month" period type toggle button in the table widget
        And the user selects the "April 2025" date period in the table widget

        And the user selects the card 1 of the table widget field
        Then the value of the row 1 on the left of the card of the table widget field is "ETIQ1512-1550"

        #Clock out
        Given the "Shopfloor Control main dashboard" titled dashboard is displayed
        And the user clicks the "Clock out" button of the table widget field
        Then a toast with text "Operator clocked out successfully" is displayed
