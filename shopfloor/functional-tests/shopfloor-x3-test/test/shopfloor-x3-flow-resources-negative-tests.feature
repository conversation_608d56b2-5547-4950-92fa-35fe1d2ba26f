@shopfloor_x3

Feature: shopfloor-x3-flow-resources-negative-tests

    Scenario: Clock in

        Given the user opens the application on a HD desktop
        Then the "Shopfloor Control main dashboard" titled dashboard is displayed

        #Refresh work orders widget
        And the user selects the "resources" titled table widget field in the dashboard
        And the user clicks the "Refresh" more actions button in the header of the table widget field

        #Clock in
        And the user clicks the "Clock in" button of the table widget field

        #Select operator modal
        And the user selects the "Operator" labelled reference field on a modal
        When the user clicks the lookup button of the reference field

        And the user selects the "operator" bound table field on a modal

        And the user filters the "ID" labelled column in the table field with value "1001"
        And the user selects the row with text "1001" in the "ID" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field

        #Moves to Site modal
        And the user selects the "Site" labelled reference field on a modal
        When the user clicks the lookup button of the reference field
        #Selects production site modal and searches for site
        And the user selects the "productionSite" bound table field on a modal

        And the user filters the "ID" labelled column in the table field with value "M-SF1"
        And the user selects the row with text "M-SF1" in the "ID" labelled column header of the table field
        And the user clicks the "ID" labelled nested field of the selected row in the table field

        And the user clicks the "OK" labelled business action button on a modal

    Scenario: Resources Widget negative test

        Given the user opens the application on a HD desktop
        Then the "Shopfloor Control main dashboard" titled dashboard is displayed

        And the user selects the "resources" titled table widget field in the dashboard
        And the user clicks the "Refresh" more actions button in the header of the table widget field

        # Workaround so we don't have to scroll to the day
        And the user selects the "month" period type toggle button in the table widget
        And the user selects the "June 2025" date period in the table widget

        And the user selects the "day" period type toggle button in the table widget
        #Selecting day after month will automatically give 1st June so there is no need to scroll to search - gives more consistent result
        #And the user selects the "Jun 1, 2025" date period in the table widget
        #Check to that M-SF12412MFG00000001 is not shown for example
        And the number of records in the table widget fields is "0"

        And the user selects the "month" period type toggle button in the table widget
        And the user selects the "June 2025" date period in the table widget
        #Check to that M-SF12412MFG00000001 is not shown for example
        And the number of records in the table widget fields is "0"

        And the user selects the "week" period type toggle button in the table widget
        And the user selects the "Week 20, 2025" date period in the table widget
        #Check to that M-SF12412MFG00000001 is not shown for example
        #Then the table field is empty
        And the number of records in the table widget fields is "0"

    Scenario: Resource page - incorrect number length in dialog box

        Given the user opens the application on a HD desktop
        Then the "Shopfloor Control main dashboard" titled dashboard is displayed

        #Refresh work orders widget
        And the user selects the "resources" titled table widget field in the dashboard
        And the user clicks the "Refresh" more actions button in the header of the table widget field

        #Select required period
        And the user selects the "month" period type toggle button in the table widget
        And the user selects the "December 2024" date period in the table widget
        And takes a screenshot

        #Select required card
        And the user selects the card 1 of the table widget field
        Then the value of the row 1 on the left of the card of the table widget field is "M-SFC-01"
        And the user clicks the link of the row 1 on the left of the card of the table widget field
        And the user waits 5 seconds

        #Starting the operation
        And the user selects the "lines" bound table field on the main page
        And the value of the "computedProgressStatus" bound nested label field of the card 3 in the table field is "Off"

        And the user clicks the "Start run time" inline action button of the card 3 in the table field
        #Check status = Run started
        And the value of the "computedProgressStatus" bound nested label field of the card 3 in the table field is "Run started"

        And the user selects the "lines" bound table field on the main page
        And the user clicks the "Done" inline action button of the card 3 in the table field

        #Operation done
        And the user selects the "Produced quantity" labelled numeric field on a modal
        And the user writes "1000000000000000000000" in the numeric field
        And the numeric field is invalid
        And the user selects the "Rejected quantity" labelled numeric field on a modal
        #Check for error message
        And the user writes "1000000000000000000000" in the numeric field
        And the numeric field is invalid

        And the user clicks the "Save" labelled business action button on a modal

        Then a error toast containing text "1 error(s): shopfloorTracking.timeTrackingHeader.update: The record was not updated." is displayed
        Then an error dialog appears on the main page
        And the user clicks the Close button of the dialog on the main page

        And the user dismisses all the toasts

        #Check tracking was not completed
        And the value of the "computedProgressStatus" bound nested label field of the card 3 in the table field is "Run started"
        And the user waits 5 seconds

        #Clean up
        And the user selects the "lines" bound table field on the main page
        And the user clicks the "Done" inline action button of the card 3 in the table field

        And the user selects the "Produced quantity" labelled numeric field on a modal
        And the user writes "1" in the numeric field
        And the user clicks the "Save" labelled business action button on a modal

    Scenario: Resource page - unsaved changes dialog box

        Given the user opens the application on a HD desktop
        Then the "Shopfloor Control main dashboard" titled dashboard is displayed

        #Refresh work orders widget
        And the user selects the "resources" titled table widget field in the dashboard
        And the user clicks the "Refresh" more actions button in the header of the table widget field

        #Select required period
        And the user selects the "month" period type toggle button in the table widget
        And the user selects the "December 2024" date period in the table widget
        And takes a screenshot

        #Select required card
        And the user selects the card 1 of the table widget field
        Then the value of the row 1 on the left of the card of the table widget field is "M-SFC-01"
        And the user clicks the link of the row 1 on the left of the card of the table widget field
        And the user waits 5 seconds

        #Starting the operation
        And the user selects the "lines" bound table field on the main page
        And the value of the "computedProgressStatus" bound nested label field of the card 3 in the table field is "Off"

        And the user clicks the "Start setup time" inline action button of the card 3 in the table field

        And the value of the "computedProgressStatus" bound nested label field of the card 3 in the table field is "Setup started"

        And the user clicks the "Clock out" labelled business action button on the main page

        Then a Confirm dialog is displayed on a full width model
        And the user clicks the "Cancel" button of the Custom dialog on the main page

        And the user selects the "lines" bound table field on the main page
        And the user clicks the "Done" inline action button of the card 3 in the table field

    Scenario: Clock out

        Given the user opens the application on a HD desktop
        Then the "Shopfloor Control main dashboard" titled dashboard is displayed

        And the user selects the "Resources" titled table widget field in the dashboard
        And the user clicks the "Clock out" button of the table widget field
        Then a toast with text "Operator clocked out successfully" is displayed
