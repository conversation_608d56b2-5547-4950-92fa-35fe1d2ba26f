{"fromVersion": "57.0.21", "toVersion": "57.0.22", "gitHead": "39117b38cd87e7a271b0fa18cc3eb056885df9f2", "commands": [{"isSysPool": true, "sql": ["", "", "CREATE EXTENSION IF NOT EXISTS pgcrypto;", ""]}, {"isSysPool": true, "sql": ["", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.get_config(setting_name varchar)", "RETURNS varchar AS", "$$", "declare", "    setting_value varchar;", "BEGIN", "    SELECT current_setting(setting_name) into setting_value;", "    RETURN setting_value;", "EXCEPTION", "    WHEN OTHERS THEN", "    RETURN NULL;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_triggers(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "begin", "\trecord_count = 0;", "    FOR triggerRecord IN", "    \tSELECT trigger_name, event_object_table", "    \tFROM information_schema.triggers", "    \tWHERE trigger_schema = schema_name AND (name_filter = '' OR event_object_table = name_filter)", "\tLOOP", "\t\trecord_count = record_count + 1;", "        EXECUTE 'DROP TRIGGER ' || triggerRecord.trigger_name || ' ON ' || schema_name || '.\"' || triggerRecord.event_object_table || '\";';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_notify_functions(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "BEGIN", "    record_count = 0;", "    FOR triggerRecord IN", "    SELECT routine_name", "    FROM information_schema.routines", "    WHERE specific_schema = schema_name and routine_name IN (name_filter || '_notify_deleted', name_filter || '_notify_created', name_filter || '_notify_updated')", "    LOOP", "        record_count = record_count + 1;", "        EXECUTE 'DROP FUNCTION ' || schema_name || '.' || triggerRecord.routine_name || ';';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id'), '') INTO USER_ID;", "        IF (USER_ID <> '') THEN", "            NEW._create_user :=  CAST(USER_ID AS INT8);", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id'), '') INTO USER_ID;", "        IF (USER_ID <> '') THEN", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.set_sync_tick()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._sync_tick :=  pg_current_xact_id();", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.signedInt32(a bigint)", "RETURNS bigint AS", "$$", "DECLARE", "BEGIN", "\t-- Convert to 32 bit signed (if leftmost bit is 1, it's a negative number)", "  \tIF (a > 2^31) THEN", "    \tRETURN a - (2^32)::bigint;", "  \tEND IF;", "  \tRETURN a;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.imul(a bigint, b bigint)", "RETURNS bigint AS", "$$", "DECLARE", "    aHi bigint;", "    aLo bigint;", "    bHi bigint;", "    bLo bigint;", "    res bigint;", "BEGIN", "    aHi = %%SCHEMA_NAME%%.zeroFillShift(a, 16) & 65535;", "    aLo = a & 65535;", "    bHi = %%SCHEMA_NAME%%.zeroFillShift(b, 16) & 65535;", "    bLo = b & 65535;", "    res = ((aLo * bLo) + %%SCHEMA_NAME%%.zeroFillShift(((aHi * bLo + aLo * bHi) << 16) % (2^32)::bigint, 0)) | 0;", "    RETURN %%SCHEMA_NAME%%.signedInt32(res);", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.zeroFillShift(a bigint, b int)", "RETURNS bigint AS", "$$", "DECLARE", "  \tres bigint;", "BEGIN", "\tIF (a < 0) THEN", "\t\tres = a + 2^32;", "\tELSE", "\t\tres = a;", "\tEND IF;", "\tres = res >> b;", "\tRETURN res;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.nanoid(\"size\" int4 DEFAULT 21)", "    RETURNS text", "    LANGUAGE plpgsql", "    STABLE", "    AS", "    $$", "    DECLARE", "        id text := '';", "        i int := 0;", "        urlAl<PERSON><PERSON> char(64) := 'ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW';", "        bytes bytea;", "        byte int;", "        pos int;", "    BEGIN", "        SELECT gen_random_bytes(size) INTO bytes;", "    WHILE i < size LOOP", "        byte := get_byte(bytes, i);", "        pos := (byte & 63) + 1; -- + 1 because substr starts at 1", "        id := id || substr(urlAlphabet, pos, 1);", "        i = i + 1;", "    END LOOP;", "    RETURN id;", "    END", "    $$", "    ;", " ", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.audit_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        -- parameters", "        p_root_table_name VARCHAR;", "        p_constructor VARCHAR;", "", "        -- audit variables", "        is_audit_enabled VARCHAR;", "        tenant_id VARCHAR;", "        rid INT8;", "        login_email VARCHAR;", "        user_id VARCHAR;", "        locale VARCHAR;", "        log_record RECORD;", "", "        -- notify variables", "        origin_id VARCHAR;", "        notify_all_disabled VARCHAR;", "        notify_tenant_disabled VARCHAR;", "        notification_id VARCHAR;", "        user_email VARCHAR;", "        constructor VARCHAR;", "        event VARCHAR;", "        topic VARCHAR;", "        envelope VARCHAR;", "        payload VARCHAR;", "    BEGIN", "        p_root_table_name := TG_ARGV[0];", "        p_constructor := TG_ARGV[1];", "", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.is_audit_enabled'), 'false') INTO is_audit_enabled;", "        IF (is_audit_enabled <> 'true') THEN", "            RETURN NEW;", "        END IF;", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.login_email'), '') INTO login_email;", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id'), '') INTO user_id;", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.locale'), 'base') INTO locale;", "", "        tenant_id := COALESCE(NEW._tenant_id, OLD._tenant_id);", "        rid := COALESCE(NEW._id, OLD._id);", "", "        IF p_constructor != '' THEN", "            constructor := p_constructor;", "        ELSE", "            constructor := COALESCE(NEW._constructor, OLD._constructor);", "        END IF;", "", "", "        SELECT * FROM %%SCHEMA_NAME%%.sys_audit_log", "        WHERE root_table_name = p_root_table_name", "            AND record_id = rid", "            AND transaction_id::TEXT = pg_current_xact_id()::TEXT", "        INTO log_record;", "", "        IF log_record IS NULL THEN", "            RAISE NOTICE 'Inserting new audit log record %:%', p_root_table_name, NEW._id;", "            IF p_root_table_name = TG_TABLE_NAME THEN", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (node_name, root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick, _create_user, _update_user)", "                    VALUES (constructor, p_root_table_name, tenant_id, rid, TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), OLD._update_tick, NEW._update_tick, user_id::INT8, user_id::INT8);", "\t        RAISE NOTICE 'Inserted new audit log record root_table=%, table=%, _id=%, transaction_id=%, update_tick=%->%', p_root_table_name, TG_TABLE_NAME, NEW._id, pg_current_xact_id(), OLD._update_tick, NEW._update_tick;", "            ELSE", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (node_name, root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick)", "                    VALUES (constructor, p_root_table_name, tenant_id, rid, TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), NULL, NULL);", "\t        RAISE NOTICE 'Inserted new audit log record root_table=%, table=%, _id=%, transaction_id=%', p_root_table_name, TG_TABLE_NAME, NEW._id, pg_current_xact_id();", "            END IF;", "        ELSE", "            RAISE NOTICE 'Updating audit log record %:%', p_root_table_name, NEW._id;", "            IF p_root_table_name = TG_TABLE_NAME THEN", "                UPDATE %%SCHEMA_NAME%%.sys_audit_log", "                SET record_data = log_record.record_data || to_jsonb(NEW), new_update_tick = NEW._update_tick", "                WHERE root_table_name = p_root_table_name", "                    AND record_id = NEW._id", "                    AND transaction_id = pg_current_xact_id()::TEXT;", "\t        RAISE NOTICE 'Updated audit log record %:%, transaction_id=%, updateTick=%->%', p_root_table_name, NEW._id, pg_current_xact_id(), log_record.old_update_tick, NEW._update_tick;", "            ELSE", "                UPDATE %%SCHEMA_NAME%%.sys_audit_log", "                SET record_data = log_record.record_data || to_jsonb(NEW)", "                WHERE root_table_name = p_root_table_name", "                    AND record_id = NEW._id", "                    AND transaction_id = pg_current_xact_id()::TEXT;", "\t        RAISE NOTICE 'Updated audit log record %:%, transaction_id=%', p_root_table_name, NEW._id, pg_current_xact_id();", "            END IF;", "        END IF;", "", "        -- do not send a notification for the update if the record was created in the same transaction", "        -- this will happen with nodes with deferred saves (sales order for instance)", "        IF (p_root_table_name = TG_TABLE_NAME) AND (log_record IS NULL) THEN", "            SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.notification.disable.ALL'), 'false') INTO notify_all_disabled;", "            SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.notification.disable.t_' || tenant_id), 'false') INTO notify_tenant_disabled;", "", "            IF (notify_all_disabled <> 'true' and notify_tenant_disabled <> 'true') THEN", "                SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.origin_id'), '') INTO origin_id;", "                SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.user_email'), '') INTO user_email;", "                SELECT %%SCHEMA_NAME%%.nanoid() INTO notification_id;", "", "                CASE TG_OP", "                    WHEN 'INSERT' THEN event := 'created';", "                    WHEN 'UPDATE' THEN event := 'updated';", "                    WHEN 'DELETE' THEN event := 'deleted';", "                END CASE;", "", "                topic := constructor || '/' || event;", "                payload := '{ \"_id\":' || rid || ', \"_updateTick\":' || COALESCE(NEW._update_tick, OLD._update_tick) || '}';", "", "                RAISE NOTICE 'Inserted new notification %:%', topic, notification_id;", "                INSERT INTO %%SCHEMA_NAME%%.sys_notification", "                    (tenant_id, origin_id, notification_id, reply_id, reply_topic, user_email, login, locale,", "                    topic, payload, status, _source_id, _update_tick, _create_stamp, _update_stamp)", "                VALUES (tenant_id, origin_id, notification_id, '', '', user_email, login_email, locale,", "                    topic, payload, 'pending', '', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);", "", "                RAISE NOTICE 'Notifying %:%', TG_OP, event;", "                PERFORM pg_notify('notification_queued', '{\"data\":\"{\\\"topic\\\":\\\"' || event || '\\\"}\"}');", "            END IF;", "        END IF;", "", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", ""]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["allow to display changelog in the app", "experimental", false, "@sage/xtrem-system", false, "changelog"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["enable dev-only features", "experimental", false, "@sage/xtrem-system", false, "devTools"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Is a demo tenant", "released", false, "@sage/xtrem-system", false, "isDemoTenant"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Enable PIN code authentication feature", "released", false, "@sage/xtrem-system", false, "sysDeviceToken"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Enable Tags feature", "released", false, "@sage/xtrem-system", false, "tags"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Notification center", "experimental", false, "@sage/xtrem-communication", false, "notificationCenter"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Authorization access control", "released", false, "@sage/xtrem-authorization", false, "authorizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Enable Auditing feature", "released", false, "@sage/xtrem-auditing", false, "auditing"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Auditing option", "workInProgress", true, "@sage/xtrem-auditing", false, "auditingOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Enable Workflow feature", "released", false, "@sage/xtrem-workflow", false, "workflow"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow advanced features (not yet released)", "workInProgress", false, "@sage/xtrem-workflow", false, "workflowAdvanced"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow option (obsolete)", "workInProgress", true, "@sage/xtrem-workflow", false, "workflowOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Synchronization", "released", false, "@sage/xtrem-interop", true, "synchronizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["enable asyncPerPostProcessing print engine feature", "experimental", false, "@sage/xtrem-reporting", false, "asyncPrePostProcessing"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["enable ReportAssignment feature", "workInProgress", false, "@sage/xtrem-reporting", false, "reportAssignment"]}, {"isSysPool": false, "sql": "NOTIFY invalidate_category_cache, '{\"data\":\"{\\\"tenantId\\\":null,\\\"category\\\":\\\"$SHARED_NODE.SysServiceOption\\\"}\",\"containerId\":\"x3-devops001BX9-82133\",\"excludeSelf\":true}';", "args": []}, {"isSysPool": false, "sql": ["SELECT", "                _id, email, is_active, first_name, last_name,", "                 is_administrator, is_api_user, is_demo_persona, operator_id", "            FROM %%SCHEMA_NAME%%.user WHERE _tenant_id=$1 AND email = $2"], "args": ["777777777777777777777", "<EMAIL>"], "actionDescription": "Reload setup layer for factories ReportTemplate,ReportTranslatableText"}, {"action": "reload_setup_data", "args": {"factory": "ReportTemplate"}}, {"action": "reload_setup_data", "args": {"factory": "ReportTranslatableText"}}], "data": {"ReportTemplate": {"metadata": {"rootFactoryName": "ReportTemplate", "name": "ReportTemplate", "naturalKeyColumns": ["_tenant_id", "name"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "name", "type": "string"}, {"name": "html_template", "type": "textStream"}, {"name": "attachment_template", "type": "textStream"}, {"name": "attachment_name", "type": "string"}, {"name": "attachment_mime_type", "type": "string"}, {"name": "header_html_template", "type": "textStream"}, {"name": "footer_html_template", "type": "textStream"}, {"name": "style_sheet", "type": "textStream"}, {"name": "query", "type": "textStream"}, {"name": "code", "type": "textStream"}, {"name": "report", "type": "reference", "isNullable": true, "targetFactoryName": "Report"}, {"name": "is_factory", "type": "boolean"}, {"name": "is_expert_document", "type": "boolean", "isOwnedByCustomer": true}, {"name": "base_locale", "type": "enum", "enumMembers": ["ar_<PERSON>", "de_DE", "en_GB", "en_US", "es_ES", "fr_FR", "it_IT", "pl_PL", "pt_BR", "pt_PT", "zh_CN"]}, {"name": "default_paper_format", "type": "enum", "enumMembers": ["letter", "legal", "tabloid", "ledger", "a0", "a1", "a2", "a3", "a4", "a5", "a6"]}, {"name": "default_page_orientation", "type": "enum", "enumMembers": ["portrait", "landscape"]}, {"name": "default_left_margin", "type": "decimal"}, {"name": "default_right_margin", "type": "decimal"}, {"name": "default_top_margin", "type": "decimal"}, {"name": "default_bottom_margin", "type": "decimal"}, {"name": "is_default_header_footer", "type": "boolean"}]}, "rows": [["sage", "activeUsers", "<div class=\"entry\">\n  <img src=\"{{reportResource 'companyLogo'}}\" style=\"width:2cm; height:2cm\"/>   <h1>Active Users</h1>\n    <table>\n        <thead>\n            <th>First Name</th>\n            <th>Last Name</th>\n            <th>Email</th>\n        </thead>\n        <tbody>\n            {{#each codeBlockResult}}\n            <tr>\n                <td>{{node.firstName}}</td>\n                <td>{{node.lastName}}</td>\n                <td>{{node.email}}</td>\n            </tr>\n            {{/each}}\n        </tbody>\n    </table>\n</div>", null, null, null, null, null, "@font-face { font-family: \"companyFont\"; src: url({{reportResource 'companyFont'}}) format(\"woff2\"); font-weight: 300;font-style: normal;font-display: swap;}\nh1,h2,h3,h4,th{\n    font-family:\"companyFont\";color: var(--themePrimary);\n}\n\ntable {\n  font-size: 12px;\n  width: 100%;\n  border-collapse: collapse;\n  margin-bottom: 16px;\n}\n\ntable tbody tr {\n  border-bottom: 1px solid var(--tableSeparator);\n}\n\ntable tbody tr:nth-child(even) {\n  background: #F2F5F6;\n}\n\ntable th {\n  border-bottom: 2px solid var(--themePrimary);\n  border-right: 1px solid var(--tableSeparator);\n  padding: 4px;\n  font-size: 12px;\n}\n\ntable th:last-child {\n    border-right: none;\n}\n\ntable th:first-child {\n    padding-left: 0\n}\n\ntable td {\n  font-size: 12px;\n  vertical-align: top;\n  border-bottom: 1px solid var(--tableSeparator);\n  border-right: 1px solid var(--tableSeparator);\n  padding: 4px;\n}\n\n\ntable td:last-child{\n    border-right: none;\n    padding-right: 0;\n}\ntable td:first-child{\n    padding-left: 0;\n}\n\ntable .column-left {\n  text-align: left;\n}\n\ntable .column-right{\n  text-align: right;\n}\n\n.header-table{\n  width: 100%;\n}\n\n.header-table td{\n  vertical-align: top;\n}", "{\n  xtremSystem {\n    user {\n      query {\n        edges {\n          node {\n            _id\n            email\n            firstName\n            lastName\n            isActive\n            displayName\n            userType\n          }\n        }\n      }\n    }\n  }\n}\n", "function capitalize(word) {\n    return word?.replace(/^(.)/, c => c.toUpperCase());\n}\n\nconst activeUsers = queryResponse.xtremSystem.user.query.edges.filter(edge => edge.node.isActive);\n\nconst capitalizedUsers = activeUsers.map(edge => {\n    const pieces = edge.node.email.split('@');\n    const segments = pieces[0].split('.');\n    const firstName = capitalize(segments[0]);\n    const lastName = capitalize(segments[1]);\n    edge.node.email = `${firstName}${lastName ? '.' + lastName : ''}@${pieces[1]}`;\n\n    return edge;\n});\n\nreturn  capitalizedUsers;", "activeUsersListing", "Y", "Y", "en_US", "letter", "portrait", "2", "2", "2", "2", null], ["sage", "reportDefinitionTemplate", "<div class=\"main\">\n    <h1>Report Definitions</h1>\n    <hr class=\"sep\">\n\n    {{#each codeBlockResult.report.query.edges}}\n        <p>\n            <h2>Report: {{node.name}}</h2>\n            <i>{{node.description}}</i>\n        </p>\n        <b>Active Template:</b> {{node.activeTemplate.name}}<br />\n        <hr>\n        <b>Query:</b><br />{{node.activeTemplate.query.value}}<br /><br />\n        <b>Code:</b><br />{{node.activeTemplate.code.value}}<br /><br />\n        <b>HTML:</b><br />{{node.activeTemplate.htmlTemplate.value}}<br /><br />\n        <b>Style Sheet:</b><br />{{node.activeTemplate.styleSheet.value}}<br /><br />\n        <div class=\"page-break\"></div>\n    {{/each}}\n</div>", null, null, null, null, null, ".main {\n    font-family: Helvetica;\n}\n\nh1,h2 {\n    color: var(--themePrimary);\n}\n\nhr.sep {\n  border-top: 1px dotted var(--themePrimary);\n}\n\n@page {\n    size: auto;\n    margin: 15mm 15mm 15mm 15mm;\n}\n\n@media print {\n  .page-break {\n    page-break-after: always;\n  }\n}\n", "{\n  query1: xtremReporting {\n    report {\n      query {\n        edges {\n          node {\n            _id\n            name\n            parentPackage\n            activeTemplate {\n              _id\n            }\n            isFactory\n            description\n            variables {\n              query {\n                edges {\n                  node {\n                    _id\n                    name\n                    type\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n  query2: xtremReporting {\n    reportTemplate {\n      query {\n        edges {\n          node {\n            _id\n            name\n            isFactory\n            query {\n              value\n            }\n            code {\n              value\n            }\n            htmlTemplate {\n              value\n            }\n            styleSheet {\n              value\n            }\n          }\n        }\n      }\n    }\n  }\n}", "queryResponse.query1.report.query.edges.forEach(report => {\n    const templateId = report.node.activeTemplate?._id;\n    if (templateId) {\n        const template = queryResponse.query2.reportTemplate.query.edges.find(edge => edge.node._id === templateId);\n        if (template) {\n            \n            report.node.activeTemplate = template.node;\n        }\n    }\n});\n\nreturn queryResponse.query1;", "reportDefinitions", "Y", "Y", "en_US", "letter", "portrait", "2", "2", "2", "2", null], ["sage", "usersByType", "<div class=\"entry\">\n    <h1>Users By Type</h1>  \n       \n    <hr class=\"sep\"> \n    <h2>Application Users</h2>\n    {{#if codeBlockResult.applicationUsersCount}}\n        <table>\n            <thead>\n                <th>First Name</th>\n                <th>Last Name</th>\n                <th>Email</th>\n            </thead>\n            <tbody>\n                {{#each codeBlockResult.applicationUsers}}\n                    <tr>\n                        <td>{{node.firstName}}</td>\n                        <td>{{node.lastName}}</td>\n                        <td>{{node.email}}</td>\n                    </tr>\n                {{/each}}\n            </tbody>\n        </table>\n        Application user count: {{length codeBlockResult.applicationUsers}}<br />\n    {{else}}\n        No application users found<br />\n    {{/if}}\n    <br />\n\n    {{#if codeBlockResult.systemUsersIncluded}}\n        <hr class=\"sep\">\n        <h2>System Users</h2>\n        {{#if codeBlockResult.systemUsersCount}}\n            <table>\n                <thead>\n                    <th>First Name</th>\n                    <th>Last Name</th>\n                    <th>Email</th>\n                </thead>\n                <tbody>\n                    {{#each codeBlockResult.systemUsers}}\n                    <tr>\n                        <td>{{node.firstName}}</td>\n                        <td>{{node.lastName}}</td>\n                        <td>{{node.email}}</td>\n                    </tr>\n                    {{/each}}\n                </tbody>\n            </table>\n            System user count: {{length codeBlockResult.systemUsers}}<br />\n        {{else}}\n            No system users found<br />\n        {{/if}}\n        <br />\n    {{/if}}\n    \n</div>", null, null, null, null, null, "table {\n  font-size: 12px;\n  width: 100%;\n  border-collapse: collapse;\n  margin-bottom: 16px;\n}\n\ntable tbody tr {\n  border-bottom: 1px solid var(--tableSeparator);\n}\n\ntable tbody tr:nth-child(even) {\n  background: #F2F5F6;\n}\n\ntable th {\n  border-bottom: 2px solid var(--themePrimary);\n  border-right: 1px solid var(--tableSeparator);\n  padding: 4px;\n  font-size: 12px;\n}\n\ntable th:last-child {\n    border-right: none;\n}\n\ntable th:first-child {\n    padding-left: 0\n}\n\ntable td {\n  font-size: 12px;\n  vertical-align: top;\n  border-bottom: 1px solid var(--tableSeparator);\n  border-right: 1px solid var(--tableSeparator);\n  padding: 4px;\n}\n\n\ntable td:last-child{\n    border-right: none;\n    padding-right: 0;\n}\ntable td:first-child{\n    padding-left: 0;\n}\n\ntable .column-left {\n  text-align: left;\n}\n\ntable .column-right{\n  text-align: right;\n}\n\n.header-table{\n  width: 100%;\n}\n\n.header-table td{\n  vertical-align: top;\n}\n\nh1 {\n    color: var(--themePrimary);\n}\n\nh2 {\n  color: var(--slate90);\n}\n\nhr.sep {\n  border-top: 1px dotted var(--themePrimary);\n}\n\n", "{\n  qApp: xtremSystem {\n    user {\n      query(filter: \"{userType:'application'}\") {\n        edges {\n          node {\n            email\n            firstName\n            lastName\n            userType\n          }\n        }\n      }\n    }\n  }\n  \n  {{#if includeSystemUsers}}\n    qSys: xtremSystem {\n      user {\n        query(filter: \"{userType:'system'}\") {\n          edges {\n            node {\n              email\n              firstName\n              lastName\n              userType\n            }\n          }\n        }\n      }\n    }\n\t{{/if}}\n\n}\n\n", "const applicationUsers = queryResponse.qApp.user.query.edges.filter(edge => edge.node.userType === 'application');\nconst applicationUsersCount = applicationUsers.length;\n\nconst systemUsersIncluded = !!queryResponse.qSys;\nconst systemUsers = systemUsersIncluded ? queryResponse.qSys.user.query.edges.filter(edge => edge.node.userType === 'system') : [];\nconst systemUsersCount = systemUsers.length;\n\nreturn {\n    applicationUsersCount,\n    applicationUsers,\n    systemUsersIncluded,\n    systemUsersCount,\n    systemUsers,\n};\n", "usersByType", "Y", "Y", "en_US", "letter", "portrait", "2", "2", "2", "2", null], ["sage", "usersByTypeAggregate", "<div class=\"entry\">\n    <h1>Users By Type</h1>\n\n    <table>\n        <tbody>\n            {{#each xtremSystem.user.query.edges 'node.authorizationGroup.query.totalCount' 'sum'}}\n                {{#printBreakIfPropertyChanged 'node.userType'}}\n                    <tr>\n                        <td colspan=\"5\" class=\"user-type\"><strong>{{node.userType}}</strong></td>\n                    </tr>\n                    <tr>\n                        <td class=\"header\">First Name</td>\n                        <td class=\"header\">Last Name</td>\n                        <td class=\"header\">Email</td>\n                        <td class=\"header\">User Type</td>\n                        <td class=\"header\">Num of Auth Groups</td>\n\n                    </tr>\n                {{/printBreakIfPropertyChanged}}\n                <tr>\n                    <td>{{node.firstName}}</td>\n                    <td>{{node.lastName}}</td>\n                    <td>{{node.email}}</td>\n                    <td>{{node.userType}}</td>\n                    <td>{{node.authorizationGroup.query.totalCount}}</td>\n                </tr>\n                {{#printBreakIfPropertyWillChange 'node.userType' 'node.authorizationGroup.query.totalCount' 'sum'}}\n                    <tr>\n                        <td colspan=\"4\"><strong>Total number of groups for user type:</strong></td>\n                        <td>{{blockAggregatedData.node.authorizationGroup.query.totalCount.sum}}</td>\n                    </tr>\n                {{/printBreakIfPropertyWillChange}}\n                {{#if @last}}\n                    <tr>\n                        <td colspan=\"5\" class=\"user-type\"></td>\n                    </tr>\n                    <tr>\n                        <td colspan=\"4\"><strong>Total number of auth groups:</strong></td>\n                        <td>{{aggregatedData.node.authorizationGroup.query.totalCount.sum}}</td>\n                    </tr>\n                {{/if}}\n            {{/each}}\n        </tbody>\n    </table>\n</div>", null, null, null, null, null, "table {\n    font-size: 12px;\n    width: 100%;\n    border-collapse: collapse;\n    margin-bottom: 16px;\n  }\n  \n  table tbody tr {\n    border-bottom: 1px solid var(--tableSeparator);\n  }\n  \n  table tbody tr:nth-child(even) {\n    background: #F2F5F6;\n  }\n  \n  .user-type{\n    padding-top:1cm;\n    padding-bottom: 1cm;\n    text-align: center\n  }\n  \n  table td.header {\n    border-bottom: 2px solid var(--themePrimary);\n    border-right: 1px solid var(--tableSeparator);\n    border-top:0;\n    padding: 4px;\n    font-size: 12px;\n    font-weight: bold;\n  }\n  \n  table td.header:last-child {\n      border-right: none;\n  }\n  \n  table td.header:first-child {\n      padding-left: 0\n  }\n  \n  table td {\n    font-size: 12px;\n    vertical-align: top;\n    border-bottom: 1px solid var(--tableSeparator);\n    border-right: 1px solid var(--tableSeparator);\n    padding: 4px;\n  }\n  \n  \n  table td:last-child{\n      border-right: none;\n      padding-right: 0;\n  }\n  table td:first-child{\n      padding-left: 0;\n  }\n  \n  table .column-left {\n    text-align: left;\n  }\n  \n  table .column-right{\n    text-align: right;\n  }\n  \n  .header-table{\n    width: 100%;\n  }\n  \n  .header-table td{\n    vertical-align: top;\n  }\n  \n  h1 {\n      color: var(--themePrimary);\n  }\n  \n  h2 {\n    color: var(--slate90);\n  }\n  \n  hr.sep {\n    border-top: 1px dotted var(--themePrimary);\n  }\n  \n ", "{\n  xtremSystem {\n    user {\n      query(orderBy: \"{userType:1}\") {\n        edges {\n          node {\n            email\n            firstName\n            lastName\n            userType\n            authorizationGroup {\n              query {\n                totalCount\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n}\n", "", "usersByType", "Y", "Y", "en_US", "letter", "portrait", "2", "2", "2", "2", null], ["sage", "onboarding_user", "<div>\n        <table>\n            <tbody><tr>\n                <td class=\"content\">\n                    {{ translatedContent \"c1a5298f939e87e8f962a5edfc206918\" }} {{userName}}{{ translatedContent \"c0cb5f0fcf239ab3d9c1fcd31fff1efc\" }} <br>\n                    <br>\n                    {{ translatedContent \"f9a331ed7b787f30458b0d40bfe5dad8\" }} {{productName}}{{ translatedContent \"5058f1af8388633f609cadb75a75dc9d\" }}\n                    <br>\n                    <br>\n                    {{ translatedContent \"f9da1f20f0d305cf4017d39c810c1974\" }} <b>{{ translatedContent \"51f571758d313dd9aa5299270b965601\" }}</b> {{ translatedContent \"81e3ddac8ffc4d2dda377b2953ebe0a7\" }} <br><br>\n                    {{ translatedContent \"a55765c16f25a04c736a1b6166e9067b\" }}\n                   <span> <a href=\"{{urlTenant}}\">{{tenantName}}</a></span>\n                    <br>\n                    <br>\n                    {{ translatedContent \"0579762f5f581cd7154a151ad0f68013\" }} <br>{{productName}}\n                </td>\n            </tr>\n        </tbody></table>\n\t</div>", null, null, null, null, null, "h1 {\r\n    margin-top: 20px;\r\n    margin-left: 10px;\r\n}\r\n\r\ntable .header {\r\n    text-align: center;\r\n}\r\n\r\ntable .content {\r\n    text-align: left;\r\n    background-color: #f2f5f6;\r\n    padding: 32px 32px 32px 32px;\r\n    border-collapse: collapse;\r\n    font-family: <PERSON>elleSansSAGE, Arial, Roboto, Segoe UI, Helvetica Neue;\r\n    font-weight: 300;\r\n    color: #191919;\r\n    font-size: 16px;\r\n    line-height: 1.5;\r\n}\r\na {\r\n    font-size: 16px;\r\n    font-family: AdelleSansSAGE, Arial, Roboto, Segoe UI, Helvetica Neue;\r\n    display: inline-block;\r\n    font-weight: bold;\r\n    text-decoration: underline;\r\n    color: #008200;\r\n}", "", "", "onboarding_user", "Y", "Y", "en_US", "letter", "portrait", "2", "2", "2", "2", null], ["sage", "onboarding_tenant", "<div>\n        <table>\n                <tbody><tr><td class=\"content\">\n                    {{ translatedContent \"c1a5298f939e87e8f962a5edfc206918\" }} {{userName}}{{ translatedContent \"c0cb5f0fcf239ab3d9c1fcd31fff1efc\" }} <br>\n                    <br>\n                    {{ translatedContent \"9819fce8eebe00198efa537109e99037\" }} <span>{{productName}}</span>{{ translatedContent \"e4279e40c5c50aff88bb684d16f24e8f\" }} <span>{{tenantName}}</span> {{ translatedContent \"a10fb6cb8511debb65203f34a01e7bc3\" }}\n                    <br>\n                    <br>\n                    {{ translatedContent \"854dd4c0e354a39d873239121bfea3f4\" }} <br><br>\n                    {{ translatedContent \"a55765c16f25a04c736a1b6166e9067b\" }}\n                    <a href=\"{{urlTenant}}\">{{tenantName}}</a>\n                    <br>\n                    <br>\n                    {{ translatedContent \"0579762f5f581cd7154a151ad0f68013\" }} <br>{{productName}}\n                </td></tr></tbody>\n        </table>\n\t</div>", null, null, null, null, null, "h1 {\r\n    margin-top: 20px;\r\n    margin-left: 10px;\r\n}\r\n\r\ntable .header {\r\n    text-align: center;\r\n}\r\n\r\ntable .content {\r\n    text-align: left;\r\n    background-color: #f2f5f6;\r\n    padding: 32px 32px 32px 32px;\r\n    border-collapse: collapse;\r\n    font-family: <PERSON>elleSansSAGE, Arial, Roboto, Segoe UI, Helvetica Neue;\r\n    font-weight: 300;\r\n    color: #191919;\r\n    font-size: 16px;\r\n    line-height: 1.5;\r\n}\r\na {\r\n    font-size: 16px;\r\n    font-family: AdelleSansSAGE, Arial, Roboto, Segoe UI, Helvetica Neue;\r\n    display: inline-block;\r\n    font-weight: bold;\r\n    text-decoration: underline;\r\n    color: #008200;\r\n}", "", "", "onboarding_tenant", "Y", "Y", "en_US", "letter", "portrait", "2", "2", "2", "2", null]]}, "ReportTranslatableText": {"metadata": {"isVitalChild": true, "isVitalCollectionChild": true, "rootFactoryName": "ReportTranslatableText", "name": "ReportTranslatableText", "naturalKeyColumns": ["_tenant_id", "report_template", "_sort_value"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "_sort_value", "type": "integer"}, {"name": "report_template", "type": "reference", "targetFactoryName": "ReportTemplate"}, {"name": "original_text", "type": "reference", "isNullable": true, "targetFactoryName": "ReportTranslatableText"}, {"name": "hash", "type": "string"}, {"name": "locale", "type": "enum", "enumMembers": ["ar_<PERSON>", "de_DE", "en_GB", "en_US", "es_ES", "fr_FR", "it_IT", "pl_PL", "pt_BR", "pt_PT", "zh_CN"]}, {"name": "is_base_locale", "type": "boolean"}, {"name": "text", "type": "string"}], "vitalParentColumn": {"name": "report_template", "type": "reference", "targetFactoryName": "ReportTemplate"}}, "rows": [["sage", "10", "onboarding_user", "", "c1a5298f939e87e8f962a5edfc206918", "en_US", "Y", "Hi"], ["sage", "20", "onboarding_user", "onboarding_user|10", "c1a5298f939e87e8f962a5edfc206918", "ar_<PERSON>", "N", ""], ["sage", "30", "onboarding_user", "onboarding_user|10", "c1a5298f939e87e8f962a5edfc206918", "de_DE", "N", ""], ["sage", "40", "onboarding_user", "onboarding_user|10", "c1a5298f939e87e8f962a5edfc206918", "en_GB", "N", ""], ["sage", "50", "onboarding_user", "onboarding_user|10", "c1a5298f939e87e8f962a5edfc206918", "es_ES", "N", ""], ["sage", "60", "onboarding_user", "onboarding_user|10", "c1a5298f939e87e8f962a5edfc206918", "fr_FR", "N", "Bonjour"], ["sage", "70", "onboarding_user", "onboarding_user|10", "c1a5298f939e87e8f962a5edfc206918", "it_IT", "N", ""], ["sage", "80", "onboarding_user", "onboarding_user|10", "c1a5298f939e87e8f962a5edfc206918", "pl_PL", "N", ""], ["sage", "90", "onboarding_user", "onboarding_user|10", "c1a5298f939e87e8f962a5edfc206918", "pt_BR", "N", ""], ["sage", "100", "onboarding_user", "onboarding_user|10", "c1a5298f939e87e8f962a5edfc206918", "pt_PT", "N", ""], ["sage", "110", "onboarding_user", "onboarding_user|10", "c1a5298f939e87e8f962a5edfc206918", "zh_CN", "N", ""], ["sage", "120", "onboarding_user", "", "c0cb5f0fcf239ab3d9c1fcd31fff1efc", "en_US", "Y", ","], ["sage", "130", "onboarding_user", "onboarding_user|120", "c0cb5f0fcf239ab3d9c1fcd31fff1efc", "ar_<PERSON>", "N", ""], ["sage", "140", "onboarding_user", "onboarding_user|120", "c0cb5f0fcf239ab3d9c1fcd31fff1efc", "de_DE", "N", ""], ["sage", "150", "onboarding_user", "onboarding_user|120", "c0cb5f0fcf239ab3d9c1fcd31fff1efc", "en_GB", "N", ""], ["sage", "160", "onboarding_user", "onboarding_user|120", "c0cb5f0fcf239ab3d9c1fcd31fff1efc", "es_ES", "N", ""], ["sage", "170", "onboarding_user", "onboarding_user|120", "c0cb5f0fcf239ab3d9c1fcd31fff1efc", "fr_FR", "N", ","], ["sage", "180", "onboarding_user", "onboarding_user|120", "c0cb5f0fcf239ab3d9c1fcd31fff1efc", "it_IT", "N", ""], ["sage", "190", "onboarding_user", "onboarding_user|120", "c0cb5f0fcf239ab3d9c1fcd31fff1efc", "pl_PL", "N", ""], ["sage", "200", "onboarding_user", "onboarding_user|120", "c0cb5f0fcf239ab3d9c1fcd31fff1efc", "pt_BR", "N", ""], ["sage", "210", "onboarding_user", "onboarding_user|120", "c0cb5f0fcf239ab3d9c1fcd31fff1efc", "pt_PT", "N", ""], ["sage", "220", "onboarding_user", "onboarding_user|120", "c0cb5f0fcf239ab3d9c1fcd31fff1efc", "zh_CN", "N", ""], ["sage", "230", "onboarding_user", "", "f9a331ed7b787f30458b0d40bfe5dad8", "en_US", "Y", "Your administrator granted you access to"], ["sage", "240", "onboarding_user", "onboarding_user|230", "f9a331ed7b787f30458b0d40bfe5dad8", "ar_<PERSON>", "N", ""], ["sage", "250", "onboarding_user", "onboarding_user|230", "f9a331ed7b787f30458b0d40bfe5dad8", "de_DE", "N", ""], ["sage", "260", "onboarding_user", "onboarding_user|230", "f9a331ed7b787f30458b0d40bfe5dad8", "en_GB", "N", ""], ["sage", "270", "onboarding_user", "onboarding_user|230", "f9a331ed7b787f30458b0d40bfe5dad8", "es_ES", "N", ""], ["sage", "280", "onboarding_user", "onboarding_user|230", "f9a331ed7b787f30458b0d40bfe5dad8", "fr_FR", "N", "Votre administrateur vous a donné des droits d'accès sur"], ["sage", "290", "onboarding_user", "onboarding_user|230", "f9a331ed7b787f30458b0d40bfe5dad8", "it_IT", "N", ""], ["sage", "300", "onboarding_user", "onboarding_user|230", "f9a331ed7b787f30458b0d40bfe5dad8", "pl_PL", "N", ""], ["sage", "310", "onboarding_user", "onboarding_user|230", "f9a331ed7b787f30458b0d40bfe5dad8", "pt_BR", "N", ""], ["sage", "320", "onboarding_user", "onboarding_user|230", "f9a331ed7b787f30458b0d40bfe5dad8", "pt_PT", "N", ""], ["sage", "330", "onboarding_user", "onboarding_user|230", "f9a331ed7b787f30458b0d40bfe5dad8", "zh_CN", "N", ""], ["sage", "340", "onboarding_user", "", "5058f1af8388633f609cadb75a75dc9d", "en_US", "Y", "."], ["sage", "350", "onboarding_user", "onboarding_user|340", "5058f1af8388633f609cadb75a75dc9d", "ar_<PERSON>", "N", ""], ["sage", "360", "onboarding_user", "onboarding_user|340", "5058f1af8388633f609cadb75a75dc9d", "de_DE", "N", ""], ["sage", "370", "onboarding_user", "onboarding_user|340", "5058f1af8388633f609cadb75a75dc9d", "en_GB", "N", ""], ["sage", "380", "onboarding_user", "onboarding_user|340", "5058f1af8388633f609cadb75a75dc9d", "es_ES", "N", ""], ["sage", "390", "onboarding_user", "onboarding_user|340", "5058f1af8388633f609cadb75a75dc9d", "fr_FR", "N", "."], ["sage", "400", "onboarding_user", "onboarding_user|340", "5058f1af8388633f609cadb75a75dc9d", "it_IT", "N", ""], ["sage", "410", "onboarding_user", "onboarding_user|340", "5058f1af8388633f609cadb75a75dc9d", "pl_PL", "N", ""], ["sage", "420", "onboarding_user", "onboarding_user|340", "5058f1af8388633f609cadb75a75dc9d", "pt_BR", "N", ""], ["sage", "430", "onboarding_user", "onboarding_user|340", "5058f1af8388633f609cadb75a75dc9d", "pt_PT", "N", ""], ["sage", "440", "onboarding_user", "onboarding_user|340", "5058f1af8388633f609cadb75a75dc9d", "zh_CN", "N", ""], ["sage", "450", "onboarding_user", "", "f9da1f20f0d305cf4017d39c810c1974", "en_US", "Y", "You can now connect with your"], ["sage", "460", "onboarding_user", "onboarding_user|450", "f9da1f20f0d305cf4017d39c810c1974", "ar_<PERSON>", "N", ""], ["sage", "470", "onboarding_user", "onboarding_user|450", "f9da1f20f0d305cf4017d39c810c1974", "de_DE", "N", ""], ["sage", "480", "onboarding_user", "onboarding_user|450", "f9da1f20f0d305cf4017d39c810c1974", "en_GB", "N", ""], ["sage", "490", "onboarding_user", "onboarding_user|450", "f9da1f20f0d305cf4017d39c810c1974", "es_ES", "N", ""], ["sage", "500", "onboarding_user", "onboarding_user|450", "f9da1f20f0d305cf4017d39c810c1974", "fr_FR", "N", "Vous pouvez maintenant vous y connecter avec votre identifiant"], ["sage", "510", "onboarding_user", "onboarding_user|450", "f9da1f20f0d305cf4017d39c810c1974", "it_IT", "N", ""], ["sage", "520", "onboarding_user", "onboarding_user|450", "f9da1f20f0d305cf4017d39c810c1974", "pl_PL", "N", ""], ["sage", "530", "onboarding_user", "onboarding_user|450", "f9da1f20f0d305cf4017d39c810c1974", "pt_BR", "N", ""], ["sage", "540", "onboarding_user", "onboarding_user|450", "f9da1f20f0d305cf4017d39c810c1974", "pt_PT", "N", ""], ["sage", "550", "onboarding_user", "onboarding_user|450", "f9da1f20f0d305cf4017d39c810c1974", "zh_CN", "N", ""], ["sage", "560", "onboarding_user", "", "81e3ddac8ffc4d2dda377b2953ebe0a7", "en_US", "Y", "If you have not established a SageID account, the authentication page will allow you to create one."], ["sage", "570", "onboarding_user", "onboarding_user|560", "81e3ddac8ffc4d2dda377b2953ebe0a7", "ar_<PERSON>", "N", ""], ["sage", "580", "onboarding_user", "onboarding_user|560", "81e3ddac8ffc4d2dda377b2953ebe0a7", "de_DE", "N", ""], ["sage", "590", "onboarding_user", "onboarding_user|560", "81e3ddac8ffc4d2dda377b2953ebe0a7", "en_GB", "N", ""], ["sage", "600", "onboarding_user", "onboarding_user|560", "81e3ddac8ffc4d2dda377b2953ebe0a7", "es_ES", "N", ""], ["sage", "610", "onboarding_user", "onboarding_user|560", "81e3ddac8ffc4d2dda377b2953ebe0a7", "fr_FR", "N", "Si vous n'avez pas encore d'identifiant SageID, la page d'authentification vous permettra d'en créer un"], ["sage", "620", "onboarding_user", "onboarding_user|560", "81e3ddac8ffc4d2dda377b2953ebe0a7", "it_IT", "N", ""], ["sage", "630", "onboarding_user", "onboarding_user|560", "81e3ddac8ffc4d2dda377b2953ebe0a7", "pl_PL", "N", ""], ["sage", "640", "onboarding_user", "onboarding_user|560", "81e3ddac8ffc4d2dda377b2953ebe0a7", "pt_BR", "N", ""], ["sage", "650", "onboarding_user", "onboarding_user|560", "81e3ddac8ffc4d2dda377b2953ebe0a7", "pt_PT", "N", ""], ["sage", "660", "onboarding_user", "onboarding_user|560", "81e3ddac8ffc4d2dda377b2953ebe0a7", "zh_CN", "N", ""], ["sage", "670", "onboarding_user", "", "a55765c16f25a04c736a1b6166e9067b", "en_US", "Y", "Connect to"], ["sage", "680", "onboarding_user", "onboarding_user|670", "a55765c16f25a04c736a1b6166e9067b", "ar_<PERSON>", "N", ""], ["sage", "690", "onboarding_user", "onboarding_user|670", "a55765c16f25a04c736a1b6166e9067b", "de_DE", "N", ""], ["sage", "700", "onboarding_user", "onboarding_user|670", "a55765c16f25a04c736a1b6166e9067b", "en_GB", "N", ""], ["sage", "710", "onboarding_user", "onboarding_user|670", "a55765c16f25a04c736a1b6166e9067b", "es_ES", "N", ""], ["sage", "720", "onboarding_user", "onboarding_user|670", "a55765c16f25a04c736a1b6166e9067b", "fr_FR", "N", "Se connecter à"], ["sage", "730", "onboarding_user", "onboarding_user|670", "a55765c16f25a04c736a1b6166e9067b", "it_IT", "N", ""], ["sage", "740", "onboarding_user", "onboarding_user|670", "a55765c16f25a04c736a1b6166e9067b", "pl_PL", "N", ""], ["sage", "750", "onboarding_user", "onboarding_user|670", "a55765c16f25a04c736a1b6166e9067b", "pt_BR", "N", ""], ["sage", "760", "onboarding_user", "onboarding_user|670", "a55765c16f25a04c736a1b6166e9067b", "pt_PT", "N", ""], ["sage", "770", "onboarding_user", "onboarding_user|670", "a55765c16f25a04c736a1b6166e9067b", "zh_CN", "N", ""], ["sage", "780", "onboarding_user", "", "0579762f5f581cd7154a151ad0f68013", "en_US", "Y", "<PERSON><PERSON>,"], ["sage", "790", "onboarding_user", "onboarding_user|780", "0579762f5f581cd7154a151ad0f68013", "ar_<PERSON>", "N", ""], ["sage", "800", "onboarding_user", "onboarding_user|780", "0579762f5f581cd7154a151ad0f68013", "de_DE", "N", ""], ["sage", "810", "onboarding_user", "onboarding_user|780", "0579762f5f581cd7154a151ad0f68013", "en_GB", "N", ""], ["sage", "820", "onboarding_user", "onboarding_user|780", "0579762f5f581cd7154a151ad0f68013", "es_ES", "N", ""], ["sage", "830", "onboarding_user", "onboarding_user|780", "0579762f5f581cd7154a151ad0f68013", "fr_FR", "N", "Cordialement,"], ["sage", "840", "onboarding_user", "onboarding_user|780", "0579762f5f581cd7154a151ad0f68013", "it_IT", "N", ""], ["sage", "850", "onboarding_user", "onboarding_user|780", "0579762f5f581cd7154a151ad0f68013", "pl_PL", "N", ""], ["sage", "860", "onboarding_user", "onboarding_user|780", "0579762f5f581cd7154a151ad0f68013", "pt_BR", "N", ""], ["sage", "870", "onboarding_user", "onboarding_user|780", "0579762f5f581cd7154a151ad0f68013", "pt_PT", "N", ""], ["sage", "880", "onboarding_user", "onboarding_user|780", "0579762f5f581cd7154a151ad0f68013", "zh_CN", "N", ""], ["sage", "890", "onboarding_user", "", "51f571758d313dd9aa5299270b965601", "en_US", "Y", "SageID."], ["sage", "900", "onboarding_user", "onboarding_user|890", "51f571758d313dd9aa5299270b965601", "ar_<PERSON>", "N", ""], ["sage", "910", "onboarding_user", "onboarding_user|890", "51f571758d313dd9aa5299270b965601", "de_DE", "N", ""], ["sage", "920", "onboarding_user", "onboarding_user|890", "51f571758d313dd9aa5299270b965601", "en_GB", "N", ""], ["sage", "930", "onboarding_user", "onboarding_user|890", "51f571758d313dd9aa5299270b965601", "es_ES", "N", ""], ["sage", "940", "onboarding_user", "onboarding_user|890", "51f571758d313dd9aa5299270b965601", "fr_FR", "N", "SageID."], ["sage", "950", "onboarding_user", "onboarding_user|890", "51f571758d313dd9aa5299270b965601", "it_IT", "N", ""], ["sage", "960", "onboarding_user", "onboarding_user|890", "51f571758d313dd9aa5299270b965601", "pl_PL", "N", ""], ["sage", "970", "onboarding_user", "onboarding_user|890", "51f571758d313dd9aa5299270b965601", "pt_BR", "N", ""], ["sage", "980", "onboarding_user", "onboarding_user|890", "51f571758d313dd9aa5299270b965601", "pt_PT", "N", ""], ["sage", "990", "onboarding_user", "onboarding_user|890", "51f571758d313dd9aa5299270b965601", "zh_CN", "N", ""], ["sage", "10", "onboarding_tenant", "", "c1a5298f939e87e8f962a5edfc206918", "en_US", "Y", "Hi"], ["sage", "20", "onboarding_tenant", "onboarding_tenant|10", "c1a5298f939e87e8f962a5edfc206918", "ar_<PERSON>", "N", ""], ["sage", "30", "onboarding_tenant", "onboarding_tenant|10", "c1a5298f939e87e8f962a5edfc206918", "de_DE", "N", ""], ["sage", "40", "onboarding_tenant", "onboarding_tenant|10", "c1a5298f939e87e8f962a5edfc206918", "en_GB", "N", ""], ["sage", "50", "onboarding_tenant", "onboarding_tenant|10", "c1a5298f939e87e8f962a5edfc206918", "es_ES", "N", ""], ["sage", "60", "onboarding_tenant", "onboarding_tenant|10", "c1a5298f939e87e8f962a5edfc206918", "fr_FR", "N", "Bonjour"], ["sage", "70", "onboarding_tenant", "onboarding_tenant|10", "c1a5298f939e87e8f962a5edfc206918", "it_IT", "N", ""], ["sage", "80", "onboarding_tenant", "onboarding_tenant|10", "c1a5298f939e87e8f962a5edfc206918", "pl_PL", "N", ""], ["sage", "90", "onboarding_tenant", "onboarding_tenant|10", "c1a5298f939e87e8f962a5edfc206918", "pt_BR", "N", ""], ["sage", "100", "onboarding_tenant", "onboarding_tenant|10", "c1a5298f939e87e8f962a5edfc206918", "pt_PT", "N", ""], ["sage", "110", "onboarding_tenant", "onboarding_tenant|10", "c1a5298f939e87e8f962a5edfc206918", "zh_CN", "N", ""], ["sage", "120", "onboarding_tenant", "", "c0cb5f0fcf239ab3d9c1fcd31fff1efc", "en_US", "Y", ","], ["sage", "130", "onboarding_tenant", "onboarding_tenant|120", "c0cb5f0fcf239ab3d9c1fcd31fff1efc", "ar_<PERSON>", "N", ""], ["sage", "140", "onboarding_tenant", "onboarding_tenant|120", "c0cb5f0fcf239ab3d9c1fcd31fff1efc", "de_DE", "N", ""], ["sage", "150", "onboarding_tenant", "onboarding_tenant|120", "c0cb5f0fcf239ab3d9c1fcd31fff1efc", "en_GB", "N", ""], ["sage", "160", "onboarding_tenant", "onboarding_tenant|120", "c0cb5f0fcf239ab3d9c1fcd31fff1efc", "es_ES", "N", ""], ["sage", "170", "onboarding_tenant", "onboarding_tenant|120", "c0cb5f0fcf239ab3d9c1fcd31fff1efc", "fr_FR", "N", ","], ["sage", "180", "onboarding_tenant", "onboarding_tenant|120", "c0cb5f0fcf239ab3d9c1fcd31fff1efc", "it_IT", "N", ""], ["sage", "190", "onboarding_tenant", "onboarding_tenant|120", "c0cb5f0fcf239ab3d9c1fcd31fff1efc", "pl_PL", "N", ""], ["sage", "200", "onboarding_tenant", "onboarding_tenant|120", "c0cb5f0fcf239ab3d9c1fcd31fff1efc", "pt_BR", "N", ""], ["sage", "210", "onboarding_tenant", "onboarding_tenant|120", "c0cb5f0fcf239ab3d9c1fcd31fff1efc", "pt_PT", "N", ""], ["sage", "220", "onboarding_tenant", "onboarding_tenant|120", "c0cb5f0fcf239ab3d9c1fcd31fff1efc", "zh_CN", "N", ""], ["sage", "230", "onboarding_tenant", "", "9819fce8eebe00198efa537109e99037", "en_US", "Y", "Welcome to"], ["sage", "240", "onboarding_tenant", "onboarding_tenant|230", "9819fce8eebe00198efa537109e99037", "ar_<PERSON>", "N", ""], ["sage", "250", "onboarding_tenant", "onboarding_tenant|230", "9819fce8eebe00198efa537109e99037", "de_DE", "N", ""], ["sage", "260", "onboarding_tenant", "onboarding_tenant|230", "9819fce8eebe00198efa537109e99037", "en_GB", "N", ""], ["sage", "270", "onboarding_tenant", "onboarding_tenant|230", "9819fce8eebe00198efa537109e99037", "es_ES", "N", ""], ["sage", "280", "onboarding_tenant", "onboarding_tenant|230", "9819fce8eebe00198efa537109e99037", "fr_FR", "N", "Bienvenu(e) sur"], ["sage", "290", "onboarding_tenant", "onboarding_tenant|230", "9819fce8eebe00198efa537109e99037", "it_IT", "N", ""], ["sage", "300", "onboarding_tenant", "onboarding_tenant|230", "9819fce8eebe00198efa537109e99037", "pl_PL", "N", ""], ["sage", "310", "onboarding_tenant", "onboarding_tenant|230", "9819fce8eebe00198efa537109e99037", "pt_BR", "N", ""], ["sage", "320", "onboarding_tenant", "onboarding_tenant|230", "9819fce8eebe00198efa537109e99037", "pt_PT", "N", ""], ["sage", "330", "onboarding_tenant", "onboarding_tenant|230", "9819fce8eebe00198efa537109e99037", "zh_CN", "N", ""], ["sage", "340", "onboarding_tenant", "", "e4279e40c5c50aff88bb684d16f24e8f", "en_US", "Y", ". \n\t\t\t\t\tYour application"], ["sage", "350", "onboarding_tenant", "onboarding_tenant|340", "e4279e40c5c50aff88bb684d16f24e8f", "ar_<PERSON>", "N", ""], ["sage", "360", "onboarding_tenant", "onboarding_tenant|340", "e4279e40c5c50aff88bb684d16f24e8f", "de_DE", "N", ""], ["sage", "370", "onboarding_tenant", "onboarding_tenant|340", "e4279e40c5c50aff88bb684d16f24e8f", "en_GB", "N", ""], ["sage", "380", "onboarding_tenant", "onboarding_tenant|340", "e4279e40c5c50aff88bb684d16f24e8f", "es_ES", "N", ""], ["sage", "390", "onboarding_tenant", "onboarding_tenant|340", "e4279e40c5c50aff88bb684d16f24e8f", "fr_FR", "N", ". Votre application"], ["sage", "400", "onboarding_tenant", "onboarding_tenant|340", "e4279e40c5c50aff88bb684d16f24e8f", "it_IT", "N", ""], ["sage", "410", "onboarding_tenant", "onboarding_tenant|340", "e4279e40c5c50aff88bb684d16f24e8f", "pl_PL", "N", ""], ["sage", "420", "onboarding_tenant", "onboarding_tenant|340", "e4279e40c5c50aff88bb684d16f24e8f", "pt_BR", "N", ""], ["sage", "430", "onboarding_tenant", "onboarding_tenant|340", "e4279e40c5c50aff88bb684d16f24e8f", "pt_PT", "N", ""], ["sage", "440", "onboarding_tenant", "onboarding_tenant|340", "e4279e40c5c50aff88bb684d16f24e8f", "zh_CN", "N", ""], ["sage", "450", "onboarding_tenant", "", "a10fb6cb8511debb65203f34a01e7bc3", "en_US", "Y", "is ready."], ["sage", "460", "onboarding_tenant", "onboarding_tenant|450", "a10fb6cb8511debb65203f34a01e7bc3", "ar_<PERSON>", "N", ""], ["sage", "470", "onboarding_tenant", "onboarding_tenant|450", "a10fb6cb8511debb65203f34a01e7bc3", "de_DE", "N", ""], ["sage", "480", "onboarding_tenant", "onboarding_tenant|450", "a10fb6cb8511debb65203f34a01e7bc3", "en_GB", "N", ""], ["sage", "490", "onboarding_tenant", "onboarding_tenant|450", "a10fb6cb8511debb65203f34a01e7bc3", "es_ES", "N", ""], ["sage", "500", "onboarding_tenant", "onboarding_tenant|450", "a10fb6cb8511debb65203f34a01e7bc3", "fr_FR", "N", "est prête."], ["sage", "510", "onboarding_tenant", "onboarding_tenant|450", "a10fb6cb8511debb65203f34a01e7bc3", "it_IT", "N", ""], ["sage", "520", "onboarding_tenant", "onboarding_tenant|450", "a10fb6cb8511debb65203f34a01e7bc3", "pl_PL", "N", ""], ["sage", "530", "onboarding_tenant", "onboarding_tenant|450", "a10fb6cb8511debb65203f34a01e7bc3", "pt_BR", "N", ""], ["sage", "540", "onboarding_tenant", "onboarding_tenant|450", "a10fb6cb8511debb65203f34a01e7bc3", "pt_PT", "N", ""], ["sage", "550", "onboarding_tenant", "onboarding_tenant|450", "a10fb6cb8511debb65203f34a01e7bc3", "zh_CN", "N", ""], ["sage", "560", "onboarding_tenant", "", "854dd4c0e354a39d873239121bfea3f4", "en_US", "Y", "You can now connect with your SageID. If you have not established a SageID account, the authentication page will allow you to create one."], ["sage", "570", "onboarding_tenant", "onboarding_tenant|560", "854dd4c0e354a39d873239121bfea3f4", "ar_<PERSON>", "N", ""], ["sage", "580", "onboarding_tenant", "onboarding_tenant|560", "854dd4c0e354a39d873239121bfea3f4", "de_DE", "N", ""], ["sage", "590", "onboarding_tenant", "onboarding_tenant|560", "854dd4c0e354a39d873239121bfea3f4", "en_GB", "N", ""], ["sage", "600", "onboarding_tenant", "onboarding_tenant|560", "854dd4c0e354a39d873239121bfea3f4", "es_ES", "N", ""], ["sage", "610", "onboarding_tenant", "onboarding_tenant|560", "854dd4c0e354a39d873239121bfea3f4", "fr_FR", "N", "Vous pouvez maintenant vous y connecter avec votre identifiant SageID. Si vous n'avez pas encore d'identifiant SageID, la page d'authentification vous permettra d'en créer un."], ["sage", "620", "onboarding_tenant", "onboarding_tenant|560", "854dd4c0e354a39d873239121bfea3f4", "it_IT", "N", ""], ["sage", "630", "onboarding_tenant", "onboarding_tenant|560", "854dd4c0e354a39d873239121bfea3f4", "pl_PL", "N", ""], ["sage", "640", "onboarding_tenant", "onboarding_tenant|560", "854dd4c0e354a39d873239121bfea3f4", "pt_BR", "N", ""], ["sage", "650", "onboarding_tenant", "onboarding_tenant|560", "854dd4c0e354a39d873239121bfea3f4", "pt_PT", "N", ""], ["sage", "660", "onboarding_tenant", "onboarding_tenant|560", "854dd4c0e354a39d873239121bfea3f4", "zh_CN", "N", ""], ["sage", "670", "onboarding_tenant", "", "a55765c16f25a04c736a1b6166e9067b", "en_US", "Y", "Connect to"], ["sage", "680", "onboarding_tenant", "onboarding_tenant|670", "a55765c16f25a04c736a1b6166e9067b", "ar_<PERSON>", "N", ""], ["sage", "690", "onboarding_tenant", "onboarding_tenant|670", "a55765c16f25a04c736a1b6166e9067b", "de_DE", "N", ""], ["sage", "700", "onboarding_tenant", "onboarding_tenant|670", "a55765c16f25a04c736a1b6166e9067b", "en_GB", "N", ""], ["sage", "710", "onboarding_tenant", "onboarding_tenant|670", "a55765c16f25a04c736a1b6166e9067b", "es_ES", "N", ""], ["sage", "720", "onboarding_tenant", "onboarding_tenant|670", "a55765c16f25a04c736a1b6166e9067b", "fr_FR", "N", "Se connecter à"], ["sage", "730", "onboarding_tenant", "onboarding_tenant|670", "a55765c16f25a04c736a1b6166e9067b", "it_IT", "N", ""], ["sage", "740", "onboarding_tenant", "onboarding_tenant|670", "a55765c16f25a04c736a1b6166e9067b", "pl_PL", "N", ""], ["sage", "750", "onboarding_tenant", "onboarding_tenant|670", "a55765c16f25a04c736a1b6166e9067b", "pt_BR", "N", ""], ["sage", "760", "onboarding_tenant", "onboarding_tenant|670", "a55765c16f25a04c736a1b6166e9067b", "pt_PT", "N", ""], ["sage", "770", "onboarding_tenant", "onboarding_tenant|670", "a55765c16f25a04c736a1b6166e9067b", "zh_CN", "N", ""], ["sage", "780", "onboarding_tenant", "", "0579762f5f581cd7154a151ad0f68013", "en_US", "Y", "<PERSON><PERSON>,"], ["sage", "790", "onboarding_tenant", "onboarding_tenant|780", "0579762f5f581cd7154a151ad0f68013", "ar_<PERSON>", "N", ""], ["sage", "800", "onboarding_tenant", "onboarding_tenant|780", "0579762f5f581cd7154a151ad0f68013", "de_DE", "N", ""], ["sage", "810", "onboarding_tenant", "onboarding_tenant|780", "0579762f5f581cd7154a151ad0f68013", "en_GB", "N", ""], ["sage", "820", "onboarding_tenant", "onboarding_tenant|780", "0579762f5f581cd7154a151ad0f68013", "es_ES", "N", ""], ["sage", "830", "onboarding_tenant", "onboarding_tenant|780", "0579762f5f581cd7154a151ad0f68013", "fr_FR", "N", "Cordialement,"], ["sage", "840", "onboarding_tenant", "onboarding_tenant|780", "0579762f5f581cd7154a151ad0f68013", "it_IT", "N", ""], ["sage", "850", "onboarding_tenant", "onboarding_tenant|780", "0579762f5f581cd7154a151ad0f68013", "pl_PL", "N", ""], ["sage", "860", "onboarding_tenant", "onboarding_tenant|780", "0579762f5f581cd7154a151ad0f68013", "pt_BR", "N", ""], ["sage", "870", "onboarding_tenant", "onboarding_tenant|780", "0579762f5f581cd7154a151ad0f68013", "pt_PT", "N", ""], ["sage", "880", "onboarding_tenant", "onboarding_tenant|780", "0579762f5f581cd7154a151ad0f68013", "zh_CN", "N", ""]]}}}