{"fromVersion": "56.0.2", "toVersion": "56.0.3", "gitHead": "908930327d079893f426f64651460ecbc1cd475b", "commands": [{"isSysPool": true, "sql": ["", "", "CREATE EXTENSION IF NOT EXISTS pgcrypto;", ""]}, {"isSysPool": true, "sql": ["", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.get_config(setting_name varchar)", "RETURNS varchar AS", "$$", "declare", "    setting_value varchar;", "BEGIN", "    SELECT current_setting(setting_name) into setting_value;", "    RETURN setting_value;", "EXCEPTION", "    WHEN OTHERS THEN", "    RETURN NULL;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_triggers(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "begin", "\trecord_count = 0;", "    FOR triggerRecord IN", "    \tSELECT trigger_name, event_object_table", "    \tFROM information_schema.triggers", "    \tWHERE trigger_schema = schema_name AND (name_filter = '' OR event_object_table = name_filter)", "\tLOOP", "\t\trecord_count = record_count + 1;", "        EXECUTE 'DROP TRIGGER ' || triggerRecord.trigger_name || ' ON ' || schema_name || '.\"' || triggerRecord.event_object_table || '\";';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_notify_functions(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "BEGIN", "    record_count = 0;", "    FOR triggerRecord IN", "    SELECT routine_name", "    FROM information_schema.routines", "    WHERE specific_schema = schema_name and routine_name IN (name_filter || '_notify_deleted', name_filter || '_notify_created', name_filter || '_notify_updated')", "    LOOP", "        record_count = record_count + 1;", "        EXECUTE 'DROP FUNCTION ' || schema_name || '.' || triggerRecord.routine_name || ';';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id'), '') INTO USER_ID;", "        IF (USER_ID <> '') THEN", "            NEW._create_user :=  CAST(USER_ID AS INT8);", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id'), '') INTO USER_ID;", "        IF (USER_ID <> '') THEN", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.set_sync_tick()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._sync_tick :=  pg_current_xact_id();", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.signedInt32(a bigint)", "RETURNS bigint AS", "$$", "DECLARE", "BEGIN", "\t-- Convert to 32 bit signed (if leftmost bit is 1, it's a negative number)", "  \tIF (a > 2^31) THEN", "    \tRETURN a - (2^32)::bigint;", "  \tEND IF;", "  \tRETURN a;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.imul(a bigint, b bigint)", "RETURNS bigint AS", "$$", "DECLARE", "    aHi bigint;", "    aLo bigint;", "    bHi bigint;", "    bLo bigint;", "    res bigint;", "BEGIN", "    aHi = %%SCHEMA_NAME%%.zeroFillShift(a, 16) & 65535;", "    aLo = a & 65535;", "    bHi = %%SCHEMA_NAME%%.zeroFillShift(b, 16) & 65535;", "    bLo = b & 65535;", "    res = ((aLo * bLo) + %%SCHEMA_NAME%%.zeroFillShift(((aHi * bLo + aLo * bHi) << 16) % (2^32)::bigint, 0)) | 0;", "    RETURN %%SCHEMA_NAME%%.signedInt32(res);", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.zeroFillShift(a bigint, b int)", "RETURNS bigint AS", "$$", "DECLARE", "  \tres bigint;", "BEGIN", "\tIF (a < 0) THEN", "\t\tres = a + 2^32;", "\tELSE", "\t\tres = a;", "\tEND IF;", "\tres = res >> b;", "\tRETURN res;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.nanoid(\"size\" int4 DEFAULT 21)", "    RETURNS text", "    LANGUAGE plpgsql", "    STABLE", "    AS", "    $$", "    DECLARE", "        id text := '';", "        i int := 0;", "        urlAl<PERSON><PERSON> char(64) := 'ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW';", "        bytes bytea;", "        byte int;", "        pos int;", "    BEGIN", "        SELECT gen_random_bytes(size) INTO bytes;", "    WHILE i < size LOOP", "        byte := get_byte(bytes, i);", "        pos := (byte & 63) + 1; -- + 1 because substr starts at 1", "        id := id || substr(urlAlphabet, pos, 1);", "        i = i + 1;", "    END LOOP;", "    RETURN id;", "    END", "    $$", "    ;", " ", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.audit_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        -- parameters", "        p_root_table_name VARCHAR;", "        p_constructor VARCHAR;", "", "        -- audit variables", "        is_audit_enabled VARCHAR;", "        tenant_id VARCHAR;", "        rid INT8;", "        login_email VARCHAR;", "        user_id VARCHAR;", "        locale VARCHAR;", "        log_record RECORD;", "", "        -- notify variables", "        origin_id VARCHAR;", "        notify_all_disabled VARCHAR;", "        notify_tenant_disabled VARCHAR;", "        notification_id VARCHAR;", "        user_email VARCHAR;", "        constructor VARCHAR;", "        event VARCHAR;", "        topic VARCHAR;", "        envelope VARCHAR;", "        payload VARCHAR;", "    BEGIN", "        p_root_table_name := TG_ARGV[0];", "        p_constructor := TG_ARGV[1];", "", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.is_audit_enabled'), 'false') INTO is_audit_enabled;", "        IF (is_audit_enabled <> 'true') THEN", "            RETURN NEW;", "        END IF;", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.login_email'), '') INTO login_email;", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id'), '') INTO user_id;", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.locale'), 'base') INTO locale;", "", "        tenant_id := COALESCE(NEW._tenant_id, OLD._tenant_id);", "        rid := COALESCE(NEW._id, OLD._id);", "", "        IF p_constructor != '' THEN", "            constructor := p_constructor;", "        ELSE", "            constructor := COALESCE(NEW._constructor, OLD._constructor);", "        END IF;", "", "", "        SELECT * FROM %%SCHEMA_NAME%%.sys_audit_log", "        WHERE root_table_name = p_root_table_name", "            AND record_id = rid", "            AND transaction_id::TEXT = pg_current_xact_id()::TEXT", "        INTO log_record;", "", "        IF log_record IS NULL THEN", "            RAISE NOTICE 'Inserting new audit log record %:%', p_root_table_name, NEW._id;", "            IF p_root_table_name = TG_TABLE_NAME THEN", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (node_name, root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick, _create_user, _update_user)", "                VALUES (constructor, p_root_table_name, tenant_id, rid, TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), OLD._update_tick, NEW._update_tick, user_id::INT8, user_id::INT8);", "            ELSE", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (node_name, root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick)", "                VALUES (constructor, p_root_table_name, tenant_id, rid, TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), NULL, NULL);", "            END IF;", "            RAISE NOTICE 'Inserted  new audit log record root_table=%, table=%, _id=%', p_root_table_name, TG_TABLE_NAME, NEW._id;", "        ELSE", "            RAISE NOTICE 'Updating audit log record %:%', p_root_table_name, NEW._id;", "            UPDATE %%SCHEMA_NAME%%.sys_audit_log", "            SET record_data = log_record.record_data || to_jsonb(NEW)", "            WHERE root_table_name = p_root_table_name", "                AND record_id = NEW._id", "                AND transaction_id = pg_current_xact_id()::TEXT;", "            RAISE NOTICE 'Updated  audit log record %:%', p_root_table_name, NEW._id;", "        END IF;", "", "        IF p_root_table_name = TG_TABLE_NAME THEN", "            SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.notification.disable.ALL'), 'false') INTO notify_all_disabled;", "            SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.notification.disable.t_' || tenant_id), 'false') INTO notify_tenant_disabled;", "", "            IF (notify_all_disabled <> 'true' and notify_tenant_disabled <> 'true') THEN", "                SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.origin_id'), '') INTO origin_id;", "                SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.user_email'), '') INTO user_email;", "                SELECT %%SCHEMA_NAME%%.nanoid() INTO notification_id;", "", "                CASE TG_OP", "                    WHEN 'INSERT' THEN event := 'created';", "                    WHEN 'UPDATE' THEN event := 'updated';", "                    WHEN 'DELETE' THEN event := 'deleted';", "                END CASE;", "", "                topic := constructor || '/' || event;", "                payload := '{ \"_id\":' || rid || ', \"_updateTick\":' || COALESCE(NEW._update_tick, OLD._update_tick) || '}';", "", "                RAISE NOTICE 'Inserted new notification %:%', topic, notification_id;", "                INSERT INTO %%SCHEMA_NAME%%.sys_notification", "                    (tenant_id, origin_id, notification_id, reply_id, reply_topic, user_email, login, locale,", "                    topic, payload, status, _source_id, _update_tick, _create_stamp, _update_stamp)", "                VALUES (tenant_id, origin_id, notification_id, '', '', user_email, login_email, locale,", "                    topic, payload, 'pending', '', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);", "", "                RAISE NOTICE 'Notifying %:%', TG_OP, event;", "                PERFORM pg_notify('notification_queued', '{\"data\":\"{\\\"topic\\\":\\\"' || event || '\\\"}\"}');", "            END IF;", "        END IF;", "", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", ""]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["allow to display changelog in the app", "experimental", false, "@sage/xtrem-system", false, "changelog"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["enable dev-only features", "experimental", false, "@sage/xtrem-system", false, "devTools"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Is a demo tenant", "released", false, "@sage/xtrem-system", false, "isDemoTenant"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Enable PIN code authentication feature", "released", false, "@sage/xtrem-system", false, "sysDeviceToken"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Enable Tags feature", "released", false, "@sage/xtrem-system", false, "tags"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Notification center", "experimental", false, "@sage/xtrem-communication", false, "notificationCenter"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Authorization access control", "released", false, "@sage/xtrem-authorization", false, "authorizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Enable Auditing feature", "released", false, "@sage/xtrem-auditing", false, "auditing"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Auditing option", "workInProgress", true, "@sage/xtrem-auditing", false, "auditingOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Enable Workflow feature", "released", false, "@sage/xtrem-workflow", false, "workflow"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow advanced features (not yet released)", "workInProgress", false, "@sage/xtrem-workflow", false, "workflowAdvanced"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow option (obsolete)", "workInProgress", true, "@sage/xtrem-workflow", false, "workflowOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Synchronization", "released", false, "@sage/xtrem-interop", true, "synchronizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["enable ReportAssignment feature", "workInProgress", false, "@sage/xtrem-reporting", false, "reportAssignment"]}, {"isSysPool": false, "sql": "NOTIFY invalidate_category_cache, '{\"data\":\"{\\\"tenantId\\\":null,\\\"category\\\":\\\"$SHARED_NODE.SysServiceOption\\\"}\",\"containerId\":\"x3-devops00LAD3-75017\",\"excludeSelf\":true}';", "args": []}, {"isSysPool": false, "sql": ["SELECT", "                _id, email, is_active, first_name, last_name,", "                 is_administrator, is_api_user, is_demo_persona, operator_id", "            FROM %%SCHEMA_NAME%%.user WHERE _tenant_id=$1 AND email = $2"], "args": ["777777777777777777777", "<EMAIL>"], "actionDescription": "Reload setup layer for factories SysNodeTransformation,SysNodeMapping"}, {"action": "reload_setup_data", "args": {"factory": "SysNodeTransformation"}}, {"action": "reload_setup_data", "args": {"factory": "SysNodeMapping"}}], "data": {"SysNodeTransformation": {"metadata": {"rootFactoryName": "SysNodeTransformation", "name": "SysNodeTransformation", "naturalKeyColumns": ["_tenant_id", "local_node", "remote_app", "remote_app_version", "id"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "id", "type": "string"}, {"name": "is_active", "type": "boolean", "isOwnedByCustomer": true}, {"name": "remote_app", "type": "reference", "targetFactoryName": "SysApp"}, {"name": "remote_app_version", "type": "string"}, {"name": "remote_node_name", "type": "string"}, {"name": "local_node", "type": "reference", "targetFactoryName": "MetaNodeFactory"}, {"name": "filter", "type": "json", "isNullable": true}, {"name": "last_error", "type": "date", "isNullable": true}, {"name": "last_sync", "type": "date", "isNullable": true}]}, "rows": [["sage", "COMPANY_SDMO", null, "sdmo", "48.0.0", "Company", "Company", "", null, null], ["sage", "COMPANY_X3", null, "x3_connector", "48.0.0", "Company", "Company", "", null, null], ["sage", "ITEM_SDMO", null, "sdmo", "48.0.0", "<PERSON><PERSON>", "<PERSON><PERSON>", "", null, null], ["sage", "ITEM_X3", null, "x3_connector", "48.0.0", "Product", "<PERSON><PERSON>", "", null, null], ["sage", "OPERATOR_SDMO", null, "sdmo", "48.0.0", "LaborResource", "Operator", "", null, null], ["sage", "OPERATOR_X3", null, "x3_connector", "48.0.0", "EmployeeId", "Operator", "", null, null], ["sage", "RESOURCE_SDMO", null, "sdmo", "48.0.0", "BaseResource", "Resource", "", null, null], ["sage", "SITE_SDMO", null, "sdmo", "48.0.0", "Site", "Site", "", null, null], ["sage", "SITE_X3", null, "x3_connector", "48.0.0", "Site", "Site", "{\"isManufacturing\":\"true\"}", null, null], ["sage", "UOM_SDMO", null, "sdmo", "48.0.0", "UnitOfMeasure", "UnitOfMeasure", "", null, null], ["sage", "UOM_X3", null, "x3_connector", "48.0.0", "UnitOfMeasure", "UnitOfMeasure", "", null, null], ["sage", "WORKORDER_SDMO", null, "sdmo", "48.0.0", "WorkOrder", "WorkOrder", "{\"trackingStatus\":\"open\"}", null, null], ["sage", "WORKORDER_X3", null, "x3_connector", "48.0.0", "WorkOrder", "WorkOrder", "{\"trackingStatus\":\"open\"}", null, null], ["sage", "WOCOMPONENT_SDMO", null, "sdmo", "48.0.0", "WorkOrderComponent", "WorkOrderComponent", "", null, null], ["sage", "WOCOMPONENT_X3", null, "x3_connector", "48.0.0", "WorkOrderMaterialLine", "WorkOrderComponent", "", null, null], ["sage", "WOITEM_SDMO", null, "sdmo", "48.0.0", "WorkOrderReleasedItem", "WorkOrderItem", "", null, null], ["sage", "WOITEM_X3", null, "x3_connector", "48.0.0", "WorkOrderProductLine", "WorkOrderItem", "", null, null], ["sage", "WOOPERATION_SDMO", null, "sdmo", "48.0.0", "WorkOrderOperation", "WorkOrderOperation", "", null, null], ["sage", "WOOPERATION_X3", null, "x3_connector", "48.0.0", "WorkOrderOperationLine", "WorkOrderOperation", "", null, null], ["sage", "WORESOURCE_SDMO", null, "sdmo", "48.0.0", "WorkOrderOperationResource", "WorkOrderOperationResource", "", null, null], ["sage", "WORESOURCE_X3", null, "x3_connector", "48.0.0", "WorkOrderOperationLine", "WorkOrderOperationResource", "", null, null], ["sage", "RESOURCE_SDMO", null, "sdmo", "53.0.0", "BaseResource", "Resource", "", null, null], ["sage", "RESOURCE_X3", null, "x3_connector", "48.0.0", "WorkCenter", "Resource", "", null, null], ["sage", "RESOURCE_X3", null, "x3_connector", "53.0.0", "WorkCenter", "Resource", "", null, null], ["sage", "WORKORDER_SDMO", null, "sdmo", "54.0.0", "WorkOrder", "WorkOrder", "{\"trackingStatus\":\"open\"}", null, null], ["sage", "WORKORDER_X3", null, "x3_connector", "54.0.0", "WorkOrder", "WorkOrder", "{\"trackingStatus\":\"open\"}", null, null], ["sage", "TRACKING_X3", null, "x3_connector", "48.0.0", "WorkOrderOperationTracking", "ExternalTrackingHeader", null, null, null], ["sage", "TRACKING_DETAIL_X3", null, "x3_connector", "48.0.0", "WorkOrderOperationTrackingLine", "ExternalTrackingDetail", null, null, null], ["sage", "TRACKING_DETAIL_X3", null, "x3_connector", "52.0.0", "WorkOrderOperationTrackingLine", "ExternalTrackingDetail", null, null, null], ["sage", "TRACKING_SDMO", null, "sdmo", "48.0.0", "OperationTracking", "ExternalTrackingHeader", null, null, null], ["sage", "TRACKING_DETAIL_SDMO", null, "sdmo", "48.0.0", "OperationTrackingLine", "ExternalTrackingDetail", null, null, null], ["sage", "TRACKING_DETAIL_SDMO", null, "sdmo", "52.0.0", "OperationTrackingLine", "ExternalTrackingDetail", null, null, null]]}, "SysNodeMapping": {"metadata": {"isVitalChild": true, "isVitalCollectionChild": true, "rootFactoryName": "SysNodeMapping", "name": "SysNodeMapping", "naturalKeyColumns": ["_tenant_id", "transform", "_sort_value"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "_sort_value", "type": "integer"}, {"name": "transform", "type": "reference", "targetFactoryName": "SysNodeTransformation"}, {"name": "local_property", "type": "string"}, {"name": "remote_property", "type": "string"}, {"name": "kind", "type": "enum", "enumMembers": ["path_to_path", "path_to_constant", "path_to_function", "constant_to_path", "function_to_path"]}], "vitalParentColumn": {"name": "transform", "type": "reference", "targetFactoryName": "SysNodeTransformation"}}, "rows": [["sage", "10", "Company|sdmo|48.0.0|COMPANY_SDMO", "id", "id", "path_to_path"], ["sage", "20", "Company|sdmo|48.0.0|COMPANY_SDMO", "isActive", "isActive", "path_to_path"], ["sage", "10", "Company|x3_connector|48.0.0|COMPANY_X3", "id", "code", "path_to_path"], ["sage", "20", "Company|x3_connector|48.0.0|COMPANY_X3", "isActive", "true", "path_to_constant"], ["sage", "10", "Item|sdmo|48.0.0|ITEM_SDMO", "description", "description", "path_to_path"], ["sage", "20", "Item|sdmo|48.0.0|ITEM_SDMO", "id", "id", "path_to_path"], ["sage", "30", "Item|sdmo|48.0.0|ITEM_SDMO", "image", "image", "path_to_path"], ["sage", "40", "Item|sdmo|48.0.0|ITEM_SDMO", "name", "name", "path_to_path"], ["sage", "10", "Item|x3_connector|48.0.0|ITEM_X3", "description", "description1", "path_to_path"], ["sage", "20", "Item|x3_connector|48.0.0|ITEM_X3", "id", "code", "path_to_path"], ["sage", "30", "Item|x3_connector|48.0.0|ITEM_X3", "image", "picture", "path_to_path"], ["sage", "40", "Item|x3_connector|48.0.0|ITEM_X3", "name", "description2", "path_to_path"], ["sage", "10", "Operator|sdmo|48.0.0|OPERATOR_SDMO", "id", "id", "path_to_path"], ["sage", "20", "Operator|sdmo|48.0.0|OPERATOR_SDMO", "name", "name", "path_to_path"], ["sage", "30", "Operator|sdmo|48.0.0|OPERATOR_SDMO", "employeeId", "id", "path_to_path"], ["sage", "40", "Operator|sdmo|48.0.0|OPERATOR_SDMO", "productionSite", "site", "path_to_path"], ["sage", "50", "Operator|sdmo|48.0.0|OPERATOR_SDMO", "resourceId", "id", "path_to_path"], ["sage", "60", "Operator|sdmo|48.0.0|OPERATOR_SDMO", "resourceGroupId", "resourceGroup.id", "path_to_path"], ["sage", "10", "Operator|x3_connector|48.0.0|OPERATOR_X3", "id", "teamId", "path_to_path"], ["sage", "20", "Operator|x3_connector|48.0.0|OPERATOR_X3", "name", "description", "path_to_path"], ["sage", "30", "Operator|x3_connector|48.0.0|OPERATOR_X3", "employeeId", "teamId", "path_to_path"], ["sage", "40", "Operator|x3_connector|48.0.0|OPERATOR_X3", "productionSite", "site", "path_to_path"], ["sage", "60", "Operator|x3_connector|48.0.0|OPERATOR_X3", "resourceId", "workCenter", "path_to_path"], ["sage", "70", "Operator|x3_connector|48.0.0|OPERATOR_X3", "resourceGroupId", "workCenterGroup", "path_to_path"], ["sage", "10", "Resource|sdmo|48.0.0|RESOURCE_SDMO", "id", "id", "path_to_path"], ["sage", "20", "Resource|sdmo|48.0.0|RESOURCE_SDMO", "name", "name", "path_to_path"], ["sage", "30", "Resource|sdmo|48.0.0|RESOURCE_SDMO", "type", "_factory.name", "path_to_path"], ["sage", "40", "Resource|sdmo|48.0.0|RESOURCE_SDMO", "site", "site", "path_to_path"], ["sage", "50", "Resource|sdmo|48.0.0|RESOURCE_SDMO", "operations", "operationResources", "path_to_path"], ["sage", "60", "Resource|sdmo|48.0.0|RESOURCE_SDMO", "isTracked", "return payload._factory.name === \"GroupResource\" ? false : true", "path_to_function"], ["sage", "10", "Resource|x3_connector|48.0.0|RESOURCE_X3", "id", "code", "path_to_path"], ["sage", "20", "Resource|x3_connector|48.0.0|RESOURCE_X3", "name", "description", "path_to_path"], ["sage", "30", "Resource|x3_connector|48.0.0|RESOURCE_X3", "type", "type", "path_to_path"], ["sage", "40", "Resource|x3_connector|48.0.0|RESOURCE_X3", "numberOfResources", "numberOfResources", "path_to_path"], ["sage", "50", "Resource|x3_connector|48.0.0|RESOURCE_X3", "site", "manufacturingSite", "path_to_path"], ["sage", "60", "Resource|x3_connector|48.0.0|RESOURCE_X3", "operations", "workOrderOperationLines", "path_to_path"], ["sage", "70", "Resource|x3_connector|48.0.0|RESOURCE_X3", "isTracked", "return payload.type === \"subcontractor\" ? false : true", "path_to_function"], ["sage", "10", "Resource|x3_connector|53.0.0|RESOURCE_X3", "id", "code", "path_to_path"], ["sage", "20", "Resource|x3_connector|53.0.0|RESOURCE_X3", "name", "description", "path_to_path"], ["sage", "30", "Resource|x3_connector|53.0.0|RESOURCE_X3", "type", "type", "path_to_path"], ["sage", "40", "Resource|x3_connector|53.0.0|RESOURCE_X3", "numberOfResources", "numberOfResources", "path_to_path"], ["sage", "50", "Resource|x3_connector|53.0.0|RESOURCE_X3", "site", "manufacturingSite", "path_to_path"], ["sage", "60", "Resource|x3_connector|53.0.0|RESOURCE_X3", "operations", "workOrderOperationLines", "path_to_path"], ["sage", "70", "Resource|x3_connector|53.0.0|RESOURCE_X3", "isTracked", "return payload.type === \"subcontractor\" ? false : true", "path_to_function"], ["sage", "10", "Site|sdmo|48.0.0|SITE_SDMO", "id", "id", "path_to_path"], ["sage", "20", "Site|sdmo|48.0.0|SITE_SDMO", "name", "name", "path_to_path"], ["sage", "30", "Site|sdmo|48.0.0|SITE_SDMO", "legalCompany", "legalCompany", "path_to_path"], ["sage", "40", "Site|sdmo|48.0.0|SITE_SDMO", "isActive", "isActive", "path_to_path"], ["sage", "10", "Site|x3_connector|48.0.0|SITE_X3", "id", "code", "path_to_path"], ["sage", "20", "Site|x3_connector|48.0.0|SITE_X3", "name", "name", "path_to_path"], ["sage", "30", "Site|x3_connector|48.0.0|SITE_X3", "isActive", "true", "path_to_constant"], ["sage", "40", "Site|x3_connector|48.0.0|SITE_X3", "legalCompany", "legalCompany", "path_to_path"], ["sage", "50", "Site|x3_connector|48.0.0|SITE_X3", "isManufacturingSite", "isManufacturing", "path_to_path"], ["sage", "10", "UnitOfMeasure|sdmo|48.0.0|UOM_SDMO", "id", "id", "path_to_path"], ["sage", "20", "UnitOfMeasure|sdmo|48.0.0|UOM_SDMO", "decimalPlaces", "decimalDigits", "path_to_path"], ["sage", "30", "UnitOfMeasure|sdmo|48.0.0|UOM_SDMO", "unitType", "type", "path_to_path"], ["sage", "40", "UnitOfMeasure|sdmo|48.0.0|UOM_SDMO", "name", "name", "path_to_path"], ["sage", "10", "UnitOfMeasure|x3_connector|48.0.0|UOM_X3", "id", "code", "path_to_path"], ["sage", "20", "UnitOfMeasure|x3_connector|48.0.0|UOM_X3", "decimalPlaces", "numberOfDecimals", "path_to_path"], ["sage", "30", "UnitOfMeasure|x3_connector|48.0.0|UOM_X3", "unitType", "unitType", "path_to_path"], ["sage", "40", "UnitOfMeasure|x3_connector|48.0.0|UOM_X3", "name", "localizedDescription", "path_to_path"], ["sage", "10", "WorkOrderComponent|sdmo|48.0.0|WOCOMPONENT_SDMO", "id", "workOrder+componentNumber", "path_to_path"], ["sage", "20", "WorkOrderComponent|sdmo|48.0.0|WOCOMPONENT_SDMO", "description", "name", "path_to_path"], ["sage", "30", "WorkOrderComponent|sdmo|48.0.0|WOCOMPONENT_SDMO", "expectedQuantity", "requiredQuantity", "path_to_path"], ["sage", "40", "WorkOrderComponent|sdmo|48.0.0|WOCOMPONENT_SDMO", "remainingQuantity", "quantityInStockUnit", "path_to_path"], ["sage", "50", "WorkOrderComponent|sdmo|48.0.0|WOCOMPONENT_SDMO", "workOrder", "workOrder", "path_to_path"], ["sage", "60", "WorkOrderComponent|sdmo|48.0.0|WOCOMPONENT_SDMO", "_sortValue", "_sortValue", "path_to_path"], ["sage", "10", "WorkOrderComponent|x3_connector|48.0.0|WOCOMPONENT_X3", "id", "number", "path_to_path"], ["sage", "20", "WorkOrderComponent|x3_connector|48.0.0|WOCOMPONENT_X3", "description", "?", "path_to_path"], ["sage", "30", "WorkOrderComponent|x3_connector|48.0.0|WOCOMPONENT_X3", "expectedQuantity", "expectedQuantity", "path_to_path"], ["sage", "40", "WorkOrderComponent|x3_connector|48.0.0|WOCOMPONENT_X3", "remainingQuantity", "remainingQuantity", "path_to_path"], ["sage", "10", "WorkOrderItem|sdmo|48.0.0|WOITEM_SDMO", "id", "return payload.document.number+\"|\"+payload._sortValue", "path_to_function"], ["sage", "20", "WorkOrderItem|sdmo|48.0.0|WOITEM_SDMO", "description", "releasedItemName", "path_to_path"], ["sage", "30", "WorkOrderItem|sdmo|48.0.0|WOITEM_SDMO", "expectedQuantity", "releasedQuantity", "path_to_path"], ["sage", "40", "WorkOrderItem|sdmo|48.0.0|WOITEM_SDMO", "remainingQuantity", "remainingQuantity", "path_to_path"], ["sage", "50", "WorkOrderItem|sdmo|48.0.0|WOITEM_SDMO", "workOrder", "document", "path_to_path"], ["sage", "60", "WorkOrderItem|sdmo|48.0.0|WOITEM_SDMO", "_sortValue", "_sortValue", "path_to_path"], ["sage", "70", "WorkOrderItem|sdmo|48.0.0|WOITEM_SDMO", "stockUnit", "stockUnit", "path_to_path"], ["sage", "80", "WorkOrderItem|sdmo|48.0.0|WOITEM_SDMO", "item", "item", "path_to_path"], ["sage", "90", "WorkOrderItem|sdmo|48.0.0|WOITEM_SDMO", "destination", "return null", "path_to_function"], ["sage", "100", "WorkOrderItem|sdmo|48.0.0|WOITEM_SDMO", "destinationName", "return null", "path_to_function"], ["sage", "10", "WorkOrderItem|x3_connector|48.0.0|WOITEM_X3", "id", "return payload.workOrder.number+\"|\"+payload.lineNumber", "path_to_function"], ["sage", "100", "WorkOrderItem|x3_connector|48.0.0|WOITEM_X3", "item", "product", "path_to_path"], ["sage", "20", "WorkOrderItem|x3_connector|48.0.0|WOITEM_X3", "description", "product.description1", "path_to_path"], ["sage", "30", "WorkOrderItem|x3_connector|48.0.0|WOITEM_X3", "expectedQuantity", "expectedQuantity", "path_to_path"], ["sage", "40", "WorkOrderItem|x3_connector|48.0.0|WOITEM_X3", "remainingQuantity", "remainingQuantity", "path_to_path"], ["sage", "50", "WorkOrderItem|x3_connector|48.0.0|WOITEM_X3", "bomCode", "bomCode.code", "path_to_path"], ["sage", "60", "WorkOrderItem|x3_connector|48.0.0|WOITEM_X3", "workOrder", "workOrder", "path_to_path"], ["sage", "80", "WorkOrderItem|x3_connector|48.0.0|WOITEM_X3", "stockUnit", "stockUnit", "path_to_path"], ["sage", "90", "WorkOrderItem|x3_connector|48.0.0|WOITEM_X3", "_sortValue", "lineNumber", "path_to_path"], ["sage", "110", "WorkOrderItem|x3_connector|48.0.0|WOITEM_X3", "destination", "customer.code", "path_to_path"], ["sage", "120", "WorkOrderItem|x3_connector|48.0.0|WOITEM_X3", "destinationName", "customer.companyName1", "path_to_path"], ["sage", "10", "WorkOrderOperationResource|sdmo|48.0.0|WORESOURCE_SDMO", "id", "_sortValue", "path_to_path"], ["sage", "100", "WorkOrderOperationResource|sdmo|48.0.0|WORESOURCE_SDMO", "expectedRunTime", "expectedRunTime", "path_to_path"], ["sage", "110", "WorkOrderOperationResource|sdmo|48.0.0|WORESOURCE_SDMO", "startDate", "workOrderOperation.startDate", "path_to_path"], ["sage", "120", "WorkOrderOperationResource|sdmo|48.0.0|WORESOURCE_SDMO", "endDate", "workOrderOperation.endDate", "path_to_path"], ["sage", "130", "WorkOrderOperationResource|sdmo|48.0.0|WORESOURCE_SDMO", "status", "status", "path_to_path"], ["sage", "20", "WorkOrderOperationResource|sdmo|48.0.0|WORESOURCE_SDMO", "operation", "workOrderOperation", "path_to_path"], ["sage", "30", "WorkOrderOperationResource|sdmo|48.0.0|WORESOURCE_SDMO", "_sortValue", "_sortValue", "path_to_path"], ["sage", "40", "WorkOrderOperationResource|sdmo|48.0.0|WORESOURCE_SDMO", "progressStatus", "off", "path_to_constant"], ["sage", "50", "WorkOrderOperationResource|sdmo|48.0.0|WORESOURCE_SDMO", "resource", "resource", "path_to_path"], ["sage", "60", "WorkOrderOperationResource|sdmo|48.0.0|WORESOURCE_SDMO", "workOrder", "workOrder", "path_to_path"], ["sage", "70", "WorkOrderOperationResource|sdmo|48.0.0|WORESOURCE_SDMO", "actualSetupTime", "actualSetupTime", "path_to_path"], ["sage", "80", "WorkOrderOperationResource|sdmo|48.0.0|WORESOURCE_SDMO", "actualRunTime", "actualRunTime", "path_to_path"], ["sage", "90", "WorkOrderOperationResource|sdmo|48.0.0|WORESOURCE_SDMO", "expectedSetupTime", "expectedSetupTime", "path_to_path"], ["sage", "10", "WorkOrderOperationResource|x3_connector|48.0.0|WORESOURCE_X3", "id", "operationNumber", "path_to_path"], ["sage", "100", "WorkOrderOperationResource|x3_connector|48.0.0|WORESOURCE_X3", "operation", "return payload.workOrder.productionSite.code+\"|\"+payload.workOrder.number+\"|\"+payload.operationNumber", "path_to_function"], ["sage", "110", "WorkOrderOperationResource|x3_connector|48.0.0|WORESOURCE_X3", "actualSetupTime", "completedSetupTime", "path_to_path"], ["sage", "120", "WorkOrderOperationResource|x3_connector|48.0.0|WORESOURCE_X3", "actualRunTime", "completedOperationTime", "path_to_path"], ["sage", "130", "WorkOrderOperationResource|x3_connector|48.0.0|WORESOURCE_X3", "expectedSetupTime", "setupTime", "path_to_path"], ["sage", "140", "WorkOrderOperationResource|x3_connector|48.0.0|WORESOURCE_X3", "expectedRunTime", "runTime", "path_to_path"], ["sage", "150", "WorkOrderOperationResource|x3_connector|48.0.0|WORESOURCE_X3", "startDate", "startDate", "path_to_path"], ["sage", "160", "WorkOrderOperationResource|x3_connector|48.0.0|WORESOURCE_X3", "endDate", "endDate", "path_to_path"], ["sage", "170", "WorkOrderOperationResource|x3_connector|48.0.0|WORESOURCE_X3", "status", "operationStatus", "path_to_path"], ["sage", "40", "WorkOrderOperationResource|x3_connector|48.0.0|WORESOURCE_X3", "progressStatus", "off", "path_to_constant"], ["sage", "50", "WorkOrderOperationResource|x3_connector|48.0.0|WORESOURCE_X3", "resource", "expectedWorkCenter", "path_to_path"], ["sage", "60", "WorkOrderOperationResource|x3_connector|48.0.0|WORESOURCE_X3", "workOrder", "workOrder", "path_to_path"], ["sage", "70", "WorkOrderOperationResource|x3_connector|48.0.0|WORESOURCE_X3", "additionalResource", "laborWorkCenter", "path_to_path"], ["sage", "90", "WorkOrderOperationResource|x3_connector|48.0.0|WORESOURCE_X3", "_sortValue", "operationNumber", "path_to_path"], ["sage", "10", "WorkOrderOperation|sdmo|48.0.0|WOOPERATION_SDMO", "id", "operationNumber", "path_to_path"], ["sage", "100", "WorkOrderOperation|sdmo|48.0.0|WOOPERATION_SDMO", "workOrder", "workOrder", "path_to_path"], ["sage", "110", "WorkOrderOperation|sdmo|48.0.0|WOOPERATION_SDMO", "_sortValue", "_sortValue", "path_to_path"], ["sage", "120", "WorkOrderOperation|sdmo|48.0.0|WOOPERATION_SDMO", "progressStatus", "status", "path_to_path"], ["sage", "130", "WorkOrderOperation|sdmo|48.0.0|WOOPERATION_SDMO", "resources", "resources", "path_to_path"], ["sage", "140", "WorkOrderOperation|sdmo|48.0.0|WOOPERATION_SDMO", "unitOfTime", "runTimeUnit", "path_to_path"], ["sage", "170", "WorkOrderOperation|sdmo|48.0.0|WOOPERATION_SDMO", "endDate", "endDate", "path_to_path"], ["sage", "180", "WorkOrderOperation|sdmo|48.0.0|WOOPERATION_SDMO", "instructions.value", "return null", "path_to_function"], ["sage", "190", "WorkOrderOperation|sdmo|48.0.0|WOOPERATION_SDMO", "number", "operationNumber", "path_to_path"], ["sage", "20", "WorkOrderOperation|sdmo|48.0.0|WOOPERATION_SDMO", "description", "name", "path_to_path"], ["sage", "200", "WorkOrderOperation|sdmo|48.0.0|WOOPERATION_SDMO", "setupUnitOfTime", "setupTimeUnit", "path_to_path"], ["sage", "40", "WorkOrderOperation|sdmo|48.0.0|WOOPERATION_SDMO", "status", "status", "path_to_path"], ["sage", "50", "WorkOrderOperation|sdmo|48.0.0|WOOPERATION_SDMO", "startDate", "startDate", "path_to_path"], ["sage", "60", "WorkOrderOperation|sdmo|48.0.0|WOOPERATION_SDMO", "actualSetupTime", "actualSetupTime", "path_to_path"], ["sage", "70", "WorkOrderOperation|sdmo|48.0.0|WOOPERATION_SDMO", "actualRunTime", "actualRunTime", "path_to_path"], ["sage", "80", "WorkOrderOperation|sdmo|48.0.0|WOOPERATION_SDMO", "expectedSetupTime", "expectedSetupTime", "path_to_path"], ["sage", "90", "WorkOrderOperation|sdmo|48.0.0|WOOPERATION_SDMO", "expectedRunTime", "expectedRunTime", "path_to_path"], ["sage", "10", "WorkOrderOperation|x3_connector|48.0.0|WOOPERATION_X3", "id", "operationNumber", "path_to_path"], ["sage", "100", "WorkOrderOperation|x3_connector|48.0.0|WOOPERATION_X3", "workOrder", "workOrder", "path_to_path"], ["sage", "110", "WorkOrderOperation|x3_connector|48.0.0|WOOPERATION_X3", "_sortValue", "return payload.operationNumber+payload.operationSplit", "path_to_function"], ["sage", "120", "WorkOrderOperation|x3_connector|48.0.0|WOOPERATION_X3", "progressStatus", "operationStatus", "path_to_path"], ["sage", "130", "WorkOrderOperation|x3_connector|48.0.0|WOOPERATION_X3", "number", "operationNumber", "path_to_path"], ["sage", "140", "WorkOrderOperation|x3_connector|48.0.0|WOOPERATION_X3", "additionalNumber", "operationSplit", "path_to_path"], ["sage", "150", "WorkOrderOperation|x3_connector|48.0.0|WOOPERATION_X3", "endDate", "endDate", "path_to_path"], ["sage", "160", "WorkOrderOperation|x3_connector|48.0.0|WOOPERATION_X3", "unitOfTime", "return payload.timeUnit === \"minutes\" ? \"MIN\" : \"HR\"", "path_to_function"], ["sage", "170", "WorkOrderOperation|x3_connector|48.0.0|WOOPERATION_X3", "unitOfTimeValue", "timeUnit", "path_to_path"], ["sage", "180", "WorkOrderOperation|x3_connector|48.0.0|WOOPERATION_X3", "instructions.value", "return null", "path_to_function"], ["sage", "20", "WorkOrderOperation|x3_connector|48.0.0|WOOPERATION_X3", "description", "operationDescription", "path_to_path"], ["sage", "200", "WorkOrderOperation|x3_connector|48.0.0|WOOPERATION_X3", "setupUnitOfTime", "return payload.timeUnit === \"minutes\" ? \"MIN\" : \"HR\"", "path_to_function"], ["sage", "40", "WorkOrderOperation|x3_connector|48.0.0|WOOPERATION_X3", "status", "operationStatus", "path_to_path"], ["sage", "50", "WorkOrderOperation|x3_connector|48.0.0|WOOPERATION_X3", "startDate", "startDate", "path_to_path"], ["sage", "60", "WorkOrderOperation|x3_connector|48.0.0|WOOPERATION_X3", "actualSetupTime", "completedSetupTime", "path_to_path"], ["sage", "70", "WorkOrderOperation|x3_connector|48.0.0|WOOPERATION_X3", "actualRunTime", "completedOperationTime", "path_to_path"], ["sage", "80", "WorkOrderOperation|x3_connector|48.0.0|WOOPERATION_X3", "expectedSetupTime", "setupTime", "path_to_path"], ["sage", "90", "WorkOrderOperation|x3_connector|48.0.0|WOOPERATION_X3", "expectedRunTime", "runTime", "path_to_path"], ["sage", "10", "WorkOrder|sdmo|48.0.0|WORKORDER_SDMO", "id", "number", "path_to_path"], ["sage", "100", "WorkOrder|sdmo|48.0.0|WORKORDER_SDMO", "endDate", "endDate", "path_to_path"], ["sage", "20", "WorkOrder|sdmo|48.0.0|WORKORDER_SDMO", "name", "name", "path_to_path"], ["sage", "30", "WorkOrder|sdmo|48.0.0|WORKORDER_SDMO", "site", "site", "path_to_path"], ["sage", "40", "WorkOrder|sdmo|48.0.0|WORKORDER_SDMO", "startDate", "startDate", "path_to_path"], ["sage", "50", "WorkOrder|sdmo|48.0.0|WORKORDER_SDMO", "status", "type", "path_to_path"], ["sage", "60", "WorkOrder|sdmo|48.0.0|WORKORDER_SDMO", "resources", "productionOperationResources", "path_to_path"], ["sage", "70", "WorkOrder|sdmo|48.0.0|WORKORDER_SDMO", "operations", "productionOperations", "path_to_path"], ["sage", "80", "WorkOrder|sdmo|48.0.0|WORKORDER_SDMO", "items", "productionItems", "path_to_path"], ["sage", "90", "WorkOrder|sdmo|48.0.0|WORKORDER_SDMO", "components", "productionComponents", "path_to_path"], ["sage", "10", "WorkOrder|x3_connector|48.0.0|WORKORDER_X3", "id", "number", "path_to_path"], ["sage", "30", "WorkOrder|x3_connector|48.0.0|WORKORDER_X3", "site", "productionSite", "path_to_path"], ["sage", "40", "WorkOrder|x3_connector|48.0.0|WORKORDER_X3", "startDate", "startDate", "path_to_path"], ["sage", "50", "WorkOrder|x3_connector|48.0.0|WORKORDER_X3", "status", "orderStatus", "path_to_path"], ["sage", "60", "WorkOrder|x3_connector|48.0.0|WORKORDER_X3", "operations", "workOrderOperationLines", "path_to_path"], ["sage", "70", "WorkOrder|x3_connector|48.0.0|WORKORDER_X3", "resources", "workOrderOperationLines", "path_to_path"], ["sage", "80", "WorkOrder|x3_connector|48.0.0|WORKORDER_X3", "items", "workOrderProductLines", "path_to_path"], ["sage", "90", "WorkOrder|x3_connector|48.0.0|WORKORDER_X3", "endDate", "endDate", "path_to_path"], ["sage", "10", "Resource|sdmo|53.0.0|RESOURCE_SDMO", "id", "id", "path_to_path"], ["sage", "20", "Resource|sdmo|53.0.0|RESOURCE_SDMO", "name", "name", "path_to_path"], ["sage", "30", "Resource|sdmo|53.0.0|RESOURCE_SDMO", "type", "_factory.name", "path_to_path"], ["sage", "40", "Resource|sdmo|53.0.0|RESOURCE_SDMO", "site", "site", "path_to_path"], ["sage", "50", "Resource|sdmo|53.0.0|RESOURCE_SDMO", "operations", "operationResources", "path_to_path"], ["sage", "60", "Resource|sdmo|53.0.0|RESOURCE_SDMO", "isTracked", "return payload._factory.name === \"GroupResource\" ? false : true", "path_to_function"], ["sage", "55", "WorkOrder|sdmo|48.0.0|WORKORDER_SDMO", "trackingStatus", "status", "path_to_path"], ["sage", "10", "WorkOrder|sdmo|54.0.0|WORKORDER_SDMO", "id", "number", "path_to_path"], ["sage", "100", "WorkOrder|sdmo|54.0.0|WORKORDER_SDMO", "endDate", "endDate", "path_to_path"], ["sage", "20", "WorkOrder|sdmo|54.0.0|WORKORDER_SDMO", "name", "name", "path_to_path"], ["sage", "30", "WorkOrder|sdmo|54.0.0|WORKORDER_SDMO", "site", "site", "path_to_path"], ["sage", "40", "WorkOrder|sdmo|54.0.0|WORKORDER_SDMO", "startDate", "startDate", "path_to_path"], ["sage", "50", "WorkOrder|sdmo|54.0.0|WORKORDER_SDMO", "status", "type", "path_to_path"], ["sage", "55", "WorkOrder|sdmo|54.0.0|WORKORDER_SDMO", "trackingStatus", "status", "path_to_path"], ["sage", "60", "WorkOrder|sdmo|54.0.0|WORKORDER_SDMO", "resources", "productionOperationResources", "path_to_path"], ["sage", "70", "WorkOrder|sdmo|54.0.0|WORKORDER_SDMO", "operations", "productionOperations", "path_to_path"], ["sage", "80", "WorkOrder|sdmo|54.0.0|WORKORDER_SDMO", "items", "productionItems", "path_to_path"], ["sage", "90", "WorkOrder|sdmo|54.0.0|WORKORDER_SDMO", "components", "productionComponents", "path_to_path"], ["sage", "55", "WorkOrder|x3_connector|48.0.0|WORKORDER_X3", "trackingStatus", "trackingFlag", "path_to_path"], ["sage", "10", "WorkOrder|x3_connector|54.0.0|WORKORDER_X3", "id", "number", "path_to_path"], ["sage", "30", "WorkOrder|x3_connector|54.0.0|WORKORDER_X3", "site", "productionSite", "path_to_path"], ["sage", "40", "WorkOrder|x3_connector|54.0.0|WORKORDER_X3", "startDate", "startDate", "path_to_path"], ["sage", "50", "WorkOrder|x3_connector|54.0.0|WORKORDER_X3", "status", "orderStatus", "path_to_path"], ["sage", "55", "WorkOrder|x3_connector|54.0.0|WORKORDER_X3", "trackingStatus", "trackingFlag", "path_to_path"], ["sage", "60", "WorkOrder|x3_connector|54.0.0|WORKORDER_X3", "operations", "workOrderOperationLines", "path_to_path"], ["sage", "70", "WorkOrder|x3_connector|54.0.0|WORKORDER_X3", "resources", "workOrderOperationLines", "path_to_path"], ["sage", "80", "WorkOrder|x3_connector|54.0.0|WORKORDER_X3", "items", "workOrderProductLines", "path_to_path"], ["sage", "90", "WorkOrder|x3_connector|54.0.0|WORKORDER_X3", "endDate", "endDate", "path_to_path"], ["sage", "10", "ExternalTrackingDetail|sdmo|48.0.0|TRACKING_DETAIL_SDMO", "trackingHeader", "document", "path_to_path"], ["sage", "100", "ExternalTrackingDetail|sdmo|48.0.0|TRACKING_DETAIL_SDMO", "unitOfTime", "runTimeUnit", "path_to_path"], ["sage", "110", "ExternalTrackingDetail|sdmo|48.0.0|TRACKING_DETAIL_SDMO", "return '#'+payload.trackingHeader.site.id+'|'+payload.trackingHeader.workOrderId+'|'+payload.operationId", "workOrderOperation", "function_to_path"], ["sage", "20", "ExternalTrackingDetail|sdmo|48.0.0|TRACKING_DETAIL_SDMO", "line", "line", "path_to_path"], ["sage", "30", "ExternalTrackingDetail|sdmo|48.0.0|TRACKING_DETAIL_SDMO", "return '#'+payload.trackingHeader.site.id+'|'+payload.trackingHeader.workOrderId+'|'+payload.operationId+'|'+payload.operationResourceId", "operationResource", "function_to_path"], ["sage", "40", "ExternalTrackingDetail|sdmo|48.0.0|TRACKING_DETAIL_SDMO", "resource", "actualResource", "path_to_path"], ["sage", "50", "ExternalTrackingDetail|sdmo|48.0.0|TRACKING_DETAIL_SDMO", "completedQuantity", "completedQuantity", "path_to_path"], ["sage", "70", "ExternalTrackingDetail|sdmo|48.0.0|TRACKING_DETAIL_SDMO", "completedSetupTimeUOM", "actualSetupTime", "path_to_path"], ["sage", "80", "ExternalTrackingDetail|sdmo|48.0.0|TRACKING_DETAIL_SDMO", "completedRunTimeUOM", "actualRunTime", "path_to_path"], ["sage", "90", "ExternalTrackingDetail|sdmo|48.0.0|TRACKING_DETAIL_SDMO", "setupUnitOfTime", "setupTimeUnit", "path_to_path"], ["sage", "10", "ExternalTrackingDetail|sdmo|52.0.0|TRACKING_DETAIL_SDMO", "trackingHeader", "document", "path_to_path"], ["sage", "100", "ExternalTrackingDetail|sdmo|52.0.0|TRACKING_DETAIL_SDMO", "unitOfTime", "runTimeUnit", "path_to_path"], ["sage", "110", "ExternalTrackingDetail|sdmo|52.0.0|TRACKING_DETAIL_SDMO", "return '#'+payload.trackingHeader.site.id+'|'+payload.trackingHeader.workOrderId+'|'+payload.operationId", "workOrderOperation", "function_to_path"], ["sage", "20", "ExternalTrackingDetail|sdmo|52.0.0|TRACKING_DETAIL_SDMO", "line", "line", "path_to_path"], ["sage", "200", "ExternalTrackingDetail|sdmo|52.0.0|TRACKING_DETAIL_SDMO", "isCompleted", "completed", "path_to_path"], ["sage", "30", "ExternalTrackingDetail|sdmo|52.0.0|TRACKING_DETAIL_SDMO", "return '#'+payload.trackingHeader.site.id+'|'+payload.trackingHeader.workOrderId+'|'+payload.operationId+'|'+payload.operationResourceId", "operationResource", "function_to_path"], ["sage", "40", "ExternalTrackingDetail|sdmo|52.0.0|TRACKING_DETAIL_SDMO", "resource", "actualResource", "path_to_path"], ["sage", "50", "ExternalTrackingDetail|sdmo|52.0.0|TRACKING_DETAIL_SDMO", "completedQuantity", "completedQuantity", "path_to_path"], ["sage", "70", "ExternalTrackingDetail|sdmo|52.0.0|TRACKING_DETAIL_SDMO", "completedSetupTimeUOM", "actualSetupTime", "path_to_path"], ["sage", "80", "ExternalTrackingDetail|sdmo|52.0.0|TRACKING_DETAIL_SDMO", "completedRunTimeUOM", "actualRunTime", "path_to_path"], ["sage", "90", "ExternalTrackingDetail|sdmo|52.0.0|TRACKING_DETAIL_SDMO", "setupUnitOfTime", "setupTimeUnit", "path_to_path"], ["sage", "10", "ExternalTrackingDetail|x3_connector|48.0.0|TRACKING_DETAIL_X3", "line", "line", "path_to_path"], ["sage", "100", "ExternalTrackingDetail|x3_connector|48.0.0|TRACKING_DETAIL_X3", "trackingHeader", "workOrderOperationTracking", "path_to_path"], ["sage", "110", "ExternalTrackingDetail|x3_connector|48.0.0|TRACKING_DETAIL_X3", "trackingHeader.trackingDate", "postingDate", "path_to_path"], ["sage", "120", "ExternalTrackingDetail|x3_connector|48.0.0|TRACKING_DETAIL_X3", "OPE", "transaction", "constant_to_path"], ["sage", "20", "ExternalTrackingDetail|x3_connector|48.0.0|TRACKING_DETAIL_X3", "trackingHeader.workOrderId", "workOrderNumber", "path_to_path"], ["sage", "30", "ExternalTrackingDetail|x3_connector|48.0.0|TRACKING_DETAIL_X3", "operationId", "operationNumber", "path_to_path"], ["sage", "40", "ExternalTrackingDetail|x3_connector|48.0.0|TRACKING_DETAIL_X3", "resource", "actualWorkCenter", "path_to_path"], ["sage", "50", "ExternalTrackingDetail|x3_connector|48.0.0|TRACKING_DETAIL_X3", "completedQuantity", "totalCompletedQuantity", "path_to_path"], ["sage", "60", "ExternalTrackingDetail|x3_connector|48.0.0|TRACKING_DETAIL_X3", "rejectedQuantity", "actualRejectedQuantity", "path_to_path"], ["sage", "70", "ExternalTrackingDetail|x3_connector|48.0.0|TRACKING_DETAIL_X3", "completedSetupTimeUOM", "actualSetupTime", "path_to_path"], ["sage", "80", "ExternalTrackingDetail|x3_connector|48.0.0|TRACKING_DETAIL_X3", "completedRunTimeUOM", "actualRunTime", "path_to_path"], ["sage", "10", "ExternalTrackingDetail|x3_connector|52.0.0|TRACKING_DETAIL_X3", "line", "line", "path_to_path"], ["sage", "100", "ExternalTrackingDetail|x3_connector|52.0.0|TRACKING_DETAIL_X3", "trackingHeader", "workOrderOperationTracking", "path_to_path"], ["sage", "110", "ExternalTrackingDetail|x3_connector|52.0.0|TRACKING_DETAIL_X3", "trackingHeader.trackingDate", "postingDate", "path_to_path"], ["sage", "120", "ExternalTrackingDetail|x3_connector|52.0.0|TRACKING_DETAIL_X3", "OPE", "transaction", "constant_to_path"], ["sage", "20", "ExternalTrackingDetail|x3_connector|52.0.0|TRACKING_DETAIL_X3", "trackingHeader.workOrderId", "workOrderNumber", "path_to_path"], ["sage", "30", "ExternalTrackingDetail|x3_connector|52.0.0|TRACKING_DETAIL_X3", "operationId", "operationNumber", "path_to_path"], ["sage", "40", "ExternalTrackingDetail|x3_connector|52.0.0|TRACKING_DETAIL_X3", "resource", "actualWorkCenter", "path_to_path"], ["sage", "50", "ExternalTrackingDetail|x3_connector|52.0.0|TRACKING_DETAIL_X3", "completedQuantity", "totalCompletedQuantity", "path_to_path"], ["sage", "60", "ExternalTrackingDetail|x3_connector|52.0.0|TRACKING_DETAIL_X3", "rejectedQuantity", "actualRejectedQuantity", "path_to_path"], ["sage", "70", "ExternalTrackingDetail|x3_connector|52.0.0|TRACKING_DETAIL_X3", "completedSetupTimeUOM", "actualSetupTime", "path_to_path"], ["sage", "80", "ExternalTrackingDetail|x3_connector|52.0.0|TRACKING_DETAIL_X3", "completedRunTimeUOM", "actualRunTime", "path_to_path"], ["sage", "200", "ExternalTrackingDetail|x3_connector|52.0.0|TRACKING_DETAIL_X3", "return payload.isCompleted === true ? \"2\" : \"1\"", "balance", "function_to_path"], ["sage", "10", "ExternalTrackingHeader|sdmo|48.0.0|TRACKING_SDMO", "site", "site", "path_to_path"], ["sage", "20", "ExternalTrackingHeader|sdmo|48.0.0|TRACKING_SDMO", "number", "number", "path_to_path"], ["sage", "30", "ExternalTrackingHeader|sdmo|48.0.0|TRACKING_SDMO", "trackingDate", "entryDate", "path_to_path"], ["sage", "40", "ExternalTrackingHeader|sdmo|48.0.0|TRACKING_SDMO", "trackingDetails", "lines", "path_to_path"], ["sage", "50", "ExternalTrackingHeader|sdmo|48.0.0|TRACKING_SDMO", "return '#'+payload.site.id+'|'+payload.workOrderId", "workOrder", "function_to_path"], ["sage", "60", "ExternalTrackingHeader|sdmo|48.0.0|TRACKING_SDMO", "workOrderId", "1", "path_to_constant"], ["sage", "10", "ExternalTrackingHeader|x3_connector|48.0.0|TRACKING_X3", "site", "productionSite", "path_to_path"], ["sage", "20", "ExternalTrackingHeader|x3_connector|48.0.0|TRACKING_X3", "number", "trackingNumber", "path_to_path"], ["sage", "30", "ExternalTrackingHeader|x3_connector|48.0.0|TRACKING_X3", "trackingDetails", "workOrderOperationTrackingLines", "path_to_path"], ["sage", "40", "ExternalTrackingHeader|x3_connector|48.0.0|TRACKING_X3", "workOrderId", "1", "path_to_constant"]]}}}