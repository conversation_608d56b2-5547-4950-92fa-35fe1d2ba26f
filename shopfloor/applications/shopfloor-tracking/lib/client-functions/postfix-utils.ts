import * as ui from '@sage/xtrem-ui';

/**
 * This function formats a string or a Text field value by capitalizing the first letter and converting the rest
 * to lowercase, with a leading space.
 * @param {ui.fields.Text | string} field - The `field` parameter in the `formatPostfixWithLeadingCapital` function can be
 * either of type `ui.fields.Text` or `string`.
 * @returns A formatted string with the first letter capitalized and the rest of the letters in lowercase, preceded by a
 * space.
 */
export function formatPostfixWithLeadingCapital(field: ui.fields.Text | string): string {
    const value = typeof field === 'string' ? field : field?.value;
    if (!value) return '';
    return ` ${value.charAt(0).toUpperCase()}${value.slice(1).toLowerCase()}`;
}
