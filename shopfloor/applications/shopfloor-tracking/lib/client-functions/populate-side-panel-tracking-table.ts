import type { WorkOrderOperationResource } from '@sage/shopfloor-master-data-api';
import { toNumber } from '@sage/xtrem-shared';
import * as ui from '@sage/xtrem-ui';
import {
    formatActualOperationTime,
    formatRemainingOperationTime,
} from '../client-functions/common-operation-format-time';
import * as TrackingHeaderQuery from '../client-functions/tracking-header-query';
import type { TimeTrackingResource as TimeTrackingResourcePage } from '../pages/time-tracking-resource';
import type { TimeTrackingWorkOrder as TimeTrackingWorkOrderPage } from '../pages/time-tracking-work-order';

export async function populateSidePanelTrackingTable(
    page: TimeTrackingWorkOrderPage | TimeTrackingResourcePage,
    recordValue: WorkOrderOperationResource,
    table: ui.fields.Table,
    setupUnitOfTime: string,
    runUnitOfTime: string,
): Promise<void> {
    const completedTrackedTimeNew = await TrackingHeaderQuery.getInternalCompletedTime(
        page,
        recordValue?.operation.id ?? '',
        recordValue?.operation.workOrder?.id ?? '',
        `${recordValue?.resource?.id ?? ''}|${recordValue?.operation?.workOrder?.site?.id ?? ''}`,
    );

    const actualOperationTime = formatActualOperationTime(
        completedTrackedTimeNew.completedRunTime ?? 0,
        completedTrackedTimeNew.completedSetupTime ?? 0,
        'seconds',
        'seconds',
    );

    const remainingOperationTime = formatRemainingOperationTime(
        toNumber(recordValue?.expectedRunTime ?? 0),
        toNumber(recordValue?.expectedSetupTime ?? 0),
        completedTrackedTimeNew.completedRunTime ?? 0,
        completedTrackedTimeNew.completedSetupTime ?? 0,
        setupUnitOfTime,
        runUnitOfTime,
    );

    table.value = [];
    table.addRecord({
        trackedTime: actualOperationTime,
        timeUnit: page.timeUnitDescription.value,
    });
    table.addRecord({
        trackedTime: remainingOperationTime,
        timeUnit: page.timeUnitDescription.value,
    });
    table.isDirty = false;
}
