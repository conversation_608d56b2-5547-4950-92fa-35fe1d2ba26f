/**
 * The function `secondsToHHMMSS` converts a given number of seconds into a string representation of hours, minutes, and
 * seconds in the format "HH:MM:SS".
 * @param {number} seconds -
 * @returns A string in the format "HH:MM:SS" where HH represents hours, MM represents minutes, and SS represents seconds.
 * The values are padded with leading zeros if needed to ensure each part is two digits long.
 */
export function secondsToHHMMSS(seconds: number): string {
    if (seconds < 0) throw new Error('Seconds cannot be negative');
    const hh = Math.floor(seconds / 3600);
    const mm = Math.floor((seconds % 3600) / 60);
    const ss = seconds % 60;
    return `${hh.toString().padStart(2, '0')}:${mm.toString().padStart(2, '0')}:${ss.toString().padStart(2, '0')}`;
}

/**
 * The function `minutesToHHMMSS` converts a given number of minutes into a string representation of hours, minutes, and
 * seconds in the format "HH:MM:SS".
 * @param {number} minutes
 * @returns The function `minutesToHHMMSS` takes a number of minutes as input and converts it to a string in the format
 * "HH:MM:SS" representing hours, minutes, and seconds. The function returns this formatted string.
 */
export function minutesToHHMMSS(minutes: number): string {
    if (minutes < 0) throw new Error('Minutes cannot be negative');
    const totalSeconds = Math.floor(minutes * 60);
    const hours = Math.floor(totalSeconds / 3600);
    const remainingSeconds = totalSeconds % 3600;
    const mins = Math.floor(remainingSeconds / 60);
    const secs = remainingSeconds % 60;
    const hh = hours.toString().padStart(2, '0');
    const mm = mins.toString().padStart(2, '0');
    const ss = secs.toString().padStart(2, '0');
    return `${hh}:${mm}:${ss}`;
}

/**
 * The function `hoursToHHMMSS` converts a given number of hours into a string representation of hours, minutes, and
 * seconds in the format "HH:MM:SS".
 * @param {number} hours
 * @returns a string in the format "HH:MM:SS" where HH represents hours, MM represents minutes, and SS represents seconds.
 * The values are padded with leading zeros if needed to ensure each part is two digits long.
 */
export function hoursToHHMMSS(hours: number): string {
    if (hours < 0) throw new Error('Hours cannot be negative');
    const totalSeconds = Math.floor(hours * 3600);
    const hh = Math.floor(totalSeconds / 3600);
    const mm = Math.floor((totalSeconds % 3600) / 60);
    const ss = totalSeconds % 60;
    return `${hh.toString().padStart(2, '0')}:${mm.toString().padStart(2, '0')}:${ss.toString().padStart(2, '0')}`;
}

/**
 * The function `daysToHHMMSS` converts a number of days into hours, minutes, and seconds in the format HH:MM:SS.
 * @param {number} days
 * @returns The function `daysToHHMMSS` takes a number of days as input and calculates the equivalent time in hours,
 * minutes, and seconds. It then returns a string in the format "HH:MM:SS" representing the calculated time.
 */
export function daysToHHMMSS(days: number): string {
    if (days < 0) throw new Error('Days cannot be negative');
    const totalSeconds = Math.floor(days * 86400);
    const hh = Math.floor(totalSeconds / 3600);
    const mm = Math.floor((totalSeconds % 3600) / 60);
    const ss = totalSeconds % 60;
    return `${hh.toString().padStart(2, '0')}:${mm.toString().padStart(2, '0')}:${ss.toString().padStart(2, '0')}`;
}

/**
 * The function `convertToSeconds` converts a given value in a specified unit of time to seconds.
 * @param {number} value - The value to be converted to seconds.
 * @param {string} unitOfTime - The unit of time for the value (e.g., 'seconds', 'minutes', 'hours', 'days').
 * @returns The function `convertToSeconds` returns the equivalent value in seconds based on the provided unit of time.
 */
export function convertToSeconds(value: number, unitOfTime: string): number {
    if (value < 0) throw new Error('Seconds cannot be negative');
    const unitInitial = unitOfTime.charAt(0).toLowerCase();
    switch (unitInitial) {
        case 's':
            return value;
        case 'm':
            return value * 60;
        case 'h':
            return value * 3600;
        case 'd':
            return value * 86400;
        default:
            throw new Error(`Unsupported unit of time: ${unitOfTime}`);
    }
}

/**
 * The function `convertTimeToHHMMSS` converts a numerical value with a specified unit of time into a formatted time string.
 * @param {number} value
 * @param {string} unitOfTime
 * @returns The `convertTimeToHHMMSS` function returns a string representing the time value formatted as HH:MM:SS based on the unit
 * of time provided. The specific formatting functions `minutesToHHMMSS`, `hoursToHHMMSS`, and `daysToHHMMSS` are called
 * based on the unit of time provided ('m' for minutes, 'h' for hours, 'd' for days
 */
export function convertTimeToHHMMSS(value: number, unitOfTime: string): string {
    const unitInitial = unitOfTime.charAt(0).toLowerCase();
    switch (unitInitial) {
        case 's':
            return secondsToHHMMSS(value);
        case 'm':
            return minutesToHHMMSS(value);
        case 'h':
            return hoursToHHMMSS(value);
        case 'd':
            return daysToHHMMSS(value);
        default:
            throw new Error(`Unsupported unit of time: ${unitOfTime}`);
    }
}

/**
 * The function `calculateRemainingTimeAsSeconds` calculates the remaining time in seconds based on expected and used
 * values in a specified unit of time.
 * @param {number} expectedValue - The expected value in the specified unit of time.
 * @param {number} usedSeconds - The number of seconds already used.
 * @param {string} unit - The unit of time for the expected value (e.g., 'seconds', 'minutes', 'hours', 'days').
 * @returns The function `calculateRemainingTimeAsSeconds` returns the remaining time in seconds after subtracting the used
 * seconds from the total expected seconds.
 */
export function calculateRemainingTimeAsSeconds(expectedValue: number, usedSeconds: number, unit: string): number {
    const totalExpectedSeconds = Math.round(convertToSeconds(expectedValue, unit));
    const remainingSeconds = Math.max(0, totalExpectedSeconds - usedSeconds);
    return remainingSeconds;
}
