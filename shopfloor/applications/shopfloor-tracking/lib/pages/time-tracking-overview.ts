import type { Operator } from '@sage/shopfloor-master-data-api';
import type { SyncStatus } from '@sage/shopfloor-master-data-api-partial';
import { getSyncStatusPillDisplay } from '@sage/shopfloor-master-data/lib/client-functions/pill-color';
import type {
    GraphApi,
    TimeTrackingDetail as TimeTrackingDetailNode,
    TimeTrackingHeader as TimeTrackingHeaderNode,
} from '@sage/shopfloor-tracking-api';
import { extractEdges } from '@sage/xtrem-client';
import type { Site } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import { formatPostfixWithLeadingCapital } from '../client-functions/postfix-utils';
import type { GetWorkOrderAndOperationData } from '../interfaces/time-tracking-overview';

@ui.decorators.page<TimeTrackingOverview, TimeTrackingHeaderNode>({
    module: 'shopfloor-tracking',
    mode: 'tabs',
    node: '@sage/shopfloor-tracking/TimeTrackingHeader',
    title: 'Time tracking overview',
    objectTypeSingular: 'Time tracking overview',
    objectTypePlural: 'Time tracking overviews',

    async onLoad() {
        await this.displayWorkOrderAndOperationData(
            this.workOrderId.value ?? '',
            this.operationId.value ?? '',
            this.workOrderDescription,
            this.operationDescription,
            this.expectedQuantity,
        );

        TimeTrackingOverview.buildQtyChart(
            this.qtyTrackedChart,
            this.completedQuantityChart.value ?? '',
            this.rejectedQuantityChart.value ?? '',
        );
        TimeTrackingOverview.buildTimeChart(
            this.timeTrackedChart,
            this.completedRunTimeChart.value ?? '',
            this.completedSetupTimeChart.value ?? '',
        );
    },

    headerSection() {
        return this.headerSection;
    },

    idField() {
        return ` - ${this.workOrderId.value} - ${this.workOrderDescription.value} ${this.operationDescription.value}`;
    },

    navigationPanel: {
        optionsMenu: [
            { title: 'All', graphQLFilter: {} },
            { title: 'Is synced', graphQLFilter: { isSynced: true } },
            { title: 'Is not synced', graphQLFilter: { isSynced: false } },
            { title: 'Sync completed', graphQLFilter: { syncHeader: { syncStatus: { _eq: 'completed' } } } },
            { title: 'Sync error', graphQLFilter: { syncHeader: { syncStatus: { _eq: 'error' } } } },
        ],
        listItem: {
            line2Right: ui.nestedFields.text({ bind: { operator: { id: true } }, title: 'Operator' }),
            operatorName: ui.nestedFields.text({ bind: { operator: { name: true } }, title: 'Operator name' }),
            line2: ui.nestedFields.text({ bind: { site: { id: true } }, title: 'Site' }),
            title: ui.nestedFields.text({ bind: { workOrderId: true }, title: 'Work order' }),
            titleRight: ui.nestedFields.text({ bind: { operationId: true }, title: 'Operation' }),
            resourceId: ui.nestedFields.text({ bind: 'resourceId', title: 'Resource' }),
            operatorResourceId: ui.nestedFields.text({ bind: 'operatorResourceId', title: 'Operator resource' }),
            isSync: ui.nestedFields.checkbox({ bind: { isSynced: true }, title: 'Synced' }),
            syncDate: ui.nestedFields.date({ bind: { syncHeader: { trackingDate: true } }, title: 'Synced date' }),
            line3: ui.nestedFields.label({
                bind: { syncHeader: { syncStatus: true } },
                title: 'Synced status',
                style: (_value, rowData) => getSyncStatusPillDisplay(rowData?.syncHeader?.syncStatus),
            }),
            unitOfMeasure: ui.nestedFields.label({ bind: { unitOfMeasure: { id: true } }, isHidden: true }),
            completedQuantity: ui.nestedFields.text({
                bind: 'completedQuantity',
                title: 'Actual qty',
                postfix(_value, rowData) {
                    return formatPostfixWithLeadingCapital(rowData?.unitOfMeasure?.id ?? '');
                },
            }),
            rejectedQuantity: ui.nestedFields.text({
                bind: 'rejectedQuantity',
                title: 'Rejected qty',
                postfix(_value, rowData) {
                    return formatPostfixWithLeadingCapital(rowData?.unitOfMeasure?.id ?? '');
                },
            }),
            completedSetupTimeFormatted: ui.nestedFields.label({
                bind: { completedSetupTimeFormatted: true },
                title: 'Setup time',
            }),
            completedRunTimeFormatted: ui.nestedFields.label({
                bind: { completedRunTimeFormatted: true },
                title: 'Run time',
            }),
            totalCompletedTimeFormatted: ui.nestedFields.label({
                bind: { totalCompletedTimeFormatted: true },
                title: 'Total time',
            }),
            externalReference: ui.nestedFields.text({
                bind: { syncHeader: { externalReference: true } },
                title: 'External reference',
            }),
        },
    },
})
export class TimeTrackingOverview extends ui.Page<GraphApi, TimeTrackingHeaderNode> {
    @ui.decorators.section<TimeTrackingOverview>({})
    headerSection: ui.containers.Section;

    @ui.decorators.block<TimeTrackingOverview>({
        parent() {
            return this.headerSection;
        },
    })
    headerBlock: ui.containers.Block;

    @ui.decorators.tile<TimeTrackingOverview>({
        parent() {
            return this.headerSection;
        },
    })
    headerTileContainer: ui.containers.Tile;

    @ui.decorators.section<TimeTrackingOverview>({
        title: 'Events',
    })
    trackingEventSection: ui.containers.Section;

    @ui.decorators.block<TimeTrackingOverview>({
        parent() {
            return this.trackingEventSection;
        },
    })
    trackingEventBlock: ui.containers.Block;

    @ui.decorators.section<TimeTrackingOverview>({
        title: 'Resources',
    })
    resourceSection: ui.containers.Section;

    @ui.decorators.block<TimeTrackingOverview>({
        parent() {
            return this.resourceSection;
        },
    })
    resourceBlock: ui.containers.Block;

    @ui.decorators.section<TimeTrackingOverview>({
        title: 'Sync',
    })
    syncSection: ui.containers.Section;

    @ui.decorators.section<TimeTrackingOverview>({
        title: 'Chart',
    })
    chartSection: ui.containers.Section;

    @ui.decorators.block<TimeTrackingOverview>({
        parent() {
            return this.syncSection;
        },
    })
    syncBlock: ui.containers.Block;

    @ui.decorators.block<TimeTrackingOverview>({
        parent() {
            return this.syncSection;
        },
    })
    syncBlockDetail: ui.containers.Block;

    @ui.decorators.block<TimeTrackingOverview>({
        parent() {
            return this.chartSection;
        },
    })
    chartBlock: ui.containers.Block;

    @ui.decorators.referenceField<TimeTrackingOverview, Site>({
        parent() {
            return this.headerBlock;
        },
        title: 'Site',
        node: '@sage/xtrem-system/Site',
        isReadOnly: true,
    })
    site: ui.fields.Reference<Site>;

    @ui.decorators.textField<TimeTrackingOverview>({
        bind: { unitOfMeasure: { id: true } },
    })
    unitOfMeasure: ui.fields.Text;

    @ui.decorators.referenceField<TimeTrackingOverview, Operator>({
        parent() {
            return this.headerBlock;
        },
        title: 'Operator',
        node: '@sage/shopfloor-master-data/Operator',
        isReadOnly: true,
    })
    operator: ui.fields.Reference;

    @ui.decorators.textField<TimeTrackingOverview>({
        parent() {
            return this.headerBlock;
        },
        title: 'Work order',
        isReadOnly: true,
    })
    workOrderId: ui.fields.Text;

    @ui.decorators.labelField<TimeTrackingOverview>({
        parent() {
            return this.headerBlock;
        },
        title: 'Description',
        isTransient: true,
    })
    workOrderDescription: ui.fields.Label;

    @ui.decorators.textField<TimeTrackingOverview>({
        parent() {
            return this.headerBlock;
        },
        title: 'Operation',
        isReadOnly: true,
    })
    operationId: ui.fields.Text;

    @ui.decorators.labelField<TimeTrackingOverview>({
        parent() {
            return this.headerBlock;
        },
        title: 'Description',
        isTransient: true,
    })
    operationDescription: ui.fields.Label;

    @ui.decorators.checkboxField<TimeTrackingOverview>({
        parent() {
            return this.headerBlock;
        },
        title: 'Completed',
        isReadOnly: true,
    })
    isCompleted: ui.fields.Checkbox;

    @ui.decorators.labelField<TimeTrackingOverview>({
        parent() {
            return this.headerTileContainer;
        },
        title: 'Actual setup time',
    })
    completedSetupTimeFormatted: ui.fields.Label;

    @ui.decorators.labelField<TimeTrackingOverview>({
        parent() {
            return this.headerTileContainer;
        },
        title: 'Actual run time',
    })
    completedRunTimeFormatted: ui.fields.Label;

    @ui.decorators.labelField<TimeTrackingOverview>({
        parent() {
            return this.headerTileContainer;
        },
        title: 'Total actual time',
    })
    totalCompletedTimeFormatted: ui.fields.Label;

    @ui.decorators.labelField<TimeTrackingOverview>({
        parent() {
            return this.headerTileContainer;
        },
        title: 'Actual qty',
        postfix() {
            if (this.completedQuantity.value) {
                return formatPostfixWithLeadingCapital(this.unitOfMeasure.value ?? '');
            }
            return '';
        },
    })
    completedQuantity: ui.fields.Label;

    @ui.decorators.labelField<TimeTrackingOverview>({
        parent() {
            return this.headerTileContainer;
        },
        title: 'Rejected qty',
        postfix() {
            if (this.rejectedQuantity.value) {
                return formatPostfixWithLeadingCapital(this.unitOfMeasure.value ?? '');
            }
            return '';
        },
    })
    rejectedQuantity: ui.fields.Label;

    @ui.decorators.labelField<TimeTrackingOverview>({
        parent() {
            return this.headerTileContainer;
        },
        isTransient: true,
        title: 'Expected qty',
        postfix() {
            if (this.expectedQuantity.value) {
                return formatPostfixWithLeadingCapital(this.unitOfMeasure.value ?? '');
            }
            return '';
        },
    })
    expectedQuantity: ui.fields.Label;

    @ui.decorators.tableField<TimeTrackingOverview, TimeTrackingDetailNode>({
        parent() {
            return this.trackingEventBlock;
        },
        canResizeColumns: true,
        isTitleHidden: true,
        canSelect: false,
        canExport: false,
        canFilter: true,
        canAddNewLine: false,
        isChangeIndicatorDisabled: true,
        isReadOnly: true,
        bind: 'trackingDetail',
        node: '@sage/shopfloor-tracking/TimeTrackingDetail',

        columns: [
            ui.nestedFields.label({
                bind: 'eventType',
                title: 'Event',
                map: (_value, rowData) => `${rowData.eventType?.toUpperCase?.() ?? ''}`,
            }),
            ui.nestedFields.datetime({ bind: 'eventStart', title: 'Event start', isTimeZoneHidden: true }),
            ui.nestedFields.datetime({ bind: 'eventStop', title: 'Event stop', isTimeZoneHidden: true }),
            ui.nestedFields.label({ bind: 'eventElapsedTimeFormatted', title: 'Elapsed' }),
            ui.nestedFields.text({
                bind: { trackingHeader: { unitOfMeasure: { id: true } } },
                isHidden: true,
            }),
            ui.nestedFields.text({
                bind: 'completedQuantity',
                title: 'Actual qty',
                postfix(_id, rowData) {
                    return formatPostfixWithLeadingCapital(rowData?.trackingHeader.unitOfMeasure.id ?? '');
                },
            }),
            ui.nestedFields.text({
                bind: 'rejectedQuantity',
                title: 'Rejected qty',
                postfix(_id, rowData) {
                    return formatPostfixWithLeadingCapital(rowData?.trackingHeader.unitOfMeasure.id ?? '');
                },
            }),
            ui.nestedFields.checkbox({ bind: 'eventPaused', title: 'Paused' }),
            ui.nestedFields.checkbox({ bind: 'isCompleted', title: 'Complete' }),
        ],
    })
    trackingDetail: ui.fields.Table<TimeTrackingDetailNode>;

    @ui.decorators.textField<TimeTrackingOverview>({
        parent() {
            return this.resourceBlock;
        },
        title: 'Resource type',
        isReadOnly: true,
    })
    resourceType: ui.fields.Text;

    @ui.decorators.textField<TimeTrackingOverview>({
        parent() {
            return this.resourceBlock;
        },
        title: 'Operation Resource',
        isReadOnly: true,
    })
    resourceId: ui.fields.Text;

    @ui.decorators.textField<TimeTrackingOverview>({
        parent() {
            return this.resourceBlock;
        },
        title: 'Operator Resource',
        isReadOnly: true,
    })
    operatorResourceId: ui.fields.Text;

    @ui.decorators.checkboxField<TimeTrackingOverview>({
        parent() {
            return this.syncBlock;
        },
        title: 'Synced',
        isReadOnly: true,
    })
    isSynced: ui.fields.Checkbox;

    @ui.decorators.labelField<TimeTrackingOverview>({
        parent() {
            return this.syncBlock;
        },
        bind: { syncHeader: { syncStatus: true } },
        title: 'Sync status',
        style() {
            return getSyncStatusPillDisplay(this.syncStatus.value as SyncStatus);
        },
    })
    syncStatus: ui.fields.Label;

    @ui.decorators.textField<TimeTrackingOverview>({
        parent() {
            return this.syncBlockDetail;
        },
        bind: { syncHeader: { trackingDate: true } },
        title: 'Sync date',
        isReadOnly: true,
    })
    trackingDate: ui.fields.Text;

    @ui.decorators.textField<TimeTrackingOverview>({
        parent() {
            return this.syncBlockDetail;
        },
        bind: { syncHeader: { externalReference: true } },
        title: 'External reference',
        isReadOnly: true,
    })
    externalReference: ui.fields.Text;

    @ui.decorators.textAreaField<TimeTrackingOverview>({
        parent() {
            return this.syncBlockDetail;
        },
        bind: { syncHeader: { synchronizationErrorMessage: true } },
        title: 'Sync error message',
        isReadOnly: true,
        rows: 4,
        width: 'large',
        placeholder: 'No error message',
    })
    externsynchronizationErrorMessage: ui.fields.TextArea;

    @ui.decorators.labelField<TimeTrackingOverview>({
        bind: { completedQuantity: true },
    })
    completedQuantityChart: ui.fields.Label;

    @ui.decorators.labelField<TimeTrackingOverview>({
        bind: { rejectedQuantity: true },
    })
    rejectedQuantityChart: ui.fields.Label;

    // @ui.decorators.numericField<TimeTrackingOverview>({
    //     bind: { rejectedQuantity: true },
    // })
    // rejectedQuantityChart: ui.fields.Numeric;

    @ui.decorators.chartField<TimeTrackingOverview>({
        title: 'Quantity tracked',
        isLegendHidden: true,
        isTransient: true,
        isHelperTextHidden: false,
        chart: ui.charts.pie<TimeTrackingOverview>({
            series: [
                ui.nestedFields.numeric({
                    bind: 'qty',
                    title: 'Quantity',
                }),
            ],
            xAxis: ui.nestedFields.text({ bind: 'name', title: 'Name' }),
        }),
        parent() {
            return this.chartBlock;
        },
    })
    qtyTrackedChart: ui.fields.Chart;

    @ui.decorators.labelField<TimeTrackingOverview>({
        bind: { completedSetupTime: true },
    })
    completedSetupTimeChart: ui.fields.Label;

    @ui.decorators.labelField<TimeTrackingOverview>({
        bind: { completedRunTime: true },
    })
    completedRunTimeChart: ui.fields.Label;

    @ui.decorators.chartField<TimeTrackingOverview>({
        title: 'Time tracked (Sec)',
        isLegendHidden: true,
        isTransient: true,
        isHelperTextHidden: false,
        chart: ui.charts.pie<TimeTrackingOverview>({
            series: [
                ui.nestedFields.numeric({
                    bind: 'time',
                    title: 'Time',
                }),
            ],
            xAxis: ui.nestedFields.text({ bind: 'name', title: 'Name' }),
        }),
        parent() {
            return this.chartBlock;
        },
    })
    timeTrackedChart: ui.fields.Chart;

    /**
     * The function `buildQtyChart` sets the unit of measure and creates a quantity chart with actual and rejected
     * quantities.
     */
    static buildQtyChart(chart: ui.fields.Chart, completedQuantity: string, rejectedQuantity: string): void {
        const qtyChartData = [
            {
                _id: '1',
                name: ui.localize(
                    '@sage/@sage/shopfloor-tracking/tracking_overview_qty_chart_actual_qty',
                    'Actual quantity',
                ),
                qty: Number(completedQuantity) || 0,
            },
            {
                _id: '2',
                name: ui.localize(
                    '@sage/@sage/shopfloor-tracking/tracking_overview_qty_chart_rejected_qty',
                    'Rejected quantity',
                ),
                qty: Number(rejectedQuantity) || 0,
            },
        ];

        chart.value = qtyChartData;
    }

    /**
     * The function `buildTimeChart` creates a time chart with data for actual run time and actual setup time.
     */
    static buildTimeChart(chart: ui.fields.Chart, completedRunTime: string, completedSetupTime: string): void {
        const timeChartData = [
            {
                _id: '1',
                name: ui.localize(
                    '@sage/@sage/shopfloor-tracking/tracking_overview_time_chart_actual_run',
                    'Actual run time',
                ),
                time: Number(completedRunTime) || 0,
            },
            {
                _id: '2',
                name: ui.localize(
                    '@sage/@sage/shopfloor-tracking/tracking_overview_time_chart_actual_setup',
                    'Actual setup time',
                ),
                time: Number(completedSetupTime) || 0,
            },
        ];
        chart.value = timeChartData;
    }

    /**
     * The function `displayWorkOrderAndOperationData` asynchronously retrieves work order and operation data and updates
     * corresponding UI fields with the retrieved information.
     * @param {string} workOrderId -
     * @param {string} operationId -
     * @param workOrderDescription - UI field of type Label that is used to display work order description
     * @param operationDescription - UI field of type Label that is used to display operation description
     * @param expectedQuantity - UI .fields of type label that is used to the expected quantity
     */
    async displayWorkOrderAndOperationData(
        workOrderId: string,
        operationId: string,
        workOrderDescription: ui.fields.Label,
        operationDescription: ui.fields.Label,
        expectedQuantity: ui.fields.Label,
    ): Promise<void> {
        const data = await this.getWorkOrderAndOperationData(workOrderId, operationId);
        workOrderDescription.value = data.name ?? '';
        expectedQuantity.value = data.expectedQuantity ?? '';
        operationDescription.value = data.description ?? '';
    }

    /**
     * This function retrieves work order and operation data based on provided IDs and returns specific
     * information.
     * @param {string} workOrderId - The `workOrderId` parameter is a string that represents the unique identifier of a work
     * order.
     * @param {string} operationId - The `operationId` parameter is used to identify a specific operation within a work
     * order.
     * @returns The `getWorkOrderAndOperationData` function returns an object with the properties `name`, `expectedQuantity`,
     * and `description`. These properties are extracted from the `workOrderOperation` data based on the provided
     * `workOrderId` and `operationId`. If the data is found and matches the criteria, the function returns an object with
     * these properties populated with the corresponding values. If no matching data
     */
    async getWorkOrderAndOperationData(
        workOrderId: string,
        operationId: string,
    ): Promise<GetWorkOrderAndOperationData> {
        let workOrderOperation: any[] = [];
        try {
            workOrderOperation = extractEdges(
                await this.$.graph
                    .node('@sage/shopfloor-master-data/WorkOrder')
                    .query(
                        ui.queryUtils.edgesSelector(
                            {
                                id: true,
                                name: true,
                                item: { expectedQuantity: true, description: true },
                                operations: {
                                    query: {
                                        edges: {
                                            node: {
                                                id: true,
                                                description: true,
                                            },
                                        },
                                    },
                                },
                            },
                            {
                                filter: {
                                    id: workOrderId,
                                },
                            },
                        ),
                    )
                    .execute(),
            );
        } catch {
            return { name: '', expectedQuantity: '', description: '' };
        }

        if (workOrderOperation.length === 0) {
            return { name: '', expectedQuantity: '', description: '' };
        }

        const wo = workOrderOperation[0];
        const operations = Array.isArray(wo.operations) ? wo.operations.filter((op: any) => op.id === operationId) : [];
        const name = wo.item?.description ?? '';
        const expectedQuantity = wo.item?.expectedQuantity != null ? String(wo.item.expectedQuantity) : '';
        const description = operations.length > 0 ? (operations[0].description ?? '') : '';

        return { name, expectedQuantity, description };
    }
}
