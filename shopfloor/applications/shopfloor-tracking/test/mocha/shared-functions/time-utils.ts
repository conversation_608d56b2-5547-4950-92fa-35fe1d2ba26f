import { expect } from 'chai';
import { convertSecondsToUnitOfTime, formatSecondsAsTime } from '../../../lib/shared-functions/time-utils';

describe('convertSecondsToUnitOfTime', () => {
    it('should return seconds when convertTo is not "MIN" or "HR"', () => {
        expect(convertSecondsToUnitOfTime(45, 'SEC')).to.equal(45);
        expect(convertSecondsToUnitOfTime(100, '')).to.equal(100);
        expect(convertSecondsToUnitOfTime(200, 'XYZ')).to.equal(200);
    });
});

describe('formatSecondsAsTime', () => {
    it('should format 0 seconds as "00:00:00"', () => {
        expect(formatSecondsAsTime(0)).to.equal('00:00:00');
    });

    it('should format seconds less than a minute correctly', () => {
        expect(formatSecondsAsTime(5)).to.equal('00:00:05');
        expect(formatSecondsAsTime(59)).to.equal('00:00:59');
    });

    it('should format seconds less than an hour correctly', () => {
        expect(formatSecondsAsTime(60)).to.equal('00:01:00');
        expect(formatSecondsAsTime(3599)).to.equal('00:59:59');
    });

    it('should format seconds equal to or more than an hour correctly', () => {
        expect(formatSecondsAsTime(3600)).to.equal('01:00:00');
        expect(formatSecondsAsTime(3661)).to.equal('01:01:01');
        expect(formatSecondsAsTime(86399)).to.equal('23:59:59');
    });

    it('should pad single digit hours, minutes, and seconds with zeros', () => {
        expect(formatSecondsAsTime(1)).to.equal('00:00:01');
        expect(formatSecondsAsTime(61)).to.equal('00:01:01');
        expect(formatSecondsAsTime(3661)).to.equal('01:01:01');
    });
});
