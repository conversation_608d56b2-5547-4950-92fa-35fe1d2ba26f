import { expect } from 'chai';
import {
    calculateRemainingTimeAsSeconds,
    convertTimeToHHMMSS,
    convertToSeconds,
    daysToHHMMSS,
    hoursToHHMMSS,
    minutesToHHMMSS,
    secondsToHHMMSS,
} from '../../../lib/shared-functions/time-conversion-utils';

describe('time-conversion-utils', () => {
    describe('secondsToHHMMSS', () => {
        it('should convert seconds to HH:MM:SS', () => {
            expect(secondsToHHMMSS(0)).to.equal('00:00:00');
            expect(secondsToHHMMSS(59)).to.equal('00:00:59');
            expect(secondsToHHMMSS(60)).to.equal('00:01:00');
            expect(secondsToHHMMSS(3599)).to.equal('00:59:59');
            expect(secondsToHHMMSS(3600)).to.equal('01:00:00');
            expect(secondsToHHMMSS(3661)).to.equal('01:01:01');
        });

        it('should throw error for negative seconds', () => {
            expect(() => secondsToHHMMSS(-1)).to.throw('Seconds cannot be negative');
        });
    });

    describe('minutesToHHMMSS', () => {
        it('should convert minutes to HH:MM:SS', () => {
            expect(minutesToHHMMSS(0)).to.equal('00:00:00');
            expect(minutesToHHMMSS(1)).to.equal('00:01:00');
            expect(minutesToHHMMSS(59)).to.equal('00:59:00');
            expect(minutesToHHMMSS(60)).to.equal('01:00:00');
            expect(minutesToHHMMSS(61)).to.equal('01:01:00');
            expect(minutesToHHMMSS(1.5)).to.equal('00:01:30');
        });

        it('should throw error for negative minutes', () => {
            expect(() => minutesToHHMMSS(-1)).to.throw('Minutes cannot be negative');
        });
    });

    describe('hoursToHHMMSS', () => {
        it('should convert hours to HH:MM:SS', () => {
            expect(hoursToHHMMSS(0)).to.equal('00:00:00');
            expect(hoursToHHMMSS(1)).to.equal('01:00:00');
            expect(hoursToHHMMSS(1.5)).to.equal('01:30:00');
            expect(hoursToHHMMSS(2.25)).to.equal('02:15:00');
        });

        it('should throw error for negative hours', () => {
            expect(() => hoursToHHMMSS(-1)).to.throw('Hours cannot be negative');
        });
    });

    describe('daysToHHMMSS', () => {
        it('should convert days to HH:MM:SS', () => {
            expect(daysToHHMMSS(0)).to.equal('00:00:00');
            expect(daysToHHMMSS(1)).to.equal('24:00:00');
            expect(daysToHHMMSS(0.5)).to.equal('12:00:00');
            expect(daysToHHMMSS(1.25)).to.equal('30:00:00');
        });

        it('should throw error for negative days', () => {
            expect(() => daysToHHMMSS(-1)).to.throw('Days cannot be negative');
        });
    });

    describe('convertToSeconds', () => {
        it('should convert value to seconds for supported units', () => {
            expect(convertToSeconds(10, 'seconds')).to.equal(10);
            expect(convertToSeconds(2, 'minutes')).to.equal(120);
            expect(convertToSeconds(1, 'hours')).to.equal(3600);
            expect(convertToSeconds(1, 'days')).to.equal(86400);
            expect(convertToSeconds(3, 's')).to.equal(3);
            expect(convertToSeconds(4, 'm')).to.equal(240);
            expect(convertToSeconds(5, 'h')).to.equal(18000);
            expect(convertToSeconds(2, 'd')).to.equal(172800);
        });

        it('should throw error for negative value', () => {
            expect(() => convertToSeconds(-1, 'seconds')).to.throw('Seconds cannot be negative');
        });

        it('should throw error for unsupported unit', () => {
            expect(() => convertToSeconds(1, 'weeks')).to.throw('Unsupported unit of time: weeks');
            expect(() => convertToSeconds(1, '')).to.throw('Unsupported unit of time: ');
        });
    });

    describe('convertTimeToHHMMSS', () => {
        it('should convert value and unit to HH:MM:SS', () => {
            expect(convertTimeToHHMMSS(3661, 'seconds')).to.equal('01:01:01');
            expect(convertTimeToHHMMSS(61, 'minutes')).to.equal('01:01:00');
            expect(convertTimeToHHMMSS(1.5, 'hours')).to.equal('01:30:00');
            expect(convertTimeToHHMMSS(0.5, 'days')).to.equal('12:00:00');
        });

        it('should throw error for unsupported unit', () => {
            expect(() => convertTimeToHHMMSS(1, 'weeks')).to.throw('Unsupported unit of time: weeks');
        });
    });

    describe('calculateRemainingTimeAsSeconds', () => {
        it('should calculate remaining time in seconds', () => {
            expect(calculateRemainingTimeAsSeconds(2, 60, 'minutes')).to.equal(60);
            expect(calculateRemainingTimeAsSeconds(1, 3600, 'hours')).to.equal(0);
            expect(calculateRemainingTimeAsSeconds(0.5, 1000, 'days')).to.equal(42200);
        });

        it('should not return negative remaining seconds', () => {
            expect(calculateRemainingTimeAsSeconds(1, 4000, 'minutes')).to.equal(0);
        });
    });
});
