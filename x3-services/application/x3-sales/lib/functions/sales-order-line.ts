import * as sageX3MasterData from '@sage/x3-master-data';
import { decimal, toNumber } from '@sage/xtrem-core';
import * as sageX3Sales from '..';

export async function getAmountInCompanyCurrency(_this: sageX3Sales.nodes.SalesOrderLine): Promise<decimal> {
    const salesOrder = await _this.salesOrder;
    const company = await salesOrder?.company;
    const fromCurrency = await salesOrder.currency;
    const fromCurrencyCode = await fromCurrency.code;
    const orderDate = await salesOrder.orderDate;
    const currencyRateType = (await salesOrder.currencyRateType) ?? 'dailyRate';
    const toCurrency = await company.currency;
    let toCurrencyCode = fromCurrencyCode;
    if (toCurrency) {
        toCurrencyCode = await toCurrency.code;
    }
    const grossPrice = (await _this.grossPrice) ?? 0;

    const grossPriceConverted = await sageX3MasterData.functions.currencyConversion(
        _this.$.context,
        fromCurrencyCode,
        toCurrencyCode,
        [grossPrice],
        {
            rounding: 0,
            companyCurrency: toCurrencyCode,
            rateType: currencyRateType,
            rateDate: orderDate,
            fallbackToEuro: false,
        },
    );
    const quantityConverted = await _this.quantityInSalesUnitOrdered;
    const amountInCompanyCurrency = toNumber(grossPriceConverted[0]) * quantityConverted;
    return amountInCompanyCurrency;
}
