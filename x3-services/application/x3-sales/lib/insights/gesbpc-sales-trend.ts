import { InsightQuery } from '@sage/xtrem-x3-copilot';

export const gesbpcSalesTrend: InsightQuery = {
    screenId: 'GESBPC',
    type: 'data',
    key: 'salesTrend',
    query: `{
                    x3Sales {
                        salesOrder {
                        queryAggregate(
                            filter: "{soldToCustomer:{_id:{_eq:'{{data.x3MasterData.customer.query.edges.0.node._id}}'}},_fn: 'this.orderDate.isBetween(date.parse(\\"{{chatContext.userCurrentDate}}\\").begOfYear(), date.parse(\\"{{chatContext.userCurrentDate}}\\").endOfYear())'}"
                        ) {
                            edges {
                                node {
                                    group {
                                        orderDate(by: month)
                                        currency {
                                            code
                                            symbol
                                            isoCode
                                            localizedDescription
                                        }
                                    }
                                    values {
                                    totalAmountExcludingTax {
                                        sum
                                    }
                                    }
                                }
                            }
                        }
                        }
                    }
               }`,
};
