import { InsightQuery } from '@sage/xtrem-x3-copilot';

export const gesbpcShipmentNotValidated: InsightQuery = {
    screenId: 'GESBPC',
    type: 'data',
    key: 'shipmentNotValidated',
    query: `{
                    x3Sales {
                        salesDelivery {
                        query(filter: "{isValidated:'false',soldToCustomer: {_id: {_eq: '{{data.x3MasterData.customer.query.edges.0.node._id}}'}}}") {
                            edges {
                                node {
                                    id
                                    shipmentDate
                                    totalAmountExcludingTax
                                    currency {
                                        code
                                        symbol
                                        isoCode
                                        localizedDescription
                                    }
                                    recordUrl
                                }
                            }
                        }
                        }
                    }
                }`,
};
