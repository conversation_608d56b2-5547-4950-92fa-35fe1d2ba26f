import { InsightQuery } from '@sage/xtrem-x3-copilot';

export const gessohShipmentDeliveryDelayed: InsightQuery = {
    screenId: 'GESSOH',
    type: 'data',
    key: 'shipmentDeliveryDelayed',
    query: `{
                    x3Sales {
                        salesDelivery {
                        query(
                            filter: "{soldToCustomer: {_id: {_eq: '{{data.x3Sales.salesOrder.query.edges.0.node.soldToCustomer._id}}'}},isValidated:'true'}"
                            orderBy: "{ shipmentDate: -1  }"
                        ) {
                            edges {
                                node {
                                    id
                                    shipmentDate
                                    isInvoiced
                                    totalAmountExcludingTax
                                    currency {
                                        code
                                        symbol
                                        isoCode
                                        localizedDescription
                                    }
                                    recordUrl
                                }
                            }
                        }
                        }
                    }
                }`,
};
