import { InsightQuery } from '@sage/xtrem-x3-copilot';

export const gesbpcSalesOrdersNotShipped: InsightQuery = {
    screenId: 'GESBPC',
    type: 'data',
    key: 'salesOrdersNotShipped',
    query: `{
                x3Sales {
                    salesOrder {
                    query(
                        filter: "{shipmentDate: {_lt: '{{chatContext.userCurrentDate}}'},deliveryStatus:{_eq:'notDelivered'} ,orderStatus: {_eq: 'open'},soldToCustomer: {_id: {_eq: '{{data.x3MasterData.customer.query.edges.0.node._id}}'}}}"
                        orderBy: "{totalAmountExcludingTax:-1}"
                    ) {
                        totalCount
                        edges {
                            node {
                                id
                                shipmentDate
                                totalAmountExcludingTax
                                currency {
                                    code
                                    symbol
                                    isoCode
                                    localizedDescription
                                }
                                recordUrl
                            }
                        }
                    }
                    }
                }
                }`,
};
