import { InsightQuery } from '@sage/xtrem-x3-copilot';

export const gessohSalesOrdersPartiallyShipped: InsightQuery = {
    screenId: 'GESSOH',
    type: 'data',
    key: 'salesOrdersPartiallyShipped',
    query: `{
                    x3Sales {
                        salesOrder {
                        query(
                            filter: "{orderStatus: {_eq: 'open'},deliveryStatus: {_eq:'partlyDelivered'},soldToCustomer: {_id: {_eq: '{{data.x3Sales.salesOrder.query.edges.0.node.soldToCustomer._id}}'}}}"
                            orderBy: "{totalAmountExcludingTax:-1}"
                        ) {
                            totalCount
                            edges {
                                node {
                                    id
                                    shipmentDate
                                    deliveryStatus
                                    linesAmountRemainingToDeliverExcludingTax
                                    currency {
                                        code
                                        symbol
                                        isoCode
                                        localizedDescription
                                    }
                                    recordUrl
                                }
                            }
                        }
                        }
                    }
                    }`,
};
