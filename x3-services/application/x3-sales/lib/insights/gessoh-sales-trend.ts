import { InsightQuery } from '@sage/xtrem-x3-copilot';

export const gessohSalesTrend: InsightQuery = {
    screenId: 'GESSOH',
    type: 'data',
    key: 'salesTrend',
    query: `{
                    x3Sales {
                        salesOrder {
                        queryAggregate(
                            filter: "{soldToCustomer:{_id:{_eq:'{{data.x3Sales.salesOrder.query.edges.0.node.soldToCustomer._id}}'}},_fn: 'this.orderDate.isBetween(date.parse(\\"{{chatContext.userCurrentDate}}\\").begOfYear(), date.parse(\\"{{chatContext.userCurrentDate}}\\").endOfYear())'}"
                        ) {
                            edges {
                                node {
                                    group {
                                    orderDate(by: month)
                                    currency {
                                        code
                                        symbol
                                        isoCode
                                        localizedDescription
                                    }
                                    }
                                    values {
                                    totalAmountExcludingTax {
                                        sum
                                    }
                                    }
                                }
                            }
                        }
                        }
                    }
               }`,
};
