import { InsightQuery } from '@sage/xtrem-x3-copilot';

export const gessohShipmentNotValidated: InsightQuery = {
    screenId: 'GESSOH',
    type: 'data',
    key: 'shipmentNotValidated',
    query: `{
                    x3Sales {
                        salesDelivery {
                        query(filter: "{isValidated:'false',soldToCustomer: {_id: {_eq: '{{data.x3Sales.salesOrder.query.edges.0.node.soldToCustomer._id}}'}}}") {
                            edges {
                                node {
                                    id
                                    shipmentDate
                                    totalAmountExcludingTax
                                    currency {
                                        code
                                        symbol
                                        isoCode
                                        localizedDescription
                                    }
                                    recordUrl
                                }
                            }
                        }
                        }
                    }
                }`,
};
