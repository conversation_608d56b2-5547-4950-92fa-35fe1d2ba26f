import { InsightQuery } from '@sage/xtrem-x3-copilot';

export const gesbpcData: InsightQuery = {
    screenId: 'GESBPC',
    type: 'main',
    key: 'data',
    query: `{
                    x3MasterData{
                        customer{
                        query(filter:"{{chatContext.recordFilter}}"){
                            edges{
                                node{
                                    _id
                                    isActive
                                    companyName1
                                    companyName2
                                    shortCompanyName
                                }
                            }
                        }
                        }
                    }
                }`,
};
