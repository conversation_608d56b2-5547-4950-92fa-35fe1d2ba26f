import { InsightQuery } from '@sage/xtrem-x3-copilot';

export const gesbpcSalesOrdersPartiallyShipped: InsightQuery = {
    screenId: 'GESBPC',
    type: 'data',
    key: 'salesOrdersPartiallyShipped',
    query: `{
                    x3Sales {
                        salesOrder {
                        query(
                            filter: "{orderStatus: {_eq: 'open'},deliveryStatus: {_eq:'partlyDelivered'},soldToCustomer: {_id: {_eq: '{{data.x3MasterData.customer.query.edges.0.node._id}}'}}}"
                            orderBy: "{totalAmountExcludingTax:-1}"
                        ) {
                            totalCount
                            edges {
                                node {
                                    id
                                    shipmentDate
                                    deliveryStatus
                                    linesAmountRemainingToDeliverExcludingTax
                                    currency {
                                        code
                                        symbol
                                        isoCode
                                        localizedDescription
                                    }
                                    recordUrl
                                }
                            }
                        }
                        }
                    }
                    }`,
};
