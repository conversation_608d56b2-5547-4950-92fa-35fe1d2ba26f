import { InsightQuery } from '@sage/xtrem-x3-copilot';

export const gessohData: InsightQuery = {
    screenId: 'GESSOH',
    type: 'main',
    key: 'data',
    query: `{
                    x3Sales {
                        salesOrder {
                        query(filter: "{{chatContext.recordFilter}}") {
                            edges {
                                node {
                                    id
                                    orderDate
                                    shipmentDate
                                    salesSite{
                                        _id
                                        code
                                        name
                                        shortName
                                    }
                                    currency {
                                        code
                                        symbol
                                        isoCode
                                        localizedDescription
                                    }
                                    soldToCustomer {
                                        _id
                                        isActive
                                        companyName1
                                        companyName2
                                        shortCompanyName
                                    }
                                    recordUrl
                                }
                            }
                        }
                        }
                    }
                }`,
};
