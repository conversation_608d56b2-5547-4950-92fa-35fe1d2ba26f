import * as sageX3MasterData from '@sage/x3-master-data';
import * as sageX3System from '@sage/x3-system';
import { decorators, Node, Reference } from '@sage/xtrem-core';
import { X3StorageManager, Joins } from '@sage/xtrem-x3-gateway';
import * as sageX3Manufacturing from '..';

const joins: Joins<ProductAttribute> = {
    referenceJoins: {
        product: {
            code: 'product',
        },
        attributeCode: {
            attributeCode: 'attributeCode',
        },
        attributeValue: {
            attributeCode: 'attributeCode',
            attributeValue: 'attributeValue',
        },
    },
};

@decorators.node<ProductAttribute>({
    storage: 'external',
    tableName: 'GFSITMATT',
    keyPropertyNames: ['product', 'attributeCode'],
    indexes: [
        {
            orderBy: {
                product: 1,
                attributeCode: 1,
            },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    externalStorageManager: new X3StorageManager({
        joins,
    }),
    serviceOptions: () => [sageX3System.serviceOptions.GfsaActivityCode],
    isPublished: true,
    canRead: true,
    canSearch: true,
})
export class ProductAttribute extends Node {
    @decorators.referenceProperty<ProductAttribute, 'product'>({
        isPublished: true,
        isStored: true,
        columnName: 'ITMREF',
        columnType: 'string',
        node: () => sageX3MasterData.nodes.Product,
    })
    readonly product: Reference<sageX3MasterData.nodes.Product>;

    @decorators.referenceProperty<ProductAttribute, 'attributeCode'>({
        isPublished: true,
        isStored: true,
        columnName: 'ATTCOD',
        columnType: 'string',
        node: () => sageX3Manufacturing.nodes.Attribute,
    })
    readonly attributeCode: Reference<sageX3Manufacturing.nodes.Attribute>;

    @decorators.referenceProperty<ProductAttribute, 'attributeValue'>({
        isPublished: true,
        isStored: true,
        columnName: 'ATTVALCOD',
        columnType: 'string',
        node: () => sageX3Manufacturing.nodes.AttributeLine,
    })
    readonly attributeValue: Reference<sageX3Manufacturing.nodes.AttributeLine>;
}
