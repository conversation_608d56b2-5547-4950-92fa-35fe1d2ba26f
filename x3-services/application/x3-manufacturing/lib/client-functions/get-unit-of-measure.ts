import { Graph<PERSON><PERSON> } from '@sage/x3-manufacturing-api';
import { UnitOfMeasure } from '@sage/x3-master-data-api';
import * as ui from '@sage/xtrem-ui';

/**
 * This function retrieves the unit of measure details for a given unit of measure code.
 * @param pageInstance The current page instance where the function is called.
 * @param uom The unit of measure code to retrieve details for.
 * @returns An object containing the unit of measure details, or an empty object if an error occurs.
 */

export async function getUOM(pageInstance: ui.Page<GraphApi>, uom: string): Promise<Partial<UnitOfMeasure>> {
    try {
        const UnitOfMeasure: Partial<UnitOfMeasure> = await pageInstance.$.graph
            .node('@sage/x3-master-data/UnitOfMeasure')
            .read(
                {
                    _id: true,
                    code: true,
                    numberOfDecimals: true,
                },
                `${uom}`,
            )
            .execute();

        return UnitOfMeasure;
    } catch (error) {
        pageInstance.$.dialog.message(
            'error',
            ui.localize(
                '@sage/x3-manufacturing/pages__utils__get_unit_of_measure_error',
                'An error occurred when loading unit of measure',
            ),
            String(error),
        );
        return {};
    }
}
