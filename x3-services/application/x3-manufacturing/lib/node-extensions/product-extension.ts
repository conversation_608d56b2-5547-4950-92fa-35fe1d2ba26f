import * as sageX3MasterData from '@sage/x3-master-data';
import { decorators, NodeExtension, Collection } from '@sage/xtrem-core';
import { X3StorageManagerExtension, Joins } from '@sage/xtrem-x3-gateway';
import * as sageX3Manufacturing from '..';

const joins: Joins<ProductExtension> = {
    collectionJoins: {
        productAttributes: {
            product: 'code',
        },
    },
};

@decorators.nodeExtension<ProductExtension>({
    extends: () => sageX3MasterData.nodes.Product,
    externalStorageManagerExtension: new X3StorageManagerExtension({
        joins,
    }),
})
export class ProductExtension extends NodeExtension<sageX3MasterData.nodes.Product> {
    @decorators.collectionProperty<ProductExtension, 'productAttributes'>({
        isPublished: true,
        node: () => sageX3Manufacturing.nodes.ProductAttribute,
        isMutable: true,
        dependsOn: ['code'],
    })
    readonly productAttributes: Collection<sageX3Manufacturing.nodes.ProductAttribute>;
}

declare module '@sage/x3-master-data/lib/nodes/product' {
    export interface Product extends ProductExtension {}
}
