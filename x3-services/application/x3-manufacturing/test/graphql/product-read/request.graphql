{
    x3MasterData {
        product {
            query(filter: "{code:'I-JBADCITEM001'}") {
                edges {
                    node {
                        code
                        description1
                        description2
                        description3
                        localizedDescription1
                        localizedDescription2
                        localizedDescription3
                        upc
                        globalTradeItemNumber
                        productStatus
                        stockUnit {
                            code
                        }
                        purchaseUnit {
                            code
                        }
                        purchaseUnitToStockUnitConversionFactor
                        lotSequenceNumber
                        defaultPotencyInPercentage
                        defaultPotencyInInternationalUnit
                        serialNumberManagementMode
                        serialSequenceNumber
                        productSites {
                            query {
                                edges {
                                    node {
                                        product {
                                            code
                                        }
                                    }
                                }
                            }
                        }
                        packingUnits {
                            query {
                                edges {
                                    node {
                                        packingUnit {
                                            code
                                        }
                                    }
                                }
                            }
                        }
                        expirationManagementMode
                        expirationStockStatus
                        expirationLeadTime
                        checkLeadTime
                        useByDateCoefficient
                        isVersionManaged
                        minorVersionSequence
                        majorVersionSequence
                        isVersionPreloaded
                        productCategory {
                            code
                        }
                        weightUnit {
                            code
                        }
                        productWeight
                        volumeUnit {
                            code
                        }
                        productVolume
                        isReceived
                        isPurchased
                        isManufactured
                        isPhantom
                        isDeliverable
                        isSold
                        isSubcontracted
                        isService
                        searchKey
                        managementMode
                        standard
                        serviceLifeStartDate
                        serviceLifeEndDate
                        storageSheet
                        buyer {
                            code
                        }
                        planner {
                            code
                        }
                        salesUnit {
                            code
                        }
                        statisticalGroups {
                            query {
                                edges {
                                    node {
                                        denormalizedIndex
                                        code
                                    }
                                }
                            }
                        }
                        statisticalUnit {
                            code
                        }
                        eeUnit {
                            code
                        }
                        isPurchaseFactorEntryAllowed
                        salesUnitToStockUnitConversionFactor
                        isSalesFactorEntryAllowed
                        statisticalUnitToStockUnitConversionFactor
                        eeUnitToStockUnitConversionFactor
                        isNegativeStockAuthorized
                        costGroup
                        alternateProduct {
                            code
                        }
                        isIntrastatDeclarationSubmitted
                        intrastatCommodity
                        purchasingPlanningHorizonTimeUnit
                        purchasingPlanningHorizon
                        purchasingFirmHorizon
                        purchasingFirmHorizonTimeUnit
                        reorderLeadTime
                        purchaseDeliveryTolerance
                        isMandatoryPurchaseOrderRequest
                        exemptionFlag
                        taxFlagStatusLocal
                        configuratorProductLine
                        configuratorNumericField1
                        configuratorNumericField2
                        configuratorNumericField3
                        configuratorNumericField4
                        configuratorNumericField5
                        configuratorNumericField6
                        configuratorAlphaField1
                        configuratorAlphaField2
                        configuratorAlphaField3
                        configuratorAlphaField4
                        configuratorAlphaField5
                        configuratorAlphaField6
                        configurationJournal
                        configuratorBusinessPartner {
                            code
                        }
                        configuratorBusinessPartnerReference
                        configuratorReferenceProduct {
                            code
                        }
                        configuratorPurgeDate
                        isInstalledBaseCreated
                        tokensToBeCredited
                        creditUnit {
                            code
                        }
                        labelFormatForStockUnit {
                            reportName
                        }
                        productTypeForAfterSales
                        isAfterSalesProductIssuedByDefault
                        daysUnit {
                            code
                        }
                        hourUnit {
                            code
                        }
                        minuteUnit {
                            code
                        }
                        productDensity
                        isGeneric
                        isTools
                        isCapitalized
                        newStockStatusAfterRecontrol
                        plmProductReference
                        plmLinkedDocuments
                        plmProductHistory
                        supplierProducts {
                            query {
                                edges {
                                    node {
                                        product {
                                            code
                                        }
                                        supplier {
                                            code {
                                                code
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        customerProducts {
                            query {
                                edges {
                                    node {
                                        product {
                                            code
                                        }
                                        customer {
                                            code {
                                                code
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        company {
                            code
                        }
                        purchaseBasePrice
                        allergenBomCode
                        productType
                        landedCostCoefficient
                        fixedLandedCostPerUnit
                        saftProductType

                        buyer {
                            code
                        }
                        planner {
                            code
                        }
                        statisticalGroups {
                            query {
                                edges {
                                    node {
                                        denormalizedIndex
                                        code
                                    }
                                }
                            }
                        }
                        productAttributes {
                            query {
                                edges {
                                    node {
                                        product {
                                            code
                                        }
                                        attributeCode {
                                            attributeCode
                                        }
                                        attributeValue {
                                            attributeValue
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
