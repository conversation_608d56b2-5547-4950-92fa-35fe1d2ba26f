# This GraphQL query retrieves detailed information about work order operation lines for a specific work order.
# The filter is set to retrieve data for the work order with code '227261_WO1'.
{
    x3Manufacturing {
        workOrderProductLine {
            query(filter: "{workOrder: 'WO000010'}") {
                edges {
                    node {
                        workOrder {
                            number
                        }
                        lineNumber
                        product {
                            code
                        }
                        majorProductVersion {
                            code
                        }
                        minorProductVersion
                        lineStatus
                        releaseUnit {
                            code
                        }
                        releaseQuantity
                        releaseStkConversion
                        stockUnit {
                            code
                        }
                        expectedQuantity
                        bomCode {
                            code
                        }
                        lot
                        productType
                        remainingQuantity
                        productCategory {
                            code
                        }
                        projectTaskNumber {
                            id
                        }
                        customer {
                            code
                            shortCompanyName
                        }
                        salesOrder
                        productSiteAttribute {
                            query {
                                edges {
                                    node {
                                        attributeCode {
                                            attributeCode
                                        }
                                        attributeValue {
                                            attributeValue
                                        }
                                    }
                                }
                            }
                        }
                        productAnalysis {
                            query {
                                edges {
                                    node {
                                        denormalizedIndex
                                        analysis {
                                            code
                                        }
                                    }
                                }
                            }
                        }
                        productAbcClass
                        baseQuantity
                        operationLeadTime
                        operationNumber
                        shipToType
                        closingDate
                        totalCompletedQuantity
                        valuation
                        endDate
                        productSource
                        workOrderProductLine
                        linkQuantity
                        linkQuantityCode
                        workOrderDescription
                        priority
                        workOrderStatus
                        trackingFlag
                        planner {
                            code
                        }
                        planningSite {
                            code
                        }
                        quantityRounding
                        actualQuantityUnderControl
                        actualRejectedQuantity
                        startDate
                        firstTrackingDate
                        firstTrackingCreateDate
                        lastTrackingDate
                        lastTrackingCreateDate
                        sourceDocumentLineNumber
                        sourceDocumentSequenceNumber
                        workInProgressOrderNumber
                    }
                }
            }
        }
    }
}
