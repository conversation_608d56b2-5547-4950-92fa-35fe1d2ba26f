import * as sageX3ManufacturingData from '@sage/x3-manufacturing-data';
import * as sageX3SalesData from '@sage/x3-sales-data';
import { X3StorageManagerExtension, Joins } from '@sage/xtrem-x3-gateway';
import { decorators, NodeExtension } from '@sage/xtrem-core';

const joins: Joins<MadeToOrderExtension> = {};

@decorators.nodeExtension<MadeToOrderExtension>({
    extends: () => sageX3ManufacturingData.nodes.MadeToOrder,
    externalStorageManagerExtension: new X3StorageManagerExtension({
        joins,
    }),
})
export class MadeToOrderExtension extends NodeExtension<sageX3ManufacturingData.nodes.MadeToOrder> {
    @decorators.enumProperty<MadeToOrderExtension, 'productSource'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'FMI',
        dataType: () => sageX3SalesData.enums.sourceOfShipmentDatatype,
    })
    readonly productSource: Promise<sageX3SalesData.enums.SourceOfShipment | null>;
}

declare module '@sage/x3-manufacturing-data/lib/nodes/made-to-order' {
    export interface MadeToOrder extends MadeToOrderExtension {}
}
