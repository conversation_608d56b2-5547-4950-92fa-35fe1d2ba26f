import * as sageX3MasterData from '@sage/x3-master-data';
import { decorators, Node, integer, Reference, decimal, Context } from '@sage/xtrem-core';
import { X3StorageManager, Joins, Denormalized } from '@sage/xtrem-x3-gateway';
import * as sageXtremX3SystemUtils from '@sage/xtrem-x3-system-utils';
import * as sageX3Finance from '..';

const denormalized: Denormalized = {
    maxRepeat: (context: Context) => sageXtremX3SystemUtils.getSizingFromActivityCode(context, 'PER'),
};

const joins: Joins<BalanceAmounts> = {
    referenceJoins: {
        _denormalizedParent: {
            ledgerTypeNumber: 'ledgerTypeNumber',
            company: 'company',
            site: 'site',
            fiscalYear: 'fiscalYear',
            account: 'account',
            businessPartner: 'businessPartner',
            currency: 'currency',
        },
    },
};

@decorators.node<BalanceAmounts>({
    storage: 'external',
    tableName: 'BALANCE',
    keyPropertyNames: [
        'denormalizedIndex',
        'ledgerTypeNumber',
        'company',
        'site',
        'fiscalYear',
        'account',
        'businessPartner',
        'currency',
    ],
    indexes: [],
    externalStorageManager: new X3StorageManager({
        joins,
        isDenormalized: true,
        denormalized,
    }),
    isPublished: true,
    canRead: true,
    canSearch: true,
    isVitalCollectionChild: true,
})
export class BalanceAmounts extends Node {
    @decorators.integerProperty<BalanceAmounts, 'denormalizedIndex'>({
        isPublished: true,
    })
    readonly denormalizedIndex: Promise<integer>;

    @decorators.enumProperty<BalanceAmounts, 'ledgerTypeNumber'>({
        isPublished: true,
        isStored: true,
        columnName: 'LEDTYP',
        dataType: () => sageX3MasterData.enums.generalLedgerTypesDatatype,
    })
    readonly ledgerTypeNumber: Promise<sageX3MasterData.enums.GeneralLedgerTypes>;

    @decorators.stringProperty<BalanceAmounts, 'company'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'CPY',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly company: Promise<string>;

    @decorators.stringProperty<BalanceAmounts, 'site'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'FCY',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly site: Promise<string>;

    @decorators.integerProperty<BalanceAmounts, 'fiscalYear'>({
        isPublished: true,
        isStored: true,
        columnName: 'FIY',
    })
    readonly fiscalYear: Promise<integer>;

    @decorators.stringProperty<BalanceAmounts, 'account'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'ACC',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly account: Promise<string>;

    @decorators.stringProperty<BalanceAmounts, 'businessPartner'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'BPR',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly businessPartner: Promise<string>;

    @decorators.stringProperty<BalanceAmounts, 'currency'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'CUR',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly currency: Promise<string>;

    @decorators.referenceProperty<BalanceAmounts, '_denormalizedParent'>({
        isStored: true,
        isVitalParent: true,
        columnType: 'string',
        node: () => sageX3Finance.nodes.Balance,
    })
    readonly _denormalizedParent: Reference<sageX3Finance.nodes.Balance>;

    @decorators.decimalProperty<BalanceAmounts, 'debitAmountInLedgerCurrency'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'DEBLED',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.decimalDatatype,
    })
    readonly debitAmountInLedgerCurrency: Promise<decimal | null>;

    @decorators.decimalProperty<BalanceAmounts, 'creditAmountInLedgerCurrency'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'CDTLED',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.decimalDatatype,
    })
    readonly creditAmountInLedgerCurrency: Promise<decimal | null>;

    @decorators.decimalProperty<BalanceAmounts, 'debitAmountInCurrency'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'DEB',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.decimalDatatype,
    })
    readonly debitAmountInCurrency: Promise<decimal | null>;

    @decorators.decimalProperty<BalanceAmounts, 'creditAmountInCurrency'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'CDT',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.decimalDatatype,
    })
    readonly creditAmountInCurrency: Promise<decimal | null>;
}
