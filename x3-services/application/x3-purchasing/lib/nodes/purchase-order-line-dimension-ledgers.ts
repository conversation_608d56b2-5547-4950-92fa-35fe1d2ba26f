import * as sageX3FinanceData from '@sage/x3-finance-data';
import * as sageX3StockData from '@sage/x3-stock-data';
import { decorators, Node, integer, Reference } from '@sage/xtrem-core';
import { X3StorageManager, Joins, Denormalized } from '@sage/xtrem-x3-gateway';
import * as sageXtremX3SystemUtils from '@sage/xtrem-x3-system-utils';
import * as sageX3Purchasing from '..';

const denormalized: Denormalized = { maxRepeat: 10 };

const joins: Joins<PurchaseOrderLineDimensionLedgers> = {
    referenceJoins: {
        _denormalizedParent: {
            abbreviation: 'abbreviation',
            type: 'type',
            purchaseOrder: 'purchaseOrder',
            purchaseOrderLine: 'purchaseOrderLine',
            sequenceNumber: 'sequenceNumber',
            keyComplement: 'keyComplement',
            sortValue: 'sortValue',
        },
        ledger: {
            code: 'ledger',
        },
        chartOfAccounts: {
            code: 'chartOfAccounts',
        },
        account: {
            chartOfAccounts: 'chartOfAccounts',
            code: 'account',
        },
    },
};

@decorators.node<PurchaseOrderLineDimensionLedgers>({
    storage: 'external',
    tableName: 'CPTANALIN',
    keyPropertyNames: [
        'denormalizedIndex',
        'abbreviation',
        'type',
        'purchaseOrder',
        'purchaseOrderLine',
        'sequenceNumber',
        'keyComplement',
        'sortValue',
    ],
    indexes: [],
    externalStorageManager: new X3StorageManager({
        joins,
        isDenormalized: true,
        denormalized,
    }),
    isPublished: true,
    canRead: true,
    canSearch: true,
    isVitalCollectionChild: true,
})
export class PurchaseOrderLineDimensionLedgers extends Node {
    @decorators.integerProperty<PurchaseOrderLineDimensionLedgers, 'denormalizedIndex'>({
        isPublished: true,
    })
    readonly denormalizedIndex: Promise<integer>;

    @decorators.stringProperty<PurchaseOrderLineDimensionLedgers, 'abbreviation'>({
        isStored: true,
        isNotEmpty: true,
        columnName: 'ABRFIC',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly abbreviation: Promise<string>;

    @decorators.enumProperty<PurchaseOrderLineDimensionLedgers, 'type'>({
        isStored: true,
        columnName: 'VCRTYP',
        dataType: () => sageX3StockData.enums.entryTypeEnumDatatype,
    })
    readonly type: Promise<sageX3StockData.enums.EntryTypeEnum>;

    @decorators.stringProperty<PurchaseOrderLineDimensionLedgers, 'purchaseOrder'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'VCRNUM',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly purchaseOrder: Promise<string>;

    @decorators.integerProperty<PurchaseOrderLineDimensionLedgers, 'purchaseOrderLine'>({
        isPublished: true,
        isStored: true,
        columnName: 'VCRLIN',
    })
    readonly purchaseOrderLine: Promise<integer>;

    @decorators.integerProperty<PurchaseOrderLineDimensionLedgers, 'sequenceNumber'>({
        isStored: true,
        columnName: 'VCRSEQ',
    })
    readonly sequenceNumber: Promise<integer>;

    @decorators.stringProperty<PurchaseOrderLineDimensionLedgers, 'keyComplement'>({
        isStored: true,
        isNotEmpty: true,
        columnName: 'CPLCLE',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly keyComplement: Promise<string>;

    @decorators.integerProperty<PurchaseOrderLineDimensionLedgers, 'sortValue'>({
        isStored: true,
        columnName: 'ANALIG',
    })
    readonly sortValue: Promise<integer>;

    @decorators.referenceProperty<PurchaseOrderLineDimensionLedgers, '_denormalizedParent'>({
        isStored: true,
        isVitalParent: true,
        columnType: 'string',
        node: () => sageX3Purchasing.nodes.PurchaseOrderLineDimension,
    })
    readonly _denormalizedParent: Reference<sageX3Purchasing.nodes.PurchaseOrderLineDimension>;

    @decorators.referenceProperty<PurchaseOrderLineDimensionLedgers, 'ledger'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'LED',
        columnType: 'string',
        node: () => sageX3FinanceData.nodes.Ledger,
    })
    readonly ledger: Reference<sageX3FinanceData.nodes.Ledger | null>;

    @decorators.referenceProperty<PurchaseOrderLineDimensionLedgers, 'chartOfAccounts'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'COA',
        columnType: 'string',
        node: () => sageX3FinanceData.nodes.ChartOfAccounts,
    })
    readonly chartOfAccounts: Reference<sageX3FinanceData.nodes.ChartOfAccounts | null>;

    @decorators.referenceProperty<PurchaseOrderLineDimensionLedgers, 'account'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'ACC',
        columnType: 'string',
        node: () => sageX3FinanceData.nodes.Account,
    })
    readonly account: Reference<sageX3FinanceData.nodes.Account | null>;
}
