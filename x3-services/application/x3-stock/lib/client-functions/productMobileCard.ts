import { ProductBinding, ProductSiteBinding } from "@sage/x3-master-data-api-partial";
import * as ui from '@sage/xtrem-ui';
import { CardDefinition } from "@sage/xtrem-ui/build/lib/component/ui/card/card-component";


export function productMobileCard(): CardDefinition<ui.Page, ProductBinding> {
    return {
        title: ui.nestedFields.text({ bind: 'code' }),
        line2: ui.nestedFields.text({ bind: 'localizedDescription1' }),
        line3: ui.nestedFields.text({
            bind: 'upc',
            isHidden(value) {
                return !value
            },
        }),
    };
}

export function productSiteMobileCard(): CardDefinition<ui.Page, ProductSiteBinding> {
    return {
        title: ui.nestedFields.text({
            bind: { product: { code: true } },
        }),
        line2: ui.nestedFields.text({
            bind: { product: { localizedDescription1: true } },
        }),
        line3: ui.nestedFields.text({
            bind: { product: { upc: true } },
            isHidden(value) {
                return !value
            },
        }),
    };
}
