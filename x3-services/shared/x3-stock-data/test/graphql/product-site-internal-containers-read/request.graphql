{
    x3StockData {
        productSiteInternalContainers {
            query(filter: "{stockSite: 'IAG10', product: 'I-PROD004'}") {
                edges {
                    node {
                        denormalizedIndex
                        product
                        stockSite
                        internalContainer {
                            code
                        }
                        packingUnitOnInternalContainer {
                            code
                        }
                        numberOfUnitsPerLicensePlateNumberCreatedOnTheFly
                    }
                }
            }
        }
    }
}
