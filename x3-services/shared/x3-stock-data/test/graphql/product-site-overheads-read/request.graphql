{
    x3StockData {
        productSiteOverheads {
            query(filter: "{stockSite: 'IAG10', product: 'I-PROD004'}") {
                edges {
                    node {
                        denormalizedIndex
                        product
                        stockSite
                        transactionType
                        overhead {
                            overhead
                        }
                        isLowerLevelOverheadIncluded
                    }
                }
            }
        }
    }
}
