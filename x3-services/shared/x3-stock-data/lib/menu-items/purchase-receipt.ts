import { SubMenuItem } from '@sage/xtrem-shared';
import { receipt } from './receipt';

/**
 * Menu item: "Purchase Receipt", located under the main "Receipt" menu.
 * Acts as a parent to the following sub-menu items:
 * - By Delivery
 * - By Order
 * - By Order pre-Receipt
 * - By Shipment and Container
 * - By Shipment
 *
 * Menu hierarchy:
 * Receipt
 * └── purchaseReceipt
 *     ├── byDelivery
 *     ├── byOrder
 *     ├── byOrderPreReceipt
 *     ├── byShipmentAndContainer
 *     └── byShipment
 */

export const purchaseReceipt: SubMenuItem = {
    id: '@sage/x3-stock-data/purchase-receipt',
    priority: 200,
    title: 'Purchase receipt',
    parentMenuItem: receipt,
};
