import * as sageX3MasterData from '@sage/x3-master-data';
import { decorators, Node, integer, Reference } from '@sage/xtrem-core';
import { X3StorageManager, Joins, Denormalized } from '@sage/xtrem-x3-gateway';
import * as sageXtremX3SystemUtils from '@sage/xtrem-x3-system-utils';
import * as sageX3StockData from '..';

const denormalized: Denormalized = { maxRepeat: 5 };

const joins: Joins<ProductSiteOverheads> = {
    referenceJoins: {
        _denormalizedParent: {
            product: 'product',
            stockSite: 'stockSite',
        },
        overhead: {
            overhead: 'overhead',
        },
    },
};

@decorators.node<ProductSiteOverheads>({
    storage: 'external',
    tableName: 'ITMFACILIT',
    keyPropertyNames: ['denormalizedIndex', 'product', 'stockSite'],
    indexes: [],
    externalStorageManager: new X3StorageManager({
        joins,
        isDenormalized: true,
        denormalized,
    }),
    isPublished: true,
    canRead: true,
    canSearch: true,
    isVitalCollectionChild: true,
})
export class ProductSiteOverheads extends Node {
    @decorators.integerProperty<ProductSiteOverheads, 'denormalizedIndex'>({
        isPublished: true,
    })
    readonly denormalizedIndex: Promise<integer>;

    @decorators.stringProperty<ProductSiteOverheads, 'product'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'ITMREF',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly product: Promise<string>;

    @decorators.stringProperty<ProductSiteOverheads, 'stockSite'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'STOFCY',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly stockSite: Promise<string>;

    @decorators.referenceProperty<ProductSiteOverheads, '_denormalizedParent'>({
        isStored: true,
        isVitalParent: true,
        columnType: 'string',
        node: () => sageX3MasterData.nodes.ProductSite,
    })
    readonly _denormalizedParent: Reference<sageX3MasterData.nodes.ProductSite>;

    @decorators.enumProperty<ProductSiteOverheads, 'transactionType'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'OTRSTYP',
        dataType: () => sageX3StockData.enums.stockTransactionTypeDatatype,
    })
    readonly transactionType: Promise<sageX3StockData.enums.StockTransactionType | null>;

    @decorators.referenceProperty<ProductSiteOverheads, 'overhead'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'OVECOD',
        columnType: 'string',
        node: () => sageX3StockData.nodes.Overhead,
    })
    readonly overhead: Reference<sageX3StockData.nodes.Overhead | null>;

    @decorators.booleanProperty<ProductSiteOverheads, 'isLowerLevelOverheadIncluded'>({
        isPublished: true,
        isStored: true,
        columnName: 'OVECPNFLG',
    })
    readonly isLowerLevelOverheadIncluded: Promise<boolean>;
}
