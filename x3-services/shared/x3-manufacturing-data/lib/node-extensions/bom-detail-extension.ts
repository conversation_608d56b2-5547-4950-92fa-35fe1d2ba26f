import * as sageX3MasterData from '@sage/x3-master-data';
import * as sageX3System from '@sage/x3-system';
import { decorators, NodeExtension, Reference } from '@sage/xtrem-core';
import { Joins, X3StorageManagerExtension } from '@sage/xtrem-x3-gateway';
import * as sageX3ManufacturingData from '..';

const joins: Joins<BomDetailExtension> = {
    referenceJoins: {
        masterContainer: {
            code: 'masterContainer',
        },
    },
};

@decorators.nodeExtension<BomDetailExtension>({
    extends: () => sageX3MasterData.nodes.BomDetail,
    externalStorageManagerExtension: new X3StorageManagerExtension({
        joins,
    }),
})
export class BomDetailExtension extends NodeExtension<sageX3MasterData.nodes.BomDetail> {
    @decorators.referenceProperty<BomDetailExtension, 'masterContainer'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'CTN',
        columnType: 'string',
        node: () => sageX3ManufacturingData.nodes.WeighingStationContainer,
        serviceOptions: () => [sageX3System.serviceOptions.MwcActivityCode],
    })
    readonly masterContainer: Reference<sageX3ManufacturingData.nodes.WeighingStationContainer | null>;
}

declare module '@sage/x3-master-data/lib/nodes/bom-detail' {
    export interface BomDetail extends BomDetailExtension {}
}
