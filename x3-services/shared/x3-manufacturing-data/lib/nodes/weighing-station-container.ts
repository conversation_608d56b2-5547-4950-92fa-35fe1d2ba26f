import * as sageX3MasterData from '@sage/x3-master-data';
import * as sageX3System from '@sage/x3-system';
import { decorators, Node, Reference, decimal } from '@sage/xtrem-core';
import { X3StorageManager, Joins } from '@sage/xtrem-x3-gateway';
import * as sageXtremX3SystemUtils from '@sage/xtrem-x3-system-utils';
import * as sageX3ManufacturingData from '..';

const joins: Joins<WeighingStationContainer> = {
    referenceJoins: {
        weightUnit: {
            code: 'weightUnit',
        },
        userGuide: {
            code: 'userGuide',
        },
    },
};

@decorators.node<WeighingStationContainer>({
    storage: 'external',
    tableName: 'CONTAINERS',
    keyPropertyNames: ['code'],
    indexes: [
        {
            orderBy: {
                code: 1,
            },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    externalStorageManager: new X3StorageManager({
        joins,
    }),
    serviceOptions: () => [sageX3System.serviceOptions.MwmActivityCode],
    isPublished: true,
    canRead: true,
    canSearch: true,
})
export class WeighingStationContainer extends Node {
    @decorators.stringProperty<WeighingStationContainer, 'code'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'CTN',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly code: Promise<string>;

    @decorators.enumProperty<WeighingStationContainer, 'type'>({
        isPublished: true,
        isStored: true,
        columnName: 'CTNTYP',
        dataType: () => sageX3ManufacturingData.enums.typeOfContainerDatatype,
    })
    readonly type: Promise<sageX3ManufacturingData.enums.TypeOfContainer>;

    @decorators.booleanProperty<WeighingStationContainer, 'isModifiableTare'>({
        isPublished: true,
        isStored: true,
        columnName: 'CTNMODTAR',
    })
    readonly isModifiableTare: Promise<boolean>;

    @decorators.booleanProperty<WeighingStationContainer, 'isManualWeightEntry'>({
        isPublished: true,
        isStored: true,
        columnName: 'CTNSAIMAN',
    })
    readonly isManualWeightEntry: Promise<boolean>;

    @decorators.referenceProperty<WeighingStationContainer, 'weightUnit'>({
        isPublished: true,
        isStored: true,
        columnName: 'CTNWEU',
        columnType: 'string',
        node: () => sageX3MasterData.nodes.UnitOfMeasure,
    })
    readonly weightUnit: Reference<sageX3MasterData.nodes.UnitOfMeasure>;

    @decorators.decimalProperty<WeighingStationContainer, 'calculatedTare'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'CTNTHETAR',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.decimalDatatype,
    })
    readonly calculatedTare: Promise<decimal | null>;

    @decorators.decimalProperty<WeighingStationContainer, 'tareTolerancePercentage'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'CTNPRCTOL',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.decimalDatatype,
    })
    readonly tareTolerancePercentage: Promise<decimal | null>;

    @decorators.referenceProperty<WeighingStationContainer, 'userGuide'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'CTNUGD',
        columnType: 'string',
        node: () => sageX3ManufacturingData.nodes.UserGuide,
    })
    readonly userGuide: Reference<sageX3ManufacturingData.nodes.UserGuide | null>;

    @decorators.stringProperty<WeighingStationContainer, 'labelFormat'>({
        isPublished: true,
        isStored: true,
        columnName: 'CTNLBEFMT',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly labelFormat: Promise<string>;

    @decorators.stringProperty<WeighingStationContainer, 'localizedDescription'>({
        isPublished: true,
        isStored: true,
        columnName: 'CTNDESAXX',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.translatableTextDatatype,
    })
    readonly localizedDescription: Promise<string>;

    @decorators.stringProperty<WeighingStationContainer, 'localizedShortDescription'>({
        isPublished: true,
        isStored: true,
        columnName: 'CTNSHOAXX',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.translatableTextDatatype,
    })
    readonly localizedShortDescription: Promise<string>;

    @decorators.booleanProperty<WeighingStationContainer, 'isMasterContainer'>({
        isPublished: true,
        isStored: true,
        columnName: 'CTNMST',
        serviceOptions: () => [sageX3System.serviceOptions.MwcActivityCode],
    })
    readonly isMasterContainer: Promise<boolean>;

    @decorators.decimalProperty<WeighingStationContainer, 'capacity'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'CTNCAP',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.decimalDatatype,
        serviceOptions: () => [sageX3System.serviceOptions.MwcActivityCode],
    })
    readonly capacity: Promise<decimal | null>;

    @decorators.booleanProperty<WeighingStationContainer, 'skipScaleReset'>({
        isPublished: true,
        isStored: true,
        columnName: 'WGGDLTRST',
    })
    readonly skipScaleReset: Promise<boolean>;
}
