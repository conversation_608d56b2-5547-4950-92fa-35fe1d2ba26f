import * as sageX3System from '@sage/x3-system';
import { X3StorageManager, Joins } from '@sage/xtrem-x3-gateway';
import * as sageXtremX3SystemUtils from '@sage/xtrem-x3-system-utils';
import { decorators, Node } from '@sage/xtrem-core';

const joins: Joins<UserGuide> = {};

@decorators.node<UserGuide>({
    storage: 'external',
    tableName: 'USERGUIDES',
    keyPropertyNames: ['code'],
    indexes: [
        {
            orderBy: {
                code: 1,
            },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    externalStorageManager: new X3StorageManager({
        joins,
    }),
    serviceOptions: () => [sageX3System.serviceOptions.MwmActivityCode],
    isPublished: true,
    canRead: true,
    canSearch: true,
})
export class UserGuide extends Node {
    @decorators.stringProperty<UserGuide, 'code'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'UGD',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<UserGuide, 'text'>({
        isPublished: true,
        isStored: true,
        columnName: 'UGDTEX',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly text: Promise<string>;

    @decorators.stringProperty<UserGuide, 'localizedDescription'>({
        isPublished: true,
        isStored: true,
        columnName: 'UGDDESAXX',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.translatableTextDatatype,
    })
    readonly localizedDescription: Promise<string>;

    @decorators.stringProperty<UserGuide, 'localizedShortDescription'>({
        isPublished: true,
        isStored: true,
        columnName: 'UGDSHOAXX',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.translatableTextDatatype,
    })
    readonly localizedShortDescription: Promise<string>;
}
