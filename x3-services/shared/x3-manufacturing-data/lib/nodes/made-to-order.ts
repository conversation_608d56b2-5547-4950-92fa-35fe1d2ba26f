import * as sageX3ProjectManagementData from '@sage/x3-project-management-data';
import * as sageX3StockData from '@sage/x3-stock-data';
import { decorators, Node, date, Reference } from '@sage/xtrem-core';
import { X3StorageManager, Joins } from '@sage/xtrem-x3-gateway';
import * as sageXtremX3SystemUtils from '@sage/xtrem-x3-system-utils';

const joins: Joins<MadeToOrder> = {
    referenceJoins: {
        project: {
            id: 'project',
        },
    },
};

@decorators.node<MadeToOrder>({
    storage: 'external',
    tableName: 'MTOHEAD',
    keyPropertyNames: ['mtoNetwork'],
    indexes: [
        {
            orderBy: {
                mtoNetwork: 1,
            },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    externalStorageManager: new X3StorageManager({
        joins,
    }),
    isPublished: true,
    canRead: true,
    canSearch: true,
})
export class MadeToOrder extends Node {
    @decorators.stringProperty<MadeToOrder, 'mtoNetwork'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'MTOREF',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly mtoNetwork: Promise<string>;

    @decorators.stringProperty<MadeToOrder, 'description'>({
        isPublished: true,
        isStored: true,
        columnName: 'MTODES',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly description: Promise<string>;

    @decorators.stringProperty<MadeToOrder, 'shortDescription'>({
        isPublished: true,
        isStored: true,
        columnName: 'MTOSHO',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly shortDescription: Promise<string>;

    @decorators.enumProperty<MadeToOrder, 'entryType'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'VCRTYP',
        dataType: () => sageX3StockData.enums.entryTypeEnumDatatype,
    })
    readonly entryType: Promise<sageX3StockData.enums.EntryTypeEnum | null>;

    @decorators.dateProperty<MadeToOrder, 'startDate'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'STRDAT',
        defaultValue() {
            return X3StorageManager.getDateDefaultValue(this);
        },
    })
    readonly startDate: Promise<date | null>;

    @decorators.dateProperty<MadeToOrder, 'endDate'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'ENDDAT',
        defaultValue() {
            return X3StorageManager.getDateDefaultValue(this);
        },
    })
    readonly endDate: Promise<date | null>;

    @decorators.referenceProperty<MadeToOrder, 'project'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'PJT',
        columnType: 'string',
        node: () => sageX3ProjectManagementData.nodes.ProjectLink,
    })
    readonly project: Reference<sageX3ProjectManagementData.nodes.ProjectLink | null>;
}
