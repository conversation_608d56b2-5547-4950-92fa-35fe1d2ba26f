import * as sageX3MasterData from '@sage/x3-master-data';
import { decorators, NodeExtension, Reference } from '@sage/xtrem-core';
import { X3StorageManagerExtension, Joins } from '@sage/xtrem-x3-gateway';
import * as sageX3PurchasingData from '..';

const joins: Joins<SupplierProductSiteExtension> = {
    referenceJoins: {
        matchingTolerance: {
            code: 'matchingTolerance',
        },
    },
};

@decorators.nodeExtension<SupplierProductSiteExtension>({
    extends: () => sageX3MasterData.nodes.SupplierProductSite,
    externalStorageManagerExtension: new X3StorageManagerExtension({
        joins,
    }),
})
export class SupplierProductSiteExtension extends NodeExtension<sageX3MasterData.nodes.SupplierProductSite> {
    @decorators.enumProperty<SupplierProductSiteExtension, 'blockingManagementMode'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'DOUFLG',
        dataType: () => sageX3PurchasingData.enums.conflictDatatype,
    })
    readonly blockingManagementMode: Promise<sageX3PurchasingData.enums.Conflict | null>;

    @decorators.referenceProperty<SupplierProductSiteExtension, 'matchingTolerance'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'MATTOL',
        columnType: 'string',
        node: () => sageX3PurchasingData.nodes.MatchingTolerance,
    })
    readonly matchingTolerance: Reference<sageX3PurchasingData.nodes.MatchingTolerance | null>;
}

declare module '@sage/x3-master-data/lib/nodes/supplier-product-site' {
    export interface SupplierProductSite extends SupplierProductSiteExtension {}
}
