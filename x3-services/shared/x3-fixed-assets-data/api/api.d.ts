declare module '@sage/x3-fixed-assets-data-api-partial' {
    import type { Package as SageXtremAppMetadata$Package } from '@sage/xtrem-app-metadata-api';
    import type { Package as SageXtremX3SystemUtils$Package } from '@sage/xtrem-x3-system-utils-api';
    export interface Package {}
    export interface GraphApi extends Package, SageXtremAppMetadata$Package, SageXtremX3SystemUtils$Package {}
}
declare module '@sage/x3-fixed-assets-data-api' {
    export type * from '@sage/x3-fixed-assets-data-api-partial';
}
declare module '@sage/xtrem-app-metadata-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/x3-fixed-assets-data-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-x3-system-utils-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/x3-fixed-assets-data-api';
    export interface GraphApi extends GraphApiExtension {}
}
