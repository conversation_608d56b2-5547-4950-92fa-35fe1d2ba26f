module.exports = {
    plugins: ['mocha', 'unicorn', '@sage/xtrem', 'unused-imports'],
    extends: ['airbnb', 'airbnb-typescript', 'eslint-config-airbnb-typescript', 'prettier'],
    rules: {
        '@typescript-eslint/naming-convention': [
            'warn',
            {
                selector: 'typeLike',
                format: ['StrictPascalCase'],
            },
        ],
        '@typescript-eslint/space-before-function-paren': 'off',
        '@typescript-eslint/indent': 'off',
        'no-underscore-dangle': 'off',
        'import/prefer-default-export': 'off',
        'import/no-cycle': 'off',
        'react/destructuring-assignment': 'off',
        'no-param-reassign': [
            'error',
            {
                props: false,
            },
        ],
        'mocha/no-exclusive-tests': 'error',
        'unicorn/filename-case': [
            'error',
            {
                case: 'kebabCase',
                ignore: [{}],
            },
        ],
        'require-await': 'error',
        'import/order': 'off',
        'prefer-template': 'off',
        'prefer-arrow-callback': 'off',
    },
    overrides: [
        {
            files: ['lib/**/*.ts', 'test/**/*.ts'],
            extends: ['plugin:@sage/xtrem/recommended-ts'],
            rules: {
                '@sage/xtrem/sql-compatible': 'error',
                '@sage/xtrem/call-super-in-control': 'error',
                'unused-imports/no-unused-imports': 'error',
                'no-await-in-loop': 'off',
                'no-async-promise-executor': 'error',
                'no-promise-executor-return': 'error',
                'no-return-await': 'error',
                'require-await': 'error',
                'prefer-promise-reject-errors': 'error',
                '@typescript-eslint/no-floating-promises': 'error',
                '@typescript-eslint/await-thenable': 'error',
                'react/forbid-prop-types': 'off',
            },
        },
        {
            files: ['test/**/*.ts'],
            rules: {
                'import/no-extraneous-dependencies': [
                    'error',
                    {
                        devDependencies: true,
                    },
                ],
            },
        },
        {
            files: ['lib/{pages,page-extensions,stickers}/**/*.ts'],
            rules: {
                '@typescript-eslint/no-floating-promises': 'off',
                '@typescript-eslint/no-misused-promises': 'off',
                'require-await': 'off',
                '@typescript-eslint/await-thenable': 'off',
                'no-promise-executor-return': 'off',
                'no-return-await': 'off',
            },
        },
        {
            files: ['test/graphql/**/*.json'],
            extends: ['plugin:@sage/xtrem/recommended-json'],
        },
    ],
    parserOptions: {
        tsconfigRootDir: '.',
        project: 'tsconfig.json',
        extraFileExtensions: ['.json'],
    },
};
