{"compilerOptions": {"module": "commonjs", "moduleResolution": "node", "target": "es2022", "lib": ["es2022", "dom"], "composite": true, "sourceMap": true, "declaration": true, "declarationMap": true, "noUnusedLocals": false, "noImplicitAny": true, "noImplicitThis": true, "strictNullChecks": true, "experimentalDecorators": true, "stripInternal": true, "watch": false, "skipLibCheck": true, "useDefineForClassFields": false, "disableReferencedProjectLoad": true, "outDir": "build", "rootDir": ".", "baseUrl": "."}, "compileOnSave": true, "include": ["lib/pages", "lib/widgets", "lib/page-extensions", "lib/page-fragments", "lib/stickers", "api/api.d.ts", "lib/menu-items", "lib/client-functions", "lib/shared-functions"]}