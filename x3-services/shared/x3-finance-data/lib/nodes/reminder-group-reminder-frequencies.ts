import * as sageX3System from '@sage/x3-system';
import { decorators, Node, integer, Reference, decimal } from '@sage/xtrem-core';
import { X3StorageManager, Joins, Denormalized } from '@sage/xtrem-x3-gateway';
import * as sageXtremX3SystemUtils from '@sage/xtrem-x3-system-utils';
import * as sageX3FinanceData from '..';

const denormalized: Denormalized = { maxRepeat: 5 };

const joins: Joins<ReminderGroupReminderFrequencies> = {
    referenceJoins: {
        _denormalizedParent: {
            code: 'code',
        },
    },
};

@decorators.node<ReminderGroupReminderFrequencies>({
    storage: 'external',
    tableName: 'FUPGRP',
    keyPropertyNames: ['denormalizedIndex', 'code'],
    indexes: [],
    externalStorageManager: new X3StorageManager({
        joins,
        isDenormalized: true,
        denormalized,
    }),
    serviceOptions: () => [sageX3System.serviceOptions.FupActivityCode],
    isPublished: true,
    canRead: true,
    canSearch: true,
    isVitalCollectionChild: true,
})
export class ReminderGroupReminderFrequencies extends Node {
    @decorators.integerProperty<ReminderGroupReminderFrequencies, 'denormalizedIndex'>({
        isPublished: true,
    })
    readonly denormalizedIndex: Promise<integer>;

    @decorators.stringProperty<ReminderGroupReminderFrequencies, 'code'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'GRP',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<ReminderGroupReminderFrequencies, '_denormalizedParent'>({
        isStored: true,
        isVitalParent: true,
        columnType: 'string',
        node: () => sageX3FinanceData.nodes.ReminderGroup,
    })
    readonly _denormalizedParent: Reference<sageX3FinanceData.nodes.ReminderGroup>;

    @decorators.integerProperty<ReminderGroupReminderFrequencies, 'reminderThreshold'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'SEUILS',
    })
    readonly reminderThreshold: Promise<integer | null>;

    @decorators.integerProperty<ReminderGroupReminderFrequencies, 'reminderInterval'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'FUPINTERVAL',
    })
    readonly reminderInterval: Promise<integer | null>;

    @decorators.decimalProperty<ReminderGroupReminderFrequencies, 'reminderCharge'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'FUPCRG',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.decimalDatatype,
    })
    readonly reminderCharge: Promise<decimal | null>;
}
