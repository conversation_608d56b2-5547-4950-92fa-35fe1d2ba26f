import { decorators, Node, integer, Reference } from '@sage/xtrem-core';
import { X3StorageManager, Joins, Denormalized } from '@sage/xtrem-x3-gateway';
import * as sageXtremX3SystemUtils from '@sage/xtrem-x3-system-utils';
import * as sageX3FinanceData from '..';

const denormalized: Denormalized = { maxRepeat: 10 };

const joins: Joins<AccountReportingCodes> = {
    referenceJoins: {
        _denormalizedParent: {
            chartOfAccounts: 'chartOfAccounts',
            code: 'code',
        },
    },
};

@decorators.node<AccountReportingCodes>({
    storage: 'external',
    tableName: 'GACCOUNT',
    keyPropertyNames: ['denormalizedIndex', 'chartOfAccounts', 'code'],
    indexes: [],
    externalStorageManager: new X3StorageManager({
        joins,
        isDenormalized: true,
        denormalized,
    }),
    isPublished: true,
    canRead: true,
    canSearch: true,
    isVitalCollectionChild: true,
})
export class AccountReportingCodes extends Node {
    @decorators.integerProperty<AccountReportingCodes, 'denormalizedIndex'>({
        isPublished: true,
    })
    readonly denormalizedIndex: Promise<integer>;

    @decorators.stringProperty<AccountReportingCodes, 'chartOfAccounts'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'COA',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly chartOfAccounts: Promise<string>;

    @decorators.stringProperty<AccountReportingCodes, 'code'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'ACC',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<AccountReportingCodes, '_denormalizedParent'>({
        isStored: true,
        isVitalParent: true,
        columnType: 'string',
        node: () => sageX3FinanceData.nodes.Account,
    })
    readonly _denormalizedParent: Reference<sageX3FinanceData.nodes.Account>;

    @decorators.stringProperty<AccountReportingCodes, 'debitReportCode'>({
        isPublished: true,
        isStored: true,
        columnName: 'RPTCODDEB',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly debitReportCode: Promise<string>;

    @decorators.stringProperty<AccountReportingCodes, 'creditReportCode'>({
        isPublished: true,
        isStored: true,
        columnName: 'RPTCODCDT',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly creditReportCode: Promise<string>;
}
