import { decorators, Node, integer, Reference, Context } from '@sage/xtrem-core';
import { X3StorageManager, Joins, Denormalized } from '@sage/xtrem-x3-gateway';
import * as sageXtremX3SystemUtils from '@sage/xtrem-x3-system-utils';
import * as sageX3FinanceData from '..';

const denormalized: Denormalized = {
    maxRepeat: (context: Context) => sageXtremX3SystemUtils.getSizingFromActivityCode(context, 'ANA'),
};

const joins: Joins<BankAccountDimensions> = {
    referenceJoins: {
        _denormalizedParent: {
            bank: 'bank',
        },
        analyticalDimension: {
            dimensionType: 'dimensionType',
            dimension: 'analyticalDimension',
        },
        dimensionType: {
            dimensionType: 'dimensionType',
        },
    },
};

@decorators.node<BankAccountDimensions>({
    storage: 'external',
    tableName: 'BANK',
    keyPropertyNames: ['denormalizedIndex', 'bank'],
    indexes: [],
    externalStorageManager: new X3StorageManager({
        joins,
        isDenormalized: true,
        denormalized,
    }),
    isPublished: true,
    canRead: true,
    canSearch: true,
    isVitalCollectionChild: true,
})
export class BankAccountDimensions extends Node {
    @decorators.integerProperty<BankAccountDimensions, 'denormalizedIndex'>({
        isPublished: true,
    })
    readonly denormalizedIndex: Promise<integer>;

    @decorators.stringProperty<BankAccountDimensions, 'bank'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'BAN',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly bank: Promise<string>;

    @decorators.referenceProperty<BankAccountDimensions, '_denormalizedParent'>({
        isStored: true,
        isVitalParent: true,
        columnType: 'string',
        node: () => sageX3FinanceData.nodes.BankAccount,
    })
    readonly _denormalizedParent: Reference<sageX3FinanceData.nodes.BankAccount>;

    @decorators.referenceProperty<BankAccountDimensions, 'analyticalDimension'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'CCE',
        columnType: 'string',
        node: () => sageX3FinanceData.nodes.Dimension,
    })
    readonly analyticalDimension: Reference<sageX3FinanceData.nodes.Dimension | null>;

    @decorators.referenceProperty<BankAccountDimensions, 'dimensionType'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'DIE',
        columnType: 'string',
        node: () => sageX3FinanceData.nodes.DimensionType,
    })
    readonly dimensionType: Reference<sageX3FinanceData.nodes.DimensionType | null>;
}
