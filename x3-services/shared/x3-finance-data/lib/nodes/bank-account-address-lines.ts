import { decorators, Node, integer, Reference } from '@sage/xtrem-core';
import { X3StorageManager, Joins, Denormalized } from '@sage/xtrem-x3-gateway';
import * as sageXtremX3SystemUtils from '@sage/xtrem-x3-system-utils';
import * as sageX3FinanceData from '..';

const denormalized: Denormalized = { maxRepeat: 3 };

const joins: Joins<BankAccountAddressLines> = {
    referenceJoins: {
        _denormalizedParent: {
            bank: 'bank',
        },
    },
};

@decorators.node<BankAccountAddressLines>({
    storage: 'external',
    tableName: 'BANK',
    keyPropertyNames: ['denormalizedIndex', 'bank'],
    indexes: [],
    externalStorageManager: new X3StorageManager({
        joins,
        isDenormalized: true,
        denormalized,
    }),
    isPublished: true,
    canRead: true,
    canSearch: true,
    isVitalCollectionChild: true,
})
export class BankAccountAddressLines extends Node {
    @decorators.integerProperty<BankAccountAddressLines, 'denormalizedIndex'>({
        isPublished: true,
    })
    readonly denormalizedIndex: Promise<integer>;

    @decorators.stringProperty<BankAccountAddressLines, 'bank'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'BAN',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly bank: Promise<string>;

    @decorators.referenceProperty<BankAccountAddressLines, '_denormalizedParent'>({
        isStored: true,
        isVitalParent: true,
        columnType: 'string',
        node: () => sageX3FinanceData.nodes.BankAccount,
    })
    readonly _denormalizedParent: Reference<sageX3FinanceData.nodes.BankAccount>;

    @decorators.stringProperty<BankAccountAddressLines, 'addressLine'>({
        isPublished: true,
        isStored: true,
        columnName: 'ADDLIG',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly addressLine: Promise<string>;
}
