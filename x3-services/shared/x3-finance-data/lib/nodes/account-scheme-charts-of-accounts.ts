import { decorators, Node, integer, Reference } from '@sage/xtrem-core';
import { X3StorageManager, Joins, Denormalized } from '@sage/xtrem-x3-gateway';
import * as sageXtremX3SystemUtils from '@sage/xtrem-x3-system-utils';
import * as sageX3FinanceData from '..';

const denormalized: Denormalized = { maxRepeat: 9 };

const joins: Joins<AccountSchemeChartsOfAccounts> = {
    referenceJoins: {
        _denormalizedParent: {
            code: 'code',
        },
        chartOfAccounts: {
            code: 'chartOfAccounts',
        },
    },
};

@decorators.node<AccountSchemeChartsOfAccounts>({
    storage: 'external',
    tableName: 'GDIAACC',
    keyPropertyNames: ['denormalizedIndex', 'code'],
    indexes: [],
    externalStorageManager: new X3StorageManager({
        joins,
        isDenormalized: true,
        denormalized,
    }),
    isPublished: true,
    canRead: true,
    canSearch: true,
    isVitalCollectionChild: true,
})
export class AccountSchemeChartsOfAccounts extends Node {
    @decorators.integerProperty<AccountSchemeChartsOfAccounts, 'denormalizedIndex'>({
        isPublished: true,
    })
    readonly denormalizedIndex: Promise<integer>;

    @decorators.stringProperty<AccountSchemeChartsOfAccounts, 'code'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'DIA',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<AccountSchemeChartsOfAccounts, '_denormalizedParent'>({
        isStored: true,
        isVitalParent: true,
        columnType: 'string',
        node: () => sageX3FinanceData.nodes.AccountScheme,
    })
    readonly _denormalizedParent: Reference<sageX3FinanceData.nodes.AccountScheme>;

    @decorators.referenceProperty<AccountSchemeChartsOfAccounts, 'chartOfAccounts'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'COA',
        columnType: 'string',
        node: () => sageX3FinanceData.nodes.ChartOfAccounts,
    })
    readonly chartOfAccounts: Reference<sageX3FinanceData.nodes.ChartOfAccounts | null>;
}
