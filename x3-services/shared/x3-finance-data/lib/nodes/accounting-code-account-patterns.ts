import * as sageX3MasterData from '@sage/x3-master-data';
import { decorators, Node, integer, Reference } from '@sage/xtrem-core';
import { X3StorageManager, Joins, Denormalized } from '@sage/xtrem-x3-gateway';
import * as sageXtremX3SystemUtils from '@sage/xtrem-x3-system-utils';
import * as sageX3FinanceData from '..';

const denormalized: Denormalized = { maxRepeat: 99 };

const joins: Joins<AccountingCodeAccountPatterns> = {
    referenceJoins: {
        _denormalizedParent: {
            type: 'type',
            accountingCode: 'accountingCode',
            chartOfAccounts: 'chartOfAccounts',
        },
    },
};

@decorators.node<AccountingCodeAccountPatterns>({
    storage: 'external',
    tableName: 'GACCCODE',
    keyPropertyNames: ['denormalizedIndex', 'type', 'accountingCode', 'chartOfAccounts'],
    indexes: [],
    externalStorageManager: new X3StorageManager({
        joins,
        isDenormalized: true,
        denormalized,
    }),
    isPublished: true,
    canRead: true,
    canSearch: true,
    isVitalCollectionChild: true,
})
export class AccountingCodeAccountPatterns extends Node {
    @decorators.integerProperty<AccountingCodeAccountPatterns, 'denormalizedIndex'>({
        isPublished: true,
    })
    readonly denormalizedIndex: Promise<integer>;

    @decorators.enumProperty<AccountingCodeAccountPatterns, 'type'>({
        isPublished: true,
        isStored: true,
        columnName: 'TYP',
        dataType: () => sageX3MasterData.enums.accountingCodeTypeDatatype,
    })
    readonly type: Promise<sageX3MasterData.enums.AccountingCodeType>;

    @decorators.stringProperty<AccountingCodeAccountPatterns, 'accountingCode'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'ACCCOD',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly accountingCode: Promise<string>;

    @decorators.stringProperty<AccountingCodeAccountPatterns, 'chartOfAccounts'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'COA',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly chartOfAccounts: Promise<string>;

    @decorators.referenceProperty<AccountingCodeAccountPatterns, '_denormalizedParent'>({
        isStored: true,
        isVitalParent: true,
        columnType: 'string',
        node: () => sageX3FinanceData.nodes.AccountingCode,
    })
    readonly _denormalizedParent: Reference<sageX3FinanceData.nodes.AccountingCode>;

    @decorators.stringProperty<AccountingCodeAccountPatterns, 'accountPattern'>({
        isPublished: true,
        isStored: true,
        columnName: 'ACC',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly accountPattern: Promise<string>;
}
