import { decorators, Node, integer, Reference } from '@sage/xtrem-core';
import { X3StorageManager, Joins, Denormalized } from '@sage/xtrem-x3-gateway';
import * as sageXtremX3SystemUtils from '@sage/xtrem-x3-system-utils';
import * as sageX3FinanceData from '..';

const denormalized: Denormalized = { maxRepeat: 30 };

const joins: Joins<ChartOfAccountsDefaultClasses> = {
    referenceJoins: {
        _denormalizedParent: {
            code: 'code',
        },
    },
};

@decorators.node<ChartOfAccountsDefaultClasses>({
    storage: 'external',
    tableName: 'GCOA',
    keyPropertyNames: ['denormalizedIndex', 'code'],
    indexes: [],
    externalStorageManager: new X3StorageManager({
        joins,
        isDenormalized: true,
        denormalized,
    }),
    isPublished: true,
    canRead: true,
    canSearch: true,
    isVitalCollectionChild: true,
})
export class ChartOfAccountsDefaultClasses extends Node {
    @decorators.integerProperty<ChartOfAccountsDefaultClasses, 'denormalizedIndex'>({
        isPublished: true,
    })
    readonly denormalizedIndex: Promise<integer>;

    @decorators.stringProperty<ChartOfAccountsDefaultClasses, 'code'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'COA',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<ChartOfAccountsDefaultClasses, '_denormalizedParent'>({
        isStored: true,
        isVitalParent: true,
        columnType: 'string',
        node: () => sageX3FinanceData.nodes.ChartOfAccounts,
    })
    readonly _denormalizedParent: Reference<sageX3FinanceData.nodes.ChartOfAccounts>;

    @decorators.stringProperty<ChartOfAccountsDefaultClasses, 'accountPrefix'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'PFX',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly accountPrefix: Promise<string>;
}
