import { decorators, Node, integer, Reference } from '@sage/xtrem-core';
import { X3StorageManager, Joins, Denormalized } from '@sage/xtrem-x3-gateway';
import * as sageXtremX3SystemUtils from '@sage/xtrem-x3-system-utils';
import * as sageX3FinanceData from '..';

const denormalized: Denormalized = { maxRepeat: 50 };

const joins: Joins<ChartOfAccountsMiscellaneousAccounts> = {
    referenceJoins: {
        _denormalizedParent: {
            code: 'code',
        },
        miscellaneousAccount: {
            chartOfAccounts: 'code',
            code: 'miscellaneousAccount',
        },
    },
};

@decorators.node<ChartOfAccountsMiscellaneousAccounts>({
    storage: 'external',
    tableName: 'GCOA',
    keyPropertyNames: ['denormalizedIndex', 'code'],
    indexes: [],
    externalStorageManager: new X3StorageManager({
        joins,
        isDenormalized: true,
        denormalized,
    }),
    isPublished: true,
    canRead: true,
    canSearch: true,
    isVitalCollectionChild: true,
})
export class ChartOfAccountsMiscellaneousAccounts extends Node {
    @decorators.integerProperty<ChartOfAccountsMiscellaneousAccounts, 'denormalizedIndex'>({
        isPublished: true,
    })
    readonly denormalizedIndex: Promise<integer>;

    @decorators.stringProperty<ChartOfAccountsMiscellaneousAccounts, 'code'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'COA',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<ChartOfAccountsMiscellaneousAccounts, '_denormalizedParent'>({
        isStored: true,
        isVitalParent: true,
        columnType: 'string',
        node: () => sageX3FinanceData.nodes.ChartOfAccounts,
    })
    readonly _denormalizedParent: Reference<sageX3FinanceData.nodes.ChartOfAccounts>;

    @decorators.referenceProperty<ChartOfAccountsMiscellaneousAccounts, 'miscellaneousAccount'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'ACCMIS',
        columnType: 'string',
        node: () => sageX3FinanceData.nodes.Account,
    })
    readonly miscellaneousAccount: Reference<sageX3FinanceData.nodes.Account | null>;
}
