import { decorators, Node, integer, Reference } from '@sage/xtrem-core';
import { X3StorageManager, Joins, Denormalized } from '@sage/xtrem-x3-gateway';
import * as sageXtremX3SystemUtils from '@sage/xtrem-x3-system-utils';
import * as sageX3FinanceData from '..';

const denormalized: Denormalized = { maxRepeat: 9 };

const joins: Joins<JournalEntryTransactionAccountStructures> = {
    referenceJoins: {
        _denormalizedParent: {
            code: 'code',
        },
        accountStructure: {
            code: 'accountStructure',
        },
    },
};

@decorators.node<JournalEntryTransactionAccountStructures>({
    storage: 'external',
    tableName: 'GDIAENTRY',
    keyPropertyNames: ['denormalizedIndex', 'code'],
    indexes: [],
    externalStorageManager: new X3StorageManager({
        joins,
        isDenormalized: true,
        denormalized,
    }),
    isPublished: true,
    canRead: true,
    canSearch: true,
    isVitalCollectionChild: true,
})
export class JournalEntryTransactionAccountStructures extends Node {
    @decorators.integerProperty<JournalEntryTransactionAccountStructures, 'denormalizedIndex'>({
        isPublished: true,
    })
    readonly denormalizedIndex: Promise<integer>;

    @decorators.stringProperty<JournalEntryTransactionAccountStructures, 'code'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'DIA',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<JournalEntryTransactionAccountStructures, '_denormalizedParent'>({
        isStored: true,
        isVitalParent: true,
        columnType: 'string',
        node: () => sageX3FinanceData.nodes.JournalEntryTransaction,
    })
    readonly _denormalizedParent: Reference<sageX3FinanceData.nodes.JournalEntryTransaction>;

    @decorators.referenceProperty<JournalEntryTransactionAccountStructures, 'accountStructure'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'DIAACC',
        columnType: 'string',
        node: () => sageX3FinanceData.nodes.AccountScheme,
    })
    readonly accountStructure: Reference<sageX3FinanceData.nodes.AccountScheme | null>;
}
