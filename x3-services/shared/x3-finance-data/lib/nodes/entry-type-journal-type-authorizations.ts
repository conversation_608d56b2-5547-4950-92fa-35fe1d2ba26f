import { decorators, Node, integer, Reference } from '@sage/xtrem-core';
import { X3StorageManager, Joins, Denormalized } from '@sage/xtrem-x3-gateway';
import * as sageXtremX3SystemUtils from '@sage/xtrem-x3-system-utils';
import * as sageX3FinanceData from '..';

const denormalized: Denormalized = { maxRepeat: 10 };

const joins: Joins<EntryTypeJournalTypeAuthorizations> = {
    referenceJoins: {
        _denormalizedParent: {
            code: 'code',
            legislation: 'legislation',
        },
    },
};

@decorators.node<EntryTypeJournalTypeAuthorizations>({
    storage: 'external',
    tableName: 'GTYPACCENT',
    keyPropertyNames: ['denormalizedIndex', 'code', 'legislation'],
    indexes: [],
    externalStorageManager: new X3StorageManager({
        joins,
        isDenormalized: true,
        denormalized,
    }),
    isPublished: true,
    canRead: true,
    canSearch: true,
    isVitalCollectionChild: true,
})
export class EntryTypeJournalTypeAuthorizations extends Node {
    @decorators.integerProperty<EntryTypeJournalTypeAuthorizations, 'denormalizedIndex'>({
        isPublished: true,
    })
    readonly denormalizedIndex: Promise<integer>;

    @decorators.stringProperty<EntryTypeJournalTypeAuthorizations, 'code'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'TYP',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<EntryTypeJournalTypeAuthorizations, 'legislation'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'LEG',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly legislation: Promise<string>;

    @decorators.referenceProperty<EntryTypeJournalTypeAuthorizations, '_denormalizedParent'>({
        isStored: true,
        isVitalParent: true,
        columnType: 'string',
        node: () => sageX3FinanceData.nodes.EntryType,
    })
    readonly _denormalizedParent: Reference<sageX3FinanceData.nodes.EntryType>;

    @decorators.booleanProperty<EntryTypeJournalTypeAuthorizations, 'journalTypeAuthorization'>({
        isPublished: true,
        isStored: true,
        columnName: 'AUZJOU',
    })
    readonly journalTypeAuthorization: Promise<boolean>;
}
