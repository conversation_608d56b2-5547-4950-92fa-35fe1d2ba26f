import * as sageX3System from '@sage/x3-system';
import { decorators, Node, integer, Reference } from '@sage/xtrem-core';
import { X3StorageManager, Joins, Denormalized } from '@sage/xtrem-x3-gateway';
import * as sageXtremX3SystemUtils from '@sage/xtrem-x3-system-utils';
import * as sageX3FinanceData from '..';

const denormalized: Denormalized = { maxRepeat: 5 };

const joins: Joins<BankAccountBankDetails> = {
    referenceJoins: {
        _denormalizedParent: {
            bank: 'bank',
        },
    },
};

@decorators.node<BankAccountBankDetails>({
    storage: 'external',
    tableName: 'BANK',
    keyPropertyNames: ['denormalizedIndex', 'bank'],
    indexes: [],
    externalStorageManager: new X3StorageManager({
        joins,
        isDenormalized: true,
        denormalized,
    }),
    isPublished: true,
    canRead: true,
    canSearch: true,
    isVitalCollectionChild: true,
})
export class BankAccountBankDetails extends Node {
    @decorators.integerProperty<BankAccountBankDetails, 'denormalizedIndex'>({
        isPublished: true,
    })
    readonly denormalizedIndex: Promise<integer>;

    @decorators.stringProperty<BankAccountBankDetails, 'bank'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'BAN',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly bank: Promise<string>;

    @decorators.referenceProperty<BankAccountBankDetails, '_denormalizedParent'>({
        isStored: true,
        isVitalParent: true,
        columnType: 'string',
        node: () => sageX3FinanceData.nodes.BankAccount,
    })
    readonly _denormalizedParent: Reference<sageX3FinanceData.nodes.BankAccount>;

    @decorators.stringProperty<BankAccountBankDetails, 'ukSortCode'>({
        isPublished: true,
        isStored: true,
        columnName: 'SRTCOD',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
        serviceOptions: () => [sageX3System.serviceOptions.KukActivityCode],
    })
    readonly ukSortCode: Promise<string>;

    @decorators.booleanProperty<BankAccountBankDetails, 'isHerMajestyRevenueAndCustomsRefunds'>({
        isPublished: true,
        isStored: true,
        columnName: 'HMRC',
        serviceOptions: () => [sageX3System.serviceOptions.KukActivityCode],
    })
    readonly isHerMajestyRevenueAndCustomsRefunds: Promise<boolean>;
}
