import { decorators, Node, integer, Reference, Context } from '@sage/xtrem-core';
import { X3StorageManager, Joins, Denormalized } from '@sage/xtrem-x3-gateway';
import * as sageXtremX3SystemUtils from '@sage/xtrem-x3-system-utils';
import * as sageX3FinanceData from '..';

const denormalized: Denormalized = {
    maxRepeat: (context: Context) => sageXtremX3SystemUtils.getSizingFromActivityCode(context, 'ANA'),
};

const joins: Joins<DimensionDimensions> = {
    referenceJoins: {
        _denormalizedParent: {
            dimensionType: 'dimensionType',
            dimension: 'dimension',
        },
        otherDimension: {
            dimensionType: 'otherDimension',
        },
        defaultDimension: {
            dimensionType: 'otherDimension',
            dimension: 'defaultDimension',
        },
    },
};

@decorators.node<DimensionDimensions>({
    storage: 'external',
    tableName: 'CACCE',
    keyPropertyNames: ['denormalizedIndex', 'dimensionType', 'dimension'],
    indexes: [],
    externalStorageManager: new X3StorageManager({
        joins,
        isDenormalized: true,
        denormalized,
    }),
    isPublished: true,
    canRead: true,
    canSearch: true,
    isVitalCollectionChild: true,
})
export class DimensionDimensions extends Node {
    @decorators.integerProperty<DimensionDimensions, 'denormalizedIndex'>({
        isPublished: true,
    })
    readonly denormalizedIndex: Promise<integer>;

    @decorators.stringProperty<DimensionDimensions, 'dimensionType'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'DIE',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly dimensionType: Promise<string>;

    @decorators.stringProperty<DimensionDimensions, 'dimension'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'CCE',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly dimension: Promise<string>;

    @decorators.referenceProperty<DimensionDimensions, '_denormalizedParent'>({
        isStored: true,
        isVitalParent: true,
        columnType: 'string',
        node: () => sageX3FinanceData.nodes.Dimension,
    })
    readonly _denormalizedParent: Reference<sageX3FinanceData.nodes.Dimension>;

    @decorators.referenceProperty<DimensionDimensions, 'otherDimension'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'OTHDIE',
        columnType: 'string',
        node: () => sageX3FinanceData.nodes.DimensionType,
    })
    readonly otherDimension: Reference<sageX3FinanceData.nodes.DimensionType | null>;

    @decorators.referenceProperty<DimensionDimensions, 'defaultDimension'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'DEFCCE',
        columnType: 'string',
        node: () => sageX3FinanceData.nodes.Dimension,
    })
    readonly defaultDimension: Reference<sageX3FinanceData.nodes.Dimension | null>;
}
