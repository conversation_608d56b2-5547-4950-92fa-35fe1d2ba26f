import * as sageX3MasterData from '@sage/x3-master-data';
import { decorators, Node, integer, Reference } from '@sage/xtrem-core';
import { X3StorageManager, Joins, Denormalized } from '@sage/xtrem-x3-gateway';
import * as sageXtremX3SystemUtils from '@sage/xtrem-x3-system-utils';
import * as sageX3FinanceData from '..';

const denormalized: Denormalized = { maxRepeat: 10 };

const joins: Joins<JournalEntryTransactionLedgerTypes> = {
    referenceJoins: {
        _denormalizedParent: {
            code: 'code',
        },
    },
};

@decorators.node<JournalEntryTransactionLedgerTypes>({
    storage: 'external',
    tableName: 'GDIAENTRY',
    keyPropertyNames: ['denormalizedIndex', 'code'],
    indexes: [],
    externalStorageManager: new X3StorageManager({
        joins,
        isDenormalized: true,
        denormalized,
    }),
    isPublished: true,
    canRead: true,
    canSearch: true,
    isVitalCollectionChild: true,
})
export class JournalEntryTransactionLedgerTypes extends Node {
    @decorators.integerProperty<JournalEntryTransactionLedgerTypes, 'denormalizedIndex'>({
        isPublished: true,
    })
    readonly denormalizedIndex: Promise<integer>;

    @decorators.stringProperty<JournalEntryTransactionLedgerTypes, 'code'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'DIA',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<JournalEntryTransactionLedgerTypes, '_denormalizedParent'>({
        isStored: true,
        isVitalParent: true,
        columnType: 'string',
        node: () => sageX3FinanceData.nodes.JournalEntryTransaction,
    })
    readonly _denormalizedParent: Reference<sageX3FinanceData.nodes.JournalEntryTransaction>;

    @decorators.enumProperty<JournalEntryTransactionLedgerTypes, 'ledgerType'>({
        isPublished: true,
        isStored: true,
        columnName: 'LEDTYP',
        dataType: () => sageX3MasterData.enums.generalLedgerTypesDatatype,
    })
    readonly ledgerType: Promise<sageX3MasterData.enums.GeneralLedgerTypes>;
}
