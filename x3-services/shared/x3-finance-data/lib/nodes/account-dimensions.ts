import { decorators, Node, integer, Reference, Context } from '@sage/xtrem-core';
import { X3StorageManager, Joins, Denormalized } from '@sage/xtrem-x3-gateway';
import * as sageXtremX3SystemUtils from '@sage/xtrem-x3-system-utils';
import * as sageX3FinanceData from '..';

const denormalized: Denormalized = {
    maxRepeat: (context: Context) => sageXtremX3SystemUtils.getSizingFromActivityCode(context, 'ANA'),
};

const joins: Joins<AccountDimensions> = {
    referenceJoins: {
        _denormalizedParent: {
            chartOfAccounts: 'chartOfAccounts',
            code: 'code',
        },
        dimensionType: {
            dimensionType: 'dimensionType',
        },
        defaultDimension: {
            dimensionType: 'dimensionType',
            dimension: 'defaultDimension',
        },
    },
};

@decorators.node<AccountDimensions>({
    storage: 'external',
    tableName: 'GACCOUNT',
    keyPropertyNames: ['denormalizedIndex', 'chartOfAccounts', 'code'],
    indexes: [],
    externalStorageManager: new X3StorageManager({
        joins,
        isDenormalized: true,
        denormalized,
    }),
    isPublished: true,
    canRead: true,
    canSearch: true,
    isVitalCollectionChild: true,
})
export class AccountDimensions extends Node {
    @decorators.integerProperty<AccountDimensions, 'denormalizedIndex'>({
        isPublished: true,
    })
    readonly denormalizedIndex: Promise<integer>;

    @decorators.stringProperty<AccountDimensions, 'chartOfAccounts'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'COA',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly chartOfAccounts: Promise<string>;

    @decorators.stringProperty<AccountDimensions, 'code'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'ACC',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<AccountDimensions, '_denormalizedParent'>({
        isStored: true,
        isVitalParent: true,
        columnType: 'string',
        node: () => sageX3FinanceData.nodes.Account,
    })
    readonly _denormalizedParent: Reference<sageX3FinanceData.nodes.Account>;

    @decorators.referenceProperty<AccountDimensions, 'dimensionType'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'DIE',
        columnType: 'string',
        node: () => sageX3FinanceData.nodes.DimensionType,
    })
    readonly dimensionType: Reference<sageX3FinanceData.nodes.DimensionType | null>;

    @decorators.referenceProperty<AccountDimensions, 'defaultDimension'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'CCEDEF',
        columnType: 'string',
        node: () => sageX3FinanceData.nodes.Dimension,
    })
    readonly defaultDimension: Reference<sageX3FinanceData.nodes.Dimension | null>;
}
