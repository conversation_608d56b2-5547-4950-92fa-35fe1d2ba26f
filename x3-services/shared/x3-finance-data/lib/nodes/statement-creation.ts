import * as sageX3MasterData from '@sage/x3-master-data';
import * as sageX3System from '@sage/x3-system';
import { decorators, Node, Reference } from '@sage/xtrem-core';
import { X3StorageManager, Joins } from '@sage/xtrem-x3-gateway';
import * as sageXtremX3SystemUtils from '@sage/xtrem-x3-system-utils';

const joins: Joins<StatementCreation> = {
    referenceJoins: {
        businessPartner: {
            code: 'businessPartner',
        },
        company: {
            code: 'company',
        },
    },
};

@decorators.node<StatementCreation>({
    storage: 'external',
    tableName: 'SOI',
    keyPropertyNames: ['id'],
    indexes: [
        {
            orderBy: {
                id: 1,
            },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    externalStorageManager: new X3StorageManager({
        joins,
    }),
    isPublished: true,
    canRead: true,
    canSearch: true,
})
export class StatementCreation extends Node {
    @decorators.stringProperty<StatementCreation, 'id'>({
        isPublished: true,
        isStored: true,
        columnName: 'SOINUM',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly id: Promise<string>;

    @decorators.referenceProperty<StatementCreation, 'businessPartner'>({
        isPublished: true,
        isStored: true,
        columnName: 'BPR',
        columnType: 'string',
        node: () => sageX3MasterData.nodes.BusinessPartner,
    })
    readonly businessPartner: Reference<sageX3MasterData.nodes.BusinessPartner>;

    @decorators.referenceProperty<StatementCreation, 'company'>({
        isPublished: true,
        isStored: true,
        columnName: 'CPY',
        columnType: 'string',
        node: () => sageX3System.nodes.Company,
    })
    readonly company: Reference<sageX3System.nodes.Company>;
}
