import * as sageX3MasterData from '@sage/x3-master-data';
import { decorators, Node, integer, Reference, decimal } from '@sage/xtrem-core';
import { X3StorageManager, Joins, Denormalized } from '@sage/xtrem-x3-gateway';
import * as sageXtremX3SystemUtils from '@sage/xtrem-x3-system-utils';
import * as sageX3FinanceData from '..';

const denormalized: Denormalized = { maxRepeat: 20 };

const joins: Joins<DimensionNonfinancialUnits> = {
    referenceJoins: {
        _denormalizedParent: {
            dimensionType: 'dimensionType',
            dimension: 'dimension',
        },
        unit: {
            code: 'unit',
        },
    },
};

@decorators.node<DimensionNonfinancialUnits>({
    storage: 'external',
    tableName: 'CACCE',
    keyPropertyNames: ['denormalizedIndex', 'dimensionType', 'dimension'],
    indexes: [],
    externalStorageManager: new X3StorageManager({
        joins,
        isDenormalized: true,
        denormalized,
    }),
    isPublished: true,
    canRead: true,
    canSearch: true,
    isVitalCollectionChild: true,
})
export class DimensionNonfinancialUnits extends Node {
    @decorators.integerProperty<DimensionNonfinancialUnits, 'denormalizedIndex'>({
        isPublished: true,
    })
    readonly denormalizedIndex: Promise<integer>;

    @decorators.stringProperty<DimensionNonfinancialUnits, 'dimensionType'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'DIE',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly dimensionType: Promise<string>;

    @decorators.stringProperty<DimensionNonfinancialUnits, 'dimension'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'CCE',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly dimension: Promise<string>;

    @decorators.referenceProperty<DimensionNonfinancialUnits, '_denormalizedParent'>({
        isStored: true,
        isVitalParent: true,
        columnType: 'string',
        node: () => sageX3FinanceData.nodes.Dimension,
    })
    readonly _denormalizedParent: Reference<sageX3FinanceData.nodes.Dimension>;

    @decorators.referenceProperty<DimensionNonfinancialUnits, 'unit'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'UOM',
        columnType: 'string',
        node: () => sageX3MasterData.nodes.UnitOfMeasure,
    })
    readonly unit: Reference<sageX3MasterData.nodes.UnitOfMeasure | null>;

    @decorators.decimalProperty<DimensionNonfinancialUnits, 'quantity'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'QTY',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.decimalDatatype,
    })
    readonly quantity: Promise<decimal | null>;
}
