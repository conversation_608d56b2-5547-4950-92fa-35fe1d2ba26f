import * as sageX3InvoicingData from '@sage/x3-invoicing-data';
import { decorators, Node, integer, Reference } from '@sage/xtrem-core';
import { X3StorageManager, Joins, Denormalized } from '@sage/xtrem-x3-gateway';
import * as sageXtremX3SystemUtils from '@sage/xtrem-x3-system-utils';
import * as sageX3FinanceData from '..';

const denormalized: Denormalized = { maxRepeat: 3 };

const joins: Joins<AccountTaxes> = {
    referenceJoins: {
        _denormalizedParent: {
            chartOfAccounts: 'chartOfAccounts',
            code: 'code',
        },
        tax: {
            code: 'tax',
            async legislation() {
                return (await (await (await this._denormalizedParent).chartOfAccounts).legislation)?.code;
            },
        },
    },
};

@decorators.node<AccountTaxes>({
    storage: 'external',
    tableName: 'GACCOUNT',
    keyPropertyNames: ['denormalizedIndex', 'chartOfAccounts', 'code'],
    indexes: [],
    externalStorageManager: new X3StorageManager({
        joins,
        isDenormalized: true,
        denormalized,
    }),
    isPublished: true,
    canRead: true,
    canSearch: true,
    isVitalCollectionChild: true,
})
export class AccountTaxes extends Node {
    @decorators.integerProperty<AccountTaxes, 'denormalizedIndex'>({
        isPublished: true,
    })
    readonly denormalizedIndex: Promise<integer>;

    @decorators.stringProperty<AccountTaxes, 'chartOfAccounts'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'COA',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly chartOfAccounts: Promise<string>;

    @decorators.stringProperty<AccountTaxes, 'code'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'ACC',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<AccountTaxes, '_denormalizedParent'>({
        isStored: true,
        isVitalParent: true,
        columnType: 'string',
        node: () => sageX3FinanceData.nodes.Account,
    })
    readonly _denormalizedParent: Reference<sageX3FinanceData.nodes.Account>;

    @decorators.referenceProperty<AccountTaxes, 'tax'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'VAT',
        columnType: 'string',
        node: () => sageX3InvoicingData.nodes.Tax,
    })
    readonly tax: Reference<sageX3InvoicingData.nodes.Tax | null>;
}
