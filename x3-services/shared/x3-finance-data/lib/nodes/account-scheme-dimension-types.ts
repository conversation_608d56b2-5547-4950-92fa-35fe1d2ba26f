import { decorators, Node, integer, Reference, Context } from '@sage/xtrem-core';
import { X3StorageManager, Joins, Denormalized } from '@sage/xtrem-x3-gateway';
import * as sageXtremX3SystemUtils from '@sage/xtrem-x3-system-utils';
import * as sageX3FinanceData from '..';

const denormalized: Denormalized = {
    maxRepeat: (context: Context) => sageXtremX3SystemUtils.getSizingFromActivityCode(context, 'ANA'),
};

const joins: Joins<AccountSchemeDimensionTypes> = {
    referenceJoins: {
        _denormalizedParent: {
            code: 'code',
        },
        dimensionType: {
            dimensionType: 'dimensionType',
        },
    },
};

@decorators.node<AccountSchemeDimensionTypes>({
    storage: 'external',
    tableName: 'GDIAACC',
    keyPropertyNames: ['denormalizedIndex', 'code'],
    indexes: [],
    externalStorageManager: new X3StorageManager({
        joins,
        isDenormalized: true,
        denormalized,
    }),
    isPublished: true,
    canRead: true,
    canSearch: true,
    isVitalCollectionChild: true,
})
export class AccountSchemeDimensionTypes extends Node {
    @decorators.integerProperty<AccountSchemeDimensionTypes, 'denormalizedIndex'>({
        isPublished: true,
    })
    readonly denormalizedIndex: Promise<integer>;

    @decorators.stringProperty<AccountSchemeDimensionTypes, 'code'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'DIA',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<AccountSchemeDimensionTypes, '_denormalizedParent'>({
        isStored: true,
        isVitalParent: true,
        columnType: 'string',
        node: () => sageX3FinanceData.nodes.AccountScheme,
    })
    readonly _denormalizedParent: Reference<sageX3FinanceData.nodes.AccountScheme>;

    @decorators.referenceProperty<AccountSchemeDimensionTypes, 'dimensionType'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'DIE',
        columnType: 'string',
        node: () => sageX3FinanceData.nodes.DimensionType,
    })
    readonly dimensionType: Reference<sageX3FinanceData.nodes.DimensionType | null>;
}
