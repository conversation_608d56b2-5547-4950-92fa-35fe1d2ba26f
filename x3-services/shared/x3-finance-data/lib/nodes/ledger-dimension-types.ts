import { decorators, Node, integer, Reference } from '@sage/xtrem-core';
import { X3StorageManager, Joins, Denormalized } from '@sage/xtrem-x3-gateway';
import * as sageXtremX3SystemUtils from '@sage/xtrem-x3-system-utils';
import * as sageX3FinanceData from '..';

const denormalized: Denormalized = { maxRepeat: 9 };

const joins: Joins<LedgerDimensionTypes> = {
    referenceJoins: {
        _denormalizedParent: {
            code: 'code',
        },
        dimensionType: {
            dimensionType: 'dimensionType',
        },
    },
};

@decorators.node<LedgerDimensionTypes>({
    storage: 'external',
    tableName: 'GLED',
    keyPropertyNames: ['denormalizedIndex', 'code'],
    indexes: [],
    externalStorageManager: new X3StorageManager({
        joins,
        isDenormalized: true,
        denormalized,
    }),
    isPublished: true,
    canRead: true,
    canSearch: true,
    isVitalCollectionChild: true,
})
export class LedgerDimensionTypes extends Node {
    @decorators.integerProperty<LedgerDimensionTypes, 'denormalizedIndex'>({
        isPublished: true,
    })
    readonly denormalizedIndex: Promise<integer>;

    @decorators.stringProperty<LedgerDimensionTypes, 'code'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'LED',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<LedgerDimensionTypes, '_denormalizedParent'>({
        isStored: true,
        isVitalParent: true,
        columnType: 'string',
        node: () => sageX3FinanceData.nodes.Ledger,
    })
    readonly _denormalizedParent: Reference<sageX3FinanceData.nodes.Ledger>;

    @decorators.referenceProperty<LedgerDimensionTypes, 'dimensionType'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'DIE',
        columnType: 'string',
        node: () => sageX3FinanceData.nodes.DimensionType,
    })
    readonly dimensionType: Reference<sageX3FinanceData.nodes.DimensionType | null>;
}
