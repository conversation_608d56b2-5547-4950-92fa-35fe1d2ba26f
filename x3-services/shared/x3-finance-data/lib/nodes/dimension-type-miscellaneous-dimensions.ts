import { decorators, Node, integer, Reference } from '@sage/xtrem-core';
import { X3StorageManager, Joins, Denormalized } from '@sage/xtrem-x3-gateway';
import * as sageXtremX3SystemUtils from '@sage/xtrem-x3-system-utils';
import * as sageX3FinanceData from '..';

const denormalized: Denormalized = { maxRepeat: 10 };

const joins: Joins<DimensionTypeMiscellaneousDimensions> = {
    referenceJoins: {
        _denormalizedParent: {
            dimensionType: 'dimensionType',
        },
        miscellaneousDimension: {
            dimensionType: 'dimensionType',
            dimension: 'miscellaneousDimension',
        },
    },
};

@decorators.node<DimensionTypeMiscellaneousDimensions>({
    storage: 'external',
    tableName: 'GDIE',
    keyPropertyNames: ['denormalizedIndex', 'dimensionType'],
    indexes: [],
    externalStorageManager: new X3StorageManager({
        joins,
        isDenormalized: true,
        denormalized,
    }),
    isPublished: true,
    canRead: true,
    canSearch: true,
    isVitalCollectionChild: true,
})
export class DimensionTypeMiscellaneousDimensions extends Node {
    @decorators.integerProperty<DimensionTypeMiscellaneousDimensions, 'denormalizedIndex'>({
        isPublished: true,
    })
    readonly denormalizedIndex: Promise<integer>;

    @decorators.stringProperty<DimensionTypeMiscellaneousDimensions, 'dimensionType'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'DIE',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly dimensionType: Promise<string>;

    @decorators.referenceProperty<DimensionTypeMiscellaneousDimensions, '_denormalizedParent'>({
        isStored: true,
        isVitalParent: true,
        columnType: 'string',
        node: () => sageX3FinanceData.nodes.DimensionType,
    })
    readonly _denormalizedParent: Reference<sageX3FinanceData.nodes.DimensionType>;

    @decorators.referenceProperty<DimensionTypeMiscellaneousDimensions, 'miscellaneousDimension'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'CCEMIS',
        columnType: 'string',
        node: () => sageX3FinanceData.nodes.Dimension,
    })
    readonly miscellaneousDimension: Reference<sageX3FinanceData.nodes.Dimension | null>;
}
