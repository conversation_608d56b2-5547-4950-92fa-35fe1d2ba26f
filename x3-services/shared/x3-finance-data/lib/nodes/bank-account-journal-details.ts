import { decorators, Node, integer, Reference } from '@sage/xtrem-core';
import { X3StorageManager, Joins, Denormalized } from '@sage/xtrem-x3-gateway';
import * as sageXtremX3SystemUtils from '@sage/xtrem-x3-system-utils';
import * as sageX3FinanceData from '..';

const denormalized: Denormalized = { maxRepeat: 10 };

const joins: Joins<BankAccountJournalDetails> = {
    referenceJoins: {
        _denormalizedParent: {
            bank: 'bank',
        },
        journal: {
            code: 'journal',
            async legislation() {
                return (await (await (await this._denormalizedParent).company).legislation)?.code;
            },
        },
        entryType: {
            code: 'entryType',
            async legislation() {
                return (await (await (await this._denormalizedParent).company).legislation)?.code;
            },
        },
        cashAccount: {
            chartOfAccounts: 'chartOfAccounts',
            code: 'cashAccount',
        },
    },
};

@decorators.node<BankAccountJournalDetails>({
    storage: 'external',
    tableName: 'BANK',
    keyPropertyNames: ['denormalizedIndex', 'bank'],
    indexes: [],
    externalStorageManager: new X3StorageManager({
        joins,
        isDenormalized: true,
        denormalized,
    }),
    isPublished: true,
    canRead: true,
    canSearch: true,
    isVitalCollectionChild: true,
})
export class BankAccountJournalDetails extends Node {
    @decorators.integerProperty<BankAccountJournalDetails, 'denormalizedIndex'>({
        isPublished: true,
    })
    readonly denormalizedIndex: Promise<integer>;

    @decorators.stringProperty<BankAccountJournalDetails, 'bank'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'BAN',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly bank: Promise<string>;

    @decorators.referenceProperty<BankAccountJournalDetails, '_denormalizedParent'>({
        isStored: true,
        isVitalParent: true,
        columnType: 'string',
        node: () => sageX3FinanceData.nodes.BankAccount,
    })
    readonly _denormalizedParent: Reference<sageX3FinanceData.nodes.BankAccount>;

    @decorators.enumProperty<BankAccountJournalDetails, 'journalType'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'JOUTYP',
        dataType: () => sageX3FinanceData.enums.journalTypesDatatype,
    })
    readonly journalType: Promise<sageX3FinanceData.enums.JournalTypes | null>;

    @decorators.referenceProperty<BankAccountJournalDetails, 'journal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'JOU',
        columnType: 'string',
        node: () => sageX3FinanceData.nodes.Journal,
    })
    readonly journal: Reference<sageX3FinanceData.nodes.Journal | null>;

    @decorators.referenceProperty<BankAccountJournalDetails, 'entryType'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'GTE',
        columnType: 'string',
        node: () => sageX3FinanceData.nodes.EntryType,
    })
    readonly entryType: Reference<sageX3FinanceData.nodes.EntryType | null>;

    @decorators.referenceProperty<BankAccountJournalDetails, 'cashAccount'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'TREACC',
        columnType: 'string',
        node: () => sageX3FinanceData.nodes.Account,
    })
    readonly cashAccount: Reference<sageX3FinanceData.nodes.Account | null>;
}
