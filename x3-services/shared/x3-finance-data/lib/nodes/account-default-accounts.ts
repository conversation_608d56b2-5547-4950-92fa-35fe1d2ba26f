import { decorators, Node, integer, Reference, Context } from '@sage/xtrem-core';
import { X3StorageManager, Joins, Denormalized } from '@sage/xtrem-x3-gateway';
import * as sageXtremX3SystemUtils from '@sage/xtrem-x3-system-utils';
import * as sageX3FinanceData from '..';

const denormalized: Denormalized = {
    maxRepeat: (context: Context) => sageXtremX3SystemUtils.getSizingFromActivityCode(context, 'NBCOA'),
};

const joins: Joins<AccountDefaultAccounts> = {
    referenceJoins: {
        _denormalizedParent: {
            chartOfAccounts: 'chartOfAccounts',
            code: 'code',
        },
        otherChartOfAccounts: {
            code: 'otherChartOfAccounts',
        },
        defaultAccount: {
            chartOfAccounts: 'otherChartOfAccounts',
            code: 'defaultAccount',
        },
    },
};

@decorators.node<AccountDefaultAccounts>({
    storage: 'external',
    tableName: 'GACCOUNT',
    keyPropertyNames: ['denormalizedIndex', 'chartOfAccounts', 'code'],
    indexes: [],
    externalStorageManager: new X3StorageManager({
        joins,
        isDenormalized: true,
        denormalized,
    }),
    isPublished: true,
    canRead: true,
    canSearch: true,
    isVitalCollectionChild: true,
})
export class AccountDefaultAccounts extends Node {
    @decorators.integerProperty<AccountDefaultAccounts, 'denormalizedIndex'>({
        isPublished: true,
    })
    readonly denormalizedIndex: Promise<integer>;

    @decorators.stringProperty<AccountDefaultAccounts, 'chartOfAccounts'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'COA',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly chartOfAccounts: Promise<string>;

    @decorators.stringProperty<AccountDefaultAccounts, 'code'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'ACC',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<AccountDefaultAccounts, '_denormalizedParent'>({
        isStored: true,
        isVitalParent: true,
        columnType: 'string',
        node: () => sageX3FinanceData.nodes.Account,
    })
    readonly _denormalizedParent: Reference<sageX3FinanceData.nodes.Account>;

    @decorators.referenceProperty<AccountDefaultAccounts, 'otherChartOfAccounts'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'OTHCOA',
        columnType: 'string',
        node: () => sageX3FinanceData.nodes.ChartOfAccounts,
    })
    readonly otherChartOfAccounts: Reference<sageX3FinanceData.nodes.ChartOfAccounts | null>;

    @decorators.referenceProperty<AccountDefaultAccounts, 'defaultAccount'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'DEFACC',
        columnType: 'string',
        node: () => sageX3FinanceData.nodes.Account,
    })
    readonly defaultAccount: Reference<sageX3FinanceData.nodes.Account | null>;

    @decorators.stringProperty<AccountDefaultAccounts, 'accountScreening'>({
        isPublished: true,
        isStored: true,
        columnName: 'SCRACC',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly accountScreening: Promise<string>;

    @decorators.booleanProperty<AccountDefaultAccounts, 'isMandatoryAllocation'>({
        isPublished: true,
        isStored: true,
        columnName: 'OBYIPT',
    })
    readonly isMandatoryAllocation: Promise<boolean>;
}
