{
    x3MasterData {
        supplierProductSite {
            query(filter: "{product:'I-OGADCITM001',supplier:'I-JBADCSUP01', stockSite:'I-AD1'}") {
                edges {
                    node {
                        product {
                            product {
                                code
                            }
                        }
                        supplier {
                            code
                        }
                        stockSite {
                            code
                        }
                        supplierProduct
                        supplierProductDescription
                        supplierUpc
                        purchaseUnit {
                            code
                        }
                        purchaseUnitToStockUnitConversionFactor
                        packingUnit {
                            code
                        }
                        packingUnitToPurchaseUnitConversionFactor
                        intrastatIncreaseCoefficient
                        minimumPurchaseQuantity
                        qualityRank
                        qualityControlManagementMode
                        isBackToBackOrder
                        subcontractBom {
                            code
                        }
                        subcontractLeadTime
                        technicalSheet {
                            technicalSheet
                        }
                        qualityControlFrequency
                        numberOfEntriesSinceLastQualityControl
                        qualityControlFrequencyToReview
                        qualityEntriesProcess
                        isVersionPreloaded
                        isDefaultSupplier
                        isDefaultBackToBackSupplier
                        purchasingText {
                            value
                        }
                        priority
                    }
                }
            }
        }
    }
}
