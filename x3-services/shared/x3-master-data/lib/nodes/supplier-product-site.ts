import * as sageX3System from '@sage/x3-system';
import { decorators, Node, Reference, integer, decimal, TextStream } from '@sage/xtrem-core';
import { X3StorageManager, Joins } from '@sage/xtrem-x3-gateway';
import * as sageXtremX3SystemUtils from '@sage/xtrem-x3-system-utils';
import * as sageX3MasterData from '..';

const joins: Joins<SupplierProductSite> = {
    referenceJoins: {
        product: {
            product: 'product',
            stockSite: 'stockSite',
        },
        supplier: {
            code: 'supplier',
        },
        stockSite: {
            code: 'stockSite',
        },
        purchaseUnit: {
            code: 'purchaseUnit',
        },
        packingUnit: {
            code: 'packingUnit',
        },
        subcontractBom: {
            bomType() {
                return 'salesKit';
            },
            code: 'subcontractBom',
        },
        technicalSheet: {
            technicalSheet: 'technicalSheet',
            lineNumber() {
                return 1;
            },
        },
        purchasingTextRef: {
            code: 'purchasingTextKey',
        },
    },
};

const compositeReferences = {
    purchasingTextRef: {
        purchasingText: 'text',
    },
};

@decorators.node<SupplierProductSite>({
    storage: 'external',
    tableName: 'ITMBPSFCY',
    keyPropertyNames: ['product', 'supplier', 'stockSite'],
    indexes: [
        {
            orderBy: {
                product: 1,
                supplier: 1,
                stockSite: 1,
            },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    externalStorageManager: new X3StorageManager({
        joins,
        compositeReferences,
    }),
    isPublished: true,
    canRead: true,
    canSearch: true,
})
export class SupplierProductSite extends Node {
    @decorators.referenceProperty<SupplierProductSite, 'product'>({
        isPublished: true,
        isStored: true,
        columnName: 'ITMREF',
        columnType: 'string',
        node: () => sageX3MasterData.nodes.ProductSite,
    })
    readonly product: Reference<sageX3MasterData.nodes.ProductSite>;

    @decorators.referenceProperty<SupplierProductSite, 'supplier'>({
        isPublished: true,
        isStored: true,
        columnName: 'BPSNUM',
        columnType: 'string',
        node: () => sageX3MasterData.nodes.BusinessPartner,
    })
    readonly supplier: Reference<sageX3MasterData.nodes.BusinessPartner>;

    @decorators.referenceProperty<SupplierProductSite, 'stockSite'>({
        isPublished: true,
        isStored: true,
        columnName: 'STOFCY',
        columnType: 'string',
        node: () => sageX3System.nodes.Site,
    })
    readonly stockSite: Reference<sageX3System.nodes.Site>;

    @decorators.integerProperty<SupplierProductSite, 'priority'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'PIO',
    })
    readonly priority: Promise<integer | null>;

    @decorators.stringProperty<SupplierProductSite, 'supplierProduct'>({
        isPublished: true,
        isStored: true,
        columnName: 'ITMREFBPS',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly supplierProduct: Promise<string>;

    @decorators.stringProperty<SupplierProductSite, 'supplierProductDescription'>({
        isPublished: true,
        isStored: true,
        columnName: 'ITMDESBPS',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly supplierProductDescription: Promise<string>;

    @decorators.stringProperty<SupplierProductSite, 'supplierUpc'>({
        isPublished: true,
        isStored: true,
        columnName: 'EANCODBPS',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly supplierUpc: Promise<string>;

    @decorators.referenceProperty<SupplierProductSite, 'purchaseUnit'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'PUU',
        columnType: 'string',
        node: () => sageX3MasterData.nodes.UnitOfMeasure,
    })
    readonly purchaseUnit: Reference<sageX3MasterData.nodes.UnitOfMeasure | null>;

    @decorators.decimalProperty<SupplierProductSite, 'purchaseUnitToStockUnitConversionFactor'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'PUUSTUCOE',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.decimalDatatype,
    })
    readonly purchaseUnitToStockUnitConversionFactor: Promise<decimal | null>;

    @decorators.referenceProperty<SupplierProductSite, 'packingUnit'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'PCU',
        columnType: 'string',
        node: () => sageX3MasterData.nodes.UnitOfMeasure,
    })
    readonly packingUnit: Reference<sageX3MasterData.nodes.UnitOfMeasure | null>;

    @decorators.decimalProperty<SupplierProductSite, 'packingUnitToPurchaseUnitConversionFactor'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'PCUPUUCOE',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.decimalDatatype,
    })
    readonly packingUnitToPurchaseUnitConversionFactor: Promise<decimal | null>;

    @decorators.decimalProperty<SupplierProductSite, 'intrastatIncreaseCoefficient'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'EECINCRAT',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.decimalDatatype,
        serviceOptions: () => [sageX3System.serviceOptions.DebActivityCode],
    })
    readonly intrastatIncreaseCoefficient: Promise<decimal | null>;

    @decorators.decimalProperty<SupplierProductSite, 'minimumPurchaseQuantity'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'PURMINQTY',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.decimalDatatype,
    })
    readonly minimumPurchaseQuantity: Promise<decimal | null>;

    @decorators.integerProperty<SupplierProductSite, 'qualityRank'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'QLYMRK',
    })
    readonly qualityRank: Promise<integer | null>;

    @decorators.enumProperty<SupplierProductSite, 'qualityControlManagementMode'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'QUAFLG',
        dataType: () => sageX3MasterData.enums.subjectToControlDatatype,
    })
    readonly qualityControlManagementMode: Promise<sageX3MasterData.enums.SubjectToControl | null>;

    @decorators.booleanProperty<SupplierProductSite, 'isBackToBackOrder'>({
        isPublished: true,
        isStored: true,
        columnName: 'CTMBPSFLG',
    })
    readonly isBackToBackOrder: Promise<boolean>;

    @decorators.stringProperty<SupplierProductSite, 'purchasingTextKey'>({
        isStored: true,
        columnName: 'TEX',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly purchasingTextKey: Promise<string>;

    @decorators.referenceProperty<SupplierProductSite, 'subcontractBom'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'BOMALT',
        columnType: 'integer',
        filters: {
            lookup: {
                bomType: 'salesKit',
            },
            control: {
                bomType: 'salesKit',
            },
        },
        node: () => sageX3MasterData.nodes.BomCodes,
    })
    readonly subcontractBom: Reference<sageX3MasterData.nodes.BomCodes | null>;

    @decorators.integerProperty<SupplierProductSite, 'subcontractLeadTime'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'SCOLTI',
    })
    readonly subcontractLeadTime: Promise<integer | null>;

    @decorators.referenceProperty<SupplierProductSite, 'technicalSheet'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'QLYCRD',
        columnType: 'string',
        filters: {
            lookup: {
                lineNumber: 1,
            },
            control: {
                lineNumber: 1,
            },
        },
        node: () => sageX3MasterData.nodes.TechnicalSheet,
    })
    readonly technicalSheet: Reference<sageX3MasterData.nodes.TechnicalSheet | null>;

    @decorators.integerProperty<SupplierProductSite, 'qualityControlFrequency'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'QUAFRY',
    })
    readonly qualityControlFrequency: Promise<integer | null>;

    @decorators.integerProperty<SupplierProductSite, 'numberOfEntriesSinceLastQualityControl'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'QUANUM',
    })
    readonly numberOfEntriesSinceLastQualityControl: Promise<integer | null>;

    @decorators.integerProperty<SupplierProductSite, 'qualityControlFrequencyToReview'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'QUAADXUID',
    })
    readonly qualityControlFrequencyToReview: Promise<integer | null>;

    @decorators.integerProperty<SupplierProductSite, 'qualityEntriesProcess'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'QUANUMUID',
    })
    readonly qualityEntriesProcess: Promise<integer | null>;

    @decorators.booleanProperty<SupplierProductSite, 'isVersionPreloaded'>({
        isPublished: true,
        isStored: true,
        columnName: 'LOAECCFLG',
        serviceOptions: () => [sageX3System.serviceOptions.EccActivityCode],
    })
    readonly isVersionPreloaded: Promise<boolean>;

    @decorators.booleanProperty<SupplierProductSite, 'isDefaultSupplier'>({
        isPublished: true,
        isStored: true,
        columnName: 'DEFBPSFLG',
    })
    readonly isDefaultSupplier: Promise<boolean>;

    @decorators.booleanProperty<SupplierProductSite, 'isDefaultBackToBackSupplier'>({
        isPublished: true,
        isStored: true,
        columnName: 'DEFCTMBPSFLG',
    })
    readonly isDefaultBackToBackSupplier: Promise<boolean>;

    @decorators.referenceProperty<SupplierProductSite, 'purchasingTextRef'>({
        isStored: true,
        isNullable: true,
        columnType: 'string',
        node: () => sageX3MasterData.nodes.CommonText,
    })
    readonly purchasingTextRef: Reference<sageX3MasterData.nodes.CommonText | null>;

    @decorators.textStreamProperty<SupplierProductSite, 'purchasingText'>({
        isPublished: true,
        isStored: true,
        columnName: 'TEXTE',
    })
    readonly purchasingText: Promise<TextStream>;
}
