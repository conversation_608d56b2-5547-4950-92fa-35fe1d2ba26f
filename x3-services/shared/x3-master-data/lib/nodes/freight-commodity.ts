import { X3StorageManager, Joins } from '@sage/xtrem-x3-gateway';
import * as sageXtremX3SystemUtils from '@sage/xtrem-x3-system-utils';
import * as sageX3MasterData from '..';
import { decorators, Node } from '@sage/xtrem-core';

const joins: Joins<FreightCommodity> = {
    localizedStrings: {
        localizedDescription: {
            tableName() {
                return 'FRTCOMCOD';
            },
            columnName() {
                return 'DESAXX';
            },
            key1: ['code'],
            key2: ['type'],
        },
        localizedShortDescription: {
            tableName() {
                return 'FRTCOMCOD';
            },
            columnName() {
                return 'SHOAXX';
            },
            key1: ['code'],
            key2: ['type'],
        },
    },
};

@decorators.node<FreightCommodity>({
    storage: 'external',
    tableName: 'FRTCOMCOD',
    keyPropertyNames: ['type', 'code'],
    indexes: [
        {
            orderBy: {
                type: 1,
                code: 1,
            },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    externalStorageManager: new X3StorageManager({
        joins,
        joinFallbackProperties: ['type'],
    }),
    isPublished: true,
    canRead: true,
    canSearch: true,
})
export class FreightCommodity extends Node {
    @decorators.enumProperty<FreightCommodity, 'type'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        columnName: 'COMTYP',
        dataType: () => sageX3MasterData.enums.freightCommodityEnumDatatype,
    })
    readonly type: Promise<sageX3MasterData.enums.FreightCommodityEnum | null>;

    @decorators.stringProperty<FreightCommodity, 'code'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        columnName: 'COMCOD',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.textDatatype,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<FreightCommodity, 'localizedDescription'>({
        isPublished: true,
        isStored: true,
        columnName: 'DESAXX',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.translatableTextDatatype,
    })
    readonly localizedDescription: Promise<string>;

    @decorators.stringProperty<FreightCommodity, 'localizedShortDescription'>({
        isPublished: true,
        isStored: true,
        columnName: 'SHOAXX',
        dataType: () => sageXtremX3SystemUtils.datatypes.genericDataTypes.translatableTextDatatype,
    })
    readonly localizedShortDescription: Promise<string>;

    @decorators.booleanProperty<FreightCommodity, 'isActive'>({
        isPublished: true,
        isStored: true,
        columnName: 'ENAFLG',
    })
    readonly isActive: Promise<boolean>;
}
