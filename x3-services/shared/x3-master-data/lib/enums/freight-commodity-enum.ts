import { X3EnumDataType } from '@sage/xtrem-x3-gateway';

export enum FreightCommodityEnumEnum {
    nmfcNumber = 1,
    hsCode = 2,
}

export interface FreightCommodityEnum$EnumInterface {
    nmfcNumber: 1;
    hsCode: 2;
}

export type FreightCommodityEnum = keyof FreightCommodityEnum$EnumInterface;

export const freightCommodityEnumDatatype = new X3EnumDataType<FreightCommodityEnum>({
    enum: FreightCommodityEnumEnum,
    filename: __filename,
    localMenuNumber: 2084,
});
