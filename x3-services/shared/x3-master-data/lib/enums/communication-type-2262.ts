import { X3EnumDataType } from '@sage/xtrem-x3-gateway';

export enum CommunicationType2262Enum {
    notManaged = 1,
    managedOnDemand = 2,
    managedAutomatically = 3,
}

export interface CommunicationType2262$EnumInterface {
    notManaged: 1;
    managedOnDemand: 2;
    managedAutomatically: 3;
}

export type CommunicationType2262 = keyof CommunicationType2262$EnumInterface;

export const communicationType2262Datatype = new X3EnumDataType<CommunicationType2262>({
    enum: CommunicationType2262Enum,
    filename: __filename,
    localMenuNumber: 2262,
});
