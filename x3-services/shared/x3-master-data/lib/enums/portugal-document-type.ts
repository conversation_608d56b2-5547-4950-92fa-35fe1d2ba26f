import { X3EnumDataType } from '@sage/xtrem-x3-gateway';

export enum PortugalDocumentTypeEnum {
    allTypes = 1,
    deliveries = 2,
    customerReturns = 3,
    loanReturns = 4,
    subContMaterialReturns = 5,
    interSiteTransfers = 6,
    subContractTransfers = 7,
    subContractReturns = 8,
    purchaseReturns = 9,
    transportNote = 10,
    orders = 11,
    quotes = 12,
    proforma = 13,
}

export interface PortugalDocumentType$EnumInterface {
    allTypes: 1;
    deliveries: 2;
    customerReturns: 3;
    loanReturns: 4;
    subContMaterialReturns: 5;
    interSiteTransfers: 6;
    subContractTransfers: 7;
    subContractReturns: 8;
    purchaseReturns: 9;
    transportNote: 10;
    orders: 11;
    quotes: 12;
    proforma: 13;
}

export type PortugalDocumentType = keyof PortugalDocumentType$EnumInterface;

export const portugalDocumentTypeDatatype = new X3EnumDataType<PortugalDocumentType>({
    enum: PortugalDocumentTypeEnum,
    filename: __filename,
    localMenuNumber: 2047,
});
