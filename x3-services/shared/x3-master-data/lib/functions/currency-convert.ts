import * as sageX3System from '@sage/x3-system';
import { Context, DateValue, decimal, Decimal, toDecimal } from '@sage/xtrem-core';
import {} from '@sage/xtrem-decimal';
import * as sageX3SystemUtils from '@sage/xtrem-x3-system-utils';
import * as sageX3MasterData from '..';

async function getRateNode(
    context: Context,
    fromCurrency: string,
    toCurrency: string,
    rateType?: sageX3System.enums.ExchangeRateType,
    rateDate?: DateValue,
    throwError = true,
): Promise<sageX3MasterData.nodes.CurrencyRate | undefined> {
    const rates = await context
        .query(sageX3MasterData.nodes.CurrencyRate, {
            filter: {
                rateType,
                sourceCurrency: fromCurrency,
                destinationCurrency: toCurrency,
                rateDate: { _lte: rateDate },
            },
            last: 1,
        })
        .toArray();

    if (rates.length > 0) return rates[0];

    if (throwError) {
        throw new Error(`No rate found for ${fromCurrency} to ${toCurrency} on ${rateDate}`);
    }
    return undefined;
}

export async function currencyConversion(
    context: Context,
    fromCurrency: string,
    toCurrency: string,
    inputAmounts: decimal[],
    options?: {
        rounding?: number;
        companyCurrency?: string;
        rateType?: sageX3System.enums.ExchangeRateType;
        rateDate?: DateValue;
        fallbackToEuro?: boolean;
    },
): Promise<Decimal[]> {
    if (inputAmounts.length === 0) {
        return [];
    }

    const { rounding, companyCurrency, rateType, rateDate, fallbackToEuro } = options ?? {};

    if (fromCurrency === toCurrency) {
        const currency = await context.tryRead(sageX3MasterData.nodes.Currency, { code: toCurrency });
        const currencyRounding = (await currency?.rounding) ?? 0;
        // Force the rounding to input rounding or fallback to currency rounding
        return inputAmounts.map(amount => toDecimal(amount).toDecimalPlaces(rounding ?? currencyRounding));
    }

    const euro =
        (await sageX3SystemUtils.nodes.SysGeneralParameter.readParameterValueString(context, {
            parameterCode: 'EURO',
            level: 'folder',
        })) || 'EUR'; // CTX_EURO

    // CTX_EURODEV = rounding

    const globalRoundingPlaces =
        (await sageX3SystemUtils.nodes.SysGeneralParameter.readParameterValueInteger(context, {
            parameterCode: 'DECRCU',
            level: 'folder',
        })) || 2; // CTX_DECRCU

    const hiDate = DateValue.make(9999, 12, 31); // CTX_HIDATE
    const euroDecimalPlaces =
        (await sageX3SystemUtils.nodes.SysGeneralParameter.readParameterValueInteger(context, {
            parameterCode: 'DECRCU',
            level: 'folder',
        })) || 2; // CTX_AROEURO

    const rateDateValue = rateDate ?? hiDate;
    const fromCurrencyNode = await context.read(sageX3MasterData.nodes.Currency, { code: fromCurrency });

    const toCurrencyNode = await context.read(sageX3MasterData.nodes.Currency, { code: toCurrency });

    const isEuroCurrency = async (currencyNode: sageX3MasterData.nodes.Currency) => {
        const targetDate = await currencyNode.euroConversionDate;
        return (await currencyNode.isEuro) && (targetDate == null || targetDate.compare(rateDateValue) <= 0);
    };

    if (fromCurrency === euro) {
        const currencyRounding = await toCurrencyNode?.rounding;
        const sum = toDecimal(0);
        const euroRate = toDecimal((await toCurrencyNode.euroRate) ?? 0);
        const decimalPlaces = rounding ?? currencyRounding ?? 0;
        if (await isEuroCurrency(toCurrencyNode)) {
            return inputAmounts.map(amount => {
                const calc = toDecimal(amount)
                    .add(sum)
                    .div(euroRate)
                    .toDecimalPlaces(decimalPlaces)
                    .sub(sum.div(euroRate).toDecimalPlaces(decimalPlaces));
                sum.add(amount);
                return calc;
            });
        }
        // Euro to Currency "out"
        const rateNode = (await getRateNode(
            context,
            euro,
            toCurrency,
            rateType,
            rateDateValue,
            true,
        )) as sageX3MasterData.nodes.CurrencyRate;

        const rate = toDecimal((await rateNode.rate) ?? 0);
        const divisor = toDecimal((await rateNode.divisor) ?? 1);

        return inputAmounts.map(amount => {
            const x = rate.div(divisor).toDecimalPlaces(globalRoundingPlaces);
            const calc = toDecimal(amount)
                .add(sum)
                .mul(x)
                .toDecimalPlaces(decimalPlaces)
                .sub(sum.mul(x).toDecimalPlaces(decimalPlaces));
            sum.add(amount);
            return calc;
        });
    }
    if (toCurrency === euro) {
        const currencyRounding = await toCurrencyNode?.rounding;
        const sum = toDecimal(0);
        const euroRate = toDecimal((await fromCurrencyNode.euroRate) ?? 1);
        const decimalPlaces = rounding ?? currencyRounding ?? 0;
        if (await isEuroCurrency(fromCurrencyNode)) {
            return inputAmounts.map(amount => {
                const calc = toDecimal(amount)
                    .add(sum)
                    .div(euroRate)
                    .toDecimalPlaces(decimalPlaces)
                    .sub(sum.div(euroRate).toDecimalPlaces(decimalPlaces));
                sum.add(amount);
                return calc;
            });
        }
        // Currency "out" to Euro
        const rateNode = (await getRateNode(
            context,
            fromCurrency,
            toCurrency,
            rateType,
            rateDateValue,
            true,
        )) as sageX3MasterData.nodes.CurrencyRate;

        const rate = toDecimal((await rateNode.rate) ?? 0);
        const divisor = toDecimal((await rateNode.divisor) ?? 1);

        return inputAmounts.map(amount => {
            // inverted rate
            const x = divisor.div(rate).toDecimalPlaces(globalRoundingPlaces);
            const calc = toDecimal(amount)
                .add(sum)
                .mul(x)
                .toDecimalPlaces(decimalPlaces)
                .sub(sum.mul(x).toDecimalPlaces(decimalPlaces));
            sum.add(amount);
            return calc;
        });
    }

    if ((await isEuroCurrency(fromCurrencyNode)) && (await isEuroCurrency(toCurrencyNode))) {
        const sum = toDecimal(0);
        const euroRate1 = toDecimal((await fromCurrencyNode.euroRate) ?? 0);
        const rounding1 = rounding ?? (await fromCurrencyNode.rounding) ?? 0;
        const rounding2 = rounding ?? (await toCurrencyNode.rounding) ?? 0;
        const euroRate2 = toDecimal((await toCurrencyNode.euroRate) ?? 0);
        return inputAmounts.map(amount => {
            const amtEuro = toDecimal(amount).add(sum).div(euroRate1).toDecimalPlaces(rounding1);
            const amtSum = sum.div(euroRate1).toDecimalPlaces(rounding1);

            const calc = amtEuro
                .mul(toDecimal(euroRate2 ?? 0))
                .toDecimalPlaces(rounding2)
                .sub(amtSum.mul(toDecimal(euroRate2 ?? 0)).toDecimalPlaces(rounding2));
            sum.add(amount);
            return calc;
        });
    }

    if (await isEuroCurrency(fromCurrencyNode)) {
        const rateNode = (await getRateNode(
            context,
            euro,
            fromCurrency,
            rateType,
            rateDateValue,
            true,
        )) as sageX3MasterData.nodes.CurrencyRate;

        const rate = toDecimal((await rateNode.rate) ?? 0);
        const divisor = toDecimal((await rateNode.divisor) ?? 1);
        const x = rate.div(divisor).toDecimalPlaces(globalRoundingPlaces);

        const sum = toDecimal(0);
        const euroRate1 = toDecimal((await fromCurrencyNode.euroRate) ?? 0);
        const rounding1 = rounding ?? euroDecimalPlaces ?? 0;
        const rounding2 = rounding ?? (await toCurrencyNode.rounding) ?? 0;
        return inputAmounts.map(amount => {
            const amtEuro = toDecimal(amount).add(sum).div(euroRate1).toDecimalPlaces(rounding1);
            const amtSum = sum.div(euroRate1).toDecimalPlaces(rounding1);

            const calc = amtEuro.mul(x).toDecimalPlaces(rounding2).sub(amtSum.mul(x).toDecimalPlaces(rounding2));
            sum.add(amount);
            return calc;
        });
    }

    if (await isEuroCurrency(toCurrencyNode)) {
        const rateNode = (await getRateNode(
            context,
            euro,
            fromCurrency,
            rateType,
            rateDateValue,
            true,
        )) as sageX3MasterData.nodes.CurrencyRate;

        const rate = toDecimal((await rateNode.rate) ?? 0);
        const divisor = toDecimal((await rateNode.divisor) ?? 1);
        // inverted rate
        const x = divisor.div(rate).toDecimalPlaces(euroDecimalPlaces);

        const sum = toDecimal(0);
        const euroRate1 = toDecimal((await fromCurrencyNode.euroRate) ?? 0);
        const euroRate2 = toDecimal((await toCurrencyNode.euroRate) ?? 0);

        const rounding1 = rounding ?? euroDecimalPlaces ?? 0;
        const rounding2 = rounding ?? (await toCurrencyNode.rounding) ?? 0;
        return inputAmounts.map(amount => {
            const amtEuro = toDecimal(amount).add(sum).mul(x).toDecimalPlaces(rounding1);
            const amtSum = sum.div(euroRate1).toDecimalPlaces(rounding1);

            const calc = amtEuro
                .mul(euroRate2)
                .toDecimalPlaces(rounding2)
                .sub(amtSum.mul(euroRate2).toDecimalPlaces(rounding2));
            sum.add(amount);
            return calc;
        });
    }

    const rateNode = await getRateNode(context, fromCurrency, toCurrency, rateType, rateDateValue, false);

    if (rateNode == null) {
        if (fallbackToEuro) {
            return currencyConversion(context, fromCurrency, euro, inputAmounts, {
                companyCurrency,
                rateType,
                rateDate: rateDateValue,
                rounding,
                fallbackToEuro: false,
            });
        }
        if (companyCurrency) {
            const companyRate1 = await getRateNode(
                context,
                companyCurrency,
                fromCurrency,
                rateType,
                rateDateValue,
                false,
            );

            if (companyRate1) {
                const rate1 = toDecimal((await companyRate1.rate) ?? 0);
                const divisor1 = toDecimal((await companyRate1.divisor) ?? 1);
                const x1 = divisor1.div(rate1).toDecimalPlaces(globalRoundingPlaces);

                const companyRate2 = await getRateNode(
                    context,
                    companyCurrency,
                    toCurrency,
                    rateType,
                    rateDateValue,
                    false,
                );
                if (companyRate2) {
                    const rate2 = toDecimal((await companyRate2.rate) ?? 0);
                    const divisor2 = toDecimal((await companyRate2.divisor) ?? 1);
                    const x2 = rate2.div(divisor2).toDecimalPlaces(globalRoundingPlaces);
                    const rounding1 = rounding ?? euroDecimalPlaces ?? 0;
                    const rounding2 = rounding ?? globalRoundingPlaces ?? 0;
                    const sum = toDecimal(0);

                    return inputAmounts.map(amount => {
                        const amt1 = toDecimal(amount)
                            .add(sum)
                            .mul(x1)
                            .toDecimalPlaces(rounding ?? globalRoundingPlaces);
                        const amtSum = sum.mul(x1).toDecimalPlaces(rounding1);

                        const calc = amt1
                            .mul(x2)
                            .toDecimalPlaces(rounding2)
                            .sub(amtSum.mul(x2).toDecimalPlaces(rounding2));
                        sum.add(amount);
                        return calc;
                    });
                }
            }
        }
        throw new Error(`No rate found for ${fromCurrency} to ${toCurrency} on ${rateDateValue}`);
    }

    const rate = toDecimal((await rateNode.rate) ?? 0);
    const divisor = toDecimal((await rateNode.divisor) ?? 1);
    const x = divisor.div(rate).toDecimalPlaces(globalRoundingPlaces);

    const sum = toDecimal(0);

    const rounding1 = rounding ?? globalRoundingPlaces ?? 0;

    return inputAmounts.map(amount => {
        const amtEuro = toDecimal(amount).add(sum).mul(x).toDecimalPlaces(rounding1);
        const amtSum = sum.mul(x).toDecimalPlaces(rounding1);

        const calc = amtEuro.sub(amtSum);
        sum.add(amount);
        return calc;
    });
}
