import { load } from 'js-yaml';
import fs from 'node:fs';
import * as fsp from 'node:path';
import _ from 'lodash';
import * as prettier from 'prettier';
import json5 from 'json5';

function readPnpmLock(path) {
    return load(fs.readFileSync(path, 'utf8'));
}

function readJson5(path) {
    const content = fs.readFileSync(path, 'utf8');
    try {
        return json5.parse(content);
    } catch (error) {
        throw new Error(`Error parsing JSON5 file at ${path}: ${error.message}`);
    }
}

function findDependency(importers, name) {
    const importerKeys = Object.keys(importers).filter(key => key !== '.');
    const importerKey = importerKeys.find(key => {
        const importer = importers[key];
        return !!(importer.dependencies?.[name] ?? importer.devDependencies?.[name]);
    });
    if (!importerKey)
        throw new Error(
            `dependency ${name} not found in pnpm-lock.yaml, add it to relevant package.json or remove it from dependency list`,
        );
    return importers[importerKey].dependencies[name] ?? importers[importerKey].devDependencies[name];
}

async function findDependencyVersionsFromLock() {
    const pnpmLock = readPnpmLock(fsp.join(import.meta.dirname, '../../../../pnpm-lock.yaml'));
    const xdevConfigFile = fsp.join(import.meta.dirname, '../../../../xdev.json5');
    const xdevConfig = readJson5(xdevConfigFile);
    const dependencyList = xdevConfig.releng?.pack?.devDependencyNames;
    if (!dependencyList) {
        throw new Error(`releng.pack.devDependencyNames not found in ${xdevConfigFile}`);
    }
    const importers = pnpmLock.importers || {};
    return dependencyList.reduce((r, k) => {
        const dependency = findDependency(importers, k);
        if (dependency) {
            r[k] = dependency.specifier;
        }
        return r;
    }, {});
}

async function main() {
    const dependencies = await findDependencyVersionsFromLock();
    const dependenciesPath = fsp.join(import.meta.dirname, '../lib/templates', 'dependencies.ts');
    const PREFIX = 'export const dependencies = ';
    const POSTFIX = ';';
    if (fs.existsSync(dependenciesPath)) {
        const currentDependenciesContent = fs.readFileSync(dependenciesPath, 'utf8');
        const currentDependenciesString = _.chain(currentDependenciesContent)
            .trimStart(PREFIX)
            .trimEnd(POSTFIX)
            .value();
        let currentDependencies = {};
        try {
            currentDependencies = JSON.parse(currentDependenciesString);
        } catch (error) {
            /* empty */
        }
        if (_.isEqual(currentDependencies, dependencies)) {
            return;
        }
    }
    const formatted = await prettier.resolveConfig(dependenciesPath).then(options => {
        return prettier.format(`${PREFIX}${JSON.stringify(dependencies, null, 2)}${POSTFIX}`, {
            ...options,
            filepath: dependenciesPath,
        });
    });
    fs.writeFileSync(dependenciesPath, formatted);
}

await main();
