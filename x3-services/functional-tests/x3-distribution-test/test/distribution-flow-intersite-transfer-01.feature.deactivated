@distribution
Feature: distribution-flow-intersite-transfer-01
    ###########################################################################
    # Header
    # -------------------------------------------------------------------------
    # - Test code : distribution-flow-intersite-transfer-01
    # - Description : Intersite transfer by changing Destination status and  Destination unit
    # - Legislation : All legislations
    # - JIRA ID : X3-299015
    # - Created by : <PERSON><PERSON>
    # - Created date - 05-06-2023
    # - Updated by :
    # - Updated date :
    ###########################################################################

    Scenario: 1 Select the endpoint

        Given the user opens the application on a mobile
        When the user selects the "param:endPointName1" endpoint
        Then the "param:endPointName1" endpoint is selected

    Scenario: 2 Set the site I-K24
        Given the user opens the application on a mobile
        When the user clicks the "Site" sticker in the navigation bar
        And the user selects the "Site" labelled reference field on a full width modal
        And the user clicks the lookup button of the reference field
        And searches for "I-K24" in the lookup dialog
        And the user selects the "selectionSite" bound reference field on a full width modal
        Then the value of the reference field is "I-K24"
        And the user clicks the "Set site" labelled business action button on a full width modal

    Scenario: 3 AdcIntersiteTransfer

        Given the user opens the application on a mobile using the following link: "@sage/x3-stock/MobileIntersiteTransfer"
        And the user waits 2 seconds
        And the user selects the "siteDestination" bound reference field on the main page
        And the user clicks the lookup button of the reference field
        And searches for "I-K25" in the lookup dialog
        And the user selects the "product" bound reference field on the main page
        And the user writes "I-K020623-A" in the reference field

    Scenario: 4 Verification of stock lines without filter

        When the "Intersite" titled page is displayed
        And the "Enter stock details" subtitled page is displayed
        And the value of the "localizedDescription" bound nested text field in the header card is "I-K020623-A"

        And the user selects the "stock" bound table field on the main page
        Then the value of the "quantityToMove" bound nested numeric field of the card 1 in the table field is "10 UN"
        # And the value of the "product" bound nested reference field of the card 1 in the table field is "I-K020623-A"
        And the value of the "location" bound nested reference field of the card 1 in the table field is "AAAAAA"
        And the value of the "identifier1" bound nested text field of the card 1 in the table field is "A"
        And the value of the "identifier2" bound nested text field of the card 1 in the table field is "B"

        And the value of the "quantityToMove" bound nested numeric field of the card 2 in the table field is "20 UN"
        # And the value of the "product" bound nested reference field of the card 2 in the table field is "I-K020623-A"
        And the value of the "location" bound nested reference field of the card 2 in the table field is "AAAAAB"
        And the value of the "identifier1" bound nested text field of the card 2 in the table field is "C"
        And the value of the "identifier2" bound nested text field of the card 2 in the table field is "D"

        And the value of the "quantityToMove" bound nested numeric field of the card 3 in the table field is "30 UN"
        # And the value of the "product" bound nested reference field of the card 3 in the table field is "I-K020623-A"
        And the value of the "location" bound nested reference field of the card 3 in the table field is "AAAAAC"
        And the value of the "identifier1" bound nested text field of the card 3 in the table field is "E"
        And the value of the "identifier2" bound nested text field of the card 3 in the table field is "F"

    Scenario: 5 Verification of stock lines with stock filters 1 = Idenfier1 and stock filters 2 = Idenfier2

        And the user selects the "identifier1" bound text field on the main page
        And the user writes "A" in the text field
        And the user selects the "identifier2" bound text field on the main page
        And the user writes "B" in the text field
        And the user presses Tab
        And the user selects the "stock" bound table field on the main page
        Then the value of the "quantityToMove" bound nested numeric field of the card 1 in the table field is "10 UN"
        # And the value of the "product" bound nested reference field of the card 1 in the table field is "I-K020623-A"
        And the value of the "location" bound nested reference field of the card 1 in the table field is "AAAAAA"
        And the value of the "identifier1" bound nested text field of the card 1 in the table field is "A"
        And the value of the "identifier2" bound nested text field of the card 1 in the table field is "B"

    Scenario: 6 Verification of stock lines with stock filters 1 = Idenfier3 and stock filters 2 = Idenfier4

        And the user selects the "identifier1" bound text field on the main page
        And the user writes "C" in the text field
        And the user selects the "identifier2" bound text field on the main page
        And the user writes "D" in the text field
        And the user presses Tab
        And the user selects the "stock" bound table field on the main page
        Then the value of the "quantityToMove" bound nested numeric field of the card 1 in the table field is "20 UN"
        # And the value of the "product" bound nested reference field of the card 1 in the table field is "I-K020623-A"
        And the value of the "location" bound nested reference field of the card 1 in the table field is "AAAAAB"
        And the value of the "identifier1" bound nested text field of the card 1 in the table field is "C"
        And the value of the "identifier2" bound nested text field of the card 1 in the table field is "D"

    Scenario: 7 Verification of stock lines with stock filters 1 = Idenfier5 and stock filters 2 = Idenfier6

        And the user selects the "identifier1" bound text field on the main page
        And the user writes "E" in the text field
        And the user selects the "identifier2" bound text field on the main page
        And the user writes "F" in the text field
        And the user presses Tab
        And the user selects the "stock" bound table field on the main page
        Then the value of the "quantityToMove" bound nested numeric field of the card 1 in the table field is "30 UN"
        # And the value of the "product" bound nested reference field of the card 1 in the table field is "I-K020623-A"
        And the value of the "location" bound nested reference field of the card 1 in the table field is "AAAAAC"
        And the value of the "identifier1" bound nested text field of the card 1 in the table field is "E"
        And the value of the "identifier2" bound nested text field of the card 1 in the table field is "F"

    Scenario: 8 Check that an alert is triggered if the quantity entered is greater than the quantity in stock.

        And the user selects the "identifier1" bound text field on the main page
        And the user writes "A" in the text field
        And the user selects the "identifier2" bound text field on the main page
        And the user writes "B" in the text field
        And the user presses Tab
        And the user selects the "stock" bound table field on the main page
        When the user clicks the card 1 in the table field
        And the user waits 1 seconds

        And the value of the "product" bound nested reference field of the row 1 in the detail list is "I-K020623-A"
        And the value of the "location" bound nested reference field of the row 2 in the detail list is "AAAAAA"

        And the value of the "quantityInPackingUnit" bound nested numeric field of the row 3 in the detail list is "10 UN"
        #   And the value of the "packingUnitToStockUnitConversionFactor" bound nested numeric field of the row 4 in the detail list is "1"
        And the value of the "quantityInStockUnit" bound nested numeric field of the row 5 in the detail list is "10 UN"
        And the value of the "allocatedQuantity" bound nested numeric field of the row 6 in the detail list is "0 UN"
        And the value of the "status" bound nested reference field of the row 7 in the detail list is "A"
        And the value of the "identifier1" bound nested text field of the row 8 in the detail list is "A"
        And the value of the "identifier2" bound nested text field of the row 9 in the detail list is "B"

        # write quantity to move
        And the user selects the "quantityToMove" bound numeric field on the main page
        And the user clicks in the numeric field
        And the user scrolls to the numeric field
        And the user writes "40" in the numeric field
        When the user selects the "locationDestination" bound reference field on the main page
        And the user clicks the lookup button of the reference field
        And searches for "AAAAAD" in the lookup dialog
        And the user clicks the "helperSelectButton" bound business action button on the main page
        And the user waits 1 seconds
        Then the text in the body of the dialog is "Enter a quantity less than or equal to the stock quantity minus the allocated quantity."
        And the user clicks the "OK" button of the Confirm dialog

    Scenario: 9 Check that an alert is triggered if the quantity entered = 0.

        And the user selects the "quantityToMove" bound numeric field on the main page
        And the user clicks in the numeric field
        And the user scrolls to the numeric field
        And the user writes "0" in the numeric field

        When the user selects the "locationDestination" bound reference field on the main page
        And the user clicks the lookup button of the reference field
        And searches for "AAAAAD" in the lookup dialog
        And the user clicks the "helperSelectButton" bound business action button on the main page
        And the user waits 1 seconds
        Then the text in the body of the dialog is "Enter a quantity to move greater than 0."
        And the user clicks the "OK" button of the Confirm dialog

    Scenario: 10 Intersite transfer by changing Destination status and  Destination unit

        And the user selects the "quantityToMove" bound numeric field on the main page
        And the user clicks in the numeric field
        And the user scrolls to the numeric field
        And the user writes "10" in the numeric field

        When the user selects the "statusDestination" bound select field on the main page
        And the user writes "A1" in the select field
        And the user selects "A1" in the select field
        Then the value of the select field is "A1"
        # When the user selects the "packingUnitDestination" bound select field on the main page
        # And the user writes "PAL" in the select field
        # And the user selects "PAL" in the select field
        # Then the value of the select field is "PAL"

        Given the user selects the "packingUnitDestination" bound dropdown-list field on the main page
        When the user clicks in the dropdown-list field
        And the user selects "PAL" in the dropdown-list field
        Then the value of the dropdown-list field is "PAL"

        When the user selects the "locationDestination" bound reference field on the main page
        And the user clicks the lookup button of the reference field
        And searches for "AAAAAD" in the lookup dialog

        And the user clicks the "helperSelectButton" bound business action button on the main page
        And the user waits 1 seconds
        And the user clicks the "nextButton" bound business action button on the main page

    Scenario: 11 Last check before creation

        # And the user selects the "effectiveDate" bound date field on the main page
        # And the user writes a generated date in the date field with value "M/T/Y"
        And the user selects the "siteDestination" bound reference field on the main page
        And the value of the reference field is "I-K25"

        And the "Products: 1 " labelled table field on the main page is displayed
        And the user selects the "intersiteTransferLines" bound table field on the main page
        Then the value of the "product" bound nested text field of the card 1 in the table field is "I-K020623-A"
        And the value of the "quantityAndStockUnit" bound nested text field of the card 1 in the table field is "10 UN"

        # And the user selects the "effectiveDate" bound date field on the main page
        # And the user writes a generated date in the date field with value "M/T/Y"

        And the user clicks the "createButton" bound business action button on the main page
        Then the text in the header of the dialog is "Success"
        And the user clicks the "OK" button of the Confirm dialog

        And the user clicks the close button in the navigation bar

    Scenario: 12 Logout

        When the user logs out of the system
