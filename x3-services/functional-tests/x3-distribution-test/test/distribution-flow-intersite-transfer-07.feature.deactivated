@distribution
Feature: distribution-flow-intersite-transfer-07
    ###########################################################################
    # Header
    # -------------------------------------------------------------------------
    # - Test code : distribution-flow-intersite-transfer-07
    # - Description : Product is under quality request, Stock line with status = Q (Quality control)
    # - Legislation : All legislations
    # - JIRA ID : X3-299568
    # - Created by : <PERSON><PERSON>
    # - Created date - 12-07-2023
    # - Updated by : <PERSON><PERSON>
    # - Updated date : 21-11-2023
    ###########################################################################

    Scenario: 1 Select the endpoint

        Given the user opens the application on a mobile
        When the user selects the "param:endPointName1" endpoint
        Then the "param:endPointName1" endpoint is selected

    Scenario: 2 Set the site I-K37
        Given the user opens the application on a mobile
        When the user clicks the "Site" sticker in the navigation bar
        And the user selects the "Site" labelled reference field on a full width modal
        And the user clicks the lookup button of the reference field
        And searches for "I-K37" in the lookup dialog
        And the user selects the "selectionSite" bound reference field on a full width modal
        Then the value of the reference field is "I-K37"
        And the user clicks the "Set site" labelled business action button on a full width modal

    Scenario: 3 AdcIntersiteTransfer

        Given the user opens the application on a mobile using the following link: "@sage/x3-stock/MobileIntersiteTransfer"
        And the user waits 2 seconds
        And  the user selects the "transaction" bound dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user writes "STD" in the dropdown-list field
        And the user selects "STD" in the dropdown-list field
        Then the value of the dropdown-list field is "STD"

        And the user selects the "siteDestination" bound reference field on the main page
        And the user clicks the lookup button of the reference field
        And searches for "I-K38" in the lookup dialog

        And the user selects the "product" bound reference field on the main page
        And the user writes "I-K100723" in the reference field

    Scenario: 4 Check that the information on the cards is correct


        When the "Intersite" titled page is displayed
        And the "Enter stock details" subtitled page is displayed
        And the value of the "product" bound nested reference field in the header card is "I-K100723"


        And the user selects the "stock" bound table field on the main page
        Then the value of the "quantityToMove" bound nested numeric field of the card 1 in the table field is "3 UN"
        # And the value of the "product" bound nested reference field of the card 1 in the table field is "I-K100723"
        And the value of the "location" bound nested reference field of the card 1 in the table field is "AAAAAA"

        And the user selects the "stock" bound table field on the main page
        When the user clicks the card 1 in the table field

    Scenario: 5 Card number 1, Check that detailed list items are correct and transfet by changing status Destination (A1)


        And  the title of the item on the row "3" in the detail list is "Packing qty."
        And the value of the "quantityInPackingUnit" bound nested numeric field of the row 3 in the detail list is "3 UN"


        And  the title of the item on the row "4" in the detail list is "Conversion factor"
        And the value of the "packingUnitToStockUnitConversionFactor" bound nested numeric field of the row 4 in the detail list is "1"

        And  the title of the item on the row "5" in the detail list is "Stock qty."
        And  the value of the "quantityInStockUnit" bound nested numeric field of the row 5 in the detail list is "3 UN"

        And  the title of the item on the row "6" in the detail list is "Allocated qty."
        And  the value of the "allocatedQuantity" bound nested numeric field of the row 6 in the detail list is "0 UN"

        And  the title of the item on the row "7" in the detail list is "Status"
        And  the value of the "status" bound nested reference field of the row 7 in the detail list is "Q"

        And  the title of the item on the row "8" in the detail list is "Analysis req."
        And  the value of the "qualityAnalysisRequestId" bound nested text field of the row 8 in the detail list is "I-K372307QUA00000001"

        And the user selects the "quantityToMove" bound numeric field on the main page
        And the user scrolls to the numeric field
        And the user clicks in the numeric field
        And the user writes "3" in the numeric field

        And the user selects the "locationDestination" bound reference field on the main page
        And the user writes "AAAAAC" in the reference field

        When the user selects the "statusDestination" bound select field on the main page
        And the user writes "A1" in the select field
        And the user selects "A1" in the select field
        Then the value of the select field is "A1"

        And the user clicks the "helperSelectButton" bound business action button on the main page

        Then the text in the header of the dialog is "Error"
        Then the text in the body of the dialog is "Intersite transfer is impossible on a line in quality control."
        And the user clicks the "OK" button of the Confirm dialog
        And the user clicks the close button in the navigation bar

    Scenario: 8 Logout

        When the user logs out of the system
