@distribution
Feature: distribution-flow-intersite-transfer-04
    ###########################################################################
    # Header
    # -------------------------------------------------------------------------
    # - Test code : distribution-flow-intersite-transfer-04
    # - Description : IntersiteTransfer inventory case, unlocked stock line
    # - Legislation : All legislations
    # - JIRA ID : X3-299566
    # - Created by : <PERSON><PERSON>
    # - Created date - 05-06-23
    # - Updated by :
    # - Updated date :
    ###########################################################################

    Scenario: 1 Select the endpoint

        Given the user opens the application on a mobile
        When the user selects the "param:endPointName1" endpoint
        Then the "param:endPointName1" endpoint is selected

    Scenario: 2 Set the site I-K31
        Given the user opens the application on a mobile
        When the user clicks the "Site" sticker in the navigation bar
        And the user selects the "Site" labelled reference field on a full width modal
        And the user clicks the lookup button of the reference field
        And searches for "I-K31" in the lookup dialog
        And the user selects the "selectionSite" bound reference field on a full width modal
        Then the value of the reference field is "I-K31"
        And the user clicks the "Set site" labelled business action button on a full width modal

    Scenario: 3 AdcIntersiteTransfer

        Given the user opens the application on a mobile using the following link: "@sage/x3-stock/MobileIntersiteTransfer"
        And the user waits 2 seconds

        And  the user selects the "transaction" bound dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user writes "STD" in the dropdown-list field
        And the user selects "STD" in the dropdown-list field
        Then the value of the dropdown-list field is "STD"

        And the user selects the "siteDestination" bound reference field on the main page
        And the user clicks the lookup button of the reference field
        And searches for "I-K32" in the lookup dialog

        And the user selects the "product" bound reference field on the main page
        And the user writes "I-K040723" in the reference field

        When the "Intersite" titled page is displayed
        And the "Enter stock details" subtitled page is displayed
        And the value of the "product" bound nested reference field in the header card is "I-K040723"
        And the user selects the "stock" bound table field on the main page
        When the user clicks the card 1 in the table field

        And the user waits 1 seconds
        Then the detail panel is displayed

        # And the value of the "product" bound nested reference field of the row 1 in the detail list is "I-K040723"
        And the value of the "location" bound nested reference field of the row 2 in the detail list is "AAAAAA"
        And  the value of the "quantityInStockUnit" bound nested numeric field of the row 5 in the detail list is "3 UN"
        And the value of the "allocatedQuantity" bound nested numeric field of the row 6 in the detail list is "0 UN"
        And the value of the "status" bound nested reference field of the row 7 in the detail list is "A"
        And the value of the "packingUnitToStockUnitConversionFactor" bound nested numeric field of the row 4 in the detail list is "1"

        And the user selects the "quantityToMove" bound numeric field on the main page
        And the user scrolls to the numeric field
        And the user clicks in the numeric field
        And the user writes "3" in the numeric field

        And the user selects the "locationDestination" bound reference field on the main page
        And the user writes "AAAAAC" in the reference field
        And the user clicks the "helperSelectButton" bound business action button on the main page
        And the user waits 1 seconds
        And the user clicks the "nextButton" bound business action button on the main page

    Scenario: 4 Last check before creation

        And the user selects the "effectiveDate" bound date field on the main page
        And the user writes a generated date in the date field with value "M/T/Y"

        And  the user selects the "transaction" bound dropdown-list field on the main page
        Then the value of the dropdown-list field is "STD"

        And the user selects the "siteDestination" bound reference field on the main page
        And the value of the reference field is "I-K32"

        And the "Products: 1 " labelled table field on the main page is displayed
        And the user selects the "intersiteTransferLines" bound table field on the main page
        Then the value of the "product" bound nested text field of the card 1 in the table field is "I-K040723"
        And the value of the "quantityAndStockUnit" bound nested text field of the card 1 in the table field is "3 UN"

    # And the user selects the "effectiveDate" bound date field on the main page
    # And the user writes a generated date in the date field with value "M/T/Y"

    Scenario: 5 Creation

        And the user clicks the "createButton" bound business action button on the main page
        Then the text in the header of the dialog is "Success"
        And the user clicks the "OK" button of the Confirm dialog
        And the user clicks the close button in the navigation bar

    Scenario: 6 Logout

        When the user logs out of the system
