@distribution
Feature:  distribution-flow-mobile-purchase-receipt-qa2user-01

    ###########################################################################
    # Header
    # -------------------------------------------------------------------------
    # - Test code : distribution-flow-mobile-purchase-receipt-qa2user-01
    # - Description : I want to be able to use the qa2 user that has only the IAG10 stock site allowed on its user function profile,
    #                 so that I can control the autorization with Mobile Automation.
    #                 Functional Authorization: Function GESPTH / Grouped by Site: IAG10
    #                 Connect with  <EMAIL>
    # - Legislation : All legislations
    # - JIRA ID : X3-341973
    # - Created by : <PERSON><PERSON>UDI
    # - Created date - 08-07-2025
    # - Updated by :
    # - Updated date :
    ###########################################################################

    Scenario: 01 <NAME_EMAIL> and select the endpoint

        When the user is logged into the system in mobile mode using the "param:loginUserName2" user name and "param:loginPassword2" password
        When the user selects the "param:endPointName1" endpoint
        Then the "param:endPointName1" endpoint is selected

    Scenario: 2 Set the site
        Given the user opens the application on a mobile
        When the user clicks the "Site" sticker in the navigation bar
        And the user selects the "Site" labelled reference field on a full width modal
        And the user clicks the lookup button of the reference field
        And searches for "IAG11" in the lookup dialog
        And the user selects the "selectionSite" bound reference field on a full width modal
        Then the value of the reference field is "IAG11"
        And the user clicks the "Set site" labelled business action button on a full width modal

    Scenario: 03 @sage/x3-purchasing/MobilePurchaseReceipt

        Given the user opens the application on a mobile using the following link: "@sage/x3-purchasing/MobilePurchaseReceipt"

        And the user selects the "supplier" bound reference field on the main page
        And the user writes "A-AT10031" in the reference field
        And the user selects the "product" bound reference field on the main page
        And the user writes "I-K070323" in the reference field
        And the user selects the "Quantity" labelled numeric field on the main page
        And the user writes "1" in the numeric field
        And the user selects the "location" bound reference field on the main page
        And the user writes "ILOC01" in the reference field
        # And the value of the reference field is "ILOC01"
        And the user clicks the "addButton" bound business action button on the main page


    Scenario: 04 Confirm that the document can't be created: message You do not have the rights for this site.

        And the user clicks the "createButton" bound business action button on the main page
        And  the text in the header of the dialog is "Error"
        Then the text in the body of the dialog contains "IAG11 : You do not have the rights for this site" on the main page
        And the user clicks the close button in the navigation bar
        Then the text in the header of the dialog is "Unsaved changes"
        And the user clicks the "Discard" button of the Confirm dialog



    Scenario: 4 Logout

        When the user logs out of the system
