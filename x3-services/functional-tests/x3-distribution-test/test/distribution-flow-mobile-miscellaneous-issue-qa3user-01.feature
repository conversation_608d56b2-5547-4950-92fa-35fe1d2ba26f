@distribution
Feature: distribution-flow-mobile-miscellaneous-issue-qa3user-01

    ###########################################################################
    # Header
    # -------------------------------------------------------------------------
    # - Test code : distribution-flow-mobile-miscellaneous-issue-qa3user-01
    # - Description : Automated test- User with ADCDIS badge only + function profile different as Admin
    #                 Function : GESSMO
    #                 Connect with this ADC user, The Misc. issue will be a stock issue for a location ,
    #                 for a quantity less than the quantity of the stock unit.
    #                 No need to go into the sidebar.
    # - Legislation : All legislations
    # - JIRA ID :X3-330591
    # - Created by : <PERSON><PERSON>DI
    # - Created date - 01-07-2025
    # - Updated by :
    # - Updated date :
    ###########################################################################

    Scenario: 01 <NAME_EMAIL> and select the endpoint

        When the user is logged into the system in mobile mode using the "param:loginUserName3" user name and "param:loginPassword3" password
        When the user selects the "param:endPointName1" endpoint
        Then the "param:endPointName1" endpoint is selected

    Scenario: 02 Set the site

        Given the user opens the application on a mobile
        When the user clicks the "Site" sticker in the navigation bar
        And the user selects the "Site" labelled reference field on a full width modal
        And the user clicks the lookup button of the reference field
        And searches for "I-M13" in the lookup dialog
        And the user selects the "selectionSite" bound reference field on a full width modal
        Then the value of the reference field is "I-M13"
        And the user clicks the "Set site" labelled business action button on a full width modal

    Scenario: 03 Select transaction MobileMiscellaneousIssue, along with the product

        Given the user opens the application on a mobile using the following link: "@sage/x3-stock/MobileMiscellaneousIssue"
        And the user waits 5 seconds
        And the user selects the "productSelected" bound reference field on the main page
        And the user writes "I-K240425-C" in the reference field
        And the user waits 2 seconds
        Then the "Miscellaneous issue" titled page is displayed
        And the site in the navigation bar is "I-M13"
        And the value of the "productCode" bound nested text field in the header card is "I-K240425-C"
        Then the "Miscellaneous issue" titled page is displayed
        And the user selects the "stock" bound table field on the main page

    Scenario: 04 Make a stock issue for a quantity less than the quantity of the stock unit.

        When the user clicks the card 1 in the table field
        And the user waits 1 seconds
        And the user selects the "quantityToIssue" bound numeric field on the main page
        And the user writes "1" in the numeric field
        And the value of the numeric field is "1"
        And the user clicks the "SelectButton" bound business action button on the main page
        And the user clicks the "nextButton" bound business action button on the main page

    Scenario: 05 Create document

        And the user clicks the "createButton" bound business action button on the main page
        Then the text in the header of the dialog is "Success"
        And the user waits 2 seconds
        And takes a screenshot
        And the user clicks the "OK" button of the Confirm dialog
        And the user clicks the close button in the navigation bar

    Scenario: 06 Logout

        When the user logs out of the system
