@distribution
Feature: distribution-flow-mobile-purchase-receipt-qa3user-01

    ###########################################################################
    # Header
    # -------------------------------------------------------------------------
    # - Test code : distribution-flow-mobile-purchase-receipt-qa3user-01
    # - Description : Automated test- User with ADCDIS badge only + function profile different as Admin
    #                 Connect with this ADC user, create a Purchase receipt
    #                 Function : GESPTH
    # - Legislation : All legislations
    # - JIRA ID :X3-330591
    # - Created by : <PERSON><PERSON>LOUDI
    # - Created date - 02-07-2025
    # - Updated by :
    # - Updated date :
    ###########################################################################

    Scenario: 01 <NAME_EMAIL> and select the endpoint

        When the user is logged into the system in mobile mode using the "param:loginUserName3" user name and "param:loginPassword3" password
        When the user selects the "param:endPointName1" endpoint
        Then the "param:endPointName1" endpoint is selected

    Scenario: 02 Set the site

        Given the user opens the application on a mobile
        When the user clicks the "Site" sticker in the navigation bar
        And the user selects the "Site" labelled reference field on a full width modal
        And the user clicks the lookup button of the reference field
        And searches for "I-M12" in the lookup dialog
        And the user selects the "selectionSite" bound reference field on a full width modal
        Then the value of the reference field is "I-M12"
        And the user clicks the "Set site" labelled business action button on a full width modal

    Scenario: 03 @sage/x3-purchasing/MobilePurchaseReceipt

        Given the user opens the application on a mobile using the following link: "@sage/x3-purchasing/MobilePurchaseReceipt"
        And the user waits 2 seconds
        Then the "Purchase receipt" titled page is displayed
        And the user selects the "purchaseOrder" bound reference field on the main page
        And the user writes "I-K122504POH00000001" in the reference field
        Then the value of the reference field is "I-K122504POH00000001"
        And the user selects the "productFromPurchaseOrder" bound reference field on the main page
        And the user writes "I-K240425-B" in the reference field
        And the value of the reference field is "I-K240425-B"
        And the user selects the "quantityReceived" bound numeric field on the main page
        And  the user writes "12" in the numeric field
        And the value of the numeric field is "12"
        And the user selects the "location" bound reference field on the main page
        And the user writes "AAAAAA" in the reference field
        And the value of the reference field is "AAAAAA"
        And the user clicks the "addButton" bound business action button on the main page
        Then the "Purchase receipt" titled page is displayed

    Scenario: 04 Create.

        And the user clicks the "createButton" bound business action button on the main page
        Then the text in the header of the dialog is "Success"
        And the user waits 2 seconds
        And takes a screenshot
        And the user clicks the "OK" button of the Confirm dialog
        And the user clicks the close button in the navigation bar

    Scenario: 05 Logout

        When the user logs out of the system
