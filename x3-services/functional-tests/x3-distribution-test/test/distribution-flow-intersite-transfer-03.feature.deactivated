@distribution
Feature: distribution-flow-intersite-transfer-03
    ###########################################################################
    # Header
    # -------------------------------------------------------------------------
    # - Test code : distribution-flow-intersite-transfer-03
    # - Description : Intersite transfer Product managed by global serial number by changing Destination status and
    #                 and checking  serial numbers
    # - Legislation : All legislations
    # - JIRA ID : X3-299021
    # - Created by : <PERSON><PERSON>
    # - Created date - 20-06-2023
    # - Updated by :
    # - Updated date :
    ###########################################################################

    Scenario: 1 Select the endpoint

        Given the user opens the application on a mobile
        When the user selects the "param:endPointName1" endpoint
        Then the "param:endPointName1" endpoint is selected

    Scenario: 2 Set the site I-K28
        Given the user opens the application on a mobile
        When the user clicks the "Site" sticker in the navigation bar
        And the user selects the "Site" labelled reference field on a full width modal
        And the user clicks the lookup button of the reference field
        And searches for "I-K28" in the lookup dialog
        And the user selects the "selectionSite" bound reference field on a full width modal
        Then the value of the reference field is "I-K28"
        And the user clicks the "Set site" labelled business action button on a full width modal

    Scenario: 3 AdcIntersiteTransfer

        Given the user opens the application on a mobile using the following link: "@sage/x3-stock/MobileIntersiteTransfer"
        And the user waits 2 seconds
        And the user selects the "siteDestination" bound reference field on the main page
        And the user clicks the lookup button of the reference field
        And searches for "I-K29" in the lookup dialog
        And the user selects the "product" bound reference field on the main page
        And the user writes "I-K150623-A" in the reference field

    Scenario: 4 Verification of stock lines without filter

        When the "Intersite" titled page is displayed
        And the "Enter stock details" subtitled page is displayed
        And the value of the "product" bound nested reference field in the header card is "I-K150623-A"

        And the user selects the "stock" bound table field on the main page
        Then the value of the "quantityToMove" bound nested numeric field of the card 1 in the table field is "3 UN"
        # And the value of the "product" bound nested reference field of the card 1 in the table field is "I-K150623-A"
        And the value of the "location" bound nested reference field of the card 1 in the table field is "AAAAAA"
        And the value of the "lot" bound nested text field of the card 1 in the table field is "LO2306I-K280001"

        And the value of the "quantityToMove" bound nested numeric field of the card 2 in the table field is "4 UN"
        # And the value of the "product" bound nested reference field of the card 2 in the table field is "I-K150623-A"
        And the value of the "location" bound nested reference field of the card 2 in the table field is "AAAAAA"
        And the value of the "lot" bound nested text field of the card 2 in the table field is "LO2306I-K280002"

    Scenario: 5 Verification of stock lines with LOT filter : LO2306I-K280001


        And the user selects the "lot" bound reference field on the main page
        And the user clicks the lookup button of the reference field
        And searches for "LO2306I-K280001" in the lookup dialog
        And the user selects the "stock" bound table field on the main page
        Then the value of the "quantityToMove" bound nested numeric field of the card 1 in the table field is "3 UN"
        # And the value of the "product" bound nested reference field of the card 1 in the table field is "I-K150623-A"
        And the value of the "location" bound nested reference field of the card 1 in the table field is "AAAAAA"
        And the value of the "lot" bound nested text field of the card 1 in the table field is "LO2306I-K280001"

    Scenario: 6 Verification of stock lines with LOT filter : LO2306I-K280002


        And the user selects the "lot" bound reference field on the main page
        And the user clicks the lookup button of the reference field
        And searches for "LO2306I-K280002" in the lookup dialog
        And the user selects the "stock" bound table field on the main page
        Then the value of the "quantityToMove" bound nested numeric field of the card 1 in the table field is "4 UN"
        # And the value of the "product" bound nested reference field of the card 1 in the table field is "I-K150623-A"
        And the value of the "lot" bound nested text field of the card 1 in the table field is "LO2306I-K280002"

    Scenario: 7 Transfer total from first card of stock by changing statusDestination also check serial numbers

        And the user selects the "lot" bound reference field on the main page
        And the user clicks the lookup button of the reference field
        And searches for "LO2306I-K280001" in the lookup dialog
        And the user selects the "stock" bound table field on the main page
        When the user clicks the card 1 in the table field
        And the user waits 1 seconds

        And  the title of the item on the row "3" in the detail list is "Lot"
        And  the value of the "lot" bound nested text field of the row 3 in the detail list is "LO2306I-K280001"

        And  the title of the item on the row "4" in the detail list is "Sublot"
        And  the value of the "sublot" bound nested text field of the row 4 in the detail list is "00001"

        And  the title of the item on the row "6" in the detail list is "Packing qty."
        And the value of the "quantityInPackingUnit" bound nested numeric field of the row 6 in the detail list is "3 UN"

        And  the title of the item on the row "7" in the detail list is "Conversion factor"
        And  the value of the "packingUnitToStockUnitConversionFactor" bound nested numeric field of the row 7 in the detail list is "1"


        And  the title of the item on the row "8" in the detail list is "Stock qty."
        And  the value of the "quantityInStockUnit" bound nested numeric field of the row 8 in the detail list is "3 UN"

        And  the title of the item on the row "9" in the detail list is "Allocated qty."
        And  the value of the "allocatedQuantity" bound nested numeric field of the row 9 in the detail list is "0 UN"

        And  the title of the item on the row "10" in the detail list is "Status"
        And  the value of the "status" bound nested reference field of the row 10 in the detail list is "A"

        # Serial no.
        And the title of the item on the row "5" in the detail list is "Serial no."
        Then the value of the "globalSerialNumber" bound nested link field of the row 5 in the detail list is "View list"

        # When the user selects the nested link field with the value "View list" of the row "5" in the detail list
        # The Serial number details page launches.
        # Then the "Serial number" titled page is displayed
        #         # Verify values on Header card, mainBlock, and card record. Select a record
        # And the value of the "product" bound nested text field in the header card is "I-K150623-A"
        # And the value of the "code" bound nested text field of the row 1 in the card list is "SERI-K2800003"
        # And the value of the "code" bound nested text field of the row 2 in the card list is "SERI-K2800002"
        # And the value of the "code" bound nested text field of the row 3 in the card list is "SERI-K2800001"

        # Return to previous screen ( a modifier apres la correction du bug demandé a Nadine )
        # Then the user clicks the arrow left button in the navigation bar
        #  And the user clicks card 1 of the "stock" bound table field in the main page
        # And the user waits 1 seconds

        #Quantity to move
        And the user selects the "quantityToMove" bound numeric field on the main page
        And the user clicks in the numeric field
        And the user scrolls to the numeric field
        And the user writes "3" in the numeric field

        And the user selects the "locationDestination" bound reference field on the main page
        And the user writes "AAAAAC" in the reference field

        And the user selects the "startingSerialNumber" bound reference field on the main page
        And the user writes "SERI-K2800001" in the reference field
        And the user waits 3 seconds
        Then the value of the reference field is "SERI-K2800001"
        And the user selects the "endingSerialNumber" bound text field on the main page
        Then the value of the text field is "SERI-K2800003"

        And the user selects the "statusDestination" bound select field on the main page
        And the user writes "A1" in the select field
        And the user selects "A1" in the select field

        # Add serial to card table
        And the user selects the "serialNumberLines" bound table field on the main page
        When the user clicks the "addSerialRange" bound action of the table field

        And the user waits 3 seconds

        # Check destination card

        Then the value of the "startingSerialNumber" bound nested text field of the card 1 in the table field is "SERI-K2800001"
        And the value of the "quantity" bound nested numeric field of the card 1 in the table field is "3 UN"
        And the value of the "endingSerialNumber" bound nested text field of the card 1 in the table field is "SERI-K2800003"

        And the user clicks the "helperSelectButton" bound business action button on the main page

    Scenario: 8 Transfer the total of the 2nd product and checking  serial numbers

        And the user clicks the "nextButton" bound business action button on the main page
        And the user selects the "product" bound reference field on the main page
        And the user writes "I-K150623-A" in the reference field

        # CHECK ON CARDS
        And the user selects the "stock" bound table field on the main page
        Then the value of the "quantityToMove" bound nested numeric field of the card 1 in the table field is "0 UN"
        And the value of the "location" bound nested reference field of the card 1 in the table field is "AAAAAA"
        And the value of the "lot" bound nested text field of the card 1 in the table field is "LO2306I-K280001"
        # And the value of the "product" bound nested reference field of the card 2 in the table field is "I-K150623-A"
        And the value of the "quantityToMove" bound nested numeric field of the card 2 in the table field is "4 UN"
        # And the value of the "product" bound nested reference field of the card 2 in the table field is "I-K150623-A"
        And the value of the "location" bound nested reference field of the card 2 in the table field is "AAAAAA"
        And the value of the "lot" bound nested text field of the card 2 in the table field is "LO2306I-K280002"

        And the user selects the "stock" bound table field on the main page
        When the user clicks the card 2 in the table field

        And the user waits 1 seconds

        And  the title of the item on the row "3" in the detail list is "Lot"
        And  the value of the "lot" bound nested text field of the row 3 in the detail list is "LO2306I-K280002"

        And  the title of the item on the row "4" in the detail list is "Sublot"
        And  the value of the "sublot" bound nested text field of the row 4 in the detail list is "00001"

        And  the title of the item on the row "6" in the detail list is "Packing qty."
        And the value of the "quantityInPackingUnit" bound nested numeric field of the row 6 in the detail list is "4 UN"

        And  the title of the item on the row "7" in the detail list is "Conversion factor"
        And  the value of the "packingUnitToStockUnitConversionFactor" bound nested numeric field of the row 7 in the detail list is "1"

        And  the title of the item on the row "8" in the detail list is "Stock qty."
        And  the value of the "quantityInStockUnit" bound nested numeric field of the row 8 in the detail list is "4 UN"

        And  the title of the item on the row "9" in the detail list is "Allocated qty."
        And  the value of the "allocatedQuantity" bound nested numeric field of the row 9 in the detail list is "0 UN"

        And  the title of the item on the row "10" in the detail list is "Status"
        And  the value of the "status" bound nested reference field of the row 10 in the detail list is "A"

        # Serial no.
        And the title of the item on the row "5" in the detail list is "Serial no."
        Then the value of the "globalSerialNumber" bound nested link field of the row 5 in the detail list is "View list"

        # When the user selects the nested link field with the value "View list" of the row "5" in the detail list
        #         # The Serial number details page launches.
        # Then the "Serial number" titled page is displayed
        #         # Verify values on Header card, mainBlock, and card record. Select a record
        # And the value of the "product" bound nested text field in the header card is "I-K150623-A"
        # And the value of the "code" bound nested text field of the row 1 in the card list is "SERI-K2800007"
        # And the value of the "code" bound nested text field of the row 2 in the card list is "SERI-K2800006"
        # And the value of the "code" bound nested text field of the row 3 in the card list is "SERI-K2800005"
        # And the value of the "code" bound nested text field of the row 4 in the card list is "SERI-K2800004"

        # Return to previous screen ( a modifier apres la correction du bug demandé a Nadine )
        # Then the user clicks the arrow left button in the navigation bar
        #  And the user clicks card 2 of the "stock" bound table field in the main page
        # And the user waits 1 seconds

        #Quantity to move
        And the user selects the "quantityToMove" bound numeric field on the main page
        And the user clicks in the numeric field
        And the user scrolls to the numeric field
        And the user writes "4" in the numeric field

        And the user selects the "locationDestination" bound reference field on the main page
        And the user writes "AAAAAD" in the reference field


        And the user selects the "startingSerialNumber" bound reference field on the main page
        And the user writes "SERI-K2800004" in the reference field
        # And the user waits 3 seconds
        Then the value of the reference field is "SERI-K2800004"
        And the user selects the "endingSerialNumber" bound text field on the main page
        Then the value of the text field is "SERI-K2800007"


        # Add serial to card table
        And the user selects the "serialNumberLines" bound table field on the main page
        When the user clicks the "addSerialRange" bound action of the table field

        And the user waits 3 seconds

        # Check destination card

        Then the value of the "startingSerialNumber" bound nested text field of the card 1 in the table field is "SERI-K2800004"
        And the value of the "quantity" bound nested numeric field of the card 1 in the table field is "4 UN"
        And the value of the "endingSerialNumber" bound nested text field of the card 1 in the table field is "SERI-K2800007"

        And the user clicks the "helperSelectButton" bound business action button on the main page
        And the user clicks the "nextButton" bound business action button on the main page

        And the user selects the "product" bound reference field on the main page
        And the user writes "I-K150623-A" in the reference field

        When the "Intersite" titled page is displayed
        And the "Enter stock details" subtitled page is displayed
        # And the value of the "localizedDescription" bound nested text field in the header card is "I-K150623-A"
        And the value of the "product" bound nested reference field in the header card is "I-K150623-A"

        And the user selects the "stock" bound table field on the main page
        Then the value of the "quantityToMove" bound nested numeric field of the card 1 in the table field is "0 UN"
        # And the value of the "product" bound nested reference field of the card 1 in the table field is "I-K150623-A"
        And the value of the "location" bound nested reference field of the card 1 in the table field is "AAAAAA"
        And the value of the "lot" bound nested text field of the card 1 in the table field is "LO2306I-K280001"

        And the value of the "quantityToMove" bound nested numeric field of the card 2 in the table field is "0 UN"
        # And the value of the "product" bound nested reference field of the card 2 in the table field is "I-K150623-A"
        And the value of the "location" bound nested reference field of the card 2 in the table field is "AAAAAA"
        And the value of the "lot" bound nested text field of the card 2 in the table field is "LO2306I-K280002"

        And the user clicks the arrow left button in the navigation bar

    Scenario: 9 Last check before creation

        And the user selects the "effectiveDate" bound date field on the main page
        And the user writes a generated date in the date field with value "M/T/Y"

        And  the user selects the "transaction" bound dropdown-list field on the main page
        Then the value of the dropdown-list field is "STD"

        And the user selects the "siteDestination" bound reference field on the main page
        And the value of the reference field is "I-K29"

        And the "Products: 2 " labelled table field on the main page is displayed
        And the user selects the "intersiteTransferLines" bound table field on the main page
        Then the value of the "product" bound nested text field of the card 1 in the table field is "I-K150623-A"
        And the value of the "quantityAndStockUnit" bound nested text field of the card 1 in the table field is "4 UN"


        And the value of the "product" bound nested text field of the card 1 in the table field is "I-K150623-A"
        And the value of the "quantityAndStockUnit" bound nested text field of the card 2 in the table field is "3 UN"

    # And the user selects the "effectiveDate" bound date field on the main page
    # And the user writes a generated date in the date field with value "M/T/Y"

    Scenario: 10 Creation

        And the user clicks the "createButton" bound business action button on the main page
        Then the text in the header of the dialog is "Success"
        And the user clicks the "OK" button of the Confirm dialog
        And the user clicks the close button in the navigation bar

    Scenario: 11 Logout

        When the user logs out of the system
