@distribution
Feature: distribution-flow-mobile-internal-stock-change-qa2user-01

    ###########################################################################
    # Header
    # -------------------------------------------------------------------------
    # - Test code : distribution-flow-mobile-internal-stock-change-qa2user-01
    # - Description : I want to be able to use the qa2 user that has only the IAG10 stock site allowed on its user function profile,
    #                 so that I can control the autorization with Mobile Automation.
    #                 Functional Authorization: Function GESSCS / Grouped by Site: IAG10
    #                 Connect with  <EMAIL>
    # - Legislation : All legislations
    # - JIRA ID : X3-341973
    # - Created by : <PERSON><PERSON>DI
    # - Created date - 08-07-2025
    # - Updated by :
    # - Updated date :
    ###########################################################################

    Scenario: 01 <NAME_EMAIL> and select the endpoint

        When the user is logged into the system in mobile mode using the "param:loginUserName2" user name and "param:loginPassword2" password
        When the user selects the "param:endPointName1" endpoint
        Then the "param:endPointName1" endpoint is selected

    Scenario: 2 Set the site
        Given the user opens the application on a mobile
        When the user clicks the "Site" sticker in the navigation bar
        And the user selects the "Site" labelled reference field on a full width modal
        And the user clicks the lookup button of the reference field
        And searches for "IAG11" in the lookup dialog
        And the user selects the "selectionSite" bound reference field on a full width modal
        Then the value of the reference field is "IAG11"
        And the user clicks the "Set site" labelled business action button on a full width modal

    Scenario: 03 Select transaction MobileInternalStockChange, along with the product, and the destination location

        Given the user opens the application on a mobile using the following link: "@sage/x3-stock/MobileInternalStockChange"
        And the user selects the "productSelected" bound reference field on the main page
        And the user writes "I-K090323" in the reference field
        And the value of the reference field is "I-K090323"
        And the user selects the "mainLocationDestination" bound reference field on the main page
        And the user writes "ILOC03" in the reference field
        And the value of the reference field is "ILOC03"
        And the user clicks the "nextButton" bound business action button on the main page
        And the user selects the "stock" bound table field on the main page
        And the user clicks the card 1 in the table field
        And the user waits 1 seconds
        And the user selects the "quantityToMove" bound numeric field on the main page
        And the user writes "1" in the numeric field
        And the value of the numeric field is "1"
        And the user clicks the "SelectButton" bound business action button on the main page
        And the user waits 5 seconds
        And the user clicks the "nextButton" bound business action button on the main page

    Scenario: 04 Confirm that the document can't be created: message You do not have the rights for this site.

        And the user clicks the "createButton" bound business action button on the main page
        And  the text in the header of the dialog is "Error"
        Then the text in the body of the dialog contains "IAG11 : You do not have the rights for this site" on the main page
        And the user clicks the close button in the navigation bar

    Scenario: 4 Logout

        When the user logs out of the system
