@distribution
Feature: distribution-flow-mobile-intersite-receipt-02
    ###########################################################################
    # Header
    # -------------------------------------------------------------------------
    # - Test code : distribution-flow-mobile-intersite-receipt-02
    # - Description :As a QA working on X3services framework, I want to be bale to cover the following tests:
    #                 Create a product with a packing unit with a changeable conversion factor.
    #                 Create the corresponding site products for 2 customer-supplier intersite sites.
    #                 Create a miscellaneous entry on the supplier site with this product.
    #                 Create a purchase order on the customer site with this product.
    #                 When the purchase order is created, a corresponding sales order is created on the supplier site.
    #                 For this sales order, create and validate the delivery.
    #                 In Mobile automation intersite receipt on the customer site, select this delivery, and select the product line.
    #                 On the detail page, select the packing unit, enter a quantity lower than that proposed, modify the conversion factor, select a location and click on submit button.
    #                 The detail page is displayed with the remaining quantity.
    #                 Try to change the conversion factor.
    #                 Expected result:
    #                 Block the change of the conversion factor after a first entry
    #                 Initialize the conversion factor to the same value as the first change for the remaining entries
    #                 Trigger a blocking message: "The conversion factor cannot be different from that entered previously on the same stock line."
    # - Legislation : All legislations
    # - JIRA ID : X3-333754
    # - Created by : Karim MILOUDI
    # - Created date -16-07-2025
    # - Updated by :
    # - Updated date :
    ###########################################################################

    Scenario: 01 Select the endpoint
        Given the user opens the application on a mobile
        When the user selects the "param:endPointName1" endpoint
        Then the "param:endPointName1" endpoint is selected

    Scenario: 02 Set the site I-M07

        Given the user opens the application on a mobile
        When the user clicks the "Site" sticker in the navigation bar
        And the user selects the "Site" labelled reference field on a full width modal
        And the user clicks the lookup button of the reference field
        And searches for "I-M07" in the lookup dialog
        And the user selects the "selectionSite" bound reference field on a full width modal
        Then the value of the reference field is "I-M07"
        And the user clicks the "Set site" labelled business action button on a full width modal
        And the user waits 2 seconds

    Scenario: 03 Select this delivery, and select the product line.

        Given the user opens the application on a mobile using the following link: "@sage/x3-purchasing-sales/MobileIntersiteReceipt"
        And the user waits 5 seconds
        And the user selects the "salesDelivery" bound reference field on the main page
        And the user writes "I-M082507SDI00000001" in the reference field
        And the value of the reference field is "I-M082507SDI00000001"
        And the user selects the "supplier" bound reference field on the main page
        And the value of the reference field is "I-M08"
        And the user selects the "salesDeliveryLine" bound reference field on the main page
        And the user writes "I-K110725-A" in the reference field

    Scenario: 04 On the detail page, check the state and default values of the fields Unit, Quantity and conversion factor

        Then the "Intersite receipt" titled page is displayed
        And the value of the "headerTitleLeft" bound nested text field in the header card is "I-M082507SDI00000001"
        And the value of the "headerTitleRight" bound nested text field in the header card is "Line no. 1000"
        And the value of the "headerLine2Left" bound nested text field in the header card is "I-K110725-A"
        And the value of the "headerLine3Left" bound nested text field in the header card is "Expected quantity"
        And the value of the "headerLine3Right" bound nested text field in the header card is "1000 UN"
        And the user selects the "destinationReceiptUnit" bound dropdown-list field on the main page
        And the value of the dropdown-list field is "UN"
        And the user selects the "destinationQuantityToReceive" bound numeric field on the main page
        And the value of the numeric field is "1,000"
        And the user selects the "destinationPackingUnitToStockUnitConversionFactor" bound numeric field on the main page
        And the numeric field is disabled
        And the value of the numeric field is "1"

    Scenario: 05 On the detail page, select the packing unit, enter a quantity lower than that proposed and modify the conversion factor.

        And the user selects the "destinationReceiptUnit" bound dropdown-list field on the main page
        When the user clicks in the dropdown-list field
        And the user selects "CAR" in the dropdown-list field
        And the value of the dropdown-list field is "CAR"
        And the user selects the "destinationQuantityToReceive" bound numeric field on the main page
        And the value of the numeric field is "200"
        And the user selects the "destinationPackingUnitToStockUnitConversionFactor" bound numeric field on the main page
        And the numeric field is enabled
        And the value of the numeric field is "5"
        And the user selects the "destinationQuantityToReceive" bound numeric field on the main page
        And the user writes "150" in the numeric field
        And the user selects the "destinationPackingUnitToStockUnitConversionFactor" bound numeric field on the main page
        And the numeric field is enabled
        And the user writes "4" in the numeric field
        And the value of the numeric field is "4"
        And the user selects the "destinationLocation" bound reference field on the main page
        And the title of the reference field is displayed
        And the reference field is enabled
        And the value of the reference field is ""
        And the user writes "LOC001" in the reference field
        And the value of the reference field is "LOC001"
        And the user clicks the "addButton" bound business action button on the main page

    Scenario: 06 On the detail page, verify that the remaining quantity is displayed correctly.

        And the user selects the "destinationQuantityToReceive" bound numeric field on the main page
        And the value of the numeric field is "100"
        And the user selects the "destinationReceiptUnit" bound dropdown-list field on the main page
        And the dropdown-list field is disabled
        And the value of the dropdown-list field is "CAR"

    Scenario: 07 Try to change the conversion factor and check message "The conversion factor cannot be different from that entered previously on the same stock line."

        And the user selects the "destinationPackingUnitToStockUnitConversionFactor" bound numeric field on the main page
        And the numeric field is enabled
        And the user writes "3" in the numeric field
        And the user clicks the "addButton" bound business action button on the main page
        And the user waits 2 seconds
        And a error toast with text "The conversion factor can't be different as the one entered previously on the same stock line." is displayed
        And the user waits 2 seconds

    Scenario: 08 Verify the data: quantities and location on nested grid Card list-level (part 1)

        And the "Intersite receipt" titled page is displayed
        And the user selects the "lines" bound nested grid field on the main page
        And the level in the header of the mobile nested grid field is "1/2"
        And the value of the "product" bound nested reference field of the card 1 in the nested grid field is "I-K110725-A"
        And the value of the "quantityInReceiptUnitReceived" bound nested numeric field of the card 1 in the nested grid field is "250 CAR"

    Scenario: 09 Document creation

        And the user clicks the "createButton" bound business action button on the main page
        Then the text in the header of the dialog is "Success"
        And the user clicks the "OK" button of the Confirm dialog
        And the user clicks the close button in the navigation bar

    Scenario: 10 Logout

        When the user logs out of the system
