@distribution
Feature: distribution-flow-intersite-transfer-02
    ###########################################################################
    # Header
    # -------------------------------------------------------------------------
    # - Test code : distribution-flow-intersite-transfer-02
    # - Description : Intersite transfer Product managed by LPN
    # - Legislation : All legislations
    # - JIRA ID : X3-299020
    # - Created by : <PERSON><PERSON>
    # - Created date - 15-06-2023
    # - Updated by :
    # - Updated date :
    ###########################################################################

    Scenario: 1 Select the endpoint

        Given the user opens the application on a mobile
        When the user selects the "param:endPointName1" endpoint
        Then the "param:endPointName1" endpoint is selected

    Scenario: 2 Set the site I-K26
        Given the user opens the application on a mobile
        When the user clicks the "Site" sticker in the navigation bar
        And the user selects the "Site" labelled reference field on a full width modal
        And the user clicks the lookup button of the reference field
        And searches for "I-K26" in the lookup dialog
        And the user selects the "selectionSite" bound reference field on a full width modal
        Then the value of the reference field is "I-K26"
        And the user clicks the "Set site" labelled business action button on a full width modal

    Scenario: 3 AdcIntersiteTransfer

        Given the user opens the application on a mobile using the following link: "@sage/x3-stock/MobileIntersiteTransfer"
        And the user waits 2 seconds
        And the user selects the "siteDestination" bound reference field on the main page
        And the user clicks the lookup button of the reference field
        And searches for "I-K27" in the lookup dialog
        And the user selects the "product" bound reference field on the main page
        And the user writes "I-K140623-A" in the reference field

    Scenario: 4 Verification of stock lines without filter

        When the "Intersite" titled page is displayed
        And the "Enter stock details" subtitled page is displayed
        And the value of the "localizedDescription" bound nested text field in the header card is "I-K140623-A"

        And the user selects the "stock" bound table field on the main page
        Then the value of the "quantityToMove" bound nested numeric field of the card 1 in the table field is "10 UN"
        # And the value of the "product" bound nested reference field of the card 1 in the table field is "I-K140623-A"
        And the value of the "location" bound nested reference field of the card 1 in the table field is "AAAAAA"
        # And the value of the "licensePlateNumber" bound nested text field of the row 1 in the card list is "ZAE23I-*********"

        And the value of the "quantityToMove" bound nested numeric field of the card 2 in the table field is "20 UN"
        # And the value of the "product" bound nested reference field of the card 2 in the table field is "I-K140623-A"
        And the value of the "location" bound nested reference field of the card 2 in the table field is "AAAAAA"
    # And the value of the "licensePlateNumber" bound nested text field of the row 2 in the card list is "ZAE23I-*********"

    Scenario: 5 Verification of stock lines with LPN filter : ZAE23I-*********


        And the user selects the "licensePlateNumber" bound reference field on the main page
        And the user clicks the lookup button of the reference field
        And searches for "ZAE23I-*********" in the lookup dialog
        And the user selects the "stock" bound table field on the main page
        Then the value of the "quantityToMove" bound nested numeric field of the card 1 in the table field is "10 UN"
        # And the value of the "product" bound nested reference field of the card 1 in the table field is "I-K140623-A"
        And the value of the "location" bound nested reference field of the card 1 in the table field is "AAAAAA"
    # And the value of the "licensePlateNumber" bound nested text field of the row 1 in the card list is "ZAE23I-*********"

    Scenario: 6 Verification of stock lines with LPN filter : ZAE23I-*********


        And the user selects the "licensePlateNumber" bound reference field on the main page
        And the user clicks the lookup button of the reference field
        And searches for "ZAE23I-*********" in the lookup dialog
        And the user selects the "stock" bound table field on the main page
        Then the value of the "quantityToMove" bound nested numeric field of the card 1 in the table field is "20 UN"
        # And the value of the "product" bound nested reference field of the card 1 in the table field is "I-K140623-A"
        And the value of the "location" bound nested reference field of the card 1 in the table field is "AAAAAA"
    # And the value of the "licensePlateNumber" bound nested text field of the row 1 in the card list is "ZAE23I-*********"

    Scenario: 7 Message checked : "Select at least one stock line."

        And the user clicks the "nextButton" bound business action button on the main page
        And the user waits 3 seconds
        Then the text in the header of the dialog is "Error"
        And the user waits 3 seconds
        Then the text in the body of the dialog is "Select at least one stock line."
        And the user clicks the "OK" button of the Confirm dialog

    # Scenario: 8 Message checked "The destination location is mandatory."

    #     And the user selects the "licensePlateNumber" bound reference field on the main page
    #     And the user clears the reference field

    #     And the user selects the "stock" bound table field on the main page
    #     When the user clicks the card 1 in the table field
    #     And the user waits 1 seconds
    #     And the user clicks the "helperSelectButton" bound business action button on the main page
    #     And the user waits 3 seconds
    #     Then the text in the header of the dialog is "Error"
    #     And the user waits 3 seconds
    #     Then the text in the body of the dialog is "Enter a quantity to move greater than 0."
    #     And the user clicks the "OK" button of the Confirm dialog

    Scenario: 9 Message checked "Enter a quantity to move greater than 0."

        And the user selects the "licensePlateNumber" bound reference field on the main page
        And the user clears the reference field

        And the user selects the "stock" bound table field on the main page
        When the user clicks the card 1 in the table field
        And the user waits 1 seconds
        #Quantity to move
        And the user selects the "quantityToMove" bound numeric field on the main page
        And the user scrolls to the numeric field
        And the user clicks in the numeric field
        And the user writes "0" in the numeric field
        And the user clicks the "helperSelectButton" bound business action button on the main page
        And the user waits 3 seconds
        Then the text in the header of the dialog is "Error"
        And the user waits 3 seconds
        Then the text in the body of the dialog is "Enter a quantity to move greater than 0."
        And the user clicks the "OK" button of the Confirm dialog

    Scenario: 10 Message checked "Enter a quantity less than or equal to the stock quantity."

        And the user selects the "quantityToMove" bound numeric field on the main page
        And the user scrolls to the numeric field
        And the user clicks in the numeric field
        And the user writes "30" in the numeric field
        And the user selects the "locationDestination" bound reference field on the main page
        And the user writes "AAAAAC" in the reference field
        And the user clicks the "helperSelectButton" bound business action button on the main page
        And the user waits 3 seconds
        Then the text in the header of the dialog is "Error"
        And the user waits 3 seconds
        Then the text in the body of the dialog is "Enter a quantity less than or equal to the stock quantity minus the allocated quantity."
        And the user clicks the "OK" button of the Confirm dialog

    Scenario: 11 full transfer of first card

        And  the title of the item on the row "2" in the detail list is "License plate number"
        And  the value of the "licensePlateNumber" bound nested reference field of the row 2 in the detail list is "ZAE23I-*********"

        And  the title of the item on the row "4" in the detail list is "Packing qty."
        And the value of the "quantityInPackingUnit" bound nested numeric field of the row 4 in the detail list is "10 UN"

        # And  the title of the item on the row "5" in the detail list is "Conversion factor"
        # And the value of the "packingUnitToStockUnitConversionFactor" bound nested numeric field of the row 5 in the detail list is "1"

        And  the title of the item on the row "6" in the detail list is "Stock qty."
        And  the value of the "quantityInStockUnit" bound nested numeric field of the row 6 in the detail list is "10 UN"

        And  the title of the item on the row "7" in the detail list is "Allocated qty."
        And  the value of the "allocatedQuantity" bound nested numeric field of the row 7 in the detail list is "0 UN"

        And  the title of the item on the row "8" in the detail list is "Status"
        And  the value of the "status" bound nested reference field of the row 8 in the detail list is "A"

        And the user selects the "quantityToMove" bound numeric field on the main page
        And the user clears the numeric field
        And the user writes "10" in the numeric field
        And the user clicks the "helperSelectButton" bound business action button on the main page
        And the user waits 1 seconds
        And the user clicks the "nextButton" bound business action button on the main page

    Scenario: 12 Last check before creation

        # And the user selects the "effectiveDate" bound date field on the main page
        # And the user writes a generated date in the date field with value "M/T/Y"

        And  the user selects the "transaction" bound dropdown-list field on the main page
        Then the value of the dropdown-list field is "STD"

        And the user selects the "siteDestination" bound reference field on the main page
        And the value of the reference field is "I-K27"

        And the "Products: 1 " labelled table field on the main page is displayed
        And the user selects the "intersiteTransferLines" bound table field on the main page
        Then the value of the "product" bound nested text field of the card 1 in the table field is "I-K140623-A"
        And the value of the "quantityAndStockUnit" bound nested text field of the card 1 in the table field is "10 UN"

    # And the user selects the "effectiveDate" bound date field on the main page
    # And the user writes a generated date in the date field with value "M/T/Y"

    Scenario: 13 Creation

        And the user clicks the "createButton" bound business action button on the main page
        Then the text in the header of the dialog is "Success"
        And the user clicks the "OK" button of the Confirm dialog
        And the user clicks the close button in the navigation bar

    Scenario: 12 Logout

        When the user logs out of the system
