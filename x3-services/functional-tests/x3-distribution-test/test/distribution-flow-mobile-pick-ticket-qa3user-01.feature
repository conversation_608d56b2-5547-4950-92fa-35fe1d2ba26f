@distribution
Feature: distribution-flow-mobile-pick-ticket-qa3user-01

    ###########################################################################
    # Header
    # -------------------------------------------------------------------------
    # - Test code : distribution-flow-mobile-pick-ticket-qa3user-01
    # - Description : Automated test- User with ADCDIS badge only + function profile different as Admin
    #                 Function GESPRH
    #                 Connect with this ADC user.
    #                 The pick ticket will have two products to pick, with detailed allocation.
    #                 Make the pick ticket to deliverable = Yes.
    # - Legislation : All legislations
    # - JIRA ID : X3-330591
    # - Created by : <PERSON><PERSON> MILOUDI
    # - Created date - 01-07-2025
    # - Updated by :
    # - Updated date :
    ###########################################################################


    Scenario: 01 <NAME_EMAIL> and select the endpoint

        When the user is logged into the system in mobile mode using the "param:loginUserName3" user name and "param:loginPassword3" password
        When the user selects the "param:endPointName1" endpoint
        Then the "param:endPointName1" endpoint is selected

    Scenario: 02 Set the site
        Given the user opens the application on a mobile
        When the user clicks the "Site" sticker in the navigation bar
        And the user selects the "Site" labelled reference field on a full width modal
        And the user selects the "Site" labelled reference field on a full width modal
        And the user clicks the lookup button of the reference field
        And searches for "I-M15" in the lookup dialog
        And the user selects the "selectionSite" bound reference field on a full width modal
        Then the value of the reference field is "I-M15"
        And the user clicks the "Set site" labelled business action button on a full width modal
        And the user waits 1 seconds

    Scenario: 03 Select Pick ticket and destination location

        Given the user opens the application on a mobile using the following link: "@sage/x3-stock/MobilePickTicket"
        When the "Pick ticket" titled page is displayed
        And the user waits 3 seconds
        And the user selects the "pickTicket" bound reference field on the main page
        And the user writes "I-M152506PIC00000002" in the reference field
        And the user selects the "destinationLocation" bound reference field on the main page
        And the user writes "AAAAAB" in the reference field
        And the user waits 2 seconds
        When the "Pick ticket" titled page is displayed
        And the "Select a line" subtitled page is displayed
        When the user clicks the "To do" toggle button in the navigation panel
        And the user searches for "I-M152506PIC00000002" in the navigation panel
        And the user waits 3 second
        And the user clicks the "1" navigation panel's row
        When the "Pick ticket" titled page is displayed
        And the "Enter line details" subtitled page is displayed
        And  the value of the "pickTicket" bound nested text field in the header card is "I-M152506PIC00000002"
        And  the value of the "product" bound nested text field in the header card is "I-K240425-E"
        And  the value of the "displayQuantityToBePicked" bound nested text field in the header card is "27 UN"
        Then the user clicks the "pickButton" bound business action button on the main page
        When the "Pick ticket" titled page is displayed
        And the "Select a line" subtitled page is displayed
        When the user clicks the "To do" toggle button in the navigation panel
        And the user searches for "I-M152506PIC00000002" in the navigation panel
        And  the value of the "pickTicket" bound nested text field in the header card is "I-M152506PIC00000002"
        And  the value of the "product" bound nested text field in the header card is "I-K240425-F"
        And  the value of the "displayQuantityToBePicked" bound nested text field in the header card is "28 UN"
        Then the user clicks the "pickButton" bound business action button on the main page
        And the user waits 2 seconds

    Scenario: 04 Check "Set the pick ticket to 'Deliverable'?" warning

        And the text in the header of the dialog is "Warning"
        And the text in the body of the dialog is "Set the pick ticket to 'Deliverable'?"
        And the user waits 2 seconds
        And takes a screenshot
        And the user waits 2 seconds
        And the user clicks the "Yes" button of the Confirm dialog

    Scenario: 05 Check "The pick ticket is now set as deliverable." information

        And the text in the header of the dialog is "Information"
        And the text in the body of the dialog is "The pick ticket is now set as deliverable."
        And the user waits 2 seconds
        And takes a screenshot
        And the user waits 2 seconds
        And the user clicks the "OK" button of the Confirm dialog
        And the user waits 1 seconds

    Scenario: 06 Logout
        And the user clicks the close button in the navigation bar
        And the user waits 1 seconds
        When the user logs out of the system
