@distribution
Feature: distribution-flow-putaway-qa2user-01

      ###########################################################################
      # Header
      # -------------------------------------------------------------------------
      # - Test code : distribution-flow-putaway-qa2user-01
      # - Description : Case 1 the user has no rights on the IAG11 site (Function : FUNSSL)
      # - Legislation : All legislations
      # - JIRA ID : X3-295160
      # - Created by : <PERSON><PERSON>UDI
      # - Created date - 21-03-2023
      # - Updated by :
      # - Updated date :
      ###########################################################################

      Scenario: 1 Select the endpoint

            When the user is logged into the system in mobile mode using the "param:loginUserName2" user name and "param:loginPassword2" password
            When the user selects the "param:endPointName1" endpoint
            Then the "param:endPointName1" endpoint is selected

      Scenario: 2 Set the site
            Given the user opens the application on a mobile
            When the user clicks the "Site" sticker in the navigation bar
            And the user selects the "Site" labelled reference field on a full width modal
            And the user clicks the lookup button of the reference field
            And searches for "IAG11" in the lookup dialog
            And the user selects the "selectionSite" bound reference field on a full width modal
            Then the value of the reference field is "IAG11"
            And the user clicks the "Set site" labelled business action button on a full width modal

      Scenario: 3 Putaway
            Given the user opens the application on a mobile using the following link: "@sage/x3-stock/MobilePutaway"
            When the "Putaway" titled page is displayed

            # Write Transaction
            And  the user selects the "transaction" bound dropdown-list field on the main page
            And the user scrolls to the dropdown-list field
            And the user clicks in the dropdown-list field
            And the user writes "ADC" in the dropdown-list field
            And the user selects "ADC" in the dropdown-list field
            Then the value of the dropdown-list field is "ADC"

            # Select a Storage list
            And the user selects the "storageList" bound select field on the main page
            And the user clicks in the select field
            And the user selects "IAG112303LAR00000001" in the select field
            When the "Putaway" titled page is displayed
            And the "Select a product" subtitled page is displayed
            And the user selects the "lines" bound table field on the main page
            When the user clicks the card 1 in the table field
            When the "Putaway" titled page is displayed
            And the "Enter details" subtitled page is displayed
            And the value of the "_storageListNumber" bound nested text field in the header card is "IAG112303LAR00000001"
            And the value of the "headerProduct" bound nested text field in the header card is "I-K090323"
            And the value of the "_localizedDescription1" bound nested text field in the header card is "I-K090323"
            And the user selects the "fromLocation" bound reference field on the main page
            Then the value of the reference field is "DOCK0"
            And the user selects the "fromStatus" bound text field on the main page
            Then the value of the text field is "A"
            And the user selects the "packingUnit" bound reference field on the main page
            Then the value of the reference field is "UN"
            And the user selects the "quantityInPackingUnit" bound numeric field on the main page
            Then the value of the numeric field is "70"
            # #Step to be activated after bug correction X3-313802
            # And the user selects the "toStatus" bound select field on the main page
            # And the user writes "A" in the select field
            # And the user selects "A" in the select field
            # Then the value of the select field is "A"
            # And the user waits 2 seconds
            # And the user selects the "toLocation" bound reference field on the main page
            # And the user scrolls to the reference field
            # And the user writes "ILOC01" in the reference field
            And the user clicks the "submitButton" bound business action button on the main page

            Then the text in the header of the dialog is "Error"

            Then the text in the body of the dialog contains "IAG11 : You do not have the rights for this site" on the main page

            And the user clicks the "Cancel" button of the Message dialog
            Then the user clicks the close button in the navigation bar
            And the user waits 2 seconds

      Scenario: 4 Logout

            When the user logs out of the system
