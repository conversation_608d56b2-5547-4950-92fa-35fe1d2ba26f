@distribution
Feature: distribution-flow-pick-ticket-qa2user-01

    ###########################################################################
    # Header
    # -------------------------------------------------------------------------
    # - Test code : distribution-flow-pick-ticket-qa2user-01
    # - Description : Case 1 the user has no rights on the IAG11 site (GESPRH function)
    # - Legislation : All legislations
    # - JIRA ID : X3-295163
    # - Created by : <PERSON><PERSON>UDI
    # - Created date - 14-03-2023
    # - Updated by :
    # - Updated date :
    ###########################################################################

    Scenario: 1 Select the endpoint

        When the user is logged into the system in mobile mode using the "param:loginUserName2" user name and "param:loginPassword2" password
        When the user selects the "param:endPointName1" endpoint
        Then the "param:endPointName1" endpoint is selected

    Scenario: 2 Set the site
        Given the user opens the application on a mobile
        When the user clicks the "Site" sticker in the navigation bar
        And the user selects the "Site" labelled reference field on a full width modal
        And the user clicks the lookup button of the reference field
        And searches for "IAG11" in the lookup dialog
        And the user selects the "selectionSite" bound reference field on a full width modal
        Then the value of the reference field is "IAG11"
        And the user clicks the "Set site" labelled business action button on a full width modal

    Scenario: 3 Pick ticket with detailed allocation

        Given the user opens the application on a mobile using the following link: "@sage/x3-stock/MobilePickTicket"
        When the "Pick ticket" titled page is displayed


        And  the user selects the "transaction" bound dropdown-list field on the main page
        And the user scrolls to the dropdown-list field
        And the user clicks in the dropdown-list field
        And the user writes "STD" in the dropdown-list field
        And the user selects "STD" in the dropdown-list field
        Then the value of the dropdown-list field is "STD"


        And the user selects the "Pick ticket" labelled reference field on the main page
        And the user clicks the lookup button of the reference field
        And searches for "IAG112302PIC00000003" in the lookup dialog
        And the user selects the "destinationLocation" bound reference field on the main page
        And the user writes "ILOC02" in the reference field


        When the "Pick ticket" titled page is displayed
        And the "Select a line" subtitled page is displayed
        When the user clicks the "To do" toggle button in the navigation panel
        And the user searches for "IAG112302PIC00000003" in the navigation panel
        Then the user clicks the "first" navigation panel's row
        When the "Pick ticket" titled page is displayed
        And the "Enter line details" subtitled page is displayed
        # Verify the header
        And the value of the "pickTicket" bound nested text field in the header card is "IAG112302PIC00000003"
        And the value of the "pickTicketLine" bound nested text field in the header card is "1"
        And the value of the "product" bound nested text field in the header card is "I-PROD002"
        And the value of the "textQuantityToBePicked" bound nested text field in the header card is "Quantity to pick:"
        And the value of the "displayQuantityToBePicked" bound nested text field in the header card is "5 UN"
        #Click Submit
        And the user clicks the "pickButton" bound business action button on the main page



        Then the text in the header of the dialog is "Error"
        # Then the text in the body of the dialog is "An error occurred.: x3Stock.pickTicketLine.updatePickTicketLine: IAG11 : You do not have the rights for this site"
        Then the text in the body of the dialog contains "IAG11 : You do not have the rights for this site" on the main page
        And the user waits 1 seconds

        And the user clicks the close button in the navigation bar
        And the user waits 1 seconds

    Scenario: 4 Logout

        When the user logs out of the system
