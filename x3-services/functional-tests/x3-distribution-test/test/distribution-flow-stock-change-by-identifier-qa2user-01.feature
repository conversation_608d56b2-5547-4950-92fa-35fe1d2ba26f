@distribution
Feature: distribution-flow-stock-change-by-identifier-qa2user-01

    ###########################################################################
    # Header
    # -------------------------------------------------------------------------
    # - Test code : distribution-flow-stock-change-by-identifier-qa2user-01
    # - Description : Case 1 the user has no rights on the IAG11 site (CWSBSCS function)
    # - Legislation : All legislations
    # - JIRA ID : X3-305372
    # - Created by : <PERSON><PERSON>DI
    # - Created date - 31-10-2023
    # - Updated by :
    # - Updated date :
    ###########################################################################

    Scenario: 1 Select the endpoint

        When the user is logged into the system in mobile mode using the "param:loginUserName2" user name and "param:loginPassword2" password
        When the user selects the "param:endPointName1" endpoint
        Then the "param:endPointName1" endpoint is selected

    Scenario: 2 Set the site
        Given the user opens the application on a mobile
        When the user clicks the "Site" sticker in the navigation bar
        And the user selects the "Site" labelled reference field on a full width modal
        And the user clicks the lookup button of the reference field
        And searches for "IAG11" in the lookup dialog
        And the user selects the "selectionSite" bound reference field on a full width modal
        Then the value of the reference field is "IAG11"
        And the user clicks the "Set site" labelled business action button on a full width modal

    Scenario: 3 Selection of the "CHI" transaction and "Identifier 1"  filters

        Given the user opens the application on a mobile using the following link: "@sage/x3-stock/MobileStockChangeByIdentifier"

        And  the user selects the "transaction" bound dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user writes "CHI" in the dropdown-list field
        And the user selects "CHI" in the dropdown-list field
        Then the value of the dropdown-list field is "CHI"

        And  the user selects the "identifier" bound dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user writes "Identifier 1" in the dropdown-list field
        And the user selects "Identifier 1" in the dropdown-list field
        Then the value of the dropdown-list field is "Identifier 1"

        And the user clicks the "nextButton" bound business action button on the main page

    Scenario: 4 The user try to make a stock change using Select by identifier1

        When the "Stock change" titled page is displayed
        And the "Select by identifier" subtitled page is displayed

        And the user selects the "identifier1" bound text field on the main page
        And the user writes "IAD1" in the text field
        And the value of the text field is "IAD1"
        And the user clicks the "searchButton" bound business action button on the main page

        #card 1
        And the user waits 1 second
        And the user selects the "stock" bound table field on the main page
        Then the value of the "quantityToMove" bound nested numeric field of the card 1 in the table field is "2 / 2 UN"
        And the value of the "product" bound nested reference field of the card 1 in the table field is "I-K311023-A"
        And the value of the "location" bound nested reference field of the card 1 in the table field is "ILOC01"
        And the value of the "identifier1" bound nested text field of the card 1 in the table field is "IAD1"

        #card 2
        And the user waits 1 second
        Then the value of the "quantityToMove" bound nested numeric field of the card 2 in the table field is "4 / 4 UN"
        And the value of the "product" bound nested reference field of the card 2 in the table field is "I-K311023-B"
        And the value of the "location" bound nested reference field of the card 2 in the table field is "ILOC01"
        And the value of the "identifier1" bound nested text field of the card 2 in the table field is "IAD1"

        And the user selects the "selectAllSwitch" bound switch field on the main page
        # #Switch field - Verify value
        And the switch field is set to "OFF"
        # #Switch field - Modify value
        And the user turns the switch field "ON"
        #Switch field - Verify value
        And the switch field is set to "ON"

    Scenario: 5 Warning message : "IAG11 : You do not have the rights for this site"

        And the user clicks the "nextButton" bound business action button on the main page

        When the "Stock change" titled page is displayed
        And the "Enter destination" subtitled page is displayed

        And the user selects the "statusDestination" bound select field on the main page
        And the user writes "A1" in the select field
        And the user selects "A1" in the select field

        And the user clicks the "nextButton" bound business action button on the main page

        And the user selects the "effectiveDate" bound date field on the main page
        And the user writes a generated date in the date field with value "M/T/Y"
        Then the title of the "stockChangeLinesBlock" bound block container on the main page is "Stock lines"

        And the user clicks the "createButton" bound business action button on the main page
        And the user waits 1 second
        Then the text in the header of the dialog is "Error"
        Then the text in the body of the dialog contains "IAG11 : You do not have the rights for this site" on the main page

        And the user waits 1 second

        And takes a screenshot

        And the user clicks the close button in the navigation bar

    Scenario: 6 Logout

        When the user logs out of the system
