@distribution
Feature: distribution-flow-mobile-miscellaneous-receipt-qa3user-01

    ###########################################################################
    # Header
    # -------------------------------------------------------------------------
    # - Test code : distribution-flow-mobile-miscellaneous-receipt-qa3user-01
    # - Description : Automated test- User with ADCDIS badge only + function profile different as Admin
    #                 Function : GESSMR
    #                 Connect with this ADC user
    #                 The misc. receipt will be a stock receipt, with a location defined. No need to go into the sidebar.
    # - Legislation : All legislations
    # - JIRA ID :X3-330591
    # - Created by : <PERSON><PERSON>
    # - Created date - 01-07-2025
    # - Updated by :
    # - Updated date :
    ###########################################################################

    Scenario: 01 <NAME_EMAIL> and select the endpoint

        When the user is logged into the system in mobile mode using the "param:loginUserName3" user name and "param:loginPassword3" password
        When the user selects the "param:endPointName1" endpoint
        Then the "param:endPointName1" endpoint is selected

    Scenario: 02 Set the site

        Given the user opens the application on a mobile
        When the user clicks the "Site" sticker in the navigation bar
        And the user selects the "Site" labelled reference field on a full width modal
        And the user clicks the lookup button of the reference field
        And searches for "I-M14" in the lookup dialog
        And the user selects the "selectionSite" bound reference field on a full width modal
        Then the value of the reference field is "I-M14"
        And the user clicks the "Set site" labelled business action button on a full width modal

    Scenario: 03 Scenario: 03 Select transaction @sage/x3-stock/MobileMiscellaneousReceipt.

        Given the user opens the application on a mobile using the following link: "@sage/x3-stock/MobileMiscellaneousReceipt"
        And the user waits 2 seconds
        And the user selects the "product" bound reference field on the main page
        And the user clicks the lookup button of the reference field
        And searches for "I-K240425-D" in the lookup dialog
        And the user selects the "quantityInPackingUnit" bound numeric field on the main page
        And the user writes "1" in the numeric field
        And the user selects the "location" bound reference field on the main page
        And the user writes "AAAAAA" in the reference field
        And the user clicks the "addButton" bound business action button on the main page

    Scenario: 04 Create Document

        And the user clicks the "createButton" bound business action button on the main page
        Then the text in the header of the dialog is "Success"
        And the user waits 2 seconds
        And takes a screenshot
        And the user clicks the "OK" button of the Confirm dialog
        And the user clicks the close button in the navigation bar

    Scenario: 05 Logout

        When the user logs out of the system
