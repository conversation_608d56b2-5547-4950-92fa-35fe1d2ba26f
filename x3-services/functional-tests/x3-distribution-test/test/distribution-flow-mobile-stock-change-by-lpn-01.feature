@distribution
Feature: distribution-flow-mobile-stock-change-by-lpn-01
    ###########################################################################
    # Header
    # -------------------------------------------------------------------------
    # - Test code : distribution-flow-mobile-stock-change-by-lpn-01
    # - Description : Define the entry transaction: location change
    #                 Apply a change on the sidebar: change the location value
    #                 Create
    # - Legislation : All legislations
    # - JIRA ID : X3-340285
    # - Created by : <PERSON><PERSON>DI
    # - Created date - 24-06-2025
    # - Updated by :
    # - Updated date :
    ###########################################################################

    Scenario: 01 Select the endpoint
        Given the user opens the application on a mobile
        When the user selects the "param:endPointName1" endpoint
        Then the "param:endPointName1" endpoint is selected

    Scenario: 02 Set the site I-M09
        Given the user opens the application on a mobile
        When the user clicks the "Site" sticker in the navigation bar
        And the user selects the "Site" labelled reference field on a full width modal
        And the user clicks the lookup button of the reference field
        And searches for "I-M09" in the lookup dialog
        And the user selects the "selectionSite" bound reference field on a full width modal
        Then the value of the reference field is "I-M09"
        And the user clicks the "Set site" labelled business action button on a full width modal
        And the user waits 2 seconds

    Scenario: 03 Select an entry transaction configured to allow location changes only, verify that only the destination location is displayed and that the destination status is hidden.

        Given the user opens the application on a mobile using the following link: "@sage/x3-stock/MobileStockChangeByLpn"
        And the user waits 3 seconds
        Then the "Stock change by LPN" titled page is displayed
        And the user selects the "transaction" bound dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "CONT2" in the dropdown-list field
        And the user selects the "licensePlateNumber" bound reference field on the main page
        And the user writes "ZAE25I-*********" in the reference field
        And the user selects the "locationDestination" bound reference field on the main page
        And the title of the reference field is displayed
        And the user writes "LOC002" in the reference field
        And the user waits 2 seconds
        And the value of the select field is "LOC002"
        And the "statusDestination" bound select field on the main page is hidden
        And the user clicks the "Next" labelled business action button on the main page

    Scenario: 04 Check the values of the card : LPN and location

        And the user selects the "licensePlateNumber" bound reference field on the main page
        And the user writes "" in the reference field
        And the user selects the "stockChangeLines" bound table field on the main page
        Then the value of the "licensePlateNumber" bound nested reference field of the card 1 in the table field is "ZAE25I-*********"
        And the value of the "location" bound nested reference field of the card 1 in the table field is "LOC002"

    Scenario: 05 Check the data of sidebar LPN and location, and also ensure that field statusDestination is hidden

        And the user clicks the "Edit line" dropdown action of the card 1 in the table field
        Then the "Stock change lines" titled page is displayed
        And the user selects the "stockChangeLines" bound table field on the main page
        And the value of the "licensePlateNumber" bound nested reference field of the card 1 in the table field is "ZAE25I-*********"
        And the value of the "location" bound nested reference field of the card 1 in the table field is "LOC002"
        And the "statusDestination" bound select field on the sidebar is hidden

    Scenario: 06 Change the location in the sidebar from LOC002 to LOC003

        # Update the script after bug X3-340623 has been fixed
        And the user selects the "location" bound reference field on the mobile sidebar
        And the user clicks the lookup button of the reference field
        And searches for "LOC003" in the lookup dialog
        And the user selects the "location" bound table field on a full width modal
        And the user clicks the card 1 in the table field
        And the user waits 2 seconds
        And the value of the reference field is "LOC003"
        And the user clicks the "Apply" button of the Message dialog
        And the user waits 2 seconds

    Scenario: 07 Check that the change have been populated in card

        And the user selects the "stockChangeLines" bound table field on the main page
        Then the value of the "licensePlateNumber" bound nested reference field of the card 1 in the table field is "ZAE25I-*********"
        And the value of the "location" bound nested reference field of the card 1 in the table field is "LOC003"

    Scenario: 08 Document creation

        And the user clicks the "createButton" bound business action button on the main page
        Then the text in the header of the dialog is "Success"
        And the user clicks the "OK" button of the Confirm dialog
        And the user clicks the close button in the navigation bar

    Scenario: 09 Logout

        When the user logs out of the system
