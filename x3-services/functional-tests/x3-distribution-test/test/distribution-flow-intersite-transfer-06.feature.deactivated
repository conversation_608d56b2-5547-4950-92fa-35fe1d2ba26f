@distribution
Feature: distribution-flow-intersite-transfer-06
    ###########################################################################
    # Header
    # -------------------------------------------------------------------------
    # - Test code : distribution-flow-intersite-transfer-06
    # - Description : IntersiteTransfer inventory case, Locked stock line
    # - Legislation : All legislations
    # - JIRA ID : X3-300446
    # - Created by : <PERSON><PERSON>
    # - Created date - 07-07-23
    # - Updated by :
    # - Updated date :
    ###########################################################################

    Scenario: 1 Select the endpoint

        Given the user opens the application on a mobile
        When the user selects the "param:endPointName1" endpoint
        Then the "param:endPointName1" endpoint is selected

    Scenario: 2 Set the site I-K35
        Given the user opens the application on a mobile
        When the user clicks the "Site" sticker in the navigation bar
        And the user selects the "Site" labelled reference field on a full width modal
        And the user clicks the lookup button of the reference field
        And searches for "I-K35" in the lookup dialog
        And the user selects the "selectionSite" bound reference field on a full width modal
        Then the value of the reference field is "I-K35"
        And the user clicks the "Set site" labelled business action button on a full width modal

    Scenario: 3 AdcIntersiteTransfer

        Given the user opens the application on a mobile using the following link: "@sage/x3-stock/MobileIntersiteTransfer"

        And  the user selects the "transaction" bound dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user writes "STD" in the dropdown-list field
        And the user selects "STD" in the dropdown-list field
        Then the value of the dropdown-list field is "STD"

        And the user selects the "siteDestination" bound reference field on the main page
        And the user clicks the lookup button of the reference field
        And searches for "I-K36" in the lookup dialog

        And the user selects the "product" bound reference field on the main page
        And the user writes "I-K070723" in the reference field

    Scenario: 4 check alert message

        And the user waits 3 seconds
        Then the text in the header of the dialog is "Warning"
        And the user waits 3 seconds
        Then the text in the body of the dialog is "Product blocked by count. Do you want to continue?"
        And the user clicks the "Yes" button of the Confirm dialog

    Scenario: 5 check that the table is empty

        And the user waits 3 seconds
        And the user selects the "stock" bound table field on the main page
        And the table field is empty
        And the user clicks the close button in the navigation bar

    Scenario: 6 Logout

        When the user logs out of the system
