import { ServerConfig } from '@sage/xtrem-shared';
import { X3ConfigManager } from '@sage/xtrem-x3-config-manager';
import * as fs from 'fs/promises';
import * as _ from 'lodash';
import * as fsp from 'path/posix';
import * as tls from 'tls';
import { logger } from './sync-execute';

export interface InternalServerConfig extends ServerConfig {
    sslShallowCopy?: tls.TlsOptions;
}

export class SyncAppTls {
    static extraCaPem: string[] = [];

    static initTlsLayer(): void {
        const oldCreateSecureContext = tls.createSecureContext;

        // Monkey patch tls.createSecureContext
        // This has two advantages:
        //   1. Being compatible with customer installation where NODE_EXTRA_CA_CERTS env variable is already defined
        //   2. Do not require a bootstrap process or script to set the NODE_EXTRA_CA_CERTS env variable
        //
        // see:
        //    https://github.com/nodejs/node/blob/a706342368ff7c05fc2ae6b55e9eaa47815785dd/lib/internal/tls/secure-context.js
        //    https://github.com/nodejs/node/blob/e46c680bf2b211bbd52cf959ca17ee98c7f657f5/test/parallel/test-tls-addca.js

        // cast to any because of read-only property error
        (tls as any).createSecureContext = (options?: tls.SecureContextOptions) => {
            const context = oldCreateSecureContext(options);

            this.extraCaPem.forEach(cert => {
                context.context.addCACert(cert.trim());
            });

            return context;
        };
    }

    static addCaFile(caFile: string, caFiles: string[]): void {
        if (caFile.startsWith('-----BEGIN ')) {
            logger.warn(`Ignoring CA file '${caFile}': it seems to be a PEM content`);
            return;
        }
        if (caFiles.includes(caFile)) {
            logger.warn(`Ignoring CA file '${caFile}': it has already been added`);
            return;
        }
        caFiles.push(caFile);
    }

    static async loadPemFromFile(file: string): Promise<string[]> {
        try {
            const content = (await fs.readFile(file, { encoding: 'ascii' })).replace(/\r\n/g, '\n');

            return (
                content.match(/-----BEGIN CERTIFICATE-----[A-Za-z0-9+/=\r\n]*-----END CERTIFICATE-----/g) || []
            ).map(cert => cert.trim());
        } catch (e) {
            logger.error(`Cannot load CA '${file}': ${e.message}`);
            return [];
        }
    }

    static async loadExtraCaCert(): Promise<void> {
        const tlsConfig = X3ConfigManager.current.security?.tls;
        const serverConfig = X3ConfigManager.current.server as InternalServerConfig;
        const sslServerConfig = serverConfig?.sslShallowCopy;
        const caFiles: string[] = [];
        if (tlsConfig?.extraCaFiles) {
            const extraCaFiles = Array.isArray(tlsConfig.extraCaFiles)
                ? tlsConfig.extraCaFiles
                : [tlsConfig.extraCaFiles];
            caFiles.push(...extraCaFiles);
        }
        if (sslServerConfig) {
            // if sslServerConfig.ca is a string, we are expecting a file path
            if (typeof sslServerConfig.ca === 'string') {
                this.addCaFile(sslServerConfig.ca, caFiles);
            }
            // if sslServerConfig.cert is defined we load the CA from the cert directory
            if (typeof sslServerConfig.cert === 'string') {
                const certDir = fsp.dirname(sslServerConfig.cert);
                // by convention, the CA file is named ca.crt
                const caFile = fsp.join(certDir, 'ca.crt');
                this.addCaFile(caFile, caFiles);
            }
        }

        // now we load the CA from the files to be able to add them during the secure context creation
        const extraPem: string[] = [];
        if (caFiles.length > 0) {
            await Promise.all(
                caFiles.map(async file => {
                    extraPem.push(...(await this.loadPemFromFile(file)));
                }),
            );
        }
        X3ConfigManager.emitter.emit('extraCa', caFiles);
        const quotedCaFiles = caFiles.map(f => `'${f}'`);
        logger.info(`loading ${extraPem.length} extra CA from: [${quotedCaFiles}]`);
        this.extraCaPem = extraPem;
    }

    static async loadExtraConfig(): Promise<void> {
        await this.loadExtraCaCert();
    }

    static registerTlsChangeListener(server: tls.Server, configPath: string, name: string): void {
        const message = `Replacing secure context of the ${name} server`;
        X3ConfigManager.emitter.addListener('tlsChange', (propertyPath: string) => {
            const sslContext = _.property(configPath)(X3ConfigManager.current);
            if (sslContext && propertyPath === configPath) {
                // Replace the secure context the server. Existing connections to the server are not interrupted.
                // see https://nodejs.org/dist/latest-v18.x/docs/api/tls.html#serversetsecurecontextoptions
                setTimeout(() => {
                    logger.info(message);
                    try {
                        server.setSecureContext(sslContext);
                        X3ConfigManager.emitter.emit('tlsApply', configPath, sslContext);
                    } catch (err) {
                        logger.error(`[FAILED] ${message}: ${err.message}`);
                    }
                }, 500);
            }
        });
    }

    static initialized = false;

    static async setupSecurity(): Promise<void> {
        if (X3ConfigManager.current == null) {
            throw new Error('Config must be loaded first');
        }

        if (this.initialized) {
            return;
        }
        this.initialized = true;

        // has already been loaded before the setup, so we can safely apply changes
        await this.loadExtraConfig();

        logger.info('Initializing security layer...');
        X3ConfigManager.emitter.addListener('loaded', () => {
            this.loadExtraConfig().catch(err => logger.error(err));
        });
        this.initTlsLayer();
        logger.info('Security layer initialized');
    }
}
