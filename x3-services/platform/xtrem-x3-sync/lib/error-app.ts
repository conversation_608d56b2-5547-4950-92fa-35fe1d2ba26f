import { Request, Response } from 'express';

function requestError(req: Request, res: Response, status: number, name: string): void {
    res.status(status).json({
        $diagnoses: [
            {
                $severity: 'error',
                $message: `${name}: ${req.method} ${req.url}`,
                $statusCode: status,
            },
        ],
    });
}

export const unauthorized = (req: Request, res: Response): void => {
    requestError(req, res, 401, 'unauthorized');
};

export const forbidden = (req: Request, res: Response): void => {
    requestError(req, res, 403, 'forbidden');
};

export const badRequest = (req: Request, res: Response): void => {
    requestError(req, res, 400, 'bad request');
};

export const notFoundApp = (req: Request, res: Response): void => {
    requestError(req, res, 404, 'not found');
};
