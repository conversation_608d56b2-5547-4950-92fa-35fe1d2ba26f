/** @module @sage/xtrem-x3-sync */
import { X3ConfigManager, X3XtremConfig } from '@sage/xtrem-x3-config-manager';
import { TableDefinition } from '@sage/xtrem-x3-sql';
import type { Express, Request, Response } from 'express';
import * as express from 'express';
import * as http from 'http';
import * as https from 'https';
import { AddressInfo } from 'net';
import * as tls from 'tls';
import { configMiddleware } from './config-middleware';
import { SyncAppTls } from './sync-app-tls';
import { ExecuteOptions, logger, SyncExecute } from './sync-execute';

export interface HttpEndpointConfig {
    port: number;
    description: string;
    ssl?: tls.TlsOptions;
}

export class SyncAppEndpoint {
    protected httpServer: http.Server | https.Server;

    protected constructor(private readonly config: HttpEndpointConfig) {}

    static getConfig(): HttpEndpointConfig {
        const config = X3ConfigManager.current;
        // If we are in a worker process allocate the port as 0 so that a dynamic port is allocated
        const port = config.server?.port ?? 8240;
        const ssl = config.server?.ssl;

        return { port, description: 'sync app', ssl };
    }

    /** The port number for the HTTP endpoint. */
    get port(): number {
        return this.config.port;
    }

    /** The description of the HTTP endpoint. */
    get description(): string {
        return this.config.description;
    }

    static get rootRoute() {
        return '/syncapi';
    }

    /**
     * Wraps an asynchronous request handler to catch errors and send a 500 response if an error occurs.
     * This is useful for ensuring that unhandled promise rejections do not crash the server.
     *
     * @param body The asynchronous request handler function.
     * @returns A wrapped request handler function that catches errors.
     */
    static wrap(body: (req: Request, res: Response) => Promise<void>): (req: Request, res: Response) => void {
        return (req, res): void => {
            const { folder } = req.params;

            if (!folder) {
                throw new Error('Missing folder name in URL');
            }

            res.locals.config.folderName = folder;

            body(req, res).catch(err => {
                logger.error(err);
                res.status(500).send();
            });
        };
    }

    // use this instead of querystring.stringify because we don't want keys with undefined values
    static params(obj: any): string {
        const strings = Object.keys(obj)
            .filter(k => obj[k])
            .map(k => `${k}=${encodeURIComponent(`${obj[k]}`)}`);
        return strings.length > 0 ? `?${strings.join('&')}` : '';
    }

    static arrayify(val: string | string[] | undefined): string[] | undefined {
        return val == null || Array.isArray(val) ? val : [val];
    }

    static getBaseUrl(req: Request, res: Response): string {
        const config = res.locals.config as X3XtremConfig;
        const folder = res.locals.config.folderName;

        const base = req.header('x-api-base-url') || config.baseUrl || '';

        const folderPart = folder ? `${this.rootRoute}/${folder}` : '';

        return `${base}${folderPart}`;
    }

    static async resolveMetadata(req: any, res: any): Promise<void> {
        logger.info(() => 'Resolving metadata for sync app endpoint');
        const config = res.locals.config as X3XtremConfig;
        const tables = await SyncExecute.listTables(config);
        const baseUrl = SyncAppEndpoint.getBaseUrl(req, res);

        res.json({
            tables: tables.map((def: TableDefinition) => ({
                name: def.tableName,
                columns: def.columns,
                indexes: def.indexes,
                isView: def.isView,
                url: `${baseUrl}/${def.tableName}`,
            })),
        });
    }

    static async resolveTableDataRequest(req: any, res: any): Promise<void> {
        const { table } = req.params;
        const q = req.query;
        const columns = q.columns ? Array.from(new Set((q.columns as string).split(',')).values()) : undefined;
        const options: ExecuteOptions = {
            since: q.since ? new Date(q.since) : undefined,
            until: q.until ? new Date(q.until) : undefined,
            key: q.key,
            countHint: q.countHint ? Number(q.countHint) : undefined,
            columns,
            where: SyncAppEndpoint.arrayify(q.where),
        };
        q.columns = columns?.join();
        const config = res.locals.config as X3XtremConfig;
        const baseUrl = SyncAppEndpoint.getBaseUrl(req, res);

        const result = await SyncExecute.execute(config, table, options);

        const nextUrl = `${baseUrl}/${table}${SyncAppEndpoint.params({
            ...q,
            since: result?.since && result?.since.toISOString(),
            until: result?.until && result?.until.toISOString(),
            key: result?.key,
        })}`;
        const countUrl = `${baseUrl}/${table}/count${SyncAppEndpoint.params({
            where: options.where,
            since: result.since && result.since.toISOString(),
            until: result.until && result.until.toISOString(),
            key: result.key,
        })}`;
        res.json({
            rows: result.rows,
            links: {
                next: {
                    url: nextUrl,
                },
                count: {
                    url: countUrl,
                },
            },
            done: result.done,
        });
    }

    /**
     * Configures the Express app with the necessary middleware, routes, etc.
     * This method should be implemented by subclasses to provide the specific configuration.
     *
     * @param expressApp The Express app instance.
     */
    static configureExpressApp(expressApp: Express): void {
        expressApp.use(configMiddleware);

        // ping and ready routes are used for health checks in Kubernetes and other orchestrators
        // They are not used for actual application logic, but rather to check if the service is running and ready to accept requests.
        // These routes need to be defined before any other routes that might handle the more generic paths.

        // Set a liveness HTTP endpoint that sends a simple 200 status to inform that the container is alive and healthy:
        // see: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-startup-probes/
        expressApp.get(`${SyncAppEndpoint.rootRoute}/ping`, (_req, res) => {
            res.json({});
        });

        // Set a readiness HTTP endpoint that:
        // - sends a simple 502 status when the container is being started
        // - sends a simple 200 status when container is ready to manager requests
        // see: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-startup-probes/
        expressApp.get(`${SyncAppEndpoint.rootRoute}/ready`, (_req, res) => {
            if (SyncAppEndpoint.isReady) res.json({});
            else res.status(502).send();
        });

        const rootRouteWithFolder = `${SyncAppEndpoint.rootRoute}/:folder`;

        expressApp.get(
            rootRouteWithFolder,
            SyncAppEndpoint.wrap(async (req: any, res: any) => {
                await SyncAppEndpoint.resolveMetadata(req, res);
            }),
        );

        expressApp.get(
            `${rootRouteWithFolder}/:table`,
            SyncAppEndpoint.wrap(async (req: any, res: any) => {
                await SyncAppEndpoint.resolveTableDataRequest(req, res);
            }),
        );

        expressApp.get(
            `${rootRouteWithFolder}/:table/count`,
            SyncAppEndpoint.wrap(async (req: any, res: any) => {
                const { table } = req.params;
                const q = req.query;
                const config = res.locals.config as X3XtremConfig;

                const result = await SyncExecute.rowCount(config, table, {
                    since: q.since ? new Date(q.since) : undefined,
                    where: q.where || undefined,
                    key: q.key,
                });
                res.json(result);
            }),
        );

        expressApp.get(
            `${rootRouteWithFolder}/:table/metadata`,
            SyncAppEndpoint.wrap(async (req: any, res: any) => {
                const { table } = req.params;
                const config = res.locals.config as X3XtremConfig;

                const result = await SyncExecute.getMetadata(config, table);
                res.json(result);
            }),
        );
    }

    static getApp(): Express {
        const expressApp = express() as Express;
        SyncAppEndpoint.configureExpressApp(expressApp);

        return expressApp;
    }

    private static isReady: boolean = false;

    static setReady(): void {
        this.isReady = true;
    }

    /**
     * Starts the HTTP server and listens for incoming requests.
     * If SSL is enabled, it creates an HTTPS server, otherwise it creates an HTTP server.
     * If running in a worker process, it allocates a dynamic port, otherwise it uses the configured port.
     *
     * @returns A Promise that resolves when the server has started successfully.
     */
    async start(): Promise<void> {
        const expressApp = SyncAppEndpoint.getApp();
        if (this.config?.ssl /* && this.config.encrypt */) {
            // eslint-disable-next-line @typescript-eslint/no-misused-promises
            this.httpServer = https.createServer(this.config.ssl, expressApp);
            SyncAppTls.registerTlsChangeListener(this.httpServer as https.Server, 'server.ssl', 'HTTPS');
        } else {
            // eslint-disable-next-line @typescript-eslint/no-misused-promises
            this.httpServer = http.createServer(expressApp);
        }

        // We set the exclusive option to true for a worker conflict as we do not want overlaps in the port allocations
        // to the server
        // If we are in a worker process allocate the port as 0 so that a dynamic port is allocated
        const { port } = this;

        await new Promise<void>((resolve, reject) => {
            const server = this.httpServer.listen({ port });
            server
                .on('listening', () => {
                    const serverPort = (server.address() as AddressInfo).port;
                    const tlsMesage = this.config.ssl ? ' over TLS' : '';

                    logger.info(`${this.description} started on port ${serverPort || this.port}${tlsMesage}`);
                    SyncAppEndpoint.setReady();
                    resolve();
                })
                .on('error', err => {
                    // eslint-disable-next-line no-console
                    console.error(`Cannot start ${this.description}: ${err.stack}`);
                    reject(err);
                });
        });
    }

    static create(): SyncAppEndpoint {
        return new SyncAppEndpoint(this.getConfig());
    }
}

export const SyncEndpointHooks = {
    setAuthenticationMiddlewares(expressApp: Express): void {
        if (!expressApp) throw new Error('expressApp not found');
        // expressApp.use(authMiddleware);
        expressApp.use(configMiddleware);
    },
};

function logApplicationInfo() {
    const config = X3ConfigManager.current;
    logger.info(`Starting sync application with configuration: ${X3ConfigManager.current.originFolder}`);

    logger.info(`Deployment mode is ${config.deploymentMode}`);
}

export async function startSyncApplication(): Promise<void> {
    logApplicationInfo();

    await SyncAppTls.setupSecurity();

    const syncHttpEndpoint = SyncAppEndpoint.create();

    await syncHttpEndpoint.start();
}
