import { getInvalidatedTokenInstance } from '@sage/xtrem-access-token';
import { Logger } from '@sage/xtrem-log';
import { X3XtremConfig } from '@sage/xtrem-x3-config-manager';

export interface JwtClaims {
    /**
     * "5f0b9648-bc74-4fe7-adbf-184fc7efc44a"
     */
    jti?: string;
    /**
     * "auth0|cd3..........."
     */
    auth0?: string;
    /**
     * "<EMAIL>"
     */
    sub?: string;
    /**
     * "<tenant-nanoid>"
     */
    tenantId?: string;
    /**
     * "<domain-of-the-cluster>"
     * example: "cluster-a.dev-sagextrem.com" or "*-ci-v2.eu.dev-sagextrem.com" when using bizapps"
     */
    aud?: string;
    /**
     * "<domain-of-authentication-service>"
     * example: login.dev-sagextrem.com
     */
    iss?: string;
    iat?: number;
    exp?: number;
    /**
     * OneTrust preferences https://jira.sage.com/browse/XT-10838
     */
    pref?: number;
    /**
     * Initial app the access token was created for
     */
    app?: string;
    /**
     * What are the apps for this tenant/user the access token is valid for (ex SDMO and SHOWCASE)
     */
    apps?: string[];
}

export const logger = Logger.getLogger(__filename, 'sync');
const isLocalEnv = (): boolean => !process.env.XTREM_ENV || process.env.XTREM_ENV === 'local';

export class TokenInvalidationService {
    private static _isStarted = false;

    // eslint-disable-next-line @typescript-eslint/no-empty-function
    private constructor() {}

    static start(config: X3XtremConfig): void {
        if (this._isStarted) {
            return;
        }
        if (isLocalEnv()) {
            logger.warn(
                "Cannot start token invalidation service: 'XTREM_ENV' environment variable not set or set to 'local'",
            );
            return;
        }
        if (config.security?.services?.tokenInvalidation?.active === false) {
            logger.warn('Cannot start token invalidation service: Deactivated by config');
            return;
        }
        if (!config.security?.loginUrl) {
            logger.error(
                "Cannot start token invalidation service: Login service must be configured - Missing 'loginUrl'",
            );
            return;
        }
        if (!config.clusterId) {
            logger.error('Cannot start token invalidation service: Cluster Id is missing');
            return;
        }

        try {
            // start fetching token list
            getInvalidatedTokenInstance().startListening({
                logger,
                tableName: process.env.XTREM_INVALIDATED_TOKEN_TABLE_NAME || 'invalidated-token-table-name',
                frequencyInSec: 15,
                cluster: config.clusterId!,
            });
            this._isStarted = true;
            logger.info('Token invalidation service started');
        } catch (e) {
            logger.error(`Cannot start token invalidation service: ${e.message}`);
        }
    }

    static stop(): void {
        if (!this._isStarted) {
            return;
        }
        getInvalidatedTokenInstance().stopListening();
        logger.info('Token invalidation service stopped');
    }

    static isInvalidatedToken(accessToken: JwtClaims): boolean {
        if (!this._isStarted) {
            // assume it has not been invalidated
            return false;
        }
        return !accessToken.jti || !getInvalidatedTokenInstance().isJTIValid(accessToken.jti);
    }
}
