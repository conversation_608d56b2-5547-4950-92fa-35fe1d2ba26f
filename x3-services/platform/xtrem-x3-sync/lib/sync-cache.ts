import { AnyValue, AsyncResponse } from '@sage/xtrem-async-helper';
import { Dict } from '@sage/xtrem-shared';

export class SyncCache<T extends AnyValue> {
    private entries = {} as Dict<T>;

    async get(key: any, fn: () => AsyncResponse<T>): Promise<T> {
        const k = JSON.stringify(key);
        if (!this.entries[k]) this.entries[k] = await fn();
        return this.entries[k];
    }

    clear(): void {
        this.entries = {};
    }
}
