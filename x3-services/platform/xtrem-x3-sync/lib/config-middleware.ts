import { Logger } from '@sage/xtrem-log';
import { X3Config, X3ConfigManager, X3XtremConfig } from '@sage/xtrem-x3-config-manager';
import { Handler, NextFunction, Request, Response } from 'express';

const logger = Logger.getLogger(__filename, 'config');

function processLocalConfig(res: Response): X3XtremConfig {
    res.locals.config = { ...X3ConfigManager.current };

    return res.locals.config;
}

export function processSyncConfig(res: Response): void {
    processLocalConfig(res);
    const x3Config = res.locals.config.x3 as X3Config;

    res.locals.referenceFolder = x3Config.development?.referenceFolder ?? x3Config.development?.folderName;
}

export const configMiddleware: Handler = (req: Request, res: Response, next: NextFunction) => {
    if (/^\/(ping|ready|login-service)$/.test(req.path)) {
        next();
        return;
    }
    try {
        logger.info(() => `Processing config for ${req.path} with method: ${req.method}`);

        processSyncConfig(res);

        next();
    } catch (error) {
        res.status(400).json({ error: error.message });
    }
};
