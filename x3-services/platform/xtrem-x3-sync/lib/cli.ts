import { X3ConfigManager, X3XtremConfig } from '@sage/xtrem-x3-config-manager';
import { performance } from 'perf_hooks';
import { startSyncApplication } from './sync-app-endpoint';
import { logger } from './sync-execute';

const dir = process.cwd();
function loadConfig(): X3XtremConfig {
    return X3ConfigManager.load(dir);
}

export async function runCli() {
    if (process.argv.includes('--version') || process.argv.includes('-v')) {
        // eslint-disable-next-line global-require
        logger.info(`Version: ${require('../../package.json').version}`);
        process.exit(0);
    }

    try {
        const timings = {
            start: performance.now(),
            total: 0,
        };
        loadConfig();
        await startSyncApplication();
        timings.total = performance.now() - timings.start;

        logger.info(`Sync application start completed in ${timings.total.toFixed(2)} ms`);
    } catch (error) {
        logger.error(`Error starting sync application: ${error.stack ?? error.message}`);
        process.exit(1);
    }
}
