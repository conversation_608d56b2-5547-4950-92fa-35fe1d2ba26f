import type {
    ColumnDefinition,
    Dialect,
    IndexDefinition,
    SqlReadTableSchemaOptions,
    TableDefinition,
} from '@sage/xtrem-x3-sql';
import { Pool } from '@sage/xtrem-x3-sql';
import { createHash } from 'crypto';
import * as _ from 'lodash';

export interface Dict<T> {
    [key: string]: T;
}

export class SyncSqlResolver {
    private static readOracleTablesAndViews(pool: Pool, schemaName: string): Promise<TableDefinition[]> {
        const sql = `SELECT a.TABLE_NAME, 'TABLE' OBJECT_TYPE
                    FROM ALL_TABLES a
                    JOIN ${pool.getFullTableName(schemaName, 'ATABLE', 't')} ON t.CODFIC_0 = a.TABLE_NAME
                    WHERE OWNER = ${pool.param(0)}
                    UNION ALL
                    SELECT a.VIEW_NAME AS TABLE_NAME, 'VIEW' OBJECT_TYPE
                    FROM ALL_VIEWS a
                    JOIN ${pool.getFullTableName(schemaName, 'AVIEW', 't')} ON t.CODVUE_0 = a.VIEW_NAME
                    WHERE OWNER = ${pool.param(0)}
                    ORDER BY TABLE_NAME`;
        return pool.withConnection(cnx =>
            pool
                .createReader<{ TABLE_NAME: string; OBJECT_TYPE: string }>(cnx, sql, [schemaName])
                .map(item => {
                    return {
                        schemaName: schemaName.toUpperCase(),
                        tableName: item.TABLE_NAME.toUpperCase(),
                        isView: item.OBJECT_TYPE === 'VIEW',
                    };
                })
                .readAll(),
        );
    }

    private static readSqlServerTablesAndViews(pool: Pool, schemaName: string): Promise<TableDefinition[]> {
        const sql = `SELECT o.name, 'TABLE' object_type
                    FROM sys.objects o
                    JOIN ${pool.getFullTableName(schemaName, 'ATABLE', 't')} ON t.CODFIC_0 = o.name
                    LEFT JOIN sys.schemas s ON o.schema_id = s.schema_id
                    WHERE o.type = 'U'
                    AND s.name = ${pool.param(0)}
                    UNION ALL
                    SELECT o.name, 'VIEW' object_type
                    FROM sys.objects o
                    JOIN ${pool.getFullTableName(schemaName, 'AVIEW', 't')} ON t.CODVUE_0 = o.name
                    LEFT JOIN sys.schemas s ON o.schema_id = s.schema_id
                    WHERE o.type = 'V'
                    AND s.name = ${pool.param(0)}
                    ORDER BY o.name`;
        return pool.withConnection(cnx =>
            pool
                .createReader<{ name: string; object_type: string }>(cnx, sql, [schemaName])
                .map(item => {
                    return {
                        schemaName: schemaName.toUpperCase(),
                        tableName: item.name.toUpperCase(),
                        isView: item.object_type === 'VIEW',
                    };
                })
                .readAll(),
        );
    }

    private static readPostgresTablesAndViews(pool: Pool, schemaName: string): Promise<TableDefinition[]> {
        const sql = `SELECT UPPER(s.table_name) "table_name", 'TABLE' "object_type"
                    FROM information_schema.tables s
                    JOIN ${pool.getFullTableName(schemaName, 'ATABLE', 't')} ON t.CODFIC_0 = UPPER(s.table_name)
                    WHERE LOWER(s.table_schema) = LOWER(${pool.param(0)})
                    AND s.table_type = 'BASE TABLE'
                    UNION ALL
                    SELECT UPPER(s.table_name) "table_name", 'VIEW' "object_type"
                    FROM information_schema.views s
                    JOIN ${pool.getFullTableName(schemaName, 'AVIEW', 't')} ON t.CODVUE_0 = UPPER(s.table_name)
                    WHERE LOWER(s.table_schema) = LOWER(${pool.param(0)})
                    ORDER BY "table_name"`;
        return pool.withConnection(cnx =>
            pool
                .createReader<{ table_name: string; object_type: string }>(cnx, sql, [schemaName])
                .map(item => {
                    return {
                        schemaName: schemaName.toUpperCase(),
                        tableName: item.table_name.toUpperCase(),
                        isView: item.object_type === 'VIEW',
                    };
                })
                .readAll(),
        );
    }

    static readTablesAndViews(pool: Pool, schemaName: string): Promise<TableDefinition[]> {
        switch (pool.dialect) {
            case 'oracle':
                return this.readOracleTablesAndViews(pool, schemaName);
            case 'sqlServer':
                return this.readSqlServerTablesAndViews(pool, schemaName);
            case 'postgres':
                return this.readPostgresTablesAndViews(pool, schemaName);
            default:
                throw new Error(`Unsupported database dialect: ${pool.dialect}`);
        }
    }

    static readTableSchema(
        pool: Pool,
        schemaName: string,
        tableName: string,
        options: SqlReadTableSchemaOptions,
    ): Promise<TableDefinition> {
        return pool.readTableSchema(schemaName, tableName, options);
    }

    static metadataEtag: string;

    static lastMetadataEtagCheck: Date;

    static async verifyMetadataEtag(pool: Pool, schemaName: string): Promise<boolean> {
        if (this.metadataEtag && Date.now() - (this.lastMetadataEtagCheck?.getTime() || 0) < 1000 * 60 * 5) {
            // Check if the last check was less than 5 minutes ago
            return true;
        }

        const etag = String(
            await pool.withConnection(async cnx => {
                const result = await pool.execute<{ ETAG: string }[]>(
                    cnx,
                    `SELECT MAX(UPDDATTIM_0) ${this.makeColumnAlias('ETAG', pool.dialect)}
                    FROM ${pool.getFullTableName(schemaName, 'ATABZON')}
                    UNION ALL
                    SELECT MAX(UPDDATTIM_0) ${this.makeColumnAlias('ETAG', pool.dialect)}
                    FROM ${pool.getFullTableName(schemaName, 'AVIEWD')}`,
                );
                return result && result.length > 0 ? result.map(r => r.ETAG).join() : '';
            }),
        );

        this.lastMetadataEtagCheck = new Date();
        if (this.metadataEtag === etag) {
            return true;
        }
        this.metadataEtag = etag;

        return false;
    }

    static definitions: Dict<Dict<Promise<TableDefinition>>> = {};

    static async fetchDefintion(pool: Pool, schemaName: string, name: string): Promise<TableDefinition> {
        if (
            this.definitions[schemaName] != null &&
            this.definitions[schemaName][name] != null &&
            (await SyncSqlResolver.verifyMetadataEtag(pool, schemaName))
        ) {
            return this.definitions[schemaName][name];
        }

        if (!this.definitions[schemaName]) this.definitions[schemaName] = {};

        this.definitions[schemaName][name] = SyncSqlResolver.readTableSchema(pool, schemaName, name, {
            skipSecurity: true,
            skipSequences: true,
        }).then(async def => {
            await SyncSqlResolver.verifyMetadataEtag(pool, schemaName);
            return {
                schemaName: def.schemaName,
                tableName: def.tableName,
                columns: def.columns?.map(col => _.omit(col, ['default'])) as ColumnDefinition[],
                indexes: def.indexes?.map(col => _.omit(col, ['isPrimaryKey'])) as IndexDefinition[],
                isView: def.isView,
            } as TableDefinition;
        });

        return this.definitions[schemaName][name];
    }

    static getDefinition(pool: Pool, schemaName: string, name: string): Promise<TableDefinition> {
        return SyncSqlResolver.fetchDefintion(pool, schemaName, name);
    }

    static makeName30(name: string): string {
        if (name.length <= 30) return name;
        const prefix = name.substring(0, 5);
        const suffix = name.substring(name.length - 10, name.length);
        const hash = createHash('sha256').update(name).digest('hex').replace(/\s/g, '').substring(0, 15);
        return `${prefix}${hash}${suffix}`.toUpperCase();
    }

    static makeColumnAlias(sql: string, dialect: Dialect, options?: { alias?: string; noQuotes?: boolean }): string {
        if (dialect === 'postgres' && !options?.noQuotes) {
            return `"${SyncSqlResolver.makeName30(
                (options?.alias ?? sql)
                    .split('._')
                    .map(s => _.snakeCase(s))
                    .join('__')
                    .toUpperCase(),
            )}"`;
        }
        return SyncSqlResolver.makeName30(
            (options?.alias ?? sql)
                .split('._')
                .map(s => _.snakeCase(s))
                .join('__')
                .toUpperCase(),
        );
    }
}
