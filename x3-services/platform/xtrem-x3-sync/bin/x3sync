#!/usr/bin/env node

const path = require('node:path');
const fs = require('node:fs');
const os = require('node:os');

const root = os.platform() === 'win32' ? process.cwd().split(path.sep)[0] + '\\' : '/';

function requireFrom(fromDir) {
    while (fromDir !== root) {
        const localExecutable = path.resolve(
            fromDir,
            'node_modules',
            '@sage',
            'xtrem-x3-sync',
            'build',
            'lib',
            'cli.js',
        );

        if (fs.existsSync(localExecutable)) {
            return require(localExecutable);
        } else {
            fromDir = path.dirname(fromDir);
        }
    }
    return undefined;
}

function getCli() {
    let cli = requireFrom(__dirname);

    if (cli) {
        return cli;
    }

    cli = requireFrom(process.cwd());
    if (cli) {
        return cli;
    }

    return require('../build/lib/cli');
}

const cli = getCli();

cli.runCli();
