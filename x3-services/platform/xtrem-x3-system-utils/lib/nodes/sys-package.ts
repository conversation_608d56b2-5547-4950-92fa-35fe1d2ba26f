import { decorators, Node } from '@sage/xtrem-core';
import { X3StorageManager } from '@sage/xtrem-x3-gateway';
import * as x3SystemUtils from '..';

/** 4GL code: FACILITY */
@decorators.node<SysPackage>({
    storage: 'external',
    tableName: 'APACKAGE',
    keyPropertyNames: ['name'],
    externalStorageManager: new X3StorageManager(),
    isCached: true,
    canRead: true,
    canSearch: true,
    isPublished: true,
    indexes: [
        {
            orderBy: {
                name: 1,
            },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
})
export class SysPackage extends Node {
    @decorators.stringProperty<SysPackage, 'name'>({
        dataType: () => x3SystemUtils.datatypes.genericDataTypes.textDatatype,
        isNotEmpty: true,
        isStored: true,
        isPublished: true,
        columnName: 'APACK',
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<SysPackage, 'description'>({
        dataType: () => x3SystemUtils.datatypes.genericDataTypes.textDatatype,
        isStored: true,
        isPublished: true,
        columnName: 'ADES',
    })
    readonly description: Promise<string>;

    @decorators.booleanProperty<SysPackage, 'isActive'>({
        isStored: true,
        isPublished: true,
        columnName: 'ENAFLG',
    })
    readonly isActive: Promise<boolean>;
}
