import * as AWS from '@aws-sdk/client-ssm';
import { ConfigManager } from '@sage/xtrem-config';
import { Logger } from '@sage/xtrem-log';
import { Config, SecurityConfig } from '@sage/xtrem-shared';
import * as crypto from 'crypto';
import { EventEmitter } from 'events';

const logger = Logger.getLogger(__filename, 'config');

export interface X3SecurityConfig extends SecurityConfig {
    syracuse?: {
        clientId: string;
        secret: string;
        clockToleranceSeconds?: number;
    };
}

export interface X3ServiceAgentConfig {
    traceInterval: number;
}

export interface X3SqlConfig {
    driver: string;
    hostname: string;
    port: number;
    database: string;
    user: string;
    password: string;
    poolSize: number;
}

export interface X3ApiConfig {
    url: string;
    secret: string;
    userCredentials: X3UserCredentialsConfig;
    origin?: string;
}

export interface X3SoapConfig {
    webServiceURL: string;
    userCredentials: X3UserCredentialsConfig;
    codeLang: string;
    poolAlias: string;
    timeout: number;
}
export interface X3UserCredentialsConfig {
    userName: string;
    password: string;
}

export interface X3DevelopmentConfig {
    folderName: string;
    referenceFolder: string;
    defaultLanguage: string;
    soap: X3SoapConfig;
    codeLang: string;
    poolAlias: string;
    timeout: number;
    api: X3ApiConfig;
    multiWorkerServiceDisabled?: boolean;
}

export interface X3Config {
    isCloud: boolean;
    agent?: X3ServiceAgentConfig;
    sql?: X3SqlConfig;
    development?: X3DevelopmentConfig;
    sync?: X3SyncConfig;
}

export interface X3SyncConfig {
    maxPageSize?: number;
}

export interface X3XtremConfig extends Config {
    security: X3SecurityConfig;
    x3?: X3Config;
    folderName?: string;
}

export class X3ConfigManager {
    static #secret: string;

    static #clientId: string;

    static get current(): X3XtremConfig {
        return ConfigManager.current as X3XtremConfig;
    }

    static #ssmClient: AWS.SSM | undefined;

    static getRegion(): string {
        return process.env.AWS_REGION ?? ConfigManager.current?.aws?.region ?? 'eu-west-1';
    }

    static get ssmClient(): AWS.SSM {
        if (this.#ssmClient) {
            return this.#ssmClient;
        }
        // TODO: Should check if region is set in ConfigManager ??
        logger.verbose(() => `Creating SSM client for region: ${this.getRegion()}`);
        this.#ssmClient = new AWS.SSM({ region: this.getRegion() });
        return this.#ssmClient;
    }

    private static async getSsmValue(ssmParamPath: string): Promise<string> {
        try {
            logger.verbose(() => `Getting SSM parameter ${ssmParamPath}`);
            const paramResponse = await this.ssmClient.getParameter({
                Name: ssmParamPath,
                WithDecryption: true,
            });
            if (!paramResponse.Parameter || !paramResponse.Parameter.Value) {
                throw new Error(`Parameter ${ssmParamPath} not found or has no value`);
            }
            logger.verbose(() => `Returning SSM parameter value from path ${ssmParamPath}.`);
            return paramResponse.Parameter.Value;
        } catch (err: any) {
            if (err.name === 'ParameterNotFound') {
                logger.error(`SSM parameter not found: ${ssmParamPath}`);
            }
            throw new Error(`Error getting parameter ${ssmParamPath}`);
        }
    }

    private static async setClientIdSsmValue() {
        logger.verbose(() => 'Setting clientId from SSM parameter');
        const param = this.current.security.syracuse?.clientId ?? '';
        this.#clientId = await this.getSsmValue(param.replace('@secret:ssm', ''));
    }

    static async getClientId(): Promise<string> {
        logger.verbose(() => 'Retrieving client id.');
        if (this.#clientId) {
            logger.verbose(() => 'Client id already set, returning cached value.');
            return this.#clientId;
        }
        if (this.isCloud() && this.current.security.syracuse?.clientId.startsWith('@secret:ssm')) {
            logger.verbose(() => 'Cloud environment detected, retrieving client id from SSM.');
            await this.setClientIdSsmValue();
            return this.#clientId;
        }
        if (!this.current.security.syracuse?.clientId) {
            throw new Error('xtrem.security.syracuse.clientId config value must be set');
        }
        logger.verbose(() => 'Using clientId from current security syracuse config.');
        this.#clientId = this.current.security.syracuse.clientId;
        return this.#clientId;
    }

    private static async setSecretSsmValue() {
        logger.verbose(() => 'Setting secret from SSM parameter');
        const param = this.current.security.syracuse?.secret ?? '';
        this.#secret = await this.getSsmValue(param.replace('@secret:ssm', ''));
    }

    static async getSecret(): Promise<string> {
        logger.verbose(() => 'Retrieving secret.');
        if (this.#secret) {
            logger.verbose(() => 'Secret already set, returning cached value.');
            return this.#secret;
        }
        if (this.isCloud() && this.current.security.syracuse?.secret.startsWith('@secret:ssm')) {
            logger.verbose(() => 'Cloud environment detected, retrieving secret from SSM.');
            await this.setSecretSsmValue();
            return this.#secret;
        }
        if (!this.current.security.syracuse?.secret) {
            throw new Error('xtrem.security.secret config value must be set');
        }
        logger.verbose(() => 'Using secret from current security syracuse config.');
        this.#secret = this.current.security.syracuse.secret;
        return this.#secret;
    }

    static isCloud(): boolean {
        logger.verbose(() => 'Checking if the current environment is cloud.');
        const isCloud = this.current.x3?.isCloud ?? false;
        logger.verbose(() => `Current environment is ${isCloud ? 'cloud' : 'not cloud'}.`);
        return isCloud;
    }

    static notLoadedError(): Error {
        return new Error('Config not loaded!');
    }

    static load(dir: string): X3XtremConfig {
        return ConfigManager.load(dir) as X3XtremConfig;
    }

    static get emitter(): EventEmitter {
        return ConfigManager.emitter;
    }

    /**
     * encrypt using shared secret
     * @param data
     * @returns
     */
    static async encrypt(data: any, options?: { secret?: string }): Promise<{ iv: string; encrypted: string }> {
        logger.verbose(() => 'Encrypt data');
        logger.verbose(() =>
            options?.secret
                ? 'Secret provided in options, using it for encryption'
                : 'No secret provided in options, call getSecret() to retrieve it for encryption',
        );
        const secret = options?.secret ?? (await X3ConfigManager.getSecret());
        // no security is configured so return the text AS IS without encryption
        if (!secret) return { iv: '', encrypted: typeof data === 'string' ? data : JSON.stringify(data) };
        const key = crypto.createHash('sha256').update(secret).digest('base64').substr(0, 32);
        const iv = crypto.randomBytes(16);
        const cipher = crypto.createCipheriv('aes-256-cbc', Buffer.from(key), iv);
        let encrypted = cipher.update(typeof data === 'string' ? data : JSON.stringify(data));
        encrypted = Buffer.concat([encrypted, cipher.final()]);
        // iv does not need to be be secret, see:
        // https://security.stackexchange.com/questions/122274/why-does-iv-not-need-to-be-secret-in-aes-cbc-encryption
        return { iv: iv.toString('hex'), encrypted: encrypted.toString('hex') };
    }

    static base64EncodedCheck(b64: string): void {
        if (!/^([A-Za-z0-9+/]{4})*([A-Za-z0-9+/]{3}=|[A-Za-z0-9+/]{2}==)?$/.test(b64))
            throw new Error('Input must be a base64 encoded string');
    }

    static async decrypt(b64: string, options?: { secret?: string }): Promise<any> {
        logger.verbose(() => 'Decrypting data');
        logger.verbose(() =>
            options?.secret
                ? 'Secret provided in options, using it for decryption'
                : 'No secret provided in options, call getSecret() to retrieve it for decryption',
        );
        const secret = options?.secret ?? (await X3ConfigManager.getSecret());
        this.base64EncodedCheck(b64);
        const encrypted = JSON.parse(Buffer.from(b64, 'base64').toString());
        if (!encrypted.iv && !encrypted.encrypted) return encrypted;
        const key = crypto.createHash('sha256').update(String(secret)).digest('base64').substr(0, 32);
        const iv = Buffer.from(encrypted.iv, 'hex');
        const encryptedText = Buffer.from(encrypted.encrypted, 'hex');
        const decipher = crypto.createDecipheriv('aes-256-cbc', Buffer.from(key), iv);
        let decrypted = decipher.update(encryptedText);
        decrypted = Buffer.concat([decrypted, decipher.final()]);
        return JSON.parse(decrypted.toString());
    }
}
