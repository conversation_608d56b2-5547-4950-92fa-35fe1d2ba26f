{"extends": "../tsconfig-base.json", "compilerOptions": {"outDir": "build", "rootDir": ".", "baseUrl": "."}, "include": ["index.ts", "application.ts", "lib/**/*", "test/**/*"], "exclude": ["lib/pages/**/*", "lib/widgets/**/*", "lib/stickers/**/*", "lib/i18n/**/*", "**/*.feature", "**/*.png"], "references": [{"path": "../../../platform/back-end/xtrem-config"}, {"path": "../../../platform/back-end/xtrem-log"}, {"path": "../../../platform/shared/xtrem-shared"}, {"path": "../../../platform/back-end/eslint-plugin-xtrem"}, {"path": "../../../platform/back-end/xtrem-minify"}]}