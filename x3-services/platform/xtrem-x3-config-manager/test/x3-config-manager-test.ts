import { assert } from 'chai';
import { X3ConfigManager } from '../lib';

X3ConfigManager.load(__dirname);
const currentConfig = X3ConfigManager.current;
const { security } = currentConfig;
const { secret } = security?.syracuse || { secret: 'secret' };

const testConfig = {
    folderName: 'testFolder',
    session: {
        userName: 'user',
        login: 'login',
    },
    xtremServiceUrl: 'http://serviceurl.com',
    sql: {
        driver: 'oracle',
        hostname: 'testServer',
        database: 'testDb',
    },
};

describe('Decrypt', () => {

    it('throws an error if value is not base64 encoded', () => {
        assert.throw(() => X3ConfigManager.base64EncodedCheck(JSON.stringify(testConfig)), 'Input must be a base64 encoded string');
    });
    it('returns decoded object if iv and encrypted is not present', async () => {
        const encoded = Buffer.from(JSON.stringify({})).toString('base64');
        const decrypted = await X3ConfigManager.decrypt(encoded, { secret });
        assert.isObject(decrypted, 'Decrypted value is not an object');
        assert.notEqual(decrypted, encoded);
    });
    it('decrypts the encrypted header value', async () => {
        const decrypted = await X3ConfigManager.decrypt(Buffer.from(JSON.stringify(await X3ConfigManager.encrypt(testConfig, { secret }))).toString('base64'), { secret });
        assert.isObject(decrypted, 'Decrypted value is not an object');
        const { sql } = decrypted;
        const { driver, hostname, database } = sql;
        assert.equal(driver, testConfig.sql.driver, 'Decrypted value is not the same as original');
        assert.equal(hostname, testConfig.sql.hostname, 'Decrypted value is not the same as original');
        assert.equal(database, testConfig.sql.database, 'Decrypted value is not the same as original');
    });
});
