import { date } from '@sage/xtrem-date-time';
import * as ui from '@sage/xtrem-ui';
import type { GraphiqlPluginProperties } from '@sage/xtrem-ui-plugin-graphiql';
import { InsightTypeType, SysInsight as SysInsightApi } from '@sage/xtrem-x3-copilot-api';
import { SysPackage } from '@sage/xtrem-x3-system-utils-api';

@ui.decorators.page<SysInsight, SysInsightApi>({
    title: 'Insights',
    module: 'xtrem-x3-copilot',
    mode: 'default',
    priority: 100,
    idField() {
        return `${this.package}/${this.screenId}/${this.key}`;
    },
    node: '@sage/xtrem-x3-copilot/SysInsight',
    createAction() {
        return this.$standardNewAction;
    },
    headerQuickActions() {
        return [this.$standardDuplicateAction];
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction, this.saveToDisk];
    },
    headerDropDownActions() {
        return [this.$standardDeleteAction, this.validateScreenQueries];
    },
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'screenId' }),
            titleRight: ui.nestedFields.label({ bind: 'key' }),
            line2: ui.nestedFields.reference({
                bind: 'package',
                valueField: 'name',
                node: '@sage/xtrem-x3-system-utils/SysPackage',
            }),
            line3: ui.nestedFields.text({ bind: 'type' }),
        },
    },
})
export class SysInsight extends ui.Page {
    @ui.decorators.pageAction<SysInsight>({
        title: 'Save to Disk',
        async onClick() {
            await this.$.graph.node('@sage/xtrem-x3-copilot/SysInsight').mutations.saveToDisk(true).execute();
        },
    })
    saveToDisk: ui.PageAction;

    @ui.decorators.pageAction<SysInsight>({
        title: 'Validate queries',
        async onClick() {
            if (this.$.isDirty) {
                throw new Error('Please save the page before validating queries.');
            }
            this.chatContextSection.isHidden = false;
            if (!this.chatContext.value) {
                this.chatContext.value = JSON.stringify({
                    userLocale: this.$.locale,
                    userCurrentDate: date.today(Intl.DateTimeFormat().resolvedOptions().timeZone).toString(),
                    nodeName: '@sage/xtrem-x3-master-data/Customer',
                    recordFilter: { _id: 'FOO' },
                    screenId: this.screenId.value,
                    screenTitle: this.screenId.value,
                });
            }

            await this.$.dialog.custom('info', this.chatContextSection, {
                dialogTitle: 'Chat context',
            });
            this.chatContextSection.isHidden = true;

            ui.console.log('Chat context:', this.chatContext.value);

            const response = await this.$.graph
                .node('@sage/xtrem-x3-copilot/SysCopilot')
                .queries.getInsightDataQueries(
                    {
                        data: true,
                        queries: {
                            key: true,
                            query: { value: true },
                        },
                    },
                    { chatContext: this.chatContext.value },
                )
                .execute();

            ui.console.log('data:', response.data);

            const insightData: any = {
                data: {
                    data: JSON.parse(response.data),
                },
            };

            const errors: string[] = [];

            if (insightData.data.data.errors) {
                errors.push(`Error in main data query: ${insightData.data.data.errors}`);
            }

            for (let query of response.queries) {
                const queryResponse = await this.$.graph.raw(query.query.value);
                insightData[query.key] = {
                    data: queryResponse.data,
                };
                ui.console.log(`Query ${query.key} response:`, queryResponse.data);
                if (queryResponse.errors) {
                    errors.push(`Error in query ${query.key}: ${queryResponse.errors}`);
                }
            }

            if (errors.length > 0) {
                await this.$.dialog.message('error', 'Validation Failed', errors.join('\n'));
            } else {
                await this.$.dialog.message('success', 'Validation passed', 'All queries validated successfully.');
            }
        },
    })
    private _validateScreenQueries: ui.PageAction;
    public get validateScreenQueries(): ui.PageAction {
        return this._validateScreenQueries;
    }
    public set validateScreenQueries(value: ui.PageAction) {
        this._validateScreenQueries = value;
    }

    @ui.decorators.section({
        isTitleHidden: true,
        title: 'Basic details',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block({
        parent() {
            return this.mainSection;
        },
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.section({
        isTitleHidden: true,
        title: 'Data query',
    })
    queryTemplateSection: ui.containers.Section;

    @ui.decorators.section({
        isTitleHidden: true,
        isHidden: true,
        title: 'Chat context',
    })
    chatContextSection: ui.containers.Section;

    @ui.decorators.block({
        parent() {
            return this.chatContextSection;
        },
    })
    chatContextBlock: ui.containers.Block;

    @ui.decorators.textAreaField({
        parent() {
            return this.chatContextBlock;
        },
        title: 'Chat context',
        isTransient: true,
    })
    chatContext: ui.fields.TextArea;

    @ui.decorators.referenceField<SysInsight, SysPackage>({
        columns: [
            ui.nestedFields.text({ bind: '_id', title: 'ID' }),
            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
            ui.nestedFields.text({ bind: 'description', title: 'Description' }),
        ],
        helperTextField: 'name',
        node: '@sage/xtrem-x3-system-utils/SysPackage',
        orderBy: { _id: 1 },
        parent() {
            return this.mainBlock;
        },
        title: 'Package',
        valueField: 'description',
        width: 'medium',
    })
    package: ui.fields.Reference<SysPackage>;

    @ui.decorators.textField({
        parent() {
            return this.mainBlock;
        },
        title: 'Screen ID',
        isMandatory: true,
        maxLength: 180,
    })
    screenId: ui.fields.Text;

    @ui.decorators.textField({
        parent() {
            return this.mainBlock;
        },
        title: 'Key',
        isMandatory: true,
        maxLength: 180,
    })
    key: ui.fields.Text;

    @ui.decorators.dropdownListField({
        parent() {
            return this.mainBlock;
        },
        title: 'Type',
        optionType: '@sage/xtrem-x3-copilot/InsightTypeType',
    })
    type: ui.fields.DropdownList<InsightTypeType>;

    @ui.decorators.pluginField<SysInsight, GraphiqlPluginProperties>({
        parent() {
            return this.queryTemplateSection;
        },
        title: 'Data query',
        helperText: 'Write a query against the database.',
        isFullWidth: true,
        pluginPackage: '@sage/xtrem-ui-plugin-graphiql',
    })
    query: ui.fields.Plugin<GraphiqlPluginProperties>;
}
