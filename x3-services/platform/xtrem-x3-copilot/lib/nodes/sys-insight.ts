import { Context, decorators, Node, Reference, TextStream, useDefaultValue } from '@sage/xtrem-core';
import * as x3SystemUtils from '@sage/xtrem-x3-system-utils';
import * as x3Copilot from '..';
import { InsightStorageManager } from '../classes/insight-storage-manager';

@decorators.node<SysInsight>({
    isPublished: true,
    canRead: true,
    canSearch: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDuplicate: true,
    storage: 'external',
    externalStorageManager: new InsightStorageManager(),
    keyPropertyNames: ['package', 'screenId', 'key'],
    indexes: [
        {
            orderBy: {
                hash: 1,
            },
            isUnique: true,
            isNaturalKey: true,
        },
        {
            orderBy: {
                package: 1,
                screenId: 1,
                key: 1,
            },
            isUnique: true,
        },
    ],
})
export class SysInsight extends Node {
    @decorators.stringProperty<SysInsight, 'hash'>({
        dataType: () => x3SystemUtils.datatypes.genericDataTypes.textDatatype,
        isNotEmpty: true,
        isStored: true,
        isPublished: true,
        dependsOn: ['screenId', 'type', 'key', 'query', 'package'],
        duplicatedValue: useDefaultValue,
        async defaultValue() {
            // eslint-disable-next-line @sage/xtrem/property-decorators-warnings
            return (await InsightStorageManager.getInsightFromNode(this)).hash;
        },
    })
    readonly hash: Promise<string>;

    @decorators.referenceProperty<SysInsight, 'package'>({
        isStored: true,
        columnType: 'string',
        node: () => x3SystemUtils.nodes.SysPackage,
        isPublished: true,
        duplicateRequiresPrompt: true,
    })
    readonly package: Reference<x3SystemUtils.nodes.SysPackage>;

    @decorators.stringProperty<SysInsight, 'screenId'>({
        dataType: () => x3SystemUtils.datatypes.genericDataTypes.textDatatype,
        isNotEmpty: true,
        isStored: true,
        isPublished: true,
        duplicateRequiresPrompt: true,
    })
    readonly screenId: Promise<string>;

    @decorators.stringProperty<SysInsight, 'key'>({
        dataType: () => x3SystemUtils.datatypes.genericDataTypes.textDatatype,
        isNotEmpty: true,
        isStored: true,
        isPublished: true,
        duplicateRequiresPrompt: true,
    })
    readonly key: Promise<string>;

    @decorators.enumProperty<SysInsight, 'type'>({
        dataType: () => x3Copilot.enums.insightTypeDataType,
        isStored: true,
        isPublished: true,
        duplicateRequiresPrompt: true,
    })
    readonly type: Promise<x3Copilot.enums.InsightTypeType>;

    @decorators.textStreamProperty<SysInsight, 'query'>({
        isStored: true,
        isPublished: true,
    })
    readonly query: Promise<TextStream>;

    @decorators.mutation<typeof SysInsight, 'saveToDisk'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'boolean',
        },
    })
    static async saveToDisk(context: Context): Promise<boolean> {
        await InsightStorageManager.saveInsightsToDisk(context);
        return true;
    }
}
