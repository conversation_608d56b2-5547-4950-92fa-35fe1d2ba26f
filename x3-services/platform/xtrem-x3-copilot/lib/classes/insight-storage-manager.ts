/* eslint-disable class-methods-use-this */
/* eslint-disable @typescript-eslint/no-unused-vars */
import prettierSync from '@prettier/sync';
import {
    AnyR<PERSON>ord,
    AnyRecordWithId,
    AnyValue,
    AsyncGenericReader,
    AsyncReader,
    AsyncResponse,
    Context,
    Dict,
    Extend,
    ExternalStorageManager,
    Node,
    NodeExternalQueryOptions,
    NodeFactory,
    OrderBy,
    OrderByClause,
    PackageApi,
    Property,
    PropertyAndValue,
    RecordPaging,
    TextStream,
    ValidationContext,
} from '@sage/xtrem-core';
import * as crypto from 'crypto';
import * as fs from 'fs';
import * as _ from 'lodash';
import * as fsp from 'path';
import { Options as PrettierOptions } from 'prettier';
import { InsightTypeType } from '../enums/insight-type-type';

export interface InsightQuery {
    screenId: string;
    type: InsightTypeType;
    key: string;
    query: string;
}

export interface Insight extends InsightQuery {
    hash: string;
    screenId: string;
    type: InsightTypeType;
    key: string;
    query: string;
    package: string;
}

export interface PackageApiWithInsights extends PackageApi {
    insights?: Dict<Insight>;
}

/**
 * InsightStorageManager is an implementation of ExternalStorageManager for managing SysInsight nodes.
 * It provides methods for loading, saving, querying, inserting, updating, and deleting Insight records,
 * which are stored externally (not in the default database). Insights are typically loaded from and saved to disk,
 * and are associated with Sage X3 Copilot packages.
 *
 * @template T The Node type managed by this storage manager.
 */
export class InsightStorageManager<T extends Node> implements ExternalStorageManager<T> {
    /** The NodeFactory instance for this manager. */
    factory: NodeFactory;

    /** Indicates if insights have been loaded into memory. */
    static isLoaded = false;

    /** Promise for the current loading operation, if any. */
    static loadingPromise: Promise<Insight[]> | undefined;

    /** In-memory cache of all loaded insights. */
    static insights: Insight[] = [];

    /**
     * Computes a unique hash for an insight based on its package, screenId, and key.
     * @param insight The insight to hash.
     * @returns The base64-encoded SHA256 hash string.
     */
    static getHash(insight: Insight): string {
        return crypto
            .createHash('SHA256')
            .update(JSON.stringify({ package: insight.package, screenId: insight.screenId, key: insight.key }))
            .digest('base64');
    }

    /**
     * Loads all insights from all packages in the application context.
     * Caches the result in memory for future access.
     * @param context The application context.
     * @returns A promise resolving to the array of all insights.
     */
    static getAllInsights(context: Context): AsyncResponse<Insight[]> {
        if (this.isLoaded) {
            return this.insights;
        }

        this.loadingPromise = new Promise(resolve => {
            const { application } = context;
            const packages = application.getPackages();

            this.insights = [];
            const result: Insight[] = [];

            packages.forEach(pkg => {
                const api = pkg.api as PackageApiWithInsights;
                if (api.insights) {
                    Object.values(api.insights).forEach(insight => {
                        const insightResult = {
                            ...insight,
                            package: '',
                            hash: '',
                        } as Insight;
                        insightResult.package = pkg.name;
                        insightResult.hash = this.getHash(insightResult);
                        result.push(insightResult);
                    });
                }
            });
            this.insights = result;
            this.isLoaded = true;
            resolve(this.insights);
        });

        return this.loadingPromise;
    }

    /**
     * Formats TypeScript source code using Prettier.
     * @param source The TypeScript source code to format.
     * @returns The formatted code.
     */
    static prettyifyTypescript(source: string): string {
        const prettierConfig: PrettierOptions = {
            parser: 'typescript',
            singleQuote: true,
            printWidth: 120,
            tabWidth: 4,
            useTabs: false,
            semi: true,
            trailingComma: 'all',
            arrowParens: 'avoid',
            endOfLine: 'lf',
            filepath: 'dummy.ts',
        };

        return prettierSync.format(source, prettierConfig);
    }

    /**
     * Saves all loaded insights to disk, generating TypeScript files for each insight
     * and updating package index files.
     * @param context The application context.
     */
    static async saveInsightsToDisk(context: Context): Promise<void> {
        const insights = await InsightStorageManager.getAllInsights(context);

        const { application } = context;

        const insightFilesByPackage: Dict<string[]> = {};

        insights.forEach(insight => {
            const { package: pkgName } = insight;
            const pkg = application.packagesByName[pkgName];
            if (pkg) {
                const { dir } = pkg;
                const insightsDir = fsp.join(dir, 'lib', 'insights');
                if (!insightFilesByPackage[pkgName]) {
                    insightFilesByPackage[pkgName] = [];
                    if (fs.existsSync(insightsDir)) {
                        fs.rmSync(insightsDir, { recursive: true, force: true });
                    }
                    fs.mkdirSync(insightsDir, { recursive: true });
                }
                const fileBaseName = _.kebabCase(`${insight.screenId}-${insight.key}`);
                const filePath = fsp.join(insightsDir, `${fileBaseName}.ts`);
                const variableName = _.camelCase(`${insight.screenId}-${insight.key}`);
                const declaration = `export const ${variableName}: InsightQuery = {`;
                const lines = [`import { InsightQuery } from '@sage/xtrem-x3-copilot';\n`, declaration];
                const spaces = ' '.repeat(declaration.length + 1);
                lines.push(`${spaces}screenId: '${insight.screenId}',`);
                lines.push(`${spaces}type: '${insight.type}',`);
                lines.push(`${spaces}key: '${insight.key}',`);
                lines.push(`${spaces}query: \`${insight.query}\``);
                lines.push(`${spaces}};`);
                const content = InsightStorageManager.prettyifyTypescript(lines.join('\n')).replace(/\\/g, '\\\\');
                insightFilesByPackage[pkgName].push(filePath);
                fs.writeFileSync(filePath, content, 'utf8');
            }
        });

        Object.entries(insightFilesByPackage).forEach(([pkgName, filePaths]) => {
            const pkg = application.packagesByName[pkgName];
            if (pkg) {
                const { dir } = pkg;
                const insightsDir = fsp.join(dir, 'lib', 'insights');
                const indexFilePath = fsp.join(insightsDir, 'index.ts');
                const indexLines = filePaths.map(filePath => {
                    const fileName = fsp.basename(filePath, '.ts');
                    const variableName = _.camelCase(fileName);
                    return `export { ${variableName} } from './${fileName}';`;
                });
                const indexContent = InsightStorageManager.prettyifyTypescript(indexLines.join('\n'));
                fs.writeFileSync(indexFilePath, indexContent, 'utf8');

                const libFilePath = fsp.join(dir, 'lib', 'index.ts');
                const libFileContent = fs.existsSync(libFilePath) ? fs.readFileSync(libFilePath, 'utf8') : '';
                if (!libFileContent.includes("export * as insights from './insights';")) {
                    const insightsImport = `\nexport * as insights from './insights';`;
                    const updatedLibContent = InsightStorageManager.prettyifyTypescript(
                        libFileContent + insightsImport,
                    );
                    fs.writeFileSync(libFilePath, updatedLibContent, 'utf8');
                }
            }
        });
    }

    /**
     * Construct an instance of Insight from a Node.
     * @param node
     * @returns
     */
    static async getInsightFromNode<T extends Node>(node: Extend<T>): Promise<Insight> {
        const screenId = await node.$.get('screenId');
        const type = await node.$.get('type');
        const key = await node.$.get('key');
        const query = (await node.$.get('query')) as TextStream;
        const packageName = (await ((await node.$.get('package')) as Node).$.get('name')) as string;

        if (!screenId || !type || !key || !query || !packageName) {
            throw new Error(
                `Invalid insight data: screenId: ${screenId}, type: ${type}, key: ${key}, query: ${query}, package: ${packageName}`,
            );
        }

        const result = {
            screenId: screenId as string,
            type: type as InsightTypeType,
            key: key as string,
            query: query.value,
            package: packageName as string,
            hash: '',
        } as Insight;

        result.hash = InsightStorageManager.getHash(result);

        return result;
    }

    /**
     * Create a new Insight.
     * @param node
     * @param _cx
     * @returns
     */
    async insert(node: Extend<T>, _cx: ValidationContext): Promise<AnyRecordWithId> {
        const insight = await InsightStorageManager.getInsightFromNode(node);

        const insights = await InsightStorageManager.getAllInsights(node.$.context);

        if (insights.some(i => i.hash === insight.hash)) {
            throw new Error(
                `Insight package: ${insight.package}, screenId: ${insight.screenId}, key: ${insight.key} already exists.`,
            );
        }

        if (insight.type === 'main' && insights.some(i => i.screenId === insight.screenId && i.type === 'main')) {
            throw new Error(`Main insight for screenId: ${insight.screenId} already exists.`);
        }

        InsightStorageManager.insights.push(insight);

        return { _id: insight.hash, hash: insight.hash };
    }

    /**
     * Update an existing Insight.
     * @param node
     * @param _cx
     * @returns
     */
    async update(node: Extend<T>, _cx: ValidationContext): Promise<AnyRecord[]> {
        const insight = await InsightStorageManager.getInsightFromNode(node);

        const insights = await InsightStorageManager.getAllInsights(node.$.context);
        const existingInsightIndex = insights.findIndex(i => i.hash === insight.hash);
        if (existingInsightIndex < 0) {
            if (insight.type === 'main' && insights.some(i => i.screenId === insight.screenId && i.type === 'main')) {
                throw new Error(`Main insight for screenId: ${insight.screenId} already exists.`);
            }
            InsightStorageManager.insights.push(insight);
        } else {
            if (
                insight.type === 'main' &&
                insights.some(i => i.screenId === insight.screenId && i.hash !== insight.hash && i.type === 'main')
            ) {
                throw new Error(`Main insight for screenId: ${insight.screenId} already exists.`);
            }
            // Update existing insight
            InsightStorageManager.insights[existingInsightIndex] = insight;
        }

        return [{ _id: insight.hash, hash: insight.hash }];
    }

    /**
     * Delete an existing Insight.
     * Throws an error if the insight does not exist or if it is a main insight with
     * @param node
     * @param _cx
     * @returns
     */
    async delete(node: Extend<T>, _cx: ValidationContext): Promise<number> {
        const insight = await InsightStorageManager.getInsightFromNode(node);

        const insights = await InsightStorageManager.getAllInsights(node.$.context);
        const existingInsightIndex = insights.findIndex(i => i.hash === insight.hash);

        if (existingInsightIndex < 0) {
            throw new Error(
                `Insight package: ${insight.package}, screenId: ${insight.screenId}, key: ${insight.key} does not exist.`,
            );
        }

        if (insight.type === 'main' && insights.some(i => i.screenId === insight.screenId && i.type !== 'main')) {
            throw new Error(`Cannot delete main insight for screenId: ${insight.screenId} when other insights exist.`);
        }

        InsightStorageManager.insights.splice(existingInsightIndex, 1);

        return 1;
    }

    /**
     * Query insights based on the provided context and options.
     * @param context
     * @param options
     * @returns
     */
    query(context: Context, options: NodeExternalQueryOptions<T>): AsyncReader<any> {
        let insightRecords: AnyRecord[];
        const getInsights = async (pagingOptions: NodeExternalQueryOptions<T>): Promise<AnyRecord[]> => {
            const insights = await InsightStorageManager.getAllInsights(context);
            const mappedInsights = insights.map(i => {
                const record: AnyRecord = {
                    _id: i.hash,
                    hash: i.hash,
                    screenId: i.screenId,
                    type: i.type,
                    key: i.key,
                    query: i.query ? TextStream.fromString(i.query) : TextStream.empty,
                    package: i.package,
                };
                return record;
            });

            return (
                await RecordPaging.applyPagingOptions(
                    this.factory,
                    context,
                    [...mappedInsights],
                    (_propContext: Context, property: Property, record: AnyRecord): AsyncResponse<AnyValue> => {
                        return record[property.name];
                    },
                    pagingOptions,
                )
            ).items;
        };

        if (options.count) {
            const readCount = async () => {
                if (!insightRecords) {
                    insightRecords = await getInsights({ filters: options.filters });
                    return insightRecords.length;
                }
                return undefined;
            };
            return new AsyncGenericReader({ read: readCount, stop() {} });
        }

        const read = async (): Promise<AnyRecord | undefined> => {
            if (!insightRecords) {
                insightRecords = await getInsights(options);
            }
            return insightRecords.pop();
        };

        return new AsyncGenericReader<any>({ read, stop() {} });
    }

    mapRecordIn(record: any): any {
        return record;
    }

    /**
     * Maps an aggregate record in. Not implemented for insights.
     * @param _record The record to map.
     * @throws Always throws (not implemented).
     */
    mapAggregateRecordIn(_record: any): any {
        throw new Error('NYI: aggregates on insights');
    }

    /**
     * Returns join information for a reference property.
     * @param propertyName The property name.
     * @returns A dictionary of join information.
     */
    getReferenceJoin(propertyName: string): Dict<string | ((this: Extend<T>) => any)> {
        if (propertyName === 'package') {
            return {
                name: 'package',
            };
        }
        return {};
    }

    /**
     * Returns join information for a collection property.
     * @param _propertyName The property name.
     * @returns An empty dictionary (no collection joins).
     */
    getCollectionJoin(_propertyName: string): Dict<string | ((this: Extend<T>) => any)> {
        return {};
    }

    /**
     * Gets join values for a reference property.
     * @param node The node instance.
     * @param data The data object.
     * @param propertyName The property name.
     * @param _index The index (unused).
     * @returns A dictionary of join values.
     */
    async getJoinValues(node: Extend<T>, data: any, propertyName: string, _index: number): Promise<Dict<any>> {
        if (propertyName === 'package') {
            return data[propertyName] ? { name: data[propertyName] } : { name: await node.$.get('package') };
        }
        return {};
    }

    /**
     * Parses an order by clause for queries.
     * @param _context The context.
     * @param _orderBy The order by object.
     * @returns An empty array (no ordering supported).
     */
    parseOrderBy(_context: Context, _orderBy: OrderBy<Node> | undefined): OrderByClause[] {
        return [];
    }

    /**
     * Parses a cursor for paging.
     * @param _orderByClauses The order by clauses.
     * @param _value The cursor value.
     * @returns An empty array (not implemented).
     */
    parseCursor(_orderByClauses: OrderByClause[], _value: string): PropertyAndValue[] {
        return [];
    }

    /**
     * Internal counter for allocating transient IDs.
     */
    private _lastTransientId = -1000000000;

    /**
     * Allocates a new transient (temporary) ID for a record.
     * @returns The allocated ID as a string.
     */
    allocateTransientId(): string {
        this._lastTransientId -= 1;
        return String(this._lastTransientId);
    }

    /**
     * Gets the key values for an insight, used for identifying records.
     * @param _context The context.
     * @param insight The insight object.
     * @param _options Options for allocation.
     * @returns A dictionary of key values.
     */
    getKeyValues(_context: Context, insight: any, _options?: { allocateTransient: boolean }): Dict<any> {
        return { package: insight.package, screenId: insight.screenId, key: insight.key };
    }

    /**
     * Indicates if a key property is transient.
     * @param _propertyName The property name.
     * @param _value The value.
     * @returns Always false (no transient key properties).
     */
    isKeyPropertyTransient(_propertyName: string, _value: any): boolean {
        return false;
    }

    /**
     * Indicates if a property is a reverse reference.
     * @param _propertyName The property name.
     * @returns Always false (no reverse references).
     */
    isReverseReferenceProperty(_propertyName: string): boolean {
        return false;
    }

    /**
     * Gets the default order by clause for insights (by key properties).
     * @returns The default order by object.
     */
    get defaultOrderBy(): OrderBy<Node> {
        return this.factory.keyProperties.reduce((r, k) => {
            r[k.name] = 1;
            return r;
        }, {} as Dict<any>);
    }

    /**
     * Gets the formatted key values as a string, concatenated by `~`.
     * Used for the _id value.
     * @param _context The context.
     * @param values The key values.
     * @returns The _id as a string.
     */
    getId(_context: Context, values: Dict<any>): string {
        return String(values._id);
    }

    /**
     * Indicates if creation is allowed.
     * @param canCreate The flag.
     * @returns The flag value.
     */
    canCreate(canCreate: boolean): boolean {
        return canCreate;
    }

    /**
     * Indicates if update is allowed.
     * @param canUpdate The flag.
     * @returns The flag value.
     */
    canUpdate(canUpdate: boolean): boolean {
        return canUpdate;
    }

    /**
     * Indicates if deletion is allowed.
     * @param canDelete The flag.
     * @returns The flag value.
     */
    canDelete(canDelete: boolean): boolean {
        return canDelete;
    }

    /**
     * Indicates if deleting many is allowed.
     * @param canDeleteMany The flag.
     * @returns The flag value.
     */
    canDeleteMany(canDeleteMany: boolean): boolean {
        return canDeleteMany;
    }
}
