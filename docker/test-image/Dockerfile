# build instructions
# pass those args to build the end image:
#
#   TARGET_APP
#   GIT_FEATURE_BRANCH
#
# some are duplicate from the cahce stage on the branch one
# this is because the build cache must look at the action instructions to decide to re-use the or not cached version

ARG NODE_VERSION=22.16
ARG ALPINE_VERSION=3.21

# ======================================
# system-dependencies stage (TO PUSH)
# ======================================
# this stage is the less likely to change over time
# and so it is mint to be runned separately before to be exported as a cache image
FROM node:${NODE_VERSION}-alpine AS system-dependencies-stage

RUN apk update && apk upgrade \
    && apk --no-cache --update add aws-cli bash git openssh-client curl unzip wget libaio libnsl libc6-compat \
       chromium build-base cairo-dev jpeg-dev pango-dev giflib-dev postgresql-client dumb-init \
    && rm -rf /var/cache/apk/* /tmp \
    && mkdir /tmp \
    && chmod 777 /tmp \
    && sed -i 's@root:x:0:0:root:/root:/bin/ash@root:x:0:0:root:/root:/bin/bash@' /etc/passwd \
# install gyp dependecies
    && apk add --no-cache jq python3 make g++ alpine-sdk \
    && ln -s /usr/lib/libnsl.so.2 /usr/lib/libnsl.so.1 \
    && ln -s /lib/libc.so.6 /usr/lib/libresolv.so.2 \
    && ln -s /lib64/ld-linux-x86-64.so.2 /usr/lib/ld-linux-x86-64.so.2

RUN mkdir -p /home/<USER>/Downloads

# set system runtime env
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser \
    TZ="CET"

# ======================================
# branch-build stage (TO PUSH)
# ======================================
FROM ghcr.io/sage-erp-x3/xtrem-test-image-azure:system-dependencies-${ALPINE_VERSION} AS system-dependencies

# At this step the image contains all the system dependencies to install, build and run an Xtrem application

FROM system-dependencies AS branch-build

# declare build-args env for the xtrem app to build
ARG TARGET_APP
# declare build-args env for git info
ARG GIT_FEATURE_BRANCH

# declare runtime env for aws to ensure env from build are cleaned in the final image and containers
ENV TARGET_APP="${TARGET_APP}"
# download progress throws an error
ENV XTREM_NO_PROGRESS="1"

ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
ENV XTREM_SKIP_GEN_ARCHIVE="true"

WORKDIR /xtrem

# for xtrem-reporting and chromium headless
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

# For security we run the commands with all secret variables (NPM_USER, NPM_PASS, ...)
# from the file id xtrem-env mounted by default to /run/secrets/xtrem-env
# This file id is passed at build time with the docker command: docker build --secret id=xtrem-env,src=.env
RUN --mount=type=secret,id=xtrem-env \
    <<EOF
    set -e
    source /run/secrets/xtrem-env
    npm config set "@sage:registry" https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/
    npm config set "@sageai:registry" https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/
    npm config set "//pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/:_authToken" "${AZURE_DEVOPS_TOKEN}"
    npm config set progress false
# git blobless clone
    git config --global user.name ${GIT_USERNAME}
    git config --global "url.https://x-access-token:${GIT_PASSWORD}@github.com.insteadof" "https://github.com"
    git clone --filter=blob:none https://github.com/Sage-ERP-X3/xtrem.git ./
    git checkout ${GIT_FEATURE_BRANCH}
    git config --global --unset user.name
    git config --global --unset "url.https://x-access-token:${GIT_PASSWORD}@github.com.insteadof"
# install pnpm
    corepack enable
    corepack prepare $(node -p 'require("./package.json").packageManager.split("+")[0]') --activate
# install global dependencies
    pnpm i -g patch-package
# pnpm install scripts execution, we start we a fresh system no clean is required
    pnpm install

    # increase the memory limit for nodejs to avoid out of memory error during the build of xtrem-ui
    NODE_OPTIONS="--max-old-space-size=4096"
    export NODE_OPTIONS

    export AWS_REGION
    export AWS_DEFAULT_REGION="${AWS_REGION}"
    export AWS_ACCESS_KEY_ID
    export AWS_SECRET_ACCESS_KEY
    XTREM_CONCURRENCY=1
    export XTREM_CONCURRENCY
# pnpm run build scripts execution
    pnpm run build:${TARGET_APP}
# re-install root package without the dev dependencies to reduce the size on node_modules
    echo "reset node_modules to production only"
    pnpm install --prod --frozen-lockfile --config.node-linker=hoisted
# clear cache so this Action's layer should take less space
    pnpm run clear:cache:local
# logout from nexus registry returns 503, so cleanup npm config to remove token and other details
    npm uninstall -g npm
    rm -rf $PNPM_HOME /usr/local/lib/node_modules/
    rm -f ~/.npmrc
# cleanup the tmp dir
    rm -rf /tmp/*
EOF

# ======================================
# branch stage (will be pushed)
# ======================================
# This stage is the final part and is meant to update from the release cache
# then it cleans-up the build dependencies so that the final image is lighter
FROM system-dependencies AS branch

# declare build-args env for the xtrem app to build
ARG TARGET_APP
# declare build-args env for git info
ARG GIT_FEATURE_BRANCH

ENV TARGET_APP="${TARGET_APP}"

WORKDIR /xtrem/services/main/xtrem-services-main

RUN mkdir -p /xtrem/config

RUN chown -R node:node /xtrem/config /home/<USER>/Downloads

COPY --from=branch-build --chown=node:node /xtrem/docker/test-image/docker-entrypoint.sh /usr/local/bin/
RUN chmod 755 /usr/local/bin/docker-entrypoint.sh

# make xtrem bin available in path
ENV PATH=/xtrem/platform/cli/xtrem-cli/bin:$PATH

# copy utility scripts to image
COPY --chown=node:node ./manage-config-template.sh ./waitlive.sh /opt/scripts/
# copy only required folders to run the target app
COPY --from=branch-build --chown=node:node /xtrem/node_modules /xtrem/node_modules
COPY --from=branch-build --chown=node:node /xtrem/platform /xtrem/platform
COPY --from=branch-build --chown=node:node /xtrem/${TARGET_APP} /xtrem/${TARGET_APP}

RUN chmod a+x /opt/scripts/*.sh

VOLUME /xtrem/config

USER node

ENTRYPOINT ["/usr/bin/dumb-init", "--", "docker-entrypoint.sh"]

CMD ["xtrem"]
