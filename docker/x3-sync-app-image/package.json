{"name": "@sage/x3sync-app-image", "version": "58.0.2", "buildStamp": "2025-07-24T20:05:49.024Z", "description": "", "main": "node_modules/@sage/xtrem-x3-sync/build/index.js", "scripts": {}, "keywords": [], "author": "", "license": "UNLICENSED", "dependencies": {"@sage/xtrem-x3-sync": "^58.0.2"}, "pnpm": {"overrides": {"typescript": "~5.8.3", "newrelic": "12.10.0"}, "onlyBuiltDependencies": ["@contrast/fn-inspect", "@newrelic/native-metrics", "@parcel/watcher", "@sage/bms-dashboard", "@sage/xdev", "@swc/core", "canvas", "core-js", "esbuild", "nx", "oracledb", "protobufjs", "puppeteer", "re2", "sharp"]}, "packageManager": "pnpm@10.12.1"}