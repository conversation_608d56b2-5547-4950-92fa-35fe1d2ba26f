{"name": "@sage/glossary", "version": "58.0.2", "buildStamp": "2025-07-24T20:05:49.025Z", "description": "", "main": "node_modules/@sage/xtrem-glossary/build/index.js", "scripts": {"xtrem": "xtrem", "manage": "xtrem manage", "schema": "xtrem schema"}, "keywords": [], "author": "", "license": "UNLICENSED", "dependencies": {"@sage/xtrem-glossary": "^58.0.2", "@sage/xtrem-cli-cloud": "^58.0.2", "@sage/xtrem-cli-main": "^58.0.2"}, "pnpm": {"overrides": {"@sage/design-tokens": "4.35.0", "@puppeteer/browsers@1.3.0>tar-fs": "^2.1.3", "@turbo/codemod@1>axios": "^0.30.0", "@types/react": "^18.3.3", "@wdio/globals": "8.12.1", "d3-color": "^3.1.0", "graphql": "16.1.0-experimental-stream-defer.6", "react-dom": "^18.3.1", "react": "^18.3.1", "sinon": "^21.0.0", "styled-components": "^5.3.11", "typescript": "~5.8.3", "webdriverio": "8.12.1", "webpack": "^5.95.0", "puppeteer-core>ws": "^8.17.1", "@cucumber/cucumber>semver": "^7.5.2", "pbkdf2": "^3.1.3", "@newrelic/security-agent>form-data": "^2.5.5"}, "peerDependencyRules": {"allowAny": ["redux"], "allowedVersions": {"codemirror-graphql@2>@codemirror/language": "6"}, "ignoreMissing": ["ckeditor5"]}, "patchedDependencies": {"react-grid-layout": "patches/<EMAIL>", "@types/react-grid-layout": "patches/@<EMAIL>", "@ag-grid-community/core": "patches/@ag-grid-community__core.patch", "carbon-react@153.7.0": "patches/<EMAIL>", "jsdom": "patches/jsdom.patch"}, "onlyBuiltDependencies": ["@contrast/fn-inspect", "@newrelic/native-metrics", "@parcel/watcher", "@sage/bms-dashboard", "@sage/xdev", "@swc/core", "canvas", "core-js", "esbuild", "nx", "oracledb", "protobufjs", "puppeteer", "re2", "sharp"]}, "packageManager": "pnpm@10.13.1"}