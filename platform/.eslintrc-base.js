module.exports = {
    plugins: ['mocha', 'unicorn', 'unused-imports', '@sage/redos'],
    extends: ['airbnb', 'airbnb-typescript', 'eslint-config-prettier', 'plugin:@sage/redos/recommended'],
    rules: {
        // TODO: switch to "error" when all warning will be fixed and add more selectors
        '@typescript-eslint/naming-convention': [
            'warn',
            {
                selector: 'typeLike',
                format: ['StrictPascalCase'],
            },
        ],
        // eslint does not always indent like prettier so disable this one
        '@typescript-eslint/indent': 'off',
        // prettier style for function paren
        '@typescript-eslint/space-before-function-paren': [
            'error',
            {
                anonymous: 'always',
                named: 'never',
                asyncArrow: 'always',
            },
        ],
        // for now, allow our special /// comments
        'spaced-comment': ['error', 'always', { markers: ['/'] }],
        // allow _foo functions and variables for now
        'no-underscore-dangle': 'off',
        // we never use default export
        'import/prefer-default-export': 'off',
        // we definitely need this one off for tests - let's see if we should turn on in lib
        'max-classes-per-file': 'off',

        // this one is purely styllistic - we'll fix later
        'no-nested-ternary': 'off',
        // allow cycles in dependencies for now
        'import/no-cycle': 'off',
        // this rule detects bad imperative style so we should fix the code
        'no-param-reassign': ['error', { props: false }],
        // we can fix this one by reordering declarations inside a file
        '@typescript-eslint/no-use-before-define': 'off',

        // this one needs more debate
        'prefer-destructuring': 'off',

        'react/destructuring-assignment': 'off',

        // rules that airbnb does not set and that we want
        'mocha/no-exclusive-tests': 'error',
        'unicorn/filename-case': [
            'error',
            {
                case: 'kebabCase',
                ignore: [/^node-\$\.ts$/],
            },
        ],
        //conflict with prettier plugin
        'import/order': 'off',

        // To review later
        'import/no-import-module-exports': 'off',

        // TS 5.2 - off for now - will enable in separate PR
        'arrow-body-style': 'off',
        'prefer-arrow-callback': 'warn',
    },
    overrides: [
        {
            files: ['lib/**/*.ts', 'test/**/*.ts'],
            rules: {
                'unused-imports/no-unused-imports': 'error',
                // promise rules
                'no-await-in-loop': 'off',
                'no-async-promise-executor': 'error',
                'no-promise-executor-return': 'error',
                'no-return-await': 'error',
                'require-await': 'error',
                'prefer-promise-reject-errors': 'error',
                '@typescript-eslint/no-floating-promises': 'error',
                // TODO: reenable this rule later
                // '@typescript-eslint/no-misused-promises': 'error',
                '@typescript-eslint/await-thenable': 'error',
                '@sage/redos/no-vulnerable': ['error', { cache: { segmented: true } }],
            },
        },
        {
            files: ['api/api.d.ts'],
            rules: {
                'import/no-duplicates': 'off',
                'import/newline-after-import': 'off',
            },
        },
        {
            files: ['test/**/*.ts'],
            rules: {
                // allow dependencies on dev packages for test
                'import/no-extraneous-dependencies': ['error', { devDependencies: true }],
                '@sage/redos/no-vulnerable': 'off',
            },
        },
        {
            files: ['lib/{pages,page-extensions,stickers}/**/*.ts'],
            rules: {
                // enforce this rule only server-side
                '@typescript-eslint/no-floating-promises': 'off',
                'require-await': 'off',
                '@typescript-eslint/await-thenable': 'off',
                'no-promise-executor-return': 'off',
            },
        },
    ],
};
