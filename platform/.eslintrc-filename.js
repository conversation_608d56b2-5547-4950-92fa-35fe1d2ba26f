module.exports = {
    plugins: ['unicorn'],
    parser: './eslint-fake-parser.js',
    ignorePatterns: ['**/*.js', '**/*.ts', '**/coverage', '**/i18n/*.json'],
    rules: {
        'unicorn/filename-case': [
            'error',
            {
                case: 'kebabCase',
                ignore: [/^(Jenkinsfile|Dockerfile|(CHANGELOG|README)\.md|node-\$\..+)$/],
            },
        ],
    },
};
