{"compileOnSave": true, "compilerOptions": {"composite": true, "declaration": true, "declarationMap": true, "experimentalDecorators": true, "lib": ["es2022", "dom"], "module": "commonjs", "moduleResolution": "node", "noImplicitAny": true, "noImplicitThis": true, "noUnusedLocals": true, "resolveJsonModule": true, "sourceMap": true, "strictNullChecks": true, "strictBindCallApply": true, "stripInternal": true, "skipLibCheck": true, "target": "es2022", "useDefineForClassFields": false, "noImplicitOverride": true, "incremental": true}}