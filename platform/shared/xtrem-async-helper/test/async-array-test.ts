import { assert } from 'chai';
import { asyncArray, asyncHealtyEventLoop } from '../lib';

const delay = <T>(value: T, millis = 1): Promise<T> => new Promise(resolve => setTimeout(() => resolve(value), millis));

describe('Async array', () => {
    it('can iterate with forEach', async () => {
        const results = [] as string[];
        const letters = 'abcdef'.split('');
        await asyncArray(letters).forEach(async letter => {
            results.push(await delay(letter));
        });
        assert.deepEqual(results, letters);
    });

    it('can map', async () => {
        const results = await asyncArray([1, 2, 3])
            .map(x => delay(x * x))
            .toArray();
        assert.deepEqual(results, [1, 4, 9]);
    });

    it('can filter', async () => {
        const results = await asyncArray([1, 2, 3])
            .filter(x => delay(x % 2 === 1))
            .toArray();
        assert.deepEqual(results, [1, 3]);
    });

    it('can find', async () => {
        const result = await asyncArray([1, 2, 3]).find(x => delay(x > 1.5));
        assert.equal(result, 2);
    });

    it('can find index', async () => {
        const result = await asyncArray([1, 2, 3]).findIndex(x => delay(x > 1.5));
        assert.equal(result, 1);
    });

    it('can test every', async () => {
        assert.isTrue(await asyncArray([1, 2, 3]).every(x => delay(x > 0)));
        assert.isFalse(await asyncArray([1, 2, 3]).every(x => delay(x > 1)));
    });

    it('can test some', async () => {
        assert.isTrue(await asyncArray([1, 2, 3]).some(x => delay(x > 1)));
        assert.isFalse(await asyncArray([1, 2, 3]).some(x => delay(x > 3)));
    });

    it('can reduce', async () => {
        assert.equal(await asyncArray([1, 2, 3]).reduce((x, y) => delay(x + y), 10), 16);
        // without initializer
        assert.equal(await asyncArray([1, 2, 3] as number[]).reduce((x, y) => delay(x + y)), 6);
    });

    it('can sort', async () => {
        assert.deepEqual(
            await asyncArray([1, 3, 4, 2])
                .sort((x, y) => delay(x - y))
                .toArray(),
            [1, 2, 3, 4],
        );
    });

    it('can join', async () => {
        assert.equal(await asyncArray([1, 2, 3]).join(), '1,2,3');
    });

    it('can reduce to unique elements', async () => {
        assert.deepEqual(await asyncArray([1, 1, 2, 3, 2]).uniq().toArray(), [1, 2, 3]);
    });

    it('can access element at', async () => {
        assert.equal(await asyncArray([1, 2, 3, 4]).elementAt(2), 3);
        assert.equal(await asyncArray([1, 2, 3, 4]).elementAt(-1), 4);
        await assert.isRejected(asyncArray([1, 2, 3, 4]).elementAt(4), 'index out of bounds: 4');

        assert.equal(await asyncArray([1, 2, 3, 4]).at(2), 3);
        assert.equal(await asyncArray([1, 2, 3, 4]).at(-1), 4);
        assert.equal(await asyncArray([1, 2, 3, 4]).at(4), undefined);
    });

    it('can get length', async () => {
        assert.equal(await asyncArray([1, 2, 3, 4]).length, 4);
    });

    it('can iterate with forEachParallel', async () => {
        const results = [] as number[];
        await asyncArray([2, 1, 4, 10, 5, 3]).forEachParallel(4, async value => {
            results.push(await delay(value, 25 * value));
        });
        // parallel should start with only 2, 1, 4 and 10 (will end respectively at 20, 10, 40 and 100 ms marks)
        // after 10 ms, results = [1] and 5 starts (will end at 60ms)
        // after 20 ms, results = [1, 2] and 3 starts (will end at 50 ms)
        // after 40 ms, results = [1, 2, 4]
        // after 50 ms, results = [1, 2, 4, 3]
        // after 60 ms, results = [1, 2, 4, 3, 5]
        // after 100 ms, results = [1, 2, 4, 3, 5, 10]
        assert.deepEqual(results, [1, 2, 4, 3, 5, 10]);
    });

    it('can yield event loop', async () => {
        const array1: any[] = Array.from({ length: 5000 });
        const array2: any[] = Array.from({ length: 1000 });
        let res;
        let hit = 0;
        let count = asyncHealtyEventLoop.getYieldCount();

        setInterval(() => {
            hit += 1;
        }, 50);

        await asyncArray(array1).forEach(a1 => {
            return asyncArray(array2).forEach(a2 => {
                res = `${a1}-${a2}`;
            });
        });
        count = asyncHealtyEventLoop.getYieldCount() - count;
        assert.closeTo(hit, count, 1);
        assert.isAtLeast(count, 2);
        assert.strictEqual(res, 'undefined-undefined');
    });

    it('can slice an asyncArray', async () => {
        const array = [{ code: 'a' }, { code: 'b' }, { code: 'c' }];
        const asyncArrayTest = await asyncArray(array).toArray();
        const slicedAsyncArrayTest = asyncArrayTest.slice(0, 2);
        assert.deepEqual(asyncArrayTest, array);
        assert.deepEqual(slicedAsyncArrayTest, [{ code: 'a' }, { code: 'b' }]);
    });
});
