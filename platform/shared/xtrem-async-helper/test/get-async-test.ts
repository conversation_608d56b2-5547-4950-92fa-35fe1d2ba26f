import { assert } from 'chai';
import { getAsync } from '../lib';

describe('Async array', () => {
    it('can get a single level', async () => {
        const input = {
            level1: new Promise(resolve => resolve(1)),
        };
        const result = await getAsync(input, 'level1');
        assert.equal(result, 1);
    });

    it('can get mult-level', async () => {
        const input = {
            level1: new Promise(resolve =>
                resolve({ level2: new Promise(resolve => resolve({ level3: new Promise(resolve => resolve(3)) })) }),
            ),
        };
        const result = await getAsync(input, 'level1.level2.level3');
        assert.equal(result, 3);
    });

    it('can get mult-level with non-promise level', async () => {
        const input = {
            level1: new Promise(resolve =>
                resolve({ level2: new Promise(resolve => resolve({ level3: { _id: 4 } })) }),
            ),
        };
        const result = await getAsync(input, 'level1.level2.level3._id');
        assert.equal(result, 4);
    });

    it('cannot get a value for key that does not exist in the object', async () => {
        const input = {
            level1: new Promise(resolve =>
                resolve({ level2: new Promise(resolve => resolve({ level3: { _id: 4 } })) }),
            ),
        };
        assert.isRejected(getAsync(input, 'level1.level2.level3.foo'), 'Invalid key in path level1.level2.level3.foo');
    });
});
