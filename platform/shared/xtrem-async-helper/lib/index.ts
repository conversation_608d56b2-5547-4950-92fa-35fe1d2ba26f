import { HealthyEventLoop } from './healthy-event-loop';

const { dynamicImport } = require('./dynamic-import');

export * from './async-array';
export * from './async-reader';
export * from './funnel';
export * from './get-async';
export * from './healthy-event-loop';
export * from './promise-markers';
export * from './sleep';
export { dynamicImport };

export const asyncHealtyEventLoop = new HealthyEventLoop(10000);
