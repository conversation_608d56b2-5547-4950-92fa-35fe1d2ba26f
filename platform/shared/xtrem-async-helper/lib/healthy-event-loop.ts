// This is a shared module between back-end and front-end,
// so we need to use the appropriate function to measure the time depending on the environment.
let perfNow: () => number;

if (typeof window === 'undefined') {
    // Node.js environment:
    // we intentionally use a dynamic require to avoid errors with webpack bundle
    const perfHooks = 'node:perf_hooks';
    try {
        // eslint-disable-next-line import/no-dynamic-require, global-require
        const { performance } = require(perfHooks);
        perfNow = performance.now.bind(performance);
    } catch (e) {
        console.warn(`WARN: Cannot require '${perfHooks}', using Date.now() instead`);
        // Fallback in case of require failure
        perfNow = () => Date.now();
    }
} else {
    // Browser environment
    perfNow = window.performance.now.bind(window.performance);
}

/**
 * This class can be used in async loops to keep the event loop healthy
 *
 * @example
 *  const healthyEventLoop = new HealthyEventLoop(10000)
 *
 *  then, in loops
 *      if (healthyEventLoop.shouldYield) {
 *          await healthyEventLoop.yield();
 *      }
 */
export class HealthyEventLoop {
    #yieldCount = 0;

    #tick = 0;

    #lastYieldTime = perfNow();

    constructor(private readonly yieldModulo = 10000) {}

    getYieldCount() {
        return this.#yieldCount;
    }

    /**
     * Should the yield() function be called ?
     */
    get shouldYield(): boolean {
        this.#tick += 1;
        return this.#tick % this.yieldModulo === 0;
    }

    /**
     * Yield with setImmediate to keep event loop healthy during any array iterations
     */
    async yield(): Promise<void> {
        const now = perfNow();
        // Do not yield too often when the body of the loop is very fast
        if (now - this.#lastYieldTime < 100) return;

        // Yield with setImmediate, to give a turn to all higher priority events
        // See https://nodejs.org/en/docs/guides/event-loop-timers-and-nexttick
        await new Promise(setImmediate);
        this.#yieldCount += 1;
        this.#lastYieldTime = now;
    }
}
