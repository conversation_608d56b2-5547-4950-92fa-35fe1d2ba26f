import { asyncHealty<PERSON><PERSON>Loop } from '.';
import { Async<PERSON><PERSON><PERSON><PERSON>ike, Async<PERSON><PERSON>yReader } from './async-array';
import { AnyValue, AsyncResponse } from './promise-markers';

/**
 * Async reader interface. Implemented by AsyncGenericReader and AsyncArrayReader
 */
export interface AsyncReader<T extends AnyValue> extends AsyncArrayLike<T> {
    map<R extends AnyValue>(body: (item: T, i: number) => AsyncResponse<R>): AsyncReader<R>;
    filter(body: (item: T) => AsyncResponse<boolean>): AsyncReader<T>;

    get readCount(): number;
    read: () => AsyncResponse<T | undefined>;
    stop: () => AsyncResponse<void>;
    readAll(): Promise<T[]>;
    toAsyncGenerator(): AsyncGenerator<T>;
}

/**
 * Constructor options for AsyncGenericReader
 */
export interface AsyncGenericReaderOptions<T extends AnyValue> {
    read: () => AsyncResponse<T | undefined>;
    stop: () => AsyncResponse<void>;
}

/**
 * Generic Implementation of AsyncReader interface
 */
export class AsyncGenericReader<T extends AnyValue> implements AsyncReader<T> {
    constructor(private readonly options: AsyncGenericReaderOptions<T>) {}

    #readCount = 0;

    get readCount(): number {
        return this.#readCount;
    }

    async read(): Promise<T | undefined> {
        if (asyncHealtyEventLoop.shouldYield) {
            await asyncHealtyEventLoop.yield();
        }
        const item = await this.options.read();
        if (item !== undefined) this.#readCount += 1;
        return item;
    }

    // eslint-disable-next-line require-await
    async stop(): Promise<void> {
        return this.options.stop();
    }

    async forEach(body: (item: T, i: number) => AsyncResponse<void>): Promise<void> {
        try {
            let i = 0;
            // eslint-disable-next-line no-constant-condition
            while (true) {
                const item = await this.read();
                if (item === undefined) break;
                await body(item, i);
                i += 1;
            }
        } finally {
            await this.stop();
        }
    }

    map<R extends AnyValue>(body: (item: T, i: number) => AsyncResponse<R>): AsyncReader<R> {
        let i = 0;
        return new AsyncGenericReader({
            read: async () => {
                const item = await this.read();
                const mapped = item !== undefined ? body(item, i) : undefined;
                i += 1;
                return mapped;
            },
            stop: () => this.stop(),
        });
    }

    filter(body: (item: T) => AsyncResponse<boolean>): AsyncReader<T> {
        return new AsyncGenericReader({
            read: async () => {
                // eslint-disable-next-line no-constant-condition
                while (true) {
                    const item = await this.read();
                    if (item === undefined) return undefined;
                    if (body(item)) return item;
                }
            },
            stop: () => this.stop(),
        });
    }

    async reduce<R extends AnyValue>(body: (prev: R, item: T) => AsyncResponse<R>, initial: R): Promise<R> {
        try {
            let result = initial;
            // eslint-disable-next-line no-constant-condition
            while (true) {
                const item = await this.read();
                if (item === undefined) return result;
                result = await body(result, item);
            }
        } finally {
            await this.stop();
        }
    }

    async find(body: (item: T, i: number) => AsyncResponse<boolean>): Promise<T | undefined> {
        try {
            let i = 0;
            // eslint-disable-next-line no-constant-condition
            while (true) {
                const item = await this.read();
                if (item === undefined) return undefined;
                if (await body(item, i)) return item;
                i += 1;
            }
        } finally {
            await this.stop();
        }
    }

    async findIndex(body: (item: T, i: number) => AsyncResponse<boolean>): Promise<number> {
        try {
            let i = 0;
            // eslint-disable-next-line no-constant-condition
            while (true) {
                const item = await this.read();
                if (item === undefined) return -1;
                if (await body(item, i)) return i;
                i += 1;
            }
        } finally {
            await this.stop();
        }
    }

    async some(body: (item: T) => AsyncResponse<boolean>): Promise<boolean> {
        return (await this.find(body)) !== undefined;
    }

    async every(body: (item: T) => AsyncResponse<boolean>): Promise<boolean> {
        return (
            (await this.find(async item => {
                return !(await body(item));
            })) === undefined
        );
    }

    async readAll(): Promise<T[]> {
        try {
            const result: T[] = [];
            // eslint-disable-next-line no-constant-condition
            while (true) {
                const item = await this.read();
                if (item === undefined) break;
                result.push(item);
            }
            return result;
        } finally {
            await this.stop();
        }
    }

    sort(compare: (elt1: T, elt2: T) => AsyncResponse<number>): AsyncReader<T> {
        return new AsyncArrayReader<T>(() => this.readAll()).sort(compare);
    }

    toAsyncGenerator(): AsyncGenerator<T> {
        return createAsyncGenerator(this);
    }
}

export function createAsyncGenerator<T extends AnyValue>(
    reader: AsyncGenericReader<T> | AsyncArrayReader<T>,
): AsyncGenerator<T> {
    async function* generator(): AsyncGenerator<T> {
        // TODO: add timer to exit the loop if feed is not read to the end
        // See https://seg.phault.net/blog/2018/03/async-iterators-cancellation/
        try {
            let item: T | null | undefined;
            // eslint-disable-next-line no-cond-assign
            while ((item = await reader.read()) != null) {
                yield item;
            }
        } finally {
            await reader.stop();
        }
    }
    return generator();
}
