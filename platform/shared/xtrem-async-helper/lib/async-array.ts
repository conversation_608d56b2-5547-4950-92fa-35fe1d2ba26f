import { add } from '@sage/xtrem-decimal';
import { uniq } from 'lodash';
import { asyncHealtyEventLoop } from '.';
import { AsyncReader, createAsyncGenerator } from './async-reader';
import { AnyValue, AsyncResponse, voidPromise } from './promise-markers';

// <PERSON>n solves a typing issue when second arg of reduce is a literal ('', 0)
type Widen<T> = T extends '' ? string : T extends number ? number : T extends boolean ? boolean : T;

/**
 * Async reader interface. Implemented by AsyncGenericReader and AsyncArrayReader.
 */
export interface AsyncArrayLike<T extends AnyValue> {
    forEach(body: (item: T, i: number) => AsyncResponse<void>): Promise<void>;
    map<R extends AnyValue>(body: (item: T, i: number) => AsyncResponse<R>): AsyncArrayLike<R>;
    filter(body: (item: T) => AsyncResponse<boolean>): AsyncArrayLike<T>;
    reduce<R extends AnyValue>(
        body: (prev: Widen<R>, item: T) => AsyncResponse<Widen<R>>,
        initial: Widen<R>,
    ): Promise<Widen<R>>;
    find(body: (item: T, i: number) => AsyncResponse<boolean>): Promise<T | undefined>;
    findIndex(body: (item: T, i: number) => AsyncResponse<boolean>): Promise<number>;
    some(body: (item: T) => AsyncResponse<boolean>): Promise<boolean>;
    every(body: (item: T) => AsyncResponse<boolean>): Promise<boolean>;

    sort(cmp?: (elt1: T, elt2: T) => AsyncResponse<number>): AsyncArrayLike<T>;
}

/**
 * Array wrapper for chainable async versions of the ES5 Array methods.
 *
 * Only the very basic features of the ES5 API are supported.
 * The goal is to favor speed and not get distracted by rarely used features.
 */
export class AsyncArray<T extends AnyValue> implements AsyncArrayLike<T> {
    constructor(protected getSource: () => Promise<T[]> | T[]) {}

    async forEach(body: (item: T, i: number) => AsyncResponse<void>): Promise<void> {
        const array = await this.getSource();
        const len = array.length;
        for (let i = 0; i < len; i += 1) {
            if (asyncHealtyEventLoop.shouldYield) {
                await asyncHealtyEventLoop.yield();
            }
            await body(array[i], i);
        }
    }

    /**
     * Asynchronously creates a shallow copy of a portion of the array, specified by the `start` and `end` parameters,
     * and returns a new `AsyncArray` instance containing the sliced portion. This method does not modify the original array.
     */
    async slice(start?: number, end?: number): Promise<AsyncArray<T>> {
        const slicedArray = (await this.getSource()).slice(start, end);
        return new AsyncArray(() => slicedArray);
    }

    /**
     * Similar to forEach but parallelizes the execution of `body`.
     * The `maxParallel` parameter controls the number of parallel executions.
     */
    async forEachParallel(maxParallel: number, body: (item: T, i: number) => AsyncResponse<void>): Promise<void> {
        if (maxParallel < 1) throw new Error(`invalid maxParallel value: ${maxParallel}`);
        const array = await this.getSource();
        // See https://maximorlov.com/parallel-tasks-with-pure-javascript/ for a detailed explanation of the trick.
        const iterator = array.entries();

        // Create maxParallel workers which consume the iterator in parallel
        const workers = Array.from<typeof iterator>({ length: maxParallel })
            .fill(iterator)
            .map(async iter => {
                // This is the trick: each worker reads the iterator but a given entry of the iterator is only read by one worker.
                // eslint-disable-next-line no-restricted-syntax
                for (const [i, value] of iter) {
                    if (asyncHealtyEventLoop.shouldYield) {
                        await asyncHealtyEventLoop.yield();
                    }
                    await body(value, i);
                }
            });
        await Promise.all(workers);
    }

    map<R extends AnyValue>(body: (item: T, i: number) => AsyncResponse<R>): AsyncArrayReader<R> {
        return new AsyncArrayReader(() =>
            (async () => {
                const array = await this.getSource();
                const results: R[] = Array.from({ length: array.length });
                const len = array.length;
                for (let i = 0; i < len; i += 1) {
                    if (asyncHealtyEventLoop.shouldYield) {
                        await asyncHealtyEventLoop.yield();
                    }
                    results[i] = await body(array[i], i);
                }
                return results;
            })(),
        );
    }

    filter(body: (item: T) => AsyncResponse<boolean>): AsyncArrayReader<T> {
        return new AsyncArrayReader(() =>
            (async () => {
                const array = await this.getSource();
                const results: T[] = [];
                const len = array.length;
                for (let i = 0; i < len; i += 1) {
                    if (asyncHealtyEventLoop.shouldYield) {
                        await asyncHealtyEventLoop.yield();
                    }
                    const item = array[i];
                    if (await body(item)) results.push(item);
                }
                return results;
            })(),
        );
    }

    async find(body: (item: T, i: number) => AsyncResponse<boolean>): Promise<T | undefined> {
        const array = await this.getSource();
        const len = array.length;
        for (let i = 0; i < len; i += 1) {
            if (asyncHealtyEventLoop.shouldYield) {
                await asyncHealtyEventLoop.yield();
            }
            const item = array[i];
            if (await body(item, i)) return item;
        }
        return undefined;
    }

    async findIndex(body: (item: T, i: number) => AsyncResponse<boolean>): Promise<number> {
        const array = await this.getSource();
        const len = array.length;
        for (let i = 0; i < len; i += 1) {
            if (asyncHealtyEventLoop.shouldYield) {
                await asyncHealtyEventLoop.yield();
            }
            if (await body(array[i], i)) return i;
        }
        return -1;
    }

    async some(body: (item: T) => AsyncResponse<boolean>): Promise<boolean> {
        return (await this.find(body)) !== undefined;
    }

    async every(body: (item: T) => AsyncResponse<boolean>): Promise<boolean> {
        return (
            (await this.find(async item => {
                if (asyncHealtyEventLoop.shouldYield) {
                    await asyncHealtyEventLoop.yield();
                }
                return !(await body(item));
            })) === undefined
        );
    }

    reduce(body: (prev: T, item: T, index: number) => AsyncResponse<T>): Promise<T>;
    reduce<R extends AnyValue>(
        body: (prev: Widen<R>, item: T, index: number) => AsyncResponse<Widen<R>>,
        initial: Widen<R>,
    ): Promise<Widen<R>>;
    async reduce<R extends AnyValue>(
        body: (prev: Widen<R>, item: T, index: number) => AsyncResponse<Widen<R>>,
        initial?: Widen<R>,
    ): Promise<Widen<R>> {
        const array = await this.getSource();
        const len = array.length;
        if (len === 0 && arguments.length === 1) throw new TypeError('Reduce of empty array with no initial value');

        let result = arguments.length === 1 ? array[0] : initial;
        const start = arguments.length === 1 ? 1 : 0;
        for (let i = start; i < len; i += 1) {
            if (asyncHealtyEventLoop.shouldYield) {
                await asyncHealtyEventLoop.yield();
            }
            result = await body(result as Widen<R>, array[i], i);
        }
        return result as Widen<R>;
    }

    private async qsort(
        array: T[],
        compare: (elt1: T, elt2: T) => AsyncResponse<number>,
        beg: number,
        end: number,
    ): Promise<void> {
        if (beg >= end) return voidPromise;

        let tmp: T;
        if (end === beg + 1) {
            if ((await compare(array[beg], array[end])) > 0) {
                tmp = array[beg];
                array[beg] = array[end];
                array[end] = tmp;
            }
            return voidPromise;
        }

        const mid = Math.floor((beg + end) / 2);
        const o = array[mid];
        let nbeg = beg;
        let nend = end;

        while (nbeg <= nend) {
            while (nbeg < end && array[nbeg] !== o && (await compare(array[nbeg], o)) < 0) nbeg += 1;
            while (beg < nend && array[nend] !== o && (await compare(o, array[nend])) < 0) nend -= 1;

            if (nbeg <= nend) {
                tmp = array[nbeg];
                array[nbeg] = array[nend];
                array[nend] = tmp;
                nbeg += 1;
                nend -= 1;
            }
        }

        if (nbeg < end) await this.qsort(array, compare, nbeg, end);
        if (beg < nend) await this.qsort(array, compare, beg, nend);
        return voidPromise;
    }

    sort(
        compare: (elt1: T, elt2: T) => AsyncResponse<number>,
        begIndex?: number,
        endIndex?: number,
    ): AsyncArrayReader<T> {
        return new AsyncArrayReader(async () => {
            const array = (await this.getSource()).slice();

            await this.qsort(array, compare, begIndex || 0, endIndex == null ? array.length - 1 : endIndex);
            return array;
        });
    }

    async join(sep?: string): Promise<string> {
        const array = await this.getSource();
        return array.join(sep);
    }

    uniq(): AsyncArrayReader<T> {
        // lodash uniq is much faster than [...new Set(...)]
        // https://www.measurethat.net/Benchmarks/Show/9647/0/lodashs-uniq-vs-array-from-set
        return new AsyncArrayReader(async () => uniq(await this.getSource()));
    }

    async elementAt(i: number): Promise<T> {
        const array = await this.getSource();
        const j = i >= 0 ? i : array.length + i;
        if (j < 0 || j >= array.length) throw new Error(`index out of bounds: ${i}`);
        return array[j];
    }

    async at(i: number): Promise<T | undefined> {
        const array = await this.getSource();
        const j = i >= 0 ? i : array.length + i;
        return array[j];
    }

    get length(): Promise<number> {
        return (async () => {
            return (await this.getSource()).length;
        })();
    }

    // eslint-disable-next-line require-await
    async toArray(): Promise<T[]> {
        return this.getSource();
    }

    async min<R extends AnyValue>(body: (item: T, i: number) => AsyncResponse<R>): Promise<R> {
        return (await this.map(body).toArray()).reduce((prev, item) => {
            if (prev == null) return item;
            if (item == null) return prev;
            return prev < item ? prev : item;
        });
    }

    async max<R extends AnyValue>(body: (item: T, i: number) => AsyncResponse<R>): Promise<R> {
        return (await this.map(body).toArray()).reduce((prev, item) => {
            if (prev == null) return item;
            if (item == null) return prev;
            return prev > item ? prev : item;
        });
    }

    async sum<R extends number>(body: (item: T, i: number) => AsyncResponse<R>): Promise<R> {
        return (await this.map(body).toArray()).reduce((prev, item) => add(prev, item), 0) as R;
    }
}

/**
 * Wraps an array with an AsyncArray.
 */
export function asyncArray<T extends AnyValue>(array: T[]) {
    return new AsyncArray(() => array);
}

/**
 * AsyncReader wrapper around arrays.
 */
export class AsyncArrayReader<T extends AnyValue> extends AsyncArray<T> implements AsyncReader<T> {
    #index = 0;

    get readCount(): number {
        return this.#index;
    }

    async read(): Promise<T | undefined> {
        const array = await this.getSource();
        if (asyncHealtyEventLoop.shouldYield) {
            await asyncHealtyEventLoop.yield();
        }
        const item = array[this.#index];
        if (item !== undefined) this.#index += 1;
        return item;
    }

    async stop(): Promise<void> {
        this.#index = (await this.getSource()).length;
    }

    async readAll(): Promise<T[]> {
        const array = await this.getSource();
        this.#index = array.length;
        return array;
    }

    toAsyncGenerator(): AsyncGenerator<T> {
        return createAsyncGenerator(this);
    }
}

/**
 * Wraps an array with an AsyncArray
 */
export function asyncArrayReader<T extends AnyValue>(array: T[]) {
    return new AsyncArrayReader(() => array);
}
