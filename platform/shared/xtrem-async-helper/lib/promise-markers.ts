export type AnyNonNullableValue = boolean | string | number | object | AnyValue[];

export type AnyValue = AnyNonNullableValue | null | undefined;

// A record of AnyValue
export type AnyRecord = { [key: string]: AnyValue };

export type AsyncResponse<T extends unknown | void> = T | PromiseLike<T>;

// An object with values that may or may not be a Promise
export type NestedMaybePromised = { [key: string]: NestedMaybePromised | AsyncResponse<AnyValue> };

// export type UnPromised<T> = T extends infer U | bigint ? U : never;
export type UnPromised<T> = T extends Promise<infer U> ? U : T;

export type UnPromisedMap<T extends object> = {
    [K in keyof T]: UnPromised<T[K]>;
};

export type Promised<T extends { [K in keyof T]: AnyValue }> = {
    [K in keyof T]: Promise<T[K]>;
};

/** silly void promise to keep sonar happy */
export const voidPromise = undefined as unknown as Promise<void>;
