import { asyncArray } from './async-array';
import { AnyValue, NestedMaybePromised } from './promise-markers';

/**
 * Similar to lodash.get, get a value from an object from an attribute path, regardless if the value is a Promise, or
 * if any of the sub-key values are Promises.
 */
export function getAsync(source: NestedMaybePromised, path: string): Promise<AnyValue> {
    const pathList = path.split('.');
    return asyncArray(pathList).reduce(async (currentVal, key) => {
        if (currentVal == null) return currentVal;
        // eslint-disable-next-line @typescript-eslint/return-await, no-return-await
        return await currentVal[key];
    }, source);
}
