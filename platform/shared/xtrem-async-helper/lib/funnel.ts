import { LogicError } from '@sage/xtrem-shared';
import { AnyValue, AsyncResponse } from './promise-markers';

/// !doc
/// ## funnel
/// * `fun = flows.funnel(max)`
///   limits the number of concurrent executions of a given code block.
///
/// The `funnel` function is typically used with the following pattern:
///
/// ``` javascript
/// // somewhere
/// var myFunnel = flows.funnel(10); // create a funnel that only allows 10 concurrent executions.
///
/// // elsewhere
/// myFunnel(_, function(_) { /* code with at most 10 concurrent executions */ });
/// ```
///
/// The `diskUsage2.js` example demonstrates how these calls can be combined to control concurrent execution.
///
/// The `funnel` function can also be used to implement critical sections. Just set funnel's `max` parameter to 1.
///
/// If `max` is set to 0, a default number of parallel executions is allowed.
/// This default number can be read and set via `flows.funnel.defaultSize`.
/// If `max` is negative, the funnel does not limit the level of parallelism.
///
/// The funnel can be closed with `fun.close()`.
/// When a funnel is closed, the operations that are still in the funnel will continue but their callbacks
/// won't be called, and no other operation will enter the funnel.

export interface Funnel {
    <T extends AnyValue | void>(fn: () => AsyncResponse<T>): Promise<T>;
    close(): void;
    length: number;
    capacity: number;
    highWaterMark: number;
}
export type Callback<T> = (err: any, result?: T) => void;
export type Thunk<T> = (cb: Callback<T>) => void;

export function funnel(max = 1, callback?: () => void): Funnel {
    let queue = [] as {
        body: () => Promise<any>;
        resolve: (val: any) => void;
        reject: (err: Error) => void;
    }[];
    let active = 0;
    let closed = false;
    let highWaterMark = 0;

    function _doOne() {
        const current = queue.shift();
        active += 1;
        if (current == null) {
            throw new LogicError(`Unexpected value: ${current}`);
        }
        current
            .body()
            .then(current.resolve, current.reject)
            .finally(() => {
                active -= 1;
                if (!closed) {
                    while (active < max && queue.length > 0) _doOne();
                }
            });
    }

    const fun = (body: () => AsyncResponse<any>) => {
        return new Promise<any>((resolve, reject) => {
            if (max < 0 || max === Infinity) body().then(resolve, reject);
            else {
                queue.push({
                    body,
                    resolve,
                    reject,
                });
                callback?.();
                if (queue.length > highWaterMark) highWaterMark = queue.length;
                if (active < max && queue.length > 0) _doOne();
            }
        });
    };

    fun.close = () => {
        queue = [];
        closed = true;
    };

    Object.defineProperty(fun, 'length', {
        get() {
            return queue.length;
        },
    });
    Object.defineProperty(fun, 'capacity', {
        get() {
            return max;
        },
    });
    Object.defineProperty(fun, 'highWaterMark', {
        get() {
            return highWaterMark;
        },
    });
    // cast is required because the getter are not resolved
    return fun as Funnel;
}
