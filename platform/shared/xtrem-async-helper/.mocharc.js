require('source-map-support').install({
    hookRequire: true,
});
require('chai').use(require('chai-as-promised'));
require('ts-node/register/transpile-only');
const tsConfigPaths = require('tsconfig-paths');

tsConfigPaths.register({
    baseUrl: './',
    paths: {
        '@sage/*': ['../*/index.ts', '../*/lib', '../*/src'],
    },
});

module.exports = {
    exclude: ['**/node_modules/**/*.*'],
    timeout: 180000,
};
