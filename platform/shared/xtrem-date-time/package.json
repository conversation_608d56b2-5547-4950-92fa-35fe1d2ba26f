{"name": "@sage/xtrem-date-time", "descript": "Xtrem date and time data types", "version": "58.0.2", "author": "Sage", "license": "UNLICENSED", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/index.js", "typings": "build/package-definition.d.ts", "dependencies": {"@sage/xtrem-decimal": "workspace:*", "@sage/xtrem-i18n": "workspace:*", "@sage/xtrem-shared": "workspace:*", "dayjs": "^1.11.10", "uuid": "^11.0.0"}, "devDependencies": {"@sage/xtrem-dts-bundle": "workspace:*", "@types/chai": "^4.3.6", "@types/mocha": "^10.0.1", "@types/node": "^22.10.2", "@types/uuid": "^10.0.0", "c8": "^10.1.2", "chai": "^4.3.10", "copyfiles": "^2.1.0", "cross-env": "^7.0.3", "eslint": "^8.49.0", "mocha": "^10.8.2", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "~5.8.3"}, "scripts": {"build": "tsc -b -v . && pnpm dts-bundle", "build:binary": "echo 'Binary mode is not available for this package, falling back to normal build.' && pnpm build", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build junit-report*", "copy:fixtures": "copyfiles {lib,test}/**/*.json build", "dts-bundle": "xtrem-dts-bundle", "lint": "eslint -c .eslintrc.js --ext .ts lib test", "lint:filename": "eslint --no-eslintrc -c .eslintrc-filename.js \"**\"", "prebuild": "pnpm copy:fixtures", "test": "mocha --recursive --exit \"test/**/*@(-|.)test.ts\"", "test:ci": "JUNIT_REPORT_PATH=junit-report-datetime.xml JUNIT_REPORT_NAME='xtrem-datetime' c8 --reporter=lcov --reporter=json --reporter=cobertura --reporter=text mocha  --recursive --exit \"test/**/*@(-|.)test.ts\" --reporter mocha-jenkins-reporter"}, "c8": {"reporter": ["json", "lcov", "text-summary"], "extension": [".ts"], "exclude": ["**/*.d.ts", "**/*-test.ts"]}, "gitHead": "f0406ede1639145fdb322ebdf823bc87abddf4b8"}