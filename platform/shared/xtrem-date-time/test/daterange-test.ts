import { assert, expect } from 'chai';
import { DateRange, DateValue } from '../index';
import { setup } from './setup';

setup();
const { strictEqual } = assert;

const date = new Date('2023-04-25').toISOString().slice(0, 10);

describe(module.id, () => {
    it('basic', () => {
        assert.ok(
            DateRange.isDateRange(new DateRange(DateValue.make(2010, 10, 13), DateValue.make(2020, 6, 3))),
            'is date range',
        );
    });

    it('equality test', () => {
        strictEqual(
            new DateRange(DateValue.make(2010, 10, 13), DateValue.make(2020, 6, 3)).equals(
                new DateRange(DateValue.make(2010, 10, 13), DateValue.make(2020, 6, 3)),
            ),
            true,
            'equals itself',
        );

        strictEqual(
            new DateRange(DateValue.make(2010, 10, 13), DateValue.make(2020, 6, 3), true, false).equals(
                new DateRange(DateValue.make(2010, 10, 13), DateValue.make(2020, 6, 3), true, false),
            ),
            true,
            'equals itself with intervals',
        );
        strictEqual(
            new DateRange(DateValue.make(2010, 10, 13), DateValue.make(2020, 6, 3)).equals(
                new DateRange(DateValue.make(2010, 10, 13), DateValue.make(2020, 5, 1)),
            ),
            false,
            'Not equals without interval options',
        );
        strictEqual(
            new DateRange(DateValue.make(2010, 10, 13), DateValue.make(2020, 6, 3), true, false).equals(
                new DateRange(DateValue.make(2010, 10, 13), DateValue.make(2020, 6, 3), false, true),
            ),
            false,
            'Not equals with Intervals Options',
        );
        strictEqual(
            new DateRange(DateValue.make(2010, 10, 13), null).equals(new DateRange(DateValue.make(2010, 10, 13), null)),
            true,
            'Equals with infinity bounds',
        );
        strictEqual(
            new DateRange(DateValue.make(2010, 10, 13), null).equals(new DateRange(null, DateValue.make(2010, 10, 13))),
            false,
            'Not Equals with infinity bounds',
        );
        strictEqual(
            new DateRange(DateValue.make(2010, 10, 13), null).equals(
                new DateRange(DateValue.make(2010, 10, 11), DateValue.make(2010, 10, 13)),
            ),
            false,
            'Not Equals with infinity bounds',
        );
    });
    it('includedStart and excludedEnd', () => {
        strictEqual(
            new DateRange(DateValue.make(2010, 10, 13), DateValue.make(2020, 6, 3)).includedStart?.value,
            DateValue.make(2010, 10, 13).value,
            'startDay included by default',
        );
        strictEqual(
            new DateRange(DateValue.make(2010, 10, 13), DateValue.make(2020, 6, 1)).excludedEnd?.value,
            DateValue.make(2020, 6, 1).value,
            'endDate excluded by default',
        );
        strictEqual(
            new DateRange(DateValue.make(2010, 10, 13), DateValue.make(2020, 6, 3), true, false).includedStart?.value,
            DateValue.make(2010, 10, 14).value,
            'exclude startDay',
        );
        strictEqual(
            new DateRange(DateValue.make(2010, 10, 13), DateValue.make(2020, 6, 1), true, false).excludedEnd?.value,
            DateValue.make(2020, 6, 2).value,
            'include endDay',
        );
    });

    it('include day test', () => {
        strictEqual(
            new DateRange(DateValue.make(2010, 10, 13), DateValue.make(2020, 6, 3)).includes(
                DateValue.make(2015, 5, 13),
            ),
            true,
            'default exclusions - test lower bound',
        );

        strictEqual(
            new DateRange(DateValue.make(2010, 10, 13), DateValue.make(2020, 6, 3)).includes(
                DateValue.make(2020, 6, 3),
            ),
            false,
            'default exclusions - test upper bound',
        );

        strictEqual(
            new DateRange(DateValue.make(2010, 10, 13), DateValue.make(2020, 6, 3), true, false).includes(
                DateValue.make(2010, 10, 13),
            ),
            false,
            'exclude start and include end',
        );

        strictEqual(
            new DateRange(DateValue.make(2010, 10, 13), DateValue.make(2020, 6, 3), false, false).includes(
                DateValue.make(2011, 6, 3),
            ),
            true,
            'Date included',
        );

        strictEqual(
            new DateRange(DateValue.make(2010, 10, 13), DateValue.make(2020, 6, 3)).includes(
                DateValue.make(2030, 6, 3),
            ),
            false,
            "Doesn't include out range day",
        );

        strictEqual(
            new DateRange(DateValue.make(2010, 10, 13), null).includes(DateValue.make(2030, 6, 3)),
            true,
            'Includes date with upper infinity',
        );
        strictEqual(
            new DateRange(DateValue.make(2010, 10, 13), null).includes(DateValue.make(2010, 10, 13)),
            true,
            'Includes start date with upper infinity',
        );
        strictEqual(
            new DateRange(null, DateValue.make(2010, 10, 13)).includes(DateValue.make(2000, 6, 3)),
            true,
            'Includes date with lower infinity',
        );
        strictEqual(
            new DateRange(null, DateValue.make(2010, 10, 13), false, false).includes(DateValue.make(1010, 10, 13)),
            true,
            'Includes endDate date with lower infinity',
        );
    });

    it('invalid range', () => {
        assert.throw(
            () => {
                // eslint-disable-next-line no-new
                new DateRange(DateValue.make(2020, 6, 1), DateValue.make(2020, 6, 1));
            },
            `Invalid range: included start ${DateValue.make(
                2020,
                6,
                1,
            ).toString()} should not be greater than excluded end ${DateValue.make(2020, 6, 1).toString()}`,
        );
    });

    it('difference in days', () => {
        strictEqual(
            new DateRange(DateValue.make(2020, 6, 1), DateValue.make(2020, 6, 2)).durationInDays,
            1,
            'Should compute difference in days',
        );

        strictEqual(
            new DateRange(DateValue.make(2020, 6, 1), DateValue.make(2020, 6, 3), true, true).durationInDays,
            1,
            'Should compute difference in days with startDate and endDate exclusions',
        );

        strictEqual(
            new DateRange(DateValue.make(2020, 6, 1), DateValue.make(2020, 6, 4), true, false).durationInDays,
            3,
            'Should compute difference in days with startDate exclusion and endDate inclusion',
        );

        strictEqual(
            new DateRange(DateValue.make(2020, 6, 1), DateValue.make(2020, 6, 14)).durationInDays,
            13,
            'Should compute difference in days',
        );

        strictEqual(
            new DateRange(DateValue.make(2020, 6, 1), null).durationInDays,
            Infinity,
            'Should compute difference in days for upper infinite bound',
        );

        strictEqual(
            new DateRange(null, DateValue.make(2020, 6, 1)).durationInDays,
            Infinity,
            'Should compute difference in days for lower infinite bound',
        );
    });
    it('exclusion range to string', () => {
        // convert to string
        strictEqual(
            new DateRange(DateValue.make(2020, 6, 1), DateValue.make(2020, 6, 3), true, true).toString(),
            '(2020-06-01,2020-06-03)',
            'Convert to string with exclude',
        );

        strictEqual(
            new DateRange(DateValue.make(2020, 6, 1), DateValue.make(2020, 6, 3), false, true).toString(),
            '[2020-06-01,2020-06-03)',
            'Convert to string with exclude end',
        );

        strictEqual(
            new DateRange(DateValue.make(2020, 6, 1), DateValue.make(2020, 6, 3), true, false).toString(),
            '(2020-06-01,2020-06-03]',
            'Convert to string with exclude start',
        );
    });

    it('string parse', () => {
        strictEqual(
            DateRange.parse('[2019-01-12,2020-01-13)').toString(),
            '[2019-01-12,2020-01-13)',
            'Parse date range',
        );
        strictEqual(
            DateRange.parse('[,2020-01-13)').toString(),
            '[,2020-01-13)',
            'Parse date range with lower infinity bound',
        );
        strictEqual(
            DateRange.parse('[2019-01-12,)').toString(),
            '[2019-01-12,)',
            'Parse date range with upper infinity bound',
        );
    });

    it('Included end and excluded start', () => {
        strictEqual(DateRange.parse('[2019-01-12,)').excludedStart?.toString(), '2019-01-11', 'Excluded start value');
        strictEqual(DateRange.parse('[2019-01-12,)').includedStart?.toString(), '2019-01-12', 'Included start value');
        strictEqual(DateRange.parse('(2019-01-12,)').includedStart?.toString(), '2019-01-13', 'Included start value');
        strictEqual(DateRange.parse('(2019-01-12,)').excludedStart?.toString(), '2019-01-12', 'Excluded start value');
        strictEqual(DateRange.parse('(,2019-01-12)').includedEnd?.toString(), '2019-01-11', 'Included end value');
        strictEqual(DateRange.parse('(,2019-01-12)').excludedEnd?.toString(), '2019-01-12', 'Excluded end value');
        strictEqual(DateRange.parse('(,2019-01-12]').includedEnd?.toString(), '2019-01-12', 'Included end value');
        strictEqual(DateRange.parse('(,2019-01-12]').excludedEnd?.toString(), '2019-01-13', 'Excluded end value');
    });

    it('string make null', () => {
        strictEqual(new DateRange(null, null).toString(), '[,)', 'Parse null date range');
    });

    it('any date should be included in start=null & end=null', () => {
        const dr = new DateRange(null, null);
        assert.deepEqual(dr.includes(DateValue.make(2023, 4, 25)), true); // wrongly fails !
    });

    it('Infinity bounds', () => {
        strictEqual(
            new DateRange(DateValue.make(2020, 6, 1), null).toString(),
            '[2020-06-01,)',
            'Create a date range with endDate infinity bound',
        );

        strictEqual(
            new DateRange(null, DateValue.make(2020, 6, 1)).toString(),
            '[,2020-06-01)',
            'Create a date range with startDate infinity bound',
        );

        strictEqual(
            DateRange.parse('[2020-06-01,)').toString(),
            '[2020-06-01,)',
            'Parse date with upper bound infinity',
        );

        strictEqual(
            DateRange.parse('[,2020-06-01)').toString(),
            '[,2020-06-01)',
            'Parse date with lower bound infinity',
        );
    });

    it('same day', () => {
        const range = DateRange.getDateRange({ date, range: 'same-day' });
        expect(range.start?.toString()).to.equal('2023-04-25');
        expect(range.end?.toString()).to.equal('2023-04-25');
    });

    it('previous day', () => {
        const range = DateRange.getDateRange({ date, range: 'previous-day' });
        expect(range.start?.toString()).to.equal('2023-04-24');
        expect(range.end?.toString()).to.equal('2023-04-24');
    });

    it('same month', () => {
        const range = DateRange.getDateRange({ date, range: 'same-month' });
        expect(range.start?.toString()).to.equal('2023-04-01');
        expect(range.end?.toString()).to.equal('2023-04-30');
    });

    it('previous month', () => {
        const range = DateRange.getDateRange({ date, range: 'previous-month' });
        expect(range.start?.toString()).to.equal('2023-03-01');
        expect(range.end?.toString()).to.equal('2023-03-31');
    });

    it('same year', () => {
        const range = DateRange.getDateRange({ date, range: 'same-year' });
        expect(range.start?.toString()).to.equal('2023-01-01');
        expect(range.end?.toString()).to.equal('2023-12-31');
    });

    it('previous year', () => {
        const range = DateRange.getDateRange({ date, range: 'previous-year' });
        expect(range.start?.toString()).to.equal('2022-01-01');
        expect(range.end?.toString()).to.equal('2022-12-31');
    });

    it('last 7 days', () => {
        const range = DateRange.getDateRange({ date, range: 'last-7-days' });
        expect(range.start?.toString()).to.equal('2023-04-18');
        expect(range.end?.toString()).to.equal('2023-04-25');
    });

    it('last 30 days', () => {
        const range = DateRange.getDateRange({ date, range: 'last-30-days' });
        expect(range.start?.toString()).to.equal('2023-03-26');
        expect(range.end?.toString()).to.equal('2023-04-25');
    });
    it('previous week with en-US locale', () => {
        const locale = 'en-US';
        const range = DateRange.getDateRange({ date, range: 'previous-week', locale });

        expect(range.includedStart?.toString()).to.equal('2023-04-16');
        expect(range.includedEnd?.toString()).to.equal('2023-04-22');
    });

    it('previous week with fr-FR locale', () => {
        const locale = 'fr-FR';
        const range = DateRange.getDateRange({ date, range: 'previous-week', locale });

        expect(range.includedStart?.toString()).to.equal('2023-04-17');
        expect(range.includedEnd?.toString()).to.equal('2023-04-23');
    });
    it('same week with en-US locale', () => {
        const locale = 'en-US';
        const range = DateRange.getDateRange({ date, range: 'same-week', locale });

        expect(range.includedStart?.toString()).to.equal('2023-04-23');
        expect(range.includedEnd?.toString()).to.equal('2023-04-29');
    });

    it('same week with fr-FR locale', () => {
        const locale = 'fr-FR';
        const range = DateRange.getDateRange({ date, range: 'same-week', locale });

        expect(range.includedStart?.toString()).to.equal('2023-04-24');
        expect(range.includedEnd?.toString()).to.equal('2023-04-30');
    });
    it('next week with en-US locale', () => {
        const locale = 'en-US';
        const range = DateRange.getDateRange({ date, range: 'next-week', locale });

        expect(range.includedStart?.toString()).to.equal('2023-04-30');
        expect(range.includedEnd?.toString()).to.equal('2023-05-06');
    });

    it('next week with fr-FR locale', () => {
        const locale = 'fr-FR';
        const range = DateRange.getDateRange({ date, range: 'next-week', locale });

        expect(range.includedStart?.toString()).to.equal('2023-05-01');
        expect(range.includedEnd?.toString()).to.equal('2023-05-07');
    });
    it('next month', () => {
        const range = DateRange.getDateRange({ date, range: 'next-month' });
        expect(range.start?.toString()).to.equal('2023-05-01');
        expect(range.end?.toString()).to.equal('2023-05-31');
    });

    it('next year', () => {
        const range = DateRange.getDateRange({ date, range: 'next-year' });
        expect(range.start?.toString()).to.equal('2024-01-01');
        expect(range.end?.toString()).to.equal('2024-12-31');
    });
});
