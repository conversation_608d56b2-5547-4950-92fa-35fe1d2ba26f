import { assert } from 'chai';
import { IntegerRange } from '../index';
import { setup } from './setup';

setup();
const { strictEqual } = assert;

const intRange = new IntegerRange(1, 4);
const intRangeExcludeStart = new IntegerRange(1, 4, true);
const intRangeIncludeEnd = new IntegerRange(1, 4, false, false);
const intRangeIncludeStartAndEnd = new IntegerRange(1, 4, false, false);
const intRangeInfinityStartBound = new IntegerRange(null, 4);
const intRangeInfinityEndBound = new IntegerRange(1, null);

// eslint-disable-next-line mocha/no-exclusive-tests
describe(module.id, () => {
    it('basic', () => {
        assert.ok(IntegerRange.isIntegerRange(intRange), 'is integer range');
        strictEqual(intRange.excludedEnd, 4, 'exclude end value');
        strictEqual(intRangeIncludeEnd.excludedEnd, 5, 'exclude end value with inclusion');
        strictEqual(intRangeExcludeStart.includedStart, 2, 'included start value with exclusion');
        strictEqual(intRange.incrementsByRangeUnit(3), 4, 'increment by range unit');
        strictEqual(intRange.incrementsByRangeUnit(3, -10), -7, 'increment with negative step');
    });
    it('equal', () => {
        strictEqual(intRange.equals(intRange), true, 'is equal integer range');
        strictEqual(intRangeExcludeStart.equals(intRangeExcludeStart), true, 'is equal with start exclusion');
        strictEqual(intRangeIncludeEnd.equals(intRangeIncludeEnd), true, 'is equal with end included');
        strictEqual(
            intRangeIncludeStartAndEnd.equals(intRangeIncludeStartAndEnd),
            true,
            'is equal with start and end included',
        );
        strictEqual(intRange.equals(intRangeExcludeStart), false, 'is not equal integer range');
        strictEqual(intRangeExcludeStart.equals(intRangeIncludeEnd), false, 'is not equal integer range');
    });
    it('includes', () => {
        strictEqual(intRange.includes(2), true, 'is equal integer range');
        strictEqual(intRangeExcludeStart.includes(1), false, 'excluded start is not included');
        strictEqual(intRangeIncludeEnd.includes(4), true, 'include end is included');
    });
    it('parse', () => {
        strictEqual(IntegerRange.parse('[1,4)').toString(), '[1,4)', 'parse with default inclusion');
        strictEqual(IntegerRange.parse('[1,4]').toString(), '[1,4]', 'parse with end included');
        strictEqual(IntegerRange.parse('(1,4]').toString(), '(1,4]', 'parse with start excluded and end included');
        strictEqual(IntegerRange.parse('(1,4)').toString(), '(1,4)', 'parse with start and end  excluded');
        strictEqual(IntegerRange.parse('[,4)').toString(), '(,4)', 'Parse range with lower infinity bound');
        strictEqual(IntegerRange.parse('[1,)').toString(), '[1,)', 'Parse range with upper infinity bound');
        assert.throw(() => {
            // eslint-disable-next-line no-new
            IntegerRange.parse('(1,4ds)');
        }, 'Invalid integer range format: (1,4ds)');
    });
    it('invalid range', () => {
        assert.throw(() => {
            // eslint-disable-next-line no-new
            new IntegerRange(3, 1);
        }, 'Invalid range: included start 3 should not be greater than excluded end 1');
    });
    it('infinity bounds', () => {
        strictEqual(intRangeInfinityStartBound.toString(), '[,4)', 'infinity start bound');
        strictEqual(intRangeInfinityEndBound.toString(), '[1,)', 'infinity end bound');
    });
});
