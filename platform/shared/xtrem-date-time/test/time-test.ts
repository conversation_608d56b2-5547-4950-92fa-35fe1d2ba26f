import { assert, expect } from 'chai';
import { time } from '../index';
import { overrideNow } from '../lib/datetime';
import { setup } from './setup';

setup();

const { strictEqual } = assert;

function equal(v: any, s: string, message?: string) {
    assert.equal(v.toString(), s, message);
}

describe(module.id, () => {
    before(() => {
        process.env.TZ = 'Europe/Paris';
    });
    after(() => {
        // Caution: assigning undefined virtually sets TZ to GMT.
        // We have to delete the key to fully revert TZ to the process env.
        delete process.env.TZ;
    });

    it('basic', () => {
        const t = time.make(18, 23, 7);
        assert.ok(time.isTime(t), 'is time');
        assert.ok(!time.isTime('hello'), 'not is time');
        strictEqual(t.hour, 18, 'hour ok');
        strictEqual(t.minute, 23, 'hour ok');
        strictEqual(t.second, 7, 'minute ok');
        equal(t, '18:23:07');
    });

    it('bounds', () => {
        let t = time.make(0, 0, 0);
        strictEqual(t.hour, 0, 'min hour ok');
        strictEqual(t.minute, 0, 'min hour ok');
        strictEqual(t.second, 0, 'min minute ok');
        equal(t, '00:00:00');
        t = time.make(23, 59, 59);
        strictEqual(t.hour, 23, 'max hour ok');
        strictEqual(t.minute, 59, 'max hour ok');
        strictEqual(t.second, 59, 'max minute ok');
        equal(t, '23:59:59');
    });

    it('compare', () => {
        const t = time.make(18, 23, 7);
        strictEqual(t.equals(time.make(18, 23, 7)), true, 'equals itself');
        strictEqual(t.equals(time.make(18, 23, 6)), false, 'not equals other');
        // eslint-disable-next-line eqeqeq
        strictEqual((t as any) == '18:23:07', true, '== this.toString()');
        // eslint-disable-next-line eqeqeq
        strictEqual((t as any) == '18:23:06', false, '!= other.toString()');
        strictEqual(t.compare(time.make(18, 23, 6)) > 0, true, 'compare > 0');
        strictEqual(t.compare(time.make(18, 23, 8)) < 0, true, 'compare < 0');
        strictEqual(t.compare(time.make(18, 23, 7)) === 0, true, 'compare == 0');
        strictEqual(t.isBetween(time.make(18, 23, 7), time.make(18, 23, 7)), true, 'between same');
        strictEqual(t.isBetween(time.make(18, 23, 6), time.make(18, 23, 8)), true, 'between different');
        strictEqual(t.isBetween(time.make(18, 23, 4), time.make(18, 23, 6)), false, 'not between before');
        strictEqual(t.isBetween(time.make(18, 23, 8), time.make(18, 23, 10)), false, 'not between after');
    });

    it('begs and ends', () => {
        const t = time.make(18, 23, 7);
        equal(t.begOfDay(), '00:00:00', 'beg of day');
        equal(t.endOfDay(), '23:59:59', 'end of day');
        equal(t.begOfHour(), '18:00:00', 'beg of day');
        equal(t.endOfHour(), '18:59:59', 'end of day');
        equal(t.begOfMinute(), '18:23:00', 'beg of day');
        equal(t.endOfMinute(), '18:23:59', 'end of day');
    });

    it('add', () => {
        const t = time.make(18, 23, 7);
        equal(t.addHours(3), '21:23:07', '+3 hours');
        equal(t.addHours(-3), '15:23:07', '-3 hours');
        equal(t.addMinutes(90), '19:53:07', '+90 mins');
        equal(t.addMinutes(-90), '16:53:07', '-90 mins');
        equal(t.addSeconds(90), '18:24:37', '+90 secs');
        equal(t.addSeconds(-90), '18:21:37', '-90 secs');
        equal(t.addHours(24), '18:23:07', '+24 hours');
        equal(t.addHours(-24), '18:23:07', '-24 hours');
        equal(t.addMinutes(24 * 60), '18:23:07', '+24 hours (mins)');
        equal(t.addMinutes(-24 * 60), '18:23:07', '-24 hours (mins)');
        equal(t.addSeconds(24 * 3600), '18:23:07', '+24 hours (secs)');
        equal(t.addSeconds(-24 * 3600), '18:23:07', '-24 hours (secs)');
    });

    it('diff', () => {
        const t = time.make(18, 23, 7);
        strictEqual(t.secondsDiff(time.make(17, 23, 7)), 3600, 'seconds diff +3600');
        strictEqual(t.secondsDiff(time.make(19, 23, 7)), -3600, 'seconds diff -3600');
    });

    it('parse', () => {
        // WARNING tests with AM/PM may not work correctly without setting locale

        let t = time.parse('18:23:07');
        strictEqual(t.hour, 18, 'parse check hour (no format)');
        equal(t, '18:23:07', 'parse check all (no format)');

        t = time.parse('04:31:16 PM', 'en-GB', 'hh:mm:ss A');
        strictEqual(t.hour, 16, 'parse check hour (hh:mm:ss A)');
        strictEqual(t.minute, 31, 'parse check minute (hh:mm:ss A)');
        strictEqual(t.second, 16, 'parse check second (hh:mm:ss A)');
        strictEqual(t.value, 59476, 'should return time value');

        t = time.parse('16.31.16', 'en-GB', 'H.mm.ss');
        strictEqual(t.hour, 16, 'parse check hour (H.mm.ss)');
        strictEqual(t.minute, 31, 'parse check minute (H.mm.ss)');
        strictEqual(t.second, 16, 'parse check second (H.mm.ss)');

        t = time.parse("Time is : 12'31'16 AM", 'en-GB', "[Time is :] h[']mm[']ss A");
        strictEqual(t.hour, 0, 'parse check hour (format with literal and quotes)');
        strictEqual(t.minute, 31, 'parse check minute (format with literal and quotes)');
        strictEqual(t.second, 16, 'parse check second (format with literal and quotes)');

        strictEqual(
            time.parse("L'heure est : 12'31'16 AM", 'en-GB', "[L'heure est :] h[']mm[']ss A").toString(),
            '00:31:16',
            'parse with quotes',
        );
    });

    // note: cannot compare exactly
    it('today and fromJsDate', () => {
        const now = time.now();
        const jsNow = new Date();
        const x = now.hour * 3600 + now.minute * 60 + now.second;
        const y = jsNow.getHours() * 3600 + jsNow.getMinutes() * 60 + jsNow.getSeconds();
        assert.ok(x <= y && y < x + 1, 'now matches new Date()');
        equal(`${time.fromJsDate(new Date(2010, 9, 13, 18, 23, 7))}`, '18:23:07', 'fromJsDate (local summer)');
        equal(`${time.fromJsDate(new Date(2010, 11, 13, 18, 23, 7))}`, '18:23:07', 'fromJsDate (local winter)');
        equal(`${time.fromJsDate(new Date(2010, 9, 13, 18, 23, 7), 'UTC')}`, '16:23:07', 'fromJsDate (utc summer)');
        equal(`${time.fromJsDate(new Date(2010, 11, 13, 18, 23, 7), 'UTC')}`, '17:23:07', 'fromJsDate (utc winter)');
    });

    it('format', () => {
        // WARNING tests with AM/PM may not work correctly without setting locale
        strictEqual(time.make(18, 23, 7).toString(), '18:23:07', 'no format');
        strictEqual(time.make(18, 23, 7).format('en-GB', 'HH:mm:ss'), '18:23:07', 'format basic');
        strictEqual(time.make(18, 23, 7).format('en-GB', 'h:mm:ss A'), '6:23:07 PM', '(1/2) format with AM/PM mode');
        strictEqual(time.make(7, 23, 7).format('en-GB', 'h.mm.ss A'), '7.23.07 AM', '(2/2) format with AM/PM mode');
        strictEqual(
            time.make(18, 23, 7).format('en-GB', "[Time is :] h[']mm[']ss A"),
            "Time is : 6'23'07 PM",
            'format with literal and quotes',
        );
        strictEqual(time.make(18, 23, 7).format('en-GB', 'H:mm:ss'), '18:23:07', 'format basic H');
        strictEqual(time.make(0, 23, 7).format('en-GB', 'h:mm:ss'), '12:23:07', 'format with 0 hour');
        strictEqual(time.make(0, 23, 7).format('en-GB', 'hh:mm:ss'), '12:23:07', 'format with 0 double hour');
        strictEqual(time.make(18, 23, 7).format('en-GB', 'hh:mm:ss'), '06:23:07', 'format with 0 double hour');
    });

    it('format errors', () => {
        expect(() => {
            time.make(2010, 10, 3).format('en-GB', 'HHH:mm:ss t');
        }).to.throw("Format of directive 'HHH' incorrect in 'HHH:mm:ss t'", 'hour H format error');

        expect(() => {
            time.make(2010, 10, 3).format('en-GB', 'hhh:mm:ss A');
        }).to.throw("Format of directive 'hhh' incorrect in 'hhh:mm:ss A'", 'hour h format error');

        expect(() => {
            time.make(2010, 10, 3).format('en-GB', 'HH:m:ss A');
        }).to.throw("Format of directive 'm' incorrect in 'HH:m:ss A'", 'minute m format error');

        expect(() => {
            time.make(2010, 10, 3).format('en-GB', 'HH:mm:s A');
        }).to.throw("Format of directive 's' incorrect in 'HH:mm:s A'", 'second s format error');

        expect(() => {
            time.make(2010, 10, 3).format('en-GB', 'HH:mm:ss AA');
        }).to.throw("Format of directive 'AA' incorrect in 'HH:mm:ss AA'", 'time-frame A format error');
    });

    it('TimeZone', () => {
        overrideNow(`2024-02-07 21:00:00Z`);
        try {
            // We'll get different times in different time zones
            strictEqual(time.now('UTC').toString(), '21:00:00');
            strictEqual(time.now().toString(), '22:00:00');
            strictEqual(time.now('America/Phoenix').toString(), '14:00:00');
            strictEqual(time.now('Asia/Tokyo').toString(), '06:00:00');
        } finally {
            overrideNow(null);
        }
    });
});
