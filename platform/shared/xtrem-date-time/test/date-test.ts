import { assert } from 'chai';
import { Datetime, date } from '../index';
import { WeekDay } from '../lib/date';
import { overrideNow } from '../lib/datetime';
import { setup } from './setup';

setup();
const { strictEqual } = assert;

function equal(v: any, s: string, message: string) {
    assert.equal(v.toString(), s, message);
}

describe(module.id, () => {
    it('basic', () => {
        const d = date.make(2010, 10, 13);
        assert.ok(date.isDate(d), 'is date');
        assert.ok(!date.isDate('hello'), 'not is date');
        strictEqual(d.year, 2010, 'year ok');
        strictEqual(d.month, 10, 'month ok');
        strictEqual(d.day, 13, 'day ok');
        strictEqual(d.week, 41, 'week ok');
        strictEqual(d.weekDay, WeekDay.wednesday, 'weekday ok');
        strictEqual(d.yearDay, 286, 'yearday ok');
        strictEqual(d.epoch, 1286928000, 'epoch ok');
        strictEqual(d.toString(), '2010-10-13', 'toString ok');
    });

    it('far in the past', () => {
        let d = date.make(1000, 1, 1);
        strictEqual(d.year, 1000, 'year ok');
        strictEqual(d.month, 1, 'month ok');
        strictEqual(d.day, 1, 'day ok');
        // don't test weekday cause we don't handle gregorian/julian gap
        strictEqual(d.yearDay, 1, 'yearday ok');
        strictEqual(d.toString(), '1000-01-01', 'toString ok');
        d = d.addDays(364);
        strictEqual(d.year, 1000, 'year ok');
        strictEqual(d.month, 12, 'month ok');
        strictEqual(d.day, 31, 'day ok');
        strictEqual(d.yearDay, 365, 'yearday ok');
        strictEqual(d.toString(), '1000-12-31', 'toString ok');
        d = d.addDays(1);
        strictEqual(d.year, 1001, 'year ok');
        strictEqual(d.month, 1, 'month ok');
        strictEqual(d.day, 1, 'day ok');
        strictEqual(d.yearDay, 1, 'yearday ok');
        strictEqual(d.toString(), '1001-01-01', 'toString ok');
    });

    it('far in the future', () => {
        let d = date.make(9999, 1, 1);
        strictEqual(d.year, 9999, 'year ok');
        strictEqual(d.month, 1, 'month ok');
        strictEqual(d.day, 1, 'day ok');
        strictEqual(d.weekDay, WeekDay.friday, 'weekday ok');
        strictEqual(d.yearDay, 1, 'yearday ok');
        strictEqual(d.toString(), '9999-01-01', 'toString ok');
        d = d.addDays(364);
        strictEqual(d.toString(), '9999-12-31', 'toString ok');
    });

    it('leap years', () => {
        strictEqual(date.isLeap(2000), true, '2000 leap');
        strictEqual(date.isLeap(2001), false, '2001 not leap');
        strictEqual(date.isLeap(2002), false, '2002 not leap');
        strictEqual(date.isLeap(2003), false, '2003 not leap');
        strictEqual(date.isLeap(2004), true, '2004 leap');
        strictEqual(date.isLeap(2096), true, '2096 leap');
        strictEqual(date.isLeap(2100), false, '2100 not leap');
        strictEqual(date.isLeap(2200), false, '2200 not leap');
        strictEqual(date.isLeap(2300), false, '2300 not leap');
        strictEqual(date.isLeap(2400), true, '2400 leap');
        strictEqual(date.make(2000, 2, 28).addDays(1).toString(), '2000-02-29', 'Feb 29 2000');
        strictEqual(date.make(2000, 2, 29).addDays(1).toString(), '2000-03-01', 'Mar 01 2000');
        strictEqual(date.make(2001, 2, 28).addDays(1).toString(), '2001-03-01', 'Mar 01 2001');
        strictEqual(date.make(2004, 2, 28).addDays(1).toString(), '2004-02-29', 'Feb 29 2004');
        strictEqual(date.make(2004, 2, 29).addDays(1).toString(), '2004-03-01', 'Mar 01 2004');
        strictEqual(date.make(2100, 2, 28).addDays(1).toString(), '2100-03-01', 'Mar 01 2100');
        strictEqual(date.make(2000, 10, 13).isLeapYear(), true, '2000 day leap');
        strictEqual(date.make(2010, 10, 13).isLeapYear(), false, '2010 day leap');
    });

    it('compare', () => {
        strictEqual(date.make(2010, 10, 13).equals(date.make(2010, 10, 13)), true, 'equals itself');
        strictEqual(date.make(2010, 10, 13).equals(date.make(2010, 10, 12)), false, 'not equals other');
        // eslint-disable-next-line eqeqeq
        strictEqual((date.make(2010, 10, 13) as any) == '2010-10-13', true, '== this.toString()');
        // eslint-disable-next-line eqeqeq
        strictEqual((date.make(2010, 10, 13) as any) == '2010-10-12', false, '!= other.toString()');
        strictEqual(date.make(2010, 10, 13).compare(date.make(2010, 10, 12)) > 0, true, 'compare > 0');
        strictEqual(date.make(2010, 10, 13).compare(date.make(2010, 10, 14)) < 0, true, 'compare < 0');
        strictEqual(date.make(2010, 10, 13).compare(date.make(2010, 10, 13)) === 0, true, 'compare == 0');
        strictEqual(
            date.make(2010, 10, 13).isBetween(date.make(2010, 10, 13), date.make(2010, 10, 13)),
            true,
            'between same',
        );
        strictEqual(
            date.make(2010, 10, 13).isBetween(date.make(2010, 10, 12), date.make(2010, 10, 14)),
            true,
            'between different',
        );
        strictEqual(
            date.make(2010, 10, 13).isBetween(date.make(2010, 10, 10), date.make(2010, 10, 12)),
            false,
            'not between before',
        );
        strictEqual(
            date.make(2010, 10, 13).isBetween(date.make(2010, 10, 14), date.make(2010, 10, 16)),
            false,
            'not between after',
        );
    });

    it('isWorkDay', () => {
        strictEqual(date.make(2010, 10, 10).isWorkDay(), false, 'sunday not workday');
        strictEqual(date.make(2010, 10, 11).isWorkDay(), true, 'monday workday');
        strictEqual(date.make(2010, 10, 12).isWorkDay(), true, 'tuesday workday');
        strictEqual(date.make(2010, 10, 13).isWorkDay(), true, 'wednesday workday');
        strictEqual(date.make(2010, 10, 14).isWorkDay(), true, 'thursday workday');
        strictEqual(date.make(2010, 10, 15).isWorkDay(), true, 'friday workday');
        strictEqual(date.make(2010, 10, 16).isWorkDay(), false, 'saturday workday');
    });

    it('daysInMonth', () => {
        strictEqual(date.make(2010, 1, 5).daysInMonth(), 31, '31 days in January');
        strictEqual(date.make(2010, 2, 5).daysInMonth(), 28, '28 days in February (not leap)');
        strictEqual(date.make(2000, 2, 5).daysInMonth(), 29, '29 days in February (leap)');
        strictEqual(date.make(2010, 3, 5).daysInMonth(), 31, '31 days in March');
        strictEqual(date.make(2010, 4, 5).daysInMonth(), 30, '30 days in April');
        strictEqual(date.make(2010, 5, 5).daysInMonth(), 31, '31 days in May');
        strictEqual(date.make(2010, 6, 5).daysInMonth(), 30, '30 days in June');
        strictEqual(date.make(2010, 7, 5).daysInMonth(), 31, '31 days in July');
        strictEqual(date.make(2010, 8, 5).daysInMonth(), 31, '31 days in August');
        strictEqual(date.make(2010, 9, 5).daysInMonth(), 30, '30 days in September');
        strictEqual(date.make(2010, 10, 5).daysInMonth(), 31, '31 days in October');
        strictEqual(date.make(2010, 11, 5).daysInMonth(), 30, '30 days in November');
        strictEqual(date.make(2010, 12, 5).daysInMonth(), 31, '31 days in December');
    });

    it('begOfYear endOfYear', () => {
        equal(date.make(2010, 10, 13).begOfYear(), '2010-01-01', 'beg of year - Oct 13');
        equal(date.make(2010, 1, 1).begOfYear(), '2010-01-01', 'beg of year - Jan 1');
        equal(date.make(2010, 12, 31).begOfYear(), '2010-01-01', 'beg of year - Dec 31');
        equal(date.make(2010, 10, 13).endOfYear(), '2010-12-31', 'end of year - Oct 13');
        equal(date.make(2010, 1, 1).endOfYear(), '2010-12-31', 'end of year - Jan 1');
        equal(date.make(2010, 12, 31).endOfYear(), '2010-12-31', 'end of year - Dec 31');
    });

    it('begOfQuarter endOfQuarter', () => {
        equal(date.make(2010, 10, 13).begOfQuarter(), '2010-10-01', 'beg of quarter - Oct 13');
        equal(date.make(2010, 1, 1).begOfQuarter(), '2010-01-01', 'beg of quarter - Jan 1');
        equal(date.make(2010, 12, 31).begOfQuarter(), '2010-10-01', 'beg of quarter - Dec 31');
        equal(date.make(2010, 10, 13).endOfQuarter(), '2010-12-31', 'end of quarter - Oct 13');
        equal(date.make(2010, 1, 1).endOfQuarter(), '2010-03-31', 'end of quarter - Jan 1');
        equal(date.make(2010, 12, 31).endOfQuarter(), '2010-12-31', 'end of quarter - Dec 31');
    });

    it('begOfMonth endOfMonth', () => {
        equal(date.make(2010, 10, 13).begOfMonth(), '2010-10-01', 'beg of month - Oct 13');
        equal(date.make(2010, 1, 1).begOfMonth(), '2010-01-01', 'beg of month - Jan 1');
        equal(date.make(2010, 12, 31).begOfMonth(), '2010-12-01', 'beg of month - Dec 31');
        equal(date.make(2010, 10, 13).endOfMonth(), '2010-10-31', 'end of month - Oct 13');
        equal(date.make(2010, 1, 1).endOfMonth(), '2010-01-31', 'end of month - Jan 1');
        equal(date.make(2010, 2, 13).endOfMonth(), '2010-02-28', 'end of month - Feb not leap');
        equal(date.make(2012, 2, 13).endOfMonth(), '2012-02-29', 'end of month - Feb leap');
        equal(date.make(2010, 12, 31).endOfMonth(), '2010-12-31', 'end of month - Dec 31');
    });

    it('sameMonth', () => {
        equal(date.make(2010, 2, 13).sameMonth(1), '2010-02-01', 'same month - beg');
        equal(date.make(2010, 2, 13).sameMonth(13), '2010-02-13', 'same month - no change');
        equal(date.make(2010, 2, 13).sameMonth(31), '2010-02-28', 'same month - end 28');
        equal(date.make(2008, 2, 13).sameMonth(31), '2008-02-29', 'same month - end 29');
        equal(date.make(2010, 3, 13).sameMonth(31), '2010-03-31', 'same month - end 31');
    });

    it('pastDay futureDay', () => {
        equal(date.make(2010, 2, 13).pastDay(12), '2010-02-12', 'past day 1 before');
        equal(date.make(2010, 2, 13).pastDay(13), '2010-01-13', 'past day same');
        equal(date.make(2010, 2, 13).pastDay(14), '2010-01-14', 'past day 1 after');
        equal(date.make(2010, 2, 13).pastDay(1), '2010-02-01', 'past day beg of month');
        equal(date.make(2010, 2, 13).pastDay(31), '2010-01-31', 'past day end of month');
        equal(date.make(2010, 2, 13).pastDay(13, true), '2010-02-13', 'past day same');
        equal(date.make(2010, 2, 13).pastDay(14, true), '2010-01-14', 'past day 1 after');
        equal(date.make(2010, 3, 13).pastDay(31), '2010-02-28', 'past day non leap test');
        equal(date.make(2008, 3, 13).pastDay(31), '2008-02-29', 'past day leap test');
        equal(date.make(2010, 2, 13).futureDay(12), '2010-03-12', 'future day 1 before');
        equal(date.make(2010, 2, 13).futureDay(13), '2010-03-13', 'future day same');
        equal(date.make(2010, 2, 13).futureDay(14), '2010-02-14', 'future day 1 after');
        equal(date.make(2010, 2, 13).futureDay(1), '2010-03-01', 'future day beg of month');
        equal(date.make(2010, 3, 13).futureDay(31), '2010-03-31', 'future day end of month');
        equal(date.make(2010, 2, 13).futureDay(13, true), '2010-02-13', 'future day same');
        equal(date.make(2010, 2, 13).futureDay(12, true), '2010-03-12', 'future day 1 before');
        equal(date.make(2010, 2, 13).futureDay(31), '2010-02-28', 'future day non leap test');
        equal(date.make(2008, 2, 13).futureDay(31), '2008-02-29', 'future day leap test');
    });

    it('pastMonth futureMonth', () => {
        equal(date.make(2010, 2, 13).pastMonth(1), '2010-01-13', 'past month 1 back');
        equal(date.make(2010, 2, 13).pastMonth(9), '2009-09-13', 'past month 5 back');
        equal(date.make(2010, 2, 13).pastMonth(2), '2009-02-13', 'past month 12 back');
        equal(date.make(2010, 2, 13).pastMonth(3), '2009-03-13', 'past month 11 back');
        equal(date.make(2010, 2, 13).pastMonth(2, true), '2010-02-13', 'past month 0 back');
        equal(date.make(2010, 2, 13).pastMonth(3, true), '2009-03-13', 'past month 11 back');
        equal(date.make(2010, 3, 31).pastMonth(2), '2010-02-28', 'past month non leap test');
        equal(date.make(2008, 3, 31).pastMonth(2), '2008-02-29', 'past month leap test');
        equal(date.make(2010, 2, 13).futureMonth(3), '2010-03-13', 'future month 1 forward');
        equal(date.make(2010, 2, 13).futureMonth(7), '2010-07-13', 'future month 5 forward');
        equal(date.make(2010, 2, 13).futureMonth(2), '2011-02-13', 'future month 12 forward');
        equal(date.make(2010, 2, 13).futureMonth(1), '2011-01-13', 'future month 11 forward');
        equal(date.make(2010, 2, 13).futureMonth(2, true), '2010-02-13', 'future month 0 forward');
        equal(date.make(2010, 2, 13).futureMonth(1, true), '2011-01-13', 'future month 11 forward');
        equal(date.make(2010, 3, 31).futureMonth(2), '2011-02-28', 'future month non leap forward');
        equal(date.make(2011, 3, 31).futureMonth(2), '2012-02-29', 'future month non leap forward');
    });

    it('begOfWeek endOfWeek', () => {
        equal(date.make(2010, 12, 29).begOfWeek(), '2010-12-26', 'begOfWeek (Sunday start)');
        equal(date.make(2010, 12, 29).endOfWeek(), '2011-01-01', 'endOfWeek (Sunday start)');
        equal(date.make(2010, 12, 29).begOfWeek(WeekDay.monday), '2010-12-27', 'begOfWeek (Monday start)');
        equal(date.make(2010, 12, 29).endOfWeek(WeekDay.monday), '2011-01-02', 'endOfWeek (Monday start)');
        equal(date.make(2010, 12, 26).begOfWeek(), '2010-12-26', 'begOfWeek (low bound)');
        equal(date.make(2010, 12, 26).endOfWeek(), '2011-01-01', 'endOfWeek (low bound)');
        equal(date.make(2011, 1, 1).begOfWeek(), '2010-12-26', 'begOfWeek (high bound)');
        equal(date.make(2011, 1, 1).endOfWeek(), '2011-01-01', 'endOfWeek (high bound)');
    });

    it('sameWeek', () => {
        equal(date.make(2010, 12, 29).sameWeek(WeekDay.sunday), '2010-12-26', 'same week Sunday (Sunday start)');
        equal(date.make(2010, 12, 29).sameWeek(WeekDay.monday), '2010-12-27', 'same week Monday (Sunday start)');
        equal(date.make(2010, 12, 29).sameWeek(WeekDay.wednesday), '2010-12-29', 'same Wednesday (Sunday start)');
        equal(date.make(2010, 12, 29).sameWeek(WeekDay.saturday), '2011-01-01', 'same week Saturday (Sunday start)');
        equal(
            date.make(2010, 12, 29).sameWeek(WeekDay.sunday, WeekDay.monday),
            '2011-01-02',
            'same week Sunday (Monday start)',
        );
        equal(
            date.make(2010, 12, 29).sameWeek(WeekDay.monday, WeekDay.monday),
            '2010-12-27',
            'same week Monday (Monday start)',
        );
        equal(
            date.make(2010, 12, 29).sameWeek(WeekDay.wednesday, WeekDay.monday),
            '2010-12-29',
            'same Wednesday (Monday start)',
        );
        equal(
            date.make(2010, 12, 29).sameWeek(WeekDay.saturday, WeekDay.monday),
            '2011-01-01',
            'same week Saturday (Monday start)',
        );
    });

    it('weekNumber', () => {
        equal(date.make(2010, 12, 29).weekNumber(1), '52', '2010-12-29, week number 52');
    });

    it('pastWeekDay futureWeekDay', () => {
        equal(date.make(2010, 12, 29).pastWeekDay(WeekDay.tuesday), '2010-12-28', 'past week day 1 back');
        equal(date.make(2010, 12, 29).pastWeekDay(WeekDay.thursday), '2010-12-23', 'past week day 6 back');
        equal(date.make(2010, 12, 29).pastWeekDay(WeekDay.wednesday), '2010-12-22', 'past week day 7 back');
        equal(date.make(2010, 12, 29).pastWeekDay(WeekDay.wednesday, true), '2010-12-29', 'past week day 0 back');
        equal(date.make(2010, 12, 29).pastWeekDay(WeekDay.thursday, true), '2010-12-23', 'past week day 6 back');
        equal(date.make(2010, 12, 29).futureWeekDay(WeekDay.thursday), '2010-12-30', 'past week day 1 back');
        equal(date.make(2010, 12, 29).futureWeekDay(WeekDay.tuesday), '2011-01-04', 'past week day 6 back');
        equal(date.make(2010, 12, 29).futureWeekDay(WeekDay.wednesday), '2011-01-05', 'past week day 7 back');
        equal(date.make(2010, 12, 29).futureWeekDay(WeekDay.wednesday, true), '2010-12-29', 'past week day 0 back');
        equal(date.make(2010, 12, 29).futureWeekDay(WeekDay.tuesday, true), '2011-01-04', 'past week day 6 back');
    });

    it('add', () => {
        equal(date.make(2010, 10, 13).addYears(2000), '4010-10-13', 'add years + 2000');
        equal(date.make(2010, 10, 13).addYears(-20), '1990-10-13', 'add years -20');
        equal(date.make(2008, 2, 29).addYears(1), '2009-02-28', 'add years non leap test');
        equal(date.make(2008, 2, 29).addYears(4), '2012-02-29', 'add years leap test');
        equal(date.make(2010, 10, 13).addMonths(24000), '4010-10-13', 'add months + 24000');
        equal(date.make(2010, 10, 13).addMonths(-240), '1990-10-13', 'add months -240');
        equal(date.make(2010, 1, 31).addMonths(1), '2010-02-28', 'add months non leap test');
        equal(date.make(2008, 1, 31).addMonths(1), '2008-02-29', 'add months leap test');
        equal(date.make(2010, 10, 13).addDays(3653), '2020-10-13', 'add days +3653');
        equal(date.make(2010, 10, 13).addDays(-3652), '2000-10-13', 'add days -3652');
        equal(date.make(2010, 2, 28).addDays(1), '2010-03-01', 'add months non leap test');
        equal(date.make(2008, 2, 28).addDays(1), '2008-02-29', 'add months leap test');
        equal(date.make(2010, 10, 13).addWeeks(52), '2011-10-12', 'add weeks +52');
        equal(date.make(2010, 10, 13).addWeeks(-52), '2009-10-14', 'add weeks -52');
        equal(
            date.make(2000, 1, 1).add({
                years: 2,
                months: 3,
                weeks: 1,
                days: 5,
            }),
            '2002-04-13',
            'add members',
        );
    });

    it('diff', () => {
        strictEqual(date.make(2010, 10, 13).daysDiff(date.make(2000, 10, 13)), 3652, 'days diff +3652');
        strictEqual(date.make(2000, 10, 13).daysDiff(date.make(2010, 10, 13)), -3652, 'days diff -3652');
    });

    it('format', () => {
        equal(date.make(2010, 10, 13).toString(), '2010-10-13', 'no format');
        equal(date.make(2010, 10, 13).format('DD-MM-YYYY'), '13-10-2010', 'formatting basic');
        equal(date.make(2010, 10, 13).format('ddd-MM-YYYY', 'en-US'), 'Wed-10-2010', 'short day naming');

        equal(date.make(2010, 10, 13).format('dddd-MM-YYYY'), 'Wednesday-10-2010', 'full day naming');
        equal(date.make(2010, 10, 13).format("[l'année] YYYY", 'fr-FR'), "l'année 2010", 'formatting with quotes');
        equal(
            date.make(2010, 10, 13).format('[date du] DD MMMM YYYY [validée]', 'fr-FR'),
            'date du 13 Octobre 2010 validée',
            'formatting with literal at the end',
        );
        equal(
            date.make(1988, 3, 23).format('[Le] D/M/YYYY', 'fr-FR'),
            'Le 23/3/1988',
            'formatting with non alphanumeric character',
        );
        equal(
            date.make(1988, 3, 13).format("[Le] D-M [de l'année] YYYY", 'fr-FR'),
            "Le 13-3 de l'année 1988",
            'formatting with non alphanumeric character and double quotes',
        );
        equal(date.make(1988, 3, 13).format('[En] MMM', 'fr-FR'), 'En Mars', 'formatting with abbreviated month');
        equal(
            date.make(1988, 3, 13).format('[Le] D/M/YY', 'fr-FR'),
            'Le 13/3/88',
            '(1/3) formatting with directives -D- and -M-',
        );
        equal(
            date.make(1988, 1, 3).format('[Le] D/M/YY', 'fr-FR'),
            'Le 3/1/88',
            '(2/3) formatting with directives -D- and -M-',
        );
        equal(
            date.make(1988, 12, 3).format('[Le] D/M/YY', 'fr-FR'),
            'Le 3/12/88',
            '(3/3) formatting with directives -D- and -M-',
        );
        equal(
            date.make(1988, 12, 3).format("['L'opération du] D/M/YY [s'est bien déroulée']", 'fr-FR'),
            "'L'opération du 3/12/88 s'est bien déroulée'",
            'formatting with many quotes',
        );
        equal(
            date.make(1988, 12, 3).format("[La date :] D[']M[']YYYY", 'fr-FR'),
            "La date : 3'12'1988",
            'formatting with quotes between directives',
        );
        equal(date.make(2000, 1, 1).toString(), '2000-01-01', 'null date without format');
        equal(date.make(2000, 1, 1).format('DD/MM/YYYY', 'fr-FR'), '01/01/2000', 'null date with format');
        equal(date.make(2000, 2, 3).format('DD/MM/YY', 'fr-FR'), '03/02/00', 'tiny date with YY format');
    });

    it('format errors', () => {
        assert.throws(
            () => date.make(2010, 10, 13).format('ddddd-MM-YYYY', 'en-GB'),
            "Format of directive 'ddddd' incorrect in 'ddddd-MM-YYYY'",
        );
        assert.throws(
            () => date.make(2010, 10, 13).format('DD-MMMMM-YYYY', 'en-GB'),
            "Format of directive 'MMMMM' incorrect in 'DD-MMMMM-YYYY'",
        );
        assert.throws(
            () => date.make(2010, 10, 13).format('DD-MM-Y', 'en-GB'),
            "Format of directive 'Y' incorrect in 'DD-MM-Y'",
        );
    });

    it('parse errors', () => {
        assert.throws(
            () => date.parse('2010-10-1').toString(),
            "bad date format, expected 'YYYY-MM-DD', got '2010-10-1'",
        );

        assert.throws(
            () => date.parse('24/02/201', 'fr-FR', 'DD/MM/YYYY').toString(),
            'Date component does not contain enough digits, expected 4, found: 3.',
        );
    });

    it('parse', () => {
        equal(date.parse('2010-10-13').toString(), '2010-10-13', 'parse no format');
        equal(
            date.parse('02/10/2010', 'base', 'DD/MM/YYYY').toString(),
            '2010-10-02',
            'parse classic format DD/MM/YYYY',
        );
        equal(
            date.parse('Le 23 mars de 1988', 'fr-FR', '[Le] DD MMMM [de] YYYY').toString(),
            '1988-03-23',
            'parse simple sentence format',
        );
        equal(
            date.parse("Le 23 mars de l'an de grâce 1988", 'fr-FR', "[Le] DD MMMM [de l'an de grâce] YYYY").toString(),
            '1988-03-23',
            'parse simple sentence with double quotes ',
        );
        equal(
            date.parse('Le 23/03/1988', 'fr-FR', '[Le] DD/MM/YYYY').toString(),
            '1988-03-23',
            'parse format containing non alphanumeric characters',
        );
        equal(
            date.parse('Le 23-03 de 1988', 'fr-FR', '[Le] DD-MM [de] YYYY').toString(),
            '1988-03-23',
            'previous parse including double quotes',
        );
        equal(
            date.parse('Le 23-juil-1988', 'fr-FR', '[Le] DD-MMM-YYYY').toString(),
            '1988-07-23',
            'parse with abbreviated month : MMM',
        );
        equal(
            date.parse('Le 23-7-1988', 'fr-FR', '[Le] DD-M-YYYY').toString(),
            '1988-07-23',
            '(1/2) parse with month without a leading zero : M',
        );
        equal(
            date.parse('Le 23-12-1988', 'fr-FR', '[Le] DD-M-YYYY').toString(),
            '1988-12-23',
            '(2/2) parse with month without a leading zero : M',
        );
        equal(
            date.parse('Le 23-12-1988', 'fr-FR', '[Le] D-M-YYYY').toString(),
            '1988-12-23',
            '(1/2) parse with D and M',
        );
        equal(date.parse('Le 3-2-1988', 'fr-FR', '[Le] D-M-YYYY').toString(), '1988-02-03', '(2/2) parse with D and M');
        equal(date.parse('Le 3-2-88', 'fr-FR', '[Le] D-M-YY').toString(), '1988-02-03', 'parse with D and M and YY');
        equal(
            date.parse('3/12/88 achat ok', 'fr-FR', 'D/M/YY [achat ok]').toString(),
            '1988-12-03',
            'parse format ending with literal',
        );
        equal(
            date
                .parse(
                    "'L'opération du 24/4/03 s'est bien déroulée'",
                    'fr-FR',
                    "['L'opération du] D/M/YY [s'est bien déroulée']",
                )
                .toString(),
            '2003-04-24',
            'parse with double quotes at beginning, middle and end',
        );
        equal(
            date.parse('02jan1974', 'fr-FR', 'DDMMMYYYY').toString(),
            '1974-01-02',
            ' (1/2) parse format with directives not separated',
        );
        equal(
            date.parse('02011974', 'fr-FR', 'DDMMYYYY').toString(),
            '1974-01-02',
            ' (2/2) parse format with directives not separated',
        );
        equal(
            date.parse("La date : 24'4'2003", 'fr-FR', "[La date :] D[']M[']YYYY").toString(),
            '2003-04-24',
            'parse with double quotes between directives',
        );
    });

    // note: may fail because of race condition if run exactly at midnight
    it('today and jsDate', () => {
        strictEqual(date.today().day, new Date().getDate(), "today's day ok");
        strictEqual(date.today().month, new Date().getMonth() + 1, "today's month ok");
        strictEqual(date.today().year, new Date().getFullYear(), "today's year ok");
        strictEqual(date.today().equals(date.fromJsDate(new Date())), true, 'today equals new Date()');
    });

    it('JSON', () => {
        equal(JSON.stringify(date.make(2010, 10, 13)), '"2010-10-13"', 'JSON');
    });

    it('monthFromName', () => {
        equal(date.monthFromName('December', 'en-GB'), '12', 'month 12 from December string');
        equal(date.monthFromName('notAMonth', 'en-GB'), '-1', 'return -1 when passing wrong month string');
    });

    it('dayFromName', () => {
        equal(date.weekDayFromName('Friday', 'en-GB'), '5', 'day 5 from Friday string');
        equal(date.weekDayFromName('notADay', 'en-GB'), '-1', 'return -1 when passing wrong day string');
    });

    it('makeInWeek', () => {
        equal(date.makeInWeek(2010, 52, 3), '2010-12-29', '2010-12-29 from week and day in week values');
    });

    it('TimeZone', () => {
        // Make a datetime in the Phoenix time zone
        const phoenixDatetime = Datetime.make(2024, 2, 7, 0, 0, 0, 0, 'America/Phoenix');
        strictEqual(phoenixDatetime.date.toString(), '2024-02-07');

        const phoenixJsDate = new Date('07 Feb 2024 20:05:50 MST');
        // Test is executed with TZ=CET so this date is one day later in CET
        strictEqual(date.fromJsDate(phoenixJsDate).toString(), '2024-02-08');
        // This date is also one day later in UTC
        strictEqual(date.fromJsDate(phoenixJsDate, 'UTC').toString(), '2024-02-08');
        // And it remains correct in MST Phoenix time zone
        strictEqual(date.fromJsDate(phoenixJsDate, 'America/Phoenix').toString(), '2024-02-07');

        // The today mock sets the datetime at 12:00:00 UTC
        overrideNow(`2020-01-08T12:00:00Z`);
        try {
            // So we'll get the mock date in all time zones
            strictEqual(date.today('UTC').toString(), '2020-01-08');
            strictEqual(date.today().toString(), '2020-01-08');
            strictEqual(date.today('America/Phoenix').toString(), '2020-01-08');
            strictEqual(date.today('Asia/Tokyo').toString(), '2020-01-08');

            // But if we use the now mock we can get different today values
            overrideNow(`2020-01-08T05:00:00Z`);
            strictEqual(date.today('UTC').toString(), '2020-01-08');
            strictEqual(date.today().toString(), '2020-01-08');
            // Phoenix is till on previous day
            strictEqual(date.today('America/Phoenix').toString(), '2020-01-07');
            strictEqual(date.today('Asia/Tokyo').toString(), '2020-01-08');
        } finally {
            overrideNow(null);
        }
    });

    it('throws on invalid dates', () => {
        // Year must be [1000, 9999]
        assert.throws(() => date.make(999, 1, 2), 'Invalid date value 9990102: invalid year 999');
        assert.throws(() => date.parse('999-01-02'), "bad date format, expected 'YYYY-MM-DD'");
        assert.throws(() => date.parse('0999-01-02'), 'Invalid date value 9990102: invalid year 999');
        assert.throws(() => date.make(10000, 1, 2), 'Invalid date value 100000102: invalid year 10000');
        // Month must be [1, 12]
        assert.throws(() => date.make(2000, 0, 2), 'Invalid date value 20000002: invalid month 0');
        assert.throws(() => date.parse('2000-00-02'), 'Invalid date value 20000002: invalid month 0');
        assert.throws(() => date.make(2000, 13, 2), 'Invalid date value 20001302: invalid month 13');
        assert.throws(() => date.parse('2000-13-02'), 'Invalid date value 20001302: invalid month 13');
        // Day must be [1, daysInMonth(year, month)]
        assert.throws(() => date.make(2000, 2, 0), 'Invalid date value 20000200: invalid day 0');
        assert.throws(() => date.parse('2000-02-00'), 'Invalid date value 20000200: invalid day 0');
        assert.throws(() => date.make(2000, 2, 32), 'Invalid date value 20000232: invalid day 32');
        assert.throws(() => date.parse('2000-02-32'), 'Invalid date value 20000232: invalid day 32');
        assert.throws(() => date.make(2000, 2, 30), 'Invalid date value 20000230: invalid day 30');
        assert.throws(() => date.parse('2000-02-30'), 'Invalid date value 20000230: invalid day 30');
        assert.doesNotThrow(() => date.make(2000, 2, 29)); // Feb of 2000 has 29 days
        assert.doesNotThrow(() => date.parse('2000-02-29'));
        assert.throws(() => date.make(2001, 2, 29), 'Invalid date value 20010229: invalid day 29'); // Feb of 2001 only has 28 days
        assert.throws(() => date.parse('2001-02-29'), 'Invalid date value 20010229: invalid day 29');

        // A valid date must be accepted
        assert.doesNotThrow(() => date.make(2000, 10, 2));
        assert.doesNotThrow(() => date.parse('2000-10-02'));
    });
});
