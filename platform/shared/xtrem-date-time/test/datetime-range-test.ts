import { assert } from 'chai';
import { Datetime, DatetimeRange } from '../index';
import { setup } from './setup';

setup();
const { strictEqual } = assert;

describe(module.id, () => {
    it('basic', () => {
        assert.ok(
            DatetimeRange.isDatetimeRange(
                new DatetimeRange(Datetime.make(2010, 10, 13, 12, 11, 10), Datetime.make(2020, 6, 3)),
            ),
            'is datetime range',
        );
    });

    it('equality test', () => {
        strictEqual(
            new DatetimeRange(Datetime.make(2010, 10, 13, 0, 0, 0), Datetime.make(2020, 6, 3, 23, 59, 59)).equals(
                new DatetimeRange(Datetime.make(2010, 10, 13, 0, 0, 0), Datetime.make(2020, 6, 3, 23, 59, 59)),
            ),
            true,
            'equals itself',
        );

        strictEqual(
            new DatetimeRange(Datetime.make(2010, 10, 13, 0, 0, 0), Datetime.make(2020, 6, 3, 23, 59, 59)).equals(null),
            false,
            'equals to null',
        );

        strictEqual(
            new DatetimeRange(
                Datetime.make(2010, 10, 13, 0, 0, 0),
                Datetime.make(2020, 6, 3, 23, 59, 59),
                true,
                false,
            ).equals(
                new DatetimeRange(
                    Datetime.make(2010, 10, 13, 0, 0, 0),
                    Datetime.make(2020, 6, 3, 23, 59, 59),
                    true,
                    false,
                ),
            ),
            true,
            'equals itself with intervals',
        );
        strictEqual(
            new DatetimeRange(Datetime.make(2010, 10, 13, 0, 0, 0), Datetime.make(2020, 6, 3, 23, 59, 59)).equals(
                new DatetimeRange(Datetime.make(2010, 10, 13, 0, 0, 0), Datetime.make(2020, 5, 1, 23, 59, 59)),
            ),
            false,
            'Not equals without interval options',
        );
        strictEqual(
            new DatetimeRange(Datetime.make(2010, 10, 13), Datetime.make(2020, 6, 3), true, false).equals(
                new DatetimeRange(Datetime.make(2010, 10, 13), Datetime.make(2020, 6, 3), false, true),
            ),
            false,
            'Not equals with Intervals Options',
        );
        strictEqual(
            new DatetimeRange(Datetime.make(2010, 10, 13), null).equals(
                new DatetimeRange(Datetime.make(2010, 10, 13), null),
            ),
            true,
            'Equals with infinity bounds',
        );
        strictEqual(
            new DatetimeRange(Datetime.make(2010, 10, 13, 0, 0, 0), null).equals(
                new DatetimeRange(null, Datetime.make(2010, 10, 13, 23, 59, 59)),
            ),
            false,
            'Not Equals with infinity bounds',
        );
        strictEqual(
            new DatetimeRange(Datetime.make(2010, 10, 13), null).equals(
                new DatetimeRange(Datetime.make(2010, 10, 11), Datetime.make(2010, 10, 13, 23, 59, 59)),
            ),
            false,
            'Not Equals with infinity bounds',
        );
    });
    it('includedStart and excludedEnd', () => {
        strictEqual(
            new DatetimeRange(Datetime.make(2010, 10, 13, 0, 0, 0), Datetime.make(2020, 6, 3, 23, 59, 59)).includedStart
                ?.value,
            Datetime.make(2010, 10, 13, 0, 0, 0).value,
            'startDay included by default',
        );
        strictEqual(
            new DatetimeRange(Datetime.make(2010, 10, 13), Datetime.make(2020, 6, 1, 23, 59, 59)).excludedEnd?.value,
            Datetime.make(2020, 6, 1, 23, 59, 59).value,
            'endDate excluded by default',
        );
        strictEqual(
            new DatetimeRange(Datetime.make(2010, 10, 13, 0, 0, 0), Datetime.make(2020, 6, 3), true, false)
                .includedStart?.value,
            Datetime.make(2010, 10, 13).addMilliseconds(1).value,
            'exclude startDay',
        );
        strictEqual(
            new DatetimeRange(Datetime.make(2010, 10, 13), Datetime.make(2020, 6, 1), true, false).excludedEnd?.value,
            Datetime.make(2020, 6, 1).addMilliseconds(1).value,
            'include endDay',
        );
    });

    it('include day test', () => {
        strictEqual(
            new DatetimeRange(Datetime.make(2010, 10, 13, 0, 0, 0), Datetime.make(2020, 6, 3)).includes(
                Datetime.make(2015, 5, 13, 0, 0, 1),
            ),
            true,
            'default exclusions - test lower bound',
        );

        strictEqual(
            new DatetimeRange(Datetime.make(2010, 10, 13), Datetime.make(2020, 6, 3)).includes(
                Datetime.make(2020, 6, 3),
            ),
            false,
            'default exclusions - test upper bound',
        );

        strictEqual(
            new DatetimeRange(Datetime.make(2010, 10, 13), Datetime.make(2020, 6, 3), true, false).includes(
                Datetime.make(2010, 10, 13),
            ),
            false,
            'exclude start and include end',
        );

        strictEqual(
            new DatetimeRange(Datetime.make(2010, 10, 13), Datetime.make(2020, 6, 3), false, false).includes(
                Datetime.make(2011, 6, 3),
            ),
            true,
            'Date included',
        );

        strictEqual(
            new DatetimeRange(Datetime.make(2010, 10, 13), Datetime.make(2020, 6, 3)).includes(
                Datetime.make(2030, 6, 3),
            ),
            false,
            "Doesn't include out range day",
        );

        strictEqual(
            new DatetimeRange(Datetime.make(2010, 10, 13), null).includes(Datetime.make(2030, 6, 3)),
            true,
            'Includes date with upper infinity',
        );
        strictEqual(
            new DatetimeRange(Datetime.make(2010, 10, 13), null).includes(Datetime.make(2010, 10, 13)),
            true,
            'Includes start date with upper infinity',
        );
        strictEqual(
            new DatetimeRange(null, Datetime.make(2010, 10, 13)).includes(Datetime.make(2000, 6, 3)),
            true,
            'Includes date with lower infinity',
        );
        strictEqual(
            new DatetimeRange(null, Datetime.make(2010, 10, 13), false, false).includes(Datetime.make(1010, 10, 13)),
            true,
            'Includes endDate date with lower infinity',
        );
    });

    it('invalid range', () => {
        assert.throw(
            () => {
                // eslint-disable-next-line no-new
                new DatetimeRange(Datetime.make(2020, 6, 1), Datetime.make(2020, 6, 1));
            },
            `Invalid range: included start ${Datetime.make(
                2020,
                6,
                1,
            ).toString()} should not be greater than excluded end ${Datetime.make(2020, 6, 1).toString()}`,
        );
    });

    it('exclusion range to string', () => {
        // convert to string
        strictEqual(
            new DatetimeRange(Datetime.makeUtc(2020, 6, 1), Datetime.makeUtc(2020, 6, 3), true, true).toString(),
            '(2020-06-01T00:00:00.000Z,2020-06-03T00:00:00.000Z)',
            'Convert to string with exclude',
        );
        strictEqual(
            new DatetimeRange(
                Datetime.makeUtc(2020, 6, 1, 14, 1, 23),
                Datetime.makeUtc(2020, 6, 3),
                false,
                true,
            ).toString(),
            '[2020-06-01T14:01:23.000Z,2020-06-03T00:00:00.000Z)',
            'Convert to string with exclude end',
        );
        strictEqual(
            new DatetimeRange(
                Datetime.makeUtc(2020, 6, 1),
                Datetime.makeUtc(2020, 6, 3, 14, 1, 23),
                true,
                false,
            ).toString(),
            '(2020-06-01T00:00:00.000Z,2020-06-03T14:01:23.000Z]',
            'Convert to string with exclude start',
        );
    });

    it('string parse (local timezone)', () => {
        strictEqual(
            DatetimeRange.parse('[2019-01-12,2020-01-13 14:01:23)', 'Europe/Paris').toString(),
            '[2019-01-11T23:00:00.000Z,2020-01-13T13:01:23.000Z)',
            'Parse date range',
        );
        strictEqual(
            DatetimeRange.parse('[,2020-01-13 14:01:23)', 'Europe/Paris').toString(),
            '[,2020-01-13T13:01:23.000Z)',
            'Parse date range with lower infinity bound',
        );
        strictEqual(
            DatetimeRange.parse('[2020-01-13 14:01:23,)', 'Europe/Paris').toString(),
            '[2020-01-13T13:01:23.000Z,)',
            'Parse date range with upper infinity bound',
        );
        strictEqual(
            DatetimeRange.parse('[2019,2020-02)', 'Europe/Paris').toString(),
            '[2018-12-31T23:00:00.000Z,2020-01-31T23:00:00.000Z)',
            'Parse date range only with year and year + month',
        );
        strictEqual(
            DatetimeRange.parse('[2019-01-01T00:00:00.000-05:00,2020-02-01T00:00:00.000)', 'Europe/Paris').toString(),
            '[2019-01-01T05:00:00.000Z,2020-01-31T23:00:00.000Z)',
            'Parse date range with timezone',
        );
    });

    it('string parse (UTC)', () => {
        strictEqual(
            DatetimeRange.parse('[2019-01-12Z,2020-01-13 14:01:23Z)').toString(),
            '[2019-01-12T00:00:00.000Z,2020-01-13T14:01:23.000Z)',
            'Parse date range',
        );
        strictEqual(
            DatetimeRange.parse('[,2020-01-13 14:01:23Z)').toString(),
            '[,2020-01-13T14:01:23.000Z)',
            'Parse date range with lower infinity bound',
        );
        strictEqual(
            DatetimeRange.parse('[2020-01-13 14:01:23Z,)').toString(),
            '[2020-01-13T14:01:23.000Z,)',
            'Parse date range with upper infinity bound',
        );
        strictEqual(
            DatetimeRange.parse('[2019Z,2020-02Z)').toString(),
            '[2019-01-01T00:00:00.000Z,2020-02-01T00:00:00.000Z)',
            'Parse date range only with year and year + month',
        );
        strictEqual(
            DatetimeRange.parse('[2019-01-01T00:00:00.000-05:00,2020-02-01T00:00:00.000Z)').toString(),
            '[2019-01-01T05:00:00.000Z,2020-02-01T00:00:00.000Z)',
            'Parse date range with timezone',
        );
    });

    it('Infinity bounds', () => {
        strictEqual(
            new DatetimeRange(Datetime.makeUtc(2020, 6, 1), null).toString(),
            '[2020-06-01T00:00:00.000Z,)',
            'Create a date range with endDate infinity bound',
        );
        strictEqual(
            new DatetimeRange(null, Datetime.makeUtc(2020, 6, 1)).toString(),
            '[,2020-06-01T00:00:00.000Z)',
            'Create a date range with startDate infinity bound',
        );

        strictEqual(
            DatetimeRange.parse('[2020-06-01 00:00:00Z,)').toString(),
            '[2020-06-01T00:00:00.000Z,)',
            'Parse date with upper bound infinity',
        );

        strictEqual(
            DatetimeRange.parse('[,2020-06-01 00:00:00Z)').toString(),
            '[,2020-06-01T00:00:00.000Z)',
            'Parse date with lower bound infinity',
        );
    });
});
