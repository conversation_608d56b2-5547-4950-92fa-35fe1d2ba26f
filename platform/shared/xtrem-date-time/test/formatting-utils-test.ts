import { assert } from 'chai';
import { formatDateToCurrentLocale } from '../lib/formatting-utils';
import { setup } from './setup';

setup();
const { strictEqual } = assert;

it('preset formats', () => {
    strictEqual(formatDateToCurrentLocale('2019-07-22', 'es-ES', 'FullDate'), '22/07/2019', 'full date');
    strictEqual(formatDateToCurrentLocale('2019-07-22', 'es-ES', 'LongMonth'), 'Julio', 'long month');
    strictEqual(
        formatDateToCurrentLocale(new Date('2019-07-22'), 'es-ES', 'LongMonthYear'),
        'Julio 2019',
        'long month year',
    );
    strictEqual(formatDateToCurrentLocale(new Date('2019-07-22'), 'es-ES', 'MonthDay'), '22/07', 'month day');
    strictEqual(formatDateToCurrentLocale('2019-07-22', 'es-ES', 'MonthYear'), '07/2019', 'month year');
});
