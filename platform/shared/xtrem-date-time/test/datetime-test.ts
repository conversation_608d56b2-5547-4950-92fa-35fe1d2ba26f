import { assert, expect } from 'chai';
import { datetime } from '../index';
import { setup } from './setup';

setup();

const { strictEqual } = assert;

describe(module.id, () => {
    before(() => {
        process.env.TZ = 'Europe/Paris';
    });
    after(() => {
        // Caution: assigning undefined virtually sets TZ to GMT.
        // We have to delete the key to fully revert TZ to the process env.
        delete process.env.TZ;
    });

    it('basic', () => {
        const d = datetime.make(2010, 10, 13, 15, 25, 3, 424);
        assert.ok(datetime.isDatetime(d), 'is datetime');
        assert.ok(!datetime.isDatetime('hello'), 'not is datetime');

        strictEqual(d.year, 2010, 'year ok');
        strictEqual(d.month, 10, 'month ok');
        strictEqual(d.day, 13, 'day ok');
        strictEqual(d.weekDay, datetime.wednesday, 'weekday ok');
        strictEqual(d.yearDay, 286, 'yearday ok');
        strictEqual(d.hour, 15, 'hour ok');
        strictEqual(d.minute, 25, 'minute ok');
        strictEqual(d.second, 3, 'second ok');
        strictEqual(d.millisecond, 424, 'millisecond ok');
    });

    it('add', () => {
        const d = datetime.makeUtc(2010, 1, 1, 0, 0, 0, 0);
        strictEqual(d.toString(), '2010-01-01T00:00:00.000Z', 'base date');
        const d1 = d.addDayFractions(0.5);
        strictEqual(d1.toString(), '2010-01-01T12:00:00.000Z', 'add 0.5 days');
        strictEqual(d.addYears(1).toString(), '2011-01-01T00:00:00.000Z', 'add 1 year');
        strictEqual(d.addMonths(1).toString(), '2010-02-01T00:00:00.000Z', 'add 1 month');
        strictEqual(d.addDays(1).toString(), '2010-01-02T00:00:00.000Z', 'add 1 day');
        strictEqual(d.addWeeks(1).toString(), '2010-01-08T00:00:00.000Z', 'add 1 week');
        strictEqual(d.addHours(12).toString(), '2010-01-01T12:00:00.000Z', 'add 12 hours');
        strictEqual(d.addMinutes(12).toString(), '2010-01-01T00:12:00.000Z', 'add 12 minutes');
        strictEqual(d.addSeconds(12).toString(), '2010-01-01T00:00:12.000Z', 'add 12 seconds');
        const d2 = d.addMilliseconds(12);
        strictEqual(d2.toString(), '2010-01-01T00:00:00.012Z', 'add 12 milliseconds');
        strictEqual(d.millisDiff(d2), -12, 'millisecond difference');
        strictEqual(
            d.add({ years: 1, months: 1, days: 1 }).toString(),
            '2011-02-02T00:00:00.000Z',
            'add time delta, big timeframe',
        );
        strictEqual(
            d.add({ hours: 1, minutes: 1, seconds: 1, millis: 1 }).toString(),
            '2010-01-01T01:01:01.001Z',
            'add time delta, small timeframe',
        );
    });

    it('toJsDate', () => {
        const d = datetime.makeUtc(2010, 1, 1, 0, 0, 0, 0);
        strictEqual(d.toJsDate().valueOf(), new Date('2010-01-01T00:00:00.000Z').valueOf(), 'test toJsDate');
    });

    it('now', () => {
        const today = datetime.now(false);
        const later = today.addSeconds(1);
        expect(today.year).to.equals(later.year, 'now function returns year as expected');
        expect(today.month).to.equals(later.month, 'now function returns month as expected');
        expect(today.day).to.equals(later.day, 'now function returns day as expected');
        strictEqual(today.millisDiff(later), -1000, 'now millis difference to be -1000');
    });

    it('parse', () => {
        // WARNING tests with AM/PM may not work correctly without setting locale
        // Parse Z for UTC date time but will return the datetime with the current machine time zone offset
        let d = datetime.parse('2010-10-13T13:25:03.424Z');
        strictEqual(d.year, 2010, 'parse check year (no format)');
        strictEqual(d.month, 10, 'parse check month (no format)');
        strictEqual(d.day, 13, 'parse check day (no format)');
        // UTC hour will be what was parsed but .hour will depend on machine time zone
        strictEqual(d.utcHour, 13, 'parse check hour (no format)');
        strictEqual(d.hour, 15, 'parse check UTC hour (no format)');
        strictEqual(d.minute, 25, 'parse check minute (no format)');
        strictEqual(d.second, 3, 'parse check second (no format)');
        strictEqual(d.millisecond, 424, 'parse check millisecond (no format)');
        strictEqual(d.toString(), '2010-10-13T13:25:03.424Z');

        // Parse timezone UTC+04:00
        d = datetime.parse('2010-10-13T15:25:03.424+04:10');
        strictEqual(d.year, 2010, 'parse check year (no format - tz offset)');
        strictEqual(d.month, 10, 'parse check month (no format - tz offset)');
        strictEqual(d.day, 13, 'parse check day (no format - tz offset)');
        // utc hour will be 4:10 hours prior to parsed value
        strictEqual(d.utcHour, 11, 'parse check UTC hour (no format)');
        strictEqual(d.utcMinute, 15, 'parse check UTC hour (no format)');
        // d.hour in current timezone of machine so CET/CEST will be 13 - UTC/GMT+2
        strictEqual(d.hour, 13, 'parse check UTC hour (no format)');
        strictEqual(d.minute, 15, 'parse check minute (no format - tz offset)');
        strictEqual(d.second, 3, 'parse check second (no format - tz offset)');
        strictEqual(d.millisecond, 424, 'parse check millisecond (no format - tz offset)');
        // toString brings back UTC (Z) as no format was passed
        strictEqual(d.toString(), '2010-10-13T11:15:03.424Z');

        d = datetime.parse('2010-10-13 15.25.03', 'en-GB', 'YYYY-MM-DD HH.mm.ss');
        strictEqual(d.year, 2010, 'parse check year (YYYY-MM-DD HH.mm.ss)');
        strictEqual(d.month, 10, 'parse check month (YYYY-MM-DD HH.mm.ss)');
        strictEqual(d.day, 13, 'parse check day (YYYY-MM-DD HH.mm.ss)');

        // this test passes no timezone so timezone of machine running will be applied
        strictEqual(d.utcHour, 13, 'parse check hour (YYYY-MM-DD HH.mm.ss)');

        strictEqual(d.hour, 15, 'parse check hour (YYYY-MM-DD HH.mm.ss)');
        strictEqual(d.minute, 25, 'parse check minute (YYYY-MM-DD HH.mm.ss)');
        strictEqual(d.second, 3, 'parse check second (YYYY-MM-DD HH.mm.ss)');
        strictEqual(d.millisecond, 0, 'parse check millisecond (YYYY-MM-DD HH.mm.ss)');
        // WARNING Following Tests only pass in CET time zone
        strictEqual(
            datetime
                .parse('datetime value was : 2/13/95 7:10:01 PM', 'en-GB', '[datetime value was :] M/D/YY h:mm:ss A')
                .toString(),
            '1995-02-13T18:10:01.000Z',
            'parse.toString() tricky example',
        );
        strictEqual(
            datetime
                .parse('date is : 2 February 1995 4:24:13 AM', 'en-GB', '[date is :] D MMMM YYYY h:mm:ss A')
                .toString(),
            '1995-02-02T03:24:13.000Z',
            'parse.toString() and month name',
        );
        strictEqual(
            datetime
                .parse('date is : Thu 2 February 2012 4:24:13 AM', 'en-GB', '[date is :] ddd D MMMM YYYY h:mm:ss A')
                .toString(),
            '2012-02-02T03:24:13.000Z',
            'parse.toString() and abbreviated day name',
        );

        const date = datetime.makeUtc(2010, 10, 13, 15, 25, 3, 555);
        d = datetime.parse(date.format('en-GB', 'YYYY-MM-DD[T]HH:mm:ss.SSSZ'), 'en-GB', 'YYYY-MM-DD[T]HH:mm:ss.SSSZ');
        strictEqual(d.millisecond, 555, 'parse check millisecond (YYYY-MM-DD[T]HH.mm.ss.SSSZ)');
        strictEqual(d.utcDate.value, 20101013, 'parse check utc date');
        strictEqual(d.utcYear, 2010, 'parse check utc year');
        strictEqual(d.utcMonth, 10, 'parse check utc month');
        strictEqual(d.utcDay, 13, 'parse check utc day');
        strictEqual(d.utcWeekDay, 3, 'parse check utc week day');
        strictEqual(d.utcYearDay, 286, 'parse check utc year day');
        strictEqual(d.utcHour, 15, 'parse check utc week hour');
        strictEqual(d.utcMinute, 25, 'parse check utc minute');
        strictEqual(d.utcSecond, 3, 'parse check utc second');
        strictEqual(d.utcMillisecond, 555, 'parse check utc millisecond');
        strictEqual(d.value, 1286983503555, 'parse check value');

        const date2 = datetime.makeUtc(2010, 10, 12, 5, 25, 3, 55);
        d = datetime.parse(date2.format('en-GB', 'YYYY-MM-DD[T]HH:mm:ss.SSSZ'), 'en-GB', 'YYYY-MM-DD[T]HH:mm:ss.SSSZ');
        strictEqual(d.utcHour, 5, 'parse check utc week hour');
        strictEqual(d.millisecond, 55, 'parse check millisecond (YYYY-MM-DD[T]HH.mm.ss.SSSZ)');
        strictEqual(date.compare(date2) >= 1, true, 'parse check datetime compare');
        strictEqual(date.equals(date2), false, 'parse check datetime equals');

        const date3 = datetime.makeUtc(2010, 10, 14, 15, 25, 3, 5);
        d = datetime.parse(date3.format('en-GB', 'YYYY-MM-DD[T]HH:mm:ss.SSSZ'), 'en-GB', 'YYYY-MM-DD[T]HH:mm:ss.SSSZ');
        strictEqual(d.millisecond, 5, 'parse check millisecond (YYYY-MM-DD[T]HH.mm.ss.SSSZ)');
        strictEqual(date.isBetween(date2, date3), true, 'check datetime between');

        // pads millis with zeros if less than 3 digits
        strictEqual(datetime.parse('2022-02-08T16:12:22.3Z').toString(), '2022-02-08T16:12:22.300Z');
        strictEqual(datetime.parse('2022-02-08T16:12:22.37Z').toString(), '2022-02-08T16:12:22.370Z');
        // throws if milliseconds part has more than 3 digits
        assert.throws(
            () => datetime.parse('2022-02-08T16:12:22.0000Z'),
            'cannot parse date: 2022-02-08T16:12:22.0000Z',
        );
    });

    it('parse - with offset format', () => {
        // Test + minute offset only
        let d = datetime.parse('2010-10-14T15:25:03.005+00:22', 'en-GB', 'YYYY-MM-DD[T]HH:mm:ss.SSSZ');
        strictEqual(d.year, 2010, 'parse check year (YYYY-MM-DD HH.mm.ss)');
        strictEqual(d.month, 10, 'parse check month (YYYY-MM-DD HH.mm.ss)');
        strictEqual(d.day, 14, 'parse check day (YYYY-MM-DD HH.mm.ss)');
        strictEqual(d.utcHour, 15, 'parse check hour (YYYY-MM-DD HH.mm.ss)');
        strictEqual(d.utcMinute, 3, 'parse check minute (YYYY-MM-DD HH.mm.ss)');

        // Test + hour offset only
        d = datetime.parse('2010-10-14T15:25:03.005+10:00', 'en-GB', 'YYYY-MM-DD[T]HH:mm:ss.SSSZ');
        strictEqual(d.utcYear, 2010, 'parse check year (YYYY-MM-DD HH.mm.ss)');
        strictEqual(d.utcMonth, 10, 'parse check month (YYYY-MM-DD HH.mm.ss)');
        strictEqual(d.utcDay, 14, 'parse check day (YYYY-MM-DD HH.mm.ss)');
        strictEqual(d.utcHour, 5, 'parse check hour (YYYY-MM-DD HH.mm.ss)');
        strictEqual(d.utcMinute, 25, 'parse check minute (YYYY-MM-DD HH.mm.ss)');

        // Test + hour and minute offset
        d = datetime.parse('2010-10-14T15:25:03.005+10:22', 'en-GB', 'YYYY-MM-DD[T]HH:mm:ss.SSSZ');
        strictEqual(d.utcYear, 2010, 'parse check year (YYYY-MM-DD HH.mm.ss)');
        strictEqual(d.utcMonth, 10, 'parse check month (YYYY-MM-DD HH.mm.ss)');
        strictEqual(d.utcDay, 14, 'parse check day (YYYY-MM-DD HH.mm.ss)');
        strictEqual(d.utcHour, 5, 'parse check hour (YYYY-MM-DD HH.mm.ss)');
        strictEqual(d.utcMinute, 3, 'parse check minute (YYYY-MM-DD HH.mm.ss)');

        // Test - minute offset only
        d = datetime.parse('2010-10-14T15:25:03.005-00:10', 'en-GB', 'YYYY-MM-DD[T]HH:mm:ss.SSSZ');
        strictEqual(d.utcYear, 2010, 'parse check year (YYYY-MM-DD HH.mm.ss)');
        strictEqual(d.utcMonth, 10, 'parse check month (YYYY-MM-DD HH.mm.ss)');
        strictEqual(d.utcDay, 14, 'parse check day (YYYY-MM-DD HH.mm.ss)');
        strictEqual(d.utcHour, 15, 'parse check hour (YYYY-MM-DD HH.mm.ss)');
        strictEqual(d.utcMinute, 35, 'parse check minute (YYYY-MM-DD HH.mm.ss)');

        // Test - hour offset only
        d = datetime.parse('2010-10-14T15:25:03.005-11:00', 'en-GB', 'YYYY-MM-DD[T]HH:mm:ss.SSSZ');
        strictEqual(d.utcYear, 2010, 'parse check year (YYYY-MM-DD HH.mm.ss)');
        strictEqual(d.utcMonth, 10, 'parse check month (YYYY-MM-DD HH.mm.ss)');
        strictEqual(d.utcDay, 15, 'parse check day (YYYY-MM-DD HH.mm.ss)');
        strictEqual(d.utcHour, 2, 'parse check hour (YYYY-MM-DD HH.mm.ss)');
        strictEqual(d.utcMinute, 25, 'parse check minute (YYYY-MM-DD HH.mm.ss)');

        // Test - hour and minute offset
        d = datetime.parse('2010-10-14T15:25:03.005-11:15', 'en-GB', 'YYYY-MM-DD[T]HH:mm:ss.SSSZ');
        strictEqual(d.utcYear, 2010, 'parse check year (YYYY-MM-DD HH.mm.ss)');
        strictEqual(d.utcMonth, 10, 'parse check month (YYYY-MM-DD HH.mm.ss)');
        strictEqual(d.utcDay, 15, 'parse check day (YYYY-MM-DD HH.mm.ss)');
        strictEqual(d.utcHour, 2, 'parse check hour (YYYY-MM-DD HH.mm.ss)');
        strictEqual(d.utcMinute, 40, 'parse check minute (YYYY-MM-DD HH.mm.ss)');

        // Test - No offset (UTC)
        d = datetime.parse('2010-10-14T15:25:03.005Z', 'en-GB', 'YYYY-MM-DD[T]HH:mm:ss.SSSZ');
        strictEqual(d.utcYear, 2010, 'parse check year (YYYY-MM-DD HH.mm.ss)');
        strictEqual(d.utcMonth, 10, 'parse check month (YYYY-MM-DD HH.mm.ss)');
        strictEqual(d.utcDay, 14, 'parse check day (YYYY-MM-DD HH.mm.ss)');
        strictEqual(d.utcHour, 15, 'parse check hour (YYYY-MM-DD HH.mm.ss)');
        strictEqual(d.utcMinute, 25, 'parse check minute (YYYY-MM-DD HH.mm.ss)');

        // Test - hour and minute offset without colon (:)
        d = datetime.parse('2010-10-14T15:25:03.005-1115', 'en-GB', 'YYYY-MM-DD[T]HH:mm:ss.SSSZ');
        strictEqual(d.utcYear, 2010, 'parse check year (YYYY-MM-DD HH.mm.ss)');
        strictEqual(d.utcMonth, 10, 'parse check month (YYYY-MM-DD HH.mm.ss)');
        strictEqual(d.utcDay, 15, 'parse check day (YYYY-MM-DD HH.mm.ss)');
        strictEqual(d.utcHour, 2, 'parse check hour (YYYY-MM-DD HH.mm.ss)');
        strictEqual(d.utcMinute, 40, 'parse check minute (YYYY-MM-DD HH.mm.ss)');
    });

    it('validate date values', () => {
        // validateDateValues is called in makeUTC so we can test using it
        // 31 day month
        expect(() => {
            datetime.makeUtc(2020, 3, 31, 12, 30, 3, 555);
        }).to.not.throw('invalid day value');

        // 30 day month pass 30 no error
        expect(() => {
            datetime.makeUtc(2020, 11, 30, 12, 30, 3, 555);
        }).to.not.throw('invalid day value');

        // 30 day month pass 31 error
        expect(() => {
            datetime.makeUtc(2020, 11, 31, 12, 30, 3, 555);
        }).to.throw('invalid day value');

        // leap year - no error
        expect(() => {
            datetime.makeUtc(2020, 2, 29, 12, 30, 3, 555);
        }).to.not.throw('invalid day value');

        // not a leap year - error
        expect(() => {
            datetime.makeUtc(2019, 2, 29, 12, 30, 3, 555);
        }).to.throw('invalid day value');

        // Parsing day of the week instead of day/date
        const date = datetime.makeUtc(2010, 10, 14, 5, 25, 3, 5);
        expect(() => {
            datetime.parse(date.format('en-GB', 'dddd MMMM YYYY H.mm.ss A'), 'en-GB', 'dddd MMMM YYYY H.mm.ss A');
        }).to.throw('invalid day value');

        // Parsing abbrv. day of the week instead of day/date
        expect(() => {
            datetime.parse(date.format('en-GB', 'ddd MMM YY hh.mm.ss A'), 'en-GB', 'ddd MMM YY hh.mm.ss A');
        }).to.throw('invalid day value');
    });

    it('parse errors', () => {
        const date = datetime.makeUtc(2010, 10, 13, 15, 25, 3, 555);
        expect(() => {
            datetime.parse(date.format('en-GB', 'ddddd MM YYYY HH.mm.ss'), 'en-GB', 'ddddd MM YYYY HH.mm.ss');
        }).to.throw(
            "Format of directive 'ddddd' incorrect in 'ddddd MM YYYY HH.mm.ss'",
            'error parsing wrong day format',
        );

        expect(() => {
            datetime.parse(date.format('en-GB', 'DD MMMMM YYYY HH.mm.ss'), 'en-GB', 'DD MM YYYY HH.mm.ss');
        }).to.throw(
            "Format of directive 'MMMMM' incorrect in 'DD MMMMM YYYY HH.mm.ss'",
            'error parsing wrong month format',
        );

        expect(() => {
            datetime.parse(date.format('en-GB', 'DD MM YYY HH.mm.ss'), 'en-GB', 'DD MM YYY HH.mm.ss');
        }).to.throw("Format of directive 'YYY' incorrect in 'DD MM YYY HH.mm.ss'", 'error parsing wrong year format');

        expect(() => {
            datetime.parse(date.format('en-GB', 'DD MM YYYY HH.mm.ss'), 'en-GB', 'DD MM YYYY HHH.mm.ss');
        }).to.throw('Hour format incorrect: HHH.mm.ss', 'error parsing wrong hour format');

        expect(() => {
            datetime.parse(date.format('en-GB', 'DD MM YYYY HH.mmm.ss'), 'en-GB', 'DD MM YYYY HH.mmm.ss');
        }).to.throw(
            "Format of directive 'mmm' incorrect in 'DD MM YYYY HH.mmm.ss'",
            'error parsing wrong minute format',
        );

        expect(() => {
            datetime.parse(date.format('en-GB', 'DD MM YYYY HH.mm.s'), 'en-GB', 'DD MM YYYY HH.mm.s');
        }).to.throw("Format of directive 's' incorrect in 'DD MM YYYY HH.mm.s'", 'error parsing wrong second format');

        expect(() => {
            datetime.parse(date.format('en-GB', 'DD MM YYYY HH.mm.ss.SZ'), 'en-GB', 'DD MM YYYY HH.mm.ss.SZ');
        }).to.throw(
            "Format of directive 'S' incorrect in 'DD MM YYYY HH.mm.ss.SZ'",
            'error parsing wrong millisecond format',
        );

        expect(() => {
            datetime.parse(date.format('en-GB', 'DD MM YYYY HH.mm.ss AA'), 'en-GB', 'DD MM YYYY HH.mm.ss AA');
        }).to.throw(
            "Format of directive 'AA' incorrect in 'DD MM YYYY HH.mm.ss AA'",
            'error parsing wrong marker format',
        );

        expect(() => {
            datetime.parse(date.format('en-GB', 'DD MM YYYY HH.mm.ss A'), 'en-GB', 'DD MM YYYY HH.mm.ss A');
        }).to.throw(
            `Hour value incorrect: expected 1<hour<12, got:17 for 1 mode.`,
            'error parsing 1<hours<12 for AM mode',
        );

        expect(() => {
            datetime.parse('2019-13-07 15.25.03', 'en-GB', 'YYYY-MM-DD HH.mm.ss');
        }).to.throw('invalid month value');

        expect(() => {
            datetime.parse('2019-12-37 15.25.03', 'en-GB', 'YYYY-MM-DD HH.mm.ss');
        }).to.throw('invalid day value');

        expect(() => {
            datetime.parse('2019-12-10 15.25.03', 'en-GB', 'YYYY-MM-DD HH.mm.ssZ');
        }).to.throw('Unknown date:');

        expect(() => {
            datetime.parse('2019-12-10 15.25.03 02:00', 'en-GB', 'YYYY-MM-DD HH.mm.ssZ');
        }).to.throw('Unknown date:');

        expect(() => {
            datetime.parse('2019-12-10 15.25.03 02:00', 'en-GB', 'YYYY-MM-DD HH.mm.ss');
        }).to.not.throw('Unknown date:');
    });

    it('format', () => {
        strictEqual(
            datetime.make(2010, 10, 13, 15, 25, 3, 424).toString(),
            '2010-10-13T13:25:03.424Z',
            'no format arg',
        );

        strictEqual(
            datetime.make(2010, 10, 3, 15, 25, 3, 424).format('en-GB', 'YY/D/M h:mm:ss A'),
            '10/3/10 3:25:03 PM',
            'format --> YY/D/M h:mm:ss A',
        );

        strictEqual(
            datetime.make(2010, 10, 3, 15, 25, 3, 424).format('en-GB', 'YY/D/M H:mm:ss A'),
            '10/3/10 15:25:03 PM',
            'format --> YY/D/M H:mm:ss A',
        );

        strictEqual(
            datetime.make(2010, 8, 13, 15, 25, 3, 424).format('en-GB', '[created on :] M/D/YYYY hh.mm.ss A'),
            'created on : 8/13/2010 03.25.03 PM',
            "format --> 'created on : 'M/D/YYYY hh.mm.ss A",
        );

        strictEqual(
            datetime.make(2010, 10, 13, 15, 25, 3, 424).format('en-GB', "[Time's :] MM-DD-YYYY hh[']mm[']ss A"),
            "Time's : 10-13-2010 03'25'03 PM",
            'format with quotes',
        );

        strictEqual(
            datetime.make(2010, 10, 3, 15, 25, 3, 424).format('en-GB', 'dddd MMMM YYYY HH.mm.ss'),
            'Sunday October 2010 15.25.03',
            "format --> 'Sunday October 2010 15.25.03'",
        );

        strictEqual(
            datetime.make(2010, 10, 3, 15, 25, 3, 424).format('en-GB', 'ddd MMMM YYYY HH.mm.ss'),
            'Sun October 2010 15.25.03',
            "format --> 'Sun October 2010 15.25.03'",
        );

        strictEqual(
            datetime.make(2010, 10, 2, 15, 25, 3, 424).format('fr-FR', 'D MMM YYYY HH.mm.ss'),
            '2 Oct 2010 15.25.03',
            'format with abbreviated month name',
        );
    });

    it('format errors', () => {
        expect(() => {
            datetime.make(2010, 10, 3, 15, 25, 3, 424).format('en-GB', 'YY/D/M HHH:mm:ss A');
        }).to.throw("Format of directive 'HHH' incorrect in 'YY/D/M HHH:mm:ss A'", 'hour H format error');

        expect(() => {
            datetime.make(2010, 10, 3, 15, 25, 3, 424).format('en-GB', 'YY/D/M hhh:mm:ss A');
        }).to.throw("Format of directive 'hhh' incorrect in 'YY/D/M hhh:mm:ss A'", 'hour h format error');

        expect(() => {
            datetime.make(2010, 10, 3, 15, 25, 3, 424).format('en-GB', 'YY/D/M HH:m:ss A');
        }).to.throw("Format of directive 'm' incorrect in 'YY/D/M HH:m:ss A'", 'minute format error');

        expect(() => {
            datetime.make(2010, 10, 3, 15, 25, 3, 424).format('en-GB', 'YY/D/M HH:mm:ss.S A');
        }).to.throw("Format of directive 'S' incorrect in 'YY/D/M HH:mm:ss.S A'", 'second S format error');

        expect(() => {
            datetime.make(2010, 10, 3, 15, 25, 3, 424).format('en-GB', 'YY/D/M HH:mm:s A');
        }).to.throw("Format of directive 's' incorrect in 'YY/D/M HH:mm:s A'", 'second s format error');

        expect(() => {
            datetime.make(2010, 10, 3, 15, 25, 3, 424).format('en-GB', 'YY/D/M HH:mm:ss AA');
        }).to.throw("Format of directive 'AA' incorrect in 'YY/D/M HH:mm:ss AA'", 'am/pm format error');

        expect(() => {
            datetime.make(2010, 10, 3, 15, 25, 3, 424).format('en-GB', 'YY/ddddd/M HH:mm:ss A');
        }).to.throw("Format of directive 'ddddd' incorrect in 'YY/ddddd/M HH:mm:ss A'", 'day format error');

        expect(() => {
            datetime.make(2010, 10, 3, 15, 25, 3, 424).format('en-GB', 'YY/D/MMMMM HH:mm:ss A');
        }).to.throw("Format of directive 'MMMMM' incorrect in 'YY/D/MMMMM HH:mm:ss A'", 'month format error');

        expect(() => {
            datetime.make(2010, 10, 3, 15, 25, 3, 424).format('en-GB', 'Y/D/M HH:mm:ss A');
        }).to.throw("Format of directive 'Y' incorrect in 'Y/D/M HH:mm:ss A'", 'year format error');
    });

    it('JSON', () => {
        strictEqual(
            JSON.stringify(datetime.makeUtc(2010, 10, 2, 15, 25, 3, 424)),
            '"2010-10-02T15:25:03.424Z"',
            'JSON',
        );
    });

    it('TimeZone', () => {
        // Make a datetime in the Phoenix time zone
        const phoenixDt = datetime.make(2024, 2, 7, 20, 5, 50, 314, 'America/Phoenix');
        // Formatting without Z letter gives Phoenix time
        strictEqual(phoenixDt.format('en-GB', 'YYYY-MM-DD HH:mm:ss.SSS'), '2024-02-07 20:05:50.314');
        // Formatting with z letter gives timezone abbreviation (MST for Phoenix)
        strictEqual(phoenixDt.format('en-GB', 'YYYY-MM-DD HH:mm:ss z'), '2024-02-07 20:05:50 MST');
        // toString() gives UTC time, 7 hours ahead of Phoenix
        strictEqual(phoenixDt.toString(), '2024-02-08T03:05:50.314Z');
        // Convert Phoenix datetime to UTC
        const utcDt = phoenixDt.inTimeZone('UTC');
        // Now, formatting without Z letter gives UTC time
        strictEqual(utcDt.format('en-GB', 'YYYY-MM-DD HH:mm:ss.SSS'), '2024-02-08 03:05:50.314');
        // toString() did not change. Still UTC
        strictEqual(utcDt.toString(), '2024-02-08T03:05:50.314Z');
        // Convert Phoenix datetime to Paris time zone
        const parisDt = phoenixDt.inTimeZone('Europe/Paris');
        // Now, formatting without Z letter gives Paris time
        strictEqual(parisDt.format('en-GB', 'YYYY-MM-DD HH:mm:ss.SSS'), '2024-02-08 04:05:50.314');
        // toString() did not change. Still UTC
        strictEqual(parisDt.toString(), '2024-02-08T03:05:50.314Z');

        // Parse a date in the Phoenix time zone
        let parsedDt = datetime.parse('2024-02-07 20:05:50.314', 'en-GB', 'YYYY-MM-DD HH:mm:ss.SSS', 'America/Phoenix');
        strictEqual(parsedDt.format('en-GB', 'YYYY-MM-DD HH:mm:ss z'), '2024-02-07 20:05:50 MST');
        strictEqual(parsedDt.toString(), '2024-02-08T03:05:50.314Z');

        // Parse it from ISO format, but with time, and check that we get the same results when we format
        parsedDt = datetime.parse('2024-02-08T03:05:50.314Z', undefined, undefined, 'America/Phoenix');
        strictEqual(parsedDt.format('en-GB', 'YYYY-MM-DD HH:mm:ss z'), '2024-02-07 20:05:50 MST');
        strictEqual(parsedDt.toString(), '2024-02-08T03:05:50.314Z');

        // Parse a date with the z format and no time zone
        parsedDt = datetime.parse('2024-02-07 20:05:50 MST', 'en-GB', 'YYYY-MM-DD HH:mm:ss z');
        strictEqual(parsedDt.format('en-GB', 'YYYY-MM-DD HH:mm:ss z'), '2024-02-07 20:05:50 MST');
        strictEqual(parsedDt.toString(), '2024-02-08T03:05:50.000Z');

        // Parse a date with the z format and a time zone
        parsedDt = datetime.parse('2024-02-07 20:05:50 MST', 'en-GB', 'YYYY-MM-DD HH:mm:ss z', 'Europe/Paris');
        // Date was correctly parsed in MST time zone because of z letter, and then converted to Europe/Paris time zone.
        strictEqual(parsedDt.format('en-GB', 'YYYY-MM-DD HH:mm:ss z'), '2024-02-08 04:05:50 GMT+1');
        // UTC result is correct
        strictEqual(parsedDt.toString(), '2024-02-08T03:05:50.000Z');

        // Parse a date with the zzz format and no time zone
        parsedDt = datetime.parse('2024-02-07 20:05:50 America/Phoenix', 'en-GB', 'YYYY-MM-DD HH:mm:ss zzz');
        // In this case we could find the IANA time zone
        strictEqual(parsedDt.format('en-GB', 'YYYY-MM-DD HH:mm:ss zzz'), '2024-02-07 20:05:50 America/Phoenix');
        strictEqual(parsedDt.toString(), '2024-02-08T03:05:50.000Z');

        // Parse a date with the zzz format and a time zone
        parsedDt = datetime.parse(
            '2024-02-07 20:05:50 America/Phoenix',
            'en-GB',
            'YYYY-MM-DD HH:mm:ss zzz',
            'Europe/Paris',
        );
        // Date was correctly parsed in MST time zone because of zzz, and then converted to Europe/Paris time zone.
        strictEqual(parsedDt.format('en-GB', 'YYYY-MM-DD HH:mm:ss zzz'), '2024-02-08 04:05:50 Europe/Paris');
        // UTC result is correct
        strictEqual(parsedDt.toString(), '2024-02-08T03:05:50.000Z');

        // Add one day and one hour to the phoenix datetime
        const phoenixLater = phoenixDt.addDays(1).addHours(1);
        // Phoenix time moves
        strictEqual(phoenixLater.format('en-GB', 'YYYY-MM-DD HH:mm:ss z'), '2024-02-08 21:05:50 MST');
        // toString() moved too.
        strictEqual(phoenixLater.toString(), '2024-02-09T04:05:50.314Z');
    });
});
