import { Decimal } from '@sage/xtrem-decimal';
import { assert } from 'chai';
import { DecimalRange } from '../index';
import { setup } from './setup';

setup();
const { strictEqual } = assert;

const decimalRange = DecimalRange.make(1.001, 4.004);
const decimalRangeExcludeStart = DecimalRange.make(1.001, 4.004, true);
const decimalRangeIncludeEnd = DecimalRange.make(1.001, 4.004, false, false);
const decimalRangeIncludeStartAndEnd = DecimalRange.make(1.001, 4.004, false, false);
const decimalRangeInfinityStartBound = DecimalRange.make(null, 4.004);
const decimalRangeInfinityEndBound = DecimalRange.make(1.001, null);

// eslint-disable-next-line mocha/no-exclusive-tests
describe(module.id, () => {
    it('basic', () => {
        assert.ok(DecimalRange.isDecimalRange(decimalRange), 'is decimal range');
    });
    it('increment by unit range', () => {
        strictEqual(
            decimalRange.incrementsByRangeUnit(Decimal.make(1)).equals(Decimal.make(1.1)),
            true,
            'increment by unit range without scale',
        );
        strictEqual(
            decimalRange.incrementsByRangeUnit(Decimal.make(1.99)).equals(Decimal.make(2)),
            true,
            'increment by unit range with scale',
        );
        strictEqual(
            decimalRange.incrementsByRangeUnit(Decimal.make(1.908)).equals(Decimal.make(1.909)),
            true,
            'increment by unit range with scale',
        );
    });

    it('equal', () => {
        strictEqual(decimalRange.equals(decimalRange), true, 'is equal integer range');
        strictEqual(decimalRangeExcludeStart.equals(decimalRangeExcludeStart), true, 'is equal with start exclusion');
        strictEqual(decimalRangeIncludeEnd.equals(decimalRangeIncludeEnd), true, 'is equal with end included');
        strictEqual(
            decimalRangeIncludeStartAndEnd.equals(decimalRangeIncludeStartAndEnd),
            true,
            'is equal with start and end included',
        );
        strictEqual(decimalRange.equals(decimalRangeExcludeStart), false, 'is not equal integer range');
        strictEqual(decimalRangeExcludeStart.equals(decimalRangeIncludeEnd), false, 'is not equal integer range');
    });
    it('includes', () => {
        // strictEqual(decimalRange.includes(2), true, 'is equal integer range');
        // strictEqual(decimalRangeExcludeStart.includes(1), false, 'excluded start is not included');
        // strictEqual(decimalRangeIncludeEnd.includes(4), true, 'include end is included');
    });
    it('parse', () => {
        strictEqual(DecimalRange.parse('[1,4)').toString(), '[1,4)', 'parse with default inclusion');
        strictEqual(DecimalRange.parse('[1,4]').toString(), '[1,4]', 'parse with end included');
        strictEqual(DecimalRange.parse('(1,4]').toString(), '(1,4]', 'parse with start excluded and end included');
        strictEqual(DecimalRange.parse('(1,4)').toString(), '(1,4)', 'parse with start and end  excluded');

        strictEqual(DecimalRange.parse('[,4)').toString(), '(,4)', 'Parse range with lower infinity bound');
        strictEqual(DecimalRange.parse('[1,)').toString(), '[1,)', 'Parse range with upper infinity bound');
        assert.throw(() => {
            // eslint-disable-next-line no-new
            DecimalRange.parse('(1,4ds)');
        }, 'Invalid decimal range format: (1,4ds)');
    });
    it('invalid range', () => {
        assert.throw(() => {
            // eslint-disable-next-line no-new
            DecimalRange.make(3, 1);
        }, 'Invalid range: included start 3 should not be greater than excluded end 1');
    });
    it('infinity bounds', () => {
        strictEqual(decimalRangeInfinityStartBound.toString(), '[,4.004)', 'infinity start bound');
        strictEqual(decimalRangeInfinityEndBound.toString(), '[1.001,)', 'infinity end bound');
    });
    it('infinity closed bounds', () => {
        const decimalRangeInfinityClosedStartBound = DecimalRange.parse('[,4.004]');
        const decimalRangeInfinityClosedEndBound = DecimalRange.parse('[1.001,]');

        strictEqual(decimalRangeInfinityClosedStartBound.toString(), '(,4.004]', 'infinity start bound');
        strictEqual(decimalRangeInfinityClosedEndBound.toString(), '[1.001,)', 'infinity end bound');
    });
});
