import { assert } from 'chai';
import { uuid } from '../index';
import { setup } from './setup';

setup();

const { lengthOf, strictEqual } = assert;

describe(module.id, () => {
    describe('generate', () => {
        // Version 4 UUIDs have the form
        // xxxxxxxx-xxxx-4xxx-xxxx-xxxxxxxxxxxx with hexadecimal digits x
        // and hexadecimal digits 8, 9, A, or B for y
        it('should return a default version 4 UUID', () => {
            const v4Uuid = uuid.generate();
            lengthOf(v4Uuid, 36, 'uuid has length of 36');
            const splitUuid = v4Uuid.split('-');
            lengthOf(splitUuid, 5, 'uuid has correct form');
            lengthOf(splitUuid[0], 8);
            lengthOf(splitUuid[1], 4);
            lengthOf(splitUuid[2], 4);
            strictEqual(splitUuid[2][0], '4');
            lengthOf(splitUuid[3], 4);
            lengthOf(splitUuid[4], 12);
        });
        it('should return a UUID with | separators', () => {
            const v4Uuid = uuid.generate('|');
            lengthOf(v4Uuid, 36, 'uuid has length of 36');
            lengthOf(v4Uuid.split('|'), 5, 'uuid has correct form');
        });
    });
    describe('fromBytes', () => {
        it('should return a uuid from bytes', () => {
            const uInt = new Uint8Array(16);
            const v4Uuid = uuid.fromBytes(uInt);
            lengthOf(v4Uuid, 36, 'uuid has length of 36');
            lengthOf(v4Uuid.split('-'), 5, 'uuid has correct form');
        });
    });
    describe('toBytes', () => {
        it('should throw if length is not 36', () => {
            assert.throws(() => uuid.toBytes('12345'), 'invalid uuid: 12345');
        });
        it('should return a correct byte array', () => {
            const v4Uuid = uuid.generate();
            lengthOf(uuid.toBytes(v4Uuid), 16, 'byte array of length 16');
        });
    });

    describe('toString32', () => {
        it('should return a 32 character string from a uuid', () => {
            const v4Uuid = uuid.generate();
            lengthOf(uuid.toString32(v4Uuid), 32);
        });
    });
    describe('fromString32', () => {
        it('should return a uuid from string', () => {
            const v4Uuid = uuid.fromString32('b4C43fB913fd400ab9Ef92929c345B3F');
            lengthOf(v4Uuid, 36, 'uuid has length of 36');
            lengthOf(v4Uuid.split('-'), 5, 'uuid has correct form');
        });
        it('should return original value after covering all conversions', () => {
            const v4Uuid = uuid.generate();
            const convUuid = uuid.fromBytes(uuid.toBytes(uuid.fromString32(uuid.toString32(v4Uuid))));
            strictEqual(convUuid, v4Uuid, 'complete conversion loop ');
        });
    });
});
