import { Datetime } from './datetime';
import { Range } from './range';

export class DatetimeRange extends Range<Datetime> {
    // eslint-disable-next-line class-methods-use-this
    get constructorName(): string {
        return 'DatetimeRange';
    }

    get durationInMilliseconds(): number {
        if (this.excludedEnd instanceof Datetime && this.includedStart instanceof Datetime) {
            return this.excludedEnd.millisDiff(this.includedStart as Datetime);
        }
        return Infinity;
    }

    /** @internal */
    // eslint-disable-next-line class-methods-use-this
    incrementsByRangeUnit(elm: Datetime): Datetime {
        return elm.addMilliseconds(1);
    }

    /** @internal */
    // eslint-disable-next-line class-methods-use-this
    getBoundValue(elm: Datetime | null): number | null {
        if (!elm) return null;
        return elm?.value;
    }

    /** @internal */
    // eslint-disable-next-line class-methods-use-this
    getStringBoundValue(elm: Datetime | null): string {
        if (!elm) return '';
        return elm.toString();
    }

    // parse datetime range from UTC to Local
    static parse(str: string, timeZone?: string): DatetimeRange {
        const [start, end] = str.split(',');
        if (!end) throw new Error(`invalid datetime range format: ${str}.`);

        if (!start && !end) return new DatetimeRange(null, null);

        const excludesStart = start.length === 0 || start.startsWith('(');
        const excludesEnd = end.length === 0 || end.endsWith(')');

        const parseHalf = (s: string): Datetime | null => {
            if (!s) {
                return null;
            }
            const clean = s.replace(
                /"?(\d{1,4})(?:-(\d{1,2})(?:-(\d{1,2})(?:[T ](\d{1,2})(?::(\d{1,2})(?::(\d{1,2}))?)?)?)?)?([^"]*)"?/,
                (
                    _all: string,
                    year: string,
                    month = '01',
                    day = '01',
                    hour = '00',
                    minute = '00',
                    second = '00',
                    tail = '',
                ) =>
                    `${year.padStart(4, '0')}-${month.padStart(2, '0')}-${day.padStart(2, '0')}T${hour.padStart(
                        2,
                        '0',
                    )}:${minute.padStart(2, '0')}:${second.padStart(2, '0')}${tail}`,
            );
            return Datetime.parse(clean, undefined, undefined, timeZone);
        };

        return new DatetimeRange(
            parseHalf(start && start.substring(1)),
            parseHalf(end && end.substring(0, end.length - 1)),
            excludesStart,
            excludesEnd,
        );
    }

    static isDatetimeRange(obj: any): obj is Range<Datetime> {
        return obj && obj.constructorName === 'DatetimeRange';
    }


    static make(start: Datetime | null, end: Datetime | null, excludesStart = false, excludesEnd = true) {
        return new DatetimeRange(start, end, excludesStart, excludesEnd);
    }
}

export const parse = DatetimeRange.parse.bind(DatetimeRange);
export const isDatetimeRange = DatetimeRange.isDatetimeRange.bind(DatetimeRange);
export const make = DatetimeRange.make.bind(DatetimeRange);
