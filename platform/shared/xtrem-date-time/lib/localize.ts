import { localizedText as locoLocalize } from '@sage/xtrem-i18n';
import { LocalizeFunction, LocalizeLocale } from '@sage/xtrem-shared';

let useLocalize: LocalizeFunction = locoLocalize;

export const localizedText: LocalizeFunction = (
    key: string,
    _template: string,
    data: object | any[] = {},
    locale: LocalizeLocale = 'base',
) => useLocalize(key, _template, data, locale);

export const setLocalizeImplementation = (localizeImpl: LocalizeFunction) => {
    useLocalize = localizeImpl;
};
