/** @packageDocumentation @module date */
import { LocalizeLocale } from '@sage/xtrem-shared';
import * as datetime from './datetime';
import { Datetime } from './datetime';
import { localizedText } from './localize';
import { createDateTimeObj, parseDateTime } from './parse';
import * as time from './time';
import { localizedGroup } from './utils';
import { walkFormat } from './walkformat';

/** Integer type, to distinguish integers in the API documentation */
// eslint-disable-next-line @typescript-eslint/naming-convention
export type integer = number;

const monthLengths = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
const monthOffsets = [0, 31, 59, 90, 120, 151, 181, 212, 243, 273, 304, 334];

export const abbreviatedMonthNames = [
    'Jan',
    'Feb',
    'Mar',
    'Apr',
    'May',
    'Jun',
    'Jul',
    'Aug',
    'Sep',
    'Oct',
    'Nov',
    'Dec',
];

export const monthNames = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
];

/** Day of the week */
export enum WeekDay {
    sunday = 0,
    monday = 1,
    tuesday = 2,
    wednesday = 3,
    thursday = 4,
    friday = 5,
    saturday = 6,
}

export const abbreviatedDayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

export type WeekDayName = (typeof dayNames)[number];
export const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'] as const;

// internal value is integer YYYYMMDD
// this is a very simple and compact representation that leads to
// very efficient component extraction and formatting.
// Also nice for debugging

function pad(val: number, len: number) {
    let s = val.toString();
    while (s.length < len) s = `0${s}`;
    return s;
}

/** @internal */
export function toOffset(date: DateValue) {
    const { year, month, day } = date;
    // Compute the number of leap days since Jan 1st, 1970.
    // The trick is to use the previous year if month is January or February,
    // the current year otherwise.
    // Then, we compute the number of multiples of 4, 100 and 400 since 1970.
    const y = month <= 2 ? year - 1 : year;

    const n4 = Math.floor(y / 4) - Math.floor(1970 / 4);
    const n100 = Math.floor(y / 100) - Math.floor(1970 / 100);
    const n400 = Math.floor(y / 400) - Math.floor(1970 / 400);

    // Years that are multiple of 400 (like 2000) contribute by 1 (1 -1 +1 in expression below)
    // Years that are multiple of 100 but not of 400 contribute by 0 (1 -1 +0 in expression below)
    // Years that are multiple of 4 but not 100 nor 400 contribute by 1 (1 -0 +0 in expression below).
    const nLeap = n4 - n100 + n400;

    // The offset is straightforward at this point.
    // The February/March transition on leap days will be handled by the fact that the
    // 'y' value above will change, and hence the 'nLeap' value.
    return (year - 1970) * 365 + nLeap + monthOffsets[month - 1] + day - 1;
}

/** @internal */
export function fromOffset(offset: number) {
    const julian = 2440588 + offset;
    let l = julian + 68569;
    const n = Math.floor((4 * l) / 146097);
    l -= Math.floor((146097 * n + 3) / 4);
    const i = Math.floor((4000 * (l + 1)) / 1461001);
    l = l - Math.floor((1461 * i) / 4) + 31;
    const j = Math.floor((80 * l) / 2447);
    const day = l - Math.floor((2447 * j) / 80);
    l = Math.floor(j / 11);
    const month = j + 2 - 12 * l;
    const year = 100 * (n - 49) + i + l;
    return new DateValue(year * 10000 + month * 100 + day);
}

///
/// #### Misc. functions
/// * `dateApi.isLeap(year)`: returns true if `year` is leap.
export function isLeap(year: number): boolean {
    return year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0);
}

/// * `dateApi.daysInMonth(year, month)`: returns the number of days in the specified month.
export function daysInMonth(year: number, month: number): number {
    return month === 2 ? (isLeap(year) ? 29 : 28) : monthLengths[month - 1];
}

export type DateDiff = {
    years?: number;
    months?: number;
    weeks?: number;
    days?: number;
};

/**
 * A `DateValue` represents a date.
 *
 * This class is called `DateValue` rather than Date to avoid confusions with JavaScript's built-in `Date` class.
 *
 * The main differences with JavaScript `Date` are:
 *
 * - `DateValue` is a pure date, without any time nor timezone information. Only year, month and day.
 * - `DateValue` is immutable.
 * - The `month` property takes values between 1 and 12, not 0 and 11.
 *
 * The API uses a simple chaining style. For example:
 *
 * ```ts
 * // the current date
 * const today = DateValue.today();
 * // the end of this month, as another date
 * const endOfThisMonth = today.endOfThisMonth();
 * // the week day of the first day of next month
 * const nextMonthWeekStart = today.begOfMonth().addMonth(1).weekDay;
 * ```
 */
export class DateValue {
    /**
     * The internal value for the date, as a number YYYYMMDD
     * This is *not* the number of milliseconds since 1970, but the year, month and day packed into a single number.
     */
    private readonly _value: number;

    constructor(value: number) {
        if (Number.isNaN(value)) throw new Error(`Invalid date value ${value}`);
        this._value = value;
        const { year, month, day } = this;
        if (year < 1000 || year > 9999) throw new Error(`Invalid date value ${value}: invalid year ${year}`);
        if (month < 1 || month > 12) throw new Error(`Invalid date value ${value}: invalid month ${month}`);
        if (day < 1 || day > daysInMonth(year, month))
            throw new Error(`Invalid date value ${value}: invalid day ${day}`);
    }

    // eslint-disable-next-line class-methods-use-this
    get constructorName() {
        return 'DateValue';
    }

    /**
     * The date's internal value.
     *
     * Note: this is *not* the UNIX timestamp but the year, month and day packed into a single number,
     * for example 20200514 for 2020-05-14
     */
    get value(): integer {
        return this._value;
    }

    /** Number of seconds since 1970-01-01 */
    get epoch(): integer {
        return toOffset(this) * 86400;
    }

    /** The year, between 0 and 9999. */
    get year(): integer {
        return Math.floor(this._value / 10000);
    }

    /** The month, between 1 and 12. */
    get month(): integer {
        return Math.floor(this._value / 100) % 100;
    }

    /** The day of the month, between 1 and 31. */
    get day(): integer {
        return this._value % 100;
    }

    /** The day of the week, between 0 (Sunday) and 6 (Saturday).  */
    get weekDay(): WeekDay {
        // Add Julian offset (+1 because Julian origin is Monday)
        // so that modulo is on positive value even if date is before 1970
        return (2_440_588 + 1 + toOffset(this)) % 7;
    }

    /** The day of the year, between 1 and 366. */
    get yearDay(): integer {
        const month = this.month;
        const leap = month > 2 && isLeap(this.year) ? 1 : 0;
        return monthOffsets[month - 1] + leap + this.day;
    }

    /**
     * The week number, between 1 and 53, as defined by [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601#Week_dates).
     *
     * Week 1 is the beginning of the week that contains January 4th.
     */
    get week(): integer {
        return this.weekNumber(1);
    }

    valueOf(): string {
        return this.toString();
    }

    /**
     * Compares two dates.
     *
     * `date1.compare(date2)` returns a positive integer if `date1 > date2`,
     * a negative integer if `date1 < date2`, and 0 if the dates are equal.
     */
    compare(date: DateValue): integer {
        return this._value - date._value;
    }

    /** Tests date equality */
    equals(date: DateValue): boolean {
        return this._value === date._value;
    }

    /** Is the date between `begin` and `end`? (inclusive) */
    isBetween(begin: DateValue, end: DateValue): boolean {
        return begin._value <= this._value && this._value <= end._value;
    }

    /** Is the date in a leap year? */
    isLeapYear(): boolean {
        return isLeap(this.year);
    }

    /** Is the date a work day? (between Monday and Friday) */
    isWorkDay(): boolean {
        return this.weekDay !== 0 && this.weekDay !== 6;
    }

    /** The number of days in the date's month, between 28 and 31. */
    daysInMonth(): number {
        return daysInMonth(this.year, this.month);
    }

    /** The 1st of January of the date's year. */
    begOfYear(): DateValue {
        return make(this.year, 1, 1);
    }

    /** The 31st of December of the date's year. */
    endOfYear(): DateValue {
        return make(this.year, 12, 31);
    }

    /** The first day of the date's quarter. */
    begOfQuarter(): DateValue {
        return make(this.year, Math.floor((this.month - 1) / 3) * 3 + 1, 1);
    }

    /** The last day of the date's quarter. */
    endOfQuarter(): DateValue {
        return this.begOfQuarter().addMonths(2).endOfMonth();
    }

    /** The first day of the date's month. */
    begOfMonth(): DateValue {
        return make(this.year, this.month, 1);
    }

    /** The last day of the date's month. */
    endOfMonth(): DateValue {
        return make(this.year, this.month, this.daysInMonth());
    }

    /** Date for a given day in the same month as this date. */
    sameMonth(day: number): DateValue {
        const d = Math.min(day, this.daysInMonth());
        return make(this.year, this.month, d);
    }

    /** The last occurence of a given day of the month before this date. */
    pastDay(day: number, includeThis = false): DateValue {
        const delta = day - this.day;
        return delta > 0 || (delta === 0 && !includeThis) ? this.addMonths(-1).sameMonth(day) : this.sameMonth(day);
    }

    /** The next occurence of a given day of the month after this date. */
    futureDay(day: number, includeThis = false): DateValue {
        const delta = day - this.day;
        return delta < 0 || (delta === 0 && !includeThis) ? this.addMonths(1).sameMonth(day) : this.sameMonth(day);
    }

    /** The last occurence of the same day in a given month before this date. */
    pastMonth(month: number, includeThis = false): DateValue {
        let delta = this.month - month;
        delta = delta === 0 ? (includeThis ? 0 : 12) : delta > 0 ? delta : delta + 12;
        return this.addMonths(-delta);
    }

    /** The next occurence of the same day in a given month after this date. */
    futureMonth(month: number, includeThis = false): DateValue {
        let delta = month - this.month;
        delta = delta === 0 ? (includeThis ? 0 : 12) : delta > 0 ? delta : delta + 12;
        return this.addMonths(delta);
    }

    /** The beginning of the week which contains this date. */
    begOfWeek(startDay = WeekDay.sunday): DateValue {
        // 0: Sunday (default), 1: Monday
        const delta = this.weekDay - (startDay || 0);
        return delta === 0 ? this : delta > 0 ? this.addDays(-delta) : this.addDays(-delta - 7);
    }

    /** The end of the week which contains this date. */
    endOfWeek(startDay = WeekDay.sunday): DateValue {
        return this.begOfWeek(startDay).addDays(6);
    }

    /** The requested week day in the same week as this date. */
    sameWeek(weekDay: WeekDay, startDay = WeekDay.sunday): DateValue {
        return this.begOfWeek(startDay).futureWeekDay(weekDay, true);
    }

    /** The last requested week day before this date. */
    pastWeekDay(weekDay: WeekDay, includeThis = false): DateValue {
        const result = this.begOfWeek(weekDay);
        return !includeThis && result.equals(this) ? this.addWeeks(-1) : result;
    }

    /** The next requested week day after this date. */
    futureWeekDay(weekDay: WeekDay, includeThis = false) {
        return this.pastWeekDay(weekDay, !includeThis).addWeeks(1);
    }

    /** The week number between 0 and 53. */
    weekNumber(firstDayOfWeek: WeekDay): integer {
        const begOfWeek1 = make(this.year, 1, 4).begOfWeek(firstDayOfWeek);
        return Math.floor((7 + toOffset(this) - toOffset(begOfWeek1)) / 7);
    }

    /** Adds `years` years to the date. */
    addYears(years: integer): DateValue {
        if (years === 0) return this;
        const year = this.year + years;
        const month = this.month;
        const day = Math.min(this.day, daysInMonth(year, month));
        return make(year, month, day);
    }

    /** Adds `months` months to the date. */
    addMonths(months: integer): DateValue {
        if (months === 0) return this;
        let { day, year } = this;
        const month0 = this.month - 1 + months;
        year += Math.floor(month0 / 12);
        const month = ((month0 + 120000) % 12) + 1;
        const monthLen = daysInMonth(year, month);
        day = day < monthLen ? day : monthLen;
        return make(year, month, day);
    }

    /** Adds `weeks` weeks to the date. */
    addWeeks(weeks: integer): DateValue {
        return this.addDays(7 * weeks);
    }

    /** Adds `days` days to the date. */
    addDays(days: integer): DateValue {
        if (days === 0) return this;
        return fromOffset(toOffset(this) + days);
    }

    /** The number of days between two dates. */
    daysDiff(date: DateValue): integer {
        return toOffset(this) - toOffset(date);
    }

    /**
     * Adds a delta which may combine years, months and days to the date.
     *
     * @example `d.add({ months: 2 })` adds 2 months to `d`.
     */
    add(delta: DateDiff): DateValue {
        return this.addYears(delta.years || 0)
            .addMonths(delta.months || 0)
            .addWeeks(delta.weeks || 0)
            .addDays(delta.days || 0);
    }

    toString() {
        // RFC 3339 by default -- very fast
        const str = pad(this._value, 8);
        return `${str.substring(0, 4)}-${str.substring(4, 6)}-${str.substring(6, 8)}`;
    }

    /**
     * formats the date according to `format`
     * example: date.toString(format = 'YYYY-MM-DD')
     * @param locale LocalizeLocale
     * @param format string
     * @returns string
     */
    format(fmt: string, locale: LocalizeLocale = 'base'): string {
        let result: string = '';
        const { day, weekDay, month, year } = this;
        walkFormat(fmt, {
            literal(lit) {
                result += lit;
            },
            d(repeat) {
                switch (repeat) {
                    case 1:
                        result += day.toString();
                        break;
                    case 2:
                        result += pad(day, 2);
                        break;
                    case 3:
                        result += weekDayName(weekDay, locale, true);
                        break;
                    case 4:
                        result += weekDayName(weekDay, locale);
                        break;
                    default:
                        throw Datetime.formatDirectiveError(fmt, 'd', locale, repeat);
                }
            },
            D(repeat) {
                switch (repeat) {
                    case 1:
                        result += day.toString();
                        break;
                    case 2:
                        result += pad(day, 2);
                        break;
                    default:
                        throw Datetime.formatDirectiveError(fmt, 'D', locale, repeat);
                }
            },
            M(repeat) {
                switch (repeat) {
                    case 1:
                        result += month.toString();
                        break;
                    case 2:
                        result += pad(month, 2);
                        break;
                    case 3:
                        result += monthName(month, locale, true);
                        break;
                    case 4:
                        result += monthName(month, locale);
                        break;
                    default:
                        throw Datetime.formatDirectiveError(fmt, 'M', locale, repeat);
                }
            },
            Y(repeat) {
                switch (repeat) {
                    case 2:
                        result += pad(year % 100, 2);
                        break;
                    case 4:
                        result += pad(year, 4);
                        break;
                    default:
                        throw Datetime.formatDirectiveError(fmt, 'Y', locale, repeat);
                }
            },
        });
        return result;
    }

    ///
    /// #### Conversion to datetime and JavaScript Date
    /// * `date.at(time, millisecond = 0)`: combines the date with `time` and `millisecond` and returns a timestamp.
    at(t: time.Time, timeZone?: string, millisecond = 0) {
        return datetime.make(this.year, this.month, this.day, t.hour, t.minute, t.second, millisecond, timeZone);
    }

    /// * `date.toJsDate(timeZone)`: converts the date to a JavaScript `Date` object.
    toJsDate(timeZone?: string | undefined): Date {
        if (!timeZone) return new Date(this.year, this.month - 1, this.day);
        if (timeZone === 'UTC') return new Date(Date.UTC(this.year, this.month - 1, this.day));
        // If timeZone, create a DateTime in the correct time zone, get its date and then apply toJsDate without arg
        const dt = datetime.make(this.year, this.month, this.day, 0, 0, 0, 0, timeZone);
        return dt.date.toJsDate();
    }

    /// * `date.toJSON()
    ///   converts to JSON format
    toJSON() {
        return this.toString();
    }

    isNull() {
        return this._value === 0;
    }

    ///
    /// ## Functions
    ///
    /// #### Localized names for months, week days, etc.
    /// * `dateApi.amDesignator()`: returns the AM designator in the provided locale.
    static amDesignator(locale: LocalizeLocale = 'base'): string {
        return localizedText('@sage/xtrem-date-time/datetime__amDesignator', 'AM', {}, locale);
    }

    /// * `dateApi.pmDesignator()`: returns the PM designator in the provided locale.
    static pmDesignator(locale: LocalizeLocale = 'base'): string {
        return localizedText('@sage/xtrem-date-time/datetime__pmDesignator', 'PM', {}, locale);
    }

    /// * `dateApi.monthName(month)`: returns the name of `month` in the provided locale.
    static monthName(month: number, locale: LocalizeLocale = 'base', abbrev = false): string {
        const mthIndex = month - 1;
        if (mthIndex < 0 || mthIndex > 11) {
            throw new Error(
                localizedText('@sage/xtrem-date-time/datetime__invMonth', 'invalid month value', {}, locale),
            );
        }
        return abbrev
            ? localizedText(
                  `@sage/xtrem-date-time/datetime__abbreviatedMonthName${mthIndex}`,
                  abbreviatedMonthNames[mthIndex],
                  {},
                  locale,
              )
            : localizedText(`@sage/xtrem-date-time/datetime__monthName${mthIndex}`, monthNames[mthIndex], {}, locale);
    }

    /// * `dateApi.monthFromName(name)`: converts a month name to a month number, using the provided locale.
    static monthFromName(name: string, locale: LocalizeLocale = 'base'): number {
        const localeMonthValue = localizedGroup(monthNames, '@sage/xtrem-date-time/datetime__monthName', {}, locale);
        const localeAbbrevMonthValue = localizedGroup(
            abbreviatedMonthNames,
            '@sage/xtrem-date-time/datetime__abbreviatedMonthName',
            {},
            locale,
        );

        const s = name.toLowerCase();
        for (let i = 0; i < localeMonthValue.length; i += 1) {
            if (localeMonthValue[i].toLowerCase() === s || localeAbbrevMonthValue[i].toLowerCase() === s) {
                return i + 1;
            }
        }
        return -1;
    }

    /// * `dateApi.weekDayName(weekDay)`: returns the name of `weekDay` in the provided locale.
    static weekDayName(weekDay: number, locale: LocalizeLocale = 'base', abbrev = false): string {
        if (weekDay < 0 || weekDay > 6) {
            throw new Error(localizedText('@sage/xtrem-date-time/datetime__invDay', 'invalid day value', {}, locale));
        }

        return abbrev
            ? localizedText(
                  `@sage/xtrem-date-time/datetime__abbreviatedDayName${weekDay}`,
                  abbreviatedDayNames[weekDay],
                  {},
                  locale,
              )
            : localizedText(`@sage/xtrem-date-time/datetime__dayName${weekDay}`, dayNames[weekDay], {}, locale);
    }

    /// * `dateApi.weekDayFromName(name)`: converts a day name to a weekDay number, using the provided locale setting.
    static weekDayFromName(name: string, locale: LocalizeLocale = 'base'): number {
        const localeDayValue = localizedGroup(dayNames, '@sage/xtrem-date-time/datetime__dayName', {}, locale);
        const localeAbbrevDayValue = localizedGroup(
            abbreviatedDayNames,
            '@sage/xtrem-date-time/datetime__abbreviatedDayName',
            {},
            locale,
        );

        const s = name.toLowerCase();
        for (let i = 0; i < localeDayValue.length; i += 1) {
            if (localeDayValue[i].toLowerCase() === s || localeAbbrevDayValue[i].toLowerCase() === s) {
                return i;
            }
        }
        return -1;
    }

    ///
    /// #### Parsing
    /// * `dateApi.parse(str, format = 'YYYY-MM-DD')`: parses `str` according to `format`. Returns a date.
    static parse(str: string, locale: LocalizeLocale = 'base', format?: string): DateValue {
        if (str == null)
            throw new Error(localizedText('@sage/xtrem-date-time/datetime__dateNull', '"date is null"', {}, locale));

        if (format == null) {
            // RFC 3339 by default -- very fast
            // No need to check the whole validity of the date, it will be checked by the constructor
            if (!/^\d{4}-\d{2}-\d{2}$/.test(str))
                throw new Error(
                    localizedText(
                        '@sage/xtrem-date-time/datetime__badFormat',
                        "bad date format, expected 'YYYY-MM-DD', got '{{format}}'",
                        { format: str },
                        locale,
                    ),
                );
            const value = parseInt(str.replace(/-/g, ''), 10);
            return new DateValue(value);
        }

        const dtObj = createDateTimeObj();
        parseDateTime(str, format, dtObj, locale);
        // ignore weekday
        return make(dtObj.year, dtObj.month, dtObj.day);
    }

    ///
    /// #### Current date
    /// * `dateApi.today(timeZone)`: returns the current date.
    static today(timeZone?: string | undefined): DateValue {
        return Datetime.now(false, timeZone).date;
    }

    ///
    /// #### Constructors
    /// * `dateApi.fromJsDate(jsDate, utc)`: convert a JavaScript `Date` to a date value.
    static fromJsDate(jsDate: Date, timeZone?: string | undefined): DateValue {
        if (!timeZone) return make(jsDate.getFullYear(), jsDate.getMonth() + 1, jsDate.getDate());
        if (timeZone === 'UTC') return make(jsDate.getUTCFullYear(), jsDate.getUTCMonth() + 1, jsDate.getUTCDate());
        // If timeZone, convert jsDate to a datetime in the time zone and then get its date.
        return datetime.fromJsDate(jsDate, timeZone).date;
    }

    static fromInternalValue(value: number): DateValue {
        return new DateValue(value);
    }

    /// * `dateApi.make(year, month, day)`: returns a date value for the specified year, month and day.
    static make(year: number, month: number, day: number): DateValue {
        return new DateValue(year * 10000 + month * 100 + day);
    }

    /// * `dateApi.makeInWeek(year, week, wday)`: returns a date value for the specified week and week day.
    static makeInWeek(year: number, week: number, wday: number): DateValue {
        const dday = wday ? wday - 1 : 6;
        return make(year, 1, 4)
            .begOfWeek(1)
            .addDays(7 * (week - 1) + dday);
    }

    static getWeekDayNames(startDay: WeekDayName = 'Sunday'): readonly WeekDayName[] {
        const startIndex = dayNames.indexOf(startDay);
        if (startIndex === 0) {
            return dayNames;
        }
        return dayNames.slice(startIndex).concat(dayNames.slice(0, startIndex));
    }

    static getWeekDayNumbers(startDay: WeekDay = WeekDay.sunday): readonly WeekDay[] {
        const days = [0, 1, 2, 3, 4, 5, 6];
        if (startDay === WeekDay.sunday) {
            return days;
        }
        return days.slice(startDay).concat(days.slice(0, startDay));
    }

    ///
    /// #### Instanceof
    /// * `dateApi.isDate(obj)`: returns true iff `obj` is a date value.
    static isDate(obj: any): obj is DateValue {
        // instanceOf is broken if multiple copies of the module are loaded
        return obj && obj.constructorName === 'DateValue';
    }
}

export const amDesignator = DateValue.amDesignator.bind(DateValue);
export const pmDesignator = DateValue.pmDesignator.bind(DateValue);
export const monthName = DateValue.monthName.bind(DateValue);
export const monthFromName = DateValue.monthFromName.bind(DateValue);
export const weekDayName = DateValue.weekDayName.bind(DateValue);
export const weekDayFromName = DateValue.weekDayFromName.bind(DateValue);
export const parse = DateValue.parse.bind(DateValue);
export const today = DateValue.today.bind(DateValue);
export const fromJsDate = DateValue.fromJsDate.bind(DateValue);
export const fromInternalValue = DateValue.fromInternalValue.bind(DateValue);
export const make = DateValue.make.bind(DateValue);
export const makeInWeek = DateValue.makeInWeek.bind(DateValue);
export const isDate = DateValue.isDate.bind(DateValue);
export const getWeekDayNames = DateValue.getWeekDayNames.bind(DateValue);
export const getWeekDayNumbers = DateValue.getWeekDayNumbers.bind(DateValue);
