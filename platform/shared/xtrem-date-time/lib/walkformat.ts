export function walkFormat(
    format: string,
    map: {
        [key: string]: (arg: any) => void;
    },
) {
    let i = 0;
    const len = format.length;

    function count(j: number) {
        const ch = format[j];
        let k = 1;
        while (format[j + k] === ch) k += 1;
        return k;
    }

    function getLiteral() {
        const j = format.indexOf(']', i);
        const result = j >= 0 ? format.substring(i, j) : format.substring(i);
        i = j >= 0 ? j + 1 : format.length;
        return result;
    }

    while (i < len) {
        const c = format[i];
        switch (c) {
            case '[':
                i += 1;
                map.literal(getLiteral());
                break;
            case 'A':
            case 'H':
            case 'h':
            case 'm':
            case 's':
            case 'S':
            case 'd':
            case 'D':
            case 'M':
            case 'Y':
            case 'z': {
                const repeat = count(i);
                map[c](repeat);
                i += repeat;
                break;
            }
            case 'Z': {
                const tzLength = 1;
                map[c](tzLength);
                // function will set tzLength
                i += tzLength;
                break;
            }
            default:
                map.literal(c);
                i += 1;
        }
    }
}
