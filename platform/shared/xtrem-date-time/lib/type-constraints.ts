export interface EnumValue {
    $value: number;
}

export interface TypeConstraints {
    $isMandatory?: boolean;
    $isNullable?: boolean;
    $minimum?: number;
    $maximum?: number;
    $minimumCanEqual?: boolean;
    $maximumCanEqual?: boolean;
    $divisibleBy?: number;
    $enum?: EnumValue[];
    $minLength?: number;
    $maxLength?: number;
    $pattern?: string;
    $patternModifiers?: string;
    $patternMessage?: string;
}
