import { LocalizeLocale } from '@sage/xtrem-shared';
import { Datetime, Time, date as XtremDate } from '.';
import { DateValue } from './date';
import { localizedText } from './localize';

export type DatePresetFormat =
    | 'FullDate'
    | 'LongMonth'
    | 'LongMonthYear'
    | 'MonthDay'
    | 'MonthYear'
    | 'LongMonthDayYear';

const localizeDatePresetFormat = (fmt: DatePresetFormat, locale: LocalizeLocale = 'en-US'): string => {
    switch (fmt) {
        case 'MonthDay': {
            return localizedText('@sage/xtrem-date-time/date-format-month-day', 'M/d', {}, locale);
        }
        case 'MonthYear': {
            return localizedText('@sage/xtrem-date-time/date-format-month-year', 'MM/YYYY', {}, locale);
        }
        case 'LongMonthDayYear': {
            return localizedText('@sage/xtrem-date-time/date-format-long-month-day-year', 'MMMM DD YYYY', {}, locale);
        }
        case 'LongMonthYear': {
            return localizedText('@sage/xtrem-date-time/date-format-long-month-year', 'MMMM YYYY', {}, locale);
        }
        case 'FullDate': {
            return localizedText('@sage/xtrem-date-time/date-format-full-date', 'DD/MM/YYYY', {}, locale);
        }
        case 'LongMonth': {
            return localizedText('@sage/xtrem-date-time/date-format-long-month', 'MMMM', {}, locale);
        }
        default:
            return localizedText('@sage/xtrem-date-time/date-format', 'DD/MM/YYYY', {}, locale);
    }
};

type FormableDate = Date | string | DateValue | Datetime;

export const formatDateToCurrentLocale = (
    date: FormableDate,
    locale: LocalizeLocale = 'en-US',
    fmt: DatePresetFormat = 'FullDate',
): string => {
    const localizedFmt = localizeDatePresetFormat(fmt, locale);
    let dateValue: DateValue;
    if (date instanceof DateValue) {
        dateValue = date;
    } else if (date instanceof Datetime) {
        dateValue = date.date;
    } else if (date instanceof Date) {
        dateValue = DateValue.fromJsDate(date);
    } else if (typeof date === 'string') {
        dateValue = DateValue.parse(date, locale, 'YYYY-MM-DD');
    } else {
        throw new Error(`Invalid date value: ${date}`);
    }

    return dateValue.format(localizedFmt, locale);
};

type FormableTime = Date | string | Time | Datetime;

export const formatTimeToLocale = (time: FormableTime, locale: LocalizeLocale = 'en-US'): string => {
    const localizedFmt = localizedText('@sage/xtrem-date-time/time-format-short', 'hh:mm A', {}, locale);
    let timeValue: Time;
    if (time instanceof Time) {
        timeValue = time;
    } else if (time instanceof Datetime) {
        timeValue = time.time;
    } else if (time instanceof Date) {
        timeValue = Time.fromJsDate(time);
    } else if (typeof time === 'string') {
        timeValue = Time.parse(time, locale, 'HH:mm');
    } else {
        throw new Error(`Invalid time value: ${time}`);
    }

    return timeValue.format(locale, localizedFmt);
};

export const parseLocalizedDate = (
    date: string,
    locale: LocalizeLocale = 'en-US',
    fmt: DatePresetFormat = 'FullDate',
): DateValue => {
    const localizedFormat = localizeDatePresetFormat(fmt, locale);
    return XtremDate.parse(date, locale, localizedFormat);
};

export const parseIsoDate = (date: string, locale: LocalizeLocale = 'en-US'): DateValue => {
    return XtremDate.parse(date, locale, 'YYYY-MM-DD');
};

export const isValidLocalizedDate = (
    date: string,
    locale: LocalizeLocale = 'en-US',
    fmt: DatePresetFormat = 'FullDate',
) => {
    try {
        parseLocalizedDate(date, locale, fmt);
        return true;
    } catch {
        return false;
    }
};

export const isValidIsoDate = (date: string, locale: LocalizeLocale = 'en-US') => {
    try {
        parseIsoDate(date, locale);
        return true;
    } catch {
        return false;
    }
};
