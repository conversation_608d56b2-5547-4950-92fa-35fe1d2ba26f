import { Range } from './range';

// eslint-disable-next-line @typescript-eslint/naming-convention
export type integer = number;

export class IntegerRange extends Range<integer> {
    static parse = parse;

    static isIntegerRange = isIntegerRange;

    static isValidIntegerRangeString = isValidIntegerRangeString;

    static make = make;

    // eslint-disable-next-line class-methods-use-this
    get constructorName(): string {
        return 'IntegerRange';
    }

    /** @internal */
    // eslint-disable-next-line class-methods-use-this
    incrementsByRangeUnit(elm: integer, step = 1): integer {
        return elm + step;
    }

    /** @internal */
    // eslint-disable-next-line class-methods-use-this
    getBoundValue(elm: integer | null): number | null {
        if (!elm) return null;
        return elm;
    }

    /** @internal */
    // eslint-disable-next-line class-methods-use-this
    getStringBoundValue(elm: integer | null): string {
        if (!elm) return '';
        return elm.toString();
    }
}

export function parse(str: string): IntegerRange {
    const integerRangeStr = str.replace(/[\s"]/g, '');

    if (!isValidIntegerRangeString(integerRangeStr)) {
        throw new Error(`Invalid integer range format: ${str}.`);
    }

    const integerRange = integerRangeStr.split(',');

    const start = integerRange[0].slice(1);
    const end = integerRange[1].slice(0, -1);
    const excludesStart = start.length === 0 || integerRange[0].slice(0, 1) === '(';
    const excludesEnd = end.length === 0 || integerRange[1].slice(-1) === ')';

    return new IntegerRange(
        start.length > 0 ? Number(start) : null,
        end.length > 0 ? Number(end) : null,
        excludesStart,
        excludesEnd,
    );
}

export function isIntegerRange(obj: any): obj is Range<integer> {
    return obj && obj.constructorName === 'IntegerRange';
}

function isValidIntegerRangeString(str: string): boolean {
    // ( or [
    const beginRangeRegex = /^(\[|\()/;
    // ) or ]
    const endRangeRegex = /(\]|\))$/;

    // integer
    const integerRegex = /(\d+)?/;

    // comma followed by at most one space
    const separatorRegex = /,/;

    const integerRangeRegex = new RegExp(
        `${beginRangeRegex.source}${integerRegex.source}${separatorRegex.source}${integerRegex.source}${endRangeRegex.source}`,
    );

    return integerRangeRegex.test(str.replace(/\s/g, ''));
}

export function make(start: integer | null, end: integer | null, excludesStart = false, excludesEnd = true) {
    return new IntegerRange(start, end, excludesStart, excludesEnd);
}
