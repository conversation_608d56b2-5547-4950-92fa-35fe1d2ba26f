export abstract class Range<T> {
    includedStart: T | null;

    excludedStart: T | null;

    includedEnd: T | null;

    excludedEnd: T | null;

    constructor(
        public start: T | null,
        public end: T | null,
        public excludesStart = false,
        public excludesEnd = true,
    ) {
        if (start) {
            if (this.excludesStart) {
                this.includedStart = this.incrementsByRangeUnit(this.start!);
                this.excludedStart = this.start;
            } else {
                this.includedStart = this.start;
                this.excludedStart = this.incrementsByRangeUnit(this.start!, -1);
            }
        } else {
            this.includedStart = null;
            this.excludedStart = null;
        }
        if (end) {
            if (this.excludesEnd) {
                this.includedEnd = this.incrementsByRangeUnit(this.end!, -1);
                this.excludedEnd = this.end;
            } else {
                this.includedEnd = this.end;
                this.excludedEnd = this.incrementsByRangeUnit(this.end!, 1);
            }
        } else {
            this.excludedEnd = null;
        }

        this.validateRange();
    }

    // return class name
    abstract get constructorName(): string;

    // should check if included start is greater than excluded end
    validateRange() {
        if (this.includedStart && this.excludedEnd) {
            if (this.getBoundValue(this.includedStart)! >= this.getBoundValue(this.excludedEnd)!) {
                throw new Error(
                    `Invalid range: included start ${this.getStringBoundValue(
                        this.includedStart,
                    )} should not be greater than excluded end ${this.getStringBoundValue(this.excludedEnd)}`,
                );
            }
        }
    }

    // Increment a range bound by unit. Ex. date increment
    abstract incrementsByRangeUnit(elm: T, step?: number): T;

    // get range bound value
    abstract getBoundValue(elm: T | null): number | null;

    // get range bound string value
    abstract getStringBoundValue(elm: T | null): string;

    includes(elmToCompareWith: T | null): boolean {
        if (this.excludedEnd === null && this.includedStart === null) return true;
        if (this.includedStart) {
            if (this.excludedEnd) {
                return (
                    this.getBoundValue(elmToCompareWith)! >= this.getBoundValue(this.includedStart)! &&
                    this.getBoundValue(elmToCompareWith)! < this.getBoundValue(this.excludedEnd)!
                );
            }
            return this.getBoundValue(elmToCompareWith)! >= this.getBoundValue(this.includedStart)!;
        }
        if (this.excludedEnd) {
            return this.getBoundValue(elmToCompareWith)! < this.getBoundValue(this.excludedEnd)!;
        }
        return false;
    }

    equals(rangeToCompareWith: Range<T> | null): boolean {
        return (
            this.getBoundValue(this.includedStart) === this.getBoundValue(rangeToCompareWith?.includedStart!) &&
            this.getBoundValue(this.excludedEnd) === this.getBoundValue(rangeToCompareWith?.excludedEnd!)
        );
    }

    toString(): string {
        const rangeBeginInclusionMark = this.excludesStart ? '(' : '[';
        const rangeEndInclusionMark = this.excludesEnd ? ')' : ']';
        const rangeStartDateMark = this.start ? this.getStringBoundValue(this.start) : '';
        const rangeEndDateMark = this.end ? this.getStringBoundValue(this.end) : '';
        return `${rangeBeginInclusionMark}${rangeStartDateMark},${rangeEndDateMark}${rangeEndInclusionMark}`;
    }

    toJSON(): string {
        return this.toString();
    }

    // abstract parse(str: string, format?: string): any;

    // abstract isDatetimeRange(obj: any): obj is Range<T>;

    // abstract make(start: T | null, end: T | null, excludesStart: boolean, excludesEnd: boolean): Range<T>;
}
