import { Decimal } from '@sage/xtrem-decimal';
import { Range } from './range';

export class DecimalRange extends Range<Decimal> {
    static parse = parse;

    static isDecimalRange = isDecimalRange;

    static isValidDecimalRangeString = isValidDecimalRangeString;

    static make = make;

    // eslint-disable-next-line class-methods-use-this
    get constructorName(): string {
        return 'DecimalRange';
    }

    /** @internal */
    // eslint-disable-next-line class-methods-use-this
    incrementsByRangeUnit(elm: Decimal): Decimal {
        // increment by scale value, ex: 1.003->1.004
        return elm.add(new Decimal(10).pow(-(elm.toString().split('.')?.[1]?.length || 1)));
    }

    /** @internal */
    // eslint-disable-next-line class-methods-use-this
    getBoundValue(elm: Decimal | null): number | null {
        if (!elm) return null;
        return Decimal.toNumber(elm);
    }

    /** @internal */
    // eslint-disable-next-line class-methods-use-this
    getStringBoundValue(elm: Decimal | null): string {
        if (!elm) return '';
        return elm.toString();
    }
}

export function parse(str: string): DecimalRange {
    const decimalRangeStr = str.replace(/[\s"]/g, '');

    if (!isValidDecimalRangeString(decimalRangeStr)) {
        throw new Error(`Invalid decimal range format: ${str}.`);
    }

    const decimalRange = decimalRangeStr.split(',');

    const start = decimalRange[0].slice(1);
    const end = decimalRange[1].slice(0, -1);
    const excludesStart = start.length === 0 || decimalRange[0].slice(0, 1) === '(';
    const excludesEnd = end.length === 0 || decimalRange[1].slice(-1) === ')';

    return new DecimalRange(
        start.length > 0 ? Decimal.make(start) : null,
        end.length > 0 ? Decimal.make(end) : null,
        excludesStart,
        excludesEnd,
    );
}

export function isDecimalRange(obj: any): obj is Range<Decimal> {
    return obj && obj.constructorName === 'DecimalRange';
}

function isValidDecimalRangeString(str: string): boolean {
    // ( or [
    const beginRangeRegex = /^(\[|\()/;
    // ) or ]
    const endRangeRegex = /(\]|\))$/;

    // decimal
    const decimalRegex = /((\d*\.)?\d+)?/;

    // comma followed by at most one space
    const separatorRegex = /,/;

    const decimalRangeRegex = new RegExp(
        `${beginRangeRegex.source}${decimalRegex.source}${separatorRegex.source}${decimalRegex.source}${endRangeRegex.source}`,
    );

    return decimalRangeRegex.test(str.replace(/\s/g, ''));
}

export function make(
    start: Decimal | string | number | null,
    end: Decimal | string | number | null,
    excludesStart = false,
    excludesEnd = true,
) {
    return new DecimalRange(
        start ? Decimal.make(start) : null,
        end ? Decimal.make(end) : null,
        excludesStart,
        excludesEnd,
    );
}
