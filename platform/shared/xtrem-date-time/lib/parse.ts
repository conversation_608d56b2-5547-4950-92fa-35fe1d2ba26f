import { LocalizeLocale } from '@sage/xtrem-shared';
import { abbreviatedDayNames, abbreviatedMonthNames, dayNames, monthNames } from './date';
import { Datetime } from './datetime';
import { localizedText } from './localize';
import { TimeZone } from './time-zone';
import { localizedGroup } from './utils';
import { walkFormat } from './walkformat';

interface DateTimeObject {
    day: number;
    month: number;
    year: number;
    hour: number;
    minute: number;
    second: number;
    millis: number;
    timeMode: number;
    abbrTimeMode: number;
    timeModes: string[];
    abbrTimeModes: string[];
    tzOffset?: number;
    timeZone?: string;
}

export function createDateTimeObj(
    timeModes: string[] = [],
    abbrTimeModes: string[] = [],
    timeZone: string | undefined = undefined,
): DateTimeObject {
    return {
        day: 0,
        month: 0,
        year: 0,
        hour: 0,
        minute: 0,
        second: 0,
        millis: 0,
        timeMode: -1,
        abbrTimeMode: -1,
        timeModes,
        abbrTimeModes,
        tzOffset: undefined,
        timeZone,
    };
}

export function parseDateTime(str: string, format: string, timeObj: DateTimeObject, locale: LocalizeLocale = 'base') {
    // position in str
    let j = 0;

    function parseInteger(max: number, strict = false) {
        if (!/\d/.test(str[j])) {
            throw new Error(
                localizedText(
                    '@sage/xtrem-date-time/datetime__noNumber',
                    'invalid date: expected number, got {{got}}',
                    { got: str.substring(j) },
                    locale,
                ),
            );
        }
        const beg = j;
        j += 1;
        while (j < beg + max && /\d/.test(str[j])) j += 1;
        const component = str.substring(beg, j);
        const actualCharCount = component.trim().length;
        if (strict && actualCharCount !== max) {
            throw new Error(
                `Date component does not contain enough digits, expected ${max}, found: ${actualCharCount}.`,
            );
        }
        return parseInt(component, 10);
    }

    function parseName(names: string[]) {
        for (let k = 0; k < names.length; k += 1) {
            const name = names[k];
            const len = name.length;
            if (str.substring(j, j + len).toUpperCase() === name.toUpperCase()) {
                j += len;
                return k;
            }
        }
        throw new Error(
            localizedText(
                '@sage/xtrem-date-time/datetime__unknown',
                'unknown date: {{date}}',
                { date: str.substring(j) },
                locale,
            ),
        );
    }

    function parseHour(arg: number): number {
        if (arg !== 1 && arg !== 2) {
            throw new Error(
                localizedText(
                    '@sage/xtrem-date-time/datetime__badHour',
                    'bad hour format: {{format}}',
                    { format: format.substring(j) },
                    locale,
                ),
            );
        }
        return parseInteger(2);
    }

    function parseMinute(arg: number): number {
        if (arg !== 2)
            throw new Error(
                localizedText(
                    '@sage/xtrem-date-time/datetime__badMin',
                    'bad minute format: {{format}}',
                    { format: format.substring(j) },
                    locale,
                ),
            );
        return parseInteger(2);
    }

    function parseTimeZoneAbbreviation() {
        const match = str.substring(j).match(/^[A-Z]+(\( \([A-Z]+\/[A-Z]+\))?/);
        const abbrev = match?.[0];
        if (abbrev) {
            j += abbrev.length;
            return abbrev;
        }
        throw new Error(
            localizedText(
                '@sage/xtrem-date-time/datetime__badTimeZone',
                'invalid time zone abbreviation: {{abbrev}}',
                { abbrev: str.substring(j) },
                locale,
            ),
        );
    }

    function parseTimeZone() {
        const availableTimeZones = TimeZone.getAvailableTimeZones();
        const i = parseName(availableTimeZones);
        return availableTimeZones[i];
    }

    walkFormat(format, {
        literal(literal) {
            if (str.substring(j, j + literal.length) !== literal) {
                throw new Error(
                    localizedText(
                        '@sage/xtrem-date-time/datetime__badMin',
                        'bad minute format: {{format}}',
                        { expected: literal, got: format.substring(j) },
                        locale,
                    ),
                );
            }
            j += literal.length;
        },
        d(arg) {
            switch (arg) {
                case 1:
                case 2:
                    timeObj.day = parseInteger(2);
                    break;
                case 3:
                    // /* weekday =*/ parseName();
                    /* weekday =*/ parseName(
                        localizedGroup(
                            abbreviatedDayNames,
                            '@sage/xtrem-date-time/datetime__abbreviatedDayName',
                            {},
                            locale,
                        ),
                    );
                    // /* weekday =*/ parseName(resources().abbreviatedDayNames);
                    break;
                case 4:
                    /* weekday =*/ parseName(
                        localizedGroup(dayNames, '@sage/xtrem-date-time/datetime__dayName', {}, locale),
                    );
                    break;
                default:
                    throw new Error(
                        localizedText(
                            '@sage/xtrem-date-time/datetime__badDay',
                            'bad day format: {{format}}',
                            { format: format.substring(j) },
                            locale,
                        ),
                    );
            }
        },
        D(arg) {
            switch (arg) {
                case 1:
                case 2:
                    timeObj.day = parseInteger(2);
                    break;
                default:
                    throw Datetime.formatDirectiveError(format, 'D', locale, arg);
            }
        },
        M(arg) {
            switch (arg) {
                case 1:
                case 2:
                    timeObj.month = parseInteger(2);
                    break;
                case 3:
                    timeObj.month =
                        parseName(
                            localizedGroup(
                                abbreviatedMonthNames,
                                '@sage/xtrem-date-time/datetime__abbreviatedMonthName',
                                {},
                                locale,
                            ),
                        ) + 1;
                    break;
                case 4:
                    timeObj.month =
                        parseName(localizedGroup(monthNames, '@sage/xtrem-date-time/datetime__monthName', {}, locale)) +
                        1;
                    // timeObj.month = parseName(resources().monthNames) + 1;
                    break;
                default:
                    throw new Error(
                        localizedText(
                            '@sage/xtrem-date-time/datetime__badDay',
                            'bad day format: {{format}}',
                            { format: format.substring(j) },
                            locale,
                        ),
                    );
            }
        },
        Y(arg) {
            switch (arg) {
                case 2:
                    timeObj.year = parseInteger(2, true);
                    timeObj.year = timeObj.year < 40 ? 2000 + timeObj.year : 1900 + timeObj.year;
                    break;
                case 4:
                    timeObj.year = parseInteger(4, true);
                    break;
                default:
                    throw new Error(
                        localizedText(
                            '@sage/xtrem-date-time/datetime__badYear',
                            'bad year format: {{format}}',
                            { format: format.substring(j) },
                            locale,
                        ),
                    );
            }
        },
        H(arg) {
            timeObj.hour = parseHour(arg);
        },
        h(arg) {
            timeObj.hour = parseHour(arg);
        },
        m(arg) {
            timeObj.minute = parseMinute(arg);
        },
        s(arg) {
            if (arg !== 2)
                throw new Error(
                    localizedText(
                        '@sage/xtrem-date-time/datetime__badSec',
                        'bad second format: {{format}}',
                        { format: format.substring(j) },
                        locale,
                    ),
                );
            timeObj.second = parseInteger(2);
        },
        S(arg) {
            if (arg !== 3) {
                throw Datetime.formatDirectiveError(format, 'S', locale, arg);
            }
            timeObj.millis = parseInteger(3);
        },
        A(arg) {
            if (arg !== 1)
                throw new Error(
                    localizedText(
                        '@sage/xtrem-date-time/datetime__badMarker',
                        'bad AM/PM marker format: {{marker}}',
                        { marker: format.substring(j) },
                        locale,
                    ),
                );
            timeObj.timeMode = parseName(timeObj.timeModes);
        },
        z(arg) {
            switch (arg) {
                case 1:
                    timeObj.timeZone = parseTimeZoneAbbreviation();
                    break;
                case 3:
                    timeObj.timeZone = parseTimeZone();
                    break;
                default:
                    throw Datetime.formatDirectiveError(format, 'z', locale, arg);
            }
        },
        Z() {
            // allowed characters
            const tzChars = ['Z', '+', '-'];
            // use parseName to check if the next character is one of the allowed characters
            const tzSign = tzChars[parseName(tzChars)];

            if (tzSign === 'Z') {
                // Z is UTC, set offset to 0, the parse function in date time will make a UTC date
                // as tzOffset is not undefined
                timeObj.tzOffset = 0;
            } else {
                // Sign is + or -
                // next four or five characters should should be HH:mm or HHmm for timezone
                const tzHours = parseHour(2);
                if (str[j] === ':') {
                    j += 1; // skip colon
                }
                const tzMinutes = parseMinute(2);
                // reverse sign vs. multiplier as we need to get offset
                // e.g. GMT+2 is an offset of -120 minutes, which is inverted to the + sign
                const multiplier = tzSign === '-' ? 1 : -1;
                timeObj.tzOffset = (tzHours * 60 + tzMinutes) * multiplier;
            }
        },
    });

    // from 12 format to 24 format, used when necessary
    function from12to24(mode: string[], index: number) {
        switch (mode[index]) {
            case mode[0]:
                // AM
                timeObj.hour = timeObj.hour === 12 ? 0 : timeObj.hour;
                break;
            case mode[1]:
                // PM
                if (timeObj.hour >= 1 && timeObj.hour <= 11) {
                    timeObj.hour += 12;
                }
                break;
            default:
                throw new Error(`bad mode: ${mode[index]}`);
        }
    }

    if (timeObj.timeMode >= 0 || timeObj.abbrTimeMode >= 0) {
        if (timeObj.hour > 12)
            throw new Error(
                localizedText(
                    '@sage/xtrem-date-time/datetime__badHourVal',
                    'bad hour value: expected 1<hour<12, got:{{got}} for mode:{{mode}}',
                    { got: timeObj.hour, mode: timeObj.timeMode },
                    locale,
                ),
            );
        const mode = timeObj.timeMode >= 0 ? timeObj.timeModes : timeObj.abbrTimeModes;
        const index = timeObj.timeMode >= 0 ? timeObj.timeMode : timeObj.abbrTimeMode;
        from12to24(mode, index);
    }
}
