/// !doc
/// [x3js dev guide](x3js-development-guide) > [x3js APIs](x3js-apis) > x3js time
///
/// # Time type
///
/// `import time as timeApi from 'x3js';`
///
import { LocalizeLocale } from '@sage/xtrem-shared';
import { Datetime } from './datetime';
import { localizedText } from './localize';
import { createDateTimeObj, parseDateTime } from './parse';
import { walkFormat } from './walkformat';

function pad(val: number) {
    const s = val.toString();
    return s.length === 1 ? `0${s}` : s;
}

function parse2(str: string, beg: number, end: number) {
    return parseInt(str.substring(beg, end), 10);
}

export type TimeDiff = {
    hours?: number;
    minutes?: number;
    seconds?: number;
    millis?: number;
};

export class Time {
    private readonly _value: number;

    constructor(value: number) {
        // force modulo on value to bring it back in range
        this._value = (value + 86400 * 365 * 10000) % 86400;
    }

    // eslint-disable-next-line class-methods-use-this
    get constructorName() {
        return 'Time';
    }

    ///
    /// ## Properties
    /// * `time.hour`:  the time's hour, between 0 and 23
    get hour() {
        return Math.floor(this._value / 3600);
    }

    /// * `time.minute`:  the time's minute, between 0 and 59
    get minute() {
        return Math.floor(this._value / 60) % 60;
    }

    /// * `time.second`:  the time's second, between 0 and 59
    get second() {
        return this._value % 60;
    }

    /// * `time.value`:  the time's internal value, between 0 and 86400
    get value() {
        return this._value;
    }

    valueOf() {
        return this.toString();
    }

    /// * `time.toJSON()
    ///   converts to JSON format
    toJSON() {
        return this.toString();
    }

    ///
    /// ## Methods
    ///
    /// #### Comparisons
    /// * `time1.compare(time2)`: compares two times.
    ///   returns a positive integer if `time1 > time2`, a negative one if `time1 < time2`.
    compare(time: Time) {
        return this._value - time._value;
    }

    /// * `time1.equals(time2)`: true if the times are equal, false otherwise.
    equals(time: Time) {
        return this._value === time._value;
    }

    /// * `time.isBetween(begin, end)`: true iff time is between `begin` and `end` time values (inclusive).
    isBetween(begin: Time, end: Time) {
        return begin._value <= this._value && this._value <= end._value;
    }

    ///
    /// #### Beginning and end of periods
    /// * `time.begOfDay()`: returns 00:00:00, as a time.
    // eslint-disable-next-line class-methods-use-this
    begOfDay() {
        return make(0, 0, 0);
    }

    /// * `time.endOfDay()`: returns 23:59:59, as a time.
    // eslint-disable-next-line class-methods-use-this
    endOfDay() {
        return make(23, 59, 59);
    }

    /// * `time.begOfHour()`: returns the beginning of time's hour, as a time.
    begOfHour() {
        return make(this.hour, 0, 0);
    }

    /// * `time.endOfHour()`: returns the end of time's hour, as a time.
    endOfHour() {
        return make(this.hour, 59, 59);
    }

    /// * `time.begOfMinute()`: returns the beginning of time's minute, as a time.
    begOfMinute() {
        return make(this.hour, this.minute, 0);
    }

    /// * `time.endOfMinute()`: returns the end of time's minute, as a time.
    endOfMinute() {
        return make(this.hour, this.minute, 59);
    }

    toString() {
        const { hour, minute, second } = this;
        return `${pad(hour)}:${pad(minute)}:${pad(second)}`;
    }

    ///
    /// #### Formatting
    /// * `time.toString(format = 'HH:mm:ss')`: formats the time according to `format`.
    format(locale: LocalizeLocale = 'base', format?: string) {
        const { hour, minute, second } = this;
        if (format == null) {
            return `${pad(hour)}:${pad(minute)}:${pad(second)}`;
        }
        let result = '';
        walkFormat(format, {
            literal(lit: string) {
                result += lit;
            },
            H(repeat: number) {
                switch (repeat) {
                    case 1:
                        result += hour;
                        break;
                    case 2:
                        result += pad(hour);
                        break;
                    default:
                        throw Datetime.formatDirectiveError(format, 'H', locale, repeat);
                }
            },
            h(repeat: number) {
                switch (repeat) {
                    case 1:
                        if (hour === 0) {
                            result += 12;
                        } else {
                            result += hour > 12 ? hour - 12 : hour;
                        }
                        break;
                    case 2:
                        if (hour === 0) {
                            result += 12;
                        } else {
                            result += hour > 12 ? pad(hour - 12) : pad(hour);
                        }
                        break;
                    default:
                        throw Datetime.formatDirectiveError(format, 'h', locale, repeat);
                }
            },
            m(repeat: number) {
                if (repeat !== 2) throw Datetime.formatDirectiveError(format, 'm', locale, repeat);
                result += pad(minute);
            },
            s(repeat: number) {
                if (repeat !== 2) throw Datetime.formatDirectiveError(format, 's', locale, repeat);
                result += pad(second);
            },
            A(repeat: number) {
                if (repeat !== 1) throw Datetime.formatDirectiveError(format, 'A', locale, repeat);
                result += hour < 12 ? amDesignator(locale) : pmDesignator(locale);
            },
        });
        return result;
    }

    ///
    /// #### Adding and subtracting
    /// * `time1.secondsDiff(time2)`: returns the number of seconds between `time2` and `time1`.
    secondsDiff(t: Time) {
        return this._value - t._value;
    }

    /// * `time.addHours(n)`: adds `n` hours to the time, returns another time
    addHours(hours: number) {
        return hours === 0 ? this : new Time(this._value + hours * 3600);
    }

    /// * `time.addMinutes(n)`: adds `n` minutes to the time, returns another time
    addMinutes(minutes: number) {
        return minutes === 0 ? this : new Time(this._value + minutes * 60);
    }

    /// * `time.addSeconds(n)`: adds `n` seconds to the time, returns another time
    addSeconds(seconds: number) {
        return seconds === 0 ? this : new Time(this._value + seconds);
    }

    /// * `time.add(delta)`: adds `delta` to the time. `delta` is an object with optional `hours`, `minutes` and `seconds` properties.
    ///   Returns another time.
    ///   For example `t.add({ minutes: 2 })` adds 2 minutes to `t`.
    add(delta: TimeDiff) {
        return this.addHours(delta.hours || 0)
            .addMinutes(delta.minutes || 0)
            .addSeconds(delta.seconds || 0);
    }

    /// Note: time values circle silently from 23:59:59 to 00:00:00 when values are added
    isNull() {
        return this._value === 0;
    }

    ///
    /// ## Functions
    ///
    /// #### Constructors
    /// * `timeApi.make(hour, minute, second)`: returns a time value for the specified hour, minute and second.
    static make(hour: number, minute: number, second: number) {
        return new Time(hour * 3600 + (minute || 0) * 60 + (second || 0));
    }

    /// * `timeApi.fromSeconds(seconds)`: converts a number of seconds since 00:00:00 to a time value.
    static fromSeconds(seconds: number) {
        return new Time(seconds);
    }

    /// * `timeApi.fromJsDate(jsDate, timeZone)`: convert the time part of a JavaScript `Date` to a time value.
    static fromJsDate(js: Date, timeZone?: string) {
        if (!timeZone) make(js.getHours(), js.getMinutes(), js.getSeconds());
        if (timeZone === 'UTC') make(js.getUTCHours(), js.getUTCMinutes(), js.getUTCSeconds());
        // If timeZone, convert jsDate to a datetime in the time zone and then get its date.
        return Datetime.fromJsDate(js, timeZone).time;
    }

    /// #### Current time
    /// * `timeApi.now(timeZone)`: returns the current time.
    static now(timeZone?: string) {
        return Datetime.now(false, timeZone).time;
    }

    ///
    /// #### Parsing
    /// * `timeApi.parse(str, format = 'HH:mm:ss')`: parses `str` according to `format`. Returns a time.
    static parse(str: string, locale: LocalizeLocale = 'base', format?: string) {
        if (str == null)
            throw new Error(localizedText('@sage/xtrem-date-time/datetime__isNull', 'datetime is null', {}, locale));

        if (format == null) {
            // RFC 3339 full time without fraction -- very fast
            // HH:mm
            if (str.length !== 5 && str.length !== 8)
                throw new Error(
                    localizedText(
                        '@sage/xtrem-date-time/datetime__badFormat',
                        "bad date format, expected 'YYYY-MM-DD', got '{{format}}'",
                        { format: str },
                        locale,
                    ),
                );

            const value =
                str.length === 8
                    ? parse2(str, 0, 2) * 3600 + parse2(str, 3, 5) * 60 + parse2(str, 6, 8)
                    : parse2(str, 0, 2) * 3600 + parse2(str, 3, 5) * 60;
            return new Time(value);
        }

        const timeModes = [amDesignator(locale), pmDesignator(locale)];
        const dtObj = createDateTimeObj(timeModes, [timeModes[0].substring(0, 1), timeModes[1].substring(0, 1)]);
        parseDateTime(str, format, dtObj, locale);
        return make(dtObj.hour, dtObj.minute, dtObj.second);
    }

    ///
    /// #### Localized names for AM/PM designators
    /// * `timeApi.amDesignator()`: returns the AM designator in the current locale.
    static amDesignator(locale: LocalizeLocale = 'base') {
        return localizedText('@sage/xtrem-date-time/datetime__amDesignator', 'AM', {}, locale);
    }

    /// * `timeApi.pmDesignator()`: returns the PM designator in the current locale.
    static pmDesignator(locale: LocalizeLocale = 'base') {
        return localizedText('@sage/xtrem-date-time/datetime__pmDesignator', 'PM', {}, locale);
    }

    ///
    /// #### Instanceof
    /// * `timeApi.isTime(obj)`: returns true iff `obj` is a time value.
    static isTime(obj: any): boolean {
        // instanceOf is broken if multiple copies of the module are loaded
        return obj && obj.constructorName === 'Time';
    }
}

export const make = Time.make.bind(Time);
export const fromSeconds = Time.fromSeconds.bind(Time);
export const fromJsDate = Time.fromJsDate.bind(Time);
export const now = Time.now.bind(Time);
export const parse = Time.parse.bind(Time);
export const amDesignator = Time.amDesignator.bind(Time);
export const pmDesignator = Time.pmDesignator.bind(Time);
export const isTime = Time.isTime.bind(Time);
