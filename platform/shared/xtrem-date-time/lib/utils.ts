import { LocalizeLocale } from '@sage/xtrem-shared';
import { DateValue } from './date';
import { Datetime } from './datetime';
import { localizedText } from './localize';
import type { DatePropertyValue, DateTimePropertyValue } from './types';

export function pad2(val: number) {
    const s = val.toString();
    return s.length === 1 ? `0${s}` : s;
}

export function pad3(val: number) {
    const s = val.toString();
    switch (s.length) {
        case 1:
            return `00${s}`;
        case 2:
            return `0${s}`;
        default:
            return s;
    }
}

/**
 * All i18n keys are individual records. Some of these records needs to function in a group i.e. days of the week and months.
 * @param baseResource string[] - see datetime/baseResource.ts
 * @param data object | any[] - default is any. Data for i18n value handlebars
 * @param i18nKey string - example: @sage/xtrem-date-time/datetime__abbreviatedDayName - NB! without the index. see datetime/i18n/en-US.json
 * @param locale LocalizeLocale - default is base
 * @returns string[] of localized semantic values
 */
export function localizedGroup(
    baseResource: readonly string[],
    i18nKey: string,
    data: object | any[] = {},
    locale: LocalizeLocale = 'base',
): string[] {
    const result = [];

    for (let i = 0; i < baseResource.length; i += 1) {
        result.push(localizedText(`${i18nKey}${i}`, baseResource[i], data, locale));
    }

    return result;
}

// eslint-disable-next-line no-useless-escape
const dateRegexp = /^\d{4}\-(0?[1-9]|1[012])-(0?[1-9]|[12][0-9]|3[01])$/;
const isoDateTimeRegexp =
    // eslint-disable-next-line no-useless-escape
    /^([\+-]?\d{4}(?!\d{2}\b))((-?)((0[1-9]|1[0-2])(\3([12]\d|0[1-9]|3[01]))?|W([0-4]\d|5[0-2])(-?[1-7])?|(00[1-9]|0[1-9]\d|[12]\d{2}|3([0-5]\d|6[1-6])))([T\s]((([01]\d|2[0-3])((:?)[0-5]\d)?|24\:?00)([\.,]\d+(?!:))?)?(\17[0-5]\d([\.,]\d+)?)?([zZ]|([\+-])([01]\d|2[0-3]):?([0-5]\d)?)?)?)?$/;

export const isStringDate = (value: any): boolean =>
    typeof value === 'string' && (!!value.match(dateRegexp) || !!value.match(isoDateTimeRegexp));

export function datePropertyValueToDateString(value: null | undefined, keepTime?: boolean): null;
export function datePropertyValueToDateString(
    value: DatePropertyValue | DateTimePropertyValue,
    keepTime?: boolean,
): string;
export function datePropertyValueToDateString(
    value: DatePropertyValue | DateTimePropertyValue | null | undefined,
    keepTime?: boolean,
): string | null;
export function datePropertyValueToDateString(
    value: DatePropertyValue | DateTimePropertyValue | null | undefined,
    keepTime = false,
): string | null {
    if (!value) {
        return null;
    }

    if (value instanceof Date && keepTime) {
        return Datetime.fromJsDate(value).toString();
    }

    if (value instanceof Date) {
        return DateValue.fromJsDate(value).toString();
    }

    if (typeof value === 'string' && isStringDate(value) && keepTime) {
        if (value.length === 10) {
            return `${value}T00:00:00.000Z`;
        }
        return value;
    }

    if (typeof value === 'string' && isStringDate(value)) {
        return value.substring(0, 10);
    }

    if (value instanceof DateValue) {
        return value.toString();
    }

    if (value instanceof Datetime) {
        if (!keepTime) {
            return value.date.toString();
        }
        return value.toString();
    }

    throw new Error(`Invalid date provided: ${value}`);
}

export function isValidDatePropertyValue(value: DatePropertyValue): boolean {
    if (!value) {
        return false;
    }
    try {
        datePropertyValueToDateString(value);
        return true;
    } catch {
        return false;
    }
}
