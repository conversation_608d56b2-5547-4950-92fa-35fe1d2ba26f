import dayjs = require('dayjs');
import timeZonePlugin = require('dayjs/plugin/timezone');
import utcPlugin = require('dayjs/plugin/utc');
import advancedFormatPlugin = require('dayjs/plugin/advancedFormat');
import { DateValue } from './date';
import { Time } from './time';
import { pad3 } from './utils';

dayjs.extend(timeZonePlugin);
dayjs.extend(utcPlugin);
dayjs.extend(advancedFormatPlugin);

export class TimeZone {
    static getAvailableTimeZones(): string[] {
        return Intl.supportedValuesOf('timeZone');
    }

    /** @internal */
    static getUtcValue(
        timeZone: string,
        year: number,
        month: number,
        day: number,
        hour?: number,
        minute?: number,
        second?: number,
        millis?: number,
    ) {
        const dateStr = DateValue.make(year, month, day).toString();
        const timeStr = Time.make(hour || 0, minute || 0, second || 0).toString();
        const millisStr = millis ? pad3(millis) : '000';
        const str = `${dateStr}T${timeStr}.${millisStr}`;

        return dayjs.tz(str, timeZone).valueOf();
    }

    /** @internal */
    static getDayjs(timeZone: string | undefined, value: number): dayjs.Dayjs {
        return dayjs.tz(value, timeZone);
    }
}
