/// !doc
/// [x3js dev guide](x3js-development-guide) > [x3js APIs](x3js-apis) > x3js datetime
///
/// # Datetime type
///
/// `import datetime as dtApi from 'x3js';`
///
import { LocalizeLocale } from '@sage/xtrem-shared';
import * as date from './date';
import { integer } from './date';
import { localizedText } from './localize';
import { createDateTimeObj, parseDateTime } from './parse';
import * as time from './time';
import { TimeZone } from './time-zone';
import { pad2, pad3 } from './utils';
import { walkFormat } from './walkformat';

// returns millis value for seconds and milliseconds part

export type DatetimeDiff = date.DateDiff & time.TimeDiff;

/**
 * A `Datetime` represents a specific point in time.
 *
 * The main differences with JavaScript `Date` are:
 *
 * - `Datetime` is immutable.
 * - The `month` property takes values between 1 and 12, not 0 and 11.
 */
export class Datetime {
    /** Internal value: GMT millis since epoch (Jan 1st, 1970) */
    private readonly _value: number;

    /** The time zone used to format the value */
    private readonly _timeZone: string | undefined;

    /**
     * Internal cache of the UTC values (year, month, day, hour, minute, second).
     * Packed int with year, month, day, hour, minute, second.
     * See Datetime.pack for details.
     */
    private _utcValues: number;

    /**
     * Internal cache of the time zone field values (year, month, day, hour, minute, second).
     * Packed int with year, month, day, hour, minute, second.
     * See Datetime.pack for details.
     */
    private _tzValues: number;

    private constructor(value: number, timeZone: string | undefined) {
        this._value = value;
        this._timeZone = timeZone;
    }

    /**
     * Pack datetime components into an int.
     * JS int is 53 bits, so we can pack year, month, day, hour, minute, second on powers of 10 without overflow
     * The millisecond part is not included as it is independent from UTC / time zone and can be obtained from #value.
     */
    private static pack(
        year: integer,
        month: integer,
        day: integer,
        hour: integer,
        minute: integer,
        second: integer,
    ): integer {
        return (
            // pack the date part like the internal value of a Date object
            // and multiply by 100_000 to leave room for the time part
            (year * 100 * 100 + month * 100 + day) * 100_000 +
            // pack the time part like the internal value of a Time object
            (hour * 3600 + minute * 60 + second)
        );
    }

    // get UTC components packed into an int
    // millis are not included as they are independent from UTC / time zone
    private get utcValue() {
        let utc = this._utcValues;

        if (!utc) {
            const d = new Date(this._value);
            utc = Datetime.pack(
                d.getUTCFullYear(), //
                d.getUTCMonth() + 1,
                d.getUTCDate(),
                d.getUTCHours(),
                d.getUTCMinutes(),
                d.getUTCSeconds(),
            );
            this._utcValues = utc;
        }
        return utc;
    }

    // get time zone components packed into an int
    // millis are not included as they are independent from UTC / time zone
    private get tzValue() {
        let tzValue = this._tzValues;
        if (!tzValue) {
            if (this._timeZone) {
                const d = TimeZone.getDayjs(this._timeZone, this.value);
                tzValue = Datetime.pack(d.year(), d.month() + 1, d.date(), d.hour(), d.minute(), d.second());
            } else {
                const d = new Date(this._value);
                tzValue = Datetime.pack(
                    d.getFullYear(), //
                    d.getMonth() + 1,
                    d.getDate(),
                    d.getHours(),
                    d.getMinutes(),
                    d.getSeconds(),
                );
            }
            this._tzValues = tzValue;
        }
        return tzValue;
    }

    // eslint-disable-next-line class-methods-use-this
    get constructorName() {
        return 'Datetime';
    }

    get timeZone(): string | undefined {
        return this._timeZone;
    }

    /** The date (only year, month, day). */
    get date(): date.DateValue {
        return date.fromInternalValue(Math.floor(this.tzValue / 100000));
    }

    /** The time (only hour, minute, second, millisecond). */
    get time(): time.Time {
        return time.fromSeconds(this.tzValue % 100000);
    }

    /** The year, between 0 and 9999. */
    get year(): integer {
        return this.date.year;
    }

    /** The month, between 1 and 12. */
    get month(): integer {
        return this.date.month;
    }

    /** The day, between 1 and 31. */
    get day(): integer {
        return this.date.day;
    }

    /** The week day, between 0 (Sunday) and 6 (Saturday). */
    get weekDay(): date.WeekDay {
        return this.date.weekDay;
    }

    /** The day of the year, between 1 and 366. */
    get yearDay(): integer {
        return this.date.yearDay;
    }

    /** The hour, between 0 and 23. */
    get hour(): integer {
        return this.time.hour;
    }

    /** The minute, between 0 and 59. */
    get minute(): integer {
        return this.time.minute;
    }

    /** The second, between 0 and 59. */
    get second(): integer {
        return this.time.second;
    }

    /** The millisecond, between 0 and 999. */
    get millisecond(): integer {
        // add millis for approx 2000 years to ensure positive before applying modulo
        return (this._value + 2000 * 365 * 24 * 3600 * 1000) % 1000;
    }

    /// * `dt.utcDate`:  the datetime's date value if interpreted in UTC timezone
    get utcDate() {
        return date.fromInternalValue(Math.floor(this.utcValue / 100000));
    }

    /// * `dt.utcTime`:  the datetime's time value if interpreted in UTC timezone
    get utcTime() {
        return time.fromSeconds(this.utcValue % 100000);
    }

    /// * `dt.utcYear`:  the datetime's UTC year, between 0 and 9999
    get utcYear() {
        return this.utcDate.year;
    }

    /// * `dt.utcMonth`:  the datetime's UTC month, between 1 and 12
    get utcMonth() {
        return this.utcDate.month;
    }

    /// * `dt.utcDay`:  the datetime's UTC month day, between 1 and 31
    get utcDay() {
        return this.utcDate.day;
    }

    /// * `dt.utcWeekDay`: the datetime's UTC week day between 0 (Sunday) and 6 (Saturday)
    get utcWeekDay() {
        return this.utcDate.weekDay;
    }

    /// * `dt.utcYearDay`: the datetime's UTC year day, between 1 and 366
    get utcYearDay() {
        return this.utcDate.yearDay;
    }

    /// * `dt.utcHour`:  the datetime's UTC hour, between 0 and 23
    get utcHour() {
        return this.utcTime.hour;
    }

    /// * `dt.utcMinute`:  the datetime's UTC minute, between 0 and 59
    get utcMinute() {
        return this.utcTime.minute;
    }

    /// * `dt.utcSecond`:  the datetime's UTC second, between 0 and 59
    get utcSecond() {
        return this.utcTime.second;
    }

    /// * `dt.utcMillisecond`:  the datetime's UTC millisecond, between 0 and 999
    get utcMillisecond() {
        return this.millisecond;
    }

    /// * `dt.value`:  the datetime's internal value (number of milliseconds since Jan 1st, 1970)
    get value() {
        return this._value;
    }

    ///
    /// ## Methods
    ///
    /// #### Comparisons
    /// * `dt1.compare(dt2)`: compares two datetime values.
    ///   returns a positive integer if `dt1 > dt2`, a negative one if `dt1 < dt2`.
    compare(dt: Datetime) {
        return this._value - dt._value;
    }

    /// * `dt1.equals(dt2)`: true if the datetime values are equal, false otherwise.
    equals(dt: Datetime) {
        return this._value === dt._value;
    }

    /// * `dt.isBetween(begin, end)`: true iff datetime is between `begin` and `end` datetime values (inclusive).
    isBetween(begin: Datetime, end: Datetime) {
        return begin._value <= this._value && this._value <= end._value;
    }

    ///
    /// #### Adding and subtracting
    /// * `dt.addYears(n)`: adds `n` years to the datetime, retuns another datetime.
    addYears(years: number) {
        return this.date.addYears(years).at(this.time, this.timeZone, this.millisecond);
    }

    /// * `dt.addMonths(n)`: adds `n` months to the datetime, retuns another datetime.
    addMonths(months: number) {
        return this.date.addMonths(months).at(this.time, this.timeZone, this.millisecond);
    }

    /// * `dt.addWeeks(n)`: adds `n` weeks to the datetime, retuns another datetime.
    addWeeks(weeks: number) {
        return this.date.addWeeks(weeks).at(this.time, this.timeZone, this.millisecond);
    }

    /// * `dt.addDays(n)`: adds `n` days to the datetime, retuns another datetime.
    addDays(days: number) {
        return this.date.addDays(days).at(this.time, this.timeZone, this.millisecond);
    }

    /// * `dt.addHours(n)`: adds `n` hours to the datetime, retuns another datetime.
    addHours(hours: number) {
        return new Datetime(this._value + hours * 3600 * 1000, this._timeZone);
    }

    /// * `dt.addMinutes(n)`: adds `n` minutes to the datetime, retuns another datetime.
    addMinutes(minutes: number) {
        return new Datetime(this._value + minutes * 60 * 1000, this._timeZone);
    }

    /// * `dt.addSeconds(n)`: adds `n` seconds to the datetime, retuns another datetime.
    addSeconds(seconds: number) {
        return new Datetime(this._value + seconds * 1000, this._timeZone);
    }

    /// * `dt.addMilliseconds(n)`: adds `n` milliseconds to the datetime, retuns another datetime.
    addMilliseconds(millis: number) {
        return new Datetime(this._value + millis, this._timeZone);
    }

    /// * `dt.addDayFractions(n)`: adds `n` days to the datetime where `n` may include a decimal fraction.
    addDayFractions(fraction: number) {
        return new Datetime(this._value + fraction * 86400 * 1000, this.timeZone);
    }

    /// * `dt1.millisDiff(dt2)`: returns the number of milliseconds between `dt2` and `dt1`.
    millisDiff(dt: Datetime) {
        return this._value - dt._value;
    }

    withoutMillis(): Datetime {
        return new Datetime(Math.floor(this.value / 1000) * 1000, this._timeZone);
    }

    inTimeZone(timeZone: string | undefined): Datetime {
        return this._timeZone === timeZone ? this : new Datetime(this._value, timeZone);
    }

    /// * `dt.add(delta)`: adds `delta` to the datetime. `delta` is an object with optional
    /// `years`, `months`, `days`, `hours`, `minutes`, `seconds`, `millis` properties.
    ///   Returns another datetime.
    ///   For example `dt.add({ months: 2 })` adds 2 months to `dt`.
    add(delta: DatetimeDiff) {
        return this.addYears(delta.years || 0)
            .addMonths(delta.months || 0)
            .addWeeks(delta.weeks || 0)
            .addDays(delta.days || 0)
            .addHours(delta.hours || 0)
            .addMinutes(delta.minutes || 0)
            .addSeconds(delta.seconds || 0)
            .addMilliseconds(delta.millis || 0);
    }

    toString() {
        return new Date(this._value).toISOString();
    }

    static formatDirectiveError(format: string, letter: string, locale: LocalizeLocale, repeat: number): Error {
        return new Error(
            localizedText(
                '@sage/xtrem-date-time/datetime__bad_format_directive',
                "Bad format directive '{{directive}}' in '{{format}}'",
                { format, directive: letter.repeat(repeat) },
                locale,
            ),
        );
    }

    ///
    /// #### Formatting
    /// * `dt.toString(format)`: formats the datetime according to `format`.
    ///   `format` defaults to ISO format.
    // eslint-disable-next-line @typescript-eslint/default-param-last
    format(locale: LocalizeLocale = 'base', format: string) {
        const utc = getIndexOfZ(format);
        const dateVal = utc ? this.utcDate : this.date;
        const timeVal = utc ? this.utcTime : this.time;

        let result = '';
        const { millisecond, second, hour } = this;

        walkFormat(format, {
            literal(lit: string) {
                result += lit;
            },
            H(repeat: number) {
                switch (repeat) {
                    case 1:
                        result += timeVal.hour.toString();
                        break;
                    case 2:
                        result += pad2(timeVal.hour);
                        break;
                    default:
                        throw Datetime.formatDirectiveError(format, 'H', locale, repeat);
                }
            },
            h(repeat: number) {
                switch (repeat) {
                    case 1:
                        result += timeVal.hour < 13 ? timeVal.hour : timeVal.hour - 12;
                        break;
                    case 2:
                        result += pad2(timeVal.hour < 13 ? timeVal.hour : timeVal.hour - 12);
                        break;
                    default:
                        throw Datetime.formatDirectiveError(format, 'h', locale, repeat);
                }
            },
            m(repeat: number) {
                if (repeat !== 2) {
                    throw Datetime.formatDirectiveError(format, 'm', locale, repeat);
                }
                result += pad2(timeVal.minute);
            },
            S(repeat: number) {
                if (repeat === 3) result += pad3(millisecond);
                else throw Datetime.formatDirectiveError(format, 'S', locale, repeat);
            },
            s(repeat: number) {
                if (repeat !== 2) {
                    throw Datetime.formatDirectiveError(format, 's', locale, repeat);
                }
                result += pad2(second);
            },
            A(repeat: number) {
                if (repeat !== 1) throw Datetime.formatDirectiveError(format, 'A', locale, repeat);
                result +=
                    hour < 12
                        ? localizedText('@sage/xtrem-date-time/datetime__amDesignator', 'AM', {}, locale)
                        : localizedText('@sage/xtrem-date-time/datetime__pmDesignator', 'PM', {}, locale);
            },
            d(repeat: number) {
                switch (repeat) {
                    case 1:
                        result += dateVal.day.toString();
                        break;
                    case 2:
                        result += pad2(dateVal.day);
                        break;
                    case 3:
                        result += date.weekDayName(dateVal.weekDay, locale, true);
                        break;
                    case 4:
                        result += date.weekDayName(dateVal.weekDay, locale);
                        break;
                    default:
                        throw Datetime.formatDirectiveError(format, 'd', locale, repeat);
                }
            },
            D(repeat: number) {
                switch (repeat) {
                    case 1:
                        result += dateVal.day.toString();
                        break;
                    case 2:
                        result += pad2(dateVal.day);
                        break;
                    default:
                        throw Datetime.formatDirectiveError(format, 'D', locale, repeat);
                }
            },
            M(repeat: number) {
                switch (repeat) {
                    case 1:
                        result += dateVal.month.toString();
                        break;
                    case 2:
                        result += pad2(dateVal.month);
                        break;
                    case 3:
                        result += date.monthName(dateVal.month, locale, true);
                        break;
                    case 4:
                        result += date.monthName(dateVal.month, locale);
                        break;
                    default:
                        throw Datetime.formatDirectiveError(format, 'M', locale, repeat);
                }
            },
            Y(repeat: number) {
                switch (repeat) {
                    case 2:
                        result += dateVal.year.toString().substring(2, 4);
                        break;
                    case 4:
                        result += dateVal.year.toString();
                        break;
                    default:
                        throw Datetime.formatDirectiveError(format, 'Y', locale, repeat);
                }
            },
            z: (repeat: number) => {
                switch (repeat) {
                    case 1:
                        result += TimeZone.getDayjs(this.timeZone, this.value).format('z');
                        break;
                    case 3:
                        result += this.timeZone || TimeZone.getDayjs(this.timeZone, this.value).format('zzz');
                        break;
                    default:
                        throw Datetime.formatDirectiveError(format, 'z', locale, repeat);
                }
            },
            Z() {
                // will only call Z if Z is present in the format
                result += 'Z';
            },
        });
        return result;
    }

    ///
    /// #### Conversions
    /// * `dt.toJsDate()`: converts the datetime to a JavaScript `Date` object.
    toJsDate() {
        return new Date(this._value);
    }

    /// * `dt.toJSON()
    ///   converts to JSON format
    toJSON() {
        return this.toString();
    }

    // undocumented
    withoutTimezoneOffset() {
        return new Datetime(this._value, this.timeZone);
    }

    // undocumented
    isNull() {
        return this._value === 0;
    }

    ///
    /// ## Functions
    private static parseIso(str: string, locale: LocalizeLocale, timeZone?: string) {
        function getOffset(sign: string, hours = 0, minutes = 0) {
            let offset = 0;
            if (sign === '-') offset = -hours * 60 - minutes;
            else if (sign === '+') offset = hours * 60 + minutes;
            return offset;
        }

        let year = 0;
        let month = 0;
        let day = 0;
        let hours = 0;
        let minutes = 0;
        let seconds = 0;
        let ms = 0;
        const d =
            /^(\d{4})-(\d{2})-(\d{2})[T\s]?(\d{2})?:?(\d{2}):?(\d{2})?(?:\.(\d{1,3}))?(Z)?(?:([+-])(\d{2}):?(\d{2})?)?$/.exec(
                str,
            ) as any[] | null;
        if (!d)
            throw new Error(
                localizedText(
                    '@sage/xtrem-date-time/datetime__noParse',
                    'cannot parse date: {{date}}',
                    { date: str },
                    locale,
                ),
            );

        year = +d[1];
        month = d[2];
        day = +d[3];
        hours = +(d[4] || 0);
        minutes = +(d[5] || 0);
        seconds = +(d[6] || 0);
        // postgres omits trailing zeros in millis. We have to restore them to get correct value.
        ms = +(d[7]?.padEnd(3, '0') || 0);
        if (d[8] === 'Z' || d[9] === '-' || d[9] === '+') {
            const offset = getOffset(d[9], +(d[10] || 0), +(d[11] || 0));
            const utc = Date.UTC(year, month - 1, day, hours, minutes, seconds, ms);
            return new Datetime(utc - offset * 60 * 1000, timeZone);
        }
        if (timeZone) {
            return Datetime.make(year, month, day, hours, minutes, seconds, ms, timeZone);
        }
        // fast path when no time zone
        const jsDate = new Date(year, month - 1, day, hours, minutes, seconds, ms);
        return new Datetime(jsDate.getTime(), undefined);
    }

    ///
    /// #### Current datetime
    /// * `dtApi.now(withMillis = false)`: returns the current datetime (without milliseconds, unless called with `withMillis` set to true)
    static now(withMillis = false, timeZone?: string) {
        let millis = nowMock ? nowMock.value : new Date().getTime();
        if (!withMillis) millis = Math.floor(millis / 1000) * 1000;
        return new Datetime(millis, timeZone);
    }

    ///
    /// #### Parsing
    /// * `dtApi.parse(str, format)`: parses `str` according to `format`. Returns a datetime.
    ///   `format` defaults to ISO format (`YYYY-MM-DD[T]HH:mm:ss` + optional millis and timezone)
    static parse(str: string, locale: LocalizeLocale = 'base', format?: string, timeZone?: string) {
        if (str == null)
            throw new Error(localizedText('@sage/xtrem-date-time/datetime__isNull', 'datetime is null', {}, locale));

        if (format == null) {
            return this.parseIso(str, locale, timeZone);
        }

        const timeModes = [
            localizedText('@sage/xtrem-date-time/datetime__amDesignator', 'AM', {}, locale),
            localizedText('@sage/xtrem-date-time/datetime__pmDesignator', 'PM', {}, locale),
        ];
        const dtObj = createDateTimeObj(timeModes, [timeModes[0].substring(0, 1), timeModes[1].substring(0, 1)]);
        parseDateTime(str, format, dtObj, locale);
        // apply timezone offset (if one is set provided) and return UTC date
        // if we don't the machine timezone is applied in make function and our offset is ignored
        if (dtObj.tzOffset != null) {
            const offsetHours = Math.trunc(dtObj.tzOffset! / 60);
            const offsetMinutes = dtObj.tzOffset! - offsetHours * 60;
            dtObj.hour = (dtObj.hour || 0) + offsetHours;
            dtObj.minute = (dtObj.minute || 0) + offsetMinutes;
            return makeUtc(dtObj.year, dtObj.month, dtObj.day, dtObj.hour, dtObj.minute, dtObj.second, dtObj.millis);
        }

        const parsed = make(
            dtObj.year,
            dtObj.month,
            dtObj.day,
            dtObj.hour,
            dtObj.minute,
            dtObj.second,
            dtObj.millis,
            dtObj.timeZone || timeZone,
        );
        return timeZone && timeZone !== dtObj.timeZone ? parsed.inTimeZone(timeZone) : parsed;
    }

    ///
    /// #### Constructors
    /// * `dtApi.fromJsDate(jsDate)`: convert a JavaScript `Date` to a datetime value.
    static fromJsDate(jsDate: Date, timeZone?: string) {
        return new Datetime(jsDate.getTime(), timeZone);
    }

    /// * `dtApi.fromValue(millis)`: convert a number of milliseconds since Jan 1st 1970 to a datetime value.
    static fromValue(value: number, timeZone?: string) {
        return new Datetime(value, timeZone);
    }

    private static validateDateValues(year: number, month: number, day: number) {
        if (month < 1 || month > 12) throw new Error(`invalid month value: ${month}`);
        if (day < 1 || day > date.daysInMonth(year, month)) throw new Error(`invalid day value: ${day}`);
    }

    /// * `dtApi.make(year, month, day, hour = 0, minute = 0, second = 0, millis = 0)`: returns a datetime value for the specified components.
    static make(
        year: number,
        month: number,
        day: number,
        hour?: number,
        minute?: number,
        second?: number,
        millis?: number,
        timeZone?: string,
    ): Datetime {
        // Validate if there date overflow
        this.validateDateValues(year, month, day);
        if (timeZone) {
            const dt = new Datetime(
                TimeZone.getUtcValue(timeZone, year, month, day, hour, minute, second, millis),
                timeZone,
            );
            dt._tzValues = Datetime.pack(year, month, day, hour || 0, minute || 0, second || 0);
            return dt;
        }
        // fast path when there is no time zone
        const jsDate = new Date(year, month - 1, day, hour || 0, minute || 0, second || 0, millis || 0);
        return new Datetime(jsDate.getTime(), undefined);
    }

    /// * `dtApi.makeUtc(year, month, day, hour = 0, minute = 0, second = 0, millis = 0)`: returns a datetime value for the specified UTC components.
    static makeUtc(
        year: number,
        month: number,
        day: number,
        hour?: number,
        minute?: number,
        second?: number,
        millis?: number,
    ) {
        // Validate if there date overflow
        this.validateDateValues(year, month, day);
        // makeUtc does not take any timeZone arg (would be confusing)
        // but you can set a time zone with Datetime.makeUtc(...).inTimeZone(timeZone)
        return new Datetime(
            Date.UTC(year, month - 1, day, hour || 0, minute || 0, second || 0, millis || 0),
            undefined,
        );
    }

    /**
     * RegExp to test a string for an ISO 8601 Date spec
     *  YYYY
     *  YYYY-MM
     *  YYYY-MM-DD
     *  YYYY-MM-DD[T| ]HH:mmTZD
     *  YYYY-MM-DD[T| ]HH:mm:ssTZD
     *  YYYY-MM-DD[T| ]HH:mm:ss.sTZD
     * @see: https://www.w3.org/TR/NOTE-datetime
     */
    static isoDatetimeRegexISO8601 =
        /^(?:\d{4}(-\d\d(-\d\d([T|\s]*\d\d:\d\d(:\d\d)?(\.\d+)?(([+-]\d\d:\d\d)|Z)?)?)?)?)?$/;

    /** @internal */
    static isValidDatetimeString(str: string): boolean {
        return this.isoDatetimeRegexISO8601.test(str);
    }

    ///
    /// #### Instanceof
    /// * `dtApi.isDatetime(obj)`: returns true iff `obj` is a datetime value.
    static isDatetime(obj: any): obj is Datetime {
        // instanceOf is broken if multiple copies of the module are loaded
        return obj && obj.constructorName === 'Datetime';
    }
}
// already documented in date module - don't document here
export const sunday = 0;
export const monday = 1;
export const tuesday = 2;
export const wednesday = 3;
export const thursday = 4;
export const friday = 5;
export const saturday = 6;

function getIndexOfZ(format: string) {
    let i = 0;
    const len = format.length;

    function literalOrFormatEnd() {
        for (; i < len; i += 1) {
            if (format[i] === "'") {
                i += 1;
                // check wether the "'" is not doubled
                if (format[i] !== "'") break;
            }
        }
    }

    while (i < len) {
        const c = format[i];
        if (c === "'") {
            i += 1;
            // going to the end of the following literal or to the format end
            literalOrFormatEnd();
        } else if (c === 'Z') {
            return i;
        } else {
            i += 1;
        }
    }
    return undefined;
}

let nowMock: Datetime | null = null;

export function overrideNow(str: string | null) {
    nowMock = str ? Datetime.parse(str) : null;
}

export const now = Datetime.now.bind(Datetime);
export const parse = Datetime.parse.bind(Datetime);
export const fromJsDate = Datetime.fromJsDate.bind(Datetime);
export const fromValue = Datetime.fromValue.bind(Datetime);
export const make = Datetime.make.bind(Datetime);
export const makeUtc = Datetime.makeUtc.bind(Datetime);
export const isDatetime = Datetime.isDatetime.bind(Datetime);
