import { v4 as uuidv4 } from 'uuid';

/* eslint-disable no-bitwise */
const uuidRE = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;

function toHex(byte: number) {
    const b = byte & 0xff;
    return b < 16 ? `0${b.toString(16)}` : b.toString(16);
}

function fromHex(str: string, i: number) {
    const code = str.charCodeAt(i);
    return code < 0x3a ? code - 0x30 : code < 0x47 ? code + 10 - 0x41 : code + 10 - 0x61;
}

export const $exported = true;

export function generate(sep = '-') {
    const newUuid = uuidv4();
    if (sep === '-') return newUuid;
    return newUuid.replace(/-/g, sep);
}

export function fromBytes(bytes: Uint8Array | number[]) {
    let s = '';
    for (let i = 0; i < bytes.length; i += 1) {
        if (i === 4 || i === 6 || i === 8 || i === 10) s += '-';
        const j = i;
        const b = bytes[j];
        s += toHex(b);
    }
    // Force flattening, see generate function above for explanations
    // eslint-disable-next-line @typescript-eslint/no-unused-expressions
    s[0];
    return s;
}
export function toBytes(str: string): number[] {
    if (!str || str.length !== 36 || !uuidRE.test(str)) throw new Error(`invalid uuid: ${str}`);
    const s = str.replace(/-/g, '');
    const bytes: number[] = [];
    for (let i = 0; i < 16; i += 1) {
        const j = i;
        const b = (fromHex(s, 2 * j) << 4) + fromHex(s, 2 * j + 1);
        bytes.push(b >= 128 ? b - 256 : b);
    }
    return bytes;
}
export function fromString32(str: string) {
    return [str.substring(0, 8), str.substring(8, 12), str.substring(12, 16), str.substring(16, 20), str.substring(20)]
        .join('-')
        .toLowerCase();
}
export function toString32(uuid: string) {
    return uuid.replace(/-/g, '');
}
