/** @packageDocumentation @module dateRange */
import { LocalizeLocale } from '@sage/xtrem-shared';
import { DateValue, WeekDay } from './date';
import { Range } from './range';
import { isValidDatePropertyValue } from './utils';

export class DateRange extends Range<DateValue> {
    static parse = parse;

    static isDateRange = isDateRange;

    static make = make;

    static getDayRange = getDayRange;

    static getMonthRange = getMonthRange;

    static getYearRange = getYearRange;

    static getDateRange = getDateRange;

    // eslint-disable-next-line class-methods-use-this
    get constructorName() {
        return 'DateRange';
    }

    get durationInDays(): number {
        if (this.excludedEnd instanceof DateValue && this.includedStart instanceof DateValue) {
            return this.excludedEnd.daysDiff(this.includedStart as DateValue);
        }
        return Infinity;
    }

    /** @internal */
    // eslint-disable-next-line class-methods-use-this
    incrementsByRangeUnit(elm: DateValue, step: number = 1): DateValue {
        return elm.addDays(step);
    }

    /** @internal */
    // eslint-disable-next-line class-methods-use-this
    getBoundValue(elm: DateValue | null): number | null {
        if (!elm) return null;
        return elm?.value;
    }

    /** @internal */
    // eslint-disable-next-line class-methods-use-this
    getStringBoundValue(elm: DateValue | null): string {
        if (!elm) return '';
        return elm.toString();
    }
}

export function isDateRange(obj: any): obj is DateRange {
    return obj && obj.constructorName === 'DateRange';
}

export function parse(str: string): DateRange {
    const [start, end] = str.split(',');
    if (!end) throw new Error(`invalid date range format: ${str}.`);

    const excludesStart = start.length === 0 || start.startsWith('(');
    const excludesEnd = end.length === 0 || end.endsWith(')');

    if (!start && !end) return new DateRange(null, null);

    const parseHalf = (s: string): DateValue | null => {
        if (!s) {
            return null;
        }
        const clean = s.replace(
            /"?(\d{1,4})(?:-(\d{1,2})(?:-(\d{1,2})))"?/,
            (_all: string, year: string, month = '01', day = '01') =>
                `${year.padStart(4, '0')}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`,
        );
        return DateValue.parse(clean);
    };

    return new DateRange(
        parseHalf(start && start.substring(1)),
        parseHalf(end && end.substring(0, end.length - 1)),
        excludesStart,
        excludesEnd,
    );
}

export function make(start: DateValue | null, end: DateValue | null, excludesStart = false, excludesEnd = true) {
    return new DateRange(start, end, excludesStart, excludesEnd);
}

export function getDayRange(d: DateValue): DateRange {
    return new DateRange(d, d, false, false);
}

export function getMonthRange(d: DateValue): DateRange {
    return new DateRange(d.begOfMonth(), d.endOfMonth(), false, false);
}

export function getYearRange(d: DateValue): DateRange {
    return new DateRange(d.begOfYear(), d.endOfYear(), false, false);
}

export function getPreviousWeekRange(date: DateValue, startOfWeek: WeekDay): DateRange {
    const rangeStart = date.begOfWeek(startOfWeek).addWeeks(-1);
    const rangeEnd = date.begOfWeek(startOfWeek).addDays(-1);
    return new DateRange(rangeStart, rangeEnd, false, false);
}

export function getSameWeekRange(date: DateValue, startOfWeek: WeekDay): DateRange {
    const rangeStart = date.begOfWeek(startOfWeek);
    const rangeEnd = date.endOfWeek(startOfWeek);
    return new DateRange(rangeStart, rangeEnd, false, false);
}

export function getNextWeekRange(date: DateValue, startOfWeek: WeekDay): DateRange {
    const rangeStart = date.endOfWeek(startOfWeek).addDays(1);
    const rangeEnd = date.endOfWeek(startOfWeek).addWeeks(1);
    return new DateRange(rangeStart, rangeEnd, false, false);
}

type AggFunc = 'year' | 'month' | 'day' | 'week';

export type DateRangeType = `${'same' | 'previous' | 'next'}-${AggFunc}` | `last-${7 | 30}-days`;

export function getDateRange({
    date: d,
    range,
    locale,
}: {
    date: string;
    range: DateRangeType;
    locale?: LocalizeLocale;
}): DateRange {
    if (!isValidDatePropertyValue(d)) {
        throw new Error(`Invalid date ${d}`);
    }
    const dateValue = DateValue.parse(d);

    let startOfWeek: WeekDay = WeekDay.monday;
    if (locale === 'en-US') {
        startOfWeek = WeekDay.sunday;
    }
    switch (range) {
        case 'same-day':
            return getDayRange(dateValue);
        case 'previous-day':
            return getDayRange(dateValue.addDays(-1));
        case 'same-month':
            return getMonthRange(dateValue);
        case 'previous-month':
            return getMonthRange(dateValue.addMonths(-1));
        case 'same-year':
            return getYearRange(dateValue);
        case 'previous-year':
            return getYearRange(dateValue.addYears(-1));
        case 'last-7-days':
            return new DateRange(dateValue.addDays(-7), dateValue);
        case 'last-30-days':
            return new DateRange(dateValue.addDays(-30), dateValue);
        case 'previous-week':
            return getPreviousWeekRange(dateValue, startOfWeek);
        case 'same-week':
            return getSameWeekRange(dateValue, startOfWeek);
        case 'next-day':
            return getDayRange(dateValue.addDays(1));
        case 'next-week':
            return getNextWeekRange(dateValue, startOfWeek);
        case 'next-month':
            return getMonthRange(dateValue.addMonths(1));
        case 'next-year':
            return getYearRange(dateValue.addYears(1));
        default:
            throw new Error(
                "Invalid parameters supplied to \"getRange\": \"range\" must be one of the following: 'same-day', 'previous-day', 'same-month', 'previous-month', 'same-year', 'previous-year', 'last-7-days', 'last-30-days', 'previous-week', 'same-week', 'next-day', 'next-week', 'next-month', 'next-year'.",
            );
    }
}
