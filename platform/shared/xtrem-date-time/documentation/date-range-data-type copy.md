PATH: XTREEM/Development+documentation/date+range+data+types

### Overview:

Inclusion's signs

-   [ includes the start date
-   ( excludes the start date
-   ] includes the end date
-   ) excludes the end date

Infinity bounds

-   infinity lower bound: keep the start date blank.
-   infinity upper bound: keep the end date blank.

Examples

-   [2018-01-01, 2019-02-04]: includes start date and end date
-   (2018-01-01, 2019-02-04]: excludes start date and includes end date
-   [2018-01-01, 2019-02-04): includes start date and excludes end date
-   [, 2019-02-04]: -infinity lower bound
-   [2018-01-01, ]: infinity upper bound

### Methods:

**toString(format?)**: return a date range with this format : ([|()YYYY-MM-DD,YYYY-MM-DD]|)). Format is optional.
**equals(DateRange)**: return true or false
**durationInDays**: return the duration in days of the date range, if one the bound is infinity it will return infinity
**includes(DateValue)**: return true if the date is contained in the date range

### Operators:

Two filter operators introduced:
**\_contains**: contains range or contains date
**\_containedBy**: range is contained by another range

### GraphQL filter API:

-   `{ _eq: "[|(YYYY-MM-DD,YYY-MM-DD]|)" }`: equal to another range
-   `{ _ne: "[|(YYYY-MM-DD,YYY-MM-DD]|)" }`: not equal to another range
-   `{ _contains: "YYYY-MM-DD" }`: contains a date
-   `{ _contains: "[|(YYYY-MM-DD,YYY-MM-DD]|)" }`: contains another range
-   `{ _containedBy: "[|(YYYY-MM-DD,YYY-MM-DD]|)" }`: contained by another range
-   `{ start: { _eq|_ne|_lt|_lte|_gt|_gte: "YYYY-MM-DD" } }`: test lower bound (as a date)
-   `{ end: { _eq|_ne|_lt|_lte|_gt|_gte: "YYYY-MM-DD" } }`: test upper bound (as a date)

Note: start and end tests can be combined.

### Typescript filter API:

-   any of the above GraphQL filters
-   `{ _eq: aRange }`: equal to another range
-   `{ _ne: aRange }`: not equal to another range
-   `{ _contains: aDateValue }`: contains a date
-   `{ _contains: aRange }`: contains another range
-   `{ _containedBy: aRange }`: contained by another range
-   `{ start: { _eq|_ne|_lt|_lte|_gt|_gte: aDate } }`: test lower bound (as a date)
-   `{ end: { _eq|_ne|_lt|_lte|_gt|_gte: aDate } }`: test upper bound (as a date)
