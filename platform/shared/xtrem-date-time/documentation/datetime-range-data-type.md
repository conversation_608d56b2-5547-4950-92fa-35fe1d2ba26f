PATH: XTREEM/Development+documentation/datetime+range+data+types

### Overview:

Inclusion's signs

-   [ includes the start datetime
-   ( excludes the start datetime
-   ] includes the end datetime
-   ) excludes the end datetime

Infinity bounds

-   infinity lower bound: keep the start datetime blank.
-   infinity upper bound: keep the end datetime blank.

Examples

-   [2018-01-01 12:00, 2019-02-04 17:00]: includes start datetime and end datetime
-   (2018-01-01 08:30:00, 2019-02-04 20:00:00]: excludes start datetime and includes end datetime
-   [2018-01-01, 2019-02-04 13:00): includes start datetime and excludes end datetime
-   [, 2019-02-04]: -infinity lower bound
-   [2018-01-01, ]: infinity upper bound

### Methods:

**toString()**: return a datetime range with ISO 8601 format : YYYY-MM-DD[T]HH:mm:ss.SSSZ.
**equals(DatetimeRange)**: return true or false
**durationInMilliseconds**: return the duration in milliseconds of the datetime range, if one the bound is infinity it will return infinity
**includes(DateValue)**: return true if the datetime is contained in the datetime range

### Operators:

Two filter operators introduced:
**\_contains**: contains range or contains datetime
**\_containedBy**: range is contained by another range

### GraphQL filter API:

-   `{ _eq: "[|(DatetimeString,DatetimeString]|)" }`: equal to another range
-   `{ _ne: "[|(DatetimeString,DatetimeString]|)" }`: not equal to another range
-   `{ _contains: "DatetimeString" }`: contains a datetime
-   `{ _contains: "[|(DatetimeString,DatetimeString]|)" }`: contains another range
-   `{ _containedBy: "[|(DatetimeString,DatetimeString]|)" }`: contained by another range
-   `{ start: { _eq|_ne|_lt|_lte|_gt|_gte: "DatetimeString" } }`: test lower bound (as a datetime)
-   `{ end: { _eq|_ne|_lt|_lte|_gt|_gte: "DatetimeString" } }`: test upper bound (as a datetime)

Valid DatetimeString are:

-   YYYY-MM-DD
-   YYYY-MM-DD HH-mm or YYYY-MM-DD HH-mmZ or YYYY-MM-DD[T]HH-mm or YYYY-MM-DD[T]HH-mmZ
-   YYYY-MM-DD HH-mm-ss or YYYY-MM-DD[T]HH-mm-ss or YYYY-MM-DD[T]HH-mm-ssZ
-   YYYY-MM-DD HH-mm-ss.mmm or YYYY-MM-DD[T]HH-mm-ss.mmm
-   YYYY-MM-DD HH-mm-ss.mmmZ or YYYY-MM-DD[T]HH-mm-ss.mmmZ

Note: start and end tests can be combined.

### Typescript filter API:

-   any of the above GraphQL filters
-   `{ _eq: aRange }`: equal to another range
-   `{ _ne: aRange }`: not equal to another range
-   `{ _contains: aDatetime }`: contains a datetime
-   `{ _contains: aRange }`: contains another range
-   `{ _containedBy: aRange }`: contained by another range
-   `{ start: { _eq|_ne|_lt|_lte|_gt|_gte: aDatetime } }`: test lower bound (as a datetime)
-   `{ end: { _eq|_ne|_lt|_lte|_gt|_gte: aDatetime } }`: test upper bound (as a datetime)
