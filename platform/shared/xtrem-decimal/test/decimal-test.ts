import { assert } from 'chai';
import {
    abs,
    add,
    ceil,
    compare,
    Decimal,
    div,
    eq,
    floor,
    gt,
    gte,
    lt,
    lte,
    max,
    min,
    mul,
    ne,
    negated,
    plus,
    pow,
    round,
    strictEq,
    strictNe,
    sub,
    sum,
    toDecimal,
    toInteger,
    toNumber,
} from '../lib';

const assertEqual = assert.equal;
assert.equal = (a: any, b: any, message?: string) => {
    assertEqual.call(assert, String(a), String(b), message);
};

const assertStrictEqual = assert.strictEqual;
assert.strictEqual = (a: any, b: any, message?: string) => {
    assertStrictEqual.call(assert, String(a), String(b), message);
};

describe('Instanciation', () => {
    it('Instanciation', () => {
        assert.strictEqual(Decimal.make('123.456'), <any>123.456);
    });
});

describe('tests on compound assignement operators', () => {
    it('tests on compound assignement operators', () => {
        // modification tests using the functions of the Decimal Class
        // test with function eq forcing an error to see if the value is equal or not
        const val1 = new Decimal(0.8);
        assert.isTrue(eq(val1, 0.8), 'val1 should be equal to 0.8');

        // test with function add, forcing an error to see if the value is equal to the addition or not
        const val2 = Decimal.add(val1, 1);
        assert.isTrue(eq(val2, 1.8), 'val2 should be equal to 1.8');

        // test with function div, forcing an error to see if the value is equal to the division or not
        const val3 = Decimal.div(val2, 2);
        assert.isTrue(eq(val3, 0.9), 'val3 should be equal to 0.9');

        //  test with function mul, forcing an error to see if the value is equal to the mutiplication or not
        const val4 = Decimal.mul(val3, 2);
        assert.isTrue(eq(val4, 1.8), 'val4 should be equal to 1.8');

        let d = 1;
        d += 2;
        assert.ok(!Decimal.isDecimal(d));
        assert.strictEqual(d, 3);
        d -= 3;
        assert.ok(!Decimal.isDecimal(d));
        assert.strictEqual(d, 0);
    });
});
// Tests for the methods of the Decimal class
describe('tests methods of _Decimal', () => {
    // tests the method Decimal.make, that converts variables to a decimal
    // forcing an error to see if the variable was converted to a Decimal
    it('tests the method Decimal.make', () => {
        const val = new Decimal(6);
        const num = 7;
        const num2 = Decimal.make(num);
        assert.isTrue(Decimal.isDecimal(val));
        assert.isTrue(Decimal.isDecimal(num2));
    });
    // tests the method Decimal.isDecimal, that verifies if a variable is a decimal
    // forcing an error to see if the checked variable is a Decimal
    it('tests the method Decimal.isDecimal', () => {
        const val = new Decimal(6);
        assert.isTrue(Decimal.isDecimal(val), 'val should be a Decimal');
    });
    // tests the method Decimal.toNumber, that converts a Decimal variable to a Number
    // forcing an error to verify that the variable was converted into a Decimal
    it('tests the method Decimal.toNumber', () => {
        const val = new Decimal(6);
        const neoValNew = Decimal.toNumber(val);
        assert.isTrue(typeof neoValNew === 'number', 'neoValNew should be a number');
    });
});
// tests the fonction decimal, that converts a string variable to a Decimal variable
// forcing an error to verify that the variable was converted into a Decimal
describe('tests string to decimal fn', () => {
    it('tests string to decimal fn', () => {
        const val = '76';
        const valNew = Decimal.make(val);
        assert.isTrue(Decimal.isDecimal(valNew), 'valNew should be a Decimal');
    });
});

// tests the strictEq fn tests if two values is are exactly the same
// forcing an error to verify that the variables are the same
describe('tests the strictEq fn', () => {
    it('verifies that val1 and val2 are same value and type', () => {
        const val1 = new Decimal(75);
        const val2 = new Decimal(75);
        assert.isTrue(strictEq(val1, val2), 'val1 and val2 should be the same (type and value)');
    });

    it('verifies that val1 and val2 are different value and type', () => {
        const val1 = '72';
        const val2 = new Decimal(72);
        assert.isFalse(strictEq(val1, val2), 'val1 and val2 should be different (type and value)');
    });
});

// tests the ne fn tests that two values is are different
// forcing an error to verify that the variables are different
describe('tests the ne fn', () => {
    it('verifies that val1 and val2 are same value and type', () => {
        const val1 = new Decimal(75);
        const val2 = new Decimal(75);
        assert.isFalse(ne(val1, val2), 'val1 and val2 should be the same (value)');
    });

    it('verifies that val1 and val2 are different value and type', () => {
        const val1 = new Decimal(72);
        const val2 = new Decimal(75);
        assert.isTrue(ne(val1, val2), 'val1 and val2 should be different (value)');
    });
});

// tests the strictNe fn tests that two values is are different in value and type
// forcing an error to verify that the variables are different in value and type
describe('tests the strictNe fn', () => {
    it('verifies that val1 and val2 are same value and type', () => {
        const val1 = new Decimal(75);
        const val2 = '75';
        assert.isTrue(strictNe(val1, val2), 'val1 and val2 should be different (value and type)');
    });

    it('verifies that val1 and val2 have either the same value or type', () => {
        const val1 = new Decimal(75);
        const val2 = new Decimal(75);
        assert.isFalse(strictNe(val1, val2), 'val1 and val2 should have the same (value or type)');
    });
});

// tests the lt fn tests if a value is less than another value
// forcing an error to verify that the value is Less than the other value
describe('tests the lt fn', () => {
    it('verifies that val1 is less than val2', () => {
        const val1 = 28;
        const val2 = 78;
        assert.isTrue(lt(val1, val2), 'val1 should be less than val2');
    });

    it('verifies that val1 is greater than val2', () => {
        const val1 = new Decimal(75);
        const val2 = new Decimal(73);
        assert.isFalse(lt(val1, val2), 'val1 should be greater than val2');
    });
});

// tests the lte fn tests if a value is less than or equal to another value
// forcing an error to verify that the value is Less than or equal to the other value
describe('tests the lte fn', () => {
    it('verifies that val1 is less than val2', () => {
        const val1 = new Decimal(28);
        const val2 = new Decimal(78);
        assert.isTrue(lte(val1, val2), 'val1 should be less than val2');
    });

    it('verifies that val1 is greater than val2', () => {
        const val1 = new Decimal(75);
        const val2 = new Decimal(73);
        assert.isFalse(lte(val1, val2), 'val1 should be greater than val2');
    });

    it('verifies that val1 is equal to val2', () => {
        const val1 = new Decimal(75);
        const val2 = new Decimal(75);
        assert.isTrue(lte(val1, val2), 'val1 should be equal to val2');
    });
});
// tests the gt fn tests if a value is greater than  another value
// forcing an error to verify that the value is greater than the other value
describe('tests the gt fn', () => {
    it('verifies that val1 is less than val2', () => {
        const val1 = new Decimal(28);
        const val2 = new Decimal(78);
        assert.isFalse(gt(val1, val2), 'val1 should be less than val2');
    });

    it('verifies that val1 is greater than val2', () => {
        const val1 = new Decimal(75);
        const val2 = new Decimal(73);
        assert.isTrue(gt(val1, val2), 'val1 should be greater than val2');
    });
});

// tests the gt fn tests if a value is greater than or equal to another value
// forcing an error to verify that the value is greater than or equal to the other value
describe('tests the gte fn', () => {
    it('verifies that val1 is less than val2', () => {
        const val1 = new Decimal(28);
        const val2 = new Decimal(78);
        assert.isFalse(gte(val1, val2), 'val1 should be less than val2');
    });

    it('verifies that val1 is greater than val2', () => {
        const val1 = new Decimal(75);
        const val2 = new Decimal(73);
        assert.isTrue(gte(val1, val2), 'val1 should be greater than val2');
    });

    it('verifies that val1 is equal than val2', () => {
        const val1 = new Decimal(73);
        const val2 = new Decimal(73);
        assert.isTrue(gte(val1, val2), 'val1 should be equal to val2');
    });
});

// tests the add fn tests if the result is the addition of the 2 values
// forcing an error to verify that the response value is the addition of the 2 values
describe('tests the add fn', () => {
    it('verifies that val1 + val2 = 106', () => {
        const val1 = 28;
        const val2 = 78;
        assert.strictEqual(add(val1, val2), 106, 'val1 + val2 should be equal to 106');
    });

    it('verifies that val1 + val2 = 148', () => {
        const val1 = new Decimal(75);
        const val2 = new Decimal(73);
        assert.strictEqual(add(val1, val2), 148, 'val1 + val2 should be equal to 148');
    });
});

// tests the sub fn tests if the result is the difference of the 2 values
// forcing an error to verify that the response value is the difference of the 2 values
describe('tests the sub fn', () => {
    it('verifies that val1 - val2 = -50', () => {
        const val1 = 28;
        const val2 = 78;
        assert.strictEqual(sub(val1, val2), -50, 'val1 - val2 should be equal to -50');
    });

    it('verifies that val1 - val2 = 2', () => {
        const val1 = new Decimal(75);
        const val2 = new Decimal(73);
        assert.strictEqual(sub(val1, val2), 2, 'val1 - val2 should be equal to 2');
    });
});

// tests the mul fn tests if the result is the product of the 2 values
// forcing an error to verify that the response value is the product of the 2 values
describe('tests the mul fn', () => {
    it('verifies that val1 * val2 = 2213.104221', () => {
        const val1 = 28.089;
        const val2 = 78.789;
        const val3 = 2;
        const val4 = 5;
        assert.strictEqual(mul(val1, val2), 2213.104221, 'val1 * val2 should be equal to 2213.104221');
        assert.strictEqual(mul(val3, val4), 10, 'val3 * val4 should be equal to 10');
    });

    it('verifies that val1 * val2 = 5598.05444', () => {
        const val1 = new Decimal(75.98);
        const val2 = new Decimal(73.678);
        assert.strictEqual(mul(val1, val2), 5598.05444, 'val1 * val2 should be equal to 5598.05444');
    });
});

// tests the div fn tests if the result is the division of the 2 values
// forcing an error to verify that the response value is the division of the 2 values
describe('tests the div fn', () => {
    it('verifies that val1 / val2 = 0.358974358974359', () => {
        const val1 = 28;
        const val2 = 78;
        const val3 = new Decimal(8);
        const val4 = new Decimal(4);
        assert.strictEqual(div(val1, val2), 0.358974358974359, 'val1 / val2 should be equal to 2213.104221');
        assert.strictEqual(div(val3, val4), new Decimal(2), 'val3 / val4 should be equal to 2');
    });
});

// tests the compare fn tests if 2 values are the same type and if a value is greater of less than the other
// forcing an error to verify that the 2 values are the same type and if a value is greater of less than the other
describe('tests the compare fn', () => {
    it('verifies that val1 < val2 ', () => {
        const val1 = 28.089;
        const val2 = 78.789;
        const val3 = new Decimal(20);
        const val4 = new Decimal(24);
        assert.strictEqual(compare(val1, val2), -1, 'val1  should be < to val2 ');
        assert.strictEqual(compare(val3, val4), -1, 'val3  should be < to val4 ');
    });

    // review later, currenty comparison to null is not behaving like in SQL
    // [null, undefined].forEach(v => {
    [undefined].forEach(v => {
        it(`compare ${v}`, () => {
            // null and undefined should be only equal to themselve but any other comparison should be false
            assert.strictEqual(compare(v, v), 0, `${v} should be equal to ${v}`);
            assert.strictEqual(
                compare(v, v === null ? undefined : null),
                undefined,
                `${v} should not be equal to ${v === null ? undefined : null}`,
            );
            const comparedToValues = [1, -1, new Decimal(1), '1', '-1', 'abc'];
            if (v === null) {
                // null is lower than anything
                comparedToValues.forEach(v2 => {
                    assert.strictEqual(compare(v, v2), -1, `null should be < ${v2}`);
                    assert.strictEqual(compare(v2, v), 1, `${v2} should be > null`);
                });
            } else {
                // undefined is not comparable to anything (except itself, see above)
                comparedToValues.forEach(v2 => {
                    assert.strictEqual(compare(v, v2), undefined, `${v} should not be < ${v2}`);
                    assert.strictEqual(compare(v2, v), undefined, `${v} should not be > ${v2}`);
                });
            }
        });
    });

    it('verifies object with null prototype is not comparable', () => {
        const plainObj = Object.create(null);

        [
            null,
            undefined,
            0,
            73.678,
            -73.678,
            new Decimal(0),
            new Decimal(75),
            new Decimal(-67),
            [],
            [1, 2, 3],
            [{}],
            [Object.create(null)],
        ].forEach(v => {
            assert.strictEqual(
                compare(plainObj, v),
                undefined,
                `object with null prototype should not be comparable to ${JSON.stringify(v)}`,
            );
            assert.strictEqual(
                compare(v, plainObj),
                undefined,
                `${JSON.stringify(v)} should not be comparable to object with null prototype`,
            );
        });
    });

    it('verifies array are not comparable', () => {
        const val = 1;

        [[], [1, 2, 3], [{}], [Object.create(null)]].forEach(v => {
            assert.strictEqual(compare(val, v), undefined, `${val} should not be comparable to ${JSON.stringify(v)}`);
            assert.strictEqual(compare(v, val), undefined, `${JSON.stringify(v)} should not be comparable to ${val}`);
        });
    });

    it('verifies that val1 > val2 ', () => {
        const val1 = 75.98;
        const val2 = 73.678;
        const val3 = new Decimal(75);
        const val4 = new Decimal(67);

        assert.strictEqual(compare(val1, val2), 1, 'val1  should be > to val2 ');
        assert.strictEqual(compare(val3, val4), 1, 'val3  should be > to val4');
    });

    it('verifies that val1 = val2 ', () => {
        const val1 = new Decimal(75.98);
        const val2 = new Decimal(75.98);
        assert.strictEqual(compare(val1, val2), 0, 'val1  should be = to val2');
    });
});

describe('tests the sum fn', () => {
    it('verifies that 1,2,3,4,5 = 15', () => {
        assert.strictEqual(sum(1, 2, 3, 4, 5), 15);
        assert.strictEqual(sum(new Decimal(1), 2, '3', new Decimal(4), new Decimal(5)), new Decimal(15));
    });
});

// tests the negated fn, returns a negated value from the original value
// forcing an error to verify that the response value is the same value but negated
describe('tests the negated fn', () => {
    it('verifies that negated val1 is -28', () => {
        const val1 = 28;
        assert.strictEqual(negated(val1), -28, 'negated val1 is -28');
    });
});

// tests the plus fn, returns a decimal value from a string value
// forcing an error to verify that the response value is a decimal value
describe('tests the plus fn', () => {
    it('verifies that val1 is 28', () => {
        const val1 = '28';
        assert.strictEqual(plus(val1), 28, 'val1 should be 28');
    });
});

// tests the abs fn, returns the absolute value of a value
// forcing an error to verify that the response value is the absolute value
describe('tests the abs fn', () => {
    it('verifies that val1 is 28', () => {
        const val1 = -28;
        const val2 = new Decimal(-40);
        assert.strictEqual(abs(val1), 28, 'val1 should be 28');
        assert.strictEqual(abs(val2), 40, 'val2 should be 40');
    });
});

// tests the ceil fn, returns the ceil of the given value
// forcing an error to verify the ceil of the given value
describe('tests the ceil fn', () => {
    it('verifies that the ceil to val1 is 25', () => {
        const val1 = 24.65;
        const val2 = new Decimal(24.65);
        const val3 = 23.004;
        const val4 = new Decimal(22.08);
        assert.strictEqual(ceil(val1), 25, 'ceil should be 25');
        assert.strictEqual(ceil(val2), 25, 'ceil should be 25');
        assert.strictEqual(ceil(val3), 24, 'ceil for val1 should be 24');
        assert.strictEqual(ceil(val4), 23, 'ceil for val2 should be 24');
    });
});

// tests the floor fn, returns the floor of the given value
// forcing an error to verify the floor of the given value
describe('tests the floor fn', () => {
    it('verifies that the floor to val1 is 24', () => {
        const val1 = 24.65;
        const val2 = new Decimal(27.65);
        const val3 = 23.004;
        const val4 = new Decimal(22.08);

        assert.strictEqual(floor(val1), 24, 'floor for val1 should be 24');
        assert.strictEqual(floor(val2), 27, 'floor for val2 should be 27');
        assert.strictEqual(floor(val3), 23, 'floor for val3 should be 23');
        assert.strictEqual(floor(val4), 22, 'floor for val4 should be 22');
    });
});

// tests the pow fn, returns the value elevated to the given power
// forcing an error to verify that the value is elevated to the given power
describe('tests the pow fn', () => {
    it('verifies that 2^3=8', () => {
        const val1 = 2;
        const val2 = new Decimal(3);
        const expo = 3;
        assert.strictEqual(pow(val1, expo), 8, '2^3=8');
        assert.strictEqual(pow(val2, expo), 27, '3^3=27');
    });
});

// tests the max fn, returns the greatest value from a series of values
// forcing an error to verify that the returned value is the greatest value
describe('tests the max fn', () => {
    it('verifies that 98 is the greatest number', () => {
        assert.strictEqual(max(0, 6, 98, 1, 7), 98, 'The greatest value is 98');
        assert.strictEqual(
            max(new Decimal(0), new Decimal(6), new Decimal(98), new Decimal(1), new Decimal(7)),
            new Decimal(98),
            'The greatest value is new Decimal(98)',
        );
    });
});

// tests the min fn, returns the minimum value from a series of values
// forcing an error to verify that the returned value is the minimum value
describe('tests the min fn', () => {
    it('verifies that 0 is the minimum number', () => {
        assert.strictEqual(min(0, 6, 98, 1, 7), 0, 'The minimum value is 0');
        assert.strictEqual(
            min(new Decimal(0), new Decimal(6), new Decimal(98), new Decimal(1), new Decimal(7)),
            0,
            'The minimum value is new Decimal(0)',
        );
    });
});

// tests the round fn, returns the closest integer value
// forcing an error to verify that the returned value is the closest integer value
describe('tests the round fn', () => {
    it('verifies that the returned value is the closest integer value', () => {
        const val1 = 6.75;
        const val2 = 0.33;
        const val3 = 5.4;
        const val4 = new Decimal(10.4);
        assert.strictEqual(round(val1), 7, 'the closest integer value is 7');
        assert.strictEqual(round(val2), 0, 'the closest integer value is 0');
        assert.strictEqual(round(val3), 5, 'the closest integer value is 5');
        assert.strictEqual(round(val4), 10, 'the closest integer value is 5');
    });
});

describe('tests on unary expressions', () => {
    it('tests on unary expressions', () => {
        const val2 = '1';
        assert.strictEqual(+val2, 1);
        const val3 = '1';
        assert.strictEqual(1, +val3);

        const val4 = 1;
        assert.strictEqual(+val4, 1);
        const val5 = 1;
        assert.strictEqual(1, +val5);
    });
});

describe('Objects', () => {
    it('tests with Objects', () => {
        const val = { value: 0.1 };
        assert.strictEqual(val.value, 0.1);
    });
});

describe('Blocks', () => {
    it('Block if 1', () => {
        let i;
        let first = true;
        for (i = 0; i < 2; i += 1, first = false) {
            if (first) {
                assert.strictEqual(i, 0);
            } else {
                assert.strictEqual(i, 1);
            }
        }
        assert.strictEqual(i, 2);
    });

    it('Block if 2', () => {
        for (let i = 0, first = true; i < 2; i += 1, first = false) {
            if (first) {
                assert.strictEqual(i, 0);
            } else {
                assert.strictEqual(i, 1);
            }
        }
    });

    it('Block if 3', () => {
        for (let first = true, i = 0; i < 2; first = false) {
            if (first) {
                assert.strictEqual(i, 0);
            } else {
                assert.strictEqual(i, 1);
            }
            i += 1;
        }
    });
});

describe('Spreads', () => {
    it('spread', () => {
        const obj1 = {
            key1: 1,
        };
        const obj2 = {
            ...obj1,
        };
        assert.strictEqual(obj2.key1, 1);
    });
});

describe('Conversion utilities', () => {
    describe('toDecimal', () => {
        it('can convert Decimal, string and number to Decimal', () => {
            const val = new Decimal('1.2');
            assert.equal(toDecimal(val), val);
            assert.isTrue(toDecimal('1.2').equals(val));
            assert.equal(toDecimal(1.2), val);
            assert.equal(toDecimal('Infinity').toString(), 'Infinity');
            assert.equal(toDecimal('-Infinity').toString(), '-Infinity');
        });
        it('throws on invalid input', () => {
            assert.throws(() => toDecimal(''), '[DecimalError] Invalid argument: ');
            assert.throws(() => toDecimal('abc'), '[DecimalError] Invalid argument: abc');
            assert.throws(() => toDecimal(NaN), 'Invalid numeric value: NaN');
            assert.throws(() => toDecimal('NaN'), "Invalid numeric value: 'NaN'");
            assert.throws(() => toDecimal({} as any), 'Invalid numeric value: [object Object]');
            assert.throws(() => toDecimal(null as any), 'Invalid numeric value: null');
        });
    });

    describe('toNumber', () => {
        it('can convert Decimal, string and number to number', () => {
            assert.equal(toNumber(new Decimal('1.2')), 1.2);
            assert.equal(toNumber('1.2'), 1.2);
            assert.equal(toNumber(1.2), 1.2);
            assert.equal(toNumber(Infinity), Infinity);
            assert.equal(toNumber(-Infinity), -Infinity);
        });
        it('throws on invalid input', () => {
            assert.throws(() => toNumber(''), "Invalid numeric value: ''");
            assert.throws(() => toNumber('abc'), "Invalid numeric value: 'abc'");
            assert.throws(() => toNumber(NaN), 'Invalid numeric value: NaN');
            assert.throws(() => toNumber('NaN'), "Invalid numeric value: 'NaN'");
            assert.throws(() => toNumber({} as any), 'Invalid numeric value: [object Object]');
            assert.throws(() => toNumber(null as any), 'Invalid numeric value: null');
        });
    });

    describe('toInteger', () => {
        it('can convert Decimal, string and number to number', () => {
            assert.equal(toInteger(new Decimal('3')), 3);
            assert.equal(toInteger('3'), 3);
            assert.equal(toInteger(3), 3);
            assert.equal(toInteger(Infinity), Infinity);
            assert.equal(toInteger(-Infinity), -Infinity);
        });
        it('throws on invalid input', () => {
            assert.throws(() => toInteger(1.2), 'Invalid integer value: 1.2');
            assert.throws(() => toInteger('1.2'), 'Invalid integer value: 1.2');
            assert.throws(() => toInteger(new Decimal('1.2')), 'Invalid integer value: 1.2');
            assert.throws(() => toInteger(''), "Invalid numeric value: ''");
            assert.throws(() => toInteger('abc'), "Invalid numeric value: 'abc");
            assert.throws(() => toInteger(NaN), 'Invalid numeric value: NaN');
            assert.throws(() => toInteger('NaN'), "Invalid numeric value: 'NaN'");
            assert.throws(() => toInteger({} as any), 'Invalid numeric value: [object Object]');
            assert.throws(() => toInteger(null as any), 'Invalid numeric value: null');
        });
    });
});
