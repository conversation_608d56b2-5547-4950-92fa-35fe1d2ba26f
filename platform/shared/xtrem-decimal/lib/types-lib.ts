/* eslint-disable no-console */
/**
Use http://mikemcl.github.io/decimal.js/
https://github.com/MikeMcl/decimal.js
*/

import { Decimal } from './decimal-class';

const valueIsANumber = (value: Value): value is number => typeof value === 'number';
const valueIsNotANumber = (value: Value): value is Exclude<number, Value> => typeof value !== 'number';

export type Value = string | number | Decimal;

// eslint-disable-next-line @typescript-eslint/naming-convention
export type integer = number;

const quote = (str: string) => `'${str}'`;

function ensureValidDecimal(value: Decimal, originalValue?: string): Decimal {
    if (value.isNaN())
        throw new Error(`Invalid numeric value: ${originalValue != null ? quote(originalValue) : value}`);
    return value;
}

function ensureValidNumber(value: number, originalValue?: string): number {
    if (Number.isNaN(value))
        throw new Error(`Invalid numeric value: ${originalValue != null ? quote(originalValue) : value}`);
    return value;
}

/**
 * Converts a value to a decimal (an instance of Decimal).
 *
 * The value may be a string, a number or a Decimal.
 * Only finite values are allowed. Special values like NaN and Infinity will throw an error.
 */
export function toDecimal(value: Value): Decimal {
    if (value instanceof Decimal) return value;
    if (typeof value === 'string') return ensureValidDecimal(new Decimal(value), value);
    if (typeof value === 'number') return ensureValidDecimal(new Decimal(value));
    throw new Error(`Invalid numeric value: ${value}`);
}

/**
 * Converts a value to a number.
 *
 * The value may be a string, a number or a decimal (an instance of Decimal).
 * Only finite values are allowed. Special values like NaN and Infinity will throw an error.
 */
export function toNumber(value: Value): number {
    if (Decimal.isDecimal(value)) return value.toNumber();
    if (typeof value === 'number') return ensureValidNumber(value);
    if (typeof value === 'string') return ensureValidNumber(parseFloat(value), value);
    throw new Error(`Invalid numeric value: ${value}`);
}

/**
 * Converts a value to an integer.
 *
 * The value may be a string, a number or a decimal (an instance of Decimal).
 * Only integer values are allowed. Non-integer values like 1.5, NaN and Infinity will throw an error.
 */
export function toInteger(value: Value): integer {
    const result = toNumber(value);
    if (result !== Math.floor(result)) throw new Error(`Invalid integer value: ${value}`);
    return result;
}

export function newDecimal(s: string) {
    return new Decimal(s);
}

export function add(a: any, b: any) {
    // Allow string promotion for concatenation
    return (Number.isInteger(a) && Number.isInteger(b)) || typeof a === 'string' || typeof b === 'string'
        ? a + b
        : Decimal.add(a, b);
}

export function sub(a: any, b: any) {
    return Number.isInteger(a) && Number.isInteger(b) ? a - b : Decimal.sub(a, b);
}

export function mul(a: any, b: any) {
    return Number.isInteger(a) && Number.isInteger(b) ? a * b : Decimal.mul(a, b);
}

export function div(a: any, b: any) {
    return Number.isInteger(a) && Number.isInteger(b) ? a / b : Decimal.div(a, b);
}

function toDecimalOrUndefined(a: any): Decimal | undefined {
    if (Decimal.isDecimal(a)) return a;
    if (valueIsANumber(a)) return new Decimal(a);
    return undefined;
}
// const compare = operation('comparedTo');

export function compare(a: any, b: any): number | undefined {
    if (a === b) return 0;

    // LATER: we should review the comparision with null to be aligned with the SQL behavior
    // // comparing undefined is always false
    // if (a === undefined || b === undefined) return undefined;
    // // everything is greater than null but any other comparison is false
    // if (a === null) return -1;
    // if (b === null) return 1;

    // To prevent from prototype pollution (https://portswigger.net/web-security/prototype-pollution, https://learn.snyk.io/lesson/prototype-pollution/),
    // some objects may have been created with a null prototype using Object.create(null),
    // those objects cannot be converted to a primitive type and thus are not comparable to anything
    if (a != null && typeof a === 'object' && (Object.getPrototypeOf(a) == null || Array.isArray(a))) return undefined;
    if (b != null && typeof b === 'object' && (Object.getPrototypeOf(b) == null || Array.isArray(b))) return undefined;

    const decimalA = toDecimalOrUndefined(a);
    const decimalB = toDecimalOrUndefined(b);
    if (decimalA && decimalB) return decimalA.comparedTo(decimalB);
    if (a < b) return -1;
    if (a > b) return 1;
    return undefined;
}

function sameTypes(a: any, b: any) {
    return (
        typeof a === typeof b ||
        ((valueIsANumber(a) || Decimal.isDecimal(a)) && (valueIsANumber(b) || Decimal.isDecimal(b)))
    );
}

export function eq(a: any, b: any) {
    // eslint-disable-next-line eqeqeq
    return a == b || compare(a, b) === 0;
}

export function strictEq(a: any, b: any) {
    return a === b || (sameTypes(a, b) && eq(a, b));
}

export function ne(a: any, b: any) {
    return !eq(a, b);
}

export function strictNe(a: any, b: any) {
    return !strictEq(a, b);
}

export function lt(a: any, b: any) {
    try {
        return compare(a, b) === -1;
    } catch (e) {
        console.error(`lt(${a} (${typeof a}) ${b} (${typeof b})) => ${e.message}`);
    }
    return false;
}

export function lte(a: any, b: any) {
    try {
        const cmp = compare(a, b);
        return cmp !== undefined && cmp <= 0;
    } catch (e) {
        console.error(`lte(${a} (${typeof a}) ${b} (${typeof b})) => ${e.message}`);
    }
    return false;
}

export function gt(a: any, b: any) {
    try {
        return compare(a, b) === 1;
    } catch (e) {
        console.error(`gt(${a} (${typeof a}) ${b} (${typeof b})) => ${e.message}`);
    }
    return false;
}

export function gte(a: any, b: any) {
    try {
        const cmp = compare(a, b);
        return cmp !== undefined && cmp >= 0;
    } catch (e) {
        console.error(`gte(${a} (${typeof a}) ${b} (${typeof b})) => ${e.message}`);
    }
    return false;
}

export function negated(a: any) {
    if (valueIsANumber(a)) return -a;
    return ((Decimal.isDecimal(a) && a) || new Decimal(a)).negated();
}

export function plus(a: any) {
    return typeof a === 'string' ? new Decimal(a) : a;
}

export function abs(value: Value): Decimal | number {
    return valueIsANumber(value) ? Math.abs(value) : Decimal.abs(value);
}

export function ceil(value: Value): number {
    return valueIsANumber(value) ? Math.ceil(value) : Decimal.ceil(value).toNumber();
}

export function floor(value: Value): number {
    return valueIsANumber(value) ? Math.floor(value) : Decimal.floor(value).toNumber();
}

export function pow(base: Value, exponent: Value): Decimal | number {
    return valueIsANumber(base) && valueIsANumber(exponent) ? base ** exponent : Decimal.pow(base, exponent);
}

export function max(...values: Value[]): Decimal | number {
    return values.find(valueIsNotANumber) ? Decimal.max(...values) : Math.max(...(<number[]>values));
}

export function min(...values: Value[]): Decimal | number {
    return values.find(valueIsNotANumber) ? Decimal.min(...values) : Math.min(...(<number[]>values));
}

export function round(value: Value): number {
    return valueIsANumber(value) ? Math.round(value) : Decimal.round(value).toNumber();
}

export function sum(...values: Value[]): Decimal | number {
    return values.find(valueIsNotANumber)
        ? Decimal.sum(...values)
        : values.reduce<number>((prevValue: number, currentValue: number) => prevValue + currentValue, 0);
}

export function uselessFunctionV3(): void {}
