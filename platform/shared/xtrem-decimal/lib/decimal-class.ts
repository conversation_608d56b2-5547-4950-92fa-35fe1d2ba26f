/* eslint-disable no-console */
/**
Use http://mikemcl.github.io/decimal.js/
https://github.com/MikeMcl/decimal.js
*/
import { Decimal as _Decimal } from 'decimal.js';

// eslint-disable-next-line @typescript-eslint/naming-convention
export type decimal = number;

export class Decimal extends _Decimal {
    static make(v: Decimal | string | number) {
        return new Decimal(v);
    }

    static toNumber(v: Decimal | number) {
        if (typeof v === 'number') return v;
        return v == null ? NaN : v.toNumber();
    }

    static roundAt(v: Decimal | string | number, scale = 0): decimal {
        if (scale === 0) return Decimal.make(v).round() as unknown as decimal;
        const scaleFactor = 10 ** scale;
        // Method actually returns a Decimal object, but it is typed as decimal (number) because
        // our transpiler converts decimals (numbers) to Decimal objects.
        return Decimal.make(v).mul(scaleFactor).round().div(scaleFactor) as unknown as decimal;
    }
}
