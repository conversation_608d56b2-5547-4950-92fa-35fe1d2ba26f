import { GraphQLTypes } from '@sage/xtrem-shared';
import { expect } from 'chai';
import { EMPTIABLE_GRAPHQL_TYPES, geFilterQuery, getFilterObject, timeFrameValues } from '../index';

type FilterProperty = Parameters<typeof getFilterObject>[0]['filters'][number];

describe('geFilterQuery', () => {
    it('should return the right filter for "contains" filter type', () => {
        const result = geFilterQuery({ filterType: 'contains', filterValue: 'test', type: 'String' });
        expect(result).to.deep.equal({ _regex: 'test', _options: 'i' });
    });

    it('should return the right filter for "notContains" filter type', () => {
        const result = geFilterQuery({ filterType: 'notContains', filterValue: 'test', type: 'String' });
        expect(result).to.deep.equal({ _regex: '^((?!test).)*$', _options: 'i' });
    });

    it('should return the right filter for "startsWith" filter type', () => {
        const result = geFilterQuery({ filterType: 'startsWith', filterValue: 'test', type: 'String' });
        expect(result).to.deep.equal({ _regex: '^test', _options: 'i' });
    });

    it('should return the right filter for "endsWith" filter type', () => {
        const result = geFilterQuery({ filterType: 'endsWith', filterValue: 'test', type: 'String' });
        expect(result).to.deep.equal({ _regex: 'test$', _options: 'i' });
    });

    it('should return the right filter for "empty" filter type - with empty string', () => {
        EMPTIABLE_GRAPHQL_TYPES.forEach(type => {
            const result = geFilterQuery({ filterType: 'empty', filterValue: 'test', type });
            expect(result).to.deep.equal({ _or: [{ _eq: null }, { _eq: '' }] });
        });
    });

    it('should return the right filter for "empty" filter type - without empty string', () => {
        [
            GraphQLTypes.Boolean,
            GraphQLTypes.Date,
            GraphQLTypes.DateTime,
            GraphQLTypes.Decimal,
            GraphQLTypes.Enum,
            GraphQLTypes.ExternalReference,
            GraphQLTypes.Float,
            GraphQLTypes.Int,
            GraphQLTypes.IntOrString,
            GraphQLTypes.IntReference,
        ].forEach(type => {
            const result = geFilterQuery({ filterType: 'empty', filterValue: 'test', type });
            expect(result).to.deep.equal({ _eq: null });
        });
    });

    it('should return the right filter for "notEmpty" filter type - with empty string', () => {
        EMPTIABLE_GRAPHQL_TYPES.forEach(type => {
            const result = geFilterQuery({ filterType: 'notEmpty', filterValue: 'test', type });
            expect(result).to.deep.equal({ _and: [{ _ne: null }, { _ne: '' }] });
        });
    });

    it('should return the right filter for "notEmpty" filter type - without empty string', () => {
        [
            GraphQLTypes.Boolean,
            GraphQLTypes.Date,
            GraphQLTypes.DateTime,
            GraphQLTypes.Decimal,
            GraphQLTypes.Enum,
            GraphQLTypes.ExternalReference,
            GraphQLTypes.Float,
            GraphQLTypes.Int,
            GraphQLTypes.IntOrString,
            GraphQLTypes.IntReference,
        ].forEach(type => {
            const result = geFilterQuery({ filterType: 'notEmpty', filterValue: 'test', type });
            expect(result).to.deep.equal({ _ne: null });
        });
    });

    it('should return the right filter for "equals" filter type', () => {
        const result = geFilterQuery({ filterType: 'equals', filterValue: 'test', type: 'String' });
        expect(result).to.deep.equal({ _eq: 'test' });
    });

    it('should return the right filter for "matches" filter type', () => {
        const result = geFilterQuery({ filterType: 'matches', filterValue: 'test', type: 'String' });
        expect(result).to.deep.equal({ _eq: 'test' });
    });

    it('should return the right filter for "notEqual" filter type', () => {
        const result = geFilterQuery({ filterType: 'notEqual', filterValue: 'test', type: 'String' });
        expect(result).to.deep.equal({ _ne: 'test' });
    });

    it('should return the right filter for "multiNotEqual" filter type', () => {
        const result = geFilterQuery({ filterType: 'multiNotEqual', filterValue: ['test'], type: 'IntReference' });
        expect(result).to.deep.equal({ _nin: ['test'] });
    });

    it('should return the right filter for "greaterThan" filter type', () => {
        const result = geFilterQuery({ filterType: 'greaterThan', filterValue: 'test', type: 'String' });
        expect(result).to.deep.equal({ _gt: 'test' });
    });

    it('should return the right filter for "greaterThanOrEqual" filter type', () => {
        const result = geFilterQuery({ filterType: 'greaterThanOrEqual', filterValue: 'test', type: 'String' });
        expect(result).to.deep.equal({ _gte: 'test' });
    });

    it('should return the right filter for "lessThan" filter type', () => {
        const result = geFilterQuery({ filterType: 'lessThan', filterValue: 'test', type: 'String' });
        expect(result).to.deep.equal({ _lt: 'test' });
    });

    it('should return the right filter for "lessThanOrEqual" filter type', () => {
        const result = geFilterQuery({ filterType: 'lessThanOrEqual', filterValue: 'test', type: 'String' });
        expect(result).to.deep.equal({ _lte: 'test' });
    });

    it('should return the right filter for "inRange" filter type', () => {
        const result = geFilterQuery({ filterType: 'inRange', filterValue: '10~20', type: 'Int' });
        expect(result).to.deep.equal({ _gte: '10', _lte: '20' });
    });

    it('should return the right filter for "set" filter type', () => {
        const result = geFilterQuery({ filterType: 'set', filterValue: ['10'], type: 'Int' });
        expect(result).to.deep.equal({ _in: ['10'] });
    });
});

describe('getFilterObject', () => {
    it('should return filter object for valid filters', () => {
        const filters: FilterProperty[] = [
            { filterType: 'equals', filterValue: 'test', id: 'name', data: { type: 'String' } },
        ];
        const result = getFilterObject({ filters });
        expect(result).to.deep.equal({ name: { _and: [{ _eq: 'test' }] } });
    });

    it('should throw error for invalid timeFrame filter value', () => {
        const filters: FilterProperty[] = [
            { filterType: 'timeFrame', filterValue: 'invalid', id: 'date', data: { type: 'Date' } },
        ];
        expect(() => getFilterObject({ filters })).to.throw(
            `Invalid filter value for type "timeFrame": expected one of ${JSON.stringify(timeFrameValues)}, got 'invalid'`,
        );
    });

    it('should throw error for unsupported filter value', () => {
        const filters: FilterProperty[] = [
            { filterType: 'inRange', filterValue: ['10'], id: 'age', data: { type: 'Number' } },
        ];
        expect(() => getFilterObject({ filters })).to.throw(
            `Unsupported filter. Filter type: inRange, filter value: ["10"]`,
        );
    });
});
