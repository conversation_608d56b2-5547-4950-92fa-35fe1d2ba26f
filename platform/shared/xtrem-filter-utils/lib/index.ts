import { DateRange } from '@sage/xtrem-date-time';
import {
    CONTAINS,
    EMPTY,
    ENDS_WITH,
    EQUALS,
    FilterProperty,
    FilterType,
    GREATER_THAN,
    GREATER_THAN_EQUAL,
    GraphQLTypes,
    LESS_THAN,
    LESS_THAN_EQUAL,
    LocalizeLocale,
    MATCHES,
    MULTI_NOT_EQUALS,
    NOT_CONTAINS,
    NOT_EMPTY,
    NOT_EQUALS,
    NodeDetails,
    RANGE,
    RANGE_DIVIDER,
    SET,
    STARTS_WITH,
    deepMerge,
} from '@sage/xtrem-shared';
import { escapeRegExp, set } from 'lodash';

export const EMPTIABLE_GRAPHQL_TYPES: GraphQLTypes[] = [
    GraphQLTypes._InputTextStream,
    GraphQLTypes._OutputTextStream,
    GraphQLTypes.Id,
    GraphQLTypes._InputStream,
    GraphQLTypes.Json,
    GraphQLTypes.String,
];

export const geFilterQuery = ({
    filterType,
    filterValue,
    parameter,
    type,
}: {
    filterType: FilterType<any>;
    filterValue: any;
    parameter?: boolean;
    type: NodeDetails['type'];
}): any => {
    const parameterPlaceholder = `{{${filterValue}}}`;
    const escapedParameterPlaceholder = `{{${filterValue}__ESCAPED}}`;
    switch (filterType) {
        case CONTAINS:
            if (parameter) {
                return { _regex: escapedParameterPlaceholder, _options: 'i' };
            }
            return { _regex: escapeRegExp(filterValue), _options: 'i' };
        case STARTS_WITH:
            if (parameter) {
                return { _regex: `^${escapedParameterPlaceholder}`, _options: 'i' };
            }
            return { _regex: `^${escapeRegExp(filterValue)}`, _options: 'i' };
        case ENDS_WITH:
            if (parameter) {
                return { _regex: `${escapedParameterPlaceholder}$`, _options: 'i' };
            }
            return { _regex: `${escapeRegExp(filterValue)}$`, _options: 'i' };
        case EMPTY: {
            return EMPTIABLE_GRAPHQL_TYPES.includes(type as GraphQLTypes)
                ? { _or: [{ _eq: null }, { _eq: '' }] }
                : { _eq: null };
        }
        case NOT_EMPTY: {
            return EMPTIABLE_GRAPHQL_TYPES.includes(type as GraphQLTypes)
                ? { _and: [{ _ne: null }, { _ne: '' }] }
                : { _ne: null };
        }
        case EQUALS:
            return { _eq: parameter ? parameterPlaceholder : filterValue };
        case MATCHES:
            return { _eq: parameter ? parameterPlaceholder : filterValue };
        case NOT_EQUALS:
            return { _ne: parameter ? parameterPlaceholder : filterValue };
        case NOT_CONTAINS:
            return { _regex: `^((?!${escapeRegExp(filterValue)}).)*$`, _options: 'i' };
        case MULTI_NOT_EQUALS:
            return { _nin: parameter ? parameterPlaceholder : filterValue };
        case GREATER_THAN:
            return { _gt: parameter ? parameterPlaceholder : filterValue };
        case GREATER_THAN_EQUAL:
            return { _gte: parameter ? parameterPlaceholder : filterValue };
        case LESS_THAN:
            return { _lt: parameter ? parameterPlaceholder : filterValue };
        case LESS_THAN_EQUAL:
            return { _lte: parameter ? parameterPlaceholder : filterValue };
        case RANGE: {
            if (parameter) {
                const parameters = filterValue.toString().split(RANGE_DIVIDER);
                return {
                    _gte: `{{${parameters[0]}}}`,
                    _lte: `{{${parameters[1]}}}`,
                };
            }

            const values = filterValue.toString().split(RANGE_DIVIDER);
            return {
                _gte: values[0],
                _lte: values[1],
            };
        }
        case SET: {
            return {
                _in: filterValue,
            };
        }
        default:
            return {};
    }
};

export const timeFrameValues = [
    'same-month',
    'same-year',
    'previous-month',
    'previous-year',
    'last-7-days',
    'last-30-days',
    'previous-day',
    'same-day',
    'previous-week',
    'same-week',
    'next-day',
    'next-week',
    'next-month',
    'next-year',
] as const;

export type TimeFrameValues = (typeof timeFrameValues)[number];

export function getFilterObject({
    filters,
    locale,
}: {
    filters: (Pick<FilterProperty, 'filterType' | 'parameter' | 'filterValue' | 'id'> & {
        data: Pick<FilterProperty['data'], 'type'>;
    })[];
    locale?: LocalizeLocale;
}): any {
    return filters?.reduce((acc, { filterType, parameter, filterValue: filterVal, id, data: { type } }) => {
        if (
            Array.isArray(filterVal) &&
            ((filterVal.length !== 2 && filterType === RANGE) ||
                (filterType !== SET && filterType !== MULTI_NOT_EQUALS && filterType !== RANGE))
        ) {
            throw new Error(
                `Unsupported filter. Filter type: ${filterType}, filter value: ${JSON.stringify(filterVal)}`,
            );
        }
        let filterValue = filterVal;
        let filter;
        if (filterType === 'timeFrame') {
            if (type !== 'Date' && type !== 'DateTime') {
                throw new Error(
                    `Invalid filter value for filter type "timeFrame": expected one of ['Date', 'DateTime'], got '${type}'`,
                );
            }
            if (!timeFrameValues.includes(filterValue)) {
                throw new Error(
                    `Invalid filter value for type "timeFrame": expected one of ${JSON.stringify(
                        timeFrameValues,
                    )}, got '${filterValue}'`,
                );
            }
            const range = DateRange.getDateRange({
                date: new Date().toISOString().slice(0, 10),
                range: filterValue,
                locale,
            });
            filter = {
                _gte: range.start,
                _lte: range.end,
            };
        } else {
            if (type === 'Boolean') {
                filterValue = filterValue === true || filterValue === 'true';
            }
            if ((type === 'Date' || type === 'DateTime') && !parameter) {
                filterValue = Array.isArray(filterValue)
                    ? filterValue.map(g => g.rawValue).join(RANGE_DIVIDER)
                    : filterValue?.rawValue;
            }
            filter = geFilterQuery({
                filterType,
                filterValue,
                parameter,
                type,
            });
        }
        return deepMerge(acc, set({}, id, { _and: [filter] }));
    }, {});
}
