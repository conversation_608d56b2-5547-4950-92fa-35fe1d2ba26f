import { default as _ } from 'lodash';
// eslint-disable-next-line import/no-extraneous-dependencies
import db from 'mime-db';
import * as fs from 'fs';
import * as fsp from 'path';
// eslint-disable-next-line import/no-extraneous-dependencies
import * as prettier from 'prettier';

const mimeTypesByExtension = Object.keys(db).reduce((map, mimeType) => {
    const extensions = db[mimeType].extensions;
    if (extensions) {
        extensions.forEach(extension => {
            const mimeTypes = map[extension] ?? [];
            mimeTypes.push(mimeType);
            map[extension] = mimeTypes;
        });
    }
    return map;
}, {});

const allowedExtensions = [
    'bmp',
    'csv',
    'doc',
    'docm',
    'docx',
    'dxf',
    'emlx', // Apple Mail format
    'gif',
    'heic',
    'heif',
    'jfif',
    'jpeg',
    'jpg',
    'msg', // outlook
    'odp', // open document
    'ods', // open document
    'odt', // open document
    'pages', // Apple pages
    'pdf',
    'png',
    'ppt',
    'pptm',
    'pptx',
    'psd',
    'rtf', // word
    'sylk',
    'slk',
    'svg',
    'tif',
    'tiff',
    'txt',
    'vsd', // visio
    'vsdx', // visio
    'vss', // visio
    'vssm', // visio
    'vssx', // visio
    'vst', // visio
    'vstm', // visio
    'vstx', // visio
    'vsw', // visio
    'webp',
    'xls',
    'xlsm',
    'xlsx',
    'xml',
    'zip',
];

// .csv is a text file, so we should also accept text/plain as a fallback for infra checks using file-type
mimeTypesByExtension.csv = [...(mimeTypesByExtension.csv ?? []), 'text/csv', 'text/plain'];

// .jfif is a JPEG File Interchange Format
mimeTypesByExtension.jfif ??= ['image/jpeg'];

// .slk and .sylk are a Microsoft Excel Symbolic Link Format stored as a plain text file
// see https://en.wikipedia.org/wiki/Symbolic_Link_(SYLK)
if (mimeTypesByExtension.slk == null) {
    mimeTypesByExtension.slk = ['application/x-sylk', 'text/plain'];
    mimeTypesByExtension.sylk = mimeTypesByExtension.slk;
}
// .emlx is the Apple Mail format
// It is an plain ascii text format, see https://www.w3.org/Protocols/rfc822/3_Lexical.html#z2
mimeTypesByExtension.emlx ??= ['message/rfc822', 'text/plain'];

// Visio mime types
mimeTypesByExtension.vsdx ??= [
    'application/vnd.visio',
    'application/vnd.ms-visio.drawing',
    'application/vnd.ms-visio.drawing.main+xml',
];
mimeTypesByExtension.vssm ??= ['application/vnd.visio', 'application/vnd.ms-visio.stencil.macroEnabled.12'];
mimeTypesByExtension.vssx ??= ['application/vnd.visio', 'application/vnd.ms-visio.stencil'];
mimeTypesByExtension.vstm ??= ['application/vnd.visio', 'application/vnd.ms-visio.stencil.macroEnabled.12'];
mimeTypesByExtension.vstx ??= ['application/vnd.visio', 'application/vnd.ms-visio.template'];

// AutoCAD non-standard mime types from https://www.sitepoint.com/mime-types-complete-list/
// This format can be binary but also a plain ascii text file
mimeTypesByExtension.dxf.push('application/dxf', 'image/x-dwg', 'text/plain');

const attachmentsMimeTypesByExtention = Object.fromEntries(
    allowedExtensions.sort().map(ext => [ext, _.uniq(mimeTypesByExtension[ext]).sort()]),
);

const content = `// =================================================================================================
// DO NOT EDIT: Generated file! (see scripts/gen-mime-types.mjs)
// =================================================================================================
import * as _ from 'lodash';
import { Dict } from './util';

export const attachmentsMimeTypesByExtention: Dict<string[]> = ${JSON.stringify(attachmentsMimeTypesByExtention)};

/**
 * Allowed mime types for attachments
 * @see https://www.iana.org/assignments/media-types/media-types.xhtml
 * @see https://www.npmjs.com/package/mime-db
 * @see https://mimetype.io/
 */
export const attachmentsMimeTypes = _.uniq(_.flatten(Object.values(attachmentsMimeTypesByExtention))).sort();`;

const file = fsp.join(import.meta.dirname, '../lib/attachments-mime-types.ts');
const prettierConfigPath = fsp.resolve(import.meta.dirname, '../../../../.prettierrc');
const prettierConfig = JSON.parse(fs.readFileSync(prettierConfigPath, 'utf-8'));
const prettifyContent = await prettier.format(content, { ...prettierConfig, filepath: file });

const currentContent = fs.readFileSync(file, 'utf8');
if (currentContent === prettifyContent) {
    console.log(`INFO: No change in generated file ${file}\n`);
    process.exit(0);
}
console.log(`INFO: Generating file ${file}\n`);
fs.writeFileSync(file, prettifyContent);
