import { assert } from 'chai';
import { dotNotate, format, handleBars, prettify, tryJsonParse } from '../lib/format-string';

describe(' HandleBar UnitTest', () => {
    it('Simple HandleBar Request', () => {
        const { result } = handleBars('{{test}}', { test: 'mytest' });
        assert.equal(result, 'mytest');
    });

    it(' HandleBar Request Using inputParameters Helpers', () => {
        const { result } = handleBars('{{inputParameters}}', {
            properties: {
                test: 'montest',
                test2: { a: '1', b: '2' },
                contacts: [{ test: 'MyTest', addresses: [{ test: 'MyTest' }] }],
            },
        });
        assert.strictEqual(
            result,
            '{test:"montest",test2:{a:"1",b:"2"},contacts:[{test:"MyTest",addresses:[{test:"MyTest"}]}]}',
        );
    });

    it(' HandleBar Request Using inputData Helpers - same as inputParameters but will delete the first { and the last  } to be use in a function with () ', () => {
        const { result } = handleBars('{{inputData}}', {
            properties: {
                test: 'montest',
                test2: { a: '1', b: '2' },
                contacts: [{ test: 'MyTest', addresses: [{ test: 'MyTest' }] }],
            },
        });
        assert.strictEqual(
            result,
            'test:"montest",test2:{a:"1",b:"2"},contacts:[{test:"MyTest",addresses:[{test:"MyTest"}]}]',
        );
    });

    it(' can format ', () => {
        let result = format('{{test}}, {{test2.a}}, {{test2.b}}', 'en-US', {
            test: 'montest',
            test2: { a: '1', b: '2' },
        });
        assert.strictEqual(result, 'montest, 1, 2');

        result = format('Formatted no data', 'en-US');

        assert.strictEqual(result, 'Formatted no data');
    });

    it(' can dotNotate ', () => {
        assert.deepEqual(dotNotate({ a: '1', b: { c: '2' } }), { a: '1', 'b.c': '2', b: { c: '2' } });
    });

    it(' can tryJsonParse ', () => {
        assert.deepEqual(tryJsonParse('{ "a": "1", "b": { "c": "2" } }'), { a: '1', b: { c: '2' } });
        assert.isUndefined(tryJsonParse('{ a: "1", b: { c: "2" } '));
    });

    it(' can prettify html ', async () => {
        assert.deepEqual(await prettify('<DIV><b>a</b></DIV>', 'html'), '<div><b>a</b></div>\n');
    });

    it(' can prettify json ', async () => {
        assert.deepEqual(await prettify('{foo: "var"}', 'json'), '{ "foo": "var" }\n');
    });

    it(' can prettify css ', async () => {
        assert.deepEqual(await prettify('.class {font-weigth:   bold}', 'css'), '.class {\n  font-weigth: bold;\n}\n');
    });
});
