import * as chai from 'chai';
import { assert } from 'chai';
import * as chaiAsPromised from 'chai-as-promised';
import { it } from 'mocha';
import { SortedList, integer, setSortValues } from '../lib/set-sort-values';

chai.use(chaiAsPromised);

class TestSortedList implements SortedList {
    constructor(public items: integer[]) {}

    private checkIndex(index: integer) {
        if (index < 0 || index > this.items.length) throw new Error(`invalid index: ${index}`);
    }

    get length() {
        return this.items.length;
    }

    async setSortValue(index: integer, value: integer): Promise<void> {
        this.checkIndex(index);
        this.items[index] = value;
    }

    async getSortValue(index: integer): Promise<integer> {
        this.checkIndex(index);
        return this.items[index];
    }

    toString() {
        return this.items.map(l => l.toString()).join('\n');
    }
}

async function test(
    values: integer[],
    start: integer,
    end: integer,
    expected: integer[],
    preserveOldValues?: boolean,
): Promise<void> {
    const list = new TestSortedList(values);
    await setSortValues(list, start, end, { preserveOldValues });
    assert.deepEqual(list.items, expected);
}

describe('set sort indexes', () => {
    it('can set entire array', async () => test([NaN, NaN, NaN], 0, 3, [10, 20, 30]));
    it('can set at end of array', async () => test([10, NaN, NaN], 1, 3, [10, 20, 30]));
    it('can set at beginning of array', async () => test([NaN, NaN, 10], 0, 2, [3, 5, 10]));
    it('can set at beginning of array (with collision)', async () =>
        test([NaN, NaN, 2, 3, 20], 0, 2, [3, 5, 10, 15, 20]));
    it('can set one item in the middle of array', async () => test([10, NaN, 20], 1, 2, [10, 15, 20]));
    it('can set several items in the middle of array', async () =>
        test([10, NaN, NaN, NaN, NaN, NaN, 20], 1, 6, [10, 11, 12, 15, 16, 17, 20]));
    it('can handle collisions at end of array', async () => test([10, 19, NaN, 20], 2, 3, [10, 19, 30, 40]));
    it('can handle collisions inside array', async () => test([10, NaN, 11, 20], 1, 2, [10, 12, 15, 20]));
    it('can handle multiple collisions', async () => {
        // 3 collisions before the end
        await test([10, NaN, 11, 12, 13, 20], 1, 2, [10, 11, 12, 15, 17, 20]);
        // 4 collisions at the end
        await test([10, NaN, 11, 12, 13, 14], 1, 2, [10, 20, 30, 40, 50, 60]);
    });
    it('will throw an error if invalid start or end provided', async () => {
        await assert.isRejected(
            test([10, NaN, 11, 20], -1, 2, [10, 12, 15, 20]),
            Error,
            'invalid range: start=-1, end=2, length=4',
        );

        await assert.isRejected(
            test([10, NaN, 11, 20], 1, 10, [10, 12, 15, 20]),
            Error,
            'invalid range: start=1, end=10, length=4',
        );
    });

    it('will throw an error if isNotOverridable is true and there is a collision', async () => {
        await assert.isRejected(
            test([10, NaN, 11, 20], 1, 2, [10, 12, 15, 20], true),
            Error,
            'Cannot override the sort value',
        );
    });
});
