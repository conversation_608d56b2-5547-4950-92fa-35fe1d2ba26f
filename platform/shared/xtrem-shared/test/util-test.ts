import { assert, use } from 'chai';
import { it } from 'mocha';
import { expectNever } from 'tsd';
import { arrayMovePreservingOrder, arrayOrderContiguous, KeysOf, strictTypeMatch } from '../lib/util';

use(require('chai-as-promised'));

const data = [
    { group: 0, _id: 'a' },
    { group: 0, _id: 'b' },
    { group: 1, _id: 'c' },
    { group: 1, _id: 'd' },
    { group: 1, _id: 'e' },
    { group: 1, _id: 'f' },
    { group: 2, _id: 'g' },
    { group: 2, _id: 'h' },
    { group: 3, _id: 'i' },
    { group: 4, _id: 'j' },
    { group: 4, _id: 'k' },
];

const data2 = [
    { group: 0, _id: 'a' },
    { group: 1, _id: 'b' },
    { group: 2, _id: 'c' },
    { group: 3, _id: 'd' },
];

const data3 = [
    { group: 0, _id: 'a' },
    { group: 1, _id: 'b' },
    { group: 1, _id: 'c' },
    { group: 2, _id: 'd' },
];

describe('util', () => {
    describe('arrayMovePreservingOrder & arrayOrderContiguous', () => {
        it('set "f" to group 0 & reorder', () => {
            const { success, data: moved } = arrayMovePreservingOrder({
                data,
                fromIndex: 5,
                orderByKey: 'group',
                toValue: 0,
            });
            assert.isTrue(success);
            assert.deepEqual(moved, [
                {
                    group: 0,
                    _id: 'a',
                },
                {
                    group: 0,
                    _id: 'b',
                },
                {
                    group: 0,
                    _id: 'f',
                },
                {
                    group: 1,
                    _id: 'c',
                },
                {
                    group: 1,
                    _id: 'd',
                },
                {
                    group: 1,
                    _id: 'e',
                },
                {
                    group: 2,
                    _id: 'g',
                },
                {
                    group: 2,
                    _id: 'h',
                },
                {
                    group: 3,
                    _id: 'i',
                },
                {
                    group: 4,
                    _id: 'j',
                },
                {
                    group: 4,
                    _id: 'k',
                },
            ]);
            assert.deepEqual(arrayOrderContiguous({ key: 'group', orderedData: moved }), []);
        });
        it('set "i" to group 1 & reorder', () => {
            const { success, data: moved } = arrayMovePreservingOrder({
                data,
                fromIndex: 8,
                orderByKey: 'group',
                toValue: 1,
            });
            assert.isTrue(success);
            assert.deepEqual(moved, [
                {
                    group: 0,
                    _id: 'a',
                },
                {
                    group: 0,
                    _id: 'b',
                },
                {
                    group: 1,
                    _id: 'c',
                },
                {
                    group: 1,
                    _id: 'd',
                },
                {
                    group: 1,
                    _id: 'e',
                },
                {
                    group: 1,
                    _id: 'f',
                },
                {
                    group: 1,
                    _id: 'i',
                },
                {
                    group: 2,
                    _id: 'g',
                },
                {
                    group: 2,
                    _id: 'h',
                },
                {
                    group: 4,
                    _id: 'j',
                },
                {
                    group: 4,
                    _id: 'k',
                },
            ]);
            assert.deepEqual(arrayOrderContiguous({ key: 'group', orderedData: moved }), [
                {
                    columnId: 'group',
                    rowData: {
                        group: 3,
                        _id: 'j',
                    },
                    rowId: 'j',
                    value: 3,
                },
                {
                    columnId: 'group',
                    rowData: {
                        group: 3,
                        _id: 'k',
                    },
                    rowId: 'k',
                    value: 3,
                },
            ]);
        });
        it('set "a" to group 3 & reorder', () => {
            const { success, data: moved } = arrayMovePreservingOrder({
                data,
                fromIndex: 0,
                orderByKey: 'group',
                toValue: 3,
            });
            assert.isTrue(success);
            assert.deepEqual(moved, [
                {
                    group: 0,
                    _id: 'b',
                },
                {
                    group: 1,
                    _id: 'c',
                },
                {
                    group: 1,
                    _id: 'd',
                },
                {
                    group: 1,
                    _id: 'e',
                },
                {
                    group: 1,
                    _id: 'f',
                },
                {
                    group: 2,
                    _id: 'g',
                },
                {
                    group: 2,
                    _id: 'h',
                },
                {
                    group: 3,
                    _id: 'i',
                },
                {
                    group: 3,
                    _id: 'a',
                },
                {
                    group: 4,
                    _id: 'j',
                },
                {
                    group: 4,
                    _id: 'k',
                },
            ]);
            assert.deepEqual(arrayOrderContiguous({ key: 'group', orderedData: moved }), []);
        });
        it('set "k" to group 0 & reorder', () => {
            const { success, data: moved } = arrayMovePreservingOrder({
                data,
                fromIndex: 10,
                orderByKey: 'group',
                toValue: 0,
            });
            assert.isTrue(success);
            assert.deepEqual(moved, [
                {
                    group: 0,
                    _id: 'a',
                },
                {
                    group: 0,
                    _id: 'b',
                },
                {
                    group: 0,
                    _id: 'k',
                },
                {
                    group: 1,
                    _id: 'c',
                },
                {
                    group: 1,
                    _id: 'd',
                },
                {
                    group: 1,
                    _id: 'e',
                },
                {
                    group: 1,
                    _id: 'f',
                },
                {
                    group: 2,
                    _id: 'g',
                },
                {
                    group: 2,
                    _id: 'h',
                },
                {
                    group: 3,
                    _id: 'i',
                },
                {
                    group: 4,
                    _id: 'j',
                },
            ]);
            assert.deepEqual(arrayOrderContiguous({ key: 'group', orderedData: moved }), []);
        });
        it('set "c" to group 1 & reorder', () => {
            const { success, data: moved } = arrayMovePreservingOrder({
                data: data2,
                fromIndex: 2,
                orderByKey: 'group',
                toValue: 1,
            });
            assert.isTrue(success);
            assert.deepEqual(moved, [
                {
                    group: 0,
                    _id: 'a',
                },
                {
                    group: 1,
                    _id: 'c',
                },
                {
                    group: 1,
                    _id: 'b',
                },
                {
                    group: 3,
                    _id: 'd',
                },
            ]);
            assert.deepEqual(arrayOrderContiguous({ key: 'group', orderedData: moved }), [
                {
                    columnId: 'group',
                    rowData: {
                        group: 2,
                        _id: 'd',
                    },
                    rowId: 'd',
                    value: 2,
                },
            ]);
        });
        it('set "a" to group 1 & reorder', () => {
            const { success, data: moved } = arrayMovePreservingOrder({
                data: data3,
                fromIndex: 0,
                orderByKey: 'group',
                toValue: 1,
            });
            assert.isTrue(success);
            assert.deepEqual(moved, [
                {
                    group: 1,
                    _id: 'b',
                },
                {
                    group: 1,
                    _id: 'c',
                },
                {
                    group: 1,
                    _id: 'a',
                },
                {
                    group: 2,
                    _id: 'd',
                },
            ]);
            assert.deepEqual(arrayOrderContiguous({ key: 'group', orderedData: moved }), [
                {
                    columnId: 'group',
                    rowData: {
                        group: 0,
                        _id: 'b',
                    },
                    rowId: 'b',
                    value: 0,
                },
                {
                    columnId: 'group',
                    rowData: {
                        group: 0,
                        _id: 'c',
                    },
                    rowId: 'c',
                    value: 0,
                },
                {
                    columnId: 'group',
                    rowData: {
                        group: 0,
                        _id: 'a',
                    },
                    rowId: 'a',
                    value: 0,
                },
                {
                    columnId: 'group',
                    rowData: {
                        group: 1,
                        _id: 'd',
                    },
                    rowId: 'd',
                    value: 1,
                },
            ]);
        });
    });

    describe('arrayOrderContiguous', () => {
        it('should return an empty array', () => {
            assert.deepEqual(arrayOrderContiguous({ orderedData: [], key: '' } as any), []);
        });

        it('should start from group 0', () => {
            assert.deepEqual(
                arrayOrderContiguous({
                    key: 'group',
                    orderedData: [
                        { group: 1, _id: 'a' },
                        { group: 1, _id: 'b' },
                        { group: 1, _id: 'c' },
                        { group: 1, _id: 'd' },
                        { group: 1, _id: 'e' },
                    ],
                }),
                [
                    {
                        columnId: 'group',
                        rowData: {
                            group: 0,
                            _id: 'a',
                        },
                        rowId: 'a',
                        value: 0,
                    },
                    {
                        columnId: 'group',
                        rowData: {
                            group: 0,
                            _id: 'b',
                        },
                        rowId: 'b',
                        value: 0,
                    },
                    {
                        columnId: 'group',
                        rowData: {
                            group: 0,
                            _id: 'c',
                        },
                        rowId: 'c',
                        value: 0,
                    },
                    {
                        columnId: 'group',
                        rowData: {
                            group: 0,
                            _id: 'd',
                        },
                        rowId: 'd',
                        value: 0,
                    },
                    {
                        columnId: 'group',
                        rowData: {
                            group: 0,
                            _id: 'e',
                        },
                        rowId: 'e',
                        value: 0,
                    },
                ],
            );
        });
        it('should start from group 0', () => {
            assert.deepEqual(
                arrayOrderContiguous({
                    key: 'group',
                    orderedData: [
                        { group: 1, _id: 'b' },
                        { group: 1, _id: 'a' },
                        { group: 2, _id: 'c' },
                        { group: 2, _id: 'd' },
                        { group: 3, _id: 'e' },
                    ],
                }),
                [
                    {
                        columnId: 'group',
                        rowData: {
                            group: 0,
                            _id: 'b',
                        },
                        rowId: 'b',
                        value: 0,
                    },
                    {
                        columnId: 'group',
                        rowData: {
                            group: 0,
                            _id: 'a',
                        },
                        rowId: 'a',
                        value: 0,
                    },
                    {
                        columnId: 'group',
                        rowData: {
                            group: 1,
                            _id: 'c',
                        },
                        rowId: 'c',
                        value: 1,
                    },
                    {
                        columnId: 'group',
                        rowData: {
                            group: 1,
                            _id: 'd',
                        },
                        rowId: 'd',
                        value: 1,
                    },
                    {
                        columnId: 'group',
                        rowData: {
                            group: 2,
                            _id: 'e',
                        },
                        rowId: 'e',
                        value: 2,
                    },
                ],
            );
        });
        it('should cascade down', () => {
            assert.deepEqual(
                arrayOrderContiguous({
                    key: 'group',
                    orderedData: [
                        { group: 0, _id: 'b' },
                        { group: 1, _id: 'e' },
                        { group: 0, _id: 'a' },
                        { group: 0, _id: 'c' },
                        { group: 0, _id: 'd' },
                    ],
                }),
                [
                    {
                        columnId: 'group',
                        rowData: {
                            group: 1,
                            _id: 'a',
                        },
                        rowId: 'a',
                        value: 1,
                    },
                    {
                        columnId: 'group',
                        rowData: {
                            group: 1,
                            _id: 'c',
                        },
                        rowId: 'c',
                        value: 1,
                    },
                    {
                        columnId: 'group',
                        rowData: {
                            group: 1,
                            _id: 'd',
                        },
                        rowId: 'd',
                        value: 1,
                    },
                ],
            );
        });
        it('should cascade down from first item', () => {
            assert.deepEqual(
                arrayOrderContiguous({
                    key: 'group',
                    orderedData: [
                        { group: 5, _id: 'b' },
                        { group: 0, _id: 'e' },
                        { group: 0, _id: 'a' },
                        { group: 1, _id: 'c' },
                    ],
                }),
                [
                    {
                        columnId: 'group',
                        rowData: {
                            group: 0,
                            _id: 'b',
                        },
                        rowId: 'b',
                        value: 0,
                    },
                ],
            );
        });
        it('should group when dragging down', () => {
            assert.deepEqual(
                arrayOrderContiguous({
                    key: 'group',
                    orderedData: [
                        { group: 0, _id: 'a' },
                        { group: 1, _id: 'b' },
                        { group: 2, _id: 'd' },
                        { group: 1, _id: 'c' },
                        { group: 2, _id: 'e' },
                    ],
                }),
                [
                    {
                        columnId: 'group',
                        rowData: {
                            group: 2,
                            _id: 'c',
                        },
                        rowId: 'c',
                        value: 2,
                    },
                ],
            );
        });
    });

    it('should be able to get object keys', () => {
        strictTypeMatch<KeysOf<{ a: ''; b: ''; 1: '' }>, 'a' | 'b' | '1'>(true);
        strictTypeMatch<KeysOf<[1, 2]>, `${number}`>(true);
        strictTypeMatch<KeysOf<any>, string>(true);
        strictTypeMatch<KeysOf<any[]>, string>(true);
        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        (): void => {
            expectNever(keysOf([] as unknown[]));
        };
        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        (): void => {
            expectNever(keysOf({}));
        };
    });
});

declare function keysOf<T>(a: T): KeysOf<T>;
