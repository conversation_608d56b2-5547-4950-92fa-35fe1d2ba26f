// eslint-disable-next-line @typescript-eslint/naming-convention
export type integer = number;

// we don't want to impart xtrem-async-helper here so I'm copying the few definitions that we need here
export type AnyValue = boolean | string | number | object | null | undefined | AnyValue[];

export type AsyncResponse<T extends unknown | void> = T | PromiseLike<T>;

/** silly void promise to keep sonar happy */
export const voidPromise = undefined as unknown as Promise<void>;

/** Interface that the `list` parameter of setSortValues must implement */
export interface SortedList {
    /** accessor to get the length of the list */
    readonly length: integer;

    /** get the value of the `_sortValue` property of the item at `index` */
    getSortValue(index: integer): Promise<integer>;

    /** set the value of the `_sortValue` property of the item at `index` */
    setSortValue(index: integer, value: integer): Promise<void>;
}

// Set sort value for all items between start (included) and end (excluded)
// Returns true if success, false if values collided because of limits on double precison
async function fillGap(list: SortedList, start: integer, end: integer): Promise<boolean> {
    // get the values at both ends
    const startValue = start === 0 ? 1 : await list.getSortValue(start - 1);
    const endValue = await list.getSortValue(end);
    // set the value on the middle item
    const mid = Math.floor((start + end) / 2);
    const midValue = Math.floor((startValue + endValue) / 2);
    if (midValue === startValue || midValue === endValue) return false;
    await list.setSortValue(mid, midValue);
    // recurse on the remaining gaps on both sides of mid.
    if (mid > start) if (!(await fillGap(list, start, mid))) return false;
    if (mid + 1 < end) if (!(await fillGap(list, mid + 1, end))) return false;
    return true;
}

const round10Up = (x: integer) => Math.floor((x + 9) / 10) * 10;

/**
 * Set sort values in the `[start, end[` range of `list`, a sorted list.
 *
 * The sort values outside of the range (if any) must already be in increasing order
 *
 * The function will _fix the range_.
 * It will set the sort values for all items inside the range so that the sort values are strictly
 * increasing across the entire list.
 * Most of the time, the function will only set values on the items inside the range.
 * But, in the exceptional case where we get collisions on double values because of limited precision,
 * the function will modify the sort value on one or more items after the range.
 */
export async function setSortValues(
    list: SortedList,
    start: integer,
    end: integer,
    options?: { preserveOldValues?: boolean },
): Promise<void> {
    if (start < 0 || end > list.length) {
        throw new Error(`invalid range: start=${start}, end=${end}, length=${list.length}`);
    }
    if (start === 0 && end === list.length) {
        // Range is the entire list.
        // Just set the order values to 10, 20, 30, ... 10 * length
        for (let i = 0; i < list.length; i += 1) await list.setSortValue(i, 10 * (i + 1));
    } else if (end === list.length) {
        // Range is at the end of the list
        // we set the values by incrementing by 10 from start.
        // [0, 10, 15, 20, x, x, x] => [0, 10, 15, 20, 30, 40, 50]
        const startValue = await list.getSortValue(start - 1);
        for (let i = start; i < list.length; i += 1)
            await list.setSortValue(i, round10Up(startValue + 10 * (i - start + 1)));
    } else if (start < end) {
        // Range is a gap inside the list
        // We fill the gap by dichotomy
        // [10, 20, x, x, x, 30] => [10, 20, 22, 25, 27, 30]
        // If fillGap fails we try again with the end bumped by 100, if the override is allowed.
        // This recursion will always succeeds because we'll eventually hit the end of the list
        // (in real life it is extremely likely to succeed on the first recursion)
        if (!(await fillGap(list, start, end))) {
            if (options?.preserveOldValues) throw new Error('Cannot override the sort value');
            await setSortValues(list, start, end + 1);
        }
    }
    return voidPromise;
}
