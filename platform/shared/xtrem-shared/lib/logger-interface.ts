import { LogLevel } from './config';

export interface ProfilerCallback {
    success: (message?: string) => void;
    fail: (message?: string) => void;
}

/** Error handler type for `logger.do` */
export type LoggerErrorHandler<T> = (err: Error) => T;

/**
 * Callback to determine if a message should be ignored
 */
export type LoggerIgnoreCallback = (message: string) => boolean;

export interface LoggingOptions {
    lowThreshold?: number;
    highThreshold?: number;

    /**
     * The callback to determine if a message should be ignored
     */
    ignoreCallback?: LoggerIgnoreCallback;
}

export interface LoggerInterface {
    /**
     * Logs a message with the provided log level
     * @param logLevel
     * @param messageProvider
     */
    log(logLevel: LogLevel, messageProvider: () => string, options?: LoggingOptions): ProfilerCallback;
    /**
     * Logs a message with the 'info' level
     */
    info(messageOrCallback: string | (() => string), options?: LoggingOptions): ProfilerCallback;
    /**
     * Logs a message with the 'warn' level
     */
    warn(messageOrCallback: string | (() => string), options?: LoggingOptions): ProfilerCallback;
    /**
     * Logs a message with the 'verbose' level
     */
    verbose(messageProvider: () => string, options?: LoggingOptions): ProfilerCallback;
    /**
     * Logs a message with the 'debug' level
     */
    debug(messageProvider: () => string, options?: LoggingOptions): ProfilerCallback;
    /**
     * Logs a message with the 'error' level
     */
    error(messageOrCallback: string | (() => string), options?: LoggingOptions): ProfilerCallback;
    /**
     * result = do(fn, onError);
     *
     * Executes `fn()` silently.
     * But if `fn()` fails, logs the stacktrace and rethrows.
     *
     * `onError(err)` is an optional callback which will be invoked if `fn()` fails.
     * By default, `onError` rethrows the error, but you can rethrow a different error, typically
     * to mask low level details, or add some context to the error.
     * You can also use `onError` to stop the error propagation and return a special value.
     */
    do<T>(fn: () => T, onError?: LoggerErrorHandler<T>): T;
}
