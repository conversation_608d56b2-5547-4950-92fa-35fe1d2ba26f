/**
 * This file allows us to expose the registerSqlFunction function to client-side code that
 * does not have access to the xtrem-ts-to-sql package.
 *
 * We setup a dummy implementation of the registerSqlFunction function that will be replaced
 * when xtrem-core is imported.
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
let sqlRegistryHook = (_fullName: string, _fn: Function): void => {};

/**
 * Registers a function to the TS to SQL converter.
 *
 * @param fullName - The full name of the SQL function.
 * @param fn - The function to be registered.
 * @returns The registered function.
 */
export function registerSqlFunction(fullName: string, fn: Function): void {
    sqlRegistryHook(fullName, fn);
}

/**
 * Replaces the dummy implementation of registerSqlFunction with the real one.
 * @param hook
 */
export function setSqlRegistryHook(hook: (fullName: string, fn: Function) => void): void {
    sqlRegistryHook = hook;
}
