import { NodeDetails } from './ui-shared-types';

export interface WorkflowNode {
    key: string;
    title: string;
    description: string;
    color: string;
    icon: string;
    configurationPage: string;
    type: string;
    defaultConfig: string;
}

export interface WorkflowError {
    stepId: string;
    message: string;
}

export type WorkflowVariableType = NodeDetails['type'];

export interface WorkflowVariable {
    // Waning: any changes to this interface must be reflected in the
    // function convertVariableForSerialization (platform/system/xtrem-workflow/lib/client-functions/variable-utils.ts)
    title: string;
    path: string;
    type: WorkflowVariableType;
    node?: string;
    enumType?: string;
    enumValues?: string[];
    /**
     * Is the variable a custom field ?
     */
    isCustom?: boolean;
}

export type VariableFilter = (r: WorkflowVariable) => boolean;

export const variableFilters = {
    and:
        (...filters: VariableFilter[]) =>
        (r: WorkflowVariable) =>
            filters.every(f => f(r)),
    or:
        (...filters: VariableFilter[]) =>
        (r: WorkflowVariable) =>
            filters.some(f => f(r)),
    not: (filter: VariableFilter) => (r: WorkflowVariable) => !filter(r),

    any: () => true,

    isBoolean: (r: WorkflowVariable) => r.type === 'Boolean',
    isString: (r: WorkflowVariable) => r.type === 'String',
    isNumeric: (r: WorkflowVariable) => r.type === 'Decimal' || r.type === 'Float' || r.type === 'Int',
    isInteger: (r: WorkflowVariable) => r.type === 'Int',
    isDate: (r: WorkflowVariable) => r.type === 'Date',
    isDateTime: (r: WorkflowVariable) => r.type === 'DateTime',
    isReference: (nodeName?: string) => (r: WorkflowVariable) =>
        (r.type === 'Reference' ||
            r.type === 'IntReference' ||
            r.type === 'ExternalReference' ||
            r.type === 'IntOrString') &&
        (r.node === nodeName || nodeName == null),

    isAssignableTo(variable: WorkflowVariable): VariableFilter {
        switch (variable.type) {
            case 'Boolean':
                return variableFilters.isBoolean;
            case 'String':
                return variableFilters.isString;
            case 'Decimal':
            case 'Float':
                return variableFilters.isNumeric;
            case 'Int':
                return variableFilters.isInteger;
            case 'Date':
                return variableFilters.isDate;
            case 'DateTime':
                return variableFilters.isDateTime;
            case 'Reference':
                return variableFilters.isReference(variable.node);
            default:
                return variableFilters.any;
        }
    },
    isOld(variable: WorkflowVariable): boolean {
        return variable.path.startsWith('$old.');
    },
};
