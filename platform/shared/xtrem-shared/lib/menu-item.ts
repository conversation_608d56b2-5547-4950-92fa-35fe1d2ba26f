import type { IconType } from 'carbon-react/esm/components/icon/icon-type';

/**
 * Menu item interface that creates a group item in the navigation menu.
 */
export interface MenuItemDefinition {
    /** Unique ID, it must start with the package name */
    id: string;
    /** The title of the menu item */
    title: string;
    /** Priority that determines the order of the elements in its category. */
    priority: number;
    /** Used internally to store the package the menu item is declared in */
    packageName?: string;
}

export interface RootMenuItem extends MenuItemDefinition {
    /** Icon that represents on the root menu item entity */
    icon: IconType;
}

export interface SubMenuItem extends MenuItemDefinition {
    /** Parent menu item that this menu is categorized under */
    parentMenuItem: RootMenuItem | SubMenuItem;
}

export interface SitemapEntry extends Partial<RootMenuItem> {
    children?: SitemapEntry[];
    isPage: boolean;
    packageName?: string;
}
