import * as _ from 'lodash';
import { getRequestContextDetailsMessage } from './security';

export enum ValidationSeverity {
    test,
    info,
    warn,
    error,
    exception,
}

export interface Diagnosis {
    severity: ValidationSeverity;
    message: string;
    path: string[];
}

export interface HttpError {
    status: number;
    statusText?: string;
    body?: any;
}

export interface ErrorExtensions {
    code: string;
    diagnoses?: Diagnosis[];
}

export interface InteropErrorExtensions {
    http?: HttpError;
}

export abstract class BaseError extends Error {
    readonly extensions: ErrorExtensions;

    constructor(
        message: string,
        innerError?: Error,
        public path?: string[],
    ) {
        const replaced = message.replace(/&quot;/g, '"').replace(/&#x27;/g, "'");
        const options = innerError ? { cause: innerError } : undefined;
        super(replaced, options);

        this.extensions = {
            code: _.kebabCase(this.constructor.name),
        };
    }

    get innerError(): Error | undefined {
        return this.cause as Error | undefined;
    }

    // Override toJSON to avoid circular references
    toJSON(): any {
        return { message: this.message, extensions: this.extensions };
    }
}

export abstract class LocalizedError extends BaseError {
    getMessageAndDiagnosesText(diagnoses: Diagnosis[]): string {
        const messages = diagnoses
            .map(diagnose => (diagnose.path?.length ? `${diagnose.path}: ${diagnose.message}` : diagnose.message))
            .join('\n');
        return `${this.message}\n${messages}`;
    }
}

export abstract class ErrorWithDiagnoses extends LocalizedError {
    constructor(message: string, diagnoses: Diagnosis[], innerError?: Error) {
        super(message, innerError);
        if (diagnoses) {
            this.extensions.diagnoses = [...diagnoses];
        }
    }
}

/**
 * Thrown when a business rule is broken.
 * The message is localized, and reaches the end user.
 */
export class BusinessRuleError extends LocalizedError {}

/**
 * Thrown when the data provided by the user is faulty.
 * The message is localized, and reaches the end user.
 */
export class DataInputError extends LocalizedError {}

/**
 * Thrown if the query tries to reach unauthorized data.
 * The message is localized, and reaches the end user.
 */
export class AuthorizationError extends LocalizedError {}

/**
 * Timeout error, thrown if the time limit for an operation has been exceeded
 * For instance: GraphQl time limit.
 * The message is localized, and reaches the end user.
 */
export class TimeoutError extends LocalizedError {}

/**
 * A technical error that does not have a localized message.
 * The framework will log the message, with a stacktrace, and will return a generic localized message to the user.
 */
export abstract class NonLocalizedError extends BaseError {}

/**
 * Not a logical error nor an application error.
 * For instance: file not found or failed connection, or unknown.
 * Message not localized, not returned to user.
 */
export class SystemError extends NonLocalizedError {}

/**
 * The application was incorrectly configured.
 * For example, invalid parameter value in the xtrem-config.yml file.
 * Message not localized, not returned to user.
 */
export class ConfigurationError extends NonLocalizedError {}

/**
 * Special error to trigger a throw but no logging.
 */
export class SilentError extends NonLocalizedError {
    constructor() {
        super('');
    }
}

/**
 * Security errors are reserved for errors that are related to security threats.
 * Message not localized, not returned to user.
 */
export class SecurityError extends NonLocalizedError {
    constructor(message: string, innerError?: Error) {
        super(`[SECURITY] ${message}`, innerError);
    }

    securityMessage(context: any): string {
        const details = getRequestContextDetailsMessage(context.request);
        return (this.stack ?? this.message).replace(this.message, `${this.message} - ${details}`);
    }
}

/**
 * Logical error, independent from external environment and data.
 * For instance: failed assertion.
 * Message not localized, not returned to user.
 */
export class LogicError extends NonLocalizedError {}

/**
 * Unknown error, that we did not catch and wrap
 */
export class UnknownError extends NonLocalizedError {}

/**
 * An interop exchange failed.
 * The message is not localized.
 * It should not reveal sensitive information about the system (filenames, port numbers, etc.).
 * The innerError is the original error that caused the interop error. It will be logged but not returned to the user.
 */
export class InteropError extends ErrorWithDiagnoses {
    constructor(message: string, diagnoses: Diagnosis[], http?: HttpError, innerError?: Error) {
        super(`[InteropError] ${message}`, diagnoses, innerError);
        if (http) Object.assign(this.extensions, http);
    }
}

export class InterruptException extends Error {
    constructor() {
        super('Interrupt');
    }
}

/**
 * SuspendException is used to suspend the execution of a process.
 * It is not an error, but a signal to the caller that the process should be suspended.
 * The caller should not log this exception, but handle it gracefully.
 * This exception should only be used in very specific cases where we have tight control over the execution flow.
 */
export class SuspendException extends Error {
    constructor() {
        super('SuspendException');
    }
}

export function unwrapError(err: Error): Error & { code?: string } {
    const innerError = (err as any).innerError;
    return innerError ? unwrapError(innerError) : err;
}
