/**
 * Creates an empty with a null prototype to prevent from prototype pollutions
 * @returns an empty object
 */
export const createPlainObject = <T>(source?: object): T => {
    const obj = Object.create(null);
    return source == null ? obj : Object.assign(obj, source);
};

/**
 * Creates a dictionary object safe to prototype pollutions
 * @returns an empty dictionary object
 */
export const createDictionary = <T>(): Record<string, T> => Object.create(null);
