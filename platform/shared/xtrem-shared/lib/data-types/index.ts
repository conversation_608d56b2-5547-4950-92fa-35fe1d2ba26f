export type { decimal } from '@sage/xtrem-decimal';

const columnTypeNames = [
    'boolean',
    'enum',
    'short',
    'integer',
    'reference',
    'integerRange',
    'decimalRange',
    'date',
    'dateRange',
    'datetimeRange',
    'time',
    'datetime',
    'float',
    'decimal',
    'double',
    'binaryStream',
    'textStream',
    'string',
    'uuid',
    'byte',
    'binary',
    'json',
    'integerArray',
    'enumArray',
    'referenceArray',
    'stringArray',
] as const;

export type ColumnTypeName = (typeof columnTypeNames)[number];

export function isColumnTypeName(name: string | undefined) {
    return columnTypeNames.includes(name as any);
}

// GraphQL has a limit to use 32-bit max and min Integer:
export const MAX_INT_32 = 2 ** 31 - 1;
export const MIN_INT_32 = -(2 ** 31);

/**
 * Type for _id (number in xtrem, string in X3)
 */
export type IdType = string | number;
