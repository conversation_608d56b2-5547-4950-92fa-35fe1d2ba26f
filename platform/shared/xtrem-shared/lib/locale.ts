import { Dict } from './util';

export const locales = [
    'en-US',
    'en-GB',
    'fr-FR',
    'es-ES',
    'pt-PT',
    'pt-BR',
    'ar-SA',
    'de-DE',
    'it-IT',
    'pl-PL',
    'zh-CN',
] as const;

export type Locale = (typeof locales)[number];
export type LocalizeLocale = Locale | 'base';

export const supportedLocales: LocalizeLocale[] = ['base', ...locales];

export type LocaleFallbacks = {
    [key in LocalizeLocale]: LocalizeLocale[];
};

/**
 * A JSON object that contains localized texts, indexed by locale.
 */
export type LocalizedText = Dict<string>;

const commonFallback: LocalizeLocale[] = ['en-US', 'en-GB', 'base'];
export const localeFallbacks: LocaleFallbacks = {
    'en-GB': ['en-GB', 'en-US', 'base'],
    'en-US': commonFallback,
    'fr-FR': ['fr-FR', ...commonFallback],
    'es-ES': ['es-ES', ...commonFallback],
    'de-DE': ['de-DE', ...commonFallback],
    'it-IT': ['it-IT', ...commonFallback],
    'ar-SA': ['ar-SA', ...commonFallback],
    'zh-CN': ['zh-CN', ...commonFallback],
    'pl-PL': ['pl-PL', ...commonFallback],
    'pt-PT': ['pt-PT', 'pt-BR', ...commonFallback],
    'pt-BR': ['pt-BR', 'pt-PT', ...commonFallback],
    base: ['base'],
};

export interface LocalizeFunction {
    (key: string, _template: string, data?: object | any[], locale?: LocalizeLocale): string;
}

export interface LocalizeEnumFunction {
    (enumFullPathName: string, memberName: string): string;
}

/**
 * Extracts the text for the given locale from a LocalizedText object.
 * @param localizedText The JSON object that contains all the texts, indexed by locale.
 * @param locale The locale to extract the text for.
 */
export function getTextForLocale(
    localizedText: LocalizedText | undefined,
    locale: Locale | string | null,
): string | null {
    if (localizedText == null) return null;
    const localeToUse = locale || 'base';
    // locale is something like 'en-US'
    if (localizedText[localeToUse]) {
        // Note: do not consider empty string as a valid string
        return localizedText[localeToUse];
    }
    const match = localeToUse.match(/(\w\w)-\w\w/);
    if (match) {
        const shortLocale = match[1];
        // shortLocale is something like 'en'
        if (localizedText[shortLocale]) return localizedText[shortLocale];
        // Try to find a locale that starts with the short locale where a text is available
        // It could be 'en-GB' here
        const fallbackLocale = Object.keys(localizedText).find(
            key => key.startsWith(`${shortLocale}-`) && localizedText[key],
        );
        if (fallbackLocale) return localizedText[fallbackLocale];
    }
    if (localizedText.base) return localizedText.base;
    return null;
}

/**
 * Merge the text for the given locale with the existing translations.
 *
 * @param localizedText The translations for the different locales.
 * @param textForLocale The text for the current locale.
 * @param locale The current locale.
 * @returns The merged translations.
 */
export function mergeLocalizedText(
    localizedText: LocalizedText | undefined,
    textForLocale: string | null | undefined,
    locale: Locale | string | null,
): LocalizedText {
    if (!textForLocale) {
        // Note: we don't want to store empty strings in the JSON object
        return localizedText ?? {};
    }
    const localeToUse = locale || 'base';
    const merged = {
        ...(localizedText ?? {}),
        [localeToUse]: textForLocale,
    };
    if (localeToUse !== 'base') {
        // Add the base locale if it doesn't exist
        if (!merged.base) {
            merged.base = textForLocale;
        }
    }
    return merged;
}
