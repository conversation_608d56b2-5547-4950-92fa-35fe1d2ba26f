import { MakeNodeType } from './util';

export interface UploadedFile {
    _id: string | number;
    mimeType: string;
    filename: string;
    contentLength: number;
    status: string;
    uploadUrl: string;
    downloadUrl: string;
    kind: 'upload' | 'attachment';
}

export interface UploadedFileNode extends MakeNodeType<UploadedFile> {}

export interface AttachmentAssociation {
    _id: string | number;
    isProtected: boolean;
    description: string;
    title: string;
    attachment: UploadedFileNode;
}

export interface AttachmentAssociationNode extends MakeNodeType<AttachmentAssociation> {}
