import type { IconType } from 'carbon-react/esm/components/icon/icon-type';
import { IdType } from './data-types';

export type NotificationLevel = 'error' | 'warning' | 'info' | 'success' | 'approval';

export type NotificationActionStyle = 'primary' | 'secondary' | 'tertiary' | 'link';

export interface InitialNotificationAction {
    icon?: string;
    link: string;
    style: 'primary' | 'secondary' | 'tertiary' | 'link';
    title: string;
}

export type { IconType };

export interface NotificationAction extends InitialNotificationAction {
    _id: string;
}
export interface NotificationPayload {
    trackingId: string;
    topic?: string;
    status?: string;
    error?: string;
}
export interface InitialNotification {
    actions: Array<InitialNotificationAction>;
    description: string;
    icon: IconType;
    level: NotificationLevel;
    shouldDisplayToast: boolean;
    title: string;
    // The optional list of recipients (the _id of the users) to whom the notification is sent.
    // If not provided, the notification is sent to the current user.
    recipientsId?: IdType[];
    payload?: NotificationPayload;
}

export interface Notification extends InitialNotification {
    _createStamp: Date;
    _id: string;
    actions: Array<NotificationAction>;
    isRead: boolean;
}
