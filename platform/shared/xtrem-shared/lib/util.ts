import {
    camelCase,
    forEach,
    get,
    isArray,
    isEmpty,
    isNil,
    isObject,
    isPlainObject,
    lastIndexOf,
    mergeWith,
    range,
    snakeCase,
    startCase,
} from 'lodash';
import type { DeepPartial } from 'ts-essentials';
import { IsStrictlyAny } from './ui-shared-types';

export { DeepPartial };

const capitalize = (s: string) => s[0].toUpperCase() + s.substring(1);
export const pascalCase = (s: string) => capitalize(camelCase(s));

/**
 * Converts an identifier to a title
 * Lodash startCase does it but it capitalizes every word. We only want to capitalize the first one.
 */
export function titleCase(str: string | undefined) {
    const s = startCase(str);
    return s[0] + s.substring(1).toLowerCase();
}

/**
 * Type name for the graphql schema
 * @param fullName fully qualified name in the form of @vendor/package/ArtifactName. Example: @sage/x3-sales/SalesOrder
 */
export function schemaTypeName(fullName: string, suffix = '') {
    return pascalCase(
        fullName.split('/').slice(-1) + suffix, // Only keep the last part (example: SalesOrder)
    );
}

export function nameToSqlName(name: string): string {
    return (name[0] === '_' ? '_' : '') + snakeCase(name);
}

/** Useful to make OR condition between properties in our interface. If we put the two properties Typescript returns an a type error.
 * E.G RequireOnlyOne<interface, 'key1' | 'key2'> */
export declare type RequireOnlyOne<T, Keys extends keyof T = keyof T> = Pick<T, Exclude<keyof T, Keys>> &
    { [K in Keys]-?: Required<Pick<T, K>> & Partial<Record<Exclude<Keys, K>, undefined>> }[Keys];

/**
 * @TJS-additionalProperties true
 */
export interface Dict<T> {
    [key: string]: T;
}

/**
 * A JSON value (string, boolean, number, null, array, plain object)
 */
export type Json = string | boolean | number | null | Json[] | Dict<Json>;

export type MakeAllRequired<T> = { [P in keyof T]-?: MakeAllRequired<NonNullable<T[P]>> };

/**
 * Flattens an input object into a one-level deep object
 * having as keys dot-notated strings that represent object paths.
 * E.g.
 *
 * { a: 'a', b: { c: 'c', d: { e: 'e' } } } => { a: 'a', 'b.c': 'c', 'b.d.e': 'e' }
 *
 *
 * @export
 * @param {object} [obj={}] the input object
 * @param {string} [prefix=''] an optional prefix
 * @param {string} [suffix=''] an optional suffix
 * @returns {Dict<string>} a dot-notated object with paths as keys
 */
export function flat(obj: object = {}, prefix = '', suffix = ''): Dict<string> {
    const result: Dict<string> = {};
    const flatten = (collection: object, pre = '', suf = '') => {
        forEach(collection, (value, key) => {
            const path = `${pre}${key}${suf}`;
            if (isArray(value)) {
                flatten(value, `${path}[`, ']');
            } else if (isPlainObject(value)) {
                flatten(value, `${path}.`);
            } else {
                result[path] = value;
            }
        });
    };
    flatten(obj, prefix, suffix);
    return result;
}

export type Maybe<T> = T | undefined;

/**
 * Remove all the instrumentation code added by the code coverage
 * @param str
 */
export function removeCodeCoverageInstrumentation(str: string): string {
    // str might be something like:
    // - newMandatoryString(){cov_11t1afc2i1().f[1]++;cov_11t1afc2i1().s[8]++;return`new ${this.name1}`;}
    // - getValue(){return 2*((cov_nf3kkbb42().b[10][0]++,this.integerVal)??(cov_nf3kkbb42().b[10][1]++,0));}
    // We want to remove all the cov_xxxxx().x[y]++; (or variants with several indices and a comma at the end)
    return str.replace(/\bcov_(?:\w)+\(\)\.\w+(?:\[\d+\])+\+\+[;,]/g, '');
}

/**
 * Access status values
 * - authorized - user is authorized to access the property or operation
 * - unauthorized - user is unauthorized to access the property or operation
 * - unavailable - property or operation is disabled by a service option
 * - readonly - property is readonly
 * - inactive - operation is inactive
 */
export type AccessStatus = 'authorized' | 'unauthorized' | 'unavailable' | 'readonly' | 'inactive';

export type PromisifyProperties<T> = { [K in keyof T]: Promise<T[K] | T[K]> };

export interface ArrayMoveResult<R> {
    success?: boolean;
    data: R[];
}

const mergeCustomizer =
    (overrideArrays = false) =>
        <T = any>(first: T, second: T): any[] | undefined => {
            if (isArray(first) && isArray(second)) {
                return overrideArrays ? second : first.concat(second);
            }
            return undefined;
        };

export const deepMerge = <T = any>(
    first: T,
    second: T | DeepPartial<T>,
    useArrayAsStart = false,
    overrideArrays = false,
): T => {
    return <T>mergeWith(useArrayAsStart ? [] : {}, first, second, mergeCustomizer(overrideArrays));
};

export function arrayMoveMutable<T>(array: T[], fromIndex: number, toIndex: number) {
    const startIndex = fromIndex < 0 ? array.length + fromIndex : fromIndex;

    if (startIndex >= 0 && startIndex < array.length) {
        const endIndex = toIndex < 0 ? array.length + toIndex : toIndex;

        const [item] = array.splice(fromIndex, 1);
        array.splice(endIndex, 0, item);
    }
}

export function arrayMoveImmutable<T>(array: T[], fromIndex: number, toIndex: number) {
    const newArray = [...array];
    arrayMoveMutable(newArray, fromIndex, toIndex);
    return newArray;
}

export function arrayMovePreservingOrder<R extends Record<string, any>, K extends keyof R>({
    fromIndex,
    toValue,
    orderByKey,
    data,
}: {
    fromIndex: number;
    toValue: R[K];
    orderByKey: K;
    data: R[];
}): ArrayMoveResult<R> {
    const insertIndex = lastIndexOf(
        data.map(d => d[orderByKey]),
        toValue,
    );
    const { canSort } = data[fromIndex]?.property?.data ?? {};
    const fromGroup = data[fromIndex].group;
    const fromGroupElements = data.filter(d => d[orderByKey] === fromGroup);
    const sameGroupLength = data.filter(d => d[orderByKey] === toValue).length;
    const indexInGroup = fromGroupElements.findIndex(element => element === data[fromIndex]);

    const fromCanSort = get(fromGroupElements[1], 'property.data.canSort');

    const isMoveAllowed =
        !(sameGroupLength === 0 && typeof canSort === 'boolean' && !canSort) &&
        !(indexInGroup === 0 && fromGroupElements.length > 1 && typeof fromCanSort === 'boolean' && !fromCanSort);

    if (!isMoveAllowed) {
        return { success: false, data };
    }
    const newData = data.map((d, idx) => (idx === fromIndex ? { ...d, [orderByKey]: toValue } : d));
    const getIndex = () => Math.min(fromIndex < insertIndex ? insertIndex : insertIndex + 1, newData.length - 1);
    let toIndex;
    if (insertIndex === -1) {
        toIndex = newData.length - 1;
    } else if (sameGroupLength > 1) {
        toIndex = getIndex();
    } else {
        toIndex = insertIndex;
    }

    return { success: true, data: arrayMoveImmutable(newData, fromIndex, toIndex) };
}

export type KeysOfValue<T, Condition> = {
    [K in keyof T]: T[K] extends Condition ? K : never;
}[keyof T];

export function arrayOrderContiguous<R extends { _id: string }, K extends KeysOfValue<R, number>>({
    orderedData,
    key,
}: {
    orderedData: R[];
    key: K;
}): {
    columnId: K;
    rowId: string;
    value: R[K];
    rowData: R;
}[] {
    if (isEmpty(orderedData)) {
        return [];
    }
    const changes: {
        columnId: K;
        rowId: string;
        value: R[K];
        rowData: R;
    }[] = [];

    let lastChangedValue: number | null = null;
    let lastDiff: number | null = null;

    const group = new Set<number>();

    // calculate value to assign and track it
    function assignAndTrack(value: number) {
        if (group.has(value)) {
            return value;
        }
        const sequence = range(0, value + 1);
        const validElement = sequence.find(i => !group.has(i));
        const element = validElement ?? sequence[0];
        group.add(element);
        return element;
    }

    const startIndex = (orderedData[0][key] as number) === 0 ? 1 : 0;
    group.add(0);

    // loop from second element and compare to previous
    for (let i = startIndex; i < orderedData.length; i += 1) {
        const currentItem = orderedData[i];
        const previousItem = orderedData[i - 1] || { [key]: -1 };
        const diff = (currentItem[key] as number) - (previousItem[key] as number);

        let newItem: R | undefined;
        if (!isNil(lastChangedValue) && !isNil(lastDiff) && (currentItem[key] as number) >= lastChangedValue) {
            newItem = { ...currentItem, [key]: assignAndTrack((currentItem[key] as number) - lastDiff) };
        } else if (diff < 0) {
            newItem = { ...currentItem, [key]: assignAndTrack(previousItem[key] as number) };
        } else if (diff > 1) {
            newItem = { ...currentItem, [key]: assignAndTrack((previousItem[key] as number) + 1) };
            lastChangedValue = currentItem[key] as number;
            lastDiff = diff - 1;
        } else {
            // no need to edit currentItem, just track its value
            group.add(currentItem[key] as number);
            lastChangedValue = null;
        }
        // assign newItem if changed
        if (!isNil(newItem)) {
            orderedData[i] = newItem;
            changes.push({
                columnId: key,
                rowId: newItem._id,
                value: newItem[key],
                rowData: newItem,
            });
        }
    }
    return changes;
}

export function remapObjectByKey(obj: any, keyToRemap: string, remapFunction: (value: any) => any): any {
    // Check if obj is an object
    if (typeof obj !== 'object' || obj === null) {
        return obj; // If not, return as is
    }

    // Check if obj is an array
    if (Array.isArray(obj)) {
        // If obj is an array, iterate over each element
        return obj.map((item: any) => remapObjectByKey(item, keyToRemap, remapFunction));
    }

    // Iterate over each key-value pair in the object
    return Object.keys(obj).reduce((acc: any, key: string) => {
        // If the current key is the one we want to remap, update its value
        if (key === keyToRemap) {
            acc[key] = remapFunction(obj[key]);
        } else {
            // If the current value is an object or array, recursively call remapKey
            acc[key] = remapObjectByKey(obj[key], keyToRemap, remapFunction);
        }
        return acc;
    }, {});
}

export type MakeNodeType<T> = {
    [Prop in Exclude<keyof T, '_id'>]: T[Prop] extends Promise<{ _sourceId: Promise<string>; _etag: Promise<string> }>
    ? Promise<MakeNodeType<T[Prop]>>
    : T[Prop] extends Promise<any>
    ? T[Prop]
    : Promise<T[Prop]>;
} & { _id: string | number };

export function defaultStringCompare(s1: string, s2: string): number {
    return s1.localeCompare(s2, 'en-US');
}

export type StringKeyMapper<T> = { [K in keyof T]: K extends string ? K : K extends number ? `${K}` : never }[keyof T];
export type KeyCaster<T, M> = keyof { [K in M as keyof T]: any } & string;
export type KeysOf<T> =
    IsStrictlyAny<T> extends true
    ? KeyCaster<T, StringKeyMapper<T>>
    : T extends readonly unknown[]
    ? number extends T['length']
    ? KeyCaster<T[number], StringKeyMapper<T>>
    : keyof T & `${number}`
    : T extends object
    ? KeyCaster<T, StringKeyMapper<T>>
    : never;

export function objectKeys<T = any>(obj: T) {
    return isObject(obj) ? (Object.keys(obj) as KeysOf<T>[] satisfies (keyof T)[]) : [];
}

export type NotAny<T> = T[] extends true[] ? T : T[] extends false[] ? T : never;

export type AssertMatch<T, Expected> = NotAny<T extends Expected ? true : false>;

class ExtraPropertiesError<T> {
    protected never!: T;
}

export type Exact<T1, T2> = T1 extends T2
    ? Exclude<keyof T1, keyof T2> extends never
    ? T1
    : ExtraPropertiesError<T2>
    : never;

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export function strictTypeMatch<T, Expected>(_arg: AssertMatch<T, Exact<T, Expected>>): void { }
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export function typeMatch<T, Expected>(_arg: AssertMatch<T, Expected>): void { }

export interface Unit {
    symbol?: string;
    id?: string;
    decimalDigits?: number;
}
