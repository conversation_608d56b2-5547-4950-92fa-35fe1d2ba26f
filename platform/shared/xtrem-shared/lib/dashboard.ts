import { integer } from './set-sort-values';

interface ItemPosition {
    x: integer;
    y: integer;
    breakpoint: 'xxs' | 'xs' | 'sm' | 'md' | 'lg';
    w: integer;
    h: integer;
}
export interface DashboardItemPosition {
    _id: string;
    positions: ItemPosition[];
}
export interface DashboardItem extends DashboardItemPosition {
    _id: string;
    type: string;
    settings: any | null;
}

export interface Dashboard {
    _id: string;
    title: string;
    description?: string;
    icon?: string;
    children: DashboardItem[];
    isTemplate?: boolean;
}
