import { Dict } from './util';

export interface XtremOptions {
    options?: string[];
    isHidden?: boolean;
    isReleased?: boolean;
    sqlSchemaVersion?: string;

    /**
     * Marker for packages that should be inactive by default.
     * We use a negative marker here as the default is to be active by default and we want to mark the exceptions.
     */
    isInactiveByDefault?: boolean;

    /**
     * Can the nodes in this package be extended ?
     */
    isSealed?: boolean;

    /** Is the package the main package of an application? */
    isMain?: boolean;

    /** The app name. Must be set in the main package */
    appName?: string;

    /**
     * Is the package a platform package (one of the platform/system packages)
     */
    isPlatform?: boolean;
    /**
     * Is the package a service with its own queue.
     */
    isService?: boolean;

    /**
     * The package is a service or add-on or has the hasListeners xtrem attribute supplied as true in the package.json
     */
    hasListeners?: boolean;

    /** The default SQS queue name for this package */
    queue?: string;

    isFrontEndApp?: boolean;

    /** The list of locales that are available. */
    locales?: string[];
}

/**
 * @disabled_internal
 */
export interface PackageJsonAbout {
    name: string;
    version: string;
    author: string;
    license: string;
    description: string;
    buildStamp?: string;
    appName?: string;
}

/**
 * @disabled_internal
 */
export interface PackageJsonFile extends PackageJsonAbout {
    dependencies: Dict<string>;
    peerDependencies: Dict<string>;
    devDependencies: Dict<string>;
    /**
     * Xtrem internal attributes.
     * Every package that contains nodes must have this attribute, even declared as an empty object {}
     */
    xtrem?: XtremOptions;
    // Indicates if the package is a xtrem client plugin
    xtremPlugin?: boolean;
    // Link to the main index.js. Sth like 'build/index.js';
    main: string;
    // Exports key for esm packages
    exports: Dict<unknown>;
    // Package type
    type: string;

    // Is this package the main package of a unit-tests session ? */
    isMainUnitTest: boolean;
}

export function getPackageName(packageJson: PackageJsonFile): string {
    return (packageJson.xtrem as Dict<string>)?.packageName || packageJson.name;
}

export function getPackageQueueName(packageJson: PackageJsonFile): string {
    if (!packageJson.xtrem?.queue) {
        throw new Error(
            `No queue name defined for package ${packageJson.name}, please set the queue property of the xtrem section in the package.json file.`,
        );
    }
    return packageJson.xtrem?.queue;
}

/**
 * Limits the length of an SQS queue name to 75 characters, 80 with the .fifo extension.
 * If the name is already 75 characters or less, it is returned as is.
 * If the name is longer than 75 characters, it is truncated and appended with "---" in the middle.
 * @param name - The original queue name.
 * @returns The modified queue name.
 */
export function limitSqsQueueName(name: string): string {
    // limit is 80 with .fifo suffix. So limit to 75
    if (name.length <= 75) return name;
    return `${name.substring(0, 36)}---${name.substring(name.length - 36)}`;
}
