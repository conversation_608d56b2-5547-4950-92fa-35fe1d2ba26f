import * as handlebars from 'handlebars';
import type { BuiltInParserName, LiteralUnion } from 'prettier';
import * as prettierPluginBabel from 'prettier/plugins/babel';
import * as prettierPluginEstree from 'prettier/plugins/estree';
import * as prettierPluginHtml from 'prettier/plugins/html';
import * as prettierPluginPostCss from 'prettier/plugins/postcss';
import * as prettier from 'prettier/standalone';

// const handlebarsIntl = require('handlebars-intl');
// handlebarsIntl.registerWith(handlebars);

function formatGraphQInputData(data: any, options?: { withoutBraces: boolean }): string {
    if (!data || typeof data !== 'object') return JSON.stringify(data);
    if (Array.isArray(data)) return `[${data.map(value => formatGraphQInputData(value))}]`;
    const inner = Object.entries(data)
        .map(([key, value]) => `${key}:${formatGraphQInputData(value)}`)
        .join();
    return options?.withoutBraces ? inner : `{${inner}}`;
}

/**
 * `inputParameters` handlebars helper: formats `parameters` as a GraphQl input object.
 */
handlebars.registerHelper('inputParameters', (data: any) => {
    return new handlebars.SafeString(formatGraphQInputData(data.data.root.properties));
});

/**
 * `inputData` handlebars helper: formats `parameters` as a list of GraphQl input parameters
 * Same as `inputParameters` but without braces around the result.
 */
handlebars.registerHelper('inputData', (data: any) => {
    return new handlebars.SafeString(formatGraphQInputData(data.data.root.properties, { withoutBraces: true }));
});

export /**
 * Handlebars helper
 *
 * @param {string} template the template to be used by Handlebars
 * @param {*} [data] data object containing all variables to be used by Handlebars
 * @returns the result of applying Handlebars to the given template with the given variables and all variables needed by the template
 */
const handleBars = (template: string, data?: any) => {
    return {
        result: handlebars.compile(template, { knownHelpers: { helperMissing: true } })(data),
        variables: getHandleBarsVariables(template),
    };
};

/**
 * Gets all the variables used in a Handlebars template
 *
 * @param {string} template Handlebars template
 * @returns {string[]} all variables needed by the given template
 */
const getHandleBarsVariables = (template: string): string[] => {
    const ast = handlebars.parse(template);
    return Object.values(ast.body)
        .filter(curr => curr.type === 'MustacheStatement')
        .map(curr => (curr as any).path.original);
};

export const format = (literal: string, locale: string, data: object | any[] = {}, noEscape = false) => {
    // Resolve arguments if they are provided
    if (Object.keys(data).length > 0) {
        const delegate = handlebars.compile(literal, { noEscape });
        const runtimeOptions = { data: { locale } } as any;
        return delegate(data, runtimeOptions);
    }
    return literal;
};

export /**
 * Returns a Javascript object in dot notation
 *
 * @param {*} input an object
 * @param {string} [prefix=''] a string prefix
 * @param {*} [target={}] a target object
 * @returns {*} the target object in dot notation
 */
const dotNotate = (input: any, prefix = '', target: any = {}): any => {
    Object.keys(input).forEach(key => {
        const val = input[key];
        if (val !== null && typeof val === 'object') {
            dotNotate(input[key], `${prefix + key}.`, target);
        }
        target[prefix + key] = val;
        return val;
    });

    return target;
};

/**
 * Parses an object with JSON.parse. If JSON is not valid it returns defaultValue.
 *
 * @param {*} input
 * @returns {*}
 */
export const tryJsonParse = (input: string, defaultValue = undefined): any => {
    try {
        return JSON.parse(input);
    } catch (e) {
        return defaultValue;
    }
};

export const isJson = (input: string): boolean => {
    return tryJsonParse(input) !== undefined;
};

/**
 * Code-formats a string using prettier standalone
 *
 * @param {string} input
 * @param {BuiltInParserName} parser
 * @returns {string}
 */
export const prettify = async (
    input: string,
    parser: Extract<LiteralUnion<BuiltInParserName>, 'html' | 'css' | 'json'>,
): Promise<string> => {
    try {
        return await prettier.format(input, {
            parser,
            plugins: [prettierPluginBabel, prettierPluginEstree, prettierPluginHtml, prettierPluginPostCss],
            tabWidth: 2,
        });
    } catch (e) {
        return input;
    }
};
