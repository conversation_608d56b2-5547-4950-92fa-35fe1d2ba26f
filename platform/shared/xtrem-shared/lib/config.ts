import { CommonConnectionOptions, SecureContextOptions, TlsOptions } from 'tls';
import { Dict } from './util';

export type TlsConnectionOptions = SecureContextOptions & CommonConnectionOptions;

export interface AddOnConfig {
    folder: string;
}

export interface SqlConfig {
    hostname: string;
    port?: number;
    /** Read-Only db replica hostname */
    readonlyHostname?: string;
    /** Read-Only db replica port */
    readonlyPort?: number;
    database: string;
    user: string;
    password: string;
    sysDatabase?: string;
    sysUser?: string;
    sysPassword?: string;
    poolMaxIdleSeconds?: number;
    /** Number of times to retry SQL connection - default 3 */
    connectionMaxRetries?: number;
    /** Milliseconds to wait before retrying connection - default 2000 */
    connectionRetryMillis?: number;
    /** Number of transactions per connection before closing to cycle connections - default 7500 */
    maxUses?: number;
    /** Pool size - default 20 */
    max?: number;
    /** Should all the $xx parameters be replaced by their value ? */
    mapArgsInLogs?: boolean;
    /** Max number of retries on conflicts between transactions */
    maxRetriesOnTransactionConflicts?: number;
    /** Subscriber config */
    subscriber?: SubscriberConfig;
    /** SSL config */
    ssl?: boolean | TlsConnectionOptions;
    /** Size of cache of SQL statements - default 1000 */
    sqlStatementCacheSize?: number;
    /** Number of milliseconds before a client is terminated if it is idle - default 10000 */
    idleTimeoutMillis?: number;
    /** Number of milliseconds to wait before timing out when connecting a new client - default 5000 */
    connectionTimeoutMillis?: number;
    /** Number of milliseconds before a statement in query will time out, default is no timeout.
     * https://node-postgres.com/apis/client
     * https://stackoverflow.com/questions/59155572/how-to-set-query-timeout-in-relation-to-statement-timeout
     */
    statementTimeoutMillis?: number;
    /** Max number of attempts to execute a SQL statement error */
    maxTries?: number;
    /** Delay before retrying SQL statement on error */
    delayBeforeRetry?: number;
    /** Maximum subquery depth that the ts-to-sql converter accepts */
    maxSubQueryDepth?: number;
}

/**
 * Configuration for SQL prefetch
 */
export interface PrefetchConfig {
    /** Is prefetch disabled */
    isDisabled?: boolean;

    /**
     * Do we log the SQL counters?
     * Counters are collected for each transaction, and logged at the end of the transaction.
     * Counters are also collected globally, and logged every 5 (configurable) seconds.
     */
    logCounters?: boolean;

    /** Number of seconds between two logs of the global SQL counters */
    logCountersIntervalInSeconds?: number;

    /** Do we log SQL statements (only verb + node name) */
    logStatements?: boolean;

    /**
     * Node names for which we are logging SQL sql statements
     * Entries prefixed by ~ are interpreted as regex
     */
    spiedNodeNames?: string[];
}

export interface SubscriberConfig {
    /**
     * Interval in ms to run a trivial query on the DB to see if
     * the database connection still works.
     * Defaults to 30s.
     */
    paranoidChecking?: number | false;
    /**
     * How much time to wait between reconnection attempts (if failed).
     * Can also be a callback returning a delay in milliseconds.
     * Defaults to 500 ms.
     */
    retryInterval?: number | ((attempt: number) => number);
    /**
     * How many attempts to reconnect after connection loss.
     * Defaults to no limit, but a default retryTimeout is set.
     */
    retryLimit?: number;
    /**
     * Timeout in ms after which to stop retrying and just fail. Defaults to 3000 ms.
     */
    retryTimeout?: number;
}

export interface StorageConfig {
    /** Is storage managed by an external storage engine (not PostgreSQL) */
    managedExternal?: boolean;
    /** pool size, for external storage engines (postgres uses storage.sql.max) */
    maxConnections?: number;
    /** Postgres configuration */
    sql?: SqlConfig;
    /** SQL prefetch configuration */
    prefetch?: PrefetchConfig;
}

export interface SystemConfig {
    /** a comma separated list of nat IP addresses */
    natIpAdresses?: string;
    /** interval of dns cache in seconds (default to 30) */
    dnsCache?: {
        /** enable dns cache (default to true) */
        enable: boolean;
        /** time to live in seconds (default to 30) */
        ttl: number;
        /** maximum number of entries to store in the cache (default to 1000) */
        cachesize: number;
    };
}

/**
 * Configuration of debug metrics
 */
type DebugMetricsOptions = Dict<boolean>;

export type LogLevel = 'off' | 'error' | 'warn' | 'info' | 'verbose' | 'debug';

export interface LogDomain {
    level: LogLevel;
}

export interface LogOptions {
    noColor?: boolean;
    json?: boolean;
}

export interface LogsConfig {
    filenamePrefix?: string;
    outputFolder?: string;
    domains?: Dict<LogDomain>;
    /** Are all logs disabled? */
    disabled?: boolean;
    /** Are all logs disabled when running tests? */
    disabledForTests?: boolean;
    options?: LogOptions;
}

export type DeploymentMode = 'development' | 'production';

export interface SizeLimits {
    maxStreamSize?: string; // string like 100mb
    maxRequestSize?: string;
    maxUploadSize?: string;
}

export interface SecurityConfig {
    loginUrl?: string;
    redirectUrl?: string;
    jwksUrl?: string;
    issuer?: string;
    audience?: string;
    renewalUrl?: string;
    renewalThreshold?: number;
    enableAutoRefresh?: boolean;
    services?: SecurityServicesConfig;
    tls?: {
        extraCaFiles?: string | string[];
    };
    sizeLimits?: SizeLimits;
    jsEval?: {
        timeoutInMillis?: number;
    };
}

export type AppsConfig = Dict<AppConfig>;

export interface RouteConfig {
    response?: {
        minDelayMillis?: number;
        maxDelayMillis?: number;
    };
}

export interface AppConfig {
    appUrl: string;
    interopUrl: string;
    metricsUrl?: string;
    version?: string;
    interopRoutes?: Dict<RouteConfig>;
}

export interface UiConfig {
    include?: string[];
    exclude?: string[];
}

interface StrictTransportSecurityOptions {
    maxAge: number;
    includeSubDomains: boolean;
    preload: boolean;
}
export interface SecurityServicesConfig {
    tokenInvalidation?: { active?: boolean };
    helmet?: {
        disabledOptions?: {
            csp: boolean;
            hsts: boolean;
        };
        csp?: {
            directives?: Dict<null | Iterable<string>>;
        };
        hsts?: boolean | StrictTransportSecurityOptions;
    };
    tooBusy?: {
        disabled?: boolean;
        maxLagInMillis?: number; // the max latency threshold
        intervalInMillis?: number; // the check interval
        smoothingFactor?: number; // the smoothing factor, see https://en.wikipedia.org/wiki/Exponential_smoothing
        retryAfterInSeconds?: number;
    };
}

export interface GraphQlConfig {
    isReadonly?: boolean;
    maxNodesPerPage?: number;
    keepLocalConfig?: boolean;
    /* Defines the time in seconds used to limit the lifespan of a graphql query */
    timeLimitInSeconds?: number;
}

export interface ClusterConfig {
    numberOfForkedProcesses: number;
}

export interface S3Config {
    accessKey: string;
    secret: string;
    region?: string;
}

export interface InteropRemoteQueryConfig {
    /** Maximum number of nodes per page */
    maxNodesPerPage?: number;
    /** maximum number of nodes per query result */
    maxNodesPerQuery?: number;
}

export interface InteropConfig {
    /** handlebars template for queue URL (cluster V2) */
    queueUrlTemplate?: string;

    /** handlebars template for dead letter queue URL (cluster V2) */
    queueUrlDeadLetterTemplate?: string;

    queues: Dict<QueueConfig>;
    /** size of the receiving notification funnel */
    concurrentNotificationsLimit?: number;
    /** size of the receiving message funnel */
    concurrentMessagesLimit?: number;

    /** interval of monitoring the listening queues in seconds (default to 1) */
    listenerMonitoringSeconds?: number;

    /** interval of notifications pooling in seconds (default to 1) */
    routingPollingSeconds?: number;

    /** maximum number of notifications pooling (default to 3)*/
    routingReadCount?: number;

    /** maximum number of retries trying to receive messages from SQS queue before error (default to 3) */
    receiveRetryCount?: number;

    /** interval of retries in seconds (default to 1) */
    receiveRetrySeconds?: number;

    /** interval to wait when polling receive queue(default to 1) */
    receivePollingSeconds?: number;

    /** time in seconds received message is made invisible on SQS (default 30 seconds) */
    messageVisibilitySeconds?: number;

    /** lower bound list size for the messages that are in progress (default to 6) */
    receiveLowerBound?: number;

    /** upper bound list size for the messages that are in progress (default to 11) */
    receiveUpperBound?: number;

    /** maximum number of retries trying to receive messages from SQS queue before error (default to 3) */
    sendRetryCount?: number;

    /** interval of retries in seconds (default to 90) */
    sendRetrySeconds?: number;

    /** Number of SQS groups per tenant user (default to 5).
     * A given tenant user cannot have more than this number of SQS messages being processed simultaneously*/
    sqsGroupsPerTenantUser?: number;

    /** heartbeat rate to refresh list of live containers */
    heartbeatSeconds?: number;

    /** interval of apps health check in seconds (default to 60) */
    appsHealthCheckSeconds?: number;

    /** timeout in seconds for the interop graqhql request to be completed (default to 120) */
    graphqlTimeLimitInSeconds?: number;

    /** elasticmq endpoint */
    devEndpoint?: string;

    /** remote query configuration */
    remoteQuery?: InteropRemoteQueryConfig;

    /** The time in seconds used to refresh the cache of SysNotificationState */
    runningJobListTtlInSeconds?: number;

    // see later for routing
    // routing: any;
}

/**
 * The interfaces for WebSockets will be refactored at a later time.
 */
export interface WebSocketResponseConfig {
    restResponseEndpoint: string;
    wsEndPoint: string;
    authToken: string;
}

export interface WebSocketRequestConfig {
    source: string;
    websocketQueueUrl: string;
}

export interface WebSocketConfig {
    dynamoDbTableName?: string;
    request?: WebSocketRequestConfig;
    response?: WebSocketResponseConfig;
}

export interface NewRelicConfig {
    trustKey?: string;
    accountId?: string;
    licenceKey?: string;
    applicationId?: string;
}

export interface QueueConfig {
    url: string;
    region?: string;
    /** size of the queue funnel */
    concurrentLimit?: number;
    /** time in seconds received message is made invisible on SQS (default 30 seconds) */
    messageVisibilitySeconds?: number;

    /** interval to wait when polling receive queue(default to 1) */
    receivePollingSeconds?: number;

    /** lower bound list size for the messages that are in progress (default to 6) */
    receiveLowerBound?: number;

    /** upper bound list size for the messages that are in progress (default to 11) */
    receiveUpperBound?: number;
}

export interface AwsConfig {
    region: string;
}

export type ServiceOptionStatus = 'workInProgress' | 'experimental' | 'released';

export interface ServiceOptions {
    level: ServiceOptionStatus;
}

export interface UpgradeOptions {
    activateNewPackages?: boolean;
    /** associated with the CLI upgrade option --prod this option makes it possible to skip
     * the playing of recorded SQL files.*/
    upgradeOnly?: boolean;

    /**
     * Temp hack for upgrade on showcase (https://jira.sage.com/browse/XT-23045)
     * Should all the CSV files from the SETUP layer be reloaded at the end of the upgrade ?
     * This will bypass the lookup from the git repo and reload all the **setup** CSV files
     */
    fullReloadOfSetupLayer?: boolean;
}

export interface WorkerConfig {
    // The number of worker per request source, default is 2
    workersPerRequestSource?: number;
}

export interface ServerConfig {
    port?: number;
    ssl?: TlsOptions;
    metricsPort?: number;
    interopPort?: number;
    /** the factor to apply to the max value of the sql config in order to compute the final request funnel size */
    requestFunnelSizeFactor?: number;
    // If multi-workers are enabled, worker related config can be supplied
    worker?: WorkerConfig;
}

export interface AuthenticationServiceConfig {
    interopUrl: string;
    ssl: Pick<TlsOptions, 'key' | 'cert' | 'ca'>;
}

/**
 * The config used to start the Authentication service container in prod-ui mode.
 *
 * The values for <clientId> and <clientSecret> can be found in Keeper (Global XTreem/Authentication service/Authentication service)
 */
export interface AuthenticationContainerConfig {
    /**
     * The CLIENT_ID to start the authentication service container
     */
    clientId: string;
    /**
     * The CLIENT_SECRET to start the authentication service container
     */
    clientSecret: string;
}

export interface S3StorageConfig {
    /**
     * For presigned URLs, we need this when adding an exception to CSP for uploading of files by the client
     * Example: https://xtrem-dev-eu-showcase.s3.eu-west-1.amazonaws.com
     */
    s3BucketUrlPrefix?: string;
    /** Cluster's S3 bucket name used to pass to file storage instance, to upload/download tenant specific files */
    s3ClusterBucket?: string;

    /** The (optional) folder (s3ClusterBucket) from where the upgrade metrics should be uploaded */
    s3UpgradeMetricsFolder?: string;

    localBasePath?: string;
}

export interface PendoOptions {
    /** pendo subscription id */
    subscriptionId?: string;
    /** pendo tag to identify cluster type */
    clusterTag?: string;
    /** The API key */
    apiKey?: string;
}

export interface ImportCsvOptions {
    chunkSize?: number;
}

export interface ExportCsvOptions {
    maxRetryUploadedFileCreation?: number;
    chunkSize?: number;
}

export interface ReportOptions {
    maxTotalPages?: number;
    /** The maximum time in milliseconds to wait for the browser to start. Pass 0 to disable it. (default to 180000) */
    browserTimeout?: number;
    /** The maximum time for individual protocol calls in milliseconds (default to 600000) */
    protocolTimeout?: number;
    pageOpeningTimeout?: number;
    /** The maximum time in milliseconds for page PDF transformation. Pass 0 to disable it. (default to 120000) */
    pdfTransformationTimeout?: number;
}

export interface WorkflowOptions {
    /** Delay after which we consider a process as killed if it has not updated its state */
    unresponsiveDelayInSeconds?: number;
    /** Max number of processes that we try to resume at once */
    captureFetchSize?: number;
    /** Minimum wait time when capturing processes */
    capturePollingMinSeconds?: number;
    /** Maximum wait time when when capturing processes */
    capturePollingMaxSeconds?: number;
}

export interface CopilotConfig {
    /** Copilot sage id client id */
    clientId: string;
    /** Copilot sage id client secret */
    clientSecret: string;
    /** Copilot sage id audience */
    audience: string;
    /** oauth url */
    oauthEndpointUrl: string;
    /** Copilot service url */
    serviceUrl: string;
    /**
     * Client name passed to the GMS Service e.g. sdmo_v1
     */
    gmsClient?: string;
    /**
     * ttl for the access code in minutes
     */
    accessCodeLifeTimeInMinutes?: number;
}

export interface Config {
    /** Visible product name, used in application header and metadata fields. */
    productName?: string;
    /** Path of config file name that we are extending */
    extends?: string;
    /** App name for this container in a multi-app infrastructure */
    app?: string;
    /** Public url of the app on this container */
    publicAppUrl?: string;
    endpoint?: string;
    server?: ServerConfig;
    storage?: StorageConfig;
    system?: SystemConfig;
    logs?: LogsConfig;
    settings?: Dict<any>;
    packages?: Dict<any>;
    tenantId?: string;
    email?: string;
    user?: string;
    /** Login is the EM login */
    login?: string;
    /** scope of api call */
    scope?: string;
    /** application id registered in api gateway */
    applicationId?: string;
    /** application name registered in api gateway */
    applicationName?: string;
    deploymentMode?: DeploymentMode;
    security?: SecurityConfig;
    /** apps, from apps.yml (next to xtrem-config.yml or under /infra) */
    apps?: AppsConfig;
    uiConfigPath?: string;
    ui?: UiConfig;
    textServerUrl?: string;
    graphql?: GraphQlConfig;
    noUi?: boolean;
    /** Disables caching the index.html template, it is practical when working on changes for xtrem-standalone in watch mode */
    disableUiTemplateCache?: boolean;
    /** Deploy the production UI application instead of the developer consumer mock */
    prodUi?: boolean;
    /** The folder where the config was loaded from */
    originFolder?: string;
    // The public URL of the AWS gateway (Mindo will send it in x-etna-config headers)
    baseUrl?: string;
    /** interval of error monitoring in seconds (default to 3600) */
    errorMonitoringInterval?: number;
    /** number of errors during the monitoring interval that will cause a process exit (default to 10) */
    errorMonitoringThreshold?: number;
    /** Licence key for ag-grid */
    agGridLicenceKey?: string;
    cluster?: ClusterConfig;
    interop?: InteropConfig;
    webSocket?: Dict<WebSocketConfig>;
    s3?: Dict<S3Config>;
    aws?: AwsConfig;
    clusterId?: string;
    asyncContextTableName?: string;
    xtremDeploymentCoreTableName?: string;
    authentication?: AuthenticationServiceConfig;
    /**
     * The configuration to start the authentication service container in prod-ui mode
     */
    authenticationContainer?: AuthenticationContainerConfig;
    operatorUserHashSecret?: string;

    s3Storage?: S3StorageConfig;
    /** Salt that is used to create anonym unique user and tenant IDs */
    telemetrySalt?: string;

    /** URL of the documentation service where user help pages are located */
    documentationServiceUrl?: string;

    /** Configuration properties used for client error reporting and benchmarking */
    newRelic?: NewRelicConfig;

    /** Content types allowed for text streams (regex list) */
    textStreamContentTypes?: string[];

    /** text stream length that will be used to check if a text stream is lazy loaded */
    textStreamLazyLoadLength?: number;

    /** Content types allowed for binary streams (regex list) */
    binaryStreamContentTypes?: string[];

    /** The level at which we will be validating the package version differences */
    semVerCompatibilityLevel?: 'major' | 'minor' | 'patch';

    /** Ignore vendor protection rule on data update */
    ignoreVendorProtection?: boolean;

    /** The level at which service options are displayed and enabled in the product
     If this attribute is not present it is set to workInProgress in dev mode,
     and to released in production mode.
     */
    serviceOptions?: ServiceOptions;

    pendo?: PendoOptions;

    importCsv?: ImportCsvOptions;

    exportCsv?: ExportCsvOptions;

    reportOptions?: ReportOptions;

    workflow?: WorkflowOptions;

    /**
     * Options for upgrade.
     */
    upgrade?: UpgradeOptions;

    /** CLI plugins */
    cli?: { plugins?: string[] };

    /**
     * Configuration of debug metrics
     */
    debugMetrics?: DebugMetricsOptions;

    env?: { isCI?: boolean };
    // Path to the config files we wish to extend the config
    extensionPath?: string;
    addOns?: AddOnConfig;

    /** Configuration for copilot chat and insights */
    copilot?: CopilotConfig;

    /** Configuration for message broadcasting */
    uiBroadcastTimeout?: number;
}

export interface AuthConfig {
    login?: string;
    auth0?: string;
    persona?: string;
    tenantId?: string;
}

export interface DevelopmentConfig extends Config {
    auth: AuthConfig;
}

export function isDevelopmentConfig(config: Config): config is DevelopmentConfig {
    return config.deploymentMode === 'development';
}

export function isDevelopmentProdUiConfig(config: Config): config is DevelopmentConfig {
    return config.deploymentMode === 'development' && !!config.prodUi;
}

export interface StandaloneConfig {
    agGridLicenceKey: string | null;
    productName?: string;
    chatbotBackendUrl?: string;
    chatbotGmsClient?: string;
    chatbotAccessCodeLifeTimeInMinutes?: number;
    app?: string;
    pendoClusterTag?: string;
    locales?: string[]; // locales available for the main package
}

export function isEnvVarTrue(v: string | undefined): boolean {
    return !!v && ['1', 'true'].includes(v.toLowerCase());
}
