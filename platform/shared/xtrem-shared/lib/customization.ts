import { Dict } from './util';

export interface MetaCustomizableNode {
    node: string;
    fullName: string;
}

export type MetaCustomizableNodesArray = Array<MetaCustomizableNode>;

export interface MetaCustomField {
    name: string;
    title: string;
    dataType: string;
    targetNodeName?: string;
    enumValues?: { name: string; title: string }[];
    componentType: string;
    anchorPropertyName?: string;
    anchorPosition: string;
    componentAttributes?: any;
    destinationTypes?: string[];
}

export type MetaCustomFields = Dict<MetaCustomField[]>;
export type MetaCustomFieldsArray = Array<{ name: string; properties: MetaCustomField[] }>;
