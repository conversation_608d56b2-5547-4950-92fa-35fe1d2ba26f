// =================================================================================================
// DO NOT EDIT: Generated file! (see scripts/gen-mime-types.mjs)
// =================================================================================================
import * as _ from 'lodash';
import { Dict } from './util';

export const attachmentsMimeTypesByExtention: Dict<string[]> = {
    bmp: ['image/bmp', 'image/x-ms-bmp'],
    csv: ['text/csv', 'text/plain'],
    doc: ['application/msword'],
    docm: ['application/vnd.ms-word.document.macroenabled.12'],
    docx: ['application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
    dxf: ['application/dxf', 'image/vnd.dxf', 'image/x-dwg', 'text/plain'],
    emlx: ['message/rfc822', 'text/plain'],
    gif: ['image/gif'],
    heic: ['image/heic'],
    heif: ['image/heif'],
    jfif: ['image/pjpeg'],
    jpeg: ['image/jpeg'],
    jpg: ['image/jpeg'],
    msg: ['application/vnd.ms-outlook'],
    odp: ['application/vnd.oasis.opendocument.presentation'],
    ods: ['application/vnd.oasis.opendocument.spreadsheet'],
    odt: ['application/vnd.oasis.opendocument.text'],
    pages: ['application/vnd.apple.pages', 'application/x-iwork-pages-sffpages'],
    pdf: ['application/pdf'],
    png: ['image/png'],
    ppt: ['application/vnd.ms-powerpoint'],
    pptm: ['application/vnd.ms-powerpoint.presentation.macroenabled.12'],
    pptx: ['application/vnd.openxmlformats-officedocument.presentationml.presentation'],
    psd: ['image/vnd.adobe.photoshop'],
    rtf: ['application/rtf', 'text/rtf'],
    slk: ['application/x-sylk', 'text/plain'],
    svg: ['image/svg+xml'],
    sylk: ['application/x-sylk', 'text/plain'],
    tif: ['image/tiff'],
    tiff: ['image/tiff'],
    txt: ['text/plain'],
    vsd: ['application/vnd.visio'],
    vsdx: ['application/vnd.visio'],
    vss: ['application/vnd.visio'],
    vssm: ['application/vnd.ms-visio.stencil.macroEnabled.12', 'application/vnd.visio'],
    vssx: ['application/vnd.ms-visio.stencil', 'application/vnd.visio'],
    vst: ['application/vnd.visio'],
    vstm: ['application/vnd.ms-visio.stencil.macroEnabled.12', 'application/vnd.visio'],
    vstx: ['application/vnd.ms-visio.template', 'application/vnd.visio'],
    vsw: ['application/vnd.visio'],
    webp: ['image/webp'],
    xls: ['application/vnd.ms-excel'],
    xlsm: ['application/vnd.ms-excel.sheet.macroenabled.12'],
    xlsx: ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
    xml: ['application/xml', 'text/xml'],
    zip: ['application/x-zip-compressed', 'application/zip'],
};

/**
 * Allowed mime types for attachments
 * @see https://www.iana.org/assignments/media-types/media-types.xhtml
 * @see https://www.npmjs.com/package/mime-db
 * @see https://mimetype.io/
 */
export const attachmentsMimeTypes = _.uniq(_.flatten(Object.values(attachmentsMimeTypesByExtention))).sort();
