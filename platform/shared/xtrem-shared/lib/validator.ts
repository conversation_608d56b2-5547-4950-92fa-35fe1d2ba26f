export const validator = {
    isAlphaNumericName(value: string, options?: { throw: { name: string } }): boolean {
        if (!/^[a-zA-Z]\w*$/.test(value)) {
            if (options?.throw) throw new Error(`Invalid ${options?.throw.name} value: '${value}'.`);
            return false;
        }
        return true;
    },
    isEmail(value: string, options?: { throw: { name: string } }): boolean {
        // from https://owasp.org/www-community/OWASP_Validation_Regex_Repository
        if (!/^[a-zA-Z0-9_+&*-]+(?:\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}$/.test(value)) {
            if (options?.throw) throw new Error(`Invalid ${options?.throw.name} value: '${value}'.`);
            return false;
        }
        return true;
    },
};
