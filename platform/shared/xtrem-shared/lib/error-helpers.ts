import { BaseError, InterruptException, SystemError } from './errors';

export function rethrownError(mapped: Error, cause: Error): Error {
    if (mapped === cause) return mapped;
    if (mapped instanceof BaseError && mapped.innerError === cause) return mapped;

    // mapError did not set cause correctly, so we wrap again to preserve cause
    // If we get this error we have to fix mapError because the error class is not being preserved.
    const remapped = new SystemError(mapped.message, cause);
    return new SystemError('invalid cause in rethrown error', remapped);
}

export function withRethrow<T>(body: () => T, mapError: (error: any) => BaseError): T {
    try {
        const result = body();
        if (result instanceof Promise) {
            return result.catch(error => {
                throw rethrownError(mapError(error), error);
            }) as T;
        }
        return result;
    } catch (error) {
        if (error instanceof InterruptException) throw error;
        throw rethrownError(mapError(error), error);
    }
}

export interface ErrorLogger {
    error: (err: Error) => any;
}

export function asyncFireAndForget(fn: () => Promise<void>, errorMessage: string, logger: ErrorLogger = console): void {
    fn().catch(error => logger.error(new SystemError(errorMessage, error)));
}
