/**
 * This file contains types that are shared between the xtrem-ui, xtrem-ui-components and other front-end folder files.
 */

import { memoize } from 'lodash';
import type { UnionToIntersection } from 'ts-essentials';
import { LocalizeFunction } from './locale';

// eslint-disable-next-line @typescript-eslint/naming-convention
export enum GraphQLTypes {
    Boolean = 'Boolean',
    Date = 'Date',
    DateTime = 'datetime', // Note: GraphQL type is lowercase 'd'
    Decimal = 'Decimal',
    Enum = 'Enum',
    ExternalReference = 'ExternalReference', // Non-vital references for ADC, same as IntReference
    Float = 'Float',
    Id = 'Id', // Yes, it is lowercase 'd'
    _InputStream = '_InputStream',
    _InputTextStream = '_InputTextStream',
    _OutputTextStream = '_OutputTextStream',
    Int = 'Int',
    IntOrString = 'IntOrString',
    IntReference = 'IntReference', // Non-vital references
    Json = 'Json',
    String = 'String',
}

export const textStreams: ReadonlyArray<GraphQLTypes> = [GraphQLTypes._OutputTextStream, GraphQLTypes._InputTextStream];

export type NodeTypeKind = 'LIST' | 'OBJECT' | 'SCALAR' | 'INPUT_OBJECT' | 'INTERFACE' | 'ENUM';

export interface NodeDetails {
    canFilter: boolean;
    canSort: boolean;
    enumValues?: string[];
    isCollection?: boolean;
    kind: NodeTypeKind;
    label: string;
    name: string;
    node?: string;
    isStored: boolean;
    isOnInputType: boolean;
    isOnOutputType: boolean;
    dataType: string;
    targetNode: string;
    enumType: string | null;
    isCustom: boolean;
    isMutable: boolean;
    type: GraphQLTypes | (string & Record<never, never>);
}

export const LESS_THAN = 'lessThan';
export const LESS_THAN_EQUAL = 'lessThanOrEqual';
export const RANGE = 'inRange';
export const MULTIPLE_RANGE = 'multipleRange';
export const GREATER_THAN = 'greaterThan';
export const GREATER_THAN_EQUAL = 'greaterThanOrEqual';

export const CONTAINS = 'contains';
export const STARTS_WITH = 'startsWith';
export const ENDS_WITH = 'endsWith';
export const NOT_CONTAINS = 'notContains';
export const MATCHES = 'matches';
export const EQUALS = 'equals';
export const NOT_EQUALS = 'notEqual';
export const MULTI_NOT_EQUALS = 'multiNotEqual';
export const SET = 'set';
export const EMPTY = 'empty';
export const NOT_EMPTY = 'notEmpty';

export type UserCustomizableWidget = 'TABLE' | 'INDICATOR_TILE' | 'BAR_CHART' | 'LINE_CHART';

export const RANGE_DIVIDER = '~';

export const enumFilter = [SET, MULTI_NOT_EQUALS] as const;
export const equalOrNotEqual = [MATCHES, NOT_EQUALS] as const;
export const emptyOrNotEmpty = [EMPTY, NOT_EMPTY] as const;
export const rangeFilter = [
    LESS_THAN,
    LESS_THAN_EQUAL,
    EQUALS,
    NOT_EQUALS,
    GREATER_THAN,
    GREATER_THAN_EQUAL,
    RANGE,
] as const;

export const stringFilter = [
    CONTAINS,
    ENDS_WITH,
    EQUALS,
    GREATER_THAN_EQUAL,
    GREATER_THAN,
    LESS_THAN_EQUAL,
    LESS_THAN,
    MATCHES,
    NOT_EQUALS,
    SET,
    STARTS_WITH,
    EMPTY,
    NOT_EMPTY,
    NOT_CONTAINS,
] as const;
export type StringFilter = (typeof stringFilter)[number];
export type MultipleRangeFilter = typeof MULTIPLE_RANGE;
export type RangeFilter = (typeof rangeFilter)[number];
export type EnumFilter = (typeof enumFilter)[number];
export type SetFilter = typeof SET;
export type BooleanFilter = typeof MATCHES;
export type Empty = typeof EMPTY;
export type NotEmpty = typeof NOT_EMPTY;
export class EnumType {
    value: string;
}
export type FiltrableType = string | number | Date | boolean | EnumType;
export type FilterTypeValue =
    | StringFilter
    | MultipleRangeFilter
    | BooleanFilter
    | SetFilter
    | RangeFilter
    | EnumFilter
    | Empty
    | NotEmpty;

export type FilterType<T extends FiltrableType> = T extends string
    ? StringFilter | MultipleRangeFilter
    : T extends number | Date
      ? RangeFilter
      : T extends boolean
        ? BooleanFilter
        : T extends EnumType
          ? EnumFilter
          : never;
export interface TreeElement<T extends any = NodeDetails> {
    key: string;
    id: string;
    labelKey: string;
    labelPath: string;
    path?: string;
    label: string;
    canBeSelected?: boolean;
    canBeExpanded?: boolean;
    data: T;
    title?: string;
}
export type Property = TreeElement;
export type FilterProperty = Property & {
    filterType: FilterTypeValue | 'timeFrame';
    filterValue: any;
    parameter?: boolean;
};
export interface AggregationOptions {
    group: number;
    operation: Aggregations | 'NONE';
}

export type GroupProperty = Property & AggregationOptions;
export const filterGraphqlMapping: Record<GraphQLTypes, Readonly<FilterTypeValue[] | undefined>> = {
    [GraphQLTypes.Boolean]: [MATCHES] as const,
    [GraphQLTypes.Date]: [...rangeFilter, ...emptyOrNotEmpty],
    [GraphQLTypes.DateTime]: [...rangeFilter, ...emptyOrNotEmpty],
    [GraphQLTypes.Decimal]: [...rangeFilter, ...emptyOrNotEmpty],
    [GraphQLTypes.Enum]: enumFilter,
    [GraphQLTypes.ExternalReference]: undefined,
    [GraphQLTypes.Float]: [...rangeFilter, ...emptyOrNotEmpty],
    [GraphQLTypes.Id]: equalOrNotEqual,
    [GraphQLTypes._InputStream]: undefined,
    [GraphQLTypes._OutputTextStream]: undefined,
    [GraphQLTypes._InputTextStream]: undefined,
    [GraphQLTypes.Int]: [...rangeFilter, ...emptyOrNotEmpty],
    [GraphQLTypes.IntOrString]: equalOrNotEqual,
    [GraphQLTypes.IntReference]: equalOrNotEqual,
    [GraphQLTypes.Json]: undefined,
    [GraphQLTypes.String]: stringFilter.filter(f => f !== MATCHES && f !== SET),
} as const;

export enum FieldKey {
    Aggregate = 'Aggregate',
    Button = 'Button',
    Calendar = 'Calendar',
    Card = 'Card',
    Chart = 'Chart',
    Checkbox = 'Checkbox',
    ContentTable = 'ContentTable',
    Count = 'Count',
    Date = 'Date',
    DetailList = 'DetailList',
    DropdownList = 'DropdownList',
    DynamicSelect = 'DynamicSelect',
    DynamicPod = 'DynamicPod',
    File = 'File',
    FileDeposit = 'FileDeposit',
    FilterEditor = 'FilterEditor',
    FilterSelect = 'FilterSelect',
    FormDesigner = 'FormDesigner',
    Icon = 'Icon',
    Image = 'Image',
    Label = 'Label',
    Link = 'Link',
    Message = 'Message',
    MultiDropdown = 'MultiDropdown',
    MultiFileDeposit = 'MultiFileDeposit',
    MultiReference = 'MultiReference',
    NestedGrid = 'NestedGrid',
    NodeBrowserTree = 'NodeBrowserTree',
    Numeric = 'Numeric',
    Preview = 'Preview',
    Plugin = 'Plugin',
    Pod = 'Pod',
    PodCollection = 'PodCollection',
    Progress = 'Progress',
    Radio = 'Radio',
    Reference = 'Reference',
    RelativeDate = 'RelativeDate',
    RichText = 'RichText',
    Select = 'Select',
    SelectionCard = 'SelectionCard',
    Separator = 'Separator',
    StaticContent = 'StaticContent',
    StepSequence = 'StepSequence',
    Switch = 'Switch',
    Table = 'Table',
    TableSummary = 'TableSummary',
    Technical = 'Technical',
    TechnicalJson = 'TechnicalJson',
    Text = 'Text',
    TextArea = 'TextArea',
    Time = 'Time',
    Datetime = 'Datetime',
    DatetimeRange = 'DatetimeRange',
    Toggle = 'ToggleButton',
    Tree = 'Tree',
    VisualProcess = 'VisualProcess',
    VitalPod = 'VitalPod',
    Workflow = 'Workflow',
}

const numericPresentation = [FieldKey.Numeric, FieldKey.Label, FieldKey.Text, FieldKey.Progress] as const;
const numericEditorPresentation = [FieldKey.Numeric] as const;

export const numericFields = [
    GraphQLTypes.Decimal,
    GraphQLTypes.Float,
    GraphQLTypes.Int,
    GraphQLTypes.IntReference,
] as GraphQLTypes[];

const datePresentation = [FieldKey.Date] as const;
const textPresentation = [FieldKey.Text] as const;

export const presentationGraphqlMapping = {
    [GraphQLTypes.Boolean]: [FieldKey.Checkbox] as const,
    [GraphQLTypes.Date]: datePresentation,
    [GraphQLTypes.DateTime]: datePresentation,
    [GraphQLTypes.Decimal]: numericPresentation,
    [GraphQLTypes.Enum]: [FieldKey.Text, FieldKey.Label] as const,
    [GraphQLTypes.ExternalReference]: textPresentation,
    [GraphQLTypes.Float]: numericPresentation,
    ID: textPresentation,
    InputStream: textPresentation,
    [GraphQLTypes._InputTextStream]: textPresentation,
    [GraphQLTypes._OutputTextStream]: textPresentation,
    [GraphQLTypes._InputStream]: textPresentation,
    [GraphQLTypes.Int]: numericPresentation,
    [GraphQLTypes.IntOrString]: textPresentation,
    [GraphQLTypes.Id]: textPresentation,
    [GraphQLTypes.IntReference]: numericPresentation,
    [GraphQLTypes.Json]: textPresentation,
    [GraphQLTypes.String]: textPresentation,
} as const;

export const presentationEditorGraphqlMapping = {
    [GraphQLTypes.Boolean]: [FieldKey.Checkbox] as const,
    [GraphQLTypes.Date]: datePresentation,
    [GraphQLTypes.DateTime]: datePresentation,
    [GraphQLTypes.Decimal]: numericEditorPresentation,
    [GraphQLTypes.Enum]: [FieldKey.Text, FieldKey.Label] as const,
    [GraphQLTypes.ExternalReference]: textPresentation,
    [GraphQLTypes.Float]: numericEditorPresentation,
    ID: textPresentation,
    [GraphQLTypes._InputStream]: textPresentation,
    InputStream: textPresentation,
    [GraphQLTypes._OutputTextStream]: textPresentation,
    [GraphQLTypes._InputTextStream]: textPresentation,
    [GraphQLTypes.Int]: numericEditorPresentation,
    [GraphQLTypes.IntOrString]: textPresentation,
    [GraphQLTypes.Id]: textPresentation,
    [GraphQLTypes.IntReference]: numericEditorPresentation,
    [GraphQLTypes.Json]: textPresentation,
    [GraphQLTypes.String]: textPresentation,
} as const;

export type GridNestedFieldTypes =
    | FieldKey.Aggregate
    | FieldKey.Checkbox
    | FieldKey.Count
    | FieldKey.Date
    | FieldKey.Datetime
    | FieldKey.DatetimeRange
    | FieldKey.DropdownList
    | FieldKey.FilterSelect
    | FieldKey.Icon
    | FieldKey.Image
    | FieldKey.Label
    | FieldKey.Link
    | FieldKey.Numeric
    | FieldKey.Progress
    | FieldKey.Reference
    | FieldKey.RelativeDate
    | FieldKey.Select
    | FieldKey.Switch
    | FieldKey.Technical
    | FieldKey.Text;

export const ATTR_CONTEXT_OBJECT_TYPE = 'data-context-object-type';
export const ATTR_CONTEXT_OBJECT_PATH = 'data-context-object-path';
export const ATTR_CONTEXT_FILTER = 'data-context-filter';
export const ATTR_CONTEXT_CONDITION = 'data-context-condition';
export const ATTR_CONTEXT_LIST_ORDER = 'data-context-list-order';
export const ATTR_PROPERTY_DISPLAY_LABEL = 'data-property-display-label';
export const ATTR_PROPERTY_NAME = 'data-property-name';
export const ATTR_PROPERTY_DATA_FORMAT = 'data-property-data-format';
export const ATTR_PROPERTY_DATA_TYPE = 'data-property-data-type';
export const ATTR_ALIAS = 'data-alias';
export const ATTR_FOOTER_GROUP = 'data-footer-group';
export const ATTR_QUERY_TABLE_HIDDEN_ROW = 'data-hidden';
/** Used for ensuring integrity of the context and their children when the properties are dragged or copied */
export const ATTR_PROPERTY_PARENT_CONTEXT_TYPE = 'data-property-parent-context';
export const RECORD_CONTEXT_CLASS = 'record-context';
export const QUERY_TABLE_CLASS = 'query-table';
export const PROPERTY_CLASS = 'property';
export const BLOCK_AGGREGATION_PROPERTY_NAME = '_blockAggregatedData';
export const BLOCK_AGGREGATION_ROLLING_PROPERTY_NAME = '_blockAggregatedRollingData';
export const ATTR_BREAK_PAGE_AFTER = 'data-break-page-after';
export const ATTR_CLASS = 'class';

export enum Aggregations {
    min = 'min',
    max = 'max',
    sum = 'sum',
    avg = 'avg',
    distinctCount = 'distinctCount',
}
export enum Sortings {
    ascending = 'ascending',
    descending = 'descending',
}

export type SortingOptions = Sortings;

export const SORTING_OPTIONS: Sortings[] = [Sortings.ascending, Sortings.descending];

export const STRING_AGGREGATIONS: Aggregations[] = [Aggregations.distinctCount];
export const NUMBER_AGGREGATIONS: Aggregations[] = [
    Aggregations.min,
    Aggregations.max,
    Aggregations.sum,
    Aggregations.avg,
    Aggregations.distinctCount,
];
export const DATE_AGGREGATIONS: Aggregations[] = [Aggregations.min, Aggregations.max, Aggregations.distinctCount];
export const OTHER_AGGREGATIONS: Aggregations[] = [Aggregations.distinctCount];

export const aggregationsGraphqlMapping: Record<GraphQLTypes, Aggregations[] | undefined> = {
    [GraphQLTypes.Boolean]: [Aggregations.distinctCount],
    [GraphQLTypes.Date]: DATE_AGGREGATIONS,
    [GraphQLTypes.DateTime]: DATE_AGGREGATIONS,
    [GraphQLTypes.Decimal]: NUMBER_AGGREGATIONS,
    [GraphQLTypes.Enum]: [Aggregations.distinctCount],
    [GraphQLTypes.ExternalReference]: [Aggregations.distinctCount],
    [GraphQLTypes.Float]: NUMBER_AGGREGATIONS,
    [GraphQLTypes.Id]: NUMBER_AGGREGATIONS,
    [GraphQLTypes._InputStream]: [Aggregations.distinctCount],
    [GraphQLTypes._OutputTextStream]: undefined,
    [GraphQLTypes._InputTextStream]: undefined,
    [GraphQLTypes.Int]: NUMBER_AGGREGATIONS,
    [GraphQLTypes.IntOrString]: [Aggregations.distinctCount],
    [GraphQLTypes.IntReference]: [Aggregations.distinctCount],
    [GraphQLTypes.Json]: [Aggregations.distinctCount],
    [GraphQLTypes.String]: STRING_AGGREGATIONS,
};

export const sortingGraphqlMapping: Record<GraphQLTypes, Sortings[] | undefined> = {
    [GraphQLTypes.Boolean]: SORTING_OPTIONS,
    [GraphQLTypes.Date]: SORTING_OPTIONS,
    [GraphQLTypes.DateTime]: SORTING_OPTIONS,
    [GraphQLTypes.Decimal]: SORTING_OPTIONS,
    [GraphQLTypes.Enum]: SORTING_OPTIONS,
    [GraphQLTypes.ExternalReference]: SORTING_OPTIONS,
    [GraphQLTypes.Float]: SORTING_OPTIONS,
    [GraphQLTypes.Id]: SORTING_OPTIONS,
    [GraphQLTypes._InputStream]: SORTING_OPTIONS,
    [GraphQLTypes._OutputTextStream]: undefined,
    [GraphQLTypes._InputTextStream]: undefined,
    [GraphQLTypes.Int]: SORTING_OPTIONS,
    [GraphQLTypes.IntOrString]: SORTING_OPTIONS,
    [GraphQLTypes.IntReference]: SORTING_OPTIONS,
    [GraphQLTypes.Json]: SORTING_OPTIONS,
    [GraphQLTypes.String]: SORTING_OPTIONS,
};

export const aggregationTranslations = memoize(
    (localize: LocalizeFunction): Record<Aggregations, string> => ({
        [Aggregations.min]: localize('@sage/xtrem-ui/minimum', 'Minimum'),
        [Aggregations.max]: localize('@sage/xtrem-ui/maximum', 'Maximum'),
        [Aggregations.sum]: localize('@sage/xtrem-ui/sum', 'Sum'),
        [Aggregations.avg]: localize('@sage/xtrem-ui/average', 'Average'),
        [Aggregations.distinctCount]: localize('@sage/xtrem-ui/distinct-count', 'Distinct count'),
    }),
);

export const sortingTranslations = memoize(
    (localize: LocalizeFunction): Record<Sortings, string> => ({
        [Sortings.ascending]: localize('@sage/xtrem-ui-components/ascending', 'Ascending'),
        [Sortings.descending]: localize('@sage/xtrem-ui-components/descending', 'Descending'),
    }),
);

// If T is `any` a union of both side of the condition is returned.
type UnionForAny<T> = T extends never ? 'A' : 'B';
// Returns true if type is any, or false for any other type.
export type IsStrictlyAny<T> = UnionToIntersection<UnionForAny<T>> extends never ? true : false;
