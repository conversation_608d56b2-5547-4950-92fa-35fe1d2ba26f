import { format, LocalizeFunction, LocalizeLocale } from '@sage/xtrem-shared';
import { getLiteral } from './resolver';
import { enumNameToStringKey } from './utils';

/**
 * Localize a message for the user.
 *
 * @param key The key which will be extracted to the translation files, must be unique in the package
 * @param template The template value which will be translated and proof-read
 * @param data Data to be used to populate the translated messages
 */
export const localizedText: LocalizeFunction = (
    key: string,
    _template: string,
    data: object | any[] = {},
    locale: LocalizeLocale = 'base',
) => {
    const literal = getLiteral(key, locale).content;
    return format(literal, locale, data);
};

export function localizeEnumMember(enumName: string, memberName: string, locale: LocalizeLocale = 'base'): string {
    const key = enumNameToStringKey(enumName, memberName);
    // Hack to prevent visiting this call during localization transpiling
    return localizedText(key, memberName, {}, locale);
}

export const localize = localizedText;
