import { Logger } from '@sage/xtrem-log';
import { Dict, localeFallbacks, LocalizeLocale, supportedLocales } from '@sage/xtrem-shared';
import * as fs from 'fs';
import { merge, snakeCase } from 'lodash';
import * as path from 'path';
import { getLocaleFromHeader } from './utils';

const platformPackages = [
    '@sage/xtrem-ui',
    '@sage/xtrem-core',
    '@sage/xtrem-date-time',
    '@sage/xtrem-standalone',
    '@sage/xtrem-ui-components',
    '@sage/xtrem-document-editor',
];
const parentPackageMap: Dict<string> = {
    '@sage/xtrem-date-time': '@sage/xtrem-core',
    '@sage/xtrem-ui-components': '@sage/xtrem-ui',
};

const logger = Logger.getLogger(__filename, 'i18n-resolver');

// SECURITY NOTE: Always use Object.create(null) to prevent from prototype pollutions
let resolvedTranslations: Dict<Dict<LocalizedLiteral>> = Object.create(null);
let allAvailableStringKeys: Dict<Dict<string[]>> = Object.create(null);
let rawStringCache: Dict<Dict<string>> = Object.create(null);

export interface LocalizedLiteral {
    key: string;
    content: string;
}

export function getKey(param: {
    isExtension?: boolean;
    packageName: string;
    nodeName: string;
    operation?: { kind: string; name: string };
}) {
    const nodeText = param.isExtension ? 'node-extensions' : 'nodes';

    const prefix = `${param.packageName}/${nodeText}__${snakeCase(param.nodeName)}${
        param.isExtension ? '_extension' : ''
    }`;
    if (param.operation) {
        return `${prefix}__${param.operation.kind}__${param.operation.name}`;
    }
    return prefix;
}

export function resetDictionary() {
    // SECURITY NOTE: Always use Object.create(null) to prevent from prototype pollutions
    resolvedTranslations = Object.create(null);
    rawStringCache = Object.create(null);
    allAvailableStringKeys = Object.create(null);
    logger.verbose(() => 'String caches reset.');
}

export function getLocaleFromString(stringLocale?: string): LocalizeLocale {
    return stringLocale ? getLocaleFromHeader({ 'accept-language': stringLocale }) : 'base';
}

/**
 * Reads all i18n JSON files of the current Etna application and puts them to the memory.
 * */
export function initializeLocalizationResolver(loadedPackages: { dir: string; name: string }[], dir: string): void {
    const packages = [...loadedPackages];
    supportedLocales.forEach(sl => {
        if (!rawStringCache[sl]) rawStringCache[sl] = {};
        if (!resolvedTranslations[sl]) resolvedTranslations[sl] = {};
    });

    // Try to load platform packages
    platformPackages.forEach(p => {
        try {
            const paths = [...loadedPackages.map(pkg => pkg.dir), dir];
            const parentPackage = parentPackageMap[p];
            if (parentPackage) {
                try {
                    // add packages that are a dependency in this parentPackage
                    paths.push(path.dirname(require.resolve(`${parentPackage}/package.json`, { paths })));
                    // eslint-disable-next-line no-empty
                } catch (e) {
                    logger.warn(`i18n ${p} not loaded due to ${e}`);
                }
            }

            const packagePath = path.dirname(require.resolve(`${p}/package.json`, { paths }));

            packages.push({
                name: p,
                dir: packagePath,
            });
        } catch (e) {
            logger.warn(`i18n ${p} not loaded due to ${e}`);
        }
    });

    packages.forEach(p => {
        // note: replace wait by sync operation for xtrem-core unit test
        const i18nDir = path.resolve(p.dir, 'build', 'lib', 'i18n');
        if (!fs.existsSync(i18nDir)) return;
        logger.verbose(() => `Loading string literals from '${i18nDir}'`);
        fs.readdirSync(i18nDir)
            .filter(f => f.endsWith('.json'))
            .forEach(f => {
                const locale = f.replace('.json', '');
                // eslint-disable-next-line global-require, import/no-dynamic-require
                const content = require(path.join(i18nDir, f));
                merge(rawStringCache[locale], content);
            });
    });

    let count = 0;
    // The base file always contains all available keys.
    // WARNING: the following code uses notably a for of loop and push to optimize performance (CHANGE ONLY IF YOU CAN IMPROVE PERF)
    if (rawStringCache.base) {
        const baseKeys = Object.keys(rawStringCache.base);
        // eslint-disable-next-line no-restricted-syntax
        for (const key of baseKeys) {
            const [scope, pack] = key.split('/', 2);
            allAvailableStringKeys[scope] = allAvailableStringKeys[scope] ?? {};
            allAvailableStringKeys[scope][pack] = allAvailableStringKeys[scope][pack] ?? [];
            allAvailableStringKeys[scope][pack].push(key);
            // eslint-disable-next-line no-plusplus
            count++;
        }
    }
    logger.verbose(() => `${count} string literals loaded.`);
}

export function addBundleLocalizationKeys(bundleStrings: Dict<Dict<string>>) {
    Object.keys(bundleStrings).forEach(locale => {
        rawStringCache[locale] = Object.assign(rawStringCache[locale] ?? {}, bundleStrings[locale] ?? {});
    });
    const bundleStringKeys = bundleStrings.base ? Object.keys(bundleStrings.base) : [];
    // WARNING: the following code uses notably a for of loop and push to optimize performance (CHANGE ONLY IF YOU CAN IMPROVE PERF)
    // eslint-disable-next-line no-restricted-syntax
    for (const key of bundleStringKeys) {
        const [scope, pack] = key.split('/', 2);
        allAvailableStringKeys[scope] = allAvailableStringKeys[scope] ?? {};
        allAvailableStringKeys[scope][pack] = allAvailableStringKeys[scope][pack] ?? [];
        allAvailableStringKeys[scope][pack].push(key);
    }
}

function resolveKey(key: string, locale: LocalizeLocale): LocalizedLiteral {
    // Loop through the optimal path
    const fallbacks = localeFallbacks[locale];
    const resolved = resolvedTranslations[locale];
    // WARNING: the following code uses notably a for of loop and push to optimize performance (CHANGE ONLY IF YOU CAN IMPROVE PERF)
    // eslint-disable-next-line no-restricted-syntax
    for (const currentLocale of fallbacks) {
        const rawStrings = rawStringCache[currentLocale];
        if (rawStrings?.[key]) {
            // If successfully resolved, put it to the cache
            resolved[key] = { key, content: rawStrings[key] };
            return resolved[key];
        }
    }

    // If nothing is available, we just return an string, but is should never be the case.
    return { key, content: ' ' };
}

/**
 * Resolves a single key to a string literal
 *
 * @param key key to lookup, requires exact match
 * @param locale optional, if not provided it will use f-stream's context
 */
export function getLiteral(key: string, locale: LocalizeLocale): LocalizedLiteral {
    const parts = key.split('/', 3);
    if (!parts[0] || !parts[1] || !parts[2] || !allAvailableStringKeys[parts[0]]?.[parts[1]]?.includes(key)) {
        throw new Error(`Invalid literal key: '${key}'`);
    }

    const resolved = resolvedTranslations[locale];
    if (resolved?.[key]) {
        return resolved[key];
    }
    return resolveKey(key, locale);
}

/**
 * Resolves a set of to a string literals
 *
 * @param keyStartsWith used to match
 * @param locale optional, if not provided it will use f-stream's context
 */
export function getLiterals(keyStartsWith: string, locale: LocalizeLocale): LocalizedLiteral[] {
    return appendLiterals(keyStartsWith, locale, []);
}

/**
 * Resolves a set of to a string literals
 *
 * @param keyStartsWith used to match
 * @param locale locale used to collect lirarals
 * @param literals array to push literals
 */
export function appendLiterals(
    keyStartsWith: string,
    locale: LocalizeLocale,
    literals: LocalizedLiteral[],
): LocalizedLiteral[] {
    let count = literals.length;

    // From the introspection the client doesn't know in which package the enum is defined at, so we need to allow enum queries
    if (keyStartsWith.startsWith('enums__')) {
        // We need to search the keys on the global scope as we don't know which package they belong to
        const enumMemberKeys = Object.keys(rawStringCache.base).filter(k => k.indexOf(`/${keyStartsWith}`) !== -1);
        enumMemberKeys.forEach(k => {
            // PERF: fast push by direct access to index
            // eslint-disable-next-line no-plusplus
            literals[count++] = getLiteral(k, locale);
        });

        return literals;
    }
    const parts = keyStartsWith.split('/', 2);
    if (!parts[0] || !parts[1]) {
        throw new Error(`Invalid package or page: '${keyStartsWith}'`);
    }

    const keys = allAvailableStringKeys[parts[0]]?.[parts[1]] || [];
    // eslint-disable-next-line no-restricted-syntax
    for (const k of keys) {
        if (k.startsWith(keyStartsWith)) {
            // PERF: fast push by direct access to index
            // eslint-disable-next-line no-plusplus
            literals[count++] = getLiteral(k, locale);
        }
    }
    return literals;
}
