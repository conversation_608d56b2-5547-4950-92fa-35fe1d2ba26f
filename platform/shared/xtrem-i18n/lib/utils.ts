import { Logger } from '@sage/xtrem-log';
import { LocalizeLocale, supportedLocales } from '@sage/xtrem-shared';
import * as parser from 'accept-language-parser';
import * as fs from 'fs';
import { IncomingHttpHeaders } from 'http';
import { snakeCase } from 'lodash';
import * as path from 'path';

const logger = new Logger(__filename, 'utils');

export interface ClientArtifactUsedLiterals {
    strings: string[];
    enums: string[];
}

/**
 * Gets the name of the current package
 * @param dir the starting directory
 * @returns the name of the package (closest ancestor)
 */
export function getPackageNameAndRoot(start: string): { name: string; root: string } {
    const isExisting = fs.existsSync(start);
    if (!isExisting) {
        throw new Error(`'${start}' doesn't exist`);
    }
    const root = path.parse(start).root;
    let dir = start;
    if (fs.lstatSync(start).isFile()) {
        dir = path.dirname(start);
    }
    const packageJson = path.join(dir, 'package.json');
    if (!fs.existsSync(packageJson)) {
        const newDir = path.join(dir, '..');
        if (newDir === root) {
            throw new Error("Couldn't find the name of the package");
        }
        return getPackageNameAndRoot(newDir);
    }

    try {
        const name = JSON.parse(fs.readFileSync(packageJson, { encoding: 'utf8' })).name;
        return { name, root: dir };
    } catch (error) {
        throw new Error(`Couldn't read the 'name' property from '${packageJson}'`);
    }
}

export const getLocaleFromHeader = (headers?: IncomingHttpHeaders): LocalizeLocale => {
    if (headers) {
        const acceptLanguage = headers['accept-language'];
        if (acceptLanguage && typeof acceptLanguage === 'string') {
            const language = parser.pick(supportedLocales, acceptLanguage);
            return language || 'base';
        }
    } else {
        logger.debug(() => 'Request headers missing');
    }
    return 'base';
};

export const menuItemIdToStringKey = (menuItemId: string) => {
    const parts = menuItemId.split('/');
    const packageName = `${parts[0]}/${parts[1]}`;
    return `${packageName}/menu_item__${menuItemId.replace(`${packageName}/`, '')}`;
};

export const artifactReferenceToTitleStringKey = (artifactReference: string, artifactType: 'stickers' | 'pages') => {
    const parts = artifactReference.split('/');
    const packageName = `${parts[0]}/${parts[1]}`;
    return `${packageName}/${artifactType}__${snakeCase(parts[2])}____title`;
};

export const enumNameToStringKey = (enumName: string, memberName: string) => {
    const parts = enumName.split('/');
    return `${parts[0]}/${parts[1]}/enums__${snakeCase(parts[2])}__${memberName}`;
};
