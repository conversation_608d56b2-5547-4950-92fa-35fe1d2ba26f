import { expect } from 'chai';
import * as sinon from 'sinon';
import { localize } from '../lib/localize';
import * as resolver from '../lib/resolver';

describe('localize', () => {
    let getLiteralStub: sinon.SinonStub<any>;
    let getLocaleFromStringStub: sinon.SinonStub<any>;

    beforeEach(() => {
        getLiteralStub = sinon.stub(resolver, 'getLiteral').returns({ content: 'This is the return value', key: '' });
        getLocaleFromStringStub = sinon.stub(resolver, 'getLocaleFromString').returns('en-US');
    });

    afterEach(() => {
        getLiteralStub.restore();
        getLocaleFromStringStub.restore();
    });

    it('should resolve a simple string', () => {
        return expect(localize('@sage/any-package/any-string', 'core value')).to.eq('This is the return value');
    });

    it('should format arguments from an array', () => {
        return getLiteralStub.returns({
            content: 'The value of the {{0}} product is {{formatNumber (t 1) style="currency" currency=(t 2) }}',
            key: '',
        });
        // expect(localize('@sage/any-package/any-string', 'core value', ['1st', 12.45123, 'USD'])).to.eq('The value of the 1st product is US$ 12.45');
    });

    it('should format arguments from an object', () => {
        return getLiteralStub.returns({
            content:
                'The value of the {{order}} product is {{formatNumber value style="currency" currency=(t \'currency\') }}',
            key: '',
        });
        // expect(localize('@sage/any-package/any-string', 'core value', { order: '1st', value: 12.45123, currency: 'USD' })).to.eq('The value of the 1st product is US$ 12.45');
    });
});
