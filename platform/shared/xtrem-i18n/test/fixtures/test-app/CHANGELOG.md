# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-10-18)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-10-16)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-10-15)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-10-15)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-10-11)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-10-11)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-10-11)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-09-29)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-09-29)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-09-29)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-09-28)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-09-28)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-09-26)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-09-25)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-09-24)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-09-23)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-09-22)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-09-21)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-09-21)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-09-21)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-09-19)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-09-19)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-09-19)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-09-19)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-09-18)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-09-18)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-09-18)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-09-18)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-09-17)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-09-17)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-09-16)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-09-16)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-09-16)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-09-16)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-09-14)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-09-14)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-09-12)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-09-12)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-09-11)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-09-10)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-09-09)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-09-09)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-09-07)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-09-07)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-09-06)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-09-05)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-09-05)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-09-04)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-09-03)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-09-02)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-09-01)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-08-31)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-08-30)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-08-30)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-08-29)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-08-29)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-08-26)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-08-25)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-08-24)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-08-23)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-08-22)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-08-21)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-08-19)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-08-18)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-08-17)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-08-16)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-08-15)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-08-14)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-08-13)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-08-11)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-08-11)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-08-09)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-08-08)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-08-07)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-08-06)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-08-05)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-08-04)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-08-01)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-07-31)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-07-30)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-07-29)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-07-28)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-07-28)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-07-28)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-07-27)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-07-26)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-07-25)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-07-24)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-07-23)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-07-22)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-07-21)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-07-20)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-07-19)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-07-19)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-07-18)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-07-17)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-07-16)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-07-15)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-07-14)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-07-14)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-07-13)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-07-13)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-07-10)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-07-09)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-07-08)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-07-07)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-07-06)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-07-05)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-07-04)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-07-04)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-07-02)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-07-01)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-06-30)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-06-29)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-06-28)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-06-27)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-06-26)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-06-25)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-06-24)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-06-23)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-06-23)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-06-22)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-06-21)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-06-21)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-06-20)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-06-19)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-06-18)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-06-17)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-06-16)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-06-16)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-06-14)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-06-14)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-06-13)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-06-12)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-06-11)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-06-10)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-06-10)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-06-08)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-06-07)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-06-06)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-06-05)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-06-04)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-06-03)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-06-03)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-06-02)

### Bug Fixes

* package version number fix ([#7344](https://github.com/issues/7344))  ([517e460](https://github.com/commit/517e460cde4848f3559f14bfd79a77835cd78223))

### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-06-02)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-06-01)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-05-29)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-05-28)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-05-27)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-05-26)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-05-26)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-05-26)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-05-24)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-05-24)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-05-23)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-05-23)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-05-22)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-05-21)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-05-20)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-05-20)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-05-18)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-05-17)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-05-16)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-05-15)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-05-14)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-05-13)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-05-13)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-05-12)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-05-11)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-05-10)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-05-10)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-05-09)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-05-08)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-05-07)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-05-06)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-05-06)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-05-04)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-05-03)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-05-02)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-05-01)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-04-30)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-04-29)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-04-28)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-04-28)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-04-28)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-04-27)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-04-27)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-04-26)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-04-26)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-04-25)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-04-25)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-04-21)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-04-21)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-04-20)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-04-20)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-04-18)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-04-18)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-04-16)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-04-15)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-04-14)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-04-13)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-04-12)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-04-11)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-04-10)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-04-09)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-04-08)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-04-07)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-04-06)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-04-05)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-04-04)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-04-03)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-04-02)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-04-01)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-03-31)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-03-31)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-03-30)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-03-29)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-03-28)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-03-27)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-03-26)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-03-25)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-03-24)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-03-24)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-03-24)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-03-23)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-03-22)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-03-21)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-03-20)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-03-20)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-03-19)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-03-19)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-03-18)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-03-18)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-03-18)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-03-17)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-03-17)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-03-13)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-03-10)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-03-09)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-03-09)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-03-08)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-03-07)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-03-06)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-03-05)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-03-04)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-03-03)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-03-03)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-03-01)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-02-28)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-02-27)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-02-26)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-02-25)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-02-24)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-02-24)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-02-24)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-02-23)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-02-22)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-02-21)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-02-20)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-02-19)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-02-18)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-02-17)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-02-16)

### Bug Fixes


### Features

* add bar chart widget XT-18752 ([#5335](https://github.com/issues/5335))  ([daab389](https://github.com/commit/daab389c9b5267d3761c4cde726f33cf42c95097))

# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-02-15)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-02-13)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-02-12)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-02-11)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-02-10)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-02-10)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-02-08)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-02-07)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-02-07)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-02-06)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-02-05)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-02-04)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-02-04)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-02-02)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-02-01)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-01-31)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-01-30)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-01-29)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-01-29)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-01-28)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-01-28)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-01-28)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-01-26)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-01-26)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-01-25)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-01-25)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-01-24)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-01-24)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-01-23)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-01-23)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-01-21)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-01-18)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-01-18)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-01-17)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-01-16)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-01-15)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-01-14)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-01-14)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-01-13)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-01-12)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-01-11)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-01-11)

### Bug Fixes

* adc document entry type enum error (X3-266896) ([#4462](https://github.com/issues/4462))  ([41a18c4](https://github.com/commit/41a18c422eb3de031399ec8b2d0ede290cdb1869))

### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-01-11)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-01-10)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-01-09)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-01-08)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-01-07)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-01-07)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-01-06)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-01-05)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-01-04)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-01-03)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-01-02)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2022-01-01)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-12-31)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-12-30)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-12-30)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-12-29)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-12-28)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-12-27)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-12-27)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-12-22)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-12-21)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-12-15)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-12-14)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-12-13)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-12-12)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-12-11)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-12-10)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-12-09)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-12-09)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-12-08)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-12-07)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-12-07)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-12-06)

### Bug Fixes


### Features

* extract localizeEnumMember calls ([#4060](https://github.com/issues/4060))  ([68c0712](https://github.com/commit/68c0712cc02111b2b30d63911541923049fd373f))

# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-12-05)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-12-04)

### Bug Fixes


### Features

* extract localizeEnumMember calls  ([0f48fc2](https://github.com/commit/0f48fc21fb37ebc13a0ea60fd210a782c126acc8))

# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-12-03)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-12-03)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-12-02)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-12-02)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-12-02)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-12-02)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-12-01)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-11-29)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-11-29)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-11-29)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-11-28)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-11-27)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-11-26)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-11-25)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-11-24)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-11-23)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-11-22)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-11-22)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-11-19)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-11-18)

### Bug Fixes


### Features

* **shared:** XT-6876 adding test units to transformer file ([#3532](https://github.com/issues/3532))  ([deabfd5](https://github.com/commit/deabfd5e1055138332781eac55f2cdffe23d8ffb))

# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-11-18)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-11-17)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-11-17)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-11-14)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-11-13)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-11-12)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-11-11)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-11-10)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-11-09)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-11-09)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-11-09)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-11-08)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-11-07)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-11-06)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-11-05)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-11-04)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-11-03)

### Bug Fixes


### Features

* **shared:** XT-6876 unit tests ([#3438](https://github.com/issues/3438))  ([8367e84](https://github.com/commit/8367e8494559cd483f7ebf6512f2a705e6d386af))

# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-11-03)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-11-02)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-10-31)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-10-30)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-10-29)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-10-28)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-10-28)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-10-26)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-10-26)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-10-25)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-10-25)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-10-24)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-10-24)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-10-23)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-10-22)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-10-22)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-10-20)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-10-19)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-10-18)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-10-17)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-10-17)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-10-16)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-10-15)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-10-14)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-10-13)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-10-12)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-10-12)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-10-10)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-10-09)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-10-08)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-10-08)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-10-07)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-10-06)

### Bug Fixes


### Features


# [12.0.0](https://github.com/compare/...@sage/test-i18n-app@12.0.0) (2021-10-05)

### Bug Fixes

* platform test fixes after major version bump ([#3026](https://github.com/issues/3026))  ([a55ff9d](https://github.com/commit/a55ff9d41972d5bf69be2d167f3a686e36741aaf))
* version bump  ([be66252](https://github.com/commit/be66252a4428e87349f6af50ea31ac6909a8160f))
* XT-999999 fix package versions ([#1780](https://github.com/issues/1780))  ([4bd5d4a](https://github.com/commit/4bd5d4a02fc8959893403c22b7d702f664082be8))

### Features

* XT-4920 updating of enum types in nodes and api to union string ([#1149](https://github.com/issues/1149))  ([097909a](https://github.com/commit/097909a82182189967d3eb15b0cde0bf2392324d))
* **core:** XT-10028 test-app renamed in test-schema-gen-app ([#2500](https://github.com/issues/2500))  ([a03e44b](https://github.com/commit/a03e44b6157fffb6b9efd51320a16ace8e264772))

