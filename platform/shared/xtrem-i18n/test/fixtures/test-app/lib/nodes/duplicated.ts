import { decorators, Node, StringDataType, ValidationContext } from '@sage/xtrem-core';

@decorators.node<Address>({
    isPublished: true,
    storage: 'sql',
    indexes: [{ orderBy: { code: +1 }, isUnique: true }],
    controlEnd(cx: ValidationContext) {
        console.log('we should keep this string');
        cx.localize('@sage/test-i18n-app/customKey', 'custom value');
        cx.error.add("The sales site's company must be the sales order's company");
    },
})
export class Address extends Node {
    @decorators.stringProperty<Address, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 10 }),
    })
    code: string;

    @decorators.stringProperty<Address, 'street'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 50 }),
    })
    street: string;

    @decorators.stringProperty<Address, 'zip'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 50 }),
    })
    zip: string;

    @decorators.stringProperty<Address, 'city'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 50 }),
    })
    city: string;

    @decorators.stringProperty<Address, 'country'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 50 }),
    })
    country: string;
}
