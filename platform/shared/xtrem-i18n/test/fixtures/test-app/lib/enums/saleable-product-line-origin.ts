import { EnumDataType } from '@sage/xtrem-core';

export enum SaleableProductLineOriginEnum {
    manual = 1,
    operations = 2,
    productRequirements = 3,
    budgetLines = 4,
}

export type SaleableProductLineOrigin = keyof typeof SaleableProductLineOriginEnum;

export const saleableProductLineOriginEnumDataType = new EnumDataType<SaleableProductLineOrigin>({
    enum: SaleableProductLineOriginEnum,
    filename: __filename,
});
