import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<ActionButtons>({
    authorizationCode: 'ACTINBTNS',
    title: 'Action Buttons',
    isTransient: true,
    createAction() {
        return this.createCrudAction;
    },
    headerDropDownActions() {
        return [this.deleteCrudAction];
    },
    businessActions() {
        return [this.businessAction1, this.businessAction2, this.saveCrudAction];
    },
    onLoad() {
        this.descriptionField.value = `This page show cases the CRUD and business actions.
The actions are located outside the page body, on the title line and on the right-side panel.`;
    },
})
export class ActionButtons extends ui.Page {
    @ui.decorators.pageAction<ActionButtons>({
        onClick() {
            this.resultField.value = 'Save CRUD Button';
        },
    })
    saveCrudAction: ui.PageAction;

    @ui.decorators.pageAction<ActionButtons>({
        onClick() {
            this.resultField.value = 'Create CRUD Button';
            this.resultField.value = 'Second string literal';
            this.resultField.value = 'Third string literal';
        },
    })
    createCrudAction: ui.PageAction;

    @ui.decorators.pageAction<ActionButtons>({
        onClick() {
            this.resultField.value = 'Delete CRUD Button';
        },
        isHidden: true,
    })
    deleteCrudAction: ui.PageAction;

    @ui.decorators.pageAction<ActionButtons>({
        onClick() {
            this.resultField.value = 'Close CRUD Button';
        },
    })
    closeCrudAction: ui.PageAction;

    @ui.decorators.pageAction<ActionButtons>({
        title: 'Business action 1',
        onClick() {
            this.resultField.value = 'Business action 1';
        },
    })
    businessAction1: ui.PageAction;

    @ui.decorators.pageAction<ActionButtons>({
        title: 'Business action 2',
        onClick() {
            this.resultField.value = 'Business action 2';
        },
    })
    businessAction2: ui.PageAction;

    @ui.decorators.section<ActionButtons>({
        title: 'About this page',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<ActionButtons>({
        parent() {
            return this.mainSection;
        },
        title: '',
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.textAreaField<ActionButtons>({
        parent() {
            return this.mainBlock;
        },
        isReadOnly: true,
        isFullWidth: true,
        rows: 4,
    })
    descriptionField: ui.fields.TextArea;

    @ui.decorators.textField<ActionButtons>({
        title: 'Selected action',
        helperText: 'I am helping here',
        parent() {
            return this.mainBlock;
        },
        isReadOnly: true,
    })
    resultField: ui.fields.Text;

    @ui.decorators.section<ActionButtons>({
        title: 'Controlling Buttons',
    })
    controlSection: ui.containers.Section;

    @ui.decorators.block<ActionButtons>({
        parent() {
            return this.controlSection;
        },
        title: '',
    })
    controlBlock: ui.containers.Block;

    @ui.decorators.buttonField<ActionButtons>({
        title: 'Toggle Business Action 1 disabled',
        map() {
            return 'Click me';
        },
        parent() {
            return this.controlBlock;
        },
        onClick() {
            this.businessAction1.isDisabled = !this.businessAction1.isDisabled;
        },
    })
    disableBusinessAction1: ui.fields.Button;

    @ui.decorators.buttonField<ActionButtons>({
        title: 'Toggle Business Action 1 hidden',
        map() {
            return 'Click me';
        },
        parent() {
            return this.controlBlock;
        },
        onClick() {
            this.businessAction2.isHidden = !this.businessAction2.isHidden;
        },
    })
    hideBusinessAction2: ui.fields.Button;

    @ui.decorators.buttonField<ActionButtons>({
        title: 'Toggle Save CRUD action disabled',
        map() {
            return 'Click me';
        },
        parent() {
            return this.controlBlock;
        },
        onClick() {
            this.saveCrudAction.isDisabled = !this.saveCrudAction.isDisabled;
        },
    })
    disableSaveCrudAction: ui.fields.Button;
}
