import { expect } from 'chai';
import * as utils from '../lib/utils';

describe('i18n utils', () => {
    describe('locale from headers', () => {
        it('should return base if no headers found', () => {
            const result = utils.getLocaleFromHeader();
            expect(result).to.eq('base');
        });

        it('should return the language from the header', () => {
            let result = utils.getLocaleFromHeader({ 'accept-language': 'en-US' });
            expect(result).to.eq('en-US');
            result = utils.getLocaleFromHeader({ 'accept-language': 'en' });
            expect(result).to.eq('en-US');
        });

        it('should return the base of no locale match found', () => {
            const result = utils.getLocaleFromHeader({ 'accept-language': 'nl-NL' });
            expect(result).to.eq('base');
        });
    });
});
