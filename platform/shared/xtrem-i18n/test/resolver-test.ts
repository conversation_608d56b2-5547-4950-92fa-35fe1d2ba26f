import { expect } from 'chai';
import * as fs from 'fs';
import * as path from 'path';
import * as sinon from 'sinon';
import * as resolver from '../lib/resolver';

const applicationDir = path.resolve(__dirname, 'fixtures', 'test-app');

describe('resolver', () => {
    let readSpy: sinon.SinonSpy;
    beforeEach(() => {
        resolver.resetDictionary();
        readSpy = sinon.spy(fs, 'readFileSync');
    });

    afterEach(() => {
        readSpy.restore();
    });

    it('should populate the cache by reading translation files from the build folder', () =>
        (() => {
            expect(readSpy.called).to.eq(false);
            resolver.initializeLocalizationResolver(
                [{ name: '@sage/x3-test-app', dir: applicationDir }],
                applicationDir,
            );

            const calls = readSpy.getCalls().filter(c => c.args[0].match(/test-app/));
            expect(calls.length).to.eq(3);
            expect(calls[0].args[0]).to.match(/test-app[/\\]build[/\\]lib[/\\]i18n[/\\]base.json/);
            expect(calls[1].args[0]).to.match(/test-app[/\\]build[/\\]lib[/\\]i18n[/\\]en-US.json/);
            expect(calls[2].args[0]).to.match(/test-app[/\\]build[/\\]lib[/\\]i18n[/\\]es-ES.json/);
        })());

    describe('getLiteral', () => {
        it('should resolve a single key', () =>
            (() => {
                resolver.initializeLocalizationResolver(
                    [{ name: '@sage/x3-test-app', dir: applicationDir }],
                    applicationDir,
                );
                const result = resolver.getLiteral('@sage/x3-test-app/pages_stuff_title', 'en-US');
                expect(result.key).to.eq('@sage/x3-test-app/pages_stuff_title');
                expect(result.content).to.eq('American test string');
            })());

        it('should correctly fall back if target language not found', () =>
            (() => {
                resolver.initializeLocalizationResolver(
                    [{ name: '@sage/x3-test-app', dir: applicationDir }],
                    applicationDir,
                );
                const result = resolver.getLiteral('@sage/x3-test-app/pages_stuff_title', 'en-GB');
                expect(result.key).to.eq('@sage/x3-test-app/pages_stuff_title');
                expect(result.content).to.eq('American test string');
            })());

        it('should correctly fall back if a specific string in the target language not found', () =>
            (() => {
                resolver.initializeLocalizationResolver(
                    [{ name: '@sage/x3-test-app', dir: applicationDir }],
                    applicationDir,
                );
                const result = resolver.getLiteral('@sage/x3-test-app/pages_other_title', 'es-ES');
                expect(result.key).to.eq('@sage/x3-test-app/pages_other_title');
                expect(result.content).to.eq('Other');
            })());

        it('should figure out target language from the context if parameter not provided', () =>
            (() => {
                resolver.initializeLocalizationResolver(
                    [{ name: '@sage/x3-test-app', dir: applicationDir }],
                    applicationDir,
                );
                const result = resolver.getLiteral(
                    '@sage/x3-test-app/pages_stuff_title',
                    resolver.getLocaleFromString('es-ES'),
                );
                expect(result.key).to.eq('@sage/x3-test-app/pages_stuff_title');
                expect(result.content).to.eq('Spanish test string');
            })());

        it('should use the cache for looking up for the second time', () =>
            (() => {
                resolver.initializeLocalizationResolver(
                    [{ name: '@sage/x3-test-app', dir: applicationDir }],
                    applicationDir,
                );
                const result = resolver.getLiteral('@sage/x3-test-app/pages_stuff_title', 'en-US');
                expect(result.key).to.eq('@sage/x3-test-app/pages_stuff_title');
                expect(result.content).to.eq('American test string');
                resolver.getLiteral('@sage/x3-test-app/pages_stuff_title', 'en-US');
            })());

        it('should validate the key', () =>
            (() => {
                expect(() => resolver.getLiteral('@sage', 'en-US')).to.throw(/Invalid literal key/);
                expect(() => resolver.getLiteral('@sage/sadsad', 'en-US')).to.throw(/Invalid literal key/);
                expect(() => resolver.getLiteral('@sage/sadsad/asdasd', 'en-US')).to.throw(/Invalid literal key/);
            })());
    });

    describe('getLiterals', () => {
        it('should find keys based package name', () =>
            (() => {
                resolver.initializeLocalizationResolver(
                    [{ name: '@sage/x3-test-app', dir: applicationDir }],
                    applicationDir,
                );
                const result = resolver.getLiterals('@sage/x3-test-app', 'en-US');
                expect(result.length).to.eq(3);
                expect(result[0].key).to.eq('@sage/x3-test-app/pages_stuff_title');
                expect(result[0].content).to.eq('American test string');
                expect(result[1].key).to.eq('@sage/x3-test-app/pages_stuff_helperText');
                expect(result[1].content).to.eq('Test Helper US');
                expect(result[2].key).to.eq('@sage/x3-test-app/pages_other_title');
                expect(result[2].content).to.eq('Other');
            })());

        it('should figure out target language from the context if parameter not provided', () =>
            (() => {
                resolver.initializeLocalizationResolver(
                    [{ name: '@sage/x3-test-app', dir: applicationDir }],
                    applicationDir,
                );
                const result = resolver.getLiterals('@sage/x3-test-app', resolver.getLocaleFromString('es-ES'));
                expect(result.length).to.eq(3);
                expect(result[0].key).to.eq('@sage/x3-test-app/pages_stuff_title');
                expect(result[0].content).to.eq('Spanish test string');
            })());

        it('should fall back only on those strings which are not found in the requested language', () =>
            (() => {
                resolver.initializeLocalizationResolver(
                    [{ name: '@sage/x3-test-app', dir: applicationDir }],
                    applicationDir,
                );
                const result = resolver.getLiterals('@sage/x3-test-app', 'es-ES');
                expect(result.length).to.eq(3);
                expect(result[0].key).to.eq('@sage/x3-test-app/pages_stuff_title');
                expect(result[0].content).to.eq('Spanish test string');
                expect(result[1].key).to.eq('@sage/x3-test-app/pages_stuff_helperText');
                expect(result[1].content).to.eq('Test Helper ES');
                expect(result[2].key).to.eq('@sage/x3-test-app/pages_other_title');
                expect(result[2].content).to.eq('Other');
            })());

        it('should validate the key', () =>
            (() => {
                expect(() => resolver.getLiterals('@sage', 'en-US')).to.throw(/Invalid package or page/);
                expect(() => resolver.getLiterals('@sage/', 'en-US')).to.throw(/Invalid package or page/);
            })());
    });
});
