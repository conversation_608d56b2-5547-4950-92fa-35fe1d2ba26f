#!/usr/bin/env bash

if [[ -z "$1" ]]; then
    echo "Please provide the exportId of the daily-export-small-tenant pipeline"
    echo "(See latest export result https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=3433&_a=summary)"
    echo "The exportId is logged at the beginning of the step 'Export tenant' (example: 'Export.id=m7zBHgINoHXkSSPG7Ihat')"
    exit 1
fi

if [[ -z "$2" ]]; then
    echo "Please provide the app name (sdmo, shopfloor, ...)"
    exit 1
fi

if [[ -z "$3" ]]; then
    echo "Please provide a path to the export zip file. (example: '/tmp/export.zip')"
    exit 1
fi

EXPORT_ZIP_URL="s3://xtrem-dev-eu-global/exports-imports/$1/$2/$1.zip"
echo "Copying $EXPORT_ZIP_URL to $3"
aws s3 cp $EXPORT_ZIP_URL "$3"
