{"dashboard": {"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "links": [], "liveNow": false, "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 22, "panels": [], "title": "Business rules", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 1}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "editorMode": "code", "exemplar": false, "expr": "rate(xtrem_rules_total_millis[$__rate_interval]) / rate(xtrem_rules_calls [$__rate_interval])", "format": "time_series", "instant": false, "interval": "", "legendFormat": "{{nodeName}}.{{propertyName}}.{{ruleName}}", "range": true, "refId": "A"}], "title": "Business rule - average time", "transformations": [], "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 1}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "editorMode": "code", "exemplar": false, "expr": "topk(10, rate(xtrem_rules_total_millis\n[$__rate_interval]))", "format": "time_series", "instant": false, "interval": "", "legendFormat": "{{nodeName}}.{{propertyName}}.{{ruleName}}", "range": true, "refId": "A"}], "title": "Business rules - elapsed time", "transformations": [], "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "reqps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 1}, "id": 24, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "editorMode": "code", "expr": "topk(10, rate(xtrem_rules_calls[$__rate_interval]))", "legendFormat": "{{nodeName}}.{{propertyName}}.{{ruleName}}", "range": true, "refId": "A"}], "title": "Business rules - number of calls", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 10}, "id": 20, "panels": [], "title": "GraphQL", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 11}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "editorMode": "builder", "expr": "rate(xtrem_graphql_total_millis[$__rate_interval])", "legendFormat": "{{operation<PERSON><PERSON>}} {{nodeName}}.{{operationName}}", "range": true, "refId": "A"}], "title": "GraphQL operations - elapsed time", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "reqps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 11}, "id": 26, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "editorMode": "code", "expr": "topk(10, rate(xtrem_graphql_calls[$__rate_interval]))", "legendFormat": "{{operation<PERSON><PERSON>}} {{nodeName}} {{operationName}}", "range": true, "refId": "A"}], "title": "GraphQL operations - number of calls", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 19}, "id": 40, "panels": [], "title": "<PERSON><PERSON><PERSON>", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 20}, "id": 36, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "editorMode": "builder", "expr": "rate(xtrem_metadata_total_millis[$__rate_interval])", "legendFormat": "{{operation<PERSON><PERSON>}} {{nodeName}}.{{operationName}}", "range": true, "refId": "A"}], "title": "Metadata operations - elapsed time", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": 3600000, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "reqps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 20}, "id": 38, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "editorMode": "code", "expr": "topk(10, rate(xtrem_metadata_calls[$__rate_interval]))", "legendFormat": "{{operation<PERSON><PERSON>}} {{nodeName}}.{{operationName}}", "range": true, "refId": "A"}], "title": "Metadata operations - number of calls", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 28}, "id": 18, "panels": [], "title": "SQL Database", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 29}, "id": 12, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "editorMode": "code", "expr": "topk(10, rate(xtrem_sql_total_millis[$__rate_interval]))", "legendFormat": "{{statement<PERSON><PERSON>}} {{nodeName}}", "range": true, "refId": "A"}], "title": "SQL statements - elapsed time", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "description": "Number of SQL statements sent to the database", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "reqps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 29}, "id": 14, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "editorMode": "builder", "expr": "rate(xtrem_sql_calls[$__rate_interval])", "legendFormat": "{{statement<PERSON><PERSON>}} {{nodeName}}", "range": true, "refId": "A"}], "title": "SQL statements - number of calls", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["user.busy"], "prefix": "All except:", "readOnly": true}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": true}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 37}, "id": 58, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "disableTextWrap": false, "editorMode": "builder", "expr": "xtrem_sql_pool_stats", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "{{poolType}}.{{statName}}", "range": true, "refId": "A", "useBackend": false}], "title": "SQL pool - usage", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 37}, "id": 59, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "disableTextWrap": false, "editorMode": "builder", "expr": "xtrem_sql_pool_retries", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "{{poolType}}.{{attemptName}}", "range": true, "refId": "A", "useBackend": false}], "title": "SQL pool - connection retries", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 45}, "id": 60, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "disableTextWrap": false, "editorMode": "builder", "expr": "xtrem_sql_pool_allocation_queue_length", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "{{poolType}}.{{originId}}", "range": true, "refId": "A", "useBackend": false}], "title": "SQL pool - allocation queue length", "type": "timeseries"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 53}, "id": 54, "panels": [{"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 29}, "id": 52, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "disableTextWrap": false, "editorMode": "builder", "expr": "xtrem_funnel_queue_length", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "{{funnelName}}", "range": true, "refId": "A", "useBackend": false}], "title": "Funnels - queue length", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 29}, "id": 53, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "disableTextWrap": false, "editorMode": "builder", "expr": "xtrem_funnel_queue_ratio", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "{{funnelName}}", "range": true, "refId": "A", "useBackend": false}], "title": "Funnels - queue usage ratio", "type": "timeseries"}], "title": "Funnels", "type": "row"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 54}, "id": 55, "panels": [], "title": "Workers", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 55}, "id": 56, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "disableTextWrap": false, "editorMode": "builder", "expr": "xtrem_worker_threads_stats", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "{{statName}}", "range": true, "refId": "A", "useBackend": false}], "title": "Workers - Threads", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 55}, "id": 57, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "disableTextWrap": false, "editorMode": "builder", "expr": "xtrem_workers_tasks_stats", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "{{statName}}", "range": true, "refId": "A", "useBackend": false}], "title": "Workers - Tasks", "type": "timeseries"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 63}, "id": 34, "panels": [{"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 31}, "id": 32, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "editorMode": "code", "expr": "rate(xtrem_debug_total_millis\r\n[$__rate_interval])", "legendFormat": "{{domain}} {{key}}", "range": true, "refId": "A"}], "title": "Debug - elapsed time", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "reqps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 31}, "id": 30, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "editorMode": "builder", "expr": "rate(xtrem_debug_calls[$__rate_interval])", "legendFormat": "{{domain}} {{key}}", "range": true, "refId": "A"}], "title": "Debug metrics - number of calls", "type": "timeseries"}], "title": "Debug", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 64}, "id": 16, "panels": [{"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 32}, "id": 49, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "editorMode": "builder", "expr": "rate(xtrem_report_generation_sum[$__rate_interval])", "legendFormat": "{{reportName}} {{method}}", "range": true, "refId": "A"}], "title": "Report generation - elapsed time", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "displayName": "${__field.name} s", "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 79.999}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 32}, "id": 44, "options": {"displayMode": "gradient", "minVizHeight": 10, "minVizWidth": 0, "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "text": {}, "valueMode": "color"}, "pluginVersion": "10.1.4", "targets": [{"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "editorMode": "builder", "exemplar": false, "expr": "sum by(le) (round(increase(xtrem_report_generation_bucket{method=\"query_data\"}[$__range])))", "format": "heatmap", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Query data", "type": "bargauge"}, {"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "displayName": "${__field.name} s", "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 79.999}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 32}, "id": 48, "options": {"displayMode": "gradient", "minVizHeight": 10, "minVizWidth": 0, "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "text": {}, "valueMode": "color"}, "pluginVersion": "10.1.4", "targets": [{"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "editorMode": "builder", "exemplar": false, "expr": "sum by(le) (round(increase(xtrem_report_generation_bucket{method=\"generate_pdf\"}[$__range])))", "format": "heatmap", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Generate PDF", "type": "bargauge"}], "title": "Reporting", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 65}, "id": 50, "panels": [{"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 33}, "id": 6, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "editorMode": "builder", "expr": "rate(process_cpu_seconds_total[$__rate_interval])", "legendFormat": "total cpu", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "editorMode": "builder", "exemplar": false, "expr": "rate(process_cpu_system_seconds_total[$__rate_interval])", "hide": false, "instant": false, "legendFormat": "system cpu", "range": true, "refId": "B"}], "title": "Node.js CPU", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 33}, "id": 8, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "editorMode": "builder", "expr": "nodejs_heap_size_total_bytes", "legendFormat": "heap size total", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "editorMode": "builder", "expr": "nodejs_heap_size_used_bytes", "hide": false, "legendFormat": "heap size used", "range": true, "refId": "B"}], "title": "Node.js memory", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": [{"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["mean lag"], "prefix": "All except:", "readOnly": true}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": true}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 41}, "id": 10, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "editorMode": "builder", "expr": "nodejs_eventloop_lag_mean_seconds", "legendFormat": "mean lag", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "editorMode": "builder", "expr": "nodejs_eventloop_lag_p99_seconds", "hide": false, "legendFormat": "p99 lag", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "editorMode": "builder", "expr": "nodejs_eventloop_lag_max_seconds", "hide": false, "legendFormat": "max lag", "range": true, "refId": "C"}], "title": "Node.js event loop", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 41}, "id": 28, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "---xtrem-prometheus---"}, "editorMode": "code", "expr": "rate(nodejs_gc_duration_seconds_sum[$__rate_interval])", "legendFormat": "{{kind}}", "range": true, "refId": "A"}], "title": "Node.js GC", "type": "timeseries"}], "title": "Node.js", "type": "row"}], "refresh": "5s", "revision": 1, "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-15m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "XTreeM Dashboard", "uid": "---xtrem-dashboard---", "version": 26, "weekStart": ""}}