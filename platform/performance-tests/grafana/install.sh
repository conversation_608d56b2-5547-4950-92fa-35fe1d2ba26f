#!/usr/bin/env bash
DIR=$(cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd)

if docker ps | grep grafana/grafana
then
    echo "Skipping install of grafana docker container because container already exists"
    exit 0
fi

echo "Creating grafana docker container"
docker run --add-host=host.docker.internal:host-gateway \
    -d --name=grafana -p 3060:3000 \
    -e "GF_AUTH_ANONYMOUS_ENABLED=true" \
    -e "GF_AUTH_ANONYMOUS_ORG_ROLE=Admin" \
    -e "GF_AUTH_DISABLE_LOGIN_FORM=true" \
    grafana/grafana

num=0
ready=0
while [[ $num -le 30 && $ready -eq 0 ]]; do
	_=$((num++))
    printf '.'
    sleep 1
    if curl -s 'http://localhost:3060' -o /dev/null; then
        printf "\nGrafana server is ready\n"
        ready=1
    fi
done

echo -e "\nAdding prometheus datasource to grafana"
curl -X "POST" "http://localhost:3060/api/datasources" \
     -H "Content-Type: application/json" \
     --user admin:admin \
     -d @"$DIR"/datasources/prometheus.json

echo -e "\nAdding xtrem dashboard to grafana"
curl -X "POST" "http://localhost:3060/api/dashboards/db" \
     -H "Content-Type: application/json" \
     --user admin:admin \
     -d @"$DIR"/dashboards/xtrem-dashboard.json

echo -e "\n"
