#!/usr/bin/env bash

if [[ -z "$1" ]]; then
    echo "Please provide a path to the export zip file to import. (example: '/tmp/export.zip')"
    exit 1
fi

BASE64_OPTS="-w 0"
if [ "$(uname)" = "Darwin" ]; then
    BASE64_OPTS=""
fi

pnpm run xtrem schema --create --reset-schema
pnpm run xtrem tenant --import $(echo -n '{"adminUsers":[{"email":"<EMAIL>","firstName":"Test","lastName":"Test","locale":"en-US"}],"customer":{"id":"aaaaaaaaaaaaaaaaaaaa","name":"SageCustomerTest"},"tenant":{"id":"777777777777777777777","name":"T777777777777777777777"},"location":"'"$1"'","skipWelcomeEmail":true}' | base64 $BASE64_OPTS)
