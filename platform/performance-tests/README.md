# Installing the tools

To install the tools run the ./install.sh script from root folder.

```bash
$(git rev-parse --show-toplevel)/platform/performance-tests/install.sh
```

This script installs:

-   [Artillery:](https://www.artillery.io) the load-testing tool.
-   [Prometheus:](https://prometheus.io) the monitoring server.
-   [Grafana:](https://grafana.com) the dashboarding application

"Artillery" is a load-testing tool. You will use it to run test scenarios that send GraphQL requests to your XTreeM application.

"Prometheus" is a monitoring server. It collects performance metrics from your XTreeM application and it provides a query API that Grafana uses to get its dashboard data.

Prometheus is installed as a docker container. It runs in the background and you don't need to interact directly with it. But if you are curious and want to play with its query language you can access it at http://localhost:9090

Grafana is a dashboarding solution. You will use it to analyze what happened inside your XTreeM application.

Grafana is installed as a docker container. It runs in the background.
You connect to it a http://localhost:3060 to access the performance dashboards.

# Using artillery

## Loading the test data

You have to create a tenant with test data before running artillery tests.

For this, you have to first locate the latest test data file.
This file is produced by the manual-export-tenant or the daily-export-small-tenant pipelines.
Before running the command, you need to get the Export.id of the 'Export tenant' step.
For example, if you want to copy the latest result of the daily-export-small-tenant,
use the following URL https://sage-liveservices.visualstudio.com/X3%20XTREM/_build?definitionId=3433&_a=summary,
and check the 'Export tenant' step : "Export.id=m7zBHgINoHXkSSPG7Ihat"

To download it, run:

```sh
$(git rev-parse --show-toplevel)/platform/performance-tests/copy-export.sh <Export.id> <App name (exp. sdmo)> /tmp/export.zip
```

Then you have to recreate the default test tenant (777777777777777777777) with this data. Run the following commands in your services/main/xtrem-services-main directory:

```sh
cd $(git rev-parse --show-toplevel)/services/main/xtrem-services-main
# Warning: this will reset your existing test data
$(git rev-parse --show-toplevel)/platform/performance-tests/import-zip.sh /tmp/export.zip
```

The import command will only succeed if the version of the export file matches your local version of XTreeM.
This should be the case if your code is in sync with the master branch because the export pipeline is automatically run every night.

If the versions don't match you are out of luck.
You need to have someone deploy a new image in the performance cluster and run the manual-export-tenant pipeline.

## Running an artillery scenario

To run an artillery scenario you have to start your XTreeM application with some special options.
Run the following command from the `services/main/xtrem-services-main` directory:

```sh
cd $(git rev-parse --show-toplevel)/services/main/xtrem-services-main
XTREM_ALL_METRICS_ENABLED=1 pnpm run start:unsecuredevlogin
```

Then you can run your scenario with the following commands:

```bash
    cd $(git rev-parse --show-toplevel)/services/performance-tests
    ../../scripts/artillery/scenarios-test-run.sh  --scenario sales-order/create --total-users 20 --ramp-up 15 --duration 150
```

This will run the "sales-order/create" scenario with 20 concurrent users, a ramp-up time of 15 seconds and a test duration of 150 seconds.
You can adjust these values to your needs.

# Using the Grafana dashboard to view the performance metrics

Grafana will let you explore the performance metrics that have been collected during your test.

Note that performance metrics are collected whenever you run your XTreeM application with the `XTREM_ALL_METRICS_ENABLED=1` environment variable set.
Artillery makes it easy to load the server with a large number of users but metrics will also be collected when you use the application directly with its UI and when you run GraphQL queries from http://localhost:8240/explorer.

## Opening the Grafana dashboard

To view the Grafana dashboard, navigate to http://localhost:3060/d/---xtrem-dashboard---/xtreem-dashboard?orgId=1

To view your data, click on the time icon on the right side of the top banner, and select a time range ("Last 15 minutes" is the default of that dashboard and is the easiest if you just ran your test).

## Dashboard contents

The dashboard contains the following metrics

-   Business rules
-   GraphQL operations
-   Metadata operations
-   SQL queries
-   Node.js CPU, memory and event loop

For the first 3 metrics, you have two graphs: one with elapsed time and one with the number of calls.
The "elapsed time" is the most important as it tells you where your application spent most of its time
but the "number of calls" panel can also help you detect rules or queries that are run at an abnormal rate.

# Technical corner

## Environment variables

The collection of metrics is controlled by the following environment variables:

-   XTREM_ALL_METRICS_ENABLED
-   XTREM_NODEJS_METRICS_ENABLED
-   XTREM_RULE_METRICS_ENABLED
-   XTREM_GRAPHQL_METRICS_ENABLED
-   XTREM_SQL_METRICS_ENABLED

The default value is false. True values are true or 1.

XTREM_ALL_METRICS_ENABLED should be set in dev on local machines. It activates all the metrics.

The NODEJS and SQL metrics are already provided by NewRelic so we should only enable RULE and GRAPHQL in our clusters.

## Endpoints

The Prometheus scraping endpoint of the XTreeM application is /metrics on port 8250.

On local machines:

-   the Prometheus server is on port 9090
-   the Graphana application is on port 3060

## Reset Grafana dashboard

If you have previously installed the Grafana docker container and you do not have the Metadata metrics in the dashboard, you can reset it by running:

```bash
platform/performance-tests/grafana/reset.sh
```

# Using import cvs

## Run import for the small data set

Download the small data set:

```sh
platform/performance-tests/tools/download-import-csv-small.sh
```

```sh
download: s3://xtrem-developers-utility/performance-tests/import-csv-artifacts/import-csv-small.tgz to platform/performance-tests/tools/import/test/data/import-csv-small.tgz
extracting content to platform/performance-tests/tools/import/test/data/import-csv-small...
done!
```

Run the import test (xtrem-services-main must be built but not started):

```sh
platform/performance-tests/tools/import-csv-small.sh
```

## Fixing import for the small data set

If the import failed:

-   check the content of the csv files in `platform/performance-tests/tools/import/test/data/import-csv-small/`
-   test the modified files
-   upload the new content once the test succeeded with the command below

```sh
platform/performance-tests/tools/upload-import-csv-small.sh
```
