#!/usr/bin/env bash

DIR=$(dirname "$0")

set -e

isCI() {
    # azure || github actions || other azure detection || others
    if [[ "${TF_BUILD,,}" == "true"  ||  -n $BUILD_BUILDID || -n $BUILD_ID ]]; then
        return 0
    fi
    return 1
}

logError() {
    echo -e "\n$1"
    if isCI; then
        echo "##vso[task.logissue type=error]$1"
    fi
}

exit_code=0

# install the dependencies if not yet done
if [ ! -d "$DIR"/node_modules ]; then
    pnpm install --dir "${DIR}" --ignore-workspace
fi

if isCI; then
    echo "##[group]import CSV files in tenant ${TENANT_ID}"
    echo "##[command]pnpm run --dir ${DIR} import" "$@"
fi
pnpm run --dir "${DIR}" import "$@" || _=$((exit_code += $?))

if isCI; then
    echo "##[endgroup]"
fi
if [[ $exit_code -ne 0 ]]; then
    logError "Some imports failed, please check the output result"
else
    echo -e "\nAll imports completed successfully"
fi
exit $exit_code
