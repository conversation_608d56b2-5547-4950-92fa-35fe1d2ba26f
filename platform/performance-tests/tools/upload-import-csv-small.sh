#!/usr/bin/env bash

set -e

DIR=$(dirname "$0")
IMPORT_TEST_DATA_DIR="${DIR}/import/test/data"

if [[ ! -d "${IMPORT_TEST_DATA_DIR}/import-csv-small" ]]; then
    echo "${IMPORT_TEST_DATA_DIR}/import-csv-small does not exist"
    exit 1
fi

rm -f "${IMPORT_TEST_DATA_DIR}/import-csv-small.tgz"
cd "${IMPORT_TEST_DATA_DIR}/import-csv-small"

echo "Creating archive ${IMPORT_TEST_DATA_DIR}/import-csv-small.tgz"
tar -czvf ../import-csv-small.tgz ./
cd -

aws s3 cp "${IMPORT_TEST_DATA_DIR}/import-csv-small.tgz" s3://xtrem-developers-utility/performance-tests/import-csv-artifacts/import-csv-small.tgz
rm -f "${IMPORT_TEST_DATA_DIR}/import-csv-small.tgz"

if [[ "$1" == "clean" ]]; then
    echo "Cleaning ${IMPORT_TEST_DATA_DIR}/import-csv-small"
    rm -rf "${IMPORT_TEST_DATA_DIR}/import-csv-small"
fi
