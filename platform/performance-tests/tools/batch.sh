#!/usr/bin/env bash

#=======================================================================================================
# TODO: create a dedicated tool published on npm to avoid this hack for installing the required packages
#=======================================================================================================

DIR=$(dirname "$0")

set -e

exit_code=0

# install the dependencies if not yet done
if [ ! -d "$DIR"/node_modules ]; then
    pnpm install --dir "${DIR}" --ignore-workspace
fi

if [[ -n $BUILD_BUILDID ]]; then
    echo "##[group]execute async mutation in tenant ${TENANT_ID}"
    echo "##[command]pnpm run --dir ${DIR} batch" "$@"
fi
pnpm run --dir "${DIR}" batch "$@" || _=$((exit_code += $?))

if [[ -n $BUILD_BUILDID ]]; then
    echo "##[endgroup]"
fi
exit $exit_code
