#!/usr/bin/env bash

DIR=$(dirname "$0")
IMPORT_TEST_DATA_DIR="${DIR}/import/test/data"

XTREM_ROOT_DIR=${XTREM_ROOT_DIR:="$(git rev-parse --show-toplevel)"}

if ! time "$XTREM_ROOT_DIR/scripts/import-csv/import-csv.sh" \
        --import-dir "$IMPORT_TEST_DATA_DIR/import-csv-small" \
        --polling-millis 5000 \
        --timeout-seconds 900 ; then
    echo -e "\n\n    Please, check the content of csv in the import-csv-small folder"
    echo -e "    If any change is required, fix the content then run the following script to upload the content to aws:"
    echo -e "\n    platform/performance-tests/tools/upload-import-csv-small.sh\n"
fi
