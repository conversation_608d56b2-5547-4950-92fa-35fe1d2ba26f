import * as fs from 'node:fs/promises';
import * as fsp from 'node:path';
import { promisify } from 'node:util';
import { batchArgs } from '../batch/utils';
import { importArgs } from '../import/utils';

export const argv = (() => {
    if (process.argv[1].includes('batch')) {
        return batchArgs.argv;
    }
    return importArgs.argv;
})();

export interface Logger {
    info: (message: string, icon?: string) => void;
    details: (message: string) => void;
    verbose: (cb: () => string) => void;
    debug: (cb: () => string) => void;
    success: (message: string) => void;
    warn: (message: string) => void;
    fail: (message: string, err?: Error | any) => void;
}

const logLevel = ['info', 'verbose', 'debug'].indexOf(argv.logLevel);

export const logger: Logger = {
    info(message: string, icon = icons.info) {
        console.log(` ${icon} ${message}`);
    },
    details(message: string) {
        console.log(message);
    },
    verbose(cb: () => string) {
        if (logLevel > 0) {
            console.log(` ${icons.verbose} ${cb()}`);
        }
    },
    debug(cb: () => string) {
        if (logLevel > 1) {
            console.log(` ${icons.debug} ${cb()}`);
        }
    },
    success(message: string) {
        console.log(` ${icons.success} ${message}`);
    },
    warn(message: string) {
        console.log(` ${icons.warn} ${message}`);
    },
    fail(message: string, err?: Error | any) {
        console.error(` ${icons.error} ${message}${err ? `: ${logLevel > 1 ? err.stack : err.message}` : ''}`);
    },
};

export const icons = {
    info: '📢',
    verbose: '💬',
    success: '✅',
    warn: '⚠️',
    error: '❌',
    debug: '🐛',
    polling: '☕',
    waiting: '⏳',
};

function camelCase(name: string) {
    return name
        .split('-')
        .map((p, i) => (i === 0 ? p : `${p[0].toUpperCase()}${p.slice(1)}`))
        .join('');
}

export function pascalCase(name: string) {
    const camelName = camelCase(name);
    return `${camelName[0].toUpperCase()}${camelName.slice(1)}`;
}

export async function sleepMillis(ms: number): Promise<void> {
    await promisify(setTimeout)(ms);
}
export interface ExecutionResult {
    _id: number;
    filename?: string;
    template?: string;
    rows?: {
        processed: number;
        errors: number;
    };
    resultTiming?: Timing;
}

export interface ArtilleryReport {
    aggregate: {
        counters: {
            'http.requests': number;
            'vusers.failed': number;
            'vusers.completed': number;
        };
        summaries: {
            'http.response_time': {
                min: number;
                max: number;
                count?: number;
                p50?: number;
                median: number;
                p75?: number;
                p90?: number;
                p95: number;
                p99?: number;
                p999?: number;
            };
        };
    };
}

export interface SaveResultOptions {
    reportDir?: string;
    artilleryReport?: boolean;
}

export interface ExecutionResultBase {
    _id: number;
    filename?: string;
    timings?: Record<string, Timing>;
    completed?: number;
    failed?: number;
    responseTime?: {
        min?: number;
        max?: number;
        median?: number;
        p95?: number;
    };
}

export class Timing {
    startStamp: number;
    endStamp: number;
    elapsedInMillis: number;
    start(): Timing {
        this.startStamp = Date.now();
        return this;
    }
    end(): Timing {
        return this._end(Date.now());
    }

    private _end(endStamp: number): Timing {
        this.endStamp = endStamp;
        this.elapsedInMillis = this.endStamp - this.startStamp;
        return this;
    }

    static fromTimestamp(startStamp: string | undefined, endStamp: string | undefined): Timing {
        const timing = new Timing();
        timing.startStamp = startStamp ? Date.parse(startStamp) : 0;
        return timing._end(endStamp ? Date.parse(endStamp) : Date.now());
    }

    get elapsed(): string {
        const ms = this.elapsedInMillis;
        const rh = ms % (60 * 60 * 1000);
        const h = ((ms - rh) / (60 * 60 * 1000)) | 0;
        const rm = rh % (60 * 1000);
        const m = ((rh - rm) / (60 * 1000)) | 0;
        const rs = rm % 1000;
        const s = ((rm - rs) / 1000) | 0;
        return `${h}h ${m}m ${s}.${String(rs).padStart(3, '0')}s`;
    }
}

export interface ExecutionResult {
    _id: number;
    filename?: string;
    counters: {
        requests?: number;
        completed?: number;
        failed?: number;
    };
    responseTime?: {
        min?: number;
        max?: number;
        median?: number;
        p95?: number;
    };
}

export function convertToArtilleryFormat(result: ExecutionResult) {
    const artillery: ArtilleryReport = {
        aggregate: {
            counters: {
                'http.requests': result?.counters?.requests || 0,
                'vusers.completed': result?.counters?.completed || 0,
                'vusers.failed': result?.counters?.failed || 0,
            },
            summaries: {
                'http.response_time': {
                    min: result?.responseTime?.min || 0,
                    max: result?.responseTime?.max || 0,
                    median: result?.responseTime?.median || 0,
                    p95: result?.responseTime?.p95 || 0,
                },
            },
        },
    };
    return artillery;
}

export async function saveResult(result: ExecutionResult, options: SaveResultOptions) {
    const { reportDir, artilleryReport } = options;
    const reportDirPath = fsp.resolve(reportDir || 'reports');
    try {
        await fs.mkdir(reportDirPath);
    } catch (e) {
        if (e.code !== 'EEXIST') throw e;
    }
    // remove the sequence number from basename
    const basename = result.filename ? fsp.basename(result.filename).replace(/^\d+-/, '') : 'result';
    const stamp = new Date().toISOString().split('.')[0].replace(/:/g, '-');
    const fileStamp = stamp.replace(/-/g, '_');
    const prefix = argv.prefix ? `${argv.prefix}-` : '';
    const filename = `${prefix}import-${basename.replace(fsp.extname(basename), '')}-${fileStamp}`;
    if (artilleryReport) {
        const artilleryFile = fsp.join(reportDirPath, `${filename}-TOTAL_USERS-1-RAMP_UP-1-DURATION-1.json`);
        fs.writeFile(artilleryFile, JSON.stringify(convertToArtilleryFormat(result), null, 2), 'utf8');
        logger.info(`result (artillery format) saved in ${artilleryFile}`);
    } else {
        const resultFile = fsp.join(reportDirPath, `${filename}-result.json`);
        fs.writeFile(resultFile, JSON.stringify(result, null, 2), 'utf8');
        logger.info(`result (full) saved in ${resultFile}`);
    }
}

export const isDeepEqual = (valueToCompare: Object | string, value: Object | string) => {
    if (!isObject(valueToCompare)) {
        return valueToCompare === value;
    }
    if (!isObject(value)) {
        return false;
    }
    const valueToCompareKeys = Object.keys(valueToCompare);
    const valueKeys = Object.keys(value);

    if (valueToCompareKeys.length !== valueKeys.length) return false;

    for (var key of valueToCompareKeys) {
        if (!isDeepEqual(valueToCompare[key], value[key])) {
            return false;
        }
    }
    return true;
};

const isObject = object => {
    return object != null && typeof object === 'object';
};
