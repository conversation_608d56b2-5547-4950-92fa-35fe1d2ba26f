import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import { logger } from './utils';

export interface ImportExportTemplate extends Node {
    id?: string;
    name?: string;
    nodeName?: string;
}

export interface LoginOptions {
    email?: string;
    tenant?: string;
    cluster?: string;
    loginHost?: string;
}

export interface LoginResult {
    jwtToken?: AccessToken;
    location?: string;
}

export interface AccessToken {
    accessToken: string; // the access_token cookie
    accessTokenSign: string; // the access_token_sign cookie
}

export interface ApiClientOptions extends LoginOptions {
    targetHost?: string;
    locale?: string;
}

export interface PollingOptions {
    timeoutMillis?: number;
    pollingMillis?: number;
}

interface Node {
    _id?: string;
}

export interface ImportResult extends Node {
    startStamp?: string;
    endStamp?: string;
    filename?: string;
    rowsProcessed?: number;
    numberOfRowsInError?: number;
    status?: string;
    dryRun?: boolean;
    doUpdate?: boolean;
    doInsert?: boolean;
    maxErrorCount?: number;
    uploadRejectReason?: string;
    generalError?: string;
}

export interface Diagnosis {
    severity?: number;
    message?: string;
    path?: string[];
}

export interface Location {
    line: number;
    column: number;
}

export interface ResponseExtensions {
    code?: string;
    diagnoses: Diagnosis[];
}

export interface ResponseError {
    message?: string;
    locations?: Location[];
    path?: string[];
    extensions?: ResponseExtensions;
}
export interface Response<T> {
    status?: number;
    statusText?: string;
    data?: T;
    errors?: ResponseError[];
    extensions?: ResponseExtensions;
}

export const delay = (ms: number): Promise<any> =>
    new Promise(resolve => {
        setTimeout(resolve, ms);
    });

function getCookie(response: any, cookieName: string): string {
    const cookie = response.headers?.['set-cookie']?.join();
    if (!cookie) {
        throw new Error(`No cookie available`);
    }
    const re = new RegExp(`(?<=${cookieName}=)[^;]*`);
    try {
        return cookie.match(re)[0];
    } catch (e: any) {
        throw new Error(`Token ${cookieName} does not exist`);
    }
}

export function logRequest(message: string) {
    logger.verbose(() => `${new Date().toISOString()} SEND ${message}`);
}

export function logResponse(resp: AxiosResponse<any, any>) {
    logger.verbose(() => `${new Date().toISOString()} RECV ${resp.status} ${resp.statusText}`);
    logResponseHeaders(resp);
    logResponseData(resp);
}

const logResponseHeaders = (resp: AxiosResponse<any, any>) =>
    logger.debug(
        () =>
            `headers:\n${Object.entries(resp.headers)
                .map(([k, v]) => `${k}: ${v}`)
                .join('\n')}`,
    );

const logResponseData = (resp: AxiosResponse<any, any>) =>
    logger.verbose(() => `data:\n${JSON.stringify(resp.data, null, 2)}`);

async function get(url: string, config?: AxiosRequestConfig<any>): Promise<any> {
    logRequest(`GET ${url}`);
    const resp = await axios.get(url, config);
    logResponse(resp);
    return resp;
}

async function requestLogin(_options: LoginOptions): Promise<LoginResult> {
    const { email, tenant, cluster, loginHost } = _options;
    if (!email) {
        throw new Error('email is required');
    }
    const finalTenant = tenant || '7'.repeat(21);
    let url;
    if (!cluster || cluster === 'local') {
        url = new URL(loginHost || 'http://localhost:8240');
    } else {
        url = new URL(loginHost || 'https://login.eu.dev-sagextrem.com');
        url.searchParams.append('cluster', cluster);
    }
    url.pathname = '/unsecuredevlogin';
    url.searchParams.append('tenant', finalTenant);
    url.searchParams.append('user', email);
    url.searchParams.append('app', 'sdmo');

    const resp = await get(url.toString(), {
        maxRedirects: 0,
        validateStatus: status => status < 400,
    });
    const accessToken = getCookie(resp, 'access_token');
    const accessTokenSign = getCookie(resp, 'access_token_sign');
    return {
        jwtToken: {
            accessToken, // access_token
            accessTokenSign, // access_token_sign
        },
        location: resp?.headers?.location,
    };
}

export function makeGraphQlResponse<T>(resp: AxiosResponse<any, any>, data: T): Response<T> {
    return {
        status: resp.status,
        statusText: resp.statusText,
        data,
        errors: resp.data?.errors,
        extensions: resp.data?.extensions,
    };
}

function RequestError(errors: ResponseError[]) {
    return new Error(`request ended with errors!
    ${errors.map(e => `- ${e.message} at ${e.locations?.[0].line}:${e.locations?.[0].column}`).join('\n')}`);
}

export class ApiClientBase {
    #jwtToken?: AccessToken;
    #location?: string;
    #apiUrl?: string;
    #locale: string;

    constructor(private _options: ApiClientOptions) {
        if (!this._options.locale || typeof this._options.locale !== 'string') {
            this.#locale = 'en-US';
        } else {
            this.#locale = this._options.locale;
        }
    }

    get locale(): string {
        return this.#locale;
    }

    async requestWithLoginRetry(config: AxiosRequestConfig<any>): Promise<AxiosResponse<any, any>> {
        const resp = await axios(config);
        if (resp?.status === 401) {
            await this.login();
            if (this.#jwtToken) {
                config.headers = config.headers ?? {};
                config.headers.cookie = `access_token=${this.#jwtToken.accessToken}; access_token_sign=${
                    this.#jwtToken.accessTokenSign
                }`;
            }
            return axios(config);
        }
        return resp;
    }

    async postGraphQl(query: string): Promise<AxiosResponse<any, any>> {
        const url = this.#apiUrl;
        logRequest(`POST ${url}`);
        const headers = {
            accept: 'application/json',
            'accept-language': this.locale,
        } as Record<string, string>;
        if (this.#jwtToken) {
            headers.cookie = `access_token=${this.#jwtToken.accessToken}; access_token_sign=${
                this.#jwtToken.accessTokenSign
            }`;
        }
        const resp = await this.requestWithLoginRetry({
            method: 'POST',
            headers,
            url,
            data: {
                query: query,
            },
            validateStatus: status => status < 500,
        });

        logResponse(resp);

        const errors = resp.data?.errors as ResponseError[];
        if (errors?.length > 0) {
            throw RequestError(errors);
        }
        return resp;
    }

    async login() {
        const { email, targetHost } = this._options;
        if (!email) {
            logger.info('no email provided, this can work only on local dev mode!');
            this.#location = targetHost || 'http://localhost:8240';
        } else {
            const loginResult = await requestLogin(this._options);
            this.#jwtToken = loginResult.jwtToken;
            this.#location = loginResult.location;
            logger.success(`logged in with ${this._options.email}`);
        }
        logger.info(`host is ${this.#location}`);
        this.#apiUrl = `${this.#location}/api`;
    }
}
