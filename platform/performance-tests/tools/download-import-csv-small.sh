#!/usr/bin/env bash

set -e

DIR=$(dirname "$0")
IMPORT_TEST_DATA_DIR="${DIR}/import/test/data"

aws s3 cp s3://xtrem-developers-utility/performance-tests/import-csv-artifacts/import-csv-small.tgz "${IMPORT_TEST_DATA_DIR}"

rm -rf "${IMPORT_TEST_DATA_DIR}/import-csv-small"
mkdir -p "${IMPORT_TEST_DATA_DIR}/import-csv-small"

echo "extracting content to ${IMPORT_TEST_DATA_DIR}/import-csv-small..."
tar -xzf "${IMPORT_TEST_DATA_DIR}/import-csv-small.tgz" --directory "${IMPORT_TEST_DATA_DIR}/import-csv-small"
rm -f "${IMPORT_TEST_DATA_DIR}/import-csv-small.tgz"

echo "done!"
