import * as fsp from 'node:path';
import { argv, logger } from '../utils/utils';
import { ApiClient } from './api-client';
import { ImportSession } from './import';
import { importArgs } from './utils';

async function main() {
    console.log('');
    const {
        importFile,
        importDir,
        importExportTemplate,
        locale,
        loginHost,
        targetHost,
        cluster,
        tenant,
        email,
        timeoutSeconds,
        pollingMillis,
        reportDir,
        artilleryReport,
    } = argv;

    if (!importDir && !importFile) {
        logger.fail('Either --import-dir or --import-file option is required. See help.');
        importArgs.showHelp();
        process.exit();
    }
    const apiClient = new ApiClient({ targetHost, email, tenant, cluster, locale, loginHost });
    const importSession = new ImportSession(apiClient, {
        timeoutMillis: (timeoutSeconds | 0) * 1000,
        pollingMillis,
        reportDir,
        artilleryReport,
    });
    let results;
    await importSession.run(async session => {
        if (importDir) {
            results = await session.importFlatFilesFromDir(importDir);
        } else {
            results = [
                await session.importSingleFlatFile({
                    importDir: fsp.dirname(importFile),
                    filename: fsp.basename(importFile),
                    template: importExportTemplate,
                }),
            ];
        }
    });

    importSession.showTimings();
    if (results.some(r => r.errorCount > 0)) {
        process.exit(1);
    }
}

main().catch(e => {
    console.log('');
    logger.fail('ERROR:', e);
    process.exit(1);
});
