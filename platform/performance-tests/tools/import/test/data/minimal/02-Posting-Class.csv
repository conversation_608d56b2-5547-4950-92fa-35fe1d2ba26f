!id;*type;*name;isDetailed;isStockItemAllowed;isNonStockItemAllowed;isServiceItemAllowed;#lines;chartOfAccount;*definition;*account;
string;enum(item,supplier,customer,tax,company,header,line,resource);localized text;boolean;boolean;boolean;boolean;collection;reference;reference;reference;IGNORE
id;type;"default locale: base, other locales can be specified with (locale-name), for example ""name (de-DE)"". Available locales are (en-GB, fr-FR, ...). You can also duplicate the columns to import several translations";is detailed (true/false);is stock item allowed (false/true);is non stock item allowed (false/true);is service item allowed (false/true);lines;chart of account (#id);definition (#legislation|postingClassType|id);account (#id|chartOfAccount);IGNORE
COMPANY_DEFAULT;company;Default;FALSE;FALSE;FALSE;FALSE;1;GB_DEFAULT;GB|company|DebtorRoundingVariance;70500|GB_DEFAULT;
COMPANY_DEFAULT;company;Default;FALSE;FALSE;FALSE;FALSE;2;GB_DEFAULT;GB|company|CreditorRoundingVariance;70500|GB_DEFAULT;
COMPANY_DEFAULT;company;Default;FALSE;FALSE;FALSE;FALSE;3;FR_DEFAULT;FR|company|DebtorRoundingVariance;********|FR_DEFAULT;
COMPANY_DEFAULT;company;Default;FALSE;FALSE;FALSE;FALSE;4;FR_DEFAULT;FR|company|CreditorRoundingVariance;********|FR_DEFAULT;
COMPANY_DEFAULT;company;Default;FALSE;FALSE;FALSE;FALSE;5;ZA_DEFAULT;ZA|company|DebtorRoundingVariance;70500|ZA_DEFAULT;
COMPANY_DEFAULT;company;Default;FALSE;FALSE;FALSE;FALSE;6;ZA_DEFAULT;ZA|company|CreditorRoundingVariance;70500|ZA_DEFAULT;
CUSTOMER_DEFAULT;customer;Default;FALSE;FALSE;FALSE;FALSE;1;GB_DEFAULT;GB|customer|Ar;12100|GB_DEFAULT;
CUSTOMER_DEFAULT;customer;Default;FALSE;FALSE;FALSE;FALSE;2;US_DEFAULT;US|customer|Ar;12100|US_DEFAULT;
CUSTOMER_DEFAULT;customer;Default;FALSE;FALSE;FALSE;FALSE;3;FR_DEFAULT;FR|customer|ArGsni;41810000|FR_DEFAULT;
CUSTOMER_DEFAULT;customer;Default;FALSE;FALSE;FALSE;FALSE;4;ZA_DEFAULT;ZA|customer|Ar;12100|ZA_DEFAULT;
CUSTOMER_DOMESTIC;customer;Customers;TRUE;FALSE;FALSE;FALSE;1;FR_DEFAULT;FR|customer|Ar;41110000|FR_DEFAULT;
FR_TVA_NORMAL_COLLECTED_ON_DEBITS;tax;Normal rate collected on debits;TRUE;FALSE;FALSE;FALSE;1;FR_DEFAULT;FR|tax|Vat;44571110|FR_DEFAULT;
FR_TVA_NORMAL_COLLECTED_ON_PAYMENT;tax;Normal rate deductible fixed assets intrastat;TRUE;FALSE;FALSE;FALSE;1;FR_DEFAULT;FR|tax|Vat;44571210|FR_DEFAULT;
FR_TVA_NORMAL_DEDUCTIBLE_INTRASTAT;tax;Normal rate deductible intrastat;TRUE;FALSE;FALSE;FALSE;1;FR_DEFAULT;FR|tax|Vat;44566290|FR_DEFAULT;
FR_TVA_NORMAL_DEDUCTIBLE_ON_DEBITS;tax;Normal rate deductible on debits;TRUE;FALSE;FALSE;FALSE;1;FR_DEFAULT;FR|tax|Vat;44566110|FR_DEFAULT;
FR_TVA_NORMAL_DEDUCTIBLE_ON_PAYMENT;tax;Normal rate deductible on payment;TRUE;FALSE;FALSE;FALSE;1;FR_DEFAULT;FR|tax|Vat;44566210|FR_DEFAULT;
FR_TVA_REDUCED_COLLECTED_ON_DEBITS;tax;Reduced rate collected on debits;TRUE;FALSE;FALSE;FALSE;1;FR_DEFAULT;FR|tax|Vat;44571130|FR_DEFAULT;
FR_TVA_REDUCED_COLLECTED_ON_PAYMENT;tax;Reduced rate collected on payment;TRUE;FALSE;FALSE;FALSE;1;FR_DEFAULT;FR|tax|Vat;44571230|FR_DEFAULT;
FR_TVA_REDUCED_DEDUCTIBLE_ON_DEBITS;tax;Reduced rate deductible on debits;TRUE;FALSE;FALSE;FALSE;1;FR_DEFAULT;FR|tax|Vat;44566130|FR_DEFAULT;
FR_TVA_REDUCED_DEDUCTIBLE_ON_PAYMENT;tax;Reduced rate deductible on payment;TRUE;FALSE;FALSE;FALSE;1;FR_DEFAULT;FR|tax|Vat;44566230|FR_DEFAULT;
ITEM_DEFAULT;item;Default;FALSE;FALSE;FALSE;FALSE;1;GB_DEFAULT;GB|item|GoodsReceivedNotInvoiced;20680|GB_DEFAULT;
ITEM_DEFAULT;item;Default;FALSE;FALSE;FALSE;FALSE;2;GB_DEFAULT;GB|item|StockIssue;51800|GB_DEFAULT;
ITEM_DEFAULT;item;Default;FALSE;FALSE;FALSE;FALSE;3;GB_DEFAULT;GB|item|CostOfGoods;50100|GB_DEFAULT;
ITEM_DEFAULT;item;Default;FALSE;FALSE;FALSE;FALSE;4;GB_DEFAULT;GB|item|ShippedNotInvoiced;12400|GB_DEFAULT;
ITEM_DEFAULT;item;Default;FALSE;FALSE;FALSE;FALSE;5;GB_DEFAULT;GB|item|WorkInProgress;13700|GB_DEFAULT;
ITEM_DEFAULT;item;Default;FALSE;FALSE;FALSE;FALSE;6;GB_DEFAULT;GB|item|StockAdjustment;51800|GB_DEFAULT;
ITEM_DEFAULT;item;Default;FALSE;FALSE;FALSE;FALSE;7;GB_DEFAULT;GB|item|StockReceipt;51800|GB_DEFAULT;
ITEM_DEFAULT;item;Default;FALSE;FALSE;FALSE;FALSE;8;GB_DEFAULT;GB|item|PayableNotInvoiced;20680|GB_DEFAULT;
ITEM_DEFAULT;item;Default;FALSE;FALSE;FALSE;FALSE;9;US_DEFAULT;US|item|GoodsReceivedNotInvoiced;20680|US_DEFAULT;
ITEM_DEFAULT;item;Default;FALSE;FALSE;FALSE;FALSE;10;US_DEFAULT;US|item|StockIssue;50200|US_DEFAULT;
ITEM_DEFAULT;item;Default;FALSE;FALSE;FALSE;FALSE;11;US_DEFAULT;US|item|CostOfGoods;50100|US_DEFAULT;
ITEM_DEFAULT;item;Default;FALSE;FALSE;FALSE;FALSE;12;US_DEFAULT;US|item|ShippedNotInvoiced;12400|US_DEFAULT;
ITEM_DEFAULT;item;Default;FALSE;FALSE;FALSE;FALSE;13;US_DEFAULT;US|item|WorkInProgress;13700|US_DEFAULT;
ITEM_DEFAULT;item;Default;FALSE;FALSE;FALSE;FALSE;14;US_DEFAULT;US|item|StockAdjustment;50100|US_DEFAULT;
ITEM_DEFAULT;item;Default;FALSE;FALSE;FALSE;FALSE;15;US_DEFAULT;US|item|PayableNotInvoiced;20680|US_DEFAULT;
ITEM_DEFAULT;item;Default;FALSE;FALSE;FALSE;FALSE;16;US_DEFAULT;US|item|StockReceipt;51800|US_DEFAULT;
ITEM_DEFAULT;item;Default;FALSE;FALSE;FALSE;FALSE;17;ZA_DEFAULT;ZA|item|GoodsReceivedNotInvoiced;20680|ZA_DEFAULT;
ITEM_DEFAULT;item;Default;FALSE;FALSE;FALSE;FALSE;18;ZA_DEFAULT;ZA|item|StockIssue;51800|ZA_DEFAULT;
ITEM_DEFAULT;item;Default;FALSE;FALSE;FALSE;FALSE;19;ZA_DEFAULT;ZA|item|CostOfGoods;50100|ZA_DEFAULT;
ITEM_DEFAULT;item;Default;FALSE;FALSE;FALSE;FALSE;20;ZA_DEFAULT;ZA|item|ShippedNotInvoiced;12400|ZA_DEFAULT;
ITEM_DEFAULT;item;Default;FALSE;FALSE;FALSE;FALSE;21;ZA_DEFAULT;ZA|item|WorkInProgress;13700|ZA_DEFAULT;
ITEM_DEFAULT;item;Default;FALSE;FALSE;FALSE;FALSE;22;ZA_DEFAULT;ZA|item|StockAdjustment;51800|ZA_DEFAULT;
ITEM_DEFAULT;item;Default;FALSE;FALSE;FALSE;FALSE;23;ZA_DEFAULT;ZA|item|StockReceipt;51800|ZA_DEFAULT;
ITEM_DEFAULT;item;Default;FALSE;FALSE;FALSE;FALSE;24;ZA_DEFAULT;ZA|item|PayableNotInvoiced;20680|ZA_DEFAULT;
ITEM_DEFAULT;item;Default;FALSE;FALSE;FALSE;FALSE;25;US_DEFAULT;US|item|ProductionVariance;59100|US_DEFAULT;
ITEM_DEFAULT;item;Default;FALSE;FALSE;FALSE;FALSE;26;GB_DEFAULT;GB|item|ProductionVariance;59100|GB_DEFAULT;
ITEM_DEFAULT;item;Default;FALSE;FALSE;FALSE;FALSE;27;ZA_DEFAULT;ZA|item|ProductionVariance;59100|ZA_DEFAULT;
ITEM_GOODS_NORMAL;item;Materials and Finished goods (Normal);TRUE;TRUE;TRUE;TRUE;1;GB_DEFAULT;GB|item|Stock;13100|GB_DEFAULT;
ITEM_GOODS_NORMAL;item;Materials and Finished goods (Normal);TRUE;TRUE;TRUE;TRUE;2;GB_DEFAULT;GB|item|SalesRevenueAccrual;41900|GB_DEFAULT;
ITEM_GOODS_NORMAL;item;Materials and Finished goods (Normal);TRUE;TRUE;TRUE;TRUE;3;GB_DEFAULT;GB|item|SalesRevenue;40900|GB_DEFAULT;
ITEM_GOODS_NORMAL;item;Materials and Finished goods (Normal);TRUE;TRUE;TRUE;TRUE;4;GB_DEFAULT;GB|item|PurchaseVariance;53100|GB_DEFAULT;
ITEM_GOODS_NORMAL;item;Materials and Finished goods (Normal);TRUE;TRUE;TRUE;TRUE;5;US_DEFAULT;US|item|Stock;13100|US_DEFAULT;
ITEM_GOODS_NORMAL;item;Materials and Finished goods (Normal);TRUE;TRUE;TRUE;TRUE;6;US_DEFAULT;US|item|SalesRevenueAccrual;41900|US_DEFAULT;
ITEM_GOODS_NORMAL;item;Materials and Finished goods (Normal);TRUE;TRUE;TRUE;TRUE;7;US_DEFAULT;US|item|SalesRevenue;40900|US_DEFAULT;
ITEM_GOODS_NORMAL;item;Materials and Finished goods (Normal);TRUE;TRUE;TRUE;TRUE;8;US_DEFAULT;US|item|PurchaseVariance;53100|US_DEFAULT;
ITEM_GOODS_NORMAL;item;Materials and Finished goods (Normal);TRUE;TRUE;TRUE;TRUE;9;FR_DEFAULT;FR|item|Stock;31100000|FR_DEFAULT;
ITEM_GOODS_NORMAL;item;Materials and Finished goods (Normal);TRUE;TRUE;TRUE;TRUE;10;FR_DEFAULT;FR|item|StockVariation;60310000|FR_DEFAULT;
ITEM_GOODS_NORMAL;item;Materials and Finished goods (Normal);TRUE;TRUE;TRUE;TRUE;11;FR_DEFAULT;FR|item|PurchaseExpense;60110110|FR_DEFAULT;
ITEM_GOODS_NORMAL;item;Materials and Finished goods (Normal);TRUE;TRUE;TRUE;TRUE;12;FR_DEFAULT;FR|item|SalesRevenue;70100110|FR_DEFAULT;
ITEM_GOODS_NORMAL;item;Materials and Finished goods (Normal);TRUE;TRUE;TRUE;TRUE;13;FR_DEFAULT;FR|item|StockVariationRevenue;71355000|FR_DEFAULT;
ITEM_GOODS_NORMAL;item;Materials and Finished goods (Normal);TRUE;TRUE;TRUE;TRUE;14;ZA_DEFAULT;ZA|item|Stock;13100|ZA_DEFAULT;
ITEM_GOODS_NORMAL;item;Materials and Finished goods (Normal);TRUE;TRUE;TRUE;TRUE;15;ZA_DEFAULT;ZA|item|SalesRevenueAccrual;41900|ZA_DEFAULT;
ITEM_GOODS_NORMAL;item;Materials and Finished goods (Normal);TRUE;TRUE;TRUE;TRUE;16;ZA_DEFAULT;ZA|item|SalesRevenue;40900|ZA_DEFAULT;
ITEM_GOODS_NORMAL;item;Materials and Finished goods (Normal);TRUE;TRUE;TRUE;TRUE;17;ZA_DEFAULT;ZA|item|PurchaseVariance;53100|ZA_DEFAULT;
ITEM_GOODS_REDUCED;item;Materials (Reduced);TRUE;TRUE;TRUE;TRUE;1;GB_DEFAULT;GB|item|Stock;13900|GB_DEFAULT;
ITEM_GOODS_REDUCED;item;Materials (Reduced);TRUE;TRUE;TRUE;TRUE;2;GB_DEFAULT;GB|item|SalesRevenueAccrual;41900|GB_DEFAULT;
ITEM_GOODS_REDUCED;item;Materials (Reduced);TRUE;TRUE;TRUE;TRUE;3;GB_DEFAULT;GB|item|SalesRevenue;40900|GB_DEFAULT;
ITEM_GOODS_REDUCED;item;Materials (Reduced);TRUE;TRUE;TRUE;TRUE;4;GB_DEFAULT;GB|item|PurchaseVariance;53100|GB_DEFAULT;
ITEM_GOODS_REDUCED;item;Materials (Reduced);TRUE;TRUE;TRUE;TRUE;5;US_DEFAULT;US|item|Stock;13900|US_DEFAULT;
ITEM_GOODS_REDUCED;item;Materials (Reduced);TRUE;TRUE;TRUE;TRUE;6;FR_DEFAULT;FR|item|Stock;32100000|FR_DEFAULT;
ITEM_GOODS_REDUCED;item;Materials (Reduced);TRUE;TRUE;TRUE;TRUE;7;FR_DEFAULT;FR|item|StockVariation;60320000|FR_DEFAULT;
ITEM_GOODS_REDUCED;item;Materials (Reduced);TRUE;TRUE;TRUE;TRUE;8;FR_DEFAULT;FR|item|PurchaseExpense;60110120|FR_DEFAULT;
ITEM_GOODS_REDUCED;item;Materials (Reduced);TRUE;TRUE;TRUE;TRUE;9;FR_DEFAULT;FR|item|SalesRevenue;70100120|FR_DEFAULT;
ITEM_GOODS_REDUCED;item;Materials (Reduced);TRUE;TRUE;TRUE;TRUE;10;FR_DEFAULT;FR|item|StockVariationRevenue;71355000|FR_DEFAULT;
ITEM_GOODS_REDUCED;item;Materials (Reduced);TRUE;TRUE;TRUE;TRUE;11;ZA_DEFAULT;ZA|item|Stock;13900|ZA_DEFAULT;
ITEM_GOODS_REDUCED;item;Materials (Reduced);TRUE;TRUE;TRUE;TRUE;12;ZA_DEFAULT;ZA|item|SalesRevenueAccrual;41900|ZA_DEFAULT;
ITEM_GOODS_REDUCED;item;Materials (Reduced);TRUE;TRUE;TRUE;TRUE;13;ZA_DEFAULT;ZA|item|SalesRevenue;40900|ZA_DEFAULT;
ITEM_GOODS_REDUCED;item;Materials (Reduced);TRUE;TRUE;TRUE;TRUE;14;ZA_DEFAULT;ZA|item|PurchaseVariance;53100|ZA_DEFAULT;
ITEM_SERVICES;item;Services;TRUE;TRUE;TRUE;TRUE;1;GB_DEFAULT;GB|item|SalesRevenue;40900|GB_DEFAULT;
ITEM_SERVICES;item;Services;TRUE;TRUE;TRUE;TRUE;2;GB_DEFAULT;GB|item|Expense;70100|GB_DEFAULT;
ITEM_SERVICES;item;Services;TRUE;TRUE;TRUE;TRUE;3;GB_DEFAULT;GB|item|SalesRevenueAccrual;41900|GB_DEFAULT;
ITEM_SERVICES;item;Services;TRUE;TRUE;TRUE;TRUE;4;US_DEFAULT;US|item|SalesRevenue;40900|US_DEFAULT;
ITEM_SERVICES;item;Services;TRUE;TRUE;TRUE;TRUE;5;US_DEFAULT;US|item|Expense;70100|US_DEFAULT;
ITEM_SERVICES;item;Services;TRUE;TRUE;TRUE;TRUE;6;US_DEFAULT;US|item|SalesRevenueAccrual;41900|US_DEFAULT;
ITEM_SERVICES;item;Services;TRUE;TRUE;TRUE;TRUE;7;FR_DEFAULT;FR|item|PurchaseExpense;60400200|FR_DEFAULT;
ITEM_SERVICES;item;Services;TRUE;TRUE;TRUE;TRUE;8;FR_DEFAULT;FR|item|SalesRevenue;70100210|FR_DEFAULT;
ITEM_SERVICES;item;Services;TRUE;TRUE;TRUE;TRUE;9;ZA_DEFAULT;ZA|item|SalesRevenue;40900|ZA_DEFAULT;
ITEM_SERVICES;item;Services;TRUE;TRUE;TRUE;TRUE;10;ZA_DEFAULT;ZA|item|Expense;70100|ZA_DEFAULT;
ITEM_SERVICES;item;Services;TRUE;TRUE;TRUE;TRUE;11;ZA_DEFAULT;ZA|item|SalesRevenueAccrual;41900|ZA_DEFAULT;
RESOURCE_DEFAULT;resource;Default;FALSE;FALSE;FALSE;FALSE;1;US_DEFAULT;US|resource|SetupProductionLabor;51710|US_DEFAULT;
RESOURCE_DEFAULT;resource;Default;FALSE;FALSE;FALSE;FALSE;2;US_DEFAULT;US|resource|SetupProductionMachine;51780|US_DEFAULT;
RESOURCE_DEFAULT;resource;Default;FALSE;FALSE;FALSE;FALSE;3;US_DEFAULT;US|resource|SetupProductionTool;51790|US_DEFAULT;
RESOURCE_DEFAULT;resource;Default;FALSE;FALSE;FALSE;FALSE;4;US_DEFAULT;US|resource|RunTimeProductionLabor;51710|US_DEFAULT;
RESOURCE_DEFAULT;resource;Default;FALSE;FALSE;FALSE;FALSE;5;US_DEFAULT;US|resource|RunTimeProductionMachine;51780|US_DEFAULT;
RESOURCE_DEFAULT;resource;Default;FALSE;FALSE;FALSE;FALSE;6;US_DEFAULT;US|resource|RunTimeProductionTool;51790|US_DEFAULT;
RESOURCE_DEFAULT;resource;Default;FALSE;FALSE;FALSE;FALSE;7;GB_DEFAULT;GB|resource|SetupProductionLabor;51710|GB_DEFAULT;
RESOURCE_DEFAULT;resource;Default;FALSE;FALSE;FALSE;FALSE;8;GB_DEFAULT;GB|resource|SetupProductionMachine;51780|GB_DEFAULT;
RESOURCE_DEFAULT;resource;Default;FALSE;FALSE;FALSE;FALSE;9;GB_DEFAULT;GB|resource|SetupProductionTool;51790|GB_DEFAULT;
RESOURCE_DEFAULT;resource;Default;FALSE;FALSE;FALSE;FALSE;10;GB_DEFAULT;GB|resource|RunTimeProductionLabor;51710|GB_DEFAULT;
RESOURCE_DEFAULT;resource;Default;FALSE;FALSE;FALSE;FALSE;11;GB_DEFAULT;GB|resource|RunTimeProductionMachine;51780|GB_DEFAULT;
RESOURCE_DEFAULT;resource;Default;FALSE;FALSE;FALSE;FALSE;12;GB_DEFAULT;GB|resource|RunTimeProductionTool;51790|GB_DEFAULT;
RESOURCE_DEFAULT;resource;Default;FALSE;FALSE;FALSE;FALSE;13;ZA_DEFAULT;ZA|resource|SetupProductionLabor;51710|ZA_DEFAULT;
RESOURCE_DEFAULT;resource;Default;FALSE;FALSE;FALSE;FALSE;14;ZA_DEFAULT;ZA|resource|SetupProductionMachine;51780|ZA_DEFAULT;
RESOURCE_DEFAULT;resource;Default;FALSE;FALSE;FALSE;FALSE;15;ZA_DEFAULT;ZA|resource|SetupProductionTool;51790|ZA_DEFAULT;
RESOURCE_DEFAULT;resource;Default;FALSE;FALSE;FALSE;FALSE;16;ZA_DEFAULT;ZA|resource|RunTimeProductionLabor;51710|ZA_DEFAULT;
RESOURCE_DEFAULT;resource;Default;FALSE;FALSE;FALSE;FALSE;17;ZA_DEFAULT;ZA|resource|RunTimeProductionMachine;51780|ZA_DEFAULT;
RESOURCE_DEFAULT;resource;Default;FALSE;FALSE;FALSE;FALSE;18;ZA_DEFAULT;ZA|resource|RunTimeProductionTool;51790|ZA_DEFAULT;
SUPPLIER_DEFAULT;supplier;Default;FALSE;FALSE;FALSE;FALSE;1;GB_DEFAULT;GB|supplier|Ap;20100|GB_DEFAULT;
SUPPLIER_DEFAULT;supplier;Default;FALSE;FALSE;FALSE;FALSE;2;US_DEFAULT;US|supplier|Ap;20100|US_DEFAULT;
SUPPLIER_DEFAULT;supplier;Default;FALSE;FALSE;FALSE;FALSE;3;FR_DEFAULT;FR|supplier|ApGrni;40810000|FR_DEFAULT;
SUPPLIER_DEFAULT;supplier;Default;FALSE;FALSE;FALSE;FALSE;4;ZA_DEFAULT;ZA|supplier|Ap;20100|ZA_DEFAULT;
SUPPLIER_DOMESTIC;supplier;Material suppliers;TRUE;FALSE;FALSE;FALSE;1;FR_DEFAULT;FR|supplier|Ap;40110000|FR_DEFAULT;
