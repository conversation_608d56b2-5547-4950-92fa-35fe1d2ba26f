*item;!supplier;supplierItemCode;supplierItemName;supplierPriority;isDefaultItemSupplier;*purchaseUnitOfMeasure;minimumPurchaseQuantity;purchaseLeadTime;isActive;
reference;reference;string;string;integer;boolean;reference;decimal;integer;boolean;IGNORE
item;supplier;supplier item code;supplier item name;supplier priority;is default item supplier(false/true);purchase unit of measure;minimum purchase quantity;purchase lead time;is active(true/false);IGNORE
BANANA_PIE;SUP-000001;A-PIE;Banana Pie;3;TRUE;EACH;10;1;TRUE;
BANANA_PIE;SUP-000002;C-PIE;Banana Pie;2;FALSE;EACH;12;3;TRUE;
