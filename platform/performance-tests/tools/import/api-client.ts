import { AxiosResponse } from 'axios';
import * as fs from 'node:fs/promises';
import * as fsp from 'node:path';
import {
    ApiClientBase,
    ApiClientOptions,
    ImportExportTemplate,
    PollingOptions,
    Response,
    delay,
    logRequest,
    logResponse,
    makeGraphQlResponse,
} from '../utils/api-client-base';
import { icons, logger } from '../utils/utils';

interface Node {
    _id?: string;
}

export interface TextStream {
    value: string;
}

export interface ImportResult extends Node {
    startStamp?: string;
    endStamp?: string;
    filename?: string;
    rowsProcessed?: number;
    numberOfRowsInError?: number;
    rowsInError: TextStream;
    status?: string;
    dryRun?: boolean;
    doUpdate?: boolean;
    doInsert?: boolean;
    maxErrorCount?: number;
    uploadRejectReason?: string;
    generalError?: string;
}

export class ApiClient extends ApiClientBase {
    constructor(private options: ApiClientOptions) {
        super(options);
    }

    async createUploadFile(file: string) {
        logger.info(`creating upload file entry`);
        const stat = await fs.stat(file);
        const filename = fsp.basename(file);
        const graphql = `mutation {
            xtremUpload {
              uploadedFile {
                create(data: {filename: "${filename}", kind:"upload", mimeType: "text/csv", lastModified: "${stat.mtime.toISOString()}", contentLength: ${
                    stat.size
                }}) {
                  _id
                  uploadUrl
                }
              }
            }
          }`;

        const resp = await this.postGraphQl(graphql);
        return makeGraphQlResponse(resp, resp.data?.data?.xtremUpload?.uploadedFile?.create);
    }

    async updateUploadFile(fileId: string) {
        const graphql = `mutation {
        xtremUpload {
          uploadedFile {
            update(data: {_id: "${fileId}", status: "uploaded"}) {
              _id
            }
          }
        }
      }`;

        return this.postGraphQl(graphql);
    }

    async batchImportFlatFile(
        templateId: string,
        fileId: string,
        options?: { doInsert?: boolean; doUpdate?: boolean },
    ) {
        const doInsert = options?.doInsert ?? true;
        const doUpdate = options?.doUpdate ?? false;
        logger.info(
            `starting batch import for file ${fileId} with template ${templateId} (${
                doUpdate ? 'update' : 'insert'
            } mode)`,
        );
        const graphql = `mutation { xtremImportExport {  importExportTemplate { batchImport{ start (
                                  templateId: "${templateId}"
                                  uploadedFileId: "${fileId}"
                                  options: {doInsert: ${doInsert}, doUpdate: ${doUpdate}, dryRun: false, maxErrorCount: 10}
                        ) { trackingId }}}}}`;

        const resp = await this.postGraphQl(graphql);

        return makeGraphQlResponse(resp, resp.data?.data?.xtremImportExport?.importExportTemplate?.batchImport?.start);
    }

    async getImportExportTemplate(nodeName: string): Promise<Response<ImportExportTemplate>> {
        logger.info(`get import template for ${nodeName}`);
        const graphql = `{
          xtremImportExport {
            importExportTemplate {
              query(filter: "{id: '${nodeName}'}") {
                edges {
                  node {
                      id
                      name
                      nodeName
                  }
                }
              }
            }
          }
      }
  `;

        const resp = await this.postGraphQl(graphql);
        return makeGraphQlResponse(
            resp,
            resp.data?.data?.xtremImportExport?.importExportTemplate?.query?.edges?.[0]?.node,
        );
    }

    async getImportExportTemplateCvs(nodeName: string): Promise<Response<string>> {
        logger.info(`get import export template csv for ${nodeName}`);
        const graphql = `mutation {
          xtremImportExport {
              importExportTemplate {
                  getDefaultCsvTemplate (nodeName: "${nodeName}") {
                    csvTemplate {
                      value
                    }
                  }
              }
          }
      }
  `;

        const resp = await this.postGraphQl(graphql);
        return makeGraphQlResponse(
            resp,
            resp.data?.data?.xtremImportExport?.importExportTemplate?.getDefaultCsvTemplate?.value,
        );
    }

    async createDefaultImportExportTemplate(
        nodeName: string,
        csvContent?: string,
    ): Promise<Response<ImportExportTemplate>> {
        let csvRaw = csvContent;
        if (!csvRaw) {
            csvRaw = (await this.getImportExportTemplateCvs(nodeName)).data;
        }
        if (!csvRaw) {
            throw new Error(`Cannot find a default template for node '${nodeName}'`);
        }
        const csv = JSON.stringify(csvRaw);
        logger.info(`create import export template for ${nodeName}`);
        const graphql = `mutation {
          xtremImportExport {
              importExportTemplate {
                  create (data: {id: "${nodeName}", name: "${nodeName}", nodeName: "${nodeName}", isActive: true, description: null, csvTemplate: {value: ${csv}}}) {
                      _id
                      id
                      name
                      nodeName
                      isActive
                      description
                      csvTemplate {
                          value
                      }
                  }
              }
          }
      }
  `;
        const resp = await this.postGraphQl(graphql);
        return makeGraphQlResponse(resp, resp.data?.data?.xtremImportExport?.importExportTemplate?.create);
    }

    async getFlatImportResult(notificationId: string): Promise<Response<ImportResult>> {
        const importResultGraphql = `{
                xtremImportExport {
                  importResult {
                    query(filter: "{notificationId: '${notificationId}'}") {
                      edges {
                        node {
                            _id
                            startStamp
                            endStamp
                            filename
                            rowsProcessed
                            numberOfRowsInError
                            rowsInError {
                              value
                            }
                            status
                            dryRun
                            doUpdate
                            doInsert
                            maxErrorCount
                            uploadRejectReason
                            generalError
                        }
                      }
                    }
                  }
                }
            }
            `;

        const resp = await this.postGraphQl(importResultGraphql);
        return makeGraphQlResponse(resp, resp.data?.data?.xtremImportExport?.importResult.query?.edges?.[0]?.node);
    }

    async pollFlatImportResult(
        notificationId: string,
        options: PollingOptions,
        startStamp?: number,
    ): Promise<Response<ImportResult>> {
        const timeoutMillis = options.timeoutMillis || 60000;
        const pollingMillis = options.pollingMillis || 5000;
        const now = Date.now();
        const startedAt = startStamp ?? now;
        logger.info(
            `polling import result ${notificationId} every ${pollingMillis} ms with a maximum of ${timeoutMillis} ms, remaining ${
                timeoutMillis - (now - startedAt)
            } ms`,
            icons.polling,
        );
        if (now - startedAt > timeoutMillis) {
            throw new Error(`polling has timed out: the maximum time allowed was ${timeoutMillis} ms`);
        }
        await delay(pollingMillis);
        const resp = await this.getFlatImportResult(notificationId);
        logger.info(`import result ${notificationId} status '${resp.data?.status}'`);
        if (!['pending', 'inProgress'].includes(resp.data?.status || '')) {
            return resp;
        }
        return this.pollFlatImportResult(notificationId, options, startedAt);
    }

    async uploadFile(fileId: string, file: string, url: string): Promise<AxiosResponse<any, any>> {
        const data = await fs.readFile(file);
        const dataLength = ['Kb', 'Mb'].reduce(
            (r, unit) => {
                if (r.val > 1024) {
                    r = { val: (((r.val / 1024) * 100) | 0) / 100, unit };
                }
                return r;
            },
            { val: data.length, unit: 'bytes' },
        );
        logger.info(`uploading csv file ${file} with id ${fileId} size ${dataLength.val} ${dataLength.unit}`);
        logRequest(`PUT ${url} `);
        // default max is 10Mb, we set it to 200Mb
        const maxContentLength = 200 * 1024 * 1024;
        // NOTE: upload on S3 pre-signed URL does not work with streaming so we have to read the file content before
        // We need to investigate again the use of form-data because some files are big (greater than 100Mb)
        const resp = await this.requestWithLoginRetry({
            method: 'PUT',
            headers: {
                'content-type': 'application/octet-stream',
                'accept-language': this.locale,
            },
            url,
            data,
            maxContentLength,
            maxBodyLength: maxContentLength,
        });
        logResponse(resp);
        // The UI is doing this but it is not really required, the server will set it as verified once uploaded and verified
        // return this.updateUploadFile(fileId);
        return resp;
    }
}
