import { uniq } from 'lodash';
import * as fs from 'node:fs/promises';
import * as fsp from 'node:path';
import { logger, pascalCase, saveResult, sleepMillis, Timing } from '../utils/utils';
import { ApiClient } from './api-client';
import { tryReadJsonFile } from './utils';

export interface ConfigOptions {
    files?: string[];
}

export interface ImportOptions {
    timeoutMillis: number;
    pollingMillis: number;
    reportDir?: string;
    artilleryReport?: boolean;
}

export interface ImportSessionTimings {
    global: Timing;
    login: Timing;
    imports: SingleImportTimings[];
}

export interface SingleImportTimings {
    filename: string;
    upload: Timing;
    polling: Timing;
    import?: Timing;
}

export interface SingleImportResult {
    timings: SingleImportTimings;
    errorCount: number;
}

export interface ImportFileOptions {
    importDir: string;
    filename?: string;
    fileInfo?: ImportFileInfo;
    template: string;
}

interface ImportFileInfo {
    filename: string;
    name: string;
    nodeName: string;
    template?: string;
    extraName?: string;
    operation?: string;
}

interface ParseImportFilenamesOptions {
    optionalSequence?: boolean;
}

export class ImportSession {
    readonly timings = {
        imports: [],
        login: new Timing(),
        global: new Timing(),
    } as ImportSessionTimings;

    readonly templateMap: Record<string, string> = {};

    constructor(private apiClient: ApiClient, private options: ImportOptions) {}

    async run(asyncCallback: (session: ImportSession) => void): Promise<ImportSession> {
        const { timings, apiClient } = this;
        timings.global.start();

        timings.login.start();
        await apiClient.login();
        timings.login.end();
        await asyncCallback(this);
        timings.global.end();
        return this;
    }

    async importFlatFilesFromDir(importDir: string): Promise<SingleImportResult[]> {
        const files = await fs.readdir(importDir);
        const configFile = fsp.join(importDir, 'config.json');
        const config = (await tryReadJsonFile(configFile)) as ConfigOptions | undefined;

        if (config) {
            if (!Array.isArray(config.files) || config.files.some(f => typeof f !== 'string')) {
                throw new Error(`Bad config file ${configFile}: Missing 'files' property must be a string array`);
            }
            logger.info(`Found config file with ${config.files.length} entries`);
        }
        // keep only filenames prefixed by sequence number if no config files
        const importFiles = config?.files || files.filter(f => /^\d+-/.test(f));
        if (importFiles.length === 0) {
            logger.warn('No file to import');
            return [];
        }
        const parsedFiles = this.parseImportFilenames(importFiles, { optionalSequence: !!config?.files });

        await this.ensureImportExportTemplates(importDir, parsedFiles);

        const results: SingleImportResult[] = [];
        for (let i = 0; i < parsedFiles.length; i++) {
            const fileInfo = parsedFiles[i];
            const template = fileInfo.template || fileInfo.nodeName;
            console.log('');
            results.push(
                await this.importSingleFlatFile({
                    importDir,
                    fileInfo,
                    template,
                }),
            );
        }
        return results;
    }

    private parseImportFilenames(importFiles: string[], options?: ParseImportFilenamesOptions): ImportFileInfo[] {
        // all filenames must be in the format: 'NN-node-name{--extra-name{--operation}}' where:
        //  - NN is the sequence number (do not capture it as it will not be there if we provide a config file)
        //  - node-name is the node name in kebab case
        //  - extrat-name is an optional informationnal name
        //  - operation is the import operation 'update", 'insert' (default)
        const regex = options?.optionalSequence
            ? /(?:\d+-)?(.*?)(?:--(.*?)(?:--(.*?))?)?\.csv/
            : /(?:\d+)-(.*?)(?:--(.*?)(?:--(.*?))?)?\.csv/;

        const filesInfo = importFiles
            .map(file => regex.exec(file) as string[])
            .filter(parsed => !!parsed)
            .map(parsed => ({
                filename: parsed[0],
                name: parsed[1],
                nodeName: pascalCase(parsed[1]),
                extraName: parsed[2],
                operation: parsed[3],
            }));

        const uniqueNames = uniq(filesInfo.map(f => f.filename.replace(/^\d+-/, '')));
        if (uniqueNames.length !== filesInfo.length) {
            throw new Error(`Numbered file names must be unique without the sequence prefix`);
        }

        return filesInfo;
    }

    async ensureImportExportTemplates(importDir: string, parsedFiles: ImportFileInfo[]) {
        const { apiClient, templateMap } = this;
        let csv;
        for (const parsed of parsedFiles) {
            let templateId: string | undefined = templateMap[parsed.nodeName];
            if (!templateId) {
                const template = await apiClient.getImportExportTemplate(parsed.nodeName);
                templateId = template?.data?.id;
                if (!templateId) {
                    csv = '';
                    try {
                        csv = await fs.readFile(fsp.join(importDir, `template-${parsed.name}.csv`), 'utf-8');
                    } catch (e) {
                        if (e.code !== 'ENOENT') {
                            throw e;
                        }
                    }
                    const newTemplate = await apiClient.createDefaultImportExportTemplate(parsed.nodeName, csv);
                    templateId = newTemplate.data?.id;
                }
                if (templateId) {
                    templateMap[parsed.nodeName] = templateId;
                }
            }
            parsed.template = parsed.template || templateId;
        }
    }

    async importSingleFlatFile(importOptions: ImportFileOptions): Promise<SingleImportResult> {
        const { apiClient, options } = this;
        const parsedFile =
            importOptions.fileInfo ||
            this.parseImportFilenames([importOptions.filename || ''], { optionalSequence: true })[0];
        if (!parsedFile) {
            throw new Error(`Cannot parse filename '${importOptions.filename}'`);
        }
        if (!importOptions.template) {
            await this.ensureImportExportTemplates(importOptions.importDir, [parsedFile]);
            importOptions.template = parsedFile.template || '';
        }
        const { importDir, template } = importOptions;
        const operation = importOptions.fileInfo?.operation;
        const filename = fsp.join(importDir, parsedFile.filename);
        const importTimings: SingleImportTimings = { filename, upload: new Timing(), polling: new Timing() };

        importTimings.upload.start();
        let res = await apiClient.createUploadFile(filename);
        const { uploadUrl, _id } = res.data;
        await apiClient.uploadFile(_id, filename, uploadUrl);
        importTimings.upload.end();

        // try to avoid race condition in the batch import on server side (to investigate later)
        await sleepMillis(100);

        importTimings.polling.start();
        const doUpdate = operation === 'update';
        res = await apiClient.batchImportFlatFile(template, _id, { doInsert: !doUpdate, doUpdate });
        const { trackingId: notificationId } = res.data;
        const pollingRes = await apiClient.pollFlatImportResult(notificationId, options);
        if (pollingRes.data?.status === 'rejected') {
            throw new Error(
                `import failed with status ${pollingRes.data?.status}, reason: ${pollingRes.data?.uploadRejectReason}`,
            );
        }
        if (pollingRes.data?.status === 'failed') {
            throw new Error(
                `import failed with status ${pollingRes.data?.status}, reason: ${
                    pollingRes.data?.generalError || 'general error'
                }`,
            );
        }
        importTimings.import = Timing.fromTimestamp(pollingRes.data?.startStamp, pollingRes.data?.endStamp);
        importTimings.polling.end();
        this.timings.imports.push(importTimings);

        const executionResult = {
            _id: notificationId,
            // do not keep the prefix to have a consistent trend in Power BI in case we change the order
            filename,
            counters: {
                completed: pollingRes?.data?.rowsProcessed || 0,
                failed: pollingRes?.data?.numberOfRowsInError || 0,
                requests: 1,
            },
            responseTime: {
                min: importTimings.import.elapsedInMillis,
                max: importTimings.import.elapsedInMillis,
                median: importTimings.import.elapsedInMillis,
                p95: importTimings.import.elapsedInMillis,
            },
        };

        await saveResult(executionResult, {
            reportDir: this.options.reportDir,
            artilleryReport: this.options.artilleryReport,
        });
        const errorCount = pollingRes.data?.numberOfRowsInError || 0;
        const logRes = getResultString({
            processed: pollingRes.data?.rowsProcessed || 0,
            errors: pollingRes.data?.numberOfRowsInError || 0,
            elapsedInMillis: importTimings.import.elapsedInMillis,
        });
        if (errorCount > 0) {
            logger.warn(`import ${notificationId} ${pollingRes.data?.status} with rows errors: { ${logRes} }`);
            logger.details(pollingRes.data?.rowsInError.value || '');
        } else {
            logger.success(`import ${notificationId} ${pollingRes.data?.status} rows: { ${logRes} }`);
        }
        return { timings: importTimings, errorCount };
    }

    showTimings() {
        const { timings } = this;

        let level = 1;
        const logTimings = (tm: Record<string, Timing | Record<string, Timing>[]>): void => {
            Object.entries(tm).forEach(([k, v]) => {
                if (Array.isArray(v)) {
                    level++;
                    v.forEach(logTimings);
                    level--;
                } else if (typeof v === 'string') {
                    console.log(`${k.padStart(6 + level * 4, ' ')}: ${v}`);
                } else if (v instanceof Timing) {
                    console.log(`${k.padStart(6 + level * 4, ' ')}: ${v.elapsed} (${v.elapsedInMillis} ms)`);
                }
            });
            console.log('');
        };
        console.log(`\ntimings:\n`);
        logTimings(timings as any as Record<string, Timing | Record<string, Timing>[]>);
    }
}

function getResultString(result): string {
    return Object.entries(result)
        .map(([k, v]) => `${k}: ${v}`)
        .join(', ');
}
