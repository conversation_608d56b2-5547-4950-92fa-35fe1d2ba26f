import * as fs from 'node:fs/promises';
import { Timing } from '../utils/utils';

export const importArgs = require('yargs/yargs')(process.argv.slice(2))
    .option('log-level', {
        desc: 'info,verbose,debug (default info)',
        choices: ['info', 'verbose', 'debug'],
    })
    .option('import-dir', {
        desc: '<dir path>: path of the directory that contain an order list of csv files to import',
        conflicts: ['import-file'],
    })
    .option('import-file', {
        desc: '<file path>: file path of the csv file to import',
        conflicts: ['import-dir'],
    })
    .describe('cluster', "<cluster id>: id of the cluster to use (default '')")
    .describe('tenant', "<tenant id>: id of the tenant to use (default '777777777777777777777')")
    .describe('email', "<email>: email of the user to use (default '')")
    .describe('locale', "<locale>: locale to use (default 'en-US')")
    .describe(
        'target-host',
        "<target host url>: url of the target cluster (use only on local without the login service to override the default 'http://localhost:8240')",
    )
    .describe('login-host', "<login host url>: url of the login service (default 'https://login.eu.dev-sagextrem.com')")
    .describe(
        'timeout-seconds',
        '<seconds>: maximum number of seconds to wait for having the import completed (default 60)',
    )
    .describe(
        'polling-millis',
        'number number of milliseconds to wait between each polling request for getting the result (default 200)',
    )
    .describe('report-dir', 'path to the report dir (default ./reports)')
    .describe('artillery-report', 'generate report in artillery format ')
    .describe('prefix', "prefix string (like 'daily' or 'monthly', default is no prefix)")
    .help('help')
    .parserConfiguration({
        'camel-case-expansion': true,
    })
    .example([
        [
            `import.sh --import-file ~/data/imports/allergen.csv --import-template Allergen --email <EMAIL>`,
        ],
        [
            `import.sh --import-file ~/data/imports/allergen.csv --import-template Allergen --timeout-millis 300000 --email <EMAIL> --cluster perf-test --tenant jq_DVS1yy1qVxPcxpbso2`,
        ],
    ])
    .requiresArg('log-level');

export interface ExecutionResult {
    _id: number;
    filename?: string;
    template?: string;
    rows?: {
        processed: number;
        errors: number;
    };
    importResultTiming?: Timing;
}

export async function tryReadJsonFile(file: string): Promise<NodeJS.Dict<any> | undefined> {
    try {
        return JSON.parse(await fs.readFile(file, 'utf-8'));
    } catch (e) {
        if (e.code == !'ENOENT') {
            throw e;
        }
    }
}
