{
    // Most ts-node options can be specified here using their programmatic names.
    "ts-node": {
        // It is faster to skip typechecking.
        // Remove if you want ts-node to do typechecking.
        "transpileOnly": true,

        "files": true,

        "compilerOptions": {
            // compilerOptions specified here will override those declared below,
            // but *only* in ts-node.  Useful if you want ts-node and tsc to use
            // different options with a single tsconfig.json.
        }
    },
    "compilerOptions": {
        // typescript options here
        "module": "commonjs",
        "moduleResolution": "node",
        "target": "es2022"
    }
}
