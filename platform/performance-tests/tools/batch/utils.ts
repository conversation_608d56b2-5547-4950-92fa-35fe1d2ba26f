export const batchArgs = require('yargs/yargs')(process.argv.slice(2))
    //.describe('log-level', 'info,verbose,debug (default info)')
    //.choices('log-level', ['info', 'verbose', 'debug'])
    .option('log-level', {
        describe: 'Description of my option',
        requiresArg: true, // This specifies that the option requires an argument
    })
    .describe('scenario-name', '<name of scenario>')
    .describe(
        'mutation',
        '<path of the GraphQL async mutation>: path to the mutation (xtremSales/salesOrder/postToAccounting)',
    )
    .describe('parameters', '<params>: mutation parameters, in JSON5 format \'({ id: "23", option1: true })')
    .describe('result-selector', 'GraphQL selector for the result.')
    .describe('expected-result', ' <result>: expected result, in JSON5 format')
    .describe('cluster', "<cluster id>: id of the cluster to use (default '')")
    .describe('tenant', "<tenant id>: id of the tenant to use (default '777777777777777777777')")
    .describe('email', "<email>: email of the user to use (default '')")
    .describe(
        'target-host',
        "<target host url>: url of the target cluster (use only on local without the login service to override the default 'http://localhost:8240')",
    )
    .describe('login-host', "<login host url>: url of the login service (default 'https://login.eu.dev-sagextrem.com')")
    .describe(
        'timeout-millis',
        '<milliseconds>: maximum number of milliseconds to wait for having the import completed (default 60000)',
    )
    .describe(
        'polling-millis',
        'number number of milliseconds to wait between each polling request for getting the result (default 200)',
    )
    .describe('report-dir', 'path to the report dir (default ./reports)')
    .describe('artillery-report', 'generate report in artillery format ')
    .help('help')
    .parserConfiguration({
        'camel-case-expansion': true,
    })
    .example([
        [
            `./batch.sh --mutation xtremSales/salesOrder/postToAccounting --parameters '(id:"SO0001", option1:true)' --email <EMAIL>  --result-selector '{result}' --artillery-report "my-report" --expected-result '{"result":"I like SO0001 with true"}'`,
        ],
    ]);
