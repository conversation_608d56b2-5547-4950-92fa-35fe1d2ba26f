import { argv, isDeepEqual, logger, saveResult, Timing } from '../utils/utils';
import { ApiClient } from './api-client';
import { batchArgs } from './utils';

async function main() {
    const {
        scenarioName,
        mutation,
        parameters,
        resultSelector,
        expectedResult,
        loginHost,
        targetHost,
        cluster,
        tenant,
        email,
        timeoutMillis,
        pollingMillis,
        reportDir,
        artilleryReport,
    } = argv;

    if (!mutation && !expectedResult && !parameters) {
        logger.fail('Either --mutation and --expected-result and --parameters options are required. See help.');
        batchArgs.showHelp();
        process.exit();
    }

    const batchTimings = {
        global: new Timing(),
        login: new Timing(),
        executeJob: new Timing(),
        polling: new Timing(),
    } as Record<string, Timing>;
    const apiClient = new ApiClient({ targetHost, email, tenant, cluster, loginHost });
    batchTimings.global.start();

    batchTimings.login.start();
    await apiClient.login();
    batchTimings.login.end();

    batchTimings.executeJob.start();

    let tarckingId = await apiClient.executeAsyncMutation(mutation, parameters);

    batchTimings.polling.start();

    const pollingRes = await apiClient.pollAsyncMutationResult(mutation, tarckingId, resultSelector, {
        timeoutMillis,
        pollingMillis,
    });

    if (pollingRes.data?.status === 'rejected') {
        throw new Error(
            `Async mutation ${mutation} failed with status ${pollingRes.data?.status}, reason: ${pollingRes.data?.uploadRejectReason}`,
        );
    }
    if (pollingRes.data?.status === 'failed') {
        throw new Error(
            `Async mutation ${mutation}  failed with status ${pollingRes.data?.status}, reason: ${
                pollingRes.data?.generalError || 'general error'
            }`,
        );
    }

    if (expectedResult) {
        const expectedResultValue = JSON.parse(expectedResult);
        const result = await pollingRes.data;
        if (!isDeepEqual(expectedResultValue, result)) {
            throw new Error(
                `Async mutation ${mutation}, result ${JSON.stringify(
                    result,
                )} is differenet expected result ${JSON.stringify(expectedResultValue)}`,
            );
        }
    }

    batchTimings.polling.end();
    batchTimings.executeJob.end();

    const elapsedTime = batchTimings.executeJob.elapsedInMillis;

    const executionResult = {
        _id: tarckingId,
        filename: scenarioName ?? 'async-mutation',
        counters: {
            completed: 1,
            failed: 0,
            requests: 1,
        },
        responseTime: {
            min: elapsedTime,
            max: elapsedTime,
            median: elapsedTime,
            p95: elapsedTime,
        },
    };

    await saveResult(executionResult, {
        reportDir: reportDir,
        artilleryReport: artilleryReport,
    });
}

main().catch(e => {
    console.log('');
    logger.fail(`ERROR: ${e.message}`);
    process.exit(1);
});
