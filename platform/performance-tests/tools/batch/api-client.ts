import * as _ from 'lodash';
import { ApiClientBase, ApiClientOptions, delay, PollingOptions, Response } from '../utils/api-client-base';

export class ApiClient extends ApiClientBase {
    constructor(private options: ApiClientOptions) {
        super(options);
    }

    async executeAsyncMutation(mutation: string, parameters: string) {
        const splitMutation = mutation.split('/');
        const pathLength = splitMutation.length;

        const graphql = `mutation {
            ${splitMutation
                .map((param, idx) => ` ${idx === 0 ? '' : '{'} ${param}`)
                .join('')} {start ${parameters} { trackingId ${'}'.repeat(pathLength + 1)}
        }`;
        const result = await (this.postGraphQl(graphql) as any);
        const path = `data.data.${mutation.replace(/\//g, '.')}.start.trackingId`;
        const val = _.get(result, path);
        return val;
    }

    async executeAsyncMutationTracker(mutation: string, trackingId: string, resultSelector: string) {
        const splitMutation = mutation.split('/');
        const pathLength = splitMutation.length;

        const graphql = `{
            ${splitMutation
                .map((param, idx) => ` ${idx === 0 ? '' : '{'} ${param}`)
                .join(
                    '',
                )}{ track (trackingId: "${trackingId}") {status result ${resultSelector} errorMessage} ${'}'.repeat(
            pathLength + 1,
        )}`;
        console.log(graphql);
        const result = await this.postGraphQl(graphql);
        const trackPath = `data.data.${mutation.replace(/\//g, '.')}.track`;
        const reponseVal = _.get(result, trackPath);
        return {
            ...reponseVal,
            data: reponseVal.result,
        };
    }

    async pollAsyncMutationResult(
        mutation: string,
        trackingId: string,
        resultSelector: string,
        options: PollingOptions,
        startStamp?: number,
    ): Promise<Response<any>> {
        const timeoutMillis = options.timeoutMillis || 60000;
        const pollingMillis = options.pollingMillis || 100;
        const startedAt = startStamp ?? Date.now();
        while (true) {
            const now = Date.now();
            if (now - startedAt > timeoutMillis) {
                throw new Error(`polling has timed out: the maximum time allowed was ${timeoutMillis} ms`);
            }
            const resp = await this.executeAsyncMutationTracker(mutation, trackingId, resultSelector);
            console.log(now - startedAt, resp);

            if (resp.status === 'error') {
                throw new Error(resp.errorMessage);
            }
            if (!['pending', 'inProgress'].includes(resp?.status || '')) {
                return resp;
            }
            await delay(pollingMillis);
        }
    }
}
