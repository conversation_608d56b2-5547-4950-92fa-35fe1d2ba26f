#!/usr/bin/env bash
DIR=$(cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd)

if docker ps | grep prom/prometheus
then
    echo "Skipping install of prometheus docker container because container already exists"
    exit 0
fi

if [ ! -f "$DIR"/prometheus.yml ]; then
    cp "$DIR"/prometheus-template.yml "$DIR"/prometheus.yml
    echo ">  prometheus.yml file created from template"
    echo "⚠️  Please update the prometheus.yml file with the correct host and port for the targets if necessary"
fi

echo "Creating prometheus docker container"
docker run --add-host=host.docker.internal:host-gateway -d --name prometheus -p 9090:9090 -v "$DIR"/prometheus.yml:/etc/prometheus/prometheus.yml prom/prometheus
