scenarios:
  - xtrem:
      resources:
        - '{{ $processEnvironment.SCENARIO_PATH }}/resources/graphql/metadata/*'
    flow:
      - loop:
          - post:
              beforeRequest: 'checkDuration'
              url: /metadata
              json:
                query: pages.graphql
              expect:
                - notHasProperty: errors
                - statusCode: 200
              name: metadata-pages
        whileTrue: 'notExceedsDuration'
