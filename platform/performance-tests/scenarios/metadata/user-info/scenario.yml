scenarios:
  - xtrem:
      resources:
        - '{{ $processEnvironment.SCENARIO_PATH }}/resources/graphql/metadata/*'
    flow:
      - loop:
          - post:
              beforeRequest: 'checkDuration'
              url: /metadata
              json:
                query: user-info.graphql
              expect:
                - notHasProperty: errors
                - statusCode: 200
              name: metadata-user-info
        whileTrue: 'notExceedsDuration'
