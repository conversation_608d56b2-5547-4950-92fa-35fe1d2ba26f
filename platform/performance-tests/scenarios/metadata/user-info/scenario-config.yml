config:
  plugins:
    xtrem: {}
    ensure: {}
    expect: {}
    metrics-by-endpoint:
      # Group metrics by request name rather than URL:
      useOnlyRequestNames: true
  phases:
    - duration: '{{ $processEnvironment.RAMP_UP }}'
      arrivalCount: '{{ $processEnvironment.TOTAL_USERS }}'
      name: 'Ramp up {{ $processEnvironment.TOTAL_USERS }} users in {{ $processEnvironment.RAMP_UP }} seconds'
    - pause: '{{ $processEnvironment.DURATION }}'
      name: 'Stress {{ $processEnvironment.TOTAL_USERS }} users for {{ $processEnvironment.DURATION }} seconds'
  environments:
    local:
      target: http://localhost:8240
    devops:
      xtrem:
        loginManager: 'UnsecureDevLogin'
      target: '{{ $processEnvironment.CLUSTER }}'
