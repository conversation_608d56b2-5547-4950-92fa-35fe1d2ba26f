[All packages](../../../docs/index.html)

# Xtrem Server Framework

The Xtrem server framework provides the TypeScript API to implement the server side artifacts and business logic for an Xtrem service.

The main server side artifacts are _nodes_. A _node_ is a _business object_, like a _company_, _customer_, _stock item_,
which may be persisted in a database and may be exposed to the outside through the GraphQL API.
