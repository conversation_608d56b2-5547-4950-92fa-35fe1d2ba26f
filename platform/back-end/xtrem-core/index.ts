/** @packageDocumentation @module index */
import type * as TestFixtures from './test/fixtures';

export * from './lib/index';
export * from './lib/sql/sql-context';
export * from './lib/test/manager-mocks';

// Special export here. build/test is not included in the published packages so we cannot brutally import
// fixtures from test. But we can import them with a dynamic require.
// This is fine as long as getTestFixtures is only called from test modules of other packages.
export function getTestFixtures(): typeof TestFixtures {
    // eslint-disable-next-line global-require
    return require('./test/fixtures');
}

/**
 * Interface hook for context module augmentation
 * Other system packages can add their own API to the Context class by extending this interface
 * @sage/xtrem-communication uses this to add the `send` and `notify` methods.
 *
 * Note: this interface must be defined *here*. Do not move it to lib/runtime/context.ts because
 * external modules cannot reference modules like @sage/xtrem-core/lib/...
 */
export interface ContextAugmentation {}

// Apply the augmentation interface to the Context class inside ./lib/runtime/context
declare module './lib/runtime/context' {
    export interface Context extends ContextAugmentation {}
}
