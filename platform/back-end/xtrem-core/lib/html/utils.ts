import { J<PERSON><PERSON> } from 'jsdom';

export interface ToHtmlTextOptions {
    shrinkEmptyLines?: boolean;
}

export function htmlFragment(html: string, options: { path?: string; unChanged?: boolean } = {}): string {
    let htmlElement: HTMLElement = new JSDOM(html).window.document.documentElement;

    if (options.path) {
        // eslint-disable-next-line no-restricted-syntax
        for (const tag of options.path.split('.')) {
            const child: Element | null = htmlElement.getElementsByTagName(tag).item(0);
            if (!child) {
                break;
            }
            htmlElement = child as HTMLElement;
        }
    }
    const { outerHTML } = htmlElement;

    return options.unChanged ? outerHTML : removeFormatting(outerHTML);
}

// Collapse into a single line to cope with diffs when html data files are reformatted
function removeFormatting(str: string): string {
    return str.replace(/>\s+</g, '><');
}

export function htmlBody(html: string, unChanged = false): string {
    return removeFormatting(htmlFragment(html, { path: 'body', unChanged }));
}

export function toHtmlText(html: string, options?: ToHtmlTextOptions): string {
    const jsdom = new JSDOM(html);
    // remove script and style tags
    jsdom.window.document.querySelectorAll('script').forEach(script => script.remove());
    jsdom.window.document.querySelectorAll('style').forEach(style => style.remove());
    if (options?.shrinkEmptyLines) {
        // remove more than 2 consecutive empty lines and trim
        return jsdom.window.document.body.textContent?.replace(/(\n\s*){3,}/g, '\n\n') ?? '';
    }
    return jsdom.window.document.body.textContent ?? '';
}
