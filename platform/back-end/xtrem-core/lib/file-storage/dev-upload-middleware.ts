import { ConfigManager } from '@sage/xtrem-config';
import { isDevelopmentConfig } from '@sage/xtrem-shared';
import { Express, Handler, Request, Response } from 'express';
import * as fs from 'fs';
import * as fsp from 'path';
import { Application } from '../application';
import { loggers } from '../runtime/loggers';
import { Test } from '../test';

const logger = loggers.fileStorage;

interface DestinationInfo {
    destination?: string;
    key?: string;
    filePath?: string;
}

/**
 * Route that we will use to mock the async upload of files to S3 when we are in development mode
 * @param app
 * @param application
 */
export function addDevUploadRoute(app: Express, application: Application): void {
    const config = ConfigManager.current;
    if (!isDevelopmentConfig(config)) return;

    /**
     * Parses the destination information from the request and returns the destination, key, and file path.
     *
     * @param req - The request object.
     * @returns An object containing the destination, key, and file path.
     */
    function parseDestination(req: Request): DestinationInfo {
        const currentApp = ConfigManager.current.app;
        const applicationTmpDir = currentApp ? fsp.join(application.tmpDir, currentApp) : application.tmpDir;
        const tenantId = config.tenantId || Test.defaultTenantId;
        const destination = req.params.destination;
        const keyInfo = JSON.parse(Buffer.from(req.params.key, 'base64').toString());
        const key = keyInfo.objectKey;

        let fileDestination = `${destination}/${key}`;
        const keySegments = keyInfo.objectKey.split('/');
        // Make an exception for the uploads folder to use the key as the destination if we have more than one segment
        // This is the case for example for print-outputs/xxx and csv-exports/xxx
        if (destination === 'uploads' && keySegments.length > 1) {
            fileDestination = key;
        }

        // File path to the verified(scanned) local location ${applicationDir}/${tenantId}/${fileDestination}
        const rootPath = fsp.join(applicationTmpDir, tenantId);
        const filePath = fsp.join(applicationTmpDir, tenantId, fileDestination);
        // Check if the file path is within the root path to prevent directory traversal
        // (if fileDestination contains ../../ or similar)
        if (!filePath.startsWith(rootPath)) {
            return {};
        }

        return { destination, key, filePath };
    }

    const devUploadMiddleware: Handler = (_req, res, next) => {
        const s3BucketUrlPrefix = config.s3Storage?.s3BucketUrlPrefix;
        if (s3BucketUrlPrefix) {
            // If we have s3BucketUrlPrefix in the xtrem-config.yml, we need to add this URL to the allowed origins,
            // using the access-control-allow-origin header.
            // Scenario: When running application with the UI watcher, the URL we use in the browser is http://localhost:3000
            // If the developer wishes to upload a file using a presigned url provided by the infrastructure helper
            // we need to add this URL (e.g. http://localhost:8240/dev/uploads) to the access-control-allow-origin header
            // If we don't set this header the request will fail with a CORS issue.
            res.setHeader('access-control-allow-origin', s3BucketUrlPrefix);
        }

        next();
    };

    app.put('/dev/:destination/:key', devUploadMiddleware, (req: Request, res: Response) => {
        const { destination, key, filePath } = parseDestination(req);
        if (!destination || !key || !filePath) {
            logger.error(`[Dev] Failed to upload destination=${destination} file='${key}' to ${filePath}`);
            res.status(404).end();
            return;
        }
        logger.info(`[Dev] Uploading file destination=${destination} file='${key}' to ${filePath}`);

        fs.mkdirSync(fsp.dirname(filePath), { recursive: true });

        const writeStream = fs.createWriteStream(filePath);
        // Stream payload and dump onto file system
        req.pipe(writeStream)
            .on('finish', () => {
                let replyTopic = '';
                if (destination === 'uploads' || destination === 'attachments') {
                    replyTopic = 'UploadedFile/InfrastructureComplete';
                } else throw new Error(`Invalid destination ${destination}`);
                // TODO: in the future we need to accept parameters in the URL to dictate if the file failed the virus scan
                const payload = {
                    contextId: key,
                    result: 'success',
                    contextValue: {
                        replyTopic,
                    },
                    // reason: '', rejection reason
                };
                // File dump is done create mocked tags file
                fs.writeFileSync(
                    `${filePath}.tags`,
                    JSON.stringify({
                        Key: key,
                        LastModified: Date.now().toString(),
                        ETag: key,
                    }),
                );
                // Emit the event devFileUploaded which will be picked up in routing service and notify to upload file listener
                Application.emitter.emit('devFileUploaded', { application, payload });
                res.status(200).end();
            })
            .on('error', error => {
                logger.error(
                    `[Dev] Failed to upload destination=${destination} file='${key}' from ${filePath}: ${error.message}`,
                );
                res.status(404).end(error.message);
            });
    });

    // Print output route
    app.get('/dev/:destination/:key', devUploadMiddleware, (req: Request, res: Response) => {
        const { destination, key, filePath } = parseDestination(req);

        if (!destination || !key || !filePath) {
            logger.error(`[Dev] Failed to download destination=${destination} file='${key}' from ${filePath}`);
            res.status(404).end();
            return;
        }
        logger.info(`[Dev] Downloading destination=${destination} file='${key}' from ${filePath}`);

        // Send the file as an attachment
        res.setHeader(
            'content-disposition',
            `attachment; filename=${req.query.filename ? encodeURIComponent(req.query.filename as string) : fsp.basename(filePath)}`,
        );
        res.sendFile(filePath, error => {
            if (error) {
                logger.error(
                    `[Dev] Failed to download destination=${destination} file='${key}' from ${filePath}: ${error.message}`,
                );
                res.status(500).send(error.message);
            }
        });
    });
}
