import { ConfigManager } from '@sage/xtrem-config';
import { createDictionary, Dict, isDevelopmentConfig, SecurityError, SystemError } from '@sage/xtrem-shared';
import { Express } from 'express';
import * as fs from 'node:fs/promises';
import * as fsp from 'path';
import { Application } from '../application';
import { StaticThis } from '../decorators';
import { getServerUrl } from '../runtime';
import { Context } from '../runtime/context';
import { Node } from '../ts-api';
import { addDevUploadRoute } from './dev-upload-middleware';
import { addDownloadRoute, downloadBasePath } from './download-middleware';

export interface DownloadTargetInfo {
    nodeName: string;
    _id: string;
}

export type FileStorageInfo = Pick<Context, 'application' | 'tenantId'>;

type DownloadHandler = (node: Node) => Promise<string>;

/**
 * Manages file storage operations and provides utility methods for handling file downloads.
 */
export abstract class FileStorageManager {
    private static _downloadHandlersByNodeName: Dict<DownloadHandler> = createDictionary<DownloadHandler>();

    /**
     * Adds authenticated routes to the Express app for handling file downloads.
     * @param app - The Express app.
     * @param application - The application instance.
     */
    static addAuthenticatedRoutes(app: Express, application: Application): void {
        addDownloadRoute(app, application);
    }

    /**
     * Adds unauthenticated routes to the Express app for handling the dev upload.
     * @param app - The Express app.
     * @param application - The application instance.
     */
    static addUnauthenticatedRoutes(app: Express, application: Application): void {
        if (isDevelopmentConfig(ConfigManager.current)) {
            // Route to mock the upload of files to S3 when we are in development mode
            addDevUploadRoute(app, application);
        }
    }

    /**
     * Registers a download handler for a specific node class.
     * @param clas - The node class.
     * @param handler - The download handler function.
     */
    static registerDownloadHandler(clas: StaticThis<Node>, handler: DownloadHandler): void {
        const nodeName = clas.name;
        if (
            this._downloadHandlersByNodeName[nodeName] != null &&
            this._downloadHandlersByNodeName[nodeName] !== handler
        ) {
            throw new Error(`A download handler for node ${nodeName} is already registered`);
        }
        this._downloadHandlersByNodeName[nodeName] = handler;
    }

    static getSandboxFilePath(storage: FileStorageInfo, objectKey: string): string {
        return fsp.join(storage.application.tmpDir, 'tenant-data', '.sandbox', storage.tenantId ?? '.', objectKey);
    }

    static clearSandbox(storage: FileStorageInfo): Promise<void> {
        return fs.rm(fsp.join(storage.application.tmpDir, 'tenant-data', '.sandbox', storage.tenantId ?? '.'), {
            recursive: true,
        });
    }

    /**
     * Generates an on-demand download URL for a given node.
     * @param node - The node to generate the download URL for.
     * @returns A Promise that resolves to the on-demand download URL.
     */
    static async getOnDemandDownloadUrl(node: Node): Promise<string> {
        const target = await node.$.context.vault.encrypt64(
            JSON.stringify({ nodeName: node.$.factory.name, _id: node._id }),
        );
        return getServerUrl(downloadBasePath, new URLSearchParams({ t: target }));
    }

    /**
     * Retrieves the download URL for a given target.
     * @param context - The context object.
     * @param target - The target string.
     * @returns A Promise that resolves to the download URL.
     * @throws {SecurityError|SystemError} If the target data is invalid.
     */
    static async getTargetDownloadUrl(context: Context, target: string): Promise<string> {
        try {
            const { nodeName, _id }: DownloadTargetInfo = JSON.parse(await context.vault.decrypt64(target));
            const handler = this._downloadHandlersByNodeName[nodeName];
            if (handler == null) {
                throw new SystemError(`No download handler for node ${nodeName} is registered`);
            }
            const factory = context.application.getFactoryByName(nodeName);
            const node = await context.read(factory.nodeConstructor, { _id });
            return await handler(node);
        } catch (e) {
            throw new SecurityError('Invalid download target data', e);
        }
    }
}
