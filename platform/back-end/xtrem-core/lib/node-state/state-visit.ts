/** @ignore */ /** */
import { AnyValue, AsyncArray, asyncArray, AsyncResponse } from '@sage/xtrem-async-helper';
import { ForeignNodeProperty, Property } from '../properties';
import { NodeFactory } from '../runtime';
import { Node } from '../ts-api';
import { NodeState } from './node-state';

/**
 * <PERSON> that returns the items to visit when we traverse a reference/collection property
 *
 * @template T the type of the item which is being visited
 * @internal
 *
 * The walker is a function that takes a parent item and a property and returns an array of child items.
 */
export type Walker<T> = (property: ForeignNodeProperty, item: T) => AsyncResponse<T[]>;

/**
 * Visitor events which are called by visitVitalTree.
 *
 * @template T the type of the item which is being visited
 * @internal
 */
export interface Visitors<T> {
    /** event called before visiting an item */
    before?: (item: T, path: string[]) => AsyncResponse<boolean>;
    /** event called when visiting an item */
    visit?: (item: T, path: string[]) => AsyncResponse<boolean>;
    /** event called after visiting an item */
    after?: (item: T, path: string[], result: boolean) => AsyncResponse<boolean>;
    /** walks the graph by returning the array of child items for a property */
    walk: Walker<T>;
}

/**
 * Filter to find out whether a property of the vital graph needs to be visited or not.
 *
 * @internal
 */
export type VisitorPropertyFilter = (property: ForeignNodeProperty) => boolean;

/**
 * This abstract class provides methods to visit a node's state and (recursively) the states of its vital children.
 *
 * @internal
 */
export abstract class StateVisit {
    /**
     * Visits (recursively) a tree of nodes by traversing its vital references/collections
     *
     * @param factory the root factory
     * @param visitors the visitor callbacks which will be called during the visit
     * @param item the root item to which visitors.walk will be applied
     * @param path the path (list of property names) traversed so far.
     *
     * @return true if all the visitors returned true, false otherwise.
     */
    static async visitVitalTree<T extends AnyValue>(
        factory: NodeFactory,
        visitors: Visitors<T>,
        item: T,
        path: string[] = [],
    ): Promise<boolean> {
        let r = true;
        if (visitors.before) r = await visitors.before(item, path);
        if (visitors.visit) r = (await visitors.visit(item, path)) && r;
        await asyncArray(factory.properties).forEach(async property => {
            if (property.isReferenceProperty() && (property.isVital || property.isAssociation)) {
                const p = [...path, property.name];
                const child = (await visitors.walk(property, item))[0];
                if (!child) return;
                r = (await StateVisit.visitVitalTree(property.targetFactory, visitors, child, p)) && r;
            } else if (property.isCollectionProperty() && (property.isVital || property.isAssociation)) {
                const p = [...path, property.name];
                await asyncArray(await visitors.walk(property, item)).forEach(async child => {
                    r = (await StateVisit.visitVitalTree(property.targetFactory, visitors, child, p)) && r;
                });
            }
        });
        if (visitors.after) r = (await visitors.after(item, path, r)) && r;
        return r;
    }

    /**
     * Returns a walker which will return the node states when visiting the graph.
     * The walker only traverses the properties contained in the `properties` set.
     *
     * @param properties the set of properties that will be visited.
     */
    private static walker(properties: Set<Property>): Walker<NodeState> {
        return async (property: ForeignNodeProperty, state: NodeState): Promise<NodeState[]> => {
            // if the property is not in the properties set, we return an empty array.
            if (!properties.has(property)) return [];

            // if the property is a collection, we map its nodes to states
            if (property.isCollectionProperty())
                return ((await state.getPropertyValue(property)) as AsyncArray<AnyValue>)
                    .map((node: Node) => node.$.state)
                    .toArray();

            // if the property is a refence we return an array of 1 or 0 states.
            const child = (await state.getPropertyValue(property)) as Node;
            return child ? [child.$.state] : [];
        };
    }

    /**
     * Computes the set of properties of the graph that will be traversed.
     * @param rootFactory the root factory
     * @param filter the predicate to filter the properties
     */
    private static async filterProperties(
        rootFactory: NodeFactory,
        filter: VisitorPropertyFilter,
    ): Promise<Set<Property>> {
        const properties: Set<Property> = new Set();

        await StateVisit.visitVitalTree<ForeignNodeProperty | null>(
            rootFactory,
            {
                // The return convention is a bit odd here because visitVitalTree return true if all visitor calls returned true,
                // and false if at least one of them returned false.
                //
                // There is one subtle issue, in building this set:
                //   If we traverse a Parent -> Child -> GrandChild chain, and if GrandChild is selected by the filter,
                //    we must select Child, even if the filter does not select it (returns false).
                //
                // For this we adopt the convention that after returns false if the property is added to the set.
                // If result is false when after is called on Child (after visiting the grandchildren), we know
                // that at least one grandchild was selected, and we select `fromProperty`, without even calling `filter`.
                after(property, path, result) {
                    if (property && (!result || filter(property))) {
                        properties.add(property);
                        return false;
                    }
                    return true;
                },

                // The walker is very simple. We just pass the property.
                walk(property) {
                    return [property];
                },
            },
            null,
        );

        return properties;
    }

    /**
     * Returns a walker which will only traverse the properties selected by the filter
     * @param rootFactory the root factory
     * @param filter the predicate to filter the properties
     */
    static async getWalker(rootFactory: NodeFactory, filter: VisitorPropertyFilter): Promise<Walker<NodeState>> {
        const propertiesToVisit = await StateVisit.filterProperties(rootFactory, filter);
        return StateVisit.walker(propertiesToVisit);
    }
}
