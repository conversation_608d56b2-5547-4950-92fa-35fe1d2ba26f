/** @ignore */ /** */
import { AnyRecord, AnyValue, asyncArray } from '@sage/xtrem-async-helper';
import * as _ from 'lodash';
import { Property } from '../properties';
import { isCompound, isScalar } from '../runtime/utils';
import { Node, OrderBy } from '../ts-api';
import { cursorChecksum } from '../types/util';
import { NodeState } from './node-state';

/**
 * This static class provides the method to compute a cursor value, given a state and an orderBy.
 * The cursor value is used for paging.
 *
 * @internal
 */
export abstract class StateCursor {
    /** Get the cursor value for a JSON property */
    private static async getJsonPropertyCursorValue(
        state: NodeState,
        property: Property,
        orderBy: OrderBy<Node>,
    ): Promise<AnyValue> {
        // Recursive function to traverse the json object
        const selectValue = (selector: AnyValue, value: AnyValue): AnyValue => {
            if (isScalar(value)) return value;
            return Object.keys(selector as AnyRecord).map(k =>
                selectValue((selector as AnyRecord)[k], (value as AnyRecord)[k]),
            );
        };

        const orderByValue = (orderBy as AnyRecord)[property.name];
        const propertyValue = await state.getPropertyValue(property);
        return selectValue(orderByValue, propertyValue);
    }

    /** Get the cursor value for a reference property */
    private static async getReferencePropertyCursorValue(
        state: NodeState,
        property: Property,
        orderBy: OrderBy<Node>,
    ): Promise<AnyValue> {
        const orderByValue = (orderBy as AnyRecord)[property.name];
        if (isScalar(orderByValue)) {
            // OrderBy does not select any of the reference's properties. We return the raw value.
            const value = state.values[property.name];
            if (value === undefined) {
                throw state.propertyDataInputError(property, {
                    message: 'invalid cursor value: {{value}}',
                    key: '@sage/xtrem-core/invalid-cursor-value',
                    data: { value },
                });
            }
            return value;
        }

        const propertyValue = await state.getPropertyValue(property);
        // Recurse through getCursorValues if propertyValue is not null.
        if (!propertyValue) return null;
        return StateCursor.getCursorValues((propertyValue as Node).$.state, orderByValue as AnyRecord);
    }

    /** Get the cursor value for a scalar property */
    private static getScalarPropertyCursorValue(
        state: NodeState,
        property: Property,
        orderBy: OrderBy<Node>,
    ): Promise<AnyValue> {
        const orderByValue = (orderBy as AnyRecord)[property.name];

        if (isCompound(orderByValue)) {
            throw state.propertyDataInputError(property, {
                message: 'invalid property type in orderBy {{type}}',
                key: '@sage/xtrem-core/invalid-property-type-order-by',
                data: { type: property.type },
            });
        }
        return state.getPropertyValue(property);
    }

    /** Get the cursor value for a property */
    private static getCursorPropertyValue(
        state: NodeState,
        property: Property,
        orderBy: OrderBy<Node>,
    ): Promise<AnyValue> {
        if (property.isReferenceProperty()) {
            return StateCursor.getReferencePropertyCursorValue(state, property, orderBy);
        }
        if (property.type === 'json') {
            return StateCursor.getJsonPropertyCursorValue(state, property, orderBy);
        }
        return StateCursor.getScalarPropertyCursorValue(state, property, orderBy);
    }

    /** Get the cursor values as an array */
    private static getCursorValues(state: NodeState, orderBy: OrderBy<Node>): Promise<AnyValue[]> {
        // graphql layer took care of adding key properties if necessary,
        // to guarantee that orderBy does not allow duplicates
        // so we don't need to worry about this issue here.
        return asyncArray(Object.keys(orderBy))
            .map(key => {
                if (key === '_constructor' && !state.factory.isAbstract) return state.factory.name;
                const prop = state.factory.findProperty(key, { includeSystemProperties: true });
                return StateCursor.getCursorPropertyValue(state, prop, orderBy);
            })
            .toArray();
    }

    /** Get the cursor value for a given node state and ordering */
    static async getCursorValue(state: NodeState, orderBy: OrderBy<Node>): Promise<string> {
        const cursor = JSON.stringify(_.flattenDeep(await StateCursor.getCursorValues(state, orderBy)));
        return `${cursor}${cursorChecksum(cursor)}`;
    }
}
