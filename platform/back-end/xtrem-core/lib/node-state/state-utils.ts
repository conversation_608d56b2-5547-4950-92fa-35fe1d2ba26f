/** @ignore */ /** */
import { <PERSON><PERSON><PERSON><PERSON>, AsyncResponse } from '@sage/xtrem-async-helper';
import { BaseError, UnknownError, ValidationSeverity } from '@sage/xtrem-shared';
import { Property } from '../properties';
import { loggers } from '../runtime/loggers';
import { NodeFactory } from '../runtime/node-factory';
import { NodeState, StateStatus } from './node-state';

/**
 * This static class contain utility methods.
 *
 * @internal
 */
export abstract class StateUtils {
    /** Throws an exception if the state is stale. */
    static checkNotStale(state: NodeState): void {
        if (state.status === StateStatus.stale) {
            throw new Error(`${state.interningKeyValues[0]}: invalid operation on stale node`);
        }
    }

    /** Returns a 'property is required' error */
    static requiredPropertyError(state: NodeState, property: Property): Error {
        return state.propertyDataInputError(property, {
            message: 'property is required',
            key: '@sage/xtrem-core/property-is-required',
        });
    }

    /**
     * Returns whether a property is enabled or not, given the service options that it may carry
     * Returns true if the property is not controlled by any service options.
     */
    static isEnabledByServiceOptions(state: NodeState, property: Property): AsyncResponse<boolean> {
        return state.context.isEnabledByServiceOptions(property);
    }

    /** Returns if a property is a pure getter (getValue but no setValue) */
    static isPropertyGetterOnly(property: Property): boolean {
        if (property.isCollectionProperty()) {
            if (!(property.isTransientInput || property.isMutable)) return true;
            if (property.getFilter) return true;
        }
        if (property.isReferenceProperty() && property.decorator.join && !property.isMutable) return true;
        return (!!property.getValue || !!property.computeValue) && !property.setValue;
    }

    /**
     * Format state values to be user friendly for user facing error message
     * If more than one value is present, filter out _id
     */
    static formatStateValues(values: AnyRecord): string {
        const entries = Object.entries(values);
        return (entries.length > 1 ? entries.filter(([k]) => k !== '_id') : entries)
            .map(([k, v]) => `${k}: ${v}`)
            .join(', ');
    }

    static async withValidationErrorRethrow(
        state: NodeState,
        property: Property,
        path: string[],
        body: () => AsyncResponse<void>,
    ): Promise<void> {
        try {
            await body();
        } catch (err) {
            // catch exception here to get correct path (including property.name)

            // Ignore errors from lookup transient nodes and getDuplicate query
            if (state.isOnlyForLookup || state.isOnlyForDuplicate) {
                loggers.runtime.warn(err.message);
                return;
            }

            if (state.isOnlyForDefaultValues) {
                // We have to initialize all properties without throwing because we need a node instance for the graphql response.
                // We only record the first exception that we catch here.
                // The exceptions that we get later may be consequences of the first exception so
                // we ignore them.
                if (state.context.severity < ValidationSeverity.exception) {
                    state.context.addDiagnoseAtPath(ValidationSeverity.exception, path, err.message);
                }
                return;
            }

            // If err is a BaseError, add the path if not already set, and rethrow
            if (err instanceof BaseError) {
                if (!err.path) err.path = path;
                throw err;
            }

            // Unexpected error, log it and rethrow a SystemError
            loggers.runtime.error(err.stack);
            throw new UnknownError(err.message, err, path);
        }
    }

    static restructureDelegatedInputValues(factory: NodeFactory, inputData: AnyRecord): AnyRecord {
        const delegatedProperties = factory.delegatedProperties;
        if (delegatedProperties.length === 0) return inputData;
        const newData = { ...inputData };
        delegatedProperties.forEach(delegatedProperty => {
            const value = newData[delegatedProperty.name];
            delete newData[delegatedProperty.name];
            const { reference, childProperty } = delegatedProperty.getDelegatingInfo();
            if (value === undefined && reference.isNullable) return;
            let childData = newData[reference.name];
            if (!childData) {
                childData = {} as AnyRecord;
                newData[reference.name] = childData;
            }
            if (value !== undefined) (childData as AnyRecord)[childProperty.name] = value;
        });

        return newData;
    }
}
