/** @ignore */ /** */
import { asyncArray, AsyncArrayReader } from '@sage/xtrem-async-helper';
import { NodeAccessBindingStatus } from '../graphql/security/access-bindings';
import { Property } from '../properties';
import { Context, standardOperations } from '../runtime';
import { NodeState } from './node-state';

/**
 * This static class provides the method to compute the access restriction of a node state.
 *
 * @internal
 */
export abstract class StateAccess {
    /**
     * get access for the standard node operations (CRUD)
     * @param state
     * @returns
     */
    private static getStandardOperationAccess(state: NodeState): AsyncArrayReader<NodeAccessBindingStatus> {
        const copyOperations = [...standardOperations];
        return asyncArray(copyOperations).map(async operation => {
            const name = `$${operation}`;
            // If a node is frozen then update is implicitly inactive
            if (operation === 'update' && (await state.isNodeFrozen()))
                return { name, status: 'inactive' } as NodeAccessBindingStatus;

            const status = (
                await Context.accessRightsManager.getUserAccessFor(state.context, state.factory.name, operation)
            ).status;

            return { name, status };
        });
    }

    /**
     * get access for the custom mutation and query node operations
     * @param state
     * @returns
     */
    private static getNodeOperationAccess(
        state: NodeState,
        options?: { isPublished?: boolean },
    ): AsyncArrayReader<NodeAccessBindingStatus> {
        const nodeOperations = [...state.factory.mutations, ...state.factory.queries].filter(
            nodeOperation => options?.isPublished == null || nodeOperation.isPublished === options?.isPublished,
        );

        return asyncArray(nodeOperations).map(async nodeOperation => {
            const name = nodeOperation.name;
            let status = (await Context.accessRightsManager.getUserAccessFor(state.context, state.factory.name, name))
                .status;

            if (status !== 'authorized') {
                if (nodeOperation && nodeOperation.isGrantedByLookup) {
                    status = (
                        await Context.accessRightsManager.getUserAccessFor(state.context, state.factory.name, 'lookup')
                    ).status;
                }
            }

            return { name, status };
        });
    }

    /**
     * get access for a specified property
     * @param state
     * @param property
     * @returns
     * @internal
     */
    static async getPropertyAccess(state: NodeState, property: Property): Promise<NodeAccessBindingStatus> {
        const name = property.name;
        if (!(await property.isEnabledByServiceOptions(state.context))) {
            return { name, status: 'unavailable' };
        }

        let status = (await Context.accessRightsManager.getUserAccessFor(state.context, state.factory.name, name))
            .status;

        if (status === 'authorized') {
            const isReadonly =
                (!['constructed', 'created'].includes(state.status) && (await state.isPropertyFrozen(property))) ||
                ((await state.vendor) != null && !property.isOwnedByCustomer);

            if (isReadonly) status = 'readonly';
        }

        return { name, status };
    }

    /**
     * Get restricted access of the current node state for the current user
     * @param state
     * @param options
     * @returns
     * @internal
     */
    static async getNodeAccess(
        state: NodeState,
        options?: { selectedPropertyNames?: string[]; isPublished?: boolean },
    ): Promise<NodeAccessBindingStatus[]> {
        const bindings: NodeAccessBindingStatus[] = [];
        // standard operation access
        const standardOperationAccess = await StateAccess.getStandardOperationAccess(state)
            .filter(operationAccess => operationAccess.status !== 'authorized')
            .toArray();
        if (standardOperationAccess.length > 0) bindings.push(...standardOperationAccess);

        // custom query and mutation access
        const nodeOperationAccess = await StateAccess.getNodeOperationAccess(state, options)
            .filter(operationAccess => operationAccess.status !== 'authorized')
            .toArray();
        if (nodeOperationAccess.length > 0) bindings.push(...nodeOperationAccess);

        // selected property access
        const selectedProperties: Property[] = [];
        (options?.selectedPropertyNames || []).forEach(selectedPropertyName => {
            const selectedProperty = state.factory.properties.find(
                property => selectedPropertyName !== '_access' && property.name === selectedPropertyName,
            );
            if (selectedProperty) selectedProperties.push(selectedProperty);
        });

        const selectedPropertiesAccess = await asyncArray(selectedProperties)
            .map(selectedProperty => StateAccess.getPropertyAccess(state, selectedProperty))
            .filter(selectedPropertyAccess => selectedPropertyAccess.status !== 'authorized')
            .toArray();
        if (selectedPropertiesAccess.length > 0) bindings.push(...selectedPropertiesAccess);

        return bindings;
    }
}
