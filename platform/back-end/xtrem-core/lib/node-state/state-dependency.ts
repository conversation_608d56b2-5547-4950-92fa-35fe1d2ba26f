/** @ignore */ /** */
import { AnyValue, AsyncResponse } from '@sage/xtrem-async-helper';
import { Property } from '../properties';
import { NodeState } from './node-state';

/**
 * This static class provides methods to limit the access on a state to the dependencies of a property.
 *
 * If property A depends on properties B and C, rules that are triggered from A can access B and C
 * but rules triggered from B or C cannot access A.
 * This policy avoids infinite recursions when we execute the rules.
 * It enforces a predictable and safe order of execution.
 *
 * @internal
 */
export abstract class StateDependency {
    /** Executes body with access limited to property and its dependencies */
    static async withDependenciesRestriction<V extends AnyValue>(
        state: NodeState,
        property: Property,
        body: () => AsyncResponse<V>,
    ): Promise<V> {
        const oldLimit = state.dependencyLimit;
        state.dependencyLimit = property.dependencyIndex!;
        try {
            return await body();
        } finally {
            state.dependencyLimit = oldLimit;
        }
    }

    /** Checks that property is accessible given the dependency restrictions of the current rule.  */
    static checkDependencyLimit(state: NodeState, property: Property): void {
        if (property.dependencyIndex! > state.dependencyLimit && !state.isOld) {
            const fromProperty = state.factory.properties.find(p => p.dependencyIndex === state.dependencyLimit)!;
            throw state.propertySystemError(
                property,
                `property cannot be accessed while executing rule for ${fromProperty.fullName}. Please check your 'dependsOn' decorators.`,
            );
        }
    }
}
