/** @packageDocumentation @module runtime */
import { AnyRecord, asyncArray } from '@sage/xtrem-async-helper';
import { Dict, LogicError } from '@sage/xtrem-shared';
import * as _ from 'lodash';
import { Property } from '../properties/property';
import { Context } from '../runtime/context';
import { NodeFactory } from '../runtime/node-factory';
import { NodeState } from './node-state';
import { StateGetValue } from './state-get-value';

/** @internal */
// TODO: move this class to context when context is refactored
export class StateIntern {
    constructor(private readonly context: Context) {}

    /**
     * Add state to specific readonly/writable cache
     * @param key
     * @param state
     * @param writable
     * @returns
     */
    addState(key: string, state: NodeState, writable: boolean): void {
        if (state.factory.storage !== 'sql' && state.factory.storage !== 'external') return;
        let states;
        if (state.isTransient) {
            states = this.context.transaction.transientNodeStates;
        } else {
            states = writable
                ? this.context.transaction.writableNodeStates
                : this.context.transaction.readonlyNodeStates;
        }

        states[key] = state;
    }

    /**
     * Flush state from writable cache to readonly cache
     * @param state
     */
    flushState(state: NodeState): void {
        state.interningKeyValues.forEach(key => {
            delete this.context.transaction.writableNodeStates[key];
            /* When saving a node whose storage is based upon an external import web service the platform isn't
            aware of the changes performed by this external component and has to force a database read when a
            consecutive call to read is performed in the same context.
            */
            if (state.factory.storage !== 'external') this.context.transaction.readonlyNodeStates[key] = state;
        });
    }

    /**
     * Purge specific key from the the interning caches
     * @param context
     * @param interningKey
     */
    static deleteKey(context: Context, interningKey: string): void {
        delete context.transaction.transientNodeStates[interningKey];
        delete context.transaction.writableNodeStates[interningKey];
        delete context.transaction.readonlyNodeStates[interningKey];
    }

    /**
     * Add interning keys to set
     * @param state
     * @param collectedKeys
     */
    private static collectKeys(state: NodeState, collectedKeys: Set<string>): void {
        state.interningKeyValues.forEach(k => {
            collectedKeys.add(k);
        });
    }

    /**
     * Remove the interned states of mutable collections and references
     * @param internedState
     * @param collectedKeys
     */
    static async removeMutablePropertyStates(internedState: NodeState, collectedKeys: Set<string>): Promise<void> {
        // Remove mutable references and collection nodes keys from interning cache, so that we reload them
        await asyncArray(internedState.factory.strictMutableProperties).forEach(async prop => {
            if (prop.isReferenceProperty()) {
                const reference = internedState.references.get(prop.name);
                if (reference) {
                    this.collectKeys(reference.$.state, collectedKeys);
                }
            }

            if (prop.isCollectionProperty()) {
                const collection = internedState.collections.get(prop.name);
                if (collection) {
                    await collection.forEach(node => {
                        this.collectKeys(node.$.state, collectedKeys);
                    });
                }
            }
        });
    }

    /**
     * Remove the state and all it's vital children from the interned caches
     * @param state
     */
    static async removeStateWithVitalChildren(state: NodeState): Promise<void> {
        const vitalChildrenNodeNames = state.factory.extendedVitalFactories.map(factory => factory.rootFactory.name);

        const deleteKeys = async (internCache: Dict<NodeState>): Promise<void> => {
            const collectedKeys = new Set<string>();

            const willBeDeleted = async (cachedState: NodeState): Promise<boolean> => {
                if (cachedState === state) return true;

                if (!cachedState.factory.isVitalChild) return false;

                const parentProperty = cachedState.factory.vitalParentProperty;
                const parentReferenceValue = (await StateGetValue.getReferenceValue(cachedState, parentProperty))!;
                if (!parentReferenceValue) {
                    if (cachedState.isTransient) {
                        return false;
                    }
                    throw new LogicError('Expecting a valid parent reference for non transient node!');
                }
                return willBeDeleted(parentReferenceValue.$.state);
            };

            await asyncArray(
                //  Filter the list of cached states to the list of vital children
                Object.keys(internCache).filter(key =>
                    vitalChildrenNodeNames.some(nodeName => key.startsWith(nodeName)),
                ),
            ).forEach(async key => {
                const internedState = internCache[key];

                if (await willBeDeleted(internedState)) {
                    this.collectKeys(internedState, collectedKeys);
                    await this.removeMutablePropertyStates(internedState, collectedKeys);
                }
            });

            collectedKeys.forEach(key => {
                delete internCache[key];
            });
        };

        if (vitalChildrenNodeNames.length > 0) {
            // process transient interning cache
            await deleteKeys(state.context.transaction.transientNodeStates);
            // process writable interning cache
            await deleteKeys(state.context.transaction.writableNodeStates);
            // process readonly interning cache
            await deleteKeys(state.context.transaction.readonlyNodeStates);
        }

        // remove the main state from the cache
        this.removeState(state);
    }

    /**
     * Remove the state from the intern and load cache
     * @param state
     */
    static removeState(state: NodeState): void {
        state.interningKeyValues.forEach(interningKey => {
            this.deleteKey(state.context, interningKey);
        });
    }

    /**
     * Get the cached state from the relevant interning cache, for the specified key
     * @param key
     * @param writable
     * @returns
     */
    getState(key: string, writable: boolean): NodeState | undefined {
        // if writable exists return it, even if asking for readonly
        let state: NodeState | undefined = this.context.transaction.writableNodeStates[key];

        // if asking for writable ignore readonly cache.
        if (!state && !writable) {
            state = this.context.transaction.readonlyNodeStates[key];
            // TODO: investigate if we should reset node?.$.state.collections
            // node?.$.state.collections = {};
        }
        if (!state) {
            state = this.context.transaction.transientNodeStates[key];
            if (state && !state.isTransient) {
                delete this.context.transaction.transientNodeStates[key];
                state = undefined;
            }
        }
        return state;
    }

    /**
     * Find a state in the relevant interning cache, if it is not found undefined is returned.
     * @param state
     * @param writable
     * @returns
     */
    static findState(state: NodeState, writable: boolean): NodeState | undefined {
        // keys to intern the state with
        const interningKeys = [...state.interningKeyValues];

        if (interningKeys.length === 0) {
            // At least one property from the PK is not set : do not intern this node
            throw new LogicError(`${state.factory.name}: no interning keys found.`);
        }

        // loop through state interned keys and find the first match
        for (let index = 0; index < interningKeys.length; index += 1) {
            const interningKey = interningKeys[index];
            const found = state.context.intern.getState(interningKey, writable);
            if (found) {
                return found;
            }
        }
        return undefined;
    }

    /**
     * Clean interning caches of states with data that match the data record passed in
     * @param context
     * @param factory
     * @param data
     */
    static async removeStateWithData(context: Context, factory: NodeFactory, data: AnyRecord): Promise<void> {
        const deleteStates = async (internCache: Dict<NodeState>): Promise<void> => {
            // collect all the states of the factory that is being purged
            const states = Object.keys(internCache)
                .filter(key => key.startsWith(factory.rootFactory.name))
                .map(key => internCache[key]);
            if (Object.keys(data).length === 0) {
                // an empty object was passed for the data, therefore we purge all states for the relevant factory
                await asyncArray(states).forEach(state => this.removeStateWithVitalChildren(state));
            } else {
                // Purge all the states with values that match the data passed
                await asyncArray(
                    states.filter(state => {
                        const stateValues = _.pick(state.values, Object.keys(data));
                        return Object.keys(data)
                            .map(k => data[k] === stateValues[k])
                            .every(result => result);
                    }),
                ).forEach(state => this.removeStateWithVitalChildren(state));
            }
        };

        //  process the transient cache
        await deleteStates(context.transaction.transientNodeStates);
        //  process the writable cache
        await deleteStates(context.transaction.writableNodeStates);
        //  process the readonly cache
        await deleteStates(context.transaction.readonlyNodeStates);
    }

    /**
     * Can the passed state be interned
     * @param state
     * @returns
     */
    private static canBeInterned(state: NodeState): boolean {
        if (state.isOnlyForLookup) return false;
        // TO REVIEW: We do not intern transient node states with true id (usually transient input)
        if (state.isTransient && typeof state.values._id === 'number' && state.values._id > 0) return false;
        if (!state.factory.keyProperties) return false;
        // Should have data for at least one unique key
        return state.interningKeyValues.length > 0;
    }

    /**
     * If a property value was updated, we need to update the interning caches keys,
     * if the property value was part of the cache key
     * @param state
     * @param property
     * @returns
     */
    static updatePropertyInterningKeys(state: NodeState, property: Property): void {
        // If there is no oldState we cannot determine the previous value and update the keys
        if (!state.oldState) return;
        // If the property is not part of any unique keys, then there is nothing to
        if (this.getPropertyUniqueKeys(state, property).length === 0) return;

        // Get the interning keys of the old state
        const oldKeys = this.getPropertyInterningKeys(state.oldState, property);

        // delete the old key values
        Object.values(oldKeys).forEach(interningKey => {
            this.deleteKey(state.context, interningKey);
        });

        const writable = state.isWritable;
        // Get the internings keys of the updated state where the property is used
        const newKeys = this.getPropertyInterningKeys(state, property);

        // update the cache with the new keys
        Object.values(newKeys).forEach(interningKey => {
            state.context.intern.addState(interningKey, state, writable);
        });
    }

    static updateInternCache(state: NodeState): void {
        if (!StateIntern.canBeInterned(state)) return;
        const writable = state.isWritable;

        // if the state was not found in the interning cache, we add the state into the cache for all keys
        state.interningKeyValues.forEach(interningKey => {
            // findState excludes the current state so we need to check if any of the current keys exists in the cache
            state.context.intern.addState(interningKey, state, writable);
        });
    }

    /**
     * Intern the passed in state
     * @param state
     * @param options
     * @returns
     */
    static intern(state: NodeState, options?: { forCreate: boolean }): NodeState {
        if (!StateIntern.canBeInterned(state)) return state;
        const writable = state.isWritable;

        const found = this.findState(state, writable);
        if (found) {
            if (state.factory.isContentAddressable) return found;
            if (options?.forCreate && !found.isTransient) {
                throw new LogicError(
                    `No cached state is expected for ${found.factory.name} key: ${found.keyToken} [${found.interningKeyValues}]`,
                );
            }
            // Do not consider transient state if we are creating a new state from context.create
            if (!(options?.forCreate && found.isTransient)) {
                return found;
            }
        }
        // if the state was not found in the interning cache, we add the state into the cache for all keys
        state.interningKeyValues.forEach(interningKey => {
            // findState excludes the current state so we need to check if any of the current keys exists in the cache
            state.context.intern.addState(interningKey, state, writable);
        });

        return state;
    }

    static getInterningKeysFromValues(factory: NodeFactory, values: AnyRecord): Dict<string> {
        return factory.uniqueKeyProperties
            .filter(uniqueKey => uniqueKey.every(propertyName => values[propertyName] != null))
            .reduce((r, k) => {
                r[k.join()] = `${factory.rootFactory.name}:${k
                    .map(propertyName => {
                        let valueKey = propertyName;
                        if (propertyName === '_constructor' && !factory.isAbstract)
                            return `${valueKey}:${factory.name}`;
                        const property = factory.findProperty(propertyName);
                        // If the properties factory is not the same as the root factory,
                        // then there is a chance in the inheritance tree, that to subnodes may have the same
                        // property name. To address this we prefix the property name with the subnode factory
                        // the property is loaded from, thereby making the key unique to the subnode.
                        // The exception to this rule is the _id property, which is the same in the subnode and every abstract node.
                        if (property.factory.name !== factory.rootFactory.name && propertyName !== '_id')
                            valueKey = `${property.factory.name}.${propertyName}`;
                        return `${valueKey}:${values[propertyName]}`;
                    })
                    .join()}`;
                return r;
            }, {} as Dict<string>);
    }

    /**
     * State unique key values for node interning
     * Format: <Root factory name>:<key property1 name>:<key property1 value>,<key property2 name>:<key property2 value>, ...
     * Examples:
     *      Currency:_id:1
     *      Currency:code:USD
     *      BaseDocument:_id:1
     *      BaseDocumentLine:_id:1
     *      BaseDocumentLine:_sortValue:100,document:1
     */
    static getInterningKeys(state: NodeState): Dict<string> {
        // On external nodes's if _id is not set, call the property getter that will call the ExternalStorageManager.getId method
        // and set the _id value
        const values =
            state.factory.storage === 'external' && state.values._id == null
                ? { ...state.values, _id: state.node._id }
                : state.values;
        return this.getInterningKeysFromValues(state.factory, values);
    }

    /**
     * Get the states unique keys where a property is used
     * @param state
     * @param property
     * @returns
     */
    static getPropertyUniqueKeys(state: NodeState, property: Property): string[][] {
        return state.factory.uniqueKeyProperties.filter(uniqueKey => uniqueKey.includes(property.name));
    }

    /**
     * Get the states unique interning key values where a property is used
     * @param state
     * @param property
     * @returns
     */
    static getPropertyInterningKeys(state: NodeState, property: Property): Dict<string> {
        const propertyUniqueKeys = this.getPropertyUniqueKeys(state, property).map(uniqueKey => uniqueKey.join());
        return _.pick(state.interningKeys, propertyUniqueKeys);
    }
}
