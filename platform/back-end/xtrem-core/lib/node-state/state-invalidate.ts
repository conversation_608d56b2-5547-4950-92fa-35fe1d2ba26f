/** @ignore */ /** */
import { AnyValue, asyncArray } from '@sage/xtrem-async-helper';
import { BaseCollection } from '../collections';
import { useDefaultValue } from '../decorators';
import { Property } from '../properties';
import { valueComparator } from '../runtime';
import { loggers } from '../runtime/loggers';
import { Node } from '../ts-api';
import { NodeState } from './node-state';
import { StateDependency } from './state-dependency';
import { StateInit } from './state-init';
import { StateSetValue } from './state-set-value';
/**
 * This static class provides the methods that manage the invalidation of properties.
 *
 * If a property carries a `dependsOn` decorators it will be invalidated
 * whenever one of its `dependsOn` properties is modified.
 * If an invalidated property has an `updatedValue` rule, this rule will be triggered to obtain its new value.
 *
 * @internal
 */
export abstract class StateInvalidate {
    /** Invalidates all the values of all the properties given in the `properties` array  */
    private static async invalidatePropertyValues(state: NodeState, properties: Property[]): Promise<void> {
        const walk = async (st: NodeState, i: number): Promise<void> => {
            const property = properties[i];
            if (i < properties.length - 1) {
                if (!property.isMutable) {
                    loggers.runtime.warn(
                        `${property.factory.name}.${property.name}: Non vital/mutable property in dependsOn`,
                    );
                }
                await StateDependency.withDependenciesRestriction(state, property, async () => {
                    const value = await st.getPropertyValue(property);
                    if (value == null) return;
                    if (value instanceof BaseCollection) {
                        await value.forEach(node => walk(node.$.state, i + 1));
                    } else if (value instanceof Node) {
                        await walk(value.$.state, i + 1);
                    } else {
                        throw st.propertySystemError(property, `property value is not a node: ${typeof value}`);
                    }
                });
            } else {
                st.invalidProperties.add(property.name);
            }
        };
        await walk(state, 0);
    }

    /** Invalidates all the properties that depend on `property` */
    static async invalidateDependantProperties(state: NodeState, property: Property): Promise<void> {
        if (!property.propagatesTo) return;
        // Do not invalidate when vital parent is modified by propagateKeyChangeToTransaction during save
        if (property.isVitalParent || property.isAssociationParent) return;
        await asyncArray(property.propagatesTo).forEach(properties =>
            StateInvalidate.invalidatePropertyValues(state, properties),
        );
    }

    /** Clears the invalidated flag on a property. Returns whether the flag has been cleared or not. */
    static clearInvalidated(state: NodeState, property: Property): boolean {
        const isInvalid = state.invalidProperties.has(property.name);
        if (isInvalid) {
            state.invalidProperties.delete(property.name);
            return true;
        }
        return false;
    }

    /**
     * Like StateSetValue.setPropertyValue but inhibits the readonly scope
     * So that we can assign the value when we revalidate a property, even if
     * called a rule executed in readonly mode.
     */
    private static async setPropertyValue(
        state: NodeState,
        property: Property,
        path: string[],
        value: AnyValue,
    ): Promise<void> {
        await state.context.withoutReadonlyScopes(() => StateSetValue.setPropertyValue(state, property, path, value));
    }

    /**
     * Updates an invalidated property by calling its `updatedValue` rule, if any.
     * If `updatedValue` returns undefined, the `defaultValue` rule is called.
     */
    private static async updatePropertyValue(state: NodeState, property: Property, path: string[]): Promise<void> {
        if ((property.getValue || property.computeValue) && property.cacheComputedValue) {
            // Invalidate cached computed value
            const cacheKey = state.keyToken;
            const cacheCategory = property.fullName;
            state.context.cache.deleteKey(cacheCategory, cacheKey);
        }

        const updatedValue = property.updatedValue;
        // If there no updatedValue rule, we do nothing. We keep existing value and invalidation flag has been cleared.
        if (updatedValue === undefined) return;

        await StateDependency.withDependenciesRestriction(state, property, async () => {
            // Note : the node is set as read-only when invoking the updateValue() function to protect it.
            // A 'updateValue' function should only be used to return a value and must not update the object.
            const oldValue = await state.getPropertyValue(property);
            let newValue: AnyValue | typeof useDefaultValue = updatedValue;
            // Test markers first
            if (typeof updatedValue === 'function') {
                // if the property is not a localized string => extract the localized value
                newValue = !property.isLocalized
                    ? await state.context.withoutLocalizedTextAsJson(() =>
                          state.context.withReadonlyScope(() => property.executeRule(state, 'updatedValue')),
                      )
                    : await state.context.withReadonlyScope(() => property.executeRule(state, 'updatedValue'));
            }
            if (newValue === useDefaultValue) {
                newValue = await StateInit.getDefaultValue(state, property);
            }
            if (valueComparator(newValue, oldValue) !== 0) {
                await StateInvalidate.setPropertyValue(state, property, path, newValue);
                await StateInvalidate.invalidateDependantProperties(state, property);
            }
        });
    }

    /** Updates a property if it was invalidated */
    static async validatePropertyValue(state: NodeState, property: Property, path: string[]): Promise<void> {
        if (StateInvalidate.clearInvalidated(state, property)) {
            await StateInvalidate.updatePropertyValue(state, property, path);
        }
    }
}
