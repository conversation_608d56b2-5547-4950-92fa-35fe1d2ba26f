import { asyncArray } from '@sage/xtrem-async-helper';
import { ValidationSeverity } from '@sage/xtrem-shared';
import { MutableCollection } from '../collections';
import { CollectionProperty, Property, ReferenceProperty } from '../properties';
import { SystemProperties } from '../system-exports';
import { Node, ValidationContext } from '../ts-api';
import { lazyLoadedMarker } from './lazy-loaded-marker';
import { NodeState } from './node-state';
import { StateInvalidate } from './state-invalidate';
import { StateUtils } from './state-utils';

/**
 * This static class provides the methods to control a node state before saving to the database.
 *
 * @internal
 */
export abstract class StatePrepare {
    private static async propertyCanBeControlled(state: NodeState, property: Property): Promise<boolean> {
        if (StateUtils.isPropertyGetterOnly(property)) return false;
        if (!(await StateUtils.isEnabledByServiceOptions(state, property))) return false;
        return true;
    }

    static async skipPropertyInPrepareAndControl(state: NodeState, property: Property): Promise<boolean> {
        if (state.isPropertyValueDeferred(property)) return true;

        if (state.values[property.name] === lazyLoadedMarker) {
            // This lazy loaded property was not resolved, nothing to control
            return true;
        }

        // Skip validation of create and update user properties, as the population of these properties will be done
        // by the trigger for sql nodes and the external system for external nodes
        if (SystemProperties.isUserManagementProperty(property)) {
            return true;
        }

        return !(await this.propertyCanBeControlled(state, property));
    }

    private static async prepareReferenceProperty(
        state: NodeState,
        property: ReferenceProperty,
        path: string[],
    ): Promise<void> {
        if (property.prepare) {
            await property.executeRule(state, 'prepare', new ValidationContext(state.context, path));
        }
        const value = (await state.getPropertyValue(property)) as Node | null;
        if (value == null) return;

        const childPath = [...path, property.name];
        await StateUtils.withValidationErrorRethrow(state, property, childPath, async () => {
            await this.prepareState(value.$.state, childPath);
        });
    }

    private static async prepareCollectionProperty(
        state: NodeState,
        property: CollectionProperty,
        path: string[],
    ): Promise<void> {
        if (property.prepareBegin) {
            await property.executeRule(state, 'prepareBegin', new ValidationContext(state.context, path));
        }

        const nodes = (await state.getPropertyValue(property)) as MutableCollection;
        await nodes.forEach(async child => {
            // The path will include the _id of the current collection node, to identify the collection entry
            // in the diagnoses.
            const childPath = MutableCollection.fillPath(path, child);
            await StateUtils.withValidationErrorRethrow(state, property, childPath, async () => {
                await this.prepareState(child.$.state, childPath);
            });
        });

        if (property.prepareEnd) {
            await property.executeRule(state, 'prepareEnd', new ValidationContext(state.context, path));
        }
    }

    private static async prepareProperty(state: NodeState, property: Property, path: string[]): Promise<void> {
        await StateUtils.withValidationErrorRethrow(state, property, path, async () => {
            if (await this.skipPropertyInPrepareAndControl(state, property)) return;

            await StateInvalidate.validatePropertyValue(state, property, path);
            if (property.isCollectionProperty() && property.isMutable) {
                await this.prepareCollectionProperty(state, property, path);
            } else if (property.isReferenceProperty() && property.isMutable) {
                await this.prepareReferenceProperty(state, property, path);
            } else if (property.prepare) {
                await property.executeRule(state, 'prepare', new ValidationContext(state.context, path));
            }
        });
    }

    /** @internal */
    static async prepareState(state: NodeState, path: string[]): Promise<boolean> {
        if (state.skipSave) return true;

        if (state.factory.prepareBegin) {
            await state.factory.executeRule(state, 'prepareBegin', new ValidationContext(state.context, path));
        }

        await asyncArray(state.factory.properties).forEach(prop =>
            this.prepareProperty(state, prop, [...path, prop.name]),
        );

        if (state.factory.prepareEnd) {
            await state.factory.executeRule(state, 'prepareEnd', new ValidationContext(state.context, path));
        }

        return !state.context.diagnoses.some(diagnose => diagnose.severity >= ValidationSeverity.error);
    }
}
