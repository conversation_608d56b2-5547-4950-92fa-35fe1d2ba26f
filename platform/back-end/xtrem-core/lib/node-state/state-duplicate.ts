/** @packageDocumentation @module runtime */
import { AnyNonNullableValue, AnyR<PERSON>ord, AnyValue, asyncArray } from '@sage/xtrem-async-helper';
import * as _ from 'lodash';
import { useDefaultValue } from '../decorators';
import { NodeFactory } from '../runtime/node-factory';
import { BaseCollection, ForeignNodeProperty, Property } from '../system-exports';
import { AnyNode, Node, NodeCreateData, NodePayloadOptions } from '../ts-api';
import { NodeState } from './node-state';
import { StateDependency } from './state-dependency';
import { StateInit } from './state-init';

/** Method to duplicate a node */
export class StateDuplicate {
    /**
     * Negate vital references on vital graph for new records to be inserted
     * clear vendor values
     * @param factory
     * @param data
     * @returns
     */
    private static convertVitalData(
        factory: NodeFactory,
        data: AnyRecord,
        mappedIds: Map<string, number>,
        recurring = false,
    ): void {
        if (!data) {
            return;
        }

        if (factory.isContentAddressable && data._id) {
            delete data._id;
        }

        if (data._id) {
            const negatedId = -Math.abs(Number(data._id));
            mappedIds.set(`${factory.name}~${data._id}`, negatedId);
            data._id = negatedId;
        }

        // Filtering properties to check if it is included in the data allows us to bypass checking for service options
        const props = factory.properties.filter(
            prop => prop.isForeignNodeProperty() && !prop.isSystemProperty && Object.keys(data).includes(prop.name),
        );

        if (factory.hasVendorProperty && data._vendor != null) {
            delete data._vendor;
        }

        props.forEach(prop => {
            if (prop.isMutable) {
                if (prop.isCollectionProperty()) {
                    (data[prop.name] as AnyValue[]).forEach((line: AnyRecord) => {
                        StateDuplicate.convertVitalData(prop.targetFactory, line, mappedIds, true);
                    });
                }
                if (prop.isReferenceProperty()) {
                    StateDuplicate.convertVitalData(prop.targetFactory, data[prop.name] as AnyRecord, mappedIds, true);
                }
            } else if (prop.isVitalParent && !prop.isAssociationParent) {
                if (!recurring) {
                    delete data._sortValue;
                }
            }
        });
    }

    /**
     * Negate non-vital references on vital graph
     * @param factory
     * @param data
     * @param mappedIds
     * @returns
     */
    private static convertNonVitalReferenceData(
        factory: NodeFactory,
        data: AnyRecord,
        mappedIds: Map<string, number>,
    ): void {
        if (!data) {
            return;
        }

        // Filtering properties to check if it is included in the data allows us to bypass checking for service options
        factory.properties
            .filter(prop => prop.isForeignNodeProperty() && Object.keys(data).includes(prop.name))
            .forEach(prop => {
                if (prop.isMutable) {
                    if (prop.isCollectionProperty()) {
                        (data[prop.name] as AnyValue[]).forEach((line: AnyRecord) => {
                            StateDuplicate.convertNonVitalReferenceData(prop.targetFactory, line, mappedIds);
                        });
                    }
                    if (prop.isReferenceProperty()) {
                        StateDuplicate.convertNonVitalReferenceData(
                            prop.targetFactory,
                            data[prop.name] as AnyRecord,
                            mappedIds,
                        );
                    }
                } else if (
                    data[prop.name] &&
                    prop.isReferenceProperty() &&
                    mappedIds.has(`${prop.targetFactory.name}~${(data[prop.name] as AnyRecord)?._id}`)
                ) {
                    data[prop.name] = mappedIds.get(
                        `${prop.targetFactory.name}~${(data[prop.name] as AnyRecord)?._id}`,
                    );
                } else if (data[prop.name] && Array.isArray(data[prop.name]) && prop.isReferenceArrayProperty()) {
                    data[prop.name] = (data[prop.name] as AnyRecord[]).map(val => {
                        const mappedValue = `${prop.targetFactory.name}~${val._id}`;

                        if (mappedIds.has(mappedValue)) return mappedIds.get(mappedValue);
                        return val;
                    });
                }
            });
    }

    private static async getForeignNodePropertyDuplicatedValue(
        state: NodeState,
        property: ForeignNodeProperty,
        propertyValue: AnyNonNullableValue,
        propertyPath: string[],
        deferredProperties?: string[],
    ): Promise<NodeCreateData<AnyNode> | NodeCreateData<AnyNode>[] | undefined> {
        if (property.isCollectionProperty() && Array.isArray(propertyValue)) {
            const collection = (await state.getPropertyValue(property)) as BaseCollection;
            return asyncArray(propertyValue)
                .map(async (v, i) => {
                    const node = await collection.at(i);
                    if (!node) throw new Error(`${propertyPath.join('.')}: collection value not found at index ${i}`);
                    return this.setDuplicatedValuesInData(node.$.state, v, propertyPath, deferredProperties);
                })
                .toArray();
        }

        if (property.isReferenceProperty()) {
            const node = (await state.getPropertyValue(property)) as Node;
            if (node == null) return undefined;
            return this.setDuplicatedValuesInData(
                node.$.state,
                propertyValue as NodeCreateData<AnyNode>,
                propertyPath,
                deferredProperties,
            );
        }
        return undefined;
    }

    private static async getPropertyDuplicatedValue(
        state: NodeState,
        property: Property,
        deferredProperties?: string[],
    ): Promise<AnyValue> {
        let newValue = property.duplicatedValue;
        if (newValue !== undefined) {
            if (typeof newValue === 'function') {
                newValue = await StateDependency.withDependenciesRestriction(state, property, () =>
                    state.context.withReadonlyScope(() => property.executeRule(state, 'duplicatedValue')),
                );
            }
            if (newValue === useDefaultValue) {
                if (property.deferredDefaultValue !== undefined) {
                    // Set default value for initial insert, will be updated to the deferred value during commit
                    newValue = await StateInit.getDefaultValue(state, property);
                    // Add deferred property to the list, to be queued on the duplicated node state
                    if (deferredProperties !== undefined && !deferredProperties.includes(property.name)) {
                        deferredProperties.push(property.name);
                    }
                } else {
                    // Use the getDefaultValue rule to obtain the new value
                    newValue = await StateInit.getDefaultValue(state, property);
                }
            } else if (newValue === null) {
                // Reset to the type's default value
                newValue = property.getTypeDefaultValue();
            }
            await state.setPropertyValue(property, newValue);
            return newValue;
        }

        if (property.isStringProperty() && property.isStoredEncrypted) {
            const defaultValue = StateInit.getDefaultValue(state, property);
            await state.setPropertyValue(property, defaultValue);
            return defaultValue;
        }

        return undefined;
    }

    /** Apply duplicatedValue */
    private static async setDuplicatedValuesInData(
        state: NodeState,
        nodeData: NodeCreateData<AnyNode>,
        path?: string[],
        deferredProperties?: string[],
    ): Promise<NodeCreateData<AnyNode>> {
        const result = { ...nodeData };
        await asyncArray(state.factory.properties).forEach(async property => {
            const propertyValue = result[property.name];
            const propertyName = property.name;
            const propertyPath = path == null ? [propertyName] : [...path, propertyName];
            if (property.isForeignNodeProperty() && property.isMutable && property.duplicatedValue === undefined) {
                if (propertyValue != null) {
                    const foreignValue = await this.getForeignNodePropertyDuplicatedValue(
                        state,
                        property,
                        propertyValue,
                        propertyPath,
                        deferredProperties,
                    );
                    if (foreignValue !== undefined) result[propertyName] = foreignValue;
                }
            } else {
                const newPropertyValue = await this.getPropertyDuplicatedValue(state, property, deferredProperties);
                if (newPropertyValue !== undefined) result[propertyName] = newPropertyValue;
            }
        });
        return result;
    }

    private static async duplicateState(
        state: NodeState,
        payloadOptions?: NodePayloadOptions<AnyNode>,
        options?: { isTransient?: boolean; data?: AnyRecord },
    ): Promise<NodeState> {
        // Get node payload
        const payloadData = await state.payload(payloadOptions);

        // Negate all the _id values and references in the vital graph
        const mappedIds = new Map();
        StateDuplicate.convertVitalData(state.factory, payloadData, mappedIds);
        StateDuplicate.convertNonVitalReferenceData(state.factory, payloadData, mappedIds);
        if (!state.node.$.factory.duplicateAttachments) {
            payloadData._attachments = [];
        }
        const dummyNode = await state.context.create(state.factory.nodeConstructor, payloadData, {
            isOnlyForDuplicate: true,
            isTransient: true,
        });
        const deferredProperties = [] as string[];
        const transformedData = await this.setDuplicatedValuesInData(
            dummyNode.$.state,
            payloadData,
            undefined,
            deferredProperties,
        );

        if (options?.isTransient) {
            if (options.data) await dummyNode.$.set(options.data);
            return dummyNode.$.state;
        }

        const nodeData = options?.data ? _.merge(transformedData, options.data) : transformedData;

        // Create node with manipulated data
        const duplicateNode = await state.context.create(state.factory.nodeConstructor, nodeData, {
            duplicates: dummyNode,
        });

        // Set deferred properties
        deferredProperties.forEach(propertyName => {
            StateInit.queueDeferredDefaultValue(duplicateNode.$.state, state.factory.findProperty(propertyName));
        });

        return duplicateNode.$.state;
    }

    /**
     * Duplicate the passed in node state. Returns the unsaved node state
     */
    static duplicate(state: NodeState, data?: AnyRecord): Promise<NodeState> {
        return this.duplicateState(state, { withIds: true }, { data });
    }

    /**
     * Get a duplicated transient instance of the state passed in
     * The vital collection tree branches loaded can be limited with using the option limitCollections
     * This is to improve the performance of the getDuplicate query
     * @param state
     * @param options
     * @returns
     */
    static getDuplicate(state: NodeState, options?: { limitCollections?: number }): Promise<NodeState> {
        return this.duplicateState(
            state,
            { withIds: true, limitCollections: options?.limitCollections },
            { isTransient: true },
        );
    }
}
