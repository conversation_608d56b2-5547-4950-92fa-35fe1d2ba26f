import { AnyR<PERSON><PERSON>, AnyVal<PERSON>, async<PERSON><PERSON>y, AsyncResponse } from '@sage/xtrem-async-helper';
import { ForeignNodeProperty, ReferenceProperty } from '../properties';
import { JoinLiteralValue, Node } from '../ts-api';
import { NodeState } from './node-state';
import { StateLoad } from './state-load';
import { StateSetValue } from './state-set-value';

/**
 * This static class manages joins for reference and collection properties.
 *
 * Joins are very simple in 'sql' storage because we are always joining on _id columns.
 * They are more complex in 'external' storage because we have to handle composite keys.
 *
 * @internal
 */
export abstract class StateJoin {
    /** Returns the column value (not the property value, which is usually a reference) for the join */
    private static async getColumnValue(state: NodeState, propertyName: string): Promise<AnyValue> {
        // Lazy load if the value is undefined
        if (state.isThunk && state.values[propertyName] === undefined) await StateLoad.load(state);
        // Return the raw value.
        return state.values[propertyName];
    }

    /** Returns the join values if storage is 'external' */
    private static getExternalJoinValues(
        state: NodeState,
        property: ForeignNodeProperty,
        index?: number,
    ): AsyncResponse<AnyRecord> {
        if (!state.factory.externalStorageManager) {
            throw state.factory.logicError('external storage manager is missing');
        }
        return state.factory.externalStorageManager.getJoinValues(state.node, state.values, property.name, index);
    }

    /**
     * Adds _constructuctor to the join filter
     * This is called when generating the join filter for a reverse property that is inherited.
     */
    private static addConstructorToJoinFilter(
        property: ForeignNodeProperty,
        reverseProperty: ReferenceProperty,
        filter: AnyRecord,
    ): void {
        const rootReverseProperty = reverseProperty.rootProperty;
        if (rootReverseProperty.factory.baseFactory) {
            // We get there with BasePurchaseDocumentLine.document and we have to return without adding the _constructor condition.
            // We should be able to improve this and reactivate the throw after we complete the document/docuemnt line refactoring.
            // throw rootReverseProperty.logicError('reverse property can only be inherited from the root factory');
            return;
        }
        if (property.targetFactory.isAbstract) {
            throw property.logicError('property cannot be polymorphic because its reverse property is inherited');
        }
        filter._constructor = property.targetFactory.name;
    }

    private static buildJoinEntry(
        state: NodeState,
        rootProperty: ForeignNodeProperty,
        property: ForeignNodeProperty,
        member: any,
        path: string[],
    ): Promise<AnyValue> {
        if (typeof member === 'string') {
            return StateJoin.getColumnValue(state, member);
        }

        if (typeof member === 'function') {
            return member.call(state.node);
        }

        if (member instanceof JoinLiteralValue) {
            return Promise.resolve(member.value);
        }

        if (typeof member === 'object') {
            if (member === null) return Promise.resolve(null);
            if (Object.keys(member).every(k => !!property.targetFactory.propertiesByName[k]))
                return asyncArray(Object.keys(member)).reduce(async (r, k) => {
                    const val = member[k];
                    if (
                        typeof val === 'object' &&
                        !(member instanceof JoinLiteralValue || typeof member === 'function' || member === null)
                    ) {
                        const targetProperty = property.targetFactory.findProperty(k);
                        if (!targetProperty.isForeignNodeProperty())
                            throw targetProperty.logicError(`Invalid join element ${k}`);
                        r[k] = await this.buildJoinEntry(state, rootProperty, targetProperty, val, [...path, k]);
                    } else {
                        r[k] = await this.buildJoinEntry(state, rootProperty, property, val, [...path, k]);
                    }
                    return r;
                }, {} as AnyRecord);
        }

        throw rootProperty.logicError(
            `invalid join member. Expected string or function, got ${typeof member} for ${path.join('.')}`,
        );
    }

    /** Returns the join values if storage is 'sql' */
    private static async getSqlJoinValues(state: NodeState, property: ForeignNodeProperty): Promise<AnyRecord> {
        // if a foreign node property has a join, we return the join values
        // the elements of a join can either be a string (property name), JoinLiteralValue instance (literal) or a function
        // if it is a string, we return the value of the property with that name
        if (property.needsJoin() && property.join) {
            const { join } = property;
            const filter = await asyncArray(Object.keys(join)).reduce(async (r, k) => {
                const member = join[k];
                r[k] = await StateJoin.buildJoinEntry(state, property, property, member, [property.name, k]);
                return r;
            }, {} as AnyRecord);
            if (property.reverseReference) {
                const reverseProperty = property.targetFactory.findProperty(
                    property.reverseReference,
                ) as ReferenceProperty;
                if (reverseProperty.isInherited)
                    StateJoin.addConstructorToJoinFilter(property, reverseProperty, filter);
            }
            return filter;
        }

        if (property.isCollectionProperty() && property.getFilter)
            return property.getFilter.call(state.node) as AnyRecord;

        return { _id: await StateJoin.getColumnValue(state, property.name) };
    }

    /** Returns the join values */
    static getJoinValues(state: NodeState, property: ForeignNodeProperty): AsyncResponse<AnyRecord> {
        switch (state.factory.storage) {
            case 'external':
                return StateJoin.getExternalJoinValues(state, property);
            case 'sql':
                return StateJoin.getSqlJoinValues(state, property);
            default:
                return {};
        }
    }

    /** Returns the array join values if storage is 'sql' */
    private static async getSqlArrayJoinValues(
        state: NodeState,
        property: ForeignNodeProperty,
        index: number,
    ): Promise<AnyRecord> {
        const value = await StateJoin.getColumnValue(state, property.name);
        if (!Array.isArray(value)) {
            throw state.propertyDataInputError(property, {
                message: 'Invalid value in array property: {{value}}',
                key: '@sage/xtrem-core/invalid-value-in-array-property',
                data: { value },
            });
        }
        return { _id: value[index] };
    }

    /** Returns the array join values for given index */
    static getArrayJoinValues(
        state: NodeState,
        property: ForeignNodeProperty,
        index: number,
    ): AsyncResponse<AnyRecord> {
        switch (state.factory.storage) {
            case 'external':
                return StateJoin.getExternalJoinValues(state, property, index);
            case 'sql':
                return StateJoin.getSqlArrayJoinValues(state, property, index);
            default:
                return {};
        }
    }

    /** Assigns the join values when a reference is modified. */
    static async assignJoinedProperties(state: NodeState, property: ReferenceProperty, reference: Node): Promise<void> {
        if (!property.join) throw property.logicError('property has no join');

        const referenceState = reference.$.state;
        const assign = async (key: string, val: AnyValue): Promise<void> => {
            const sourceProperty = state.factory.findProperty(key);
            if (val !== undefined) await StateSetValue.setRawValue(state, sourceProperty, val);
        };

        await asyncArray(Object.keys(property.join)).forEach(async k => {
            if (!property.join) return;
            const member = property.join[k];
            // If the join is like `{ targetProp1: 'sourceProp', targetProp2() { return 5; } }` we assign as follows:
            // - sourceNode.sourceProp = referenceNode.targetProp1;
            // We only process targetProp1. Function members like targetProp2 are ignored.
            if (typeof member === 'string') {
                const targetVal = await StateJoin.getColumnValue(referenceState, k);
                await assign(member, targetVal);
            }
        });
    }

    /** Initializes the join values in a vital reference. */
    static initVitalReferenceJoin(state: NodeState, property: ReferenceProperty, data: AnyRecord): void {
        // This is the opposite of assignJoinedProperties.
        // If the join is like `{ targetProp1: 'sourceProp', targetProp2() { return 5; } }` we assign as:
        // - referenceNode.targetProp1 = sourceNode.sourceProp;
        // - referenceNode.targetProp2 = 5;
        // Note: Actually we don't assign the property values, we assign the raw column values from state.values.
        Object.keys(property.join || {}).forEach(k => {
            let value: AnyValue;
            if (!(k in data)) {
                const member = property.join![k];
                if (typeof member === 'function') {
                    value = member.call(state.node);
                } else if (typeof member === 'string') {
                    value = state.values[member];
                }
            }
            // if value is undefined we ignore it (silently!)
            if (value === undefined) return;

            if (data[k] === undefined) {
                data[k] = value;
            } else if (data[k] !== value) {
                throw state.propertyDataInputError(property, {
                    message: "cannot initialize join: values for '{{k}}' differ: expected {{data}}, got {{value}}",
                    key: '@sage/xtrem-core/cannot-initialize-join',
                    data: { k, data: data[k], value },
                });
            }
        });
    }
}
