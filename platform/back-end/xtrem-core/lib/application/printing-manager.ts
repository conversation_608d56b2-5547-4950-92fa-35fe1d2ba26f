import type { Context } from '../runtime';

export interface PrintingManager {
    getPrintingAssignmentDialogUrl(context: Context): Promise<string | null>;
    getListPrintingWizardUrl(context: Context): Promise<string | null>;
    getRecordPrintingWizardUrl(context: Context): Promise<string | null>;

    hasRecordPrintingTemplates(context: Context, page: string): Promise<boolean>;

    /**
     * Global async mutation name for printing a record list from the main list.
     * It must must have the following parameters:
     * - `filter`: The filter to apply to the record list, string.
     * - `nodeName`: The name of the node to print without vendor or package name such as `SalesOrder`, string.
     * - `parameters`: JSON string with the parameters to pass to print configuration extracted from the bulk action configuration page, string.
     * @param context
     */
    getRecordPrintingGlobalBulkMutationName(context: Context): Promise<string | null>;

    /**
     * Bulk action configuration URL for printing a record list from the main list.
     */
    getRecordListPrintingGlobalBulkActionConfigurationURL(context: Context): Promise<string | null>;
}
