import { AsyncResponse } from '@sage/xtrem-shared';
import { StaticThis } from '../decorators/decorator-utils';
import { Context } from '../runtime';
import { Node } from '../ts-api';
import { Application } from './application';
import { DataValidator } from './data-validator';

/**
 * Interface for the DataValidationManager service.
 */
export interface DataValidationManager {
    /**
     * Registers a data validator for a specific Node class.
     * @param nodeClass The Node class to register the validator for.
     * @param validator The data validator to register.
     */
    registerValidator<T extends Node>(nodeClass: StaticThis<T>, validator?: DataValidator<T>): void;

    /**
     * Builds the validation reports for the given application (one report per tenant)
     */
    buildReports(application: Application): AsyncResponse<void>;

    /**
     * Builds a validation report for the given writable context (will only concern the tenant of the writable context).
     * @param writableContext The writable context to generate the report for.
     * @returns The _id of the generated validation report.
     */
    buildReport(writableContext: Context): AsyncResponse<number>;

    /**
     * Retrieves all registered data validators.
     */
    getValidators(): DataValidator[];
}

export class DataValidationManagerStub implements DataValidationManager {
    // eslint-disable-next-line class-methods-use-this, @typescript-eslint/no-unused-vars
    registerValidator<T extends Node>(_nodeClass: StaticThis<T>, _validator?: DataValidator<T>): void {
        throw new Error('DataValidationManager manager is not registered.');
    }

    // eslint-disable-next-line class-methods-use-this
    getValidators(): DataValidator[] {
        throw new Error('DataValidationManager manager is not registered.');
    }

    // eslint-disable-next-line class-methods-use-this, @typescript-eslint/no-unused-vars
    buildReports(_application: Application): AsyncResponse<void> {
        throw new Error('DataValidationManager manager is not registered.');
    }

    // eslint-disable-next-line class-methods-use-this, @typescript-eslint/no-unused-vars
    buildReport(_writableContext: Context): AsyncResponse<number> {
        throw new Error('DataValidationManager manager is not registered.');
    }
}
