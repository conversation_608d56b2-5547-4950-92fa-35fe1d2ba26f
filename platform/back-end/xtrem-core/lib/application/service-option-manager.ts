import { asyncArray } from '@sage/xtrem-async-helper';
import { AsyncResponse, Dict, LogicError, SystemError } from '@sage/xtrem-shared';
import { topoSort } from '@sage/xtrem-toposort';
import * as _ from 'lodash';
import { Context } from '../runtime';
import { Application } from './application';
import { Package } from './package';
import { ServiceOption, ServiceOptionStatus } from './service-option';

/**
 * Base class for service option managers
 */
export abstract class ServiceOptionManager {
    // All the application's service options, indexed by name
    #serviceOptionsByName: Dict<ServiceOption> = {};

    // Sorted array of service options.
    // High-level service options are sorted before the low-level (usually hidden) ones that they activate.
    #sortedServiceOptions: ServiceOption[];

    constructor(readonly application: Application) {
        application.getPackages().forEach(pack => this.createServiceOptions(pack));
        Object.freeze(this.#serviceOptionsByName);
    }

    /** @internal - create service options for the passed package */
    private createServiceOptions(pack: Package): void {
        Object.keys(pack.serviceOptions).forEach(serviceOptionName => {
            const serviceOption = pack.serviceOptions[serviceOptionName];

            // If the serviceOption was loaded in another package, we throw an error
            if (this.#serviceOptionsByName[serviceOption.name]) {
                throw new SystemError(
                    `Service option ${serviceOption.name} is declared in more than one package. ${
                        this.#serviceOptionsByName[serviceOption.name].packageName
                    } and ${pack.name}.`,
                );
            }

            serviceOption.setPackageName(pack.name);
            this.#serviceOptionsByName[serviceOption.name] = serviceOption;
        });
    }

    /** @internal Sorts the service options */
    private sortServiceOptions(serviceOptions: ServiceOption[]): ServiceOption[] {
        const entries = serviceOptions.map(option => ({
            name: option.name,
            dependsOn: option.activates?.().map(opt => opt.name),
        }));

        const sorted = topoSort(entries).reverse();
        return sorted.map(entry => this.application.serviceOptionsByName[entry.name]);
    }

    /** Returns a toposorted array of all the application's service options */
    protected get sortedServiceOptions(): ServiceOption[] {
        if (!this.#sortedServiceOptions) {
            this.#sortedServiceOptions = this.sortServiceOptions(Object.values(this.application.serviceOptionsByName));
            Object.freeze(this.#sortedServiceOptions);
        }
        Object.freeze(this.#sortedServiceOptions);
        return this.#sortedServiceOptions;
    }

    /** Returns the service options indexed by name */
    get serviceOptionsByName(): Dict<ServiceOption> {
        return this.#serviceOptionsByName;
    }

    /** Returns whether a service option status is enabled or not by the config file */
    static isServiceOptionStatusEnabled(status: ServiceOptionStatus): boolean {
        if (Context.getConfigurationValue('serviceOptionsLevel') === 'released') {
            return status === 'released';
        }
        if (Context.getConfigurationValue('serviceOptionsLevel') === 'experimental') {
            return status !== 'workInProgress';
        }
        return true;
    }

    /**
     * Returns whether a service option is enabled or not.
     * Enabled means enabled by configuration and by tenant.
     * @param context the context
     * @param ServiceOption the service option
     */
    abstract isServiceOptionEnabled(context: Context, serviceOption: ServiceOption): AsyncResponse<boolean>;

    /**
     * Gets the array of enabled service options
     * @param context: the context
     */
    abstract getEnabledServiceOptions(context: Context): Promise<ServiceOption[]>;

    /** Low-level method to set and persist the isActive state of a service option */
    abstract setServiceOptionActiveState(
        context: Context,
        serviceOption: ServiceOption,
        isActive: boolean,
    ): Promise<void>;

    /** Activates a single service option */
    private async activateServiceOption(
        context: Context,
        serviceOption: ServiceOption,
        visited = {} as Dict<boolean>,
    ): Promise<void> {
        if (visited[serviceOption.name]) return;
        visited[serviceOption.name] = true;

        if (!(await context.isServiceOptionEnabled(serviceOption))) {
            await this.setServiceOptionActiveState(context, serviceOption, true);
            context.setServiceOptionsEnabledFlag(serviceOption, true);
        }

        // find all the service options that are activated by this one, and activate them (recursively)
        const activatedOptions = serviceOption.activates?.();
        if (activatedOptions?.length) {
            await asyncArray(activatedOptions).forEach(async activatedOption => {
                await this.activateServiceOption(context, activatedOption, visited);
            });
        }
    }

    /** Get the array of service options that activate a given option */
    private static getActivatingActiveOptions(
        context: Context,
        serviceOption: ServiceOption,
    ): Promise<ServiceOption[]> {
        const activatedByOptions = Object.values(context.application.serviceOptionsByName).filter(option =>
            option.activates?.().some(opt => opt === serviceOption),
        );
        return asyncArray(activatedByOptions)
            .filter(option => context.isServiceOptionEnabled(option))
            .toArray();
    }

    /** Deactivates a single service option */
    private async deactivateServiceOption(context: Context, serviceOption: ServiceOption): Promise<boolean> {
        const activatingActiveOptions = await ServiceOptionManager.getActivatingActiveOptions(context, serviceOption);
        if (serviceOption.isHidden && activatingActiveOptions.length > 0) {
            // We cannot deactivate it because it is hidden and still activated by other active service option.
            // TODO: add a diagnose
            return false;
        }

        const changeIt = await context.isServiceOptionEnabled(serviceOption);
        if (changeIt) {
            await this.setServiceOptionActiveState(context, serviceOption, false);
            context.setServiceOptionsEnabledFlag(serviceOption, false);
        }

        if (serviceOption.isHidden) {
            // try to deactivate its hidden parent
            // will only succeed if all its siblings are also inactive.
            if (serviceOption.activates)
                await asyncArray(serviceOption.activates?.()).forEach(async option => {
                    await this.deactivateServiceOption(context, option);
                });
        } else if (activatingActiveOptions.length > 0) {
            // We are deactivating a visible option
            // Deactivate all the options that depend on it (they should all be visible)
            await asyncArray(activatingActiveOptions).forEach(async activatingOption => {
                await this.deactivateServiceOption(context, activatingOption);
            });
        }
        return changeIt;
    }

    /** Activates an array of service options */
    async activateServiceOptions(context: Context, serviceOptions: ServiceOption[]): Promise<ServiceOption[]> {
        const visited = {} as Dict<boolean>;
        const activeBefore = await context.activeServiceOptions;
        await asyncArray(serviceOptions).forEach(async serviceOption => {
            await this.activateServiceOption(context, serviceOption, visited);
        });
        return _.difference(await context.activeServiceOptions, activeBefore);
    }

    /** Deactivates an array of service options */
    async deactivateServiceOptions(context: Context, serviceOptions: ServiceOption[]): Promise<void> {
        context.clearServiceOptionEnabledFlags();
        // We have to start from the leaves of the dependency tree, to ensure that all requested options can be
        // deleted in a single pass.
        const sortedOptions = this.sortedServiceOptions.filter(option => serviceOptions.includes(option));
        await asyncArray(sortedOptions).forEach(async serviceOption => {
            await this.deactivateServiceOption(context, serviceOption);
        });
        // Sanity check: if we requested a deactivate on all service options, this is guaranteed to work
        // (but not if we try to deactivate only a subset)
        if (serviceOptions.length === this.sortedServiceOptions.length) {
            await asyncArray(serviceOptions).forEach(async serviceOption => {
                if (!(await context.isServiceOptionEnabled(serviceOption)))
                    throw new LogicError(`deactivation of service options failed to deactivate ${serviceOption.name}`);
            });
        }
    }

    /** Activates or deactivates a service option (and its dependencies) */
    async setServiceOptionActive(context: Context, serviceOption: ServiceOption, isActive: boolean): Promise<void> {
        if (isActive) await this.activateServiceOptions(context, [serviceOption]);
        else await this.deactivateServiceOptions(context, [serviceOption]);
    }

    /**
     * Creates missing service options (existing in the code but not in the database) and update existing ones (existing both in code and in db).
     */
    // eslint-disable-next-line class-methods-use-this, @typescript-eslint/no-unused-vars
    createOrUpdateServiceOptions(_context: Context): Promise<void> {
        return Promise.resolve();
    }

    /**
     * Deletes the obsolete service options (existing in the database but not in the code).
     */
    // eslint-disable-next-line class-methods-use-this, @typescript-eslint/no-unused-vars
    deleteObsoleteServiceOptions(_context: Context): Promise<void> {
        return Promise.resolve();
    }

    /**
     * Creates or upgrades the service option states of a tenant (the context's tenant).
     * Called at the end of schema creation and upgrade.
     */
    // eslint-disable-next-line class-methods-use-this, @typescript-eslint/no-unused-vars
    createOrUpgradeServiceOptionStates(_context: Context, _serviceOptionActiveFlags?: Dict<boolean>): Promise<void> {
        return Promise.resolve();
    }
}
