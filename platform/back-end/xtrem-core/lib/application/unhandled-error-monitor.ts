import { inspect } from 'util';
import { loggers } from '../runtime/loggers';

const logger = loggers.application;

export class UnhandledErrorMonitor {
    private _started = false;

    private _threshold = 10;

    private readonly _interval = 3600;

    private _unhandledRejection = 0;

    private _uncaughtException = 0;

    unhandledRejection(reason: any, p: Promise<any>): void {
        this._unhandledRejection += 1;
        logger.error(`Unhandled Rejection: ${reason} at Promise ${inspect(p)}`);
    }

    uncaughtException(err: Error): void {
        this._uncaughtException += 1;
        logger.error(`Uncaught Exception thrown: ${err.stack}`);
        if (this._uncaughtException > this._threshold) {
            logger.error(
                `Uncaught Exception reatched threshold of ${this._threshold} in ${this._interval} seconds, exiting...`,
            );
            process.exit(1);
        }
    }

    start(seconds: number | undefined, threshold: number | undefined): void {
        if (this._started) return;
        this._started = true;
        this._threshold = threshold || this._threshold;
        // reset counters
        global.setInterval(
            () => {
                if (this._uncaughtException !== 0 || this._unhandledRejection !== 0) {
                    logger.info(
                        `resetting uncaught exception counters: uncaughtException=${this._uncaughtException}, unhandledRejection=${this._unhandledRejection}`,
                    );
                }
                this._uncaughtException = 0;
                this._unhandledRejection = 0;
            },
            (seconds || this._interval) * 1000,
        );
    }
}

export const unhandledErrorMonitor = new UnhandledErrorMonitor();

process
    .on('unhandledRejection', (reason, p) => unhandledErrorMonitor.unhandledRejection(reason, p))
    .on('uncaughtException', err => unhandledErrorMonitor.uncaughtException(err));
