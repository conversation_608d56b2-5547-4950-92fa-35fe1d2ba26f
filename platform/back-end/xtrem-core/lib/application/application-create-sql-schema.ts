import { asyncArray } from '@sage/xtrem-async-helper';
import { ReferenceProperty } from '../properties';
import { Context, NodeFactory } from '../runtime';
import { loggers } from '../runtime/loggers';
import { ForeignKey } from '../sql/schema';
import { ReadTableSqlContext } from '../sql/sql-context/read-table-sql-context';

export abstract class ApplicationCreateSqlSchema {
    /** @internal */
    static async verifyAndCreateTables(context: Context): Promise<void> {
        // Actually, only foreign-keys-test needs the table verification, to detect circular dependencies
        // application.getAllSortedFactories detects it too, but with a more obscure error message
        await asyncArray(context.application.getAllFactories()).forEach(factory => factory.verifyTable(context));

        await this.createTables(context, []);
    }

    static async createTables(context: Context, factoriesToAddForeignKeys: NodeFactory[]): Promise<void> {
        const factoriesToCreate = await asyncArray(context.application.getSqlPackageFactories())
            .filter(async factory => !(await factory.table.tableExists(context)))
            .toArray();

        // Create tables without constraints
        await asyncArray(factoriesToCreate).forEach(async factory => {
            loggers.application.info(`${factory.name}: Creating new table: ${factory.table.name}`);
            await factory.table.createTable(context, { skipForeignKeys: true, skipDrop: true });
        });

        // Add constraints
        const foreignKeysMap = await this.addForeignKeys(context, factoriesToCreate.concat(factoriesToAddForeignKeys));

        foreignKeysMap.forEach((foreignKeys: ForeignKey[], factory: NodeFactory) => {
            factory.table.foreignKeys = foreignKeys;
        });
    }

    static async addForeignKeys(
        context: Context,
        sortedFactoryList: NodeFactory[],
    ): Promise<Map<NodeFactory, ForeignKey[]>> {
        const profiler = loggers.application.info(`Creating foreign keys (${sortedFactoryList.length} tables)`);

        const foreignKeys = new Map<NodeFactory, ForeignKey[]>();

        if (sortedFactoryList.length === 0) return foreignKeys;
        let fksCount = 0;

        // Optim: read the table schema only once for all the factories
        const tableDefsForFks = await new ReadTableSqlContext(context.application).readTableDefinitions(
            sortedFactoryList.map(factory => factory.table.name),
            {
                skipColumns: true,
                skipIndexes: true,
                skipSecurity: true,
                skipSequences: true,
            },
        );

        await asyncArray(sortedFactoryList).forEach(async factory => {
            const factoryForeignKeys: ForeignKey[] = factory.table.foreignKeys || [];

            const shouldAddForeignKey = (property: ReferenceProperty): boolean => {
                /*
                if (referencedFactories) {
                    // We want to add only foreign keys referencing a factory in referencedFactories
                    return referencedFactories.includes(
                        factory.application.getFactoryByConstructor(property.node!()).name,
                    );
                }
                */
                // We want to add all non-existing foreign keys
                return !factory.table.foreignKeys?.find(fk => fk.columnNames.includes(property.requiredColumnName));
            };

            factory.properties
                .filter(property => property.isStored && property.isReferenceProperty() && !property.isInherited)
                .forEach((property: ReferenceProperty) => {
                    if (shouldAddForeignKey(property)) {
                        const referenceFactory = property.targetFactory;
                        const foreignKey = factory.createForeignKey(referenceFactory, property);
                        if (foreignKey) factoryForeignKeys.push(foreignKey);
                    }
                });

            if (factoryForeignKeys.length > 0) {
                // Optim: read the table schema only once per factory
                const tableDefForFks = tableDefsForFks[factory.table.name];
                await asyncArray(factoryForeignKeys).forEach(async foreignKey => {
                    if (!(await factory.table.getMatchingForeignKeyDefinition(context, foreignKey, tableDefForFks))) {
                        loggers.application.verbose(
                            () =>
                                `Adding foreign key constraints to table: ${factory.table.name} (${foreignKey.targetTable})(${foreignKey.name})`,
                        );
                        fksCount += 1;
                        await factory.table.addForeignKey(context, foreignKey, tableDefForFks);
                    }
                });
                foreignKeys.set(factory, factoryForeignKeys);
            }
        });
        profiler?.success(`Created ${fksCount} foreign keys for ${sortedFactoryList.length} factories`);
        return foreignKeys;
    }
}
