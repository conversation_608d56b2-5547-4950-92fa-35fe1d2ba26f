import { LogicError } from '@sage/xtrem-shared';
import { Context } from '../runtime/context';
import { Package } from './package';
import { CreateSchemaOptions, PackageManager } from './package-manager';

export class PackageManagerStub extends PackageManager {
    // eslint-disable-next-line @typescript-eslint/explicit-function-return-type, class-methods-use-this, @typescript-eslint/no-unused-vars
    override getCurrentVersion(_pack: Package, _context?: Context): Promise<string> {
        throw new LogicError('packageManager.getCurrentVersion: missing override');
    }

    // eslint-disable-next-line @typescript-eslint/explicit-function-return-type, class-methods-use-this, @typescript-eslint/no-unused-vars
    override validatePackageVersions(_context?: Context): Promise<void> {
        // C<PERSON><PERSON> calls this one for X3 so we don't throw here
        return Promise.resolve();
    }

    // eslint-disable-next-line class-methods-use-this, @typescript-eslint/no-unused-vars
    override createSqlSchemaAndTables(_options: CreateSchemaOptions = {}): Promise<boolean> {
        throw new LogicError('packageManager.createSqlSchemaAndTables: missing override');
    }

    // eslint-disable-next-line class-methods-use-this, @typescript-eslint/no-unused-vars
    override ensureSchemaExists(_options: CreateSchemaOptions = {}): Promise<void> {
        throw new LogicError('packageManager.ensureSchemaExists: missing override');
    }
}
