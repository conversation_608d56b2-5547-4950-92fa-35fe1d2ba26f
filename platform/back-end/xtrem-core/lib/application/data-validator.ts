import { MessageSeverity } from '../enums';
import { Context } from '../runtime';
import { Node } from '../ts-api';

/**
 * Represents a single line in the validation result, containing a message and optional extra information.
 */
export interface InstanceDataValidationResultLine {
    /**
     * The message of the error/warning/info.
     */
    message: string;
    /**
     * The severity of the message.
     */
    severity: MessageSeverity;
    /**
     * The validation path that raised the error/warning/info.
     */
    path: string;
    /**
     * Extra information about the error/warning/info.
     */
    extraInfo?: any;
}

/**
 * The result of the invocation of a data validator.
 * One result per instance.
 */
export type InstanceDataValidationResult = InstanceDataValidationResultLine[];

/**
 * A data validator validates an instance and return errors (if any).
 */
export type DataValidator<T extends Node = Node> = (
    context: Context,
    instance: T,
) => Promise<InstanceDataValidationResult>;
