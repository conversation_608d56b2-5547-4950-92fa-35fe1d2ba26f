import type { Context } from '../runtime/context';

export interface ClientUserSettings {
    _id: string | number;
    title: Promise<string>;
    elementId: Promise<string>;
    description?: Promise<string>;
    content?: Promise<string>;
    isSelected?: Promise<boolean>;
}

export interface ClientSettings {
    title: string;
    description: string;
    content: string;
}

export interface ClientSettingsManager {
    getActiveClientSettingsForArtifact(context: Context, artifactId: string): Promise<ClientUserSettings[]>;
    getVariantsForElement(context: Context, artifactId: string, elementId: string): Promise<ClientUserSettings[]>;
    createClientSetting(
        context: Context,
        artifactId: string,
        elementId: string,
        content: string,
        title: string,
        description?: string,
    ): Promise<ClientUserSettings>;
    updateClientSetting(
        context: Context,
        _id: string,
        content: string,
        title: string,
        description?: string,
    ): Promise<ClientUserSettings>;
    selectClientSetting(
        context: Context,
        artifactId: string,
        elementId: string,
        _id: string,
    ): Promise<ClientUserSettings>;
    resetClientSettings(context: Context): Promise<boolean>;
    deleteClientSetting(context: Context, _id: string): Promise<boolean>;
    getCustomizationListPage(context: Context): string;
    getCustomizationEditPage(context: Context): string;
    unselectedClientSetting(context: Context, artifactId: string, elementId: string): Promise<void>;
}

export class ClientSettingsManagerStub implements ClientSettingsManager {
    // eslint-disable-next-line class-methods-use-this
    unselectedClientSetting(): Promise<void> {
        throw new Error('Method not implemented.');
    }

    // eslint-disable-next-line class-methods-use-this
    getCustomizationListPage(): string {
        throw new Error('Method not implemented.');
    }

    // eslint-disable-next-line class-methods-use-this
    getCustomizationEditPage(): string {
        throw new Error('Method not implemented.');
    }

    // eslint-disable-next-line class-methods-use-this
    resetClientSettings(): Promise<boolean> {
        throw new Error('Method not implemented.');
    }

    // eslint-disable-next-line class-methods-use-this
    createClientSetting(): Promise<ClientUserSettings> {
        throw new Error('Method not implemented.');
    }

    // eslint-disable-next-line class-methods-use-this
    getActiveClientSettingsForArtifact(): Promise<ClientUserSettings[]> {
        throw new Error('Method not implemented.');
    }

    // eslint-disable-next-line class-methods-use-this
    getVariantsForElement(): Promise<ClientUserSettings[]> {
        throw new Error('Method not implemented.');
    }

    // eslint-disable-next-line class-methods-use-this
    updateClientSetting(): Promise<ClientUserSettings> {
        throw new Error('Method not implemented.');
    }

    // eslint-disable-next-line class-methods-use-this
    selectClientSetting(): Promise<ClientUserSettings> {
        throw new Error('Method not implemented.');
    }

    // eslint-disable-next-line class-methods-use-this
    deleteClientSetting(): Promise<boolean> {
        throw new Error('Method not implemented.');
    }
}
