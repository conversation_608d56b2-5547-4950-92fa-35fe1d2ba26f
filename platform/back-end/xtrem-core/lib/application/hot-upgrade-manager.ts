import { Application } from './application';

/**
 * Should metrics files be generated for the upgrade ?
 * local:only on local, s3: local + S3
 */
export type UpgradeMetricsType = 'local' | 's3';

/**
 * Manager for hot-upgrades
 */
export interface HotUpgradeManager {
    /**
     * Executes the hot upgrade of an application.
     * Can only be used on a released application
     * @metrics should metrics be generated when replaying SQL files ?
     */
    executeHotUpgrade(application: Application, metrics?: UpgradeMetricsType): Promise<void>;
}

/**
 * A mock for HoyUpgradeManager: should only be used for unit-tests
 */
export class HotUpgradeManagerStub implements HotUpgradeManager {
    /**
     * Executes the hot upgrade of an application.
     * Can only be used on a released application
     */
    // eslint-disable-next-line @typescript-eslint/no-unused-vars, class-methods-use-this
    executeHotUpgrade(_application: Application, _metrics?: UpgradeMetricsType): Promise<void> {
        throw new Error('Hot-upgrade manager is not registered.');
    }
}
