import { ConfigManager } from '@sage/xtrem-config';
import { Dict } from '@sage/xtrem-shared';
import * as fsp from 'path';
import { setup as setupSecurity } from '../security';
import { Test } from '../test';
import { Application, ApplicationStartServicesOptions, ApplicationType } from './application';

export abstract class ApplicationManager {
    private static applicationCache: Dict<Application> = {};

    static getBuildDir(dir: string): string {
        // eslint-disable-next-line global-require, import/no-dynamic-require
        const jsonFileContent = require(fsp.resolve(dir, 'package.json'));
        let buildDir = jsonFileContent.main ? jsonFileContent.main : 'build/index.js';
        // If no main attribute is specified for the xtrem production image, the buildDir will be /xtrem/app/build/index.js,
        // which does not exist. We need to direct the build to the node_modules destination of the package dependency
        if (!jsonFileContent.main && !jsonFileContent.xtrem && ConfigManager.current.deploymentMode === 'production') {
            const dependencyArray = Object.keys(jsonFileContent.dependencies).filter(dep => dep.startsWith('@sage/'));
            if (dependencyArray.length > 1)
                throw new Error('Production environment should only have one @sage package dependency');
            const dependency = dependencyArray[0];
            buildDir = fsp.resolve(dir, 'node_modules', dependency, 'build/index.js');
        }
        return fsp.dirname(fsp.resolve(dir, buildDir));
    }

    /**
     * Returns the default schema name that will be used for applications
     * Note: this schema name can be overriden by using the XTREM_SCHEMA_NAME environment variable
     */
    static getDefaultServiceSchemaName(): string {
        const config = ConfigManager.current;
        switch (config.deploymentMode) {
            case 'development':
            case 'production': {
                // app name has already been validated in the config
                return config.app ?? 'xtrem';
            }
            default:
                throw new Error(`invalid deployment mode: ${config.deploymentMode}`);
        }
    }

    private static getSchemaName(buildDir: string, applicationType?: ApplicationType): string {
        const envSchemaName = process.env.XTREM_SCHEMA_NAME;
        if (envSchemaName) return envSchemaName;
        return applicationType === 'test' ? Test.getTestSchemaName(buildDir) : this.getDefaultServiceSchemaName();
    }

    /**
     */
    static async getApplication(
        dir: string,
        options?: { applicationType?: ApplicationType; startOptions?: ApplicationStartServicesOptions },
    ): Promise<Application> {
        // worker id of the process
        const workerId = process.env.XTREM_WORKER_ID;
        const applicationCacheKey = workerId ? `${workerId}~${dir}` : dir;
        if (this.applicationCache[applicationCacheKey]) return this.applicationCache[applicationCacheKey];
        await setupSecurity();
        const buildDir = this.getBuildDir(dir);
        const application = await Application.create({
            buildDir,
            ...options,
            schemaName: this.getSchemaName(buildDir, options?.applicationType),
        });
        this.applicationCache[applicationCacheKey] = application;
        return application;
    }
}
