/* eslint-disable class-methods-use-this */
import { AsyncResponse } from '@sage/xtrem-shared';
import { Context } from '../runtime/context';

/**
 * Management of CSV's checksum.
 * Every time a CSV file is loaded, its checksum is stored (sys_csv_checksum table). This allow
 * the system to skip the next loading of the same CSV file, if it was not changed.
 */
export interface CsvChecksumManager {
    /**
     * Return the checksum of the last successfully loaded Csv for a specific factory
     */
    getLastAppliedChecksum(context: Context, factoryName: string): AsyncResponse<string | undefined>;

    /**
     * Store the checksum of the last successfully loaded Csv for a specific factory
     */
    setLastAppliedChecksum(context: Context, factoryName: string, checksum: string): AsyncResponse<void>;

    /**
     * Compute the checksum of the content of a CSV file
     */
    computeChecksum(csvContent: string): string;
}

/**
 * A mock for TenantManager: should only be used for unit-tests
 */
export class CsvChecksumManagerStub implements CsvChecksumManager {
    /**
     * Return the checksum of the last successfully loaded Csv for a specific factory
     */
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    getLastAppliedChecksum(_context: Context, _factoryName: string): string | undefined {
        throw new Error('CsvChecksum manager is not registered.');
    }

    /**
     * Store the checksum of the last successfully loaded Csv for a specific factory
     */
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    setLastAppliedChecksum(_context: Context, _factoryName: string, _checksum: string): void {
        throw new Error('CsvChecksum manager is not registered.');
    }

    /**
     * Compute the checksum of the content of a CSV file
     */
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    computeChecksum(_csvContent: string): string {
        throw new Error('CsvChecksum manager is not registered.');
    }
}
