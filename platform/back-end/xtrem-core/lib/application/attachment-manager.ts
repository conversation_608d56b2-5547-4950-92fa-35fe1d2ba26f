import { Datetime, Reference, StaticThis } from '..';
import { Node } from '../../index';

type AttachmentStatus = 'created' | 'uploaded' | 'uploadFailed' | 'verified' | 'rejected'; // Compliant, "type" statements are ignored

export interface AttachmentData {
    _id: string | number;
    filename: string;
    mimeType: string;
    lastModified: Datetime | null;
    contentLength: number;
    status: AttachmentStatus;
}

export interface AttachmentAssociationData {
    _id?: string | number;
    sourceNodeName: string;
    sourceNodeId: string;
    attachment: AttachmentData;
    isProtected: boolean;
    title: string;
    description: string;
}

export interface InternalAttachmentAssociationData {
    _id?: string | number;
    sourceNodeName: Promise<string>;
    sourceNodeId: Promise<string | number>;
    attachment: Reference<Node>;
    isProtected: Promise<boolean>;
    title: Promise<string>;
    description: Promise<string>;
}

/**
 * Management of attachment.
 */
export interface AttachmentManager<NodeT extends Node = Node> {
    getAttachmentNode(): StaticThis<NodeT>;
}
