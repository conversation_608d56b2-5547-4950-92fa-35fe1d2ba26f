import { LogicError } from '@sage/xtrem-shared';
import { Context } from '../runtime/context';
import { Application } from './application';
import { Package } from './package';

export interface CreateSchemaOptions {
    resetSchema?: boolean;
}

/**
 * Base class for service option managers
 */
export abstract class PackageManager {
    constructor(readonly application: Application) {}

    findPackageByName(name: string): Package {
        const pack = this.application.packagesByName[name];
        if (!pack) throw new LogicError(`${name}: package not found`);
        return pack;
    }

    // Overridden (and cached) in xtrem-system
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    getActivePackageNames(_context: Context): Promise<string[]> {
        return Promise.resolve(this.application.getPackages().map(p => p.name));
    }

    abstract getCurrentVersion(_pack: Package, _context?: Context): Promise<string | undefined>;

    abstract validatePackageVersions(_context?: Context): Promise<void>;

    /**
     * Create the SQL schema (if needed) and all the tables
     */
    abstract createSqlSchemaAndTables(options?: CreateSchemaOptions): Promise<boolean>;

    /**
     * Make sure the schema for the application exists (and create it if needed)
     */
    abstract ensureSchemaExists(options?: CreateSchemaOptions): Promise<void>;

    // eslint-disable-next-line @typescript-eslint/no-unused-vars, class-methods-use-this
    createOrUpgradePackageAllocations(_context: Context, _activePackageNames?: string[]): Promise<void> {
        return Promise.resolve();
    }
}
