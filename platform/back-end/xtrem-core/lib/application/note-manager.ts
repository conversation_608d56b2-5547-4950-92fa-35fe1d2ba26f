import { Reference, StaticThis, TextStream } from '..';
import { Node } from '../../index';

export interface NoteData {
    _id: string | number;
    title: string;
    content: TextStream;
}

export interface NoteAssociationData {
    _id?: string | number;
    sourceNodeName: string;
    sourceNodeId: string;
    note: NoteData;
}

export interface InternalNoteAssociationData {
    _id?: string | number;
    sourceNodeName: Promise<string>;
    sourceNodeId: Promise<string | number>;
    note: Reference<Node>;
}

/**
 * Management of note.
 */
export interface NoteManager<NodeT extends Node = Node> {
    getNoteNode(): StaticThis<NodeT>;
}
