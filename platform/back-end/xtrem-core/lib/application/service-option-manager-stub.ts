import { Context } from '../runtime/context';
import { loggers } from '../runtime/loggers';
import { ServiceOption } from './service-option';
import { ServiceOptionManager } from './service-option-manager';

/**
 * Service option manager stub used by xtrem-core unit tests.
 * It manages the service options and their active state in-memory, with a simple array of active service options.
 */
export class ServiceOptionManagerStub extends ServiceOptionManager {
    #activeServiceOptions: ServiceOption[] = [];

    override isServiceOptionEnabled(_context: Context, serviceOption: ServiceOption): boolean {
        return this.#activeServiceOptions.some(activeServiceOption => activeServiceOption === serviceOption);
    }

    override setServiceOptionActiveState(
        _context: Context,
        serviceOption: ServiceOption,
        isActive: boolean,
    ): Promise<void> {
        const index = this.#activeServiceOptions.indexOf(serviceOption);
        if (isActive) {
            if (index < 0) this.#activeServiceOptions.push(serviceOption);
        } else if (index >= 0) {
            this.#activeServiceOptions.splice(this.#activeServiceOptions.indexOf(serviceOption), 1);
        }

        loggers.application.debug(
            () =>
                `fixtures serviceOptionManager update serviceOption:${JSON.stringify(
                    serviceOption,
                )} isActive:${isActive}`,
        );
        return Promise.resolve();
    }

    override getEnabledServiceOptions(): Promise<ServiceOption[]> {
        return Promise.resolve(this.#activeServiceOptions);
    }
}
