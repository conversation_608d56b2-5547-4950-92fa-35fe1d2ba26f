import { camelCase, snakeCase } from 'lodash';
import * as fsp from 'path';
import { Context } from '../runtime';

export type ServiceOptionStatus = 'workInProgress' | 'experimental' | 'released';

export interface ServiceOptionArg {
    readonly __filename: string;
    name?: string;
    code?: string; // code attribute will be used for X3 to store activity code, but can be utilized in other applications when getting service option information
    status: ServiceOptionStatus;
    description?: string;
    isSubscribable?: boolean;
    isHidden: boolean;
    activates?: () => ServiceOption[];
    isActiveByDefault?: true;
    onEnabled?: (context: Context) => Promise<void>;
    onDisabled?: (context: Context) => Promise<void>;
    notifies?: true;
}

export class ServiceOption {
    readonly name: string;

    readonly code?: string;

    readonly status: ServiceOptionStatus;

    readonly description?: string;

    readonly isSubscribable: boolean;

    isHidden: boolean;

    readonly activates?: () => ServiceOption[];

    readonly isActiveByDefault: boolean;

    readonly notifies?: true;

    onEnabled?: (context: Context) => Promise<void>;

    onDisabled?: (context: Context) => Promise<void>;

    #packageName: string;

    constructor(arg: ServiceOptionArg) {
        this.name = arg.name ?? camelCase(fsp.basename(arg.__filename, fsp.extname(arg.__filename)));
        this.code = arg.code;
        this.status = arg.status;
        this.description = arg.description;
        this.isSubscribable = !!arg.isSubscribable;
        this.isHidden = arg.isHidden;
        this.activates = arg.activates;
        this.isActiveByDefault = !!arg.isActiveByDefault;
        this.onEnabled = arg.onEnabled;
        this.onDisabled = arg.onDisabled;
        this.notifies = arg.notifies;
    }

    setPackageName(name: string): void {
        this.#packageName = name;
    }

    get packageName(): string {
        return this.#packageName;
    }

    getLocalizedTitleKey(): string {
        return `${this.packageName}/service_options__${snakeCase(this.name)}__name`;
    }
}
