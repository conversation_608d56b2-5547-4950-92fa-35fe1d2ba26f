import { Dict } from '@sage/xtrem-shared';
import { loggers } from '../runtime/loggers';
import { NodeFactory } from '../runtime/node-factory';
import { Activity, FlattenedPermission } from '../ts-api/activity';
import { Application } from './application';
import { Package } from './package';

const logger = loggers.authorization;

export class ActivityManager {
    private _activities: Dict<Activity> = {};

    #accessControlledNodes: Set<string> = new Set<string>();

    constructor(private application: Application) {}

    /** @internal - create activities and merge activity extensions for the passed package */
    createActivities(pack: Package): void {
        Object.keys(pack.activities).forEach(activityName => {
            // If the activity was loaded in another package, we print and error and ignore the current package activity
            if (this._activities[activityName]) {
                logger.error(
                    `Activity ${activityName} is declared more than once in the application and will be ignored in package ${pack.name}.`,
                );
                return;
            }
            this._activities[activityName] = pack.activities[activityName];
            this._activities[activityName].package = pack.name;
        });

        this.mergeActivityExtensions(pack);
    }

    /**
     * @internal merge all activity extensions for the passed package
     */
    private mergeActivityExtensions(pack: Package): void {
        Object.keys(pack.activitiesExtensions).forEach(activityExtensionName => {
            // current activity extension
            const activityExtension = pack.activitiesExtensions[activityExtensionName];
            // Name of activity being extended
            const activityIndex = Object.values(this._activities).indexOf(activityExtension.extends);

            const activityName = Object.keys(this._activities)[activityIndex];

            // Check is activity has been loaded
            if (this._activities[activityName]) {
                this._activities[activityName].mergeExtension(activityExtension);
            } else {
                throw new Error(
                    `${activityExtension.filename}, activity linked to activity extension is not loaded on application.`,
                );
            }
        });
    }

    /**
     * Get the activities for the application
     */
    getActivities(): Dict<Activity> {
        return this._activities;
    }

    /**
     * Check if the node is access controlled, recursively checking the parent
     * @param factory
     */
    isNodeAccessControlled(factory: NodeFactory): boolean {
        if (this.#accessControlledNodes.has(factory.name)) return true;
        if (factory.isVitalChild) return this.isNodeAccessControlled(factory.vitalParentFactory);
        return false;
    }

    /**
     * Check the if the permission is allowed for the factory passed
     */
    static checkOperation(
        activityName: string,
        operation: string,
        factory: NodeFactory,
        options?: { throws?: boolean },
    ): boolean {
        let valid = false;

        const checkFactoryOperation = (factoryToCheck: NodeFactory, decoratorAttribute: string): boolean => {
            if ((factoryToCheck as any)[decoratorAttribute]) return true;
            return (
                factoryToCheck.isVitalChild &&
                checkFactoryOperation(factoryToCheck.vitalParentFactory, decoratorAttribute)
            );
        };

        switch (operation) {
            case 'lookup':
                valid = true;
                break;
            case 'read':
                valid = checkFactoryOperation(factory, 'canRead');
                break;
            case 'create':
                valid =
                    checkFactoryOperation(factory, 'canCreate') ||
                    factory.mutations.some(mutation => mutation.isPublished && mutation.name === 'create');
                break;
            case 'update':
                valid =
                    checkFactoryOperation(factory, 'canUpdate') ||
                    factory.mutations.some(mutation => mutation.isPublished && mutation.name === 'update');
                break;
            case 'delete':
                valid = checkFactoryOperation(factory, 'canDelete');
                break;
            case 'import':
                valid = checkFactoryOperation(factory, 'canCreate') || checkFactoryOperation(factory, 'canUpdate');
                break;
            default:
                valid =
                    factory.mutations.some(decorator => decorator.name === operation) ||
                    factory.queries.some(decorator => decorator.name === operation);
        }

        if (!valid && options?.throws)
            throw new Error(
                `Activity ${activityName}: invalid operation granted on Node ${factory.name}, ${operation} not allowed or not found.`,
            );

        return valid;
    }

    /**
     * Fill each activity with all its derived and inherited grants
     */
    private fillGrants(): void {
        // Fill the grants that are explicit from the activity first
        Object.values(this._activities).forEach(activity => {
            activity.fillInternalGrants(this.application);
        });

        // Populate access controlled node list from explicit definitions
        Object.values(this._activities).forEach(activity => {
            this.#accessControlledNodes.add(activity.node.name);
            Object.values(activity.internalGrants).forEach(permissionMap => {
                Object.values(permissionMap).forEach(grant => {
                    if (grant.isOperationGrant()) {
                        this.#accessControlledNodes.add(grant.node);
                    }
                });
            });
        });

        // Cache of target factories that we collect while filling grants
        const lookupFactoriesMap = {} as Dict<NodeFactory[]>;
        // Fill the inherited grants until the list of grants are saturated and the loop will break
        // when the no new grants are added
        Object.values(this._activities).forEach(activity => {
            let added = activity.fillInheritedInternalGrants(this.application, lookupFactoriesMap);

            while (added) {
                added = activity.fillInheritedInternalGrants(this.application, lookupFactoriesMap);
            }
        });

        let looping = true;

        while (looping) {
            looping = false;
            // eslint-disable-next-line @typescript-eslint/no-loop-func
            Object.values(this._activities).forEach(activity => {
                const grantKeys = Object.keys(activity.internalGrants);
                grantKeys.forEach(grantKey => {
                    const permissionGrants = Object.values(activity.internalGrants[grantKey]).filter(value =>
                        value.isPermissionGrant(),
                    );
                    permissionGrants.forEach(permissionGrant => {
                        if (permissionGrant.isPermissionGrant()) {
                            const grantActivity = this._activities[permissionGrant.activity];
                            const internalGrants = grantActivity?.internalGrants[permissionGrant.permission] || {};
                            Object.values(internalGrants).forEach(internalGrant => {
                                if (internalGrant.isOperationGrant())
                                    looping = activity.addGrant(grantKey, internalGrant);
                            });
                        }
                    });
                });
            });
        }

        logger.info(`Filled grants of ${Object.keys(this._activities).length} activities`);
    }

    /**
     * Flatten the activity permissions to a structure like
     * SalesOrder { read: [{read:{ SaleOrderLine }, lookup:{ SaleOrderLine }}]}
     */
    private flattenPermissions(): void {
        let count = 0;
        Object.keys(this._activities).forEach(activityName => {
            const activity = this._activities[activityName];
            const flattenedPermissions: Dict<FlattenedPermission> = {};
            Object.entries(activity.internalGrants).forEach(([grantKey, permissionMap]) => {
                const extendedPermission: FlattenedPermission = {};
                Object.values(permissionMap).forEach(grant => {
                    if (grant.isOperationGrant()) {
                        const nodeName = grant.node;
                        const factory = this.application.getFactoryByName(nodeName);
                        const isNodeOperation = ActivityManager.checkOperation(activityName, grant.operation, factory);
                        if (isNodeOperation) {
                            if (!extendedPermission[grant.operation]) extendedPermission[grant.operation] = [];

                            if (!extendedPermission[grant.operation].includes(nodeName)) {
                                extendedPermission[grant.operation].push(nodeName);
                            }
                        }
                    }
                });
                flattenedPermissions[grantKey] = extendedPermission;
                // eslint-disable-next-line no-plusplus
                count++;
            });
            activity.flattenedPermissions = flattenedPermissions;
        });
        logger.info(`Flatten ${count} permissions among ${Object.keys(this._activities).length} activities`);
    }

    #resolved = false;

    /** Resolve activity permissions into a fully resolved flattened permission  list */
    resolvePermissions(): void {
        if (!this.#resolved) {
            this.#resolved = true;
            // Fill the grants on each activity with all the derived and inherited access
            let profiler = logger.verbose(() => `Filling grants of ${Object.keys(this._activities).length} activities`);
            this.fillGrants();
            profiler.success();

            profiler = logger.verbose(() => 'Setting permissions');
            // Flatten the permissions
            this.flattenPermissions();
            profiler.success();
        }
    }
}
