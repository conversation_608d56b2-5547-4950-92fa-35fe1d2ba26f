import { InitialNotification, Notification, NotificationPayload, PromisifyProperties } from '@sage/xtrem-shared';
import { Context } from '../runtime';
import { UiBroadcastMessage } from './ui-broadcaster';

export interface NotificationManager {
    getUserNotifications(context: Context): Promise<Array<PromisifyProperties<Notification>>>;
    markRead(context: Context, _id: string): Promise<boolean>;
    markUnread(context: Context, _id: string): Promise<boolean>;
    markAllRead(context: Context): Promise<boolean>;
    delete(context: Context, _id: string): Promise<boolean>;
    dispatchUserNotification(context: Context, notification: InitialNotification): Promise<void>;
    dispatchAsyncMutationNotification(context: Context, payload: NotificationPayload): Promise<void>;
    broadcast(message: UiBroadcastMessage): Promise<void>;
}

export class NotificationManagerStub implements NotificationManager {
    // eslint-disable-next-line class-methods-use-this
    getUserNotifications = (): Promise<Array<PromisifyProperties<Notification>>> => {
        throw new Error('Method not implemented.');
    };

    // eslint-disable-next-line class-methods-use-this
    markRead = (): Promise<boolean> => {
        throw new Error('Method not implemented.');
    };

    // eslint-disable-next-line class-methods-use-this
    markUnread = (): Promise<boolean> => {
        throw new Error('Method not implemented.');
    };

    // eslint-disable-next-line class-methods-use-this
    markAllRead = (): Promise<boolean> => {
        throw new Error('Method not implemented.');
    };

    // eslint-disable-next-line class-methods-use-this
    delete = (): Promise<boolean> => {
        throw new Error('Method not implemented.');
    };

    // eslint-disable-next-line class-methods-use-this
    dispatchUserNotification = (): Promise<void> => {
        throw new Error('Method not implemented.');
    };

    // eslint-disable-next-line class-methods-use-this
    dispatchAsyncMutationNotification = (): Promise<void> => {
        throw new Error('Method not implemented.');
    };

    // eslint-disable-next-line class-methods-use-this
    broadcast = (): Promise<void> => {
        throw new Error('Method not implemented.');
    };
}
