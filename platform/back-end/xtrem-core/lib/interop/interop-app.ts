import { ConfigManager } from '@sage/xtrem-config';
import { AuthConfig, BaseError, Dict, createDictionary } from '@sage/xtrem-shared';
import { json } from 'body-parser';
import * as express from 'express';
import { Express, NextFunction, Request, Response } from 'express';
import * as _ from 'lodash';
import { Application } from '../application';
import { graphQlApp } from '../graphql';
import { getRequestId, requestLoggerMiddleware } from '../http';
import { CoreHooks } from '../runtime/core-hooks';
import { getPeerCertificate } from '../security';
import { InteropGraphqlClient, logger } from './interop-graphql-client';

export interface InteropAppInfo {
    name: string;
    title: string;
    isConnector: boolean;
    interopPackage: string;
    isActive: boolean;
}

export interface InteropAuthConfig extends AuthConfig {
    appName: string;
    scope: string;
    sourceUserEmail: string;
    iat: number;
    exp: number;
}

interface ComputedResponseRouteDelay {
    minDelayMillis: number;
    delayWindowSizeMillis: number;
}

interface RequestWithClientAuth extends Request {
    client?: { authorized: boolean };
}

const errorMiddleware = (status: number, message: string) => (_req: Request, res: Response) => {
    res.status(status).send(message);
};

const interopAuthMiddleware = async (req: RequestWithClientAuth, res: Response, next: NextFunction): Promise<void> => {
    const cert = getPeerCertificate(req);
    // if the connection is not secure, we don't have a certificate
    if (cert) {
        if (!req.client?.authorized) {
            logger.error('Invalid client certificate: Not authorized.');
            res.status(403).send('Invalid client certificate.');
            return;
        }
        if (!cert.subjectaltname) {
            logger.error('Invalid client certificate: No Subject Alternative Name.');
            res.status(403).send('Invalid client certificate.');
            return;
        }
        // Extract the DNS names from the certificate, the DNS names are in the form of:
        // 'DNS:<app-name>-xtrem-service-srv.<cluster>.local' and 'DNS:<app-name>-xtrem-service-internal.<cluster>.local'
        // We extract the app names and check if they are valid in the config
        const altNames = cert.subjectaltname.split(', ');
        const dnsNames = altNames.filter(name => name.startsWith('DNS:')).map(name => name.replace('DNS:', ''));
        const appNames = _.uniq(dnsNames.map(name => /^([a-z0-9-]+)-xtrem-service-/.exec(name)?.[1])).map(name =>
            name?.replace(/-/g, '_'),
        );
        const { apps } = ConfigManager.current;
        if (!apps || !appNames.some(app => app && apps[app] != null)) {
            logger.error(
                `Invalid client certificate: No matching app among the DNS names. Expecting one of [${Object.keys(apps ?? {})}] got [${appNames.join(', ')}]`,
            );
            res.status(403).send('Invalid client certificate.');
            return;
        }
    }
    const auth = req.headers.authorization;
    if (!auth) {
        res.status(401).send('no Authorization header');
        return;
    }
    const parsedAuth = /^Bearer\s+(.*)/.exec(auth);
    const bearer = parsedAuth?.[1];
    if (!bearer) {
        res.status(401).send('no Bearer token');
        return;
    }
    const token = await InteropGraphqlClient.parseBearerToken(bearer);
    const login = InteropGraphqlClient.getScopeUserEmail(token.scope);
    const { tenantId } = token;
    res.locals.auth = { ...token, login } as InteropAuthConfig;
    res.locals.config = { tenantId };

    next();
};

/**
 * Creates an express app that serves the interop API.
 * @param application The application to serve.
 * @returns The express app.
 */
export function interopApp(application: Application): Express {
    const graphqlTimeLimitInSeconds = ConfigManager.current.interop?.graphqlTimeLimitInSeconds || 120;

    routesConfig.refresh();
    ConfigManager.emitter.addListener('loaded', () => routesConfig.refresh());
    const delayMiddleware = routesConfig.createDelayHandler();

    const app = express();
    app.use(requestLoggerMiddleware(logger));
    app.use(delayMiddleware);
    app.get('/ping', (_req, res) => {
        res.json({});
    });
    app.use(interopAuthMiddleware);
    // we do not set a limit on the body size, as the interop API is internal only
    app.use(json());
    app.use('/proxy', CoreHooks.interopManager.interopProxyMiddleware(application));
    app.use('/api', graphQlApp(application, { graphqlTimeLimitInSeconds }));

    app.use(errorMiddleware(404, 'not found'));
    // The error handler must be the last middleware
    app.use((err: any, _req: Request, res: Response, next: NextFunction) => {
        logger.error(err.stack);

        if (err instanceof BaseError) {
            res.status(200).send({ extensions: err.extensions });
        } else {
            next(err);
        }
    });

    return app;
}

const routesConfig = new (class {
    #responseRouteDelayMap: Dict<ComputedResponseRouteDelay>;

    /**
     * Refreshes the interop routes delay configuration.
     * This method is called when the configuration is loaded or reloaded.
     */
    refresh(): void {
        logger.info('Refreshing interop routes delay configuration');
        const appRoutesConfig = ConfigManager.current.app
            ? (ConfigManager.current.apps?.[ConfigManager.current.app]?.interopRoutes ?? {})
            : {};
        this.#responseRouteDelayMap = Object.keys(appRoutesConfig).reduce((acc, routeName) => {
            const responseConfig = appRoutesConfig[routeName].response;
            // if the route has a response configuration with delay settings, add it to the map
            if (responseConfig && (responseConfig.minDelayMillis || responseConfig.maxDelayMillis)) {
                const minDelayMillis = responseConfig.minDelayMillis ?? 0;
                const maxDelayMillis = responseConfig.maxDelayMillis ?? minDelayMillis;
                acc[routeName] = { minDelayMillis, delayWindowSizeMillis: maxDelayMillis - minDelayMillis + 1 };
            }
            return acc;
        }, createDictionary<ComputedResponseRouteDelay>());
    }

    /**
     * Creates a handler for a route that delays the response by a random amount of time.
     * The delay is configured in the route configuration of each app.
     * The handler is compatible with the proxy middleware.
     * @param routeName The name of the route.
     * @returns The handler.
     * @see https://github.com/chimurai/http-proxy-middleware/blob/master/recipes/delay.md
     */
    createDelayHandler(): express.RequestHandler {
        return (req, res, next) => {
            const id = getRequestId(req);
            const routeName = req.originalUrl.split('/', 2)[1];
            const responseConfig = this.#responseRouteDelayMap[routeName];
            if (!responseConfig) {
                logger.verbose(() => `HTTP nodelay  ${id} ${req.method} ${req.originalUrl} `);
                next();
                return;
            }
            const { minDelayMillis, delayWindowSizeMillis } = responseConfig;
            const delay = Math.floor(Math.random() * delayWindowSizeMillis + minDelayMillis);
            // delay the response by the random delay
            logger.info(() => `HTTP delay    ${id} ${req.method} ${req.originalUrl} (delay response by ${delay} ms)`);
            const endOriginal = res.end;
            res.end = ((...args: any[]) => {
                setTimeout(() => endOriginal.apply(res, args), delay);
            }) as typeof endOriginal;

            next();
        };
    }
})();
