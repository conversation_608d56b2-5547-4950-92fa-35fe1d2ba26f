/**
 * This class manages the health monitoring of the apps.
 *
 * Periodically ping the apps to get their status.
 */

import { ConfigManager } from '@sage/xtrem-config';
import { AppsConfig, Dict } from '@sage/xtrem-shared';
import { AppHealthInfo, InteropGraphqlClient, logger } from './interop-graphql-client';

type AppHealthInfos = Dict<AppHealthInfo>;

/**
 * @internal
 */
export abstract class InteropAppHealthMonitor {
    private static _isStarted = false;

    // The list of alive apps
    private static _apps: AppHealthInfos = {};

    /**
     * The health check period (in millis)
     */
    static get appsHealthCheckMillis(): number {
        return (ConfigManager.current.interop?.appsHealthCheckSeconds ?? 60) * 1000;
    }

    /**
     * Returns the id of all the alive apps (current app is excluded)
     * @returns the list of alive apps
     */
    static get aliveApps(): string[] {
        return Object.keys(InteropAppHealthMonitor._apps).filter(
            appName => InteropAppHealthMonitor._apps[appName].isAlive,
        );
    }

    /**
     * Returns whether the app is alive or not
     * @param appName the name of the app
     * @returns true if the app is alive
     */
    static async isAppAlive(appName: string, options: { force?: boolean } = {}): Promise<boolean> {
        if (!InteropAppHealthMonitor._apps[appName] || options.force) {
            await InteropAppHealthMonitor.setAppHealth(appName);
        }

        return InteropAppHealthMonitor._apps[appName]?.isAlive ?? false;
    }

    /**
     * Indicates whether the health check monitor is started
     * @returns true if the health check monitor is started
     */
    static get isStarted(): boolean {
        return InteropAppHealthMonitor._isStarted;
    }

    /**
     * Activate the apps health check monitor.
     */
    static start(): void {
        if (ConfigManager.current.storage?.managedExternal) {
            throw new Error('Apps health check monitor not supported in the context of a managed external storage');
        }
        const { app, apps } = ConfigManager.current;
        if (!app) {
            return;
        }
        if (!apps || Object.keys(apps).length === 0) {
            logger.warn('Cannot start apps health check: No apps defined in the configuration');
            return;
        }

        if (InteropAppHealthMonitor._isStarted) return;
        InteropAppHealthMonitor._isStarted = true;
        setInterval(
            () => InteropAppHealthMonitor._checkAppsHealth(apps),
            InteropAppHealthMonitor.appsHealthCheckMillis,
        );

        logger.info('Apps health check monitor started');
    }

    private static _checkAppsHealth(apps: AppsConfig): void {
        Promise.allSettled(Object.keys(apps).map(appName => InteropAppHealthMonitor.setAppHealth(appName))).catch(
            err => {
                logger.error(`Error while checking health of apps: ${err}`);
            },
        );
    }

    private static setAppHealth(appName: string): Promise<void> {
        return InteropGraphqlClient.getAppHealth(appName)
            .then(health => {
                InteropAppHealthMonitor._apps[appName] = health;
            })
            .catch(err => {
                logger.error(`Error while checking health of app ${appName}: ${err}`);
            })
            .finally(() => {
                const health = InteropAppHealthMonitor._apps[appName];
                if (health?.status !== 200) {
                    logger.error(
                        () =>
                            `Check health of ${appName} app: status ${health?.status} (${health?.isAlive ? 'alive' : 'unavailable'})`,
                    );
                } else {
                    logger.debug(
                        () =>
                            `Check health of ${appName} app: status ${health?.status} (${health?.isAlive ? 'alive' : 'unavailable'})`,
                    );
                }
            });
    }
}
