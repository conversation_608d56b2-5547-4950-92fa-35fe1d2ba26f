import { LogicError, titleCase } from '@sage/xtrem-shared';
import { camelCase } from 'lodash';
import { Application, Package } from '../application';

/** Options passed to the NotificationTopic constructor. */
export interface NotificationTopicConstructOptions {
    /**
     * The app name. It is only set in inter-app notification topics.
     * It identifies the app which produces the notifications.
     *
     * If the topic is internal to an application (intra-app), app must be empty.
     */

    appName: string;

    /**
     * The notification topic:
     * - /appName/nodeName/eventName if inter-app
     * - nodeName/eventName if intra-app
     */
    topic: string;

    // TODO: add payload descriptor
}

/**
 * Class for notification topics
 *
 * A NotificationTopic describes a topic which may be listened to by this app or by other apps deployed in the cluster.
 */
export class NotificationTopic {
    #name: string | undefined;

    #definingPackage: Package | undefined;

    #verified = false;

    /**
     * const myNotificationTopic = new NotificationTopic({ app: myApp, topic: '/myApp/myNode/myEvent' })
     */
    constructor(private readonly options: NotificationTopicConstructOptions) {}

    /**
     * The app which produces the notification.
     *
     * This property must be:
     * - non-null for inter-app notifications
     * - null for intra-app notifications.
     */
    get appName(): string {
        return this.options.appName;
    }

    /** The name of the notification topic */
    get name(): string {
        if (!this.#name) throw this.logicError('notification topic not registered');
        return this.#name;
    }

    /** The topic */
    get topic(): string {
        return this.options.topic;
    }

    /** Friendly title for the topic */
    get title(): string {
        // TODO: localize
        return this.topic.split('/').map(titleCase).join(' / ');
    }

    /** The package which defines the notification topic */
    get definingPackage(): Package {
        if (!this.#definingPackage) throw this.logicError('notification topic not registered');
        return this.#definingPackage;
    }

    /** @internal sets the name and definingPackage when the topic is registered in the application object */
    completeRegistration(name: string, pack: Package): void {
        if (!name) throw new LogicError('cannot registrer notification topic with empty name');
        if (this.#name || this.#definingPackage)
            throw this.logicError(
                `notification topic registered twice: in ${this.#definingPackage?.name} and in ${pack.name}`,
            );
        this.#name = name;
        this.#definingPackage = pack;
    }

    /** The application object */
    get application(): Application {
        return this.definingPackage.application;
    }

    /** Utility to create a LogicError. Prefixes the message with the topic name */
    logicError(message: string): LogicError {
        return new LogicError(`${this.name}: ${message}`);
    }

    /** @internal Verifies the notification topic. Called when the application object is verified */
    verify(): void {
        if (this.#verified) return;

        // Check that it is registered in its application
        this.application.findNotificationTopic(this.name);

        if (this.name !== camelCase(this.topic))
            throw this.logicError(
                `Mismatch on name of notification topic. Expected ${camelCase(this.topic)}, got ${this.name}`,
            );

        if (this.appName && !this.topic.startsWith('/'))
            throw this.logicError('Notification topic with app must have an absolute path (with a leading /)');
        if (!this.appName && this.topic.startsWith('/'))
            throw this.logicError('Notification topic without app must have a relative path (no leading /)');
        this.#verified = true;
    }

    /** Throws an error if the verify method has not been called. */
    checkVerified(): void {
        if (!this.#verified) throw this.logicError('Notification topic was not verified');
    }
}
