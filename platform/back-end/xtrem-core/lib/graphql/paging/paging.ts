import { asyncArray } from '@sage/xtrem-async-helper';
import { ConfigManager } from '@sage/xtrem-config';
import {
    GraphQLDirective,
    GraphQLFieldConfig,
    GraphQLFieldConfigArgumentMap,
    GraphQLInt,
    GraphQLInterfaceType,
    GraphQLList,
    GraphQLObjectType,
    GraphQLResolveInfo,
    GraphQLString,
} from 'graphql';
import { BaseCollection } from '../../collections';
import { Context, NodeFactory } from '../../runtime';
import {
    Aggregate,
    AggregateGroups,
    AggregateValues,
    AnyOrderBy,
    Node,
    NodeQueryFilter,
    NodeQueryOptions,
    QueryAggregateReturn,
} from '../../ts-api';
import { TypeCache } from '../utils/type-cache';
import { PagingFilter } from './paging-filter';
import { PagingOrderBy } from './paging-order-by';
import { pageInfoType } from './paging-types';

export interface QueryPageInfo {
    endCursor?: string;
    hasNextPage: boolean;
    startCursor?: string;
    hasPreviousPage?: boolean;
    totalCount?: number;
}

/** @internal */
export interface QueryArgs {
    first?: number;
    after?: string;
    last?: number;
    before?: string;
    orderBy?: string;
    filter?: string;
}

/** @internal */
export type PageResolver<T = any> = {
    // The context for resolvers deeper in the graphql query
    context: Context;
    // top level queries are resolved lazily with a promise
    pagePromise?: Promise<{
        edges?: T[];
        pageInfo?: QueryPageInfo;
    }>;
    pagingOptions?: NodeQueryOptions;
} & {
    // collection queries are resolved directly, with property getter
    edges?: T[];
    aggregate?: T[];
    pageInfo?: QueryPageInfo;
};

export interface QueryEdge<T = any> {
    node: T;
    cursor: string;
}

export interface QueryNode<T = any> {
    query: QueryPage<T>;
}

export interface QueryPage<T = any> {
    totalCount?: number;
    edges?: QueryEdge<T>[];
    pageInfo?: QueryPageInfo;
}

export interface QueryAggregateNode<T extends Node> {
    queryAggregate: QueryAggregatePage<T>;
}

export interface QueryAggregatePage<T extends Node> {
    totalCount?: number;
    edges?: QueryAggregateEdge<T>[];
    pageInfo?: QueryPageInfo;
}

export interface QueryAggregateEdge<T extends Node> {
    node: QueryAggregateReturn<T, AggregateGroups<T>, AggregateValues<T>>;
    cursor: string;
}

const defaultPageSize = 20;
const defaultMaxPerPage = 1000;
const defaultStreamMaxPerPage = 100_000;

/**
 * @internal
 *
 * Static class which provides paging utilities shared by all paged GraphQL queries
 */
export class Paging {
    /** The argument types for all paged queries */
    static getPagingArgumentTypes(): GraphQLFieldConfigArgumentMap {
        return {
            first: { type: GraphQLInt },
            after: { type: GraphQLString },
            last: { type: GraphQLInt },
            before: { type: GraphQLString },
            filter: { type: GraphQLString },
            orderBy: { type: GraphQLString },
        };
    }

    /**
     * Builds the output page
     *
     * Adds the `{ edges: [{ node: ..., cursor: ... }], pageInfo: ... }`
     * wrapper around the page data.
     */
    static async buildOutputPage(
        page: { items: Node[]; totalCount?: number },
        pagingOptions: NodeQueryOptions,
        reverseIfLast = false,
    ): Promise<QueryPage> {
        // get full list of edges with extra node
        const allEdges: QueryEdge[] = await asyncArray(page.items)
            .map(async item => ({
                node: item,
                cursor: await item.$.state.getCursorValue(pagingOptions.orderBy || {}),
            }))
            .toArray();

        if (pagingOptions.last) {
            const hasPreviousPage = page.items.length > pagingOptions.last;
            let edges = hasPreviousPage ? allEdges.slice(0, allEdges.length - 1) : allEdges;
            // TODO: this feels strange - needs investigation
            //
            // Reverse the edges/aggregates if paging option last is applied,
            // if not the results will be returned backwards
            // and "has" cursors will be incorrect
            // Data - [1,2,3,4,5] order by -1 query = [5,4,3,2,1]
            // without reverse last:2 will be [4,5] instead of [2,1]
            if (reverseIfLast) edges = edges.slice().reverse();
            return {
                edges,
                pageInfo: {
                    hasNextPage: pagingOptions.before != null, // before was supplied so there must be a next page
                    endCursor: edges.length > 0 ? edges[edges.length - 1].cursor : undefined,
                    hasPreviousPage,
                    startCursor: edges.length > 0 ? edges[0].cursor : undefined,
                    totalCount: page.totalCount,
                },
            };
        }
        const hasNextPage = page.items.length > pagingOptions.first!;
        {
            const edges = hasNextPage ? allEdges.slice(0, allEdges.length - 1) : allEdges;

            return {
                edges,
                pageInfo: {
                    hasNextPage,
                    endCursor: edges.length > 0 ? edges[edges.length - 1].cursor : undefined,
                    hasPreviousPage: pagingOptions.after != null,
                    startCursor: edges.length > 0 ? edges[0].cursor : undefined,
                    totalCount: page.totalCount,
                },
            };
        }
    }

    static async addInternalFilters(
        context: Context,
        factory: NodeFactory,
        filter: NodeQueryFilter<Node, Node> | undefined,
        info: GraphQLResolveInfo,
    ): Promise<NodeQueryFilter<Node, Node> | undefined> {
        const operation = info.path.prev?.key === 'lookups' ? 'lookup' : 'read';
        const constructorFilter = await factory.getConstructorFilter(context, operation);
        const securityFilter = await Context.accessRightsManager.getOperationSecurityFilter(
            context,
            factory,
            operation,
        );

        const filters = [filter, constructorFilter, securityFilter].filter(f => f !== undefined);
        return filters.length <= 1 ? filters.shift() : { _and: filters };
    }

    /** Parses the paging options into a `NodeQueryOptions` object which can then be passed to context.query */
    static async parsePagingOptions(
        context: Context,
        factory: NodeFactory,
        args: QueryArgs,
        info: GraphQLResolveInfo,
        defaultOrderBy?: AnyOrderBy,
        aggregate?: Aggregate,
    ): Promise<NodeQueryOptions> {
        const maxPerPage = ConfigManager.current.graphql?.maxNodesPerPage || defaultMaxPerPage;

        // If aggregate and no explicit orderBy in args, we will use aggregate groups
        const orderByAggregateGroups = !!aggregate?.groups?.length && !args.orderBy;
        const orderBy = orderByAggregateGroups
            ? undefined
            : await PagingOrderBy.parseOrderBy(context, factory, args.orderBy, defaultOrderBy);

        const filter = await this.addInternalFilters(
            context,
            factory,
            await PagingFilter.parseFilter(context, factory, args.filter),
            info,
        );

        if (args.last) {
            if (args.first) throw new Error('first cannot be supplied with last.');
            if (args.after) throw new Error('after cannot be supplied with last.');
            return {
                last: Math.min(args.last, maxPerPage),
                before: args.before,
                orderBy,
                filter,
                aggregate,
            };
        }
        if (args.before) throw new Error('before cannot be supplied without last.');

        // If arg.first is not defined then return defaultPageSize
        // If arg.first is negative then return maxPerPage
        // If arg.first is positive then return the min of arg.first and maxPerPage
        // If query contains @stream directive and first argument is not defined then set first to defaultStreamMaxPerPage
        // defaultPageSize and maxPerPage are defined in the configuration

        const queryContainsStreamDirective = (): boolean =>
            info.schema.getDirectives().some((_directive: GraphQLDirective) => _directive.name === 'stream') &&
            info.fieldNodes.some(fieldNode =>
                fieldNode?.selectionSet?.selections.some(selection =>
                    selection.directives?.some(directive => directive.name.value === 'stream'),
                ),
            );

        const getFirstArg = (firstArg?: number): number => {
            if (firstArg == null) {
                return queryContainsStreamDirective() ? defaultStreamMaxPerPage : Math.min(defaultPageSize, maxPerPage);
            }
            return Math.min(firstArg >= 0 ? firstArg : maxPerPage, maxPerPage);
        };

        return {
            first: getFirstArg(args.first),
            after: args.after,
            orderBy,
            filter,
            aggregate,
        };
    }

    // adds the collection join filter(s) to the paging filter
    static async addCollectionFilter(paging: NodeQueryOptions, collection: BaseCollection): Promise<void> {
        paging.collection = collection;
        paging.filter = paging.filter
            ? { _and: [paging.filter, await collection.joinValues] }
            : await collection.joinValues;
    }

    // Wraps the node type to create the edge type
    private static makeEdgeType(
        typeCache: TypeCache,
        nodeType: GraphQLObjectType | GraphQLInterfaceType,
    ): GraphQLObjectType {
        const name = `${nodeType.toString()}_Edge`;
        return typeCache.internType(name, () => {
            return new GraphQLObjectType({
                name,
                fields: {
                    node: {
                        type: nodeType,
                    },
                    cursor: {
                        type: GraphQLString,
                    },
                },
            });
        });
    }

    /** Resolver for the `edges` element */
    static makeEdgesResolver(
        typeCache: TypeCache,
        nodeType: GraphQLObjectType | GraphQLInterfaceType,
    ): GraphQLFieldConfig<PageResolver, Context, {}> {
        return {
            type: new GraphQLList(Paging.makeEdgeType(typeCache, nodeType)),
            resolve(obj) {
                if (!obj.pagePromise && !obj.edges) throw new Error('internal error: cannot resolve edges');
                return obj.pagePromise ? obj.pagePromise.then(result => result.edges) : obj.edges;
            },
        };
    }

    /** Resolver for the `pageInfo` element */
    static makePageInfoResolver(): GraphQLFieldConfig<PageResolver, Context, {}> {
        return {
            type: pageInfoType,
            resolve(obj) {
                return obj.pagePromise ? obj.pagePromise.then(result => result.pageInfo) : obj.pageInfo;
            },
        };
    }
}
