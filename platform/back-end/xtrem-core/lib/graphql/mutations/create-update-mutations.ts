import { Any<PERSON><PERSON><PERSON> } from '@sage/xtrem-async-helper';
import { AnyValue, BusinessRuleError, Dict, with<PERSON><PERSON><PERSON> } from '@sage/xtrem-shared';
import { GraphQLFieldConfig, GraphQLID } from 'graphql';
import { capitalize, isEmpty, omit, pick } from 'lodash';
import { OperationError } from '../../errors/operation-error';
import { Context, NodeFactory } from '../../runtime';
import { AccessRights } from '../security/access-rights';
import { NodeType } from '../types/node-type';
import { TypeCache } from '../utils/type-cache';

/**
 * @internal
 *
 * Static class which provides the GraphQL schema resolver for the `create` and `update/updateById` mutations:
 *
 * ``` graphql
 *  mutation {
 *      xtremFoo {
 *          Bar {
 *              create(data: ...) { ... }
 *              update(data: ...) { ... }
 *              updateById(data: ...) { ... }
 *          }
 *      }
 *  }
 *  ```
 */
export class CreateUpdateMutations {
    static async executeCreateOrUpdate(
        context: Context,
        factory: NodeFactory,
        op: 'create' | 'update' | 'updateById',
        _id: string | undefined,
        data: Dict<any>,
    ): Promise<AnyValue> {
        const {
            dataAtlevel: topLevelData,
            factoryAtlevel: topLevelFactory,
            keyAtlevel: topLevelKey,
            pathAtlevel: finalPath,
            reversedPathAtlevel: finalReversedPathAtlevel,
        } = await CreateUpdateMutations.transformVitalChildPayloadToParentPayload(context, factory, data, [], []);

        if (finalPath.length > 0) {
            // If the payload contains vital parent data
            return CreateUpdateMutations.createOrUpdateVitalParent(
                context,
                topLevelData,
                topLevelFactory,
                topLevelKey,
                finalPath,
                finalReversedPathAtlevel,
            );
        }

        // If the payload does not contain vital parent data
        if (op === 'create') {
            const n = await NodeType.nodeIn(context, data, factory);
            await n.$.save();
            return n;
        }

        const updateData = await NodeType.nodeValuesIn(context, data, factory);
        const updateKey =
            factory.storage === 'external'
                ? op === 'updateById'
                    ? { _id }
                    : pick(updateData, [
                          ...factory.keyProperties.map(prop => prop.name),
                          // We include _id in the filter key for the cases where _id is passed in the update data
                          // instead of the node key properties.
                          '_id',
                      ])
                : {
                      _id: op === 'updateById' ? _id : updateData._id,
                  };

        const node = await context.read(factory.nodeConstructor, updateKey, {
            forUpdate: true,
        });

        if (updateData._etag != null) {
            const etag = String(await node.$.etag);

            if (updateData._etag !== etag) {
                throw context.businessRuleError({
                    key: '@sage/xtrem-core/record-modified-reload-required',
                    message: 'Another user made changes to this record. Refresh the page to add your changes.',
                });
            }
        }

        await node.$.update(updateData);
        return node;
    }

    /** Resolver for `mutation { xtremFoo { Bar { create/update/updateById(data/id: ...) { ... } } } }` mutations */
    static makeResolver(
        typeCache: TypeCache,
        factory: NodeFactory,
        op: 'create' | 'update' | 'updateById',
    ): GraphQLFieldConfig<unknown, Context, { _id?: string; data: Dict<any> }> {
        return {
            description: `${capitalize(op)} a ${factory.name}`,
            type: NodeType.makeOutputType(typeCache, factory, 'nodeOutput'),
            args: {
                _id: { type: GraphQLID },
                data: { type: NodeType.makeInputType(typeCache, factory, 'nodeInput') },
            },
            resolve(root, args, rootContext) {
                return AccessRights.runSecure(
                    rootContext,
                    op,
                    {
                        factory,
                        isMutation: true,
                        operation: op === 'create' ? op : 'update',
                        args,
                    },
                    () =>
                        rootContext.withChildContext(
                            context =>
                                withRethrow(
                                    () =>
                                        CreateUpdateMutations.executeCreateOrUpdate(
                                            context,
                                            factory,
                                            op,
                                            args._id,
                                            args.data,
                                        ),
                                    OperationError.errorMapper(context, factory, op === 'updateById' ? 'update' : op),
                                ),

                            {
                                isolationLevel: factory.getIsolationLevel(op === 'create' ? 'create' : 'update'),
                            },
                        ),
                );
            },
        };
    }

    /**
     * Checks if the given data has a vital parent payload.
     *
     * @param factory - The NodeFactory instance.
     * @param data - The data to check.
     * @returns A boolean indicating whether the data has a vital parent payload.
     */
    private static hasVitalParentPayload(factory: NodeFactory, data: Dict<any>): boolean {
        return (
            factory.isVitalChild &&
            factory.vitalParentProperty.isVitalParentInput &&
            data[factory.vitalParentProperty.name] != null &&
            typeof data[factory.vitalParentProperty.name] === 'object' &&
            factory.storage === 'sql'
        );
    }

    /**
     * Picks the key properties value from the payload data.
     * If the data contains an _id property, it returns an object with _id property.
     * Otherwise, it picks the key properties from the data object.
     * @param factory - The NodeFactory instance.
     * @param data - The payload data.
     * @returns An object with the picked key properties or an object with _id property.
     */
    private static pickKeyPropertiesValueFromPayload(
        factory: NodeFactory,
        data: Dict<any>,
    ):
        | Pick<AnyRecord, string>
        | {
              _id: any;
          } {
        return data._id
            ? {
                  _id: data._id,
              }
            : pick(data, [
                  ...factory.keyProperties.map(prop => prop.name),
                  // We include _id in the filter key for the cases where _id is passed in the update data
                  // instead of the node key properties.
                  '_id',
              ]);
    }

    /**
     * Recursively processes the payload data and returns the relevant information at each level.
     *
     * @param context - The context object.
     * @param factory - The node factory object.
     * @param data - The payload data.
     * @param path - The current path in the payload.
     * @param reversedPath - The reversed path in the payload.
     * @returns A promise that resolves to an object containing the data, factory, key, path, and reversedPath at each level.
     */
    private static async transformVitalChildPayloadToParentPayload(
        context: Context,
        factory: NodeFactory,
        data: Dict<any>,
        path: string[],
        reversedPath: string[],
    ): Promise<{
        dataAtlevel: AnyRecord;
        factoryAtlevel: NodeFactory;
        keyAtlevel:
            | Pick<AnyRecord, string>
            | {
                  _id: any;
              };
        pathAtlevel: string[];
        reversedPathAtlevel: string[];
    }> {
        if (CreateUpdateMutations.hasVitalParentPayload(factory, data)) {
            const { vitalParentFactory } = factory;

            const vitalChildren = vitalParentFactory.properties.filter(
                prop => prop.isReferenceProperty() && prop.targetFactory.name === factory.name,
            );
            if (vitalChildren.length !== 1) {
                // TODO: change this to a LogicError
                throw new BusinessRuleError(
                    `Vital parent property ${factory.vitalParentProperty.name} must have a unique child property ${factory.name}`,
                );
            }

            const parentData = await NodeType.nodeValuesIn(
                context,
                data[factory.vitalParentProperty.name],
                vitalParentFactory,
            );
            const parentKey = CreateUpdateMutations.pickKeyPropertiesValueFromPayload(vitalParentFactory, parentData);
            const vitalChild = factory.getReverseVitalProperty();
            if (vitalChild) {
                parentData[vitalChild.name] = await NodeType.nodeValuesIn(
                    context,
                    omit(data, factory.vitalParentProperty.name),
                    factory,
                );
                path.unshift(vitalChild.name);
                reversedPath.unshift(factory.vitalParentProperty.name);
                if (vitalParentFactory.isVitalChild)
                    return CreateUpdateMutations.transformVitalChildPayloadToParentPayload(
                        context,
                        vitalParentFactory,
                        parentData,
                        path,
                        reversedPath,
                    );

                return {
                    dataAtlevel: parentData,
                    factoryAtlevel: vitalParentFactory,
                    keyAtlevel: parentKey,
                    pathAtlevel: path,
                    reversedPathAtlevel: reversedPath,
                };
            }
        }
        const keyAtlevel = CreateUpdateMutations.pickKeyPropertiesValueFromPayload(factory, data);
        return {
            dataAtlevel: data,
            factoryAtlevel: factory,
            keyAtlevel,
            pathAtlevel: path,
            reversedPathAtlevel: reversedPath,
        };
    }

    /**
     * Manages the input payload containing vital parent payload.
     *
     * @param context The context object.
     * @param data The data object.
     * @param factory The node factory.
     * @param key The key object.
     * @param path The path array.
     * @param reversedPath The reversed path array.
     * @returns A promise that resolves to the updated value.
     */
    private static async createOrUpdateVitalParent(
        context: Context,
        data: AnyRecord,
        factory: NodeFactory,
        key:
            | Pick<AnyRecord, string>
            | {
                  _id: any;
              },
        path: string[],
        reversedPath: string[],
    ): Promise<AnyValue> {
        let parentNode = isEmpty(key)
            ? undefined
            : await context.tryRead(factory.nodeConstructor, key, {
                  forUpdate: true,
              });
        const isUpdate = !!parentNode;
        try {
            if (!parentNode) {
                // create
                parentNode = await context.create(factory.nodeConstructor, data);
            } else {
                // update
                await parentNode.$.set(data);
            }

            await parentNode.$.save();
            return await parentNode.$.get(path.join('.'));
        } catch (err) {
            CreateUpdateMutations.fixDiagnosesPaths(context, isUpdate, path, reversedPath);
            throw err;
        }
    }

    /**
     * Fixes the diagnoses paths by updating the path property of each diagnose in the context.
     * @param context - The context object containing the diagnoses.
     * @param pathToChild - The path to the child node.
     * @param reversedPathToChild - The reversed path to the child node.
     */
    private static fixDiagnosesPaths(
        context: Context,
        isUpdate: boolean,
        pathToChild: string[],
        reversedPathToChild: string[],
    ): void {
        if (pathToChild.length > 0) {
            context.diagnoses.forEach(diagnose => {
                const currentPath = [...diagnose.path];

                const difference = currentPath.filter(x => !pathToChild.includes(x));
                // find the first difference
                const indexFirstDifference = currentPath.findIndex(x => x === difference[0]);

                diagnose.path =
                    difference.length === currentPath.length && reversedPathToChild.length === 1
                        ? // if the diagnoses path does not contain the child path
                          [...currentPath]
                        : // if not, we remove the common path in the corresponding reversed and add the difference
                          [...reversedPathToChild.slice(indexFirstDifference).reverse(), ...difference];
                diagnose.message = isUpdate
                    ? `[Updating vital parent] ${diagnose.message}`
                    : `[Creating vital parent] ${diagnose.message}`;
            });
        }
    }
}
