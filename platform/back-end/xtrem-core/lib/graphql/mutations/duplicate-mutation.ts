import { Dict, with<PERSON><PERSON><PERSON> } from '@sage/xtrem-shared';
import { GraphQLFieldConfig, GraphQLID } from 'graphql';
import { Context, NodeFactory } from '../../runtime';
import { OperationError } from '../../ts-api';
import { AccessRights } from '../security/access-rights';
import { NodeType } from '../types/node-type';
import { TypeCache } from '../utils/type-cache';

/**
 * @internal
 *
 * Static class which provides the GraphQL schema resolver for the `duplicate` mutation:
 *
 * ``` graphql
 *  mutation {
 *      xtremFoo {
 *          Bar {
 *              duplicate(_id: ..., data: ...) { ... }
 *          }
 *      }
 *  }
 *  ```
 */
export class DuplicateMutation {
    private static async execute(context: Context, factory: NodeFactory, args: Dict<any>): Promise<any> {
        const node = await context.read(factory.nodeConstructor, { _id: args._id });

        const data = args.data
            ? await NodeType.nodeValuesIn(context, args.data, factory, {
                  omitNulls: false,
              })
            : args.data;
        const duplicate = await node.$.duplicate(data);

        await duplicate.$.save();
        return duplicate;
    }

    /** Resolver for `query { xtremFoo { Bar { duplicate(_id, data) { ... } } } }` */
    static makeResolver(
        typeCache: TypeCache,
        factory: NodeFactory,
    ): GraphQLFieldConfig<unknown, Context, { _id: string; data?: Dict<any> }> {
        return {
            description: `Duplicate a ${factory.name}`,
            type: NodeType.makeOutputType(typeCache, factory, 'nodeOutput'),
            args: {
                _id: { type: GraphQLID },
                data: { type: NodeType.makeInputType(typeCache, factory, 'nodeInput') },
            },
            resolve(_root, args, rootContext) {
                return AccessRights.runSecure(
                    rootContext,
                    'create',
                    {
                        factory,
                        isMutation: true,
                        operation: 'create',
                        args,
                    },
                    () =>
                        rootContext.withChildContext(
                            context =>
                                withRethrow(
                                    () => DuplicateMutation.execute(context, factory, args),
                                    OperationError.errorMapper(context, factory, 'duplicate'),
                                ),
                            {
                                isolationLevel: factory.getIsolationLevel('create'),
                            },
                        ),
                );
            },
        };
    }
}
