import { Dict, with<PERSON><PERSON><PERSON> } from '@sage/xtrem-shared';
import { GraphQLBoolean, GraphQLFieldConfig, GraphQLString } from 'graphql';
import { Context, NodeFactory } from '../../runtime';
import { Node, OperationError } from '../../ts-api';
import { AccessRights } from '../security/access-rights';
import { NodeType } from '../types/node-type';
import { executeBulkAction } from '../utils/bulk-utils';
import { TypeCache } from '../utils/type-cache';

export class BulkUpdateMutation {
    /** Resolver for `mutation { xtremFoo { Bar { create/update/updateById(data/id: ...) { ... } } } }` mutations */
    static makeResolver(
        typeCache: TypeCache,
        factory: NodeFactory,
    ): GraphQLFieldConfig<unknown, Context, { filter: string; data: Dict<any> }> {
        return {
            description: `Bulk update ${factory.name}`,
            type: GraphQLBoolean,
            args: {
                filter: { type: GraphQLString },
                data: { type: NodeType.makeInputType(typeCache, factory, 'nodeInput') },
            },
            resolve(_root, args, rootContext) {
                return AccessRights.runSecure(
                    rootContext,
                    'update',
                    {
                        factory,
                        isMutation: true,
                        operation: 'update',
                        args,
                    },
                    () =>
                        rootContext.withChildContext(
                            context =>
                                withRethrow(
                                    async () => {
                                        await executeBulkAction(context, args.filter, factory.nodeConstructor, {
                                            body: async (subContext: Context, instance: Node): Promise<void> => {
                                                const updateData = await NodeType.nodeValuesIn(
                                                    subContext,
                                                    args.data,
                                                    factory,
                                                    { forUpdate: true },
                                                );
                                                await instance.$.set(updateData);
                                                await instance.$.save();
                                            },
                                        });

                                        return true;
                                    },
                                    OperationError.errorMapper(context, factory, 'bulkUpdate'),
                                ),
                            {
                                isolationLevel: factory.getIsolationLevel('update'),
                            },
                        ),
                );
            },
        };
    }
}
