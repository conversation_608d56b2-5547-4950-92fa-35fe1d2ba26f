import { Dict, with<PERSON><PERSON><PERSON> } from '@sage/xtrem-shared';
import {
    GraphQLFieldConfig,
    GraphQLFieldConfigArgumentMap,
    GraphQLID,
    GraphQLInputType,
    GraphQLInt,
    GraphQLNonNull,
} from 'graphql';
import { OperationError } from '../../errors/operation-error';
import { Context, NodeFactory } from '../../runtime';
import { AccessRights } from '../security/access-rights';
import { NodeType } from '../types/node-type';
import { PropertyType } from '../types/property-type';
import { TypeCache } from '../utils/type-cache';

/**
 * @internal
 *
 * Static class which provides the GraphQL schema resolver for the `delete` mutation:
 *
 * ``` graphql
 *  mutation {
 *      xtremFoo {
 *          Bar {
 *              delete(data: ...) { ... }
 *              deleteById(id: ...) { ... }
 *          }
 *      }
 *  }
 *  ```
 */
export class DeleteMutation {
    // The GraphQL arguments for the mutation
    private static readTypeArgs(typeCache: TypeCache, factory: NodeFactory): GraphQLFieldConfigArgumentMap {
        const args = {} as GraphQLFieldConfigArgumentMap;
        factory.keyProperties.forEach(prop => {
            args[prop.name] = {
                type: new GraphQLNonNull(
                    PropertyType.makePropertyType(typeCache, factory, prop, 'nodeInput') as GraphQLInputType,
                ),
            };
        });
        return args;
    }

    static async executeDelete(
        context: Context,
        factory: NodeFactory,
        op: 'delete' | 'deleteById',
        args: Dict<any>,
    ): Promise<number> {
        if (op === 'deleteById') {
            const key = {
                _id: args._id,
            };
            const node = await context.tryRead(factory.nodeConstructor, key, {
                forUpdate: true,
            });
            if (!node) return 0;
            await node.$.delete();

            return 1;
        }
        let key = {};
        try {
            key = await NodeType.nodeValuesIn(context, args, factory);
        } catch {
            return 0;
        }
        return factory.deleteMany(context, key);
    }

    /** Resolver for `mutation { xtremFoo { Bar { delete/deleteById(data/id: ...) { ... } } } }` mutations */
    static makeResolver(
        typeCache: TypeCache,
        factory: NodeFactory,
        op: 'delete' | 'deleteById',
    ): GraphQLFieldConfig<unknown, Context, { _id: string } | Dict<any>> {
        return {
            description: `Delete a ${factory.name}`,
            type: GraphQLInt,
            args: op === 'deleteById' ? { _id: { type: GraphQLID } } : DeleteMutation.readTypeArgs(typeCache, factory),
            resolve(root: unknown, args: any, rootContext: Context) {
                return AccessRights.runSecure(
                    rootContext,
                    op,
                    {
                        factory,
                        isMutation: true,
                        operation: 'delete',
                        args,
                    },
                    () =>
                        rootContext.withChildContext(
                            context =>
                                withRethrow(
                                    () => DeleteMutation.executeDelete(context, factory, op, args),
                                    OperationError.errorMapper(context, factory, 'delete'),
                                ),

                            { isolationLevel: factory.getIsolationLevel('delete') },
                        ),
                );
            },
        };
    }
}
