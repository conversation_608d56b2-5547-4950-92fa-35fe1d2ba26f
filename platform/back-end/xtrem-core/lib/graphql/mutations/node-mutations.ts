import { schemaTypeName } from '@sage/xtrem-shared';
import { GraphQLFieldConfig, GraphQLFieldConfigMap, GraphQLObjectType } from 'graphql';
import { Context, NodeFactory } from '../../runtime';
import { NodeOperations } from '../metadata-schema/node-operations';
import { OperationType } from '../types/operation-type';
import { TypeCache } from '../utils/type-cache';
import { BulkUpdateMutation } from './bulk-update-mutation';
import { CreateUpdateMutations } from './create-update-mutations';
import { DeleteMutation } from './delete-mutation';
import { DuplicateMutation } from './duplicate-mutation';

/**
 * @internal
 *
 * Static class which provides the GraphQL schema resolver all the node mutations:
 *
 * ``` graphql
 *  mutation {
 *      xtremFoo {
 *          Bar { ... }
 *      }
 *  }
 *  ```
 */

export class NodeMutations extends NodeOperations {
    private static nodeMutationsType(typeCache: TypeCache, factory: NodeFactory): GraphQLObjectType | undefined {
        const fields = {} as GraphQLFieldConfigMap<any, any>;

        if (factory.storage === 'sql' || factory.storage === 'external') {
            if (factory.canCreate && !factory.isAbstract)
                fields.create = CreateUpdateMutations.makeResolver(typeCache, factory, 'create');
            if (factory.canUpdate) {
                fields.update = CreateUpdateMutations.makeResolver(typeCache, factory, 'update');
                fields.bulkUpdate = BulkUpdateMutation.makeResolver(typeCache, factory);
                fields.updateById = CreateUpdateMutations.makeResolver(typeCache, factory, 'updateById');
            }
            if (factory.canDelete) {
                fields.delete = DeleteMutation.makeResolver(typeCache, factory, 'delete');
                fields.deleteById = DeleteMutation.makeResolver(typeCache, factory, 'deleteById');
            }
            if (factory.canDuplicate) {
                fields.duplicate = DuplicateMutation.makeResolver(typeCache, factory);
            }
        }

        const name = `${schemaTypeName(factory.fullName)}_Mutations`;

        OperationType.addOperationFields(typeCache, factory, name, factory.mutations, true, fields);

        if (Object.keys(fields).length === 0) return undefined;

        return new GraphQLObjectType({ name, fields });
    }

    /**
     * Resolver for `mutation { xtremFoo { Bar { ... } } }` mutations
     *
     * Returns undefined if the node does not have any mutations.
     */
    static makeResolver(
        typeCache: TypeCache,
        factory: NodeFactory,
    ): GraphQLFieldConfig<unknown, Context, {}> | undefined {
        const type = NodeMutations.nodeMutationsType(typeCache, factory);
        if (!type) return undefined;
        return {
            description: factory.getNodeSchemaDescription(),
            type,
            resolve(): object {
                return {};
            },
        };
    }
}
