/** @module @sage/xtrem-core */
import { Dict } from '@sage/xtrem-shared';
import {
    GraphQLDeferDirective,
    GraphQLFieldConfig,
    GraphQLFieldConfigMap,
    GraphQLIncludeDirective,
    GraphQLNamedType,
    GraphQLObjectType,
    GraphQLSchema,
    GraphQLSkipDirective,
    GraphQLStreamDirective,
} from 'graphql';
import * as _ from 'lodash';
import { camelCase, isEmpty } from 'lodash';
import { Application } from '../application';
import { Package } from '../application/package';
import { Context } from '../runtime/context';
import { loggers } from '../runtime/loggers';
import { EnumDataType } from '../types';
import { NodeMutations } from './mutations/node-mutations';
import { NodeQueries } from './queries/node-queries';
import { AccessRights } from './security/access-rights';
import { TenantContext } from './types/basic-types';
import { EnumType } from './types/enum-type';
import { TypeCache } from './utils/type-cache';

const logger = loggers.graphQl;

/**
 * @disabled_internal
 *
 * Static class to generate the GraphQL schema for a given application
 */
export class SchemaBuilder {
    // creates the query side of the schema `query { ... }`
    private static createQueries(
        typeCache: TypeCache,
        tenantContext: TenantContext,
        pack: Package,
    ): Dict<GraphQLFieldConfig<any, any>> {
        if (!AccessRights.isPackageAvailable(pack, tenantContext.tenantPackages)) {
            return {} as Dict<GraphQLFieldConfig<any, any>>;
        }
        return pack.factories
            .filter(factory => factory.nodeDecorator.isPublished)
            .reduce(
                (r, factory) => {
                    const queries = NodeQueries.makeResolver(typeCache, factory);
                    if (queries) r[camelCase(factory.name)] = queries;
                    return r;
                },
                {} as Dict<GraphQLFieldConfig<any, any>>,
            );
    }

    // creates the mutation side of the schema `mutation { ... }`
    private static createMutations(
        typeCache: TypeCache,
        tenantContext: TenantContext,
        pack: Package,
    ): Dict<GraphQLFieldConfig<any, any>> {
        if (!AccessRights.isPackageAvailable(pack, tenantContext.tenantPackages)) {
            return {} as Dict<GraphQLFieldConfig<any, any>>;
        }
        const result = pack.factories
            .filter(factory => factory.isPublished)
            .reduce(
                (r, factory) => {
                    const mutations = NodeMutations.makeResolver(typeCache, factory);
                    if (mutations) r[camelCase(factory.name)] = mutations;
                    return r;
                },
                {} as Dict<GraphQLFieldConfig<any, any>>,
            );
        return result;
    }

    // creates the standalone types, like enums that are not allocated to properties
    private static createTypes(application: Application, typeCache: TypeCache): GraphQLNamedType[] {
        const types: GraphQLNamedType[] = [];
        Object.values(application.dataTypes)
            .filter(dataType => dataType instanceof EnumDataType)
            .forEach(dataType => {
                const enumDataType = dataType as EnumDataType;
                const enumTypeName = EnumType.getTypeName(enumDataType, false);
                const enumTypeNameInput = EnumType.getTypeName(enumDataType, true);

                if (typeCache.getCached(enumTypeName) === undefined)
                    types.push(EnumType.makeGraphQLEnumType(typeCache, enumDataType, false));
                if (typeCache.getCached(enumTypeNameInput) === undefined)
                    types.push(EnumType.makeGraphQLEnumType(typeCache, enumDataType, true));
            });
        return types;
    }

    // Wraps a package GraphQL API with `xtremPackage { ... }`
    private static getNamespace(
        application: Application,
        suffix: string,
        getFields: (pack: Package) => GraphQLFieldConfigMap<any, any>,
    ): GraphQLFieldConfigMap<any, any> {
        const marker = {};
        const descriptions = {} as Dict<any>;
        const version = application.version || '';

        const namespaces = application
            .getPackages()
            .filter(pack => pack.factories.some(factory => !!factory.isPublished))
            .reduce((r, pack) => {
                const fields = getFields(pack);
                if (Object.keys(fields).length === 0) return r;
                const segs = pack.name.split('/');
                const scope = segs[0].startsWith('@') ? segs.shift() : '';
                const checkSegment = (segment?: string): void => {
                    // just in case to prevent package name collision
                    if (scope !== '@sage' && segment?.startsWith('xtrem-')) {
                        throw new Error("'xtrem' prefix is reserved!");
                    }
                };
                let rr = r;
                while (segs.length > 1) {
                    const part = segs.shift();
                    checkSegment(part);
                    const name = camelCase(part);
                    if (!rr[name]) {
                        rr[name] = {
                            name,
                            fields: {},
                            marker,
                        };
                    }
                    rr = rr[name].fields;
                }
                const lastPart = segs.shift();
                checkSegment(lastPart);
                const lastSeg = camelCase(lastPart);
                rr[lastSeg] = fields;
                descriptions[lastSeg] = `Package Version : ${pack.packageJson.version}`;
                return r;
            }, {} as Dict<any>);
        const wrapFields = (obj: any): any => {
            if (obj.marker !== marker) return obj;

            return Object.keys(obj.fields).reduce((r, k) => {
                r[k] = {
                    type: new GraphQLObjectType({
                        name: k + suffix,
                        fields: wrapFields(obj.fields[k]),
                    }),
                    description: descriptions[k] || `Application Version : ${version}`,
                    resolve(): object {
                        return {};
                    },
                };
                return r;
            }, {} as any);
        };

        return _.isEmpty(this.typeCache.globalNamespace)
            ? wrapFields({
                  fields: { ...namespaces },
                  marker,
              })
            : wrapFields({
                  fields: { ...namespaces, global: this.typeCache.globalNamespace },
                  marker,
              });
    }

    /** @disabled_internal */
    static typeCache: TypeCache;

    // /** Gets the tenant schema for an application - called by application.getGraphQlSchema() */
    static async getSchema(application: Application, context?: Context): Promise<GraphQLSchema> {
        logger.info(() => `Creating GraphQL schema for application ${application.name}`);

        const tenantPackages = context ? await context.getActivePackageNames() : ['all'];

        logger.verbose(() => `GraphQL active packages: ${tenantPackages.join()}`);
        this.typeCache = new TypeCache(application);

        const profiler = loggers.graphQl.verbose(() => `Create schema ${application.name}`);

        const queryFields = SchemaBuilder.getNamespace(application, 'Query', pack =>
            SchemaBuilder.createQueries(this.typeCache, { tenantPackages }, pack),
        );
        // We reset the global namespace because for async mutation, tracking query and start/stop mutation action has the same operation name
        this.typeCache.resetGlobalNamespace();

        const mutationFields = SchemaBuilder.getNamespace(application, 'Mutation', pack =>
            SchemaBuilder.createMutations(this.typeCache, { tenantPackages }, pack),
        );

        if (isEmpty(queryFields) && isEmpty(mutationFields)) {
            throw new Error('Cannot create an empty Graphql schema: you are missing both queries and mutations!');
        }

        const types = SchemaBuilder.createTypes(application, this.typeCache);

        const result = new GraphQLSchema({
            query: new GraphQLObjectType({
                name: 'RootQueryType',
                fields: queryFields,
            }),
            // see https://medium.com/@HurricaneJames/graphql-mutations-fb3ad5ae73c4
            ...(!isEmpty(mutationFields) && {
                mutation: new GraphQLObjectType({
                    name: 'MutationsType',
                    fields: mutationFields,
                }),
            }),
            types,
            // Register @defer, @stream, @skip and @include, directives
            directives: [GraphQLDeferDirective, GraphQLStreamDirective, GraphQLSkipDirective, GraphQLIncludeDirective],
            // This flag is not documented but very important (@defer and @stream are ignored without it).
            // It may go away. See https://github.com/robrichard/defer-stream-wg/discussions/12
            enableDeferStream: true,
        });
        profiler.success();
        return result;
    }
}
