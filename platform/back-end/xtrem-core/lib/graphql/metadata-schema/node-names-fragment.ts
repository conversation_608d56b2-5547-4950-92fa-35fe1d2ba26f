import { CustomMetrics } from '@sage/xtrem-metrics';
import { GraphQLBoolean, GraphQLList, GraphQLObjectType, GraphQLString } from 'graphql';
import { runResolver } from '../../concurrency-utils';
import { Context } from '../../runtime';
import { MetadataSchemaFragment, SchemaFragmentFunction } from './metadata-schema-utils';

export const nodeDetailsFields = {
    name: { type: GraphQLString },
    title: { type: GraphQLString },
};

export const nodeDetailsNameType = new GraphQLObjectType({
    name: '_NodeNameLocalization',
    fields: nodeDetailsFields,
});

const nodeDetailsListType = new GraphQLList(nodeDetailsNameType);

export const getNodeNamesSchemaFragment: SchemaFragmentFunction = (): MetadataSchemaFragment => {
    const getNodeNames = {
        type: nodeDetailsListType,
        args: {
            onlyPublishedNodes: {
                type: GraphQLBoolean,
            },
        },
        resolve: (_: unknown, args: any, context: Context) =>
            CustomMetrics.metadata.withMetrics(
                {
                    nodeName: 'getNodeNames',
                    operationName: 'read',
                    operationKind: 'query',
                },
                () =>
                    runResolver(context, () => {
                        const packageFactories = context.application
                            .getAllFactories()
                            .filter(
                                factory =>
                                    !factory.isSharedByAllTenants &&
                                    !factory.isPlatformNode &&
                                    (!args.onlyPublishedNodes || factory.isPublished),
                            );

                        return packageFactories.map(factory => {
                            return {
                                name: `${factory.package.name}/${factory.name}`,
                                title: factory.getLocalizedTitle(context),
                            };
                        });
                    }),
            ),
    };

    return {
        queries: {
            getNodeNames,
        },
    };
};
