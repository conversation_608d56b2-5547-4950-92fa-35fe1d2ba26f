import { getLiterals, getLocaleFromHeader } from '@sage/xtrem-i18n';
import { CustomMetrics } from '@sage/xtrem-metrics';
import { GraphQLInputObjectType, GraphQLString } from 'graphql';
import { runResolver } from '../../concurrency-utils';
import { Context } from '../../runtime';
import { localizationsType, MetadataSchemaFragment, SchemaFragmentFunction } from './metadata-schema-utils';

export const getStringsSchemaFragment: SchemaFragmentFunction = (): MetadataSchemaFragment => {
    const strings = {
        type: localizationsType,
        args: {
            filter: {
                type: new GraphQLInputObjectType({
                    name: 'StringFilter',
                    fields: {
                        packageOrPage: { type: GraphQLString },
                    },
                }),
            },
        },
        resolve: (
            _: unknown,
            { filter: { packageOrPage } }: { filter: { packageOrPage: string } },
            context: Context,
        ): Promise<{ key: string; content: string }[]> =>
            CustomMetrics.metadata.withMetrics(
                {
                    nodeName: 'strings',
                    operationName: 'read',
                    operationKind: 'query',
                },
                () =>
                    runResolver(context, () => {
                        const locale = getLocaleFromHeader(context.request.headers);
                        return packageOrPage
                            .split(',')
                            .reduce<
                                { key: string; content: string }[]
                            >((literals, filter) => literals.concat(getLiterals(filter, locale)), []);
                    }),
            ),
    };

    return {
        queries: {
            strings,
        },
    };
};
