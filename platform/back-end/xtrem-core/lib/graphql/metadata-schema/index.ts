/** @ignore */ /** */
import { GraphQLFieldConfigMap, GraphQLObjectType, GraphQLSchema, GraphQLString } from 'graphql';
import { Application } from '../../application';
import { CoreHooks } from '../../runtime/core-hooks';
import { getAboutSchemaFragment } from './about-fragment';
import { getClientUserSettingsSchemaFragment } from './client-user-settings-fragment';
import { getDashboardSchemaFragment } from './dashboard-fragment';
import { getDataTypeSchemaFragment } from './data-type-fragment';
import { getInstalledPackagesSchemaFragment } from './installed-packages-fragment';
import { MetadataSchemaFragment } from './metadata-schema-utils';
import { getNodeDetailsSchemaFragment } from './node-details-fragment';
import { getNodeNamesSchemaFragment } from './node-names-fragment';
import { getNotificationSchemaFragment } from './notification-fragment';
import { getPagesSchemaFragment } from './pages-fragment';
import { getPrintingSettingsSchemaFragment } from './printing-settings-fragment';
import { getServiceOptionsSchemaFragment } from './service-options-fragment';
import { getServiceSettingsSchemaFragment } from './service-settings-fragment';
import { getSitemapSchemaFragment } from './sitemap-fragment';
import { getStickersSchemaFragment } from './stickers-fragment';
import { getStringsSchemaFragment } from './strings-fragment';
import { getUserInfoSchemaFragment } from './user-info-fragment';
import { getWidgetsSchemaFragment } from './widgets-fragment';
import { getWorkflowSchemaFragment } from './workflow-fragment';

/**
 * Resolves various parts of the sitemap request. It returns the values from at the sitemap tree of the application and
 * based on the GraphQL request's path it returns the right elements. In addition to that, it sets the entry titles
 * based on the
 *  */
export function createMetadataSchema(application: Application): GraphQLSchema {
    const schemaFragments = [
        getAboutSchemaFragment,
        getDashboardSchemaFragment,
        getDataTypeSchemaFragment,
        getInstalledPackagesSchemaFragment,
        getNodeDetailsSchemaFragment,
        getNodeNamesSchemaFragment,
        getPrintingSettingsSchemaFragment,
        getNotificationSchemaFragment,
        getPagesSchemaFragment,
        getServiceOptionsSchemaFragment,
        getSitemapSchemaFragment,
        getStickersSchemaFragment,
        getStringsSchemaFragment,
        getUserInfoSchemaFragment,
        getWidgetsSchemaFragment,
        getWorkflowSchemaFragment,
        getClientUserSettingsSchemaFragment,
        getServiceSettingsSchemaFragment,
    ].map(getter => getter(application));

    const queryFields = schemaFragments
        .filter(s => !!s.queries)
        .reduce(
            (prevValue: GraphQLFieldConfigMap<any, any>, s: MetadataSchemaFragment) => ({ ...prevValue, ...s.queries }),
            {} as GraphQLFieldConfigMap<any, any>,
        );

    const mutationFields = schemaFragments
        .filter(s => !!s.mutations)
        .reduce(
            (prevValue: GraphQLFieldConfigMap<any, any>, s: MetadataSchemaFragment) => ({
                ...prevValue,
                ...s.mutations,
            }),
            {} as GraphQLFieldConfigMap<any, any>,
        );

    return new GraphQLSchema({
        query: new GraphQLObjectType({
            name: 'RootQueryType',
            fields: {
                ...queryFields,
                customizationWizardPage: {
                    type: GraphQLString,
                    resolve: () => CoreHooks.customizationManager.getWizardUrl(),
                },
                exportConfigurationPage: {
                    type: GraphQLString,
                    resolve: () => CoreHooks.importExportManager.getExportPageUrl(),
                },
            },
        }),
        mutation: new GraphQLObjectType({
            name: 'RootMutationType',
            fields: mutationFields,
        }),
    });
}
