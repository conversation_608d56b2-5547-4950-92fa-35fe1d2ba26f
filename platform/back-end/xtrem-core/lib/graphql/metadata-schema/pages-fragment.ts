import { GraphQLBoolean, GraphQLList, GraphQLObjectType, GraphQLString } from 'graphql';
import { Application } from '../../application/application';
import { runResolver } from '../../concurrency-utils';
import { Context } from '../../runtime';
import { ArtifactManager } from '../../system/artifact-manager';
import { ApplicationArtifact, ClientArtifactType } from '../../system/pages/client-service';
import { getNameWithoutPackage } from '../../types/util';
import { jsonType } from '../types/basic-types';
import {
    MetadataSchemaFragment,
    SchemaFragmentFunction,
    accessListType,
    extensionsListType,
    fragmentsType,
    getArtifactStringsConfig,
    getArtifactTitleConfig,
    pagesArgsWithFilter,
    pluginsType,
    resolveArtifact,
} from './metadata-schema-utils';
import {
    nodeCustomDetailsListType,
    nodeDetailsListType,
    resolveNodeCustomDetailsList,
    resolveNodeDetailsList,
} from './node-details-fragment';

const duplicateBindingsType = new GraphQLList(GraphQLString);

const customizableNodeType = new GraphQLObjectType({
    name: '_MetaCustomizableNode',
    fields: {
        node: { type: GraphQLString },
        fullName: { type: GraphQLString },
    },
});

const customizableNodesType = new GraphQLList(customizableNodeType);

const customFieldType = new GraphQLObjectType({
    name: '_MetaCustomField',
    fields: {
        name: { type: GraphQLString },
        dataType: { type: GraphQLString },
        targetNodeName: { type: GraphQLString },
        componentType: { type: GraphQLString },
        anchorPropertyName: { type: GraphQLString },
        anchorPosition: { type: GraphQLString },
        destinationTypes: { type: new GraphQLList(GraphQLString) },
        componentAttributes: { type: GraphQLString },
    },
});

const customFieldsType = new GraphQLList(customFieldType);

const customNodeType = new GraphQLObjectType({
    name: '_MetaCustomNode',
    fields: {
        name: { type: GraphQLString },
        properties: { type: customFieldsType },
    },
});

const exportTemplateType = new GraphQLObjectType({
    name: '_MetaExportTemplateType',
    fields: {
        id: { type: GraphQLString },
        name: { type: GraphQLString },
        isDefault: { type: GraphQLBoolean },
    },
});

const nodeExportTemplateType = new GraphQLObjectType({
    name: '_MetaNodeExportTemplateType',
    fields: {
        name: { type: GraphQLString },
        exportTemplates: { type: new GraphQLList(exportTemplateType) },
    },
});

const userSetting = new GraphQLObjectType({
    name: '_MetaUserSetting',
    fields: {
        _id: { type: GraphQLString },
        content: { type: jsonType },
        elementId: { type: GraphQLString },
        title: { type: GraphQLString },
        description: { type: GraphQLString },
    },
});

const customNodesType = new GraphQLList(customNodeType);
const exportTemplatesType = new GraphQLList(nodeExportTemplateType);
const userSettingList = new GraphQLList(userSetting);

const pageType = new GraphQLObjectType({
    name: '_Page',
    fields: {
        key: { type: GraphQLString },
        title: getArtifactTitleConfig(ClientArtifactType.pages),
        content: {
            type: GraphQLString,
            resolve: (artifact: ApplicationArtifact, _args: any, context: Context) =>
                runResolver(context, () => {
                    return artifact ? artifact.content.toString() : '';
                }),
        },
        category: { type: GraphQLString },
        extensions: {
            type: extensionsListType,
            resolve: (artifact: ApplicationArtifact, _args: any, context: Context) =>
                runResolver(context, async () => {
                    const extensions = await ArtifactManager.getExtensionsForPage(
                        context,
                        artifact.key,
                        ClientArtifactType.pages,
                    );
                    return extensions.map(a => ({ content: a.content.toString(), packageName: a.packageName }));
                }),
        },
        fragments: {
            type: fragmentsType,
            resolve: (artifact: ApplicationArtifact, _args: any, context: Context) =>
                runResolver(context, async () => {
                    const fragments = await ArtifactManager.getArtifactPageFragments(context, artifact);
                    return Object.values(fragments).map(a => ({
                        name: a.key,
                        content: a.content.toString(),
                        packageName: a.packageName,
                    }));
                }),
        },
        plugins: { type: pluginsType },
        strings: getArtifactStringsConfig(ClientArtifactType.pages),
        access: {
            type: accessListType,
            resolve: (artifact: ApplicationArtifact, _args: any, context: Context) =>
                runResolver(context, () => {
                    return ArtifactManager.resolveAccess(context, artifact);
                }),
        },
        pageAccess: {
            type: GraphQLString,
            resolve: (artifact: ApplicationArtifact, __: any, context: Context) =>
                runResolver(context, () => {
                    return ArtifactManager.resolvePageAccess(context, artifact);
                }),
        },
        pageNode: { type: GraphQLString },
        customFields: {
            type: customNodesType,
            resolve: (artifact: ApplicationArtifact, __: any, context: Context) =>
                runResolver(context, () => {
                    return ArtifactManager.resolvePageCustomFields(context, artifact);
                }),
        },
        exportTemplatesByNode: {
            type: exportTemplatesType,
            resolve: (artifact: ApplicationArtifact, __: any, context: Context) =>
                runResolver(context, () => {
                    return ArtifactManager.resolvePageExportTemplatesByNode(context, artifact);
                }),
        },
        hasRecordPrintingTemplates: {
            type: GraphQLBoolean,
            resolve: (artifact: ApplicationArtifact, __: any, context: Context) =>
                runResolver(context, () => {
                    return ArtifactManager.resolveHasRecordPrintingTemplates(context, artifact);
                }),
        },
        duplicateBindings: { type: duplicateBindingsType },
        customizableNodes: {
            type: customizableNodesType,
            resolve: (artifact: ApplicationArtifact, __: any, context: Context) =>
                runResolver(context, () => {
                    return ArtifactManager.resolvePageCustomizableNodes(context, artifact);
                }),
        },
        customizableNodesWizard: {
            type: customizableNodesType,
            resolve: (artifact: ApplicationArtifact, __: any, context: Context) =>
                runResolver(context, () => {
                    return ArtifactManager.resolvePageCustomizableNodesWizard(context, artifact);
                }),
        },
        activeUserSettings: {
            type: userSettingList,
            resolve: (artifact: ApplicationArtifact, __: any, context: Context) =>
                runResolver(context, () => {
                    const artifactName = artifact.key.split('/')[2];

                    return context.application.clientSettingsManager.getActiveClientSettingsForArtifact(
                        context,
                        artifactName,
                    );
                }),
        },
        nodeDetails: {
            type: nodeDetailsListType,
            args: {
                knownNodeNames: { type: new GraphQLList(GraphQLString) },
            },
            resolve: (artifact: ApplicationArtifact, args: { knownNodeNames: string[] }, context: Context) =>
                runResolver(context, async () => {
                    const nodes = await ArtifactManager.getArtifactNodes(context, artifact);

                    if (nodes.length === 0) return [];

                    const missingNodeNames = nodes.map(getNameWithoutPackage);

                    return resolveNodeDetailsList(context, {
                        missingNodeNames,
                        knownNodeNames: args.knownNodeNames ?? [],
                        depth: 3,
                    });
                }),
        },
        nodeCustomDetails: {
            type: nodeCustomDetailsListType,
            resolve: (artifact: ApplicationArtifact, args: { knownNodeNames: string[] }, context: Context) =>
                runResolver(context, async () => {
                    const nodes = await ArtifactManager.getArtifactNodes(context, artifact);

                    if (nodes.length === 0) return [];

                    const missingNodeNames = nodes.map(getNameWithoutPackage);

                    return resolveNodeCustomDetailsList(context, {
                        missingNodeNames,
                        knownNodeNames: args.knownNodeNames ?? [],
                        depth: 3,
                    });
                }),
        },
    },
});

const pagesType = new GraphQLList(pageType);

export const getPagesSchemaFragment: SchemaFragmentFunction = (application: Application): MetadataSchemaFragment => {
    const pages = {
        type: pagesType,
        args: pagesArgsWithFilter,
        resolve: resolveArtifact(application, ClientArtifactType.pages),
    };

    return {
        queries: {
            pages,
        },
    };
};
