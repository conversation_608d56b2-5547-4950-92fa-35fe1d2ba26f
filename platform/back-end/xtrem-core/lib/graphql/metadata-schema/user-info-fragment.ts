import { CustomMetrics } from '@sage/xtrem-metrics';
import { GraphQLList, GraphQLObjectType, GraphQLString } from 'graphql';
import { runResolver } from '../../concurrency-utils';
import { Context, UserNavigationInfo } from '../../runtime';
import { ClientService } from '../../system/pages/client-service';
import { MetadataSchemaFragment, SchemaFragmentFunction } from './metadata-schema-utils';

const userInfoType = new GraphQLObjectType({
    name: '_UserInfo',
    fields: {
        _id: { type: GraphQLString },
        firstName: { type: GraphQLString },
        lastName: { type: GraphQLString },
        /**
         * The userCode can be used if the application has a username that is different from the login user.
         */
        userCode: { type: GraphQLString },
        email: { type: GraphQLString },
        photo: { type: GraphQLString },
        bookmarks: { type: new GraphQLList(GraphQLString) },
        history: { type: new GraphQLList(GraphQLString) },
        uniqueUserId: { type: GraphQLString },
        uniqueTenantId: { type: GraphQLString },
        clientEncryptionKey: { type: GraphQLString },
    },
});

export const getUserInfoSchemaFragment: SchemaFragmentFunction = (): MetadataSchemaFragment => {
    const userInfo = {
        type: userInfoType,
        resolve: (_: unknown, __: unknown, context: Context) =>
            CustomMetrics.metadata.withMetrics(
                {
                    nodeName: 'sitemap',
                    operationName: 'read',
                    operationKind: 'query',
                },
                () =>
                    runResolver(context, async () => {
                        const uniqueUserId = await ClientService.getUniqueUserId(context);
                        const uniqueTenantId = ClientService.getUniqueTenantId(context);
                        const defaultEncryptionKey = await ClientService.getDefaultUserEncryptionKey(context);
                        const user = await context.user;
                        if (user) {
                            const navigation = Context.accessRightsManager.getUserNavigation
                                ? await Context.accessRightsManager.getUserNavigation(context)
                                : ({} as UserNavigationInfo);
                            return {
                                _id: user._id,
                                firstName: user.firstName,
                                lastName: user.lastName,
                                userCode: user.userName,
                                email: user.email,
                                history: navigation.history || [],
                                bookmarks: navigation.bookmarks || [],
                                photo: user.photo?.value.toString('base64'),
                                clientEncryptionKey: user.clientEncryptionKey || defaultEncryptionKey,
                                uniqueUserId,
                                uniqueTenantId,
                            };
                        }

                        return null;
                    }),
            ),
    };

    return {
        queries: {
            userInfo,
        },
    };
};
