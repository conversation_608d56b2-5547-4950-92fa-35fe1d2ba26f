import { AsyncResponse, Dashboard } from '@sage/xtrem-shared';
import {
    GraphQLBoolean,
    GraphQLEnumType,
    GraphQLFieldConfig,
    GraphQLInt,
    GraphQLList,
    GraphQLNonNull,
    GraphQLObjectType,
    GraphQLString,
} from 'graphql';
import { runResolver } from '../../concurrency-utils';
import { Context } from '../../runtime';
import { CoreHooks } from '../../runtime/core-hooks';
import { MetadataSchemaFragment, SchemaFragmentFunction } from './metadata-schema-utils';

const dashboardItemPosition = new GraphQLObjectType({
    name: '_DashboardItemPosition',
    fields: {
        x: { type: GraphQLInt },
        y: { type: GraphQLInt },
        w: { type: GraphQLInt },
        h: { type: GraphQLInt },
        breakpoint: {
            type: new GraphQLEnumType({
                name: '_DashboardItemPositionBreakpoint',
                values: {
                    xxs: { value: 'xxs' },
                    xs: { value: 'xs' },
                    sm: { value: 'sm' },
                    md: { value: 'md' },
                    lg: { value: 'lg' },
                },
            }),
        },
    },
});

const dashboardItem = new GraphQLObjectType({
    name: '_DashboardItem',
    fields: {
        _id: { type: GraphQLString },
        type: { type: GraphQLString },
        settings: { type: GraphQLString },
        positions: { type: new GraphQLList(dashboardItemPosition) },
    },
});

const dashboardType = new GraphQLObjectType({
    name: '_Dashboard',
    fields: {
        _id: { type: GraphQLString },
        title: { type: GraphQLString },
        description: { type: GraphQLString },
        icon: { type: GraphQLString },
        children: { type: new GraphQLList(dashboardItem) },
        isTemplate: { type: GraphQLBoolean },
    },
});

const dashboardNoChildrenType = new GraphQLObjectType({
    name: '_DashboardWithoutChildren',
    fields: {
        _id: { type: GraphQLString },
        title: { type: GraphQLString },
        listIcon: { type: GraphQLString },
        description: { type: GraphQLString },
    },
});

const widgetType = new GraphQLObjectType({
    name: '_Widget',
    fields: {
        key: { type: GraphQLString },
        title: { type: GraphQLString },
    },
});

const dashboardNoChildrenListType = new GraphQLList(dashboardNoChildrenType);
const widgetListType = new GraphQLList(widgetType);

const dashboardRootType = new GraphQLObjectType({
    name: '_DashboardRoot',
    fields: {
        selectedDashboard: {
            type: dashboardType,
            args: {
                group: { type: GraphQLString },
            },
            resolve: (_: unknown, args: any, context: Context) =>
                runResolver<Dashboard | null>(context, () =>
                    CoreHooks.createDashboardManager(context.application).getSelectedDashboard(context, args.group),
                ),
        },
        availableDashboards: {
            type: dashboardNoChildrenListType,
            args: {
                group: { type: GraphQLString },
            },
            resolve: (_: unknown, args: any, context: Context) =>
                runResolver<{ title: string; _id: string }[]>(context, async () => {
                    const dashboards = await CoreHooks.createDashboardManager(context.application).getDashboardList(
                        context,
                        args.group,
                    );
                    return Object.keys(dashboards).map(_id => ({ _id, title: dashboards[_id] }));
                }),
        },
        templates: {
            type: dashboardNoChildrenListType,
            args: {
                group: { type: GraphQLString },
            },
            resolve: (_: unknown, args: any, context: Context) =>
                runResolver<Dashboard[]>(context, () =>
                    CoreHooks.createDashboardManager(context.application).getFactoryDashboardList(context, args.group),
                ),
        },
        getWidgetCategories: {
            type: widgetListType,
            resolve: (_: unknown, __: unknown, context: Context) =>
                runResolver<{ key: string; title: string }[]>(context, () =>
                    CoreHooks.createDashboardManager(context.application).getWidgetCategories(context),
                ),
        },
        canEditDashboards: {
            type: GraphQLBoolean,
            resolve: (_: unknown, __: unknown, context: Context) => {
                return runResolver<boolean>(context, () =>
                    CoreHooks.createDashboardManager(context.application).canEditDashboards(context),
                );
            },
        },
    },
});

const updateDashboardLayoutMutation: GraphQLFieldConfig<unknown, any, Context> = {
    type: dashboardType,
    args: {
        dashboardId: { type: new GraphQLNonNull(GraphQLString) },
        layout: { type: new GraphQLNonNull(GraphQLString) },
    },
    resolve: (_: unknown, args: any, context: Context) => {
        return runResolver<Dashboard>(context, (): AsyncResponse<Dashboard> => {
            // Just to ensure that the value is valid JSON.
            const layout = JSON.parse(args.layout);
            return context.application.requestFunnel(
                (): AsyncResponse<Dashboard> =>
                    context.withChildContext(
                        ctx =>
                            CoreHooks.createDashboardManager(ctx.application).updateDashboardLayout(
                                ctx,
                                args.dashboardId,
                                layout,
                            ),
                        { isolationLevel: 'low' },
                    ),
            );
        });
    },
};

const updateWidgetSettingsMutation: GraphQLFieldConfig<unknown, any, Context> = {
    type: GraphQLBoolean,
    args: {
        dashboardItemId: { type: new GraphQLNonNull(GraphQLString) },
        settings: { type: new GraphQLNonNull(GraphQLString) },
    },
    resolve: (_: unknown, args: any, context: Context) => {
        return runResolver<boolean>(context, async () => {
            // Just to ensure that the value is valid JSON.
            const settings = JSON.parse(args.settings);
            await context.application.requestFunnel(() =>
                context.withChildContext(
                    ctx =>
                        CoreHooks.createDashboardManager(ctx.application).updateDashboardItemSettings(
                            ctx,
                            args.dashboardItemId,
                            settings,
                        ),
                    { isolationLevel: 'low' },
                ),
            );
            return true;
        });
    },
};

const createDashboardMutation: GraphQLFieldConfig<unknown, any, Context> = {
    type: dashboardType,
    args: {
        group: { type: GraphQLString },
    },
    resolve: (_: unknown, args: any, context: Context) =>
        runResolver<Dashboard>(context, () =>
            context.application.requestFunnel(() =>
                context.withChildContext(
                    ctx => CoreHooks.createDashboardManager(ctx.application).createDashboard(ctx, args.group),
                    { isolationLevel: 'low' },
                ),
            ),
        ),
};
const deleteDashboardMutation: GraphQLFieldConfig<unknown, any, Context> = {
    type: GraphQLBoolean,
    args: {
        dashboardId: { type: new GraphQLNonNull(GraphQLString) },
    },
    resolve: (_: unknown, args: any, context: Context) =>
        runResolver<void>(
            context,
            (): Promise<void> =>
                context.application.requestFunnel(() =>
                    context.withChildContext(
                        ctx => CoreHooks.createDashboardManager(ctx.application).deleteDashboard(ctx, args.dashboardId),
                        { isolationLevel: 'low' },
                    ),
                ),
        ),
};

const selectDashboardMutation: GraphQLFieldConfig<unknown, any, Context> = {
    type: dashboardType,
    args: {
        dashboardId: { type: new GraphQLNonNull(GraphQLString) },
        group: { type: GraphQLString },
    },
    resolve: (_: unknown, args: any, context: Context) =>
        runResolver<Dashboard>(context, () =>
            context.application.requestFunnel(() =>
                context.withChildContext(
                    ctx =>
                        CoreHooks.createDashboardManager(ctx.application).setSelectedDashboard(
                            ctx,
                            args.dashboardId,
                            args.group,
                        ),
                    { isolationLevel: 'low' },
                ),
            ),
        ),
};

const cloneDashboardMutation: GraphQLFieldConfig<unknown, any, Context> = {
    type: dashboardType,
    args: {
        dashboardId: { type: new GraphQLNonNull(GraphQLString) },
    },
    resolve: (_: unknown, args: any, context: Context) =>
        runResolver<Dashboard>(context, () =>
            context.application.requestFunnel(() =>
                context.withChildContext(
                    ctx => CoreHooks.createDashboardManager(ctx.application).cloneDashboard(ctx, args.dashboardId),
                    { isolationLevel: 'low' },
                ),
            ),
        ),
};

const dashboardMutationType = new GraphQLObjectType({
    name: '_DashboardRootMutation',
    fields: {
        updateWidgetSettings: updateWidgetSettingsMutation,
        updateDashboardLayout: updateDashboardLayoutMutation,
        createDashboard: createDashboardMutation,
        cloneDashboard: cloneDashboardMutation,
        deleteDashboard: deleteDashboardMutation,
        selectDashboard: selectDashboardMutation,
    },
});

export const getDashboardSchemaFragment: SchemaFragmentFunction = (): MetadataSchemaFragment => {
    const dashboard = {
        type: dashboardRootType,
        resolve: () => ({}),
    };
    const dashboardMutation = {
        type: dashboardMutationType,
        resolve: () => ({}),
    };

    return {
        queries: {
            dashboard,
        },
        mutations: {
            dashboard: dashboardMutation,
        },
    };
};
