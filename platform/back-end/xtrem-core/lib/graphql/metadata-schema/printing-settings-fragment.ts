import { CustomMetrics } from '@sage/xtrem-metrics';
import { GraphQLBoolean, GraphQLObjectType, GraphQLString } from 'graphql';
import { runResolver } from '../../concurrency-utils';
import { Context } from '../../runtime';
import { CoreHooks } from '../../runtime/core-hooks';
import { ArtifactManager } from '../../system/artifact-manager';
import { ClientArtifactType, ClientService } from '../../system/pages/client-service';
import { MetadataSchemaFragment, SchemaFragmentFunction } from './metadata-schema-utils';

const isPageAccessible = async (context: Context, page: string): Promise<boolean> => {
    const artifact = await ArtifactManager.readClientArtifact(context, ClientArtifactType.pages, {
        exactMatch: true,
        packageOrPage: page,
    });

    const pageAccessStatus = await ClientService.getPageAccess(context, artifact?.pageAccess, {
        authorizationCode: artifact?.authorizationCode,
    });

    const extensionAccessStatus = await ClientService.getPageAccess(context, artifact?.extensionAccess, {
        authorizationCode: artifact?.authorizationCode,
    });

    return pageAccessStatus === 'authorized' && extensionAccessStatus === 'authorized';
};

const printingSettingsRootType = new GraphQLObjectType({
    name: '_ReportingSettingsRoot',
    fields: {
        printingAssignmentDialogUrl: {
            type: GraphQLString,
        },
        canAccessPrintingAssignmentDialog: {
            type: GraphQLBoolean,
        },
        listPrintingWizardUrl: {
            type: GraphQLString,
        },
        canAccessListPrintingWizard: {
            type: GraphQLBoolean,
        },
        recordPrintingWizardUrl: {
            type: GraphQLString,
        },
        recordPrintingGlobalBulkMutationName: {
            type: GraphQLString,
        },
        listPrintingGlobalMutationConfigPage: {
            type: GraphQLString,
        },
        canAccessRecordPrintingWizard: {
            type: GraphQLBoolean,
        },
    },
});

export const getPrintingSettingsSchemaFragment: SchemaFragmentFunction = (): MetadataSchemaFragment => {
    const printingSettings = {
        type: printingSettingsRootType,
        resolve: (_source: unknown, args: unknown, context: Context) =>
            CustomMetrics.metadata.withMetrics(
                {
                    nodeName: 'printingSettings',
                    operationName: 'read',
                    operationKind: 'query',
                },
                () =>
                    runResolver(context, async () => {
                        const printingManager = CoreHooks.createPrintingManager(context.application);

                        if (!printingManager) {
                            return null;
                        }
                        const printingAssignmentDialogUrl =
                            await printingManager.getPrintingAssignmentDialogUrl(context);
                        const listPrintingWizardUrl = await printingManager.getListPrintingWizardUrl(context);
                        const recordPrintingWizardUrl = await printingManager.getRecordPrintingWizardUrl(context);
                        const recordPrintingGlobalBulkMutationName =
                            await printingManager.getRecordPrintingGlobalBulkMutationName(context);

                        const listPrintingGlobalMutationConfigPage =
                            await printingManager.getRecordListPrintingGlobalBulkActionConfigurationURL(context);
                        return {
                            printingAssignmentDialogUrl,
                            recordPrintingGlobalBulkMutationName,
                            canAccessPrintingAssignmentDialog: printingAssignmentDialogUrl
                                ? isPageAccessible(context, printingAssignmentDialogUrl)
                                : null,
                            listPrintingWizardUrl,
                            canAccessListPrintingWizard: false, // force disabled for now until we implement the page associated with this
                            recordPrintingWizardUrl,
                            canAccessRecordPrintingWizard: false, // force disabled for now until we implement the page associated with this
                            listPrintingGlobalMutationConfigPage,
                        };
                    }),
            ),
    };

    return {
        queries: {
            printingSettings,
        },
    };
};
