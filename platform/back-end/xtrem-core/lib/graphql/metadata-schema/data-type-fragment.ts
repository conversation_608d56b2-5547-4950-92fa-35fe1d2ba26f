import { CustomMetrics } from '@sage/xtrem-metrics';
import {
    GraphQLBoolean,
    GraphQLFieldConfigArgumentMap,
    GraphQLFloat,
    GraphQLInt,
    GraphQLList,
    GraphQLNonNull,
    GraphQLObjectType,
    GraphQLString,
} from 'graphql';
import { runResolver } from '../../concurrency-utils';
import { Context } from '../../runtime';
import { MetadataSchemaFragment, SchemaFragmentFunction } from './metadata-schema-utils';

const fieldBindingType = new GraphQLObjectType({
    name: '_DataTypeFieldBinding',
    fields: {
        bind: { type: GraphQLString },
        title: { type: GraphQLString },
        type: { type: GraphQLString },
        enumType: { type: GraphQLString },
    },
});
// [{value:'first', title:'First value'}, {value: 'second', title:'Second value'}, {value:'third', title:'Third value'}, {value: 'fourth', title:'Fourth value'}]
const enumValuesType = new GraphQLObjectType({
    name: '_DataTypeEnumValues',
    fields: {
        value: { type: GraphQLString },
        title: { type: GraphQLString },
    },
});

export const dataTypeGQType = new GraphQLObjectType({
    name: '_DataType',
    fields: {
        name: { type: GraphQLString },
        type: { type: GraphQLString },
        title: { type: GraphQLString },
        node: { type: GraphQLString },
        value: { type: fieldBindingType },
        helperText: { type: fieldBindingType },
        imageField: { type: fieldBindingType },
        tunnelPage: { type: GraphQLString },
        tunnelPageId: { type: fieldBindingType },
        isDefault: { type: GraphQLBoolean },
        columns: { type: new GraphQLList(fieldBindingType) },
        precision: { type: GraphQLInt },
        scale: { type: GraphQLInt },
        roundingMode: { type: GraphQLString }, // RoundingMode,
        maxLength: { type: GraphQLFloat },
        isLocalized: { type: GraphQLBoolean },
        doNotTrim: { type: GraphQLBoolean },
        truncate: { type: GraphQLBoolean },
        values: { type: new GraphQLList(enumValuesType) },
        allowedContentTypes: { type: new GraphQLList(GraphQLString) },
    },
});

export const dataTypeFilter: GraphQLFieldConfigArgumentMap = {
    dataTypeNames: {
        type: new GraphQLList(new GraphQLNonNull(GraphQLString)),
    },
};

export const dataTypesGQType = new GraphQLList(dataTypeGQType);

export const resolveDataTypes = (context: Context, dataTypeNames: string[]): any => {
    const filteredDataTypes = [];
    const { dataTypes } = context.application;

    const localize = (localizationKey: string, name: string): string => context.localize(localizationKey, name);

    // PERFORMANCE: Use a for-of loop for effeciency and avoid creating an intermediate array
    // eslint-disable-next-line no-restricted-syntax
    for (const dataTypeName of dataTypeNames) {
        if (dataTypeName) {
            const dataType = dataTypes[dataTypeName];
            if (dataType) {
                filteredDataTypes.push(
                    dataType.getMetaData({
                        application: context.application,
                        localize,
                        context,
                    }),
                );
            } else {
                context.logger.warn(`Data type "${dataTypeName}" not found in application data types.`);
            }
        }
    }
    return filteredDataTypes.length > 0 ? filteredDataTypes : null;
};

export const getDataTypeSchemaFragment: SchemaFragmentFunction = (): MetadataSchemaFragment => {
    const getDataType = {
        type: dataTypesGQType,
        args: dataTypeFilter,
        resolve: (_: unknown, args: { dataTypeNames: string[] }, context: Context) =>
            CustomMetrics.metadata.withMetrics(
                {
                    nodeName: 'getDataType',
                    operationName: 'read',
                    operationKind: 'query',
                },
                () =>
                    runResolver(context, () => {
                        return resolveDataTypes(context, args.dataTypeNames);
                    }),
            ),
    };

    return {
        queries: {
            getDataType,
        },
    };
};
