import { asyncArray } from '@sage/xtrem-async-helper';
import { CustomMetrics } from '@sage/xtrem-metrics';
import {
    GraphQLBoolean,
    GraphQLFieldConfigArgumentMap,
    GraphQLInt,
    GraphQLList,
    GraphQLNonNull,
    GraphQLObjectType,
    GraphQLString,
} from 'graphql';
import * as _ from 'lodash';
import { runResolver } from '../../concurrency-utils';
import { TypeName } from '../../decorators';
import { PlainOperationDecorator } from '../../decorators/operation-decorators';
import { Context, NodeFactory } from '../../runtime';
import { CoreHooks } from '../../runtime/core-hooks';
import { ReferenceDataType } from '../../types/reference-data-type';
import { dataTypeGQType, resolveDataTypes } from './data-type-fragment';
import { MetadataSchemaFragment, SchemaFragmentFunction, expandNodeNames } from './metadata-schema-utils';
import { nodeDetailsFields } from './node-names-fragment';
import { NodeOperations } from './node-operations';

const nodeDetailsPropertyType = new GraphQLObjectType({
    name: '_NodeDetailsProperty',
    fields: () => ({
        name: { type: GraphQLString },
        enumType: { type: GraphQLString },
        title: { type: GraphQLString },
        canSort: { type: GraphQLBoolean },
        canFilter: { type: GraphQLBoolean },
        type: { type: GraphQLString },
        isCustom: { type: GraphQLBoolean },
        dataType: { type: GraphQLString },
        dataTypeDetails: {
            type: dataTypeGQType,
            resolve: (source: any, _args: any, context: Context) => {
                return resolveDataTypes(context, [source.dataType])?.[0];
            },
        },
        targetNode: { type: GraphQLString },
        targetNodeDetails: {
            type: nodeDetailsType,
            resolve: (source: any, _args: any, context: Context) => {
                const referenceComponents = source.targetNode?.split('/');
                const nodeName = referenceComponents?.[2];
                if (nodeName) {
                    return resolveNodeDetails(context, nodeName);
                }
                return null;
            },
        },
        isStored: { type: GraphQLBoolean },
        isOnInputType: { type: GraphQLBoolean },
        isOnOutputType: { type: GraphQLBoolean },
        isMutable: { type: GraphQLBoolean },
        isSystemProperty: { type: GraphQLBoolean },
    }),
});

const nodeDetailOperationParameterType = new GraphQLObjectType({
    name: '_NodeDetailsOperationParameter',
    fields: {
        name: { type: GraphQLString },
        title: { type: GraphQLString },
    },
});

const nodeDetailsOperationType = new GraphQLObjectType({
    name: '_NodeDetailsOperation',
    fields: {
        name: { type: GraphQLString },
        title: { type: GraphQLString },
        parameters: { type: new GraphQLList(nodeDetailOperationParameterType) },
    },
});

export const nodeDetailsType: GraphQLObjectType = new GraphQLObjectType({
    name: '_NodeDetails',
    fields: () => ({
        packageName: { type: GraphQLString },
        ...nodeDetailsFields,
        hasAttachments: { type: GraphQLBoolean },
        hasNotes: { type: GraphQLBoolean },
        hasTags: { type: GraphQLBoolean },
        defaultDataType: { type: GraphQLString },
        defaultDataTypeDetails: {
            type: dataTypeGQType,
            resolve: (source: any, _args: any, context: Context) => {
                return resolveDataTypes(context, [source.defaultDataType])?.[0];
            },
        },
        properties: {
            type: new GraphQLList(nodeDetailsPropertyType),
        },
        mutations: {
            type: new GraphQLList(nodeDetailsOperationType),
        },
        queries: {
            type: new GraphQLList(nodeDetailsOperationType),
        },
    }),
});

export const nodeDetailsFilter: GraphQLFieldConfigArgumentMap = {
    nodeName: {
        type: new GraphQLNonNull(GraphQLString),
    },
};

export const nodeDetailsListType = new GraphQLList(nodeDetailsType);

export interface NodeDetailsListArgs {
    missingNodeNames: string[];
    knownNodeNames: string[];
    depth: number | null;
}

export const nodeDetailsListArgs: GraphQLFieldConfigArgumentMap = {
    missingNodeNames: {
        type: new GraphQLList(new GraphQLNonNull(GraphQLString)),
    },
    knownNodeNames: {
        type: new GraphQLList(new GraphQLNonNull(GraphQLString)),
    },
    depth: {
        type: GraphQLInt,
    },
};

export const nodeCustomDetailsType: GraphQLObjectType = new GraphQLObjectType({
    name: '_NodeCustomDetails',
    fields: () => ({
        name: { type: GraphQLString },
        properties: {
            type: new GraphQLList(nodeDetailsPropertyType),
        },
    }),
});

export const nodeCustomDetailsListType = new GraphQLList(nodeCustomDetailsType);

const getNodeDetailsOperation = (
    context: Context,
    factory: NodeFactory,
    operation: PlainOperationDecorator,
    excludedParameters: string[] = [],
): any => {
    return {
        name: operation.name,
        title: NodeOperations.getLocalizedTitle(context, factory, operation),
        parameters: operation.parameters
            .filter(parameter => !excludedParameters.includes(parameter.name))
            .map(parameter => {
                return {
                    name: parameter.name,
                    title: NodeOperations.getLocalizedParameter(context, factory, operation, parameter),
                };
            }),
    };
};

export const resolveNodeDetails = (context: Context, nodeName: string): any => {
    const factory = context.application.factoriesManager.getFactoryByName(nodeName);
    const localize = (localizationKey: string, name: string): string => context.localize(localizationKey, name);

    const propertiesDetails = factory.properties
        .filter(prop => prop.isPublished && prop.name !== '_sortValue')
        .map(prop => {
            // Stored properties and getValue properties can be translated to SQL, therefore they can be filtered and sorted
            const enumType = prop.isEnumProperty() || prop.isEnumArrayProperty() ? prop.dataType.enumFullName() : null;
            const dataType =
                prop.dataType ??
                (prop.isForeignNodeProperty()
                    ? ReferenceDataType.getNodeDefaultDataType(context.application, prop.targetFactory)
                    : null);
            const isMutable = prop.isReferenceProperty()
                ? !!prop.isMutable || !!prop.isVitalParentInput
                : !!prop.isMutable;
            return {
                name: prop.name,
                title: prop.getLocalizedTitle(context),
                canSort: prop.canSort,
                canFilter: prop.canFilter,
                enumType,
                type: prop.type,
                isCustom: false,
                dataType: dataType?.name || '',
                dataTypeDetails: dataType
                    ? dataType.getMetaData({
                          application: context.application,
                          localize,
                          context,
                      })
                    : null,
                targetNode: prop.isForeignNodeProperty() ? prop.targetFactory.fullName : '',
                targetNodeDetails: null,
                isStored: prop.isStored,
                isOnInputType: prop.isOnInputType,
                isOnOutputType: prop.isOnOutputType,
                isMutable,
                isSystemProperty: prop.isSystemProperty,
            };
        });

    const mutationsLocalization = factory.mutations
        .filter(
            mutation =>
                mutation.isPublished &&
                (mutation.operationKind === 'mutation' || mutation.operationKind === 'asyncMutation'),
        )
        .map(mutation => {
            return getNodeDetailsOperation(context, factory, mutation, ['trackingId', 'reason']);
        });

    const queriesLocalization = factory.queries
        .filter(query => query.isPublished && query.operationKind === 'query')
        .map(query => {
            return getNodeDetailsOperation(context, factory, query);
        });

    const defaultDataType = ReferenceDataType.getNodeDefaultDataType(context.application, factory);

    return {
        packageName: factory.package.name,
        name: nodeName,
        title: factory.getLocalizedTitle(context),
        hasAttachments: factory.hasAttachments,
        hasNotes: factory.hasNotes,
        hasTags: factory.hasTags,
        defaultDataType: defaultDataType?.name || '',
        defaultDataTypeDetails: defaultDataType
            ? defaultDataType.getMetaData({
                  application: context.application,
                  localize,
                  context,
              })
            : null,
        properties: propertiesDetails,
        mutations: mutationsLocalization,
        queries: queriesLocalization,
    };
};

export const resolveNodeCustomDetails = async (context: Context, nodeName: string): Promise<any> => {
    const factory = context.application.factoriesManager.getFactoryByName(nodeName);
    const properties = [] as any[];
    if (factory.isCustomizable) {
        const customFieldsDict = await CoreHooks.customizationManager.getCustomFields(
            context,
            factory.fullName ? [factory.fullName] : [],
        );
        if (customFieldsDict) {
            Object.keys(customFieldsDict).forEach(key => {
                customFieldsDict[key].forEach(customField => {
                    properties.push({
                        name: customField.name,
                        title: customField.title,
                        canSort: true,
                        canFilter: true,
                        enumType: '',
                        type: customField.dataType as unknown as TypeName, // How to transform enum type values from string type ?
                        isCustom: true,
                        dataType: '',
                        dataTypeDetails: null,
                        targetNode: '',
                        targetNodeDetails: null,
                        isStored: true,
                        isOnInputType: true,
                        isOnOutputType: true,
                        isMutable: false,
                        isSystemProperty: false,
                    });
                });
            });
        }
    }
    return {
        name: nodeName,
        properties,
    };
};

export const resolveNodeDetailsList = (context: Context, args: NodeDetailsListArgs): Promise<any[]> => {
    const missingNodeNames = expandNodeNames(context.application, args.missingNodeNames, args.depth);
    const nodeNames = _.difference(missingNodeNames, args.knownNodeNames);
    return asyncArray(nodeNames)
        .map(nodeName => resolveNodeDetails(context, nodeName))
        .toArray();
};

export const resolveNodeCustomDetailsList = (context: Context, args: NodeDetailsListArgs): Promise<any[]> => {
    const missingNodeNames = expandNodeNames(context.application, args.missingNodeNames, args.depth);
    const nodeNames = _.difference(missingNodeNames, args.knownNodeNames);
    return asyncArray(nodeNames)
        .map(nodeName => resolveNodeCustomDetails(context, nodeName))
        .filter(details => details.properties.length > 0)
        .toArray();
};

export const getNodeDetailsSchemaFragment: SchemaFragmentFunction = (): MetadataSchemaFragment => {
    const getNodeDetails = {
        type: nodeDetailsType,
        args: nodeDetailsFilter,
        resolve: (_source: unknown, args: { nodeName: string }, context: Context) =>
            CustomMetrics.metadata.withMetrics(
                {
                    nodeName: 'getNodeDetails',
                    operationName: 'read',
                    operationKind: 'query',
                },
                () =>
                    runResolver(context, () => {
                        return resolveNodeDetails(context, args.nodeName);
                    }),
            ),
    };

    const getNodeCustomDetails = {
        type: nodeCustomDetailsType,
        args: nodeDetailsFilter,
        resolve: (_source: unknown, args: { nodeName: string }, context: Context) =>
            CustomMetrics.metadata.withMetrics(
                {
                    nodeName: 'getNodeCustomDetails',
                    operationName: 'read',
                    operationKind: 'query',
                },
                () =>
                    runResolver(context, () => {
                        return resolveNodeCustomDetails(context, args.nodeName);
                    }),
            ),
    };

    const getNodeDetailsList = {
        type: nodeDetailsListType,
        args: nodeDetailsListArgs,
        resolve: (_source: unknown, args: NodeDetailsListArgs, context: Context) =>
            CustomMetrics.metadata.withMetrics(
                {
                    nodeName: 'getNodeDetailsList',
                    operationName: 'read',
                    operationKind: 'query',
                },
                () =>
                    runResolver(context, () => {
                        return resolveNodeDetailsList(context, args);
                    }),
            ),
    };

    const getNodeCustomDetailsList = {
        type: nodeCustomDetailsListType,
        args: nodeDetailsListArgs,
        resolve: (_source: unknown, args: NodeDetailsListArgs, context: Context) =>
            CustomMetrics.metadata.withMetrics(
                {
                    nodeName: 'getNodeCustomDetailsList',
                    operationName: 'read',
                    operationKind: 'query',
                },
                () =>
                    runResolver(context, () => {
                        return resolveNodeCustomDetailsList(context, args);
                    }),
            ),
    };

    return {
        queries: {
            getNodeDetails,
            getNodeCustomDetails,
            getNodeDetailsList,
            getNodeCustomDetailsList,
        },
    };
};
