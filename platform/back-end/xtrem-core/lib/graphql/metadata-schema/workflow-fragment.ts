import type { WorkflowNode } from '@sage/xtrem-shared';
import { GraphQLEnumType, GraphQLList, GraphQLObjectType, GraphQLString } from 'graphql';
import { runResolver } from '../../concurrency-utils';
import type { Context } from '../../runtime';
import { MetadataSchemaFragment, SchemaFragmentFunction } from './metadata-schema-utils';

const workflowNodeTypeType = new GraphQLEnumType({
    name: '_WorkflowNodeType',
    values: {
        action: { value: 'action' },
        event: { value: 'event' },
        condition: { value: 'condition' },
    },
});

const workflowNodeType = new GraphQLObjectType<WorkflowNode>({
    name: '_WorkflowNode',
    fields: {
        key: { type: GraphQLString },
        title: { type: GraphQLString },
        description: { type: GraphQLString },
        color: { type: GraphQLString },
        icon: { type: GraphQLString },
        configurationPage: { type: Graph<PERSON>String },
        defaultConfig: { type: Graph<PERSON>String },
        type: { type: workflowNodeTypeType },
    },
});

const workflowNodeListType = new GraphQLList(workflowNodeType);

const workflowRootType = new GraphQLObjectType({
    name: '_WorkflowRoot',
    fields: {
        getAvailableWorkflowNodes: {
            type: workflowNodeListType,
            resolve: (_: unknown, __: unknown, context: Context) =>
                runResolver<WorkflowNode[]>(context, async (): Promise<WorkflowNode[]> => {
                    const descriptors = await context.application.workflowManager.getWorkflowStepDescriptors(context);
                    return descriptors.map(stepDescriptor => {
                        const { type, key, title, description } = stepDescriptor;
                        const defaultConfig = JSON.stringify(stepDescriptor.defaultConfig ?? {});
                        return {
                            type,
                            key,
                            title,
                            description,
                            ...stepDescriptor.ui,
                            defaultConfig,
                        };
                    });
                }),
        },
    },
});

export const getWorkflowSchemaFragment: SchemaFragmentFunction = (): MetadataSchemaFragment => {
    const workflow = {
        type: workflowRootType,
        resolve: () => ({}),
    };

    return {
        queries: {
            workflow,
        },
    };
};
