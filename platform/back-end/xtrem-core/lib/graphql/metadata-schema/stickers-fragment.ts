import { GraphQLList, GraphQLObjectType, GraphQLString } from 'graphql';
import { Application } from '../../application/application';
import { runResolver } from '../../concurrency-utils';
import { Context } from '../../runtime';
import { ApplicationArtifact, ClientArtifactType } from '../../system/pages/client-service';
import {
    MetadataSchemaFragment,
    SchemaFragmentFunction,
    artifactArgsWithFilter,
    getArtifactStringsConfig,
    getArtifactTitleConfig,
    resolveArtifact,
} from './metadata-schema-utils';

const stickerType = new GraphQLObjectType({
    name: '_Sticker',
    fields: {
        key: { type: GraphQLString },
        content: {
            type: GraphQLString,
            resolve: (artifact: ApplicationArtifact, _args: any, context: Context) =>
                runResolver(context, () => {
                    return artifact ? artifact.content.toString() : '';
                }),
        },
        title: getArtifactTitleConfig(ClientArtifactType.stickers),
        strings: getArtifactStringsConfig(ClientArtifactType.stickers),
    },
});

const stickersType = new GraphQLList(stickerType);

export const getStickersSchemaFragment: SchemaFragmentFunction = (application: Application): MetadataSchemaFragment => {
    const stickers = {
        type: stickersType,
        args: artifactArgsWithFilter,
        resolve: resolveArtifact(application, ClientArtifactType.stickers),
    };

    return {
        queries: {
            stickers,
        },
    };
};
