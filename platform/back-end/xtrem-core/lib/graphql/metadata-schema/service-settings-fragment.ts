import { GraphQLBoolean, GraphQLObjectType, GraphQLString } from 'graphql';
import { runResolver } from '../../concurrency-utils';
import { Context } from '../../runtime';
import { CopilotClient } from '../../security/copilot-client';
import { MetadataSchemaFragment, SchemaFragmentFunction } from './metadata-schema-utils';

const copilotSetting = new GraphQLObjectType({
    name: '_MetaServiveSetting',
    fields: {
        token: { type: GraphQLString },
        expiration: { type: GraphQLString },
        enabled: { type: GraphQLBoolean },
    },
});

export const serviceSettingsRootType = new GraphQLObjectType({
    name: '_ServiceSettingsRoot',
    fields: {
        copilot: {
            type: copilotSetting,
            resolve: (_: unknown, _args: any, context: Context) =>
                runResolver(context, () =>
                    context.application.requestFunnel(() =>
                        context.withChildContext(
                            async ctx => {
                                const enabled = CopilotClient.isEnabled();
                                if (!enabled) {
                                    return { token: '', enabled };
                                }
                                const token = await CopilotClient.getToken(ctx);
                                return { token: token.token, expiration: token.expiration, enabled };
                            },
                            { isolationLevel: 'low' },
                        ),
                    ),
                ),
        },
    },
});

export const getServiceSettingsSchemaFragment: SchemaFragmentFunction = (): MetadataSchemaFragment => {
    const serviceSettings = {
        type: serviceSettingsRootType,
        resolve: () => ({}),
    };

    return {
        queries: {
            serviceSettings,
        },
    };
};
