import { Notification, PromisifyProperties } from '@sage/xtrem-shared';
import {
    GraphQLBoolean,
    GraphQLEnumType,
    GraphQLFieldConfig,
    GraphQLList,
    GraphQLNonNull,
    GraphQLObjectType,
    GraphQLString,
} from 'graphql';
import { runResolver } from '../../concurrency-utils';
import { Context } from '../../runtime';
import { MetadataSchemaFragment, SchemaFragmentFunction } from './metadata-schema-utils';

const notificationActionType = new GraphQLObjectType({
    name: '_NotificationAction',
    fields: {
        _id: { type: GraphQLString },
        title: { type: GraphQLString },
        link: { type: GraphQLString },
        style: {
            type: new GraphQLEnumType({
                name: '_NotificationActionStyle',
                values: {
                    primary: { value: 'primary' },
                    secondary: { value: 'secondary' },
                    tertiary: { value: 'tertiary' },
                    link: { value: 'link' },
                },
            }),
        },
    },
});

const notificationActionListType = new GraphQLList(notificationActionType);

const notificationType = new GraphQLObjectType({
    name: '_Notification',
    fields: {
        _id: { type: GraphQLString },
        _createStamp: { type: GraphQLString },
        level: {
            type: new GraphQLEnumType({
                name: '_NotificationLevel',
                values: {
                    error: { value: 'error' },
                    warning: { value: 'warning' },
                    info: { value: 'info' },
                    success: { value: 'success' },
                    approval: { value: 'approval' },
                },
            }),
        },
        shouldDisplayToast: { type: GraphQLBoolean },
        isRead: { type: GraphQLBoolean },
        icon: { type: GraphQLString },
        title: { type: GraphQLString },
        description: { type: GraphQLString },
        actions: { type: notificationActionListType },
    },
});

const notificationListType = new GraphQLList(notificationType);

const markReadMutation: GraphQLFieldConfig<unknown, any, Context> = {
    type: GraphQLBoolean,
    args: {
        _id: { type: new GraphQLNonNull(GraphQLString) },
    },
    resolve: (_: unknown, args: any, context: Context) =>
        runResolver<Promise<boolean>>(context, () =>
            context.application.requestFunnel(() =>
                context.withChildContext(ctx => context.application.notificationManager.markRead(ctx, args._id), {
                    isolationLevel: 'low',
                }),
            ),
        ),
};

const markUnreadMutation: GraphQLFieldConfig<unknown, any, Context> = {
    type: GraphQLBoolean,
    args: {
        _id: { type: new GraphQLNonNull(GraphQLString) },
    },
    resolve: (_: unknown, args: any, context: Context) =>
        runResolver<Promise<boolean>>(context, () =>
            context.application.requestFunnel(() =>
                context.withChildContext(ctx => context.application.notificationManager.markUnread(ctx, args._id), {
                    isolationLevel: 'low',
                }),
            ),
        ),
};

const deleteMutation: GraphQLFieldConfig<unknown, any, Context> = {
    type: GraphQLBoolean,
    args: {
        _id: { type: new GraphQLNonNull(GraphQLString) },
    },
    resolve: (_: unknown, args: any, context: Context) =>
        runResolver<Promise<boolean>>(context, () =>
            context.application.requestFunnel(() =>
                context.withChildContext(ctx => context.application.notificationManager.delete(ctx, args._id), {
                    isolationLevel: 'low',
                }),
            ),
        ),
};

const markAllReadMutation: GraphQLFieldConfig<unknown, any, Context> = {
    type: GraphQLBoolean,
    resolve: (_: unknown, args: any, context: Context) =>
        runResolver<Promise<boolean>>(context, () =>
            context.application.requestFunnel(() =>
                context.withChildContext(ctx => context.application.notificationManager.markAllRead(ctx), {
                    isolationLevel: 'low',
                }),
            ),
        ),
};

const notificationMutationType = new GraphQLObjectType({
    name: '_NotificationsMutation',
    fields: {
        markRead: markReadMutation,
        markUnread: markUnreadMutation,
        markAllRead: markAllReadMutation,
        delete: deleteMutation,
    },
});

export const getNotificationSchemaFragment: SchemaFragmentFunction = (): MetadataSchemaFragment => {
    const notifications = {
        type: notificationListType,
        resolve: (_: unknown, args: any, context: Context) =>
            runResolver<Promise<Array<PromisifyProperties<Notification>>>>(context, () =>
                context.application.requestFunnel(() =>
                    context.application.notificationManager.getUserNotifications(context),
                ),
            ),
    };

    const notificationsMutation = {
        type: notificationMutationType,
        resolve: () => ({}),
    };

    return {
        queries: {
            notifications,
        },
        mutations: {
            notifications: notificationsMutation,
        },
    };
};
