import { CustomMetrics } from '@sage/xtrem-metrics';
import { GraphQLList, GraphQLObjectType, GraphQLString } from 'graphql';
import { Application, Dependency } from '../../application/application';
import { runResolver } from '../../concurrency-utils';
import { Context } from '../../runtime';
import { MetadataSchemaFragment, SchemaFragmentFunction } from './metadata-schema-utils';

const installedPackageType = new GraphQLObjectType({
    name: '_InstalledApplicationPackage',
    fields: {
        name: { type: GraphQLString },
        version: { type: GraphQLString },
    },
});

const installedPackagesType = new GraphQLList(installedPackageType);
let installedPackageCache: Dependency[];

export const getInstalledPackagesSchemaFragment: SchemaFragmentFunction = (
    application: Application,
): MetadataSchemaFragment => {
    const installedPackages = {
        type: installedPackagesType,
        resolve: (_: unknown, __: unknown, context: Context) =>
            CustomMetrics.metadata.withMetrics(
                {
                    nodeName: 'installedPackages',
                    operationName: 'read',
                    operationKind: 'query',
                },
                () =>
                    runResolver(context, () => {
                        if (installedPackageCache) {
                            return installedPackageCache;
                        }
                        const pluginDependencies = application.getPluginDependencies();
                        installedPackageCache = [
                            ...application.getPlatformPackages(),
                            ...Object.keys(pluginDependencies).map(name => {
                                return { name, version: pluginDependencies[name] };
                            }),
                            ...application.getPackages().map(p => ({ name: p.name, version: p.packageJson.version })),
                        ];
                        return installedPackageCache;
                    }),
            ),
    };

    return {
        queries: {
            installedPackages,
        },
    };
};
