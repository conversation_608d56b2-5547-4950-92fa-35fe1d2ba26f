import {
    GraphQLBoolean,
    GraphQLFieldConfigArgumentMap,
    GraphQLInputObjectType,
    GraphQLList,
    GraphQLObjectType,
    GraphQLString,
} from 'graphql';
import { Application } from '../../application/application';
import { runResolver } from '../../concurrency-utils';
import { Context } from '../../runtime';
import { ArtifactFilter } from '../../system/artifact-manager';
import { ApplicationArtifact, ClientArtifactType, MetaPackArtifact } from '../../system/pages/client-service';
import {
    MetadataSchemaFragment,
    SchemaFragmentFunction,
    getArtifactDescriptionConfig,
    getArtifactStringsConfig,
    getArtifactTitleConfig,
    resolveArtifact,
} from './metadata-schema-utils';

export const artifactArgsWithFilterWithGroup: GraphQLFieldConfigArgumentMap = {
    filter: {
        type: new GraphQLInputObjectType({
            name: '_ArtifactFilterWithGroup',
            fields: {
                packageOrPage: { type: GraphQLString },
                group: { type: GraphQLString },
                exactMatch: { type: GraphQLBoolean, defaultValue: false },
            },
        }),
    },
};

const widgetType = new GraphQLObjectType({
    name: '_WidgetType',
    fields: {
        key: { type: GraphQLString },
        content: {
            type: GraphQLString,
            resolve: (artifact: ApplicationArtifact, _args: any, context: Context) =>
                runResolver(context, () => {
                    return artifact ? artifact.content.toString() : '';
                }),
        },
        category: { type: GraphQLString },
        categoryLabel: { type: GraphQLString },
        group: { type: GraphQLString },
        type: { type: GraphQLString },
        title: getArtifactTitleConfig(ClientArtifactType.widgets),
        description: getArtifactDescriptionConfig(ClientArtifactType.widgets),
        strings: getArtifactStringsConfig(ClientArtifactType.widgets),
    },
});

const widgetsType = new GraphQLList(widgetType);

export const getWidgetsSchemaFragment: SchemaFragmentFunction = (application: Application): MetadataSchemaFragment => {
    const widgets = {
        type: widgetsType,
        args: artifactArgsWithFilterWithGroup,
        resolve: (obj: unknown, args: { filter: ArtifactFilter & { group?: string } }, context: Context) =>
            resolveArtifact(application, ClientArtifactType.widgets)(obj, args, context).then(
                (resolvedWidgets: MetaPackArtifact[]) => {
                    if (args.filter?.group) {
                        const normalizedGroupFilter = args.filter?.group ?? 'home';
                        return resolvedWidgets.filter((widget): boolean => {
                            const normalizedWidgetGroup = widget.group ?? 'home';
                            return normalizedWidgetGroup === normalizedGroupFilter;
                        });
                    }
                    return resolvedWidgets;
                },
            ),
    };

    return {
        queries: {
            widgets,
        },
    };
};
