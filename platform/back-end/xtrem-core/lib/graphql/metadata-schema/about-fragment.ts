import { CustomMetrics } from '@sage/xtrem-metrics';
import { GraphQLObjectType, GraphQLString } from 'graphql';
import { Application } from '../../application/application';
import { runResolver } from '../../concurrency-utils';
import { Context } from '../../runtime';
import { MetadataSchemaFragment, SchemaFragmentFunction } from './metadata-schema-utils';

const aboutPackageType = new GraphQLObjectType({
    name: '_AboutPackage',
    fields: {
        name: { type: GraphQLString },
        version: { type: GraphQLString },
        author: { type: GraphQLString },
        tenantId: { type: GraphQLString },
        description: { type: GraphQLString },
        license: { type: GraphQLString },
        buildStamp: { type: GraphQLString },
    },
});

const aboutType = new GraphQLObjectType({
    name: '_About',
    fields: {
        root: { type: aboutPackageType },
        application: { type: aboutPackageType },
    },
});

export const getAboutSchemaFragment: SchemaFragmentFunction = (application: Application): MetadataSchemaFragment => {
    const about = {
        type: aboutType,
        resolve: (_: unknown, __: unknown, context: Context) =>
            CustomMetrics.metadata.withMetrics(
                {
                    nodeName: 'about',
                    operationName: 'read',
                    operationKind: 'query',
                },
                () =>
                    runResolver(context, () => ({
                        application: application.about,
                        root: { ...application.rootAbout, tenantId: context.tenantId },
                    })),
            ),
    };

    return {
        queries: {
            about,
        },
    };
};
