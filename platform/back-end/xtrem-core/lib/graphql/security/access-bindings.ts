import { AccessStatus } from '@sage/xtrem-shared';

export interface AccessBinding {
    node?: string;
    bind?: string;
}

/**
 * Interface representing the access of node property or operation
 */
export interface NodeAccessBindingStatus {
    /**
     * name of the operation or property
     */
    name: string;
    /**
     * access status of the  operation or property
     */
    status: AccessStatus;
}
