import { asyncArray } from '@sage/xtrem-async-helper';
import { AnyValue } from '@sage/xtrem-shared';
import { GraphQLList, GraphQLType } from 'graphql';
import { Context } from '../../runtime';
import { TypeCache } from '../utils/type-cache';
import { GraphQlTypingMode } from './node-type';
import { OperationType } from './operation-type';
import { GraphQlFactory, GraphQlProperty, PropertyType } from './property-type';

export class ArrayType {
    static makeArrayType(
        typeCache: TypeCache,
        factory: GraphQlFactory,
        property: GraphQlProperty,
        typingMode: GraphQlTypingMode,
    ): GraphQLType {
        if (!(property as any).item)
            throw new Error(`${factory.fullName}.${property.name}: 'item' attribute missing in array property`);
        const item = OperationType.makeParameterProperty(
            typeCache,
            factory.name,
            (property as any).item,
            property.name,
            typingMode,
        );
        const targetType = PropertyType.makePropertyType(typeCache, factory, item, typingMode);
        return new GraphQLList(targetType);
    }

    static getArrayInputValue(
        context: Context,
        val: AnyValue[],
        factory: GraphQlFactory,
        property: GraphQlProperty,
    ): Promise<AnyValue[]> {
        return asyncArray(val)
            .map(v => {
                if (v == null) return v;
                return PropertyType.getInputValue(context, v, factory, property.item!);
            })
            .toArray();
    }
}
