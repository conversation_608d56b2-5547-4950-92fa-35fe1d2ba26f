import { Any<PERSON><PERSON><PERSON>, AnyValue, asyncArray } from '@sage/xtrem-async-helper';
import { AsyncResponse } from '@sage/xtrem-shared';
import { GraphQLType } from 'graphql';
import { Context } from '../../runtime';
import { TypeCache } from '../utils/type-cache';
import { ArrayType } from './array-type';
import { GraphQlTypingMode, NodeType } from './node-type';
import { OperationType } from './operation-type';
import { GraphQlFactory, GraphQlProperty, PropertyType } from './property-type';

export class ObjectType {
    static makeObjectType(
        typeCache: TypeCache,
        factory: GraphQlFactory,
        property: GraphQlProperty,
        typingMode: GraphQlTypingMode,
    ): GraphQLType {
        const targetFactory = OperationType.makeGraphQlFactory(typeCache, factory.name, property, typingMode);
        return NodeType.isInput(typingMode)
            ? NodeType.makeInputType(typeCache, targetFactory, typingMode)
            : NodeType.makeOutputType(typeCache, targetFactory, typingMode);
    }

    // small override of PropertyType.getInputValue, to preserve null and handle arrays
    // TODO: eliminate this and preserve null in PropertyType.getInputValue.
    static getInputValue(
        context: Context,
        val: AnyValue,
        factory: GraphQlFactory,
        property: GraphQlProperty,
    ): AsyncResponse<AnyValue> {
        if (val == null) return val;
        if (property.type === 'array') return ArrayType.getArrayInputValue(context, val as any[], factory, property);

        return PropertyType.getInputValue(context, val, factory, property);
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    static getObjectInputValue(
        context: Context,
        val: AnyValue,
        factory: GraphQlFactory,
    ): AsyncResponse<AnyRecord | null> {
        if (val == null) return val as null;
        return asyncArray(factory.properties).reduce(async (result, prop) => {
            const propVal = (val as AnyRecord)[prop.name];
            if (propVal == null) {
                if (propVal === null)
                    result[prop.name] = await PropertyType.getInputValue(context, propVal, factory, prop);
            } else {
                if (prop.name === '_id' && +propVal < 0) {
                    context.adjustLastExternalId(+propVal);
                }
                result[prop.name] = await ObjectType.getInputValue(context, propVal, factory, prop);
            }
            return result;
        }, {} as AnyRecord);
    }
}
