import { GraphQLList, GraphQLObjectType, GraphQLResolveInfo, GraphQLString } from 'graphql';
import { runResolver } from '../../concurrency-utils';
import { StateAccess } from '../../node-state/state-access';
import { Context } from '../../runtime';
import { Node } from '../../ts-api';
import { NodeAccessBindingStatus } from '../security/access-bindings';
import { NodeType } from './node-type';
import { GraphQlFactory } from './property-type';

/** @internal _access output type */
export const accessBindingType = new GraphQLList(
    new GraphQLObjectType({
        name: '_OutputAccessBinding',
        fields: {
            name: {
                type: GraphQLString,
            },
            status: {
                type: GraphQLString,
            },
        },
    }),
);

/**
 * _access field type
 * @param factory
 * @returns
 * @internal
 */
export function getAccessField(factory: GraphQlFactory): {
    description: string;
    type: GraphQLList<GraphQLObjectType<any, any>>;
    args: {};
    resolve: (
        obj: Node,
        _args: unknown,
        _rootContext: Context,
        ast: GraphQLResolveInfo,
    ) => Promise<NodeAccessBindingStatus[]>;
} {
    return {
        description: `${factory.name}: node access`,
        type: accessBindingType,
        args: {},
        resolve: (
            obj: Node,
            _args: unknown,
            _rootContext: Context,
            ast: GraphQLResolveInfo,
        ): Promise<NodeAccessBindingStatus[]> => {
            // The _access property is special, it is resolved as the current users node operation/property access that the user
            // is not authorized to.
            return runResolver(obj.$.context, () => {
                const selectedPropertyNames = NodeType.getSelectedProperties('_access', ast);
                return StateAccess.getNodeAccess(obj.$.state, { selectedPropertyNames, isPublished: true });
            });
        },
    };
}
