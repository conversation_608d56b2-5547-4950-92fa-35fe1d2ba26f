import { schemaTypeName } from '@sage/xtrem-shared';
import { GraphQLEnumType, GraphQLScalarType } from 'graphql';
import { EnumDataType } from '../../types';
import { TypeCache } from '../utils/type-cache';
import { makeCustomEnumInputType, makeCustomEnumOutputType } from './basic-types';

/**
 * @internal
 *
 * Static class to generate GraphQL type for a node property
 */
export class EnumType {
    /**
     * Get graphl type name from enum datatype
     * @param enumDataType
     * @param isInput
     * @returns
     */
    static getTypeName(enumDataType: EnumDataType, isInput: boolean): string {
        return `${schemaTypeName(enumDataType.enumFullName())}${isInput ? '_EnumInput' : ''}`;
    }

    /**
     * Defines a enum graphql type based on an enum datatype
     * @param typeCache
     * @param enumDataType
     * @param isInput
     * @returns
     */
    static makeGraphQLEnumType(
        typeCache: TypeCache,
        enumDataType: EnumDataType,
        isInput: boolean,
    ): GraphQLEnumType | GraphQLScalarType {
        const name = `${schemaTypeName(enumDataType.enumFullName())}${isInput ? '_EnumInput' : ''}`;
        return typeCache.internType(name, () =>
            isInput ? makeCustomEnumInputType(name, enumDataType) : makeCustomEnumOutputType(name, enumDataType),
        );
    }
}
