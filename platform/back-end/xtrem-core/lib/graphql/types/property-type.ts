import { AnyR<PERSON>ord, asyncArray, AsyncResponse } from '@sage/xtrem-async-helper';
import { DateRange, Datetime, DatetimeRange, DateValue, DecimalRange, IntegerRange, Time } from '@sage/xtrem-date-time';
import { Decimal } from '@sage/xtrem-decimal';
import {
    AnyValue,
    ColumnTypeName,
    DataInputError,
    Dict,
    LocalizeLocale,
    LogicError,
    schemaTypeName,
} from '@sage/xtrem-shared';
import {
    GraphQLBoolean,
    GraphQLFieldConfigMap,
    GraphQLFloat,
    GraphQLInputType,
    GraphQLInt,
    GraphQLInterfaceType,
    GraphQLList,
    GraphQLObjectType,
    GraphQLString,
    GraphQLType,
} from 'graphql';
import { camelCase } from 'lodash';
import { NodeStorage, ParameterTypeName, TypeName } from '../../decorators';
import { CollectionProperty, foreignNodePropertyTypes, Property, ReferenceProperty } from '../../properties';
import { Context, NodeFactory } from '../../runtime';
import { loggers } from '../../runtime/loggers';
import { Node } from '../../ts-api';
import { BinaryStream, DataType, EnumDataType, safeParseInt, TextStream, Uuid } from '../../types';
import { AggregateQuery } from '../aggregates/aggregate-query';
import { AggregateRead } from '../aggregates/aggregate-read';
import { CollectionQuery } from '../queries/collection-query';
import { TypeCache } from '../utils/type-cache';
import { ArrayType } from './array-type';
import {
    binaryStreamOutputType,
    // booleanInputType,
    dateRangeType,
    datetimeRangeType,
    datetimeType,
    dateType,
    decimalRangeType,
    decimalType,
    externalReferenceInputType,
    floatInputType,
    idInputType,
    integerInputType,
    integerRangeType,
    jsonType,
    makeCustomEnumInputType,
    makeCustomEnumOutputType,
    referenceInputType,
    streamInputType,
    textStreamOutputType,
} from './basic-types';
import { EnumType } from './enum-type';
import { GraphQlTypingMode, NodeType } from './node-type';
import { ObjectType } from './object-type';
import { OperationType } from './operation-type';

const logger = loggers.graphQl;

/** @internal */
export type DiscardNameAndType<T> = { [K in Exclude<Exclude<keyof T, 'name'>, 'type'>]?: T[K] };

// Minimal interface for properties and mutation/query parameters and returns.
/** @internal */
export interface GraphQlProperty {
    name: string;
    type: TypeName | ParameterTypeName;
    dataType?: DataType<AnyValue, Node>;
    targetFactory?: GraphQlFactory;
    item?: GraphQlProperty;
    getValue?: () => AsyncResponse<AnyValue>;
    computeValue?: () => AsyncResponse<AnyValue>;
    columnType?: ColumnTypeName;
    isVital?: boolean;
    isMutable?: boolean;
    isPublished?: boolean;
    isMandatory?: boolean;
    isNullable?: boolean;
    isInputOnly: boolean;
    isOnInputType: boolean;
    isOutputOnly: boolean;
    isTransientInput?: boolean;
    isWritable?: boolean;
    isVitalParent?: boolean;
    isVitalParentInput?: boolean;
}

// Minimal interface for node factory and mutation/query object parameters
/** @internal */
export interface GraphQlFactory {
    name: string;
    fullName: string;
    storage?: NodeStorage;
    getNodeSchemaDescription: () => string;
    properties: GraphQlProperty[];
    publishedInputSystemProperties: string[];
    publishedSystemProperties: Dict<Property>;
    baseFactory?: GraphQlFactory;
    subFactories: GraphQlFactory[];
    impactingServiceOptions: string[];
    naturalKey: string[] | undefined;
    isAbstract?: boolean;
    isVitalReferenceChild?: boolean;
}

/**
 * @internal
 *
 * Static class to generate GraphQL type for a node property
 */
export class PropertyType {
    /**
     * Parses the `value` from a GraphQL query to the corresponding format for `property`
     * @param context the context
     * @param val The raw value for the property
     * @param property The property we want to parse the value for
     */
    static async getInputValue(
        context: Context,
        val: AnyValue,
        factory: GraphQlFactory,
        property: GraphQlProperty,
        options?: { type: TypeName | ParameterTypeName },
    ): Promise<AnyValue> {
        let type = options?.type || property.type;

        // val can be null or undefined if the input value being resolved is optional
        if (val == null) {
            if (val === undefined) return undefined;
            switch (type) {
                case 'string':
                    return '';
                case 'stringArray':
                    return [];
                case 'textStream':
                    return new TextStream('');
                default:
                    return val;
            }
        }

        const getArrayInputType = (dataType: TypeName | ParameterTypeName): Promise<AnyValue> => {
            type = dataType;

            if (Array.isArray(val)) {
                return asyncArray(val)
                    .map(v => PropertyType.getInputValue(context, v, factory, property, { type: dataType }))
                    .toArray();
            }

            // if the val is not an array then it is a single/scalar value and should be processed as the passed data type
            // arrays will not be passed as a string so this value should be valid for the array type.
            // The type that requires this the most is enumArray, as if the value is passed as a string we need to resolve it
            // to the number value.
            return PropertyType.getInputValue(context, val, factory, property, { type: dataType });
        };

        // TODO: type testing + parsing should be eliminated here as we now have custom scalar types.
        // but let us play it safe for now as we'd need more tests to validate all.
        try {
            switch (type) {
                case 'boolean':
                    return typeof val === 'boolean' ? !!val : val === 'true';
                case 'enumArray':
                    return await getArrayInputType('enum');
                case 'enum':
                case 'string':
                    return typeof val === 'string' ? val : val.toString();
                case 'integerArray':
                    return await getArrayInputType('integer');
                case 'stringArray':
                    return await getArrayInputType('string');
                case 'short':
                case 'integer':
                    if (property.name === '_id' && typeof val === 'string' && val[0] === '#') {
                        // Do not use tryRead here, it will remove _id from the filter and we could end up with an empty filter
                        // Rather let this throw and let the mutation or query resolver manager it.
                        return (await context.read((factory as NodeFactory).nodeConstructor, { _id: val }))._id;
                    }
                    return typeof val === 'number' ? val : safeParseInt(val, property.name);
                case 'float':
                case 'double':
                    return typeof val === 'number' ? val : parseFloat(val as string);
                case 'decimal':
                    return Decimal.isDecimal(val) ? val : Decimal.make(val as string);

                case 'date':
                    return DateValue.isDate(val)
                        ? val
                        : DateValue.parse((val as string).substring(0, 10), context.currentLocale as LocalizeLocale);
                case 'datetime':
                    return Datetime.isDatetime(val)
                        ? val
                        : Datetime.parse(val as string, context.currentLocale as LocalizeLocale);
                case 'time':
                    return Time.isTime(val)
                        ? val
                        : Time.parse((val as string).substring(0, 8), context.currentLocale as LocalizeLocale);

                case 'integerRange':
                    return IntegerRange.isIntegerRange(val) ? val : IntegerRange.parse(val as string);
                case 'decimalRange':
                    return DecimalRange.isDecimalRange(val) ? val : DecimalRange.parse(val as string);
                case 'dateRange':
                    return DateRange.isDateRange(val) ? val : DateRange.parse(val as string);
                case 'datetimeRange':
                    return DatetimeRange.isDatetimeRange(val) ? val : DatetimeRange.parse(val as string);

                case 'textStream':
                    return TextStream.fromString((val as { value: string }).value);
                case 'binaryStream':
                    return BinaryStream.fromBuffer(Buffer.from((val as { value: string }).value, 'base64'));
                case 'uuid':
                    return Uuid.fromString(val as string);

                case 'instance': {
                    const targetFactory = (property as ReferenceProperty).targetFactory;
                    if (!targetFactory) throw new Error(`${property.name}: missing class`);
                    return await NodeType.nodeIn(context, val as AnyRecord, targetFactory, {
                        isTransientInput: property.isTransientInput || !context.isWritable,
                    });
                }
                case 'referenceArray':
                    return await getArrayInputType('reference');
                case 'reference':
                    return await PropertyType.getReferenceInputValue(context, val, property);
                case 'object': {
                    const objectTargetFactory = property.targetFactory;
                    if (!objectTargetFactory) throw new Error(`${property.name}: missing class`);
                    return await ObjectType.getObjectInputValue(
                        context,
                        typeof val === 'string' ? JSON.parse(val) : val,
                        objectTargetFactory,
                    );
                }
                case 'collection':
                    return await PropertyType.getCollectionInputValue(context, val, property);
                case 'array':
                    return val;
                case 'json':
                case 'jsonReference':
                    return val;
                default:
                    throw new Error(`${factory.name}.${property.name}: invalid property type: ${property.type}`);
            }
        } catch (e) {
            logger.debug(() => e.stack);
            throw new DataInputError(e.message, e);
        }
    }

    private static getCollectionInputValue(
        context: Context,
        val: AnyValue,
        property: GraphQlProperty,
    ): Promise<AnyRecord[]> {
        const collectionFactory = (property as CollectionProperty).targetFactory;

        if (!Array.isArray(val)) throw new Error(`${property.name}: collection value is not an array`);

        if (!val.every(v => typeof v === 'object')) throw new Error(`${property.name}: collection invalid value`);

        return asyncArray(val)
            .map(async v => (await ObjectType.getObjectInputValue(context, v, collectionFactory))!)
            .toArray();
    }

    private static getReferenceInputValue(
        context: Context,
        val: AnyValue,
        property: GraphQlProperty,
    ): AsyncResponse<AnyValue> {
        const referenceFactory = (property as ReferenceProperty).targetFactory;
        if (!referenceFactory) throw new Error(`${property.name}: getInputValue reference: no REFERENCE`);
        if (!(referenceFactory instanceof NodeFactory))
            throw new Error(`${property.name}: target factory is not a node factory`);

        // if the value is null or undefined then we return null
        if (val == null) return null;

        switch (typeof val) {
            case 'number':
                // if the storage is external, we do not read the reference as there may be more properties in the reference factory
                // key other than the current property. It will eventually be resolved as a thunk and loaded with the join values later.
                // Also if the value < 0, its a transient value so we will just return the val.
                if (referenceFactory.storage !== 'external' && val >= 0) {
                    return context.read(
                        referenceFactory.nodeConstructor,
                        { _id: val },
                        { forUpdate: property.isWritable },
                    );
                }
                return val || null;
            case 'string':
                if (val.startsWith('#')) {
                    // id is a non-empty, non-negative value, read the node
                    return context.read(
                        referenceFactory.nodeConstructor,
                        { _id: val },
                        { forUpdate: property.isWritable },
                    );
                }
                // val is in the form '_id:1', we therefore need to extract the _id
                if (val.startsWith('_id:')) {
                    // extract _id as string for external factory and number for sql factory
                    const id = referenceFactory.storage === 'external' ? val.substring(4) : Number(val.substring(4));

                    // id is null, undefined, empty string or NaN (for non-external factories), then we return null
                    if (id == null || id === '' || (referenceFactory.storage !== 'external' && !Number.isFinite(id)))
                        return null;

                    // id is a number < 0, therefore it is transient and we should just return it
                    if (Number(id) < 0) return id;

                    // id is a non-empty, non-negative value, read the node
                    return context.read(
                        referenceFactory.nodeConstructor,
                        { _id: id },
                        { forUpdate: property.isWritable },
                    );
                }

                // the string does not start with _id: and the reference factory is external, so we return the value, and the reference will be loaded
                if (referenceFactory.storage === 'external') {
                    return val ?? null;
                }

                // the factory is not external at this point, therefore the string value must be a number
                // if the value is NaN then it will passed back and resolved as null in the number block of the switch
                return this.getReferenceInputValue(context, Number(val), property);

            case 'object':
                // if val is null the typeof will resolve to object therefore we need to check for null
                if (val == null) return null;
                return ObjectType.getObjectInputValue(context, val, property.targetFactory!);
            default:
                return val ?? null;
        }
    }

    /**
     * Transforms the `value` parameter to be sent in a GraphQL response
     * @param value The value to be transformed
     * @param property The property associated to the value
     */
    static getOutputValue(
        factory: NodeFactory,
        value: AnyValue,
        property: Property,
        options?: { type: TypeName },
    ): AnyValue {
        let type = options?.type || property.type;
        if (Array.isArray(value) && type !== 'json') {
            switch (type) {
                case 'integerArray':
                    type = 'integer';
                    break;
                case 'enumArray':
                    type = 'enum';
                    break;
                case 'referenceArray':
                    type = 'reference';
                    break;
                case 'stringArray':
                    type = 'string';
                    break;
                default:
                    // logger.error for demo. Should throw an exception
                    loggers.graphQl.error(`${factory.fullName}.${property.name}: UNEXPECTED ARRAY VALUE: ${value}`);
            }
            return value.map(v => PropertyType.getOutputValue(factory, v, property, { type }));
        }
        switch (type) {
            case 'date':
            case 'dateRange':
            case 'datetimeRange':
            case 'time':
            case 'datetime':
            case 'uuid':
                return value ? value.toString() : null;
            case 'decimal':
                return value == null ? null : value.toString();
            case 'textStream':
                return value;
            case 'binaryStream':
                return value;
            case 'json':
                return value;
            default:
                return value;
        }
    }

    /**
     * Defines the enum described by `propertyDecorator` in the `this.typesCache` cache, only if the enum has not been defined yet
     * @param factory the node factory
     * @param property The property that contains the enum definition
     */
    static makePropertyGraphQLEnumType(
        typeCache: TypeCache,
        factory: GraphQlFactory | undefined,
        property: GraphQlProperty,
        typingMode: GraphQlTypingMode,
    ): GraphQLType {
        const enumDataType = property.dataType as EnumDataType;
        if (!enumDataType) {
            // We get this error because some enums are missing
            logger.warn(`${factory && factory.fullName}.${property.name}: invalid property meta: enum member missing`);
            return GraphQLString;
        }
        const isInput = NodeType.isInput(typingMode);
        return EnumType.makeGraphQLEnumType(typeCache, enumDataType, isInput);
    }

    /**
     * Defines a enum graphql type based on an enum datatype
     * @param typeCache
     * @param enumDataType
     * @param typingMode
     * @returns
     */
    static makeGraphQLEnumType(typeCache: TypeCache, enumDataType: EnumDataType, isInput: boolean): GraphQLType {
        const name = `${schemaTypeName(enumDataType.enumFullName())}${isInput ? '_EnumInput' : ''}`;
        return typeCache.internType(name, () =>
            isInput ? makeCustomEnumInputType(name, enumDataType) : makeCustomEnumOutputType(name, enumDataType),
        );
    }

    /**
     * Returns the corresponding GraphQL type for the `property` simple property
     * @param factory the node factory
     * @param property The property we want to get the GraphQL type for
d     */
    private static makeSimplePropertyGraphQLType(
        typeCache: TypeCache,
        factory: GraphQlFactory,
        property: GraphQlProperty,
        typingMode: GraphQlTypingMode,
    ): GraphQLType {
        if (property.name === '_id') return idInputType;
        const isInput = NodeType.isInput(typingMode);
        switch (property.type) {
            case 'string':
            case 'time':
            case 'uuid':
                return GraphQLString;
            case 'short':
                return isInput ? integerInputType : GraphQLInt;
            case 'enum':
                return PropertyType.makePropertyGraphQLEnumType(typeCache, factory, property, typingMode);
            case 'integer':
                return isInput ? integerInputType : GraphQLInt;
            case 'integerRange':
                return integerRangeType;
            case 'decimalRange':
                return decimalRangeType;
            case 'decimal':
                return decimalType;
            case 'float':
                return isInput ? floatInputType : GraphQLFloat;
            case 'double':
                return isInput ? floatInputType : GraphQLFloat;
            case 'boolean':
                // TODO after release return isInput ? booleanInputType : GraphQLBoolean;
                return GraphQLBoolean;
            case 'date':
                return dateType;
            case 'dateRange':
                return dateRangeType;
            case 'datetimeRange':
                return datetimeRangeType;
            case 'datetime':
                return datetimeType;
            case 'binaryStream':
                return isInput ? streamInputType : binaryStreamOutputType;
            case 'textStream':
                return isInput ? streamInputType : textStreamOutputType;
            case 'json':
                return jsonType;
            case 'integerArray':
                return isInput ? new GraphQLList(integerInputType) : new GraphQLList(GraphQLInt);
            case 'enumArray':
                return new GraphQLList(
                    PropertyType.makePropertyGraphQLEnumType(typeCache, factory, property, typingMode),
                );
            case 'stringArray':
                return new GraphQLList(GraphQLString);
            default:
                throw new LogicError(`${factory.name}.${property.name}: unsupported GraphQL type: ${property.type}`);
        }
    }

    // Make input type for a property
    private static makePropertyInputType(
        typeCache: TypeCache,
        factory: GraphQlFactory,
        property: GraphQlProperty,
        propFactory: GraphQlFactory,
        typingMode: GraphQlTypingMode,
    ): GraphQLInputType {
        switch (property.type) {
            case 'collection': {
                const targetType = NodeType.makeInputType(typeCache, propFactory, typingMode);
                return new GraphQLList(targetType);
            }
            case 'instance': {
                return NodeType.makeInputType(typeCache, propFactory, typingMode);
            }
            case 'reference': {
                if (property.isMutable || property.isVitalParentInput)
                    return NodeType.makeInputType(typeCache, propFactory, typingMode);
                if (propFactory.storage === 'external') {
                    return externalReferenceInputType;
                }
                return referenceInputType;
            }
            case 'referenceArray': {
                return new GraphQLList(referenceInputType);
            }
            default:
                throw new LogicError(`${propFactory.fullName}.${property.name}: invalid type: ${property.type}`);
        }
    }

    private static getCollectionFields(
        typeCache: TypeCache,
        factory: NodeFactory,
        nodeType: GraphQLObjectType | GraphQLInterfaceType,
    ): GraphQLFieldConfigMap<any, any> {
        const fields: GraphQLFieldConfigMap<any, any> = {
            query: CollectionQuery.makeResolver(typeCache, factory, nodeType),
            readAggregate: AggregateRead.makeResolver(typeCache, factory),
            queryAggregate: AggregateQuery.makeResolver(typeCache, factory),
        };
        return fields;
    }

    // TODO: These functions should be refactored with different functions for nodeQuery and for 'collection' property
    static collectionPropertyType(typeCache: TypeCache, factory: NodeFactory): GraphQLObjectType | undefined {
        const nodeType = NodeType.makeOutputType(typeCache, factory, 'nodeOutput');
        const name = `${schemaTypeName(factory.fullName)}_Collection`;
        const fields = PropertyType.getCollectionFields(typeCache, factory, nodeType);

        const queries = factory.queries;
        queries
            .filter(query => query.isPublished)
            .forEach(query => {
                const queryName = camelCase(query.name);
                if (queryName.startsWith('_') || fields[queryName]) {
                    throw new Error(`${factory.fullName}.${queryName}: query names conflicts with system query`);
                }
                fields[queryName] = OperationType.makeResolver(typeCache, factory, query, false);
            });
        if (Object.keys(fields).length === 0) return undefined;

        return typeCache.internType(name, () => {
            return new GraphQLObjectType({
                name,
                fields,
            });
        });
    }

    // Make output type for a property
    private static makePropertyOutputType(
        typeCache: TypeCache,
        property: GraphQlProperty,
        propFactory: NodeFactory,
        typingMode: GraphQlTypingMode,
    ): GraphQLObjectType | GraphQLInterfaceType | GraphQLType {
        if (property.type === 'collection') {
            const connectionType = PropertyType.collectionPropertyType(typeCache, propFactory);
            if (connectionType) return connectionType;
        }

        if (property.type === 'referenceArray') {
            return new GraphQLList(NodeType.makeOutputType(typeCache, propFactory, typingMode));
        }

        return NodeType.makeOutputType(typeCache, propFactory, typingMode);
    }

    /**
     * Returns the corresponding GraphQL type for the `propertyDecorator` property
     * @param factory the node factory
     * @param propertyOrTypeName The property decorator or the name of the type we want to get the GraphQL type for
     * @param name The name of the property we want to get the GraphQL type for
     * @param typingMode The typing mode
     */
    static makePropertyType(
        typeCache: TypeCache,
        factory: GraphQlFactory,
        property: GraphQlProperty,
        typingMode: GraphQlTypingMode,
    ): GraphQLType {
        const isInput = NodeType.isInput(typingMode);
        if (foreignNodePropertyTypes.includes(property.type)) {
            const propFactory = property.targetFactory;
            if (!propFactory) throw new Error(`${factory.fullName}.${property.name}: no target factory`);

            if (
                propFactory.storage !== 'sql' &&
                propFactory.storage !== 'external' &&
                property.type === 'collection' &&
                !property.getValue &&
                !property.computeValue
            ) {
                logger.error(`${factory.fullName}.${property.name}: COLLECTION TARGETCLASS not persistent!!!`);
                // will fix later - for now, don't fail
                return GraphQLString;
            }

            if (isInput)
                return PropertyType.makePropertyInputType(typeCache, factory, property, propFactory, typingMode);
            return this.makePropertyOutputType(typeCache, property, propFactory as NodeFactory, typingMode);
        }
        if (property.type === 'object' || property.type === 'array') {
            if (property.type === 'object') {
                return ObjectType.makeObjectType(typeCache, factory, property, typingMode);
            }
            return ArrayType.makeArrayType(typeCache, factory, property, typingMode);
        }
        return this.makeSimplePropertyGraphQLType(typeCache, factory, property, typingMode);
    }
}
