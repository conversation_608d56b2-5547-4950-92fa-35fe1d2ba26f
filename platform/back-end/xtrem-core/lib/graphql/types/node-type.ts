import { <PERSON><PERSON><PERSON><PERSON>, AnyValue, asyncArray } from '@sage/xtrem-async-helper';
import { Dict, schemaTypeName } from '@sage/xtrem-shared';
import {
    FieldNode,
    GraphQLInputObjectType,
    GraphQLInterfaceType,
    GraphQLObjectType,
    GraphQLResolveInfo,
    GraphQLString,
    GraphQLType,
    Kind,
    ResponsePath,
    SelectionSetNode,
} from 'graphql';
import { camelCase } from 'lodash';
import { BaseCollection } from '../../collections';
import { runResolver } from '../../concurrency-utils';
import { CollectionProperty, Property, ReferenceProperty, foreignNodePropertyTypes } from '../../properties';
import { Context, NodeFactory } from '../../runtime';
import { CoreHooks } from '../../runtime/core-hooks';
import { loggers } from '../../runtime/loggers';
import { getPropertySchemaDescription } from '../../runtime/node-factory-utils';
import { Node } from '../../ts-api';
import { AggregateTypes, groupByVerbsEnumType } from '../aggregates/aggregate-types';
import { AccessRights } from '../security/access-rights';
import { TypeCache } from '../utils/type-cache';
import { getAccessField } from './access-type';
import { GraphQlFactory, GraphQlProperty, PropertyType } from './property-type';

const logger = loggers.graphQl;

export type GraphQlTypingMode = 'nodeOutput' | 'nodeInput' | 'values' | 'group' | 'parameter' | 'return';

const maskedPassword = '********************';

/**
 * @internal
 *
 * Static class to generate GraphQL type for a node factory
 */
export class NodeType {
    static isInput(typingMode: GraphQlTypingMode): boolean {
        return typingMode === 'nodeInput' || typingMode === 'parameter';
    }

    // Returns only published properties
    private static publishedProperties(factory: GraphQlFactory, typingMode: GraphQlTypingMode): GraphQlProperty[] {
        const allProps: Dict<GraphQlProperty> = {};
        const isInput = NodeType.isInput(typingMode);
        let f: GraphQlFactory | undefined = factory;
        while (f) {
            f.properties
                .filter(prop => {
                    if (!prop.isPublished) return false;
                    if (allProps[prop.name]) return false;
                    if (isInput) return prop.isOnInputType || !prop.isOutputOnly;
                    return !prop.isInputOnly;
                })
                .forEach(prop => {
                    allProps[prop.name] = prop;
                });
            f = f.baseFactory;
        }

        const publishedInputSystemProperties = Object.values(factory.publishedSystemProperties).filter(
            property => !isInput || (isInput && factory.publishedInputSystemProperties.includes(property.name)),
        );

        return Object.values(allProps).concat(publishedInputSystemProperties);
    }

    /**
     * Get the path to the leaf/node based on the graphql ResponsePath
     * Example: ['myPackage', 'myNode','query','edges','node','myProperty']
     * Note, we discard numeric values in the path which are the indices of array/list fields
     * @param path
     * @returns
     */
    private static getPropertyPath(path: ResponsePath): string[] {
        const astPath = [] as string[];
        const buildPath = (responsePath: ResponsePath): void => {
            // Discard numeric properties, which are only used for array indices
            if (typeof responsePath.key === 'string') astPath.unshift(responsePath.key);
            if (responsePath.prev) buildPath(responsePath.prev);
        };
        buildPath(path);

        return astPath;
    }

    /**
     * Walks the AST of the graphql requests and returns the list of siblings of the leaf property name passed in
     * @param propertyName
     * @param ast
     * @returns
     */
    static getSelectedProperties(propertyName: string, ast: GraphQLResolveInfo): string[] {
        const selectedProperties = [] as string[];
        // We get path to the property and reverse it so that we can walk the path from top to bottom
        const path = this.getPropertyPath(ast.path).reverse();
        const walkSelectionSets = (selectionSet: SelectionSetNode): void => {
            const pathEntry = path.pop();
            // We have reached the end of the path and our property is not in the path we will return an empty array in this case
            if (!pathEntry) return;

            const currentLevelSelectionNode = selectionSet.selections.find(
                selection => selection.kind === Kind.FIELD && selection.name.value === pathEntry,
            ) as FieldNode;

            // the current level in the path is not a selected item in the AST
            if (!currentLevelSelectionNode) {
                return;
            }

            const nextSelection = currentLevelSelectionNode.selectionSet;

            if (path.length === 0 && currentLevelSelectionNode.name.value === propertyName) {
                // We have reached the leaves of the graph, and the property we are walking to is leaf
                // push all the leaf and its siblings into the selectedProperties array
                // (take note that some of these nodes will have leaves of their own, e.g. reference properties),
                // as these are the selected properties of this node
                selectedProperties.push(...selectionSet.selections.map(s => (s as FieldNode).name.value));
                return;
            }

            // We are at a leaf, we cannot continue, empty array will be returned
            if (!nextSelection) {
                return;
            }

            walkSelectionSets(nextSelection);
        };

        walkSelectionSets(ast.operation.selectionSet);

        return selectedProperties;
    }

    static async resolveCustomFields(context: Context, factory: GraphQlFactory, property: Property): Promise<void> {
        if (!context.customFields[factory.fullName]) {
            // Note: we resolve the custom fields here rather than inside the resolver
            // because we have to await and resolver code is sync.
            const customFields = await CoreHooks.customizationManager.getCustomFields(context, [factory.fullName]);
            if (!customFields) throw property.logicError(`custom fields not found: ${factory.name}`);
            Object.assign(context.customFields, customFields);
        }
    }

    // Resolved the value of a property.
    // Replaces it by null if the API client is not authorized to see it.
    private static async resolveValue(
        factory: GraphQlFactory,
        context: Context,
        obj: Node,
        property: Property,
        args: { selector?: string } | undefined,
        ast: GraphQLResolveInfo,
    ): Promise<AnyValue> {
        const forbiddenProperty =
            !(await property.isEnabledByServiceOptions(context)) || !(await property.isAuthorized(context));

        let value: AnyValue;

        if (args?.selector && property.name === '_customData')
            await this.resolveCustomFields(context, factory, property);

        if (!forbiddenProperty) {
            value = await obj.$.state.getPropertyValue(property);
        } else {
            const path = NodeType.getPropertyPath(ast.path);
            const selectedProperties = this.getSelectedProperties(property.name, ast);
            // If _access is one of the selected properties then we do not add a diagnose
            if (!selectedProperties.includes('_access')) context.addRestrictedPropertyDiagnose(path);
            value = null;
        }
        if (property.isCollectionProperty()) {
            // TODO: Implement filters on collection properties
            const collection = value as BaseCollection;
            return collection;
        }
        if (value && property.isJsonProperty() && args?.selector) {
            value = (value as Dict<AnyValue>)[args?.selector];
        }

        const propertyValue = PropertyType.getOutputValue(factory as NodeFactory, value, property);
        if (property.isStringProperty() && property.isStoredEncrypted) {
            return propertyValue ? maskedPassword : '';
        }
        return propertyValue;
    }

    /** @internal */
    static nodeValuesIn(
        context: Context,
        obj: AnyRecord,
        factory: NodeFactory,
        // TODO: remove this option when UI omits empty fields that are not dirty, instead of sending them as null.
        options?: { omitNulls?: boolean; forUpdate?: boolean },
    ): Promise<AnyRecord> {
        context.prefetcher.visit(factory, !!options?.forUpdate, obj);

        return asyncArray(Object.keys(obj)).reduce(async (r, k) => {
            const property = factory.findProperty(k);
            const value = await PropertyType.getInputValue(context, obj[k], factory, property);
            if (value != null || !options?.omitNulls) r[property.name] = value;
            // if encrypted value, remove it from the payload
            if (property.isStringProperty() && property.isStoredEncrypted && value === maskedPassword) {
                delete r[property.name];
            }
            return r;
        }, {} as AnyRecord);
    }

    /** @internal */
    static async nodeIn(
        context: Context,
        obj: AnyRecord,
        factory: NodeFactory,
        options?: { isTransientInput?: boolean },
    ): Promise<Node> {
        return context.create(factory.nodeConstructor, await NodeType.nodeValuesIn(context, obj, factory), {
            isTransient: options?.isTransientInput,
        });
    }

    /**
     * Get the field description for the property
     * @param prop
     * @returns
     */
    private static getPropertyFieldDescription(prop: GraphQlProperty): string {
        if (prop instanceof Property) return getPropertySchemaDescription(prop);
        return `Parameter ${prop.name}`;
    }

    /**
     * get the arguments of the field
     * @param prop
     * @param typingMode
     * @returns
     */
    private static getPropertyFieldArgs(prop: GraphQlProperty, typingMode: GraphQlTypingMode): any {
        // group types that are not references have an argument to say what to group by, default is value
        const args = {} as Dict<{ type: GraphQLType }>;
        if (typingMode === 'group' && prop.type !== 'reference') args.by = { type: groupByVerbsEnumType };
        if (prop.type === 'json') args.selector = { type: GraphQLString };
        return args;
    }

    /**
     * get the property field type
     * @param typeCache
     * @param schemaContext
     * @param prop
     * @param factory
     * @param typingMode
     * @returns
     */
    private static getPropertyFieldType(
        typeCache: TypeCache,
        prop: GraphQlProperty,
        factory: GraphQlFactory,
        typingMode: GraphQlTypingMode,
    ): GraphQLType | undefined {
        // We don't manage collection for aggregates
        if ((typingMode === 'values' || typingMode === 'group') && prop.type === 'collection') return undefined;

        // get the aggregate property, reference properties need to be aggregated on its own fields
        if (typingMode === 'values' && prop.type !== 'reference') {
            return AggregateTypes.getAggregateType(typeCache, prop);
        }

        return PropertyType.makePropertyType(typeCache, factory, prop, typingMode);
    }

    /**
     * get property field resolver
     * @param prop
     * @param factory
     * @param typingMode
     * @returns
     */
    private static getPropertyFieldResolver(
        prop: GraphQlProperty,
        factory: GraphQlFactory,
        typingMode: GraphQlTypingMode,
    ): ((obj: Node, args: any, rootContext: Context, ast: GraphQLResolveInfo) => Promise<AnyValue>) | undefined {
        // Set resolver for output types only
        if (typingMode === 'nodeOutput') {
            return (obj: Node, args: any, _rootContext: Context, ast: GraphQLResolveInfo) => {
                const context = obj.$.context;
                return AccessRights.runSecure(context, prop.name, { factory, isPropertyAccess: true }, () =>
                    NodeType.resolveValue(factory, context, obj, prop as Property, args, ast),
                );
            };
        }

        // We need a resolver for values and group, as the fields may contain a JSON property with a selector argument
        if (typingMode === 'values' || typingMode === 'group') {
            if (prop.type === 'collection') return undefined;
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            return (obj: any, args: any, _rootContext: Context, _ast: GraphQLResolveInfo) => {
                const value = obj[prop.name];
                if (obj && prop.type === 'json' && args?.selector) {
                    return Promise.resolve(value[args.selector]);
                }
                return Promise.resolve(value);
            };
        }

        return undefined;
    }

    // Makes a GraphQL field for a property and adds it to `fields`
    private static makePropertyField(
        typeCache: TypeCache,
        fields: Dict<any>,
        prop: GraphQlProperty,
        factory: GraphQlFactory,
        options: {
            typingMode: GraphQlTypingMode;
            fieldName?: string;
        },
    ): void {
        const typingMode = options.typingMode;
        const fieldName = options.fieldName || prop.name;

        // Validate property name
        if (!/[a-z]/i.test(fieldName)) throw new Error(`${factory.fullName}.${fieldName}: invalid property name`);

        const type: GraphQLType | undefined = this.getPropertyFieldType(typeCache, prop, factory, typingMode);

        // Do not expose this property if type is not defined
        // Could be a
        //  - property type is not involved in aggregation
        if (type === undefined) {
            return;
        }

        fields[fieldName] = {
            description: this.getPropertyFieldDescription(prop),
            type,
            args: this.getPropertyFieldArgs(prop, typingMode),
        };

        const resolve = this.getPropertyFieldResolver(prop, factory, typingMode);
        if (resolve != null) {
            fields[fieldName].resolve = resolve;
        }
    }

    private static makeCollectionPropertyFields(
        typeCache: TypeCache,
        fields: Dict<any>,
        prop: CollectionProperty,
        factory: GraphQlFactory,
        typingMode: GraphQlTypingMode,
    ): void {
        const rootProperty = prop.rootProperty;
        if (factory.isAbstract && rootProperty.targetFactory.isAbstract) {
            // Use rootProperty rather than property, to get identical property types all along the hierarchy
            NodeType.makePropertyField(typeCache, fields, rootProperty, rootProperty.factory, {
                typingMode,
                fieldName: camelCase(`any ${prop.name}`),
            });
        } else {
            if (rootProperty !== prop) {
                // Here also, use rootProperty rather than property.
                NodeType.makePropertyField(typeCache, fields, rootProperty, rootProperty.factory, {
                    typingMode,
                    fieldName: camelCase(`any ${prop.name}`),
                });
            }
            NodeType.makePropertyField(typeCache, fields, prop, factory, { typingMode });
        }
    }

    // Makes property fields for all the node's properties
    private static makeFields(
        typeCache: TypeCache,
        factory: GraphQlFactory,
        options: {
            isThunk?: boolean;
            typingMode: GraphQlTypingMode;
        },
    ): Dict<any> {
        let properties = NodeType.publishedProperties(factory, options.typingMode);
        if (properties.length === 0) throw new Error(`${factory.name}: cannot create graphql type: no properties`);
        if (options.isThunk) {
            properties = properties.filter(prop => prop.name === '_id');
        }

        const fields = {} as Dict<any>;

        if (options.typingMode === 'nodeOutput') {
            // TODO: we need to implement _access for aggregate output types

            // The _access property is a special output type (array of objects)
            fields._access = getAccessField(factory);
        }

        return properties.reduce((r, prop) => {
            if (
                (prop.type === 'reference' || prop.type === 'referenceArray') &&
                (prop as ReferenceProperty).targetFactory.storage !== 'sql' &&
                (prop as ReferenceProperty).targetFactory.storage !== 'external'
            ) {
                logger.warn(`${factory.name}.${prop.name}: not published because target class is not persistent`);
                return r;
            }
            if (
                prop instanceof Property &&
                foreignNodePropertyTypes.includes(prop.type) &&
                !prop.getValue &&
                !prop.computeValue &&
                !(prop.isCollectionProperty() && prop.getFilter) &&
                !prop.join
            ) {
                logger.warn(`${factory.name}.${prop.name}: not published because join and getValue are missing`);
                return r;
            }

            if (prop.type === 'collection') {
                NodeType.makeCollectionPropertyFields(
                    typeCache,
                    r,
                    prop as CollectionProperty,
                    factory,
                    options.typingMode,
                );
            } else {
                // Fill the property's object fields
                NodeType.makePropertyField(typeCache, r, prop, factory, options);
            }
            return r;
        }, fields);
    }

    /**
     * Defines the object type described by `factory` in the `this.typesCache` cache only if the type has not been defined yet
     */
    static makeOutputType(
        typeCache: TypeCache,
        factory: GraphQlFactory,
        typingMode: GraphQlTypingMode,
        options?: { isInterface: boolean },
    ): GraphQLObjectType | GraphQLInterfaceType {
        // Aggregates must return object types even on abstract nodes to allow queries
        const isAggregate = typingMode === 'group' || typingMode === 'values';
        let name = schemaTypeName(factory.fullName);
        switch (typingMode) {
            case 'values':
                name += '_Values';
                break;
            case 'group':
                name += '_Group';
                break;
            default:
                break;
        }
        return typeCache.internType(name, () => {
            const interfaces: GraphQLInterfaceType[] = [];
            let f: GraphQlFactory | undefined = factory.baseFactory;
            while (f) {
                const interfaceType = NodeType.makeOutputType(typeCache, f, typingMode, { isInterface: !isAggregate });
                if (!isAggregate) {
                    interfaces.push(interfaceType as GraphQLInterfaceType);
                }
                f = f.baseFactory;
            }
            if (options?.isInterface || (factory.isAbstract && !isAggregate)) {
                return this.makeInterface(name, interfaces, typeCache, factory, typingMode);
            }

            return new GraphQLObjectType({
                interfaces,
                description: factory.getNodeSchemaDescription(),
                name,
                fields: () => NodeType.makeFields(typeCache, factory, { typingMode }),
            });
        });
    }

    private static makeInterface(
        name: string,
        interfaces: GraphQLInterfaceType[],
        typeCache: TypeCache,
        factory: GraphQlFactory,
        typingMode: GraphQlTypingMode,
    ): GraphQLInterfaceType {
        return new GraphQLInterfaceType({
            interfaces,
            name,
            fields: () => {
                return NodeType.makeFields(typeCache, factory, {
                    typingMode,
                });
            },
            // resolveType is called without fiber, we need to return a promise using run so that we can call wait
            resolveType: (data: Node, context: Context) =>
                runResolver(context, () => typeCache.getCached<GraphQLObjectType>(data.$?.state?.factory?.name).name),
        });
    }

    /**
     * Defines the input object type described by `factory` in the `this.typesCache` cache only if the type has not been defined yet
     */
    static makeInputType(
        typeCache: TypeCache,
        factory: GraphQlFactory,
        typingMode: GraphQlTypingMode,
    ): GraphQLInputObjectType {
        const name = `${schemaTypeName(factory.fullName)}_Input`;
        return typeCache.internType(name, () => {
            return new GraphQLInputObjectType({
                name,
                fields: () => NodeType.makeFields(typeCache, factory, { typingMode }),
            });
        });
    }
}
