import { Dict } from '@sage/xtrem-shared';
import { GraphQLFieldConfig, GraphQLFieldConfigMap, GraphQLType } from 'graphql';
import { Application } from '../../application/application';
import { Context } from '../../runtime/context';

/**
 * @disabled_internal
 *
 * Static class to cache GraphQL types so that we don't generate two copies of the same type.
 */
export class TypeCache {
    /**
     * typesCache is a dictionary such as:
     *        - each key is the name of a GraphQl type
     *        - each value is an actual GraphQl type.
     */
    private readonly typesCache = {} as Dict<GraphQLType | undefined>;

    #globalNamespace = {} as GraphQLFieldConfigMap<any, any>;

    constructor(readonly application: Application) {}

    /**
     * Adds a type named `name` in the `this.typesCache` cache as the result of executing `body`, only if the type has not been added yet.
     * Interning is a method of storing only one copy of each distinct string value (see https://en.wikipedia.org/wiki/String_interning)
     * @param name The name of the type to be defined
     * @param body The function that will be executed to define the new type
     */
    internType<T extends GraphQLType | undefined>(name: string, body: () => T): T {
        if (!(name in this.typesCache)) this.addCacheEntry(name, body);
        return this.getCached(name);
    }

    getCached<T extends GraphQLType | undefined>(name: string): T {
        return this.typesCache[name] as T;
    }

    /** @disabled_internal */
    addCacheEntry<T extends GraphQLType | undefined>(name: string, body: () => T): void {
        this.typesCache[name] = body();
    }

    get globalNamespace(): GraphQLFieldConfigMap<any, any> {
        return this.#globalNamespace;
    }

    /** @disabled_internal */
    addGlobalNamespaceEntry(name: string, resolver: GraphQLFieldConfig<unknown, Context, Dict<any>>): void {
        if (this.#globalNamespace[name] !== undefined)
            throw new Error(`${name}: operation name conflicts with another global operation`);
        this.#globalNamespace[name] = resolver;
    }

    resetGlobalNamespace(): void {
        this.#globalNamespace = {};
    }
}
