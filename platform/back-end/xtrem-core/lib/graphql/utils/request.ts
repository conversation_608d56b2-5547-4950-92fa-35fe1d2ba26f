import { Request } from 'express';

export interface RequestWithHint extends Request {
    hint?: string | null;
}

export function getRequestHint(request: RequestWithHint): string | null {
    const prefix = request.headers?.['xtrem-graphql-fetcher'] ? 'graphiql:' : '';
    return `${prefix}${request.body ? (setGraphQlHint(request) ?? '') : ''}`;
}

/**
 * Set a string representing a hint on the GraphQL query purpose
 * @param request the HTTP request
 * @returns the string hint or null
 */
export function setGraphQlHint(request: RequestWithHint): string | null {
    // check if the request hint as already been set
    if (request.hint !== undefined) {
        return request.hint;
    }

    const body = request.body;
    if (typeof body?.query !== 'string') {
        request.hint = null;
        return null;
    }
    // This is a very basic GraphQL query parser based on regexp
    // All regexp have been checked on https://devina.io/redos-checker
    // We assume that the first 250 char is good enough for having a relevant context
    const query = (body?.query as string).substring(0, 250).replace(/[\n\r]/g, '');

    // eat GraphQL operation:
    //  { ... } => query
    //  query { ... } => query
    //  mutation { ... } => mutation
    const queryTypeMatch = /^(query|mutation)?\s*{\s*(.*)/.exec(query);
    const queryType = queryTypeMatch?.[1] ?? 'query';
    let remaining = queryTypeMatch?.[2] ?? '';

    // eat query type, removing potential alias:
    //  query { xtremAuthorization { ... } } => xtremAuthorization
    //  query { rootnode: xtremAuthorization { ... } } => xtremAuthorization
    const typeRegex = /^(?:\w+:\s*)?(\w+)\b(?:\s*\(\w+:.*\))?\s*{\s*(.*)/;
    const typeMatch = typeRegex.exec(remaining);
    const type = typeMatch?.[1] ?? '';
    remaining = typeMatch?.[2] ?? '';

    // eat query node or field:
    //  query { xtremAuthorization { activity { ... } } } => activity
    const nameRegex = /^(\w+)\b[^}{]*[}{]\s*(.*)?/;
    const fieldMatch = nameRegex.exec(remaining);
    const field = fieldMatch?.[1] ?? '';
    remaining = fieldMatch?.[2] ?? '';

    const segments = [`${request.method} ${request.baseUrl}${request.route?.path ?? ''}`, queryType, type, field];
    if (request.baseUrl !== '/metadata') {
        // eat node operation:
        //  query { xtremAuthorization { activity { query(filter: "{name: 'dummyTest'}") { ... } } } } => query
        //  mutation { xtremAuthorization { activity { update(data: { _id: "#dummyTest", description: "Dummy Updated" }) { ... } } } } => update
        const operationRegex = /^((?:\w+))(?:\(\w+:.*\))?/;
        const operationMatch = operationRegex.exec(remaining);
        const operation = operationMatch?.[1] ?? '';
        segments.push(operation);
    }
    const hint = segments.join(' > ');
    (request as any).hint = hint;
    return hint;
}
