import { asyncArray } from '@sage/xtrem-async-helper';
import { DataInputError } from '@sage/xtrem-shared';
import { isObject } from 'lodash';
import { Context } from '../../runtime';
import { CoreHooks } from '../../runtime/core-hooks';
import { loggers } from '../../runtime/loggers';
import { friendlyJsonParse } from '../../runtime/node-factory-utils';
import { Node, NodeQueryFilter } from '../../ts-api';

const logger = loggers.graphQl;

const fetchBulkIds = (context: Context, nodeConstructor: typeof Node, filter: string): Promise<(string | number)[]> => {
    if (!filter) {
        // TODO: localize
        throw new DataInputError('Filter is empty');
    }
    const parsedFilter: NodeQueryFilter<any> = friendlyJsonParse(filter);
    if (!isObject(parsedFilter)) {
        // TODO: localize
        throw new DataInputError('Parsed filter is not an object');
    }

    return context.select(nodeConstructor, true, { filter: parsedFilter });
};

export const executeBulkAction = async (
    context: Context,
    filter: string,
    nodeConstructor: typeof Node,
    {
        body,
        onComplete,
    }: {
        body: (subContext: Context, instance: Node) => Promise<unknown>;
        onComplete?: (subContext: Context, results: unknown[]) => Promise<void>;
    },
): Promise<number> => {
    let lastChecked = Date.now();
    const ids = await fetchBulkIds(context, nodeConstructor, filter);
    let successCount = 0;
    let errorCount = 0;

    const bodyResults = onComplete ? ([] as unknown[]) : undefined;
    const stopRequested = await asyncArray(ids).some(async instanceId => {
        // Only check every 100 ms, to reduce impact on the database, and keep the loop fast
        // Check for stop first so that we exit the loop if the select query took time and stop was requested.
        if (Date.now() > lastChecked + 100) {
            lastChecked = Date.now();
            if (await CoreHooks.communicationManager.isStopRequested(context)) return true;
        }

        try {
            const executeBody = async (bodyContext: Context): Promise<void> => {
                const newInstance = await bodyContext.read(
                    nodeConstructor,
                    { _id: instanceId },
                    { forUpdate: bodyContext.isWritable },
                );
                const result = await body(bodyContext, newInstance);
                if (bodyResults) bodyResults.push(result);
            };

            if (context.isWritable) {
                await context.withChildContext(executeBody, { isolationLevel: 'low', isDetachedContext: true });
            } else {
                await executeBody(context);
            }

            successCount += 1;
        } catch (err) {
            logger.error(`${nodeConstructor.name} bulk mutation failed with ${instanceId}`);
            logger.error(err.stack);
            errorCount += 1;
        }
        await CoreHooks.communicationManager.updateProgress(context, {
            successCount,
            errorCount,
            totalCount: ids.length,
        });
        return false;
    });

    if (stopRequested) {
        await CoreHooks.communicationManager.setStopped(context, context.getContextValue('notificationId') as string);
    }

    if (onComplete && bodyResults) {
        if (context.isWritable) {
            await context.withChildContext(bodyContext => onComplete(bodyContext, bodyResults), {
                isolationLevel: 'low',
                isDetachedContext: true,
            });
        } else {
            await onComplete(context, bodyResults);
        }
    }

    return ids.length;
};
