import { AnyValue } from '@sage/xtrem-async-helper';
import { Dict } from '@sage/xtrem-shared';
import { GraphQLFieldConfig } from 'graphql';
import { BaseCollection } from '../../collections';
import { Context, NodeFactory } from '../../runtime';
import { Aggregate, NodeQueryOptions } from '../../ts-api';
import { Paging, QueryArgs } from '../paging/paging';
import { AccessRights } from '../security/access-rights';
import { NodeType } from '../types/node-type';
import { TypeCache } from '../utils/type-cache';
import { AggregateTypes } from './aggregate-types';

export interface ReadAggregateNode {
    values: Dict<AnyValue>;
}

/**
 * @internal
 *
 * Static class which provides the GraphQL schema resolver for the `readAggregate` operation:
 *
 * ``` graphql
 *  query {
 *      xtremFoo {
 *          Bar {
 *              readAggregate { ... }
 *          }
 *      }
 *  }
 *  ```
 */
export class AggregateRead {
    // The `resolve` callback for the `readAggregate` operation
    private static resolveAggregateRead(
        context: Context,
        factory: NodeFactory,
        obj: unknown,
        pagingOptions: NodeQueryOptions,
        aggregate: Aggregate,
        args: Dict<any>,
    ): Promise<ReadAggregateNode> {
        return AccessRights.runSecure(context, 'read', { factory, args }, async () => {
            const results = (await context
                .query(factory.nodeConstructor, {
                    ...pagingOptions,
                    aggregate,
                })
                .toArray()) as Dict<any>[];
            return results[0].values;
        });
    }

    /** Resolver for
     * `query { xtremFoo { Bar { readAggregate { ... } } } }`
     * `query { xtremFoo { Bar { query { edges { node { lines { readAggregate { ... } } } } } } } }`
     */
    static makeResolver(
        typeCache: TypeCache,
        factory: NodeFactory,
    ): GraphQLFieldConfig<BaseCollection | {}, Context, QueryArgs> {
        return {
            type: NodeType.makeOutputType(typeCache, factory, 'values'),
            args: Paging.getPagingArgumentTypes(),
            resolve(obj, args, rootContext, info) {
                return AccessRights.runSecure(rootContext, 'read', { factory }, async () => {
                    const context = obj instanceof BaseCollection ? obj.sourceNode.$.context : rootContext;
                    const aggregate = await AggregateTypes.parseAggregate(context, info, factory);
                    const pagingOptions = await Paging.parsePagingOptions(
                        context,
                        factory,
                        args,
                        info,
                        undefined,
                        aggregate,
                    );
                    if (obj instanceof BaseCollection) await Paging.addCollectionFilter(pagingOptions, obj);
                    return AggregateRead.resolveAggregateRead(context, factory, obj, pagingOptions, aggregate, args);
                });
            },
        };
    }
}
