import { AnyValue, Dict, schemaTypeName } from '@sage/xtrem-shared';
import { GraphQLFieldConfig, GraphQLList, GraphQLObjectType, GraphQLString } from 'graphql';
import * as _ from 'lodash';
import { BaseCollection } from '../../collections';
import { Context, NodeFactory } from '../../runtime';
import { Aggregate, Node, NodeQueryOptions } from '../../ts-api';
import { cursorChecksum } from '../../types';
import { PageResolver, Paging, QueryArgs, QueryPage } from '../paging/paging';
import { pageInfoType } from '../paging/paging-types';
import { GetTotalCount } from '../queries/get-total-count';
import { AccessRights } from '../security/access-rights';
import { NodeType } from '../types/node-type';
import { TypeCache } from '../utils/type-cache';
import { AggregateTypes } from './aggregate-types';

interface QueryAggregateNode {
    group?: Dict<any>;
    values?: Dict<any>;
}

/**
 * @internal
 *
 * Static class which provides the GraphQL schema resolver for the `queryAggregate` operation:
 *
 * ``` graphql
 *  query {
 *      xtremFoo {
 *          Bar {
 *              queryAggregate { ... }
 *          }
 *      }
 *  }
 *  ```
 */
export class AggregateQuery {
    private static makeGroupCursor(item: any): string {
        const parseCursor = (values: AnyValue[]): AnyValue[] =>
            Object.values(values).map((value: AnyValue) =>
                _.isPlainObject(value) || _.isArray(value) ? parseCursor(value as AnyValue[]) : value,
            );
        const str = JSON.stringify(_.flattenDeep(parseCursor(Object.values(item))));
        return `${str}${cursorChecksum(str)}`;
    }

    private static makeGroupNode(item: any): Node {
        return {
            ...item,
            $: { state: { getCursorValue: () => this.makeGroupCursor(item.group) } },
        };
    }

    private static async makePageValue(
        context: Context,
        factory: NodeFactory,
        results: any[],
        pagingOptions: NodeQueryOptions,
        aggregate: Aggregate,
    ): Promise<QueryPage<QueryAggregateNode>> {
        const items = results.map(item => this.makeGroupNode(item));
        let totalCount = items.length;
        // if we are paging the aggreate query, we need to count the total number of items, as not all items are returned
        if (Object.keys(pagingOptions).some(key => ['first', 'last', 'before', 'after'].includes(key))) {
            totalCount = await context.queryCount(factory.nodeConstructor, {
                ..._.omit(pagingOptions, ['first', 'last', 'before', 'after']),
                aggregate,
            });
        }

        return Paging.buildOutputPage(
            {
                items,
                totalCount,
            },
            pagingOptions,
        );
    }

    // The `resolve` callback for the `queryAggregate` operation
    private static resolveAggregateQuery(
        context: Context,
        factory: NodeFactory,
        obj: unknown,
        pagingOptions: NodeQueryOptions,
        aggregate: Aggregate,
        args: Dict<any>,
    ): Promise<QueryPage<QueryAggregateNode>> {
        return AccessRights.runSecure(context, 'read', { factory, args }, async () => {
            const results = (await context
                .query(factory.nodeConstructor, {
                    ...pagingOptions,
                    first: pagingOptions.first && pagingOptions.first + 1,
                    last: pagingOptions.last && pagingOptions.last + 1,
                    aggregate,
                })
                .toArray()) as Dict<any>[];
            return AggregateQuery.makePageValue(context, factory, results, pagingOptions, aggregate);
        });
    }

    // Intermediate resolver for `query { xtremFoo { Bar { queryAggregate { edges { node { ... } } } } } }`
    private static makeEdgeNodeResolver(
        typeCache: TypeCache,
        factory: NodeFactory,
        name: string,
    ): GraphQLFieldConfig<unknown, Context, {}> {
        return {
            type: new GraphQLObjectType({
                name: `${name}_Query_Edges_Node`,
                fields: {
                    group: {
                        type: NodeType.makeOutputType(typeCache, factory, 'group'),
                    },
                    values: {
                        type: NodeType.makeOutputType(typeCache, factory, 'values'),
                    },
                },
            }),
        };
    }

    // Intermediate resolver for `query { xtremFoo { Bar { queryAggregate { edges { ... } } } } }`
    private static makeEdgesResolver(
        typeCache: TypeCache,
        factory: NodeFactory,
        name: string,
    ): GraphQLFieldConfig<PageResolver, Context, {}> {
        return {
            type: new GraphQLList(
                new GraphQLObjectType({
                    name: `${name}_Query_Edges`,
                    fields: {
                        node: AggregateQuery.makeEdgeNodeResolver(typeCache, factory, name),
                        cursor: {
                            type: GraphQLString,
                        },
                    },
                }),
            ),
            resolve(obj) {
                return obj.pagePromise!.then((result: any) => {
                    return result.edges;
                });
            },
        };
    }

    // Intermediate resolver for `query { xtremFoo { Bar { queryAggregate { pageInfo { ... } } } } }`
    private static makePageInfoResolver(): GraphQLFieldConfig<PageResolver, Context> {
        return {
            type: pageInfoType,
            resolve(obj) {
                return obj.pagePromise ? obj.pagePromise.then(result => result.pageInfo) : obj.pageInfo;
            },
        };
    }

    /** Resolver for
     * `query { xtremFoo { Bar { queryAggregate { ... } } } }`
     * `query { xtremFoo { Bar { query { edges { node { lines { queryAggregate { ... } } } } } } } }`
     */
    static makeResolver(
        typeCache: TypeCache,
        factory: NodeFactory,
    ): GraphQLFieldConfig<BaseCollection | {}, Context, QueryArgs> {
        const name = `${schemaTypeName(factory.fullName)}_Agg`;
        const typeName = `${name}_Query`;
        return {
            type: typeCache.internType(
                typeName,
                () =>
                    new GraphQLObjectType({
                        name: typeName,
                        fields: {
                            edges: AggregateQuery.makeEdgesResolver(typeCache, factory, name),
                            pageInfo: AggregateQuery.makePageInfoResolver(),
                            totalCount: GetTotalCount.makeResolver(factory),
                        },
                    }),
            ),
            args: Paging.getPagingArgumentTypes(),
            resolve(obj, args, rootContext, info) {
                return AccessRights.runSecure(rootContext, 'read', { factory }, async () => {
                    const context = obj instanceof BaseCollection ? obj.sourceNode.$.context : rootContext;

                    const aggregate = await AggregateTypes.parseAggregate(context, info, factory);
                    const pagingOptions = await Paging.parsePagingOptions(context, factory, args, info, {}, aggregate);
                    if (obj instanceof BaseCollection) await Paging.addCollectionFilter(pagingOptions, obj);
                    return {
                        context,
                        pagePromise: AggregateQuery.resolveAggregateQuery(
                            context,
                            factory,
                            obj,
                            pagingOptions,
                            aggregate,
                            args,
                        ),
                    };
                });
            },
        };
    }
}
