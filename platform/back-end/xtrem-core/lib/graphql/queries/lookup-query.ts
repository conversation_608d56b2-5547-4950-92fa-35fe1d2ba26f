import { AnyRecord } from '@sage/xtrem-async-helper';
import { GraphQLFieldConfig, GraphQLInterfaceType, GraphQLObjectType } from 'graphql';
import { Context, NodeFactory } from '../../runtime';
import { Node, NodeQueryOptions } from '../../ts-api';
import { PageResolver, Paging, QueryArgs } from '../paging/paging';
import { AccessRights } from '../security/access-rights';
import { NodeType } from '../types/node-type';
import { TypeCache } from '../utils/type-cache';
import { LookupTotalCount } from './lookup-total-count';

/** @internal */
export interface LookupsArgs {
    _id?: string;
    data?: AnyRecord;
}

/** @internal */
export interface ParentLookupsArgs {
    lookupArgs: LookupsArgs;
    factory: NodeFactory;
    parentFactory: NodeFactory;
    propertyName: string;
    pagingOptions: NodeQueryOptions;
}

/** @internal */
export interface ReferenceLookupsArgs<T = any> extends PageResolver<T> {
    parentArgs: ParentLookupsArgs;
}

/**
 * @internal
 *
 * Static class which provides the GraphQL schema resolver for a lookup on a reference property:
 *
 * ``` graphql
 *  query {
 *      xtremFoo {
 *          Bar {
 *              lookups {
 *                  zoo(...) { ... }
 *              }
 *          }
 *      }
 *  }
 *  ```
 */
export class LookupQuery {
    // The resolver's type
    private static makeLookupQueryType(
        typeCache: TypeCache,
        nodeType: GraphQLObjectType | GraphQLInterfaceType,
    ): GraphQLObjectType {
        const name = `${nodeType.toString()}_Lookups_Reference`;
        return typeCache.internType(name, () => {
            return new GraphQLObjectType({
                name,
                fields: {
                    totalCount: LookupTotalCount.makeResolver(),
                    edges: Paging.makeEdgesResolver(typeCache, nodeType),
                    pageInfo: Paging.makePageInfoResolver(),
                },
            });
        });
    }

    // The resolve callback
    private static async lookupQuery(context: Context, parent: ParentLookupsArgs): Promise<Node[]> {
        const queryOptions = {
            ...parent.pagingOptions,
            first: parent.pagingOptions.first && parent.pagingOptions.first + 1,
            last: parent.pagingOptions.last && parent.pagingOptions.last + 1,
        };

        if (!parent.lookupArgs._id && !parent.lookupArgs.data)
            throw new Error(
                `${parent.parentFactory.name}.${parent.propertyName}: lookups missing id or data parameter`,
            );

        if (parent.lookupArgs.data) {
            parent.lookupArgs.data = await NodeType.nodeValuesIn(
                context,
                parent.lookupArgs.data,
                parent.parentFactory,
                {
                    omitNulls: false,
                },
            );
        }

        const lookupQueryParams = await context.makeLookupQueryParameters(
            parent.parentFactory.nodeConstructor,
            parent.propertyName,
            parent.lookupArgs,
        );

        return context.lookup(lookupQueryParams, queryOptions).toArray();
    }

    /** Resolver for `query { xtremFoo { Bar { lookups { zoo(...) { ... } } } } }` */
    static makeResolver(
        typeCache: TypeCache,
        factory: NodeFactory,
        parentFactory: NodeFactory,
        nodeType: GraphQLObjectType | GraphQLInterfaceType,
        propertyName: string,
    ): GraphQLFieldConfig<LookupsArgs, Context, QueryArgs> {
        return {
            type: LookupQuery.makeLookupQueryType(typeCache, nodeType),
            args: Paging.getPagingArgumentTypes(),
            resolve(lookupArgs, args, rootContext, info) {
                return AccessRights.runSecure(rootContext, 'lookup', { factory, args }, async () => {
                    const parentArgs = {
                        lookupArgs,
                        factory,
                        parentFactory,
                        propertyName,
                    };
                    const context = rootContext;
                    const pagingOptions = await Paging.parsePagingOptions(
                        context,
                        factory,
                        args,
                        info,
                        factory.defaultOrderBy,
                    );
                    const nodes = await LookupQuery.lookupQuery(context, { ...parentArgs, pagingOptions });
                    const pagePromise = Paging.buildOutputPage({ items: nodes }, pagingOptions, true);
                    return { context, pagePromise, parentArgs };
                });
            },
        };
    }
}
