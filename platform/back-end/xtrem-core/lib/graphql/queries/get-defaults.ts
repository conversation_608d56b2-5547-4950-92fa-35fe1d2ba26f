import { Dict } from '@sage/xtrem-shared';
import { GraphQLFieldConfig } from 'graphql';
import { Context, NodeFactory } from '../../runtime';
import { AccessRights } from '../security/access-rights';
import { NodeType } from '../types/node-type';
import { TypeCache } from '../utils/type-cache';

/** @internal */
export interface GetDefaultsArgs {
    data?: object;
}

/**
 * @internal
 *
 * Static class which provides the GraphQL schema resolver for the `getDefaults` operation:
 *
 * ``` graphql
 *  query {
 *      xtremFoo {
 *          Bar {
 *              getDefaults(data: ...) { ... }
 *          }
 *      }
 *  }
 *  ```
 */
export class GetDefaults {
    /** Resolver for `query { xtremFoo { Bar { getDefaults(data...) { ... } } } }` */
    static makeResolver(
        typeCache: TypeCache,
        factory: NodeFactory,
    ): GraphQLFieldConfig<unknown, Context, { data: Dict<any> }> {
        return {
            type: NodeType.makeOutputType(typeCache, factory, 'nodeOutput'),
            args: {
                data: {
                    type: NodeType.makeInputType(typeCache, factory, 'nodeInput'),
                },
            },
            resolve(obj, args, rootContext) {
                return AccessRights.runSecure(rootContext, 'read', { factory, args }, async () => {
                    if (args.data) {
                        args.data = await NodeType.nodeValuesIn(rootContext, args.data, factory, {
                            omitNulls: true,
                        });
                    }
                    return rootContext.withExternalIds(async () => {
                        const node = await rootContext.create(factory.nodeConstructor, args.data || {}, {
                            isTransient: true,
                            isOnlyForDefaultValues: true,
                        });
                        // Do not call node.$.control() for now.
                        // We may need an option for this, and we should not call control if we already caught
                        // an exception in the create call.
                        return node;
                    });
                });
            },
        };
    }
}
