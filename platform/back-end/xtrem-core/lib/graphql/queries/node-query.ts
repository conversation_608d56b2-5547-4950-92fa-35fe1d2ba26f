import { DataInputError, Dict } from '@sage/xtrem-shared';
import { GraphQLFieldConfig, GraphQLInterfaceType, GraphQLObjectType } from 'graphql';
import { Context, NodeFactory } from '../../runtime';
import { loggers } from '../../runtime/loggers';
import { NodeQueryOptions } from '../../ts-api';
import { Paging, QueryArgs, QueryPage } from '../paging/paging';
import { AccessRights } from '../security/access-rights';
import { TypeCache } from '../utils/type-cache';
import { GetTotalCount } from './get-total-count';

const logger = loggers.graphQl;

/**
 * @internal
 *
 * Static class which provides the GraphQL schema resolver for the main query on a node:
 *
 * ``` graphql
 *  query {
 *      xtremFoo {
 *          Bar {
 *              query(...) { ... }
 *          }
 *      }
 *  }
 *  ```
 */
export class NodeQuery {
    // The resolver's type
    static makeType(
        typeCache: TypeCache,
        factory: NodeFactory,
        nodeType: GraphQLObjectType | GraphQLInterfaceType,
    ): GraphQLObjectType {
        const name = `${nodeType.toString()}_Query`;
        return typeCache.internType(name, () => {
            return new GraphQLObjectType({
                name,
                fields: {
                    totalCount: GetTotalCount.makeResolver(factory),
                    edges: Paging.makeEdgesResolver(typeCache, nodeType),
                    pageInfo: Paging.makePageInfoResolver(),
                },
            });
        });
    }

    // The resolve callback
    private static resolveNodeQuery(
        context: Context,
        factory: NodeFactory,
        pagingOptions: NodeQueryOptions,
        args: Dict<any>,
    ): Promise<QueryPage> {
        return AccessRights.runSecure(context, 'read', { factory, args }, async () => {
            const nodes = await context
                .query(factory.nodeConstructor, {
                    ...pagingOptions,
                    first: pagingOptions.first && pagingOptions.first + 1,
                    last: pagingOptions.last && pagingOptions.last + 1,
                })
                .toArray();
            return Paging.buildOutputPage({ items: nodes }, pagingOptions, true);
        });
    }

    /** Resolver for `query { xtremFoo { Bar { query(...) { ... } } } }` */
    static makeResolver(
        typeCache: TypeCache,
        factory: NodeFactory,
        nodeType: GraphQLObjectType | GraphQLInterfaceType,
    ): GraphQLFieldConfig<unknown, Context, QueryArgs> {
        return {
            type: NodeQuery.makeType(typeCache, factory, nodeType),
            args: Paging.getPagingArgumentTypes(),
            resolve(obj, args, rootContext, info) {
                return AccessRights.runSecure(rootContext, 'read', { factory }, async () => {
                    const context = rootContext;
                    try {
                        const pagingOptions = await Paging.parsePagingOptions(
                            context,
                            factory,
                            args,
                            info,
                            factory.defaultOrderBy,
                        );
                        return {
                            context,
                            // query
                            pagePromise: NodeQuery.resolveNodeQuery(context, factory, pagingOptions, args),
                            pagingOptions, // for totalCount
                        };
                    } catch (e) {
                        logger.error(e.stack);
                        throw new DataInputError(e.message, e);
                    }
                });
            },
        };
    }
}
