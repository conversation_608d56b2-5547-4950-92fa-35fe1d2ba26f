import { GraphQLFieldConfig, GraphQLInt } from 'graphql';
import { Context, NodeFactory } from '../../runtime';
import { PageResolver } from '../paging/paging';
import { AccessRights } from '../security/access-rights';

/**
 * @internal
 *
 * Static class which provides the GraphQL schema resolver for the `totalCount` elements.
 */
export class GetTotalCount {
    /** Resolver for `totalCount` */
    static makeResolver(factory: NodeFactory): GraphQLFieldConfig<PageResolver, Context, {}> {
        return {
            type: GraphQLInt,
            async resolve(obj, args, rootContext) {
                if (obj.pageInfo && obj.pageInfo.totalCount !== undefined) {
                    // Collection property
                    return obj.pageInfo.totalCount;
                }

                if (obj?.pagePromise instanceof Promise) {
                    const resolvedObj = await obj.pagePromise;
                    if (resolvedObj.pageInfo?.totalCount !== undefined) {
                        return resolvedObj.pageInfo?.totalCount;
                    }
                }

                if (!obj.pagePromise) {
                    if (obj.edges) {
                        // Collection property
                        return obj.edges.length;
                    }
                }
                const queryFilter = obj.pagingOptions?.filter;

                return AccessRights.runSecure(rootContext, 'read', { factory }, () => {
                    const context = obj.context;
                    return context.queryCount(factory.nodeConstructor, { filter: queryFilter });
                });
            },
        };
    }
}
