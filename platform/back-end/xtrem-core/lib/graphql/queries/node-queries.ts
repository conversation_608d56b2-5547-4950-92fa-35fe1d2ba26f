import { schemaTypeName } from '@sage/xtrem-shared';
import { GraphQLFieldConfig, GraphQLFieldConfigMap, GraphQLObjectType } from 'graphql';
import { Context, NodeFactory } from '../../runtime';
import { AggregateQuery } from '../aggregates/aggregate-query';
import { AggregateRead } from '../aggregates/aggregate-read';
import { NodeOperations } from '../metadata-schema/node-operations';
import { NodeType } from '../types/node-type';
import { OperationType } from '../types/operation-type';
import { TypeCache } from '../utils/type-cache';
import { GetDefaults } from './get-defaults';
import { GetDuplicate } from './get-duplicate';
import { Lookups } from './lookups';
import { NodeQuery } from './node-query';
import { NodeRead } from './node-read';

/**
 * @internal
 *
 * Static class which provides the GraphQL schema resolver for all the queries of a node:
 *
 * ``` graphql
 *  query {
 *      xtremFoo {
 *          Bar { ... }
 *      }
 *  }
 *  ```
 */

export class NodeQueries extends NodeOperations {
    // The resolver's type
    private static makeType(typeCache: TypeCache, factory: NodeFactory): GraphQLObjectType | undefined {
        const nodeType = NodeType.makeOutputType(typeCache, factory, 'nodeOutput');
        const name = `${schemaTypeName(factory.fullName)}_Connection`;
        const fields = {} as GraphQLFieldConfigMap<any, any>;

        if (factory.storage === 'sql' || factory.storage === 'external') {
            fields.query = NodeQuery.makeResolver(typeCache, factory, nodeType);
            fields.read = NodeRead.makeResolver(typeCache, factory);

            if (factory.properties.find(property => property.isReferenceProperty() && property.canLookup))
                fields.lookups = Lookups.makeResolver(typeCache, factory, nodeType);

            fields.queryAggregate = AggregateQuery.makeResolver(typeCache, factory);
            fields.readAggregate = AggregateRead.makeResolver(typeCache, factory);
            fields.getDefaults = GetDefaults.makeResolver(typeCache, factory);
            if (factory.canCreate) {
                fields.getDuplicate = GetDuplicate.makeResolver(typeCache, factory);
            }
        }

        OperationType.addOperationFields(typeCache, factory, name, factory.queries, false, fields);

        if (Object.keys(fields).length === 0) return undefined;

        return typeCache.internType(name, () => {
            return new GraphQLObjectType({ name, fields });
        });
    }

    /** Resolver for `query { xtremFoo { Bar { ... } } }` */
    static makeResolver(
        typeCache: TypeCache,
        factory: NodeFactory,
    ): GraphQLFieldConfig<unknown, Context, {}> | undefined {
        const connectionType = NodeQueries.makeType(typeCache, factory);
        if (!connectionType) return undefined;
        return {
            description: factory.getNodeSchemaDescription(),
            type: connectionType,
            resolve() {
                return {};
            },
        };
    }
}
