import { Dict } from '@sage/xtrem-shared';
import { GraphQLFieldConfig, GraphQLInterfaceType, GraphQLObjectType, GraphQLString } from 'graphql';
import { ReferenceArrayProperty, ReferenceProperty } from '../../properties';
import { Context, NodeFactory } from '../../runtime';
import { NodeQueryOptions } from '../../ts-api';
import { PageResolver } from '../paging/paging';
import { NodeType } from '../types/node-type';
import { TypeCache } from '../utils/type-cache';
import { LookupQuery } from './lookup-query';

/** @internal */
export interface LookupsArgs {
    _id?: string;
    data?: object;
}

/** @internal */
export interface ParentLookupsArgs {
    lookupArgs: LookupsArgs;
    factory: NodeFactory;
    parentFactory: NodeFactory;
    propertyName: string;
    pagingOptions: NodeQueryOptions;
}

/** @internal */
export interface ReferenceLookupsArgs<T = any> extends PageResolver<T> {
    parentArgs: ParentLookupsArgs;
}

/**
 * @internal
 *
 * Static class which provides the GraphQL schema resolver a node's lookups:
 *
 * ``` graphql
 *  query {
 *      xtremFoo {
 *          Bar {
 *              lookups { ... }
 *          }
 *      }
 *  }
 *  ```
 */
export class Lookups {
    // The resolver's type
    private static makeLookupsType(
        typeCache: TypeCache,
        factory: NodeFactory,
        nodeType: GraphQLObjectType | GraphQLInterfaceType,
    ): GraphQLObjectType {
        const name = `${nodeType.toString()}_Lookups`;

        return typeCache.internType(name, () => {
            const referenceProperties: Dict<any> = {};
            factory.properties
                .filter(property => property.isReferenceProperty() || property.isReferenceArrayProperty())
                .forEach((property: ReferenceProperty | ReferenceArrayProperty) => {
                    const propertyFactory = property.targetFactory;

                    const propertyType = NodeType.makeOutputType(typeCache, propertyFactory, 'nodeOutput');

                    referenceProperties[property.name] = LookupQuery.makeResolver(
                        typeCache,
                        propertyFactory,
                        factory,
                        propertyType,
                        property.name,
                    );
                });

            return new GraphQLObjectType({
                name,
                fields: referenceProperties,
            });
        });
    }

    /** Resolver for `query { xtremFoo { Bar { lookups { ... } } } }` */
    static makeResolver(
        typeCache: TypeCache,
        factory: NodeFactory,
        nodeType: GraphQLObjectType | GraphQLInterfaceType,
    ): GraphQLFieldConfig<unknown, Context, LookupsArgs> {
        return {
            type: Lookups.makeLookupsType(typeCache, factory, nodeType),
            args: {
                _id: { type: GraphQLString },
                data: { type: NodeType.makeInputType(typeCache, factory, 'nodeInput') },
            },
            resolve(obj, args) {
                return args;
            },
        };
    }
}
