import { GraphQLFieldConfig } from 'graphql';
import { Context, NodeFactory } from '../../runtime';
import { AccessRights } from '../security/access-rights';
import { idInputType } from '../types/basic-types';
import { NodeType } from '../types/node-type';
import { TypeCache } from '../utils/type-cache';

/**
 * @internal
 *
 * Static class which provides the GraphQL schema resolver for a node's read operation:
 *
 * ``` graphql
 *  query {
 *      xtremFoo {
 *          Bar {
 *              read(...) { ... }
 *          }
 *      }
 *  }
 *  ```
 */
export class NodeRead {
    /** Resolver for `query { xtremFoo { Bar { read(...) { ... } } } }` */
    static makeResolver(
        typeCache: TypeCache,
        factory: NodeFactory,
    ): GraphQLFieldConfig<unknown, Context, { _id: string }> {
        return {
            type: NodeType.makeOutputType(typeCache, factory, 'nodeOutput'),
            args: { _id: { type: idInputType } },
            resolve(obj, args, rootContext) {
                return AccessRights.runSecure(rootContext, 'read', { factory, args }, () =>
                    rootContext.tryRead(factory.nodeConstructor, {
                        _id: args._id,
                    }),
                );
            },
        };
    }
}
