import { GraphQLFieldConfig } from 'graphql';
import { StateDuplicate } from '../../node-state/state-duplicate';
import { Context, NodeFactory } from '../../runtime';
import { AccessRights } from '../security/access-rights';
import { idInputType } from '../types/basic-types';
import { NodeType } from '../types/node-type';
import { TypeCache } from '../utils/type-cache';

/**
 * @internal
 *
 * Static class which provides the GraphQL schema resolver for the `getDuplicate` operation:
 *
 * ``` graphql
 *  query {
 *      xtremFoo {
 *          Bar {
 *              getDuplicate(...) { ... }
 *          }
 *      }
 *  }
 *  ```
 */
export class GetDuplicate {
    /** Resolver for `query { xtremFoo { Bar { getDuplicate(...) { ... } } } }` */
    static makeResolver(
        typeCache: TypeCache,
        factory: NodeFactory,
    ): GraphQLFieldConfig<unknown, Context, { _id: string }> {
        return {
            type: NodeType.makeOutputType(typeCache, factory, 'nodeOutput'),
            args: { _id: { type: idInputType } },
            resolve(_obj, args, rootContext) {
                return AccessRights.runSecure(rootContext, 'read', { factory, args }, () => {
                    return rootContext.withExternalIds(async () => {
                        const node = await rootContext.read(factory.nodeConstructor, { _id: args._id });
                        const duplicateState = await StateDuplicate.getDuplicate(node.$.state, { limitCollections: 1 });
                        return duplicateState.node;
                    });
                });
            },
        };
    }
}
