import { setSqlRegistryHook } from '@sage/xtrem-shared';
import { registerSqlFunction } from '@sage/xtrem-ts-to-sql';

// Re-export transformers from 'xtrem-i18n' in order to avoid circular dependencies
export * from '@sage/xtrem-async-helper';
export { ConfigManager } from '@sage/xtrem-config';
export { Datetime, DatetimeRange, DateValue, time } from '@sage/xtrem-date-time';
export { toDecimal, toInteger, toNumber } from '@sage/xtrem-decimal';
export { localize, localizeEnumMember } from '@sage/xtrem-i18n';
export { Logger, sourceMapSetup } from '@sage/xtrem-log';
export { DatabaseError } from '@sage/xtrem-postgres';
export {
    asyncFireAndForget,
    AuthorizationError,
    BusinessRuleError,
    DataInputError,
    Dict,
    InterruptException,
    isJson,
    LocalizedError,
    LocalizeLocale,
    LogicError,
    MAX_INT_32,
    MIN_INT_32,
    SystemError,
    titleCase,
    tryJsonParse,
    ValidationSeverity,
    withRethrow,
} from '@sage/xtrem-shared';
export {
    allowedStartChannels,
    Application,
    ApplicationCreateOptions,
    ApplicationCreateSqlSchema,
    ApplicationManager,
    ApplicationStartServicesOptions,
    ApplicationType,
    AttachmentAssociationData,
    AttachmentData,
    AttachmentManager,
    ClientSettings,
    ClientSettingsManager,
    ClientUserSettings,
    CsvChecksumManager,
    DashboardManager,
    DataValidationManager,
    DataValidator,
    getPackageName,
    getPackageQueueName,
    gracefulShutdown,
    HotUpgradeManager,
    InstanceDataValidationResult,
    InstanceDataValidationResultLine,
    NoteManager,
    NotificationManager,
    Package,
    PackageApi,
    PackageJsonFile,
    PrintingManager,
    ServiceOption,
    StartChannel,
    TagManager,
    UiBroadcaster,
    UiBroadcastMessage,
    unhandledErrorMonitor,
    UpgradeMetricsType,
} from './application';
export { AuditManagerInterface } from './hooks/auditing';
export {
    WorkflowManagerInterface,
    WorkflowMock,
    WorkflowMockOptions,
    WorkflowResult,
    WorkflowRunOptions,
    WorkflowStartEvent,
    WorkflowStepDescriptor,
    WorkflowStepIcon,
    workflowStepIconDataType,
    WorkflowStepInterface,
    WorkflowStepUiDescriptor,
    WorkflowStepUiOptions,
} from './workflow';

export * from '@sage/xtrem-metrics';
export { getRegion, S3Bucket, S3ConfigurationType, S3Helper, S3Manager, S3ObjectInfo } from './aws';
export { withDetachedClsContext } from './concurrency-utils';
export * from './decorators';
export { fileExists } from './file-utils';
export * from './graphql/index';
export { AccessBinding } from './graphql/security/access-bindings';
export * from './html';
export {
    AppConstructOptions as AppOptions,
    GraphQlRequestOptions,
    GraphQlResponse,
    interopApp,
    InteropAppHealthMonitor,
    InteropAppInfo,
    InteropAuthConfig,
    InteropGraphqlClient,
    NotificationTopic,
    NotificationTopicConstructOptions as NotificationTopicOptions,
} from './interop';
export { lazyLoadedMarker } from './node-state';
export * from './properties/enum-property';
export * from './properties/string-property';
export * from './runtime/core-hooks';
export {
    AccessRightsManager,
    ActivityInfo,
    adminDemoPersona,
    applyFilter,
    BulkUpdateOptions,
    ContainerManager,
    Context,
    ContextInternal,
    CreateAdminUserOptions,
    CustomerInfo,
    CustomRecordInterface,
    DataSettingsManager,
    friendlyJsonParse,
    friendlyOperationSignature,
    FriendlyParameter,
    getLanguageFromLocale,
    getServerUrl,
    globalRunningContext,
    isCompound,
    isScalar,
    LocalizationManager,
    monitoredFunnel,
    nanoIdDataType,
    NodeIndex,
    PackAllocationInterface,
    PackVersionInterface,
    PackVersionInterfaceWithId,
    PackVersionOptions,
    personaCookieDefinition,
    PropertyAndValue,
    ReferringProperty,
    retry,
    rootUserEmail,
    StandardOperation,
    supportReadonlyUserEmail,
    supportUserEmail,
    SysGlobalLock,
    TenantInfo,
    TenantInfoBase,
    TenantManager,
    UpdateSetFunctionSet,
    UserAccess,
    UserData,
    UserInfo,
    UserNavigationInfo,
    valueComparator,
    withAdvisoryLock,
} from './runtime/index';
export { htmlSanitizer, JwtClaims, registerTlsChangeListener, setup as setupSecurity } from './security';
export {
    DataDiagnosis,
    DataDiagnosisCategory,
    DataDiagnosisSeverity,
    garbageCollectContentAddressableTable,
    garbageCollectContentAddressableTables,
    OrderByClause,
    SqlNaturalKeyUtils,
    SqlValueConverter,
    tenantIdColumnName,
    TriggerBuilder,
} from './sql';
export { SyncInfo } from './synchronization/sync-info';
export * from './system-exports';
export {
    ArtifactDataType,
    ArtifactDataTypeEnum,
    ArtifactManager,
    PackArtifactInterface,
} from './system/artifact-manager';
export {
    ApplicationArtifact,
    ClientArtifactMetadata,
    ClientArtifactType,
    JsPackArtifact,
    MetaPackArtifact,
} from './system/pages/client-service';
export * from './test/index';
export * from './ts-api';
export {
    BinaryStreamDataType,
    DataType,
    DecimalDataType,
    DecimalDataTypeOptions,
    Enum,
    EnumDataType,
    EnumDataTypeOptions,
    EnumType,
    InternalPropertyJoin,
    JoinMember,
    JsonDataType,
    JsonDataTypeOptions,
    MimeTypeHelper,
    notNull,
    ReferenceDataType,
    ReferenceDataTypeOptions,
    RoundingMode,
    StreamDataTypeOptions,
    StringArrayDataType,
    StringDataType,
    StringDataTypeOptions,
    TextStreamDataType,
} from './types';
export {
    BinaryStream,
    ColumnMetadata,
    customFieldTypeDefaultValue,
    FactoryMetadata,
    SqlFileDataRow,
    SqlFileDataSet,
    SqlFileDataSets,
    Stream,
    TextStream,
    typeDefaultValue,
    Uuid,
} from './types/index';
export { fastHash, getNameWithoutPackage, parseCursor, parseTypeValue } from './types/util';
export * from './utils/anonymize';
export * from './utils/check-recompute-values-hash';
export * from './utils/record-paging';
export { registerSqlFunction };
setSqlRegistryHook(registerSqlFunction);

export { FileStorageManager } from './file-storage';
export { bodyLoggerMiddleware, getRequestId, loggerAllowsCompress, requestLoggerMiddleware } from './http';
export { json5Stringify } from './utils/log-helper';

export * as enums from './enums';
