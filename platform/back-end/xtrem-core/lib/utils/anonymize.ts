import * as crypto from 'crypto';

/**
 * Default list of characters to preserve from the original string when randomizing the string
 */
export const defaultPreserveList = '\'\\ "@!#$%^&*()-=_+,.?<>/|:;{}[]«»ºª~€¨£§';

/**
 * Randomize a string to have a similar structure as the original by replacing each character with a random
 * character of the same type (uppercase, lowercase or digit) and preserving a specific list of characters.
 * Other characters from the original, like non ASCII Unicode characters are not supported at this stage and
 * will be replaced with random lowercase letters.
 *
 * Because we translate the byte value to a number using the modulus of the required range, the random characters
 * will have a slight bias, so do not use this for any security related functions.
 *
 * Please see here for more information:
 * https://codeql.github.com/codeql-query-help/javascript/js-biased-cryptographic-random/
 *
 * @param original String to use as a structure template
 * @returns Randomized string
 */
export function unsafeRandomizeCharacters(original: string, preserveList = defaultPreserveList): string {
    const randomValues = crypto.randomBytes(original.length);
    return original
        .split('')
        .map((char, index) => {
            if (preserveList.includes(char)) return char;

            if (char >= '0' && char <= '9')
                return String.fromCharCode(48 + Math.floor(randomValues.readUInt8(index) % 10));

            if (char >= 'A' && char <= 'Z')
                return String.fromCharCode(65 + Math.floor(randomValues.readUInt8(index) % 26));

            // Default is to replace any other character with a lowercase letter
            return String.fromCharCode(97 + Math.floor(randomValues.readUInt8(index) % 26));
        })
        .join('');
}

/**
 * Randomize a URL
 *
 * @returns URL that satisfies the URL regex
 */
export function randomizeUrl(original: string): string {
    const parts = original.split('://');
    return parts.length > 1
        ? parts
              .map((value, index) => {
                  return index ? unsafeRandomizeCharacters(value) : value;
              })
              .join('://')
        : unsafeRandomizeCharacters(original);
}

/**
 * Performs a loose comparison of strings.
 * Characters are considered equal when they belong to the same class (digit, lowercase letter, uppercase letter, etc.)
 */
export function compareAnonymizedStrings(
    strOriginalIn: string,
    strCompare: string,
    preserveList = defaultPreserveList,
): boolean {
    const quoted = /^"(.*)"$/.exec(strOriginalIn) || [];
    const strOriginal = quoted[1] ?? strOriginalIn;

    if (strOriginal.length !== strCompare.length) {
        return false;
    }

    for (let i = 0; i < strOriginal.length; i += 1) {
        const char1 = strOriginal.charCodeAt(i);
        const char2 = strCompare.charCodeAt(i);

        // Check if the characters match, ie
        // A lowercase, uppercase or digit should be replaced by the same
        if (
            (char1 >= 65 && char1 <= 90 && (char2 < 65 || char2 > 90)) ||
            (char1 >= 97 && char1 <= 122 && (char2 < 97 || char2 > 122)) ||
            (char1 >= 48 && char1 <= 57 && (char2 < 48 || char2 > 57))
        ) {
            return false;
        }

        // Check if character was preserved
        if (preserveList.includes(String.fromCharCode(char1)) && char1 !== char2) return false;
    }
    return true;
}
