import { AnyRecord, AnyValue, AsyncResponse, asyncArray } from '@sage/xtrem-async-helper';
import { Dict, LogicError } from '@sage/xtrem-shared';
import { omit } from 'lodash';
import { Property } from '../properties';
import { Context, NodeFactory, PropertyAndValue, dateFromString, isCompound, valueComparator } from '../runtime';
import { SystemProperties } from '../runtime/system-properties';
import { OrderByClause } from '../sql';
import { AnyFilterObject, AnyFilterValue, Node, NodeQueryOptions } from '../ts-api';
import { parseCursor } from '../types';

type Comparator = (val1: AnyValue, val2: AnyValue) => AsyncResponse<number>;

type GetPropertyValue = (context: Context, property: Property, parent: AnyRecord) => AsyncResponse<AnyValue>;

type Predicate = (val: AnyValue) => boolean;

type Op = (val: AnyValue, parent?: AnyValue) => Predicate;

export class RecordPaging {
    /**
     * Compose two comparators into a new comparator
     * @param f
     * @param g
     * @returns
     */
    static composeComparators(f: Comparator, g: Comparator): Comparator {
        return async (val1: AnyValue, val2: AnyValue) => {
            const fResult = await f(val1, val2);
            if (fResult !== 0) return fResult;
            return g(val1, val2);
        };
    }

    /**
     * Compile the orderBy clause into a comparator
     * @param context
     * @param factory
     * @param orderBy
     * @param getPropertyValue
     * @returns
     */
    static async compileOrderBy(
        context: Context,
        factory: NodeFactory,
        orderBy: AnyRecord,
        getPropertyValue: GetPropertyValue,
    ): Promise<Comparator> {
        let out: Comparator = () => 0;
        await asyncArray(Object.keys(orderBy)).forEach(async (propertyName, idx) => {
            const dir = orderBy[propertyName];
            let newComparator: Comparator;
            const property = factory.findProperty(propertyName, {
                includeSystemProperties: true,
            });
            if (dir === -1 || dir === 1) {
                newComparator = async (parent1: AnyRecord, parent2: AnyRecord): Promise<number> => {
                    if (parent1 === parent2) {
                        return 0;
                    }
                    if (parent1 == null) return parent2 == null ? 0 : -dir;
                    if (parent2 == null) return dir;
                    const val1 = await getPropertyValue(context, property, parent1);
                    const val2 = await getPropertyValue(context, property, parent2);

                    return valueComparator(val1, val2, context.locales) * dir;
                };
            } else {
                if (!property.isForeignNodeProperty()) throw new Error(`Invalid orderBy property: ${propertyName}`);
                const p = await RecordPaging.compileOrderBy(
                    context,
                    property.targetFactory,
                    dir as AnyRecord,
                    getPropertyValue,
                );
                newComparator = (val1: Dict<AnyValue>, val2: Dict<AnyValue>) =>
                    p(val1[propertyName], val2[propertyName]);
            }
            if (idx === 0) out = newComparator;
            else out = RecordPaging.composeComparators(out, newComparator);
        });
        return out;
    }

    /**
     * Convert a filter object into a predicate function that can be used to filter an array of objects.
     * The filter object can contain the following operators:
     * - _eq: equal
     * - _ne: not equal
     * - _gt: greater than
     * - _gte: greater than or equal
     * - _lt: less than
     * - _lte: less than or equal
     * - _in: in
     * - _nin: not in
     * - _and: and
     * - _or: or
     * - _nor: nor
     * - _not: not
     * - _mod: mod
     * - _regex: regex
     * And the follow quantifiers:
     * - _atLeast: at least
     * - _atMost: at most
     * - _none: none
     * - _every: every
     *
     * @param locales
     * @returns
     */
    static converter(locales?: string[]): (val: AnyValue) => Predicate {
        const pfalse: Predicate = () => false;
        const ptrue: Predicate = () => true;

        // function to check is value passed is an array, if not then return the value converted to a single value array
        const valueToArray = (value: AnyValue): AnyValue[] => {
            if (Array.isArray(value)) return value;

            return [value];
        };

        const reTest = (re: RegExp) => (val: AnyValue) => typeof val === 'string' && re.test(val);
        const not =
            (predicate: Predicate): ((arg1: AnyValue) => boolean) =>
            (obj: AnyValue) =>
                !predicate(obj);

        const or = (predicates: Predicate[]): Predicate => {
            if (predicates.length === 0) return pfalse;
            if (predicates.length === 1) return predicates[0];
            return (obj: AnyValue) => predicates.some(predicate => predicate(obj));
        };

        const and = (predicates: Predicate[]): Predicate => {
            if (predicates.length === 0) return ptrue;
            if (predicates.length === 1) return predicates[0];
            return (obj: AnyValue) => predicates.every(predicate => predicate(obj));
        };

        const compose =
            (f: Predicate, g: Predicate): ((arg1: AnyValue) => boolean) =>
            (obj: AnyValue) =>
                f(g(obj));

        const deref = (key: string) => (obj: AnyRecord) => {
            if (obj == null) return undefined;
            const v = obj[key];
            return typeof v === 'function' ? v() : v;
        };

        const ops: { [name: string]: Op } = {
            _eq: val => v => valueComparator(v, val, locales) === 0,
            _ne: val => v => valueComparator(v, val, locales) !== 0,
            _gt: val => v => valueComparator(v, val, locales) > 0,
            _gte: val => v => valueComparator(v, val, locales) >= 0,
            _lt: val => v => valueComparator(v, val, locales) < 0,
            _lte: val => v => valueComparator(v, val, locales) <= 0,
            _in: val => v => valueToArray(val).some(elt => valueComparator(elt, v, locales) === 0),
            _nin: val => v => valueToArray(val).every(elt => valueComparator(elt, v, locales) !== 0),
            _and: val => and(valueToArray(val).map(cvt)),
            _or: val => or(valueToArray(val).map(cvt)),
            _nor: val => not(or(valueToArray(val).map(cvt))),
            _not: val => not(cvt(val)),
            _exists: val => v =>
                (typeof val === 'string' || typeof val === 'number') && v != null && typeof v === 'object' && val in v,
            _type: val => v => typeof v === val,
            _mod: val => v =>
                Array.isArray(val) && typeof val[0] === 'number' && typeof v === 'number' && v % val[0] === val[1],
            _regex: (val, parent) => {
                if (typeof val !== 'string') throw new LogicError(`invalid regex type:${typeof val}`);
                const re = new RegExp(val, ((parent as AnyRecord)._options as string) || '');
                return v => typeof v === 'string' && re.test(v);
            },
        };

        const walk: (p: string) => Predicate = p => {
            const i = p.indexOf('.');
            if (i >= 0) {
                return compose(walk(p.substring(i + 1)), walk(p.substring(0, i)));
            }
            return deref(p);
        };

        const isQuantifiedBy = (val: object, quantifier: string): boolean => quantifier in val;

        const quantifiedFilter = (
            val: AnyFilterObject,
            quantifier: string,
            fn: (count: number, filterObj: AnyFilterObject) => boolean,
        ): ((v: AnyRecord) => boolean) => {
            const condition = cvt(omit(val, quantifier));
            return v => {
                let count = 0;
                // If the filter is on the parent level but filters a collection
                // { children: { _every: true, intVal: { _eq: 1 } } }
                // The item passed is the collection
                if (Array.isArray(v)) {
                    count = v.filter(condition).length;
                }

                return fn(count, val);
            };
        };

        const isFilterObject = (val: AnyFilterValue): val is AnyFilterObject => {
            return val != null && typeof val === 'object';
        };

        const cvt: (val: AnyValue) => Predicate = val => {
            if (val instanceof RegExp) {
                return reTest(val);
            }
            if (isFilterObject(val) && isQuantifiedBy(val, '_atLeast')) {
                return quantifiedFilter(val, '_atLeast', (count, filterObj) => count >= (filterObj._atLeast as number));
            }
            if (isFilterObject(val) && isQuantifiedBy(val, '_atMost')) {
                return quantifiedFilter(val, '_atMost', (count, filterObj) => count <= (filterObj._atMost as number));
            }
            if (isFilterObject(val) && isQuantifiedBy(val, '_none')) {
                return quantifiedFilter(val, '_none', (count, filterObj) =>
                    filterObj._none ? count === 0 : count !== 0,
                );
            }
            if (isFilterObject(val) && isQuantifiedBy(val, '_every')) {
                // every x satisfies y <==> none x satisfies not y
                return cvt({
                    _not: { ...omit(val, '_every') },
                    _none: val && val._every,
                });
            }

            if (val && isCompound(val)) {
                return and(
                    Object.keys(val).map(k => {
                        const v = (val as any)[k];
                        if (k === '_options') {
                            // Skip '_options', it will only be used by the _regex
                            // Just return ptrue as it will have no effect on the 'and'
                            return ptrue;
                        }
                        if (k[0] === '_' && k !== '_id' && k !== '_sortValue') {
                            if (!ops[k]) throw new LogicError(`bad operator: ${k}`);
                            return ops[k](v, val);
                        }
                        return compose(cvt(v), walk(k));
                    }),
                );
            }
            return ops._eq(val);
        };
        return cvt;
    }

    /**
     * We will receive the data object with the references, collections and referencesArrays filled with the _id
     * We will fill the data with the actual data from the data store, so that the Predicate can be applied.
     * We will only fill the data that is needed for the filter.
     * @param context
     * @param factory
     * @param getPropertyValue
     * @param items
     * @param filter
     * @returns
     */
    static fillData(
        context: Context,
        factory: NodeFactory,
        getPropertyValue: GetPropertyValue,
        items: AnyRecord[],
        obj: AnyFilterValue,
    ): Promise<AnyRecord[]> {
        const fill = async (
            currentFactory: NodeFactory,
            item: AnyRecord,
            currentObj: AnyFilterValue,
        ): Promise<AnyRecord> => {
            let res: AnyRecord = { ...item };
            if (currentObj && typeof currentObj === 'object') {
                await asyncArray(Object.keys(currentObj)).forEach(async key => {
                    const property = currentFactory.properties.find(prop => prop.name === key);
                    const nextObj = (currentObj as AnyFilterObject)[key];
                    if (nextObj) {
                        let keyValue = res[key];
                        if (property) {
                            if (property.isForeignNodeProperty() && keyValue && typeof nextObj === 'object') {
                                const { targetFactory } = property;
                                keyValue = await getPropertyValue(context, property, item);
                                if (Array.isArray(keyValue)) {
                                    res[key] = await asyncArray(keyValue)
                                        .map(elt => fill(targetFactory, elt, nextObj))
                                        .toArray();
                                } else if (typeof keyValue === 'object') {
                                    res[key] = await fill(targetFactory, keyValue as AnyRecord, nextObj);
                                }
                            }
                        } else if (Array.isArray(nextObj)) {
                            // We are in a _and, or _or, _nor, _not ...
                            await asyncArray(nextObj).forEach(async elt => {
                                res = await fill(factory, res, elt);
                            });
                        } else if (typeof nextObj === 'object') {
                            // We are in a nested filter
                            res = await fill(factory, res, nextObj);
                        }
                    }
                });
            }

            return res;
        };

        return asyncArray(items)
            .map(i => fill(factory, i, obj))
            .toArray();
    }

    /**
     * Apply the filter to a list of items
     * @param context
     * @param factory
     * @param getPropertyValue
     * @param items
     * @param filter
     * @param locales
     * @returns
     */
    static async applyFilter(
        context: Context,
        factory: NodeFactory,
        getPropertyValue: GetPropertyValue,
        items: AnyRecord[],
        filter: AnyFilterValue,
        locales?: string[],
    ): Promise<AnyRecord[]> {
        const parsedFilter = RecordPaging.parseFilter(context, factory, filter);
        const predicate = RecordPaging.converter(locales)(parsedFilter);
        const filledItems = await this.fillData(context, factory, getPropertyValue, items, parsedFilter);
        const filterResult = filledItems
            .filter(item => {
                return predicate(item);
            })
            .map(item => item._id);
        return items.filter(item => filterResult.includes(item._id));
    }

    /**
     * Apply the orderBy clause to a list of items
     * @param context
     * @param factory
     * @param items
     * @param orderBy
     * @param getPropertyValue
     * @returns
     */
    static async applyOrderBy(
        context: Context,
        factory: NodeFactory,
        items: AnyRecord[],
        orderBy: AnyRecord,
        getPropertyValue: GetPropertyValue,
    ): Promise<AnyRecord[]> {
        const indexedItems = items.map((item, i) => ({ ...item, $$index: i }));
        const filledItems = await this.fillData(context, factory, getPropertyValue, indexedItems, orderBy);

        const orderedList = await asyncArray(filledItems)
            .sort(async (elt1, elt2) =>
                (await RecordPaging.compileOrderBy(context, factory, orderBy, getPropertyValue))(elt1, elt2),
            )
            .toArray();

        return orderedList.map(elt => items[elt.$$index as number]);
    }

    /**
     * Apply the paging operation (before, after) to a list of items
     * @param context
     * @param factory
     * @param getPropertyValue
     * @param items
     * @param orderByClauses
     * @param values
     * @param op
     * @returns
     */
    static applyPagingOperation(
        context: Context,
        factory: NodeFactory,
        getPropertyValue: GetPropertyValue,
        items: AnyRecord[],
        orderByClauses: OrderByClause[],
        values: PropertyAndValue[],
        op: 'after' | 'before',
    ): Promise<AnyRecord[]> {
        const parts = orderByClauses.map((orderByClause, i) => {
            const ands = [] as AnyFilterValue[];
            for (let j = 0; j < i; j += 1) {
                // We pass in the order by clause path joined by `.` as the converter will resolve each part to the correct value
                // if the path down a reference property tree
                ands.push({ [orderByClauses[j].path.join('.')]: { _eq: values[j].value } });
            }

            let operator;
            if (orderByClause.direction > 0) {
                operator = op === 'before' ? '_lt' : '_gt';
            } else {
                operator = op === 'before' ? '_gt' : '_lt';
            }
            // We pass in the order by clause path joined by `.` as the converter will resolve each part to the correct value
            // if the path down a reference property tree
            ands.push({ [orderByClauses[i].path.join('.')]: { [operator]: values[i].value } });
            return ands.length === 1 ? ands[0] : { _and: ands };
        });

        const filters = parts.length === 1 ? parts[0] : { _or: parts };
        return RecordPaging.applyFilter(context, factory, getPropertyValue, items, filters);
    }

    /**
     * Convert the filter object into a format that can be used by the query
     * @param context
     * @param factory
     * @param filter
     * @param parentPath
     * @param property
     * @returns
     */
    static convertFilter(
        context: Context,
        factory: NodeFactory,
        filter: AnyFilterValue | undefined,
        parentPath: string[] = [],
        property?: Property,
    ): AnyFilterValue | any[] | undefined {
        if (!filter) return filter;
        if (Array.isArray(filter)) {
            if (filter.length === 0) return [];
            const arrayOfResult = filter
                .map((val, i) =>
                    RecordPaging.convertFilter(context, factory, val, [...parentPath, String(i)], property),
                )
                .filter(element => element !== undefined);
            return arrayOfResult.length === 0 ? undefined : arrayOfResult;
        }

        if (property?.isForeignNodeProperty()) {
            if (typeof filter === 'string' || typeof filter === 'number') {
                return { _id: filter };
            }

            if (filter instanceof Node) {
                return { _id: filter._id };
            }
        }

        if (property?.isDateProperty() && typeof filter === 'string') {
            return dateFromString(filter);
        }

        if (isCompound(filter)) {
            return Object.keys(filter).reduce((r, k) => {
                const val = filter[k];
                if (k[0] === '_') {
                    // TODO: remove '_' test later
                    const systemProperty =
                        factory.properties.find(prop => prop.name === k && prop.isSystemProperty) ||
                        SystemProperties.getSystemProperties(factory).find(prop => prop.name === k);

                    r[k] = RecordPaging.convertFilter(
                        context,
                        factory,
                        val,
                        [...parentPath, k],
                        systemProperty || property,
                    );
                    return r;
                }
                const filterProp = factory.findProperty(k);
                if (
                    (filterProp.isReferenceProperty() && val && isCompound(val)) ||
                    filterProp.isCollectionProperty() ||
                    filterProp.isReferenceArrayProperty()
                ) {
                    const { targetFactory } = filterProp;

                    r[k] = RecordPaging.convertFilter(context, targetFactory, val, [...parentPath, k], filterProp);
                    return r;
                }

                r[k] = RecordPaging.convertFilter(context, factory, val, [...parentPath, k], filterProp);

                return r;
            }, {} as AnyFilterObject);
        }

        return filter;
    }

    /**
     * Parse the filter object
     * @param context
     * @param factory
     * @param filter
     * @returns
     */
    static parseFilter(
        context: Context,
        factory: NodeFactory,
        filter: AnyFilterValue | undefined,
    ): AnyFilterValue | undefined {
        if (!filter) return filter;
        return RecordPaging.convertFilter(context, factory, filter) as AnyFilterValue;
    }

    /**
     * Apply the paging options to a list of records (filter, order by, first, last, after, before)
     * @param factory
     * @param context
     * @param records
     * @param getPropertyValue
     * @param options
     * @returns
     */
    static async applyPagingOptions(
        factory: NodeFactory,
        context: Context,
        records: AnyRecord[],
        getPropertyValue: GetPropertyValue,
        options?: NodeQueryOptions,
    ): Promise<{ items: AnyRecord[]; totalCount: number }> {
        let items = records;
        let totalCount = items.length;
        if (!options) return Promise.resolve({ items, totalCount });
        if (options.last) {
            if (options.first) throw new Error('first cannot be supplied with last.');

            if (options.after) throw new Error('after cannot be supplied with last.');
        } else if (options.before) {
            throw new Error('before cannot be supplied without last.');
        }

        if (options.filter) {
            items = await RecordPaging.applyFilter(
                context,
                factory,
                getPropertyValue,
                items,
                options.filter,
                context.locales,
            );

            totalCount = items.length;
        }

        let orderBy = [] as OrderByClause[];

        if (factory.storage !== 'external') {
            orderBy = factory.parseOrderBy(context, options.orderBy);
        } else {
            // we set the order by to the key properties
            orderBy = factory.externalStorageManager
                ? factory.externalStorageManager.parseOrderBy(context, options.orderBy).map(clause => {
                      const property = factory.findProperty(clause.property.name);
                      return {
                          ...clause,
                          property,
                      } as OrderByClause;
                  })
                : [];
        }
        // The full collection has been fetched from the database: orderBy clause has to be computed in memory
        if (orderBy.length !== 0 && options.orderBy) {
            items = await RecordPaging.applyOrderBy(context, factory, items, options.orderBy, getPropertyValue);
        }

        const getCursorValue = (val: string): PropertyAndValue[] => {
            if (factory.storage === 'external') {
                if (!factory.externalStorageManager)
                    throw new Error(`${factory.name}: External storage manager is not defined`);

                return factory.externalStorageManager.parseCursor(orderBy, val).map((propVal: PropertyAndValue) => {
                    const property = factory.findProperty(propVal.property.name);
                    return { property, value: propVal.value } as PropertyAndValue;
                });
            }
            return parseCursor(
                orderBy.map(clause => clause.property),
                val,
            );
        };

        if (options.after != null) {
            // apply after
            items = await RecordPaging.applyPagingOperation(
                context,
                factory,
                getPropertyValue,
                items,
                orderBy,
                getCursorValue(options.after),
                'after',
            );
            // Note : totalCount must not be updated to items.length
        }

        if (options.before != null) {
            // apply before
            items = await RecordPaging.applyPagingOperation(
                context,
                factory,
                getPropertyValue,
                items,
                orderBy,
                getCursorValue(options.before),
                'before',
            );
            // Note : totalCount must not be updated to items.length
        }

        if (options.first != null && options.first !== 0) {
            items = items.slice(0, options.first);
            // Note : totalCount must not be updated to items.length
        }
        if (options.last != null && options.last !== 0) {
            items = items.slice(-options.last);
            // Note : totalCount must not be updated to items.length
        }

        return { items, totalCount };
    }
}
