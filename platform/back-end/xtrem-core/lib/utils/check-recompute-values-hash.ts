import { asyncArray } from '@sage/xtrem-async-helper';
import { Logger } from '@sage/xtrem-log';
import { Dict, unwrapError } from '@sage/xtrem-shared';
import { DatabaseError } from '..';
import { Application } from '../application';
import { StateLoad } from '../node-state/state-load';
import { Context, NodeFactory } from '../runtime';
import { AnyNode } from '../ts-api';

/**
 * Given a specific tenantId, this function will ensure that the _valuesHash of all content addressable nodes
 * in the tenant is correct. This is useful when importing/restoring anonymized data, where the _valuesHash is not updated.
 * @param application
 * @param tenantId
 * @param logger
 */
export async function checkAndSyncValuesHash(
    application: Application,
    tenantId: string,
    logger: Logger,
): Promise<void> {
    logger?.info(`Checking and syncing values hash for tenant ${tenantId}`);
    await application.withReadonlyContext(
        tenantId,
        async context => {
            await asyncArray(application.getAllFactories().filter(factory => factory.isContentAddressable)).forEach(
                async factory => {
                    if (!(await factory.table.tableExists(context))) return;
                    logger?.info(`Checking and syncing values hash for factory ${factory.name}`);
                    await checkAndSyncFactoryValuesHash(context, factory, logger);
                },
            );
        },
        { source: 'customMutation' }, // in runInWritableContext we only allow certain sources (see ContextSource from context.ts)
    );
    logger?.info(`Finished checking and syncing values hash for tenant ${tenantId}`);
}

export async function checkAndSyncFactoryValuesHash(
    context: Context,
    factory: NodeFactory,
    logger: Logger,
): Promise<void> {
    const onlyColumns = { onlyColumns: { _values_hash: true } };
    const idsWithCollision: Dict<string> = {};
    await context.queryWithReader(factory.nodeConstructor, {}, async reader => {
        await reader.forEach(async (record: AnyNode) => {
            // Load lazy loaded properties before computing the hash
            await StateLoad.resolveLazyLoadedValues(record.$.state);
            const hash = factory.getValuesHash(record.$.state.values);
            if (hash === record.$.getRawPropertyValue('_valuesHash')) return;
            await context.runInWritableContext(async writableContext => {
                try {
                    await factory.table.update(
                        writableContext,
                        {
                            _id: record._id,
                            _valuesHash: hash,
                        },
                        onlyColumns,
                    );
                } catch (error) {
                    const unwrappedError = unwrapError(error);

                    if (unwrappedError instanceof DatabaseError && unwrappedError.code === '23505') {
                        // Collision detected, we collect all records
                        idsWithCollision[record._id] = hash;
                    } else {
                        logger?.error(`Error while updating _valuesHash for record ${record._id}`, error);
                    }
                }
            });
        });
    });

    // If we have ids with collision, we need to fix all factories that reference this factory
    await asyncArray(Object.keys(idsWithCollision)).forEach(async _id => {
        logger?.info(`Hash collision detected with hash ${idsWithCollision[_id]}`);
        logger?.info(`recordWithWrongHash ${_id}`);
        const recordWithRightHash = await context.tryRead(factory.nodeConstructor, {
            _valuesHash: idsWithCollision[_id],
        });
        if (!recordWithRightHash) {
            // This case happens when the record with the collision hash has been recomputed in the first pass
            await context.runInWritableContext(async writableContext => {
                await factory.table.update(
                    writableContext,
                    {
                        _id,
                        _valuesHash: idsWithCollision[_id],
                    },
                    onlyColumns,
                );
            });
            logger?.info(`recordWithRightHash not found, updated record ${_id} with hash ${idsWithCollision[_id]}`);
            delete idsWithCollision[_id];
        } else {
            logger?.info(`recordWithRightHash ${recordWithRightHash._id}`);
            await asyncArray(factory.application.getAllFactories()).forEach(async factoryElement => {
                const propsToUpdate = factoryElement.properties
                    .filter(
                        property =>
                            property.isReferenceProperty() &&
                            !property.isInherited &&
                            property.targetFactory.name === factory.name,
                    )
                    .map(property => property.name);
                if (propsToUpdate.length === 0) return;

                logger?.info(`factoryToFix ${JSON.stringify(factoryElement.name)}`);
                logger?.info(`properties to update ${JSON.stringify(propsToUpdate)}`);

                await asyncArray(propsToUpdate).forEach(async prop => {
                    const updatedRecord = await context.bulkUpdate(factoryElement.nodeConstructor, {
                        set: { [prop]: recordWithRightHash._id },
                        where: {
                            [prop]: _id,
                        },
                    });
                    logger?.info(`updatedRecord ${updatedRecord}`);
                });
            });
        }
    });

    await context.runInWritableContext(async writableContext => {
        await asyncArray(Object.keys(idsWithCollision)).forEach(_id =>
            writableContext.delete(factory.nodeConstructor, {
                _id,
            }),
        );
    });
}

export async function getTenantIdList(application: Application): Promise<string[]> {
    let sysTenantIds: string[] = [];
    await application.asRoot.withReadonlyContext(
        null,
        async rootContext => {
            sysTenantIds = await Context.tenantManager.listTenantsIds(rootContext);
        },
        { source: 'customMutation' },
    );
    return sysTenantIds;
}
