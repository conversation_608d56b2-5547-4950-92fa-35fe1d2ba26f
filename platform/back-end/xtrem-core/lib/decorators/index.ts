import { Package } from '../application/package';
import { NodeFactory } from '../runtime/node-factory';
import {
    node,
    nodeExtension,
    subNode,
    subNodeExtension1,
    subNodeExtension2,
    subNodeExtension3,
    subNodeExtension4,
    subNodeExtension5,
} from './node-decorators';
import { mutation, query } from './operation-decorators';
import {
    binaryStreamProperty,
    binaryStreamPropertyOverride,
    booleanProperty,
    booleanPropertyOverride,
    collectionProperty,
    collectionPropertyOverride,
    dateProperty,
    datePropertyOverride,
    dateRangeProperty,
    dateRangePropertyOverride,
    datetimeProperty,
    datetimePropertyOverride,
    datetimeRangeProperty,
    datetimeRangePropertyOverride,
    decimalProperty,
    decimalPropertyOverride,
    decimalRangeProperty,
    decimalRangePropertyOverride,
    doubleProperty,
    doublePropertyOverride,
    enumArrayProperty,
    enumArrayPropertyOverride,
    enumProperty,
    enumPropertyOverride,
    floatProperty,
    floatPropertyOverride,
    integerArrayProperty,
    integerArrayPropertyOverride,
    integerProperty,
    integerPropertyOverride,
    integerRangeProperty,
    integerRangePropertyOverride,
    jsonProperty,
    jsonPropertyOverride,
    referenceArrayProperty,
    referenceArrayPropertyOverride,
    referenceProperty,
    referencePropertyOverride,
    shortProperty,
    shortPropertyOverride,
    stringArrayProperty,
    stringArrayPropertyOverride,
    stringProperty,
    stringPropertyOverride,
    textStreamProperty,
    textStreamPropertyOverride,
    timeProperty,
    timePropertyOverride,
    uuidProperty,
    uuidPropertyOverride,
} from './property-decorators';

export { useDefaultValue } from './property-decorators/base-property-events';

export {
    ColumnTypeName,
    Decorated,
    FilterTag,
    StaticMemberDecoratorFunction,
    StaticThis,
    TypeName,
    staticMemberDecorator,
} from './decorator-utils';
export * from './external-storage-manager';
export * from './node-decorators';
export * from './operation-decorators';
export * from './property-decorators';

// These interfaces are placeholders for listeners
// They will be extended by xtrem-communication
export interface NotificationListener {
    name: string;
    definingPackage: Package;
    register(factory: NodeFactory): void;
}
export interface MessageListener {
    name: string;
    definingPackage: Package;
    register(factory: NodeFactory): void;
}

export const coreDecorators = {
    node,
    nodeExtension,
    subNodeExtension1,
    subNodeExtension2,
    subNodeExtension3,
    subNodeExtension4,
    subNodeExtension5,
    subNode,
    mutation,
    query,
    // Properties and property extensions
    binaryStreamProperty,
    binaryStreamPropertyOverride,
    booleanProperty,
    booleanPropertyOverride,
    collectionProperty,
    collectionPropertyOverride,
    dateProperty,
    datePropertyOverride,
    dateRangeProperty,
    dateRangePropertyOverride,
    datetimeProperty,
    datetimePropertyOverride,
    datetimeRangeProperty,
    datetimeRangePropertyOverride,
    decimalProperty,
    decimalPropertyOverride,
    decimalRangeProperty,
    decimalRangePropertyOverride,
    doubleProperty,
    doublePropertyOverride,
    enumProperty,
    enumPropertyOverride,
    enumArrayProperty,
    enumArrayPropertyOverride,
    floatProperty,
    floatPropertyOverride,
    integerProperty,
    integerPropertyOverride,
    integerRangeProperty,
    integerRangePropertyOverride,
    integerArrayProperty,
    integerArrayPropertyOverride,
    jsonProperty,
    jsonPropertyOverride,
    referenceProperty,
    referencePropertyOverride,
    referenceArrayProperty,
    referenceArrayPropertyOverride,
    shortProperty,
    shortPropertyOverride,
    stringProperty,
    stringPropertyOverride,
    stringArrayProperty,
    stringArrayPropertyOverride,
    textStreamProperty,
    textStreamPropertyOverride,
    timeProperty,
    timePropertyOverride,
    uuidProperty,
    uuidPropertyOverride,
};

type CoreDecorators = typeof coreDecorators;
export interface Decorators extends CoreDecorators {}

export const decorators: Decorators = coreDecorators;
