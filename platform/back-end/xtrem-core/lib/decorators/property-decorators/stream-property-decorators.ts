/* eslint-disable @typescript-eslint/explicit-function-return-type */
/** @packageDocumentation @module decorators */
import { AnyValue } from '@sage/xtrem-async-helper';
import { Node } from '../../ts-api';
import { BinaryStream } from '../../types/binary-stream';
import { BinaryStreamDataType, TextStreamDataType } from '../../types/stream-data-type';
import { TextStream } from '../../types/text-stream';
import { createTypedProperty, extendProperty, InferValT } from '../decorator-utils';
import { TypedPropertyDecorator } from './base-property-decorator';
import { PropertyOverrideDecorator } from './base-property-extension-decorator';

export interface StreamPropertyDecorator<This extends Node = Node, ValT extends AnyValue = any>
    extends TypedPropertyDecorator<This, ValT> {
    /**
     * Should the lazy-loading on this column be skipped ? i.e. should this stream column be loaded when its
     * node is read from the database ?
     */
    noLazyLoad?: boolean;
}

/** Parameter type for &#064;decorators.binaryStreamProperty decorator */
export interface BinaryStreamPropertyDecorator<
    This extends Node = Node,
    ValT extends BinaryStream | null = BinaryStream,
> extends StreamPropertyDecorator<This, ValT> {
    /** Is property value nullable? */
    isNullable?: boolean;
    dataType?: () => BinaryStreamDataType<ValT, This>;
}

/** Parameter type for &#064;decorators.textStreamProperty decorator */
export interface TextStreamPropertyDecorator<This extends Node = Node, ValT extends TextStream | null = TextStream>
    extends StreamPropertyDecorator<This, ValT> {
    dataType?: () => TextStreamDataType<ValT extends null ? never : ValT, This>;
}

/** &#064;decorators.textStreamProperty(arg) property decorator */
export function textStreamProperty<This extends Node, K extends keyof This>(
    arg: TextStreamPropertyDecorator<This, InferValT<This[K], TextStream>>,
) {
    return createTypedProperty<This, K>('textStream', arg);
}

/** &#064;decorators.binaryStreamProperty(arg) property decorator */
export function binaryStreamProperty<This extends Node, K extends keyof This>(
    arg: BinaryStreamPropertyDecorator<This, InferValT<This[K], BinaryStream | null>>,
) {
    return createTypedProperty<This, K>('binaryStream', arg);
}

/** &#064;decorators.textStreamPropertyOverride(arg) property decorator */
export function textStreamPropertyOverride<This extends Node, K extends keyof This>(
    arg: PropertyOverrideDecorator<This, InferValT<This[K], TextStream>>,
) {
    return extendProperty('textStream', arg);
}

/** &#064;decorators.binaryStreamPropertyOverride(arg) property decorator */
export function binaryStreamPropertyOverride<This extends Node, K extends keyof This>(
    arg: PropertyOverrideDecorator<This, InferValT<This[K], BinaryStream>>,
) {
    return extendProperty('binaryStream', arg);
}
