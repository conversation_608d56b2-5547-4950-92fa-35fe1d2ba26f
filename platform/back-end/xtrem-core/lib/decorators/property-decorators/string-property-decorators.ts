/* eslint-disable @typescript-eslint/explicit-function-return-type */
/** @packageDocumentation @module decorators */
import { Node } from '../../ts-api';
import { StringArrayDataType, StringDataType } from '../../types';
import {
    InferNonNullValT,
    InferValT,
    MemberDecoratorFunction,
    createTypedProperty,
    extendProperty,
} from '../decorator-utils';
import { TypedPropertyDecorator } from './base-property-decorator';
import { PropertyOverrideDecorator } from './base-property-extension-decorator';

export interface StringPropertyDecorator<This extends Node = Node, ValT extends string = string>
    extends TypedPropertyDecorator<This, ValT extends string ? ValT : never> {
    /** Can the property be equal to empty string? */
    isNotEmpty?: boolean;
    isStoredEncrypted?: boolean;
    dataType?: () => StringDataType<This, ValT extends string ? ValT : never>;
}

/** &#064;decorators.stringProperty(arg) property decorator */
export function stringProperty<This extends Node, K extends keyof This>(
    arg: StringPropertyDecorator<This, InferNonNullValT<This[K], string>>,
): MemberDecoratorFunction<This, K> {
    return createTypedProperty<This, K>('string', arg);
}

/** &#064;decorators.stringPropertyOverride(arg) property decorator */
export function stringPropertyOverride<This extends Node, K extends keyof This>(
    arg: PropertyOverrideDecorator<This, InferNonNullValT<This[K], string>>,
): MemberDecoratorFunction<This, K> {
    return extendProperty<This, K>('string', arg);
}

export interface StringArrayPropertyDecorator<This extends Node = Node, K extends keyof This = any>
    extends TypedPropertyDecorator<This, InferValT<This[K], string[] | null>> {
    /** Can the property be equal to empty string? */
    dataType?: () => StringArrayDataType<This, InferValT<This[K], string[] | null>>;
}

/** &#064;decorators.stringArrayProperty(arg) property decorator */
export function stringArrayProperty<This extends Node, K extends keyof This>(
    arg: StringArrayPropertyDecorator<This, K>,
) {
    return createTypedProperty<This, K>('stringArray', arg);
}

/** &#064;decorators.stringArrayPropertyOverride(arg) property decorator */
export function stringArrayPropertyOverride<This extends Node, K extends keyof This>(
    arg: PropertyOverrideDecorator<This, InferValT<This[K], string[]>>,
) {
    return extendProperty<This, K>('stringArray', arg);
}
