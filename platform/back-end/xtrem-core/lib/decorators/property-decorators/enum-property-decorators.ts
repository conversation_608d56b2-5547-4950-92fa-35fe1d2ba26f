/* eslint-disable @typescript-eslint/explicit-function-return-type */
/** @packageDocumentation @module decorators */
import { Node } from '../../ts-api';
import { ArrayType, EnumDataType } from '../../types';
import { InferValT, createTypedProperty, extendProperty } from '../decorator-utils';
import { TypedPropertyDecorator } from './base-property-decorator';
import { PropertyOverrideDecorator } from './base-property-extension-decorator';

/** Parameter type for &#064;decorators.enumProperty decorator */
export interface EnumPropertyDecorator<This extends Node = Node, ValT extends string | null = string>
    extends TypedPropertyDecorator<This, ValT> {
    /** Is property value nullable? */
    isNullable?: boolean;
    /** A nullable property is not allowed by default in unique index, set this attribute to allow it */
    allowedInUniqueIndex?: boolean;
    dataType: () => EnumDataType<ValT, This>;
}

/** &#064;decorators.enumProperty(arg) property decorator */
export function enumProperty<This extends Node, K extends keyof This>(
    arg: EnumPropertyDecorator<This, InferValT<This[K], string | null>>,
) {
    return createTypedProperty<This, K>('enum', arg);
}

/** &#064;decorators.enumPropertyOverride(arg) property decorator */
export function enumPropertyOverride<This extends Node, K extends keyof This>(
    arg: PropertyOverrideDecorator<This, InferValT<This[K], string | null>>,
) {
    return extendProperty<This, K>('enum', arg);
}

/** Parameter type for &#064;decorators.enumArrayProperty decorator */
export interface EnumArrayPropertyDecorator<This extends Node = Node, ValT extends string[] | null = string[]>
    extends TypedPropertyDecorator<This, ValT> {
    /** Is property value nullable? */
    isNullable?: boolean;
    dataType: () => EnumDataType<ArrayType<ValT, string>, This>;
}

/** &#064;decorators.enumArrayProperty(arg) property decorator */
export function enumArrayProperty<This extends Node, K extends keyof This>(
    arg: EnumArrayPropertyDecorator<This, InferValT<This[K], string[] | null>>,
) {
    return createTypedProperty<This, K>('enumArray', arg);
}

/** &#064;decorators.enumArrayPropertyOverride(arg) property decorator */
export function enumArrayPropertyOverride<This extends Node, K extends keyof This>(
    arg: PropertyOverrideDecorator<This, InferValT<This[K], string[] | null>>,
) {
    return extendProperty<This, K>('enumArray', arg);
}
