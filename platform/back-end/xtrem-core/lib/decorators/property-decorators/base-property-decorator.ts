/** @packageDocumentation @module decorators */
import { AnyValue, AsyncResponse, UnPromised } from '@sage/xtrem-async-helper';
import { ServiceOption } from '../../application';
import { PropagatePath } from '../../runtime/property';
import { Extend, Node, integer } from '../../ts-api';
import { DataType, DataTypeOptions, TextStream } from '../../types';
import { ColumnTypeName, FilterTag, TypeName } from '../decorator-utils';
import { PropertyEvents } from './base-property-events';

export type PropertyFilterTag =
    | FilterTag
    | 'key'
    | 'company'
    | 'legislation'
    | 'title'
    | 'shortTitle'
    | 'sequenceNumber'
    | 'documentDate';

export type DataSensitivityLevel =
    | 'notSensitive'
    | 'businessSensitive'
    | 'personal'
    | 'personalSensitive'
    | 'highlySensitive';

export type AnonymizeMethod = 'fixed' | 'random' | 'hash' | 'hashLimit' | 'perCharRandom' | 'custom' | 'binary' | 'url';

export type AnonymizeBinary = 'pdf' | 'image';

type AnonymizeValueType = AnonymizeBinary | TextStream | number | null;

export type DelegatesTo<This extends Node | null = Node> = {
    [K in Exclude<keyof This, '$' | '_id' | '_action'>]?: This[K] extends infer U
        ? UnPromised<U> extends Node | null
            ? keyof Exclude<UnPromised<U>, null>
            : never
        : never;
};

export type DelegatePath<NodeT extends Node | null> = DelegatesTo<Extend<NodeT>>;

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export interface BasePropertyDecorator<This extends Node = Node> {
    /** Access code that may restrict access to property value */
    accessCode?: string;
    /** Is the property mapped to a table column */
    isStored?: boolean;
    /** Is the property a transient input value to mutations */
    isTransientInput?: boolean;
    /** Is the property a stored output value */
    isStoredOutput?: boolean;
    /** Column name if property is mapped to a column with a different name */
    columnType?: ColumnTypeName;
    /** Column name that may be used by external storage node */
    columnName?: string;
    /** Is this property auto-increment ? (i.e.: the value is assigned by the database engine when inserting) */
    isAutoIncrement?: boolean;

    /** Does property provide one or more standard attributes (company, site, legislation, ...) for the node */
    provides?: PropertyFilterTag[];

    /** Is this property searchable? */
    canSearch?: boolean;
    /** Search category for search engine */
    searchCategory?: string;

    /**
     * Is this property available for lookup queries
     *
     * Note: _id and _customData defaults to true
     */
    lookupAccess?: boolean;

    /** Excluded from payload (used to hide technical properties) */
    excludedFromPayload?: boolean;

    /** Setup data for the property can be modified */
    isOwnedByCustomer?: boolean;
}

/**
 * Additional information we can use to create a column SQL definitions
 */
export interface PropertySqlAttributes {
    /**
     * the SQL default that will be used on property column
     */
    default?: string | null;
    /**
     * Use attribute to flag that a column should be excluded from an insert statement if the value passed to it is null
     * If the column has a default defined the default will be utilized (NULL does not work)
     */
    excludeFromInsertIfNull?: boolean;
}

/** Parameter type for all property decorators */
export interface TypedPropertyDecorator<
    This extends Node = Node,
    ValT extends AnyValue = AnyValue,
    ControlVal extends AnyValue = ValT,
    DefaultVal extends AnyValue = ValT,
    DataTypeOptionsT extends DataTypeOptions = DataTypeOptions,
> extends BasePropertyDecorator<This>,
        PropertyEvents<This, ValT, ControlVal, DefaultVal> {
    /** @internal */
    name?: string;

    /** @internal */
    type?: TypeName;

    /** Property's service option */
    serviceOptions?: () => ServiceOption[];

    /** published in graphql API? */
    isPublished?: boolean;
    /** property is required */
    isRequired?: boolean;

    dataType?: () => DataType<ValT, This, DataTypeOptionsT>;

    // TODO: xtrem-x3 uses these ones - it should NOT
    max?: integer;

    //
    /** @internal */
    propagatesTo?: PropagatePath[];

    /** Name of a reference property to which the property delegates */
    delegatesTo?: DelegatePath<This>;

    /** @internal --  to check that a dependant is never read while computing the value for one of its dependencies.*/
    dependencyIndex?: number;

    /** flag for decorators of property overrides
    @internal */
    isOverride?: boolean;

    /**
     * Indicates whether the property has been inherited from a base factory.
     * When dealing with superClasses, this attribute will be used to retrieve the baseClass of the node that
     * declared the property.
     @internal */
    isInherited?: boolean;

    /**
     * Indicates if the property should be cleared to its default value when we do a reset
     */
    isClearedByReset?: boolean | ((this: Extend<This>) => AsyncResponse<boolean>);

    /** Is user input required on the duplicated value */
    duplicateRequiresPrompt?: boolean;

    /** Alternate value to export instead of excluding the property (used to hide sensitive information) */
    exportValue?: ValT;

    /** Data sensitivity level for GDPR related functionality */
    dataSensitivityLevel?: DataSensitivityLevel;

    /** Anonymize the value of the property for export (used to hide sensitive customer information when
     * tenant data is exported with anonymize flag) */
    anonymizeMethod?: AnonymizeMethod;

    /** When anonymizeMethod is fixed (fixed value), hashLimit (hash limited to specified length) or
     * custom (callback function) define the relevant detail with anonymizeValue */
    anonymizeValue?: ValT | ((value: string) => string | null) | AnonymizeValueType;

    /**
     * Internal sql attributes used when we generate the column SQL definition.
     * default - used on the column DEFAULT
     @internal */
    sqlAttributes?: PropertySqlAttributes;
}
