/* eslint-disable @typescript-eslint/explicit-function-return-type */
/** @packageDocumentation @module decorators */
import { DecimalRange, IntegerRange } from '@sage/xtrem-date-time';
import { Node, integer } from '../../ts-api';
import { DecimalDataType } from '../../types';
import { InferValT, createTypedProperty, extendProperty } from '../decorator-utils';
import { TypedPropertyDecorator } from './base-property-decorator';
import { PropertyOverrideDecorator } from './base-property-extension-decorator';

export interface NumberPropertyDecorator<This extends Node = Node, ValT extends number | null = number>
    extends TypedPropertyDecorator<This, ValT> {
    /** Can the property be equal to zero? */
    isNotZero?: boolean;
    /** Is property value nullable? */
    isNullable?: boolean;
}

export interface IntegerPropertyDecorator<This extends Node = Node, ValT extends integer | null = integer>
    extends NumberPropertyDecorator<This, ValT> {
    /** A nullable property is not allowed by default in unique index, set this attribute to allow it */
    allowedInUniqueIndex?: boolean;
}

export interface ShortPropertyDecorator<This extends Node = Node, ValT extends integer | null = integer>
    extends NumberPropertyDecorator<This, ValT> {}
export interface FloatPropertyDecorator<This extends Node = Node, ValT extends number | null = number>
    extends NumberPropertyDecorator<This, ValT> {}
export interface DoublePropertyDecorator<This extends Node = Node, ValT extends number | null = number>
    extends NumberPropertyDecorator<This, ValT> {}
export interface DecimalPropertyDecorator<This extends Node = Node, ValT extends number | null = number>
    extends NumberPropertyDecorator<This, ValT> {
    dataType: () => DecimalDataType<This, ValT>;
}

export interface IntegerRangePropertyDecorator<
    This extends Node = Node,
    ValT extends IntegerRange | null = IntegerRange,
> extends TypedPropertyDecorator<This, ValT extends IntegerRange | null ? ValT : never> {
    /** Is property value nullable? */
    isNullable?: boolean;
}
export interface DecimalRangePropertyDecorator<
    This extends Node = Node,
    ValT extends DecimalRange | null = DecimalRange,
> extends TypedPropertyDecorator<This, ValT> {
    /** Is property value nullable? */
    isNullable?: boolean;
}

export interface IntegerArrayPropertyDecorator<This extends Node = Node, ValT extends integer[] | null = integer[]>
    extends TypedPropertyDecorator<This, ValT> {
    /** Is property value nullable? */
    isNullable?: boolean;
}

/** &#064;decorators.integerProperty(arg) property decorator */
export function integerProperty<This extends Node, K extends keyof This>(
    arg: IntegerPropertyDecorator<This, InferValT<This[K], integer | null>>,
) {
    return createTypedProperty<This, K>('integer', arg);
}
/** &#064;decorators.floatProperty(arg) property decorator */
export function floatProperty<This extends Node, K extends keyof This>(
    arg: FloatPropertyDecorator<This, InferValT<This[K], number | null>>,
) {
    return createTypedProperty<This, K>('float', arg);
}

/** &#064;decorators.decimalProperty(arg) property decorator */
export function decimalProperty<This extends Node, K extends keyof This>(
    arg: DecimalPropertyDecorator<This, InferValT<This[K], number | null>>,
) {
    return createTypedProperty<This, K>('decimal', arg);
}

/** &#064;decorators.doubleProperty(arg) property decorator */
export function doubleProperty<This extends Node, K extends keyof This>(
    arg: DoublePropertyDecorator<This, InferValT<This[K], number | null>>,
) {
    return createTypedProperty<This, K>('double', arg);
}

/** &#064;decorators.shortProperty(arg) property decorator */
export function shortProperty<This extends Node, K extends keyof This>(
    arg: ShortPropertyDecorator<This, InferValT<This[K], number | null>>,
) {
    return createTypedProperty<This, K>('short', arg);
}

/** &#064;decorators.integerRangeProperty(arg) property decorator */
export function integerRangeProperty<This extends Node, K extends keyof This>(
    arg: IntegerRangePropertyDecorator<This, InferValT<This[K], IntegerRange | null>>,
) {
    return createTypedProperty<This, K>('integerRange', arg);
}

/** &#064;decorators.integerArrayProperty(arg) property decorator */
export function integerArrayProperty<This extends Node, K extends keyof This>(
    arg: IntegerArrayPropertyDecorator<This, InferValT<This[K], integer[] | null>>,
) {
    return createTypedProperty<This, K>('integerArray', arg);
}

/** &#064;decorators.decimalRangeProperty(arg) property decorator */
export function decimalRangeProperty<This extends Node, K extends keyof This>(
    arg: DecimalRangePropertyDecorator<This, InferValT<This[K], DecimalRange | null>>,
) {
    return createTypedProperty<This, K>('decimalRange', arg);
}

/** &#064;decorators.integerPropertyOverride(arg) property decorator */
export function integerPropertyOverride<This extends Node, K extends keyof This>(
    arg: PropertyOverrideDecorator<This, InferValT<This[K], integer | null>>,
) {
    return extendProperty<This, K>('integer', arg);
}

/** &#064;decorators.integerRangePropertyOverride(arg) property decorator */
export function integerRangePropertyOverride<This extends Node, K extends keyof This>(
    arg: PropertyOverrideDecorator<This, InferValT<This[K], IntegerRange | null>>,
) {
    return extendProperty<This, K>('integerRange', arg);
}

/** &#064;decorators.decimalRangePropertyOverride(arg) property decorator */
export function decimalRangePropertyOverride<This extends Node, K extends keyof This>(
    arg: PropertyOverrideDecorator<This, InferValT<This[K], DecimalRange | null>>,
) {
    return extendProperty<This, K>('decimalRange', arg);
}

/** &#064;decorators.floatPropertyOverride(arg) property decorator */
export function floatPropertyOverride<This extends Node, K extends keyof This>(
    arg: PropertyOverrideDecorator<This, InferValT<This[K], number | null>>,
) {
    return extendProperty<This, K>('float', arg);
}

/** &#064;decorators.decimalPropertyOverride(arg) property decorator */
export function decimalPropertyOverride<This extends Node, K extends keyof This>(
    arg: PropertyOverrideDecorator<This, InferValT<This[K], number | null>>,
) {
    return extendProperty<This, K>('decimal', arg);
}

/** &#064;decorators.doublePropertyOverride(arg) property decorator */
export function doublePropertyOverride<This extends Node, K extends keyof This>(
    arg: PropertyOverrideDecorator<This, InferValT<This[K], number | null>>,
) {
    return extendProperty<This, K>('double', arg);
}

/** &#064;decorators.shortPropertyOverride(arg) property decorator */
export function shortPropertyOverride<This extends Node, K extends keyof This>(
    arg: PropertyOverrideDecorator<This, InferValT<This[K], integer | null>>,
) {
    return extendProperty<This, K>('short', arg);
}

/** &#064;decorators.integerArrayPropertyOverride(arg) property decorator */
export function integerArrayPropertyOverride<This extends Node, K extends keyof This>(
    arg: PropertyOverrideDecorator<This, InferValT<This[K], integer[] | null>>,
) {
    return extendProperty<This, K>('integerArray', arg);
}
