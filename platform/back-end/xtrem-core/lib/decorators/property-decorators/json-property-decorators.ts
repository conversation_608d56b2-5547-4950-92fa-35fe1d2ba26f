/* eslint-disable @typescript-eslint/explicit-function-return-type */
/** @packageDocumentation @module decorators */
import { AnyValue } from '@sage/xtrem-async-helper';
import { Node } from '../../ts-api';
import { JsonDataType } from '../../types';
import { createTypedProperty, extendProperty, InferValT } from '../decorator-utils';
import { TypedPropertyDecorator } from './base-property-decorator';
import { PropertyOverrideDecorator } from './base-property-extension-decorator';

export interface JsonPropertyDecorator<This extends Node = Node, ValT extends AnyValue = object>
    extends TypedPropertyDecorator<This, ValT> {
    /** Is property value nullable? */
    isNullable?: boolean;
    dataType?: () => JsonDataType<This, ValT>;
}

/** &#064;decorators.jsonProperty(arg) property decorator */
export function jsonProperty<This extends Node, K extends keyof This>(
    arg: JsonPropertyDecorator<This, InferValT<This[K], object | null>>,
) {
    return createTypedProperty<This, K>('json', arg);
}
export function jsonPropertyOverride<This extends Node, K extends keyof This>(
    arg: PropertyOverrideDecorator<This, InferValT<This[K], object | null>>,
) {
    return extendProperty<This, K>('json', arg);
}
