/* eslint-disable @typescript-eslint/explicit-function-return-type */
/** @packageDocumentation @module decorators */
import { AnyValue } from '@sage/xtrem-async-helper';
import { ServiceOption } from '../../application';
import { Node } from '../../ts-api';
import { PropertyEvents } from './base-property-events';

export interface PropertyOverrideDecorator<
    This extends Node,
    ValT extends AnyValue,
    ControlVal extends AnyValue = ValT,
    DefaultVal extends AnyValue = ValT,
> extends PropertyEvents<This, ValT, ControlVal, DefaultVal> {
    serviceOptions?: () => ServiceOption[];
}
