/* eslint-disable @typescript-eslint/explicit-function-return-type */
/** @packageDocumentation @module decorators */
import { Node } from '../../ts-api';
import { Uuid } from '../../types';
import { createTypedProperty, extendProperty, InferValT } from '../decorator-utils';
import { TypedPropertyDecorator } from './base-property-decorator';
import { PropertyOverrideDecorator } from './base-property-extension-decorator';

export interface UuidPropertyDecorator<This extends Node = Node, ValT extends Uuid | null = Uuid>
    extends TypedPropertyDecorator<This, ValT> {
    /** Is property value nullable? */
    isNullable?: boolean;
}

/** &#064;decorators.uuidProperty(arg) property decorator */
export function uuidProperty<This extends Node, K extends keyof This>(
    arg: UuidPropertyDecorator<This, InferValT<This[K], Uuid | null>>,
) {
    return createTypedProperty<This, K>('uuid', arg);
}

/** &#064;decorators.uuidPropertyOverride(arg) property decorator */
export function uuidPropertyOverride<This extends Node, K extends keyof This>(
    arg: PropertyOverrideDecorator<This, InferValT<This[K], Uuid>>,
) {
    return extendProperty<This, K>('uuid', arg);
}
