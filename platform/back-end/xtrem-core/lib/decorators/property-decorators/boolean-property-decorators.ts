/* eslint-disable @typescript-eslint/explicit-function-return-type */
/** @packageDocumentation @module decorators */
import { Node } from '../../ts-api';
import { createTypedProperty, extendProperty, InferValT } from '../decorator-utils';
import { TypedPropertyDecorator } from './base-property-decorator';
import { PropertyOverrideDecorator } from './base-property-extension-decorator';

export interface BooleanPropertyDecorator<This extends Node = Node, ValT extends boolean | null = boolean | null>
    extends TypedPropertyDecorator<This, ValT> {
    /** Is property value nullable? */
    isNullable?: boolean;
}

/** &#064;decorators.booleanProperty(arg) property decorator */
export function booleanProperty<This extends Node, K extends keyof This>(
    arg: BooleanPropertyDecorator<This, InferValT<This[K], boolean | null>>,
) {
    return createTypedProperty<This, K>('boolean', arg);
}

/** &#064;decorators.booleanPropertyOverride(arg) property decorator */
export function booleanPropertyOverride<This extends Node, K extends keyof This>(
    arg: PropertyOverrideDecorator<This, InferValT<This[K], boolean | null>>,
) {
    return extendProperty<This, K>('boolean', arg);
}
