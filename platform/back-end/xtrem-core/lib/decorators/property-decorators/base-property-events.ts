/** @packageDocumentation @module decorators */
import { AnyValue, AsyncArray, AsyncResponse } from '@sage/xtrem-async-helper';
import { Dict } from '@sage/xtrem-shared';
import { Extend, Node, NodeCreateData, ValidationContext, Validator } from '../../ts-api';
import { CollectionPropertyEvents } from './collection-property-decorators';

export type PropertyPaths<This extends Node> = (string | Dict<PropertyPaths<This>>)[];

export type SetValueData<ValT> = ValT extends (infer U)[] ? (U extends Node ? NodeCreateData<U>[] : ValT) : ValT;

export type MatchingKey<This, ValT> = keyof This extends infer K
    ? K extends keyof This
        ? This[K] extends ValT
            ? K
            : never
        : never
    : never;

export type DefaultValueRule<This extends Node, DefaultVal extends AnyValue> =
    | DefaultVal
    | null
    | ((this: Extend<This>) => AsyncResponse<DefaultVal | null>);

export type DuplicatedValueRule<This extends Node, DefaultVal extends AnyValue> =
    | typeof useDefaultValue
    | DefaultVal
    | null
    | ((this: Extend<This>) => AsyncResponse<DefaultVal | null>);

export type UpdatedValueRule<This extends Node, DefaultVal extends AnyValue> =
    | typeof useDefaultValue
    | DefaultVal
    | ((this: Extend<This>) => AsyncResponse<DefaultVal | typeof useDefaultValue>);

export interface PropertyEvents<
    This extends Node = Node,
    ValT extends AnyValue = AnyValue,
    ControlVal extends AnyValue = ValT,
    DefaultVal extends AnyValue = ValT,
> {
    /**
     * Event to obtain default value
     * Note: even non-nullable properties are allowed to return 'null'. The user will have to provide
     * a valid value for this property when editing it (saving a node with a 'null' value for a non-nullable
     * property would raise an error).
     */
    defaultValue?: DefaultValueRule<This, DefaultVal>;
    /** event to prepare value before control */
    prepare?: (this: Extend<This>, cx: ValidationContext) => AsyncResponse<void>;
    /** event to control value */
    control?: Validator<Extend<This>, ControlVal>;
    /** Return "ts-to-sql compliant" the value of a computed property */
    getValue?: (this: Extend<This>) => AsyncResponse<ValT>;
    /** Return "ts-to-sql **NOT** compliant" the value of a computed property */
    computeValue?: (this: Extend<This>) => AsyncResponse<ValT extends AsyncArray<infer EltT> ? EltT[] : ValT>;
    /**  Event to set value */
    setValue?: (this: Extend<This>, val: SetValueData<ValT>) => AsyncResponse<void>;
    /** Event to adapt value when setting it*/
    adaptValue?: (this: Extend<This>, val: ValT) => AsyncResponse<ValT>;
    /** Dependencies on other properties */
    dependsOn?: PropertyPaths<This>;
    /** event to update the value when a dependency is modified */
    updatedValue?: UpdatedValueRule<This, DefaultVal>;
    /** Event to obtain value to use when the node is duplicated */
    duplicatedValue?: DuplicatedValueRule<This, ValT>;
    /** Is the property frozen */
    isFrozen?: boolean | ((this: Extend<This>) => AsyncResponse<boolean>);
    /** Event to provide a deferred value which will be set at commit time */
    deferredDefaultValue?: DefaultValueRule<This, DefaultVal>;
    /** cache getValue or computeValue using context cache */
    cacheComputedValue?: boolean;
}

export type PropertyRuleName = keyof PropertyEvents | keyof CollectionPropertyEvents;

/**
 * Special value for the `updatedValue` and `duplicatedValue` rules.
 * It indicates that the value should be obtained with the `defaultValue` rule.
 */
export const useDefaultValue = Symbol('use default value');
