/** @packageDocumentation @module decorators */
import { AnyValue, AsyncResponse, UnPromised } from '@sage/xtrem-async-helper';
import { Extend, Node, NodeControlFilters, NodeCreateData, NodeId, NodeQueryFilter } from '../../ts-api';
import { ArrayType, PropertyJoin, ReferenceDataTypeOptions } from '../../types';
import { InferValT, MemberDecoratorFunction, extendProperty, typedMemberDecorator } from '../decorator-utils';
import { TypedPropertyDecorator } from './base-property-decorator';
import { PropertyOverrideDecorator } from './base-property-extension-decorator';

export type ReferenceElement<T> = T extends Node ? T : never;

export type InferReferenceVal<PropType, ExpectedType> =
    UnPromised<PropType> extends ExpectedType ? UnPromised<PropType> : never;

/** Intermediate type for reference property decorator */
export interface BaseReferencePropertyDecorator<
    This extends Node,
    ValT extends Node | null,
    De<PERSON>ultV<PERSON> extends AnyValue,
> extends TypedPropertyDecorator<This, ValT, ValT, DefaultVal, ReferenceDataTypeOptions<ValT, This>> {
    /** Target class */
    node: () => { new (): ValT };

    reverseReference?: keyof ReferenceElement<ValT>;

    /** Is property value nullable? */
    isNullable?: boolean;

    /**
     * Should this reference be ignored when toposorting factories (only valid when target factory is a setup node and reference is nullable)
     */
    ignoreInToposort?: boolean;

    /** A nullable property is not allowed by default in unique index, set this attribute to allow it */
    allowedInUniqueIndex?: boolean;
    /** Is it a vital link ? */
    isVital?: boolean;

    /** Flags the reference property which points to the parent node in a vital relationship */
    isVitalParent?: boolean;

    /** Can the parent be created or updated by passing a payload in this property */
    isVitalParentInput?: boolean;

    /** Is it an association link ? */
    isAssociation?: boolean;

    /** Flags the property as an association */
    isAssociationParent?: boolean;

    /** Is it a mutable references? - Only relevant for references and collections on external storage nodes  */
    isMutable?: boolean;

    filters?: {
        lookup?: NodeQueryFilter<ValT extends Node | null ? ValT & Node : never, Extend<This>>;
        control?: NodeQueryFilter<ValT extends Node | null ? ValT & Node : never, Extend<This>>;
        controls?: NodeControlFilters<ValT extends Node | null ? ValT & Node : never, Extend<This>>;
    };
    /** If true the isActive option of the provides attribute is ignored, when querying the target node */
    ignoreIsActive?: boolean | ((this: Extend<This>) => AsyncResponse<boolean>);
    /** can a lookup query be performed against the reference */
    canLookup?: boolean;

    /** Explicit join */
    join?: PropertyJoin<ValT extends Node | null ? ValT & Node : never, Extend<This>>;

    prefetch?: (record: any) => any;
}

// We need a bit of extra work to handle the fact that defaultValue has a different return type when
// isVital is true. The defaultValue event returns:
// - an id or a node instance if isVital is undefined or false
// - an initialization payload for the child node if isVital is true
//
// We should probably introduce separate decorators (@decorators.vitalReferenceProperty) for vital references later.
// This will allow us to simplify this.

/** Intermediate type for non vital reference property decorator */
export interface NonVitalReferencePropertyDecorator<This extends Node, ValT extends Node | null>
    extends BaseReferencePropertyDecorator<This, ValT, ValT | NodeId> {
    isVital?: false;
}

/** Intermediate type for vital reference property decorator */
export interface VitalReferencePropertyDecorator<This extends Node, ValT extends Node | null>
    extends BaseReferencePropertyDecorator<This, ValT, NodeCreateData<NonNullable<ValT>>> {
    isVital: true;
}

/** Parameter type for &#064;decorators.referenceProperty decorator */
export type ReferencePropertyDecorator<This extends Node = Node, ValT extends Node | null = any> =
    | NonVitalReferencePropertyDecorator<This, ValT>
    | VitalReferencePropertyDecorator<This, ValT>;

/** &#064;decorators.referenceProperty(arg) property decorator */
export function referenceProperty<
    This extends Node,
    K extends keyof This,
    ValT extends InferReferenceVal<This[K], Node | null> = InferReferenceVal<This[K], Node | null>,
>(arg: ReferencePropertyDecorator<This, ValT>): MemberDecoratorFunction<This, K> {
    // cast to any because we don't include type in DecoratorArg
    (arg as any).type = 'reference';
    return typedMemberDecorator<This, TypedPropertyDecorator<This, This[K] extends AnyValue ? This[K] : never>>(
        'properties',
    )(arg as TypedPropertyDecorator<This, any>);
}

export interface ReferencePropertyOverrideDecorator<This extends Node = Node, ValT extends AnyValue = Node>
    extends PropertyOverrideDecorator<This, ValT, ValT, ValT> {
    node?: () => { new (): ValT };
    filters?: {
        lookup?: NodeQueryFilter<ValT extends Node | null ? ValT & Node : never, Extend<This>>;
        control?: NodeQueryFilter<ValT extends Node | null ? ValT & Node : never, Extend<This>>;
        controls?: NodeControlFilters<ValT extends Node | null ? ValT & Node : never, Extend<This>>;
    };
}

/** &#064;decorators.referencePropertyOverride(arg) property decorator */
export function referencePropertyOverride<
    This extends Node,
    K extends keyof This = any,
    ValT extends InferReferenceVal<This[K], Node | null> = InferReferenceVal<This[K], Node | null>,
>(arg: ReferencePropertyOverrideDecorator<This, ValT>): MemberDecoratorFunction<This, K> {
    return extendProperty('reference', arg);
}

/** &#064;decorators.referenceArrayProperty decorator */
export interface ReferenceArrayPropertyDecorator<
    This extends Node = Node,
    K extends keyof This = any,
    RefT extends Node = Node,
    ValT extends InferValT<This[K], RefT[] | null> = any,
> extends TypedPropertyDecorator<This, ValT, ValT, ValT, ReferenceDataTypeOptions<RefT, This>> {
    /** Target class */
    node: () => {
        new (): RefT;
    };
    /** Is property value nullable? */
    isNullable?: boolean;

    filters?: {
        lookup?: NodeQueryFilter<RefT, Extend<This>>;
        control?: NodeQueryFilter<RefT, Extend<This>>;
        controls?: NodeControlFilters<RefT, Extend<This>>;
    };
    /** If true the isActive option of the provides attribute is ignored, when querying the target node */
    ignoreIsActive?: boolean | ((this: Extend<This>) => AsyncResponse<boolean>);

    /**
     * The behavior when the reference is deleted:
     * - 'remove' - remove the reference from the reference array
     * - 'restrict' - restrict the deletion of the reference
     * This decorator is mandatory when isStored=true
     */
    onDelete?: 'remove' | 'restrict';
}

/** &#064;decorators.referenceArrayProperty(arg) property decorator */
export function referenceArrayProperty<
    This extends Node,
    K extends keyof This,
    ValT extends AnyValue = This[K] extends AnyValue ? This[K] : never,
>(
    arg: ReferenceArrayPropertyDecorator<This, K, ArrayType<UnPromised<This[K]>, Node>>,
): MemberDecoratorFunction<This, K> {
    // cast to any because we don't include type in DecoratorArg
    (arg as any).type = 'referenceArray';
    return typedMemberDecorator<This, TypedPropertyDecorator<This, ValT>>('properties')(
        arg as TypedPropertyDecorator<This, ValT>,
    );
}

/** &#064;decorators.referenceArrayPropertyOverride(arg) property decorator */
export function referenceArrayPropertyOverride<This extends Node, K extends keyof This = any>(
    arg: PropertyOverrideDecorator<This, InferValT<This[K], Node | null>>,
): MemberDecoratorFunction<This, K> {
    return extendProperty('referenceArray', arg);
}
