/* eslint-disable @typescript-eslint/explicit-function-return-type */
/** @packageDocumentation @module decorators */
import { DateRange, Datetime, DatetimeRange, DateValue, Time } from '@sage/xtrem-date-time';
import { Node } from '../../ts-api';
import { createTypedProperty, extendProperty, InferValT } from '../decorator-utils';
import { TypedPropertyDecorator } from './base-property-decorator';
import { PropertyOverrideDecorator } from './base-property-extension-decorator';

export interface DatePropertyDecorator<This extends Node = Node, ValT extends DateValue | null = DateValue>
    extends TypedPropertyDecorator<This, ValT> {
    /** Is property value nullable? */
    isNullable?: boolean;
    /** A nullable property is not allowed by default in unique index, set this attribute to allow it */
    allowedInUniqueIndex?: boolean;
}
export interface DateRangePropertyDecorator<This extends Node = Node, ValT extends DateRange | null = DateRange>
    extends TypedPropertyDecorator<This, ValT extends DateRange | null ? ValT : never> {
    /** Is property value nullable? */
    isNullable?: boolean;
}

export interface DatetimePropertyDecorator<This extends Node = Node, ValT extends Datetime | null = Datetime>
    extends TypedPropertyDecorator<This, ValT extends Datetime | null ? ValT : never> {
    /** Is property value nullable? */
    isNullable?: boolean;
}

export interface DatetimeRangePropertyDecorator<
    This extends Node = Node,
    ValT extends DatetimeRange | null = DatetimeRange,
> extends TypedPropertyDecorator<This, ValT extends DatetimeRange | null ? ValT : never> {
    /** Is property value nullable? */
    isNullable?: boolean;
}

export interface TimePropertyDecorator<This extends Node = Node, ValT extends Time | null = Time>
    extends TypedPropertyDecorator<This, ValT extends Time | null ? ValT : never> {
    /** Is property value nullable? */
    isNullable?: boolean;
}

/** &#064;decorators.dateProperty(arg) property decorator */
export function dateProperty<This extends Node, K extends keyof This>(
    arg: DatePropertyDecorator<This, InferValT<This[K], DateValue | null>>,
) {
    return createTypedProperty<This, K>('date', arg);
}

/** &#064;decorators.dateRangeProperty(arg) property decorator */
export function dateRangeProperty<This extends Node, K extends keyof This>(
    arg: DateRangePropertyDecorator<This, InferValT<This[K], DateRange | null>>,
) {
    return createTypedProperty<This, K>('dateRange', arg);
}

/** &#064;decorators.dateRangeProperty(arg) property decorator */
export function datetimeRangeProperty<This extends Node, K extends keyof This>(
    arg: DatetimeRangePropertyDecorator<This, InferValT<This[K], DatetimeRange | null>>,
) {
    return createTypedProperty<This, K>('datetimeRange', arg);
}

/** &#064;decorators.datetimeProperty(arg) property decorator */
export function datetimeProperty<This extends Node, K extends keyof This>(
    arg: DatetimePropertyDecorator<This, InferValT<This[K], Datetime | null>>,
) {
    return createTypedProperty<This, K>('datetime', arg);
}

/** &#064;decorators.timeProperty(arg) property decorator */
export function timeProperty<This extends Node, K extends keyof This>(
    arg: TimePropertyDecorator<This, InferValT<This[K], Time | null>>,
) {
    return createTypedProperty<This, K>('time', arg);
}

/** &#064;decorators.datePropertyOverride(arg) property decorator */
export function datePropertyOverride<This extends Node, K extends keyof This>(
    arg: PropertyOverrideDecorator<This, InferValT<This[K], DateValue | null>>,
) {
    return extendProperty<This, K>('date', arg);
}

/** &#064;decorators.datePropertyOverride(arg) property decorator */
export function dateRangePropertyOverride<This extends Node, K extends keyof This>(
    arg: PropertyOverrideDecorator<This, InferValT<This[K], DateRange | null>>,
) {
    return extendProperty<This, K>('dateRange', arg);
}

/** &#064;decorators.datePropertyOverride(arg) property decorator */
export function datetimeRangePropertyOverride<This extends Node, K extends keyof This>(
    arg: PropertyOverrideDecorator<This, InferValT<This[K], DatetimeRange | null>>,
) {
    return extendProperty<This, K>('datetimeRange', arg);
}

/** &#064;decorators.timePropertyOverride(arg) property decorator */
export function timePropertyOverride<This extends Node, K extends keyof This>(
    arg: PropertyOverrideDecorator<This, InferValT<This[K], Time | null>>,
) {
    return extendProperty<This, K>('time', arg);
}

/** &#064;decorators.datetimePropertyOverride(arg) property decorator */
export function datetimePropertyOverride<This extends Node, K extends keyof This>(
    arg: PropertyOverrideDecorator<This, InferValT<This[K], Datetime | null>>,
) {
    return extendProperty<This, K>('datetime', arg);
}
