import { AnyRecord } from '@sage/xtrem-async-helper';
import { DateRange, Datetime, DatetimeRange, DateValue, Time } from '@sage/xtrem-date-time';
import { Decimal } from '@sage/xtrem-decimal';
import { Node } from '../../ts-api';
import { BinaryStream, DataType, TextStream } from '../../types';
import { StaticThis } from '../decorator-utils';
import { ArrayParameter } from './array-parameter';
import { BaseParameter } from './base-parameter';
import { InstanceParameter, ReferenceParameter } from './foreign-node-parameter';
import { ObjectParameter } from './object-parameter';

export type NamesOfType<ValT> = ValT extends boolean
    ? 'boolean'
    : ValT extends string
      ? 'string' | 'enum'
      : ValT extends number
        ? 'integer' | 'decimal'
        : ValT extends Decimal
          ? 'decimal'
          : ValT extends DateValue
            ? 'date'
            : ValT extends DateRange
              ? 'dateRange'
              : ValT extends Datetime
                ? 'datetime'
                : ValT extends DatetimeRange
                  ? 'datetimeRange'
                  : ValT extends Time
                    ? 'time'
                    : ValT extends TextStream
                      ? 'textStream'
                      : ValT extends BinaryStream
                        ? 'binaryStream'
                        : ValT extends AnyRecord
                          ? 'json'
                          : never;

export interface ScalarParameter<This extends StaticThis<Node> = typeof Node, ValT = any> extends BaseParameter<This> {
    type: Exclude<NamesOfType<ValT>, 'collection' | 'reference' | 'instance'>;
    dataType?: () => DataType<any, InstanceType<This>>;
}

export type Parameter<This extends StaticThis<Node> = typeof Node, NameT = any> =
    | ReferenceParameter<This>
    | InstanceParameter<This>
    | ObjectParameter<This>
    | ArrayParameter<This>
    | ScalarParameter<This, NameT>;
