/** @packageDocumentation @module decorators */
import { AsyncResponse, IsolationLevel, Package, UnPromised } from '../..';
import { ServiceOption } from '../../application';
import { Context, UserAccess } from '../../runtime';
import { Node } from '../../ts-api';
import {
    OperationKind,
    StaticMemberDecoratorFunction,
    StaticThis,
    TypeName,
    staticMemberDecorator,
} from '../decorator-utils';
import { NamesOfType, Parameter } from './parameter';

export type ParameterTypeName = TypeName | 'object' | 'array';

export type OperationReturn<This extends StaticThis<Node>, ReturnT> = Parameter<This, ReturnT> | NamesOfType<ReturnT>;

export type MappedParameters<Params> = {
    [K in keyof Params]-?: { readonly name: string } & Parameter<typeof Node, Params[K]>;
};

export interface BaseOperationDecorator {
    isPublished?: boolean;
    /** The service options */
    serviceOptions?: () => ServiceOption[];
    /** code for access rights (X3 _function_ code) */
    authorizationCode?: string;
    /**
     * authorizedBy on operation level overrides the authorization of operations with custom logic defined on the operation.
     *
     * If an operation has an authorizedBy function, it will take precedence over the default authorizedBy function at the node level.
     *
     * CAUTION: When binding to UI, no args will be available when resolving page metadata bound to the node, therefore
     * the function should cater for the case where no args are passed. Returning undefined will
     * result in the default authorization check for the operation.
     */
    authorizedBy?: string;
    /**
     * Does the operation belong to global namespace?
     */
    isGlobal?: true;
}

export type AuthorizedBy<
    This extends StaticThis<Node>,
    ParamsT extends any[],
    K2 extends keyof This = keyof This,
> = K2 extends string
    ? This[K2] extends (...args: ParamsT) => AsyncResponse<UserAccess | undefined>
        ? K2
        : never
    : never;

/** Argument for &#064;decorators.query or &#064;decorators.mutation decorators */
export interface OperationDecorator<This extends StaticThis<Node>, K extends keyof This>
    extends BaseOperationDecorator {
    isPublished?: boolean;

    /**
     * Is the operation an interop call?
     * Note that it may still have a body, which will then be executed as a local fallback.
     */
    isInterop?: true;

    authorizedBy?: AuthorizedBy<
        This,
        This[K] extends (...args: any[]) => any
            ? [...[options: { action?: string; trackingId?: string }, ...Parameters<This[K]>]]
            : never
    >;

    parameters: This[K] extends (context: Context, ...args: any[]) => any
        ? MappedParameters<Parameters<This[K]> extends [Context, ...infer R1] ? R1 : never>
        : never;

    return: OperationReturn<This, This[K] extends (...args: any[]) => infer ReturnT ? UnPromised<ReturnT> : never>;
}

export interface QueryDecorator<This extends StaticThis<Node>, K extends keyof This>
    extends OperationDecorator<This, K> {
    /** The isolation level */
    isolationLevel?: IsolationLevel;
    /** Can the query be deferred - advanced option reserved to 'high' isolation level */
    isDeferrable?: boolean;

    /** If set to true access to execute this query will be based on access to this operation or lookup */
    isGrantedByLookup?: boolean;
}

export interface BaseMutationDecorator<This extends StaticThis<Node>, K extends keyof This>
    extends OperationDecorator<This, K> {
    /** The isolation level */
    isolationLevel?: IsolationLevel;

    /**
     * startsReadOnly is used to determine whether the static method of a custom mutation is called with a readonly or writable context.
     * It can be set as a boolean or a callback that returns a boolean.
     */
    startsReadOnly?: boolean | ((this: This, context: Context) => boolean);
}

export interface MutationDecorator<This extends StaticThis<Node>, K extends keyof This>
    extends BaseMutationDecorator<This, K> {}

// Simplified parameter type, without generics and with name attribute
export type PlainParameter = Parameter<typeof Node, any> & { name: string };

// Simplified OperationDecorator interface, without the generics and
// with parameters typed as an array rather than a tuple,
export interface PlainOperationDecorator extends BaseOperationDecorator {
    readonly name: string;
    action?: string;
    definingPackage?: Package;
    isolationLevel?: IsolationLevel;
    isDeferrable?: boolean;
    isSchedulable?: boolean;
    isInterop?: boolean;
    operationKind: OperationKind;
    parameters: PlainParameter[];
    return: OperationReturn<typeof Node, any>;
    className?: string;
    /**
     * startsReadOnly is used to determine whether the static method of a custom mutation is called with a readonly or writable context.
     * It can be set as a boolean or a callback that returns a boolean.
     */
    startsReadOnly?: boolean | ((this: typeof Node, context: Context) => boolean);
    /** If set to true, access to execute this query will be granted if the user is authorized for the query or `lookup` operation */
    isGrantedByLookup?: boolean;
}

/** &#064;decorators.mutation(arg) static method decorator */
export function mutation<This extends StaticThis<Node>, K extends keyof This>(
    arg: MutationDecorator<This, K>,
): StaticMemberDecoratorFunction<typeof Node> {
    return staticMemberDecorator<typeof Node, any>('mutations', 'mutation')(arg);
}

/** &#064;decorators.query(arg) static method decorator */
export function query<This extends StaticThis<Node>, K extends keyof This>(
    arg: QueryDecorator<This, K>,
): StaticMemberDecoratorFunction<typeof Node> {
    return staticMemberDecorator<typeof Node, any>('queries', 'query')(arg);
}
