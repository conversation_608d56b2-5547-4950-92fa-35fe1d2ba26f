import { Node } from '../../ts-api';
import { StaticThis } from '../decorator-utils';
import { ParameterTypeName } from './operation-decorator';

/** Parameter descriptor for &#064;decorators.query or &#064;decorators.mutation decorators */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export interface BaseParameter<This extends StaticThis<Node> = typeof Node> {
    name?: string;
    type: ParameterTypeName;
    isMandatory?: boolean;
    isNullable?: boolean;
    isTransientInput?: boolean;
}
