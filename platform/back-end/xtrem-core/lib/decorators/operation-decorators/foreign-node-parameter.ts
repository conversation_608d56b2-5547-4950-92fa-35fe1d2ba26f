import { Node } from '../../ts-api';
import { StaticThis } from '../decorator-utils';
import { BaseParameter } from './base-parameter';

export interface ForeignNodeParameter<This extends StaticThis<Node>> extends BaseParameter<This> {
    node: () => { new (): Node };
}

export interface ReferenceParameter<This extends StaticThis<Node>> extends ForeignNodeParameter<This> {
    type: 'reference';
    isWritable?: boolean;
}

export interface InstanceParameter<This extends StaticThis<Node>> extends ForeignNodeParameter<This> {
    type: 'instance';
}
