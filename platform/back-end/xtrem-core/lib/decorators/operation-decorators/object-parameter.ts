import { Dict } from '@sage/xtrem-shared';
import { Node } from '../../ts-api';
import { StaticThis, TypeName } from '../decorator-utils';
import { BaseParameter } from './base-parameter';
import { Parameter } from './parameter';

export interface ObjectParameter<This extends StaticThis<Node> = typeof Node> extends BaseParameter<This> {
    type: 'object';
    properties: Dict<Parameter<This> | TypeName>;
}
