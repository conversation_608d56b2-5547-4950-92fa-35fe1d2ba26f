import { AsyncResponse } from '@sage/xtrem-async-helper';
import { Context } from '../../runtime/context';
import { Extend, Node, NodeQueryFilter, ValidationContext } from '../../ts-api';

/** Events for &#064;decorators.node decorator */
export interface NodeEvents<This extends Node = Node> {
    /** User filter that applies to all queries */
    getFilters?: (context: Context) => AsyncResponse<NodeQueryFilter<This>[]>;

    /** compatibility alias for prepareBegin */
    prepare?: (this: Extend<This>, cx: ValidationContext) => AsyncResponse<void>;

    /** event before individual prepare on properties */
    prepareBegin?: (this: Extend<This>, cx: ValidationContext) => AsyncResponse<void>;

    /** event before individual prepare on properties */
    prepareEnd?: (this: Extend<This>, cx: ValidationContext) => AsyncResponse<void>;

    /** event raised after a node is manually created (will not be fired when the node is created from db read) */
    createEnd?: (this: Extend<This>) => AsyncResponse<void>;

    /** event before individual control on properties */
    controlBegin?: (this: Extend<This>, cx: ValidationContext) => AsyncResponse<void>;
    /** event after individual control on properties */
    controlEnd?: (this: Extend<This>, cx: ValidationContext) => AsyncResponse<void>;

    /** event before deletion */
    controlDelete?: (this: Extend<This>, cx: ValidationContext, isCascading: boolean) => AsyncResponse<void>;

    /** event before save */
    saveBegin?: (this: Extend<This>) => AsyncResponse<void>;
    /** event after save */
    saveEnd?: (this: Extend<This>) => AsyncResponse<void>;

    /** event before deletion */
    deleteBegin?: (this: Extend<This>, isCascading: boolean) => AsyncResponse<void>;

    /** event after deletion */
    deleteEnd?: (this: Extend<This>, isCascading: boolean) => AsyncResponse<void>;
}

// TODO: move isFrozen to NodeEvents (=> add it to the merge events logic in NodeFactory)
export type NodeRuleName = keyof NodeEvents | 'isFrozen';
