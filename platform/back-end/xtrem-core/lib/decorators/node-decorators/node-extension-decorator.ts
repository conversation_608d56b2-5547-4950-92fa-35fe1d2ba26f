/* eslint-disable @typescript-eslint/explicit-function-return-type */
/** @packageDocumentation @module decorators */
import { ServiceOption } from '../../application';
import {
    Extend,
    Node,
    NodeExtension,
    SubNodeExtension1,
    SubNodeExtension2,
    SubNodeExtension3,
    SubNodeExtension4,
    SubNodeExtension5,
} from '../../ts-api';
import { classDecorator } from '../decorator-utils';
import { ExternalStorageManagerExtension } from '../external-storage-manager';
import { NodeIndexDecorator } from './node-decorator';
import { NodeEvents } from './node-events';

export interface NodeExtensionDecorator<This extends Node> extends NodeEvents<This> {
    extends: () => typeof Node;
    indexes?: NodeIndexDecorator<Extend<This>>[];
    externalStorageManagerExtension?: ExternalStorageManagerExtension<Extend<This>>;
    /** Class service options */
    serviceOptions?: () => ServiceOption[];
    /** Is it a target of a vital reference ? */
    isVitalReferenceChild?: boolean;
    /** Is it a target of an association collection ? */
    isAssociationCollectionChild?: boolean;
    /** Is it a target of an association reference ? */
    isAssociationReferenceChild?: boolean;
    /** node extension can override attachments */
    hasAttachments?: boolean;
    /**
     * Can the node have tags?
     *
     * Nodes with tags will have a new system property '_tags'. This property is managed by the framework.
     */
    hasTags?: true;
    /**
     * Can the node have notes?
     *
     * Nodes with notes will have a new system property '_notes'. This property is managed by the framework.
     */
    hasNotes?: true;
    /** Is this node a source for synchronization */
    isSynchronizable?: true;

    /** Is this node a target for synchronization */
    isSynchronized?: true;
}

/** &#064;decorators.nodeExtension(arg) class extension decorator */
export function nodeExtension<This extends NodeExtension<Node>>(arg: NodeExtensionDecorator<This>) {
    return classDecorator<any, NodeExtensionDecorator<This>>('nodeExtension')(arg);
}

/** &#064;decorators.subNodeExtension1(arg) class extension decorator */
export function subNodeExtension1<This extends SubNodeExtension1<Node>>(arg: NodeExtensionDecorator<This>) {
    return classDecorator<any, NodeExtensionDecorator<This>>('nodeExtension')(arg);
}

/** &#064;decorators.subNodeExtension2(arg) class extension decorator */
export function subNodeExtension2<This extends SubNodeExtension2<Node>>(arg: NodeExtensionDecorator<This>) {
    return classDecorator<any, NodeExtensionDecorator<This>>('nodeExtension')(arg);
}

/** &#064;decorators.subNodeExtension3(arg) class extension decorator */
export function subNodeExtension3<This extends SubNodeExtension3<Node>>(arg: NodeExtensionDecorator<This>) {
    return classDecorator<any, NodeExtensionDecorator<This>>('nodeExtension')(arg);
}

/** &#064;decorators.subNodeExtension4(arg) class extension decorator */
export function subNodeExtension4<This extends SubNodeExtension4<Node>>(arg: NodeExtensionDecorator<This>) {
    return classDecorator<any, NodeExtensionDecorator<This>>('nodeExtension')(arg);
}

/** &#064;decorators.subNodeExtension5(arg) class extension decorator */
export function subNodeExtension5<This extends SubNodeExtension5<Node>>(arg: NodeExtensionDecorator<This>) {
    return classDecorator<any, NodeExtensionDecorator<This>>('nodeExtension')(arg);
}
