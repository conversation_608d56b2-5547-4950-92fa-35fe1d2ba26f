/** @packageDocumentation @module decorators */
import { AsyncResponse, UnPromised } from '@sage/xtrem-async-helper';
import { Dict } from '@sage/xtrem-shared';
import { ServiceOption } from '../../application';
import { Context, StandardOperation, UserAccess } from '../../runtime';
import { Extend, FlatOrderBy, Node, ValidationContext } from '../../ts-api';
import { ClassDecoratorFunction, NodeFilterTag, classDecorator } from '../decorator-utils';
import { ExternalStorageManager } from '../external-storage-manager';
import { NodeEvents } from './node-events';

export type NodeStorage = 'sql' | 'json' | 'external';

export type NotifyOperation = 'created' | 'updated' | 'deleted';

/**
 * * low -> read-committed
 * * medium -> repeatable read
 * * high -> serializable
 */
export type IsolationLevel = 'low' | 'medium' | 'high';
/**
 * Isolation levels for node operations
 */

export interface IsolationLevels {
    /** Isolation level for update mutation */
    create?: IsolationLevel;
    /** Isolation level for update mutation */
    update?: IsolationLevel;
    /** Isolation level for delete mutation */
    delete?: IsolationLevel;
}

/** Node index definition */
export interface NodeIndexDecorator<T extends Node = Node> {
    orderBy: FlatOrderBy<T>;
    isUnique?: boolean;
    isNaturalKey?: boolean;
    name?: string;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export interface NodeUpdateEventArg<This extends Node> {
    modifiedPropertyNames: string[];
}

// Silly type to keep sonar happy
export type StringOrNumberOrNull = string | number | null;

export type NodeKey<T extends Node> = { [K in keyof T]?: UnPromised<T[K]> | StringOrNumberOrNull };

export type NodePropertyName<T extends Node> = Exclude<keyof T & string, '$'>;

export type NodeAuthorizedBy = (
    context: Context,
    propertyOrOperation: string,
    args?: Dict<any>,
) => AsyncResponse<UserAccess | undefined>;

export interface NodePersistHooks<This extends Node> {
    /** save hook
     * Saves the node and all its children (collections).
     *
     * This hook short-circuits all the SQL save logic (saveXxx events, insert, update events).
     * It can be used to serialize the node and send it to another service which will persist it.
     */
    save?: (this: Extend<This>, cx: ValidationContext) => void;

    /** delete hook
     * Deletes the node and all its children (collections).
     *
     * This hook short-circuits all the SQL delete logic.
     * It can be used to send the node key to another service which will delete it.
     */
    delete?: (this: Extend<This>, cx: ValidationContext) => void;

    /** read hook
     * Reads a node given its key.
     *
     * This hook short-circuits all the SQL read logic.
     * It can be used to read the node from another service which persists it.
     *
     * Note: there is no query hook yet, only read. Query is more challenging because we
     * have to pass paging/filtering/sorting parameters.
     */
    read?: (context: Context, key: NodeKey<This>) => Extend<This> | null;
}

export interface NodeTableHooks<This extends Node> {
    /** insert hook - not called if storage is 'sql'
     * Mandatory if persistency not handled by framework
     */
    insert?: (this: Extend<This>) => void;
    /** update hook - not called if storage is 'sql'
     * Mandatory if persistency not handled by framework
     */
    update?: (this: Extend<This>, data: Partial<This>, arg: NodeUpdateEventArg<This>) => number;
    /** delete hook - not called if storage is 'sql'
     * Mandatory if persistency not handled by framework
     */
    delete?: (this: Extend<This>) => number;
}

/** Parameter type for &#064;decorators.node decorator */
export interface NodeDecorator<This extends Node = Node> extends NodeEvents<This> {
    /** Are the node instances cached. This attribute should only set on nodes that change infrequently */
    isCached?: true;
    /** Is there a unique index based on a hash of the contents. Implies that records are immutable (inserted but never updated) */
    isContentAddressable?: true;
    /** Package name */
    package?: string;
    /** May be used by external storage nodes */
    tableName?: string;
    /** Class storage */
    storage?: NodeStorage;
    /** data layer */
    isSetupNode?: boolean;
    /** node has a vendor property, must also have a naturalKey and be a setup node and the default is true in that case */
    hasVendorProperty?: boolean;
    /** node has a layer property */
    hasLayerProperty?: boolean;
    /** node can have attachments */
    hasAttachments?: boolean;

    /**
     * Can the node have tags?
     *
     * Nodes with tags will have a new system property '_tags'. This property is managed by the framework.
     */
    hasTags?: true;
    /**
     * Can the node have notes?
     *
     * Nodes with notes will have a new system property '_notes'. This property is managed by the framework.
     */
    hasNotes?: true;
    /** Indicates whether this node is abstract and is only intended to be accessed through its subclasses */
    isAbstract?: true;
    /** Is the node instance frozen (immutable)? */
    isFrozen?: boolean | ((this: Extend<This>) => AsyncResponse<boolean>);
    /** Can the class be read/queried? */
    canRead?: boolean;
    /** Can new nodes be created? */
    canCreate?: boolean;
    /** Can existing nodes be updated? */
    canUpdate?: boolean;
    /** Can nodes be deleted? */
    canDelete?: boolean;
    /** Can nodes be duplicated? */
    canDuplicate?: true;
    /** should the attachments be duplicated? */
    duplicateAttachments?: boolean;
    /** Can nodes be updated via a async bulk mutation? */
    // see later for bulkUpdate
    // canBulkUpdate?: boolean;
    /** Can nodes be deleted via a async bulk mutation? */
    canBulkDelete?: boolean;
    /** Are nodes indexed in the search engine */
    canSearch?: boolean;
    /** code for access rights (X3 _function_ code) */
    authorizationCode?: string;
    /**
     * authorizedBy is a static method that overrides the authorization with custom logic defined on the node or operation level.
     *
     * Note: Permissions defined by activities will effectively be ignored if there are authorizedBy
     * functions for the relevant permission.
     *
     * CAUTION: When binding to UI, no args will be available when resolving page metadata bound to the node, therefore
     * the function should cater for the case where no args are passed. Returning undefined will
     * result in the default authorization check for the operation.
     */
    authorizedBy?: NodeAuthorizedBy;
    /** Do we include the export mutation on this node */
    canExport?: boolean;

    /* Is the node shared by all tenants and tenantId is not required*/
    isSharedByAllTenants?: boolean;
    /** published in graphql API? */
    isPublished?: boolean;

    /** Class service options */
    serviceOptions?: () => ServiceOption[];

    /** Node holding the table definition (if table is shared by several nodes) */
    tableNode?: () => typeof Node;
    /** Indexes */
    indexes?: NodeIndexDecorator<This>[];

    externalStorageManager?: ExternalStorageManager<This>;

    /** key property names */
    keyPropertyNames?: (keyof This)[];

    /** Returns whether the BulkDelete function can be used on this Node */
    canDeleteMany?: boolean;

    /** Is it a target of a vital reference ? */
    isVitalReferenceChild?: boolean;

    /** Is it a target of a vital collection ? */
    isVitalCollectionChild?: boolean;

    /** Is it a target of an association collection ? */
    isAssociationCollectionChild?: boolean;

    /** Is it a target of an association reference ? */
    isAssociationReferenceChild?: boolean;

    /** List of notification operations to trigger */
    notifies?: NotifyOperation[];

    /**
     * Indicates whether the node is from the platform. These nodes can't be extended
     */
    isPlatformNode?: boolean;

    /**
     * Indicates whether a platform node is included in the tenant export. The common use case is to not export them
     */
    isPlatformNodeExportable?: boolean;

    /**
     * Indicates whether a node is skipped when we extract the layers
     */
    isSkippedByLayerExtract?: boolean;

    /** Isolation level for CRUD mutations */
    isolationLevels?: IsolationLevels;

    /**
     * Indicates if the property should be cleared to its default value when we do a reset
     */
    isClearedByReset?: boolean | ((this: Extend<This>) => AsyncResponse<boolean>);

    /**
     * Does node provide an access rights node (site, legislation, ...).
     * When this is set we will filter the query but the corresponding allowed codes
     * Only site is implemented today
     */
    provides?: NodeFilterTag[];

    /**
     * Stores the list of operations that require admin privileges.
     */
    adminOperations?: 'all' | 'allMutations' | (keyof Node | StandardOperation)[];

    /** Can this node be customized with customFields */
    isCustomizable?: boolean;

    /** Is this node a source for synchronization */
    isSynchronizable?: true;

    /** Is this node a target for synchronization */
    isSynchronized?: true;

    /** Can this node be transformed and used in interop queries or mutations */
    isInterop?: true;

    /** Queue name for this node */
    queue?: string;

    /** Do non-nullable references to this node default to the found record if lookup returns one record */
    defaultsToSingleMatch?: boolean;

    /** Do not allow a read operation when user has only lookup access
     * The default is to allow the read operation, but only access to lookup access fields
     * In certain cases we do not want to allow direct read on a node, for example the User node
     */
    denyReadOnLookupOnlyAccess?: boolean;
}

/** &#064;decorators.node(arg) class decorator */
export function node<This extends Node>(arg: NodeDecorator<This>): ClassDecoratorFunction<typeof Node> {
    return classDecorator<typeof Node, NodeDecorator<This>>('node')(arg);
}
