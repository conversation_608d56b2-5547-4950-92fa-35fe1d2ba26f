/** @packageDocumentation @module decorators */
import { AsyncResponse } from '@sage/xtrem-async-helper';
import { ServiceOption } from '../../application';
import { Extend, Node } from '../../ts-api';
import { ClassDecoratorFunction, classSubNodeDecorator } from '../decorator-utils';
import { NodeIndexDecorator } from './node-decorator';
import { NodeEvents } from './node-events';

export interface SubNodeDecorator<This extends Node> extends NodeEvents<This> {
    extends: () => typeof Node;
    isPublished?: boolean;
    isAbstract?: boolean;
    canDeleteMany?: boolean;
    isSetupNode?: boolean;
    isVitalReferenceChild?: boolean;
    isVitalCollectionChild?: boolean;
    isAssociationReferenceChild?: boolean;
    isAssociationCollectionChild?: boolean;
    isFrozen?: boolean | ((this: Extend<This>) => AsyncResponse<boolean>);
    canRead?: boolean;
    canCreate?: boolean;
    canUpdate?: boolean;
    canDelete?: boolean;
    canDuplicate?: true;
    // see later for bulkUpdate
    // canBulkUpdate?: boolean;
    canBulkDelete?: boolean;
    canSearch?: boolean;
    /** can override if false on base node */
    hasAttachments?: boolean;
    /**
     * Can the node have tags?
     *
     * Nodes with tags will have a new system property '_tags'. This property is managed by the framework.
     * This decorator can only be overriden when set to false on the base node.
     */
    hasTags?: true;
    /**
     * Can the node have notes?
     *
     * Nodes with notes will have a new system property '_notes'. This property is managed by the framework.
     * This decorator can only be overriden when set to false on the base node.
     */
    hasNotes?: true;
    serviceOptions?: () => ServiceOption[];
    isClearedByReset?: boolean | ((this: Extend<This>) => AsyncResponse<boolean>);
    /** Indexes */
    indexes?: NodeIndexDecorator<This>[];
    /** Can this node be customized with customFields */
    isCustomizable?: boolean;
    /** Is this node a source for synchronization */
    isSynchronizable?: true;
    /** Is this node a target for synchronization */
    isSynchronized?: true;
}

/**
 * Describes a node that inherits from another (the base node)
 * @param arg
 */
export function subNode<This extends Node>(arg: SubNodeDecorator<This>): ClassDecoratorFunction<typeof Node> {
    arg.isAbstract = !!arg.isAbstract;
    arg.isPublished = !!arg.isPublished;
    arg.canDeleteMany = !!arg.canDeleteMany;
    // see later for bulkUpdate
    // arg.canBulkUpdate = !!arg.canBulkUpdate;
    arg.canBulkDelete = !!arg.canBulkDelete;
    arg.canRead = !!arg.canRead;
    arg.canCreate = !!arg.canCreate;
    arg.canUpdate = !!arg.canUpdate;
    arg.canDelete = !!arg.canDelete;
    arg.canSearch = !!arg.canSearch;

    return classSubNodeDecorator<typeof Node, This>('node')(arg);
}
