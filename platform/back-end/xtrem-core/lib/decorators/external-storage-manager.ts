import { <PERSON><PERSON><PERSON><PERSON>, AsyncReader, AsyncResponse } from '@sage/xtrem-async-helper';
import { Dict } from '@sage/xtrem-shared';
import { BaseCollection } from '../collections/base-collection';
import { Context, NodeFactory, PropertyAndValue } from '../runtime/index';
import { OrderByClause } from '../sql';
import { Aggregate, Extend, Node, NodeQueryFilter, NodeSelector, OrderBy, ValidationContext } from '../ts-api';
import { InternalPropertyJoin } from '../types';
import { FilterTag } from './decorator-utils';

export interface NodeExternalQueryOptions<T extends Node> {
    filters: NodeQueryFilter<T>[];
    orderBy?: OrderBy<T>;
    first?: number;
    after?: string;
    last?: number;
    before?: string;
    locale?: string;
    forUpdate?: boolean;
    aggregate?: Aggregate;
    count?: boolean;
    /** The query expects a single record in the result */
    singleResultRequest?: boolean;
    /** Selector, for low level context.select(...) queries */
    selector?: NodeSelector<T>;
    collection?: BaseCollection;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export interface ExternalStorageManagerExtension<This extends Node = Node> {}

export interface AnyRecordWithId extends AnyRecord {
    _id: string;
}

export interface ExternalStorageManager<This extends Node> {
    factory?: NodeFactory;

    verifyFactory?: () => void;

    /** insert hook - not called if storage is 'sql'
     * Mandatory if persistency not handled by framework
     */
    insert?: (node: Extend<This>, cx: ValidationContext) => AsyncResponse<AnyRecordWithId>;
    /** update hook - not called if storage is 'sql'
     * Mandatory if persistency not handled by framework
     */
    update?: (node: Extend<This>, cx: ValidationContext) => AsyncResponse<AnyRecord[]>;
    /**
     *  delete hook - not called if storage is 'sql'
     * Mandatory if persistency not handled by framework
     */
    delete?: (node: Extend<This>, cx: ValidationContext) => AsyncResponse<number>;

    canCreate?: (canCreate: boolean) => boolean;
    canUpdate?: (canUpdate: boolean) => boolean;
    canDelete?: (canDelete: boolean) => boolean;
    canDeleteMany?: (canDeleteMany: boolean) => boolean;

    getAllowedAccessCodes?: (
        context: Context,
        tag: FilterTag,
        allowedAccessCodes: string[] | null,
    ) => AsyncResponse<string[] | null>;

    /**
     * query and read - hooks getRawReader in SqlQuery
     **/
    query?: (context: Context, options: NodeExternalQueryOptions<This>) => AsyncReader<any>;

    getCollectionJoin: (propertyName: string) => InternalPropertyJoin<This>;

    getReferenceJoin: (propertyName: string) => InternalPropertyJoin<This>;

    mapRecordIn?: (record: any) => any;

    mapAggregateRecordIn?: (record: any) => any;

    parseOrderBy(context: Context, orderBy: OrderBy<Node> | undefined): OrderByClause[];

    parseCursor(orderByClauses: OrderByClause[], value: string): PropertyAndValue[];

    getKeyValues(
        context: Context,
        values: any,
        options?: {
            allocateTransient: boolean;
            isTransient?: boolean;
            isOnlyForDefaultValues?: boolean;
            isOnlyForDuplicate?: boolean;
            writable?: boolean;
            isOnlyForLookup?: boolean;
            collection?: BaseCollection;
        },
    ): AnyRecord;

    getJoinValues(node: Extend<This>, data: any, propertyName: string, index?: number): AsyncResponse<Dict<any>>;

    defaultOrderBy: OrderBy<Node>;

    skipValidation?: boolean;

    getId(context: Context, values: Dict<any>): string;

    isKeyPropertyTransient(propertyName: string, value: any): boolean;

    isReverseReferenceProperty(propertyName: string): boolean;
}
