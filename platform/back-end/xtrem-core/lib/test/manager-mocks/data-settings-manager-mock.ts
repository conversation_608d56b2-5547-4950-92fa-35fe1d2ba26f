import { decorators, StaticThis } from '../../decorators';
import { Context, DataSettingsManager } from '../../runtime/context';
import { loggers } from '../../runtime/loggers';
import { Node } from '../../ts-api';
import { StringDataType } from '../../types';

const nameDataType = new StringDataType({ maxLength: 80 });
const descriptionDataType = new StringDataType({ maxLength: 200 });
@decorators.node<TestSysVendor>({
    isPublished: true,
    storage: 'sql',
    canCreate: false,
    canRead: true,
    canUpdate: true,
    canDeleteMany: false,
    isPlatformNode: true,
    isSharedByAllTenants: true,
    hasVendorProperty: false,
    indexes: [
        {
            orderBy: { name: +1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    isSetupNode: true,
})
export class TestSysVendor extends Node {
    /**
     * the vendor's name
     */
    @decorators.stringProperty<TestSysVendor, 'name'>({
        isPublished: true,
        isStored: true,
        isFrozen: true,
        dataType: () => nameDataType,
    })
    readonly name: Promise<string>;

    /**
     * the vendor's description
     */
    @decorators.stringProperty<TestSysVendor, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly description: Promise<string>;
}

/**
 * A mock for DataSettingsManager: should only be used for unit-tests
 */
class DataSettingsManagerMock implements DataSettingsManager {
    // eslint-disable-next-line class-methods-use-this
    getSysVendorNode(): StaticThis<Node> {
        return TestSysVendor;
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars, class-methods-use-this
    sageVendorId(context: Context): number {
        return 1;
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars, class-methods-use-this
    initializeManager(_context: Context): void {
        loggers.test.verbose(() => 'Data settings manager is loaded');
    }
}

export const dataSettingsManagerMock = new DataSettingsManagerMock();
