import { Context, LocalizationManager } from '../../runtime/context';

/**
 * A mock for LocalizationManager: should only be used for unit-tests
 */
export const localizationManagerMock: LocalizationManager = {
    initializeManager() {},

    // The Locale node in xtrem-services will enforce that the default locale should also be the master locale for that language
    getDefaultTenantLocale(): string {
        return 'en-GB';
    },
    isMasterLocale(context: Context): boolean {
        return ['en-GB', 'fr-FR', 'de-DE'].includes(context.currentLocale);
    },
    createTenantLocale(): void {},
};
