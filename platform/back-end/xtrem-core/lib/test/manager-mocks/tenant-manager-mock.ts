import { Application } from '../../application';
import { Context, rootUserEmail, TenantInfo, TenantManager } from '../../runtime/context';

/**
 * A mock for TenantManager: should only be used for unit-tests
 */
class TenantManagerMock implements TenantManager {
    private _tenantId: string | null;

    private _additionalTenants: TenantInfo[];

    set additionalTenants(tenants: TenantInfo[]) {
        this._additionalTenants = tenants;
    }

    // eslint-disable-next-line class-methods-use-this
    async ensureTenantExists(
        context: Context,
        options: {
            customer: { id: string; name: string };
            tenant: { id: string; name: string };
        },
    ): Promise<void> {
        await context.application.withCommittedContext(
            options.tenant.id,
            createUserContext => Context.accessRightsManager.createRequiredUsers(createUserContext),
            { userEmail: rootUserEmail, withoutTransactionUser: true },
        );
    }

    listTenantsIds(): string[] {
        if (this._tenantId == null) return [];
        return [this._tenantId];
    }

    getTenantsInfo(context: Context, tenantId?: string): TenantInfo[] {
        if (tenantId && tenantId !== this._tenantId) return [];
        if (this._tenantId == null) return [];
        return [
            {
                id: this._tenantId,
                name: 'Test Tenant',
                directoryName: 'test-tenant',
                customer: {
                    id: '1',
                    name: 'Test customer',
                },
            },
            ...this._additionalTenants,
        ];
    }

    initializeManager(context: Context): void {
        this._tenantId = context.tenantId;
    }

    // eslint-disable-next-line class-methods-use-this, @typescript-eslint/no-unused-vars
    async deleteTenant(application: Application, tenantId: string): Promise<void> {
        /* stub */
    }
}

export const tenantManagerMock = new TenantManagerMock();
