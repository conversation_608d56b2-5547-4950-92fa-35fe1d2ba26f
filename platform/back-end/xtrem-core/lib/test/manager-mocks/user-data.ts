import { rootUserEmail, UserInfo } from '../../runtime/context';

const _userData: Omit<UserInfo, '_id'>[] = [
    {
        email: rootUserEmail,
        userName: rootUserEmail,
        firstName: 'Root',
        lastName: 'Sage',
        isActive: true,
        userType: 'system',
        isAdministrator: true,
        isRequired: true,
    },
    {
        email: '<EMAIL>',
        userName: '<EMAIL>',
        firstName: 'Scope synchronization',
        lastName: 'Sage',
        isActive: true,
        userType: 'system',
    },
    {
        email: '<EMAIL>',
        firstName: 'Unit',
        lastName: 'Test',
        isActive: true,
        userType: 'application',
        isAdministrator: true,
    },
    {
        email: '<EMAIL>',
        firstName: 'One',
        lastName: 'Site',
        isActive: true,
        userType: 'application',
    },
    {
        email: '<EMAIL>',
        firstName: '<PERSON>',
        lastName: 'Duck',
        isActive: true,
        userType: 'application',
    },
    {
        email: '<EMAIL>',
        firstName: '<PERSON>',
        lastName: 'da <PERSON> e <PERSON>lo <PERSON>raes',
        isActive: true,
        userType: 'application',
    },
    {
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        isActive: true,
        userType: 'application',
    },
    {
        email: '<EMAIL>',
        firstName: 'Jane',
        lastName: 'Doe',
        isActive: true,
        userType: 'application',
    },
    {
        email: '<EMAIL>',
        firstName: 'Foo',
        lastName: 'Test',
        isActive: true,
        userType: 'application',
    },
    {
        email: '<EMAIL>',
        firstName: 'Foo1',
        lastName: 'Test',
        isActive: true,
        userType: 'application',
    },
    {
        email: '<EMAIL>',
        firstName: 'All',
        lastName: 'Auth',
        isActive: true,
        userType: 'application',
    },
    {
        email: '<EMAIL>',
        firstName: 'Site 1 3',
        lastName: 'Auth',
        isActive: true,
        userType: 'application',
    },
    {
        email: '<EMAIL>',
        firstName: 'Access 1 2',
        lastName: 'Auth',
        isActive: true,
        userType: 'application',
    },
    {
        email: '<EMAIL>',
        firstName: 'None',
        lastName: 'Auth',
        isActive: true,
        userType: 'application',
    },
    {
        email: '<EMAIL>',
        firstName: 'User1',
        lastName: 'Test',
        isActive: true,
        userType: 'application',
    },
    {
        email: '<EMAIL>',
        firstName: 'Lookup',
        lastName: 'Access',
        isActive: true,
        userType: 'application',
    },
    {
        email: '<EMAIL>',
        firstName: 'Admin',
        lastName: 'Persona',
        isActive: true,
        isAdministrator: true,
        isDemoPersona: true,
        userType: 'application',
    },
    {
        email: '<EMAIL>',
        firstName: 'Demo',
        lastName: 'Persona',
        isActive: true,
        isDemoPersona: true,
        userType: 'application',
    },
    {
        email: '<EMAIL>',
        firstName: 'Demo2',
        lastName: 'Persona',
        isActive: true,
        isDemoPersona: true,
        userType: 'application',
    },
    {
        email: '<EMAIL>',
        firstName: 'API',
        lastName: '0000000000',
        isActive: true,
        isApiUser: true,
        userType: 'application',
    },
    {
        email: '<EMAIL>',
        firstName: 'Copilot',
        lastName: '0000000000',
        isActive: true,
        isApiUser: false,
        userType: 'application',
    },
];

export const userData: UserInfo[] = _userData.map((u, i) => ({ _id: i + 1, ...u }));
