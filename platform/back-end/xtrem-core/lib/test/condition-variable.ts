import { loggers } from '../runtime/loggers';

const logger = loggers.test;

/**
 * Utility class for tests that need synchronization between concurrent tasks
 *
 * See https://en.wikipedia.org/wiki/Monitor_(synchronization)#Condition_variables
 *
 * This is a simplistic implementation with promises.
 * Provides only `notifyAll`, not `notify`.
 */
export class ConditionVariable {
    /** @internal */
    private resolve: () => void;

    /** @internal */
    private promise = new Promise<void>(resolve => {
        this.resolve = resolve;
    });

    /** @internal */
    constructor(readonly name: string) {}

    /**
     * Waits on the condition to be notified
     */
    async wait(): Promise<void> {
        logger.verbose(() => `WAIT   ${this.name}`);
        await this.promise;
        logger.verbose(() => `RESUME ${this.name}`);
    }

    /**
     * Notifies all the waiters on this condition
     */
    notifyAll(): void {
        logger.verbose(() => `NOTIFY ${this.name}`);
        this.resolve();
    }
}
