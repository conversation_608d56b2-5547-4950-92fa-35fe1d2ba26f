import { asyncArray } from '@sage/xtrem-async-helper';
import * as _ from 'lodash';
import { ApplicationCreateSqlSchema } from '../application';
import { Application, CoreHooks, Node, StaticThis, SystemProperties, Test } from '../index';
import { NodeFactory, rootUserEmail } from '../runtime';
import { loggers } from '../runtime/loggers';
import { Table, TestStatus } from '../sql/schema';
import { userData } from './manager-mocks/user-data';

/**
 * This module contains many functions to manipulate (create, init, drop, ...)
 * the db tables in the tests
 */
const tablesToClear: Table[] = [];

/**
 * Initialized constrained tables
 * tables should be ordered entered so that the most constrained should be the last.
 * ex. document->documentLine
 * @param tables
 */
export async function initTables(
    tables: { nodeConstructor: typeof Node; data: any[]; forceDelete?: boolean }[],
    options?: { skipUsers?: boolean },
): Promise<void> {
    loggers.test.info(`Initializing test tables (${tables.map(table => table.nodeConstructor.name)})`);

    if (!Test.application)
        throw new Error('cannot call initTables: Test.application is not set (forgot to call setup?)');

    const tablesToInit = options?.skipUsers
        ? tables
        : [
              {
                  nodeConstructor: CoreHooks.sysManager.getUserNode(),
                  data: userData.map(row => _.omit(row, ['_id'])),
                  forceDelete: true,
              },
              ...tables,
          ];

    await Test.application.createContextForDdl(
        context =>
            asyncArray(tablesToInit).forEach(async table => {
                const factory = Test.application.getFactoryByConstructor(table.nodeConstructor);
                if (await factory.table.tableExists(context))
                    await dropTestTable(table.nodeConstructor, table.forceDelete);
            }),
        { ...Test.convertOptions(undefined), description: () => 'Drop test tables' },
    );

    await _fillTables(
        Test.application,
        tablesToInit.map(table => {
            return {
                factory: Test.application.getFactoryByConstructor(table.nodeConstructor),
                data: table.data,
            };
        }),
    );
}

async function _fillTables(application: Application, tables: { factory: NodeFactory; data: any[] }[]): Promise<void> {
    // need to verify factory with a test context, so that all tables are created as un-logged

    Test.state = {
        application: 'Test app',
        layers: [],
        nowMock: '',
    };

    // Note: UpgradeSqlSchema.verifyAndCreateTables must use force=true
    // if not, no upgrade will be done as the package version already matches
    await Test.application.createContextForDdl(context => ApplicationCreateSqlSchema.verifyAndCreateTables(context), {
        description: () => 'Verify and create tables',
    });

    await Test.withCommittedContext(createUserContext => Test.ensureTestTenantExists(createUserContext), {
        userEmail: rootUserEmail,
        withoutTransactionUser: true,
        description: () => 'Ensure test tenant exists',
    });

    await asyncArray(tables).forEach(async tbl => {
        await Test.withCommittedContext(
            async context => {
                const table = tbl.factory.table;
                let tblData = tbl.data;

                // Remove users created by ensureTestTenantExists
                if (tbl.factory.name === CoreHooks.sysManager.getUserNode().name) {
                    tblData = tbl.data.filter(row => row.email !== rootUserEmail);
                }

                if (!tablesToClear.includes(table)) {
                    tablesToClear.push(table);
                }
                let baseTable = table.baseTable;
                while (baseTable) {
                    if (!tablesToClear.includes(baseTable)) {
                        tablesToClear.push(baseTable);
                    }
                    baseTable = baseTable.baseTable;
                }
                const idColumnName = SystemProperties.idProperty(table.factory).columnName || '';
                await asyncArray(tblData).forEach(async rec => {
                    await asyncArray(Object.keys(rec)).forEach(async propName => {
                        const val = rec[propName];
                        const property = table.factory.propertiesByName[propName];

                        if (typeof val === 'function') {
                            // Replace the function by its value. Useful to create references
                            // See @sage\xtrem-core\test\runtime\subclassing-test.ts
                            rec[propName] = rec[propName]();
                        } else if (property?.isStringProperty() && property.isStoredEncrypted) {
                            // encrypt value in database
                            rec[propName] = context.vault.recordValue(await context.vault.encrypt(val));
                        } else if (property?.isReferenceProperty() && typeof val === 'string' && val[0] === '#') {
                            rec[propName] = await property.targetFactory.resolveReferenceId(context, val);
                        }
                    });
                    if (!rec._customData) rec._customData = {};
                    // Insert the record (and put the autoIds in the original data)
                    const returningValues = await table.insert(context, rec);
                    if (returningValues[idColumnName] != null) rec._id = returningValues[idColumnName];
                });
                table.markAsModifiedForTests();
            },
            Test.convertOptions({ user: { email: rootUserEmail } }),
        );
        await tbl.factory.fixAutoIncrementSequences(null);
    });
}

/**
 * @internal
 * Drops some tables
 *
 * @param factories the list of the factories
 */
export async function dropTables(application: Application, factories: NodeFactory[]): Promise<void> {
    await Test.withCommittedContext(context =>
        asyncArray(factories).forEach(factory => factory.table.dropTable(context, { ifExists: true, cascade: true })),
    );
}

/**
 * @internal
 * USE WITH EXTREME CAUTION !!!! Drops the table in db - can't be recovered
 * Tables will be dropped with the CASCADE option by default
 * @param clas
 * @param cascade
 */
export async function dropTestTable(clas: StaticThis<Node>, forceDelete: boolean = false): Promise<void> {
    const factory = Test.application.getFactoryByConstructor(clas);
    const table = factory.table;
    if ((table.name.startsWith('sys_') || table.name.startsWith('system_')) && !forceDelete) return;
    if (!table.name.startsWith('test_') && !forceDelete)
        throw new Error(`${table.name}: Can only drop 'test_*' tables`);

    await Test.withReadonlyContext(async context => {
        if (factory.baseFactory && (await factory.baseFactory.table.tableExists(context))) {
            await dropTestTable(factory.baseFactory.nodeConstructor);
        }
        await table.dropTable(context, { ifExists: true, cascade: true });
        if (await table.tableExists(context)) throw new Error(`The table ${table.name} could not be dropped`);
    }, Test.convertOptions(undefined));
}

/**
 * Restores the db by dropping all the tables that were created by the initTables function
 */
export async function restoreTables(): Promise<void> {
    await asyncArray(tablesToClear.filter(table => table.testStatus === TestStatus.modified)).forEach(table =>
        dropTestTable(table.factory.nodeConstructor),
    );
    tablesToClear.length = 0;
}
