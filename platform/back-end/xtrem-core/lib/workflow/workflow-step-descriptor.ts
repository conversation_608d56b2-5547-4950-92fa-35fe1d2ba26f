import { ServiceOption } from '../application/service-option';
import { WorkflowStepIcon } from './workflow-step-icon';

export interface WorkflowStepUiOptions {
    icon: WorkflowStepIcon;
    color: string;
    configurationPage: string;
}

export interface WorkflowStepUiDescriptor extends WorkflowStepUiOptions {
    type: 'event' | 'action' | 'condition';
    key: string;
    title: string;
    description: string;
}

export interface WorkflowStepDescriptor<ConfigT extends {} = {}> {
    type: 'event' | 'action' | 'condition';
    key: string;
    title: string;
    description: string;

    serviceOptions?: () => ServiceOption[];

    startTopics?: string;
    resumeTopics?: string;

    // Control
    minOutputs?: number;
    maxOutputs?: number;

    defaultConfig?: ConfigT;

    ui: WorkflowStepUiOptions;
}

export interface WorkflowStepInterface {
    descriptor: WorkflowStepDescriptor;
}
