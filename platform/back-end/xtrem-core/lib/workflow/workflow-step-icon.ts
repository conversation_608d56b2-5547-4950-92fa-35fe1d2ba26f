import { EnumDataType } from '../types';

export enum WorkflowStepIconEnum {
    'addons',
    'bright',
    'clock',
    'print',
    'mail',
    'megaphone',
    'accounting',
    'binocular',
    'database',
    'pencil',
    'connected',
    'hourglass',
    'undo',
}

export type WorkflowStepIcon = keyof typeof WorkflowStepIconEnum;

export const workflowStepIconDataType = new EnumDataType<WorkflowStepIcon>({
    enum: WorkflowStepIconEnum,
    filename: __filename,
});
