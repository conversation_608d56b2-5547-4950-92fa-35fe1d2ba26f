import * as _ from 'lodash';

/** Mock options provided by a workflow scenario */
export interface WorkflowMockOptions {
    path: string;
    calls: { input: any; output: any }[];
}

export abstract class WorkflowMock {
    constructor(readonly options: WorkflowMockOptions) {}

    abstract execute<T>(body: () => Promise<T>): Promise<T>;

    static loadMock(options: WorkflowMockOptions): Promise<WorkflowMock> {
        const lastSlash = options.path.lastIndexOf('/');
        if (lastSlash === -1) throw new Error(`Invalid mock path ${options.path}`);
        const modulePath = options.path.substring(0, lastSlash);
        const mockName = options.path.substring(lastSlash + 1);

        try {
            // eslint-disable-next-line import/no-dynamic-require, global-require
            const mockModule = require(modulePath);
            const mockConstructor = _.get(mockModule, mockName);
            if (!mockConstructor) throw new Error(`Mock not found at path ${options.path}`);
            if (typeof mockConstructor !== 'function')
                throw new Error(`Mock is not a class constructor at path ${options.path}`);
            // eslint-disable-next-line new-cap
            return new mockConstructor(options);
        } catch (e) {
            throw new Error(`Error loading mock ${options.path}: ${e.stack}`);
        }
    }

    static async run<T>(mocks: WorkflowMockOptions[] | undefined, body: () => Promise<T>): Promise<T> {
        if (!mocks?.length) return body();
        const [mockOptions, ...rest] = mocks;
        const mock = await this.loadMock(mockOptions);
        return mock.execute(() => WorkflowMock.run(rest, body));
    }
}
