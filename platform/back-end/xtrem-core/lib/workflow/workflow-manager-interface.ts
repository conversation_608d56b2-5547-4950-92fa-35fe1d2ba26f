import { AsyncResponse, Dict, WorkflowError, WorkflowVariable } from '@sage/xtrem-shared';
import type { Context } from '../runtime';
import { WorkflowMockOptions } from './workflow-mock';
import { WorkflowStepDescriptor, WorkflowStepInterface } from './workflow-step-descriptor';

export interface WorkflowRunOptions {
    tenantId: string;
    userEmail: string;
    loginEmail: string;
    mocks?: WorkflowMockOptions[];
    locale?: string;
}

export interface WorkflowStartEvent<PayloadT = unknown> {
    topic: string;
    payload: PayloadT;
}

export interface WorkflowResult {
    status: string;
    variables?: Dict<WorkflowVariable>;
    errors?: WorkflowError[];
}

export interface WorkflowManagerInterface {
    registerWorkflowStepConstructor(packageName: string, workflowStep: WorkflowStepInterface): void;
    getWorkflowStepDescriptors(context: Context): AsyncResponse<WorkflowStepDescriptor[]>;
    getWorkflowStepDescriptor(key: string): WorkflowStepDescriptor;
    getWorkflowTopics(): string[];

    runTest(startEvent: WorkflowStartEvent, options: WorkflowRunOptions): Promise<WorkflowResult>;
    start(): Promise<void>;
}
