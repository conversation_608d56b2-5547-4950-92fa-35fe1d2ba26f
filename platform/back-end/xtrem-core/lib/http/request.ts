import { Request, Response } from 'express';

interface RequestWithId extends Request {
    [requestIdSymbol]?: number | string;
}

const requestIdSymbol = Symbol('requestId');

export function setRequestId(request: RequestWithId, id: number | string): void {
    if (getRequestId(request) === undefined) {
        request[requestIdSymbol] = id;
    }
}

export function getRequestId(request: RequestWithId): number | string | undefined {
    return request ? request[requestIdSymbol] : undefined;
}

export function getResponseId(response: Response): number | string | undefined {
    return response ? (response.req as RequestWithId)?.[requestIdSymbol] : undefined;
}
