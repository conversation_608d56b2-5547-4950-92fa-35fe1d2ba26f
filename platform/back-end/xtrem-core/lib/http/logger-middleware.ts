/**
 * This module provides the HTTP loggers
 */
import { ConfigManager } from '@sage/xtrem-config';
import { Logger } from '@sage/xtrem-log';
import { Handler, NextFunction, Request, Response } from 'express';
import { getRequestId, setRequestId } from './request';

/**
 * The compression middleware must be disabled in verbose and debug levels
 * because we need the response body in clear text.
 *
 * @returns whether the compression middleware should be loaded or not.
 */
export function loggerAllowsCompress(logger: Logger): boolean {
    // note: we only need to test 'verbose' as 'debug' active implies 'verbose' active.
    return !logger.isActive('verbose');
}

function truncate(str: string, length = 80): string {
    const lengthSuffix = length < 10 ? '' : ` (len=${str.length})`;
    return str.length > length ? `${str.substring(0, length)}...${lengthSuffix}` : str;
}

// Truncate all string values (except _ids) in JSON body
function getBodyJsonSanitizer(): (key: string, value: any) => any {
    if (ConfigManager.current.deploymentMode === 'development') return (_key, value) => value;

    return (key, value) => {
        if (typeof value !== 'string') return value;
        if (key === '_id') return value;
        return '...';
    };
}

/**
 * Verbose logger methods.
 * These methods need to sanitize the values included into the logs, but only in production mode.
 */
class HttpVerbose {
    constructor(private readonly service: string) {}

    // Sanitize JSON from string rather than object.
    static bodyStringSanitizer(str: string): string {
        try {
            return JSON.stringify(JSON.parse(str), getBodyJsonSanitizer());
        } catch {
            return truncate(str, 3);
        }
    }

    logRequestBody(request: Request): string {
        if (!request.body) return '';
        const id = getRequestId(request);
        const json = JSON.stringify(request.body, getBodyJsonSanitizer());
        return `HTTP ${this.service} request  ${id} body: ${truncate(json)}`;
    }

    logResponse(response: Response, part: 'chunk' | 'end', str: Buffer | string | undefined): string {
        const id = getRequestId(response.req);
        // truncate and replace newlines to fit it into a single line.
        if (typeof str === 'string') {
            return `HTTP ${this.service} response ${id} ${part}: ${
                str && truncate(HttpVerbose.bodyStringSanitizer(str).replace(/\n/g, '\\n'))
            }`;
        }
        const slice = str == null ? `${str}` : `0x${str.subarray(0, 3).toString('hex')} [length: ${str.length}]`;
        return `HTTP ${this.service} response ${id} ${part}: ${slice}`;
    }
}

/**
 * Debug logger methods
 * These methods don't need special security as debug level logging is only allowed in dev mode.
 */
class HttpDebug {
    constructor(private readonly service: string) {}

    logRequestBody(request: Request): string {
        if (!request.body) return '';
        const id = getRequestId(request);
        // if body contains graphql, output it without its JSON wrapper; otherwise output the full JSON
        const body = request.body.query
            ? Object.keys(request.body).map(key => `\n${key}: ${request.body[key]}`)
            : JSON.stringify(request.body, getBodyJsonSanitizer());
        return `HTTP ${this.service} request  ${id} body: ${body}`;
    }

    logRequestHeaders(request: Request): string {
        const id = getRequestId(request);
        return `HTTP ${this.service} request  ${id} headers: ${JSON.stringify(request.headers)}`;
    }

    logResponseHeaders(response: Response): string {
        const id = getRequestId(response.req);
        return `HTTP ${this.service} response ${id} headers: ${JSON.stringify(response.getHeaders())}`;
    }

    logResponse(response: Response, part: 'chunk' | 'end', str: Buffer | string | undefined): string {
        const id = getRequestId(response.req);
        if (typeof str === 'string') {
            return `HTTP ${this.service} response ${id} ${part}: ${str}`;
        }
        let slice = '';
        if (str == null) {
            slice = `${str}`;
        } else if (str.length < 50) {
            slice = `0x${str.toString('hex')} [length: ${str.length}]`;
        } else {
            slice = `0x${str.subarray(0, 50).toString('hex')}...[${str.length - 50} more bytes, length: ${str.length}]`;
        }
        return `HTTP ${this.service} response ${id} ${part}: ${slice}`;
    }
}

/**
 * The request logger middleware.
 * This middleware should be loaded first, so that we log all requests,
 * even those which may contain invalid JSON
 */
export function requestLoggerMiddleware(logger: Logger): Handler {
    let id = 0;
    return (req: Request, res: Response, next: NextFunction) => {
        id += 1;
        setRequestId(req, id);

        const t0 = performance.now();
        // Keep track of the request ID to not log the same ID multiple times.
        const requestId = id;
        const info = { cfRay: req.headers['cf-ray'], userAgent: req.headers['user-agent'] };
        logger.info(`HTTP request  ${requestId} ${req.method} ${req.originalUrl} ${JSON.stringify(info)}`);
        // Override response.end to get logs and the correct elapse time at the end of the response.
        const oldEnd = res.end;
        res.end = (...args: any[]) => {
            logger.info(
                `HTTP response ${requestId} ${res.statusCode} ${req.method} ${req.originalUrl} ${Math.round((performance.now() - t0) * 100) / 100}ms`,
            );
            return oldEnd.apply(res, args);
        };

        return next();
    };
}

/**
 * The body logger middleware.
 * This middleware must be loaded after the middleware which parses the JSON body
 */
export function bodyLoggerMiddleware(logger: Logger): Handler {
    const httpDebug = new HttpDebug('');
    const httpVerbose = new HttpVerbose('');
    return (request: Request, response: Response, next: NextFunction) => {
        // Do not log body if level is less than verbose
        if (!logger.isActive('verbose')) return next();

        if (logger.isActive('debug')) {
            // in debug mode we log all headers and the complete body
            logger.debug(() => httpDebug.logRequestHeaders(request));
            logger.debug(() => httpDebug.logRequestBody(request));
        } else {
            // in verbose mode we only log a truncated body
            logger.verbose(() => httpVerbose.logRequestBody(request));
        }

        // Override response.write in verbose and debug modes, to get log entries for response chunks
        const oldWrite = response.write;
        response.write = (...args: any[]) => {
            if (logger.isActive('debug')) {
                logger.debug(() => httpDebug.logResponse(response, 'chunk', args[0]));
            } else {
                logger.verbose(() => httpVerbose.logResponse(response, 'chunk', args[0]));
            }
            return oldWrite.apply(response, args);
        };

        // Override response.end to get logs at the end of the response.
        const oldEnd = response.end;
        response.end = (...args: any[]) => {
            if (logger.isActive('debug')) {
                logger.debug(() => httpDebug.logResponse(response, 'end', args[0]));
                logger.debug(() => httpDebug.logResponseHeaders(response));
            } else {
                logger.verbose(() => httpVerbose.logResponse(response, 'end', args[0]));
            }
            return oldEnd.apply(response, args);
        };

        return next();
    };
}
