import { Diagnosis, ErrorWithDiagnoses } from '@sage/xtrem-shared';
import * as _ from 'lodash';
import { Context } from '../runtime/context';
import { loggers } from '../runtime/loggers';
import { NodeFactory } from '../system-exports';
import { Node } from '../ts-api/node';
import { mapErrorToDiagnosis } from './error-utils';
import { ValidationError } from './validation-error';

const logger = loggers.core;

/**
 * GraphQl operation error.
 * The message is localized, and reaches the end user.
 * Contains a list of sanitized diagnoses. Technical details are replaced by a generic message.
 */

export class OperationError extends ErrorWithDiagnoses {
    constructor(
        context: Context,
        readonly factory: NodeFactory,
        readonly operation: string,
        diagnoses: Diagnosis[],
        innerError?: Error,
    ) {
        super(factory.getLocalizedOperationFailedMessage(context, operation), diagnoses, innerError);
        diagnoses.forEach(diag => logger.error(() => JSON.stringify(diag)));
    }

    static fromNodeMutation(node: Node, operation: string, diagnoses: Diagnosis[], innerError?: Error): OperationError {
        return new OperationError(node.$.context, node.$.factory, operation, diagnoses, innerError);
    }

    static errorMapper(context: Context, factory: NodeFactory, operation: string): (err: Error) => OperationError {
        return err => {
            if (!(err instanceof ValidationError)) {
                context.diagnoses.push(_.omit(mapErrorToDiagnosis(context, err), 'factoryName'));
            }
            return new OperationError(context, factory, operation, context.diagnoses, err);
        };
    }
}
