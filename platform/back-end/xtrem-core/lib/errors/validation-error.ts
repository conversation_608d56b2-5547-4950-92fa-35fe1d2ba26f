import { LocalizedError } from '@sage/xtrem-shared';
import { Context } from '../runtime/context';
import { NodeFactory } from '../runtime/node-factory';

/**
 * Validation error.
 * Special error class to be thrown when a validation fails.
 * The GraphQL layer will rethrow it as an OperationError, adding the diagnoses.
 */
export class ValidationError extends LocalizedError {
    constructor(context: Context, factory: NodeFactory, operation: string) {
        super(`${factory.getLocalizedOperationFailedMessage(context, operation)}`);
    }
}
