import { DatabaseError } from '@sage/xtrem-postgres';
import { BaseError, Diagnosis, SystemError, ValidationSeverity } from '@sage/xtrem-shared';
import { wrapError } from '../concurrency-utils';
import { Context } from '../runtime';

function mapDatabaseErrorMessage(
    context: Context,
    err: DatabaseError,
): { message: string; factoryName: string; path: string[] } {
    if (!(err.code?.startsWith('23') && err.table && err.constraint))
        return { message: wrapError(context, err).message, factoryName: '', path: [] };

    const table = context.application.getTableByName(err.table);
    const foreignKey = table.getForeignKeyByName(err.constraint);
    const factoryName = table.factory.name;

    if (err.code === '23503' && foreignKey && foreignKey.columnNames.length) {
        const property = table.factory.properties.find(
            p => p.columnName === foreignKey.columnNames[foreignKey.columnNames.length - 1],
        )?.name;

        return {
            message: context.localize(
                '@sage/xtrem-core/could-not-delete-blocked-by-property',
                'Could not delete: blocked by property {{factory}}.{{property}}',
                { factory: factoryName, property },
            ),
            factoryName,
            path: [],
        };
    }
    if (err.code === '23505') {
        // Parse the error massage to extract the key and values
        // eslint-disable-next-line @sage/redos/no-vulnerable
        const [keys, values] = (/\((.*)\)=\((.*)\)/.exec(err.detail || '') || []).slice(1).map(v => v.split(', '));
        if (!keys) return { message: wrapError(context, err).message, factoryName, path: [] };

        if (keys[0] === '_tenant_id') {
            keys.shift();
            values.shift();
        }
        const propertyNames = keys.map(
            columnName => table.findColumnByColumnName(columnName)?.property?.name ?? '<unknown>',
        );

        if (propertyNames.length === 1) {
            return {
                message: context.localize(
                    '@sage/xtrem-core/unique-index-violation',
                    'The operation failed because a record already exists.', // (conflict on {{propertyNames}})',
                ),
                factoryName,
                path: [propertyNames[0]],
            };
        }

        return {
            message: context.localize(
                '@sage/xtrem-core/unique-index-violation-composite-key',
                'The operation failed because a record already exists. Conflict found on {{propertyNames}}.',
                { propertyNames: propertyNames.join(', ') },
            ),
            factoryName,
            path: [],
        };
    }
    return { message: wrapError(context, err).message, factoryName, path: [] };
}

export interface DiagnosisWithFactoryName extends Diagnosis {
    factoryName: string;
}

export function mapErrorToDiagnosis(context: Context, err: Error): DiagnosisWithFactoryName {
    if (err instanceof SystemError && err.innerError instanceof DatabaseError) {
        return { ...mapDatabaseErrorMessage(context, err.innerError), severity: ValidationSeverity.exception };
    }
    return {
        severity: ValidationSeverity.exception,
        message: wrapError(context, err).message,
        factoryName: '',
        path: err instanceof BaseError ? (err.path ?? []) : [],
    };
}
