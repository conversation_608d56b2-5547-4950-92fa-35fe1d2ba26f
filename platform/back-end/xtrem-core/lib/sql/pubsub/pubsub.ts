import { AnyRecord, asyncArray, AsyncResponse } from '@sage/xtrem-async-helper';
import { ConfigManager } from '@sage/xtrem-config';
import type { Subscriber } from '@sage/xtrem-postgres';
import { snakeCase } from 'lodash';
import { ContainerManager } from '../../runtime/container-manager';
import { Context } from '../../runtime/context';
import { loggers } from '../../runtime/loggers';
import { NotificationSubscriptionManager } from './subscriber';

const logger = loggers.pubsub;
export interface PublishOptions {
    excludeSelf?: boolean;
}

export type PubSubPayload = AnyRecord & { tenantId: string | null };

export interface PubSubEnvelope {
    data: string;
    containerId: string;
    excludeSelf: boolean;
}

/**
 * Static class to support pubsub of topics with postgres NOTIFY / LISTEN.
 * This mechanism is used by the framework to synchronize operations across containers.
 * This API is reserved to system components. Applicative modules should not use it.
 */
export abstract class PubSub {
    /** @internal */
    static get subscriber(): Promise<Subscriber | undefined> {
        return (async () => {
            const storage = ConfigManager.current.storage!;
            if (storage.managedExternal) return undefined;
            const subscriber = await NotificationSubscriptionManager.getSubscriber(storage);
            return subscriber;
        })();
    }

    /**
     * Gets the channel name based on the topic
     */
    static getChannelName(topic: string): string {
        if (!/^[\w _-]+$/.test(topic))
            throw new Error(
                `Invalid character(s) in topic, only alphabets, numbers, _, - and spaces are allowed, ${topic}`,
            );
        return snakeCase(topic);
    }

    /**
     * Publish notification for topic, if this is a broadcast we need to use a prefix on channel name
     */
    static async publish(
        context: Context,
        topic: string,
        data: PubSubPayload,
        options?: PublishOptions,
    ): Promise<void> {
        if (!context.isWritable) throw new Error(`Cannot publish using a context that is not writable: ${topic}`);

        const containerId = ContainerManager.containerId;

        const envelope: PubSubEnvelope = {
            data: JSON.stringify(data),
            containerId,
            excludeSelf: !!options?.excludeSelf,
        };
        const channel = PubSub.getChannelName(topic);
        logger.verbose(
            () => `Sending notification: channel ${channel}, container ${containerId}, ${JSON.stringify(envelope)}`,
        );
        // NOTIFY does not accept $1 so we must escape the quotes inside the JSON
        const escaped = JSON.stringify(envelope).replace(/'/g, "''");
        await context.executeSql(`NOTIFY ${channel}, '${escaped}';`, []);
    }

    /**
     * Subscribe to a topic
     */
    static async subscribe(
        topic: string,
        listener: (data: PubSubPayload, containerId?: string) => AsyncResponse<void>,
    ): Promise<void> {
        const channel = PubSub.getChannelName(topic);
        const subscriber = await this.subscriber;
        const containerId = ContainerManager.containerId;
        if (!subscriber) return;

        // register notification channel with notification event emitter
        // payload is type any as it has already been passed to JSON.parse
        subscriber.notifications.on(channel, (payload: PubSubEnvelope): void => {
            // if flag is set, exclude the current process from receiving the notification
            if (payload.excludeSelf && payload.containerId === containerId) {
                return;
            }
            (async () => {
                logger.verbose(
                    () =>
                        `Processing notification: channel ${channel}, container ${containerId}, ${JSON.stringify(
                            payload,
                        )}`,
                );

                // execute listener passing payload
                await listener(JSON.parse(payload.data), containerId);

                logger.verbose(
                    () =>
                        `Completed processing notification: channel ${channel}, container ${containerId}, ${JSON.stringify(
                            payload,
                        )}`,
                );
            })().catch(error => {
                logger.error(
                    () =>
                        `Error while processing notification: channel ${channel}, container ${containerId}, ${error.stack}`,
                );
            });
        });

        const promise = subscriber.listenTo(channel);
        if (!promise) {
            logger.warn(`Already subscribed to channel ${channel}, container ${containerId}`);
        } else {
            await promise;
            logger.info(`Subscribed to channel ${channel}, containerId ${containerId}`);
        }
    }

    /**
     * Returns whether topic has been subscribed or not
     */
    static async isSubscribed(topic: string): Promise<boolean> {
        const subscriber = await this.subscriber;
        return !!subscriber && subscriber.getSubscribedChannels().includes(PubSub.getChannelName(topic));
    }

    /**
     * Unsubscribe from notifications on channel
     */
    private static async unsubscribeChannel(channel: string): Promise<void> {
        const subscriber = await this.subscriber;
        if (!subscriber) return;
        const promise = subscriber.unlisten(channel);
        const containerId = ContainerManager.containerId;
        if (!promise) {
            logger.warn(`WARNING: was not subscribed to channel ${channel}, container ${containerId}`);
        } else {
            await promise;
            logger.info(`Unsubscribed from channel ${channel}, container ${containerId}`);
        }

        // unlisten does not remove the listeners for the channel, so if we don't remove the listener, we execute the notification more than once
        // if the process re-subscribes to channel
        subscriber.notifications.removeAllListeners(channel);
    }

    /**
     * Unsubscribe from notifications on topic
     */
    static async unsubscribe(topic: string): Promise<void> {
        const subscriber = await this.subscriber;
        if (!subscriber) return;

        const channel = PubSub.getChannelName(topic);
        await PubSub.unsubscribeChannel(channel);
    }

    /**
     * Unsubscribe from all channels
     */
    static async unsubscribeAll(): Promise<void> {
        const subscriber = await this.subscriber;
        if (!subscriber) return;

        const channels = subscriber.getSubscribedChannels();
        await asyncArray(channels).forEach(channel => PubSub.unsubscribeChannel(channel));
    }
}
