/**
 * This class manages heartbeats between containers.
 *
 * Every container periodically publishes a heartbeat and listen to the heartbeats from the other containers.
 */

import { ConfigManager } from '@sage/xtrem-config';
import { Datetime } from '@sage/xtrem-date-time';
import { Logger } from '@sage/xtrem-log';
import { Dict } from '@sage/xtrem-shared';
import { Application } from '../../application';
import { ContainerManager } from '../../runtime/container-manager';
import { PubSub, PubSubPayload } from './pubsub';

type ContainerInfo = {
    timestamp: Datetime;
};

type ContainerInfos = Dict<ContainerInfo>;

interface HeartbeatPayload extends PubSubPayload {
    containerId: string;
}

const logger = Logger.getLogger(__filename, 'heartbeat');

/**
 * @internal
 */
export abstract class ContainerHeartbeatMonitor {
    private static _isActive = false;

    private static _isPublishing = false;

    static readonly _topic = 'containers-heartbeats';

    // The list of alive containers
    private static _containers: ContainerInfos = {};

    /**
     * The heartbeat period (in millis)
     */
    static get heartbeatMillis(): number {
        return (ConfigManager.current.interop?.heartbeatSeconds ?? 1) * 1000;
    }

    /**
     * Returns the id of all the alive containers (current container is excluded)
     */
    static get aliveContainers(): string[] {
        return Object.keys(ContainerHeartbeatMonitor._containers);
    }

    /**
     * Invoked when a heartbeat is received (published by another container)
     * @param payload
     */
    private static _containerHeartbeatListener(payload: HeartbeatPayload): void {
        const containers = ContainerHeartbeatMonitor._containers;
        logger.debug(() => `Received heartbeat from container ${payload.containerId}`);
        if (payload.containerId !== ContainerManager.containerId && containers[payload.containerId] == null) {
            logger.verbose(() => `New container ${payload.containerId} has been started`);
        }
        containers[payload.containerId] = { timestamp: Datetime.now(true) };
    }

    /**
     * Indicates whether the heartbeat monitor has been activated for the current container
     */
    static get isActive(): boolean {
        return ContainerHeartbeatMonitor._isActive;
    }

    /**
     * Publish the heartbeat for the current container
     */
    private static async _publishHeartbeat(application: Application): Promise<void> {
        await application.asRoot.withCommittedContext(
            null,
            async context => {
                logger.debug(() => `Container ${ContainerManager.containerId}: publish heartbeat`);
                await PubSub.publish(
                    context,
                    ContainerHeartbeatMonitor._topic,
                    {
                        tenantId: null,
                        containerId: ContainerManager.containerId,
                    },
                    { excludeSelf: true },
                );
            },
            {
                description: () => 'Publish heartbeat',
            },
        );
        const containers = ContainerHeartbeatMonitor._containers;
        // Remove the obsolete containers
        const now = Datetime.now(true).value;
        Object.keys(containers).forEach(containerId => {
            if (now - containers[containerId].timestamp.value > 3 * ContainerHeartbeatMonitor.heartbeatMillis) {
                // Consider this container as dead
                delete containers[containerId];
                logger.info(`Container ${containerId} is dead`);
            }
        });
    }

    /**
     * Activate the periodical publish of heartbeats for the current container.
     *
     * Will allow the other containers to monitor the 'alive' status of the current container
     */
    static async activateHeartbeatPublications(application: Application): Promise<void> {
        if (ContainerHeartbeatMonitor.heartbeatMillis === 0) return;
        if (ConfigManager.current.storage?.managedExternal) {
            throw new Error('Hearbeat monitor not supported in this context');
        }

        if (ContainerHeartbeatMonitor._isPublishing) return;
        ContainerHeartbeatMonitor._isPublishing = true;

        logger.info(`Publication of heartbeats started on container ${ContainerManager.containerId}`);

        await ContainerHeartbeatMonitor._publishHeartbeat(application);
        setInterval(() => {
            (() => ContainerHeartbeatMonitor._publishHeartbeat(application))().catch(err => logger.error(err));
        }, ContainerHeartbeatMonitor.heartbeatMillis);
    }

    /**
     * Activate the heartbeat monitor for the current container.
     *
     * Will allow the container to get the list of alive containers (at least the containers that publish their
     * heartbeat, i.e. the one that invoked the activateHeartbeatPublications function)
     */
    static async activate(): Promise<void> {
        if (ConfigManager.current.storage?.managedExternal) {
            throw new Error('Hearbeat monitor not supported in this context');
        }

        if (ContainerHeartbeatMonitor._isActive) return;
        ContainerHeartbeatMonitor._isActive = true;

        logger.info(`Monitor activated on container ${ContainerManager.containerId}`);

        await PubSub.subscribe(ContainerHeartbeatMonitor._topic, ContainerHeartbeatMonitor._containerHeartbeatListener);
    }
}
