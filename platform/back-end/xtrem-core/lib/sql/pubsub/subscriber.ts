import { Logger } from '@sage/xtrem-log';
import { createSubscriber, Subscriber } from '@sage/xtrem-postgres';
import { SqlConfig, StorageConfig } from '@sage/xtrem-shared';
import { ContainerManager } from '../../runtime/container-manager';

const logger = new Logger(__filename, 'pubsub');

export class NotificationSubscriptionManager {
    private static subscriber: Subscriber | undefined;

    /**
     * create a subscriber instance
     * @param sqlConfig
     */
    private static async createPostgresSubscriber(sqlConfig: SqlConfig): Promise<Subscriber> {
        const subscriber = createSubscriber(
            {
                host: sqlConfig.hostname,
                user: sqlConfig.user,
                password: sqlConfig.password,
                database: sqlConfig.database,
                port: sqlConfig.port,
            },
            { ...sqlConfig?.subscriber },
        );
        await subscriber.connect();

        const containerId = ContainerManager.containerId;

        logger.info(`Subscriber session connected ${containerId}`);

        subscriber.events.on('error', error => {
            // if the subscriber emitter throws an error, we should print it into the logs and not rethrow
            logger.error(() => `Subscriber error: ${error.stack}`);
            process.exit(1);
        });

        process.on('exit', () => {
            logger.info(() => `Closing subscriber session ${containerId}`);
            // when the process exits we have to close the subscribers database session
            this.closeSubscriber();
        });

        return subscriber;
    }

    /**
     * gets the subscriber instance if it is cached for the process, or creates a new one
     * @param storageConfig
     */
    static async getSubscriber(storageConfig: StorageConfig): Promise<Subscriber> {
        if (!this.subscriber) {
            const sqlConfig = { ...storageConfig.sql! };
            if (!sqlConfig) throw new Error("cannot get SQL pool: missing 'sql' configuration");
            this.subscriber = await this.createPostgresSubscriber(sqlConfig);
        }
        return this.subscriber;
    }

    /**
     * Closed the subscriber instance
     * @param subscriber
     */
    private static closeSubscriber(): void {
        // this will be called when the process exits so we need a fibre to wait_ for the close to complete
        (async () => {
            if (!this.subscriber) return;
            await this.subscriber.close();
            logger.info(`Subscriber session closed ${ContainerManager.containerId}`);
            this.subscriber = undefined;
        })().catch(err => logger.error(err));
    }
}
