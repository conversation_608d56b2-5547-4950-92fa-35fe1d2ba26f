import { IndexColumn } from '@sage/xtrem-postgres';

export type DataDiagnosisSeverity = 'info' | 'warn' | 'error';
export type DataDiagnosisCategory = 'autofix';

export interface DataDiagnosis {
    severity: DataDiagnosisSeverity;
    category: DataDiagnosisCategory;
    message: string;
}

/**
 * @disabled_internal
 *
 * Note: indexDefinition is sth like
 *  CREATE INDEX delete_columns_node__tenant_id_idx ON xtrem_core_test.delete_columns_node USING btree (_tenant_id, name_1 DESC, name_2, name_3 DESC)
 *  or
 *  CREATE UNIQUE INDEX table_1_ind0 ON xtrem.table_1 USING btree (name, xtrem.col_1_enum_coalesce(col_1))
 *  or
 *  CREATE UNIQUE INDEX table_1_ind1 ON xtrem.table_1 USING btree (name, coalesce(col_ref_1, (0)::bigint)))
 */
export function parseIndexDefinition(indexName: string, indexDefinition: string): IndexColumn[] {
    if (!indexDefinition) return [];
    const defStart = indexDefinition.indexOf('(');
    const defEnd = indexDefinition.lastIndexOf(')');
    if (defStart < 0 || defEnd < 0) {
        throw new Error(`Could not parse index definition '${indexDefinition}'`);
    }
    const definition = indexDefinition.slice(defStart + 1, defEnd);
    if (!definition) {
        throw new Error(`Could not parse index definition '${indexDefinition}'`);
    }

    const parts: string[] = [];
    const len = definition.length;
    let start = 0;
    const openBrackets: number[] = [];
    let i = 0;
    // eslint-disable-next-line no-plusplus
    for (i = 0; i < len; i++) {
        if (definition[i] === '(') {
            openBrackets.push(i);
        } else if (definition[i] === ')') {
            const eStart = openBrackets.pop();
            if (eStart == null)
                throw Error(`bad index expression for ${indexName} near ${definition.substring(start, i)}`);
        } else if (openBrackets.length === 0) {
            if (definition[i] === ',') {
                parts.push(definition.substring(start, i));
                // eslint-disable-next-line no-plusplus
                while (definition[i + 1] === ' ') i++;
                start = i + 1;
            }
        }
    }
    if (start >= len)
        throw Error(
            `index expression of $${indexName} does not end correctly near ${definition.substring(len - 5, len)}`,
        );
    parts.push(definition.substring(start, i));

    return parts.map(colDef => {
        // colDef is sth like '_tenant_id' or 'name_1 DESC'
        //    or schema_name.enum_1_coalesce(name_1)
        //    or COALESCE(name_1, (0)::bigint)
        //    or COALESCE(name_1, (- ((2)::bigint ^ (62)::bigint))::bigint)
        // Other expressions are might not be supported
        const match =
            /^(?:([\w_.]+_coalesce\(([\w_"]+)\)|COALESCE\(([\w_"]+), ([\w\s()'^:-]+)?\))|([\w_"]+))(?:\s+(DESC|ASC))?$/.exec(
                colDef,
            );
        if (!match) {
            throw new Error(`Could not parse column definition '${colDef}' of index ${indexName} on '${definition}'`);
        }

        return {
            name: (match[2] || match[3] || match[5]).replace(/"/g, ''),
            ascending: match[6] !== 'DESC',
            expression: match[1],
        };
    });
}
