/** @ignore */ /** */
// NOTE: these decorators are not true decorators
// They are NOT exposed in the public API
// They come from an early stage of the project where tables were exposed as decorated classes to
// applicative code.
//
// They need to be refactored as wrappers around node and property decorators, that only add the
// column specific attributes instead of duplicating node and property decorators.
import { ColumnJsonComment } from '@sage/xtrem-postgres';
import { ColumnTypeName, isColumnTypeName } from '@sage/xtrem-shared';
import { TypeName } from '../../decorators';
import { Property } from '../../properties';
import { DecimalDataType, EnumDataType, StringDataType } from '../../types';
import { getSqlCurrvalOfIdSequence } from '../statements/types-conversion';
import { Table } from './table';

/** @disabled_internal */
export class Column {
    constructor(readonly property: Property) {}

    get isSystem(): boolean {
        return this.property.isSystemProperty;
    }

    get table(): Table {
        return this.property.factory.table;
    }

    /** Property name */
    get propertyName(): string {
        return this.property.name;
    }

    /** Column name - may be different from property name */
    get columnName(): string {
        if (!this.property.columnName) throw new Error(`${this.property.name}: missing columnName`);
        return this.property.columnName;
    }

    /** Column type - may be different from property type */
    get type(): TypeName {
        // isLocalized column type is json, but the property type stays string
        if (this.property.isLocalized) return 'json';
        return this.property.columnType || this.property.type;
    }

    /**
     * Returns whether this column should be lazy loaded
     */
    get shouldLazyLoad(): boolean {
        return this.property.shouldLazyLoad;
    }

    /** Is property value nullable ? (must not be used for string properties : use isNotEmpty instead for strings) */
    get isNullable(): boolean {
        return !!this.property.isNullable;
    }

    /** Is property value an encrypted string ? (only applies to string properties) */
    get isEncryptedString(): boolean {
        return this.property.isStringProperty() && this.property.isStoredEncrypted;
    }

    getColumnType(): ColumnTypeName {
        const columnType = this.type;
        if (!columnType) {
            throw this.columnError('column type missing!');
        }

        if (isColumnTypeName(columnType)) {
            return columnType as ColumnTypeName;
        }
        throw this.columnError(`invalid column type: ${columnType}`);
    }

    columnError(message: string): Error {
        return new Error(`column ${this.table.name}.${this.propertyName}: ${message}`);
    }

    /**
     * Returns the JSON comment that must be put on the column (in the database)
     */
    getJsonComment(): ColumnJsonComment {
        const columnType = this.getColumnType();
        const dataType = this.property.dataType;

        const comment: ColumnJsonComment = {
            type: this.property.isReferenceProperty() ? 'reference' : columnType,
            isSystem: this.property.isSystemProperty,
        };
        if (this.isAutoIncrement) {
            comment.isAutoIncrement = true;
        }
        if (this.property.isReferenceProperty()) {
            comment.targetTableName = this.property.targetFactory.tableName;
            comment.isSelfReference = this.property.isSelfReference;
        }

        if (dataType instanceof EnumDataType) {
            const columnDataType = dataType as EnumDataType;
            comment.enumTypeName = columnDataType.getEnumType().name;
        }
        if (columnType === 'json') {
            if (dataType instanceof StringDataType) {
                // Not a "real" JSON but a localized string
                comment.type = 'string';
                comment.isLocalized = true;
                comment.maxLength = this.property.maxLength;
            }
        }
        if (this.property.isDecimalProperty()) {
            comment.precision = (this.property.dataType as DecimalDataType).precision;
            comment.scale = (this.property.dataType as DecimalDataType).scale;
        }
        if (columnType === 'string') {
            if (this.isEncryptedString) comment.isEncrypted = true;
            comment.maxLength = this.property.maxLength;
        }

        return comment;
    }

    /**
     * The SQL default of this column
     */
    get default(): string | undefined | null {
        if (
            this.property.isReferenceProperty() &&
            this.property.isSelfReference &&
            this.property.factory.rootFactory.fullTableName
        )
            return getSqlCurrvalOfIdSequence(this.property.factory.rootFactory.fullTableName);
        return this.property.sqlAttributes?.default;
    }

    /**
     * Is this column set to be auto incremented
     */
    get isAutoIncrement(): boolean {
        return !!this.property.isAutoIncrement;
    }

    /**
     * The SQL default of this column
     */
    get excludeFromInsertIfNull(): boolean {
        return this.isAutoIncrement || !!this.property.sqlAttributes?.excludeFromInsertIfNull;
    }

    /**
     * The column is a system column that is only on the table but not on the factory
     */
    get isInternalSystemProperty(): boolean {
        return this.isSystem && !!this.property.factory.properties.find(prop => prop.name === this.propertyName);
    }
}
