/** @ignore */ /** */
import { ForeignKeyDeleteBehaviour, ForeignKeyJsonComment } from '@sage/xtrem-postgres';
import * as lodash from 'lodash';

/** @disabled_internal */
export class ForeignKey {
    constructor(
        private readonly options: {
            name: string;
            /**
             * The name of the target table
             */
            targetTable: string;
            /**
             * The name of the target columns (columns from target table)
             */
            targetColumnNames: string[];
            /**
             * The column names (columns from the current table)
             */
            columnNames: string[];

            /**
             * The behaviour of the foreign key on 'delete' operation
             */
            onDeleteBehaviour: ForeignKeyDeleteBehaviour;

            /**
             * Is the foreignKey deferrable ?
             */
            isDeferrable: boolean;
        },
    ) {}

    get name(): string {
        return this.options.name;
    }

    /**
     * The name of the target table
     */
    get targetTable(): string {
        return this.options.targetTable;
    }

    /**
     * The name of the target columns (columns from target table)
     */
    get targetColumnNames(): string[] {
        return this.options.targetColumnNames;
    }

    /**
     * The column names (columns from the curren table)
     */
    get columnNames(): string[] {
        return this.options.columnNames;
    }

    /**
     * The behaviour of the foreign key on 'delete' operation
     */
    get onDeleteBehaviour(): ForeignKeyDeleteBehaviour {
        return this.options.onDeleteBehaviour;
    }

    /**
     * Is the foreignKey deferrable ?
     */
    get isDeferrable(): boolean {
        return this.options.isDeferrable;
    }

    /**
     * Returns the JSON comment that must be put on the foreignKey (in the database)
     */
    getJsonComment(): ForeignKeyJsonComment {
        return {
            targetTableName: this.targetTable,
            columns: lodash.zipObject(this.columnNames, this.targetColumnNames),
            onDeleteBehaviour: this.onDeleteBehaviour,
            isDeferrable: this.isDeferrable,
        };
    }
}
