import { asyncArray } from '@sage/xtrem-async-helper';
import { Context } from '../../runtime/context';
import { loggers } from '../../runtime/loggers';
import { NodeFactory } from '../../runtime/node-factory';

function getGcFunctionName(tableName: string): string {
    return `pg_temp.gc_${tableName}`;
}

function getTenantIdCondition(withWhere: boolean, tenantId?: string): string {
    const isTenantDefined = typeof tenantId === 'string' && tenantId !== '';
    if (withWhere) return isTenantDefined ? `WHERE _tenant_id = '${tenantId}'` : '';
    return isTenantDefined ? `AND _tenant_id = '${tenantId}'` : '';
}

function getCreateGcFunctionSql(schemaName: string, tableName: string, tenantId?: string): string {
    return `
    CREATE OR REPLACE FUNCTION ${getGcFunctionName(tableName)}() RETURNS INT8 AS $$
    DECLARE
        r RECORD;
        deleted INT8;
        total INT8;
        done INT8;
    BEGIN
        total = (SELECT COUNT(*) FROM ${schemaName}.${tableName} ${getTenantIdCondition(true, tenantId)});
        deleted = 0;
        done = 0;
        FOR r IN
            SELECT _id FROM ${schemaName}.${tableName} ${getTenantIdCondition(true, tenantId)}
        LOOP
            BEGIN
                DELETE FROM ${schemaName}.${tableName} WHERE _id = r._id ${getTenantIdCondition(false, tenantId)};
                deleted = deleted + 1;
                RAISE NOTICE '${tableName} table gc: deleted %', r._id;
            EXCEPTION WHEN foreign_key_violation THEN NULL;
            done = done + 1;
            IF MOD(done, 1000) = 0 OR done = total THEN
                RAISE INFO '${tableName} table gc: % processed % out of %', CONCAT(ROUND(100 * done / total), '%'), done, total;
            END IF;
            END;
        END LOOP;
        return deleted;
    END $$ LANGUAGE plpgsql;
    `;
}

export async function garbageCollectContentAddressableTable(
    context: Context,
    factory: NodeFactory,
    tenantId?: string,
): Promise<number> {
    const tableName = factory.tableName;
    if (!tableName) throw factory.logicError('no table name');

    // Create a temporary function which performs the GC
    const createFunctionSql = getCreateGcFunctionSql(context.application.schemaName, tableName, tenantId);
    await context.executeSql(createFunctionSql, [], { allowUnsafe: true });

    // Execute the GC.
    const gcFunctionName = getGcFunctionName(tableName);
    return context.executeSql(`SELECT * FROM ${gcFunctionName}()`, []);
}

export async function garbageCollectContentAddressableTables(context: Context, tenantId?: string): Promise<void> {
    await asyncArray(
        context.application.getAllSortedFactories().filter(factory => factory.isContentAddressable),
    ).forEach(async factory => {
        const deleted = await context.runInWritableContext(childContext =>
            garbageCollectContentAddressableTable(childContext, factory, tenantId),
        );
        loggers.application.info(`${factory.tableName}: ${JSON.stringify(deleted)} records purged by GC`);
    });
}
