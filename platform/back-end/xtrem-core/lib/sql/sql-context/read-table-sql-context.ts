import { asyncArray } from '@sage/xtrem-async-helper';
import {
    ColumnDefinition,
    ForeignKeyDefinition,
    parseForeignKeyDefinition,
    SqlReadTableSchemaOptions,
    TableDefinition,
    TriggerDefinition,
    TriggerEventType,
    TriggerTimingType,
} from '@sage/xtrem-postgres';
import { Dict } from '@sage/xtrem-shared';
import { performance } from 'perf_hooks';
import { Context } from '../..';
import { loggers } from '../../runtime/loggers';
import { parseIndexDefinition } from '../schema/utils';
import { getSqlCurrvalOfIdSequence, postgreSqlToXtrem } from '../statements/types-conversion';
import { SchemaSqlContext } from './schema-sql-context';
import { PostgreSqlTypes } from './types';

/**
 * Returns whether a column name matches a system column
 * @param colName
 */
export function isSystemColumn(colName: string): boolean {
    return [
        '_tenant_id',
        '_custom_data',
        '_create_stamp',
        '_create_user',
        '_id',
        '_source_id',
        '_update_stamp',
        '_update_tick',
        '_update_user',
        '_layer',
        '_vendor',
    ].includes(colName);
}

/**
 * Returns the table definition of a table (from its name)
 */
export function readTableSchema(
    context: Context,
    tableName: string,
    options: SqlReadTableSchemaOptions,
): Promise<TableDefinition> {
    return new ReadTableSqlContext(context.application).readSchema(tableName, options);
}

/**
 * Returns the table definitions of all the provided tables (from thier name) or all the table if no name is provided
 */
export function readTableSchemas(
    context: Context,
    tableNames: string[],
    options: SqlReadTableSchemaOptions,
): Promise<Dict<TableDefinition>> {
    return new ReadTableSqlContext(context.application).readTableDefinitions(tableNames, options);
}

/**
 * Returns the tables in the database for the application schema
 */
export function readTablesForSchema(context: Context): Promise<string[]> {
    return new ReadTableSqlContext(context.application).readTablesForSchema();
}

interface SqlArgs {
    schemaName: string;
    tableNames?: string[];
}

export class ReadTableSqlContext extends SchemaSqlContext {
    async readSchema(
        tableName: string,
        options: SqlReadTableSchemaOptions = {
            skipSequences: true,
            skipColumns: true,
            skipForeignKeys: true,
            skipSecurity: true,
        },
    ): Promise<TableDefinition> {
        return (await this.readTableDefinitions([tableName], options))[tableName];
    }

    private _getOrInitTableDef(tableDefs: Dict<TableDefinition>, tableName: string): TableDefinition {
        let tableDef = tableDefs[tableName];
        if (tableDef == null) {
            tableDef = {
                schemaName: this.schemaName,
                tableName,
                columns: [],
                indexes: [],
                foreignKeys: [],
                triggers: [],
            };
            tableDefs[tableName] = tableDef;
        }
        return tableDef;
    }

    /**
     * Read all the tables of the schema
     * @returns list of tables
     */
    async readTablesForSchema(): Promise<string[]> {
        const tables = [] as string[];
        const sqlQuery = `SELECT t.table_name
                FROM information_schema.tables t
                WHERE t.table_schema = $1`;
        const sqlArgs: SqlArgs = { schemaName: this.schemaName };
        await this.withConnection(async cnx => {
            await this.createReader<{ table_name: string }>(
                cnx,
                sqlQuery,
                ReadTableSqlContext._transformSqlArgs(sqlArgs),
                {
                    logLevel: 'debug',
                },
            ).forEach(row => {
                tables.push(row.table_name);
            });
        });

        return tables;
    }

    /**
     *
     * @param tableNames if [], will return the table definitions for all the tables, in a row
     * @param options
     * @returns
     */
    async readTableDefinitions(
        tableNames: string[],
        options: SqlReadTableSchemaOptions = {},
    ): Promise<Dict<TableDefinition>> {
        const tableDefs: Dict<TableDefinition> = {};

        // The following loop will make sure that, if tableNames is not [], there will be a tableDef
        // for every table (even if, for instance, the options are set to only query the foreignKeys and the
        // table has no foreign key)
        tableNames.forEach(tableName => this._getOrInitTableDef(tableDefs, tableName));

        const sqlArgs: SqlArgs = { schemaName: this.schemaName };
        if (tableNames.length) sqlArgs.tableNames = tableNames;

        if (!options.skipColumns) {
            const dt0 = performance.now();
            loggers.upgrade.debug(() => `Loading columns for tables ${tableNames}`);
            await this.fillColumnInfos(tableDefs, sqlArgs, !!options.getComments);
            loggers.upgrade.debug(() => `Loaded columns for tables ${tableNames} in ${performance.now() - dt0} ms`);
        }
        if (!options.skipIndexes) {
            const dt0 = performance.now();
            loggers.upgrade.debug(() => `Loading indexes for tables ${tableNames}`);
            await this.fillIndexInfos(tableDefs, sqlArgs);
            loggers.upgrade.debug(() => `Loaded indexes for tables ${tableNames} in ${performance.now() - dt0} ms`);
        }
        if (!options.skipForeignKeys) {
            const dt0 = performance.now();
            loggers.upgrade.debug(() => `Loading FKs for tables ${tableNames}`);
            await this._fillForeignKeyInfo(tableDefs, sqlArgs, !!options.getComments);
            loggers.upgrade.debug(() => `Loaded FKs for tables ${tableNames} in ${performance.now() - dt0} ms`);
        }

        const includes = new Set(options.includes);
        if (includes.has('triggers')) {
            const dt0 = performance.now();
            loggers.upgrade.debug(() => `Loading triggers for tables ${tableNames}`);
            await this.fillTableTriggers(tableDefs, sqlArgs);
            loggers.upgrade.debug(() => `Loaded triggers for tables ${tableNames} in ${performance.now() - dt0} ms`);
        }

        if (options.getComments) {
            await this._fillComments(Object.values(tableDefs));
        }
        return tableDefs;
    }

    /**
     * Completes a set of tableDef with their comments (table, columns, ...)
     * @param tableDef
     */
    private async _fillComments(tableDefs: TableDefinition[]): Promise<void> {
        // Approx duration : 120ms for 364 tables
        await this.withConnection(cnx =>
            asyncArray(Object.values(tableDefs)).forEach(async tableDef => {
                const sqlQuery = `SELECT obj_description('${tableDef.schemaName}.${tableDef.tableName}'::regclass);`;
                const comment = (await this.execute<{ obj_description: string }[]>(cnx, sqlQuery))[0];
                if (comment && comment.obj_description) tableDef.comment = JSON.parse(comment.obj_description);
            }),
        );
    }

    // eslint-disable-next-line class-methods-use-this
    private _fillUpColumnDefinitionForEnums(rrow: any, column: ColumnDefinition): void {
        column.enumDataType = {
            name: rrow.udt_name,
            values: {},
        };
        switch (rrow.data_type) {
            case 'USER-DEFINED':
                column.type = 'enum';
                break;
            case 'ARRAY':
                column.type = 'enumArray';
                break;
            default:
                throw new Error(
                    `Unmanaged enum related type. column=${rrow.column_name}, data_type=${rrow.data_type}, enumType=${rrow.udt_name}`,
                );
        }
    }

    /**
     * Parses a SQL row to a columnDefinition (refer to fillColumnInfos to get the format of the SQL row)
     * @param rrow
     * @returns
     */
    private _sqlRowToColumnDefinition(rrow: any): ColumnDefinition {
        const column: ColumnDefinition = {
            name: rrow.column_name,
            isNullable: rrow.is_nullable !== 'NO',
        };

        // TODO: we need to pass the full root table name into getSqlCurrvalOfIdSequence as the
        // sequence created from the _id column of the root table. We need to use the comment on table to determine the root table
        if (rrow.column_default === getSqlCurrvalOfIdSequence(`${this.schemaName}.${rrow.table_name}`)) {
            column.isSelfReference = true;
        }

        if (rrow.col_comment != null) {
            column.comment = JSON.parse(rrow.col_comment);
        }

        if (rrow.column_default) {
            if (/^nextval\(/.test(rrow.column_default)) {
                // Auto-increment column: something like nextval('xtrem.sys_communication_history__id_seq'::regclass)
                // Ignore this default value, we only want to keep the default values that were set by the framework
                // and skip the default values automatically added by postgres
                column.isAutoIncrement = true;
                column.default = null;
            } else if (rrow.column_default === "''::character varying") {
                // for string, textStream and binaryStream the default is '' which is represented on the column definition as ''::character varying
                column.default = '';
            } else {
                column.default = rrow.column_default;
            }
        } else {
            column.default = null;
        }

        switch (rrow.udt_name) {
            case 'int4':
                column.type = 'short';
                break;
            case '_int8':
                column.type = rrow.data_type === 'ARRAY' ? 'integerArray' : 'integer';
                break;
            case 'varchar':
            case 'string':
                column.type = 'string';
                column.maxLength = parseInt(rrow.character_maximum_length, 10);
                break;
            case 'numeric':
                column.type = 'decimal';
                column.precision = parseInt(rrow.numeric_precision, 10);
                column.scale = parseInt(rrow.numeric_scale, 10);
                break;
            // See later if we need to support the number of fractional digits in the seconds
            // case 'time':
            //     column.precision = parseInt(rrow.datetime_precision, 10);
            //     break;
            default: {
                if (rrow.udt_name?.endsWith('_enum')) {
                    this._fillUpColumnDefinitionForEnums(rrow, column);
                    return column;
                }
                if (rrow.udt_name?.toUpperCase().startsWith('TIMESTAMPTZ')) {
                    rrow.udt_name = `${rrow.udt_name}(${rrow.datetime_precision})`;
                }
                column.type = postgreSqlToXtrem[rrow.udt_name.toUpperCase() as PostgreSqlTypes]?.name;
                if (column.type == null) {
                    throw new Error(
                        `Unmanaged column type. column=${rrow.column_name}, data_type=${rrow.data_type}, udt_name=${rrow.udt_name}`,
                    );
                }
            }
        }
        return column;
    }

    private async fillColumnInfos(
        tableDefs: Dict<TableDefinition>,
        sqlArgs: SqlArgs,
        readComments: boolean,
    ): Promise<void> {
        const sqlQuery = `SELECT c.table_name, c.udt_name, c.column_name, c.data_type, c.character_maximum_length, c.numeric_precision,
                c.numeric_scale, c.datetime_precision, c.is_nullable, c.column_default ${
                    readComments
                        ? ", pg_catalog.col_description(format('%s.%s',c.table_schema,c.table_name)::regclass::oid,c.ordinal_position) col_comment"
                        : ''
                }
                FROM information_schema.columns c
                WHERE c.table_schema = $1 ${sqlArgs.tableNames ? ' AND c.table_name = ANY($2::TEXT[])' : ''}
                ORDER BY c.table_name, c.ordinal_position`;

        await this.withConnection(async cnx => {
            const buildColumn = (row: any): ColumnDefinition => {
                const column: ColumnDefinition = this._sqlRowToColumnDefinition(row);
                column.isSystem = isSystemColumn(column.name);
                return column;
            };

            await this.createReader<{ table_name: string }>(
                cnx,
                sqlQuery,
                ReadTableSqlContext._transformSqlArgs(sqlArgs),
                {
                    logLevel: 'debug',
                },
            ).forEach(row => {
                const tableName = row.table_name;
                const tableDef = this._getOrInitTableDef(tableDefs, tableName);
                tableDef.columns!.push(buildColumn(row));
            });
        });
    }

    private static _transformSqlArgs(sqlArgs: SqlArgs): any[] {
        const out: any[] = [sqlArgs.schemaName];
        if (sqlArgs.tableNames) out.push(sqlArgs.tableNames);
        return out;
    }

    private async fillIndexInfos(tableDefs: Dict<TableDefinition>, sqlArgs: SqlArgs): Promise<void> {
        // Note: WHERE xxx IN (a, b, c) does not work with parameterized queries
        // Instead, we use WHERE xxx = ([a, b, c]::TEXT[])
        // see https://stackoverflow.com/questions/10720420/node-postgres-how-to-execute-where-col-in-dynamic-value-list-query
        const sqlQuery = `SELECT c.tablename table_name, c.indexname index_name, c.indexdef index_def FROM pg_indexes c
                          WHERE c.schemaname = $1 ${sqlArgs.tableNames ? ' AND c.tablename = ANY($2::TEXT[])' : ''}
                          ORDER BY c.tablename, c.indexname;`;

        await this.withConnection(cnx =>
            this.createReader<{ table_name: string; index_name: string; index_def: string }>(
                cnx,
                sqlQuery,
                ReadTableSqlContext._transformSqlArgs(sqlArgs),
                {
                    logLevel: 'debug',
                },
            ).forEach(row => {
                const tableName = row.table_name;
                const tableDef = this._getOrInitTableDef(tableDefs, tableName);
                const indexName = row.index_name;
                const idxDef = row.index_def;
                const columns = parseIndexDefinition(indexName, idxDef);

                if (indexName.toUpperCase().endsWith('_PK')) {
                    // Primary key
                    tableDef.primaryKey = { columns: columns.map(col => col.name) };
                } else {
                    // index
                    tableDef.indexes!.push({
                        name: row.index_name,
                        isUnique: idxDef.includes('CREATE UNIQUE INDEX'),
                        columns,
                    });
                }
            }),
        );
    }

    private async _fillForeignKeyInfo(
        tableDefs: Dict<TableDefinition>,
        sqlArgs: SqlArgs,
        readComments: boolean,
    ): Promise<void> {
        const sqlQuery = `
        SELECT
            pgc.relname AS table_name,
            pgcf.relname AS fk_table_name,
            r.conname AS constraint_name,
            pg_catalog.pg_get_constraintdef(r.oid, true) AS fk_def
            ${readComments ? ', obj_description(r.oid) fk_comment' : ''}
        FROM pg_catalog.pg_constraint r
        JOIN pg_catalog.pg_class pgcf ON pgcf.oid = r.confrelid
        JOIN pg_catalog.pg_class pgc ON pgc.oid = r.conrelid
        WHERE r.contype='f' AND r.connamespace = $1::regnamespace ${
            sqlArgs.tableNames ? 'AND pgc.relname = ANY($2::TEXT[])' : ''
        }
        ORDER BY table_name, constraint_name;`;

        await this.withConnection(cnx =>
            this.createReader<{
                table_name: string;
                fk_def: string;
                constraint_name: string;
                fk_table_name: string;
                fk_comment?: string;
            }>(cnx, sqlQuery, ReadTableSqlContext._transformSqlArgs(sqlArgs), {
                logLevel: 'debug',
            }).forEach(rrow => {
                const tableDef = this._getOrInitTableDef(tableDefs, rrow.table_name);
                const result = parseForeignKeyDefinition(rrow.fk_def);
                const fkDef: ForeignKeyDefinition = {
                    name: rrow.constraint_name,
                    targetTable: rrow.fk_table_name,
                    columnNames: result.sourceColumns,
                    targetColumnNames: result.targetColumns,
                    onDeleteBehaviour: result.onDeleteBehaviour,
                    isDeferrable: result.isDeferrable,
                };
                if (rrow.fk_comment) {
                    fkDef.comment = JSON.parse(rrow.fk_comment);
                }
                tableDef.foreignKeys!.push(fkDef);
            }),
        );
    }

    private async fillTableTriggers(tableDefs: Dict<TableDefinition>, sqlArgs: SqlArgs): Promise<void> {
        const sqlQuery = `
                SELECT event_object_table, trigger_name, action_timing, event_manipulation, action_statement
                FROM information_schema.triggers
                WHERE event_object_schema = $1
                ${sqlArgs.tableNames ? 'AND event_object_table = ANY($2::TEXT[])' : ''}
                ORDER BY event_object_table, trigger_name, action_timing, event_manipulation`;

        await this.withConnection(async cnx => {
            let lastTrigger: TriggerDefinition | undefined;
            // When we declare a DELETE OR INSERT trigger, postgres will expose them as 2 different triggers
            // The first one is a DELETE trigger and the second one is an INSERT trigger (se sortOder in the SQL query)
            const transitions: Dict<TriggerEventType> = {
                'DELETE+INSERT': 'DELETE OR INSERT',
                'INSERT+UPDATE': 'INSERT OR UPDATE',
                'DELETE OR INSERT+UPDATE': 'DELETE OR INSERT OR UPDATE',
            };
            await this.createReader<{
                event_object_table: string;
                action_statement: string;
                trigger_name: string;
                action_timing: TriggerTimingType;
                event_manipulation: TriggerEventType;
            }>(cnx, sqlQuery, ReadTableSqlContext._transformSqlArgs(sqlArgs), {
                logLevel: 'debug',
            }).forEach(rrow => {
                const tableName = rrow.event_object_table;
                const tableDef = this._getOrInitTableDef(tableDefs, tableName);
                tableDef.triggers = tableDef.triggers || [];
                const actionStatement = rrow.action_statement;
                const match = actionStatement.match(/^EXECUTE (?:PROCEDURE|FUNCTION) ([0-9a-zA-Z_.]+)\((.*)\)$/);
                const functionName = match ? match[1] : actionStatement;
                const functionParameters = match ? match[2] : '';
                const newTrigger: TriggerDefinition = {
                    name: rrow.trigger_name,
                    when: rrow.action_timing,
                    event: rrow.event_manipulation,
                    functionName,
                    functionParameters,
                };
                if (
                    lastTrigger &&
                    lastTrigger.name === newTrigger.name &&
                    lastTrigger.when === newTrigger.when &&
                    lastTrigger.functionName === newTrigger.functionName
                ) {
                    // Same trigger except for the event
                    // Try to find a transition to merge the events
                    const transitionKey = `${lastTrigger.event}+${newTrigger.event}`;
                    const transition = transitions[transitionKey];
                    if (transition == null)
                        throw new Error(
                            `Unsupported trigger transition: ${transitionKey} on trigger ${tableDef.tableName}.${newTrigger.name}`,
                        );
                    lastTrigger.event = transition;
                    return;
                }
                tableDef.triggers.push(newTrigger);
                lastTrigger = newTrigger;
            });
        });
    }
}
