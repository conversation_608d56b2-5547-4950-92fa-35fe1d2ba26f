import { asyncArray } from '@sage/xtrem-async-helper';
import { Dict } from '@sage/xtrem-shared';
import { parse as parsePgArray } from 'postgres-array';
import { EnumType } from '../../types';
import { SchemaSqlContext } from './schema-sql-context';

export class EnumSqlContext extends SchemaSqlContext {
    /**
     * create new enum datatypes
     */
    async createEnumTypes(types: EnumType[]): Promise<void> {
        const enumTypes = types.map(type => this.createEnumTypeSql(type)).join(' ');
        if (enumTypes) await this.executeSqlStatement({ sql: enumTypes });
    }

    /**
     * create a new enum datatype
     */
    private createEnumTypeSql(type: EnumType): string {
        return `
                DO $$
                    BEGIN
                        IF NOT EXISTS (
                        SELECT 1 FROM pg_type t
                        LEFT JOIN pg_namespace p ON t.typnamespace=p.oid
                        WHERE t.typname='${type.name}' AND p.nspname='${this.schemaName}'
                        ) THEN
                            CREATE TYPE ${this.schemaName}.${type.name} AS ENUM(${Object.keys(type.values)
                                .map(val => `'${val}'`)
                                .join(',')});
                        END IF;
                    END
                $$;
                `;
    }

    /**
     * get list of all enum types in the schema
     */
    async listAllEnumTypes(): Promise<
        {
            enumTypeName: string;
            enumValues: string;
        }[]
    > {
        const sqlQuery = `SELECT t.typname AS udt_name,
                                string_agg(e.enumlabel, ', ' ORDER BY e.enumsortorder) AS enum_values
                            FROM pg_type t 
                            JOIN pg_enum e 
                                ON t.oid = e.enumtypid  
                            JOIN pg_catalog.pg_namespace n 
                                ON n.oid = t.typnamespace
                            WHERE n.nspname = '${this.schemaName}'
                            GROUP BY udt_name
                            ORDER BY udt_name;`;
        const result = await this.executeSqlStatement<{ udt_name: string; enum_values: string }[]>({ sql: sqlQuery });
        return (result || []).map(value => {
            return {
                enumTypeName: value.udt_name,
                enumValues: `{${value.enum_values}}`,
            };
        });
    }

    /**
     * get in which table and column enum type is used.
     * @returns
     */
    async listEnumTypesUsage(enumTypeName?: string): Promise<
        {
            enumTypeName: string;
            tableName: string;
            columnName: string;
        }[]
    > {
        const enumTypeNameClause = enumTypeName ? `AND col.udt_name = '${enumTypeName}'` : '';
        // Note: we join the schema name between column and enum namespace to avoid errors with test fixtures
        // which are shared by xtrem-core and xtrem-graphql
        // (enum type belongs to xtrem_graphql_test but table belongs to xtrem_core_test)
        const sqlQuery = `SELECT 
                                col.table_name,
                                col.column_name,
                                col.udt_name
                            FROM information_schema.columns col
                            INNER JOIN information_schema.tables tab
                                ON tab.table_schema = col.table_schema
                                AND tab.table_name = col.table_name
                                AND tab.table_type = 'BASE TABLE'
                            INNER JOIN pg_type typ
                                ON col.udt_name = typ.typname
                            INNER JOIN pg_namespace ns
                                ON typ.typnamespace = ns.oid 
                                AND ns.nspname = col.table_schema
                            WHERE col.table_schema = '${this.schemaName}'
                                AND typ.typtype in ('e', 'b')  
                                ${enumTypeNameClause}
                            ORDER BY col.table_name,
                                col.ordinal_position;`;
        const result = await this.executeSqlStatement<{ table_name: string; column_name: string; udt_name: string }[]>({
            sql: sqlQuery,
        });
        return (result || []).map(value => {
            return {
                tableName: value.table_name,
                columnName: value.column_name,
                enumTypeName: value.udt_name.startsWith('_') ? value.udt_name.substring(1) : value.udt_name,
            };
        });
    }

    enumTypeExists(typeName: string): Promise<boolean> {
        const sqlQuery =
            'SELECT 1 FROM pg_type t LEFT JOIN pg_namespace p ON t.typnamespace=p.oid WHERE t.typname=$1 AND p.nspname=$2';
        return this.connectionPool.withConnection(async cnx => {
            const records = await this.connectionPool.execute<any[]>(cnx, sqlQuery, [typeName, this.schemaName], {
                logLevel: 'debug',
            });
            return records.length > 0;
        });
    }

    /**
     *
     * @param options
     *  restrict: Refuse to drop the type if any objects depend on it. This is the default.
     *  cascade: Automatically drop objects that depend on the type (such as table columns, functions, and operators), and in turn all objects that depend on those objects (see Section 5.14).
     */
    async dropEnum(typeName: string, options?: { cascade?: boolean; restrict?: boolean }): Promise<void> {
        if (options?.cascade && options.restrict) throw new Error('drop can only be cascade or restrict and not both');
        const sqlQuery = `DROP TYPE IF EXISTS ${this.schemaName}.${typeName} ${options?.cascade ? 'CASCADE' : ''} ${
            options?.restrict ? 'RESTRICT' : ''
        };`;
        await this.executeSqlStatement({ sql: sqlQuery });
    }

    /**
     * rename attribute value of an enum type
     * @param options
     */
    async renameEnumAttributes(typeName: string, options: { oldValue: string; newValue: string }): Promise<void> {
        const sqlQuery = `ALTER TYPE ${this.schemaName}.${typeName} RENAME VALUE '${options.oldValue}' TO '${options.newValue}';`;
        await this.executeSqlStatement({ sql: sqlQuery });
    }

    /**
     * add attribute to an existing enum type
     * @param options
     */
    async addAttributesToEnum(
        typeName: string,
        options: { newValue: string; position?: { after?: string; before?: string } },
    ): Promise<void> {
        const sqlQuery = `ALTER TYPE ${this.schemaName}.${typeName} ADD VALUE IF NOT EXISTS '${options.newValue}' ${
            options?.position?.after ? `AFTER '${options.position.after}'` : ''
        } ${options?.position?.before ? `BEFORE '${options.position.before}'` : ''} ;`;
        await this.executeSqlStatement({ sql: sqlQuery });
    }

    /**
     * rename enum type name
     * @param newTypeName
     */
    async renameEnumType(typeName: string, newTypeName: string): Promise<void> {
        const sqlQuery = `ALTER TYPE ${this.schemaName}.${typeName} RENAME TO ${newTypeName}`;
        await this.executeSqlStatement({ sql: sqlQuery });
    }

    /**
     * Drop enum attribute
     * https://stackoverflow.com/questions/25811017/how-to-delete-an-enum-type-value-in-postgres
     
     * @param options
     */
    async dropEnumAttributes(
        typeName: string,
        attributesToRemove: {
            memberName: string;
            replacement: string | null;
        }[],
    ): Promise<void> {
        const quoteValue = (value: string | null): string => {
            if (value) {
                return `'${value}'`;
            }
            return 'NULL';
        };

        const valuesToRemove = attributesToRemove.map(attributeToRemove => attributeToRemove.memberName);

        const values = parsePgArray(await this.getEnumAttributes(typeName), v => v).filter(
            v => !valuesToRemove.includes(v),
        );

        const enumUsages = await this.listEnumTypesUsage(typeName);

        let sql = '';
        // perform replacement based values passed
        if (attributesToRemove.length > 0) {
            const attributeToReplaceCases = attributesToRemove
                .map(
                    attributeToRemove =>
                        `WHEN ${quoteValue(attributeToRemove.memberName)} THEN ${quoteValue(
                            attributeToRemove.replacement,
                        )}`,
                )
                .join(' ');

            const valuesToReplace = attributesToRemove.map(attributeToRemove =>
                quoteValue(attributeToRemove.memberName),
            );

            await asyncArray(enumUsages).forEach(async usage => {
                sql = `UPDATE ${this.schemaName}.${usage.tableName} SET ${usage.columnName} = CASE ${
                    usage.columnName
                }::TEXT ${attributeToReplaceCases} ELSE  ${usage.columnName} END WHERE ${
                    usage.columnName
                }::TEXT IN (${valuesToReplace.join()});`;

                await this.executeSqlStatement({ sql });
            });
        }

        // values are replaced, we can work on the schema actions now
        const tempTypeName = `${typeName}__old`;

        // rename enum type
        await this.renameEnumType(typeName, tempTypeName);

        // recreate with enum correct values
        sql = this.createEnumTypeSql({
            name: typeName,
            values: values.reduce((r, k, i) => {
                r[k] = i + 1;
                return r;
            }, {} as Dict<number>),
        });

        await this.executeSqlStatement({ sql });

        // Alter current tables with __old type as datatype on columns back to original enum
        await asyncArray(enumUsages).forEach(async usage => {
            sql = `ALTER TABLE ${this.schemaName}.${usage.tableName} ALTER COLUMN ${usage.columnName} TYPE ${this.schemaName}.${typeName} USING ${usage.columnName}::TEXT::${this.schemaName}.${typeName};`;
            await this.executeSqlStatement({ sql });
        });

        //  Check to see if the enum coalesce function exists
        if (await this.doesEnumCoalesceFunctionExist(this.schemaName, typeName)) {
            // Create index coalesce function with recreated enum type
            sql = EnumSqlContext.getSqlToCreateEnumCoalesceFunction(this.schemaName, typeName);
            await this.executeSqlStatement({ sql });

            // drop index coalesce function with param typed to __old enum
            sql = EnumSqlContext.getSqlToDropEnumCoalesceFunction(this.schemaName, typeName, tempTypeName);
            await this.executeSqlStatement({ sql });
        }

        // drop __old enum
        await this.dropEnum(tempTypeName);
    }

    /**
     * get enum attributes values
     */
    async getEnumAttributes(typeName: string): Promise<string> {
        const sqlQuery = `SELECT enum_range(NULL::${this.schemaName}.${typeName})`;
        return (await this.executeSqlStatement<{ enum_range: string }[]>({ sql: sqlQuery }))[0].enum_range;
    }

    static getEnumCoalesceFunctionName(enumName: string): string {
        return `${enumName}_coalesce`;
    }

    private async doesEnumCoalesceFunctionExist(schemaName: string, enumName: string): Promise<boolean> {
        const sql = `SELECT p.oid::regprocedure,n.nspname
                        FROM pg_proc p 
                            JOIN pg_namespace n 
                            ON p.pronamespace = n.oid 
                    WHERE n.nspname ='${schemaName}' AND p.oid::regprocedure::text LIKE '%${EnumSqlContext.getEnumCoalesceFunctionName(
                        enumName,
                    )}%';`;

        const result = await this.executeSqlStatement<{ pg_proc: number }[]>({ sql });

        return (result || []).length > 0;
    }

    static getSqlToCreateEnumCoalesceFunction(schemaName: string, enumName: string, paramEnumName?: string): string {
        return `CREATE OR REPLACE FUNCTION ${schemaName}.${this.getEnumCoalesceFunctionName(enumName)}(${schemaName}.${
            paramEnumName || enumName
        })
            RETURNS text
            LANGUAGE sql
            IMMUTABLE
          AS $function$select coalesce($1::text,'');$function$
       ;`;
    }

    static getSqlToDropEnumCoalesceFunction(schemaName: string, enumName: string, paramEnumName?: string): string {
        return `DROP FUNCTION IF EXISTS ${schemaName}.${this.getEnumCoalesceFunctionName(enumName)}(${schemaName}.${
            paramEnumName || enumName
        });`;
    }
}
