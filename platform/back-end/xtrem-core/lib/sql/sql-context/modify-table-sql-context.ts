import { asyncArray } from '@sage/xtrem-async-helper';
import {
    ColumnDefinition,
    DeferredSql,
    ForeignKeyDefinition,
    ForeignKeyOptions,
    IndexColumn,
    IndexDefinition,
    PoolClient,
    SqlCreateTableOptions,
    SqlObjectJsonComment,
    TableDefinition,
    TriggerDefinition,
} from '@sage/xtrem-postgres';
import { LogicError } from '@sage/xtrem-shared';
import { ColumnInconsistencyDefaultValue } from '.';
import { Application } from '../../application';
import { Context } from '../../runtime/context';
import { makeName63 } from '../statements/naming';
import { TriggerBuilder } from '../statements/triggers';
import { parseColumnDefinition } from '../statements/types-conversion';
import { EnumSqlContext } from './enum-sql-context';
import { SchemaSqlContext } from './schema-sql-context';
import { SqlContext } from './sql-context';
import { ColumnInconsistency, ColumnInconsistencyNullable, SqlAddColumnsOptions } from './types';

export class ModifyTableSqlContext extends SchemaSqlContext {
    /** @disabled_internal */
    constructor(
        application: Application,
        readonly tableDef: TableDefinition,
    ) {
        super(application);
    }

    private async createTableFromTableDefinitionWithConnection(
        cnx: PoolClient,
        options: SqlCreateTableOptions = {},
    ): Promise<void> {
        if (options.onlyIndexes) {
            options.skipDrop = true;
            options.skipCreate = true;
            options.skipSequences = true;
            options.skipIndexes = false;
            options.dropTrigger = false;
            options.skipTriggers = true;
            options.skipForeignKeys = true;
        }

        // Lock before the drop
        // TODO: pass cnx to tableExists and dropTable
        // -------------------------------------
        // Drop the existing table
        // -------------------------------------
        if (!options.skipDrop && (await this.tableExists(this.tableDef.tableName)))
            await this.dropTable(this.tableDef.tableName);

        // -------------------------------------
        // Create the table and its columns
        // -------------------------------------
        if (!options.skipCreate) {
            // Create the table (and the columns)
            const opt = options.temp ? ' TEMP' : '';

            const fullTableName = SqlContext.getFullTableDefName(this.tableDef);

            // TEMP tables are created in their own schema, we cannot create it in a specific schema
            let sql = `CREATE${opt} TABLE ${options.temp ? this.tableDef.tableName : fullTableName} (`;
            sql += this.createTableColumns(options);
            sql += this.createPrimaryKey();
            sql += ');';
            if (!options.skipIndexes) {
                sql += this.createTableIndexes();
            }

            // Process comments (table, columns, ...)
            if (this.tableDef.comment) {
                sql = `${sql}\n${ModifyTableSqlContext.getSqlToComment(
                    'table',
                    {
                        schemaName: this.tableDef.schemaName,
                        tableName: this.tableDef.tableName,
                    },
                    this.tableDef.comment,
                )}`;
            }
            this.tableDef.columns
                ?.filter(col => col.comment)
                .forEach(col => {
                    sql = `${sql}\n${ModifyTableSqlContext.getSqlToComment(
                        'column',
                        {
                            schemaName: this.tableDef.schemaName,
                            tableName: this.tableDef.tableName,
                            columnName: col.name,
                        },
                        col.comment!,
                    )}`;
                });

            if (!options.skipCommands) await this.execute(cnx, sql);
        }
        if (!options.skipForeignKeys) {
            const sql = this.getSqlToCreateForeignKeys();
            await this.execute(cnx, sql);
        }

        if (!options.skipTriggers && !options.skipCommands && this.tableDef.triggers) {
            await this.createTriggers(this.tableDef.triggers, cnx);
        }
    }

    async createTableFromTableDefinition(options: SqlCreateTableOptions = {}): Promise<void> {
        await this.withAdvisoryLock(cnx => this.createTableFromTableDefinitionWithConnection(cnx, options));
    }

    async createTemporaryTable(context: Context): Promise<void> {
        if (!context.isWritable) throw new LogicError('cannot create temporary table: context is not writable');
        await this.createTableFromTableDefinitionWithConnection(context.transaction.connection, {
            skipTriggers: true,
            skipDefault: true,
            temp: true,
        });
    }

    /**
     *  Give a user the 'select/update/insert/delete' rights to a table
     */
    async giveUserRightsToTable(login: string): Promise<void> {
        try {
            await this.executeSqlStatement({
                sql: `GRANT DELETE, INSERT, UPDATE, SELECT on ${SqlContext.escape(this.schemaName)}.${
                    this.tableName
                } to ${SqlContext.escape(login)}`,
            });
            await asyncArray(this.tableDef.columns || [])
                .filter(colDef => !!colDef.isAutoIncrement)
                .forEach(async colDef => {
                    const sql = `GRANT USAGE, SELECT ON SEQUENCE ${SqlContext.escape(
                        this.schemaName,
                    )}.${SqlContext.escape(`${this.tableDef.tableName}_${colDef.name}_seq`)} TO ${SqlContext.escape(
                        login,
                    )}`;

                    await this.executeSqlStatement({
                        sql,
                    });
                });
        } catch (err) {
            SqlContext.logger.error(
                `[${this.user}] Could not give access rights to ${login} on "${this.schemaName}"."${this.tableName}" : ${err.message}\n${err.stack}`,
            );
        }
    }

    /**
     * Create a bunch of triggers from their definition
     */
    async createTriggers(triggers: TriggerDefinition[], client?: PoolClient): Promise<void> {
        if (triggers.length === 0) return;
        await this.withAdvisoryLock(async cnx => {
            const sqlParts: string[] = [];

            triggers.forEach(triggerDef => {
                const builder = TriggerBuilder.getBuilderForTrigger(triggerDef);
                if (builder.useDedicatedFunction) {
                    // First, create the function
                    const src = builder.getSqlToCreateDedicatedFunction(this.tableDef);
                    if (!src) {
                        throw new Error(
                            `The trigger ${triggerDef} does not provide any source for its dedicated function.`,
                        );
                    }
                    sqlParts.push(src);
                }
                const createTriggerSql = builder.getSqlToCreateTrigger(this.tableDef);
                sqlParts.push(createTriggerSql);
            });

            await this.execute(cnx, sqlParts.join('\n'), [], { logLevel: 'verbose' });
        }, client);
    }

    /**
     * Drop a bunch of triggers from their definition
     */
    async dropTriggers(triggers: TriggerDefinition[], client?: PoolClient): Promise<void> {
        await this.withAdvisoryLock(async cnx => {
            const sqlParts: string[] = [];

            triggers.forEach(triggerDef => {
                const builder = TriggerBuilder.getBuilderForTrigger(triggerDef);
                sqlParts.push(builder.getSqlToDrop(this.tableDef));
            });
            await this.execute(cnx, sqlParts.join('\n'), [], { logLevel: 'verbose' });
        }, client);
    }

    /** @internal */
    async createIndex(index: IndexDefinition): Promise<void> {
        await this.executeSqlStatement({
            sql: `${this.getSqlToCreateIndexFunctions([index])}${this.getSqlToCreateIndex(index)}`,
        });
    }

    /** @internal */
    async dropIndex(indexName: string): Promise<void> {
        await this.executeSqlStatement({ sql: this.getSqlToDropIndex(indexName) });
    }

    /** @internal */
    async addColumns(colDefs: ColumnDefinition[], options?: SqlAddColumnsOptions): Promise<void> {
        await this.executeSqlStatement({ sql: this.getSqlToAddColumns(colDefs, options) });
    }

    /** @internal */
    async alterColumns(
        columns: { definition: ColumnDefinition; inconsistencies: ColumnInconsistency[] }[],
    ): Promise<void> {
        const sql = this.getSqlToAlterColumns(columns);
        if (sql.length === 0) return;
        await this.executeSqlStatement({ sql });
    }

    /** @internal */
    async dropColumns(columnNames: string[]): Promise<void> {
        await this.executeSqlStatement({ sql: this.getSqlToDropColumns(columnNames) });
    }

    /** @internal */
    async renameForeignKey(oldFkName: string, newFkName: string): Promise<void> {
        await this.executeSqlStatement({ sql: this.getSqlToRenameForeignKey(oldFkName, newFkName) });
    }

    /** @internal */
    async addForeignKey(foreignKeyDefinition: ForeignKeyDefinition, options?: ForeignKeyOptions): Promise<void> {
        await this.executeSqlStatement({ sql: this.getSqlToCreateForeignKey(foreignKeyDefinition, options) });
    }

    /** @internal */
    async dropForeignKey(foreignKeyName: string): Promise<void> {
        await this.executeSqlStatement({ sql: this.getSqlToDropForeignKey(foreignKeyName) });
    }

    private get tableName(): string {
        return SqlContext.escape(this.tableDef.tableName);
    }

    private createTableColumns(options?: SqlCreateTableOptions): string {
        if (!this.tableDef.columns) return '';
        return this.tableDef.columns.map(colDef => this.getSqlToAddColumn(colDef, options)).join(', ');
    }

    private createPrimaryKey(): string {
        const table = this.tableDef;
        let primaryKey = '';
        if (table.primaryKey) {
            primaryKey = `,CONSTRAINT ${SqlContext.escape(
                makeName63(`${table.tableName}_PK`),
            )} PRIMARY KEY(${table.primaryKey.columns.map(pkCol => SqlContext.escape(pkCol)).join()})`;
        } else {
            throw new Error('Primary key not supplied in table definition');
        }
        return primaryKey;
    }

    /**
     * Returns the SQL query to execute to add/update a comment on a table or a column
     */
    public static getSqlToComment(
        objectType: 'table' | 'column' | 'foreignKey',
        objectInfo: {
            schemaName: string;
            tableName: string;
            columnName?: string;
            fkName?: string;
        },
        comment: SqlObjectJsonComment,
    ): string {
        switch (objectType) {
            case 'table':
                return `COMMENT ON TABLE ${objectInfo.schemaName}.${objectInfo.tableName} IS '${JSON.stringify(
                    comment,
                    null,
                    '  ',
                )}';`;
            case 'column':
                return `COMMENT ON COLUMN ${objectInfo.schemaName}.${objectInfo.tableName}.${
                    objectInfo.columnName
                } IS '${JSON.stringify(comment, null, '  ')}';`;
            case 'foreignKey':
                return `COMMENT ON CONSTRAINT ${objectInfo.fkName} ON ${objectInfo.schemaName}.${
                    objectInfo.tableName
                } IS '${JSON.stringify(comment, null, '  ')}';`;

            default:
                throw new Error(`Invalid object type to comment ${objectType}`);
        }
    }

    private getSqlToRenameForeignKey(oldFkName: string, newFkName: string): string {
        return `ALTER TABLE ${this.schemaName}.${this.tableName} RENAME CONSTRAINT "${oldFkName}" TO "${newFkName}"`;
    }

    private getSqlToCreateForeignKey(foreignKey: ForeignKeyDefinition, options?: ForeignKeyOptions): string {
        let deleteStmt: string;
        switch (foreignKey.onDeleteBehaviour) {
            case 'cascade':
                deleteStmt = ' ON DELETE CASCADE DEFERRABLE';
                break;
            case 'restrict':
                deleteStmt = ' ON DELETE RESTRICT DEFERRABLE';
                break;
            case 'noAction':
                deleteStmt = ' ON DELETE NO ACTION';
                break;
            default:
                throw new Error(`Unmanaged 'onDelete' behaviour : ${foreignKey.onDeleteBehaviour}`);
        }

        const withUpdateCascadePart = options?.withUpdateCascade ? ' ON UPDATE CASCADE' : '';
        const deferrablePart = foreignKey.isDeferrable ? ' DEFERRABLE' : '';
        let sql = `ALTER TABLE ${this.schemaName}.${this.tableName} ADD CONSTRAINT "${
            foreignKey.name
        }" FOREIGN KEY(${foreignKey.columnNames.map(column => SqlContext.escape(column)).join()}) REFERENCES ${
            this.schemaName
        }.${foreignKey.targetTable}(${foreignKey.targetColumnNames
            .map(targetColumnName => SqlContext.escape(targetColumnName))
            .join()})${deleteStmt}${withUpdateCascadePart}${deferrablePart};`;

        if (foreignKey.comment) {
            sql = `${sql}\n${ModifyTableSqlContext.getSqlToComment(
                'foreignKey',
                {
                    schemaName: this.schemaName,
                    tableName: this.tableName,
                    fkName: foreignKey.name,
                },
                foreignKey.comment,
            )};`;
        }
        return sql;
    }

    private getSqlToCreateForeignKeys(options?: ForeignKeyOptions): string {
        let foreignKeys = '';
        if (this.tableDef.foreignKeys) {
            this.tableDef.foreignKeys.forEach(foreignKey => {
                foreignKeys += this.getSqlToCreateForeignKey(foreignKey, options);
            });
        }
        return foreignKeys;
    }

    private createTableIndexes(): string {
        if (!this.tableDef.indexes) return '';
        return `${this.getSqlToCreateIndexFunctions(this.tableDef.indexes)}${this.tableDef.indexes
            .map(index => this.getSqlToCreateIndex(index))
            .join('\n')}`;
    }

    private getSqlToCreateIndexFunctions(indexes: IndexDefinition[]): string {
        const indexColumns = new Set<IndexColumn>();
        indexes.forEach(index => index.columns.filter(c => c.expression).forEach(idx => indexColumns.add(idx)));
        const columns = Array.from(indexColumns);
        const enums = new Set<string>();
        columns.forEach(idxCol => {
            const col = this.tableDef.columns?.find(c => c.name === idxCol.name);
            const enumName = col?.enumDataType?.name;
            if (enumName) enums.add(enumName);
        });
        if (enums.size > 0) {
            return Array.from(enums)
                .map(e => EnumSqlContext.getSqlToCreateEnumCoalesceFunction(this.schemaName, e))
                .join('\n');
        }
        return '';
    }

    /**
     * Returns the SQL statement that must be executed to add a column
     */
    private getSqlToAddColumn(column: ColumnDefinition, options?: SqlCreateTableOptions): string {
        const parsed = parseColumnDefinition(this.schemaName, this.tableName, column, options);
        let columnSql = `${parsed.columnName} ${parsed.typeName}`;
        if (!options?.skipDefault && parsed.default) {
            const deferredSql = options?.deferredSql;
            // currval cannot be used in default expression when adding a column because it requires
            // a nextval to being called previously in the same session
            if (deferredSql && /\bcurrval\(/.test(parsed.default)) {
                this.computeDeferredDefaultSql(parsed.columnName, parsed.default, deferredSql);
            } else {
                columnSql = `${columnSql} DEFAULT ${parsed.default}`;
            }
        }

        if (parsed.nullable) {
            columnSql = `${columnSql} ${parsed.nullable}`;
        }
        return options?.isAltering ? `ADD COLUMN IF NOT EXISTS ${columnSql}` : columnSql;
    }

    private computeDeferredDefaultSql(columnName: string, defaultValue: string, deferredSql: DeferredSql): void {
        deferredSql.alterColumns = deferredSql.alterColumns ?? [];
        deferredSql.alterColumns.push(`ALTER COLUMN ${columnName} SET DEFAULT ${defaultValue}`);
        deferredSql.updateColumns = deferredSql.updateColumns ?? [];
        // extract table from which to get the value
        const m =
            /currval\(\(pg_get_serial_sequence\('([a-z_][a-z0-9_.]*)'::text, '_id'::text\)\)::regclass\)(.*)/.exec(
                defaultValue.startsWith('(') ? defaultValue.slice(1, -1) : defaultValue,
            );
        if (!m) {
            throw new Error(`Unhandled currval expression in DEFAULT constraint: ${defaultValue}`);
        }
        const [qualifiedTableName, expression] = m.slice(1);
        const sqlUpdate = `UPDATE ${this.schemaName}.${this.tableName} AS t0 SET ${columnName}`;
        if (qualifiedTableName === `${this.schemaName}.${this.tableName}`) {
            deferredSql.updateColumns.push(`${sqlUpdate}=_id ${expression} WHERE ${columnName} IS NULL;`);
        } else {
            const tenantIdFilter = this.tableDef.isSharedByAllTenants ? '' : 'AND t0._tenant_id = t1._tenant_id';
            deferredSql.updateColumns.push(
                `${sqlUpdate}=(SELECT _id ${expression} FROM ${qualifiedTableName} as t1 WHERE t0._id = t1._id ${tenantIdFilter}) WHERE ${columnName} IS NULL;`,
            );
        }
    }

    /**
     * Returns the SQL statement that must be executed to alter a column
     */
    private getSqlToAlterColumn(
        column: ColumnDefinition,
        inconsistencies: ColumnInconsistency[],
        options?: SqlCreateTableOptions,
    ): string {
        const parsed = parseColumnDefinition(this.schemaName, this.tableName, column, options);

        // Note : if some more inconsistencies are managed in the future, it may be necessary
        // to update the comment of the column as well
        return inconsistencies
            .map(inconsistency => {
                if (inconsistency instanceof ColumnInconsistencyNullable) {
                    return `ALTER COLUMN ${parsed.columnName} ${inconsistency.oldNullableFlag ? 'SET NOT NULL' : 'DROP NOT NULL'}`;
                }
                if (inconsistency instanceof ColumnInconsistencyDefaultValue) {
                    return `ALTER COLUMN ${parsed.columnName} ${
                        parsed.default ? `SET DEFAULT ${parsed.default}` : 'DROP DEFAULT'
                    }`;
                }
                throw new Error(
                    `Unmanaged inconsistency on column ${this.tableName}.${column.name}: ${inconsistency.description}`,
                );
            })
            .filter(sql => !!sql)
            .join(', ');
    }

    /** @internal */
    private getSqlToAddColumns(columns: ColumnDefinition[], options?: SqlAddColumnsOptions): string {
        // We allow the tableNameToUse to be passed in for the scenario where the table we wish to add the column to has been
        // renamed, but the rename action has not been executed yet, therefore we can use tableNameToUse to pass the old table name.
        const tableName = options?.tableNameToUse || this.tableName;
        const deferredSql = {} as DeferredSql;
        const sqlStatements = [
            `ALTER TABLE ${this.schemaName}.${tableName} ${columns
                .map(column => {
                    return this.getSqlToAddColumn(column, {
                        isAltering: true,
                        skipConstraints: options?.skipConstraints,
                        deferredSql,
                    });
                })
                .join(', ')};`,
        ];
        const { alterColumns, updateColumns } = deferredSql;
        if (updateColumns && updateColumns.length > 0) {
            // then we may need to update the value of these default column
            sqlStatements.push(...updateColumns);
        }
        if (alterColumns && alterColumns.length > 0) {
            // set default constraint of those that were deferred
            sqlStatements.push(`ALTER TABLE ${this.schemaName}.${tableName} ${alterColumns.join(', ')};`);
        }
        sqlStatements.push(
            ...columns
                .filter(column => !!column.comment)
                .map(column =>
                    ModifyTableSqlContext.getSqlToComment(
                        'column',
                        {
                            schemaName: this.schemaName,
                            tableName,
                            columnName: column.name,
                        },
                        column.comment!,
                    ),
                ),
        );
        return sqlStatements.join('\n');
    }

    /**
     * Returns the SQL query to execute to update a set of columns
     */
    private getSqlToAlterColumns(
        columns: { definition: ColumnDefinition; inconsistencies: ColumnInconsistency[] }[],
    ): string {
        const columnsParts = columns
            .map(column =>
                this.getSqlToAlterColumn(column.definition, column.inconsistencies, {
                    skipConstraints: false,
                }),
            )
            .filter(sql => !!sql); // Remove '' when there are no inconsistencies
        if (columnsParts.length === 0) {
            // This will happen when all the inconsistencies are related to a type change that
            // was already managed by a convertColumnTypeAction
            return '';
        }
        return `ALTER TABLE ${this.schemaName}.${this.tableName} ${columnsParts.join(', ')};`;
    }

    private getSqlToDropColumns(columnNames: string[]): string {
        return `ALTER TABLE ${this.schemaName}.${this.tableName} ${columnNames
            .map(colName => `DROP COLUMN IF EXISTS ${colName}`)
            .join(', ')};`;
    }

    private getSqlToDropForeignKey(foreignKeyName: string): string {
        return `ALTER TABLE ${this.schemaName}.${this.tableName} DROP CONSTRAINT IF EXISTS ${foreignKeyName};`;
    }

    private getSqlToCreateIndex(index: IndexDefinition): string {
        return `CREATE ${index.isUnique ? 'UNIQUE' : ''} INDEX ${index.name} ON ${this.schemaName}.${
            this.tableName
        }(${index.columns
            .map(
                column => `${column.expression || SqlContext.escape(column.name)} ${column.ascending ? 'ASC' : 'DESC'}`,
            )
            .join()});`;
    }

    private getSqlToDropIndex(indexName: string): string {
        return `DROP INDEX IF EXISTS ${this.schemaName}.${SqlContext.escape(indexName)};`;
    }
}
