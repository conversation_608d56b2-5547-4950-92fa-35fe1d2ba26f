import { asyncArray } from '@sage/xtrem-async-helper';
import { Context } from '../../runtime/context';
import { SchemaSqlContext } from './schema-sql-context';
import { SqlContext } from './sql-context';

interface TableOptions {
    errorsAsWarnings: boolean;
    isShared?: boolean;
}

interface SqlResult {
    success: boolean;
    rowCount: number;
}

export class UpdateSqlContext extends SchemaSqlContext {
    /**
     * Tries to update a table and returns whether the update succeeded
     */
    async updateTable(
        context: Context,
        name: string,
        dataToUpdate: { columnName: string; value: any }[],
        options?: TableOptions,
    ): Promise<SqlResult> {
        let updateCount = 0;
        let success = true;
        await this.withConnection(async cnx => {
            const tableName = SqlContext.getFullTableName(this.schemaName, name);

            try {
                const result = await this.execute(
                    cnx,
                    `UPDATE ${tableName} SET ${dataToUpdate
                        .map(data => `${data.columnName} = ${data.value}`)
                        .join(',')} WHERE _tenant_id = '${context.tenantId}'`,
                );
                updateCount += (result as any).updateCount;
            } catch (err) {
                // The table does not exist
                if (options?.errorsAsWarnings) {
                    SqlContext.logger.warn(
                        `Could not update table '${tableName} for tenant ${context.tenantId} : ${err.stack}`,
                    );
                    success = false;
                } else {
                    throw err;
                }
            }
        });
        return {
            success,
            rowCount: updateCount,
        };
    }

    /**
     * Tries to delete a table and returns whether the drop succeeded
     */
    deleteFromTable(context: Context, name: string, options?: TableOptions): Promise<SqlResult> {
        return this.deleteFromTables(context, [name], options);
    }

    async deleteFromTables(context: Context, tablesToDelete: string[], options?: TableOptions): Promise<SqlResult> {
        let updateCount = 0;
        let success = true;
        await this.withConnection(cnx =>
            asyncArray(tablesToDelete).forEach(async name => {
                const tableName = SqlContext.getFullTableName(this.schemaName, name);
                try {
                    const result = await this.execute<{ updateCount: number }>(
                        cnx,
                        `DELETE FROM ${tableName} WHERE ${options?.isShared ? 'tenant_id' : '_tenant_id'} = '${
                            context.tenantId
                        }'`,
                    );
                    updateCount += result.updateCount;
                } catch (err) {
                    // The table couldn't be deleted:
                    const message = `Could not delete table '${tableName} for tenant ${context.tenantId} : ${err.stack}`;
                    if (options?.errorsAsWarnings) {
                        SqlContext.logger.warn(message);
                        success = false;
                    } else {
                        SqlContext.logger.error(message);
                        throw err;
                    }
                }
            }),
        );
        return {
            success,
            rowCount: updateCount,
        };
    }
}
