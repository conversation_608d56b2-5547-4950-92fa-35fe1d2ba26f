import { CustomMetrics } from '@sage/xtrem-metrics';
import { ConnectionPool, DatabaseService, PoolConfig } from '@sage/xtrem-postgres';
import { loggers } from '../../runtime/loggers';

const logger = loggers.sql;

export abstract class DatabaseManager {
    static getDatabaseService(config: PoolConfig): DatabaseService {
        ConnectionPool.setup({
            logger,
            metricsEmitter: CustomMetrics.emitter,
        });
        return DatabaseService.getInstance(config);
    }

    static getSysPool(config: PoolConfig): ConnectionPool {
        return this.getDatabaseService(config).sysPool;
    }

    static getPool(config: PoolConfig): ConnectionPool {
        return this.getDatabaseService(config).userPool;
    }
}
