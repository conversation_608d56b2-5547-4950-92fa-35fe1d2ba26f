import { DatabaseService } from '@sage/xtrem-postgres';
import { loggers } from '../../runtime/loggers';
import { createExtensions } from '../statements/extensions';
import { createFunctions } from '../statements/functions';
import { DatabaseManager } from './database-manager';
import { SqlContext } from './sql-context';

const logger = loggers.sql;

export class DatabaseSqlContext extends SqlContext {
    private databaseService: DatabaseService;

    /**
     * @param force force reset database user and schema if they exist
     */
    constructor() {
        super(5771350283228132, 'sys');
        this.databaseService = DatabaseManager.getDatabaseService(this.config);
    }

    /**
     * Drops the database if it exists
     * Note: it won't drop the database if it is the sys database (we should never drop the sys database)
     */
    async dropDatabaseIfExists(): Promise<void> {
        await this.databaseService.dropDatabaseIfExists();
    }

    /**
     * Create database
     * @param force force reset database user and schema if they exist
     */
    async createDatabaseIfNotExists(): Promise<void> {
        await this.databaseService.createDatabaseIfNotExists();
    }

    /**
     * Rename a schema
     */
    async renameSchema(oldName: string, newName: string): Promise<void> {
        await this.databaseService.renameSchema(oldName, newName);
    }

    /**
     * Create schema
     */

    async createSchemaIfNotExists(schemaName: string): Promise<void> {
        await this.createDatabaseIfNotExists();

        const { user, password, sysUser, sysPassword } = this.config;

        let found = false;
        await this.withConnection(async cnx => {
            await this.databaseService.createUsers(cnx, user, password, sysUser, sysPassword);
            const result = await this.execute<{ schema_name: string }[]>(
                cnx,
                'SELECT schema_name FROM information_schema.schemata WHERE schema_name = $1',
                [schemaName],
            );
            found = result.length > 0;
            if (found) {
                logger.verbose(() => `Schema ${schemaName} already exists`);
                return;
            }
            logger.info(`Creating SQL schema ${schemaName}`);
            await this.execute(cnx, `CREATE SCHEMA ${schemaName};`);
            await createExtensions(sql => this.execute(cnx, sql));
            await createFunctions(schemaName, sql => this.execute(cnx, sql));
        });
        if (!found && this.config.user !== this.config.sysUser) await this.setUserDefaultPrivileges(schemaName);
    }

    /**
     * Create schema
     * @param schemaName
     */
    async dropSchemaIfExists(schemaName: string): Promise<void> {
        await this.withAdvisoryLock(cnx =>
            this.execute(cnx, `DROP SCHEMA IF EXISTS ${schemaName} CASCADE;`, [], { logLevel: 'debug' }),
        );
    }

    /**
     * @internal
     * Set user default privileges. These privileges will be inheritted to all tables and sequences created in the future.
     */
    async setUserDefaultPrivileges(schemaName: string): Promise<void> {
        const user = this.config.user;
        await this.withConnection(async cnx => {
            await this.execute(cnx, `GRANT USAGE ON SCHEMA ${schemaName} TO ${user};`);
            await this.execute(
                cnx,
                `ALTER DEFAULT PRIVILEGES IN SCHEMA ${schemaName} GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO ${user}`,
            );
            await this.execute(
                cnx,
                `ALTER DEFAULT PRIVILEGES IN SCHEMA ${schemaName} GRANT USAGE, SELECT ON SEQUENCES TO ${user}`,
            );
        });
    }

    /**
     * @internal
     * Grant user privileges to existing tables and sequences on the schema
     */
    async setUserPrivileges(schemaName: string): Promise<void> {
        const user = this.config.user;
        await this.withConnection(async cnx => {
            // Grant on all tables for DML statements: SELECT, INSERT, UPDATE, DELETE
            await this.execute(
                cnx,
                `GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA ${schemaName} TO ${user};`,
            );
            // Grant selact and usage privileges on all sequences in the schema
            await this.execute(cnx, `GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA ${schemaName} TO ${user};`);
        });
    }
}
