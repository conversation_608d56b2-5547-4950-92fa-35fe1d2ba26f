/** @module @sage/xtrem-postgres */
// why query on collections when insert? --> planned work to separate collections vs references + scope *.
// too many connections errors in lists (cache?)  --> pool
// cache schemas?? --> double check and provide traces.
import { AnyValue, AsyncReader, AsyncResponse } from '@sage/xtrem-async-helper';
import { ConfigManager } from '@sage/xtrem-config';
import {
    Connection,
    ConnectionPool,
    PoolClient,
    PoolConfig,
    PoolLoggerInterface,
    PostgresPoolReaderOptions,
    PostgresSqlExecuteOptions,
    TableDefinition,
} from '@sage/xtrem-postgres';
import { globalRunningContext } from '../../runtime';
import { loggers } from '../../runtime/loggers';
import { keywordsRegExp } from '../statements/keywords';
import { DatabaseManager } from './database-manager';
import { SqlErrorMessageType } from './types';

interface ExecuteSqlParameters {
    cnx?: Connection;
    sql: string;
    args?: any[];
    opts?: PostgresSqlExecuteOptions;
}

type ContextConnectionPoolType = 'user' | 'sys';

export class SqlContext {
    /** The default pool type: 'sys' or 'user' */
    #poolType: ContextConnectionPoolType;

    /** The default pool */
    #connectionPool: ConnectionPool;

    readonly config: PoolConfig;

    /**
     * Creates a new instance of the SqlContext
     * @param lockId the lock number (must be unique and randomly generated)
     * @param poolType the pool type: 'sys' or 'user'
     */
    constructor(
        protected lockId?: number,
        poolType?: ContextConnectionPoolType,
    ) {
        this.#poolType = globalRunningContext.isServicesMode ? 'user' : (poolType ?? 'user');
        this.config = ConfigManager.current.storage!.sql!;
        // Use the sys pool (except for the creation of temporary tables)
    }

    static get logger(): PoolLoggerInterface {
        return loggers.sql;
    }

    protected withAdvisoryLock<T extends AnyValue>(
        body: (cnx: Connection) => AsyncResponse<T>,
        client?: Connection,
    ): Promise<T> {
        if (client) {
            return this.withAdvisoryLockCnx(client, body);
        }
        return this.withConnection(cnx => this.withAdvisoryLockCnx(cnx, body));
    }

    private async withAdvisoryLockCnx<T extends AnyValue>(
        cnx: Connection,
        body: (cnx: Connection) => AsyncResponse<T>,
    ): Promise<T> {
        const { lockId } = this;
        if (!lockId) throw new Error('No lock id defined for setting advisory lock in this context');
        try {
            await this.connectionPool.execute(cnx, `SELECT pg_advisory_lock(${lockId})`);
            return await body(cnx);
        } finally {
            await this.connectionPool.execute(cnx, `SELECT pg_advisory_unlock(${lockId})`);
        }
    }

    get connectionPool(): ConnectionPool {
        if (!this.#connectionPool) {
            this.#connectionPool =
                this.#poolType === 'sys'
                    ? DatabaseManager.getSysPool(this.config)
                    : DatabaseManager.getPool(this.config);
        }
        return this.#connectionPool;
    }

    // Todo 6027: pass it to protected. For now, only used outside by the upgrade
    execute<T extends AnyValue | void>(
        cnx: Connection,
        sql: string,
        args?: any[],
        opts?: PostgresSqlExecuteOptions,
    ): Promise<T> {
        return this.connectionPool.execute(cnx, sql, args, opts);
    }

    // Todo 6027: pass it to protected. For now, only used outside by the upgrade.
    withConnection<T extends AnyValue | void>(
        body: (cnx: Connection) => AsyncResponse<T>,
        connection?: PoolClient,
    ): Promise<T> {
        return this.connectionPool.withConnection(body, connection);
    }

    // Todo 6027: pass it to protected. For now, only used outside by the upgrade.
    createReader<T extends AnyValue>(
        cnx: Connection,
        sql: string,
        args: any[],
        opts?: PostgresPoolReaderOptions,
    ): AsyncReader<T> {
        return this.connectionPool.createReader(cnx, sql, args, opts);
    }

    executeSqlStatement<T extends AnyValue | void>(params: ExecuteSqlParameters): Promise<T> {
        return this.withConnection(cnx => this.execute(cnx, params.sql, params.args, params.opts), params.cnx);
    }

    get user(): string {
        return this.connectionPool.user;
    }

    /**
     * Returns string containing parameter with passed index i
     * @param i
     */
    static param(i: number): string {
        return `$${i + 1}`;
    }

    /**
     * Formats a column name with double quotes
     *
     * @see https://www.postgresql.org/docs/13/sql-syntax-lexical.html
     *
     * @param name
     */
    static escape(name: string): string {
        // TODO: throw if name contains characters that require escaping
        // if (/[^a-z0-9_]/.test(name)) throw new Error(`${name}: invalid postgres name`);
        if (/[^a-z0-9_]/.test(name)) return `"${name}"`;
        return keywordsRegExp.test(name) ? `"${name}"` : name;
    }

    /**
     * Checks if passed error is an index not found error
     * @param err
     */
    static isIndexNotFound(ex: Error): boolean {
        return /ER_CANT_DROP_FIELD_OR_KEY/.test(ex.message);
    }

    /**
     * Checks if passed error is a table not found error
     * @param err
     */
    static isTableNotFound(ex: Error): boolean {
        return /ER_NO_SUCH_TABLE/.test(ex.message);
    }

    /**
     * Checks if passed error is a unique contraint violation
     * @param err
     */
    static isUniqueViolated(ex: Error): boolean {
        return /ER_DUP_ENTRY/.test(ex.message);
    }

    /**
     * Checks if passed error is a locked record error
     * @param err
     */
    static isLocked(ex: Error): boolean {
        return /ER_LOCK_OR_ACTIVE_TRANSACTION/.test(ex.message);
    }

    /**
     * Checks if passed error is a no data found error
     * @param err
     */
    static isNoRecord(ex: Error): boolean {
        return /ER_KEY_NOT_FOUND/.test(ex.message);
    }

    /**
     * Return the full qualified table name based on the passed table definition
     * @param def
     */
    static getFullTableDefName(tableDef: TableDefinition): string {
        return SqlContext.getFullTableName(tableDef.schemaName, tableDef.tableName);
    }

    /**
     *  Return the full qualified table name based on the passed schema, table name and alias
     * @param schemaName
     * @param tableName
     * @param alias
     */
    static getFullTableName(schemaName: string, tableName: string, alias?: string): string {
        const fullName = schemaName
            ? `${SqlContext.escape(schemaName)}.${SqlContext.escape(tableName)}`
            : `${SqlContext.escape(tableName)}`;
        return alias ? `${fullName} ${alias}` : fullName;
    }

    /**
     * Returning message error according to the type.
     * The type as will as its options should be defined in SqlErrorMessageType
     * @param type
     * @param options
     */
    static sqlErrorMessage(
        type: keyof SqlErrorMessageType,
        options: SqlErrorMessageType[keyof SqlErrorMessageType],
    ): string {
        if (type === 'violatesForeignKeyConstraint') {
            return `insert or update on table "${options!.table}" violates foreign key constraint "${
                options!.foreignKey
            }": Key (${Object.keys(options!.key).join(', ')})=(${Object.values(options!.key).join(
                ', ',
            )}) is not present in table "${options?.fkTable}".`;
        }

        return 'Unknown type';
    }
}
