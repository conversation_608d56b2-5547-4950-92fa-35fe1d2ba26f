import { asyncArray } from '@sage/xtrem-async-helper';
import { ColumnJsonComment } from '@sage/xtrem-postgres';
import { ModifyTableSqlContext } from '.';
import { Application } from '../../application';
import { createExtensions } from '../statements/extensions';
import { createFunctions } from '../statements/functions';
import { SqlContext } from './sql-context';

interface DropTableOptions {
    isCascade: boolean;
    errorsAsWarnings: boolean;
}

export class SchemaSqlContext extends SqlContext {
    constructor(readonly application: Application) {
        super(7435141139632913, 'sys');
    }

    get schemaName(): string {
        return this.application.schemaName;
    }

    createSchema(): Promise<void> {
        return this.executeSqlStatement<void>({ sql: `CREATE SCHEMA IF NOT EXISTS ${this.schemaName}` });
    }

    dropSchema(schemaName?: string): Promise<void> {
        return this.executeSqlStatement({ sql: `DROP SCHEMA IF EXISTS ${schemaName || this.schemaName} CASCADE` });
    }

    renameSchema(oldName: string, newName: string): Promise<void> {
        return this.executeSqlStatement({ sql: `ALTER SCHEMA ${oldName} RENAME TO ${newName}` });
    }

    async createFunctions(): Promise<void> {
        await this.withConnection(cnx => createFunctions(this.schemaName, sql => this.execute(cnx, sql)));
    }

    async createExtensions(): Promise<void> {
        await this.withConnection(cnx => createExtensions(sql => this.execute(cnx, sql)));
    }

    /**
     * Verifies that the supplied table name exists in the supplied schema
     * @param name
     */
    tableExists(tableName: string): Promise<boolean> {
        const sql = 'SELECT to_regclass($1);';
        return this.withConnection(async cnx => {
            const records = await this.execute<{ to_regclass: string }[]>(
                cnx,
                sql,
                [(this.schemaName ? `${this.schemaName}.` : '') + tableName],
                {
                    logLevel: 'debug',
                },
            );
            return !!(records.length && records[0].to_regclass != null);
        });
    }

    /**
     * Tries to drop a table and returns whether the drop succeeded
     */
    dropTable(
        name: string,
        options: DropTableOptions = { isCascade: false, errorsAsWarnings: false },
    ): Promise<boolean> {
        return this.dropTables([name], options);
    }

    dropTables(
        tablesToDrop: string[],
        options: DropTableOptions = { isCascade: false, errorsAsWarnings: true },
    ): Promise<boolean> {
        return this.withConnection(async cnx => {
            let success = true;
            await asyncArray(tablesToDrop).forEach(async name => {
                const tableName = SqlContext.getFullTableName(this.schemaName, name);
                try {
                    await this.execute(cnx, `DROP TABLE ${tableName} ${options.isCascade ? ' CASCADE ' : ''}`);
                } catch (err) {
                    // The table does not exist
                    if (options.errorsAsWarnings) {
                        SqlContext.logger.warn(`Could not drop table '${tableName} : ${err.stack}`);
                        success = false;
                        return;
                    }
                    throw err;
                }
            });
            return success;
        });
    }

    async updateColumnType(
        tableName: string,
        columnName: string,
        typeName: string,
        columnComment: ColumnJsonComment,
        options: {
            using?: string;
            dropDefault?: boolean;
        },
    ): Promise<void> {
        const alterColumnSql = `ALTER TABLE ${this.schemaName}.${tableName} ALTER COLUMN ${columnName}`;
        let sql = `${alterColumnSql} TYPE ${typeName}`;
        if (options.dropDefault || options.using) {
            // We need to drop default as the using is not applied if the column has a default value
            await this.executeSqlStatement({ sql: `${alterColumnSql} DROP DEFAULT` });
            await this.executeSqlStatement({
                sql: `DROP SEQUENCE IF EXISTS ${this.schemaName}.${tableName}_${columnName}_seq`,
            });
        }
        if (options.using) {
            sql = `${sql} USING ${options.using}`;
        }
        sql = `${sql};${ModifyTableSqlContext.getSqlToComment(
            'column',
            {
                schemaName: this.schemaName,
                tableName,
                columnName,
            },
            columnComment,
        )};`;

        await this.executeSqlStatement({ sql });
    }
}
