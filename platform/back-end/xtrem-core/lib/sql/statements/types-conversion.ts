import {
    ColumnDefinition,
    ColumnTypeName,
    decimalColumnPrecision,
    decimalColumnScale,
    SqlCreateTableOptions,
} from '@sage/xtrem-postgres';
import { SqlContext } from '../sql-context/sql-context';
import {
    ParseColumnDefinitionResult,
    PostgreSqlColumnType,
    PostgreSqlTypes,
    PostgreSqlTypesToXtrem,
    XtremToPostgreSqlType,
} from '../sql-context/types';

export const xtremToPostgreSql: XtremToPostgreSqlType = {
    boolean: { type: 'BOOL', default: null },
    enum: { type: 'INT2', default: null },
    short: { type: 'INT4', default: null },
    reference: { type: 'INT8', default: null },
    // integer after reference so that reverse map gives 'integer' rather than 'reference'
    integer: { type: 'INT8', default: null },
    integerRange: { type: 'INT8RANGE', default: null },
    decimalRange: { type: 'NUMRANGE', default: null },
    date: { type: 'DATE', default: null },
    dateRange: { type: 'DATERANGE', default: null },
    datetimeRange: { type: 'TSTZRANGE', default: null },
    time: { type: 'TIME', default: null },
    datetime: { type: 'TIMESTAMPTZ(3)', default: null },
    float: { type: 'FLOAT4', default: null },
    decimal: { type: 'NUMERIC', default: null },
    double: { type: 'FLOAT8', default: null },
    binaryStream: { type: 'BYTEA', default: "''" },
    textStream: { type: 'TEXT', default: "''" },
    string: { type: 'VARCHAR', default: "''" },
    uuid: { type: 'UUID', default: null },
    byte: { type: 'INT2', default: null },
    binary: { type: 'BYTEA', default: null },
    json: { type: 'JSONB', default: null },
    integerArray: { type: '_INT8', default: null },
    enumArray: { type: '_INT2', default: null },
    referenceArray: { type: '_INT8', default: null },
    stringArray: { type: '_VARCHAR', default: null },
};

export const postgreSqlToXtrem: PostgreSqlTypesToXtrem = {};

Object.keys(xtremToPostgreSql).forEach((key: ColumnTypeName) => {
    const val = xtremToPostgreSql[key];
    postgreSqlToXtrem[val.type] = {
        name: key,
        default: val.default,
    };
});

// Compat mappings to avoid failing with "Unmanaged column type" error during upgrade from version 15.
(postgreSqlToXtrem as any).TIMESTAMP = { name: 'datetime', default: null };
(postgreSqlToXtrem as any).TSRANGE = { name: 'datetimeRange', default: null };
(postgreSqlToXtrem as any)['TIMESTAMPTZ(6)'] = { name: 'datetime', default: null };

/**
 * Returns the storage size needed to store a string in encrypted mode
 * (encrypted strings are longer than decrypted string)
 */
export function getEncryptedStringStorageSize(maxLength: number): number {
    return 1 + 2 * (16 + maxLength);
}

/**
 * Returns the SQL default value to apply to non nullable self references
 * currval(pg_get_serial_sequence('<schemaName>.<rootTablename>'::text, '_id'::text)::regclass)
 */
export function getSqlSerialSequence(rootTableFullName: string): string {
    return `(pg_get_serial_sequence('${rootTableFullName}'::text, '_id'::text))::regclass`;
}

/**
 * Returns the SQL default value to apply to non nullable self references
 * currval(pg_get_serial_sequence('<schemaName>.<rootTablename>'::text, '_id'::text)::regclass)
 */
export function getSqlCurrvalOfIdSequence(rootTableFullName: string): string {
    return `currval(${getSqlSerialSequence(rootTableFullName)})`;
}

/**
 * Get the SQL clause (WITHOUT the 'DEFAULT' keyword) to apply to a column to create it's default value.
 * Will return undefined if the column does not need any SQL default value.
 * @param tableName
 * @param column
 * @param type
 * @returns
 */
export function getColumnSqlDefaultClause(column: ColumnDefinition, type?: PostgreSqlColumnType): string | undefined {
    const typeToUse = type ?? getPostgreSqlType(column);
    const defVal = getColumnDefaultValue(typeToUse.name);
    if (
        typeToUse.name !== 'SERIAL8' &&
        typeToUse.name !== 'BYTEA' &&
        typeToUse.name !== 'TEXT' &&
        !typeToUse.isEnum &&
        (column.default !== null || defVal !== null)
    ) {
        return column.default ? column.default : defVal;
    }
    return undefined;
}

/**
 * Parses a columnDefinition into easy to use informations
 * @param tableName
 * @param column
 * @param options
 * @returns
 */
export function parseColumnDefinition(
    schemaName: string,
    tableName: string,
    column: ColumnDefinition,
    options?: SqlCreateTableOptions,
): ParseColumnDefinitionResult {
    const result: ParseColumnDefinitionResult = {
        columnName: '',
        typeName: '',
        nullable: '',
    };

    const type = getPostgreSqlType(column);
    if (!type) {
        throw new Error(`Column ${tableName}.${column.name}: unknown column type ${column.colType}`);
    }
    result.columnName = SqlContext.escape(column.name);
    result.typeName = type.isEnum ? `${schemaName}.${type.name}` : type.name;
    if (type.typeNotation) result.typeName = `${result.typeName}${type.typeNotation}`;

    result.default = getColumnSqlDefaultClause(column, type);

    if (options?.skipConstraints) {
        // Do not set any 'NOT NULL' constraint: they will be added later
    } else if (!column.isNullable) result.nullable = 'NOT NULL';

    return result;
}

export function getPostgreSqlType(colDef: ColumnDefinition): PostgreSqlColumnType {
    let typeNotation = '';
    let isEnum = false;
    switch (colDef.type) {
        case 'string': {
            let length = colDef.maxLength ? colDef.maxLength : 255;
            if (colDef.isEncryptedString) {
                length = getEncryptedStringStorageSize(length);
            }
            typeNotation = `(${length}) COLLATE "und-x-icu"`;
            break;
        }
        case 'decimal':
            // Whatever the definition of the property is, the decimal columns are always stored as (28,10)
            typeNotation = `(${decimalColumnPrecision},${decimalColumnScale})`;
            break;
        case 'time':
            typeNotation = `(${colDef.precision || 0})`;
            break;
        default:
            break;
    }

    let typeName;
    if (colDef.isAutoIncrement) {
        typeName = 'SERIAL8';
    } else {
        typeName = xtremToPostgreSql[colDef.type as ColumnTypeName].type;
    }

    if (colDef.type === 'enum') {
        typeName = colDef.enumDataType!.name;
        isEnum = true;
    }

    if (colDef.type === 'enumArray') {
        typeName = `_${colDef.enumDataType!.name}`;
        isEnum = true;
    }

    return { name: typeName as PostgreSqlTypes, typeNotation, isEnum };
}

export function getColumnDefaultValue(columnType: PostgreSqlTypes): any {
    if (columnType === 'SERIAL8') {
        return 0;
    }
    return postgreSqlToXtrem[columnType]?.default;
}
