import { createHash } from 'crypto';

/**
 * Ensure that the passed string, name, is at most 63 characters long
 * When the string is longer, return a new string with the following structure:
 *      First 5 characters of original string + _ +
 *      MD5 hash of original string (32 characters) + _ +
 *      The last 24 characters of the original string
 * @param name
 * @returns Original name, or new string with maximum 63 characters when longer than 63
 */
export function makeName63(name: string): string {
    if (name.length <= 63) return name;
    const prefix = name.substr(0, 5);
    const suffix = name.substr(name.length - 24, name.length);
    const hash = createHash('md5').update(name).digest('hex');
    return `${prefix}_${hash}_${suffix}`;
}

/**
 * Ensure that the passed string, name, is within the maxLength limit
 * When the string is longer than the limit, return a new string with an MD5 hash (32 characters) at the end
 * of the string and the original content as the start of the string, concatenated to the maxLength limit
 * @param name
 * @param maxLength
 * @returns Original or new string within the maxLength limit
 */
export function makeNameLimit(name: string, maxLength: number): string {
    if (name.length <= maxLength) return name;
    const hash = createHash('md5').update(name).digest('hex');
    const prefix = name.substr(0, maxLength - hash.length);
    return `${prefix}${hash}`;
}
