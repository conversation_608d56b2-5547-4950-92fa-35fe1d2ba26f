import { TableDefinition } from '@sage/xtrem-postgres';
import { TriggerBuilder } from './trigger-builder';

/**
 * @internal
 */
export class DeleteAttachmentNodeTriggerBuilder extends TriggerBuilder {
    constructor(
        public sourceNodeName: string,
        public attachmentAssociationTableName: string,
    ) {
        super('delete_attachments', {
            when: 'AFTER',
            event: 'DELETE',
            options: `${sourceNodeName}/${attachmentAssociationTableName}`,
        });
    }

    /**
     * Indicates whether the trigger uses a dedicated function (that should be created
     * when the trigger is created and dropped when the trigger is dropped).
     * Should return false if the trigger uses a common function (insert_table, ...)
     */
    // eslint-disable-next-line class-methods-use-this
    override get useDedicatedFunction(): boolean {
        return true;
    }

    /**
     * Returns the SQL command to execute to create the dedicated function that will be called by the trigger (if any)
     * Will return undefined when the trigger is using a common function (insert_table, ...).
     * Must be in sync with useDedicatedFunction.
     */
    override getSqlToCreateDedicatedFunction(tableDef: TableDefinition): string | undefined {
        return `CREATE OR REPLACE FUNCTION ${this.getFunctionFullname(tableDef)}()
            RETURNS TRIGGER
            AS
            $$
            BEGIN
                DELETE FROM ${tableDef.schemaName}.${this.attachmentAssociationTableName} WHERE source_node_name = '${this.sourceNodeName}' AND source_node_id = OLD._id AND _tenant_id = OLD._tenant_id;
            RETURN OLD;
            END;
            $$
            LANGUAGE plpgsql;`;
    }
}
