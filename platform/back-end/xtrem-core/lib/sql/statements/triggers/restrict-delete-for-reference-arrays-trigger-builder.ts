import { TableDefinition } from '@sage/xtrem-postgres';
import { Dict } from '@sage/xtrem-shared';
import { TriggerBuilder } from './trigger-builder';

/**
 * @internal
 * A builder to restrict delete the tag from _tags collections (nodes with hasTags=true)
 */
export class RestrictDeleteForReferenceArraysTriggerBuilder extends TriggerBuilder {
    constructor(
        tableName: string,
        private readonly tablesByColumn: Dict<string>,
    ) {
        super('restrict_delete_for_reference_arrays', {
            when: 'AFTER',
            event: 'DELETE',
            options: tableName,
        });
    }

    /**
     * Indicates whether the trigger uses a dedicated function (that should be created
     * when the trigger is created and dropped when the trigger is dropped).
     * Should return false if the trigger uses a common function (insert_table, ...)
     */
    // eslint-disable-next-line class-methods-use-this
    override get useDedicatedFunction(): boolean {
        return true;
    }

    /**
     * Returns the SQL command to execute to create the dedicated function that will be called by the trigger (if any)
     * Will return undefined when the trigger is using a common function (insert_table, ...).
     * Must be in sync with useDedicatedFunction.
     */
    override getSqlToCreateDedicatedFunction(tableDef: TableDefinition): string | undefined {
        const updateStatements = Object.entries(this.tablesByColumn).map(
            ([factoryTableName, columnName]) =>
                // Note : only raise an error with the first 5 referring ids
                `SELECT array_agg(_id) INTO referring_ids
FROM (SELECT _id FROM ${tableDef.schemaName}.${factoryTableName}
WHERE OLD._id = ANY(${columnName}) AND _tenant_id = OLD._tenant_id LIMIT 5);
            IF array_length(referring_ids, 1) IS NOT NULL THEN
                RAISE EXCEPTION 'Error: %.%(%) are referencing ${tableDef.tableName}(%)', '${factoryTableName}', '${columnName}', referring_ids, OLD._id;
            END IF;`,
        );

        return `CREATE OR REPLACE FUNCTION ${this.getFunctionFullname(tableDef)}()
            RETURNS TRIGGER
            AS
            $$
            DECLARE referring_ids bigint[];
            BEGIN
                ${updateStatements.join('\n')}
                RETURN OLD;
            END;
            $$
            LANGUAGE plpgsql;`;
    }
}
