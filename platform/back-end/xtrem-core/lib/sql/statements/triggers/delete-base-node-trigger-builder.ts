import { TableDefinition } from '@sage/xtrem-postgres';
import { TriggerBuilder } from './trigger-builder';

/**
 * @internal
 */
class DeleteBaseNodeTriggerBuilder extends TriggerBuilder {
    constructor() {
        super('base_delete', { when: 'AFTER', event: 'DELETE' });
    }

    /**
     * Indicates whether the trigger uses a dedicated function (that should be created
     * when the trigger is created and dropped when the trigger is dropped).
     * Should return false if the trigger uses a common function (insert_table, ...)
     */
    // eslint-disable-next-line class-methods-use-this
    override get useDedicatedFunction(): boolean {
        return true;
    }

    /**
     * Returns the SQL command to execute to create the dedicated function that will be called by the trigger (if any)
     * Will return undefined when the trigger is using a common function (insert_table, ...).
     * Must be in sync with useDedicatedFunction.
     */
    override getSqlToCreateDedicatedFunction(tableDef: TableDefinition): string | undefined {
        if (!tableDef.baseDefinition) throw new Error(`${tableDef.tableName} does not have a base table`);
        const baseName = tableDef.baseDefinition.tableName;
        return `CREATE OR REPLACE FUNCTION ${this.getFunctionFullname(tableDef)}()
            RETURNS TRIGGER
            AS
            $$
                BEGIN
                    EXECUTE 'DELETE FROM ${tableDef.schemaName}.${baseName} WHERE _id = '
                        || OLD._id${
                            tableDef.isSharedByAllTenants ? '' : " || ' AND _tenant_id = ''' || OLD._tenant_id || ''''"
                        };
                    RETURN OLD;
                END;
            $$
            LANGUAGE plpgsql;`;
    }
}

export const deleteBaseNodeTriggerBuilder = TriggerBuilder.registerBuilder(new DeleteBaseNodeTriggerBuilder());
