import { TableDefinition, TriggerDefinition, TriggerEventType, TriggerTimingType } from '@sage/xtrem-postgres';
import { Dict } from '@sage/xtrem-shared';
import { SqlContext } from '../../sql-context/sql-context';
import { makeName63 } from '../naming';

export interface TriggerBuilderParams {
    when: TriggerTimingType;
    event: TriggerEventType;
    options?: string;
    functionParametersProvider?: (tableDef: TableDefinition) => string;
}

/**
 * A trigger builder is used to manage triggers in database (create/drop).
 */
export class TriggerBuilder {
    private static _builders: Dict<TriggerBuilder> = {};

    protected readonly when: TriggerTimingType;

    protected readonly event: TriggerEventType;

    protected readonly options?: string;

    protected readonly functionParametersProvider?: (tableDef: TableDefinition) => string;

    protected constructor(
        protected readonly name: string,
        params: TriggerBuilderParams,
    ) {
        this.when = params.when;
        this.event = params.event;
        this.options = params.options;
        this.functionParametersProvider = params.functionParametersProvider;
    }

    private static _getKey(name: string, when: TriggerTimingType, event: TriggerEventType, options?: string): string {
        const parts = [name, when, event];
        if (options) parts.push(options);
        return parts.join('/');
    }

    public get key(): string {
        return TriggerBuilder._getKey(this.name, this.when, this.event, this.options);
    }

    /**
     * Create (and register) a table trigger (insert/update/delete base/...)
     */
    static createTableTriggerBuilder(name: string, params: TriggerBuilderParams): TriggerBuilder {
        return this.registerBuilder(new TriggerBuilder(name, params));
    }

    /**
     * Register a trigger builder
     */
    static registerBuilder(triggerBuilder: TriggerBuilder): TriggerBuilder {
        const key = triggerBuilder.key;
        const builder = TriggerBuilder._builders[key];
        if (builder) return builder;
        TriggerBuilder._builders[key] = triggerBuilder;
        return triggerBuilder;
    }

    /**
     * Get a trigger builder (already registered with registerBuilder() function)
     */
    static getBuilder(
        name: string,
        when: TriggerTimingType,
        event: TriggerEventType,
        options?: string,
    ): TriggerBuilder {
        const key = TriggerBuilder._getKey(name, when, event, options);
        let builder = TriggerBuilder._builders[key];
        if (!builder) {
            if (!/^notify_/.test(name)) {
                // Create a builder on the fly (this trigger will probably be used
                // to drop an obsolete trigger). Cannot be used for notification triggers
                builder = TriggerBuilder.createTableTriggerBuilder(name, {
                    when,
                    event,
                });
            } else {
                throw new Error(`No builder was registered for key : ${key}`);
            }
        }
        return builder;
    }

    /**
     * Returns the registered builder that can manage the provided trigger definition
     */
    static getBuilderForTrigger(trigger: TriggerDefinition): TriggerBuilder {
        return TriggerBuilder.getBuilder(trigger.name, trigger.when, trigger.event, trigger.options);
    }

    /**
     * Returns the SQL command to execute to create the trigger.
     */
    getSqlToCreateTrigger(tableDef: TableDefinition): string {
        return `DO $$ BEGIN
            CREATE TRIGGER ${this.name}
            ${this.when.toUpperCase()} ${this.event.toUpperCase()} ON ${SqlContext.getFullTableDefName(tableDef)}
            FOR EACH ROW
            EXECUTE PROCEDURE ${this.getFunctionFullname(tableDef)}(${this.getFunctionParameters(tableDef)});
            EXCEPTION
                WHEN others THEN null;
            END $$;`;
    }

    getFunctionParameters(tableDef: TableDefinition): string {
        return this.functionParametersProvider ? this.functionParametersProvider(tableDef) : '';
    }

    /**
     * Return the definition of the trigger
     */
    getDefinition(tableDef: TableDefinition): TriggerDefinition {
        return {
            name: this.name,
            event: this.event,
            when: this.when,
            options: this.options,
            functionName: this.getFunctionFullname(tableDef),
            functionParameters: this.getFunctionParameters(tableDef),
        };
    }

    /**
     * Returns the full name (including the schema name) of the function that will be executed by the trigger.
     */
    getFunctionFullname(tableDef: TableDefinition): string {
        if (this.useDedicatedFunction)
            return `${tableDef.schemaName}.${makeName63(`${tableDef.tableName}_${this.name}`)}`;
        return `${tableDef.schemaName}.${this.name}`;
    }

    /**
     * Indicates whether the trigger uses a dedicated function (that should be created
     * when the trigger is created and dropped when the trigger is dropped).
     * Should return false if the trigger uses a common function (insert_table, ...)
     */
    // eslint-disable-next-line class-methods-use-this
    get useDedicatedFunction(): boolean {
        return false;
    }

    /**
     * Returns the SQL command to execute to create the dedicated function that will be called by the trigger (if any)
     * Will return undefined when the trigger is using a common function (insert_table, ...).
     * Must be in sync with useDedicatedFunction.
     */
    // eslint-disable-next-line class-methods-use-this, @typescript-eslint/no-unused-vars
    getSqlToCreateDedicatedFunction(tableDef: TableDefinition): string | undefined {
        return undefined;
    }

    /**
     * The SQL command to drop the trigger and its dedicated function (if any)
     * @param tableDef
     * @returns
     */
    getSqlToDrop(tableDef: TableDefinition): string {
        let sql = `DROP TRIGGER IF EXISTS ${this.name} ON ${SqlContext.getFullTableDefName(tableDef)};`;

        if (this.useDedicatedFunction) {
            // Drop the dedicated function as well
            sql = `${sql}\nDROP FUNCTION IF EXISTS ${this.getFunctionFullname(tableDef)}();`;
        }
        return sql;
    }
}
