import { TableDefinition, TriggerEventType } from '@sage/xtrem-postgres';
import { Dict, pascalCase } from '@sage/xtrem-shared';
import { NotifyOperation } from '../../..';
import { TriggerBuilder } from './trigger-builder';

const eventTypeByNotificationType = {
    created: 'INSERT',
    updated: 'UPDATE',
    deleted: 'DELETE',
} as Dict<TriggerEventType>;

/**
 * A notification trigger builder is used to manage notification triggers in database (create/drop).
 *  @internal
 */
class NotifyTriggerBuilder extends TriggerBuilder {
    constructor(private readonly notificationType: NotifyOperation) {
        super(`notify_${notificationType}`, { when: 'AFTER', event: eventTypeByNotificationType[notificationType] });
    }

    /**
     * Indicates whether the trigger uses a dedicated function (that should be created
     * when the trigger is created and dropped when the trigger is dropped).
     * Should return false if the trigger uses a common function (insert_table, ...)
     */
    // eslint-disable-next-line class-methods-use-this
    override get useDedicatedFunction(): boolean {
        return true;
    }

    /**
     * Returns the SQL command to execute to create the dedicated function that will be called by the trigger (if any)
     * Will return undefined when the trigger is using a common function (insert_table, ...).
     * Must be in sync with useDedicatedFunction.
     */
    override getSqlToCreateDedicatedFunction(tableDef: TableDefinition): string | undefined {
        if (!(tableDef.notify?.event as any)?.[this.notificationType]) return undefined;

        if (tableDef.isSharedByAllTenants)
            throw new Error(`notification triggers are not supported on shared table ${tableDef.tableName}`);

        const factoryName = pascalCase(tableDef.tableName);
        const rowName = this.notificationType === 'deleted' ? 'OLD' : 'NEW';
        const payload = (tableDef.notify?.sqlPayloadExpression || '').replace(/\{\{ROW\}\}/g, rowName);

        // the trigger cannot get the source containerId but it does not matter because self is not excluded
        const envelope = {
            data: JSON.stringify({ topic: this.notificationType }),
        };

        return `CREATE OR REPLACE FUNCTION ${this.getFunctionFullname(tableDef)}()
        RETURNS TRIGGER
        AS
        $$
            DECLARE
                origin_id varchar;
                notification_id varchar;
                email varchar;
                login varchar;
                all_disabled varchar;
                tenant_disabled varchar;
            BEGIN
                SELECT COALESCE(${tableDef.schemaName}.get_config('xtrem.origin_id'), '') INTO origin_id;
                SELECT COALESCE(${tableDef.schemaName}.get_config('xtrem.notification.disable.ALL'), 'false') INTO all_disabled;
                SELECT COALESCE(${tableDef.schemaName}.get_config('xtrem.notification.disable.t_'||${rowName}._tenant_id), 'false') INTO tenant_disabled;
                SELECT COALESCE(${tableDef.schemaName}.get_config('xtrem.user_email'), '') INTO email;
                SELECT COALESCE(${tableDef.schemaName}.get_config('xtrem.login_email'), '') INTO login;

                IF (all_disabled <> 'true' and tenant_disabled <> 'true') THEN
                    SELECT ${tableDef.schemaName}.nanoid() INTO notification_id;
                    INSERT INTO ${tableDef.schemaName}.sys_notification
                        (tenant_id, origin_id, notification_id, reply_id, reply_topic, user_email, login, locale, topic, payload, status, _source_id, _update_tick, _create_stamp, _update_stamp)
                    VALUES (${rowName}._tenant_id, origin_id, notification_id, '', '', email, login, '', '${factoryName}/${
                        this.notificationType
                    }',
                        ${payload}, 'pending',
                        '', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
                    NOTIFY notification_queued, '${JSON.stringify(envelope)}';
                END IF;


                RETURN ${rowName};
            END;
        $$
        LANGUAGE plpgsql;`;
    }
}

TriggerBuilder.registerBuilder(new NotifyTriggerBuilder('created'));
TriggerBuilder.registerBuilder(new NotifyTriggerBuilder('updated'));
TriggerBuilder.registerBuilder(new NotifyTriggerBuilder('deleted'));

/**
 * Returns the notification trigger builder bound to a notification type
 * @internal
 */
export function getNotifyTriggerBuilder(notificationType: NotifyOperation): TriggerBuilder {
    return TriggerBuilder.getBuilder(
        `notify_${notificationType}`,
        'AFTER',
        eventTypeByNotificationType[notificationType],
    );
}
