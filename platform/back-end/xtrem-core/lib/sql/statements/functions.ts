import { CoreHooks } from '../../runtime/core-hooks';

export async function createFunctions(
    schemaName: string,
    createFromSql: (sql: string) => Promise<void>,
): Promise<void> {
    await createFromSql(`
${getSqlToCreateGetConfig(schemaName)}
${getSqlToCreateDropTriggers(schemaName)}
${getSqlToCreateDropNotifyFunctions(schemaName)}
${getSqlToCreateInsertTable(schemaName)}
${getSqlToCreateInsertSharedTable(schemaName)}
${getSqlToCreateUpdateTable(schemaName)}
${getSqlToCreateUpdateSharedTable(schemaName)}
${getSqlToSetSyncTick(schemaName)}
${getSqlToCreateSignedInt32(schemaName)}
${getSqlToCreateImul(schemaName)}
${getSqlToCreateZeroFillShift(schemaName)}
${getSqlToCreateNanoId(schemaName)}
${CoreHooks.auditManager.getTriggerFunctionsSql(schemaName)}
`);
}
/**
 * Gets the sql string to create the `get_config` function used to safely get a session setting without raising an error.
 * The created function returns an empty string if the setting does not exist in the current session transaction.
 * @param schemaName
 * @returns sql string
 */
const getSqlToCreateGetConfig = (schemaName: string): string => `
CREATE OR REPLACE FUNCTION ${schemaName}.get_config(setting_name varchar)
RETURNS varchar AS
$$
declare
    setting_value varchar;
BEGIN
    SELECT current_setting(setting_name) into setting_value;
    RETURN setting_value;
EXCEPTION
    WHEN OTHERS THEN
    RETURN NULL;
END;
$$
LANGUAGE plpgsql;
`;

/**
 * Trigger invoked when a record is inserted into a non-shared table (isSharedByAllTenants = false)
 * @param schemaName
 * @returns sql string
 */
const getSqlToCreateInsertTable = (schemaName: string): string => `
CREATE OR REPLACE FUNCTION ${schemaName}.insert_table()
RETURNS TRIGGER
AS
$$
    DECLARE
        USER_ID VARCHAR;
    BEGIN
        SELECT COALESCE(${schemaName}.get_config('xtrem.transaction_user_id'), '') INTO USER_ID;
        IF (USER_ID <> '') THEN
            NEW._create_user :=  CAST(USER_ID AS INT8);
            NEW._update_user :=  CAST(USER_ID AS INT8);
        END IF;
        IF (NEW._create_stamp IS NULL) THEN
            NEW._create_stamp := NOW();
        END IF;
        IF (NEW._update_stamp IS NULL) THEN
            NEW._update_stamp := NOW();
        END IF;
        IF (NEW._update_tick IS NULL) THEN
            NEW._update_tick := 1;
        END IF;

        RETURN NEW;
    END;
$$
LANGUAGE plpgsql;
`;

/**
 * Trigger invoked when a record is inserted into a shared table (isSharedByAllTenants = true)
 * @param schemaName
 * @returns sql string
 */
const getSqlToCreateInsertSharedTable = (schemaName: string): string => `
CREATE OR REPLACE FUNCTION ${schemaName}.insert_shared_table()
RETURNS TRIGGER
AS
$$
    BEGIN
        IF (NEW._create_stamp IS NULL) THEN
            NEW._create_stamp := NOW();
        END IF;
        IF (NEW._update_stamp IS NULL) THEN
            NEW._update_stamp := NOW();
        END IF;
        IF (NEW._update_tick IS NULL) THEN
            NEW._update_tick := 1;
        END IF;

        RETURN NEW;
    END;
$$
LANGUAGE plpgsql;
`;

/**
 * Trigger invoked when a record is updated in a non-shared table (isSharedByAllTenants = false)
 * @param schemaName
 * @returns sql string
 */
const getSqlToCreateUpdateTable = (schemaName: string): string => `
CREATE OR REPLACE FUNCTION ${schemaName}.update_table()
RETURNS TRIGGER
AS
$$
    DECLARE
        USER_ID VARCHAR;
    BEGIN
        SELECT COALESCE(${schemaName}.get_config('xtrem.transaction_user_id'), '') INTO USER_ID;
        IF (USER_ID <> '') THEN
            NEW._update_user :=  CAST(USER_ID AS INT8);
        END IF;
        NEW._update_stamp := NOW();
        NEW._update_tick := OLD._update_tick + 1;

        RETURN NEW;
    END;
$$
LANGUAGE plpgsql;
`;

/**
 * Trigger invoked when a record is updated in a shared table (isSharedByAllTenants = true)
 * @param schemaName
 * @returns sql string
 */
const getSqlToCreateUpdateSharedTable = (schemaName: string): string => `
CREATE OR REPLACE FUNCTION ${schemaName}.update_shared_table()
RETURNS TRIGGER
AS
$$
    BEGIN
        NEW._update_stamp := NOW();
        NEW._update_tick := OLD._update_tick + 1;

        RETURN NEW;
    END;
$$
LANGUAGE plpgsql;
`;

/**
 * Trigger invoked when a record is inserted or updated into a synchronization source table
 * @param schemaName
 * @returns sql string
 */
const getSqlToSetSyncTick = (schemaName: string): string => `
CREATE OR REPLACE FUNCTION ${schemaName}.set_sync_tick()
RETURNS TRIGGER
AS
$$
    BEGIN
        NEW._sync_tick :=  pg_current_xact_id();
        RETURN NEW;
    END;
$$
LANGUAGE plpgsql;
`;

/**
 * Gets the sql string to create the `drop_triggers` function used to drop all triggers, or all per table
 * @param schemaName
 * @returns sql string
 */
const getSqlToCreateDropTriggers = (schemaName: string): string => `
CREATE OR REPLACE FUNCTION ${schemaName}.drop_triggers(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS
$$
DECLARE
    triggerRecord RECORD;
    record_count integer;
begin
	record_count = 0;
    FOR triggerRecord IN
    	SELECT trigger_name, event_object_table
    	FROM information_schema.triggers
    	WHERE trigger_schema = schema_name AND (name_filter = '' OR event_object_table = name_filter)
	LOOP
		record_count = record_count + 1;
        EXECUTE 'DROP TRIGGER ' || triggerRecord.trigger_name || ' ON ' || schema_name || '."' || triggerRecord.event_object_table || '";';
    END LOOP;

    RETURN record_count;
END;
$$ LANGUAGE plpgsql;
`;

/**
 * Gets the sql string to create the `drop_notify_functions` function used to drop all notify functions per table
 * @param schemaName
 * @returns sql string
 */

const getSqlToCreateDropNotifyFunctions = (schemaName: string): string => `
CREATE OR REPLACE FUNCTION ${schemaName}.drop_notify_functions(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS
$$
DECLARE
    triggerRecord RECORD;
    record_count integer;
BEGIN
    record_count = 0;
    FOR triggerRecord IN
    SELECT routine_name
    FROM information_schema.routines
    WHERE specific_schema = schema_name and routine_name IN (name_filter || '_notify_deleted', name_filter || '_notify_created', name_filter || '_notify_updated')
    LOOP
        record_count = record_count + 1;
        EXECUTE 'DROP FUNCTION ' || schema_name || '.' || triggerRecord.routine_name || ';';
    END LOOP;

    RETURN record_count;
END;
$$ LANGUAGE plpgsql;
`;

/**
 * Function to return an integer as a 32 bit signed int, with the leftmost bit representing the sign (1 = negative)
 * @param schemaName
 * @returns sql string
 */
const getSqlToCreateSignedInt32 = (schemaName: string): string => `
CREATE OR REPLACE FUNCTION ${schemaName}.signedInt32(a bigint)
RETURNS bigint AS
$$
DECLARE
BEGIN
	-- Convert to 32 bit signed (if leftmost bit is 1, it's a negative number)
  	IF (a > 2^31) THEN
    	RETURN a - (2^32)::bigint;
  	END IF;
  	RETURN a;
END;
$$
LANGUAGE plpgsql;
`;

/**
 * Function to do 32 bit signed integer multiplication
 * @param schemaName
 * @returns sql string
 */
const getSqlToCreateImul = (schemaName: string): string => `
CREATE OR REPLACE FUNCTION ${schemaName}.imul(a bigint, b bigint)
RETURNS bigint AS
$$
DECLARE
    aHi bigint;
    aLo bigint;
    bHi bigint;
    bLo bigint;
    res bigint;
BEGIN
    aHi = ${schemaName}.zeroFillShift(a, 16) & 65535;
    aLo = a & 65535;
    bHi = ${schemaName}.zeroFillShift(b, 16) & 65535;
    bLo = b & 65535;
    res = ((aLo * bLo) + ${schemaName}.zeroFillShift(((aHi * bLo + aLo * bHi) << 16) % (2^32)::bigint, 0)) | 0;
    RETURN ${schemaName}.signedInt32(res);
END;
$$
LANGUAGE plpgsql;
`;

/**
 * Bitwise right shift with zero fill
 * @param schemaName
 * @returns sql string
 */
const getSqlToCreateZeroFillShift = (schemaName: string): string => `
CREATE OR REPLACE FUNCTION ${schemaName}.zeroFillShift(a bigint, b int)
RETURNS bigint AS
$$
DECLARE
  	res bigint;
BEGIN
	IF (a < 0) THEN
		res = a + 2^32;
	ELSE
		res = a;
	END IF;
	res = res >> b;
	RETURN res;
END;
$$
LANGUAGE plpgsql;
`;

/**
 * Generate nanoid.
 * https://gist.github.com/coreybutler/e4034d18ffeb8d73f0ac1583939dd013
 * @param schemaName
 * @returns sql text
 */
const getSqlToCreateNanoId = (schemaName: string): string => `
CREATE OR REPLACE FUNCTION ${schemaName}.nanoid("size" int4 DEFAULT 21)
    RETURNS text
    LANGUAGE plpgsql
    STABLE
    AS
    $$
    DECLARE
        id text := '';
        i int := 0;
        urlAlphabet char(64) := 'ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW';
        bytes bytea;
        byte int;
        pos int;
    BEGIN
        SELECT gen_random_bytes(size) INTO bytes;
    WHILE i < size LOOP
        byte := get_byte(bytes, i);
        pos := (byte & 63) + 1; -- + 1 because substr starts at 1
        id := id || substr(urlAlphabet, pos, 1);
        i = i + 1;
    END LOOP;
    RETURN id;
    END
    $$
    ;
 `;
