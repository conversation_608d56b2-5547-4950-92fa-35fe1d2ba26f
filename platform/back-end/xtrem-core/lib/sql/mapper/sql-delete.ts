import { AnyRecord } from '@sage/xtrem-async-helper';
import { CustomMetrics } from '@sage/xtrem-metrics';
import { Context, isScalar, NodeFactory, tenantCondition } from '../../runtime';
import { Table } from '../schema/table';
import { SqlConverter } from './sql-converter';

/** @internal */
export class SqlDelete {
    constructor(private readonly table: Table) {}

    get factory(): NodeFactory {
        return this.table.factory;
    }

    async delete(context: Context, filter: AnyRecord): Promise<number> {
        if (this.factory.isAbstract) {
            throw new Error(`${this.table.name} : Deletions can't be done on abstract factories`);
        }

        context.sqlSpy.incrementCounter(this.factory, 'DELETE');
        const sqlConverter = new SqlConverter(context, this.factory);
        // if we have a filter we need to deal with base table because the filter can contain a property
        // from one of the the base tables, otherwise we rely on 'delete-base' triggers
        if (this.table.baseTable && Object.keys(filter).length > 0) {
            // Note : we are on the 'concrete' table.
            const deleteFromTables = async (table: Table, whereClause: string): Promise<number> => {
                const sqlDelete = `DELETE FROM ${table.getFullTableName(context)} WHERE ${tenantCondition(
                    sqlConverter,
                    this.table,
                    '',
                    whereClause ? [whereClause] : [],
                )}`;

                // we don't care about the delete in the base table, the 'delete-base' trigger will do it
                const paramValues = await SqlConverter.getParameterValues(context, sqlConverter.sqlParameters, {
                    where: filter,
                });
                return (
                    await CustomMetrics.sql.withMetrics({ nodeName: this.factory.name, statementKind: 'delete' }, () =>
                        context.executeSql<{ updateCount: number }>(sqlDelete, paramValues),
                    )
                ).updateCount;
            };

            if (filter._id && isScalar(filter._id)) {
                // We already have the (single) _id we want to delete
                return deleteFromTables(this.table, `_id = ${filter._id}`);
            }
            // We don't have the _id(s) to delete, we have to :
            // - retrieve the '_id' of all the lines to be deleted base on the filter
            // - delete these _ids from the current table only, the trigger will delete the base table records
            const where = sqlConverter.convertWhere(filter);
            const outputColumns = sqlConverter.convertOutputPaths([{ path: ['_id'] }]);
            const outputSqls = outputColumns.map(r => r.sql);
            const aliasedTables = sqlConverter.getTableAliases();
            const filterClause = tenantCondition(sqlConverter, this.table, 't0', [where]);
            return deleteFromTables(
                this.table,
                `_id IN (SELECT ${outputSqls} FROM ${aliasedTables} WHERE ${filterClause})`,
            );
        }
        // "Classic" delete
        const whereSql = sqlConverter.convertWhere(filter);
        const sql = `DELETE FROM ${this.table.getFullTableName(context)} t0 WHERE ${tenantCondition(
            sqlConverter,
            this.table,
            '',
            whereSql ? [whereSql] : [],
        )}`;
        const parameterValues = await SqlConverter.getParameterValues(context, sqlConverter.sqlParameters, {
            where: filter,
        });
        const result = await CustomMetrics.sql.withMetrics(
            { nodeName: this.factory.name, statementKind: 'delete' },
            () => context.executeSql<{ updateCount: number }>(sql, parameterValues),
        );
        // TODO: delete localization entries matching the where clause. -- use RETURNING/OUTPUT clause
        return result.updateCount;
    }

    async deleteAll(context: Context): Promise<void> {
        await this.delete(context, {});
    }
}
