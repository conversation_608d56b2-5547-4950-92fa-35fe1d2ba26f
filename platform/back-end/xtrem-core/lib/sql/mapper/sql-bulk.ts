import { CustomMetrics } from '@sage/xtrem-metrics';
import { StaticThis } from '../../decorators/decorator-utils';
import { BulkDeleteOptions, BulkUpdateOptions, Context } from '../../runtime/context';
import { Node } from '../../ts-api/node';
import { SqlConverter } from './sql-converter';

/** @internal */
export abstract class SqlBulk {
    /**
     * Runs a bulk update. The options.set and options.where must be simple enough to be parsed into sql statement.
     * If not, an error will be raised
     * @param nodeConstructor
     * @param options
     */
    static async update<This extends Node>(
        nodeConstructor: StaticThis<This>,
        context: Context,
        options: BulkUpdateOptions<This>,
    ): Promise<number> {
        const factory = context.application.getFactoryByConstructor(nodeConstructor);
        const sqlConverter = new SqlConverter(context, factory);
        const updateSql = sqlConverter.getUpdateSetCommand(options);
        const parameterValues = await SqlConverter.getParameterValues(context, sqlConverter.sqlParameters, {
            values: options.set,
            where: options.where,
        });
        const updateCount = (
            await CustomMetrics.sql.withMetrics({ nodeName: nodeConstructor.name, statementKind: 'update' }, () =>
                context.executeSql<{ updateCount: number }>(updateSql, parameterValues),
            )
        ).updateCount;
        if (updateCount) await factory.cache.invalidate(context);
        return updateCount;
    }

    /**
     * Runs a bulk update. The options.set and options.where must be simple enough to be parsed into sql statement.
     * If not, an error will be raised
     * @param nodeConstructor
     * @param options
     */
    static async delete<This extends Node>(
        nodeConstructor: StaticThis<This>,
        context: Context,
        options: BulkDeleteOptions<This>,
    ): Promise<number> {
        const sqlConverter = new SqlConverter(context, context.application.getFactoryByConstructor(nodeConstructor));
        const updateSql = sqlConverter.getDeleteFromCommand(options);
        const parameterValues = await SqlConverter.getParameterValues(context, sqlConverter.sqlParameters, {
            where: options.where,
        });
        return (
            await CustomMetrics.sql.withMetrics({ nodeName: nodeConstructor.name, statementKind: 'delete' }, () =>
                context.executeSql<{ updateCount: number }>(updateSql, parameterValues),
            )
        ).updateCount;
    }
}
