import { AnyValue, AsyncResponse } from '@sage/xtrem-async-helper';
import { DateRange, DateValue, Datetime, DatetimeRange, DecimalRange, IntegerRange, Time } from '@sage/xtrem-date-time';
import { Decimal } from '@sage/xtrem-decimal';
import { ConnectionPool } from '@sage/xtrem-postgres';
import { LogicError } from '@sage/xtrem-shared';
import * as assert from 'assert';
import * as _ from 'lodash';
import { parse as parsePgArray } from 'postgres-array';
import { TypeName } from '../../decorators/decorator-utils';
import { Property, ReferenceProperty } from '../../properties';
import { Context } from '../../runtime';
import { integer } from '../../ts-api';
import { BinaryStream, TextStream, Uuid } from '../../types';
import { Column } from '../schema/column';
import { OutputColumn } from './sql-converter';

export type TransformationForSqlValue = TypeName | 'encryptedString';

function fromSqlTypeError(outputColumn: OutputColumn, val: AnyValue): Error {
    const property = outputColumn.property as Property;
    return property.systemError(
        `invalid ${property.type} value: '${val}' of type ${typeof val}, op=${outputColumn.aggregationOperator}`,
    );
}

function toSqlTypeError(column: Column, val: AnyValue): Error {
    return column.property.inputError(`invalid ${column.type} value: '${val}' of type ${typeof val}`);
}

export class SqlValueConverter {
    // API is a bit asymetrical because we are passing a `OutputColumn` here rather than a `Column`
    // This is because we have to convert aggregated values which may have a different type than the column
    // they are based on. For example distinctCount is always an integer, regardless of the column type.
    // `outputColumn.type` is the type of the aggregate value, and `column.type` the column type.
    static async fromSql(context: Context, outputColumn: OutputColumn, val: AnyValue): Promise<AnyValue> {
        const property = outputColumn.property as Property;
        if (val == null) {
            // Postgres driver should never return undefined
            if (val === undefined) throw property.systemError(`invalid SQL value: ${val}`);
            // If the property is not nullable we may still receive a null, because of a join or a missing
            // entry in _customData
            // Don't call typeDefaultValue here, just return null
            return val;
        }

        switch (outputColumn.type) {
            case 'string':
                if (
                    outputColumn.property?.isLocalized &&
                    typeof val === 'object' &&
                    context.processLocalizedTextAsJson
                ) {
                    return JSON.stringify(val);
                }
                if (typeof val !== 'string') throw fromSqlTypeError(outputColumn, val);
                /** encrypt property value */
                if (property.isStringProperty() && property.isStoredEncrypted) {
                    return context.vault.recordValue(val);
                }
                return val;
            case 'boolean':
                if (typeof val !== 'boolean') throw fromSqlTypeError(outputColumn, val);
                return val;
            case 'enum':
                if (typeof val !== 'string') throw fromSqlTypeError(outputColumn, val);
                return val;
            case 'short':
            case 'integer':
                if (!_.isInteger(val)) {
                    // TODO: Hack to upgrade user columns from string to integer. REMOVE later
                    if (outputColumn.property && ['_createUser', '_updateUser'].includes(outputColumn.property.name))
                        return null;
                    throw fromSqlTypeError(outputColumn, val);
                }
                return val;
            case 'float':
            case 'double':
                if (typeof val !== 'number') throw fromSqlTypeError(outputColumn, val);
                return val;
            case 'decimal':
                if (typeof val !== 'string' && typeof val !== 'number') throw fromSqlTypeError(outputColumn, val);
                return Decimal.make(val);

            case 'date':
                if (typeof val !== 'string') throw fromSqlTypeError(outputColumn, val);
                return DateValue.parse(val);
            case 'time':
                if (typeof val !== 'string') throw fromSqlTypeError(outputColumn, val);
                return Time.parse(val);
            case 'datetime':
                if (typeof val !== 'string' && !(val instanceof Date)) throw fromSqlTypeError(outputColumn, val);
                if (typeof val === 'string') return Datetime.parse(val);
                return Datetime.fromJsDate(val);

            case 'reference':
                assert(property.isReferenceProperty());
                return context.convertReference(property.targetFactory, val as integer);

            case 'integerRange':
                if (typeof val !== 'string') throw fromSqlTypeError(outputColumn, val);
                return IntegerRange.parse(val);
            case 'enumArray': {
                if (typeof val !== 'string') throw fromSqlTypeError(outputColumn, val);
                const enumArray = parsePgArray(val, v => v);

                if (!Array.isArray(enumArray)) throw fromSqlTypeError(outputColumn, val);
                return Object.freeze(enumArray);
            }
            case 'integerArray':
            case 'referenceArray': {
                if (!Array.isArray(val)) throw fromSqlTypeError(outputColumn, val);
                return Object.freeze(val.map(n => Number(n)));
            }
            case 'stringArray': {
                if (!Array.isArray(val)) throw fromSqlTypeError(outputColumn, val);
                return Object.freeze(val);
            }
            case 'decimalRange':
                if (typeof val !== 'string') throw fromSqlTypeError(outputColumn, val);
                return DecimalRange.parse(val);
            case 'dateRange':
                if (typeof val !== 'string') throw fromSqlTypeError(outputColumn, val);
                return DateRange.parse(val);
            case 'datetimeRange':
                if (typeof val !== 'string') throw fromSqlTypeError(outputColumn, val);
                return DatetimeRange.parse(val);

            case 'uuid':
                if (typeof val !== 'string') throw fromSqlTypeError(outputColumn, val);
                return Uuid.fromString(val);
            case 'binaryStream': {
                const bytes = await ConnectionPool.readBlob(val);
                if (bytes && bytes.length > 0) return BinaryStream.fromBuffer(bytes);
                return property.getTypeDefaultValue();
            }
            case 'textStream': {
                const str = await ConnectionPool.readClob(val);
                if (str && str.length > 0) return TextStream.fromString(str);
                return property.getTypeDefaultValue();
            }
            case 'json':
                return val;

            default:
                // TODO: reenable throw
                // throw new Error(`invalid type: ${outputColumn.type}`);
                return val;
        }
    }

    /**
     * Performs a check on a value
     */
    private static _checkValue(val: AnyValue, checkFunction: (v: AnyValue) => boolean, column?: Column): void {
        if (column == null) return;
        if (!checkFunction(val)) throw toSqlTypeError(column, val);
    }

    /**
     * Ensures a value has the right type
     * @param val
     * @param expectedType
     * @param column
     */
    private static _checkValueType(val: AnyValue, expectedType: string, column?: Column): void {
        this._checkValue(val, v => typeof v === expectedType, column);
    }

    /**
     * Ensures a value has the right type
     * @param val
     * @param expectedType
     * @param column
     */
    private static _checkValueIsInstanceOf(val: AnyValue, expectedType: any, column?: Column): void {
        this._checkValue(val, v => v instanceof expectedType, column);
    }

    /**
     * Convert a value to its SQL representation
     */
    static toSql(context: Context, column: Column, value: AnyValue): AsyncResponse<AnyValue> {
        const transformation = SqlValueConverter.getSqlTransformation(context, column);
        return SqlValueConverter.applyTransformationToValue(context, value, transformation, column);
    }

    static parseIntegerColumnValue(context: Context, column: Column, val: AnyValue): AsyncResponse<number | null> {
        if (column.property.isReferenceProperty())
            return column.property.targetFactory.resolveReferenceId(context, val as string | number);
        if (column.property.name === '_id')
            return column.property.factory.resolveReferenceId(context, val as string | number);

        const intVal = parseInt(String(val), 10);
        if (Number.isNaN(intVal))
            throw column.property.inputError(`invalid integer value: '${val}' of type ${typeof val}`);
        return intVal;
    }

    /**
     * Applies a TransformationForSqlValue to a value
     * @param context
     * @param value the value to transform
     * @param transformation the transformation to apply
     * @param column the (optional) column that will be used to check the consistency of the value
     * @returns
     */
    static applyTransformationToValue(
        context: Context,
        value: AnyValue,
        transformation: TransformationForSqlValue,
        column?: Column,
    ): AsyncResponse<AnyValue> {
        const fixValue = (): AnyValue => {
            if (value != null) return value;
            if (column) {
                if (column.columnName === '_tenant_id') return context.tenantId;
                return column.property.getTypeDefaultValue();
            }
            return null;
        };

        const val = fixValue();
        if (val == null) return null;
        switch (transformation) {
            case null:
                return null;
            case 'encryptedString':
                SqlValueConverter._checkValueType(val, 'string', column);
                return context.vault.getValueFromVault(val as string);
            case 'string':
                SqlValueConverter._checkValueType(val, 'string', column);
                return val;
            case 'boolean':
                SqlValueConverter._checkValueType(val, 'boolean', column);
                return val;
            case 'enum':
                SqlValueConverter._checkValueType(val, 'string', column);
                return val as string;
            case 'integer':
                if (typeof val !== 'number' && column) return this.parseIntegerColumnValue(context, column, val);
                SqlValueConverter._checkValueType(val, 'number', column);
                return val;
            case 'short':
            case 'float':
            case 'double':
                SqlValueConverter._checkValueType(val, 'number', column);
                return val;
            case 'decimal':
                if (typeof val === 'string') return val;
                if (typeof val !== 'number' && !Decimal.isDecimal(val)) {
                    if (column) throw toSqlTypeError(column, val);
                    throw new Error(`Invalid decimal value ${val}, type = ${typeof val}`);
                }
                return val.toString();
            case 'date':
                if (typeof val === 'string') return val;
                SqlValueConverter._checkValueIsInstanceOf(val, DateValue, column);
                // No need to convert to JS Date here - string is faster and won't carry any timezone.
                return val.toString();
            case 'time':
                SqlValueConverter._checkValueIsInstanceOf(val, Time, column);
                return val.toString();
            case 'datetime':
                SqlValueConverter._checkValueIsInstanceOf(val, Datetime, column);
                // No need to convert to JS Date here - string is faster and will contain the 'Z' timezone suffix.
                return val.toString();

            case 'enumArray':
            case 'integerArray':
            case 'stringArray': {
                SqlValueConverter._checkValue(val, Array.isArray, column);
                return val;
            }
            case 'referenceArray': {
                SqlValueConverter._checkValue(val, Array.isArray, column);
                const arrayVal = val as Array<any>;
                return Promise.all(
                    arrayVal.map(v =>
                        (column?.property as ReferenceProperty).targetFactory.resolveReferenceId(context, v?._id || v),
                    ),
                );
            }

            case 'integerRange':
                if (typeof val === 'string') return val;
                SqlValueConverter._checkValueIsInstanceOf(val, IntegerRange, column);
                return val.toString();
            case 'decimalRange':
                if (typeof val === 'string') return val;
                SqlValueConverter._checkValueIsInstanceOf(val, DecimalRange, column);
                return val.toString();
            case 'dateRange':
                if (typeof val === 'string') return val;
                SqlValueConverter._checkValueIsInstanceOf(val, DateRange, column);
                return val.toString();
            case 'datetimeRange':
                if (typeof val === 'string') return val;
                SqlValueConverter._checkValueIsInstanceOf(val, DatetimeRange, column);
                return val.toString();

            case 'uuid':
                SqlValueConverter._checkValueIsInstanceOf(val, Uuid, column);
                return val.toString();

            case 'binaryStream': {
                SqlValueConverter._checkValueIsInstanceOf(val, BinaryStream, column);
                const binaryValue = val as BinaryStream;
                return binaryValue.value ? binaryValue.value : '';
            }
            case 'textStream': {
                SqlValueConverter._checkValueIsInstanceOf(val, TextStream, column);
                const textValue = val as TextStream;
                return textValue.value ? textValue.value : '';
            }
            case 'json':
                // Array mapping issue https://github.com/brianc/node-postgres/issues/442
                return Array.isArray(val) ? JSON.stringify(val) : val;

            default:
                throw new LogicError(`invalid SQL transformation : ${transformation}`);
        }
    }

    /**
     * Returns the TransformationForSqlValue to apply to a value
     */
    static getSqlTransformation(context: Context, column: Column): TransformationForSqlValue {
        switch (column.type) {
            case 'string':
                /** encrypt property value */
                if (!context.inCsvLoading && column.property.isStringProperty() && column.property.isStoredEncrypted) {
                    return 'encryptedString';
                }
                return 'string';
            case 'boolean':
            case 'enum':
            case 'short':
            case 'integer':
            case 'float':
            case 'double':
            case 'decimal':
            case 'date':
            case 'time':
            case 'datetime':
            case 'integerRange':
            case 'enumArray':
            case 'integerArray':
            case 'referenceArray':
            case 'stringArray':
            case 'decimalRange':
            case 'dateRange':
            case 'datetimeRange':
            case 'uuid':
            case 'binaryStream':
            case 'textStream':
            case 'json':
                return column.type;

            default:
                throw new LogicError(`invalid column type: ${column.type}`);
        }
    }
}
