import { AsyncReader } from '@sage/xtrem-async-helper';
import { Context, NodeFactory } from '../../runtime';
import { Node } from '../../ts-api';
import { NodeSelectOptions, NodeSelectResult, NodeSelector } from '../../ts-api/node-select-types';

export class SqlSelect {
    static async select(
        context: Context,
        factory: NodeFactory,
        selector: NodeSelector<Node>,
        options: NodeSelectOptions<Node>,
    ): Promise<NodeSelectResult<Node, NodeSelector<Node>>[]> {
        return (await this.getSelectReader(context, factory, selector, options)).readAll();
    }

    static async getSelectReader(
        context: Context,
        factory: NodeFactory,
        selector: NodeSelector<Node>,
        options: NodeSelectOptions<Node>,
    ): Promise<AsyncReader<NodeSelectResult<Node, NodeSelector<Node>>>> {
        const query = await factory.createNodeQuery(context, options, selector);

        return query.getSelectReader();
    }
}
