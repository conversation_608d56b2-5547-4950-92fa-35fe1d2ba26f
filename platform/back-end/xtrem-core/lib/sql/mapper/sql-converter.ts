import { AnyR<PERSON>ord } from '@sage/xtrem-async-helper';
import { dbCommandMarker } from '@sage/xtrem-postgres';
import { AnyValue, AsyncResponse, DataInputError, Dict, LogicError } from '@sage/xtrem-shared';
import {
    CastOptions,
    ConversionResultType,
    Converter,
    GenericConversionResult,
    Walker,
    get<PERSON><PERSON>umn<PERSON><PERSON>,
    quote,
    setLogger as setSqlConverterLogger,
} from '@sage/xtrem-ts-to-sql';
import * as _ from 'lodash';
import { Column, OutputPath, SqlContext } from '..';
import { ConfigManager } from '../..';
import { Property } from '../../properties';
import { BulkDeleteOptions, BulkUpdateOptions, Context, ContextGetConfigurationKeyType } from '../../runtime/context';
import { loggers } from '../../runtime/loggers';
import { NodeFactory } from '../../runtime/node-factory';
import { SystemProperties } from '../../runtime/system-properties';
import { isCompound } from '../../runtime/utils';
import { Aggregate, AggregateGroup, AnyFilterObject, JoinLiteralValue, Node } from '../../ts-api';
import { arrayTypes } from '../../types/arrays';
import { OrderByClause } from './sql-query';
import { SqlResolver } from './sql-resolver';
import { SqlValueConverter } from './sql-value-converter';

export type ConversionResult = GenericConversionResult<NodeFactory, Property>;

/**
 * OutputColumn is a conversion result for an element which is returned by a SQL query.
 * It may be an element of the select list, or a group or value returned by an aggregate query.
 */
export interface OutputColumn extends ConversionResult {
    /** Path (array of property names) in the result payload */
    payloadPath?: string[];
    /** Output columns of this column's value (only if this is a collection output column) */
    subColumns?: OutputColumn[];
}

export interface AggregateResult {
    groups?: OutputColumn[];
    values?: OutputColumn[];
}

/**
 * Describes a SQL statement parameter
 */
export interface SqlParameter {
    /** The path to the parameter's value */
    valuePath: string;

    /** The type of the parameter's value */
    type: ConversionResultType | 'encryptedString';

    /** Optional factory name, if the value is a reference */
    factoryName?: string;

    /** The column, if the value is a column value */
    column?: Column;

    /** Sub-path for composite value like regex */
    subPath?: string;

    /** Is the value an array ? */
    isArray?: boolean;

    /** Value, only for literals */
    literalValue?: AnyValue;
}

type Op = (slot: ConversionResult, v1: string, v2?: string) => string;

setSqlConverterLogger(loggers.sqlMapper);

function cast(sql: string | undefined, options: CastOptions<Property>): string {
    if (!sql) throw new LogicError('cannot cast: missing sql');
    const { type, property } = options;
    if (!type) return sql;
    const addCast = (sqlType: string): string =>
        sql.toLowerCase().endsWith(`::${sqlType.toLowerCase()}`) ? sql : `${sql}::${sqlType}`;
    switch (type) {
        case 'integerRange':
            return addCast('INT8');
        case 'decimalRange':
            return addCast('DECIMAL');
        case 'date':
        case 'dateRange':
            return addCast('DATE');
        case 'datetime':
        case 'datetimeRange':
            return addCast('TIMESTAMPTZ(3)');
        case 'enum': {
            if (!property?.isEnumProperty()) throw new Error('cannot cast enum: invalid property');
            const schemaName = property.factory.application.schemaName;
            const typeName = property.dataType.getEnumType().name;
            return addCast(`${schemaName}.${typeName}`);
        }
        default:
            return sql;
    }
}

const orIsNull = (slot: ConversionResult, v: string | undefined): string =>
    slot.isNullable ? `OR (${v} IS NULL)` : '';

const andIsNotNull = (slot: ConversionResult, v: string | undefined): string =>
    slot.isNullable ? `AND (${v} IS NOT NULL)` : '';

const collate = (slot: ConversionResult): string | undefined => {
    return slot.property?.isLocalized ? `COLLATE "${slot.collation}"` : '';
};

const binaryOps: Dict<Op> = {
    _eq: (slot, v1, v2) => `(${v1} ${collate(slot)} = ${v2} ${andIsNotNull(slot, v1)})`,
    _ne: (slot, v1, v2) => `(${v1} ${collate(slot)} != ${v2} ${orIsNull(slot, v1)})`,
    _gt: (slot, v1, v2) => `(${v1} ${collate(slot)} > ${v2} ${andIsNotNull(slot, v1)})`,
    _gte: (slot, v1, v2) => `(${v1} ${collate(slot)} >= ${v2} ${andIsNotNull(slot, v1)})`,
    _lt: (slot, v1, v2) => `(${v1} ${collate(slot)} < ${v2} ${orIsNull(slot, v1)})`,
    _lte: (slot, v1, v2) => `(${v1} ${collate(slot)} <= ${v2} ${orIsNull(slot, v1)})`,
    _contains: (slot, v1, v2) => {
        switch (slot.property?.type) {
            case 'integerArray':
            case 'enumArray':
            case 'referenceArray':
            case 'stringArray':
                return `(${v1} = ANY (${v2}))`;
            case 'json':
                return `(${v1} ? ${v2})`;
            default:
                return `(${v1} @> ${cast(v2, slot)})`;
        }
    },
    _containsRange: (_slot, v1, v2) => {
        return `(${v1} @> ${v2})`;
    },
    _containedBy: (_slot, v1, v2) => `(${v1} <@ ${v2})`,
};

function isFilterObject(val: AnyValue): val is AnyFilterObject {
    return !!val && isCompound(val);
}

function logWrongType(type: string, name: string, val: any): void {
    let valType = typeof val;
    if (valType === type) return;

    if (valType === 'object') {
        valType = val == null ? 'null' : val.constructor.name;
    }

    loggers.sql.warn(`'${name}': expected a 'string', got type='${valType}', value=${JSON.stringify(val)}`);
}

export interface SqlConverterOptions {
    quiet?: boolean;
}

/** @internal */
export class SqlConverter extends Converter<Context, NodeFactory, Property> {
    readonly sqlParameters: SqlParameter[] = [];

    #literals: Dict<string> = {};

    constructor(
        override readonly context: Context,
        public override rootFactory: NodeFactory,
        options?: SqlConverterOptions,
    ) {
        super(
            context,
            rootFactory,
            {
                resolveColumnName: (cx, parent, propertyName) =>
                    SqlResolver.resolve(this, cx, parent, propertyName, rootFactory),
                resolveTableName: factory => `${context.schemaName}.${factory.tableName || _.snakeCase(factory.name)}`,
                resolveLiteral: value => this.resolveLiteral(value),
            },
            {
                dialect: 'postgres',
                maxSubQueryDepth: ConfigManager.current.storage?.sql?.maxSubQueryDepth,
                ...options,
            },
        );
    }

    override convertTemplateElementString(str: string): string {
        return `${this.resolveLiteral(str)}::TEXT`;
    }

    addSqlParameter(parameter: SqlParameter): string {
        const sql = SqlContext.param(this.sqlParameters.length);
        this.sqlParameters.push(parameter);
        return sql;
    }

    hasArgDescriptor(valuePath: string): boolean {
        return this.sqlParameters.some(parameter => parameter.valuePath === valuePath);
    }

    resolveLiteral(value: AnyValue): string {
        if (value == null) {
            return 'NULL';
        }
        if (typeof value === 'string' && value.includes(dbCommandMarker)) {
            return value;
        }
        if (typeof value === 'boolean') {
            return value ? '(1=1)' : '(1=2)';
        }
        const key = `${typeof value}/${value.toString()}`;
        if (!this.#literals[key]) {
            // type does not matter as value will be directly obtained from literalValue
            this.#literals[key] = this.addSqlParameter({ valuePath: '', type: 'string', literalValue: value });
        }
        return this.#literals[key];
    }

    /**
     * Returns the SQL command to execute to run a 'UPDATE ... SET' command
     */
    getUpdateSetCommand<This extends Node>(options: BulkUpdateOptions<This>): string {
        const convertValue = (key: string, value: any): string => {
            const property = this.rootFactory.propertiesByName[key];
            if (typeof value === 'function') {
                let sql = this.convertFunction(value).sql;
                if (property?.isEnumProperty()) {
                    // When the property is an enum, we need to cast the generated SQL to the enum type
                    // The generated SQL is typed as string because we don't have any information on the
                    // property type when resolving the enum value
                    sql = `(${sql})::${this.context.schemaName}.${property.dataType.getEnumType().name}`;
                }
                if (property?.isLocalized && this.context.processLocalizedTextAsJson) {
                    sql = `json_build_object('base', ${sql})`;
                }
                return sql;
            }
            if (typeof value === 'string' && value.endsWith('/*%%DB_COMMAND%%*/')) {
                // TODO: find a better way - this could be exploited for SQL injection
                console.warn(`POTENTIAL SQL INJECTION: ${value}`);
                return value;
            }

            return this.addSqlParameter({
                valuePath: `values.${property.name}`,
                type: property.type || 'string',
                column: property.column,
                // isArray: property.isArrayProperty(),
                factoryName:
                    property.isReferenceProperty() || property.isReferenceArrayProperty()
                        ? property.targetFactory.name
                        : undefined,
            });
        };

        const setClauses = Object.keys(options.set).map(key => {
            const value = (options.set as any)[key];
            return `${getColumnName(key)}=${convertValue(key, value)}`;
        });

        let whereClause = options.where ? this.convertWhere(options.where) : 'TRUE';

        const alias0 = this.aliases[0];

        if (this.context.tenantId) {
            if (!this.rootFactory.isSharedByAllTenants) {
                whereClause = `${whereClause} AND ${alias0.alias}._tenant_id = ${this.resolveLiteral(
                    this.context.tenantId,
                )}`;
            }
        } else if (!this.context.unsafeApplyToAllTenants) {
            // TODO: fix unit test and throw an error
            loggers.sql.warn('Operation not permitted: unsafeApplyToAllTenants is not set (update)');
        }

        const whereParts = [whereClause];
        if (this.aliases.length === 1) {
            return `UPDATE ${alias0.tableName} AS ${alias0.alias} SET ${setClauses.join(',')} WHERE (${whereClause})`;
        }

        if (!this.rootFactory.isSharedByAllTenants) {
            whereParts.unshift('t00._tenant_id = t0._tenant_id');
        }
        whereParts.unshift('t0._id = t00._id');

        return `UPDATE ${alias0.tableName} AS t00 SET ${setClauses.join(
            ',',
        )} FROM ${this.getTableAliases()} WHERE (${whereParts.join(') AND (')})`;
    }

    /**
     * Returns the SQL command to execute to run a 'DELETE FROM WHERE ' command
     */
    getDeleteFromCommand<This extends Node>(options: BulkDeleteOptions<This>): string {
        let whereClause = options.where ? this.convertWhere(options.where) : 'TRUE';

        const alias0 = this.aliases[0];

        if (this.context.tenantId) {
            whereClause = `${whereClause} AND ${alias0.alias}._tenant_id = ${this.addSqlParameter({
                valuePath: 'context.tenantId',
                type: 'string',
            })}`;
        } else if (!this.context.unsafeApplyToAllTenants) {
            // TODO: fix unit test and throw an error
            loggers.sql.warn('Operation not permitted: unsafeApplyToAllTenants is not set (delete)');
        }

        const whereParts = [whereClause];
        if (this.aliases.length === 1) {
            return `DELETE FROM ${alias0.tableName} AS ${alias0.alias} WHERE (${whereClause})`;
        }

        // https://www.drupal.org/docs/7/guidelines-for-sql/writing-code-compliant-with-both-mysql-and-postgresql/mysqlism-and-0
        return `DELETE FROM ${alias0.tableName} AS t0 USING ${this.aliases
            .slice(1)
            .map(alias => `${alias.tableName} AS ${alias.alias}`)
            .join(',')} WHERE (${this.aliases.slice(1).map(alias => alias.join?.condition)}) AND (${whereParts.join(
            ') AND (',
        )})`;
    }

    private static hasQuantifier(val: any): boolean {
        return Object.keys(val).filter(key => /^_(atMost|atLeast|every|none)$/.test(key)).length > 0;
    }

    private static getQuantifier(slot: ConversionResult, val: any): string {
        if (!val || typeof val !== 'object') {
            throw new Error(`${slot.path}: invalid collection filter - not an object`);
        }
        if (!this.hasQuantifier(val)) {
            throw new Error(
                `${slot.path}: missing _atLeast/_atMost/_every/_none quantifier on nested filter ${Object.keys(val)}`,
            );
        }
        const quantifiers = Object.keys(val).filter(key => /^_(atMost|atLeast|every|none)$/.test(key));
        if (quantifiers.length > 1) {
            throw new Error(
                `${slot.path}: only ONE _atLeast/_atMost/_every/_none quantifier on nested filter can be defined but found ${quantifiers}`,
            );
        }
        return quantifiers[0];
    }

    private static checkQuantifierValue(slot: ConversionResult, val: any, quantifier: string): void {
        if (quantifier === '_atMost' || quantifier === '_atLeast') {
            if (!Number.isFinite(Number(val[quantifier])))
                throw new Error(`${slot.path}.${quantifier}: value must be a number.`);
        } else if (val[quantifier] !== true) {
            throw new Error(`${slot.path}.${quantifier}: value must be true`);
        }
    }

    private convertSubqueryFilter(
        valuePath: string,
        slot: ConversionResult,
        innerFilter: any,
        getJoinCondition?: () => string,
    ): {
        innerFilterSql: string;
        aliases: string;
        joinCondition: string;
    } {
        return super.withSubQueryScope(() => {
            const originalSkipJoin = !!slot.skipJoin;
            let propertyJoin;
            if (slot.property?.type === 'collection' && !slot.property?.reverseReference && slot.property?.join) {
                if (Object.values(slot.property.join).some(element => typeof element === 'string'))
                    throw new Error(
                        `Invalid join ${JSON.stringify(slot.property.join)} with string value. Please use function instead or new JoinLiteralValue(value) syntax.`,
                    );
                propertyJoin = slot.property.join;
                // we need to skip the join for the nested filter as we have added the join condition to the filter
                slot.skipJoin = true;
            }
            let sql = this.convertFilterObject(valuePath, slot, innerFilter);
            if (propertyJoin) {
                const propertyJoinSql = this.convertFilterObject(valuePath, slot, propertyJoin);
                sql = Converter.and([sql, propertyJoinSql]);
            }
            slot.skipJoin = originalSkipJoin;

            if (
                !getJoinCondition &&
                slot.property &&
                slot.property.isForeignNodeProperty() &&
                !slot.property.targetFactory.isSharedByAllTenants
            ) {
                // if the target factory is not shared by all tenants, we need to add the tenant filter if not provided in the getJoinCondition method
                sql = Converter.and([
                    sql,
                    `${this.scopeAlias?.alias}._tenant_id = ${this.addSqlParameter({
                        valuePath: 'context.tenantId',
                        type: 'string',
                    })}`,
                ]);
            }
            return {
                innerFilterSql: sql,
            };
        }, getJoinCondition);
    }

    private getNestedFilterSubquery(
        valuePath: string,
        slot: ConversionResult,
        val: any,
        getJoinCondition?: () => string,
    ): string {
        const quantifier = SqlConverter.getQuantifier(slot, val);
        SqlConverter.checkQuantifierValue(slot, val, quantifier);

        // Remove the quantifier from the filter
        const innerFilter = _.omit(val, [quantifier]) as Dict<any>;

        const results = this.convertSubqueryFilter(valuePath, slot, innerFilter, getJoinCondition);

        const condition = quantifier === '_every' ? `NOT(${results.innerFilterSql})` : results.innerFilterSql;
        // every x satisfies y <=> none x statisfies not y

        const subQuery = `(SELECT COUNT(*) FROM ${results.aliases} WHERE ${SqlConverter.and([
            results.joinCondition,
            condition,
        ])})`;
        switch (quantifier) {
            case '_atLeast': {
                const arg = this.addSqlParameter({ valuePath, subPath: '_atLeast', type: 'integer' });
                return `(${subQuery} >= ${arg})`;
            }
            case '_atMost': {
                const arg = this.addSqlParameter({ valuePath, subPath: '_atMost', type: 'integer' });
                return `(${subQuery} <= ${arg})`;
            }
            case '_none':
            case '_every':
                return `(${subQuery} = 0)`;
            default:
                throw new Error(`invalid quantifier: ${quantifier}`);
        }
    }

    private convertCollectionFilter(valuePath: string, slot: ConversionResult, val: any): string {
        return this.getNestedFilterSubquery(valuePath, slot, val);
    }

    private convertReferenceArrayFilter(valuePath: string, slot: ConversionResult, val: any): string {
        if (!SqlConverter.hasQuantifier(val)) return this.convertAnyFilter(valuePath, slot, val);

        // we need a callback here, to get the condition generated inside the new collection scope
        const getJoinCondition = (): string => {
            return `${this.scopeAlias?.alias}._id in (select unnest(${slot.sql})) AND ${
                this.scopeAlias?.alias
            }._tenant_id = ${this.addSqlParameter({
                valuePath: 'context.tenantId',
                type: 'string',
            })}`;
        };

        return this.getNestedFilterSubquery(valuePath, slot, val, getJoinCondition);
    }

    private convertArrayFilter(valuePath: string, slot: ConversionResult, val: any): string {
        if (typeof val === 'object' && !Array.isArray(val)) {
            if (slot.type === 'referenceArray') return this.convertReferenceArrayFilter(valuePath, slot, val);

            return this.convertAnyFilter(valuePath, slot, val);
        }

        const left = slot.sql;
        const right = this.convertArg(valuePath, slot);

        return `${left} = ${right}`;
    }

    private convertPropertyFilter(valuePath: string, slot: ConversionResult, filter: any, key: string): string {
        const childSlot = { ...super.walk(slot, key), collation: this.context.collation };
        const newValuePath = `${valuePath}.${key}`;
        const val = filter[key];
        if (childSlot.type === 'collection') return this.convertCollectionFilter(newValuePath, childSlot, val);
        if ((arrayTypes as ConversionResultType[]).includes(childSlot.type))
            return this.convertArrayFilter(newValuePath, childSlot, val);
        return this.convertAnyFilter(newValuePath, childSlot, val);
    }

    private static convertNumber(parameter: SqlParameter, val: any): number {
        const number = typeof val === 'number' ? val : parseFloat(String(val));
        if (!Number.isFinite(number))
            throw new DataInputError(`${parameter.valuePath}: invalid numeric value: '${val}'`);
        return number;
    }

    private static convertBinary(parameter: SqlParameter, val: any): Buffer {
        if (Buffer.isBuffer(val.value)) return val.value;
        throw new Error(
            `${parameter.valuePath}: cannot convert value ${val} of type ${typeof val} to ${parameter.type} to SQL`,
        );
    }

    private static getParameterSubValue(parameter: SqlParameter, val: AnyValue): AnyValue {
        switch (parameter.subPath) {
            case '_regex':
                return val instanceof RegExp ? val.source : String((val as AnyRecord)._regex);
            case '_options':
                return val instanceof RegExp ? val.flags : String((val as AnyRecord)._options || '');
            case '_atLeast':
            case '_atMost': {
                const subVal = (val as AnyRecord)?.[parameter.subPath];
                if (typeof subVal !== 'number')
                    throw new Error(`${parameter.valuePath}.${parameter.subPath}: invalid value: ${subVal}`);
                return subVal;
            }
            default:
                throw new LogicError(`${parameter.valuePath}: invalid subPath: ${parameter.subPath}`);
        }
    }

    private static getParameterValue(
        context: Context,
        parameter: SqlParameter,
        val: AnyValue,
    ): AsyncResponse<AnyValue> {
        if (val === undefined && !parameter.column) {
            throw new Error(`${parameter.valuePath}: SQL arg is undefined`);
        }

        if (parameter.isArray && Array.isArray(val)) {
            // Promise.all is needed if the array contains references in natural key syntax ('#...').
            return Promise.all(val.map(v => this.getParameterValue(context, { ...parameter, isArray: false }, v)));
        }

        if (parameter.column) return SqlValueConverter.toSql(context, parameter.column, val);
        if (val == null) return null;

        if (parameter.subPath) return this.getParameterSubValue(parameter, val);

        switch (parameter.type) {
            case 'boolean':
            case 'enum':
                return val;

            case 'encryptedString':
                if (typeof val !== 'string')
                    throw new Error(`Invalid value for ${parameter.type}: ${val} of type ${typeof val}`);
                return context.vault.getValueFromVault(val);
            case 'string':
                if (typeof val !== 'string') logWrongType(parameter.type, parameter.valuePath || '', val);
                return String(val);

            case 'decimal':
                return String(val);
            case 'short':
            case 'float':
            case 'double':
            case 'integer':
                return this.convertNumber(parameter, val);

            // Array types
            case 'enumArray':
                if (!Array.isArray(val)) throw new Error(`Invalid array value for ${parameter.type}: ${val}`);
                return val;
            case 'integerArray':
            case 'referenceArray': // TODO: see if referenceArray is used here?
                if (!Array.isArray(val)) throw new Error(`Invalid array value for ${parameter.type}: ${val}`);
                return val.map(v => this.convertNumber(parameter, v));

            case 'stringArray':
                if (!Array.isArray(val)) throw new Error(`Invalid value for ${parameter.type}: ${val}`);
                return val;

            // Range types
            case 'integerRange':
            case 'decimalRange':
            case 'dateRange':
            case 'datetimeRange':
            case 'date':
            case 'time':
            case 'datetime':
            case 'textStream':
                return String(val);

            case 'reference': {
                // TODO: see if we get here
                if (!parameter.factoryName) throw new Error(`${parameter.valuePath}: no targetFactory`);
                const factory = context.application.getFactoryByName(parameter.factoryName);
                return factory.resolveReferenceId(context, val as string | number);
            }
            case 'json':
            case 'jsonReference':
                return val;
            case 'uuid':
            case 'binaryStream':
                return this.convertBinary(parameter, val);
            default:
                throw new Error(`${parameter.valuePath}: cannot convert type ${parameter.type} to SQL`);
        }
    }

    static getParameterValues(context: Context, sqlParameters: SqlParameter[], data: AnyRecord): Promise<AnyValue[]> {
        const parametersData = context.tenantId ? { ...data, context: { tenantId: context.tenantId } } : data;

        return Promise.all(
            sqlParameters.map(parameter =>
                parameter.literalValue !== undefined
                    ? parameter.literalValue
                    : this.getParameterValue(context, parameter, _.get(parametersData, parameter.valuePath)),
            ),
        );
    }

    private convertArg(valuePath: string, slot: ConversionResult): string {
        if (!slot.type) throw new Error(`${valuePath}: cannot convert arg: type is missing`);
        return this.addSqlParameter({
            valuePath,
            type: slot.type,
            column: slot.property?.column,
            factoryName: slot.factory?.name,
        });
    }

    static convertDefaultValue(val: any): string {
        if (val == null) return 'NULL';
        if (typeof val === 'number') return String(val);
        if (typeof val === 'boolean') return val ? 'TRUE' : 'FALSE';
        return quote(String(val));
    }

    private static jsonCast(sql: string, value: any): string {
        // https://www.reddit.com/r/PostgreSQL/comments/qwvipl/comment/hl5cxq1/?utm_source=reddit&utm_medium=web2x&context=3
        // array_agg(x) is null when if x is an empty array, we want it to be an empty array instead
        if (Array.isArray(value)) {
            return `COALESCE((select array_agg(x) from jsonb_array_elements_text(${sql}) x),ARRAY[]::text[])`;
        }
        switch (typeof value) {
            case 'boolean':
                return `(${sql})::TEXT::BOOLEAN`;
            case 'number':
                return `(${sql})::TEXT::NUMERIC`;
            case 'string':
                // WARNING: Postgres fails on repeated casts like (((t0._custom_data->'strValue'->>0)::TEXT)->>0)::TEXT
                return sql.endsWith('->>0)::TEXT') ? sql : `((${sql})->>0)::TEXT`;
            default:
                throw new Error(`invalid JSON value type: ${typeof value}`);
        }
    }

    private convertRegExpOps(valuePath: string, slot: ConversionResult): string {
        // use quote directly as type is string, not slot.type
        const regex = this.addSqlParameter({ valuePath, subPath: '_regex', type: 'string' });
        const options = this.addSqlParameter({ valuePath, subPath: '_options', type: 'string' });
        return this.regexConverter.convertRegex(regex, options, slot, { isParam: true }).sql;
    }

    private convertValue(valuePath: string, slot: ConversionResult): string {
        const right = this.convertArg(valuePath, slot);
        const left =
            slot.type === 'json' || slot.type === 'jsonReference' ? SqlConverter.jsonCast(slot.sql, right) : slot.sql;

        const eqSql = `${left} = ${right}`;
        return slot.isNullable ? `(${eqSql} OR (${left} IS NULL AND ${right} is NULL))` : eqSql;
    }

    private static checkArray(slot: ConversionResult, val: any): asserts val is any[] {
        if (!Array.isArray(val)) throw new Error(`${slot.path}: values is not an array`);
    }

    private convertXinFilter(valuePath: string, slot: ConversionResult, filter: any, key: string): string {
        if (slot.type?.endsWith('Array'))
            throw new Error(
                `${slot.path}: _in and _nin operators not supported on array types (checking if inside an array of arrays)`,
            );

        SqlConverter.checkArray(slot, filter[key]);

        const elementsArg = this.addSqlParameter({
            valuePath: `${valuePath}.${key}`,
            type: slot.type || 'string',
            column: slot.property?.column,
            isArray: true,
            factoryName: slot.factory?.name,
        });
        const eqAny = `(${slot.sql} = ANY(${elementsArg}))`;
        const orHasNull = ` OR (${slot.sql} IS NULL AND (TRUE = ANY (SELECT unnest(${elementsArg}) IS NULL))) `;

        const inSql = `(${eqAny} ${orHasNull})`;

        // COALESCE is necessary here, for _nin, because NOT(FALSE) is TRUE whereas NOT(NULL) is NULL.
        return key === '_in' ? inSql : `NOT COALESCE(${inSql}, FALSE)`;
    }

    private convertAndFilter(valuePath: string, slot: ConversionResult, val: any): string {
        SqlConverter.checkArray(slot, val);
        const strings = val.map((v, i) => this.convertFilterObject(`${valuePath}._and.${i}`, slot, v));
        return SqlConverter.and(strings);
    }

    private convertOrFilter(valuePath: string, slot: ConversionResult, val: any): string {
        SqlConverter.checkArray(slot, val);
        const strings = val.map((v, i) => this.convertAnyFilter(`${valuePath}._or.${i}`, slot, v));
        return SqlConverter.or(strings);
    }

    private convertNorFilter(valuePath: string, slot: ConversionResult, val: any): string {
        SqlConverter.checkArray(slot, val);
        const strings = val.map((v, i) => this.convertAnyFilter(`${valuePath}._nor.${i}`, slot, v));
        return SqlConverter.and(strings.map(sql => `(NOT (${sql}))`));
    }

    private convertModFilter(valuePath: string, slot: ConversionResult): string {
        const modulus = this.convertArg(`${valuePath}._mod.0`, slot);
        const remainder = this.convertArg(`${valuePath}._mod.1`, slot);
        return `(${slot.sql} % ${modulus} = ${remainder})`;
    }

    private convertNotFilter(valuePath: string, slot: ConversionResult, val: any): string {
        const sql = this.convertFilterObject(`${valuePath}._not`, slot, val);
        return `NOT (${sql})`;
    }

    private convertBinaryOpFilter(valuePath: string, slot: ConversionResult, filter: any, key: string): string {
        const op = binaryOps[key];
        const val = filter[key];
        if (val == null) {
            if (key === '_eq') return `${slot.sql} IS NULL`;
            if (key === '_ne') return `${slot.sql} IS NOT NULL`;
            if (key === '_gt') return `${slot.sql} IS NOT NULL`;
            if (key === '_gte') return 'TRUE';
            if (key === '_lt') return 'FALSE';
            if (key === '_lte') return `${slot.sql} IS NULL`;
        }

        let left: string;
        let right: string;

        switch (slot.type) {
            case 'integerArray':
            case 'enumArray':
            case 'referenceArray':
            case 'stringArray':
                if (key === '_contains') {
                    left = this.addSqlParameter({
                        valuePath: `${valuePath}.${key}`,
                        type: slot.type.replace('Array', '') as ConversionResultType,
                        // do not pass column because its type is incorrect (should not be Array)
                        factoryName: slot.factory?.name,
                    });
                    right = slot.sql;
                } else {
                    left = slot.sql;
                    right = this.convertArg(`${valuePath}.${key}`, slot);
                }
                break;
            case 'integerRange':
            case 'decimalRange':
            case 'dateRange':
            case 'datetimeRange':
                if (key === '_contains') {
                    left = slot.sql;
                    right = this.addSqlParameter({
                        valuePath: `${valuePath}.${key}`,
                        type: slot.type.replace('Range', '') as ConversionResultType,
                        // do not pass column because its type is incorrect (should not be Array)
                    });
                } else {
                    left = slot.sql;
                    right = this.convertArg(`${valuePath}.${key}`, slot);
                }
                break;

            case 'json':
            case 'jsonReference':
                left = key === '_contains' ? slot.sql : SqlConverter.jsonCast(slot.sql, filter[key]);
                right = this.convertArg(`${valuePath}.${key}`, slot);
                break;

            default:
                left = slot.sql;
                right = this.convertArg(`${valuePath}.${key}`, slot);
                break;
        }

        return op(slot, left, right);
    }

    private convertOperatorFilter(valuePath: string, slot: ConversionResult, filter: any, key: string): string {
        if (binaryOps[key]) return this.convertBinaryOpFilter(valuePath, slot, filter, key);
        const val = filter[key];
        switch (key) {
            case '_in':
            case '_nin':
                return this.convertXinFilter(valuePath, slot, filter, key);
            case '_and':
                return this.convertAndFilter(valuePath, slot, val);
            case '_or':
                return this.convertOrFilter(valuePath, slot, val);
            case '_nor':
                return this.convertNorFilter(valuePath, slot, val);
            case '_mod':
                return this.convertModFilter(valuePath, slot);
            case '_not':
                return this.convertNotFilter(valuePath, slot, val);
            case '_regex':
                return !val ? '' : this.convertRegExpOps(valuePath, slot);
            case '_options':
                // we may have NaN for both _regex and _options, we should not throw in that case
                if (filter._options && !filter._regex) throw new Error(`${slot.path}: _options without _regex`);
                return '';
            case '_fn':
                if (typeof val !== 'string') throw new Error(`${slot.path}: _fn value is not a string`);
                return this.convertFunctionBody(slot, val).sql;

            default:
                // TODO: localize
                throw new DataInputError(`${slot.path}: invalid operator: ${key}`);
        }
    }

    private convertFilterObject(valuePath: string, slot: ConversionResult, filter: any): string {
        const keys = Object.keys(filter);
        if (keys.length === 0) {
            if (slot.parent) this.makeAliasAndJoin(slot);
            return 'TRUE';
        }
        const strings = keys
            .filter(key => filter[key] !== undefined)
            .map(key => {
                if (key[0] === '_' && !SystemProperties.isSystemOrTechnicalProperty(key))
                    return this.convertOperatorFilter(valuePath, slot, filter, key);
                return this.convertPropertyFilter(valuePath, slot, filter, key);
            })
            .filter(s => !!s);
        return SqlConverter.and(strings);
    }

    private convertFunctionValue(slot: ConversionResult, val: () => AsyncResponse<AnyValue>): string {
        // if we cannot evaluate the function, we attempt to convert it to a SQL
        const right = this.convertFunction(val).sql;
        const left =
            slot.type === 'json' || slot.type === 'jsonReference' ? SqlConverter.jsonCast(slot.sql, right) : slot.sql;

        const eqSql = `${left} = ${right}`;
        return slot.isNullable ? `(${eqSql} OR (${left} IS NULL AND ${right} is NULL))` : eqSql;
    }

    private convertJoinLiteralValue(slot: ConversionResult, val: JoinLiteralValue): string {
        // if we cannot evaluate the function, we attempt to convert it to a SQL
        const right = this.convertLiteral(val.value).sql;
        const left =
            slot.type === 'json' || slot.type === 'jsonReference' ? SqlConverter.jsonCast(slot.sql, right) : slot.sql;

        const eqSql = `${left} = ${right}`;
        return slot.isNullable ? `(${eqSql} OR (${left} IS NULL AND ${right} is NULL))` : eqSql;
    }

    private convertAnyFilter(valuePath: string, slot: ConversionResult, filter: any): string {
        // convert regex literal to object
        if (filter instanceof RegExp)
            return this.convertAnyFilter(valuePath, slot, { _regex: filter.source, _options: filter.flags });

        if (filter instanceof Node) return this.convertFilterObject(valuePath, slot, { _id: filter._id });

        // We have received a function as a value, in the filter, we need convert it to a SQL expression
        if (typeof filter === 'function') {
            return this.convertFunctionValue(slot, filter);
        }

        if (filter instanceof JoinLiteralValue) {
            return this.convertJoinLiteralValue(slot, filter);
        }

        if (isFilterObject(filter)) return this.convertFilterObject(valuePath, slot, filter);

        return this.convertValue(valuePath, slot);
    }

    private convertFilter(valuePath: string, filter: any, referenceItem?: ConversionResult): string {
        if (typeof filter === 'string') return filter;
        if (typeof filter === 'function') {
            const result = super.convertFunction(filter);
            if (referenceItem && result.type === 'json') {
                return SqlResolver.castFromJson(result.sql, referenceItem.type);
            }
            if (result.type === 'reference' && result.property && !result.property.isStored) {
                return this.walk(result as ConversionResult, '_id').sql;
            }
            return result.sql;
        }
        if (filter instanceof JoinLiteralValue) return this.convertLiteral(filter.value).sql;
        if (!(filter && typeof filter === 'object')) {
            throw new Error(`filter is not an object: ${filter && typeof filter}`);
        }
        const slot = {
            factory: this.rootFactory,
            type: 'reference',
            sql: 't0',
            path: 'this',
            alias: 't0',
            collation: this?.context?.collation,
        } as const;
        return this.convertFilterObject(valuePath, slot, filter);
    }

    convertWhere(filter: any): string {
        return this.convertFilter('where', filter);
    }

    convertFilters(filters: any[]): string[] {
        return filters.map((filter, i) => this.convertFilter(`filters.${i}`, filter));
    }

    convertOrderBy(orderBy: any): OrderByClause[] {
        const clauses: OrderByClause[] = [];
        const convertOne = (prevResult: ConversionResult, previousPath: string[], obj: any): void => {
            Object.keys(obj).forEach(key => {
                const path = [...previousPath, key];
                const result = this.withThisResultScope(prevResult, () => super.walk(prevResult, key));
                const val = obj[key];

                if (val && typeof val === 'object') convertOne(result, path, val);
                else if (val === -1 || val === 1) {
                    if (!result.property) throw new Error(`${path}: 'Missing property in result.`);

                    clauses.push({
                        property: result.property,
                        path,
                        direction: val,
                        sql: result.sql,
                        columnAlias: SqlResolver.makeColumnAlias(result.columnAlias ?? ''),
                    });
                } else throw new Error(`${path}: invalid value: ${val}`);
            });
        };
        const thisResult = super.convertThisExpression();
        convertOne(thisResult, [], orderBy);
        return clauses;
    }

    // Returns the SQL json_agg(...) expression for a collection JSON.
    // See https://stackoverflow.com/questions/60458369/subquery-as-a-json-field
    private getJsonAggSql(outputColumns: OutputColumn[], alias: string): string {
        // If we did not select any properties in the collection node, the JSON value will be an array of _id values
        if (outputColumns.length === 0) {
            return `json_agg(${alias}._id)`;
        }

        // jsonFieldsSql is a list of key value pairs, all in a single comma separated list, that we pass to
        // json_build_object to create a JSON object.
        // The keys are the property names. We pass them as parameters to keep the SQL injection check happy.
        // So we get something like: `$1::TEXT, t2.column1, $2::TEXT, t2.column2, $3::TEXT, t2.column3`
        // with parameters ['prop1', 'prop2', 'prop3']
        const jsonFieldsSql = outputColumns
            .map(
                outputColumn =>
                    `${this.addSqlParameter({
                        valuePath: '',
                        type: 'string',
                        literalValue: outputColumn.columnAlias,
                    })}::TEXT, ${outputColumn.sql}`,
            )
            .join(', ');
        return `json_agg(json_build_object(${jsonFieldsSql}))`;
    }

    // Returns the SQL expression for the sub-paths selected in a collection.
    private convertCollectionSubPaths(parent: ConversionResult, subPaths: OutputPath[]): OutputColumn {
        if (parent.property?.type !== 'collection')
            throw new LogicError(`parent property is not a collection: ${parent.property?.type}`);

        const result: OutputColumn = { ...parent };
        result.columnAlias = SqlResolver.makeColumnAlias(`${parent.parent?.alias}.${parent.property?.name}`);

        const results = this.withSubQueryScope(() => {
            this.makeAliasAndJoin(result);
            if (!result.alias) throw new LogicError('no alias');
            const subColumns = this.convertOutputPaths(subPaths, { ...result, type: 'reference' });
            const jsonAggSql = this.getJsonAggSql(subColumns, result.alias);
            return { jsonAggSql, subColumns };
        }, undefined);

        const emptyArray = this.addSqlParameter({ valuePath: '', type: 'string', literalValue: '[]' });
        // See https://stackoverflow.com/questions/60458369/subquery-as-a-json-field
        result.sql = `COALESCE(
                (SELECT ${results.jsonAggSql}
                    FROM  ${results.aliases}
                    WHERE ${results.joinCondition}),
                ${emptyArray}::json)`;
        result.subColumns = results.subColumns;

        return result;
    }

    // Converts a single selected path
    convertOutputPath(outputPath: OutputPath, start = this.convertThisExpression()): OutputColumn {
        let parent = start;
        // Stop the walk before last element if compute is set
        const compute = outputPath.compute;
        const path = compute ? outputPath.path.slice(0, -1) : outputPath.path;
        path.forEach(name => {
            parent = super.walk(parent, name);
        });

        if (compute) {
            parent = this.withThisResultScope(parent, () => this.convertFunction(compute)) as ConversionResult;
        } else if (outputPath.subPaths) {
            parent = this.convertCollectionSubPaths(parent, outputPath.subPaths);
        } else if (!parent.sql && parent.property?.isReferenceProperty()) {
            // This happens when parent is a referenced property defined by a join (not stored)
            // We walk to `_id` to set parent.sql
            super.walk(parent, '_id');
            parent.columnAlias = SqlResolver.makeColumnAlias(parent.sql);
        }
        return { ...parent, payloadPath: outputPath.path };
    }

    // Converts an array of selected paths
    convertOutputPaths(outputPaths: OutputPath[], start = this.convertThisExpression()): OutputColumn[] {
        return outputPaths.map(outputPath => this.convertOutputPath(outputPath, start));
    }

    convertAggregateGroups(sqlConverter: SqlConverter, groups: AggregateGroup[]): OutputColumn[] {
        return groups.map(group =>
            SqlResolver.resolveAggregate(sqlConverter, this.convertOutputPath({ path: group.path }), group.groupedBy),
        );
    }

    convertAggregate(sqlConverter: SqlConverter, aggregate: Aggregate | undefined): AggregateResult {
        if (!aggregate) return {};
        const groups = aggregate.groups.map(group =>
            SqlResolver.resolveAggregate(sqlConverter, this.convertOutputPath({ path: group.path }), group.groupedBy),
        );
        const values = aggregate.values.map(value =>
            SqlResolver.resolveAggregate(sqlConverter, this.convertOutputPath({ path: value.path }), value.operator),
        );
        return { groups, values };
    }

    // eslint-disable-next-line class-methods-use-this
    override getConfigurationValue(name: string): string {
        return Context.getConfigurationValue(<ContextGetConfigurationKeyType>name);
    }

    convertDelegatesTo(result: ConversionResult, property: Property): ConversionResult {
        const { reference, childProperty } = property.getDelegatingInfo();

        const referenceResult = this.walk(result, reference.name);
        return this.walk(referenceResult, childProperty.name);
    }

    convertJoin(prevResult: ConversionResult, previousPath: string, obj: any): void {
        const parent = prevResult.parent;
        if (!parent) throw new LogicError('no parent');

        const parentSqls: string[] = [];
        const resolvedSqls: string[] = [];

        Object.keys(obj).forEach(key => {
            const currentJoinVal = obj[key];
            const valueType = typeof obj[key];
            const currentResult = this.walk(prevResult, key);

            if (valueType === 'object' && !(currentJoinVal instanceof JoinLiteralValue)) {
                throw new Error('NYI: nested join');
                // TODO: When having nested joins, we have a problem with a cycle in the join,
                // where the parent is joined to the child and the child is joined to the parent.
                // convertJoin(currentResult, `${previousPath}.${key}`, currentJoinVal);
            } else {
                resolvedSqls.push(currentResult.sql);
                if (typeof currentJoinVal === 'string') {
                    parentSqls.push(this.walk(parent, currentJoinVal).sql);
                } else {
                    parentSqls.push(
                        this.withThisResultScope(parent, () =>
                            this.convertFilter(previousPath, currentJoinVal, currentResult),
                        ),
                    );
                }
            }
        });

        prevResult.sql = `${prevResult.alias}._id`;
        prevResult.join = Walker.createJoin(
            {
                alias: parent.alias,
                sqls: parentSqls,
            },
            {
                alias: prevResult.alias,
                sqls: resolvedSqls,
            },
            {
                withTenantId: !prevResult.factory?.isSharedByAllTenants,
                isNullable: !!prevResult.property?.isNullable || !!parent.join?.isNullable,
            },
        );
    }

    // Override to handle the case where a property has an explicit join in a decorator attribute.
    protected override createReferenceJoin(resolved: ConversionResult, reverseResolved: ConversionResult | null): void {
        const join = resolved.property?.isReferenceProperty() && resolved.property.decorator.join;
        if (!join) {
            super.createReferenceJoin(resolved, reverseResolved);
            if (reverseResolved?.parent?.factory.isAbstract && resolved?.join?.condition) {
                const constructorSql = this.resolveLiteral(resolved.factory.name);
                resolved.join.condition = SqlConverter.and([
                    resolved.join.condition,
                    `${reverseResolved.parent.alias}._constructor=${constructorSql}`,
                ]);
            }
            return;
        }

        this.convertJoin(resolved, resolved.path, join);
    }

    // Override to handle the case where a property has an explicit join in a decorator attribute.
    protected override createCollectionJoin(
        resolved: ConversionResult,
        reverseResolved: ConversionResult | null,
    ): void {
        const join = resolved.property?.isCollectionProperty() && resolved.property.decorator.join;

        if (!join) {
            super.createCollectionJoin(resolved, reverseResolved);
            return;
        }

        if (resolved.skipJoin) {
            this.scopeAlias = {
                tableName: this.resolver.resolveTableName(resolved.factory),
                alias: resolved.alias,
            };
            return;
        }
        this.convertJoin(resolved, resolved.path, join);
        this.scopeAlias = {
            tableName: this.resolver.resolveTableName(resolved.factory),
            alias: resolved.alias,
            join: resolved.join,
        };
    }
}
