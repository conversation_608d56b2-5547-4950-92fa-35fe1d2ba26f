/** @ignore */ /** */
import { AnyRecord } from '@sage/xtrem-async-helper';
import { CustomMetrics } from '@sage/xtrem-metrics';
import { Dict } from '@sage/xtrem-shared';
import * as lodash from 'lodash';
import { Context, NodeFactory } from '../../runtime';
import { tenantCondition } from '../../runtime/utils';
import { Table } from '../schema/table';
import { SqlContext } from '../sql-context/sql-context';
import { SqlConverter } from './sql-converter';
import { SqlUtil } from './sql-util';

/** @internal */
export interface RecordUpdateOptions {
    onlyColumns?: Dict<boolean>;
    ignoreAllSystemFilters?: boolean;
    /** Update comes from CSV, so we should be able to update vendor data and not isOwnedByCustomer data */
    updateFromCsv?: boolean;
    filter?: Dict<any>;
}

/** @internal */
export class SqlUpdate {
    constructor(private readonly table: Table) {}

    get factory(): NodeFactory {
        return this.table.factory;
    }

    private getUpdateParameters(
        sqlConverter: SqlConverter,
        data: AnyRecord,
        options: RecordUpdateOptions,
    ): {
        columnNames: string[];
        valsOrParameters: string[];
    } {
        const { context } = sqlConverter;
        const onlyColumns = options.onlyColumns;

        const columns = this.table.columns.filter(col => {
            if (onlyColumns?.[col.columnName]) return true;
            return (
                !onlyColumns && (!col.isSystem || col.columnName === '_custom_data' || col.columnName === '_sync_info')
            );
        });

        const columnNames = [] as string[];
        const valsOrParameters = [] as string[];

        columns
            .filter(column => column.propertyName !== '_id' && column.propertyName !== '_constructor')
            /**
             * We need to not update the protected property values
             * One case were we will receive a value in the data are protected localized properties.
             * The context locale may be different from the locale the value was retrieved from the database with
             * Example: current locale is `fr-FR`, there is a value stored for this locale,
             * therefore the value is loaded from the tenant default locale `en`
             * The value did not change but the update this column by adding `fr-FR` to the JSON column on the table.
             * Hence we need to exclude it here from the data set to update.
             *
             * updateFromCsv is there to allow us to update reference properties from CSV that were reset to null by the
             * 2 pass functionality of loadLayerData. There is however a problem with this at the moment. isOwnedByCustomer
             * properties will also be reset to null by the first pass, so all we can do for now is to reload the value from CSV
             * TODO: Find a way to retain isOwnedByCustomer reference properties in loadLayerData
             */
            .filter(
                column =>
                    !data._vendor || (data._vendor && (options.updateFromCsv || column.property.isOwnedByCustomer)),
            )
            .forEach(column => {
                // Set localized properties in json format { locale: 'text value' }
                if (column.property.isLocalized) {
                    const propertyName = column.property.name;
                    data[propertyName] = SqlUtil.fixLocalizedValue(context, data[propertyName], propertyName, 'update');
                }
                SqlUtil.pushValuesForUpdateOrInsert(
                    this.table,
                    sqlConverter,
                    column,
                    data[column.propertyName],
                    columnNames,
                    valsOrParameters,
                );
            });
        return { columnNames, valsOrParameters };
    }

    private getForWhereParameters(
        sqlConverter: SqlConverter,
        data: AnyRecord,
    ): {
        columnsForWhere: string[];
        valsForWhere: string[];
    } {
        const columnsForWhere: string[] = [];
        const valsForWhere: string[] = [];
        SqlUtil.pushValuesForUpdateOrInsert(
            this.table,
            sqlConverter,
            this.table.columnsByPropertyName._id,
            data._id,
            columnsForWhere,
            valsForWhere,
        );
        return { columnsForWhere, valsForWhere };
    }

    async update(context: Context, inputData: AnyRecord, options: RecordUpdateOptions = {}): Promise<AnyRecord[]> {
        context.sqlSpy.incrementCounter(this.factory, 'UPDATE');
        try {
            const data = { ...inputData };
            let baseResult = [] as AnyRecord[];
            const sqlConverter = new SqlConverter(context, this.factory);
            if (this.table.baseTable) {
                // Save base tables first (to fit foreign keys)
                baseResult = await new SqlUpdate(this.table.baseTable).update(context, data, options);
            }

            // TODO: see if we need this later
            // await SqlUtil.mapCustomData(this.table, context, data);

            const { columnNames, valsOrParameters } = this.getUpdateParameters(sqlConverter, data, options);

            // Nothing to update
            if (!columnNames.length) {
                // If the table is not an abstract root table, skip the update
                if (!this.table.factory.isAbstract || this.table.factory.baseFactory) return baseResult;
                // Otherwise, perform a dummy update to update the system columns (_updateTick, _updateUser, _updateStamp)
                columnNames.push('_id');
                valsOrParameters.push(String(data._id));
            }

            const { columnsForWhere, valsForWhere } = options.ignoreAllSystemFilters
                ? { columnsForWhere: [], valsForWhere: [] }
                : this.getForWhereParameters(sqlConverter, data);

            // Get a list of the localized properties so we can update the json and not overwrite the values
            // if the context.processLocalizedTextAsJson = true, we overwrite the value in the db
            let localizedProperties: string[] = [];
            if (!context.processLocalizedTextAsJson) {
                localizedProperties = this.table.columns
                    .filter(col => col.property.isLocalized && columnNames.includes(SqlContext.escape(col.columnName)))
                    .map(col => SqlContext.escape(col.columnName));
            }

            // Columns to return in result of the update
            // - Database computed columns or _id for subnodes
            // - Localized columns, when context.processLocalizedTextAsJson
            const returningColumns = ['_id', ...this.table.databaseComputedColumnNames];

            const colsPart = columnNames
                .map((colName, idx) => {
                    // Need to use this syntax to merge the existing json with the new json values
                    // https://stackoverflow.com/questions/38883233/postgres-jsonb-set-multiple-keys-update
                    if (localizedProperties.includes(colName))
                        return `${colName}=${colName} || ${valsOrParameters[idx]}`;
                    // Special handling for _custom_data - we need a shallow merge here
                    if (colName === '_custom_data') {
                        returningColumns.push('_custom_data');
                        return `${colName}=COALESCE(${colName} || ${valsOrParameters[idx]}, ${valsOrParameters[idx]})`;
                    }
                    return `${colName}=${valsOrParameters[idx]}`;
                })
                .join(',');

            const wherePart = columnsForWhere.map((colName, idx) => `${colName}=${valsForWhere[idx]}`);
            if (options.filter) {
                wherePart.push(sqlConverter.convertWhere(options.filter));
            }

            if (context.processLocalizedTextAsJson) {
                returningColumns.push(
                    ...this.table.columns
                        .filter(
                            col => col.property.isLocalized && columnNames.includes(SqlContext.escape(col.columnName)),
                        )
                        .map(col => SqlContext.escape(col.columnName)),
                );
            }

            const sql = `UPDATE ${this.table.getFullTableName(context)}
SET ${colsPart}
WHERE ${tenantCondition(sqlConverter, this.table, '', wherePart)}
RETURNING ${lodash.uniq(returningColumns).join(',')}`;

            const parameterValues = await SqlConverter.getParameterValues(context, sqlConverter.sqlParameters, {
                values: data,
                where: options.filter,
            });
            const result = await CustomMetrics.sql.withMetrics(
                { nodeName: this.factory.name, statementKind: 'update' },
                () => context.executeSql<AnyRecord[] | { updateCount?: number }>(sql, parameterValues),
            );
            if (!Array.isArray(result)) {
                if (result.updateCount === 0) return [];
                throw this.factory.logicError(`Unexpected result from update: updateCount=${result.updateCount}`);
            }
            if (result.length > 0) {
                SqlContext.logger.verbose(() => `Returning values: ${JSON.stringify(result)}`);
            }

            if (context.testMode) this.table.markAsModifiedForTests();
            if (result && baseResult.length) {
                return result.map(r => ({ ...r, ...(baseResult.find(b => b._id === r._id) || {}) }));
            }

            return result || [];
        } catch (e) {
            if (context.testMode) this.table.markAsModifiedForTests();
            throw e;
        }
    }
}
