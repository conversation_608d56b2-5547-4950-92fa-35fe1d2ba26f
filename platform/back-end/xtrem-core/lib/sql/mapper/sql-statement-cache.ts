import { ConfigManager } from '@sage/xtrem-config';
import * as crypto from 'crypto';
import { LRUCache } from 'mnemonist';
import { CachedInsert } from './sql-insert';
import { CachedQuery } from './sql-query';

/** Union of all the statement types that may be cached */
export type CachedStatement = CachedQuery | CachedInsert;

/** The kind of statement that we are caching */
export type SqlStatementKind = 'select' | 'insert' | 'upsert';

/** The data that we use to compute the key */
export interface SqlStatementCacheKeyData {
    /** The kind of statement */
    kind: SqlStatementKind;
    /** The schema name */
    schemaName: string;
    /** The name of the node factory */
    factoryName: string;
    /** Options that are specific to the statement kind */
    options: object;
}

/**
 * Options passed to statementCache.fetch.
 */
export interface SqlStatementCacheFetchOptions<T> {
    /** Returns the key */
    getKeyData: () => SqlStatementCacheKeyData;
    /** Builds the statement. Only called if key is not found in the cache */
    buildStatement: () => T;
}

/**
 * Statement cache.
 * One instance per application: application.sqlStatementCache.
 * @internal
 */
export class SqlStatementCache {
    private readonly lruCache: LRUCache<string, CachedStatement> | undefined;

    constructor() {
        const size = ConfigManager.current.storage?.sql?.sqlStatementCacheSize ?? 1000;
        if (size > 0) this.lruCache = new LRUCache(size);
    }

    /**
     * Fetch a cache entry.
     * The key will be obtained from options.getKey().
     * If the statement is not found in the cache, it will be built with options.build().
     */
    fetch<T extends CachedStatement>(options: SqlStatementCacheFetchOptions<T>): T {
        // If cache size is configured to 0 we don't need a key.
        if (!this.lruCache) return options.buildStatement();

        const keyData = options.getKeyData();
        // We hash to keep the key short (64 hex chars).
        // We don't really need a crypto hash here. All we need is a collision free hash.
        // If this becomes a perf hotspot we'll review this.
        const key = crypto.createHash('sha256').update(JSON.stringify(keyData)).digest('hex');

        const cached = this.lruCache.get(key);
        if (cached) return cached as T;

        const statement = options.buildStatement();
        this.lruCache.set(key, statement);
        return statement;
    }
}
