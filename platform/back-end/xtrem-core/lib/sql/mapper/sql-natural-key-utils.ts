import { Dict } from '@sage/xtrem-shared';
import { Table } from '../schema/table';
import { SqlContext } from '../sql-context';

export class SqlNaturalKeyUtils {
    /**
     * Builds all the parts needed to build a SELECT query where all the natural keys will be returned as 'foo|bar'.
     * This function will take care of base tables (joins and aliases will be created).
     * Returns:
     *   - aliasedColumnNames: the name of all the columns, with aliases (t1.a AS a, t2.b AS b, t1.c || '|' || t2.d AS c, ...)
     *   - aliases: list of created aliases, indexed by table name
     *   - joins: list of joins required to follow the references / base relations
     *   - args: args to be used by the query (will by used to store the constructor names when dealing with base tables)
     * @param schemaName
     * @param table
     * @param columnNamesToQuery the columns to query from the table (a column can be either from the table or from its base tables)
     * @param options
     */
    static getSqlQueryPartsWithNaturalKeys(
        schemaName: string,
        table: Table,
        columnNamesToQuery: string[],
        options: {
            /**
             * The alias already assigned to the table
             */
            tableAlias?: string;
            /**
             * When dealing with base tables, should the name of the constructor be inserted in
             * the LEFT JOIN clause (true) or inserted in the args array (false) ?
             */
            doNotUseArgsForConstructors?: boolean;
            /**
             * When chaining many calls to the function in the same context, you can pass the aliases, joins, args returned by the previous
             * calls.
             */
            aliases?: Dict<string>;
            /**
             * When chaining many calls to the function in the same context, you can pass the aliases, joins, args returned by the previous
             * calls.
             */
            joins?: string[];
            /**
             * When chaining many calls to the function in the same context, you can pass the aliases, joins, args returned by the previous
             * calls.
             */
            args?: any[];
        } = {},
    ): {
        columns: {
            /**
             * The column with the 'AS'. Something like "t2.name || '|' || t1.name || '|' || t1.action AS operation"
             */
            aliased: string;
            /**
             * The 'simple' column name (something like 'operation')
             */
            unaliased: string;
        }[];
        aliases: Dict<string>;
        joins: string[];
        args: any[];
    } {
        const aliases: Dict<string> = options.aliases || {};
        const joins: string[] = options.joins || [];
        const args: any[] = options.args || [];
        const columns = columnNamesToQuery.map(columnName => {
            return {
                aliased: `${this._getAliasedColumnForSqlQuery({
                    schemaName,
                    aliases,
                    args,
                    joins,
                    table,
                    tableAlias: options.tableAlias,
                    columnName,
                    doNotUseArgsForConstructors: !!options.doNotUseArgsForConstructors,
                })} AS ${SqlContext.escape(columnName)}`,
                unaliased: SqlContext.escape(columnName),
            };
        });

        return {
            columns,
            aliases,
            joins,
            args,
        };
    }

    /**
     * Returns the column name (with its alias) that should be used to in a SQL query.
     * If needed (column belongs to a base table, references, ...), some joins will be generated and some new aliases will be created
     */
    private static _getAliasedColumnForSqlQuery(ctx: {
        /**
         * The schema name
         */
        schemaName: string;
        /**
         * The current table to process
         */
        table: Table;
        /**
         * The alias for the current table
         */
        tableAlias?: string;
        /**
         * The column name
         */
        columnName: string;
        /**
         * The first table in the recursive chain
         */
        firstTable?: Table;
        /**
         * The list of already allocated aliases (indexed by table name) - new items will be added to this list, if needed.
         */
        aliases: Dict<string>;
        /**
         * The list of already allocated joins - new items will be added to this list, if needed.
         */
        joins: string[];
        /**
         * The arguments to be used to execute the SELECT query - new items will be added to this list, if needed.
         */
        args: any[];
        /**
         * When dealing with base tables, should the name of the constructor be inserted in
         * the LEFT JOIN clause (true) or inserted in the args array (false) ?
         */
        doNotUseArgsForConstructors: boolean;
    }): string {
        const firstTable = ctx.firstTable || ctx.table;
        const tableAlias = ctx.tableAlias || 't0';

        const getOrCreateTableAlias = (table: Table, joinProvider: (alias: string) => string): string => {
            let alias = ctx.aliases[table.name];
            if (alias) return alias;

            // No alias already exists for this table, let's create a new one, starting from 't1'
            alias = `t${Object.keys(ctx.aliases).length + 1}`;
            ctx.aliases[table.name] = alias;
            // Retrieve the definition of the SQL join from the caller
            const join = joinProvider(alias);
            if (table.isSharedByAllTenants) {
                ctx.joins.push(join);
            } else {
                ctx.joins.push(`${join} AND ${alias}._tenant_id=${tableAlias}._tenant_id`);
            }
            return alias;
        };

        const c = ctx.table.columnsByColumnName[ctx.columnName];
        if (c == null) {
            if (ctx.table.baseTable == null) {
                throw new Error(`Could not find column ${ctx.columnName} from the table ${firstTable.name}`);
            }
            const baseTable = ctx.table.baseTable;
            const baseTableAlias = getOrCreateTableAlias(baseTable, alias => {
                let constructorValue: string;
                if (ctx.doNotUseArgsForConstructors) {
                    constructorValue = ctx.table.factory.name;
                } else {
                    ctx.args.push(ctx.table.factory.name);
                    constructorValue = `$${ctx.args.length}`;
                }
                return ` LEFT JOIN ${ctx.schemaName}.${baseTable.name} ${alias} ON ${alias}._id=${tableAlias}._id AND ${alias}._constructor=${constructorValue}`;
            });
            return this._getAliasedColumnForSqlQuery({
                ...ctx,
                ...{
                    firstTable,
                    table: baseTable,
                    tableAlias: baseTableAlias,
                },
            });
        }

        const property = c.property;
        if (!property) throw new Error(`The column ${ctx.table.name}.${ctx.columnName} is not bound to any property`);
        if (property.isReferenceProperty()) {
            // when dealing with references, here is what could happen :
            // for instance, when reloading data for Journal, the natural key is ['legislation', 'id']
            // but legislation is a reference to the reference table.
            // The natural key of a journal row will be something like FR|ACH but a classic SELECT
            // would return 4|ACH as the FR legislation is stored as a reference (here legislation(4) === 'FR')
            // So, when we have references, we have to use a join on the target table to get the value that will be used
            // in the natural key (here, the 'FR' bound to legislation(4))
            const targetFactory = property.targetFactory;
            if (!targetFactory.naturalKey || targetFactory.naturalKey.length === 0) {
                throw new Error(
                    `The node ${targetFactory.name} has no natural key but is referenced in the natural key of the node ${ctx.table.factory.name}`,
                );
            }
            const targetTable = targetFactory.table;
            const targetTableAlias = getOrCreateTableAlias(
                targetTable,
                alias =>
                    ` LEFT JOIN ${ctx.schemaName}.${
                        targetTable.name
                    } ${alias} ON ${alias}._id=${tableAlias}.${SqlContext.escape(ctx.columnName)}`,
            );

            return targetFactory.naturalKey
                .map(propName => {
                    const p = targetFactory.propertiesByName[propName];
                    if (p.columnName == null) throw new Error(`No column bound to ${targetFactory.name}.${propName}`);
                    return this._getAliasedColumnForSqlQuery({
                        ...ctx,
                        ...{
                            firstTable,
                            table: targetTable,
                            tableAlias: targetTableAlias,
                            columnName: p.columnName,
                        },
                    });
                })
                .join(" || '|' || "); // SQL concatenation (the result will look like foo|bar)
        }
        const escapedColumnName = SqlContext.escape(c.columnName);
        return `${tableAlias}.${escapedColumnName}`;
    }
}
