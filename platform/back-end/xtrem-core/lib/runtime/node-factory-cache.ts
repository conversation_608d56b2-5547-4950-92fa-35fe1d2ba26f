import { AnyValue } from '@sage/xtrem-async-helper';
import { DebugMetrics, recordCustomEventToNewrelic } from '@sage/xtrem-metrics';
import { Dict } from '@sage/xtrem-shared';
import { GetValueCallback } from '../cache/global-cache';
import { Context } from './context';
import { loggers } from './loggers';
import { NodeFactory } from './node-factory';

/**
 * @internal
 * This class caches results of SQL queries.
 *
 * The `factory.cache` of every node factory is an instance of this class.
 *
 * This class only caches in the application's global cache if the `isCached` attribute of the node decorator is set.
 * If not set, this class is just a stub.
 */

export interface NodeFactoryCacheCounters {
    ignored: number;
    countQueries: number;
    fetches: number;
    misses: number;
    invalidations: number;
}

const emptyCounters: NodeFactoryCacheCounters = {
    ignored: 0,
    countQueries: 0,
    fetches: 0,
    misses: 0,
    invalidations: 0,
};

export class NodeFactoryCache {
    // true if counters have changed since last posted to new relic
    #countersDirty = false;

    // Performance counters
    #counters: NodeFactoryCacheCounters = { ...emptyCounters };

    constructor(readonly factory: NodeFactory) {
        this.factory.application.globalCache.setCategoryTokens(this.cacheCategory, this.factory.getAllTableNames());
    }

    get cacheCategory(): string {
        return `$${this.factory.isSharedByAllTenants ? 'SHARED_NODE' : 'NODE'}.${this.factory.name}`;
    }

    /** fetches data through the cache */
    async fetch<T extends AnyValue>(
        context: Context,
        options: {
            getKey: () => string;
            getValue: GetValueCallback<T>;
            // isolateInContext has to bet set by the caller
            isolateInContext: boolean;
            // Should the cache be ignored and getValue be invoked ?
            ignoreCache?: boolean;
        },
    ): Promise<T> {
        // If factory is not cached, read directly.
        // We are not passing filters because factory.createNodeQuery will set them.
        if (!this.factory.isCached || options.ignoreCache) {
            this.#counters.ignored += 1;
            return (await options.getValue()).value;
        }

        const getValue: GetValueCallback<T> = () => {
            this.#counters.misses += 1;
            return DebugMetrics.withMetrics('nodeCache', `${this.factory.name}.miss`, () => options.getValue());
        };

        this.#counters.fetches += 1;
        // Read with context cache
        return DebugMetrics.withMetrics('nodeCache', `${this.factory.name}.query`, () =>
            context.getCachedValue({
                category: this.cacheCategory,
                key: options.getKey(),
                getValue,
                isolateInContext: options.isolateInContext,
                cacheInMemory: this.factory.isCached,
                ttlInSeconds: 3600,
            }),
        );
    }

    /**
     * Invalidates the factory's category in the cache.
     * This is called when records are inserted, updated and deleted in the factory's table
     */
    async invalidate(context: Context): Promise<void> {
        if (!this.factory.isCached) return;
        this.#counters.invalidations += 1;
        await context.invalidateCachedCategory(this.cacheCategory, { skipNotify: true });
    }

    /** Returns the performance counters for the factory's cache */
    get counters(): NodeFactoryCacheCounters & {
        totalQueries: number;
        totalMisses: number;
        totalHitRate: string;
        fetchHitRate: string;
    } {
        const counters = this.#counters;
        const totalMisses = counters.ignored + counters.misses + counters.countQueries;
        const totalQueries = counters.ignored + counters.fetches + counters.countQueries;
        const percent = (numer: number, denom: number): string => `${Math.round((100 * numer) / (denom || 1))}%`;
        const totalHitRate = percent(totalQueries - totalMisses, totalQueries);
        const fetchHitRate = percent(counters.fetches - counters.misses, counters.fetches);

        return {
            totalMisses,
            totalQueries,
            totalHitRate,
            fetchHitRate,
            ...counters,
        };
    }

    postToNewRelic(): void {
        if (!this.#countersDirty) return;
        loggers.performance.verbose(() => `Node cache: ${this.performanceSummary}`);
        try {
            recordCustomEventToNewrelic('NodeCache', { nodeName: this.factory.name, ...this.#counters });
        } catch ({ stack, message }) {
            // log and swallow, we don't want this event emiter to disturb the processing of the request
            loggers.performance.warn(`Failed to send NodeCache to newrelic ${stack || message}`);
        }
        this.#countersDirty = false;
    }

    get performanceSummary(): string {
        const counters = this.counters as unknown as Dict<number | string>;
        return `${this.factory.name}: ${Object.keys(counters)
            .map(k => `${k}: ${counters[k]}`)
            .join(', ')}`;
    }

    incrementCountQueryCounter(): void {
        this.#countersDirty = true;
        this.#counters.countQueries += 1;
    }

    incrementIgnoredCounter(): void {
        this.#countersDirty = true;
        this.#counters.ignored += 1;
    }
}
