import { ConfigManager } from '@sage/xtrem-config';
import { AnyValue, AsyncResponse, createDictionary, LogicError } from '@sage/xtrem-shared';
import * as crypto from 'crypto';
import { nanoid } from 'nanoid';
import { StringProperty } from '../properties';
import { Context } from './context';
import { SecretManager } from './secret-manager';

interface MapEntry {
    encryptedValue: string;
    category?: string;
}

/**
 * @internal
 * Context Vault is used to encrypt properties values on database. The context-vault API is responsible for encrypting and decrypting value.
 * Context Vault can be used to store external passwords, like, Intacct for instance.
 */
export class ContextVault {
    #mappedValues = createDictionary<MapEntry>();

    static readonly #cachedMappedValues = createDictionary<MapEntry>();

    private readonly ivLength = 16;

    private readonly cryptoAlgorithm = 'aes-256-gcm';

    constructor(private readonly context: Context) {}

    recordValue(encryptedValue: string): string {
        if (!encryptedValue) return encryptedValue;
        const key = nanoid();
        this.#mappedValues[key] = { encryptedValue };
        return key;
    }

    getValueFromVault(key: string): string {
        if (!key) return key;
        const entry = this.#mappedValues[key] ?? ContextVault.#cachedMappedValues[key];
        if (!entry) throw new LogicError(`invalid vault key: ${key}`);
        return entry.encryptedValue;
    }

    /**
     *  Encrypt your message with cipheriv
     * @param val
     * @returns
     */
    async encrypt(val: string): Promise<string> {
        if (!val) return val;
        const iv = crypto.randomFillSync(Buffer.allocUnsafe(16));
        if (iv.length !== this.ivLength) {
            throw new LogicError(`encrypt iv must be exactly 16 bytes, but received ${iv.length}`);
        }

        const cipher = crypto.createCipheriv(this.cryptoAlgorithm, await this.getEncryptionKey(), iv);

        let encryptedText = cipher.update(val);
        encryptedText = Buffer.concat([encryptedText, cipher.final()]);

        return `${iv.toString('hex')}:${encryptedText.toString('hex')}`;
    }

    /**
     * Decrypt the recorded value
     * @param val
     * @returns
     */
    async decrypt(val: string): Promise<string> {
        if (!val) return val;
        const [iv, bytes] = this.getValueFromVault(val)
            .split(':')
            .map(s => Buffer.from(s, 'hex'));

        const decipher = crypto.createDecipheriv(this.cryptoAlgorithm, await this.getEncryptionKey(), iv);

        return decipher.update(bytes).toString();
    }

    /**
     * Encrypts a string value using the vault encryption algorithm.
     * @param val The string value to encrypt.
     * @returns A Promise that resolves to the encrypted string.
     */
    async encrypt64(val: string): Promise<string> {
        const iv = crypto.randomFillSync(Buffer.alloc(this.ivLength));
        const key = await this.getEncryptionKey();
        const cipher = crypto.createCipheriv(this.cryptoAlgorithm, key, iv);
        let encrypted = cipher.update(val);
        encrypted = Buffer.concat([encrypted, cipher.final()]);
        return [iv.toString('base64'), encrypted.toString('base64'), cipher.getAuthTag().toString('base64')].join('.');
    }

    /**
     * Decrypts a string value using the vault encryption algorithm.
     * @param encrypted The string value to decrypt.
     * @returns A Promise that resolves to the decrypted string.
     */
    async decrypt64(encrypted: string): Promise<string> {
        const [ivString, encString, authTagString] = encrypted.split('.');
        const iv = Buffer.from(ivString, 'base64');
        const enc = Buffer.from(encString, 'base64');
        const authTag = Buffer.from(authTagString, 'base64');

        const key = await this.getEncryptionKey();
        const decipher = crypto.createDecipheriv(this.cryptoAlgorithm, key, iv);
        decipher.setAuthTag(authTag);
        const decrypted = decipher.update(enc);
        return Buffer.concat([decrypted, decipher.final()]).toString();
    }

    private async getEncryptionKey(): Promise<Buffer> {
        const config = {
            xtremEnv: process.env.XTREM_ENV,
            clusterId: this.context.clusterId,
            // secretCacheTimeoutInMinutes defaults is 5 * 60 * 1000
        };
        const key = await SecretManager.getTenantEncryptionKey(config, this.context.tenantId || '*');
        return Buffer.from(key, 'base64');
    }

    static getConfigSecretValue(name: string): AsyncResponse<string> {
        const config = {
            xtremEnv: process.env.XTREM_ENV,
            clusterId: ConfigManager.current.clusterId,
            // secretCacheTimeoutInMinutes defaults is 5 * 60 * 1000
        };
        return SecretManager.getConfigSecretValue(config, name);
    }

    /**
     * Cache the values for the given properties to retreive them later in another context
     * @param category
     * @param value
     * @param properties
     * @returns
     */
    cacheValues(category: string, value: AnyValue, properties: StringProperty[]): void {
        if (!properties || properties.length === 0) return;
        // eslint-disable-next-line no-restricted-syntax
        for (const prop of properties) {
            const key = (value as any)[prop.name];
            // expecting a nanoid key
            if (typeof key === 'string' && key.length === 21) {
                const entry = this.#mappedValues[key];
                if (!entry) return;
                entry.category = category;
                ContextVault.#cachedMappedValues[key] = entry;
            }
        }
    }

    /**
     * Invalidate the cache for an entire category or for a specific key of that category
     * @param category
     * @param key
     */
    static invalidateCache(category: string, key?: string): void {
        if (key) {
            delete ContextVault.#cachedMappedValues[key];
        } else {
            // eslint-disable-next-line no-restricted-syntax
            for (const k of Object.keys(ContextVault.#cachedMappedValues)) {
                const entry = ContextVault.#cachedMappedValues[k];
                if (entry.category === category) {
                    delete ContextVault.#cachedMappedValues[k];
                }
            }
        }
    }

    /**
     * Generates an HMAC (Hash-based Message Authentication Code) using the SHA-256 algorithm.
     *
     * @param val - The input string to be hashed.
     * @returns A promise that resolves to the generated HMAC as a hexadecimal string.
     */
    async createHmac(val: string): Promise<string> {
        const key = await this.getEncryptionKey();
        return crypto.createHmac('sha256', key).update(val).digest('hex');
    }
}
