import { asyncArray } from '@sage/xtrem-async-helper';
import { AnyValue, AsyncResponse, Dict } from '@sage/xtrem-shared';
import { performance } from 'perf_hooks';

export interface BasicProfilerItem {
    name?: string;
    hits?: number;
    duration?: number;
    children: BasicProfilerItem[];
}
class BasicProfiler {
    private _items = {} as Dict<BasicProfilerItem>;

    private _stack: BasicProfilerItem[] = [{ children: [] }];

    async measure<T extends AnyValue | void>(name: string, cb: () => AsyncResponse<T>): Promise<T> {
        const current = this._stack[0];

        let item = this._items[name];
        if (!item) {
            this._items[name] = { name, children: [] };
            item = this._items[name];
            current.children.push(item);
        }

        this._stack.unshift(item);
        item.hits = item.hits ?? 0;
        item.hits += 1;
        const start = performance.now();
        try {
            return await cb();
        } finally {
            item.duration = item.duration ?? 0;
            item.duration += performance.now() - start;
            this._stack.shift();
        }
    }

    async walk(cb: (path: string[], item: BasicProfilerItem) => AsyncResponse<void>): Promise<void> {
        const walker = async (path: string[] | null, item: BasicProfilerItem): Promise<void> => {
            const itemPath = path == null ? [] : [...path, item.name || ''];
            if (path != null) await cb(itemPath, item);
            await asyncArray(item.children).forEach(child => walker(itemPath, child));
        };
        await walker(null, this._stack[0]);
    }
}

export const basicProfiler = new BasicProfiler();
