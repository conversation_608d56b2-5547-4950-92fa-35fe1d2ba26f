/** @ignore */ /** */
import { Logger } from '@sage/xtrem-log';

export const loggers = {
    application: Logger.getLogger(__filename, 'application'),
    globalCache: Logger.getLogger(__filename, 'global-cache'),
    authorization: Logger.getLogger(__filename, 'authorization'),
    graphQl: Logger.getLogger(__filename, 'graphql'),
    sql: Logger.getLogger(__filename, 'sql'),
    sqlMapper: Logger.getLogger(__filename, 'sql-mapper'),
    runtime: Logger.getLogger(__filename, 'runtime'),
    nodeFactory: Logger.getLogger(__filename, 'node-factory'),
    core: Logger.getLogger(__filename, 'core'),
    globalLock: Logger.getLogger(__filename, 'global_lock'),
    property: Logger.getLogger(__filename, 'property'),
    operation: Logger.getLogger(__filename, 'operation'),
    upgrade: Logger.getLogger(__filename, 'upgrade'),
    tenant: Logger.getLogger(__filename, 'tenant'),
    queue: Logger.getLogger(__filename, 'queue'),
    pubsub: new Logger(__filename, 'pubsub'),
    packageManager: new Logger(__filename, 'package-manager'),
    test: new Logger(__filename, 'test'),
    performance: new Logger(__filename, 'performance'),
    dump: Logger.getLogger(__filename, 'dump'),
    compress: Logger.getLogger(__filename, 'compress'),
    interop: Logger.getLogger(__filename, 'interop'),
    fileStorage: Logger.getLogger(__filename, 'file-storage'),
};
