import { hostname } from 'os';
import { loggers } from './loggers';

export abstract class ContainerManager {
    private static _containerId: string | undefined;

    /**
     * The id of the current container
     */
    static get containerId(): string {
        if (ContainerManager._containerId == null) {
            ContainerManager._containerId = `${hostname()}-${process.pid}`;
            loggers.runtime.info(`Container ${ContainerManager._containerId} registered`);
        }
        return ContainerManager._containerId;
    }
}
