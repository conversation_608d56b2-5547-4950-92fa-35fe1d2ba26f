import { AnyValue, AsyncResponse } from '@sage/xtrem-shared';
import { Context } from './context';

export async function withAdvisoryLock<T extends AnyValue>(
    context: Context,
    lockId: number,
    body: (context: Context) => AsyncResponse<T>,
): Promise<T> {
    try {
        await context.executeSql(`SELECT PG_ADVISORY_LOCK(${lockId});`, []);
        return await body(context);
    } finally {
        await context.executeSql(`SELECT PG_ADVISORY_UNLOCK(${lockId});`, []);
    }
}
