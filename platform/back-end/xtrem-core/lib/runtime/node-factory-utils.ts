/** @ignore */ /** */
import { AnyValue, Dict } from '@sage/xtrem-shared';
import * as json5 from 'json5';
import * as _ from 'lodash';
import { Parameter, PlainOperationDecorator, TypeName } from '../decorators';
import { Property } from '../properties';
import { loggers } from './loggers';
import { NodeFactory } from './node-factory';
import { NodeIndex } from './node-factory-types';

const logger = loggers.nodeFactory;

// "foo" must be escaped as \"foo\" in graphiql. Not nice!
// So we accept JSON5 as an alternative.
// Note: this is just a convenience for interactive users.
// Programmatic clients should use JSON for performance and robustness.
/** @internal */
export function friendlyJsonParse<T extends AnyValue = AnyValue>(str: string): T {
    // auto-detect: if first character after the { is not a ", if it is use JSON. Otherwise use JSON5
    return !/^[\s\n\r]*{[\s\n\r]*["]/.test(str) ? json5.parse(str) : JSON.parse(str);
}

export function sameIndexProperties(index1: NodeIndex, index2: NodeIndex): boolean {
    const keys1 = Object.keys(index1.orderBy);
    const keys2 = Object.keys(index2.orderBy);
    return keys1.every(key => keys2.includes(key)) && keys2.every(key => keys1.includes(key));
}

export function arraysAreEqual(a: any[], b: any[]): boolean {
    if (a.length !== b.length) return false;
    return a.every((value, index) => value === b[index]);
}

// Recursively set childFactories' table attribute
/** @internal */
export function setChildFactoriesTable(factory: NodeFactory): void {
    if (factory.decorators.childFactories) {
        factory.decorators.childFactories.forEach(childFactory => {
            childFactory.table = factory.table;
            setChildFactoriesTable(childFactory);
        });
        factory.decorators.childFactories = undefined;
    }
}

// Schema descriptions
// For now, we are just reformatting the name as the titles that we get from ATEXTE are out of sync with
// our renaming, and we are adding the code to help classic developers
export function getSchemaDescription(name: string, code: string | undefined): string {
    const words = _.kebabCase(name).replace(/-/g, ' ');
    const title = `${words[0].toUpperCase()}${words.substr(1)}`;
    return code ? `${title} (${code})` : title;
}

export function getPropertySchemaDescription(property: Property): string {
    return getSchemaDescription(property.name, property.columnName);
}

/**
 * Ensures that a setup factory does not have more than one unique index.
 */
export function ensureNoMultipleUniqueIndexes(factory: NodeFactory): void {
    if (!factory.isSetupNode) {
        // We are only interested in setup nodes as having multiple unique indexes may prevent from reloading
        // setup data, when applying an upgrade.
        return;
    }
    if (factory.indexes == null || factory.storage !== 'sql') return;
    const uniqueIndexes = factory.indexes.filter(index => index.isUnique && !!index.name);
    if (uniqueIndexes.length <= 1) return;

    const indexAsString = (index: NodeIndex): string => {
        const indexName = index.isNaturalKey ? 'NaturalKey' : index.name;
        return `${indexName}(${Object.keys(index.orderBy).join(',')})`;
    };

    // if we have 2 unique indexes, I1:(A,B) and I2:(A,B,C), then it's OK because
    // I2 contains I1, but if we have 2 unique indexes I1:(A,B) and I2:(A,C), then it's an error
    // Same, if we have 2 unique indexes I1:(A,B) and I2:(B,A), then it's OK
    uniqueIndexes.forEach((index1, idxInList) => {
        for (let j = idxInList + 1; j < uniqueIndexes.length; j++) {
            const index2 = uniqueIndexes[j];
            const propertyNames1 = Object.keys(index1.orderBy);
            const propertyNames2 = Object.keys(index2.orderBy);
            const diffs1To2 = _.difference(propertyNames1, propertyNames2);
            const diffs2To1 = _.difference(propertyNames2, propertyNames1);
            if (diffs1To2.length > 0 && diffs2To1.length > 0) {
                logger.warn(
                    `${factory.name} : multiple unique indexes are forbidden for setup nodes. (${indexAsString(index1)} / ${indexAsString(index2)})`,
                );
            }
        }
    });
}

export type FriendlyParameter =
    | (Omit<Omit<Omit<Omit<Parameter, 'node'>, 'dataType'>, 'item'>, 'properties'> & {
          node?: string;
          dataType?: string;
          item?: FriendlyParameter;
          properties?: Dict<FriendlyParameter>;
      })
    | TypeName;

function makeFriendlyParameter(parameter: Parameter | TypeName): FriendlyParameter {
    if (typeof parameter === 'string') {
        return parameter;
    }

    switch (parameter.type) {
        case 'object': {
            return {
                ...parameter,
                properties: _.mapValues(parameter.properties, value => {
                    return makeFriendlyParameter(value);
                }),
            };
        }
        case 'array':
            return {
                ...parameter,
                item: makeFriendlyParameter(parameter.item),
            };
        case 'reference':
        case 'instance': {
            return {
                ...parameter,
                node: parameter.node().name,
            };
        }
        default:
            return {
                ...parameter,
                dataType: parameter.dataType ? parameter.dataType().name : undefined,
            };
    }
}

export function friendlyOperationSignature(operation: PlainOperationDecorator): {
    parameters: FriendlyParameter[];
    return: FriendlyParameter;
} {
    const parameters = operation.parameters.map(p => makeFriendlyParameter(p));
    const operationReturn = makeFriendlyParameter(operation.return);
    return { parameters, return: operationReturn };
}
