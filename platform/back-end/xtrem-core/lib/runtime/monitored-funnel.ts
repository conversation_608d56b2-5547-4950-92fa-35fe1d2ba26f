import { Funnel, funnel } from '@sage/xtrem-async-helper';
import { PromGauge as Gauge, allMetricsEnabled } from '@sage/xtrem-metrics';
import { Dict, Maybe, createDictionary, isEnvVarTrue } from '@sage/xtrem-shared';

const funnelMetricsEnabled = allMetricsEnabled || isEnvVarTrue(process.env.XTREM_FUNNEL_METRICS_ENABLED);

interface MonitoredFunnel {
    funnelName: string;
    funnel: Funnel;
    highWaterMark: number;
}

interface MonitoredFunnelMetrics {
    highWaterMark: Gauge<'funnelName'>;
    length: Gauge<'funnelName'>;
    ratio: Gauge<'funnelName'>;
}

const monitoredFunnelMap: Dict<MonitoredFunnel> = createDictionary();

const ensureInitialized = ((): (() => MonitoredFunnelMetrics) => {
    let metrics: Maybe<MonitoredFunnelMetrics>;
    return () => {
        if (metrics != null) {
            return metrics;
        }

        metrics = {
            highWaterMark: new Gauge({
                name: 'xtrem_funnel_queue_hwm',
                help: 'The high water mark of the funnel queues',
                labelNames: ['funnelName'],
                collect() {
                    Object.values(monitoredFunnelMap).forEach(m => {
                        const funnelName = m.funnelName;
                        this.set({ funnelName }, m.funnel.highWaterMark);
                        metrics?.length.set({ funnelName }, m.highWaterMark);
                        metrics?.ratio.set({ funnelName }, m.highWaterMark / m.funnel.capacity);
                        m.highWaterMark = 0;
                    });
                },
            }),
            length: new Gauge({
                name: 'xtrem_funnel_queue_length',
                help: 'The length of the funnel queues',
                labelNames: ['funnelName'],
            }),
            ratio: new Gauge({
                name: 'xtrem_funnel_queue_ratio',
                help: 'The usage ratio of the funnel queues (length / max capacity)',
                labelNames: ['funnelName'],
            }),
        };
        return metrics;
    };
})();

export function monitoredFunnel(funnelName: string, max = 1): Funnel {
    if (!funnelMetricsEnabled) {
        return funnel(max);
    }
    ensureInitialized();
    const fun = funnel(max, () => {
        const monitored = monitoredFunnelMap[funnelName];
        const queueLength = monitored.funnel.length;
        if (queueLength > monitored.highWaterMark) {
            monitored.highWaterMark = queueLength;
        }
    });
    monitoredFunnelMap[funnelName] = { funnelName, funnel: fun, highWaterMark: 0 };
    return fun;
}
