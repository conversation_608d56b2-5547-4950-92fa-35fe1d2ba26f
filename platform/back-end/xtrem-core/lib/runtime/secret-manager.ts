import * as secret from '@sage/xtrem-secret';
import { SecretConfig } from '@sage/xtrem-secret';

export class SecretManager {
    static getTenantEncryptionKey(config: SecretConfig, tenantId: string): Promise<string> {
        const secretHandler: secret.SecretHandler = secret.getSecretHandler(config);
        return secretHandler.getTenantEncryptionKey(tenantId);
    }

    static getConfigSecretValue(config: SecretConfig, name: string): Promise<string> {
        return secret.getSecretHandler(config).getConfigSecretValue(name);
    }
}
