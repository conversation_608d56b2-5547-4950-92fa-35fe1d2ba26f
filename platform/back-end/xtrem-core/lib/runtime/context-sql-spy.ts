import { ConfigManager } from '@sage/xtrem-config';
import { CustomMetrics } from '@sage/xtrem-metrics';
import { AnyValue, Dict } from '@sage/xtrem-shared';
import { NodeRuleName } from '../decorators/node-decorators/node-events';
import { PropertyRuleName } from '../decorators/property-decorators/base-property-events';
import { NodeFactory } from '../system-exports';
import { Context } from './context';

export type SqlVerb = 'SELECT' | 'INSERT' | 'UPDATE' | 'DELETE';

export class ContextSqlSpy {
    private readonly startMillis: number;

    private readonly isEnabled: boolean;

    constructor(private readonly context: Context) {
        this.isEnabled = !!ConfigManager.current.storage?.prefetch?.logCounters;
        this.startMillis = Date.now();
        if (this.isEnabled)
            this.context.on('closed', () =>
                ContextSqlSpy.logCounters('CONTEXT SQL COUNTERS', this.perfCounters, this.startMillis),
            );
    }

    private static globalPerfCounters = {} as Dict<number>;

    private perfCounters = {} as Dict<number>;

    private ruleKey = '';

    private static logCounters(message: string, counters: Dict<number>, startMillis: number): void {
        if (Object.keys(counters).length === 0) return;
        const sep = '-'.repeat(50);
        // eslint-disable-next-line no-console
        console.log(sep);
        // eslint-disable-next-line no-console
        console.log(message);
        Object.entries(counters)
            .sort((e1, e2) => e1[1] - e2[1])
            // eslint-disable-next-line no-console
            .forEach(e => console.log(String(e[1]).padStart(6), e[0]));
        const total = Object.values(counters).reduce((acc, value) => acc + value, 0);
        // eslint-disable-next-line no-console
        console.log(sep);
        // eslint-disable-next-line no-console
        console.log(String(total).padStart(6), 'TOTAL');
        // eslint-disable-next-line no-console
        console.log(String(Date.now() - startMillis).padStart(6), 'MILLIS');
        // eslint-disable-next-line no-console
        console.log(sep);
    }

    incrementCounter(factory: NodeFactory, verb: SqlVerb): void {
        if (!this.isEnabled) return;
        const key = `${verb} ${factory.name.padEnd(40)} ${this.ruleKey}`;

        const counter = this.perfCounters[key] ?? 0;
        this.perfCounters[key] = counter + 1;

        const globalCounter = ContextSqlSpy.globalPerfCounters[key] ?? 0;
        ContextSqlSpy.globalPerfCounters[key] = globalCounter + 1;

        this.context.prefetcher.spy(factory, key, counter + 1);
    }

    async withRuleMetrics<ResultT extends AnyValue>(
        metrics: { nodeName: string; propertyName: string; ruleName: NodeRuleName | PropertyRuleName },
        body: () => Promise<ResultT>,
    ): Promise<ResultT> {
        const oldRuleKey = this.ruleKey;
        this.ruleKey = metrics.propertyName
            ? `${metrics.nodeName}.${metrics.propertyName}/${metrics.ruleName}`
            : `${metrics.nodeName}.${metrics.ruleName}`;
        try {
            return await CustomMetrics.rules.withMetrics(metrics, body);
        } finally {
            this.ruleKey = oldRuleKey;
        }
    }

    static {
        let timer: NodeJS.Timeout;

        ConfigManager.emitter.on('loaded', () => {
            // Restart the timer in case timer interval changed
            if (timer) clearInterval(timer);

            if (ConfigManager.current.storage?.prefetch?.logCounters) {
                const startMillis = Date.now();
                timer = setInterval(
                    () => {
                        this.logCounters('GLOBAL SQL COUNTERS', this.globalPerfCounters, startMillis);
                    },
                    (ConfigManager.current.storage?.prefetch?.logCountersIntervalInSeconds ?? 5) * 1000,
                );
            }
        });
    }
}
