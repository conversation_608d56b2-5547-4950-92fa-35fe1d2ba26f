/** @packageDocumentation @module runtime */
import { Config, DeploymentMode } from '@sage/xtrem-shared';

export { DeploymentMode };

export class ConfigurationService {
    constructor(private readonly config: Config) {}

    /** deployment mode: allows applicative code to test if code is executing in production or development mode  */
    get deploymentMode(): DeploymentMode {
        // assumes production by default as this is the safest option
        return this.config.deploymentMode ?? 'production';
    }

    /** return the configuration data for a given package
     * @param name: the name of the package. For example '@sage/x3-sales'
     * @param defaultValue: the default value if there is no data in the config file.
     */
    getPackageConfig<T, DefT extends T | undefined = T | undefined>(name: string, defaultValue?: DefT): DefT {
        return this.config.packages?.[name] ?? defaultValue;
    }

    /** return the visible name of the product */
    getProductName(): string {
        return this.config.productName || 'Sage Distribution and Manufacturing Operations';
    }
}
