import { ConfigManager } from '@sage/xtrem-config';
import { ApplicationType } from '../application';
import { loggers } from './loggers';

const dnscache = require('dnscache');

interface Argv {
    _: string[];
    $cli?: {
        plugins?: {
            name?: string;
        }[];
    };
}

export interface RunningContextInitOptions {
    argv: Argv;
}

export class RunningContext {
    static readonly defaultNatIpAddresses = '0.0.0.0';

    #option: RunningContextInitOptions;

    #isServicesMode = false;

    #natIpAddressesRaw = RunningContext.defaultNatIpAddresses;

    #natIpAddresses: string[] = [];

    #natIpIndex = 0;

    #isConfigured = false;

    get isServicesMode(): boolean {
        return this.#isServicesMode;
    }

    get inceptionCommand(): string {
        return this.#option.argv._[0];
    }

    get natIpAddress(): string {
        if (this.#natIpAddressesRaw !== ConfigManager.current.system?.natIpAdresses) {
            this.initNatIpAddresses();
        }
        const ip = this.#natIpAddresses[this.#natIpIndex];
        if (this.#natIpAddresses.length > 1) {
            this.#natIpIndex = (this.#natIpIndex + 1) % this.#natIpAddresses.length;
        }
        return ip;
    }

    init(options: RunningContextInitOptions): void {
        if (this.#option) {
            throw new Error('Running context is already initialized');
        }
        this.#option = options;
        loggers.runtime.info(`Init running context for command '${this.inceptionCommand}'`);
        const dnsCacheOptions = {
            enable: true,
            ttl: 30, // Set TTL to 30 seconds
            cachesize: 1000, // Maximum number of entries to store in the cache
            ...ConfigManager.current.system?.dnsCache,
        };
        if (dnsCacheOptions.enable) {
            dnscache(dnsCacheOptions);
            loggers.runtime.info(
                `DNS cache enabled with options ${Object.entries(dnsCacheOptions)
                    .filter(([k]) => k !== 'enable')
                    .map(([k, v]) => `${k}=${v}`)
                    .join(', ')}`,
            );
        } else {
            loggers.runtime.info('DNS cache disabled');
        }
    }

    configure(applicationType?: ApplicationType): void {
        if (applicationType === 'service') {
            this.setServicesMode();
        }
        if (!this.#isConfigured) {
            this.initNatIpAddresses();
            this.#isConfigured = true;
        }
    }

    private setServicesMode(): void {
        this.#isServicesMode = true;
        ConfigManager.fixServicesConfig();
    }

    private initNatIpAddresses(): void {
        const config = ConfigManager.current;
        config.system = config.system ?? {};
        this.#natIpAddressesRaw = config.system.natIpAdresses || RunningContext.defaultNatIpAddresses;
        this.#natIpAddresses = this.#natIpAddressesRaw.split(',') ?? [];
        this.#natIpIndex = 0;
    }
}

export const globalRunningContext = new RunningContext();
