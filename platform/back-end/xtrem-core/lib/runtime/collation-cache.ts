import { funnel } from '@sage/xtrem-async-helper';
import { Context } from './context';

export class CollationCache {
    private static collations: Map<string, string>;

    static initFunnel = funnel(1);

    static async init(context: Context): Promise<void> {
        if (this.collations) return;

        await this.initFunnel(() => this.loadCollation(context));
    }

    private static async loadCollation(context: Context): Promise<void> {
        if (this.collations || context.withoutSqlConnection) return;
        // on PostgreSQL 15, colliculocale has been introduced to define the ICU locale ID, previously in PostgresSQL 13 it was in collcollate
        // see https://www.postgresql.org/docs/15/catalog-pg-collation.html
        // This request will work both on PG 13 and PG 15
        const collations = (
            await context.executeSql<{ collname: string; collcollate: string; colliculocale?: string }[]>(
                'SELECT * FROM pg_collation WHERE collprovider = $1',
                ['i'],
            )
        ).filter(collation => /^[a-z]{2}(-[A-Z]{2})$/.test(collation.colliculocale ?? collation.collcollate));

        this.collations = new Map(
            collations.map(collation => [collation.colliculocale ?? collation.collcollate, collation.collname]),
        );
    }

    static getCollation(locale: string, localeLanguage: string): string | undefined {
        if (!this.collations) return undefined; // Happens if called before init
        return this.collations.get(locale) || this.collations.get(localeLanguage);
    }
}
