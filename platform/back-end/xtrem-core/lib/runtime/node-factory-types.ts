/** @ignore */ /** */
import { AnyR<PERSON>ord, AnyValue } from '@sage/xtrem-async-helper';
import { BaseCollection } from '../collections';
import {
    MessageListener,
    NodeDecorator,
    NodeExtensionDecorator,
    NodeIndexDecorator,
    NotificationListener,
    OperationDecorator,
    PlainOperationDecorator,
} from '../decorators';
import { ForeignNodeProperty, ReferenceProperty } from '../properties';
import { Property } from '../properties/property';
import { Node } from '../ts-api';
import { NodeFactory } from './node-factory';
import { PropertyDecorator } from './property';
/** @internal */
export interface FindPropertyOptions {
    includeSystemProperties?: boolean;
}

/** @disabled_internal */
export interface ReferringProperty {
    targetFactory: NodeFactory;
    property: Property;
}

export interface ReversePropertyStruct {
    prop: ForeignNodeProperty;
    join: AnyRecord;
}

export interface FactoryLinkedList {
    previousLink?: FactoryLinkedList;
    targetFactory: NodeFactory;
    property: ReferenceProperty;
}

export interface FactoryNodeDecorator extends NodeDecorator {
    readonly name: string;
    baseNode?: FactoryNodeDecorator;
}

export interface FactoryExtensionDecorator extends NodeDecorator {
    readonly name: string;
    baseNode?: FactoryNodeDecorator;
}

/** @internal */
export interface FactoryDecorators {
    node: FactoryNodeDecorator;
    superDecorators?: FactoryDecorators;
    properties: PropertyDecorator[];
    mutations: PlainOperationDecorator[];
    queries: PlainOperationDecorator[];
    notificationListeners: NotificationListener[];
    messageListeners: MessageListener[];
    childFactories?: NodeFactory[];
}

type FilterBase<Base, Condition> = {
    [Key in keyof Base]: Base[Key] extends Condition ? Key : never;
};
type FilterKeys<Base, Condition> = NonNullable<FilterBase<Base, Condition>[keyof Base]>;

export interface NodeNamedExtensionDecorator extends NodeExtensionDecorator<Node> {
    name: string;
}

export interface FactoryExtensionDecorators {
    nodeExtension: NodeNamedExtensionDecorator;
    superDecorators?: FactoryDecorators;
    properties: PropertyDecorator[];
    mutations: PlainOperationDecorator[];
    queries: PlainOperationDecorator[];
    notificationListeners: NotificationListener[];
    messageListeners: MessageListener[];
}

type FactoryDecoratorsArrayKeys = FilterKeys<FactoryDecorators, any[]>;
type FactoryExtensionDecoratorsArrayKeys = FilterKeys<FactoryExtensionDecorators, any[]>;
type NodeFactoryDecoratorsDictKeys = FilterKeys<NodeFactory, any[]>;
export type FactoryDecoratorsDictArrayKeys = NonNullable<
    (FactoryDecoratorsArrayKeys | FactoryExtensionDecoratorsArrayKeys) & NodeFactoryDecoratorsDictKeys
>;

/** @internal */
export interface InternalOperationDecorator extends OperationDecorator<typeof Node, any> {
    name: string;
}

/** @internal */
export interface NodeConstructOptions {
    forUpdate?: boolean;
    isThunk?: boolean;
    isTransient?: boolean;
    isOnlyForDefaultValues?: boolean;
    isOnlyForDuplicate?: boolean;
    duplicates?: Node;
    collection?: BaseCollection;
    isOnlyForLookup?: boolean;
    isOld?: boolean;
}

export interface ErrorParameters {
    key: string;
    message: string;
    data?: object | AnyValue[];
    innerError?: Error;
}

/** @internal */
export interface NodeIndex extends NodeIndexDecorator {
    isVerified?: boolean;
}
