import { AnyValue, Dict } from '@sage/xtrem-shared';
import { TypeName, TypedPropertyDecorator } from '../decorators';
import { Column } from '../sql/schema';
import { Extend, Node } from '../ts-api';
import { DataType } from '../types';

type JoinValue = string | number;

/** @internal */
// warning: all the properties except the last one are back pointers!!
export type PropagatePath = PropertyDecorator[];

export interface Enum {
    [id: number]: string;
}

/** @internal */
export interface PropertyDecorator extends TypedPropertyDecorator<Node> {
    name: string;

    type: TypeName;

    dataType?: () => DataType<AnyValue, Partial<Node>>;

    /** Target node if column is a reference or a collection */
    node?: () => { new (): Node };

    /** Join between source and target columns if target class is persistent */
    // TODO Remove undefined, test generated em-core code
    join?: Dict<JoinValue | ((this: Extend<Node>) => any) | undefined>;

    /** Is it a vital link ? - Only relevant for references and collections */
    isVital?: boolean;

    /** Is it a mutable? - Only relevant for references and collections on external storage nodes */
    isMutable?: boolean;

    /** @internal */
    column?: Column;

    // Only relevant for strings
    isNotEmpty?: boolean;
    maxLength?: number;
    // Only relevant for numbers
    isNotZero?: boolean;
    // Only relevant for everything but strings
    isNullable?: boolean;
    // A value for the property has to be passed, and we cannot default to datatype default value
    isRequired?: boolean;
    // Only relevant for enums
    enum?: () => Enum;
    // Only relevant for collections
    countColumnName?: string;
    // internal -- inverse of dependsOn
    propagatesTo?: PropagatePath[];
    // internal -- to check that a dependant is never read while computing the value for one of its dependencies.
    dependencyIndex?: number;
    // flag for decorators of property overrides
    isOverride?: boolean;
    /**
     * Indicates whether the property has been inherited from a base factory.
     * When dealing with superClasses, this attribute will be used to retrieve the baseClass of the node that
     * declared the property.
     */
    isInherited?: boolean;
}
