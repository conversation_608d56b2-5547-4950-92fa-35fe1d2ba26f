import * as _ from 'lodash';
import { integer } from '../ts-api';
import { fastHash } from '../types/util';
import { Context } from './context';
import { NodeFactory } from './node-factory';

/**
 * Converter that converts reference ids to natural keys.
 * It is activated by context.withReferenceAsNaturalKey when running synchronization queries.
 */
export class ContextNaturalKeyConverter {
    constructor(private readonly context: Context) {}

    /** Size of the cache */
    static cacheSize = 4096;

    /**
     * Small cache of converted natural keys.
     * This is a very simple fixed sized cache because we want to cap its memory usage when running long
     * synchronization queries.
     */
    #cache: string[] = Array(ContextNaturalKeyConverter.cacheSize);

    /**
     * Converts a reference when mapping records returned by SQL queries.
     * It returns the
     */
    async convertReference(factory: NodeFactory, id: integer): Promise<string | integer> {
        const naturalKey = factory.naturalKey;
        if (!naturalKey) return id;

        // Lookup the result in the cache.
        const cacheKey = `${factory.name}/${id}/`;
        const cacheIndex = fastHash(cacheKey) % ContextNaturalKeyConverter.cacheSize;
        const cached = this.#cache[cacheIndex];
        // Check that the cached entry starts with the cache key because cache entries are clobbered
        // when cache indexes collide.
        if (cached && cached.startsWith(cacheKey)) return cached.substring(cacheKey.length);

        const selector = _.zipObject(naturalKey, Array(naturalKey.length).fill(true));
        const [selected] = await this.context.select(factory.nodeConstructor, selector, { filter: { _id: id } });
        if (!selected) throw factory.logicError(`_id not found: ${id}`);
        const result = `#${Object.values(selected).join('|')}`;

        // Store the result in the cache, prefixed by its cache key.
        this.#cache[cacheIndex] = `${cacheKey}${result}`;

        return result;
    }
}
