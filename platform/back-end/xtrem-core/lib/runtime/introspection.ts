/** @packageDocumentation @module runtime */
import { Application, Package } from '../application';
import { TypeName } from '../decorators';
import { AnyOrderBy, Node } from '../ts-api';
import { DecimalDataType, EnumDataType, StringDataType } from '../types';
import { Context } from './context';
import { Enum } from './property';

export interface IntrospectionPropertyDescriptor {
    name: string;
    type: TypeName;
    isNullable: boolean;
    // graphql
    isPublished: boolean;
    // code (property or column code)
    code: string | null;
    // table info
    isStored: boolean;
    columnType: TypeName | null;
    // enum
    enum: Enum | null;
    // collections and references
    node: typeof Node | null;
    // strings
    isNotEmpty: boolean;
    maxLength: number;
    // decimals
    precision: number;
    scale: number;
    isRequired: boolean;
}

export interface IntrospectionIndexDescriptor {
    isUnique: boolean;
    orderBy: AnyOrderBy;
}

export interface IntrospectionNodeDescriptor {
    application: Application;
    package: Package;
    name: string;
    tableName?: string;
    properties: IntrospectionPropertyDescriptor[];
    indexes: IntrospectionIndexDescriptor[];
}

export class Introspection {
    constructor(private readonly context: Context) {}

    getNodeFromTableName(tableName: string): typeof Node | undefined {
        const found = this.context.application.getSqlPackageFactories().find(factory => {
            const table = factory.table;
            return !!table && table.name === tableName;
        });
        return found && found.nodeConstructor;
    }

    // eslint-disable-next-line class-methods-use-this
    getPropertyNameFromColumnName(clas: typeof Node, columnName: string): string | null {
        const table = this.context.application.getFactoryByConstructor(clas).table;
        const column = table && table.columns.find(col => col.columnName === columnName);
        return column ? column.propertyName : null;
    }

    // eslint-disable-next-line class-methods-use-this
    getNodeDescriptor(clas: typeof Node): IntrospectionNodeDescriptor {
        const factory = this.context.application.getFactoryByConstructor(clas);
        return {
            application: factory.application,
            package: factory.package,
            name: factory.name,
            tableName: factory.tableName || '',
            properties: factory.properties.map(property => {
                const dataType = property.dataType;
                return {
                    name: property.name,
                    type: property.type,
                    isNullable: !!property.isNullable,
                    isPublished: !!property.isPublished,
                    isStored: !!property.isStored,
                    isRequired: !!property.isRequired,
                    code: null,
                    columnType: property.isStored ? property.columnType || property.type : null,
                    node: property.isForeignNodeProperty() ? property.node : null,
                    isNotEmpty: !!property.isNotEmpty,
                    maxLength: dataType instanceof StringDataType && dataType.maxLength ? dataType.maxLength : 0,
                    precision: dataType instanceof DecimalDataType && dataType.precision ? dataType.precision : 0,
                    scale: dataType instanceof DecimalDataType && dataType.scale ? dataType.scale : 0,
                    enum: (dataType instanceof EnumDataType && dataType.enum) || null,
                };
            }),
            indexes: (factory.indexes || []).map(index => ({
                isUnique: !!index.isUnique,
                orderBy: index.orderBy,
            })),
        };
    }
}
