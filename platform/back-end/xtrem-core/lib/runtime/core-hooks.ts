/* eslint-disable @typescript-eslint/no-unused-vars */
import { AnyRecord } from '@sage/xtrem-async-helper';
import { Logger } from '@sage/xtrem-log';
import { AnyValue, AsyncResponse, Dict, LogicError, MetaCustomFields, NotificationProgress } from '@sage/xtrem-shared';
import { RequestHandler } from 'express';
import {
    Application,
    DataValidationManager,
    HotUpgradeManager,
    HotUpgradeManagerStub,
    PrintingManager,
} from '../application';
import { AttachmentManager } from '../application/attachment-manager';
import { ClientSettingsManager, ClientSettingsManagerStub } from '../application/client-settings-manager';
import { CsvChecksumManager, CsvChecksumManagerStub } from '../application/csv-checksum-manager';
import { DashboardManager, DashboardManagerStub } from '../application/dashboard-manager';
import { NoteManager } from '../application/note-manager';
import { NotificationManager, NotificationManagerStub } from '../application/notification-manager';
import { CreateSchemaOptions, PackageManager } from '../application/package-manager';
import { PackageManagerStub } from '../application/package-manager-stub';
import { ServiceOptionManager } from '../application/service-option-manager';
import { ServiceOptionManagerStub } from '../application/service-option-manager-stub';
import { TagManager } from '../application/tag-manager';
import { PlainOperationDecorator, StaticThis } from '../decorators';
import { AuditManagerInterface, auditManagerStub } from '../hooks/auditing';
import { InteropAppInfo } from '../interop';
import { Node } from '../ts-api';
import { WorkflowManagerInterface, WorkflowManagerStub } from '../workflow';
import { Context } from './context';
import { loadedOnce } from './loaded-once';
import { NodeFactory } from './node-factory';

loadedOnce('core-hooks');

export interface SourceCode {
    content: string;
    file: string;
    contentToLint?: string;
}

export interface WebSocketMessageRequest {
    tenantId: string;
    developerId: string;
    contextId: string;
    attributes: Dict<AnyValue>;
    type: string;
    source: string;
    request: string;
    replyId: string;
    replyTopic: string;
    locale?: string;
}

export interface WebSocketMessageResponse extends WebSocketMessageRequest {
    response: AnyValue;
}

/**
 * A source code resolver is responsible for returning the response to a web socket request
 */
export interface WebSocketMessageHandler {
    getResponse(application: Application, data: WebSocketMessageRequest): Promise<WebSocketMessageResponse>;
}

export interface ExportTemplate {
    id: string;
    name: string;
    isDefault?: boolean;
}

export interface CsvTemplateContent {
    _id: number;
    path: string;
    dataType: string;
    description: string;
    locale: string;
    isCustom?: boolean;
}

export interface HeaderDefinition {
    path: string;
    title: string;
}
export interface NodeExportTemplate {
    name: string;
    exportTemplates: ExportTemplate[];
}

export interface SecretConfig {
    xtremEnv?: string;
    clusterId?: string;
    secretCacheTimeoutInMinutes?: number;
}

export type BatchLogLevel = 'info' | 'warning' | 'error' | 'exception' | 'result' | 'test';

export interface BatchLogEntry {
    level: BatchLogLevel;
    message: string;
}

/**
 * The Manager global object contain methods that other packages (like xtrem-system) will override to inject
 * optional functionality into xtrem-core
 *
 * This API is currently very sketchy and will evolve soon.
 *
 * The other managers which are currently registered on Context (localizationManager, authorizationManager, ...)
 * will be moved here.
 *
 * TODO: improve this API and suggest a better name.
 * Maybe something like `InjectedDependencies`, or just 'Hooks'
 */
export abstract class CoreHooks {
    static createPackageManager: (application: Application) => PackageManager = application => {
        return new PackageManagerStub(application);
    };

    static createServiceOptionManager: (application: Application) => ServiceOptionManager = application => {
        return new ServiceOptionManagerStub(application);
    };

    static createHotUpgradeManager: (_application: Application) => HotUpgradeManager = () => {
        return new HotUpgradeManagerStub();
    };

    static createDashboardManager: (_application: Application) => DashboardManager = () => {
        return new DashboardManagerStub();
    };

    static createPrintingManager: (_application: Application) => PrintingManager | null = () => {
        return null;
    };

    static createClientSettingsManager: (_application: Application) => ClientSettingsManager = () => {
        return new ClientSettingsManagerStub();
    };

    static createNotificationManager: (_application: Application) => NotificationManager = () => {
        return new NotificationManagerStub();
    };

    static createCsvChecksumManager: (_application: Application) => CsvChecksumManager = () => {
        return new CsvChecksumManagerStub();
    };

    static getDataValidationManager: () => DataValidationManager = () => {
        throw new LogicError('DataValidation manager has not been registered');
    };

    static createWorkflowManager: (_application: Application) => WorkflowManagerInterface = () => {
        return new WorkflowManagerStub();
    };

    static auditManager: AuditManagerInterface = auditManagerStub;

    static getAttachmentManager: () => AttachmentManager = () => {
        throw new LogicError('Attachment manager has not been registered');
    };

    static getTagManager: () => TagManager = () => {
        throw new LogicError('Tag manager has not been registered');
    };

    static getNoteManager: () => NoteManager = () => {
        throw new LogicError('Note manager has not been registered');
    };

    static testManager = {
        async loadTestData(
            _application: Application,
            _options: { testLayers?: string[] },
            _tenantId: string,
        ): Promise<void> {
            /* stub */
        },

        /**
         * Initializes the pack allocation for system factories
         */
        async initTestTenantActivePackages(_application: Application): Promise<void> {
            /* stub */
        },
    };

    static webSocketHandlers: Dict<WebSocketMessageHandler> = {};

    /** API injected by xtrem-communication */
    static communicationManager = {
        /**
         * Sends a notification
         */
        notify(_context: Context, topic: string, _payload: AnyRecord): Promise<string> {
            throw new LogicError(`cannot send ${topic} notification: notification middleware not installed`);
        },

        /**
         * Starts an async mutation
         */
        startAsyncMutation(
            _context: Context,
            factoryName: string,
            mutationName: string,
            _payload: AnyRecord,
        ): Promise<string> {
            throw new LogicError(
                `cannot start ${factoryName}/${mutationName} async operation: notification middleware not installed`,
            );
        },

        /**
         * Tracks the progress of an async mutation
         */
        trackAsyncMutation(
            _context: Context,
            _factory: NodeFactory,
            trackingId: string,
            _op: PlainOperationDecorator,
        ): Promise<{
            status: string;
            result?: AnyValue;
            errorMessage?: string;
            logMessages?: BatchLogEntry[];
        }> {
            throw new LogicError(`cannot track ${trackingId} async mutation: notification middleware not installed`);
        },

        /**
         * Sets ths status of an async mutation to stopRequested
         */
        requestStop(_context: Context, trackingId: string, _reason: string): Promise<void> {
            throw new LogicError(
                `cannot request a stop on ${trackingId} async mutation: notification middleware not installed`,
            );
        },

        /**
         * Sets ths status of an async mutation to stopped
         */
        setStopped(_context: Context, trackingId: string): Promise<void> {
            throw new LogicError(
                `cannot set ${trackingId} async mutation to stopped: notification middleware not installed`,
            );
        },

        /**
         * Sets ths status of an async mutation to stopped
         */
        requestUserNotification(_context: Context, trackingId: string): Promise<void> {
            throw new LogicError(
                `cannot set ${trackingId} async mutation to notify the user: notification middleware not installed`,
            );
        },

        /**
         * Tracks the progress of an async mutation
         */
        isStopRequested(_context: Context): Promise<boolean> {
            throw new LogicError('cannot call isStopRequested: notification middleware not installed');
        },

        extendClassDecorator(_factory: NodeFactory): void {
            // Do not throw to not fail when compiling from xtrem-system;
        },

        updateProgress(_context: Context, _partialProgress: Partial<NotificationProgress>): Promise<void> {
            throw new LogicError('cannot call updateProgress: notification middleware not installed');
        },

        logErrorBatchMessage(_context: Context, _message: string): Promise<void> {
            throw new LogicError('cannot call logErrorBatchMessage: notification middleware not installed');
        },
    };

    static interopManager = {
        getInteropAppInfo(_context: Context, _app: string): Promise<InteropAppInfo> {
            throw new LogicError('cannot call getInteropAppInfo: interop manager not installed');
        },

        interopProxyMiddleware(_application: Application): RequestHandler {
            // no proxy middleware by default
            return (_req, res) => {
                res.status(404).send('not found');
            };
        },

        getTenantApps(_tenantId: string): Promise<string[] | null> {
            throw new LogicError('cannot call getTenantApps: interop manager not installed');
        },

        /**
         * Fix the activation of sysNodeTransformations at the end of upgrades
         * @param _context
         */
        fixInteropTransformationsActivation(_context: Context, _options?: { logger?: Logger }): AsyncResponse<void> {},
    };

    /** API injected by xtrem-metadata */
    static metadataManager = {
        /**
         * Synchronize the metadata to the database
         */
        upgradeMetadata(
            _application: Application,
            _options?: { fullReload?: boolean; onlyCreate?: boolean },
        ): AsyncResponse<void> {},

        getMetaNodeFactoryConstructor(): StaticThis<Node> | null {
            return null;
        },
    };

    /** API injected by xtrem-system */
    static sysManager = {
        /**
         * Gets the User node
         */
        getUserNode(): StaticThis<Node> {
            throw new Error('User node not set');
        },
    };

    /** API injected by xtrem-customization */
    static customizationManager = {
        getWizardUrl(): string {
            return '';
        },

        /**
         * Gets the metadata of the custom fields for a list of node names.
         */
        getCustomFields(
            _context: Context,
            _nodeNames: string[],
            _options?: { includeMutableChildren: boolean },
        ): Promise<MetaCustomFields | undefined> {
            return Promise.resolve(undefined);
        },
    };

    /** API injected for import-export data */
    static importExportManager = {
        executeExport(
            _context: Context,
            _id: string,
            _outputFormat: string,
            _filter: string,
            _orderBy: string,
        ): Promise<string> {
            return Promise.resolve('');
        },
        executeExportByDefinition(
            _context: Context,
            _templateDefinition: HeaderDefinition[],
            _nodeName: string,
            _outputFormat: string,
            _filter: string,
            _orderBy: string,
        ): Promise<string> {
            return Promise.resolve('');
        },
        getNodeExportTemplates(_context: Context, _nodeNames: string[]): Promise<Dict<ExportTemplate[]>> {
            return Promise.resolve({});
        },
        getExportPageUrl(): string {
            return '';
        },
    };
}

export { CreateSchemaOptions, PackageManager, ServiceOptionManager };
