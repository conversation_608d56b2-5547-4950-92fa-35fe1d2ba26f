/** @ignore */ /** */
/** @internal */
import { funnel } from '@sage/xtrem-async-helper';
import { Application } from '../application';
import { decorators } from '../decorators';
import { Node } from '../ts-api';
import { Context } from './context';
import { loggers } from './loggers';
import * as dataTypes from './system-data-types';

@decorators.node<SysGlobalLock>({
    isPublished: false,
    storage: 'sql',
    canCreate: true,
    canDeleteMany: true,
    isPlatformNode: true,
    indexes: [{ orderBy: { key: 1 }, isUnique: true }],
})
export class SysGlobalLock extends Node {
    @decorators.stringProperty<SysGlobalLock, 'key'>({
        dataType: () => dataTypes.cacheKey,
        isStored: true,
    })
    readonly key: Promise<string>;
}

const logger = loggers.globalLock;

/** @internal */
export class GlobalLock {
    private readonly _initFunnel = funnel(1);

    private _isInitialized = false;

    constructor(private readonly application: Application) {}

    private async _initIfNeeded(context: Context): Promise<void> {
        await this._initFunnel(async () => {
            if (this._isInitialized) return;

            await this.application
                .getFactoryByConstructor(SysGlobalLock)
                .ensureTableExists(context, { skipDrop: true });
            this._isInitialized = true;
        });
    }

    async acquire(context: Context, key: string): Promise<SysGlobalLock> {
        await this._initIfNeeded(context);
        if (!context.transaction.isWritable) {
            throw new Error('Exclusive processes can only be run inside a writable transaction');
        }
        const profiler = logger.info(`Try to acquire lock '${key}'`, { lowThreshold: 50, highThreshold: 200 });
        return logger.doAsync(
            async () => {
                let lck = await context.tryRead(SysGlobalLock, { key }, { forUpdate: true });
                if (lck == null) {
                    await this.application.asRoot.withCommittedContext(
                        context.tenantId,
                        async createContext => {
                            logger.verbose(() => `Created lock '${key}'`);
                            try {
                                lck = await createContext.create(SysGlobalLock, { key });
                                await lck.$.save();
                            } catch (err) {
                                logger.warn(`Race condition during creation of global lock: ${err.message}`);
                            }
                        },
                        { description: () => `Create lock key ${key}` },
                    );

                    // Reset the prefetcher because the lock was created in another context
                    context.prefetcher.reset();
                    lck = await context.tryRead(SysGlobalLock, { key }, { forUpdate: true });
                    if (!lck) throw new Error(`Could not acquire lock '${key}'`);
                }
                profiler.success();
                return lck;
            },
            {
                onError: err => {
                    profiler.fail(err.toString());
                    throw err;
                },
            },
        );
    }
}
