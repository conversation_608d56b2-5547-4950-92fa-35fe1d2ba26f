/** @ignore */ /** */
import { UpdateActionEnum } from '../ts-api/create-data';
import { DecimalDataType, EnumDataType, JsonDataType, StringDataType } from '../types';

export const _updateActionDataType = new EnumDataType({ enum: UpdateActionEnum, filename: __filename });
export const _constructorDataType = new StringDataType({ maxLength: 100 });
export const nanoIdDataType = new StringDataType({ maxLength: 21 });
export const _jsonDataType = new JsonDataType();
export const _etagDataType = new StringDataType({ maxLength: 100 });
export const _sourceIdDataType = new StringDataType({ maxLength: 128 });
// values hash is encoded in base64 => 44 chars (instead of 64 for hex)
export const _valuesHashDataType = new StringDataType({ maxLength: 44 });
export const _syncTickDataType = new DecimalDataType({ precision: 28, scale: 0 });

export const cacheKey = new StringDataType({ maxLength: 20 });
