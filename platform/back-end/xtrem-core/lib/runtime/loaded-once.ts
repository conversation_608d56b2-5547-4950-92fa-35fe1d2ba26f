/* eslint-disable no-console */
import { createHash } from 'crypto';
/** @ignore */ /** */
Error.stackTraceLimit = 1000;
/** @internal
 * This function checks that _peer_ modules are only loaded once during the entire life
 * of a process.
 * Peer modules are modules that define classes that we test with instanceof, or symbols.
 * If such modules are loaded more than once, two copies of the class or symbols will be created and
 * terrible things may happen: instanceof returning false positive, indexing by symbol returning unexpected undefined values, etc.
 *
 * If this function detects that the the module is loaded twice it displays two error stacks,
 * at the two points where the module was loaded, and it aborts the process with an error status of 1.
 */
export function loadedOnce(name: string): void {
    // we use this message as key - low risk of collision
    const message = `FATAL ERROR: '${name} loaded twice'`;
    const hashedMessage = createHash('md5').update(message).digest('hex');
    const alreadyHere = (global as any)[hashedMessage];
    if (alreadyHere) {
        console.error(message);
        console.error(alreadyHere.stack);
        console.error(new Error('STACK 2').stack);
        // exit after 100 ms to avoid truncated error messages.
        setTimeout(() => process.exit(1), 100);
    }
    Object.defineProperty(global, hashedMessage, {
        value: new Error('STACK 1'),
        enumerable: false,
        writable: false,
        configurable: false,
    });
}
