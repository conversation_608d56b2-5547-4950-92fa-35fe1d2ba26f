import { asyncArray } from '@sage/xtrem-async-helper';
import { ConfigManager } from '@sage/xtrem-config';
import { ClientArtifactUsedLiterals, getLiteral, getLocaleFromHeader, LocalizedLiteral } from '@sage/xtrem-i18n';
import {
    AccessStatus,
    AsyncResponse,
    Dict,
    LocalizeLocale,
    Maybe,
    MetaCustomFieldsArray,
    MetaCustomizableNodesArray,
    RootMenuItem,
    SitemapEntry,
    SubMenuItem,
} from '@sage/xtrem-shared';
import { createHash } from 'crypto';
import { kebabCase, snakeCase, union } from 'lodash';
import { AccessBinding, NodeAccessBindingStatus } from '../../graphql/security/access-bindings';
import { AccessRights } from '../../graphql/security/access-rights';
import { NodeFactory, standardOperations } from '../../runtime';
import { Context } from '../../runtime/context';
import { ExportTemplate } from '../../runtime/core-hooks';
import { loggers } from '../../runtime/loggers';
import { TextStream } from '../../types';
import { ArtifactFilter, ArtifactManager } from '../artifact-manager';

export interface ClientArtifactMetadata extends MetaPackArtifact {
    className: string;
    md5?: string;
}

interface PackageArtifact {
    key: string;
    packageName: string;
    content: TextStream;
    artifactFilePath: string;
}

export interface ApplicationArtifact extends ClientArtifactMetadata, PackageArtifact {
    extensions?: string[];
    plugins?: string[];
    menuItem?: string;
    priority?: number;
    strings?: LocalizedLiteral[];
}

export interface JsPackArtifact extends PackageArtifact {
    className: string;
    plugins?: string[];
    menuItem?: string;
    priority?: number;
    literals?: ClientArtifactUsedLiterals;
    type?: string;
}

export interface MetaPackArtifact {
    title?: string;
    authorizationCode?: string;
    pageAccess?: AccessBinding;
    pageNode?: string;
    extensionAccess?: AccessBinding;
    category?: string;
    group?: string;
    categoryLabel?: string;
    type?: string;
    extends?: string;
    plugins?: string[];
    fragments?: string[];
    menuItem?: string;
    parentMenuItem?: string;
    priority?: number;
    listIcon?: string;
    description?: string;
    // List of property names that require a new value for duplication
    duplicateBindings?: string[];
    literals?: ClientArtifactUsedLiterals;
    nodes?: string[];
}

export interface MetaCustomFields {
    // List of customizable nodes, used by the custom fields wizard, based on the vital tree of a page node
    customizableNodesWizard?: MetaCustomizableNodesArray;
    // List of customizable nodes, used by client side to render custom fields on the UI, based on access bindings
    customizableNodes?: MetaCustomizableNodesArray;
    // List of custom fields for customizableNodes
    customFields?: MetaCustomFieldsArray;
}

export enum ClientArtifactType {
    pages = 'pages',
    widgets = 'widgets',
    stickers = 'stickers',
    strings = 'strings',
    pageExtensions = 'page-extensions',
    pageFragments = 'page-fragments',
}

export interface NodeAccess {
    node: string;
    bindings: NodeAccessBindingStatus[];
}

export interface PageArtifact {
    pageNode?: string;
    pageAccess?: AccessBinding;
    authorizationCode?: string;
    exportTemplates?: ExportTemplate[];
}

interface NodeStatus {
    package: string;
    name: string;
    status?: AccessStatus;
}

export type ArtifactType = ApplicationArtifact | PageArtifact | null;

/** @internal */
export class ClientService {
    static async getUniqueUserId(context: Context): Promise<string | null> {
        if ((await context.loginUser)?.email) {
            return createHash('sha256')
                .update(`${(await context.loginUser)?.email}_${ConfigManager.current.telemetrySalt || ''}`)
                .digest('base64');
        }

        return null;
    }

    static getUniqueTenantId(context: Context): string | null {
        if (context.tenantId && context.configuration) {
            return createHash('sha256')
                .update(`${context.tenantId}_${ConfigManager.current.telemetrySalt || ''}`)
                .digest('base64');
        }

        return null;
    }

    static async getDefaultUserEncryptionKey(context: Context): Promise<string | null> {
        if ((await context.user)?.email) {
            const uniqueUserId = await ClientService.getUniqueUserId(context);
            const uniqueTenantId = ClientService.getUniqueTenantId(context);
            return createHash('sha256')
                .update(`${uniqueUserId}_${ConfigManager.current.telemetrySalt || ''}_${uniqueTenantId}`)
                .digest('base64');
        }

        return null;
    }

    private static sitemapTree: SitemapEntry[] | null;

    /**
     * Compares two sitemap entries baed on their priority, the lower number means higher priority.
     * If no priority is set, the item is prioritized to the end.
     */
    private static compareSitemapEntries = (entry1: SitemapEntry, entry2: SitemapEntry): number => {
        const priority1 = entry1.priority === undefined ? Number.MAX_SAFE_INTEGER : entry1.priority;
        const priority2 = entry2.priority === undefined ? Number.MAX_SAFE_INTEGER : entry2.priority;
        return priority1 - priority2;
    };

    /**
     * Structures the flat parent-child map into a sitemap tree, sorts the leaves.
     * @param entry
     * @param childrenMenuItemMap
     */
    private static populateSitemapLeafChildren = (
        entry: SitemapEntry,
        childrenMenuItemMap: Dict<SitemapEntry[]>,
    ): SitemapEntry => {
        if (childrenMenuItemMap[entry.id!]) {
            const children = childrenMenuItemMap[entry.id!].map(c =>
                ClientService.populateSitemapLeafChildren(c, childrenMenuItemMap),
            );
            children.sort(ClientService.compareSitemapEntries);
            entry.children = children;
        }

        return entry;
    };

    static async getSitemapTree(context: Context): Promise<SitemapEntry[]> {
        if (!ClientService.sitemapTree) {
            ClientService.sitemapTree = await ClientService.calculateSitemapTree(context);
        }

        return ClientService.sitemapTree;
    }

    /**
     * This should search in the DB table
     * @param key
     * @param artifactFolder
     */
    static async calculateSitemapTree(context: Context): Promise<SitemapEntry[]> {
        loggers.application.debug(() => 'calculateSitemapTree');

        const childrenMenuItemMap: Dict<SitemapEntry[]> = {};

        // Identify page parents
        loggers.application.debug(() => 'calculateSitemapTree queryPackArtifacts');
        (await ArtifactManager.queryPackArtifacts(context, { dataType: 'meta' })).forEach(packArtifact => {
            loggers.application.debug(
                () => `calculateSitemapTree artifact package=${packArtifact.packageName} path=${packArtifact.path}`,
            );
            const parsedContent = ArtifactManager.getParsedContent(packArtifact);
            if (parsedContent.parentMenuItem) {
                childrenMenuItemMap[parsedContent.parentMenuItem] = [
                    ...(childrenMenuItemMap[parsedContent.parentMenuItem] || []),
                    {
                        id: `${packArtifact.packageName}/${parsedContent.className}`,
                        isPage: true,
                        priority: parsedContent.priority,
                        packageName: packArtifact.packageName,
                    },
                ];
            }
        });

        // Identify menu relationships
        const menuItemIdMap: Dict<SubMenuItem | RootMenuItem> = {};
        const locale = getLocaleFromHeader(context.request.headers);

        context.application.getPackages().forEach(pack => {
            if (pack.api?.menuItems) {
                Object.values(pack.api.menuItems).forEach((menuItem: SubMenuItem | RootMenuItem) => {
                    menuItemIdMap[menuItem.id] = {
                        ...menuItem,
                        packageName: pack.name,
                        title: getLiteral(
                            `${pack.name}/menu_item__${kebabCase(menuItem.id.replace(`${pack.name}`, ''))}`,
                            locale,
                        ).content,
                    };
                    const subMenuItem = { ...menuItem, packageName: pack.name } as SubMenuItem;
                    if (subMenuItem.parentMenuItem) {
                        childrenMenuItemMap[subMenuItem.parentMenuItem.id] = [
                            ...(childrenMenuItemMap[subMenuItem.parentMenuItem.id] || []),
                            {
                                id: menuItem.id,
                                priority: menuItem.priority,
                                icon: (menuItem as RootMenuItem).icon,
                                isPage: false,
                                packageName: pack.name,
                            },
                        ];
                    }
                });
            }
        });
        // Structuring and sorting the tree
        const tree = Object.values(menuItemIdMap)
            .filter(menuItem => !!(menuItem as RootMenuItem).icon)
            .map((menuItem: RootMenuItem) =>
                ClientService.populateSitemapLeafChildren({ ...menuItem, isPage: false }, childrenMenuItemMap),
            );

        loggers.application.debug(
            () => `calculateSitemapTree tree (before sorting):${JSON.stringify(tree, null, '\t')}`,
        );

        // Sorting top level entries
        tree.sort(ClientService.compareSitemapEntries);

        loggers.application.debug(
            () => `calculateSitemapTree menuItemIdMap (after sorting):${JSON.stringify(menuItemIdMap, null, '\t')}`,
        );
        return tree;
    }

    /**
     * This should search in the DB table
     * @param key
     * @param artifactType
     */
    static async searchClientArtifactsFromStorage(
        context: Context,
        artifactType: ClientArtifactType,
        filter: ArtifactFilter,
        locale: LocalizeLocale,
    ): Promise<ApplicationArtifact[]> {
        return (await ArtifactManager.listAllClientArtifactsFromStorage(context, artifactType))
            .filter(a => !filter.packageOrPage || a.key.startsWith(filter.packageOrPage))
            .filter(a => !filter.pageNode || a.pageNode === filter.pageNode)
            .map((a: ApplicationArtifact) => {
                return {
                    ...a,
                    title: getLiteral(`${a.packageName}/${artifactType}__${snakeCase(a.className)}____title`, locale)
                        .content,
                };
            });
    }

    static async getAccessStatuses(
        context: Context,
        accessBindings: Maybe<Dict<Maybe<string[]>>>,
        options?: { pageAccess?: AccessBinding; authorizationCode?: string },
    ): Promise<NodeAccess[]> {
        if (!accessBindings) return [];
        const nodeAccessList: NodeAccess[] = [];

        await asyncArray(Object.entries(accessBindings)).forEach(async ([node, bindings]) => {
            nodeAccessList.push({
                node,
                bindings: await asyncArray(bindings || [])
                    ?.map(
                        async name =>
                            ({
                                name,
                                status: await this.getAccessStatus(
                                    context,
                                    node,
                                    name,
                                    node === options?.pageAccess?.node
                                        ? { authorizationCode: options?.authorizationCode }
                                        : {},
                                ),
                            }) as NodeAccessBindingStatus,
                    )
                    .toArray(),
            } as NodeAccess);
        });
        return nodeAccessList;
    }

    private static resolveNodeStatus(context: Context, fullNodeName: string): NodeStatus {
        const parts = fullNodeName.split('/');
        const node = { name: parts.pop() || '', package: parts.join('/') } as NodeStatus;

        return node;
    }

    private static parseBindValue(bind: string): string {
        return bind.startsWith('$') ? bind.substring(1) : bind;
    }

    private static async getAccessStatus(
        context: Context,
        fullNodeName: string,
        bind: string,
        options?: { authorizationCode?: string },
    ): Promise<AccessStatus> {
        const node = this.resolveNodeStatus(context, fullNodeName);
        if (node.status) return node.status;

        const factory = context.application.getFactoryByName(node.name);
        const bindValue = this.parseBindValue(bind);
        const operation = factory.getOperationByName(bindValue);

        if (factory.authorizedBy || operation?.authorizedBy) {
            const authorizedByAccess = await AccessRights.getAuthorizedByAccess(context, factory, operation, bindValue);
            if (authorizedByAccess) return authorizedByAccess.status;
        }

        return (await Context.accessRightsManager.getUserAccessFor(context, node.name, bindValue, options)).status;
    }

    static getPageAccess(
        context: Context,
        pageAccess?: AccessBinding,
        options?: { authorizationCode?: string },
    ): AsyncResponse<AccessStatus> {
        if (!pageAccess) return 'authorized';
        return this.getAccessStatus(context, pageAccess.node!, pageAccess.bind!, options);
    }

    static async getFactoryAccess(context: Context, factory: NodeFactory): Promise<NodeAccess[]> {
        const { properties, queries, mutations } = factory;

        let factoryComponentList: string[] = [];

        factoryComponentList = union(
            factoryComponentList,
            properties.filter(p => p.isPublished).map(p => p.name),
            queries.map(q => q.name),
            mutations.map(m => m.name),
            standardOperations.map(o => `$${o}`),
        );

        const user = await context.user;

        return context.getCachedValue({
            category: 'ARTIFACT',
            key: `USER:${user?._id || '*'}:FACTORY:${factory.name}`,
            getValue: async () => ({
                value: await this.getAccessStatuses(context, { [factory.fullName]: factoryComponentList }),
            }),
            cacheInMemory: true,
            ttlInSeconds: 60,
        });
    }

    /**
     * Checks that a user is authorized to access the specified page
     * @param context
     * @param key
     * @param locale
     * @returns
     */
    static async userHasAccessToPage(context: Context, key: string): Promise<boolean> {
        const page = await ArtifactManager.getPageArtifact(context, key);
        if (page == null) {
            loggers.authorization.error(() => `${key}: page not found`);
            return false;
        }
        const pageAccessStatus = await ClientService.getPageAccess(context, page.pageAccess, {
            authorizationCode: page.authorizationCode,
        });

        // TODO: remove this logger later if not required
        const email = (await context.user)?.email;
        loggers.authorization.debug(
            () => `${key}: access status = ${pageAccessStatus}, user = ${email}, page access details = ${page}`,
        );

        return pageAccessStatus === 'authorized';
    }

    static invalidateStorageStatus(): void {
        ArtifactManager.clearStored();
    }
}
