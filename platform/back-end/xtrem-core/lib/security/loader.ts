import { asyncArray } from '@sage/xtrem-async-helper';
import { ConfigManager } from '@sage/xtrem-config';
import { Config } from '@sage/xtrem-shared';
import * as _ from 'lodash';
import { ContextVault } from '../runtime/context-vault';
import { logger } from './loggers';
import { initTlsLayer, loadExtraConfig } from './tls';

let initialized = false;

export async function setup(): Promise<void> {
    if (ConfigManager.current == null) {
        throw new Error('Config must be loaded first');
    }

    if (initialized) {
        return;
    }
    initialized = true;

    // has already been loaded before the setup, so we can safely apply changes
    await asyncLoadSecret(ConfigManager.current);
    await loadExtraConfig();

    logger.info('Initializing security layer...');
    ConfigManager.emitter.addListener('loaded', () => {
        asyncLoadSecret(ConfigManager.current)
            .then(() => loadExtraConfig())
            .catch(err => logger.error(err));
    });
    initTlsLayer();
    logger.info('Security layer initialized');
}

function collectSecrets(obj: any, secrets: SecretValue[], path: string[]): void {
    _.forIn(obj, (val, key) => {
        if (typeof val === 'string') {
            if (val.startsWith('@secret/')) {
                secrets.push({ obj, key, placeholder: val, path: [...path, key] });
            }
        } else if (_.isArray(val)) {
            val.forEach((el, i) => {
                if (_.isObject(el)) {
                    collectSecrets(el, secrets, [...path, key, `[${i}]`]);
                }
            });
        } else if (_.isObject(val)) {
            collectSecrets(val, secrets, [...path, key]);
        }
    });
}

interface SecretValue {
    obj: any;
    key: string;
    placeholder: string;
    path: string[];
}

async function asyncLoadSecret(config: Config): Promise<void> {
    const secrets = [] as SecretValue[];
    collectSecrets(config, secrets, []);
    await asyncArray(secrets).forEach(async secret => {
        const { obj, key, placeholder, path } = secret;
        logger.info(`Loading secret for '${path.join('.')}' with placeholder '${placeholder}'`);
        obj[key] = await ContextVault.getConfigSecretValue(placeholder);
    });
}
