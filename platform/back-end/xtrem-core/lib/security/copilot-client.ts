import { ConfigManager } from '@sage/xtrem-config';
import { Logger } from '@sage/xtrem-log';
import { CopilotConfig } from '@sage/xtrem-shared';
import axios, { AxiosError, AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import { Context } from '../runtime';
import { SageIdClient } from './sage-id-client';

const logger = Logger.getLogger(__filename, 'copilot');

export class CopilotClient {
    #sageIdAccessToken: string;

    #sageIdTokenExpireAt = 0;

    #gmsAccessToken: string;

    #gmsTokenExpireAt = 0;

    private static errorResponseInterceptor(error: AxiosError): AxiosError {
        logger.error(`Copilot error: ${error}`);
        if (error.response?.data) {
            logger.debug(() => `Copilot Error response interceptor${JSON.stringify(error.response?.data, null, 4)}`);
        }
        return error;
    }

    private static successResponseInterceptor(response: AxiosResponse<any, any>): AxiosResponse<any, any> {
        logger.debug(
            () =>
                `Copilot response: ${response.config.method} ${response.config.baseURL}${response.config.url} ${response.status}`,
        );
        logger.debug(() => `Headers:\n${JSON.stringify(response.headers, null, 4)}`);

        if (response?.data) {
            logger.debug(() => JSON.stringify(response?.data, null, 4));
        }
        return response;
    }

    private static requestInterceptor(request: InternalAxiosRequestConfig<any>): InternalAxiosRequestConfig {
        logger.debug(() => `Copilot request: ${request.method} ${request.baseURL}${request.url}`);
        logger.debug(() => `Copilot Headers:\n${JSON.stringify(request.headers, null, 4)}`);
        if (request?.data) {
            logger.debug(() => `Copilot Body:\n${JSON.stringify(request?.data, null, 4)}`);
        }
        return request;
    }

    private async getSageIdAccessToken(copilotConfig: CopilotConfig): Promise<string> {
        const expiration = Date.now() - 5 * 60 * 1000;
        if (expiration > this.#sageIdTokenExpireAt) {
            const token = await SageIdClient.getToken({
                url: copilotConfig.oauthEndpointUrl,
                clientId: copilotConfig.clientId,
                clientSecret: copilotConfig.clientSecret,
                audience: copilotConfig.audience,
            });
            this.#sageIdAccessToken = token.accessToken;
            this.#sageIdTokenExpireAt = Date.now() + token.expiresIn * 1000;
        }
        return this.#sageIdAccessToken;
    }

    private async getGmsToken(context: Context): Promise<{ token: string; expiration: number }> {
        const expiration = Date.now() - 2 * 60 * 1000;
        const copilotConfig = ConfigManager.current.copilot;
        if (!copilotConfig) {
            throw new Error('copilot config not found');
        }
        if (expiration > this.#gmsTokenExpireAt) {
            const client = axios.create({
                baseURL: copilotConfig.serviceUrl,
                headers: {
                    'Content-Type': 'application/json',
                    Accept: 'application/json',
                },
            });

            client.interceptors.response.use(
                CopilotClient.successResponseInterceptor,
                CopilotClient.errorResponseInterceptor,
            );

            client.interceptors.request.use(CopilotClient.requestInterceptor);

            const loginUserName = (await context.loginUser)?.userName ?? (await context.loginUser)?.email;
            const userName = (await context.user)?.userName ?? (await context.user)?.email;
            const currentUserName = loginUserName ?? userName;
            const { tenantId } = context;
            if (!currentUserName && !tenantId) {
                throw new Error('No user or tenant id found');
            }

            const userId = `${currentUserName ?? ''}${tenantId ? '@' : ''}${tenantId ?? ''}`;

            const sageIdToken = await this.getSageIdAccessToken(copilotConfig);

            const result = await client.post(
                '/token',
                {
                    user_id: userId,
                },
                {
                    headers: {
                        'Content-Type': 'application/json',
                        Authorization: SageIdClient.formatBearerAuthorization(sageIdToken),
                    },
                },
            );

            if (result.status !== 200) {
                throw new Error(`Copilot: Unexpected status code ${result.status}`);
            }

            if (!result.data?.access_token) {
                throw new Error('Copilot: No access token found in auth response');
            }

            this.#gmsAccessToken = result.data.access_token;
            this.#gmsTokenExpireAt = Date.now() + (result.data.expires_in ?? 0) * 1000;
        }
        return { token: this.#gmsAccessToken, expiration: this.#gmsTokenExpireAt };
    }

    private static client: CopilotClient;

    static getToken(context: Context): Promise<{ token: string; expiration: number }> {
        if (!CopilotClient.client) {
            CopilotClient.client = new CopilotClient();
        }
        return CopilotClient.client.getGmsToken(context);
    }

    static isEnabled(): boolean {
        return !!ConfigManager.current.copilot?.serviceUrl;
    }
}
