import { CustomMetrics, PromGauge as Gauge, allMetricsEnabled } from '@sage/xtrem-metrics';
import * as fsp from 'path/posix';
import { performance } from 'perf_hooks';
import * as workerpool from 'workerpool';
import { SystemError } from '..';
import { logger } from './loggers';

// ================================================================================================================
// WARNING: xss-worker.js must be a js file not encoded with bytenode to work at runtime with worker threads
//          We might investigate later, but at the moment there are issues with mocha tests and bytenode.
// ================================================================================================================
const workerFile = fsp.join(__dirname, 'xss-worker.js');

// eslint-disable-next-line import/no-dynamic-require
const { sanitizeHtml } = require(workerFile);

const poolMetrics = allMetricsEnabled
    ? {
          workersThreads: new Gauge({
              name: 'xtrem_worker_threads_stats',
              help: 'The worker threads stats',
              labelNames: ['statName'],
          }),
          workerTasks: new Gauge({
              name: 'xtrem_workers_tasks_stats',
              help: 'The worker tasks stats',
              labelNames: ['statName'],
          }),
      }
    : {};

let _pool: workerpool.Pool;
const pool = (): workerpool.Pool => {
    if (!_pool) {
        _pool = workerpool.pool(workerFile, {
            onCreateWorker: opts => {
                logger.info(
                    `Create worker thread: ${opts.script?.replace(/^.*\/build\//, '.../build/')} stats:${JSON.stringify(
                        _pool.stats(),
                    )}`,
                );
                updateWorkerMetrics();
                return undefined;
            },
            onTerminateWorker: opts => {
                logger.info(
                    `Terminate worker thread: ${opts.script?.replace(
                        /^.*\/build\//,
                        '.../build/',
                    )} stats:${JSON.stringify(_pool.stats())}`,
                );
            },
        });
        updateWorkerMetrics();
        CustomMetrics.emitter.on('collected', () => {
            // we should not refresh to frequently otherwise nothing appears in grafana dashboard
            if (performance.now() - updateStamp > 15000) {
                updateWorkerMetrics();
            }
        });
    }
    return _pool;
};

let updateStamp = performance.now();
function updateWorkerMetrics(): void {
    if (!_pool) {
        return;
    }

    updateStamp = performance.now();
    const stats = _pool.stats();
    if (poolMetrics.workersThreads) {
        poolMetrics.workersThreads.set({ statName: 'busy' }, stats.busyWorkers);
        poolMetrics.workersThreads.set({ statName: 'idle' }, stats.idleWorkers);
        poolMetrics.workersThreads.set({ statName: 'total' }, stats.totalWorkers);
    }
    if (poolMetrics.workerTasks) {
        poolMetrics.workerTasks.set({ statName: 'active' }, stats.activeTasks);
        poolMetrics.workerTasks.set({ statName: 'pending' }, stats.pendingTasks);
    }
}

const htmlLengthSyncThreshold = 10 * 1024; // 1K chars (not bytes)

export interface HtmlSanitizerOptions {
    changes?: { records?: MutationRecord[]; removed?: any[] };
    throwIfModified?: boolean;
    ignoreTemplating?: boolean;
}

export interface SanitizeResult {
    html?: string;
    error?: string;
    details?: string[];
}

/**
 * Async function to sanitize an html content.
 * The underlying code is not async so the function use a worker thread to make it async
 * @param html
 * @param options
 * @returns
 */
export function htmlSanitizer(html: string, options?: HtmlSanitizerOptions): Promise<string> {
    if (!html) return Promise.resolve(html);

    if (workerpool.isMainThread) {
        // Content smaller than the given threshold are sanitized synchronously even if with the worker pool
        // the overhead is only the serialization of data but we are saving the worker pool queue.
        if (htmlLengthSyncThreshold >= 0 && html.length > htmlLengthSyncThreshold) {
            return _asyncSanitizeHtml(html, options);
        }
        return Promise.resolve(_syncSanitizeHtml(html, options));
    }
    throw new Error('htmlSanitizer cannot be invoked from workers');
}

async function _asyncSanitizeHtml(html: string, options?: HtmlSanitizerOptions): Promise<string> {
    const resultPromise = pool().exec<(html: string, options?: HtmlSanitizerOptions) => SanitizeResult>(
        'sanitizeHtml',
        [html, options],
    );
    updateWorkerMetrics();
    const result = await resultPromise;
    return _getSanitizeString(result);
}

function _syncSanitizeHtml(html: string, options?: HtmlSanitizerOptions): string {
    return _getSanitizeString(sanitizeHtml(html, options));
}

function _getSanitizeString(result: SanitizeResult): string {
    if (result.html != null) {
        return result.html;
    }
    if (result.error) {
        if (result.details) {
            logger.error(result.details.join(' | '));
        }
        throw new SystemError(result.error);
    }
    throw new SystemError('sanitize html is expected to return a value');
}
