export interface JwtClaims {
    /**
     * "5f0b9648-bc74-4fe7-adbf-184fc7efc44a"
     */
    jti?: string;
    /**
     * "auth0|cd3..........."
     */
    auth0?: string;
    /**
     * "<EMAIL>"
     */
    sub?: string;
    /**
     * "<tenant-nanoid>"
     */
    tenantId?: string;
    /**
     * "<domain-of-the-cluster>"
     * example: "cluster-a.dev-sagextrem.com" or "*-ci-v2.eu.dev-sagextrem.com" when using bizapps"
     */
    aud?: string;
    /**
     * "<domain-of-authentication-service>"
     * example: login.dev-sagextrem.com
     */
    iss?: string;
    iat?: number;
    exp?: number;
    /**
     * OneTrust preferences https://jira.sage.com/browse/XT-10838
     */
    pref?: number;
    /**
     * Initial app the access token was created for
     */
    app?: string;
    /**
     * What are the apps for this tenant/user the access token is valid for (ex SDMO and SHOWCASE)
     */
    apps?: string[];
}
