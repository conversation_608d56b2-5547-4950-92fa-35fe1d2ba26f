import { ConfigManager } from '@sage/xtrem-config';
import { ServerConfig } from '@sage/xtrem-shared';
import { Request } from 'express';
import * as fs from 'fs/promises';
import * as _ from 'lodash';
import * as https from 'node:https';
import * as fsp from 'path/posix';
import * as tls from 'tls';
import { logger } from './loggers';

export interface InternalServerConfig extends ServerConfig {
    sslShallowCopy?: tls.TlsOptions;
}

// Monkey patch tls.createSecureContext
// This has two advantages:
//   1. Being compatible with customer installation where NODE_EXTRA_CA_CERTS env variable is already defined
//   2. Do not require a bootstrap process or script to set the NODE_EXTRA_CA_CERTS env variable
//
// see:
//    https://github.com/nodejs/node/blob/a706342368ff7c05fc2ae6b55e9eaa47815785dd/lib/internal/tls/secure-context.js
//    https://github.com/nodejs/node/blob/e46c680bf2b211bbd52cf959ca17ee98c7f657f5/test/parallel/test-tls-addca.js

let extraCaPem: string[] = [];

export function initTlsLayer(): void {
    const oldCreateSecureContext = tls.createSecureContext;

    // cast to any because of read-only property error
    (tls as any).createSecureContext = (options?: tls.SecureContextOptions) => {
        const context = oldCreateSecureContext(options);

        extraCaPem.forEach(cert => {
            context.context.addCACert(cert.trim());
        });

        return context;
    };
}

function addCaFile(caFile: string, caFiles: string[]): void {
    if (caFile.startsWith('-----BEGIN ')) {
        logger.warn(`Ignoring CA file '${caFile}': it seems to be a PEM content`);
        return;
    }
    if (caFiles.includes(caFile)) {
        logger.warn(`Ignoring CA file '${caFile}': it has already been added`);
        return;
    }
    caFiles.push(caFile);
}

export async function loadExtraConfig(): Promise<void> {
    await loadExtraCaCert();
    setHttpsAgent();
    setDeviceHttpsAgent();
}

async function loadExtraCaCert(): Promise<void> {
    const tlsConfig = ConfigManager.current.security?.tls;
    const serverConfig = ConfigManager.current.server as InternalServerConfig;
    const sslServerConfig = serverConfig?.sslShallowCopy;
    const caFiles: string[] = [];
    if (tlsConfig?.extraCaFiles) {
        const extraCaFiles = Array.isArray(tlsConfig.extraCaFiles) ? tlsConfig.extraCaFiles : [tlsConfig.extraCaFiles];
        caFiles.push(...extraCaFiles);
    }
    if (sslServerConfig) {
        // if sslServerConfig.ca is a string, we are expecting a file path
        if (typeof sslServerConfig.ca === 'string') {
            addCaFile(sslServerConfig.ca, caFiles);
        }
        // if sslServerConfig.cert is defined we load the CA from the cert directory
        if (typeof sslServerConfig.cert === 'string') {
            const certDir = fsp.dirname(sslServerConfig.cert);
            // by convention, the CA file is named ca.crt
            const caFile = fsp.join(certDir, 'ca.crt');
            addCaFile(caFile, caFiles);
        }
    }

    // now we load the CA from the files to be able to add them during the secure context creation
    const extraPem: string[] = [];
    if (caFiles.length > 0) {
        await Promise.all(
            caFiles.map(async file => {
                extraPem.push(...(await loadPemFromFile(file)));
            }),
        );
    }
    ConfigManager.emitter.emit('extraCa', caFiles);
    const quotedCaFiles = caFiles.map(f => `'${f}'`);
    logger.info(`loading ${extraPem.length} extra CA from: [${quotedCaFiles}]`);
    extraCaPem = extraPem;
}

async function loadPemFromFile(file: string): Promise<string[]> {
    try {
        const content = (await fs.readFile(file, { encoding: 'ascii' })).replace(/\r\n/g, '\n');

        return (content.match(/-----BEGIN CERTIFICATE-----[A-Za-z0-9+/=\r\n]*-----END CERTIFICATE-----/g) || []).map(
            cert => cert.trim(),
        );
    } catch (e) {
        logger.error(`Cannot load CA '${file}': ${e.message}]`);
        return [];
    }
}

export function registerTlsChangeListener(server: tls.Server, configPath: string, name: string): void {
    const message = `Replacing secure context of the ${name} server`;
    ConfigManager.emitter.addListener('tlsChange', (propertyPath: string) => {
        const sslContext = _.property(configPath)(ConfigManager.current);
        if (sslContext && propertyPath === configPath) {
            // Replace the secure context the server. Existing connections to the server are not interrupted.
            // see https://nodejs.org/dist/latest-v18.x/docs/api/tls.html#serversetsecurecontextoptions
            setTimeout(() => {
                logger.info(message);
                try {
                    server.setSecureContext(sslContext);
                    ConfigManager.emitter.emit('tlsApply', configPath, sslContext);
                } catch (err) {
                    logger.error(`[FAILED] ${message}: ${err.message}`);
                }
            }, 500);
        }
    });
}

let interopHttpsAgent: https.Agent | null = null;

export function getInteropHttpsAgent(): https.Agent | null {
    return interopHttpsAgent;
}

function setHttpsAgent(): void {
    const sslConfig = getTlsConfig();
    interopHttpsAgent = null;
    if (sslConfig.ca && sslConfig.cert && sslConfig.key) {
        interopHttpsAgent = new https.Agent(sslConfig);
    }
}

export function getTlsConfig(): tls.ConnectionOptions {
    const serverConfig = ConfigManager.current.server as InternalServerConfig;
    if (!serverConfig?.ssl) {
        return {};
    }
    return {
        ca: serverConfig.ssl.ca,
        cert: serverConfig.ssl.cert,
        key: serverConfig.ssl.key,
    };
}

function isTlsSocket(socket: NodeJS.Socket | tls.TLSSocket): socket is tls.TLSSocket {
    return (socket as tls.TLSSocket).getPeerCertificate !== undefined;
}

export function getPeerCertificate(req: Request): tls.PeerCertificate | null {
    if (!isTlsSocket(req.socket)) {
        return null;
    }
    return req.socket.getPeerCertificate();
}

let deviceHttpsAgent: https.Agent | null = null;

export function getDeviceHttpsAgent(): https.Agent | null {
    return deviceHttpsAgent;
}

function setDeviceHttpsAgent(): void {
    const sslConfig = getDeviceTlsConfig();
    deviceHttpsAgent = null;
    if (sslConfig.ca && sslConfig.cert && sslConfig.key) {
        deviceHttpsAgent = new https.Agent(sslConfig);
    }
}

export function getDeviceTlsConfig(): tls.ConnectionOptions {
    const authenticationConfig = ConfigManager.current.authentication;

    if (!authenticationConfig?.ssl) {
        return {};
    }

    return {
        key: authenticationConfig.ssl.key,
        cert: authenticationConfig.ssl.cert,
        ca: authenticationConfig.ssl.ca,
    };
}
