import { Logger } from '@sage/xtrem-log';
import axios, { AxiosError, AxiosResponse, InternalAxiosRequestConfig } from 'axios';

const logger = Logger.getLogger(__filename, 'sage-id');

export class SageIdClient {
    private static errorResponseInterceptor(error: AxiosError): AxiosError {
        logger.error(`Sage ID error: ${error}`);
        if (error.response?.data) {
            logger.debug(() => `Sage ID Error response interceptor${JSON.stringify(error.response?.data, null, 4)}`);
        }
        return error;
    }

    private static successResponseInterceptor(response: AxiosResponse<any, any>): AxiosResponse<any, any> {
        logger.debug(
            () =>
                `Sage ID response: ${response.config.method} ${response.config.baseURL}${response.config.url} ${response.status}`,
        );
        logger.debug(() => `Sage ID Headers:\n${JSON.stringify(response.headers, null, 4)}`);

        if (response?.data) {
            logger.debug(() => JSON.stringify(response?.data, null, 4));
        }
        return response;
    }

    private static requestInterceptor(request: InternalAxiosRequestConfig<any>): InternalAxiosRequestConfig {
        logger.debug(() => `Sage ID request: ${request.method} ${request.baseURL}${request.url}`);
        logger.debug(() => `Sage ID Headers:\n${JSON.stringify(request.headers, null, 4)}`);
        if (request?.data) {
            logger.debug(() => `Sage ID Body:\n${JSON.stringify(request?.data, null, 4)}`);
        }
        return request;
    }

    // https://developer.sage.com/internal/identity/guides/calling-apis-machine/
    static async getToken(props: {
        url: string;
        clientId: string;
        clientSecret: string;
        audience: string;
    }): Promise<{ accessToken: string; expiresIn: number }> {
        const client = axios.create({
            baseURL: props.url,
            headers: {
                'Content-Type': 'application/json',
                Accept: 'application/json',
            },
        });

        client.interceptors.response.use(this.successResponseInterceptor, this.errorResponseInterceptor);

        client.interceptors.request.use(this.requestInterceptor);

        const result = await client.post(
            '/oauth/token',
            new URLSearchParams({
                client_secret: props.clientSecret,
                client_id: props.clientId,
                grant_type: 'client_credentials',
                audience: props.audience,
            }).toString(),
            { headers: { 'content-type': 'application/x-www-form-urlencoded' } },
        );

        if (result.status !== 200) {
            throw new Error(`Sage ID: Unexpected status code ${result.status}`);
        }

        if (!result.data?.access_token) {
            throw new Error('Sage ID: No access token found in auth response');
        }

        return { accessToken: result.data.access_token, expiresIn: result.data.expires_in ?? 0 };
    }

    static formatBearerAuthorization(token: string): string {
        return `Bearer ${token}`;
    }
}
