// @ts-check

// ================================================================================================================
// WARNING: This file must be a js file not encoded with bytenode to work at runtime with worker threads.
//          It is uglified at build time (see uglify:js).
//          We might investigate later, but at the moment there are issues with mocha tests and bytenode.
// ================================================================================================================

/** @type {import('dompurify').DOMPurify} */
// @ts-ignore
const createDOMPurify = require('dompurify');
const { JSDOM } = require('jsdom');
const { nanoid } = require('nanoid');
const {
    ATTR_ALIAS,
    ATTR_BREAK_PAGE_AFTER,
    ATTR_CONTEXT_CONDITION,
    ATTR_CONTEXT_FILTER,
    ATTR_CONTEXT_LIST_ORDER,
    ATTR_CONTEXT_OBJECT_PATH,
    ATTR_CONTEXT_OBJECT_TYPE,
    ATTR_FOOTER_GROUP,
    ATTR_PROPERTY_DATA_FORMAT,
    ATTR_PROPERTY_DATA_TYPE,
    ATTR_PROPERTY_DISPLAY_LABEL,
    ATTR_PROPERTY_NAME,
    ATTR_PROPERTY_PARENT_CONTEXT_TYPE,
    ATTR_QUERY_TABLE_HIDDEN_ROW,
} = require('@sage/xtrem-shared');

const workerpool = require('workerpool');

/**
 * @typedef {import('./xss.ts').HtmlSanitizerOptions} HtmlSanitizerOptions
 *
 * @typedef {import('./xss.ts').SanitizeResult} SanitizeResult
 *
 * @typedef TransformedData
 * @property {string} html
 * @property {string} [firstTag]
 * @property {Record<string,string>} templates
 *
 * @typedef ChildNames
 * @property {string[]} removed
 * @property {string[]} added
 *
 */

class DomChangeObserver {
    /** @type {MutationObserver} */
    #observer;

    /** @type {MutationRecord[]} */
    #records;

    /**
     *
     * @param {JSDOM} jsdom
     */
    constructor(jsdom) {
        this.#observer = new jsdom.window.MutationObserver(() => undefined);
        // Start observing the root node to detect changes
        // Options for the observer (which mutations to observe)
        const config = {
            attributes: true,
            attributeOldValue: true,
            characterDataOldValue: true,
            characterData: true,
            childList: true,
            subtree: true,
        };
        this.#observer.observe(jsdom.window.document.getRootNode(), config);
        this.#observer.takeRecords();
    }

    /**
     *
     * @param {Record<string,string>} templates
     * @returns {MutationRecord[]}
     */
    getChanges(templates) {
        /** @type {Record<string,Set<Node>>} */
        const uniqueTargetsByType = Object.create(null);
        this.#records = this.#observer.takeRecords().filter(record => {
            /** @type {Set<Node>} */
            let uniqueTargets = uniqueTargetsByType[record.type];
            if (!uniqueTargets) {
                uniqueTargets = new Set();
                uniqueTargetsByType[record.type] = uniqueTargets;
            }
            return DomChangeObserver.addElementChange(record, templates, uniqueTargets);
        });
        this.#observer.disconnect();

        return this.#records;
    }

    /**
     * Adds element change to the unique target set
     * @param {MutationRecord} r
     * @param {*} templates
     * @param {*} uniqueTargets
     * @returns {boolean}
     */
    static addElementChange(r, templates, uniqueTargets) {
        const targetElement = getTargetElement(r);
        if (targetElement == null || uniqueTargets.has(targetElement)) {
            return false;
        }

        uniqueTargets.add(targetElement);
        const oldValue = r.oldValue ? r.oldValue.trimEnd() : r.oldValue;
        switch (r.type) {
            case 'attributes': {
                // attribute values can contain spaces at the beginning and the end but they are not relevant
                return (
                    r.attributeName != null && oldValue?.trim() !== targetElement.getAttribute(r.attributeName)?.trim()
                );
            }
            case 'characterData': {
                let newValue = targetElement.nodeValue;
                if (newValue) {
                    Object.entries(templates).forEach(([k, v]) => {
                        newValue = (newValue ?? '').replace(k, v);
                    });
                }
                return oldValue !== newValue;
            }
            case 'childList':
                return r.removedNodes.length > 0;

            default:
                return true;
        }
    }

    /**
     * Adds element change to the unique target set
     * @param {MutationRecord} r
     * @param {*} templates
     * @param {*} uniqueTargets
     * @returns {boolean}
     */
    static getElementDetails(r, templates, uniqueTargets) {
        const targetElement = getTargetElement(r);
        if (targetElement == null || uniqueTargets.has(targetElement)) {
            return false;
        }

        uniqueTargets.add(targetElement);
        const oldValue = r.oldValue ? r.oldValue.trimEnd() : r.oldValue;
        switch (r.type) {
            case 'attributes': {
                // attribute values can contain spaces at the beginning and the end but they are not relevant
                return (
                    r.attributeName != null && oldValue?.trim() !== targetElement.getAttribute(r.attributeName)?.trim()
                );
            }
            case 'characterData': {
                let newValue = targetElement.nodeValue;
                if (newValue) {
                    Object.entries(templates).forEach(([k, v]) => {
                        newValue = (newValue ?? '').replace(k, v);
                    });
                }
                return oldValue !== newValue;
            }
            case 'childList':
                return r.removedNodes.length > 0;

            default:
                return true;
        }
    }

    /**
     *
     * @returns {string[]}
     */
    getChangeDetails() {
        /** @type {string[]} */
        const messages = [];
        // eslint-disable-next-line no-restricted-syntax
        for (const r of this.#records) {
            const target = r.target;
            switch (r.type) {
                case 'attributes':
                    {
                        const targetElement = getTargetElement(r);
                        if (targetElement) {
                            messages.push(
                                `${targetElement.nodeName} ${r.type} ${r.attributeName} ${JSON.stringify(
                                    r.oldValue,
                                )} changed to ${JSON.stringify(
                                    r.attributeName && targetElement.getAttribute(r.attributeName),
                                )}`,
                            );
                        }
                    }
                    break;
                case 'childList': {
                    /** @type {ChildNames} */
                    const childNames = { removed: [], added: [] };
                    r.removedNodes.forEach(n => childNames.removed.push(n.nodeName));
                    r.addedNodes.forEach(n => childNames.added.push(n.nodeName));
                    messages.push(
                        `${target.nodeName} ${r.type} removed [${childNames.removed}] added [${childNames.added}]`,
                    );
                    break;
                }
                case 'characterData':
                    messages.push(`${target.nodeName} ${r.type} changed "${r.oldValue}" to "${target.nodeValue}"`);
                    break;
                default:
                    break;
            }
        }
        return messages;
    }
}

/**
 * Sanitiezs the content of an html to prevent from XSS attacks
 * @param {string} htmlContent
 * @param {HtmlSanitizerOptions} [options]
 * @returns {SanitizeResult}
 */
function sanitizeHtml(htmlContent, options) {
    const { html, templates, firstTag } = getTransformedData(htmlContent, options);

    // JSDOM has poor performance, we might investigate linkedom https://www.npmjs.com/package/linkedom
    const jsdom = new JSDOM(html, {
        // includeNodeLocations: true, // make parsing slower but we might need it
        url: 'http://localhost', // for css @import url to be resolved by jsdom > whatwg-url
    });
    const document = getJsDomDocument(jsdom);

    const DOMPurify = _createDOMPurify(jsdom);

    let observer;
    if (options?.throwIfModified || options?.changes) {
        observer = new DomChangeObserver(jsdom);
    }

    DOMPurify.sanitize(document, {
        IN_PLACE: true,
        WHOLE_DOCUMENT: true,
        // we remove support of mathml which is in the default profile
        USE_PROFILES: { html: true, svg: true },
        // do not allow iframes this can display malicious content or disclose sensitive information like the server's IP address
        ADD_TAGS: ['meta', '#comment', '#document'],
        ADD_ATTR: [
            // for meta tag
            'http-equiv',
            'content',
            'charset',
            // Document editor special attributes
            ATTR_ALIAS,
            ATTR_CONTEXT_OBJECT_TYPE,
            ATTR_CONTEXT_OBJECT_PATH,
            ATTR_CONTEXT_CONDITION,
            ATTR_CONTEXT_FILTER,
            ATTR_CONTEXT_LIST_ORDER,
            ATTR_PROPERTY_DISPLAY_LABEL,
            ATTR_PROPERTY_NAME,
            ATTR_PROPERTY_DATA_FORMAT,
            ATTR_PROPERTY_DATA_TYPE,
            ATTR_QUERY_TABLE_HIDDEN_ROW,
            ATTR_FOOTER_GROUP,
            ATTR_PROPERTY_PARENT_CONTEXT_TYPE,
            ATTR_BREAK_PAGE_AFTER,
        ],
    });

    if (options?.throwIfModified || options?.changes) {
        const records = observer?.getChanges(templates);
        if (options?.throwIfModified && records && records.length > 0) {
            return { error: 'html content violate xss validation rules', details: observer?.getChangeDetails() };
        }
        if (options?.changes) {
            options.changes.records = records;
            options.changes.removed = DOMPurify.removed;
        }
    }
    const sanitized = Object.entries(templates).reduce((r, [k, v]) => r.replace(k, v), getHtml(jsdom, firstTag));
    // replace to get the original webpack special url
    return { html: sanitized.replace(/@import '\/~@sage\//g, "@import '~@sage/'") };
}

/**
 * Gets transforms data
 * @param {string} html
 * @param {HtmlSanitizerOptions} [options]
 * @returns {TransformedData}
 */
function getTransformedData(html, options) {
    /** @type {Record<string,string>} */
    const templates = Object.create(null);
    const blockRegex = /^\{\{(?:[#]\w+|\belse if |(?:[/]\w+|else)\}\})/;

    /**
     * Transforms html to be ready from loading in DOM.
     * Html with mustache templates requires special processing to make it compliant with jsdom/dompurify.
     * Templates are replaced by unique identifier and those that mark a begin/end block are replaced by
     * a comment tag to keep their position in the html tree
     * @param {string} chunk
     * @param {string} mustache
     * @returns {string}
     */
    const transform = (chunk, mustache) => {
        let id = `__${nanoid()}__`;
        if (blockRegex.test(mustache)) {
            // WARN: keep the id inside a comment, this is very important (see above)
            id = `<!-- ${id} -->`;
        }
        templates[id] = chunk;
        return id;
    };
    const mustacheRegex = /(?:<!--\s*)?(\{\{[^}{]+?\}\})(?:\s*-->)?/gm;

    // The HTML comment part of the regex from here: https://skeptric.com/html-comment-regexp/
    // Should not cause exponential backtracking as detected by CodeQL
    // see https://devina.io/redos-checker
    const firstTag =
        /^(?:<!--(?!-?>)(?:[^<-]|<(?!!--(?!>))|-(?!-!>))*?(?<!<!-)-->[\s\n\r]*)*<(!DOCTYPE|html|head|body)\b/i
            .exec(html)?.[1]
            .toLowerCase();

    return {
        // replace webpack special url by an url that can be resolved jsdom > whatwg-url
        html: (options?.ignoreTemplating ? html : html.replace(mustacheRegex, transform)).replace(
            /@import '~@sage\//g,
            "@import '/~@sage/",
        ),
        firstTag,
        templates,
    };
}

/**
 * Gets the jsdom document
 * @param {JSDOM} jsdom
 * @returns {Document}
 */
function getJsDomDocument(jsdom) {
    const document = jsdom.window.document;
    if (document.head.childNodes.length === 0) {
        document.head.remove();
    }
    if (document.body.childNodes.length === 0 && document.body.attributes.length === 0) {
        document.body.remove();
    }
    return document;
}

/**
 *
 * @param {JSDOM} jsdom
 * @param {string} [firstTag]
 * @returns {string}
 */
function getHtml(jsdom, firstTag) {
    const document = jsdom.window.document;
    if (document.head?.childNodes.length === 0) {
        document.head.remove();
    }
    if (document.body?.childNodes.length === 0 && document.body?.attributes.length === 0) {
        document.body.remove();
    }
    switch (firstTag) {
        case '!doctype':
            return jsdom.serialize();
        case 'html':
            return document.documentElement.outerHTML;
        case 'head':
        case 'body':
            return document.documentElement.innerHTML;
        default:
            return (document.head || document.body || document.documentElement).innerHTML;
    }
}

/**
 *
 * @param {JSDOM} jsdom
 * @returns {import('dompurify').DOMPurify}
 */
function _createDOMPurify(jsdom) {
    /** @type {import('dompurify').WindowLike} */
    const jsDomWindow = jsdom.window;

    /** @type {import('dompurify').DOMPurify} */
    const DOMPurify = createDOMPurify(jsDomWindow);

    DOMPurify.addHook('beforeSanitizeElements', currentNode => {
        if (currentNode.nodeName === 'STYLE') {
            if (currentNode.textContent) {
                // xml tags in css are considered harmful by dompurify, so we remove comments which is where we can have some.
                // The regex does not cover all cases but it is a good start
                currentNode.textContent = currentNode.textContent.replace(/\/\*[^*]*\*\//gm, '');
            }
        }
        return currentNode;
    });

    DOMPurify.addHook('uponSanitizeElement', (node, data) => {
        const element = /** @type {Element} */ (node);

        if (data.tagName === 'meta') {
            if ('charset' in element.attributes) {
                const encoding = element.getAttribute('charset');
                if (encoding !== 'utf-8') {
                    element.removeAttribute('charset');
                }
            }
            // special processing for the meta tag because the content attribute can contain malicious url
            if ('content' in element.attributes) {
                const content = element
                    .getAttribute('content')
                    ?.split(';')
                    .map(s => {
                        const parts = s.split('=');
                        // validate the url using DOMPurify method
                        if (/url/i.test(parts[0]) && !DOMPurify.isValidAttribute('a', 'href', parts[1])) {
                            return '';
                        }
                        return s;
                    })
                    .filter(s => !!s)
                    .join(';');
                element.removeAttribute('content');
                if (content) {
                    element.setAttribute('content', content);
                }
            }
        } else if (
            data.tagName === '#comment' &&
            node.textContent &&
            !/^(?:<!--)?\s*__([a-zA-Z0-9_-]+)__\s*(?:-->)?/.test(node.textContent)
        ) {
            // removing the node is a perf killer so we just replace the content by an empty string
            node.textContent = '';
        }
        return node;
    });

    return DOMPurify;
}

/**
 * Gets the target element of a mutation record or null
 * @param {MutationRecord} record
 * @returns {Element|null}
 */
function getTargetElement(record) {
    /** @type {*} */
    const target = record.target;
    // target instanceof Element does not work in worker
    return typeof target?.getAttribute === 'function' ? target : null;
}

/**
 * Adds attribute to element
 * @param {Element} node
 * @param {string} name
 * @param {string} value
 * @param {string | null} namespaceURI
 */
function addAtribute(node, name, value, namespaceURI) {
    if (namespaceURI) {
        node.setAttributeNS(namespaceURI, name, value);
    } else {
        /* Fallback to setAttribute() for browser-unrecognized namespaces e.g. "x-schema". */
        node.setAttribute(name, value);
    }
}

if (!workerpool.isMainThread && !process.env.XTREM_WORKER_ID) {
    // create a worker and register the public function
    workerpool.worker({
        sanitizeHtml,
    });
} else {
    module.exports = { sanitizeHtml };
}
