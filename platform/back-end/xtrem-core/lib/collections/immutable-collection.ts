/** @packageDocumentation @module runtime */
import { Async<PERSON><PERSON><PERSON>Reader, AsyncReader } from '@sage/xtrem-async-helper';
import { CollectionProperty } from '../properties';
import { Node, NodeQueryOptions } from '../ts-api';
import { BaseCollection } from './base-collection';

export class ImmutableCollection extends BaseCollection {
    #targetFactoryTick: number;

    /** @internal */
    constructor(sourceNode: Node, property: CollectionProperty) {
        super(sourceNode, property);
        this.#targetFactoryTick = this.getCurrentTargetFactoryTick();
    }

    /** @internal */
    // eslint-disable-next-line class-methods-use-this, @typescript-eslint/no-unused-vars
    getQueryForUpdate(options?: NodeQueryOptions): boolean {
        return false;
    }

    /** @internal */
    getReader(): AsyncReader<Node> {
        return new AsyncArrayReader(async () => {
            if (!this._nodes) this._nodes = await this.queryFullCollection();
            return this._nodes;
        });
    }

    private getCurrentTargetFactoryTick(): number {
        return this.sourceNode.$.context.transaction.getFactoryTick(this.property.targetFactory);
    }

    /** @internal */
    isValid(): boolean {
        if (this.sourceNode.$.state.isOld) {
            // For instance document.$.old.lines must not be invalidated if one of the lines of the document
            // was deleted otherwise, document.$.old will be rebuilt from the current content of the database (without the deleted line)
            return true;
        }
        // content is queried => it is invalid if target table(s) changed.
        return this.#targetFactoryTick === this.getCurrentTargetFactoryTick();
    }
}
