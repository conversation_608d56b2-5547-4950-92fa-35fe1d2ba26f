/** @packageDocumentation @module runtime */
import { asyncArray, AsyncResponse } from '@sage/xtrem-async-helper';
import { add } from '@sage/xtrem-decimal';
import { CollectionProperty } from '../properties';
import { Node, SqlWhereInterface } from '../ts-api';
import { BaseCollection } from './base-collection';

export interface SqlAggregateOptions<NodeT extends Node = Node> {
    where?: (node: NodeT) => AsyncResponse<boolean>;
    sum: (node: NodeT) => AsyncResponse<number>;
}

export interface SqlFindAndMapOptions<NodeT extends Node = Node, ResultT = any> {
    matching: (node: NodeT) => AsyncResponse<boolean>;
    ifFound: (node: NodeT) => AsyncResponse<ResultT>;
}

/**
 * @internal
 * Helper class to handle queries that are translated to SQL.
 * For now only handles sum aggregate but more expected in the future.
 */
export class SqlCollection implements SqlWhereInterface<Node> {
    constructor(
        readonly collection: BaseCollection,
        readonly where?: (node: Node) => AsyncResponse<boolean>,
    ) {}

    get sourceNode(): Node {
        return this.collection.sourceNode;
    }

    get property(): CollectionProperty {
        return this.collection.property;
    }

    // Only handles sum for now
    async sum(callback: (node: Node) => AsyncResponse<number>): Promise<number> {
        let nodes = asyncArray(await this.collection.nodes);
        const where = this.where;
        if (where) nodes = nodes.filter(node => where(node));
        return nodes.reduce(async (acc, node) => add(acc, await callback(node)), 0);
    }
}
