import { AsyncResponse, ValidationSeverity, integer, type Dict } from '@sage/xtrem-shared';
import { ValidationContext } from '../ts-api';
import { DataType, DataTypeMetadata, DataTypeOptions, MetaDataOptions } from './data-type';

export interface StringDataTypeOptions<T = unknown, ValT extends string | null = string> extends DataTypeOptions {
    maxLength?: number;
    isLocalized?: true;
    doNotTrim?: boolean;
    truncate?: boolean;
    defaultValue?: (this: T) => AsyncResponse<ValT | undefined>;
}

export interface StringDataTypeMetadata<TitleT> extends DataTypeMetadata<TitleT> {
    maxLength: number | null;
    isLocalized: boolean;
    doNotTrim: boolean;
    truncate: boolean;
}

export interface StringArrayDataTypeMetadata<TitleT> extends StringDataTypeMetadata<TitleT> {}

export class StringDataType<T = unknown, ValT extends string | null = string> extends DataType<
    ValT,
    T,
    StringDataTypeOptions<T, ValT>
> {
    constructor(options: StringDataTypeOptions<T, ValT>) {
        super('string', options);
    }

    get maxLength(): integer {
        return this.options.maxLength ?? Infinity;
    }

    get isLocalized(): boolean {
        return this.options.isLocalized ?? false;
    }

    public override async controlValue(node: T, cx: ValidationContext, val: string): Promise<void> {
        await super.controlValue(node, cx, val);
        if (this.maxLength && val && val.length > this.maxLength) {
            cx.addDiagnose(
                ValidationSeverity.error,
                `${val} exceeds the maximum length allowed for this field: ${this.maxLength} characters.`,
            );
        }
    }

    public override adaptValue(_node: T, val: ValT): AsyncResponse<ValT> {
        let adaptedValue = val;
        if (!this.options.doNotTrim) {
            adaptedValue = adaptedValue?.trim() as ValT;
        }
        if (this.options.truncate && adaptedValue && adaptedValue?.length > this.maxLength) {
            return `${adaptedValue?.substring(0, this.maxLength - 3)}...` as ValT;
        }
        return adaptedValue;
    }

    public override defaultValue(node: T): AsyncResponse<ValT | undefined> {
        return this.options.defaultValue ? this.options.defaultValue.call(node) : super.defaultValue(node);
    }

    override getMetaData<TitleT extends string | Dict<string>>(
        options: MetaDataOptions<TitleT, any, any>,
    ): StringDataTypeMetadata<TitleT> {
        const commonDataTypeMetaData = super.getMetaData<TitleT>(options);
        return {
            ...commonDataTypeMetaData,
            type: 'string',
            isDefault: false,
            maxLength: this.maxLength === Infinity ? null : this.maxLength,
            isLocalized: this.isLocalized,
            doNotTrim: !!this.dataTypeOptions().doNotTrim,
            truncate: !!this.dataTypeOptions().truncate,
        };
    }
}

export class StringArrayDataType<T = unknown, ValT extends string[] | null = string[]> extends DataType<
    ValT,
    T,
    StringDataTypeOptions
> {
    constructor(options: StringDataTypeOptions) {
        super('stringArray', options);
    }

    get maxLength(): integer {
        return this.options.maxLength ?? Infinity;
    }

    public override async controlValue(node: T, cx: ValidationContext, val: string[]): Promise<void> {
        await super.controlValue(node, cx, val);
        val.forEach(v => {
            if (this.maxLength && v && v.length > this.maxLength) {
                cx.addDiagnose(
                    ValidationSeverity.error,
                    `${v} exceeds the maximum length allowed for this field: ${this.maxLength} characters.`,
                );
            }
        });
    }

    override getMetaData<TitleT extends string | Dict<string>>(
        options: MetaDataOptions<TitleT, any, any>,
    ): StringArrayDataTypeMetadata<TitleT> {
        const commonDataTypeMetaData = super.getMetaData<TitleT>(options);
        return {
            name: commonDataTypeMetaData.name,
            title: commonDataTypeMetaData.title,
            type: 'stringArray',
            isDefault: false,
            maxLength: this.maxLength,
            isLocalized: !!this.dataTypeOptions().isLocalized,
            doNotTrim: !!this.dataTypeOptions().doNotTrim,
            truncate: !!this.dataTypeOptions().truncate,
        };
    }
}
