import { Dict } from '@sage/xtrem-shared';
import { TypeName } from '../decorators/decorator-utils';

/**
 * A data row to be reloaded for a factory
 * Will be used when replaying SQL files with data inside
 */
export type SqlFileDataRow = Dict<any>;

/**
 * Description of a column to be reloaded
 * Will be used when replaying SQL files with data inside
 */
export type ColumnMetadata = {
    /**
     * The name of the column
     */
    name: string;
    /**
     * The type of the column (more precisely is equal to colum.property.type)
     */
    type: TypeName;

    /**
     * Is the column nullable ?
     * Note: only set when true
     */
    isNullable?: true;

    /**
     * Is the column encrypted (when type==='string') ?
     * Note: only set when true
     */
    isEncrypted?: true;

    /**
     * Is the column localized (when type==='string') ?
     * Note: only set when true
     */
    isLocalized?: true;

    /**
     * Is the column owned by customer ?
     * Note: only set when true
     */
    isOwnedByCustomer?: true;

    /**
     * The factory that contains the property of the column
     * Only set when it differs from the factory the column is bound to
     */
    definingFactory?: string;

    /**
     * When type is 'reference' : the name of the target factory
     */
    targetFactoryName?: string;

    /**
     * When type is 'reference' : the root name of the target factory
     * Only set when differs from targetFactoryName
     */
    targetRootFactoryName?: string;

    /**
     * When type is 'enum', the list of possible members
     */
    enumMembers?: string[];
};

/**
 * Metadata for a factory
 * Will mainly be used when replaying SQL files
 */
export type FactoryMetadata = {
    /**
     * The name of the factory
     */
    name: string;
    /**
     * The name of the root factory
     */
    rootFactoryName: string;
    /**
     * Is the factory abstract ?
     * Note: only set when true
     */
    isAbstract?: true;
    /**
     * Name of the base factory (if any)
     */
    baseFactoryName?: string;
    /**
     * Is the factory a vital collection child ?
     * Note: only set when true
     */
    isVitalCollectionChild?: true;
    /**
     * Is the factory a vital child ?
     * Note: only set when true
     */
    isVitalChild?: true;
    /**
     * Is the factory shared by all the tenants ?
     * Note: only set when true
     */
    isSharedByAllTenants?: true;
    /**
     * The columns of the table
     */
    columns: ColumnMetadata[];
    /**
     * The natural keys (column names) to be used to reload rows
     */
    naturalKeyColumns: string[];

    vitalParentColumn?: ColumnMetadata;
};

/**
 * A data set (headers + rows) to be reloaded for a factory
 * Will be used when replaying SQL files with data inside
 */
export type SqlFileDataSet = {
    /**
     * The metadata of the factory
     */
    metadata: FactoryMetadata;
    /**
     * The rows to reload
     */
    rows: SqlFileDataRow[];
};

/**
 * A list of data, indexed by factory name
 */
export type SqlFileDataSets = Dict<SqlFileDataSet>;
