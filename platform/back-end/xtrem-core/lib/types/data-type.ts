/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable class-methods-use-this */

import { AnyValue, AsyncResponse, ColumnTypeName, type Dict } from '@sage/xtrem-shared';
import * as _ from 'lodash';
import { ServiceOption } from '../application';
import { ValidationContext } from '../ts-api/validation';

export interface DataTypeOptions {
    serviceOptions?: () => ServiceOption[];
}

export type DataTypeLocalize<TitleT> = (localizationKey: string, name: string) => TitleT;

export interface FieldBinding<TitleT> {
    bind: string;
    title?: TitleT;
    type?: string;
    enumType?: string | null;
}
export interface DataTypeMetadata<TitleT> {
    name: string;
    title: TitleT;
    type: string;
    isDefault: boolean;
}

export interface MetaDataOptions<TitleT, ApplicationT extends any, ContextT extends any> {
    localize: DataTypeLocalize<TitleT>;
    application: ApplicationT;
    context?: ContextT;
}

export abstract class DataType<ValT extends AnyValue, T, OptionsT extends DataTypeOptions = DataTypeOptions> {
    public name?: string;

    public pack?: string;

    constructor(
        readonly type: ColumnTypeName,
        protected readonly options: OptionsT,
    ) {}

    get serviceOptions(): ServiceOption[] {
        return this.options.serviceOptions?.() || [];
    }

    public defaultValue<FullT extends T>(_node: FullT): AsyncResponse<ValT | undefined> {
        return undefined;
    }

    public controlValue<FullT extends T>(_node: FullT, _cx: ValidationContext, _val: any): AsyncResponse<void> {
        return undefined;
    }

    public adaptValue<FullT extends T>(_node: FullT, _val: ValT): AsyncResponse<ValT> {
        return _val;
    }

    public dataTypeOptions(): OptionsT {
        return this.options;
    }

    public getLocalizedTitleKey(): string {
        // Remove the xtrem-core exception after we eliminate inlined data types
        if (!this.name && !process.cwd().includes('xtrem-core')) throw new Error('Data type name is missing');
        if (!this.pack && !process.cwd().includes('xtrem-core')) throw new Error('Data type package name is missing');
        return `${this.pack}/data_types__${_.snakeCase(this.name).replace(/[.-]/g, '_')}__name`;
    }

    public getMetaData<TitleT extends string | Dict<string>>(
        options: MetaDataOptions<TitleT, any, any>,
    ): DataTypeMetadata<TitleT> {
        let title: TitleT;
        try {
            title = options.localize(this.getLocalizedTitleKey(), this.name || '');
        } catch {
            title = this.name as TitleT; // Fallback to name if localization is not available (should not happen in production)
        }
        return {
            name: this.name,
            title,
        } as DataTypeMetadata<TitleT>;
    }
}

export const isInt32 = (val: any): boolean => {
    // eslint-disable-next-line no-bitwise
    return val === (val | 0);
};
