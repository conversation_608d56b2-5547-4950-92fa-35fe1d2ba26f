import { Config<PERSON>anager } from '@sage/xtrem-config';
import { ColumnTypeName, Dict, SystemError, ValidationSeverity } from '@sage/xtrem-shared';
import { Application } from '../application';
import { Context } from '../runtime';
import { Node, ValidationContext } from '../ts-api';
import { BinaryStream } from './binary-stream';
import { DataType, DataTypeMetadata, DataTypeOptions, MetaDataOptions } from './data-type';
import { MimeTypeHelper } from './mime-type-helper';
import { Stream } from './stream';
import { TextStream } from './text-stream';

export interface StreamDataTypeOptions extends DataTypeOptions {
    maxLength?: number;
    allowedContentTypes?: string[];
    dangerouslyUnsafe?: boolean;
}

export interface StreamDataTypeMetadata<TitleT> extends DataTypeMetadata<TitleT> {
    maxLength: number | null;
    allowedContentTypes: string[];
}

function shrinkString(val: string, length?: number): string {
    const maxlen = length || 500;
    return val.length > maxlen ? `${val.substr(0, maxlen / 2)}...${val.substr(-(maxlen / 2 + (maxlen % 2) - 3))}` : val;
}

abstract class StreamDataType<ValT extends Stream | null, T = unknown> extends DataType<
    ValT,
    T,
    StreamDataTypeOptions
> {
    constructor(
        type: ColumnTypeName,
        options: StreamDataTypeOptions,
        private readonly unit: string,
    ) {
        super(type, options);
    }

    get maxLength(): number {
        return this.options.maxLength ?? Infinity;
    }

    get allowedContentTypes(): string[] {
        return this.options.allowedContentTypes || [];
    }

    get dangerouslyUnsafe(): boolean {
        return !!this.options.dangerouslyUnsafe;
    }

    abstract checkLength(val: ValT): boolean;

    abstract getByteLength(val: ValT): number;

    abstract validateContent(node: T, name: string, val: ValT): Promise<void>;

    public override async controlValue(node: T, cx: ValidationContext, val: ValT): Promise<void> {
        await super.controlValue(node, cx, val);
        const name = cx.path.join('.');

        const maxStreamSize = ConfigManager.getSizeLimit('maxStreamSize');
        if (this.getByteLength(val) > maxStreamSize) {
            cx.addDiagnose(
                ValidationSeverity.error,
                `Value of '${name}' exceeds the maximum length allowed: got ${
                    (val as any).value.length
                } expected ${maxStreamSize} ${this.unit}s max.`,
            );
            return;
        }

        if (!this.checkLength(val)) {
            cx.addDiagnose(
                ValidationSeverity.error,
                `Value of '${name}' exceeds the maximum length allowed: got ${(val as any).value.length} expected ${
                    this.maxLength
                } ${this.unit}s max.`,
            );
            return;
        }
        await this.validateContent(node, name, val);
    }
}

export class BinaryStreamDataType<ValT extends BinaryStream | null = BinaryStream, T = unknown> extends StreamDataType<
    ValT,
    T
> {
    constructor(options: StreamDataTypeOptions) {
        super('binaryStream', options, 'byte');
    }

    // eslint-disable-next-line class-methods-use-this
    getByteLength(val: BinaryStream | null): number {
        const value = val?.value;
        if (!value) return 0;
        return value.byteLength;
    }

    checkLength(val: BinaryStream | null): boolean {
        const value = val?.value;
        if (!value) return true;
        const length = this.getByteLength(val);
        return !(this.maxLength && length > this.maxLength);
    }

    async validateContent(node: T, name: string, val: BinaryStream | null): Promise<void> {
        const isPlatformNode = (node as any as Node).$.factory.isPlatformNode;
        if (!isPlatformNode && val) {
            await this.validateContentType(name, val);
        }
    }

    async validateContentType(name: string, val: BinaryStream): Promise<void> {
        const value = val.value;
        const streamContentType = await MimeTypeHelper.guessFromBuffer(value);
        const allowedContentTypesByConfig = ConfigManager.current.binaryStreamContentTypes || [];

        if (!MimeTypeHelper.matchPatterns(allowedContentTypesByConfig, streamContentType)) {
            throw new SystemError(
                `binary stream property '${name}' has a content type '${streamContentType}' not authorized by config. Expecting [${allowedContentTypesByConfig}]`,
            );
        }
        if (!MimeTypeHelper.matchPatterns(this.allowedContentTypes, streamContentType)) {
            throw new SystemError(
                `binary stream property '${name}' has a content type '${streamContentType}' not authorized by property dataType. Expecting [${this.allowedContentTypes.map(
                    t => `'${t}'`,
                )}]`,
            );
        }
    }

    override getMetaData<TitleT extends string | Dict<string>>(
        options: MetaDataOptions<TitleT, Application, Context>,
    ): StreamDataTypeMetadata<TitleT> {
        const commonDataTypeMetaData = super.getMetaData<TitleT>(options);

        return {
            ...commonDataTypeMetaData,
            type: 'binaryStream',
            isDefault: false,
            maxLength: this.maxLength === Infinity ? null : this.maxLength,
            allowedContentTypes: this.allowedContentTypes,
        };
    }
}

export class TextStreamDataType<ValT extends TextStream = TextStream, T = unknown> extends StreamDataType<ValT, T> {
    constructor(options: StreamDataTypeOptions) {
        super('textStream', options, 'character');
    }

    static default = new TextStreamDataType({
        maxLength: 2048,
        allowedContentTypes: ['application/json', 'text/plain'],
    });

    // eslint-disable-next-line class-methods-use-this
    getByteLength(val: TextStream | null): number {
        const value = val?.value;
        if (!value) return 0;
        return Buffer.byteLength(value);
    }

    checkLength(val: TextStream): boolean {
        const value = val.value;
        if (!value) return true;
        const length = value.length;
        return !(this.maxLength && length > this.maxLength);
    }

    async validateContent(node: T, name: string, val: TextStream): Promise<void> {
        const isPlatformNode = (node as any as Node).$.factory.isPlatformNode;
        if (!isPlatformNode) {
            await this.validateContentType(name, val);
        }
        if (MimeTypeHelper.matchPatterns(await MimeTypeHelper.guessFromString(val.value), '*/xml')) {
            TextStreamDataType.validateXmlAgainstXXE(val.value);
        }
    }

    async validateContentType(name: string, val: TextStream): Promise<void> {
        const textStream = val;
        const streamContentType = await textStream.contentType;

        const allowedContentTypesByConfig = ConfigManager.current.textStreamContentTypes || [];

        if (!MimeTypeHelper.matchPatterns(allowedContentTypesByConfig, streamContentType)) {
            throw new SystemError(
                `text stream property '${name}' value '${shrinkString(
                    val.toString(),
                )}' has a content type '${streamContentType}' not authorized by config. Expecting [${allowedContentTypesByConfig}]`,
            );
        }
        if (!MimeTypeHelper.matchPatterns(this.allowedContentTypes, streamContentType)) {
            throw new SystemError(
                `text stream property '${name}' value '${shrinkString(
                    val.toString(),
                )}' has a content type '${streamContentType}' not authorized by property dataType. Expecting [${this.allowedContentTypes.map(
                    t => `'${t}'`,
                )}]`,
            );
        }
    }

    static validateXmlAgainstXXE(value: string): void {
        // DTD is considered unsafe, xml schema should be used instead
        // https://cheatsheetseries.owasp.org/cheatsheets/XML_Security_Cheat_Sheet.html
        // https://cheatsheetseries.owasp.org/cheatsheets/XML_External_Entity_Prevention_Cheat_Sheet.html
        const dtdRegex = /<!DOCTYPE\s+/i;
        // see https://www.w3.org/TR/xml/#sec-entity-decl
        const systemEntityRegex = /<!ENTITY\s+(?:%\s+)?[^><\s]+\s+SYSTEM/;
        if (dtdRegex.test(value) || systemEntityRegex.test(value)) {
            throw new Error('DTD not allowed in xml');
        }
    }

    // eslint-disable-next-line class-methods-use-this
    override async adaptValue(node: T, val: ValT): Promise<ValT> {
        if (this.dangerouslyUnsafe) {
            // get content type to be sure it is verified
            // eslint-disable-next-line @typescript-eslint/no-unused-expressions
            await val.contentType;
            return val;
        }
        return (await val.sanitized()) as ValT;
    }

    override getMetaData<TitleT extends string | Dict<string>>(
        options: MetaDataOptions<TitleT, Application, Context>,
    ): StreamDataTypeMetadata<TitleT> {
        const commonDataTypeMetaData = super.getMetaData<TitleT>(options);

        return {
            ...commonDataTypeMetaData,
            type: 'textStream',
            isDefault: false,
            maxLength: this.maxLength === Infinity ? null : this.maxLength,
            allowedContentTypes: this.allowedContentTypes,
        };
    }
}
