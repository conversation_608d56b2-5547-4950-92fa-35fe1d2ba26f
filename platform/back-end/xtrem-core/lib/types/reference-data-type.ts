/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable class-methods-use-this */

import { UnPromised } from '@sage/xtrem-async-helper';
import { AsyncResponse, Dict, LogicError } from '@sage/xtrem-shared';
import { camelCase } from 'lodash';
import { Application, Package } from '../application';
import { Property } from '../properties';
import { Context, NodeFactory } from '../runtime';
import { Extend, Node, NodeQueryFilter, PropertyQueryFilter } from '../ts-api';
import { JoinLiteralValue } from '../ts-api/join';
import { DataType, DataTypeMetadata, DataTypeOptions, FieldBinding, MetaDataOptions } from './data-type';

/** Type for property paths in dot notation, like 'address.city' */
export type PropertyPath<
    ValT extends Node | null,
    SeenT = never,
    Depth extends unknown[] = [unknown],
    K extends keyof ValT = keyof ValT,
> = K extends string
    ? // eliminate system keys except '_id'
      K extends '$' | Exclude<`_${infer Unused}`, '_id'>
        ? never
        : ValT[K] extends Promise<infer RefT extends Node | null>
          ? // Test SeenT to avoids infinite recursion on cyclic types
            RefT extends SeenT
              ? never
              : // Limit to 3 levels of depth, to avoid generating types with too many members
                Depth['length'] extends 3
                ? never
                : `${K}.${PropertyPath<RefT, SeenT | ValT, [...Depth, unknown]>}`
          : K
    : never;

export interface LookupDefinition<ValT extends Node> {
    readonly valuePath: PropertyPath<ValT>;
    readonly helperTextPath?: PropertyPath<ValT>;
    readonly imageFieldPath?: PropertyPath<ValT>;
    readonly tunnelPageIdPath?: PropertyPath<ValT>;
    readonly tunnelPage?: string;
    columnPaths: readonly PropertyPath<ValT>[];
}

export interface ReferenceDataTypeOptions<ValT extends Node | null, NodeT extends Node> extends DataTypeOptions {
    reference: () => { new (): ValT };
    filters?: {
        lookup?: NodeQueryFilter<ValT extends Node | null ? ValT & Node : never, Extend<NodeT>>;
        control?: NodeQueryFilter<ValT extends Node | null ? ValT & Node : never, Extend<NodeT>>;
    };
    isNullable?: boolean;
    ignoreIsActive?: boolean;
    isDefault?: boolean;
    lookup: LookupDefinition<NonNullable<ValT>>;
    referenceNodePackage?: string;
}

export interface ReferenceDataTypeMetadata<TitleT> extends DataTypeMetadata<TitleT> {
    node: string;
    value: FieldBinding<TitleT>;
    helperText?: FieldBinding<TitleT>;
    imageField?: FieldBinding<TitleT>;
    columns: FieldBinding<TitleT>[];
    tunnelPage?: string;
    tunnelPageId?: FieldBinding<TitleT>;
}

export class ReferenceDataType<ValT extends Node, NodeT extends Node = Node> extends DataType<
    ValT,
    NodeT,
    ReferenceDataTypeOptions<ValT, NodeT>
> {
    constructor(options: ReferenceDataTypeOptions<ValT, NodeT>) {
        super('reference', options);
    }

    get isNullable(): boolean {
        return !!this.options.isNullable;
    }

    get ignoreIsActive(): boolean {
        return !!this.options.ignoreIsActive;
    }

    get isDefault(): boolean {
        return !!this.options.isDefault;
    }

    get referenceNodePackage(): string | undefined {
        return this.options.referenceNodePackage;
    }

    get node(): string {
        return `${this.referenceNodePackage}/${this.options.reference().name}`;
    }

    /**
     * Returns the metadata of the data type.
     * This is called from 2 places:
     * - from the graphql metadata endpoint in xtrem-core, to provide the data type fragment of the response.
     * - from the metadata upgrader in xtrem-metadata, to provide the _attributes_ of reference data types.
     * In the first case the titles are returned as strings because we only need the titles for the user's locale.
     * In the second case the titles are returned as Dict<string> because we need the values for all the locales.
     * The TitleT generic type allows us to handle these two scenarios with a type-safe implementation.
     */
    override getMetaData<TitleT extends string | Dict<string>>(
        options: MetaDataOptions<TitleT, Application, Context>,
    ): ReferenceDataTypeMetadata<TitleT> {
        const factory = options.application?.getFactoryByConstructor(this.options.reference());

        if (!factory) throw new LogicError('Factory not found for reference data type');

        const { valuePath, helperTextPath, columnPaths, tunnelPageIdPath, tunnelPage, imageFieldPath } =
            this.dataTypeOptions().lookup;

        const getBinding = (path: string, pathProp: Property): FieldBinding<TitleT> => {
            const enumType =
                pathProp.isEnumProperty() || pathProp.isEnumArrayProperty() ? pathProp.dataType.enumFullName() : null;
            return {
                bind: path,
                title: pathProp && options.localize(pathProp.getLocalizedTitleKey(), pathProp.name),
                type: pathProp.type,
                enumType,
            };
        };

        const getPropertyFromPath = (
            startFactory: NodeFactory,
            path: string,
            keys = path.split('.'),
        ): FieldBinding<TitleT> => {
            const [propertyName, ...rest] = keys;
            const prop = startFactory.findProperty(propertyName);
            if (keys.length === 1 || prop.isTextStreamProperty() || prop.isBinaryStreamProperty()) {
                // If we are at the end of the path, return the binding
                if (prop.isForeignNodeProperty()) {
                    throw new LogicError(
                        `Bind path on dataType cannot end on a reference or collection: ${this.name} - ${path}`,
                    );
                }

                return getBinding(path, prop);
            }

            if (!prop.isReferenceProperty()) throw prop.logicError(`Invalid property in dataType: ${this.name}`);
            return getPropertyFromPath(prop.targetFactory, path, rest);
        };

        const value = getPropertyFromPath(factory, valuePath);

        const helperText = helperTextPath ? getPropertyFromPath(factory, helperTextPath) : undefined;

        const imageField = imageFieldPath ? getPropertyFromPath(factory, imageFieldPath) : undefined;

        const tunnelPageId = tunnelPageIdPath ? getPropertyFromPath(factory, tunnelPageIdPath) : undefined;

        const columns = columnPaths.map((columnPath: string) => {
            return getPropertyFromPath(factory, columnPath);
        });

        if (!this.name) throw new LogicError('name is missing');

        // Temporary code to handle auto-generated missing reference data types
        // TODO: remove when we have reference data types for all published nodes
        let title: TitleT;
        try {
            title = options.localize(this.getLocalizedTitleKey(), this.name);
        } catch {
            title = options.localize(factory.getLocalizedTitleKey(), this.name);
        }

        return {
            name: this.name,
            title,
            type: 'reference',
            isDefault: !!this.options.isDefault,
            node: `${this.pack}/${factory.name}`,
            value,
            helperText,
            imageField,
            columns,
            tunnelPage,
            tunnelPageId,
        };
    }

    static getNodeDefaultDataType(application: Application, factory: NodeFactory): ReferenceDataType<Node> | undefined {
        const dataTypes = Object.values(application.dataTypes).filter(
            dataType => dataType instanceof ReferenceDataType && dataType.options.reference().name === factory.name,
        );

        const defaultDataType =
            dataTypes.find(elt => elt instanceof ReferenceDataType && elt.isDefault) ??
            dataTypes.find(elt => elt instanceof ReferenceDataType && elt.name === camelCase(factory.name)) ??
            dataTypes[0];

        return defaultDataType as ReferenceDataType<Node> | undefined;
    }

    private checkReferenceDataTypeUniqueness(referenceNodeFactory: NodeFactory, pack: Package): void {
        // default reference data types could not be defined in service package
        if (this.isDefault && referenceNodeFactory.package.isService)
            throw new Error(
                `isDefault Reference datatype cannot be defined in a service package. ${this.name} in ${pack.name}`,
            );
        if (this.options.isDefault) {
            // check for duplicate default reference data types
            const duplicateDefaultReferenceDataType = Object.values(pack.application.dataTypes).find(
                dt =>
                    dt.name !== this.name &&
                    dt instanceof ReferenceDataType &&
                    dt.options.isDefault &&
                    dt.options.reference().name === this.options.reference().name,
            );

            if (duplicateDefaultReferenceDataType) {
                throw new Error(
                    `Default dataType ${this.name} for ${this.options.reference().name} node in ${pack.name} already declared as default in ${duplicateDefaultReferenceDataType.pack} -> ${duplicateDefaultReferenceDataType.name}`,
                );
            }
        }
    }

    private getDataTypeReferenceNodePackage(referenceNodeFactory: NodeFactory, pack: Package): string {
        // check if a node is extended in the current package
        if (referenceNodeFactory.extensions?.find(ext => NodeFactory.getExtensionPackage(ext).name === pack.name)) {
            return pack.name;
        }

        return referenceNodeFactory.package.name;
    }

    setReferenceNodePackage(pack: Package): void {
        const referenceNodeFactory = pack.application.getFactoryByName(this.options.reference().name);
        this.checkReferenceDataTypeUniqueness(referenceNodeFactory, pack);
        this.options.referenceNodePackage = this.getDataTypeReferenceNodePackage(referenceNodeFactory, pack);
    }
}

export type JoinMember<T extends Node, This extends Node, K extends Exclude<keyof T, '$'>> =
    | string
    | JoinLiteralValue
    | ((this: Extend<This>) => AsyncResponse<PropertyQueryFilter<This, UnPromised<T[K]>>>)
    | (T[K] extends Promise<infer U> ? (U extends Node ? PropertyJoin<U, This> : never) : never);

export type PropertyJoin<T extends Node, This extends Node = T> = Partial<{
    [K in Exclude<keyof T, '$'>]: JoinMember<T, This, K>;
}>;

export type InternalPropertyJoin<T extends Node, This extends Node = T> = Dict<JoinMember<T, This, any>>;
