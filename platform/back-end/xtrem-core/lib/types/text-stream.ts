import { Maybe } from '@sage/xtrem-shared';
import { htmlSanitizer } from '../security/xss';
import { MimeTypeHelper } from './mime-type-helper';
import { Stream } from './stream';

/** @packageDocumentation @module types */
export class TextStream extends Stream {
    #contentType: Maybe<string>;

    #verified = false;

    constructor(
        public value: string,
        contentType?: string,
    ) {
        super();
        if (contentType && !MimeTypeHelper.isValidMimeType(contentType)) {
            throw new Error(`Invalid content type: ${contentType}`);
        }
        this.#contentType = contentType;
    }

    static fromString(value: string, contentType?: string): TextStream {
        return new TextStream(value, contentType);
    }

    private static fromSanitizedString(value: string, contentType: string): TextStream {
        const text = new TextStream(value, contentType);
        text.#verified = true;
        return text;
    }

    static fromJsonObject(value: any): TextStream {
        return new TextStream(JSON.stringify(value), 'application/json');
    }

    static isTextStream(obj: any): obj is TextStream {
        return obj instanceof TextStream;
    }

    static readonly empty = new TextStream('');

    private async verifyContentType(): Promise<void> {
        if (!this.#contentType || !this.#verified) {
            this.#verified = true;
            this.#contentType = await MimeTypeHelper.guessFromString(this.value, this.#contentType);
        }
    }

    get contentType(): Promise<string> {
        return (async () => {
            await this.verifyContentType();
            return this.#contentType!;
        })();
    }

    async sanitized(): Promise<TextStream> {
        if (MimeTypeHelper.matchPatterns(await this.contentType, '*/html')) {
            // Prettier formats large embedded css style directives on several lines with spaces and newlines
            // between the double quotes and the value.
            // Purify does not like these heading and trailing spaces around the css value
            // so we trim the text inside the double quotes.
            const fixedValue = this.value?.replace(/style="([^"]+)"/g, (_all, val) => `style="${val.trim()}"`);
            const sanitized = await htmlSanitizer(fixedValue, { throwIfModified: true });
            return TextStream.fromSanitizedString(sanitized, await this.contentType);
        }
        return this;
    }

    override toString(): string {
        return this.value;
    }

    compareTo(arg: any): number {
        if (arg == null) return 1;

        if (!(arg instanceof TextStream)) throw new Error(`invalid arg passed to TextStream compare: ${arg}`);

        if (this.value === arg.value) return 0;
        if (this.value < arg.value) return -1;
        if (this.value > arg.value) return 1;
        return 0;
    }
}
