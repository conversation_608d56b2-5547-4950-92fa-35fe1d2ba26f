import { AnyValue, type Dict } from '@sage/xtrem-shared';
import { ValidationContext } from '../ts-api/validation';
import { DataType, DataTypeMetadata, DataTypeOptions, MetaDataOptions } from './data-type';

export interface JsonDataTypeOptions extends DataTypeOptions {}

export interface JsonDataTypeMetadata<TitleT> extends DataTypeMetadata<TitleT> {}

export class JsonDataType<T = unknown, ValT extends AnyValue = AnyValue> extends DataType<
    ValT,
    T,
    JsonDataTypeOptions
> {
    constructor(options: JsonDataTypeOptions = {}) {
        super('json', options);
    }

    public override async controlValue(node: T, cx: ValidationContext, val: object): Promise<void> {
        await super.controlValue(node, cx, val);
    }

    override getMetaData<TitleT extends string | Dict<string>>(
        options: MetaDataOptions<TitleT, any, any>,
    ): JsonDataTypeMetadata<TitleT> {
        const commonDataTypeMetaData = super.getMetaData<TitleT>(options);

        return {
            ...commonDataTypeMetaData,
            type: 'json',
            isDefault: false,
        };
    }
}
