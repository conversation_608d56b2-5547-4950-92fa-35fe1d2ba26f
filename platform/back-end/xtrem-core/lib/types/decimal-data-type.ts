import { integer } from '@sage/xtrem-client';
import { Decimal } from '@sage/xtrem-decimal';
import { AsyncResponse, ValidationSeverity, type Dict } from '@sage/xtrem-shared';
import * as _ from 'lodash';
import { decimal } from '../ts-api';
import { ValidationContext } from '../ts-api/validation';
import { DataType, DataTypeMetadata, DataTypeOptions, MetaDataOptions } from './data-type';

export enum RoundingMode {
    roundUp,
    roundDown,
    roundCeil,
    roundFloor,
    roundHalfUp,
    roundHalfDown,
    roundHalfEven,
    roundHalfCeil,
    roundHalfFloor,
}

export interface DecimalDataTypeOptions extends DataTypeOptions {
    precision?: number;
    scale?: number;
    roundingMode?: RoundingMode;
}

export interface DecimalDataTypeMetadata<TitleT> extends DataTypeMetadata<TitleT> {
    precision: number;
    scale: number;
    roundingMode: keyof RoundingMode;
}

export class DecimalDataType<T = unknown, ValT extends decimal | null = decimal> extends DataType<
    ValT,
    T,
    DecimalDataTypeOptions
> {
    static defaultRoundingMode = RoundingMode.roundHalfUp;

    static defaultPrecision = 16;

    static defaultScale = 2;

    constructor(protected override options: DecimalDataTypeOptions) {
        super('decimal', options);
    }

    get roundingMode(): RoundingMode {
        return this.options.roundingMode ?? DecimalDataType.defaultRoundingMode;
    }

    get precision(): integer {
        return this.options.precision ?? DecimalDataType.defaultPrecision;
    }

    get scale(): integer {
        // if we have a precision but no scale, assume that scale is 0
        return this.options.scale ?? (this.options.precision === undefined ? DecimalDataType.defaultScale : 0);
    }

    private static toImplementation(val: decimal): Decimal {
        return (val as unknown) instanceof Decimal ? (val as unknown as Decimal) : new Decimal(val);
    }

    private static fromImplementation(val: Decimal): decimal {
        return val as unknown as decimal;
    }

    public override adaptValue(_node: T, val: ValT): AsyncResponse<ValT> {
        if (val == null) return val;
        const impl = DecimalDataType.toImplementation(val).toDecimalPlaces(this.scale, this.roundingMode);
        return DecimalDataType.fromImplementation(impl) as ValT;
    }

    public override async controlValue(node: T, cx: ValidationContext, val?: decimal): Promise<void> {
        await super.controlValue(node, cx, val);

        const impl = val && DecimalDataType.toImplementation(val);
        if (this.precision && impl && impl.precision() > this.precision) {
            cx.addDiagnose(
                ValidationSeverity.error,
                `${val} exceeds the maximum precision allowed for this field: ${this.precision}.`,
            );
        }

        if (this.scale && impl && impl.decimalPlaces() > this.scale) {
            cx.addDiagnose(
                ValidationSeverity.error,
                `${val} exceeds the maximum scale allowed for this field: ${this.scale}.`,
            );
        }
    }

    static toDecimalPlaces(val: number, scale: number, roundingMode = DecimalDataType.defaultRoundingMode): decimal {
        const impl = this.toImplementation(val).toDecimalPlaces(scale, roundingMode);
        return this.fromImplementation(impl);
    }

    override getMetaData<TitleT extends string | Dict<string>>(
        options: MetaDataOptions<TitleT, any, any>,
    ): DecimalDataTypeMetadata<TitleT> {
        const commonDataTypeMetaData = super.getMetaData<TitleT>(options);
        return {
            ...commonDataTypeMetaData,
            type: 'decimal',
            isDefault: false,
            precision: this.precision,
            scale: this.scale,
            roundingMode: _.invert(_.pickBy(RoundingMode, _.isNumber))[this.roundingMode] as keyof RoundingMode, // TODO: move the enum from xtrem-shared to xtrem-core to get localization
        };
    }
}
