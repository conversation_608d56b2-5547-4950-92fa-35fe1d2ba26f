/** @ignore */ /** */
/* eslint-disable no-bitwise */
import {
    date,
    DateRange,
    Datetime,
    DatetimeRange,
    DateValue,
    DecimalRange,
    IntegerRange,
    time,
    Time,
} from '@sage/xtrem-date-time';
import { Decimal } from '@sage/xtrem-decimal';
import { AnyValue, LogicError, SystemError } from '@sage/xtrem-shared';
import * as crc32 from 'crc-32';
import { NodeStorage, TypeName } from '../decorators';
import { Property } from '../properties';
import { friendlyJsonParse, PropertyAndValue } from '../runtime';
import { PropertyDecorator } from '../runtime/property';
import { stringToArray } from './arrays';
import { BinaryStream } from './binary-stream';
import { TextStream } from './text-stream';
import { Uuid } from './uuid';

/**
 * `notNull(x)` is equivalent to `x!` but keeps sonar quiet
 *
 * We use it to deal with nullable properties inside functions that must be convertible to SQL
 * because the ts-to-sql converter does not (yet) support local variables inside functions.
 */
export const notNull = <T extends AnyValue>(x: T): NonNullable<T> => {
    return x as NonNullable<T>;
};

export function typeDefaultValue(
    type: TypeName | undefined,
    isNullable = false,
    isLocalized = false,
    storage?: NodeStorage,
): any {
    if (isNullable) return null;
    switch (type) {
        case 'string':
            return storage === 'sql' && isLocalized ? {} : '';
        case 'float':
        case 'double':
        case 'integer':
        case 'short':
            return 0;
        case 'integerRange':
            return new IntegerRange(null, null);
        case 'integerArray':
        case 'enumArray':
        case 'referenceArray':
        case 'stringArray':
            return [];
        case 'decimalRange':
            return new DecimalRange(null, null);
        case 'decimal':
            return Decimal.make(0);
        case 'enum':
            return 1;
        case 'boolean':
            return false;
        case 'date':
            return date.today();
        case 'dateRange':
            return new DateRange(null, null);
        case 'datetimeRange':
            return new DatetimeRange(null, null);
        case 'time':
            return time.now('UTC');
        case 'datetime':
            return Datetime.now();
        case 'reference':
            return null;
        case 'uuid':
            return Uuid.generate();
        case 'binaryStream':
            return BinaryStream.fromBuffer(Buffer.alloc(0));
        case 'textStream':
            return TextStream.empty;
        case 'json':
            return {};
        default:
            throw new LogicError(`unsupported type ${type}`);
    }
}

/**
 * Returns the default value for a custom field, based on its type and isMandatory attribute.
 * We cannot use getTypeDefaultValue() for this because custom fields do not have an isNullable attribute.
 * This function only returns `null` if the field is not mandatory and the type does not have an _obvious_ default value.
 * 0 is an obvious default value for numbers but null is a more obvious default value than date.today() for non mandatory dates.
 */
export function customFieldTypeDefaultValue(type: TypeName | undefined, isMandatory = false): any {
    switch (type) {
        case 'string':
            return '';
        case 'float':
        case 'double':
        case 'integer':
        case 'short':
            return 0;
        case 'integerRange':
            return new IntegerRange(null, null);
        case 'integerArray':
        case 'enumArray':
        case 'referenceArray':
        case 'stringArray':
            return [];
        case 'decimalRange':
            return new DecimalRange(null, null);
        case 'decimal':
            return Decimal.make(0);
        case 'enum':
            return isMandatory ? 1 : null;
        case 'boolean':
            return false;
        case 'date':
            return isMandatory ? date.today() : null;
        case 'dateRange':
            return new DateRange(null, null);
        case 'datetimeRange':
            return new DatetimeRange(null, null);
        case 'time':
            return isMandatory ? time.now('UTC') : null;
        case 'datetime':
            return isMandatory ? Datetime.now() : null;
        case 'reference':
            return null;
        case 'uuid':
            return isMandatory ? Uuid.generate() : null;
        case 'binaryStream':
            return BinaryStream.fromBuffer(Buffer.alloc(0));
        case 'textStream':
            return TextStream.empty;
        case 'json':
            return {};
        default:
            throw new LogicError(`unsupported type ${type}`);
    }
}

/** @internal */
function getTypeError(message: string, propertyName?: string): Error {
    if (propertyName) return new LogicError(`column ${propertyName}: ${message}`);
    return new Error(message);
}

export function safeParseInt(value: unknown, propertyName?: string): number {
    const val = Number(value);
    if (!Number.isInteger(val)) throw getTypeError(`invalid integer value ${value}`, propertyName);
    return val;
}

function safeParseFloat(value: unknown, propertyName?: string): number {
    const val = Number(value);
    if (!Number.isFinite(val)) throw getTypeError(`invalid number value ${value}`, propertyName);
    return val;
}

function safeParseBoolean(value: unknown, propertyName?: string): boolean {
    if (typeof value === 'boolean') return value;
    if (value === 'false') return false;
    if (value === 'true') return true;
    throw getTypeError(`invalid boolean value ${value}`, propertyName);
}

function assertString(value: unknown): asserts value is string {
    if (typeof value !== 'string') throw getTypeError(`value is not a string: ${value} of type ${typeof value}`);
}

/**
 * Parses a value according to the specified type, converting it to the appropriate JavaScript type or structure.
 *
 * @param type - The type to parse the value as. Supported types include:
 *   - 'string', 'json', 'float', 'double', 'integerRange', 'integerArray', 'referenceArray',
 *     'enumArray', 'stringArray', 'decimalRange', 'integer', 'short', 'decimal', 'boolean',
 *     'enum', 'date', 'dateRange', 'datetimeRange', 'time', 'datetime', 'uuid'.
 * @param value - The value to be parsed. Can be of any type.
 * @param propertyName - (Optional) The name of the property being parsed, used for error reporting.
 * @returns The parsed value, converted to the appropriate type, or `null` if the value is `null` or an empty string.
 * @throws Will throw an error if the type is unsupported or if parsing fails for the given type.
 */
export function parseTypeValue(type: string | undefined, value: unknown, propertyName?: string): any {
    if (type === 'string' || type === 'json') return value;
    if (value == null || value === '') return null;

    switch (type) {
        case 'float':
        case 'double':
            return safeParseFloat(value, propertyName);
        case 'integerRange':
            assertString(value);
            return IntegerRange.parse(value);
        case 'integerArray':
        case 'referenceArray': {
            assertString(value);
            const array = stringToArray(value);

            return array.map(n => Number(n));
        }
        case 'enumArray':
        case 'stringArray':
            assertString(value);
            return stringToArray(value);
        case 'decimalRange':
            assertString(value);
            return DecimalRange.parse(value);
        case 'integer':
        case 'short':
            return safeParseInt(value, propertyName);
        case 'decimal':
            return safeParseFloat(value, propertyName);
        case 'boolean':
            return safeParseBoolean(value, propertyName);
        case 'enum':
            return value;
        case 'date':
            assertString(value);
            return DateValue.parse(value);
        case 'dateRange':
            assertString(value);
            return DateRange.parse(value);
        case 'datetimeRange':
            assertString(value);
            return DatetimeRange.parse(value);
        case 'time':
            assertString(value);
            return Time.parse(value);
        case 'datetime':
            assertString(value);
            return Datetime.parse(value);
        case 'uuid':
            assertString(value);
            return Uuid.fromString(value);
        default:
            throw getTypeError(`unsupported type ${value}`, propertyName);
    }
}

/** @internal */
export function parsePropertyValue(property: Property, value: unknown): any {
    let type = property.isReferenceProperty() ? property.columnType : property.type;
    if (
        !type &&
        property.isReferenceProperty() &&
        property.factory.storage === 'external' &&
        property.targetFactory.storage === 'sql'
    )
        type = typeof value === 'string' ? 'string' : 'integer';
    return parseTypeValue(type, value, property.name);
}

/** @internal */
export function formatPropertyValue(property: PropertyDecorator, val: any): string {
    return val ? val.toString() : '';
}

// We cannot use string-hash because CC0 it is not compliant with the permissive license guidance of Sage
// A small benchmark shows that crc32 the fastest alternative to string-hash
// Benchmarking with 100000 iterations...
// FastHash: 6.16 ms
// CRC32: 8.97 ms
// MD5: 57.70 ms
// XXHash: 55.87 ms
/**
 * Fast non crypto hash to use only for non security purposes
 */
export const fastHash = (str: string): number => crc32.str(str) >>> 0;

// Get just the node name from the full name that includes the package name
export function getNameWithoutPackage(fullName: string): string {
    return fullName.replace(/^([^/]*\/)*/, '');
}

/** @internal */
export function cursorChecksum(str: string): string {
    const s = str.replace(/'/g, '"'); // [1,'10.36'] and [1,"10.36"] must have the same cursor checksum
    const hash = fastHash(s.replace(/'/g, '"')) % 100;
    return `#${hash.toString().padStart(2, '0')}`;
}

/** @internal */
export function removeCursorChecksum(value: string): string {
    if (!value) return value;
    const trimmed = value.substring(0, value.length - 3);
    const checksum = cursorChecksum(trimmed);
    // todo XT-859: dataInputError ? But we don't have the context to localize the error.
    if (!value.endsWith(checksum))
        throw new SystemError(`${value}: invalid cursor value (checksum verification failed)`);
    return trimmed;
}

/** @internal */
export function parseCursorValues(orderByProperties: Property[], paramValues: unknown[]): PropertyAndValue[] {
    // todo XT-859: dataInputErrors ? But we don't have the context to localize the error.
    if (orderByProperties.length === 0) throw new SystemError('Cannot parse cursor: missing orderBy');
    if (paramValues.length > orderByProperties.length) throw new SystemError('Cannot parse cursor: too many values');
    if (paramValues.length < orderByProperties.length) throw new SystemError('Cannot parse cursor: too few values');

    return orderByProperties.map((property, i) => ({
        property,
        value: parsePropertyValue(property, paramValues[i]),
    }));
}

/** @disabled_internal */
export function parseCursor(orderByProperties: Property[], value: string): PropertyAndValue[] {
    const withoutChecksum = removeCursorChecksum(value);
    return parseCursorValues(orderByProperties, friendlyJsonParse<unknown[]>(withoutChecksum));
}
