import { asyncArray } from '@sage/xtrem-async-helper';
import { enumNameToStringKey } from '@sage/xtrem-i18n';
import { Dict, ValidationSeverity } from '@sage/xtrem-shared';
import * as fs from 'fs';
import * as _ from 'lodash';
import * as fsp from 'path';
import { Application } from '../application/application';
import { Context } from '../runtime';
import { ValidationContext } from '../ts-api/validation';
import { DataType, DataTypeMetadata, DataTypeOptions, MetaDataOptions } from './data-type';

export interface Enum {
    [id: number]: string;
}

export interface EnumDataTypeOptions extends DataTypeOptions {
    enum: Enum;
    filename: string;
    extends?: EnumDataType;
}

export interface EnumType {
    name: string;
    values: Dict<number>;
}

function upperFirst(str: string): string {
    return str[0].toUpperCase() + str.substring(1);
}

export interface EnumDataTypeMetadata<TitleT> extends DataTypeMetadata<TitleT> {
    values: { value: string; title: string | TitleT }[];
    enumName: string;
}

export class EnumDataType<ValueT extends string | null = string, T = unknown> extends DataType<
    ValueT extends string ? ValueT : never,
    T,
    EnumDataTypeOptions
> {
    private _packageName: string;

    private _values: Dict<number> = {};

    private _extendedMemberInfo: Dict<{ enumFullName: string }> = {};

    constructor(public override options: EnumDataTypeOptions) {
        super('enum', options);
        this._values = _.pickBy(this.enum, _.isNumber);
    }

    get enum(): Enum {
        return this.options.enum;
    }

    get filename(): string {
        return this.options.filename;
    }

    get instanceValues(): Dict<number> {
        return this._values;
    }

    get enumValues(): Dict<number> {
        if (!this.rootDataType) return this._values;
        return this.rootDataType._values;
    }

    public override async controlValue(node: T, cx: ValidationContext, val?: string | string[]): Promise<void> {
        if (Array.isArray(val)) {
            await asyncArray(val).forEach(async v => {
                await this.controlValue(node, cx, v);
            });
            return;
        }

        await super.controlValue(node, cx, val);
        if (val != null && this.enumValues[val] == null) {
            cx.addDiagnose(
                ValidationSeverity.error,
                `${val} is not in the accepted domain of values for enum ${this.enumFullName()}.`,
            );
        }
    }

    numberValue(memberValue: string): number {
        return this.enumValues[memberValue] ?? -1;
    }

    stringValue(keyValue: number): ValueT {
        const memberValue = _.invert(this.enumValues)[keyValue];
        if (!memberValue) throw new Error(`${this.enumName()}: Invalid key value, ${keyValue}`);
        return memberValue as ValueT;
    }

    validateMemberValue(memberValue: string): void {
        if (memberValue && this.enumValues[memberValue] == null) {
            throw new Error(`${this.enumName()}: Invalid member value, ${memberValue}`);
        }
    }

    getLocalizedValue(context: Context, memberValue: string): string {
        this.validateMemberValue(memberValue);
        // If this member is from an enum extension we need to look for the full name stored when registering the extension
        // e
        const enumFullName = this.rootDataType?._extendedMemberInfo?.[memberValue]?.enumFullName ?? this.enumFullName();
        return context.localizeEnumMember(enumFullName, memberValue);
    }

    getLocalizedValues(context: Context): string[] {
        return this.values.map(value => this.getLocalizedValue(context, String(value)));
    }

    enumName(): string {
        const name = upperFirst(_.camelCase(fsp.basename(this.filename).replace(/\.(js|ts)$/, '')));
        return /[A-Z]/.test(name[0]) ? name : `E${name}`;
    }

    get packageName(): string {
        if (this._packageName) return this._packageName;

        const getPackageName = (folder: string): string => {
            const packageFilename = 'package.json';
            let folderToTest = folder;
            // Walk the directory branch until we find the package.json, or we reach the root.
            // eslint-disable-next-line no-constant-condition
            while (true) {
                const path = fsp.join(folderToTest, packageFilename);

                if (fs.existsSync(path)) {
                    return JSON.parse(fs.readFileSync(path, 'utf8')).name;
                }

                if (path === packageFilename) {
                    // We have reached the top folder. No need to go further
                    return '';
                }
                const parentFolder = fsp.join(folderToTest, '..');
                if (parentFolder === folderToTest) return '';
                folderToTest = parentFolder;
            }
        };

        const packageName = getPackageName(fsp.parse(this.filename).dir);

        if (!packageName) {
            throw new Error(`${this.filename}: scoped package not found`);
        }
        this._packageName = packageName;

        return this._packageName;
    }

    enumFullName(): string {
        return `${this.packageName}/${this.enumName()}`;
    }

    get values(): ValueT[] {
        return Object.keys(this.enumValues) as any as ValueT[];
    }

    compareValues(val1: string | number | null, val2: string | number | null): number {
        if (val1 == null) {
            return val2 == null ? 0 : -1;
        }

        if (val1 === val2) return 0;

        const numVal1 = typeof val1 === 'string' ? this.numberValue(val1) : val1;
        const numVal2 = typeof val2 === 'string' ? this.numberValue(val2) : val2 || -1;

        return numVal1 < numVal2 ? -1 : +1;
    }

    getEnumType(): EnumType {
        return {
            values: this.values.reduce((r, k) => {
                const key = String(k);
                r[key] = this.numberValue(key);
                return r;
            }, {} as Dict<number>),
            name: `${_.snakeCase(this.enumName())}_enum`,
        } as EnumType;
    }

    get baseDataType(): EnumDataType | undefined {
        return this.options.extends;
    }

    get rootDataType(): EnumDataType | undefined {
        return this.baseDataType?.rootDataType ?? this.baseDataType;
    }

    /**
     * @internal
     */
    mergeExtension(): void {
        if (!this.rootDataType) return;
        //  base values takes precendence when merging values
        this.rootDataType._values = _.merge(this.rootDataType._values, this._values);
        // Store information on the extended member
        Object.keys(this.instanceValues).forEach(member => {
            if (this.rootDataType) {
                this.rootDataType._extendedMemberInfo[member] = { enumFullName: this.enumFullName() };
            }
        });
    }

    public override getLocalizedTitleKey(): string {
        // Remove the xtrem-core exception after we eliminate inlined data types
        if (!this.name && !process.cwd().includes('xtrem-core')) throw new Error('Data type name is missing');
        if (!this.pack && !process.cwd().includes('xtrem-core')) throw new Error('Data type package name is missing');
        return `${this.pack}/data_types__${_.snakeCase((this.name || '').replace(/[d,D]ata[t,T]ype/, '')).replace(
            /[.-]/g,
            '_',
        )}_enum__name`;
    }

    override getMetaData<TitleT extends string | Dict<string>>(
        options: MetaDataOptions<TitleT, Application, Context>,
    ): EnumDataTypeMetadata<TitleT> {
        const commonDataTypeMetaData = super.getMetaData<TitleT>(options);
        const enumName = this.enumName();
        const values = this.values.map(elt => {
            if (elt == null) return { value: '', title: '' };
            if (options.context) {
                return {
                    value: elt,
                    title: this.getLocalizedValue(options.context, elt),
                };
            }
            return {
                value: elt,
                title: options.localize?.(enumNameToStringKey(enumName, elt), elt) ?? elt,
            };
        });

        return {
            ...commonDataTypeMetaData,
            type: 'enum',
            isDefault: false,
            values,
            enumName,
        };
    }
}
