import { TypeName } from '..';

export const arrayTypes: (TypeName | '')[] = ['integerArray', 'enumArray', 'referenceArray', 'stringArray'];

export function stringToArray(value: string): string[] {
    return value
        .replace(/(\[|]|"|')/g, '') // remove open array bracket [, array bracket ], single quotes ' and double quotes "
        .split(',')
        .map(v => v.trim()); // remove spaces
}

export type ArrayType<This, T> = This extends (infer ElementT)[] | null
    ? ElementT extends T
        ? ElementT
        : never
    : never;
