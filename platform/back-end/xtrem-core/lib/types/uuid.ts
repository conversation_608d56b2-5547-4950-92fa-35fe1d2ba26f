/** @packageDocumentation @module types */
import { uuid } from '@sage/xtrem-date-time';

export class Uuid {
    // TODO: optimize with <PERSON><PERSON><PERSON>
    /** @internal */
    private constructor(private readonly data: string) {
        if (data.length !== 32) throw new Error(`invalid uuid: ${data}`);
    }

    toHexString(): string {
        return this.data;
    }

    toString(sep = '-'): string {
        const d = this.data;
        return [d.substring(0, 8), d.substring(8, 12), d.substring(12, 16), d.substring(16, 20), d.substring(20)].join(
            sep,
        );
    }

    static isZero(val: Buffer): boolean {
        if (val.length !== 16) throw new Error(`invalid uuid buffer length: ${val.length}`);
        for (let i = 0; i < 16; i += 1) {
            if (val[i] !== 0) return false;
        }
        return true;
    }

    static generate(): Uuid {
        return new Uuid(uuid.generate(''));
    }

    static fromBytes(bytes: Buffer): Uuid {
        return new Uuid(bytes.toString('hex'));
    }

    static fromString(str: string): Uuid {
        if (str.length === 32) return new Uuid(str.toLowerCase());
        // node uses lowercase for hex format
        if (str.length === 36 && str[8] === '-') return Uuid.fromString(str.replace(/-/g, ''));
        throw new Error(`invalid UUID string: ${str}`);
    }

    static zero = new Uuid('0'.repeat(32));
}
