import { Dict } from '@sage/xtrem-shared';
import {
    CollectionPropertyDecorator,
    ColumnTypeName,
    NodeDecorator,
    NodeExtensionDecorator,
    PlainOperationDecorator,
    ReferencePropertyDecorator,
    TypeName,
} from '../decorators';
import { DataType } from '../types';
import { Node } from './node';

export interface ExternalPropertyDecorator extends TypedPropertyDescriptor<Node> {
    name: string;
    type: TypeName;
    isNullable?: boolean;
    isMandatory?: boolean;
    isPublished?: boolean;
    isStored?: boolean;
    isSystemProperty?: boolean;
    columnName?: string;
    columnType?: ColumnTypeName;
    dataType?: DataType<any, any>;
}

export type ExternalReferencePropertyDecorator = ExternalPropertyDecorator & ReferencePropertyDecorator<Node, any>;

export type ExternalCollectionPropertyDecorator = ExternalPropertyDecorator & CollectionPropertyDecorator;

export type ExternalOperationDecorator = PlainOperationDecorator;

export interface ExternalNodeDecorator extends NodeDecorator {
    name: string;
    super?: string;
    isVitalReferenceChild?: boolean;
    isVitalCollectionChild?: boolean;
    isVitalChild?: boolean;
    properties: ExternalPropertyDecorator[];
    queries: ExternalOperationDecorator[];
    mutations: ExternalOperationDecorator[];
}

export interface ExternalNodeExtensionDecorator extends NodeExtensionDecorator<Node> {
    name: string;
    properties: ExternalPropertyDecorator[];
    queries: ExternalOperationDecorator[];
    mutations: ExternalOperationDecorator[];
}

export interface ExternalEnumDecorator {
    name: string;
    values: Dict<number>;
}
