import { UnPromised } from '@sage/xtrem-async-helper';
import { DateValue } from '@sage/xtrem-date-time';
import { kebabCase } from 'lodash';
import * as fsp from 'path';
import { EnumDataType } from '../types';
import { Node } from './node';
import { NodeQueryFilter } from './query-filter';
import { NodeQueryOptions } from './query-options';

export enum ValuesOperatorEnum {
    min,
    max,
    avg,
    sum,
    distinctCount,
    hasNull,
}
export type ValuesOperator = keyof typeof ValuesOperatorEnum;

export enum GroupBySelectorEnum {
    value,
    month,
    year,
    day,
}
export type GroupBySelector = keyof typeof GroupBySelectorEnum;

export const GroupBySelectorDataType = new EnumDataType<GroupBySelector>({
    enum: GroupBySelectorEnum,
    filename: fsp.join(__dirname, kebabCase('GroupBySelector')),
});

export interface AggregateGroup {
    path: string[];
    groupedBy?: GroupBySelector;
}

// TODO
export type AggregateGroupResult<T> = T;

export interface AggregateValue {
    path: string[];
    operator?: ValuesOperator;
    resolve?: (val: any) => any;
}

export interface Aggregate {
    groups: AggregateGroup[];
    values: AggregateValue[];
}

export type AggregateGroupItem<V> = V extends Node
    ? AggregateGroups<V>
    : { _by: V extends DateValue ? GroupBySelector : 'value' };

export type AggregateGroups<T extends Node> = {
    [K in keyof T]?: AggregateGroupItem<UnPromised<T[K]>>;
};

export type AggregateValueItem<V> = V extends Node ? AggregateValues<V> : { [K in ValuesOperator]?: boolean };

export type AggregateValues<T extends Node> = {
    [K in keyof T]?: AggregateValueItem<UnPromised<T[K]>>;
};

export interface QueryAggregateOptions<
    T extends Node,
    GroupsT extends AggregateGroups<T>,
    ValuesT extends AggregateValues<T>,
> extends NodeQueryOptions<T> {
    group: GroupsT;
    values: ValuesT;
}

export interface ReadAggregateOptions<T extends Node, ValuesT extends AggregateValues<T>> {
    filter?: NodeQueryFilter<T>;
    locale?: string;
    values: ValuesT;
}

export type AggregateGroupReturn<T extends Node, GroupT> = {
    [K in keyof GroupT]-?: K extends keyof T
        ? UnPromised<T[K]> extends infer U
            ? U extends Node
                ? AggregateGroupReturn<U, GroupT[K]>
                : U
            : never
        : never;
};

export type AggregateValuesReturn<T extends Node, ValuesT> = {
    [K in keyof ValuesT]-?: K extends keyof T
        ? UnPromised<T[K]> extends infer U
            ? U extends Node
                ? AggregateValuesReturn<U, ValuesT[K]>
                : { [KK in keyof ValuesT[K]]: U }
            : never
        : never;
};

export type QueryAggregateReturn<T extends Node, GroupT, ValuesT> = {
    group: AggregateGroupReturn<T, GroupT>;
    values: AggregateValuesReturn<T, ValuesT>;
};

export type ReadAggregateReturn<T extends Node, ValuesT> = AggregateValuesReturn<T, ValuesT>;
