/** @packageDocumentation @module runtime */
import { Node } from './node';

export type DummmyKeys = '_dummy' | '_dummy1' | '_dummy2' | '_dummy3' | '_dummy4' | '_dummy5';

export type ExtensionMembers<T> = Omit<T, DummmyKeys>;

export abstract class NodeExtensionBase extends Node {
    constructor() {
        super();
        throw new Error('Cannot instantiate extension class');
    }
}

/**
 * Base class for node extensions.
 */
export abstract class NodeExtension<Base extends Node> extends NodeExtensionBase {
    // _dummy property to get nominal typing - see https://michalzalecki.com/nominal-typing-in-typescript/
    protected _dummy: Base;

    // static property to verify depth (see below)
    protected static _extensionDepth = 0;
}

// NodeExtension does not work on subclasses.
// If we try to extends a base node and one of its subnodes we get a type conflict on _dummy.
// This is fixed by introducing variants of NodeExtension, one per depth in the hierarchy.
// Note the name change on the dummy variable: _dummy1, _dummy2, ...
// The static extensionDepth allows us to verify that extensions are subclassed from the right variant
// when the factories are initialized.
/**
 * Base class for extensions of sub-node classes at the first level of a subclass hierarchy.
 * For example, extending Mammal in an Animal -> Mammal hierachy
 */
export abstract class SubNodeExtension1<Base extends Node> extends NodeExtensionBase {
    protected _dummy1: Base;

    protected static _extensionDepth = 1;
}

/**
 * Base class for extensions of sub-node classes at the second level of a subclass hierarchy.
 * For example, extending Dog in an Animal -> Mammal -> Dog hierachy
 */
export abstract class SubNodeExtension2<Base extends Node> extends NodeExtensionBase {
    protected _dummy2: Base;

    protected static _extensionDepth = 2;
}

/**
 * Base class for extensions of sub-node classes at the third level of a subclass hierarchy.
 * For example, extending CompanionDog in an Animal -> Mammal -> Dog -> CompanionDog hierachy
 */
export abstract class SubNodeExtension3<Base extends Node> extends NodeExtensionBase {
    protected _dummy3: Base;

    protected static _extensionDepth = 3;
}

/**
 * Base class for extensions of sub-node classes at the fourth level of a subclass hierarchy.
 * For example, extending Spaniel in an Animal -> Mammal -> Dog -> CompanionDog -> Spaniel hierachy
 */
export abstract class SubNodeExtension4<Base extends Node> extends NodeExtensionBase {
    protected _dummy4: Base;

    protected static _extensionDepth = 4;
}

/**
 * Base class for extensions of sub-node classes at the fith level of a subclass hierarchy.
 * For example, extending CavalierKingCharles in an Animal -> Mammal -> Dog -> CompanionDog -> Spaniel -> CavalierKingCharles hierachy
 */
export abstract class SubNodeExtension5<Base extends Node> extends NodeExtensionBase {
    protected _dummy5: Base;

    protected static _extensionDepth = 5;
}

/**
 * Maximum depth of subclassing currently allowed.
 */
export const maxNodeSubclassingDepth = 5;

export type Extend<This extends Node | null> =
    This extends NodeExtension<infer Base>
        ? ExtensionMembers<This & Base> & This
        : This extends SubNodeExtension1<infer Base>
          ? ExtensionMembers<This & Base> & This
          : This extends SubNodeExtension2<infer Base>
            ? ExtensionMembers<This & Base> & This
            : This extends SubNodeExtension3<infer Base>
              ? ExtensionMembers<This & Base> & This
              : This extends SubNodeExtension4<infer Base>
                ? ExtensionMembers<This & Base> & This
                : This extends SubNodeExtension5<infer Base>
                  ? ExtensionMembers<This & Base> & This
                  : This;
