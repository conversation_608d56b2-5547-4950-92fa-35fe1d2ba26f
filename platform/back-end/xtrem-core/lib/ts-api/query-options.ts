import { BaseCollection } from '../collections';
import { Node } from './node';
import { Aggregate } from './query-aggregate';
import { NodeQueryFilter } from './query-filter';
import { OrderBy } from './query-order-by';

export interface NodeQueryOptions<T extends Node = Node> {
    forUpdate?: boolean;
    skipLocked?: boolean;
    filter?: NodeQueryFilter<T>;
    orderBy?: OrderBy<T>;
    first?: number;
    after?: string;
    last?: number;
    before?: string;
    aggregate?: Aggregate;
    locale?: string;
    count?: boolean;
    /** @internal - set by context.queryWithReader to prevent caching */
    doNotCache?: boolean;
    /** @internal - The query expects a single record in the result */
    singleResultRequest?: boolean;
    collection?: BaseCollection;
}
