import { Dict } from '@sage/xtrem-shared';
import { StaticThis } from '../decorators';
import { Activity, BaseActivityDefinition, OperationGrant, PermissionGrant } from './activity';
import { Node } from './node';

export interface ActivityExtensionDefinition extends BaseActivityDefinition {
    extends: Activity;
    __filename: string;
}

export class ActivityExtension {
    constructor(private readonly definition: ActivityExtensionDefinition) {}

    get extends(): Activity {
        return this.definition.extends;
    }

    get description(): string {
        return this.definition.extends.description;
    }

    get node(): StaticThis<Node> {
        return this.definition.extends.node;
    }

    get permissions(): string[] {
        return this.definition.permissions;
    }

    get operationGrants(): Dict<OperationGrant[]> | undefined {
        return this.definition.operationGrants;
    }

    get permissionGrants(): Dict<PermissionGrant[]> | undefined {
        return this.definition.permissionGrants;
    }

    get filename(): string {
        return this.definition.__filename;
    }
}
