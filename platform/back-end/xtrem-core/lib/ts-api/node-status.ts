/** Node status, as returned by `node.$.status` */
export enum NodeStatus {
    /** node has been read from database and not changed since */
    unchanged = 'unchanged',
    /** node is a new node which has not been persisted yet. */
    added = 'added',
    /** node has been read from database and then modified */
    modified = 'modified',
    /** node has been deleted */
    deleted = 'deleted',
    /** node pointer was set in a transaction that does not exist any more - do not use it */
    invalid = 'invalid',
}
