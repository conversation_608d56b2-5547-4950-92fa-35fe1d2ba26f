import { UnPromised } from '@sage/xtrem-async-helper';
import { integer } from '@sage/xtrem-shared';
import { Collection } from './collection';
import { Node } from './node';

export enum UpdateActionEnum {
    create = 1,
    update = 2,
    delete = 3,
}

export type UpdateAction = keyof typeof UpdateActionEnum;

export type NodeId = integer | string;
export type NullableNodeId = NodeId | null;

export type PropertyCreateData<V> =
    V extends Promise<infer U>
        ? PropertyCreateData<U>
        : V extends Node
          ? NodeCreateData<V> | V | NullableNodeId
          : V extends Collection<infer U>
            ? NodeCreateData<U & { _sortValue?: integer; _action: UpdateAction; _constructor?: string }>[]
            : V extends any[]
              ? V[number] extends Node
                  ? PropertyCreateData<V[number]>[]
                  : V
              : UnPromised<V>;

export type NodeCreateData<T extends Node> = { [K in Exclude<keyof T, '$'>]?: PropertyCreateData<T[K]> };
// Same type but different name in case we need to distinguish them later
export type NodeUpdateData<T extends Node> = NodeCreateData<T> & { _action?: UpdateAction };

export type PropertyPayloadData<V> =
    V extends Promise<infer U>
        ? PropertyPayloadData<U>
        : V extends Node
          ? NodePayloadData<V>
          : V extends Collection<infer U>
            ? NodePayloadData<U & { _sortValue?: integer; _action: UpdateAction }>[]
            : V extends any[]
              ? V[number] extends Node
                  ? PropertyPayloadData<V[number]>[]
                  : V
              : UnPromised<V extends number ? number : V>;

// Typing may need some fine tuning - see later
// Keep all properties optional for now as we cannot differentiate between vital and non vital
export type NodePayloadData<T extends Node> = { [K in Exclude<keyof T, '$'>]?: PropertyPayloadData<T[K]> };
