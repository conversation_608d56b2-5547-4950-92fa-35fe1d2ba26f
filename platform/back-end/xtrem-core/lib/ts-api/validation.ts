/** @packageDocumentation @module runtime */
import { AsyncResponse } from '@sage/xtrem-async-helper';
import { DateValue } from '@sage/xtrem-date-time';
import * as decimalLib from '@sage/xtrem-decimal';
import { ValidationSeverity } from '@sage/xtrem-shared';
import { BusinessRuleError, Context } from '..';
import { Node } from './node';

interface ForSeverity {
    if: <T>(arg: T) => { is: ContextValidators<T> };
    add: (message: string) => void;
    addLocalized: (key: string, template: string, data?: object | any[]) => void;
    withMessage: (key: string, template: string, data?: () => AsyncResponse<object | any[]>) => ForSeverity;
}

interface Expect<T> {
    to: { be: ContextValidators<T> };
    withMessage: (key: string, template: string, data?: () => AsyncResponse<object | any[]>) => Expect<T>;
}

interface LocalizedMessage {
    key: string;
    template: string;
    data?: () => AsyncResponse<object | any[]>;
}

interface ValidatorsEqual<T> {
    to: (v2: T) => AsyncResponse<boolean>;
}

interface ValidatorsComparison<T> {
    than: (v2: T) => AsyncResponse<boolean>;
}

interface ValidatorsAt<T> {
    least: (v2: T) => AsyncResponse<boolean>;
    most: (v2: T) => AsyncResponse<boolean>;
}

function fmt(val: any): any {
    return typeof val === 'string' ? `'${val}'` : val;
}

export class ContextValidators<T> {
    /** @internal */
    constructor(
        private readonly messageContext: {
            context: ValidationContext;
            severity: ValidationSeverity;
            localizedMessage?: LocalizedMessage;
        },
        private readonly arg: T,
        private readonly negated = false,
    ) {}

    private get context(): ValidationContext {
        return this.messageContext.context;
    }

    private get severity(): ValidationSeverity {
        return this.messageContext.severity;
    }

    get not(): ContextValidators<T> {
        return new ContextValidators<T>(this.messageContext, this.arg, !this.negated);
    }

    /** @internal */
    private async getLocalized(key: string, message: string, data?: { v2: any }): Promise<string> {
        const localizedMessage = this.messageContext.localizedMessage;
        const context = this.messageContext.context;
        if (localizedMessage) {
            return context.localize(localizedMessage.key, localizedMessage.template, await localizedMessage.data?.());
        }

        let localized: string = '';
        const v2 = data ? fmt(data.v2) : '';
        if (this.negated) {
            localized = context.localize(key, message, { v2 });
        } else {
            localized = context.localize(key.replace('-not-', '-'), message.replace(' not ', ' '), { v2 });
        }
        return localized.replace(/&quot;/g, '"').replace(/&#x27;/g, "'");
    }

    // note result is the opposite of the predicate, because the returned value is true if the diagnose was emitted
    // (and thus if the predicate is false).
    async empty(this: ContextValidators<string | null>): Promise<boolean> {
        if ((this.arg == null || this.arg.length === 0) === this.negated) {
            this.context.addDiagnose(
                this.severity,
                await this.getLocalized('@sage/xtrem-core/value-must-not-be-empty', 'value must not be empty'),
            );
            return true;
        }
        return false;
    }

    // comparisons
    get equal(): ValidatorsEqual<T> {
        return {
            to: async (v2: T) => {
                if (decimalLib.eq(this.arg, v2) === this.negated) {
                    this.context.addDiagnose(
                        this.severity,
                        await this.getLocalized(
                            '@sage/xtrem-core/value-must-not-be-equal',
                            'value must not be equal to {{v2}}',
                            { v2 },
                        ),
                    );
                    return true;
                }
                return false;
            },
        };
    }

    get greater(): ValidatorsComparison<T> {
        return {
            than: async (v2: T) => {
                if (decimalLib.gt(this.arg, v2) === this.negated) {
                    this.context.addDiagnose(
                        this.severity,
                        await this.getLocalized(
                            '@sage/xtrem-core/value-must-not-be-greater-than',
                            'value must not be greater than {{v2}}',
                            { v2 },
                        ),
                    );
                    return true;
                }
                return false;
            },
        };
    }

    get less(): ValidatorsComparison<T> {
        return {
            than: async (v2: T) => {
                if (decimalLib.lt(this.arg, v2) === this.negated) {
                    this.context.addDiagnose(
                        this.severity,
                        await this.getLocalized(
                            '@sage/xtrem-core/value-must-not-be-less-than',
                            'value must not be less than {{v2}}',
                            { v2 },
                        ),
                    );
                    return true;
                }
                return false;
            },
        };
    }

    get at(): ValidatorsAt<T> {
        return {
            least: async (v2: T) => {
                if (decimalLib.gte(this.arg, v2) === this.negated) {
                    this.context.addDiagnose(
                        this.severity,
                        await this.getLocalized(
                            '@sage/xtrem-core/value-must-not-be-at-least',
                            'value must not be at least {{v2}}',
                            { v2 },
                        ),
                    );
                    return true;
                }
                return false;
            },
            most: async (v2: T) => {
                if (decimalLib.lte(this.arg, v2) === this.negated) {
                    this.context.addDiagnose(
                        this.severity,
                        await this.getLocalized(
                            '@sage/xtrem-core/value-must-not-be-at-most',
                            'value must not be at most {{v2}}',
                            { v2 },
                        ),
                    );
                    return true;
                }
                return false;
            },
        };
    }

    async in(this: ContextValidators<T>, set: T[]): Promise<boolean> {
        if (set.some(elt => decimalLib.eq(elt, this.arg)) === this.negated) {
            this.context.addDiagnose(
                this.severity,
                await this.getLocalized(
                    '@sage/xtrem-core/value-must-not-be-part-of-set',
                    'value must not be part of set [{{v2}}]',
                    { v2: set },
                ),
            );
            return true;
        }
        return false;
    }

    // boolean validators
    async true(this: ContextValidators<boolean>): Promise<boolean> {
        if (this.arg === this.negated) {
            this.context.addDiagnose(
                this.severity,
                await this.getLocalized('@sage/xtrem-core/value-must-not-be-true', 'value must not be true'),
            );
            return true;
        }
        return false;
    }

    async false(this: ContextValidators<boolean>): Promise<boolean> {
        if (!this.arg === this.negated) {
            // hack to obtain 'value must be false' instead of silly 'true must be false'.
            this.context.addDiagnose(
                this.severity,
                await this.getLocalized('@sage/xtrem-core/value-must-not-be-false', 'value must not be false'),
            );
            return true;
        }
        return false;
    }

    // string validators
    async matching(this: ContextValidators<string | null>, re: RegExp): Promise<boolean> {
        if ((this.arg != null && re.test(this.arg)) === this.negated) {
            this.context.addDiagnose(
                this.severity,
                await this.getLocalized('@sage/xtrem-core/value-must-not-match', 'value must not match {{v2}}', {
                    v2: re,
                }),
            );
            return true;
        }
        return false;
    }

    // number validators
    async zero(this: ContextValidators<number | null>): Promise<boolean> {
        if (decimalLib.eq(this.arg, 0) === this.negated) {
            this.context.addDiagnose(
                this.severity,
                await this.getLocalized('@sage/xtrem-core/value-must-not-be-zero', 'value must not be zero'),
            );
            return true;
        }
        return false;
    }

    async positive(this: ContextValidators<number | null>): Promise<boolean> {
        if (decimalLib.gt(this.arg, 0) === this.negated) {
            this.context.addDiagnose(
                this.severity,
                await this.getLocalized('@sage/xtrem-core/value-must-not-be-positive', 'value must not be positive'),
            );
            return true;
        }
        return false;
    }

    async negative(this: ContextValidators<number | null>): Promise<boolean> {
        if (decimalLib.lt(this.arg, 0) === this.negated) {
            this.context.addDiagnose(
                this.severity,
                await this.getLocalized('@sage/xtrem-core/value-must-not-be-negative', 'value must not be negative'),
            );
            return true;
        }
        return false;
    }

    // date validators
    async before(this: ContextValidators<DateValue | null>, v2: DateValue): Promise<boolean> {
        if ((this.arg == null || this.arg.compare(v2) < 0) === this.negated) {
            this.context.addDiagnose(
                this.severity,
                await this.getLocalized(
                    '@sage/xtrem-core/value-must-not-be-before',
                    'value must not be before {{v2}}',
                    {
                        v2,
                    },
                ),
            );
            return true;
        }
        return false;
    }

    async after(this: ContextValidators<DateValue | null>, v2: DateValue): Promise<boolean> {
        if ((this.arg != null && this.arg.compare(v2) > 0) === this.negated) {
            this.context.addDiagnose(
                this.severity,
                await this.getLocalized('@sage/xtrem-core/value-must-not-be-after', 'value must not be after {{v2}}', {
                    v2,
                }),
            );
            return true;
        }
        return false;
    }
}

export class ValidationContext {
    constructor(
        public context: Context,
        public path: string[] = [],
    ) {}

    localize(key: string, template: string, data: object | any[] = {}): string {
        return this.context.localize(key, template, data);
    }

    addDiagnose(severity: ValidationSeverity, message: string): void {
        if (severity === ValidationSeverity.exception) throw new BusinessRuleError(message, undefined, this.path);
        this.context.addDiagnoseAtPath(severity, this.path, message);
    }

    addError(message: string): void {
        this.addDiagnose(ValidationSeverity.error, message);
    }

    addWarning(message: string): void {
        this.addDiagnose(ValidationSeverity.warn, message);
    }

    addInfo(message: string): void {
        this.addDiagnose(ValidationSeverity.info, message);
    }

    get severity(): ValidationSeverity {
        // severity is tracked globally in node context.
        return this.context.severity;
    }

    at(...keys: string[]): ValidationContext {
        return new ValidationContext(this.context, keys);
    }

    /** @internal */
    private forSeverity(severity: ValidationSeverity, localizedMessage?: LocalizedMessage): ForSeverity {
        return {
            if: <T>(arg: T) => {
                return {
                    is: new ContextValidators<T>({ context: this, severity, localizedMessage }, arg, true),
                };
            },
            add: (message: string) => this.addDiagnose(severity, message),
            addLocalized: (key: string, template: string, data: object | any[] = {}) => {
                const message = this.context.localize(key, template, data);
                this.addDiagnose(severity, message);
            },
            withMessage: (key: string, template: string, data?: () => AsyncResponse<object | any[]>): ForSeverity => {
                return this.forSeverity(severity, { key, template, data });
            },
        };
    }

    get info(): ForSeverity {
        return this.forSeverity(ValidationSeverity.info);
    }

    get warn(): ForSeverity {
        return this.forSeverity(ValidationSeverity.warn);
    }

    get error(): ForSeverity {
        return this.forSeverity(ValidationSeverity.error);
    }

    get throw(): ForSeverity {
        return this.forSeverity(ValidationSeverity.exception);
    }

    forExpect<T>(arg: T, localizedMessage?: LocalizedMessage): Expect<T> {
        return {
            to: {
                be: new ContextValidators<T>(
                    { context: this, severity: ValidationSeverity.error, localizedMessage },
                    arg,
                    false,
                ),
            },
            withMessage: (key: string, template: string, data?: () => AsyncResponse<object | any[]>): Expect<T> => {
                return this.forExpect(arg, { key, template, data });
            },
        };
    }

    expect<T>(arg: T): Expect<T> {
        return this.forExpect(arg);
    }
}

export type Validator<This extends Node, ValT = void> = (
    this: This,
    cx: ValidationContext,
    val: ValT,
) => AsyncResponse<ValT extends never ? never : void>;
