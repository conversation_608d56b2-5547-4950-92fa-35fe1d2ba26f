import { AnyRecord, UnPromised } from '@sage/xtrem-async-helper';
import { Node } from './node';

export type OrderBy<T extends Node | null> = {
    [K in keyof T]?: K extends '$'
        ? never
        : T[K] extends infer U
          ? UnPromised<U> extends Node | null
              ? OrderBy<UnPromised<U>> | 1 | -1
              : 1 | -1
          : 1 | -1;
} & { _sortValue?: 1 | -1 }; // TODO: _sortValue should only be on collection vital child

export type FlatOrderBy<T extends Node | null> = {
    [K in Exclude<keyof T, '$'> | '_sortValue' | '_constructor']?: 1 | -1;
}; // TODO: _sortValue should only be on collection vital child

/** @internal */
export type AnyOrderBy = AnyRecord;
