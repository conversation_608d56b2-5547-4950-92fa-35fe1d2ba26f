/** @packageDocumentation @module runtime */
import { AnyValue, As<PERSON><PERSON>rray, <PERSON><PERSON><PERSON><PERSON><PERSON>, AsyncResponse } from '@sage/xtrem-async-helper';
import { NodeCreateData } from './create-data';
import { Node } from './node';
import { NodeQueryFilter } from './query-filter';
import { NodeQueryOptions } from './query-options';

export interface SqlWhereInterface<NodeT extends Node> {
    sum: (callback: (node: NodeT) => AsyncResponse<number>) => AsyncResponse<number>;
}

export interface Collection<T extends Node> extends AsyncArray<T> {
    joinValues: AsyncResponse<NodeQueryFilter<T>>;

    name: string;

    withReader<R extends AnyValue>(body: (reader: AsyncReader<T>) => R): Promise<R>;

    fullName: string;

    reset(): Promise<void>;

    fill(array: NodeCreateData<T>[]): Promise<void>;

    insert(i: number, data: NodeCreateData<T>): Promise<void>;

    append(data: NodeCreateData<T>): Promise<void>;

    delete(start: number, deleteCount?: number): Promise<void>;

    query(options: NodeQueryOptions<T>): AsyncArray<T>;

    where(condition?: (node: T) => AsyncResponse<boolean>): SqlWhereInterface<T>;

    takeOne(condition: (node: T) => AsyncResponse<boolean>): AsyncResponse<T | null>;
}
