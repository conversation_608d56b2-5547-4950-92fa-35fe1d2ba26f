/** @packageDocumentation @module runtime */
import { AnyR<PERSON>ord, AnyValue } from '@sage/xtrem-async-helper';
import { Decimal } from '@sage/xtrem-decimal';
import { Dict, integer } from '@sage/xtrem-shared';
import { datetime } from '.';
import { InternalAttachmentAssociationData } from '../application/attachment-manager';
import { InternalNoteAssociationData } from '../application/note-manager';
import { Decorated } from '../decorators';
import { decoratorsSymbol } from '../decorators/decorator-utils';
import { NodeState } from '../node-state';
import { FactoryDecorators } from '../runtime/node-factory';
import { SyncInfo } from '../synchronization/sync-info';
import { Collection } from './collection';
import { Node$ } from './node-$';

/**
 * The root `Node` class
 *
 * All  node classes will derive from this class.
 */
export class Node extends Decorated {
    /** @internal */
    protected readonly debugId: number = 0;

    /** @internal */
    private static nextDebugId = 0;

    /** @internal */
    static override [decoratorsSymbol]: FactoryDecorators;

    /** @internal */
    constructor(state?: NodeState) {
        super();
        this.debugId += Node.nextDebugId;
        Node.nextDebugId += 1;
        if (state) {
            this.#$ = new Node$(state);
        }
    }

    /**
     * Private property for node.$
     *
     * The combination of `#$` private property and `get $()` accessor fixes a problem mocha/chai.
     * It prevents chai from traversing huge node.$ object graphs when generating diffs for assert.equals failures.
     */
    readonly #$: Node$<this>;

    /**
     * Reference to an object that carries system methods and properties related to the node.
     *
     * For example, you can retrieve the node's status with `node.$.status`, or its context with
     * `node.$.context`.
     * You also use this special reference to save a modified node to the database with `node.$.save()`.
     */
    get $(): Node$<this> {
        return this.#$;
    }

    /**
     * The internal id of the node.
     * This id is unique within the node's factory.
     * Node : id are negative for newly created nodes that were not saved yet.
     */
    get _id(): number {
        return this.$.id;
    }

    /**
     * The node's source ID, an ID that relates the record to the source system.
     */
    get _sourceId(): Promise<string> {
        return this.$.sourceId;
    }

    /**
     * The node's sort value, only valid on collection child nodes.
     */
    get _sortValue(): Promise<integer> {
        return this.$.sortValue;
    }

    /**
     * The node's sort value, only valid on collection child nodes.
     */
    get _vendor(): Promise<Node | null> {
        return this.$.vendor;
    }

    /**
     * The node's etag value.
     */
    get _etag(): Promise<string> {
        return this.$.etag;
    }

    /**
     * The node's creation timestamp.
     */
    get _createStamp(): Promise<datetime> {
        return this.$.createStamp;
    }

    /**
     * The node's update timestamp
     */
    get _updateStamp(): Promise<datetime> {
        return this.$.updateStamp;
    }

    /**
     * The node's synchronization tick
     */
    get _syncTick(): Promise<Decimal> {
        return this.$.syncTick;
    }

    /**
     * The node's synchronization tick
     */
    get _syncInfo(): Promise<SyncInfo> {
        return this.$.syncInfo;
    }

    /**
     * The node's custom data value.
     */
    get _customData(): Promise<object> {
        return this.$.customData;
    }

    /**
     * The node's values hash, only valid on content addressable nodes.
     */
    get _valuesHash(): Promise<string> {
        return this.$.valuesHash;
    }

    /**
     * attachments collection.
     */
    get _attachments(): Collection<InternalAttachmentAssociationData & Node> {
        return this.$.attachments;
    }

    /**
     * notes collection.
     */
    get _notes(): Collection<InternalNoteAssociationData & Node> {
        return this.$.notes;
    }

    // class level system API
    protected toJSON(): AnyRecord {
        return this.$.state.values;
    }
}

// hide this override so that we don't have to filter out this key in mapped types
Object.defineProperty(Node.prototype, 'toString', {
    value(this: Node): string {
        return `${this._id}`;
    },
});

export type AnyNode = Node & Dict<Promise<AnyValue>>;

export type NodeFromInterface<I extends AnyRecord> = Node & {
    [K in keyof I]: I[K] extends Array<infer EltT>
        ? EltT extends AnyRecord
            ? Collection<NodeFromInterface<EltT>>
            : Promise<I[K]>
        : Promise<I[K] extends AnyRecord ? NodeFromInterface<I[K]> : I[K]>;
};
