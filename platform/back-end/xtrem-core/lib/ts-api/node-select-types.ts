import { Collection } from './collection';
import { Node } from './node';
import { NodeQueryFilter } from './query-filter';
import { OrderBy } from './query-order-by';

export type NodeSelector<NodeT extends Node | null> =
    | boolean
    | {
          [K in keyof NodeT]?: NodeT[K] extends Collection<infer ItemT>
              ? NodeSelector<ItemT>
              : NodeT[K] extends Promise<infer ValT>
                ? ValT extends Node | null
                    ? NodeSelector<ValT>
                    : boolean
                : K extends '_id'
                  ? boolean
                  : never;
      }
    | { [K in string]: (this: NodeT, ...args: any[]) => any };

export interface NodeSelectOptions<NodeT extends Node> {
    filter: NodeQueryFilter<NodeT>;
    orderBy?: OrderBy<NodeT>;
    first?: number;
    after?: string;
    last?: number;
    before?: string;
    locale?: string;
    forUpdate?: boolean;
    returnReferencesAsNaturalKey?: boolean;
}

export type NodeSelectResult<
    NodeT extends Node | null,
    SelectorT extends NodeSelector<NodeT>,
> = SelectorT extends boolean
    ? number | string
    : {
          [K in keyof SelectorT]: K extends keyof NodeT
              ? NodeT[K] extends Collection<infer ItemT extends Node> // The property is a collection
                  ? SelectorT[K] extends NodeSelector<ItemT>
                      ? NodeSelectResult<ItemT, SelectorT[K]>[]
                      : never
                  : NodeT[K] extends Promise<infer RefT> // The property is a reference
                    ? RefT extends null
                        ? RefT extends Node
                            ? SelectorT[K] extends NodeSelector<RefT>
                                ? NodeSelectResult<RefT, SelectorT[K]> | null
                                : SelectorT[K] extends boolean
                                  ? string | number | null
                                  : never
                            : RefT | null
                        : RefT extends Node
                          ? SelectorT[K] extends NodeSelector<RefT>
                              ? NodeSelectResult<RefT, SelectorT[K]>
                              : SelectorT[K] extends boolean
                                ? string | number
                                : never
                          : RefT
                    : K extends '_id' // _id is a special as it is not a scalar that return a Promise, we just use the Node _id type here
                      ? NodeT[K]
                      : never
              : SelectorT[K] extends (...args: any[]) => Promise<infer R>
                ? R
                : never;
      };
