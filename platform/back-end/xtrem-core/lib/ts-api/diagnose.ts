/** @packageDocumentation @module runtime */

import { ValidationSeverity } from '@sage/xtrem-shared';

export class Diagnose {
    constructor(
        public severity: ValidationSeverity,
        public path: string[],
        public message: string,
    ) {}

    toString(): string {
        return `[${this.path.join('.')}]: ${ValidationSeverity[this.severity]}: ${this.message}`;
    }

    static areEqual(d1: Diagnose, d2: Diagnose): boolean {
        return d1.severity === d2.severity && d1.message === d2.message && d1.path.join() === d2.path.join();
    }
}
