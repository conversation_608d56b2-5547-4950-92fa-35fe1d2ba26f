import { AnyRecord, AnyValue } from '@sage/xtrem-async-helper';

export type JsonType<T extends AnyValue> = T extends boolean | null
    ? T
    : T extends number | null
      ? T
      : T extends Array<infer ElementT>
        ? ElementT extends AnyValue
            ? Array<JsonType<ElementT>>
            : never
        : T extends AnyRecord
          ? { [K in keyof T]: T[K] extends AnyValue ? JsonType<T[K]> : never }
          : string;
