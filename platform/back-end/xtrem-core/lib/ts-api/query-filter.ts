import { AnyValue, AsyncResponse, UnPromised } from '@sage/xtrem-async-helper';
import { DateRange, DateValue, Datetime, DatetimeRange, DecimalRange, IntegerRange } from '@sage/xtrem-date-time';
import { Decimal } from '@sage/xtrem-decimal';
import { Dict, integer } from '@sage/xtrem-shared';
import { SyncInfo } from '../synchronization/sync-info';
import { Uuid } from '../types/uuid';
import { Collection } from './collection';
import { Node } from './node';

type QuantifiedFilter<T> =
    | (T & { _atLeast: number })
    | (T & { _atMost: number })
    | (T & { _every: boolean })
    | (T & { _none: boolean });

export type PropertyQueryFilter<This extends Node, V> =
    Exclude<V, null> extends infer U
        ? U extends Collection<infer ElementNode>
            ? ElementNode extends Node
                ? QuantifiedFilter<NodeFilter<This, ElementNode>>
                : never
            : U extends Node
              ? NodeFilter<This, U> | ValueQueryFilter<This, V extends null ? string | null : string>
              : U extends any[]
                ? (U[number] extends Node ? U | string[] | number[] : U) | ValueQueryFilter<This, V>
                : ValueQueryFilter<This, V>
        : never;

type PropertyOpFilter<This extends Node, V> = {
    _and?: ValueQueryFilter<This, V>[];
    _or?: ValueQueryFilter<This, V>[];
    _nor?: ValueQueryFilter<This, V>[];
    _not?: ValueQueryFilter<This, V>;

    _eq?: ValueQueryFilter<This, V>;
    _ne?: ValueQueryFilter<This, V>;
    _lt?: ValueQueryFilter<This, V>;
    _lte?: ValueQueryFilter<This, V>;
    _gt?: ValueQueryFilter<This, V>;
    _gte?: ValueQueryFilter<This, V>;

    _contains?: V extends any[]
        ? V[number] extends Node
            ? V[number] | string | number
            : V[number]
        : ValueQueryFilter<This, V>;
    _containsRange?: V extends any[]
        ? V[number] extends Node
            ? V[number] | string | number
            : V[number]
        : ValueQueryFilter<This, V>;
    _containedBy?: V extends any[]
        ? V[number] extends Node
            ? V[number] | string[] | number[]
            : V[number][]
        : ValueQueryFilter<This, V>;

    _in?: ValueQueryFilter<This, V>[];
    _nin?: ValueQueryFilter<This, V>[];
    _mod?: number[];
    _fn?: string;
    text?: never;
};

export type FilterOp = Omit<keyof PropertyOpFilter<Node, any>, 'text'>;

type RangeType<This extends Node> =
    | {
          start?: ValueQueryFilter<This, DateValue>;
          end?: ValueQueryFilter<This, DateValue>;
          includedEnd?: ValueQueryFilter<This, DateValue>;
      }
    | DateRange
    | DatetimeRange
    | Datetime
    | IntegerRange
    | DecimalRange
    | Decimal
    | DateValue
    | string
    | number
    | null;

export type QueryFilterBasicValue = boolean | number | DateValue | Datetime | Uuid | null;

type DateFilter<This extends Node> = {
    value?: ValueQueryFilter<This, integer>;
    epoch?: ValueQueryFilter<This, integer>;
    year?: ValueQueryFilter<This, integer>;
    month?: ValueQueryFilter<This, integer>;
    day?: ValueQueryFilter<This, integer>;
    week?: ValueQueryFilter<This, integer>;
    weekDay?: ValueQueryFilter<This, integer>;
    yearDay?: ValueQueryFilter<This, integer>;
};

type ValueQueryFilter<This extends Node, V> =
    | PropertyOpFilter<This, V>
    | (V extends string
          ? RegExp | number | string | { _regex: string; _options?: string } | ((this: This) => AsyncResponse<string>)
          : V extends QueryFilterBasicValue
            ? V | ((this: This) => AsyncResponse<V>)
            : never)
    | RangeType<This>
    | (V extends DateValue ? DateFilter<This> : never);

type NodeOpFilter<This extends Node, T extends Node> = {
    _and?: NodeFilter<This, T>[];
    _or?: NodeFilter<This, T>[];
    _nor?: NodeFilter<This, T>[];
    _not?: NodeFilter<This, T>;
    _fn?: string;
    _id?: number | PropertyOpFilter<This, number>;
    _createStamp?: Datetime | PropertyOpFilter<This, Datetime>;
    _updateStamp?: Datetime | PropertyOpFilter<This, Datetime>;
    _syncTick?: Decimal | PropertyOpFilter<This, Decimal>;
    _syncInfo?: PropertyOpFilter<This, SyncInfo>;
};

// Local helper type for NodeQueryFilter
// Generics are swapped wrt NodeQueryFilter, for consistency with other local types above.
type NodeFilter<This extends Node, T extends Node> =
    | Partial<T>
    | ((this: This) => AsyncResponse<boolean>)
    | (NodeOpFilter<This, T> & {
          -readonly [K in keyof T]?: K extends '$'
              ? never
              :
                    | PropertyQueryFilter<This, UnPromised<T[K]>>
                    | ((this: This) => AsyncResponse<PropertyQueryFilter<This, UnPromised<T[K]>>>);
      });

/**
 * Type for query filters
 *
 * `T` is the node we are querying
 * `This` is the type of the _this_ parameter in the filter functions.
 *
 * On the control/lookup filters of a reference proeprty, `This` is different from `T`:
 * - `T` is the referenced node (Customer if the property is the SalesOrder.customer)
 * - `This` is the referencing node (SalesOrder if the property is the SalesOrder.customer)
 */
export type NodeQueryFilter<T extends Node, This extends Node = T> = NodeFilter<This, T>;

export type NodeControlFilter<T extends Node, This extends Node = T> = {
    filter: NodeQueryFilter<T, This>;
    getErrorMessage: (this: This) => AsyncResponse<string>;
};

export type NodeControlFilters<T extends Node, This extends Node = T> = NodeControlFilter<T, This>[];

export type AnyFilterValue = AnyValue | ((this: Node) => AsyncResponse<AnyFilterValue>) | AnyFilterObject;

export type AnyFilterObject = Dict<AnyFilterValue>;
