/** @ignore */ /** */
import { AnyValue, AsyncResponse } from '@sage/xtrem-async-helper';
import { Logger, clsContext, withClsContext } from '@sage/xtrem-log';
import { recordXtremErrorToNewrelic } from '@sage/xtrem-metrics';
import { BaseError, LocalizedError, LogicError, SecurityError, SystemError } from '@sage/xtrem-shared';
import { Context } from './runtime';
import { loggers } from './runtime/loggers';

const logger = loggers.core;

const logError = (context: Context, e: Error): void => {
    const message = e instanceof SecurityError ? e.securityMessage(context) : e.message;
    if (e instanceof BaseError && e.innerError) {
        logger.error(message);
        logError(context, e.innerError);
    } else if (e.stack) {
        if (message !== e.message) {
            logger.error(message);
        }
        logger.error(e.stack);
    } else {
        logger.error(message);
    }
};

export const wrapError = (context: Context, err: Error): BaseError => {
    if (err instanceof LocalizedError) return err;

    logError(context, err);
    recordXtremErrorToNewrelic(context, err);

    // cloudflareRayID is logged only when logs are in json format.
    const localized =
        context.cloudflareRayID && Logger.isLogAsJson()
            ? context.localize(
                  '@sage/xtrem-core/generic-error-origin-id',
                  'An error occurred. Contact your administrator. Support ID: {{cloudflareRayID}}',
                  {
                      cloudflareRayID: context.cloudflareRayID,
                  },
              )
            : context.localize('@sage/xtrem-core/generic-error', 'An error occurred. Contact your administrator.');

    if (err instanceof LogicError) {
        return new LogicError(localized, err);
    }
    return new SystemError(localized, err);
};

// run concurrent tasks with fresh context so that re-entrency detection works ok
// eslint-disable-next-line require-await
async function runWithNewClsContext<T extends AnyValue | void>(
    fn: () => AsyncResponse<T>,
    context?: Context,
): Promise<T> {
    return withClsContext(fn, { context });
}

/** @internal */
export function runMiddleware<T extends AnyValue | void>(
    context: Context,
    fn: () => AsyncResponse<T>,
    error: () => void,
): Promise<T | void> {
    return runWithNewClsContext(fn, context).catch((err: Error) => {
        logError(context, err);
        error();
    });
}

/** @internal */
export function runResolver<T extends AnyValue | void>(context: Context, fn: () => AsyncResponse<T>): Promise<T> {
    return runWithNewClsContext(
        () =>
            loggers.graphQl.doAsync(fn, {
                // Do not duplicate "Request timeout" logs
                ignoreCallback: message => context.shouldIgnoreDuplicateLogs(message),
            }),
        context,
    ).catch((err: Error) => {
        throw wrapError(context, err);
    });
}

/** @internal */
export function safeSetInterval(fn: () => void, millis: number): NodeJS.Timeout {
    return global.setInterval(() => {
        runWithNewClsContext(fn).catch(err => logger.error(err));
    }, millis);
}

/** @internal */
export function safeSetImmediate(fn: () => void): NodeJS.Immediate {
    return global.setImmediate(() => {
        runWithNewClsContext(fn).catch(err => logger.error(err));
    });
}

export async function withDetachedClsContext<T extends AnyValue>(
    context: Context,
    body: (context: Context) => AsyncResponse<T>,
): Promise<T> {
    const oldContext = clsContext().context;
    try {
        clsContext().context = context;
        return await body(context);
    } finally {
        clsContext().context = oldContext;
    }
}
