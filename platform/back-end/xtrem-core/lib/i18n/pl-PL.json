{"@sage/xtrem-core/generic-error": "An error occurred. Contact your administrator.", "@sage/xtrem-core/generic-error-origin-id": "An error occurred. Contact your administrator. Support ID: {{cloudflareRayID}}", "@sage/xtrem-core/invalid-filter": "Invalid filter (parsed value is a string, expected an object): {{filter}}", "@sage/xtrem-core/expected-array": "Expected array, got {{type}}", "@sage/xtrem-core/invalid-property-type-order-by": "Invalid property type in orderBy {{type}}.", "@sage/xtrem-core/invalid-cursor-value": "Invalid cursor value: {{value}}", "@sage/xtrem-core/value-provided-auto-increment": "A value is provided for an auto-increment property.", "@sage/xtrem-core/cannot-initialize-join": "\"Join\" initialization impossible: Different values for '{{k}}': expected {{data}}, got {{value}}.", "@sage/xtrem-core/invalid-value-in-array-property": "Invalid value in array property: {{value}}", "@sage/xtrem-core/record-does-not-exist": "Invalid value. The record in array does not exist.", "@sage/xtrem-core/invalid-reference-array-value": "Invalid reference array value: {{type}}, {{value}}", "@sage/xtrem-core/invalid-reference-value": "Invalid reference value: {{type}}, {{value}}", "@sage/xtrem-core/invalid-collection-value-not-array": "Invalid collection value: not an array", "@sage/xtrem-core/cannot-set-value-on-computed-property": "Computed properties cannot be assigned a value.", "@sage/xtrem-core/cannot-set-inactive-property": "Inactive properties cannot be set.", "@sage/xtrem-core/invalid-value": "Invalid value: {{type}}, {{value}}", "@sage/xtrem-core/cannot-assign-to-non-nullable": "The {{value}} value cannot be assigned to a non-nullable property.", "@sage/xtrem-core/invalid-value-vital-reference": "Invalid value for vital reference: typeof={{type}}, value={{value}}", "@sage/xtrem-core/node-is-readonly": "The node is read-only.", "@sage/xtrem-core/node-is-frozen": "The node is frozen.", "@sage/xtrem-core/cannot-set-value-on-frozen-property": "Frozen properties cannot be assigned a value.", "@sage/xtrem-core/bundle-set-own-properties": "The '{{id}}' ID can only set its own properties.", "@sage/xtrem-core/decorators-lacks-key-information": "Reference update impossible. Decorator is missing key information.", "@sage/xtrem-core/cannot-update-immutable-collection": "Immutable collections cannot be updated.", "@sage/xtrem-core/property-is-required": "Wymagana właściwość", "@sage/xtrem-core/bundle-not-active": "{{factory}}: {{extension}}.{{k}} unavailable: The '{{id}}' bundle is inactive.", "@sage/xtrem-core/record-not-found": "Record not found: {{values}}", "@sage/xtrem-core/record-not-found-unauthorized": "Unauthorized: {{values}}", "@sage/xtrem-core/could-not-delete-blocked-by-property": "Deletion impossible: Blocked by property {{factory}}.{{property}}", "@sage/xtrem-core/shared-by-all-tenants": "{{verb}}: action impossible. Information shared by all tenants.", "@sage/xtrem-core/user-not-allowed-operation": "", "@sage/xtrem-core/value-must-be-empty": "Zostaw pole {{v1}} puste.", "@sage/xtrem-core/value-must-not-be-empty": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pole {{v1}}.", "@sage/xtrem-core/value-must-be-equal": "W<PERSON><PERSON>adź {{v2}} w polu {{v1}}.", "@sage/xtrem-core/value-must-not-be-equal": "Enter a value different from {{v2}} in the {{v1}} field.", "@sage/xtrem-core/value-must-be-greater-than": "<PERSON><PERSON><PERSON><PERSON><PERSON> warto<PERSON> wię<PERSON><PERSON><PERSON> niż {{v2}} w polu {{v1}}.", "@sage/xtrem-core/value-must-not-be-greater-than": "You cannot enter a value greater than {{v2}} in the {{v1}} field.", "@sage/xtrem-core/value-must-be-less-than": "<PERSON><PERSON><PERSON><PERSON><PERSON> wartoś<PERSON> mniej<PERSON> niż {{v2}} w polu {{v1}}.", "@sage/xtrem-core/value-must-not-be-less-than": "You cannot enter a value less than {{v2}} in the {{v1}} field.", "@sage/xtrem-core/value-must-be-at-least": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{v2}} lub wi<PERSON><PERSON><PERSON><PERSON> w polu {{v1}}.", "@sage/xtrem-core/value-must-not-be-at-least": "Enter a value lower than {{v2}}.", "@sage/xtrem-core/value-must-be-at-most": "Enter {{v2}} or less in the {{v1}} field.", "@sage/xtrem-core/value-must-not-be-at-most": "<PERSON><PERSON><PERSON><PERSON><PERSON> warto<PERSON> wię<PERSON><PERSON><PERSON> niż {{v2}} w polu {{v1}}.", "@sage/xtrem-core/value-must-be-part-of-set": "Wprowadź jedną z następujących wartości w polu {{v1}}: {{v2}}", "@sage/xtrem-core/value-must-not-be-part-of-set": "<PERSON>e można wprow<PERSON><PERSON>ć następujących wartości w polu {{v1}}: {{v2}}", "@sage/xtrem-core/value-must-be-true": "<PERSON><PERSON><PERSON>ć musi być typu prawda.", "@sage/xtrem-core/value-must-not-be-true": "War<PERSON>ść nie może być typu prawda.", "@sage/xtrem-core/value-must-be-false": "War<PERSON>ść musi być typu fałsz.", "@sage/xtrem-core/value-must-not-be-false": "Wartość nie może być typu fałsz.", "@sage/xtrem-core/value-must-match": "Value {{v1}} must match {{v2}}.", "@sage/xtrem-core/value-must-not-match": "Value {{v1}} must not match {{v2}}.", "@sage/xtrem-core/value-must-be-zero": "W<PERSON><PERSON>adź zero w polu {{v1}}.", "@sage/xtrem-core/value-must-not-be-zero": "<PERSON><PERSON><PERSON><PERSON><PERSON> wartość inną niż zero w polu {{v1}}.", "@sage/xtrem-core/value-must-be-positive": "<PERSON><PERSON><PERSON><PERSON><PERSON> wartość dodatnią w polu {{v1}}.", "@sage/xtrem-core/value-must-not-be-positive": "<PERSON>e można wprowadzić dodatniej wartości w polu {{v1}}.", "@sage/xtrem-core/value-must-be-negative": "Wprowadź ujemną warto<PERSON>ć w polu {{v1}}.", "@sage/xtrem-core/value-must-not-be-negative": "<PERSON>e można wprowadzić ujemnej wartości w polu {{v1}}.", "@sage/xtrem-core/value-must-be-before": "W<PERSON><PERSON><PERSON><PERSON> datę wcześniejszą niż {{v2}} w polu {{v1}}.", "@sage/xtrem-core/value-must-not-be-before": "Nie można wp<PERSON><PERSON><PERSON>ć wartości wcześniejszej niż {{v2}} w polu {{v1}}.", "@sage/xtrem-core/value-must-not-be-after": "value must not be after {{v2}}", "@sage/xtrem-core/field-integrity-constraint-violation": "A record with this value already exists.", "@sage/xtrem-core/integrity-constraint-violation": "A record already exists with the same data.", "@sage/xtrem-core/pages__page_1____title": "page1 title", "@sage/xtrem-core/pages__page_2____title": "page2 title", "@sage/xtrem-core/stickers__sticker_1____title": "sticker1 title", "@sage/xtrem-core/stickers__sticker_2____title": "sticker2 title", "@sage/xtrem-core/cannot-set-value-protected-by-a-vendor": "Vendor protected properties cannot be assigned a value.", "@sage/xtrem-core/cannot-modify-collection-protected-by-a-vendor": "Cannot add to or delete from vendor protected collections.", "@sage/xtrem-core/property-unavailable": "Property is unavailable.", "@sage/xtrem-core/order-by-property-unavailable-or-unauthorized": "Property in order by is unavailable or unauthorized.", "@sage/xtrem-core/filter-property-unavailable-or-unauthorized": "Property in filter is unavailable or unauthorized.", "@sage/xtrem-core/record-modified-reload-required": "Another user made changes to this record. Refresh the page to add your changes.", "@sage/xtrem-core/operation-not-enabled": "Operation {{nodeName}}.{{operationName}} is not enabled by the configuration of the application.", "@sage/xtrem-core/must-not-be-deleted": "{{value}} MUST NOT BE DELETED", "@sage/xtrem-core/could-not-delete-blocked-by-vendor-code": "", "@sage/xtrem-core/unique-index-violation": "", "@sage/xtrem-core/system_property_tenant_id": "", "@sage/xtrem-core/system_property_id": "", "@sage/xtrem-core/system_property_sort_value": "", "@sage/xtrem-core/system_property_update_user": "", "@sage/xtrem-core/system_property_update_stamp": "", "@sage/xtrem-core/system_property_update_tick": "", "@sage/xtrem-core/system_property_create_user": "", "@sage/xtrem-core/system_property_create_stamp": "", "@sage/xtrem-core/system_property_custom_data": "", "@sage/xtrem-core/system_property_source_id": "", "@sage/xtrem-core/system_property_sync_tick": "", "@sage/xtrem-core/system_property_sync_info": "", "@sage/xtrem-core/system_property_etag": "", "@sage/xtrem-core/system_property_vendor": "", "@sage/xtrem-core/system_property_attachments": "", "@sage/xtrem-core/system_property_constructor": "", "@sage/xtrem-core/system_property_factory": "", "@sage/xtrem-core/system_property_action": "", "@sage/xtrem-core/system_property_tags": "", "@sage/xtrem-core/unique-index-violation-composite-key": "", "@sage/xtrem-core/record-was-not-created": "", "@sage/xtrem-core/record-was-not-updated": "", "@sage/xtrem-core/record-was-not-deleted": "", "@sage/xtrem-core/record-was-not-duplicated": "", "@sage/xtrem-core/record-was-not-saved": "", "@sage/xtrem-core/cannot-reference-inactive-record": "", "@sage/xtrem-core/control-filter-not-satisfied": ""}