{"@sage/xtrem-core/generic-error": "An error has occurred. Please contact your administrator.", "@sage/xtrem-core/generic-error-origin-id": "An error occurred. Contact your administrator. Support ID: {{cloudflareRayID}}", "@sage/xtrem-core/invalid-filter": "Invalid filter (parsed value is a string, expected an object): {{filter}}", "@sage/xtrem-core/expected-array": "expected array, got {{type}}", "@sage/xtrem-core/invalid-property-type-order-by": "invalid property type in orderBy {{type}}", "@sage/xtrem-core/invalid-cursor-value": "invalid cursor value: {{value}}", "@sage/xtrem-core/value-provided-auto-increment": "a value is provided for an auto-increment property", "@sage/xtrem-core/cannot-initialize-join": "cannot initialize join: values for '{{k}}' differ: expected {{data}}, got {{value}}", "@sage/xtrem-core/invalid-value-in-array-property": "Invalid value in array property: {{value}}", "@sage/xtrem-core/record-does-not-exist": "invalid value supplied. Record in array does not exist", "@sage/xtrem-core/invalid-reference-array-value": "invalid reference array value: {{type}}, {{value}}", "@sage/xtrem-core/invalid-reference-value": "invalid reference value: {{type}}, {{value}}", "@sage/xtrem-core/invalid-collection-value-not-array": "invalid collection value: not an array", "@sage/xtrem-core/cannot-set-value-on-computed-property": "cannot set value on computed property", "@sage/xtrem-core/cannot-set-inactive-property": "cannot set an inactive property", "@sage/xtrem-core/invalid-value": "invalid value: {{type}}, {{value}}", "@sage/xtrem-core/cannot-assign-to-non-nullable": "cannot assign {{value}} to non nullable property", "@sage/xtrem-core/invalid-value-vital-reference": "invalid value for vital reference: typeof={{type}}, value={{value}}", "@sage/xtrem-core/node-is-readonly": "node is readonly", "@sage/xtrem-core/node-is-frozen": "node is frozen", "@sage/xtrem-core/cannot-set-value-on-frozen-property": "cannot set value on frozen property", "@sage/xtrem-core/cannot-set-value-protected-by-a-vendor": "Vendor protected properties cannot be assigned a value.", "@sage/xtrem-core/cannot-modify-collection-protected-by-a-vendor": "Cannot add to or delete from vendor protected collections.", "@sage/xtrem-core/cannot-reference-inactive-record": "The record cannot be referenced because it is inactive.", "@sage/xtrem-core/control-filter-not-satisfied": "The record is not valid. You need to select a different record.", "@sage/xtrem-core/bundle-set-own-properties": "Bundle '{{id}}' can only set its own properties.", "@sage/xtrem-core/decorators-lacks-key-information": "cannot update reference, decorator lacks key information", "@sage/xtrem-core/cannot-update-immutable-collection": "cannot update immutable collection", "@sage/xtrem-core/operation-not-enabled": "Operation {{nodeName}}.{{operationName}} is not enabled by the configuration of the application.", "@sage/xtrem-core/property-is-required": "property is required", "@sage/xtrem-core/bundle-not-active": "{{factory}}: {{extension}}.{{k}} unavailable: bundle '{{id}}' not active.", "@sage/xtrem-core/record-not-found": "record not found: {{values}}", "@sage/xtrem-core/record-not-found-unauthorized": "Unauthorized: {{values}}", "@sage/xtrem-core/record-was-not-created": "The record was not created.", "@sage/xtrem-core/record-was-not-updated": "The record was not updated.", "@sage/xtrem-core/record-was-not-deleted": "The record was not deleted.", "@sage/xtrem-core/record-was-not-duplicated": "The record was not duplicated.", "@sage/xtrem-core/record-was-not-saved": "The record was not saved.", "@sage/xtrem-core/could-not-delete-blocked-by-property": "Could not delete: blocked by property {{factory}}.{{property}}", "@sage/xtrem-core/shared-by-all-tenants": "cannot {{verb}}: shared by all tenants", "@sage/xtrem-core/user-not-allowed-operation": "You cannot perform this operation {{nodeName}}.{{operation}}", "@sage/xtrem-core/value-must-be-empty": "value must be empty", "@sage/xtrem-core/value-must-not-be-empty": "value must not be empty", "@sage/xtrem-core/value-must-be-equal": "value must be equal to {{v2}}", "@sage/xtrem-core/value-must-not-be-equal": "value must not be equal to {{v2}}", "@sage/xtrem-core/value-must-be-greater-than": "value must be greater than {{v2}}", "@sage/xtrem-core/value-must-not-be-greater-than": "value must not be greater than {{v2}}", "@sage/xtrem-core/value-must-be-less-than": "value must be less than {{v2}}", "@sage/xtrem-core/value-must-not-be-less-than": "value must not be less than {{v2}}", "@sage/xtrem-core/value-must-be-at-least": "value must be at least {{v2}}", "@sage/xtrem-core/value-must-not-be-at-least": "value must not be at least {{v2}}", "@sage/xtrem-core/value-must-be-at-most": "value must be at most {{v2}}", "@sage/xtrem-core/value-must-not-be-at-most": "value must not be at most {{v2}}", "@sage/xtrem-core/value-must-be-part-of-set": "value must be part of set [{{v2}}]", "@sage/xtrem-core/value-must-not-be-part-of-set": "value must not be part of set [{{v2}}]", "@sage/xtrem-core/value-must-be-true": "value must be true", "@sage/xtrem-core/value-must-not-be-true": "value must not be true", "@sage/xtrem-core/value-must-be-false": "value must be false", "@sage/xtrem-core/value-must-not-be-false": "value must not be false", "@sage/xtrem-core/value-must-match": "value must match {{v2}}", "@sage/xtrem-core/value-must-not-match": "value must not match {{v2}}", "@sage/xtrem-core/value-must-be-zero": "value must be zero", "@sage/xtrem-core/value-must-not-be-zero": "value must not be zero", "@sage/xtrem-core/value-must-be-positive": "value must be positive", "@sage/xtrem-core/value-must-not-be-positive": "value must not be positive", "@sage/xtrem-core/value-must-be-negative": "value must be negative", "@sage/xtrem-core/value-must-not-be-negative": "value must not be negative", "@sage/xtrem-core/value-must-be-before": "value must be before {{v2}}", "@sage/xtrem-core/value-must-not-be-before": "value must not be before {{v2}}", "@sage/xtrem-core/value-must-not-be-after": "value must not be after {{v2}}", "@sage/xtrem-core/field-integrity-constraint-violation": "The database rejected this operation.", "@sage/xtrem-core/integrity-constraint-violation": "The database rejected this operation.", "@sage/xtrem-core/unique-index-violation": "The operation failed because the record already exists.", "@sage/xtrem-core/unique-index-violation-composite-key": "The operation failed because the record already exists. Conflict found on {{propertyNames}}.", "@sage/xtrem-core/pages__page_1____title": "page1 title", "@sage/xtrem-core/pages__page_2____title": "page2 title", "@sage/xtrem-core/stickers__sticker_1____title": "sticker1 title", "@sage/xtrem-core/stickers__sticker_2____title": "sticker2 title", "@sage/xtrem-core/property-unavailable": "The property is unavailable.", "@sage/xtrem-core/order-by-property-unavailable-or-unauthorized": "The property in the sort order is unavailable or unauthorized.", "@sage/xtrem-core/filter-property-unavailable-or-unauthorized": "The property in the filter is unavailable or unauthorized.", "@sage/xtrem-core/must-not-be-deleted": "{{value}} MUST NOT BE DELETED", "@sage/xtrem-core/record-modified-reload-required": "Another user made changes to this record. Refresh the page to add your changes.", "@sage/xtrem-core/could-not-delete-blocked-by-vendor-code": "The record is protected by a vendor code and cannot be deleted.", "@sage/xtrem-core/system_property_tenant_id": "Tenant id", "@sage/xtrem-core/system_property_id": "_id", "@sage/xtrem-core/system_property_sort_value": "Sort value", "@sage/xtrem-core/system_property_update_user": "Update user", "@sage/xtrem-core/system_property_update_stamp": "Update stamp", "@sage/xtrem-core/system_property_update_tick": "Update tick", "@sage/xtrem-core/system_property_create_user": "Create user", "@sage/xtrem-core/system_property_create_stamp": "Create stamp", "@sage/xtrem-core/system_property_custom_data": "Custom data", "@sage/xtrem-core/system_property_source_id": "Source id", "@sage/xtrem-core/system_property_sync_tick": "Synchronization tick", "@sage/xtrem-core/system_property_sync_info": "Synchronization information", "@sage/xtrem-core/system_property_etag": "Etag", "@sage/xtrem-core/system_property_vendor": "<PERSON><PERSON><PERSON>", "@sage/xtrem-core/system_property_attachments": "Attachments", "@sage/xtrem-core/system_property_constructor": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-core/system_property_factory": "Factory", "@sage/xtrem-core/system_property_action": "Collection action", "@sage/xtrem-core/system_property_tags": "Tags"}