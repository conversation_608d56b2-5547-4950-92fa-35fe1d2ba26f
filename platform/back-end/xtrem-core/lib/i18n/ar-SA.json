{"@sage/xtrem-core/generic-error": "", "@sage/xtrem-core/generic-error-origin-id": "", "@sage/xtrem-core/invalid-filter": "", "@sage/xtrem-core/expected-array": "", "@sage/xtrem-core/invalid-property-type-order-by": "", "@sage/xtrem-core/invalid-cursor-value": "", "@sage/xtrem-core/value-provided-auto-increment": "", "@sage/xtrem-core/cannot-initialize-join": "", "@sage/xtrem-core/invalid-value-in-array-property": "", "@sage/xtrem-core/record-does-not-exist": "", "@sage/xtrem-core/invalid-reference-array-value": "", "@sage/xtrem-core/invalid-reference-value": "", "@sage/xtrem-core/invalid-collection-value-not-array": "", "@sage/xtrem-core/cannot-set-value-on-computed-property": "", "@sage/xtrem-core/cannot-set-inactive-property": "", "@sage/xtrem-core/invalid-value": "", "@sage/xtrem-core/cannot-assign-to-non-nullable": "", "@sage/xtrem-core/invalid-value-vital-reference": "", "@sage/xtrem-core/node-is-readonly": "", "@sage/xtrem-core/node-is-frozen": "", "@sage/xtrem-core/cannot-set-value-on-frozen-property": "", "@sage/xtrem-core/bundle-set-own-properties": "", "@sage/xtrem-core/decorators-lacks-key-information": "", "@sage/xtrem-core/cannot-update-immutable-collection": "", "@sage/xtrem-core/property-is-required": "", "@sage/xtrem-core/bundle-not-active": "", "@sage/xtrem-core/record-not-found": "", "@sage/xtrem-core/could-not-delete-blocked-by-property": "", "@sage/xtrem-core/shared-by-all-tenants": "", "@sage/xtrem-core/user-not-allowed-operation": "", "@sage/xtrem-core/value-must-be-empty": "", "@sage/xtrem-core/value-must-not-be-empty": "", "@sage/xtrem-core/value-must-be-equal": "", "@sage/xtrem-core/value-must-not-be-equal": "", "@sage/xtrem-core/value-must-be-greater-than": "", "@sage/xtrem-core/value-must-not-be-greater-than": "", "@sage/xtrem-core/value-must-be-less-than": "", "@sage/xtrem-core/value-must-not-be-less-than": "", "@sage/xtrem-core/value-must-be-at-least": "", "@sage/xtrem-core/value-must-not-be-at-least": "", "@sage/xtrem-core/value-must-be-at-most": "", "@sage/xtrem-core/value-must-not-be-at-most": "", "@sage/xtrem-core/value-must-be-part-of-set": "", "@sage/xtrem-core/value-must-not-be-part-of-set": "", "@sage/xtrem-core/value-must-be-true": "", "@sage/xtrem-core/value-must-not-be-true": "", "@sage/xtrem-core/value-must-be-false": "", "@sage/xtrem-core/value-must-not-be-false": "", "@sage/xtrem-core/value-must-match": "", "@sage/xtrem-core/value-must-not-match": "", "@sage/xtrem-core/value-must-be-zero": "", "@sage/xtrem-core/value-must-not-be-zero": "", "@sage/xtrem-core/value-must-be-positive": "", "@sage/xtrem-core/value-must-not-be-positive": "", "@sage/xtrem-core/value-must-be-negative": "", "@sage/xtrem-core/value-must-not-be-negative": "", "@sage/xtrem-core/value-must-be-before": "", "@sage/xtrem-core/value-must-not-be-before": "", "@sage/xtrem-core/value-must-not-be-after": "", "@sage/xtrem-core/field-integrity-constraint-violation": "A record with this value already exists.", "@sage/xtrem-core/integrity-constraint-violation": "A record already exists with the same data.", "@sage/xtrem-core/pages__page_1____title": "", "@sage/xtrem-core/pages__page_2____title": "", "@sage/xtrem-core/stickers__sticker_1____title": "", "@sage/xtrem-core/stickers__sticker_2____title": "", "@sage/xtrem-core/property-unavailable": "", "@sage/xtrem-core/order-by-property-unavailable-or-unauthorized": "", "@sage/xtrem-core/filter-property-unavailable-or-unauthorized": "", "@sage/xtrem-core/record-modified-reload-required": "", "@sage/xtrem-core/could-not-delete-blocked-by-vendor-code": "", "@sage/xtrem-core/cannot-set-value-protected-by-a-vendor": "", "@sage/xtrem-core/cannot-modify-collection-protected-by-a-vendor": "", "@sage/xtrem-core/operation-not-enabled": "", "@sage/xtrem-core/unique-index-violation": "", "@sage/xtrem-core/must-not-be-deleted": "", "@sage/xtrem-core/system_property_tenant_id": "", "@sage/xtrem-core/system_property_id": "", "@sage/xtrem-core/system_property_sort_value": "", "@sage/xtrem-core/system_property_update_user": "", "@sage/xtrem-core/system_property_update_stamp": "", "@sage/xtrem-core/system_property_update_tick": "", "@sage/xtrem-core/system_property_create_user": "", "@sage/xtrem-core/system_property_create_stamp": "", "@sage/xtrem-core/system_property_custom_data": "", "@sage/xtrem-core/system_property_source_id": "", "@sage/xtrem-core/system_property_sync_tick": "", "@sage/xtrem-core/system_property_sync_info": "", "@sage/xtrem-core/system_property_etag": "", "@sage/xtrem-core/system_property_vendor": "", "@sage/xtrem-core/system_property_attachments": "", "@sage/xtrem-core/record-not-found-unauthorized": "", "@sage/xtrem-core/system_property_constructor": "", "@sage/xtrem-core/system_property_factory": "", "@sage/xtrem-core/system_property_action": "", "@sage/xtrem-core/system_property_tags": "", "@sage/xtrem-core/unique-index-violation-composite-key": "", "@sage/xtrem-core/record-was-not-created": "", "@sage/xtrem-core/record-was-not-updated": "", "@sage/xtrem-core/record-was-not-deleted": "", "@sage/xtrem-core/record-was-not-duplicated": "", "@sage/xtrem-core/record-was-not-saved": "", "@sage/xtrem-core/cannot-reference-inactive-record": "", "@sage/xtrem-core/control-filter-not-satisfied": ""}