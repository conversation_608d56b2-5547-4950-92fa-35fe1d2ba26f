{"@sage/xtrem-core/generic-error": "Ha habido un error. Contacta con el administrador.", "@sage/xtrem-core/generic-error-origin-id": "Ha habido un error. Contacta con el administrador. Id. de soporte: {{cloudflareRayID}}", "@sage/xtrem-core/invalid-filter": "Invalid filter (parsed value is a string, expected an object): {{filter}}", "@sage/xtrem-core/expected-array": "Expected array, got {{type}}", "@sage/xtrem-core/invalid-property-type-order-by": "Invalid property type in orderBy {{type}}.", "@sage/xtrem-core/invalid-cursor-value": "Invalid cursor value: {{value}}", "@sage/xtrem-core/value-provided-auto-increment": "A value is provided for an auto-increment property.", "@sage/xtrem-core/cannot-initialize-join": "\"Join\" initialization impossible: Different values for '{{k}}': expected {{data}}, got {{value}}.", "@sage/xtrem-core/invalid-value-in-array-property": "Invalid value in array property: {{value}}", "@sage/xtrem-core/record-does-not-exist": "Invalid value. The record in array does not exist.", "@sage/xtrem-core/invalid-reference-array-value": "Invalid reference array value: {{type}}, {{value}}", "@sage/xtrem-core/invalid-reference-value": "Invalid reference value: {{type}}, {{value}}", "@sage/xtrem-core/invalid-collection-value-not-array": "Invalid collection value: not an array", "@sage/xtrem-core/cannot-set-value-on-computed-property": "No se puede asignar un valor a las propiedades calculadas.", "@sage/xtrem-core/cannot-set-inactive-property": "Inactive properties cannot be set.", "@sage/xtrem-core/invalid-value": "Invalid value: {{type}}, {{value}}", "@sage/xtrem-core/cannot-assign-to-non-nullable": "The {{value}} value cannot be assigned to a non-nullable property.", "@sage/xtrem-core/invalid-value-vital-reference": "Invalid value for vital reference: typeof={{type}}, value={{value}}", "@sage/xtrem-core/node-is-readonly": "The node is read-only.", "@sage/xtrem-core/node-is-frozen": "The node is frozen.", "@sage/xtrem-core/cannot-set-value-on-frozen-property": "No se puede asignar un valor a las propiedades que han dejado de responder.", "@sage/xtrem-core/bundle-set-own-properties": "The '{{id}}' ID can only set its own properties.", "@sage/xtrem-core/decorators-lacks-key-information": "Reference update impossible. Decorator is missing key information.", "@sage/xtrem-core/cannot-update-immutable-collection": "Immutable collections cannot be updated.", "@sage/xtrem-core/property-is-required": "Propiedad obligatoria", "@sage/xtrem-core/bundle-not-active": "{{factory}}: {{extension}}.{{k}} unavailable: The '{{id}}' bundle is inactive.", "@sage/xtrem-core/record-not-found": "Record not found: {{values}}", "@sage/xtrem-core/record-not-found-unauthorized": "Unauthorized: {{values}}", "@sage/xtrem-core/could-not-delete-blocked-by-property": "Deletion impossible: Blocked by property {{factory}}.{{property}}", "@sage/xtrem-core/shared-by-all-tenants": "{{verb}}: action impossible. Information shared by all tenants.", "@sage/xtrem-core/user-not-allowed-operation": "No puedes realizar la operación {{nodeName}}.{{operation}}", "@sage/xtrem-core/value-must-be-empty": "Deja el campo {{v1}} en blanco.", "@sage/xtrem-core/value-must-not-be-empty": "Introduce el campo {{v1}}.", "@sage/xtrem-core/value-must-be-equal": "Introduce {{v2}} en el campo {{v1}}.", "@sage/xtrem-core/value-must-not-be-equal": "Introduce un valor que no sea {{v2}} en el campo {{v1}}.", "@sage/xtrem-core/value-must-be-greater-than": "Introduce un valor superior a {{v2}} en el campo {{v1}}.", "@sage/xtrem-core/value-must-not-be-greater-than": "No puedes introducir un valor superior a {{v2}} en el campo {{v1}}.", "@sage/xtrem-core/value-must-be-less-than": "Introduce un valor inferior a {{v2}} en el campo {{v1}}.", "@sage/xtrem-core/value-must-not-be-less-than": "No puedes introducir un valor inferior a {{v2}} en el campo {{v1}}.", "@sage/xtrem-core/value-must-be-at-least": "Introduce {{v2}} o más en el campo {{v1}}.", "@sage/xtrem-core/value-must-not-be-at-least": "Introduce un valor inferior a {{v2}}.", "@sage/xtrem-core/value-must-be-at-most": "Introduce {{v2}} o menos en el campo {{v1}}.", "@sage/xtrem-core/value-must-not-be-at-most": "Introduce un valor superior a {{v2}} en el campo {{v1}}.", "@sage/xtrem-core/value-must-be-part-of-set": "Introduce uno de los siguientes valores en el campo {{v1}}: {{v2}}", "@sage/xtrem-core/value-must-not-be-part-of-set": "No puedes introducir los siguientes valores en el campo {{v1}}: {{v2}}", "@sage/xtrem-core/value-must-be-true": "El valor debe ser verdadero.", "@sage/xtrem-core/value-must-not-be-true": "El valor no debe ser verdadero.", "@sage/xtrem-core/value-must-be-false": "El valor debe ser falso.", "@sage/xtrem-core/value-must-not-be-false": "El valor no debe ser falso.", "@sage/xtrem-core/value-must-match": "El valor {{v1}} debe coincidir con {{v2}}.", "@sage/xtrem-core/value-must-not-match": "El valor {{v1}} no debe coincidir con {{v2}}.", "@sage/xtrem-core/value-must-be-zero": "Introduce cero en el campo {{v1}}.", "@sage/xtrem-core/value-must-not-be-zero": "Introduce un valor que no sea cero en el campo {{v1}}.", "@sage/xtrem-core/value-must-be-positive": "Introduce un valor positivo en el campo {{v1}}.", "@sage/xtrem-core/value-must-not-be-positive": "No puedes introducir un valor positivo en el campo {{v1}}.", "@sage/xtrem-core/value-must-be-negative": "Introduce un valor negativo en el campo {{v1}}.", "@sage/xtrem-core/value-must-not-be-negative": "No puedes introducir un valor negativo en el campo {{v1}}.", "@sage/xtrem-core/value-must-be-before": "Introduce una fecha anterior a {{v2}} en el campo {{v1}}.", "@sage/xtrem-core/value-must-not-be-before": "No puedes introducir un valor anterior a {{v2}} en el campo {{v1}}.", "@sage/xtrem-core/value-must-not-be-after": "El valor no debe ser posterior a {{v2}}.", "@sage/xtrem-core/field-integrity-constraint-violation": "La base de datos ha rechazado esta operación.", "@sage/xtrem-core/integrity-constraint-violation": "La base de datos ha rechazado esta operación.", "@sage/xtrem-core/pages__page_1____title": "page1 title", "@sage/xtrem-core/pages__page_2____title": "page2 title", "@sage/xtrem-core/stickers__sticker_1____title": "sticker1 title", "@sage/xtrem-core/stickers__sticker_2____title": "sticker2 title", "@sage/xtrem-core/cannot-set-value-protected-by-a-vendor": "No se puede asignar un valor a las propiedades que están protegidas por el proveedor.", "@sage/xtrem-core/cannot-modify-collection-protected-by-a-vendor": "No se puede añadir a colecciones protegidas de proveedores ni tampoco se puede eliminar de ellas.", "@sage/xtrem-core/property-unavailable": "La propiedad no está disponible.", "@sage/xtrem-core/order-by-property-unavailable-or-unauthorized": "La propiedad del argumento orderBy no está disponible ni autorizada.", "@sage/xtrem-core/filter-property-unavailable-or-unauthorized": "La propiedad del filtro no está disponible ni autorizada.", "@sage/xtrem-core/must-not-be-deleted": "{{value}} MUST NOT BE DELETED", "@sage/xtrem-core/record-modified-reload-required": "Otro usuario ha modificado este registro. Actualiza la página para añadir tus cambios.", "@sage/xtrem-core/operation-not-enabled": "La operación \"{{nodeName}}.{{operationName}}\" no está habilitada por la configuración de la aplicación.", "@sage/xtrem-core/unique-index-violation": "Ha habido un error con la operación porque el registro ya existe.", "@sage/xtrem-core/could-not-delete-blocked-by-vendor-code": "No puedes eliminar este registro porque está protegido por el proveedor.", "@sage/xtrem-core/system_property_tenant_id": "Id. de instancia", "@sage/xtrem-core/system_property_id": "Id.", "@sage/xtrem-core/system_property_sort_value": "Número de línea", "@sage/xtrem-core/system_property_update_user": "Actualizado por", "@sage/xtrem-core/system_property_update_stamp": "Actualización", "@sage/xtrem-core/system_property_update_tick": "Marca de actualización", "@sage/xtrem-core/system_property_create_user": "<PERSON><PERSON>o por", "@sage/xtrem-core/system_property_create_stamp": "Creación", "@sage/xtrem-core/system_property_custom_data": "Datos personalizados", "@sage/xtrem-core/system_property_source_id": "Id. de origen", "@sage/xtrem-core/system_property_sync_tick": "Marca de sincronización", "@sage/xtrem-core/system_property_sync_info": "Información de sincronización", "@sage/xtrem-core/system_property_etag": "ETag", "@sage/xtrem-core/system_property_vendor": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-core/system_property_attachments": "Archivos adjuntos", "@sage/xtrem-core/system_property_constructor": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-core/system_property_factory": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-core/system_property_action": "Acción de colección", "@sage/xtrem-core/system_property_tags": "Etiquetas", "@sage/xtrem-core/unique-index-violation-composite-key": "Ha habido un error con la operación porque el registro ya existe. Hay un conflicto en las siguientes propiedades: {{propertyNames}}.", "@sage/xtrem-core/record-was-not-created": "El registro no se ha creado.", "@sage/xtrem-core/record-was-not-updated": "El registro no se ha actualizado.", "@sage/xtrem-core/record-was-not-deleted": "El registro no se ha eliminado.", "@sage/xtrem-core/record-was-not-duplicated": "El registro no se ha duplicado.", "@sage/xtrem-core/record-was-not-saved": "El registro no se ha guardado.", "@sage/xtrem-core/cannot-reference-inactive-record": "No puedes referenciar un registro inactivo.", "@sage/xtrem-core/control-filter-not-satisfied": "Selecciona un registro válido."}