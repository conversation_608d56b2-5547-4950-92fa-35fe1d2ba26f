{"@sage/xtrem-core/generic-error": "Ocorreu um erro. Contactar o administrador de sistemas.", "@sage/xtrem-core/generic-error-origin-id": "Ocorreu um erro. Contacte o seu administrador  de sistemas. ID de apoio: {{cloudflareRayID}}", "@sage/xtrem-core/invalid-filter": "Filtro inválido (valor parsed é uma string, espera-se um objecto): {{filter}}", "@sage/xtrem-core/expected-array": "Espera-se uma matriz, obteve-se {{type}}", "@sage/xtrem-core/invalid-property-type-order-by": "Tipo de propriedade inválida em ordemPor (orderBy) {{type}}.", "@sage/xtrem-core/invalid-cursor-value": "Valor inválido do cursor: {{value}}", "@sage/xtrem-core/value-provided-auto-increment": "É fornecido um valor para uma propriedade de auto-incrementação.", "@sage/xtrem-core/cannot-initialize-join": "Inicialização \"Join\" impossível: Valores diferentes para '{{k}}': esperado {{data}}, obtido {{value}}.", "@sage/xtrem-core/invalid-value-in-array-property": "Valor inválido na propriedade da matriz: {{value}}", "@sage/xtrem-core/record-does-not-exist": "Valor inválido. O registo na matriz não existe.", "@sage/xtrem-core/invalid-reference-array-value": "<PERSON><PERSON> in<PERSON><PERSON><PERSON> da matriz de referência: {{type}}, {{value}}", "@sage/xtrem-core/invalid-reference-value": "Valor de referência inválido: {{type}}, {{value}}", "@sage/xtrem-core/invalid-collection-value-not-array": "Valor inválido da colecção: não é uma matriz", "@sage/xtrem-core/cannot-set-value-on-computed-property": "Não se pode atribuir um valor às propriedades computadas.", "@sage/xtrem-core/cannot-set-inactive-property": "As propriedades inativas não podem ser definidas.", "@sage/xtrem-core/invalid-value": "Valor inválido: {{type}}, {{value}}", "@sage/xtrem-core/cannot-assign-to-non-nullable": "O valor {{value}} não pode ser atribuído a um bem não-nulo.", "@sage/xtrem-core/invalid-value-vital-reference": "Valor inválido para referência vital: tipoof={{type}}, valor={{value}}", "@sage/xtrem-core/node-is-readonly": "O nó é apenas de leitura.", "@sage/xtrem-core/node-is-frozen": "O nó está suspenso.", "@sage/xtrem-core/cannot-set-value-on-frozen-property": "<PERSON>ão pode ser atribuído um valor às propriedades suspensas.", "@sage/xtrem-core/bundle-set-own-properties": "O ID '{{id}}' s<PERSON> pode definir as suas pr<PERSON><PERSON><PERSON><PERSON> propriedades.", "@sage/xtrem-core/decorators-lacks-key-information": "Actualização de referência impossível. Falta informação chave sobre o decorador.", "@sage/xtrem-core/cannot-update-immutable-collection": "As colecções imutáveis não podem ser actualizadas.", "@sage/xtrem-core/property-is-required": "Propriedade obrigatória", "@sage/xtrem-core/bundle-not-active": "{{factory}}: {{extension}}.{{k}} não disponível: O pacote '{{id}}' está inativo.", "@sage/xtrem-core/record-not-found": "Registo não encontrado: {{values}}", "@sage/xtrem-core/could-not-delete-blocked-by-property": "Eliminação impossível: Bloqueado pela propriedade {{factory}}.{{property}}", "@sage/xtrem-core/shared-by-all-tenants": "{{verb}}: acção impossível. Informação partilhada por todos os tenants.", "@sage/xtrem-core/user-not-allowed-operation": "Não é possível efetuar esta operação {{nodeName}}.{{operation}}", "@sage/xtrem-core/value-must-be-empty": "Deixar o campo {{v1}} vazio.", "@sage/xtrem-core/value-must-not-be-empty": "Registar o campo {{v1}}.", "@sage/xtrem-core/value-must-be-equal": "Registar {{v2}} no campo {{v1}}.", "@sage/xtrem-core/value-must-not-be-equal": "Introduza um valor diferente de {{v2}} no campo {{v1}}.", "@sage/xtrem-core/value-must-be-greater-than": "Enter a value greater than {{v2}} in the {{v1}} field.", "@sage/xtrem-core/value-must-not-be-greater-than": "Não é possível introduzir um valor superior a {{v2}} no campo {{v1}}.", "@sage/xtrem-core/value-must-be-less-than": "Introduzir um valor inferior a {{v2}} no campo {{v1}}.", "@sage/xtrem-core/value-must-not-be-less-than": "<PERSON><PERSON> pode introduzir um valor inferior a {{v2}} no campo {{v1}}.", "@sage/xtrem-core/value-must-be-at-least": "Insira {{v2}} ou mais no campo {{v1}}.", "@sage/xtrem-core/value-must-not-be-at-least": "Introduzir um valor inferior a {{v2}}.", "@sage/xtrem-core/value-must-be-at-most": "Introduzir {{v2}} ou inferior no campo {{v1}}.", "@sage/xtrem-core/value-must-not-be-at-most": "Enter a value greater than {{v2}} in the {{v1}} field.", "@sage/xtrem-core/value-must-be-part-of-set": "Introduza um dos seguintes valores no campo {{v1}}: {{v2}}", "@sage/xtrem-core/value-must-not-be-part-of-set": "Não é possível introduzir os seguintes valores no campo {{v1}}: {{v2}}", "@sage/xtrem-core/value-must-be-true": "O valor deve ser uma data e hora.", "@sage/xtrem-core/value-must-not-be-true": "O valor não deve ser verdadeiro.", "@sage/xtrem-core/value-must-be-false": "O valor deve ser uma data e hora.", "@sage/xtrem-core/value-must-not-be-false": "O valor não deve ser falso.", "@sage/xtrem-core/value-must-match": "O valor {{{v1}} deve ser no máximo {{v2}}.", "@sage/xtrem-core/value-must-not-match": "Valor {{v1}} must not match {{v2}}.", "@sage/xtrem-core/value-must-be-zero": "Introduzir zero no campo {{v1}}.", "@sage/xtrem-core/value-must-not-be-zero": "Introduza um valor diferente de zero no campo {{v1}}.", "@sage/xtrem-core/value-must-be-positive": "Introduza um valor positivo no campo {{v1}}.", "@sage/xtrem-core/value-must-not-be-positive": "Não é possível introduzir um valor positivo no campo {{v1}}.", "@sage/xtrem-core/value-must-be-negative": "Insira um valor negativo no campo {{v1}}.", "@sage/xtrem-core/value-must-not-be-negative": "Não é possível insirir um valor negativo no campo {{v1}}.", "@sage/xtrem-core/value-must-be-before": "Insira uma data antes de {{v2}} no campo {{v1}}.", "@sage/xtrem-core/value-must-not-be-before": "Não é possível introduzir um valor antes de {{v2}} no campo {{v1}}.", "@sage/xtrem-core/value-must-not-be-after": "valor não deve ser depois de {{v2}}", "@sage/xtrem-core/field-integrity-constraint-violation": "A base de dados rejeitou esta operação.", "@sage/xtrem-core/integrity-constraint-violation": "A base de dados rejeitou esta operação.", "@sage/xtrem-core/pages__page_1____title": "página1 título", "@sage/xtrem-core/pages__page_2____title": "página2 título", "@sage/xtrem-core/stickers__sticker_1____title": "sticker1 título", "@sage/xtrem-core/stickers__sticker_2____title": "sticker2 título", "@sage/xtrem-core/cannot-set-value-protected-by-a-vendor": "<PERSON>ão pode ser atribuído um valor às propriedades protegidas pelo fornecedor.", "@sage/xtrem-core/cannot-modify-collection-protected-by-a-vendor": "Não é possível acrescentar ou eliminar colecções protegidas pelo vendedor.", "@sage/xtrem-core/property-unavailable": "A propriedade não está disponível.", "@sage/xtrem-core/order-by-property-unavailable-or-unauthorized": "A propriedade em ordenação por (orderBy), não está disponível ou não está autorizada.", "@sage/xtrem-core/filter-property-unavailable-or-unauthorized": "A propriedade no filtro não está disponível ou não está autorizada.", "@sage/xtrem-core/must-not-be-deleted": "{{value}} NÃO DEVE SER ELIMINADO", "@sage/xtrem-core/record-modified-reload-required": "Outro utilizador fez alterações a este registo. Actualize a página para adicionar as suas alterações.", "@sage/xtrem-core/operation-not-enabled": "Operação {{nodeName}}.{{operationName}} não está ativada pela configuração da aplicação.", "@sage/xtrem-core/unique-index-violation": "A operação falhou porque o registo já existe.", "@sage/xtrem-core/could-not-delete-blocked-by-vendor-code": "O registo está protegido por um código de fornecedor e não pode ser eliminado.", "@sage/xtrem-core/system_property_tenant_id": "ID do tenant", "@sage/xtrem-core/system_property_id": "ID", "@sage/xtrem-core/system_property_sort_value": "Ordenar valor", "@sage/xtrem-core/system_property_update_user": "Atualizar utilizador", "@sage/xtrem-core/system_property_update_stamp": "<PERSON><PERSON><PERSON><PERSON> selo", "@sage/xtrem-core/system_property_update_tick": "Atualizar a marca de verificação", "@sage/xtrem-core/system_property_create_user": "<PERSON><PERSON>r utilizador", "@sage/xtrem-core/system_property_create_stamp": "<PERSON><PERSON><PERSON> selo", "@sage/xtrem-core/system_property_custom_data": "Dados personalizados", "@sage/xtrem-core/system_property_source_id": "ID de origem", "@sage/xtrem-core/system_property_sync_tick": "Marca de sincronização", "@sage/xtrem-core/system_property_sync_info": "Informações de sincronização", "@sage/xtrem-core/system_property_etag": "ETag", "@sage/xtrem-core/system_property_vendor": "Fornecedor", "@sage/xtrem-core/system_property_attachments": "Anexos", "@sage/xtrem-core/record-not-found-unauthorized": "Não autorizado: {{values}}", "@sage/xtrem-core/system_property_constructor": "Construtor", "@sage/xtrem-core/system_property_factory": "Fábrica", "@sage/xtrem-core/system_property_action": "Ação de recolha (collection)", "@sage/xtrem-core/system_property_tags": "Etiquetas", "@sage/xtrem-core/unique-index-violation-composite-key": "A operação falhou porque o registo já existe. Conflito encontrado em {{propertyNames}}.", "@sage/xtrem-core/record-was-not-created": "O registo não foi criado.", "@sage/xtrem-core/record-was-not-updated": "O registo não foi atualizado.", "@sage/xtrem-core/record-was-not-deleted": "O registo não foi eliminado.", "@sage/xtrem-core/record-was-not-duplicated": "O registo não foi duplicado.", "@sage/xtrem-core/record-was-not-saved": "The record has not been duplicated.", "@sage/xtrem-core/cannot-reference-inactive-record": "O registo não pode ser referenciado porque está inativo.", "@sage/xtrem-core/control-filter-not-satisfied": "O registo não é válido. É necessário selecionar um registo diferente."}