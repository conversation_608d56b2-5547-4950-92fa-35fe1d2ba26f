{"@sage/xtrem-core/generic-error": "Une erreur s'est produite. Contacter votre administrateur.", "@sage/xtrem-core/generic-error-origin-id": "Une erreur s'est produite. Contacter votre administrateur. Code support : {{cloudflareRayID}}", "@sage/xtrem-core/invalid-filter": "Filtre invalide (la valeur analysée est une chaîne alors que la valeur attendue est un objet) : {{filter}}", "@sage/xtrem-core/expected-array": "Tableau attendu, obtenu {{type}}", "@sage/xtrem-core/invalid-property-type-order-by": "Type de propriété invalide dans orderBy {{type}}.", "@sage/xtrem-core/invalid-cursor-value": "<PERSON><PERSON> de <PERSON>ur invalide : {{value}}", "@sage/xtrem-core/value-provided-auto-increment": "Une valeur est fournie pour une propriété d'auto-incrémentation.", "@sage/xtrem-core/cannot-initialize-join": "Initialisation \"Jointure\" impossible : valeurs différentes pour '{{k}}' : attendues {{data}}, obtenues {{value}}.", "@sage/xtrem-core/invalid-value-in-array-property": "Valeur invalide dans la propriété de tableau : {{value}}", "@sage/xtrem-core/record-does-not-exist": "Valeur invalide. L'enregistrement dans le tableau n'existe pas.", "@sage/xtrem-core/invalid-reference-array-value": "<PERSON>ur de tableau de référence invalide : {{type}}, {{value}}", "@sage/xtrem-core/invalid-reference-value": "<PERSON><PERSON> <PERSON> ré<PERSON><PERSON><PERSON><PERSON> invalide :{{type}}, {{value}}", "@sage/xtrem-core/invalid-collection-value-not-array": "Valeur de collection invalide : pas un tableau", "@sage/xtrem-core/cannot-set-value-on-computed-property": "Il est impossible d'affecter une valeur aux propriétés calculées.", "@sage/xtrem-core/cannot-set-inactive-property": "Il est impossible de définir des propriétés calculées.", "@sage/xtrem-core/invalid-value": "Valeur invalide : {{type}}, {{value}}", "@sage/xtrem-core/cannot-assign-to-non-nullable": "La valeur {{value}} ne peut pas être affectée à une propriété qui n'autorise pas les valeurs nulles.", "@sage/xtrem-core/invalid-value-vital-reference": "Valeur invalide pour la référence vitale : typeof={{type}}, value={{value}}", "@sage/xtrem-core/node-is-readonly": "Le node est en lecture seule.", "@sage/xtrem-core/node-is-frozen": "Le node est gelé.", "@sage/xtrem-core/cannot-set-value-on-frozen-property": "Il est impossible d'affecter une valeur aux propriétés gelées.", "@sage/xtrem-core/bundle-set-own-properties": "L'ID '{{id}}' peut uniquement définir ses propres propriétés.", "@sage/xtrem-core/decorators-lacks-key-information": "Mise à jour de la référence impossible. Il manque des informations clés au décorateur.", "@sage/xtrem-core/cannot-update-immutable-collection": "Les collections non modifiables ne peuvent pas être mises à jour.", "@sage/xtrem-core/property-is-required": "Propriété obligatoire", "@sage/xtrem-core/bundle-not-active": "{{factory}} : {{extension}}.{{k}} indisponible : le bundle '{{id}}' est inactif.", "@sage/xtrem-core/record-not-found": "Enregistrement introuvable : {{values}}", "@sage/xtrem-core/could-not-delete-blocked-by-property": "Suppression impossible : bloquée par la propriété {{factory}}.{{property}}", "@sage/xtrem-core/shared-by-all-tenants": "{{verb}} : action impossible. Information partagée par tous les tenants.", "@sage/xtrem-core/user-not-allowed-operation": "Vous n'êtes pas autorisé à effectuer cette opération : {{nodeName}}.{{operation}}", "@sage/xtrem-core/value-must-be-empty": "<PERSON><PERSON> le champ {{v1}} vide.", "@sage/xtrem-core/value-must-not-be-empty": "<PERSON><PERSON><PERSON><PERSON> le champ {{v1}}.", "@sage/xtrem-core/value-must-be-equal": "<PERSON><PERSON><PERSON><PERSON> {{v2}} dans le champ {{v1}}.", "@sage/xtrem-core/value-must-not-be-equal": "<PERSON><PERSON><PERSON><PERSON> une valeur différente de {{v2}} dans le champ {{v1}}.", "@sage/xtrem-core/value-must-be-greater-than": "<PERSON><PERSON><PERSON><PERSON> une valeur supérieure à {{v2}} dans le champ {{v1}}.", "@sage/xtrem-core/value-must-not-be-greater-than": "Vous ne pouvez pas renseigner une valeur supérieure à {{v2}} dans le champ {{v1}}.", "@sage/xtrem-core/value-must-be-less-than": "<PERSON><PERSON><PERSON><PERSON> une valeur inférieure à {{v2}} dans le champ {{v1}}.", "@sage/xtrem-core/value-must-not-be-less-than": "Vous ne pouvez pas renseigner une valeur inférieure à {{v2}} dans le champ {{v1}}.", "@sage/xtrem-core/value-must-be-at-least": "<PERSON><PERSON><PERSON><PERSON> {{v2}} ou plus dans le champ {{v1}}.", "@sage/xtrem-core/value-must-not-be-at-least": "Renseigner une valeur inférieure à {{v2}}.", "@sage/xtrem-core/value-must-be-at-most": "<PERSON><PERSON><PERSON><PERSON> {{v2}} ou une valeur inférieure dans le champ {{v1}}.", "@sage/xtrem-core/value-must-not-be-at-most": "<PERSON><PERSON><PERSON><PERSON> une valeur supérieure à {{v2}} dans le champ {{v1}}.", "@sage/xtrem-core/value-must-be-part-of-set": "Renseigner l'une des valeurs suivantes dans le champ {{v1}} : {{v2}}.", "@sage/xtrem-core/value-must-not-be-part-of-set": "Vous ne pouvez pas renseigner l'une des valeurs suivantes dans le champ {{v1}} : {{v2}}.", "@sage/xtrem-core/value-must-be-true": "La valeur doit être de type true.", "@sage/xtrem-core/value-must-not-be-true": "La valeur ne doit pas être de type true.", "@sage/xtrem-core/value-must-be-false": "La valeur doit être de type false.", "@sage/xtrem-core/value-must-not-be-false": "La valeur ne doit pas être de type false.", "@sage/xtrem-core/value-must-match": "La valeur {{v1}} doit correspondre à {{v2}}.", "@sage/xtrem-core/value-must-not-match": "La valeur {{v1}} ne doit pas correspondre à {{v2}}.", "@sage/xtrem-core/value-must-be-zero": "<PERSON><PERSON><PERSON><PERSON> dans le champ {{v1}}.", "@sage/xtrem-core/value-must-not-be-zero": "Ren<PERSON><PERSON><PERSON> une valeur différente de zéro dans le champ {{v1}}.", "@sage/xtrem-core/value-must-be-positive": "<PERSON><PERSON><PERSON><PERSON> une valeur positive dans le champ {{v1}}.", "@sage/xtrem-core/value-must-not-be-positive": "Vous ne pouvez pas renseigner de valeur positive dans le champ {{v1}}.", "@sage/xtrem-core/value-must-be-negative": "Ren<PERSON><PERSON>r une valeur négative dans le champ {{v1}}.", "@sage/xtrem-core/value-must-not-be-negative": "Vous ne pouvez pas renseigner de valeur négative dans le champ {{v1}}.", "@sage/xtrem-core/value-must-be-before": "<PERSON><PERSON><PERSON>r une date antérieure à {{v2}} dans le champ {{v1}}.", "@sage/xtrem-core/value-must-not-be-before": "Vous ne pouvez pas renseigner de valeur antérieure à {{v2}} dans le champ {{v1}}.", "@sage/xtrem-core/value-must-not-be-after": "valeur ne doit pas se situer après {{v2}}", "@sage/xtrem-core/field-integrity-constraint-violation": "La base de données a rejeté cette opération.", "@sage/xtrem-core/integrity-constraint-violation": "La base de données a rejeté cette opération.", "@sage/xtrem-core/pages__page_1____title": "Titre page1", "@sage/xtrem-core/pages__page_2____title": "Titre page2", "@sage/xtrem-core/stickers__sticker_1____title": "Titre sticker1", "@sage/xtrem-core/stickers__sticker_2____title": "Titre sticker2", "@sage/xtrem-core/cannot-set-value-protected-by-a-vendor": "Il est impossible d'affecter une valeur aux propriétés fournisseurs protégées.", "@sage/xtrem-core/cannot-modify-collection-protected-by-a-vendor": "Impossible d'ajouter ou de retirer des éléments des collections protégées par les fournisseurs.", "@sage/xtrem-core/property-unavailable": "La propriété est indisponible.", "@sage/xtrem-core/order-by-property-unavailable-or-unauthorized": "La propriété in order by est indisponible ou interdite.", "@sage/xtrem-core/filter-property-unavailable-or-unauthorized": "La propriété in filter est indisponible ou interdite.", "@sage/xtrem-core/must-not-be-deleted": "{{value}} NE DOIT PAS ETRE SUPPRIMEE.", "@sage/xtrem-core/record-modified-reload-required": "Un autre utilisateur a modifié cet enregistrement. Actualisez la page pour ajouter vos modifications.", "@sage/xtrem-core/operation-not-enabled": "L'opération {{nodeName}}.{{operationName}} n'est pas activée par la configuration de l'application.", "@sage/xtrem-core/unique-index-violation": "L'opération a échoué parce que l'enregistrement existe déjà.", "@sage/xtrem-core/could-not-delete-blocked-by-vendor-code": "L'enregistrement est protégé par un code fournisseur et ne peut pas être supprimé.", "@sage/xtrem-core/system_property_tenant_id": "Code tenant", "@sage/xtrem-core/system_property_id": "Code", "@sage/xtrem-core/system_property_sort_value": "Trier valeur", "@sage/xtrem-core/system_property_update_user": "Utilisateur de mise à jour", "@sage/xtrem-core/system_property_update_stamp": "Mettre à jour horodatage", "@sage/xtrem-core/system_property_update_tick": "Coche de mise à jour", "@sage/xtrem-core/system_property_create_user": "<PERSON><PERSON><PERSON> utilisateur", "@sage/xtrem-core/system_property_create_stamp": "<PERSON><PERSON><PERSON> horodatage", "@sage/xtrem-core/system_property_custom_data": "Donn<PERSON>", "@sage/xtrem-core/system_property_source_id": "Code d'origine", "@sage/xtrem-core/system_property_sync_tick": "Coche de synchronisation", "@sage/xtrem-core/system_property_sync_info": "Information de synchronisation", "@sage/xtrem-core/system_property_etag": "Etag", "@sage/xtrem-core/system_property_vendor": "Fournisseur", "@sage/xtrem-core/system_property_attachments": "Pièces jointes", "@sage/xtrem-core/record-not-found-unauthorized": "Non autorisé : {{values}}", "@sage/xtrem-core/system_property_constructor": "Con<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-core/system_property_factory": "Livrée", "@sage/xtrem-core/system_property_action": "Action de collection", "@sage/xtrem-core/system_property_tags": "Étiquettes", "@sage/xtrem-core/unique-index-violation-composite-key": "L'opération a échoué car l'enregistrement existe déjà. Conflit trouvé : {{propertyNames}}.", "@sage/xtrem-core/record-was-not-created": "L'enregistrement n'a pas été créé.", "@sage/xtrem-core/record-was-not-updated": "L'enregistrement n'a pas été mis à jour.", "@sage/xtrem-core/record-was-not-deleted": "L'enregistrement n'a pas été supprimé.", "@sage/xtrem-core/record-was-not-duplicated": "L'enregistrement n'a pas été dupliqué.", "@sage/xtrem-core/record-was-not-saved": "L'enregistrement n'a pas été sauvegardé.", "@sage/xtrem-core/cannot-reference-inactive-record": "L'enregistrement ne peut pas être référencé car il est inactif.", "@sage/xtrem-core/control-filter-not-satisfied": "L'enregistrement est incorrect. Vous devez en sélectionner un autre."}