{"@sage/xtrem-core/generic-error": "Ein Fehler ist aufgetreten. Wen<PERSON> Si<PERSON> sich an Ihren Administrator.", "@sage/xtrem-core/generic-error-origin-id": "<PERSON> Fehler ist aufgetreten. <PERSON><PERSON> sich an Ihren Administrator. Support-ID: {{cloudflareRayID}}", "@sage/xtrem-core/invalid-filter": "Ungültiger Filter (geparster Wert ist eine Zeichenfolge, Objekt erwartet): {{filter}}", "@sage/xtrem-core/expected-array": "<PERSON><PERSON><PERSON> erwartet, {{type}} erhalten", "@sage/xtrem-core/invalid-property-type-order-by": "Ungültiger Eigenschaftentyp in orderBy {{type}}.", "@sage/xtrem-core/invalid-cursor-value": "Ungültiger Cursor-Wert: {{value}}", "@sage/xtrem-core/value-provided-auto-increment": "Ein Wert wird für eine AutoIncrement-Eigenschaft bereitgestellt.", "@sage/xtrem-core/cannot-initialize-join": "\"Join\"-Initialisierung nicht möglich: Verschiedene Werte für '{{k}}': erwartet {{data}}, erhalten {{value}}.", "@sage/xtrem-core/invalid-value-in-array-property": "Ungültiger Wert in Array-Eigenschaft: {{value}}", "@sage/xtrem-core/record-does-not-exist": "Ungültiger Wert. Der Datensatz im Array ist nicht vorhanden.", "@sage/xtrem-core/invalid-reference-array-value": "Ungültiger Referenz-Array-Wert: {{type}}, {{value}}", "@sage/xtrem-core/invalid-reference-value": "Ungültiger Referenzwert: {{type}}, {{value}}", "@sage/xtrem-core/invalid-collection-value-not-array": "Ungültiger Collection-Wert: <PERSON><PERSON>", "@sage/xtrem-core/cannot-set-value-on-computed-property": "Berechnete Eigenschaften können nicht mit einem Wert belegt werden.", "@sage/xtrem-core/cannot-set-inactive-property": "Inaktive Eigenschaften können nicht gesetzt werden.", "@sage/xtrem-core/invalid-value": "Ungültiger Wert: {{type}}, {{value}}", "@sage/xtrem-core/cannot-assign-to-non-nullable": "Eine Eigenschaft, die keine NULL-Werte zulässt kann nicht mit dem Wert {{value}} belegt werden.", "@sage/xtrem-core/invalid-value-vital-reference": "Ungültiger Wert für vitale Referenz: TypeOf={{type}}, Wert={{value}}", "@sage/xtrem-core/node-is-readonly": "Der Node ist schreibgeschützt.", "@sage/xtrem-core/node-is-frozen": "Der Node ist fixiert.", "@sage/xtrem-core/cannot-set-value-on-frozen-property": "Fixierte Eigenschaften können nicht mit einem Wert belegt werden.", "@sage/xtrem-core/bundle-set-own-properties": "Die ID '{{id}}' kann nur ihre eigenen Eigenschaften setzen.", "@sage/xtrem-core/decorators-lacks-key-information": "Die Referenz kann nicht aktualisert werden. Dem Decorator fehlen wichtige Informationen.", "@sage/xtrem-core/cannot-update-immutable-collection": "Unveränderliche Collections können nicht aktualisiert werden.", "@sage/xtrem-core/property-is-required": "Erforderliche Eigenschaft", "@sage/xtrem-core/bundle-not-active": "{{factory}}: {{extension}}.{{k}} nicht verfügbar: Das Bundle '{{id}}' ist nicht aktiv.", "@sage/xtrem-core/record-not-found": "Datensatz nicht gefunden: {{values}}", "@sage/xtrem-core/could-not-delete-blocked-by-property": "Löschen nicht möglich: Gesperrt durch Eigenschaft {{factory}}.{{property}}", "@sage/xtrem-core/shared-by-all-tenants": "{{verb}}: Aktion nicht möglich. Informationen mit allen Tenants geteilt.", "@sage/xtrem-core/user-not-allowed-operation": "<PERSON>e können diesen Vorgang nicht ausführen: {{nodeName}}.{{operation}}", "@sage/xtrem-core/value-must-be-empty": "<PERSON><PERSON> Si<PERSON> das Feld {{v1}} leer.", "@sage/xtrem-core/value-must-not-be-empty": "Erfassen Sie das Feld {{v1}}.", "@sage/xtrem-core/value-must-be-equal": "Erfassen Sie im Feld {{v1}} den Wert {{v2}}.", "@sage/xtrem-core/value-must-not-be-equal": "Erfassen Sie im Feld {{v1}} einen anderen Wert als {{v2}}.", "@sage/xtrem-core/value-must-be-greater-than": "Erfassen Sie im Feld {{v1}} einen Wert größer als {{v2}}.", "@sage/xtrem-core/value-must-not-be-greater-than": "Sie können im Feld {{v1}} keinen Wert er<PERSON>, der größer als {{v2}} ist.", "@sage/xtrem-core/value-must-be-less-than": "Erfassen Sie im Feld {{v1}} einen Wert kleiner als {{v2}}.", "@sage/xtrem-core/value-must-not-be-less-than": "Sie können im Feld {{v1}} keinen Wert er<PERSON>, der kleiner als {{v2}} is.", "@sage/xtrem-core/value-must-be-at-least": "Erfassen Sie im Feld {{v1}} den Wert {{v2}} oder mehr.", "@sage/xtrem-core/value-must-not-be-at-least": "<PERSON><PERSON>ass<PERSON> Sie einen Wert kleiner als {{v2}}.", "@sage/xtrem-core/value-must-be-at-most": "Erfassen Sie im Feld {{v1}} einen Wert kleiner oder gleich {{v2}}.", "@sage/xtrem-core/value-must-not-be-at-most": "Erfassen Sie im Feld {{v1}} einen Wert größer als {{v2}}.", "@sage/xtrem-core/value-must-be-part-of-set": "Erfassen Sie im Feld {{v1}} einen der folgenden Werte: {{v2}}", "@sage/xtrem-core/value-must-not-be-part-of-set": "Sie können im Feld {{v1}} keinen der folgenden Werte erfassen: {{v2}}", "@sage/xtrem-core/value-must-be-true": "Der Wert muss true sein.", "@sage/xtrem-core/value-must-not-be-true": "Der Wert darf nicht true sein.", "@sage/xtrem-core/value-must-be-false": "<PERSON> muss false sein.", "@sage/xtrem-core/value-must-not-be-false": "<PERSON> Wert darf nicht false sein.", "@sage/xtrem-core/value-must-match": "Der Wert {{v1}} muss {{v2}} entsprechen.", "@sage/xtrem-core/value-must-not-match": "Der Wert {{v1}} darf nicht {{v2}} entsprechen.", "@sage/xtrem-core/value-must-be-zero": "Erfassen Sie im Feld {{v1}} den Wert Null.", "@sage/xtrem-core/value-must-not-be-zero": "Erfassen Sie im Feld {{v1}} einen anderen Wert al<PERSON> Null.", "@sage/xtrem-core/value-must-be-positive": "Erfassen Sie im Feld {{v1}} einen positiven Wert.", "@sage/xtrem-core/value-must-not-be-positive": "Sie können im Feld {{v1}} keinen positiven Wert erfassen.", "@sage/xtrem-core/value-must-be-negative": "Erfassen Sie im Feld {{v1}} einen negativen Wert.", "@sage/xtrem-core/value-must-not-be-negative": "Sie können im Feld {{v1}} keinen negativen Wert erfassen.", "@sage/xtrem-core/value-must-be-before": "Erfassen Si<PERSON> im Feld {{v1}} ein Datum vor dem {{v2}}.", "@sage/xtrem-core/value-must-not-be-before": "Sie können im Feld {{v1}} keinen Wert vor dem {{v2}} er<PERSON>ssen.", "@sage/xtrem-core/value-must-not-be-after": "Wert darf nicht nach {{v2}} liegen", "@sage/xtrem-core/field-integrity-constraint-violation": "Die Datenbank hat diesen Vorgang abgewiesen.", "@sage/xtrem-core/integrity-constraint-violation": "Die Datenbank hat diesen Vorgang abgewiesen.", "@sage/xtrem-core/pages__page_1____title": "Seite 1 Titel", "@sage/xtrem-core/pages__page_2____title": "Seite 2 Titel", "@sage/xtrem-core/stickers__sticker_1____title": "Sticker 1 Titel", "@sage/xtrem-core/stickers__sticker_2____title": "Sticker 2 Titel", "@sage/xtrem-core/cannot-set-value-protected-by-a-vendor": "Vom Hersteller geschützte Eigenschaften können nicht mit einem Wert belegt werden.", "@sage/xtrem-core/cannot-modify-collection-protected-by-a-vendor": "<PERSON>s ist nicht möglich, etwas zu den vom Hersteller geschützten Collections hinzuzufügen oder daraus zu löschen.", "@sage/xtrem-core/property-unavailable": "Die Eigenschaft ist nicht verfügbar.", "@sage/xtrem-core/order-by-property-unavailable-or-unauthorized": "Die Eigenschaft in Orderby ist nicht verfügbar oder nicht zul<PERSON>ssig.", "@sage/xtrem-core/filter-property-unavailable-or-unauthorized": "Die Eigenschaft in Filter ist nicht verfügbar oder nicht zulässig.", "@sage/xtrem-core/must-not-be-deleted": "{{value}} DARF NICHT GELÖSCHT WERDEN", "@sage/xtrem-core/record-modified-reload-required": "Ein anderer Benutzer führt Änderungen an diesem Datensatz durch. Aktualisieren Sie die Seite, um Ihre Änderungen hinzufügen.", "@sage/xtrem-core/operation-not-enabled": "Der Vorgang {{nodeName}}.{{operationName}} ist durch die Konfiguration der Anwendung nicht aktiviert.", "@sage/xtrem-core/unique-index-violation": "Der Vorgang ist fehlgeschlagen, da der Datensatz bereits vorhanden ist.", "@sage/xtrem-core/could-not-delete-blocked-by-vendor-code": "Der Datensatz ist durch einen Herstellercode geschützt und kann nicht gelöscht werden.", "@sage/xtrem-core/system_property_tenant_id": "Tenant-ID", "@sage/xtrem-core/system_property_id": "ID", "@sage/xtrem-core/system_property_sort_value": "Sortierwert", "@sage/xtrem-core/system_property_update_user": "Benutzer aktualisieren", "@sage/xtrem-core/system_property_update_stamp": "Stempel Aktualisieren", "@sage/xtrem-core/system_property_update_tick": "Takt aktualisieren", "@sage/xtrem-core/system_property_create_user": "<PERSON><PERSON><PERSON> er<PERSON>", "@sage/xtrem-core/system_property_create_stamp": "<PERSON><PERSON><PERSON>", "@sage/xtrem-core/system_property_custom_data": "Benutzerdefinierte Daten", "@sage/xtrem-core/system_property_source_id": "ID Ursprung", "@sage/xtrem-core/system_property_sync_tick": "Synchronisationstakt", "@sage/xtrem-core/system_property_sync_info": "Informationen Synchronisierung", "@sage/xtrem-core/system_property_etag": "ETag", "@sage/xtrem-core/system_property_vendor": "<PERSON><PERSON><PERSON>", "@sage/xtrem-core/system_property_attachments": "<PERSON><PERSON><PERSON>", "@sage/xtrem-core/record-not-found-unauthorized": "Nicht zulässig: {{values}}", "@sage/xtrem-core/system_property_constructor": "Konstruktor", "@sage/xtrem-core/system_property_factory": "Standard", "@sage/xtrem-core/system_property_action": "Collection-Aktion", "@sage/xtrem-core/system_property_tags": "Tags", "@sage/xtrem-core/unique-index-violation-composite-key": "Der Vorgang ist fehlgeschlagen, da der Datensatz bereits existiert. Konflikt gefunden in {{propertyNames}}.", "@sage/xtrem-core/record-was-not-created": "Der Datensatz wurde nicht erstellt.", "@sage/xtrem-core/record-was-not-updated": "Der Datensatz wurde nicht aktualisiert.", "@sage/xtrem-core/record-was-not-deleted": "Der Datensatz wurde nicht gelöscht.", "@sage/xtrem-core/record-was-not-duplicated": "Der Datensatz wurde nicht dupliziert.", "@sage/xtrem-core/record-was-not-saved": "Der Datensatz wurde nicht gespeichert.", "@sage/xtrem-core/cannot-reference-inactive-record": "Der Datensatz kann nicht referenziert werden, da er inaktiv ist.", "@sage/xtrem-core/control-filter-not-satisfied": "Der Datensatz ist nicht gültig. Sie müssen einen anderen Datensatz auswählen."}