{"@sage/xtrem-core/generic-error": "An error occurred. Contact your administrator.", "@sage/xtrem-core/generic-error-origin-id": "An error occurred. Contact your administrator. Support ID: {{cloudflareRayID}}", "@sage/xtrem-core/invalid-filter": "Invalid filter (parsed value is a string, expected an object): {{filter}}", "@sage/xtrem-core/expected-array": "Expected array, got {{type}}", "@sage/xtrem-core/invalid-property-type-order-by": "Invalid property type in orderBy {{type}}.", "@sage/xtrem-core/invalid-cursor-value": "Invalid cursor value: {{value}}", "@sage/xtrem-core/value-provided-auto-increment": "A value is provided for an auto-increment property.", "@sage/xtrem-core/cannot-initialize-join": "\"Join\" initialization impossible: Different values for '{{k}}': expected {{data}}, got {{value}}.", "@sage/xtrem-core/invalid-value-in-array-property": "Invalid value in array property: {{value}}", "@sage/xtrem-core/record-does-not-exist": "Invalid value. The record in array does not exist.", "@sage/xtrem-core/invalid-reference-array-value": "Invalid reference array value: {{type}}, {{value}}", "@sage/xtrem-core/invalid-reference-value": "Invalid reference value: {{type}}, {{value}}", "@sage/xtrem-core/invalid-collection-value-not-array": "Invalid collection value: not an array", "@sage/xtrem-core/cannot-set-value-on-computed-property": "Computed properties cannot be assigned a value.", "@sage/xtrem-core/cannot-set-inactive-property": "Inactive properties cannot be set.", "@sage/xtrem-core/invalid-value": "Invalid value: {{type}}, {{value}}", "@sage/xtrem-core/cannot-assign-to-non-nullable": "The {{value}} value cannot be assigned to a non-nullable property.", "@sage/xtrem-core/invalid-value-vital-reference": "Invalid value for vital reference: typeof={{type}}, value={{value}}", "@sage/xtrem-core/node-is-readonly": "The node is read-only.", "@sage/xtrem-core/node-is-frozen": "The node is frozen.", "@sage/xtrem-core/cannot-set-value-on-frozen-property": "Frozen properties cannot be assigned a value.", "@sage/xtrem-core/bundle-set-own-properties": "The '{{id}}' ID can only set its own properties.", "@sage/xtrem-core/decorators-lacks-key-information": "Reference update impossible. Decorator is missing key information.", "@sage/xtrem-core/cannot-update-immutable-collection": "Immutable collections cannot be updated.", "@sage/xtrem-core/property-is-required": "Mandatory property", "@sage/xtrem-core/bundle-not-active": "{{factory}}: {{extension}}.{{k}} unavailable: The '{{id}}' bundle is inactive.", "@sage/xtrem-core/record-not-found": "Record not found: {{values}}", "@sage/xtrem-core/record-not-found-unauthorized": "Unauthorized: {{values}}", "@sage/xtrem-core/could-not-delete-blocked-by-property": "Deletion impossible: Blocked by property {{factory}}.{{property}}", "@sage/xtrem-core/shared-by-all-tenants": "{{verb}}: action impossible. Information shared by all tenants.", "@sage/xtrem-core/user-not-allowed-operation": "You cannot perform this operation {{nodeName}}.{{operation}}", "@sage/xtrem-core/value-must-be-empty": "Leave the {{v1}} field empty.", "@sage/xtrem-core/value-must-not-be-empty": "Enter the {{v1}} field.", "@sage/xtrem-core/value-must-be-equal": "Enter {{v2}} in the {{v1}} field.", "@sage/xtrem-core/value-must-not-be-equal": "Enter a value different from {{v2}} in the {{v1}} field.", "@sage/xtrem-core/value-must-be-greater-than": "Enter a value greater than {{v2}} in the {{v1}} field.", "@sage/xtrem-core/value-must-not-be-greater-than": "You cannot enter a value greater than {{v2}} in the {{v1}} field.", "@sage/xtrem-core/value-must-be-less-than": "Enter a value less than {{v2}} in the {{v1}} field.", "@sage/xtrem-core/value-must-not-be-less-than": "You cannot enter a value less than {{v2}} in the {{v1}} field.", "@sage/xtrem-core/value-must-be-at-least": "Enter {{v2}} or more in the {{v1}} field.", "@sage/xtrem-core/value-must-not-be-at-least": "Enter a value lower than {{v2}}.", "@sage/xtrem-core/value-must-be-at-most": "Enter {{v2}} or less in the {{v1}} field.", "@sage/xtrem-core/value-must-not-be-at-most": "Enter a value greater than {{v2}} in the {{v1}} field.", "@sage/xtrem-core/value-must-be-part-of-set": "Enter one of the following values in the {{v1}} field: {{v2}}", "@sage/xtrem-core/value-must-not-be-part-of-set": "You cannot enter the following values in the {{v1}} field: {{v2}}", "@sage/xtrem-core/value-must-be-true": "The value must be true.", "@sage/xtrem-core/value-must-not-be-true": "The value must not be true.", "@sage/xtrem-core/value-must-be-false": "The value must be false.", "@sage/xtrem-core/value-must-not-be-false": "The value must not be false.", "@sage/xtrem-core/value-must-match": "Value {{v1}} must match {{v2}}.", "@sage/xtrem-core/value-must-not-match": "Value {{v1}} must not match {{v2}}.", "@sage/xtrem-core/value-must-be-zero": "Enter zero in the {{v1}} field.", "@sage/xtrem-core/value-must-not-be-zero": "Enter a value different from zero in the {{v1}} field.", "@sage/xtrem-core/value-must-be-positive": "Enter a positive value in the {{v1}} field.", "@sage/xtrem-core/value-must-not-be-positive": "You cannot enter a positive value in the {{v1}} field.", "@sage/xtrem-core/value-must-be-negative": "Enter a negative value in the {{v1}} field.", "@sage/xtrem-core/value-must-not-be-negative": "You cannot enter a negative value in the {{v1}} field.", "@sage/xtrem-core/value-must-be-before": "Enter a date before {{v2}} in the {{v1}} field.", "@sage/xtrem-core/value-must-not-be-before": "You cannot enter a value before {{v2}} in the {{v1}} field.", "@sage/xtrem-core/value-must-not-be-after": "value must not be after {{v2}}", "@sage/xtrem-core/field-integrity-constraint-violation": "The database rejected this operation.", "@sage/xtrem-core/integrity-constraint-violation": "The database rejected this operation.", "@sage/xtrem-core/pages__page_1____title": "page1 title", "@sage/xtrem-core/pages__page_2____title": "page2 title", "@sage/xtrem-core/stickers__sticker_1____title": "sticker1 title", "@sage/xtrem-core/stickers__sticker_2____title": "sticker2 title", "@sage/xtrem-core/cannot-set-value-protected-by-a-vendor": "Vendor protected properties cannot be assigned a value.", "@sage/xtrem-core/cannot-modify-collection-protected-by-a-vendor": "Cannot add to or delete from vendor protected collections.", "@sage/xtrem-core/property-unavailable": "Property is unavailable.", "@sage/xtrem-core/order-by-property-unavailable-or-unauthorized": "Property in order by is unavailable or unauthorized.", "@sage/xtrem-core/filter-property-unavailable-or-unauthorized": "Property in filter is unavailable or unauthorized.", "@sage/xtrem-core/must-not-be-deleted": "{{value}} MUST NOT BE DELETED", "@sage/xtrem-core/record-modified-reload-required": "Another user made changes to this record. Refresh the page to add your changes.", "@sage/xtrem-core/operation-not-enabled": "Operation {{nodeName}}.{{operationName}} is not enabled by the configuration of the application.", "@sage/xtrem-core/unique-index-violation": "The operation failed because the record already exists.", "@sage/xtrem-core/could-not-delete-blocked-by-vendor-code": "The record is protected by a vendor code and cannot be deleted.", "@sage/xtrem-core/system_property_tenant_id": "Tenant ID", "@sage/xtrem-core/system_property_id": "ID", "@sage/xtrem-core/system_property_sort_value": "Sort value", "@sage/xtrem-core/system_property_update_user": "Update user", "@sage/xtrem-core/system_property_update_stamp": "Update stamp", "@sage/xtrem-core/system_property_update_tick": "Update tick", "@sage/xtrem-core/system_property_create_user": "Create user", "@sage/xtrem-core/system_property_create_stamp": "Create stamp", "@sage/xtrem-core/system_property_custom_data": "Custom data", "@sage/xtrem-core/system_property_source_id": "Source ID", "@sage/xtrem-core/system_property_sync_tick": "Synchronization tick", "@sage/xtrem-core/system_property_sync_info": "Synchronization information", "@sage/xtrem-core/system_property_etag": "Etag", "@sage/xtrem-core/system_property_vendor": "<PERSON><PERSON><PERSON>", "@sage/xtrem-core/system_property_attachments": "Attachments", "@sage/xtrem-core/system_property_constructor": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-core/system_property_factory": "Factory", "@sage/xtrem-core/system_property_action": "Collection action", "@sage/xtrem-core/system_property_tags": "Tags", "@sage/xtrem-core/unique-index-violation-composite-key": "The operation failed because the record already exists. Conflict found on {{propertyNames}}.", "@sage/xtrem-core/record-was-not-created": "The record was not created.", "@sage/xtrem-core/record-was-not-updated": "The record was not updated.", "@sage/xtrem-core/record-was-not-deleted": "The record was not deleted.", "@sage/xtrem-core/record-was-not-duplicated": "The record was not duplicated.", "@sage/xtrem-core/record-was-not-saved": "The record was not saved.", "@sage/xtrem-core/cannot-reference-inactive-record": "The record cannot be referenced because it is inactive.", "@sage/xtrem-core/control-filter-not-satisfied": "The record is not valid. You need to select a different record."}