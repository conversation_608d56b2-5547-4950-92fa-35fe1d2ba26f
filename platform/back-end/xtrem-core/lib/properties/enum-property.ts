import { AnyValue } from '@sage/xtrem-async-helper';
import { Package } from '../application';
import { EnumArrayPropertyDecorator, EnumPropertyDecorator } from '../decorators';
import { NodeFactory } from '../runtime';
import { Enum, EnumDataType } from '../types';
import { Property } from './property';
import { isArrayOfStrings, stringToArrayOfStrings } from './util';

export class EnumProperty extends Property {
    constructor(
        factory: NodeFactory,
        protected override _decorator: EnumPropertyDecorator | EnumArrayPropertyDecorator,
        definingPackage: Package,
    ) {
        super(factory, _decorator, definingPackage);
    }

    override get dataType(): EnumDataType {
        return super.dataType as EnumDataType;
    }

    override get enum(): Enum {
        return this.dataType.enum;
    }

    // eslint-disable-next-line class-methods-use-this
    isValueTypeValid(value: AnyValue): boolean {
        return typeof value === 'string' && this.dataType.values.includes(value);
    }

    override getTypeDefaultValue(): AnyValue {
        return this.isNullable ? null : this.dataType.values[0];
    }
}

export class EnumArrayProperty extends EnumProperty {
    constructor(
        factory: NodeFactory,
        protected override _decorator: EnumArrayPropertyDecorator,
        definingPackage: Package,
    ) {
        super(factory, _decorator, definingPackage);
    }

    // eslint-disable-next-line class-methods-use-this
    override isValueTypeValid(value: AnyValue): boolean {
        return Array.isArray(value) && value.every(v => typeof v === 'string' && this.dataType.values.includes(v));
    }

    override needsTypeConversion(value: AnyValue): boolean {
        return (
            isArrayOfStrings(value as string) &&
            stringToArrayOfStrings(value as string).every(v => this.dataType.values.includes(v))
        );
    }

    // eslint-disable-next-line class-methods-use-this
    override mapInputValue(value: AnyValue): AnyValue {
        return stringToArrayOfStrings(value as string);
    }
}
