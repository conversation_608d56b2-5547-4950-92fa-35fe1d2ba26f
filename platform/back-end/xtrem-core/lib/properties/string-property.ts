import { AnyRecord, AnyValue } from '@sage/xtrem-async-helper';
import { Package } from '../application';
import { StringArrayPropertyDecorator, StringPropertyDecorator } from '../decorators';
import { NodeFactory } from '../runtime';
import { StringDataType } from '../types';
import { Property } from './property';
import { isArrayOfStrings, stringToArrayOfStrings } from './util';

export class StringProperty extends Property {
    constructor(
        factory: NodeFactory,
        protected override _decorator: StringPropertyDecorator,
        definingPackage: Package,
    ) {
        super(factory, _decorator, definingPackage);
    }

    override get isNotEmpty(): boolean {
        if (this.delegatesTo) return this.getDelegatingInfo().childProperty.isNotEmpty;
        return !!this._decorator.isNotEmpty;
    }

    override get isLocalized(): boolean {
        if (this.delegatesTo) return this.getDelegatingInfo().childProperty.isLocalized;
        return (this.dataType as StringDataType)?.isLocalized;
    }

    get isStoredEncrypted(): boolean {
        return !!this._decorator.isStoredEncrypted;
    }

    /**
     * Overrides Property.maxLength
     */
    override get maxLength(): number {
        const stringDataType = this.dataType as StringDataType;
        if (stringDataType) return stringDataType.maxLength;
        return 255;
    }

    // eslint-disable-next-line class-methods-use-this
    isValueTypeValid(value: AnyValue): boolean {
        return typeof value === 'string';
    }

    override needsTypeConversion(value: AnyValue): boolean {
        return (
            this.isLocalized &&
            typeof value === 'object' &&
            Object.values(value as AnyRecord).every(v => this.isValueTypeValid(v))
        );
    }

    // eslint-disable-next-line class-methods-use-this
    override mapInputValue(value: AnyValue): AnyValue {
        return JSON.stringify(value);
    }

    override verify(): void {
        super.verify();
        if (this.isNullable) throw this.systemError('A string property cannot be nullable');

        // Do not throw error if the property is a delegated property (as it is not stored)
        if (this.isLocalized && !this.isStored && this.delegatesTo == null)
            throw this.systemError(
                'localized properties isStored attribute must be set to true or delegated to another property',
            );
    }
}

export class StringArrayProperty extends Property {
    constructor(
        factory: NodeFactory,
        protected override _decorator: StringArrayPropertyDecorator,
        definingPackage: Package,
    ) {
        super(factory, _decorator, definingPackage);
    }

    // eslint-disable-next-line class-methods-use-this
    isValueTypeValid(value: AnyValue): boolean {
        return Array.isArray(value) && value.every(v => typeof v === 'string');
    }

    // eslint-disable-next-line class-methods-use-this
    override needsTypeConversion(value: AnyValue): boolean {
        return isArrayOfStrings(value as string);
    }

    // eslint-disable-next-line class-methods-use-this
    override mapInputValue(value: AnyValue): AnyValue {
        return stringToArrayOfStrings(value as string);
    }

    override verify(): void {
        super.verify();
        if (this.isNullable) throw this.systemError('A string array property cannot be nullable');

        if (this.isLocalized && !this.isStored)
            throw this.systemError('localized properties isStored attribute must be set to true');
    }
}
