import { AnyValue } from '@sage/xtrem-async-helper';
import { Package } from '../application';
import { BooleanPropertyDecorator } from '../decorators';
import { NodeFactory } from '../runtime/node-factory';
import { Property } from './property';

export class BooleanProperty extends Property {
    constructor(
        factory: NodeFactory,
        protected override _decorator: BooleanPropertyDecorator,
        definingPackage: Package,
    ) {
        super(factory, _decorator, definingPackage);
    }

    // eslint-disable-next-line class-methods-use-this
    isValueTypeValid(value: AnyValue): boolean {
        return typeof value === 'boolean';
    }

    // eslint-disable-next-line class-methods-use-this
    override needsTypeConversion(value: AnyValue): boolean {
        return typeof value === 'string' && ['true', 'false'].includes(value.toLowerCase());
    }

    // mapInputValue makes it possible to use strings 'true' and 'false' in a payload
    // eslint-disable-next-line class-methods-use-this
    override mapInputValue(value: AnyValue): AnyValue {
        if (!value) return false;
        const stringValue = value as string;
        return stringValue.toLowerCase() === 'true';
    }
}
