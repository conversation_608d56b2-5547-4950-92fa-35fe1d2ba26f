import { LogicError, SecurityError } from '@sage/xtrem-shared';
import {
    BinaryStreamProperty,
    BooleanProperty,
    CollectionProperty,
    DateProperty,
    DateRangeProperty,
    DatetimeProperty,
    DatetimeRangeProperty,
    DecimalProperty,
    DecimalRangeProperty,
    DoubleProperty,
    EnumArrayProperty,
    EnumProperty,
    FloatProperty,
    IntegerArrayProperty,
    IntegerProperty,
    IntegerRangeProperty,
    JsonProperty,
    ReferenceProperty,
    ShortProperty,
    StringArrayProperty,
    StringProperty,
    TextStreamProperty,
    TimeProperty,
    UuidProperty,
} from '.';
import { Package } from '../application';
import {
    BinaryStreamPropertyDecorator,
    BooleanPropertyDecorator,
    CollectionPropertyDecorator,
    DatePropertyDecorator,
    DateRangePropertyDecorator,
    DatetimePropertyDecorator,
    DatetimeRangePropertyDecorator,
    DecimalPropertyDecorator,
    DecimalRangePropertyDecorator,
    DoublePropertyDecorator,
    FloatPropertyDecorator,
    IntegerArrayPropertyDecorator,
    IntegerPropertyDecorator,
    IntegerRangePropertyDecorator,
    JsonPropertyDecorator,
    ReferenceArrayPropertyDecorator,
    ReferencePropertyDecorator,
    ShortPropertyDecorator,
    StringArrayPropertyDecorator,
    StringPropertyDecorator,
    TextStreamPropertyDecorator,
    TimePropertyDecorator,
    TypedPropertyDecorator,
    UuidPropertyDecorator,
} from '../decorators';
import {
    EnumArrayPropertyDecorator,
    EnumPropertyDecorator,
} from '../decorators/property-decorators/enum-property-decorators';
import { NodeFactory } from '../runtime/node-factory';
import { JsonReferenceProperty, ReferenceArrayProperty } from './foreign-node-property';
import { Property } from './property';

const propertyNamesBlacklist = ['prototype', 'constructor'];

export function createProperty(
    factory: NodeFactory,
    propertyDecorator: TypedPropertyDecorator,
    definingPackage: Package,
): Property {
    // Do not authorized:
    //  - property name with included in the blacklist
    //  - property name starting with '#'
    //  - property name starting with more than 2 underscores (like __proto__)
    //    This is to prevent from prototype pollution when we use property name as key for other objects
    if (
        propertyDecorator.name &&
        (/^(#|__)/.test(propertyDecorator.name) || propertyNamesBlacklist.includes(propertyDecorator.name))
    ) {
        throw new SecurityError(`Unauthorized property name '${propertyDecorator.name}'`);
    }

    switch (propertyDecorator.type) {
        case 'boolean':
            return new BooleanProperty(factory, propertyDecorator as BooleanPropertyDecorator, definingPackage);
        case 'enum':
            return new EnumProperty(factory, propertyDecorator as EnumPropertyDecorator, definingPackage);
        case 'enumArray':
            return new EnumArrayProperty(factory, propertyDecorator as EnumArrayPropertyDecorator, definingPackage);
        case 'short':
            return new ShortProperty(factory, propertyDecorator as ShortPropertyDecorator, definingPackage);
        case 'integer':
            return new IntegerProperty(factory, propertyDecorator as IntegerPropertyDecorator, definingPackage);
        case 'integerRange':
            return new IntegerRangeProperty(
                factory,
                propertyDecorator as IntegerRangePropertyDecorator,
                definingPackage,
            );
        case 'integerArray':
            return new IntegerArrayProperty(
                factory,
                propertyDecorator as IntegerArrayPropertyDecorator,
                definingPackage,
            );
        case 'decimalRange':
            return new DecimalRangeProperty(
                factory,
                propertyDecorator as DecimalRangePropertyDecorator,
                definingPackage,
            );
        case 'date':
            return new DateProperty(factory, propertyDecorator as DatePropertyDecorator, definingPackage);
        case 'dateRange':
            return new DateRangeProperty(factory, propertyDecorator as DateRangePropertyDecorator, definingPackage);
        case 'datetimeRange':
            return new DatetimeRangeProperty(
                factory,
                propertyDecorator as DatetimeRangePropertyDecorator,
                definingPackage,
            );
        case 'datetime':
            return new DatetimeProperty(factory, propertyDecorator as DatetimePropertyDecorator, definingPackage);
        case 'time':
            return new TimeProperty(factory, propertyDecorator as TimePropertyDecorator, definingPackage);
        case 'float':
            return new FloatProperty(factory, propertyDecorator as FloatPropertyDecorator, definingPackage);
        case 'decimal':
            return new DecimalProperty(factory, propertyDecorator as DecimalPropertyDecorator, definingPackage);
        case 'double':
            return new DoubleProperty(factory, propertyDecorator as DoublePropertyDecorator, definingPackage);
        case 'string':
            return new StringProperty(factory, propertyDecorator as StringPropertyDecorator, definingPackage);
        case 'stringArray':
            return new StringArrayProperty(factory, propertyDecorator as StringArrayPropertyDecorator, definingPackage);
        case 'uuid':
            return new UuidProperty(factory, propertyDecorator as UuidPropertyDecorator, definingPackage);
        case 'reference': {
            const referenceProperty = propertyDecorator as ReferencePropertyDecorator;
            const referenceFactory = factory.application.getFactoryByConstructor(referenceProperty.node());
            const isJson = factory.storage === 'json' || referenceFactory.storage === 'json';
            if (isJson) return new JsonReferenceProperty(factory, referenceProperty, definingPackage);
            return new ReferenceProperty(factory, referenceProperty, definingPackage);
        }
        case 'referenceArray': {
            const referenceArrayProperty = propertyDecorator as ReferenceArrayPropertyDecorator;
            return new ReferenceArrayProperty(factory, referenceArrayProperty, definingPackage);
        }
        case 'collection':
            return new CollectionProperty(factory, propertyDecorator as CollectionPropertyDecorator, definingPackage);
        case 'jsonReference':
            return new JsonReferenceProperty(factory, propertyDecorator as ReferencePropertyDecorator, definingPackage);
        case 'json':
            return new JsonProperty(factory, propertyDecorator as JsonPropertyDecorator, definingPackage);
        case 'binaryStream':
            return new BinaryStreamProperty(
                factory,
                propertyDecorator as BinaryStreamPropertyDecorator,
                definingPackage,
            );
        case 'textStream':
            return new TextStreamProperty(factory, propertyDecorator as TextStreamPropertyDecorator, definingPackage);
        case 'byte':
        case 'binary':
        case 'instance':
        default:
            break;
    }
    throw new LogicError(`${propertyDecorator.name}: couldn't create a property with type ${propertyDecorator.type}`);
}
