import { AnyValue } from '@sage/xtrem-shared';
import { Package } from '../application';
import { UuidPropertyDecorator } from '../decorators';
import { NodeFactory } from '../runtime';
import { Uuid } from '../types';
import { Property } from './property';

export class UuidProperty extends Property {
    constructor(
        factory: NodeFactory,
        protected override _decorator: UuidPropertyDecorator,
        definingPackage: Package,
    ) {
        super(factory, _decorator, definingPackage);
    }

    // eslint-disable-next-line class-methods-use-this
    isValueTypeValid(value: AnyValue): boolean {
        return value instanceof Uuid;
    }
}
