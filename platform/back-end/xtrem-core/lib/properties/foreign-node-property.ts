import { AnyR<PERSON>ord, AnyValue, AsyncResponse } from '@sage/xtrem-async-helper';
import { ColumnTypeName, LogicError, isColumnTypeName, removeCodeCoverageInstrumentation } from '@sage/xtrem-shared';
import * as _ from 'lodash';
import { Package } from '../application';
import {
    CollectionPropertyDecorator,
    CollectionPropertyOverrideDecorator,
    ReferenceArrayPropertyDecorator,
    ReferencePropertyDecorator,
    ReferencePropertyOverrideDecorator,
    TypeName,
    useDefaultValue,
} from '../decorators';
import { chainArrays, chainEvents, chainFilters } from '../decorators/decorator-utils';
import { loggers } from '../runtime/loggers';
import { NodeFactory } from '../runtime/node-factory';
import { Node, NodeQueryFilter, ValidationContext, Validator } from '../ts-api';
import { InternalPropertyJoin } from '../types';
import { Property, overridableAttributes } from './property';

const logger = loggers.property;

export abstract class ForeignNodeProperty extends Property {
    #targetFactory: NodeFactory;

    constructor(
        factory: NodeFactory,
        protected override _decorator: ReferencePropertyDecorator | CollectionPropertyDecorator,
        definingPackage: Package,
    ) {
        super(factory, _decorator, definingPackage);
    }

    abstract override get join(): InternalPropertyJoin<Node> | undefined;

    override get decorator(): ReferencePropertyDecorator | CollectionPropertyDecorator {
        return this._decorator;
    }

    override verify(): void {
        super.verify();

        const node = this._factoryDecorators.node;

        if (node.isSharedByAllTenants && !this.targetFactory.isSharedByAllTenants) {
            throw this.systemError(`A ${this.type} property of a shared node cannot be linked to a non-shared node.`);
        }

        if (this.isVital && this.isReferenceArrayProperty()) {
            throw this.systemError(`A ${this.type} cannot be vital`);
        }

        if (
            this.decorator.isMutable &&
            this.factory.storage !== 'external' &&
            !this.targetFactory.isContentAddressable &&
            !this.targetFactory.isAssociationChild &&
            !this.isSystemProperty
        )
            throw this.systemError(
                'A mutable property is only allowed on nodes with external storage or on content addressable nodes.',
            );

        if (this.decorator.isMutable && this.isReferenceArrayProperty()) {
            throw this.systemError(`A ${this.type} cannot be mutable`);
        }

        if (this.isVital && this.isTransientInput)
            throw this.systemError('A vital property cannot be a transient input');

        if (this.isVital && this.decorator.isMutable)
            throw this.systemError('Remove the isMutable attribute, as it is implied by isVital.');

        if (this.targetFactory.isClearedByReset && this.isClearedByReset)
            throw this.systemError('A property can only be tagged isClearedByReset if its target node is not tagged');

        if (this.targetFactory.storage === 'json') {
            if (!this.isVital) throw this.systemError('A reference property to a json node must be vital');
            const jsonProperties = this.targetFactory.properties;
            const forbiddenProp = jsonProperties.find(property => !isColumnTypeName(property.type));
            if (forbiddenProp)
                throw this.systemError(`${forbiddenProp.name}: json nodes can only have scalar properties`);
        } else if (this.isVital) {
            if (!this.reverseReference)
                throw this.systemError("A vital property must have a 'reverseReference' attribute");
            if (this.isStored) throw this.systemError("A vital property cannot have 'isStored' set to true");

            if (this instanceof ReferenceProperty && !this.targetFactory.isVitalReferenceChild)
                throw this.systemError('The referenced class does not have an isVitalReferenceChild attribute');
            if (this instanceof CollectionProperty && !this.targetFactory.isVitalCollectionChild)
                throw this.systemError('The referenced class does not have an isVitalCollectionChild attribute');

            const vitalReferences = this.targetFactory.properties.filter(
                property => property.isReferenceProperty() && (property.isVitalParent || property.isAssociationParent),
            ) as ReferenceProperty[];

            if (vitalReferences.length === 0) {
                throw this.targetFactory.systemError('No vital parent reference');
            } else if (vitalReferences.length > 1) {
                if (vitalReferences.filter(property => property.isVitalParent).length > 1)
                    throw this.targetFactory.systemError('Too many vital parent references');
            } else {
                const vitalReference = vitalReferences[0];
                if (vitalReference.isNullable === true)
                    throw this.systemError('This parent reference must be non nullable');
                if (vitalReference.name !== this.reverseReference)
                    throw this.systemError(
                        `The reverseReference attribute doesn't match the ${this.targetFactory.name}'s property flagged with isVitalParent`,
                    );

                if (!this.factory.isAssignableTo(vitalReference.targetFactory)) {
                    throw this.systemError(
                        `${this.factory.name} is not assignable to ${vitalReference.targetFactory.name}, the parent of ${this.targetFactory.name}`,
                    );
                }
            }

            const reverseReference = this.targetFactory.findProperty(this.reverseReference);
            if (!reverseReference.isVitalParent && !reverseReference.isAssociationParent)
                throw this.systemError(
                    "The 'reverseReference' attribute must point to the property flagged with 'isVitalParent' or 'isAssociationParent' in the target child node",
                );
        } else if (this.isAssociation) {
            if (!this.reverseReference)
                throw this.systemError("An association property must have a 'reverseReference' attribute");

            if (this.isStored) throw this.systemError("An association property cannot have 'isStored' set to true");

            if (!this.targetFactory.isAssociationChild)
                throw this.systemError(
                    'The referenced class does not have an isAssociationCollectionChild or isAssociationReferenceChild attribute',
                );

            const reverseReference = this.targetFactory.findProperty(this.reverseReference);
            if (!reverseReference.isAssociationParent)
                throw this.systemError(
                    "The 'reverseReference' attribute must point to the property flagged with 'isAssociationParent' in the target child node",
                );
        }

        if (this.isInherited && !this.isSystemProperty) {
            const baseProperty = this.factory.baseFactory?.findProperty(this.name);
            if (!baseProperty?.isForeignNodeProperty())
                throw this.logicError('invalid override: mismatch with property in super class');
            if (!this.targetFactory.isAssignableTo(baseProperty.targetFactory)) {
                throw new LogicError(
                    `${this.fullName}: invalid override: '${this.targetFactory.name}' is not a subclass of '${baseProperty.targetFactory.name}'`,
                );
            }
        }
    }

    needsJoin(): boolean {
        return !this.getValue && !this.computeValue && this.targetFactory.storage !== 'json';
    }

    verifyJoin(): void {
        if (this.needsJoin()) {
            if (!this.join) throw this.systemError('join missing');
            if (this.join) Object.keys(this.join).forEach(k => this.targetFactory.findProperty(k));
        }
    }

    override get reverseReference(): string | undefined {
        return this._decorator.reverseReference as string | undefined;
    }

    override get isVital(): boolean {
        return !!this._decorator.isVital;
    }

    get isAssociation(): boolean {
        return !!this.decorator.isAssociation;
    }

    override get isMutable(): boolean {
        return !!this._decorator.isMutable || this.isVital || this.isAssociation;
    }

    get targetFactory(): NodeFactory {
        if (this.#targetFactory) return this.#targetFactory;
        const node = this._decorator.node();
        this.#targetFactory = this.factory.application.getFactoryByConstructor(node);
        return this.#targetFactory;
    }

    get node(): any {
        const node = this._decorator.node();
        if (!node)
            throw this.systemError(`node() returns ${node}, probably caused by a circular dependency during require`);
        return node;
    }

    override overrideAttributes(
        extension: ReferencePropertyOverrideDecorator | CollectionPropertyOverrideDecorator,
    ): void {
        super.overrideAttributes(extension);
        if (extension.node) {
            this.#targetFactory = this.factory.application.getFactoryByConstructor(extension.node());
        }
    }

    /**
     * Creates a filter for the reverse reference and adds the constructor condition if needed.
     * @returns
     */
    protected getReverseReferenceFilter(): InternalPropertyJoin<Node> {
        const reverseReference = this.reverseReference as string;

        return { [reverseReference]: '_id' } as unknown as InternalPropertyJoin<Node>;
    }
}

/** @internal */
export const referenceOverridableAttributes = [...overridableAttributes, 'filters'];

export class ReferenceProperty extends ForeignNodeProperty {
    private _isSelfReference: boolean;

    #filters: any;

    constructor(
        factory: NodeFactory,
        protected override _decorator: ReferencePropertyDecorator,
        definingPackage: Package,
    ) {
        super(factory, _decorator, definingPackage);
        this.#filters = this._decorator.filters;
    }

    override get decorator(): ReferencePropertyDecorator {
        return this._decorator;
    }

    override get isVitalParent(): boolean {
        return !!this._decorator.isVitalParent;
    }

    get isVitalParentInput(): boolean {
        return !!this._decorator.isVitalParentInput;
    }

    get canLookup(): boolean {
        return this._decorator.canLookup !== false;
    }

    override get isAssociationParent(): boolean {
        return !!this._decorator.isAssociationParent;
    }

    get isSelfReference(): boolean {
        // A node may have a non nullable self reference if its default value is `{ return this; }`
        if (this._isSelfReference === undefined) {
            this._isSelfReference =
                !this._decorator.isNullable &&
                typeof this._decorator.defaultValue === 'function' &&
                removeCodeCoverageInstrumentation(this._decorator.defaultValue.toString())
                    .replace(/\s+/g, ' ')
                    .replace('{return this;}', '{ return this; }')
                    .replace('{return this}', '{ return this; }')
                    .endsWith('{ return this; }');
        }

        return this._isSelfReference;
    }

    // vital parent is implicitly required
    override get isRequired(): boolean {
        return super.isRequired || !!this._decorator.isVitalParent || !!this._decorator.isAssociationParent;
    }

    private verifyExternalStorage(): void {
        if (this.isStored) {
            if (this._factory.storage !== 'external')
                throw this.systemError(
                    'A reference property linked to an external storage node cannot have the isStored attribute set to true, if the parent node is not an external storage node.',
                );
        }
    }

    /**
     * @returns `true` if this property is a reference that is not nullable or explicitly allowed in indexes
     * by the `allowedInUniqueIndex` attribute and that is not a self reference
     */
    get isHardDependency(): boolean {
        if (this.isSelfReference) return false;
        if (!this.isNullable) return true;
        if (this.allowedInUniqueIndex) return true;
        return false;
    }

    get isToposortDependency(): boolean {
        if (this._decorator.ignoreInToposort) return false;
        if (this.isHardDependency) return true;
        if (this.targetFactory.isSetupNode) return true;
        return false;
    }

    get ignoreIsActive(): boolean | ((this: Node) => AsyncResponse<boolean>) | undefined {
        return this._decorator.ignoreIsActive;
    }

    override verifyDelegatedToProperty(toProperty: Property): void {
        super.verifyDelegatedToProperty(toProperty);

        if (!toProperty.isForeignNodeProperty()) {
            throw this.logicError('delegated property is not a foreign node property');
        }
        if (toProperty.node !== this.node) {
            throw this.logicError(
                `Invalid delegatesTo decorator attribute: target node mispatch, expected ${this.node.name}, got ${toProperty.node.name}`,
            );
        }
    }

    override verify(): void {
        super.verify();
        const factory = this._factoryDecorators;
        if (
            (factory.node.tableName || factory.node.name) &&
            !this.isMutable &&
            !this.isStored &&
            !this.isTransientInput &&
            this.delegatesTo == null &&
            this.getValue == null &&
            this.computeValue == null &&
            this.decorator.join == null &&
            this.isPublished
        )
            throw this.systemError(
                "A published property needs either 'isStored', 'isVital', 'isMutable', 'isTransientInput', 'computeValue', 'getValue' or 'join' decorator member to be set.",
            );

        if (!(this.isVital || this.isAssociation) && this.reverseReference)
            throw this.systemError("Non vital reference properties do not have any 'reverseReference' attribute.");

        if (this.isVital && this.targetFactory.isContentAddressable)
            throw this.systemError(
                'isVital is forbidden on reference to a content addressable node, use isMutable instead',
            );

        if (this.targetFactory.storage === 'external') this.verifyExternalStorage();

        // Check for 'default' duplicatedValue for non-nullable reference property with no defaultValue
        if (
            this.factory.canCreate &&
            !this.isNullable &&
            this.duplicatedValue === useDefaultValue &&
            !this.defaultValue
        ) {
            throw this.systemError(
                'Non-nullable reference properties with duplicatedValue: useDefaultValue need a defaultValue.',
            );
        }
        if (this._decorator.ignoreInToposort) {
            if (!this.targetFactory.isSetupNode || !this.isNullable) {
                throw this.systemError(
                    'ignoreInToposort attribute can only be set on a nullable reference to a setup node',
                );
            }
        }
    }

    get join(): InternalPropertyJoin<Node> | undefined {
        switch (this.targetFactory.storage) {
            case 'sql':
                if (this.isVital || this.isAssociation) {
                    return this.getReverseReferenceFilter();
                }
                // If the decorator has an explicit join attribute, return it
                if (this.decorator.join) return this.decorator.join as InternalPropertyJoin<Node>;
                // Join must between this column and the targetFactory's _id
                return { _id: this.name } as unknown as InternalPropertyJoin<Node>;

            case 'external':
                return this._factoryDecorators.node.externalStorageManager?.getReferenceJoin(this.name);

            default:
                return undefined;
        }
    }

    override get canTranslateToSql(): boolean {
        return super.canTranslateToSql || !!this.decorator.join;
    }

    override get columnType(): ColumnTypeName | undefined {
        if (this._factory.storage !== 'sql' || this.isVital) return super.columnType;
        return 'integer';
    }

    get filters(): ReferencePropertyDecorator['filters'] {
        return this.#filters;
    }

    // eslint-disable-next-line class-methods-use-this
    isValueTypeValid(value: AnyValue): boolean {
        return _.isInteger(value) || typeof value === 'string' || typeof value === 'object';
    }

    getTargetIsActivePropertyName(): string | undefined {
        /** Should not get isActive for a parent reference */
        return this.isVitalParent || this.isAssociationParent
            ? undefined
            : this.targetFactory.properties.find(prop => prop.provides?.includes('isActive'))?.name;
    }

    override addExtension(extension: ReferencePropertyOverrideDecorator): void {
        this.verifyExtensionKeys(Object.keys(extension), referenceOverridableAttributes);
        this.overrideAttributes(extension);
    }

    override overrideAttributes(extension: ReferencePropertyOverrideDecorator): void {
        super.overrideAttributes(extension);
        if (!extension.filters) return;
        try {
            const filters = this.#filters;
            const extensionFilters = extension.filters;
            this.#filters = {
                control: chainFilters(filters?.control, extensionFilters?.control),
                lookup: chainFilters(filters?.lookup, extensionFilters?.lookup),
                controls: chainArrays(filters?.controls, extensionFilters?.controls),
            };
        } catch (e) {
            throw this.systemError(e.message);
        }
    }
}

export class JsonReferenceProperty extends ReferenceProperty {
    constructor(
        factory: NodeFactory,
        protected override _decorator: ReferencePropertyDecorator,
        definingPackage: Package,
    ) {
        super(factory, _decorator, definingPackage);
    }

    // eslint-disable-next-line class-methods-use-this
    override get type(): TypeName {
        return 'jsonReference';
    }

    // eslint-disable-next-line class-methods-use-this
    override get columnType(): ColumnTypeName | undefined {
        return 'json';
    }
}

export class ReferenceArrayProperty extends ReferenceProperty {
    constructor(
        factory: NodeFactory,
        protected override _decorator: ReferenceArrayPropertyDecorator,
        definingPackage: Package,
    ) {
        super(factory, _decorator, definingPackage);
    }

    // eslint-disable-next-line class-methods-use-this
    override get type(): TypeName {
        return 'referenceArray';
    }

    // eslint-disable-next-line class-methods-use-this
    override get columnType(): ColumnTypeName | undefined {
        return 'referenceArray';
    }

    // eslint-disable-next-line class-methods-use-this
    override isValueTypeValid(value: AnyValue): boolean {
        return (
            Array.isArray(value) && value.every(v => _.isInteger(v) || typeof v === 'string' || typeof v === 'object')
        );
    }

    override needsTypeConversion(value: AnyValue): boolean {
        if (typeof value === 'string') {
            if (!value.length) return true;

            if (value.startsWith('[') && value.endsWith(']')) {
                return Array.isArray(this.mapInputValue(value));
            }
        }
        return false;
    }

    // eslint-disable-next-line class-methods-use-this
    override mapInputValue(value: AnyValue): AnyValue {
        const stringValue = value as string;
        if (!stringValue || !stringValue.length) return [] as string[];
        return stringValue
            .slice(1, stringValue.length - 1)
            .split(',')
            .map(token =>
                token
                    .replace(/^'(.*)'$/, '$1') // strip heading and trailing simple quotes
                    .replace(/^"(.*)"$/, '$1') // strip heading and trailing double quotes
                    .trim(),
            );
    }
}

/** @internal */
export const collectionOverridableAttributes = [
    ...overridableAttributes,
    'controlBegin',
    'controlEnd',
    'saveBegin',
    'saveEnd',
];

export class CollectionProperty extends ForeignNodeProperty {
    private _prepareBegin?: Validator<Node>;

    private _prepareEnd?: Validator<Node>;

    private _controlBegin?: Validator<Node>;

    private _controlEnd?: Validator<Node>;

    private _saveBegin?: (this: Node) => void;

    private _saveEnd?: (this: Node) => void;

    constructor(
        factory: NodeFactory,
        protected override _decorator: CollectionPropertyDecorator,
        definingPackage: Package,
    ) {
        super(factory, _decorator, definingPackage);
    }

    override get decorator(): CollectionPropertyDecorator {
        return this._decorator;
    }

    override verify(): void {
        super.verify();

        if (this._decorator.orderBy && this.isVital)
            throw this.systemError('orderBy attribute is forbidden on vital collections');

        if (this.targetFactory.isContentAddressable)
            throw this.systemError('Collections of content addressable nodes are not supported');

        if (this.reverseReference) {
            // The type of a reverseReference must be a reference
            const reverseProp = this.targetFactory.findProperty(this.reverseReference);
            if (!['reference', 'referenceArray', 'jsonReference'].includes(reverseProp.type)) {
                throw this.systemError(
                    `Invalid 'reverseReference' decorator ('${this.targetFactory.name}.${this.reverseReference}' is not a reference)`,
                );
            }
        }

        if (this.targetFactory.storage === 'external') this.verifyExternalStorage();

        if (this.getValue) logger.warn(`${this.fullName}: getValue is deprecated on collections (PERF KILLER)`);
        if (this.computeValue) logger.warn(`${this.fullName}: computeValue is deprecated on collections (PERF KILLER)`);
    }

    override addExtension(extension: CollectionPropertyOverrideDecorator): void {
        this.verifyExtensionKeys(Object.keys(extension), collectionOverridableAttributes);
        this.overrideAttributes(extension);
    }

    override overrideAttributes(extension: CollectionPropertyOverrideDecorator): void {
        super.overrideAttributes(extension);
        const extensionKeys = Object.keys(extension);
        extensionKeys.forEach(key => {
            switch (key) {
                case 'controlBegin':
                case 'controlEnd':
                case 'saveBegin':
                case 'saveEnd':
                    try {
                        this[key] = chainEvents(this[key], extension[key], key);
                    } catch (e) {
                        throw this.systemError(e.message);
                    }
                    break;
                default:
                    break;
            }
        });
    }

    private verifyExternalStorage(): void {
        const findIndex = (propertyName: string): number => {
            const index = this._factory.properties.findIndex(prop => prop.name === propertyName);
            if (index < 0) throw this.systemError(`invalid join: property not found: ${propertyName}`);
            return index;
        };
        const thisIndex = findIndex(this.name);
        Object.values(this.join || {})
            .filter(value => typeof value === 'string')
            .forEach((name: string) => {
                const joinedIndex = findIndex(name);
                if (joinedIndex === thisIndex)
                    throw this.systemError('invalid join: collection property cannot join to itself');
                if (joinedIndex > thisIndex)
                    throw this.systemError(
                        `missing property dependency: add "dependsOn: ['${this._factory.properties[joinedIndex].name}']" to property '${this.name}'`,
                    );
            });
    }

    get join(): InternalPropertyJoin<Node> | undefined {
        switch (this._factory.storage) {
            case 'sql':
                if (this.reverseReference) {
                    return this.getReverseReferenceFilter();
                }
                if (this.decorator.join) return this.decorator.join as InternalPropertyJoin<Node>;
                return undefined;

            case 'external':
                return this._factoryDecorators.node.externalStorageManager!.getCollectionJoin(this.name);

            default:
                return undefined;
        }
    }

    get orderBy(): AnyRecord | undefined {
        // If the target factory is an association child, there is no _sortValue property on the association node
        if (this._factory.storage === 'sql' && this.isVital && !this.targetFactory.isAssociationChild)
            return { _sortValue: 1 };
        return this._decorator.orderBy;
    }

    get prepareBegin(): ((this: Node, cx: ValidationContext) => AsyncResponse<void>) | undefined {
        // TODO: compat code for obsolete prepare decorator - remove later
        if (
            this._decorator.prepareBegin &&
            this._decorator.prepare &&
            this._decorator.prepare !== this._decorator.prepareBegin
        ) {
            throw this.logicError('prepare and prepareBegin are mutually exclusive');
        }
        this._decorator.prepareBegin = this._decorator.prepareBegin || this._decorator.prepare;

        return this._prepareBegin === undefined ? this._decorator.prepareBegin : this._prepareBegin;
    }

    set prepareBegin(prepareBegin: ((this: Node, cx: ValidationContext) => AsyncResponse<void>) | undefined) {
        this._prepareBegin = prepareBegin;
    }

    get prepareEnd(): ((this: Node, cx: ValidationContext) => AsyncResponse<void>) | undefined {
        return this._prepareEnd === undefined ? this._decorator.prepareEnd : this._prepareEnd;
    }

    set prepareEnd(prepareEnd: ((this: Node, cx: ValidationContext) => AsyncResponse<void>) | undefined) {
        this._prepareEnd = prepareEnd;
    }

    get controlBegin(): ((this: Node, cx: ValidationContext) => AsyncResponse<void>) | undefined {
        return this._controlBegin === undefined ? this._decorator.controlBegin : this._controlBegin;
    }

    set controlBegin(controlBegin: ((this: Node, cx: ValidationContext) => AsyncResponse<void>) | undefined) {
        this._controlBegin = controlBegin;
    }

    get controlEnd(): ((this: Node, cx: ValidationContext) => AsyncResponse<void>) | undefined {
        return this._controlEnd === undefined ? this._decorator.controlEnd : this._controlEnd;
    }

    set controlEnd(controlEnd: ((this: Node, cx: ValidationContext) => AsyncResponse<void>) | undefined) {
        this._controlEnd = controlEnd;
    }

    get saveBegin(): ((this: Node) => void) | undefined {
        return this._saveBegin === undefined ? this._decorator.saveBegin : this._saveBegin;
    }

    set saveBegin(saveBegin: ((this: Node) => void) | undefined) {
        this._saveBegin = saveBegin;
    }

    get saveEnd(): ((this: Node) => void) | undefined {
        return this._saveEnd === undefined ? this._decorator.saveEnd : this._saveEnd;
    }

    set saveEnd(saveEnd: ((this: Node) => void) | undefined) {
        this._saveEnd = saveEnd;
    }

    get getFilter(): ((this: Node) => AsyncResponse<NodeQueryFilter<Node>>) | undefined {
        return this._decorator.getFilter;
    }

    override needsJoin(): boolean {
        return super.needsJoin() && !this.getFilter;
    }

    get forceFullSave(): boolean {
        return !!this._decorator.forceFullSave;
    }

    // eslint-disable-next-line class-methods-use-this
    isValueTypeValid(value: AnyValue): boolean {
        return Array.isArray(value);
    }
}
