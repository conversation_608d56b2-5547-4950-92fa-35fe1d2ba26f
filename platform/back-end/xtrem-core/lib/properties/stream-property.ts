import { AnyValue } from '@sage/xtrem-async-helper';
import { ConfigManager } from '@sage/xtrem-config';
import { Package } from '../application';
import { BinaryStreamPropertyDecorator, StreamPropertyDecorator, TextStreamPropertyDecorator } from '../decorators';
import { NodeFactory } from '../runtime';
import { BinaryStream, BinaryStreamDataType, TextStream, TextStreamDataType } from '../types';
import { Property } from './property';

export abstract class StreamProperty extends Property {
    constructor(
        factory: NodeFactory,
        protected override _decorator: StreamPropertyDecorator,
        definingPackage: Package,
    ) {
        super(factory, _decorator, definingPackage);
    }

    /**
     * Returns whether this property should be lazy loaded
     */
    override get shouldLazyLoad(): boolean {
        if (ConfigManager.current.storage?.managedExternal) {
            // No lazy loading for X3
            return false;
        }
        return !this._decorator.noLazyLoad;
    }
}

export class BinaryStreamProperty extends StreamProperty {
    constructor(
        factory: NodeFactory,
        protected override _decorator: BinaryStreamPropertyDecorator,
        definingPackage: Package,
    ) {
        super(factory, _decorator, definingPackage);
    }

    // eslint-disable-next-line class-methods-use-this
    isValueTypeValid(value: AnyValue): boolean {
        return value instanceof BinaryStream;
    }

    // eslint-disable-next-line class-methods-use-this
    override needsTypeConversion(value: AnyValue): boolean {
        return typeof value === 'string';
    }

    // eslint-disable-next-line class-methods-use-this
    override mapInputValue(value: AnyValue): AnyValue {
        return BinaryStream.fromBuffer(Buffer.from(value as string, 'base64'));
    }

    override get dataType(): BinaryStreamDataType {
        return super.dataType as BinaryStreamDataType;
    }
}

export class TextStreamProperty extends StreamProperty {
    constructor(
        factory: NodeFactory,
        protected override _decorator: TextStreamPropertyDecorator,
        definingPackage: Package,
    ) {
        super(factory, _decorator, definingPackage);
    }

    // eslint-disable-next-line class-methods-use-this
    isValueTypeValid(value: AnyValue): boolean {
        return value instanceof TextStream;
    }

    // eslint-disable-next-line class-methods-use-this
    override needsTypeConversion(value: AnyValue): boolean {
        return typeof value === 'string';
    }

    // eslint-disable-next-line class-methods-use-this
    override mapInputValue(value: AnyValue): AnyValue {
        return new TextStream(value as string);
    }

    override verify(): void {
        super.verify();
        if (this.isNullable) throw this.systemError('A textStream property cannot be nullable');
    }

    override get dataType(): TextStreamDataType {
        return (super.dataType as TextStreamDataType) || TextStreamDataType.default;
    }

    /**
     * Returns whether this property should be lazy loaded
     */
    override get shouldLazyLoad(): boolean {
        if (
            ConfigManager.current.storage?.managedExternal ||
            this.dataType.maxLength <= (ConfigManager.current.textStreamLazyLoadLength ?? 3000)
        ) {
            // No lazy loading for X3
            return false;
        }
        return super.shouldLazyLoad;
    }
}
