import { AnyValue } from '@sage/xtrem-async-helper';
import { DecimalRange, IntegerRange } from '@sage/xtrem-date-time';
import { Decimal } from '@sage/xtrem-decimal';
import * as _ from 'lodash';
import { Package } from '../application';
import {
    DecimalPropertyDecorator,
    DecimalRangePropertyDecorator,
    DoublePropertyDecorator,
    FloatPropertyDecorator,
    IntegerArrayPropertyDecorator,
    IntegerPropertyDecorator,
    IntegerRangePropertyDecorator,
    NumberPropertyDecorator,
    ShortPropertyDecorator,
} from '../decorators';
import { NodeFactory } from '../runtime';
import { Property } from './property';
import { isArrayOfStrings, stringToArrayOfStrings } from './util';

export abstract class NumberProperty extends Property {
    constructor(
        factory: NodeFactory,
        protected override _decorator: NumberPropertyDecorator,
        definingPackage: Package,
    ) {
        super(factory, _decorator, definingPackage);
    }

    override get isNotZero(): boolean {
        if (this.delegatesTo) return this.getDelegatingInfo().childProperty.isNotZero;
        return !!this._decorator.isNotZero;
    }
}

const isValueConvertibleToInteger = (value: AnyValue): boolean => typeof value === 'string' && /^[+-]?\d+$/.test(value);

export class IntegerProperty extends NumberProperty {
    constructor(
        factory: NodeFactory,
        protected override _decorator: IntegerPropertyDecorator,
        definingPackage: Package,
    ) {
        super(factory, _decorator, definingPackage);
    }

    // eslint-disable-next-line class-methods-use-this
    isValueTypeValid(value: AnyValue): boolean {
        return _.isInteger(value) || (this.name === '_id' && typeof value === 'string');
    }

    // eslint-disable-next-line class-methods-use-this
    override needsTypeConversion(value: AnyValue): boolean {
        return isValueConvertibleToInteger(value);
    }

    // eslint-disable-next-line class-methods-use-this
    override mapInputValue(value: AnyValue): AnyValue {
        return parseInt(value as string, 10);
    }
}

export class IntegerArrayProperty extends Property {
    constructor(
        factory: NodeFactory,
        protected override _decorator: IntegerArrayPropertyDecorator,
        definingPackage: Package,
    ) {
        super(factory, _decorator, definingPackage);
    }

    // eslint-disable-next-line class-methods-use-this
    isValueTypeValid(value: AnyValue): boolean {
        return Array.isArray(value) && value.every(v => _.isInteger(v));
    }

    // eslint-disable-next-line class-methods-use-this
    override needsTypeConversion(value: AnyValue): boolean {
        return (
            isArrayOfStrings(value as string) &&
            stringToArrayOfStrings(value as string).every(v => isValueConvertibleToInteger(v))
        );
    }

    // eslint-disable-next-line class-methods-use-this
    override mapInputValue(value: AnyValue): AnyValue {
        return stringToArrayOfStrings(value as string).map(v => parseInt(v, 10));
    }
}
export class ShortProperty extends NumberProperty {
    constructor(
        factory: NodeFactory,
        protected override _decorator: ShortPropertyDecorator,
        definingPackage: Package,
    ) {
        super(factory, _decorator, definingPackage);
    }

    // eslint-disable-next-line class-methods-use-this
    isValueTypeValid(value: AnyValue): boolean {
        return _.isInteger(value);
    }

    // eslint-disable-next-line class-methods-use-this
    override needsTypeConversion(value: AnyValue): boolean {
        return isValueConvertibleToInteger(value);
    }

    // eslint-disable-next-line class-methods-use-this
    override mapInputValue(value: AnyValue): AnyValue {
        return parseInt(value as string, 10);
    }
}

export class FloatProperty extends NumberProperty {
    constructor(
        factory: NodeFactory,
        protected override _decorator: FloatPropertyDecorator,
        definingPackage: Package,
    ) {
        super(factory, _decorator, definingPackage);
    }

    // eslint-disable-next-line class-methods-use-this
    isValueTypeValid(value: AnyValue): boolean {
        return typeof value === 'number';
    }
}

export class DoubleProperty extends NumberProperty {
    constructor(
        factory: NodeFactory,
        protected override _decorator: DoublePropertyDecorator,
        definingPackage: Package,
    ) {
        super(factory, _decorator, definingPackage);
    }

    // eslint-disable-next-line class-methods-use-this
    isValueTypeValid(value: AnyValue): boolean {
        return typeof value === 'number';
    }
}

export class DecimalProperty extends NumberProperty {
    constructor(
        factory: NodeFactory,
        protected override _decorator: DecimalPropertyDecorator,
        definingPackage: Package,
    ) {
        super(factory, _decorator, definingPackage);
    }

    // eslint-disable-next-line class-methods-use-this
    isValueTypeValid(value: AnyValue): boolean {
        return typeof value === 'number' || Decimal.isDecimal(value);
    }

    // eslint-disable-next-line class-methods-use-this
    override needsTypeConversion(value: AnyValue): boolean {
        // eslint-disable-next-line @sage/redos/no-vulnerable
        return typeof value === 'string' && /^[+-]?([0-9]+\.?[0-9]*|\.[0-9]+)$/.test(value);
    }

    // eslint-disable-next-line class-methods-use-this
    override mapInputValue(value: AnyValue): AnyValue {
        return Decimal.make(value as string);
    }
}

export class IntegerRangeProperty extends Property {
    constructor(
        factory: NodeFactory,
        protected override _decorator: IntegerRangePropertyDecorator,
        definingPackage: Package,
    ) {
        super(factory, _decorator, definingPackage);
    }

    // eslint-disable-next-line class-methods-use-this
    isValueTypeValid(value: AnyValue): boolean {
        return value instanceof IntegerRange;
    }
}

export class DecimalRangeProperty extends Property {
    constructor(
        factory: NodeFactory,
        protected override _decorator: DecimalRangePropertyDecorator,
        definingPackage: Package,
    ) {
        super(factory, _decorator, definingPackage);
    }

    // eslint-disable-next-line class-methods-use-this
    isValueTypeValid(value: AnyValue): boolean {
        return value instanceof DecimalRange;
    }
}
