export const isString = (value: any): value is string => typeof value === 'string';

export const stringToArrayOfStrings = (value: any): string[] => {
    if (!isArrayOfStrings(value)) {
        return [] as string[];
    }
    const arrayBody = value
        .slice(1, value.length - 1)
        .trimLeft()
        .trimRight();
    if (arrayBody.length === 0) {
        return [] as string[];
    }
    return arrayBody.split(',').map(token =>
        token
            .trim()
            .replace(/^'(.*)'$/, '$1') // strip heading and trailing simple quotes
            .replace(/^"(.*)"$/, '$1'),
    );
};

export const isArrayOfStrings = (value: any): value is string =>
    isString(value) && value.length !== 0 && value.startsWith('[') && value.endsWith(']');
