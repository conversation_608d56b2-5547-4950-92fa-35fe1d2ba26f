import * as stream from 'stream';
import { loggers } from '../runtime/loggers';
import { toMegabytesString } from './s3-helper';

const logger = loggers.dump;

export class MappingTransform extends stream.Transform {
    private static readonly highWaterMark = 10 * 1024 * 1024; // 10MB

    #remaining = Buffer.alloc(0);

    private readonly mappings;

    private done = false;

    private size = 0;

    private totalSize = 0;

    private readonly logSize: number;

    constructor(
        readonly schemaName: string,
        readonly user: string,
        readonly expectedSize?: number,
    ) {
        super({ highWaterMark: MappingTransform.highWaterMark, objectMode: true });
        this.mappings = [
            { reg: /\[SCHEMA_NAME\]/g, val: schemaName },
            { reg: /\[USER_NAME\]/g, val: user },
        ] as { reg: RegExp; val: string }[];
        this.logSize = expectedSize != null ? expectedSize / 10 : 10 * MappingTransform.highWaterMark;
    }

    override _transform(chunk: Buffer, _encoding: any, callback: () => void): void {
        this.size += chunk.length;
        this.totalSize += chunk.length;
        if (this.size > this.logSize) {
            this.size = 0;
            logger.info(
                `Transformed: ${toMegabytesString(this.totalSize)} out of ${toMegabytesString(this.expectedSize ?? 0)} (rss ${toMegabytesString(process.memoryUsage().rss)})`,
            );
        }
        const lastNewlineIndex = chunk.lastIndexOf('\n');
        let buffer = this.#remaining;
        // No complete line, aggregate the chunk and return
        if (lastNewlineIndex === -1) {
            this.#remaining = Buffer.concat([this.#remaining, chunk]);
            callback();
            return;
        }

        // We have a complete line to process
        const beginning = chunk.subarray(0, lastNewlineIndex + 1);
        this.#remaining = chunk.subarray(lastNewlineIndex + 1);
        buffer = Buffer.concat([buffer, beginning]);
        // We have not found the mapping so far
        if (!this.done) {
            const chunkString = buffer.toString('utf8');
            const lines = chunkString.split('\n');
            // eslint-disable-next-line no-restricted-syntax
            for (const line of lines) {
                const userMatch = line.match(/^-- Name: \S+; Type: SCHEMA; Schema: \S+; Owner: (\S+)$/m);
                if (userMatch && this.user !== userMatch[1]) {
                    logger.info(`\t- will remap user '${userMatch[1]}' to '${this.user}'`);
                    this.mappings.push({
                        reg: new RegExp(` OWNER TO ${userMatch[1]};$`, 'gm'),
                        val: ` OWNER TO ${this.user};`,
                    });
                    this.done = true;
                    break;
                }
            }
            // If we have not found the mapping, no replacement is needed in this chunk, push it and return
            if (!this.done) {
                this.pushWithBackPressureGuard(buffer, callback);
                return;
            }
        }

        // We have found the mapping, we need to apply it to the whole buffer converted to string
        let data = buffer.toString('utf8');
        // Apply the replacements
        this.mappings.forEach(({ reg, val }) => {
            data = data.replace(reg, val);
        });
        this.pushWithBackPressureGuard(data, callback);
    }

    private pushWithBackPressureGuard(data: Buffer | string, callback: () => void): void {
        if (!this.push(data)) {
            this.once('drain', callback);
        } else {
            callback();
        }
    }

    override _flush(callback: () => void): void {
        let data = this.#remaining.toString('utf8');
        this.#remaining = Buffer.alloc(0);
        // Apply the replacements
        this.mappings.forEach(({ reg, val }) => {
            data = data.replace(reg, val);
        });
        if (!this.push(data)) {
            this.once('drain', callback);
        } else {
            callback();
        }

        logger.info(`Final transformed: ${toMegabytesString(this.totalSize)}`);
        if (this.expectedSize != null && this.totalSize !== this.expectedSize) {
            logger.warn(`The expected size (${this.expectedSize}) does not match the actual size (${this.totalSize})`);
        }
    }
}
