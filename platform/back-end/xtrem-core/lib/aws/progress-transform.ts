import * as stream from 'stream';
import { loggers } from '../runtime/loggers';
import { toMegabytesString } from './s3-helper';

const logger = loggers.dump;

export class ProgressTransform extends stream.Transform {
    length = 0;

    totalLength = 0;

    constructor(readonly prefix?: string) {
        super();
        this.prefix = prefix ?? 'Downloaded';
    }

    override _transform(chunk: string | any[], _encoding: any, callback: () => void): void {
        this.totalLength += chunk.length;
        this.length += chunk.length;
        this.push(chunk);
        callback();
        // Log every 10MB
        if (this.length > 100 * 1024 * 1024) {
            this.length = 0;
            const rss = process.memoryUsage().rss;
            logger.info(`${this.prefix}: ${toMegabytesString(this.totalLength)} (rss ${toMegabytesString(rss)})`);
        }
    }

    override _flush(callback: () => void): void {
        // replace the first character of the prefix with its lowercase version
        const prefix = this.prefix ? `${this.prefix.charAt(0).toLowerCase()}${this.prefix.substring(1)}` : 'downloaded';

        logger.info(
            `Total ${prefix}: ${toMegabytesString(this.totalLength)} (rss ${toMegabytesString(process.memoryUsage().rss)})`,
        );
        callback();
    }
}
