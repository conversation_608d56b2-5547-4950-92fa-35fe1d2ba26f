import { ConfigManager } from '@sage/xtrem-config';
import * as fs from 'node:fs';
import * as os from 'node:os';
import * as fsp from 'node:path';
import * as stream from 'node:stream';
import * as unzipper from 'unzipper';
import { loggers } from '../runtime/loggers';
import { MappingTransform } from './maping-transform';
import { S3Helper, S3ObjectInfo } from './s3-helper';
import { S3ConfigurationType, S3Manager } from './s3-manager';

type RunRestoreCallback = (envelop: RestoreEnvelop, filenameOrStream: string | stream.Readable) => Promise<void>;

const logger = loggers.dump;

export class RestoreEnvelop {
    #localFullFilename: string;

    #sourceS3Info?: S3ObjectInfo;

    #mayBeAnonymizedData = false;

    constructor(
        readonly s3Manager: S3Manager,
        readonly versionOrS3Uri: string,
        readonly s3ConfigurationType: S3ConfigurationType,
    ) {}

    get mayBeAnonymizedData(): boolean {
        return this.#mayBeAnonymizedData;
    }

    get sourceS3Info(): S3ObjectInfo | undefined {
        return this.#sourceS3Info;
    }

    get isFile(): boolean {
        return this.versionOrS3Uri.startsWith('file://');
    }

    get localFullFilename(): string {
        return this.#localFullFilename;
    }

    get isZip(): boolean {
        return this.#localFullFilename.endsWith('.zip');
    }

    get isDump(): boolean {
        return this.#localFullFilename.endsWith('.gz');
    }

    get isS3(): boolean {
        return this.versionOrS3Uri.startsWith('s3://');
    }

    private async init(): Promise<void> {
        if (this.versionOrS3Uri.startsWith('file://')) {
            this.#localFullFilename = this.versionOrS3Uri.substring(7);
            this.#mayBeAnonymizedData = true;
            return;
        }
        if (this.versionOrS3Uri.startsWith('s3://')) {
            // Restore from a S3 URI
            this.#sourceS3Info = S3Helper.parseS3Uri(this.versionOrS3Uri);
            this.#mayBeAnonymizedData = true;
        } else {
            this.#sourceS3Info = await this.s3Manager.getS3Info(this.s3ConfigurationType, this.versionOrS3Uri);
        }
        const tempFolder = os.tmpdir();

        const localFilename = S3Manager.normalizeApplicationName(this.#sourceS3Info.key);
        this.#localFullFilename = fsp.join(tempFolder, localFilename);
    }

    async restore(runRestore: RunRestoreCallback): Promise<void> {
        await this.init();

        try {
            if (this.#sourceS3Info?.key.endsWith('.zip')) {
                await this.restoreFromS3Zip(runRestore);
                return;
            }
            if (this.isFile && this.#localFullFilename.endsWith('.zip')) {
                // Restore from a local zip file
                await this.restoreFromFileZip(runRestore);
                return;
            }

            // Restore a dump file or S3 object (with .gz extension)
            if (!this.#localFullFilename.endsWith('.gz')) {
                throw new Error(`Invalid dump file type: ${this.#localFullFilename}`);
            }

            let sourceStream: stream.Readable | undefined;
            if (this.isFile) {
                sourceStream = fs.createReadStream(this.#localFullFilename);
            } else {
                if (!this.#sourceS3Info) {
                    throw new Error('No S3 info found');
                }
                sourceStream = await this.downloadDumpFileFromS3ObjectInfo(this.#sourceS3Info);
            }

            if (!sourceStream) {
                throw new Error(`No source stream found for ${this.#localFullFilename}`);
            }

            await runRestore(this, sourceStream);
        } finally {
            if (!this.isFile && fs.existsSync(this.#localFullFilename)) {
                logger.info(`Delete temp file ${this.#localFullFilename}`);
                fs.unlinkSync(this.#localFullFilename);
            }
        }
    }

    /**
     * Download a file from a S3ObjectInfo.
     * The function will return the filename of the written file (short filename, without the folder)
     *
     * @param sourceS3Info the information of the file to download
     */
    private downloadDumpFileFromS3ObjectInfo(sourceS3Info: S3ObjectInfo): Promise<stream.Readable> {
        logger.info(() => `Download ${S3Helper.buildS3Uri(sourceS3Info)} to ${this.#localFullFilename}`);
        return S3Helper.downloadStream(sourceS3Info);
    }

    private async restoreFromS3Zip(runRestore: RunRestoreCallback): Promise<void> {
        const sourceS3Info = this.#sourceS3Info;
        if (!sourceS3Info) {
            throw new Error('No S3 info found');
        }
        const s3Bucket = S3Helper.getS3Bucket(sourceS3Info.bucketName);
        const s3Client = s3Bucket.s3Client;
        const directory = await unzipper.Open.s3_v3(s3Client, {
            Bucket: sourceS3Info.bucketName,
            Key: `${sourceS3Info.folder}/${sourceS3Info.key}`,
        });
        await this.restoreFromZipEntry(directory, runRestore);
    }

    private async restoreFromFileZip(runRestore: RunRestoreCallback): Promise<void> {
        const directory = await unzipper.Open.file(this.#localFullFilename);
        await this.restoreFromZipEntry(directory, runRestore);
    }

    private async restoreFromZipEntry(
        directory: unzipper.CentralDirectory,
        runRestore: RunRestoreCallback,
    ): Promise<void> {
        const sqlCfg = ConfigManager.current.storage?.sql;
        if (!sqlCfg) {
            throw new Error('No SQL configuration found');
        }

        const file = directory.files[0];
        const zipStream = file.stream();
        await this.s3Manager.dropApplicationSchema();

        return this.restoreFromMappedStream(
            directory.files[0].path,
            zipStream,
            new MappingTransform(this.s3Manager.application.schemaName, sqlCfg.user, file.uncompressedSize),
            runRestore,
        );
    }

    private async restoreFromMappedStream(
        dumpFullFilename: string,
        inStream: stream.Readable,
        mappingTransform: MappingTransform,
        runRestore: RunRestoreCallback,
    ): Promise<void> {
        const { schemaName } = mappingTransform;
        // SQL config
        const sqlCfg = ConfigManager.current.storage?.sql;
        if (sqlCfg == null) throw new Error('SQL configuration is missing');

        const mappedDumpFullFilename = fsp.join(
            os.tmpdir(),
            fsp.basename(dumpFullFilename).replace(/\.(zip|sql)$/, '.dump'),
        );
        const outStream = fs.createWriteStream(mappedDumpFullFilename);
        logger.info(() => `[${schemaName}] generating mapped file to ${mappedDumpFullFilename}`);
        await new Promise<void>((resolve, reject) => {
            inStream
                .pipe(mappingTransform)
                .pipe(outStream)
                .on('finish', () => {
                    logger.info(() => `[${schemaName}] mapped file saved to ${mappedDumpFullFilename}`);
                    resolve();
                })
                .on('error', err => {
                    logger.info(() => `[${schemaName}] Failed to save mapped file to ${mappedDumpFullFilename}`);
                    reject(err);
                });
        });

        return runRestore(this, mappedDumpFullFilename);
    }

    get successMessage(): string {
        if (this.#sourceS3Info) {
            return `Successfully restored ${this.#sourceS3Info.key}`;
        }
        if (this.#localFullFilename) {
            return `Successfully restored from local file ${this.#localFullFilename}`;
        }
        return '';
    }
}
