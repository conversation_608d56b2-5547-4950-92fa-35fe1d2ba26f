import * as extract from 'extract-zip';

/**
 * Helper to decompress zip archives (only zip archives are supported by extract-zip).
 * Uses 'extract-zip' npm module https://www.npmjs.com/package/extract-zip
 */
export abstract class Decompress {
    /**
     * Decompress a zip archive to a local folder (this folder must already exist) and returns the RELATIVE path
     * of the extracted files (relative to the provided targetPath)
     * @param archiveFilename the path of the zip archive to extract
     * @param targetPath the local folder where the extracted files should be written (this folder must exist)
     */
    static async decompressZipToFolder(archiveFilename: string, targetPath: string): Promise<string[]> {
        const filenames: string[] = [];
        try {
            await extract(archiveFilename, {
                dir: targetPath,
                onEntry: entry => {
                    if (/\/$/.test(entry.fileName)) return; // a directory
                    filenames.push(entry.fileName);
                },
            });
        } catch (err) {
            throw new Error(`Could not decompress ${archiveFilename}, reason was ${err.message}`);
        }
        return filenames;
    }
}
