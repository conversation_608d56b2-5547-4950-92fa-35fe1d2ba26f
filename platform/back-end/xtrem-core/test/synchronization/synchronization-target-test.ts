import { assert } from 'chai';
import { decorators, Node } from '../../index';
import { codeDataType } from '../fixtures/data-types/data-types';
import { createApplicationWithApi, restoreTables } from '../fixtures/index';

@decorators.node<TestSyncTarget>({
    isPublished: true,
    storage: 'sql',
    isSynchronized: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true, isNaturalKey: true }],
})
class TestSyncTarget extends Node {
    @decorators.stringProperty<TestSyncTarget, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;
}

@decorators.node<TestSyncTargetAndSource>({
    isPublished: true,
    storage: 'sql',
    isSynchronizable: true,
    isSynchronized: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true, isNaturalKey: true }],
})
class TestSyncTargetAndSource extends Node {
    @decorators.stringProperty<TestSyncTarget, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;
}

@decorators.node<TestSyncTargetAbstract>({
    isAbstract: true,
    isPublished: true,
    storage: 'sql',
    isSynchronized: true,
})
class TestSyncTargetAbstract extends Node {}

@decorators.node<TestSyncTargetWithInvalidStorage>({
    isPublished: true,
    storage: 'json',
    isSynchronized: true,
})
class TestSyncTargetWithInvalidStorage extends Node {}

@decorators.node<TestSyncTargetWithoutNaturalKey>({
    isPublished: true,
    storage: 'sql',
    isSynchronized: true,
})
class TestSyncTargetWithoutNaturalKey extends Node {}

describe('isSynchronized node attribute', () => {
    it('succeeds if node has valid attributes', async () => {
        await createApplicationWithApi({ nodes: { TestSyncTarget } });
    });

    it('succeeds if node is both a synchronization target and source', async () => {
        await createApplicationWithApi({ nodes: { TestSyncTargetAndSource } });
    });

    it('throws if node is abstract', async () => {
        await assert.isRejected(
            createApplicationWithApi({ nodes: { TestSyncTargetAbstract } }),
            'TestSyncTargetAbstract: isSynchronized cannot be set on an abstract node',
        );
    });

    it('throws if node has invalid storage', async () => {
        await assert.isRejected(
            createApplicationWithApi({ nodes: { TestSyncTargetWithInvalidStorage } }),
            'TestSyncTargetWithInvalidStorage: isSynchronized can only be set on nodes with sql storage',
        );
    });

    it('throws if node does not have a natural key', async () => {
        await assert.isRejected(
            createApplicationWithApi({ nodes: { TestSyncTargetWithoutNaturalKey } }),
            'TestSyncTargetWithoutNaturalKey: isSynchronized can only be set on nodes with a natural key',
        );
    });

    after(() => restoreTables());
});
