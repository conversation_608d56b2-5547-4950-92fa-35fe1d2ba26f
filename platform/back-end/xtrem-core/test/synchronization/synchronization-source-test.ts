import { Decimal } from '@sage/xtrem-decimal';
import { assert } from 'chai';
import * as _ from 'lodash';
import { Node, Test, asyncArray, decorators } from '../../index';
import { codeDataType } from '../fixtures/data-types/data-types';
import { createApplicationWithApi, initTables, restoreTables, setup } from '../fixtures/index';

@decorators.node<TestSyncSource>({
    isPublished: true,
    storage: 'sql',
    isSynchronizable: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true, isNaturalKey: true }],
})
class TestSyncSource extends Node {
    @decorators.stringProperty<TestSyncSource, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;
}

@decorators.node<TestSyncSourceSharedByAllTenants>({
    isPublished: true,
    storage: 'sql',
    isSharedByAllTenants: true,
    isSynchronizable: true,
})
class TestSyncSourceSharedByAllTenants extends Node {}

@decorators.node<TestSyncSourceWithInvalidStorage>({
    isPublished: true,
    storage: 'json',
    isSynchronizable: true,
})
class TestSyncSourceWithInvalidStorage extends Node {}

@decorators.node<TestSyncSourceWithoutNaturalKey>({
    isPublished: true,
    storage: 'sql',
    isSynchronizable: true,
})
class TestSyncSourceWithoutNaturalKey extends Node {}

@decorators.node<TestSyncSourceBase>({
    isAbstract: true,
    isPublished: true,
    storage: 'sql',
    isSynchronizable: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true, isNaturalKey: true }],
})
class TestSyncSourceBase extends Node {
    @decorators.stringProperty<TestSyncSource, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;
}

@decorators.subNode<TestSyncSourceSub>({
    extends: () => TestSyncSourceBase,
})
class TestSyncSourceSub extends TestSyncSourceBase {
    @decorators.booleanProperty<TestSyncSourceSub, 'bool'>({
        isPublished: true,
        isStored: true,
    })
    readonly bool: Promise<boolean>;
}

@decorators.subNode<TestSyncSourceSubRedefines>({
    extends: () => TestSyncSourceBase,
    isSynchronizable: true,
})
class TestSyncSourceSubRedefines extends TestSyncSourceBase {}

@decorators.node<TestSyncSourceBaseNotSync>({
    isAbstract: true,
    isPublished: true,
    storage: 'sql',
})
class TestSyncSourceBaseNotSync extends Node {}

@decorators.subNode<TestSyncSourceSubSync>({
    extends: () => TestSyncSourceBaseNotSync,
    isSynchronizable: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true, isNaturalKey: true }],
})
class TestSyncSourceSubSync extends TestSyncSourceBaseNotSync {
    @decorators.stringProperty<TestSyncSourceSubSync, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;
}

describe('isSynchronizable node attribute', () => {
    describe('checking decorators', () => {
        it('succeeds if node has valid attributes', async () => {
            await createApplicationWithApi({ nodes: { TestSyncSource } });
        });

        it('succeeds if sub node inherits from synchronized node', async () => {
            await createApplicationWithApi({ nodes: { TestSyncSourceBase, TestSyncSourceSub } });
        });

        it('succeeds if sub node is sync source but super node is not', async () => {
            await createApplicationWithApi({ nodes: { TestSyncSourceBaseNotSync, TestSyncSourceSubSync } });
        });

        it('throws if node has invalid storage', async () => {
            await assert.isRejected(
                createApplicationWithApi({ nodes: { TestSyncSourceWithInvalidStorage } }),
                'TestSyncSourceWithInvalidStorage: isSynchronizable can only be set on nodes with sql storage',
            );
        });

        it('throws if node is shared by all tenants', async () => {
            await assert.isRejected(
                createApplicationWithApi({ nodes: { TestSyncSourceSharedByAllTenants } }),
                'TestSyncSourceSharedByAllTenants: isSynchronizable cannot be set on nodes shared by all tenants',
            );
        });

        it('throws if node does not have a natural key', async () => {
            await assert.isRejected(
                createApplicationWithApi({ nodes: { TestSyncSourceWithoutNaturalKey } }),
                'TestSyncSourceWithoutNaturalKey: isSynchronizable can only be set on nodes with a natural key',
            );
        });

        it('throws if sub node redefines isSynchronizable', async () => {
            await assert.isRejected(
                createApplicationWithApi({ nodes: { TestSyncSourceBase, TestSyncSourceSubRedefines } }),
                'TestSyncSourceSubRedefines: isSynchronizable cannot be redefined on a sub node',
            );
        });

        it('throws if sub node redefines isSynchronizable', async () => {
            await assert.isRejected(
                createApplicationWithApi({ nodes: { TestSyncSourceBase, TestSyncSourceSubRedefines } }),
                'TestSyncSourceSubRedefines: isSynchronizable cannot be redefined on a sub node',
            );
        });
    });

    describe('_syncTick system property', () => {
        const initData = [{ code: 'CODE1' }, { code: 'CODE2' }, { code: 'CODE3' }];
        before(async () => {
            await setup({
                application: await createApplicationWithApi({
                    nodes: {
                        TestSyncSource,
                        TestSyncSourceBase,
                        TestSyncSourceSub,
                        TestSyncSourceBaseNotSync,
                        TestSyncSourceSubSync,
                    },
                }),
            });
            await initTables([
                { nodeConstructor: TestSyncSource, data: initData },
                { nodeConstructor: TestSyncSourceBase, data: [] },
                { nodeConstructor: TestSyncSourceSub, data: [] },
                { nodeConstructor: TestSyncSourceBaseNotSync, data: [] },
                { nodeConstructor: TestSyncSourceSubSync, data: [] },
            ]);
        });

        it('manages _syncTick correctly in a simple source', async () => {
            const syncTick1 = await Test.withContext(async context => {
                const records = await context
                    .query(TestSyncSource, {})
                    .map(async (record, i) => ({
                        _id: i + 1,
                        code: await record.code,
                        _syncTick: (await record._syncTick).toNumber(),
                        _customData: {},
                    }))
                    .toArray();
                const syncTick = records[0]._syncTick;
                assert.isAbove(syncTick, 0);
                assert.deepEqual(
                    records,
                    initData.map(data => ({ ...data, _syncTick: syncTick })),
                );
                return syncTick;
            });
            await Test.withContext(async context => {
                const record2 = await context.read(TestSyncSource, { _id: 2 }, { forUpdate: true });
                await record2.$.set({ code: 'CODE2M' });
                assert.equal((await record2._syncTick).toNumber(), syncTick1);
                await record2.$.save();
                const syncTick2 = (await record2._syncTick).toNumber();
                assert.isAbove(syncTick2, syncTick1);

                const modified = await context.select(
                    TestSyncSource,
                    { _id: true, code: true, _syncTick: true },
                    { filter: { _syncTick: { _gt: syncTick1 } } },
                );
                const mappedModified = modified.map(modifiedRow =>
                    _.mapValues(modifiedRow, (value, key) => {
                        if (key === '_syncTick' && value instanceof Decimal) {
                            return value.toNumber();
                        }
                        return value;
                    }),
                );

                assert.deepEqual(mappedModified, [{ _id: 2, code: 'CODE2M', _syncTick: syncTick2 }]);
            });
        });

        async function testSubClass(construct: typeof TestSyncSourceSub | typeof TestSyncSourceSubSync) {
            const codes = ['C1', 'C2', 'C3'];
            const payloads = await Test.withCommittedContext(context => {
                return asyncArray(codes)
                    .map(async code => {
                        const record = await context.create(construct, { code });
                        await record.$.save();
                        const recordPayload = await record.$.payload({
                            propertyNames: { code: true, _syncTick: true },
                        });
                        return _.mapValues(recordPayload, (value, key) => {
                            if (key === '_syncTick' && value instanceof Decimal) {
                                return value.toNumber();
                            }
                            return value;
                        });
                    })
                    .toArray();
            });
            assert.equal(payloads.length, codes.length);
            const syncTick1 = Number(payloads[0]._syncTick) || 0;
            assert.isAbove(syncTick1, 0);
            assert.deepEqual(
                payloads,
                codes.map(code => ({ code, _syncTick: syncTick1 })),
            );

            await Test.withContext(async context => {
                const record2 = await context.read(construct, { _id: 2 }, { forUpdate: true });
                await record2.$.set({ code: 'C2M' });
                assert.equal((await record2._syncTick).toNumber(), syncTick1);
                await record2.$.save();
                const syncTick2 = (await record2._syncTick).toNumber();
                assert.isAbove(syncTick2, syncTick1);

                const modified = await context.select(
                    construct,
                    { _id: true, code: true, _syncTick: true },
                    { filter: { _syncTick: { _gt: syncTick1 } } },
                );
                const mappedModified = modified.map(modifiedRow =>
                    _.mapValues(modifiedRow, (value, key) => {
                        if (key === '_syncTick' && value instanceof Decimal) {
                            return value.toNumber();
                        }
                        return value;
                    }),
                );
                assert.deepEqual(mappedModified, [{ _id: 2, code: 'C2M', _syncTick: syncTick2 }]);
            });
        }

        it('manages _syncTick correctly in a subnode root source', async () => {
            await testSubClass(TestSyncSourceSub);
        });

        it('manages _syncTick correctly in a subnode leaf source', async () => {
            await testSubClass(TestSyncSourceSubSync);
        });

        after(() => restoreTables());
    });
});
