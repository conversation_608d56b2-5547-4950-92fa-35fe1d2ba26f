/* eslint-disable global-require */
import { AxiosRequestConfig } from 'axios';
import { assert, expect } from 'chai';
import { Test } from '../../index';
import { Mocker, mockPlugins, Plugin } from '../../lib/test/mocker';
import { createApplicationWithApi, initTables, restoreTables, setup } from '../fixtures/index';

class FsPlugin extends Plugin {
    constructor(
        public override readonly path: string = '',
        public override readonly scenario: string = '',
    ) {
        super('fs', path, scenario);
    }

    // eslint-disable-next-line class-methods-use-this
    override mock(): any {
        const original = require('fs');
        return {
            readFileSync: (...args: any) => {
                if (args[0] === 'hello') {
                    return 'world';
                }
                return original.readFileSync(...args);
            },
        };
    }
}

class AxiosPlugin extends Plugin {
    constructor(
        public override readonly path: string = '',
        public override readonly scenario: string = '',
    ) {
        super('axios', path, scenario);
    }

    // eslint-disable-next-line class-methods-use-this
    override mock(): any {
        return {};
    }
}

function fsTest() {
    const fs = Mocker.get('fs', require);
    assert.equal(fs.readFileSync('hello'), 'world');
    expect(() => {
        fs.readFileSync('hey');
    }).to.throw("ENOENT: no such file or directory, open 'hey'");
}

async function axiosTest(): Promise<void> {
    const axios = Mocker.get('axios', require);
    const url = 'scmx3-dev-abc.sagefr.adinternal.com';
    const data =
        '<soapenv:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:wss="http://www.adonix.com/WSS" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/">\r\n            <soap:Header/>\r\n                <soapenv:Body>\r\n                    <wss:save soapenv:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">\r\n                        <callContext xsi:type="wss:WebServicePoolParameters">\r\n                            <codeLang xsi:type="xsd:string">FRA</codeLang>\r\n                            <poolAlias xsi:type="xsd:string">TOGRAPH</poolAlias>\r\n                            <poolId xsi:type="xsd:string"></poolId>\r\n                            <requestConfig xsi:type="xsd:string">adxwss.optreturn=JSON&adxwss.beautify=true</requestConfig>\r\n                        </callContext>\r\n                        <publicName xsi:type="xsd:string">CWSSCS</publicName>\r\n\r\n        <objectXml xsi:type="xsd:string">\r\n            <![CDATA[<?xml version="1.0" encoding="UTF-8"?>\r\n                <PARAM>\r\n                    <GRP ID="SCS1_1"> <FLD NAME="STOFCY">FR012</FLD> <FLD NAME="SGHTYP">CHG</FLD> <FLD NAME="BETFCYCOD">1</FLD> <FLD NAME="VCRNUM">CHGFR0120056</FLD> <FLD NAME="IPTDAT">20200122</FLD> </GRP> <TAB ID="SCS1_7"> <LIN NUM="1"> <FLD NAME="VCRLIN">1</FLD> <FLD NAME="ITMREF">BMS001</FLD> <FLD NAME="STA">A</FLD> <FLD NAME="LOCTYP">ST</FLD> <FLD NAME="LOC">A1C11</FLD> <FLD NAME="PCU">UN</FLD> <FLD NAME="QTYPCU">1</FLD> <FLD NAME="PCUSTUCOE">1</FLD> <FLD NAME="QTYSTUDES">1</FLD> <FLD NAME="COEDES">0</FLD> <FLD NAME="QTYPCUDES">1</FLD> <FLD NAME="STADES">A</FLD> <FLD NAME="LOCTYPDES">STO</FLD> <FLD NAME="LOCDES">A1C12</FLD> <FLD NAME="PRI">0</FLD> </LIN> </TAB>\r\n                </PARAM>\r\n            ]]>\r\n        </objectXml>\r\n                    </wss:save>\r\n                </soapenv:Body>\r\n        </soapenv:Envelope>';
    const header = {
        'Content-Type': 'text/xml;charset=UTF-8',
        SOAPAction: 'SOAPAction',
        Connection: 'Keep-Alive',
    };

    const soapRequest: AxiosRequestConfig = {
        method: 'post',
        url,
        timeout: 500,
        headers: header,
        data,
    };
    assert.deepEqual(await axios(soapRequest), { hello: 'world' });
}

describe('mocker', () => {
    before(async () => {
        await setup({ application: await createApplicationWithApi({ nodes: {} }) });
        await initTables([]);
        mockPlugins.fs = FsPlugin;
    });
    after(async () => {
        delete mockPlugins.fs;
        await restoreTables();
    });

    it("should mock 'fs' library", () =>
        Test.withContext(
            () => {
                fsTest();
            },
            { plugins: [new FsPlugin()] },
        ));

    it("should mock 'axios' library", () =>
        Test.withContext(() => axiosTest(), {
            mocks: ['fs', 'axios'],
            scenario: 'soap-test-1',
            directory: __dirname,
        }));

    it("should mock 'axios' and 'fs' library", () =>
        Test.withContext(
            async () => {
                await axiosTest();
                fsTest();
            },
            {
                mocks: ['axios'],
                plugins: [new FsPlugin()],
                scenario: 'soap-test-1',
                directory: __dirname,
            },
        ));

    it("should throw error if 'axios' is mocked and has a plugin", async () => {
        await assert.isRejected(
            Test.withContext(
                async () => {
                    await axiosTest();
                    fsTest();
                },
                {
                    mocks: ['axios'],
                    plugins: [new AxiosPlugin()],
                    scenario: 'soap-test-1',
                    directory: __dirname,
                },
            ),
            'Cannot mock and supply a plugin for the same module, axios',
        );
    });

    it("should mock multiple 'axios' requests", () =>
        Test.withContext(
            async () => {
                await axiosTest();
                await axiosTest();
            },
            {
                mocks: ['axios'],
                scenario: 'soap-test-2',
                directory: __dirname,
            },
        ));
});
