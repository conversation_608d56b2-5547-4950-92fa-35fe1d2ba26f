import { assert } from 'chai';
import { Test } from '../../index';
import { createApplicationWithApi, initTables, restoreTables, setup } from '../fixtures/index';

const fooUser = { email: '<EMAIL>' };
const defaultUser = { email: '<EMAIL>' };

describe('test options', () => {
    before(async () => {
        await setup({ application: await createApplicationWithApi({ nodes: {} }) });
        await initTables([]);
    });

    after(() => restoreTables());

    it('can set user with email', () =>
        Test.readonly(
            async context => {
                assert.equal((await context.user)?.email, fooUser.email);
            },
            { user: fooUser },
        ));

    it('has default test user without config', () =>
        Test.readonly(async context => {
            assert.equal((await context.user)?.email, defaultUser.email);
        }));

    it('can set user from config without email', () =>
        Test.readonly(
            async context => {
                assert.equal((await context.user)?.email, defaultUser.email);
            },
            { config: {} },
        ));

    it('can set user from config with only email', () =>
        Test.readonly(
            async context => {
                assert.equal((await context.user)?.email, fooUser.email);
            },
            { config: fooUser },
        ));

    it('user email overrides config', () =>
        Test.readonly(
            async context => {
                assert.equal((await context.user)?.email, '<EMAIL>');
            },
            { user: { email: '<EMAIL>' }, config: fooUser },
        ));

    it('empty user email overrides config', () =>
        Test.readonly(
            async context => {
                assert.equal((await context.user)?.email, defaultUser.email);
            },
            { user: { email: '' }, config: fooUser },
        ));
});
