import { assert } from 'chai';
import { assertDeepPartialMatch } from '../../index';

describe('assertDeepPartialMatch', () => {
    it('succeeds when all members of expected match actual', () =>
        assertDeepPartialMatch({ a: 1, b: { c: 2, d: 3 }, e: 4 }, { a: 1, b: { d: 3 } }));

    it('throws when some member of expected does not match actual', () =>
        assert.throws(
            () =>
                assertDeepPartialMatch({ a: 1, b: { c: 2, d: 3 }, e: 4 }, { a: 1, b: { d: 3 }, f: 5 }, 'testing abdf'),
            'testing abdf: mismatch on expected.f: expected undefined to equal 5',
        ));

    it('throws with detailed message when several members of expected do not match actual', () =>
        assert.throws(
            () =>
                assertDeepPartialMatch({ a: 1, b: { c: 2, d: 3 }, e: 4 }, { a: 1, b: { d: 4 }, f: 5 }, 'testing abdf'),
            `testing abdf: 2 mismatches:
    - on expected.b.d: expected 3 to equal 4
    - on expected.f: expected undefined to equal 5`,
        ));

    it('succeeds when actual string value matches a regexp in expected', () =>
        assertDeepPartialMatch({ a: 'hello', b: 'world' }, { a: 'hello', b: '~orl' }));

    it('throws when actual string values does not match a regexp in expected', () =>
        assert.throws(
            () => assertDeepPartialMatch({ a: 'hello', b: 'world' }, { a: 'hello', b: '~b' }),
            "mismatch on expected.b: expected 'world' to match /b/",
        ));

    it('throws when actual string values does not exactly match a string in expected', () =>
        assert.throws(
            () => assertDeepPartialMatch({ a: 'hello', b: 'world' }, { a: 'hello', b: 'orl' }),
            "mismatch on expected.b: expected 'world' to equal 'orl'",
        ));

    it('succeeds when missing value is matched against <<undefined>>', () =>
        assertDeepPartialMatch({ a: 'hello' }, { a: 'hello', b: '<<undefined>>' }));

    it('succeeds when undefined value is matched against <<undefined>>', () =>
        assertDeepPartialMatch({ a: 'hello', b: undefined }, { a: 'hello', b: '<<undefined>>' }));

    it('throws when null is matched against <<undefined>> ', () =>
        assert.throws(
            () => assertDeepPartialMatch({ a: 'hello', b: null }, { a: 'hello', b: '<<undefined>>' }),
            'mismatch on expected.b: expected null to equal undefined',
        ));
});
