{"request": {"method": "post", "url": "scmx3-dev-abc.sagefr.adinternal.com", "headers": {"Content-Type": "text/xml;charset=UTF-8", "SOAPAction": "SOAPAction", "Connection": "Keep-Alive"}, "data": "<soapenv:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:wss=\"http://www.adonix.com/WSS\" xmlns:soapenc=\"http://schemas.xmlsoap.org/soap/encoding/\">\r\n            <soap:Header/>\r\n                <soapenv:Body>\r\n                    <wss:save soapenv:encodingStyle=\"http://schemas.xmlsoap.org/soap/encoding/\">\r\n                        <callContext xsi:type=\"wss:WebServicePoolParameters\">\r\n                            <codeLang xsi:type=\"xsd:string\">FRA</codeLang>\r\n                            <poolAlias xsi:type=\"xsd:string\">TOGRAPH</poolAlias>\r\n                            <poolId xsi:type=\"xsd:string\"></poolId>\r\n                            <requestConfig xsi:type=\"xsd:string\">adxwss.optreturn=JSON&adxwss.beautify=true</requestConfig>\r\n                        </callContext>\r\n                        <publicName xsi:type=\"xsd:string\">CWSSCS</publicName>\r\n\r\n        <objectXml xsi:type=\"xsd:string\">\r\n            <![CDATA[<?xml version=\"1.0\" encoding=\"UTF-8\"?>\r\n                <PARAM>\r\n                    <GRP ID=\"SCS1_1\"> <FLD NAME=\"STOFCY\">FR012</FLD> <FLD NAME=\"SGHTYP\">CHG</FLD> <FLD NAME=\"BETFCYCOD\">1</FLD> <FLD NAME=\"VCRNUM\">CHGFR0120056</FLD> <FLD NAME=\"IPTDAT\">20200122</FLD> </GRP> <TAB ID=\"SCS1_7\"> <LIN NUM=\"1\"> <FLD NAME=\"VCRLIN\">1</FLD> <FLD NAME=\"ITMREF\">BMS001</FLD> <FLD NAME=\"STA\">A</FLD> <FLD NAME=\"LOCTYP\">ST</FLD> <FLD NAME=\"LOC\">A1C11</FLD> <FLD NAME=\"PCU\">UN</FLD> <FLD NAME=\"QTYPCU\">1</FLD> <FLD NAME=\"PCUSTUCOE\">1</FLD> <FLD NAME=\"QTYSTUDES\">1</FLD> <FLD NAME=\"COEDES\">0</FLD> <FLD NAME=\"QTYPCUDES\">1</FLD> <FLD NAME=\"STADES\">A</FLD> <FLD NAME=\"LOCTYPDES\">STO</FLD> <FLD NAME=\"LOCDES\">A1C12</FLD> <FLD NAME=\"PRI\">0</FLD> </LIN> </TAB>\r\n                </PARAM>\r\n            ]]>\r\n        </objectXml>\r\n                    </wss:save>\r\n                </soapenv:Body>\r\n        </soapenv:Envelope>", "timeout": 500}, "response": {"hello": "world"}}