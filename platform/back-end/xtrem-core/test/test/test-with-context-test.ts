/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable func-names */
/* eslint-disable no-console */
// tslint:disable: no-unused-expression
import { assert } from 'chai';
import { Context } from '../../lib/runtime';
import { TestStatus } from '../../lib/sql/schema';
import { Test } from '../../lib/test';
import { createApplicationWithApi, setup } from '../fixtures/index';
import {
    TestAnimal,
    TestCat,
    TestDog,
    TestFish,
    TestMammal,
    TestPetOwner,
    TestProduct,
    TestProductCollection,
    TestProductCollectionReference,
    TestReferencedByCollection,
} from '../fixtures/nodes';

function getString(parent: string, child: string, nb: number) {
    return [...Array(nb).keys()].map(i => `strFrom${parent}/${child}/${i + 1}`);
}

const newCatValues = {
    strFromCat: 'animal/mammal/cat/new1',
    strFromMammal: 'animal/mammal/cat/new1',
    strFromAnimal: 'animal/mammal/cat/new1',
};

async function checkContents(context: Context, expectedCatNumber: number): Promise<void> {
    const cats = await context
        .query(TestCat)
        .map(cat => cat.strFromCat)
        .toArray();
    assert.deepEqual(cats, getString('Cat', 'cat', expectedCatNumber));

    const mammals = await context
        .query(TestMammal)
        .map(mammal => mammal.strFromMammal)
        .toArray();
    assert.deepEqual(mammals, [...getString('Mammal', 'cat', expectedCatNumber), ...getString('Mammal', 'dog', 3)]);

    const animals = await context
        .query(TestAnimal)
        .map(animal => animal.strFromAnimal)
        .toArray();
    assert.deepEqual(animals, [
        ...getString('Animal', 'cat', expectedCatNumber),
        ...getString('Animal', 'dog', 3),
        ...getString('Animal', 'fish', 3),
    ]);
}

const api = {
    nodes: {
        TestProduct,
        TestProductCollection,
        TestProductCollectionReference,
        TestReferencedByCollection,
        TestAnimal,
        TestCat,
        TestMammal,
        TestPetOwner,
        TestDog,
        TestFish,
    },
};

describe.skip('Test.withContext', () => {
    before(async () => {
        // pass false to stubResetTables parameter to not stub resetTablesForTests in Application
        const application = await createApplicationWithApi(api);
        await setup({ stubResetTables: false, application });
    });

    it('can load abstract and non-abstract tables correctly', () =>
        Test.withContext(context => checkContents(context, 3)));

    it('can create a cat and correctly rolls back after withContext', async () => {
        Test.application.globalCache.clearAll();
        await Test.withContext(async context => {
            const newNode = await context.create(TestCat, newCatValues);
            await newNode.$.save();
            const newCat = (
                await context.query(TestCat, { filter: { strFromCat: newCatValues.strFromCat } }).toArray()
            )[0];
            assert.equal(await newCat.strFromCat, newCatValues.strFromCat);
            assert.equal(await newCat.strFromMammal, newCatValues.strFromMammal);
            assert.equal(await newCat.strFromAnimal, newCatValues.strFromAnimal);
            const newMammal = (
                await context.query(TestMammal, { filter: { strFromMammal: newCatValues.strFromMammal } }).toArray()
            )[0];
            assert.equal(await newMammal.strFromMammal, newCatValues.strFromMammal);
            assert.equal(await newMammal.strFromAnimal, newCatValues.strFromAnimal);
        });
        await Test.withContext(async context => {
            const rolledBackCat = await context
                .query(TestCat, { filter: { strFromCat: newCatValues.strFromCat } })
                .toArray();
            assert.isEmpty(rolledBackCat);
        });
    });

    it("doesn't reloads tables if layers have not changed and no time mock was provided", async () => {
        await Test.withContext(_context => {});
        const nodeBefore = (
            await Test.withReadonlyContext(context => context.query(TestCat, { first: 1 }).toArray())
        )[0];
        await Test.withContext(_context => {});

        const nodeAfter = (
            await Test.withReadonlyContext(context => context.query(TestCat, { first: 1 }).toArray())
        )[0];

        assert.equal(nodeBefore._id, nodeAfter._id);
        // update stamps are equal and there is no mock now in either call, there record was not reloaded
        assert.deepEqual(
            nodeBefore.$.getRawPropertyValue('_updateStamp'),
            nodeAfter.$.getRawPropertyValue('_updateStamp'),
        );
    });

    it('reloads tables if layers have changed', async () => {
        await Test.withContext(_context => {});
        const nodeBefore = (
            await Test.withReadonlyContext(context => context.query(TestCat, { first: 1 }).toArray())
        )[0];
        await Test.withContext(_context => {}, { config: { layers: [''] } });
        const nodeAfter = (
            await Test.withReadonlyContext(context => context.query(TestCat, { first: 1 }).toArray())
        )[0];
        // first pass a result was returned and the table had data
        assert.isDefined(nodeBefore._id);
        // second pass no results was return as the layers changed to a none existing layer and the table was reloaded with
        // no data.
        assert.isUndefined(nodeAfter);
    });

    it('reloads tables if a time mock is provided', async () => {
        await Test.withContext(_context => {});
        const nodeBefore = (
            await Test.withReadonlyContext(context => context.query(TestCat, { first: 1 }).toArray())
        )[0];
        await Test.withContext(_context => {}, { now: '2020-01-01T10:00:00' });
        const nodeAfter = (
            await Test.withReadonlyContext(context => context.query(TestCat, { first: 1 }).toArray())
        )[0];

        assert.equal(nodeBefore._id, nodeAfter._id);
        // compare the update stamps of the loaded values before and after
        // update stamps are not same there the record was re-inserted in the second run
        assert.notDeepEqual(
            nodeBefore.$.getRawPropertyValue('_updateStamp'),
            nodeAfter.$.getRawPropertyValue('_updateStamp'),
        );

        // TODO: Investigate why these 2 datetime values are the same, except the _updateStamp has a timezone offset
        // assert.equal(nodeAfter.state.values._updateStamp, Datetime.parse('2020-01-01T10:00:00Z'));
    });

    it('loads the correct records when a time mock is or is not provided', async () => {
        await Test.withContext(
            // Only two cats shall be present when the now mock is provided.
            context => checkContents(context, 2),
            { now: '2020-01-01T10:00:00' },
        );

        await Test.withContext(context => checkContents(context, 3));
    });

    it('correctly identifies dirty tables from previous tests and alters sequences accordingly', async () => {
        const catFactory = Test.application.getFactoryByConstructor(TestCat);
        let lastIdBefore = 0;
        let lastIdAfter = 0;
        const checkIds = async (context: Context) => {
            // check the status of tables before the record is saved. In every pass, this status should be loaded
            assert.equal(
                catFactory.table.testStatus,
                TestStatus.loaded,
                `Table ${catFactory.table.name} is not loaded`,
            );
            let baseCatFactory = catFactory.baseFactory;
            while (baseCatFactory) {
                assert.equal(
                    baseCatFactory.table.testStatus,
                    TestStatus.loaded,
                    `Abstract table ${baseCatFactory.table.name} is not loaded`,
                );
                baseCatFactory = baseCatFactory.baseFactory;
            }

            if (lastIdBefore === 0) {
                // first pass - we store the last animal _id
                lastIdBefore = (await context.query(TestAnimal, { last: 1 }).toArray())[0]._id;
            } else {
                // second pass - we compare the first pass stored value for the last animal _id and the current queried value
                // they should be equal as table was reloaded and sequence for abstract table test_animal must have been reset to
                // the correct value.
                assert.equal(lastIdBefore, (await context.query(TestAnimal, { last: 1 }).toArray())[0]._id);
            }
            const newNode = await context.create(TestCat, newCatValues);
            await newNode.$.save();
            if (lastIdAfter === 0) {
                // first pass - we store the _id that was assigned when record was inserted
                lastIdAfter = newNode._id;
            } else {
                // second pass - we compare the first pass stored _id value for the inserted test_cat record to the current value
                assert.equal(lastIdAfter, newNode._id);
            }
            // in both passes animal last records _id must equal to new records _id-1
            assert.equal(lastIdBefore, lastIdAfter - 1);

            // check the status of tables after the record is saved
            assert.equal(
                catFactory.table.testStatus,
                TestStatus.modified,
                `Table ${catFactory.table.name} is not dirty`,
            );
            baseCatFactory = catFactory.baseFactory;
            while (baseCatFactory) {
                assert.equal(
                    baseCatFactory.table.testStatus,
                    TestStatus.modified,
                    `Abstract table ${baseCatFactory.table.name} is not dirty`,
                );
                baseCatFactory = baseCatFactory.baseFactory;
            }
        };

        // Run scenario once inserting a row and checking _id values and table test status
        await Test.withContext(checkIds);

        // Run scenario again inserting a row and checking _id values and table test status.
        await Test.withContext(checkIds);

        // Note - layers have changed in this tests so tables are not dropped and reloaded
    });
});
