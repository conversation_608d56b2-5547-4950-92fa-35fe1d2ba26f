import { assert } from 'chai';
import { NotificationTopic } from '../../index';
import { createApplicationWithApi } from '../fixtures/index';

const createTestTopic = (appName: string, topic: string) => new NotificationTopic({ appName, topic });

describe('NotificationTopic artifact', () => {
    it('can create application with valid topics', async () => {
        const testApp1TestNode1Event1 = createTestTopic('testApp1', '/testApp1/TestNode1/event1');
        const testNode1Event2 = createTestTopic('', 'TestNode1/event2');
        await createApplicationWithApi({
            notificationTopics: { testApp1TestNode1Event1, testNode1Event2 },
        });
    });

    it('throws if name of topic variable does not match topic', async () => {
        const mismatchingName = createTestTopic('testApp1', 'testApp1/TestNode1/event1');
        await assert.isRejected(
            createApplicationWithApi({ notificationTopics: { mismatchingName } }),
            'mismatchingName: Mismatch on name of notification topic. Expected testApp1TestNode1Event1, got mismatchingName',
        );
    });

    it('throws if absolute topic does not start with /', async () => {
        const testApp1TestNode1Event1 = createTestTopic('testApp1', 'testApp1/TestNode1/event1');
        await assert.isRejected(
            createApplicationWithApi({ notificationTopics: { testApp1TestNode1Event1 } }),
            'testApp1TestNode1Event1: Notification topic with app must have an absolute path (with a leading /)',
        );
    });

    it('throws if relative topic starts with /', async () => {
        const testApp1TestNode1Event2 = createTestTopic('', '/testApp1/TestNode1/event2');
        await assert.isRejected(
            createApplicationWithApi({ notificationTopics: { testApp1TestNode1Event2 } }),
            'testApp1TestNode1Event2: Notification topic without app must have a relative path (no leading /)',
        );
    });
});
