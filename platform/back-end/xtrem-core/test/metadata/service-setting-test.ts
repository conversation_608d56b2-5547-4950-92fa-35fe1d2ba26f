import { Axios } from 'axios';
import { assert } from 'chai';
import * as sinon from 'sinon';
import { Application, ConfigManager, initTables } from '../../lib';
import { GraphQlHelper, createApplicationWithApi, graphqlMetadataSetup, restoreTables } from '../fixtures/index';
import { TestDatatypes } from '../fixtures/nodes';
import { serviceOption1, serviceOption2, serviceOption3, serviceOption4 } from '../fixtures/service-options';

let graphqlHelper: GraphQlHelper;
let application: Application;

describe('serviceSettings', () => {
    let axiosPostStub: sinon.SinonStub;

    before(async () => {
        application = await createApplicationWithApi({
            nodes: { TestDatatypes },
            serviceOptions: { serviceOption1, serviceOption2, serviceOption3, serviceOption4 },
        });
        graphqlHelper = await graphqlMetadataSetup({ application });
        await initTables([]);

        axiosPostStub = sinon.stub(Axios.prototype, 'post');
    });

    it('can query metadata schema for copilot token and enabled flag', async () => {
        axiosPostStub.resolves(Promise.resolve({ status: 200, data: { access_token: 'token', expires_in: 1000 } }));
        const copilotConfig = ConfigManager.current.copilot;
        ConfigManager.current.copilot = {
            serviceUrl: 'http://localhost:8080',
            oauthEndpointUrl: 'http://localhost:8080',
            clientId: 'clientId',
            clientSecret: 'clientSecret',
            audience: 'audience',
        };
        const result = await graphqlHelper.execute(
            `
            {
                serviceSettings {
                    copilot{
                        token
                        expiration
                        enabled
                    }
                }
              }
            `,
        );
        assert.equal((result.data as any).serviceSettings.copilot.token, 'token');
        assert.equal((result.data as any).serviceSettings.copilot.enabled, true);
        assert.isNumber(Number((result.data as any).serviceSettings.copilot.expiration));

        ConfigManager.current.copilot = copilotConfig;
    });
    after(async () => {
        await restoreTables();
        axiosPostStub.restore();
    });
});
