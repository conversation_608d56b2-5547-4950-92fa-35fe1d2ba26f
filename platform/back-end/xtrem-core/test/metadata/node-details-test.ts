import { assert } from 'chai';
import { Application, initTables } from '../../lib';
import * as dataTypes from '../fixtures/data-types/_index';
import * as enums from '../fixtures/enums';
import { GraphQlHelper, createApplicationWithApi, graphqlMetadataSetup, restoreTables } from '../fixtures/index';
import {
    TestDocument,
    TestDocumentLine,
    TestNodeDetails,
    TestReferred,
    TestVitalCollectionChild,
    TestVitalCollectionParent,
    TestVitalCollectionSubChild,
} from '../fixtures/nodes';
import { serviceOption1, serviceOption2, serviceOption3, serviceOption4 } from '../fixtures/service-options';

let graphqlHelper: GraphQlHelper;
let application: Application;

describe('node details metadata query', () => {
    before(async () => {
        application = await createApplicationWithApi({
            nodes: {
                TestNodeDetails,
                TestDocument,
                TestDocumentLine,
                TestReferred,
                TestVitalCollectionParent,
                TestVitalCollectionChild,
                TestVitalCollectionSubChild,
            },
            serviceOptions: { serviceOption1, serviceOption2, serviceOption3, serviceOption4 },
            dataTypes,
            enums,
        });
        graphqlHelper = await graphqlMetadataSetup({ application });
        await initTables([]);
    });

    it('Should get details for the TestNodeDetails (no dataType used) node and all the properties', async () => {
        const result = await graphqlHelper.execute(
            `{ getNodeDetails(nodeName: "TestNodeDetails") {
                packageName
                title
                name
                hasAttachments
                defaultDataType
                defaultDataTypeDetails {
                    node
                    type
                    title
                    value {
                        bind
                        title
                        type
                        enumType
                    }
                    helperText {
                        bind
                        title
                        type
                        enumType
                    }
                    isDefault
                    columns {
                        bind
                        title
                        type
                        enumType
                    }
                    precision
                    scale
                    roundingMode
                    maxLength
                    isLocalized
                    doNotTrim
                    truncate
                    values {
                        value
                        title
                    }
                    allowedContentTypes
                }
                properties {
                    title
                    name
                    canSort
                    canFilter
                    enumType
                    type
                    isCustom
                    dataType
                    dataTypeDetails {
                        node
                        type
                        title
                        value {
                            bind
                            title
                            type
                            enumType
                        }
                        helperText {
                            bind
                            title
                            type
                            enumType
                        }
                        isDefault
                        columns {
                            bind
                            title
                            type
                            enumType
                        }
                        precision
                        scale
                        roundingMode
                        maxLength
                        isLocalized
                        doNotTrim
                        truncate
                        values {
                            value
                            title
                        }
                        allowedContentTypes
                    }
                    targetNode
                    isOnInputType
                    isOnOutputType
                    isMutable
                    isSystemProperty
                }
                queries {
                    title
                    name
                    parameters {
                        name
                        title
                    },
                },
                mutations {
                    title
                    name
                    parameters {
                        name
                        title
                    },
                }
            }
        }`,
        );

        assert.deepEqual(result, {
            data: {
                getNodeDetails: {
                    packageName: '@sage/xtrem-core',
                    title: 'Test node details',
                    name: 'TestNodeDetails',
                    hasAttachments: false,
                    defaultDataType: 'testNodeDetails',
                    defaultDataTypeDetails: {
                        node: '@sage/xtrem-core/TestNodeDetails',
                        type: 'reference',
                        title: 'Test node details',
                        value: {
                            bind: '_id',
                            title: '_id',
                            type: 'integer',
                            enumType: null,
                        },
                        helperText: {
                            bind: '_id',
                            title: '_id',
                            type: 'integer',
                            enumType: null,
                        },
                        isDefault: false,
                        columns: [
                            {
                                bind: '_id',
                                title: '_id',
                                type: 'integer',
                                enumType: null,
                            },
                        ],
                        precision: null,
                        scale: null,
                        roundingMode: null,
                        maxLength: null,
                        isLocalized: null,
                        doNotTrim: null,
                        truncate: null,
                        values: null,
                        allowedContentTypes: null,
                    },
                    properties: [
                        {
                            title: 'Update tick',
                            name: '_updateTick',
                            canSort: true,
                            canFilter: true,
                            enumType: null,
                            type: 'integer',
                            isCustom: false,
                            dataType: '',
                            dataTypeDetails: null,
                            targetNode: '',
                            isOnInputType: true,
                            isOnOutputType: true,
                            isMutable: false,
                            isSystemProperty: true,
                        },
                        {
                            title: 'Update user',
                            name: '_updateUser',
                            canSort: true,
                            canFilter: true,
                            enumType: null,
                            type: 'reference',
                            isCustom: false,
                            dataType: 'testUser',
                            dataTypeDetails: {
                                node: '@sage/xtrem-core/TestUser',
                                type: 'reference',
                                title: 'Test user',
                                value: {
                                    bind: 'email',
                                    title: 'Email',
                                    type: 'string',
                                    enumType: null,
                                },
                                helperText: {
                                    bind: 'email',
                                    title: 'Email',
                                    type: 'string',
                                    enumType: null,
                                },
                                isDefault: false,
                                columns: [
                                    {
                                        bind: 'email',
                                        title: 'Email',
                                        type: 'string',
                                        enumType: null,
                                    },
                                ],
                                precision: null,
                                scale: null,
                                roundingMode: null,
                                maxLength: null,
                                isLocalized: null,
                                doNotTrim: null,
                                truncate: null,
                                values: null,
                                allowedContentTypes: null,
                            },
                            targetNode: '@sage/xtrem-core/TestUser',
                            isOnInputType: true,
                            isOnOutputType: true,
                            isMutable: false,
                            isSystemProperty: true,
                        },
                        {
                            title: 'Create user',
                            name: '_createUser',
                            canSort: true,
                            canFilter: true,
                            enumType: null,
                            type: 'reference',
                            isCustom: false,
                            dataType: 'testUser',
                            dataTypeDetails: {
                                node: '@sage/xtrem-core/TestUser',
                                type: 'reference',
                                title: 'Test user',
                                value: {
                                    bind: 'email',
                                    title: 'Email',
                                    type: 'string',
                                    enumType: null,
                                },
                                helperText: {
                                    bind: 'email',
                                    title: 'Email',
                                    type: 'string',
                                    enumType: null,
                                },
                                isDefault: false,
                                columns: [
                                    {
                                        bind: 'email',
                                        title: 'Email',
                                        type: 'string',
                                        enumType: null,
                                    },
                                ],
                                precision: null,
                                scale: null,
                                roundingMode: null,
                                maxLength: null,
                                isLocalized: null,
                                doNotTrim: null,
                                truncate: null,
                                values: null,
                                allowedContentTypes: null,
                            },
                            targetNode: '@sage/xtrem-core/TestUser',
                            isOnInputType: true,
                            isOnOutputType: true,
                            isMutable: false,
                            isSystemProperty: true,
                        },
                        {
                            title: 'Custom data',
                            name: '_customData',
                            canSort: true,
                            canFilter: true,
                            enumType: null,
                            type: 'json',
                            isCustom: false,
                            dataType: '_jsonDataType',
                            dataTypeDetails: {
                                node: null,
                                type: 'json',
                                title: 'Json data type',
                                value: null,
                                helperText: null,
                                isDefault: false,
                                columns: null,
                                precision: null,
                                scale: null,
                                roundingMode: null,
                                maxLength: null,
                                isLocalized: null,
                                doNotTrim: null,
                                truncate: null,
                                values: null,
                                allowedContentTypes: null,
                            },
                            targetNode: '',
                            isOnInputType: true,
                            isOnOutputType: true,
                            isMutable: false,
                            isSystemProperty: true,
                        },
                        {
                            title: 'Source id',
                            name: '_sourceId',
                            canSort: true,
                            canFilter: true,
                            enumType: null,
                            type: 'string',
                            isCustom: false,
                            dataType: '_sourceIdDataType',
                            dataTypeDetails: {
                                node: null,
                                type: 'string',
                                title: 'Source id data type',
                                value: null,
                                helperText: null,
                                isDefault: false,
                                columns: null,
                                precision: null,
                                scale: null,
                                roundingMode: null,
                                maxLength: 128,
                                isLocalized: false,
                                doNotTrim: false,
                                truncate: false,
                                values: null,
                                allowedContentTypes: null,
                            },
                            targetNode: '',
                            isOnInputType: true,
                            isOnOutputType: true,
                            isMutable: false,
                            isSystemProperty: true,
                        },
                        {
                            title: '_id',
                            name: '_id',
                            canSort: true,
                            canFilter: true,
                            enumType: null,
                            type: 'integer',
                            isCustom: false,
                            dataType: '',
                            dataTypeDetails: null,
                            targetNode: '',
                            isOnInputType: true,
                            isOnOutputType: true,
                            isMutable: false,
                            isSystemProperty: true,
                        },
                        {
                            title: 'Name',
                            name: 'name',
                            canSort: true,
                            canFilter: true,
                            enumType: null,
                            type: 'string',
                            isCustom: false,
                            dataType: 'descriptionDataType',
                            dataTypeDetails: {
                                node: null,
                                type: 'string',
                                title: 'Description data type',
                                value: null,
                                helperText: null,
                                isDefault: false,
                                columns: null,
                                precision: null,
                                scale: null,
                                roundingMode: null,
                                maxLength: 250,
                                isLocalized: false,
                                doNotTrim: false,
                                truncate: false,
                                values: null,
                                allowedContentTypes: null,
                            },
                            targetNode: '',
                            isOnInputType: true,
                            isOnOutputType: true,
                            isMutable: false,
                            isSystemProperty: false,
                        },
                        {
                            title: 'Is localized',
                            name: 'isLocalized',
                            canSort: true,
                            canFilter: true,
                            enumType: null,
                            type: 'boolean',
                            isCustom: false,
                            dataType: '',
                            dataTypeDetails: null,
                            targetNode: '',
                            isOnInputType: true,
                            isOnOutputType: true,
                            isMutable: false,
                            isSystemProperty: false,
                        },
                        {
                            title: 'Is localized computed',
                            name: 'isLocalizedComputed',
                            canSort: false,
                            canFilter: false,
                            enumType: null,
                            type: 'boolean',
                            isCustom: false,
                            dataType: '',
                            dataTypeDetails: null,
                            targetNode: '',
                            isOnInputType: false,
                            isOnOutputType: true,
                            isMutable: false,
                            isSystemProperty: false,
                        },
                        {
                            title: 'Is localized get value',
                            name: 'isLocalizedGetValue',
                            canSort: true,
                            canFilter: true,
                            enumType: null,
                            type: 'boolean',
                            isCustom: false,
                            dataType: '',
                            dataTypeDetails: null,
                            targetNode: '',
                            isOnInputType: false,
                            isOnOutputType: true,
                            isMutable: false,
                            isSystemProperty: false,
                        },
                        {
                            title: 'Test base enum',
                            name: 'testBaseEnum',
                            canSort: true,
                            canFilter: true,
                            enumType: '@sage/xtrem-core/TestEnum',
                            type: 'enum',
                            isCustom: false,
                            dataType: 'testEnumDataType',
                            dataTypeDetails: {
                                node: null,
                                type: 'enum',
                                title: 'Test enum enum',
                                value: null,
                                helperText: null,
                                isDefault: false,
                                columns: null,
                                precision: null,
                                scale: null,
                                roundingMode: null,
                                maxLength: null,
                                isLocalized: null,
                                doNotTrim: null,
                                truncate: null,
                                values: [
                                    {
                                        value: 'value1',
                                        title: 'Value 1',
                                    },
                                    {
                                        value: 'value2',
                                        title: 'Value 2',
                                    },
                                    {
                                        value: 'value3',
                                        title: 'Value 3',
                                    },
                                    {
                                        value: 'value4',
                                        title: 'Value 4',
                                    },
                                ],
                                allowedContentTypes: null,
                            },
                            targetNode: '',
                            isOnInputType: true,
                            isOnOutputType: true,
                            isMutable: false,
                            isSystemProperty: false,
                        },
                        {
                            title: 'Etag',
                            name: '_etag',
                            canSort: false,
                            canFilter: false,
                            enumType: null,
                            type: 'string',
                            isCustom: false,
                            dataType: '_etagDataType',
                            dataTypeDetails: {
                                node: null,
                                type: 'string',
                                title: 'Etag data type',
                                value: null,
                                helperText: null,
                                isDefault: false,
                                columns: null,
                                precision: null,
                                scale: null,
                                roundingMode: null,
                                maxLength: 100,
                                isLocalized: false,
                                doNotTrim: false,
                                truncate: false,
                                values: null,
                                allowedContentTypes: null,
                            },
                            targetNode: '',
                            isOnInputType: false,
                            isOnOutputType: true,
                            isMutable: false,
                            isSystemProperty: true,
                        },
                    ],
                    queries: [
                        {
                            title: 'Test query',
                            name: 'testQuery',
                            parameters: [
                                {
                                    name: 'name',
                                    title: 'Name',
                                },
                            ],
                        },
                    ],
                    mutations: [
                        {
                            title: 'Test create',
                            name: 'testCreate',
                            parameters: [
                                {
                                    name: 'name',
                                    title: 'Name',
                                },
                                {
                                    name: 'isLocalized',
                                    title: 'Is localized',
                                },
                            ],
                        },
                    ],
                },
            },
        });
    });

    it('Should get details for the TestDocument (with dataType details) node and all the properties', () =>
        graphqlHelper.runScenario('node-details-test-document'));

    it('Should get details for the TestDocumentLine (with dataType details) node and all the properties', () =>
        graphqlHelper.runScenario('node-details-test-document-line'));

    it('Should get details for TestDocument and an empty list of known node names', async () => {
        const result = await graphqlHelper.execute(
            `{
                getNodeDetailsList(missingNodeNames: ["TestDocument"], knownNodeNames: []) {
                    name
                    queries { name }
                }
            }`,
        );

        assert.deepEqual(result, {
            data: {
                getNodeDetailsList: [
                    'TestDocument',
                    'TestUser',
                    'TestSysVendor',
                    'TestReferred',
                    'TestDocumentLine',
                ].map(name => ({ name, queries: [] })),
            },
        });
    });

    it('Should get details for TestReferred and an empty list of known node names', async () => {
        const result = await graphqlHelper.execute(
            `{
                getNodeDetailsList(missingNodeNames: ["TestReferred"], knownNodeNames: []) {
                    name
                    queries { name }
                }
            }`,
        );

        // TestReferred has a reference to TestUser, which has a reference to TestSysVendor
        assert.deepEqual(result, {
            data: {
                getNodeDetailsList: ['TestReferred', 'TestUser', 'TestSysVendor'].map(name => ({ name, queries: [] })),
            },
        });
    });

    it('Should only return details for the missing nodes when we pass a list of known node details', async () => {
        const result = await graphqlHelper.execute(
            `{
                getNodeDetailsList(missingNodeNames: ["TestDocument"], knownNodeNames: ["TestReferred"]) {
                    name
                    queries { name }
                }
            }`,
        );

        // We should not exclude the expanded tree of the known nodes, only nodes that are explicitly in the known list, so
        // we should get the details for TestDocument, TestDocumentLine, TestUser and TestSysVendor, even though the
        // last 2 are known to TestReferred
        assert.deepEqual(result, {
            data: {
                getNodeDetailsList: ['TestDocument', 'TestUser', 'TestSysVendor', 'TestDocumentLine'].map(name => ({
                    name,
                    queries: [],
                })),
            },
        });
    });

    it('Should only get 2 levels if we set the depth to 2', async () => {
        const result = await graphqlHelper.execute(
            `{
                getNodeDetailsList(missingNodeNames: ["TestDocument"], knownNodeNames: [], depth: 2) {
                    name
                    queries { name }
                }
            }`,
        );

        // We get TestUser through the _createUser and _updateUser properties of TestDocument
        assert.deepEqual(result, {
            data: {
                getNodeDetailsList: ['TestDocument', 'TestUser', 'TestReferred', 'TestDocumentLine'].map(name => ({
                    name,
                    queries: [],
                })),
            },
        });
    });

    it('Should get details for the TestNodeDetails (no dataType used) node and all will exclude vitalParent in vitalChild', async () => {
        const result = await graphqlHelper.execute(
            `{ getNodeDetails(nodeName: "TestVitalCollectionParent") {
                title
                name
                hasAttachments
                defaultDataType
                defaultDataTypeDetails {
                    node
                    type
                    title
                    value {
                        bind
                        title
                        type
                        enumType
                    }
                    helperText {
                        bind
                        title
                        type
                        enumType
                    }
                    isDefault
                    columns {
                        bind
                        title
                        type
                        enumType
                    }
                    precision
                    scale
                    roundingMode
                    maxLength
                    isLocalized
                    doNotTrim
                    truncate
                    values {
                        value
                        title
                    }
                    allowedContentTypes
                }
                properties {
                    title
                    name
                    canSort
                    canFilter
                    enumType
                    type
                    isCustom
                    dataType
                    dataTypeDetails {
                        node
                        type
                        title
                        value {
                            bind
                            title
                            type
                            enumType
                        }
                        helperText {
                            bind
                            title
                            type
                            enumType
                        }
                        isDefault
                        columns {
                            bind
                            title
                            type
                            enumType
                        }
                        precision
                        scale
                        roundingMode
                        maxLength
                        isLocalized
                        doNotTrim
                        truncate
                        values {
                            value
                            title
                        }
                        allowedContentTypes
                    }
                    targetNode
                    isOnInputType
                    isOnOutputType
                    isMutable
                    isSystemProperty
                }
                queries {
                    title
                    name
                    parameters {
                        name
                        title
                    },
                },
                mutations {
                    title
                    name
                    parameters {
                        name
                        title
                    },
                }
            }
        }`,
        );

        assert.deepEqual(result, {
            data: {
                getNodeDetails: {
                    title: 'Test vital collection parent',
                    name: 'TestVitalCollectionParent',
                    hasAttachments: false,
                    defaultDataType: 'testVitalCollectionParent',
                    defaultDataTypeDetails: {
                        node: '@sage/xtrem-core/TestVitalCollectionParent',
                        type: 'reference',
                        title: 'Test vital collection parent',
                        value: {
                            bind: 'code',
                            title: 'Code',
                            type: 'string',
                            enumType: null,
                        },
                        helperText: {
                            bind: 'code',
                            title: 'Code',
                            type: 'string',
                            enumType: null,
                        },
                        isDefault: false,
                        columns: [
                            {
                                bind: 'code',
                                title: 'Code',
                                type: 'string',
                                enumType: null,
                            },
                        ],
                        precision: null,
                        scale: null,
                        roundingMode: null,
                        maxLength: null,
                        isLocalized: null,
                        doNotTrim: null,
                        truncate: null,
                        values: null,
                        allowedContentTypes: null,
                    },
                    properties: [
                        {
                            title: 'Update tick',
                            name: '_updateTick',
                            canSort: true,
                            canFilter: true,
                            enumType: null,
                            type: 'integer',
                            isCustom: false,
                            dataType: '',
                            dataTypeDetails: null,
                            targetNode: '',
                            isOnInputType: true,
                            isOnOutputType: true,
                            isMutable: false,
                            isSystemProperty: true,
                        },
                        {
                            title: 'Update user',
                            name: '_updateUser',
                            canSort: true,
                            canFilter: true,
                            enumType: null,
                            type: 'reference',
                            isCustom: false,
                            dataType: 'testUser',
                            dataTypeDetails: {
                                node: '@sage/xtrem-core/TestUser',
                                type: 'reference',
                                title: 'Test user',
                                value: {
                                    bind: 'email',
                                    title: 'Email',
                                    type: 'string',
                                    enumType: null,
                                },
                                helperText: {
                                    bind: 'email',
                                    title: 'Email',
                                    type: 'string',
                                    enumType: null,
                                },
                                isDefault: false,
                                columns: [
                                    {
                                        bind: 'email',
                                        title: 'Email',
                                        type: 'string',
                                        enumType: null,
                                    },
                                ],
                                precision: null,
                                scale: null,
                                roundingMode: null,
                                maxLength: null,
                                isLocalized: null,
                                doNotTrim: null,
                                truncate: null,
                                values: null,
                                allowedContentTypes: null,
                            },
                            targetNode: '@sage/xtrem-core/TestUser',
                            isOnInputType: true,
                            isOnOutputType: true,
                            isMutable: false,
                            isSystemProperty: true,
                        },
                        {
                            title: 'Create user',
                            name: '_createUser',
                            canSort: true,
                            canFilter: true,
                            enumType: null,
                            type: 'reference',
                            isCustom: false,
                            dataType: 'testUser',
                            dataTypeDetails: {
                                node: '@sage/xtrem-core/TestUser',
                                type: 'reference',
                                title: 'Test user',
                                value: {
                                    bind: 'email',
                                    title: 'Email',
                                    type: 'string',
                                    enumType: null,
                                },
                                helperText: {
                                    bind: 'email',
                                    title: 'Email',
                                    type: 'string',
                                    enumType: null,
                                },
                                isDefault: false,
                                columns: [
                                    {
                                        bind: 'email',
                                        title: 'Email',
                                        type: 'string',
                                        enumType: null,
                                    },
                                ],
                                precision: null,
                                scale: null,
                                roundingMode: null,
                                maxLength: null,
                                isLocalized: null,
                                doNotTrim: null,
                                truncate: null,
                                values: null,
                                allowedContentTypes: null,
                            },
                            targetNode: '@sage/xtrem-core/TestUser',
                            isOnInputType: true,
                            isOnOutputType: true,
                            isMutable: false,
                            isSystemProperty: true,
                        },
                        {
                            title: 'Custom data',
                            name: '_customData',
                            canSort: true,
                            canFilter: true,
                            enumType: null,
                            type: 'json',
                            isCustom: false,
                            dataType: '_jsonDataType',
                            dataTypeDetails: {
                                node: null,
                                type: 'json',
                                title: 'Json data type',
                                value: null,
                                helperText: null,
                                isDefault: false,
                                columns: null,
                                precision: null,
                                scale: null,
                                roundingMode: null,
                                maxLength: null,
                                isLocalized: null,
                                doNotTrim: null,
                                truncate: null,
                                values: null,
                                allowedContentTypes: null,
                            },
                            targetNode: '',
                            isOnInputType: true,
                            isOnOutputType: true,
                            isMutable: false,
                            isSystemProperty: true,
                        },
                        {
                            title: 'Source id',
                            name: '_sourceId',
                            canSort: true,
                            canFilter: true,
                            enumType: null,
                            type: 'string',
                            isCustom: false,
                            dataType: '_sourceIdDataType',
                            dataTypeDetails: {
                                node: null,
                                type: 'string',
                                title: 'Source id data type',
                                value: null,
                                helperText: null,
                                isDefault: false,
                                columns: null,
                                precision: null,
                                scale: null,
                                roundingMode: null,
                                maxLength: 128,
                                isLocalized: false,
                                doNotTrim: false,
                                truncate: false,
                                values: null,
                                allowedContentTypes: null,
                            },
                            targetNode: '',
                            isOnInputType: true,
                            isOnOutputType: true,
                            isMutable: false,
                            isSystemProperty: true,
                        },
                        {
                            title: '_id',
                            name: '_id',
                            canSort: true,
                            canFilter: true,
                            enumType: null,
                            type: 'integer',
                            isCustom: false,
                            dataType: '',
                            dataTypeDetails: null,
                            targetNode: '',
                            isOnInputType: true,
                            isOnOutputType: true,
                            isMutable: false,
                            isSystemProperty: true,
                        },
                        {
                            title: 'Code',
                            name: 'code',
                            canSort: true,
                            canFilter: true,
                            enumType: null,
                            type: 'string',
                            isCustom: false,
                            dataType: 'codeDataType',
                            dataTypeDetails: {
                                node: null,
                                type: 'string',
                                title: 'Code data type',
                                value: null,
                                helperText: null,
                                isDefault: false,
                                columns: null,
                                precision: null,
                                scale: null,
                                roundingMode: null,
                                maxLength: 32,
                                isLocalized: false,
                                doNotTrim: false,
                                truncate: false,
                                values: null,
                                allowedContentTypes: null,
                            },
                            targetNode: '',
                            isOnInputType: true,
                            isOnOutputType: true,
                            isMutable: false,
                            isSystemProperty: false,
                        },
                        {
                            title: 'Computed last',
                            name: 'computedLast',
                            canSort: false,
                            canFilter: false,
                            enumType: null,
                            type: 'string',
                            isCustom: false,
                            dataType: 'codeDataType',
                            dataTypeDetails: {
                                node: null,
                                type: 'string',
                                title: 'Code data type',
                                value: null,
                                helperText: null,
                                isDefault: false,
                                columns: null,
                                precision: null,
                                scale: null,
                                roundingMode: null,
                                maxLength: 32,
                                isLocalized: false,
                                doNotTrim: false,
                                truncate: false,
                                values: null,
                                allowedContentTypes: null,
                            },
                            targetNode: '',
                            isOnInputType: false,
                            isOnOutputType: true,
                            isMutable: false,
                            isSystemProperty: false,
                        },
                        {
                            title: 'Children',
                            name: 'children',
                            canSort: false,
                            canFilter: false,
                            enumType: null,
                            type: 'collection',
                            isCustom: false,
                            dataType: 'testVitalCollectionChild',
                            dataTypeDetails: {
                                node: '@sage/xtrem-core/TestVitalCollectionChild',
                                type: 'reference',
                                title: 'Test vital collection child',
                                value: {
                                    bind: 'parent.code',
                                    title: 'Code',
                                    type: 'string',
                                    enumType: null,
                                },
                                helperText: {
                                    bind: 'parent.code',
                                    title: 'Code',
                                    type: 'string',
                                    enumType: null,
                                },
                                isDefault: false,
                                columns: [
                                    {
                                        bind: 'parent.code',
                                        title: 'Code',
                                        type: 'string',
                                        enumType: null,
                                    },
                                    {
                                        bind: '_sortValue',
                                        title: 'Sort value',
                                        type: 'integer',
                                        enumType: null,
                                    },
                                ],
                                precision: null,
                                scale: null,
                                roundingMode: null,
                                maxLength: null,
                                isLocalized: null,
                                doNotTrim: null,
                                truncate: null,
                                values: null,
                                allowedContentTypes: null,
                            },
                            targetNode: '@sage/xtrem-core/TestVitalCollectionChild',
                            isOnInputType: true,
                            isOnOutputType: true,
                            isMutable: true,
                            isSystemProperty: false,
                        },
                        {
                            title: 'Etag',
                            name: '_etag',
                            canSort: false,
                            canFilter: false,
                            enumType: null,
                            type: 'string',
                            isCustom: false,
                            dataType: '_etagDataType',
                            dataTypeDetails: {
                                node: null,
                                type: 'string',
                                title: 'Etag data type',
                                value: null,
                                helperText: null,
                                isDefault: false,
                                columns: null,
                                precision: null,
                                scale: null,
                                roundingMode: null,
                                maxLength: 100,
                                isLocalized: false,
                                doNotTrim: false,
                                truncate: false,
                                values: null,
                                allowedContentTypes: null,
                            },
                            targetNode: '',
                            isOnInputType: false,
                            isOnOutputType: true,
                            isMutable: false,
                            isSystemProperty: true,
                        },
                    ],
                    queries: [],
                    mutations: [],
                },
            },
        });
    });

    after(() => restoreTables());
});
