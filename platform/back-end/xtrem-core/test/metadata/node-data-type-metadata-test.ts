import { AnyValue } from '@sage/xtrem-shared';
import { assert } from 'chai';
import { Application, initTables } from '../../lib';
import * as dataTypes from '../fixtures/data-types/_index';
import {
    testDocumentDataType,
    testDocumentLineDataType,
    testNodeDetailsDataType,
    testReferenceDataType,
} from '../fixtures/data-types/reference-data-types';
import * as enums from '../fixtures/enums';
import { createApplicationWithApi, GraphQlHelper, graphqlMetadataSetup, restoreTables } from '../fixtures/index';
import {
    TestChildReferenceDataType,
    TestDatatypes,
    TestDocument,
    TestDocumentLine,
    TestGrandChildReferenceDataType,
    TestNodeDetails,
    TestNoReferenceDataType,
    TestReferenceDataType,
    TestReferred,
} from '../fixtures/nodes';
import { serviceOption1, serviceOption2, serviceOption3, serviceOption4 } from '../fixtures/service-options';

let graphqlHelper: GraphQlHelper;
let application: Application;

describe('getDataType', () => {
    before(async () => {
        application = await createApplicationWithApi({
            nodes: {
                TestChildReferenceDataType,
                TestGrandChildReferenceDataType,
                TestReferenceDataType,
                TestNoReferenceDataType,
                TestDatatypes,
                TestDocument,
                TestReferred,
                TestDocumentLine,
                TestNodeDetails,
            },
            serviceOptions: { serviceOption1, serviceOption2, serviceOption3, serviceOption4 },
            dataTypes: {
                ...dataTypes,
                testDocumentDataType,
                testDocumentLineDataType,
                testNodeDetailsDataType,
                testReferenceDataType,
            },
            enums,
        });
        graphqlHelper = await graphqlMetadataSetup({ application });
        await initTables([]);
    });

    it('should get a reference data type', async () => {
        const result = await graphqlHelper.execute<{ getDataType: { name: string }[] }>(
            `
            {
                getDataType(dataTypeNames: ["testReferenceDataType","testNoReferenceDataType","codeDataType","testEnumDataType","testEnumForArrayDataType","descriptionDataType","descriptionArrayDataType","defaultDecimalDataType","testTextStreamType","mailTemplateType","testJsonDataType"]) {
                    name
                    title
                    type
                    isDefault
                    node
                    value {
                        bind
                        title
                        type
                        enumType
                        }
                    helperText {
                        bind
                        title
                        type
                        enumType
                        }
                    columns {
                        bind
                        title
                        type
                        enumType
                        }
                    values {
                        value
                        title
                    }
                    maxLength
                    isLocalized
                    doNotTrim
                    truncate
                    allowedContentTypes
                    precision
                    scale
                    roundingMode
                }
              }
            `,
        );

        const expectedDataTypes = [
            {
                name: 'codeDataType',
                title: 'Code data type',
                type: 'string',
                isDefault: false,
                node: null,
                value: null,
                helperText: null,
                columns: null,
                values: null,
                maxLength: 32,
                isLocalized: false,
                doNotTrim: false,
                truncate: false,
                allowedContentTypes: null,
                precision: null,
                scale: null,
                roundingMode: null,
            },
            {
                name: 'descriptionDataType',
                title: 'Description data type',
                type: 'string',
                isDefault: false,
                node: null,
                value: null,
                helperText: null,
                columns: null,
                values: null,
                maxLength: 250,
                isLocalized: false,
                doNotTrim: false,
                truncate: false,
                allowedContentTypes: null,
                precision: null,
                scale: null,
                roundingMode: null,
            },
            {
                name: 'descriptionArrayDataType',
                title: 'Description array data type',
                type: 'stringArray',
                isDefault: false,
                node: null,
                value: null,
                helperText: null,
                columns: null,
                values: null,
                maxLength: 250,
                isLocalized: false,
                doNotTrim: false,
                truncate: false,
                allowedContentTypes: null,
                precision: null,
                scale: null,
                roundingMode: null,
            },
            {
                name: 'defaultDecimalDataType',
                title: 'Default decimal data type',
                type: 'decimal',
                isDefault: false,
                node: null,
                value: null,
                helperText: null,
                columns: null,
                values: null,
                maxLength: null,
                isLocalized: null,
                doNotTrim: null,
                truncate: null,
                allowedContentTypes: null,
                precision: 9,
                scale: 3,
                roundingMode: 'roundHalfUp',
            },
            {
                name: 'testReferenceDataType',
                title: 'Test reference data type',
                type: 'reference',
                isDefault: false,
                node: '@sage/xtrem-core/TestReferenceDataType',
                value: {
                    bind: 'code',
                    title: 'Code',
                    type: 'string',
                    enumType: null,
                },
                helperText: {
                    bind: 'name',
                    title: 'Name',
                    type: 'string',
                    enumType: null,
                },
                columns: [
                    {
                        bind: 'code',
                        title: 'Code',
                        type: 'string',
                        enumType: null,
                    },
                    {
                        bind: 'name',
                        title: 'Name',
                        type: 'string',
                        enumType: null,
                    },
                    {
                        bind: 'child.childCode',
                        title: 'Child code',
                        type: 'string',
                        enumType: null,
                    },
                    {
                        bind: 'child.grandChild.grandChildCode',
                        title: 'Grand child code',
                        type: 'string',
                        enumType: null,
                    },
                ],
                values: null,
                maxLength: null,
                isLocalized: null,
                doNotTrim: null,
                truncate: null,
                allowedContentTypes: null,
                precision: null,
                scale: null,
                roundingMode: null,
            },
            {
                name: 'testNoReferenceDataType',
                title: 'Test no reference data type',
                type: 'reference',
                isDefault: false,
                node: '@sage/xtrem-core/TestNoReferenceDataType',
                value: {
                    bind: 'code',
                    title: 'Code',
                    type: 'string',
                    enumType: null,
                },
                helperText: {
                    bind: 'code',
                    title: 'Code',
                    type: 'string',
                    enumType: null,
                },
                columns: [
                    {
                        bind: 'code',
                        title: 'Code',
                        type: 'string',
                        enumType: null,
                    },
                    {
                        bind: 'ref.code',
                        title: 'Code',
                        type: 'string',
                        enumType: null,
                    },
                ],
                values: null,
                maxLength: null,
                isLocalized: null,
                doNotTrim: null,
                truncate: null,
                allowedContentTypes: null,
                precision: null,
                scale: null,
                roundingMode: null,
            },
            {
                name: 'testEnumDataType',
                title: 'Test enum enum',
                type: 'enum',
                isDefault: false,
                node: null,
                value: null,
                helperText: null,
                columns: null,
                values: [
                    {
                        value: 'value1',
                        title: 'Value 1',
                    },
                    {
                        value: 'value2',
                        title: 'Value 2',
                    },
                    {
                        value: 'value3',
                        title: 'Value 3',
                    },
                    {
                        value: 'value4',
                        title: 'Value 4',
                    },
                ],
                maxLength: null,
                isLocalized: null,
                doNotTrim: null,
                truncate: null,
                allowedContentTypes: null,
                precision: null,
                scale: null,
                roundingMode: null,
            },
            {
                name: 'testEnumForArrayDataType',
                title: 'Test enum for array enum',
                type: 'enum',
                isDefault: false,
                node: null,
                value: null,
                helperText: null,
                columns: null,
                values: [
                    {
                        value: 'arrayVal1',
                        title: 'Array val 1',
                    },
                    {
                        value: 'arrayVal2',
                        title: 'Array val 2',
                    },
                    {
                        value: 'arrayVal3',
                        title: 'Array val 3',
                    },
                ],
                maxLength: null,
                isLocalized: null,
                doNotTrim: null,
                truncate: null,
                allowedContentTypes: null,
                precision: null,
                scale: null,
                roundingMode: null,
            },
        ];

        // sort result.data.getDataType and expectedDataTypes by name to ensure consistent order for comparison
        result.data?.getDataType?.sort((a, b) => a.name.localeCompare(b.name));
        expectedDataTypes.sort((a, b) => a.name.localeCompare(b.name));

        assert.deepEqual(result, {
            data: {
                getDataType: expectedDataTypes,
            },
        } as AnyValue);
    });
    it('should return null if non existing data type', async () => {
        const result = await graphqlHelper.execute(
            `
            {
                getDataType(dataTypeNames: ["nonExistingDataType"]) {
                    name
                    title
                    type
                    isDefault
                }
              }
            `,
        );
        assert.deepEqual(result, {
            data: {
                getDataType: null,
            },
        });
    });
    after(() => restoreTables());
});
