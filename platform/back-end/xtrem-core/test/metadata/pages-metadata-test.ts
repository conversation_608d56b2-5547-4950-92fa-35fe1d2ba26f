import { assert } from 'chai';
import { Application, initTables } from '../../lib';
import { Test } from '../../lib/test';
import { GraphQlHelper, createApplicationWithApi, graphqlMetadataSetup, restoreTables } from '../fixtures/index';
import { TestDatatypes } from '../fixtures/nodes';
import { serviceOption1, serviceOption2, serviceOption3, serviceOption4 } from '../fixtures/service-options';

let graphqlHelper: GraphQlHelper;
let application: Application;

describe('page metadata queries', () => {
    before(async () => {
        application = await createApplicationWithApi({
            nodes: { TestDatatypes },
            serviceOptions: { serviceOption1, serviceOption2, serviceOption3, serviceOption4 },
        });
        graphqlHelper = await graphqlMetadataSetup({ application });
        await initTables([]);
    });

    it('Should return all pages with access and pageAccess', () => graphqlHelper.runScenario('page-access'));

    it('Should not return any pages extensions as user it is unavailable', () =>
        graphqlHelper.runScenario('page-extensions', {
            graphqlOptions: { userEmail: '<EMAIL>' },
        }));

    it('Should return stickers', async () => {
        const result = await graphqlHelper.execute('{ stickers {title, key} }');
        assert.deepEqual(result, {
            data: {
                stickers: [
                    {
                        key: '@sage/xtrem-core/Sticker1',
                        title: 'sticker1 title',
                    },
                    {
                        key: '@sage/xtrem-core/Sticker2',
                        title: 'sticker2 title',
                    },
                ],
            },
        });
    });
    it('Pages should have content', async () => {
        const result = await graphqlHelper.execute<{ pages: { content: string }[] }>('{ pages {title, key, content} }');
        const data = result.data!;
        assert.isDefined(data.pages[0].content);
        assert.isString(data.pages[0].content);
    });

    it('Should return packages', async () => {
        const result = await graphqlHelper.execute<{
            installedPackages: { name: string; version: string }[];
        }>('{ installedPackages {name, version} }');
        const data = result.data!;
        assert.isString(data.installedPackages[0].name);
        assert.isTrue(data.installedPackages.some(p => p.name === '@sage/xtrem-core'));
    });

    it('Should return pages for a specified node', () => graphqlHelper.runScenario('node-pages'));

    it('Should return service options', () =>
        Test.withContext(
            async context => {
                const result = await graphqlHelper.execute('{ serviceOptions {name, package, status, isActive} }', {
                    context,
                });
                assert.deepEqual(result, {
                    data: {
                        serviceOptions: [
                            {
                                name: 'option1',
                                package: '@sage/xtrem-core',
                                status: 'released',
                                isActive: true,
                            },
                            {
                                name: 'option2',
                                package: '@sage/xtrem-core',
                                status: 'experimental',
                                isActive: true,
                            },
                            {
                                name: 'option3',
                                package: '@sage/xtrem-core',
                                status: 'workInProgress',
                                isActive: false,
                            },
                            {
                                name: 'option4',
                                package: '@sage/xtrem-core',
                                status: 'workInProgress',
                                isActive: false,
                            },
                        ],
                    },
                });
            },
            {
                testActiveServiceOptions: [serviceOption1, serviceOption2],
            },
        ));

    it('Should return service options', async () => {
        const result = await graphqlHelper.execute('{ serviceOptions {name, package, status, isActive} }');
        assert.deepEqual(result, {
            data: {
                serviceOptions: [
                    { name: 'option1', package: '@sage/xtrem-core', status: 'released', isActive: false },
                    { name: 'option2', package: '@sage/xtrem-core', status: 'experimental', isActive: false },
                    { name: 'option3', package: '@sage/xtrem-core', status: 'workInProgress', isActive: false },
                    { name: 'option4', package: '@sage/xtrem-core', status: 'workInProgress', isActive: false },
                ],
            },
        });
    });

    it('Should return about information', async () => {
        const result = await graphqlHelper.execute(
            '{ about {application { name, version, author, description, license, buildStamp } , root { name, version, author, tenantId, description, license, buildStamp} } }',
        );
        assert.deepEqual(result, {
            data: {
                about: {
                    application: application.about,
                    root: { ...application.rootAbout, tenantId: Test.defaultTenantId },
                },
            },
        });
    });

    after(() => restoreTables());
});
