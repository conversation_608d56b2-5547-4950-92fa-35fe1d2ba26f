// tslint:disable: no-unused-expression
import { asyncArray } from '@sage/xtrem-async-helper';
import { assert } from 'chai';
import { fixtures, testDocumentWithTransientApplication } from '..';
import { SchemaSqlContext } from '../../lib/sql/sql-context/schema-sql-context';
import { Test } from '../../lib/test/test';
import {
    datatypesData,
    documentData,
    documentLineData,
    initTables,
    referredData,
    referringData,
    restoreTables,
    setup,
} from '../fixtures/index';
import { checkSchemaExists, cleanEnums } from './fixtures';

describe('table sql context', () => {
    beforeEach(async () => {
        await setup({ application: await testDocumentWithTransientApplication.application });
        await initTables([
            { nodeConstructor: fixtures.nodes.TestDatatypes, data: datatypesData },
            { nodeConstructor: fixtures.nodes.TestReferred, data: referredData },
            { nodeConstructor: fixtures.nodes.TestReferring, data: referringData },
            { nodeConstructor: fixtures.nodes.TestDocument, data: documentData },
            { nodeConstructor: fixtures.nodes.TestDocumentLine, data: documentLineData },
        ]);
        await cleanEnums();
    });

    after(() => restoreTables());

    it('can create and drop a schema', () =>
        Test.uncommitted(async context => {
            const oldName = context.application.schemaName;
            context.application.schemaName = 'sql_helper_schema';
            const sqlContext = new SchemaSqlContext(context.application);
            await sqlContext.createSchema();
            assert(await checkSchemaExists(sqlContext, 'sql_helper_schema'), 'schema creation failed');
            await sqlContext.dropSchema();
            assert(!(await checkSchemaExists(sqlContext, 'sql_helper_schema')), 'schema deletion failed');
            context.application.schemaName = oldName;
        }));

    it('can check if a table exists', () =>
        Test.uncommitted(async context => {
            const sqlContext = new SchemaSqlContext(context.application);
            assert(await sqlContext.tableExists('test_referred'));
            assert(!(await sqlContext.tableExists('test_i_don_t_exist')));
        }));

    it('can drop a table', () =>
        Test.uncommitted(async context => {
            const sqlContext = new SchemaSqlContext(context.application);
            assert(await sqlContext.tableExists('test_referred'), 'test_referred does not exist');
            await sqlContext.dropTable('test_referred', { isCascade: true, errorsAsWarnings: false });
            assert(!(await sqlContext.tableExists('test_referred')), 'test_referred was not dropped');
        }));

    it('can drop a list of tables', () =>
        Test.uncommitted(async context => {
            const sqlContext = new SchemaSqlContext(context.application);
            const tables = ['test_referred', 'test_referring', 'test_document', 'test_document_line'];
            await asyncArray(tables).forEach(async t => assert(await sqlContext.tableExists(t), `${t} does not exist`));
            await sqlContext.dropTables(tables, { isCascade: true, errorsAsWarnings: false });
            await asyncArray(tables).forEach(async t =>
                assert(!(await sqlContext.tableExists(t)), `${t} was not dropped`),
            );
        }));
});
