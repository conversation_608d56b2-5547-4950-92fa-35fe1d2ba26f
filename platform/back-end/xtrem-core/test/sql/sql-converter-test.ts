import { sourceMapSetup } from '@sage/xtrem-log';
import { assert } from 'chai';
import { AnyValue, Context, Node, StringDataType, Test, decorators } from '../../lib/index';
import { createApplicationWithApi, setup } from '../fixtures/index';

import { SqlConverter } from '../../lib/sql/mapper/sql-converter';

sourceMapSetup();

const context: any = {};

@decorators.node<TestSimpleNode>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
})
export class TestSimpleNode extends Node {
    @decorators.booleanProperty<TestSimpleNode, 'booleanVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly booleanVal: Promise<boolean | null>;

    @decorators.stringProperty<TestSimpleNode, 'stringVal'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 50 }),
    })
    readonly stringVal: Promise<string>;
}

// type StaticThis<T> = new () => T;

function test(fn: () => any, expected: string, expectedArgs: AnyValue[]) {
    const converter = new SqlConverter(context, Test.application.getFactoryByConstructor(TestSimpleNode));

    const result = converter.convertFunction(fn).sql;
    assert.equal(result, expected);
    assert.deepEqual(
        converter.sqlParameters.map(param => param.literalValue),
        expectedArgs,
    );
}

describe('SQL Converter', () => {
    before(async () =>
        setup({
            application: await createApplicationWithApi({
                nodes: {
                    TestSimpleNode,
                },
            }),
        }),
    );

    it('can convert Context.getConfigurationValue', () => {
        test(
            () => {
                return Context.getConfigurationValue('serviceOptionsLevel');
            },
            '$1',
            [Context.getConfigurationValue('serviceOptionsLevel')],
        );
    });
});
