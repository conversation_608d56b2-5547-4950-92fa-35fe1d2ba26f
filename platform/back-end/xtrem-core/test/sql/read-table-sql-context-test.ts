// tslint:disable: no-unused-expression
import { assert } from 'chai';
import { fixtures, testDocumentWithTransientApplication } from '..';
import { ReadTableSqlContext } from '../../lib/sql/sql-context/read-table-sql-context';
import { parseColumnDefinition } from '../../lib/sql/statements/types-conversion';
import { Test } from '../../lib/test';
import {
    datatypesData,
    documentData,
    documentLineData,
    initTables,
    referredData,
    referringData,
    restoreTables,
    setup,
} from '../fixtures/index';
import { cleanEnums } from './fixtures/setup';

describe('read table sql context', () => {
    beforeEach(async () => {
        await setup({ application: await testDocumentWithTransientApplication.application });
        await initTables([
            { nodeConstructor: fixtures.nodes.TestDatatypes, data: datatypesData },
            { nodeConstructor: fixtures.nodes.TestReferred, data: referredData },
            { nodeConstructor: fixtures.nodes.TestReferring, data: referringData },
            { nodeConstructor: fixtures.nodes.TestDocument, data: documentData },
            { nodeConstructor: fixtures.nodes.TestDocumentLine, data: documentLineData },
        ]);
        await cleanEnums();
    });

    after(() => restoreTables());

    it('can read table schemas', () =>
        Test.withContext(async context => {
            const sqlContext = new ReadTableSqlContext(context.application);
            const tableDef = await sqlContext.readSchema('test_datatypes', { skipColumns: false });
            assert.strictEqual(tableDef.schemaName, 'xtrem_core_test');
            assert.strictEqual(tableDef.tableName, 'test_datatypes');
            assert.strictEqual(JSON.stringify(tableDef.primaryKey), JSON.stringify({ columns: ['_tenant_id', '_id'] }));

            // first 2 columns of table are specific system columns
            assert.deepEqual(tableDef.columns?.map(c => c.name).splice(0, 2), ['_tenant_id', '_id']);

            // last 7 columns of table are the remainder of the system columns
            assert.deepEqual(tableDef.columns?.map(c => c.name).splice(-7, 7), [
                '_create_user',
                '_update_user',
                '_create_stamp',
                '_update_stamp',
                '_update_tick',
                '_source_id',
                '_custom_data',
            ]);
        }));

    it('can parse a column definition', () =>
        Test.uncommitted(async context => {
            const table = (await testDocumentWithTransientApplication.application)
                .getSqlPackageFactories()
                .filter(t => t.tableName === 'test_datatypes')
                .map(f => f.table)[0];

            const column = table.columns.filter(c => c.propertyName === 'booleanVal')[0];
            const columnDef = table.getColumnDefinition(column);
            const columnDefParsed = parseColumnDefinition(context.schemaName, table.name, columnDef);

            assert.strictEqual(columnDefParsed.columnName, 'boolean_val');
            assert.strictEqual(columnDefParsed.typeName, 'BOOL');
        }));
});
