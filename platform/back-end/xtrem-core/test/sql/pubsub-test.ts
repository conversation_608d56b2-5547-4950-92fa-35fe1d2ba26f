import { asyncArray } from '@sage/xtrem-async-helper';
import { assert } from 'chai';
import { spawn } from 'child_process';
import { sortBy } from 'lodash';
import { hostname } from 'os';
import { setTimeout as delay } from 'timers/promises';
import { testBasicDocumentApplication } from '..';
import { Test } from '../../lib';
import { PubSub } from '../../lib/sql/pubsub';
import { initTables, referredData, restoreTables, setup } from '../fixtures';
import { TestDocument, TestDocumentLine, TestReferred } from '../fixtures/nodes';

// It is about 4ms to get the pubsub listener notified, so a 150ms delay is good enough.
// No need for a 1 second delay which considerably slows down the unit test.
const delayMs = 150;

async function listener(data: any): Promise<void> {
    // data is already parsed as the default parser in pg-listen is JSON.parse
    await Test.withCommittedContext(async context => {
        const doc1 = await context.create(TestDocument, {
            code: `${data.topic}${data.code}`,
            description: `${data.description} ${data.topic}`,
            mandatoryReference: 1,
        });
        await doc1.$.save();
    });
}

const testData = (topic: string): any[] => [
    { topic, code: '1', description: 'one' },
    { topic, code: '2', description: 'two' },
    { topic, code: '3', description: 'three' },
    { topic, code: '4', description: 'four' },
];

const completeResult = (data: any) => ({
    ...data,
    mandatoryReference: {},
    lines: [],
    computedLines: [],
    filteredLines: [],
    filteredLinesWithReverseReference: [],
    _customData: {},
});

const testResults = (topic: string): any[] =>
    [
        {
            code: `${topic}1`,
            description: `one ${topic}`,
        },
        {
            code: `${topic}2`,
            description: `two ${topic}`,
        },
        {
            code: `${topic}3`,
            description: `three ${topic}`,
        },
        {
            code: `${topic}4`,
            description: `four ${topic}`,
        },
    ].map(completeResult);

describe('pubsub tests', () => {
    before(async () => setup({ application: await testBasicDocumentApplication.application }));

    beforeEach(() =>
        initTables([
            { nodeConstructor: TestReferred, data: referredData },
            {
                nodeConstructor: TestDocumentLine,
                data: [],
            },
            {
                nodeConstructor: TestDocument,
                data: [],
            },
        ]),
    );

    it('can listen for broadcasts on the same process', async () => {
        const topic = 'test-topic1';
        await PubSub.subscribe(topic, listener);
        const inputs = testData(topic);

        await asyncArray(inputs).forEach(async input => {
            await Test.withCommittedContext(context => PubSub.publish(context, topic, input));

            await delay(delayMs);
        });

        await Test.withReadonlyContext(async context => {
            const results = await context
                .query(TestDocument, { orderBy: { code: +1 } })
                .map(node => node.$.payload())
                .toArray();
            assert.deepEqual(results, testResults(topic));
        });
    });

    it('can stop subscription on specific topics', async () => {
        const topic = 'test-topic1';
        await Test.withReadonlyContext(async context => {
            const results = await context
                .query(TestDocument)
                .map(node => node.$.payload())
                .toArray();
            assert.deepEqual(results, []);
        });
        await PubSub.subscribe(topic, listener);

        const inputs = testData(topic);

        await asyncArray(inputs).forEach(async input => {
            await Test.withCommittedContext(context => PubSub.publish(context, topic, input));

            await delay(delayMs);
        });

        await Test.withReadonlyContext(async context => {
            const results = await context
                .query(TestDocument)
                .map(node => node.$.payload())
                .toArray();
            assert.deepEqual(results, testResults(topic));
        });

        await PubSub.unsubscribe(topic);

        // channel is closed , but we try to publish once again
        await Test.withCommittedContext(context =>
            PubSub.publish(context, topic, {
                tenantId: Test.defaultTenantId,
                topic,
                code: '5',
                description: 'five',
            }),
        );

        await delay(delayMs);

        // We re-query node and same result is retrieved last publish was not inserted, as expected
        await Test.withReadonlyContext(async context => {
            const results = await context
                .query(TestDocument)
                .map(node => node.$.payload())
                .toArray();
            assert.deepEqual(results, testResults(topic));
        });
    });

    it('can stop subscriptions on all topics', async () => {
        const topic1 = 'test-topic1';
        await PubSub.subscribe(topic1, listener);

        const topic2 = 'test-topic2';
        await PubSub.subscribe(topic2, listener);

        const testData1 = testData(topic1);
        const testData2 = testData(topic2);

        const testResults1 = testResults(topic1);
        const testResults2 = testResults(topic2);

        await Test.withCommittedContext(context => PubSub.publish(context, topic1, testData1[0]));

        await delay(delayMs);

        await Test.withCommittedContext(context => PubSub.publish(context, topic2, testData2[1]));

        await delay(delayMs);

        await Test.withReadonlyContext(async context => {
            const results = await context
                .query(TestDocument)
                .map(node => node.$.payload())
                .toArray();
            assert.deepEqual(results, [testResults1[0], testResults2[1]]);
        });

        await PubSub.unsubscribeAll();

        await Test.withCommittedContext(context => PubSub.publish(context, topic1, testData1[2]));

        await delay(delayMs);

        await Test.withCommittedContext(context => PubSub.publish(context, topic2, testData2[3]));

        await delay(delayMs);

        await Test.withReadonlyContext(async context => {
            const results = await context
                .query(TestDocument)
                .map(node => node.$.payload())
                .toArray();
            assert.deepEqual(results, [testResults1[0], testResults2[1]]);
        });
    });

    it('can exclude current process from processing broadcast notification', async () => {
        const topic = 'test-topic1';
        await PubSub.subscribe(topic, listener);
        const inputs = testData(topic);

        await asyncArray(inputs).forEach(async input => {
            await Test.withCommittedContext(context => PubSub.publish(context, topic, input, { excludeSelf: true }));

            await delay(delayMs);
        });

        await Test.withReadonlyContext(async context => {
            const results = await context
                .query(TestDocument)
                .map(node => node.$.payload())
                .toArray();
            assert.deepEqual(results, []);
        });
    });

    it('will not publish if transaction is rolled back ', async () => {
        const topic = 'test-topic1';
        await PubSub.subscribe(topic, listener);
        const inputs = testData(topic);

        await asyncArray(inputs).forEach(async input => {
            // all of these broadcasts should not trigger any events and nothing will be inserted into table
            await Test.withUncommittedContext(context => PubSub.publish(context, topic, input));

            await delay(delayMs);
        });

        await Test.withReadonlyContext(async context => {
            const results = await context
                .query(TestDocument)
                .map(node => node.$.payload())
                .toArray();
            assert.deepEqual(results, []);
        });
    });

    it('will subscribe and publish between multiple processes ', async () => {
        const child = spawn('pwd');

        // Parent process is the only process subscribed to 'child' topic
        await PubSub.subscribe('child', listener);

        child.on('message', (message: any) => {
            (async () => {
                if (message.subscribe) {
                    // child process is the only process subscribed to 'parent' topic
                    await PubSub.subscribe('parent', listener);
                } else if (message.publish) {
                    const inputs = testData('child');

                    await asyncArray(inputs).forEach(async input => {
                        // publish to parent process on 'child' topic
                        await Test.withCommittedContext(context => PubSub.publish(context, 'child', input));

                        await delay(delayMs);
                    });
                }
            })().catch(err => console.error(err));
        });

        // instruct child process to subscribe
        child.emit('message', { subscribe: true });

        // instruct child process to publish
        child.emit('message', { publish: true });

        const inputs = testData('parent');

        await asyncArray(inputs).forEach(async input => {
            // broadcast to child process on 'parent' topic
            // parent process will not receive this notification as it is not subscribed to the parent topic
            await Test.withCommittedContext(context => PubSub.publish(context, 'parent', input));

            await delay(delayMs);
        });

        const result = sortBy([...testResults('child'), ...testResults('parent')], 'code');

        await Test.withReadonlyContext(async context => {
            const results = sortBy(
                await context
                    .query(TestDocument)
                    .map(node => node.$.payload())
                    .toArray(),
                'code',
            );
            assert.deepEqual(results, result);
        });

        child.kill();
    });

    it('will throw an error if there are invalid characters in the topic', async () => {
        const topic = 'test-topic1&&';
        await assert.isRejected(
            PubSub.subscribe(topic, listener),
            'Invalid character(s) in topic, only alphabets, numbers, _, - and spaces are allowed, test-topic1&&',
        );
    });

    it('can subscribe and publish direct from PubSub class', async () => {
        const child = spawn('pwd');

        // Parent process is the only process subscribed to 'child' topic
        await PubSub.subscribe('child', listener);

        child.on('message', (message: any) => {
            (async () => {
                if (message.subscribe) {
                    // child process is the only process subscribed to 'parent' topic
                    await PubSub.subscribe('parent', listener);
                } else if (message.publish) {
                    const inputs = testData('child');

                    await asyncArray(inputs).forEach(async input => {
                        // publish to parent process on 'child' topic, as we are not passing in a context, publish will create its own committed context on the current process
                        await Test.withCommittedContext(context => PubSub.publish(context, 'child', input));

                        await delay(delayMs);
                    });
                }
            })().catch(err => console.error(err));
        });

        // instruct child process to subscribe
        child.emit('message', { subscribe: true });

        // instruct child process to publish
        child.emit('message', { publish: true });

        const inputs = testData('parent');

        await asyncArray(inputs).forEach(async input => {
            // broadcast to child process on 'parent' topic, as we are not passing in a context, publish will create its own committed context on the current process
            // parent process will not receive this notification as it is not subscribed to the parent channel
            await Test.withCommittedContext(context => PubSub.publish(context, 'parent', input));

            await delay(delayMs);
        });

        const result = sortBy([...testResults('child'), ...testResults('parent')], 'code');

        await Test.withReadonlyContext(async context => {
            const results = sortBy(
                await context
                    .query(TestDocument)
                    .map(node => node.$.payload())
                    .toArray(),
                'code',
            );
            assert.deepEqual(results, result);
        });

        process.on('exit', () => child.kill());
    });

    it('uses containerId passed to listener', async () => {
        const child = spawn('pwd');
        const channel = 'test-containerId-channel';

        const parentListener = async (data: any, container: string): Promise<void> => {
            if (container === `${hostname()}-${process.pid}`) {
                await listener({ topic: channel, code: 'source', description: data.message });
            } else {
                await listener({ topic: channel, code: 'other', description: 'I am not in source process' });
            }
        };

        await PubSub.subscribe(channel, parentListener);

        child.on('message', (message: any) => {
            const childListener = async (data: any, container: string): Promise<void> => {
                if (container === `${hostname()}-${child.pid}`) {
                    await listener({ topic: channel, code: 'source', description: data.message });
                } else {
                    await listener({ topic: channel, code: 'other', description: 'I am not in source process' });
                }
            };
            if (message.subscribe) {
                PubSub.subscribe('test-containerId-channel', childListener).catch(console.error);
            }
        });

        // instruct child process to subscribe
        child.emit('message', { subscribe: true });

        // broadcast message to parent and child that is subscribed to channel
        await Test.withCommittedContext(context =>
            PubSub.publish(context, channel, {
                tenantId: Test.defaultTenantId,
                message: 'I am in source process',
            }),
        );
        await delay(delayMs);

        await Test.withReadonlyContext(async context => {
            const results = await context
                .query(TestDocument, { orderBy: { code: -1 } })
                .map(node => node.$.payload())
                .toArray();
            assert.deepEqual(
                results,
                [
                    {
                        code: `${channel}source`,
                        description: `I am in source process ${channel}`,
                    },
                    {
                        code: `${channel}other`,
                        description: `I am not in source process ${channel}`,
                    },
                ].map(completeResult),
            );
        });

        process.on('exit', () => child.kill());
    });

    it('cannot publish with a non-writable context', async () => {
        const topic = 'test-topic1';
        await PubSub.subscribe(topic, listener);
        const input = testData(topic)[0];

        await Test.withReadonlyContext(async context => {
            await assert.isRejected(
                PubSub.publish(context, topic, input),
                'Cannot publish using a context that is not writable: test-topic1',
            );
        });

        await delay(delayMs);

        await Test.withReadonlyContext(async context => {
            const results = await context
                .query(TestDocument)
                .map(node => node.$.payload())
                .toArray();
            assert.deepEqual(results, []);
        });
    });

    afterEach(() => PubSub.unsubscribeAll());

    after(() => restoreTables());
});
