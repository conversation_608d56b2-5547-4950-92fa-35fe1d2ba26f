// tslint:disable: no-unused-expression
import { ColumnDefinition } from '@sage/xtrem-postgres';
import { assert } from 'chai';
import { fixtures, testDocumentWithTransientApplication } from '..';
import { ModifyTableSqlContext } from '../../lib/sql/sql-context/modify-table-sql-context';
import { SqlContext } from '../../lib/sql/sql-context/sql-context';
import { Test } from '../../lib/test';
import {
    datatypesData,
    documentData,
    documentLineData,
    initTables,
    referredData,
    referringData,
    restoreTables,
    setup,
} from '../fixtures/index';
import { checkTableExists, cleanEnums, dropTable } from './fixtures/setup';

const tableDef = {
    schemaName: 'xtrem_core_test',
    tableName: 'test_table_sql_helper',
    columns: [
        { name: 'code', maxLength: 20, type: 'string', isNullable: false },
        { name: 'number', type: 'integer', isNullable: false },
        { name: '_id', type: 'integer', isAutoIncrement: true },
    ] as ColumnDefinition[],
    primaryKey: { columns: ['_id'] },
    indexes: [],
};

describe('modify table sql context', () => {
    beforeEach(async () => {
        await setup({ application: await testDocumentWithTransientApplication.application });
        await initTables([
            { nodeConstructor: fixtures.nodes.TestDatatypes, data: datatypesData },
            { nodeConstructor: fixtures.nodes.TestReferred, data: referredData },
            { nodeConstructor: fixtures.nodes.TestReferring, data: referringData },
            { nodeConstructor: fixtures.nodes.TestDocument, data: documentData },
            { nodeConstructor: fixtures.nodes.TestDocumentLine, data: documentLineData },
        ]);
        await cleanEnums();
    });

    after(() => restoreTables());

    it('can create a table from a table definition', () =>
        Test.uncommitted(async context => {
            const sqlContext = new ModifyTableSqlContext(context.application, tableDef);
            await sqlContext.createTableFromTableDefinition();
            await checkTableExists(tableDef.tableName);
            await dropTable(tableDef.tableName);
        }));

    it('can give user rights to table', async () => {
        const getUserRights = (
            sqlContext: SqlContext,
        ): Promise<
            {
                table_catalog: string;
                table_schema: string;
                table_name: string;
                privilege_type: string;
            }[]
        > => {
            const sql = ` SELECT table_catalog, table_schema, table_name, privilege_type
                            FROM information_schema.table_privileges 
                           WHERE table_name = 'test_datatypes'
                             AND grantee = 'test_login'`;
            return sqlContext.executeSqlStatement({ sql });
        };
        await Test.uncommitted(async context => {
            const tableDefinition = (await testDocumentWithTransientApplication.application)
                .getSqlPackageFactories()
                .filter(t => t.tableName === 'test_datatypes')
                .map(f => f.table)[0]
                .getTableDefinition(context);
            const sqlContext = new ModifyTableSqlContext(context.application, tableDefinition);
            await sqlContext.executeSqlStatement({ sql: 'DROP ROLE IF EXISTS test_login' });
            await sqlContext.executeSqlStatement({ sql: 'CREATE ROLE test_login' });
            assert.equal((await getUserRights(sqlContext)).length, 0);
            await sqlContext.giveUserRightsToTable('test_login');
            const rights = (await getUserRights(sqlContext)).map(e => e.privilege_type);
            ['INSERT', 'SELECT', 'UPDATE', 'DELETE'].forEach(e => assert(rights.includes(e), `${e} was not granted.`));
        });
    });
});
