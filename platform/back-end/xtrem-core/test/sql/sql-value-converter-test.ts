import { sourceMapSetup } from '@sage/xtrem-log';
import { assert } from 'chai';
import { AnyValue, Node, Test, decorators } from '../../lib/index';
import { createApplicationWithApi, setup } from '../fixtures/index';

import { SqlValueConverter } from '../../lib/sql/mapper/sql-value-converter';

sourceMapSetup();

const context: any = {};

@decorators.node<TestSimpleNode>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
})
export class TestSimpleNode extends Node {
    @decorators.jsonProperty<TestSimpleNode, 'jsonVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly jsonVal: Promise<boolean | null>;
}

describe('SQL Value Converter', () => {
    before(async () =>
        setup({
            application: await createApplicationWithApi({
                nodes: {
                    TestSimpleNode,
                },
            }),
        }),
    );

    it('can convert value of json property', () => {
        const factory = Test.application.getFactoryByConstructor(TestSimpleNode);
        const column = factory.propertiesByName.jsonVal.column;
        const values = [{ foo: { bar: 'bar' } }, [{ foo: { bar: 'bar' } }, { bar: { foo: 'foo' } }]];
        const expectedValues = [values[0], JSON.stringify(values[1])];
        const actualValues: AnyValue = values.map(value =>
            SqlValueConverter.applyTransformationToValue(context, value, 'json', column),
        );

        assert.deepEqual(actualValues, expectedValues);
    });
});
