// tslint:disable: no-unused-expression
import { Datetime } from '@sage/xtrem-date-time';
import { assert } from 'chai';
import { omit } from 'lodash';
import { fixtures, testDocumentWithTransientApplication } from '..';
import { restoreTables } from '../../index';
import { SequenceSqlContext } from '../../lib/sql/sql-context/sequence-sql-context';
import { SqlContext } from '../../lib/sql/sql-context/sql-context';
import { Test } from '../../lib/test';
import {
    datatypesData,
    documentData,
    documentLineData,
    initTables,
    referredData,
    referringData,
    setup,
} from '../fixtures/index';
import { cleanEnums } from './fixtures/setup';

const testRecord = {
    ...omit(datatypesData[1], '_id'),
    id: 9999,
    jsonVal: JSON.stringify(datatypesData[1].jsonVal),
    _tenantId: Test.defaultTenantId,
    _updateTick: 1,
    _createStamp: Datetime.now(),
    _updateStamp: Datetime.now(),
    _createUser: 1,
    _updateUser: 1,
};

describe('sequence sql context', () => {
    beforeEach(async () => {
        await setup({ application: await testDocumentWithTransientApplication.application });
        await initTables([
            { nodeConstructor: fixtures.nodes.TestDatatypes, data: datatypesData },
            { nodeConstructor: fixtures.nodes.TestReferred, data: referredData },
            { nodeConstructor: fixtures.nodes.TestReferring, data: referringData },
            { nodeConstructor: fixtures.nodes.TestDocument, data: documentData },
            { nodeConstructor: fixtures.nodes.TestDocumentLine, data: documentLineData },
        ]);
        await cleanEnums();
    });

    after(() => restoreTables());

    it('can fix auto increment sequence', async () => {
        const getMaxId = async (sqlContext: SqlContext): Promise<number> => {
            const sql = 'SELECT max(_id) max_val from "xtrem_core_test"."test_datatypes";';
            return (await sqlContext.executeSqlStatement<{ max_val: number }[]>({ sql }))[0].max_val;
        };

        await Test.uncommitted(async context => {
            const sqlContext = new SequenceSqlContext(context.application, 'test_datatypes');
            assert.strictEqual(await getMaxId(sqlContext), 16);
            await sqlContext.fixAutoIncrementSequence(context.tenantId, '_id', { newMaxValue: 20 });
            const factory = context.application.getFactoryByTableName('test_datatypes');

            const id = (await factory.table.insert(context, testRecord))._id;

            assert.strictEqual(id, 21);
        });
    });
});
