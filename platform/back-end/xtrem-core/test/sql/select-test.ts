import { DateValue } from '@sage/xtrem-date-time';
import { assert } from 'chai';
import { Test } from '../../index';
import * as fixtures from '../fixtures';
import {
    createApplicationWithApi,
    documentData,
    documentLineData,
    initTables,
    referredData,
    restoreTables,
    setup,
} from '../fixtures/index';
import {
    TestAggDocument,
    TestAggDocumentBaseLine,
    TestAggDocumentExtendedLine,
    TestAggDocumentLine,
    TestAggDocumentReverseRef,
    TestDocument,
    TestDocumentLine,
    TestNaturalKey,
    TestReferred,
} from '../fixtures/nodes';

describe('context.select', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi({
                nodes: {
                    TestReferred,
                    TestDocument,
                    TestDocumentLine,
                    TestNaturalKey,
                    TestAggDocument,
                    TestAggDocumentLine,
                    TestAggDocumentExtendedLine,
                    TestAggDocumentBaseLine,
                    TestAggDocumentReverseRef,
                },
            }),
        });
        await initTables([
            { nodeConstructor: fixtures.nodes.TestReferred, data: referredData },
            { nodeConstructor: fixtures.nodes.TestDocument, data: documentData },
            { nodeConstructor: fixtures.nodes.TestDocumentLine, data: documentLineData },
            {
                nodeConstructor: fixtures.nodes.TestNaturalKey,
                data: [
                    { key: 'K1', text: 'text 1' },
                    { key: 'K2', text: 'text 2' },
                ],
            },
            // Last test uses these nodes because they have a date property and we want to check data type conversions in sub queries.
            { nodeConstructor: fixtures.nodes.TestAggDocument, data: fixtures.aggDocumentData },
            { nodeConstructor: fixtures.nodes.TestAggDocumentLine, data: fixtures.aggDocumentLineData },
        ]);
    });

    after(() => restoreTables());

    it('can select the array of _ids with `true` selector', () =>
        Test.withReadonlyContext(async context => {
            const result = await context.select(TestDocumentLine, true, {
                filter: { document: { code: 'DOCD' } },
            });
            assert.deepEqual(result, [9, 10]);
        }));

    it('can select the array of _ids with `true` selector with a reader', () =>
        Test.withReadonlyContext(async context => {
            const reader = await context.getSelectReader(TestDocumentLine, true, {
                filter: { document: { code: 'DOCD' } },
            });
            assert.deepEqual(await reader.readAll(), [9, 10]);
        }));

    it('can select the array of _ids with `true` selector and node has a natural key', () =>
        Test.withReadonlyContext(async context => {
            const result = await context.select(TestNaturalKey, true, {
                filter: { key: 'K2' },
            });
            assert.deepEqual(result, [2]);
        }));

    it('can select with a basic selector', () =>
        Test.withReadonlyContext(async context => {
            const result = await context.select(
                TestDocument,
                { _id: true, code: true, mandatoryReference: true },
                { filter: { _id: { _in: [2, 3] } } },
            );
            assert.deepEqual(result, [
                {
                    _id: 2,
                    code: 'DOCB',
                    mandatoryReference: 2,
                },
                {
                    _id: 3,
                    code: 'DOCC',
                    mandatoryReference: 1,
                },
            ]);
        }));

    it('can select with reference joins', () =>
        Test.withReadonlyContext(async context => {
            const result = await context.select(
                TestDocument,
                {
                    _id: true,
                    code: true,
                    mandatoryReference: { _id: true, code: true, details: true },
                },
                {
                    filter: { _id: { _in: [2, 3] } },
                },
            );
            assert.deepEqual(result, [
                {
                    _id: 2,
                    code: 'DOCB',
                    mandatoryReference: { _id: 2, code: 'REF2', details: 'reference A2' },
                },
                {
                    _id: 3,
                    code: 'DOCC',
                    mandatoryReference: { _id: 1, code: 'REF1', details: 'reference B1' },
                },
            ]);
        }));

    it('can select with optional reference joins (with _id in reference selector)', () =>
        Test.withReadonlyContext(async context => {
            const result = await context.select(
                TestDocumentLine,
                {
                    _id: true,
                    document: true,
                    lineNumber: true,
                    optionalReference: { code: true, _id: true, details: true },
                },
                {
                    filter: { _id: { _in: [1, 2, 3] } },
                },
            );
            assert.deepEqual(result, [
                {
                    _id: 1,
                    document: 1,
                    lineNumber: 1,
                    optionalReference: null,
                },
                {
                    _id: 2,
                    document: 2,
                    lineNumber: 1,
                    optionalReference: { _id: 1, code: 'REF1', details: 'reference B1' },
                },
                {
                    _id: 3,
                    document: 2,
                    lineNumber: 2,
                    optionalReference: null,
                },
            ]);
        }));

    it('can select with optional reference joins (without _id in reference selector)', () =>
        Test.withReadonlyContext(async context => {
            const result = await context.select(
                TestDocumentLine,
                {
                    _id: true,
                    document: true,
                    lineNumber: true,
                    optionalReference: { code: true, details: true },
                },
                {
                    filter: { _id: { _in: [1, 2, 3] } },
                },
            );
            assert.deepEqual(result, [
                {
                    _id: 1,
                    document: 1,
                    lineNumber: 1,
                    optionalReference: null,
                },
                {
                    _id: 2,
                    document: 2,
                    lineNumber: 1,
                    optionalReference: { code: 'REF1', details: 'reference B1' },
                },
                {
                    _id: 3,
                    document: 2,
                    lineNumber: 2,
                    optionalReference: null,
                },
            ]);
        }));

    it('can select a collection', () =>
        Test.withReadonlyContext(async context => {
            const result = await context.select(
                TestDocument,
                { code: true, mandatoryReference: { code: true }, lines: { lineNumber: true, description: true } },
                { filter: {} },
            );
            assert.deepEqual(result, [
                {
                    code: 'DOCA',
                    lines: [{ description: 'line A 1', lineNumber: 1 }],
                    mandatoryReference: { code: 'REF1' },
                },
                {
                    code: 'DOCB',
                    lines: [
                        { description: 'line B 1', lineNumber: 1 },
                        { description: 'line B 2', lineNumber: 2 },
                    ],
                    mandatoryReference: { code: 'REF2' },
                },
                {
                    code: 'DOCC',
                    lines: [
                        { description: 'x', lineNumber: 1 },
                        { description: 'y', lineNumber: 2 },
                        { description: 'z', lineNumber: 3 },
                        { description: 'y', lineNumber: 4 },
                        { description: 'y', lineNumber: 5 },
                    ],
                    mandatoryReference: { code: 'REF1' },
                },
                {
                    code: 'DOCD',
                    lines: [
                        { description: 'line D 1', lineNumber: 1 },
                        { description: 'line D 2', lineNumber: 2 },
                    ],
                    mandatoryReference: { code: 'REF1' },
                },
                {
                    code: 'DOCE',
                    lines: [
                        { description: 'line E 1', lineNumber: 1 },
                        { description: 'line E 2', lineNumber: 2 },
                    ],
                    mandatoryReference: { code: 'REF1' },
                },
                {
                    code: 'TEST_DELETE_LINE',
                    lines: [
                        {
                            description: 'remaining line #1',
                            lineNumber: 1,
                        },
                        {
                            description: 'line to be deleted',
                            lineNumber: 2,
                        },
                        {
                            description: 'remaining line #2',
                            lineNumber: 3,
                        },
                    ],
                    mandatoryReference: {
                        code: 'REF1',
                    },
                },
            ]);
        }));

    it('can select only the line ids as an array of objects', () =>
        Test.withReadonlyContext(async context => {
            const result = await context.select(
                TestDocument,
                { code: true, lines: { _id: true } },
                { filter: { code: { _in: ['DOCA', 'DOCB', 'DOCC'] } } },
            );
            assert.deepEqual(result, [
                { code: 'DOCA', lines: [{ _id: 1 }] },
                { code: 'DOCB', lines: [{ _id: 2 }, { _id: 3 }] },
                { code: 'DOCC', lines: [{ _id: 4 }, { _id: 5 }, { _id: 6 }, { _id: 7 }, { _id: 8 }] },
            ]);
        }));

    it('can select only the line ids as an array of integers', () =>
        Test.withReadonlyContext(async context => {
            const result = await context.select(
                TestDocument,
                { code: true, lines: true },
                { filter: { code: { _in: ['DOCA', 'DOCB', 'DOCC'] } } },
            );
            assert.deepEqual(result, [
                { code: 'DOCA', lines: [1] },
                { code: 'DOCB', lines: [2, 3] },
                { code: 'DOCC', lines: [4, 5, 6, 7, 8] },
            ]);
        }));

    it('can select collection with predicate filter', () =>
        Test.withReadonlyContext(async context => {
            const result = await context.select(
                TestDocument,
                { code: true, mandatoryReference: { code: true }, lines: true },
                { filter: { lines: { _none: true, description: /line/ } } },
            );
            assert.deepEqual(result, [
                {
                    code: 'DOCC',
                    lines: [4, 5, 6, 7, 8],
                    mandatoryReference: { code: 'REF1' },
                },
            ]);
        }));

    it('can select recursively and convert dates', () =>
        Test.withReadonlyContext(async context => {
            const result = await context.select(
                TestAggDocument,
                { code: true, lines: { _id: true, document: { code: true, lines: true }, date: true } },
                { filter: { code: 'DOC2' } },
            );
            assert.deepEqual(result, [
                {
                    code: 'DOC2',
                    lines: [
                        {
                            _id: 5,
                            document: { code: 'DOC2', lines: [5, 6, 7] },
                            date: DateValue.parse('2003-01-08'),
                        },
                        {
                            _id: 6,
                            document: { code: 'DOC2', lines: [5, 6, 7] },
                            date: DateValue.parse('2003-08-24'),
                        },
                        {
                            _id: 7,
                            document: { code: 'DOC2', lines: [5, 6, 7] },
                            date: DateValue.parse('2004-02-18'),
                        },
                    ],
                },
            ]);
        }));

    it('can select references as natural key', () =>
        Test.withReadonlyContext(async context => {
            const result = await context.select(
                TestDocument,
                {
                    code: true,
                    mandatoryReference: true,
                    lines: { lineNumber: true, optionalReference: true },
                },
                {
                    filter: { code: { _in: ['DOCA', 'DOCB'] } },
                    returnReferencesAsNaturalKey: true,
                },
            );
            assert.deepEqual(result, [
                {
                    code: 'DOCA',
                    lines: [{ lineNumber: 1, optionalReference: null }],
                    mandatoryReference: '#REF1',
                },
                {
                    code: 'DOCB',
                    lines: [
                        { lineNumber: 1, optionalReference: '#REF1' },
                        { lineNumber: 2, optionalReference: null },
                    ],
                    mandatoryReference: '#REF2',
                },
            ]);
        }));

    it('can select with selector functions', () =>
        Test.withReadonlyContext(async context => {
            const result = await context.select(
                TestDocument,
                {
                    _id: true,
                    code: true,
                    mandatoryReference: {
                        refCodeInner() {
                            return this.code;
                        },
                    },
                    async refCodeOuter() {
                        const ref = await this.mandatoryReference;
                        return ref.code;
                    },
                },
                { filter: { _id: { _in: [2, 3] } } },
            );
            assert.deepEqual(result, [
                {
                    _id: 2,
                    code: 'DOCB',
                    mandatoryReference: { refCodeInner: 'REF2' },
                    refCodeOuter: 'REF2',
                },
                {
                    _id: 3,
                    code: 'DOCC',
                    mandatoryReference: { refCodeInner: 'REF1' },
                    refCodeOuter: 'REF1',
                },
            ]);
        }));

    it('cannot select computed properties', () =>
        Test.withReadonlyContext(async context => {
            await assert.isRejected(
                context.select(TestDocument, { code: true, computedLines: true }, { filter: {} }),
                'TestDocument.computedLines: computed properties are not allowed in selector',
            );
        }));
});
