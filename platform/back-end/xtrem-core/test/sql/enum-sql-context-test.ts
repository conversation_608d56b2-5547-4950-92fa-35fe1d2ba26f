// tslint:disable: no-unused-expression
import { asyncArray } from '@sage/xtrem-async-helper';
import { assert } from 'chai';
import { fixtures, testDocumentWithTransientApplication } from '..';
import { EnumSqlContext } from '../../lib/sql/sql-context/enum-sql-context';
import { Test } from '../../lib/test';
import {
    datatypesData,
    documentData,
    documentLineData,
    initTables,
    referredData,
    referringData,
    restoreTables,
    setup,
} from '../fixtures/index';
import { cleanEnums, newEnums } from './fixtures/setup';

export const nodeEnums = [
    // restoreTables cleans the tables but it should also clean the enums.
    { enumTypeName: 'test_animal_fly_behavior_enum', enumValues: '{flyWithWings, cannotFly}' },
    {
        enumTypeName: 'test_animal_sleep_behavior_enum',
        enumValues: '{sleepOnTheGround, sleepInATree, sleepInTheAir, neverSleep}',
    },
    { enumTypeName: 'test_enum_enum', enumValues: '{value1, value2, value3, value4}' },
    { enumTypeName: 'test_enum_for_array_enum', enumValues: '{arrayVal1, arrayVal2, arrayVal3}' },
    { enumTypeName: 'test_user_type_enum_enum', enumValues: '{application, system}' },
];

async function createEnums(): Promise<void> {
    await Test.uncommitted(async context => {
        const sqlContext = new EnumSqlContext(context.application);
        await sqlContext.createEnumTypes(newEnums);
    });
}

describe('enum sql context', () => {
    beforeEach(async () => {
        await setup({ application: await testDocumentWithTransientApplication.application });
        await initTables([
            { nodeConstructor: fixtures.nodes.TestDatatypes, data: datatypesData },
            { nodeConstructor: fixtures.nodes.TestReferred, data: referredData },
            { nodeConstructor: fixtures.nodes.TestReferring, data: referringData },
            { nodeConstructor: fixtures.nodes.TestDocument, data: documentData },
            { nodeConstructor: fixtures.nodes.TestDocumentLine, data: documentLineData },
        ]);
        await cleanEnums();
    });

    after(() => restoreTables());

    it('can list enum types', () =>
        Test.uncommitted(async context => {
            const sqlContext = new EnumSqlContext(context.application);
            const obtained = await sqlContext.listAllEnumTypes();

            assert.deepEqual(obtained, nodeEnums);
        }));

    it('can create enum types', () =>
        Test.uncommitted(async context => {
            const sqlContext = new EnumSqlContext(context.application);
            await sqlContext.createEnumTypes(newEnums);
            const expected = [
                { enumTypeName: 'enum1', enumValues: '{value1, value2}' },
                { enumTypeName: 'enum2', enumValues: '{value1, value2}' },
                ...nodeEnums,
            ];
            const obtained = await sqlContext.listAllEnumTypes();

            assert.deepEqual(obtained, expected);
        }));

    it('can list enum type usage', () =>
        Test.uncommitted(async context => {
            const sqlContext = new EnumSqlContext(context.application);
            const expected = [
                {
                    tableName: 'test_base',
                    columnName: 'test_base_enum',
                    enumTypeName: 'test_enum_enum',
                },
                {
                    tableName: 'test_datatypes',
                    columnName: 'enum_val',
                    enumTypeName: 'test_enum_enum',
                },
                {
                    tableName: 'test_datatypes',
                    columnName: 'enum_array_val',
                    enumTypeName: 'test_enum_for_array_enum',
                },
                {
                    columnName: 'fly_behavior',
                    enumTypeName: 'test_animal_fly_behavior_enum',
                    tableName: 'test_fly_behavior',
                },
                {
                    columnName: 'test_base_enum',
                    enumTypeName: 'test_enum_enum',
                    tableName: 'test_node_details',
                },
                {
                    columnName: 'behavior',
                    enumTypeName: 'test_animal_sleep_behavior_enum',
                    tableName: 'test_sleep_behavior',
                },
                {
                    columnName: 'user_type',
                    enumTypeName: 'test_user_type_enum_enum',
                    tableName: 'test_user',
                },
            ];
            const obtained = await sqlContext.listEnumTypesUsage();

            assert.deepEqual(obtained, expected);
        }));

    it('can check if an enum type exists', () =>
        Test.uncommitted(context =>
            asyncArray(nodeEnums).forEach(async e => {
                const sqlContext = new EnumSqlContext(context.application);
                assert(await sqlContext.enumTypeExists(e.enumTypeName), `${e.enumTypeName} not found in the database`);
            }),
        ));

    it('can drop an enum type', async () => {
        await Test.uncommitted(async context => {
            const sqlContext = new EnumSqlContext(context.application);
            await sqlContext.createEnumTypes(newEnums);
        });
        await Test.uncommitted(context =>
            asyncArray(newEnums).forEach(async e => {
                const sqlContext = new EnumSqlContext(context.application);
                await sqlContext.dropEnum(e.name);
            }),
        );
        await Test.uncommitted(context =>
            asyncArray(newEnums).forEach(async e => {
                const sqlContext = new EnumSqlContext(context.application);
                assert(!(await sqlContext.enumTypeExists(e.name)), `${e.name} not dropped`);
            }),
        );
    });

    it('can rename enum attributes', async () => {
        await createEnums();
        await Test.uncommitted(async context => {
            const sqlContext = new EnumSqlContext(context.application);
            assert.strictEqual(await sqlContext.getEnumAttributes(newEnums[0].name), '{value1,value2}');
            await sqlContext.renameEnumAttributes(newEnums[0].name, {
                oldValue: 'value2',
                newValue: 'value2renamed',
            });
            assert.strictEqual(await sqlContext.getEnumAttributes(newEnums[0].name), '{value1,value2renamed}');
        });
    });

    it('can add attributes to enum', async () => {
        await createEnums();
        await Test.uncommitted(async context => {
            const sqlContext = new EnumSqlContext(context.application);
            assert.strictEqual(await sqlContext.getEnumAttributes(newEnums[0].name), '{value1,value2}');
            await sqlContext.addAttributesToEnum(newEnums[0].name, {
                newValue: 'newValue',
                position: { before: 'value2' },
            });
            assert.strictEqual(await sqlContext.getEnumAttributes(newEnums[0].name), '{value1,newValue,value2}');
        });
    });

    it('can rename enum type', async () => {
        await createEnums();
        await Test.uncommitted(async context => {
            const sqlContext = new EnumSqlContext(context.application);
            await sqlContext.renameEnumType(newEnums[0].name, 'enum1renamed');
            assert(!(await sqlContext.enumTypeExists(newEnums[0].name)), `${newEnums[0].name} not properly renamed`);
            assert(await sqlContext.enumTypeExists('enum1renamed'), `${newEnums[0].name} not properly renamed`);
        });
    });

    it('can drop enum attributes', async () => {
        await createEnums();
        await Test.uncommitted(async context => {
            const sqlContext = new EnumSqlContext(context.application);
            assert.strictEqual(await sqlContext.getEnumAttributes(newEnums[0].name), '{value1,value2}');
            await sqlContext.dropEnumAttributes(newEnums[0].name, [{ memberName: 'value2', replacement: null }]);
            assert.strictEqual(await sqlContext.getEnumAttributes(newEnums[0].name), '{value1}');
        });
    });

    it('can get enum attributes', () =>
        Test.uncommitted(context =>
            asyncArray(nodeEnums).forEach(async e => {
                const sqlContext = new EnumSqlContext(context.application);
                assert.strictEqual(await sqlContext.getEnumAttributes(e.enumTypeName), e.enumValues.replace(/ /g, ''));
            }),
        ));
});
