import { asyncArray } from '@sage/xtrem-async-helper';
import { assert } from 'chai';
import { SchemaSqlContext } from '../../../lib/sql/sql-context/schema-sql-context';
import { Test } from '../../../lib/test/test';

export const newEnums = [
    { name: 'enum1', values: { value1: 1, value2: 2 } },
    { name: 'enum2', values: { value1: 1, value2: 2 } },
];

export async function cleanEnums(options?: { names?: string[]; cascade?: boolean }): Promise<void> {
    await Test.application.createContextForDdl(async context => {
        const sqlContext = new SchemaSqlContext(context.application);
        await asyncArray([...(options?.names || []), ...newEnums.map(e => e.name), 'enum1renamed']).forEach(name =>
            sqlContext.executeSqlStatement({
                sql: `DROP TYPE IF EXISTS xtrem_core_test.${name} ${options?.cascade ? 'CASCADE' : ''}`,
            }),
        );
    });
}

export async function checkSchemaExists(sqlContext: SchemaSqlContext, schema: string): Promise<boolean> {
    const sql = `SELECT schema_name FROM information_schema.schemata WHERE schema_name = '${schema}';`;
    return !!(await sqlContext.executeSqlStatement<{ schemaName: string }[]>({ sql })).length;
}

export async function checkTableExists(table: string): Promise<void> {
    await Test.application.createContextForDdl(async context => {
        const sqlContext = new SchemaSqlContext(context.application);
        assert(await sqlContext.tableExists(table));
    });
}

export async function dropTable(table: string): Promise<void> {
    await Test.application.createContextForDdl(async context => {
        const sqlContext = new SchemaSqlContext(context.application);
        assert(await sqlContext.dropTable(table, { isCascade: true, errorsAsWarnings: false }));
    });
}
