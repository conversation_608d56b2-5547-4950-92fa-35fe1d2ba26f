// tslint:disable: no-unused-expression
import { assert } from 'chai';
import * as sinon from 'sinon';
import { ConditionVariable, Dict, Test } from '../../index';
import { ContextVault } from '../../lib/runtime/context-vault';
import { ConfigManager, createApplicationWithApi, restoreTables, setup } from '../fixtures/index';

const sandbox = sinon.createSandbox();

describe('Secret placeholder tests', () => {
    before(async () => {
        await setup({ application: await createApplicationWithApi({}) });
    });

    after(async () => {
        await restoreTables();
    });

    it('can reload secrets on "loaded" event', async () => {
        const secretConditionMap = ['@secret/secret1', '@secret/nested/secret2'].reduce((r, k) => {
            r[k] = Test.createConditionVariable(k);
            return r;
        }, {} as Dict<ConditionVariable>);

        const config: any = ConfigManager.current;

        config.testSecrets = {
            secret1: '@secret/secret1',
            nested: {
                secret2: '@secret/nested/secret2',
            },
        };

        sandbox.stub(ContextVault, 'getConfigSecretValue').callsFake((placeholder: string) => {
            // notify the condition variable immediately after this async call
            setImmediate(() => secretConditionMap[placeholder]?.notifyAll());
            return Promise.resolve(placeholder.replace(/^@secret\//g, ''));
        });

        // create the loaded condition variable
        const loadedCondition = new Promise(resolve => {
            ConfigManager.emitter.once('loaded', () => {
                resolve(true);
            });
        });

        ConfigManager.emitter.emit('loaded', config);

        const loaded = await loadedCondition;
        assert.strictEqual(loaded, true);

        await Promise.all(Object.values(secretConditionMap).map(cond => cond.wait()));

        assert.deepEqual(config.testSecrets, {
            secret1: 'secret1',
            nested: {
                secret2: 'nested/secret2',
            },
        });

        sandbox.restore();
    });
});
