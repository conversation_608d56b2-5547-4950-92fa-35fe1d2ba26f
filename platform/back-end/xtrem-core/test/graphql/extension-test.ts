import { assert } from 'chai';
import { testExtensionApplication } from '..';
import { Test } from '../../lib';
import { GraphQlHelper, graphqlSetup, initTables, restoreTables } from '../fixtures/index';
import { TestBase, TestExtensionReference } from '../fixtures/nodes';

let graphqlHelper: GraphQlHelper;

describe('Node extensions', () => {
    before(async () => {
        graphqlHelper = await graphqlSetup({ application: await testExtensionApplication.application });
        await initTables([{ nodeConstructor: TestExtensionReference, data: [] }]);
    });
    after(() => restoreTables());
    it('can get an extended node with its extended properties', async () => {
        const result = await graphqlHelper.mutation(
            `{ 
                    testBaseJson { testCreate(code: "abc") {
                    code,
                    extensionCode1,
                    extensionCode2
                } }
            }`,
        );
        assert.deepEqual(result, {
            testBaseJson: {
                testCreate: {
                    code: 'abc',
                    extensionCode1: 'abcExt1',
                    extensionCode2: 'abcExt2',
                },
            },
        });
    });
    it('can call an operation defined inside an extension', async () => {
        // Static method defined in an extension.
        const result1 = await graphqlHelper.mutation(`{ 
                    testBase { extensionOperation1(param: 5) }
            }`);

        assert.deepEqual(result1, {
            testBase: {
                extensionOperation1: 'extensionOperation param=5',
            },
        });

        // Non static method defined in an extension.
        await initTables([{ nodeConstructor: TestBase, data: [{ _id: 1, code: 'BASE1' }] }]);
        await Test.withCommittedContext(async context => {
            const testBase = await context.read(TestBase, { _id: 1 }, { forUpdate: true });
            assert.equal(
                await testBase.extensionMethod2(6),
                'extensionMethod2 code=BASE1, extensionCode2=BASE1Ext2, param=6',
            );
        });
    });

    it('can create with enum override on extension', async () => {
        const createResult = await graphqlHelper.mutation<{
            testBase: { create: { code: string; testBaseEnum: string } };
        }>(
            `{ testBase { create(data: {
                code: "abc",
                testBaseEnum: "value4",
            })  { code testBaseEnum } } }`,
        );
        assert.deepEqual(createResult, {
            testBase: {
                create: {
                    code: 'abc',
                    testBaseEnum: 'value4',
                },
            },
        });
    });
});
