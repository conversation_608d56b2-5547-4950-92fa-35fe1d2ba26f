import {
    ClientCollection,
    ClientNode,
    ClientNodeInput,
    CreateOperation,
    Graph,
    QueryOperation,
    querySelector,
    UpdateOperation,
} from '@sage/xtrem-client';
import { assert } from 'chai';
import { Collection, decorators, Node, Reference, StringDataType } from '../../index';
import {
    clientSetup,
    createApplicationWithApi,
    GraphQlHelper,
    graphqlSetup,
    initTables,
    restoreTables,
} from '../fixtures/index';

@decorators.node<TestAddress>({
    isPublished: true,
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canDelete: true,
    canUpdate: true,
    isVitalCollectionChild: true,
})
export class TestAddress extends Node {
    @decorators.referenceProperty<TestAddress, 'customer'>({
        isPublished: true,
        isStored: true,
        isVitalParent: true,
        node: () => TestCustomer,
    })
    readonly customer: Reference<TestCustomer>;

    @decorators.stringProperty<TestAddress, 'street'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 100 }),
    })
    readonly street: Promise<string>;

    @decorators.stringProperty<TestAddress, 'city'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 100 }),
    })
    readonly city: Promise<string>;
}

@decorators.node<TestCustomer>({
    isPublished: true,
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canDelete: true,
    canUpdate: true,
})
export class TestCustomer extends Node {
    @decorators.collectionProperty<TestCustomer, 'addresses'>({
        isPublished: true,
        node: () => TestAddress,
        isVital: true,
        reverseReference: 'customer',
    })
    readonly addresses: Collection<TestAddress>;

    @decorators.stringProperty<TestCustomer, 'name'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 100 }),
    })
    readonly name: Promise<string>;

    @decorators.referenceProperty<TestCustomer, 'billingAddress'>({
        isStored: true,
        isNullable: true,
        isPublished: true,
        node: () => TestAddress,
    })
    readonly billingAddress: Reference<TestAddress | null>;
}

interface ClientTestAddress extends ClientNode {
    customer: ClientTestCustomer;
    street: string;
    city: string;
}

interface ClientTestAddressInput extends ClientNodeInput {
    customer?: string;
    street?: string;
    city?: string;
}

interface ClientTestCustomer extends ClientNode {
    addresses: ClientCollection<ClientTestAddress>;
    name: string;
    billingAddress: ClientTestAddress | null;
}

interface ClientTestCustomerInput extends ClientNodeInput {
    addresses?: ClientTestAddressInput[];
    name?: string;
    billingAddress?: string | null;
}

export interface TestCustomer$Operations {
    query: QueryOperation<ClientTestCustomer>;
    create: CreateOperation<ClientTestCustomerInput, ClientTestCustomer>;
    update: UpdateOperation<ClientTestCustomerInput, ClientTestCustomer>;
}

let graph: Graph<{ '@sage/xtrem-core/testCustomer': TestCustomer$Operations }>;
let graphqlHelper: GraphQlHelper;

describe('cross references in graphql', () => {
    before(async () => {
        const application = await createApplicationWithApi({ nodes: { TestAddress, TestCustomer } });
        graph = await clientSetup({
            application,
        });

        graphqlHelper = await graphqlSetup({ application });

        await initTables([
            { nodeConstructor: TestAddress, data: [] },
            { nodeConstructor: TestCustomer, data: [] },
        ]);
    });
    after(() => restoreTables());

    it('can create and update an object with cross references', async () => {
        const testCustomer = graph.node('@sage/xtrem-core/testCustomer');
        const address1 = { street: '444 High Street', city: 'Palo Alto' };
        const address2 = { street: '156 University Av', city: 'Palo Alto' };
        const address3 = { street: 'Palo Alto City Hall', city: 'Palo Alto' };

        const inputData = {
            name: 'My Customer',
            addresses: [
                { ...{ _id: '-3' }, ...address1 },
                { ...{ _id: '-4' }, ...address2 },
            ],
            billingAddress: '-4',
        };

        const createdCustomer = await testCustomer
            .create(
                {
                    _id: true,
                    name: true,
                    addresses: querySelector({ _id: true, street: true, city: true }),
                    billingAddress: { street: true, city: true },
                },
                {
                    data: inputData,
                },
            )
            .execute();

        let createdAddresses = createdCustomer.addresses.query.edges.map((edge: any) => edge.node);
        assert.deepEqual(
            {
                ...createdCustomer,
                addresses: createdAddresses,
            },
            {
                _id: createdCustomer._id,
                name: inputData.name,
                addresses: [
                    { ...{ _id: '1' }, ...address1 },
                    { ...{ _id: '2' }, ...address2 },
                ],
                billingAddress: address2,
            },
        );

        const updatedCustomer = await testCustomer
            .update(
                {
                    addresses: querySelector({ _id: true, street: true, city: true }),
                    billingAddress: { street: true, city: true },
                },
                {
                    data: {
                        _id: createdCustomer._id,
                        addresses: [...createdAddresses, { ...{ _id: '-1', ...address3 } }],
                        billingAddress: '-1',
                    },
                },
            )
            .execute();

        createdAddresses = updatedCustomer.addresses.query.edges.map((edge: any) => edge.node);
        assert.deepEqual(
            {
                ...updatedCustomer,
                addresses: createdAddresses,
            },
            {
                addresses: [
                    { ...{ _id: '1' }, ...address1 },
                    { ...{ _id: '2' }, ...address2 },
                    { ...{ _id: '3' }, ...address3 },
                ],

                billingAddress: address3,
            },
        );
    });

    it('can get default values with cross references', async () => {
        const result = (await graphqlHelper.query<{ testCustomer: TestCustomer }>(
            `{
                testCustomer {
                    getDefaults(data: { _id: "-1", name: "My Customer",addresses: [{ _id:"-3", street: "444 High Street", city: "Palo Alto" }, { _id: "-4", street: "156 University Av", city: "Palo Alto" }], billingAddress: "-4" }) {
                        _id
                        name
                        addresses {
                        query {
                            edges {
                                node {
                                     _id 
                                     street 
                                     city
                                    }
                                }
                            }
                        }
                        billingAddress { 
                            _id 
                            street 
                            city
                        }
                    }
                }
            }`,
        )) as any;

        assert.deepEqual(result, {
            testCustomer: {
                getDefaults: {
                    _id: '-1',
                    name: 'My Customer',
                    addresses: {
                        query: {
                            edges: [
                                { node: { _id: '-3', street: '444 High Street', city: 'Palo Alto' } },
                                { node: { _id: '-4', street: '156 University Av', city: 'Palo Alto' } },
                            ],
                        },
                    },
                    billingAddress: { _id: '-4', street: '156 University Av', city: 'Palo Alto' },
                },
            },
        });

        // This test is here on purpose. getDefaults must not return a transient _id like -1000000001:
        assert.equal(result.testCustomer.getDefaults._id, -1);
    });

    it('can get default values with cross references (with potential collision because of missing id)', async () => {
        // same query as before but we omitted the _id: "-1" on the customer input data
        const result = (await graphqlHelper.query<{ testCustomer: TestCustomer }>(
            `{
                testCustomer {
                    getDefaults(data: { name: "My Customer",addresses: [{ _id:"-3", street: "444 High Street", city: "Palo Alto" }, { _id: "-4", street: "156 University Av", city: "Palo Alto" }], billingAddress: "-4" }) {
                        _id
                        name
                        addresses {
                        query {
                            edges {
                                node {
                                     _id 
                                     street 
                                     city
                                    }
                                }
                            }
                        }
                        billingAddress { 
                            _id 
                            street 
                            city
                        }
                    }
                }
            }`,
        )) as any;

        assert.deepEqual(result, {
            testCustomer: {
                getDefaults: {
                    // -5 was allocated to avoid a collision with the other negative values
                    _id: '-5',
                    name: 'My Customer',
                    addresses: {
                        query: {
                            edges: [
                                { node: { _id: '-3', street: '444 High Street', city: 'Palo Alto' } },
                                { node: { _id: '-4', street: '156 University Av', city: 'Palo Alto' } },
                            ],
                        },
                    },
                    billingAddress: { _id: '-4', street: '156 University Av', city: 'Palo Alto' },
                },
            },
        });

        // This test is here on purpose. getDefaults must not return a transient _id like -1000000001:
        assert.equal(result.testCustomer.getDefaults._id, -5);
    });
});
