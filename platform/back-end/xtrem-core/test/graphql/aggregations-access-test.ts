import { assert } from 'chai';
import {
    createApplicationWith<PERSON>pi,
    GraphQlHelper,
    graphqlSetup,
    initTables,
    restoreTables,
    secureData,
    siteData,
} from '../fixtures/index';
import { TestSecure, TestSite } from '../fixtures/nodes';

let graphqlHelper: GraphQlHelper;

describe('Aggregates - access', () => {
    before(async () => {
        graphqlHelper = await graphqlSetup({
            application: await createApplicationWithApi({
                nodes: { TestSite, TestSecure },
            }),
        });
        await initTables([
            { nodeConstructor: TestSite, data: siteData },
            { nodeConstructor: TestSecure, data: secureData },
        ]);
    });
    after(() => restoreTables());
    it('cannot request aggregate query for restricted properties', async () => {
        const result = (await graphqlHelper.execute<{ testSecure: TestSecure }>(
            `{
                        xtremCore{
                            testSecure {
                                readAggregate {
                                    site  { code { distinctCount } }
                                }
                            }
                        }
                    }`,
            { userEmail: '<EMAIL>', withDiagnoses: true, dontThrow: true },
        )) as any;

        assert.equal(result.errors.length, 1);
        assert.equal(result.errors[0].message, 'An error has occurred. Please contact your administrator.');
        assert.deepEqual(result.errors[0].path, ['xtremCore', 'testSecure', 'readAggregate']);
        assert.deepEqual(result.errors[0].locations, [
            {
                line: 4,
                column: 33,
            },
        ]);
        assert.deepEqual(result.errors[0].extensions, {
            code: 'system-error',
        });

        assert.deepEqual(result.data, {
            xtremCore: {
                testSecure: {
                    readAggregate: null,
                },
            },
        });

        assert.deepEqual(result.extensions, {
            diagnoses: [
                {
                    severity: 3,
                    path: ['site', 'code'],
                    message: 'The property is unavailable.',
                },
            ],
        });
    });

    it('can only access unrestricted properties in aggregate query', async () => {
        const result = (await graphqlHelper.execute<{ testSecure: TestSecure }>(
            `{
                        xtremCore{
                            testSecure {
                                readAggregate {
                                    code { distinctCount }
                                    site { code { distinctCount } }
                                }
                            }
                        }
                    }`,
            { userEmail: '<EMAIL>', withDiagnoses: true, dontThrow: true },
        )) as any;

        assert.deepEqual(result.data, {
            xtremCore: {
                testSecure: {
                    readAggregate: {
                        code: {
                            distinctCount: 5,
                        },
                        site: null,
                    },
                },
            },
        });

        assert.deepEqual(result.extensions, {
            diagnoses: [
                {
                    severity: 3,
                    path: ['site', 'code'],
                    message: 'The property is unavailable.',
                },
            ],
        });
    });
});
