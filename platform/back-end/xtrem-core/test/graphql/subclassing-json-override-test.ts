import { assert } from 'chai';
import { testSubclassingDataExtractionApplication } from '..';
import { GraphQlHelper, graphqlSetup, initTables, QueryNode, restoreTables } from '../fixtures/index';
import { TestHorse } from '../fixtures/nodes';

let graphqlHelper: GraphQlHelper;

const dataHorses = [
    {
        _id: 1,
        jsonAnimal: { name: 'Horse', size: 2 },
    },
    {
        _id: 2,
        jsonAnimal: { name: 'Horse2', size: 3 },
    },
];

describe('GraphQL - Subclassing JSON override', () => {
    before(async () => {
        graphqlHelper = await graphqlSetup({ application: await testSubclassingDataExtractionApplication.application });
        await initTables([{ nodeConstructor: TestHorse, data: dataHorses }]);
    });

    // This test is more special as its purpose is not only to check modifications done to the database,
    // but to verify that the framework is correctly handling the override of (in this case) JSON properties.

    describe('JSON override reference can query', () => {
        it("Can query - 'Horse' level", async () => {
            const result = (await graphqlHelper.query<{ testHorse: QueryNode<TestHorse> }>(
                `
            {
                testHorse {
                    query {
                        totalCount
                        edges {
                            node {
                               jsonAnimal
                            }
                        }
                    }
                }
            }`,
            )) as any;

            assert.deepEqual(result, {
                testHorse: {
                    query: {
                        totalCount: 2,
                        edges: [
                            {
                                node: {
                                    jsonAnimal: JSON.stringify(dataHorses[0].jsonAnimal),
                                },
                            },
                            {
                                node: {
                                    jsonAnimal: JSON.stringify(dataHorses[1].jsonAnimal),
                                },
                            },
                        ],
                    },
                },
            });
        });
    });

    after(() => restoreTables());
});
