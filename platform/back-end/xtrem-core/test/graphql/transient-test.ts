import { Graph, querySelector } from '@sage/xtrem-client';
import { assert } from 'chai';
import { fixtures, testTransientApplication } from '..';
import { GraphApi } from '../fixtures/client-nodes';
import { clientSetup, initTables, referredData, restoreTables } from '../fixtures/index';

let graph: Graph<GraphApi>;
let transientNode: GraphApi['@sage/xtrem-core/testTransient'];

const { TestReferred, TestTransient, TestTransientLines } = fixtures.nodes;

describe('Transient property test - GraphQL', () => {
    before(async () => {
        graph = await clientSetup({ application: await testTransientApplication.application });
        transientNode = graph.node('@sage/xtrem-core/testTransient');

        await initTables([
            { nodeConstructor: TestTransient, data: [] },
            { nodeConstructor: TestTransientLines, data: [] },
            { nodeConstructor: TestReferred, data: referredData },
        ]);
    });

    after(() => restoreTables());

    it('can create with simple transient property', async () => {
        let result = await transientNode
            .create(
                { stringValue: true, derivedValue: true },
                { data: { stringValue: 'Value 1', transientValue: 'transient text' } },
            )
            .execute();

        assert.equal(result.derivedValue, 'transient set in defaultValue (transient text)- added');

        result = await transientNode
            .create({ stringValue: true, derivedValue: true }, { data: { stringValue: 'Value 2' } })
            .execute();

        assert.equal(result.derivedValue, 'transient not set in defaultValue - added');
    });

    it('can create with reference transient property', async () => {
        let result = await transientNode
            .create(
                { stringValue: true, derivedFromRef: true },
                { data: { stringValue: 'Reference 1', transientRef: 1 } },
            )
            .execute();

        assert.equal(result.derivedFromRef, referredData[0].code);

        result = await transientNode
            .create({ stringValue: true, derivedFromRef: true }, { data: { stringValue: 'Reference 2' } })
            .execute();

        assert.equal(result.derivedFromRef, 'transient reference not set');
    });

    it('can create with collection transient property', async () => {
        let result = await transientNode
            .create(
                {
                    _id: true,
                    stringValue: true,
                    derivedFromCollection: true,
                    derivedCollection: querySelector({
                        stringValue: true,
                        parent: { _id: true, stringValue: true },
                    }),
                },
                {
                    data: {
                        stringValue: 'Collection 1',
                        transientLines: [
                            { stringValue: 'col1', parent: 1 },
                            { stringValue: 'col2', parent: 1 },
                        ],
                    },
                },
            )
            .execute();
        assert.deepEqual(result, {
            _id: '5',
            derivedCollection: {
                query: {
                    edges: [
                        {
                            cursor: '[10,1]#55',
                            node: {
                                parent: {
                                    _id: '5',
                                    stringValue: 'Collection 1',
                                },
                                stringValue: 'col1',
                            },
                        },
                        {
                            cursor: '[20,2]#64',
                            node: {
                                parent: {
                                    _id: '5',
                                    stringValue: 'Collection 1',
                                },
                                stringValue: 'col2',
                            },
                        },
                    ],
                    pageInfo: {
                        startCursor: '[10,1]#55',
                        endCursor: '[20,2]#64',
                        hasNextPage: false,
                        hasPreviousPage: false,
                    },
                },
            },
            derivedFromCollection: 2,
            stringValue: 'Collection 1',
        } as any);

        result = await transientNode
            .create(
                {
                    _id: true,
                    stringValue: true,
                    derivedFromCollection: true,
                    derivedCollection: querySelector({
                        stringValue: true,
                        parent: { _id: true, stringValue: true },
                    }),
                },
                { data: { stringValue: 'Collection 2' } },
            )
            .execute();

        assert.deepEqual(result, {
            _id: '6',
            derivedCollection: {
                query: {
                    edges: [],
                    pageInfo: {
                        endCursor: null,
                        hasNextPage: false,
                        hasPreviousPage: false,
                        startCursor: null,
                    },
                },
            },
            derivedFromCollection: 0,
            stringValue: 'Collection 2',
        } as any);
    });

    it('can update with transient property', async () => {
        const createResult = await transientNode
            .create(
                { _id: true, stringValue: true, derivedValue: true },
                { data: { stringValue: 'Value 1', transientValue: 'transient text' } },
            )
            .execute();

        assert.equal(createResult.stringValue, 'Value 1');
        assert.equal(createResult.derivedValue, 'transient set in defaultValue (transient text)- added');

        let updateResult = await transientNode
            .updateById(
                { stringValue: true, derivedValue: true },
                {
                    _id: createResult._id,
                    data: { stringValue: 'Value 2', transientValue: 'second transient text' } as any,
                },
            )
            .execute();

        assert.equal(updateResult.stringValue, 'Value 2');
        assert.equal(updateResult.derivedValue, 'transient set in defaultValue (second transient text)- modified');

        updateResult = await transientNode
            .updateById(
                { stringValue: true, derivedValue: true },
                {
                    _id: createResult._id,
                    data: { stringValue: 'Value 3', transientValue: '' },
                },
            )
            .execute();

        assert.equal(updateResult.stringValue, 'Value 3');
        assert.equal(updateResult.derivedValue, 'transient not set in defaultValue - modified');
    });

    it('can control with transient property', async () => {
        await assert.isRejected(
            transientNode
                .create(
                    { _id: true, stringValue: true, derivedValue: true },
                    {
                        data: {
                            stringValue: 'Value 1',
                            transientValue: 'transient text',
                            transientRef: 2,
                        },
                    },
                )
                .execute(),
            'The record was not created.',
        );

        const createResult = await transientNode
            .create(
                { _id: true, stringValue: true, derivedValue: true },
                { data: { stringValue: 'Value 1', transientValue: 'transient text' } },
            )
            .execute();

        assert.equal(createResult.stringValue, 'Value 1');
        assert.equal(createResult.derivedValue, 'transient set in defaultValue (transient text)- added');

        await assert.isRejected(
            transientNode
                .updateById(
                    { stringValue: true, derivedValue: true },
                    {
                        _id: createResult._id,
                        data: {
                            stringValue: 'Value 2',
                            transientValue: 'second transient text',
                            transientRef: 1,
                        } as any,
                    },
                )
                .execute(),
            'The record was not updated.',
        );
    });
});
