import {
    edgesSelector,
    Graph,
    Order<PERSON>y,
    Pa<PERSON>O<PERSON>s,
    Selector,
    withoutEdges,
    WithoutSelectedEdges,
} from '@sage/xtrem-client';
import { assert } from 'chai';
import { testDocumentWithTransientApplication } from '..';
import * as fixtures from '../fixtures';
import { ClientDocumentLine, GraphApi, TestDatatypesInterface } from '../fixtures/client-nodes/index';
import {
    clientSetup,
    datatypesData,
    documentData,
    documentLineData,
    initTables,
    referredData,
    restoreTables,
} from '../fixtures/index';

let graph: Graph<GraphApi>;
let datatypesNode: GraphApi['@sage/xtrem-core/testDatatypes'];
let documentLineNode: GraphApi['@sage/xtrem-core/testDocumentLine'];

describe('query paging', () => {
    before(async () => {
        graph = await clientSetup({ application: await testDocumentWithTransientApplication.application });
        documentLineNode = graph.node('@sage/xtrem-core/testDocumentLine');
        datatypesNode = graph.node('@sage/xtrem-core/testDatatypes');

        await initTables([
            { nodeConstructor: fixtures.nodes.TestReferred, data: referredData },
            { nodeConstructor: fixtures.nodes.TestDatatypes, data: datatypesData },
            { nodeConstructor: fixtures.nodes.TestDocument, data: documentData },
            { nodeConstructor: fixtures.nodes.TestDocumentLine, data: documentLineData },
        ]);
    });
    it('can limit page size and returns connection payload', async () => {
        const result = await datatypesNode
            .query(
                edgesSelector(
                    { id: true },
                    {
                        first: 2,
                    },
                ),
            )
            .execute();
        assert.equal(result.edges.length, 2);
        result.edges.forEach((t: any, i: any) => {
            assert.isObject(t.node);
            assert.equal(t.node.id, i);
            assert.isString(t.cursor);
        });
        const pageInfo = result.pageInfo;
        assert.isObject(pageInfo);
        assert.isString(pageInfo.endCursor);
        assert.isTrue(pageInfo.hasNextPage);
    });

    it('can read a complete feed forwards', async () => {
        let cursor = '';
        let start = 0;
        const first = 5;
        // eslint-disable-next-line no-constant-condition
        while (true) {
            const result = await datatypesNode
                .query(
                    edgesSelector(
                        { id: true },
                        {
                            first,
                            after: cursor,
                        },
                    ),
                )
                .execute();
            assert.isAtMost(result.edges.length, first);
            // eslint-disable-next-line @typescript-eslint/no-loop-func
            result.edges.forEach((edge: any) => {
                assert.equal(edge.node.id, start);
                start += 1;
            });
            if (!result.pageInfo.hasNextPage) break;
            assert.isBelow(start, datatypesData.length);
            cursor = result.pageInfo.endCursor!;
        }
        assert.equal(start, datatypesData.length);
    });

    it('can read a complete feed backwards', async () => {
        let cursor = '';
        let start = 0;
        const last = 5;
        // eslint-disable-next-line no-constant-condition
        while (true) {
            const result = await datatypesNode
                .query(
                    edgesSelector(
                        { id: true },
                        {
                            last,
                            before: cursor,
                        },
                    ),
                )
                .execute();
            assert.isAtMost(result.edges.length, last);
            if (start === 0) start = result.totalCount ? result.totalCount : 0 - last;
            else start -= last;
            if (start <= 0) break;
            // eslint-disable-next-line @typescript-eslint/no-loop-func
            result.edges.forEach((edge: any) => {
                assert.equal(edge.node.id, start);
                start += 1;
            });
            if (!result.pageInfo.hasPreviousPage) break;
            assert.isBelow(start, datatypesData.length);
            cursor = result.pageInfo.endCursor!;
        }
        assert.equal(start, -last);
    });

    async function readAll<SelectorT extends Selector<TestDatatypesInterface>>(
        params: PagingOptions<TestDatatypesInterface>,
        selector: SelectorT,
    ): Promise<WithoutSelectedEdges<TestDatatypesInterface, SelectorT>[]> {
        let got = [] as any[];
        let cursor = '';
        const knownCursors: string[] = [];
        // eslint-disable-next-line no-constant-condition
        while (true) {
            const result = await datatypesNode
                .query(
                    edgesSelector<TestDatatypesInterface>(selector, {
                        ...params,
                        after: cursor,
                    }),
                )
                .execute();
            got = got.concat(withoutEdges(result));
            if (!result.pageInfo.hasNextPage) break;
            const newCursor = result.pageInfo.endCursor!;
            assert(knownCursors.indexOf(newCursor) === -1, `Loop detected : ${knownCursors}`);
            knownCursors.push(newCursor);
            cursor = newCursor;
        }
        return got;
    }

    it('can read a complete feed on non unique sort', async () => {
        const result = (await readAll({ first: 2, orderBy: { enumVal: 1 } }, { id: true, enumVal: true }))
            .map(r => `${r.enumVal}_${r.id}`)
            .join(', ');
        assert.equal(
            result,
            'null_0, ' +
                'value1_3, value1_6, value1_9, value1_12, value1_15, ' +
                'value2_1, value2_4, value2_7, value2_10, value2_13, ' +
                'value3_2, value3_5, value3_8, value3_11, value3_14',
        );
    });
    it('can page with a date column', async () => {
        const result = (await readAll({ first: 2, orderBy: { dateVal: -1 } }, { id: true, dateVal: true }))
            .map(r => `${r.dateVal}_${r.id}`)
            .join(', ');
        assert.equal(
            result,
            '2018-04-15_15, 2018-03-15_14, 2018-02-15_13, 2018-01-15_12, 2017-12-15_11, ' +
                '2017-11-15_10, 2017-10-15_9, 2017-09-15_8, 2017-08-15_7, 2017-07-15_6, ' +
                '2017-06-15_5, 2017-05-15_4, 2017-04-15_3, 2017-03-15_2, 2017-02-15_1, ' +
                'null_0',
        );
    });
    async function pageDocumentLine(
        orderBy: OrderBy<ClientDocumentLine> | undefined,
        expected: { document: { _id: string }; lineNumber: number }[],
    ): Promise<void> {
        let cursor: string | undefined;
        for (let i = 0; i < expected.length; i += 1) {
            const result = await documentLineNode
                .query(
                    edgesSelector({ document: { _id: true }, lineNumber: true }, { first: 1, after: cursor, orderBy }),
                )
                .execute();

            if (i + 1 === expected.length) {
                assert.equal(result.edges.length, 1);
                assert.equal(result.pageInfo.hasNextPage, false);
            } else {
                assert.equal(result.edges.length, 1);
                assert.deepEqual(result.edges[0].node, expected[i]);
                assert.equal(result.pageInfo.hasNextPage, true);
                cursor = result.pageInfo.endCursor as string;
            }
        }
    }

    async function pageBackDocumentLine(
        orderBy: OrderBy<ClientDocumentLine> | undefined,
        expected: { document: { _id: string }; lineNumber: number }[],
    ): Promise<void> {
        let cursor: string | undefined;
        for (let i = expected.length; i > 0; i -= 1) {
            const result = await documentLineNode
                .query(
                    edgesSelector({ document: { _id: true }, lineNumber: true }, { last: 1, before: cursor, orderBy }),
                )
                .execute();

            if (i - 1 === 0) {
                assert.equal(result.edges.length, 1);
                assert.equal(result.pageInfo.hasPreviousPage, false);
            } else {
                assert.equal(result.edges.length, 1);
                assert.deepEqual(result.edges[0].node, expected[i - 1]);
                assert.equal(result.pageInfo.hasPreviousPage, true);
                cursor = result.pageInfo.startCursor as string;
            }
        }
    }

    const linesDataAsc = documentLineData.map(d => ({
        document: { _id: `${d.document}` },
        lineNumber: d.lineNumber,
    }));
    const linesDataDesc = linesDataAsc.slice().reverse();

    it('can page on a key that contains a reference', async () => {
        await pageDocumentLine({ document: { _id: 1 }, lineNumber: 1 }, linesDataAsc);
        await pageDocumentLine({ document: { _id: -1 }, lineNumber: -1 }, linesDataDesc);
    });

    it('can page with an order by on multiple properties of a reference', () =>
        pageDocumentLine({ document: { code: 1, description: 1 } }, linesDataAsc));

    it('can page backwards on an key that contains a reference', async () => {
        await pageBackDocumentLine({ document: { _id: 1 }, lineNumber: 1 }, linesDataAsc);
        await pageBackDocumentLine({ document: { _id: -1 }, lineNumber: -1 }, linesDataDesc);
    });

    it('cannot use last and first together', async () => {
        await assert.isRejected(
            datatypesNode.query({ ...edgesSelector({ id: true }, { last: 5, first: 5 }), totalCount: true }).execute(),
            'first cannot be supplied with last.',
        );
    });

    it('after cannot be supplied with last.', async () => {
        await assert.isRejected(
            datatypesNode
                .query({
                    ...edgesSelector({ id: true }, { last: 5, after: 'something' }),
                    totalCount: true,
                })
                .execute(),
            'after cannot be supplied with last.',
        );
    });

    it('before cannot be supplied without last.', async () => {
        await assert.isRejected(
            datatypesNode
                .query({
                    ...edgesSelector({ id: true }, { first: 5, before: 'something' }),
                    totalCount: true,
                })
                .execute(),
            'before cannot be supplied without last.',
        );
    });

    it('can page with default key', () => pageDocumentLine(undefined, linesDataAsc));
    it('can page with _id', async () => {
        await pageDocumentLine({ _id: 1 }, linesDataAsc);
        await pageDocumentLine({ _id: -1 }, linesDataDesc);
    });
    it('can get totalCount without filter', async () => {
        const result = await datatypesNode.query({ ...edgesSelector({ id: true }, {}), totalCount: true }).execute();
        assert.equal(result.totalCount, 16);
    });
    it('can get totalCount with filter', async () => {
        const result = await datatypesNode
            .query({ ...edgesSelector({ id: true }, { filter: { id: { _lt: 3 } } }), totalCount: true })
            .execute();
        assert.equal(result.totalCount, 3);
    });
    after(() => restoreTables());
});
