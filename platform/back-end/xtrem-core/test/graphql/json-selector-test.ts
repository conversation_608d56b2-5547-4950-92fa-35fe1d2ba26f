import { assert } from 'chai';
import {
    createApplicationWithApi,
    datatypesData,
    GraphQlHelper,
    graphqlSetup,
    initTables,
    restoreTables,
} from '../fixtures/index';
import { TestDatatypes } from '../fixtures/nodes';

let graphqlHelper: GraphQlHelper;

describe('GraphQL JSON selector', () => {
    before(async () => {
        graphqlHelper = await graphqlSetup({
            application: await createApplicationWithApi({ nodes: { TestDatatypes } }),
        });
        await initTables([{ nodeConstructor: TestDatatypes, data: datatypesData }]);
    });
    after(() => restoreTables());

    it('can query a JSON property with a selector', async () => {
        const result = await graphqlHelper.execute(
            `query { xtremCore { testDatatypes { query(filter: "{ _id: { _lt: 4 } }") {
                edges { node { _id, jsonCode: jsonVal(selector: "code") } }      
            } } } }`,
        );
        assert.deepEqual(result, {
            data: {
                xtremCore: {
                    testDatatypes: {
                        query: {
                            edges: [
                                { node: { _id: '1', jsonCode: null } },
                                { node: { _id: '2', jsonCode: '"string_1"' } },
                                { node: { _id: '3', jsonCode: '"string_2"' } },
                            ],
                        },
                    },
                },
            },
        });
    });
});
