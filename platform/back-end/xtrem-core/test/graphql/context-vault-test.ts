import { edgesSelector, Graph, withoutEdges } from '@sage/xtrem-client';
import { assert } from 'chai';
import { testEncryptedValuesApplication } from '..';
import { Test } from '../../lib/test/test';
import * as fixtures from '../fixtures';
import { GraphApi } from '../fixtures/client-nodes';
import { clientSetup, initTables, restoreTables } from '../fixtures/index';
import { TestEncryptedValues } from '../fixtures/nodes';

const maskedPassword = '********************';

let graph: Graph<GraphApi>;
let encryptedValueNode: GraphApi['@sage/xtrem-core/testEncryptedValues'];

describe('graphql read encrypted value', () => {
    before(async () => {
        // check the env to prevent from errors like: "No cluster id defined to the wrapper constructor"
        assert.strictEqual(process.env.XTREM_ENV, 'local', "XTREM_ENV env variable must be set to 'local'");
        graph = await clientSetup({ application: await testEncryptedValuesApplication.application });
        encryptedValueNode = graph.node('@sage/xtrem-core/testEncryptedValues');
        await initTables([
            {
                nodeConstructor: fixtures.nodes.TestEncryptedValues,
                data: [
                    { id: 1, passwordValue: 'myPlainPassword1' },
                    { id: 2, passwordValue: '' },
                ],
            },
        ]);
    });
    it('can hide encrypted field', async () => {
        const result = await encryptedValueNode
            .create(
                {
                    id: true,
                    passwordValue: true,
                },
                {
                    data: {
                        id: 3,
                        passwordValue: 'myPlainPassword3',
                    },
                },
            )
            .execute();
        assert.deepEqual(result, { id: 3, passwordValue: maskedPassword });
    });

    it('can hide encrypted field and return *', async () => {
        const graphqlQuery = await encryptedValueNode
            .query(
                edgesSelector({
                    id: true,
                    passwordValue: true,
                }),
            )
            .execute();
        const result = withoutEdges(graphqlQuery);

        assert.deepEqual(result, [
            { id: 1, passwordValue: maskedPassword },
            { id: 2, passwordValue: '' },
            { id: 3, passwordValue: maskedPassword },
        ]);
    });

    it('can retrieve password values with node.$.decryptValue', () =>
        Test.withContext(async context => {
            const nodeQuery = await context.query(TestEncryptedValues).toArray();
            assert.equal(await nodeQuery[0].$.decryptValue('passwordValue'), 'myPlainPassword1');
            assert.equal(await nodeQuery[1].$.decryptValue('passwordValue'), '');
            assert.equal(await nodeQuery[2].$.decryptValue('passwordValue'), 'myPlainPassword3');
        }));

    it('can filter empty encrypted value', async () => {
        // add another empty passwordValue
        await encryptedValueNode
            .create(
                {
                    id: true,
                    passwordValue: true,
                },
                {
                    data: {
                        id: 4,
                        passwordValue: '',
                    },
                },
            )
            .execute();

        const graphqlQuery = await encryptedValueNode
            .query(edgesSelector({ id: true, passwordValue: true }, { filter: { passwordValue: { _eq: '' } } }))
            .execute();
        const result = withoutEdges(graphqlQuery);

        assert.deepEqual(result, [
            { id: 2, passwordValue: '' },
            { id: 4, passwordValue: '' },
        ]);
    });

    it('will not erase password if password contains stars', () =>
        Test.withContext(async context => {
            // add another empty passwordValue
            await encryptedValueNode
                .update(
                    {
                        id: true,
                        passwordValue: true,
                    },
                    {
                        data: {
                            _id: '1',
                            passwordValue: maskedPassword,
                        },
                    },
                )
                .execute();

            const nodeQuery = await context.query(TestEncryptedValues).toArray();
            assert.equal(await nodeQuery[0].$.decryptValue('passwordValue'), 'myPlainPassword1');
        }));

    after(() => restoreTables());
});
