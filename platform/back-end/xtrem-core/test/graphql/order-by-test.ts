import { assert } from 'chai';
import { testBasicDocumentApplication } from '..';
import { Dict } from '../../lib/index';
import * as fixtures from '../fixtures';
import { GraphQlHelper, graphqlSetup, initTables, ReferredData, restoreTables } from '../fixtures/index';
import { TestDocument, TestDocumentLine } from '../fixtures/nodes';

let graphqlHelper: GraphQlHelper;

const referredData: ReferredData[] = [
    {
        _id: 1,
        code: 'REF1',
        details: 'reference B1',
        restricted: 'restricted1',
    },
];

const documentData = [
    {
        _id: 1,
        code: 'DOCA',
        description: 'Article 1',
        mandatoryReference: 1,
    },
    {
        _id: 2,
        code: 'DOCB',
        description: 'article 2',
        mandatoryReference: 1,
    },
];

const documentLineData = [
    {
        document: 1,
        lineNumber: 1,
        description: 'line A a',
    },
    {
        document: 2,
        lineNumber: 1,
        description: 'Line B a',
        optionalReference: 1,
    },
    {
        document: 2,
        lineNumber: 2,
        description: 'line B B',
    },
    {
        document: 2,
        lineNumber: 3,
        description: 'Line B c',
        optionalReference: 1,
    },
    {
        document: 2,
        lineNumber: 4,
        description: 'Line B D',
    },
    {
        document: 2,
        lineNumber: 5,
        description: 'line B e',
        optionalReference: 1,
    },
    {
        document: 2,
        lineNumber: 6,
        description: 'Line B F',
    },
    {
        document: 2,
        lineNumber: 7,
        description: 'line B g',
        optionalReference: 1,
    },
    {
        document: 2,
        lineNumber: 8,
        description: 'Line B H',
    },
    {
        document: 2,
        lineNumber: 9,
        description: 'line B i',
        optionalReference: 1,
    },
    {
        document: 2,
        lineNumber: 10,
        description: 'Line B J',
    },
];

describe('graphql nested-collection orderBy tests', () => {
    function checkDocumentLineNumbers(current: any[], expectedNumbers: number[]): void {
        const lineNumbers = current.map(c => c.node.lineNumber);
        assert.deepEqual(lineNumbers, expectedNumbers);
    }
    before(async () => {
        graphqlHelper = await graphqlSetup({ application: await testBasicDocumentApplication.application });
        await initTables([
            { nodeConstructor: fixtures.nodes.TestReferred, data: referredData },
            { nodeConstructor: fixtures.nodes.TestDocument, data: documentData },
            { nodeConstructor: fixtures.nodes.TestDocumentLine, data: documentLineData },
        ]);
    });
    const orderBys: Dict<number[]> = {
        '{description:1}': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
        '{description:-1}': [10, 9, 8, 7, 6, 5, 4, 3, 2, 1],
    };
    Object.keys(orderBys).forEach(orderBy => {
        const expected = orderBys[orderBy];

        it(`check orderBy '${orderBy}'`, async () => {
            const result = (await graphqlHelper.query<{ documents: { node: TestDocument } }>(
                `{
                testDocument {
                    query(filter:"{code:'DOCB'}") {
                    totalCount
                    edges {
                        node {
                            description
                                lines {
                                    query(orderBy:"${orderBy}") {
                                        totalCount
                                        edges {
                                            node {
                                                lineNumber
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }`,
            )) as any;
            const documents = result.testDocument.query;
            assert.equal(documents.totalCount, 1);
            const lines = documents.edges[0].node.lines.query;
            assert.equal(lines.totalCount, documentLineData.filter(l => l.document === 2).length);
            checkDocumentLineNumbers(lines.edges, expected);
        });
    });

    // Decription =>lineNumber
    // [
    //     'Line B a' =>1, 'line B B' =>2,
    //     'Line B c' =>2, 'Line B D' =>4,
    //     'line B e' =>5, 'Line B F' =>6,
    //     'line B g' =>7, 'Line B H' =>8,
    //     'line B i' =>9, 'Line B J' =>10
    //   ]
    const orderByAndAfters: { orderBy: string; after: string; expected: number[] }[] = [
        {
            orderBy: '{description:1}',
            after: "['line B e',6]#61",
            expected: [6, 7, 8, 9, 10],
        },
        {
            orderBy: '{description:-1}',
            after: "['line B e',6]#61",
            expected: [4, 3, 2, 1],
        },
        {
            orderBy: '{optionalReference:{code:1}}',
            after: '[null,11]#43',
            expected: [1, 3, 5, 7, 9],
        },
    ];

    orderByAndAfters.forEach(orderByAndAfter => {
        it(`check orderBy(${orderByAndAfter.orderBy}) / after(${orderByAndAfter.after})`, async () => {
            const result = (await graphqlHelper.query<{ documents: { node: TestDocument } }>(
                `{
                    testDocument {
                        query(filter:"{code:'DOCB'}") {
                            totalCount
                            edges {
                                node {
                                    description
                                    lines {
                                        query(orderBy:"${orderByAndAfter.orderBy}", after:"${orderByAndAfter.after}") {
                                            totalCount
                                            edges {
                                                cursor
                                                node {
                                                    lineNumber
                                                    optionalReference { code }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }`,
            )) as any;
            const documents = result.testDocument.query;
            const lines = documents.edges[0].node.lines.query;
            assert.equal(lines.totalCount, 10);
            checkDocumentLineNumbers(lines.edges, orderByAndAfter.expected);
        });
    });

    orderByAndAfters.forEach(orderByAndAfter => {
        it(`check orderBy(${orderByAndAfter.orderBy}) / after(${orderByAndAfter.after}) on computed collection`, async () => {
            const result = (await graphqlHelper.query<{ documents: { node: TestDocument } }>(
                `{
                    testDocument {
                        query(filter:"{code:'DOCB'}") {
                            totalCount
                            edges {
                                node {
                                    description
                                    computedLines {
                                        query(orderBy:"${orderByAndAfter.orderBy}", after:"${orderByAndAfter.after}") {
                                            totalCount
                                            edges {
                                                cursor
                                                node {
                                                    lineNumber
                                                    optionalReference { code }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }`,
            )) as any;
            const documents = result.testDocument.query;
            const lines = documents.edges[0].node.computedLines.query;
            assert.equal(lines.totalCount, 10);
            checkDocumentLineNumbers(lines.edges, orderByAndAfter.expected);
        });
    });

    it("check 'first' + 'filter' + 'orderBy' + 'after'", async () => {
        const result = (await graphqlHelper.query<{ documents: { node: TestDocument } }>(
            `{
                testDocument {
                    query(filter:"{code:'DOCB'}") {
                    totalCount
                    edges {
                        node {
                            description
                                lines {
                                    query(first:3,
                                        filter:"{lineNumber:{_in:[1, 3, 5, 7, 8, 9, 10]}}",
                                        orderBy:"{description:-1}",
                                        after:"['line B i',10]#02"
                                    ) {
                                        totalCount
                                        edges {
                                            node {
                                                lineNumber
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }`,
        )) as any;
        const documents = result.testDocument.query;
        const lines = documents.edges[0].node.lines.query;
        // We used a filter so the totalCount must return 1 (even if there are 4 docs in the db, only 1
        // meets the filter criteria)
        assert.equal(documents.totalCount, 1);
        // Doc 'DODB' has 10 lines but we only queried the first 3.
        // TotalCount should be 10
        assert.equal(lines.edges.length, 3);
        assert.equal(lines.totalCount, 7);
        // Decription =>lineNumber
        // [
        //     'Line B a' =>1, 'line B B' =>2,
        //     'Line B c' =>3, 'Line B D' =>4,
        //     'line B e' =>5, 'Line B F' =>6,
        //     'line B g' =>7, 'Line B H' =>8,
        //     'line B i' =>9, 'Line B J' =>10
        //   ]
        // description order reverse and with the filter after i will return [8,7,5]
        // 3 results because of first: 3
        // line number 6 is not included because of _in filter
        checkDocumentLineNumbers(lines.edges, [8, 7, 5]);
    });

    it("check 'first' + 'filter' + 'orderBy' + 'after' with getValue property", async () => {
        // const x = {lines:{_atLeast:1,{getDescription:{_regex:"line"}}}};
        const result = (await graphqlHelper.query<{ documents: { node: TestDocumentLine } }>(
            `{
                testDocumentLine {
                    query(
                        filter:"{_and:[{document:{_id:2}},{getDescription:{_regex:'line'}}]}",
                        orderBy:"{getDescription:-1}",
                        after:"['line B i',10]#02"
                    ) {
                        totalCount
                        edges {
                            node {
                                lineNumber
                                getDescription
                            }
                        }
                    }
                }
            }`,
        )) as any;
        const lines = result.testDocumentLine.query;
        assert.equal(lines.edges.length, 3);
        assert.equal(lines.totalCount, 4);
        checkDocumentLineNumbers(lines.edges, [7, 5, 2]);
    });

    it("check 'first' + 'orderBy' + 'after' with reference property", async () => {
        const result = (await graphqlHelper.query<{ documents: { node: TestDocumentLine } }>(
            `{
                testDocumentLine {
                    query(
                        first:3,
                        orderBy:"{optionalReference:{code:1}}",
                        after:"[null,11]#43"
                    ) {
                        totalCount
                        edges {
                            node {
                                lineNumber
                                getDescription
                            }
                        }
                    }
                }
            }`,
        )) as any;
        const lines = result.testDocumentLine.query;
        assert.equal(lines.edges.length, 3);
        assert.equal(lines.totalCount, 11);
        checkDocumentLineNumbers(lines.edges, [1, 3, 5]);
    });

    after(() => restoreTables());
});
