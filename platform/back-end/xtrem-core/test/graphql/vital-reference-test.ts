import { Graph } from '@sage/xtrem-client';
import { assert } from 'chai';
import { fixtures, testVitalReferenceApplication } from '..';
import { Test } from '../../lib';
import { GraphApi } from '../fixtures/client-nodes/index';
import { clientSetup, initTables, nonVitalReferenceData, restoreTables } from '../fixtures/index';

let graph: Graph<GraphApi>;

export interface QueryResult {
    _id: string;
    code: string;
    mandatoryVitalRef: { code: string; text: string };
    optionalVitalRef: {
        code: string;
        text: string;
        nonVitalRef: { code: string; text: string };
    } | null;
}

const {
    TestVitalReferenceChildMandatory,
    TestVitalReferenceChildOptional,
    TestVitalReferenceParent,
    TestNonVitalReference,
} = fixtures.nodes;

interface CreateOptions {
    withOptional?: boolean;
    nonVitalRefId?: number;
}

describe('graphql vital reference tests', () => {
    before(async () => {
        graph = await clientSetup({ application: await testVitalReferenceApplication.application });
        await initTables([
            { nodeConstructor: TestVitalReferenceParent, data: [] },
            { nodeConstructor: TestVitalReferenceChildMandatory, data: [] },
            { nodeConstructor: TestVitalReferenceChildOptional, data: [] },
            { nodeConstructor: TestNonVitalReference, data: nonVitalReferenceData },
        ]);
    });

    beforeEach(() => Test.committed(context => context.deleteMany(TestVitalReferenceParent, {})));

    async function createParent(options?: CreateOptions): Promise<QueryResult> {
        const { withOptional, nonVitalRefId } = options ?? {};
        const vitalParentNode = graph.node('@sage/xtrem-core/testVitalReferenceParent');
        const result = await vitalParentNode
            .create(
                {
                    _id: true,
                    code: true,
                    mandatoryVitalRef: { code: true, text: true },
                    optionalVitalRef: {
                        code: true,
                        text: true,
                        nonVitalRef: { code: true, text: true },
                    },
                },
                {
                    data: {
                        code: 'PARENT',
                        mandatoryVitalRef: { code: 'MANDATORY', text: 'mandatory child' },
                        ...(withOptional
                            ? {
                                  optionalVitalRef: {
                                      code: 'OPTIONAL',
                                      text: 'optional child',
                                      nonVitalRef: nonVitalRefId ?? 1,
                                  },
                              }
                            : null),
                    },
                },
            )
            .execute();
        return result;
    }

    it('can create vital reference (without optional child)', async () => {
        const parent = await createParent();
        assert.deepEqual(parent, {
            _id: parent._id,
            code: 'PARENT',
            mandatoryVitalRef: { code: 'MANDATORY', text: 'mandatory child' },
            optionalVitalRef: null,
        });
    });

    it('can create vital reference (with optional child)', async () => {
        const parent = await createParent({ withOptional: true });
        assert.deepEqual(parent, {
            _id: parent._id,
            code: 'PARENT',
            mandatoryVitalRef: { code: 'MANDATORY', text: 'mandatory child' },
            optionalVitalRef: {
                code: 'OPTIONAL',
                text: 'optional child',
                nonVitalRef: { code: 'NONVITAL1', text: 'non vital child 1' },
            },
        });
    });

    it('can update vital reference (adding optional child)', async () => {
        const vitalParentNode = graph.node('@sage/xtrem-core/testVitalReferenceParent');
        const parent = await createParent();
        assert.isNull(parent.optionalVitalRef);
        const updated = await vitalParentNode
            .update(
                {
                    _id: true,
                    code: true,
                    mandatoryVitalRef: { code: true, text: true },
                    optionalVitalRef: { code: true, text: true, nonVitalRef: { code: true, text: true } },
                },
                {
                    data: {
                        _id: parent._id,
                        code: 'PARENT-MODIFIED',
                        mandatoryVitalRef: { text: 'updated text' },
                        optionalVitalRef: { code: 'OPT', text: 'opt text', nonVitalRef: '_id:1' },
                    },
                },
            )
            .execute();
        assert.deepEqual(updated, {
            _id: parent._id,
            code: 'PARENT-MODIFIED',
            mandatoryVitalRef: { code: 'MANDATORY', text: 'updated text' },
            optionalVitalRef: {
                code: 'OPT',
                text: 'opt text',
                nonVitalRef: { code: 'NONVITAL1', text: 'non vital child 1' },
            },
        });
    });

    it('can update vital reference (resetting optional child)', async () => {
        const vitalParentNode = graph.node('@sage/xtrem-core/testVitalReferenceParent');
        const parent = await createParent({ withOptional: true });
        assert.isNotNull(parent.optionalVitalRef);
        const updated = await vitalParentNode
            .update(
                {
                    _id: true,
                    code: true,
                    mandatoryVitalRef: { code: true, text: true },
                    optionalVitalRef: { code: true, text: true },
                },
                {
                    data: {
                        _id: parent._id,
                        code: 'PARENT-MODIFIED',
                        mandatoryVitalRef: { text: 'updated text' },
                        optionalVitalRef: null,
                    },
                },
            )
            .execute();
        assert.deepEqual(updated, {
            _id: parent._id,
            code: 'PARENT-MODIFIED',
            mandatoryVitalRef: { code: 'MANDATORY', text: 'updated text' },
            optionalVitalRef: null,
        });
    });

    it('can update vital reference (with non vital reference)', async () => {
        const vitalParentNode = graph.node('@sage/xtrem-core/testVitalReferenceParent');
        const parent = await createParent({ withOptional: true });
        assert.isNotNull(parent.optionalVitalRef);
        let updated = await vitalParentNode
            .update(
                {
                    _id: true,
                    code: true,
                    mandatoryVitalRef: { code: true, text: true },
                    optionalVitalRef: { code: true, text: true, nonVitalRef: { code: true, text: true } },
                },
                {
                    data: {
                        _id: parent._id,
                        code: 'PARENT-MODIFIED',
                        mandatoryVitalRef: { text: 'updated text' },
                        optionalVitalRef: {
                            code: 'OPT',
                            text: 'opt text',
                            nonVitalRef: '_id:2',
                        },
                    },
                },
            )
            .execute();
        assert.deepEqual(updated, {
            _id: parent._id,
            code: 'PARENT-MODIFIED',
            mandatoryVitalRef: { code: 'MANDATORY', text: 'updated text' },
            optionalVitalRef: {
                code: 'OPT',
                text: 'opt text',
                nonVitalRef: { code: 'NONVITAL2', text: 'non vital child 2' },
            },
        });

        updated = await vitalParentNode
            .update(
                {
                    _id: true,
                    code: true,
                    mandatoryVitalRef: { code: true, text: true },
                    optionalVitalRef: { code: true, text: true, nonVitalRef: { code: true, text: true } },
                },
                {
                    data: {
                        _id: parent._id,
                        code: 'PARENT-MODIFIED',
                        mandatoryVitalRef: { text: 'updated text' },
                        optionalVitalRef: {
                            code: 'OPT',
                            text: 'opt text',
                            nonVitalRef: '3',
                        },
                    },
                },
            )
            .execute();
        assert.deepEqual(updated, {
            _id: parent._id,
            code: 'PARENT-MODIFIED',
            mandatoryVitalRef: { code: 'MANDATORY', text: 'updated text' },
            optionalVitalRef: {
                code: 'OPT',
                text: 'opt text',
                nonVitalRef: { code: 'NONVITAL3', text: 'non vital child 3' },
            },
        });
    });

    after(() => restoreTables());
});
