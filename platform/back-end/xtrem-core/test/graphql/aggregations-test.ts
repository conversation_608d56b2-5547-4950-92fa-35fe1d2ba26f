import { asyncArray } from '@sage/xtrem-async-helper';
import { date, dateRange, datetime, datetimeRange, decimalRange, integerRange, time } from '@sage/xtrem-date-time';
import { Decimal } from '@sage/xtrem-decimal';
import { assert } from 'chai';
import { BinaryStream, TextStream, Uuid } from '../../index';
import { TestEnum, TestEnumForArray } from '../fixtures/enums';
import {
    createApplicationWithApi,
    DatatypesData,
    GraphQlHelper,
    graphqlSetup,
    initTables,
    ReferredData,
    ReferringData,
    restoreTables,
} from '../fixtures/index';
import { TestDatatypes, TestReferred, TestReferring } from '../fixtures/nodes';

let graphqlHelper: GraphQlHelper;

const datatypesData: DatatypesData[] = [];

for (let i = 0; i <= 7; i += 1) {
    const month = (i % 12) + 1;
    const year = 2017 + Math.floor(i / 12);
    datatypesData.push({
        _id: i + 1,
        id: i,
        booleanVal: i === 0 ? null : i % 2 === 1,
        enumVal: (['value1', 'value2', 'value3'] as TestEnum[])[i % 3],
        shortVal: i * 2,
        integerVal: i * 4,
        integerRangeVal: integerRange.make(i, i + 2),
        decimalRangeVal: decimalRange.make(`${i}.001`, `${i + 2}.0004`),
        decimalVal: Decimal.make(i * 8) as any, // more challenging roundtrip later
        floatVal: i * 16,
        doubleVal: i * 32,
        dateVal: date.make(year, month, 15),
        dateRangeVal: dateRange.make(date.make(year, month, 15), date.make(year, month, 20)),
        datetimeRangeVal: datetimeRange.make(datetime.make(year, month, 15), datetime.make(year, month, 20)),
        timeVal: time.make(i, (i * 7) % 60, (i * 21) % 60),
        datetimeVal: datetime.makeUtc(year, month, 15, 14, 30, 10),
        stringVal: `string%_$${i}`,
        uuidVal: Uuid.generate(),
        binaryStream: BinaryStream.fromBuffer(Buffer.from([i, i + 200, i % 5])),
        textStream: TextStream.fromString(`textStream${i}`),
        mailTemplate: TextStream.fromString(`mailTemplate${i}`, 'text/html'),
        unsafeMailTemplate: TextStream.fromString(`unsafeMailTemplate${i}`, 'text/html'),
        jsonVal: {},
        _sourceId: '',
        enumArrayVal: [
            'arrayVal1',
            (['arrayVal1', 'arrayVal2', 'arrayVal3'] as TestEnumForArray[])[i % 3],
            'arrayVal1',
        ],
        integerArrayVal: [i, i + 1, i + 2],
        stringArrayVal: [`string%_$${i}`, `string%_$${i + 1}`, `string%_$${i + 2}`],
    });
}

const referringData: ReferringData[] = [];
for (let i = 0; i <= 7; i += 1) {
    referringData.push({
        _id: i + 1,
        code: `PARENT${i}`,
        description: i % 2 === 0 ? 'even' : 'odd',
        reference: i + 1,
        referenceArray: [2, 2],
    });
}

const referredData: ReferredData[] = [];
for (let i = 0; i <= 7; i += 1) {
    referredData.push({
        _id: i + 1,
        code: `REF${i}`,
        details: `details_${i}`,
        integerVal: i * 2,
    });
}

describe('Aggregates - graphQl', () => {
    before(async () => {
        graphqlHelper = await graphqlSetup({
            application: await createApplicationWithApi({
                nodes: { TestDatatypes, TestReferred, TestReferring },
            }),
        });
        await initTables([
            { nodeConstructor: TestDatatypes, data: datatypesData },
            { nodeConstructor: TestReferred, data: referredData },
            { nodeConstructor: TestReferring, data: referringData },
        ]);
    });
    after(() => restoreTables());
    it('can query max/min/sum/avg - one level', async () => {
        const result = (await graphqlHelper.query<{ testDatatypes: TestDatatypes }>(
            `{
                testDatatypes {
                    readAggregate {
                        id { min, max, sum, avg, distinctCount}
                        booleanVal { min, max, distinctCount }
                        shortVal { min, max, sum, avg, distinctCount}
                        integerVal { min, max, sum, avg, distinctCount}
                        decimalVal { min, max, sum, avg, distinctCount}
                        floatVal { min, max, sum, avg, distinctCount}
                        doubleVal { min, max, sum, avg, distinctCount}
                        stringVal { min, max, distinctCount}
                        datetimeVal { min, max, distinctCount}
                        dateVal { min, max, distinctCount}
                        timeVal { min, max, distinctCount }
                        uuidVal { distinctCount}
                        enumVal { distinctCount, min, max}
                        integerRangeVal { distinctCount}
                        decimalRangeVal { distinctCount}
                        dateRangeVal { distinctCount}
                        datetimeRangeVal { distinctCount}
                        enumArrayVal { distinctCount}
                        integerArrayVal { distinctCount}
                        stringArrayVal { distinctCount}
                    }
                }
            }`,
        )) as any;
        assert.deepEqual(result, {
            testDatatypes: {
                readAggregate: {
                    id: {
                        min: 0,
                        max: 7,
                        sum: 28,
                        avg: 3.5,
                        distinctCount: 8,
                    },
                    booleanVal: {
                        min: false,
                        max: true,
                        distinctCount: 2,
                    },
                    shortVal: {
                        min: 0,
                        max: 14,
                        sum: 56,
                        avg: 7,
                        distinctCount: 8,
                    },
                    integerVal: {
                        min: 0,
                        max: 28,
                        sum: 112,
                        avg: 14,
                        distinctCount: 8,
                    },
                    decimalVal: {
                        min: '0',
                        max: '56',
                        sum: '224',
                        avg: '28',
                        distinctCount: 8,
                    },
                    floatVal: {
                        min: '0',
                        max: '112',
                        sum: '448',
                        avg: '56',
                        distinctCount: 8,
                    },
                    doubleVal: {
                        min: '0',
                        max: '224',
                        sum: '896',
                        avg: '112',
                        distinctCount: 8,
                    },
                    stringVal: {
                        min: 'string%_$0',
                        max: 'string%_$7',
                        distinctCount: 8,
                    },
                    datetimeVal: {
                        min: '2017-01-15T14:30:10.000Z',
                        max: '2017-08-15T14:30:10.000Z',
                        distinctCount: 8,
                    },
                    dateVal: {
                        min: '2017-01-15',
                        max: '2017-08-15',
                        distinctCount: 8,
                    },
                    timeVal: {
                        min: '00:00:00',
                        max: '07:49:27',
                        distinctCount: 8,
                    },
                    uuidVal: {
                        distinctCount: 8,
                    },
                    enumVal: {
                        min: 'value1',
                        max: 'value3',
                        distinctCount: 3,
                    },
                    integerRangeVal: {
                        distinctCount: 8,
                    },
                    decimalRangeVal: {
                        distinctCount: 8,
                    },
                    dateRangeVal: {
                        distinctCount: 8,
                    },
                    datetimeRangeVal: {
                        distinctCount: 8,
                    },
                    enumArrayVal: {
                        distinctCount: 3,
                    },
                    integerArrayVal: {
                        distinctCount: 8,
                    },
                    stringArrayVal: {
                        distinctCount: 8,
                    },
                },
            },
        });
    });
    it('can query max/min/sum/avg - multi level', async () => {
        const result = (await graphqlHelper.query<{ testReferring: TestReferring }>(
            `{
                testReferring {
                    readAggregate {
                        reference {
                            integerVal { min, max, avg, sum, distinctCount }
                        }
                    }
                }
            }`,
        )) as any;
        assert.deepEqual(result, {
            testReferring: {
                readAggregate: {
                    reference: {
                        integerVal: {
                            min: 0,
                            max: 14,
                            avg: 7,
                            sum: 56,
                            distinctCount: 8,
                        },
                    },
                },
            },
        });
    });

    it('cannot query max/min/sum/avg/distinctCount - multi level on reference array', () =>
        asyncArray(['max', 'min', 'sum', 'avg', 'distinctCount']).forEach(async operator => {
            await assert.isRejected(
                graphqlHelper.query<{ testReferring: TestReferring }>(
                    `{
                    testReferring {
                        readAggregate {
                            referenceArray {
                                integerVal { ${operator} }
                            }
                        }
                    }
                }`,
                ),
                /Cannot query field "integerVal" on type "AggregateTypeForReferenceArrayProperty".*/,
            );
        }));

    it('can query distinctCount - one level on reference array', async () => {
        const result = (await graphqlHelper.query<{ testReferring: TestReferring }>(
            `{
                testReferring {
                    readAggregate {
                        referenceArray {
                            distinctCount
                        }
                    }
                }
            }`,
        )) as any;

        assert.deepEqual(result, {
            testReferring: {
                readAggregate: {
                    referenceArray: {
                        distinctCount: 1,
                    },
                },
            },
        });
    });

    it('cannot query max/min/sum/avg - one level on reference array', () =>
        asyncArray(['max', 'min', 'sum', 'avg']).forEach(async operator => {
            await assert.isRejected(
                graphqlHelper.query<{ testReferring: TestReferring }>(
                    `{
                    testReferring {
                        readAggregate {
                            referenceArray {
                                ${operator}
                            }
                        }
                    }
                }`,
                ),
                new RegExp(`Cannot query field "${operator}" on type "AggregateTypeForReferenceArrayProperty".`),
            );
        }));

    it('can query max/min/sum/avg - multi level - with filters', async () => {
        const result = (await graphqlHelper.query<{ testReferring: TestReferring }>(
            `{
                testReferring {
                    readAggregate(filter:"{description:'even'}") {
                        reference {
                            integerVal { min, max, avg, sum, distinctCount }
                        }
                    }
                }
            }`,
        )) as any;
        assert.deepEqual(result, {
            testReferring: {
                readAggregate: {
                    reference: {
                        integerVal: {
                            min: 0,
                            max: 12,
                            avg: 6,
                            sum: 24,
                            distinctCount: 4,
                        },
                    },
                },
            },
        });
    });
    it('can query and read aggregates at the same time', async () => {
        const result = (await graphqlHelper.query<{ testReferring: TestReferring }>(
            `{
                testReferring {
                    query(filter:"{description:'odd'}") {
                        edges {
                            node {
                                code
                            }
                        }
                    }
                    readAggregate {
                        reference {
                            integerVal { sum }
                        }
                    }
                }
            }`,
        )) as any;
        assert.deepEqual(result, {
            testReferring: {
                query: {
                    edges: [
                        {
                            node: {
                                code: 'PARENT1',
                            },
                        },
                        {
                            node: {
                                code: 'PARENT3',
                            },
                        },
                        {
                            node: {
                                code: 'PARENT5',
                            },
                        },
                        {
                            node: {
                                code: 'PARENT7',
                            },
                        },
                    ],
                },
                readAggregate: {
                    reference: {
                        integerVal: {
                            sum: 56,
                        },
                    },
                },
            },
        });
    });
    it('can query and read aggregates at the same time - with filter', async () => {
        const result = (await graphqlHelper.query<{ testReferring: TestReferring }>(
            `{
                testReferring {
                    query(filter:"{description:'odd'}") {
                        edges {
                            node {
                                code
                            }
                        }
                    }
                    readAggregate(filter:"{description:'odd'}") {
                        reference {
                            integerVal { sum }
                        }
                    }
                }
            }`,
        )) as any;
        assert.deepEqual(result, {
            testReferring: {
                query: {
                    edges: [
                        {
                            node: {
                                code: 'PARENT1',
                            },
                        },
                        {
                            node: {
                                code: 'PARENT3',
                            },
                        },
                        {
                            node: {
                                code: 'PARENT5',
                            },
                        },
                        {
                            node: {
                                code: 'PARENT7',
                            },
                        },
                    ],
                },
                readAggregate: {
                    reference: {
                        integerVal: {
                            sum: 32,
                        },
                    },
                },
            },
        });
    });
});
