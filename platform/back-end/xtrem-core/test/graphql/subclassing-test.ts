import { asyncArray } from '@sage/xtrem-async-helper';
import { assert } from 'chai';
import { testSubclassingApplication } from '..';
import { Test } from '../../lib';
import {
    GraphQlHelper,
    graphqlSetup,
    initTables,
    QueryAggregateEdge,
    QueryAggregateNode,
    QueryNode,
    restoreTables,
    TestInitData,
} from '../fixtures/index';
import {
    TestAnimal,
    TestCat,
    TestDog,
    TestFish,
    TestFlyBehavior,
    TestMammal,
    TestPetOwner,
    TestSleepBehavior,
} from '../fixtures/nodes';

let graphqlHelper: GraphQlHelper;

const allIds: number[] = [];
export const dataFishes: TestInitData<TestFish>[] = [];
for (let i = 0; i < 5; i += 1) {
    const id = allIds.length + 1;
    allIds.push(id);
    dataFishes.push({
        _id: id,
        strFromAnimal: `fish(${id})/animal`,
        strFromFish: `fish(${id})/fish`,
        flyBehavior: i === 0 ? 2 : null,
        sleepBehavior: 4,
    });
}

export const dataDogs: TestInitData<TestDog>[] = [];
for (let i = 0; i < 5; i += 1) {
    const id = allIds.length + 1;
    allIds.push(id);
    dataDogs.push({
        _id: id,
        strFromAnimal: `dog(${id})/animal`,
        strFromMammal: `dog(${id})/mammal`,
        strFromDog: `dog(${id})/dog`,
        flyBehavior: i === 0 ? 2 : null,
        sleepBehavior: 1,
    });
}

export const dataCats: TestInitData<TestCat>[] = [];
for (let i = 0; i < 5; i += 1) {
    const id = allIds.length + 1;
    allIds.push(id);
    dataCats.push({
        _id: id,
        strFromAnimal: `cat(${id})/animal`,
        strFromMammal: `cat(${id})/mammal`,
        strFromCat: `cat(${id})/cat`,
        flyBehavior: i === 0 ? 2 : null,
        sleepBehavior: 2,
    });
}

export const dataOwners: TestInitData<TestPetOwner>[] = [
    {
        _id: 1,
        favoritePet: dataDogs[0]._id as any,
        pets: [dataDogs[0]._id, dataCats[0]._id, dataFishes[0]._id] as any,
    },
    {
        _id: 2,
        favoritePet: dataCats[1]._id as any,
        pets: [dataDogs[1]._id, dataCats[1]._id, dataFishes[1]._id] as any,
    },
    {
        _id: 3,
        favoritePet: dataFishes[2]._id as any,
        pets: [dataDogs[2]._id, dataCats[2]._id, dataFishes[2]._id] as any,
    },
    {
        _id: 4,
        favoritePet: dataDogs[3]._id as any,
        pets: [dataDogs[3]._id, dataCats[3]._id, dataFishes[3]._id] as any,
    },
    {
        _id: 5,
        pets: [dataDogs[4]._id, dataCats[4]._id] as any,
    },
];

export const dataAnimalFlyBehavior: TestInitData<TestFlyBehavior>[] = [
    {
        _id: 1,
        flyBehavior: 'flyWithWings',
    },
    {
        _id: 2,
        flyBehavior: 'cannotFly',
    },
];

export const dataAnimalSleepBehavior: TestInitData<TestSleepBehavior>[] = [
    {
        _id: 1,
        behavior: 'sleepOnTheGround',
    },
    {
        _id: 2,
        behavior: 'sleepInATree',
    },
    {
        _id: 3,
        behavior: 'sleepInTheAir',
    },
    {
        _id: 4,
        behavior: 'neverSleep',
    },
];

// Note : this test only ensures that filters on nested collections works fine on graphQl.
// More intensive tests for this kind of filters can be found in runtime unit-tests
describe('GraphQL - Subclassing', () => {
    before(async () => {
        graphqlHelper = await graphqlSetup({ application: await testSubclassingApplication.application });
        await initTables([
            { nodeConstructor: TestFlyBehavior, data: dataAnimalFlyBehavior },
            { nodeConstructor: TestSleepBehavior, data: dataAnimalSleepBehavior },
            { nodeConstructor: TestFish, data: dataFishes },
            { nodeConstructor: TestDog, data: dataDogs },
            { nodeConstructor: TestCat, data: dataCats },
            { nodeConstructor: TestPetOwner, data: dataOwners },
        ]);
        await Test.withCommittedContext(context =>
            asyncArray(dataOwners).forEach(dataOwner =>
                asyncArray(dataOwner.pets || []).forEach(async petId => {
                    const animal = await context.read(TestAnimal, { _id: petId as any as number }, { forUpdate: true });
                    await animal.$.set({ owner: dataOwner._id as any });
                    await animal.$.save();
                }),
            ),
        );
    });

    describe('Can query', () => {
        it("Can query - 'Animal' level", async () => {
            const result = await graphqlHelper.query<{ testAnimal: QueryNode<TestAnimal> }>(
                `
            {
                testAnimal {
                    query {
                        totalCount
                        edges {
                            node {
                                strFromAnimal
                            }
                        }
                    }
                }
            }`,
            );
            assert.equal(result.testAnimal.query.totalCount, dataDogs.length + dataCats.length + dataFishes.length);
            const got = (result.testAnimal.query.edges || []).map((r: any) => r.node.strFromAnimal);
            const expected = await asyncArray([...dataFishes, ...dataDogs, ...dataCats])
                .map(d => d.strFromAnimal)
                .toArray();
            assert.deepEqual(got, expected);
        });
        it("can query - 'Mammal' level", async () => {
            const result = await graphqlHelper.query<{ testMammal: QueryNode<TestMammal> }>(
                `
            {
                testMammal {
                    query {
                        totalCount
                        edges {
                            node {
                                strFromAnimal
                                strFromMammal
                            }
                        }
                    }
                }
            }`,
            );
            assert.equal(result.testMammal.query.totalCount, dataDogs.length + dataCats.length);
            const got = (result.testMammal.query.edges || []).map(
                (r: any) => `${r.node.strFromAnimal}/${r.node.strFromMammal}`,
            );
            const expected = await asyncArray([...dataDogs, ...dataCats])
                .map(d => `${d.strFromAnimal}/${d.strFromMammal}`)
                .toArray();
            assert.deepEqual(got, expected);
        });
        it("Can query - 'Dog' level", async () => {
            const result = await graphqlHelper.query<{ testDog: QueryNode<TestDog> }>(
                `
            {
                testDog {
                    query {
                        totalCount
                        edges {
                            node {
                                strFromAnimal
                                strFromMammal
                                strFromDog
                            }
                        }
                    }
                }
            }`,
            );
            assert.equal(result.testDog.query.totalCount, dataDogs.length);
            const got = (result.testDog.query.edges || []).map(
                (r: any) => `${r.node.strFromAnimal}/${r.node.strFromMammal}/${r.node.strFromDog}`,
            );
            const expected = await asyncArray([...dataDogs])
                .map(d => `${d.strFromAnimal}/${d.strFromMammal}/${d.strFromDog}`)
                .toArray();
            assert.deepEqual(got, expected);
        });
    });

    describe('Can queryAggregate', () => {
        it("Can queryAggregate - 'Animal' level", async () => {
            const result = await graphqlHelper.query<{ testAnimal: QueryAggregateNode<TestAnimal> }>(
                `
            {
                testAnimal {
                    queryAggregate {
                        edges {
                            node {
                                group {
                                    owner {
                                        _id
                                    }
                                }
                                values {
                                    strFromAnimal {
                                        min
                                    }
                                }
                            }
                        }
                    }
                }
            }`,
            );
            assert.equal(
                result.testAnimal.queryAggregate.edges?.length,
                dataOwners.length + 1, // owners + null owner
            );
            const got = result.testAnimal.queryAggregate.edges || [];
            const expected = [
                {
                    node: {
                        group: {
                            owner: {
                                _id: null,
                            },
                        },
                        values: {
                            strFromAnimal: {
                                min: 'fish(5)/animal',
                            },
                        },
                    },
                },
                {
                    node: {
                        group: {
                            owner: {
                                _id: '1',
                            },
                        },
                        values: {
                            strFromAnimal: {
                                min: 'cat(11)/animal',
                            },
                        },
                    },
                },
                {
                    node: {
                        group: {
                            owner: {
                                _id: '2',
                            },
                        },
                        values: {
                            strFromAnimal: {
                                min: 'cat(12)/animal',
                            },
                        },
                    },
                },
                {
                    node: {
                        group: {
                            owner: {
                                _id: '3',
                            },
                        },
                        values: {
                            strFromAnimal: {
                                min: 'cat(13)/animal',
                            },
                        },
                    },
                },
                {
                    node: {
                        group: {
                            owner: {
                                _id: '4',
                            },
                        },
                        values: {
                            strFromAnimal: {
                                min: 'cat(14)/animal',
                            },
                        },
                    },
                },
                {
                    node: {
                        group: {
                            owner: {
                                _id: '5',
                            },
                        },
                        values: {
                            strFromAnimal: {
                                min: 'cat(15)/animal',
                            },
                        },
                    },
                },
            ] as unknown as QueryAggregateEdge<TestAnimal>[];
            assert.deepEqual(got, expected);
        });
    });

    describe('With fragments', () => {
        it("Can query - 'Animal' level", async () => {
            const result = await graphqlHelper.query<{ testAnimal: QueryNode<TestAnimal> }>(
                `
            {
                testAnimal {
                    query {
                        totalCount
                        edges {
                            node {
                                _constructor
                                _id
                                strFromAnimal
                                ... on TestMammal {
                                    strFromMammal
                                    ... on TestDog {
                                        strFromDog
                                    }
                                    ... on TestCat {
                                        strFromCat
                                    }
                                }
                                ... on TestFish {
                                    strFromFish
                                }
                            }
                        }
                    }
                }
            }`,
            );
            assert.deepEqual(result, {
                testAnimal: {
                    query: {
                        totalCount: 15,
                        edges: [
                            {
                                node: {
                                    _constructor: 'TestFish',
                                    _id: '1',
                                    strFromAnimal: 'fish(1)/animal',
                                    strFromFish: 'fish(1)/fish',
                                },
                            },
                            {
                                node: {
                                    _constructor: 'TestFish',
                                    _id: '2',
                                    strFromAnimal: 'fish(2)/animal',
                                    strFromFish: 'fish(2)/fish',
                                },
                            },
                            {
                                node: {
                                    _constructor: 'TestFish',
                                    _id: '3',
                                    strFromAnimal: 'fish(3)/animal',
                                    strFromFish: 'fish(3)/fish',
                                },
                            },
                            {
                                node: {
                                    _constructor: 'TestFish',
                                    _id: '4',
                                    strFromAnimal: 'fish(4)/animal',
                                    strFromFish: 'fish(4)/fish',
                                },
                            },
                            {
                                node: {
                                    _constructor: 'TestFish',
                                    _id: '5',
                                    strFromAnimal: 'fish(5)/animal',
                                    strFromFish: 'fish(5)/fish',
                                },
                            },
                            {
                                node: {
                                    _constructor: 'TestDog',
                                    _id: '6',
                                    strFromAnimal: 'dog(6)/animal',
                                    strFromMammal: 'dog(6)/mammal',
                                    strFromDog: 'dog(6)/dog',
                                },
                            },
                            {
                                node: {
                                    _constructor: 'TestDog',
                                    _id: '7',
                                    strFromAnimal: 'dog(7)/animal',
                                    strFromMammal: 'dog(7)/mammal',
                                    strFromDog: 'dog(7)/dog',
                                },
                            },
                            {
                                node: {
                                    _constructor: 'TestDog',
                                    _id: '8',
                                    strFromAnimal: 'dog(8)/animal',
                                    strFromMammal: 'dog(8)/mammal',
                                    strFromDog: 'dog(8)/dog',
                                },
                            },
                            {
                                node: {
                                    _constructor: 'TestDog',
                                    _id: '9',
                                    strFromAnimal: 'dog(9)/animal',
                                    strFromMammal: 'dog(9)/mammal',
                                    strFromDog: 'dog(9)/dog',
                                },
                            },
                            {
                                node: {
                                    _constructor: 'TestDog',
                                    _id: '10',
                                    strFromAnimal: 'dog(10)/animal',
                                    strFromMammal: 'dog(10)/mammal',
                                    strFromDog: 'dog(10)/dog',
                                },
                            },
                            {
                                node: {
                                    _constructor: 'TestCat',
                                    _id: '11',
                                    strFromAnimal: 'cat(11)/animal',
                                    strFromMammal: 'cat(11)/mammal',
                                    strFromCat: 'cat(11)/cat',
                                },
                            },
                            {
                                node: {
                                    _constructor: 'TestCat',
                                    _id: '12',
                                    strFromAnimal: 'cat(12)/animal',
                                    strFromMammal: 'cat(12)/mammal',
                                    strFromCat: 'cat(12)/cat',
                                },
                            },
                            {
                                node: {
                                    _constructor: 'TestCat',
                                    _id: '13',
                                    strFromAnimal: 'cat(13)/animal',
                                    strFromMammal: 'cat(13)/mammal',
                                    strFromCat: 'cat(13)/cat',
                                },
                            },
                            {
                                node: {
                                    _constructor: 'TestCat',
                                    _id: '14',
                                    strFromAnimal: 'cat(14)/animal',
                                    strFromMammal: 'cat(14)/mammal',
                                    strFromCat: 'cat(14)/cat',
                                },
                            },
                            {
                                node: {
                                    _constructor: 'TestCat',
                                    _id: '15',
                                    strFromAnimal: 'cat(15)/animal',
                                    strFromMammal: 'cat(15)/mammal',
                                    strFromCat: 'cat(15)/cat',
                                },
                            },
                        ],
                    },
                },
            } as any);
        });
    });
    it("Can read - 'Animal' level", async () => {
        const read = (id: number) =>
            graphqlHelper.query<{ testAnimal: QueryNode<TestAnimal> }>(
                `
        {
            testAnimal {
                read(_id:"${id}") {
                    _constructor
                    _id
                    strFromAnimal
                    ... on TestMammal {
                        strFromMammal
                        ... on TestDog {
                            strFromDog
                        }
                        ... on TestCat {
                            strFromCat
                        }
                    }
                    ... on TestFish {
                        strFromFish
                    }
                }
            }
        }`,
            );

        let result = await read(3);
        assert.deepEqual(result, {
            testAnimal: {
                read: {
                    _constructor: 'TestFish',
                    _id: '3',
                    strFromAnimal: 'fish(3)/animal',
                    strFromFish: 'fish(3)/fish',
                },
            },
        } as any);
        result = await read(7);
        assert.deepEqual(result, {
            testAnimal: {
                read: {
                    _constructor: 'TestDog',
                    _id: '7',
                    strFromAnimal: 'dog(7)/animal',
                    strFromMammal: 'dog(7)/mammal',
                    strFromDog: 'dog(7)/dog',
                },
            },
        } as any);
        result = await read(14);
        assert.deepEqual(result, {
            testAnimal: {
                read: {
                    _constructor: 'TestCat',
                    _id: '14',
                    strFromAnimal: 'cat(14)/animal',
                    strFromMammal: 'cat(14)/mammal',
                    strFromCat: 'cat(14)/cat',
                },
            },
        } as any);
    });

    it('Can read a polymorphic reference - without fragment', async () => {
        const read = (id: number) =>
            graphqlHelper.query<{ petOwner: QueryNode<TestPetOwner> }>(
                `
            {
                testPetOwner {
                    read(_id:"${id}") {
                        _id
                        favoritePet {
                            _id
                            strFromAnimal
                        }
                    }
                }
            }`,
            );

        let result = await read(2);
        assert.deepEqual(result, {
            testPetOwner: {
                read: {
                    _id: '2',
                    favoritePet: {
                        _id: `${dataCats[1]._id}`,
                        strFromAnimal: `cat(${dataCats[1]._id})/animal`,
                    },
                },
            },
        } as any);

        result = await read(5);
        assert.deepEqual(result, {
            testPetOwner: {
                read: {
                    _id: '5',
                    favoritePet: null,
                },
            },
        } as any);
    });

    it('Can read a polymorphic reference - with fragment', async () => {
        const read = (id: number) =>
            graphqlHelper.query<{ petOwner: QueryNode<TestPetOwner> }>(
                `
            {
                testPetOwner {
                    read(_id:"${id}") {
                        _id
                        favoritePet {
                            _id
                            strFromAnimal
                            ... on TestMammal {
                                strFromMammal
                                ... on TestDog {
                                    strFromDog
                                }
                                ... on TestCat {
                                    strFromCat
                                }
                            }
                        }
                    }
                }
            }`,
            );

        let result = await read(2);
        assert.deepEqual(result, {
            testPetOwner: {
                read: {
                    _id: '2',
                    favoritePet: {
                        _id: `${dataCats[1]._id}`,
                        strFromAnimal: `cat(${dataCats[1]._id})/animal`,
                        strFromMammal: `cat(${dataCats[1]._id})/mammal`,
                        strFromCat: `cat(${dataCats[1]._id})/cat`,
                    },
                },
            },
        } as any);

        result = await read(5);
        assert.deepEqual(result, {
            testPetOwner: {
                read: {
                    _id: '5',
                    favoritePet: null,
                },
            },
        } as any);
    });

    after(() => restoreTables());
});
