import { assert } from 'chai';
import { testBasicDocumentApplication } from '..';
import { Dict } from '../../lib/index';
import * as fixtures from '../fixtures';
import { GraphQlHelper, graphqlSetup, initTables, QueryNode, ReferredData, restoreTables } from '../fixtures/index';
import { TestDocument } from '../fixtures/nodes';

let graphqlHelper: GraphQlHelper;

const referredData: ReferredData[] = [
    {
        _id: 1,
        code: 'REF1',
        details: 'reference B1',
        restricted: 'restricted1',
    },
];

const documentData = [
    {
        _id: 1,
        code: 'DOCA',
        description: 'document A',
        mandatoryReference: 1,
    },
    {
        _id: 2,
        code: 'DOCB',
        description: 'document B',
        mandatoryReference: 1,
    },
];

const documentLineData = [
    {
        document: 1,
        lineNumber: 1,
        description: 'line A 1',
        _sortValue: 1,
    },
    {
        document: 2,
        lineNumber: 1,
        description: 'line B 1',
        optionalReference: 1,
        _sortValue: 1,
    },
    {
        document: 2,
        lineNumber: 2,
        description: 'line B 2',
        _sortValue: 2,
    },
    {
        document: 2,
        lineNumber: 3,
        description: 'line B 3',
        optionalReference: 1,
        _sortValue: 3,
    },
    {
        document: 2,
        lineNumber: 4,
        description: 'line B 4',
        _sortValue: 4,
    },
    {
        document: 2,
        lineNumber: 5,
        description: 'line B 5',
        optionalReference: 1,
        _sortValue: 5,
    },
    {
        document: 2,
        lineNumber: 6,
        description: 'line B 6',
        _sortValue: 6,
    },
    {
        document: 2,
        lineNumber: 7,
        description: 'line B 7',
        optionalReference: 1,
        _sortValue: 7,
    },
    {
        document: 2,
        lineNumber: 8,
        description: 'line B 8',
        _sortValue: 8,
    },
    {
        document: 2,
        lineNumber: 9,
        description: 'line B 9',
        optionalReference: 1,
        _sortValue: 9,
    },
    {
        document: 2,
        lineNumber: 10,
        description: 'line B 10',
        _sortValue: 10,
    },
];

describe('graphql nested-collection tests', () => {
    before(async () => {
        graphqlHelper = await graphqlSetup({ application: await testBasicDocumentApplication.application });
    });

    function checkDocumentLineNumbers(current: any[], expectedNumbers: number[]): void {
        const lineNumbers = current.map(c => c.node.lineNumber);
        assert.deepEqual(lineNumbers, expectedNumbers);
    }

    before(() =>
        initTables([
            { nodeConstructor: fixtures.nodes.TestReferred, data: referredData },
            { nodeConstructor: fixtures.nodes.TestDocument, data: documentData },
            { nodeConstructor: fixtures.nodes.TestDocumentLine, data: documentLineData },
        ]),
    );

    after(() => restoreTables());

    it("check 'first'", async () => {
        const result = await graphqlHelper.query<{ testDocument: QueryNode<TestDocument> }>(
            `{
                testDocument {
                    query(filter:"{code:'DOCB'}") {
                    totalCount
                    edges {
                        node {
                            description
                                lines {
                                    query(first:4) {
                                        totalCount
                                        edges {
                                            node {
                                                lineNumber
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }`,
        );
        const documents = result.testDocument.query as any;
        // We used a filter so the totalCount must return 1 (even if there are 4 docs in the db, only 1
        // meets the filter criteria)
        assert.equal(documents.totalCount, 1);
        // Doc 'DODB' has 10 lines but we only queried the first 4.
        // TotalCount should be 10
        const lines = documents.edges[0].node.lines.query;
        assert.equal(lines.edges.length, 4);
        assert.equal(lines.totalCount, documentLineData.filter(l => l.document === 2).length);
    });

    const filters: Dict<number[]> = {
        '{lineNumber:2}': [2],
        '{lineNumber:{_eq:2}}': [2],
        '{lineNumber:{_ne:2}}': [1, 3, 4, 5, 6, 7, 8, 9, 10],
        '{lineNumber:{_not: {_ne:2}}}': [2],
        '{lineNumber:{_in:[1, 3, 5]}}': [1, 3, 5],
        '{lineNumber:{_nin:[1, 3, 5]}}': [2, 4, 6, 7, 8, 9, 10],
        '{lineNumber:{_gte:4}}': [4, 5, 6, 7, 8, 9, 10],
        '{lineNumber:{_gt:4}}': [5, 6, 7, 8, 9, 10],
        '{lineNumber:{_lte:4}}': [1, 2, 3, 4],
        '{lineNumber:{_lt:4}}': [1, 2, 3],
        '{lineNumber:{_and: [{_gte: 2}, {_lte:8}]}}': [2, 3, 4, 5, 6, 7, 8],
        '{lineNumber:{_or: [{_lte: 2}, {_eq:5}, {_gte:8}]}}': [1, 2, 5, 8, 9, 10],
        '{lineNumber:{_and: [{_or: [{_lte: 2}, {_gte:8}]}, {_in:[1,5,9]}]}}': [1, 9],
        "{lineNumber:2, description:'line B 2'}": [2],
        "{lineNumber:2, description:'line B 6'}": [],
        "{_or:[{lineNumber:2}, {description:'line B 6'}]}": [2, 6],
        "{optionalReference:{details:'reference B1'}}": [1, 3, 5, 7, 9],
        // TestReferred.restricted is controled by a functionCode and should not be accessible (no filter should be applied)
        "{optionalReference:{restricted:'restricted1'}}": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
    };

    Object.keys(filters).forEach(filter => {
        const expected = filters[filter];

        it(`check filter '${filter}'`, async () => {
            const result = (await graphqlHelper.query<{ documents: { node: TestDocument } }>(
                `{
                    testDocument {
                        query(filter:"{code:{_eq:'DOCB'}}") {
                        totalCount
                        edges {
                            node {
                                description
                                    lines {
                                        query(filter:"${filter}") {
                                            totalCount
                                            edges {
                                                node {
                                                    lineNumber
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }`,
            )) as any;
            const documents = result.testDocument.query;
            assert.equal(documents.totalCount, 1);
            const lines = documents.edges[0].node.lines.query;
            assert.equal(lines.totalCount, expected.length);
            checkDocumentLineNumbers(lines.edges, expected);
        });
    });

    const orderBys: Dict<number[]> = {
        '{lineNumber:1}': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
        '{lineNumber:-1}': [10, 9, 8, 7, 6, 5, 4, 3, 2, 1],
        '{optionalReference:1}': [2, 4, 6, 8, 10, 1, 3, 5, 7, 9],
        '{optionalReference:-1}': [1, 3, 5, 7, 9, 2, 4, 6, 8, 10],
        '{optionalReference:1, lineNumber:-1}': [10, 8, 6, 4, 2, 9, 7, 5, 3, 1],
        // note : lines #2,4,6,8,10 have a null detail
        '{optionalReference:{details:1}}': [2, 4, 6, 8, 10, 1, 3, 5, 7, 9],
        '{optionalReference:{details:1}, lineNumber:-1}': [10, 8, 6, 4, 2, 9, 7, 5, 3, 1],
        '{optionalReference:{details:-1}}': [1, 3, 5, 7, 9, 2, 4, 6, 8, 10],
        '{optionalReference:{details:-1}, lineNumber:-1}': [9, 7, 5, 3, 1, 10, 8, 6, 4, 2],
    };
    Object.keys(orderBys).forEach(orderBy => {
        const expected = orderBys[orderBy];

        it(`check orderBy '${orderBy}'`, async () => {
            const result = (await graphqlHelper.query<{ documents: { node: TestDocument } }>(
                `{
                testDocument {
                    query(filter:"{code:'DOCB'}") {
                    totalCount
                    edges {
                        node {
                            description
                                lines {
                                    query(orderBy:"${orderBy}") {
                                        totalCount
                                        edges {
                                            node {
                                                lineNumber
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }`,
            )) as any;
            const documents = result.testDocument.query;
            assert.equal(documents.totalCount, 1);
            const lines = documents.edges[0].node.lines.query;
            assert.equal(lines.totalCount, documentLineData.filter(l => l.document === 2).length);
            checkDocumentLineNumbers(lines.edges, expected);
        });
    });

    const orderByAndAfters: { orderBy: string; after: string; expected: number[] }[] = [
        {
            orderBy: '{lineNumber:1}',
            after: '[5,6]#68',
            expected: [6, 7, 8, 9, 10],
        },
        {
            orderBy: '{lineNumber:-1}',
            after: '[5,6]#68',
            expected: [4, 3, 2, 1],
        },
    ];

    orderByAndAfters.forEach(orderByAndAfter => {
        it(`check orderBy(${orderByAndAfter.orderBy}) / after(${orderByAndAfter.after})`, async () => {
            const result = (await graphqlHelper.query<{ documents: { node: TestDocument } }>(
                `{
                    testDocument {
                        query(filter:"{code:'DOCB'}") {
                            totalCount
                            edges {
                                node {
                                    description
                                    lines {
                                        query(orderBy:"${orderByAndAfter.orderBy}", after:"${orderByAndAfter.after}") {
                                            totalCount
                                            edges {
                                                cursor
                                                node {
                                                    lineNumber
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }`,
            )) as any;
            const documents = result.testDocument.query;
            const lines = documents.edges[0].node.lines.query;
            assert.equal(lines.totalCount, 10);
            checkDocumentLineNumbers(lines.edges, orderByAndAfter.expected);
        });
    });

    it("check 'first' + 'filter' + 'orderBy' + 'after'", async () => {
        const result = (await graphqlHelper.query<{ documents: { node: TestDocument } }>(
            `{
                testDocument {
                    query(filter:"{code:'DOCB'}") {
                    totalCount
                    edges {
                        node {
                            description
                                lines {
                                    query(first:3,
                                        filter:"{lineNumber:{_in:[1, 3, 5, 7, 8, 9, 10]}}",
                                        orderBy:"{lineNumber:-1}",
                                        after:"[9,10]#11"
                                    ) {
                                        totalCount
                                        edges {
                                            node {
                                                lineNumber
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }`,
        )) as any;
        const documents = result.testDocument.query;
        const lines = documents.edges[0].node.lines.query;
        // We used a filter so the totalCount must return 1 (even if there are 4 docs in the db, only 1
        // meets the filter criteria)
        assert.equal(documents.totalCount, 1);
        // Doc 'DODB' has 10 lines but we only queried the first 4.
        // TotalCount should be 10
        assert.equal(lines.edges.length, 3);
        assert.equal(lines.totalCount, 7);
        checkDocumentLineNumbers(lines.edges, [8, 7, 5]);
    });

    after(() => restoreTables());
});
