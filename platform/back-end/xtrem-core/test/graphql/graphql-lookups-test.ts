import { ClientError, edgesSelector } from '@sage/xtrem-client';
import { Dict } from '@sage/xtrem-shared';
import { assert, expect } from 'chai';
import { fixtures, testLookupsApplication } from '..';
import { Application } from '../../lib';
import {
    clientSetup,
    documentData,
    documentLineData,
    GraphQlHelper,
    graphqlSetup,
    initTables,
    referredData,
    restoreTables,
} from '../fixtures/index';
import { TestDocument, TestDocumentLookup } from '../fixtures/nodes';

let graphqlHelper: GraphQlHelper;

const documentLookupData = [
    {
        _id: 1,
        parentControlInteger: 10,
    },
    {
        _id: 2,
        parentControlInteger: 11,
        details: 1,
    },
];

const referencedDocumentData = [
    {
        _id: 1,
        lookupString: 'str1',
        controlInteger: 2,
        controlDetails: 1,
    },
    {
        _id: 2,
        lookupString: 'str2',
        controlInteger: 10,
    },
    {
        _id: 3,
        lookupString: 'str2',
        controlInteger: 11,
        controlDetails: 2,
    },
    {
        _id: 4,
        lookupString: 'str2',
        controlInteger: 10,
    },
];

const referencedDocumentDetailsData = [
    {
        _id: 1,
        text: 'text1',
    },
    {
        _id: 2,
        text: 'text2',
    },
    {
        _id: 3,
        text: 'text3',
    },
];

const referencedDocumentOtherData: any[] = [];
[...Array(30).keys()].forEach(i =>
    referencedDocumentOtherData.push({
        _id: i + 1,
        lookupString: 'str1',
        controlInteger: 10,
    }),
);

const lookupQuery = `{
        testDocumentLookup {
            lookups(%) {
                reference {
                    totalCount
                    pageInfo {
                        endCursor
                        hasNextPage
                        startCursor
                        hasPreviousPage
                    }
                    edges {
                        node {
                            _id
                            lookupString
                            controlInteger
                        }

                    }
                }
                noControlReference {
                    totalCount
                    edges {
                        node {
                            _id
                            lookupString
                            controlInteger
                        }

                    }
                }
                noLookupReference {
                    totalCount
                    edges {
                        node {
                            _id
                            lookupString
                            controlInteger
                        }

                    }
                }
                noFiltersReference {
                    totalCount
                    edges {
                        node {
                            _id
                            lookupString
                            controlInteger
                        }

                    }
                }
                otherReference {
                    totalCount
                    edges {
                        node {
                            _id
                            lookupString
                            controlInteger
                        }

                    }
                }
                controlledByDetailsReference {
                    totalCount
                    edges {
                        node {
                            _id
                            lookupString
                            controlInteger
                            controlDetails { _id, text }
                        }
                    }
                }
                controlledByDetailsId {
                    totalCount
                    edges {
                        node {
                            _id
                            lookupString
                            controlInteger
                            controlDetails { _id, text }
                        }
                    }
                }
            }
        }
    }`;

const bothLookupAndControl = [
    {
        node: {
            _id: '2',
            lookupString: 'str2',
            controlInteger: 10,
        },
    },
    {
        node: {
            _id: '4',
            lookupString: 'str2',
            controlInteger: 10,
        },
    },
];

const detailsReferenceMatchingNull = [
    {
        node: {
            _id: '2',
            lookupString: 'str2',
            controlInteger: 10,
            controlDetails: null,
        },
    },
    {
        node: {
            _id: '4',
            lookupString: 'str2',
            controlInteger: 10,
            controlDetails: null,
        },
    },
];
const noLookup = bothLookupAndControl;

const noControl = [
    ...bothLookupAndControl,
    {
        node: {
            _id: '3',
            lookupString: 'str2',
            controlInteger: 11,
        },
    },
];

const noFilters = [
    ...noControl,
    {
        node: {
            _id: '1',
            lookupString: 'str1',
            controlInteger: 2,
        },
    },
];

const only1 = [
    {
        node: {
            _id: '1',
            lookupString: 'str1',
            controlInteger: 2,
            controlDetails: { _id: '1', text: 'text1' },
        },
    },
];

const only3 = [
    {
        node: {
            _id: '3',
            lookupString: 'str2',
            controlInteger: 11,
        },
    },
];

const otherReference = referencedDocumentOtherData.map(item => ({ node: { ...item, _id: `${item._id}` } }));

const expectedResults1 = {
    noControlReference: {
        totalCount: 3,
        edges: noControl,
    },
    noFiltersReference: {
        totalCount: 4,
        edges: noFilters,
    },
    noLookupReference: {
        totalCount: 2,
        edges: noLookup,
    },
    reference: {
        totalCount: 2,
        pageInfo: {
            endCursor: '[4]#93',
            hasNextPage: false,
            startCursor: '[2]#75',
            hasPreviousPage: false,
        },
        edges: bothLookupAndControl,
    },
    otherReference: {
        totalCount: 30,
        edges: otherReference.slice(0, 20),
    },
    controlledByDetailsReference: {
        totalCount: 2,
        edges: detailsReferenceMatchingNull,
    },
    controlledByDetailsId: {
        totalCount: 2,
        edges: detailsReferenceMatchingNull,
    },
} as Dict<any>;

const expectedResults2 = {
    noControlReference: {
        totalCount: 3,
        edges: noControl,
    },
    noFiltersReference: {
        totalCount: 4,
        edges: noFilters,
    },
    noLookupReference: {
        totalCount: 1,
        edges: only3,
    },
    reference: {
        totalCount: 1,
        pageInfo: {
            endCursor: '[3]#54',
            hasNextPage: false,
            startCursor: '[3]#54',
            hasPreviousPage: false,
        },
        edges: only3,
    },
    otherReference: {
        totalCount: 30,
        edges: otherReference.slice(0, 20),
    },
    controlledByDetailsReference: {
        totalCount: 1,
        edges: only1,
    },
    controlledByDetailsId: {
        totalCount: 1,
        edges: only1,
    },
} as Dict<any>;
const lookupQueryWithOptions = `{
    testDocumentLookup {
        lookups(_id: "1") {
            reference(first: 1) {
                totalCount
                edges {
                    node {
                        _id
                        lookupString
                        controlInteger
                    }

                }
            }
            referenceArrayLookup(first: 1) {
                totalCount
                edges {
                    node {
                        _id
                        lookupString
                        controlInteger
                    }

                }
            }
            otherReference(first: 18) {
                totalCount
                edges {
                    node {
                        _id
                        lookupString
                        controlInteger
                    }

                }
            }
        }
    }
}`;

const expectedResultsWithOptions = {
    reference: {
        totalCount: 2,
        edges: [
            {
                node: {
                    _id: '2',
                    lookupString: 'str2',
                    controlInteger: 10,
                },
            },
        ],
    },
    referenceArrayLookup: {
        totalCount: 2,
        edges: [
            {
                node: {
                    _id: '2',
                    lookupString: 'str2',
                    controlInteger: 10,
                },
            },
        ],
    },
    otherReference: {
        totalCount: 30,
        edges: otherReference.slice(0, 18),
    },
} as Dict<any>;

const documentLookupQueryWithData = `{
    testDocument {
        lookups(data: { lines: [{ _action: "create", description: "test" }], _id: "1" }) {
            mandatoryReference {
                totalCount
                edges {
                    node {
                        _id
                        code
                        details
                        restricted
                    }
                }
            }
        }
    }
}`;

const expectedResultsWithData = {
    mandatoryReference: {
        totalCount: 2,
        edges: [
            {
                node: {
                    _id: '1',
                    code: 'REF1',
                    details: 'reference B1',
                    restricted: null, // null because it's a restricted property
                },
            },
            {
                node: {
                    _id: '2',
                    code: 'REF2',
                    details: 'reference A2',
                    restricted: null, // null because it's a restricted property
                },
            },
        ],
    },
} as Dict<any>;

function compare(lookupResult: any, expectedResult: any): void {
    Object.keys(lookupResult.testDocumentLookup.lookups).forEach(property => {
        const expected = expectedResult[property];
        const actual = lookupResult.testDocumentLookup.lookups[property];

        assert.equal(actual.edges.length, expected.edges.length);
        assert.equal(actual.totalCount, expected.totalCount);
        if (expected.pageInfo) assert.deepEqual(actual.pageInfo, expected.pageInfo);
        actual.edges.forEach((edge: any) => expect(expected.edges).to.deep.include(edge));
    });
}

let application: Application;

describe('Graphql lookup method', () => {
    before(async () => {
        application = await testLookupsApplication.application;
        graphqlHelper = await graphqlSetup({ application });
        await initTables([
            { nodeConstructor: fixtures.nodes.TestReferencedDocumentOther, data: referencedDocumentOtherData },
            {
                nodeConstructor: fixtures.nodes.TestReferencedDocumentDetails,
                data: referencedDocumentDetailsData,
            },
            { nodeConstructor: fixtures.nodes.TestReferencedDocument, data: referencedDocumentData },
            { nodeConstructor: fixtures.nodes.TestDocumentLookup, data: documentLookupData },
            { nodeConstructor: fixtures.nodes.TestReferred, data: referredData },
            { nodeConstructor: fixtures.nodes.TestDocument, data: documentData },
            { nodeConstructor: fixtures.nodes.TestDocumentLine, data: documentLineData },
        ]);
    });

    after(() => restoreTables());

    it('works with id 1', async () => {
        const lookupResult = await graphqlHelper.query<{ documentLookups: { node: TestDocumentLookup } }>(
            lookupQuery.replace('%', '_id: "1"'),
        );

        compare(lookupResult, expectedResults1);
    });

    it('works with id 2', async () => {
        const lookupResult = await graphqlHelper.query<{ documentLookups: { node: TestDocumentLookup } }>(
            lookupQuery.replace('%', '_id: "2"'),
        );

        compare(lookupResult, expectedResults2);
    });

    it('works with data', async () => {
        const lookupResult = await graphqlHelper.query<{ documentLookups: { node: TestDocumentLookup } }>(
            lookupQuery.replace('%', 'data: { parentControlInteger: 10 }'),
        );

        compare(lookupResult, expectedResults1);
    });

    it('works with options provided', async () => {
        const lookupResult = (await graphqlHelper.query<{ documentLookups: { node: TestDocumentLookup } }>(
            lookupQueryWithOptions,
        )) as any;

        compare(lookupResult, expectedResultsWithOptions);
    });

    it('works with vital collection data passed in', async () => {
        const lookupResult = (await graphqlHelper.query<{ documentLookups: { node: TestDocument } }>(
            documentLookupQueryWithData,
        )) as any;

        Object.keys(lookupResult.testDocument.lookups).forEach(property => {
            const expected = expectedResultsWithData[property];
            const actual = lookupResult.testDocument.lookups[property];

            assert.equal(actual.edges.length, expected.edges.length);
            assert.equal(actual.totalCount, expected.totalCount);
            assert.deepEqual(actual.edges, expected.edges);
        });
    });

    it('works when called with the typescript api', async () => {
        const graph = await clientSetup({ application });
        const testDocumentLookup = graph.node('@sage/xtrem-core/testDocumentLookup');
        const selector = edgesSelector({ _id: true, lookupString: true, controlInteger: true });

        let result = (await testDocumentLookup.lookups('1').reference(selector).execute()).edges.map(
            (edge: { node: any }) => edge.node,
        );
        let expectedNodes = expectedResults1.reference.edges.map((edge: { node: any }) => edge.node);
        expectedNodes.forEach((edge: any) => expect(result).to.deep.include(edge));

        result = (
            await testDocumentLookup
                .lookups({ data: { parentControlInteger: 10 } })
                .noFiltersReference(selector)
                .execute()
        ).edges.map((edge: { node: any }) => edge.node);
        expectedNodes = expectedResults1.noFiltersReference.edges.map((edge: { node: any }) => edge.node);
        expectedNodes.forEach((edge: any) => expect(result).to.deep.include(edge));
    });

    it('passes filters/control on reference property, with valid reference', async () => {
        const graph = await clientSetup({ application });
        const testDocumentLookup = graph.node('@sage/xtrem-core/testDocumentLookup');

        const result = await testDocumentLookup
            .create(
                {
                    _id: true,
                    details: { _id: true, text: true },
                    controlledByDetailsReference: {
                        _id: true,
                        controlDetails: { _id: true, text: true },
                    },
                },
                {
                    data: {
                        details: '1',
                        controlledByDetailsReference: '1',
                    },
                },
            )
            .execute();
        assert.deepEqual(result, {
            _id: '3',
            details: { _id: '1', text: 'text1' },
            controlledByDetailsReference: {
                _id: '1',
                controlDetails: { _id: '1', text: 'text1' },
            },
        });
    });

    it('rejects filters/control on reference property, with reference mismatch', async () => {
        const graph = await clientSetup({ application });
        const testDocumentLookup = graph.node('@sage/xtrem-core/testDocumentLookup');

        try {
            await testDocumentLookup
                .create(
                    {
                        _id: true,
                        details: { _id: true, text: true },
                        controlledByDetailsReference: {
                            _id: true,
                            controlDetails: { _id: true, text: true },
                        },
                    },
                    {
                        data: {
                            details: '1',
                            // control rule will fail because this referenced document has no controlDetails.
                            controlledByDetailsReference: '2',
                        },
                    },
                )
                .execute();
        } catch (err) {
            // err is passed as instance of Error and not ClientError in mono-repo
            // TODO: Needs investigation on where the typing is lost
            // // assert.instanceOf(err, ClientError);
            assert.deepEqual((err as ClientError).errors, [
                {
                    extensions: {
                        code: 'operation-error',
                        diagnoses: [
                            {
                                message: 'The record is not valid. You need to select a different record.',
                                path: ['controlledByDetailsReference'],
                                severity: 3,
                            },
                        ],
                    },
                    locations: [
                        {
                            column: 13,
                            line: 4,
                        },
                    ],
                    message: 'The record was not created.',
                    path: ['xtremCore', 'testDocumentLookup', 'create'],
                },
            ]);
        }
    });

    it('rejects filters/control on reference array property, with reference mismatch', async () => {
        const graph = await clientSetup({ application });
        const testDocumentLookup = graph.node('@sage/xtrem-core/testDocumentLookup');

        try {
            await testDocumentLookup
                .create(
                    {
                        _id: true,
                        details: { _id: true, text: true },
                        referenceArrayControl: {
                            _id: true,
                            controlDetails: { _id: true, text: true },
                        },
                    },
                    {
                        data: {
                            details: '1',
                            // control rule will fail because this referenced document has no controlDetails.
                            referenceArrayControl: ['2'],
                        },
                    },
                )
                .execute();
        } catch (err) {
            // err is passed as instance of Error and not ClientError in mono-repo
            // TODO: Needs investigation on where the typing is lost
            // // assert.instanceOf(err, ClientError);
            assert.deepEqual((err as ClientError).errors, [
                {
                    extensions: {
                        code: 'operation-error',
                        diagnoses: [
                            {
                                message: 'The record is not valid. You need to select a different record.',
                                path: ['referenceArrayControl', '0'],
                                severity: 3,
                            },
                        ],
                    },
                    locations: [
                        {
                            column: 13,
                            line: 4,
                        },
                    ],
                    message: 'The record was not created.',
                    path: ['xtremCore', 'testDocumentLookup', 'create'],
                },
            ]);
        }
    });
});
