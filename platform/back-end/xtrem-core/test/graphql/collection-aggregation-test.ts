import { assert } from 'chai';
import * as fixtures from '../fixtures';
import {
    GraphQlHelper,
    createApplicationWithApi,
    documentData,
    documentLineData,
    graphqlSetup,
    initTables,
    referredData,
    restoreTables,
} from '../fixtures/index';
import { ComputedDocumentLine, TestDocument, TestDocumentLine, TestReferred } from '../fixtures/nodes';

let graphqlHelper: GraphQlHelper;

const api = {
    nodes: {
        TestReferred,
        TestDocument,
        TestDocumentLine,
        ComputedDocumentLine,
    },
};

describe('graphql collection tests', () => {
    before(async () => {
        const application = await createApplicationWithApi(api);
        graphqlHelper = await graphqlSetup({ application });

        await initTables([
            { nodeConstructor: fixtures.nodes.TestReferred, data: referredData },
            { nodeConstructor: fixtures.nodes.TestDocument, data: documentData },
            { nodeConstructor: fixtures.nodes.TestDocumentLine, data: documentLineData },
        ]);
    });
    after(() => restoreTables());

    it('Nested filter on collection property without reverse reference is not supported', () =>
        assert.isRejected(
            graphqlHelper.query<{ testDocument: TestDocument }>(
                `{
                        testDocument {
                           query (filter: "{filteredLines:{_atLeast:1}}") {
                               edges{
                                    node{
                                        code
                                        filteredLines{
                                            readAggregate {
                                                lineNumber{
                                                   distinctCount
                                                   min
                                                   max
                                                   hasNull
                                               }
                                           }
                                        }
                                    }
                               }
                           }
                    }
                }`,
            ),
            /Nested filter on collection property without reverse reference is not supported*/,
        ));
    it('can query readAggregate - on collection with filter', async () => {
        const result = (await graphqlHelper.query<{ testDocument: TestDocument }>(
            `{
                testDocument {
                   query {
                       edges{
                            node{
                                code
                                filteredLines{
                                    readAggregate {
                                        lineNumber{
                                           distinctCount
                                           min
                                           max
                                           hasNull
                                       }
                                   }
                                }
                            }
                       }
                   }
            }
        }`,
        )) as any;

        assert.deepEqual(result, {
            testDocument: {
                query: {
                    edges: [
                        {
                            node: {
                                code: 'DOCA',
                                filteredLines: {
                                    readAggregate: {
                                        lineNumber: {
                                            distinctCount: 1,
                                            hasNull: false,
                                            max: 1,
                                            min: 1,
                                        },
                                    },
                                },
                            },
                        },
                        {
                            node: {
                                code: 'DOCB',
                                filteredLines: {
                                    readAggregate: {
                                        lineNumber: {
                                            distinctCount: 2,
                                            hasNull: false,
                                            max: 2,
                                            min: 1,
                                        },
                                    },
                                },
                            },
                        },
                        {
                            node: {
                                code: 'DOCC',
                                filteredLines: {
                                    readAggregate: {
                                        lineNumber: {
                                            distinctCount: 5,
                                            hasNull: false,
                                            max: 5,
                                            min: 1,
                                        },
                                    },
                                },
                            },
                        },
                        {
                            node: {
                                code: 'DOCD',
                                filteredLines: {
                                    readAggregate: {
                                        lineNumber: {
                                            distinctCount: 2,
                                            hasNull: false,
                                            max: 2,
                                            min: 1,
                                        },
                                    },
                                },
                            },
                        },
                        {
                            node: {
                                code: 'DOCE',
                                filteredLines: {
                                    readAggregate: {
                                        lineNumber: {
                                            distinctCount: 2,
                                            hasNull: false,
                                            max: 2,
                                            min: 1,
                                        },
                                    },
                                },
                            },
                        },
                        {
                            node: {
                                code: 'TEST_DELETE_LINE',
                                filteredLines: {
                                    readAggregate: {
                                        lineNumber: {
                                            distinctCount: 3,
                                            hasNull: false,
                                            max: 3,
                                            min: 1,
                                        },
                                    },
                                },
                            },
                        },
                    ],
                },
            },
        });
    });

    it('can query readAggregate - on collection with filter', async () => {
        const result = (await graphqlHelper.query<{ testDocument: TestDocument }>(
            `{
                testDocument {
                   query (filter: "{filteredLinesWithReverseReference:{_atLeast:2}}") {
                       edges{
                            node{
                                code
                                filteredLinesWithReverseReference{
                                    readAggregate {
                                        lineNumber{
                                           distinctCount
                                           min
                                           max
                                           hasNull
                                       }
                                   }
                                }
                            }
                       }
                   }
            }
        }`,
        )) as any;

        assert.deepEqual(result, {
            testDocument: {
                query: {
                    edges: [
                        {
                            node: {
                                code: 'DOCB',
                                filteredLinesWithReverseReference: {
                                    readAggregate: {
                                        lineNumber: {
                                            distinctCount: 2,
                                            hasNull: false,
                                            max: 2,
                                            min: 1,
                                        },
                                    },
                                },
                            },
                        },
                        {
                            node: {
                                code: 'DOCC',
                                filteredLinesWithReverseReference: {
                                    readAggregate: {
                                        lineNumber: {
                                            distinctCount: 5,
                                            hasNull: false,
                                            max: 5,
                                            min: 1,
                                        },
                                    },
                                },
                            },
                        },
                        {
                            node: {
                                code: 'DOCD',
                                filteredLinesWithReverseReference: {
                                    readAggregate: {
                                        lineNumber: {
                                            distinctCount: 2,
                                            hasNull: false,
                                            max: 2,
                                            min: 1,
                                        },
                                    },
                                },
                            },
                        },
                        {
                            node: {
                                code: 'DOCE',
                                filteredLinesWithReverseReference: {
                                    readAggregate: {
                                        lineNumber: {
                                            distinctCount: 2,
                                            hasNull: false,
                                            max: 2,
                                            min: 1,
                                        },
                                    },
                                },
                            },
                        },
                        {
                            node: {
                                code: 'TEST_DELETE_LINE',
                                filteredLinesWithReverseReference: {
                                    readAggregate: {
                                        lineNumber: {
                                            distinctCount: 3,
                                            hasNull: false,
                                            max: 3,
                                            min: 1,
                                        },
                                    },
                                },
                            },
                        },
                    ],
                },
            },
        });
    });

    it('can query readAggregate - on collection with filter', async () => {
        const result = (await graphqlHelper.query<{ testDocument: TestDocument }>(
            `{
                testDocument {
                   query (filter: "{code:'DOCC'}"){
                       edges{
                            node{
                                code
                                filteredLines{
                                    readAggregate {
                                        lineNumber{
                                            distinctCount
                                           min
                                           max
                                           hasNull
                                       }
                                   }
                                }
                            }
                       }
                   }
            }
        }`,
        )) as any;

        assert.deepEqual(result, {
            testDocument: {
                query: {
                    edges: [
                        {
                            node: {
                                code: 'DOCC',
                                filteredLines: {
                                    readAggregate: {
                                        lineNumber: {
                                            distinctCount: 5,
                                            hasNull: false,
                                            max: 5,
                                            min: 1,
                                        },
                                    },
                                },
                            },
                        },
                    ],
                },
            },
        });
    });

    it('Nested filter on collection property without reverse reference is not supported', () =>
        assert.isRejected(
            graphqlHelper.query<{ testDocument: TestDocument }>(
                `{
                    testDocument {
                       query (filter: "{filteredLines:{_atLeast:1}}") {
                           edges{
                                node{
                                    code
                                    filteredLines{
                                        queryAggregate {
                                            edges {
                                              node {
                                                group {
                                                  lineNumber
                                                }
                                              }
                                            }
                                            totalCount
                                          }
                                    }
                                }
                           }
                       }
                }
            }`,
            ),
            /Nested filter on collection property without reverse reference is not supported*/,
        ));

    it('can query queryAggregate - on collection with filter', async () => {
        const result = (await graphqlHelper.query<{ testDocument: TestDocument }>(
            `{
                testDocument {
                   query {
                       edges{
                            node{
                                code
                                filteredLines{
                                    queryAggregate {
                                        edges {
                                          node {
                                            group {
                                              lineNumber
                                            }
                                            values{
                                                description{
                                                    distinctCount
                                                    min
                                                    max
                                                    hasNull
                                                }
                                            }
                                          }
                                        }
                                        totalCount
                                      }
                                }
                            }
                       }
                   }
            }
        }`,
        )) as any;

        assert.deepEqual(result, {
            testDocument: {
                query: {
                    edges: [
                        {
                            node: {
                                code: 'DOCA',
                                filteredLines: {
                                    queryAggregate: {
                                        edges: [
                                            {
                                                node: {
                                                    group: {
                                                        lineNumber: 1,
                                                    },
                                                    values: {
                                                        description: {
                                                            distinctCount: 1,
                                                            hasNull: false,
                                                            max: 'line A 1',
                                                            min: 'line A 1',
                                                        },
                                                    },
                                                },
                                            },
                                        ],
                                        totalCount: 1,
                                    },
                                },
                            },
                        },
                        {
                            node: {
                                code: 'DOCB',
                                filteredLines: {
                                    queryAggregate: {
                                        edges: [
                                            {
                                                node: {
                                                    group: {
                                                        lineNumber: 1,
                                                    },
                                                    values: {
                                                        description: {
                                                            distinctCount: 1,
                                                            hasNull: false,
                                                            max: 'line B 1',
                                                            min: 'line B 1',
                                                        },
                                                    },
                                                },
                                            },
                                            {
                                                node: {
                                                    group: {
                                                        lineNumber: 2,
                                                    },
                                                    values: {
                                                        description: {
                                                            distinctCount: 1,
                                                            hasNull: false,
                                                            max: 'line B 2',
                                                            min: 'line B 2',
                                                        },
                                                    },
                                                },
                                            },
                                        ],
                                        totalCount: 2,
                                    },
                                },
                            },
                        },
                        {
                            node: {
                                code: 'DOCC',
                                filteredLines: {
                                    queryAggregate: {
                                        edges: [
                                            {
                                                node: {
                                                    group: {
                                                        lineNumber: 1,
                                                    },
                                                    values: {
                                                        description: {
                                                            distinctCount: 1,
                                                            hasNull: false,
                                                            max: 'x',
                                                            min: 'x',
                                                        },
                                                    },
                                                },
                                            },
                                            {
                                                node: {
                                                    group: {
                                                        lineNumber: 2,
                                                    },
                                                    values: {
                                                        description: {
                                                            distinctCount: 1,
                                                            hasNull: false,
                                                            max: 'y',
                                                            min: 'y',
                                                        },
                                                    },
                                                },
                                            },
                                            {
                                                node: {
                                                    group: {
                                                        lineNumber: 3,
                                                    },
                                                    values: {
                                                        description: {
                                                            distinctCount: 1,
                                                            hasNull: false,
                                                            max: 'z',
                                                            min: 'z',
                                                        },
                                                    },
                                                },
                                            },
                                            {
                                                node: {
                                                    group: {
                                                        lineNumber: 4,
                                                    },
                                                    values: {
                                                        description: {
                                                            distinctCount: 1,
                                                            hasNull: false,
                                                            max: 'y',
                                                            min: 'y',
                                                        },
                                                    },
                                                },
                                            },
                                            {
                                                node: {
                                                    group: {
                                                        lineNumber: 5,
                                                    },
                                                    values: {
                                                        description: {
                                                            distinctCount: 1,
                                                            hasNull: false,
                                                            max: 'y',
                                                            min: 'y',
                                                        },
                                                    },
                                                },
                                            },
                                        ],
                                        totalCount: 5,
                                    },
                                },
                            },
                        },
                        {
                            node: {
                                code: 'DOCD',
                                filteredLines: {
                                    queryAggregate: {
                                        edges: [
                                            {
                                                node: {
                                                    group: {
                                                        lineNumber: 1,
                                                    },
                                                    values: {
                                                        description: {
                                                            distinctCount: 1,
                                                            hasNull: false,
                                                            max: 'line D 1',
                                                            min: 'line D 1',
                                                        },
                                                    },
                                                },
                                            },
                                            {
                                                node: {
                                                    group: {
                                                        lineNumber: 2,
                                                    },
                                                    values: {
                                                        description: {
                                                            distinctCount: 1,
                                                            hasNull: false,
                                                            max: 'line D 2',
                                                            min: 'line D 2',
                                                        },
                                                    },
                                                },
                                            },
                                        ],
                                        totalCount: 2,
                                    },
                                },
                            },
                        },
                        {
                            node: {
                                code: 'DOCE',
                                filteredLines: {
                                    queryAggregate: {
                                        edges: [
                                            {
                                                node: {
                                                    group: {
                                                        lineNumber: 1,
                                                    },
                                                    values: {
                                                        description: {
                                                            distinctCount: 1,
                                                            hasNull: false,
                                                            max: 'line E 1',
                                                            min: 'line E 1',
                                                        },
                                                    },
                                                },
                                            },
                                            {
                                                node: {
                                                    group: {
                                                        lineNumber: 2,
                                                    },
                                                    values: {
                                                        description: {
                                                            distinctCount: 1,
                                                            hasNull: false,
                                                            max: 'line E 2',
                                                            min: 'line E 2',
                                                        },
                                                    },
                                                },
                                            },
                                        ],
                                        totalCount: 2,
                                    },
                                },
                            },
                        },
                        {
                            node: {
                                code: 'TEST_DELETE_LINE',
                                filteredLines: {
                                    queryAggregate: {
                                        edges: [
                                            {
                                                node: {
                                                    group: {
                                                        lineNumber: 1,
                                                    },
                                                    values: {
                                                        description: {
                                                            distinctCount: 1,
                                                            hasNull: false,
                                                            max: 'remaining line #1',
                                                            min: 'remaining line #1',
                                                        },
                                                    },
                                                },
                                            },
                                            {
                                                node: {
                                                    group: {
                                                        lineNumber: 2,
                                                    },
                                                    values: {
                                                        description: {
                                                            distinctCount: 1,
                                                            hasNull: false,
                                                            max: 'line to be deleted',
                                                            min: 'line to be deleted',
                                                        },
                                                    },
                                                },
                                            },
                                            {
                                                node: {
                                                    group: {
                                                        lineNumber: 3,
                                                    },
                                                    values: {
                                                        description: {
                                                            distinctCount: 1,
                                                            hasNull: false,
                                                            max: 'remaining line #2',
                                                            min: 'remaining line #2',
                                                        },
                                                    },
                                                },
                                            },
                                        ],
                                        totalCount: 3,
                                    },
                                },
                            },
                        },
                    ],
                },
            },
        });
    });

    it('can query queryAggregate - on collection with filter with reverse reference', async () => {
        const result = (await graphqlHelper.query<{ testDocument: TestDocument }>(
            `{
                testDocument {
                   query (filter: "{filteredLinesWithReverseReference:{_atLeast:2}}") {
                       edges{
                            node{
                                code
                                filteredLinesWithReverseReference{
                                    queryAggregate {
                                        edges {
                                          node {
                                            group {
                                              lineNumber
                                            }
                                            values{
                                                description{
                                                    distinctCount
                                                    min
                                                    max
                                                    hasNull
                                                }
                                            }
                                          }
                                        }
                                        totalCount
                                      }
                                }
                            }
                       }
                   }
            }
        }`,
        )) as any;

        assert.deepEqual(result, {
            testDocument: {
                query: {
                    edges: [
                        {
                            node: {
                                code: 'DOCB',
                                filteredLinesWithReverseReference: {
                                    queryAggregate: {
                                        edges: [
                                            {
                                                node: {
                                                    group: {
                                                        lineNumber: 1,
                                                    },
                                                    values: {
                                                        description: {
                                                            distinctCount: 1,
                                                            hasNull: false,
                                                            max: 'line B 1',
                                                            min: 'line B 1',
                                                        },
                                                    },
                                                },
                                            },
                                            {
                                                node: {
                                                    group: {
                                                        lineNumber: 2,
                                                    },
                                                    values: {
                                                        description: {
                                                            distinctCount: 1,
                                                            hasNull: false,
                                                            max: 'line B 2',
                                                            min: 'line B 2',
                                                        },
                                                    },
                                                },
                                            },
                                        ],
                                        totalCount: 2,
                                    },
                                },
                            },
                        },
                        {
                            node: {
                                code: 'DOCC',
                                filteredLinesWithReverseReference: {
                                    queryAggregate: {
                                        edges: [
                                            {
                                                node: {
                                                    group: {
                                                        lineNumber: 1,
                                                    },
                                                    values: {
                                                        description: {
                                                            distinctCount: 1,
                                                            hasNull: false,
                                                            max: 'x',
                                                            min: 'x',
                                                        },
                                                    },
                                                },
                                            },
                                            {
                                                node: {
                                                    group: {
                                                        lineNumber: 2,
                                                    },
                                                    values: {
                                                        description: {
                                                            distinctCount: 1,
                                                            hasNull: false,
                                                            max: 'y',
                                                            min: 'y',
                                                        },
                                                    },
                                                },
                                            },
                                            {
                                                node: {
                                                    group: {
                                                        lineNumber: 3,
                                                    },
                                                    values: {
                                                        description: {
                                                            distinctCount: 1,
                                                            hasNull: false,
                                                            max: 'z',
                                                            min: 'z',
                                                        },
                                                    },
                                                },
                                            },
                                            {
                                                node: {
                                                    group: {
                                                        lineNumber: 4,
                                                    },
                                                    values: {
                                                        description: {
                                                            distinctCount: 1,
                                                            hasNull: false,
                                                            max: 'y',
                                                            min: 'y',
                                                        },
                                                    },
                                                },
                                            },
                                            {
                                                node: {
                                                    group: {
                                                        lineNumber: 5,
                                                    },
                                                    values: {
                                                        description: {
                                                            distinctCount: 1,
                                                            hasNull: false,
                                                            max: 'y',
                                                            min: 'y',
                                                        },
                                                    },
                                                },
                                            },
                                        ],
                                        totalCount: 5,
                                    },
                                },
                            },
                        },
                        {
                            node: {
                                code: 'DOCD',
                                filteredLinesWithReverseReference: {
                                    queryAggregate: {
                                        edges: [
                                            {
                                                node: {
                                                    group: {
                                                        lineNumber: 1,
                                                    },
                                                    values: {
                                                        description: {
                                                            distinctCount: 1,
                                                            hasNull: false,
                                                            max: 'line D 1',
                                                            min: 'line D 1',
                                                        },
                                                    },
                                                },
                                            },
                                            {
                                                node: {
                                                    group: {
                                                        lineNumber: 2,
                                                    },
                                                    values: {
                                                        description: {
                                                            distinctCount: 1,
                                                            hasNull: false,
                                                            max: 'line D 2',
                                                            min: 'line D 2',
                                                        },
                                                    },
                                                },
                                            },
                                        ],
                                        totalCount: 2,
                                    },
                                },
                            },
                        },
                        {
                            node: {
                                code: 'DOCE',
                                filteredLinesWithReverseReference: {
                                    queryAggregate: {
                                        edges: [
                                            {
                                                node: {
                                                    group: {
                                                        lineNumber: 1,
                                                    },
                                                    values: {
                                                        description: {
                                                            distinctCount: 1,
                                                            hasNull: false,
                                                            max: 'line E 1',
                                                            min: 'line E 1',
                                                        },
                                                    },
                                                },
                                            },
                                            {
                                                node: {
                                                    group: {
                                                        lineNumber: 2,
                                                    },
                                                    values: {
                                                        description: {
                                                            distinctCount: 1,
                                                            hasNull: false,
                                                            max: 'line E 2',
                                                            min: 'line E 2',
                                                        },
                                                    },
                                                },
                                            },
                                        ],
                                        totalCount: 2,
                                    },
                                },
                            },
                        },
                        {
                            node: {
                                code: 'TEST_DELETE_LINE',
                                filteredLinesWithReverseReference: {
                                    queryAggregate: {
                                        edges: [
                                            {
                                                node: {
                                                    group: {
                                                        lineNumber: 1,
                                                    },
                                                    values: {
                                                        description: {
                                                            distinctCount: 1,
                                                            hasNull: false,
                                                            max: 'remaining line #1',
                                                            min: 'remaining line #1',
                                                        },
                                                    },
                                                },
                                            },
                                            {
                                                node: {
                                                    group: {
                                                        lineNumber: 2,
                                                    },
                                                    values: {
                                                        description: {
                                                            distinctCount: 1,
                                                            hasNull: false,
                                                            max: 'line to be deleted',
                                                            min: 'line to be deleted',
                                                        },
                                                    },
                                                },
                                            },
                                            {
                                                node: {
                                                    group: {
                                                        lineNumber: 3,
                                                    },
                                                    values: {
                                                        description: {
                                                            distinctCount: 1,
                                                            hasNull: false,
                                                            max: 'remaining line #2',
                                                            min: 'remaining line #2',
                                                        },
                                                    },
                                                },
                                            },
                                        ],
                                        totalCount: 3,
                                    },
                                },
                            },
                        },
                    ],
                },
            },
        });
    });

    it('can query queryAggregate - on collection with filter', async () => {
        const result = (await graphqlHelper.query<{ testDocument: TestDocument }>(
            `{
                testDocument {
                   query (filter: "{code:'DOCC'}"){
                       edges{
                            node{
                                code
                                filteredLines{
                                    queryAggregate {
                                        edges {
                                          node {
                                            group {
                                              lineNumber
                                            }
                                            values{
                                                description{
                                                  distinctCount
                                                  min
                                                  max
                                                  hasNull
                                                }
                                            }
                                          }
                                        }
                                        totalCount
                                      }
                                }
                            }
                       }
                   }
            }
        }`,
        )) as any;

        assert.deepEqual(result, {
            testDocument: {
                query: {
                    edges: [
                        {
                            node: {
                                code: 'DOCC',
                                filteredLines: {
                                    queryAggregate: {
                                        edges: [
                                            {
                                                node: {
                                                    group: {
                                                        lineNumber: 1,
                                                    },
                                                    values: {
                                                        description: {
                                                            distinctCount: 1,
                                                            hasNull: false,
                                                            max: 'x',
                                                            min: 'x',
                                                        },
                                                    },
                                                },
                                            },
                                            {
                                                node: {
                                                    group: {
                                                        lineNumber: 2,
                                                    },
                                                    values: {
                                                        description: {
                                                            distinctCount: 1,
                                                            hasNull: false,
                                                            max: 'y',
                                                            min: 'y',
                                                        },
                                                    },
                                                },
                                            },
                                            {
                                                node: {
                                                    group: {
                                                        lineNumber: 3,
                                                    },
                                                    values: {
                                                        description: {
                                                            distinctCount: 1,
                                                            hasNull: false,
                                                            max: 'z',
                                                            min: 'z',
                                                        },
                                                    },
                                                },
                                            },
                                            {
                                                node: {
                                                    group: {
                                                        lineNumber: 4,
                                                    },
                                                    values: {
                                                        description: {
                                                            distinctCount: 1,
                                                            hasNull: false,
                                                            max: 'y',
                                                            min: 'y',
                                                        },
                                                    },
                                                },
                                            },
                                            {
                                                node: {
                                                    group: {
                                                        lineNumber: 5,
                                                    },
                                                    values: {
                                                        description: {
                                                            distinctCount: 1,
                                                            hasNull: false,
                                                            max: 'y',
                                                            min: 'y',
                                                        },
                                                    },
                                                },
                                            },
                                        ],
                                        totalCount: 5,
                                    },
                                },
                            },
                        },
                    ],
                },
            },
        });
    });
    after(() => restoreTables());
});
