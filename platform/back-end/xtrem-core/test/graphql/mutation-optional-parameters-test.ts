import { assert } from 'chai';
import { createApplicationWithApi, GraphQlHelper, graphqlSetup, initTables } from '../fixtures/index';
import { MutationOptionalParameters } from '../fixtures/nodes';

let graphqlHelper: GraphQlHelper;

describe('Mutation optional parameters', () => {
    before(async () => {
        graphqlHelper = await graphqlSetup({
            application: await createApplicationWithApi({ nodes: { MutationOptionalParameters } }),
        });
        await initTables([]);
    });

    it('Can call a mutation leaving out first optional parameter', async () => {
        const result = await graphqlHelper.mutation(
            `{ 
                        mutationOptionalParameters { testMutationOptional(parm1Mandatory: "abc", parm3Optional: "optional") {
                            parm1
                            parm2
                            parm3
                            parm4
                        }}
                    }`,
        );
        assert.deepEqual(result, {
            mutationOptionalParameters: {
                testMutationOptional: {
                    parm1: 'abc',
                    parm2: '',
                    parm3: 'optional',
                    parm4: '',
                },
            },
        });
    });
});
