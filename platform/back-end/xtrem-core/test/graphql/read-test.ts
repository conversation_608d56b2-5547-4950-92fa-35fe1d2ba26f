import { assert } from 'chai';
import { testDatatypesApplication } from '..';
import * as fixtures from '../fixtures';
import { TestDatatypesInterface } from '../fixtures/client-nodes/datatypes';
import { datatypesData, GraphQlHelper, graphqlSetup, initTables, restoreTables } from '../fixtures/index';

let graphqlHelper: GraphQlHelper;

describe('graphql read', () => {
    before(async () => {
        graphqlHelper = await graphqlSetup({ application: await testDatatypesApplication.application });
        await initTables([{ nodeConstructor: fixtures.nodes.TestDatatypes, data: datatypesData }]);
    });
    it('can read existing record', async () => {
        const result = await graphqlHelper.query<{ testDatatypes: { read: TestDatatypesInterface } }>(
            '{ testDatatypes { read(_id: "3") { _id, id, stringVal } } }',
        );
        const data = result.testDatatypes;
        assert.isObject(data);
        assert.isObject(data.read);
        assert.equal(data.read.id, 2);
        assert.equal(data.read.stringVal, datatypesData[2].stringVal);
    });
    it('gets null when reading invalid id', async () => {
        const result = await graphqlHelper.query<{ testDatatypes: { read: TestDatatypesInterface } }>(
            '{ testDatatypes { read(_id: "123") { id, stringVal } } }',
        );
        const data = result.testDatatypes;
        assert.isObject(data);
        assert.isNull(data.read);
    });
    it('can read existing record containing a json property', async () => {
        const result = await graphqlHelper.query<{ testDatatypes: { read: TestDatatypesInterface } }>(
            '{ testDatatypes { read(_id: "3") { _id, id, stringVal, jsonVal} } }',
        );
        const data = result.testDatatypes;
        assert.isObject(data);
        assert.isObject(data.read);
        assert.equal(data.read.id, 2);
        assert.equal(data.read.stringVal, datatypesData[2].stringVal);
        assert.deepEqual(JSON.parse(data.read.jsonVal), datatypesData[2].jsonVal);
    });

    after(() => restoreTables());
});
