import { AnyValue, AsyncResponse } from '@sage/xtrem-async-helper';
import { assert } from 'chai';
import { testDatatypesApplication } from '..';
import { assertIsRejectedWithDiagnoses } from '../../lib';
import * as fixtures from '../fixtures';
import {
    ConfigManager,
    datatypesData,
    GraphQlHelper,
    graphqlPageNodes,
    graphqlSetup,
    initTables,
    QueryNode,
    restoreTables,
} from '../fixtures/index';
import { TestDatatypes } from '../fixtures/nodes';

let graphqlHelper: GraphQlHelper;

const withReadonlyConfig = async <T extends AnyValue>(fn: () => AsyncResponse<T>) => {
    ConfigManager.current.graphql!.isReadonly = true;
    try {
        return await fn();
    } finally {
        ConfigManager.current.graphql!.isReadonly = false;
    }
};

describe('readonly config flag', () => {
    before(async () => {
        graphqlHelper = await graphqlSetup({ application: await testDatatypesApplication.application });
        await initTables([{ nodeConstructor: fixtures.nodes.TestDatatypes, data: datatypesData }]);
    });
    it('allows queries', () =>
        withReadonlyConfig(async () => {
            const result = await graphqlHelper.query<{ testDatatypes: QueryNode<TestDatatypes> }>(
                '{ testDatatypes { query { edges { node { id, booleanVal } } } } }',
            );
            const testDatatypes = graphqlPageNodes(result.testDatatypes.query);
            assert.equal(testDatatypes.length, datatypesData.length);
        }));
    it('forbids mutation', () =>
        withReadonlyConfig(async () => {
            await assertIsRejectedWithDiagnoses(
                graphqlHelper.execute(
                    `mutation { xtremCore { testDatatypes { create(data: { id: ${datatypesData.length} })  { id } } } }`,
                ),
                {
                    message: 'The record was not created.',
                    diagnoses: [
                        {
                            severity: 4,
                            message: 'Mutations are not allowed. Endpoint is readonly',
                            path: [],
                        },
                    ],
                },
            );
        }));
    after(() => restoreTables());
});
