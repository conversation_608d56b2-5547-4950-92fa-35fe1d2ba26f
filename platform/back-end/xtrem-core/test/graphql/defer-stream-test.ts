import { assert } from 'chai';
import { sortBy } from 'lodash';
import {
    ConfigManager,
    createApplicationWithApi,
    datatypesData,
    GraphQlHelper,
    graphqlSetup,
    initTables,
} from '../fixtures/index';
import { TestDatatypes } from '../fixtures/nodes';

let graphqlHelper: GraphQlHelper;

describe('Test query with @stream and @defer directives', () => {
    before(async () => {
        graphqlHelper = await graphqlSetup({
            application: await createApplicationWithApi({ nodes: { TestDatatypes } }),
        });
        await initTables([{ nodeConstructor: TestDatatypes, data: datatypesData }]);
    });
    it('can execute a query containing @stream and @defer directives', async () => {
        const results = await graphqlHelper.executeChunked(
            `
                query { xtremCore {  testDatatypes { query(first: 3) { edges @stream { node  { id, ...stringValFragment @defer } } } } } }
                fragment stringValFragment on TestDatatypes { stringVal }
                `,
        );

        // fibers and async/await modes don't output the chunks in the same order, so normalize them before comparing them
        const normalize = (entries: object[]) => sortBy(entries, entry => JSON.stringify(entry));

        assert.deepEqual(
            normalize(results),
            normalize([
                // first chunk, contains the response skeleton
                {
                    data: {
                        xtremCore: {
                            testDatatypes: { query: { edges: [] } },
                        },
                    },
                    hasNext: true,
                },
                // chunks for streamed edges
                ...datatypesData.slice(0, 3).map((val, i) => ({
                    data: { node: { id: val.id } },
                    hasNext: true,
                    path: ['xtremCore', 'testDatatypes', 'query', 'edges', i],
                })),
                // chunks for the deferred { stringVal } fragments
                ...datatypesData.slice(0, 3).map((val, i, array) => ({
                    data: { stringVal: val.stringVal },
                    hasNext: i < array.length - 1,
                    path: ['xtremCore', 'testDatatypes', 'query', 'edges', i, 'node'],
                })),
            ]),
            'chunks are correct',
        );

        assert.deepEqual(
            GraphQlHelper.mergeChunks(results),
            {
                xtremCore: {
                    testDatatypes: {
                        query: {
                            edges: datatypesData.slice(0, 3).map(val => ({
                                node: { id: val.id, stringVal: val.stringVal },
                            })),
                        },
                    },
                },
            },
            'merged data is what a basic (not chunked) query would return',
        );
    });
    it('can execute a query containing @stream and fetch the data regardless to maxNodesPerPage', async () => {
        const maxNodesPerPageBackup = ConfigManager.current.graphql!.maxNodesPerPage;
        ConfigManager.current.graphql!.maxNodesPerPage = 1;
        const results = await graphqlHelper.executeChunked(
            'query { xtremCore {  testDatatypes { query { edges @stream { node  { id } } } } } }',
        );
        assert.isTrue((GraphQlHelper.mergeChunks(results) as any).xtremCore.testDatatypes.query.edges.length > 1);
        ConfigManager.current.graphql!.maxNodesPerPage = maxNodesPerPageBackup;
    });
    it('set to maxNodesPerPage if first filter exists when execute a query containing @stream and fetch the data regardless to maxNodesPerPage', async () => {
        const maxNodesPerPageBackup = ConfigManager.current.graphql!.maxNodesPerPage;
        ConfigManager.current.graphql!.maxNodesPerPage = 1;
        const results = await graphqlHelper.executeChunked(
            'query { xtremCore {  testDatatypes { query(first: 1) { edges @stream { node  { id } } } } } }',
        );
        assert.isFalse((GraphQlHelper.mergeChunks(results) as any).xtremCore.testDatatypes.query.edges.length > 1);
        ConfigManager.current.graphql!.maxNodesPerPage = maxNodesPerPageBackup;
    });
});
