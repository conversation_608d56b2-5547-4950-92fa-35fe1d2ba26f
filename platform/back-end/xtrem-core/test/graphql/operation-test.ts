import { Graph } from '@sage/xtrem-client';
import { assert } from 'chai';
import { GraphApi, TestOperation$Operations } from '../fixtures/client-nodes';
import * as fixtures from '../fixtures/index';
import { clientSetup, createApplicationWithApi, initTables, restoreTables } from '../fixtures/index';
import { TestOperation, TestOperationReference } from '../fixtures/nodes';

let graph: Graph<GraphApi>;
let operationNode: TestOperation$Operations;

const referenceData = [
    {
        _id: 1,
        code: 'REF1',
        quantity: 5,
    },
    {
        _id: 2,
        code: 'REF2',
        quantity: 10,
    },
];

const refrenceOutput = referenceData.map(ref => ({ _id: `${ref._id}`, code: ref.code, quantity: ref.quantity }));

describe('Service operations', () => {
    before(async () => {
        graph = await clientSetup({
            application: await createApplicationWithApi({ nodes: { TestOperation, TestOperationReference } }),
        });
        operationNode = graph.node('@sage/xtrem-core/testOperation');
        await initTables([
            {
                nodeConstructor: fixtures.nodes.TestOperationReference,
                data: referenceData,
            },
        ]);
    });
    it('can invoke mutation returning simple type', async () => {
        const result = await operationNode.mutations
            .mutationReturningString(true, {
                code: 'ABC',
                stringVal: 'xyz',
                intVal: 5,
            })
            .execute();
        assert.equal(result, 'mutation xyz:5');
    });

    it('can invoke query returning simple type', async () => {
        const result = await operationNode.queries
            .queryReturningString(true, {
                code: 'ABC',
                stringVal: 'xyz',
                intVal: 5,
                enumVal: 'value2',
            })
            .execute();
        assert.equal(result, 'query xyz:5:value2');
    });

    it('can invoke mutation returning a node', async () => {
        const result = await operationNode.mutations
            .mutationReturningNode(
                {
                    code: true,
                    value: true,
                },
                {
                    code: 'DEF',
                    intVal: 8,
                },
            )
            .execute();
        assert.deepEqual(result, {
            code: 'DEF',
            value: 8,
        });
    });

    it('can pass node data via parameter', async () => {
        const result = await operationNode.mutations
            .mutationWithNodeParameter(
                {
                    code: true,
                    value: true,
                },
                {
                    arg: {
                        code: 'GHI',
                        value: 2,
                    },
                },
            )
            .execute();
        assert.deepEqual(result, {
            code: 'GHI',
            value: 2,
        });
    });

    const testComplexInput = async (
        arg: Parameters<TestOperation$Operations['queries']['queryWithComplexInput']>[1],
    ) => {
        const result = await operationNode.queries.queryWithComplexInput(true, arg).execute();
        assert.deepEqual(JSON.parse(result), arg);
    };

    it('can pass complex input (partial)', () =>
        testComplexInput({
            object: { mandatory: 'abc' },
        }));

    it('can pass complex input (full)', () =>
        testComplexInput({
            object: { simple: true, mandatory: 'bbb', nullable: '' },
            optionalObjects: [{ nestedStrings: ['abc', 'def'], flag: true }, {}, { flag: false }],
        }));

    it('can return complex output (partial)', async () => {
        const arg = {
            object: { mandatory: 'abc' },
        };
        const result = await operationNode.queries
            .queryWithComplexOutput(
                {
                    object: { mandatory: true },
                },
                arg,
            )
            .execute();

        assert.deepEqual(result, arg);
    });

    it('can return complex output (full)', async () => {
        const arg = {
            object: { simple: true, mandatory: 'bbb', nullable: '' },
            optionalObjects: [
                { nestedStrings: ['abc', 'def'], flag: true },
                { nestedStrings: [] },
                { nestedStrings: [], flag: false },
            ] as {
                nestedStrings: string[];
                flag?: boolean;
            }[],
        };
        const expectedResult = {
            object: arg.object,
            optionalObjects: arg.optionalObjects.map(opt => ({ flag: false, ...opt })),
        };

        const result = await operationNode.queries
            .queryWithComplexOutput(
                {
                    object: {
                        simple: true,
                        mandatory: true,
                        nullable: true,
                    },
                    optionalObjects: {
                        nestedStrings: true,
                        flag: true,
                    },
                },
                arg,
            )
            .execute();

        assert.deepEqual(result, expectedResult);
    });

    it('can pass references (input and output)', async () => {
        const arg = {
            reference: '_id:1',
            nullableReference: null,
            arrayOfReferences: ['_id:1', '_id:2'],
            arrayOfNullableReferences: ['_id:1', null, '_id:2'],
            nested: {
                reference: '_id:2',
                nullableReference: '_id:1',
                arrayOfReferences: ['_id:2', '_id:1'],
                arrayOfNullableReferences: ['_id:2', null, null],
            },
        };
        const expected = {
            reference: refrenceOutput[0],
            nullableReference: null,
            arrayOfReferences: [refrenceOutput[0], refrenceOutput[1]],
            arrayOfNullableReferences: [refrenceOutput[0], null, refrenceOutput[1]],
            nested: {
                reference: refrenceOutput[1],
                nullableReference: refrenceOutput[0],
                arrayOfReferences: [refrenceOutput[1], refrenceOutput[0]],
                arrayOfNullableReferences: [refrenceOutput[1], null, null],
            },
        };
        const result = await operationNode.queries
            .queryWithReferences(
                {
                    reference: { _id: true, code: true, quantity: true },
                    nullableReference: { _id: true, code: true, quantity: true },
                    arrayOfReferences: { _id: true, code: true, quantity: true },
                    arrayOfNullableReferences: { _id: true, code: true, quantity: true },
                    nested: {
                        reference: { _id: true, code: true, quantity: true },
                        nullableReference: { _id: true, code: true, quantity: true },
                        arrayOfReferences: { _id: true, code: true, quantity: true },
                        arrayOfNullableReferences: { _id: true, code: true, quantity: true },
                    },
                },
                arg,
            )
            .execute();

        assert.deepEqual(result, expected);
    });

    it('can return simple array', async () => {
        const result = await operationNode.queries.queryReturningSimpleArray(true, { len: 2 }).execute();

        assert.deepEqual(result, ['element 0', 'element 1']);
    });

    it('can return array of objects', async () => {
        const result = await operationNode.queries
            .queryReturningArrayOfObjects({ index: true, text: true }, { len: 2 })
            .execute();

        assert.deepEqual(result, [
            { index: 0, text: 'element 0' },
            { index: 1, text: 'element 1' },
        ]);
    });

    it('can invoke mutation with array of instances returning a string', async () => {
        const result = await operationNode.mutations
            .mutationArrayOfInstanceReturningString(true, {
                instanceArray: [
                    { code: 'A', value: 1 },
                    { code: 'B', value: 2 },
                    { code: 'C', value: 3 },
                ],
            })
            .execute();
        assert.deepEqual(result, 'A1;B2;C3');
    });

    it('should return {value} subfield when invoke mutation returning a BinaryStream', async () => {
        const result = await operationNode.mutations
            .mutationWithBinaryStreamAsResult(
                { value: true },
                {
                    binaryContent: Buffer.from('binary test').toString('base64'),
                },
            )
            .execute();
        assert.deepEqual(Buffer.from(result.value, 'base64').toString(), 'binary test');
    });

    it('should return {value} subfield when invoke mutation returning a TextStream', async () => {
        const result = await operationNode.mutations
            .mutationWithTextStreamAsResult(
                { value: true },
                {
                    textContent: 'text test',
                },
            )
            .execute();
        assert.deepEqual(result.value, 'text test');
    });

    it('handles optional parameters correctly', async () => {
        const result = await operationNode.queries
            .queryWithOptionalArgs(true, {
                option2: 'opt2',
            })
            .execute();
        assert.deepEqual(result, 'undefined:opt2:def3');
    });

    it('cannot update a non-writable reference parameter', async () => {
        await assert.isRejected(
            operationNode.mutations
                .mutationWithNonWritableReferenceParameter(true, {
                    reference: '1',
                })
                .execute(),
            'Mutation with non writable reference parameter failed.',
        );
    });

    it('can update a writable reference parameter', async () => {
        const result = await operationNode.mutations
            .mutationWithWritableReferenceParameter(true, {
                reference: '1',
            })
            .execute();

        assert.deepEqual(result, 'NEW_CODE');
    });

    it('can start a mutation as readonly and code with runInWritableContext', async () => {
        const result = await operationNode.mutations
            .mutationStartedReadonlyRunningWithWritable(true, {
                reference: '1',
            })
            .execute();

        assert.equal(result, 'START_READONLY_RUN_WRITABLE');
    });

    it('can call a mutation without parameters', async () => {
        const result = await operationNode.mutations.mutationWithoutParameters(true, {}).execute();
        assert.equal(result, 'hello');
    });

    after(() => restoreTables());
});
