import { AnyValue, AsyncResponse } from '@sage/xtrem-async-helper';
import { assert } from 'chai';
import { testDuplicatedValueApplication } from '..';
import { ConfigManager, GraphQlHelper, graphqlSetup, initTables, restoreTables } from '../fixtures/index';
import {
    TestDuplicatedChild,
    TestDuplicatedGrandChild,
    TestDuplicatedParent,
    TestDuplicatedReferenceForNull,
    TestDuplicatedSecondChild,
    duplicatedReferenceForNull,
    duplicatedValueChildData,
    duplicatedValueGrandChildData,
    duplicatedValueParentData,
    duplicatedValueSecondChildData,
} from '../fixtures/nodes';

let graphqlHelper: GraphQlHelper;

const withReadonlyConfig = async <T extends AnyValue>(fn: () => AsyncResponse<T>) => {
    ConfigManager.current.graphql!.isReadonly = true;
    try {
        return await fn();
    } finally {
        ConfigManager.current.graphql!.isReadonly = false;
    }
};

describe('graphql getDuplicate test', () => {
    before(async () => {
        graphqlHelper = await graphqlSetup({ application: await testDuplicatedValueApplication.application });
        await initTables([
            { nodeConstructor: TestDuplicatedReferenceForNull, data: duplicatedReferenceForNull },
            { nodeConstructor: TestDuplicatedParent, data: duplicatedValueParentData },
            { nodeConstructor: TestDuplicatedChild, data: duplicatedValueChildData },
            { nodeConstructor: TestDuplicatedSecondChild, data: duplicatedValueSecondChildData },
            { nodeConstructor: TestDuplicatedGrandChild, data: duplicatedValueGrandChildData },
        ]);
    });

    it('returns a duplicate of a node', () =>
        withReadonlyConfig(async () => {
            const result = (await graphqlHelper.query<{ testDuplicatedParent: TestDuplicatedParent }>(
                `{
                    testDuplicatedParent {
                        getDuplicate(_id: "2") {
                            _id
                            code
                            stringDuplicateFunction
                            lines {
                                query {
                                    edges {
                                        node {
                                            _id
                                            duplicatedParent {
                                                _id
                                            }
                                            duplicatedReferenceWithNullDuplicate{
                                                _id
                                            }
                                            code
                                            stringDuplicateFunction
                                            childLines {
                                                query {
                                                    edges {
                                                        node {
                                                            _id
                                                            duplicatedChildParent {
                                                                _id
                                                            }
                                                            code
                                                            stringDuplicateFunction
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }`,
            )) as any;
            // NOTE: getDuplicate should return first 1 row for the collection
            assert.deepEqual(result, {
                testDuplicatedParent: {
                    getDuplicate: {
                        _id: '-2',
                        code: 'CODE02',
                        stringDuplicateFunction: 'duplicatedValue',
                        lines: {
                            query: {
                                edges: [
                                    {
                                        node: {
                                            _id: '-2',
                                            duplicatedParent: {
                                                _id: '-2',
                                            },
                                            code: 'CHILD0201',
                                            duplicatedReferenceWithNullDuplicate: null,
                                            stringDuplicateFunction: 'duplicatedValue',
                                            childLines: {
                                                query: {
                                                    edges: [
                                                        {
                                                            node: {
                                                                _id: '-2',
                                                                duplicatedChildParent: {
                                                                    _id: '-2',
                                                                },
                                                                code: 'GRANDCHILD020101',
                                                                stringDuplicateFunction: 'duplicatedValue',
                                                            },
                                                        },
                                                    ],
                                                },
                                            },
                                        },
                                    },
                                ],
                            },
                        },
                    },
                },
            });
        }));

    it('returns a duplicate of a child node with _sortValue reset', () =>
        withReadonlyConfig(async () => {
            const result = (await graphqlHelper.query<{ testDuplicatedChild: TestDuplicatedChild }>(
                `{
                    testDuplicatedChild {
                        getDuplicate(_id: "2") {
                            _id
                            duplicatedParent {
                                _id
                            }
                            code
                            stringDuplicateFunction
                            _sortValue
                            childLines {
                                query {
                                    edges {
                                        node {
                                            _id
                                            duplicatedChildParent {
                                                _id
                                            }
                                            code
                                            stringDuplicateFunction
                                        }
                                    }
                                }
                            }
                        }
                    }
                }`,
            )) as any;

            // NOTE: getDuplicate should return first 1 row for the collection

            assert.deepEqual(result, {
                testDuplicatedChild: {
                    getDuplicate: {
                        _id: '-2',
                        _sortValue: 0,
                        duplicatedParent: {
                            _id: '2',
                        },
                        code: 'CHILD0201',
                        stringDuplicateFunction: 'duplicatedValue',
                        childLines: {
                            query: {
                                edges: [
                                    {
                                        node: {
                                            _id: '-2',
                                            duplicatedChildParent: {
                                                _id: '-2',
                                            },
                                            code: 'GRANDCHILD020101',
                                            stringDuplicateFunction: 'duplicatedValue',
                                        },
                                    },
                                ],
                            },
                        },
                    },
                },
            });
        }));

    after(() => restoreTables());
});
