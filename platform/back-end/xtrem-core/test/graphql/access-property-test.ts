import { edgesSelector, Graph } from '@sage/xtrem-client';
import { assert } from 'chai';
import { Test } from '../../lib';
import { StateAccess } from '../../lib/node-state/state-access';
import { GraphApi } from '../fixtures/client-nodes/index';
import {
    clientSetup,
    createApplicationWithApi,
    GraphQlHelper,
    graphqlSetup,
    initTables,
    restoreTables,
    secureData,
    siteData,
} from '../fixtures/index';
import { TestFrozen, TestSecure, TestSite } from '../fixtures/nodes';

let graph: Graph<GraphApi>;
let secureNode: GraphApi['@sage/xtrem-core/testSecure'];
let frozenNode: GraphApi['@sage/xtrem-core/testFrozen'];

let graphqlHelper: GraphQlHelper;

describe('_access property', () => {
    before(async () => {
        const application = await createApplicationWithApi({
            nodes: { TestSite, TestSecure, TestF<PERSON>zen },
        });
        graph = await clientSetup({
            application,
        });

        graphqlHelper = await graphqlSetup({
            application,
        });

        secureNode = graph.node('@sage/xtrem-core/testSecure');
        frozenNode = graph.node('@sage/xtrem-core/testFrozen');
    });

    beforeEach(() =>
        initTables([
            { nodeConstructor: TestSite, data: siteData },
            { nodeConstructor: TestSecure, data: secureData },
            {
                nodeConstructor: TestFrozen,
                data: [
                    { code: 'FROZEN', description: 'FROZEN' },
                    { code: 'NOT-FROZEN', description: 'NOT-FROZEN' },
                ],
            },
        ]),
    );
    afterEach(() => restoreTables());
    const testSecure = (title: string, user: string, expected: object) => {
        it(title, async () => {
            (graph as any).testUser = user;
            const result = await secureNode
                .query({
                    ...edgesSelector(
                        {
                            code: true,
                            site: { code: true, _access: { name: true, status: true } },
                            access: true,
                            _access: { name: true, status: true },
                        },
                        {},
                    ),
                })
                .execute();
            assert.deepEqual(result, expected);
        });
    };
    testSecure('can query with user with full access', '<EMAIL>', {
        edges: [
            {
                node: { code: 'SITE1R1', site: { code: 'SITE1', _access: [] }, access: '', _access: [] },
                cursor: '[1]#92',
            },
            {
                node: { code: 'SITE1R2A1', site: { code: 'SITE1', _access: [] }, access: 'ACCESS1', _access: [] },
                cursor: '[2]#75',
            },
            {
                node: { code: 'SITE2R1', site: { code: 'SITE2', _access: [] }, access: '', _access: [] },
                cursor: '[3]#54',
            },
            {
                node: { code: 'SITE3R1', site: { code: 'SITE3', _access: [] }, access: '', _access: [] },
                cursor: '[4]#93',
            },
            {
                node: { code: 'SITE4R1A2', site: { code: 'SITE4', _access: [] }, access: 'ACCESS2', _access: [] },
                cursor: '[5]#72',
            },
        ],
        pageInfo: { hasNextPage: false, endCursor: '[5]#72', hasPreviousPage: false, startCursor: '[1]#92' },
    });

    testSecure('can query with user with restricted access ', '<EMAIL>', {
        edges: [
            {
                node: {
                    code: 'SITE1R1',
                    site: {
                        code: null, // code is unauthorized therefore null
                        _access: [
                            { name: '$create', status: 'unauthorized' },
                            { name: '$update', status: 'unauthorized' },
                            { name: '$delete', status: 'unauthorized' },
                            { name: '$import', status: 'unauthorized' },
                            { name: 'code', status: 'unauthorized' },
                        ],
                    },
                    access: null,
                    _access: [
                        { name: '$create', status: 'unauthorized' },
                        { name: '$update', status: 'unauthorized' },
                        { name: '$delete', status: 'unauthorized' },
                        { name: '$import', status: 'unauthorized' },
                        {
                            name: 'lookupQueryWithoutGrant',
                            status: 'unauthorized',
                        },
                        { name: 'code', status: 'readonly' },
                        { name: 'site', status: 'readonly' },
                        { name: 'access', status: 'unavailable' },
                    ],
                },
                cursor: '[1]#92',
            },
            {
                node: {
                    code: 'SITE1R2A1',
                    site: {
                        code: null, // code is unauthorized therefore null
                        _access: [
                            { name: '$create', status: 'unauthorized' },
                            { name: '$update', status: 'unauthorized' },
                            { name: '$delete', status: 'unauthorized' },
                            { name: '$import', status: 'unauthorized' },
                            { name: 'code', status: 'unauthorized' },
                        ],
                    },
                    access: null,
                    _access: [
                        { name: '$create', status: 'unauthorized' },
                        { name: '$update', status: 'unauthorized' },
                        { name: '$delete', status: 'unauthorized' },
                        { name: '$import', status: 'unauthorized' },
                        {
                            name: 'lookupQueryWithoutGrant',
                            status: 'unauthorized',
                        },
                        { name: 'code', status: 'readonly' },
                        { name: 'site', status: 'readonly' },
                        { name: 'access', status: 'unavailable' },
                    ],
                },
                cursor: '[2]#75',
            },
            {
                node: {
                    code: 'SITE2R1',
                    site: {
                        code: null, // code is unauthorized therefore null
                        _access: [
                            { name: '$create', status: 'unauthorized' },
                            { name: '$update', status: 'unauthorized' },
                            { name: '$delete', status: 'unauthorized' },
                            { name: '$import', status: 'unauthorized' },
                            { name: 'code', status: 'unauthorized' },
                        ],
                    },
                    access: null,
                    _access: [
                        { name: '$create', status: 'unauthorized' },
                        { name: '$update', status: 'unauthorized' },
                        { name: '$delete', status: 'unauthorized' },
                        { name: '$import', status: 'unauthorized' },
                        {
                            name: 'lookupQueryWithoutGrant',
                            status: 'unauthorized',
                        },
                        { name: 'code', status: 'readonly' },
                        { name: 'site', status: 'readonly' },
                        { name: 'access', status: 'unavailable' },
                    ],
                },
                cursor: '[3]#54',
            },
            {
                node: {
                    code: 'SITE3R1',
                    site: {
                        code: null, // code is unauthorized therefore null
                        _access: [
                            { name: '$create', status: 'unauthorized' },
                            { name: '$update', status: 'unauthorized' },
                            { name: '$delete', status: 'unauthorized' },
                            { name: '$import', status: 'unauthorized' },
                            { name: 'code', status: 'unauthorized' },
                        ],
                    },
                    access: null,
                    _access: [
                        { name: '$create', status: 'unauthorized' },
                        { name: '$update', status: 'unauthorized' },
                        { name: '$delete', status: 'unauthorized' },
                        { name: '$import', status: 'unauthorized' },
                        {
                            name: 'lookupQueryWithoutGrant',
                            status: 'unauthorized',
                        },
                        { name: 'code', status: 'readonly' },
                        { name: 'site', status: 'readonly' },
                        { name: 'access', status: 'unavailable' },
                    ],
                },
                cursor: '[4]#93',
            },
            {
                node: {
                    code: 'SITE4R1A2',
                    site: {
                        code: null, // code is unauthorized therefore null
                        _access: [
                            { name: '$create', status: 'unauthorized' },
                            { name: '$update', status: 'unauthorized' },
                            { name: '$delete', status: 'unauthorized' },
                            { name: '$import', status: 'unauthorized' },
                            { name: 'code', status: 'unauthorized' },
                        ],
                    },
                    access: null,
                    _access: [
                        { name: '$create', status: 'unauthorized' },
                        { name: '$update', status: 'unauthorized' },
                        { name: '$delete', status: 'unauthorized' },
                        { name: '$import', status: 'unauthorized' },
                        {
                            name: 'lookupQueryWithoutGrant',
                            status: 'unauthorized',
                        },
                        { name: 'code', status: 'readonly' },
                        { name: 'site', status: 'readonly' },
                        { name: 'access', status: 'unavailable' },
                    ],
                },
                cursor: '[5]#72',
            },
        ],
        pageInfo: { hasNextPage: false, endCursor: '[5]#72', hasPreviousPage: false, startCursor: '[1]#92' },
    });

    it('can get correct diagnoses access to property is restricted and _access is not a selected property ', async () => {
        const result = await graphqlHelper.execute(
            `{
                        xtremCore{
                            testSecure {
                                query{
                                    edges{
                                        node{
                                            code
                                            access
                                        }
                                    }
                                }
                            }
                        }
                     }`,
            { userEmail: '<EMAIL>', withDiagnoses: true },
        );
        assert.deepEqual(result.extensions?.diagnoses, [
            // For this user _id is unauthorized
            {
                message: 'The property in the sort order is unavailable or unauthorized.',
                path: ['_id'],
                severity: 2,
            },
            {
                message: 'The property is unavailable.',
                path: ['xtremCore', 'testSecure', 'query', 'edges', 'node', 'access'],
                severity: 3,
            },
        ]);
    });

    it('can query _access correctly on Frozen node and property', async () => {
        (graph as any).testUser = '<EMAIL>';
        const result = await frozenNode
            .query({
                ...edgesSelector(
                    {
                        code: true,
                        description: true,
                        _access: { name: true, status: true },
                    },
                    {},
                ),
            })
            .execute();

        assert.deepEqual(result, {
            edges: [
                {
                    node: {
                        code: 'FROZEN',
                        description: 'FROZEN',
                        _access: [
                            { name: '$update', status: 'inactive' },
                            { name: 'description', status: 'readonly' },
                        ],
                    },
                    cursor: '[1]#92',
                },
                { node: { code: 'NOT-FROZEN', description: 'NOT-FROZEN', _access: [] }, cursor: '[2]#75' },
            ],
            pageInfo: { hasNextPage: false, endCursor: '[2]#75', hasPreviousPage: false, startCursor: '[1]#92' },
        });
    });

    it('can get correct node access', async () => {
        await Test.withContext(async context => {
            const frozen = await context.read(TestFrozen, { code: 'FROZEN' });
            const frozenAccess = await StateAccess.getNodeAccess(frozen.$.state, {
                selectedPropertyNames: ['code', 'description'],
            });
            // Cannot update if node is frozen
            // if property is frozen then the property is readonly
            assert.deepEqual(frozenAccess, [
                { name: '$update', status: 'inactive' },
                { name: 'description', status: 'readonly' },
            ]);

            // On duplicate the property cannot be duplicated as the state status is created
            const frozenDuplicate = await frozen.$.duplicate();

            const frozenDuplicateAccess = await StateAccess.getNodeAccess(frozenDuplicate.$.state, {
                selectedPropertyNames: ['code', 'description'],
            });

            assert.deepEqual(frozenDuplicateAccess, [{ name: '$update', status: 'inactive' }]);
        });

        await Test.withContext(
            async context => {
                const secure = await context.read(TestSecure, { code: 'SITE1R1' });
                const secureAccess = await StateAccess.getNodeAccess(secure.$.state, {
                    selectedPropertyNames: ['code', 'site', 'access'],
                });
                // Access based on user authorization
                assert.deepEqual(secureAccess, [
                    { name: '$create', status: 'unauthorized' },
                    { name: '$update', status: 'unauthorized' },
                    { name: '$delete', status: 'unauthorized' },
                    { name: '$import', status: 'unauthorized' },
                    { name: 'lookupQueryWithoutGrant', status: 'unauthorized' },
                    { name: 'code', status: 'readonly' },
                    { name: 'site', status: 'readonly' },
                    { name: 'access', status: 'unavailable' },
                ]);
            },
            { user: { email: '<EMAIL>' } },
        );
    });
});
