import { AnyValue, AsyncResponse } from '@sage/xtrem-async-helper';
import { assert } from 'chai';
import { testDefaultValueApplication } from '..';
import { ConfigManager, GraphQlHelper, graphqlSetup, initTables, restoreTables } from '../fixtures/index';
import { TestDefaultValue, TestDefaultValuesAdvanced, TestDefaultValuesReference } from '../fixtures/nodes';

let graphqlHelper: GraphQlHelper;

const withReadonlyConfig = async <T extends AnyValue>(fn: () => AsyncResponse<T>) => {
    ConfigManager.current.graphql!.isReadonly = true;
    try {
        return await fn();
    } finally {
        ConfigManager.current.graphql!.isReadonly = false;
    }
};

describe('graphql get default values tests', () => {
    before(async () => {
        graphqlHelper = await graphqlSetup({ application: await testDefaultValueApplication.application });
        await initTables([
            { nodeConstructor: TestDefaultValue, data: [] },
            {
                nodeConstructor: TestDefaultValuesReference,
                data: [
                    { _id: 1, code: 'REF1' },
                    { _id: 2, code: 'REF2' },
                ],
            },
            { nodeConstructor: TestDefaultValuesAdvanced, data: [] },
        ]);
    });
    it('returns the default values from the node', () =>
        withReadonlyConfig(async () => {
            const result = (await graphqlHelper.query<{ testDefaultValue: TestDefaultValue }>(
                `{
                    testDefaultValue {
                        getDefaults {
                            booleanNoDefault
                            booleanDefaultTrueLiteral
                            booleanDefaultTrueFunction
                            stringDefaultFunction
                            booleanNullDefault
                            dateNullDefault
                            failingComputedCollection { query { edges { node { _id } }, totalCount } }
                        }
                    }
                }`,
            )) as any;
            assert.deepEqual(result, {
                testDefaultValue: {
                    getDefaults: {
                        booleanNoDefault: false,
                        booleanDefaultTrueLiteral: true,
                        booleanDefaultTrueFunction: true,
                        stringDefaultFunction: 'true',
                        booleanNullDefault: null,
                        dateNullDefault: null,
                        failingComputedCollection: { query: { edges: [], totalCount: 0 } },
                    },
                },
            });
        }));
    it('returns the default values from the node when data is passed in', () =>
        withReadonlyConfig(async () => {
            const result = (await graphqlHelper.query<{ testDefaultValue: TestDefaultValue }>(
                `{
                    testDefaultValue {
                        getDefaults(data: { booleanDefaultTrueLiteral: false }) {
                            booleanNoDefault
                            booleanDefaultTrueLiteral
                            booleanDefaultTrueFunction
                            stringDefaultFunction
                        }
                    }
                }`,
            )) as any;
            assert.deepEqual(result, {
                testDefaultValue: {
                    getDefaults: {
                        booleanNoDefault: false,
                        booleanDefaultTrueLiteral: false,
                        booleanDefaultTrueFunction: true,
                        stringDefaultFunction: 'false',
                    },
                },
            });
        }));
    it('returns no default values if data for all fields are passed in', () =>
        withReadonlyConfig(async () => {
            const result = (await graphqlHelper.query<{ testDefaultValue: TestDefaultValue }>(
                `{
                    testDefaultValue {
                        getDefaults(data: {
                                booleanNoDefault: true,
                                booleanDefaultTrueLiteral: false,
                                booleanDefaultTrueFunction: false,
                                stringDefaultFunction: "This is a string" }) {
                                    booleanNoDefault
                                    booleanDefaultTrueLiteral
                                    booleanDefaultTrueFunction
                                    stringDefaultFunction
                        }
                    }
                }`,
            )) as any;
            assert.deepEqual(result, {
                testDefaultValue: {
                    getDefaults: {
                        booleanNoDefault: true,
                        booleanDefaultTrueLiteral: false,
                        booleanDefaultTrueFunction: false,
                        stringDefaultFunction: 'This is a string',
                    },
                },
            });
        }));
    it('returns defaults if non nullable references are missing', () =>
        withReadonlyConfig(async () => {
            const result = (await graphqlHelper.query<{ testDefaultValuesAdvanced: TestDefaultValuesAdvanced }>(
                `{
                    testDefaultValuesAdvanced {
                        getDefaults(data: {}) {
                            reference1 { code }
                            reference2 { code }
                            text1
                            text2
                        }
                    }
                }`,
            )) as any;
            assert.deepEqual(result, {
                testDefaultValuesAdvanced: {
                    getDefaults: {
                        reference1: null,
                        reference2: null,
                        text1: null,
                        text2: 'hello',
                    },
                },
            });
        }));
    it('returns defaults if non nullable references are present', () =>
        withReadonlyConfig(async () => {
            const result = (await graphqlHelper.query<{ testDefaultValuesAdvanced: TestDefaultValuesAdvanced }>(
                `{
                    testDefaultValuesAdvanced {
                        getDefaults(data: { reference1: "1" }) {
                            reference1 { code }
                            reference2 { code }
                            text1
                            text2
                        }
                    }
                }`,
            )) as any;
            assert.deepEqual(result, {
                testDefaultValuesAdvanced: {
                    getDefaults: {
                        reference1: { code: 'REF1' },
                        reference2: { code: 'REF1' },
                        text1: 'REF1',
                        text2: 'hello',
                    },
                },
            });
        }));
    it('returns defaults if non nullable references with _id: are present', () =>
        withReadonlyConfig(async () => {
            const result = (await graphqlHelper.query<{ testDefaultValuesAdvanced: TestDefaultValuesAdvanced }>(
                `{
                    testDefaultValuesAdvanced {
                        getDefaults(data: { reference1: "_id:1" }) {
                            reference1 { code }
                            reference2 { code }
                            text1
                            text2
                        }
                    }
                }`,
            )) as any;
            assert.deepEqual(result, {
                testDefaultValuesAdvanced: {
                    getDefaults: {
                        reference1: { code: 'REF1' },
                        reference2: { code: 'REF1' },
                        text1: 'REF1',
                        text2: 'hello',
                    },
                },
            });
        }));
    after(() => restoreTables());
});
