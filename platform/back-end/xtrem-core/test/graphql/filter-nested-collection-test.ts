import { AnyValue, AsyncResponse } from '@sage/xtrem-async-helper';
import { assert } from 'chai';
import { QueryPage } from '../../lib';
import * as fixtures from '../fixtures';
import {
    ConfigManager,
    GraphQlHelper,
    childData,
    createApplicationWithApi,
    grandParentData,
    graphqlPageNodes,
    graphqlSetup,
    initTables,
    parentData,
    restoreTables,
} from '../fixtures/index';
import { TestFilterNestedGrandParent } from '../fixtures/nodes';

let graphqlHelper: GraphQlHelper;

const withReadonlyConfig = async <T extends AnyValue>(fn: () => AsyncResponse<T>) => {
    ConfigManager.current.graphql!.isReadonly = true;
    try {
        return await fn();
    } finally {
        ConfigManager.current.graphql!.isReadonly = false;
    }
};

const testFilters = (filter1: string, filter2: string, filter3: string) => {
    const args1 = filter1 ? `(filter: "${filter1}")` : '';
    const args2 = filter2 ? `(filter: "${filter2}")` : '';
    const args3 = filter3 ? `(filter: "${filter3}")` : '';
    return withReadonlyConfig(async () => {
        const result = await graphqlHelper.query<{
            testFilterNestedGrandParent: {
                query: QueryPage<{
                    text: string;
                    children: {
                        query: QueryPage<{
                            text: string;
                            children: { query: QueryPage<{ text: string }> };
                        }>;
                    };
                }>;
            };
        }>(
            `{
                testFilterNestedGrandParent {
                    query${args1}  {
                        edges {
                            node {
                                text
                                children {
                                    query${args2} {
                                        edges {
                                            node {
                                                text
                                                children {
                                                    query${args3} {
                                                        edges {
                                                            node {
                                                                text
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }`,
        );
        return result;
    });
};

// Note : this test only ensures that filters on nested collections works fine on graphQl.
// More intensive tests for this kind of filters can be found in runtime unit-tests
describe('graphql filters on nested collections', () => {
    before(async () => {
        graphqlHelper = await graphqlSetup({
            application: await createApplicationWithApi({
                nodes: {
                    TestFilterNestedGrandParent: fixtures.nodes.TestFilterNestedGrandParent,
                    TestFilterNestedParent: fixtures.nodes.TestFilterNestedParent,
                    TestFilterNestedChild: fixtures.nodes.TestFilterNestedChild,
                },
            }),
        });
        await initTables([
            { nodeConstructor: fixtures.nodes.TestFilterNestedGrandParent, data: grandParentData },
            { nodeConstructor: fixtures.nodes.TestFilterNestedParent, data: parentData },
            { nodeConstructor: fixtures.nodes.TestFilterNestedChild, data: childData },
        ]);
    });

    const testGrandParentFilter = async (filter: string, expected: string[]) => {
        const result = await testFilters(filter, '', '');
        const grandParents = graphqlPageNodes(result.testFilterNestedGrandParent.query);
        assert.deepEqual(
            grandParents.map(d => d.text),
            expected,
        );
    };

    it('can query with filter on parent at grand-parent level (_atLeast)', () =>
        testGrandParentFilter('{ children: { _atLeast: 2, intVal: { _gte: 2 } } }', ['TestGrandParent#1']));
    it('can query with filter on parent at grand-parent level (_atMost)', () =>
        testGrandParentFilter('{ children: { _atMost: 1, intVal: { _gte: 2 } } }', [
            'TestGrandParent#2',
            'TestGrandParent#3',
        ]));
    it('can query with filter on parent at grand-parent level (_every)', () =>
        testGrandParentFilter('{ children: { _every: true, intVal: 1 } }', ['TestGrandParent#3']));
    it('can query with filter on parent at grand-parent level (_none)', () =>
        testGrandParentFilter('{ children: { _none: true, intVal: 2 } }', ['TestGrandParent#3']));

    it('can query with filter on parent and grand-child, at grand-parent level (_atLeast/_atLeast)', () =>
        testGrandParentFilter(
            // { _none: true, intVal: 3 } => ['TestGrandParent#2', 'TestGrandParent#3'],
            // { _atLeast: 1, intVal: 1 }  is true on grand-children of TestGrandParent#3 (there are none) but not of TestGrandParent#2
            '{ _and: [{ children: { _none: true, intVal: 3 } }, { children: { _atLeast: 1, children: { _atLeast: 1, intVal: 1 } } }] }',
            ['TestGrandParent#2'],
        ));
    it('can query with filter on parent and grand-child, at grand-parent level (_atLeast/_none)', () =>
        testGrandParentFilter(
            // { _none: true, intVal: 3 } => ['TestGrandParent#2', 'TestGrandParent#3'],
            // { _none: true, intVal: 1 }  is true on grand-children of TestGrandParent#3 (there are none) but not of TestGrandParent#2
            '{ _and: [{ children: { _none: true, intVal: 3 } }, { children: { _atLeast: 1, children: { _none: true, intVal: 1 } } }] }',
            ['TestGrandParent#3'],
        ));

    const testParentFilter = async (filter: string, expected: { text: string; children: string[] }[]) => {
        const result = await testFilters('', filter, '');
        const grandParents = graphqlPageNodes(result.testFilterNestedGrandParent.query);
        assert.deepEqual(
            grandParents.map(d => ({ text: d.text, children: d.children.query.edges?.map(child => child.node.text) })),
            expected,
        );
    };

    it('can query with filter on grand-children at parent level (_atLeast)', () =>
        testParentFilter('{ children: { _atLeast: 1, intVal: 4 } }', [
            { text: 'TestGrandParent#1', children: ['Parent#1.2', 'Parent#1.3'] },
            { text: 'TestGrandParent#2', children: ['Parent#2.1'] },
            { text: 'TestGrandParent#3', children: [] },
        ]));
    it('can query with filter on grand-children at parent level (_atMost)', () =>
        testParentFilter('{ children: {_atMost: 1, intVal: { _gte: 2 } } }', [
            { text: 'TestGrandParent#1', children: [] },
            { text: 'TestGrandParent#2', children: ['Parent#2.2'] },
            { text: 'TestGrandParent#3', children: ['Parent#3.1'] },
        ]));
    it('can query with filter on grand-children at parent level (_none)', () =>
        testParentFilter('{ children: { _none: true, intVal: { _gte: 3 } } }', [
            { text: 'TestGrandParent#1', children: [] },
            { text: 'TestGrandParent#2', children: ['Parent#2.2'] },
            { text: 'TestGrandParent#3', children: ['Parent#3.1'] },
        ]));
    it('can query with filter on grand-children at parent level (_every)', () =>
        testParentFilter('{ children: { _every: true, intVal: { _lt: 3 } } }', [
            { text: 'TestGrandParent#1', children: [] },
            { text: 'TestGrandParent#2', children: ['Parent#2.2'] },
            { text: 'TestGrandParent#3', children: ['Parent#3.1'] },
        ]));
    it('can query with filter on grand-children at parent level (_every)', () =>
        testParentFilter('{ children: { _every: true, intVal: { _eq: 1 } } }', [
            { text: 'TestGrandParent#1', children: [] },
            { text: 'TestGrandParent#2', children: ['Parent#2.2'] },
            { text: 'TestGrandParent#3', children: ['Parent#3.1'] },
        ]));
    it('can query with filter on parent at parent level (_gte)', () =>
        testParentFilter('{ intVal: { _gte: 2 } }', [
            { text: 'TestGrandParent#1', children: ['Parent#1.2', 'Parent#1.3'] },
            { text: 'TestGrandParent#2', children: ['Parent#2.2'] },
            { text: 'TestGrandParent#3', children: [] },
        ]));
    it('can query with filter on parent at parent level (_eq)', () =>
        testParentFilter('{ intVal: 2 }', [
            { text: 'TestGrandParent#1', children: ['Parent#1.2'] },
            { text: 'TestGrandParent#2', children: ['Parent#2.2'] },
            { text: 'TestGrandParent#3', children: [] },
        ]));

    const testGrandChildFilter = async (
        filter: string,
        expected: { text: string; children: { text: string; children: string[] }[] }[],
    ) => {
        const result = await testFilters('', '', filter);
        const grandParents = graphqlPageNodes(result.testFilterNestedGrandParent.query);
        assert.deepEqual(
            grandParents.map(d => ({
                text: d.text,
                children: d.children.query.edges?.map(child => ({
                    text: child.node.text,
                    children: child.node.children.query.edges?.map(grandChild => grandChild.node.text),
                })),
            })),
            expected,
        );
    };

    it('can query with filter on grand-children at grand-child level', () =>
        testGrandChildFilter('{ intVal: 4 }', [
            {
                text: 'TestGrandParent#1',
                children: [
                    { text: 'Parent#1.1', children: [] },
                    { text: 'Parent#1.2', children: ['Child#1.2.4'] },
                    { text: 'Parent#1.3', children: ['Child#1.3.4'] },
                ],
            },
            {
                text: 'TestGrandParent#2',
                children: [
                    { text: 'Parent#2.1', children: ['Child#2.1.4'] },
                    { text: 'Parent#2.2', children: [] },
                ],
            },
            {
                text: 'TestGrandParent#3',
                children: [{ text: 'Parent#3.1', children: [] }],
            },
        ]));

    it('should throw error when trying to directly access fields of collection without edges', () =>
        withReadonlyConfig(async () => {
            await assert.isRejected(
                graphqlHelper.query<{ TestNestedGrandParent: TestFilterNestedGrandParent }>(
                    `{
                        testFilterNestedGrandParent {
                            query {
                                edges {
                                    node {
                                        children {
                                            query {
                                                node {
                                                    text
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }`,
                ),
                'Cannot query field "node" on type "TestFilterNestedParent_Query"',
            );
        }));

    it('should throw error when query parameters are misplaced in the query', () =>
        withReadonlyConfig(async () => {
            await assert.isRejected(
                graphqlHelper.query<{ TestNestedGrandParent: TestFilterNestedGrandParent }>(
                    `{
                        testFilterNestedGrandParent {
                            query {
                                edges {
                                    node {
                                        children(filter: "{}") {
                                            query {
                                                edges {
                                                    node {
                                                        text
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }`,
                ),
                'Unknown argument "filter" on field "TestFilterNestedGrandParent.children"',
            );
        }));

    after(() => restoreTables());
});
