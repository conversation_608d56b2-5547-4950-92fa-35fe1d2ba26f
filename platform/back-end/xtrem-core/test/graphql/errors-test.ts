import { ClientNode } from '@sage/xtrem-client';
import { BusinessRuleError, LogicError, SecurityError, SystemError } from '@sage/xtrem-shared';
import { assert, expect } from 'chai';
import * as sinon from 'sinon';
import { Context, Logger, Node, Test, decorators, integer } from '../../index';

import { GraphQlHelper, createApplicationWithApi, graphqlSetup, initTables, restoreTables } from '../fixtures/index';

let graphqlHelper: GraphQlHelper;

const sandbox = sinon.createSandbox();

@decorators.node<TestErrors>({
    isPublished: true,
    storage: 'sql',
})
class TestErrors extends Node {
    @decorators.integerProperty<TestErrors, 'throwsLogicError'>({
        getValue(): integer {
            throw new LogicError('logic error message');
        },
        isPublished: true,
    })
    readonly throwsLogicError: Promise<integer>;

    @decorators.integerProperty<TestErrors, 'throwsSystemError'>({
        getValue(): integer {
            throw new SystemError('system error message');
        },
        isPublished: true,
    })
    readonly throwsSystemError: Promise<integer>;

    @decorators.integerProperty<TestErrors, 'throwsSecurityError'>({
        getValue(): integer {
            throw new SecurityError('security error message');
        },
        isPublished: true,
    })
    readonly throwsSecurityError: Promise<integer>;

    @decorators.integerProperty<TestErrors, 'throwsTypescriptError'>({
        getValue(): integer {
            throw new Error('typescript error message');
        },
        isPublished: true,
    })
    readonly throwsTypescriptError: Promise<integer>;

    @decorators.integerProperty<TestErrors, 'throwsBusinessRuleError'>({
        getValue(): integer {
            throw new BusinessRuleError('application error message');
        },
        isPublished: true,
    })
    readonly throwsBusinessRuleError: Promise<integer>;
}

const api = { nodes: { TestErrors } };

interface TestErrorsInterface extends ClientNode {
    throwsLogicError: integer;
    throwsSystemError: integer;
    throwsTypescriptError: integer;
    throwsBusinessRuleError: integer;
}

describe('graphql errors', () => {
    const loggerInternalsOld = {} as {
        _logAsJson?: boolean;
        isDisabled?: boolean;
    };

    function getLogTemplateSpy() {
        (Logger as any)._logAsJson = true;
        (Logger as any).isDisabled = false;
        return sandbox.spy((Logger as any).consoleFormatter, 'template');
    }

    function getJsonGraphQlLoggerError(spy: sinon.SinonSpy) {
        return spy.returnValues
            .map(r => JSON.parse(r))
            .find(r => r.domain === 'xtrem-core/graphql' && r.errorHint != null);
    }

    before(async () => {
        const application = await createApplicationWithApi(api);
        graphqlHelper = await graphqlSetup({ application });
        await initTables([{ nodeConstructor: TestErrors, data: [{ _id: 1 }] }]);
        loggerInternalsOld._logAsJson = (Logger as any)._logAsJson;
        loggerInternalsOld.isDisabled = (Logger as any).isDisabled;
    });

    afterEach(() => {
        (Logger as any)._logAsJson = loggerInternalsOld._logAsJson;
        (Logger as any).isDisabled = loggerInternalsOld.isDisabled;
        sandbox.restore();
    });

    it('gives generic message when typescript errors are thrown', async () => {
        try {
            await graphqlHelper.query<{ testErrors: { read: TestErrorsInterface } }>(
                '{ testErrors { read(_id: "1") { _id, throwsTypescriptError } } }',
            );
        } catch (err) {
            expect(err.message).to.satisfy((m: string) =>
                m.startsWith('An error has occurred. Please contact your administrator.'),
            );
        }
    });
    it('gives generic message when system errors are thrown', async () => {
        try {
            await graphqlHelper.query<{ testErrors: { read: TestErrorsInterface } }>(
                '{ testErrors { read(_id: "1") { _id, throwsSystemError } } }',
            );
        } catch (err) {
            expect(err.message).to.satisfy((m: string) =>
                m.startsWith('An error has occurred. Please contact your administrator.'),
            );
        }
    });
    it('gives generic message when security errors are thrown', async () => {
        try {
            await graphqlHelper.query<{ testErrors: { read: TestErrorsInterface } }>(
                '{ testErrors { read(_id: "1") { _id, throwsSecurityError } } }',
            );
        } catch (err) {
            expect(err.message).to.satisfy((m: string) =>
                m.startsWith('An error has occurred. Please contact your administrator.'),
            );
        }
    });
    it('gives generic message when logic errors are thrown', async () => {
        try {
            await graphqlHelper.query<{ testErrors: { read: TestErrorsInterface } }>(
                '{ testErrors { read(_id: "1") { _id, throwsLogicError } } }',
            );
        } catch (err) {
            expect(err.message).to.satisfy((m: string) =>
                m.startsWith('An error has occurred. Please contact your administrator.'),
            );
        }
    });
    it('gives specific error when application errors are thrown', async () => {
        const spy = getLogTemplateSpy();

        try {
            await graphqlHelper.query<{ testErrors: { read: TestErrorsInterface } }>(
                '{ testErrors { read(_id: "1") { _id, throwsBusinessRuleError } } }',
            );
        } catch (err) {
            expect(err.message).to.satisfy((m: string) => m.startsWith('application error message'));
        }
        const jsonError = getJsonGraphQlLoggerError(spy);
        expect(jsonError.errorHint).to.be.equal('POST /api{/*path} > query > xtremCore > testErrors > read');
    });
    it('should throw an error and write a message in the context', () =>
        Test.withContext(async (context: Context) => {
            const spy = getLogTemplateSpy();
            try {
                await graphqlHelper.mutation('{ site { create( data: { is: "1", mok: "lex" }) { id } } }', {
                    context,
                });
            } catch (e) {
                assert.exists(e);
            }
            const jsonError = getJsonGraphQlLoggerError(spy);
            expect(jsonError.errorHint).to.be.equal('POST /api{/*path} > mutation > xtremCore > site > create');
            assert.deepEqual(context.diagnoses.length, 1);
        }));

    after(async () => {
        await restoreTables();
    });
});
