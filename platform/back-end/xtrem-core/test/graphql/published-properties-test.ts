import { asyncArray } from '@sage/xtrem-async-helper';
import { assert } from 'chai';
import * as fixtures from '../fixtures';
import {
    createApplicationWithApi,
    GraphQlHelper,
    graphqlPageNodes,
    graphqlSetup,
    initTables,
    QueryNode,
    restoreTables,
    testIoData,
} from '../fixtures/index';
import { TestIoProperties } from '../fixtures/nodes';

let graphqlHelper: GraphQlHelper;

describe('published properties', () => {
    before(async () => {
        graphqlHelper = await graphqlSetup({
            application: await createApplicationWithApi({ nodes: { TestIoProperties } }),
        });
        await initTables([{ nodeConstructor: fixtures.nodes.TestIoProperties, data: testIoData }]);
    });

    it('can query a non input-only property', async () => {
        const data = asyncArray(testIoData);
        const inputAndOutputProperties = await data.map(d => d.inputAndOutputProperty).toArray();
        const outputOnlyPropertiesGet = await data.map(d => d.outputOnlyPropertyGet).toArray();
        const outputOnlyPropertiesCompute = await data.map(d => d.outputOnlyPropertyCompute).toArray();

        const result = await graphqlHelper.query<{ testIoProperties: QueryNode<TestIoProperties> }>(
            '{ testIoProperties { query { edges { node { inputAndOutputProperty, outputOnlyPropertyGet, outputOnlyPropertyCompute } } } } }',
        );
        const testIoProperties = graphqlPageNodes(result.testIoProperties.query);
        assert.equal(testIoProperties.length, testIoData.length);
        // TODO: review this statement - it DOES NOT test anything
        await asyncArray(testIoProperties)
            .map(
                async prop =>
                    inputAndOutputProperties.includes(await prop.inputAndOutputProperty) &&
                    outputOnlyPropertiesGet.includes(await prop.outputOnlyPropertyGet) &&
                    outputOnlyPropertiesCompute.includes(await prop.outputOnlyPropertyCompute),
            )
            .toArray();
    });

    it("can't query an input-only property", async () => {
        await assert.isRejected(
            graphqlHelper.query<{ testIoProperties: QueryNode<TestIoProperties> }>(
                '{ testIoProperties { query { edges { node { inputOnlyProperty } } } } }',
            ),
            'Cannot query field "inputOnlyProperty" on type "TestIoProperties". Did you mean "outputOnlyPropertyGet"?',
        );
    });

    it('can mutate a non output-only property', async () => {
        const result = (
            await graphqlHelper.mutation<{ testIoProperties: { update: { inputAndOutputProperty: string } } }>(
                `{ testIoProperties
                { update(data: {
                    _id: "1",
                    inputAndOutputProperty:"inputAndOutputProperty1Mutated",
                    inputOnlyProperty:"inputOnlyProperty1Mutated"
                })
                { inputAndOutputProperty } } }`,
            )
        ).testIoProperties.update;

        assert.equal(result.inputAndOutputProperty, 'inputAndOutputProperty1Mutated');
    });

    it("can't mutate an output-only property", async () => {
        await assert.isRejected(
            graphqlHelper.mutation(
                `{ testIoProperties
                    { update(data: {
                        _id: "1",
                        outputOnlyPropertyGet:"outputOnlyPropertyGet1Mutated"
                    })
                    { outputOnlyPropertyGet } } }`,
            ),
            'Field "outputOnlyPropertyGet" is not defined by type "TestIoProperties_Input". Did you mean "inputOnlyProperty"?',
        );

        await assert.isRejected(
            graphqlHelper.mutation(
                `{ testIoProperties
                    { update(data: {
                        _id: "1",
                        outputOnlyPropertyCompute:"outputOnlyPropertyCompute1Mutated"
                    })
                    { outputOnlyPropertyCompute } } }`,
            ),
            'Field "outputOnlyPropertyCompute" is not defined by type "TestIoProperties_Input". Did you mean "inputOnlyProperty"?',
        );
    });

    after(() => restoreTables());
});
