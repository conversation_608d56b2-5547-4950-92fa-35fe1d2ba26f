import { assert } from 'chai';
import { GraphQLError } from 'graphql';
import { createApplicationWithApi, GraphQlHelper, graphqlSetup, initTables, restoreTables } from '../fixtures/index';
import { TestValidationPersistent } from '../fixtures/nodes';

let graphqlHelper: GraphQlHelper;

describe('testing basic validation in graphql', () => {
    before(async () => {
        graphqlHelper = await graphqlSetup({
            application: await createApplicationWithApi({ nodes: { TestValidationPersistent } }),
        });
        await initTables([{ nodeConstructor: TestValidationPersistent, data: [] }]);
    });
    after(() => restoreTables());
    it('succeeds when rules pass', async () => {
        const result = await graphqlHelper.execute(
            `mutation { xtremCore { testValidationPersistent { create(data: {
                s1: "a",
                s2: "a",
                s3: "a",
                i1: 2,
                i2: 1,
            })  { s1,s2,s3,i1,i2 } } } }`,
        );
        assert.deepEqual(result, {
            data: {
                xtremCore: {
                    testValidationPersistent: {
                        create: {
                            i1: 2,
                            i2: 1,
                            s1: 'a',
                            s2: 'a',
                            s3: 'a',
                        },
                    },
                },
            },
        });
    });
    it("fails when rules don't pass", async () => {
        try {
            await graphqlHelper.execute(
                `mutation { xtremCore { testValidationPersistent { create(data: {
                    s1: "",
                    s2: "B",
                    s3: "abcdef",
                    i1: 1,
                    i2: 1,
                })  { s1,s2,s3,i1,i2 } } } }`,
            );
        } catch (err) {
            assert.deepEqual((err as GraphQLError).toJSON(), {
                message: 'The record was not created.',
                path: ['xtremCore', 'testValidationPersistent', 'create'],
                locations: [
                    {
                        column: 51,
                        line: 1,
                    },
                ],
                extensions: {
                    code: 'operation-error',
                    diagnoses: [
                        {
                            message: 'value must not be empty',
                            path: ['s1'],
                            severity: 3,
                        },
                        {
                            message: "value must be equal to ''",
                            path: ['s2'],
                            severity: 3,
                        },
                        {
                            message: "value must be equal to ''",
                            path: ['s3'],
                            severity: 3,
                        },
                        {
                            message: 'value must not match /abc/',
                            path: ['s3'],
                            severity: 3,
                        },
                        {
                            message: 'value must not match /bcd/',
                            path: ['s3'],
                            severity: 3,
                        },
                        {
                            message: 'value must be greater than 1',
                            path: ['i1'],
                            severity: 3,
                        },
                        {
                            message: "value must be equal to 'B'",
                            path: ['s1'],
                            severity: 3,
                        },
                        {
                            message: 'value must be greater than 1',
                            path: ['i2'],
                            severity: 3,
                        },
                    ],
                },
            });
        }
    });
});
