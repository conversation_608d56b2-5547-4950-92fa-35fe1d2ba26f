import { assert } from 'chai';
import { createApplicationWith<PERSON>pi, GraphQlHelper, graphqlSetup, initTables, restoreTables } from '../fixtures';
import { TestMutationLongDelay } from '../fixtures/nodes';

let graphqlHelper: GraphQlHelper;

describe('Mutation long delay', () => {
    before(async () => {
        graphqlHelper = await graphqlSetup({
            application: await createApplicationWithApi({
                nodes: { TestMutationLongDelay },
            }),
        });
    });

    beforeEach(() =>
        initTables([
            {
                nodeConstructor: TestMutationLongDelay,
                data: [
                    {
                        name: 'asd',
                    },
                ],
            },
        ]),
    );

    afterEach(() => restoreTables());

    it('should execute the query in less time than limited at', async () => {
        const result = await graphqlHelper.query(
            `{
                    testMutationLongDelay {
                        query {
                            edges {
                                node {
                                    reference { name }
                                }
                            }
                        }
                    }
                }
                `,
            {
                timeLimitAsTimestamp: performance.now() + 2000,
            },
        );

        assert.deepEqual(result, {
            testMutationLongDelay: {
                query: {
                    edges: [
                        {
                            node: {
                                reference: {
                                    name: 'asd',
                                },
                            },
                        },
                    ],
                },
            },
        });
    });

    it('should throw a timeout error, the query taking too long to execute', async () => {
        try {
            await graphqlHelper.query(
                `{
                        testMutationLongDelay {
                            query {
                                edges {
                                    node {
                                        reference { name }
                                    }
                                }
                            }
                        }
                    }
                    `,
                {
                    timeLimitAsTimestamp: performance.now() + 100,
                },
            );
        } catch (err) {
            assert.deepEqual(err.message, 'Request Timeout');
        }
    });
});
