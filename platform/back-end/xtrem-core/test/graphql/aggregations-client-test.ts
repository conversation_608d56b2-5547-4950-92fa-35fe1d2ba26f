import { aggregateEdgesSelector, edgesSelector, Graph, withoutEdges } from '@sage/xtrem-client';
import { assert } from 'chai';
import { fixtures, testAggregationApplication } from '..';
import { date } from '../../lib/index';
import { ClientAggDocumentLine, GraphApi } from '../fixtures/client-nodes';
import { clientSetup, initTables, restoreTables } from '../fixtures/index';

let graph: Graph<GraphApi>;
let aggDocumentNode: GraphApi['@sage/xtrem-core/testAggDocument'];
let aggDocumentLineNode: GraphApi['@sage/xtrem-core/testAggDocumentLine'];

export const documentData = [
    {
        _id: 1,
        code: 'DOC1',
        linesCount: 4,
        totalAmount1: 35.5,
        totalAmount2: 20.13,
        totalAmount3: 47.0,
        totalAmount4: 15.66,
        totalAmount5: 9.2,
    },
    {
        _id: 2,
        code: 'DOC2',
        linesCount: 3,
        totalAmount1: 45.0,
        totalAmount2: 20.7,
        totalAmount3: 22.91,
        totalAmount4: 22.66,
        totalAmount5: 16.01,
    },
];

export const documentLineData = [
    {
        document: 1, // 'DOC1',
        lineNumber: 1,
        quantity: 4,
        amount1: 15.0,
        amount2: 8.2,
        amount3: 7.1,
        amount4: 1.5,
        amount5: 5.65,
        date: date.make(2000, 2, 7),
    },
    {
        document: 1, // 'DOC1',
        lineNumber: 2,
        quantity: 1,
        amount1: 5.0,
        amount2: 5.13,
        amount3: 8,
        amount4: 11.5,
        amount5: 2.65,
        date: date.make(2000, 2, 7),
    },
    {
        document: 1, // 'DOC1',
        lineNumber: 3,
        quantity: 2,
        amount1: 7.0,
        amount2: 2.9,
        amount3: 15,
        amount4: 1.51,
        amount5: 0.25,
        date: date.make(2000, 2, 15),
    },
    {
        document: 1, // 'DOC1',
        lineNumber: 4,
        quantity: 8,
        amount1: 8.5,
        amount2: 3.9,
        amount3: 16.9,
        amount4: 1.15,
        amount5: 0.65,
        date: date.make(2000, 2, 15),
    },
    {
        document: 2, // 'DOC2',
        lineNumber: 1,
        quantity: 5,
        amount1: 6.5,
        amount2: 8.2,
        amount3: 14.25,
        amount4: 11.15,
        amount5: 10.36,
        date: date.make(2003, 1, 8),
    },
    {
        document: 2, // 'DOC2',
        lineNumber: 2,
        quantity: 8,
        amount1: 16.5,
        amount2: 7.6,
        amount3: 7.1,
        amount4: 9.51,
        amount5: 2.65,
        date: date.make(2003, 8, 24),
    },
    {
        document: 2, // 'DOC2',
        lineNumber: 3,
        quantity: 2,
        amount1: 22.0,
        amount2: 4.9,
        amount3: 1.56,
        amount4: 2,
        amount5: 3,
        date: date.make(2004, 2, 18),
    },
];

describe('Aggregates - graphQl', () => {
    before(async () => {
        graph = await clientSetup({ application: await testAggregationApplication.application });
        aggDocumentNode = graph.node('@sage/xtrem-core/testAggDocument');
        aggDocumentLineNode = graph.node('@sage/xtrem-core/testAggDocumentLine');
        await initTables([
            { nodeConstructor: fixtures.nodes.TestAggDocument, data: documentData },
            { nodeConstructor: fixtures.nodes.TestAggDocumentLine, data: documentLineData },
        ]);
    });
    after(() => restoreTables());
    it('can get a global aggregate with client api', async () => {
        const result = await aggDocumentLineNode.aggregate
            .read({
                amount1: {
                    min: true,
                    max: true,
                    sum: true,
                    avg: true,
                    distinctCount: true,
                },
            })
            .execute();
        assert.deepEqual(result, { amount1: { min: '5', max: '22', sum: '80.5', avg: '11.5', distinctCount: 7 } });
    });

    it('can get a global aggregate with client api - with filter', async () => {
        const result = await aggDocumentLineNode.aggregate
            .read(
                {
                    amount1: {
                        min: true,
                        max: true,
                        sum: true,
                        avg: true,
                        distinctCount: true,
                    },
                    quantity: {
                        min: true,
                        max: true,
                        sum: true,
                        avg: true,
                    },
                },
                {
                    filter: {
                        amount1: { _gt: '8' },
                    },
                },
            )
            .execute();
        assert.deepEqual(result, {
            amount1: { min: '8.5', max: '22', sum: '62', avg: '15.5', distinctCount: 4 },
            quantity: { min: 2, max: 8, sum: 22, avg: 5.5 },
        });
    });

    it('can execute a group by query with client api', async () => {
        const arg = {
            group: {
                document: { code: { _by: 'value' } as const },
            },
            values: {
                amount1: {
                    min: true,
                    max: true,
                    sum: true,
                    avg: true,
                    distinctCount: true,
                },
            },
        };
        const result = await aggDocumentLineNode.aggregate.query(aggregateEdgesSelector(arg)).execute();
        const expectedEdges = [
            {
                node: {
                    group: { document: { code: 'DOC1' } },
                    values: { amount1: { min: '5', max: '15', sum: '35.5', avg: '8.875', distinctCount: 4 } },
                },
                cursor: '["DOC1"]#55',
            },
            {
                node: {
                    group: { document: { code: 'DOC2' } },
                    values: { amount1: { min: '6.5', max: '22', sum: '45', avg: '15', distinctCount: 3 } },
                },
                cursor: '["DOC2"]#82',
            },
        ];
        assert.deepEqual(result, {
            edges: expectedEdges,
            pageInfo: {
                startCursor: '["DOC1"]#55',
                endCursor: '["DOC2"]#82',
                hasPreviousPage: false,
                hasNextPage: false,
            },
        });

        const lines = withoutEdges(result);
        assert.deepEqual(
            lines,
            expectedEdges.map(edge => edge.node),
        );
    });

    it('can execute a group by query with client api - with filter', async () => {
        const arg = {
            group: {
                document: { code: { _by: 'value' } as const },
            },
            values: {
                amount1: {
                    min: true,
                    max: true,
                    sum: true,
                    avg: true,
                    distinctCount: true,
                },
            },
        };

        const result = await aggDocumentLineNode.aggregate
            .query(
                aggregateEdgesSelector(arg, {
                    filter: {
                        amount1: { _gt: '8' },
                    },
                }),
            )
            .execute();

        const expectedEdges = [
            {
                node: {
                    group: { document: { code: 'DOC1' } },
                    values: { amount1: { min: '8.5', max: '15', sum: '23.5', avg: '11.75', distinctCount: 2 } },
                },
                cursor: '["DOC1"]#55',
            },
            {
                node: {
                    group: { document: { code: 'DOC2' } },
                    values: { amount1: { min: '16.5', max: '22', sum: '38.5', avg: '19.25', distinctCount: 2 } },
                },
                cursor: '["DOC2"]#82',
            },
        ];
        assert.deepEqual(result, {
            edges: expectedEdges,
            pageInfo: {
                startCursor: '["DOC1"]#55',
                endCursor: '["DOC2"]#82',
                hasPreviousPage: false,
                hasNextPage: false,
            },
        });

        const lines = withoutEdges(result);
        assert.deepEqual(
            lines,
            expectedEdges.map(edge => edge.node),
        );
    });

    it('can execute readAggregate on a collection', async () => {
        const result = await aggDocumentNode
            .query(
                edgesSelector({
                    code: true,
                    lines: {
                        readAggregate: {
                            amount1: {
                                min: true,
                            },
                        },
                    },
                }),
                {},
            )

            .execute();
        const expectedEdges = [
            {
                code: 'DOC1',
                lines: {
                    readAggregate: { amount1: { min: '5' } },
                },
            },
            {
                code: 'DOC2',
                lines: {
                    readAggregate: { amount1: { min: '6.5' } },
                },
            },
        ];
        assert.deepEqual(result, {
            edges: [
                {
                    node: expectedEdges[0],
                    cursor: '[1]#92',
                },
                {
                    node: expectedEdges[1],
                    cursor: '[2]#75',
                },
            ],
            pageInfo: { hasNextPage: false, endCursor: '[2]#75', hasPreviousPage: false, startCursor: '[1]#92' },
        });

        const lines = withoutEdges(result);
        assert.deepEqual(lines, expectedEdges);
    });

    it('can execute queryAggregate on a collection', async () => {
        const arg = {
            group: {
                date: { _by: 'year' } as const,
            },

            values: {
                amount1: {
                    min: true,
                },
            },
        };

        const result = (await aggDocumentNode
            .query(
                edgesSelector({
                    code: true,
                    lines: {
                        queryAggregate: aggregateEdgesSelector<ClientAggDocumentLine>(arg, {}),
                    },
                } as any),
                {},
            )
            .execute()) as any; // too hard to get typing right with nested queryAggregate - will get back to it later

        const expectedEdges = [
            {
                code: 'DOC1',
                lines: {
                    queryAggregate: [{ group: { date: '2000-01-01' }, values: { amount1: { min: '5' } } }],
                },
            },
            {
                code: 'DOC2',
                lines: {
                    queryAggregate: [
                        { group: { date: '2003-01-01' }, values: { amount1: { min: '6.5' } } },
                        { group: { date: '2004-01-01' }, values: { amount1: { min: '22' } } },
                    ],
                },
            },
        ];
        assert.deepEqual(result, {
            edges: [
                {
                    node: {
                        code: 'DOC1',
                        lines: {
                            queryAggregate: {
                                edges: [
                                    {
                                        node: { group: { date: '2000-01-01' }, values: { amount1: { min: '5' } } },
                                        cursor: '["2000-01-01"]#07',
                                    },
                                ],
                                pageInfo: {
                                    hasNextPage: false,
                                    endCursor: '["2000-01-01"]#07',
                                    hasPreviousPage: false,
                                    startCursor: '["2000-01-01"]#07',
                                },
                            },
                        },
                    },
                    cursor: '[1]#92',
                },
                {
                    node: {
                        code: 'DOC2',
                        lines: {
                            queryAggregate: {
                                edges: [
                                    {
                                        node: {
                                            group: { date: '2003-01-01' },
                                            values: { amount1: { min: '6.5' } },
                                        },
                                        cursor: '["2003-01-01"]#82',
                                    },
                                    {
                                        node: { group: { date: '2004-01-01' }, values: { amount1: { min: '22' } } },
                                        cursor: '["2004-01-01"]#87',
                                    },
                                ],
                                pageInfo: {
                                    hasNextPage: false,
                                    endCursor: '["2004-01-01"]#87',
                                    hasPreviousPage: false,
                                    startCursor: '["2003-01-01"]#82',
                                },
                            },
                        },
                    },
                    cursor: '[2]#75',
                },
            ],
            pageInfo: { hasNextPage: false, endCursor: '[2]#75', hasPreviousPage: false, startCursor: '[1]#92' },
        });

        const lines = withoutEdges(result);
        assert.deepEqual(lines, expectedEdges);
    });
});
