import { assert } from 'chai';
import * as fixtures from '../fixtures';
import { createApplicationWith<PERSON>pi, GraphQlHelper, graphqlSetup, initTables, restoreTables } from '../fixtures/index';
import { TestNullable } from '../fixtures/nodes';

let graphqlHelper: GraphQlHelper;

const testNullableData = [
    {
        _id: 1,
        nullableDecimal: 12,
    },
];

describe('Mutation with a null decimal', () => {
    before(async () => {
        graphqlHelper = await graphqlSetup({
            application: await createApplicationWithApi({ nodes: { TestNullable } }),
        });
        await initTables([{ nodeConstructor: fixtures.nodes.TestNullable, data: testNullableData }]);
    });
    it('empty strings for decimals are converted to null in mutations', async () => {
        const result = await graphqlHelper.mutation<{ testNullable: { update: { nullableDecimal: string | null } } }>(
            `{ 
                    testNullable { update( data: { _id:"1", nullableDecimal: "" } ) {
                        id,
                        nullableDecimal
                } }
            }`,
        );
        assert.equal(result.testNullable.update.nullableDecimal, null);
    });
    it('mutation passes with an explicit null value', async () => {
        const result = await graphqlHelper.mutation<{ testNullable: { update: { nullableDecimal: string | null } } }>(
            `{ 
                    testNullable { update( data: { _id:"1", nullableDecimal: null } ) {
                        id,
                        nullableDecimal
                } }
            }`,
        );
        assert.equal(result.testNullable.update.nullableDecimal, null);
    });
    after(() => restoreTables());
});
