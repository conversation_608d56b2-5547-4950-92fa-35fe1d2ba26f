import {
    ClientNode,
    ClientNodeInput,
    CreateOperation,
    edgesSelector,
    Graph,
    QueryOperation,
    UpdateByIdOperation,
    UpdateOperation,
} from '@sage/xtrem-client';
import { assert } from 'chai';
import { decorators, Node } from '../../index';
import { codeDataType } from '../fixtures/data-types/data-types';
import { clientSetup, createApplicationWithApi, initTables, restoreTables } from '../fixtures/index';

@decorators.node<TestCachedNode>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    tableName: 'TestCachedNode',
    isCached: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
class TestCachedNode extends Node {
    @decorators.stringProperty<TestCachedNode, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.booleanProperty<TestCachedNode, 'isActive'>({
        isPublished: true,
        isStored: true,
    })
    readonly isActive: Promise<boolean>;
}

interface ClientCacheNode extends ClientNode {
    code: string;
    isActive: boolean;
}

interface CacheNodeInput extends ClientNodeInput {
    code?: string;
    isActive?: boolean;
}

interface TestCachedNode$Operations {
    query: QueryOperation<ClientCacheNode>;
    create: CreateOperation<CacheNodeInput, ClientCacheNode>;
    update: UpdateOperation<CacheNodeInput, ClientCacheNode>;
    updateById: UpdateByIdOperation<CacheNodeInput, ClientCacheNode>;
}

let graph: Graph<{ '@sage/xtrem-core/testCachedNode': TestCachedNode$Operations }>;
let testCachedNode: TestCachedNode$Operations;

describe('globalCache tests', () => {
    beforeEach(async () => {
        graph = await clientSetup({ application: await createApplicationWithApi({ nodes: { TestCachedNode } }) });
        testCachedNode = graph.node('@sage/xtrem-core/testCachedNode');

        await initTables([{ nodeConstructor: TestCachedNode, data: [] }]);
    });

    afterEach(() => restoreTables());

    it('mutation: create cached nodes', async () => {
        for (let i = 0; i < 10; i += 1) {
            await testCachedNode
                .create({ _id: true, code: true, isActive: true }, { data: { code: `code${i + 1}`, isActive: true } })
                .execute();
        }

        const nodes = await testCachedNode
            .query(
                edgesSelector({
                    _id: true,
                    code: true,
                    isActive: true,
                }),
            )
            .execute();
        assert.equal(nodes.edges.length, 10);
        nodes.edges.forEach((edge: any) => assert.equal(edge.node.isActive, true));

        await testCachedNode.update({ _id: true, isActive: true }, { data: { _id: '5', isActive: false } }).execute();

        const updatedNodes = await testCachedNode
            .query(
                edgesSelector(
                    {
                        code: true,
                        isActive: true,
                    },
                    { filter: { isActive: { _eq: false } } },
                ),
            )
            .execute();
        assert.equal(updatedNodes.edges.length, 1);
        assert.equal(updatedNodes.edges[0].node.code, 'code5');
    });

    after(() => {});
});
