import { AnyRecord } from '@sage/xtrem-async-helper';
import { assert, expect } from 'chai';
import { fixtures, testAggregationApplication } from '..';
import {
    aggDocumentData,
    aggDocumentExtendedLineData,
    aggDocumentLineData,
    aggDocumentReverseRefData,
    itemData,
    stockData,
} from '../fixtures/data';
import { GraphQlHelper, graphqlSetup, initTables, restoreTables } from '../fixtures/index';
import { TestAggDocument, TestAggDocumentExtendedLine, TestAggDocumentLine, TestStock } from '../fixtures/nodes';

let graphqlHelper: GraphQlHelper;

/*
describe('Aggregates - groupBy - graphQl', () => {
    before(() => {
        initTables([{nodeConstructor: TestAggDocument, data: documentData}, {nodeConstructor: TestAggDocumentLine, data: documentLineData}]);
    });
    it('single-level groupBy', () => {
        const result = graphqlHelper.query<{ testAggDocumentLine: TestAggDocumentLine }>(
            `{
                testAggDocumentLine {
                    queryAggregate {
                        edges {
                            node {
                                group {
                                    lineNumber
                                    amount5
                                }
                            }
                        }
                    }
                }
            }`,
        ) as any;
        assert.deepEqual(result, {
            testAggDocumentLine: {
                aggregate: {
                    edges: [
                        { node: { group: { lineNumber: 1, amount5: '5.65' } } },
                        { node: { group: { lineNumber: 1, amount5: '10.36' } } },
                        { node: { group: { lineNumber: 2, amount5: '2.65' } } },
                        { node: { group: { lineNumber: 3, amount5: '0.25' } } },
                        { node: { group: { lineNumber: 3, amount5: '3' } } },
                        { node: { group: { lineNumber: 4, amount5: '0.65' } } },
                    ],
                },
            },
        });
    });
}
*/

describe('Aggregates - groupBy - graphQl', () => {
    before(async () => {
        graphqlHelper = await graphqlSetup({ application: await testAggregationApplication.application });
        await initTables([
            { nodeConstructor: fixtures.nodes.TestAggDocument, data: aggDocumentData },
            { nodeConstructor: fixtures.nodes.TestAggDocumentLine, data: aggDocumentLineData },
            { nodeConstructor: fixtures.nodes.TestAggDocumentExtendedLine, data: aggDocumentExtendedLineData },
            { nodeConstructor: fixtures.nodes.TestAggDocumentReverseRef, data: aggDocumentReverseRefData },
            { nodeConstructor: fixtures.nodes.TestItem, data: itemData },
            { nodeConstructor: fixtures.nodes.TestStock, data: stockData },
        ]);
    });
    after(() => restoreTables());
    it('single-level groupBy', async () => {
        const result = (await graphqlHelper.query<{ testAggDocumentLine: TestAggDocumentLine }>(
            `{
                testAggDocumentLine {
                    queryAggregate {
                        edges {
                            node {
                                group {
                                    lineNumber
                                    amount5
                                }
                            }
                        }
                        totalCount
                    }
                }
            }`,
        )) as any;
        assert.deepEqual(result, {
            testAggDocumentLine: {
                queryAggregate: {
                    edges: [
                        { node: { group: { lineNumber: 1, amount5: '5.65' } } },
                        { node: { group: { lineNumber: 1, amount5: '10.36' } } },
                        { node: { group: { lineNumber: 2, amount5: '2.65' } } },
                        { node: { group: { lineNumber: 3, amount5: '0.25' } } },
                        { node: { group: { lineNumber: 3, amount5: '3' } } },
                        { node: { group: { lineNumber: 4, amount5: '0.65' } } },
                    ],
                    totalCount: 6,
                },
            },
        });
    });
    it('single-level groupBy - with filter', async () => {
        const result = (await graphqlHelper.query<{ testAggDocumentLine: TestAggDocumentLine }>(
            `{
                testAggDocumentLine {
                    queryAggregate(filter:"{amount5:{_gt:2}}") {
                        edges {
                            node {
                                group {
                                    lineNumber
                                    amount5
                                }
                            }
                        }
                        totalCount
                    }
                }
            }`,
        )) as any;
        assert.deepEqual(result, {
            testAggDocumentLine: {
                queryAggregate: {
                    edges: [
                        { node: { group: { lineNumber: 1, amount5: '5.65' } } },
                        { node: { group: { lineNumber: 1, amount5: '10.36' } } },
                        { node: { group: { lineNumber: 2, amount5: '2.65' } } },
                        { node: { group: { lineNumber: 3, amount5: '3' } } },
                    ],
                    totalCount: 4,
                },
            },
        });
    });
    it('single-level groupBy - with first', async () => {
        const result = (await graphqlHelper.query<{ testAggDocumentLine: TestAggDocumentLine }>(
            `{
                testAggDocumentLine {
                    queryAggregate(first:2) {
                        edges {
                            node {
                                group {
                                    lineNumber
                                    amount5
                                }
                            }
                        }
                        totalCount
                    }
                }
            }`,
        )) as any;
        assert.deepEqual(result, {
            testAggDocumentLine: {
                queryAggregate: {
                    edges: [
                        { node: { group: { lineNumber: 1, amount5: '5.65' } } },
                        { node: { group: { lineNumber: 1, amount5: '10.36' } } },
                    ],
                    totalCount: 6,
                },
            },
        });
    });
    it('single-level groupBy - with orderBy', async () => {
        const result = (await graphqlHelper.query<{ testAggDocumentLine: TestAggDocumentLine }>(
            `{
                testAggDocumentLine {
                    queryAggregate(orderBy:"{amount5:-1}") {
                        edges {
                            node {
                                group {
                                    lineNumber
                                    amount5
                                }
                            }
                        }
                    }
                }
            }`,
        )) as any;
        // Note : the orderBy used in the sql command should be "order by t0.AMOUNT5_0 desc, t0.LINENUMBER_0 asc"
        // {amount5:-1} will be completed from the grouped columns
        assert.deepEqual(result, {
            testAggDocumentLine: {
                queryAggregate: {
                    edges: [
                        { node: { group: { lineNumber: 1, amount5: '10.36' } } },
                        { node: { group: { lineNumber: 1, amount5: '5.65' } } },
                        { node: { group: { lineNumber: 3, amount5: '3' } } },
                        { node: { group: { lineNumber: 2, amount5: '2.65' } } },
                        { node: { group: { lineNumber: 4, amount5: '0.65' } } },
                        { node: { group: { lineNumber: 3, amount5: '0.25' } } },
                    ],
                },
            },
        });
    });
    it('single-level groupBy - with orderBy a date', async () => {
        const result = (await graphqlHelper.query<{ testAggDocumentLine: TestAggDocumentLine }>(
            `{
                testAggDocumentLine {
                    queryAggregate(orderBy:"{date:-1}") {
                        edges {
                            node {
                                group {
                                    date(by: month)
                                }
                                values {
                                    lineNumber {
                                      distinctCount
                                    }
                                  }
                            }
                        }
                    }
                }
            }`,
        )) as any;
        assert.deepEqual(result, {
            testAggDocumentLine: {
                queryAggregate: {
                    edges: [
                        { node: { group: { date: '2004-02-01' }, values: { lineNumber: { distinctCount: 1 } } } },
                        { node: { group: { date: '2003-08-01' }, values: { lineNumber: { distinctCount: 1 } } } },
                        { node: { group: { date: '2003-01-01' }, values: { lineNumber: { distinctCount: 1 } } } },
                        { node: { group: { date: '2000-02-01' }, values: { lineNumber: { distinctCount: 4 } } } },
                    ],
                },
            },
        });
    });
    it('single-level groupBy - with orderBy a value', async () => {
        const result = (await graphqlHelper.query<{ testAggDocumentLine: TestAggDocumentLine }>(
            `{
                testAggDocumentLine {
                    queryAggregate(orderBy:"{lineNumber:-1}") {
                        edges {
                            node {
                                group {
                                    date(by: month)
                                }
                                values {
                                    lineNumber {
                                      distinctCount
                                    }
                                  }
                            }
                        }
                    }
                }
            }`,
        )) as any;
        assert.deepEqual(result, {
            testAggDocumentLine: {
                queryAggregate: {
                    edges: [
                        { node: { group: { date: '2000-02-01' }, values: { lineNumber: { distinctCount: 4 } } } },
                        { node: { group: { date: '2003-01-01' }, values: { lineNumber: { distinctCount: 1 } } } },
                        { node: { group: { date: '2003-08-01' }, values: { lineNumber: { distinctCount: 1 } } } },
                        { node: { group: { date: '2004-02-01' }, values: { lineNumber: { distinctCount: 1 } } } },
                    ],
                },
            },
        });
    });
    it('single-level groupBy - with orderBy a column not in the GROUP BY clause', async () => {
        try {
            await graphqlHelper.query<{ testAggDocumentLine: TestAggDocumentLine }>(
                `{
                testAggDocumentLine {
                    queryAggregate(orderBy:"{document:-1}") {
                        edges {
                            node {
                                group {
                                    date(by: month)
                                }
                                values {
                                    lineNumber {
                                      distinctCount
                                    }
                                  }
                            }
                        }
                    }
                }
            }`,
            );
        } catch (err) {
            expect(err.message).to.satisfy((m: string) =>
                m.startsWith('An error has occurred. Please contact your administrator.'),
            );
        }
    });
    it('single-level groupBy with null', async () => {
        // the purpose of hasNull is to know if the sum include some null sublot
        // because distinctCount is the count of non null values and then you cannot know
        // if you have null except if distinctCount is 0
        const result = (await graphqlHelper.query<{ testStock: TestStock }>(
            `{
                        testStock {
                            queryAggregate {
                                edges {
                                    node {
                                      group {
                                        product {
                                          code(by:value)
                                        }
                                      }
                                      values {
                                        quantityInStockUnit {
                                          sum
                                        }
                                        sublot {
                                          min,
                                          distinctCount,
                                          hasNull
                                        }
                                      }
                                    }
                                }
                                totalCount
                            }
                        }
                    }`,
        )) as any;
        assert.deepEqual(result, {
            testStock: {
                queryAggregate: {
                    edges: [
                        {
                            node: {
                                group: {
                                    product: { code: 'RAW401' },
                                },
                                values: {
                                    quantityInStockUnit: { sum: '11850' },
                                    sublot: { min: null, distinctCount: 0, hasNull: true },
                                },
                            },
                        },
                        {
                            node: {
                                group: {
                                    product: { code: 'RAW404' },
                                },
                                values: {
                                    quantityInStockUnit: { sum: '4.942' },
                                    sublot: { min: 1, distinctCount: 1, hasNull: false },
                                },
                            },
                        },
                        {
                            node: {
                                group: {
                                    product: { code: 'RAW407' },
                                },
                                values: {
                                    quantityInStockUnit: { sum: '25' },
                                    sublot: { min: 1, distinctCount: 1, hasNull: true },
                                },
                            },
                        },
                        {
                            node: {
                                group: {
                                    product: { code: 'RAW411' },
                                },
                                values: {
                                    quantityInStockUnit: { sum: '120' },
                                    sublot: { min: 0, distinctCount: 2, hasNull: true },
                                },
                            },
                        },
                    ],
                    totalCount: 4,
                },
            },
        });
    });

    it('multi-level groupBy', async () => {
        const result = (await graphqlHelper.query<{ testAggDocumentLine: TestAggDocumentLine }>(
            `{
                testAggDocumentLine {
                    queryAggregate {
                        edges {
                            node {
                                group {
                                    lineNumber
                                    document {
                                        totalAmount1
                                        totalAmount5
                                    }
                                }
                            }
                            cursor
                        }
                        totalCount
                        pageInfo {
                            endCursor
                            hasNextPage
                            startCursor
                            hasPreviousPage
                        }
                    }
                }
            }`,
        )) as any;
        assert.deepEqual(result, {
            testAggDocumentLine: {
                queryAggregate: {
                    edges: [
                        {
                            node: {
                                group: {
                                    lineNumber: 1,
                                    document: {
                                        totalAmount1: '35.5',
                                        totalAmount5: '9.2',
                                    },
                                },
                            },
                            cursor: '[1,"35.5","9.2"]#05',
                        },
                        {
                            node: {
                                group: {
                                    lineNumber: 1,
                                    document: {
                                        totalAmount1: '45',
                                        totalAmount5: '16.01',
                                    },
                                },
                            },
                            cursor: '[1,"45","16.01"]#94',
                        },
                        {
                            node: {
                                group: {
                                    lineNumber: 2,
                                    document: {
                                        totalAmount1: '35.5',
                                        totalAmount5: '9.2',
                                    },
                                },
                            },
                            cursor: '[2,"35.5","9.2"]#16',
                        },
                        {
                            node: {
                                group: {
                                    lineNumber: 2,
                                    document: {
                                        totalAmount1: '45',
                                        totalAmount5: '16.01',
                                    },
                                },
                            },
                            cursor: '[2,"45","16.01"]#47',
                        },
                        {
                            node: {
                                group: {
                                    lineNumber: 3,
                                    document: {
                                        totalAmount1: '35.5',
                                        totalAmount5: '9.2',
                                    },
                                },
                            },
                            cursor: '[3,"35.5","9.2"]#76',
                        },
                        {
                            node: {
                                group: {
                                    lineNumber: 3,
                                    document: {
                                        totalAmount1: '45',
                                        totalAmount5: '16.01',
                                    },
                                },
                            },
                            cursor: '[3,"45","16.01"]#07',
                        },
                        {
                            node: {
                                group: {
                                    lineNumber: 4,
                                    document: {
                                        totalAmount1: '35.5',
                                        totalAmount5: '9.2',
                                    },
                                },
                            },
                            cursor: '[4,"35.5","9.2"]#70',
                        },
                    ],
                    totalCount: 7,
                    pageInfo: {
                        endCursor: '[4,"35.5","9.2"]#70',
                        hasNextPage: false,
                        startCursor: '[1,"35.5","9.2"]#05',
                        hasPreviousPage: false,
                    },
                },
            },
        });
    });
    it('multi-level groupBy on reference id', async () => {
        const result = (await graphqlHelper.query<{ testAggDocumentLine: TestAggDocumentLine }>(
            `{
                testAggDocumentLine {
                    queryAggregate {
                        edges {
                            node {
                                group {
                                    lineNumber
                                    document {
                                        _id
                                    }
                                }
                            }
                        }
                    }
                }
            }`,
        )) as any;
        assert.deepEqual(result, {
            testAggDocumentLine: {
                queryAggregate: {
                    edges: [
                        { node: { group: { lineNumber: 1, document: { _id: '1' } } } },
                        { node: { group: { lineNumber: 1, document: { _id: '2' } } } },
                        { node: { group: { lineNumber: 2, document: { _id: '1' } } } },
                        { node: { group: { lineNumber: 2, document: { _id: '2' } } } },
                        { node: { group: { lineNumber: 3, document: { _id: '1' } } } },
                        { node: { group: { lineNumber: 3, document: { _id: '2' } } } },
                        { node: { group: { lineNumber: 4, document: { _id: '1' } } } },
                    ],
                },
            },
        });
    });
    it('groupBy on a reference field property', async () => {
        const result = (await graphqlHelper.query<{ testAggDocument: TestAggDocument }>(
            `{
                testAggDocument {
                    queryAggregate {
                        edges {
                            node {
                                group {
                                    code
                                    documentReverseRef {
                                        code

                                    }
                                }
                            }
                        }
                    }
                }
            }`,
        )) as any;
        assert.deepEqual(result, {
            testAggDocument: {
                queryAggregate: {
                    edges: [
                        { node: { group: { code: 'DOC1', documentReverseRef: { code: 'DOC1ReverseRef' } } } },
                        { node: { group: { code: 'DOC2', documentReverseRef: { code: 'DOC2ReverseRef' } } } },
                    ],
                },
            },
        });
    });
    it('multi-level groupBy - with filter', async () => {
        const result = (await graphqlHelper.query<{ testAggDocumentLine: TestAggDocumentLine }>(
            `{
                testAggDocumentLine {
                    queryAggregate(filter:"{document:{totalAmount5:{_gt:10}}}") {
                        edges {
                            node {
                                group {
                                    lineNumber
                                    document {
                                        totalAmount5
                                    }
                                }
                            }
                        }
                    }
                }
            }`,
        )) as any;
        assert.deepEqual(result, {
            testAggDocumentLine: {
                queryAggregate: {
                    edges: [
                        { node: { group: { lineNumber: 1, document: { totalAmount5: '16.01' } } } },
                        { node: { group: { lineNumber: 2, document: { totalAmount5: '16.01' } } } },
                        { node: { group: { lineNumber: 3, document: { totalAmount5: '16.01' } } } },
                    ],
                },
            },
        });
    });

    it('multi-level groupBy - with orderBy', async () => {
        const result = (await graphqlHelper.query<{ testAggDocumentLine: TestAggDocumentLine }>(
            `{
                testAggDocumentLine {
                    queryAggregate(orderBy:"{document:{totalAmount5:-1}, lineNumber:1}") {
                        edges {
                            node {
                                group {
                                    lineNumber
                                    document {
                                        totalAmount5
                                    }
                                }
                            }
                        }
                    }
                }
            }`,
        )) as any;
        assert.deepEqual(result, {
            testAggDocumentLine: {
                queryAggregate: {
                    edges: [
                        { node: { group: { lineNumber: 1, document: { totalAmount5: '16.01' } } } },
                        { node: { group: { lineNumber: 2, document: { totalAmount5: '16.01' } } } },
                        { node: { group: { lineNumber: 3, document: { totalAmount5: '16.01' } } } },
                        { node: { group: { lineNumber: 1, document: { totalAmount5: '9.2' } } } },
                        { node: { group: { lineNumber: 2, document: { totalAmount5: '9.2' } } } },
                        { node: { group: { lineNumber: 3, document: { totalAmount5: '9.2' } } } },
                        { node: { group: { lineNumber: 4, document: { totalAmount5: '9.2' } } } },
                    ],
                },
            },
        });
    });
    it('single-level groupBy - get pageInfos', async () => {
        const result = (await graphqlHelper.query<{ testAggDocumentLine: TestAggDocumentLine }>(
            `{
                testAggDocumentLine {
                    queryAggregate(first:2) {
                        edges {
                            node {
                                group {
                                    lineNumber
                                    amount5
                                }
                            }
                        }
                        pageInfo {
                            endCursor
                            hasNextPage
                            startCursor
                            hasPreviousPage
                        }
                        totalCount
                    }
                }
            }`,
        )) as any;
        assert.deepEqual(result, {
            testAggDocumentLine: {
                queryAggregate: {
                    edges: [
                        { node: { group: { lineNumber: 1, amount5: '5.65' } } },
                        { node: { group: { lineNumber: 1, amount5: '10.36' } } },
                    ],
                    pageInfo: {
                        hasNextPage: true,
                        hasPreviousPage: false,
                        startCursor: '[1,"5.65"]#76',
                        endCursor: '[1,"10.36"]#00',
                    },
                    totalCount: 6,
                },
            },
        });
    });
    it('single-level groupBy - query next page', async () => {
        const result = (await graphqlHelper.query<{ testAggDocumentLine: TestAggDocumentLine }>(
            `{
                testAggDocumentLine {
                    queryAggregate(first:2, after:"[1,'10.36']#00") {
                        edges {
                            node {
                                group {
                                    lineNumber
                                    amount5
                                }
                            }
                        }
                        pageInfo {
                            endCursor
                            hasNextPage
                            startCursor
                            hasPreviousPage
                        }
                        totalCount
                    }
                }
            }`,
        )) as any;
        assert.deepEqual(result, {
            testAggDocumentLine: {
                queryAggregate: {
                    edges: [
                        { node: { group: { lineNumber: 2, amount5: '2.65' } } },
                        { node: { group: { lineNumber: 3, amount5: '0.25' } } },
                    ],
                    pageInfo: {
                        hasNextPage: true,
                        hasPreviousPage: true,
                        startCursor: '[2,"2.65"]#53',
                        endCursor: '[3,"0.25"]#38',
                    },
                    totalCount: 6,
                },
            },
        });
    });
    it('single-level groupBy - query previous page', async () => {
        const result = (await graphqlHelper.query<{ testAggDocumentLine: TestAggDocumentLine }>(
            `{
                testAggDocumentLine {
                    queryAggregate(last:2, before:"[2,'2.65']#53") {
                        edges {
                            node {
                                group {
                                    lineNumber
                                    amount5
                                }
                            }
                        }
                        pageInfo {
                            endCursor
                            hasNextPage
                            startCursor
                            hasPreviousPage
                        }
                        totalCount
                    }
                }
            }`,
        )) as any;
        assert.deepEqual(result, {
            testAggDocumentLine: {
                queryAggregate: {
                    edges: [
                        { node: { group: { lineNumber: 1, amount5: '10.36' } } },
                        { node: { group: { lineNumber: 1, amount5: '5.65' } } },
                    ],
                    pageInfo: {
                        hasNextPage: true,
                        hasPreviousPage: false,
                        startCursor: '[1,"10.36"]#00',
                        endCursor: '[1,"5.65"]#76',
                    },
                    totalCount: 6,
                },
            },
        });
    });
    it("groupBy('year')", async () => {
        const result = (await graphqlHelper.query<{ testAggDocumentLine: TestAggDocumentLine }>(
            `{
                testAggDocumentLine {
                    queryAggregate {
                        edges {
                            node {
                                group {
                                    date(by:year)
                                }
                            }
                        }
                    }
                }
            }`,
        )) as any;
        assert.deepEqual(result, {
            testAggDocumentLine: {
                queryAggregate: {
                    edges: [
                        { node: { group: { date: '2000-01-01' } } },
                        { node: { group: { date: '2003-01-01' } } },
                        { node: { group: { date: '2004-01-01' } } },
                    ],
                },
            },
        });
    });
    it("groupBy('month')", async () => {
        const result = (await graphqlHelper.query<{ testAggDocumentLine: TestAggDocumentLine }>(
            `{
                testAggDocumentLine {
                    queryAggregate {
                        edges {
                            node {
                                group {
                                    date(by:month)
                                }
                            }
                        }
                    }
                }
            }`,
        )) as any;
        assert.deepEqual(result, {
            testAggDocumentLine: {
                queryAggregate: {
                    edges: [
                        { node: { group: { date: '2000-02-01' } } },
                        { node: { group: { date: '2003-01-01' } } },
                        { node: { group: { date: '2003-08-01' } } },
                        { node: { group: { date: '2004-02-01' } } },
                    ],
                },
            },
        });
    });
    it("groupBy('day')", async () => {
        const result = (await graphqlHelper.query<{ testAggDocumentLine: TestAggDocumentLine }>(
            `{
                testAggDocumentLine {
                    queryAggregate {
                        edges {
                            node {
                                group {
                                    date(by:day)
                                }
                            }
                        }
                    }
                }
            }`,
        )) as any;
        assert.deepEqual(result, {
            testAggDocumentLine: {
                queryAggregate: {
                    edges: [
                        { node: { group: { date: '2000-02-07' } } },
                        { node: { group: { date: '2000-02-15' } } },
                        { node: { group: { date: '2003-01-08' } } },
                        { node: { group: { date: '2003-08-24' } } },
                        { node: { group: { date: '2004-02-18' } } },
                    ],
                },
            },
        });
    });

    it('can queryAggregate and readAggregate in the same query', async () => {
        const result = await graphqlHelper.query<AnyRecord>(
            `{
            testAggDocumentLine {
                queryAggregate(first: 2) {
                    edges {
                        node {
                            group {
                                date(by:month)
                            }
                        }
                    }
                }
                readAggregate {
                    amount1 {sum, avg}
                }
            }
        }`,
        );
        assert.deepEqual(result, {
            testAggDocumentLine: {
                queryAggregate: {
                    edges: [
                        {
                            node: {
                                group: {
                                    date: '2000-02-01',
                                },
                            },
                        },
                        {
                            node: {
                                group: {
                                    date: '2003-01-01',
                                },
                            },
                        },
                    ],
                },
                readAggregate: {
                    amount1: {
                        avg: '11.5',
                        sum: '80.5',
                    },
                },
            },
        });
    });

    it('group + values', async () => {
        const result = (await graphqlHelper.query<{ testAggDocumentLine: TestAggDocumentLine }>(
            `{
                testAggDocumentLine {
                    queryAggregate {
                        edges {
                            node {
                                group {
                                    date(by:month)
                                }
                                values {
                                    amount1 {sum, avg}
                                }
                            }
                        }
                    }
                }
            }`,
        )) as any;
        assert.deepEqual(result, {
            testAggDocumentLine: {
                queryAggregate: {
                    edges: [
                        {
                            node: {
                                group: {
                                    date: '2000-02-01',
                                },
                                values: {
                                    amount1: {
                                        sum: '35.5',
                                        avg: '8.875',
                                    },
                                },
                            },
                        },
                        {
                            node: {
                                group: {
                                    date: '2003-01-01',
                                },
                                values: {
                                    amount1: {
                                        sum: '6.5',
                                        avg: '6.5',
                                    },
                                },
                            },
                        },
                        {
                            node: {
                                group: {
                                    date: '2003-08-01',
                                },
                                values: {
                                    amount1: {
                                        sum: '16.5',
                                        avg: '16.5',
                                    },
                                },
                            },
                        },
                        {
                            node: {
                                group: {
                                    date: '2004-02-01',
                                },
                                values: {
                                    amount1: {
                                        sum: '22',
                                        avg: '22',
                                    },
                                },
                            },
                        },
                    ],
                },
            },
        });
    });
    it('group + values sub node', async () => {
        const result = (await graphqlHelper.query<{ testAggDocumentExtendedLine: TestAggDocumentExtendedLine }>(
            `{
                testAggDocumentExtendedLine {
                    queryAggregate {
                        edges {
                            node {
                                group {
                                    date(by:month)
                                }
                                values {
                                    amount2 {sum, avg}
                                }
                            }
                        }
                    }
                }
            }`,
        )) as any;
        assert.deepEqual(result, {
            testAggDocumentExtendedLine: {
                queryAggregate: {
                    edges: [
                        {
                            node: {
                                group: {
                                    date: '2000-02-01',
                                },
                                values: {
                                    amount2: {
                                        sum: '20.13',
                                        avg: '5.0325',
                                    },
                                },
                            },
                        },
                        {
                            node: {
                                group: {
                                    date: '2003-01-01',
                                },
                                values: {
                                    amount2: {
                                        sum: '8.2',
                                        avg: '8.2',
                                    },
                                },
                            },
                        },
                        {
                            node: {
                                group: {
                                    date: '2003-08-01',
                                },
                                values: {
                                    amount2: {
                                        sum: '7.6',
                                        avg: '7.6',
                                    },
                                },
                            },
                        },
                        {
                            node: {
                                group: {
                                    date: '2004-02-01',
                                },
                                values: {
                                    amount2: {
                                        sum: '4.9',
                                        avg: '4.9',
                                    },
                                },
                            },
                        },
                    ],
                },
            },
        });
    });
    it('cursors', async () => {
        const result = (await graphqlHelper.query<{ testAggDocumentLine: TestAggDocumentLine }>(
            `{
                testAggDocumentLine {
                    queryAggregate {
                        edges {
                            node {
                                group {
                                    date(by:month)
                                }
                                values {
                                    amount1 {sum, avg}
                                }
                            }
                            cursor
                        }
                        pageInfo {
                            hasNextPage
                            endCursor
                            hasPreviousPage
                            startCursor
                        }
                    }
                }
            }`,
        )) as any;
        assert.deepEqual(result, {
            testAggDocumentLine: {
                queryAggregate: {
                    edges: [
                        {
                            node: {
                                group: {
                                    date: '2000-02-01',
                                },
                                values: {
                                    amount1: {
                                        sum: '35.5',
                                        avg: '8.875',
                                    },
                                },
                            },
                            cursor: '["2000-02-01"]#17',
                        },
                        {
                            node: {
                                group: {
                                    date: '2003-01-01',
                                },
                                values: {
                                    amount1: {
                                        sum: '6.5',
                                        avg: '6.5',
                                    },
                                },
                            },
                            cursor: '["2003-01-01"]#82',
                        },
                        {
                            node: {
                                group: {
                                    date: '2003-08-01',
                                },
                                values: {
                                    amount1: {
                                        sum: '16.5',
                                        avg: '16.5',
                                    },
                                },
                            },
                            cursor: '["2003-08-01"]#30',
                        },
                        {
                            node: {
                                group: {
                                    date: '2004-02-01',
                                },
                                values: {
                                    amount1: {
                                        sum: '22',
                                        avg: '22',
                                    },
                                },
                            },
                            cursor: '["2004-02-01"]#45',
                        },
                    ],
                    pageInfo: {
                        hasNextPage: false,
                        endCursor: '["2004-02-01"]#45',
                        hasPreviousPage: false,
                        startCursor: '["2000-02-01"]#17',
                    },
                },
            },
        });
    });

    it('can query documents with nested lines aggregate', async () => {
        const result = (await graphqlHelper.query<{ testAggDocument: TestAggDocument }>(
            `{
                testAggDocument {
                    query {
                        edges {
                            node {
                                code
                                lines {
                                    readAggregate {
                                        amount1 { min, max, sum }
                                    }
                                    queryAggregate {
                                        edges {
                                            node {
                                                group {
                                                    date
                                                }
                                                values {
                                                    amount1 {min, max, sum}
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }`,
        )) as any;
        assert.deepEqual(result, {
            testAggDocument: {
                query: {
                    edges: [
                        {
                            node: {
                                code: 'DOC1',
                                lines: {
                                    readAggregate: {
                                        amount1: {
                                            min: '5',
                                            max: '15',
                                            sum: '35.5',
                                        },
                                    },
                                    queryAggregate: {
                                        edges: [
                                            {
                                                node: {
                                                    group: {
                                                        date: '2000-02-07',
                                                    },
                                                    values: {
                                                        amount1: {
                                                            min: '5',
                                                            max: '15',
                                                            sum: '20',
                                                        },
                                                    },
                                                },
                                            },
                                            {
                                                node: {
                                                    group: {
                                                        date: '2000-02-15',
                                                    },
                                                    values: {
                                                        amount1: {
                                                            min: '7',
                                                            max: '8.5',
                                                            sum: '15.5',
                                                        },
                                                    },
                                                },
                                            },
                                        ],
                                    },
                                },
                            },
                        },
                        {
                            node: {
                                code: 'DOC2',
                                lines: {
                                    readAggregate: {
                                        amount1: {
                                            min: '6.5',
                                            max: '22',
                                            sum: '45',
                                        },
                                    },
                                    queryAggregate: {
                                        edges: [
                                            {
                                                node: {
                                                    group: {
                                                        date: '2003-01-08',
                                                    },
                                                    values: {
                                                        amount1: {
                                                            min: '6.5',
                                                            max: '6.5',
                                                            sum: '6.5',
                                                        },
                                                    },
                                                },
                                            },
                                            {
                                                node: {
                                                    group: {
                                                        date: '2003-08-24',
                                                    },
                                                    values: {
                                                        amount1: {
                                                            min: '16.5',
                                                            max: '16.5',
                                                            sum: '16.5',
                                                        },
                                                    },
                                                },
                                            },
                                            {
                                                node: {
                                                    group: {
                                                        date: '2004-02-18',
                                                    },
                                                    values: {
                                                        amount1: {
                                                            min: '22',
                                                            max: '22',
                                                            sum: '22',
                                                        },
                                                    },
                                                },
                                            },
                                        ],
                                    },
                                },
                            },
                        },
                    ],
                },
            },
        });
    });
});
