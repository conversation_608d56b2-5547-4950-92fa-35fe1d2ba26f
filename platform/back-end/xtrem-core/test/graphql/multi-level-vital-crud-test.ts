import { Dict } from '@sage/xtrem-shared';
import { assert } from 'chai';
import { GraphQLError } from 'graphql';
import { isEqual } from 'lodash';
import { fixtures, testMultiLevelVitalApplication, testMultiLevelVitalApplicationWithTwoChildrenSameType } from '..';
import { Application, assertIsRejectedWithDiagnoses } from '../../lib';
import { GraphQlHelper, graphqlSetup, initTables, restoreTables } from '../fixtures/index';

let graphqlHelper: GraphQlHelper;

const testVitalReferenceParentTopLevel = [
    {
        _id: 1,
        code: 'top 1',
        selfRef: 1,
    },
    {
        _id: 2,
        code: 'top 2',
        selfRef: 2,
    },
];

const testVitalReferenceParentMidLevel = [
    {
        _id: 1,
        parentTopLevel: 1,
        code: 'mid 1',
        selfRef: 1,
    },
    {
        _id: 2,
        parentTopLevel: 2,
        code: 'mid 2',
        selfRef: 2,
    },
];

const testVitalReferenceParentLowLevel = [
    {
        _id: 1,
        parentMidLevel: 1,
        code: 'low 1',
        selfRef: 1,
    },
    {
        _id: 2,
        parentMidLevel: 2,
        code: 'low 2',
        selfRef: 2,
    },
];

let application: Application;

describe('Create or update of a vital parent from a vital child', () => {
    before(async () => {
        application = await testMultiLevelVitalApplication.application;
        graphqlHelper = await graphqlSetup({ application });
        await initTables([
            {
                nodeConstructor: fixtures.nodes.TestVitalReferenceParentTopLevel,
                data: testVitalReferenceParentTopLevel,
            },
            {
                nodeConstructor: fixtures.nodes.TestVitalReferenceParentMidLevel,
                data: testVitalReferenceParentMidLevel,
            },
            {
                nodeConstructor: fixtures.nodes.TestVitalReferenceParentLowLevel,
                data: testVitalReferenceParentLowLevel,
            },
        ]);
    });

    after(() => restoreTables());

    it('Create whole new hierarchy', async () => {
        const createMutation = `{
                    testVitalReferenceParentLowLevel {
                        create(
                            data: {
                                code: "low 3"
                                parentMidLevel: {
                                    code: "mid 3"
                                    parentTopLevel:{
                                        code: "top 3"
                                    }
                                }
                            }
                        ) {
                            code
                            parentMidLevel{
                                code
                                parentTopLevel{
                                    code
                                    selfRef{
                                        code
                                    }
                                }
                                selfRef{
                                    code
                                    parentTopLevel{
                                        code
                                    }
                                }
                            }
                            selfRef{
                                code
                                parentMidLevel{
                                    code
                                    parentTopLevel{
                                        code
                                    }
                                }
                            }
                        }

                }
            }`;

        const expectedResultsCreate = {
            code: 'low 3',
            parentMidLevel: {
                code: 'mid 3',
                parentTopLevel: {
                    code: 'top 3',
                    selfRef: {
                        code: 'top 3',
                    },
                },
                selfRef: {
                    code: 'mid 3',
                    parentTopLevel: {
                        code: 'top 3',
                    },
                },
            },
            selfRef: {
                code: 'low 3',
                parentMidLevel: {
                    code: 'mid 3',
                    parentTopLevel: {
                        code: 'top 3',
                    },
                },
            },
        } as Dict<any>;

        const result = await graphqlHelper.mutation<{
            testVitalReferenceParentLowLevel: {
                create: {
                    code: string;
                    parentMidLevel: {
                        code: string;
                        parentTopLevel: {
                            code: string;
                            selfRef: {
                                code: string;
                            };
                        };
                        selfRef: {
                            code: string;
                            parentTopLevel: {
                                code: string;
                            };
                        };
                    };
                    selfRef: {
                        code: string;
                        ParentMidLevel: {
                            code: string;
                            parentTopLevel: {
                                code: string;
                            };
                        };
                    };
                };
            };
        }>(createMutation);

        assert.isTrue(isEqual(result.testVitalReferenceParentLowLevel.create, expectedResultsCreate));
    });

    it('Update whole hierarchy', async () => {
        const updateMutation = `{
            testVitalReferenceParentLowLevel {
                update(
                    data: {
                        _id: "2"
                        code: "new low 2"
                        parentMidLevel: {
                            _id: "2"
                            code: "new mid 2"
                            parentTopLevel:{
                                _id: "2"
                                code: "new top 2"
                            }
                        }
                    }
                ) {
                    code
                    parentMidLevel{
                        code
                        parentTopLevel{
                            code
                        }
                    }
                }

            }
        }`;
        const expectedResultsUpdate = {
            code: 'new low 2',
            parentMidLevel: {
                code: 'new mid 2',
                parentTopLevel: {
                    code: 'new top 2',
                },
            },
        } as Dict<any>;
        const result = await graphqlHelper.mutation<{
            testVitalReferenceParentLowLevel: {
                update: {
                    code: string;
                    parentMidLevel: {
                        code: string;
                        parentTopLevel: {
                            code: string;
                        };
                    };
                };
            };
        }>(updateMutation);

        assert.isTrue(isEqual(result.testVitalReferenceParentLowLevel.update, expectedResultsUpdate));
    });

    it('Create new low and med with existing top', async () => {
        const createMutationWithExistingParent = `{
            testVitalReferenceParentLowLevel {
                create(
                    data: {
                        code: "low 4"
                        parentMidLevel: {
                            code: "mid 4"
                            parentTopLevel:{
                                _id: "2"
                                code: "new top 2"
                            }
                        }
                    }
                ) {
                    code
                    parentMidLevel{
                        code
                        parentTopLevel{
                            code
                        }
                    }
                }

        }
        }`;

        const expectedResultsCreateWithExistingParent = {
            code: 'low 4',
            parentMidLevel: {
                code: 'mid 4',
                parentTopLevel: {
                    code: 'new top 2',
                },
            },
        } as Dict<any>;

        const result = await graphqlHelper.mutation<{
            testVitalReferenceParentLowLevel: {
                create: {
                    code: string;
                    parent: {
                        code: string;
                        parent: {
                            code: string;
                        };
                    };
                };
            };
        }>(createMutationWithExistingParent);

        assert.isTrue(isEqual(result.testVitalReferenceParentLowLevel.create, expectedResultsCreateWithExistingParent));
    });

    it('Create whole new hierarchy with error', async () => {
        try {
            const createMutationWithError = `{
                    testVitalReferenceParentLowLevel {
                        create(
                            data: {
                                code: "low 2"
                                parentMidLevel: {
                                    code: "mid 2"
                                    parentTopLevel:{
                                        code: "top 2"
                                    }
                                }
                            }
                        ) {
                            code
                            parentMidLevel{
                                code
                                parentTopLevel{
                                    code
                                }
                            }
                        }

                    }
                }`;
            await graphqlHelper.mutation<{
                testVitalReferenceParentLowLevel: {
                    create: {
                        code: string;
                        parentMidLevel: {
                            code: string;
                            parentTopLevel: {
                                code: string;
                            };
                        };
                    };
                };
            }>(createMutationWithError);
        } catch (err) {
            assert.isTrue(
                isEqual(err.extensions, {
                    code: 'operation-error',
                    diagnoses: [
                        {
                            message: 'The operation failed because the record already exists.',
                            path: ['code'],
                            severity: 3,
                        },
                    ],
                }),
            );
            assert.isTrue(
                isEqual(err.locations, [
                    {
                        column: 9,
                        line: 3,
                    },
                ]),
            );
            assert.isTrue(isEqual(err.message, 'The operation failed because the record already exists.'));
            assert.isTrue(isEqual(err.path, ['xtremCore', 'testVitalReferenceParentLowLevel', 'create']));
        }
    });

    it('Create whole new hierarchy with validation error', async () => {
        try {
            const createMutationWithError = `{
                    testVitalReferenceParentLowLevel {
                        create(
                            data: {
                                code: "low 5"
                                parentMidLevel: {
                                    code: "bad code"
                                    parentTopLevel:{
                                        code: "bad code"
                                    }
                                }
                            }
                        ) {
                            code
                            parentMidLevel{
                                code
                                parentTopLevel{
                                    code
                                }
                            }
                        }

                    }
                }`;
            await graphqlHelper.mutation<{
                testVitalReferenceParentLowLevel: {
                    create: {
                        code: string;
                        parent: {
                            code: string;
                            parent: {
                                code: string;
                            };
                        };
                    };
                };
            }>(createMutationWithError);
        } catch (err) {
            assert.deepEqual((err as GraphQLError).toJSON(), {
                extensions: {
                    code: 'operation-error',
                    diagnoses: [
                        {
                            message: "[Creating vital parent] value must not be equal to 'bad code'",
                            path: ['parentMidLevel', 'parentTopLevel', 'code'],
                            severity: 3,
                        },
                        {
                            message: "[Creating vital parent] value must not be equal to 'bad code'",
                            path: ['parentMidLevel', 'code'],
                            severity: 3,
                        },
                    ],
                },
                locations: [
                    {
                        column: 25,
                        line: 3,
                    },
                ],
                message: 'The record was not created.',
                path: ['xtremCore', 'testVitalReferenceParentLowLevel', 'create'],
            });
        }
    });

    it('Create whole new hierarchy with validation error', async () => {
        try {
            const createMutationWithError = `{
                    testVitalReferenceParentLowLevel {
                        create(
                            data: {
                                code: "bad code"
                                parentMidLevel: {
                                    code: "bad code"
                                    parentTopLevel:{
                                        code: "bad code"
                                    }
                                }
                            }
                        ) {
                            code
                            parentMidLevel{
                                code
                                parentTopLevel{
                                    code
                                }
                            }
                        }

                    }
                }`;
            await graphqlHelper.mutation<{
                testVitalReferenceParentLowLevel: {
                    create: {
                        code: string;
                        parent: {
                            code: string;
                            parent: {
                                code: string;
                            };
                        };
                    };
                };
            }>(createMutationWithError);
        } catch (err) {
            assert.deepEqual((err as GraphQLError).toJSON(), {
                extensions: {
                    code: 'operation-error',
                    diagnoses: [
                        {
                            message: "[Creating vital parent] value must not be equal to 'bad code'",
                            path: ['parentMidLevel', 'parentTopLevel', 'code'],
                            severity: 3,
                        },
                        {
                            message: "[Creating vital parent] value must not be equal to 'bad code'",
                            path: ['parentMidLevel', 'code'],
                            severity: 3,
                        },
                        {
                            message: "[Creating vital parent] value must not be equal to 'bad code'",
                            path: ['code'],
                            severity: 3,
                        },
                    ],
                },
                locations: [
                    {
                        column: 25,
                        line: 3,
                    },
                ],
                message: 'The record was not created.',
                path: ['xtremCore', 'testVitalReferenceParentLowLevel', 'create'],
            });
        }
    });

    it('Create whole new hierarchy with validation error', async () => {
        try {
            const createMutationWithError = `{
                    testVitalReferenceParentLowLevel {
                        create(
                            data: {
                                code: "low 5"
                                parentMidLevel: {
                                    code: "mid 5"
                                    parentTopLevel:{
                                        code: "bad code"
                                    }
                                }
                            }
                        ) {
                            code
                            parentMidLevel{
                                code
                                parentTopLevel{
                                    code
                                }
                            }
                        }

                    }
                }`;
            await graphqlHelper.mutation<{
                testVitalReferenceParentLowLevel: {
                    create: {
                        code: string;
                        parent: {
                            code: string;
                            parent: {
                                code: string;
                            };
                        };
                    };
                };
            }>(createMutationWithError);
        } catch (err) {
            assert.deepEqual((err as GraphQLError).toJSON(), {
                extensions: {
                    code: 'operation-error',
                    diagnoses: [
                        {
                            message: "[Creating vital parent] value must not be equal to 'bad code'",
                            path: ['parentMidLevel', 'parentTopLevel', 'code'],
                            severity: 3,
                        },
                    ],
                },
                locations: [
                    {
                        column: 25,
                        line: 3,
                    },
                ],
                message: 'The record was not created.',
                path: ['xtremCore', 'testVitalReferenceParentLowLevel', 'create'],
            });
        }
    });

    it('Create whole new hierarchy with validation error', async () => {
        try {
            const createMutationWithError = `{
                    testVitalReferenceParentLowLevel {
                        create(
                            data: {
                                code: "bad code"
                                parentMidLevel: {
                                    code: "mid 2"
                                    parentTopLevel:{
                                        code: "bad code"
                                    }
                                }
                            }
                        ) {
                            code
                            parentMidLevel{
                                code
                                parentTopLevel{
                                    code
                                }
                            }
                        }

                    }
                }`;
            await graphqlHelper.mutation<{
                testVitalReferenceParentLowLevel: {
                    create: {
                        code: string;
                        parent: {
                            code: string;
                            parent: {
                                code: string;
                            };
                        };
                    };
                };
            }>(createMutationWithError);
        } catch (err) {
            assert.deepEqual((err as GraphQLError).toJSON(), {
                extensions: {
                    code: 'operation-error',
                    diagnoses: [
                        {
                            message: "[Creating vital parent] value must not be equal to 'bad code'",
                            path: ['parentMidLevel', 'parentTopLevel', 'code'],
                            severity: 3,
                        },
                        {
                            message: "[Creating vital parent] value must not be equal to 'bad code'",
                            path: ['code'],
                            severity: 3,
                        },
                    ],
                },
                locations: [
                    {
                        column: 25,
                        line: 3,
                    },
                ],
                message: 'The record was not created.',
                path: ['xtremCore', 'testVitalReferenceParentLowLevel', 'create'],
            });
        }
    });
});

describe('Graphql vital parent with 2 children same type test method', () => {
    before(async () => {
        application = await testMultiLevelVitalApplicationWithTwoChildrenSameType.application;
        graphqlHelper = await graphqlSetup({ application });
        const testVitalReferenceParentWithTwoChildrenSameTypeParent = [
            {
                _id: 1,
                code: 'top 1',
            },
            {
                _id: 2,
                code: 'top 2',
            },
        ];

        const testVitalReferenceParentWithTwoChildrenSameTypeChild = [
            {
                _id: 1,
                parent: 1,
                code: 'mid 1',
            },
        ];
        await initTables([
            {
                nodeConstructor: fixtures.nodes.TestVitalReferenceParentWithTwoChildrenSameTypeParent,
                data: testVitalReferenceParentWithTwoChildrenSameTypeParent,
            },
            {
                nodeConstructor: fixtures.nodes.TestVitalReferenceParentWithTwoChildrenSameTypeChild,
                data: testVitalReferenceParentWithTwoChildrenSameTypeChild,
            },
        ]);
    });

    after(() => restoreTables());

    it('Create whole new hierarchy', async () => {
        const createMutationWithError = `{
                testVitalReferenceParentWithTwoChildrenSameTypeChild {
                        create(
                            data: {
                                code: "child 2"
                                parent: {
                                    code: "parent 2"
                                }
                            }
                        ) {
                            code
                            parent{
                                code
                            }
                        }
                    }
                }`;
        await assertIsRejectedWithDiagnoses(
            graphqlHelper.mutation<{
                testVitalReferenceParentWithTwoChildrenSameTypeChild: {
                    create: {
                        code: string;
                        parent: {
                            code: string;
                        };
                    };
                };
            }>(createMutationWithError),
            {
                message: 'The record was not created.',
                diagnoses: [
                    {
                        message:
                            'Vital parent property parent must have a unique child property TestVitalReferenceParentWithTwoChildrenSameTypeChild',
                        severity: 4,
                        path: [],
                    },
                ],
            },
        );
    });
});
