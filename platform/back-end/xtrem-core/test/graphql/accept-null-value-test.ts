import { assert } from 'chai';
import * as fixtures from '../fixtures';
import { createApplicationWithApi, GraphQlHelper, graphqlSetup, initTables, restoreTables } from '../fixtures/index';

const { TestDefaultValue, TestDefaultValuesReference } = fixtures.nodes;

let graphqlHelper: GraphQlHelper;

describe('null reference test', () => {
    before(async () => {
        graphqlHelper = await graphqlSetup({
            application: await createApplicationWithApi({
                nodes: { TestDefaultValue, TestDefaultValuesReference },
            }),
        });
        await initTables([
            { nodeConstructor: TestDefaultValue, data: [] },
            { nodeConstructor: TestDefaultValuesReference, data: [{ code: 'REF1' }] },
        ]);
    });
    it('can save a reference using null value', async () => {
        const createResult = await graphqlHelper.mutation<{
            testDefaultValue: { create: { referenceNotNullDefault: { code: string } | null } };
        }>(
            `{ testDefaultValue { create(data: {
                booleanNullDefault: true,
                dateNullDefault: "2022-10-21",
                referenceNullDefault: "_id: 1",
                referenceNotNullDefault: null
            })  { referenceNotNullDefault {code} } } }`,
        );
        assert.deepEqual(createResult, {
            testDefaultValue: {
                create: {
                    referenceNotNullDefault: null,
                },
            },
        });
    });

    after(() => restoreTables());
});
