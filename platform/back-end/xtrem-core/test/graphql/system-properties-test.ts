import { assert } from 'chai';
import { createHash } from 'crypto';
import { testDatatypesApplication } from '..';
import { assertIsRejectedWithDiagnoses, datetime } from '../../index';
import * as fixtures from '../fixtures';
import {
    datatypesData,
    GraphQlHelper,
    graphqlPageNodes,
    graphqlSetup,
    initTables,
    QueryNode,
    restoreTables,
} from '../fixtures/index';

let graphqlHelper: GraphQlHelper;

let lastId = datatypesData.length - 1;

describe('system properties', () => {
    before(async () => {
        graphqlHelper = await graphqlSetup({ application: await testDatatypesApplication.application });
        await initTables([{ nodeConstructor: fixtures.nodes.TestDatatypes, data: datatypesData }]);
    });
    it('can read system properties', async () => {
        const result = await graphqlHelper.query<{
            testDatatypes: QueryNode<{
                id: number;
                _id: string;
                _createStamp: datetime;
                _updateStamp: datetime;
            }>;
        }>(
            `{ testDatatypes { query { edges { node {
                id
                _id
                _createStamp
                _updateStamp
                _etag
            } } } } }`,
        );
        const testDatatypes = graphqlPageNodes(result.testDatatypes.query);
        assert.equal(testDatatypes.length, datatypesData.length);
        testDatatypes.forEach((t: any, i: number) => {
            assert.isObject(t);
            assert.equal(t.id, i);
            assert.equal(t._id, i + 1);
            assert.match(t._createStamp, /\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.*/);
            assert.match(t._updateStamp, /\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.*/);
            assert.isNotNull(t._etag);
        });
    });
    it('cannot set stamps when creating object', async () => {
        lastId += 1;
        await assert.isRejected(
            graphqlHelper.mutation<{
                testDatatypes: { create: { id: number; _updateStamp: datetime } };
            }>(
                `{ testDatatypes { create(data: {
                    id: ${lastId},
                    _updateStamp: "${datetime.now()}"
                })  { id  } } }`,
            ),
            /Field "_updateStamp" is not defined by type "TestDatatypes_Input"/,
        );
    });

    it('can update without etag', async () => {
        const response = await graphqlHelper.mutation<{
            testDatatypes: { update: { id: number; _updateStamp: datetime; _etag: string } };
        }>(
            `{ testDatatypes { update(data: {
                    _id: "${datatypesData[0]._id}",
                    id: "999",
                })  { id _updateStamp  _etag} } }`,
        );

        assert.equal(
            createHash('sha256').update(response.testDatatypes.update._updateStamp.toString()).digest('base64'),
            response.testDatatypes.update._etag,
        );
    });

    it('can update with correct etag', async () => {
        const result = await graphqlHelper.query<{
            testDatatypes: QueryNode<{
                id: number;
                _id: string;
                _createStamp: datetime;
                _updateStamp: datetime;
                _etag: string;
            }>;
        }>(
            `{ testDatatypes { query { edges { node {
                id
                _id
                _createStamp
                _updateStamp
                _etag
            } } } } }`,
        );

        if (result.testDatatypes.query.edges) {
            const response = await graphqlHelper.mutation<{
                testDatatypes: { update: { id: number; _updateStamp: datetime; _etag: string } };
            }>(
                `{ testDatatypes { update(data: {
                    _id: "${datatypesData[0]._id}",
                    id: "999",
                    _etag: "${result.testDatatypes.query.edges[0].node._etag}",
                })  { id _updateStamp  _etag} } }`,
            );

            assert.equal(
                createHash('sha256').update(response.testDatatypes.update._updateStamp.toString()).digest('base64'),
                response.testDatatypes.update._etag,
            );
        }
    });

    it('update blocked without matching etag', async () => {
        await assertIsRejectedWithDiagnoses(
            graphqlHelper.mutation<{
                testDatatypes: { create: { id: number; _updateStamp: datetime } };
            }>(
                `{ testDatatypes { update(data: {
                    _id: "${datatypesData[0]._id}",
                    id: "999",
                    _etag: "X",
                })  { id _updateStamp  _etag} } }`,
            ),
            {
                message: 'The record was not updated.',
                diagnoses: [
                    {
                        message: 'Another user made changes to this record. Refresh the page to add your changes.',
                        severity: 4,
                        path: [],
                    },
                ],
            },
        );
    });

    after(() => restoreTables());
});
