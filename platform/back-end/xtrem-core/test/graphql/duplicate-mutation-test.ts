import { assert } from 'chai';
import { Context, Test } from '../../index';
import { GraphQlHelper, createApplicationWithApi, graphqlSetup, initTables } from '../fixtures/index';
import { TestComplexDuplicatedValue, complexDuplicatedValueData } from '../fixtures/nodes';

let graphqlHelper: GraphQlHelper;

describe('Mutation optional parameters', () => {
    before(async () => {
        graphqlHelper = await graphqlSetup({
            application: await createApplicationWithApi({ nodes: { TestComplexDuplicatedValue } }),
        });
        await initTables([{ nodeConstructor: TestComplexDuplicatedValue, data: complexDuplicatedValueData }]);
    });

    it('Cannot duplicate without required value', () =>
        Test.withContext(async (context: Context) => {
            await assert.isRejected(
                graphqlHelper.mutation(
                    `{
                    testComplexDuplicatedValue {
                        duplicate(_id: "1") {
                            _id
                            stringOldValue
                            stringNewValue
                        }
                    }
                }`,
                    {
                        context,
                    },
                ),
                'The record was not duplicated.',
            );
        }));

    it('Can duplicate with data for required value', () =>
        Test.withContext(async (context: Context) => {
            const result = await graphqlHelper.mutation(
                `{
                    testComplexDuplicatedValue {
                        duplicate(_id: "1", data: { stringNewValue: "New string" }) {
                            _id
                            stringOldValue
                            stringNewValue
                        }
                    }
                }`,
                {
                    context,
                },
            );

            assert.deepEqual(result, {
                testComplexDuplicatedValue: {
                    duplicate: {
                        _id: '3',
                        stringOldValue: 'Some string',
                        stringNewValue: 'New string',
                    },
                },
            });
        }));
});
