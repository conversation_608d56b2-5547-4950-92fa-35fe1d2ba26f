import { DateValue } from '@sage/xtrem-date-time';
import { assert } from 'chai';
import * as fixtures from '../fixtures';
import { createApplicationWithApi, GraphQlHelper, graphqlSetup, initTables, restoreTables } from '../fixtures/index';
import { TestMutationOnNullableProperty } from '../fixtures/nodes';

let graphqlHelper: GraphQlHelper;

const testMutationOnNullablePropertyValue = [
    {
        _id: 1,
        dateVal: DateValue.make(2020, 1, 15),
        intVal: 1,
    },
];

describe('Mutation on nullable property', () => {
    before(async () => {
        graphqlHelper = await graphqlSetup({
            application: await createApplicationWithApi({ nodes: { TestMutationOnNullableProperty } }),
        });
        await initTables([
            {
                nodeConstructor: fixtures.nodes.TestMutationOnNullableProperty,
                data: testMutationOnNullablePropertyValue,
            },
        ]);
    });
    it('preserves old value if property absent from payload', async () => {
        const result = await graphqlHelper.mutation<{
            testMutationOnNullableProperty: { update: { dateVal: string } };
        }>(
            `{ 
                testMutationOnNullableProperty { update( data: { _id: "1", intVal: "3" } ) {
                        dateVal
                        intVal
                } }
            }`,
        );
        assert.equal(result.testMutationOnNullableProperty.update.dateVal, '2020-01-15');
    });
    it('sets value to null if payload contains null', async () => {
        const result = await graphqlHelper.mutation<{
            testMutationOnNullableProperty: { update: { dateVal: string } };
        }>(
            `{ 
                testMutationOnNullableProperty { update( data: { _id: "1", intVal: "3", dateVal: null } ) {
                        dateVal
                        intVal
                } }
            }`,
        );
        assert.equal(result.testMutationOnNullableProperty.update.dateVal, null);
    });
    after(() => restoreTables());
});
