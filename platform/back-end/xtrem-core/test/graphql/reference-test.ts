import { edgesSelector, Graph, OrderBy, withoutEdges, WithoutSelectedEdges } from '@sage/xtrem-client';
import { assert } from 'chai';
import { testDocumentWithTransientApplication } from '..';
import * as fixtures from '../fixtures';
import { Graph<PERSON>pi, TestReferringInterface } from '../fixtures/client-nodes/index';
import { clientSetup, initTables, referredData, referringData, restoreTables } from '../fixtures/index';

let graph: Graph<GraphApi>;
let referringNode: GraphApi['@sage/xtrem-core/testReferring'];

const refData = referringData.map(referring => {
    const referred = referredData.find(r => r._id === referring.reference);
    if (!referred) throw new Error(`referred not found: ${referring.reference}`);
    return {
        _id: `${referring._id}`,
        reference: {
            _id: `${referring.reference}`,
            details: referred.details!,
        },
    };
});

const refArrayData = referringData.map(referring => {
    const referred = referredData.find(r => r._id === referring.reference);
    const referred1 = referredData.find(r => r._id === 1);
    if (!referred) throw new Error(`referred not found: ${referring.reference}`);
    return {
        _id: `${referring._id}`,
        referenceArray: [
            {
                _id: `${referred1!._id}`,
                details: referred1!.details!,
            },
            {
                _id: `${referring.reference}`,
                details: referred.details!,
            },
        ],
    };
});

function castIds(arr: any[]): any[] {
    function mapItem(item: any): any {
        if (item._id) item._id = `${item._id}`;
        if (item.reference && item.reference._id) {
            item.reference._id = `${item.reference._id}`;
        }
        return item;
    }
    arr.forEach(mapItem);
    return arr;
}

describe('graphql reference tests', () => {
    before(async () => {
        graph = await clientSetup({ application: await testDocumentWithTransientApplication.application });
        referringNode = graph.node('@sage/xtrem-core/testReferring');

        await initTables([
            { nodeConstructor: fixtures.nodes.TestReferred, data: referredData },
            { nodeConstructor: fixtures.nodes.TestReferring, data: referringData },
        ]);
    });
    it('query without reference', async () => {
        const result = await referringNode.query(edgesSelector({ _id: true })).execute();
        const referrings = withoutEdges(result);
        assert.equal(referrings.length, referringData.length);
        referrings.forEach((t: any, i: number) => {
            assert.isObject(t);
            assert.equal(t._id, referringData[i]._id);
            assert.equal(t.reference, null);
        });
    });
    it('query with reference', async () => {
        const result = await referringNode
            .query(
                edgesSelector({
                    _id: true,
                    reference: { _id: true, details: true },
                }),
            )
            .execute();
        const referrings = castIds(withoutEdges(result));
        assert.deepEqual(referrings, refData);
    });

    it('query with reference array', async () => {
        const result = await referringNode
            .query(
                edgesSelector({
                    _id: true,
                    referenceArray: { _id: true, details: true },
                }),
            )
            .execute();

        const referrings = castIds(withoutEdges(result));
        assert.deepEqual(referrings, refArrayData);
    });

    it('can query with filter on reference', async () => {
        const result = await referringNode
            .query(
                edgesSelector(
                    {
                        _id: true,
                        reference: { _id: true, details: true },
                    },
                    {
                        filter: { reference: { details: { _eq: 'reference B1' } } },
                    },
                ),
            )
            .execute();
        const referrings = castIds(withoutEdges(result));
        assert.equal(referrings.length, 2);
        assert.deepEqual(
            referrings,
            refData.filter(d => d.reference.details === 'reference B1'),
        );
    });

    it('can query with filter with _atLeast on reference array', async () => {
        const result = await referringNode
            .query(
                edgesSelector(
                    {
                        _id: true,
                        referenceArray: { _id: true, details: true },
                    },
                    {
                        filter: {
                            referenceArray: {
                                _atLeast: 1,
                                _id: { _eq: 1 },
                            },
                        },
                    },
                ),
            )
            .execute();
        const referrings = castIds(withoutEdges(result));
        assert.equal(referrings.length, 3);
        assert.deepEqual(
            referrings,
            refArrayData.filter(d => d.referenceArray.map(r => r._id).includes('1')),
        );
    });

    it('can query with filter with _atMost on reference array', async () => {
        const result = await referringNode
            .query(
                edgesSelector(
                    {
                        _id: true,
                        referenceArray: { _id: true, details: true },
                    },
                    {
                        filter: {
                            referenceArray: {
                                _atMost: 1,
                                _id: { _eq: 1 },
                            },
                        },
                    },
                ),
            )
            .execute();
        const referrings = castIds(withoutEdges(result));
        assert.equal(referrings.length, 3);
        assert.deepEqual(
            referrings,
            refArrayData.filter(d => d.referenceArray.map(r => r._id).includes('1')),
        );
    });

    it('can query with filter with static array on reference array', async () => {
        const result = await referringNode
            .query(
                edgesSelector(
                    {
                        _id: true,
                        referenceArray: { _id: true, details: true },
                    },
                    {
                        filter: {
                            referenceArray: [1, 2],
                        },
                    },
                ),
            )
            .execute();
        const referrings = castIds(withoutEdges(result));
        assert.equal(referrings.length, 1);
        assert.deepEqual(
            referrings,
            refArrayData.filter(d => JSON.stringify(d.referenceArray.map(r => r._id)) === JSON.stringify(['1', '2'])),
        );
    });

    it('can query with _none filter on reference array', async () => {
        const result = await referringNode
            .query(
                edgesSelector(
                    {
                        _id: true,
                        referenceArray: { _id: true, details: true },
                    },
                    {
                        filter: { referenceArray: { _none: true, _id: { _eq: 1 } } },
                    },
                ),
            )
            .execute();

        const referrings = castIds(withoutEdges(result));
        assert.equal(referrings.length, 0);
    });

    it('can query with _every filter on reference array', async () => {
        const result = await referringNode
            .query(
                edgesSelector(
                    {
                        _id: true,
                        referenceArray: { _id: true, details: true },
                    },
                    {
                        filter: { referenceArray: { _every: true, _id: { _eq: 1 } } },
                    },
                ),
            )
            .execute();

        const referrings = castIds(withoutEdges(result));
        assert.equal(referrings.length, 2);

        assert.deepEqual(
            referrings,

            refArrayData.filter(d => d.referenceArray.map(v => Number(v._id)).every(i => i === 1)),
        );
    });

    it('can query with _contains filter on reference array', async () => {
        const result = await referringNode
            .query(
                edgesSelector(
                    {
                        _id: true,
                        referenceArray: { _id: true, details: true },
                    },
                    {
                        filter: { referenceArray: { _contains: 2 } },
                    },
                ),
            )
            .execute();

        const referrings = castIds(withoutEdges(result));
        assert.equal(referrings.length, 1);
        assert.deepEqual(
            referrings,
            refArrayData.filter(d => d.referenceArray.map(v => Number(v._id)).includes(2)),
        );
    });

    it('query with simplified filter on reference', async () => {
        const result = await referringNode
            .query(
                edgesSelector(
                    {
                        _id: true,
                        reference: { _id: true, details: true },
                    },
                    {
                        filter: { reference: { _eq: '1' } },
                    },
                ),
            )
            .execute();
        const referrings = castIds(withoutEdges(result));
        assert.equal(referrings.length, 2);
        assert.deepEqual(
            referrings,
            refData.filter(d => d.reference._id === '1'),
        );
    });
    it('query with orderBy on reference property', async () => {
        const selector = {
            _id: true,
            reference: { _id: true, details: true },
        };
        const result = await referringNode
            .query(
                edgesSelector(selector, {
                    orderBy: {
                        reference: { details: -1 },
                    },
                }),
            )
            .execute();
        const referrings = castIds(withoutEdges(result));

        const compare = (
            d1: WithoutSelectedEdges<TestReferringInterface, typeof selector>,
            d2: WithoutSelectedEdges<TestReferringInterface, typeof selector>,
        ) =>
            d1.reference!.details < d2.reference!.details ? 1 : d1.reference!.details > d2.reference!.details ? -1 : 0;

        assert.equal(referrings.length, refData.length);
        const t = refData.slice().sort(compare);
        assert.deepEqual(referrings, t);
    });

    it('paging with orderBy on reference property', async () => {
        const selector = {
            _id: true,
            reference: { _id: true, details: true },
        };
        const orderBy = {
            reference: { details: -1 },
        } as OrderBy<TestReferringInterface>;
        let result = await referringNode.query(edgesSelector(selector, { first: 2, orderBy })).execute();
        assert.equal(result.edges.length, 2);
        assert.isTrue(result.pageInfo.hasNextPage);
        const after = result.pageInfo.endCursor!;
        assert.equal(after, '["reference B1",3]#71');

        result = await referringNode.query(edgesSelector(selector, { first: 2, after, orderBy })).execute();
        assert.equal(result.edges.length, 1);
        assert.isFalse(result.pageInfo.hasNextPage);
        assert.equal(result.pageInfo.endCursor, '["reference A2",2]#48');

        result = await referringNode.query(edgesSelector(selector, { last: 2, orderBy })).execute();
        assert.equal(result.edges.length, 2);
        assert.isFalse(result.pageInfo.hasNextPage);
        assert.equal(result.pageInfo.startCursor, '["reference B1",3]#71');

        const before = result.pageInfo.startCursor!;
        assert.equal(before, '["reference B1",3]#71');
        result = await referringNode.query(edgesSelector(selector, { last: 2, before, orderBy })).execute();
        assert.equal(result.edges.length, 1);
        assert.isFalse(result.pageInfo.hasPreviousPage);
        assert.equal(result.pageInfo.startCursor, '["reference B1",1]#65');
    });

    it('can create with a reference on secondary key', async () => {
        const result = await referringNode
            .create(
                {
                    _id: true,
                    code: true,
                    reference: { details: true },
                },
                {
                    data: {
                        code: 'CREATED',
                        reference: `{ code: '${referredData[1].code}' }`,
                    },
                },
            )
            .execute();
        assert.deepInclude(result, { code: 'CREATED', reference: { details: referredData[1].details! } });

        referringNode.delete({ _id: result._id });
    });

    after(() => restoreTables());
});
