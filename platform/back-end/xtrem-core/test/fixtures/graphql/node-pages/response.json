{"data": {"pages": [{"title": "page1 title", "key": "@sage/xtrem-core/Page1", "access": [{"node": "@sage/xtrem-core/TestDatatypes", "bindings": [{"name": "_updateTick", "status": "authorized"}, {"name": "_updateUser", "status": "authorized"}, {"name": "_createUser", "status": "authorized"}, {"name": "_customData", "status": "authorized"}, {"name": "_sourceId", "status": "authorized"}, {"name": "_id", "status": "authorized"}, {"name": "id", "status": "authorized"}, {"name": "booleanVal", "status": "authorized"}, {"name": "shortVal", "status": "authorized"}, {"name": "integerVal", "status": "authorized"}, {"name": "integerRangeVal", "status": "authorized"}, {"name": "decimalRangeVal", "status": "authorized"}, {"name": "enumVal", "status": "authorized"}, {"name": "stringVal", "status": "authorized"}, {"name": "decimalVal", "status": "authorized"}, {"name": "floatVal", "status": "authorized"}, {"name": "doubleVal", "status": "authorized"}, {"name": "dateVal", "status": "authorized"}, {"name": "dateRangeVal", "status": "authorized"}, {"name": "datetimeRangeVal", "status": "authorized"}, {"name": "timeVal", "status": "authorized"}, {"name": "datetimeVal", "status": "authorized"}, {"name": "binaryStream", "status": "authorized"}, {"name": "textStream", "status": "authorized"}, {"name": "mailTemplate", "status": "authorized"}, {"name": "unsafeMailTemplate", "status": "authorized"}, {"name": "uuidVal", "status": "authorized"}, {"name": "computed", "status": "authorized"}, {"name": "computedCached", "status": "authorized"}, {"name": "complexComputed", "status": "authorized"}, {"name": "jsonVal", "status": "authorized"}, {"name": "integerArrayVal", "status": "authorized"}, {"name": "enumArrayVal", "status": "authorized"}, {"name": "stringArrayVal", "status": "authorized"}, {"name": "_etag", "status": "authorized"}, {"name": "$create", "status": "authorized"}, {"name": "$update", "status": "authorized"}, {"name": "$delete", "status": "authorized"}, {"name": "$read", "status": "authorized"}, {"name": "$lookup", "status": "authorized"}, {"name": "$import", "status": "authorized"}]}, {"node": "@sage/xtrem-core/TestUser", "bindings": [{"name": "_vendor", "status": "authorized"}, {"name": "_updateTick", "status": "authorized"}, {"name": "_updateUser", "status": "authorized"}, {"name": "_createUser", "status": "authorized"}, {"name": "_customData", "status": "authorized"}, {"name": "_sourceId", "status": "authorized"}, {"name": "_id", "status": "authorized"}, {"name": "email", "status": "authorized"}, {"name": "firstName", "status": "authorized"}, {"name": "lastName", "status": "authorized"}, {"name": "isActive", "status": "authorized"}, {"name": "displayName", "status": "authorized"}, {"name": "userType", "status": "authorized"}, {"name": "isAdministrator", "status": "authorized"}, {"name": "isDemoPersona", "status": "authorized"}, {"name": "isApiUser", "status": "authorized"}, {"name": "_etag", "status": "authorized"}, {"name": "$create", "status": "authorized"}, {"name": "$update", "status": "authorized"}, {"name": "$delete", "status": "authorized"}, {"name": "$read", "status": "authorized"}, {"name": "$lookup", "status": "authorized"}, {"name": "$import", "status": "authorized"}]}, {"node": "@sage/xtrem-core/TestSysVendor", "bindings": [{"name": "_updateTick", "status": "authorized"}, {"name": "_sourceId", "status": "authorized"}, {"name": "_id", "status": "authorized"}, {"name": "name", "status": "authorized"}, {"name": "description", "status": "authorized"}, {"name": "_etag", "status": "authorized"}, {"name": "$create", "status": "authorized"}, {"name": "$update", "status": "authorized"}, {"name": "$delete", "status": "authorized"}, {"name": "$read", "status": "authorized"}, {"name": "$lookup", "status": "authorized"}, {"name": "$import", "status": "authorized"}]}], "pageNode": "@sage/xtrem-core/TestDatatypes", "pageAccess": "authorized", "extensions": [{"packageName": "@sage/xtrem-core", "content": "import { Page1 } from '../pages/page1';\n\nexport class Page1Extension extends Page1 {\n    // eslint-disable-next-line class-methods-use-this\n    bar() {\n        return 'bar-page-1';\n    }\n}\n"}], "fragments": [{"name": "@sage/xtrem-core/Page1Fragment", "content": "export class Page1Fragment {\n    // eslint-disable-next-line class-methods-use-this\n    bar() {\n        return 'bar-page-1';\n    }\n}\n"}, {"name": "@sage/xtrem-core/Page1ExtensionFragment", "content": "export class Page1ExtensionFragment {\n    // eslint-disable-next-line class-methods-use-this\n    barExtension() {\n        return 'bar-page-1';\n    }\n}\n"}], "nodeDetails": [{"title": "Test datatypes", "name": "TestDatatypes", "hasAttachments": false, "properties": [{"name": "_updateTick"}, {"name": "_updateUser"}, {"name": "_createUser"}, {"name": "_customData"}, {"name": "_sourceId"}, {"name": "_id"}, {"name": "id"}, {"name": "booleanVal"}, {"name": "shortVal"}, {"name": "integerVal"}, {"name": "integerRangeVal"}, {"name": "decimalRangeVal"}, {"name": "enumVal"}, {"name": "stringVal"}, {"name": "decimalVal"}, {"name": "floatVal"}, {"name": "doubleVal"}, {"name": "dateVal"}, {"name": "dateRangeVal"}, {"name": "datetimeRangeVal"}, {"name": "timeVal"}, {"name": "datetimeVal"}, {"name": "binaryStream"}, {"name": "textStream"}, {"name": "mailTemplate"}, {"name": "unsafeMailTemplate"}, {"name": "uuidVal"}, {"name": "computed"}, {"name": "computedCached"}, {"name": "complexComputed"}, {"name": "jsonVal"}, {"name": "integerArrayVal"}, {"name": "enumArrayVal"}, {"name": "stringArrayVal"}, {"name": "_etag"}], "queries": [], "mutations": []}, {"title": "Test user", "name": "TestUser", "hasAttachments": false, "properties": [{"name": "_vendor"}, {"name": "_updateTick"}, {"name": "_updateUser"}, {"name": "_createUser"}, {"name": "_customData"}, {"name": "_sourceId"}, {"name": "_id"}, {"name": "email"}, {"name": "firstName"}, {"name": "lastName"}, {"name": "isActive"}, {"name": "displayName"}, {"name": "userType"}, {"name": "isAdministrator"}, {"name": "isDemoPersona"}, {"name": "isApiUser"}, {"name": "_etag"}], "queries": [], "mutations": []}, {"title": "Test sys vendor", "name": "TestSysVendor", "hasAttachments": false, "properties": [{"name": "_updateTick"}, {"name": "_sourceId"}, {"name": "_id"}, {"name": "name"}, {"name": "description"}, {"name": "_etag"}], "queries": [], "mutations": []}]}]}}