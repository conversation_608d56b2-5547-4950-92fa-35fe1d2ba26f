{
    getNodeDetails(nodeName: "TestDocument") {
        title
        name
        hasAttachments
        defaultDataType
        defaultDataTypeDetails {
            node
            type
            title
            value {
                bind
                title
                type
                enumType
            }
            helperText {
                bind
                title
                type
                enumType
            }
            isDefault
            columns {
                bind
                title
                type
                enumType
            }
            precision
            scale
            roundingMode
            maxLength
            isLocalized
            doNotTrim
            truncate
            values {
                value
                title
            }
            allowedContentTypes
        }
        properties {
            title
            name
            canSort
            canFilter
            enumType
            type
            isCustom
            dataType
            dataTypeDetails {
                node
                type
                title
                value {
                    bind
                    title
                    type
                    enumType
                }
                helperText {
                    bind
                    title
                    type
                    enumType
                }
                isDefault
                columns {
                    bind
                    title
                    type
                    enumType
                }
                precision
                scale
                roundingMode
                maxLength
                isLocalized
                doNotTrim
                truncate
                values {
                    value
                    title
                }
                allowedContentTypes
            }
            targetNode
            targetNodeDetails {
                title
                name
                hasAttachments
                defaultDataType
                defaultDataTypeDetails {
                    node
                    type
                    title
                    value {
                        bind
                        title
                        type
                        enumType
                    }
                    helperText {
                        bind
                        title
                        type
                        enumType
                    }
                    isDefault
                    columns {
                        bind
                        title
                        type
                        enumType
                    }
                    precision
                    scale
                    roundingMode
                    maxLength
                    isLocalized
                    doNotTrim
                    truncate
                    values {
                        value
                        title
                    }
                    allowedContentTypes
                }
                properties {
                    title
                    name
                    canSort
                    canFilter
                    enumType
                    type
                    isCustom
                    dataType
                    dataTypeDetails {
                        node
                        type
                        title
                        value {
                            bind
                            title
                            type
                            enumType
                        }
                        helperText {
                            bind
                            title
                            type
                            enumType
                        }
                        isDefault
                        columns {
                            bind
                            title
                            type
                            enumType
                        }
                        precision
                        scale
                        roundingMode
                        maxLength
                        isLocalized
                        doNotTrim
                        truncate
                        values {
                            value
                            title
                        }
                        allowedContentTypes
                    }
                    targetNode
                    isOnInputType
                    isOnOutputType
                    isMutable
                    isSystemProperty
                }
                queries {
                    title
                    name
                    parameters {
                        name
                        title
                    }
                }
                mutations {
                    title
                    name
                    parameters {
                        name
                        title
                    }
                }
            }
            isOnInputType
            isOnOutputType
            isMutable
            isSystemProperty
        }
        queries {
            title
            name
            parameters {
                name
                title
            }
        }
        mutations {
            title
            name
            parameters {
                name
                title
            }
        }
    }
}
