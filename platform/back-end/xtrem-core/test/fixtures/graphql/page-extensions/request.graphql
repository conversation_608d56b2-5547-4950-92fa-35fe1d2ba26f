{
    pages {
        title
        key
        access {
            node
            bindings {
                name
                status
            }
        }
        pageNode
        pageAccess
        extensions {
            packageName
            content
        }
        fragments {
            name
            content
        }
        nodeDetails {
            title
            name
            hasAttachments
            properties {
                name
            }
            queries {
                name
            }
            mutations {
                name
            }
        }
    }
}
