{"data": {"getNodeDetails": {"title": "Test document line", "name": "TestDocumentLine", "hasAttachments": false, "defaultDataType": "testDocumentLine", "defaultDataTypeDetails": {"node": "@sage/xtrem-core/TestDocumentLine", "type": "reference", "title": "Test document line", "value": {"bind": "_id", "title": "_id", "type": "integer"}, "helperText": {"bind": "_id", "title": "_id", "type": "integer"}, "isDefault": false, "columns": [{"bind": "_id", "title": "_id", "type": "integer"}], "precision": null, "scale": null, "roundingMode": null, "maxLength": null, "isLocalized": null, "doNotTrim": null, "truncate": null, "values": null, "allowedContentTypes": null}, "properties": [{"title": "Update tick", "name": "_updateTick", "canSort": true, "canFilter": true, "enumType": null, "type": "integer", "isCustom": false, "dataType": "", "dataTypeDetails": null, "targetNode": "", "targetNodeDetails": null, "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": true}, {"title": "Update user", "name": "_updateUser", "canSort": true, "canFilter": true, "enumType": null, "type": "reference", "isCustom": false, "dataType": "testUser", "dataTypeDetails": {"node": "@sage/xtrem-core/TestUser", "type": "reference", "title": "Test user", "value": {"bind": "email", "title": "Email", "type": "string", "enumType": null}, "helperText": {"bind": "email", "title": "Email", "type": "string", "enumType": null}, "isDefault": false, "columns": [{"bind": "email", "title": "Email", "type": "string", "enumType": null}], "precision": null, "scale": null, "roundingMode": null, "maxLength": null, "isLocalized": null, "doNotTrim": null, "truncate": null, "values": null, "allowedContentTypes": null}, "targetNode": "@sage/xtrem-core/TestUser", "targetNodeDetails": {"title": "Test user", "name": "TestUser", "hasAttachments": false, "defaultDataType": "testUser", "defaultDataTypeDetails": {"node": "@sage/xtrem-core/TestUser", "type": "reference", "title": "Test user", "value": {"bind": "email", "title": "Email", "type": "string", "enumType": null}, "helperText": {"bind": "email", "title": "Email", "type": "string", "enumType": null}, "isDefault": false, "columns": [{"bind": "email", "title": "Email", "type": "string", "enumType": null}], "precision": null, "scale": null, "roundingMode": null, "maxLength": null, "isLocalized": null, "doNotTrim": null, "truncate": null, "values": null, "allowedContentTypes": null}, "properties": [{"title": "<PERSON><PERSON><PERSON>", "name": "_vendor", "canSort": true, "canFilter": true, "enumType": null, "type": "reference", "isCustom": false, "dataType": "testSysVendor", "dataTypeDetails": {"node": "@sage/xtrem-core/TestSysVendor", "type": "reference", "title": "Test sys vendor", "value": {"bind": "name", "title": "Name", "type": "string", "enumType": null}, "helperText": {"bind": "name", "title": "Name", "type": "string", "enumType": null}, "isDefault": false, "columns": [{"bind": "name", "title": "Name", "type": "string", "enumType": null}], "precision": null, "scale": null, "roundingMode": null, "maxLength": null, "isLocalized": null, "doNotTrim": null, "truncate": null, "values": null, "allowedContentTypes": null}, "targetNode": "@sage/xtrem-core/TestSysVendor", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": true}, {"title": "Update tick", "name": "_updateTick", "canSort": true, "canFilter": true, "enumType": null, "type": "integer", "isCustom": false, "dataType": "", "dataTypeDetails": null, "targetNode": "", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": true}, {"title": "Update user", "name": "_updateUser", "canSort": true, "canFilter": true, "enumType": null, "type": "reference", "isCustom": false, "dataType": "testUser", "dataTypeDetails": {"node": "@sage/xtrem-core/TestUser", "type": "reference", "title": "Test user", "value": {"bind": "email", "title": "Email", "type": "string", "enumType": null}, "helperText": {"bind": "email", "title": "Email", "type": "string", "enumType": null}, "isDefault": false, "columns": [{"bind": "email", "title": "Email", "type": "string", "enumType": null}], "precision": null, "scale": null, "roundingMode": null, "maxLength": null, "isLocalized": null, "doNotTrim": null, "truncate": null, "values": null, "allowedContentTypes": null}, "targetNode": "@sage/xtrem-core/TestUser", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": true}, {"title": "Create user", "name": "_createUser", "canSort": true, "canFilter": true, "enumType": null, "type": "reference", "isCustom": false, "dataType": "testUser", "dataTypeDetails": {"node": "@sage/xtrem-core/TestUser", "type": "reference", "title": "Test user", "value": {"bind": "email", "title": "Email", "type": "string", "enumType": null}, "helperText": {"bind": "email", "title": "Email", "type": "string", "enumType": null}, "isDefault": false, "columns": [{"bind": "email", "title": "Email", "type": "string", "enumType": null}], "precision": null, "scale": null, "roundingMode": null, "maxLength": null, "isLocalized": null, "doNotTrim": null, "truncate": null, "values": null, "allowedContentTypes": null}, "targetNode": "@sage/xtrem-core/TestUser", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": true}, {"title": "Custom data", "name": "_customData", "canSort": true, "canFilter": true, "enumType": null, "type": "json", "isCustom": false, "dataType": "_jsonDataType", "dataTypeDetails": {"node": null, "type": "json", "title": "Json data type", "value": null, "helperText": null, "isDefault": false, "columns": null, "precision": null, "scale": null, "roundingMode": null, "maxLength": null, "isLocalized": null, "doNotTrim": null, "truncate": null, "values": null, "allowedContentTypes": null}, "targetNode": "", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": true}, {"title": "Source id", "name": "_sourceId", "canSort": true, "canFilter": true, "enumType": null, "type": "string", "isCustom": false, "dataType": "_sourceIdDataType", "dataTypeDetails": {"node": null, "type": "string", "title": "Source id data type", "value": null, "helperText": null, "isDefault": false, "columns": null, "precision": null, "scale": null, "roundingMode": null, "maxLength": 128, "isLocalized": false, "doNotTrim": false, "truncate": false, "values": null, "allowedContentTypes": null}, "targetNode": "", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": true}, {"title": "_id", "name": "_id", "canSort": true, "canFilter": true, "enumType": null, "type": "integer", "isCustom": false, "dataType": "", "dataTypeDetails": null, "targetNode": "", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": true}, {"title": "Email", "name": "email", "canSort": true, "canFilter": true, "enumType": null, "type": "string", "isCustom": false, "dataType": "", "dataTypeDetails": null, "targetNode": "", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": false}, {"title": "First name", "name": "firstName", "canSort": true, "canFilter": true, "enumType": null, "type": "string", "isCustom": false, "dataType": "", "dataTypeDetails": null, "targetNode": "", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": false}, {"title": "Last name", "name": "lastName", "canSort": true, "canFilter": true, "enumType": null, "type": "string", "isCustom": false, "dataType": "", "dataTypeDetails": null, "targetNode": "", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": false}, {"title": "Is active", "name": "isActive", "canSort": true, "canFilter": true, "enumType": null, "type": "boolean", "isCustom": false, "dataType": "", "dataTypeDetails": null, "targetNode": "", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": false}, {"title": "Display name", "name": "displayName", "canSort": true, "canFilter": true, "enumType": null, "type": "string", "isCustom": false, "dataType": "", "dataTypeDetails": null, "targetNode": "", "isOnInputType": false, "isOnOutputType": true, "isMutable": false, "isSystemProperty": false}, {"title": "User type", "name": "userType", "canSort": true, "canFilter": true, "enumType": "@sage/xtrem-core/TestUserTypeEnum", "type": "enum", "isCustom": false, "dataType": "", "dataTypeDetails": null, "targetNode": "", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": false}, {"title": "Is administrator", "name": "isAdministrator", "canSort": true, "canFilter": true, "enumType": null, "type": "boolean", "isCustom": false, "dataType": "", "dataTypeDetails": null, "targetNode": "", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": false}, {"title": "Is demo persona", "name": "isDemoPersona", "canSort": true, "canFilter": true, "enumType": null, "type": "boolean", "isCustom": false, "dataType": "", "dataTypeDetails": null, "targetNode": "", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": false}, {"title": "Is api user", "name": "isApiUser", "canSort": true, "canFilter": true, "enumType": null, "type": "boolean", "isCustom": false, "dataType": "", "dataTypeDetails": null, "targetNode": "", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": false}, {"title": "Etag", "name": "_etag", "canSort": false, "canFilter": false, "enumType": null, "type": "string", "isCustom": false, "dataType": "_etagDataType", "dataTypeDetails": {"node": null, "type": "string", "title": "Etag data type", "value": null, "helperText": null, "isDefault": false, "columns": null, "precision": null, "scale": null, "roundingMode": null, "maxLength": 100, "isLocalized": false, "doNotTrim": false, "truncate": false, "values": null, "allowedContentTypes": null}, "targetNode": "", "isOnInputType": false, "isOnOutputType": true, "isMutable": false, "isSystemProperty": true}], "queries": [], "mutations": []}, "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": true}, {"title": "Create user", "name": "_createUser", "canSort": true, "canFilter": true, "enumType": null, "type": "reference", "isCustom": false, "dataType": "testUser", "dataTypeDetails": {"node": "@sage/xtrem-core/TestUser", "type": "reference", "title": "Test user", "value": {"bind": "email", "title": "Email", "type": "string", "enumType": null}, "helperText": {"bind": "email", "title": "Email", "type": "string", "enumType": null}, "isDefault": false, "columns": [{"bind": "email", "title": "Email", "type": "string", "enumType": null}], "precision": null, "scale": null, "roundingMode": null, "maxLength": null, "isLocalized": null, "doNotTrim": null, "truncate": null, "values": null, "allowedContentTypes": null}, "targetNode": "@sage/xtrem-core/TestUser", "targetNodeDetails": {"title": "Test user", "name": "TestUser", "hasAttachments": false, "defaultDataType": "testUser", "defaultDataTypeDetails": {"node": "@sage/xtrem-core/TestUser", "type": "reference", "title": "Test user", "value": {"bind": "email", "title": "Email", "type": "string", "enumType": null}, "helperText": {"bind": "email", "title": "Email", "type": "string", "enumType": null}, "isDefault": false, "columns": [{"bind": "email", "title": "Email", "type": "string", "enumType": null}], "precision": null, "scale": null, "roundingMode": null, "maxLength": null, "isLocalized": null, "doNotTrim": null, "truncate": null, "values": null, "allowedContentTypes": null}, "properties": [{"title": "<PERSON><PERSON><PERSON>", "name": "_vendor", "canSort": true, "canFilter": true, "enumType": null, "type": "reference", "isCustom": false, "dataType": "testSysVendor", "dataTypeDetails": {"node": "@sage/xtrem-core/TestSysVendor", "type": "reference", "title": "Test sys vendor", "value": {"bind": "name", "title": "Name", "type": "string", "enumType": null}, "helperText": {"bind": "name", "title": "Name", "type": "string", "enumType": null}, "isDefault": false, "columns": [{"bind": "name", "title": "Name", "type": "string", "enumType": null}], "precision": null, "scale": null, "roundingMode": null, "maxLength": null, "isLocalized": null, "doNotTrim": null, "truncate": null, "values": null, "allowedContentTypes": null}, "targetNode": "@sage/xtrem-core/TestSysVendor", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": true}, {"title": "Update tick", "name": "_updateTick", "canSort": true, "canFilter": true, "enumType": null, "type": "integer", "isCustom": false, "dataType": "", "dataTypeDetails": null, "targetNode": "", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": true}, {"title": "Update user", "name": "_updateUser", "canSort": true, "canFilter": true, "enumType": null, "type": "reference", "isCustom": false, "dataType": "testUser", "dataTypeDetails": {"node": "@sage/xtrem-core/TestUser", "type": "reference", "title": "Test user", "value": {"bind": "email", "title": "Email", "type": "string", "enumType": null}, "helperText": {"bind": "email", "title": "Email", "type": "string", "enumType": null}, "isDefault": false, "columns": [{"bind": "email", "title": "Email", "type": "string", "enumType": null}], "precision": null, "scale": null, "roundingMode": null, "maxLength": null, "isLocalized": null, "doNotTrim": null, "truncate": null, "values": null, "allowedContentTypes": null}, "targetNode": "@sage/xtrem-core/TestUser", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": true}, {"title": "Create user", "name": "_createUser", "canSort": true, "canFilter": true, "enumType": null, "type": "reference", "isCustom": false, "dataType": "testUser", "dataTypeDetails": {"node": "@sage/xtrem-core/TestUser", "type": "reference", "title": "Test user", "value": {"bind": "email", "title": "Email", "type": "string", "enumType": null}, "helperText": {"bind": "email", "title": "Email", "type": "string", "enumType": null}, "isDefault": false, "columns": [{"bind": "email", "title": "Email", "type": "string", "enumType": null}], "precision": null, "scale": null, "roundingMode": null, "maxLength": null, "isLocalized": null, "doNotTrim": null, "truncate": null, "values": null, "allowedContentTypes": null}, "targetNode": "@sage/xtrem-core/TestUser", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": true}, {"title": "Custom data", "name": "_customData", "canSort": true, "canFilter": true, "enumType": null, "type": "json", "isCustom": false, "dataType": "_jsonDataType", "dataTypeDetails": {"node": null, "type": "json", "title": "Json data type", "value": null, "helperText": null, "isDefault": false, "columns": null, "precision": null, "scale": null, "roundingMode": null, "maxLength": null, "isLocalized": null, "doNotTrim": null, "truncate": null, "values": null, "allowedContentTypes": null}, "targetNode": "", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": true}, {"title": "Source id", "name": "_sourceId", "canSort": true, "canFilter": true, "enumType": null, "type": "string", "isCustom": false, "dataType": "_sourceIdDataType", "dataTypeDetails": {"node": null, "type": "string", "title": "Source id data type", "value": null, "helperText": null, "isDefault": false, "columns": null, "precision": null, "scale": null, "roundingMode": null, "maxLength": 128, "isLocalized": false, "doNotTrim": false, "truncate": false, "values": null, "allowedContentTypes": null}, "targetNode": "", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": true}, {"title": "_id", "name": "_id", "canSort": true, "canFilter": true, "enumType": null, "type": "integer", "isCustom": false, "dataType": "", "dataTypeDetails": null, "targetNode": "", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": true}, {"title": "Email", "name": "email", "canSort": true, "canFilter": true, "enumType": null, "type": "string", "isCustom": false, "dataType": "", "dataTypeDetails": null, "targetNode": "", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": false}, {"title": "First name", "name": "firstName", "canSort": true, "canFilter": true, "enumType": null, "type": "string", "isCustom": false, "dataType": "", "dataTypeDetails": null, "targetNode": "", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": false}, {"title": "Last name", "name": "lastName", "canSort": true, "canFilter": true, "enumType": null, "type": "string", "isCustom": false, "dataType": "", "dataTypeDetails": null, "targetNode": "", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": false}, {"title": "Is active", "name": "isActive", "canSort": true, "canFilter": true, "enumType": null, "type": "boolean", "isCustom": false, "dataType": "", "dataTypeDetails": null, "targetNode": "", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": false}, {"title": "Display name", "name": "displayName", "canSort": true, "canFilter": true, "enumType": null, "type": "string", "isCustom": false, "dataType": "", "dataTypeDetails": null, "targetNode": "", "isOnInputType": false, "isOnOutputType": true, "isMutable": false, "isSystemProperty": false}, {"title": "User type", "name": "userType", "canSort": true, "canFilter": true, "enumType": "@sage/xtrem-core/TestUserTypeEnum", "type": "enum", "isCustom": false, "dataType": "", "dataTypeDetails": null, "targetNode": "", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": false}, {"title": "Is administrator", "name": "isAdministrator", "canSort": true, "canFilter": true, "enumType": null, "type": "boolean", "isCustom": false, "dataType": "", "dataTypeDetails": null, "targetNode": "", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": false}, {"title": "Is demo persona", "name": "isDemoPersona", "canSort": true, "canFilter": true, "enumType": null, "type": "boolean", "isCustom": false, "dataType": "", "dataTypeDetails": null, "targetNode": "", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": false}, {"title": "Is api user", "name": "isApiUser", "canSort": true, "canFilter": true, "enumType": null, "type": "boolean", "isCustom": false, "dataType": "", "dataTypeDetails": null, "targetNode": "", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": false}, {"title": "Etag", "name": "_etag", "canSort": false, "canFilter": false, "enumType": null, "type": "string", "isCustom": false, "dataType": "_etagDataType", "dataTypeDetails": {"node": null, "type": "string", "title": "Etag data type", "value": null, "helperText": null, "isDefault": false, "columns": null, "precision": null, "scale": null, "roundingMode": null, "maxLength": 100, "isLocalized": false, "doNotTrim": false, "truncate": false, "values": null, "allowedContentTypes": null}, "targetNode": "", "isOnInputType": false, "isOnOutputType": true, "isMutable": false, "isSystemProperty": true}], "queries": [], "mutations": []}, "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": true}, {"title": "Custom data", "name": "_customData", "canSort": true, "canFilter": true, "enumType": null, "type": "json", "isCustom": false, "dataType": "_jsonDataType", "dataTypeDetails": {"node": null, "type": "json", "title": "Json data type", "value": null, "helperText": null, "isDefault": false, "columns": null, "precision": null, "scale": null, "roundingMode": null, "maxLength": null, "isLocalized": null, "doNotTrim": null, "truncate": null, "values": null, "allowedContentTypes": null}, "targetNode": "", "targetNodeDetails": null, "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": true}, {"title": "Source id", "name": "_sourceId", "canSort": true, "canFilter": true, "enumType": null, "type": "string", "isCustom": false, "dataType": "_sourceIdDataType", "dataTypeDetails": {"node": null, "type": "string", "title": "Source id data type", "value": null, "helperText": null, "isDefault": false, "columns": null, "precision": null, "scale": null, "roundingMode": null, "maxLength": 128, "isLocalized": false, "doNotTrim": false, "truncate": false, "values": null, "allowedContentTypes": null}, "targetNode": "", "targetNodeDetails": null, "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": true}, {"title": "_id", "name": "_id", "canSort": true, "canFilter": true, "enumType": null, "type": "integer", "isCustom": false, "dataType": "", "dataTypeDetails": null, "targetNode": "", "targetNodeDetails": null, "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": true}, {"title": "Collection action", "name": "_action", "canSort": false, "canFilter": false, "enumType": "@sage/xtrem-core/SystemDataTypes", "type": "enum", "isCustom": false, "dataType": "_updateActionDataType", "dataTypeDetails": {"node": null, "type": "enum", "title": "Update action enum", "value": null, "helperText": null, "isDefault": false, "columns": null, "precision": null, "scale": null, "roundingMode": null, "maxLength": null, "isLocalized": null, "doNotTrim": null, "truncate": null, "values": [{"value": "create", "title": "Create"}, {"value": "update", "title": "Update"}, {"value": "delete", "title": "Delete"}], "allowedContentTypes": null}, "targetNode": "", "targetNodeDetails": null, "isOnInputType": true, "isOnOutputType": false, "isMutable": false, "isSystemProperty": true}, {"title": "Description", "name": "description", "canSort": true, "canFilter": true, "enumType": null, "type": "string", "isCustom": false, "dataType": "descriptionDataType", "dataTypeDetails": {"node": null, "type": "string", "title": "Description data type", "value": null, "helperText": null, "isDefault": false, "columns": null, "precision": null, "scale": null, "roundingMode": null, "maxLength": 250, "isLocalized": false, "doNotTrim": false, "truncate": false, "values": null, "allowedContentTypes": null}, "targetNode": "", "targetNodeDetails": null, "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": false}, {"title": "Line number", "name": "lineNumber", "canSort": true, "canFilter": true, "enumType": null, "type": "integer", "isCustom": false, "dataType": "", "dataTypeDetails": null, "targetNode": "", "targetNodeDetails": null, "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": false}, {"title": "Optional reference", "name": "optionalReference", "canSort": true, "canFilter": true, "enumType": null, "type": "reference", "isCustom": false, "dataType": "test<PERSON><PERSON><PERSON><PERSON>", "dataTypeDetails": {"node": "@sage/xtrem-core/TestReferred", "type": "reference", "title": "Test referred", "value": {"bind": "code", "title": "Code", "type": "string", "enumType": null}, "helperText": {"bind": "code", "title": "Code", "type": "string", "enumType": null}, "isDefault": false, "columns": [{"bind": "code", "title": "Code", "type": "string", "enumType": null}], "precision": null, "scale": null, "roundingMode": null, "maxLength": null, "isLocalized": null, "doNotTrim": null, "truncate": null, "values": null, "allowedContentTypes": null}, "targetNode": "@sage/xtrem-core/TestReferred", "targetNodeDetails": {"title": "Test referred", "name": "TestReferred", "hasAttachments": false, "defaultDataType": "test<PERSON><PERSON><PERSON><PERSON>", "defaultDataTypeDetails": {"node": "@sage/xtrem-core/TestReferred", "type": "reference", "title": "Test referred", "value": {"bind": "code", "title": "Code", "type": "string", "enumType": null}, "helperText": {"bind": "code", "title": "Code", "type": "string", "enumType": null}, "isDefault": false, "columns": [{"bind": "code", "title": "Code", "type": "string", "enumType": null}], "precision": null, "scale": null, "roundingMode": null, "maxLength": null, "isLocalized": null, "doNotTrim": null, "truncate": null, "values": null, "allowedContentTypes": null}, "properties": [{"title": "Update tick", "name": "_updateTick", "canSort": true, "canFilter": true, "enumType": null, "type": "integer", "isCustom": false, "dataType": "", "dataTypeDetails": null, "targetNode": "", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": true}, {"title": "Update user", "name": "_updateUser", "canSort": true, "canFilter": true, "enumType": null, "type": "reference", "isCustom": false, "dataType": "testUser", "dataTypeDetails": {"node": "@sage/xtrem-core/TestUser", "type": "reference", "title": "Test user", "value": {"bind": "email", "title": "Email", "type": "string", "enumType": null}, "helperText": {"bind": "email", "title": "Email", "type": "string", "enumType": null}, "isDefault": false, "columns": [{"bind": "email", "title": "Email", "type": "string", "enumType": null}], "precision": null, "scale": null, "roundingMode": null, "maxLength": null, "isLocalized": null, "doNotTrim": null, "truncate": null, "values": null, "allowedContentTypes": null}, "targetNode": "@sage/xtrem-core/TestUser", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": true}, {"title": "Create user", "name": "_createUser", "canSort": true, "canFilter": true, "enumType": null, "type": "reference", "isCustom": false, "dataType": "testUser", "dataTypeDetails": {"node": "@sage/xtrem-core/TestUser", "type": "reference", "title": "Test user", "value": {"bind": "email", "title": "Email", "type": "string", "enumType": null}, "helperText": {"bind": "email", "title": "Email", "type": "string", "enumType": null}, "isDefault": false, "columns": [{"bind": "email", "title": "Email", "type": "string", "enumType": null}], "precision": null, "scale": null, "roundingMode": null, "maxLength": null, "isLocalized": null, "doNotTrim": null, "truncate": null, "values": null, "allowedContentTypes": null}, "targetNode": "@sage/xtrem-core/TestUser", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": true}, {"title": "Custom data", "name": "_customData", "canSort": true, "canFilter": true, "enumType": null, "type": "json", "isCustom": false, "dataType": "_jsonDataType", "dataTypeDetails": {"node": null, "type": "json", "title": "Json data type", "value": null, "helperText": null, "isDefault": false, "columns": null, "precision": null, "scale": null, "roundingMode": null, "maxLength": null, "isLocalized": null, "doNotTrim": null, "truncate": null, "values": null, "allowedContentTypes": null}, "targetNode": "", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": true}, {"title": "Source id", "name": "_sourceId", "canSort": true, "canFilter": true, "enumType": null, "type": "string", "isCustom": false, "dataType": "_sourceIdDataType", "dataTypeDetails": {"node": null, "type": "string", "title": "Source id data type", "value": null, "helperText": null, "isDefault": false, "columns": null, "precision": null, "scale": null, "roundingMode": null, "maxLength": 128, "isLocalized": false, "doNotTrim": false, "truncate": false, "values": null, "allowedContentTypes": null}, "targetNode": "", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": true}, {"title": "_id", "name": "_id", "canSort": true, "canFilter": true, "enumType": null, "type": "integer", "isCustom": false, "dataType": "", "dataTypeDetails": null, "targetNode": "", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": true}, {"title": "Code", "name": "code", "canSort": true, "canFilter": true, "enumType": null, "type": "string", "isCustom": false, "dataType": "codeDataType", "dataTypeDetails": {"node": null, "type": "string", "title": "Code data type", "value": null, "helperText": null, "isDefault": false, "columns": null, "precision": null, "scale": null, "roundingMode": null, "maxLength": 32, "isLocalized": false, "doNotTrim": false, "truncate": false, "values": null, "allowedContentTypes": null}, "targetNode": "", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": false}, {"title": "Details", "name": "details", "canSort": true, "canFilter": true, "enumType": null, "type": "string", "isCustom": false, "dataType": "descriptionDataType", "dataTypeDetails": {"node": null, "type": "string", "title": "Description data type", "value": null, "helperText": null, "isDefault": false, "columns": null, "precision": null, "scale": null, "roundingMode": null, "maxLength": 250, "isLocalized": false, "doNotTrim": false, "truncate": false, "values": null, "allowedContentTypes": null}, "targetNode": "", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": false}, {"title": "Restricted", "name": "restricted", "canSort": true, "canFilter": true, "enumType": null, "type": "string", "isCustom": false, "dataType": "descriptionDataType", "dataTypeDetails": {"node": null, "type": "string", "title": "Description data type", "value": null, "helperText": null, "isDefault": false, "columns": null, "precision": null, "scale": null, "roundingMode": null, "maxLength": 250, "isLocalized": false, "doNotTrim": false, "truncate": false, "values": null, "allowedContentTypes": null}, "targetNode": "", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": false}, {"title": "Integer val", "name": "integerVal", "canSort": true, "canFilter": true, "enumType": null, "type": "integer", "isCustom": false, "dataType": "", "dataTypeDetails": null, "targetNode": "", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": false}, {"title": "Etag", "name": "_etag", "canSort": false, "canFilter": false, "enumType": null, "type": "string", "isCustom": false, "dataType": "_etagDataType", "dataTypeDetails": {"node": null, "type": "string", "title": "Etag data type", "value": null, "helperText": null, "isDefault": false, "columns": null, "precision": null, "scale": null, "roundingMode": null, "maxLength": 100, "isLocalized": false, "doNotTrim": false, "truncate": false, "values": null, "allowedContentTypes": null}, "targetNode": "", "isOnInputType": false, "isOnOutputType": true, "isMutable": false, "isSystemProperty": true}], "queries": [], "mutations": []}, "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": false}, {"title": "Document", "name": "document", "canSort": true, "canFilter": true, "enumType": null, "type": "reference", "isCustom": false, "dataType": "testDocument", "dataTypeDetails": {"node": "@sage/xtrem-core/TestDocument", "type": "reference", "title": "Test document", "value": {"bind": "_id", "title": "_id", "type": "integer", "enumType": null}, "helperText": {"bind": "_id", "title": "_id", "type": "integer", "enumType": null}, "isDefault": false, "columns": [{"bind": "_id", "title": "_id", "type": "integer", "enumType": null}], "precision": null, "scale": null, "roundingMode": null, "maxLength": null, "isLocalized": null, "doNotTrim": null, "truncate": null, "values": null, "allowedContentTypes": null}, "targetNode": "@sage/xtrem-core/TestDocument", "targetNodeDetails": {"title": "Test document", "name": "TestDocument", "hasAttachments": false, "defaultDataType": "testDocument", "defaultDataTypeDetails": {"node": "@sage/xtrem-core/TestDocument", "type": "reference", "title": "Test document", "value": {"bind": "_id", "title": "_id", "type": "integer", "enumType": null}, "helperText": {"bind": "_id", "title": "_id", "type": "integer", "enumType": null}, "isDefault": false, "columns": [{"bind": "_id", "title": "_id", "type": "integer", "enumType": null}], "precision": null, "scale": null, "roundingMode": null, "maxLength": null, "isLocalized": null, "doNotTrim": null, "truncate": null, "values": null, "allowedContentTypes": null}, "properties": [{"title": "Update tick", "name": "_updateTick", "canSort": true, "canFilter": true, "enumType": null, "type": "integer", "isCustom": false, "dataType": "", "dataTypeDetails": null, "targetNode": "", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": true}, {"title": "Update user", "name": "_updateUser", "canSort": true, "canFilter": true, "enumType": null, "type": "reference", "isCustom": false, "dataType": "testUser", "dataTypeDetails": {"node": "@sage/xtrem-core/TestUser", "type": "reference", "title": "Test user", "value": {"bind": "email", "title": "Email", "type": "string", "enumType": null}, "helperText": {"bind": "email", "title": "Email", "type": "string", "enumType": null}, "isDefault": false, "columns": [{"bind": "email", "title": "Email", "type": "string", "enumType": null}], "precision": null, "scale": null, "roundingMode": null, "maxLength": null, "isLocalized": null, "doNotTrim": null, "truncate": null, "values": null, "allowedContentTypes": null}, "targetNode": "@sage/xtrem-core/TestUser", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": true}, {"title": "Create user", "name": "_createUser", "canSort": true, "canFilter": true, "enumType": null, "type": "reference", "isCustom": false, "dataType": "testUser", "dataTypeDetails": {"node": "@sage/xtrem-core/TestUser", "type": "reference", "title": "Test user", "value": {"bind": "email", "title": "Email", "type": "string", "enumType": null}, "helperText": {"bind": "email", "title": "Email", "type": "string", "enumType": null}, "isDefault": false, "columns": [{"bind": "email", "title": "Email", "type": "string", "enumType": null}], "precision": null, "scale": null, "roundingMode": null, "maxLength": null, "isLocalized": null, "doNotTrim": null, "truncate": null, "values": null, "allowedContentTypes": null}, "targetNode": "@sage/xtrem-core/TestUser", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": true}, {"title": "Custom data", "name": "_customData", "canSort": true, "canFilter": true, "enumType": null, "type": "json", "isCustom": false, "dataType": "_jsonDataType", "dataTypeDetails": {"node": null, "type": "json", "title": "Json data type", "value": null, "helperText": null, "isDefault": false, "columns": null, "precision": null, "scale": null, "roundingMode": null, "maxLength": null, "isLocalized": null, "doNotTrim": null, "truncate": null, "values": null, "allowedContentTypes": null}, "targetNode": "", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": true}, {"title": "Source id", "name": "_sourceId", "canSort": true, "canFilter": true, "enumType": null, "type": "string", "isCustom": false, "dataType": "_sourceIdDataType", "dataTypeDetails": {"node": null, "type": "string", "title": "Source id data type", "value": null, "helperText": null, "isDefault": false, "columns": null, "precision": null, "scale": null, "roundingMode": null, "maxLength": 128, "isLocalized": false, "doNotTrim": false, "truncate": false, "values": null, "allowedContentTypes": null}, "targetNode": "", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": true}, {"title": "_id", "name": "_id", "canSort": true, "canFilter": true, "enumType": null, "type": "integer", "isCustom": false, "dataType": "", "dataTypeDetails": null, "targetNode": "", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": true}, {"title": "Code", "name": "code", "canSort": true, "canFilter": true, "enumType": null, "type": "string", "isCustom": false, "dataType": "codeDataType", "dataTypeDetails": {"node": null, "type": "string", "title": "Code data type", "value": null, "helperText": null, "isDefault": false, "columns": null, "precision": null, "scale": null, "roundingMode": null, "maxLength": 32, "isLocalized": false, "doNotTrim": false, "truncate": false, "values": null, "allowedContentTypes": null}, "targetNode": "", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": false}, {"title": "Description", "name": "description", "canSort": true, "canFilter": true, "enumType": null, "type": "string", "isCustom": false, "dataType": "descriptionDataType", "dataTypeDetails": {"node": null, "type": "string", "title": "Description data type", "value": null, "helperText": null, "isDefault": false, "columns": null, "precision": null, "scale": null, "roundingMode": null, "maxLength": 250, "isLocalized": false, "doNotTrim": false, "truncate": false, "values": null, "allowedContentTypes": null}, "targetNode": "", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": false}, {"title": "Mandatory reference", "name": "mandatoryReference", "canSort": true, "canFilter": true, "enumType": null, "type": "reference", "isCustom": false, "dataType": "test<PERSON><PERSON><PERSON><PERSON>", "dataTypeDetails": {"node": "@sage/xtrem-core/TestReferred", "type": "reference", "title": "Test referred", "value": {"bind": "code", "title": "Code", "type": "string", "enumType": null}, "helperText": {"bind": "code", "title": "Code", "type": "string", "enumType": null}, "isDefault": false, "columns": [{"bind": "code", "title": "Code", "type": "string", "enumType": null}], "precision": null, "scale": null, "roundingMode": null, "maxLength": null, "isLocalized": null, "doNotTrim": null, "truncate": null, "values": null, "allowedContentTypes": null}, "targetNode": "@sage/xtrem-core/TestReferred", "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": false}, {"title": "Lines", "name": "lines", "canSort": false, "canFilter": false, "enumType": null, "type": "collection", "isCustom": false, "dataType": "testDocumentLine", "dataTypeDetails": {"node": "@sage/xtrem-core/TestDocumentLine", "type": "reference", "title": "Test document line", "value": {"bind": "_id", "title": "_id", "type": "integer", "enumType": null}, "helperText": {"bind": "_id", "title": "_id", "type": "integer", "enumType": null}, "isDefault": false, "columns": [{"bind": "_id", "title": "_id", "type": "integer", "enumType": null}], "precision": null, "scale": null, "roundingMode": null, "maxLength": null, "isLocalized": null, "doNotTrim": null, "truncate": null, "values": null, "allowedContentTypes": null}, "targetNode": "@sage/xtrem-core/TestDocumentLine", "isOnInputType": true, "isOnOutputType": true, "isMutable": true, "isSystemProperty": false}, {"title": "Computed lines", "name": "computedLines", "canSort": false, "canFilter": false, "enumType": null, "type": "collection", "isCustom": false, "dataType": "testDocumentLine", "dataTypeDetails": {"node": "@sage/xtrem-core/TestDocumentLine", "type": "reference", "title": "Test document line", "value": {"bind": "_id", "title": "_id", "type": "integer", "enumType": null}, "helperText": {"bind": "_id", "title": "_id", "type": "integer", "enumType": null}, "isDefault": false, "columns": [{"bind": "_id", "title": "_id", "type": "integer", "enumType": null}], "precision": null, "scale": null, "roundingMode": null, "maxLength": null, "isLocalized": null, "doNotTrim": null, "truncate": null, "values": null, "allowedContentTypes": null}, "targetNode": "@sage/xtrem-core/TestDocumentLine", "isOnInputType": false, "isOnOutputType": true, "isMutable": false, "isSystemProperty": false}, {"title": "Filtered lines", "name": "filteredLines", "canSort": false, "canFilter": false, "enumType": null, "type": "collection", "isCustom": false, "dataType": "testDocumentLine", "dataTypeDetails": {"node": "@sage/xtrem-core/TestDocumentLine", "type": "reference", "title": "Test document line", "value": {"bind": "_id", "title": "_id", "type": "integer", "enumType": null}, "helperText": {"bind": "_id", "title": "_id", "type": "integer", "enumType": null}, "isDefault": false, "columns": [{"bind": "_id", "title": "_id", "type": "integer", "enumType": null}], "precision": null, "scale": null, "roundingMode": null, "maxLength": null, "isLocalized": null, "doNotTrim": null, "truncate": null, "values": null, "allowedContentTypes": null}, "targetNode": "@sage/xtrem-core/TestDocumentLine", "isOnInputType": false, "isOnOutputType": true, "isMutable": false, "isSystemProperty": false}, {"title": "Filtered lines with reverse reference", "name": "filteredLinesWithReverseReference", "canSort": false, "canFilter": false, "enumType": null, "type": "collection", "isCustom": false, "dataType": "testDocumentLine", "dataTypeDetails": {"node": "@sage/xtrem-core/TestDocumentLine", "type": "reference", "title": "Test document line", "value": {"bind": "_id", "title": "_id", "type": "integer", "enumType": null}, "helperText": {"bind": "_id", "title": "_id", "type": "integer", "enumType": null}, "isDefault": false, "columns": [{"bind": "_id", "title": "_id", "type": "integer", "enumType": null}], "precision": null, "scale": null, "roundingMode": null, "maxLength": null, "isLocalized": null, "doNotTrim": null, "truncate": null, "values": null, "allowedContentTypes": null}, "targetNode": "@sage/xtrem-core/TestDocumentLine", "isOnInputType": false, "isOnOutputType": true, "isMutable": false, "isSystemProperty": false}, {"title": "Etag", "name": "_etag", "canSort": false, "canFilter": false, "enumType": null, "type": "string", "isCustom": false, "dataType": "_etagDataType", "dataTypeDetails": {"node": null, "type": "string", "title": "Etag data type", "value": null, "helperText": null, "isDefault": false, "columns": null, "precision": null, "scale": null, "roundingMode": null, "maxLength": 100, "isLocalized": false, "doNotTrim": false, "truncate": false, "values": null, "allowedContentTypes": null}, "targetNode": "", "isOnInputType": false, "isOnOutputType": true, "isMutable": false, "isSystemProperty": true}], "queries": [], "mutations": []}, "isOnInputType": true, "isOnOutputType": true, "isMutable": false, "isSystemProperty": false}, {"title": "Get description", "name": "getDescription", "canSort": true, "canFilter": true, "enumType": null, "type": "string", "isCustom": false, "dataType": "descriptionDataType", "dataTypeDetails": {"node": null, "type": "string", "title": "Description data type", "value": null, "helperText": null, "isDefault": false, "columns": null, "precision": null, "scale": null, "roundingMode": null, "maxLength": 250, "isLocalized": false, "doNotTrim": false, "truncate": false, "values": null, "allowedContentTypes": null}, "targetNode": "", "targetNodeDetails": null, "isOnInputType": false, "isOnOutputType": true, "isMutable": false, "isSystemProperty": false}, {"title": "Etag", "name": "_etag", "canSort": false, "canFilter": false, "enumType": null, "type": "string", "isCustom": false, "dataType": "_etagDataType", "dataTypeDetails": {"node": null, "type": "string", "title": "Etag data type", "value": null, "helperText": null, "isDefault": false, "columns": null, "precision": null, "scale": null, "roundingMode": null, "maxLength": 100, "isLocalized": false, "doNotTrim": false, "truncate": false, "values": null, "allowedContentTypes": null}, "targetNode": "", "targetNodeDetails": null, "isOnInputType": false, "isOnOutputType": true, "isMutable": false, "isSystemProperty": true}], "queries": [], "mutations": []}}}