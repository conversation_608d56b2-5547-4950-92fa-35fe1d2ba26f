import { ActivityExtension } from '../../../index';
import { testActivity } from '../activities/_index';
import { TestActivityRelated } from '../nodes';

export const testActivityExtension = new ActivityExtension({
    extends: testActivity,
    __filename: 'test-activity',
    permissions: ['read', 'create', 'update', 'delete', 'report2'],
    operationGrants: {
        lookup: [],
        update: [
            {
                operations: ['create'],
                on: [() => TestActivityRelated],
            },
        ],
        report2: [
            {
                operations: ['reportAExt'],
                on: [() => TestActivityRelated],
            },
        ],
    },
});
