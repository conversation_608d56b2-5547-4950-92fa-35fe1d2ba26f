import { Context, decorators, integer, NodeExtension, Reference } from '../../../index';
import { codeDataType } from '../data-types/data-types';
import { TestBase } from '../nodes/base';
import { TestExtensionReference } from '../nodes/test-extension-reference';

@decorators.nodeExtension<BaseExtension1>({
    extends: () => TestBase,
})
export class BaseExtension1 extends NodeExtension<TestBase> {
    @decorators.stringProperty<BaseExtension1, 'extensionCode1'>({
        isPublished: true,
        dataType: () => codeDataType,
        async getValue(): Promise<string> {
            return `${await this.code}Ext1`;
        },
    })
    readonly extensionCode1: Promise<string>;

    @decorators.referenceProperty<BaseExtension1, 'extensionReference1'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        node: () => TestExtensionReference,
        filters: {
            lookup: {
                refCode() {
                    return this.code;
                },
            },
        },
    })
    readonly extensionReference1: Reference<TestExtensionReference>;

    async extensionMethod1(this: TestBase, param: integer): Promise<string> {
        return `extensionMethod1 code=${await this.code}, extensionCode1=${await this.extensionCode1}, param=${param}`;
    }

    @decorators.mutation<typeof BaseExtension1, 'extensionOperation1'>({
        isPublished: true,
        parameters: [{ name: 'param', type: 'integer' }],
        return: 'string',
    })
    static extensionOperation1(_context: Context, param: integer): Promise<string> {
        return Promise.resolve(`extensionOperation param=${param}`);
    }
}

declare module '../nodes/base' {
    export interface TestBase extends BaseExtension1 {}
}
