import { NodeExtension, decorators } from '../../../index';
import { TestNotAllowedFrozenCallbackChaining } from '../nodes/frozen-not-allowed-callback';

@decorators.nodeExtension<TestNotAllowedFrozenCallbackChainingExtension>({
    extends: () => TestNotAllowedFrozenCallbackChaining,
})
export class TestNotAllowedFrozenCallbackChainingExtension extends NodeExtension<TestNotAllowedFrozenCallbackChaining> {
    @decorators.stringPropertyOverride<TestNotAllowedFrozenCallbackChainingExtension, 'valueFrozen'>({
        async isFrozen() {
            return (await this.callbackFrozen) === 'any';
        },
    })
    readonly valueFrozen: Promise<string>;
}

declare module './../nodes/frozen-not-allowed-callback' {
    export interface TestNotAllowedFrozenCallbackChaining extends TestNotAllowedFrozenCallbackChainingExtension {}
}
