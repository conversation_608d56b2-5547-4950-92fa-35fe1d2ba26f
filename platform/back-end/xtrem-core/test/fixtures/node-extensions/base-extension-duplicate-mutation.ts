import { Context, decorators, NodeExtension } from '../../../index';
import { TestBase } from '../nodes/base';

@decorators.nodeExtension<BaseExtensionDuplicateMutation>({
    extends: () => TestBase,
})
export class BaseExtensionDuplicateMutation extends NodeExtension<TestBase> {
    @decorators.mutation<typeof BaseExtensionDuplicateMutation, 'testCreate'>({
        isPublished: true,
        parameters: [{ name: 'code', type: 'string', isMandatory: true }],
        return: {
            type: 'instance',
            node(): typeof TestBase {
                return TestBase;
            },
        },
    })
    static async testCreate(context: Context, code: string): Promise<TestBase> {
        const base = await context.create(TestBase, { code });
        return base;
    }
}

declare module '../nodes/base' {
    export interface TestBase extends BaseExtensionDuplicateMutation {}
}
