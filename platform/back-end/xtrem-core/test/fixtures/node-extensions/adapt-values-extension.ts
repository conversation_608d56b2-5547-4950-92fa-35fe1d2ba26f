import { decorators, NodeExtension } from '../../../index';
import { TestAdaptValues } from '../nodes/adapt-values';

@decorators.nodeExtension<TestAdaptValuesExtension>({
    extends: () => TestAdaptValues,
})
export class TestAdaptValuesExtension extends NodeExtension<TestAdaptValues> {
    @decorators.stringPropertyOverride<TestAdaptValuesExtension, 'extendedAdaptValue'>({
        adaptValue(val: string): string {
            return `${val}_extended-property`;
        },
    })
    readonly extendedAdaptValue: Promise<string>;
}

declare module './../nodes/adapt-values' {
    export interface TestAdaptValues extends TestAdaptValuesExtension {}
}
