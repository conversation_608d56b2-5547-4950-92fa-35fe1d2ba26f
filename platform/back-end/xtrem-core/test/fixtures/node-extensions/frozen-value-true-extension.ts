import { NodeExtension, decorators } from '../../../index';
import { descriptionDataType } from '../data-types/data-types';
import { TestFrozenValueTrueChaining } from '../nodes/frozen-value-true';

@decorators.nodeExtension<TestFrozenValueTrueChainingExtension>({
    extends: () => TestFrozenValueTrueChaining,
})
export class TestFrozenValueTrueChainingExtension extends NodeExtension<TestFrozenValueTrueChaining> {
    @decorators.stringProperty<TestFrozenValueTrueChainingExtension, 'strFromOverrideNotFrozen'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly strFromOverrideNotFrozen: Promise<string>;

    @decorators.stringPropertyOverride<TestFrozenValueTrueChainingExtension, 'callbackFrozen'>({
        isFrozen: true,
    })
    readonly callbackFrozen: Promise<string>;

    @decorators.stringPropertyOverride<TestFrozenValueTrueChainingExtension, 'valueFrozen'>({})
    readonly valueFrozen: Promise<string>;

    @decorators.stringPropertyOverride<TestFrozenValueTrueChainingExtension, 'notFrozen'>({
        isFrozen: true,
    })
    readonly notFrozen: Promise<string>;
}

declare module './../nodes/frozen-value-true' {
    export interface TestFrozenValueTrueChaining extends TestFrozenValueTrueChainingExtension {}
}
