import { NodeExtension, decorators } from '../../../index';
import { TestNotAllowedFrozenValueChaining } from '../nodes/frozen-not-allowed-value';

@decorators.nodeExtension<TestNotAllowedFrozenValueChainingExtension>({
    extends: () => TestNotAllowedFrozenValueChaining,
})
export class TestNotAllowedFrozenValueChainingExtension extends NodeExtension<TestNotAllowedFrozenValueChaining> {
    @decorators.stringPropertyOverride<TestNotAllowedFrozenValueChainingExtension, 'valueFrozen'>({
        isFrozen: false,
    })
    readonly valueFrozen: Promise<string>;
}

declare module './../nodes/frozen-not-allowed-value' {
    export interface TestNotAllowedFrozenValueChaining extends TestNotAllowedFrozenValueChainingExtension {}
}
