import { Collection, decorators, integer, NodeExtension, Reference, useDefaultValue } from '../../../index';
import { codeDataType } from '../data-types/data-types';
import { TestBase, TestBaseCollectionElement, TestBaseReference } from '../nodes/base';

@decorators.nodeExtension<TestBaseExtension2>({
    extends: () => TestBase,
    async controlBegin(cx): Promise<void> {
        await cx.error.if(await this.controlled).is.equal.to('BAD3');
    },
})
export class TestBaseExtension2 extends NodeExtension<TestBase> {
    @decorators.stringPropertyOverride<TestBaseExtension2, 'controlled'>({
        defaultValue(): string {
            return 'GOOD2';
        },
        async control(cx): Promise<void> {
            await cx.error.if(await this.controlled).is.equal.to('BAD2');
        },
    })
    readonly controlled: Promise<string>;

    @decorators.stringPropertyOverride<TestBaseExtension2, 'dependsOn'>({
        dependsOn: ['dependedUpon'],
        async updatedValue(): Promise<string> {
            return `UPDATED2: ${await this.dependedUpon}/${await this.controlled}`;
        },
    })
    readonly dependsOn: Promise<string>;

    @decorators.stringPropertyOverride<TestBaseExtension2, 'dependsOn2'>({
        dependsOn: ['dependedUpon'],
        updatedValue() {
            return useDefaultValue;
        },
        async defaultValue(): Promise<string> {
            return `DEP2: ${await this.dependedUpon}/${await this.controlled}`;
        },
    })
    readonly dependsOn2: Promise<string>;

    @decorators.stringProperty<TestBaseExtension2, 'dependedUpon'>({
        dataType: () => codeDataType,
        isStored: true,
        defaultValue(): string {
            return 'DEP1';
        },
    })
    readonly dependedUpon: Promise<string>;

    @decorators.stringProperty<TestBaseExtension2, 'extensionCode2'>({
        dataType: () => codeDataType,
        isPublished: true,
        async getValue(): Promise<string> {
            return `${await this.code}Ext2`;
        },
    })
    readonly extensionCode2: Promise<string>;

    // Only to test typing
    @decorators.referencePropertyOverride<TestBaseExtension2, 'testBaseReference'>({
        dependsOn: ['code'],
        defaultValue() {
            return null;
        },
        updatedValue() {
            return null;
        },
    })
    readonly testBaseReference: Reference<TestBaseReference | null>;

    // Only to test typing
    @decorators.collectionPropertyOverride<TestBaseExtension2, 'testBaseCollection'>({
        dependsOn: ['code'],
    })
    readonly testBaseCollection: Collection<TestBaseCollectionElement>;

    async extensionMethod2(this: TestBase, param: integer): Promise<string> {
        return `extensionMethod2 code=${await this.code}, extensionCode2=${await this.extensionCode2}, param=${param}`;
    }
}

declare module '../nodes/base' {
    export interface TestBase extends TestBaseExtension2 {}
}
