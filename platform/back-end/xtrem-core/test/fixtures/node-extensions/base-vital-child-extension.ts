import { decorators, NodeExtension, Reference } from '../../../index';
import { TestBaseVitalChild } from '../nodes/base';
import { TestVitalReferenceExtensionParent } from '../nodes/vital-extension-reference-parent';

@decorators.nodeExtension<TestBaseVitalChildExtension>({
    extends: () => TestBaseVitalChild,
    isVitalReferenceChild: true,
})
export class TestBaseVitalChildExtension extends NodeExtension<TestBaseVitalChild> {
    @decorators.referenceProperty<TestBaseVitalChildExtension, 'parent'>({
        isStored: true,
        isPublished: true,
        node: () => TestVitalReferenceExtensionParent,
        isVitalParent: true,
    })
    readonly parent: Reference<TestVitalReferenceExtensionParent>;
}

declare module '../nodes/base' {
    export interface TestBaseVitalChild extends TestBaseVitalChildExtension {}
}
