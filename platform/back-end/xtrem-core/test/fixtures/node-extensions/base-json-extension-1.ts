import { Context, decorators, integer, NodeExtension } from '../../../index';
import { codeDataType } from '../data-types/data-types';
import { TestBaseJson } from '../nodes/base-json';

@decorators.nodeExtension<BaseJsonExtension1>({
    extends: () => TestBaseJson,
})
export class BaseJsonExtension1 extends NodeExtension<TestBaseJson> {
    @decorators.stringProperty<BaseJsonExtension1, 'extensionCode1'>({
        isPublished: true,
        dataType: () => codeDataType,
        async getValue(): Promise<string> {
            return `${await this.code}Ext1`;
        },
    })
    readonly extensionCode1: Promise<string>;

    @decorators.mutation<typeof BaseJsonExtension1, 'extensionOperation1'>({
        isPublished: true,
        parameters: [{ name: 'param', type: 'integer' }],
        return: 'string',
    })
    static extensionOperation1(context: Context, param: integer): string {
        return `extensionOperation param=${param}`;
    }
}

declare module '../nodes/base-json' {
    export interface TestBaseJson extends BaseJsonExtension1 {}
}
