import { NodeExtension, decorators } from '../../../index';
import { descriptionDataType } from '../data-types/data-types';
import { TestFrozenCallbackChaining } from '../nodes/frozen-callback';

@decorators.nodeExtension<TestFrozenCallbackChainingExtension>({
    extends: () => TestFrozenCallbackChaining,
})
export class TestFrozenCallbackChainingExtension extends NodeExtension<TestFrozenCallbackChaining> {
    @decorators.stringProperty<TestFrozenCallbackChainingExtension, 'strFromOverrideNotFrozen'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly strFromOverrideNotFrozen: Promise<string>;

    @decorators.stringPropertyOverride<TestFrozenCallbackChainingExtension, 'callbackFrozen'>({
        async isFrozen() {
            return (await this.callbackFrozen) === 'EXT-FROZEN';
        },
    })
    readonly callbackFrozen: Promise<string>;

    @decorators.stringPropertyOverride<TestFrozenCallbackChainingExtension, 'notFrozen'>({
        async isFrozen() {
            return (await this.notFrozen) === 'NOT-FROZEN';
        },
    })
    readonly notFrozen: Promise<string>;
}

declare module './../nodes/frozen-callback' {
    export interface TestFrozenCallbackChaining extends TestFrozenCallbackChainingExtension {}
}
