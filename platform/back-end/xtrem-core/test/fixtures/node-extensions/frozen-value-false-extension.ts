import { NodeExtension, decorators } from '../../../index';
import { descriptionDataType } from '../data-types/data-types';
import { TestFrozenValueFalseChaining } from '../nodes/frozen-value-false';

@decorators.nodeExtension<TestFrozenValueFalseChainingExtension>({
    extends: () => TestFrozenValueFalseChaining,
})
export class TestFrozenValueFalseChainingExtension extends NodeExtension<TestFrozenValueFalseChaining> {
    @decorators.stringProperty<TestFrozenValueFalseChainingExtension, 'strFromOverrideNotFrozen'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly strFromOverrideNotFrozen: Promise<string>;

    @decorators.stringPropertyOverride<TestFrozenValueFalseChainingExtension, 'callbackFrozen'>({
        isFrozen: false,
    })
    readonly callbackFrozen: Promise<string>;

    @decorators.stringPropertyOverride<TestFrozenValueFalseChainingExtension, 'valueFrozen'>({})
    readonly valueFrozen: Promise<string>;

    @decorators.stringPropertyOverride<TestFrozenValueFalseChainingExtension, 'notFrozen'>({
        isFrozen: false,
    })
    readonly notFrozen: Promise<string>;
}

declare module './../nodes/frozen-value-false' {
    export interface TestFrozenValueFalseChaining extends TestFrozenValueFalseChainingExtension {}
}
