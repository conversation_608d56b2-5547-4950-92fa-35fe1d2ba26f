import { decorators, integer, NodeExtension } from '../../../index';
import { codeDataType } from '../data-types/data-types';
import { TestBaseJson } from '../nodes/base-json';

@decorators.nodeExtension<TestBaseJsonExtension2>({
    extends: () => TestBaseJson,
})
export class TestBaseJsonExtension2 extends NodeExtension<TestBaseJson> {
    @decorators.stringProperty<TestBaseJsonExtension2, 'extensionCode2'>({
        dataType: () => codeDataType,
        isPublished: true,
        async getValue(): Promise<string> {
            return `${await this.code}Ext2`;
        },
    })
    readonly extensionCode2: Promise<string>;

    async extensionMethod2(this: TestBase<PERSON>son, param: integer): Promise<string> {
        return `extensionMethod2 code=${await this.code}, extensionCode2=${await this.extensionCode2}, param=${param}`;
    }
}

declare module '../nodes/base-json' {
    export interface TestBase<PERSON>son extends TestBaseJsonExtension2 {}
}
