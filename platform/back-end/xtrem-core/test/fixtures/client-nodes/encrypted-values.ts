import { ClientNode, ClientNodeInput, CreateOperation, QueryOperation, UpdateOperation } from '@sage/xtrem-client';

export interface TestEncryptedValues extends ClientNode {
    id: number;
    passwordValue: string;
}

export interface TestEncryptedValuesInput extends ClientNodeInput {
    id?: number;
    passwordValue?: string;
}

export interface TestEncryptedValues$Operations {
    query: QueryOperation<TestEncryptedValues>;
    create: CreateOperation<TestEncryptedValuesInput, TestEncryptedValues>;
    update: UpdateOperation<TestEncryptedValuesInput, TestEncryptedValues>;
}
