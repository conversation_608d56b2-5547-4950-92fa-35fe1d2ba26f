import { ClientNode, VitalClientNode, VitalClientNodeInput } from '@sage/xtrem-client';
import { ClientVitalReferenceParent } from './vital-reference-parent';

export interface ClientVitalReferenceChildMandatory extends VitalClientNode {
    parent: ClientVitalReferenceParent;
    code: string;
    text: string;
}

export interface ClientVitalReferenceChildMandatoryInput extends VitalClientNodeInput {
    code?: string;
    text?: string;
}

export interface ClientNonVitalReference extends ClientNode {
    code: string;
    text: string;
}

export interface ClientVitalReferenceChildOptional extends VitalClientNode {
    parent: ClientVitalReferenceParent;
    code: string;
    text: string;
    nonVitalRef: ClientNonVitalReference;
}

export interface ClientVitalReferenceChildOptionalInput extends VitalClientNodeInput {
    code?: string;
    text?: string;
    nonVitalRef?: number | string;
}
