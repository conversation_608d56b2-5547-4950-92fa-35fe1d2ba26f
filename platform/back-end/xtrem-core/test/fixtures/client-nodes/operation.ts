import {
    BinaryStream,
    ClientNode,
    ClientNodeInput,
    GetDefaultsOperation,
    GetDuplicateOperation,
    integer,
    Operation,
    TextStream,
} from '@sage/xtrem-client';
import { TestEnum } from './datatypes';

export interface TestOperationReference extends ClientNode {
    code: string;
    quantity: integer;
}
export interface TestOperationReferenceInput extends ClientNodeInput {
    code?: string;
    quantity?: integer | string;
}
export interface TestOperation extends ClientNode {
    code: string;
    value: integer;
}
export interface TestOperationInput extends ClientNodeInput {
    code?: string;
    value?: integer | string;
}
export interface TestOperation$Queries {
    queryReturningString: Operation<
        {
            code?: string;
            stringVal?: string;
            intVal?: integer;
            enumVal?: TestEnum;
        },
        string
    >;
    queryWithComplexInput: Operation<
        {
            object: {
                simple?: boolean;
                mandatory: string;
                nullable?: string | null;
            };
            optionalObjects?: {
                nestedStrings?: string[];
                flag?: boolean;
            }[];
        },
        string
    >;
    queryWithComplexOutput: Operation<
        {
            object: {
                simple?: boolean;
                mandatory: string;
                nullable?: string | null;
            };
            optionalObjects?: {
                nestedStrings?: string[];
                flag?: boolean;
            }[];
        },
        {
            object: {
                simple: boolean;
                mandatory: string;
                nullable: string | null;
            };
            optionalObjects: {
                nestedStrings: string[];
                flag: boolean;
            }[];
        }
    >;
    queryWithReferences: Operation<
        {
            reference?: string;
            nullableReference?: string | null;
            arrayOfReferences?: string[];
            arrayOfNullableReferences?: (string | null)[];
            nested?: {
                reference?: integer | string;
                nullableReference?: (integer | string) | null;
                arrayOfReferences?: string[];
                arrayOfNullableReferences?: (string | null)[];
            };
        },
        {
            reference: TestOperationReference;
            nullableReference: TestOperationReference | null;
            arrayOfReferences: TestOperationReference[];
            arrayOfNullableReferences: (TestOperationReference | null)[];
            nested: {
                reference: TestOperationReference;
                nullableReference: TestOperationReference | null;
                arrayOfReferences: TestOperationReference[];
                arrayOfNullableReferences: (TestOperationReference | null)[];
            };
        }
    >;
    queryReturningSimpleArray: Operation<
        {
            len?: integer | string;
        },
        string[]
    >;
    queryReturningArrayOfObjects: Operation<
        {
            len?: integer | string;
        },
        {
            index: integer;
            text: string;
        }[]
    >;
    queryWithOptionalArgs: Operation<
        {
            option1?: string;
            option2?: string;
            option3?: string;
        },
        string
    >;
}
export interface TestOperation$Mutations {
    mutationReturningString: Operation<
        {
            code?: string;
            stringVal?: string;
            intVal?: integer;
        },
        string
    >;
    mutationReturningNode: Operation<
        {
            code: string;
            intVal?: integer;
        },
        TestOperation
    >;
    mutationWithNodeParameter: Operation<
        {
            arg: TestOperationInput;
        },
        TestOperation
    >;
    mutationArrayOfInstanceReturningString: Operation<
        {
            instanceArray?: TestOperationInput[];
        },
        string
    >;
    mutationWithBinaryStreamAsResult: Operation<
        {
            binaryContent: string;
        },
        BinaryStream
    >;
    mutationWithTextStreamAsResult: Operation<
        {
            textContent: string;
        },
        TextStream
    >;
    mutationWithNonWritableReferenceParameter: Operation<
        {
            reference: string;
        },
        string
    >;
    mutationWithWritableReferenceParameter: Operation<
        {
            reference: string;
        },
        string
    >;
    mutationStartedReadonlyRunningWithWritable: Operation<
        {
            reference: string;
        },
        string
    >;
    mutationWithoutParameters: Operation<{}, string>;
}

export interface TestOperation$Operations {
    queries: TestOperation$Queries;
    mutations: TestOperation$Mutations;
    getDefaults: GetDefaultsOperation<TestOperation>;
    getDuplicate: GetDuplicateOperation<TestOperation>;
}
