import { Client<PERSON>ollection, ClientNode, CreateOperation, QueryOperation, UpdateByIdOperation } from '@sage/xtrem-client';
import { ClientDocumentLine, DocumentLineInput } from './document-line';
import { TestReferredInterface } from './referred';

export interface ClientDocument extends ClientNode {
    code: string;
    description: string;
    mandatoryReference: TestReferredInterface;
    lines: ClientCollection<ClientDocumentLine>;
}

export interface DocumentKey {
    code: string;
}

export interface DocumentInput extends DocumentKey {
    description?: string;
    mandatoryReference?: number;
    lines?: Partial<DocumentLineInput>[];
}

export interface ClientDocument$Operations {
    query: QueryOperation<ClientDocument>;
    create: CreateOperation<DocumentInput, ClientDocument>;
    updateById: UpdateByIdOperation<DocumentInput, ClientDocument>;
}
