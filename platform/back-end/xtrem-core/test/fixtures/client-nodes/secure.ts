import { ClientNode, QueryOperation, ReadOperation } from '@sage/xtrem-client';

export interface TestSite extends ClientNode {
    code: string;
}
export interface TestSecure extends ClientNode {
    code: string;
    site: TestSite | null;
    access: string;
}

export interface SecureKey {
    code: string;
}

export interface SecureInput extends SecureKey {
    site?: string | null;
}

export interface TestSecure$Operations {
    query: QueryOperation<TestSecure>;
    read: ReadOperation<TestSecure>;
}

export interface TestSite$Operations {
    query: QueryOperation<TestSite>;
    read: ReadOperation<TestSite>;
}
