import {
    ClientNode,
    CreateOperation,
    QueryOperation,
    ReadOperation,
    UpdateByIdOperation,
    UpdateOperation,
} from '@sage/xtrem-client';
import {
    ClientVitalReferenceChildMandatory,
    ClientVitalReferenceChildMandatoryInput,
    ClientVitalReferenceChildOptional,
    ClientVitalReferenceChildOptionalInput,
} from './vital-reference-child';

export interface ClientVitalReferenceParent extends ClientNode {
    code: string;
    mandatoryVitalRef: ClientVitalReferenceChildMandatory;
    optionalVitalRef: ClientVitalReferenceChildOptional | null;
}

export interface ClientVitalReferenceParentInput {
    _id?: string;
    code?: string;
    mandatoryVitalRef?: ClientVitalReferenceChildMandatoryInput;
    optionalVitalRef?: ClientVitalReferenceChildOptionalInput | null;
}

export interface ClientVitalReferenceParent$Operations {
    query: QueryOperation<ClientVitalReferenceParent>;
    read: ReadOperation<ClientVitalReferenceParent>;
    create: CreateOperation<ClientVitalReferenceParentInput, ClientVitalReferenceParent>;
    update: UpdateOperation<ClientVitalReferenceParentInput, ClientVitalReferenceParent>;
    updateById: UpdateByIdOperation<ClientVitalReferenceParentInput, ClientVitalReferenceParent>;
}
