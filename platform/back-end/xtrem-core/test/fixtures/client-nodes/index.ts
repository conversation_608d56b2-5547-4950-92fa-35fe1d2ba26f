import { TestDocumentLookup$Operations } from '@sage/test-app-api';
import { ClientAggDocument$Operations } from './agg-document';
import { ClientAggDocumentLine$Operations } from './agg-document-line';
import { TestDatatypesInterface$Operations } from './datatypes';
import { ClientDocument$Operations } from './document';
import { ClientDocumentLine$Operations } from './document-line';
import { TestEncryptedValues$Operations } from './encrypted-values';
import { TestFrozen$Operations } from './frozen';
import { TestOperation$Operations } from './operation';
import {
    TestProvidesChild$Operations,
    TestProvidesChildSite$Operations,
    TestProvidesGrandChild$Operations,
    TestProvidesGrandChildSite$Operations,
    TestProvidesParent$Operations,
    TestProvidesParentNoSite$Operations,
    TestProvidesSite$Operations,
} from './provides';
import { TestReferringInterface$Operations } from './referring';
import { TestSecure$Operations, TestSite$Operations } from './secure';
import {
    TestAnimal$Operations,
    TestCat$Operations,
    TestDog$Operations,
    TestFish$Operations,
    TestMammal$Operations,
} from './subclassing';
import { TestTransient$Operations } from './transient';
import { ClientVitalCollectionParent$Operations } from './vital-collection-parent';
import { ClientVitalReferenceParent$Operations } from './vital-reference-parent';

export * from './agg-document';
export * from './agg-document-line';
export * from './datatypes';
export * from './document';
export * from './document-line';
export * from './encrypted-values';
export * from './frozen';
export * from './operation';
export * from './provides';
export * from './referred';
export * from './referring';
export * from './secure';
export * from './subclassing';
export * from './transient';

export interface Test$Package {
    '@sage/xtrem-core/testDatatypes': TestDatatypesInterface$Operations;
    '@sage/xtrem-core/testDocument': ClientDocument$Operations;
    '@sage/xtrem-core/testDocumentLine': ClientDocumentLine$Operations;
    '@sage/xtrem-core/testAggDocument': ClientAggDocument$Operations;
    '@sage/xtrem-core/testAggDocumentLine': ClientAggDocumentLine$Operations;
    '@sage/xtrem-core/testEncryptedValues': TestEncryptedValues$Operations;
    '@sage/xtrem-core/testFrozen': TestFrozen$Operations;
    '@sage/xtrem-core/testOperation': TestOperation$Operations;
    '@sage/xtrem-core/testReferring': TestReferringInterface$Operations;
    '@sage/xtrem-core/testSite': TestSite$Operations;
    '@sage/xtrem-core/testSecure': TestSecure$Operations;
    '@sage/xtrem-core/testDocumentLookup': TestDocumentLookup$Operations;
    '@sage/xtrem-core/testTransient': TestTransient$Operations;
    '@sage/xtrem-core/testVitalCollectionParent': ClientVitalCollectionParent$Operations;
    '@sage/xtrem-core/testVitalReferenceParent': ClientVitalReferenceParent$Operations;
    '@sage/xtrem-core/testAnimal': TestAnimal$Operations;
    '@sage/xtrem-core/testMammal': TestMammal$Operations;
    '@sage/xtrem-core/testDog': TestDog$Operations;
    '@sage/xtrem-core/testCat': TestCat$Operations;
    '@sage/xtrem-core/testFish': TestFish$Operations;
    '@sage/xtrem-core/testProvidesSite': TestProvidesSite$Operations;
    '@sage/xtrem-core/testProvidesParent': TestProvidesParent$Operations;
    '@sage/xtrem-core/testProvidesChild': TestProvidesChild$Operations;
    '@sage/xtrem-core/testProvidesGrandChild': TestProvidesGrandChild$Operations;
    '@sage/xtrem-core/testProvidesParentNoSite': TestProvidesParentNoSite$Operations;
    '@sage/xtrem-core/testProvidesChildSite': TestProvidesChildSite$Operations;
    '@sage/xtrem-core/testProvidesGrandChildSite': TestProvidesGrandChildSite$Operations;
}

export interface GraphApi extends Test$Package {}
