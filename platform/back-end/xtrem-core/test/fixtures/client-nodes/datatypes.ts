import {
    BinaryStream,
    ClientNode,
    CreateOperation,
    DeleteOperation,
    integer,
    QueryOperation,
    ReadOperation,
    short,
    TextStream,
    UpdateByIdOperation,
} from '@sage/xtrem-client';

export type TestEnum = 'value1' | 'value2' | 'value3';

export type TestEnumForArray = 'arrayVal1' | 'arrayVal2' | 'arrayVal3';

export interface TestDatatypesInterface extends ClientNode {
    id: integer;
    booleanVal: boolean | null;
    shortVal: short;
    integerVal: integer;
    integerRangeVal: string | null;
    decimalRangeVal: string | null;
    enumVal: TestEnum | null;
    stringVal: string;
    stringArrayVal: string[] | null;
    decimalVal: string;
    floatVal: number;
    doubleVal: number;
    dateVal: string | null; // date
    dateRangeVal: string | null;
    datetimeRangeVal: string | null;
    timeVal: string | null; // time
    datetimeVal: string | null; // datetime
    binaryStream: BinaryStream | null;
    textStream: TextStream | null;
    uuidVal: string;
    computed: integer;
    computedCached: integer;
    complexComputed: integer;
    jsonVal: any;
    integerArrayVal: integer[] | null;
    enumArrayVal: TestEnumForArray[] | null;
}

export interface DatatypesKey {
    id: integer;
}

export interface DatatypesInput extends DatatypesKey {
    booleanVal?: boolean;
    shortVal?: short | string;
    integerVal?: integer | string;
    integerRangeVal?: string | null;
    decimalRangeVal?: string | null;
    enumVal?: TestEnum;
    stringVal?: string;
    stringArrayVal?: string[];
    decimalVal?: number | string;
    floatVal?: number | string;
    doubleVal?: number | string;
    dateVal?: string | null; // date
    timeVal?: string | null; // time
    datetimeVal?: string | null; // datetime
    binaryStream?: BinaryStream | null;
    textStream?: TextStream | null;
    uuidVal?: string;
    integerArrayVal?: (integer | string)[];
    enumArrayVal?: TestEnumForArray[];
    computed?: integer;
    computedCached?: integer;
}

export interface DatatypesIdInput extends DatatypesInput {
    _id: string;
}

export interface TestDatatypesInterface$Operations {
    query: QueryOperation<TestDatatypesInterface>;
    read: ReadOperation<TestDatatypesInterface>;
    create: CreateOperation<DatatypesInput, TestDatatypesInterface>;
    updateById: UpdateByIdOperation<DatatypesInput, TestDatatypesInterface>;
    deleteById: DeleteOperation<string>;
}
