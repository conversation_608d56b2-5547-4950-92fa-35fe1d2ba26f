import {
    C<PERSON><PERSON>ollection,
    ClientNode,
    CreateOperation,
    QueryOperation,
    ReadOperation,
    UpdateByIdOperation,
} from '@sage/xtrem-client';
import { ClientVitalCollectionChild, ClientVitalCollectionChildInput } from './vital-collection-child';

export interface ClientVitalCollectionParent extends ClientNode {
    code: string;
    children: ClientCollection<ClientVitalCollectionChild>;
}

export interface ClientVitalCollectionParentInput {
    _id?: string;
    code?: string;
    children?: Partial<ClientVitalCollectionChildInput>[];
}

export interface ClientVitalCollectionParent$Operations {
    query: QueryOperation<ClientVitalCollectionParent>;
    read: ReadOperation<ClientVitalCollectionParent>;
    create: CreateOperation<ClientVitalCollectionParentInput, ClientVitalCollectionParent>;
    update: UpdateByIdOperation<ClientVitalCollectionParentInput, ClientVitalCollectionParent>;
}
