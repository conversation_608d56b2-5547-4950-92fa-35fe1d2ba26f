import { ClientNode, integer, QueryOperation } from '@sage/xtrem-client';
import { ClientDocument } from './document';
import { TestReferredInterface } from './referred';

export interface ClientDocumentLine extends ClientNode {
    document: ClientDocument;
    lineNumber: integer;
    description: string;
    getDescription: string;
    optionalReference?: TestReferredInterface | null;
}

export interface DocumentLineKey {
    document: string;
    lineNumber: integer | string;
}

export interface DocumentLineInput extends DocumentLineKey {
    description?: string;
    optionalReference?: string | null;
}

export interface ClientDocumentLine$Operations {
    query: QueryOperation<ClientDocumentLine>;
}
