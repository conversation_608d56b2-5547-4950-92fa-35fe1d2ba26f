import { ClientNode, QueryOperation, ReadOperation } from '@sage/xtrem-client';

export interface TestProvidesSite extends ClientNode {
    code: string;
}
export interface TestProvidesParent extends ClientNode {
    code: string;
    site: TestProvidesSite | null;
}

export interface TestProvidesChild extends ClientNode {
    parent: TestProvidesParent;
    code: string;
    children: TestProvidesGrandChild[];
}

export interface TestProvidesGrandChild extends ClientNode {
    parent: TestProvidesChild;
    code: string;
}

export interface TestProvidesParentNoSite extends ClientNode {
    code: string;
}

export interface TestProvidesChildSite extends ClientNode {
    parent: TestProvidesParentNoSite;
    code: string;
    site: TestProvidesSite;
}

export interface TestProvidesGrandChildSite extends ClientNode {
    parent: TestProvidesChildSite;
    code: string;
}

export interface TestProvidesSite$Operations {
    query: QueryOperation<TestProvidesSite>;
    read: ReadOperation<TestProvidesSite>;
}

export interface TestProvidesParent$Operations {
    query: QueryOperation<TestProvidesParent>;
    read: ReadOperation<TestProvidesParent>;
}

export interface TestProvidesChild$Operations {
    query: QueryOperation<TestProvidesChild>;
    read: ReadOperation<TestProvidesChild>;
}

export interface TestProvidesGrandChild$Operations {
    query: QueryOperation<TestProvidesGrandChild>;
    read: ReadOperation<TestProvidesGrandChild>;
}

export interface TestProvidesParentNoSite$Operations {
    query: QueryOperation<TestProvidesParentNoSite>;
    read: ReadOperation<TestProvidesParentNoSite>;
}

export interface TestProvidesChildSite$Operations {
    query: QueryOperation<TestProvidesChildSite>;
    read: ReadOperation<TestProvidesChildSite>;
}

export interface TestProvidesGrandChildSite$Operations {
    query: QueryOperation<TestProvidesGrandChildSite>;
    read: ReadOperation<TestProvidesGrandChildSite>;
}
