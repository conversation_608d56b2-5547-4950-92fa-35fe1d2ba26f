import { ClientN<PERSON>, C<PERSON>Operation, DeleteOperation, QueryOperation } from '@sage/xtrem-client';
import { TestReferredInterface } from './referred';

export interface TestReferringInterface extends ClientNode {
    code: string;
    description: string;
    reference?: TestReferredInterface | null;
    restricted?: string;
    referenceArray?: TestReferredInterface[] | null;
}

export interface TestReferringInput {
    code?: string;
    description?: string;
    reference?: string | null;
    restricted?: string;
    referenceArray?: string[] | null;
}

export interface TestMandatoryReferring extends ClientNode {
    code: string;
    description: string;
    reference: TestReferredInterface;
}

export interface TestReferringInterface$Operations {
    query: QueryOperation<TestReferringInterface>;
    create: CreateOperation<TestReferringInput, TestReferringInterface>;
    delete: DeleteOperation<{}>;
}
