import {
    ClientNode,
    CreateOperation,
    DeleteOperation,
    integer,
    QueryOperation,
    ReadOperation,
    UpdateByIdOperation,
    UpdateOperation,
} from '@sage/xtrem-client';

export type TestEnum = 'value1' | 'value2' | 'value3';

export interface ArraysClientNode extends ClientNode {
    id: integer;
    booleanValues: (boolean | null)[];
    integerValues: integer[];
    enumValues: (TestEnum | null)[];
    stringValues: string[];
    decimalValues: string[];
    floatValues: number[];
    doubleValues: number[];
    dateValues: (string | null)[];
    datetimeValues: (string | null)[];
    uuidValues: (string | null)[];
}

export interface ArraysClientKey {
    id: integer;
}

export interface ArraysClientInput extends ArraysClientKey {
    booleanValues: (boolean | null)[];
    integerValues: (integer | string)[];
    enumValues: (TestEnum | null)[];
    stringValues: string[];
    decimalValues: (number | string)[];
    floatValues: (number | string)[];
    doubleValues: (number | string)[];
    dateValues: (string | null)[];
    datetimeValues: (string | null)[];
    uuidValues: (string | null)[];
}

export interface ArraysClientIdInput extends ArraysClientInput {
    _id: string;
}

export interface Arrays$Operations {
    query: QueryOperation<ArraysClientNode>;
    read: ReadOperation<ArraysClientNode>;
    create: CreateOperation<ArraysClientInput, ArraysClientNode>;
    update: UpdateOperation<ArraysClientInput, ArraysClientNode>;
    updateById: UpdateByIdOperation<ArraysClientInput, ArraysClientNode>;
    delete: DeleteOperation<ArraysClientKey>;
    deleteById: DeleteOperation<string>;
}
