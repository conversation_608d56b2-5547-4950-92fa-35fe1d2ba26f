import { ClientNode, ClientNodeInput, QueryOperation, ReadOperation } from '@sage/xtrem-client';

export interface TestAnimal extends ClientNode {
    strFromAnimal: string;
    _constructor: string;
}

export interface TestAnimalTypeInput extends ClientNodeInput {
    strFromAnimal: string;
}

export interface TestMammal extends ClientNode {
    strFromAnimal: string;
    strFromMammal: string;
    _constructor: string;
}

export interface TestDog extends ClientNode {
    strFromAnimal: string;
    strFromMammal: string;
    strFromDog: string;
}

export interface TestCat extends ClientNode {
    strFromAnimal: string;
    strFromMammal: string;
    strFromCat: string;
}

export interface TestFish extends ClientNode {
    strFromAnimal: string;
    strFromMammal: string;
    strFromFish: string;
}

export interface TestAnimal$Operations {
    query: QueryOperation<TestAnimal>;
    read: ReadOperation<TestAnimal>;
}

export interface TestMammal$Operations {
    query: QueryOperation<TestMammal>;
    read: ReadOperation<TestMammal>;
}

export interface TestDog$Operations {
    query: QueryOperation<TestDog>;
    read: ReadOperation<TestDog>;
}

export interface TestCat$Operations {
    query: QueryOperation<TestCat>;
    read: ReadOperation<TestCat>;
}

export interface TestFish$Operations {
    query: QueryOperation<TestFish>;
    read: ReadOperation<TestFish>;
}
