import {
    AggregateQueryOperation,
    AggregateReadOperation,
    ClientNode,
    integer,
    QueryOperation,
} from '@sage/xtrem-client';
import { ClientAggDocument } from './agg-document';

export interface ClientAggDocumentLine extends ClientNode {
    document: ClientAggDocument;
    date: string;
    lineNumber: integer;
    quantity: integer;
    amount1: string;
}

export interface ClientAggDocumentLine$Operations {
    query: QueryOperation<ClientAggDocumentLine>;
    aggregate: {
        read: AggregateReadOperation<ClientAggDocumentLine>;
        query: AggregateQueryOperation<ClientAggDocumentLine>;
    };
}
