import {
    ClientNode,
    CreateOperation,
    DeleteOperation,
    integer,
    Query,
    QueryOperation,
    ReadOperation,
    UpdateByIdOperation,
} from '@sage/xtrem-client';

export interface TestTransient extends ClientNode {
    stringValue: string;
    derivedValue: string;
    derivedFromRef: string;
    derivedFromCollection: integer;
    derivedCollection: Query<TestTransientLines>[];
}

export interface TestTransientKey {}

export interface TestTransientInput extends TestTransientKey {
    stringValue: string;
    transientValue?: string;
    derivedValue?: string;
    derivedFromRef?: string;
    derivedFromCollection?: integer;
    derivedCollection?: Partial<TransientLinesInput>[];
    transientRef?: integer | string;
    transientLines?: Partial<TransientLinesInput>[];
}

export interface TestTransientIdInput extends TestTransientInput {
    _id: string;
}

export interface TestTransient$Operations {
    query: QueryOperation<TestTransient>;
    read: ReadOperation<TestTransient>;
    create: CreateOperation<TestTransientInput, TestTransient>;
    updateById: UpdateByIdOperation<TestTransientInput, TestTransient>;
    deleteById: DeleteOperation<string>;
}

export interface TestTransientLines extends ClientNode {
    stringValue: string;
    parent: TestTransient;
}

export interface TransientLinesKey {
    _id: string;
}

export interface TransientLinesInput extends TransientLinesKey {
    stringValue: string;
    parent: integer | string;
}

export interface TestTransientLines$Operations {
    query: QueryOperation<TestTransientLines>;
    read: ReadOperation<TestTransientLines>;
    create: CreateOperation<TransientLinesInput, TestTransientLines>;
    updateById: UpdateByIdOperation<TransientLinesInput, TestTransientLines>;
    deleteById: DeleteOperation<string>;
}
