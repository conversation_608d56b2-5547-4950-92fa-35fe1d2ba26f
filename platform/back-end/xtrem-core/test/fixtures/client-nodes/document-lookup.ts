declare module '@sage/test-app-api' {
    import { ClientNode, CreateOperation, integer, QueryOperation } from '@sage/xtrem-client';

    export type StatusEnum = 'draft' | 'reviewed' | 'accepted';
    export interface TestReferencedDocument extends ClientNode {
        lookupString: string;
        controlInteger: integer;
        controlDetails: TestReferencedDocumentDetails | null;
    }
    export interface TestReferencedDocumentKey {}
    export interface TestReferencedDocumentInput extends TestReferencedDocumentKey {
        lookupString?: string;
        controlInteger?: integer;
        controlDetails?: TestReferencedDocumentDetails | string | null;
    }
    export interface TestReferencedDocumentOther extends ClientNode {
        lookupString: string;
        controlInteger: integer;
    }
    export interface TestReferencedDocumentOtherKey {}
    export interface TestReferencedDocumentOtherInput extends TestReferencedDocumentOtherKey {
        lookupString?: string;
        controlInteger?: integer;
    }
    export interface TestReferencedDocumentDetails extends ClientNode {
        text: string;
    }
    export interface TestReferencedDocumentDetailsKey {}
    export interface TestReferencedDocumentDetailsInput extends TestReferencedDocumentDetailsKey {
        text?: string;
    }

    export interface TestDocumentLookup extends ClientNode {
        lookupString: string;
        parentControlInteger: integer;
        reference: TestReferencedDocument;
        noControlReference: TestReferencedDocument;
        noLookupReference: TestReferencedDocument;
        noFiltersReference: TestReferencedDocument;
        otherReference: TestReferencedDocumentOther;
        details: TestReferencedDocumentDetails | null;
        controlledByDetailsReference: TestReferencedDocument | null;
        referenceArrayLookup: TestReferencedDocument[];
        referenceArrayControl: TestReferencedDocument[];
    }
    export interface TestDocumentLookupKey {}
    export interface TestDocumentLookupInput extends TestDocumentLookupKey {
        lookupString?: string;
        parentControlInteger?: integer;
        reference?: TestReferencedDocument;
        noControlReference?: TestReferencedDocument;
        noLookupReference?: TestReferencedDocument;
        noFiltersReference?: TestReferencedDocument;
        otherReference?: TestReferencedDocumentOther;
        details?: TestReferencedDocumentDetails | string | null;
        controlledByDetailsReference?: TestReferencedDocument | string | null;
        referenceArrayLookup?: (integer | string)[];
        referenceArrayControl?: (integer | string)[];
    }
    export interface TestDocumentLookup$Lookups {
        reference: QueryOperation<TestReferencedDocument>;
        noControlReference: QueryOperation<TestReferencedDocument>;
        noLookupReference: QueryOperation<TestReferencedDocument>;
        noFiltersReference: QueryOperation<TestReferencedDocument>;
        otherReference: QueryOperation<TestReferencedDocumentOther>;
    }
    export interface TestDocumentLookup$Operations {
        create: CreateOperation<TestDocumentLookupInput, TestDocumentLookup>;
        lookups(dataOrId: string | { data: TestDocumentLookupInput }): TestDocumentLookup$Lookups;
    }
    export interface Package {
        '@sage/test-app/TestDocumentLookup': TestDocumentLookup$Operations;
    }
    export interface GraphApi extends Package {}
}
