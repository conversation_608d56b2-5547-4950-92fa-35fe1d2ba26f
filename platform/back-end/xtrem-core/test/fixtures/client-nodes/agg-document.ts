import { Client<PERSON>ollection, ClientNode, QueryOperation } from '@sage/xtrem-client';
import { ClientAggDocumentLine } from './agg-document-line';

export interface ClientAggDocument extends ClientNode {
    code: string;
    lines: ClientCollection<ClientAggDocumentLine>;
}

export interface AggDocumentKey {
    code: string;
}

export interface ClientAggDocument$Operations {
    query: QueryOperation<ClientAggDocument>;
}
