export interface TestStudentData {
    _id: number;
    name: string;
}

export const testStudentData: TestStudentData[] = [
    {
        _id: 1,
        name: '<PERSON>',
    },
    {
        _id: 2,
        name: '<PERSON>',
    },
    {
        _id: 3,
        name: '<PERSON>',
    },
];

export interface TestCourseData {
    _id: number;
    name: string;
}

export const testCourseData: TestCourseData[] = [
    {
        _id: 1,
        name: 'Math',
    },
    {
        _id: 2,
        name: 'Science',
    },
    {
        _id: 3,
        name: 'English',
    },
];

export interface TestCampusData {
    _id: number;
    name: string;
}

export const testCampusData: TestCampusData[] = [
    {
        _id: 1,
        name: 'Main',
    },
    {
        _id: 2,
        name: 'West',
    },
    {
        _id: 3,
        name: 'East',
    },
];
export interface AssociationVitalParentData {
    _id: number;
    code: string;
    text: string;
}

export const associationVitalParentData: AssociationVitalParentData[] = [
    {
        _id: 1,
        code: 'P1',
        text: 'Parent 1',
    },
    {
        _id: 2,
        code: 'P2',
        text: 'Parent 2',
    },
    {
        _id: 3,
        code: 'P3',
        text: 'Parent 3',
    },
];

export interface AssociationAssociatedData {
    _id: number;
    code: string;
    text: string;
}

export const associationAssociatedData: AssociationAssociatedData[] = [
    {
        _id: 1,
        code: 'A1',
        text: 'Associated 1',
    },
    {
        _id: 2,
        code: 'A2',
        text: 'Associated 2',
    },
    {
        _id: 3,
        code: 'A3',
        text: 'Associated 3',
    },
];

export interface TestReferenceData {
    _id: number;
    name: string;
}

export const testReferenceData: TestReferenceData[] = [
    {
        _id: 1,
        name: 'Ref 1',
    },
    {
        _id: 2,
        name: 'Ref 2',
    },
    {
        _id: 3,
        name: 'Ref 3',
    },
];

export interface TestCollectionData {
    _id: number;
    name: string;
}

export const testCollectionData: TestCollectionData[] = [
    {
        _id: 1,
        name: 'Coll 1',
    },
    {
        _id: 2,
        name: 'Coll 2',
    },
    {
        _id: 3,
        name: 'Coll 3',
    },
];
