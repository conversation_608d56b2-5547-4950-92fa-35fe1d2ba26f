export const documentData = [
    {
        _id: 1,
        code: 'DOCA',
        description: 'document A',
        mandatoryReference: 1,
    },
    {
        _id: 2,
        code: 'DOCB',
        description: 'document B',
        mandatoryReference: 2,
    },
    {
        _id: 3,
        code: 'DOCC',
        description: 'document C',
        mandatoryReference: 1,
    },
    {
        _id: 4,
        code: 'DOCD',
        description: 'document D',
        mandatoryReference: 1,
    },
    {
        _id: 5,
        code: 'DOCE',
        description: 'document E',
        mandatoryReference: 1,
    },
    {
        _id: 6,
        code: 'TEST_DELETE_LINE',
        description: 'document for line deletion',
        mandatoryReference: 1,
    },
];

export const documentLineData = [
    {
        _id: 1,
        _sortValue: 10,
        document: 1,
        lineNumber: 1,
        description: 'line A 1',
    },
    {
        _id: 2,
        _sortValue: 10,
        document: 2,
        lineNumber: 1,
        description: 'line B 1',
        optionalReference: 1,
    },
    {
        _id: 3,
        _sortValue: 20,
        document: 2,
        lineNumber: 2,
        description: 'line B 2',
    },
    {
        _id: 4,
        _sortValue: 10,
        document: 3,
        lineNumber: 1,
        description: 'x',
    },
    {
        _id: 5,
        _sortValue: 20,
        document: 3,
        lineNumber: 2,
        description: 'y',
    },
    {
        _id: 6,
        _sortValue: 30,
        document: 3,
        lineNumber: 3,
        description: 'z',
    },
    {
        _id: 7,
        _sortValue: 40,
        document: 3,
        lineNumber: 4,
        description: 'y',
    },
    {
        _id: 8,
        _sortValue: 50,
        document: 3,
        lineNumber: 5,
        description: 'y',
    },
    {
        _id: 9,
        _sortValue: 10,
        document: 4,
        lineNumber: 1,
        description: 'line D 1',
    },
    {
        _id: 10,
        _sortValue: 20,
        document: 4,
        lineNumber: 2,
        description: 'line D 2',
    },
    {
        _id: 11,
        _sortValue: 10,
        document: 5,
        lineNumber: 1,
        description: 'line E 1',
    },
    {
        _id: 12,
        _sortValue: 20,
        document: 5,
        lineNumber: 2,
        description: 'line E 2',
    },
    {
        _id: 13,
        _sortValue: 10,
        document: 6,
        lineNumber: 1,
        description: 'remaining line #1',
    },
    {
        _id: 14,
        _sortValue: 20,
        document: 6,
        lineNumber: 2,
        description: 'line to be deleted',
    },
    {
        _id: 15,
        _sortValue: 30,
        document: 6,
        lineNumber: 3,
        description: 'remaining line #2',
    },
];
