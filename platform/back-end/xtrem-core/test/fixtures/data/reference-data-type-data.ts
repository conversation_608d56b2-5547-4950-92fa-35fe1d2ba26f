import { TestChildReferenceDataType, TestGrandChildReferenceDataType, TestReferenceDataType } from '../nodes/index';
import { TestInitData } from '../util';

export type GrandChildRefData = TestInitData<TestGrandChildReferenceDataType>;
export type ChildRefData = TestInitData<TestChildReferenceDataType>;
export type ParentRefData = TestInitData<TestReferenceDataType>;

export const grandChildRefData: GrandChildRefData[] = [
    {
        _id: 1,
        grandChildCode: 'GC01',
    },
    {
        _id: 2,
        grandChildCode: 'GC02',
    },
];

export const childRefData: ChildRefData[] = [
    {
        _id: 1,
        childCode: 'C01',
        grandChild: 1,
    },
    {
        _id: 2,
        childCode: 'C02',
        grandChild: 2,
    },
];

export const parentRefData: ParentRefData[] = [
    {
        _id: 1,
        code: 'P01',
        name: 'Parent 1',
        noLookup: 'No lookup 1',
        child: 1,
    },
    {
        _id: 2,
        code: 'P02',
        name: 'Parent 2',
        noLookup: 'No lookup 2',
        child: 2,
    },
];
