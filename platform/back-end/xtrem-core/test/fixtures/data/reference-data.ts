import { NodeCreateData } from '../../../lib';
import { TestReferred, TestReferring, TestReferringWithJoin } from '../nodes/index';
import { TestInitData } from '../util';

export type ReferredData = TestInitData<TestReferred>;
export type ReferringData = TestInitData<TestReferring>;
export type ReferringWithJoinData = TestInitData<TestReferringWithJoin>;

export const referringData: ReferringData[] = [
    {
        _id: 1,
        code: 'PAR1',
        description: 'referring 1',
        reference: 1,
        restricted: 'restricted1',
        referenceArray: [1, 1],
    },
    {
        _id: 2,
        code: 'PAR2',
        description: 'referring 2',
        reference: 2,
        restricted: 'restricted2',
        referenceArray: [1, 2],
    },
    {
        _id: 3,
        code: 'PAR3',
        description: 'referring 3',
        reference: 1,
        restricted: 'restricted3',
        referenceArray: [1, 1],
    },
];

export const referredData: NodeCreateData<TestReferred>[] = [
    {
        _id: 1,
        code: 'REF1',
        details: 'reference B1',
        restricted: 'restricted1',
    },
    {
        _id: 2,
        code: 'REF2',
        details: 'reference A2', // sorts differently than code,
        restricted: 'restricted2',
    },
];

export const referringWithJoinData: ReferringWithJoinData[] = [
    {
        _id: 1,
        code: 'PAR1',
        referenceCode: 'REF1',
    },
    {
        _id: 2,
        code: 'PAR2',
    },
];
