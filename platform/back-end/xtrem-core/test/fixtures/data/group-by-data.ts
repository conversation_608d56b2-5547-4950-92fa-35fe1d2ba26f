// use quite the same data as https://jira.sage.com/browse/X3-197179
// create RAW401, RAW404, RAW007
export const itemData = [
    { _id: 1, code: 'RAW401' },
    { _id: 2, code: 'RAW404' },
    { _id: 3, code: 'RAW407' },
    { _id: 4, code: 'RAW411' },
];

// create stocks for RAW401..RAW407
export const stockData = [
    {
        _id: 1,
        stockSite: 'NA022',
        location: 'QUA01',
        product: 1, // 'RAW401'
        quantityInStockUnit: 10600,
    },
    {
        _id: 2,
        stockSite: 'NA022',
        location: 'QUA01',
        product: 1, // 'RAW401'
        quantityInStockUnit: 1250,
    },
    {
        _id: 3,
        stockSite: 'NA022',
        location: 'QUA01',
        product: 2, // 'RAW404'
        sublot: 1,
        quantityInStockUnit: 4.942,
    },
    {
        _id: 4,
        stockSite: 'NA022',
        location: 'QUA01',
        product: 3, // 'RAW407'
        quantityInStockUnit: 10,
    },
    {
        _id: 5,
        stockSite: 'NA022',
        location: 'QUA01',
        product: 3, // 'RAW407'
        sublot: 1,
        quantityInStockUnit: 15,
    },
    {
        _id: 6,
        stockSite: 'NA022',
        location: 'QUA01',
        product: 4, // 'RAW411'
        quantityInStockUnit: 30,
    },
    {
        _id: 7,
        stockSite: 'NA022',
        location: 'QUA01',
        product: 4, // 'RAW411'
        sublot: 0,
        quantityInStockUnit: 40,
    },
    {
        _id: 8,
        stockSite: 'NA022',
        location: 'QUA01',
        product: 4, // 'RAW411'
        sublot: 1,
        quantityInStockUnit: 50,
    },
];
