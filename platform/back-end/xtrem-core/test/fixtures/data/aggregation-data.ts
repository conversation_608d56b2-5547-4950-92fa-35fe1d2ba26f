import { date } from '@sage/xtrem-date-time';
import { Decimal } from '@sage/xtrem-decimal';
import { Dict } from '@sage/xtrem-shared';

function mapDecimals(arr: Dict<any>[]): Dict<any>[] {
    arr.forEach(obj => {
        [
            'amount1',
            'amount2',
            'amount3',
            'amount4',
            'amount5',
            'totalAmount1',
            'totalAmount2',
            'totalAmount3',
            'totalAmount4',
            'totalAmount5',
        ].forEach(key => {
            if (Object.prototype.hasOwnProperty.call(obj, key)) {
                obj[key] = Decimal.make(obj[key].toString());
            }
        });
    });
    return arr;
}

export const aggDocumentData = mapDecimals([
    {
        _id: 1,
        code: 'DOC1',
        linesCount: 4,
        totalAmount1: 35.5,
        totalAmount2: 20.13,
        totalAmount3: 47.0,
        totalAmount4: 15.66,
        totalAmount5: 9.2,
    },
    {
        _id: 2,
        code: 'DOC2',
        linesCount: 3,
        totalAmount1: 45.0,
        totalAmount2: 20.7,
        totalAmount3: 22.91,
        totalAmount4: 22.66,
        totalAmount5: 16.01,
    },
]);

export const aggDocumentReverseRefData = mapDecimals([
    {
        _id: 1,
        document: 1,
        code: 'DOC1ReverseRef',
    },
    {
        _id: 2,
        document: 2,
        code: 'DOC2ReverseRef',
    },
]);

export const aggDocumentLineData = mapDecimals([
    {
        document: 1, // 'DOC1',
        lineNumber: 1,
        amount1: 15.0,
        amount2: 8.2,
        amount3: 7.1,
        amount4: 1.5,
        amount5: 5.65,
        date: date.make(2000, 2, 7),
    },
    {
        document: 1, // 'DOC1',
        lineNumber: 2,
        amount1: 5.0,
        amount2: 5.13,
        amount3: 8,
        amount4: 11.5,
        amount5: 2.65,
        date: date.make(2000, 2, 7),
    },
    {
        document: 1, // 'DOC1',
        lineNumber: 3,
        amount1: 7.0,
        amount2: 2.9,
        amount3: 15,
        amount4: 1.51,
        amount5: 0.25,
        date: date.make(2000, 2, 15),
    },
    {
        document: 1, // 'DOC1',
        lineNumber: 4,
        amount1: 8.5,
        amount2: 3.9,
        amount3: 16.9,
        amount4: 1.15,
        amount5: 0.65,
        date: date.make(2000, 2, 15),
    },
    {
        document: 2, // 'DOC2',
        lineNumber: 1,
        amount1: 6.5,
        amount2: 8.2,
        amount3: 14.25,
        amount4: 11.15,
        amount5: 10.36,
        date: date.make(2003, 1, 8),
    },
    {
        document: 2, // 'DOC2',
        lineNumber: 2,
        amount1: 16.5,
        amount2: 7.6,
        amount3: 7.1,
        amount4: 9.51,
        amount5: 2.65,
        date: date.make(2003, 8, 24),
    },
    {
        document: 2, // 'DOC2',
        lineNumber: 3,
        amount1: 22.0,
        amount2: 4.9,
        amount3: 1.56,
        amount4: 2,
        amount5: 3,
        date: date.make(2004, 2, 18),
    },
]);

export const aggDocumentExtendedLineData = mapDecimals([
    {
        document: 1, // 'DOC1',
        lineNumber: 1,
        amount1: 15.0,
        amount2: 8.2,
        date: date.make(2000, 2, 7),
    },
    {
        document: 1, // 'DOC1',
        lineNumber: 2,
        amount1: 5.0,
        amount2: 5.13,
        date: date.make(2000, 2, 7),
    },
    {
        document: 1, // 'DOC1',
        lineNumber: 3,
        amount1: 7.0,
        amount2: 2.9,
        date: date.make(2000, 2, 15),
    },
    {
        document: 1, // 'DOC1',
        lineNumber: 4,
        amount1: 8.5,
        amount2: 3.9,
        date: date.make(2000, 2, 15),
    },
    {
        document: 2, // 'DOC2',
        lineNumber: 1,
        amount1: 6.5,
        amount2: 8.2,
        date: date.make(2003, 1, 8),
    },
    {
        document: 2, // 'DOC2',
        lineNumber: 2,
        amount1: 16.5,
        amount2: 7.6,
        date: date.make(2003, 8, 24),
    },
    {
        document: 2, // 'DOC2',
        lineNumber: 3,
        amount1: 22.0,
        amount2: 4.9,
        date: date.make(2004, 2, 18),
    },
]);
