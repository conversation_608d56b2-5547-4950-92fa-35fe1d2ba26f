import { TestRequiredProperty } from '../nodes/index';
import { TestInitData } from '../util';

export type RequiredPropertyData = Pick<
    TestInitData<TestRequiredProperty>,
    Exclude<
        keyof TestRequiredProperty,
        | 'computed'
        | '$'
        | 'state'
        | 'requiredReference'
        | 'requiredReferenceArray'
        | '_sourceId'
        | '_customData'
        | '_sortValue'
    >
> & {
    requiredReference: number;
    requiredReferenceArray: number[];
};

export const requiredPropertyData: RequiredPropertyData[] = [
    {
        _id: 1,
        notRequiredNotNullable: 1,
        requiredReference: 1,
        notRequiredNullable: 1,
        notRequiredNullable2: null,
        requiredComputed: 5,
        requiredReferenceArray: [1, 2],
    },
    {
        _id: 2,
        notRequiredNotNullable: 2,
        requiredReference: 1,
        notRequiredNullable: 2,
        notRequiredNullable2: null,
        requiredComputed: 5,
        requiredReferenceArray: [1, 2],
    },
];
