import { Decimal } from '@sage/xtrem-decimal';
import { BinaryStream } from '../../../lib';
import { TestDataTypeEvents } from '../nodes/index';
import { readSampleFile, TestInitData } from '../util';

export type DataTypesEventsData = TestInitData<TestDataTypeEvents>;

function make(id: number, data?: DataTypesEventsData): DataTypesEventsData {
    return {
        _id: id,
        id,
        enumVal: null,
        stringVal: `string_${id}`,
        adaptableLength: 30,
        adaptableDefault: `default_${id}`,
        stringValAdvancedControls: `astring_${id}`,
        stringValDefaultValue: '',
        decimalVal: Decimal.make(0) as any,
        binaryVal: null,
        binary1024Val: null,
        ...data,
    };
}

export const dataTypeEventsData: DataTypesEventsData[] = [
    make(1),
    make(2, { adaptableLength: 2 }),
    make(3, { adaptableLength: 35, stringValAdvancedControls: 'string_222222222' }),
    make(4, {
        stringVal: 'string_4'.repeat(20),
    }),
    make(5, {
        binaryVal: new BinaryStream(Buffer.from(readSampleFile('png'), 'base64')),
        binary1024Val: new BinaryStream(Buffer.from(readSampleFile('pdf', 1024), 'base64')),
    }),
    make(6, {
        binary1024Val: new BinaryStream(Buffer.from(`limit exceeded ${'a'.repeat(2000)}`)),
    }),
    make(7, {
        binary1024Val: new BinaryStream(Buffer.from(`${'é'.repeat(1024)}`)),
    }),
    make(8, {
        binary1024Val: new BinaryStream(Buffer.from(`${'é'.repeat(33554433)}`)),
    }),
];

export function makeNewRecord(id: number): DataTypesEventsData {
    const newRecord = { ...dataTypeEventsData[id - 1] };
    delete (newRecord as any)._id;
    return newRecord;
}
