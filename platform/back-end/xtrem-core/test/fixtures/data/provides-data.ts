import { NodeCreateData } from '../../../lib/ts-api/create-data';
import {
    TestProvidesChild,
    TestProvidesChildSite,
    TestProvidesGrandChild,
    TestProvidesGrandChildSite,
    TestProvidesParent,
    TestProvidesParentNoSite,
    TestProvidesSite,
} from '../nodes/index';

export type ProvidesSiteData = NodeCreateData<TestProvidesSite>;
export type ProvidesParentData = NodeCreateData<TestProvidesParent>;
export type ProvidesChildData = NodeCreateData<TestProvidesChild>;
export type ProvidesGrandChildData = NodeCreateData<TestProvidesGrandChild>;
export type ProvidesParentNoSiteData = NodeCreateData<TestProvidesParentNoSite>;
export type ProvidesChildSiteData = NodeCreateData<TestProvidesChildSite>;
export type ProvidesGrandChildSiteData = NodeCreateData<TestProvidesGrandChildSite>;

export const providesSiteData: ProvidesSiteData[] = [
    {
        _id: 1,
        code: 'SITE1',
    },
    {
        _id: 2,
        code: 'SITE2',
    },
    {
        _id: 3,
        code: 'SITE3',
    },
    {
        _id: 4,
        code: 'SITE4',
    },
    {
        _id: 5,
        code: 'SITE5',
    },
];

export const providesParentData: ProvidesParentData[] = [
    {
        _id: 1,
        code: 'PARENT1',
        site: 1,
    },
    {
        _id: 2,
        code: 'PARENT2',
        site: 2,
    },
    {
        _id: 3,
        code: 'PARENT3',
        site: 3,
    },
    {
        _id: 4,
        code: 'PARENT4',
        site: 4,
    },
    {
        _id: 5,
        code: 'PARENT5',
        site: 5,
    },
];

export const providesChildData: ProvidesChildData[] = [
    {
        _id: 1,
        code: 'CHILD1of1',
        parent: 1,
    },
    {
        _id: 2,
        code: 'CHILD2of1',
        parent: 1,
    },
    {
        _id: 3,
        code: 'CHILD1of2',
        parent: 2,
    },
    {
        _id: 4,
        code: 'CHILD2of2',
        parent: 2,
    },
    {
        _id: 5,
        code: 'CHILD1of3',
        parent: 3,
    },
];

export const providesGrandChildData: ProvidesGrandChildData[] = [
    {
        _id: 1,
        code: 'GRANDCHILD1of1of1',
        parent: 1,
    },
    {
        _id: 2,
        code: 'GRANDCHILD1of2of1',
        parent: 2,
    },
    {
        _id: 3,
        code: 'GRANDCHILD1of1of2',
        parent: 3,
    },
    {
        _id: 4,
        code: 'GRANDCHILD1of2of2',
        parent: 4,
    },
    {
        _id: 5,
        code: 'GRANDCHILD1of1of3',
        parent: 5,
    },
];

export const providesParentNoSiteData = [
    {
        _id: 1,
        code: 'PARENT1',
    },
    {
        _id: 2,
        code: 'PARENT2',
    },
    {
        _id: 3,
        code: 'PARENT3',
    },
    {
        _id: 4,
        code: 'PARENT4',
    },
    {
        _id: 5,
        code: 'PARENT5',
    },
];

export const providesChildSiteData = [
    {
        _id: 1,
        code: 'CHILD1of1',
        parent: 1,
        site: 1,
    },
    {
        _id: 2,
        code: 'CHILD2of1',
        parent: 1,
        site: 1,
    },
    {
        _id: 3,
        code: 'CHILD1of2',
        parent: 2,
        site: 2,
    },
    {
        _id: 4,
        code: 'CHILD2of2',
        parent: 2,
        site: 2,
    },
    {
        _id: 5,
        code: 'CHILD1of3',
        parent: 3,
        site: 3,
    },
];

export const providesGrandChildSiteData = [
    {
        _id: 1,
        code: 'GRANDCHILD1of1of1',
        parent: 1,
    },
    {
        _id: 2,
        code: 'GRANDCHILD1of2of1',
        parent: 2,
    },
    {
        _id: 3,
        code: 'GRANDCHILD1of1of2',
        parent: 3,
    },
    {
        _id: 4,
        code: 'GRANDCHILD1of2of2',
        parent: 4,
    },
    {
        _id: 5,
        code: 'GRANDCHILD1of1of3',
        parent: 5,
    },
];
