export const htmlMailTemplate = `<!DOCTYPE html
  PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml"
  xmlns:o="urn:schemas-microsoft-com:office:office">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <title>Xtrem</title>
  <!-- Brand_EmailTemplate_V5.7.2 -->

  <!--<link rel="icon" type="image/x-icon" href="https://www.sage.com/favicon.ico">
  <link rel="shortcut icon" type="image/x-icon" href="https://www.sage.com/favicon.ico">-->

  <!--[if gte mso 9]>
  <xml>
    <o:OfficeDocumentSettings>
      <o:AllowPNG/>
      <o:PixelsPerInch>96</o:PixelsPerInch>
    </o:OfficeDocumentSettings>
  </xml>
  <style>
    li {
      text-indent: -1em; /* Normalise space between bullets and text */
    }
  </style>
  <![endif]-->

  <!--[if (gte mso 9)|(IE)]>
  <style type="text/css">
    table {
      border-collapse: collapse;
      border-spacing: 0;
      mso-line-height-rule: exactly;
      mso-margin-bottom-alt: 0;
      mso-margin-top-alt: 0;
      mso-table-lspace: 0pt;
      mso-table-rspace: 0pt;
    }
  </style>
  <![endif]-->

  <!--[if gte mso 9]>
  <xml>
    <o:OfficeDocumentSettings>
      <o:AllowPNG/>
      <o:PixelsPerInch>96</o:PixelsPerInch>
    </o:OfficeDocumentSettings>
  </xml>
  <![endif]-->
  <!--[if gt mso 15]>
  <style type="text/css" media="all">
    /* Outlook 2016 Height Fix */
    table, tr, td {
      border-collapse: collapse;
    }

    tr {
      font-size: 0px;
      line-height: 0px;
      border-collapse: collapse;
    }
  </style>
  <![endif]-->

  <style type="text/css">
    /* Client-specific styles */
    .ExternalClass {
      width: 100%;
    }

    .ExternalClass,
    .ExternalClass p,
    .ExternalClass span,
    .ExternalClass font,
    .ExternalClass td,
    .ExternalClass div {
      line-height: 100%;
    }

    body {
      background-image: none;
      background-repeat: repeat;
      background-position: top left;
      background-attachment: scroll;
      -ms-text-size-adjust: none;
      -webkit-text-size-adjust: none;
      -webkit-font-smoothing: antialiased;
      margin-top: 0;
      margin-bottom: 0;
      margin-right: 0;
      margin-left: 0;
      padding-top: 0;
      padding-bottom: 0;
      padding-right: 0;
      padding-left: 0;
      color: rgb(0, 0, 0, .9);
    }

    a[href^=tel] {
      color: #FFFFFF;
      text-decoration: none;
    }

    img {
      display: block;
      border: none;
      outline: none;
      text-decoration: none;
      -ms-interpolation-mode: bicubic;
    }

    table td,
    table th {
      border-collapse: collapse;
    }

    table {
      border-collapse: collapse;
      mso-table-lspace: 0pt;
      mso-table-rspace: 0pt;
      table-layout: fixed;
    }

    tr {
      font-size: 0px;
      line-height: 0px;
      border-collapse: collapse;
    }

    table table {
      table-layout: auto;
    }

    a {
      color: #008200;
    }

    a:hover {
      color: #009000 !important;
    }

    a img {
      border: none;
    }

    @media print {
      body {
        -webkit-print-color-adjust: exact;
      }
    }

    body,
    table,
    td,
    a {
      -webkit-text-size-adjust: 100%;
      -ms-text-size-adjust: 100%;
    }

    img {
      -ms-interpolation-mode: bicubic;
    }

    /* Reset styles */

    img {
      border: 0 !important;
      line-height: 100%;
      outline: none;
      text-decoration: none;
    }

    table td {
      border-collapse: collapse !important;
    }

    body {
      height: 100% !important;
      margin: 0 !important;
      padding: 0 !important;
      width: 100% !important;
    }

    /* iOS blue links */

    a[x-apple-data-detectors] {
      color: inherit !important;
      text-decoration: none !important;
      font-size: inherit !important;
      font-family: inherit !important;
      font-weight: inherit !important;
      line-height: inherit !important;
    }

    .content-calendar {
      padding: 15px 0 30px 4%;

    }

    /* Global styles */

    body,
    table,
    td {
      font-family: AdelleSansSAGE, Arial, Roboto, Segoe UI, Helvetica Neue;
      font-size: 16px;
      font-weight: 300;
      line-height: 1.5;
      color: #191919
    }


    /* Button style */

    .sage-button {
      background-color: #009000;
      display: inline-block;
      cursor: pointer;
      color: #ffffff;
      text-decoration: none;
      width: 232px;
    }

    .sage-button a {
      color: #ffffff;
    }

    .sage-button:hover {
      background-color: #008000;

    }

    .sage-button a:hover {
      color: #ffffff !important;

    }

    .sage-button-white {
      background-color: #ffffff;
      display: inline-block;
      cursor: pointer;
      color: #191919;
      text-decoration: none;
      width: 232px;
    }

    .sage-button-white a {

      color: #191919;

    }

    .sage-button-white:hover {
      background-color: #eeeeee;

    }

    .sage-button-white a:hover {

      color: #191919 !important;

    }

    .sage-trans-button {
      background-color: transparent;
      display: inline-block;
      cursor: pointer;
      color: #008200;
      text-decoration: none;
      border-color: #008200;
      border-style: solid;
      border-width: 1px;
      width: 232px;

    }

    .sage-trans-button a {
      color: #008200;
    }

    .sage-button:active,
    .sage-button-white:active {
      position: relative;
      top: 1px;
    }

    .SBC-icons {

      /*height:170px;*/
      height: 125px;
      display: block !important;
      padding: 0px !important;

    }

    a.SBC-icon-title:link {

      text-align: center;
      color: #004b87;
      font-size: 20px;
      font-weight: 300;

    }

    .SBC-icon-title {

      text-align: center;
      color: #004b87;
      font-size: 20px;
      font-weight: 300;

    }

    /* Media query: scale from 600px to 480px break point */

    @media screen and (max-width: 600px) {
      h1 {
        font-size: 32px;
        font-family: 'AdelleSansSAGE-Bold', Arial, Roboto, Segoe UI, Helvetica Neue;
        line-height: 1.25 !important;

      }

      h2 {
        font-size: 24px;
        font-family: 'AdelleSansSAGE-Bold', Arial, Roboto, Segoe UI, Helvetica Neue;
        line-height: 1.25 !important;
      }

      h3 {
        font-size: 16px;
        font-family: 'AdelleSansSAGE-Bold', Arial, Roboto, Segoe UI, Helvetica Neue;
      }

      .img-max {
        width: 100% !important;
        max-width: 100% !important;
        height: auto !important;
        min-width: 100px !important;
      }

      .img-max-center {
        width: 100% !important;
        max-width: 100% !important;
        height: auto !important;
        min-width: 100px !important;
        text-align: center !important;
        float: none !important;
        margin: 0 auto !important;
        padding: 0 !important;
      }

      .img-max-pad-top {
        width: 100% !important;
        max-width: 100% !important;
        height: auto !important;
        min-width: 100px !important;
        padding-top: 10px !important;
      }

      .container {
        width: 100% !important;
        max-width: 600px !important;
        height: auto !important;
        min-width: 0 !important;
      }

      .img-pad-top {
        padding-top: 10px;
      }

      .pad-bottom {
        padding-bottom: 20px;
      }

      .pad-bottom-center {
        padding-bottom: 20px;
        margin-left: auto !important;
        margin-right: auto !important;
      }

      .extra-marg-bottom {
        margin-bottom: 30px !important;
      }

      .no-pad-bottom {
        padding-bottom: 0px !important;

      }

      .no-pad-bottom-center {
        padding-bottom: 0px !important;
        margin-left: auto !important;
        margin-right: auto !important;
        float: none !important;
      }

      .text-center-right {
        text-align: center !important;
        padding-right: 20px !important;
        padding-left: 0px !important;
      }

      .text-center-right-pad {
        text-align: center !important;
        padding-left: 0px !important;
      }

      .center-table {
        text-align: center !important;
      }

      .text-center {
        text-align: center !important;
      }

      .text-center-75 {
        text-align: center !important;
        max-width: 75% !important;
      }

      .ul-style {
        padding-left: 20px !important;
      }

      .max-width {
        max-width: 100% !important;
        width: 100% !important;

      }

      .max-width-remove-height {
        max-width: 100% !important;
        width: 100% !important;
        ;
        height: auto !important;
      }

      .max-width-center {
        max-width: 100% !important;
        width: 100% !important;

        text-align: center !important;
        margin: 0 !important;
        padding: 0 !important;
      }

      .max-width-pad {
        max-width: 100% !important;
        width: 100% !important;
        margin-bottom: 12px !important;
      }

      .max-width-top-pad {
        max-width: 100% !important;
        width: 100% !important;

        margin-top: 20px !important;
      }


      .max-width-no-pad {
        max-width: 100% !important;
        width: 100% !important;

      }

      .max-width-no-pad-l {
        max-width: 100% !important;
        width: 100% !important;
        padding-left: 0 !important;
      }

      .max-width-no-pad-a {
        max-width: 100% !important;
        width: 100% !important;
        padding-left: 0 !important;
        padding-right: 0 !important;
        padding-bottom: 0 !important;
        padding-top: 0 !important;
      }

      .max-width-center-pad {
        max-width: 100% !important;
        width: 100% !important;

        text-align: center !important;
        margin: 0 auto !important;
        margin-bottom: 30px !important;
      }

      .mobile-wrapper {
        width: 90% !important;
        max-width: 90% !important;
      }

      .mobile-padding {
        padding-left: 5% !important;
        padding-right: 5% !important;
      }

      .fix-margin {
        margin-top: 0px;
        padding-top: 0px;
      }

      .mobile-center {
        text-align: center !important;
        padding-bottom: 5px !important;
      }

      .mobile-stacked {
        padding-top: 12px !important;
        padding-bottom: 12px !important;
      }


      .remove-height {
        height: auto !important;
      }

      .remove-pad {
        padding: 0px !important;
      }

      .content {
        padding-left: 25px !important;
        padding-right: 25px !important;
      }

      .sage_logo {
        width: auto !important;
        height: 32px !important;
      }


      .desktop-masthead {
        width: 100% !important;
        height: auto !important;
      }

      .img-center {
        margin-left: auto !important;
        margin-right: auto !important;
      }

      .two-col-icons {
        width: 50% !important;
        height: 50% !important;
        display: block;
      }

      .quote-signature {
        padding-left: 10px !important;
      }

      .SBC-icons {
        width: 138px !important;
        height: auto !important;
      }

      .content-SBC-icons {
        padding-left: 40px !important;
        padding-right: 40px !important;
      }

      .content-calendar {
        padding: 15px 0 30px 14%;
      }


      /* Media query for mobile viewport
       * Developer:  hero graphics should be 2 x width for HD rendering.
       */
      @media only screen and (max-width: 480px) {

        h1 {

          font-size: 24px !important;
          font-weight: bold;
        }

        h2 {
          font-size: 20px !important;
          font-weight: bold;
        }

        h3 {
          font-size: 16px;
          font-weight: bold;
        }

        table[class=max-width-pad] {
          max-width: 100% !important;
          width: 100% !important;
          padding-top: 15px !important;
          height: auto !important;
        }

        table[class=max-width] {
          max-width: 100% !important;
          width: 100% !important;
          height: auto !important;
        }

        table[class=container] {
          margin: 0 auto !important;
        }

        .desktop-masthead {
          display: none !important;
        }

        *[class].hidden. *[class=desktop-masthead] {
          display: none !important;
        }

        *[class].elastic {
          width: 100% !important;
          height: auto !important;
        }

        *[class].centered {
          text-align: center !important;
        }

        *[class].fluid,
        [class=fluid-mob] {
          width: 100% !important;
        }

        [class=fluid-mob] {
          position: relative;
        }

        .mobile-show {
          display: table-cell !important;
          width: auto !important;
          height: auto !important;
          max-height: none !important;
          overflow: visible !important;
          visibility: visible !important;
          position: relative !important;
          text-align: center !important;
        }

        .show-mob {
          display: block !important;
          max-height: none !important;
          width: auto !important;
          visibility: visible !important;
        }

        *[class].mob-masthead {
          width: 100% !important;
          display: block !important;
          height: auto !important;
          max-height: none !important;
          padding: 0 !important;
        }

        *[class].mob-masthead-img {
          position: absolute !important;
          top: 0px !important;
          display: block !important;
          height: auto !important;
          max-height: none !important;
          padding: 0 !important;
          width: auto !important;
        }

        [class=fluid-mob] {
          display: table-cell !important;
          width: auto !important;
          height: auto !important;
          max-height: none !important;
          overflow: visible !important;
          visibility: visible !important;
          position: relative !important;
          text-align: center !important;
        }

        .SBC-icons {
          margin: 2px;
          height: auto !important;
        }

        .content-quote {
          margin-top: 20px
        }

        .quote-signature {
          padding-left: 0px !important;
        }

      }

      @media only screen and (max-width: 410px) {
        .SBC-icons {

          height: auto !important;
          clear: none;

        }

        .content-SBC-icons {
          padding-left: 35px !important;
          padding-right: 15px !important;
          margin: 4px;

        }

        .content-calendar {
          padding: 15px 7% 30px;
        }
      }
  </style>
</head>

<body bgcolor="#dddddb"
  style="margin: 0 auto !important; padding: 0 !important; background-color: #F2F5F6; font-family: AdelleSansSAGE, Arial, Roboto, Segoe UI, Helvetica Neue; color: #191919; font-size: 16px">

  <table class="ab_section" data-description="Body" border="0" bgcolor="#F2F5F6" cellpadding="0" cellspacing="0"
    width="100%"
    style="padding-top:0;padding-right:0px;padding-bottom:0px;padding-left:0px; mso-cellspacing: 0px; mso-padding-alt: 0px 0px 0px 0px; border-collapse:collapse; mso-table-lspace:0pt;mso-table-rspace:0pt; background-color: #F2F5F6">
    <tr>
      <td align="center" style="text-align: center">
        <table bgcolor="#ffffff" border="0" cellpadding="0" cellspacing="0" align="center" width="600"
          style="display: table; width: 600px; background-color: #ffffff; margin: 0 auto !important; padding: 0px; mso-table-lspace: 0pt; mso-table-rspace: 0pt; border-collapse:collapse; border-style: solid; border-width: 1px; border-color: #cccccc"
          class="container">
          <!-- Start: A1 Top logo -->
          <!-- Instructions: After changing the logo image and adjusting the "height" and both "width" values below, review the .sage_logo class in the media query CSS -->
          <!-- <tr class="ab_cloneable" data-description="A1 Top Logo"> -->
          <!-- <td align="center" valign="top" style="background-color:#FFFFFF; padding: 17px 30px 17px 30px"> -->
          <!-- <table cellspacing="0" border="0" align="center" -->
          <!-- style="" padding: 0; margin: 0; padding-top:0;padding-right:0px;padding-bottom:0px;padding-left:0px; mso-cellspacing: 0px; mso-padding-alt: 0px 0px 0px 0px; border-collapse:collapse; mso-table-lspace:0pt;mso-table-rspace:0"> -->
          <!-- <tr> -->
          <!-- <td align="center" valign="top"><a class="ab_editable" href="http://sage.com" -->
          <!-- data-description="Sage Logo Link"> -->
          <!-- <img -->
          <!-- src="cid:logo-sage.png" -->
          <!-- class="ab_editable sage_logo" data-description="Sage Logo Image" width="80" -->
          <!-- border="0" alt="Sage" -->
          <!-- style="width: 100%; max-width: 80px; margin-left: auto !important; margin-right: auto !important"> -->
          <!-- </a> -->
          <!-- </td> -->
          <!-- </tr> -->
          <!-- </table> -->
          <!-- </td> -->
          <!-- </tr> -->
          <!-- End: A1 Top logo -->

          <!-- Start: B1 Hero banner image 1 1200px (2 x 600px for HD); breakpoint at 480px -->
          <tr>
            <td align="center" valign="top" class="ab_cloneable" data-description="B1 Hero banner image 1"
              style="margin:0;padding-top:0;padding-right:0px;padding-bottom:0px;padding-left:0px; mso-cellspacing: 0px; mso-padding-alt: 0px 0px 0px 0px; border-collapse:collapse; mso-table-lspace:0pt;mso-table-rspace:0pt">
              <!-- B1 Mobile email header -->
              <!--[if !mso]><!-->
              <table cellpadding="0" cellspacing="0" border="0" class="fluid"
                style="display:table; padding-top:0;padding-right:0px;padding-bottom:0px;padding-left:0px; mso-cellspacing: 0px; mso-padding-alt: 0px 0px 0px 0px; border-collapse:collapse; mso-table-lspace:0pt;mso-table-rspace:0pt">
                <tbody>
                  <tr>
                    <td
                      style="margin:0;padding-top:0;padding-right:0px;padding-bottom:0px;padding-left:0px; mso-cellspacing: 0px; mso-padding-alt: 0px 0px 0px 0px; border-collapse:collapse; mso-table-lspace:0pt;mso-table-rspace:0pt">
                      <div class="show-mob"
                        style="font-size: 0; max-height: 0; overflow: hidden; display: none; line-height:0; visibility:hidden;">
                        <table width="100%" cellpadding="0" cellspacing="0" border="0">
                          <tr>
                            <th width="600" align="center" valign="top" bgcolor="#51534a"
                              class="fluid"
                              style="width: 600px; padding-top:0;padding-right:0px;padding-bottom:0px;padding-left:0px; mso-cellspacing: 0px; mso-padding-alt: 0px 0px 0px 0px; border-collapse:collapse; mso-table-lspace:0pt;mso-table-rspace:0pt;">
                              <table width="100%" cellpadding="0" cellspacing="0"
                                border="0" bgcolor="#51534A" class="fluid-mob"
                                style="mso-cellspacing: 0px; mso-padding-alt: 0px 0px 0px 0px; border-collapse:collapse; mso-table-lspace:0pt;mso-table-rspace:0pt; font-size: 0; max-height: 0; overflow: hidden; display: none; line-height:0; visibility:hidden;">
                                <tr>
                                  <td align="left" bgcolor="#51534A"
                                    class="mobile-show"
                                    style="font-size: 0; max-height: 0; overflow: hidden; display: none; line-height:0; visibility:hidden;">
                                    <table width="100%" border="0" cellpadding="0"
                                      cellspacing="0">
                                      <tr>
                                        <td align="center"
                                          style="background-color: #ffffff"
                                          class="mobile-show">
                                          <a class="ab_editable"
                                            href="http://place-holder.com">
                                            <h1
                                              style="margin-top:20px; margin-left:10px;">
                                              Request for {{approvalType}}
                                              approval</h1>
                                          </a>
                                        </td>
                                      </tr>
                                    </table>
                                  </td>
                                </tr>
                              </table>
                            </th>
                          </tr>
                        </table>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
              <!--<![endif]-->
              <!-- End B1 Mobile email header -->

              <!-- Desktop B1 email header -->
              <table width="100%" cellpadding="0" cellspacing="0" border="0" class="fluid"
                style="margin: 0;padding-top:0;padding-right:0px;padding-bottom:0px;padding-left:0px; mso-cellspacing: 0px; mso-padding-alt: 0px 0px 0px 0px; border-collapse:collapse; mso-table-lspace:0pt;mso-table-rspace:0pt; background-color:#f2f5f6;">
                <tr>
                  <td align="center"
                    style="margin: 0;padding-top:0;padding-right:0px;padding-bottom:0px;padding-left:0px; mso-cellspacing: 0px; mso-padding-alt: 0px 0px 0px 0px; border-collapse:collapse; mso-table-lspace:0pt;mso-table-rspace:0pt;background-color:#ffffff;">
                    <h1 style="margin-top:20px; margin-left:10px;">Request for {{approvalType}}
                      approval</h1>
                  </td>
                </tr>
                <tr>
                  <td align="center"
                    style="margin: 0; mso-cellspacing: 0px; mso-padding-alt: 20px 0px 0px 0px; border-collapse:collapse; mso-table-lspace:0pt;mso-table-rspace:0pt">
                  </td>
                </tr>
              </table>
            </td>
          </tr>

          <!-- Start: C1 Single column copy -->
          <tr class="ab_cloneable" data-description="C1 Single column copy">
            <td align="center" valign="top"
              style="background-color: #f2f5f6; padding: 32px 32px 32px 32px; mso-table-lspace: 0pt; mso-table-rspace: 0pt; border-collapse:collapse"
              class="content">
              <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%"
                style="mso-cellspacing: 0px; mso-padding-alt: 0px; border-collapse:collapse">
                <tr>
                  <td valign="top" align="left"
                    style="font-family:AdelleSansSAGE, Arial, Roboto, Segoe UI, Helvetica Neue; font-weight: 300; color: #191919; font-size: 16px; line-height: 1.5">
                    <span class="ab_editable" data-rawhtml="true">
                      Hello,
                      <br />
                      <br />
                      You have a pending {{approvalType}} approval for : <h3
                        style="font-size: 16px; font-weight: bold; margin: 12px 0; line-height: 1.25"
                        class="ab_editable">Formula {{code}} version {{version}} -
                        {{description}} </h3>
                      <br />
                      You can find details on this formula by following this link :
                      <a href="{{urlFormula}}" target="_blank"
                        style="font-size: 16px; font-family:AdelleSansSAGE, Arial, Roboto, Segoe UI, Helvetica Neue; display: inline-block; font-weight: bold;  text-decoration: underline; color:#008200;"
                        class="red ab_editable"><span class="ab_editable"
                          data-rawhtml="true">Click here</span></a>
                      <br />
                      <br />
                      Please use the following buttons to approve or reject the formula.
                    </span>
                    <br><br>
                  </td>
                </tr>
                <tr>
                  <td align="center" valign="top" class="max-width" width="100%"
                    style="font-size:0;  padding-bottom: 30px; mso-padding-alt: 20px 0px 30px 0px;">
                    <table cellpadding="0" cellspacing="0" border="0" width="260"
                      style="width: 260px; text-align: center" class="max-width">
                      <tr>
                        <td align="center">
                          <table width="296" border="0" cellspacing="0" cellpadding="0"
                            style="mso-cellspacing: 0px; mso-padding-alt: 0px; width: 296px; height: 36px; text-align: center; -webkit-text-size-adjust:none"
                            class="no-pad-bottom-center">
                            <tr>
                              <td bgcolor="#009900" style="width: 300px;" align="center">
                                <a class="ab_editable" href="{{urlApprove}}"
                                  target="_blank"
                                  style="font-size: 16px; font-family:AdelleSansSAGE, Arial, Roboto, Segoe UI, Helvetica Neue; font-weight: bold !important; color: #ffffff; text-decoration: none; display: inline-block">
                                  Approve</a></td>
                              <td style="width: 232px;"></td>
                              <td bgcolor="#99001c" style="width: 350px;" align="center">
                                <a class="ab_editable" href="{{urlReject}}"
                                  target="_blank"
                                  style="font-size: 16px; font-family:AdelleSansSAGE, Arial, Roboto, Segoe UI, Helvetica Neue; font-weight: bold !important; color: #ffffff; text-decoration: none; display: inline-block">
                                  Reject</a></td>
                            </tr>
                          </table>
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
          <!-- End: C1 Single column copy -->

        </table>
        <br><br>
      </td>
    </tr>
  </table>
</body>

</html>
`;

export const reportTemplates = [
    `<div id="main">
{{#with xtremPurchasing.purchaseOrder.query.edges.0.node}}
<div id="header">
  <h2>Purchase Order - {{number}}</h2>
  <div>
    <table class="header-table">
      <thead>
        <tr>
          <th class="column-left">Vendor</th>
          <th class="column-left">Ship to</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>
            <strong>{{supplier.name}}</strong>
            <br />
            {{#with supplier.addresses.query.edges.0.node}}
              {{addressLine1}}<br />{{city}}<br />{{postcode}}<br />{{country.name}}
            {{/with}}
          </td>
          <td>
            <strong>{{stockSite.legalCompany.name}}</strong>
            <br />
            {{#with stockSite.addresses.query.edges.0.node}}
              {{addressLine1}}<br />{{city}}<br />{{postcode}}<br />{{country.name}}
            {{/with}}
          </td>
        </tr>
      </tbody>
    </table>
    <table class="header-table">
      <thead>
        <tr>
          <th class="column-left">PO Number</th>
          <th class="column-left">Order Date</th>
          <th class="column-left">Approval Status</th>
          <th class="column-left">Payment Term</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>{{number}}</td>
          <td>{{orderDate}}</td>
          <td>{{enumValue '@sage/xtrem-purchasing/PurchaseOrderStatus' status}}</td>
          <td>{{paymentTerm.name}}</td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
<div id="report-body">
  <h4>Order Lines</h4>
  <table class="lines-table">
    <thead>
      <tr>
        <th class="column-left">
          Item
        </th>
        <th class="column-left">
          Description
        </th>
        <th class="column-right">
          Quantity
        </th>
        <th class="column-left">
          Unit
        </th>
        <th class="column-right">
          Unit Price
        </th>
        <th class="column-right">
          Net Price
        </th>
        <th class="column-right">
          Gross Price
        </th>
        <th class="column-right">
          Total
        </th>
      </tr>
    </thead>
    <tbody>
      {{#each lines.query.edges}}
        <tr>
          <td class="column-left"><strong>{{node.item.name}}</strong></td>
          <td class="column-left">{{node.item.description}}</td>
          <td class="column-right">{{toFixed node.quantity 2}}</td>
          <td class="column-left">{{node.purchaseUnit.name}}</td>
          <td class="column-right">{{node.currency.symbol}} {{toFixed node.price 2}}</td>
          <td class="column-right">{{node.currency.symbol}} {{toFixed totalTaxExcludedAmount 2}}</td>
          <td class="column-right">{{node.currency.symbol}} {{toFixed price 2}}</td>
          <td class="column-right"><strong>{{node.currency.symbol}} {{toFixed (multiply node.quantity node.price) 2}}</strong></td>
        </tr>
      {{/each}}
      <tr>
        <td class="column-right" colspan="7"><strong>Total:</strong></td>
        <td class="column-right"><strong>{{currency.symbol}} {{toFixed totalTaxExcludedAmount 2}}</strong></td>
      </tr>
    </tbody>
  </table>
  {{#if text}}
    <h4>Notes</h4>
    <p>
      {{text.value}}
    </p>
  {{/if}}
  {{#if changeRequestedDescription}}
    <h4>Changes Requested</h4>
    <p>
      {{changeRequestedDescription.value}}
    </p>
  {{/if}}
</div>
{{/with}}
</div>
`,
    `<div id="main">
{{#with xtremSales.salesShipment.query.edges.0.node}}
<div id="header">
    <h2>Packing slip - {{number}}</h2>
    <div>
        <table class="header-table">
            <thead>
                <tr>
                    <th class="column-left">Ship to</th>
                    <th class="column-left">Bill to</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>
                        <strong>{{shipToCustomer.name}}</strong>
                        <br />
                        {{#with shipToAddressDetail}}
                            {{addressLine1}}<br />{{city}}<br />{{postcode}}<br />{{country.name}}
                        {{/with}}
                    </td>
                    <td>
                        <strong>{{billToCustomer.name}}</strong>
                        <br />
                        {{#with billToAddressDetail}}
                            {{addressLine1}}<br />{{city}}<br />{{postcode}}<br />{{country.name}}
                        {{/with}}
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>
<div id="report-body">
    <h4>Details</h4>
    <table class="lines-table">
        <thead>
            <tr>
                <th class="column-left">
                    Order number
                </th>
                <th class="column-left">
                    Item
                </th>
                <th class="column-left">
                    Description
                </th>
                <th class="column-left">
                    Unit
                </th>
                <th class="column-right">
                    Quantity
                </th>
            </tr>
        </thead>
        <tbody>
            {{#each lines.query.edges}}
                <tr>
                    <td class="column-left">{{node.salesOrderLines.query.edges.0.node.linkedDocument.document.number}}</td>
                    <td class="column-left">{{node.itemId}}</td>
                    <td class="column-left">{{node.item.description}}</td>
                    <td class="column-left">{{node.salesUnit.name}}</td>
                    <td class="column-right">{{node.quantity}}</td>
                </tr>
            {{/each}}
        </tbody>
    </table>
</div>
{{/with}}
</div>
`,
    // activeUsers
    `<div class="entry">
<img src="{{reportResource 'companyLogo'}}" style="width:2cm; height:2cm"/>   <h1>Active Users</h1>
  <table>
      <thead>
          <th>First Name</th>
          <th>Last Name</th>
          <th>Email</th>
      </thead>
      <tbody>
          {{#each codeBlockResult}}
          <tr>
              <td>{{node.firstName}}</td>
              <td>{{node.lastName}}</td>
              <td>{{node.email}}</td>
          </tr>
          {{/each}}
      </tbody>
  </table>
</div>
`,
    `
<div>
    {{#with xtremTechnicalData.billOfMaterialPrintout.query.edges.0.node}}
    <table class="report-container">
        <thead class="report-header">
            <tr>
                <th class="report-header-cell normal-black">
                    <div class="header-info">
                        <table>
                            <tbody>
                                <tr>
                                    <td class="column-right">
                                        <div>
                                            <h1>Bill of material multi level components list</h1><br>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="frame">
                        <table style="border-style:none">
                            <tbody>
                                <tr>
                                    <td class="column-left">
                                        <span class="strong-blue">Item name:</span> {{bomCode.item.name}}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="column-left">
                                        <span class="strong-blue">Item id:</span> {{bomCode.item.id}}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="column-left">
                                        <span class="strong-blue">Site:</span> {{bomCode.site.name}}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="column-left">
                                        <span class="strong-blue">Status:</span> {{enumValue '@sage/xtrem-technical-data/Availability' bomCode.status}}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="column-left">
                                        <span class="strong-blue">Base quantity:</span>
                                        {{bomCode.baseQuantity}} {{bomCode.item.stockUnit.symbol}}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="column-left">
                                        <span class="strong-blue">Total standard cost:</span>
                                        {{bomCode.standardCost}} {{bomCode.currency.symbol}}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="column-left">
                                        <span class="strong-blue">Routing:</span> {{bomCode.routingCode.name}}
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </th>
            </tr>
        </thead>
        <tfoot class="report-footer">
        </tfoot>

        <tbody class="report-content">
            <tr>
                <td class="report-content-cell">
                    <table class="lines-table">
                        <thead>
                            <tr>
                                <th class="column-left">Level</th>
                                <th class="column-left">Component number</th>
                                <th class="column-left">Line type</th>
                                <th class="column-left">Component description</th>
                                <th class="column-left">Item id</th>
                                <th class="column-left">Operation</th>
                                <th class="column-left">Fixed quantity</th>
                                <th class="column-left">Linked quantity</th>
                                <th class="column-left">Scrap factor %</th>
                                <th class="column-left">Standard cost</th>
                            </tr>
                        </thead>
                        <tbody>
                            {{#each components.query.edges}}
                            <tr>
                                <td class="column-right" width="5%">{{node.level}}</td>
                                <td class="column-right" width="5%">{{node.component.componentNumber}}</td>
                                <td class="column-left" width="5%">{{enumValue '@sage/xtrem-technical-data/BomLineType' node.component.lineType}}</td>
                                {{#if (is node.component.lineType "normal")}}
                                    <td class="column-left" width="20%">{{node.component.name}}</td>
                                    <td class="column-left" width="15%">{{node.component.item.id}}</td>
                                    <td class="column-left" width="20%">{{node.component.operation.name}}</td>
                                    <td class="column-left" width="5%">{{node.component.isFixedLinkQuantity}}</td>
                                    <td class="column-right" width="10%">{{node.component.linkQuantity}} {{node.component.unit.symbol}}</td>
                                    <td class="column-right" width="5%">{{node.component.scrapFactor}}</td>
                                    <td class="column-right" width="15%">{{node.component.standardCost}}</td>
                                {{else}}
                                    <td class="column-left" width="85%" colspan="7">{{node.component.name}}</td>
                                {{/if}}
                            </tr>
                            {{/each}}
                        </tbody>
                    </table>
                </td>
            </tr>
        </tbody>
    </table>
    {{/with}}
</div>
`,
    `<div>
{{#with xtremPurchasing.purchaseOrder.query.edges.0.node}}
    <div>
        <h2>Purchase Order - {{number}}</h2>
        <div>
            <table class="header-table">
                <thead>
                    <tr>
                        <th class="column-left">Vendor</th>
                        <th class="column-left">Ship to</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            {{#with supplierAddress}}
                            <strong>{{name}}</strong>
                            <br>

                            {{addressLine1}}<br>
                                {{#if addressLine2}}{{addressLine2}}<br>{{/if}}
                                {{#if (is country.iso31661Alpha3  "GBR")}}
                                    {{city}}
                                    {{#if region}} {{region}}{{/if}}
                                    {{postcode}}<br>
                                {{else if (is country.iso31661Alpha3 "ZAF")}}
                                    {{city}}
                                    {{#if region}} {{region}}{{/if}}
                                    {{postcode}}<br>
                                {{else if (is country.iso31661Alpha3 "USA")}}
                                    {{city}}
                                    {{#if region}} {{region}}{{/if}}
                                    {{postcode}}<br>
                                {{else}}
                                    {{postcode}} {{city}}<br>
                                {{/if}}
                                {{country.name}}
                            {{/with}}
                        </td>
                        <td>
                            {{#with siteAddress}}
                                <strong>{{name}}</strong>
                                <br>
                                {{addressLine1}}<br>
                                {{#if addressLine2}}{{addressLine2}}<br>{{/if}}
                                {{#if (is country.iso31661Alpha3  "GBR")}}
                                    {{city}}
                                    {{#if region}} {{region}}{{/if}}
                                    {{postcode}}<br>
                                {{else if (is country.iso31661Alpha3 "ZAF")}}
                                    {{city}}{{#if region}} {{region}}{{/if}}
                                    {{postcode}}<br>
                                {{else if (is country.iso31661Alpha3 "USA")}}
                                    {{city}}
                                    {{#if region}} {{region}}{{/if}}
                                    {{postcode}}<br>
                                {{else}}
                                    {{postcode}} {{city}}<br>
                                {{/if}}
                                {{country.name}}
                            {{/with}}
                        </td>
                    </tr>
                </tbody>
            </table>
            {{enumValue '@sage/xtrem-purchasing/PurchaseOrderStatus' status}}
            <table class="header-table">
                <thead>
                    <tr>
                        <th class="column-left">PO Number</th>
                        <th class="column-left">Order Date</th>
                        <th class="column-left">Approval Status</th>
                        <th class="column-left">Payment Term</th>
                        <th class="column-left">Delivery Mode</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>{{number}}</td>
                        <td>{{formatDate orderDate}}</td>
                        <td>{{status}}</td>
                        <td>{{paymentTerm.name}}</td>
                        <td>{{deliveryMode.name}}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>`,
    // ReDoS attack
    `<div>${'m{{'.repeat(30000)}m</div>`,
    // ReDoS attack
    `<div>${'{{{{'.repeat(30000)}</div>`,
];
