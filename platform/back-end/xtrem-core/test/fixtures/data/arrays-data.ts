import { Decimal } from '@sage/xtrem-decimal';
import { date, datetime, Uuid } from '../../../index';

export const arraysData = [
    {
        id: 0,
        booleanValues: [null, true, false],
        integerValues: [0, -2, 5],
        decimalValues: [Decimal.make(0), Decimal.make(-0.2), Decimal.make(0.3)],
        floatValues: [0, -0.25, 0.625],
        doubleValues: [0, -0.25, 0.625],
        stringValues: ['', 'abc', 'def'],
        datetimeValues: [null, datetime.parse('2019-12-01T01:02:03Z'), datetime.parse('2019-12-05T06:07:08Z')],
        dateValues: [null, date.parse('2019-12-01'), date.parse('2019-12-05')],
        enumValues: [null, 'value1', 'value3'],
        uuidValues: [null, Uuid.generate(), Uuid.generate()],
    },
];
