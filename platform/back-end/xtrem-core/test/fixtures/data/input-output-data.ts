import { TestIoProperties } from '../nodes/index';
import { TestInitData } from '../util';

export type IoData = Pick<
    TestInitData<TestIoProperties>,
    Exclude<keyof TestIoProperties, 'computed' | '$' | 'state' | '_sourceId' | '_customData' | '_sortValue'>
>;

export const testIoData: IoData[] = [
    {
        _id: 1,
        inputAndOutputProperty: 'inputAndOutputProperty1',
        inputOnlyProperty: 'inputOnlyProperty1',
        outputOnlyPropertyGet: 'outputOnlyPropertyGet1',
        outputOnlyPropertyCompute: 'outputOnlyPropertyCompute1',
    },
    {
        _id: 2,
        inputAndOutputProperty: 'inputAndOutputProperty2',
        inputOnlyProperty: 'inputOnlyProperty2',
        outputOnlyPropertyGet: 'outputOnlyPropertyGet2',
        outputOnlyPropertyCompute: 'outputOnlyPropertyCompute2',
    },
    {
        _id: 3,
        inputAndOutputProperty: 'inputAndOutputProperty3',
        inputOnlyProperty: 'inputOnlyProperty3',
        outputOnlyPropertyGet: 'outputOnlyPropertyGet3',
        outputOnlyPropertyCompute: 'outputOnlyPropertyCompute3',
    },
];
