import {
    DateRange,
    Datetime,
    DatetimeRange,
    DateValue,
    DecimalRange,
    IntegerRange,
    Time,
    time,
} from '@sage/xtrem-date-time';
import { Decimal } from '@sage/xtrem-decimal';
import { integer } from '@sage/xtrem-shared';
import { BinaryStream, date, dateRange, datetime, datetimeRange, TextStream, Uuid } from '../../../index';
import { TestEnum, TestEnumForArray } from '../enums/index';

export interface DatatypesData {
    _id: integer;
    _sourceId: string;
    booleanVal: boolean | null;
    id: integer;
    shortVal: integer | null;
    integerVal: integer;
    integerRangeVal: IntegerRange | null;
    decimalRangeVal: DecimalRange | null;
    decimalVal: number;
    floatVal: number;
    doubleVal: number;
    stringVal: string;
    stringArrayVal: string[];
    datetimeVal: Datetime | null;
    dateVal: DateValue | null;
    dateRangeVal: DateRange | null;
    datetimeRangeVal: DatetimeRange | null;
    timeVal: Time | null;
    enumVal: TestEnum | null;
    textStream: TextStream;
    mailTemplate: TextStream;
    unsafeMailTemplate: TextStream;
    binaryStream: BinaryStream | null;
    uuidVal: Uuid;
    jsonVal: object;
    enumArrayVal: TestEnumForArray[];
    integerArrayVal: integer[];
}

export const datatypesData: DatatypesData[] = [
    {
        _id: 1,
        _sourceId: '',
        booleanVal: null,
        id: 0,
        shortVal: null,
        integerVal: 0,
        integerRangeVal: null,
        decimalRangeVal: null,
        decimalVal: Decimal.make(0) as any,
        floatVal: 0,
        doubleVal: 0,
        stringVal: '',
        stringArrayVal: [],
        datetimeVal: null,
        dateVal: null,
        dateRangeVal: null,
        datetimeRangeVal: null,
        timeVal: null,
        enumVal: null,
        textStream: TextStream.empty,
        mailTemplate: TextStream.empty,
        unsafeMailTemplate: TextStream.empty,
        binaryStream: null,
        uuidVal: Uuid.generate(),
        jsonVal: {},
        enumArrayVal: [],
        integerArrayVal: [],
    },
];

for (let i = 1; i <= 15; i += 1) {
    const month = (i % 12) + 1;
    const year = 2017 + Math.floor(i / 12);
    datatypesData.push({
        _id: i + 1,
        _sourceId: '',
        id: i,
        booleanVal: i % 2 === 1,
        enumVal: (['value1', 'value2', 'value3'] as TestEnum[])[i % 3],
        shortVal: 1000 - i,
        integerVal: 1000000 - i,
        integerRangeVal: IntegerRange.make(i, i + 2),
        decimalRangeVal: DecimalRange.make(`${i}.001`, `${i + 2}.0004`),
        decimalVal: Decimal.make(i + 0.5) as any, // more challenging roundtrip later
        floatVal: i + 0.25,
        doubleVal: i + 0.125,
        dateVal: date.make(year, month, 15),
        dateRangeVal: dateRange.make(date.make(year, month, 1), date.make(year, month, 4 + i)),
        datetimeRangeVal: datetimeRange.make(
            datetime.make(year, month, 1, 0, 0, 0),
            datetime.make(year, month, 4 + i, 23, 59, 59),
        ),
        timeVal: time.make(i - 1, ((i - 1) * 7) % 60, ((i - 1) * 21) % 60),
        datetimeVal: datetime.make(year, month, 15, 14, 30, 10),
        stringVal: `string%_$${i}`,
        stringArrayVal: [`string%_$${i}`, `string%_$${i + 1}`, `string%_$${i + 2}`],
        uuidVal: Uuid.generate(),
        binaryStream: BinaryStream.fromBuffer(Buffer.from([i, i + 200, i % 5])),
        textStream: TextStream.fromString(`textStream${i}`),
        mailTemplate: TextStream.fromString(`mailTemplate${i}`),
        unsafeMailTemplate: TextStream.fromString(`unsafeMailTemplate${i}`),
        jsonVal: {
            id: i,
            code: `string_${i}`,
            isValidated: i % 2 === 1,
            authors: [`author_${i}_A`, `author_${i}_B`],
            text: { fr: `chaîne%_$${i}`, en: `string%_$${i}` },
        },
        enumArrayVal: [
            'arrayVal1',
            (['arrayVal1', 'arrayVal2', 'arrayVal3'] as TestEnumForArray[])[i % 3],
            'arrayVal1',
        ],
        integerArrayVal: [i, i + 1, i + 2],
    });
}

export const allIds = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15];
export const allIdsExcept = (...ids: number[]) => allIds.filter(x => ids.indexOf(x) < 0);
