import { NodeCreateData } from '../../../lib/ts-api/create-data';
import { TestSecure, TestSite } from '../nodes/index';

export type SecureData = NodeCreateData<TestSecure>;

export type SiteData = NodeCreateData<TestSite>;

export const siteData: SiteData[] = [
    {
        _id: 1,
        code: 'SITE1',
    },
    {
        _id: 2,
        code: 'SITE2',
    },
    {
        _id: 3,
        code: 'SITE3',
    },
    {
        _id: 4,
        code: 'SITE4',
    },
    {
        _id: 5,
        code: 'SITE5',
    },
];

export const secureData: SecureData[] = [
    {
        _id: 1,
        code: 'SITE1R1',
        site: 1,
        access: '',
    },
    {
        _id: 2,
        code: 'SITE1R2A1',
        site: 1,
        access: 'ACCESS1',
    },
    {
        _id: 3,
        code: 'SITE2R1',
        site: 2,
        access: '',
    },
    {
        _id: 4,
        code: 'SITE3R1',
        site: 3,
        access: '',
    },
    {
        _id: 5,
        code: 'SITE4R1A2',
        site: 4,
        access: 'ACCESS2',
    },
];
