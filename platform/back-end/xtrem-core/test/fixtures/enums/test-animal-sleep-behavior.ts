import { EnumDataType } from '../../../lib';

export enum TestAnimalSleepBehavior {
    sleepOnTheGround = 1,
    sleepInATree = 2,
    sleepInTheAir = 3,
    neverSleep = 4,
}

export type TestAnimalSleepBehaviorEnum = keyof typeof TestAnimalSleepBehavior;

export const testAnimalSleepBehaviorDataType = new EnumDataType<TestAnimalSleepBehaviorEnum>({
    enum: TestAnimalSleepBehavior,
    filename: __filename,
});
