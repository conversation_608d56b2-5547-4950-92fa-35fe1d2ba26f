import { Dict } from '@sage/xtrem-shared';
import { Application } from '../../lib/application';
import * as enumExtensions from './enum-extensions';
import * as enums from './enums';
import { createApplicationWithApi } from './index';
import * as fixtureNodeExtensions from './node-extensions';
import * as fixtureNodes from './nodes';

/** MiniTestApplicationGenerator and TestApplication are used to generate minimal applications for xtrem-core tests. */

/** TestApplication is a wrapper that returns an application for a given api.
 * We cannot return directly an application because at the time this file is executed, fixtures.nodes is still
 * undefined. Defining global constants such as:
 *
 * "export const testLookupsApplication = new Application({ api: { nodes: { fixtures.nodes.TestDocumentLookup } } })"
 *
 * thus causes an error. Solution: store the actual application in an internal _application attribute, that will
 * be instantiated in a before(() => { ... }) function. */
class TestApplication {
    private _application: Application;

    constructor(private _contents: { nodes?: string[]; nodeExtensions?: string[] }) {}

    get application(): Promise<Application> {
        return (async () => {
            if (!this._application) {
                const nodeNames = this._contents?.nodes || [];
                const nodeExtensionNames = this._contents?.nodeExtensions || [];
                const nodes = nodeNames.reduce((r, k) => {
                    r[k] = (fixtureNodes as any)[k];
                    return r;
                }, {} as Dict<any>);
                const nodeExtensions = nodeExtensionNames.reduce((r, k) => {
                    r[k] = (fixtureNodeExtensions as any)[k];
                    return r;
                }, {} as Dict<any>);
                this._application = await createApplicationWithApi({ nodes, nodeExtensions, enums, enumExtensions });
            }
            return this._application;
        })();
    }
}

/** MiniApplicationGenerator keeps track of the TestApplication instances that were already created by a previously
 * executed test. If a TestApplication was already created for the same api, then the cached TestApplication instance
 * is returned. */
class MiniTestApplicationGenerator {
    private _testApplications: Dict<TestApplication> = {};

    getMiniTestApplication(contents: { nodes?: string[]; nodeExtensions?: string[] }): TestApplication {
        const nodeNames = contents?.nodes || [];
        const nodeExtensionNames = contents?.nodeExtensions || [];
        const key = [...nodeNames, ...nodeExtensionNames].sort((a, b) => (a < b ? 1 : -1)).join();
        if (!this._testApplications[key]) this._testApplications[key] = new TestApplication(contents);
        return this._testApplications[key];
    }
}

export const testApplicationGenerator = new MiniTestApplicationGenerator();

export const testAggregationApplication = testApplicationGenerator.getMiniTestApplication({
    nodes: [
        'TestAggDocument',
        'TestAggDocumentLine',
        'TestAggDocumentExtendedLine',
        'TestAggDocumentBaseLine',
        'TestAggDocumentReverseRef',
        'TestItem',
        'TestStock',
    ],
});

export const testExtensionApplication = testApplicationGenerator.getMiniTestApplication({
    nodes: ['TestBase', 'TestBaseJson', 'TestExtensionReference', 'TestBaseReference', 'TestBaseCollectionElement'],
    nodeExtensions: ['BaseExtension1', 'TestBaseExtension2', 'BaseJsonExtension1', 'TestBaseJsonExtension2'],
});

export const testVitalChildExtensionApplication = testApplicationGenerator.getMiniTestApplication({
    nodes: ['TestBaseVitalChild', 'TestVitalReferenceExtensionParent'],
    nodeExtensions: ['TestBaseVitalChildExtension'],
});
const documentNodes = [
    'TestReferred',
    'TestReferring',
    'TestDocument',
    'TestDocumentLine',
    'ComputedDocumentLine',
    'TestMultiUniqueIndexes',
];
export const testBasicDocumentApplication = testApplicationGenerator.getMiniTestApplication({
    nodes: documentNodes,
});

export const testEmptyApplication = testApplicationGenerator.getMiniTestApplication({
    nodes: [],
});

export const testDocumentWithTransientApplication = testApplicationGenerator.getMiniTestApplication({
    nodes: [...documentNodes, 'TestDatatypes', 'TestTransient', 'TestTransientLines'],
});

export const testTransientApplication = testApplicationGenerator.getMiniTestApplication({
    nodes: ['TestReferred', 'TestTransient', 'TestTransientLines'],
});

export const testDatatypesApplication = testApplicationGenerator.getMiniTestApplication({
    nodes: ['TestDatatypes'],
});

export const testLazyLoadingApplication = testApplicationGenerator.getMiniTestApplication({
    nodes: ['TestDatatypes', 'TestRefToNodeWithLazyLoading'],
});

export const testEncryptedValuesApplication = testApplicationGenerator.getMiniTestApplication({
    nodes: ['TestEncryptedValues'],
});

export const testTenantApplication = testApplicationGenerator.getMiniTestApplication({
    nodes: ['TestDatatypes'],
});

export const testIsFrozenApplication = testApplicationGenerator.getMiniTestApplication({
    nodes: ['TestFrozen', 'TestNotFrozenBase', 'TestOverrideFrozen', 'TestOverrideNotFrozen'],
});

export const testSubclassingIsFrozenChainingApplication = testApplicationGenerator.getMiniTestApplication({
    nodes: [
        'TestFrozenChaining',
        'TestFrozenChainingBase',
        'TestOverrideFrozenChainingValueTrue',
        'TestOverrideFrozenChainingValueFalse',
        'TestOverrideFrozenChainingCallback',
    ],
});

export const testExtensionIsFrozenChainingApplication = testApplicationGenerator.getMiniTestApplication({
    nodes: ['TestFrozenValueFalseChaining', 'TestFrozenValueTrueChaining', 'TestFrozenCallbackChaining'],
    nodeExtensions: [
        'TestFrozenValueFalseChainingExtension',
        'TestFrozenValueTrueChainingExtension',
        'TestFrozenCallbackChainingExtension',
    ],
});

export const testSubclassingApplication = testApplicationGenerator.getMiniTestApplication({
    nodes: [
        'TestFish',
        'TestDog',
        'TestCat',
        'TestPetOwner',
        'TestAnimal',
        'TestMammal',
        'TestAnimalLine',
        'TestBird',
        'TestFlyBehavior',
        'TestSleepBehavior',
        'TestRefPropWithDataType',
        'TestRefPropNoDataType',
    ],
});

export const testSubclassingOverrideApplication = testApplicationGenerator.getMiniTestApplication({
    nodes: [
        'TestOverrideAnimal',
        'TestOverrideAnimalOwner',
        'TestOverrideHorse',
        'TestOverrideHorseOwner',
        'TestOverrideCow',
        'TestOverrideCowOwner',
        'TestOverrideBaseDocument',
        'TestOverrideBaseDocumentLine',
        'TestOverrideInvoice',
        'TestOverrideInvoiceLine',
    ],
});

export const testSubclassingDataExtractionApplication = testApplicationGenerator.getMiniTestApplication({
    nodes: ['TestAnimalJsonOverride', 'TestHorse'],
});

export const testDefaultValueApplication = testApplicationGenerator.getMiniTestApplication({
    nodes: ['TestDefaultValue', 'TestDefaultValuesAdvanced', 'TestDefaultValuesReference'],
});

export const testDuplicatedValueApplication = testApplicationGenerator.getMiniTestApplication({
    nodes: [
        'TestDuplicatedValue',
        'TestDuplicatedVitalReference',
        'TestDuplicatedNonVitalReference',
        'TestDuplicatedParent',
        'TestDuplicatedChild',
        'TestDuplicatedSecondChild',
        'TestDuplicatedGrandChild',
        'TestDuplicatedReferenceForNull',
    ],
});

export const testLookupsApplication = testApplicationGenerator.getMiniTestApplication({
    nodes: [
        'TestDocumentLookup',
        'TestReferencedDocument',
        'TestReferencedDocumentDetails',
        'TestReferencedDocumentOther',
        'TestDocument',
        'TestReferred',
        'TestDocumentLine',
        'TestNonVitalReference',
        'TestVitalReferenceParent',
        'TestVitalReferenceChildMandatory',
        'TestVitalReferenceChildOptional',
    ],
});

export const testValidationApplication = testApplicationGenerator.getMiniTestApplication({
    nodes: [
        'TestControlFlow',
        'TestValidationBasic',
        'TestValidationBoolean',
        'TestValidationNumber',
        'TestValidationPersistent',
        'TestValidationReturns',
        'TestValidationSeverity',
        'TestValidationString',
        'TestValidationDate',
    ],
});

export const testValidIsActiveApplication = testApplicationGenerator.getMiniTestApplication({
    nodes: [
        'TestValidIsActive',
        'TestReferringValidIsActive',
        'TestReferringValidIsActiveFunc',
        'TestParentIsActive',
        'TestChildIsActive',
    ],
});

export const testVitalReferenceApplication = testApplicationGenerator.getMiniTestApplication({
    nodes: [
        'TestVitalReferenceChildMandatory',
        'TestVitalReferenceChildOptional',
        'TestVitalReferenceParent',
        'TestNonVitalReference',
    ],
});

export const testAssociationReferenceApplication = testApplicationGenerator.getMiniTestApplication({
    nodes: [
        'TestStudent',
        'TestCourse',
        'TestStudentCourse',
        'TestCampus',
        'TestStudentCourseCampus',
        'TestAssociationVitalParent',
        'TestAssociationAssociated',
        'TestAssociationItem',
        'TestReference',
        'TestCollection',
        'TestReferenceCollection',
    ],
});

export const testVitalCollectionApplication = testApplicationGenerator.getMiniTestApplication({
    nodes: ['TestVitalCollectionParent', 'TestVitalCollectionChild', 'TestVitalCollectionSubChild'],
});

export const testNotificationApplication = testApplicationGenerator.getMiniTestApplication({
    nodes: ['TestNotification'],
    nodeExtensions: ['TestAdaptValuesExtension'],
});

export const testMutationsApplication = testApplicationGenerator.getMiniTestApplication({
    nodes: ['TestOperation', 'TestOperationReference'],
});

export const testCachedNodeApplication = testApplicationGenerator.getMiniTestApplication({
    nodes: ['TestCachedNode'],
});

export const testDataSensitivityLogApplication = testApplicationGenerator.getMiniTestApplication({
    nodes: ['TestDataSensitivityLog', 'TestDelegatedTo', 'TestDelegatedToReference'],
});

export const testMultiLevelVitalApplication = testApplicationGenerator.getMiniTestApplication({
    nodes: ['TestVitalReferenceParentTopLevel', 'TestVitalReferenceParentMidLevel', 'TestVitalReferenceParentLowLevel'],
});

export const testMultiLevelVitalApplicationWithTwoChildrenSameType = testApplicationGenerator.getMiniTestApplication({
    nodes: [
        'TestVitalReferenceParentWithTwoChildrenSameTypeParent',
        'TestVitalReferenceParentWithTwoChildrenSameTypeChild',
    ],
});

export const dirname = __dirname;
