import { Node } from '../../../index';
import { ReferenceDataType } from '../../../lib/types/reference-data-type';
import { TestDocument } from '../nodes/document';
import { TestDocumentLine } from '../nodes/document-line';
import { TestNodeDetails } from '../nodes/test-node-details';
import { TestReferenceDataType } from '../nodes/test-reference-data-type';

export interface LikeTestDataTypes extends Node {
    readonly stringVal: Promise<string>;
}

export const testReferenceDataType = new ReferenceDataType<TestReferenceDataType, LikeTestDataTypes>({
    reference: () => TestReferenceDataType,
    filters: {
        lookup: {
            code() {
                return this.stringVal;
            },
        },
    },
    lookup: {
        valuePath: 'code',
        helperTextPath: 'name',
        columnPaths: ['code', 'name', 'child.childCode', 'child.grandChild.grandChildCode'],
    },
});

export const testDocumentLineDataType = new ReferenceDataType<TestDocumentLine, LikeTestDataTypes>({
    reference: () => TestDocumentLine,
    lookup: {
        valuePath: 'document.code',
        helperTextPath: 'description',
        columnPaths: ['document.code', 'description'],
    },
});

export const testDocumentDataType = new ReferenceDataType<TestDocument, LikeTestDataTypes>({
    reference: () => TestDocument,
    lookup: {
        valuePath: 'code',
        helperTextPath: 'description',
        columnPaths: ['code', 'description'],
    },
});

export const testNodeDetailsDataType = new ReferenceDataType<TestNodeDetails, LikeTestDataTypes>({
    reference: () => TestNodeDetails,
    lookup: {
        valuePath: 'name',
        helperTextPath: 'name',
        columnPaths: ['name'],
    },
});
