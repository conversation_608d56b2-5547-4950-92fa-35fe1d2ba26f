import { DecimalDataType, JsonDataType, StringArrayDataType, StringDataType } from '../../../lib/types';
import { BinaryStreamDataType } from '../../../lib/types/stream-data-type';

export const codeDataType = new StringDataType({ maxLength: 32 });
export const descriptionDataType = new StringDataType({ maxLength: 250 });
export const descriptionArrayDataType = new StringArrayDataType({ maxLength: 250 });
export const defaultDecimalDataType = new DecimalDataType({ precision: 9, scale: 3 });
export const bundleIdDataType = new StringDataType({ maxLength: 100 });
export const bundlePathType = new StringDataType({ maxLength: 1024 });
export const bundleVersionDataType = new StringDataType({ maxLength: 50 });
export const notificationDataDataType = new BinaryStreamDataType({ maxLength: 8 * 1024 });
export const name = new StringDataType({ maxLength: 80 });
export const email = new StringDataType({ maxLength: 320 });
export const jsonDataType1 = new JsonDataType();
export const id = new StringDataType({ maxLength: 30 });
export const decimalDataType = new DecimalDataType({});
export const localizedCodeDataType = new StringDataType({ maxLength: 32, isLocalized: true });
export const localizedDescriptionDataType = new StringDataType({ maxLength: 250, isLocalized: true });
export const urlDataType = new StringDataType({ maxLength: 200 });
