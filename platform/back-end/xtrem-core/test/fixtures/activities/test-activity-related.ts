import { Activity } from '../../../index';
import { TestActivity, TestActivityRelated } from '../nodes';
import { testActivity } from './test-activity';

export const testActivityRelated = new Activity({
    description: 'Test activity related',
    node: () => TestActivityRelated,
    __filename,
    permissions: ['create', 'report'],
    operationGrants: {
        create: [
            {
                operations: ['reportFromRelated'],
                on: [() => TestActivity],
            },
        ],
    },
    permissionGrants: {
        report: [
            {
                permissions: ['report'],
                on: [() => testActivity],
            },
        ],
    },
});
