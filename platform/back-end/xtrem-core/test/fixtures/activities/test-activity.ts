import { Activity } from '../../../index';
import { TestActivity, TestActivityRelated } from '../nodes';

export const testActivity = new Activity({
    description: 'Test activity',
    node: () => TestActivity,
    __filename,
    permissions: ['read', 'create', 'update', 'delete', 'report'],
    operationGrants: {
        lookup: [],
        create: [
            {
                operations: ['create'],
                on: [() => TestActivityRelated],
            },
        ],
        report: [
            {
                operations: ['reportA', 'reportB'],
                on: [() => TestActivityRelated],
            },
        ],
        testMutation: [],
    },
});
