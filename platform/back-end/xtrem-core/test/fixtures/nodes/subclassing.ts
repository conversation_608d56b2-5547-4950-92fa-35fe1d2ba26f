import { integer, ValidationSeverity } from '@sage/xtrem-shared';
import { decorators, Node, Reference } from '../../../index';
import { Collection, Context, ReferenceDataType } from '../../../lib';
import { descriptionDataType } from '../data-types/data-types';
import {
    testAnimalFlyBehaviorDataType,
    TestAnimalFlyBehaviorEnum,
    testAnimalSleepBehaviorDataType,
    TestAnimalSleepBehaviorEnum,
} from '../enums/index';

@decorators.node<TestAnimal>({
    isPublished: true,
    storage: 'sql',
    isAbstract: true,
    controlBegin(cx): void {
        cx.addDiagnose(ValidationSeverity.info, 'TestAnimal.controlBegin');
    },
})
export class TestAnimal extends Node {
    @decorators.stringProperty<TestAnimal, 'strFromAnimal'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly strFromAnimal: Promise<string>;

    @decorators.referenceProperty<TestAnimal, 'owner'>({
        isPublished: true,
        isStored: true,
        node: () => TestPetOwner,
        isNullable: true,
    })
    readonly owner: Reference<TestPetOwner | null>;

    @decorators.referenceProperty<TestAnimal, 'flyBehavior'>({
        isPublished: true,
        isStored: true,
        node: () => TestFlyBehavior,
        isNullable: true,
        filters: {
            control: { flyBehavior: { _in: ['flyWithWings', 'cannotFly', null] } },
        },
    })
    readonly flyBehavior: Reference<TestFlyBehavior | null>;

    @decorators.referenceProperty<TestAnimal, 'sleepBehavior'>({
        isPublished: true,
        isStored: true,
        node: () => TestSleepBehavior,
        isNullable: true,
    })
    readonly sleepBehavior: Reference<TestSleepBehavior | null>;

    @decorators.referenceProperty<TestAnimal, 'baseRefPropWithDataType'>({
        isPublished: true,
        isStored: true,
        node: () => TestRefPropWithDataType,
        dataType: () => testRefPropDataType,
        isNullable: true,
    })
    readonly baseRefPropWithDataType: Reference<TestRefPropWithDataType | null>;

    @decorators.referenceProperty<TestAnimal, 'baseRefPropNoDataType'>({
        isPublished: true,
        isStored: true,
        node: () => TestRefPropNoDataType,
        isNullable: true,
    })
    readonly baseRefPropNoDataType: Reference<TestRefPropNoDataType | null>;

    @decorators.referenceProperty<TestAnimal, 'refPropWithDataType'>({
        isPublished: true,
        isStored: true,
        node: () => TestRefPropWithDataType,
        dataType: () => testRefPropDataType,
        isNullable: true,
    })
    readonly refPropWithDataType: Reference<TestRefPropWithDataType | null>;

    @decorators.referenceProperty<TestAnimal, 'refPropNoDataType'>({
        isPublished: true,
        isStored: true,
        node: () => TestRefPropNoDataType,
        isNullable: true,
    })
    readonly refPropNoDataType: Reference<TestRefPropNoDataType | null>;

    @decorators.collectionProperty<TestAnimal, 'lines'>({
        isPublished: true,
        isVital: true,
        node: () => TestAnimalLine,
        reverseReference: 'testAnimal',
    })
    readonly lines: Collection<TestAnimalLine>;

    async nonStaticMethod(param: integer): Promise<string> {
        return `nonStaticMethod strFromAnimal=${await this.strFromAnimal}, param=${param}`;
    }

    @decorators.mutation<typeof TestAnimal, 'staticOperation'>({
        isPublished: true,
        parameters: [{ name: 'param', type: 'integer' }],
        return: 'string',
    })
    static staticOperation(_context: Context, param: integer): string {
        return `staticOperation param=${param}`;
    }
}

@decorators.node<TestAnimalLine>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestAnimalLine',
    isVitalCollectionChild: true,
})
export class TestAnimalLine extends Node {
    @decorators.referenceProperty<TestAnimalLine, 'testAnimal'>({
        isStored: true,
        isPublished: true,
        node: () => TestAnimal,
        isVitalParent: true,
    })
    readonly testAnimal: Reference<TestAnimal>;
}

@decorators.subNode<TestMammal>({
    extends: () => TestAnimal,
    isPublished: true,
    canDeleteMany: true,
    isAbstract: true,
    controlBegin(cx): void {
        cx.addDiagnose(ValidationSeverity.info, 'TestMammal.controlBegin');
    },
})
export class TestMammal extends TestAnimal {
    @decorators.stringProperty<TestMammal, 'strFromMammal'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly strFromMammal: Promise<string>;

    @decorators.referencePropertyOverride<TestMammal, 'flyBehavior'>({
        filters: {
            control: { flyBehavior: { _in: ['cannotFly', null] } },
        },
    })
    override readonly flyBehavior: Reference<TestFlyBehavior | null>;
}

@decorators.subNode<TestDog>({
    extends: () => TestMammal,
    isPublished: true,
    canDeleteMany: true,
    controlBegin(cx): void {
        cx.addDiagnose(ValidationSeverity.info, 'TestDog.controlBegin');
    },
})
export class TestDog extends TestMammal {
    @decorators.stringProperty<TestDog, 'strFromDog'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly strFromDog: Promise<string>;

    @decorators.referencePropertyOverride<TestDog, 'sleepBehavior'>({
        filters: {
            controls: [
                {
                    filter: { behavior: 'sleepOnTheGround' },
                    async getErrorMessage() {
                        return `Dog ${await this.strFromAnimal} does not sleep on the ground.`;
                    },
                },
            ],
        },
    })
    override readonly sleepBehavior: Reference<TestSleepBehavior>;

    @decorators.referencePropertyOverride<TestDog, 'refPropWithDataType'>({})
    override readonly refPropWithDataType: Reference<TestRefPropWithDataType | null>;

    @decorators.referencePropertyOverride<TestDog, 'refPropNoDataType'>({})
    override readonly refPropNoDataType: Reference<TestRefPropNoDataType | null>;

    @decorators.referenceProperty<TestDog, 'fromDogRefPropWithDataType'>({
        isPublished: true,
        isStored: true,
        node: () => TestRefPropWithDataType,
        dataType: () => testRefPropDataType,
        isNullable: true,
    })
    readonly fromDogRefPropWithDataType: Reference<TestRefPropWithDataType | null>;

    @decorators.referenceProperty<TestDog, 'fromDogRefPropNoDataType'>({
        isPublished: true,
        isStored: true,
        node: () => TestRefPropNoDataType,
        isNullable: true,
    })
    readonly fromDogRefPropNoDataType: Reference<TestRefPropNoDataType | null>;
}

@decorators.subNode<TestCat>({
    extends: () => TestMammal,
    isPublished: true,
})
export class TestCat extends TestMammal {
    @decorators.stringProperty<TestCat, 'strFromCat'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly strFromCat: Promise<string>;

    @decorators.referencePropertyOverride<TestCat, 'sleepBehavior'>({
        filters: {
            controls: [{ filter: { behavior: 'sleepInATree' }, getErrorMessage: () => 'Cats sleep in trees.' }],
        },
    })
    override readonly sleepBehavior: Reference<TestSleepBehavior>;
}

@decorators.subNode<TestFish>({
    extends: () => TestAnimal,
    isPublished: true,
})
export class TestFish extends TestAnimal {
    @decorators.stringProperty<TestFish, 'strFromFish'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly strFromFish: Promise<string>;

    @decorators.referencePropertyOverride<TestFish, 'flyBehavior'>({
        filters: {
            control: { flyBehavior: { _in: ['cannotFly', null] } },
        },
    })
    override readonly flyBehavior: Reference<TestFlyBehavior | null>;

    @decorators.referencePropertyOverride<TestFish, 'sleepBehavior'>({
        filters: {
            controls: [
                {
                    filter: { behavior: 'neverSleep' },
                    getErrorMessage: () => 'Fish never sleep. Neither does rust.',
                },
            ],
        },
    })
    override readonly sleepBehavior: Reference<TestSleepBehavior>;
}

@decorators.subNode<TestBird>({
    extends: () => TestAnimal,
    isPublished: true,
})
export class TestBird extends TestAnimal {
    @decorators.stringProperty<TestBird, 'strFromBird'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly strFromBird: Promise<string>;

    @decorators.referencePropertyOverride<TestBird, 'flyBehavior'>({
        filters: {
            control: { flyBehavior: 'flyWithWings' },
        },
    })
    override readonly flyBehavior: Reference<TestFlyBehavior | null>;

    @decorators.referencePropertyOverride<TestBird, 'sleepBehavior'>({
        filters: {
            controls: [{ filter: { behavior: { _ne: 'neverSleep' } }, getErrorMessage: () => 'Birds sleep.' }],
        },
    })
    override readonly sleepBehavior: Reference<TestSleepBehavior>;
}
@decorators.node<TestPetOwner>({
    isPublished: true,
    canDeleteMany: true,
    storage: 'sql',
    controlBegin(cx): void {
        cx.addDiagnose(ValidationSeverity.info, 'TestAnimal.controlBegin');
    },
})
export class TestPetOwner extends Node {
    @decorators.collectionProperty<TestPetOwner, 'pets'>({
        isPublished: true,
        node: () => TestAnimal,
        reverseReference: 'owner',
    })
    readonly pets: Collection<TestAnimal>;

    @decorators.referenceProperty<TestPetOwner, 'favoritePet'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        node: () => TestAnimal,
    })
    readonly favoritePet: Reference<TestAnimal | null>;
}

@decorators.node<TestFlyBehavior>({
    isPublished: true,
    storage: 'sql',
})
export class TestFlyBehavior extends Node {
    @decorators.enumProperty<TestFlyBehavior, 'flyBehavior'>({
        isPublished: true,
        isStored: true,
        dataType: () => testAnimalFlyBehaviorDataType,
    })
    readonly flyBehavior: Promise<TestAnimalFlyBehaviorEnum>;
}

@decorators.node<TestSleepBehavior>({
    isPublished: true,
    storage: 'sql',
})
export class TestSleepBehavior extends Node {
    @decorators.enumProperty<TestSleepBehavior, 'behavior'>({
        isPublished: true,
        isStored: true,
        dataType: () => testAnimalSleepBehaviorDataType,
    })
    readonly behavior: Promise<TestAnimalSleepBehaviorEnum>;
}

export const testRefPropDataType = new ReferenceDataType({
    reference: () => TestRefPropWithDataType,
    lookup: {
        valuePath: 'id',
        helperTextPath: 'id',
        columnPaths: ['id'],
    },
});
@decorators.node<TestRefPropWithDataType>({
    isPublished: true,
    storage: 'sql',
    indexes: [{ orderBy: { _id: 1 }, isUnique: true, isNaturalKey: true }],
})
export class TestRefPropWithDataType extends Node {
    @decorators.integerProperty<TestRefPropWithDataType, 'id'>({
        isPublished: true,
        isStored: true,
    })
    readonly id: Promise<integer>;
}

@decorators.node<TestRefPropNoDataType>({
    isPublished: true,
    storage: 'sql',
    indexes: [{ orderBy: { _id: 1 }, isUnique: true, isNaturalKey: true }],
})
export class TestRefPropNoDataType extends Node {
    @decorators.integerProperty<TestRefPropNoDataType, 'id'>({
        isPublished: true,
        isStored: true,
    })
    readonly id: Promise<integer>;
}
