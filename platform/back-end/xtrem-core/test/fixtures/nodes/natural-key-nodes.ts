import { integer } from '@sage/xtrem-shared';
import { Collection, decorators, Node, Reference } from '../../../index';
import { codeDataType, localizedCodeDataType } from '../data-types/data-types';
import { TestReferred } from './referred';

@decorators.node<TestNaturalKey>({
    isPublished: true,
    storage: 'sql',
    indexes: [{ orderBy: { key: +1 }, isUnique: true, isNaturalKey: true }],
})
export class TestNaturalKey extends Node {
    @decorators.stringProperty<TestNaturalKey, 'key'>({
        isPublished: true,
        dataType: () => codeDataType,
        isStored: true,
    })
    readonly key: Promise<string>;

    @decorators.stringProperty<TestNaturalKey, 'text'>({
        isPublished: true,
        dataType: () => codeDataType,
        isStored: true,
    })
    readonly text: Promise<string>;
}

@decorators.node<TestNaturalKeyComposite>({
    isPublished: true,
    storage: 'sql',
    indexes: [{ orderBy: { key: +1, key2: +1 }, isUnique: true, isNaturalKey: true }],
})
export class TestNaturalKeyComposite extends Node {
    @decorators.stringProperty<TestNaturalKeyComposite, 'key'>({
        isPublished: true,
        dataType: () => codeDataType,
        isStored: true,
    })
    readonly key: Promise<string>;

    @decorators.stringProperty<TestNaturalKeyComposite, 'key2'>({
        isPublished: true,
        dataType: () => codeDataType,
        isStored: true,
    })
    readonly key2: Promise<string>;

    @decorators.stringProperty<TestNaturalKeyComposite, 'text'>({
        isPublished: true,
        dataType: () => codeDataType,
        isStored: true,
    })
    readonly text: Promise<string>;
}

@decorators.node<TestNaturalKeyCompositePartialNullableParent>({
    isPublished: true,
    storage: 'sql',
    indexes: [{ orderBy: { key: +1 }, isUnique: true, isNaturalKey: true }],
})
export class TestNaturalKeyCompositePartialNullableParent extends Node {
    @decorators.stringProperty<TestNaturalKeyCompositePartialNullableParent, 'key'>({
        isPublished: true,
        dataType: () => codeDataType,
        isStored: true,
    })
    readonly key: Promise<string>;

    @decorators.referenceProperty<TestNaturalKeyCompositePartialNullableParent, 'keyReference'>({
        isPublished: true,
        node: () => TestNaturalKeyCompositePartialNullable,
        isStored: true,
    })
    readonly keyReference: Promise<TestNaturalKeyCompositePartialNullable>;

    @decorators.stringProperty<TestNaturalKeyCompositePartialNullableParent, 'text'>({
        isPublished: true,
        dataType: () => codeDataType,
        isStored: true,
    })
    readonly text: Promise<string>;
}

@decorators.node<TestNaturalKeyCompositePartialNullable>({
    isPublished: true,
    storage: 'sql',
    indexes: [{ orderBy: { key: +1, keyInteger: +1, keyReference: +1 }, isUnique: true, isNaturalKey: true }],
})
export class TestNaturalKeyCompositePartialNullable extends Node {
    @decorators.stringProperty<TestNaturalKeyCompositePartialNullable, 'key'>({
        isPublished: true,
        dataType: () => codeDataType,
        isStored: true,
    })
    readonly key: Promise<string>;

    @decorators.integerProperty<TestNaturalKeyCompositePartialNullable, 'keyInteger'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        allowedInUniqueIndex: true,
    })
    readonly keyInteger: Promise<integer | null>;

    @decorators.referenceProperty<TestNaturalKeyCompositePartialNullable, 'keyReference'>({
        isPublished: true,
        isStored: true,
        node: () => TestNaturalKey,
        allowedInUniqueIndex: true,
        isNullable: true,
    })
    readonly keyReference: Reference<TestNaturalKey | null>;

    @decorators.stringProperty<TestNaturalKeyCompositePartialNullable, 'text'>({
        isPublished: true,
        dataType: () => codeDataType,
        isStored: true,
    })
    readonly text: Promise<string>;
}

@decorators.node<TestNaturalKeyParent>({
    isPublished: true,
    storage: 'sql',
    indexes: [{ orderBy: { key: +1 }, isUnique: true, isNaturalKey: true }],
})
export class TestNaturalKeyParent extends Node {
    @decorators.stringProperty<TestNaturalKeyParent, 'key'>({
        isPublished: true,
        dataType: () => codeDataType,
        isStored: true,
    })
    readonly key: Promise<string>;

    @decorators.stringProperty<TestNaturalKeyParent, 'text'>({
        isPublished: true,
        dataType: () => codeDataType,
        isStored: true,
    })
    readonly text: Promise<string>;

    @decorators.collectionProperty<TestNaturalKeyParent, 'lines'>({
        isPublished: true,
        isVital: true,
        node: () => TestNaturalKeyVitalChild,
        reverseReference: 'parent',
    })
    readonly lines: Collection<TestNaturalKeyVitalChild>;
}

@decorators.node<TestNaturalKeyVitalChild>({
    isPublished: true,
    storage: 'sql',
    isVitalCollectionChild: true,
    indexes: [{ orderBy: { parent: +1, code1: +1 }, isUnique: true, isNaturalKey: true }],
})
export class TestNaturalKeyVitalChild extends Node {
    @decorators.referenceProperty<TestNaturalKeyVitalChild, 'parent'>({
        isStored: true,
        isPublished: true,
        node: () => TestNaturalKeyParent,
        isVitalParent: true,
    })
    readonly parent: Reference<TestNaturalKeyParent>;

    @decorators.stringProperty<TestNaturalKeyVitalChild, 'code1'>({
        isPublished: true,
        dataType: () => codeDataType,
        isStored: true,
    })
    readonly code1: Promise<string>;

    @decorators.stringProperty<TestNaturalKeyVitalChild, 'code2'>({
        isPublished: true,
        dataType: () => codeDataType,
        isStored: true,
    })
    readonly code2: Promise<string>;
}

@decorators.node<TestReferenceOnVitalChild>({
    isPublished: true,
    storage: 'sql',
})
export class TestReferenceOnVitalChild extends Node {
    @decorators.referenceProperty<TestReferenceOnVitalChild, 'vitalChildRef'>({
        isStored: true,
        isPublished: true,
        node: () => TestNaturalKeyVitalChild,
    })
    readonly vitalChildRef: Reference<TestNaturalKeyVitalChild>;
}

@decorators.node<TestReferenceToNaturalKey>({
    isPublished: true,
    storage: 'sql',
    indexes: [{ orderBy: { id: +1 }, isUnique: true }],
})
export class TestReferenceToNaturalKey extends Node {
    @decorators.stringProperty<TestReferenceToNaturalKey, 'id'>({
        isPublished: true,
        dataType: () => codeDataType,
        isStored: true,
    })
    readonly id: Promise<string>;

    @decorators.referenceProperty<TestReferenceToNaturalKey, 'ref'>({
        isPublished: true,
        isStored: true,
        node: () => TestNaturalKey,
    })
    readonly ref: Reference<TestNaturalKey>;
}

@decorators.node<InvalidNaturalKeyNotUnique>({
    isPublished: true,
    storage: 'sql',
    indexes: [{ orderBy: { key: +1 }, isUnique: false, isNaturalKey: true }],
})
export class InvalidNaturalKeyNotUnique extends Node {
    @decorators.stringProperty<TestNaturalKey, 'key'>({
        isPublished: true,
        dataType: () => codeDataType,
        isStored: true,
    })
    readonly key: Promise<string>;

    @decorators.stringProperty<InvalidNaturalKeyNotUnique, 'text'>({
        isPublished: true,
        dataType: () => codeDataType,
        isStored: true,
    })
    readonly text: Promise<string>;
}

@decorators.node<InvalidNaturalKeyNotPublished>({
    isPublished: true,
    storage: 'sql',
    indexes: [{ orderBy: { key: +1, key2: +1 }, isUnique: true, isNaturalKey: true }],
})
export class InvalidNaturalKeyNotPublished extends Node {
    @decorators.stringProperty<TestNaturalKey, 'key'>({
        isPublished: true,
        dataType: () => codeDataType,
        isStored: true,
    })
    readonly key: Promise<string>;

    @decorators.stringProperty<InvalidNaturalKeyNotPublished, 'key2'>({
        isPublished: false,
        dataType: () => codeDataType,
        isStored: true,
    })
    readonly key2: Promise<string>;

    @decorators.stringProperty<InvalidNaturalKeyNotPublished, 'text'>({
        isPublished: true,
        dataType: () => codeDataType,
        isStored: true,
    })
    readonly text: Promise<string>;
}

@decorators.node<InvalidNaturalKeyString>({
    isPublished: true,
    storage: 'sql',
    indexes: [{ orderBy: { key: +1, key2: +1 }, isUnique: true, isNaturalKey: true }],
})
export class InvalidNaturalKeyString extends Node {
    @decorators.stringProperty<TestNaturalKey, 'key'>({
        isPublished: true,
        dataType: () => localizedCodeDataType,
        isStored: true,
    })
    readonly key: Promise<string>;

    @decorators.stringProperty<InvalidNaturalKeyString, 'key2'>({
        isPublished: true,
        dataType: () => codeDataType,
        isStored: true,
        isStoredEncrypted: true,
    })
    readonly key2: Promise<string>;

    @decorators.stringProperty<InvalidNaturalKeyString, 'text'>({
        isPublished: true,
        dataType: () => codeDataType,
        isStored: true,
    })
    readonly text: Promise<string>;
}

@decorators.node<InvalidNaturalKeyMultiple>({
    isPublished: true,
    storage: 'sql',
    isAbstract: true,
    indexes: [
        { orderBy: { key: +1 }, isUnique: true, isNaturalKey: true },
        { orderBy: { key2: +1 }, isUnique: true, isNaturalKey: true },
    ],
})
export class InvalidNaturalKeyMultiple extends Node {
    @decorators.stringProperty<InvalidNaturalKeyMultiple, 'key'>({
        isPublished: true,
        dataType: () => codeDataType,
        isStored: true,
    })
    readonly key: Promise<string>;

    @decorators.stringProperty<InvalidNaturalKeyMultiple, 'key2'>({
        isPublished: true,
        dataType: () => codeDataType,
        isStored: true,
    })
    readonly key2: Promise<string>;

    @decorators.stringProperty<InvalidNaturalKeyMultiple, 'text'>({
        isPublished: true,
        dataType: () => codeDataType,
        isStored: true,
    })
    readonly text: Promise<string>;
}

@decorators.node<InvalidNaturalKeyParent>({
    isPublished: true,
    storage: 'sql',
    indexes: [{ orderBy: { key: +1 }, isUnique: true, isNaturalKey: true }],
})
export class InvalidNaturalKeyParent extends Node {
    @decorators.stringProperty<InvalidNaturalKeyParent, 'key'>({
        isPublished: true,
        dataType: () => codeDataType,
        isStored: true,
    })
    readonly key: Promise<string>;

    @decorators.stringProperty<InvalidNaturalKeyParent, 'text'>({
        isPublished: true,
        dataType: () => codeDataType,
        isStored: true,
    })
    readonly text: Promise<string>;

    @decorators.collectionProperty<InvalidNaturalKeyParent, 'lines'>({
        isPublished: true,
        isVital: true,
        node: () => InvalidNaturalKeyVitalChild,
        reverseReference: 'parent',
    })
    readonly lines: Collection<InvalidNaturalKeyVitalChild>;
}

@decorators.node<InvalidNaturalKeyVitalChild>({
    isPublished: true,
    storage: 'sql',
    isVitalCollectionChild: true,
    indexes: [{ orderBy: { parent: +1, keyChild: +1 }, isUnique: true, isNaturalKey: true }],
})
export class InvalidNaturalKeyVitalChild extends Node {
    @decorators.referenceProperty<InvalidNaturalKeyVitalChild, 'parent'>({
        isStored: true,
        isPublished: true,
        node: () => InvalidNaturalKeyParent,
        isVitalParent: true,
    })
    readonly parent: Reference<InvalidNaturalKeyParent>;

    @decorators.stringProperty<InvalidNaturalKeyVitalChild, 'keyChild'>({
        isPublished: true,
        dataType: () => codeDataType,
        isStored: true,
    })
    readonly keyChild: Promise<string>;

    @decorators.stringProperty<InvalidNaturalKeyVitalChild, 'textChild'>({
        isPublished: true,
        dataType: () => codeDataType,
        isStored: true,
    })
    readonly textChild: Promise<string>;
}

@decorators.node<InvalidNaturalKeyNonNullableReference>({
    isPublished: true,
    storage: 'sql',
    indexes: [{ orderBy: { parent: +1 }, isUnique: true, isNaturalKey: true }],
})
export class InvalidNaturalKeyNonNullableReference extends Node {
    @decorators.referenceProperty<InvalidNaturalKeyNonNullableReference, 'parent'>({
        isStored: true,
        isPublished: true,
        node: () => TestReferred,
    })
    readonly parent: Reference<TestReferred>;
}

@decorators.node<SetupNodeNoNaturalKey>({
    isPublished: true,
    storage: 'sql',
    isAbstract: true,
    isSetupNode: true,
    indexes: [{ orderBy: { key: +1 }, isUnique: true }],
})
export class SetupNodeNoNaturalKey extends Node {
    @decorators.stringProperty<SetupNodeNoNaturalKey, 'key'>({
        isPublished: true,
        dataType: () => codeDataType,
        isStored: true,
    })
    readonly key: Promise<string>;
}
