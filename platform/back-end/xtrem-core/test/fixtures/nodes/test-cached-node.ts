import { decorators, Node, StringDataType } from '../../../index';

@decorators.node<TestCachedNode>({
    storage: 'sql',
    isCached: true,
    indexes: [{ orderBy: { value: 1 }, isUnique: true, isNaturalKey: true }],
})
export class TestCachedNode extends Node {
    @decorators.stringProperty<TestCachedNode, 'value'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 100 }),
    })
    readonly value: Promise<string>;

    @decorators.stringProperty<TestCachedNode, 'value2'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 100 }),
    })
    readonly value2: Promise<string>;
}
