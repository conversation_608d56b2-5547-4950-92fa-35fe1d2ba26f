import { decorators, Node, StringDataType } from '../../../lib';

@decorators.node<TestExportValue>({
    storage: 'sql',
    isPublished: true,
    tableName: 'TestExportValue',
    indexes: [],
})
export class TestExportValue extends Node {
    @decorators.stringProperty<TestExportValue, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 30 }),
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestExportValue, 'sensitive'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 100 }),
        exportValue: 'hidden',
    })
    readonly sensitive: Promise<string>;
}
