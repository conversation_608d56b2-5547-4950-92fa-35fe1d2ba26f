import { decorators, Node } from '../../../index';
import { codeDataType, descriptionDataType } from '../data-types/data-types';

@decorators.node<TestNotAllowedFrozenValueChaining>({
    isPublished: true,
    storage: 'sql',
    async isFrozen() {
        return (await this.code) === 'FROZEN';
    },
    indexes: [{ orderBy: { code: +1 }, isUnique: true }],
})
export class TestNotAllowedFrozenValueChaining extends Node {
    @decorators.stringProperty<TestNotAllowedFrozenValueChaining, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestNotAllowedFrozenValueChaining, 'callbackFrozen'>({
        isPublished: true,
        isStored: true,
        async isFrozen() {
            return (await this.callbackFrozen) === 'FROZEN';
        },
        dataType: () => descriptionDataType,
    })
    readonly callbackFrozen: Promise<string>;

    @decorators.stringProperty<TestNotAllowedFrozenValueChaining, 'valueFrozen'>({
        isPublished: true,
        isStored: true,
        isFrozen: true,
        dataType: () => descriptionDataType,
    })
    readonly valueFrozen: Promise<string>;

    @decorators.stringProperty<TestNotAllowedFrozenValueChaining, 'notFrozen'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly notFrozen: Promise<string>;
}
