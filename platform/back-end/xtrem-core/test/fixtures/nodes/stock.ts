import { Reference } from '../../../index';
import { decimal, DecimalDataType, decorators, integer, Node, StringDataType } from '../../../lib';

const quantityDataType = new DecimalDataType({ precision: 9, scale: 3 });
const codeDatatype = new StringDataType({ maxLength: 10 });

@decorators.node<TestStock>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
})
export class TestItem extends Node {
    @decorators.stringProperty<TestItem, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => codeDatatype,
    })
    readonly code: Promise<string>;
}

@decorators.node<TestStock>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
})
export class TestStock extends Node {
    @decorators.stringProperty<TestStock, 'stockSite'>({
        isStored: true,
        isPublished: true,
        dataType: () => codeDatatype,
    })
    readonly stockSite: Promise<string>;

    @decorators.stringProperty<TestStock, 'location'>({
        isStored: true,
        isPublished: true,
        dataType: () => codeDatatype,
    })
    readonly location: Promise<string>;

    @decorators.referenceProperty<TestStock, 'product'>({
        isStored: true,
        isPublished: true,
        node: () => TestItem,
        isNullable: true,
    })
    readonly product: Reference<TestItem>;

    @decorators.integerProperty<TestStock, 'sublot'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly sublot: Promise<integer | null>;

    @decorators.decimalProperty<TestStock, 'quantityInStockUnit'>({
        isStored: true,
        isPublished: true,
        dataType: () => quantityDataType,
    })
    readonly quantityInStockUnit: Promise<decimal>;
}
