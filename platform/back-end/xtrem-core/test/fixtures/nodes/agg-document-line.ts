import { date, decimal, decorators, integer, Node, Reference } from '../../../index';
import { defaultDecimalDataType } from '../data-types/data-types';
import { TestAggDocument } from './agg-document';

@decorators.node<TestAggDocumentLine>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestAggDocumentLine',
    isVitalCollectionChild: true,
    indexes: [
        {
            orderBy: {
                document: 1,
                lineNumber: 1,
            },
            isUnique: true,
        },
    ],
})
export class TestAggDocumentLine extends Node {
    @decorators.integerProperty<TestAggDocumentLine, 'lineNumber'>({
        isPublished: true,
        isStored: true,
    })
    readonly lineNumber: Promise<integer>;

    @decorators.referenceProperty<TestAggDocumentLine, 'document'>({
        isPublished: true,
        isStored: true,
        node: () => TestAggDocument,
        isVitalParent: true,
    })
    readonly document: Reference<TestAggDocument>;

    @decorators.integerProperty<TestAggDocumentLine, 'quantity'>({
        isPublished: true,
        isStored: true,
    })
    readonly quantity: Promise<integer>;

    @decorators.decimalProperty<TestAggDocumentLine, 'amount1'>({
        isPublished: true,
        isStored: true,
        dataType: () => defaultDecimalDataType,
    })
    readonly amount1: Promise<decimal>;

    @decorators.decimalProperty<TestAggDocumentLine, 'amount2'>({
        isPublished: true,
        isStored: true,
        dataType: () => defaultDecimalDataType,
    })
    readonly amount2: Promise<decimal>;

    @decorators.decimalProperty<TestAggDocumentLine, 'amount3'>({
        isPublished: true,
        isStored: true,
        dataType: () => defaultDecimalDataType,
    })
    readonly amount3: Promise<decimal>;

    @decorators.decimalProperty<TestAggDocumentLine, 'amount4'>({
        isPublished: true,
        isStored: true,
        dataType: () => defaultDecimalDataType,
    })
    readonly amount4: Promise<decimal>;

    @decorators.decimalProperty<TestAggDocumentLine, 'amount5'>({
        isPublished: true,
        isStored: true,
        dataType: () => defaultDecimalDataType,
    })
    readonly amount5: Promise<decimal>;

    @decorators.dateProperty<TestAggDocumentLine, 'date'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly date: Promise<date>;
}
