import { TestVitalCollectionParent, TestVitalCollectionSubChild } from '.';
import { Reference } from '../../../index';
import { Collection, decorators, Node } from '../../../lib';
import { codeDataType } from '../data-types/data-types';

@decorators.node<TestVitalCollectionChild>({
    isPublished: true,
    storage: 'sql',
    isVitalCollectionChild: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestVitalCollectionChild extends Node {
    @decorators.stringProperty<TestVitalCollectionChild, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestVitalCollectionChild, 'parent'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => TestVitalCollectionParent,
    })
    readonly parent: Reference<TestVitalCollectionParent>;

    @decorators.collectionProperty<TestVitalCollectionChild, 'children'>({
        isPublished: true,
        isVital: true,
        node: () => TestVitalCollectionSubChild,
        reverseReference: 'parent',
    })
    readonly children: Collection<TestVitalCollectionSubChild>;
}
