import { decorators, Node } from '../../../index';
import { codeDataType, descriptionDataType } from '../data-types/data-types';

@decorators.node<TestFrozenChaining>({
    isPublished: true,
    storage: 'sql',
    async isFrozen() {
        return (await this.code) === 'FROZEN';
    },
    indexes: [{ orderBy: { code: +1 }, isUnique: true }],
})
export class TestFrozenChaining extends Node {
    @decorators.stringProperty<TestFrozenChaining, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestFrozenChaining, 'description'>({
        isPublished: true,
        isStored: true,
        async isFrozen() {
            return (await this.description) === 'FROZEN';
        },
        dataType: () => descriptionDataType,
    })
    readonly description: Promise<string>;

    @decorators.stringProperty<TestFrozenChaining, 'frozen'>({
        isPublished: true,
        isStored: true,
        isFrozen: true,
        dataType: () => descriptionDataType,
    })
    readonly frozen: Promise<string>;

    @decorators.stringProperty<TestFrozenChaining, 'notFrozen'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly notFrozen: Promise<string>;
}

@decorators.node<TestFrozenChainingBase>({
    isPublished: true,
    storage: 'sql',
    isAbstract: true,
    indexes: [{ orderBy: { code: +1 }, isUnique: true }],
})
export class TestFrozenChainingBase extends Node {
    @decorators.stringProperty<TestFrozenChainingBase, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestFrozenChainingBase, 'callbackFrozen'>({
        isPublished: true,
        isStored: true,
        async isFrozen() {
            return (await this.callbackFrozen) === 'FROZEN';
        },
        dataType: () => descriptionDataType,
    })
    readonly callbackFrozen: Promise<string>;

    @decorators.stringProperty<TestFrozenChainingBase, 'valueFrozen'>({
        isPublished: true,
        isStored: true,
        isFrozen: true,
        dataType: () => descriptionDataType,
    })
    readonly valueFrozen: Promise<string>;

    @decorators.stringProperty<TestFrozenChainingBase, 'notFrozen'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly notFrozen: Promise<string>;
}

@decorators.subNode<TestOverrideFrozenChainingValueFalse>({
    isPublished: true,
    extends: () => TestFrozenChainingBase,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
})
export class TestOverrideFrozenChainingValueFalse extends TestFrozenChainingBase {
    @decorators.stringProperty<TestOverrideFrozenChainingValueFalse, 'strFromOverrideNotFrozen'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly strFromOverrideNotFrozen: Promise<string>;

    @decorators.stringPropertyOverride<TestOverrideFrozenChainingValueFalse, 'callbackFrozen'>({
        isFrozen: false,
    })
    override readonly callbackFrozen: Promise<string>;

    @decorators.stringPropertyOverride<TestOverrideFrozenChainingValueFalse, 'valueFrozen'>({})
    override readonly valueFrozen: Promise<string>;

    @decorators.stringPropertyOverride<TestOverrideFrozenChainingValueFalse, 'notFrozen'>({
        isFrozen: false,
    })
    override readonly notFrozen: Promise<string>;
}

@decorators.subNode<TestOverrideFrozenChainingValueTrue>({
    isPublished: true,
    extends: () => TestFrozenChainingBase,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
})
export class TestOverrideFrozenChainingValueTrue extends TestFrozenChainingBase {
    @decorators.stringProperty<TestOverrideFrozenChainingValueTrue, 'strFromOverrideNotFrozen'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly strFromOverrideNotFrozen: Promise<string>;

    @decorators.stringPropertyOverride<TestOverrideFrozenChainingValueTrue, 'callbackFrozen'>({
        isFrozen: true,
    })
    override readonly callbackFrozen: Promise<string>;

    @decorators.stringPropertyOverride<TestOverrideFrozenChainingValueTrue, 'valueFrozen'>({})
    override readonly valueFrozen: Promise<string>;

    @decorators.stringPropertyOverride<TestOverrideFrozenChainingValueTrue, 'notFrozen'>({
        isFrozen: true,
    })
    override readonly notFrozen: Promise<string>;
}

@decorators.subNode<TestOverrideFrozenChainingValue>({
    isPublished: true,
    extends: () => TestFrozenChainingBase,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
})
export class TestOverrideFrozenChainingValue extends TestFrozenChainingBase {
    @decorators.stringProperty<TestOverrideFrozenChainingValue, 'strFromOverrideNotFrozen'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly strFromOverrideNotFrozen: Promise<string>;

    @decorators.stringPropertyOverride<TestOverrideFrozenChainingValue, 'callbackFrozen'>({
        isFrozen: false,
    })
    override readonly callbackFrozen: Promise<string>;

    @decorators.stringPropertyOverride<TestOverrideFrozenChainingValue, 'valueFrozen'>({})
    override readonly valueFrozen: Promise<string>;

    @decorators.stringPropertyOverride<TestOverrideFrozenChainingValue, 'notFrozen'>({
        isFrozen: false,
    })
    override readonly notFrozen: Promise<string>;
}

@decorators.subNode<TestOverrideFrozenChainingCallback>({
    isPublished: true,
    extends: () => TestFrozenChainingBase,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
})
export class TestOverrideFrozenChainingCallback extends TestFrozenChainingBase {
    @decorators.stringProperty<TestOverrideFrozenChainingCallback, 'strFromOverrideNotFrozen'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly strFromOverrideNotFrozen: Promise<string>;

    @decorators.stringPropertyOverride<TestOverrideFrozenChainingCallback, 'callbackFrozen'>({
        async isFrozen() {
            return (await this.callbackFrozen) === 'SUB-FROZEN';
        },
    })
    override readonly callbackFrozen: Promise<string>;

    @decorators.stringPropertyOverride<TestOverrideFrozenChainingCallback, 'notFrozen'>({
        async isFrozen() {
            return (await this.notFrozen) === 'NOT-FROZEN';
        },
    })
    override readonly notFrozen: Promise<string>;
}
