import { decorators, integer, Node } from '../../../index';
import { decimal } from '../../../lib';
import { defaultDecimalDataType } from '../data-types/data-types';

@decorators.node<TestNullable>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [{ orderBy: { id: 1 }, isUnique: true }],
})
export class TestNullable extends Node {
    @decorators.integerProperty<TestNullable, 'id'>({
        isPublished: true,
        isStored: true,
    })
    readonly id: Promise<integer>;

    @decorators.decimalProperty<TestNullable, 'nullableDecimal'>({
        isPublished: true,
        isStored: true,
        dataType: () => defaultDecimalDataType,
        isNullable: true,
    })
    readonly nullableDecimal: Promise<decimal>;
}
