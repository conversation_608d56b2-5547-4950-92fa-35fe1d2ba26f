import { decorators, Node } from '../../../index';
import { codeDataType, descriptionDataType } from '../data-types/data-types';

@decorators.node<TestNotAllowedFrozenCallbackChaining>({
    isPublished: true,
    storage: 'sql',
    async isFrozen() {
        return (await this.code) === 'FROZEN';
    },
    indexes: [{ orderBy: { code: +1 }, isUnique: true }],
})
export class TestNotAllowedFrozenCallbackChaining extends Node {
    @decorators.stringProperty<TestNotAllowedFrozenCallbackChaining, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestNotAllowedFrozenCallbackChaining, 'callbackFrozen'>({
        isPublished: true,
        isStored: true,
        async isFrozen() {
            return (await this.callbackFrozen) === 'FROZEN';
        },
        dataType: () => descriptionDataType,
    })
    readonly callbackFrozen: Promise<string>;

    @decorators.stringProperty<TestNotAllowedFrozenCallbackChaining, 'valueFrozen'>({
        isPublished: true,
        isStored: true,
        isFrozen: true,
        dataType: () => descriptionDataType,
    })
    readonly valueFrozen: Promise<string>;

    @decorators.stringProperty<TestNotAllowedFrozenCallbackChaining, 'notFrozen'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly notFrozen: Promise<string>;
}
