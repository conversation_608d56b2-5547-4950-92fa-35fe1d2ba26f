import { TestReferred } from '.';
import { fixtures } from '../..';
import { Collection, Context, decorators, Node, Reference } from '../../../index';
import { codeDataType, descriptionDataType } from '../data-types/data-types';

@decorators.node<TestActivity>({
    isPublished: true,
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canDelete: true,
    canUpdate: true,
    indexes: [{ orderBy: { code: +1 }, isUnique: true }],
})
export class TestActivity extends Node {
    @decorators.stringProperty<TestActivity, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestActivity, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly description: Promise<string>;

    @decorators.collectionProperty<TestActivity, 'lines'>({
        isPublished: true,
        isVital: true,
        node: () => fixtures.nodes.TestActiLine,
        reverseReference: 'testActivity',
    })
    readonly lines: Collection<TestActiLine>;

    @decorators.referenceProperty<TestActivity, 'reference'>({
        isStored: true,
        isPublished: true,
        node: () => fixtures.nodes.TestReferred,
        isNullable: true,
    })
    readonly reference: Reference<TestReferred | null>;

    @decorators.mutation<typeof TestActivity, 'testMutation'>({
        isPublished: true,
        parameters: [{ name: 'something', type: 'string' }],
        return: 'string',
    })
    static testMutation(context: Context, something: string): string {
        return something.toLowerCase();
    }

    @decorators.query<typeof TestActivity, 'report'>({
        isPublished: true,
        parameters: [{ name: 'test', type: 'string' }],
        return: {
            type: 'string',
        },
    })
    static report(context: Context, test: string) {
        return test.toLowerCase();
    }
}

@decorators.node<TestActiLine>({
    isPublished: true,
    storage: 'sql',
    isVitalCollectionChild: true,
})
export class TestActiLine extends Node {
    @decorators.referenceProperty<TestActiLine, 'testActivity'>({
        isStored: true,
        isPublished: true,
        node: () => TestActivity,
        isVitalParent: true,
    })
    readonly testActivity: Reference<TestActivity>;

    @decorators.collectionProperty<TestActiLine, 'details'>({
        isPublished: true,
        isVital: true,
        node: () => fixtures.nodes.TestActiLineDetail,
        reverseReference: 'testActiLine',
    })
    readonly details: Collection<TestActiLineDetail>;
}

@decorators.node<TestActiLineDetail>({
    isPublished: true,
    storage: 'sql',
    isVitalCollectionChild: true,
})
export class TestActiLineDetail extends Node {
    @decorators.referenceProperty<TestActiLineDetail, 'testActiLine'>({
        isStored: true,
        isPublished: true,
        node: () => TestActiLine,
        isVitalParent: true,
    })
    readonly testActiLine: Reference<TestActiLine>;
}
