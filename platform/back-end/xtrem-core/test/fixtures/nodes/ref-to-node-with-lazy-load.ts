import { decorators, Node, Reference } from '../../../index';
import { TestDatatypes } from './datatypes';

@decorators.node<TestRefToNodeWithLazyLoading>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
})
export class TestRefToNodeWithLazyLoading extends Node {
    @decorators.referenceProperty<TestRefToNodeWithLazyLoading, 'reference'>({
        isPublished: true,
        isStored: true,
        node: () => TestDatatypes,
    })
    readonly reference: Reference<TestDatatypes>;
}
