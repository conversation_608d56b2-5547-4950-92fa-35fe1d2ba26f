import { Collection, decorators, Node, Reference } from '../../../index';
import { codeDataType } from '../data-types/data-types';

@decorators.node<TestFilterNestedGrandParent>({
    isPublished: true,
    storage: 'sql',
})
export class TestFilterNestedGrandParent extends Node {
    @decorators.stringProperty<TestFilterNestedGrandParent, 'text'>({
        isPublished: true,
        dataType: () => codeDataType,
        isStored: true,
    })
    readonly text: Promise<string>;

    @decorators.collectionProperty<TestFilterNestedGrandParent, 'children'>({
        isPublished: true,
        node: () => TestFilterNestedParent,
        reverseReference: 'parent',
    })
    readonly children: Collection<TestFilterNestedParent>;
}

@decorators.node<TestFilterNestedParent>({
    isPublished: true,
    storage: 'sql',
})
export class TestFilterNestedParent extends Node {
    @decorators.integerProperty<TestFilterNestedParent, 'intVal'>({
        isPublished: true,
        isStored: true,
    })
    readonly intVal: Promise<number>;

    @decorators.stringProperty<TestFilterNestedParent, 'text'>({
        isPublished: true,
        dataType: () => codeDataType,
        isStored: true,
    })
    readonly text: Promise<string>;

    @decorators.referenceProperty<TestFilterNestedParent, 'parent'>({
        isPublished: true,
        isStored: true,
        node: () => TestFilterNestedGrandParent,
    })
    readonly parent: Reference<TestFilterNestedGrandParent>;

    @decorators.collectionProperty<TestFilterNestedParent, 'children'>({
        isPublished: true,
        node: () => TestFilterNestedChild,
        reverseReference: 'parent',
    })
    readonly children: Collection<TestFilterNestedChild>;
}

@decorators.node<TestFilterNestedChild>({
    isPublished: true,
    storage: 'sql',
})
export class TestFilterNestedChild extends Node {
    @decorators.integerProperty<TestFilterNestedChild, 'intVal'>({
        isPublished: true,
        isStored: true,
    })
    readonly intVal: Promise<number>;

    @decorators.integerProperty<TestFilterNestedChild, 'intVal2'>({
        isPublished: true,
        isStored: true,
    })
    readonly intVal2: Promise<number>;

    @decorators.stringProperty<TestFilterNestedChild, 'text'>({
        isPublished: true,
        dataType: () => codeDataType,
        isStored: true,
    })
    readonly text: Promise<string>;

    @decorators.referenceProperty<TestFilterNestedChild, 'parent'>({
        isPublished: true,
        isStored: true,
        node: () => TestFilterNestedParent,
    })
    readonly parent: Reference<TestFilterNestedParent>;
}
