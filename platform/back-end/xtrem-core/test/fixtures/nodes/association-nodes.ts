import { Collection, decorators, Node, Reference } from '../../../lib';
import { name } from '../data-types/data-types';

@decorators.node<TestStudent>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    indexes: [{ orderBy: { name: 1 }, isUnique: true, isNaturalKey: true }],
})
export class TestStudent extends Node {
    @decorators.stringProperty<TestStudent, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => name,
    })
    readonly name: Promise<string>;

    @decorators.collectionProperty<TestStudent, 'courses'>({
        isPublished: true,
        isAssociation: true,
        node: () => TestStudentCourse,
        reverseReference: 'student',
    })
    readonly courses: Collection<TestStudentCourse>;
}

@decorators.node<TestCourse>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    indexes: [{ orderBy: { name: 1 }, isUnique: true, isNaturalKey: true }],
})
export class TestCourse extends Node {
    @decorators.stringProperty<TestCourse, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => name,
    })
    readonly name: Promise<string>;

    @decorators.collectionProperty<TestCourse, 'students'>({
        isPublished: true,
        isAssociation: true,
        node: () => TestStudentCourse,
        reverseReference: 'course',
    })
    readonly students: Collection<TestStudentCourse>;
}

@decorators.node<TestStudentCourse>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    isAssociationCollectionChild: true,
})
export class TestStudentCourse extends Node {
    @decorators.referenceProperty<TestStudentCourse, 'student'>({
        isStored: true,
        isPublished: true,
        isAssociationParent: true,
        node: () => TestStudent,
    })
    readonly student: Reference<TestStudent>;

    @decorators.referenceProperty<TestStudentCourse, 'course'>({
        isStored: true,
        isPublished: true,
        isAssociationParent: true,
        node: () => TestCourse,
    })
    readonly course: Reference<TestCourse>;

    @decorators.stringProperty<TestStudentCourse, 'someText'>({
        isStored: true,
        isPublished: true,
        dataType: () => name,
    })
    readonly someText: Promise<string>;
}

@decorators.node<TestCampus>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    indexes: [{ orderBy: { name: 1 }, isUnique: true, isNaturalKey: true }],
})
export class TestCampus extends Node {
    @decorators.stringProperty<TestCampus, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => name,
    })
    readonly name: Promise<string>;

    @decorators.collectionProperty<TestCampus, 'studentCourses'>({
        isPublished: true,
        isAssociation: true,
        node: () => TestStudentCourseCampus,
        reverseReference: 'campus',
    })
    readonly studentCourses: Collection<TestStudentCourseCampus>;
}

@decorators.node<TestStudentCourseCampus>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    isAssociationCollectionChild: true,
})
export class TestStudentCourseCampus extends Node {
    @decorators.referenceProperty<TestStudentCourseCampus, 'student'>({
        isStored: true,
        isPublished: true,
        isAssociationParent: true,
        node: () => TestStudent,
    })
    readonly student: Reference<TestStudent>;

    @decorators.referenceProperty<TestStudentCourseCampus, 'course'>({
        isStored: true,
        isPublished: true,
        isAssociationParent: true,
        node: () => TestCourse,
    })
    readonly course: Reference<TestCourse>;

    @decorators.referenceProperty<TestStudentCourseCampus, 'campus'>({
        isStored: true,
        isPublished: true,
        isAssociationParent: true,
        node: () => TestCampus,
    })
    readonly campus: Reference<TestCampus>;

    @decorators.stringProperty<TestStudentCourseCampus, 'someText'>({
        isStored: true,
        isPublished: true,
        dataType: () => name,
    })
    readonly someText: Promise<string>;
}

// Nodes for testing association with a reference and a collection
@decorators.node<TestReference>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    indexes: [{ orderBy: { name: 1 }, isUnique: true, isNaturalKey: true }],
})
export class TestReference extends Node {
    @decorators.stringProperty<TestReference, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => name,
    })
    readonly name: Promise<string>;

    @decorators.referenceProperty<TestReference, 'reference'>({
        isPublished: true,
        isAssociation: true,
        node: () => TestReferenceCollection,
        reverseReference: 'reference',
    })
    readonly reference: Reference<TestReferenceCollection>;
}
@decorators.node<TestCollection>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    indexes: [{ orderBy: { name: 1 }, isUnique: true, isNaturalKey: true }],
})
export class TestCollection extends Node {
    @decorators.stringProperty<TestCollection, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => name,
    })
    readonly name: Promise<string>;

    @decorators.collectionProperty<TestCollection, 'collection'>({
        isPublished: true,
        isAssociation: true,
        node: () => TestReferenceCollection,
        reverseReference: 'collection',
    })
    readonly collection: Collection<TestReferenceCollection>;
}

@decorators.node<TestReferenceCollection>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    isAssociationCollectionChild: true,
    isAssociationReferenceChild: true,
})
export class TestReferenceCollection extends Node {
    @decorators.referenceProperty<TestReferenceCollection, 'reference'>({
        isStored: true,
        isPublished: true,
        isAssociationParent: true,
        node: () => TestReference,
    })
    readonly reference: Reference<TestReference>;

    @decorators.referenceProperty<TestReferenceCollection, 'collection'>({
        isStored: true,
        isPublished: true,
        isAssociationParent: true,
        node: () => TestCollection,
    })
    readonly collection: Reference<TestCollection>;

    @decorators.stringProperty<TestReferenceCollection, 'someText'>({
        isStored: true,
        isPublished: true,
        dataType: () => name,
    })
    readonly someText: Promise<string>;
}
