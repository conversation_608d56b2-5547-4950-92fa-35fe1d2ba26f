import { decorators, Node } from '../../../index';
import { descriptionDataType } from '../data-types/data-types';

@decorators.node<TestReferencedDocumentDetails>({
    isPublished: true,
    storage: 'sql',
})
export class TestReferencedDocumentDetails extends Node {
    @decorators.stringProperty<TestReferencedDocumentDetails, 'text'>({
        isStored: true,
        isPublished: true,
        dataType: () => descriptionDataType,
    })
    readonly text: Promise<string>;
}
