import { Reference } from '../../../index';
import { decorators, Node } from '../../../lib';
import { codeDataType } from '../data-types/_index';
import { TestDefaultValuesReference } from './test-default-values-reference';

@decorators.node<TestDefaultValuesAdvanced>({
    storage: 'sql',
    isPublished: true,
})
export class TestDefaultValuesAdvanced extends Node {
    @decorators.referenceProperty<TestDefaultValuesAdvanced, 'reference1'>({
        isStored: true,
        isPublished: true,
        node: () => TestDefaultValuesReference,
    })
    readonly reference1: Reference<TestDefaultValuesReference>;

    @decorators.referenceProperty<TestDefaultValuesAdvanced, 'reference2'>({
        isStored: true,
        isPublished: true,
        node: () => TestDefaultValuesReference,
        defaultValue() {
            return this.reference1;
        },
    })
    readonly reference2: Reference<TestDefaultValuesReference>;

    @decorators.stringProperty<TestDefaultValuesAdvanced, 'text1'>({
        isStored: true,
        isPublished: true,
        dataType: () => codeDataType,
        async defaultValue() {
            return (await this.reference2).code;
        },
    })
    readonly text1: Promise<string>;

    @decorators.stringProperty<TestDefaultValuesAdvanced, 'text2'>({
        isStored: true,
        isPublished: true,
        dataType: () => codeDataType,
        defaultValue: 'hello',
    })
    readonly text2: Promise<string>;
}
