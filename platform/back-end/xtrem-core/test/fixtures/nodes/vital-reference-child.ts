import { Reference } from '../../../index';
import { decorators, Node } from '../../../lib';
import { codeDataType, descriptionDataType } from '../data-types/data-types';
import { TestVitalReferenceParent } from './vital-reference-parent';

@decorators.node<TestVitalReferenceChildMandatory>({
    isPublished: true,
    storage: 'sql',
    isVitalReferenceChild: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestVitalReferenceChildMandatory extends Node {
    @decorators.referenceProperty<TestVitalReferenceChildMandatory, 'parent'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => TestVitalReferenceParent,
    })
    readonly parent: Reference<TestVitalReferenceParent>;

    @decorators.stringProperty<TestVitalReferenceChildMandatory, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => codeDataType,
        async defaultValue() {
            return `${await (await this.parent).code}_CHILD`;
        },
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestVitalReferenceChildMandatory, 'text'>({
        isStored: true,
        isPublished: true,
        dataType: () => descriptionDataType,
        async control(cx, val): Promise<void> {
            await cx.error.if(val).is.equal.to('bad text');
        },
    })
    readonly text: Promise<string>;
}

@decorators.node<TestVitalReferenceChildOptional>({
    isPublished: true,
    storage: 'sql',
    isVitalReferenceChild: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestVitalReferenceChildOptional extends Node {
    @decorators.referenceProperty<TestVitalReferenceChildOptional, 'parent'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => TestVitalReferenceParent,
    })
    readonly parent: Reference<TestVitalReferenceParent>;

    @decorators.stringProperty<TestVitalReferenceChildOptional, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestVitalReferenceChildOptional, 'text'>({
        isStored: true,
        isPublished: true,
        dataType: () => descriptionDataType,
        async control(cx, val): Promise<void> {
            await cx.error.if(val).is.equal.to('bad text');
        },
    })
    readonly text: Promise<string>;

    @decorators.referenceProperty<TestVitalReferenceChildOptional, 'nonVitalRef'>({
        isStored: true,
        isPublished: true,
        isNullable: false,
        node: () => TestNonVitalReference,
    })
    readonly nonVitalRef: Reference<TestNonVitalReference>;
}

@decorators.node<TestNonVitalReference>({
    isPublished: true,
    storage: 'sql',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestNonVitalReference extends Node {
    @decorators.stringProperty<TestNonVitalReference, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestNonVitalReference, 'text'>({
        isStored: true,
        isPublished: true,
        dataType: () => descriptionDataType,
    })
    readonly text: Promise<string>;
}
