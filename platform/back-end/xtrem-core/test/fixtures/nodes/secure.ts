import { Context, decorators, Node, Reference } from '../../../index';
import { codeDataType, descriptionDataType } from '../data-types/data-types';

@decorators.node<TestSite>({
    isPublished: true,
    storage: 'sql',
    authorizationCode: 'SECURE',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
    provides: ['site'],
})
export class TestSite extends Node {
    @decorators.stringProperty<TestSite, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;
}

@decorators.node<TestSecure>({
    isPublished: true,
    storage: 'sql',
    authorizationCode: 'SECURE',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestSecure extends Node {
    @decorators.stringProperty<TestSecure, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestSecure, 'site'>({
        isPublished: true,
        isStored: true,
        node: () => TestSite,
        isNullable: true,
        provides: ['site'],
    })
    readonly site: Reference<TestSite | null>;

    @decorators.stringProperty<TestSecure, 'access'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
        provides: ['accessCode'],
    })
    readonly access: Promise<string>;

    @decorators.query<typeof TestSecure, 'lookupQueryWithoutGrant'>({
        isPublished: true,
        parameters: [{ name: 'param1', type: 'string' }],
        return: 'string',
    })
    static lookupQueryWithoutGrant(context: Context, param1: string): string {
        return param1;
    }

    @decorators.query<typeof TestSecure, 'lookupQueryWithGrant'>({
        isPublished: true,
        parameters: [{ name: 'param1', type: 'string' }],
        return: 'string',
        isGrantedByLookup: true,
    })
    static lookupQueryWithGrant(context: Context, param1: string): string {
        return param1;
    }
}
