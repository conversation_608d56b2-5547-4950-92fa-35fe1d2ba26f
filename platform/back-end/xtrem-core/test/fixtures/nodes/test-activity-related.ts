import { fixtures } from '../..';
import { Context, decorators, Node, Reference } from '../../../index';
import { codeDataType, descriptionDataType } from '../data-types/data-types';
import { TestCat } from './subclassing';

@decorators.node<TestActivityRelated>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    indexes: [{ orderBy: { code: +1 }, isUnique: true }],
})
export class TestActivityRelated extends Node {
    @decorators.stringProperty<TestActivityRelated, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestActivityRelated, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly description: Promise<string>;

    @decorators.referenceProperty<TestActivityRelated, 'cat'>({
        isPublished: true,
        isStored: true,
        node: () => fixtures.nodes.TestCat,
    })
    readonly cat: Reference<TestCat>;

    @decorators.query<typeof TestActivityRelated, 'reportA'>({
        isPublished: true,
        parameters: [{ name: 'test', type: 'string' }],
        return: {
            type: 'string',
        },
    })
    static reportA(context: Context, test: string) {
        return test.toLowerCase();
    }

    @decorators.query<typeof TestActivityRelated, 'reportB'>({
        isPublished: true,
        parameters: [{ name: 'test', type: 'string' }],
        return: {
            type: 'string',
        },
    })
    static reportB(context: Context, test: string) {
        return test.toUpperCase();
    }

    @decorators.query<typeof TestActivityRelated, 'reportAExt'>({
        isPublished: true,
        parameters: [{ name: 'test', type: 'string' }],
        return: {
            type: 'string',
        },
    })
    static reportAExt(context: Context, test: string) {
        return test.toUpperCase();
    }
}
