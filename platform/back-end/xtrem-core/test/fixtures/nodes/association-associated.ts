import { Collection, decorators, Node } from '../../../lib';
import { codeDataType, descriptionDataType } from '../data-types/data-types';
import { TestAssociationItem } from './association-item';

@decorators.node<TestAssociationAssociated>({
    isPublished: true,
    storage: 'sql',
    indexes: [{ orderBy: { code: 1 }, isUnique: true, isNaturalKey: true }],
})
export class TestAssociationAssociated extends Node {
    @decorators.stringProperty<TestAssociationAssociated, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestAssociationAssociated, 'text'>({
        isStored: true,
        isPublished: true,
        dataType: () => descriptionDataType,
        async control(cx, val): Promise<void> {
            await cx.error.if(val).is.equal.to('bad text');
        },
    })
    readonly text: Promise<string>;

    @decorators.collectionProperty<TestAssociationAssociated, 'associationItems'>({
        isPublished: true,
        isAssociation: true,
        node: () => TestAssociationItem,
        reverseReference: 'associated',
    })
    readonly associationItems: Collection<TestAssociationItem>;
}
