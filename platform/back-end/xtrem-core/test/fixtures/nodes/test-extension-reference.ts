import { decorators, Node } from '../../../lib';
import { codeDataType } from '../data-types/data-types';

@decorators.node<TestExtensionReference>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestExtensionReference',
    indexes: [{ orderBy: { refCode: 1 }, isUnique: true }],
})
export class TestExtensionReference extends Node {
    @decorators.stringProperty<TestExtensionReference, 'refCode'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly refCode: Promise<string>;

    @decorators.stringProperty<TestExtensionReference, 'refExtensionCode'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly refExtensionCode: Promise<string>;
}
