import { Collection, decorators, Node, Reference } from '../../../index';
import { codeDataType } from '../data-types/data-types';

@decorators.node<TestBaseCertificate>({
    storage: 'sql',
    isPublished: true,
    isAbstract: true,
})
export class TestBaseCertificate extends Node {
    @decorators.stringProperty<TestBaseCertificate, 'code'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;
}

@decorators.subNode<TestSupplierCertificate>({
    extends: () => TestBaseCertificate,
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canSearch: true,
    canRead: true,
    isVitalCollectionChild: true,
})
export class TestSupplierCertificate extends TestBaseCertificate {
    @decorators.referenceProperty<TestSupplierCertificate, 'supplier'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        isVitalParent: true,
        node: () => TestSupplier,
    })
    readonly supplier: Reference<TestSupplier>;
}

@decorators.node<TestSupplier>({
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    isPublished: true,
    indexes: [
        {
            orderBy: { code: 1 },
            isUnique: true,
        },
    ],
})
export class TestSupplier extends Node {
    @decorators.stringProperty<TestSupplier, 'code'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.collectionProperty<TestSupplier, 'certificates'>({
        node: () => TestSupplierCertificate,
        reverseReference: 'supplier',
        isPublished: true,
        isVital: true,
    })
    readonly certificates: Collection<TestSupplierCertificate>;
}
