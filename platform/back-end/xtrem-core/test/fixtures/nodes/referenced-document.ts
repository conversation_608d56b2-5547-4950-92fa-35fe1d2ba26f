import { decorators, Node, Reference } from '../../../index';
import { integer } from '../../../lib';
import { descriptionDataType } from '../data-types/data-types';
import { TestReferencedDocumentDetails } from './referenced-document-details';

@decorators.node<TestReferencedDocument>({
    isPublished: true,
    storage: 'sql',
})
export class TestReferencedDocument extends Node {
    @decorators.stringProperty<TestReferencedDocument, 'lookupString'>({
        isStored: true,
        isPublished: true,
        dataType: () => descriptionDataType,
    })
    readonly lookupString: Promise<string>;

    @decorators.integerProperty<TestReferencedDocument, 'controlInteger'>({
        isStored: true,
        isPublished: true,
    })
    readonly controlInteger: Promise<integer>;

    @decorators.referenceProperty<TestReferencedDocument, 'controlDetails'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => TestReferencedDocumentDetails,
    })
    readonly controlDetails: Reference<TestReferencedDocumentDetails | null>;
}
