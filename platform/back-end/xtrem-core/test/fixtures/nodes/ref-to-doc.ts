import { TestDocument } from '.';
import { decorators, Node, Reference, ValidationSeverity } from '../../../index';
import { codeDataType } from '../data-types/data-types';
import * as fixtures from '../index';

@decorators.node<TestRefToDoc>({
    isPublished: true,
    storage: 'sql',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
    async controlDelete(cx): Promise<void> {
        if ((await this.code).startsWith('DONOTDELETE')) {
            cx.addDiagnose(ValidationSeverity.error, `TestRefToDoc(${await this.code}) MUST NOT BE DELETED`);
        }
    },
    deleteEnd(): void {},
})
export class TestRefToDoc extends Node {
    @decorators.stringProperty<TestRefToDoc, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestRefToDoc, 'vitalDoc'>({
        isPublished: true,
        node: () => fixtures.nodes.TestDocument,
        isStored: true,
    })
    readonly vitalDoc: Reference<TestDocument>;

    @decorators.referenceProperty<TestRefToDoc, 'forbidDoc'>({
        isPublished: true,
        node: () => fixtures.nodes.TestDocument,
        isStored: true,
        isNullable: true,
    })
    readonly forbidDoc: Reference<TestDocument | null>;
}
