import { decorators, Node, Reference } from '../../../index';
import { codeDataType } from '../data-types/data-types';
import { TestAggDocument } from './agg-document';

@decorators.node<TestAggDocumentReverseRef>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestAggDocumentReverseRef',
    isVitalReferenceChild: true,
})
export class TestAggDocumentReverseRef extends Node {
    @decorators.referenceProperty<TestAggDocumentReverseRef, 'document'>({
        isPublished: true,
        isStored: true,
        node: () => TestAggDocument,
        isVitalParent: true,
    })
    readonly document: Reference<TestAggDocument>;

    @decorators.stringProperty<TestAggDocumentReverseRef, 'code'>({
        isStored: true,
        dataType: () => codeDataType,
        isPublished: true,
    })
    readonly code: Promise<string>;
}
