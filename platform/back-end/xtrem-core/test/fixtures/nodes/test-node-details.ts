import { BaseMutationDecorator, Context, Node, StaticThis, decorators } from '../../../index';
import { descriptionDataType } from '../data-types/data-types';
import { TestEnum, testEnumDataType } from '../enums';

export interface AsyncMutationDecorator<This extends StaticThis<Node>, K extends keyof This>
    extends BaseMutationDecorator<This, K> {
    /** Can the mutation be scheduled as a batch job */
    isSchedulable?: boolean;
}

@decorators.node<TestNodeDetails>({
    tableName: 'TestNodeDetails', // Force the class to be classic (X3 compatible)
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [{ orderBy: { name: 1 }, isUnique: true }],
})
export class TestNodeDetails extends Node {
    @decorators.stringProperty<TestNodeDetails, 'name'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly name: Promise<string>;

    @decorators.booleanProperty<TestNodeDetails, 'isLocalized'>({
        isPublished: true,
        isStored: true,
    })
    readonly isLocalized: Promise<boolean>;

    @decorators.booleanProperty<TestNodeDetails, 'isLocalizedComputed'>({
        isPublished: true,
        computeValue() {
            return true;
        },
    })
    readonly isLocalizedComputed: Promise<boolean>;

    @decorators.booleanProperty<TestNodeDetails, 'isLocalizedGetValue'>({
        isPublished: true,
        getValue() {
            return this.isLocalized;
        },
    })
    readonly isLocalizedGetValue: Promise<boolean>;

    @decorators.enumProperty<TestNodeDetails, 'testBaseEnum'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => testEnumDataType,
    })
    readonly testBaseEnum: Promise<TestEnum | null>;

    @decorators.mutation<typeof TestNodeDetails, 'testCreate'>({
        isPublished: true,
        parameters: [
            { name: 'name', type: 'string', isMandatory: true },
            { name: 'isLocalized', type: 'boolean', isMandatory: true },
        ],
        return: {
            type: 'instance',
            node(): typeof TestNodeDetails {
                return TestNodeDetails;
            },
        },
    })
    static async testCreate(context: Context, name: string, isLocalized: boolean): Promise<TestNodeDetails> {
        const base = await context.create(TestNodeDetails, { name, isLocalized });
        return base;
    }

    @decorators.query<typeof TestNodeDetails, 'testQuery'>({
        isPublished: true,
        parameters: [{ name: 'name', type: 'string', isMandatory: true }],
        return: {
            type: 'instance',
            node(): typeof TestNodeDetails {
                return TestNodeDetails;
            },
        },
    })
    static async testQuery(context: Context, name: string): Promise<TestNodeDetails> {
        const base = await context.read(TestNodeDetails, { name });
        return base;
    }
}
