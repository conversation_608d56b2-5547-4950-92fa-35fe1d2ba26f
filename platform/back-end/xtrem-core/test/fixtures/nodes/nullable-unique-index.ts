import { date, decorators, integer, Node, Reference } from '../../../index';
import { testEnumNullableDataType, TestEnumNullableEnum } from '../enums';
import { TestDatatypes } from './datatypes';

@decorators.node<TestNullableUniqueIndex>({
    isPublished: true,
    storage: 'sql',
    indexes: [
        { orderBy: { id: +1, reference: +1 }, isUnique: true },
        { orderBy: { id: +1, integerVal: +1 }, isUnique: true },
        { orderBy: { id: +1, enumVal: +1 }, isUnique: true },
        { orderBy: { id: +1, dateVal: +1 }, isUnique: true },
    ],
})
export class TestNullableUniqueIndex extends Node {
    @decorators.integerProperty<TestNullableUniqueIndex, 'id'>({
        isPublished: true,
        isStored: true,
    })
    readonly id: Promise<integer>;

    @decorators.referenceProperty<TestNullableUniqueIndex, 'reference'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        allowedInUniqueIndex: true,
        node: () => TestDatatypes,
    })
    readonly reference: Reference<TestDatatypes | null>;

    @decorators.integerProperty<TestNullableUniqueIndex, 'integerVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        allowedInUniqueIndex: true,
    })
    readonly integerVal: Promise<integer | null>;

    @decorators.enumProperty<TestNullableUniqueIndex, 'enumVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        allowedInUniqueIndex: true,
        dataType: () => testEnumNullableDataType,
    })
    readonly enumVal: Promise<TestEnumNullableEnum | null>;

    @decorators.dateProperty<TestNullableUniqueIndex, 'dateVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        allowedInUniqueIndex: true,
    })
    readonly dateVal: Promise<date | null>;
}
