import { LogicError } from '@sage/xtrem-shared';
import { TestDefaultValuesReference } from '.';
import { Reference } from '../../../index';
import { Collection, decorators, Node, StringDataType } from '../../../lib';

@decorators.node<TestDefaultValue>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    tableName: 'TestDefaultValue',
    indexes: [],
})
export class TestDefaultValue extends Node {
    @decorators.booleanProperty<TestDefaultValue, 'booleanNoDefault'>({
        isStored: true,
        isPublished: true,
    })
    readonly booleanNoDefault: Promise<boolean>;

    @decorators.booleanProperty<TestDefaultValue, 'booleanDefaultTrueLiteral'>({
        isStored: true,
        defaultValue: true,
        isPublished: true,
    })
    readonly booleanDefaultTrueLiteral: Promise<boolean>;

    @decorators.booleanProperty<TestDefaultValue, 'booleanDefaultTrueFunction'>({
        isStored: true,
        isPublished: true,
        defaultValue() {
            return true;
        },
    })
    readonly booleanDefaultTrueFunction: Promise<boolean>;

    @decorators.stringProperty<TestDefaultValue, 'stringDefaultFunction'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 30 }),
        async defaultValue() {
            return (await this.booleanDefaultTrueLiteral) ? 'true' : 'false';
        },
    })
    readonly stringDefaultFunction: Promise<string>;

    @decorators.booleanProperty<TestDefaultValue, 'booleanNullDefault'>({
        isStored: true,
        isPublished: true,
        // https://jira.sage.com/browse/XT-4720: the 'defaultValue' decorator is allowed to return null, even for a
        // non-nullable property: the user will have to provide a valid value for this property before saving the node
        defaultValue: null,
    })
    readonly booleanNullDefault: Promise<boolean>;

    @decorators.dateProperty<TestDefaultValue, 'dateNullDefault'>({
        isStored: true,
        isPublished: true,
        // https://jira.sage.com/browse/XT-4720: the 'defaultValue' decorator is allowed to return null, even for a
        // non-nullable property: the user will have to provide a valid value for this property before saving the node
        defaultValue: null,
    })
    readonly dateNullDefault: Promise<boolean>;

    @decorators.referenceProperty<TestDefaultValue, 'referenceNullDefault'>({
        isStored: true,
        isPublished: true,
        node: () => TestDefaultValuesReference,
        // https://jira.sage.com/browse/XT-4720: the 'defaultValue' decorator is allowed to return null, even for a
        // non-nullable property: the user will have to provide a valid value for this property before saving the node
        defaultValue: null,
    })
    readonly referenceNullDefault: Reference<TestDefaultValuesReference>;

    @decorators.referenceProperty<TestDefaultValue, 'referenceNotNullDefault'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => TestDefaultValuesReference,
        defaultValue(): Promise<any> {
            return this.$.context.read(TestDefaultValuesReference, { _id: 1 });
        },
    })
    readonly referenceNotNullDefault: Reference<TestDefaultValuesReference | null>;

    @decorators.collectionProperty<TestDefaultValue, 'failingComputedCollection'>({
        isPublished: true,
        node: () => TestDefaultValuesReference,
        computeValue(): Promise<any> {
            throw new LogicError('throwing for test');
        },
    })
    readonly failingComputedCollection: Collection<TestDefaultValuesReference>;
}
