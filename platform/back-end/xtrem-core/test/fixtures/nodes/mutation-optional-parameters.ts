import { Context, decorators, Node } from '../../../lib';
import { name } from '../data-types/data-types';

@decorators.node<MutationOptionalParameters>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [],
})
export class MutationOptionalParameters extends Node {
    @decorators.stringProperty<MutationOptionalParameters, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => name,
    })
    readonly name: Promise<string>;

    @decorators.mutation<typeof MutationOptionalParameters, 'testMutationOptional'>({
        isPublished: true,
        parameters: [
            { name: 'parm1Mandatory', type: 'string', isMandatory: true },
            { name: 'parm2Optional', type: 'string', isMandatory: false },
            { name: 'parm3Optional', type: 'string', isMandatory: false },
            { name: 'parm4Optional', type: 'string', isMandatory: false },
        ],
        return: {
            type: 'object',
            properties: {
                parm1: 'string',
                parm2: 'string',
                parm3: 'string',
                parm4: 'string',
            },
        },
    })
    static testMutationOptional(
        context: Context,
        parm1Mandatory = '',
        parm2Optional = '',
        parm3Optional = '',
        parm4Optional = '',
    ) {
        const graphQLPayload = {
            parm1: parm1Mandatory,
            parm2: parm2Optional,
            parm3: parm3Optional,
            parm4: parm4Optional,
        };
        return Promise.resolve(graphQLPayload);
    }
}
