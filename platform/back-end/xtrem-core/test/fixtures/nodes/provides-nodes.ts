import { Reference } from '../../../index';
import { Collection, decorators, Node } from '../../../lib';
import { codeDataType } from '../data-types/data-types';

// Node that provides site
@decorators.node<TestProvidesSite>({
    isPublished: true,
    storage: 'sql',
    provides: ['site'],
    authorizationCode: 'SECURE',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestProvidesSite extends Node {
    @decorators.stringProperty<TestProvidesSite, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;
}

// Root parent node providing site
@decorators.node<TestProvidesParent>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    authorizationCode: 'SECURE',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestProvidesParent extends Node {
    @decorators.stringProperty<TestProvidesParent, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestProvidesParent, 'site'>({
        isStored: true,
        isPublished: true,
        node: () => TestProvidesSite,
        provides: ['site'],
    })
    readonly site: Reference<TestProvidesSite>;

    @decorators.collectionProperty<TestProvidesParent, 'children'>({
        isPublished: true,
        isVital: true,
        node: () => TestProvidesChild,
        reverseReference: 'parent',
    })
    readonly children: Collection<TestProvidesChild>;
}

// Child node where site is provided by the root parent
@decorators.node<TestProvidesChild>({
    isPublished: true,
    storage: 'sql',
    isVitalCollectionChild: true,
    authorizationCode: 'SECURE',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestProvidesChild extends Node {
    @decorators.referenceProperty<TestProvidesChild, 'parent'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => TestProvidesParent,
    })
    readonly parent: Reference<TestProvidesParent>;

    @decorators.stringProperty<TestProvidesChild, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.collectionProperty<TestProvidesChild, 'children'>({
        isPublished: true,
        isVital: true,
        node: () => TestProvidesGrandChild,
        reverseReference: 'parent',
    })
    readonly children: Collection<TestProvidesGrandChild>;
}

// Grand child node where site is provided by the root parent
@decorators.node<TestProvidesGrandChild>({
    isPublished: true,
    storage: 'sql',
    isVitalCollectionChild: true,
    authorizationCode: 'SECURE',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestProvidesGrandChild extends Node {
    @decorators.referenceProperty<TestProvidesGrandChild, 'parent'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => TestProvidesChild,
    })
    readonly parent: Reference<TestProvidesChild>;

    @decorators.stringProperty<TestProvidesGrandChild, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;
}

// Root parent node with the child node providing site
@decorators.node<TestProvidesParentNoSite>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    authorizationCode: 'SECURE',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestProvidesParentNoSite extends Node {
    @decorators.stringProperty<TestProvidesParentNoSite, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.collectionProperty<TestProvidesParentNoSite, 'children'>({
        isPublished: true,
        isVital: true,
        node: () => TestProvidesChildSite,
        reverseReference: 'parent',
    })
    readonly children: Collection<TestProvidesChildSite>;
}

// Child node where site is provided by the child node
@decorators.node<TestProvidesChildSite>({
    isPublished: true,
    storage: 'sql',
    isVitalCollectionChild: true,
    authorizationCode: 'SECURE',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestProvidesChildSite extends Node {
    @decorators.referenceProperty<TestProvidesChildSite, 'parent'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => TestProvidesParentNoSite,
    })
    readonly parent: Reference<TestProvidesParentNoSite>;

    @decorators.stringProperty<TestProvidesChildSite, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestProvidesChildSite, 'site'>({
        isStored: true,
        isPublished: true,
        node: () => TestProvidesSite,
        provides: ['site'],
    })
    readonly site: Reference<TestProvidesSite>;

    @decorators.collectionProperty<TestProvidesChildSite, 'children'>({
        isPublished: true,
        isVital: true,
        node: () => TestProvidesGrandChildSite,
        reverseReference: 'parent',
    })
    readonly children: Collection<TestProvidesGrandChildSite>;
}

// Grand child node where site is provided by the child node (parent of grand child)
@decorators.node<TestProvidesGrandChildSite>({
    isPublished: true,
    storage: 'sql',
    isVitalCollectionChild: true,
    authorizationCode: 'SECURE',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestProvidesGrandChildSite extends Node {
    @decorators.referenceProperty<TestProvidesGrandChildSite, 'parent'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => TestProvidesChildSite,
    })
    readonly parent: Reference<TestProvidesChildSite>;

    @decorators.stringProperty<TestProvidesGrandChildSite, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;
}
