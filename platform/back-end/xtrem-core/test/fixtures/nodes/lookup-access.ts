import { decorators, Node } from '../../../index';
import { codeDataType } from '../data-types/data-types';

@decorators.node<TestLookupAccess>({
    isPublished: true,
    storage: 'sql',
    authorizationCode: 'LOOKUP',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestLookupAccess extends Node {
    @decorators.stringProperty<TestLookupAccess, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
        lookupAccess: true,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestLookupAccess, 'noLookup'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly noLookup: Promise<string>;
}
