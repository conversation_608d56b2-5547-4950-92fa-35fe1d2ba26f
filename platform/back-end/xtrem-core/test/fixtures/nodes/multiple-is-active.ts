import { decorators, Node } from '../../../index';
import { codeDataType } from '../data-types/data-types';

// Don't add this node to the index.ts file, it will break other tests
@decorators.node<MultipleIsActive>({
    isPublished: true,
    storage: 'sql',
    tableName: 'MultipleIsActive',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class MultipleIsActive extends Node {
    @decorators.stringProperty<MultipleIsActive, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.booleanProperty<MultipleIsActive, 'isActive1'>({
        isPublished: true,
        isStored: true,
        provides: ['isActive'],
    })
    readonly isActive1: Promise<boolean>;

    @decorators.booleanProperty<MultipleIsActive, 'isActive2'>({
        isPublished: true,
        isStored: true,
        provides: ['isActive'],
    })
    readonly isActive2: Promise<boolean>;
}
