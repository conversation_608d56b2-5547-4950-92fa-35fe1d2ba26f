import { TestVitalCollectionChild } from '.';
import { Reference } from '../../../index';
import { decorators, Node } from '../../../lib';
import { codeDataType } from '../data-types/data-types';

@decorators.node<TestVitalCollectionSubChild>({
    isPublished: true,
    storage: 'sql',
    isVitalCollectionChild: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestVitalCollectionSubChild extends Node {
    @decorators.stringProperty<TestVitalCollectionSubChild, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestVitalCollectionSubChild, 'parent'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => TestVitalCollectionChild,
    })
    readonly parent: Reference<TestVitalCollectionChild>;
}
