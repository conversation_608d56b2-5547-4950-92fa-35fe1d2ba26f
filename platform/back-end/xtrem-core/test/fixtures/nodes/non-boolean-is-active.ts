import { decorators, Node } from '../../../index';
import { codeDataType } from '../data-types/data-types';

// Don't add this node to the index.ts file, it will break other tests
@decorators.node<NonBooleanIsActive>({
    isPublished: true,
    storage: 'sql',
    tableName: 'NonBooleanIsActive',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class NonBooleanIsActive extends Node {
    @decorators.stringProperty<NonBooleanIsActive, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<NonBooleanIsActive, 'isActive'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
        provides: ['isActive'],
    })
    readonly isActive: Promise<string>;
}
