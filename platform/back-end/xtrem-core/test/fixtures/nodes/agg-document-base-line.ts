import { date, decimal, decorators, integer, Node } from '../../../index';
import { defaultDecimalDataType } from '../data-types/data-types';

@decorators.node<TestAggDocumentBaseLine>({
    isPublished: true,
    storage: 'sql',
    isAbstract: true,
})
export class TestAggDocumentBaseLine extends Node {
    @decorators.integerProperty<TestAggDocumentBaseLine, 'lineNumber'>({
        isPublished: true,
        isStored: true,
    })
    readonly lineNumber: Promise<integer>;

    @decorators.integerProperty<TestAggDocumentBaseLine, 'quantity'>({
        isPublished: true,
        isStored: true,
    })
    readonly quantity: Promise<integer>;

    @decorators.decimalProperty<TestAggDocumentBaseLine, 'amount1'>({
        isPublished: true,
        isStored: true,
        dataType: () => defaultDecimalDataType,
    })
    readonly amount1: Promise<decimal>;

    @decorators.dateProperty<TestAggDocumentBaseLine, 'date'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly date: Promise<date>;
}
