import {
    BinaryStream,
    DecimalDataType,
    Node,
    RoundingMode,
    StringDataType,
    ValidationContext,
    ValidationSeverity,
    decimal,
    decorators,
    integer,
} from '../../../index';
import { BinaryStreamDataType } from '../../../lib/types/stream-data-type';
import { codeDataType } from '../data-types/data-types';
import * as fixtures from '../index';

interface HasAdaptableColumns {
    adaptableLength: Promise<number>;
    adaptableDefault: Promise<string>;
}

export class AdvancedStringDataType<T extends HasAdaptableColumns> extends StringDataType<T> {
    public override async defaultValue(node: T): Promise<string> {
        return (await node.adaptableDefault) || ((await super.defaultValue(node)) as string);
    }

    public override async controlValue(node: T, cx: ValidationContext, val: string): Promise<void> {
        await super.controlValue(node, cx, val);

        if (val && val.length > (await node.adaptableLength)) {
            cx.addDiagnose(
                ValidationSeverity.error,
                `${val} exceeds the maximum adaptable length allowed for this field: ${await node.adaptableLength} characters.`,
            );
        }
    }
}

const advancedStringDataType = new AdvancedStringDataType<TestDataTypeEvents>({ maxLength: 12 });
const binary1024 = new BinaryStreamDataType({
    maxLength: 1024,
    allowedContentTypes: ['text/plain', 'application/x-empty', 'application/pdf'],
});

@decorators.node<TestDataTypeEvents>({
    tableName: 'TestDataTypeEvents',
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [{ orderBy: { id: 1 }, isUnique: true }],
})
export class TestDataTypeEvents extends Node {
    @decorators.integerProperty<TestDataTypeEvents, 'id'>({
        isPublished: true,
        isStored: true,
    })
    readonly id: Promise<integer>;

    @decorators.enumProperty<TestDataTypeEvents, 'enumVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => fixtures.enums.testEnumDataType,
    })
    readonly enumVal: Promise<fixtures.enums.TestEnum | null>;

    @decorators.stringProperty<TestDataTypeEvents, 'stringVal'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly stringVal: Promise<string>;

    @decorators.integerProperty<TestDataTypeEvents, 'adaptableLength'>({
        isPublished: true,
        isStored: true,
    })
    readonly adaptableLength: Promise<integer>;

    @decorators.stringProperty<TestDataTypeEvents, 'adaptableDefault'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly adaptableDefault: Promise<string>;

    @decorators.stringProperty<TestDataTypeEvents, 'stringValAdvancedControls'>({
        isPublished: true,
        isStored: true,
        dataType: () => advancedStringDataType,
    })
    readonly stringValAdvancedControls: Promise<string>;

    @decorators.stringProperty<TestDataTypeEvents, 'stringValDefaultValue'>({
        isPublished: true,
        isStored: true,
        defaultValue: 'propDefault',
        dataType: () => advancedStringDataType,
    })
    readonly stringValDefaultValue: Promise<string>;

    @decorators.decimalProperty<TestDataTypeEvents, 'decimalVal'>({
        isPublished: true,
        isStored: true,
        dataType: () => new DecimalDataType({ precision: 9, scale: 3, roundingMode: RoundingMode.roundCeil }),
    })
    readonly decimalVal: Promise<decimal>;

    @decorators.binaryStreamProperty<TestDataTypeEvents, 'binaryVal'>({
        isStored: true,
        isNullable: true,
    })
    readonly binaryVal: Promise<BinaryStream | null>;

    @decorators.binaryStreamProperty<TestDataTypeEvents, 'binary1024Val'>({
        isStored: true,
        isNullable: true,
        dataType: () => binary1024,
    })
    readonly binary1024Val: Promise<BinaryStream | null>;
}
