import { decorators, Node } from '../../../lib';
import { codeDataType } from '../data-types/_index';

@decorators.node<TestDefaultValuesReference>({
    storage: 'sql',
    isPublished: true,
})
export class TestDefaultValuesReference extends Node {
    @decorators.stringProperty<TestDefaultValuesReference, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;
}
