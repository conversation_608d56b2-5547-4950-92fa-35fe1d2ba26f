import { Context, decorators, Node } from '../../../lib/index';
import { codeDataType } from '../data-types/data-types';

@decorators.node<TestGraphQlOperation>({
    storage: 'json',
    isPublished: true,
    authorizationCode: 'FCT1',
})
export class TestGraphQlOperation extends Node {
    @decorators.stringProperty<TestGraphQlOperation, 'code'>({
        isPublished: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.mutation<typeof TestGraphQlOperation, 'createOperation'>({
        isPublished: true,
        parameters: [{ name: 'param1', type: 'string' }],
        return: 'string',
    })
    static createOperation(context: Context, param1: string): Promise<string> {
        return Promise.resolve(param1);
    }

    @decorators.mutation<typeof TestGraphQlOperation, 'updateOperation'>({
        isPublished: true,
        parameters: [{ name: 'param1', type: 'string' }],
        return: 'string',
    })
    static updateOperation(context: Context, param1: string): Promise<string> {
        return Promise.resolve(param1);
    }

    @decorators.mutation<typeof TestGraphQlOperation, 'deleteOperation'>({
        isPublished: true,
        parameters: [{ name: 'param1', type: 'string' }],
        return: 'string',
    })
    static deleteOperation(context: Context, param1: string): Promise<string> {
        return Promise.resolve(param1);
    }

    @decorators.query<typeof TestGraphQlOperation, 'lookupQueryWithGrant'>({
        isPublished: true,
        parameters: [{ name: 'param1', type: 'string' }],
        return: 'string',
        isGrantedByLookup: true,
    })
    static lookupQueryWithGrant(context: Context, param1: string): Promise<string> {
        return Promise.resolve(param1);
    }

    @decorators.query<typeof TestGraphQlOperation, 'lookupQueryWithoutGrant'>({
        isPublished: true,
        parameters: [{ name: 'param1', type: 'string' }],
        return: 'string',
    })
    static lookupQueryWithoutGrant(context: Context, param1: string): Promise<string> {
        return Promise.resolve(param1);
    }
}
