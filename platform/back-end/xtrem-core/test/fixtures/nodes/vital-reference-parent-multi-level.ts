import { Reference } from '../../../index';
import { decorators, Node } from '../../../lib';
import { codeDataType } from '../data-types/data-types';

@decorators.node<TestVitalReferenceParentLowLevel>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    isVitalReferenceChild: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestVitalReferenceParentLowLevel extends Node {
    @decorators.stringProperty<TestVitalReferenceParentLowLevel, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => codeDataType,
        async control(cx, val): Promise<void> {
            await cx.error.if(val).is.equal.to('bad code');
        },
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestVitalReferenceParentLowLevel, 'parentMidLevel'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        isVitalParentInput: true,
        node: () => TestVitalReferenceParentMidLevel,
    })
    readonly parentMidLevel: Reference<TestVitalReferenceParentMidLevel>;

    @decorators.referenceProperty<TestVitalReferenceParentLowLevel, 'selfRef'>({
        isPublished: true,
        isStored: true,
        node: () => TestVitalReferenceParentLowLevel,
        defaultValue() {
            return this;
        },
    })
    readonly selfRef: Reference<TestVitalReferenceParentLowLevel>;
}

@decorators.node<TestVitalReferenceParentMidLevel>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    isVitalReferenceChild: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestVitalReferenceParentMidLevel extends Node {
    @decorators.stringProperty<TestVitalReferenceParentMidLevel, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => codeDataType,
        async control(cx, val): Promise<void> {
            await cx.error.if(val).is.equal.to('bad code');
        },
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestVitalReferenceParentMidLevel, 'lowLevelRef'>({
        isPublished: true,
        isVital: true,
        node: () => TestVitalReferenceParentLowLevel,
        reverseReference: 'parentMidLevel',
    })
    readonly lowLevelRef: Reference<TestVitalReferenceParentLowLevel>;

    @decorators.referenceProperty<TestVitalReferenceParentMidLevel, 'parentTopLevel'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        isVitalParentInput: true,
        node: () => TestVitalReferenceParentTopLevel,
    })
    readonly parentTopLevel: Reference<TestVitalReferenceParentTopLevel>;

    @decorators.referenceProperty<TestVitalReferenceParentMidLevel, 'selfRef'>({
        isPublished: true,
        isStored: true,
        node: () => TestVitalReferenceParentMidLevel,
        defaultValue() {
            return this;
        },
    })
    readonly selfRef: Reference<TestVitalReferenceParentMidLevel>;
}

@decorators.node<TestVitalReferenceParentTopLevel>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestVitalReferenceParentTopLevel extends Node {
    @decorators.stringProperty<TestVitalReferenceParentTopLevel, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => codeDataType,
        async control(cx, val): Promise<void> {
            await cx.error.if(val).is.equal.to('bad code');
        },
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestVitalReferenceParentTopLevel, 'midLevelRef'>({
        isPublished: true,
        isVital: true,
        node: () => TestVitalReferenceParentMidLevel,
        reverseReference: 'parentTopLevel',
    })
    readonly midLevelRef: Reference<TestVitalReferenceParentMidLevel>;

    @decorators.referenceProperty<TestVitalReferenceParentTopLevel, 'selfRef'>({
        isPublished: true,
        isStored: true,
        node: () => TestVitalReferenceParentTopLevel,
        defaultValue() {
            return this;
        },
    })
    readonly selfRef: Reference<TestVitalReferenceParentTopLevel>;
}

@decorators.node<TestVitalReferenceParentWithTwoChildrenSameTypeParent>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestVitalReferenceParentWithTwoChildrenSameTypeParent extends Node {
    @decorators.stringProperty<TestVitalReferenceParentWithTwoChildrenSameTypeParent, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => codeDataType,
        async control(cx, val): Promise<void> {
            await cx.error.if(val).is.equal.to('bad code');
        },
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestVitalReferenceParentWithTwoChildrenSameTypeParent, 'childRef'>({
        isPublished: true,
        isVital: true,
        node: () => TestVitalReferenceParentWithTwoChildrenSameTypeChild,
        reverseReference: 'parent',
    })
    readonly childRef: Reference<TestVitalReferenceParentWithTwoChildrenSameTypeChild>;

    @decorators.referenceProperty<TestVitalReferenceParentWithTwoChildrenSameTypeParent, 'anotherRef'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        node: () => TestVitalReferenceParentWithTwoChildrenSameTypeChild,
    })
    readonly anotherRef: Reference<TestVitalReferenceParentWithTwoChildrenSameTypeChild | null>;
}

@decorators.node<TestVitalReferenceParentWithTwoChildrenSameTypeChild>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    isVitalReferenceChild: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestVitalReferenceParentWithTwoChildrenSameTypeChild extends Node {
    @decorators.stringProperty<TestVitalReferenceParentWithTwoChildrenSameTypeChild, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => codeDataType,
        async control(cx, val): Promise<void> {
            await cx.error.if(val).is.equal.to('bad code');
        },
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestVitalReferenceParentWithTwoChildrenSameTypeChild, 'parent'>({
        isPublished: true,
        isStored: true,
        node: () => TestVitalReferenceParentWithTwoChildrenSameTypeParent,
        isVitalParent: true,
        isVitalParentInput: true,
    })
    readonly parent: Reference<TestVitalReferenceParentWithTwoChildrenSameTypeParent>;
}
