import { decorators, Node } from '../../../index';
import { codeDataType, descriptionDataType } from '../data-types/data-types';

@decorators.node<TestFrozenValueFalseChaining>({
    isPublished: true,
    storage: 'sql',
    async isFrozen() {
        return (await this.code) === 'FROZEN';
    },
    indexes: [{ orderBy: { code: +1 }, isUnique: true }],
})
export class TestFrozenValueFalseChaining extends Node {
    @decorators.stringProperty<TestFrozenValueFalseChaining, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestFrozenValueFalseChaining, 'callbackFrozen'>({
        isPublished: true,
        isStored: true,
        async isFrozen() {
            return (await this.callbackFrozen) === 'FROZEN';
        },
        dataType: () => descriptionDataType,
    })
    readonly callbackFrozen: Promise<string>;

    @decorators.stringProperty<TestFrozenValueFalseChaining, 'valueFrozen'>({
        isPublished: true,
        isStored: true,
        isFrozen: true,
        dataType: () => descriptionDataType,
    })
    readonly valueFrozen: Promise<string>;

    @decorators.stringProperty<TestFrozenValueFalseChaining, 'notFrozen'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly notFrozen: Promise<string>;
}
