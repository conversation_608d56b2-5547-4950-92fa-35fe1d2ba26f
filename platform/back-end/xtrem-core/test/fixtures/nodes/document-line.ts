import { TestDocument, TestReferred } from '.';
import { Node, Reference, decorators, integer } from '../../../index';
import { descriptionDataType } from '../data-types/data-types';
import * as fixtures from '../index';

@decorators.node<TestDocumentLine>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestDocumentLine',
    isVitalCollectionChild: true,
    indexes: [
        {
            orderBy: {
                document: 1,
                lineNumber: 1,
            },
            isUnique: true,
        },
    ],
})
export class TestDocumentLine extends Node {
    @decorators.stringProperty<TestDocumentLine, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly description: Promise<string>;

    @decorators.integerProperty<TestDocumentLine, 'lineNumber'>({
        isPublished: true,
        isStored: true,
    })
    readonly lineNumber: Promise<integer>;

    @decorators.referenceProperty<TestDocumentLine, 'optionalReference'>({
        isStored: true,
        isPublished: true,
        node: () => fixtures.nodes.TestReferred,
        isNullable: true,
    })
    readonly optionalReference: Reference<TestReferred | null>;

    @decorators.referenceProperty<TestDocumentLine, 'document'>({
        isStored: true,
        isPublished: true,
        node: () => fixtures.nodes.TestDocument,
        isVitalParent: true,
    })
    readonly document: Reference<TestDocument>;

    @decorators.stringProperty<TestDocumentLine, 'getDescription'>({
        isPublished: true,
        dataType: () => descriptionDataType,
        getValue() {
            return this.description;
        },
    })
    readonly getDescription: Promise<string>;
}

@decorators.node<ComputedDocumentLine>({
    isPublished: true,
    storage: 'json',
    indexes: [
        {
            orderBy: {
                lineNumber: 1,
            },
            isUnique: true,
        },
    ],
})
export class ComputedDocumentLine extends Node {
    @decorators.integerProperty<ComputedDocumentLine, 'lineNumber'>({
        isPublished: true,
    })
    readonly lineNumber: Promise<integer>;

    @decorators.stringProperty<ComputedDocumentLine, 'text'>({
        isPublished: true,
        dataType: () => descriptionDataType,
        async control(cx): Promise<void> {
            await cx.expect(await this.text).to.be.matching(/^computed /);
        },
    })
    readonly text: Promise<string>;
}
