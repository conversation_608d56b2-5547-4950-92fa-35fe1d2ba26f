import { decorators, Node, Reference } from '../../../lib';
import { name } from '../data-types/data-types';

@decorators.node<TestMutationLongDelay>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [],
})
export class TestMutationLongDelay extends Node {
    @decorators.stringProperty<TestMutationLongDelay, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => name,
    })
    readonly name: Promise<string>;

    @decorators.referenceProperty<TestMutationLongDelay, 'reference'>({
        isPublished: true,
        node: () => TestMutationLongDelay,
        isNullable: true,
        async computeValue() {
            await new Promise<void>(resolve => {
                setTimeout(resolve, 1000);
            });

            return this;
        },
    })
    readonly reference: Reference<TestMutationLongDelay | null>;
}
