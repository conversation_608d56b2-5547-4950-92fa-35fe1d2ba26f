import { Collection, decimal, decorators, integer, Node, Reference } from '../../../index';
import { codeDataType, defaultDecimalDataType } from '../data-types/data-types';
import { TestAggDocumentExtendedLine } from './agg-document-extended-line';
import { TestAggDocumentLine } from './agg-document-line';
import { TestAggDocumentReverseRef } from './agg-document-reverse-ref';

@decorators.node<TestAggDocument>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    tableName: 'TestAggDocument',
    indexes: [{ orderBy: { code: +1 }, isUnique: true }],
})
export class TestAggDocument extends Node {
    @decorators.stringProperty<TestAggDocument, 'code'>({
        isStored: true,
        dataType: () => codeDataType,
        isPublished: true,
    })
    readonly code: Promise<string>;

    @decorators.decimalProperty<TestAggDocument, 'totalAmount1'>({
        isPublished: true,
        isStored: true,
        dataType: () => defaultDecimalDataType,
    })
    readonly totalAmount1: Promise<decimal>;

    @decorators.decimalProperty<TestAggDocument, 'totalAmount2'>({
        isPublished: true,
        isStored: true,
        dataType: () => defaultDecimalDataType,
    })
    readonly totalAmount2: Promise<decimal>;

    @decorators.decimalProperty<TestAggDocument, 'totalAmount3'>({
        isPublished: true,
        isStored: true,
        dataType: () => defaultDecimalDataType,
    })
    readonly totalAmount3: Promise<decimal>;

    @decorators.decimalProperty<TestAggDocument, 'totalAmount4'>({
        isPublished: true,
        isStored: true,
        dataType: () => defaultDecimalDataType,
    })
    readonly totalAmount4: Promise<decimal>;

    @decorators.decimalProperty<TestAggDocument, 'totalAmount5'>({
        isPublished: true,
        isStored: true,
        dataType: () => defaultDecimalDataType,
    })
    readonly totalAmount5: Promise<decimal>;

    @decorators.integerProperty<TestAggDocument, 'linesCount'>({
        isPublished: true,
        isStored: true,
    })
    readonly linesCount: Promise<integer>;

    @decorators.collectionProperty<TestAggDocument, 'lines'>({
        isPublished: true,
        isVital: true,
        node: () => TestAggDocumentLine,
        reverseReference: 'document',
    })
    readonly lines: Collection<TestAggDocumentLine>;

    @decorators.collectionProperty<TestAggDocument, 'extendedLines'>({
        isPublished: true,
        isVital: true,
        node: () => TestAggDocumentExtendedLine,
        reverseReference: 'document',
    })
    readonly extendedLines: Collection<TestAggDocumentExtendedLine>;

    @decorators.referenceProperty<TestAggDocument, 'documentReverseRef'>({
        isVital: true,
        isPublished: true,
        reverseReference: 'document',
        isNullable: true,
        node: () => TestAggDocumentReverseRef,
    })
    readonly documentReverseRef: Reference<TestAggDocumentReverseRef | null>;
}
