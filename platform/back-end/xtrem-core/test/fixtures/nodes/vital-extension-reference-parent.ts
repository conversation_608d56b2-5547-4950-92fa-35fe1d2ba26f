import { decorators, Node, Reference } from '../../../lib';
import { codeDataType } from '../data-types/data-types';
import { TestBaseVitalChild } from './base';

@decorators.node<TestVitalReferenceExtensionParent>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true, isNaturalKey: true }],
})
export class TestVitalReferenceExtensionParent extends Node {
    @decorators.stringProperty<TestVitalReferenceExtensionParent, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestVitalReferenceExtensionParent, 'child'>({
        isPublished: true,
        isVital: true,
        node: () => TestBaseVitalChild,
        reverseReference: 'parent',
    })
    readonly child: Reference<TestBaseVitalChild>;
}
