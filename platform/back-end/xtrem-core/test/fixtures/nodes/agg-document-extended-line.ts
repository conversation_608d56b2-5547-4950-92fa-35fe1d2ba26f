import { decimal, decorators, Reference } from '../../../index';
import { defaultDecimalDataType } from '../data-types/data-types';
import { TestAggDocument } from './agg-document';
import { TestAggDocumentBaseLine } from './agg-document-base-line';

@decorators.subNode<TestAggDocumentExtendedLine>({
    extends: () => TestAggDocumentBaseLine,
    isPublished: true,
    isVitalCollectionChild: true,
})
export class TestAggDocumentExtendedLine extends TestAggDocumentBaseLine {
    @decorators.referenceProperty<TestAggDocumentExtendedLine, 'document'>({
        isPublished: true,
        isStored: true,
        node: () => TestAggDocument,
        isVitalParent: true,
    })
    readonly document: Reference<TestAggDocument>;

    @decorators.decimalProperty<TestAggDocumentExtendedLine, 'amount2'>({
        isPublished: true,
        isStored: true,
        dataType: () => defaultDecimalDataType,
    })
    readonly amount2: Promise<decimal>;
}
