import { Reference } from '../../../index';
import { decorators, Node } from '../../../lib';
import { codeDataType } from '../data-types/data-types';
import {
    TestNonVitalReference,
    TestVitalReferenceChildMandatory,
    TestVitalReferenceChildOptional,
} from './vital-reference-child';

@decorators.node<TestVitalReferenceParent>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestVitalReferenceParent extends Node {
    @decorators.stringProperty<TestVitalReferenceParent, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestVitalReferenceParent, 'mandatoryVitalRef'>({
        isPublished: true,
        isVital: true,
        node: () => TestVitalReferenceChildMandatory,
        reverseReference: 'parent',
    })
    readonly mandatoryVitalRef: Reference<TestVitalReferenceChildMandatory>;

    @decorators.referenceProperty<TestVitalReferenceParent, 'optionalVitalRef'>({
        isPublished: true,
        isVital: true,
        isNullable: true,
        node: () => TestVitalReferenceChildOptional,
        reverseReference: 'parent',
    })
    readonly optionalVitalRef: Reference<TestVitalReferenceChildOptional | null>;

    @decorators.referenceProperty<TestVitalReferenceParent, 'nonVitalRef'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => TestNonVitalReference,
        filters: {
            lookup: {
                async code() {
                    return (await this.optionalVitalRef) ? { _regex: 'NONVITAL1' } : { _regex: '.*' };
                },
            },
        },
    })
    readonly nonVitalRef: Reference<TestNonVitalReference | null>;
}
