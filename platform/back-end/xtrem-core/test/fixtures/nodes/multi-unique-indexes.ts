import { decorators, Node } from '../../../index';
import { codeDataType, descriptionDataType } from '../data-types/data-types';

@decorators.node<TestMultiUniqueIndexes>({
    isPublished: true,
    storage: 'sql',
    indexes: [
        { orderBy: { code: 1 }, isUnique: true },
        { orderBy: { alt1: 1, alt2: 1 }, isUnique: true },
    ],
})
export class TestMultiUniqueIndexes extends Node {
    @decorators.stringProperty<TestMultiUniqueIndexes, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestMultiUniqueIndexes, 'alt1'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly alt1: Promise<string>;

    @decorators.stringProperty<TestMultiUniqueIndexes, 'alt2'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly alt2: Promise<string>;

    @decorators.stringProperty<TestMultiUniqueIndexes, 'details'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly details: Promise<string>;
}
