import { Collection, decorators, Node, Reference } from '../../../index';
import { codeDataType } from '../data-types/data-types';

@decorators.node<TestBaseAddress>({
    storage: 'sql',
    isPublished: true,
    isAbstract: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestBaseAddress extends Node {
    @decorators.stringProperty<TestBaseAddress, 'code'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.collectionProperty<TestBaseAddress, 'contacts'>({
        node: () => TestAddressContact,
        reverseReference: 'address',
        isPublished: true,
        isVital: true,
    })
    readonly contacts: Collection<TestAddressContact>;
}

@decorators.subNode<TestSupplierAddress>({
    extends: () => TestBaseAddress,
    canRead: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    isVitalCollectionChild: true,
})
export class TestSupplierAddress extends TestBaseAddress {
    @decorators.booleanProperty<TestSupplierAddress, 'isPrimary'>({
        isStored: true,
        isPublished: true,
        defaultValue: false,
    })
    readonly isPrimary: Promise<boolean>;

    @decorators.referenceProperty<TestSupplierAddress, 'supplier'>({
        isStored: true,
        isPublished: true,
        node: () => TestSupplierWithSubNodeCollection,
        isVitalParent: true,
    })
    readonly supplier: Reference<TestSupplierWithSubNodeCollection>;
}

@decorators.node<TestAddressContact>({
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    isPublished: true,
    isVitalCollectionChild: true,
    indexes: [
        {
            orderBy: { address: +1, code: +1 },
            isUnique: true,
        },
    ],
})
export class TestAddressContact extends Node {
    @decorators.stringProperty<TestAddressContact, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestAddressContact, 'address'>({
        isStored: true,
        isPublished: true,
        node: () => TestBaseAddress,
        isVitalParent: true,
    })
    readonly address: Reference<TestBaseAddress>;
}

@decorators.node<TestSupplierWithSubNodeCollection>({
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestSupplierWithSubNodeCollection extends Node {
    @decorators.stringProperty<TestSupplierWithSubNodeCollection, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.collectionProperty<TestSupplierWithSubNodeCollection, 'addresses'>({
        node: () => TestSupplierAddress,
        reverseReference: 'supplier',
        isPublished: true,
        isVital: true,
    })
    readonly addresses: Collection<TestSupplierAddress>;
}
