import { decorators, integer, Node, StringDataType } from '../../../index';

@decorators.node<TestEncryptedValues>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [{ orderBy: { id: 1 }, isUnique: true }],
})
export class TestEncryptedValues extends Node {
    @decorators.integerProperty<TestEncryptedValues, 'id'>({
        isPublished: true,
        isStored: true,
    })
    readonly id: Promise<integer>;

    @decorators.stringProperty<TestEncryptedValues, 'passwordValue'>({
        isPublished: true,
        isStored: true,
        isStoredEncrypted: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly passwordValue: Promise<string>;
}
