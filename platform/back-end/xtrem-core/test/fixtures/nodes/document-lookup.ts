import { decorators, Node, Reference } from '../../../index';
import { integer } from '../../../lib';
import { TestReferencedDocument } from './referenced-document';
import { TestReferencedDocumentDetails } from './referenced-document-details';
import { TestReferencedDocumentOther } from './referenced-document-other';

@decorators.node<TestDocumentLookup>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestDocumentLookup',
    canCreate: true,
})
export class TestDocumentLookup extends Node {
    @decorators.integerProperty<TestDocumentLookup, 'parentControlInteger'>({
        isStored: true,
        isPublished: true,
    })
    readonly parentControlInteger: Promise<integer>;

    @decorators.referenceProperty<TestDocumentLookup, 'reference'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => TestReferencedDocument,
        filters: {
            lookup: { lookupString: 'str2' },
            control: {
                controlInteger() {
                    return this.parentControlInteger;
                },
            },
        },
    })
    readonly reference: Reference<TestReferencedDocument | null>;

    @decorators.referenceProperty<TestDocumentLookup, 'noLookupReference'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => TestReferencedDocument,
        filters: {
            control: {
                controlInteger() {
                    return this.parentControlInteger;
                },
            },
        },
    })
    readonly noLookupReference: Reference<TestReferencedDocument>;

    @decorators.referenceProperty<TestDocumentLookup, 'noControlReference'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => TestReferencedDocument,
        filters: {
            lookup: { lookupString: 'str2' },
        },
    })
    readonly noControlReference: Reference<TestReferencedDocument>;

    @decorators.referenceProperty<TestDocumentLookup, 'noFiltersReference'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => TestReferencedDocument,
    })
    readonly noFiltersReference: Reference<TestReferencedDocument>;

    @decorators.referenceProperty<TestDocumentLookup, 'otherReference'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => TestReferencedDocumentOther,
        filters: {
            lookup: {
                lookupString(): string {
                    return 'str1';
                },
            },
        },
    })
    readonly otherReference: Reference<TestReferencedDocumentOther>;

    @decorators.referenceProperty<TestDocumentLookup, 'dynamicReference'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => TestReferencedDocumentOther,
        filters: {
            lookup: {
                async lookupString() {
                    return (await this.parentControlInteger) === 10 ? 'str1' : 'str2';
                },
            },
        },
    })
    readonly dynamicReference: Reference<TestReferencedDocumentOther>;

    @decorators.referenceProperty<TestDocumentLookup, 'details'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => TestReferencedDocumentDetails,
    })
    readonly details: Reference<TestReferencedDocumentDetails | null>;

    @decorators.referenceProperty<TestDocumentLookup, 'controlledByDetailsReference'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => TestReferencedDocument,
        filters: {
            control: {
                controlDetails() {
                    return this.details;
                },
            },
        },
    })
    readonly controlledByDetailsReference: Reference<TestReferencedDocument | null>;

    @decorators.referenceProperty<TestDocumentLookup, 'controlledByDetailsId'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => TestReferencedDocument,
        filters: {
            control: {
                async controlDetails() {
                    return (await this.details)?._id || null;
                },
            },
        },
    })
    readonly controlledByDetailsId: Reference<TestReferencedDocument | null>;

    @decorators.referenceArrayProperty<TestDocumentLookup, 'referenceArrayLookup'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => TestReferencedDocument,
        onDelete: 'restrict',
        filters: {
            lookup: {
                async lookupString() {
                    return (await this.parentControlInteger) === 10 ? 'str2' : 'str1';
                },
                controlInteger() {
                    return this.parentControlInteger;
                },
            },
        },
    })
    readonly referenceArrayLookup: Promise<TestReferencedDocument[] | null>;

    @decorators.referenceArrayProperty<TestDocumentLookup, 'referenceArrayControl'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        onDelete: 'restrict',
        node: () => TestReferencedDocument,
        filters: {
            control: {
                controlInteger() {
                    return this.parentControlInteger;
                },
            },
        },
    })
    readonly referenceArrayControl: Promise<TestReferencedDocument[] | null>;
}
