// import { datetime } from '@sage/xtrem-shared';
import { datetime, decorators, Node } from '../../../index';
import * as dataTypes from '../data-types/data-types';

@decorators.node<TestSystemComputed>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    indexes: [
        {
            orderBy: { id: +1 },
            isUnique: true,
        },
    ],
})
export class TestSystemComputed extends Node {
    @decorators.stringProperty<TestSystemComputed, 'id'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.id,
    })
    readonly id: Promise<string>;

    @decorators.stringProperty<TestSystemComputed, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => dataTypes.descriptionDataType,
    })
    readonly description: Promise<string>;

    @decorators.stringProperty<TestSystemComputed, 'createdBy'>({
        isPublished: true,
        dataType: () => dataTypes.name,
        async computeValue() {
            // TODO: add node._createUser property
            return (await (this as any)._createUser).displayName;
        },
    })
    readonly createdBy: Promise<string>;

    @decorators.stringProperty<TestSystemComputed, 'updatedBy'>({
        isPublished: true,
        dataType: () => dataTypes.name,
        async computeValue() {
            return (await (this as any)._updateUser).displayName;
        },
    })
    readonly updatedBy: Promise<string>;

    @decorators.datetimeProperty<TestSystemComputed, 'updateStamp'>({
        isPublished: true,
        computeValue() {
            return (this as any)._updateStamp;
        },
    })
    readonly updateStamp: Promise<datetime>;

    @decorators.datetimeProperty<TestSystemComputed, 'createStamp'>({
        isPublished: true,
        computeValue() {
            return (this as any)._createStamp;
        },
    })
    readonly createStamp: Promise<datetime>;
}
