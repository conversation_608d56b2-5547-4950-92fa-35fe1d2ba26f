import { decorators, integer, Node } from '../../../index';
import { codeDataType, descriptionDataType } from '../data-types/data-types';

@decorators.node<TestReferred>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestReferred',
    indexes: [{ orderBy: { code: 1 }, isUnique: true, isNaturalKey: true }],
})
export class TestReferred extends Node {
    @decorators.stringProperty<TestReferred, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestReferred, 'details'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly details: Promise<string>;

    @decorators.stringProperty<TestReferred, 'restricted'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
        accessCode: 'RESTRICTED',
    })
    readonly restricted: Promise<string>;

    @decorators.integerProperty<TestReferred, 'integerVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly integerVal: Promise<integer | null>;

    @decorators.integerProperty<TestReferred, 'computedVal'>({
        async getValue() {
            return 2 * ((await this.integerVal) ?? 0);
        },
    })
    readonly computedVal: Promise<integer>;
}
