import { Collection, Context, decorators, Node, Reference } from '../../../index';
import { codeDataType, descriptionDataType } from '../data-types/data-types';
import { TestEnum, testEnumDataType } from '../enums';

@decorators.node<TestBaseReference>({
    isPublished: true,
    storage: 'sql',
})
export class TestBaseReference extends Node {
    @decorators.stringProperty<TestBaseReference, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;
}

@decorators.node<TestBaseCollectionElement>({
    isPublished: true,
    storage: 'sql',
})
export class TestBaseCollectionElement extends Node {
    @decorators.stringProperty<TestBaseCollectionElement, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestBaseCollectionElement, 'base'>({
        isPublished: true,
        isStored: true,
        node: () => TestBase,
    })
    readonly base: Reference<TestBase>;
}

@decorators.node<TestBaseVitalChild>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
})
export class TestBaseVitalChild extends Node {
    @decorators.stringProperty<TestBaseVitalChild, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;
}

@decorators.node<TestBase>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
})
export class TestBase extends Node {
    @decorators.stringProperty<TestBase, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestBase, 'controlled'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
        defaultValue(): string {
            return 'GOOD1';
        },
        async control(cx): Promise<void> {
            await cx.error.if(await this.controlled).is.equal.to('BAD1');
        },
    })
    readonly controlled: Promise<string>;

    @decorators.stringProperty<TestBase, 'dependsOn'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
        dependsOn: ['controlled'],
        defaultValue(): string {
            return 'INITIAL1';
        },
        updatedValue(): string {
            return 'UPDATED1';
        },
    })
    readonly dependsOn: Promise<string>;

    @decorators.stringProperty<TestBase, 'dependsOn2'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly dependsOn2: Promise<string>;

    @decorators.referenceProperty<TestBase, 'testBaseReference'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        node: () => TestBaseReference,
    })
    readonly testBaseReference: Reference<TestBaseReference | null>;

    @decorators.enumProperty<TestBase, 'testBaseEnum'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => testEnumDataType,
    })
    readonly testBaseEnum: Promise<TestEnum | null>;

    @decorators.collectionProperty<TestBase, 'testBaseCollection'>({
        isPublished: true,
        node: () => TestBaseCollectionElement,
        reverseReference: 'base',
    })
    readonly testBaseCollection: Collection<TestBaseCollectionElement>;

    @decorators.mutation<typeof TestBase, 'testCreate'>({
        isPublished: true,
        parameters: [{ name: 'code', type: 'string', isMandatory: true }],
        return: {
            type: 'instance',
            node(): typeof TestBase {
                return TestBase;
            },
        },
    })
    static async testCreate(context: Context, code: string): Promise<TestBase> {
        const base = await context.create(TestBase, { code });
        return base;
    }
}
