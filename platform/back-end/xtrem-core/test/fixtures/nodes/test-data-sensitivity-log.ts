/* eslint-disable @typescript-eslint/no-unused-vars */
import { BusinessRuleError } from '@sage/xtrem-shared';
import { Collection, Context, Node, Reference, decimal, decorators } from '../../../lib';
import { descriptionDataType } from '../data-types/_index';

@decorators.node<TestDataSensitivityLog>({
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canCreate: true,
    canDelete: true,
    canUpdate: true,
    tableName: 'TestDataSensitivityLog',
    indexes: [],
    controlDelete() {
        throw new BusinessRuleError('delete failed');
    },
})
export class TestDataSensitivityLog extends Node {
    @decorators.stringProperty<TestDataSensitivityLog, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestDataSensitivityLog, 'stringRegular'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
        async control(cx, val) {
            await cx.error.if(val).is.empty();
        },
    })
    readonly stringRegular: Promise<string>;

    @decorators.stringProperty<TestDataSensitivityLog, 'stringNotSensitive'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
        dataSensitivityLevel: 'notSensitive',
    })
    readonly stringNotSensitive: Promise<string>;

    @decorators.stringProperty<TestDataSensitivityLog, 'stringSensitive'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
        dataSensitivityLevel: 'personal',
    })
    readonly stringSensitive: Promise<string>;

    @decorators.referenceProperty<TestDataSensitivityLog, 'delegate'>({
        isStored: true,
        isPublished: true,
        isMutable: true,
        node: () => TestDelegatedToReference,
    })
    readonly delegate: Reference<TestDelegatedToReference>;

    @decorators.stringProperty<TestDataSensitivityLog, 'text'>({
        isPublished: true,
        delegatesTo: { delegate: 'text' },
    })
    readonly text: Promise<string>;

    @decorators.booleanProperty<TestDataSensitivityLog, 'bool'>({
        isPublished: true,
        delegatesTo: { delegate: 'bool' },
    })
    readonly bool: Promise<boolean>;

    @decorators.stringProperty<TestDataSensitivityLog, 'textSensitive'>({
        isPublished: true,
        delegatesTo: { delegate: 'textSensitive' },
    })
    readonly textSensitive: Promise<string>;

    @decorators.booleanProperty<TestDataSensitivityLog, 'boolSensitive'>({
        isPublished: true,
        delegatesTo: { delegate: 'boolSensitive' },
    })
    readonly boolSensitive: Promise<boolean>;

    @decorators.collectionProperty<TestDataSensitivityLog, 'lines'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'testDataSensitivityLog',
        node: () => TestDelegatedTo,
    })
    readonly lines: Collection<TestDelegatedTo>;

    @decorators.query<typeof TestDataSensitivityLog, 'customQuery'>({
        isPublished: true,
        parameters: [
            {
                name: 'searchCriteria',
                type: 'object',
                isMandatory: true,
                properties: {
                    item: { type: 'reference', node: () => TestDataSensitivityLog },
                    itemList: {
                        type: 'array',
                        item: {
                            type: 'object',
                            properties: {
                                status: 'integer',
                            },
                        },
                    },
                },
            },
        ],
        return: 'decimal',
    })
    static customQuery(
        context: Context,
        searchCriteria: {
            item: Reference<TestDataSensitivityLog>;
            itemList?: { status: number }[];
        },
    ): decimal {
        throw new BusinessRuleError('customQuery failed');
    }

    @decorators.mutation<typeof TestDataSensitivityLog, 'customMutation'>({
        isPublished: true,
        parameters: [
            {
                name: 'searchCriteria',
                type: 'object',
                isMandatory: true,
                properties: {
                    item: { type: 'reference', node: () => TestDataSensitivityLog },
                    itemList: {
                        type: 'array',
                        item: {
                            type: 'object',
                            properties: {
                                status: 'integer',
                            },
                        },
                    },
                },
            },
        ],
        return: 'decimal',
    })
    static customMutation(
        context: Context,
        searchCriteria: {
            item: Reference<TestDataSensitivityLog>;
            itemList?: { status: number }[];
        },
    ): decimal {
        throw new BusinessRuleError('mutationQuery failed');
    }
}

@decorators.node<TestDelegatedTo>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestDelegatedTo',
    isVitalCollectionChild: true,
})
export class TestDelegatedTo extends Node {
    @decorators.referenceProperty<TestDelegatedTo, 'testDelegatedToReference'>({
        isStored: true,
        isPublished: true,
        isMutable: true,
        node: () => TestDelegatedToReference,
    })
    readonly testDelegatedToReference: Reference<TestDelegatedToReference>;

    @decorators.referenceProperty<TestDelegatedTo, 'testDataSensitivityLog'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => TestDataSensitivityLog,
    })
    readonly testDataSensitivityLog: Reference<TestDataSensitivityLog>;

    @decorators.stringProperty<TestDelegatedTo, 'text'>({
        isPublished: true,
        delegatesTo: { testDelegatedToReference: 'text' },
    })
    readonly text: Promise<string>;

    @decorators.booleanProperty<TestDelegatedTo, 'bool'>({
        isPublished: true,
        delegatesTo: { testDelegatedToReference: 'bool' },
    })
    readonly bool: Promise<boolean>;

    @decorators.stringProperty<TestDelegatedTo, 'textSensitive'>({
        isPublished: true,
        delegatesTo: { testDelegatedToReference: 'textSensitive' },
    })
    readonly textSensitive: Promise<string>;

    @decorators.booleanProperty<TestDelegatedTo, 'boolSensitive'>({
        isPublished: true,
        delegatesTo: { testDelegatedToReference: 'boolSensitive' },
    })
    readonly boolSensitive: Promise<boolean>;
}

@decorators.node<TestDelegatedToReference>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestDelegatedToReference',
    isContentAddressable: true,
})
export class TestDelegatedToReference extends Node {
    @decorators.stringProperty<TestDelegatedToReference, 'text'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly text: Promise<string>;

    @decorators.booleanProperty<TestDelegatedToReference, 'bool'>({
        isStored: true,
        isPublished: true,
    })
    readonly bool: Promise<boolean>;

    @decorators.stringProperty<TestDelegatedToReference, 'textSensitive'>({
        isPublished: true,
        isStored: true,
        dataSensitivityLevel: 'personal',
        dataType: () => descriptionDataType,
    })
    readonly textSensitive: Promise<string>;

    @decorators.booleanProperty<TestDelegatedToReference, 'boolSensitive'>({
        dataSensitivityLevel: 'personal',
        isStored: true,
        isPublished: true,
    })
    readonly boolSensitive: Promise<boolean>;
}
