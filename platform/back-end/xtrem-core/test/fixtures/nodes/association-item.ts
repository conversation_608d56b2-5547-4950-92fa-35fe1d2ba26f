import { Reference } from '../../../index';
import { decorators, Node } from '../../../lib';
import { codeDataType } from '../data-types/data-types';
import { TestAssociationAssociated } from './association-associated';
import { TestAssociationVitalParent } from './association-vital-parent';

@decorators.node<TestAssociationItem>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    isVitalCollectionChild: true,
    isAssociationCollectionChild: true,
    indexes: [
        { orderBy: { code: 1 }, isUnique: true },
        { orderBy: { parent: 1, associated: 1 }, isUnique: true },
    ],
})
export class TestAssociationItem extends Node {
    @decorators.stringProperty<TestAssociationItem, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestAssociationItem, 'parent'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => TestAssociationVitalParent,
    })
    readonly parent: Reference<TestAssociationVitalParent>;

    @decorators.referenceProperty<TestAssociationItem, 'associated'>({
        isStored: true,
        isPublished: true,
        isAssociationParent: true,
        node: () => TestAssociationAssociated,
    })
    readonly associated: Reference<TestAssociationAssociated>;
}
