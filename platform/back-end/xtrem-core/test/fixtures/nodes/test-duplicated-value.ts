import { Collection, decorators, Node, Reference, StringDataType, useDefaultValue } from '../../../lib';

@decorators.node<TestDuplicatedValue>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canDuplicate: true,
    tableName: 'TestDuplicatedValue',
    indexes: [],
})
export class TestDuplicatedValue extends Node {
    @decorators.booleanProperty<TestDuplicatedValue, 'booleanVal'>({
        isStored: true,
        isPublished: true,
    })
    readonly booleanVal: Promise<boolean>;

    @decorators.booleanProperty<TestDuplicatedValue, 'booleanDuplicateDefault'>({
        isStored: true,
        isPublished: true,
        defaultValue: true,
        duplicatedValue: useDefaultValue,
    })
    readonly booleanDuplicateDefault: Promise<boolean>;

    @decorators.booleanProperty<TestDuplicatedValue, 'booleanDuplicateTrueFunction'>({
        isStored: true,
        isPublished: true,
        duplicatedValue: true,
    })
    readonly booleanDuplicateTrueFunction: Promise<boolean>;

    @decorators.stringProperty<TestDuplicatedValue, 'stringDuplicateFunction'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 30 }),
        duplicatedValue: '',
    })
    readonly stringDuplicateFunction: Promise<string>;

    @decorators.stringProperty<TestDuplicatedValue, 'stringIndicator'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 30 }),
    })
    readonly stringIndicator: Promise<string>;

    @decorators.referenceProperty<TestDuplicatedValue, 'testVitalReference'>({
        isVital: true,
        isPublished: true,
        reverseReference: 'testDuplicatedValue',
        isNullable: true,
        node: () => TestDuplicatedVitalReference,
    })
    readonly testVitalReference: Reference<TestDuplicatedVitalReference | null>;

    @decorators.referenceProperty<TestDuplicatedValue, 'testReference'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => TestDuplicatedNonVitalReference,
        duplicatedValue: null,
    })
    readonly testReference: Reference<TestDuplicatedNonVitalReference | null>;

    @decorators.referenceProperty<TestDuplicatedValue, 'testReferenceNotNullable'>({
        isStored: true,
        isPublished: true,
        isNullable: false,
        node: () => TestDuplicatedNonVitalReference,
        async duplicatedValue() {
            if ((await this.stringIndicator) === 'Non-nullable reference test') return null;
            return this.$.context.read(TestDuplicatedNonVitalReference, { _id: 1 });
        },
    })
    readonly testReferenceNotNullable: Reference<TestDuplicatedNonVitalReference>;
}

@decorators.node<TestDuplicatedVitalReference>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    tableName: 'TestDuplicatedVitalReference',
    indexes: [],
    isVitalReferenceChild: true,
})
export class TestDuplicatedVitalReference extends Node {
    @decorators.referenceProperty<TestDuplicatedVitalReference, 'testDuplicatedValue'>({
        isPublished: true,
        isStored: true,
        node: () => TestDuplicatedValue,
        isVitalParent: true,
    })
    readonly testDuplicatedValue: Reference<TestDuplicatedValue>;

    @decorators.stringProperty<TestDuplicatedVitalReference, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 30 }),
    })
    readonly code: Promise<string>;
}

@decorators.node<TestDuplicatedNonVitalReference>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    tableName: 'TestDuplicatedNonVitalReference',
    indexes: [],
})
export class TestDuplicatedNonVitalReference extends Node {
    @decorators.stringProperty<TestDuplicatedNonVitalReference, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 30 }),
    })
    readonly code: Promise<string>;
}

@decorators.node<TestDuplicatedReferenceForNull>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    tableName: 'TestDuplicatedReferenceForNull',
    indexes: [],
})
export class TestDuplicatedReferenceForNull extends Node {
    @decorators.stringProperty<TestDuplicatedReferenceForNull, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 30 }),
    })
    readonly code: Promise<string>;
}

@decorators.node<TestDuplicatedParent>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canDuplicate: true,
    tableName: 'TestDuplicatedParent',
    indexes: [],
})
export class TestDuplicatedParent extends Node {
    @decorators.stringProperty<TestDuplicatedParent, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 30 }),
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestDuplicatedParent, 'stringDuplicateFunction'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 30 }),
        duplicatedValue: 'duplicatedValue',
    })
    readonly stringDuplicateFunction: Promise<string>;

    @decorators.collectionProperty<TestDuplicatedParent, 'lines'>({
        isPublished: true,
        isVital: true,
        node: () => TestDuplicatedChild,
        reverseReference: 'duplicatedParent',
    })
    readonly lines: Collection<TestDuplicatedChild>;

    @decorators.collectionProperty<TestDuplicatedParent, 'linesWithDuplicatedValue'>({
        isPublished: true,
        isVital: true,
        node: () => TestDuplicatedSecondChild,
        reverseReference: 'duplicatedParent',
        duplicatedValue: [],
    })
    readonly linesWithDuplicatedValue: Collection<TestDuplicatedSecondChild>;
}

@decorators.node<TestDuplicatedChild>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    tableName: 'TestDuplicatedChild',
    indexes: [],
    isVitalCollectionChild: true,
})
export class TestDuplicatedChild extends Node {
    @decorators.referenceProperty<TestDuplicatedChild, 'duplicatedParent'>({
        isStored: true,
        isPublished: true,
        node: () => TestDuplicatedParent,
        isVitalParent: true,
    })
    readonly duplicatedParent: Reference<TestDuplicatedParent>;

    @decorators.stringProperty<TestDuplicatedChild, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 30 }),
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestDuplicatedChild, 'stringDuplicateFunction'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 30 }),
        duplicatedValue: 'duplicatedValue',
    })
    readonly stringDuplicateFunction: Promise<string>;

    @decorators.collectionProperty<TestDuplicatedChild, 'childLines'>({
        isPublished: true,
        isVital: true,
        node: () => TestDuplicatedGrandChild,
        reverseReference: 'duplicatedChildParent',
    })
    readonly childLines: Collection<TestDuplicatedGrandChild>;

    @decorators.referenceProperty<TestDuplicatedChild, 'duplicatedReferenceWithNullDuplicate'>({
        isStored: true,
        isPublished: true,
        node: () => TestDuplicatedReferenceForNull,
        duplicatedValue: null,
    })
    readonly duplicatedReferenceWithNullDuplicate: Reference<TestDuplicatedReferenceForNull>;
}

@decorators.node<TestDuplicatedSecondChild>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    indexes: [],
    isVitalCollectionChild: true,
})
export class TestDuplicatedSecondChild extends Node {
    @decorators.referenceProperty<TestDuplicatedSecondChild, 'duplicatedParent'>({
        isStored: true,
        isPublished: true,
        node: () => TestDuplicatedParent,
        isVitalParent: true,
    })
    readonly duplicatedParent: Reference<TestDuplicatedParent>;
}

@decorators.node<TestDuplicatedGrandChild>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    tableName: 'TestDuplicatedGrandChild',
    indexes: [],
    isVitalCollectionChild: true,
})
export class TestDuplicatedGrandChild extends Node {
    @decorators.referenceProperty<TestDuplicatedGrandChild, 'duplicatedChildParent'>({
        isStored: true,
        isPublished: true,
        node: () => TestDuplicatedChild,
        isVitalParent: true,
    })
    readonly duplicatedChildParent: Reference<TestDuplicatedChild>;

    @decorators.stringProperty<TestDuplicatedGrandChild, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 30 }),
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestDuplicatedGrandChild, 'stringDuplicateFunction'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 30 }),
        duplicatedValue: 'duplicatedValue',
    })
    readonly stringDuplicateFunction: Promise<string>;
}

@decorators.node<TestComplexDuplicatedValue>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canDuplicate: true,
    tableName: 'TestComplexDuplicatedValue',
    indexes: [],
})
export class TestComplexDuplicatedValue extends Node {
    @decorators.stringProperty<TestComplexDuplicatedValue, 'stringOldValue'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 30 }),
    })
    readonly stringOldValue: Promise<string>;

    @decorators.stringProperty<TestComplexDuplicatedValue, 'stringNewValue'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 30 }),
        duplicatedValue: '',
        duplicateRequiresPrompt: true,
        control(cx, value) {
            if (!value || value === '') {
                cx.error.add('stringNewValue cannot be empty');
            }
        },
    })
    readonly stringNewValue: Promise<string>;
}

@decorators.node<TestDuplicatedBaseNode>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canDuplicate: true,
    isAbstract: true,
})
export class TestDuplicatedBaseNode extends Node {
    @decorators.stringProperty<TestDuplicatedBaseNode, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 30 }),
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestDuplicatedBaseNode, 'stringValue'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 30 }),
        duplicatedValue: 'duplicatedValue',
    })
    readonly stringValue: Promise<string>;
}

@decorators.subNode<TestDuplicatedOverride>({
    isPublished: true,
    extends: () => TestDuplicatedBaseNode,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
})
export class TestDuplicatedOverride extends TestDuplicatedBaseNode {
    @decorators.stringPropertyOverride<TestDuplicatedOverride, 'stringValue'>({
        duplicatedValue: 'duplicateOverride',
    })
    override readonly stringValue: Promise<string>;
}

@decorators.subNode<TestDuplicatedOverride2>({
    isPublished: true,
    extends: () => TestDuplicatedBaseNode,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
})
export class TestDuplicatedOverride2 extends TestDuplicatedBaseNode {
    @decorators.stringPropertyOverride<TestDuplicatedOverride2, 'stringValue'>({
        duplicatedValue: 'anotherDuplicateOverride',
    })
    override readonly stringValue: Promise<string>;
}

export const duplicatedValueData = [
    {
        _id: 1,
        booleanVal: true,
        booleanDuplicateDefault: false,
        booleanDuplicateTrueFunction: false,
        stringDuplicateFunction: 'Some string',
        testReference: 1,
        testReferenceNotNullable: 1,
        stringIndicator: '',
    },
    {
        _id: 2,
        booleanVal: true,
        booleanDuplicateDefault: false,
        booleanDuplicateTrueFunction: false,
        stringDuplicateFunction: 'Non-nullable reference test',
        stringIndicator: 'Non-nullable reference test',
        testReference: 1,
        testReferenceNotNullable: 1,
    },
];

export const duplicatedVitalReferenceData = [
    {
        testDuplicatedValue: 1,
        code: 'COD01',
    },
];

export const duplicatedNonVitalReferenceData = [
    {
        _id: 1,
        code: 'COD01',
    },
];

export const duplicatedReferenceForNull = [{ code: '1' }, { code: '2' }, { code: '3' }];

export const duplicatedValueParentData = [
    {
        _id: 1,
        code: 'CODE01',
        stringDuplicateFunction: 'StingVal01',
    },
    {
        _id: 2,
        code: 'CODE02',
        stringDuplicateFunction: 'StingVal02',
    },
];

export const duplicatedValueChildData = [
    {
        _id: 1,
        duplicatedParent: 1,
        code: 'CHILD0101',
        stringDuplicateFunction: 'StingVal0101',
        _sortValue: 10,
        duplicatedReferenceWithNullDuplicate: 1,
    },
    {
        _id: 2,
        duplicatedParent: 2,
        code: 'CHILD0201',
        stringDuplicateFunction: 'StingVal0201',
        _sortValue: 10,
        duplicatedReferenceWithNullDuplicate: 2,
    },
    {
        _id: 3,
        duplicatedParent: 2,
        code: 'CHILD0202',
        stringDuplicateFunction: 'StingVal0202',
        _sortValue: 20,
        duplicatedReferenceWithNullDuplicate: 3,
    },
];

export const duplicatedValueSecondChildData = [
    {
        _id: 1,
        duplicatedParent: 1,
        _sortValue: 10,
    },
    {
        _id: 2,
        duplicatedParent: 2,
        _sortValue: 10,
    },
    {
        _id: 3,
        duplicatedParent: 2,
        _sortValue: 20,
    },
];

export const duplicatedValueGrandChildData = [
    {
        _id: 1,
        duplicatedChildParent: 1,
        code: 'GRANDCHILD010101',
        stringDuplicateFunction: 'StingVal010101',
        _sortValue: 10,
    },
    {
        _id: 2,
        duplicatedChildParent: 2,
        code: 'GRANDCHILD020101',
        stringDuplicateFunction: 'StingVal020101',
        _sortValue: 10,
    },
    {
        _id: 3,
        duplicatedChildParent: 2,
        code: 'GRANDCHILD020102',
        stringDuplicateFunction: 'StingVal020102',
        _sortValue: 20,
    },
];

export const complexDuplicatedValueData = [
    {
        _id: 1,
        stringOldValue: 'Some string',
        stringNewValue: 'Another string',
    },
    {
        _id: 2,
        stringOldValue: 'Some string 2',
        stringNewValue: 'Another string 2',
    },
];

export const duplicatedOverrideData = [
    {
        _id: 1,
        stringValue: 'Some string',
    },
    {
        _id: 2,
        stringValue: 'Some string 2',
    },
];

export const duplicatedOverrideData2 = [
    {
        _id: 3,
        stringValue: 'Some string 3',
    },
    {
        _id: 4,
        stringValue: 'Some string 4',
    },
];
