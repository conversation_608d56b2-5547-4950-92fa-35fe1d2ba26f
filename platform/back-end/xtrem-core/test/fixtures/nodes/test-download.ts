import { nanoid } from 'nanoid';
import { ConfigManager, decorators, FileStorageManager, Node } from '../../../index';
import { nanoIdDataType } from '../../../lib/runtime/system-data-types';
import { codeDataType, descriptionDataType, urlDataType } from '../data-types/data-types';

@decorators.node<TestDownload>({
    isPublished: true,
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canDelete: true,
    canUpdate: true,
    indexes: [{ orderBy: { key: +1 }, isUnique: true }],
})
export class TestDownload extends Node {
    @decorators.stringProperty<TestDownload, 'key'>({
        isStored: true,
        isPublished: true,
        dataType: () => nanoIdDataType,
        defaultValue: () => {
            return nanoid();
        },
    })
    readonly key: Promise<string>;

    @decorators.stringProperty<TestDownload, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestDownload, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly description: Promise<string>;

    @decorators.stringProperty<TestDownload, 'downloadUrl'>({
        isPublished: true,
        dataType: () => urlDataType,
        computeValue() {
            return FileStorageManager.getOnDemandDownloadUrl(this);
        },
    })
    readonly downloadUrl: Promise<string>;
}

function getDevUrl(type: string, objectKey: string): string {
    const config = ConfigManager.current;
    const key = Buffer.from(JSON.stringify({ objectKey })).toString('base64');
    const host = config.security?.redirectUrl || `http://localhost:${config.server?.port || '8240'}`;
    const url = `${host}/dev/${type}/${key}`;
    return url;
}

FileStorageManager.registerDownloadHandler(TestDownload, async (node: TestDownload) => {
    const key = await node.key;
    return getDevUrl('uploads', `test-download/${key}`);
});
