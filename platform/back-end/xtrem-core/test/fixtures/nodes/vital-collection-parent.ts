import { Collection, decorators, Node } from '../../../lib';
import { codeDataType } from '../data-types/data-types';
import { TestVitalCollectionChild } from './vital-collection-child';

@decorators.node<TestVitalCollectionParent>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true, isNaturalKey: true }],
})
export class TestVitalCollectionParent extends Node {
    @decorators.stringProperty<TestVitalCollectionParent, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestVitalCollectionParent, 'computedLast'>({
        isPublished: true,
        dataType: () => codeDataType,
        async computeValue() {
            const childNodes = await this.children.toArray();
            return (await childNodes[childNodes.length - 1].code) || '';
        },
    })
    readonly computedLast: Promise<string>;

    @decorators.collectionProperty<TestVitalCollectionParent, 'children'>({
        isPublished: true,
        isVital: true,
        node: () => TestVitalCollectionChild,
        reverseReference: 'parent',
    })
    readonly children: Collection<TestVitalCollectionChild>;
}
