import { Time } from '@sage/xtrem-date-time';
import {
    BinaryStream,
    date,
    dateRange,
    datetime,
    datetimeRange,
    decimal,
    decimalRange,
    decorators,
    integer,
    integerRange,
    Node,
    Reference,
    short,
    TextStream,
    Uuid,
} from '../../../index';
import { defaultDecimalDataType, descriptionArrayDataType, descriptionDataType } from '../data-types/data-types';
import { TestEnum, testEnumDataType, TestEnumForArray, testEnumForArrayDataType } from '../enums';
import { mailTemplateType, TestDatatypesJson, testJsonDataType, testTextStreamType } from './datatypes';

function complex(a: integer, b: integer): integer {
    return Math.max(a, b);
}
@decorators.node<TestIsClearedByResetDatatypes>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [{ orderBy: { id: 1 }, isUnique: true }],
})
export class TestIsClearedByResetDatatypes extends Node {
    @decorators.integerProperty<TestIsClearedByResetDatatypes, 'id'>({
        isPublished: true,
        isStored: true,
    })
    readonly id: Promise<integer>;

    @decorators.booleanProperty<TestIsClearedByResetDatatypes, 'booleanVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        isClearedByReset: true,
    })
    readonly booleanVal: Promise<boolean | null>;

    @decorators.shortProperty<TestIsClearedByResetDatatypes, 'shortVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        async isClearedByReset() {
            return (await this.id) === 2;
        },
    })
    readonly shortVal: Promise<short>;

    @decorators.integerProperty<TestIsClearedByResetDatatypes, 'integerVal'>({
        isPublished: true,
        isStored: true,
        isNullable: false,
        isClearedByReset: true,
    })
    readonly integerVal: Promise<integer>;

    @decorators.integerRangeProperty<TestIsClearedByResetDatatypes, 'integerRangeVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        isClearedByReset: true,
    })
    readonly integerRangeVal: Promise<integerRange | null>;

    @decorators.decimalRangeProperty<TestIsClearedByResetDatatypes, 'decimalRangeVal'>({
        isPublished: true,
        isStored: true,
        isNullable: false,
        isClearedByReset: true,
    })
    readonly decimalRangeVal: Promise<decimalRange | null>;

    @decorators.enumProperty<TestIsClearedByResetDatatypes, 'enumVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => testEnumDataType,
        isClearedByReset: true,
    })
    readonly enumVal: Promise<TestEnum | null>;

    @decorators.stringProperty<TestIsClearedByResetDatatypes, 'stringVal'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
        isClearedByReset: true,
        defaultValue: 'test',
    })
    readonly stringVal: Promise<string>;

    @decorators.decimalProperty<TestIsClearedByResetDatatypes, 'decimalVal'>({
        isPublished: true,
        isStored: true,
        dataType: () => defaultDecimalDataType,
        isClearedByReset: true,
    })
    readonly decimalVal: Promise<decimal>;

    @decorators.floatProperty<TestIsClearedByResetDatatypes, 'floatVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        isClearedByReset: true,
    })
    readonly floatVal: Promise<number>;

    @decorators.doubleProperty<TestIsClearedByResetDatatypes, 'doubleVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        isClearedByReset: true,
    })
    readonly doubleVal: Promise<number>;

    @decorators.dateProperty<TestIsClearedByResetDatatypes, 'dateVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        isClearedByReset: true,
    })
    readonly dateVal: Promise<date | null>;

    @decorators.dateRangeProperty<TestIsClearedByResetDatatypes, 'dateRangeVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        isClearedByReset: true,
    })
    readonly dateRangeVal: Promise<dateRange | null>;

    @decorators.datetimeRangeProperty<TestIsClearedByResetDatatypes, 'datetimeRangeVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        isClearedByReset: true,
    })
    readonly datetimeRangeVal: Promise<datetimeRange | null>;

    @decorators.timeProperty<TestIsClearedByResetDatatypes, 'timeVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly timeVal: Promise<Time | null>;

    @decorators.datetimeProperty<TestIsClearedByResetDatatypes, 'datetimeVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly datetimeVal: Promise<datetime | null>;

    @decorators.binaryStreamProperty<TestIsClearedByResetDatatypes, 'binaryStream'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly binaryStream: Promise<BinaryStream | null>;

    @decorators.textStreamProperty<TestIsClearedByResetDatatypes, 'textStream'>({
        isPublished: true,
        isStored: true,
        dataType: () => testTextStreamType,
    })
    readonly textStream: Promise<TextStream>;

    @decorators.textStreamProperty<TestIsClearedByResetDatatypes, 'mailTemplate'>({
        isPublished: true,
        isStored: true,
        noLazyLoad: true,
        dataType: () => mailTemplateType,
    })
    readonly mailTemplate: Promise<TextStream>;

    @decorators.uuidProperty<TestIsClearedByResetDatatypes, 'uuidVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly uuidVal: Promise<Uuid>;

    @decorators.integerProperty<TestIsClearedByResetDatatypes, 'computed'>({
        async getValue() {
            return (await this.id) * (await this.integerVal);
        },
        isPublished: true,
    })
    readonly computed: Promise<integer>;

    @decorators.integerProperty<TestIsClearedByResetDatatypes, 'complexComputed'>({
        computeValue() {
            return complex(10, 1);
        },
        isPublished: true,
    })
    readonly complexComputed: Promise<integer>;

    @decorators.jsonProperty<TestIsClearedByResetDatatypes, 'jsonVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => testJsonDataType,
    })
    readonly jsonVal: Promise<TestDatatypesJson>;

    @decorators.integerArrayProperty<TestIsClearedByResetDatatypes, 'integerArrayVal'>({
        isStored: true,
        isNullable: true,
        isPublished: true,
    })
    readonly integerArrayVal: Promise<integer[] | null>;

    @decorators.enumArrayProperty<TestIsClearedByResetDatatypes, 'enumArrayVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => testEnumForArrayDataType,
    })
    readonly enumArrayVal: Promise<TestEnumForArray[] | null>;

    @decorators.stringArrayProperty<TestIsClearedByResetDatatypes, 'stringArrayVal'>({
        isStored: true,
        isPublished: true,
        dataType: () => descriptionArrayDataType,
    })
    readonly stringArrayVal: Promise<string[] | null>;
}

@decorators.node<TestIsClearedByResetInClassDecorator>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    isClearedByReset: true,
    indexes: [{ orderBy: { id: 1 }, isUnique: true }],
})
export class TestIsClearedByResetInClassDecorator extends Node {
    @decorators.integerProperty<TestIsClearedByResetInClassDecorator, 'id'>({
        isPublished: true,
        isStored: true,
    })
    readonly id: Promise<integer>;

    @decorators.integerProperty<TestIsClearedByResetInClassDecorator, 'integerVal'>({
        isPublished: true,
        isStored: true,
        isNullable: false,
    })
    readonly integerVal: Promise<integer>;
}

@decorators.node<TestIsClearedByResetTooComplex>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [{ orderBy: { id: 1 }, isUnique: true }],
})
export class TestIsClearedByResetTooComplex extends Node {
    @decorators.integerProperty<TestIsClearedByResetTooComplex, 'id'>({
        isPublished: true,
        isStored: true,
    })
    readonly id: Promise<integer>;

    @decorators.integerProperty<TestIsClearedByResetTooComplex, 'integerVal'>({
        isPublished: true,
        isStored: true,
        isNullable: false,
        async isClearedByReset() {
            return Math.log((await this.id) * 12) / 300 === 1;
        },
    })
    readonly integerVal: Promise<integer>;
}

@decorators.node<TestIsClearedByResetWithRef>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
})
export class TestIsClearedByResetWithRef extends Node {
    @decorators.integerProperty<TestIsClearedByResetWithRef, 'sequenceNumber'>({
        isPublished: true,
        isStored: true,
        async isClearedByReset() {
            return (await (await this.ref).id) === 1;
        },
    })
    readonly sequenceNumber: Promise<integer>;

    @decorators.referenceProperty<TestIsClearedByResetWithRef, 'ref'>({
        isPublished: true,
        isStored: true,
        node: () => TestIsClearedByResetDatatypes,
    })
    readonly ref: Reference<TestIsClearedByResetDatatypes>;
}

@decorators.node<TestIsClearedByResetFunctionInNode>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    async isClearedByReset() {
        return (await (await this.ref).codeRef) === 2;
    },
})
export class TestIsClearedByResetFunctionInNode extends Node {
    @decorators.integerProperty<TestIsClearedByResetFunctionInNode, 'code'>({
        isPublished: true,
        isStored: true,
    })
    readonly code: Promise<integer>;

    @decorators.referenceProperty<TestIsClearedByResetFunctionInNode, 'ref'>({
        isPublished: true,
        isStored: true,
        node: () => TestIsClearedByResetRefNode,
    })
    readonly ref: Reference<TestIsClearedByResetRefNode>;
}

@decorators.node<TestIsClearedByResetRefNode>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [{ orderBy: { codeRef: 1 }, isUnique: true }],
})
export class TestIsClearedByResetRefNode extends Node {
    @decorators.integerProperty<TestIsClearedByResetRefNode, 'codeRef'>({
        isPublished: true,
        isStored: true,
    })
    readonly codeRef: Promise<integer>;
}
