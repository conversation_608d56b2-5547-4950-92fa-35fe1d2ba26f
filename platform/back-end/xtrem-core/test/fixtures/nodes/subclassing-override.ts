import { ValidationSeverity } from '@sage/xtrem-shared';
import { Collection, date, decorators, integer, Node, Reference } from '../../../index';
import { codeDataType, descriptionDataType } from '../data-types/data-types';

@decorators.node<TestOverrideAnimal>({
    isPublished: true,
    storage: 'sql',
    isAbstract: true,
    controlBegin(cx): void {
        cx.addDiagnose(ValidationSeverity.info, 'TestOverrideAnimal.controlBegin');
    },
})
export class TestOverrideAnimal extends Node {
    @decorators.stringProperty<TestOverrideAnimal, 'name'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly name: Promise<string>;

    @decorators.referenceProperty<TestOverrideAnimal, 'owner'>({
        isPublished: true,
        isStored: true,
        node: () => TestOverrideAnimalOwner,
        isNullable: true,
    })
    readonly owner: Reference<TestOverrideAnimalOwner | null>;
}

@decorators.node<TestOverrideAnimalOwner>({
    isPublished: true,
    canDeleteMany: true,
    storage: 'sql',
    isAbstract: true,
    controlBegin(cx): void {
        cx.addDiagnose(ValidationSeverity.info, 'TestOverrideAnimal.controlBegin');
    },
})
export class TestOverrideAnimalOwner extends Node {
    @decorators.collectionProperty<TestOverrideAnimalOwner, 'animals'>({
        isPublished: true,
        node: () => TestOverrideAnimal,
        reverseReference: 'owner',
    })
    readonly animals: Collection<TestOverrideAnimal>;

    @decorators.referenceProperty<TestOverrideAnimalOwner, 'favoriteAnimal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        node: () => TestOverrideAnimal,
    })
    readonly favoriteAnimal: Reference<TestOverrideAnimal | null>;
}

// We will have this subNode implemented with only one property in order to test the referencePropertyOverride
@decorators.subNode<TestOverrideHorse>({
    extends: () => TestOverrideAnimal,
    isPublished: true,
})
export class TestOverrideHorse extends TestOverrideAnimal {
    @decorators.referencePropertyOverride<TestOverrideHorse, 'owner'>({
        node: () => TestOverrideHorseOwner,
    })
    override readonly owner: Reference<TestOverrideHorseOwner | null>;

    @decorators.integerProperty<TestOverrideHorse, 'bestRaceResult'>({
        isPublished: true,
        isStored: true,
    })
    readonly bestRaceResult: integer;
}

@decorators.subNode<TestOverrideHorseOwner>({
    extends: () => TestOverrideAnimalOwner,
    isPublished: true,
    canDeleteMany: true,
})
export class TestOverrideHorseOwner extends TestOverrideAnimalOwner {
    @decorators.collectionPropertyOverride<TestOverrideAnimalOwner, 'animals'>({
        node: () => TestOverrideHorse,
    })
    override readonly animals: Collection<TestOverrideHorse>;

    @decorators.integerProperty<TestOverrideHorseOwner, 'racesWon'>({
        isPublished: true,
        isStored: true,
    })
    readonly racesWon: Promise<integer>;
}

@decorators.subNode<TestOverrideCow>({
    extends: () => TestOverrideAnimal,
    isPublished: true,
})
export class TestOverrideCow extends TestOverrideAnimal {}

@decorators.subNode<TestOverrideCowOwner>({
    extends: () => TestOverrideAnimalOwner,
    isPublished: true,
    canDeleteMany: true,
})
export class TestOverrideCowOwner extends TestOverrideAnimalOwner {}

@decorators.node<TestOverrideBaseDocument>({
    isPublished: true,
    storage: 'sql',
    isAbstract: true,
})
export class TestOverrideBaseDocument extends Node {
    @decorators.stringProperty<TestOverrideBaseDocument, 'id'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly id: Promise<string>;

    @decorators.collectionProperty<TestOverrideBaseDocument, 'lines'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'document',
        node: () => TestOverrideBaseDocumentLine,
    })
    readonly lines: Collection<TestOverrideBaseDocumentLine>;
}

@decorators.node<TestOverrideBaseDocumentLine>({
    isPublished: true,
    storage: 'sql',
    isAbstract: true,
    isVitalCollectionChild: true,
})
export class TestOverrideBaseDocumentLine extends Node {
    @decorators.integerProperty<TestOverrideBaseDocumentLine, 'quantity'>({
        isPublished: true,
        isStored: true,
    })
    readonly quantity: Promise<integer>;

    @decorators.referenceProperty<TestOverrideBaseDocumentLine, 'document'>({
        isPublished: true,
        isStored: true,
        isVitalParent: true,
        node: () => TestOverrideBaseDocument,
    })
    readonly document: Reference<TestOverrideBaseDocument>;
}

@decorators.subNode<TestOverrideInvoice>({
    isPublished: true,
    extends: () => TestOverrideBaseDocument,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
})
export class TestOverrideInvoice extends TestOverrideBaseDocument {
    @decorators.collectionPropertyOverride<TestOverrideInvoice, 'lines'>({
        node: () => TestOverrideInvoiceLine,
    })
    override readonly lines: Collection<TestOverrideInvoiceLine>;

    @decorators.booleanProperty<TestOverrideInvoice, 'isClosed'>({
        isPublished: true,
        isStored: true,
    })
    readonly isClosed: Promise<boolean>;
}

@decorators.subNode<TestOverrideInvoiceLine>({
    isPublished: true,
    extends: () => TestOverrideBaseDocumentLine,
})
export class TestOverrideInvoiceLine extends TestOverrideBaseDocumentLine {
    @decorators.referencePropertyOverride<TestOverrideInvoiceLine, 'document'>({
        node: () => TestOverrideInvoice,
    })
    override readonly document: Reference<TestOverrideInvoice>;

    @decorators.dateProperty<TestOverrideInvoiceLine, 'date'>({
        isPublished: true,
        isStored: true,
    })
    readonly date: Promise<date>;
}
