import { decorators, Node } from '../../../index';
import { codeDataType, descriptionDataType } from '../data-types/data-types';

@decorators.node<TestSignal>({
    isPublished: true,
    storage: 'sql',
    indexes: [{ orderBy: { code: +1 }, isUnique: true }],
})
export class TestSignal extends Node {
    @decorators.stringProperty<TestSignal, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestSignal, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly description: Promise<string>;
}
