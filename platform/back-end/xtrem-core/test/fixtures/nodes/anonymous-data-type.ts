import { decorators, Node, StringDataType } from '../../../index';

@decorators.node<TestAnonymousDataType>({
    isPublished: true,
    storage: 'sql',
})
export class TestAnonymousDataType extends Node {
    @decorators.stringProperty<TestAnonymousDataType, 'type'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 80 }),
    })
    readonly type: Promise<string>;
}
