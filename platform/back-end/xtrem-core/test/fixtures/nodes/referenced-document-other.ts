import { decorators, Node } from '../../../index';
import { integer } from '../../../lib';
import { descriptionDataType } from '../data-types/data-types';

@decorators.node<TestReferencedDocumentOther>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestReferencedDocumentOther',
})
export class TestReferencedDocumentOther extends Node {
    @decorators.stringProperty<TestReferencedDocumentOther, 'lookupString'>({
        isStored: true,
        isPublished: true,
        dataType: () => descriptionDataType,
    })
    readonly lookupString: Promise<string>;

    @decorators.integerProperty<TestReferencedDocumentOther, 'controlInteger'>({
        isStored: true,
        isPublished: true,
    })
    readonly controlInteger: Promise<integer>;
}
