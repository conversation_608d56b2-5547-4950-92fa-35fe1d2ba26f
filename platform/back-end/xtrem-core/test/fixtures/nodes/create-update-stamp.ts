import { decorators, integer, Node } from '../../../index';

@decorators.node<TestCreateUpdateStamp>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
})
export class TestCreateUpdateStamp extends Node {
    @decorators.integerProperty<TestCreateUpdateStamp, 'val'>({
        isPublished: true,
        isStored: true,
    })
    readonly val: Promise<integer>;
}
