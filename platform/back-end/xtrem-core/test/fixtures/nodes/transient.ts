import { Reference } from '../../../index';
import { Collection, decorators, Node, NodeStatus, useDefaultValue } from '../../../lib';
import { integer, NodeCreateData } from '../../../lib/ts-api';
import { descriptionDataType } from '../data-types/data-types';
import { TestReferred } from './referred';
import { TestTransientLines } from './transient-lines';

@decorators.node<TestTransient>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [],
    async controlBegin(cx) {
        if (await this.transientRef) {
            await cx.error.if(await (await this.transientRef)?.code).is.equal.to('REF2');
        }
    },
})
export class TestTransient extends Node {
    @decorators.stringProperty<TestTransient, 'stringValue'>({
        isStored: true,
        isPublished: true,
        dataType: () => descriptionDataType,
    })
    readonly stringValue: Promise<string>;

    @decorators.stringProperty<TestTransient, 'transientValue'>({
        isTransientInput: true,
        dataType: () => descriptionDataType,
        isPublished: true,
    })
    readonly transientValue: Promise<string>;

    @decorators.stringProperty<TestTransient, 'derivedValue'>({
        isStored: true,
        dataType: () => descriptionDataType,
        isPublished: true,
        dependsOn: ['transientValue'],
        updatedValue: useDefaultValue,
        async defaultValue() {
            if ((await this.transientValue) !== '') {
                return `transient set in defaultValue (${await this.transientValue})- ${this.$.status}`;
            }

            return `transient not set in defaultValue - ${this.$.status}`;
        },
    })
    readonly derivedValue: Promise<string>;

    @decorators.referenceProperty<TestTransient, 'transientRef'>({
        isPublished: true,
        isTransientInput: true,
        isNullable: true,
        node: () => TestReferred,
    })
    readonly transientRef: Reference<TestReferred | null>;

    @decorators.stringProperty<TestTransient, 'derivedFromRef'>({
        isStored: true,
        dataType: () => descriptionDataType,
        isPublished: true,
        dependsOn: ['transientRef'],
        updatedValue: useDefaultValue,
        async defaultValue() {
            if ((await this.transientRef) != null) {
                return (await this.transientRef)!.code;
            }
            return 'transient reference not set';
        },
        async control(cx) {
            if (this.$.status === NodeStatus.modified && (await this.transientRef)) {
                await cx.error.if(await (await this.transientRef)!.code).is.equal.to('REF1');
            }
        },
    })
    readonly derivedFromRef: Promise<string>;

    @decorators.collectionProperty<TestTransient, 'transientLines'>({
        isPublished: true,
        node: () => TestTransientLines,
        reverseReference: 'parent',
        isTransientInput: true,
    })
    readonly transientLines: Collection<TestTransientLines>;

    @decorators.collectionProperty<TestTransient, 'derivedCollection'>({
        isPublished: true,
        node: () => TestTransientLines,
        reverseReference: 'parent',
        isVital: true,
        defaultValue() {
            return (
                this.transientLines
                    .filter(async node => (await node.stringValue) !== 'EXCLUDE')
                    // TODO: fix typing issue
                    .map(async line => (await line.$.payload()) as unknown as NodeCreateData<TestTransientLines>)
                    .toArray()
            );
        },
    })
    readonly derivedCollection: Collection<TestTransientLines>;

    @decorators.integerProperty<TestTransient, 'derivedFromCollection'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        defaultValue() {
            return this.transientLines.length;
        },
    })
    readonly derivedFromCollection: Promise<integer>;
}
