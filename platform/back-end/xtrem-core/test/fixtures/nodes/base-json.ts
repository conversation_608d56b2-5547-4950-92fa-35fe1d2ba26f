import { Context, decorators, Node } from '../../../index';
import { codeDataType } from '../data-types/data-types';

@decorators.node<TestBaseJson>({
    isPublished: true,
    storage: 'json',
})
export class <PERSON><PERSON><PERSON><PERSON>son extends Node {
    @decorators.stringProperty<TestBaseJson, 'code'>({
        isPublished: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.mutation<typeof TestBaseJson, 'testCreate'>({
        isPublished: true,
        parameters: [{ name: 'code', type: 'string', isMandatory: true }],
        return: {
            type: 'instance',
            node(): typeof TestBaseJson {
                return TestBaseJson;
            },
        },
    })
    static async testCreate(context: Context, code: string): Promise<TestBaseJson> {
        const base = await context.create(Test<PERSON>ase<PERSON><PERSON>, { code });
        return base;
    }
}
