import { Time } from '@sage/xtrem-date-time';
import {
    BinaryStream,
    JsonDataType,
    Node,
    TextStream,
    TextStreamDataType,
    Uuid,
    date,
    dateRange,
    datetime,
    datetimeRange,
    decimal,
    decimalRange,
    decorators,
    integer,
    integerRange,
    short,
} from '../../../index';
import { defaultDecimalDataType, descriptionArrayDataType, descriptionDataType } from '../data-types/data-types';
import { TestEnum, TestEnumForArray, testEnumDataType, testEnumForArrayDataType } from '../enums';

export const testTextStreamType = new TextStreamDataType({
    maxLength: 1500 * 1024,
    allowedContentTypes: ['text/xml', 'text/html', 'text/plain', 'text/css', 'application/xml'],
});

export const mailTemplateType = new TextStreamDataType({
    allowedContentTypes: ['text/plain', 'text/html'],
});

const unsafeMailTemplateType = new TextStreamDataType({
    allowedContentTypes: ['text/plain', 'text/html'],
    dangerouslyUnsafe: true,
});

export interface TestDatatypesJson {
    id?: number;
    code?: string;
    isValidated?: boolean;
    text?: {
        fr?: string;
        en?: string;
        es?: string;
    };
    authors?: Array<string>;
}

export const testJsonDataType = new JsonDataType<unknown, TestDatatypesJson>();

function complex(a: integer, b: integer): integer {
    return Math.max(a, b);
}
@decorators.node<TestDatatypes>({
    tableName: 'TestDatatypes', // Force the class to be classic (X3 compatible)
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [{ orderBy: { id: 1 }, isUnique: true }],
})
export class TestDatatypes extends Node {
    @decorators.integerProperty<TestDatatypes, 'id'>({
        isPublished: true,
        isStored: true,
    })
    readonly id: Promise<integer>;

    @decorators.booleanProperty<TestDatatypes, 'booleanVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly booleanVal: Promise<boolean | null>;

    @decorators.shortProperty<TestDatatypes, 'shortVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly shortVal: Promise<short | null>;

    @decorators.integerProperty<TestDatatypes, 'integerVal'>({
        isPublished: true,
        isStored: true,
    })
    readonly integerVal: Promise<integer>;

    @decorators.integerRangeProperty<TestDatatypes, 'integerRangeVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly integerRangeVal: Promise<integerRange | null>;

    @decorators.decimalRangeProperty<TestDatatypes, 'decimalRangeVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly decimalRangeVal: Promise<decimalRange | null>;

    @decorators.enumProperty<TestDatatypes, 'enumVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => testEnumDataType,
    })
    readonly enumVal: Promise<TestEnum | null>;

    @decorators.stringProperty<TestDatatypes, 'stringVal'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly stringVal: Promise<string>;

    @decorators.decimalProperty<TestDatatypes, 'decimalVal'>({
        isPublished: true,
        isStored: true,
        dataType: () => defaultDecimalDataType,
    })
    readonly decimalVal: Promise<decimal>;

    @decorators.floatProperty<TestDatatypes, 'floatVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly floatVal: Promise<number>;

    @decorators.doubleProperty<TestDatatypes, 'doubleVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly doubleVal: Promise<number>;

    @decorators.dateProperty<TestDatatypes, 'dateVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly dateVal: Promise<date | null>;

    @decorators.dateRangeProperty<TestDatatypes, 'dateRangeVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly dateRangeVal: Promise<dateRange | null>;

    @decorators.datetimeRangeProperty<TestDatatypes, 'datetimeRangeVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly datetimeRangeVal: Promise<datetimeRange | null>;

    @decorators.timeProperty<TestDatatypes, 'timeVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly timeVal: Promise<Time | null>;

    @decorators.datetimeProperty<TestDatatypes, 'datetimeVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly datetimeVal: Promise<datetime | null>;

    @decorators.binaryStreamProperty<TestDatatypes, 'binaryStream'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly binaryStream: Promise<BinaryStream | null>;

    @decorators.textStreamProperty<TestDatatypes, 'textStream'>({
        isPublished: true,
        isStored: true,
        dataType: () => testTextStreamType,
    })
    readonly textStream: Promise<TextStream>;

    @decorators.textStreamProperty<TestDatatypes, 'mailTemplate'>({
        isPublished: true,
        isStored: true,
        noLazyLoad: true,
        dataType: () => mailTemplateType,
    })
    readonly mailTemplate: Promise<TextStream>;

    @decorators.textStreamProperty<TestDatatypes, 'unsafeMailTemplate'>({
        isPublished: true,
        isStored: true,
        noLazyLoad: true,
        dataType: () => unsafeMailTemplateType,
    })
    readonly unsafeMailTemplate: Promise<TextStream>;

    @decorators.uuidProperty<TestDatatypes, 'uuidVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly uuidVal: Promise<Uuid>;

    @decorators.integerProperty<TestDatatypes, 'computed'>({
        dependsOn: ['id', 'integerVal'],
        async getValue() {
            return (await this.id) * (await this.integerVal);
        },
        isPublished: true,
    })
    readonly computed: Promise<integer>;

    @decorators.integerProperty<TestDatatypes, 'computedCached'>({
        dependsOn: ['id', 'integerVal'],
        async getValue() {
            return (await this.id) * (await this.integerVal);
        },
        isPublished: true,
        cacheComputedValue: true,
    })
    readonly computedCached: Promise<integer>;

    @decorators.integerProperty<TestDatatypes, 'complexComputed'>({
        computeValue() {
            return complex(10, 1);
        },
        isPublished: true,
    })
    readonly complexComputed: Promise<integer>;

    @decorators.jsonProperty<TestDatatypes, 'jsonVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => testJsonDataType,
    })
    readonly jsonVal: Promise<TestDatatypesJson>;

    @decorators.integerArrayProperty<TestDatatypes, 'integerArrayVal'>({
        isStored: true,
        isNullable: true,
        isPublished: true,
    })
    readonly integerArrayVal: Promise<integer[] | null>;

    @decorators.enumArrayProperty<TestDatatypes, 'enumArrayVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => testEnumForArrayDataType,
    })
    readonly enumArrayVal: Promise<TestEnumForArray[] | null>;

    @decorators.stringArrayProperty<TestDatatypes, 'stringArrayVal'>({
        isStored: true,
        isPublished: true,
        dataType: () => descriptionArrayDataType,
    })
    readonly stringArrayVal: Promise<string[] | null>;
}
