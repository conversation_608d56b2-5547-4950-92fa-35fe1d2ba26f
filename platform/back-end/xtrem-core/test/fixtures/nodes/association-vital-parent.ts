import { Collection, decorators, Node } from '../../../lib';
import { codeDataType, descriptionDataType } from '../data-types/data-types';
import { TestAssociationItem } from './association-item';

@decorators.node<TestAssociationVitalParent>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true, isNaturalKey: true }],
})
export class TestAssociationVitalParent extends Node {
    @decorators.stringProperty<TestAssociationVitalParent, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestAssociationVitalParent, 'text'>({
        isStored: true,
        isPublished: true,
        dataType: () => descriptionDataType,
        async control(cx, val): Promise<void> {
            await cx.error.if(val).is.equal.to('bad text');
        },
    })
    readonly text: Promise<string>;

    @decorators.collectionProperty<TestAssociationVitalParent, 'associationItems'>({
        isPublished: true,
        isVital: true,
        node: () => TestAssociationItem,
        reverseReference: 'parent',
    })
    readonly associationItems: Collection<TestAssociationItem>;
}
