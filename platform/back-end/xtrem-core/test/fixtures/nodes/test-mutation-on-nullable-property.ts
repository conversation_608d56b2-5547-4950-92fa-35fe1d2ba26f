import { integer } from '@sage/xtrem-shared';
import { date, decorators, Node } from '../../../lib';

@decorators.node<TestMutationOnNullableProperty>({
    tableName: 'TestMutationOnNullableProperty', // Force the class to be classic (X3 compatible)
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
})
export class TestMutationOnNullableProperty extends Node {
    @decorators.dateProperty<TestMutationOnNullableProperty, 'dateVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly dateVal: Promise<date | null>;

    @decorators.integerProperty<TestMutationOnNullableProperty, 'intVal'>({
        isPublished: true,
        isStored: true,
    })
    readonly intVal: Promise<integer>;
}
