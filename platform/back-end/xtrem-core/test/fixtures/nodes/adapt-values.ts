import { decorators, integer, Node, StringDataType } from '../../../index';

export class AdaptValuesStringDataType<T> extends StringDataType<T> {
    // eslint-disable-next-line class-methods-use-this
    public override adaptValue(_node: any, val: string): string {
        return `${val}_data-type`;
    }
}

@decorators.node<TestAdaptValues>({
    tableName: 'TestAdaptValues',
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [{ orderBy: { id: 1 }, isUnique: true }],
})
export class TestAdaptValues extends Node {
    @decorators.integerProperty<TestAdaptValues, 'id'>({
        isPublished: true,
        isStored: true,
    })
    readonly id: Promise<integer>;

    @decorators.stringProperty<TestAdaptValues, 'dataTypeAdaptValue'>({
        isPublished: true,
        isStored: true,
        dataType: () => new AdaptValuesStringDataType({ maxLength: 100 }),
    })
    readonly dataTypeAdaptValue: Promise<string>;

    @decorators.stringProperty<TestAdaptValues, 'propertyAdaptValue'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 100 }),
        adaptValue(val) {
            return `${val}_property`;
        },
    })
    readonly propertyAdaptValue: Promise<string>;

    @decorators.stringProperty<TestAdaptValues, 'bothAdaptValue'>({
        isPublished: true,
        isStored: true,
        dataType: () => new AdaptValuesStringDataType({ maxLength: 100 }),
        adaptValue(val) {
            return `${val}_property`;
        },
    })
    readonly bothAdaptValue: Promise<string>;

    @decorators.stringProperty<TestAdaptValues, 'bothAdaptValue2'>({
        isPublished: true,
        isStored: true,
        dataType: () => new AdaptValuesStringDataType({ maxLength: 100 }),
        adaptValue(val) {
            return `${val}_property`;
        },
    })
    readonly bothAdaptValue2: Promise<string>;

    @decorators.stringProperty<TestAdaptValues, 'extendedAdaptValue'>({
        isPublished: true,
        isStored: true,
        dataType: () => new AdaptValuesStringDataType({ maxLength: 100 }),
        adaptValue(val) {
            return `${val}_property`;
        },
    })
    readonly extendedAdaptValue: Promise<string>;

    @decorators.stringProperty<TestAdaptValues, 'trimAdaptValue'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 100 }),
    })
    readonly trimAdaptValue: Promise<string>;

    @decorators.stringProperty<TestAdaptValues, 'doNotTrimAdaptValue'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ doNotTrim: true, maxLength: 100 }),
    })
    readonly doNotTrimAdaptValue: Promise<string>;

    @decorators.stringProperty<TestAdaptValues, 'truncateAdaptValue'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ truncate: true, maxLength: 30 }),
    })
    readonly truncateAdaptValue: Promise<string>;
}
