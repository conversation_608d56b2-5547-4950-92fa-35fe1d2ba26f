import { asyncArray } from '@sage/xtrem-async-helper';
import { assert } from 'chai';
import { BinaryStream, Context, decorators, integer, Node, TextStream } from '../../../lib/index';
import * as fixtures from '../index';

type ComplexParameters = {
    object: { simple?: boolean; mandatory: string; nullable?: integer | null };
    optionalObjects?: { nestedStrings?: string[] | null; flag?: boolean }[];
};

type ObjectWithArrayOfEnums = {
    statusList: fixtures.enums.TestEnumEnum[];
};

@decorators.node<TestOperationReference>({
    storage: 'sql',
    isPublished: true,
})
export class TestOperationReference extends Node {
    @decorators.stringProperty<TestOperationReference, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => fixtures.dataTypes.codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.integerProperty<TestOperationReference, 'quantity'>({
        isPublished: true,
        isStored: true,
    })
    readonly quantity: Promise<integer>;
}

@decorators.node<TestOperation>({
    storage: 'json',
    isPublished: true,
})
export class TestOperation extends Node {
    @decorators.stringProperty<TestOperation, 'code'>({
        isPublished: true,
        dataType: () => fixtures.dataTypes.codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.integerProperty<TestOperation, 'value'>({
        isPublished: true,
    })
    readonly value: Promise<integer>;

    @decorators.mutation<typeof TestOperation, 'mutationReturningString'>({
        isPublished: true,
        parameters: [
            { name: 'code', type: 'string' },
            { name: 'stringVal', type: 'string' },
            { name: 'intVal', type: 'integer' },
        ],
        return: 'string',
    })
    static async mutationReturningString(
        context: Context,
        code: string,
        stringVal: string,
        intVal: integer,
    ): Promise<string> {
        const n = await context.create(TestOperation, { code, value: intVal });
        return `mutation ${stringVal}:${await n.value}`;
    }

    @decorators.query<typeof TestOperation, 'queryReturningString'>({
        isPublished: true,
        parameters: [
            { name: 'code', type: 'string' },
            { name: 'stringVal', type: 'string' },
            { name: 'intVal', type: 'integer' },
            { name: 'enumVal', type: 'enum', dataType: () => fixtures.enums.testEnumDataType },
        ],
        return: 'string',
    })
    static queryReturningString(
        context: Context,
        code: string,
        stringVal: string,
        intVal: integer,
        enumVal: fixtures.enums.TestEnum,
    ): Promise<string> {
        return Promise.resolve(`query ${stringVal}:${intVal}:${enumVal}`);
    }

    @decorators.mutation<typeof TestOperation, 'mutationReturningNode'>({
        isPublished: true,
        parameters: [
            {
                name: 'code',
                type: 'string',
                isMandatory: true,
            },
            { name: 'intVal', type: 'integer' },
        ],
        return: {
            type: 'instance',
            node: () => TestOperation,
        },
    })
    static mutationReturningNode(context: Context, code: string, intVal: integer): Promise<TestOperation> {
        return context.create(TestOperation, { code, value: intVal });
    }

    @decorators.mutation<typeof TestOperation, 'mutationWithNodeParameter'>({
        isPublished: true,
        parameters: [{ name: 'arg', type: 'instance', isMandatory: true, node: () => TestOperation }],
        return: {
            type: 'instance',
            node: () => TestOperation,
        },
    })
    static mutationWithNodeParameter(context: Context, arg: TestOperation): Promise<TestOperation> {
        assert.instanceOf(arg, TestOperation);
        return Promise.resolve(arg);
    }

    @decorators.query<typeof TestOperation, 'queryWithComplexInput'>({
        isPublished: true,
        parameters: [
            {
                name: 'object',
                type: 'object',
                isMandatory: true,
                properties: {
                    simple: 'boolean',
                    mandatory: {
                        type: 'string',
                        isMandatory: true,
                    },
                    nullable: {
                        type: 'string',
                        isNullable: true,
                    },
                },
            },
            {
                name: 'optionalObjects',
                type: 'array',
                item: {
                    type: 'object',
                    properties: {
                        nestedStrings: {
                            type: 'array',
                            item: 'string',
                        },
                        flag: 'boolean',
                    },
                },
            },
        ],
        return: 'string',
    })
    static queryWithComplexInput(
        context: Context,
        object: ComplexParameters['object'],
        optionalObjects?: ComplexParameters['optionalObjects'],
    ): Promise<string> {
        return Promise.resolve(JSON.stringify({ object, optionalObjects }));
    }

    @decorators.query<typeof TestOperation, 'queryWithComplexOutput'>({
        isPublished: true,
        parameters: [
            {
                name: 'object',
                type: 'object',
                isMandatory: true,
                properties: {
                    simple: 'boolean',
                    mandatory: {
                        type: 'string',
                        isMandatory: true,
                    },
                    nullable: {
                        type: 'string',
                        isNullable: true,
                    },
                },
            },
            {
                name: 'optionalObjects',
                type: 'array',
                item: {
                    type: 'object',
                    properties: {
                        nestedStrings: {
                            type: 'array',
                            item: 'string',
                        },
                        flag: 'boolean',
                    },
                },
            },
        ],
        return: {
            type: 'object',
            properties: {
                object: {
                    type: 'object',
                    isMandatory: true,
                    properties: {
                        simple: 'boolean',
                        mandatory: {
                            type: 'string',
                            isMandatory: true,
                        },
                        nullable: {
                            type: 'string',
                            isNullable: true,
                        },
                    },
                },
                optionalObjects: {
                    type: 'array',
                    item: {
                        type: 'object',
                        properties: {
                            nestedStrings: {
                                type: 'array',
                                item: 'string',
                            },
                            flag: 'boolean',
                        },
                    },
                },
            },
        },
    })
    static queryWithComplexOutput(
        context: Context,
        object: ComplexParameters['object'],
        optionalObjects?: ComplexParameters['optionalObjects'],
    ): Promise<ComplexParameters> {
        return Promise.resolve({ object, optionalObjects: optionalObjects?.map(opt => ({ flag: false, ...opt })) });
    }

    @decorators.query<typeof TestOperation, 'queryWithReferences'>({
        isPublished: true,
        parameters: [
            { name: 'reference', type: 'reference', node: () => TestOperationReference },
            { name: 'nullableReference', type: 'reference', node: () => TestOperationReference, isNullable: true },
            {
                name: 'arrayOfReferences',
                type: 'array',
                item: {
                    type: 'reference',
                    node: () => TestOperationReference,
                },
            },
            {
                name: 'arrayOfNullableReferences',
                type: 'array',
                item: {
                    type: 'reference',
                    node: () => TestOperationReference,
                    isNullable: true,
                },
            },
            {
                name: 'nested',
                type: 'object',
                properties: {
                    reference: {
                        type: 'reference',
                        node: () => TestOperationReference,
                    },
                    nullableReference: {
                        type: 'reference',
                        node: () => TestOperationReference,
                        isNullable: true,
                    },
                    arrayOfReferences: {
                        type: 'array',
                        item: {
                            type: 'reference',
                            node: () => TestOperationReference,
                        },
                    },
                    arrayOfNullableReferences: {
                        type: 'array',
                        item: {
                            type: 'reference',
                            node: () => TestOperationReference,
                            isNullable: true,
                        },
                    },
                },
            },
        ],
        return: {
            type: 'object',
            properties: {
                reference: {
                    type: 'reference',
                    node: () => TestOperationReference,
                },
                nullableReference: {
                    type: 'reference',
                    node: () => TestOperationReference,
                    isNullable: true,
                },
                arrayOfReferences: {
                    type: 'array',
                    item: {
                        type: 'reference',
                        node: () => TestOperationReference,
                    },
                },
                arrayOfNullableReferences: {
                    type: 'array',
                    item: {
                        type: 'reference',
                        node: () => TestOperationReference,
                        isNullable: true,
                    },
                },
                nested: {
                    type: 'object',
                    properties: {
                        reference: {
                            type: 'reference',
                            node: () => TestOperationReference,
                        },
                        nullableReference: {
                            type: 'reference',
                            node: () => TestOperationReference,
                            isNullable: true,
                        },
                        arrayOfReferences: {
                            type: 'array',
                            item: {
                                type: 'reference',
                                node: () => TestOperationReference,
                            },
                        },
                        arrayOfNullableReferences: {
                            type: 'array',
                            item: {
                                type: 'reference',
                                node: () => TestOperationReference,
                                isNullable: true,
                            },
                        },
                    },
                },
            },
        },
    })
    static queryWithReferences(
        context: Context,
        reference: TestOperationReference,
        nullableReference: TestOperationReference | null,
        arrayOfReferences: TestOperationReference[],
        arrayOfNullableReferences: (TestOperationReference | null)[],
        nested: {
            reference: TestOperationReference;
            nullableReference: TestOperationReference | null;
            arrayOfReferences: TestOperationReference[];
            arrayOfNullableReferences: (TestOperationReference | null)[];
        },
    ) {
        assert.instanceOf(reference, TestOperationReference);
        if (nullableReference !== null) assert.instanceOf(nullableReference, TestOperationReference);
        assert.isArray(arrayOfReferences);
        arrayOfReferences.forEach(ref => assert.instanceOf(ref, TestOperationReference));
        arrayOfNullableReferences.forEach(ref => {
            if (ref !== null) assert.instanceOf(ref, TestOperationReference);
        });
        assert.isObject(nested);

        return Promise.resolve({ reference, nullableReference, arrayOfReferences, arrayOfNullableReferences, nested });
    }

    @decorators.query<typeof TestOperation, 'queryReturningSimpleArray'>({
        isPublished: true,
        parameters: [{ name: 'len', type: 'integer' }],
        return: {
            type: 'array',
            item: 'string',
        },
    })
    static queryReturningSimpleArray(context: Context, len: integer) {
        return Promise.resolve([...Array(len)].map((_, i) => `element ${i}`));
    }

    @decorators.query<typeof TestOperation, 'queryReturningArrayOfObjects'>({
        isPublished: true,
        parameters: [{ name: 'len', type: 'integer' }],
        return: {
            type: 'array',
            item: {
                type: 'object',
                properties: {
                    index: 'integer',
                    text: 'string',
                },
            },
        },
    })
    static queryReturningArrayOfObjects(context: Context, len: integer) {
        return Promise.resolve([...Array(len)].map((_, i) => ({ index: i, text: `element ${i}` })));
    }

    @decorators.mutation<typeof TestOperation, 'mutationArrayOfInstanceReturningString'>({
        isPublished: true,
        parameters: [
            {
                name: 'instanceArray',
                type: 'array',
                item: {
                    type: 'instance',
                    node: () => TestOperation,
                },
            },
        ],
        return: 'string',
    })
    static mutationArrayOfInstanceReturningString(context: Context, instanceArray: TestOperation[]): Promise<string> {
        instanceArray.forEach(instance => assert.instanceOf(instance, TestOperation));
        return asyncArray(instanceArray)
            .map(async instance => `${await instance.code}${await instance.value}`)
            .join(';');
    }

    @decorators.mutation<typeof TestOperation, 'mutationWithBinaryStreamAsResult'>({
        isPublished: true,
        parameters: [
            {
                name: 'binaryContent',
                type: 'string',
                isMandatory: true,
            },
        ],
        return: {
            type: 'binaryStream',
        },
    })
    static mutationWithBinaryStreamAsResult(context: Context, base64Data: string): Promise<BinaryStream> {
        return Promise.resolve(new BinaryStream(Buffer.from(base64Data, 'base64')));
    }

    @decorators.mutation<typeof TestOperation, 'mutationWithTextStreamAsResult'>({
        isPublished: true,
        parameters: [
            {
                name: 'textContent',
                type: 'string',
                isMandatory: true,
            },
        ],
        return: {
            type: 'textStream',
        },
    })
    static mutationWithTextStreamAsResult(context: Context, textContent: string): Promise<TextStream> {
        return Promise.resolve(new TextStream(textContent));
    }

    @decorators.query<typeof TestOperation, 'queryWithOptionalArgs'>({
        isPublished: true,
        parameters: [
            { name: 'option1', type: 'string', isMandatory: false },
            { name: 'option2', type: 'string', isMandatory: false },
            { name: 'option3', type: 'string', isMandatory: false },
        ],
        return: 'string',
    })
    static queryWithOptionalArgs(
        context: Context,
        option1?: string,
        option2?: string,
        option3 = 'def3',
    ): Promise<string> {
        return Promise.resolve(`${option1}:${option2}:${option3}`);
    }

    @decorators.mutation<typeof TestOperation, 'mutationWithNonWritableReferenceParameter'>({
        isPublished: true,
        parameters: [{ name: 'reference', type: 'reference', node: () => TestOperationReference }],
        return: {
            type: 'string',
        },
    })
    static async mutationWithNonWritableReferenceParameter(
        context: Context,
        reference: TestOperationReference,
    ): Promise<string> {
        await reference.$.set({ code: 'NEW_CODE' });
        await reference.$.save();
        return reference.code;
    }

    @decorators.mutation<typeof TestOperation, 'mutationWithWritableReferenceParameter'>({
        isPublished: true,
        parameters: [{ name: 'reference', type: 'reference', node: () => TestOperationReference, isWritable: true }],
        return: {
            type: 'string',
        },
    })
    static async mutationWithWritableReferenceParameter(
        context: Context,
        reference: TestOperationReference,
    ): Promise<string> {
        await reference.$.set({ code: 'NEW_CODE' });
        await reference.$.save();
        const node = await context.read(TestOperationReference, { _id: reference._id });
        return node.code;
    }

    @decorators.mutation<typeof TestOperation, 'mutationStartedReadonlyRunningWithWritable'>({
        isPublished: true,
        parameters: [{ name: 'reference', type: 'reference', node: () => TestOperationReference }],
        return: {
            type: 'string',
        },
        startsReadOnly: true,
    })
    static async mutationStartedReadonlyRunningWithWritable(
        context: Context,
        reference: TestOperationReference,
    ): Promise<string> {
        let code = await reference.code;
        const id = reference._id;
        if (!context.isWritable) {
            code = 'START_READONLY_RUN_WRITABLE';
        }

        const result = await context.runInWritableContext(async writableContext => {
            const ref = await writableContext.read(TestOperationReference, { _id: id }, { forUpdate: true });

            await ref.$.set({ code });

            await ref.$.save();

            return ref.code;
        });
        return result;
    }

    @decorators.mutation<typeof TestOperation, 'mutationArrayOfEnums'>({
        isPublished: true,
        parameters: [
            {
                name: 'objectWithArrayOfEnums',
                type: 'object',
                isMandatory: false,
                properties: {
                    statusList: {
                        type: 'array',
                        item: {
                            type: 'enum',
                            dataType: () => fixtures.enums.testEnumDataType,
                        },
                    },
                },
            },
        ],
        return: {
            type: 'string',
        },
    })
    static mutationArrayOfEnums(context: Context, objectWithArrayOfEnums: ObjectWithArrayOfEnums): Promise<string> {
        return Promise.resolve(
            JSON.stringify(objectWithArrayOfEnums.statusList.map(code => fixtures.enums.TestEnumEnum[code])),
        );
    }

    @decorators.mutation<typeof TestOperation, 'mutationWithoutParameters'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'string',
        },
    })
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    static mutationWithoutParameters(context: Context): Promise<string> {
        return Promise.resolve('hello');
    }
}
