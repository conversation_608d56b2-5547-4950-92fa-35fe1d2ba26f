import { decorators, integer, JoinLiteralValue, Node, Reference } from '../../../index';
import { codeDataType, descriptionDataType } from '../data-types/data-types';
import { TestReferred } from './referred';

@decorators.node<TestReferring>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    tableName: 'TestReferring',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestReferring extends Node {
    @decorators.stringProperty<TestReferring, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestReferring, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly description: Promise<string>;

    @decorators.stringProperty<TestReferring, 'restricted'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
        accessCode: 'RESTRICTED',
    })
    readonly restricted: Promise<string>;

    @decorators.referenceProperty<TestReferring, 'reference'>({
        isPublished: true,
        isStored: true,
        node: () => TestReferred,
        isNullable: true,
    })
    readonly reference: Reference<TestReferred | null>;

    @decorators.referenceArrayProperty<TestReferring, 'referenceArray'>({
        isPublished: true,
        onDelete: 'restrict',
        isStored: true,
        node: () => TestReferred,
        isNullable: true,
    })
    readonly referenceArray: Promise<TestReferred[] | null>;

    @decorators.integerProperty<TestReferring, 'nestedComputedVal'>({
        async getValue() {
            return 2 * ((await (await this.reference)?.integerVal) ?? 0);
        },
    })
    readonly nestedComputedVal: Promise<integer>;
}

@decorators.node<TestMandatoryReferring>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestMandatoryReferring',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestMandatoryReferring extends Node {
    @decorators.stringProperty<TestMandatoryReferring, 'code'>({
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestMandatoryReferring, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly description: Promise<string>;

    @decorators.stringProperty<TestMandatoryReferring, 'restricted'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
        accessCode: 'RESTRICTED',
    })
    readonly restricted: Promise<string>;

    @decorators.referenceProperty<TestMandatoryReferring, 'reference'>({
        isPublished: true,
        isStored: true,
        node: () => TestReferred,
    })
    readonly reference: Reference<TestReferred>;
}

@decorators.node<TestReferringWithJoin>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestReferringWithJoin extends Node {
    @decorators.stringProperty<TestReferringWithJoin, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestReferringWithJoin, 'referenceCode'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly referenceCode: Promise<string>;

    @decorators.referenceProperty<TestReferringWithJoin, 'reference'>({
        isPublished: true,
        node: () => TestReferred,
        join: {
            code() {
                return this.referenceCode;
            },
        },
        isNullable: true,
    })
    readonly reference: Reference<TestReferred | null>;

    @decorators.referenceProperty<TestReferringWithJoin, 'propertyJoinReference'>({
        isPublished: true,
        node: () => TestReferred,
        join: {
            code: 'referenceCode',
        },
        isNullable: true,
    })
    readonly propertyJoinReference: Reference<TestReferred | null>;

    @decorators.referenceProperty<TestReferringWithJoin, 'joinLiteralReference'>({
        isPublished: true,
        node: () => TestReferred,
        join: {
            code: new JoinLiteralValue('REF1'),
        },
        isNullable: true,
    })
    readonly joinLiteralReference: Reference<TestReferred | null>;

    @decorators.referenceProperty<TestReferringWithJoin, 'deepJoinReference'>({
        isPublished: true,
        node: () => TestReferring,
        join: {
            reference: {
                code() {
                    return this.referenceCode;
                },
            },
        },
        isNullable: true,
    })
    readonly deepJoinReference: Reference<TestReferring | null>;
}
