import { decorators, integer, Node, StringDataType } from '../../../index';

@decorators.node<TestCachedEncryptedValues>({
    isPublished: true,
    isCached: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [{ orderBy: { id: 1 }, isUnique: true }],
})
export class TestCachedEncryptedValues extends Node {
    @decorators.integerProperty<TestCachedEncryptedValues, 'id'>({
        isPublished: true,
        isStored: true,
    })
    readonly id: Promise<integer>;

    @decorators.stringProperty<TestCachedEncryptedValues, 'passwordValue'>({
        isPublished: true,
        isStored: true,
        isStoredEncrypted: true,
        dataType: () => new StringDataType({ maxLength: 250 }),
    })
    readonly passwordValue: Promise<string>;
}
