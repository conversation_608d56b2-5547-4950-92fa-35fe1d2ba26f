import { decorators, Node } from '../../../index';
import { codeDataType } from '../data-types/data-types';

/**
 * This node is used to test the onDelete behavior on referenceArray properties.
 * It works with the TestNodeWithReferenceArrays node
 */
@decorators.node<TestReferenceForReferenceArrays>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    indexes: [{ orderBy: { code: +1 }, isUnique: true }],
})
export class TestReferenceForReferenceArrays extends Node {
    @decorators.stringProperty<TestReferenceForReferenceArrays, 'code'>({
        isStored: true,
        dataType: () => codeDataType,
        isPublished: true,
    })
    readonly code: Promise<string>;
}
