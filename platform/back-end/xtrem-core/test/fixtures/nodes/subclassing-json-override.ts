import { JsonDataType, Node, ValidationSeverity, decorators } from '../../../index';

export interface HorseDatatypesJson {
    name?: string;
    size?: number;
}

// This specific base node will only have one JSON property which will be queried from its subNode in order to test
// its proper functioning.

@decorators.node<TestAnimalJsonOverride>({
    isPublished: true,
    storage: 'sql',
    isAbstract: true,
    controlBegin(cx): void {
        cx.addDiagnose(ValidationSeverity.info, 'TestAnimalJsonOverride.controlBegin');
    },
})
export class TestAnimalJsonOverride extends Node {
    @decorators.jsonProperty<TestAnimalJsonOverride, 'jsonAnimal'>({
        isPublished: true,
        isStored: true,
        dataType: () => new JsonDataType<unknown, HorseDatatypesJson>(),
    })
    readonly jsonAnimal: Promise<HorseDatatypesJson>;
}

@decorators.subNode<TestHorse>({
    extends: () => TestAnimalJsonOverride,
    isPublished: true,
})
export class TestHorse extends TestAnimalJsonOverride {
    @decorators.jsonPropertyOverride<TestHorse, 'jsonAnimal'>({})
    override readonly jsonAnimal: Promise<HorseDatatypesJson>;
}
