import { BinaryStream, decorators, integer, Node, short, StringDataType, TextStream } from '../../../lib';
import { codeDataType, descriptionDataType } from '../data-types/_index';

const hashStringDataType = new StringDataType({ maxLength: 50 });
const shortHashStringDataType = new StringDataType({ maxLength: 10 });

@decorators.node<TestAnonymizeExport>({
    storage: 'sql',
    isPublished: true,
    tableName: 'TestAnonymizeExport',
    indexes: [],
})
export class TestAnonymizeExport extends Node {
    @decorators.stringProperty<TestAnonymizeExport, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestAnonymizeExport, 'stringRegular'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly stringRegular: Promise<string>;

    @decorators.stringProperty<TestAnonymizeExport, 'stringFixed'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
        dataSensitivityLevel: 'notSensitive',
        anonymizeMethod: 'fixed',
        anonymizeValue: 'FixedValue',
    })
    readonly stringFixed: Promise<string>;

    @decorators.stringProperty<TestAnonymizeExport, 'stringHash'>({
        isPublished: true,
        isStored: true,
        dataType: () => hashStringDataType,
        dataSensitivityLevel: 'notSensitive',
        anonymizeMethod: 'hash',
    })
    readonly stringHash: Promise<string>;

    @decorators.stringProperty<TestAnonymizeExport, 'stringShortHash'>({
        isPublished: true,
        isStored: true,
        dataType: () => shortHashStringDataType,
        dataSensitivityLevel: 'notSensitive',
        anonymizeMethod: 'hash',
    })
    readonly stringShortHash: Promise<string>;

    @decorators.stringProperty<TestAnonymizeExport, 'stringHashLimit'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
        dataSensitivityLevel: 'notSensitive',
        anonymizeMethod: 'hashLimit',
        anonymizeValue: 15,
    })
    readonly stringHashLimit: Promise<string>;

    @decorators.integerProperty<TestAnonymizeExport, 'integerRandom'>({
        isPublished: true,
        isStored: true,
        dataSensitivityLevel: 'notSensitive',
        anonymizeMethod: 'random',
    })
    readonly integerRandom: Promise<integer>;

    @decorators.integerProperty<TestAnonymizeExport, 'integerLimitRandom'>({
        isPublished: true,
        isStored: true,
        dataSensitivityLevel: 'notSensitive',
        anonymizeMethod: 'random',
        max: 500,
    })
    readonly integerLimitRandom: Promise<integer>;

    @decorators.shortProperty<TestAnonymizeExport, 'shortRandom'>({
        isPublished: true,
        isStored: true,
        dataSensitivityLevel: 'notSensitive',
        anonymizeMethod: 'random',
    })
    readonly shortRandom: Promise<short>;

    @decorators.stringProperty<TestAnonymizeExport, 'stringCustom'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
        dataSensitivityLevel: 'notSensitive',
        anonymizeMethod: 'custom',
        anonymizeValue(value) {
            return value.split('').reverse().join('');
        },
    })
    readonly stringCustom: Promise<string>;

    @decorators.stringProperty<TestAnonymizeExport, 'stringPerCharacter'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
        dataSensitivityLevel: 'notSensitive',
        anonymizeMethod: 'perCharRandom',
    })
    readonly stringPerCharacter: Promise<string>;

    @decorators.stringProperty<TestAnonymizeExport, 'urlToAnonymize'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
        dataSensitivityLevel: 'notSensitive',
        anonymizeMethod: 'url',
    })
    readonly urlToAnonymize: Promise<string>;

    @decorators.binaryStreamProperty<TestAnonymizeExport, 'pdf'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataSensitivityLevel: 'notSensitive',
        anonymizeMethod: 'binary',
        anonymizeValue: 'pdf',
    })
    readonly pdf: Promise<BinaryStream> | null;

    @decorators.binaryStreamProperty<TestAnonymizeExport, 'image'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataSensitivityLevel: 'notSensitive',
        anonymizeMethod: 'binary',
        anonymizeValue: 'image',
    })
    readonly image: Promise<BinaryStream> | null;

    @decorators.textStreamProperty<TestAnonymizeExport, 'textStream'>({
        isPublished: true,
        isStored: true,
        dataSensitivityLevel: 'notSensitive',
        anonymizeMethod: 'fixed',
        anonymizeValue: TextStream.fromString('This text was anonymized.'),
    })
    readonly textStream: Promise<TextStream> | null;
}
