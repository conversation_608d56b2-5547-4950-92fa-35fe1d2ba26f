import { Reference } from '../../../index';
import { decorators, Node } from '../../../lib';
import { descriptionDataType } from '../data-types/data-types';
import { TestTransient } from './transient';

@decorators.node<TestTransientLines>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [],
    isVitalCollectionChild: true,
})
export class TestTransientLines extends Node {
    @decorators.stringProperty<TestTransientLines, 'stringValue'>({
        isStored: true,
        isPublished: true,
        dataType: () => descriptionDataType,
    })
    readonly stringValue: Promise<string>;

    @decorators.referenceProperty<TestTransientLines, 'parent'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => TestTransient,
    })
    readonly parent: Reference<TestTransient>;
}
