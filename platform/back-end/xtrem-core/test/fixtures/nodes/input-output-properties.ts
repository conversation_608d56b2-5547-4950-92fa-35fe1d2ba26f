import { decorators, Node } from '../../../lib';
import { descriptionDataType } from '../data-types/data-types';

@decorators.node<TestIoProperties>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [],
})
export class TestIoProperties extends Node {
    @decorators.stringProperty<TestIoProperties, 'inputAndOutputProperty'>({
        isStored: true,
        isPublished: true,
        dataType: () => descriptionDataType,
    })
    readonly inputAndOutputProperty: Promise<string>;

    @decorators.stringProperty<TestIoProperties, 'inputOnlyProperty'>({
        isTransientInput: true,
        dataType: () => descriptionDataType,
        isPublished: true,
    })
    readonly inputOnlyProperty: Promise<string>;

    @decorators.stringProperty<TestIoProperties, 'outputOnlyPropertyGet'>({
        dataType: () => descriptionDataType,
        isPublished: true,
        getValue(): string {
            return 'outputtedGet';
        },
    })
    readonly outputOnlyPropertyGet: Promise<string>;

    @decorators.stringProperty<TestIoProperties, 'outputOnlyPropertyCompute'>({
        dataType: () => descriptionDataType,
        isPublished: true,
        getValue(): string {
            return 'outputtedCompute';
        },
    })
    readonly outputOnlyPropertyCompute: Promise<string>;
}
