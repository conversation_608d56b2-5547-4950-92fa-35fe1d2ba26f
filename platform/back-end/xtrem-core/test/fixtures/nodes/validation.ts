import { assert } from 'chai';
import { date, decorators, integer, Node } from '../../../index';
import { descriptionDataType } from '../data-types/data-types';

@decorators.node<TestValidationBasic>({
    storage: 'json',
    // Class level controls
    // This is the right place for controls involving several properties when there is
    // no clear hierarchy between the properties.
    async controlEnd(cx): Promise<void> {
        await cx
            .at('s1')
            .error.if(await this.s1)
            .is.not.equal.to(await this.s2);
        await cx
            .at('i2')
            .error.if(await this.i1)
            .is.not.greater.than(await this.i2);
        // void/string type mismatch is detected if next line is commented out
        // noUppercase: validators.hasNoUppercase,
    },
})
export class TestValidationBasic extends Node {
    @decorators.stringProperty<TestValidationBasic, 's1'>({
        dataType: () => descriptionDataType,
        async control(cx, val) {
            await cx.error.if(val).is.empty();
        },
    })
    readonly s1: Promise<string>;

    @decorators.stringProperty<TestValidationBasic, 's2'>({
        dataType: () => descriptionDataType,
        // control can also be a simple function
        // note that `this` and the type of val are correctly set inside the function
        async control(cx, val): Promise<void> {
            await cx.error.if(val).is.not.equal.to(await this.s1);
            await cx.error.if(val).is.matching(/abc/);
        },
    })
    readonly s2: Promise<string>;

    @decorators.stringProperty<TestValidationBasic, 's3'>({
        dataType: () => descriptionDataType,
        async control(cx, val): Promise<void> {
            await cx.error.if(val).is.not.equal.to(await this.s1);
            await cx.error.if(val).is.matching(/abc/);
            await cx.error.if(val).is.matching(/bcd/);
        },
    })
    readonly s3: Promise<string>;

    // typing detects mismatch on value type
    // this is not tested by mocha - you have to modify in vscode and see the result
    @decorators.integerProperty<TestValidationBasic, 'i1'>({
        // integer/string type mismatch is detected if next line is commented out
        // control: validators.hasNoUppercase,
        async control(cx, val): Promise<void> {
            await cx.error.if(val).is.not.greater.than(await this.i2);
            await cx.expect(val).to.be.in([1, 2]);
        },
    })
    readonly i1: Promise<integer>;

    // no control on integer
    @decorators.integerProperty<TestValidationBasic, 'i2'>({})
    readonly i2: Promise<integer>;
}

@decorators.node<TestValidationPersistent>({
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canDelete: true,
    canUpdate: true,
    isPublished: true,
    // Class level controls
    // This is the right place for controls involving several properties when there is
    // no clear hierarchy between the properties.
    async controlEnd(cx): Promise<void> {
        await cx
            .at('s1')
            .error.if(await this.s1)
            .is.not.equal.to(await this.s2);
        await cx
            .at('i2')
            .error.if(await this.i1)
            .is.not.greater.than(await this.i2);
        // void/string type mismatch is detected if next line is commented out
        // noUppercase: validators.hasNoUppercase,
    },
})
export class TestValidationPersistent extends Node {
    @decorators.stringProperty<TestValidationPersistent, 's1'>({
        isStored: true,
        isPublished: true,
        dataType: () => descriptionDataType,
        async control(cx, val) {
            await cx.error.if(val).is.empty();
        },
    })
    readonly s1: Promise<string>;

    @decorators.stringProperty<TestValidationPersistent, 's2'>({
        isStored: true,
        isPublished: true,
        dataType: () => descriptionDataType,
        // control can also be a simple function
        // note that `this` and the type of val are correctly set inside the function
        async control(cx, val): Promise<void> {
            await cx.error.if(val).is.not.equal.to(await this.s1);
            await cx.error.if(val).is.matching(/abc/);
        },
    })
    readonly s2: Promise<string>;

    @decorators.stringProperty<TestValidationPersistent, 's3'>({
        isStored: true,
        isPublished: true,
        dataType: () => descriptionDataType,
        async control(cx, val) {
            await cx.error.if(val).is.not.equal.to(await this.s1);
            await cx.error.if(val).is.matching(/abc/);
            await cx.error.if(val).is.matching(/bcd/);
        },
    })
    readonly s3: Promise<string>;

    // typing detects mismatch on value type
    // this is not tested by mocha - you have to modify in vscode and see the result
    @decorators.integerProperty<TestValidationPersistent, 'i1'>({
        isStored: true,
        isPublished: true,
        // integer/string type mismatch is detected if next line is commented out
        // control: validators.hasNoUppercase,
        async control(cx, val): Promise<void> {
            await cx.error.if(val).is.not.greater.than(await this.i2);
            await cx.expect(val).to.be.in([1, 2]);
        },
    })
    readonly i1: Promise<integer>;

    // no control on integer
    @decorators.integerProperty<TestValidationPersistent, 'i2'>({
        isStored: true,
        isPublished: true,
    })
    readonly i2: Promise<integer>;
}

@decorators.node<TestValidationSeverity>({
    storage: 'json',
})
export class TestValidationSeverity extends Node {
    @decorators.stringProperty<TestValidationSeverity, 'value'>({
        dataType: () => descriptionDataType,
        async control(cx, val) {
            await cx.info.if(val).is.not.empty();
            await cx.warn.if(val).is.not.empty();
            await cx.error.if(val).is.not.empty();
            await cx.throw.if(val.length).is.greater.than(1);
        },
    })
    readonly value: Promise<string>;
}

@decorators.node<TestValidationBoolean>({
    storage: 'json',
})
export class TestValidationBoolean extends Node {
    @decorators.booleanProperty<TestValidationBoolean, 'mustBeTrue'>({
        async control(cx, val) {
            await cx.error.if(val).is.not.true();
            await cx.error.if(val).is.false();
        },
    })
    readonly mustBeTrue: Promise<boolean>;

    @decorators.booleanProperty<TestValidationBoolean, 'mustBeFalse'>({
        async control(cx, val): Promise<void> {
            await cx.error.if(val).is.not.false();
            await cx.error.if(val).is.true();
        },
    })
    readonly mustBeFalse: Promise<boolean>;
}

@decorators.node<TestValidationNumber>({
    storage: 'json',
})
export class TestValidationNumber extends Node {
    @decorators.integerProperty<TestValidationNumber, 'mandatory'>({})
    readonly mandatory: Promise<number>;

    @decorators.integerProperty<TestValidationNumber, 'mustBeZero'>({
        async control(cx, val): Promise<void> {
            await cx.error.if(val).is.not.zero();
            await cx.error.if(val).is.positive();
            await cx.error.if(val).is.negative();
            await cx.error.if(val).is.not.equal.to(0);
            await cx.error.if(val).is.not.greater.than(-1);
            await cx.error.if(val).is.not.less.than(1);
        },
    })
    readonly mustBeZero: Promise<number>;

    @decorators.integerProperty<TestValidationNumber, 'mustBePositive'>({
        async control(cx, val): Promise<void> {
            await cx.error.if(val).is.not.positive();
            await cx.error.if(val).is.zero();
            await cx.error.if(val).is.negative();
        },
    })
    readonly mustBePositive: Promise<number>;

    @decorators.integerProperty<TestValidationNumber, 'mustBeNegative'>({
        async control(cx, val): Promise<void> {
            await cx.error.if(val).is.not.negative();
            await cx.error.if(val).is.zero();
            await cx.error.if(val).is.positive();
        },
    })
    readonly mustBeNegative: Promise<number>;

    // The custom message was chosen among the existing xtrem-core i18n messages for more simplicity.
    @decorators.integerProperty<TestValidationNumber, 'mustBeNegativeCustomMessage'>({
        async control(cx, val): Promise<void> {
            await cx.error
                .withMessage('@sage/xtrem-core/invalid-value', 'invalid value: {{type}}, {{value}}', () => {
                    return { type: 'returnedType', value: 'returnedValue' };
                })
                .if(val)
                .is.not.negative();
        },
    })
    readonly mustBeNegativeCustomMessage: Promise<number>;

    // The custom message was chosen among the existing xtrem-core i18n messages for more simplicity.
    @decorators.integerProperty<TestValidationNumber, 'mustBePositiveCustomMessage'>({
        async control(cx, val): Promise<void> {
            await cx
                .expect(val)
                .withMessage('@sage/xtrem-core/property-is-required', 'property is required')
                .to.be.positive();
        },
    })
    readonly mustBePositiveCustomMessage: Promise<number>;

    @decorators.integerProperty<TestValidationNumber, 'mustBeA32BitSignedInteger'>({})
    readonly mustBeA32BitSignedInteger: Promise<number>;
}

@decorators.node<TestValidationString>({
    storage: 'json',
})
export class TestValidationString extends Node {
    @decorators.stringProperty<TestValidationString, 'mandatory'>({
        dataType: () => descriptionDataType,
        isNotEmpty: true,
    })
    readonly mandatory: Promise<string>;

    @decorators.stringProperty<TestValidationString, 'mustBeEmpty'>({
        dataType: () => descriptionDataType,
        async control(cx, val): Promise<void> {
            await cx.error.if(val).is.not.empty();
            await cx.error.if(val).is.not.equal.to('');
            await cx.error.if(val).is.matching(/./);
            await cx.error.if(val).is.not.less.than('A');
            await cx.error.if(val).is.greater.than('');
            await cx.error.if(val).is.greater.than('A');
        },
    })
    readonly mustBeEmpty: Promise<string>;

    @decorators.stringProperty<TestValidationString, 'mustBeA'>({
        dataType: () => descriptionDataType,
        async control(cx, val): Promise<void> {
            await cx.error.if(val).is.empty();
            await cx.error.if(val).is.not.equal.to('A');
            await cx.error.if(val).is.not.matching(/^A$/);
            await cx.error.if(val).is.matching(/[^A]/);
            await cx.error.if(val).is.less.than('A');
            await cx.error.if(val).is.greater.than('A');
        },
    })
    readonly mustBeA: Promise<string>;
}

@decorators.node<TestControlFlow>({
    storage: 'json',
    controlBegin(cx): void {
        cx.info.add('validating before');
    },
    controlEnd(cx): void {
        cx.info.add('validating after');
    },
})
export class TestControlFlow extends Node {
    @decorators.stringProperty<TestControlFlow, 'v1'>({
        dataType: () => descriptionDataType,
        async control(cx, val): Promise<void> {
            await cx.throw.if(val).is.equal.to('throw');
            cx.info.add('validating v1: after throw');
            if (await cx.error.if(val).is.equal.to('error')) return;
            cx.info.add('validating v1: after error');
        },
    })
    readonly v1: Promise<string>;

    @decorators.stringProperty<TestControlFlow, 'v2'>({
        dataType: () => descriptionDataType,
        control(cx): void {
            cx.info.add('validating v2');
        },
    })
    readonly v2: Promise<string>;
}

@decorators.node<TestValidationDate>({
    storage: 'json',
})
export class TestValidationDate extends Node {
    @decorators.dateProperty<TestValidationDate, 'referenceDate'>({
        isNullable: true,
    })
    readonly referenceDate: Promise<date>;

    @decorators.dateProperty<TestValidationDate, 'testDate'>({
        isNullable: true,
        async control(cx, val): Promise<void> {
            if (val) {
                await cx.error.if(val).is.after(await this.referenceDate);
            }
        },
    })
    readonly testDate: Promise<date | null>;
}

@decorators.node<TestValidationReturns>({
    storage: 'json',
    async controlEnd(cx): Promise<void> {
        assert.isTrue(await cx.info.if('').is.empty());
        assert.isFalse(await cx.info.if('').is.not.empty());
        assert.isTrue(await cx.info.if(1).is.equal.to(1));
        assert.isFalse(await cx.info.if(1).is.not.equal.to(1));
        assert.isTrue(await cx.info.if(2).is.greater.than(1));
        assert.isFalse(await cx.info.if(2).is.not.greater.than(1));
        assert.isTrue(await cx.info.if(1).is.less.than(2));
        assert.isFalse(await cx.info.if(1).is.not.less.than(2));
        assert.isTrue(await cx.info.if(2).is.at.least(1));
        assert.isFalse(await cx.info.if(2).is.not.at.least(1));
        assert.isTrue(await cx.info.if(1).is.at.most(2));
        assert.isFalse(await cx.info.if(1).is.not.at.most(2));

        assert.isTrue(await cx.info.if(true).is.true());
        assert.isFalse(await cx.info.if(true).is.not.true());
        assert.isTrue(await cx.info.if(false).is.false());
        assert.isFalse(await cx.info.if(false).is.not.false());

        assert.isTrue(await cx.info.if('a').is.matching(/a/));
        assert.isFalse(await cx.info.if('a').is.not.matching(/a/));

        assert.isTrue(await cx.info.if(0).is.zero());
        assert.isFalse(await cx.info.if(0).is.not.zero());
        assert.isTrue(await cx.info.if(1).is.positive());
        assert.isFalse(await cx.info.if(1).is.not.positive());
        assert.isTrue(await cx.info.if(-1).is.negative());
        assert.isFalse(await cx.info.if(-1).is.not.negative());

        assert.isTrue(await cx.info.if(date.make(2016, 1, 1)).is.before(date.make(2017, 1, 1)));
        assert.isFalse(await cx.info.if(date.make(2016, 1, 1)).is.not.before(date.make(2017, 1, 1)));
        assert.isTrue(await cx.info.if(date.make(2017, 1, 1)).is.after(date.make(2016, 1, 1)));
        assert.isFalse(await cx.info.if(date.make(2017, 1, 1)).is.not.after(date.make(2016, 1, 1)));
    },
})
export class TestValidationReturns extends Node {}
