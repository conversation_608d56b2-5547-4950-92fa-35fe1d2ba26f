import { decorators, Node, Reference } from '../../../index';
import { integer } from '../../../lib';
import { TestReferred } from './referred';

@decorators.node<TestRequiredProperty>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestRequiredProperty',
    indexes: [],
})
export class TestRequiredProperty extends Node {
    @decorators.integerProperty<TestRequiredProperty, 'notRequiredNullable'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly notRequiredNullable: Promise<integer | null>;

    @decorators.integerProperty<TestRequiredProperty, 'notRequiredNullable2'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly notRequiredNullable2: Promise<integer | null>;

    @decorators.integerProperty<TestRequiredProperty, 'notRequiredNotNullable'>({
        isStored: true,
        isPublished: true,
    })
    readonly notRequiredNotNullable: Promise<integer>;

    @decorators.referenceProperty<TestRequiredProperty, 'requiredReference'>({
        isStored: true,
        isPublished: true,
        node: () => TestReferred,
        isRequired: true,
    })
    readonly requiredReference: Reference<TestReferred>;

    @decorators.referenceArrayProperty<TestRequiredProperty, 'requiredReferenceArray'>({
        isStored: true,
        isPublished: true,
        onDelete: 'restrict',
        node: () => TestReferred,
        isRequired: true,
    })
    readonly requiredReferenceArray: Promise<TestReferred[]>;

    @decorators.integerProperty<TestRequiredProperty, 'requiredComputed'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dependsOn: ['notRequiredNullable'],
        async defaultValue() {
            return (await this.notRequiredNullable2) == null ? 10 : null;
        },
    })
    readonly requiredComputed: Promise<integer | null>;
}
