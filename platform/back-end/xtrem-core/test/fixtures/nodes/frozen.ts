import { decorators, Node } from '../../../index';
import { codeDataType, descriptionDataType } from '../data-types/data-types';

@decorators.node<TestFrozen>({
    isPublished: true,
    storage: 'sql',
    async isFrozen() {
        return (await this.code) === 'FROZEN';
    },
    indexes: [{ orderBy: { code: +1 }, isUnique: true }],
})
export class TestF<PERSON>zen extends Node {
    @decorators.stringProperty<TestFrozen, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestFrozen, 'description'>({
        isPublished: true,
        isStored: true,
        async isFrozen() {
            return (await this.description) === 'FROZEN';
        },
        dataType: () => descriptionDataType,
    })
    readonly description: Promise<string>;
}

@decorators.node<TestNotFrozenBase>({
    isPublished: true,
    storage: 'sql',
    isAbstract: true,
    indexes: [{ orderBy: { code: +1 }, isUnique: true }],
})
export class <PERSON>NotF<PERSON>zenBase extends Node {
    @decorators.stringProperty<TestNotFrozenBase, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestNotFrozenBase, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
        defaultValue: 'base',
    })
    readonly description: Promise<string>;
}

@decorators.subNode<TestOverrideNotFrozen>({
    isPublished: true,
    extends: () => TestNotFrozenBase,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
})
export class TestOverrideNotFrozen extends TestNotFrozenBase {
    @decorators.stringProperty<TestOverrideNotFrozen, 'strFromOverrideNotFrozen'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly strFromOverrideNotFrozen: Promise<string>;

    @decorators.stringPropertyOverride<TestOverrideNotFrozen, 'description'>({
        defaultValue: 'notFrozen',
        isFrozen: false,
    })
    override readonly description: Promise<string>;
}

@decorators.subNode<TestOverrideFrozen>({
    isPublished: true,
    extends: () => TestNotFrozenBase,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
})
export class TestOverrideFrozen extends TestNotFrozenBase {
    @decorators.stringProperty<TestOverrideFrozen, 'strFromOverrideFrozen'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly strFromOverrideFrozen: Promise<string>;

    @decorators.stringPropertyOverride<TestOverrideFrozen, 'description'>({
        defaultValue: 'frozen',
        isFrozen: true,
    })
    override readonly description: Promise<string>;
}
