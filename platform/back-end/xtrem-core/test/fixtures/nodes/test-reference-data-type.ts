import { decorators, integer, Node, Reference, ReferenceDataType } from '../../../index';
import { codeDataType, descriptionDataType } from '../data-types/data-types';
import { testReferenceDataType } from '../data-types/reference-data-types';

@decorators.node<TestGrandChildReferenceDataType>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestGrandChildReferenceDataType',
    indexes: [{ orderBy: { grandChildCode: 1 }, isUnique: true, isNaturalKey: true }],
})
export class TestGrandChildReferenceDataType extends Node {
    @decorators.stringProperty<TestGrandChildReferenceDataType, 'grandChildCode'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
        lookupAccess: true,
    })
    readonly grandChildCode: Promise<string>;
}

@decorators.node<TestChildReferenceDataType>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestChildReferenceDataType',
    indexes: [{ orderBy: { childCode: 1 }, isUnique: true, isNaturalKey: true }],
})
export class TestChildReferenceDataType extends Node {
    @decorators.stringProperty<TestChildReferenceDataType, 'childCode'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
        lookupAccess: true,
    })
    readonly childCode: Promise<string>;

    @decorators.referenceProperty<TestChildReferenceDataType, 'grandChild'>({
        isPublished: true,
        isStored: true,
        node: () => TestGrandChildReferenceDataType,
    })
    readonly grandChild: Reference<TestGrandChildReferenceDataType>;
}

@decorators.node<TestReferenceDataType>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestReferenceDataType',
    indexes: [{ orderBy: { code: 1 }, isUnique: true, isNaturalKey: true }],
})
export class TestReferenceDataType extends Node {
    @decorators.stringProperty<TestReferenceDataType, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
        lookupAccess: true,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestReferenceDataType, 'name'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
        lookupAccess: true,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<TestReferenceDataType, 'noLookup'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly noLookup: Promise<string>;

    @decorators.referenceProperty<TestReferenceDataType, 'child'>({
        isPublished: true,
        isStored: true,
        node: () => TestChildReferenceDataType,
    })
    readonly child: Reference<TestChildReferenceDataType>;
}

// Temporary test class for automatically generated reference data types.
@decorators.node<TestNoReferenceDataType>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestNoReferenceDataType',
    indexes: [{ orderBy: { code: 1, ref: 1 }, isUnique: true, isNaturalKey: true }],
})
export class TestNoReferenceDataType extends Node {
    @decorators.stringProperty<TestNoReferenceDataType, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
        lookupAccess: true,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestNoReferenceDataType, 'ref'>({
        isPublished: true,
        isStored: true,
        node: () => TestReferenceDataType,
    })
    readonly ref: Reference<TestReferenceDataType>;
}

@decorators.node<TestReferenceDataTypeParent>({
    tableName: 'TestReferenceDataTypeParent',
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [{ orderBy: { id: 1 }, isUnique: true, isNaturalKey: true }],
})
export class TestReferenceDataTypeParent extends Node {
    @decorators.integerProperty<TestReferenceDataTypeParent, 'id'>({
        isPublished: true,
        isStored: true,
    })
    readonly id: Promise<integer>;

    @decorators.stringProperty<TestReferenceDataTypeParent, 'stringVal'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly stringVal: Promise<string>;

    @decorators.referenceProperty<TestReferenceDataTypeParent, 'referenceDataType'>({
        isPublished: true,
        isStored: true,
        dataType: () => testReferenceDataType,
        node: () => TestReferenceDataType,
    })
    readonly referenceDataType: Reference<TestReferenceDataType>;

    @decorators.referenceProperty<TestReferenceDataTypeParent, 'nullableReferenceDataType'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => testReferenceDataType,
        node: () => TestReferenceDataType,
    })
    readonly nullableReferenceDataType: Reference<TestReferenceDataType | null>;
}

// Self/Cyclic references are not supported
// testReferenceDataTypeCyclic below cannot have 'selfReference' as a path

export const testReferenceDataTypeCyclic = new ReferenceDataType<TestReferenceDataTypeCyclic>({
    reference: () => TestReferenceDataTypeCyclic,
    lookup: {
        valuePath: 'id',
        helperTextPath: 'name',
        columnPaths: ['id', 'name'],
    },
});

@decorators.node<TestReferenceDataTypeCyclic>({
    tableName: 'TestReferenceDataTypeCyclic',
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [{ orderBy: { id: 1 }, isUnique: true }],
})
export class TestReferenceDataTypeCyclic extends Node {
    @decorators.integerProperty<TestReferenceDataTypeCyclic, 'id'>({
        isPublished: true,
        isStored: true,
    })
    readonly id: Promise<integer>;

    @decorators.stringProperty<TestReferenceDataTypeCyclic, 'name'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
        lookupAccess: true,
    })
    readonly name: Promise<string>;

    @decorators.referenceProperty<TestReferenceDataTypeCyclic, 'selfReference'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => testReferenceDataTypeCyclic,
        node: () => TestReferenceDataTypeCyclic,
    })
    readonly selfReference: Reference<TestReferenceDataTypeCyclic | null>;
}
