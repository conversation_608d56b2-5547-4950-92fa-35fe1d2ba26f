import { Collection, decorators, Node, Reference } from '../../../index';
import { codeDataType } from '../data-types/data-types';

@decorators.node<TestReferringValidIsActive>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    tableName: 'TestReferValidIsActive',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestReferringValidIsActive extends Node {
    @decorators.stringProperty<TestReferringValidIsActive, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestReferringValidIsActive, 'reference'>({
        isPublished: true,
        isStored: true,
        node: () => TestValidIsActive,
        isNullable: true,
    })
    readonly reference: Reference<TestValidIsActive | null>;

    @decorators.referenceProperty<TestReferringValidIsActive, 'ignoringReference'>({
        isPublished: true,
        isStored: true,
        node: () => TestValidIsActive,
        isNullable: true,
        ignoreIsActive: true,
    })
    readonly ignoringReference: Reference<TestValidIsActive | null>;
}

@decorators.node<TestValidIsActive>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestValidIsActive',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestValidIsActive extends Node {
    @decorators.stringProperty<TestValidIsActive, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.booleanProperty<TestValidIsActive, 'activeField'>({
        isPublished: true,
        isStored: true,
        provides: ['isActive'],
    })
    readonly activeField: Promise<boolean>;
}

@decorators.node<TestReferringValidIsActiveFunc>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    tableName: 'TestReferringValidIsActiveFunc',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestReferringValidIsActiveFunc extends Node {
    @decorators.stringProperty<TestReferringValidIsActiveFunc, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestReferringValidIsActiveFunc, 'reference'>({
        isPublished: true,
        isStored: true,
        node: () => TestValidIsActive,
        isNullable: true,
    })
    readonly reference: Reference<TestValidIsActive | null>;

    @decorators.referenceProperty<TestReferringValidIsActiveFunc, 'ignoringReference'>({
        isPublished: true,
        isStored: true,
        node: () => TestValidIsActive,
        isNullable: true,
        async ignoreIsActive() {
            return !!((await this.code) === 'testCode');
        },
    })
    readonly ignoringReference: Reference<TestValidIsActive | null>;
}

@decorators.node<TestParentIsActive>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestParentIsActive',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestParentIsActive extends Node {
    @decorators.stringProperty<TestParentIsActive, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.booleanProperty<TestParentIsActive, 'activeField'>({
        isPublished: true,
        isStored: true,
        provides: ['isActive'],
    })
    readonly activeField: Promise<boolean>;

    @decorators.collectionProperty<TestParentIsActive, 'children'>({
        isVital: true,
        isPublished: true,
        reverseReference: 'parent',
        isRequired: true,
        node: () => TestChildIsActive,
    })
    readonly children: Collection<TestChildIsActive>;
}

@decorators.node<TestChildIsActive>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestChildIsActive',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
    isVitalCollectionChild: true,
})
export class TestChildIsActive extends Node {
    @decorators.referenceProperty<TestChildIsActive, 'parent'>({
        isVitalParent: true,
        isStored: true,
        isPublished: true,
        node: () => TestParentIsActive,
    })
    readonly parent: Reference<TestParentIsActive>;

    @decorators.stringProperty<TestChildIsActive, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;
}
