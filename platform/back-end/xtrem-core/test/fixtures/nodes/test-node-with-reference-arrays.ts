import { decorators, Node } from '../../../index';
import { codeDataType } from '../data-types/data-types';
import { TestReferenceForReferenceArrays } from './test-reference-for-reference-arrays';

/**
 * This node is used to test the onDelete behavior on referenceArray properties.
 * It works with the TestReferenceForReferenceArrays node
 */
@decorators.node<TestNodeWithReferenceArrays>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    indexes: [{ orderBy: { code: +1 }, isUnique: true }],
})
export class TestNodeWithReferenceArrays extends Node {
    @decorators.stringProperty<TestNodeWithReferenceArrays, 'code'>({
        isStored: true,
        dataType: () => codeDataType,
        isPublished: true,
    })
    readonly code: Promise<string>;

    @decorators.referenceArrayProperty<TestNodeWithReferenceArrays, 'refsWithRemove'>({
        isPublished: true,
        isStored: true,
        onDelete: 'remove',
        node: () => TestReferenceForReferenceArrays,
    })
    readonly refsWithRemove: Promise<TestReferenceForReferenceArrays[]>;

    @decorators.referenceArrayProperty<TestNodeWithReferenceArrays, 'refsWithRestrict'>({
        isPublished: true,
        isStored: true,
        onDelete: 'restrict',
        node: () => TestReferenceForReferenceArrays,
    })
    readonly refsWithRestrict: Promise<TestReferenceForReferenceArrays[]>;
}
