import { assert } from 'chai';
import * as express from 'express';
import * as fs from 'node:fs';
import * as fsp from 'node:path';
import { URL } from 'node:url';
import * as request from 'supertest';
import { FileStorageManager, Test } from '../../lib';
import * as dataTypes from '../fixtures/data-types/_index';
import * as enums from '../fixtures/enums';
import { createApplicationWithApi, initTables, restoreTables, setup } from '../fixtures/index';
import { TestDownload } from '../fixtures/nodes';

const testApp = express();

function getRelativeUrl(url: URL): string {
    return url.href.replace(url.origin, '');
}

describe('file storage tests', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi({
                nodes: { TestDownload },
                dataTypes,
                enums,
            }),
        });

        await initTables([{ nodeConstructor: TestDownload, data: [] }]);

        testApp.use(Test.fixRequestMiddleWare);
        FileStorageManager.addUnauthenticatedRoutes(testApp, Test.application);
        FileStorageManager.addAuthenticatedRoutes(testApp, Test.application);
    });

    it('Can get download url and and get contents', async () => {
        let downloadUrlPath: string | undefined;
        let testData = '';
        await Test.withCommittedContext(
            async context => {
                const node = await context.create(TestDownload, {
                    code: 'D01',
                    description: 'Test',
                });
                await node.$.save();
                assert.deepEqual(context.diagnoses, []);
                const downloadUrlString = await node.downloadUrl;
                assert.isString(downloadUrlString);
                const downloadUrl = new URL(downloadUrlString);
                const targetParam = downloadUrl.searchParams.get('t');
                assert.isString(targetParam);
                const decrypted64 = await context.vault.decrypt64(targetParam!);
                const target = JSON.parse(decrypted64);
                assert.equal(target.nodeName, 'TestDownload');
                assert.equal(target._id, node._id);

                const nodeKey = await node.key;
                const objectKey = `test-download/${nodeKey}`;
                const filePath = FileStorageManager.getSandboxFilePath(context, objectKey);
                fs.mkdirSync(fsp.dirname(filePath), { recursive: true });
                testData = `test data ${node._id}-${nodeKey}`;
                fs.writeFileSync(filePath, testData);
                const devUploadUrlPath = `/dev/uploads/${Buffer.from(JSON.stringify({ objectKey })).toString('base64')}`;

                await request(testApp).put(devUploadUrlPath).attach('file', filePath).expect(200);
                downloadUrlPath = getRelativeUrl(downloadUrl);
            },
            { config: { email: '<EMAIL>' } },
        );

        assert.isString(downloadUrlPath);
        const redirectResponse = await request(testApp)
            .get(downloadUrlPath ?? '')
            .expect(302);

        assert.isString(redirectResponse.headers.location);
        const locationUrl = getRelativeUrl(new URL(redirectResponse.headers.location));
        const response = await request(testApp).get(locationUrl).expect(200);
        const multiparts = response.body.toString();
        assert.include(multiparts, testData);
    });
    after(async () => {
        await restoreTables();
        await FileStorageManager.clearSandbox({ tenantId: Test.defaultTenantId, application: Test.application });
        // To review: dev upload should always use a sandbox path
        fs.rmSync(fsp.join(Test.application.tmpDir, Test.defaultTenantId, 'test-download'), {
            recursive: true,
        });
    });
});
