import { Decimal } from '@sage/xtrem-decimal';
import { assert } from 'chai';
import { Collection, date, decimal, decorators, integer, Node, Reference, Test, useDefaultValue } from '../../index';
import { codeDataType, defaultDecimalDataType, descriptionDataType } from '../fixtures/data-types/data-types';
import { createApplicationWithApi, initTables, restoreTables, setup } from '../fixtures/index';

@decorators.node<TestDependencyDocument>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestDependencyDocument',
    indexes: [{ orderBy: { id: 1 }, isUnique: true }],
})
class TestDependencyDocument extends Node {
    @decorators.stringProperty<TestDependencyDocument, 'id'>({
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly id: Promise<string>;

    @decorators.stringProperty<TestDependencyDocument, 'description'>({
        isStored: true,
        dataType: () => descriptionDataType,
        dependsOn: ['id'],
        async defaultValue(): Promise<string> {
            return `description of ${await this.id}`;
        },
    })
    readonly description: Promise<string>;

    // would normally be declared after startDate
    // but we check that properties are correctly reordered.
    @decorators.dateProperty<TestDependencyDocument, 'endDate'>({
        isStored: true,
        dependsOn: ['startDate'],
        updatedValue() {
            // use defaultValue()
            return useDefaultValue;
        },
        async defaultValue(): Promise<date> {
            return (await this.startDate).addWeeks(1);
        },
    })
    readonly endDate: Promise<date>;

    @decorators.dateProperty<TestDependencyDocument, 'endDateWithoutUpdatedValue'>({
        isStored: true,
        dependsOn: ['startDate'],
        async defaultValue(): Promise<date> {
            return (await this.startDate).addWeeks(1);
        },
    })
    readonly endDateWithoutUpdatedValue: Promise<date>;

    // This property tests that updatedValue event is properly invoked
    @decorators.dateProperty<TestDependencyDocument, 'endDateWithUpdate'>({
        isStored: true,
        dependsOn: ['startDate'],
        async updatedValue(): Promise<date> {
            return (await this.endDateWithUpdate) < (await this.startDate) ? this.startDate : this.endDateWithUpdate;
        },
        async defaultValue(): Promise<date> {
            return (await this.startDate).addWeeks(1);
        },
    })
    readonly endDateWithUpdate: Promise<date>;

    // This property tests that updates are properly cascaded
    // If startDate is updated, endDate should be updated, and then afterEndDate too.
    @decorators.dateProperty<TestDependencyDocument, 'afterEndDate'>({
        isStored: true,
        dependsOn: ['endDate'],
        async updatedValue(): Promise<date> {
            return (await this.endDate).addMonths(2);
        },
        async defaultValue(): Promise<date> {
            return (await this.endDate).addMonths(1);
        },
    })
    readonly afterEndDate: Promise<date>;

    @decorators.dateProperty<TestDependencyDocument, 'startDate'>({
        isStored: true,
        defaultValue(): date {
            return date.make(2019, 5, 20);
        },
    })
    readonly startDate: Promise<date>;

    @decorators.decimalProperty<TestDependencyDocument, 'total'>({
        isStored: true,
        dependsOn: [{ lines: ['amount'] }],
        dataType: () => defaultDecimalDataType,
        updatedValue() {
            // use defaultValue()
            return useDefaultValue;
        },
        defaultValue() {
            // This example is not preprocessed for decimals so we have to explictly convert line.amount to number
            return this.lines.reduce(async (total, line) => total + +(await line.amount), 0.0);
        },
    })
    readonly total: Promise<decimal>;

    @decorators.collectionProperty<TestDependencyDocument, 'lines'>({
        node: () => TestDependencyLine,
        reverseReference: 'document',
        isVital: true,
    })
    readonly lines: Collection<TestDependencyLine>;
}

@decorators.node<TestDependencyLine>({
    storage: 'sql',
    isVitalCollectionChild: true,
    tableName: 'TestDependencyLine',
    indexes: [{ orderBy: { document: 1, lineNumber: 1 }, isUnique: true }],
})
class TestDependencyLine extends Node {
    @decorators.referenceProperty<TestDependencyLine, 'document'>({
        isStored: true,
        isVitalParent: true,
        node: () => TestDependencyDocument,
    })
    readonly document: Reference<TestDependencyDocument>;

    @decorators.integerProperty<TestDependencyLine, 'lineNumber'>({
        isStored: true,
        defaultValue(): integer {
            return 0;
        },
    })
    readonly lineNumber: Promise<integer>;

    @decorators.stringProperty<TestDependencyLine, 'text'>({
        isStored: true,
        dataType: () => descriptionDataType,
        dependsOn: [{ document: ['description'] }, 'lineNumber'],
        updatedValue() {
            // use defaultValue()
            return useDefaultValue;
        },
        async defaultValue(): Promise<string> {
            return `${await (await this.document).description}: line ${await this.lineNumber}`;
        },
    })
    readonly text: Promise<string>;

    @decorators.decimalProperty<TestDependencyLine, 'amount'>({
        isStored: true,
        dataType: () => defaultDecimalDataType,
        async defaultValue(): Promise<decimal> {
            return (await this.lineNumber) * 10;
        },
    })
    readonly amount: Promise<decimal>;
}

@decorators.node<TestOutOfOrderDependency>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestOutOfOrderDependency',
    indexes: [{ orderBy: { id: 1 }, isUnique: true }],
})
class TestOutOfOrderDependency extends Node {
    @decorators.stringProperty<TestOutOfOrderDependency, 'id'>({
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly id: Promise<string>;

    @decorators.integerProperty<TestOutOfOrderDependency, 'p1'>({
        isStored: true,
        async defaultValue(): Promise<integer> {
            // out of order !!
            return 2 * (await this.p3);
        },
    })
    readonly p1: Promise<integer>;

    @decorators.integerProperty<TestOutOfOrderDependency, 'p2'>({
        isStored: true,
        dependsOn: ['p1'],
        defaultValue(): integer {
            return 10;
        },
        async updatedValue(): Promise<integer> {
            // out of order !!
            return 3 * (await this.p3);
        },
    })
    readonly p2: Promise<integer>;

    @decorators.integerProperty<TestOutOfOrderDependency, 'p3'>({
        isStored: true,
        dependsOn: ['p1'],
        defaultValue(): integer {
            return 20;
        },
    })
    readonly p3: Promise<integer>;
}

describe('Property dependencies', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi({
                nodes: { TestOutOfOrderDependency, TestDependencyDocument, TestDependencyLine },
            }),
        });
        await initTables([
            { nodeConstructor: TestOutOfOrderDependency, data: [] },
            { nodeConstructor: TestDependencyDocument, data: [] },
            { nodeConstructor: TestDependencyLine, data: [] },
        ]);
    });

    it('calls defaultValue rules in the right order', () =>
        Test.withContext(async context => {
            const document = await context.create(TestDependencyDocument, {
                id: 'DOC1',
                lines: [
                    { lineNumber: 10, amount: 5 },
                    { lineNumber: 20, amount: 7 },
                ],
            });
            // 'line.text' property should have been set by the defaultValue() function
            assert.deepEqual(await document.$.payload(), {
                id: 'DOC1',
                description: 'description of DOC1',
                startDate: date.make(2019, 5, 20),
                endDate: date.make(2019, 5, 27),
                endDateWithoutUpdatedValue: date.make(2019, 5, 27),
                endDateWithUpdate: date.make(2019, 5, 27),
                afterEndDate: date.make(2019, 6, 27),
                lines: [
                    {
                        _sortValue: 10,
                        document: {}, // payload() does not expose _ids
                        lineNumber: 10,
                        text: 'description of DOC1: line 10',
                        amount: new Decimal(5) as unknown as number,
                        _customData: {},
                    },
                    {
                        _sortValue: 20,
                        document: {}, // payload() does not expose _ids
                        lineNumber: 20,
                        text: 'description of DOC1: line 20',
                        amount: new Decimal(7) as unknown as number,
                        _customData: {},
                    },
                ],
                total: new Decimal(12) as unknown as number,
                _customData: {},
            });
        }));

    it('propagates to other property of same node', () =>
        Test.withContext(async context => {
            const document = await context.create(TestDependencyDocument, {
                id: 'DOC1',
                lines: [
                    { lineNumber: 10, amount: 5 },
                    { lineNumber: 20, amount: 7 },
                ],
            });
            assert.deepEqual(await document.startDate, date.make(2019, 5, 20), 'startDate: defaultValue');
            assert.deepEqual(await document.endDate, date.make(2019, 5, 27), 'endDate: defaultValue');
            assert.deepEqual(
                await document.endDateWithoutUpdatedValue,
                date.make(2019, 5, 27),
                'endDateWithoutUpdatedValue: defaultValue',
            );
            assert.deepEqual(await document.afterEndDate, date.make(2019, 6, 27), 'afterEndDate: defaultValue');

            // Modify startDate
            await document.$.set({ startDate: date.make(2019, 8, 15) });
            assert.deepEqual(
                await document.endDate,
                date.make(2019, 8, 22),
                'endDate: new defaultValue after startDate change',
            );
            assert.deepEqual(
                await document.endDateWithoutUpdatedValue,
                date.make(2019, 5, 27),
                'endDateWithoutUpdatedValue: defaultValue',
            );
            assert.deepEqual(
                await document.endDateWithUpdate,
                date.make(2019, 8, 15),
                'endDataWithUpdate: updatedValue after startDate change',
            );
            assert.deepEqual(
                await document.afterEndDate,
                date.make(2019, 10, 22),
                'afterEndDate: updatedValue after startDate change',
            );
        }));

    it('propagates from header to line', () =>
        Test.withContext(async context => {
            const document = await context.create(TestDependencyDocument, {
                id: 'DOC1',
                description: 'document 1',
                lines: [
                    { lineNumber: 10, amount: 5 },
                    { lineNumber: 20, amount: 7 },
                ],
            });
            assert.deepEqual(await document.description, 'document 1');
            assert.deepEqual(
                await (
                    await document.lines.elementAt(0)
                ).text,
                'document 1: line 10',
                'line 1 text: defaultValue',
            );
            assert.deepEqual(
                await (
                    await document.lines.elementAt(1)
                ).text,
                'document 1: line 20',
                'line 2 text: defaultValue',
            );
            await document.$.set({ description: 'updated 1' });
            assert.deepEqual(
                await (
                    await document.lines.elementAt(0)
                ).text,
                'updated 1: line 10',
                'line 1 text: new defaultValue after document.description change',
            );
            assert.deepEqual(
                await (
                    await document.lines.elementAt(1)
                ).text,
                'updated 1: line 20',
                'line 2 text: new defaultValue after document.description change',
            );
            // also test that multiple dependencies works
            await (await document.lines.elementAt(1)).$.set({ lineNumber: 30 });
            assert.deepEqual(
                await (
                    await document.lines.elementAt(1)
                ).text,
                'updated 1: line 30',
                'line 2 text: new defaultValue after line 2 lineNumber change',
            );
        }));

    it('propagates from line to header', () =>
        Test.withContext(async context => {
            const document = await context.create(TestDependencyDocument, {
                id: 'DOC1',
                lines: [
                    { lineNumber: 10, amount: 5 },
                    { lineNumber: 20, amount: 7 },
                ],
            });
            const toNumber = (v: decimal) => (v as any as Decimal).toNumber();
            assert.deepEqual(toNumber(await document.total), 12, 'total: defaultValue');
            await (await document.lines.elementAt(0)).$.set({ amount: 8 });
            assert.deepEqual(toNumber(await document.total), 15, 'total: new defaultValue after line 1 change');
            await (await document.lines.elementAt(1)).$.set({ amount: 10 });
            assert.deepEqual(toNumber(await document.total), 18, 'total: new defaultValue after line 2 change');
        }));

    it('propagates to header when lines are inserted, updated or deleted', () =>
        Test.withContext(async context => {
            const document = await context.create(TestDependencyDocument, {
                id: 'DOC1',
                description: 'document 1',
                lines: [
                    { lineNumber: 10, amount: 5 },
                    { lineNumber: 20, amount: 7 },
                ],
            });
            await document.$.save();
            assert.equal(await document.total, 12);

            const lineIds = await document.lines.map(line => line._id).toArray();

            // delete one line
            await document.$.set({ lines: [{ _action: 'delete', _id: lineIds[1] }] });
            await document.$.save();
            assert.equal(await document.total, 5);

            // add one line
            await document.$.set({ lines: [{ _action: 'create', lineNumber: 30, amount: 1 }] });
            await document.$.save();
            assert.equal(await document.total, 6);

            // Update one line
            await document.$.set({ lines: [{ _action: 'update', _id: lineIds[0], amount: 15 }] });
            await document.$.save();
            assert.equal(await document.total, 16);

            // Replace all lines
            await document.$.set({
                lines: [
                    { lineNumber: 10, amount: 10 },
                    { lineNumber: 20, amount: 20 },
                    { lineNumber: 30, amount: 30 },
                ],
            });
            await document.$.save();
            assert.equal(await document.total, 60);

            // Delete all lines
            await document.$.set({ lines: [] });
            await document.$.save();
            assert.equal(await document.total, 0);
        }));

    it('throws if properties are initialized/updated out of order', () =>
        Test.uncommitted(async context => {
            let node: TestOutOfOrderDependency;

            // no problem if all properties are initialized
            node = await context.create(TestOutOfOrderDependency, { id: 'A', p1: 1, p2: 2, p3: 3 });
            assert.equal(await node.p1, 1);
            assert.equal(await node.p2, 2);
            assert.equal(await node.p3, 3);

            // throws if p1 not initialized
            await assert.isRejected(
                context.create(TestOutOfOrderDependency, { id: 'B' }),
                "TestOutOfOrderDependency.p3: property cannot be accessed while executing rule for TestOutOfOrderDependency.p1. Please check your 'dependsOn' decorators.",
            );

            // ok if p1 is initialized but not p2
            node = await context.create(TestOutOfOrderDependency, { id: 'C', p1: 1 });
            assert.equal(await node.p1, 1);
            assert.equal(await node.p2, 10);
            assert.equal(await node.p3, 20);
            // invalidates p2 and p3
            await node.$.set({ p1: (await node.p1) + 1 });
            // reading p2 will trigger updatedValue which will trigger read on p3
            // reading p3 will fail out of order
            await assert.isRejected(
                node.p2,
                'TestOutOfOrderDependency.p3: property cannot be accessed while executing rule for TestOutOfOrderDependency.p2',
            );
        }));

    it('can trigger updatedValue when update collection', () =>
        Test.withContext(async context => {
            const document = await context.create(TestDependencyDocument, {
                id: 'DOC1',
                lines: [
                    { lineNumber: 10, amount: 5 },
                    { lineNumber: 20, amount: 7 },
                ],
            });
            await document.$.save();
            const createdLines = await document.lines.toArray();
            const docLineId1 = createdLines[0]._id;
            const docLineId2 = createdLines[1]._id;
            // update existing line
            const updateLineRead = await context.read(TestDependencyDocument, { id: 'DOC1' }, { forUpdate: true });
            assert.equal(await updateLineRead.total, 12);
            await updateLineRead.$.set({
                lines: [
                    {
                        _id: docLineId1,
                        lineNumber: 10,
                        amount: 15,
                    },
                    { _id: docLineId2, lineNumber: 20, amount: 7 },
                ],
            });
            await updateLineRead.$.save();

            // insert new line
            const addNewLineResult = await context.read(TestDependencyDocument, { id: 'DOC1' }, { forUpdate: true });
            assert.equal(await addNewLineResult.total, 22);
            await addNewLineResult.$.set({
                lines: [
                    {
                        lineNumber: 30,
                        amount: 10,
                    },
                    {
                        _id: docLineId1,
                        lineNumber: 10,
                        amount: 15,
                    },
                    { _id: docLineId2, lineNumber: 20, amount: 7 },
                ],
            });
            await addNewLineResult.$.save();
            const readAddNewLineResult = await context.read(
                TestDependencyDocument,
                { id: 'DOC1' },
                { forUpdate: true },
            );
            assert.equal(await readAddNewLineResult.total, 32);
        }));

    after(() => restoreTables());
});
