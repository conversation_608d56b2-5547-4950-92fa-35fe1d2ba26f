import { DateValue } from '@sage/xtrem-date-time';
import { Decimal } from '@sage/xtrem-decimal';
import { assert } from 'chai';
import { testAggregationApplication } from '..';
import { Test } from '../../lib';
import * as fixtures from '../fixtures';
import { aggDocumentData, aggDocumentLineData } from '../fixtures/data';
import { initTables, restoreTables, setup } from '../fixtures/index';

describe('Aggregate queries', () => {
    before(async () => setup({ application: await testAggregationApplication.application }));
    beforeEach(() =>
        initTables([
            { nodeConstructor: fixtures.nodes.TestAggDocument, data: aggDocumentData },
            { nodeConstructor: fixtures.nodes.TestAggDocumentLine, data: aggDocumentLineData },
        ]),
    );

    it('can query an aggregate, grouped by year', () =>
        Test.withContext(async context => {
            const lines = await context
                .queryAggregate(fixtures.nodes.TestAggDocumentLine, {
                    group: { date: { _by: 'year' } },
                    values: { amount1: { sum: true } },
                })
                .toArray();

            assert.deepEqual(
                lines,
                [
                    {
                        group: { date: '2000-01-01' },
                        values: { amount1: { sum: 35.5 } },
                    },
                    {
                        group: { date: '2003-01-01' },
                        values: { amount1: { sum: 23 } },
                    },
                    {
                        group: { date: '2004-01-01' },
                        values: { amount1: { sum: 22 } },
                    },
                ].map(item => ({
                    group: { date: DateValue.parse(item.group.date) },
                    values: { amount1: { sum: Decimal.make(item.values.amount1.sum) as any as number } },
                })),
            );
        }));

    it('can read an aggregate', () =>
        Test.withContext(async context => {
            const result = await context.readAggregate(fixtures.nodes.TestAggDocumentLine, {
                values: { amount1: { sum: true } },
            });

            assert.deepEqual(result, {
                amount1: { sum: Decimal.make(80.5) as any as number },
            });
        }));

    it('can sum with collection.where().sum(...)', () =>
        Test.withContext(async context => {
            const document = await context.read(fixtures.nodes.TestAggDocument, { code: 'DOC1' });
            const result = await document.lines.where().sum(line => line.amount1);
            assert.equal(result, 35.5);
        }));

    it('can sum with collection.where(...).sum(...)', () =>
        Test.withContext(async context => {
            const document = await context.read(fixtures.nodes.TestAggDocument, { code: 'DOC1' });
            const result = await document.lines
                .where(async line => (await line.date).value === DateValue.parse('2000-02-15').value)
                .sum(line => line.amount1);
            assert.equal(result, 15.5);
        }));

    it('can fetch value with collection.takeOne(...)?.prop', () =>
        Test.withContext(async context => {
            const document = await context.read(fixtures.nodes.TestAggDocument, { code: 'DOC1' });
            const result = await (await document.lines.takeOne(async line => (await line.lineNumber) === 3))?.amount2;
            assert.equal(result, 2.9);
        }));

    it('can fetch value with collection.takeOne(...)', () =>
        Test.withContext(async context => {
            const document = await context.read(fixtures.nodes.TestAggDocument, { code: 'DOC1' });
            const result = await document.lines.takeOne(async line => (await line.lineNumber) === 3);
            assert.equal(result?._id, 3);
        }));

    after(() => restoreTables());
});
