import { testBasicDocumentApplication } from '..';
import { assertIsRejectedWithDiagnoses, Test } from '../../index';
import * as fixtures from '../fixtures';
import { initTables, restoreTables, setup } from '../fixtures/index';

const referredData = [
    {
        _id: 1,
        code: 'REF1',
        details: 'reference 1',
    },
    {
        _id: 2,
        code: 'REF2',
        details: 'reference 2',
        integerVal: 2,
    },
];

const multiUniqueIndexesData = [
    {
        _id: 1,
        code: 'REF1',
        alt1: 'ALT1_1',
        alt2: 'ALT2_1',
        details: 'reference 1',
    },
    {
        _id: 2,
        code: 'REF2',
        alt1: 'ALT1_2',
        alt2: 'ALT2_2',
        details: 'reference 2',
    },
];

describe('Database errors', () => {
    before(async () => {
        await setup({ application: await testBasicDocumentApplication.application });
        await initTables([
            { nodeConstructor: fixtures.nodes.TestReferred, data: referredData },
            { nodeConstructor: fixtures.nodes.TestMultiUniqueIndexes, data: multiUniqueIndexesData },
        ]);
    });
    it('cannot violate unique index', async () => {
        await assertIsRejectedWithDiagnoses(
            Test.withUncommittedContext(async context => {
                const referredNode = await context.create(fixtures.nodes.TestReferred, {
                    ...referredData[0],
                    _id: undefined,
                });
                await referredNode.$.save();
                const referredNode1 = await context.create(fixtures.nodes.TestReferred, {
                    ...referredData[0],
                    _id: undefined,
                });
                await referredNode1.$.save();
            }),
            {
                message: 'The record was not saved.',
                diagnoses: [
                    {
                        message: 'The operation failed because the record already exists.',
                        severity: 4,
                        path: ['code'],
                    },
                ],
            },
        );
    });

    it('cannot violate unique index on multi key', () =>
        assertIsRejectedWithDiagnoses(
            Test.withUncommittedContext(async context => {
                const data = {
                    code: 'REF3',
                    alt1: 'ALT1_1',
                    alt2: 'ALT2_1',
                    details: 'reference 3',
                };
                const node = await context.create(fixtures.nodes.TestMultiUniqueIndexes, {
                    ...data,
                    _id: undefined,
                });
                await node.$.save();
                throw new Error('should fail on save');
            }),
            {
                message: 'The record was not saved.',
                diagnoses: [
                    {
                        message:
                            'The operation failed because the record already exists. Conflict found on alt1, alt2.',
                        path: [],
                        severity: 4,
                    },
                ],
            },
        ));

    after(() => restoreTables());
});
