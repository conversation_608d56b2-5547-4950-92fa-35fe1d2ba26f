import { ValidationSeverity } from '@sage/xtrem-shared';
import { assert } from 'chai';
import { assertIsRejectedWithContextDiagnoses, Collection, decorators, Node, Reference, Test } from '../../index';
import { codeDataType } from '../fixtures/data-types/data-types';
import { createApplicationWithApi, initTables, restoreTables, setup } from '../fixtures/index';

@decorators.node<TestDeleteGrandParent>({
    tableName: 'TestDeleteGrandParent',
    storage: 'sql',
    canDeleteMany: true,
    indexes: [
        {
            orderBy: {
                name: 1,
            },
            isUnique: true,
        },
    ],
})
class TestDeleteGrandParent extends Node {
    @decorators.stringProperty<TestDeleteGrandParent, 'name'>({
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly name: Promise<string>;

    @decorators.collectionProperty<TestDeleteGrandParent, 'parents'>({
        isVital: true,
        node: () => TestDeleteParent,
        reverseReference: 'grandParent',
    })
    readonly parents: Collection<TestDeleteParent>;
}

@decorators.node<TestDeleteParent>({
    tableName: 'TestDeleteParent',
    storage: 'sql',
    indexes: [
        {
            orderBy: {
                name: 1,
            },
            isUnique: true,
        },
    ],
    isVitalCollectionChild: true,
})
class TestDeleteParent extends Node {
    @decorators.stringProperty<TestDeleteParent, 'name'>({
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly name: Promise<string>;

    @decorators.collectionProperty<TestDeleteParent, 'children'>({
        isVital: true,
        node: () => TestDeleteChild,
        reverseReference: 'parent',
    })
    readonly children: Collection<TestDeleteChild>;

    @decorators.referenceProperty<TestDeleteParent, 'grandParent'>({
        isStored: true,
        node: () => TestDeleteGrandParent,
        isVitalParent: true,
    })
    readonly grandParent: Reference<TestDeleteGrandParent>;
}

@decorators.node<TestDeleteChild>({
    tableName: 'TestDeleteChild',
    storage: 'sql',
    isVitalCollectionChild: true,
    indexes: [
        {
            orderBy: {
                name: 1,
            },
            isUnique: true,
        },
    ],
    async controlDelete(cx): Promise<void> {
        if ((await this.name).includes('DO_NOT_DELETE')) {
            cx.addDiagnose(ValidationSeverity.error, `${await this.name} MUST NOT BE DELETED`);
        }

        await cx.error
            .withMessage('@sage/xtrem-core/must-not-be-deleted', '{{value}} MUST NOT BE DELETED', async () => {
                return { value: await this.name };
            })
            .if((await this.name).includes('DONOTDELETE'))
            .is.true();
    },
})
class TestDeleteChild extends Node {
    @decorators.stringProperty<TestDeleteChild, 'name'>({
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly name: Promise<string>;

    @decorators.referenceProperty<TestDeleteChild, 'parent'>({
        isStored: true,
        node: () => TestDeleteParent,
        isVitalParent: true,
    })
    readonly parent: Reference<TestDeleteParent>;
}

describe('controlDelete', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi({
                nodes: {
                    TestDeleteGrandParent,
                    TestDeleteParent,
                    TestDeleteChild,
                },
            }),
        });
        await initTables([
            {
                nodeConstructor: TestDeleteGrandParent,
                data: [
                    { _id: 1, name: 'GRANDPARENT1' },
                    { _id: 2, name: 'GRANDPARENT2' },
                    { _id: 3, name: 'GRANDPARENT3' },
                ],
            },
            {
                nodeConstructor: TestDeleteParent,
                data: [
                    { _id: 1, name: 'PARENT1', grandParent: 1, value: 'AAA' },
                    { _id: 2, name: 'PARENT2', grandParent: 1, value: 'AAA' },
                    { _id: 3, name: 'PARENT3', grandParent: 1, value: 'AAA' },
                    { _id: 4, name: 'PARENT4', grandParent: 2, value: 'AAA' },
                    { _id: 5, name: 'PARENT5', grandParent: 3, value: 'AAA' },
                ],
            },
            {
                nodeConstructor: TestDeleteChild,
                data: [
                    { name: 'CHILD1', parent: 1, value: 'CCC' },
                    { name: 'CHILD2', parent: 1, value: 'CCC' },
                    { name: 'CHILD3_DONOTDELETE', parent: 4, value: 'CCC' },
                    { name: 'CHILD4_DO_NOT_DELETE', parent: 5, value: 'CCC' },
                ],
            },
        ]);
    });

    after(() => restoreTables());

    it('can delete child', () =>
        Test.uncommitted(async context => {
            const child = await context.read(TestDeleteChild, { name: 'CHILD1' }, { forUpdate: true });
            assert.isTrue(await child.$.tryDelete());
        }));

    it('cannot delete child', () =>
        Test.uncommitted(async context => {
            const child = await context.read(TestDeleteChild, { name: 'CHILD4_DO_NOT_DELETE' }, { forUpdate: true });
            const expected = {
                message: 'The record was not deleted.',
                diagnoses: [
                    {
                        path: [],
                        severity: ValidationSeverity.error,
                        message: 'CHILD4_DO_NOT_DELETE MUST NOT BE DELETED',
                    },
                ],
            };

            assert.isFalse(await child.$.tryDelete());
            assert.equal(child.$.context.severity, ValidationSeverity.error);
            await assertIsRejectedWithContextDiagnoses(child.$.delete(), context, expected);
        }));

    it('can delete parent', () =>
        Test.uncommitted(async context => {
            const parent = await context.read(TestDeleteParent, { name: 'PARENT1' }, { forUpdate: true });
            assert.isTrue(await parent.$.tryDelete());
        }));

    it('cannot delete parent', () =>
        Test.uncommitted(async context => {
            const parent = await context.read(TestDeleteParent, { name: 'PARENT4' }, { forUpdate: true });
            const expected = {
                message: 'The record was not deleted.',
                diagnoses: [
                    {
                        path: ['children', '3'],
                        severity: ValidationSeverity.error,
                        message: 'CHILD3_DONOTDELETE MUST NOT BE DELETED',
                    },
                ],
            };

            assert.isFalse(await parent.$.tryDelete());
            assert.equal(parent.$.context.severity, ValidationSeverity.error);
            await assertIsRejectedWithContextDiagnoses(parent.$.delete(), context, expected);
        }));

    it('can delete grandparent', () =>
        Test.uncommitted(async context => {
            const parent = await context.read(TestDeleteGrandParent, { name: 'GRANDPARENT1' }, { forUpdate: true });
            assert.isTrue(await parent.$.tryDelete());
        }));

    it('cannot delete grandparent', () =>
        Test.uncommitted(async context => {
            const grandparent = await context.read(
                TestDeleteGrandParent,
                { name: 'GRANDPARENT2' },
                { forUpdate: true },
            );
            const expected = {
                message: 'The record was not deleted.',
                diagnoses: [
                    {
                        path: ['parents', 'children', '3'],
                        severity: ValidationSeverity.error,
                        message: 'CHILD3_DONOTDELETE MUST NOT BE DELETED',
                    },
                ],
            };

            assert.isFalse(await grandparent.$.tryDelete());
            assert.equal(grandparent.$.context.severity, ValidationSeverity.error);

            await assertIsRejectedWithContextDiagnoses(grandparent.$.delete(), context, expected);
        }));
});
