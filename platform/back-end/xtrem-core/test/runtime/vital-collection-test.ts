import { asyncArray } from '@sage/xtrem-async-helper';
import { assert } from 'chai';
import { pick } from 'lodash';
import { testVitalCollectionApplication } from '..';
import { Test } from '../../index';
import { Context, integer, NodeCreateData } from '../../lib';
import { initTables, restoreTables, setup } from '../fixtures/index';
import { TestVitalCollectionChild, TestVitalCollectionParent, TestVitalCollectionSubChild } from '../fixtures/nodes';

interface TestVitalNodeId {
    id: number;
    children?: TestVitalNodeId[];
}

async function createTestNode(len: integer): Promise<number[]> {
    return (await createTestNodeInternal(len)) as number[];
}
async function createTestNodeWithChildren(len: integer, subLen: integer): Promise<TestVitalNodeId> {
    return (await createTestNodeInternal(len, subLen)) as unknown as TestVitalNodeId;
}

function createTestNodeInternal(
    len: integer,
    subLen?: integer,
): Promise<number[] | { id: number; children: { id: number; children: { id: number }[] }[] }> {
    return Test.committed(async context => {
        const children = [...Array(len).keys()].map(i => ({
            code: `CHILD-${i + 1}`,
        }));
        if (subLen! > 0) {
            children.forEach((c: NodeCreateData<TestVitalCollectionChild>, i) => {
                c.children = [...Array(subLen).keys()].map(j => ({ code: `SUBCHILD-${i * len + j + 1}` }));
            });
        }
        const parent = await context.create(TestVitalCollectionParent, {
            code: 'PARENT-1',
            children,
        });
        await parent.$.save();
        if (subLen! > 0) {
            return {
                id: parent._id,
                children: await parent.children
                    .map(async child => ({
                        id: child._id,
                        children: await child.children.map(c => ({ id: c._id })).toArray(),
                    }))
                    .toArray(),
            };
        }
        return parent.children.map(child => child._id).toArray();
    });
}

function checkTestNode(children: { _sortValue: number; code?: string; _id?: integer }[]): Promise<void> {
    return Test.readonly(async context => {
        const parent = await (
            await context.read(TestVitalCollectionParent, { code: 'PARENT-1' })
        ).$.payload({ withIds: true });
        const gotChildren = parent.children!;
        assert.equal(gotChildren.length, children.length);
        assert.deepEqual(
            gotChildren.map((child, i) => pick(child, Object.keys(children[i]))),
            children,
        );
    });
}

function readParentForUpdate(context: Context, code: string): Promise<TestVitalCollectionParent> {
    return context.read(TestVitalCollectionParent, { code }, { forUpdate: true });
}

describe('Vital collection', () => {
    before(async () => {
        await setup({ application: await testVitalCollectionApplication.application });
        await initTables([
            { nodeConstructor: TestVitalCollectionParent, data: [] },
            { nodeConstructor: TestVitalCollectionChild, data: [] },
            { nodeConstructor: TestVitalCollectionSubChild, data: [] },
        ]);
    });

    beforeEach(() => Test.committed(context => context.deleteMany(TestVitalCollectionParent, {})));

    it('added unique index to child table', () =>
        Test.readonly(context => {
            const desc = Test.application
                .getFactoryByConstructor(TestVitalCollectionChild)
                .table.getTableDefinition(context);
            assert.deepInclude(desc.indexes!, {
                name: 'test_vital_collection_child_ind1',
                isUnique: true,
                columns: [
                    { name: '_tenant_id', ascending: true },
                    { name: 'parent', ascending: true },
                    {
                        name: '_sort_value',
                        ascending: true,
                    },
                ],
            });
        }));

    it('can create', async () => {
        await createTestNode(3);
        await checkTestNode([
            { _sortValue: 10, code: 'CHILD-1' },
            { _sortValue: 20, code: 'CHILD-2' },
            { _sortValue: 30, code: 'CHILD-3' },
        ]);
    });

    it('can delete', async () => {
        await createTestNode(3);
        await Test.committed(async context => (await readParentForUpdate(context, 'PARENT-1')).$.delete());
        await Test.readonly(async context => {
            assert.isNull(await context.tryRead(TestVitalCollectionParent, { code: 'PARENT-1' }));
            assert.isNull(await context.tryRead(TestVitalCollectionChild, { code: 'CHILD-1' }));
            assert.isNull(await context.tryRead(TestVitalCollectionChild, { code: 'CHILD-2' }));
            assert.isNull(await context.tryRead(TestVitalCollectionChild, { code: 'CHILD-3' }));
        });
    });

    it('can delete a node with line / sub-lines', async () => {
        await createTestNodeWithChildren(3, 3);

        await Test.committed(async context => (await readParentForUpdate(context, 'PARENT-1')).$.delete());

        await Test.readonly(async context => {
            assert.isNull(await context.tryRead(TestVitalCollectionParent, { code: 'PARENT-1' }));
            assert.isNull(await context.tryRead(TestVitalCollectionChild, { code: 'CHILD-1' }));
            assert.isNull(await context.tryRead(TestVitalCollectionChild, { code: 'CHILD-2' }));
            assert.isNull(await context.tryRead(TestVitalCollectionChild, { code: 'CHILD-3' }));
        });
    });

    it('can update with new ids', async () => {
        const ids = await createTestNode(3);
        const lastId = ids[ids.length - 1];

        await Test.committed(async context => {
            const parent = await readParentForUpdate(context, 'PARENT-1');
            await parent.$.set({
                children: [{ code: 'CHILD-A' }, { code: 'CHILD-B' }, { code: 'CHILD-C' }, { code: 'CHILD-D' }],
            });
            await parent.$.save();
        });
        await checkTestNode([
            { _sortValue: 10, code: 'CHILD-A', _id: lastId + 1 },
            { _sortValue: 20, code: 'CHILD-B', _id: lastId + 2 },
            { _sortValue: 30, code: 'CHILD-C', _id: lastId + 3 },
            { _sortValue: 40, code: 'CHILD-D', _id: lastId + 4 },
        ]);
    });

    it('can delete a line', async () => {
        const ids = await createTestNode(3);
        await Test.committed(async context => {
            const parent = await readParentForUpdate(context, 'PARENT-1');
            await parent.$.set({
                children: [{ _id: ids[1], _action: 'delete' }],
            });
            await parent.$.save();
        });
        await checkTestNode([
            { _sortValue: 10, code: 'CHILD-1', _id: ids[0] },
            // { _sortValue: 20, code: 'CHILD-2', _id: ids[1] },
            { _sortValue: 30, code: 'CHILD-3', _id: ids[2] },
        ]);
    });

    it('can delete a line with sub-lines', async () => {
        const nodeId = await createTestNodeWithChildren(3, 3);
        const ids = nodeId.children || [];
        await Test.committed(async context => {
            const parent = await readParentForUpdate(context, 'PARENT-1');
            await parent.$.set({
                children: [{ _id: ids[0].id, _action: 'delete' }],
            });
            await parent.$.save();
        });
        await checkTestNode([
            // { _sortValue: 10, code: 'CHILD-1', _id: ids[0]._id },
            { _sortValue: 20, code: 'CHILD-2', _id: ids[1].id },
            { _sortValue: 30, code: 'CHILD-3', _id: ids[2].id },
        ]);
    });

    it('can update with existing ids and new child at end', async () => {
        const ids = await createTestNode(3);
        const lastId = ids[ids.length - 1];

        await Test.committed(async context => {
            const parent = await readParentForUpdate(context, 'PARENT-1');
            await parent.$.set({
                children: [
                    { code: 'CHILD-A', _id: ids[0] },
                    { code: 'CHILD-B', _id: ids[1] },
                    { code: 'CHILD-C', _id: ids[2] },
                    { code: 'CHILD-D' },
                ],
            });
            await parent.$.save();
        });
        await checkTestNode([
            { _sortValue: 10, code: 'CHILD-A', _id: ids[0] },
            { _sortValue: 20, code: 'CHILD-B', _id: ids[1] },
            { _sortValue: 30, code: 'CHILD-C', _id: ids[2] },
            { _sortValue: 40, code: 'CHILD-D', _id: lastId + 1 },
        ]);
    });

    it('can create and delete children with full update', async () => {
        const ids = await createTestNode(3);
        const lastId = ids[ids.length - 1];

        await Test.committed(async context => {
            const parent = await readParentForUpdate(context, 'PARENT-1');
            await parent.$.set({
                children: [{ code: 'CHILD-A', _id: ids[0] }, { code: 'CHILD-B', _id: ids[1] }, { code: 'CHILD-D' }],
            });
            await parent.$.save();
        });
        await checkTestNode([
            { _sortValue: 10, code: 'CHILD-A', _id: ids[0] },
            { _sortValue: 20, code: 'CHILD-B', _id: ids[1] },
            { _sortValue: 30, code: 'CHILD-D', _id: lastId + 1 },
        ]);
    });

    it('can update with one removal and one append', async () => {
        const ids = await createTestNode(3);
        const lastId = ids[ids.length - 1];

        await Test.committed(async context => {
            const parent = await readParentForUpdate(context, 'PARENT-1');
            await parent.$.set({
                children: [
                    //
                    { code: 'CHILD-A', _id: ids[0] },
                    { code: 'CHILD-C', _id: ids[2] },
                    { code: 'CHILD-D' },
                ],
            });
            await parent.$.save();
        });
        await checkTestNode([
            { _sortValue: 10, code: 'CHILD-A', _id: ids[0] },
            { _sortValue: 30, code: 'CHILD-C', _id: ids[2] },
            { _sortValue: 40, code: 'CHILD-D', _id: lastId + 1 },
        ]);
    });

    it('can update with two inserted and two appended', async () => {
        const ids = await createTestNode(3);
        const lastId = ids[ids.length - 1];

        await Test.committed(async context => {
            const parent = await readParentForUpdate(context, 'PARENT-1');
            await parent.$.set({
                children: [
                    //
                    { code: 'CHILD-A', _id: ids[0] },
                    { code: 'CHILD-B' },
                    { code: 'CHILD-C' },
                    { code: 'CHILD-D', _id: ids[1] },
                    { _id: ids[2] },
                    { code: 'CHILD-E' },
                    { code: 'CHILD-F' },
                ],
            });
            await parent.$.save();
        });
        await checkTestNode([
            { _sortValue: 10, code: 'CHILD-A', _id: ids[0] },
            // _sortValue allocated between 0 and 1 for 2 new inserted items
            { _sortValue: 12, code: 'CHILD-B', _id: lastId + 1 },
            { _sortValue: 15, code: 'CHILD-C', _id: lastId + 2 },
            { _sortValue: 20, code: 'CHILD-D', _id: ids[1] },
            // kept old code for ids[2]
            { _sortValue: 30, code: 'CHILD-3', _id: ids[2] },
            // _sortValue allocated for appended records
            { _sortValue: 40, code: 'CHILD-E', _id: lastId + 3 },
            { _sortValue: 50, code: 'CHILD-F', _id: lastId + 4 },
        ]);
    });

    it('can reorder with 2 items moved', async () => {
        const ids = await createTestNode(6);

        await Test.committed(async context => {
            const parent = await readParentForUpdate(context, 'PARENT-1');
            await parent.$.set({
                children: [
                    { _id: ids[0] },
                    { _id: ids[3] },
                    { _id: ids[4] },
                    { _id: ids[1] },
                    { _id: ids[2] },
                    { _id: ids[5] },
                ],
            });
            await parent.$.save();
        });
        await checkTestNode([
            { _sortValue: 10, code: 'CHILD-1', _id: ids[0] },
            { _sortValue: 40, code: 'CHILD-4', _id: ids[3] },
            { _sortValue: 50, code: 'CHILD-5', _id: ids[4] },
            { _sortValue: 52, code: 'CHILD-2', _id: ids[1] },
            { _sortValue: 55, code: 'CHILD-3', _id: ids[2] },
            { _sortValue: 60, code: 'CHILD-6', _id: ids[5] },
        ]);
    });

    it('can reverse order', async () => {
        const ids = await createTestNode(6);

        await Test.committed(async context => {
            const parent = await readParentForUpdate(context, 'PARENT-1');
            await parent.$.set({
                children: [
                    { _id: ids[5] },
                    { _id: ids[4] },
                    { _id: ids[3] },
                    { _id: ids[2] },
                    { _id: ids[1] },
                    { _id: ids[0] },
                ],
            });
            await parent.$.save();
        });
        // Only one _sortValue is preserved: 5
        await checkTestNode([
            { _sortValue: 60, code: 'CHILD-6', _id: ids[5] },
            { _sortValue: 70, code: 'CHILD-5', _id: ids[4] },
            { _sortValue: 80, code: 'CHILD-4', _id: ids[3] },
            { _sortValue: 90, code: 'CHILD-3', _id: ids[2] },
            { _sortValue: 100, code: 'CHILD-2', _id: ids[1] },
            { _sortValue: 110, code: 'CHILD-1', _id: ids[0] },
        ]);
    });

    it('can mix reorder, insertions and deletions', async () => {
        const ids = await createTestNode(8);
        const lastId = ids[ids.length - 1];

        await Test.committed(async context => {
            const parent = await readParentForUpdate(context, 'PARENT-1');
            await parent.$.set({
                children: [
                    { _id: ids[0] },
                    { code: 'CHILD-A' },
                    { _id: ids[1] },
                    { _id: ids[4] },
                    { code: 'CHILD-B' },
                    { code: 'CHILD-C' },
                    { _id: ids[3] },
                    { code: 'CHILD-D' },
                    { _id: ids[6] },
                    { code: 'CHILD-E' },
                ],
            });
            await parent.$.save();
        });
        await checkTestNode([
            { _sortValue: 10, code: 'CHILD-1', _id: ids[0] },
            { _sortValue: 15, code: 'CHILD-A', _id: lastId + 1 },
            { _sortValue: 20, code: 'CHILD-2', _id: ids[1] },
            { _sortValue: 50, code: 'CHILD-5', _id: ids[4] },
            { _sortValue: 52, code: 'CHILD-B', _id: lastId + 2 },
            { _sortValue: 55, code: 'CHILD-C', _id: lastId + 3 },
            { _sortValue: 60, code: 'CHILD-4', _id: ids[3] },
            { _sortValue: 65, code: 'CHILD-D', _id: lastId + 4 },
            { _sortValue: 70, code: 'CHILD-7', _id: ids[6] },
            { _sortValue: 80, code: 'CHILD-E', _id: lastId + 5 },
        ]);
    });

    it('can append a line with a partial payload', async () => {
        const ids = await createTestNode(3);
        const lastId = ids[ids.length - 1];

        await Test.committed(async context => {
            const parent = await readParentForUpdate(context, 'PARENT-1');
            await parent.$.set({
                children: [{ _action: 'create', code: 'CHILD-A' }],
            });
            await parent.$.save();
        });
        await checkTestNode([
            { _sortValue: 10, code: 'CHILD-1', _id: ids[0] },
            { _sortValue: 20, code: 'CHILD-2', _id: ids[1] },
            { _sortValue: 30, code: 'CHILD-3', _id: ids[2] },
            { _sortValue: 40, code: 'CHILD-A', _id: lastId + 1 },
        ]);
    });

    it('can insert a line with a partial payload', async () => {
        const ids = await createTestNode(3);
        const lastId = ids[ids.length - 1];

        await Test.committed(async context => {
            const parent = await readParentForUpdate(context, 'PARENT-1');
            await parent.$.set({
                children: [{ _action: 'create', _sortValue: 25, code: 'CHILD-A' }],
            });
            await parent.$.save();
        });
        await checkTestNode([
            { _sortValue: 10, code: 'CHILD-1', _id: ids[0] },
            { _sortValue: 20, code: 'CHILD-2', _id: ids[1] },
            { _sortValue: 25, code: 'CHILD-A', _id: lastId + 1 },
            { _sortValue: 30, code: 'CHILD-3', _id: ids[2] },
        ]);
    });

    it('can update a line with with a partial payload', async () => {
        const ids = await createTestNode(3);

        await Test.committed(async context => {
            const parent = await readParentForUpdate(context, 'PARENT-1');
            await parent.$.set({
                children: [{ _action: 'update', _id: ids[1], code: 'CHILD-A' }],
            });
            await parent.$.save();
        });
        await checkTestNode([
            { _sortValue: 10, code: 'CHILD-1', _id: ids[0] },
            { _sortValue: 20, code: 'CHILD-A', _id: ids[1] },
            { _sortValue: 30, code: 'CHILD-3', _id: ids[2] },
        ]);
    });

    it('can update with _etag value in parent and vital child', async () => {
        const ids = await createTestNode(3);

        await Test.committed(async context => {
            const parent = await readParentForUpdate(context, 'PARENT-1');
            const oldEtag = await parent._etag;
            await parent.$.set({
                children: [
                    {
                        _action: 'update',
                        _id: ids[1],
                        code: 'CHILD-A',
                        _etag: await (await parent.children.at(1))?._etag,
                    },
                ],
                _etag: oldEtag,
            });
            await parent.$.save();
            assert.notEqual(await parent._etag, oldEtag);
        });
        await checkTestNode([
            { _sortValue: 10, code: 'CHILD-1', _id: ids[0] },
            { _sortValue: 20, code: 'CHILD-A', _id: ids[1] },
            { _sortValue: 30, code: 'CHILD-3', _id: ids[2] },
        ]);
    });

    it('can reorder a line with with a partial payload', async () => {
        const ids = await createTestNode(3);

        await Test.committed(async context => {
            const parent = await readParentForUpdate(context, 'PARENT-1');
            await parent.$.set({
                children: [{ _action: 'update', _sortValue: 40, _id: ids[1] }],
            });
            await parent.$.save();
        });
        await checkTestNode([
            { _sortValue: 10, code: 'CHILD-1', _id: ids[0] },
            { _sortValue: 30, code: 'CHILD-3', _id: ids[2] },
            { _sortValue: 40, code: 'CHILD-2', _id: ids[1] },
        ]);
    });

    it('throws if payload mixes lines with and without _action', async () => {
        const ids = await createTestNode(3);

        await assert.isRejected(
            Test.committed(async context => {
                const parent = await readParentForUpdate(context, 'PARENT-1');
                await parent.$.set({
                    children: [{ _action: 'update', _id: ids[1] }, { code: 'CHILD-A' }],
                });
            }),
            'TestVitalCollectionParent.children: invalid update data: mix of rows with and without _action',
        );
        await assert.isRejected(
            Test.committed(async context => {
                const parent = await readParentForUpdate(context, 'PARENT-1');
                await parent.$.set({
                    children: [{ code: 'CHILD-A' }, { _action: 'update', _id: ids[1] }],
                });
            }),
            'TestVitalCollectionParent.children: invalid update data: mix of rows with and without _action',
        );
    });

    it('throws if missing _sortValues are not all at the end in a partial payload', async () => {
        const ids = await createTestNode(3);

        await assert.isRejected(
            Test.committed(async context => {
                const parent = await readParentForUpdate(context, 'PARENT-1');
                await parent.$.set({
                    children: [
                        { _action: 'create', code: 'CHILD-A' },
                        { _action: 'update', _id: ids[1] },
                    ],
                });
            }),
            'TestVitalCollectionParent.children: invalid update array: some new entries without _sortValue are not at the end of the list',
        );
        await assert.isRejected(
            Test.committed(async context => {
                const parent = await readParentForUpdate(context, 'PARENT-1');
                await parent.$.set({
                    children: [
                        { _action: 'create', code: 'CHILD-A' },
                        { _action: 'create', _sortValue: 25, code: 'CHILD-B' },
                    ],
                });
            }),
            'TestVitalCollectionParent.children: invalid update array: some new entries without _sortValue are not at the end of the list',
        );
    });

    it('throws if update which creates node in collection has duplicate sort values', async () => {
        await createTestNode(3);

        await assert.isRejected(
            Test.withContext(async context => {
                const parent = await readParentForUpdate(context, 'PARENT-1');
                await parent.$.set({
                    children: [
                        { _action: 'create', _sortValue: 200, code: 'CHILD-A' },
                        { _action: 'create', _sortValue: 200, code: 'CHILD-B' },
                    ],
                });
            }),
            'TestVitalCollectionParent.children: cannot update collection: duplicate _sortValue 200',
        );
    });

    it('throws if update which creates node in collection has nodes that already exist', async () => {
        await createTestNode(3);

        await assert.isRejected(
            Test.withContext(async context => {
                const parent = await readParentForUpdate(context, 'PARENT-1');
                await parent.$.set({
                    children: [{ _action: 'create', _sortValue: 20, code: 'CHILD-A' }],
                });
            }),
            /TestVitalCollectionParent.children: cannot create entry: TestVitalCollectionChild:_id:/,
        );
    });

    it('can filter on _sortValue', async () => {
        await createTestNode(8);

        await Test.readonly(async context => {
            const lines = await context
                .query(TestVitalCollectionChild, { filter: { _sortValue: { _lte: 50 } } })
                .toArray();
            assert.deepEqual(
                await asyncArray(lines)
                    .map(line => line.$.sortValue)
                    .toArray(),
                [10, 20, 30, 40, 50],
            );
        });
    });

    it('can sort on _sortValue', async () => {
        await createTestNode(3);

        await Test.readonly(async context => {
            const lines = await context.query(TestVitalCollectionChild, { orderBy: { _sortValue: -1 } }).toArray();
            assert.deepEqual(
                await asyncArray(lines)
                    .map(line => line.$.sortValue)
                    .toArray(),
                [30, 20, 10],
            );
        });
    });

    it('can create with partial payload', async () => {
        await Test.committed(async context => {
            const parent = await context.create(TestVitalCollectionParent, {
                code: 'PARENT-1',
                children: [{ _action: 'create', code: 'CHILD-A' }],
            });
            await parent.$.save();
        });
        await checkTestNode([{ _sortValue: 10, code: 'CHILD-A' }]);
    });

    [true, false].forEach(writableParent => {
        it(`Can transact on vital child if parent is loaded in the ${
            writableParent ? 'writable' : 'readonly'
        } interning cache, the parent collection will be reloaded`, async () => {
            await createTestNode(3);

            await Test.withContext(async context => {
                const parent = await context.read(
                    TestVitalCollectionParent,
                    { code: 'PARENT-1' },
                    { forUpdate: writableParent },
                );

                assert.equal(await parent.computedLast, 'CHILD-3');

                const child = await context.read(TestVitalCollectionChild, { code: 'CHILD-1' }, { forUpdate: true });

                const childId = child._id;

                // Load the collection and check the original code value
                assert.equal(await (await parent.children.toArray()).find(c => c._id === childId)?.code, 'CHILD-1');

                await child.$.set({ code: 'CHILD-100000', _sortValue: 100000 });

                await child.$.save();

                // the array was sorted again, after the save and the first child is now last as we updated the sortValue
                // therefore the computed property on the parent returns the correct value
                // readonly, it reloads the collection
                // writable, the collection is resorted

                assert.equal(await parent.computedLast, 'CHILD-100000');
            });
        });
    });

    after(() => restoreTables());
});
