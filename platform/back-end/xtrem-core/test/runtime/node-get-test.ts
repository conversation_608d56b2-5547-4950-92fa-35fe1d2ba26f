import { assert } from 'chai';
import { asyncArray, Test } from '../../index';
import { MutableCollection } from '../../lib/collections';
import { testBasicDocumentApplication } from '../fixtures/applications';
import {
    documentData,
    documentLineData,
    initTables,
    referredData,
    referringData,
    restoreTables,
    setup,
} from '../fixtures/index';
import { TestDocument, TestDocumentLine, TestReferred, TestReferring } from '../fixtures/nodes';

describe('Node get proeprty value from path', () => {
    before(async () => {
        await setup({ application: await testBasicDocumentApplication.application });
        await initTables([
            { nodeConstructor: TestReferred, data: referredData },
            { nodeConstructor: TestReferring, data: referringData },
            { nodeConstructor: TestDocument, data: documentData },
            { nodeConstructor: TestDocumentLine, data: documentLineData },
        ]);
    });

    it('can get simple property value', () =>
        Test.withContext(async context => {
            const code = referringData[0].code;
            const node = await context.read(TestReferring, { code });
            const result = await node.$.get('code');
            assert.isString(result);
            assert.equal(result, code);
        }));

    it('can get reference property', () =>
        Test.withContext(async context => {
            const code = referringData[0].code;
            const node = await context.read(TestReferring, { code });
            let result = await node.$.get('reference');
            assert.instanceOf(result, TestReferred);
            result = await node.$.get('reference.code');
            assert.isString(result);
            assert.equal(result, referredData[0].code);
        }));

    it('can get reference array property', () =>
        Test.withContext(async context => {
            const code = referringData[0].code;
            const node = await context.read(TestReferring, { code });
            const resultArray = await node.$.get('referenceArray');
            assert.isArray(resultArray);
            if (Array.isArray(resultArray))
                resultArray.forEach(result => {
                    assert.instanceOf(result, TestReferred);
                });
        }));

    it('can get collection property', () =>
        Test.withContext(async context => {
            const code = documentData[0].code;
            const node = await context.read(TestDocument, { code });
            let result = await node.$.get('lines');
            assert.instanceOf(result, MutableCollection);
            await asyncArray(documentLineData.filter(lineData => lineData.document === documentData[0]._id)).forEach(
                async (lineData, i) => {
                    const currentRecord = (await (result as MutableCollection).at(i)) as TestDocumentLine;
                    assert.instanceOf(currentRecord, TestDocumentLine);
                    const currentDescription = await currentRecord?.description;
                    assert.equal(currentDescription, lineData.description);
                    const currentCode = await (await currentRecord?.document)?.code;
                    assert.isString(currentCode);
                    assert.equal(currentCode, documentData[0].code);
                },
            );

            result = await node.$.get('lines.length');
            assert.isNumber(result);
            assert.isTrue(result && typeof result === 'number' && result > 0);
        }));

    it('can get computed property', () =>
        Test.withContext(async context => {
            const node = await context.read(TestDocumentLine, { document: 1, lineNumber: 1 });
            const result = await node.$.get('getDescription');
            assert.isString(result);
            assert.equal(result, await node.description);
        }));

    it('can get complex value', () =>
        Test.withContext(async context => {
            const code = referringData[0].code;
            const node = await context.read(TestReferring, { code });
            const result = await node.$.get('reference.code.length');
            assert.isNumber(result);
            assert.isTrue(result && typeof result === 'number' && result > 0);
        }));

    it('gets undefined if property in path does not exists', () =>
        Test.withContext(async context => {
            const code = referringData[0].code;
            const node = await context.read(TestReferring, { code });
            const result = await node.$.get('reference.xxx.length');
            assert.isUndefined(result);
        }));

    after(() => restoreTables());
});
