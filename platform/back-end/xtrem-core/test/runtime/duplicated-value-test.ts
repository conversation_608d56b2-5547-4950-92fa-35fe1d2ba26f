import { assert } from 'chai';
import { Test } from '../../index';
import { decorators, Node, Reference, StringDataType, useDefaultValue } from '../../lib';
import { createApplicationWithApi, initTables, setup } from '../fixtures/index';
import {
    complexDuplicatedValueData,
    duplicatedNonVitalReferenceData,
    duplicatedOverrideData,
    duplicatedOverrideData2,
    duplicatedReferenceForNull,
    duplicatedValueChildData,
    duplicatedValueData,
    duplicatedValueGrandChildData,
    duplicatedValueParentData,
    duplicatedValueSecondChildData,
    duplicatedVitalReferenceData,
    TestComplexDuplicatedValue,
    TestDuplicatedBaseNode,
    TestDuplicatedChild,
    TestDuplicatedGrandChild,
    TestDuplicatedNonVitalReference,
    TestDuplicatedOverride,
    TestDuplicatedOverride2,
    TestDuplicatedParent,
    TestDuplicatedReferenceForNull,
    TestDuplicatedSecondChild,
    TestDuplicatedValue,
    TestDuplicatedVitalReference,
    TestReferred,
    TestUser,
} from '../fixtures/nodes';

/** Node with valid some duplicatedValue options for naturalKey fields*/
@decorators.node<TestValidNaturalKeyDuplicate>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canDuplicate: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true, isNaturalKey: true }],
})
class TestValidNaturalKeyDuplicate extends Node {
    @decorators.stringProperty<TestValidNaturalKeyDuplicate, 'code'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
        dataType: () => new StringDataType({ maxLength: 30 }),
        defaultValue: 'code',
        duplicatedValue: useDefaultValue,
    })
    readonly code: Promise<string>;

    @decorators.booleanProperty<TestValidNaturalKeyDuplicate, 'booleanVal'>({
        isStored: true,
        isPublished: true,
        duplicatedValue: true,
    })
    readonly booleanVal: Promise<boolean>;
}

/** Invalid Node with duplicatedValue, but no canCreate */
@decorators.node<TestInvalidDuplicatedValue>({
    isPublished: true,
    storage: 'sql',
    canCreate: false,
})
class TestInvalidDuplicatedValue extends Node {
    @decorators.stringProperty<TestInvalidDuplicatedValue, 'text'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 30 }),
        duplicatedValue: '',
    })
    readonly text: Promise<string>;

    @decorators.booleanProperty<TestInvalidDuplicatedValue, 'booleanVal'>({
        isStored: true,
        isPublished: true,
        duplicatedValue: true,
    })
    readonly booleanVal: Promise<boolean>;

    @decorators.booleanProperty<TestInvalidDuplicatedValue, 'booleanDuplicateDefault'>({
        isStored: true,
        isPublished: true,
        defaultValue: true,
        duplicatedValue: useDefaultValue,
    })
    readonly booleanDuplicateDefault: Promise<boolean>;
}

/** Node with invalid duplicatedValue - 'default' with no defaultValue */
@decorators.node<TestInvalidDuplicatedValue>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canDuplicate: true,
})
class TestInvalidDuplicatedDefaultValue extends Node {
    @decorators.stringProperty<TestInvalidDuplicatedDefaultValue, 'text'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 30 }),
        duplicatedValue: useDefaultValue,
    })
    readonly text: Promise<string>;

    @decorators.referenceProperty<TestInvalidDuplicatedDefaultValue, 'nonNullableReference'>({
        isPublished: true,
        isStored: true,
        node: () => TestReferred,
        isNullable: false,
        duplicatedValue: useDefaultValue,
    })
    readonly nonNullableReference: Reference<TestReferred | null>;

    @decorators.booleanProperty<TestInvalidDuplicatedDefaultValue, 'booleanVal'>({
        isStored: true,
        isPublished: true,
        duplicatedValue: true,
    })
    readonly booleanVal: Promise<boolean>;

    @decorators.booleanProperty<TestInvalidDuplicatedDefaultValue, 'booleanDuplicateDefault'>({
        isStored: true,
        isPublished: true,
        defaultValue: true,
        duplicatedValue: useDefaultValue,
    })
    readonly booleanDuplicateDefault: Promise<boolean>;
}

describe('duplicatedValue validations', () => {
    it('Can create application with valid duplicatedValue nodes', () =>
        createApplicationWithApi({
            nodes: {
                TestDuplicatedValue,
                TestDuplicatedNonVitalReference,
                TestDuplicatedVitalReference,
                TestValidNaturalKeyDuplicate,
                TestDuplicatedReferenceForNull,
                TestUser,
            },
        }));

    it('Cannot create application with invalid duplicatedValue for a non-vital node with no canCreate', async () => {
        await assert.isRejected(
            createApplicationWithApi({ nodes: { TestInvalidDuplicatedValue } }),
            'TestInvalidDuplicatedValue.text: The duplicatedValue attribute can only be set if the node can be duplicated.',
        );
    });

    it('Cannot create application with invalid duplicatedValue - no defaultValue', async () => {
        await assert.isRejected(
            createApplicationWithApi({
                nodes: { TestReferred, TestInvalidDuplicatedDefaultValue },
            }),
            'TestInvalidDuplicatedDefaultValue.nonNullableReference: Non-nullable reference properties with duplicatedValue: useDefaultValue need a defaultValue.',
        );
    });
});

describe('$.duplicate', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi({
                nodes: {
                    TestDuplicatedNonVitalReference,
                    TestDuplicatedValue,
                    TestDuplicatedVitalReference,
                    TestDuplicatedParent,
                    TestDuplicatedChild,
                    TestDuplicatedSecondChild,
                    TestDuplicatedGrandChild,
                    TestUser,
                    TestDuplicatedReferenceForNull,
                    TestComplexDuplicatedValue,
                    TestDuplicatedBaseNode,
                    TestDuplicatedOverride,
                    TestDuplicatedOverride2,
                },
            }),
        });
        await initTables([
            { nodeConstructor: TestDuplicatedReferenceForNull, data: duplicatedReferenceForNull },
            { nodeConstructor: TestDuplicatedNonVitalReference, data: duplicatedNonVitalReferenceData },
            { nodeConstructor: TestDuplicatedValue, data: duplicatedValueData },
            { nodeConstructor: TestDuplicatedVitalReference, data: duplicatedVitalReferenceData },
            { nodeConstructor: TestDuplicatedParent, data: duplicatedValueParentData },
            { nodeConstructor: TestDuplicatedChild, data: duplicatedValueChildData },
            { nodeConstructor: TestDuplicatedSecondChild, data: duplicatedValueSecondChildData },
            { nodeConstructor: TestDuplicatedGrandChild, data: duplicatedValueGrandChildData },
            { nodeConstructor: TestComplexDuplicatedValue, data: complexDuplicatedValueData },
            { nodeConstructor: TestDuplicatedOverride, data: duplicatedOverrideData },
            { nodeConstructor: TestDuplicatedOverride2, data: duplicatedOverrideData2 },
        ]);
    });

    it('Can duplicate node with children', () =>
        Test.withUncommittedContext(async context => {
            const parent = await context.read(TestDuplicatedParent, { _id: 2 });
            assert.instanceOf(parent, TestDuplicatedParent);
            assert.equal(
                await parent.linesWithDuplicatedValue.length,
                duplicatedValueSecondChildData.filter(data => data.duplicatedParent === 2).length,
            );
            const dup = await parent.$.duplicate();

            assert.equal(dup._id, -parent._id);
            assert.equal(await dup.code, await parent.code);
            assert.equal(await dup.stringDuplicateFunction, 'duplicatedValue');
            await dup.lines.forEach(async (line: TestDuplicatedChild) => {
                const idx = await dup.lines.findIndex((item: TestDuplicatedChild) => item === line);
                const parentLine = await parent.lines.elementAt(idx);
                assert.equal(line._id, -parentLine._id);
                assert.equal((await line.duplicatedParent)._id, dup._id);
                assert.equal(await line._sortValue, await parentLine._sortValue);
                assert.equal(await line.code, await parentLine.code);
                assert.equal(await line.stringDuplicateFunction, 'duplicatedValue');
                await line.childLines.forEach(async (childLine: TestDuplicatedGrandChild, index: number) => {
                    const parentLineChildLine = await parentLine.childLines.elementAt(index);
                    assert.equal((await childLine.duplicatedChildParent)._id, line._id);
                    assert.equal(await childLine._sortValue, await parentLineChildLine._sortValue);
                    assert.equal(await childLine.code, await parentLineChildLine.code);
                    assert.equal(await childLine.stringDuplicateFunction, 'duplicatedValue');
                });
            });

            assert.equal(await dup.linesWithDuplicatedValue.length, 0);
        }));

    it('Can duplicate child node and clear _sortValue', () =>
        Test.withUncommittedContext(async context => {
            const parent = await context.read(TestDuplicatedParent, { _id: 2 });
            assert.instanceOf(parent, TestDuplicatedParent);

            await parent.lines.forEach(async line => {
                const dup = await line.$.duplicate({ duplicatedReferenceWithNullDuplicate: 1 });
                const idx = await parent.lines.findIndex(item => item === line);
                const parentLine = await parent.lines.elementAt(idx);
                assert.equal(dup._id, -parentLine._id);
                assert.equal((await dup.duplicatedParent)._id, parent._id);
                assert.isAtMost(await dup._sortValue, 0);
                assert.equal(await dup.code, await (await parent.lines.elementAt(idx)).code);
                assert.equal(await dup.stringDuplicateFunction, 'duplicatedValue');
                assert.isNotNull(await dup.duplicatedReferenceWithNullDuplicate);

                await dup.childLines.forEach(async (childLine: TestDuplicatedGrandChild, index: number) => {
                    const parentLineChildLine = await parentLine.childLines.elementAt(index);
                    assert.equal((await childLine.duplicatedChildParent)._id, dup._id);
                    assert.equal(await childLine._sortValue, await parentLineChildLine._sortValue);
                    assert.equal(await childLine.code, await parentLineChildLine.code);
                    assert.equal(await childLine.stringDuplicateFunction, 'duplicatedValue');
                });
            });
        }));

    it('Can duplicate node with vital reference', () =>
        Test.withUncommittedContext(async context => {
            const parent = await context.read(TestDuplicatedValue, { _id: 1 });
            assert.instanceOf(parent, TestDuplicatedValue);
            const dup = await parent.$.duplicate();

            assert.equal(dup._id, -parent._id);
            assert.equal(await dup.booleanVal, await parent.booleanVal);
            assert.equal(await dup.booleanDuplicateDefault, true);
            assert.equal(await dup.booleanDuplicateTrueFunction, true);
            assert.equal(await dup.stringDuplicateFunction, '');
            assert.isNotNull(await dup.testVitalReference);
            assert.equal((await dup.testVitalReference)!._id, -(await parent.testVitalReference)!._id);
            assert.equal((await (await dup.testVitalReference)!.testDuplicatedValue)._id, dup._id);
            assert.equal(await (await dup.testVitalReference)!.code, 'COD01');

            await dup.$.save();
            const saved = await context.read(TestDuplicatedValue, { _id: dup._id });
            assert.equal(await saved.booleanVal, await parent.booleanVal);
            assert.equal(await saved.booleanDuplicateDefault, true);
            assert.equal(await saved.booleanDuplicateTrueFunction, true);
            assert.equal(await saved.stringDuplicateFunction, '');
            assert.isNotNull(await saved.testVitalReference);
            assert.equal((await saved.testVitalReference)!._id, 2);
            assert.equal(await (await saved.testVitalReference)!.code, 'COD01');
        }));

    it('Can duplicate node with a reference set to null by duplicatedValue', () =>
        Test.withUncommittedContext(async context => {
            const parent = await context.read(TestDuplicatedValue, {
                _id: 1,
            });
            assert.instanceOf(parent, TestDuplicatedValue);

            const dup = await parent.$.duplicate();
            assert.equal(dup._id, -parent._id);

            assert.isNotNull(await parent.testReference);
            assert.isNotNull(await parent.testReferenceNotNullable);
            assert.isNull(await dup.testReference);
        }));

    it('Can duplicate subNode with duplicatedValue override', () =>
        Test.withUncommittedContext(async context => {
            const parent = await context.read(TestDuplicatedOverride, {
                _id: 1,
            });
            assert.instanceOf(parent, TestDuplicatedOverride);

            const dup = await parent.$.duplicate();
            assert.equal(dup._id, -parent._id);

            assert.strictEqual(await dup.stringValue, 'duplicateOverride');
        }));

    it('Can duplicate another subNode from same super class with duplicatedValue override', () =>
        Test.withUncommittedContext(async context => {
            const parent = await context.read(TestDuplicatedOverride2, {
                _id: 3,
            });
            assert.instanceOf(parent, TestDuplicatedOverride2);

            const dup = await parent.$.duplicate();
            assert.equal(dup._id, -parent._id);

            assert.strictEqual(await dup.stringValue, 'anotherDuplicateOverride');
        }));
});
