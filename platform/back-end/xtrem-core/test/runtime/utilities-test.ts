import { assert } from 'chai';
import { makeName63 } from '../../lib/sql/statements/naming';

describe('Utilities', () => {
    it('makeName63 should return original name when 63 chars or less', () => {
        const someString = 'shortName';
        const result = makeName63(someString);
        assert.equal(result, someString);
    });

    it('makeName63 should return a name of 63 chars when given a string of more than 63 chars', () => {
        const someString = 'longNameThatIsOverZzzzzzzzzzAAAAAZzzzzzzzzzSixtyThreeCharactersLong';
        const result = makeName63(someString);
        assert.equal(result.length, 63);
    });
});
