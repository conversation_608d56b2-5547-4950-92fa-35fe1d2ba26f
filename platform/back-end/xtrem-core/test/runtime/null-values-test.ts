import { Decimal } from '@sage/xtrem-decimal';
import { assert } from 'chai';
import {
    BinaryStream,
    date,
    dateRange,
    datetime,
    datetimeRange,
    decimal,
    decorators,
    EnumDataType,
    integer,
    Node,
    short,
    Test,
} from '../../index';
import { defaultDecimalDataType } from '../fixtures/data-types/data-types';
import { createApplicationWithApi, initTables, restoreTables, setup, TestInitData } from '../fixtures/index';
import { cleanEnums } from '../sql/fixtures';

export enum TheEnumEnum {
    val1 = 1,
    val2 = 2,
    val3 = 3,
    val4 = 4,
    val5 = 5,
}

export type TheEnum = keyof typeof TheEnumEnum;

const theEnumDataType = new EnumDataType<TheEnum>({ enum: TheEnumEnum, filename: __filename });

@decorators.node<TestNullValues>({
    isPublished: true,
    storage: 'sql',
    indexes: [],
})
export class TestNullValues extends Node {
    @decorators.booleanProperty<TestNullValues, 'booleanVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly booleanVal: Promise<boolean | null>;

    @decorators.shortProperty<TestNullValues, 'shortVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly shortVal: Promise<short | null>;

    @decorators.integerProperty<TestNullValues, 'integerVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly integerVal: Promise<integer | null>;

    @decorators.enumProperty<TestNullValues, 'enumVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => theEnumDataType,
    })
    readonly enumVal: Promise<TheEnum | null>;

    @decorators.decimalProperty<TestNullValues, 'decimalVal'>({
        isPublished: true,
        isStored: true,
        dataType: () => defaultDecimalDataType,
        isNullable: true,
    })
    readonly decimalVal: Promise<decimal | null>;

    @decorators.floatProperty<TestNullValues, 'floatVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly floatVal: Promise<number | null>;

    @decorators.doubleProperty<TestNullValues, 'doubleVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly doubleVal: Promise<number | null>;

    @decorators.dateProperty<TestNullValues, 'dateVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly dateVal: Promise<date | null>;

    @decorators.dateRangeProperty<TestNullValues, 'dateRangeVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly dateRangeVal: Promise<dateRange | null>;

    @decorators.datetimeRangeProperty<TestNullValues, 'datetimeRangeVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly datetimeRangeVal: Promise<datetimeRange | null>;

    @decorators.datetimeProperty<TestNullValues, 'datetimeVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly datetimeVal: Promise<datetime | null>;

    @decorators.binaryStreamProperty<TestNullValues, 'binaryStreamVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly binaryStreamVal: Promise<BinaryStream | null>;
}

const data: TestInitData<TestNullValues>[] = [];
data.push({
    booleanVal: null,
    dateVal: null,
    dateRangeVal: null,
    datetimeVal: null,
    decimalVal: null,
    doubleVal: null,
    enumVal: null,
    floatVal: null,
    integerVal: null,
    shortVal: null,
    binaryStreamVal: null,
});
for (let i = 1; i <= 5; i += 1) {
    data.push({
        booleanVal: i % 2 === 1,
        dateVal: date.make(2020 + i, i, 1),
        dateRangeVal: dateRange.make(date.make(2020 + i, i, 1), date.make(2021 + i, i, 1)),
        datetimeRangeVal: datetimeRange.make(datetime.make(2020 + i, i, 1), datetime.make(2021 + i, i, 1)),
        datetimeVal: datetime.makeUtc(2020 + i, i, 1, i, i, i),
        decimalVal: Decimal.make(i - 2.5) as any,
        doubleVal: i - 2.5,
        enumVal: (['val1', 'val2', 'val3', 'val4', 'val5'] as TheEnum[])[i - 1],
        floatVal: i - 2.5,
        integerVal: i - 3,
        shortVal: i - 3,
        binaryStreamVal: BinaryStream.fromBuffer(Buffer.from([i, i + 200, i % 5])),
    });
}

const types = [
    'boolean',
    'date',
    'dateRange',
    'datetimeRange',
    'datetime',
    'decimal',
    'double',
    'enum',
    'float',
    'integer',
    'short',
    'binaryStream',
];

function getNodeWithNullValue(nodes: TestNullValues[]): TestNullValues | undefined {
    return nodes.find(n => (n as any).integerVal == null);
}

describe('test null values', () => {
    before(async () => {
        await setup({ application: await createApplicationWithApi({ nodes: { TestNullValues } }) });
        await initTables([{ nodeConstructor: TestNullValues, data }]);
    });

    types.forEach(typeName => {
        const propName = `${typeName}Val` as keyof TestNullValues;
        it(`check null position (ASC) : ${typeName}`, () =>
            Test.uncommitted(async context => {
                const nodes = await context.query(TestNullValues, { orderBy: { [propName]: 1 } }).toArray();
                assert.isNull(await nodes[0].$.getValue(propName));
            }));
    });
    types.forEach(typeName => {
        const propName = `${typeName}Val` as keyof TestNullValues;
        it(`check null position (DESC) : ${typeName}`, () =>
            Test.uncommitted(async context => {
                const nodes = await context.query(TestNullValues, { orderBy: { [propName]: -1 } }).toArray();
                assert.isNull(await nodes[nodes.length - 1].$.getValue(propName));
            }));
    });
    types.forEach(typeName => {
        const propName = `${typeName}Val` as keyof TestNullValues;
        if (typeName === 'binaryStream' || typeName === 'textStream') {
            it.skip(`NYI < : ${typeName}`, () => {});
        } else {
            it(`check < : ${typeName}`, () =>
                Test.uncommitted(async context => {
                    const nodes = await context
                        .query(TestNullValues, {
                            filter: {
                                [propName]: { _lte: (data[2] as any)[propName] },
                            },
                            orderBy: { [propName]: 1 },
                        })
                        .toArray();
                    // we have an 'order by asc' clause : the first node should be the one with null values
                    assert.isNull(await nodes[0].$.getValue(propName));
                    assert.equal(nodes.length, 3);
                }));
        }
    });
    types.forEach(typeName => {
        if (typeName === 'binaryStream' || typeName === 'textStream') {
            it.skip(`NYI < : ${typeName}`, () => {});
        } else {
            const propName = `${typeName}Val` as keyof TestNullValues;
            it(`check > : ${typeName}`, () =>
                Test.uncommitted(async context => {
                    const nodes = await context
                        .query(TestNullValues, {
                            filter: {
                                [propName]: { _gte: (data[2] as any)[propName] },
                            },
                            orderBy: { [propName]: -1 },
                        })
                        .toArray();
                    // nodes should not contain any node with null values
                    assert.isUndefined(getNodeWithNullValue(nodes));
                    if (propName === 'booleanVal') {
                        // Special case for booleans : data is created using modulo
                        assert.equal(nodes.length, 5);
                    } else {
                        assert.equal(nodes.length, 4);
                    }
                }));
        }
    });
    types.forEach(typeName => {
        const propName = `${typeName}Val` as keyof TestNullValues;
        it(`check == null : ${typeName}`, () =>
            Test.uncommitted(async context => {
                const nodes = await context
                    .query(TestNullValues, {
                        filter: {
                            [propName]: { _eq: null },
                        },
                    })
                    .toArray();
                assert.equal(nodes.length, 1);
                assert.isNull(await nodes[0].$.getValue(propName));
            }));
    });
    types.forEach(typeName => {
        const propName = `${typeName}Val`;
        it(`check != null : ${typeName}`, () =>
            Test.uncommitted(async context => {
                const nodes = await context
                    .query(TestNullValues, {
                        filter: {
                            [propName]: { _ne: null },
                        },
                    })
                    .toArray();
                assert.equal(nodes.length, 5);
                // nodes should not contain any node with null values
                assert.isUndefined(getNodeWithNullValue(nodes));
            }));
    });
    types.forEach(typeName => {
        const propName = `${typeName}Val` as keyof TestNullValues;
        it(`check in(null) : ${typeName}`, () =>
            Test.uncommitted(async context => {
                const nodes = await context
                    .query(TestNullValues, {
                        filter: {
                            [propName]: { _in: [null] },
                        },
                    })
                    .toArray();
                assert.equal(nodes.length, 1);
                assert.isNull(await nodes[0].$.getValue(propName));
            }));
    });
    types.forEach(typeName => {
        const propName = `${typeName}Val`;
        it(`check nin(null) : ${typeName}`, () =>
            Test.uncommitted(async context => {
                const nodes = await context
                    .query(TestNullValues, {
                        filter: {
                            [propName]: { _nin: [null] },
                        },
                    })
                    .toArray();
                assert.equal(nodes.length, 5);
                // nodes should not contain any node with null values
                assert.isUndefined(getNodeWithNullValue(nodes));
            }));
    });
    after(async () => {
        await restoreTables();
        await cleanEnums({ names: ['null_values_test_enum'] });
    });
});
