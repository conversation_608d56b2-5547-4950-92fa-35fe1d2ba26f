import { assert } from 'chai';
import { decorators, integer, Node, StringDataType } from '../../lib';
import { createApplicationWithApi } from '../fixtures/index';
import { TestAnonymizeExport } from '../fixtures/nodes';

/** Node with invalid anonymizeMethod: no anonymizeValue for fixed */
@decorators.node<TestInvalidNoFixedValue>({
    isPublished: true,
    storage: 'sql',
})
class TestInvalidNoFixedValue extends Node {
    @decorators.stringProperty<TestInvalidNoFixedValue, 'text'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 30 }),
        dataSensitivityLevel: 'notSensitive',
        anonymizeMethod: 'fixed',
    })
    readonly text: Promise<string>;
}

/** Node with invalid anonymizeMethod: no anonymizeValue for custom */
@decorators.node<TestInvalidNoCustomValue>({
    isPublished: true,
    storage: 'sql',
})
class TestInvalidNoCustomValue extends Node {
    @decorators.stringProperty<TestInvalidNoCustomValue, 'text'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 30 }),
        dataSensitivityLevel: 'notSensitive',
        anonymizeMethod: 'custom',
    })
    readonly text: Promise<string>;
}

/** Node with invalid anonymizeMethod: hash only for string */
@decorators.node<TestInvalidHashValue>({
    isPublished: true,
    storage: 'sql',
})
class TestInvalidHashValue extends Node {
    @decorators.integerProperty<TestInvalidHashValue, 'number'>({
        isStored: true,
        isPublished: true,
        dataSensitivityLevel: 'notSensitive',
        anonymizeMethod: 'hash',
    })
    readonly number: Promise<integer>;
}

/** Node with invalid anonymizeMethod: random only for numbers */
@decorators.node<TestInvalidRandomValue>({
    isPublished: true,
    storage: 'sql',
})
class TestInvalidRandomValue extends Node {
    @decorators.stringProperty<TestInvalidRandomValue, 'text'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 30 }),
        dataSensitivityLevel: 'notSensitive',
        anonymizeMethod: 'random',
    })
    readonly text: Promise<string>;
}

/** Node with anonymizeMethod, but no dataSensitivityLevel */
@decorators.node<TestNoDataSensitivityLevelValue>({
    isPublished: true,
    storage: 'sql',
})
class TestNoDataSensitivityLevelValue extends Node {
    @decorators.stringProperty<TestNoDataSensitivityLevelValue, 'text'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 30 }),
        anonymizeMethod: 'hash',
    })
    readonly text: Promise<string>;
}

describe('anonymizeMethod validations', () => {
    it('Can create application with valid anonymizeMethod node', () =>
        createApplicationWithApi({
            nodes: { TestAnonymizeExport },
        }));

    it('Cannot create application with invalid anonymizeMethod - no anonymizeValue for fixed', async () => {
        await assert.isRejected(
            createApplicationWithApi({ nodes: { TestInvalidNoFixedValue } }),
            "TestInvalidNoFixedValue.text: anonymizeValue is required for 'fixed' and 'custom' anonymize methods.",
        );
    });

    it('Cannot create application with invalid anonymizeMethod - no anonymizeValue for custom', async () => {
        await assert.isRejected(
            createApplicationWithApi({
                nodes: { TestInvalidNoCustomValue },
            }),
            "TestInvalidNoCustomValue.text: anonymizeValue is required for 'fixed' and 'custom' anonymize methods.",
        );
    });

    it('Cannot create application with invalid anonymizeMethod - no anonymizeValue for custom', async () => {
        await assert.isRejected(
            createApplicationWithApi({
                nodes: { TestInvalidHashValue },
            }),
            "TestInvalidHashValue.number: anonymizeMethod 'hash' is only applicable to properties of type string.",
        );
    });

    it('Cannot create application with invalid anonymizeMethod - no anonymizeValue for custom', async () => {
        await assert.isRejected(
            createApplicationWithApi({
                nodes: { TestInvalidRandomValue },
            }),
            "TestInvalidRandomValue.text: anonymizeMethod 'random' is only applicable to properties with a number type.",
        );
    });

    it('Cannot create application with invalid anonymizeMethod - no dataSensitivityLevel', async () => {
        await assert.isRejected(
            createApplicationWithApi({
                nodes: { TestNoDataSensitivityLevelValue },
            }),
            'TestNoDataSensitivityLevelValue.text: dataSensitivityLevel is required when specifying an anonymize method.',
        );
    });
});
