import { assert } from 'chai';
import { testDatatypesApplication } from '..';
import { Test } from '../../index';
import { datatypesData, initTables, restoreTables, setup } from '../fixtures/index';
import { TestDatatypes, testJsonDataType } from '../fixtures/nodes';

describe('testDatatypes tests (runtime)', () => {
    before(async () => {
        await setup({ application: await testDatatypesApplication.application });
        await initTables([{ nodeConstructor: TestDatatypes, data: datatypesData }]);
    });

    it('can get factory using node constructor', () =>
        Test.readonly(() => {
            const factory = Test.application.getFactoryByConstructor(TestDatatypes);
            assert.equal(factory.name, 'TestDatatypes');
            assert.equal(factory.isSharedByAllTenants, false);
            assert.equal(factory.package.packageName, '@sage/xtrem-core');
            assert.equal(factory.nodeConstructor, TestDatatypes);
            assert.deepEqual(
                factory.properties.map(property => property.name),
                [
                    '_updateTick',
                    '_updateUser',
                    '_createUser',
                    '_customData',
                    '_sourceId',
                    '_id',
                    'id',
                    'booleanVal',
                    'shortVal',
                    'integerVal',
                    'integerRangeVal',
                    'decimalRangeVal',
                    'enumVal',
                    'stringVal',
                    'decimalVal',
                    'floatVal',
                    'doubleVal',
                    'dateVal',
                    'dateRangeVal',
                    'datetimeRangeVal',
                    'timeVal',
                    'datetimeVal',
                    'binaryStream',
                    'textStream',
                    'mailTemplate',
                    'unsafeMailTemplate',
                    'uuidVal',
                    'computed',
                    'computedCached',
                    'complexComputed',
                    'jsonVal',
                    'integerArrayVal',
                    'enumArrayVal',
                    'stringArrayVal',
                    '_etag',
                    '_createStamp',
                    '_updateStamp',
                ],
            );
        }));

    it('can get external factories that have a property using a specified DataType', () =>
        Test.readonly(() => {
            const factory = Test.application.getFactoryByConstructor(TestDatatypes);
            const dataTypeFactories = Test.application.findFactoriesUsingDatatype(testJsonDataType);
            assert.deepEqual(dataTypeFactories, [factory]);
            assert(dataTypeFactories[0].properties.some(property => property.dataType === testJsonDataType));
        }));
    after(() => restoreTables());
});
