import { assert } from 'chai';
import { testBasicDocumentApplication } from '..';
import { Test } from '../../index';
import * as fixtures from '../fixtures';
import { initTables, referringData, restoreTables, setup } from '../fixtures/index';

const documentData = [
    {
        _id: 1,
        code: 'DOC1',
        description: 'doc 1',
        mandatoryReference: 1,
    },
    {
        _id: 2,
        code: 'DOC2',
        description: 'doc 2',
        mandatoryReference: 2,
    },
    {
        _id: 3,
        code: 'DOC3',
        description: 'doc 3',
        mandatoryReference: 1,
    },
];

export const documentLineData = [
    {
        _id: 1,
        document: 1,
        lineNumber: 1,
        description: 'line 1.1',
    },
    {
        _id: 2,
        document: 2,
        lineNumber: 1,
        description: 'line 2.1',
        optionalReference: 1,
    },
    {
        _id: 3,
        document: 2,
        lineNumber: 2,
        description: 'line 2.2',
    },
    {
        _id: 4,
        document: 3,
        lineNumber: 1,
        description: 'line 3.1',
    },
    {
        _id: 5,
        document: 3,
        lineNumber: 2,
        description: 'line 3.2',
    },
    {
        _id: 6,
        document: 3,
        lineNumber: 3,
        description: 'line 3.3',
    },
    {
        _id: 7,
        document: 3,
        lineNumber: 4,
        description: 'line 3.4',
    },
    {
        _id: 8,
        document: 3,
        lineNumber: 5,
        description: 'line 3.5',
    },
];

const referredData = [
    {
        _id: 1,
        code: 'REF1',
        details: 'reference 1',
    },
    {
        _id: 2,
        code: 'REF2',
        details: 'reference 2',
        integerVal: 2,
    },
];

describe('order by', () => {
    before(async () => {
        await setup({ application: await testBasicDocumentApplication.application });
        await initTables([
            { nodeConstructor: fixtures.nodes.TestReferred, data: referredData },
            { nodeConstructor: fixtures.nodes.TestReferring, data: referringData },
            { nodeConstructor: fixtures.nodes.TestDocument, data: documentData },
            { nodeConstructor: fixtures.nodes.TestDocumentLine, data: documentLineData },
        ]);
    });
    it('simple orderBy', () =>
        Test.readonly(async context => {
            const result = await context
                .query(fixtures.nodes.TestDocumentLine, {
                    orderBy: {
                        description: 1,
                    },
                })
                .map(async line => `${await (await line.document).code}/${await line.lineNumber}`)
                .toArray();
            assert.deepEqual(result, ['DOC1/1', 'DOC2/1', 'DOC2/2', 'DOC3/1', 'DOC3/2', 'DOC3/3', 'DOC3/4', 'DOC3/5']);
        }));
    it('simple reverse orderBy', () =>
        Test.readonly(async context => {
            const result = await context
                .query(fixtures.nodes.TestDocumentLine, {
                    orderBy: {
                        description: -1,
                    },
                })
                .map(async line => `${await (await line.document).code}/${await line.lineNumber}`)
                .toArray();
            assert.deepEqual(result, ['DOC3/5', 'DOC3/4', 'DOC3/3', 'DOC3/2', 'DOC3/1', 'DOC2/2', 'DOC2/1', 'DOC1/1']);
        }));
    it('default orderBy', () =>
        Test.readonly(async context => {
            const result = await context
                .query(fixtures.nodes.TestDocumentLine)
                .map(async line => `${await (await line.document).code}/${await line.lineNumber}`)
                .toArray();
            // Should be sorted by PK
            assert.deepEqual(result, ['DOC1/1', 'DOC2/1', 'DOC2/2', 'DOC3/1', 'DOC3/2', 'DOC3/3', 'DOC3/4', 'DOC3/5']);
        }));

    it('complex orderBy', () =>
        Test.readonly(async context => {
            const result = await context
                .query(fixtures.nodes.TestDocumentLine, {
                    orderBy: {
                        document: {
                            mandatoryReference: {
                                details: 1,
                            },
                            description: -1,
                        },
                        lineNumber: -1,
                    },
                })
                .map(async line => `${await (await line.document).code}/${await line.lineNumber}`)
                .toArray();
            assert.deepEqual(result, ['DOC3/5', 'DOC3/4', 'DOC3/3', 'DOC3/2', 'DOC3/1', 'DOC1/1', 'DOC2/2', 'DOC2/1']);
        }));

    it('orderBy on computed column of reference', () =>
        Test.readonly(async context => {
            const result = await context
                .query(fixtures.nodes.TestDocument, {
                    orderBy: {
                        mandatoryReference: {
                            computedVal: 1,
                        },
                    },
                })
                .map(async line => `${await line.code}`)
                .toArray();
            assert.deepEqual(result, ['DOC1', 'DOC3', 'DOC2']);
        }));

    it('orderBy on computed column that uses a property of a reference node', () =>
        Test.withContext(async context => {
            const result = await context
                .query(fixtures.nodes.TestReferring, {
                    orderBy: {
                        nestedComputedVal: 1,
                    },
                })
                .map(async referring => `${await referring.code}`)
                .toArray();
            assert.deepEqual(result, ['PAR1', 'PAR3', 'PAR2']);
        }));

    it('mixed orderBy/filter', () =>
        Test.readonly(async context => {
            const result = await context
                .query(fixtures.nodes.TestDocumentLine, {
                    filter: {
                        document: {
                            mandatoryReference: {
                                code: 'REF1',
                            },
                        },
                    },
                    orderBy: {
                        document: {
                            mandatoryReference: {
                                details: 1,
                            },
                            description: -1,
                        },
                        lineNumber: -1,
                    },
                })
                .map(async line => `${await (await line.document).code}/${await line.lineNumber}`)
                .toArray();
            assert.deepEqual(result, ['DOC3/5', 'DOC3/4', 'DOC3/3', 'DOC3/2', 'DOC3/1', 'DOC1/1']);
        }));

    it('orderBy on getValue property that is not stored', () =>
        Test.readonly(async context => {
            const result1 = await context
                .query(fixtures.nodes.TestDocumentLine, {
                    orderBy: {
                        description: 1,
                    },
                })
                .map(async line => (await line.document).code)
                .toArray();

            const result2 = await context
                .query(fixtures.nodes.TestDocumentLine, {
                    orderBy: {
                        getDescription: 1,
                    },
                })
                .map(async line => (await line.document).code)
                .toArray();

            assert.deepEqual(result1, result2);
            assert.deepEqual(
                result2,
                ['DOC1', 'DOC2', 'DOC2', 'DOC3', 'DOC3', 'DOC3', 'DOC3', 'DOC3'],
                result1.toString(),
            );
        }));

    after(() => restoreTables());
});
