import { assert } from 'chai';
import { decorators, Node, Test } from '../../index';
import { codeDataType } from '../fixtures/data-types/data-types';
import { createApplicationWithApi, initTables, restoreTables, setup } from '../fixtures/index';

@decorators.node<TestNodeFilter>({
    isPublished: true,
    storage: 'sql',
    async getFilters(context) {
        return (await context.user)?.email === '<EMAIL>' ? [{ code: 'CODE2' }] : [{}];
    },
    indexes: [{ orderBy: { code: 1 }, isUnique: true, isNaturalKey: true }],
})
export class TestNodeFilter extends Node {
    @decorators.stringProperty<TestNodeFilter, 'code'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;
}

describe('Node filter', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi({
                nodes: {
                    TestNodeFilter,
                },
            }),
        });
        await initTables([
            {
                nodeConstructor: TestNodeFilter,
                data: [{ code: 'CODE1' }, { code: 'CODE2' }, { code: 'CODE3' }],
            },
        ]);
    });

    it('filters queries if user has a filter', () =>
        Test.withContext(
            async context => {
                const codes1 = await context
                    .query(TestNodeFilter)
                    .map(node => node.code)
                    .toArray();
                assert.deepEqual(codes1, ['CODE2']);
            },
            { user: { email: '<EMAIL>' } },
        ));

    it('does not filter query if user does not have any filter', () =>
        Test.withContext(async context => {
            const codes1 = await context
                .query(TestNodeFilter)
                .map(node => node.code)
                .toArray();
            assert.deepEqual(codes1, ['CODE1', 'CODE2', 'CODE3']);
        }));

    it('filters read if user has a filter', () =>
        Test.withContext(
            async context => {
                assert.isNull(await context.tryRead(TestNodeFilter, { code: 'CODE1' }));
                assert.isNotNull(await context.tryRead(TestNodeFilter, { code: 'CODE2' }));
            },
            { user: { email: '<EMAIL>' } },
        ));

    it('does not filter read if user does not have any filter', () =>
        Test.withContext(async context => {
            assert.isNotNull(await context.tryRead(TestNodeFilter, { code: 'CODE1' }));
            assert.isNotNull(await context.tryRead(TestNodeFilter, { code: 'CODE2' }));
        }));

    it('filters read if user has a filter with # _id', () =>
        Test.withContext(
            async context => {
                const result = await context.tryRead(TestNodeFilter, { _id: '#CODE2' });
                assert.isNotNull(result);
                assert.equal(await result?.code, 'CODE2');
            },
            { user: { email: '<EMAIL>' } },
        ));

    it('applies filter to context.select', () =>
        Test.withContext(
            async context => {
                assert.deepEqual(await context.select(TestNodeFilter, { code: true }, { filter: {} }), [
                    { code: 'CODE2' },
                ]);
                assert.deepEqual(
                    await context.select(TestNodeFilter, { code: true }, { filter: { code: 'CODE1' } }),
                    [],
                );
            },
            { user: { email: '<EMAIL>' } },
        ));

    it('does not filter context.select if user does not have any filter', () =>
        Test.withContext(async context => {
            assert.deepEqual(await context.select(TestNodeFilter, { code: true }, { filter: {} }), [
                { code: 'CODE1' },
                { code: 'CODE2' },
                { code: 'CODE3' },
            ]);
        }));

    after(() => restoreTables());
});
