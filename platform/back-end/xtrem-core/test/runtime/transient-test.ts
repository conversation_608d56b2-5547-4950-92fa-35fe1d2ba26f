import { assert } from 'chai';
import { testTransientApplication } from '..';
import { Collection, decorators, Node, ValidationSeverity } from '../../lib';
import { Test } from '../../lib/test/test';
import * as fixtures from '../fixtures/index';
import { createApplicationWithApi, initTables, referredData, restoreTables, setup } from '../fixtures/index';
import { TestReferred, TestTransient, TestTransientLines } from '../fixtures/nodes';

@decorators.node<TestTransientBad1>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [],
})
export class TestTransientBad1 extends Node {
    @decorators.stringProperty<TestTransientBad1, 'stringValue'>({
        isPublished: true,
        dataType: () => fixtures.dataTypes.descriptionDataType,
    })
    readonly stringValue: Promise<string>;
}

@decorators.node<TestTransientBad2>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [],
})
export class TestTransientBad2 extends Node {
    @decorators.stringProperty<TestTransientBad2, 'stringValue'>({
        dataType: () => fixtures.dataTypes.descriptionDataType,
        isPublished: true,
        isStored: true,
        isTransientInput: true,
    })
    readonly stringValue: Promise<string>;
}

@decorators.node<TestTransientBad4>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [],
})
export class TestTransientBad4 extends Node {
    @decorators.collectionProperty<TestTransientBad4, 'transientLines'>({
        isPublished: true,
        node: () => TestTransientLines,
        reverseReference: 'parent',
        isTransientInput: true,
        isVital: true,
    })
    readonly transientLines: Collection<TestTransientLines>;
}

describe('Transient property test', () => {
    before(async () => {
        await setup({ application: await testTransientApplication.application });
        await initTables([
            { nodeConstructor: TestTransient, data: [] },
            { nodeConstructor: TestTransientLines, data: [] },
            { nodeConstructor: TestReferred, data: [] },
        ]);
    });
    it('can create with simple transient property', () =>
        Test.uncommitted(async context => {
            let node = await context.create(TestTransient, {
                stringValue: 'Value 1',
                transientValue: 'transient text',
            });
            await node.$.save();
            let readResult = await context.read(TestTransient, { _id: node._id });
            assert.equal(await readResult.derivedValue, 'transient set in defaultValue (transient text)- added');
            node = await context.create(TestTransient, { stringValue: 'Value 2' });
            await node.$.save();
            readResult = await context.read(TestTransient, { _id: node._id });
            assert.equal(await readResult.derivedValue, 'transient not set in defaultValue - added');
        }));

    it('can create with reference transient property', () =>
        Test.uncommitted(async context => {
            const referred = await context.create(TestReferred, { ...referredData[0], _id: undefined });
            await referred.$.save();
            let node = await context.create(TestTransient, {
                stringValue: 'Reference 1',
                transientRef: referred,
            });
            await node.$.save();
            let readResult = await context.read(TestTransient, { _id: node._id });
            assert.equal(await readResult.stringValue, 'Reference 1');
            assert.equal(await readResult.derivedFromRef, await referred.code);

            // Create a second one
            node = await context.create(TestTransient, {
                stringValue: 'Reference 2',
                transientRef: referred,
            });
            await node.$.save();
            readResult = await context.read(TestTransient, { _id: node._id });
            assert.equal(await readResult.stringValue, 'Reference 2');
            assert.equal(await readResult.derivedFromRef, await referred.code);
        }));

    it('can create with collection transient property', () =>
        Test.uncommitted(async context => {
            let node = await context.create(TestTransient, {
                stringValue: 'Collection 1',
                transientLines: [{ stringValue: 'col1' }, { stringValue: 'col2' }],
            });
            await node.$.save();
            let readResult = await context.read(TestTransient, { _id: node._id });
            assert.equal(await readResult.stringValue, 'Collection 1');

            await readResult.derivedCollection.forEach(async (colNode, i) => {
                switch (i) {
                    case 0:
                        assert.equal(await colNode.stringValue, 'col1');
                        break;
                    case 1:
                        assert.equal(await colNode.stringValue, 'col2');
                        break;
                    default:
                        break;
                }
            });
            node = await context.create(TestTransient, { stringValue: 'Collection 2' });
            await node.$.save();
            readResult = await context.read(TestTransient, { _id: node._id });
            assert.equal(await readResult.stringValue, 'Collection 2');
            assert.deepEqual(await readResult.derivedCollection.length, 0);
        }));

    it('cannot persist with collection transient property', () =>
        Test.uncommitted(async context => {
            const node = await context.create(TestTransient, {
                stringValue: 'Collection 1',
                transientLines: [{ stringValue: 'EXCLUDE' }],
            });
            await node.$.save();
            const readResult = await context.read(TestTransient, { _id: node._id });
            assert.equal(await readResult.stringValue, 'Collection 1');

            assert.deepEqual(await readResult.derivedCollection.length, 0);

            const collectionQuery = await context.query(TestTransientLines, { filter: { parent: node._id } }).toArray();
            assert.deepEqual(collectionQuery, []);
        }));

    it('can update with transient property', () =>
        Test.uncommitted(async context => {
            const node = await context.create(TestTransient, {
                stringValue: 'Value 1',
                transientValue: 'transient text',
            });
            await node.$.save();
            let readResult = await context.read(TestTransient, { _id: node._id }, { forUpdate: true });
            assert.equal(await readResult.stringValue, 'Value 1');
            assert.equal(await readResult.derivedValue, 'transient set in defaultValue (transient text)- added');

            await readResult.$.set({ stringValue: 'Value 2' });
            await readResult.$.set({ transientValue: 'second transient text' });
            await readResult.$.save();

            readResult = await context.read(TestTransient, { _id: node._id }, { forUpdate: true });
            assert.equal(await readResult.stringValue, 'Value 2');
            assert.equal(
                await readResult.derivedValue,
                'transient set in defaultValue (second transient text)- modified',
            );

            await readResult.$.set({ stringValue: 'Value 3' });
            await readResult.$.set({ transientValue: '' });
            await readResult.$.save();

            assert.equal(await readResult.stringValue, 'Value 3');
            assert.equal(await readResult.derivedValue, 'transient not set in defaultValue - modified');
        }));

    it('can control with transient property', async () => {
        // Control on node decorator using transient property
        await Test.uncommitted(async context => {
            const referred2 = await context.create(TestReferred, { ...referredData[1], _id: undefined });
            await referred2.$.save();
            const node = await context.create(TestTransient, {
                stringValue: 'Value 1',
                transientValue: 'transient text',
                transientRef: referred2,
            });
            await assert.isRejected(node.$.save(), /was not created/);
            assert.deepEqual(node.$.context.diagnoses, [
                {
                    severity: ValidationSeverity.error,
                    path: [],
                    message: "value must not be equal to 'REF2'",
                },
            ]);
        });

        // Control on property decorator (derivedFromRef)
        await Test.uncommitted(async context => {
            const referred1 = await context.create(TestReferred, { ...referredData[0], _id: undefined });
            await referred1.$.save();
            const node = await context.create(TestTransient, {
                stringValue: 'Value 1',
                transientValue: 'transient text',
            });
            await node.$.save();
            const readResult = await context.read(TestTransient, { _id: node._id }, { forUpdate: true });
            assert.equal(await readResult.stringValue, 'Value 1');
            assert.equal(await readResult.derivedValue, 'transient set in defaultValue (transient text)- added');
            await readResult.$.set({ stringValue: 'Value 2' });
            await readResult.$.set({ transientValue: 'second transient text' });
            await readResult.$.set({ transientRef: referred1 });
            await assert.isRejected(readResult.$.save());
            assert.deepEqual(node.$.context.diagnoses, [
                {
                    severity: ValidationSeverity.error,
                    path: ['derivedFromRef'],
                    message: "value must not be equal to 'REF1'",
                },
            ]);
        });
    });

    it('cannot have a property with isStored, isTransientInput or getValue', async () => {
        await assert.isRejected(
            createApplicationWithApi({ nodes: { TestTransientBad1 } }),
            "TestTransientBad1.stringValue: A published property needs either 'isStored', 'isVital', 'isMutable', 'isTransientInput', 'computeValue', 'getValue' or 'join' decorator member to be set.",
        );
    });

    it('cannot have a property with isStored and isTransientInput set at the same time', async () => {
        await assert.isRejected(
            createApplicationWithApi({ nodes: { TestTransientBad2 } }),
            "TestTransientBad2.stringValue: Property attributes 'isStored' and 'isTransientInput' cannot be both true on a single property.",
        );
    });

    it('cannot have a vital property that is transient', async () => {
        await assert.isRejected(
            createApplicationWithApi({
                nodes: { TestReferred, TestTransient, TestTransientLines, TestTransientBad4 },
            }),
            'TestTransientBad4.transientLines: A vital property cannot be a transient input',
        );
    });

    after(() => restoreTables());
});
