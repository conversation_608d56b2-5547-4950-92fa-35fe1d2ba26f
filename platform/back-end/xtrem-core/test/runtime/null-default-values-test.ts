import { assert } from 'chai';
import { date, decorators, Node, Reference, Test } from '../../index';
import { codeDataType, descriptionDataType } from '../fixtures/data-types/data-types';
import { createApplicationWithApi, initTables, restoreTables, setup } from '../fixtures/index';

/** DECLARE SOME TYPES */
@decorators.node<TestWithNullInDb>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestWithNullInDb',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
class TestWithNullInDb extends Node {
    @decorators.stringProperty<TestWithNullInDb, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.dateProperty<TestWithNullInDb, 'nullablePropDate'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly nullablePropDate: Promise<date | null>;

    @decorators.integerProperty<TestWithNullInDb, 'nullablePropInt'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly nullablePropInt: Promise<number | null>;

    @decorators.dateProperty<TestWithNullInDb, 'notNullablePropDate'>({
        isPublished: true,
        isStored: true,
    })
    readonly notNullablePropDate: Promise<date>;

    @decorators.integerProperty<TestWithNullInDb, 'notNullablePropInt'>({
        isPublished: true,
        isStored: true,
    })
    readonly notNullablePropInt: Promise<number>;

    @decorators.stringProperty<TestWithNullInDb, 'notNullablePropStr'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly notNullablePropStr: Promise<string>;
}

/** DECLARE SOME TYPES */
@decorators.node<TestWithoutNullInDb>({
    tableName: 'TestWithoutNullInDb',
    //    code: 'ZNOTNULLDEF', // Defining a code will make the framework consider this class as a "classic class", without any null value in DB
    isPublished: true,
    storage: 'sql',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
class TestWithoutNullInDb extends Node {
    @decorators.stringProperty<TestWithoutNullInDb, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.dateProperty<TestWithoutNullInDb, 'nullablePropDate'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly nullablePropDate: Promise<date | null>;

    @decorators.dateProperty<TestWithoutNullInDb, 'notNullablePropDate'>({
        isPublished: true,
        isStored: true,
    })
    readonly notNullablePropDate: Promise<date>;

    @decorators.integerProperty<TestWithoutNullInDb, 'notNullablePropInt'>({
        isPublished: true,
        isStored: true,
    })
    readonly notNullablePropInt: Promise<number>;

    @decorators.stringProperty<TestWithoutNullInDb, 'notNullablePropStr'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly notNullablePropStr: Promise<string>;
}

/** DECLARE SOME TYPES */
@decorators.node<TestNullChild>({
    // code: 'ZNULLCHILD', // Defining a code will make the framework consider this class as a "classic class", without any null value in DB
    isPublished: true,
    storage: 'sql',
    tableName: 'TestNullChild',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
class TestNullChild extends Node {
    @decorators.stringProperty<TestNullChild, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestNullChild, 'parent'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        node: () => TestNullParent,
        defaultValue: () => {
            // Do not remove : this line ensures that defaultValue() is allowed to return null
            return null;
        },
    })
    readonly parent: Reference<TestNullParent | null>;
}

/** DECLARE SOME TYPES */
@decorators.node<TestNullParent>({
    // code: 'ZNULLPARENT', // Defining a code will make the framework consider this class as a "classic class", without any null value in DB
    isPublished: true,
    storage: 'sql',
    tableName: 'TestNullParent',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
class TestNullParent extends Node {
    @decorators.integerProperty<TestNullParent, 'code'>({
        isPublished: true,
        isStored: true,
    })
    readonly code: Promise<number>;
}

const data = [
    {
        code: 'ITEM1',
        notNullablePropInt: 1,
        notNullablePropStr: 'Item 1',
        notNullablePropDate: date.today(),
    },
];

describe('null default values tests', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi({
                nodes: {
                    TestWithNullInDb,
                    TestWithoutNullInDb,
                    TestNullChild,
                    TestNullParent,
                },
            }),
        });
        await initTables([
            { nodeConstructor: TestWithNullInDb, data },
            { nodeConstructor: TestWithoutNullInDb, data },
            { nodeConstructor: TestNullChild, data: [] },
            { nodeConstructor: TestNullParent, data: [] },
        ]);
    });
    it('Relations with null', () =>
        Test.uncommitted(async context => {
            const child1 = await context.create(TestNullChild, {
                code: 'child1',
            });
            await child1.$.save();
            assert.deepEqual(context.diagnoses, []);
        }));

    [TestWithNullInDb, TestWithoutNullInDb].forEach(type => {
        it(`Check initTables ${type.name}`, () =>
            Test.uncommitted(async context => {
                const result = await context
                    .query(type, {
                        filter: {
                            code: { _eq: 'ITEM1' },
                        },
                    })
                    .toArray();
                assert.equal(result.length, 1);
                assert.equal(await result[0].code, 'ITEM1');
                assert.isNull(await result[0].nullablePropDate);
                if (type instanceof TestWithNullInDb) {
                    assert.isNull(await (result[0] as TestWithNullInDb).nullablePropInt);
                }
                assert.isNotNull(await result[0].notNullablePropDate);
                assert.isNotNull(await result[0].notNullablePropInt);
                assert.isNotNull(await result[0].notNullablePropStr);
            }));
    });
    [TestWithNullInDb, TestWithoutNullInDb].forEach(type => {
        it(`check initTables (null filter) ${type.name}`, () =>
            Test.uncommitted(async context => {
                const result = await context
                    .query(type, {
                        filter: {
                            nullablePropDate: { _eq: null },
                        },
                    })
                    .toArray();
                assert.equal(result.length, 1);
                assert.equal(await result[0].code, 'ITEM1');
                assert.isNull(await result[0].nullablePropDate);
                if (type instanceof TestWithNullInDb) {
                    assert.isNull(await (result[0] as TestWithNullInDb).nullablePropInt);
                }
                assert.isNotNull(await result[0].notNullablePropDate);
                assert.isNotNull(await result[0].notNullablePropInt);
                assert.isNotNull(await result[0].notNullablePropStr);
            }));
    });
    [TestWithNullInDb, TestWithoutNullInDb].forEach(type => {
        it(`create from code  ${type.name}`, () =>
            Test.uncommitted(async context => {
                const node = await context.create(type, {
                    code: 'ITEM2',
                    notNullablePropDate: date.today(),
                    notNullablePropInt: 2,
                    notNullablePropStr: 'Item 2',
                });
                assert.isNull(await node.nullablePropDate);
                await node.$.save();

                const result = await context
                    .query(type, {
                        filter: {
                            code: { _eq: 'ITEM2' },
                        },
                    })
                    .toArray();
                assert.equal(result.length, 1);
                assert.equal(await result[0].code, 'ITEM2');
                assert.isNull(await result[0].nullablePropDate);
                if (type instanceof TestWithNullInDb) {
                    assert.isNull(await (result[0] as TestWithNullInDb).nullablePropInt);
                }
                assert.isNotNull(await result[0].notNullablePropDate);
                assert.isNotNull(await result[0].notNullablePropInt);
                assert.isNotNull(await result[0].notNullablePropStr);
            }));
    });
    after(() => restoreTables());
});
