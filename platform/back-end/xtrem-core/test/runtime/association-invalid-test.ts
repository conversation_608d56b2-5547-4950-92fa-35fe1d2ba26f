import { assert } from 'chai';
import { Collection, Node, Reference, decorators } from '../../index';
import { name } from '../fixtures/data-types/data-types';
import { createApplicationWithApi } from '../fixtures/index';

// Some nodes with invalid association definitions
@decorators.node<TestAssociationNoReverseRef>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    indexes: [{ orderBy: { name: 1 }, isUnique: true, isNaturalKey: true }],
})
export class TestAssociationNoReverseRef extends Node {
    @decorators.stringProperty<TestAssociationNoReverseRef, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => name,
    })
    readonly name: Promise<string>;

    @decorators.collectionProperty<TestAssociationNoReverseRef, 'refs'>({
        isPublished: true,
        isAssociation: true,
        node: () => TestAssociationInvalid,
        // reverseReference: 'noReverseRef',
    })
    readonly refs: Collection<TestAssociationInvalid>;
}

@decorators.node<TestAssociationInvalid>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    isAssociationCollectionChild: true,
})
export class TestAssociationInvalid extends Node {
    @decorators.referenceProperty<TestAssociationInvalid, 'noReverseRef'>({
        isStored: true,
        isPublished: true,
        isAssociationParent: true,
        node: () => TestAssociationNoReverseRef,
    })
    readonly noReverseRef: Reference<TestAssociationNoReverseRef>;
}

@decorators.node<TestAssociationRefInvalidNoChild>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    indexes: [{ orderBy: { name: 1 }, isUnique: true, isNaturalKey: true }],
})
export class TestAssociationRefInvalidNoChild extends Node {
    @decorators.stringProperty<TestAssociationRefInvalidNoChild, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => name,
    })
    readonly name: Promise<string>;

    @decorators.collectionProperty<TestAssociationRefInvalidNoChild, 'refs'>({
        isPublished: true,
        isAssociation: true,
        node: () => TestAssociationInvalidNoChild,
        reverseReference: 'ref',
    })
    readonly refs: Collection<TestAssociationInvalidNoChild>;
}

@decorators.node<TestAssociationInvalidNoChild>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    // isAssociationCollectionChild: true,
})
export class TestAssociationInvalidNoChild extends Node {
    @decorators.referenceProperty<TestAssociationInvalidNoChild, 'ref'>({
        isStored: true,
        isPublished: true,
        isAssociationParent: true,
        node: () => TestAssociationRefInvalidNoChild,
    })
    readonly ref: Reference<TestAssociationRefInvalidNoChild>;
}

@decorators.node<TestAssociationRef>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    indexes: [{ orderBy: { name: 1 }, isUnique: true, isNaturalKey: true }],
})
export class TestAssociationRef extends Node {
    @decorators.stringProperty<TestAssociationRef, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => name,
    })
    readonly name: Promise<string>;

    @decorators.collectionProperty<TestAssociationRef, 'refs'>({
        isPublished: true,
        isAssociation: true,
        node: () => TestAssociation,
        reverseReference: 'ref',
    })
    readonly refs: Collection<TestAssociation>;
}

@decorators.node<TestAssociationRef2>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    indexes: [{ orderBy: { name: 1 }, isUnique: true, isNaturalKey: true }],
})
export class TestAssociationRef2 extends Node {
    @decorators.stringProperty<TestAssociationRef2, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => name,
    })
    readonly name: Promise<string>;

    @decorators.collectionProperty<TestAssociationRef2, 'refs'>({
        isPublished: true,
        isAssociation: true,
        node: () => TestAssociation,
        reverseReference: 'ref2',
    })
    readonly refs: Collection<TestAssociation>;
}

@decorators.node<TestAssociationRef3>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    indexes: [{ orderBy: { name: 1 }, isUnique: true, isNaturalKey: true }],
})
export class TestAssociationRef3 extends Node {
    @decorators.stringProperty<TestAssociationRef3, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => name,
    })
    readonly name: Promise<string>;

    @decorators.collectionProperty<TestAssociationRef3, 'refs'>({
        isPublished: true,
        isAssociation: true,
        node: () => TestAssociationInvalid3,
        reverseReference: 'ref3',
    })
    readonly refs: Collection<TestAssociationInvalid3>;
}

@decorators.node<TestAssociation>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    isAssociationCollectionChild: true,
})
export class TestAssociation extends Node {
    @decorators.referenceProperty<TestAssociation, 'ref'>({
        isStored: true,
        isPublished: true,
        isAssociationParent: true,
        node: () => TestAssociationRef,
    })
    readonly ref: Reference<TestAssociationRef>;

    @decorators.referenceProperty<TestAssociation, 'ref2'>({
        isStored: true,
        isPublished: true,
        isAssociationParent: true,
        node: () => TestAssociationRef2,
    })
    readonly ref2: Reference<TestAssociationRef2>;
}

@decorators.node<TestAssociationInvalid2>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    isAssociationCollectionChild: true,
})
export class TestAssociationInvalid2 extends Node {
    @decorators.referenceProperty<TestAssociationInvalid2, 'ref'>({
        isStored: true,
        isPublished: true,
        isAssociationParent: true,
        isNullable: true,
        node: () => TestAssociationRef,
    })
    readonly ref: Reference<TestAssociationRef>;

    @decorators.referenceProperty<TestAssociationInvalid2, 'ref2'>({
        isStored: true,
        isPublished: true,
        isAssociationParent: true,
        node: () => TestAssociationRef2,
    })
    readonly ref2: Reference<TestAssociationRef2>;
}

@decorators.node<TestAssociationInvalid3>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    isAssociationCollectionChild: true,
})
export class TestAssociationInvalid3 extends Node {
    @decorators.referenceProperty<TestAssociationInvalid3, 'ref'>({
        isStored: true,
        isPublished: true,
        isAssociationParent: true,
        node: () => TestAssociationRef,
    })
    readonly ref: Reference<TestAssociationRef>;

    @decorators.referenceProperty<TestAssociationInvalid3, 'ref3'>({
        isStored: true,
        isPublished: true,
        // isAssociationParent: true,
        node: () => TestAssociationRef3,
    })
    readonly ref3: Reference<TestAssociationRef3>;
}

describe('Association reference', () => {
    it('Cannot create application with invalid association definition - no reverse reference', async () => {
        await assert.isRejected(
            createApplicationWithApi({
                nodes: {
                    TestAssociationNoReverseRef,
                    TestAssociationInvalid,
                },
            }),
            "TestAssociationNoReverseRef.refs: An association property must have a 'reverseReference' attribute",
        );
    });

    it('Cannot create application with invalid association definition - no association child', async () => {
        await assert.isRejected(
            createApplicationWithApi({
                nodes: {
                    TestAssociationRefInvalidNoChild,
                    TestAssociationInvalidNoChild,
                },
            }),
            'TestAssociationRefInvalidNoChild.refs: The referenced class does not have an isAssociationCollectionChild or isAssociationReferenceChild attribute',
        );
    });

    it('Cannot create application with invalid association definition - cannot be nullable', async () => {
        await assert.isRejected(
            createApplicationWithApi({
                nodes: {
                    TestAssociationInvalid2,
                    TestAssociationRef,
                    TestAssociationRef2,
                    TestAssociation,
                },
            }),
            'TestAssociationInvalid2.ref: required property cannot be nullable',
        );
    });

    it('Cannot create application with invalid association definition - invalid reverse reference', async () => {
        await assert.isRejected(
            createApplicationWithApi({
                nodes: {
                    TestAssociationInvalid3,
                    TestAssociationRef,
                    TestAssociationRef2,
                    TestAssociationRef3,
                    TestAssociation,
                },
            }),
            "TestAssociationRef3.refs: The 'reverseReference' attribute must point to the property flagged with 'isAssociationParent' in the target child node",
        );
    });
});
