import { assert } from 'chai';
import { Test } from '../../index';
import { testCachedNodeApplication } from '../fixtures/applications';
import { initTables, restoreTables, setup } from '../fixtures/index';
import { TestCachedNode } from '../fixtures/nodes/test-cached-node';

const data = [
    {
        _id: 1,
        value: 'VAL1',
    },
    {
        _id: 2,
        value: 'VAL2',
    },
    {
        _id: 3,
        value: 'VAL3',
    },
];

// LATER
describe.skip('nodeFactoryCache', () => {
    before(async () => {
        await setup({ application: await testCachedNodeApplication.application });
        await initTables([{ nodeConstructor: TestCachedNode, data }]);
    });

    it('cache invalidation', async () => {
        const initialLength = await Test.withUncommittedContext(context => context.query(TestCachedNode).length);

        await Test.withCommittedContext(async context => {
            const newNode = await context.create(TestCachedNode, { value: 'VAL4', value2: '1' });
            await newNode.$.save();
        });

        await Test.withCommittedContext(async context => {
            const allNodesBeforeDelete = await context.query(TestCachedNode).length;
            assert.equal(allNodesBeforeDelete, initialLength + 1);

            await context.executeSql(`DELETE FROM ${context.schemaName}.test_cached_node WHERE value=$1`, ['VAL4']);

            // Note : a direct SQL query was executed on the table but the factory cache was not invalidated
            // allNodesAfterDelete should still contain the directly deleted node
            const allNodesBeforeInvalidation = await context.query(TestCachedNode).length;
            assert.equal(allNodesBeforeInvalidation, initialLength + 1);

            // Invalidate the factory cache
            await context.application.getFactoryByName('TestCachedNode').invalidateCache(context);

            // context.query should now execute a new SQL query (as the factory cache was invalidated) and return an array WITHOUT the deleted node
            const allNodesAfterInvalidation = await context.query(TestCachedNode).length;
            assert.equal(allNodesAfterInvalidation, initialLength);
        });
    });

    it('table.insert', async () => {
        const allNodes = await Test.withUncommittedContext(context => context.query(TestCachedNode).toArray());
        const initialLength = allNodes.length;

        const factory = Test.application.getFactoryByName('TestCachedNode');
        const table = factory.table;
        await Test.withCommittedContext(async context => {
            await table.insert(context, { value: 'VAL5', value2: '1' });
            const allNodesAfterInsert = await context.query(TestCachedNode).toArray();
            // Table.insert should have invalidated the cache and the new record should be there
            assert.equal(allNodesAfterInsert.length, initialLength + 1);
        });
    });

    it('table.update', async () => {
        const allNodes = await Test.withUncommittedContext(context => context.query(TestCachedNode).toArray());
        const initialLength = allNodes.length;

        const id = await Test.withCommittedContext(async context => {
            const newNode = await context.create(TestCachedNode, { value: 'VAL6', value2: '1' });
            await newNode.$.save();
            return newNode.$.id;
        });

        const factory = Test.application.getFactoryByName('TestCachedNode');
        const table = factory.table;
        await Test.withCommittedContext(async context => {
            await table.update(context, { _id: id, value: 'VAL6', value2: '2' });
            const allNodesAfterUpdate = await context.query(TestCachedNode).toArray();
            // Table.update should have invalidated the cache and the new record should be there
            assert.equal(allNodesAfterUpdate.length, initialLength + 1);

            const lastNode = allNodesAfterUpdate[allNodesAfterUpdate.length - 1];
            assert.equal(await lastNode.value, 'VAL6');
            assert.equal(await lastNode.value2, '2');
        });
    });

    it.skip('table.update.fail', async () => {
        // For Bruno : investigate why this test fails with 'Release called on client which has already been released to the pool.'
        // when running the last context.query (the toArray is done outside of the context)
        const allNodes = await (await Test.withUncommittedContext(context => context.query(TestCachedNode))).toArray();
        const initialLength = allNodes.length;

        const id = await Test.withCommittedContext(async context => {
            const newNode = await context.create(TestCachedNode, { value: 'VAL6', value2: '1' });
            await newNode.$.save();
            return newNode.$.id;
        });

        const factory = Test.application.getFactoryByName('TestCachedNode');
        const table = factory.table;
        await Test.withCommittedContext(async context => {
            await table.update(context, { _id: id, value: 'VAL6', value2: '2' });
        });

        const allNodes2 = await (await Test.withUncommittedContext(context => context.query(TestCachedNode))).toArray();
        // Table.update should have invalidated the cache and the new record should be there
        assert.equal(allNodes2.length, initialLength + 1);
    });

    after(() => restoreTables());
});
