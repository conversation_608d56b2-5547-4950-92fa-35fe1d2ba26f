import { assert } from 'chai';
import { SystemProperties, Test } from '../../index';
import * as fixtures from '../fixtures';
import { createApplicationWithApi, initTables, restoreTables, setup } from '../fixtures/index';
import { cleanEnums } from '../sql/fixtures';

const { TestNullableUniqueIndex, TestDatatypes } = fixtures.nodes;

describe('nullable unique index', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi({ nodes: { TestNullableUniqueIndex, TestDatatypes } }),
        });
        // Manually create the table.
        await initTables([
            { nodeConstructor: TestNullableUniqueIndex, data: [] },
            { nodeConstructor: TestDatatypes, data: [] },
        ]);
    });

    it('supported nullable column replaced by coalesce functions', () =>
        Test.withContext(context => {
            const factory = context.application.getFactoryByName('TestNullableUniqueIndex');
            const indexes = factory.getAllIndexes();
            assert.equal(indexes.length, 4);
            const tenantIdColumn = SystemProperties.tenantIdColumn(factory);
            const indexDefs = indexes.map(index =>
                factory.table.getIndexDefinition(context.schemaName, index, tenantIdColumn),
            );

            assert.deepEqual(indexDefs, [
                {
                    name: 'test_nullable_unique_index_ind0',
                    isUnique: true,
                    columns: [
                        {
                            name: '_tenant_id',
                            ascending: true,
                        },
                        {
                            name: 'id',
                            ascending: true,
                        },
                        {
                            name: 'reference',
                            ascending: true,
                            expression: 'COALESCE(reference, (0)::bigint)',
                        },
                    ],
                },
                {
                    name: 'test_nullable_unique_index_ind1',
                    isUnique: true,
                    columns: [
                        {
                            name: '_tenant_id',
                            ascending: true,
                        },
                        {
                            name: 'id',
                            ascending: true,
                        },
                        {
                            name: 'integer_val',
                            ascending: true,
                            expression: 'COALESCE(integer_val, (- ((2)::bigint ^ (62)::bigint))::bigint)',
                        },
                    ],
                },
                {
                    name: 'test_nullable_unique_index_ind2',
                    isUnique: true,
                    columns: [
                        {
                            name: '_tenant_id',
                            ascending: true,
                        },
                        {
                            name: 'id',
                            ascending: true,
                        },
                        {
                            name: 'enum_val',
                            ascending: true,
                            expression: `${context.schemaName}.test_enum_nullable_enum_coalesce(enum_val)`,
                        },
                    ],
                },
                {
                    name: 'test_nullable_unique_index_ind3',
                    isUnique: true,
                    columns: [
                        {
                            name: '_tenant_id',
                            ascending: true,
                        },
                        {
                            name: 'id',
                            ascending: true,
                        },
                        {
                            name: 'date_val',
                            ascending: true,
                            expression: "COALESCE(date_val, '0001-01-01'::date)",
                        },
                    ],
                },
            ]);
        }));

    after(async () => {
        await restoreTables();
        await cleanEnums({ names: ['test_enum_nullable_enum'], cascade: true });
    });
});
