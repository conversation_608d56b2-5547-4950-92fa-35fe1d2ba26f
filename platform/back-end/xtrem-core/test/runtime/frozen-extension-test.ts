import { assert } from 'chai';
import { Test } from '../../index';
import { testExtensionIsFrozenChainingApplication } from '../fixtures/applications';
import { initTables, restoreTables, setup } from '../fixtures/index';
import {
    TestFrozenCallbackChaining,
    TestFrozenValueFalseChaining,
    TestFrozenValueTrueChaining,
} from '../fixtures/nodes';

describe('frozen extension chaining', () => {
    before(async () => {
        await setup({ application: await testExtensionIsFrozenChainingApplication.application });
        await initTables([
            { nodeConstructor: TestFrozenValueFalseChaining, data: [] },
            { nodeConstructor: TestFrozenValueTrueChaining, data: [] },
            { nodeConstructor: TestFrozenCallbackChaining, data: [] },
        ]);
    });

    it('can create regardless of frozen values', () =>
        Test.withContext(async context => {
            const node = await context.create(TestFrozenCallbackChaining, { code: 'FRO<PERSON><PERSON>', callbackFrozen: 'FROZEN' });
            await node.$.save();
            assert.equal(await node.code, 'FROZEN');
            assert.equal(await node.callbackFrozen, 'FROZEN');
        }));

    it('cannot update when node is frozen', () =>
        Test.withContext(async context => {
            const node = await context.create(TestFrozenCallbackChaining, { code: 'FROZEN', callbackFrozen: 'Desc A' });
            await node.$.save();
            const readResult = await context.read(TestFrozenCallbackChaining, { code: 'FROZEN' }, { forUpdate: true });
            await assert.isRejected(
                readResult.$.set({ callbackFrozen: 'Desc X' }),
                'TestFrozenCallbackChaining: node is frozen',
            );
        }));

    it('cannot update frozen property', () =>
        Test.withContext(async context => {
            const node = await context.create(TestFrozenCallbackChaining, { code: 'A', callbackFrozen: 'FROZEN' });
            await node.$.save();
            const readResult = await context.read(TestFrozenCallbackChaining, { code: 'A' }, { forUpdate: true });
            await assert.isRejected(
                readResult.$.set({ callbackFrozen: 'Desc X' }),
                'TestFrozenCallbackChaining.callbackFrozen: cannot set value on frozen property',
            );
        }));

    it('can update non-frozen property', () =>
        Test.withContext(async context => {
            const node = await context.create(TestFrozenCallbackChaining, { code: 'A', callbackFrozen: 'FROZEN' });
            await node.$.save();
            const readResult = await context.read(TestFrozenCallbackChaining, { code: 'A' }, { forUpdate: true });
            await readResult.$.set({ code: 'B' });
            await readResult.$.save();
            assert.strictEqual(await readResult.code, 'B');
        }));

    // isFrozen turns to false
    it('isFrozen turns to false: can update node with non-frozen property', () =>
        Test.withContext(async context => {
            const node = await context.create(TestFrozenValueFalseChaining, {
                code: 'A1',
                strFromOverrideNotFrozen: 'strFromOverrideNotFrozen',
                callbackFrozen: 'callbackFrozen',
                valueFrozen: 'valueFrozen',
                notFrozen: 'notFrozen',
            });
            await node.$.save();
            const readResult = await context.read(TestFrozenValueFalseChaining, { code: 'A1' }, { forUpdate: true });
            assert.strictEqual(await readResult.callbackFrozen, 'callbackFrozen');
            await readResult.$.set({ callbackFrozen: 'FROZEN', notFrozen: 'Updated text' });
            await readResult.$.save();
            assert.strictEqual(await readResult.callbackFrozen, 'FROZEN');
            assert.strictEqual(await readResult.notFrozen, 'Updated text');
        }));

    it('isFrozen turns to false: cannot update node with isFrozen override on property', () =>
        Test.withContext(async context => {
            const node = await context.create(TestFrozenValueFalseChaining, {
                code: 'A1',
                strFromOverrideNotFrozen: 'strFromOverrideNotFrozen',
                callbackFrozen: 'FROZEN',
                valueFrozen: 'valueFrozen',
                notFrozen: 'notFrozen',
            });
            await node.$.save();
            const readResult = await context.read(TestFrozenValueFalseChaining, { code: 'A1' }, { forUpdate: true });
            await assert.isRejected(
                readResult.$.set({ callbackFrozen: 'Updated text' }),
                'TestFrozenValueFalseChaining.callbackFrozen: cannot set value on frozen property',
            );
        }));

    // isFrozen turns to true
    it('isFrozen turns to true: can update node with non-frozen property', () =>
        Test.withContext(async context => {
            const node = await context.create(TestFrozenValueTrueChaining, {
                code: 'A1',
                strFromOverrideNotFrozen: 'strFromOverrideNotFrozen',
                callbackFrozen: 'callbackFrozen',
                valueFrozen: 'valueFrozen',
                notFrozen: 'notFrozen',
            });
            await node.$.save();
            const readResult = await context.read(TestFrozenValueTrueChaining, { code: 'A1' }, { forUpdate: true });
            assert.strictEqual(await readResult.callbackFrozen, 'callbackFrozen');
            await assert.isRejected(
                readResult.$.set({ callbackFrozen: 'Updated text' }),
                'TestFrozenValueTrueChaining.callbackFrozen: cannot set value on frozen property',
            );
        }));

    it('isFrozen turns to true: cannot update node with isFrozen override on property', () =>
        Test.withContext(async context => {
            const node = await context.create(TestFrozenValueTrueChaining, {
                code: 'A1',
                strFromOverrideNotFrozen: 'strFromOverrideNotFrozen',
                callbackFrozen: 'FROZEN',
                valueFrozen: 'valueFrozen',
                notFrozen: 'notFrozen',
            });
            await node.$.save();
            const readResult = await context.read(TestFrozenValueTrueChaining, { code: 'A1' }, { forUpdate: true });
            await assert.isRejected(
                readResult.$.set({ callbackFrozen: 'Updated text' }),
                'TestFrozenValueTrueChaining.callbackFrozen: cannot set value on frozen property',
            );
        }));

    // isFrozen depends on callback
    it('isFrozen depends on callback: can update node with non-frozen property (callback on base node) then frozen on next update because of base node', () =>
        Test.withContext(async context => {
            const node = await context.create(TestFrozenCallbackChaining, {
                code: 'A1',
                strFromOverrideNotFrozen: 'strFromOverrideNotFrozen',
                callbackFrozen: 'callbackFrozen',
                valueFrozen: 'valueFrozen',
                notFrozen: 'notFrozen',
            });
            await node.$.save();
            const readResult = await context.read(TestFrozenCallbackChaining, { code: 'A1' }, { forUpdate: true });
            assert.strictEqual(await readResult.callbackFrozen, 'callbackFrozen');
            await readResult.$.set({ callbackFrozen: 'FROZEN', notFrozen: 'Updated text' });
            await readResult.$.save();
            assert.strictEqual(await readResult.callbackFrozen, 'FROZEN');
            assert.strictEqual(await readResult.notFrozen, 'Updated text');
            const readResultFrozen = await context.read(
                TestFrozenCallbackChaining,
                { code: 'A1' },
                { forUpdate: true },
            );
            await assert.isRejected(
                readResultFrozen.$.set({ callbackFrozen: 'Updated text' }),
                'TestFrozenCallbackChaining.callbackFrozen: cannot set value on frozen property',
            );
        }));

    it('isFrozen depends on callback: can update node with non-frozen property (callback on base node) then frozen on next update because of node extension', () =>
        Test.withContext(async context => {
            const node = await context.create(TestFrozenCallbackChaining, {
                code: 'A1',
                strFromOverrideNotFrozen: 'strFromOverrideNotFrozen',
                callbackFrozen: 'callbackFrozen',
                valueFrozen: 'valueFrozen',
                notFrozen: 'notFrozen',
            });
            await node.$.save();
            const readResult = await context.read(TestFrozenCallbackChaining, { code: 'A1' }, { forUpdate: true });
            assert.strictEqual(await readResult.callbackFrozen, 'callbackFrozen');
            await readResult.$.set({ callbackFrozen: 'EXT-FROZEN', notFrozen: 'Updated text' });
            await readResult.$.save();
            assert.strictEqual(await readResult.callbackFrozen, 'EXT-FROZEN');
            assert.strictEqual(await readResult.notFrozen, 'Updated text');
            const readResultFrozen = await context.read(
                TestFrozenCallbackChaining,
                { code: 'A1' },
                { forUpdate: true },
            );
            await assert.isRejected(
                readResultFrozen.$.set({ callbackFrozen: 'Updated text' }),
                'TestFrozenCallbackChaining.callbackFrozen: cannot set value on frozen property',
            );
        }));

    it('isFrozen depends on callback: can update node with non-frozen property (false on base node) then frozen on next update because of node extension', () =>
        Test.withContext(async context => {
            const node = await context.create(TestFrozenCallbackChaining, {
                code: 'A1',
                strFromOverrideNotFrozen: 'strFromOverrideNotFrozen',
                callbackFrozen: 'callbackFrozen',
                valueFrozen: 'valueFrozen',
                notFrozen: 'notFrozen',
            });
            await node.$.save();
            const readResult = await context.read(TestFrozenCallbackChaining, { code: 'A1' }, { forUpdate: true });
            assert.strictEqual(await readResult.notFrozen, 'notFrozen');
            await readResult.$.set({ notFrozen: 'NOT-FROZEN' });
            await readResult.$.save();
            assert.strictEqual(await readResult.notFrozen, 'NOT-FROZEN');
            const readResultFrozen = await context.read(
                TestFrozenCallbackChaining,
                { code: 'A1' },
                { forUpdate: true },
            );
            await assert.isRejected(
                readResultFrozen.$.set({ notFrozen: 'Updated text' }),
                'TestFrozenCallbackChaining.notFrozen: cannot set value on frozen property',
            );
        }));
    after(() => restoreTables());
});
