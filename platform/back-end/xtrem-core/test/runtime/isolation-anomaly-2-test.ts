import { integer } from '@sage/xtrem-shared';
import { assert } from 'chai';
import { Context, decorators, Node, Test } from '../../index';
import { IsolationOptions } from '../../lib';
import { codeDataType } from '../fixtures/data-types/data-types';
import {
    createApplicationWith<PERSON>pi,
    GraphQl<PERSON>elper,
    graphqlSetup,
    initTables,
    restoreTables,
    setup,
} from '../fixtures/index';

// Anomaly #2 from https://drkp.net/papers/ssi-vldb12.pdf
//
// ----------------------------------- + -------------------------------+ -------------------------------
// Report on last closed batch         | Add Receipt to batch           | Close batch
// ----------------------------------- + -------------------------------+ -------------------------------
//                                     | x ← SELECT current batch       |
//                                     |                                |
//                                     |                                | INCREMENT current batch
//                                     |                                | COMMIT
// x ← SELECT current batch            |                                |
//                                     |                                |
// SELECT SUM(amount) FROM receipts    |                                |
//     WHERE batch = x−1               |                                |
// COMMIT                              |                                |
//                                     |                                |
//                                     | INSERT INTO receipts           |
//                                     |     VALUES (x, somedata)       |
//                                     | COMMIT                         |

@decorators.node<TestBatchValue>({
    storage: 'sql',
    canDeleteMany: true,
    indexes: [{ orderBy: { name: 1 }, isUnique: true }],
})
class TestBatchValue extends Node {
    @decorators.stringProperty<TestBatchValue, 'name'>({
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly name: Promise<string>;

    @decorators.integerProperty<TestBatchValue, 'value'>({
        isStored: true,
    })
    readonly value: Promise<integer>;
}

@decorators.node<TestReceipt>({
    storage: 'sql',
    canDeleteMany: true,
    indexes: [{ orderBy: { id: 1 }, isUnique: true }],
})
class TestReceipt extends Node {
    @decorators.integerProperty<TestReceipt, 'batchNumber'>({
        isStored: true,
    })
    readonly batchNumber: Promise<integer>;

    @decorators.stringProperty<TestReceipt, 'id'>({
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly id: Promise<string>;

    @decorators.integerProperty<TestReceipt, 'amount'>({
        isStored: true,
    })
    readonly amount: Promise<integer>;
}

@decorators.node<TestAnomaly2>({
    isPublished: true,
})
class TestAnomaly2 extends Node {
    static async getCurrentBatchNumber(context: Context): Promise<number> {
        return (await context.read(TestBatchValue, { name: 'currentNumber' })).value;
    }

    @decorators.mutation<typeof TestAnomaly2, 'addReceipt'>({
        isPublished: true,
        isolationLevel: 'high',
        parameters: [],
        return: 'boolean',
    })
    static async addReceipt(context: Context): Promise<boolean> {
        const batchNumber = await this.getCurrentBatchNumber(context);

        conditions.afterReceiptReadBatch.notifyAll();
        await conditions.afterReportCommit.wait();

        const receipt = await context.create(TestReceipt, { id: 'R4', batchNumber, amount: 50 });
        await receipt.$.save();
        return true;
    }

    @decorators.mutation<typeof TestAnomaly2, 'closeBatch'>({
        isPublished: true,
        isolationLevel: 'high',
        parameters: [],
        return: 'integer',
    })
    static async closeBatch(context: Context): Promise<number> {
        await conditions.afterReceiptReadBatch.wait();

        return context.bulkUpdate(TestBatchValue, {
            set: {
                async value() {
                    return (await this.value) + 1;
                },
            },
            async where() {
                return (await this.name) === 'currentNumber';
            },
        });
    }

    static async getBatchTotalAmount(context: Context, batchNumber: integer): Promise<number> {
        const aggregate = (
            await context.readAggregate(TestReceipt, {
                filter: { batchNumber },
                values: { amount: { sum: true } },
            })
        ).amount;
        return aggregate.sum || 0;
    }

    @decorators.query<typeof TestAnomaly2, 'report'>({
        isPublished: true,
        isolationLevel: 'high',
        parameters: [],
        return: {
            type: 'object',
            properties: {
                batchNumber: 'integer',
                batchAmount: 'integer',
            },
        },
    })
    static async report(context: Context): Promise<{ batchNumber: integer; batchAmount: integer }> {
        const batchNumber = (await this.getCurrentBatchNumber(context)) - 1;

        const batchAmount = await this.getBatchTotalAmount(context, batchNumber);

        return { batchNumber, batchAmount };
    }

    @decorators.query<typeof TestAnomaly2, 'deferrableReport'>({
        isPublished: true,
        isolationLevel: 'high',
        isDeferrable: true,
        parameters: [],
        return: {
            type: 'object',
            properties: {
                batchNumber: 'integer',
                batchAmount: 'integer',
            },
        },
    })
    static async deferrableReport(context: Context): Promise<{ batchNumber: integer; batchAmount: integer }> {
        const batchNumber = (await this.getCurrentBatchNumber(context)) - 1;

        const batchAmount = await this.getBatchTotalAmount(context, batchNumber);

        return { batchNumber, batchAmount };
    }
}

class Conditions {
    afterReceiptReadBatch = Test.createConditionVariable('afterReceiptReadBatch');

    afterCloseBatchCommit = Test.createConditionVariable('afterCloseBatchCommit');

    afterReportCommit = Test.createConditionVariable('afterReportCommit');
}

let conditions: Conditions;

const tsTasks = {
    addReceipt(options: IsolationOptions) {
        return Test.withCommittedContext(context => TestAnomaly2.addReceipt(context), options);
    },

    async closeBatch(options: IsolationOptions) {
        const result = await Test.withCommittedContext(context => TestAnomaly2.closeBatch(context), options);

        conditions.afterCloseBatchCommit.notifyAll();

        return result;
    },

    async report(options: IsolationOptions) {
        await conditions.afterCloseBatchCommit.wait();

        const result = await Test.withReadonlyContext(context => TestAnomaly2.report(context), {
            isReadonly: true,
            ...options,
        });

        conditions.afterReportCommit.notifyAll();

        return result;
    },

    getCurrentBatchNumber(): Promise<number> {
        return Test.withReadonlyContext(context => TestAnomaly2.getCurrentBatchNumber(context));
    },

    getBatchTotalAmount(batchNumber: integer) {
        return Test.withReadonlyContext(context => TestAnomaly2.getBatchTotalAmount(context, batchNumber));
    },
};

export type Tasks = typeof tsTasks;

let graphqlHelper: GraphQlHelper;

const graphQlTasks = (isDeferrable: boolean) => ({
    ...tsTasks,
    async addReceipt() {
        const result = (
            await graphqlHelper.mutation<{ testAnomaly2: { addReceipt: boolean } }>('{ testAnomaly2 { addReceipt } }')
        ).testAnomaly2.addReceipt;
        return result;
    },

    async closeBatch() {
        const result = (
            await graphqlHelper.mutation<{ testAnomaly2: { closeBatch: integer } }>('{ testAnomaly2 { closeBatch } }')
        ).testAnomaly2.closeBatch;

        conditions.afterCloseBatchCommit.notifyAll();

        return result;
    },

    async report() {
        await conditions.afterCloseBatchCommit.wait();

        const method = isDeferrable ? 'deferrableReport' : 'report';
        const result = (
            await graphqlHelper.query<{
                testAnomaly2: {
                    [K in 'report' | 'deferrableReport']: { batchNumber: integer; batchAmount: integer };
                };
            }>(`{ testAnomaly2 { ${method} { batchNumber, batchAmount } } }`)
        ).testAnomaly2[method];

        conditions.afterReportCommit.notifyAll();

        return result;
    },
});

describe('can handle serialization anomaly #2', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi({ nodes: { TestBatchValue, TestReceipt, TestAnomaly2 } }),
        });
        graphqlHelper = await graphqlSetup({ application: Test.application });
    });
    before(() =>
        initTables([
            {
                nodeConstructor: TestBatchValue,
                data: [{ name: 'currentNumber', value: 5 }],
            },
            {
                nodeConstructor: TestReceipt,
                data: [
                    { batchNumber: 4, id: 'R1', amount: 10 },
                    { batchNumber: 5, id: 'R2', amount: 5 },
                    { batchNumber: 5, id: 'R3', amount: 20 },
                ],
            },
        ]),
    );

    async function resetData(): Promise<void> {
        await Test.withCommittedContext(async context => {
            await context.bulkUpdate(TestBatchValue, {
                set: { value: 5 },
                where: {
                    name: 'currentNumber',
                },
            });
            await context.deleteMany(TestReceipt, {
                id: 'R4',
            });
        });
    }

    async function testBatchClose(
        tasks: Tasks,
        options: IsolationOptions,
    ): Promise<{ batchNumber: integer; batchAmount: integer; batch5Amount: integer; batch6Amount: integer }> {
        await resetData();

        conditions = new Conditions();

        if (options.isDeferrable) {
            // The report transaction will be deferred to the next _safe_ snapshot.
            // We need to relax the afterReportCommit condition so that the addReceipt transaction can complete
            // and the database reaches a _safe_ snapshot.
            setTimeout(() => conditions.afterReportCommit.notifyAll(), 500);
        }

        const writeOptions = { ...options, isDeferrable: false };
        const promises = {
            addReceipt: tasks.addReceipt(writeOptions),
            closeBatch: tasks.closeBatch(writeOptions),
            report: tasks.report(options),
        };
        const results = {
            addReceipt: await promises.addReceipt,
            closeBatch: await promises.closeBatch,
            report: await promises.report,
        };

        assert.equal(await tsTasks.getCurrentBatchNumber(), 6);
        return {
            ...results.report,
            batch5Amount: await tsTasks.getBatchTotalAmount(5),
            batch6Amount: await tsTasks.getBatchTotalAmount(6),
        };
    }

    const results = {
        success: {
            // isolation succeeded: addReceipt was retried after the two other.
            batchNumber: 5,
            batchAmount: 25,
            batch5Amount: 25,
            batch6Amount: 50,
        },
        successDeferrable: {
            // isolation succeeded (but differently!): report was deferred after the two other transactions.
            batchNumber: 5,
            batchAmount: 75,
            batch5Amount: 75,
            batch6Amount: 0,
        },
        failure: {
            // isolation failed: report is incorrect because R4 was added to batch 5 after batch 5 was closed
            batchNumber: 5,
            batchAmount: 25,
            batch5Amount: 75,
            batch6Amount: 0,
        },
    };

    it('succeeds with serializable isolation', async () => {
        assert.deepEqual(await testBatchClose(tsTasks, { isolationLevel: 'high' }), results.success);
    });

    it('succeeds with serializable isolation and deferrable', async () => {
        assert.deepEqual(
            await testBatchClose(tsTasks, { isolationLevel: 'high', isDeferrable: true }),
            results.successDeferrable,
        );
    });

    it('fails with repeatable read isolation', async () => {
        assert.deepEqual(await testBatchClose(tsTasks, { isolationLevel: 'medium' }), results.failure);
    });

    it('fails with read committed isolation', async () => {
        assert.deepEqual(await testBatchClose(tsTasks, { isolationLevel: 'low' }), results.failure);
    });

    it('succeeds with graphql mutations and queries', async () => {
        assert.deepEqual(await testBatchClose(graphQlTasks(false), { isolationLevel: 'high' }), results.success);
    });

    it('succeeds with graphql mutations and deferrable query', async () => {
        assert.deepEqual(
            await testBatchClose(graphQlTasks(true), { isolationLevel: 'high', isDeferrable: true }),
            results.successDeferrable,
        );
    });

    after(() => restoreTables());
});
