import { asyncArray, AsyncResponse } from '@sage/xtrem-async-helper';
import { assert, expect } from 'chai';
import {
    Collection,
    decorators,
    integer,
    Node,
    NodePayloadData,
    NodePayloadOptions,
    NodeQueryOptions,
    Reference,
    Test,
} from '../../index';
import * as fixtures from '../fixtures';
import { codeDataType, descriptionDataType } from '../fixtures/data-types/data-types';
import {
    createApplicationWithApi,
    documentData,
    documentLineData,
    initTables,
    referredData,
    restoreTables,
    setup,
} from '../fixtures/index';
import { TestDocument, TestDocumentLine, TestReferred } from '../fixtures/nodes';

@decorators.node<TestDocumentRequiredCollection>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    tableName: 'TestDocumentRequiredCollection',
    indexes: [{ orderBy: { code: +1 }, isUnique: true }],
})
export class TestDocumentRequiredCollection extends Node {
    @decorators.stringProperty<TestDocumentRequiredCollection, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestDocumentRequiredCollection, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly description: Promise<string>;

    @decorators.referenceProperty<TestDocumentRequiredCollection, 'mandatoryReference'>({
        isPublished: true,
        isStored: true,
        node: () => fixtures.nodes.TestReferred,
    })
    readonly mandatoryReference: Reference<TestReferred>;

    @decorators.collectionProperty<TestDocumentRequiredCollection, 'lines'>({
        isPublished: true,
        isVital: true,
        node: () => TestDocumentLineRequiredCollection,
        reverseReference: 'document',
        isRequired: true,
    })
    readonly lines: Collection<TestDocumentLineRequiredCollection>;
}

@decorators.node<TestDocumentLineRequiredCollection>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestDocumentLineRequiredCollection',
    isVitalCollectionChild: true,
    indexes: [
        {
            orderBy: {
                document: 1,
                lineNumber: 1,
            },
            isUnique: true,
        },
    ],
})
export class TestDocumentLineRequiredCollection extends Node {
    @decorators.stringProperty<TestDocumentLineRequiredCollection, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly description: Promise<string>;

    @decorators.integerProperty<TestDocumentLineRequiredCollection, 'lineNumber'>({
        isPublished: true,
        isStored: true,
    })
    readonly lineNumber: Promise<integer>;

    @decorators.referenceProperty<TestDocumentLineRequiredCollection, 'optionalReference'>({
        isStored: true,
        isPublished: true,
        node: () => fixtures.nodes.TestReferred,
        isNullable: true,
    })
    readonly optionalReference: Reference<TestReferred | null>;

    @decorators.referenceProperty<TestDocumentLineRequiredCollection, 'document'>({
        isStored: true,
        isPublished: true,
        node: () => TestDocumentRequiredCollection,
        isVitalParent: true,
    })
    readonly document: Reference<TestDocumentRequiredCollection>;
}

@decorators.node<TestDocumentRequiredCollectionNonVital>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    tableName: 'TestDocumentRequiredCollectionNonVital',
    indexes: [{ orderBy: { code: +1 }, isUnique: true }],
})
export class TestDocumentRequiredCollectionNonVital extends Node {
    @decorators.stringProperty<TestDocumentRequiredCollectionNonVital, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestDocumentRequiredCollectionNonVital, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly description: Promise<string>;

    @decorators.referenceProperty<TestDocumentRequiredCollectionNonVital, 'mandatoryReference'>({
        isPublished: true,
        isStored: true,
        node: () => fixtures.nodes.TestReferred,
    })
    readonly mandatoryReference: Reference<TestReferred>;

    @decorators.collectionProperty<TestDocumentRequiredCollectionNonVital, 'lines'>({
        isPublished: true,
        isVital: false,
        node: () => TestDocumentLineRequiredCollection,
        reverseReference: 'document',
        isRequired: true,
    })
    readonly lines: Collection<TestDocumentLineRequiredCollection>;
}

/** DECLARE SOME TYPES */
@decorators.node<TestInterningParent>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestInterningParent',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
class TestInterningParent extends Node {
    @decorators.stringProperty<TestInterningParent, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestInterningParent, 'text'>({
        dataType: () => descriptionDataType,
        isStored: true,
    })
    readonly text: Promise<string>;

    @decorators.collectionProperty<TestInterningParent, 'children'>({
        isPublished: true,
        isVital: true,
        node: () => TestInterningChild,
        reverseReference: 'parentId',
    })
    readonly children: Collection<TestInterningChild>;
}

@decorators.node<TestInterningChild>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestInterningChild',
    isVitalCollectionChild: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
class TestInterningChild extends Node {
    @decorators.stringProperty<TestInterningChild, 'code'>({
        dataType: () => codeDataType,
        isStored: true,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestInterningChild, 'parentId'>({
        isPublished: true,
        isStored: true,
        isVitalParent: true,
        node: () => TestInterningParent,
    })
    readonly parentId: Reference<TestInterningParent>;

    @decorators.stringProperty<TestInterningChild, 'text'>({
        dataType: () => descriptionDataType,
        isStored: true,
        async defaultValue() {
            // Invoking parent.text ensures that the parent is correctly interned when
            // the child is created
            return (await this.parentId).text;
        },
    })
    readonly text: Promise<string>;
}

async function testRead(i: number, withBackPointer: boolean): Promise<void> {
    await Test.readonly(async context => {
        const document = await context.read(fixtures.nodes.TestDocument, { code: documentData[i].code });
        assert.instanceOf(document, fixtures.nodes.TestDocument);
        assert.equal(await document.code, documentData[i].code);
        const lineData = documentLineData.filter(line => line.document === document._id);
        const testLines = (lines: Collection<TestDocumentLine>) =>
            lines.forEach(async (line, j) => {
                assert.instanceOf(line, fixtures.nodes.TestDocumentLine);
                assert.equal(await line.lineNumber, lineData[j].lineNumber);
                assert.equal(await line.description, lineData[j].description);
                if (withBackPointer) {
                    assert.isObject(await line.document);
                    assert.equal(await (await line.document).code, await document.code);
                }
            });
        await testLines(document.lines);
    });
}

describe('collection', () => {
    before(async () =>
        setup({
            application: await createApplicationWithApi({
                nodes: {
                    TestReferred,
                    TestDocument,
                    TestDocumentLine,
                    TestDocumentRequiredCollection,
                    TestDocumentLineRequiredCollection,
                    TestInterningParent,
                    TestInterningChild,
                },
            }),
        }),
    );
    beforeEach(() =>
        initTables([
            { nodeConstructor: fixtures.nodes.TestReferred, data: referredData },
            { nodeConstructor: fixtures.nodes.TestDocument, data: documentData },
            { nodeConstructor: fixtures.nodes.TestDocumentLine, data: documentLineData },
            { nodeConstructor: TestDocumentRequiredCollection, data: documentData },
            { nodeConstructor: TestDocumentLineRequiredCollection, data: documentLineData },
            { nodeConstructor: TestInterningParent, data: [] },
            { nodeConstructor: TestInterningChild, data: [] },
        ]),
    );
    it('create a parent with children', () =>
        Test.uncommitted(async context => {
            const node = await context.create(TestInterningParent, {
                code: 'PARENT1',
                text: 'ParentText',
                children: [
                    {
                        code: 'CHILD1',
                    },
                ],
            });

            await node.$.save();
            const readResult = await context.tryRead(TestInterningParent, { code: 'PARENT1' });
            assert.isNotNull(readResult);
        }));
    it('read collection without back pointer', async () => {
        await testRead(0, false);
        await testRead(1, false);
    });
    it('read collection with back pointer', async () => {
        await testRead(0, true);
        await testRead(1, true);
    });
    it('create document with lines', async () => {
        async function verifyDocument(document: TestDocument): Promise<void> {
            assert.instanceOf(document, fixtures.nodes.TestDocument);
            assert.equal(await document.code, 'DOCZ');
            assert.equal(await document.description, 'document Z');
            const lines = await document.lines.toArray();
            assert.equal(lines.length, 2);
            await asyncArray(lines).forEach(async (line, i) => {
                assert.equal(await line.lineNumber, i + 1);
                assert.equal(await line.description, `line Z${i + 1}`);
                assert.isObject(await line.document);
                assert.equal(await (await line.document).code, await document.code);
                // reading description checks that document has been properly interned
                assert.equal(await (await line.document).description, await document.description);
            });
        }
        await Test.committed(async context => {
            const document = await context.create(fixtures.nodes.TestDocument, {
                code: 'DOCZ',
                description: 'document Z',
                mandatoryReference: referredData[0]._id,
                lines: [
                    {
                        lineNumber: 1,
                        description: 'line Z1',
                    },
                    {
                        lineNumber: 2,
                        description: 'line Z2',
                    },
                ],
            });
            await document.$.save();
        });
        await Test.readonly(async context => {
            const doc = await context.read(fixtures.nodes.TestDocument, { code: 'DOCZ' });
            await verifyDocument(doc);
        });
    });
    it('can create collection with insert, append and delete', () =>
        Test.uncommitted(async context => {
            const document = await context.create(fixtures.nodes.TestDocument, {
                code: 'DOCZ',
                description: 'document Z',
            });
            await document.lines.append({
                lineNumber: 1,
                description: 'line Z1',
            });
            await document.lines.append({
                lineNumber: 3,
                description: 'line Z3',
            });
            await document.lines.insert(1, {
                lineNumber: 5,
                description: 'line Z2a',
            });
            await document.lines.insert(1, {
                lineNumber: 2,
                description: 'line Z2',
            });
            await document.lines.delete(2);

            // now read it with length / item API
            assert.equal(await document.lines.length, 3);
            for (let i = 0; i < 3; i += 1) {
                const line = await document.lines.elementAt(i);
                assert.equal(await line.lineNumber, i + 1);
                assert.equal(await line.description, `line Z${i + 1}`);
            }
        }));

    async function testQuery(options: NodeQueryOptions<TestDocumentLine>, lineNumbers: number[]): Promise<void> {
        await Test.readonly(async context => {
            const document = await context.read(fixtures.nodes.TestDocument, { code: 'DOCC' });
            const nums = await document.lines
                .query(options)
                .map(line => line.lineNumber)
                .toArray();
            assert.deepEqual(nums, lineNumbers);
        });
    }

    async function testFind(
        condition: (line: TestDocumentLine) => AsyncResponse<boolean>,
        expected: string | null,
    ): Promise<void> {
        await Test.withContext(async context => {
            const document = await context.read(fixtures.nodes.TestDocument, { code: 'DOCC' });
            const line = await document.lines.find(condition);
            assert.equal(line ? await line.description : line, expected);
        });
    }

    it('can limit collection page size', async () => {
        await testQuery({ first: 2 }, [1, 2]);
        await testQuery({ last: 2 }, [4, 5]);
    });

    it('can filter collection query', () => testQuery({ filter: { description: 'y' } }, [2, 4, 5]));

    it('can find an element in a collection', async () => {
        await testFind(async line => (await line.description) === 'y', 'y');
        await testFind(async line => (await line.description) === 'bad', null);
    });

    it('can control order', async () => {
        await testQuery({ orderBy: { description: -1, lineNumber: 1 } }, [3, 2, 4, 5, 1]);
        await testQuery({ orderBy: { description: -1, lineNumber: -1 } }, [3, 5, 4, 2, 1]);
    });

    it('Should be able to access to old collection value after update', () =>
        Test.uncommitted(async context => {
            const document = await context.read(fixtures.nodes.TestDocument, { code: 'DOCB' }, { forUpdate: true });
            expect(await (await document.lines.elementAt(0)).description).to.eq('line B 1');
            expect(await (await (await document.lines.elementAt(0)).$.old).description).to.eq('line B 1');
            await (await document.lines.elementAt(0)).$.set({ description: 'line-change Z1' });
            expect(await (await document.lines.elementAt(0)).description).to.eq('line-change Z1');
            expect(await (await (await document.lines.elementAt(0)).$.old).description).to.eq('line B 1');

            const node = await (await document.lines.elementAt(0)).$.payload();
            const oldNode = await (await (await document.$.old).lines.elementAt(0)).$.payload();
            expect(node).to.not.deep.equal(oldNode);

            const mappedOldNodes = await document.lines.map(val => val.$.old).toArray();

            await document.lines.append({
                description: 'line-append',
                lineNumber: 12,
            });
            const oldNodes = await (await document.$.old).lines.toArray();

            await asyncArray(mappedOldNodes).forEach(async (mOldNode, index) =>
                assert.deepEqual(await mOldNode.$.payload(), await oldNodes[index].$.payload()),
            );
            expect(oldNodes.length).to.eq(mappedOldNodes.length);
            await asyncArray(mappedOldNodes).forEach(async (line, i) => {
                expect(await line.description).to.eq(await oldNodes[i].description);
                expect(await mappedOldNodes[0].description).to.eq(await oldNodes[0].description);
            });
        }));

    it('Check of payload propertyNames', () =>
        Test.uncommitted(async context => {
            const documentDOCB = await context.read(fixtures.nodes.TestDocument, { code: 'DOCB' }, { forUpdate: true });
            const payload = await documentDOCB.$.payload({
                propertyNames: {
                    code: true,
                    description: true,
                    lines: { lineNumber: true },
                },
            } as unknown as NodePayloadOptions<TestDocument>);
            assert.deepEqual(payload, {
                code: 'DOCB',
                description: 'document B',
                lines: [{ lineNumber: 1 }, { lineNumber: 2 }],
            });
        }));

    it('Check of payload propertyNames with optional reference', () =>
        Test.uncommitted(async context => {
            const documentDOCC = await context.read(fixtures.nodes.TestDocument, { code: 'DOCC' }, { forUpdate: true });
            const payload = await documentDOCC.$.payload({
                propertyNames: {
                    code: true,
                    description: true,
                    optionalReference: true,
                    lines: { lineNumber: true, optionalReference: true },
                },
            } as unknown as NodePayloadOptions<TestDocument>);
            assert.deepEqual(payload, {
                code: 'DOCC',
                description: 'document C',
                lines: [
                    { lineNumber: 1, optionalReference: null },
                    { lineNumber: 2, optionalReference: null },
                    { lineNumber: 3, optionalReference: null },
                    { lineNumber: 4, optionalReference: null },
                    { lineNumber: 5, optionalReference: null },
                ],
            });
        }));

    it('can update a collection', async () => {
        let expectedUpdatedLines: NodePayloadData<TestDocumentLine>[] = [];
        await Test.committed(async context => {
            const document = await context.read(fixtures.nodes.TestDocument, { code: 'DOCC' }, { forUpdate: true });
            assert.deepEqual(await document.lines.map(line => line.lineNumber).toArray(), [1, 2, 3, 4, 5]);
            const lineId = (await document.lines.elementAt(1))._id;
            expectedUpdatedLines = [
                {
                    _id: lineId,
                    _sourceId: '',
                    _sortValue: 20,
                    document: {
                        _id: 3,
                    },
                    lineNumber: 2,
                    description: 'y', //  we are providing _id for this line, so wi will reuse the already created line
                    getDescription: 'y', //  we are providing _id for this line, so wi will reuse the already created line
                    optionalReference: null,
                    _customData: {},
                },
                {
                    _id: -1000000001, // Will be a transient id
                    _sourceId: '',
                    _sortValue: 30,
                    document: {
                        _id: 3,
                    },
                    lineNumber: 6,
                    description: 'created by update',
                    getDescription: 'created by update',
                    optionalReference: null,
                    _customData: {},
                },
            ];

            await document.$.set({
                lines: [
                    {
                        _id: lineId,
                        lineNumber: 2,
                    },
                    {
                        lineNumber: 6,
                        description: 'created by update',
                    },
                ],
            });
            assert.deepEqual(
                (await document.$.payload({ withIds: true, withLayer: true })).lines,
                expectedUpdatedLines,
            );
            await document.$.save();
            // The document has been saved, the transient _id of its second line has been updated by the db engine
            expectedUpdatedLines[1]._id = (await document.lines.elementAt(1))._id;
        });
        await Test.readonly(async context => {
            const updatedNode = await context.read(fixtures.nodes.TestDocument, { code: 'DOCC' });
            assert.deepEqual(
                (await updatedNode.$.payload({ withIds: true, withLayer: true })).lines,
                expectedUpdatedLines,
            );
        });
    });

    it("cannot create with empty collection when it's required", () =>
        Test.uncommitted(async context => {
            const document = await context.create(TestDocumentRequiredCollection, {
                code: 'DOCZ',
                description: 'document Z',
                mandatoryReference: referredData[0]._id,
                lines: [],
            });
            await assert.isRejected(document.$.save(), /was not created/);
            assert.deepEqual(context.diagnoses, [
                {
                    severity: 3,
                    path: ['lines'],
                    message: 'required data collection cannot be empty',
                },
            ]);
        }));

    it("can create with full collection when it's required", async () => {
        async function verifyDocument(document: TestDocumentRequiredCollection): Promise<void> {
            assert.instanceOf(document, TestDocumentRequiredCollection);
            assert.equal(await document.code, 'DOCZ');
            assert.equal(await document.description, 'document Z');
            const lines = await document.lines.toArray();
            assert.equal(lines.length, 2);
            await asyncArray(lines).forEach(async (line, i) => {
                assert.equal(await line.lineNumber, i + 1);
                assert.equal(await line.description, `line Z${i + 1}`);
                assert.isObject(await line.document);
                assert.equal(await (await line.document).code, await document.code);
                // reading description checks that document has been properly interned
                assert.equal(await (await line.document).description, await document.description);
            });
        }
        await Test.committed(async context => {
            const document = await context.create(TestDocumentRequiredCollection, {
                code: 'DOCZ',
                description: 'document Z',
                mandatoryReference: referredData[0]._id,
                lines: [
                    {
                        lineNumber: 1,
                        description: 'line Z1',
                    },
                    {
                        lineNumber: 2,
                        description: 'line Z2',
                    },
                ],
            });
            await document.$.save();
        });
        await Test.readonly(async context => {
            const doc = await context.read(TestDocumentRequiredCollection, { code: 'DOCZ' });
            await verifyDocument(doc);
        });
    });

    it("cannot assign create with full collection when it's required", async () => {
        await assert.isRejected(
            createApplicationWithApi({
                nodes: {
                    TestReferred,
                    TestDocument,
                    TestDocumentLine,
                    TestDocumentRequiredCollection,
                    TestDocumentLineRequiredCollection,
                    TestInterningParent,
                    TestInterningChild,
                    TestDocumentRequiredCollectionNonVital,
                },
            }),
            'TestDocumentRequiredCollectionNonVital.lines: Non vital collection cannot be required.',
        );
    });

    it('$.old is valid when deleting a line', async () => {
        await Test.uncommitted(async context => {
            const document = await context.read(
                fixtures.nodes.TestDocument,
                { code: 'TEST_DELETE_LINE' },
                { forUpdate: true },
            );
            await document.lines.delete(1, 1);
            await document.$.save();
            // Note: nothing is checked here. The check is done in TestDocument.SaveBegin
        });
    });
    it('$.old is valid when setting the node data', async () => {
        await Test.uncommitted(async context => {
            const document = await context.read(
                fixtures.nodes.TestDocument,
                { code: 'TEST_DELETE_LINE' },
                { forUpdate: true },
            );
            // Here we set the data, but a line is deleted compared to the db content (see documentData)
            await document.$.set({
                lines: [
                    {
                        _id: 13,
                        _sortValue: 10,
                        document: 6,
                        lineNumber: 1,
                        description: 'remaining line #1',
                    },
                    {
                        _id: 14,
                        _sortValue: 30,
                        document: 6,
                        lineNumber: 3,
                        description: 'remaining line #2',
                    },
                ],
            });
            await document.$.save();
            // Note: nothing is checked here. The check is done in TestDocument.SaveBegin
        });
    });

    after(() => restoreTables());
});
