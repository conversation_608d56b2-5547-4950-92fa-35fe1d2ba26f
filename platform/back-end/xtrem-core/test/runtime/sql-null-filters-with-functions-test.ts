import {} from '@sage/xtrem-date-time';
import { assert } from 'chai';
import { date, datetime, decorators, Node, Reference, Test } from '../../index';
import { codeDataType, descriptionDataType } from '../fixtures/data-types/data-types';
import { createApplicationWithApi, initTables, restoreTables, setup } from '../fixtures/index';

/** DECLARE SOME TYPES */
@decorators.node<TestCustomerNullFilterFn>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestCustomerNullFilterFn',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
class TestCustomerNullFilterFn extends Node {
    @decorators.stringProperty<TestCustomerNullFilterFn, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestCustomerNullFilterFn, 'name'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly name: Promise<string>;

    @decorators.dateProperty<TestCustomerNullFilterFn, 'date'>({
        isPublished: true,
        isNullable: true,
        isStored: true,
    })
    readonly date: Promise<date | null>;

    @decorators.datetimeProperty<TestCustomerNullFilterFn, 'dateTime'>({
        isPublished: true,
        isNullable: true,
        isStored: true,
    })
    readonly dateTime: Promise<datetime | null>;

    @decorators.referenceProperty<TestCustomerNullFilterFn, 'address'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        node: () => TestAddressNullFilterFn,
    })
    readonly address: Reference<TestAddressNullFilterFn | null>;
}

@decorators.node<TestAddressNullFilterFn>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestAddressNullFilterFn',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
class TestAddressNullFilterFn extends Node {
    @decorators.stringProperty<TestAddressNullFilterFn, 'code'>({
        dataType: () => codeDataType,
        isStored: true,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestAddressNullFilterFn, 'text'>({
        dataType: () => descriptionDataType,
        isStored: true,
    })
    readonly text: Promise<string>;
}

/** DECLARE SOME DATA */
const customerData = [
    {
        _id: 1,
        code: 'CUST1',
        date: date.today(),
        dateTime: datetime.now(),
        address: 1,
    },
    {
        _id: 2,
        code: 'CUST2',
        date: null,
        dateTime: null,
    },
    {
        _id: 3,
        code: 'CUST3',
        date: null,
        dateTime: null,
        address: 2,
    },
];

const addressData = [
    {
        _id: 1,
        code: 'ADDR1',
        text: 'Lacanau',
    },
    {
        _id: 2,
        code: 'ADDR2',
        text: 'Le Porge',
    },
];

describe('sql null-filters tests', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi({
                nodes: { TestAddressNullFilterFn, TestCustomerNullFilterFn },
            }),
        });
        await initTables([
            { nodeConstructor: TestAddressNullFilterFn, data: addressData },
            { nodeConstructor: TestCustomerNullFilterFn, data: customerData },
        ]);
    });
    it('query with null filter : date + _eq', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestCustomerNullFilterFn, {
                    async filter() {
                        return (await this.date) == null;
                    },
                })
                .map(r => r.code)
                .toArray();
            assert.equal(result.join('/'), 'CUST2/CUST3');
        }));
    it('query with null filter : date + _ne', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestCustomerNullFilterFn, {
                    async filter() {
                        return (await this.date) != null;
                    },
                })
                .toArray();
            assert.equal(result.length, 1);
            assert.equal(await result[0].code, 'CUST1');
        }));
    it('query with null filter : datetime + _eq', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestCustomerNullFilterFn, {
                    async filter() {
                        return (await this.dateTime) == null;
                    },
                })
                .map(r => r.code)
                .toArray();
            assert.equal(result.join('/'), 'CUST2/CUST3');
        }));
    it('query with null filter : datetime + _ne', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestCustomerNullFilterFn, {
                    async filter() {
                        return (await this.dateTime) != null;
                    },
                })
                .toArray();
            assert.equal(result.length, 1);
            assert.equal(await result[0].code, 'CUST1');
        }));
    it('query with null filter : reference + _eq', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestCustomerNullFilterFn, {
                    async filter() {
                        return (await this.address) == null;
                    },
                })
                .toArray();
            assert.equal(result.length, 1);
            assert.equal(await result[0].code, 'CUST2');
        }));
    it('query with null filter : reference + _ne null', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestCustomerNullFilterFn, {
                    async filter() {
                        return (await this.address) != null;
                    },
                })
                .map(r => r.code)
                .toArray();
            assert.equal(result.join('/'), 'CUST1/CUST3');
        }));
    it('query with null filter : reference + _ne', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestCustomerNullFilterFn, {
                    async filter() {
                        return (await this.address) == null || (await this.address)!._id !== 1;
                    },
                })
                .map(r => r.code)
                .toArray();
            assert.equal(result.join('/'), 'CUST2/CUST3');
        }));
    it('complex query with null filter : reference + _gt', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestCustomerNullFilterFn, {
                    async filter() {
                        return (await this.address) == null || (await this.address)!._id > 1;
                    },
                })
                .map(r => r.code)
                .toArray();
            assert.equal(result.join('/'), 'CUST2/CUST3');
        }));
    after(() => restoreTables());
});
