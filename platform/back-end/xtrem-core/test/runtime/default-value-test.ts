import { assert } from 'chai';
import { fixtures, testDefaultValueApplication } from '..';
import { Test } from '../../index';
import { initTables, restoreTables, setup } from '../fixtures/index';
import { TestDefaultValuesReference } from '../fixtures/nodes';

const { TestDefaultValue } = fixtures.nodes;

describe('Default values', () => {
    before(async () => {
        await setup({ application: await testDefaultValueApplication.application });
        await initTables([
            {
                nodeConstructor: TestDefaultValuesReference,
                data: [
                    {
                        _id: 1,
                        code: 'foo',
                    },
                    {
                        _id: 2,
                        code: 'too',
                    },
                ],
            },
            {
                nodeConstructor: TestDefaultValue,
                data: [
                    {
                        _id: 1,
                        booleanNoDefault: true,
                        booleanDefaultTrueLiteral: false,
                        booleanDefaultTrueFunction: false,
                        stringDefaultFunction: 'false',
                        referenceNullDefault: 1,
                    },
                ],
            },
        ]);
    });
    it('gets correct default boolean values', () =>
        Test.uncommitted(async context => {
            const node = await context.create(TestDefaultValue, {});
            assert.isFalse(await node.booleanNoDefault);
            assert.isTrue(await node.booleanDefaultTrueLiteral);
            assert.isTrue(await node.booleanDefaultTrueFunction);
            await assert.isRejected(
                node.booleanNullDefault,
                'TestDefaultValue.booleanNullDefault: property is required',
            );
            await assert.isRejected(node.dateNullDefault, 'TestDefaultValue.dateNullDefault: property is required');
            await assert.isRejected(
                node.referenceNullDefault,
                'TestDefaultValue.referenceNullDefault: property is required',
            );
            await assert.isRejected(node.$.save(), /was not created/);
            assert.isTrue(context.diagnoses.length === 1);
            assert.deepEqual(context.diagnoses[0], {
                severity: 4,
                path: ['booleanNullDefault'],
                message: 'TestDefaultValue.booleanNullDefault: property is required',
            });
        }));
    it('correctly updates boolean properties with default values', async () => {
        // toggle values
        await Test.committed(async context => {
            const node = await context.read(TestDefaultValue, { _id: 1 }, { forUpdate: true });
            assert.isTrue(await node.booleanNoDefault);
            assert.isFalse(await node.booleanDefaultTrueLiteral);
            assert.isFalse(await node.booleanDefaultTrueFunction);
            await node.$.set({ booleanNoDefault: false });
            await node.$.set({ booleanDefaultTrueLiteral: true });
            await node.$.set({ booleanDefaultTrueFunction: true });
            await node.$.save();
        });
        await Test.readonly(async context => {
            const node = await context.read(TestDefaultValue, { _id: 1 });
            assert.isFalse(await node.booleanNoDefault);
            assert.isTrue(await node.booleanDefaultTrueLiteral);
            assert.isTrue(await node.booleanDefaultTrueFunction);
        });
        // toggle again
        await Test.committed(async context => {
            const node = await context.read(TestDefaultValue, { _id: 1 }, { forUpdate: true });
            await node.$.set({ booleanNoDefault: true });
            await node.$.set({ booleanDefaultTrueLiteral: false });
            await node.$.set({ booleanDefaultTrueFunction: false });
            await node.$.save();
        });
        await Test.readonly(async context => {
            const node = await context.read(TestDefaultValue, { _id: 1 });
            assert.isTrue(await node.booleanNoDefault);
            assert.isFalse(await node.booleanDefaultTrueLiteral);
            assert.isFalse(await node.booleanDefaultTrueFunction);
        });
    });

    it('Default value function should not trigger when a null value is provided', async () => {
        // 1. check that the default value of referenceNotNullDefault is not null:
        await Test.uncommitted(async context => {
            const node = await context.create(TestDefaultValue, {});
            assert.isNotNull(await node.referenceNotNullDefault);
        });

        // 2. check that it is possible to set referenceNotNullDefault with a null reference
        await Test.uncommitted(async context => {
            const node = await context.create(TestDefaultValue, { referenceNotNullDefault: null });
            assert.isNull(await node.referenceNotNullDefault);
        });
    });
    after(() => restoreTables());
});
