import { assert } from 'chai';
import { initTables, SqlConverter, Test } from '../../index';
import { tenantCondition } from '../../lib/runtime';
import { createApplicationWithApi, datatypesData, restoreTables, setup } from '../fixtures/index';
import { TestDatatypes } from '../fixtures/nodes';

describe('Context tenant tests', () => {
    before(async () => {
        await setup({ application: await createApplicationWithApi({ nodes: { TestDatatypes } }) });
        await initTables([{ nodeConstructor: TestDatatypes, data: datatypesData }]);
    });

    after(() => restoreTables());

    describe('unsafeApplyToAllTenants context option', () => {
        it('can create a context without unsafeApplyToAllTenants and the tenant join condition is applied', () =>
            Test.application.withReadonlyContext(Test.defaultTenantId, async context => {
                const factory = Test.application.getFactoryByConstructor(TestDatatypes);
                const sqlConverter = new SqlConverter(context, factory);
                const condition = tenantCondition(sqlConverter, factory.table, 't0', []);
                assert.equal(condition, 't0._tenant_id = $1');
                const parameterValues = await SqlConverter.getParameterValues(context, sqlConverter.sqlParameters, {});
                assert.deepEqual(parameterValues, ['777777777777777777777']);
            }));

        it('can create a context with unsafeApplyToAllTenants as true and then tenant join condition is not applied', () =>
            Test.application.withReadonlyContext(
                null,
                async context => {
                    const factory = Test.application.getFactoryByConstructor(TestDatatypes);
                    const sqlConverter = new SqlConverter(context, factory);
                    const condition = tenantCondition(sqlConverter, factory.table, 't0', []);
                    assert.equal(condition, '1=1');
                    const parameterValues = await SqlConverter.getParameterValues(
                        context,
                        sqlConverter.sqlParameters,
                        {},
                    );
                    assert.deepEqual(parameterValues, []);
                },
                { unsafeApplyToAllTenants: true },
            ));

        it('will throw an error is unsafeApplyToAllTenants is true and tenantId is supplied', async () => {
            await assert.isRejected(
                Test.application.withReadonlyContext(
                    Test.defaultTenantId,
                    context => {
                        const factory = Test.application.getFactoryByConstructor(TestDatatypes);
                        const sqlConverter = new SqlConverter(context, factory);
                        tenantCondition(sqlConverter, factory.table, 't0', []);
                    },
                    { unsafeApplyToAllTenants: true },
                ),
                'Cannot create a context where unsafeApplyToAllTenants is true and a tenantId is supplied',
            );
        });

        it('will throw an error is unsafeApplyToAllTenants is true and setTenantId is called', () =>
            Test.application.withReadonlyContext(
                null,
                async context => {
                    await assert.isRejected(
                        context.setTenantId(Test.defaultTenantId),
                        'Cannot set a tenantId when unsafeApplyToAllTenants is true',
                    );
                },
                { unsafeApplyToAllTenants: true },
            ));
    });
});
