import { assert } from 'chai';
import { Collection, decorators, integer, Node, Reference } from '../../index';
import { codeDataType } from '../fixtures/data-types/data-types';
import { createApplicationWithApi, restoreTables } from '../fixtures/index';

@decorators.node<TestVitalChildWithIsClearedByReset>({
    isPublished: true,
    storage: 'sql',
    isVitalCollectionChild: true,
    isClearedByReset: true,
    indexes: [
        {
            orderBy: {
                document: 1,
            },
            isUnique: true,
        },
    ],
})
export class TestVitalChildWithIsClearedByReset extends Node {
    @decorators.referenceProperty<TestVitalChildWithIsClearedByReset, 'document'>({
        isStored: true,
        isPublished: true,
        node: () => TestVitalChildWithIsClearedByResetCollection,
        isVitalParent: true,
    })
    readonly document: Reference<TestVitalChildWithIsClearedByResetCollection>;
}

@decorators.node<TestVitalChildWithIsClearedByResetCollection>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
})
export class TestVitalChildWithIsClearedByResetCollection extends Node {
    @decorators.collectionProperty<TestVitalChildWithIsClearedByResetCollection, 'lines'>({
        isPublished: true,
        isVital: true,
        node: () => TestVitalChildWithIsClearedByReset,
        reverseReference: 'document',
        isRequired: true,
    })
    readonly lines: Collection<TestVitalChildWithIsClearedByReset>;
}

@decorators.node<TestNodeWithIsClearedByResetInReferenceNode>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
})
export class TestNodeWithIsClearedByResetInReferenceNode extends Node {
    @decorators.referenceProperty<TestNodeWithIsClearedByResetInReferenceNode, 'isClearedByResetReference'>({
        isPublished: true,
        isStored: true,
        isClearedByReset: true,
        isNullable: true,
        node: () => TestNodeWithIsClearedByResetInReferenceNodeTarget,
    })
    readonly isClearedByResetReference: Reference<TestNodeWithIsClearedByResetInReferenceNodeTarget | null>;
}

@decorators.node<TestNodeWithIsClearedByResetInReferenceNodeTarget>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    isClearedByReset: true,
})
export class TestNodeWithIsClearedByResetInReferenceNodeTarget extends Node {
    @decorators.stringProperty<TestNodeWithIsClearedByResetInReferenceNodeTarget, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;
}

@decorators.node<TestNodeWithIsClearedByResetInDecoratorAndProperty>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    isClearedByReset: true,
})
export class TestNodeWithIsClearedByResetInDecoratorAndProperty extends Node {
    @decorators.stringProperty<TestNodeWithIsClearedByResetInDecoratorAndProperty, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
        isClearedByReset: true,
    })
    readonly code: Promise<string>;
}

@decorators.node<TestSubNodeInParent>({
    isPublished: true,
    storage: 'sql',
    isClearedByReset: true,
    isAbstract: true,
})
export class TestSubNodeInParent extends Node {
    @decorators.stringProperty<TestSubNodeInParent, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;
}

@decorators.subNode<TestSubNode>({
    extends: () => TestSubNodeInParent,
    isPublished: true,
    isClearedByReset: true,
})
export class TestSubNode extends TestSubNodeInParent {
    @decorators.stringProperty<TestSubNode, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly description: Promise<string>;
}

@decorators.node<TestSetupNode>({
    isPublished: true,
    isClearedByReset: true,
    storage: 'sql',
    isSetupNode: true,
    indexes: [{ orderBy: { description: 1 }, isUnique: true, isNaturalKey: true }],
})
export class TestSetupNode extends Node {
    @decorators.stringProperty<TestSetupNode, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
        isFrozen: true,
    })
    readonly description: Promise<string>;
}

@decorators.node<TestNonStoredProperty>({
    isPublished: true,
    storage: 'sql',
})
export class TestNonStoredProperty extends Node {
    @decorators.stringProperty<TestNonStoredProperty, 'description'>({
        isPublished: true,
        isTransientInput: true,
        isClearedByReset: true,
        dataType: () => codeDataType,
    })
    readonly description: Promise<string>;
}

@decorators.node<TestOnUniqueIndex>({
    isPublished: true,
    storage: 'sql',
    indexes: [{ orderBy: { description: 1 }, isUnique: true }],
})
export class TestOnUniqueIndex extends Node {
    @decorators.stringProperty<TestOnUniqueIndex, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
        isClearedByReset: true,
    })
    readonly description: Promise<string>;
}

@decorators.node<TestOnDefaultValueFunction>({
    isPublished: true,
    storage: 'sql',
})
export class TestOnDefaultValueFunction extends Node {
    @decorators.stringProperty<TestOnDefaultValueFunction, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
        isClearedByReset: true,
        defaultValue() {
            return 'will not pass';
        },
    })
    readonly description: Promise<string>;
}

// check flagged factory consistency
@decorators.node<TestIsClearedByResetNodeA>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    isClearedByReset: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestIsClearedByResetNodeA extends Node {
    @decorators.integerProperty<TestIsClearedByResetNodeA, 'code'>({
        isPublished: true,
        isStored: true,
    })
    readonly code: Promise<integer>;
}

@decorators.node<TestIsClearedByResetNodeB>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [{ orderBy: { codeRefA: 1 }, isUnique: true }],
})
export class TestIsClearedByResetNodeB extends Node {
    @decorators.referenceProperty<TestIsClearedByResetNodeB, 'codeRefA'>({
        isPublished: true,
        isStored: true,
        node: () => TestIsClearedByResetNodeA,
    })
    readonly codeRefA: Reference<TestIsClearedByResetNodeA>;
}

// shared by alltenant
@decorators.node<TestIsClearedByResetSharedNode>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    isClearedByReset: true,
    isSharedByAllTenants: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestIsClearedByResetSharedNode extends Node {
    @decorators.integerProperty<TestIsClearedByResetSharedNode, 'code'>({
        isPublished: true,
        isStored: true,
    })
    readonly code: Promise<integer>;
}

describe('isClearByReset tag', () => {
    before(() => {});
    beforeEach(() => {});
    it('isClearByReset can not be set in vital child', async () => {
        await assert.isRejected(
            createApplicationWithApi({
                nodes: {
                    TestVitalChildWithIsClearedByReset,
                    TestVitalChildWithIsClearedByResetCollection,
                },
            }),
            'TestVitalChildWithIsClearedByReset: isClearedByReset can only be tagged in parent node',
        );
    });

    it('A property can only be tagged isClearedByReset if its target node is not tagged', async () => {
        await assert.isRejected(
            createApplicationWithApi({
                nodes: {
                    TestNodeWithIsClearedByResetInReferenceNodeTarget,
                    TestNodeWithIsClearedByResetInReferenceNode,
                },
            }),
            'TestNodeWithIsClearedByResetInReferenceNode.isClearedByResetReference: A property can only be tagged isClearedByReset if its target node is not tagged',
        );
    });

    it('isClearedByReset tag should not be declared on the property if it is already declared in the node decorator', async () => {
        await assert.isRejected(
            createApplicationWithApi({
                nodes: {
                    TestNodeWithIsClearedByResetInDecoratorAndProperty,
                },
            }),
            'TestNodeWithIsClearedByResetInDecoratorAndProperty.code: isClearedByReset tag is already declared in the node decorator',
        );
    });

    it('isClearByReset can not be set in both subnode and its parent', async () => {
        await assert.isRejected(
            createApplicationWithApi({
                nodes: {
                    TestSubNodeInParent,
                    TestSubNode,
                },
            }),
            'TestSubNodeInParent: A subNode can only be tagged isClearedByReset if its base node is not tagged',
        );
    });

    it('isClearedByReset cannot be set on setup node', async () => {
        await assert.isRejected(
            createApplicationWithApi({
                nodes: {
                    TestSetupNode,
                },
            }),
            'TestSetupNode: isClearedByReset cannot be set on setup node',
        );
    });

    it('isClearedByReset tag can only be set to stored properties', async () => {
        await assert.isRejected(
            createApplicationWithApi({
                nodes: {
                    TestNonStoredProperty,
                },
            }),
            'TestNonStoredProperty.description: isClearedByReset tag can only be set to stored properties',
        );
    });

    it('isClearedByReset tag cannot be set on a unique index', async () => {
        await assert.isRejected(
            createApplicationWithApi({
                nodes: {
                    TestOnUniqueIndex,
                },
            }),
            'TestOnUniqueIndex.description: isClearedByReset tag cannot be set on a unique index',
        );
    });

    it('isClearedByReset tag cannot be set if defaultValue is a function', async () => {
        await assert.isRejected(
            createApplicationWithApi({
                nodes: {
                    TestOnDefaultValueFunction,
                },
            }),
            'TestOnDefaultValueFunction.description: isClearedByReset tag cannot be set if defaultValue is a function',
        );
    });

    it('isClearedByReset cannot be set on shared by all tenants node', async () => {
        await assert.isRejected(
            createApplicationWithApi({
                nodes: {
                    TestIsClearedByResetSharedNode,
                },
            }),
            'TestIsClearedByResetSharedNode: isClearedByReset tag cannot be set on a node which is shared by all tenants',
        );
    });

    it('check if isClearedByReset is set', async () => {
        await assert.isRejected(
            createApplicationWithApi({
                nodes: {
                    TestIsClearedByResetNodeA,
                    TestIsClearedByResetNodeB,
                },
            }),
            'TestIsClearedByResetNodeB.codeRefA: target factory TestIsClearedByResetNodeA is flagged by isClearedByReset. The current factory TestIsClearedByResetNodeB should be also be flagged by isClearedByReset',
        );
    });

    after(() => restoreTables());
});
