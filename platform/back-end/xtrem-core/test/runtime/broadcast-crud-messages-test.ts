import { integer, NOTIFICATION_CATEGORY_NODE_MODIFIED } from '@sage/xtrem-shared';
import { assert } from 'chai';
import * as sinon from 'sinon';
import { Collection, Context, decorators, Node, StringDataType, Test } from '../../index';
import { codeDataType } from '../fixtures/data-types/data-types';
import { createApplicationWithApi, initTables, restoreTables, setup } from '../fixtures/index';

@decorators.node<TestCrudBroadcast>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    tableName: 'TestCrudBroadcast',
    indexes: [
        {
            orderBy: {
                name: 1,
            },
            isUnique: true,
        },
    ],
})
class TestCrudBroadcast extends Node {
    @decorators.stringProperty<TestCrudBroadcast, 'name'>({
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<TestCrudBroadcast, 'value'>({
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly value: Promise<string>;

    @decorators.collectionProperty<TestCrudBroadcast, 'lines'>({
        isPublished: true,
        isVital: true,
        node: () => TestCrudBroadcastLine,
        reverseReference: 'parent',
    })
    readonly lines: Collection<TestCrudBroadcastLine>;
}

@decorators.node<TestCrudBroadcastLine>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    tableName: 'TestCrudBroadcastLine',
    isVitalCollectionChild: true,
    indexes: [
        {
            orderBy: {
                lineNumber: 1,
            },
            isUnique: true,
        },
    ],
})
class TestCrudBroadcastLine extends Node {
    @decorators.integerProperty<TestCrudBroadcastLine, 'lineNumber'>({
        isPublished: true,
        isStored: true,
    })
    readonly lineNumber: Promise<integer>;

    @decorators.stringProperty<TestCrudBroadcastLine, 'broadcast'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 50 }),
    })
    readonly broadcast: Promise<string>;

    @decorators.referenceProperty<TestCrudBroadcastLine, 'parent'>({
        isPublished: true,
        isStored: true,
        node: () => TestCrudBroadcast,
        isVitalParent: true,
    })
    readonly parent: Promise<TestCrudBroadcast>;
}

const sandbox = sinon.createSandbox();

describe('broadcastCrudMessages', () => {
    // Spy helpers for testing
    const spyAddDeferredBroadcastMessage = (context: Context): sinon.SinonSpy =>
        sandbox.spy(context as any, 'addDeferredBroadcastMessage');
    const spyBroadcastToAllUsers = (context: Context): sinon.SinonSpy => sandbox.spy(context, 'broadcastToAllUsers');
    const spyBroadcast = (context: Context): sinon.SinonSpy =>
        sandbox.spy(context.application.uiBroadcaster, 'broadcast');

    before(async () => {
        await setup({
            application: await createApplicationWithApi({
                nodes: {
                    TestCrudBroadcast,
                    TestCrudBroadcastLine,
                },
            }),
        });
        await initTables([
            {
                nodeConstructor: TestCrudBroadcast,
                data: [
                    { _id: 1, name: 'Delete Broadcast', value: 'TestCrudBroadcast/delete' },
                    { _id: 2, name: 'Delete Broadcast Line', value: 'TestCrudBroadcastLine/delete' },
                    { _id: 3, name: 'Update Broadcast', value: 'TestCrudBroadcast/create' },
                    { _id: 4, name: 'Bulk Update Broadcast 1', value: 'TestBulkBroadcast/update' },
                    { _id: 5, name: 'Bulk Update Broadcast 2', value: 'TestBulkBroadcast/update' },
                    { _id: 6, name: 'Bulk Update Broadcast 3', value: 'TestBulkBroadcast/update' },
                    { _id: 7, name: 'Bulk Delete Broadcast 1', value: 'TestBulkBroadcast/delete' },
                    { _id: 8, name: 'Bulk Delete Broadcast 2', value: 'TestBulkBroadcast/delete' },
                    { _id: 9, name: 'Bulk Delete Broadcast 3', value: 'TestBulkBroadcast/delete' },
                ],
            },
            {
                nodeConstructor: TestCrudBroadcastLine,
                data: [
                    { lineNumber: 1, broadcast: 'DeleteBroadcast', parent: 1 },
                    { lineNumber: 2, broadcast: 'DeleteBroadcastLine', parent: 2 },
                ],
            },
        ]);
    });

    afterEach(() => {
        sandbox.restore();
    });

    after(() => restoreTables());

    it('should call broadcast on create', () =>
        Test.withContext(async context => {
            const broadcastSpy = spyAddDeferredBroadcastMessage(context);

            const testBroadcastNode = await context.create(TestCrudBroadcast, {
                name: 'Create New Entry',
            });
            await testBroadcastNode.$.save();

            assert.equal(broadcastSpy.callCount, 1);
            assert.equal(
                broadcastSpy.calledWith(
                    context.tenantId!,
                    NOTIFICATION_CATEGORY_NODE_MODIFIED,
                    'TestCrudBroadcast/create',
                ),
                true,
            );

            broadcastSpy.resetHistory();
        }));

    it('should call broadcast two times due to vital child reference on create', () =>
        Test.withContext(async context => {
            const broadcastSpy = spyAddDeferredBroadcastMessage(context);

            const testBroadcastNode = await context.create(TestCrudBroadcast, { name: 'Create New Entry' });
            await testBroadcastNode.$.set({
                lines: [{ lineNumber: 3, broadcast: 'LineBroadcast', parent: testBroadcastNode._id }],
            });

            await testBroadcastNode.$.save();

            assert.equal(broadcastSpy.callCount, 2);
            assert.equal(
                broadcastSpy.calledWith(
                    context.tenantId!,
                    NOTIFICATION_CATEGORY_NODE_MODIFIED,
                    'TestCrudBroadcast/create',
                ),
                true,
            );
            assert.equal(
                broadcastSpy.calledWith(
                    context.tenantId!,
                    NOTIFICATION_CATEGORY_NODE_MODIFIED,
                    'TestCrudBroadcastLine/create',
                ),
                true,
            );

            broadcastSpy.resetHistory();
        }));

    it('should call broadcast on update', () =>
        Test.withContext(async context => {
            const broadcastSpy = spyAddDeferredBroadcastMessage(context);

            const testBroadcast = await context.read(
                TestCrudBroadcast,
                { name: 'Update Broadcast' },
                { forUpdate: true },
            );
            await testBroadcast.$.update({ name: 'New Name' });

            assert.equal(broadcastSpy.callCount, 1);
            assert.equal(
                broadcastSpy.calledWith(
                    context.tenantId!,
                    NOTIFICATION_CATEGORY_NODE_MODIFIED,
                    'TestCrudBroadcast/update',
                ),
                true,
            );

            broadcastSpy.resetHistory();
        }));

    it('should call broadcast two times due to vital child reference on update', () =>
        Test.withContext(async context => {
            const broadcastSpy = spyAddDeferredBroadcastMessage(context);

            const testBroadcast = await context.read(
                TestCrudBroadcast,
                { name: 'Update Broadcast' },
                { forUpdate: true },
            );
            await testBroadcast.$.update({
                name: 'New Name',
                lines: [{ lineNumber: 3, broadcast: 'LineBroadcast' }],
            });

            assert.equal(broadcastSpy.callCount, 2);
            assert.equal(
                broadcastSpy.calledWith(
                    context.tenantId!,
                    NOTIFICATION_CATEGORY_NODE_MODIFIED,
                    'TestCrudBroadcast/update',
                ),
                true,
            );
            assert.equal(
                broadcastSpy.calledWith(
                    context.tenantId!,
                    NOTIFICATION_CATEGORY_NODE_MODIFIED,
                    'TestCrudBroadcastLine/create',
                ),
                true,
            );

            broadcastSpy.resetHistory();
        }));

    it('should call broadcast on bulk update', () =>
        Test.withContext(async context => {
            const broadcastSpy = spyAddDeferredBroadcastMessage(context);

            await context.bulkUpdate(TestCrudBroadcast, {
                set: { value: 'Update value' },
                where: {
                    value: 'TestBulkBroadcast/update',
                },
            });

            assert.equal(broadcastSpy.callCount, 1);
            assert.equal(
                broadcastSpy.calledWith(
                    context.tenantId!,
                    NOTIFICATION_CATEGORY_NODE_MODIFIED,
                    'TestCrudBroadcast/update',
                ),
                true,
            );

            broadcastSpy.resetHistory();
        }));

    it('should call broadcast two times due to vital child reference on delete', () =>
        Test.withContext(async context => {
            const broadcastSpy = spyAddDeferredBroadcastMessage(context);

            const testBroadcast = await context.read(
                TestCrudBroadcast,
                { name: 'Delete Broadcast Line' },
                { forUpdate: true },
            );

            await testBroadcast.$.delete();

            assert.equal(broadcastSpy.callCount, 2);
            assert.equal(
                broadcastSpy.calledWith(
                    context.tenantId!,
                    NOTIFICATION_CATEGORY_NODE_MODIFIED,
                    'TestCrudBroadcast/delete',
                ),
                true,
            );

            assert.equal(
                broadcastSpy.calledWith(
                    context.tenantId!,
                    NOTIFICATION_CATEGORY_NODE_MODIFIED,
                    'TestCrudBroadcastLine/delete',
                ),
                true,
            );

            broadcastSpy.resetHistory();
        }));

    it('should call broadcast on delete', () =>
        Test.withContext(async context => {
            const broadcastSpy = spyAddDeferredBroadcastMessage(context);

            const testBroadcast = await context.read(
                TestCrudBroadcast,
                { name: 'Delete Broadcast' },
                { forUpdate: true },
            );
            await testBroadcast.$.delete();

            assert.equal(broadcastSpy.callCount, 2);
            assert.equal(
                broadcastSpy.calledWith(
                    context.tenantId!,
                    NOTIFICATION_CATEGORY_NODE_MODIFIED,
                    'TestCrudBroadcast/delete',
                ),
                true,
            );

            assert.equal(
                broadcastSpy.calledWith(
                    context.tenantId!,
                    NOTIFICATION_CATEGORY_NODE_MODIFIED,
                    'TestCrudBroadcastLine/delete',
                ),
                true,
            );

            broadcastSpy.resetHistory();
        }));

    it('should call broadcast on bulk delete', () =>
        Test.withContext(async context => {
            const broadcastSpy = spyAddDeferredBroadcastMessage(context);

            await context.bulkDeleteSql(TestCrudBroadcast, {
                where: {
                    value: 'TestBulkBroadcast/delete',
                },
            });

            assert.equal(broadcastSpy.callCount, 1);
            assert.equal(
                broadcastSpy.calledWith(
                    context.tenantId!,
                    NOTIFICATION_CATEGORY_NODE_MODIFIED,
                    'TestCrudBroadcast/delete',
                ),
                true,
            );

            broadcastSpy.resetHistory();
        }));

    it('should call broadcast 2 times once with created and once with update', () =>
        Test.withContext(async context => {
            const broadcastSpy = spyAddDeferredBroadcastMessage(context);

            const testBroadcastCreate = await context.create(TestCrudBroadcast, { name: 'Create New Entry' });
            await testBroadcastCreate.$.save();

            const testBroadcastUpdate = await context.read(
                TestCrudBroadcast,
                { name: 'Update Broadcast' },
                { forUpdate: true },
            );
            await testBroadcastUpdate.$.update({ name: 'New Name' });

            assert.equal(broadcastSpy.callCount, 2);

            broadcastSpy.resetHistory();
        }));

    it('should call broadcast on create with afterCommit set to true', () =>
        Test.withContext(async context => {
            const broadcastToAllUsersSpy = spyBroadcastToAllUsers(context);
            const broadcastSpy = spyAddDeferredBroadcastMessage(context);
            const applicationBroadcastSpy = spyBroadcast(context);

            const testBroadcastNode = await context.create(TestCrudBroadcast, { name: 'Create New Entry' });
            await testBroadcastNode.$.save();

            assert.equal(broadcastToAllUsersSpy.callCount, 1);
            assert.equal(broadcastSpy.callCount, 1);
            assert.equal(applicationBroadcastSpy.callCount, 0);

            assert.equal(
                broadcastToAllUsersSpy.calledWith({
                    category: NOTIFICATION_CATEGORY_NODE_MODIFIED,
                    payload: 'TestCrudBroadcast/create',
                    afterCommit: true,
                }),
                true,
            );
            assert.equal(
                broadcastSpy.calledWith(
                    context.tenantId!,
                    NOTIFICATION_CATEGORY_NODE_MODIFIED,
                    'TestCrudBroadcast/create',
                ),
                true,
            );

            broadcastSpy.resetHistory();
        }));

    it('should call application broadcast when afterCommit is undefined', () =>
        Test.withContext(context => {
            const broadcastToAllUsersSpy = spyBroadcastToAllUsers(context);
            const broadcastSpy = spyAddDeferredBroadcastMessage(context);
            const applicationBroadcastSpy = spyBroadcast(context);

            context.broadcastToAllUsers({ category: 'TestCrudBroadcast/custom_event', payload: 'custom_payload' });

            assert.equal(broadcastToAllUsersSpy.callCount, 1);
            assert.equal(applicationBroadcastSpy.callCount, 1);
            assert.equal(broadcastSpy.callCount, 0);

            assert.equal(
                broadcastToAllUsersSpy.calledWith({
                    category: 'TestCrudBroadcast/custom_event',
                    payload: 'custom_payload',
                }),
                true,
            );
            assert.equal(
                applicationBroadcastSpy.calledWith(
                    context.tenantId!,
                    'TestCrudBroadcast/custom_event',
                    'custom_payload',
                ),
                true,
            );

            broadcastSpy.resetHistory();
        }));
});
