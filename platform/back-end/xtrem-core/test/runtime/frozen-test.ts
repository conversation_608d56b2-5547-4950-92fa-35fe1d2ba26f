import { assert } from 'chai';
import { testIsFrozenApplication } from '..';
import { Node, Test, decorators } from '../../index';
import * as fixtures from '../fixtures';
import { codeDataType, descriptionDataType } from '../fixtures/data-types/data-types';
import { createApplicationWithApi, initTables, restoreTables, setup } from '../fixtures/index';

const { TestFrozen, TestOverrideFrozen, TestOverrideNotFrozen } = fixtures.nodes;

@decorators.node<TestFrozenBase>({
    isPublished: true,
    storage: 'sql',
    isAbstract: true,
    indexes: [{ orderBy: { code: +1 }, isUnique: true }],
})
class TestFrozenBase extends Node {
    @decorators.stringProperty<TestFrozenBase, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestFrozenBase, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
        defaultValue: 'base',
        isFrozen: true,
    })
    readonly description: Promise<string>;
}

@decorators.subNode<TestOverrideFrozenNotFrozen>({
    isPublished: true,
    extends: () => TestFrozenBase,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
})
class TestOverrideFrozenNotFrozen extends TestFrozenBase {
    @decorators.stringProperty<TestOverrideFrozenNotFrozen, 'strFromOverrideNotFrozen'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly strFromOverrideNotFrozen: Promise<string>;

    @decorators.stringPropertyOverride<TestOverrideFrozenNotFrozen, 'description'>({
        defaultValue: 'notFrozen',
        isFrozen: false,
    })
    override readonly description: Promise<string>;
}

describe('frozen', () => {
    before(async () => {
        await setup({ application: await testIsFrozenApplication.application });
        await initTables([
            { nodeConstructor: TestFrozen, data: [] },
            { nodeConstructor: TestOverrideFrozen, data: [] },
            { nodeConstructor: TestOverrideNotFrozen, data: [] },
        ]);
    });

    it('can create regardless of frozen values', () =>
        Test.withContext(async context => {
            const node = await context.create(TestFrozen, { code: 'FROZEN', description: 'FROZEN' });
            await node.$.save();
            assert.equal(await node.code, 'FROZEN');
            assert.equal(await node.description, 'FROZEN');
        }));

    it('cannot update when node is frozen', () =>
        Test.withContext(async context => {
            const node = await context.create(TestFrozen, { code: 'FROZEN', description: 'Desc A' });
            await node.$.save();
            const readResult = await context.read(TestFrozen, { code: 'FROZEN' }, { forUpdate: true });
            await assert.isRejected(readResult.$.set({ description: 'Desc X' }), 'TestFrozen: node is frozen');
        }));

    it('cannot update frozen property', () =>
        Test.withContext(async context => {
            const node = await context.create(TestFrozen, { code: 'A', description: 'FROZEN' });
            await node.$.save();
            const readResult = await context.read(TestFrozen, { code: 'A' }, { forUpdate: true });
            await assert.isRejected(
                readResult.$.set({ description: 'Desc X' }),
                'TestFrozen.description: cannot set value on frozen property',
            );
        }));

    it('can update non-frozen property', () =>
        Test.withContext(async context => {
            const node = await context.create(TestFrozen, { code: 'A', description: 'FROZEN' });
            await node.$.save();
            const readResult = await context.read(TestFrozen, { code: 'A' }, { forUpdate: true });
            await readResult.$.set({ code: 'B' });
            await readResult.$.save();
            assert.strictEqual(await readResult.code, 'B');
        }));

    it('can update sub node with non-frozen property', () =>
        Test.withContext(async context => {
            const node = await context.create(TestOverrideNotFrozen, { code: 'A1' });
            await node.$.save();
            const readResult = await context.read(TestOverrideNotFrozen, { code: 'A1' }, { forUpdate: true });
            assert.strictEqual(await readResult.description, 'notFrozen');
            await readResult.$.set({ description: 'Updated text' });
            await readResult.$.save();
            assert.strictEqual(await readResult.description, 'Updated text');
        }));

    it('cannot update sub node with isFrozen override on property', () =>
        Test.withContext(async context => {
            const node = await context.create(TestOverrideFrozen, { code: 'A2' });
            await node.$.save();
            const readResult = await context.read(TestOverrideFrozen, { code: 'A2' }, { forUpdate: true });
            assert.strictEqual(await readResult.description, 'frozen');
            await assert.isRejected(
                readResult.$.set({ description: 'Updated text' }),
                'TestOverrideFrozen.description: cannot set value on frozen property',
            );
        }));

    it('Cannot create application with invalid isFrozen override', async () => {
        await assert.isRejected(
            createApplicationWithApi({ nodes: { TestFrozenBase, TestOverrideFrozenNotFrozen } }),
            'Cannot override isFrozen if already frozen in base class',
        );
    });

    it('Cannot create application with subNodes with missing base nodes', async () => {
        await assert.isRejected(
            createApplicationWithApi({ nodes: { TestOverrideFrozenNotFrozen } }),
            'Some subNodes were not created due to missing base nodes',
        );
    });

    after(() => restoreTables());
});
