import { assert } from 'chai';
import { decorators, Node } from '../../index';
import { Test, ValidationSeverity } from '../../lib';
import { codeDataType } from '../fixtures/data-types/data-types';
import { createApplicationWithApi, initTables, restoreTables, setup } from '../fixtures/index';

@decorators.node<TestPrimaryNestedDiagnose>({
    storage: 'sql',
    canCreate: true,
    async saveBegin() {
        await (await this.$.context.create(TestSecondaryNestedDiagnose, { code: await this.code })).$.save();
    },
})
export class TestPrimaryNestedDiagnose extends Node {
    @decorators.stringProperty<TestPrimaryNestedDiagnose, 'code'>({
        isStored: true,
        dataType: () => codeDataType,
        async control(cx) {
            // generate warn1 twice to test that we eliminate duplicate warnings
            await cx.warn.if(await this.code).is.matching(/warn1/);
            await cx.warn.if(await this.code).is.matching(/warn1/);
            await cx.error.if(await this.code).is.matching(/error1/);
        },
    })
    readonly code: Promise<string>;
}

@decorators.node<TestSecondaryNestedDiagnose>({
    storage: 'sql',
    canCreate: true,
})
export class TestSecondaryNestedDiagnose extends Node {
    @decorators.stringProperty<TestSecondaryNestedDiagnose, 'code'>({
        isStored: true,
        dataType: () => codeDataType,
        async control(cx) {
            await cx.warn.if(await this.code).is.matching(/warn2/);
            await cx.error.if(await this.code).is.matching(/error2/);
        },
    })
    readonly code: Promise<string>;
}

describe('Nested diagnoses', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi({
                nodes: { TestPrimaryNestedDiagnose, TestSecondaryNestedDiagnose },
            }),
        });
        await initTables([
            { nodeConstructor: TestPrimaryNestedDiagnose, data: [] },
            { nodeConstructor: TestSecondaryNestedDiagnose, data: [] },
        ]);
    });
    it('generate correct diagnoses with only warnings', () =>
        Test.withContext(async context => {
            await (await context.create(TestPrimaryNestedDiagnose, { code: 'warn1 warn2' })).$.save();
            assert.deepEqual(context.diagnoses, [
                {
                    severity: ValidationSeverity.warn,
                    path: ['code'],
                    message: 'value must not match /warn1/',
                },
                {
                    severity: ValidationSeverity.warn,
                    path: [],
                    message: 'While saving TestSecondaryNestedDiagnose(-1000000002): value must not match /warn2/',
                },
            ]);
        }));

    it('generate correct diagnoses if first save fails', () =>
        Test.withContext(async context => {
            await assert.isRejected(
                (await context.create(TestPrimaryNestedDiagnose, { code: 'warn1 warn2 error1' })).$.save(),
                /was not created/,
            );
            assert.deepEqual(context.diagnoses, [
                {
                    severity: ValidationSeverity.warn,
                    path: ['code'],
                    message: 'value must not match /warn1/',
                },
                {
                    severity: ValidationSeverity.error,
                    path: ['code'],
                    message: 'value must not match /error1/',
                },
            ]);
        }));

    it('generate correct diagnoses if second save fails', () =>
        Test.withContext(async context => {
            const node = await context.create(TestPrimaryNestedDiagnose, {
                code: 'warn1 warn2 error2',
            });
            await assert.isRejected(node.$.save(), /was not created/);
            assert.deepEqual(context.diagnoses, [
                {
                    severity: ValidationSeverity.warn,
                    path: ['code'],
                    message: 'value must not match /warn1/',
                },
                {
                    severity: ValidationSeverity.warn,
                    path: [],
                    message: 'While saving TestSecondaryNestedDiagnose(-1000000002): value must not match /warn2/',
                },
                {
                    severity: ValidationSeverity.error,
                    path: [],
                    message: 'While saving TestSecondaryNestedDiagnose(-1000000002): value must not match /error2/',
                },
            ]);
        }));

    after(() => restoreTables());
});
