import { assert } from 'chai';
import { Reference } from '../../index';
import { Collection, Context, decorators, initTables, integer, Node, Test } from '../../lib';
import { createApplicationWithApi, setup } from '../fixtures';
import { codeDataType } from '../fixtures/data-types/data-types';

@decorators.node<TestDocumentWithNonVitalCollection>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    indexes: [{ orderBy: { code: +1 }, isUnique: true }],
})
export class TestDocumentWithNonVitalCollection extends Node {
    @decorators.stringProperty<TestDocumentWithNonVitalCollection, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.collectionProperty<TestDocumentWithNonVitalCollection, 'items'>({
        isPublished: true,
        isVital: false,
        node: () => TestNonVitalCollectionItem,
        reverseReference: 'document',
    })
    readonly items: Collection<TestNonVitalCollectionItem>;
}

@decorators.node<TestNonVitalCollectionItem>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    indexes: [{ orderBy: { number: +1 }, isUnique: true }],
})
export class TestNonVitalCollectionItem extends Node {
    @decorators.integerProperty<TestNonVitalCollectionItem, 'number'>({
        isPublished: true,
        isStored: true,
    })
    readonly number: Promise<integer>;

    @decorators.referenceProperty<TestNonVitalCollectionItem, 'document'>({
        isStored: true,
        isPublished: true,
        node: () => TestDocumentWithNonVitalCollection,
    })
    readonly document: Reference<TestDocumentWithNonVitalCollection>;
}

describe('Collection reload when target table changes', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi({
                nodes: {
                    TestDocumentWithNonVitalCollection,
                    TestNonVitalCollectionItem,
                },
            }),
        });
        await initTables([
            {
                nodeConstructor: TestDocumentWithNonVitalCollection,
                data: [{ code: 'code1' }, { code: 'code2' }],
            },
            {
                nodeConstructor: TestNonVitalCollectionItem,
                data: [
                    { number: 1, document: 1 },
                    { number: 2, document: 1 },
                    { number: 5, document: 2 },
                ],
            },
        ]);
    });

    async function checkItems(context: Context, documentCode: string, expectedLineNumbers: integer[]): Promise<void> {
        const document = await context.read(TestDocumentWithNonVitalCollection, { code: documentCode });
        assert.deepEqual(await document.items.map(item => item.number).toArray(), expectedLineNumbers);
    }

    it('reloads collection if one collection item has been created', () =>
        Test.withContext(async context => {
            await checkItems(context, 'code1', [1, 2]);

            const newItem = await context.create(TestNonVitalCollectionItem, {
                number: 3,
                document: 1,
            });
            await newItem.$.save();

            await checkItems(context, 'code1', [1, 2, 3]);
        }));

    it('reloads collection if one collection item has been updated', () =>
        Test.withContext(async context => {
            await checkItems(context, 'code1', [1, 2]);
            await checkItems(context, 'code2', [5]);

            const updatedItem = await context.read(TestNonVitalCollectionItem, { number: 2 }, { forUpdate: true });
            await updatedItem.$.set({
                document: await context.read(TestDocumentWithNonVitalCollection, { code: 'code2' }),
            });
            await updatedItem.$.save();

            await checkItems(context, 'code1', [1]);
            await checkItems(context, 'code2', [2, 5]);
        }));

    it('reloads collection if one collection item has been deleted', () =>
        Test.withContext(async context => {
            await checkItems(context, 'code1', [1, 2]);

            const linesNode = await context.read(TestNonVitalCollectionItem, { number: 2 }, { forUpdate: true });
            await linesNode.$.delete();

            await checkItems(context, 'code1', [1]);
        }));
});
