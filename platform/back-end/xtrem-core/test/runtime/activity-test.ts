import { assert } from 'chai';
import { Activity, Dict, Test } from '../../index';
import { ActivityExtension } from '../../lib/ts-api/activity-extension';
import { activities, activityExtensions, createApplicationWithApi, setup } from '../fixtures/index';
// import * as nodes from '../fixtures/nodes';
import {
    TestActiLine,
    TestActiLineDetail,
    TestActivity,
    TestActivityRelated,
    TestAnimal,
    TestAnimalLine,
    TestCat,
    TestFlyBehavior,
    TestMammal,
    TestPetOwner,
    TestRefPropNoDataType,
    TestRefPropWithDataType,
    TestReferred,
    TestSleepBehavior,
} from '../fixtures/nodes';

const nodes = {
    TestActiLine,
    TestActiLineDetail,
    TestActivity,
    TestActivityRelated,
    TestAnimal,
    TestAnimalLine,
    TestCat,
    TestMammal,
    TestPetOwner,
    TestReferred,
    TestFlyBehavior,
    TestSleepBehavior,
    TestRefPropNoDataType,
    TestRefPropWithDataType,
};

const invalidActivity = new Activity({
    description: 'Invalid activity',
    node: () => nodes.TestActivity,
    __filename: 'invalid-activity',
    permissions: ['report'],
    operationGrants: {
        report: [
            {
                operations: ['reportC'],
                on: [() => nodes.TestActivityRelated],
            },
        ],
    },
});

const invalidActivity2 = new Activity({
    description: 'Invalid activity 2',
    node: () => nodes.TestActivity,
    __filename: 'invalid-activity2',
    permissions: [],
});

const expectedGrantsTestActivity = {
    read: [
        { operation: 'read', node: 'TestActivity', type: 'operationGrant' },
        { permission: 'lookup', activity: 'testActivity', type: 'permissionGrant' },
        { operation: 'lookup', node: 'TestActivity', type: 'operationGrant' },
        {
            operation: 'read',
            node: 'TestActiLine',
            type: 'operationGrant',
        },
        {
            operation: 'lookup',
            node: 'TestActiLine',
            type: 'operationGrant',
        },
        {
            operation: 'read',
            node: 'TestActiLineDetail',
            type: 'operationGrant',
        },
        {
            operation: 'lookup',
            node: 'TestActiLineDetail',
            type: 'operationGrant',
        },
        {
            operation: 'lookup',
            node: 'TestReferred',
            type: 'operationGrant',
        },
    ],
    create: [
        {
            operation: 'create',
            node: 'TestActivity',
            type: 'operationGrant',
        },
        { permission: 'read', activity: 'testActivity', type: 'permissionGrant' },
        { permission: 'lookup', activity: 'testActivity', type: 'permissionGrant' },
        { operation: 'create', node: 'TestActivityRelated', type: 'operationGrant' },
        { operation: 'read', node: 'TestActivity', type: 'operationGrant' },
        { operation: 'lookup', node: 'TestActivity', type: 'operationGrant' },
        { operation: 'create', node: 'TestActiLine', type: 'operationGrant' },
        { operation: 'read', node: 'TestActiLine', type: 'operationGrant' },
        { operation: 'lookup', node: 'TestActiLine', type: 'operationGrant' },
        { operation: 'create', node: 'TestActiLineDetail', type: 'operationGrant' },
        { operation: 'read', node: 'TestActiLineDetail', type: 'operationGrant' },
        { operation: 'lookup', node: 'TestActiLineDetail', type: 'operationGrant' },
        {
            node: 'TestReferred',
            operation: 'lookup',
            type: 'operationGrant',
        },
        { operation: 'read', node: 'TestActivityRelated', type: 'operationGrant' },
        { operation: 'lookup', node: 'TestActivityRelated', type: 'operationGrant' },
        {
            node: 'TestCat',
            operation: 'lookup',
            type: 'operationGrant',
        },
        {
            node: 'TestAnimalLine',
            operation: 'lookup',
            type: 'operationGrant',
        },
    ],
    update: [
        { operation: 'update', node: 'TestActivity', type: 'operationGrant' },
        { permission: 'read', activity: 'testActivity', type: 'permissionGrant' },
        { permission: 'lookup', activity: 'testActivity', type: 'permissionGrant' },
        {
            operation: 'create',
            node: 'TestActivityRelated',
            type: 'operationGrant',
        },
        {
            operation: 'read',
            node: 'TestActivity',
            type: 'operationGrant',
        },
        {
            operation: 'lookup',
            node: 'TestActivity',
            type: 'operationGrant',
        },
        {
            operation: 'update',
            node: 'TestActiLine',
            type: 'operationGrant',
        },
        {
            operation: 'read',
            node: 'TestActiLine',
            type: 'operationGrant',
        },
        {
            operation: 'lookup',
            node: 'TestActiLine',
            type: 'operationGrant',
        },
        {
            operation: 'update',
            node: 'TestActiLineDetail',
            type: 'operationGrant',
        },
        {
            operation: 'read',
            node: 'TestActiLineDetail',
            type: 'operationGrant',
        },
        {
            operation: 'lookup',
            node: 'TestActiLineDetail',
            type: 'operationGrant',
        },
        {
            operation: 'lookup',
            node: 'TestReferred',
            type: 'operationGrant',
        },
        { operation: 'read', node: 'TestActivityRelated', type: 'operationGrant' },
        { operation: 'lookup', node: 'TestActivityRelated', type: 'operationGrant' },
        {
            operation: 'lookup',
            node: 'TestCat',
            type: 'operationGrant',
        },
        {
            operation: 'lookup',
            node: 'TestAnimalLine',
            type: 'operationGrant',
        },
    ],
    delete: [
        { operation: 'delete', node: 'TestActivity', type: 'operationGrant' },
        { permission: 'read', activity: 'testActivity', type: 'permissionGrant' },
        { permission: 'lookup', activity: 'testActivity', type: 'permissionGrant' },
        {
            operation: 'read',
            node: 'TestActivity',
            type: 'operationGrant',
        },
        {
            operation: 'lookup',
            node: 'TestActivity',
            type: 'operationGrant',
        },
        {
            operation: 'delete',
            node: 'TestActiLine',
            type: 'operationGrant',
        },
        {
            operation: 'read',
            node: 'TestActiLine',
            type: 'operationGrant',
        },
        {
            operation: 'lookup',
            node: 'TestActiLine',
            type: 'operationGrant',
        },
        {
            operation: 'delete',
            node: 'TestActiLineDetail',
            type: 'operationGrant',
        },
        {
            operation: 'read',
            node: 'TestActiLineDetail',
            type: 'operationGrant',
        },
        {
            operation: 'lookup',
            node: 'TestActiLineDetail',
            type: 'operationGrant',
        },
        {
            operation: 'lookup',
            node: 'TestReferred',
            type: 'operationGrant',
        },
    ],
    report: [
        {
            operation: 'report',
            node: 'TestActivity',
            type: 'operationGrant',
        },
        {
            operation: 'reportA',
            node: 'TestActivityRelated',
            type: 'operationGrant',
        },
        {
            operation: 'reportB',
            node: 'TestActivityRelated',
            type: 'operationGrant',
        },
        {
            node: 'TestActiLine',
            operation: 'lookup',
            type: 'operationGrant',
        },
        {
            node: 'TestReferred',
            operation: 'lookup',
            type: 'operationGrant',
        },
        {
            node: 'TestCat',
            operation: 'lookup',
            type: 'operationGrant',
        },
        {
            node: 'TestActiLineDetail',
            operation: 'lookup',
            type: 'operationGrant',
        },
        {
            node: 'TestAnimalLine',
            operation: 'lookup',
            type: 'operationGrant',
        },
    ],
    lookup: [
        { node: 'TestActivity', operation: 'lookup', type: 'operationGrant' },
        { node: 'TestActiLine', operation: 'lookup', type: 'operationGrant' },
        { node: 'TestActiLineDetail', operation: 'lookup', type: 'operationGrant' },
    ],
    testMutation: [
        { operation: 'testMutation', node: 'TestActivity', type: 'operationGrant' },
        {
            node: 'TestActiLine',
            operation: 'lookup',
            type: 'operationGrant',
        },
        {
            node: 'TestReferred',
            operation: 'lookup',
            type: 'operationGrant',
        },
        {
            node: 'TestActiLineDetail',
            operation: 'lookup',
            type: 'operationGrant',
        },
    ],
    report2: [
        {
            operation: 'reportAExt',
            node: 'TestActivityRelated',
            type: 'operationGrant',
        },
        {
            operation: 'lookup',
            node: 'TestCat',
            type: 'operationGrant',
        },
        {
            operation: 'lookup',
            node: 'TestAnimalLine',
            type: 'operationGrant',
        },
    ],
} as Dict<any>;

const expectedGrantsTestActivityRelated = {
    create: [
        {
            node: 'TestActivityRelated',
            operation: 'create',
            type: 'operationGrant',
        },
        {
            activity: 'testActivityRelated',
            permission: 'read',
            type: 'permissionGrant',
        },
        {
            activity: 'testActivityRelated',
            permission: 'lookup',
            type: 'permissionGrant',
        },
        {
            node: 'TestActivity',
            operation: 'reportFromRelated',
            type: 'operationGrant',
        },
        {
            node: 'TestActivityRelated',
            operation: 'read',
            type: 'operationGrant',
        },
        {
            node: 'TestActivityRelated',
            operation: 'lookup',
            type: 'operationGrant',
        },
        {
            node: 'TestCat',
            operation: 'lookup',
            type: 'operationGrant',
        },
        {
            node: 'TestActiLine',
            operation: 'lookup',
            type: 'operationGrant',
        },
        {
            node: 'TestReferred',
            operation: 'lookup',
            type: 'operationGrant',
        },
        {
            node: 'TestAnimalLine',
            operation: 'lookup',
            type: 'operationGrant',
        },
        {
            node: 'TestActiLineDetail',
            operation: 'lookup',
            type: 'operationGrant',
        },
    ],
    report: [
        {
            activity: 'testActivity',
            permission: 'report',
            type: 'permissionGrant',
        },
        {
            node: 'TestActivity',
            operation: 'report',
            type: 'operationGrant',
        },
        {
            node: 'TestActivityRelated',
            operation: 'reportA',
            type: 'operationGrant',
        },
        {
            node: 'TestActivityRelated',
            operation: 'reportB',
            type: 'operationGrant',
        },
        {
            node: 'TestActiLine',
            operation: 'lookup',
            type: 'operationGrant',
        },
        {
            node: 'TestReferred',
            operation: 'lookup',
            type: 'operationGrant',
        },
        {
            node: 'TestCat',
            operation: 'lookup',
            type: 'operationGrant',
        },
        {
            node: 'TestActiLineDetail',
            operation: 'lookup',
            type: 'operationGrant',
        },
        {
            node: 'TestAnimalLine',
            operation: 'lookup',
            type: 'operationGrant',
        },
    ],
} as Dict<any>;

const expectedTestActivityActualPermissions = {
    lookup: { lookup: ['TestActivity', 'TestActiLine', 'TestActiLineDetail'] },
    read: {
        read: ['TestActivity', 'TestActiLine', 'TestActiLineDetail'],
        lookup: ['TestActivity', 'TestActiLine', 'TestActiLineDetail', 'TestReferred'],
    },
    create: {
        create: ['TestActivity', 'TestActivityRelated', 'TestActiLine', 'TestActiLineDetail'],
        read: ['TestActivity', 'TestActiLine', 'TestActiLineDetail'],
        lookup: [
            'TestActivity',
            'TestActiLine',
            'TestActiLineDetail',
            'TestReferred',
            'TestActivityRelated',
            'TestCat',
            'TestAnimalLine',
        ],
    },
    update: {
        create: ['TestActivityRelated'],
        read: ['TestActivity', 'TestActiLine', 'TestActiLineDetail'],
        lookup: [
            'TestActivity',
            'TestActiLine',
            'TestActiLineDetail',
            'TestReferred',
            'TestActivityRelated',
            'TestCat',
            'TestAnimalLine',
        ],
        update: ['TestActivity', 'TestActiLine', 'TestActiLineDetail'],
    },
    delete: {
        read: ['TestActivity', 'TestActiLine', 'TestActiLineDetail'],
        lookup: ['TestActivity', 'TestActiLine', 'TestActiLineDetail', 'TestReferred'],
        delete: ['TestActivity', 'TestActiLine', 'TestActiLineDetail'],
    },
    report: {
        report: ['TestActivity'],
        reportA: ['TestActivityRelated'],
        reportB: ['TestActivityRelated'],
        lookup: ['TestActiLine', 'TestReferred', 'TestCat', 'TestActiLineDetail', 'TestAnimalLine'],
    },
    report2: {
        reportAExt: ['TestActivityRelated'],
        lookup: ['TestCat', 'TestAnimalLine'],
    },
    testMutation: {
        testMutation: ['TestActivity'],
        lookup: ['TestActiLine', 'TestReferred', 'TestActiLineDetail'],
    },
};

const expectedTestActivityRelatedActualPermissions = {
    create: {
        create: ['TestActivityRelated'],
        lookup: [
            'TestActivityRelated',
            'TestCat',
            'TestActiLine',
            'TestReferred',
            'TestAnimalLine',
            'TestActiLineDetail',
        ],
    },
    report: {
        lookup: ['TestActiLine', 'TestReferred', 'TestCat', 'TestActiLineDetail', 'TestAnimalLine'],
        report: ['TestActivity'],
        reportA: ['TestActivityRelated'],
        reportB: ['TestActivityRelated'],
    },
};

describe('Activity', () => {
    before(async () =>
        setup({ application: await createApplicationWithApi({ activities, activityExtensions, nodes }) }),
    );

    it('can populate testActivity (activity and extension) and testActivityRelated from fixtures into application. The internal grants, and inheritted internal grants are loaded correctly.', () => {
        assert.instanceOf(Test.application.activities.testActivity, Activity);
        assert.equal(Test.application.activities.testActivity.description, 'Test activity');
        assert.equal(Test.application.activities.testActivity.node, nodes.TestActivity);
        assert.equal(Test.application.activities.testActivity.name, 'testActivity');
        assert.equal(Test.application.activities.testActivity.package, '@sage/xtrem-core');

        assert.deepEqual(Test.application.activities.testActivity.permissions, [
            'read',
            'create',
            'update',
            'delete',
            'report',
            'report2',
        ]);

        assert.deepEqual(
            Object.keys(Test.application.activities.testActivity.internalGrants),
            Object.keys(expectedGrantsTestActivity),
        );

        assert.deepEqual(Test.application.activities.testActivity.permissionGrants, {});

        Object.entries(Test.application.activities.testActivity.internalGrants).forEach(([grantKey, grantMap]) => {
            const grants = Object.values(grantMap);
            assert.deepEqual(
                grants,
                expectedGrantsTestActivity[grantKey],
                `[testActivity] Grants mismatch on ${grantKey}`,
            );
        });

        assert.instanceOf(Test.application.activities.testActivityRelated, Activity);
        assert.equal(Test.application.activities.testActivityRelated.description, 'Test activity related');
        assert.equal(Test.application.activities.testActivityRelated.node, nodes.TestActivityRelated);
        assert.equal(Test.application.activities.testActivityRelated.name, 'testActivityRelated');
        assert.equal(Test.application.activities.testActivityRelated.package, '@sage/xtrem-core');

        assert.deepEqual(Test.application.activities.testActivityRelated.permissions, ['create', 'report']);

        assert.deepEqual(Object.keys(Test.application.activities.testActivityRelated.internalGrants), [
            'create',
            'report',
        ]);

        assert.deepEqual(Object.keys(Test.application.activities.testActivityRelated.permissionGrants!), ['report']);

        const testActivityRelatedReportsPermissionGrant =
            Test.application.activities.testActivityRelated.permissionGrants!.report;

        testActivityRelatedReportsPermissionGrant.forEach(permissionGrant => {
            assert.deepEqual(
                permissionGrant.on.map(activity => activity().name),
                ['testActivity'],
            );
            assert.deepEqual(permissionGrant.permissions, ['report']);
        });

        Object.entries(Test.application.activities.testActivityRelated.internalGrants).forEach(
            ([grantKey, grantMap]) => {
                const grants = Object.values(grantMap);
                assert.deepEqual(
                    grants,
                    expectedGrantsTestActivityRelated[grantKey],
                    `[testActivityRelated] Grants mismatch on ${grantKey}`,
                );
            },
        );
    });

    it('can merge grants properly', () => {
        assert.isDefined(Test.application.activities.testActivity.operationGrants);

        if (Test.application.activities.testActivity.operationGrants) {
            const testActivityBeforeMerge: unknown = {
                permissions: [],
                permissionGrants: {},
                operationGrants: {
                    lookup: [{ operations: ['create'] }],
                    create: [{ operations: ['create'] }],
                    report: [{ operations: ['reportC', 'reportD'] }],
                    update: [],
                    report2: [{ operations: ['reportAExt'], on: [() => {}] }],
                    watch: [{ operations: ['design'] }],
                },
            };

            Test.application.activities.testActivity.mergeExtension(testActivityBeforeMerge as ActivityExtension);

            // lookup
            assert.exists(Test.application.activities.testActivity.operationGrants.lookup);
            assert.equal(Test.application.activities.testActivity.operationGrants.lookup.length, 1);
            assert.exists(Test.application.activities.testActivity.operationGrants.lookup[0].operations);
            assert.equal(Test.application.activities.testActivity.operationGrants.lookup[0].operations.length, 1);
            assert.equal(Test.application.activities.testActivity.operationGrants.lookup[0].operations[0], 'create');

            // create
            assert.exists(Test.application.activities.testActivity.operationGrants.create);
            assert.equal(Test.application.activities.testActivity.operationGrants.create.length, 2);
            assert.exists(Test.application.activities.testActivity.operationGrants.create[0].operations);
            assert.deepEqual(Test.application.activities.testActivity.operationGrants.create[0].operations, ['create']);
            assert.exists(Test.application.activities.testActivity.operationGrants.create[1].operations);
            assert.deepEqual(Test.application.activities.testActivity.operationGrants.create[1].operations, ['create']);

            // update
            assert.exists(Test.application.activities.testActivity.operationGrants.update);
            assert.equal(Test.application.activities.testActivity.operationGrants.update.length, 1);
            assert.exists(Test.application.activities.testActivity.operationGrants.update[0].operations);
            assert.deepEqual(Test.application.activities.testActivity.operationGrants.update[0].operations, ['create']);

            // report
            assert.exists(Test.application.activities.testActivity.operationGrants.report);
            assert.equal(Test.application.activities.testActivity.operationGrants.report.length, 2);
            assert.exists(Test.application.activities.testActivity.operationGrants.report[0].operations);
            assert.deepEqual(Test.application.activities.testActivity.operationGrants.report[0].operations, [
                'reportA',
                'reportB',
            ]);
            assert.exists(Test.application.activities.testActivity.operationGrants.report[1].operations);
            assert.deepEqual(Test.application.activities.testActivity.operationGrants.report[1].operations, [
                'reportC',
                'reportD',
            ]);

            // report2
            assert.exists(Test.application.activities.testActivity.operationGrants.report2);
            assert.equal(Test.application.activities.testActivity.operationGrants.report2.length, 2);
            assert.exists(Test.application.activities.testActivity.operationGrants.report2[0].operations);
            assert.deepEqual(Test.application.activities.testActivity.operationGrants.report2[0].operations, [
                'reportAExt',
            ]);
            assert.exists(Test.application.activities.testActivity.operationGrants.report2[1].operations);
            assert.deepEqual(Test.application.activities.testActivity.operationGrants.report2[1].operations, [
                'reportAExt',
            ]);

            // testMutation
            assert.exists(Test.application.activities.testActivity.operationGrants.testMutation);
            assert.equal(Test.application.activities.testActivity.operationGrants.testMutation.length, 0);

            // watch
            assert.exists(Test.application.activities.testActivity.operationGrants.watch);
            assert.equal(Test.application.activities.testActivity.operationGrants.watch.length, 1);
            assert.exists(Test.application.activities.testActivity.operationGrants.watch[0].operations);
            assert.deepEqual(Test.application.activities.testActivity.operationGrants.watch[0].operations, ['design']);
        }
    });

    it('Activity validation should fail for invalidActivity', async () => {
        const application = await createApplicationWithApi({ activities: { invalidActivity }, nodes });
        assert.throw(
            () => application.validateActivities(),
            'Activity invalidActivity: invalid operation granted on Node TestActivityRelated, reportC not allowed or not found.',
        );
    });

    it('Activity validation should fail for invalidActivity2', async () => {
        const application = await createApplicationWithApi({ activities: { invalidActivity2 }, nodes });
        assert.throw(() => application.validateActivities(), 'Activity invalidActivity2 has no permissions defined.');
    });

    it('Activity validation should fail where nodes are not loaded', async () => {
        await assert.isRejected(createApplicationWithApi({ activities }), "Factory 'TestActivity' could not be found.");
    });

    it('Correctly fills, cascades and flattens permissions', () => {
        // Rules:
        // - All permissions are cascading to vital children.
        // - Create or update imply lookup on the node's references.
        // - Create, update or delete imply read.
        // - Read implies lookup.
        // - Lookup implies lookup on the superclasses' vital references.
        // - Reference properties implies lookup.
        //
        // Note on Lookup:
        // - Lookup cascades:
        //          1. to vital references of the node that was granted lookup.
        //          2. to vital references of superclasses of the node that was granted lookup.
        //          3. but not to the superclasses themselves.
        // - Here, in the "create" and "update" permissions:
        //          1. lookup is granted to TestCat because TestCat is a vital collection of TestActivityRelated.
        //          2. TestCat derives from TestAnimal, and TestAnimal has a vital collection of TestAnimalLines.
        //   So actualPermissions.create grants lookup on TestCat, and TestAnimalLines, but not on TestAnimal itself.

        assert.deepEqual(
            Test.application.activities.testActivity.flattenedPermissions,
            expectedTestActivityActualPermissions,
            'Permission mismatch on testActivity',
        );

        assert.deepEqual(
            Test.application.activities.testActivityRelated.flattenedPermissions,
            expectedTestActivityRelatedActualPermissions,
            'Permission mismatch on testActivityRelated',
        );
    });
});
