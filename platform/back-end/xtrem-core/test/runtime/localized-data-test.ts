import { asyncArray } from '@sage/xtrem-async-helper';
import { assert } from 'chai';
import { date, decimal, decorators, Dict, Node } from '../../index';
import { NodeKey, NodeQueryOptions, StaticThis, Test, ValidationSeverity } from '../../lib';
import {
    codeDataType,
    defaultDecimalDataType,
    descriptionDataType,
    localizedCodeDataType,
    localizedDescriptionDataType,
} from '../fixtures/data-types/data-types';
import { ConfigManager, createApplicationWithApi, initTables, restoreTables, setup } from '../fixtures/index';

@decorators.node<TestLocalizedData>({
    storage: 'sql',
    tableName: 'TestLocalizedData',
    canDeleteMany: true,
    indexes: [
        {
            orderBy: { id: 1 },
            isUnique: true,
        },
    ],
})
export class TestLocalizedData extends Node {
    @decorators.stringProperty<TestLocalizedData, 'id'>({
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly id: Promise<string>;

    @decorators.stringProperty<TestLocalizedData, 'code'>({
        isStored: true,
        dataType: () => localizedCodeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestLocalizedData, 'text'>({
        isStored: true,
        dataType: () => localizedDescriptionDataType,
    })
    readonly text: Promise<string>;
}

@decorators.node<TestLocalizedDataInvalid>({
    storage: 'sql',
    tableName: 'TestLocalizedDataInvalid',
    canDeleteMany: true,
    indexes: [
        {
            orderBy: { id: 1 },
            isUnique: true,
        },
    ],
})
export class TestLocalizedDataInvalid extends Node {
    @decorators.stringProperty<TestLocalizedDataInvalid, 'id'>({
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly id: Promise<string>;

    @decorators.stringProperty<TestLocalizedDataInvalid, 'code'>({
        isStored: true,
        dataType: () => localizedCodeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestLocalizedDataInvalid, 'text'>({
        dataType: () => localizedDescriptionDataType,
    })
    readonly text: Promise<string>;
}

@decorators.node<TestDummyNode>({
    storage: 'sql',
    tableName: 'TestDummyNode',
    indexes: [
        {
            orderBy: { id: 1 },
            isUnique: true,
        },
    ],
})
export class TestDummyNode extends Node {
    @decorators.stringProperty<TestDummyNode, 'id'>({
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly id: Promise<string>;

    @decorators.stringProperty<TestDummyNode, 'code'>({
        isStored: true,
        dataType: () => localizedCodeDataType,
    })
    readonly code: Promise<string>;
}

@decorators.node<TestLocalizedData2>({
    storage: 'sql',
    tableName: 'TestLocalizedData2',
    canDeleteMany: true,
    indexes: [
        {
            orderBy: { id: 1 },
            isUnique: true,
        },
    ],
})
export class TestLocalizedData2 extends Node {
    @decorators.stringProperty<TestLocalizedData2, 'id'>({
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly id: Promise<string>;

    @decorators.stringProperty<TestLocalizedData2, 'code'>({
        isStored: true,
        dataType: () => localizedCodeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestLocalizedData2, 'strProp'>({
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly strProp?: Promise<string>;

    @decorators.dateProperty<TestLocalizedData2, 'dateProp'>({
        isStored: true,
    })
    readonly dateProp?: Promise<date>;

    @decorators.decimalProperty<TestLocalizedData2, 'decimalProp'>({
        isStored: true,
        dataType: () => defaultDecimalDataType,
    })
    readonly decimalProp: Promise<decimal>;

    @decorators.stringProperty<TestLocalizedData2, 'text'>({
        isStored: true,
        dataType: () => localizedDescriptionDataType,
    })
    readonly text: Promise<string>;

    @decorators.stringProperty<TestLocalizedData2, 'text2'>({
        isStored: true,
        dataType: () => localizedDescriptionDataType,
        dependsOn: ['text'],
        defaultValue() {
            return this.text;
        },
    })
    readonly text2: Promise<string>;
}

@decorators.node<TestIsNotEmpty>({
    storage: 'sql',
    tableName: 'TestIsNotEmpty',
    indexes: [
        {
            orderBy: { id: 1 },
            isUnique: true,
        },
    ],
})
export class TestIsNotEmpty extends Node {
    @decorators.stringProperty<TestIsNotEmpty, 'id'>({
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly id: Promise<string>;

    @decorators.stringProperty<TestIsNotEmpty, 'code'>({
        isStored: true,
        dataType: () => localizedCodeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestIsNotEmpty, 'notEmptyLocalizedText'>({
        isStored: true,
        isNotEmpty: true,
        dataType: () => localizedDescriptionDataType,
    })
    readonly notEmptyLocalizedText: Promise<string>;
}

async function clearTable(classToUse: any): Promise<void> {
    await Test.withCommittedContext(context => context.deleteMany(classToUse, {}));
}

async function checkLocales<T extends Node & { id: Promise<string> }>(
    classToUse: StaticThis<T>,
    key: NodeKey<T>,
    locales: Dict<string>,
): Promise<void> {
    await asyncArray(Object.keys(locales)).forEach(locale =>
        Test.withReadonlyContext(async context => {
            await context.setTestLocale(locale);
            const result: TestLocalizedData | TestLocalizedData2 = (await context.read(classToUse, key)) as any;
            assert.isNotNull(result, `No result for locale '${locale}'`);
            assert.equal(await result.code, key.id, `Empty results for locale '${locale}'`);
            assert.equal(await result.text, locales[locale], `Content differs for locale '${locale}'`);
        }),
    );
}

describe('Localized properties - validations', () => {
    it('should throw an error if localized property is not set as column', async () => {
        await assert.isRejected(
            createApplicationWithApi({ nodes: { TestLocalizedDataInvalid } }),
            'TestLocalizedDataInvalid.text: localized properties isStored attribute must be set to true',
        );
    });
});

describe('Localized properties - isNotEmpty', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi({
                nodes: { TestIsNotEmpty },
            }),
        });
        await initTables([{ nodeConstructor: TestIsNotEmpty, data: [] }]);
    });

    it('should create a node with a not empty notEmptyLocalizedText', () =>
        Test.uncommitted(async context => {
            const nodeCreate = await context.create(TestIsNotEmpty, {
                id: 'C1',
                code: 'C1',
                notEmptyLocalizedText: 'not empty localized text',
            });
            await nodeCreate.$.save();
            const nodeRead = await context.read(TestIsNotEmpty, { id: 'C1' });
            assert.equal(await nodeRead.notEmptyLocalizedText, 'not empty localized text');
        }));

    it('should create a node with a not empty notEmptyLocalizedText (context.withLocalizedTextAsJson)', () =>
        Test.uncommitted(context =>
            context.withLocalizedTextAsJson(async () => {
                const nodeCreate = await context.create(TestIsNotEmpty, {
                    id: 'C2',
                    code: JSON.stringify({
                        'en-US': 'C2',
                        'fr-FR': 'C2',
                    }),
                    notEmptyLocalizedText: JSON.stringify({
                        'en-US': 'not empty localized text',
                        'fr-FR': "ce texte n'est pas vide",
                    }),
                });
                await nodeCreate.$.save();
                const nodeRead = await context.read(TestIsNotEmpty, { id: 'C2' });
                assert.deepEqual(JSON.parse(await nodeRead.notEmptyLocalizedText), {
                    base: 'not empty localized text',
                    en: 'not empty localized text',
                    'en-US': 'not empty localized text',
                    fr: "ce texte n'est pas vide",
                    'fr-FR': "ce texte n'est pas vide",
                });
            }),
        ));

    it('should throw an error if notEmptyLocalizedText is empty', () =>
        Test.uncommitted(async context => {
            const node = await context.create(TestIsNotEmpty, {
                id: 'C3',
                code: 'C3',
                notEmptyLocalizedText: '',
            });
            await assert.isRejected(node.$.save(), 'The record was not created.');
            assert.deepEqual(context.diagnoses, [
                {
                    message: 'string cannot be empty',
                    path: ['notEmptyLocalizedText'],
                    severity: ValidationSeverity.error,
                },
            ]);
        }));

    it('should throw an error if notEmptyLocalizedText is empty  (context.withLocalizedTextAsJson)', () =>
        Test.uncommitted(context =>
            context.withLocalizedTextAsJson(async () => {
                const node = await context.create(TestIsNotEmpty, {
                    id: 'C2',
                    code: JSON.stringify({
                        'en-US': 'C2',
                        'fr-FR': 'C2',
                    }),
                });
                await assert.isRejected(node.$.save(), 'The record was not created.');
                assert.deepEqual(context.diagnoses, [
                    {
                        message: 'string cannot be empty',
                        path: ['notEmptyLocalizedText'],
                        severity: ValidationSeverity.error,
                    },
                ]);
            }),
        ));

    it("should get '' value with empty localized", () =>
        Test.uncommitted(async context => {
            const nodeCreate = await context.create(TestIsNotEmpty, {
                id: 'C1',
                notEmptyLocalizedText: 'not empty localized text',
            });
            await nodeCreate.$.save();
            const nodeRead = await context.read(TestIsNotEmpty, { id: 'C1' }, { forUpdate: true });
            assert.equal(await nodeRead.notEmptyLocalizedText, 'not empty localized text');
            assert.equal(await nodeRead.code, '');
        }));

    it('should get an {} value with empty localized withLocalizedTextAsJson=true', () =>
        Test.uncommitted(context =>
            context.withLocalizedTextAsJson(async () => {
                const nodeCreate = await context.create(TestIsNotEmpty, {
                    id: 'C2',
                    notEmptyLocalizedText: JSON.stringify({
                        'en-US': 'not empty localized text',
                        'fr-FR': "ce texte n'est pas vide",
                    }),
                });
                await nodeCreate.$.save();
                const nodeRead = await context.read(TestIsNotEmpty, { id: 'C2' }, { forUpdate: true });
                assert.equal(await nodeRead.code, '{}');
                assert.deepEqual(JSON.parse(await nodeRead.notEmptyLocalizedText), {
                    base: 'not empty localized text',
                    en: 'not empty localized text',
                    'en-US': 'not empty localized text',
                    fr: "ce texte n'est pas vide",
                    'fr-FR': "ce texte n'est pas vide",
                });
            }),
        ));
    after(() => restoreTables());
});

[TestLocalizedData, TestLocalizedData2].forEach((classToUse, classIndex) => {
    describe(`Localized properties ${classIndex === 0 ? '(classic)' : '(overriden)'}`, () => {
        before(async () => {
            await setup({
                application: await createApplicationWithApi({
                    nodes: { TestLocalizedData, TestLocalizedData2, TestDummyNode },
                }),
            });
            await initTables([
                { nodeConstructor: classToUse, data: [] },
                { nodeConstructor: TestDummyNode, data: [] },
            ]);
        });
        it('should create and read nodes with localized property', () =>
            Test.uncommitted(async context => {
                const node1a = await context.create(classToUse, {
                    id: 'C1',
                    code: 'C1',
                    text: 'hello C1',
                });

                await node1a.$.save();
                const node2a = await context.create(classToUse, {
                    id: 'C2',
                    code: 'C2',
                    text: 'hello C2',
                });
                await node2a.$.save();
                const node1b = await context.read(classToUse, { id: 'C1' });
                assert.equal(await node1b.text, 'hello C1');
                const node2b = await context.read(classToUse, { id: 'C2' });
                assert.equal(await node2b.text, 'hello C2');
            }));

        it('should create and read node checking original language', () =>
            Test.uncommitted(async context => {
                const node1a = await context.create(classToUse, {
                    id: 'C1',
                    code: 'C1',
                    text: 'hello C1',
                });
                await node1a.$.save();
                const node1b = await context.read(classToUse, { id: 'C1' });
                assert.equal(await node1b.text, 'hello C1');
            }));

        it('filtering on localized property, simple filter', () =>
            Test.uncommitted(async context => {
                const testSimpleFilter = async () => {
                    let result = await context.query(classToUse, { filter: { text: 'hello C1' } }).toArray();
                    assert.equal(await result[0].text, 'hello C1');
                    result = await context.query(classToUse, { filter: { text: { _regex: 'hello' } } }).toArray();
                    await asyncArray(result).forEach(async r => {
                        assert.equal((await r.text).search(/\bhello\b/), 0);
                    });
                };
                if (classIndex === 1) {
                    const node1a = await context.create(TestLocalizedData2, {
                        id: 'C1',
                        code: 'C1',
                        strProp: '',
                        dateProp: date.today(),
                        decimalProp: 1.0,
                        text: 'hello C1',
                    });
                    await node1a.$.save();
                    const node2a = await context.create(TestLocalizedData2, {
                        id: 'C2',
                        code: 'C2',
                        strProp: 'STR2',
                        text: 'hello C2',
                    });
                    await node2a.$.save();
                    await testSimpleFilter();
                } else if (classIndex === 0) {
                    const node1a = await context.create(classToUse, {
                        id: 'C1',
                        code: 'C1',
                        text: 'hello C1',
                    });
                    await node1a.$.save();
                    const node2a = await context.create(classToUse, {
                        id: 'C2',
                        code: 'C2',
                        text: 'hello C2',
                    });
                    await node2a.$.save();
                    await testSimpleFilter();
                }
            }));

        it('filtering on localized property with complex filter', () =>
            Test.uncommitted(async context => {
                const testComplexFilter = async (options: NodeQueryOptions) => {
                    const result = (await context.query(classToUse, options).toArray()) as TestLocalizedData2[];
                    assert.equal(await result[0].code, 'C1');
                    assert.equal(await result[1].code, 'C2');
                };
                if (classIndex === 1) {
                    const node1a = await context.create(TestLocalizedData2, {
                        id: 'C1',
                        code: 'C1',
                        strProp: '',
                        dateProp: date.make(2019, 12, 31),
                        decimalProp: 1.0,
                        text: 'hello C1',
                    });
                    await node1a.$.save();
                    const node2a = await context.create(TestLocalizedData2, {
                        id: 'C2',
                        code: 'C2',
                        strProp: 'STR2',
                        text: 'hello C2',
                    });
                    await node2a.$.save();
                    await testComplexFilter({
                        filter: {
                            _or: [{ text: 'hello C1' }, { text: 'hello C2' }],
                        },
                    } as NodeQueryOptions);

                    await testComplexFilter({
                        filter: {
                            _or: [{ text: { _regex: 'C1' } }, { text: { _regex: 'C2' } }],
                        },
                    } as NodeQueryOptions);

                    await testComplexFilter({
                        filter: {
                            text: { _in: ['hello C1', 'hello C2'] },
                        },
                    } as NodeQueryOptions);
                } else if (classIndex === 0) {
                    const node1a = await context.create(classToUse, {
                        id: 'C1',
                        code: 'C1',
                        text: 'hello C1',
                    });
                    await node1a.$.save();
                    const node2a = await context.create(classToUse, {
                        id: 'C2',
                        code: 'C2',
                        text: 'hello C2',
                    });
                    await node2a.$.save();

                    await testComplexFilter({
                        filter: {
                            _or: [{ text: 'hello C1' }, { text: 'hello C2' }],
                        },
                    } as NodeQueryOptions);

                    await testComplexFilter({
                        filter: {
                            _or: [{ text: { _regex: 'C1' } }, { text: { _regex: 'C2' } }],
                        },
                    } as NodeQueryOptions);

                    await testComplexFilter({
                        filter: {
                            text: { _in: ['hello C1', 'hello C2'] },
                        },
                    } as NodeQueryOptions);
                }
            }));

        it('filtering on localized property with filter where data does not exist', () =>
            Test.uncommitted(async context => {
                const testComplexFilter = async (options: NodeQueryOptions) => {
                    const result = await context.query(classToUse, options).toArray();
                    assert.equal(result.length, 1);
                    // assert.equal(result[0].$.id, 'C1');
                };
                if (classIndex === 1) {
                    const node1a = await context.create(TestLocalizedData2, {
                        id: 'C1',
                        code: 'C1',
                        strProp: '',
                        dateProp: date.make(2019, 12, 31),
                        decimalProp: 1.0,
                        text: 'hello C1',
                    });
                    await node1a.$.save();
                    const node2a = await context.create(TestLocalizedData2, {
                        id: 'C2',
                        code: 'C2',
                        strProp: 'STR2',
                        text: 'hello C2',
                    });
                    await node2a.$.save();

                    await testComplexFilter({
                        filter: {
                            _or: [{ text: 'hello C1' }, { text: 'hello C3' }],
                        },
                    } as NodeQueryOptions);
                } else if (classIndex === 0) {
                    const node1a = await context.create(classToUse, {
                        id: 'C1',
                        code: 'C1',
                        text: 'hello C1',
                    });
                    await node1a.$.save();
                    const node2a = await context.create(classToUse, {
                        id: 'C2',
                        code: 'C2',
                        text: 'hello C2',
                    });
                    await node2a.$.save();

                    await testComplexFilter({
                        filter: {
                            _or: [{ text: 'hello C1' }, { text: 'hello C3' }],
                        },
                    } as NodeQueryOptions);
                }
            }));

        it('filtering delete on localized property', () =>
            Test.uncommitted(async context => {
                const testDeleteFilter = async () => {
                    const result = await context
                        .query(classToUse, {
                            filter: {
                                text: { _regex: 'C1' },
                            },
                        })
                        .toArray();

                    assert.equal(result.length, 1);
                    const deleted = await context.deleteMany(classToUse, {
                        text: { _regex: 'C1' },
                    });
                    assert.equal(deleted, 1);
                    const result2 = await context
                        .query(classToUse, {
                            filter: {
                                text: { _regex: 'C1' },
                            },
                        })
                        .toArray();

                    assert.equal(result2.length, 0);

                    const result3 = await context
                        .query(classToUse, {
                            filter: {
                                code: 'C2',
                            },
                        })
                        .toArray();

                    assert.equal(result3.length, 1);
                    assert.equal(await result3[0].code, 'C2');
                };
                if (classIndex === 1) {
                    const node1a = await context.create(TestLocalizedData2, {
                        id: 'C1',
                        code: 'C1',
                        strProp: '',
                        dateProp: date.today(),
                        decimalProp: 1.0,
                        text: 'hello C1',
                    });
                    await node1a.$.save();
                    const node2a = await context.create(TestLocalizedData2, {
                        id: 'C2',
                        code: 'C2',
                        strProp: 'STR2',
                        text: 'hello C2',
                    });
                    await node2a.$.save();

                    await testDeleteFilter();
                } else if (classIndex === 0) {
                    const node1a = await context.create(TestLocalizedData, {
                        id: 'C1',
                        code: 'C1',
                        text: 'hello C1',
                    });
                    await node1a.$.save();
                    const node2a = await context.create(TestLocalizedData, {
                        id: 'C2',
                        code: 'C2',
                        text: 'hello C2',
                    });
                    await node2a.$.save();

                    await testDeleteFilter();
                }
            }));

        it('filtering on more than one localized property', async () => {
            if (classIndex === 1) {
                await Test.uncommitted(async context => {
                    const testFilter = async () => {
                        const result = await context
                            .query(TestLocalizedData2, {
                                filter: {
                                    _or: [{ text: 'hello C1' }, { text2: 'bye C2' }],
                                },
                            })
                            .toArray();

                        assert.equal(await result[0].code, 'C1');
                        assert.equal(await result[0].text, 'hello C1');
                        assert.equal(await result[1].code, 'C2');
                        assert.equal(await result[1].text2, 'bye C2');
                    };
                    const node1a = await context.create(TestLocalizedData2, {
                        id: 'C1',
                        code: 'C1',
                        strProp: 'XXX',
                        dateProp: date.today(),
                        decimalProp: 1.0,
                        text: 'hello C1',
                        text2: 'bye C1',
                    });
                    await node1a.$.save();

                    const node2a = await context.create(TestLocalizedData2, {
                        id: 'C2',
                        code: 'C2',
                        strProp: 'STR2',
                        text: 'hello C2',
                        text2: 'bye C2',
                    });
                    await node2a.$.save();

                    await testFilter();
                });
            }
        });

        it('filtering on default value localized property', async () => {
            if (classIndex === 1) {
                await Test.uncommitted(async context => {
                    const node1a = await context.create(TestLocalizedData2, {
                        id: 'C1',
                        code: 'C1',
                        strProp: 'XXX',
                        dateProp: date.today(),
                        decimalProp: 1.0,
                        text: 'hello C1',
                    });
                    await node1a.$.save();

                    const result = await context
                        .query(TestLocalizedData2, {
                            filter: {
                                _and: [{ text: 'hello C1' }, { text2: 'hello C1' }],
                            },
                        })
                        .toArray();
                    assert.equal(await result[0].code, 'C1');
                    assert.equal(await result[0].text, 'hello C1');
                    assert.equal(await result[0].text2, 'hello C1');
                });
            }
        });

        it('localized property with different concatenator', async () => {
            if (classIndex === 1) {
                await Test.uncommitted(async context => {
                    const node1a = await context.create(TestLocalizedData2, {
                        id: 'C1',
                        code: 'C1',
                        strProp: 'XXX',
                        dateProp: date.today(),
                        decimalProp: 1.0,
                        text2: 'Bye C2',
                    });
                    await node1a.$.save();

                    const result = await context
                        .query(TestLocalizedData2, {
                            filter: {
                                text2: 'Bye C2',
                            },
                        })
                        .toArray();

                    assert.equal(await result[0].code, 'C1');
                    assert.equal(await result[0].text2, 'Bye C2');
                });
            }
        });

        it('filtering delete on more than one localized property', async () => {
            if (classIndex === 1) {
                await Test.uncommitted(async context => {
                    const testDeleteFilter = async () => {
                        const result = await context
                            .query(TestLocalizedData2, {
                                filter: {
                                    _or: [{ text: 'hello C1' }, { text2: 'Bye C2' }],
                                },
                            })
                            .toArray();

                        assert.equal(result.length, 2);

                        const deleted = await context.deleteMany(TestLocalizedData2, {
                            _or: [{ text: 'hello C1' }, { text2: 'Bye C2' }],
                        });
                        assert.equal(deleted, 2);

                        const result2 = await context.query(TestLocalizedData2).toArray();

                        assert.equal(result2.length, 0);
                    };
                    const node1a = await context.create(TestLocalizedData2, {
                        id: 'C1',
                        code: 'C1',
                        strProp: 'XXX',
                        dateProp: date.today(),
                        decimalProp: 1.0,
                        text: 'hello C1',
                        text2: 'bye C1',
                    });
                    await node1a.$.save();
                    const node2a = await context.create(TestLocalizedData2, {
                        id: 'C2',
                        code: 'C2',
                        strProp: 'STR2',
                        text: 'hello C2',
                        text2: 'Bye C2',
                    });
                    await node2a.$.save();

                    await testDeleteFilter();
                });
            }
        });

        it('should propagate default text', async () => {
            await Test.committed(async context => {
                await context.setTestLocale('en-US');
                const node = await context.create(classToUse, {
                    id: 'C1',
                    code: 'C1',
                    text: 'Color',
                });
                await node.$.save();
            });

            // The 'en' value should have been reported to all the other languages
            await checkLocales(
                classToUse,
                { id: 'C1' },
                {
                    'en-US': 'Color',
                    'en-GB': 'Color',
                    'fr-FR': 'Color',
                },
            );

            await Test.committed(async context => {
                await context.setTestLocale('en-US');
                const node = await context.read(classToUse, { id: 'C1' }, { forUpdate: true });
                await node.$.set({ text: 'New color' });
                await node.$.save();
            });

            // The 'en' value should have been reported to all the other languages
            await checkLocales(
                classToUse,
                { id: 'C1' },
                {
                    'en-US': 'New color',
                    'en-GB': 'Color',
                    'fr-FR': 'Color',
                },
            );

            await Test.committed(async context => {
                await context.setTestLocale('en-GB');
                const node = await context.read(classToUse, { id: 'C1' }, { forUpdate: true });
                await node.$.set({ text: 'Colour' });
                await node.$.save();
            });

            // The 'en' value should have been reported to all the other languages
            await checkLocales(
                classToUse,
                { id: 'C1' },
                {
                    'en-US': 'New color',
                    'en-GB': 'Colour',
                    'fr-FR': 'Colour',
                },
            );

            await Test.committed(async context => {
                await context.setTestLocale('fr-FR');
                const node = await context.read(classToUse, { id: 'C1' }, { forUpdate: true });
                await node.$.set({ text: 'Couleur' });
                await node.$.save();
            });

            // Only the 'fr' should have been changed
            await checkLocales(
                classToUse,
                { id: 'C1' },
                {
                    'en-US': 'New color',
                    'en-GB': 'Colour',
                    'fr-FR': 'Couleur',
                },
            );

            await Test.committed(async context => {
                await context.setTestLocale('fr-FR');
                const node = await context.create(classToUse, {
                    id: 'C2',
                    code: 'C2',
                    text: 'Couleur',
                });
                await node.$.save();
            });

            await checkLocales(
                classToUse,
                { id: 'C2' },
                {
                    'en-US': 'Couleur',
                    'fr-FR': 'Couleur',
                },
            );
        });

        it('should create, update and read node with localized property (several languages)', async () => {
            await Test.committed(async context => {
                await context.setTestLocale('en-US');
                const node1a = await context.create(classToUse, {
                    id: 'C1',
                    code: 'C1',
                    text: 'hello world',
                });
                await node1a.$.save();

                await context.setTestLocale('fr-FR');
                const node1b = await context.read(classToUse, { id: 'C1' }, { forUpdate: true });
                await node1b.$.set({ text: 'bonjour monde' });
                await node1b.$.save();
            });
            await Test.withReadonlyContext(async context => {
                await context.setTestLocale('en-US');
                const node1b = await context.read(classToUse, { id: 'C1' });
                assert.equal(await node1b.text, 'hello world');
            });
            await Test.withReadonlyContext(async context => {
                await context.setTestLocale('fr-FR');
                const node1b = await context.read(classToUse, { id: 'C1' });
                assert.equal(await node1b.text, 'bonjour monde');
            });
            await Test.withCommittedContext(async context => {
                await context.setTestLocale('de-DE');
                const node1b = await context.read(classToUse, { id: 'C1' });
                assert.equal(await node1b.text, 'hello world'); // default is en

                // Since the master locale for en is en-GB, the locale language value will not be updated when updating en-US
                let node1c = await context.read(classToUse, { id: 'C1' }, { forUpdate: true });
                await context.setTestLocale('en-US');
                await node1c.$.set({ text: 'bye bye' });
                await node1c.$.save();
                node1c = await context.read(classToUse, { id: 'C1' }, { forUpdate: true });
                await context.setTestLocale('de-DE');
                await node1c.$.set({ text: 'auf wiedersehen' });
                await node1c.$.save();
            });
            await Test.withReadonlyContext(async context => {
                await context.setTestLocale('en-US');
                const node1d = await context.read(classToUse, { id: 'C1' });
                assert.equal(await node1d.text, 'bye bye');
            });
            await Test.withReadonlyContext(async context => {
                await context.setTestLocale('en-GB');
                const node1d = await context.read(classToUse, { id: 'C1' });
                assert.equal(await node1d.text, 'hello world');
            });
            await Test.withReadonlyContext(async context => {
                await context.setTestLocale('fr-FR');
                const node1d = await context.read(classToUse, { id: 'C1' });
                assert.equal(await node1d.text, 'bonjour monde');
            });
            await Test.withReadonlyContext(async context => {
                await context.setTestLocale('de-DE');
                const node1d = await context.read(classToUse, { id: 'C1' });
                assert.equal(await node1d.text, 'auf wiedersehen');
            });
        });

        async function createNode(id: string, translations: { text: string; locale: string }[]): Promise<void> {
            await Test.committed(async context => {
                await context.setTestLocale(translations[0].locale);
                const node1 = await context.create(classToUse, {
                    id,
                    code: id,
                    text: translations[0].text,
                });
                await node1.$.save();
            });
            await Test.committed(async context => {
                await context.setTestLocale(translations[1].locale);
                const node1 = await context.read(classToUse, { id }, { forUpdate: true });
                await node1.$.set({ text: translations[1].text });
                await node1.$.save();
                await context.setTestLocale(translations[0].locale);
            });
        }
        it('should delete text records when node is deleted with static method (PK value)', async () => {
            await createNode('C1', [
                { text: 'hello world', locale: 'en-US' },
                { text: 'bonjour monde', locale: 'fr-FR' },
            ]);
            await Test.committed(context => context.deleteMany(classToUse, { id: 'C1' }));
            await createNode('C1', [
                { text: 'bye bye', locale: 'en-US' },
                { text: 'auf wiedersehen', locale: 'de-DE' },
            ]);

            await Test.withReadonlyContext(async context => {
                await context.setTestLocale('en-US');
                const node1a = await context.read(classToUse, { id: 'C1' });
                assert.equal(await node1a.text, 'bye bye');
            });
            await Test.withReadonlyContext(async context => {
                await context.setTestLocale('fr-FR');
                const node1b = await context.read(classToUse, { id: 'C1' });
                assert.equal(await node1b.text, 'bye bye'); // from default en
            });
            await Test.withReadonlyContext(async context => {
                await context.setTestLocale('de-DE');
                const node1c = await context.read(classToUse, { id: 'C1' });
                assert.equal(await node1c.text, 'auf wiedersehen');
            });
        });
        it('should delete text records when node is deleted with static method (complex filter)', async () => {
            await createNode('C1', [
                { text: 'hello world', locale: 'en-US' },
                { text: 'bonjour monde', locale: 'fr-FR' },
            ]);
            await Test.committed(context => context.deleteMany(classToUse, { id: 'C1' }));
            await createNode('C1', [
                { text: 'bye bye', locale: 'en-US' },
                { text: 'auf wiedersehen', locale: 'de-DE' },
            ]);

            await Test.withReadonlyContext(async context => {
                await context.setTestLocale('en-US');
                const node1d = await context.read(classToUse, { id: 'C1' });
                assert.equal(await node1d.text, 'bye bye');
            });
            await Test.withReadonlyContext(async context => {
                await context.setTestLocale('fr-FR');
                const node1d = await context.read(classToUse, { id: 'C1' });
                assert.equal(await node1d.text, 'bye bye'); // from default
            });
            await Test.withReadonlyContext(async context => {
                await context.setTestLocale('de-DE');
                const node1d = await context.read(classToUse, { id: 'C1' });
                assert.equal(await node1d.text, 'auf wiedersehen');
            });
        });
        it('should delete text records when node is deleted after read', async () => {
            await createNode('C1', [
                { text: 'hello world', locale: 'en-US' },
                { text: 'bonjour monde', locale: 'fr-FR' },
            ]);

            await Test.committed(async context => {
                const node1 = await context.read(classToUse, { id: 'C1' }, { forUpdate: true });
                await node1.$.delete();
            });

            await createNode('C1', [
                { text: 'bye bye', locale: 'en-US' },
                { text: 'auf wiedersehen', locale: 'de-DE' },
            ]);

            await Test.withReadonlyContext(async context => {
                await context.setTestLocale('en-US');
                const node1d = await context.read(classToUse, { id: 'C1' });
                assert.equal(await node1d.text, 'bye bye');
            });

            await Test.withReadonlyContext(async context => {
                await context.setTestLocale('fr-FR');
                const node1d = await context.read(classToUse, { id: 'C1' });
                assert.equal(await node1d.text, 'bye bye'); // from default
            });
            await Test.withReadonlyContext(async context => {
                await context.setTestLocale('de-DE');
                const node1d = await context.read(classToUse, { id: 'C1' });
                assert.equal(await node1d.text, 'auf wiedersehen');
            });
        });

        it('set locale:', () =>
            Test.withUncommittedContext(async context => {
                // locale is base therefore should return default language and default locale
                await context.setTestLocale('base');
                assert.equal(context.currentLocaleLanguage, 'en');
                assert.equal(context.currentLocale, 'en-GB');
                assert.equal(context.isMasterLocale(), true);

                await context.setTestLocale('en-US');
                assert.equal(context.currentLocaleLanguage, 'en');
                assert.equal(context.currentLocale, 'en-US');
                assert.equal(context.isMasterLocale(), false);

                await context.setTestLocale('de-DE');
                assert.equal(context.currentLocaleLanguage, 'de');

                // locale is wrong-locale therefore currentLanguage should return default language
                await assert.isRejected(context.setTestLocale('wrong-locale'), 'Locale wrong-locale not supported');
                // reverted locale after error
                assert.equal(context.currentLocaleLanguage, 'de');
                assert.equal(context.currentLocale, 'de-DE');

                await context.setTestLocale('en-XX');
                assert.equal(context.currentLocaleLanguage, 'en');
                assert.equal(context.isMasterLocale(), false);
            }));

        it('set language through locale:', () =>
            Test.withUncommittedContext(async context => {
                await context.setTestLocale('en-US');
                // language is en therefore locale changes to en-US
                assert.equal(context.currentLocaleLanguage, 'en');
                assert.equal(context.isMasterLocale(), false);

                await context.setTestLocale('de-DE');
                assert.equal(context.currentLocaleLanguage, 'de');
                assert.equal(context.isMasterLocale(), true);

                await assert.isRejected(context.setTestLocale('FOO'), 'Locale FOO not supported');

                // locale and language remain the same after error
                assert.equal(context.currentLocaleLanguage, 'de');
                assert.equal(context.currentLocale, 'de-DE');
            }));

        it('set locale with test options:', () =>
            Test.withUncommittedContext(
                context => {
                    assert.equal(context.currentLocaleLanguage, 'de');
                },
                {
                    config: ConfigManager.current,
                    locale: 'de-DE',
                },
            ));
        it('should propagate default text (changing locale)', async () => {
            await Test.committed(async context => {
                await context.setTestLocale('en-US');
                const node = await context.create(classToUse, {
                    id: 'C1',
                    code: 'C1',
                    text: 'English text',
                });
                await node.$.save();
            });

            // The 'en' value should have been reported to all the other languages
            await checkLocales(
                classToUse,
                { id: 'C1' },
                {
                    'en-US': 'English text',
                    'fr-FR': 'English text',
                },
            );

            await Test.committed(async context => {
                await context.setTestLocale('en-US');
                const node = await context.read(classToUse, { id: 'C1' }, { forUpdate: true });
                await node.$.set({ text: 'New english text' });
                await node.$.save();
            });

            // The 'en' value should have been reported to all the other languages
            await checkLocales(
                classToUse,
                { id: 'C1' },
                {
                    'en-US': 'New english text',
                    'fr-FR': 'English text',
                },
            );

            await Test.committed(async context => {
                await context.setTestLocale('fr-FR');
                const node = await context.read(classToUse, { id: 'C1' }, { forUpdate: true });
                await node.$.set({ text: 'French text' });
                await node.$.save();
            });

            // Only the 'fr' should have been changed
            await checkLocales(
                classToUse,
                { id: 'C1' },
                {
                    'en-US': 'New english text',
                    'fr-FR': 'French text',
                },
            );

            await Test.committed(async context => {
                await context.setTestLocale('fr-FR');
                const node = await context.create(classToUse, {
                    id: 'C2',
                    code: 'C2',
                    text: 'French text',
                });
                await node.$.save();
            });

            await checkLocales(
                classToUse,
                { id: 'C2' },
                {
                    'en-US': 'French text',
                    'fr-FR': 'French text',
                },
            );
        });

        it('should sort by localized properties', async () => {
            await Test.committed(async context => {
                await context.setTestLocale('en-US');
                const node = await context.create(classToUse, {
                    id: 'C1',
                    code: 'C1',
                    text: 'vegetable',
                });
                await node.$.save();
            });
            await Test.committed(async context => {
                const node = await context.create(classToUse, {
                    id: 'C2',
                    code: 'C2',
                    text: 'meat',
                });
                await node.$.save();
            });
            await Test.committed(async context => {
                const node = await context.create(classToUse, {
                    id: 'C3',
                    code: 'C3',
                    text: 'fruit',
                });
                await node.$.save();
            });

            await Test.committed(async context => {
                await context.setTestLocale('fr-FR');
                const node = await context.read(classToUse, { id: 'C1' }, { forUpdate: true });
                await node.$.set({ text: 'légume' });
                await node.$.save();
            });
            await Test.committed(async context => {
                const node = await context.read(classToUse, { id: 'C2' }, { forUpdate: true });
                await node.$.set({ text: 'viande' });
                await node.$.save();
            });
            await Test.committed(async context => {
                const node = await context.read(classToUse, { id: 'C3' }, { forUpdate: true });
                await node.$.set({ text: 'fruit' });
                await node.$.save();
            });

            await Test.withReadonlyContext(async context => {
                const result = await context.query(classToUse, { orderBy: { text: 1 }, locale: 'en-US' }).toArray();
                assert.equal(await result[0].code, 'C3');
                assert.equal(await result[1].code, 'C2');
                assert.equal(await result[2].code, 'C1');
            });

            await Test.withReadonlyContext(async context => {
                const result = await context.query(classToUse, { orderBy: { text: 1 }, locale: 'fr-FR' }).toArray();
                assert.equal(await result[0].code, 'C3');
                assert.equal(await result[1].code, 'C1');
                assert.equal(await result[2].code, 'C2');
            });
        });
        it('should handle the fallback values correctly', async () => {
            // Current locale --> Current locale language code --> Default locale language code
            await Test.committed(async context => {
                await context.setTestLocale('fr-CA');
                const node = await context.create(classToUse, {
                    id: 'C1',
                    code: 'C1',
                    text: 'voisin',
                });
                await node.$.save();
            });

            await checkLocales(
                classToUse,
                { id: 'C1' },
                {
                    'en-US': 'voisin',
                    'en-GB': 'voisin',
                    'fr-FR': 'voisin',
                    'fr-CA': 'voisin',
                    'fr-CH': 'voisin',
                    'de-DE': 'voisin',
                },
            );

            await Test.committed(async context => {
                await context.setTestLocale('en-US');
                const node = await context.read(classToUse, { id: 'C1' }, { forUpdate: true });
                await node.$.set({ text: 'neighbor' });
                await node.$.save();
            });

            await checkLocales(
                classToUse,
                { id: 'C1' },
                {
                    'en-US': 'neighbor',
                    'en-GB': 'voisin',
                    'fr-FR': 'voisin',
                    'fr-CA': 'voisin',
                    'fr-CH': 'voisin',
                    'de-DE': 'voisin',
                },
            );

            await Test.committed(async context => {
                await context.setTestLocale('en-GB');
                const node = await context.read(classToUse, { id: 'C1' }, { forUpdate: true });
                await node.$.set({ text: 'neighbour' });
                await node.$.save();
            });

            await checkLocales(
                classToUse,
                { id: 'C1' },
                {
                    'en-US': 'neighbor',
                    'en-GB': 'neighbour',
                    'fr-FR': 'voisin',
                    'fr-CA': 'voisin',
                    'fr-CH': 'voisin',
                    'de-DE': 'neighbour',
                },
            );

            await Test.committed(async context => {
                await context.setTestLocale('fr-FR');
                const node = await context.read(classToUse, { id: 'C1' }, { forUpdate: true });
                await node.$.set({ text: 'voisine' });
                await node.$.save();
            });

            await checkLocales(
                classToUse,
                { id: 'C1' },
                {
                    'en-US': 'neighbor',
                    'en-GB': 'neighbour',
                    'fr-FR': 'voisine',
                    'fr-CA': 'voisin',
                    'fr-CH': 'voisine',
                    'de-DE': 'neighbour',
                },
            );
        });

        it('should sort based on the collation for current locale', async () => {
            await Test.committed(async context => {
                const node = await context.create(classToUse, {
                    id: 'C1',
                    code: 'C1',
                    text: 'ñame',
                });
                await node.$.save();
            });
            await Test.committed(async context => {
                const node = await context.create(classToUse, {
                    id: 'C2',
                    code: 'C2',
                    text: 'next',
                });
                await node.$.save();
            });

            await Test.withReadonlyContext(async context => {
                const result = await context.query(classToUse, { orderBy: { text: 1 }, locale: 'en-US' }).toArray();
                assert.equal(await result[0].code, 'C1');
                assert.equal(await result[1].code, 'C2');
            });

            await Test.withReadonlyContext(async context => {
                const result = await context.query(classToUse, { orderBy: { text: 1 }, locale: 'es-ES' }).toArray();
                assert.equal(await result[0].code, 'C2');
                assert.equal(await result[1].code, 'C1');
            });
        });
        afterEach(() => clearTable(classToUse));
        after(() => restoreTables());
    });
});

describe('Localized properties withLocalizedTextAsJson', () => {
    before(async () => {
        await setup({ application: await createApplicationWithApi({ nodes: { TestLocalizedData } }) });
        await initTables([{ nodeConstructor: TestLocalizedData, data: [] }]);
    });

    it('should create localized property with stringified json', async () => {
        await Test.committed(context =>
            context.withLocalizedTextAsJson(async () => {
                const node1a = await context.create(TestLocalizedData, {
                    id: 'C1',
                    code: JSON.stringify({
                        base: 'C1',
                        en: 'C1-en',
                        fr: 'C1-fr',
                    }),
                    text: JSON.stringify({
                        en: 'vegetable',
                        'en-US': 'vegetable',
                        fr: 'légume',
                        'fr-FR': 'légume',
                    }),
                });
                await node1a.$.save();
            }),
        );
        await Test.committed(context =>
            context.withLocalizedTextAsJson(async () => {
                const node1a = await context.create(TestLocalizedData, {
                    id: 'C2',
                    code: 'C2',
                    text: JSON.stringify({
                        en: 'meat',
                        'en-US': 'meat',
                        fr: 'viande',
                        'fr-FR': 'viande',
                    }),
                });
                await node1a.$.save();
            }),
        );
        await Test.committed(async context => {
            await context.setTestLocale('en-US');
            await context.withLocalizedTextAsJson(async () => {
                const node1b = await context.read(TestLocalizedData, { id: 'C1' }, { forUpdate: true });
                assert.deepEqual(JSON.parse(await node1b.code), {
                    base: 'C1',
                    en: 'C1-en',
                    fr: 'C1-fr',
                });

                assert.deepEqual(JSON.parse(await node1b.text), {
                    base: 'vegetable',
                    en: 'vegetable',
                    fr: 'légume',
                    'en-US': 'vegetable',
                    'fr-FR': 'légume',
                });
                await node1b.$.set({ text: '{"en-US": "no more vegetable"}' });

                await node1b.$.save();
                const result = await context.read(TestLocalizedData, { id: 'C1' });

                assert.deepEqual(JSON.parse(await result.text), {
                    base: 'vegetable',
                    en: 'vegetable',
                    fr: 'légume',
                    'en-US': 'no more vegetable',
                    'fr-FR': 'légume',
                });
            });
        });
        await Test.readonly(async context => {
            await context.setTestLocale('fr-FR');
            const result = await context.read(TestLocalizedData, { id: 'C1' });
            assert.equal(await result.text, 'légume');
        });
        await Test.readonly(async context => {
            await context.setTestLocale('en-US');
            const result = await context.read(TestLocalizedData, { id: 'C1' });
            assert.equal(await result.text, 'no more vegetable');
        });
        await Test.readonly(async context => {
            await context.setTestLocale('en');
            const result = await context.read(TestLocalizedData, { id: 'C1' });
            assert.equal(await result.text, 'vegetable');
        });
        await Test.readonly(async context => {
            await context.setTestLocale('en');
            const result = await context.read(TestLocalizedData, { id: 'C2' });
            assert.equal(await result.text, 'meat');
        });
    });

    it('should reject too long localized text with stringified json', async () => {
        await Test.withUncommittedContext(context =>
            context.withLocalizedTextAsJson(async () => {
                const node = await context.create(TestLocalizedData, {
                    id: 'C3',
                    code: JSON.stringify({
                        base: 'this code is too long 1234567890123456789012345678901234567890',
                        en: 'this code is also too long 1234567890123456789012345678901234567890',
                        fr: 'this code is ok',
                    }),
                });
                await assert.isRejected(node.$.save(), 'The record was not created.');
                assert.deepEqual(context.diagnoses, [
                    {
                        message:
                            'this code is too long 1234567890123456789012345678901234567890 exceeds the maximum length allowed for this field: 32 characters.',
                        path: ['code'],
                        severity: ValidationSeverity.error,
                    },
                    {
                        message:
                            'this code is also too long 1234567890123456789012345678901234567890 exceeds the maximum length allowed for this field: 32 characters.',
                        path: ['code'],
                        severity: ValidationSeverity.error,
                    },
                ]);
            }),
        );
    });

    it('should reject too long localized text', async () => {
        await Test.withUncommittedContext(async context => {
            const node = await context.create(TestLocalizedData, {
                id: 'C3',
                code: 'this code is too long 1234567890123456789012345678901234567890',
            });
            assert.notOk(await node.$.trySave());
            assert.deepEqual(context.diagnoses, [
                {
                    message:
                        'this code is too long 1234567890123456789012345678901234567890 exceeds the maximum length allowed for this field: 32 characters.',
                    path: ['code'],
                    severity: ValidationSeverity.error,
                },
            ]);
        });
    });

    it('should throw an error if the wrong format is provided', async () => {
        const insertText = '{invalid-key: "wrong format"}';
        await assert.isRejected(
            Test.committed(context =>
                context.withLocalizedTextAsJson(async () => {
                    const node1a = await context.create(TestLocalizedData, {
                        id: 'C1',
                        code: 'C1',
                        text: insertText,
                    });
                    await node1a.$.save();
                }),
            ),
            `Localized property 'text' has an incorrect format: '${insertText}'. Expecting '{"key": "value"}'`,
        );
    });

    afterEach(() => clearTable(TestLocalizedData));

    after(() => restoreTables());
});
