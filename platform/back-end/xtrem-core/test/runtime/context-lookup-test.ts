import { ValidationSeverity } from '@sage/xtrem-shared';
import { assert, expect } from 'chai';
import { fixtures, testLookupsApplication } from '..';
import { Test } from '../../index';
import {
    documentData,
    documentLineData,
    initTables,
    nonVitalReferenceData,
    referredData,
    restoreTables,
    setup,
} from '../fixtures/index';
import {
    TestDocument,
    TestDocumentLookup,
    TestNonVitalReference,
    TestReferencedDocument,
    TestReferencedDocumentOther,
    TestReferred,
    TestVitalReferenceChildMandatory,
    TestVitalReferenceChildOptional,
    TestVitalReferenceParent,
} from '../fixtures/nodes';

const documentLookupData = [
    {
        _id: 1,
        parentControlInteger: 10,
    },
    {
        _id: 2,
        parentControlInteger: 11,
    },
];

const referencedDocumentData = [
    {
        _id: 1,
        lookupString: 'str1',
        controlInteger: 2,
        controlDetails: null,
    },
    {
        _id: 2,
        lookupString: 'str2',
        controlInteger: 10,
        controlDetails: null,
    },
    {
        _id: 3,
        lookupString: 'str2',
        controlInteger: 11,
        controlDetails: null,
    },
    {
        _id: 4,
        lookupString: 'str2',
        controlInteger: 10,
        controlDetails: null,
    },
    {
        _id: 5,
        lookupString: 'str99',
        controlInteger: 99,
        controlDetails: 1, // Reference to TestReferencedDocumentDetails
    },
];

const expectedCandidates = [
    {
        _id: 2,
        lookupString: 'str2',
        controlInteger: 10,
    },
    {
        _id: 4,
        lookupString: 'str2',
        controlInteger: 10,
    },
];

const expectedNoControl = [
    ...expectedCandidates,
    {
        _id: 3,
        lookupString: 'str2',
        controlInteger: 11,
    },
];

const referencedDocumentDetailsData = [
    {
        _id: 1,
        text: 'text1',
    },
    {
        _id: 2,
        text: 'text2',
    },
    {
        _id: 3,
        text: 'text3',
    },
];

describe('Context lookup method', () => {
    before(async () => {
        await setup({ application: await testLookupsApplication.application });
        await initTables([
            { nodeConstructor: fixtures.nodes.TestReferencedDocumentDetails, data: referencedDocumentDetailsData },
            { nodeConstructor: fixtures.nodes.TestReferencedDocument, data: referencedDocumentData },
            { nodeConstructor: fixtures.nodes.TestDocumentLookup, data: documentLookupData },
            { nodeConstructor: fixtures.nodes.TestReferred, data: referredData },
            { nodeConstructor: fixtures.nodes.TestDocument, data: documentData },
            { nodeConstructor: fixtures.nodes.TestDocumentLine, data: documentLineData },
            { nodeConstructor: TestVitalReferenceChildMandatory, data: [] },
            { nodeConstructor: TestVitalReferenceChildOptional, data: [] },
            { nodeConstructor: TestVitalReferenceParent, data: [] },
            { nodeConstructor: TestNonVitalReference, data: nonVitalReferenceData },
        ]);
    });

    after(() => restoreTables());

    it('works with an id and both lookup and control filters', () =>
        Test.withReadonlyContext(async context => {
            const lookupParameters = await context.makeLookupQueryParameters(TestDocumentLookup, 'reference', {
                _id: '1',
            });
            const candidates = await context
                .lookup(lookupParameters)
                .map(async (candidate: TestReferencedDocument) => {
                    return {
                        _id: candidate._id,
                        lookupString: await candidate.lookupString,
                        controlInteger: await candidate.controlInteger,
                    };
                })
                .toArray();

            assert.equal(candidates.length, expectedCandidates.length);
            expectedCandidates.forEach(expectedCandidate => assert.deepInclude(candidates, expectedCandidate));
            candidates.forEach(candidate => expect(candidate instanceof TestReferencedDocument));
        }));

    it('works with data', () =>
        Test.withReadonlyContext(async context => {
            const entryData = { parentControlInteger: 10 } as any;
            const lookupParameters = await context.makeLookupQueryParameters(TestDocumentLookup, 'reference', {
                data: entryData,
            });
            const candidates = await context
                .lookup(lookupParameters)
                .map(async (candidate: TestReferencedDocument) => {
                    return {
                        _id: candidate._id,
                        lookupString: await candidate.lookupString,
                        controlInteger: await candidate.controlInteger,
                    };
                })
                .toArray();

            assert.equal(candidates.length, expectedCandidates.length);
            expectedCandidates.forEach(expectedCandidate => assert.deepInclude(candidates, expectedCandidate));
            candidates.forEach(candidate => expect(candidate instanceof TestReferencedDocument));
        }));

    it('works with data as empty object and no _id', () =>
        Test.withReadonlyContext(async context => {
            const lookupParameters = await context.makeLookupQueryParameters(TestDocumentLookup, 'noControlReference', {
                data: {},
            });
            const candidates = await context
                .lookup(lookupParameters)
                .map(async (candidate: TestReferencedDocument) => {
                    return {
                        _id: candidate._id,
                        lookupString: await candidate.lookupString,
                        controlInteger: await candidate.controlInteger,
                    };
                })
                .toArray();

            assert.equal(candidates.length, expectedNoControl.length);
            expectedNoControl.forEach(expectedNoControlCandidate =>
                assert.deepInclude(candidates, expectedNoControlCandidate),
            );
            candidates.forEach(candidate => expect(candidate instanceof TestReferencedDocument));
        }));

    it('works with data and _id', () =>
        Test.withReadonlyContext(async context => {
            // No data passed
            let lookupParameters = await context.makeLookupQueryParameters(TestDocumentLookup, 'dynamicReference', {
                _id: '1',
            });
            let candidates = await context
                .lookup(lookupParameters)
                .map(async (candidate: TestReferencedDocumentOther) => {
                    return {
                        _id: candidate._id,
                        lookupString: await candidate.lookupString,
                        controlInteger: await candidate.controlInteger,
                    };
                })
                .toArray();

            // all candidates will have lookupString property1 as parentControlInteger is 10

            candidates.forEach(candidate => expect(candidate.lookupString === 'str1'));

            // pass data which makes parentControlInteger is 11
            lookupParameters = await context.makeLookupQueryParameters(TestDocumentLookup, 'dynamicReference', {
                _id: '1',
                data: { parentControlInteger: 11 },
            });
            candidates = await context
                .lookup(lookupParameters)
                .map(async (candidate: TestReferencedDocumentOther) => {
                    return {
                        _id: candidate._id,
                        lookupString: await candidate.lookupString,
                        controlInteger: await candidate.controlInteger,
                    };
                })
                .toArray();

            // all candidates will have lookupString property2 as parentControlInteger is 11

            candidates.forEach(candidate => expect(candidate.lookupString === 'str2'));
        }));

    it('works with _id in data', () =>
        Test.withReadonlyContext(async context => {
            // No data passed
            let lookupParameters = await context.makeLookupQueryParameters(TestDocumentLookup, 'dynamicReference', {
                data: { _id: '1' },
            });
            let candidates = await context
                .lookup(lookupParameters)
                .map(async (candidate: TestReferencedDocumentOther) => {
                    return {
                        _id: candidate._id,
                        lookupString: await candidate.lookupString,
                        controlInteger: await candidate.controlInteger,
                    };
                })
                .toArray();

            // all candidates will have lookupString property1 as parentControlInteger is 10

            candidates.forEach(candidate => expect(candidate.lookupString === 'str1'));

            // pass data which makes parentControlInteger is 11
            lookupParameters = await context.makeLookupQueryParameters(TestDocumentLookup, 'dynamicReference', {
                data: { _id: '1', parentControlInteger: 11 },
            });
            candidates = await context
                .lookup(lookupParameters)
                .map(async (candidate: TestReferencedDocumentOther) => {
                    return {
                        _id: candidate._id,
                        lookupString: await candidate.lookupString,
                        controlInteger: await candidate.controlInteger,
                    };
                })
                .toArray();

            // all candidates will have lookupString property2 as parentControlInteger is 11

            candidates.forEach(candidate => expect(candidate.lookupString === 'str2'));
        }));

    it('works with _id and _id in data', () =>
        Test.withReadonlyContext(async context => {
            // No data passed
            let lookupParameters = await context.makeLookupQueryParameters(TestDocumentLookup, 'dynamicReference', {
                _id: '1',
                data: { _id: '1' },
            });
            let candidates = await context
                .lookup(lookupParameters)
                .map(async (candidate: TestReferencedDocumentOther) => {
                    return {
                        _id: candidate._id,
                        lookupString: await candidate.lookupString,
                        controlInteger: await candidate.controlInteger,
                    };
                })
                .toArray();

            // all candidates will have lookupString property1 as parentControlInteger is 10

            candidates.forEach(candidate => expect(candidate.lookupString === 'str1'));

            // pass data which makes parentControlInteger is 11
            lookupParameters = await context.makeLookupQueryParameters(TestDocumentLookup, 'dynamicReference', {
                _id: '1',
                data: { _id: '1', parentControlInteger: 11 },
            });
            candidates = await context
                .lookup(lookupParameters)
                .map(async (candidate: TestReferencedDocumentOther) => {
                    return {
                        _id: candidate._id,
                        lookupString: await candidate.lookupString,
                        controlInteger: await candidate.controlInteger,
                    };
                })
                .toArray();

            // all candidates will have lookupString property2 as parentControlInteger is 11

            candidates.forEach(candidate => expect(candidate.lookupString === 'str2'));
        }));

    it('works with query options', () =>
        Test.withReadonlyContext(async context => {
            const lookupParameters = await context.makeLookupQueryParameters(TestDocumentLookup, 'reference', {
                _id: '1',
            });
            const candidates = await context
                .lookup(lookupParameters, { first: 1 })
                .map(async (candidate: TestReferencedDocument) => {
                    return {
                        _id: candidate._id,
                        lookupString: await candidate.lookupString,
                        controlInteger: await candidate.controlInteger,
                    };
                })
                .toArray();

            assert.equal(candidates.length, 1);
            assert.deepEqual(candidates[0], expectedCandidates[0]);
            expect(candidates[0] instanceof TestReferencedDocument);
        }));

    it('works with no lookup filter', () =>
        Test.withReadonlyContext(async context => {
            const lookupParameters = await context.makeLookupQueryParameters(TestDocumentLookup, 'noLookupReference', {
                _id: '1',
            });
            const candidates = await context
                .lookup(lookupParameters)
                .map(async (candidate: TestReferencedDocument) => {
                    return {
                        _id: candidate._id,
                        lookupString: await candidate.lookupString,
                        controlInteger: await candidate.controlInteger,
                    };
                })
                .toArray();

            assert.equal(candidates.length, expectedCandidates.length);
            expectedCandidates.forEach(expectedCandidate => assert.deepInclude(candidates, expectedCandidate));
            candidates.forEach(candidate => expect(candidate instanceof TestReferencedDocument));
        }));

    it('works with no control filter', () =>
        Test.withReadonlyContext(async context => {
            const lookupParameters = await context.makeLookupQueryParameters(TestDocumentLookup, 'noControlReference', {
                _id: '1',
            });
            const candidates = await context
                .lookup(lookupParameters)
                .map(async (candidate: TestReferencedDocument) => {
                    return {
                        _id: candidate._id,
                        lookupString: await candidate.lookupString,
                        controlInteger: await candidate.controlInteger,
                    };
                })
                .toArray();

            assert.equal(candidates.length, expectedNoControl.length);
            expectedNoControl.forEach(expectedCandidate => assert.deepInclude(candidates, expectedCandidate));
            candidates.forEach(candidate => expect(candidate instanceof TestReferencedDocument));
        }));

    it('works with no filters', () =>
        Test.withReadonlyContext(async context => {
            const lookupParameters = await context.makeLookupQueryParameters(TestDocumentLookup, 'noFiltersReference', {
                _id: '1',
            });
            const candidates = await context
                .lookup(lookupParameters)
                .map(async (candidate: TestReferencedDocument) => {
                    return {
                        _id: candidate._id,
                        lookupString: await candidate.lookupString,
                        controlInteger: await candidate.controlInteger,
                        _customData: await candidate._customData,
                        controlDetails: (await candidate.controlDetails)?._id ?? null,
                    };
                })
                .toArray();

            assert.equal(candidates.length, referencedDocumentData.length);
            referencedDocumentData.forEach(expectedCandidate => assert.deepInclude(candidates, expectedCandidate));
            candidates.forEach(candidate => expect(candidate instanceof TestReferencedDocument));
        }));

    it('works with vital collection data passed in', () =>
        Test.withReadonlyContext(async context => {
            const lookupParameters = await context.makeLookupQueryParameters(TestDocument, 'mandatoryReference', {
                data: { lines: [{ _action: 'create', description: 'test' }], _id: '1' },
            });
            const candidates = await context
                .lookup(lookupParameters)
                .map(async (candidate: TestReferred) => {
                    return {
                        code: await candidate.code,
                        details: await candidate.details,
                        restricted: await candidate.restricted,
                    };
                })
                .toArray();
            assert.deepEqual(candidates, [
                {
                    code: 'REF1',
                    details: 'reference B1',
                    restricted: 'restricted1',
                },
                {
                    code: 'REF2',
                    details: 'reference A2',
                    restricted: 'restricted2',
                },
            ]);
        }));

    it('works with vital reference data passed in', () =>
        Test.withReadonlyContext(async context => {
            const lookupParameters = await context.makeLookupQueryParameters(TestVitalReferenceParent, 'nonVitalRef', {
                data: {
                    code: 'P1',
                    mandatoryVitalRef: { code: 'M1' },
                    optionalVitalRef: { code: 'O1', nonVitalRef: nonVitalReferenceData[0] },
                },
            });

            const candidates = await context
                .lookup(lookupParameters)
                .map(async (candidate: TestNonVitalReference) => {
                    return {
                        code: await candidate.code,
                    };
                })
                .toArray();
            // nonVitalRef on TestVitalReferenceParent is not null therefore the lookup filter is to only
            // return NONVITAL1
            assert.deepEqual(candidates, [
                {
                    code: 'NONVITAL1',
                },
            ]);
        }));

    it('works without optional vital reference data passed in', () =>
        Test.withReadonlyContext(async context => {
            const lookupParameters = await context.makeLookupQueryParameters(TestVitalReferenceParent, 'nonVitalRef', {
                data: {
                    code: 'P1',
                    mandatoryVitalRef: { code: 'M1' },
                },
            });
            const candidates = await context
                .lookup(lookupParameters)
                .map(async (candidate: TestNonVitalReference) => {
                    return {
                        code: await candidate.code,
                    };
                })
                .toArray();
            // nonVitalRef on TestVitalReferenceParent is not null therefore the lookup filter is to only
            // return all values starting with NONVITAL
            assert.deepEqual(candidates, [
                {
                    code: 'NONVITAL1',
                },
                {
                    code: 'NONVITAL2',
                },
                {
                    code: 'NONVITAL3',
                },
            ]);
        }));

    it('throws error if _id is not a number', () =>
        Test.withReadonlyContext(async context => {
            await assert.isRejected(
                context.makeLookupQueryParameters(TestDocumentLookup, 'otherReference', {
                    _id: 'X',
                }),
                'TestDocumentLookup.otherReference: _id passed to lookup is not a number.',
            );
        }));

    it('control filter is executed at validation and adds a diagnose in case of problem', () =>
        Test.withUncommittedContext(async context => {
            const reference = await context.read(TestReferencedDocument, { _id: referencedDocumentData[0]._id });
            const noLookupReference = await context.read(TestReferencedDocument, {
                _id: referencedDocumentData[1]._id,
            });
            const noControlReference = await context.read(TestReferencedDocument, {
                _id: referencedDocumentData[2]._id,
            });
            const noFiltersReference = await context.read(TestReferencedDocument, {
                _id: referencedDocumentData[3]._id,
            });

            const controlledByDetailsReference = await context.read(TestReferencedDocument, {
                _id: referencedDocumentData[4]._id,
            });

            const details = await context.read(fixtures.nodes.TestReferencedDocumentDetails, {
                _id: referencedDocumentDetailsData[0]._id,
            });

            const documentLookupNewData = {
                parentControlInteger: 10,
                reference,
                noLookupReference,
                noControlReference,
                noFiltersReference,
                details,
                controlledByDetailsReference,
            };

            const newDocumentLookup = await context.create(TestDocumentLookup, documentLookupNewData);
            assert.notOk(await newDocumentLookup.$.control());
            assert.deepEqual(context.diagnoses, [
                {
                    path: ['reference'],
                    severity: ValidationSeverity.error,
                    message: 'The record is not valid. You need to select a different record.',
                },
            ]);
        }));

    it('control filter is executed at validation and adds a diagnose in case of problem (references read in parent context)', () =>
        Test.withReadonlyContext(
            async context => {
                const reference = await context.read(TestReferencedDocument, { _id: referencedDocumentData[0]._id });
                const noLookupReference = await context.read(TestReferencedDocument, {
                    _id: referencedDocumentData[1]._id,
                });
                const noControlReference = await context.read(TestReferencedDocument, {
                    _id: referencedDocumentData[2]._id,
                });
                const noFiltersReference = await context.read(TestReferencedDocument, {
                    _id: referencedDocumentData[3]._id,
                });

                const controlledByDetailsReference = await context.read(TestReferencedDocument, {
                    _id: referencedDocumentData[4]._id,
                });

                // load the controlDetails in the parent context
                await (
                    await controlledByDetailsReference.controlDetails
                )?.text;

                await context.runInWritableContext(
                    async writableContext => {
                        const details = await writableContext.read(fixtures.nodes.TestReferencedDocumentDetails, {
                            _id: referencedDocumentDetailsData[0]._id,
                        });

                        // controlDetails is loaded in the outer context,
                        // details is loaded in the writable context
                        // controlledByDetailsReference control filter will determine equal even though there are loaded in different contexts
                        const documentLookupNewData = {
                            parentControlInteger: 10,
                            reference,
                            noLookupReference,
                            noControlReference,
                            noFiltersReference,
                            details,
                            controlledByDetailsReference,
                        };

                        const newDocumentLookup = await writableContext.create(
                            TestDocumentLookup,
                            documentLookupNewData,
                        );
                        assert.notOk(await newDocumentLookup.$.control());
                        assert.deepEqual(writableContext.diagnoses, [
                            {
                                path: ['reference'],
                                severity: ValidationSeverity.error,
                                message: 'The record is not valid. You need to select a different record.',
                            },
                        ]);
                    },
                    { noCommit: true },
                );
            },
            {
                source: 'customMutation',
            },
        ));

    it('can lookup on reference array property', () =>
        Test.withReadonlyContext(async context => {
            let lookupParameters = await context.makeLookupQueryParameters(TestDocumentLookup, 'referenceArrayLookup', {
                data: { _id: '1' },
            });
            let candidates = await context
                .lookup(lookupParameters)
                .map(async (candidate: TestReferencedDocument) => {
                    return {
                        _id: candidate._id,
                        lookupString: await candidate.lookupString,
                        controlInteger: await candidate.controlInteger,
                    };
                })
                .toArray();

            // all candidates will have lookupString property1 as parentControlInteger is 10

            candidates.forEach(candidate => expect(candidate.lookupString === 'str1'));

            // pass data which makes parentControlInteger is 11
            lookupParameters = await context.makeLookupQueryParameters(TestDocumentLookup, 'referenceArrayLookup', {
                data: { _id: '1', parentControlInteger: 11 },
            });
            candidates = await context
                .lookup(lookupParameters)
                .map(async (candidate: TestReferencedDocument) => {
                    return {
                        _id: candidate._id,
                        lookupString: await candidate.lookupString,
                        controlInteger: await candidate.controlInteger,
                    };
                })
                .toArray();

            // all candidates will have lookupString property2 as parentControlInteger is 11

            candidates.forEach(candidate => expect(candidate.lookupString === 'str2'));
        }));
});
