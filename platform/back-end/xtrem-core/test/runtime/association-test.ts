import { assert } from 'chai';
import { omit } from 'lodash';
import { fixtures, testAssociationReferenceApplication } from '..';
import { Test } from '../../index';
import {
    associationAssociatedData,
    associationVitalParentData,
    testCampusData,
    testCollectionData,
    testCourseData,
    testReferenceData,
    testStudentData,
} from '../fixtures/data/association-data';
import { initTables, setup } from '../fixtures/index';

const {
    TestStudent,
    TestCourse,
    TestStudentCourse,
    TestCampus,
    TestStudentCourseCampus,
    TestAssociationVitalParent,
    TestAssociationAssociated,
    TestAssociationItem,
    TestReference,
    TestCollection,
    TestReferenceCollection,
} = fixtures.nodes;

describe('Association nodes', () => {
    before(async () => {
        await setup({ application: await testAssociationReferenceApplication.application });
        await initTables([
            { nodeConstructor: TestStudent, data: testStudentData },
            { nodeConstructor: TestCourse, data: testCourseData },
            { nodeConstructor: TestStudentCourse, data: [] },
            { nodeConstructor: TestCampus, data: testCampusData },
            { nodeConstructor: TestStudentCourseCampus, data: [] },
            { nodeConstructor: TestAssociationVitalParent, data: associationVitalParentData },
            { nodeConstructor: TestAssociationAssociated, data: associationAssociatedData },
            { nodeConstructor: TestAssociationItem, data: [] },
            { nodeConstructor: TestReference, data: testReferenceData },
            { nodeConstructor: TestCollection, data: testCollectionData },
            { nodeConstructor: TestReferenceCollection, data: [] },
        ]);
    });

    // Some tests are based on the state of the database after a previous test, so we need to run them all and in order

    it('TestAssociationItem - Foreign keys on association references have delete cascade', () =>
        Test.withReadonlyContext(context => {
            const desc = Test.application
                .getFactoryByConstructor(TestAssociationItem)
                .table.getTableDefinition(context);

            assert.deepInclude(
                desc.foreignKeys!.map(fk => omit(fk, 'comment')),
                {
                    name: 'test_association_item_parent_fk',
                    targetTable: 'test_association_vital_parent',
                    targetColumnNames: ['_tenant_id', '_id'],
                    columnNames: ['_tenant_id', 'parent'],
                    onDeleteBehaviour: 'cascade',
                    isDeferrable: true,
                },
            );

            assert.deepInclude(
                desc.foreignKeys!.map(fk => omit(fk, 'comment')),
                {
                    name: 'test_association_item_associated_fk',
                    targetTable: 'test_association_associated',
                    targetColumnNames: ['_tenant_id', '_id'],
                    columnNames: ['_tenant_id', 'associated'],
                    onDeleteBehaviour: 'cascade',
                    isDeferrable: true,
                },
            );
        }));

    it('TestAssociationItem - check indexes - no sortValue', () =>
        Test.withReadonlyContext(context => {
            const desc = Test.application
                .getFactoryByConstructor(TestAssociationItem)
                .table.getTableDefinition(context);

            const idxs = desc.indexes!.map(idx => omit(idx, 'comment'));

            // Declared unique index on code
            assert.deepInclude(idxs, {
                name: 'test_association_item_ind0',
                isUnique: true,
                columns: [
                    { name: '_tenant_id', ascending: true },
                    { name: 'code', ascending: true },
                ],
            });

            // Declared index, flagged as natural key
            assert.deepInclude(idxs, {
                name: 'test_association_item_ind1',
                isUnique: true,
                columns: [
                    { name: '_tenant_id', ascending: true },
                    { name: 'parent', ascending: true },
                    { name: 'associated', ascending: true },
                ],
            });

            // Generated index with other parent combination
            assert.deepInclude(idxs, {
                name: 'test_association_item_ind2',
                isUnique: true,
                columns: [
                    { name: '_tenant_id', ascending: true },
                    { name: 'associated', ascending: true },
                    { name: 'parent', ascending: true },
                ],
            });
        }));

    it('TestStudentCourse - Both foreign keys on association references have delete cascade', () =>
        Test.withReadonlyContext(context => {
            const desc = Test.application.getFactoryByConstructor(TestStudentCourse).table.getTableDefinition(context);

            const fks = desc.foreignKeys!.map(fk => omit(fk, 'comment'));

            assert.deepInclude(fks, {
                name: 'test_student_course_student_fk',
                targetTable: 'test_student',
                targetColumnNames: ['_tenant_id', '_id'],
                columnNames: ['_tenant_id', 'student'],
                onDeleteBehaviour: 'cascade',
                isDeferrable: true,
            });

            assert.deepInclude(fks, {
                name: 'test_student_course_course_fk',
                targetTable: 'test_course',
                targetColumnNames: ['_tenant_id', '_id'],
                columnNames: ['_tenant_id', 'course'],
                onDeleteBehaviour: 'cascade',
                isDeferrable: true,
            });
        }));

    it('TestStudentCourse - Natural key on association parents and another unique index starting with other association parent', () =>
        Test.withReadonlyContext(context => {
            const desc = Test.application.getFactoryByConstructor(TestStudentCourse).table.getTableDefinition(context);

            const idxs = desc.indexes!.map(idx => omit(idx, 'comment'));

            assert.deepInclude(idxs, {
                name: 'test_student_course_ind0',
                isUnique: true,
                columns: [
                    { name: '_tenant_id', ascending: true },
                    { name: 'student', ascending: true },
                    { name: 'course', ascending: true },
                ],
            });

            assert.deepInclude(idxs, {
                name: 'test_student_course_ind1',
                isUnique: true,
                columns: [
                    { name: '_tenant_id', ascending: true },
                    { name: 'course', ascending: true },
                    { name: 'student', ascending: true },
                ],
            });
        }));

    it('TestStudentCourseCampus - Natural key on association parents and unique index starting with other association parents', () =>
        Test.withReadonlyContext(context => {
            const desc = Test.application
                .getFactoryByConstructor(TestStudentCourseCampus)
                .table.getTableDefinition(context);

            const idxs = desc.indexes!.map(idx => omit(idx, 'comment'));

            assert.deepInclude(idxs, {
                name: 'test_student_course_campus_ind0',
                isUnique: true,
                columns: [
                    { name: '_tenant_id', ascending: true },
                    { name: 'student', ascending: true },
                    { name: 'course', ascending: true },
                    { name: 'campus', ascending: true },
                ],
            });

            assert.deepInclude(idxs, {
                name: 'test_student_course_campus_ind1',
                isUnique: true,
                columns: [
                    { name: '_tenant_id', ascending: true },
                    { name: 'course', ascending: true },
                    { name: 'campus', ascending: true },
                    { name: 'student', ascending: true },
                ],
            });

            assert.deepInclude(idxs, {
                name: 'test_student_course_campus_ind2',
                isUnique: true,
                columns: [
                    { name: '_tenant_id', ascending: true },
                    { name: 'campus', ascending: true },
                    { name: 'student', ascending: true },
                    { name: 'course', ascending: true },
                ],
            });
        }));

    it('TestStudentCourseCampus - All 3 foreign keys on association references has delete cascade', () =>
        Test.withReadonlyContext(context => {
            const desc = Test.application
                .getFactoryByConstructor(TestStudentCourseCampus)
                .table.getTableDefinition(context);

            const fks = desc.foreignKeys!.map(fk => omit(fk, 'comment'));

            assert.deepInclude(fks, {
                name: 'test_student_course_campus_student_fk',
                targetTable: 'test_student',
                targetColumnNames: ['_tenant_id', '_id'],
                columnNames: ['_tenant_id', 'student'],
                onDeleteBehaviour: 'cascade',
                isDeferrable: true,
            });

            assert.deepInclude(fks, {
                name: 'test_student_course_campus_course_fk',
                targetTable: 'test_course',
                targetColumnNames: ['_tenant_id', '_id'],
                columnNames: ['_tenant_id', 'course'],
                onDeleteBehaviour: 'cascade',
                isDeferrable: true,
            });

            assert.deepInclude(fks, {
                name: 'test_student_course_campus_campus_fk',
                targetTable: 'test_campus',
                targetColumnNames: ['_tenant_id', '_id'],
                columnNames: ['_tenant_id', 'campus'],
                onDeleteBehaviour: 'cascade',
                isDeferrable: true,
            });
        }));

    it('can create association node', () =>
        Test.withCommittedContext(async context => {
            const createItem = await context.create(TestAssociationItem, {
                code: 'P1A1',
                parent: { _id: 1 },
                associated: { _id: 1 },
            });
            await createItem.$.save();

            const readItem = await context.read(TestAssociationItem, { code: 'P1A1' });
            assert.equal(await (await readItem.parent).code, 'P1');
            assert.equal(await (await readItem.associated).code, 'A1');
        }));

    it('can create student class association', () =>
        Test.withCommittedContext(async context => {
            const studentName = 'John Doe';
            const courseName = 'Math';

            const studentCourse = await context.create(TestStudentCourse, {
                student: { name: studentName },
                course: { name: courseName },
                someText: 'John Doe - Math',
            });
            await studentCourse.$.save();

            const student = await context.read(TestStudent, { name: studentName });
            const course = await context.read(TestCourse, { name: courseName });

            assert.equal(await student.courses.length, 1);
            assert.equal(await course.students.length, 1);
        }));

    it('can create second student class association', () =>
        Test.withCommittedContext(async context => {
            const studentName = 'John Doe';
            const courseName = 'Science';

            const studentCourse = await context.create(TestStudentCourse, {
                student: { name: studentName },
                course: { name: courseName },
                someText: 'John Doe - Science',
            });
            await studentCourse.$.save();

            const student = await context.read(TestStudent, { name: studentName });
            const course = await context.read(TestCourse, { name: courseName });

            assert.equal(await student.courses.length, 2);
            assert.equal(await course.students.length, 1);
        }));

    it('can create student class association from student', () =>
        Test.withCommittedContext(async context => {
            const studentName = 'Bob Johnson';
            const courseName = 'English';

            const student = await context.read(TestStudent, { name: studentName }, { forUpdate: true });

            await student.courses.append({
                course: { name: courseName },
                someText: 'Bob Johnson - English',
            });

            await student.$.save();

            const course = await context.read(TestCourse, { name: courseName });
            const courseStudents = await course.students.toArray();

            assert.equal(courseStudents.length, 1);
            assert.equal(await courseStudents[0].someText, 'Bob Johnson - English');
        }));

    it('can create course student association from course', () =>
        Test.withCommittedContext(async context => {
            const studentName = 'Bob Johnson';
            const courseName = 'Math';

            const course = await context.read(TestCourse, { name: courseName }, { forUpdate: true });

            await course.students.append({
                student: { name: studentName },
                someText: 'Bob Johnson - Math',
            });

            await course.$.save();

            const student = await context.read(TestStudent, { name: studentName });
            const studentCourses = await student.courses.toArray();

            assert.equal(studentCourses.length, 2);
            assert.equal(await studentCourses[1].someText, 'Bob Johnson - Math');
        }));

    it('can create campus course student association from campus', () =>
        Test.withCommittedContext(async context => {
            const studentName = 'Bob Johnson';
            const courseName = 'Math';
            const campusName = 'Main';

            const campus = await context.read(TestCampus, { name: campusName }, { forUpdate: true });

            await campus.studentCourses.append({
                student: { name: studentName },
                course: { name: courseName },
                someText: 'Main - Bob Johnson - Math',
            });

            await campus.$.save();

            const campusRead = await context.read(TestCampus, { name: campusName });
            const studentCourses = await campusRead.studentCourses.toArray();

            assert.equal(studentCourses.length, 1);
            assert.equal(await studentCourses[0].someText, 'Main - Bob Johnson - Math');
        }));

    it('delete cascade on course delete', () =>
        Test.withCommittedContext(async context => {
            const studentName = 'John Doe';
            const courseName = 'Math';
            const campusName = 'Main';

            const course = await context.read(TestCourse, { name: courseName });
            await context.delete(TestCourse, { _id: course._id });

            const student = await context.read(TestStudent, { name: studentName });
            assert.equal(await student.courses.length, 1);

            const campus = await context.read(TestCampus, { name: campusName });
            const studentCourses = await campus.studentCourses.toArray();

            assert.equal(studentCourses.length, 0);
        }));

    it('delete cascade on student delete', () =>
        Test.withCommittedContext(async context => {
            const studentName = 'Alice Smith';
            const courseName = 'English';

            const studentCourse = await context.create(TestStudentCourse, {
                student: { name: studentName },
                course: { name: courseName },
                someText: 'Alice Smith - English',
            });
            await studentCourse.$.save();

            const student = await context.read(TestStudent, { name: studentName });
            await context.delete(TestStudent, { _id: student._id });

            const course = await context.read(TestCourse, { name: courseName });
            assert.equal(await course.students.length, 1);
        }));

    it('can create association with reference and collection on associated nodes', () =>
        Test.withCommittedContext(async context => {
            const referenceName = 'Ref 1';
            const collectionName = 'Coll 1';

            const referenceCollection = await context.create(TestReferenceCollection, {
                reference: { name: referenceName },
                collection: { name: collectionName },
                someText: 'Ref 1 - Coll 1',
            });
            await referenceCollection.$.save();

            const testReference = await context.read(TestReference, { name: referenceName });
            const testCollection = await context.read(TestCollection, { name: collectionName });

            assert.equal((await testReference.reference)._id, 1);
            assert.equal(await testCollection.collection.length, 1);
        }));

    it('can create collection association from associated node', () =>
        Test.withCommittedContext(async context => {
            const referenceName = 'Ref 2';
            const collectionName = 'Coll 2';

            const testCollection = await context.read(TestCollection, { name: collectionName }, { forUpdate: true });

            await testCollection.collection.append({ reference: { name: referenceName }, someText: 'Ref 2 - Coll 2' });
            await testCollection.$.save();

            const testReference = await context.read(TestReference, { name: referenceName });
            assert.equal(await (await (await testReference.reference).collection).name, collectionName);
        }));

    it('can create reference association from associated node', () =>
        Test.withCommittedContext(async context => {
            const referenceName = 'Ref 3';
            const collectionName = 'Coll 3';

            const testReference = await context.read(TestReference, { name: referenceName }, { forUpdate: true });
            await testReference.$.set({
                reference: { collection: { name: collectionName }, someText: 'Ref 3 - Coll 3' },
            });
            await testReference.$.save();

            const testCollection = await (
                await context.read(TestCollection, { name: collectionName })
            ).collection.toArray();

            assert.equal(testCollection.length, 1);
            assert.equal(await (await testCollection[0].collection).name, 'Coll 3');
            assert.equal(await testCollection[0].someText, 'Ref 3 - Coll 3');
        }));
});
