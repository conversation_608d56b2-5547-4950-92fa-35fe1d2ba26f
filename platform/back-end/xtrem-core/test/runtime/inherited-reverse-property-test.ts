import { assert } from 'chai';
import { decorators, Node, Reference, Test } from '../../index';
import * as fixtures from '../fixtures';
import { codeDataType } from '../fixtures/data-types/data-types';
import { createApplicationWithApi, GraphQlHelper, graphqlSetup, restoreTables, setup } from '../fixtures/index';

@decorators.node<TestIrpBusinessEntity>({
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    indexes: [{ orderBy: { id: 1 }, isUnique: true, isNaturalKey: true }],
    isSetupNode: true,
})
export class TestIrpBusinessEntity extends Node {
    @decorators.stringProperty<TestIrpBusinessEntity, 'id'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly id: Promise<string>;

    @decorators.referenceProperty<TestIrpBusinessEntity, 'customer'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'businessEntity',
        node: () => TestIrpCustomer,
        isNullable: true,
    })
    readonly customer: Reference<TestIrpCustomer | null>;

    @decorators.booleanProperty<TestIrpBusinessEntity, 'isCustomer'>({
        isPublished: true,
        dependsOn: ['customer'],
        async getValue() {
            return (await this.customer) != null;
        },
    })
    readonly isCustomer: Promise<boolean>;

    @decorators.referenceProperty<TestIrpBusinessEntity, 'supplier'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'businessEntity',
        node: () => TestIrpSupplier,
        isNullable: true,
    })
    readonly supplier: Reference<TestIrpSupplier | null>;
}

@decorators.node<TestIrpBusinessRelation>({
    isAbstract: true,
    isPublished: true,
    storage: 'sql',
    isVitalReferenceChild: true,
    indexes: [{ orderBy: { _constructor: 1, businessEntity: 1 }, isUnique: true, isNaturalKey: true }],
})
export class TestIrpBusinessRelation extends Node {
    @decorators.referenceProperty<TestIrpBusinessRelation, 'businessEntity'>({
        isPublished: true,
        isStored: true,
        isVitalParent: true,
        node: () => TestIrpBusinessEntity,
    })
    readonly businessEntity: Promise<TestIrpBusinessEntity>;
}

@decorators.subNode<TestIrpCustomer>({
    extends: () => TestIrpBusinessRelation,
})
export class TestIrpCustomer extends TestIrpBusinessRelation {
    @decorators.stringProperty<TestIrpCustomer, 'customerName'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly customerName: Promise<string>;
}

@decorators.subNode<TestIrpSupplier>({
    extends: () => TestIrpBusinessRelation,
})
export class TestIrpSupplier extends TestIrpBusinessRelation {
    @decorators.stringProperty<TestIrpSupplier, 'supplierName'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly supplierName: Promise<string>;
}

let graphqlHelper: GraphQlHelper;

describe('Inherited reverse property', () => {
    before(async () =>
        setup({
            application: await createApplicationWithApi({
                nodes: { TestIrpBusinessEntity, TestIrpBusinessRelation, TestIrpCustomer, TestIrpSupplier },
            }),
        }),
    );

    /** Reset tables before each to start autoIncrement over for each test */
    before(async () => {
        await fixtures.initTables([
            { nodeConstructor: TestIrpBusinessEntity, data: [] },
            { nodeConstructor: TestIrpBusinessRelation, data: [] },
            { nodeConstructor: TestIrpCustomer, data: [] },
            { nodeConstructor: TestIrpSupplier, data: [] },
        ]);
        await Test.withCommittedContext(async context => {
            await (
                await context.create(TestIrpBusinessEntity, {
                    id: 'A',
                    customer: { customerName: 'customer A' },
                    supplier: { supplierName: 'supplier A' },
                })
            ).$.save();

            await (
                await context.create(TestIrpBusinessEntity, {
                    id: 'B',
                    customer: { customerName: 'customer B' },
                })
            ).$.save();

            await (
                await context.create(TestIrpBusinessEntity, {
                    id: 'C',
                    supplier: { supplierName: 'supplier C' },
                })
            ).$.save();
        });
    });

    after(() => restoreTables());

    it('can query customers and suppliers', () =>
        Test.withContext(async context => {
            const customers = await context
                .query(TestIrpCustomer)
                .map(node => node.customerName)
                .toArray();
            assert.deepEqual(customers, ['customer A', 'customer B']);

            const suppliers = await context
                .query(TestIrpSupplier)
                .map(node => node.supplierName)
                .toArray();
            assert.deepEqual(suppliers, ['supplier A', 'supplier C']);
        }));

    it('can query business entities with filters on customer or supplier', () =>
        Test.withContext(async context => {
            const customerBusinessEntities = await context
                .query(TestIrpBusinessEntity, { filter: { isCustomer: true } })
                .map(node => node.id)
                .toArray();
            assert.deepEqual(customerBusinessEntities, ['A', 'B']);
        }));
});

describe('Query abstract node with _constructor key', () => {
    before(async () => {
        graphqlHelper = await graphqlSetup({
            application: await createApplicationWithApi({
                nodes: { TestIrpBusinessEntity, TestIrpBusinessRelation, TestIrpCustomer, TestIrpSupplier },
            }),
        });
    });

    /** Reset tables before each to start autoIncrement over for each test */
    before(async () => {
        await fixtures.initTables([
            { nodeConstructor: TestIrpBusinessEntity, data: [] },
            { nodeConstructor: TestIrpBusinessRelation, data: [] },
            { nodeConstructor: TestIrpCustomer, data: [] },
            { nodeConstructor: TestIrpSupplier, data: [] },
        ]);
        await Test.withCommittedContext(async context => {
            await (
                await context.create(TestIrpBusinessEntity, {
                    id: 'A',
                    customer: { customerName: 'customer A' },
                    supplier: { supplierName: 'supplier A' },
                })
            ).$.save();

            await (
                await context.create(TestIrpBusinessEntity, {
                    id: 'B',
                    customer: { customerName: 'customer B' },
                })
            ).$.save();

            await (
                await context.create(TestIrpBusinessEntity, {
                    id: 'C',
                    supplier: { supplierName: 'supplier C' },
                })
            ).$.save();
        });
    });

    after(() => restoreTables());

    it('can query abstract node with _contructor in keys', async () => {
        const result = await graphqlHelper.query(
            `{
                testIrpBusinessRelation {
                            query {
                                edges {
                                    node {
                                        businessEntity { id }
                                    }
                                    cursor
                                }
                                pageInfo {
                                    startCursor
                                    endCursor
                                    hasPreviousPage
                                    hasNextPage
                                }
                            }
                        }
                    }
                    `,
        );

        assert.deepEqual(result, {
            testIrpBusinessRelation: {
                query: {
                    edges: [
                        {
                            node: {
                                businessEntity: {
                                    id: 'A',
                                },
                            },
                            cursor: '["TestIrpCustomer","A"]#66',
                        },
                        {
                            cursor: '["TestIrpCustomer","B"]#95',
                            node: {
                                businessEntity: {
                                    id: 'B',
                                },
                            },
                        },
                        {
                            cursor: '["TestIrpSupplier","A"]#67',
                            node: {
                                businessEntity: {
                                    id: 'A',
                                },
                            },
                        },
                        {
                            cursor: '["TestIrpSupplier","C"]#01',
                            node: {
                                businessEntity: {
                                    id: 'C',
                                },
                            },
                        },
                    ],
                    pageInfo: {
                        endCursor: '["TestIrpSupplier","C"]#01',
                        hasNextPage: false,
                        hasPreviousPage: false,
                        startCursor: '["TestIrpCustomer","A"]#66',
                    },
                },
            },
        });
    });

    it('can navigate with pagination', async () => {
        let result = await graphqlHelper.query(
            `{
                testIrpBusinessRelation {
                            query (first: 1) {
                                edges {
                                    node {
                                        businessEntity { id }
                                    }
                                    cursor
                                }
                                pageInfo {
                                    startCursor
                                    endCursor
                                    hasPreviousPage
                                    hasNextPage
                                }
                            }
                        }
                    }
                    `,
        );

        assert.deepEqual(result, {
            testIrpBusinessRelation: {
                query: {
                    edges: [
                        {
                            node: {
                                businessEntity: {
                                    id: 'A',
                                },
                            },
                            cursor: '["TestIrpCustomer","A"]#66',
                        },
                    ],
                    pageInfo: {
                        endCursor: '["TestIrpCustomer","A"]#66',
                        hasNextPage: true,
                        hasPreviousPage: false,
                        startCursor: '["TestIrpCustomer","A"]#66',
                    },
                },
            },
        });

        result = await graphqlHelper.query(
            `{
                testIrpBusinessRelation {
                            query (first: 1, after: "['TestIrpCustomer','A']#66") {
                                edges {
                                    node {
                                        businessEntity { id }
                                    }
                                    cursor
                                }
                                pageInfo {
                                    startCursor
                                    endCursor
                                    hasPreviousPage
                                    hasNextPage
                                }
                            }
                        }
                    }
                    `,
        );

        assert.deepEqual(result, {
            testIrpBusinessRelation: {
                query: {
                    edges: [
                        {
                            cursor: '["TestIrpCustomer","B"]#95',
                            node: {
                                businessEntity: {
                                    id: 'B',
                                },
                            },
                        },
                    ],
                    pageInfo: {
                        endCursor: '["TestIrpCustomer","B"]#95',
                        hasNextPage: true,
                        hasPreviousPage: true,
                        startCursor: '["TestIrpCustomer","B"]#95',
                    },
                },
            },
        });

        result = await graphqlHelper.query(
            `{
                testIrpBusinessRelation {
                            query (last: 1, before: "['TestIrpSupplier','C']#01") {
                                edges {
                                    node {
                                        businessEntity { id }
                                    }
                                    cursor
                                }
                                pageInfo {
                                    startCursor
                                    endCursor
                                    hasPreviousPage
                                    hasNextPage
                                }
                            }
                        }
                    }
                    `,
        );

        assert.deepEqual(result, {
            testIrpBusinessRelation: {
                query: {
                    edges: [
                        {
                            cursor: '["TestIrpSupplier","A"]#67',
                            node: {
                                businessEntity: {
                                    id: 'A',
                                },
                            },
                        },
                    ],
                    pageInfo: {
                        endCursor: '["TestIrpSupplier","A"]#67',
                        hasNextPage: true,
                        hasPreviousPage: true,
                        startCursor: '["TestIrpSupplier","A"]#67',
                    },
                },
            },
        });
    });
});
