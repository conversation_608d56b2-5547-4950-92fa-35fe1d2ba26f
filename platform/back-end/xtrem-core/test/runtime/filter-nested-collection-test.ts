import { assert } from 'chai';
import { Test } from '../../index';
import { Context, Node, NodeQueryFilter, restoreTables } from '../../lib';
import { StaticThis } from '../../lib/decorators/index';
import { childData, createApplicationWithApi, grandParentData, initTables, parentData, setup } from '../fixtures/index';
import { TestFilterNestedChild, TestFilterNestedGrandParent, TestFilterNestedParent } from '../fixtures/nodes';

async function testFilter<T extends Node>(
    context: Context,
    c: StaticThis<T>,
    filter: NodeQueryFilter<T>,
    expectedResult: number[],
): Promise<void> {
    const docs = await context
        .query(c, {
            filter,
        })
        .toArray();
    assert.deepEqual(
        docs.map(d => d._id),
        expectedResult,
    );
}

describe('filters on nested collections', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi({
                nodes: {
                    TestFilterNestedGrandParent,
                    TestFilterNestedParent,
                    TestFilterNestedChild,
                },
            }),
        });
        await initTables([
            { nodeConstructor: TestFilterNestedGrandParent, data: grandParentData },
            { nodeConstructor: TestFilterNestedParent, data: parentData },
            { nodeConstructor: TestFilterNestedChild, data: childData },
        ]);
    });
    it("can't filter without quantifier", () =>
        Test.uncommitted(async context => {
            await assert.isRejected(
                testFilter(
                    context,
                    TestFilterNestedParent,
                    {
                        children: {
                            invVal: { _gte: 2 },
                        } as any,
                    },
                    [],
                ),
                'this.children: missing _atLeast/_atMost/_every/_none quantifier on nested filter',
            );
        }));
    it("can't filter without multiple quantifiers", () =>
        Test.uncommitted(async context => {
            await assert.isRejected(
                testFilter(
                    context,
                    TestFilterNestedParent,
                    {
                        children: {
                            _atLeast: 3,
                            _atMost: 3,
                            intVal: { _gte: 2 },
                        },
                    },
                    [],
                ),
                'this.children: only ONE _atLeast/_atMost/_every/_none quantifier on nested filter can be defined but found _atLeast,_atMost',
            );
        }));
    it('can filter on children - atLeast', () =>
        Test.uncommitted(context =>
            testFilter(
                context,
                TestFilterNestedParent,
                {
                    children: {
                        _atLeast: 3,
                        intVal: { _gte: 2 },
                    },
                },
                [2, 3, 4],
            ),
        ));

    it('can filter on children - atLeast + complex filter', () =>
        Test.uncommitted(context =>
            testFilter(
                context,
                TestFilterNestedParent,
                {
                    _and: [
                        {
                            _or: [
                                {
                                    children: {
                                        _atLeast: 3,
                                        intVal: { _eq: 2 },
                                    },
                                },
                                {
                                    children: {
                                        _atLeast: 3,
                                        intVal: { _gte: 1 },
                                    },
                                },
                            ],
                        },
                        { intVal: { _ne: 2 } },
                    ],
                },
                [1, 3, 4],
            ),
        ));

    it('can filter on children - atLeast without filter', () =>
        Test.uncommitted(context =>
            testFilter(
                context,
                TestFilterNestedParent,
                {
                    children: {
                        _atLeast: 2,
                    },
                },
                [1, 2, 3, 4],
            ),
        ));

    it('can filter on children - atMost without filter', () =>
        Test.uncommitted(context =>
            testFilter(
                context,
                TestFilterNestedParent,
                {
                    children: {
                        _atMost: 3,
                    },
                },
                [1, 5, 6],
            ),
        ));

    it('can filter on children - atMost', () =>
        Test.uncommitted(context =>
            testFilter(
                context,
                TestFilterNestedParent,
                {
                    children: {
                        _atMost: 2,
                        intVal: { _gte: 2 },
                    },
                },
                [1, 5, 6],
            ),
        ));

    it('can filter on children - none', async () => {
        await Test.uncommitted(context =>
            testFilter(
                context,
                TestFilterNestedParent,
                {
                    children: {
                        _none: true,
                        intVal: { _gt: 2 },
                    },
                },
                [5, 6],
            ),
        );

        await Test.uncommitted(context =>
            testFilter(
                context,
                TestFilterNestedParent,
                {
                    children: {
                        _none: true,
                        intVal: { _eq: 10 },
                    },
                },
                [1, 2, 3, 4, 5, 6],
            ),
        );

        await Test.uncommitted(context =>
            testFilter(
                context,
                TestFilterNestedParent,
                {
                    _and: [
                        {
                            children: {
                                _atLeast: 1,
                                intVal: { _ne: 10 },
                            },
                        },
                        {
                            children: {
                                _none: true,
                                intVal: { _eq: 10 },
                            },
                        },
                    ],
                },
                [1, 2, 3, 4, 5],
            ),
        );
    });

    it('can filter on children - every', async () => {
        await Test.uncommitted(context =>
            testFilter(
                context,
                TestFilterNestedParent,
                {
                    children: {
                        _every: true,
                        intVal: { _lte: 3 },
                    },
                },
                [1, 5, 6],
            ),
        );

        await Test.uncommitted(context =>
            testFilter(
                context,
                TestFilterNestedParent,
                {
                    _and: [
                        {
                            children: {
                                _atLeast: 1,
                                intVal: { _eq: 1 },
                            },
                        },
                        {
                            children: {
                                _every: true,
                                intVal: { _eq: 1 },
                            },
                        },
                    ],
                },
                [5],
            ),
        );
    });

    it('can filter on parent + children', () =>
        Test.uncommitted(context =>
            testFilter(
                context,
                TestFilterNestedParent,
                {
                    intVal: { _ne: 2 },
                    children: {
                        _every: true,
                        intVal: { _lte: 3 },
                    },
                },
                [1, 6],
            ),
        ));

    it('can filter on children - complex filter', () =>
        Test.uncommitted(context =>
            testFilter(
                context,
                TestFilterNestedParent,
                {
                    children: {
                        _every: true,
                        intVal: { _lte: 4 },
                        intVal2: { _gt: 4 },
                    },
                },
                [5, 6],
            ),
        ));

    it('can filter on children - complex filter 2', () =>
        Test.uncommitted(context =>
            testFilter(
                context,
                TestFilterNestedParent,
                {
                    children: {
                        _every: true,
                        _or: [
                            {
                                intVal: { _lte: 4 },
                                intVal2: { _gt: 1 },
                            },
                        ],
                    },
                },
                [1, 5, 6],
            ),
        ));

    it('can even filter multi-level', () =>
        Test.uncommitted(context =>
            testFilter(
                context,
                TestFilterNestedGrandParent,
                {
                    children: {
                        _every: true,
                        children: {
                            _every: true,
                            intVal: { _lte: 4 },
                        },
                    },
                },
                [2, 3],
            ),
        ));

    it('can filter on children - some (method)', () =>
        Test.uncommitted(context =>
            testFilter(
                context,
                TestFilterNestedParent,
                function filter() {
                    return this.children.some(async child => (await child.intVal2) === 3);
                },
                [2, 4],
            ),
        ));

    it('can filter on children - some (method) - with cross property condition', async () => {
        await Test.uncommitted(context =>
            testFilter(
                context,
                TestFilterNestedParent,
                function filter() {
                    return this.children.some(async child => (await child.intVal) > (await child.intVal2));
                },
                [2, 3, 4],
            ),
        );
    });

    it('can filter on children - every (method)', async () => {
        await Test.uncommitted(context =>
            testFilter(
                context,
                TestFilterNestedParent,
                function filter() {
                    return this.children.every(async child => (await child.intVal) < 4);
                },
                [1, 5, 6],
            ),
        );
    });

    it('can sum children properties', () =>
        Test.uncommitted(context =>
            testFilter(
                context,
                TestFilterNestedParent,
                async function filter() {
                    return (await this.children.sum(child => child.intVal)) === 10;
                },
                [2, 4],
            ),
        ));

    it('can test collection length', () =>
        Test.uncommitted(context =>
            testFilter(
                context,
                TestFilterNestedParent,
                async function filter() {
                    return (await this.children.length) > 3;
                },
                [2, 3, 4],
            ),
        ));

    it('can filter with collection.where().sum(...)', () =>
        Test.uncommitted(context =>
            testFilter(
                context,
                TestFilterNestedParent,
                async function filter() {
                    return (await this.children.where().sum(child => child.intVal)) === 10;
                },
                [2, 4],
            ),
        ));

    it('can filter with collection.where(...).sum(...)', () =>
        Test.uncommitted(context =>
            testFilter(
                context,
                TestFilterNestedParent,
                async function filter() {
                    return (
                        (await this.children
                            .where(async child => (await child.intVal2) !== 5)
                            .sum(child => child.intVal)) === 10
                    );
                },
                [4],
            ),
        ));

    it('can filter with collection.takeOne(...)?.prop and found', () =>
        Test.uncommitted(context =>
            testFilter(
                context,
                TestFilterNestedParent,
                async function filter() {
                    return (
                        (await (
                            await this.children.takeOne(async child => (await child.intVal) === (await child.intVal2))
                        )?.intVal) === 3
                    );
                },
                [2],
            ),
        ));

    it('can filter with collection.takeOne(...)?.prop and not found', () =>
        Test.uncommitted(context =>
            testFilter(
                context,
                TestFilterNestedParent,
                async function filter() {
                    return (
                        (await (await this.children.takeOne(async child => (await child.intVal) === 50))?.intVal) ==
                        null
                    );
                },
                [1, 2, 3, 4, 5, 6],
            ),
        ));

    it('can filter with collection.takeOne(...)?.prop1?.prop2 and found', () =>
        Test.uncommitted(context =>
            testFilter(
                context,
                TestFilterNestedParent,
                async function filter() {
                    return (
                        (await (
                            await (
                                await this.children.takeOne(async child => (await child.intVal) === 4)
                            )?.parent
                        )?.intVal) === 2
                    );
                },
                [2],
            ),
        ));

    it('can filter with collection.takeOne(...) and found', () =>
        Test.uncommitted(context =>
            testFilter(
                context,
                TestFilterNestedParent,
                async function filter() {
                    return (
                        (await this.children.takeOne(async child => (await child.intVal) === (await child.intVal2))) !=
                        null
                    );
                },
                [2],
            ),
        ));

    it('can filter with collection.takeOne(...)?.prop and not found', () =>
        Test.uncommitted(context =>
            testFilter(
                context,
                TestFilterNestedParent,
                async function filter() {
                    return (
                        (await (await this.children.takeOne(async child => (await child.intVal) === 100))?.text) == null
                    );
                },
                [1, 2, 3, 4, 5, 6],
            ),
        ));

    it('can filter with collection.length', () =>
        Test.uncommitted(context =>
            testFilter(
                context,
                TestFilterNestedParent,
                async function filter() {
                    return (await this.children.length) > 3;
                },
                [2, 3, 4],
            ),
        ));

    after(() => restoreTables());
});
