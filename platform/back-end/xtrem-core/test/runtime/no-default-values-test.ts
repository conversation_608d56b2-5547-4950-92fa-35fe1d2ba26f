import { assert } from 'chai';
import { decorators, Node, Test } from '../../index';
import { BinaryStream, decimal, DecimalDataType, TextStream } from '../../lib';
import { codeDataType, descriptionDataType } from '../fixtures/data-types/data-types';
import { createApplicationWithApi, initTables, restoreTables, setup } from '../fixtures/index';

/* DECLARE SOME TYPES */
@decorators.node<TestNoDefault>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestMyNode',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
class TestNoDefault extends Node {
    @decorators.stringProperty<TestNoDefault, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestNoDefault, 'text'>({
        dataType: () => descriptionDataType,
        isStored: true,
    })
    readonly text: Promise<string>;

    @decorators.decimalProperty<TestNoDefault, 'decimalProp'>({
        dataType: () => new DecimalDataType({ precision: 9, scale: 2 }),
        isStored: true,
    })
    readonly decimalProp: Promise<decimal>;

    @decorators.textStreamProperty<TestNoDefault, 'textStream'>({
        isStored: true,
    })
    readonly textStream: Promise<TextStream>;

    @decorators.binaryStreamProperty<TestNoDefault, 'binaryStream'>({
        isStored: true,
    })
    readonly binaryStream: Promise<BinaryStream>;
}

describe('columns no default value', () => {
    before(async () => {
        await setup({ application: await createApplicationWithApi({ nodes: { TestNoDefault } }) });
        // Manually create the table.
        await initTables([{ nodeConstructor: TestNoDefault, data: [] }]);
    });
    it('Can insert a record without data for columns defined without default constraints', () =>
        Test.uncommitted(async context => {
            const child1 = await context.create(TestNoDefault, {
                code: 'NODE1',
                text: 'Text 1',
                textStream: TextStream.fromString('textStream text'),
            });
            await child1.$.save();
            assert.deepEqual(context.diagnoses, []);
        }));
    after(() => restoreTables());
});
