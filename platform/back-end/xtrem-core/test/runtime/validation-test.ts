import { date } from '@sage/xtrem-date-time';
import { ValidationSeverity } from '@sage/xtrem-shared';
import { assert } from 'chai';
import { fixtures, testValidationApplication } from '..';
import { MAX_INT_32, Test } from '../../index';
import { initTables, restoreTables, setup } from '../fixtures/index';

const {
    TestControlFlow,
    TestValidationBasic,
    TestValidationBoolean,
    TestValidationNumber,
    TestValidationPersistent,
    TestValidationReturns,
    TestValidationSeverity,
    TestValidationString,
    TestValidationDate,
} = fixtures.nodes;

describe('testing basic validation', () => {
    before(async () => {
        await setup({ stubResetTables: false, application: await testValidationApplication.application });
        await initTables([{ nodeConstructor: TestValidationPersistent, data: [] }]);
    });

    it('succeeds when rules pass', () =>
        Test.uncommitted(async context => {
            const n = await context.create(TestValidationBasic, {
                s1: 'a',
                s2: 'a',
                s3: 'a',
                i1: 2,
                i2: 1,
            });
            assert.ok(await n.$.control());
        }));
    it("fails when rules don't pass", () =>
        Test.uncommitted(async context => {
            const n = await context.create(TestValidationBasic, {
                s1: '',
                s2: 'B',
                s3: 'abcdef',
                i1: 1,
                i2: 1,
            });
            assert.notOk(await n.$.control());
            assert.deepEqual(context.diagnoses, [
                {
                    path: ['s1'],
                    severity: ValidationSeverity.error,
                    message: 'value must not be empty',
                },
                {
                    path: ['s2'],
                    severity: ValidationSeverity.error,
                    message: "value must be equal to ''",
                },
                {
                    path: ['s3'],
                    severity: ValidationSeverity.error,
                    message: "value must be equal to ''",
                },
                {
                    path: ['s3'],
                    severity: ValidationSeverity.error,
                    message: 'value must not match /abc/',
                },
                {
                    path: ['s3'],
                    severity: ValidationSeverity.error,
                    message: 'value must not match /bcd/',
                },
                {
                    path: ['i1'],
                    severity: ValidationSeverity.error,
                    message: 'value must be greater than 1',
                },
                {
                    path: ['s1'],
                    severity: ValidationSeverity.error,
                    message: "value must be equal to 'B'",
                },
                {
                    path: ['i2'],
                    severity: ValidationSeverity.error,
                    message: 'value must be greater than 1',
                },
            ]);
        }));
    after(() => restoreTables());
});

describe('testing severity levels', () => {
    before(async () => {
        await setup({ stubResetTables: false, application: await testValidationApplication.application });
        await initTables([{ nodeConstructor: TestValidationPersistent, data: [] }]);
    });
    it('succeeds when rules pass', () =>
        Test.uncommitted(async context => {
            const n = await context.create(TestValidationSeverity, {
                value: '',
            });
            assert.ok(await n.$.control());
        }));
    it("fails when rules don't pass", () =>
        Test.uncommitted(async context => {
            const n = await context.create(TestValidationSeverity, {
                value: 'a',
            });
            assert.notOk(await n.$.control());
            assert.deepEqual(context.diagnoses, [
                {
                    path: ['value'],
                    severity: ValidationSeverity.info,
                    message: 'value must be empty',
                },
                {
                    path: ['value'],
                    severity: ValidationSeverity.warn,
                    message: 'value must be empty',
                },
                {
                    path: ['value'],
                    severity: ValidationSeverity.error,
                    message: 'value must be empty',
                },
            ]);
        }));

    it('throws when throw test fails', () =>
        Test.uncommitted(async context => {
            const n = await context.create(TestValidationSeverity, {
                value: 'ab',
            });
            assert.notOk(await n.$.control());
            assert.deepEqual(context.diagnoses, [
                {
                    path: ['value'],
                    severity: ValidationSeverity.info,
                    message: 'value must be empty',
                },
                {
                    path: ['value'],
                    severity: ValidationSeverity.warn,
                    message: 'value must be empty',
                },
                {
                    path: ['value'],
                    severity: ValidationSeverity.error,
                    message: 'value must be empty',
                },
                {
                    path: ['value'],
                    severity: ValidationSeverity.exception,
                    message: 'value must not be greater than 1',
                },
            ]);
        }));
    after(() => restoreTables());
});

describe('testing boolean validators', () => {
    before(async () => {
        await setup({ stubResetTables: false, application: await testValidationApplication.application });
        await initTables([{ nodeConstructor: TestValidationPersistent, data: [] }]);
    });
    it('succeeds when rules pass', () =>
        Test.uncommitted(async context => {
            const n = await context.create(TestValidationBoolean, {
                mustBeTrue: true,
                mustBeFalse: false,
            });
            assert.ok(await n.$.control());
        }));
    it('correct messages when validation fails', () =>
        Test.uncommitted(async context => {
            const n = await context.create(TestValidationBoolean, {
                mustBeTrue: false,
                mustBeFalse: true,
            });
            assert.notOk(await n.$.control());
            assert.deepEqual(n.$.context.diagnoses, [
                {
                    path: ['mustBeTrue'],
                    severity: ValidationSeverity.error,
                    message: 'value must be true',
                },
                {
                    path: ['mustBeTrue'],
                    severity: ValidationSeverity.error,
                    message: 'value must not be false',
                },
                {
                    path: ['mustBeFalse'],
                    severity: ValidationSeverity.error,
                    message: 'value must be false',
                },
                {
                    path: ['mustBeFalse'],
                    severity: ValidationSeverity.error,
                    message: 'value must not be true',
                },
            ]);
        }));
    after(() => restoreTables());
});

describe('testing numeric validators', () => {
    before(async () => {
        await setup({ stubResetTables: false, application: await testValidationApplication.application });
        await initTables([{ nodeConstructor: TestValidationPersistent, data: [] }]);
    });
    it('succeeds when rules pass', () =>
        Test.uncommitted(async context => {
            const n = await context.create(TestValidationNumber, {
                mandatory: 1,
                mustBeZero: 0,
                mustBePositive: 1,
                mustBeNegative: -1,
                mustBeNegativeCustomMessage: -1,
                mustBePositiveCustomMessage: 1,
                mustBeA32BitSignedInteger: MAX_INT_32,
            });
            assert.ok(await n.$.control());
        }));
    it('correct messages when validation fails', () =>
        Test.uncommitted(async context => {
            const n = await context.create(TestValidationNumber, {
                mandatory: 0,
                mustBeZero: 1,
                mustBePositive: 0,
                mustBeNegative: 1,
                mustBeNegativeCustomMessage: 1,
                mustBePositiveCustomMessage: -1,
                mustBeA32BitSignedInteger: 1 + MAX_INT_32,
            });
            assert.notOk(await n.$.control());
            assert.deepEqual(context.diagnoses, [
                {
                    path: ['mustBeZero'],
                    severity: ValidationSeverity.error,
                    message: 'value must be zero',
                },
                {
                    path: ['mustBeZero'],
                    severity: ValidationSeverity.error,
                    message: 'value must not be positive',
                },
                {
                    path: ['mustBeZero'],
                    severity: ValidationSeverity.error,
                    message: 'value must be equal to 0',
                },
                {
                    path: ['mustBeZero'],
                    severity: ValidationSeverity.error,
                    message: 'value must be less than 1',
                },
                {
                    path: ['mustBePositive'],
                    severity: ValidationSeverity.error,
                    message: 'value must be positive',
                },
                {
                    path: ['mustBePositive'],
                    severity: ValidationSeverity.error,
                    message: 'value must not be zero',
                },
                {
                    path: ['mustBeNegative'],
                    severity: ValidationSeverity.error,
                    message: 'value must be negative',
                },
                {
                    path: ['mustBeNegative'],
                    severity: ValidationSeverity.error,
                    message: 'value must not be positive',
                },
                {
                    path: ['mustBeNegativeCustomMessage'],
                    message: 'invalid value: returnedType, returnedValue',
                    severity: 3,
                },
                {
                    path: ['mustBePositiveCustomMessage'],
                    message: 'property is required',
                    severity: 3,
                },
                {
                    path: ['mustBeA32BitSignedInteger'],
                    message: 'integer property cannot represent non 32-bit signed integer value',
                    severity: 3,
                },
            ]);
        }));
    after(() => restoreTables());
});

describe('testing string validators', () => {
    before(async () => {
        await setup({ stubResetTables: false, application: await testValidationApplication.application });
        await initTables([{ nodeConstructor: TestValidationPersistent, data: [] }]);
    });
    it('succeeds when rules pass', () =>
        Test.uncommitted(async context => {
            const n = await context.create(TestValidationString, {
                mandatory: 'A',
                mustBeEmpty: '',
                mustBeA: 'A',
            });
            assert.ok(await n.$.control());
        }));
    it('correct messages when validation fails', () =>
        Test.uncommitted(async context => {
            const n = await context.create(TestValidationString, {
                mandatory: '',
                mustBeEmpty: 'X',
                mustBeA: 'Y',
            });
            assert.notOk(await n.$.control());
            assert.deepEqual(context.diagnoses, [
                {
                    path: ['mandatory'],
                    severity: ValidationSeverity.error,
                    message: 'string cannot be empty',
                },
                {
                    path: ['mustBeEmpty'],
                    severity: ValidationSeverity.error,
                    message: 'value must be empty',
                },
                {
                    path: ['mustBeEmpty'],
                    severity: ValidationSeverity.error,
                    message: "value must be equal to ''",
                },
                {
                    path: ['mustBeEmpty'],
                    severity: ValidationSeverity.error,
                    message: 'value must not match /./',
                },
                {
                    path: ['mustBeEmpty'],
                    severity: ValidationSeverity.error,
                    message: "value must be less than 'A'",
                },
                {
                    path: ['mustBeEmpty'],
                    severity: ValidationSeverity.error,
                    message: "value must not be greater than ''",
                },
                {
                    path: ['mustBeEmpty'],
                    severity: ValidationSeverity.error,
                    message: "value must not be greater than 'A'",
                },
                {
                    path: ['mustBeA'],
                    severity: ValidationSeverity.error,
                    message: "value must be equal to 'A'",
                },
                {
                    path: ['mustBeA'],
                    severity: ValidationSeverity.error,
                    message: 'value must match /^A$/',
                },
                {
                    path: ['mustBeA'],
                    severity: ValidationSeverity.error,
                    message: 'value must not match /[^A]/',
                },
                {
                    path: ['mustBeA'],
                    severity: ValidationSeverity.error,
                    message: "value must not be greater than 'A'",
                },
            ]);
        }));
    after(() => restoreTables());
});

describe('testing control flow', () => {
    before(async () => {
        await setup({ stubResetTables: false, application: await testValidationApplication.application });
        await initTables([{ nodeConstructor: TestValidationPersistent, data: [] }]);
    });
    it('go through all', () =>
        Test.uncommitted(async context => {
            const n = await context.create(TestControlFlow, {
                v1: '',
                v2: '',
            });
            assert.ok(await n.$.control());
            assert.deepEqual(context.diagnoses, [
                {
                    path: [],
                    severity: ValidationSeverity.info,
                    message: 'validating before',
                },
                {
                    path: ['v1'],
                    severity: ValidationSeverity.info,
                    message: 'validating v1: after throw',
                },
                {
                    path: ['v1'],
                    severity: ValidationSeverity.info,
                    message: 'validating v1: after error',
                },
                {
                    path: ['v2'],
                    severity: ValidationSeverity.info,
                    message: 'validating v2',
                },
                {
                    path: [],
                    severity: ValidationSeverity.info,
                    message: 'validating after',
                },
            ]);
        }));
    it('return after error', () =>
        Test.uncommitted(async context => {
            const n = await context.create(TestControlFlow, {
                v1: 'error',
                v2: '',
            });
            assert.notOk(await n.$.control());
            assert.deepEqual(n.$.context.diagnoses, [
                {
                    path: [],
                    severity: ValidationSeverity.info,
                    message: 'validating before',
                },
                {
                    path: ['v1'],
                    severity: ValidationSeverity.info,
                    message: 'validating v1: after throw',
                },
                {
                    path: ['v1'],
                    severity: ValidationSeverity.error,
                    message: "value must not be equal to 'error'",
                },
                {
                    path: ['v2'],
                    severity: ValidationSeverity.info,
                    message: 'validating v2',
                },
                {
                    path: [],
                    severity: ValidationSeverity.info,
                    message: 'validating after',
                },
            ]);
        }));
    it('throw', () =>
        Test.uncommitted(async context => {
            const n = await context.create(TestControlFlow, {
                v1: 'throw',
                v2: '',
            });
            assert.notOk(await n.$.control());
            assert.deepEqual(context.diagnoses, [
                {
                    path: [],
                    severity: ValidationSeverity.info,
                    message: 'validating before',
                },
                {
                    path: ['v1'],
                    severity: ValidationSeverity.exception,
                    message: "value must not be equal to 'throw'",
                },
            ]);
        }));
    after(() => restoreTables());
});

describe('testing values returned by validation api', () => {
    before(async () => {
        await setup({ stubResetTables: false, application: await testValidationApplication.application });
        await initTables([{ nodeConstructor: TestValidationPersistent, data: [] }]);
    });
    it('returns the expected boolean values', () =>
        Test.uncommitted(async context => {
            const n = await context.create(TestValidationReturns, {});
            assert.ok(await n.$.control());
            assert.equal(context.diagnoses.length, 14);
        }));
    after(() => restoreTables());
});

describe('testing date validators', () => {
    before(async () => {
        await setup({ stubResetTables: false, application: await testValidationApplication.application });
        await initTables([{ nodeConstructor: TestValidationPersistent, data: [] }]);
    });

    it('correct messages when date validation fails', () =>
        Test.uncommitted(async context => {
            const n = await context.create(TestValidationDate, {
                referenceDate: date.make(2022, 8, 8),
                testDate: date.make(2022, 9, 9),
            });
            assert.notOk(await n.$.control());
            assert.deepEqual(context.diagnoses, [
                {
                    severity: 3,
                    path: ['testDate'],
                    message: `value must not be after ${await n.referenceDate}`,
                },
            ]);
        }));

    after(() => restoreTables());
});
