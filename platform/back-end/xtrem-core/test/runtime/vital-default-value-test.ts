import { assert } from 'chai';
import { Reference, Test } from '../../index';
import { Collection, Node, decorators, integer } from '../../lib';
import { codeDataType } from '../fixtures/data-types/data-types';
import { createApplicationWithApi, initTables, restoreTables, setup } from '../fixtures/index';

@decorators.node<TestVitalDefValueParent>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
})
export class TestVitalDefValueParent extends Node {
    @decorators.stringProperty<TestVitalDefValueParent, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestVitalDefValueParent, 'vitalRef'>({
        isPublished: true,
        isVital: true,
        node: () => TestVitalDefValueReferenced,
        reverseReference: 'parent',
    })
    readonly vitalRef: Reference<TestVitalDefValueReferenced>;

    @decorators.collectionProperty<TestVitalDefValueParent, 'vitalCollection'>({
        isPublished: true,
        isVital: true,
        node: () => TestVitalDefValueChild,
        reverseReference: 'parent',
        defaultValue() {
            return [{}, { value: 5 }];
        },
    })
    readonly vitalCollection: Collection<TestVitalDefValueChild>;
}

@decorators.node<TestVitalDefValueReferenced>({
    isPublished: true,
    storage: 'sql',
    isVitalReferenceChild: true,
})
export class TestVitalDefValueReferenced extends Node {
    @decorators.referenceProperty<TestVitalDefValueReferenced, 'parent'>({
        isVitalParent: true,
        isStored: true,
        node: () => TestVitalDefValueParent,
    })
    readonly parent: Reference<TestVitalDefValueParent>;

    @decorators.integerProperty<TestVitalDefValueReferenced, 'value'>({
        isStored: true,
        isPublished: true,
        defaultValue: 2,
    })
    readonly value: Promise<integer>;
}

@decorators.node<TestVitalDefValueChild>({
    isPublished: true,
    storage: 'sql',
    isVitalCollectionChild: true,
})
export class TestVitalDefValueChild extends Node {
    @decorators.referenceProperty<TestVitalDefValueChild, 'parent'>({
        isVitalParent: true,
        isStored: true,
        node: () => TestVitalDefValueParent,
    })
    readonly parent: Reference<TestVitalDefValueParent>;

    @decorators.integerProperty<TestVitalDefValueChild, 'value'>({
        isStored: true,
        isPublished: true,
        defaultValue: 3,
    })
    readonly value: Promise<integer>;
}

describe('Vital default value', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi({
                nodes: { TestVitalDefValueParent, TestVitalDefValueReferenced, TestVitalDefValueChild },
            }),
        });
        await initTables([
            { nodeConstructor: TestVitalDefValueParent, data: [] },
            { nodeConstructor: TestVitalDefValueReferenced, data: [] },
            { nodeConstructor: TestVitalDefValueChild, data: [] },
        ]);
    });

    it('can create a vital parent with default values', () =>
        Test.withContext(async context => {
            const parent = await context.create(TestVitalDefValueParent, {
                code: 'P1',
            });
            assert.instanceOf(await parent.vitalRef, TestVitalDefValueReferenced);
            assert.equal(await parent.vitalCollection.length, 2);
            // parent.vitalCollection.forEach(elt => assert.instanceOf(elt, TestVitalCollectionChild));
            /* does not throw */ await (() => parent.$.save())();

            const p = await context.read(TestVitalDefValueParent, { _id: 1 });
            assert.deepEqual(await p.$.payload(), {
                code: 'P1',
                vitalRef: { parent: {}, value: 2, _customData: {} },
                vitalCollection: [
                    { parent: {}, value: 3, _sortValue: 10, _customData: {} },
                    { parent: {}, value: 5, _sortValue: 20, _customData: {} },
                ],
                _customData: {},
            });
        }));

    after(() => restoreTables());
});
