import { assert } from 'chai';
import { Test } from '../../lib';
import { createApplicationWithApi, initTables, restoreTables, setup } from '../fixtures/index';
import { TestAdaptValuesExtension } from '../fixtures/node-extensions';
import { TestAdaptValues } from '../fixtures/nodes';

describe('adaptValues tests', () => {
    before(async () =>
        setup({
            application: await createApplicationWithApi({
                nodes: { TestAdaptValues },
                nodeExtensions: { TestAdaptValuesExtension },
            }),
        }),
    );
    beforeEach(() => initTables([{ nodeConstructor: TestAdaptValues, data: [] }]));

    it('correctly applies the adaptValue rules in the correct order', () =>
        Test.withContext(async context => {
            const newRecord = await context.create(TestAdaptValues, {
                id: 1,
                dataTypeAdaptValue: 'a',
                propertyAdaptValue: 'b',
                bothAdaptValue: 'c',
                extendedAdaptValue: 'd',
                trimAdaptValue: ' e ',
                doNotTrimAdaptValue: ' f ',
                truncateAdaptValue: 'a string that must be truncated the max length',
            });
            await newRecord.$.save();
            const savedRecord = (await context.query(TestAdaptValues).toArray())[0];
            assert.equal(await savedRecord.dataTypeAdaptValue, 'a_data-type');
            assert.equal(await savedRecord.propertyAdaptValue, 'b_property');

            // adaptValue is called first on the data type, then on the property
            assert.equal(await savedRecord.bothAdaptValue, 'c_data-type_property');

            // if no value is set, adaptValue methods are not called
            assert.equal(await savedRecord.bothAdaptValue2, '');

            // the adaptValue method of the extended property overrides the property's one
            assert.equal(await savedRecord.extendedAdaptValue, 'd_data-type_extended-property');

            // the default adaptValue method from the StringDataType should trim the value
            assert.equal(await savedRecord.trimAdaptValue, 'e');

            // if the doNotTim option is true
            // the default adaptValue method from the StringDataType should not trim the value
            assert.equal(await savedRecord.doNotTrimAdaptValue, ' f ');

            // the default adaptValue method from the StringDataType should truncate the string to the max length to prevent raising an validation error
            assert.equal(await savedRecord.truncateAdaptValue, 'a string that must be trunc...');
        }));

    after(() => restoreTables());
});
