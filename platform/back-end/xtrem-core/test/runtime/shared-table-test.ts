import { assert } from 'chai';
import { decorators, Node, Reference, Test } from '../../index';
import { codeDataType, descriptionDataType } from '../fixtures/data-types/data-types';
import { createApplicationWithApi, initTables, restoreTables, setup } from '../fixtures/index';

@decorators.node<TestSharedTableNode>({
    isPublished: true,
    storage: 'sql',
    isSharedByAllTenants: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
class TestSharedTableNode extends Node {
    @decorators.stringProperty<TestSharedTableNode, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestSharedTableNode, 'text'>({
        dataType: () => codeDataType,
        isStored: true,
    })
    readonly text: Promise<string>;
}

@decorators.node<TestNormalNode>({
    isPublished: true,
    storage: 'sql',
    isSharedByAllTenants: false,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
class TestNormalNode extends Node {
    @decorators.stringProperty<TestNormalNode, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestNormalNode, 'text'>({
        dataType: () => descriptionDataType,
        isStored: true,
    })
    readonly text: Promise<string>;

    @decorators.referenceProperty<TestNormalNode, 'reference'>({
        isPublished: true,
        isStored: true,
        node: () => TestSharedTableNode,
        isNullable: true,
    })
    readonly reference: Reference<TestSharedTableNode | null>;
}

@decorators.node<TestBadSharedNode>({
    isPublished: true,
    storage: 'sql',
    isSharedByAllTenants: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
class TestBadSharedNode extends Node {
    @decorators.stringProperty<TestBadSharedNode, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestBadSharedNode, 'reference'>({
        isPublished: true,
        isStored: true,
        node: () => TestNormalNode,
        isNullable: true,
    })
    readonly reference: Reference<TestSharedTableNode | null>;
}

describe('Shared table tests', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi({
                nodes: {
                    TestSharedTableNode,
                    TestNormalNode,
                },
            }),
        });
        // Manually create the table.
        await initTables([
            { nodeConstructor: TestSharedTableNode, data: [] },
            { nodeConstructor: TestNormalNode, data: [] },
        ]);
    });
    it('validate table definition', () =>
        Test.readonly(context => {
            const sharedFactory = Test.application.getFactoryByConstructor(TestSharedTableNode);
            assert(
                !sharedFactory.table
                    .getTableDefinition(context)
                    .columns?.map(column => column.name)
                    .includes('_tenant_id'),
            );

            const normalFactory = Test.application.getFactoryByConstructor(TestNormalNode);
            assert(
                normalFactory.table
                    .getTableDefinition(context)
                    .columns?.map(column => column.name)
                    .includes('_tenant_id'),
            );
        }));

    it('can read the same record with different tenantIds', async () => {
        const sharedFactory = Test.application.getFactoryByConstructor(TestSharedTableNode);

        // Create an instance of a shared node into the default test tenantId
        const insertedId = await Test.withCommittedContext(async context => {
            const inserted = await sharedFactory.table.insert(context, { code: 'ABC', text: 'Some text' });
            const shared = await context.read(TestSharedTableNode, { _id: inserted._id });
            assert.instanceOf(shared, TestSharedTableNode);
            assert.equal(await shared.code, 'ABC');
            return inserted._id;
        });

        // Ensure the new instance can be read with another tenantId
        await Test.withReadonlyContext(
            async context => {
                const sharedAgain = await context.read(TestSharedTableNode, { _id: insertedId });

                assert.instanceOf(sharedAgain, TestSharedTableNode);
                assert.equal(await sharedAgain.code, 'ABC');
            },
            { tenantId: '1'.repeat(21) },
        );
    });

    it('can query the same record with different tenantIds', async () => {
        await Test.withReadonlyContext(async context => {
            const shared = await context.query(TestSharedTableNode, { filter: { _id: 1 } }).toArray();
            assert.instanceOf(shared[0], TestSharedTableNode);
            assert.equal(await shared[0].code, 'ABC');
        });

        await Test.withReadonlyContext(
            async context => {
                const sharedAgain = await context.query(TestSharedTableNode, { filter: { _id: 1 } }).toArray();

                assert.instanceOf(sharedAgain[0], TestSharedTableNode);
                assert.equal(await sharedAgain[0].code, 'ABC');
            },
            { tenantId: '1'.repeat(21) },
        );
    });

    it('cannot create', () =>
        Test.withUncommittedContext(async context => {
            await assert.isRejected(
                context.create(TestSharedTableNode, { code: 'ABC', text: 'Some text' }),
                'TestSharedTableNode: cannot create: shared by all tenants',
            );
        }));

    it('cannot bulk delete', () =>
        Test.withUncommittedContext(async context => {
            await assert.isRejected(
                context.deleteMany(TestSharedTableNode, { _id: 1 }),
                'TestSharedTableNode: cannot delete: shared by all tenants',
            );
        }));

    it('cannot delete', () =>
        Test.withUncommittedContext(async context => {
            const shared = await context.read(TestSharedTableNode, { _id: 1 });
            await assert.isRejected(shared.$.delete(), 'TestSharedTableNode: cannot delete: shared by all tenants');
        }));

    it('cannot update', () =>
        Test.withUncommittedContext(async context => {
            const shared = await context.read(TestSharedTableNode, { _id: 1 }, { forUpdate: true });
            await shared.$.set({ code: 'ZZZ' });
            await assert.isRejected(shared.$.save(), 'TestSharedTableNode: cannot update: shared by all tenants');
        }));

    it('a non-shared node can have a reference property to a shared node', () =>
        Test.withUncommittedContext(async context => {
            const normal = await context.create(TestNormalNode, {
                code: 'REF1',
                reference: 1,
                text: 'Normal node with special reference',
            });
            await normal.$.save();

            const normal2 = await context.read(TestNormalNode, { _id: normal._id });
            const shared = await context.read(TestSharedTableNode, { _id: 1 });
            assert.deepEqual(await normal2.reference, shared);
        }));

    it('a shared node cannot have a reference property to a non-shared node', async () => {
        await assert.isRejected(
            createApplicationWithApi({
                nodes: { TestSharedTableNode, TestNormalNode, TestBadSharedNode },
            }),
            'TestBadSharedNode.reference: A reference property of a shared node cannot be linked to a non-shared node.',
        );
    });

    after(() => restoreTables());
});
