import { assert } from 'chai';
import { decorators, Node, Reference, Test } from '../../index';
import { garbageCollectContentAddressableTables } from '../../lib/sql';
import { codeDataType } from '../fixtures/data-types/data-types';
import { createApplicationWithApi, initTables, restoreTables, setup } from '../fixtures/index';

@decorators.node<TestContentAddressable>({
    isPublished: true,
    storage: 'sql',
    isContentAddressable: true,
})
class TestContentAddressable extends Node {
    @decorators.stringProperty<TestContentAddressable, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.booleanProperty<TestContentAddressable, 'bool'>({
        isStored: true,
    })
    readonly bool: Promise<boolean>;
}

@decorators.node<TestContentAddressableParent>({
    isPublished: true,
    storage: 'sql',
    canDeleteMany: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true, isNaturalKey: true }],
})
class TestContentAddressableParent extends Node {
    @decorators.stringProperty<TestContentAddressableParent, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestContentAddressableParent, 'mandatoryRef'>({
        isStored: true,
        isMutable: true,
        node: () => TestContentAddressable,
    })
    readonly mandatoryRef: Reference<TestContentAddressable>;

    @decorators.referenceProperty<TestContentAddressableParent, 'optionalRef'>({
        isStored: true,
        isMutable: true,
        isNullable: true,
        node: () => TestContentAddressable,
    })
    readonly optionalRef: Reference<TestContentAddressable | null>;
}

describe('Content addressable node', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi({
                nodes: {
                    TestContentAddressable,
                    TestContentAddressableParent,
                },
            }),
        });
        // Manually create the table.
        await initTables([
            { nodeConstructor: TestContentAddressable, data: [] },
            { nodeConstructor: TestContentAddressableParent, data: [] },
        ]);
    });

    it('can create and update content addressable nodes', async () => {
        let id1: number;
        // create a node and save it
        await Test.withCommittedContext(async context => {
            const node = await context.create(TestContentAddressable, { code: 'CODE1' });
            await node.$.save();
            id1 = node._id;
            assert.isAbove(id1, 0);
            const payload = await node.$.payload({ withIds: true, withoutCustomData: true });
            assert.deepEqual(payload, { _id: id1, code: 'CODE1', bool: false, _sourceId: '' });
        });
        // verify that the node has been saved, by reading it from another context
        await Test.withReadonlyContext(async context => {
            const nodes = await context
                .query(TestContentAddressable, {})
                .map(node => node.$.payload({ withIds: true, withoutCustomData: true }))
                .toArray();
            assert.deepEqual(nodes, [{ _id: id1, code: 'CODE1', bool: false, _sourceId: '' }]);
        });
        // create a second identical node and save it
        await Test.withCommittedContext(async context => {
            const node = await context.create(TestContentAddressable, { code: 'CODE1' });
            await node.$.save();
            const payload = await node.$.payload({ withIds: true, withoutCustomData: true });
            assert.deepEqual(payload, { _id: id1, code: 'CODE1', bool: false, _sourceId: '' });
        });
        // verify that the we still have only one record in the table
        await Test.withReadonlyContext(async context => {
            const nodes = await context
                .query(TestContentAddressable, {})
                .map(node => node.$.payload({ withIds: true, withoutCustomData: true }))
                .toArray();
            assert.deepEqual(nodes, [{ _id: id1, code: 'CODE1', bool: false, _sourceId: '' }]);
        });
        // create a different node
        let id2: number;
        await Test.withCommittedContext(async context => {
            const node = await context.create(TestContentAddressable, { code: 'CODE1', bool: true });
            await node.$.save();
            id2 = node._id;
            assert.isAbove(id2, id1);
            const payload = await node.$.payload({ withIds: true, withoutCustomData: true });
            assert.deepEqual(payload, { _id: id2, code: 'CODE1', bool: true, _sourceId: '' });
        });
        // verify that the we have two records
        await Test.withReadonlyContext(async context => {
            const nodes = await context
                .query(TestContentAddressable, {})
                .map(node => node.$.payload({ withIds: true, withoutCustomData: true }))
                .toArray();
            assert.deepEqual(nodes, [
                { _id: id1, code: 'CODE1', bool: false, _sourceId: '' },
                { _id: id2, code: 'CODE1', bool: true, _sourceId: '' },
            ]);
        });
    });

    it('can create a node with a reference to a content addressable node', async () => {
        // Create a node and save it
        await Test.withCommittedContext(async context => {
            const node = await context.create(TestContentAddressableParent, {
                code: 'P1',
                mandatoryRef: { code: 'C1', bool: true },
                optionalRef: { code: 'C2', bool: false },
            });
            await node.$.save();
        });
        // Verify that the node has been saved, by reading it from another context
        await Test.withReadonlyContext(async context => {
            const node = await context.read(TestContentAddressableParent, { code: 'P1' });
            assert.deepEqual(await node.$.payload({ withoutCustomData: true }), {
                code: 'P1',
                mandatoryRef: { code: 'C1', bool: true },
                optionalRef: { code: 'C2', bool: false },
            });
        });
        // Change mandatory ref and set option ref to null
        await Test.withCommittedContext(async context => {
            const node = await context.read(TestContentAddressableParent, { code: 'P1' }, { forUpdate: true });
            await node.$.set({
                mandatoryRef: { code: 'C1', bool: false },
                optionalRef: null,
            });
            await node.$.save();
        });
        // Verify that the changes have been saved
        await Test.withReadonlyContext(async context => {
            const node = await context.read(TestContentAddressableParent, { code: 'P1' });
            assert.deepEqual(await node.$.payload({ withoutCustomData: true }), {
                code: 'P1',
                mandatoryRef: { code: 'C1', bool: false },
                optionalRef: null,
            });
        });
        // Make both references point to the same new node
        await Test.withCommittedContext(async context => {
            const node = await context.read(TestContentAddressableParent, { code: 'P1' }, { forUpdate: true });
            await node.$.set({
                mandatoryRef: { code: 'C2', bool: false },
                optionalRef: { code: 'C2', bool: false },
            });
            await node.$.save();
        });
        // Verify that both references point to the same node
        await Test.withReadonlyContext(async context => {
            const node = await context.read(TestContentAddressableParent, { code: 'P1' });
            assert.deepEqual(await node.$.payload({ withoutCustomData: true }), {
                code: 'P1',
                mandatoryRef: { code: 'C2', bool: false },
                optionalRef: { code: 'C2', bool: false },
            });
            assert.equal(await node.mandatoryRef, await node.optionalRef);
        });

        // Create another node with the same references and save it
        await Test.withCommittedContext(async context => {
            const node = await context.create(TestContentAddressableParent, {
                code: 'P2',
                mandatoryRef: { code: 'C1', bool: false },
                optionalRef: { code: 'C3', bool: false },
            });
            await node.$.save();
        });
        // Verify the node payload
        await Test.withReadonlyContext(async context => {
            const node = await context.read(TestContentAddressableParent, { code: 'P2' });
            assert.deepEqual(await node.$.payload({ withoutCustomData: true }), {
                code: 'P2',
                mandatoryRef: { code: 'C1', bool: false },
                optionalRef: { code: 'C3', bool: false },
            });
        });
    });

    it('can handle updates on multiple references', async () => {
        // Create a node and save it
        await Test.withCommittedContext(async context => {
            const node = await context.create(TestContentAddressableParent, {
                code: 'P3',
                mandatoryRef: { code: 'C1', bool: true },
                optionalRef: { code: 'C1', bool: true },
            });
            await node.$.save();

            await node.$.set({ mandatoryRef: { bool: false } });
            await node.$.save();

            assert.deepEqual(await node.$.payload({ withoutCustomData: true }), {
                code: 'P3',
                mandatoryRef: { code: 'C1', bool: false },
                optionalRef: { code: 'C1', bool: true },
            });
        });
        // Verify that the save really worked
        await Test.withReadonlyContext(async context => {
            const node = await context.read(TestContentAddressableParent, { code: 'P3' });
            assert.deepEqual(await node.$.payload({ withoutCustomData: true }), {
                code: 'P3',
                mandatoryRef: { code: 'C1', bool: false },
                optionalRef: { code: 'C1', bool: true },
            });
        });
    });

    it('cannot modify a content-addressable node directly', async () => {
        await Test.withCommittedContext(async context => {
            const node = await context.create(TestContentAddressable, { code: 'C1', bool: false });
            await node.$.save();
            await assert.isRejected(
                node.$.set({ bool: true }),
                'TestContentAddressable: Content-addressable node instances are immutable. You cannot modify them directly with node.$.set().',
            );
        });
    });

    it('can modify a content-addressable node when created for lookup only', async () => {
        await Test.withCommittedContext(async context => {
            const node = await context.create(
                TestContentAddressable,
                { code: 'C1', bool: false },
                { isOnlyForLookup: true },
            );
            await node.$.set({ bool: true });
            assert.equal(await node.bool, true);
            assert.equal(await node.code, 'C1');
        });
    });

    it('can GC content-addressable tables', async () => {
        const deleteAllParents = () =>
            Test.withCommittedContext(async context => {
                await context.deleteMany(TestContentAddressableParent, {});
            });

        const garbageCollect = () =>
            // Mutation starts with a readonly context and processes every table in a child context
            Test.withReadonlyContext(async context => {
                context.source = 'listener';
                await garbageCollectContentAddressableTables(context);
            });

        // Delete all the parent records
        await deleteAllParents();
        // Run a GC pass
        await garbageCollect();

        let remaining = await Test.withReadonlyContext(context => context.queryCount(TestContentAddressable, {}));
        assert.equal(remaining, 0);

        // Create a node with 2 content-addressable children
        await Test.withCommittedContext(async context => {
            const node = await context.create(TestContentAddressableParent, {
                code: 'P1',
                mandatoryRef: { code: 'C1', bool: true },
                optionalRef: { code: 'C2', bool: false },
            });
            await node.$.save();
        });

        // Check that we have 2 content-addressable records
        remaining = await Test.withReadonlyContext(context => context.queryCount(TestContentAddressable, {}));
        assert.equal(remaining, 2);

        // Run a GC pass
        await garbageCollect();

        // Check that we still have 2 content-addressable records
        remaining = await Test.withReadonlyContext(context => context.queryCount(TestContentAddressable, {}));
        assert.equal(remaining, 2);

        // Delete all the parent records
        await deleteAllParents();
        // Check that we still have 2 content-addressable records
        remaining = await Test.withReadonlyContext(context => context.queryCount(TestContentAddressable, {}));

        // Run a GC pass
        await garbageCollect();

        // Check that we have 0 content-addressable records
        remaining = await Test.withReadonlyContext(context => context.queryCount(TestContentAddressable, {}));
        assert.equal(remaining, 0);
    });

    after(() => restoreTables());
});
