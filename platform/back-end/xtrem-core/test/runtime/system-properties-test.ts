import { asyncArray } from '@sage/xtrem-async-helper';
import { ValidationSeverity } from '@sage/xtrem-shared';
import { assert } from 'chai';
import { createHash } from 'crypto';
import { promisify } from 'util';
import { testSubclassingApplication } from '..';
import { datetime, rootUserEmail, Test } from '../../index';
import { createApplicationWithApi, datatypesData, initTables, restoreTables, setup } from '../fixtures/index';
import { TestCreateUpdateStamp, TestDatatypes, TestUser } from '../fixtures/nodes';

async function sleep(duration: number): Promise<void> {
    await promisify(setTimeout)(duration);
}

describe('testDatatypes tests (system)', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi({
                nodes: {
                    TestDatatypes,
                    TestCreateUpdateStamp,
                },
            }),
        });
        await initTables([
            { nodeConstructor: TestDatatypes, data: datatypesData },
            { nodeConstructor: TestCreateUpdateStamp, data: [] },
        ]);
    });

    it('can read $.id', () =>
        Test.withContext(async context => {
            const data = datatypesData;
            const nodes = await context.query(TestDatatypes).toArray();
            assert.equal(nodes.length, data.length, 'result length matches');
            nodes.forEach((node, i) => {
                assert.equal(node.$.id, i + 1);
            });
        }));

    it('sourceId is empty', () =>
        Test.withContext(async context => {
            const data = datatypesData;
            const nodes = await context.query(TestDatatypes).toArray();
            assert.equal(nodes.length, data.length, 'result length matches');
            await asyncArray(nodes).forEach(async node => {
                assert.equal(await node.$.sourceId, '');
            });
        }));

    it('can get createdBy, updatedBy, createStamp, updateStamp and etag values', () =>
        Test.withContext(async context => {
            const data = datatypesData;
            const user = await context.read(TestUser, { email: rootUserEmail });
            const nodes = await context.query(TestDatatypes).toArray();
            assert.equal(nodes.length, data.length, 'result length matches');
            assert.equal(await nodes[0].$.createdBy, user);
            assert.equal(await nodes[0].$.updatedBy, user);
            // We cannot check a specific value as the test data is loaded before the now mock
            const createStamp = await nodes[0].$.createStamp;
            assert.isNotNull(createStamp);
            assert.isTrue(createStamp instanceof datetime);

            assert.isNotNull(await nodes[0].$.updateStamp);
            assert.isTrue((await nodes[0].$.updateStamp) instanceof datetime);
            assert.equal(
                await nodes[0].$.etag,
                createHash('sha256')
                    .update((await nodes[0].$.updateStamp).toString())
                    .digest('base64'),
            );
        }));

    it('can allocate properties correctly on nodes and subnode', async () => {
        const subclassingApplication = await testSubclassingApplication.application;

        // Subnode most system properties are not inherited
        const dogFactory = subclassingApplication.getFactoryByName('TestDog');
        const dogSystemProperties = dogFactory.properties.filter(prop => prop.isSystemProperty);
        assert.deepEqual(
            dogSystemProperties.map(prop => ({ name: prop.name, isInherited: prop.isInherited })),
            [
                {
                    isInherited: true,
                    name: '_updateTick',
                },
                {
                    isInherited: true,
                    name: '_updateUser',
                },
                {
                    isInherited: true,
                    name: '_createUser',
                },
                {
                    isInherited: false,
                    name: '_customData',
                },
                {
                    isInherited: true,
                    name: '_sourceId',
                },
                {
                    isInherited: false,
                    name: '_id',
                },
                {
                    isInherited: false,
                    name: '_etag',
                },
                {
                    isInherited: true,
                    name: '_createStamp',
                },
                {
                    isInherited: true,
                    name: '_updateStamp',
                },
            ],
        );

        const dogSystemColumns = dogFactory.table.columns.filter(col => col.isSystem);
        // No inheritted columns
        assert.deepEqual(
            dogSystemColumns.map(col => ({
                columnName: col.columnName,
                isInherited: col.property.isInherited,
            })),
            [
                {
                    columnName: '_custom_data',
                    isInherited: false,
                },
                {
                    columnName: '_id',
                    isInherited: false,
                },
                {
                    columnName: '_tenant_id',
                    isInherited: false,
                },
            ],
        );

        // Non subnode all system properties are not inherited
        const petOwnerFactory = subclassingApplication.getFactoryByName('TestPetOwner');
        const petOwnerSystemProperties = petOwnerFactory.properties.filter(prop => prop.isSystemProperty);
        assert.deepEqual(
            petOwnerSystemProperties.map(prop => ({ name: prop.name, isInherited: prop.isInherited })),
            [
                {
                    isInherited: false,
                    name: '_updateTick',
                },
                {
                    isInherited: false,
                    name: '_updateUser',
                },
                {
                    isInherited: false,
                    name: '_createUser',
                },
                {
                    isInherited: false,
                    name: '_customData',
                },
                {
                    isInherited: false,
                    name: '_sourceId',
                },
                {
                    isInherited: false,
                    name: '_id',
                },
                {
                    isInherited: false,
                    name: '_etag',
                },
                {
                    isInherited: false,
                    name: '_createStamp',
                },
                {
                    isInherited: false,
                    name: '_updateStamp',
                },
            ],
        );

        const petOwnerSystemColumns = petOwnerFactory.table.columns.filter(col => col.isSystem);

        assert.deepEqual(
            petOwnerSystemColumns.map(col => ({
                columnName: col.columnName,
                isInherited: col.property.isInherited,
            })),
            [
                {
                    columnName: '_update_tick',
                    isInherited: false,
                },
                {
                    columnName: '_update_user',
                    isInherited: false,
                },
                {
                    columnName: '_create_user',
                    isInherited: false,
                },
                {
                    columnName: '_custom_data',
                    isInherited: false,
                },
                {
                    columnName: '_source_id',
                    isInherited: false,
                },
                {
                    columnName: '_id',
                    isInherited: false,
                },
                {
                    columnName: '_create_stamp',
                    isInherited: false,
                },
                {
                    columnName: '_update_stamp',
                    isInherited: false,
                },
                {
                    columnName: '_tenant_id',
                    isInherited: false,
                },
            ],
        );
    });

    it('passed in etag in node create data and no error is thrown', () =>
        Test.withContext(async context => {
            const result = await context.create(TestDatatypes, { id: 9999, integerVal: 1, _etag: 'XX' });
            await result.$.save();
            assert.isTrue(context.severity < ValidationSeverity.error, 'Context severity is on error or above');
            assert.deepEqual(context.diagnoses, []);
            assert.notEqual(await result._etag, 'XX');
        }));

    describe('create/update stamps', () => {
        const tablename = 'test_create_update_stamp';

        it('can filter query by createStamp', async () => {
            await Test.withContext(async context => {
                const nowDatetime = await context.getSqlTimestamp();
                const result = await context
                    .query(TestDatatypes, { filter: { _createStamp: { _lt: nowDatetime } } })
                    .toArray();
                assert.isTrue(result.length > 0, 'No results found filtering by createStamp');
                await asyncArray(result).forEach(async r => {
                    assert.isTrue(nowDatetime.compare(await r.$.createStamp) > 0);
                });
            });
        });

        it('can filter query by updateStamp', async () => {
            await Test.withContext(async context => {
                const nowDatetime = await context.getSqlTimestamp();
                const result = await context
                    .query(TestDatatypes, { filter: { _updateStamp: { _lt: nowDatetime } } })
                    .toArray();
                assert.isTrue(result.length > 0, 'No results found filtering by updateStamp');
                await asyncArray(result).forEach(async r => {
                    assert.isTrue(nowDatetime.compare(await r.$.updateStamp) > 0);
                });
            });
        });

        it('default time should be applied to create/update stamps', async () => {
            let dbServerTime: datetime;
            let newId: string;

            await Test.withCommittedContext(async context => {
                // Fetch the time on the db server
                dbServerTime = datetime.fromJsDate(
                    (await context.executeSql<{ t: Date }[]>('SELECT NOW() t', []))[0].t,
                );

                newId = (
                    await context.executeSql<{ id: string }>(
                        `INSERT INTO ${context.schemaName}.${tablename}
                                (
                                    val,
                                    _tenant_id,
                                    _update_tick
                                )
                                VALUES
                                (
                                    1,
                                    ${context.tenantId},
                                    1
                                ) RETURNING _id as id;`,
                        [],
                    )
                ).id;
            });

            await Test.withCommittedContext(async context => {
                let instance = await context.read(TestCreateUpdateStamp, { _id: newId }, { forUpdate: true });
                const createStamp1 = await instance.$.createStamp;
                const updateStamp1 = await instance.$.updateStamp;

                // updateStamp / createStamp of the new record should have been set to the db server's time
                // There is a SQL default value in these 2 columns
                assert.isTrue(createStamp1.value - dbServerTime.value < 100);
                assert.isTrue(updateStamp1.value - dbServerTime.value < 100);

                await sleep(1500);

                await instance.$.set({ val: (await instance.val) + 1 });
                await instance.$.save();

                instance = await context.read(TestCreateUpdateStamp, { _id: newId });
                const createStamp2 = await instance.$.createStamp;
                const updateStamp2 = await instance.$.updateStamp;

                // The createStamp should not have been updated
                assert.equal(createStamp2.value, createStamp1.value);
                // The updateStamp should have been updated to the db server time
                // The 'update_table' function should have detected that no value was provided for
                // the update_stamp column
                assert.notEqual(updateStamp2.value, updateStamp1.value);
                assert.isTrue(updateStamp2.value - updateStamp1.value < 2000);
            });
        });

        // Why would we want this?
        it.skip('default time should not be applied to update/create stamps of provided', () =>
            Test.withContext(async context => {
                // Fetch the time on the db server
                const dbServerTime = datetime.fromJsDate(
                    (await context.executeSql<{ t: Date }[]>('SELECT NOW() t', []))[0].t,
                );

                const fixedDate = new Date('1973-01-01 00:00:00');

                const newId = (
                    await context.executeSql<{ id: string }>(
                        `INSERT INTO ${context.schemaName}.${tablename}
                                (
                                    val,
                                    _tenant_id,
                                    _update_tick,
                                    _update_stamp
                                )
                                VALUES
                                (
                                    1,
                                    ${context.tenantId},
                                    1,
                                    '${fixedDate.toISOString()}'
                                ) RETURNING _id as id;`,
                        [],
                    )
                ).id;

                const instance = await context.read(TestCreateUpdateStamp, { _id: newId });
                const createStamp = await instance.$.createStamp;
                const updateStamp = await instance.$.updateStamp;

                // createStamp should have been set to the time of the db server
                assert.isTrue(createStamp.value - dbServerTime.value < 100);
                // updateStamp should be the value provided in the payload
                // The 'update_table' function should have detected that a value was provided for
                // the update_stamp column
                assert.isTrue(updateStamp.value - datetime.fromJsDate(fixedDate).value < 100);
            }));

        // Why would we want this?
        it.skip('default time should not be applied to update/create stamps of provided', () =>
            Test.withCommittedContext(async context => {
                // Fetch the time on the db server
                const dbServerTime = datetime.fromJsDate(
                    (await context.executeSql<{ t: Date }[]>('SELECT NOW() t', []))[0].t,
                );

                let instance = await context.create(TestCreateUpdateStamp, {
                    val: 1,
                });
                await instance.$.save();
                const id = instance._id;
                instance = await context.read(TestCreateUpdateStamp, { _id: id }, { forUpdate: true });
                // createStamp should have been set to the time of the db server
                assert.isTrue((await instance.$.createStamp).value - dbServerTime.value < 100);
                // udpateStamp should have been set to the time of the db server
                assert.isTrue((await instance.$.updateStamp).value - dbServerTime.value < 100);

                await sleep(1500);

                await instance.$.set({ val: (await instance.val) + 1 });
                await instance.$.save();

                instance = await context.read(TestCreateUpdateStamp, { _id: id });
                // udpateStamp should have been set to the time of the db server
                assert.isTrue((await instance.$.updateStamp).value - dbServerTime.value > 100);
                assert.isTrue((await instance.$.updateStamp).value - dbServerTime.value < 2000);
            }));
    });

    after(() => restoreTables());
});
