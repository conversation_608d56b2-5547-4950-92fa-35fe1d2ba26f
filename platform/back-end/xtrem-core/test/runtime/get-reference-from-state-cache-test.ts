import { assert } from 'chai';
import { Collection, decorators, Node, Reference, Test } from '../../index';
import { codeDataType } from '../fixtures/data-types/data-types';
import { createApplicationWithApi, initTables, restoreTables, setup } from '../fixtures/index';

// let graphqlHelper: GraphQlHelper;

@decorators.node<TestStateCacheParentNode>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestStateCacheParentNode extends Node {
    @decorators.stringProperty<TestStateCacheParentNode, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestStateCacheParentNode, 'reference'>({
        isPublished: true,
        isNullable: true,
        isStored: true,
        node: () => TestStateCacheVitalCollectionChild,
    })
    readonly reference: Reference<TestStateCacheVitalCollectionChild | null>;

    @decorators.referenceArrayProperty<TestStateCacheParentNode, 'refArr'>({
        isPublished: true,
        isNullable: true,
        onDelete: 'restrict',
        isStored: true,
        node: () => TestStateCacheVitalCollectionChild,
    })
    readonly refArr: Promise<TestStateCacheVitalCollectionChild[]>;

    @decorators.collectionProperty<TestStateCacheParentNode, 'collection'>({
        isPublished: true,
        isVital: true,
        node: () => TestStateCacheVitalCollectionChild,
        reverseReference: 'parent',
    })
    readonly collection: Collection<TestStateCacheVitalCollectionChild>;
}
@decorators.node<TestStateCacheVitalCollectionChild>({
    isPublished: true,
    storage: 'sql',
    isVitalCollectionChild: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestStateCacheVitalCollectionChild extends Node {
    @decorators.stringProperty<TestStateCacheVitalCollectionChild, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestStateCacheVitalCollectionChild, 'parent'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => TestStateCacheParentNode,
    })
    readonly parent: Reference<TestStateCacheParentNode>;
}

describe('Get reference from state cache', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi({
                nodes: {
                    TestStateCacheParentNode,
                    TestStateCacheVitalCollectionChild,
                },
            }),
        });
        await initTables([
            { nodeConstructor: TestStateCacheParentNode, data: [] },
            { nodeConstructor: TestStateCacheVitalCollectionChild, data: [] },
        ]);

        // Initialization for reference test
        await Test.withCommittedContext(async context => {
            const parentInit = await context.create(TestStateCacheParentNode, {
                _id: 1,
                code: 'PAR1',
                reference: -1,
                refArr: [],
                collection: [
                    { _id: -1, code: 'COL1A' },
                    { _id: -2, code: 'COL1B' },
                ],
            });

            await parentInit.$.save();
        });

        // Initialization for referenceArray test
        await Test.withCommittedContext(async context => {
            const parentInit = await context.create(TestStateCacheParentNode, {
                _id: 2,
                code: 'PAR2',
                reference: null,
                collection: [
                    { _id: -1, code: 'COL1C' },
                    { _id: -2, code: 'COL1D' },
                ],
                refArr: [{ _id: 1 }],
            });

            await parentInit.$.save();
        });
    });

    it('should update a non-vital reference with a vital collection value', () =>
        Test.withUncommittedContext(async context => {
            const parent = await context.read(TestStateCacheParentNode, { _id: 1 }, { forUpdate: true });
            assert.equal(await (await parent.reference)?.code, await (await parent.collection.elementAt(0)).code);
            await parent.$.set({ reference: await parent.collection.elementAt(1) });
            await parent.$.save();

            const updatedParent = await context.read(TestStateCacheParentNode, { _id: 1 }, { forUpdate: true });
            // Previously this line would cause the test to fail
            assert.equal(await (await updatedParent.reference)?.code, 'COL1B');
            await (await updatedParent.collection.elementAt(1)).$.set({ code: 'XXXX' });
            await updatedParent.$.save();
            assert.equal(await (await updatedParent.reference)?.code, 'XXXX');
        }));

    it('should update a non-vital referenceArray with a vital collection value', () =>
        Test.withUncommittedContext(async context => {
            const parent = await context.read(TestStateCacheParentNode, { _id: 2 }, { forUpdate: true });
            // array properties should be immutable when retrieved
            // set the entire refArr to invoke mutator
            await parent.$.set({ refArr: [await parent.collection.elementAt(1)] });
            await parent.$.save();

            const updatedParent = await context.read(TestStateCacheParentNode, { _id: 2 }, { forUpdate: true });

            // Previously this line would cause the test to fail
            assert.equal(await (await updatedParent.refArr)[0]?.code, 'COL1D');
        }));

    after(() => restoreTables());
});
