import { assert } from 'chai';
import { Test } from '../../lib';
import {
    createApplicationWithApi,
    initTables,
    referredData,
    requiredPropertyData,
    restoreTables,
    setup,
} from '../fixtures/index';
import { TestReferred, TestRequiredProperty } from '../fixtures/nodes';

describe('Required property tests', () => {
    before(async () =>
        setup({
            application: await createApplicationWithApi({ nodes: { TestReferred, TestRequiredProperty } }),
        }),
    );
    beforeEach(() =>
        initTables([
            { nodeConstructor: TestReferred, data: referredData },
            { nodeConstructor: TestRequiredProperty, data: requiredPropertyData },
        ]),
    );
    it('can create record with required and non-nullable properties', () =>
        Test.uncommitted(async context => {
            const node = await context.create(TestRequiredProperty, {
                notRequiredNotNullable: 3,
                requiredReference: 1,
                requiredReferenceArray: [1, 1],
            });
            await node.$.save();

            const readNode = await context.read(TestRequiredProperty, { _id: node._id });
            assert.equal(await readNode.notRequiredNotNullable, 3);
            assert.isNull(await readNode.notRequiredNullable);
            assert.equal((await readNode.requiredReference)?._id, 1);

            assert.deepEqual(
                (await readNode.requiredReferenceArray)?.map(r => r._id),
                [1, 1],
            );
        }));

    it('cannot create without required property', () =>
        Test.uncommitted(async context => {
            const node = await context.create(TestRequiredProperty, {
                notRequiredNotNullable: 3,
            });

            await assert.isRejected(node.$.save());
            assert.deepEqual(context.diagnoses, [
                {
                    message: 'TestRequiredProperty.requiredReference: property is required',
                    path: ['requiredReference'],
                    severity: 4,
                },
            ]);
        }));

    it('cannot create record with required reference array passed as empty array', () =>
        Test.uncommitted(async context => {
            const node = await context.create(TestRequiredProperty, {
                notRequiredNotNullable: 3,
                requiredReference: 1,
                requiredReferenceArray: [],
            });
            await assert.isRejected(node.$.save(), /was not created/);
            assert.deepEqual(context.diagnoses, [
                {
                    severity: 3,
                    path: ['requiredReferenceArray'],
                    message: 'array cannot be empty',
                },
            ]);
        }));

    it('cannot set a required property to null from node property', () =>
        Test.uncommitted(async context => {
            const node = await context.read(TestRequiredProperty, { _id: 1 }, { forUpdate: true });

            await node.$.set({ requiredReference: null as any });
            await assert.isRejected(node.$.save(), /was not updated/);
            assert.deepEqual(context.diagnoses, [
                {
                    severity: 4,
                    path: ['requiredReference'],
                    message: 'TestRequiredProperty.requiredReference: property is required',
                },
            ]);
        }));

    it('can set a non-required nullable property to null', () =>
        Test.uncommitted(async context => {
            const node = await context.read(TestRequiredProperty, { _id: 1 }, { forUpdate: true });
            await node.$.set({ notRequiredNullable: 8 });
            await node.$.save();
            const node2 = await context.read(TestRequiredProperty, { _id: 1 }, { forUpdate: true });
            assert.equal(await node2.notRequiredNullable, 8);
            await node2.$.set({ notRequiredNullable: null });
            await node2.$.save();
            const node3 = await context.read(TestRequiredProperty, { _id: 1 });
            assert.isNull(await node3.notRequiredNullable);
        }));

    it('cannot insert via factory without required property', () =>
        Test.uncommitted(async context => {
            const factory = Test.application.getFactoryByConstructor(TestRequiredProperty);
            await assert.isRejected(
                factory.table.insert(context, {
                    notRequiredNotNullable: 5,
                    requiredNotNullable: 5,
                }),
                'null value in column "required_reference" of relation "test_required_property" violates not-null constraint',
            );
        }));

    it('cannot update via factory without required reference property', () =>
        Test.uncommitted(async context => {
            const factory = Test.application.getFactoryByConstructor(TestRequiredProperty);
            await assert.isRejected(
                factory.table.update(context, {
                    _id: 1,
                    notRequiredNotNullable: 5,
                    requiredNotNullable: 5,
                }),
                'Cannot insert null value in column test_required_property.required_reference',
            );
        }));

    after(() => restoreTables());
});
