import { assert } from 'chai';
import { retry } from '../../lib/runtime/utils';

class RetryTester {
    constructor(
        private readonly numberOfFailuresBeforeSuccess: number,
        private readonly timeToFailBeforeSuccess: number,
    ) {}

    public numberOfCalls = 0;

    public creationDate = Date.now();

    public testRetries(): string {
        const timeOfCall = Date.now();
        this.numberOfCalls += 1;
        if (
            this.numberOfCalls > this.numberOfFailuresBeforeSuccess &&
            timeOfCall >= this.creationDate + this.timeToFailBeforeSuccess
        ) {
            return 'Success!';
        }

        if (this.numberOfCalls <= this.numberOfFailuresBeforeSuccess) {
            throw new Error(
                `Function has been called ${this.numberOfCalls} time(s) of ${this.numberOfFailuresBeforeSuccess} failures.`,
            );
        } else {
            throw new Error(`Function was called before the failure timeout of ${this.timeToFailBeforeSuccess} ms`);
        }
    }
}

describe('Retry function', () => {
    it('Tries once with success', async () => {
        for (let numberOfTries = 1; numberOfTries < 6; numberOfTries += 1) {
            const retryTester = new RetryTester(numberOfTries - 1, 0);
            try {
                const result = await retry(() => retryTester.testRetries(), {
                    maxTries: numberOfTries,
                    delayBeforeRetry: 0,
                });
                assert.equal(retryTester.numberOfCalls, numberOfTries);
                assert.equal(result, 'Success!');
            } catch (error) {
                assert.fail(`retry failed: ${error}`);
            }
        }
    });

    it('Tries only once with fail if maxTries = 0', async () => {
        const retryTester = new RetryTester(1, 0);
        try {
            await retry(() => retryTester.testRetries(), { maxTries: 0, delayBeforeRetry: 0 });
            if (retryTester.numberOfCalls > 1) {
                assert.fail(`The retry function tried too many times: ${retryTester.numberOfCalls}.`);
            } else {
                assert.fail(
                    `Something is wrong with the RetryTester.
                    It should not succeed after 1 try with the given parameters.`,
                );
            }
        } catch (error) {
            assert.equal(retryTester.numberOfCalls, 1);
            assert.equal(error.message, 'Function has been called 1 time(s) of 1 failures.\r\n\r\n');
        }
    });

    it('Retries until success', async () => {
        const retryTester = new RetryTester(4, 0);
        try {
            const result = await retry(() => retryTester.testRetries(), { maxTries: 5, delayBeforeRetry: 0 });
            assert.equal(retryTester.numberOfCalls, 5);
            assert.equal(result, 'Success!');
        } catch (error) {
            assert.fail(`retry failed: ${error}`);
        }
    });

    it('Retries after a delay', async () => {
        const retryTester = new RetryTester(0, 100);
        try {
            const result = await retry(() => retryTester.testRetries(), { maxTries: 4, delayBeforeRetry: 40 });
            assert.equal(result, 'Success!');
        } catch (error) {
            assert.fail(`retry failed: ${error}`);
        }
    });
});
