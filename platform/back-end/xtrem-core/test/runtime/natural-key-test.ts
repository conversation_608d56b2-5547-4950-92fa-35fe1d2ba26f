import { assert } from 'chai';
import { Test } from '../../index';
import { createApplicationWithApi, initTables, setup } from '../fixtures/index';
import {
    InvalidNaturalKeyMultiple,
    InvalidNaturalKeyNotPublished,
    InvalidNaturalKeyNotUnique,
    InvalidNaturalKeyString,
    SetupNodeNoNaturalKey,
    TestNaturalKey,
    TestNaturalKeyComposite,
    TestNaturalKeyCompositePartialNullable,
    TestNaturalKeyCompositePartialNullableParent,
    TestNaturalKeyParent,
    TestNaturalKeyVitalChild,
    TestReferenceOnVitalChild,
    TestReferenceToNaturalKey,
} from '../fixtures/nodes/natural-key-nodes';
import { TestReferred } from '../fixtures/nodes/referred';

describe('Natural key validations', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi({
                nodes: {
                    TestNatural<PERSON><PERSON>,
                    TestNaturalKeyComposite,
                    TestReferenceToNaturalKey,
                    TestNaturalKeyCompositePartialNullable,
                    TestNaturalKeyVitalChild,
                    TestNaturalKeyParent,
                    TestReferenceOnVitalChild,
                },
            }),
        });
        await initTables([
            {
                nodeConstructor: TestNaturalKey,
                data: [
                    { key: 'K1', text: 'text 1' },
                    { key: 'K2', text: 'text 2' },
                ],
            },
            {
                nodeConstructor: TestReferenceToNaturalKey,
                data: [
                    { id: 'REF1', ref: '#K1' },
                    { id: 'REF2', ref: '#K2' },
                ],
            },
            {
                nodeConstructor: TestNaturalKeyParent,
                data: [
                    {
                        key: 'REF11',
                        text: 'textKey',
                    },
                ],
            },
            {
                nodeConstructor: TestNaturalKeyVitalChild,
                data: [
                    { parent: '#REF11', code1: 'code11', code2: 'code12' },
                    { parent: '#REF11', code1: 'code21', ref2: 'code22' },
                ],
            },
            {
                nodeConstructor: TestReferenceOnVitalChild,
                data: [{ vitalChildRef: '#REF11|code11' }],
            },
        ]);
    });

    it('can filter on a reference on a natural key', () =>
        Test.withContext(async context => {
            const ids = await context
                .query(TestReferenceToNaturalKey, { filter: { ref: '#K2' } })
                .map(node => node.id)
                .toArray();
            assert.deepEqual(ids, ['REF2']);
        }));

    it('can filter with _in on a natural key', () =>
        Test.withContext(async context => {
            const ids = await context
                .query(TestReferenceToNaturalKey, { filter: { ref: { _in: ['#K2', '#K3'] } } })
                .map(node => node.id)
                .toArray();
            assert.deepEqual(ids, ['REF2']);
        }));

    it('Cannot create application with invalid natural key - index not unique', async () => {
        await assert.isRejected(
            createApplicationWithApi({ nodes: { InvalidNaturalKeyNotUnique } }),
            'InvalidNaturalKeyNotUnique.invalid_natural_key_not_unique_ind0 natural key must be a unique index.',
        );
    });

    it('Cannot create application with invalid natural key - not all index properties published', async () => {
        await assert.isRejected(
            createApplicationWithApi({ nodes: { InvalidNaturalKeyNotPublished } }),
            'Natural key index InvalidNaturalKeyNotPublished.invalid_natural_key_not_published_ind0 must have these properties published or assigned a defaultValue [key2]',
        );
    });

    it('Cannot create application with invalid natural key - index contains localized or encrypted strings', async () => {
        await assert.isRejected(
            createApplicationWithApi({ nodes: { InvalidNaturalKeyString } }),
            'InvalidNaturalKeyString.invalid_natural_key_string_ind0 contains properties with types not allowed in an index [key (string - localized),key2 (string - encrypted)]',
        );
    });

    it('Cannot create application with invalid natural key - Sub node cannot override base node natural index', async () => {
        await assert.isRejected(
            createApplicationWithApi({ nodes: { InvalidNaturalKeyMultiple } }),
            'InvalidNaturalKeyMultiple: InvalidNaturalKeyMultiple cannot have more than 1 index with isNaturalKey set to true.',
        );
    });

    it('can add natural key to vital child', () =>
        Test.withContext(async context => {
            const node = await context.read(TestReferenceOnVitalChild, { _id: 1 });
            assert.deepEqual(await (await node.vitalChildRef).code1, 'code11');
            assert.deepEqual(await (await (await node.vitalChildRef).parent).key, 'REF11');
        }));

    it('Cannot create application with invalid setup node withhout a natural key', async () => {
        await assert.isRejected(
            createApplicationWithApi({
                nodes: { TestReferred, SetupNodeNoNaturalKey },
            }),
            'SetupNodeNoNaturalKey setup nodes must have a natural key.',
        );
    });
});

describe('Partial natural key validations', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi({
                nodes: {
                    TestNaturalKey,
                    TestNaturalKeyCompositePartialNullable,
                    TestNaturalKeyCompositePartialNullableParent,
                },
            }),
        });
        await initTables([
            {
                nodeConstructor: TestNaturalKey,
                data: [
                    { key: 'K1', text: 'text 1' },
                    { key: 'K2', text: 'text 2' },
                ],
            },
            {
                nodeConstructor: TestNaturalKeyCompositePartialNullable,
                data: [
                    { key: 'TNKCPN1', keyInteger: 1, keyReference: '#K1', text: '1' },
                    { key: 'TNKCPN2', keyReference: '#K2', text: '2' },
                    { key: 'TNKCPN3', keyInteger: 2, text: '3' },
                    { key: 'TNKCPN1', text: '4' },
                ],
            },
            {
                nodeConstructor: TestNaturalKeyCompositePartialNullableParent,
                data: [
                    { key: 'Key1', keyReference: '#TNKCPN1|1|K1', text: 'text 1' },
                    { key: 'Key2', keyReference: '#TNKCPN2||K2', text: 'text 2' },
                    { key: 'Key3', keyReference: '#TNKCPN3|2', text: 'text 3' },
                    { key: 'Key4', keyReference: '#TNKCPN1', text: 'text 4' },
                ],
            },
        ]);
    });

    it('can filter on a reference on a natural key', () =>
        Test.withContext(async context => {
            let text = await context
                .query(TestNaturalKeyCompositePartialNullableParent, { filter: { keyReference: '#TNKCPN1|1|K1' } })
                .map(node => node.text)
                .toArray();
            assert.deepEqual(text, ['text 1']);

            text = await context
                .query(TestNaturalKeyCompositePartialNullableParent, { filter: { keyReference: '#TNKCPN2||K2' } })
                .map(node => node.text)
                .toArray();
            assert.deepEqual(text, ['text 2']);

            text = await context
                .query(TestNaturalKeyCompositePartialNullableParent, { filter: { keyReference: '#TNKCPN3|2' } })
                .map(node => node.text)
                .toArray();
            assert.deepEqual(text, ['text 3']);

            text = await context
                .query(TestNaturalKeyCompositePartialNullableParent, { filter: { keyReference: '#TNKCPN1' } })
                .map(node => node.text)
                .toArray();
            assert.deepEqual(text, ['text 4']);

            text = await context
                .query(TestNaturalKeyCompositePartialNullableParent, { filter: { keyReference: '#TNKCPN3' } })
                .map(node => node.text)
                .toArray();
            assert.deepEqual(text, []);
        }));

    it('can filter with _in on a natural key', () =>
        Test.withContext(async context => {
            const text = await context
                .query(TestNaturalKeyCompositePartialNullableParent, {
                    filter: { keyReference: { _in: ['#TNKCPN1|1|K1', '##TNKCPN1|1|K2'] } },
                })
                .map(node => node.text)
                .toArray();
            assert.deepEqual(text, ['text 1']);
        }));
});
