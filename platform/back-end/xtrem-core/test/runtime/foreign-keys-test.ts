import { assert } from 'chai';
import { decorators, Dict, Node, Reference, Test, ValidationSeverity } from '../../index';
import { ApplicationCreateSqlSchema } from '../../lib/application';
import * as fixtures from '../fixtures';
import { codeDataType, descriptionDataType } from '../fixtures/data-types/data-types';
import { createApplicationWithApi, dropTestTable, referringData, restoreTables, setup } from '../fixtures/index';

const { TestReferring, TestReferred } = fixtures.nodes;

@decorators.node<TestForeign1>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestForeign1 extends Node {
    @decorators.stringProperty<TestForeign1, 'code'>({
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestForeign1, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly description: Promise<string>;
}

@decorators.node<TestForeignReffering1>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestForeignReffering1 extends Node {
    @decorators.stringProperty<TestForeignReffering1, 'code'>({
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestForeignReffering1, 'reference'>({
        isPublished: true,
        isStored: true,
        node: () => TestForeign1,
        isNullable: true,
    })
    readonly reference: Reference<TestForeign1 | null>;
}

@decorators.node<TestForeignReffering2>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestForeignReffering2 extends Node {
    @decorators.stringProperty<TestForeignReffering2, 'code'>({
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestForeignReffering2, 'reference'>({
        isPublished: true,
        isStored: true,
        node: () => TestForeignReffering1,
        isNullable: true,
    })
    readonly reference: Reference<TestForeignReffering1 | null>;
}

@decorators.node<TestForeignReffering3>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestForeignReffering3 extends Node {
    @decorators.stringProperty<TestForeignReffering3, 'code'>({
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestForeignReffering3, 'reference1'>({
        isPublished: true,
        isStored: true,
        node: () => TestForeignReffering1,
        isNullable: true,
    })
    readonly reference1: Reference<TestForeignReffering1 | null>;

    @decorators.referenceProperty<TestForeignReffering3, 'reference2'>({
        isPublished: true,
        isStored: true,
        node: () => TestForeignReffering2,
        isNullable: true,
    })
    readonly reference2: Reference<TestForeignReffering2 | null>;

    @decorators.referenceProperty<TestForeignReffering3, 'reference3'>({
        isPublished: true,
        isStored: true,
        node: () => TestForeign1,
        isNullable: true,
    })
    readonly reference3: Reference<TestForeign1 | null>;
}

@decorators.node<TestForeignNullable1>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestForeignNullable1 extends Node {
    @decorators.stringProperty<TestForeignNullable1, 'code'>({
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestForeignNullable1, 'parent'>({
        isPublished: true,
        isStored: true,
        node: () => TestForeignNullable1,
    })
    readonly parent: Reference<TestForeignNullable1>;
}

@decorators.node<TestForeignNullable2>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestForeignNullable2 extends Node {
    @decorators.stringProperty<TestForeignNullable2, 'code'>({
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestForeignNullable2, 'reference2'>({
        isPublished: true,
        isStored: true,
        node: () => TestForeignNullable3,
        defaultValue: 0 as any, // Keep this to make upgrade happy
    })
    readonly reference2: Reference<TestForeignNullable3>;
}

@decorators.node<TestForeignNullable3>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestForeignNullable3 extends Node {
    @decorators.stringProperty<TestForeignNullable3, 'code'>({
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestForeignNullable3, 'reference3'>({
        isPublished: true,
        isStored: true,
        node: () => TestForeignNullable4,
        defaultValue: 0 as any, // Keep this to make upgrade happy
    })
    readonly reference3: Reference<TestForeignNullable4>;
}

@decorators.node<TestForeignNullable4>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestForeignNullable4 extends Node {
    @decorators.stringProperty<TestForeignNullable4, 'code'>({
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestForeignNullable4, 'reference4'>({
        isPublished: true,
        isStored: true,
        node: () => TestForeignNullable2,
        defaultValue: 0 as any, // Keep this to make upgrade happy
    })
    readonly reference4: Reference<TestForeignNullable2>;
}

@decorators.node<TestCyclic1>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestCyclic1 extends Node {
    @decorators.stringProperty<TestCyclic1, 'code'>({
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestCyclic1, 'parent'>({
        isPublished: true,
        isStored: true,
        node: () => TestCyclic2,
        isNullable: true,
    })
    readonly parent: Reference<TestCyclic2>;
}

@decorators.node<TestCyclic2>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestCyclic2 extends Node {
    @decorators.stringProperty<TestCyclic2, 'code'>({
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestCyclic2, 'reference2'>({
        isPublished: true,
        isStored: true,
        node: () => TestCyclic3,
    })
    readonly reference2: Reference<TestCyclic3>;
}

@decorators.node<TestCyclic3>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestCyclic3 extends Node {
    @decorators.stringProperty<TestCyclic3, 'code'>({
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestCyclic3, 'reference3'>({
        isPublished: true,
        isStored: true,
        node: () => TestCyclic1,
    })
    readonly reference3: Reference<TestCyclic1>;
}

describe('foreign key tests', () => {
    it('verify factory', async () => {
        await setup({ application: await createApplicationWithApi({ nodes: { TestReferring, TestReferred } }) });
        await fixtures.initTables([]);
        await Test.withContext(async context => {
            const referredFactory = Test.application.getFactoryByConstructor(TestReferred);
            await dropTestTable(TestReferring);
            await dropTestTable(TestReferred);
            await ApplicationCreateSqlSchema.verifyAndCreateTables(context);

            assert(await referredFactory.table.tableExists(context));
        });
    });

    it('Simple foreign key violation', async () => {
        await setup({ application: await createApplicationWithApi({ nodes: { TestReferring, TestReferred } }) });
        await fixtures.initTables([]);
        await Test.withContext(async context => {
            await dropTestTable(TestReferring);
            await dropTestTable(TestReferred);
            await ApplicationCreateSqlSchema.verifyAndCreateTables(context);
            const referringNode = await context.create(TestReferring, { ...referringData[0], _id: undefined });
            await assert.isRejected(referringNode.$.save(), /was not created/);
            assert.deepEqual(context.diagnoses, [
                {
                    severity: ValidationSeverity.exception,
                    path: ['reference'],
                    message: 'TestReferred: record not found: {"_id":1}',
                },
            ]);
        });
    });

    it('verify factory recursive table creation - check foreign keys', async () => {
        await setup({
            application: await createApplicationWithApi({
                nodes: { TestForeign1, TestForeignReffering1, TestForeignReffering2, TestForeignReffering3 },
            }),
        });
        await fixtures.initTables([]);
        await Test.withContext(async context => {
            const foreign1Factory = Test.application.getFactoryByConstructor(TestForeign1);
            const foreignReffering1Factory = Test.application.getFactoryByConstructor(TestForeignReffering1);
            const foreignReffering2Factory = Test.application.getFactoryByConstructor(TestForeignReffering2);
            const foreignReffering3Factory = Test.application.getFactoryByConstructor(TestForeignReffering3);
            await dropTestTable(TestForeign1);
            await dropTestTable(TestForeignReffering1);
            await dropTestTable(TestForeignReffering2);
            await dropTestTable(TestForeignReffering3);

            // all dependent tables don't exist
            assert(!(await foreign1Factory.table.tableExists(context)));
            assert(!(await foreignReffering1Factory.table.tableExists(context)));
            assert(!(await foreignReffering2Factory.table.tableExists(context)));
            assert(!(await foreignReffering3Factory.table.tableExists(context)));

            // Verify lowest level node
            await ApplicationCreateSqlSchema.verifyAndCreateTables(context);

            // all dependent tables exist
            assert(await foreign1Factory.table.tableExists(context));

            assert(await foreignReffering1Factory.table.tableExists(context));
            assert.equal(foreignReffering1Factory.table.foreignKeys?.length, 3);
            assert.equal(foreignReffering1Factory.table.foreignKeys[0].targetTable, 'test_user');
            assert.equal(foreignReffering1Factory.table.foreignKeys[1].targetTable, 'test_user');
            assert.equal(foreignReffering1Factory.table.foreignKeys[2].targetTable, 'test_foreign_1');

            assert(await foreignReffering2Factory.table.tableExists(context));
            assert.equal(foreignReffering2Factory.table.foreignKeys?.length, 3);
            assert.equal(foreignReffering2Factory.table.foreignKeys[0].targetTable, 'test_user');
            assert.equal(foreignReffering2Factory.table.foreignKeys[1].targetTable, 'test_user');
            assert.equal(foreignReffering2Factory.table.foreignKeys[2].targetTable, 'test_foreign_reffering_1');

            assert(await foreignReffering3Factory.table.tableExists(context));
            assert.equal(foreignReffering3Factory.table.foreignKeys?.length, 5);
            assert.equal(foreignReffering3Factory.table.foreignKeys[0].targetTable, 'test_user');
            assert.equal(foreignReffering3Factory.table.foreignKeys[1].targetTable, 'test_user');
            assert.equal(foreignReffering3Factory.table.foreignKeys[2].targetTable, 'test_foreign_reffering_1');
            assert.equal(foreignReffering3Factory.table.foreignKeys[3].targetTable, 'test_foreign_reffering_2');
            assert.equal(foreignReffering3Factory.table.foreignKeys[4].targetTable, 'test_foreign_1');
        });
    });

    // Test needed to split between several Test.committed, cannot insert into table
    // and verify factory in the same transaction table has transaction lock on it
    // and will hang causing mocha timeout.
    it('data is correct after verifying recursive node', async () => {
        const foreign1Factory = Test.application.getFactoryByConstructor(TestForeign1);
        const foreignReffering1Factory = Test.application.getFactoryByConstructor(TestForeignReffering1);
        const foreignReffering2Factory = Test.application.getFactoryByConstructor(TestForeignReffering2);
        const foreignReffering3Factory = Test.application.getFactoryByConstructor(TestForeignReffering3);
        await fixtures.initTables([]);
        await dropTestTable(TestForeign1);
        await dropTestTable(TestForeignReffering1);
        await dropTestTable(TestForeignReffering2);
        await dropTestTable(TestForeignReffering3);

        await Test.withCommittedContext(async context => {
            // all dependent tables don't exist
            assert(!(await foreign1Factory.table.tableExists(context)));
            assert(!(await foreignReffering1Factory.table.tableExists(context)));
            assert(!(await foreignReffering2Factory.table.tableExists(context)));
            assert(!(await foreignReffering3Factory.table.tableExists(context)));
            // Verify TestForeign1 node
            await ApplicationCreateSqlSchema.verifyAndCreateTables(context);
        });

        const oldValues: Dict<any> = {};
        const propertyNames = ['_createStamp', '_updateStamp', '_id', 'code', 'description'];

        await Test.withCommittedContext(async context => {
            const foreignNode = await context.create(TestForeign1, { code: 'FOR' });
            await foreignNode.$.save();
            propertyNames.forEach(key => {
                oldValues[key] = foreignNode.$.state.values[key] || '';
            });
            // Verify lowest level node
            // TestForeign1 is a reference in TestForeignReffering1 and TestForeignReffering3
            // Foreign key must be added to TestForeignReffering3 but not lose TestForeign1
            await ApplicationCreateSqlSchema.verifyAndCreateTables(context);
        });

        await Test.withCommittedContext(async context => {
            const foreignReffering3Node = await context.create(TestForeignReffering3, {
                code: 'FR3',
                reference3: oldValues._id,
            });
            await foreignReffering3Node.$.save();
            const foreignNode2 = await context.read(TestForeign1, { code: 'FOR' });

            // we haven't lost the TestForeign1 node data after verifying node that is dependent on it
            propertyNames.forEach(key => {
                assert.deepEqual(oldValues[key], foreignNode2.$.state.values[key]);
            });

            // correct TestForeign1 is retrieved when getting reference3 property
            assert.deepEqual(oldValues._id, (await foreignReffering3Node.reference3)!._id);
        });
    });

    it('Cyclic foreign key without nullable reference property', async () => {
        await setup({
            application: await createApplicationWithApi({
                nodes: { TestForeignNullable1 },
            }),
        });
        await Test.application.createContextForDdl(async context => {
            await assert.isRejected(
                ApplicationCreateSqlSchema.verifyAndCreateTables(context),
                'A cyclic reference exists (TestForeignNullable1=>TestForeignNullable1). A nullable property is required in the dependency tree for a cyclic reference.',
            );
        });
        await dropTestTable(TestForeignNullable1);
    });

    it('Cyclic foreign key without nullable reference property - multi levels', async () => {
        await setup({
            application: await createApplicationWithApi({
                nodes: { TestForeignNullable2, TestForeignNullable3, TestForeignNullable4 },
            }),
        });
        await Test.application.createContextForDdl(async context => {
            await assert.isRejected(
                ApplicationCreateSqlSchema.verifyAndCreateTables(context),
                'A cyclic reference exists (TestForeignNullable2=>TestForeignNullable4=>TestForeignNullable3=>TestForeignNullable2). A nullable property is required in the dependency tree for a cyclic reference.',
            );
        });
    });

    it('Cyclic foreign key without nullable reference property - multi levels (middle node)', async () => {
        await setup({
            application: await createApplicationWithApi({
                nodes: { TestForeignNullable3, TestForeignNullable2, TestForeignNullable4 },
            }),
        });
        await Test.application.createContextForDdl(async context => {
            await assert.isRejected(
                ApplicationCreateSqlSchema.verifyAndCreateTables(context),
                'A cyclic reference exists (TestForeignNullable3=>TestForeignNullable2=>TestForeignNullable4=>TestForeignNullable3). A nullable property is required in the dependency tree for a cyclic reference.',
            );
        });
    });

    it('recursive node foreign key violation', async () => {
        await setup({
            application: await createApplicationWithApi({
                nodes: { TestCyclic1, TestCyclic2, TestCyclic3 },
            }),
        });

        await fixtures.initTables([]);

        await Test.withContext(async context => {
            const cyclic1Factory = Test.application.getFactoryByConstructor(TestCyclic1);
            const cyclic2Factory = Test.application.getFactoryByConstructor(TestCyclic2);
            const cyclic3Factory = Test.application.getFactoryByConstructor(TestCyclic3);
            await dropTestTable(TestCyclic1);
            await dropTestTable(TestCyclic2);
            await dropTestTable(TestCyclic3);

            // all dependent tables don't exist
            assert(!(await cyclic1Factory.table.tableExists(context)));
            assert(!(await cyclic2Factory.table.tableExists(context)));
            assert(!(await cyclic3Factory.table.tableExists(context)));

            await ApplicationCreateSqlSchema.verifyAndCreateTables(context);
            const cyclic1Node = await context.create(TestCyclic1, { code: 'PK1' });
            await cyclic1Node.$.save();
            await ApplicationCreateSqlSchema.verifyAndCreateTables(context);
            const cyclic3Node = await context.create(TestCyclic3, { code: 'FK3', reference3: cyclic1Node._id });
            await cyclic3Node.$.save();
        });
    });

    after(() => restoreTables());
});
