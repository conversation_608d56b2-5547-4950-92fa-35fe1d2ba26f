import { assert } from 'chai';
import { Test, decorators } from '../../index';
import { testSubclassingIsFrozenChainingApplication } from '../fixtures/applications';
import { createApplicationWithApi, initTables, restoreTables, setup } from '../fixtures/index';
import {
    TestFrozenChaining,
    TestFrozenChainingBase,
    TestOverrideFrozenChainingCallback,
    TestOverrideFrozenChainingValueFalse,
    TestOverrideFrozenChainingValueTrue,
} from '../fixtures/nodes';

describe('frozen sub-classing chaining', () => {
    before(async () => {
        await setup({ application: await testSubclassingIsFrozenChainingApplication.application });
        await initTables([
            { nodeConstructor: TestFrozenChaining, data: [] },
            { nodeConstructor: TestFrozenChainingBase, data: [] },
            { nodeConstructor: TestOverrideFrozenChainingValueTrue, data: [] },
            { nodeConstructor: TestOverrideF<PERSON>zenChainingValueFalse, data: [] },
            { nodeConstructor: TestOverrideFrozenChainingCallback, data: [] },
        ]);
    });

    it('can create regardless of frozen values', () =>
        Test.withContext(async context => {
            const node = await context.create(TestFrozenChaining, { code: 'FROZEN', description: 'FROZEN' });
            await node.$.save();
            assert.equal(await node.code, 'FROZEN');
            assert.equal(await node.description, 'FROZEN');
        }));

    it('cannot update when node is frozen', () =>
        Test.withContext(async context => {
            const node = await context.create(TestFrozenChaining, { code: 'FROZEN', description: 'Desc A' });
            await node.$.save();
            const readResult = await context.read(TestFrozenChaining, { code: 'FROZEN' }, { forUpdate: true });
            await assert.isRejected(readResult.$.set({ description: 'Desc X' }), 'TestFrozenChaining: node is frozen');
        }));

    it('cannot update frozen property', () =>
        Test.withContext(async context => {
            const node = await context.create(TestFrozenChaining, { code: 'A', description: 'FROZEN' });
            await node.$.save();
            const readResult = await context.read(TestFrozenChaining, { code: 'A' }, { forUpdate: true });
            await assert.isRejected(
                readResult.$.set({ description: 'Desc X' }),
                'TestFrozenChaining.description: cannot set value on frozen property',
            );
        }));

    it('can update non-frozen property', () =>
        Test.withContext(async context => {
            const node = await context.create(TestFrozenChaining, { code: 'A', description: 'FROZEN' });
            await node.$.save();
            const readResult = await context.read(TestFrozenChaining, { code: 'A' }, { forUpdate: true });
            await readResult.$.set({ code: 'B' });
            await readResult.$.save();
            assert.strictEqual(await readResult.code, 'B');
        }));

    // isFrozen turns to false
    it('isFrozen turns to false: can update sub node with non-frozen property', () =>
        Test.withContext(async context => {
            const node = await context.create(TestOverrideFrozenChainingValueFalse, {
                code: 'A1',
                strFromOverrideNotFrozen: 'strFromOverrideNotFrozen',
                callbackFrozen: 'callbackFrozen',
                valueFrozen: 'valueFrozen',
                notFrozen: 'notFrozen',
            });
            await node.$.save();
            const readResult = await context.read(
                TestOverrideFrozenChainingValueFalse,
                { code: 'A1' },
                { forUpdate: true },
            );
            assert.strictEqual(await readResult.callbackFrozen, 'callbackFrozen');
            await readResult.$.set({ callbackFrozen: 'FROZEN', notFrozen: 'Updated text' });
            await readResult.$.save();
            assert.strictEqual(await readResult.callbackFrozen, 'FROZEN');
            assert.strictEqual(await readResult.notFrozen, 'Updated text');
        }));

    it('isFrozen turns to false: cannot update sub node with isFrozen override on property', () =>
        Test.withContext(async context => {
            const node = await context.create(TestOverrideFrozenChainingValueFalse, {
                code: 'A1',
                strFromOverrideNotFrozen: 'strFromOverrideNotFrozen',
                callbackFrozen: 'FROZEN',
                valueFrozen: 'valueFrozen',
                notFrozen: 'notFrozen',
            });
            await node.$.save();
            const readResult = await context.read(
                TestOverrideFrozenChainingValueFalse,
                { code: 'A1' },
                { forUpdate: true },
            );
            await assert.isRejected(
                readResult.$.set({ callbackFrozen: 'Updated text' }),
                'TestOverrideFrozenChainingValueFalse.callbackFrozen: cannot set value on frozen property',
            );
        }));

    // isFrozen turns to true
    it('isFrozen turns to true: can update sub node with non-frozen property', () =>
        Test.withContext(async context => {
            const node = await context.create(TestOverrideFrozenChainingValueTrue, {
                code: 'A1',
                strFromOverrideNotFrozen: 'strFromOverrideNotFrozen',
                callbackFrozen: 'callbackFrozen',
                valueFrozen: 'valueFrozen',
                notFrozen: 'notFrozen',
            });
            await node.$.save();
            const readResult = await context.read(
                TestOverrideFrozenChainingValueTrue,
                { code: 'A1' },
                { forUpdate: true },
            );
            assert.strictEqual(await readResult.callbackFrozen, 'callbackFrozen');
            await assert.isRejected(
                readResult.$.set({ callbackFrozen: 'Updated text' }),
                'TestOverrideFrozenChainingValueTrue.callbackFrozen: cannot set value on frozen property',
            );
        }));

    it('isFrozen turns to true: cannot update sub node with isFrozen override on property', () =>
        Test.withContext(async context => {
            const node = await context.create(TestOverrideFrozenChainingValueTrue, {
                code: 'A1',
                strFromOverrideNotFrozen: 'strFromOverrideNotFrozen',
                callbackFrozen: 'FROZEN',
                valueFrozen: 'valueFrozen',
                notFrozen: 'notFrozen',
            });
            await node.$.save();
            const readResult = await context.read(
                TestOverrideFrozenChainingValueTrue,
                { code: 'A1' },
                { forUpdate: true },
            );
            await assert.isRejected(
                readResult.$.set({ callbackFrozen: 'Updated text' }),
                'TestOverrideFrozenChainingValueTrue.callbackFrozen: cannot set value on frozen property',
            );
        }));

    // isFrozen depends on callback
    it('isFrozen depends on callback: can update sub node with non-frozen property (callback on base node) then frozen on next update because of base node', () =>
        Test.withContext(async context => {
            const node = await context.create(TestOverrideFrozenChainingCallback, {
                code: 'A1',
                strFromOverrideNotFrozen: 'strFromOverrideNotFrozen',
                callbackFrozen: 'callbackFrozen',
                valueFrozen: 'valueFrozen',
                notFrozen: 'notFrozen',
            });
            await node.$.save();
            const readResult = await context.read(
                TestOverrideFrozenChainingCallback,
                { code: 'A1' },
                { forUpdate: true },
            );
            assert.strictEqual(await readResult.callbackFrozen, 'callbackFrozen');
            await readResult.$.set({ callbackFrozen: 'FROZEN', notFrozen: 'Updated text' });
            await readResult.$.save();
            assert.strictEqual(await readResult.callbackFrozen, 'FROZEN');
            assert.strictEqual(await readResult.notFrozen, 'Updated text');
            const readResultFrozen = await context.read(
                TestOverrideFrozenChainingCallback,
                { code: 'A1' },
                { forUpdate: true },
            );
            await assert.isRejected(
                readResultFrozen.$.set({ callbackFrozen: 'Updated text' }),
                'TestOverrideFrozenChainingCallback.callbackFrozen: cannot set value on frozen property',
            );
        }));

    it('isFrozen depends on callback: can update sub node with non-frozen property (callback on base node) then frozen on next update because of sub node', () =>
        Test.withContext(async context => {
            const node = await context.create(TestOverrideFrozenChainingCallback, {
                code: 'A1',
                strFromOverrideNotFrozen: 'strFromOverrideNotFrozen',
                callbackFrozen: 'callbackFrozen',
                valueFrozen: 'valueFrozen',
                notFrozen: 'notFrozen',
            });
            await node.$.save();
            const readResult = await context.read(
                TestOverrideFrozenChainingCallback,
                { code: 'A1' },
                { forUpdate: true },
            );
            assert.strictEqual(await readResult.callbackFrozen, 'callbackFrozen');
            await readResult.$.set({ callbackFrozen: 'SUB-FROZEN', notFrozen: 'Updated text' });
            await readResult.$.save();
            assert.strictEqual(await readResult.callbackFrozen, 'SUB-FROZEN');
            assert.strictEqual(await readResult.notFrozen, 'Updated text');
            const readResultFrozen = await context.read(
                TestOverrideFrozenChainingCallback,
                { code: 'A1' },
                { forUpdate: true },
            );
            await assert.isRejected(
                readResultFrozen.$.set({ callbackFrozen: 'Updated text' }),
                'TestOverrideFrozenChainingCallback.callbackFrozen: cannot set value on frozen property',
            );
        }));

    it('isFrozen depends on callback: can update sub node with non-frozen property (false on base node) then frozen on next update because of sub node', () =>
        Test.withContext(async context => {
            const node = await context.create(TestOverrideFrozenChainingCallback, {
                code: 'A1',
                strFromOverrideNotFrozen: 'strFromOverrideNotFrozen',
                callbackFrozen: 'callbackFrozen',
                valueFrozen: 'valueFrozen',
                notFrozen: 'notFrozen',
            });
            await node.$.save();
            const readResult = await context.read(
                TestOverrideFrozenChainingCallback,
                { code: 'A1' },
                { forUpdate: true },
            );
            assert.strictEqual(await readResult.notFrozen, 'notFrozen');
            await readResult.$.set({ notFrozen: 'NOT-FROZEN' });
            await readResult.$.save();
            assert.strictEqual(await readResult.notFrozen, 'NOT-FROZEN');
            const readResultFrozen = await context.read(
                TestOverrideFrozenChainingCallback,
                { code: 'A1' },
                { forUpdate: true },
            );
            await assert.isRejected(
                readResultFrozen.$.set({ notFrozen: 'Updated text' }),
                'TestOverrideFrozenChainingCallback.notFrozen: cannot set value on frozen property',
            );
        }));
    // Not allowed cases
    it('Cannot create application with invalid isFrozen override', async () => {
        await assert.isRejected(
            createApplicationWithApi({
                nodes: { TestFrozenChainingBase, TestOverrideFrozenChainingCallbackImpossible },
            }),
            'Cannot override isFrozen if already frozen in base class',
        );
    });

    it('Cannot create application with invalid isFrozen override', async () => {
        await assert.isRejected(
            createApplicationWithApi({ nodes: { TestFrozenChainingBase, TestOverrideFrozenChainingValueImpossible } }),
            'Cannot override isFrozen if already frozen in base class',
        );
    });
    after(() => restoreTables());
});

@decorators.subNode<TestOverrideFrozenChainingCallbackImpossible>({
    isPublished: true,
    extends: () => TestFrozenChainingBase,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
})
class TestOverrideFrozenChainingCallbackImpossible extends TestFrozenChainingBase {
    @decorators.stringPropertyOverride<TestOverrideFrozenChainingCallbackImpossible, 'valueFrozen'>({
        async isFrozen() {
            return (await this.callbackFrozen) === 'FROZEN';
        },
    })
    override readonly valueFrozen: Promise<string>;
}

@decorators.subNode<TestOverrideFrozenChainingValueImpossible>({
    isPublished: true,
    extends: () => TestFrozenChainingBase,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
})
class TestOverrideFrozenChainingValueImpossible extends TestFrozenChainingBase {
    @decorators.stringPropertyOverride<TestOverrideFrozenChainingValueImpossible, 'valueFrozen'>({
        isFrozen: false,
    })
    override readonly valueFrozen: Promise<string>;
}
