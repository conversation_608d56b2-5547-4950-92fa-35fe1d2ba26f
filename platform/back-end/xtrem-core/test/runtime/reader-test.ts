import { assert, expect } from 'chai';
import * as sinon from 'sinon';
import { testDatatypesApplication } from '..';
import { Logger, Test } from '../../index';
import * as fixtures from '../fixtures';
import { datatypesData, initTables, restoreTables, setup } from '../fixtures/index';

const sandbox = sinon.createSandbox();

describe('reader', () => {
    const loggerInternalsOld = {} as {
        _logAsJson?: boolean;
        isDisabled?: boolean;
    };
    function getLogTemplateSpy() {
        (Logger as any)._logAsJson = true;
        (Logger as any).isDisabled = false;
        return sandbox.spy((Logger as any).consoleFormatter, 'template');
    }

    before(async () => {
        await setup({ application: await testDatatypesApplication.application });
        await initTables([{ nodeConstructor: fixtures.nodes.TestDatatypes, data: datatypesData }]);
        loggerInternalsOld._logAsJson = (Logger as any)._logAsJson;
        loggerInternalsOld.isDisabled = (Logger as any).isDisabled;
    });

    afterEach(() => {
        (Logger as any)._logAsJson = loggerInternalsOld._logAsJson;
        (Logger as any).isDisabled = loggerInternalsOld.isDisabled;
        sandbox.restore();
    });

    it('can reduce with a reader', () =>
        Test.readonly(async context => {
            const sumWithReader = await context.queryWithReader(
                fixtures.nodes.TestDatatypes,
                { orderBy: { id: 1 } },
                reader => reader.reduce(async (r, node) => r + (await node.id), 0),
            );
            const sumWithData = datatypesData.reduce((r, data) => r + data.id, 0);
            assert.equal(sumWithReader, sumWithData);
        }));

    it('does not throw error in readOnly context with queryWithReader', () =>
        Test.withReadonlyContext(context => {
            return assert.isFulfilled(
                context.queryWithReader(fixtures.nodes.TestDatatypes, { orderBy: { id: 1 } }, reader =>
                    reader.reduce(async (r, node) => r + (await node.id), 0),
                ),
            );
        }));

    it('throw error with writable context with queryWithReader', () =>
        Test.withCommittedContext(async context => {
            const spy = getLogTemplateSpy();
            await context.queryWithReader(fixtures.nodes.TestDatatypes, { orderBy: { id: 1 } }, reader =>
                reader.reduce(async (r, node) => r + (await node.id), 0),
            );
            const jsonError = spy.returnValues.map(r => JSON.parse(r)).find(r => r.domain === 'xtrem-core/runtime');
            expect(jsonError.message).to.be.equal('cannot use queryWithReader with writable context');
        }));

    it('throw error with writable context with queryWithReader', () =>
        Test.withReadonlyContext(
            async context => {
                const spy = getLogTemplateSpy();
                await context.runInWritableContext(writableContext => {
                    return writableContext.queryWithReader(
                        fixtures.nodes.TestDatatypes,
                        { orderBy: { id: 1 } },
                        reader => reader.reduce(async (r, node) => r + (await node.id), 0),
                    );
                });
                const jsonError = spy.returnValues.map(r => JSON.parse(r)).find(r => r.domain === 'xtrem-core/runtime');
                expect(jsonError.message).to.be.equal('cannot use queryWithReader with writable context');
            },
            { source: 'listener' },
        ));
    after(() => restoreTables());
});
