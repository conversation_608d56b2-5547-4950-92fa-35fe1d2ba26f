import { asyncArray } from '@sage/xtrem-async-helper';
import { assert } from 'chai';
import { Collection, decorators, integer, Node, Test } from '../../index';
import { codeDataType, descriptionDataType } from '../fixtures/data-types/data-types';
import { createApplicationWithApi, initTables, restoreTables, setup } from '../fixtures/index';

@decorators.node({
    storage: 'json',
})
export class TestVolatile extends Node {
    @decorators.stringProperty<TestVolatile, 'code'>({
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestVolatile, 'description'>({
        dataType: () => descriptionDataType,
    })
    readonly description: Promise<string>;

    @decorators.collectionProperty<TestVolatile, 'lines'>({
        node: () => TestVolatileLine,
        isVital: true,
    })
    readonly lines: Collection<TestVolatileLine>;
}

@decorators.node({
    storage: 'json',
})
export class TestVolatileLine extends Node {
    @decorators.stringProperty<TestVolatileLine, 'description'>({
        dataType: () => descriptionDataType,
    })
    readonly description: Promise<string>;

    @decorators.integerProperty<TestVolatileLine, 'lineNumber'>({})
    readonly lineNumber: Promise<integer>;

    // TODO: handle parent reference
    /* @decorators.referenceProperty<FooBar, 'document'>({
         node: () => TestVolatile,
     })
     document TestVolatile;*/
}

describe('volatile collection', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi({
                nodes: { TestVolatile, TestVolatileLine },
            }),
        });
        await initTables([]);
    });

    after(() => restoreTables());

    it('create document with lines', () =>
        Test.uncommitted(async context => {
            const document = await context.create(TestVolatile, {
                code: 'DOCZ',
                description: 'document Z',
                lines: [
                    {
                        lineNumber: 1,
                        description: 'line Z1',
                    },
                    {
                        lineNumber: 2,
                        description: 'line Z2',
                    },
                ],
            });
            assert.instanceOf(document, TestVolatile);
            assert.equal(await document.code, 'DOCZ');
            assert.equal(await document.description, 'document Z');
            const lines = await document.lines.toArray();
            assert.equal(lines.length, 2);
            await asyncArray(lines).forEach(async (line, i) => {
                assert.equal(await line.lineNumber, i + 1);
                assert.equal(await line.description, `line Z${i + 1}`);
            });
        }));
    it('create collection with insert, append and delete', () =>
        Test.uncommitted(async context => {
            const document = await context.create(TestVolatile, {
                code: 'DOCZ',
                description: 'document Z',
            });
            await document.lines.append({
                lineNumber: 1,
                description: 'line Z1',
            });
            await document.lines.append({
                lineNumber: 3,
                description: 'line Z3',
            });
            await document.lines.insert(1, {
                lineNumber: 5,
                description: 'line Z2a',
            });
            await document.lines.insert(1, {
                lineNumber: 2,
                description: 'line Z2',
            });
            await document.lines.delete(2);

            // now read it with length / item API
            assert.equal(await document.lines.length, 3);
            for (let i = 0; i < 3; i += 1) {
                const line = await document.lines.elementAt(i);
                assert.equal(await line.lineNumber, i + 1);
                assert.equal(await line.description, `line Z${i + 1}`);
            }
        }));
});
