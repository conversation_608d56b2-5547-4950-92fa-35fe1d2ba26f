import { assert } from 'chai';
import { decorators, Node, Reference, Test } from '../../index';
import { codeDataType, localizedDescriptionDataType } from '../fixtures/data-types/data-types';
import { createApplicationWithApi, setup } from '../fixtures/index';

@decorators.node<TestMetaDataDelegatedTo>({
    isPublished: true,
    storage: 'sql',
    isContentAddressable: true,
})
class TestMetaDataDelegatedTo extends Node {
    @decorators.stringProperty<TestMetaDataDelegatedTo, 'text'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        isClearedByReset: true,
        isNotEmpty: true,
        isFrozen: true,
        dataType: () => codeDataType,
    })
    readonly text: Promise<string>;

    @decorators.stringProperty<TestMetaDataDelegatedTo, 'localizedText'>({
        isStored: true,
        isRequired: false,
        isClearedByReset: false,
        isNotEmpty: false,
        isFrozen: false,
        dataType: () => localizedDescriptionDataType,
    })
    readonly localizedText: Promise<string>;

    @decorators.stringProperty<TestMetaDataDelegatedTo, 'inputOnlyProperty'>({
        isTransientInput: true,
        isPublished: true,
        dataType: () => codeDataType,
    })
    readonly inputOnlyProperty: Promise<string>;

    @decorators.stringProperty<TestMetaDataDelegatedTo, 'outputOnlyString'>({
        isPublished: true,
        isStoredOutput: true,
        dataType: () => codeDataType,
        updatedValue: () => Promise.resolve('output only string'),
    })
    readonly outputOnlyString: Promise<string>;

    @decorators.booleanProperty<TestMetaDataDelegatedTo, 'bool'>({
        isStored: true,
        isNullable: true,
    })
    readonly bool: Promise<boolean>;

    @decorators.integerProperty<TestMetaDataDelegatedTo, 'requiredNumber'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        isNotZero: true,
        isNullable: false,
    })
    readonly requiredNumber: Promise<number>;

    @decorators.integerProperty<TestMetaDataDelegatedTo, 'number'>({
        isStored: true,
        isRequired: false,
        isNotZero: false,
    })
    readonly number: Promise<number>;
}

@decorators.node<TestMetadataDelegatesTo>({
    isPublished: true,
    storage: 'sql',
    indexes: [{ orderBy: { code: 1 }, isUnique: true, isNaturalKey: true }],
    isSetupNode: true,
})
export class TestMetadataDelegatesTo extends Node {
    @decorators.stringProperty<TestMetadataDelegatesTo, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestMetadataDelegatesTo, 'delegate'>({
        isStored: true,
        isMutable: true,
        node: () => TestMetaDataDelegatedTo,
    })
    readonly delegate: Reference<TestMetaDataDelegatedTo>;

    @decorators.stringProperty<TestMetadataDelegatesTo, 'text'>({
        delegatesTo: { delegate: 'text' },
    })
    readonly text: Promise<string>;

    @decorators.booleanProperty<TestMetadataDelegatesTo, 'bool'>({
        delegatesTo: { delegate: 'bool' },
    })
    readonly bool: Promise<boolean>;

    @decorators.stringProperty<TestMetadataDelegatesTo, 'localizedText'>({
        delegatesTo: { delegate: 'localizedText' },
    })
    readonly localizedText: Promise<string>;

    @decorators.stringProperty<TestMetadataDelegatesTo, 'inputOnlyProperty'>({
        delegatesTo: { delegate: 'inputOnlyProperty' },
    })
    readonly inputOnlyProperty: Promise<string>;

    @decorators.stringProperty<TestMetadataDelegatesTo, 'outputOnlyString'>({
        delegatesTo: { delegate: 'outputOnlyString' },
    })
    readonly outputOnlyString: Promise<string>;

    @decorators.integerProperty<TestMetadataDelegatesTo, 'requiredNumber'>({
        delegatesTo: { delegate: 'requiredNumber' },
    })
    readonly requiredNumber: Promise<number>;

    @decorators.integerProperty<TestMetadataDelegatesTo, 'number'>({
        delegatesTo: { delegate: 'number' },
    })
    readonly number: Promise<number>;
}

describe('DelegatesTo decorator attribute', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi({
                nodes: {
                    TestMetaDataDelegatedTo,
                    TestMetadataDelegatesTo,
                },
            }),
        });
    });

    it('check isXXXX value on TestMetadataDelegatesTo node', () => {
        const factory = Test.application.getFactoryByConstructor(TestMetadataDelegatesTo);
        factory.properties.forEach(prop => {
            switch (prop.name) {
                case 'text':
                    assert.equal(prop.isRequired, true, `${prop.name} isRequired is not true`);
                    assert.equal(prop.isClearedByReset, true, `${prop.name} isClearedByReset is not true`);
                    assert.equal(prop.isNotEmpty, true, `${prop.name} isNotEmpty is not true`);
                    assert.equal(prop.isFrozen, true, `${prop.name} isFrozen is not true`);
                    assert.equal(prop.isPublished, true, `${prop.name} isPublished is not true`);
                    assert.equal(prop.isLocalized, false, `${prop.name} isLocalized is not false`);
                    break;
                case 'localizedText':
                    assert.equal(prop.isRequired, false, `${prop.name} isRequired is not false`);
                    assert.equal(prop.isClearedByReset, false, `${prop.name} isClearedByReset is not false`);
                    assert.equal(prop.isNotEmpty, false, `${prop.name} isNotEmpty is not true`);
                    assert.equal(prop.isFrozen, false, `${prop.name} isFrozen is not false`);
                    assert.equal(prop.isPublished, false, `${prop.name} isPublished is not false`);
                    assert.equal(prop.isLocalized, true, `${prop.name} isLocalized is not true`);
                    break;
                case 'bool':
                    assert.equal(prop.isNullable, true, `${prop.name} isNullable is not true`);
                    assert.equal(prop.isPublished, false, `${prop.name} isPublished is not false`);
                    break;
                case 'requiredNumber':
                    assert.equal(prop.isRequired, true, `${prop.name} isRequired is not true`);
                    assert.equal(prop.isNotZero, true, `${prop.name} isNotZero is not true`);
                    assert.equal(prop.isPublished, true, `${prop.name} isPublished is not true`);
                    assert.equal(prop.isNullable, false, `${prop.name} isNullable is not false`);
                    break;
                case 'number':
                    assert.equal(prop.isRequired, false, `${prop.name} isRequired is not false`);
                    assert.equal(prop.isNotZero, false, `${prop.name} isNotZero is not false`);
                    assert.equal(prop.isPublished, false, `${prop.name} isPublished is not false`);
                    break;
                case 'inputOnlyProperty':
                    assert.equal(prop.isTransientInput, true, `${prop.name} isTransientInput is not true`);
                    assert.equal(prop.isOnInputType, true, `${prop.name} isOnInputType is not true`);
                    assert.equal(prop.isInputOnly, true, `${prop.name} isInputOnly is not true`);
                    break;
                case 'outputOnlyProperty':
                    assert.equal(prop.isStoredOutput, true, `${prop.name} isStoredOutput is not true`);
                    assert.equal(prop.isStoredOutput, true, `${prop.name} isStoredOutput is not true`);
                    assert.equal(prop.isOutputOnly, true, `${prop.name} isOutputOnly is not true`);
                    break;
                default:
                    break;
            }
        });
    });
});
