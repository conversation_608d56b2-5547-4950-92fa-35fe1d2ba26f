import { assert } from 'chai';
import { decorators, Node, Test } from '../../index';
import { descriptionDataType } from '../fixtures/data-types/data-types';
import { createApplicationWithApi, initTables, setup } from '../fixtures/index';

@decorators.node<BasicTest>({
    storage: 'json',
})
export class BasicTest extends Node {
    @decorators.stringProperty<BasicTest, 's1'>({
        dataType: () => descriptionDataType,
        // defaultValue may be an expression
        defaultValue: 'abc',
    })
    readonly s1: Promise<string>;

    @decorators.stringProperty<BasicTest, 's2'>({
        dataType: () => descriptionDataType,
        // defaultValue may also be a function
        defaultValue() {
            return this.s1;
        },
    })
    readonly s2: Promise<string>;

    @decorators.stringProperty<BasicTest, 's3'>({
        dataType: () => descriptionDataType,
        // prepareValue can be used to modify a value before it will be controlled.
        async prepare(): Promise<void> {
            await this.$.set({ s3: ((await this.s3) || (await this.s1)).toUpperCase() });
        },
    })
    readonly s3: Promise<string>;

    @decorators.stringProperty<BasicTest, 's4'>({
        dataType: () => descriptionDataType,
        // defaultValue and prepareValue can both be present
        // defaultValue is executed first in this case
        defaultValue() {
            return this.s1;
        },
        async prepare(): Promise<void> {
            await this.$.set({ s4: (await this.s4).toUpperCase() });
        },
    })
    readonly s4: Promise<string>;
}

describe('testing default values and prepare', () => {
    before(async () => {
        await setup({ application: await createApplicationWithApi({ nodes: { BasicTest } }) });
        await initTables([]);
    });
    it('prepare from empty state', () =>
        Test.uncommitted(async context => {
            const nnode = await context.create(BasicTest, {});
            assert.ok(await nnode.$.control());
            assert.equal(await nnode.s1, 'abc');
            assert.equal(await nnode.s2, 'abc');
            assert.equal(await nnode.s3, 'ABC');
            assert.equal(await nnode.s4, 'ABC');
        }));
    it('defaultValue does not overwrite', () =>
        Test.uncommitted(async context => {
            const nnode = await context.create(BasicTest, {
                s1: 'def',
                s2: '',
                s3: 'ghi',
                s4: 'jkl',
            });
            assert.ok(await nnode.$.control());
            assert.equal(await nnode.s1, 'def');
            assert.equal(await nnode.s2, '');
            assert.equal(await nnode.s3, 'GHI');
            assert.equal(await nnode.s4, 'JKL');
        }));
});

@decorators.node<StopTest>({
    storage: 'json',
})
export class StopTest extends Node {
    @decorators.stringProperty<StopTest, 's1'>({
        dataType: () => descriptionDataType,
        defaultValue(): string {
            return 'abc';
        },
        async prepare(cx): Promise<void> {
            await cx.throw.if(await this.s1).is.equal.to('throw');
            if (await cx.error.if(await this.s1).is.equal.to('error')) return;
            await this.$.set({ s1: (await this.s1).toUpperCase() });
        },
    })
    readonly s1: Promise<string>;

    @decorators.stringProperty<StopTest, 's2'>({
        dataType: () => descriptionDataType,
        // ...{ isNullable: true },
        async defaultValue(): Promise<string> {
            return `${await this.s1}!!!`;
        },
    })
    readonly s2: Promise<string>;
}

describe('testing throw', () => {
    before(async () => {
        await setup({ application: await createApplicationWithApi({ nodes: { StopTest } }) });
        await initTables([]);
    });
    it('prepare without throw', () =>
        Test.uncommitted(async context => {
            const nnode = await context.create(StopTest, {});
            assert.ok(await nnode.$.control());
            assert.equal(await nnode.s1, 'ABC');
            assert.equal(await nnode.s2, 'abc!!!');
        }));
    it('error only stops prepare pass', () =>
        Test.uncommitted(async context => {
            const nnode = await context.create(StopTest, {
                s1: 'error',
            });
            assert.notOk(await nnode.$.control());
            assert.equal(await nnode.s1, 'error');
            assert.equal(await nnode.s2, 'error!!!');
        }));
});
