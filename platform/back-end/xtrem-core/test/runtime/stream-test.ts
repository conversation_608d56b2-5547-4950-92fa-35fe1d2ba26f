import { asyncArray } from '@sage/xtrem-async-helper';
import { assert } from 'chai';
import * as fs from 'fs';
import { JSDOM } from 'jsdom';
import * as fsp from 'path';
import { testDatatypesApplication } from '..';
import { BinaryStream, restoreTables, Test, TextStream } from '../../index';
import { Context } from '../../lib/runtime';
import { Stream } from '../../lib/types/stream';
import * as fixtures from '../fixtures';
import { htmlMailTemplate, reportTemplates } from '../fixtures/data/text-stream';
import { datatypesData, initTables, restoreConfigManager, setup } from '../fixtures/index';

interface HtmlTestEntryOptions {
    propertyName?: string;
    isRawData?: boolean;
    throw?: boolean;
}

interface HtmlTestEntry {
    html: string;
    sanitized: string;
    options?: HtmlTestEntryOptions;
}

function safeHtmlToCompare(html: string): string {
    const blockRegex = /^\{\{(?:[#]\w+|(?:[/]\w+|else)\}\})/;
    const mustacheRegEx = /\{\{[^}{]+?\}\}/gm;
    const transform = (chunk: string): string => {
        if (blockRegex.test(chunk)) {
            return `<!-- ${chunk} -->`;
        }
        return chunk;
    };
    return new JSDOM(html.replace(mustacheRegEx, transform)).serialize();
}

const collectFacturxFiles = (dir: string, collector: string[]) => {
    if (fs.existsSync(dir)) {
        fs.readdirSync(dir).forEach(f => {
            const fullName = fsp.join(dir, f);
            const stat = fs.statSync(fullName);
            if (stat.isDirectory()) {
                collectFacturxFiles(fullName, collector);
            } else if (f.endsWith('.xml')) {
                collector.push(fullName);
            }
        });
    }
};

class HtmlTest {
    private testEntries: HtmlTestEntry[] = [];

    private static baseOffset = 3000;

    private static maxEntries = 100;

    readonly rowOffset: number;

    constructor() {
        this.rowOffset = HtmlTest.baseOffset;
        HtmlTest.baseOffset += HtmlTest.maxEntries;
    }

    addSafe(html: string, sanitized: string, options?: HtmlTestEntryOptions): HtmlTest {
        if (this.testEntries.length >= 200) throw new Error('Max html test entries reached for a single instance.');
        this.testEntries.push({ html, sanitized, options });
        return this;
    }

    addUnsafe(html: string, sanitized: string, options?: HtmlTestEntryOptions): HtmlTest {
        if (this.testEntries.length >= 200) throw new Error('Max html test entries reached for a single instance.');
        this.testEntries.push({ html, sanitized, options: { ...options, throw: true } });
        return this;
    }

    private async saveAll(context: Context): Promise<void> {
        const { rowOffset, testEntries } = this;
        // following change to use the workers pool, the first save might take more time
        const maxCreateMillis = 4000;
        const create = async (t: HtmlTestEntry, i: number): Promise<number> => {
            const t0 = Date.now();
            const node = await context.create(fixtures.nodes.TestDatatypes, {
                ...datatypesData[i],
                _id: rowOffset + i,
                id: rowOffset + i,
                [t.options?.propertyName || 'textStream']: TextStream.fromString(t.html),
            });
            await node.$.save();
            return Date.now() - t0;
        };
        await asyncArray(testEntries).forEach(async (t, i) => {
            if (t.options?.throw) {
                await assert.isRejected(create(t, i), 'html content violate xss validation rules');
            } else {
                const elapsed = await create(t, i);
                if (elapsed >= maxCreateMillis) {
                    assert.fail(`create exceeds ${maxCreateMillis}ms (${elapsed}ms): ${t.html.slice(0, 30)}`);
                }
            }
        });
    }

    private async checkAll(context: Context): Promise<void> {
        const { rowOffset, testEntries } = this;
        await asyncArray(testEntries).forEach(async (t, i) => {
            if (t.options?.throw) return;
            const savedHtml =
                (
                    await ((await context.read(fixtures.nodes.TestDatatypes, { id: rowOffset + i })) as any)[
                        t.options?.propertyName || 'textStream'
                    ]
                )?.value || '';
            assert.equal(
                t.options?.isRawData ? savedHtml : safeHtmlToCompare(savedHtml),
                t.options?.isRawData ? t.sanitized : safeHtmlToCompare(t.sanitized),
            );
        });
    }

    async start(): Promise<void> {
        await Test.withCommittedContext(context => this.saveAll(context));
        await Test.withCommittedContext(context => this.checkAll(context));
    }
}

async function testXmlAttacks(attacks: string[], rowOffset: number) {
    const maxValidationMillis = 200;

    await Test.withContext(context =>
        asyncArray(attacks).forEach(async (attack: string, index: number) => {
            const node = await context.create(fixtures.nodes.TestDatatypes, {
                ...datatypesData[index],
                _id: rowOffset + index,
                id: rowOffset + index,
                textStream: TextStream.fromString(attack),
            });
            const t0 = Date.now();
            await assert.isRejected(node.$.save(), /was not created/); // values are equal then
            const elapsed = Date.now() - t0;
            if (elapsed > maxValidationMillis) {
                assert.fail(`validation exceeds ${maxValidationMillis}ms (${elapsed}ms): ${attack.slice(0, 30)}`);
            }
        }),
    );
}

describe('Stream datatypes tests', () => {
    before(async () => {
        await setup({ application: await testDatatypesApplication.application });
        await initTables([{ nodeConstructor: fixtures.nodes.TestDatatypes, data: datatypesData }]);
    });

    it('can verify an object is a stream and the correct stream type', () =>
        Test.withContext(async context => {
            const data = datatypesData;
            const nodes = await context.query(fixtures.nodes.TestDatatypes).toArray();
            assert.equal(nodes.length, data.length, 'result length matches');
            await asyncArray(nodes).forEach(async (node, i) => {
                if (i !== 0) {
                    // eslint-disable-next-line no-extra-boolean-cast
                    assert(!!!((await node.stringVal) as any)?.isStream);
                    assert(Stream.isStream(await node.binaryStream));
                    assert(BinaryStream.isBinaryStream(await node.binaryStream));
                    assert(!BinaryStream.isBinaryStream(await node.textStream));
                    assert(Stream.isStream(await node.textStream));
                    assert(TextStream.isTextStream(await node.textStream));
                    assert(!TextStream.isTextStream(await node.binaryStream));
                }
            });
        }));

    it('can compare', () =>
        Test.withContext(async context => {
            const data = datatypesData;
            const nodes = await context.query(fixtures.nodes.TestDatatypes).toArray();
            assert.equal(nodes.length, data.length, 'result length matches');
            await asyncArray(nodes).forEach(async (node, i) => {
                if (i !== 0) {
                    assert.equal((await node.textStream)?.compareTo(null), 1); //  value and null so +1
                    assert.equal((await node.textStream)?.compareTo(TextStream.fromString(`textStream${i}`)), 0); // values are equal then
                    assert.equal((await node.textStream)?.compareTo(TextStream.fromString('textStream999')), -1); // value1 < value2 so -1
                    assert.equal((await node.textStream)?.compareTo(TextStream.fromString('textStream0')), 1); // value1 > value2 so 1

                    assert.equal((await node.binaryStream)?.compareTo(null), 1); //  value and null so +1
                    assert.equal(
                        (await node.binaryStream)?.compareTo(BinaryStream.fromBuffer(Buffer.from([i, i + 200, i % 5]))),
                        0,
                    ); // values are equal then
                    assert.equal(
                        (await node.binaryStream)?.compareTo(
                            BinaryStream.fromBuffer(Buffer.from([i + 1, i + 201, (i + 1) % 5])),
                        ),
                        -1,
                    ); // value1 < value2 so -1
                    assert.equal(
                        (await node.binaryStream)?.compareTo(
                            BinaryStream.fromBuffer(Buffer.from([i - 1, i + 199, (i - 1) % 5])),
                        ),
                        1,
                    ); // value1 > value2 so 1
                }
            });
        }));

    it('can get raw value', () =>
        Test.withContext(async context => {
            const data = datatypesData;
            const nodes = await context.query(fixtures.nodes.TestDatatypes).toArray();
            assert.equal(nodes.length, data.length, 'result length matches');
            await asyncArray(nodes).forEach(async (node, i) => {
                if (i !== 0) {
                    assert.equal((await node.textStream)?.toString(), `textStream${i}`);
                    assert.equal(
                        (await node.binaryStream)?.toString(),
                        Buffer.from([i, i + 200, i % 5]).toString('base64'),
                    );
                }
            });
        }));

    it('will throw if invalid arg is passed to compare', () =>
        Test.withContext(async context => {
            const node = await context.read(fixtures.nodes.TestDatatypes, { _id: 2 });
            const textStream = await node.textStream;
            assert.throws(() => textStream.compareTo('foo'), 'invalid arg passed to TextStream compare: foo'); // values are equal then
            const binaryStream = await node.binaryStream;
            assert.throws(() => binaryStream?.compareTo('bar'), 'invalid arg passed to BinaryStream compare: bar'); // values are equal then
        }));

    it('can prevent XXE attacks', async () => {
        const rowOffset = 2000;
        // https://owasp.org/www-project-top-ten/2017/A4_2017-XML_External_Entities_(XXE)
        const attacks = [
            '<?xml version="1.0" encoding="ISO-8859-1"?><!DOCTYPE d [<!ENTITY e SYSTEM "file:///etc/passwd">]><t>&e;</t>', // The attacker attempts to extract data from the server:
            '<?xml version="1.0" encoding="ISO-8859-1"?><!DOCTYPE d [<!ENTITY e SYSTEM "https://192.168.1.1/private" >]><t>&e;</t>', // An attacker probes the server’s private network by changing the above ENTITY line to
            '<?xml version="1.0" encoding="ISO-8859-1"?><!DOCTYPE d [<!ENTITY e SYSTEM "file:///dev/random">]><t>&e;</t>', // An attacker attempts a denial-of-service attack by including a potentially endless file
            '<!DOCTYPE d [<!ENTITY e SYSTEM "file:///dev/random">]><t>&e;</t>', // An attacker attempts a denial-of-service attack by including a potentially endless file
        ];
        await testXmlAttacks(attacks, rowOffset);
    });

    it('can prevent ReDoS attacks', async () => {
        const rowOffset = 3000;
        const attacks = [
            `<?xml version="1.0" encoding="ISO-8859-1"?><!ENTITY\t${'\t'.repeat(54773)}<!ENTITY\t\tN\tSYSTEM`,
            `<?xml version="1.0" encoding="ISO-8859-1"?><!ENTITY %\t${'\t'.repeat(54773)}<!ENTITY\t\tN\tSYSTEM`,
            `<?xml version="1.0" encoding="ISO-8859-1"?><!ENTITY\t${'\t'.repeat(54773)}<!ENTITY\t\t%\tN\tSYSTEM`,
            `<?xml version="1.0" encoding="ISO-8859-1"?><!ENTITY %\t${'\t'.repeat(54773)}<!ENTITY\t\t%\tN\tSYSTEM`,
        ];
        await testXmlAttacks(attacks, rowOffset);
    });

    it('can store valid xml', async () => {
        const rowOffset = 1000;
        const validXml = [
            '<?xml version="1.0" encoding="ISO-8859-1"?><t>hello</t>',
            '<?xml version="1.0" standalone="yes" ?><foo>Hello World.</foo>',
        ];

        await Test.withContext(context =>
            asyncArray(validXml).forEach(async (xml: string, index: number) => {
                const node = await context.create(fixtures.nodes.TestDatatypes, {
                    ...datatypesData[index],
                    _id: rowOffset + index,
                    id: rowOffset + index,
                    textStream: TextStream.fromString(xml),
                });
                /* does not throw */ await (() => node.$.save())(); // values are equal then
                assert.strictEqual(await (await node.textStream).contentType, 'application/xml');
            }),
        );
    });

    it('can store factur-x xml files', async () => {
        const rowOffset = 1500;
        const facturxFiles = [] as string[];
        collectFacturxFiles(fsp.join(__dirname, '..', 'fixtures', 'data', 'factur-x'), facturxFiles);
        await Test.withContext(context =>
            asyncArray(facturxFiles).forEach(async (file: string, index: number) => {
                const xml = fs.readFileSync(file, 'utf8');
                const node = await context.create(fixtures.nodes.TestDatatypes, {
                    ...datatypesData[index],
                    _id: rowOffset + index,
                    id: rowOffset + index,
                    textStream: TextStream.fromString(xml),
                });
                /* does not throw */ await (() => node.$.save())(); // values are equal then
                const textStream = await node.textStream;
                assert.strictEqual(await textStream.contentType, 'application/xml');
                assert.strictEqual(textStream.value.toString(), xml);
            }),
        );
    });

    it('cannot save xml with DTD', async () => {
        const rowOffset = 2500;
        // https://owasp.org/www-project-top-ten/2017/A4_2017-XML_External_Entities_(XXE)
        // https://cheatsheetseries.owasp.org/cheatsheets/XML_External_Entity_Prevention_Cheat_Sheet.html
        const safeXmls = [
            `<?xml version="1.0" standalone="yes" ?>
            <!--open the DOCTYPE declaration -
              the open square bracket indicates an internal DTD-->
            <!DOCTYPE foo [
            <!--define the internal DTD-->
              <!ELEMENT foo (#PCDATA)>
            <!--close the DOCTYPE declaration-->
            ]>
            <foo>Hello World.</foo>`,
        ];

        await Test.withContext(context =>
            asyncArray(safeXmls).forEach(async (xml, index) => {
                const node = await context.create(fixtures.nodes.TestDatatypes, {
                    ...datatypesData[index],
                    _id: rowOffset + index,
                    id: rowOffset + index,
                    textStream: TextStream.fromString(xml, 'application/xml'),
                });
                await assert.isRejected(node.$.save(), /was not created/); // values are equal then
            }),
        );

        const safeHtmlLike = ['<foo>Hello World.</foo>'];

        await Test.withContext(context =>
            asyncArray(safeHtmlLike).forEach(async (xml, index) => {
                const node = await context.create(fixtures.nodes.TestDatatypes, {
                    ...datatypesData[index],
                    _id: rowOffset + index,
                    id: rowOffset + index,
                    textStream: TextStream.fromString(xml, 'text/html'),
                });
                /* does not throw */ await (() => node.$.save())();
                assert.strictEqual(await (await node.textStream).contentType, 'text/html');
            }),
        );
    });

    // OK dompurify with throw
    it('can sanitize basic html', () =>
        new HtmlTest()
            .addUnsafe(
                "<html><strong>hello world</strong><script>alert('hello world')</script></html>",
                '<html><strong>hello world</strong></html>', // escape script tag
            )
            .addUnsafe(
                '<html><a onclick="alert(\'Hello, this is my old-fashioned event handler!\');">Press me</a></html>',
                '<html><a>Press me</a></html>', // sanitize event
            )
            .addUnsafe(
                '<html><img src="x" onerror=alert("img")></html>',
                '<html><img src="x" /></html>', // sanitize event
            )
            .addSafe(
                '<html><a href="https://www.website.com/">Visit website.com!</a></html>',
                '<html><a href="https://www.website.com/">Visit website.com!</a></html>', // keep safe href
            )
            .addSafe('<html><div id="main"><p>hello</p></div></html>', '<html><div id="main"><p>hello</p></div></html>')
            .start());

    it('can save css without affecting content', async () => {
        const css = `
            @import '~@sage/xtrem-ui-components/build/xtrem-ui-components.css';
            .ck .ck-placeholder,
            .ck.ck-placeholder {
                position: relative;
            }

            .ck .ck-placeholder:before,
            .ck.ck-placeholder:before {
                content: attr(data-placeholder);
                left: 0;
                pointer-events: none;
                position: absolute;
                right: 0;
            }

            .ck.ck-read-only .ck-placeholder:before {
                display: none;
            }

            .ck.ck-labeled-field-view > .ck.ck-labeled-field-view__input-wrapper {
                display: flex;
                position: relative;
                width: 100%;
            }

            .ck-content .image.image_resized {
                max-width: 100%;
                display: block;
                box-sizing: border-box;
            }

            .ck-content .image.image_resized img {
                /* For resized images it is the '<figure>' element that determines the image width. */
                width: 100%;
            }
            .ck-content .image.image_resized > figcaption {
                /* The '<figure>' element uses 'display:block', so '<figcaption>' also has to. */
                display: block;
            }
    `;

        await new HtmlTest().addSafe(css, css, { isRawData: true }).start();
    });

    // OK dompurify with throw
    it('can sanitize basic html body', () =>
        new HtmlTest()
            .addUnsafe(
                "<strong>hello world</strong><script>alert('hello world')</script>",
                '<strong>hello world</strong>', // should throw script tag
            )
            .addUnsafe(
                `<!-- with single line comment -->
                <strong>hello world</strong><script>alert('hello world')</script>`,
                '<strong>hello world</strong>',
            )
            .addUnsafe(
                `<!--
                    with
                    multi line
                    comment
                 -->
                <strong>hello world</strong><script>alert('hello world')</script>`,
                '<strong>hello world</strong>',
            )
            .addUnsafe(
                '<a onclick="alert(\'Hello, this is my old-fashioned event handler!\');">Press me</a>',
                '<a>Press me</a>', // sanitize event
            )
            .addUnsafe(
                '<img src=x onerror=alert("img")>',
                '<img src="x">', // sanitize event
            )
            .addUnsafe(
                '<img src="./x" onerror=alert("img")>',
                '<img src="./x">', // sanitize event
            )
            .addSafe(
                '<a href="https://www.website.com/">Visit website.com!</a>',
                '<a href="https://www.website.com/">Visit website.com!</a>', // keep safe href
            )
            .addSafe('<div id="main"><p>hello</p></div>', '<div id="main"><p>hello</p></div>')
            // do not allow iframes this can display malicious content or disclose sensitive information like the server's IP address
            .addUnsafe(
                '<div><iframe src="https://www.w3schools.com" title="W3Schools Free Online Web Tutorials"></iframe></div>',
                '<div></div>',
            )
            .start());

    // OK dompurify with throw
    it('can sanitize OWASP XSS filter evasion', () =>
        new HtmlTest()
            // OWASP XSS filter evasion: https://owasp.org/www-community/xss-filter-evasion-cheatsheet
            .addUnsafe(
                '<head><META HTTP-EQUIV="refresh" CONTENT="0;url=javascript:alert(\'XSS\');"></head>',
                '<head><META HTTP-EQUIV="refresh" CONTENT="0"></head>',
            )
            .addSafe(
                '<head><META HTTP-EQUIV="refresh" CONTENT="0;url=/x"></head>',
                '<head><META HTTP-EQUIV="refresh" CONTENT="0;url=/x"></head>',
            )
            .start());

    // OK dompurify with throw
    it('can sanitize DOM clobbering attacks', () =>
        new HtmlTest()
            .addUnsafe('<div id="body">DOM clobbering attacks</div>', '<div>DOM clobbering attacks</div>')
            .start());

    // OK dompurify with throw
    it('can sanitize DomPurify samples', () =>
        new HtmlTest()
            .addUnsafe('<img src=x onerror=alert(1)//>', '<img src="x">')
            // iframe syntax is bad or malicious so dompurify is removing all
            .addUnsafe('<p>abc<iframe//src=jAva&Tab;script:alert(3)>def</p>', '<p>abc</p>')
            // iframe syntax is bad or malicious so dompurify is removing all
            .addUnsafe('<p>abc<iframe src=jAva&Tab;script:alert(3)>def</p>', '<p>abc</p>')
            .addUnsafe('<p>abc<iframe src=jAva&Tab;script:alert(3)></iframe>def</p>', '<p>abc<iframe></iframe>def</p>')
            .addUnsafe(
                '<iframe src="https://giphy.com/embed/3ohzdIuqJoo8QdKlnW" width="480" height="222" frameBorder="0" class="giphy-embed" allowFullScreen></iframe><p><a href="https://giphy.com/gifs/reactionseditor-yes-awesome-3ohzdIuqJoo8QdKlnW">via GIPHY</a></p>',
                '<p><a href="https://giphy.com/gifs/reactionseditor-yes-awesome-3ohzdIuqJoo8QdKlnW">via GIPHY</a></p>',
            )
            .addSafe('<TABLE><tr><td>HELLO</tr></TABL>', '<table><tr><td>HELLO</td></tr></table>')
            .addSafe('<UL><li><A HREF=//google.com>click</UL>', '<ul><li><a href="//google.com">click</ul>')
            // svg alone is not in the whitelist so we embed it into a div
            .addUnsafe('<div><svg><g/onload=alert(2)//<p></div>', '<div><svg><g></g></svg></div>')
            .start());

    // OK dompurify with throw
    it('sanitizing svg should throw an error', async () => {
        // DomPurify svg samples
        // svg alone is not in the whitelist so it is rejected
        await assert.isRejected(
            new HtmlTest().addSafe('<svg><g/onload=alert(2)//<p>', '<svg><g /><p />').start(),
            'html content violate xss validation rules',
        );
    });

    // OK dompurify with throw
    it('can sanitize mathml', () =>
        new HtmlTest()
            .addUnsafe('<math><mi//xlink:href="data:x,<script>alert(4)</script>">', '')
            .addUnsafe('<math><mi href="data:x,<script>alert(4)</script>"></mi></math>', '')
            .addUnsafe('<math><mi> sin </mi></math>', '')
            .start());

    // OK dompurify with throw
    it('can sanitize report templates', async () => {
        const htmlTest = new HtmlTest();
        reportTemplates.forEach(html => htmlTest.addSafe(html, html));
        await htmlTest.start();
    });

    it('can sanitize mail templates with dangerouslyUnsafe option', () =>
        new HtmlTest()
            // Mail templates
            // remove HTML4 or xhtml DOCTYPE info
            .addSafe(htmlMailTemplate, htmlMailTemplate, { propertyName: 'unsafeMailTemplate' })
            .start());

    // OK dompurify with throw
    it('sanitizing mail templates with HTML4 or xhtml should throw an error', async () => {
        await assert.isRejected(
            new HtmlTest()
                // Mail templates
                // remove HTML4 or xhtml DOCTYPE info
                .addSafe(htmlMailTemplate, htmlMailTemplate, { propertyName: 'mailTemplate' })
                .start(),
            'html content violate xss validation rules',
        );
    });

    after(async () => {
        restoreConfigManager();
        await restoreTables();
    });
});
