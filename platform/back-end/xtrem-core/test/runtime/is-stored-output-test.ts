import { assert } from 'chai';
import { decorators, Node, StringDataType, Test } from '../../index';
import { createApplicationWithApi, initTables, restoreTables, setup } from '../fixtures/index';

@decorators.node<TestIsStoredOutputBad>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [],
})
export class TestIsStoredOutputBad extends Node {
    @decorators.stringProperty<TestIsStoredOutputBad, 'outputOnlyString'>({
        dataType: () => new StringDataType({ maxLength: 50 }),
        isPublished: true,
        isStoredOutput: true,
    })
    readonly outputOnlyString: Promise<string>;
}
@decorators.node<TestIsStoredOutputNode>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestIsStoredOutputNode extends Node {
    @decorators.stringProperty<TestIsStoredOutputNode, 'code'>({
        isStored: true,
        isPublished: true,
        defaultValue() {
            return 'code1';
        },
        dataType: () => new StringDataType({ maxLength: 50 }),
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestIsStoredOutputNode, 'isStoredOutputTrue'>({
        isPublished: true,
        isStoredOutput: true,
        dataType: () => new StringDataType({ maxLength: 50 }),
        defaultValue() {
            return 'Default';
        },
        dependsOn: ['code'],
        async updatedValue() {
            return (await this.code).toUpperCase();
        },
    })
    readonly isStoredOutputTrue: Promise<string>;
}

describe('isStoredOutput test', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi({
                nodes: {
                    TestIsStoredOutputNode,
                },
            }),
        });
        await initTables([
            {
                nodeConstructor: TestIsStoredOutputNode,
                data: [],
            },
        ]);
    });

    it.skip('should ignore incoming update value if isStoredOutput is true', () =>
        Test.withUncommittedContext(async context => {
            const initStoredOutput = await context.create(TestIsStoredOutputNode, { _id: 1 });
            await initStoredOutput.$.save();

            const TestIsStoredOutput = await context.read(TestIsStoredOutputNode, { _id: 1 }, { forUpdate: true });

            await TestIsStoredOutput.$.save();

            await assert.isRejected(
                TestIsStoredOutput.$.set({
                    code: 'updatedCode',
                    isStoredOutputTrue: 'ignoreMe!', // should equal 'updatedValue' function result
                }),
                'TestIsStoredOutputNode.isStoredOutputTrue: Cannot set value - property is stored output',
            );
        }));

    it('should throw error if isStoredOutput attribute set to true if updateValue is not provided', async () => {
        await assert.isRejected(
            createApplicationWithApi({ nodes: { TestIsStoredOutputBad } }),
            "If 'isStoredOutput' is set to true an 'updatedValue' must be supplied.",
        );
    });

    after(() => restoreTables());
});
