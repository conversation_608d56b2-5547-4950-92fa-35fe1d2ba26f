import { assert } from 'chai';
import { testBasicDocumentApplication } from '..';
import { Test } from '../../index';
import * as fixtures from '../fixtures';
import { initTables, referredData, referringData, restoreTables, setup } from '../fixtures/index';
import { TestReferred, TestReferring } from '../fixtures/nodes';

describe('can get payload with options', () => {
    before(async () => {
        await setup({ application: await testBasicDocumentApplication.application });
        await initTables([
            { nodeConstructor: TestReferred, data: referredData },
            { nodeConstructor: TestReferring, data: referringData },
        ]);
    });

    it('can get payload with _updateStamp and _createStamp', () =>
        Test.withReadonlyContext(async context => {
            const node = await context.read(fixtures.nodes.TestReferring, { code: referringData[0].code });
            const payload = await node.$.payload({ withTimeStamps: true });
            assert.isTrue(!!payload._createStamp);
            assert.isTrue(!!payload._updateStamp);
        }));

    it('can get payload with natural keys on non-vital nodes', () =>
        Test.withReadonlyContext(async context => {
            const node = await context.read(fixtures.nodes.TestReferring, { code: referringData[0].code });
            const payload = await node.$.payload({ withNaturalKeyWhenThunk: true });
            assert.deepEqual(payload.reference, {
                code: 'REF1',
            });
        }));

    after(() => restoreTables());
});
