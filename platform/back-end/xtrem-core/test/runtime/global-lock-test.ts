import { AsyncResponse } from '@sage/xtrem-async-helper';
import { withClsContext } from '@sage/xtrem-log';
import { assert } from 'chai';
import { promisify } from 'util';
import { initTables, Test } from '../../lib/index';
import { loggers } from '../../lib/runtime/loggers';
import { createApplicationWithApi, setup } from '../fixtures/index';

// GlobalLocks
// read en mode pessimistic

let executionTrace = '';

// run concurrent tasks with fresh context so that re-entrency detection works ok
function spawn(fn: () => AsyncResponse<void>): Promise<void> {
    return withClsContext(fn, {});
}

function runAsExclusive(processId: string, timeToSleep: number): void {
    spawn(() =>
        Test.committed(async context => {
            executionTrace += `${processId}0-`;
            loggers.runtime.info(`process ${processId} : before`);
            await context.runAsExclusive('test', async () => {
                executionTrace += `${processId}1-`;
                loggers.runtime.info(`process ${processId} : in`);
                await new Promise<void>(resolve => {
                    setTimeout(resolve, timeToSleep);
                });
                loggers.runtime.info(`process ${processId} : out`);
                executionTrace += `${processId}2-`;
            });
        }),
    ).catch(err => loggers.runtime.error(err.stack));
}

describe('Global lock', () => {
    before(async () => {
        await setup({ application: await createApplicationWithApi({}) });
        await initTables([]);
    });

    it('concurrent processes', async () => {
        // Run 2 process in parallel (using different fibers)
        setTimeout(() => {
            // Run the 'A' process
            runAsExclusive('A', 3000);
        });
        setTimeout(() => {
            // Wait for 1s and try to run the 'B' process using another fiber (in parallel)
            // We are waiting 200ms just to make sure the 'process B:before' trace will be after the 'process A:in' trace
            runAsExclusive('B', 2000);
        }, 1000);
        await promisify(setTimeout)(8000);
        // The expected sequence is :
        // Process A : before
        // Process A : in
        // Process B : before (waiting for 200ms ensures that it will be after 'Process A : in')
        // Process A : out (process B should still be waiting for the lock)
        // Process B : in
        // Process B : out
        assert.equal(executionTrace, 'A0-A1-B0-A2-B1-B2-');
    });
});
