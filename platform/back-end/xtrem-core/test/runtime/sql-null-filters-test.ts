import { assert } from 'chai';
import { date, datetime, decorators, Node, Reference, Test } from '../../index';
import { codeDataType, descriptionDataType } from '../fixtures/data-types/data-types';
import { createApplicationWithApi, initTables, restoreTables, setup } from '../fixtures/index';

/** DECLARE SOME TYPES */
@decorators.node<TestCustomerNullFilter>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestCustomerNullFilter',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
class TestCustomerNullFilter extends Node {
    @decorators.stringProperty<TestCustomerNullFilter, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestCustomerNullFilter, 'name'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly name: Promise<string>;

    @decorators.dateProperty<TestCustomerNullFilter, 'date'>({
        isPublished: true,
        isNullable: true,
        isStored: true,
    })
    readonly date: Promise<date | null>;

    @decorators.datetimeProperty<TestCustomerNullFilter, 'dateTime'>({
        isPublished: true,
        isNullable: true,
        isStored: true,
    })
    readonly dateTime: Promise<datetime | null>;

    @decorators.referenceProperty<TestCustomerNullFilter, 'address'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        node: () => TestAddressNullFilter,
    })
    readonly address: Reference<TestAddressNullFilter | null>;
}

@decorators.node<TestAddressNullFilter>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestAddressNullFilter',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
class TestAddressNullFilter extends Node {
    @decorators.stringProperty<TestAddressNullFilter, 'code'>({
        dataType: () => codeDataType,
        isStored: true,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestAddressNullFilter, 'text'>({
        dataType: () => descriptionDataType,
        isStored: true,
    })
    readonly text: Promise<string>;
}

/** DECLARE SOME DATA */
const customerData = [
    {
        _id: 1,
        code: 'CUST1',
        date: date.today(),
        dateTime: datetime.now(),
        address: 1,
    },
    {
        _id: 2,
        code: 'CUST2',
        date: null,
        dateTime: null,
    },
    {
        _id: 3,
        code: 'CUST3',
        date: null,
        dateTime: null,
        address: 2,
    },
];

const addressData = [
    {
        _id: 1,
        code: 'ADDR1',
        text: 'Lacanau',
    },
    {
        _id: 2,
        code: 'ADDR2',
        text: 'Le Porge',
    },
];

describe('sql null-filters tests', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi({ nodes: { TestAddressNullFilter, TestCustomerNullFilter } }),
        });
        await initTables([
            { nodeConstructor: TestAddressNullFilter, data: addressData },
            { nodeConstructor: TestCustomerNullFilter, data: customerData },
        ]);
    });
    it('query with null filter : date + _eq', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestCustomerNullFilter, {
                    filter: {
                        date: { _eq: null },
                    },
                })
                .map(r => r.code)
                .toArray();
            assert.equal(result.join('/'), 'CUST2/CUST3');
        }));
    it('query with null filter : date + _ne', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestCustomerNullFilter, {
                    filter: {
                        date: { _ne: null },
                    },
                })
                .toArray();
            assert.equal(result.length, 1);
            assert.equal(await result[0].code, 'CUST1');
        }));
    it('query with null filter : datetime + _eq', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestCustomerNullFilter, {
                    filter: {
                        dateTime: { _eq: null },
                    },
                })
                .map(r => r.code)
                .toArray();
            assert.equal(result.join('/'), 'CUST2/CUST3');
        }));
    it('query with null filter : datetime + _ne', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestCustomerNullFilter, {
                    filter: {
                        dateTime: { _ne: null },
                    },
                })
                .toArray();
            assert.equal(result.length, 1);
            assert.equal(await result[0].code, 'CUST1');
        }));
    it('query with null filter : reference + _eq', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestCustomerNullFilter, {
                    filter: {
                        address: { _eq: null },
                    },
                })
                .toArray();
            assert.equal(result.length, 1);
            assert.equal(await result[0].code, 'CUST2');
        }));
    it('query with null filter : reference + _ne null', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestCustomerNullFilter, {
                    filter: {
                        address: { _ne: null },
                    },
                })
                .map(r => r.code)
                .toArray();
            assert.equal(result.join('/'), 'CUST1/CUST3');
        }));
    it('query with null filter : reference + _ne', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestCustomerNullFilter, {
                    filter: {
                        address: { _ne: 1 },
                    },
                })
                .map(r => r.code)
                .toArray();
            assert.equal(result.join('/'), 'CUST2/CUST3');
        }));
    it('query with null filter : reference + null', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestCustomerNullFilter, {
                    filter: {
                        address: null,
                    },
                })
                .toArray();
            assert.equal(result.length, 1);
            assert.equal(await result[0].code, 'CUST2');
        }));
    it('complex query with null filter : reference + _eq', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestCustomerNullFilter, {
                    filter: {
                        address: {
                            _or: [{ _eq: null }, { _gt: 1 }],
                        },
                    },
                })
                .map(r => r.code)
                .toArray();
            assert.equal(result.join('/'), 'CUST2/CUST3');
        }));
    it('complex query with null filter : _in', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestCustomerNullFilter, {
                    filter: {
                        address: { _in: [null, 2] },
                    },
                })
                .map(r => r.code)
                .toArray();
            assert.equal(result.join('/'), 'CUST2/CUST3');
        }));
    it('complex query with only one null filter : _in', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestCustomerNullFilter, {
                    filter: {
                        address: { _in: [null] },
                    },
                })
                .toArray();
            assert.equal(result.length, 1);
            assert.equal(await result[0].code, 'CUST2');
        }));
    it('complex query with null filter : _nin', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestCustomerNullFilter, {
                    filter: {
                        address: { _nin: [null, 1] },
                    },
                })
                .toArray();
            assert.equal(result.length, 1);
            assert.equal(await result[0].code, 'CUST3');
        }));
    after(() => restoreTables());
});
