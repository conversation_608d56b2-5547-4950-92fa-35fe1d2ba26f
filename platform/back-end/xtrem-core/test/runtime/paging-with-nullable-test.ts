import { DateValue } from '@sage/xtrem-date-time';
import { assert } from 'chai';
import { OrderBy, Test } from '../../index';
import * as fixtures from '../fixtures';
import { testDatatypesApplication } from '../fixtures/applications';
import { initTables, setup } from '../fixtures/index';
import { TestDatatypes } from '../fixtures/nodes';

function getTestData() {
    const data = [];
    for (let i = 0; i < 3; i += 1) {
        for (let j = 0; j < 3; j += 1) {
            const id = 3 * i + j;
            // _id is not nullable
            // shortVal and dateVal are nullable
            const shortVal = j === 0 ? null : j;
            const dateVal = i === 0 ? null : DateValue.today().addDays(i);
            const booleanVal = [null, null, false, true][id % 4];
            data.push({
                id,
                shortVal,
                dateVal,
                booleanVal,
            });
        }
    }
    return data;
}

type Direction = 'forwards' | 'backwards';

function page(direction: Direction, orderBy: OrderBy<TestDatatypes>): Promise<number[]> {
    return Test.withReadonlyContext(async context => {
        let cursor: string | undefined;
        const ids = [] as number[];
        // Loop until 100 to keep lint happy
        for (let iter = 0; iter < 100; iter += 1) {
            // assert to get an error if paging won't terminate (there are only 9 records in the table)
            assert.isBelow(iter, 10);
            const results = await context
                .query(
                    TestDatatypes,
                    direction === 'forwards'
                        ? // use diffent page sizes to exercise a bit more
                          { first: 2, after: cursor, orderBy }
                        : { last: 4, before: cursor, orderBy },
                )
                .toArray();
            if (results.length === 0) break;
            ids.push(...results.map(r => r._id));
            cursor = await results[results.length - 1].$.getCursorValue(orderBy);
        }
        // reverse the ids if we go backwards so that we get the same expected result as in forwards
        return direction === 'forwards' ? ids : ids.reverse();
    });
}

describe('paging with nullable properties', () => {
    before(async () => {
        await setup({ application: await testDatatypesApplication.application });
        await initTables([{ nodeConstructor: fixtures.nodes.TestDatatypes, data: getTestData() }]);
    });

    ['forwards', 'backwards'].forEach((direction: Direction) => {
        it(`can page with nullable properties (${direction})`, async () => {
            assert.deepEqual(await page(direction, { shortVal: 1, id: 1 }), [1, 4, 7, 2, 5, 8, 3, 6, 9]);
            assert.deepEqual(await page(direction, { shortVal: -1, id: -1 }), [1, 4, 7, 2, 5, 8, 3, 6, 9].reverse());
            assert.deepEqual(await page(direction, { shortVal: 1, id: -1 }), [7, 4, 1, 8, 5, 2, 9, 6, 3]);
            assert.deepEqual(await page(direction, { shortVal: -1, id: 1 }), [7, 4, 1, 8, 5, 2, 9, 6, 3].reverse());
            assert.deepEqual(await page(direction, { dateVal: 1, id: 1 }), [1, 2, 3, 4, 5, 6, 7, 8, 9]);
            assert.deepEqual(await page(direction, { dateVal: 1, id: -1 }), [3, 2, 1, 6, 5, 4, 9, 8, 7]);
            assert.deepEqual(await page(direction, { dateVal: 1, shortVal: 1 }), [1, 2, 3, 4, 5, 6, 7, 8, 9]);
            assert.deepEqual(
                await page(direction, { dateVal: -1, shortVal: -1 }),
                [1, 2, 3, 4, 5, 6, 7, 8, 9].reverse(),
            );
            assert.deepEqual(await page(direction, { dateVal: 1, shortVal: -1 }), [3, 2, 1, 6, 5, 4, 9, 8, 7]);
            assert.deepEqual(
                await page(direction, { dateVal: -1, shortVal: 1 }),
                [3, 2, 1, 6, 5, 4, 9, 8, 7].reverse(),
            );
            assert.deepEqual(await page(direction, { booleanVal: 1, id: 1 }), [1, 2, 5, 6, 9, 3, 7, 4, 8]);
            assert.deepEqual(await page(direction, { booleanVal: -1, id: -1 }), [1, 2, 5, 6, 9, 3, 7, 4, 8].reverse());
            assert.deepEqual(await page(direction, { booleanVal: 1, id: -1 }), [9, 6, 5, 2, 1, 7, 3, 8, 4]);
            assert.deepEqual(await page(direction, { booleanVal: -1, id: 1 }), [9, 6, 5, 2, 1, 7, 3, 8, 4].reverse());
        });
    });

    // after(() => restoreTables());
});
