import { AsyncResponse } from '@sage/xtrem-async-helper';
import { withClsContext } from '@sage/xtrem-log';
import { assert } from 'chai';
import { promisify } from 'util';
import { testDatatypesApplication } from '..';
import { Context, Test } from '../../index';
import { StateStatus } from '../../lib/node-state';
import * as fixtures from '../fixtures';
import { datatypesData, initTables, restoreTables, setup } from '../fixtures/index';
import { TestDatatypes } from '../fixtures/nodes';

async function sleep(): Promise<void> {
    await promisify(setTimeout)(500);
}

// run concurrent tasks with fresh context so that re-entrency detection works ok
function spawn(fn: () => Promise<void>): Promise<void> {
    return withClsContext(fn, {});
}

async function pessimisticConcurrencyTest(readFn: (context: Context) => AsyncResponse<TestDatatypes>): Promise<void> {
    let insideCount = 0;
    const task = () =>
        Test.committed(async context => {
            const obj = await readFn(context);
            assert.equal(insideCount, 0);
            insideCount += 1;
            await obj.$.set({ integerVal: (await obj.integerVal) + 1 });
            await sleep();
            insideCount -= 1;
            await obj.$.save();
        });
    const t1 = spawn(task);
    const t2 = spawn(task);
    await t1;
    await t2;
    await Test.readonly(async context => {
        const obj = await context.read(TestDatatypes, { id: 1 });
        assert.equal(await obj.integerVal, datatypesData[1].integerVal + 2);
    });
}

describe('Basic transaction', () => {
    before(async () => setup({ application: await testDatatypesApplication.application }));
    beforeEach(() => initTables([{ nodeConstructor: fixtures.nodes.TestDatatypes, data: datatypesData }]));

    it('commits when body completes', async () => {
        const createId = 50;
        const createVal = -2;
        const deleteId = 2;
        const updateId = 5;
        const updateVal = -5;
        await Test.committed(async context => {
            // insert a new record
            assert.ok(!(await context.exists(TestDatatypes, { id: createId })));
            const created = await context.create(TestDatatypes, { id: createId, integerVal: createVal });
            await created.$.save();
            assert.ok(await context.exists(TestDatatypes, { id: createId }));

            // delete an existing record
            assert.ok(await context.exists(TestDatatypes, { id: deleteId }));
            await context.deleteMany(TestDatatypes, { id: deleteId });
            assert.ok(!(await context.exists(TestDatatypes, { id: deleteId })));

            // update an existing record
            assert.ok(await context.exists(TestDatatypes, { id: updateId }));
            const updated = await context.read(TestDatatypes, { id: updateId }, { forUpdate: true });
            await updated.$.set({ integerVal: updateVal });
            await updated.$.save();
        });
        await Test.readonly(async context => {
            // check that database was correctly updated
            assert.equal(await (await context.read(TestDatatypes, { id: createId })).integerVal, createVal);
            assert.ok(!(await context.exists(TestDatatypes, { id: deleteId })));
            assert.equal(await (await context.read(TestDatatypes, { id: updateId })).integerVal, updateVal);
        });
    });

    it('_id autoIncrement', () =>
        Test.uncommitted(async context => {
            await (await context.create(TestDatatypes, { id: 100, integerVal: 1000 })).$.save();
            await (await context.create(TestDatatypes, { id: 101, integerVal: 1001 })).$.save();

            const instance1 = await context.read(TestDatatypes, { id: 100 });
            const instance2 = await context.read(TestDatatypes, { id: 101 });
            assert.equal(instance2.$.id, instance1.$.id + 1);
        }));

    it('rolls back when body fails', async () => {
        const createId = 51;
        const createVal = -7;
        const deleteId = 3;
        const updateId = 6;
        const updateVal = -9;
        await Test.readonly(async context => {
            assert.ok(!(await context.exists(TestDatatypes, { id: createId })));
            assert.ok(await context.exists(TestDatatypes, { id: deleteId }));
        });

        await assert.isRejected(
            Test.committed(async context => {
                // insert a new record
                assert.ok(!(await context.exists(TestDatatypes, { id: createId })));
                const created = await context.create(TestDatatypes, { id: createId, integerVal: createVal });
                await created.$.save();
                assert.ok(await context.exists(TestDatatypes, { id: createId }));

                // delete an existing record
                assert.ok(await context.exists(TestDatatypes, { id: deleteId }));
                await context.deleteMany(TestDatatypes, { id: deleteId });
                assert.ok(!(await context.exists(TestDatatypes, { id: deleteId })));

                // update an existing record
                assert.ok(await context.exists(TestDatatypes, { id: updateId }));
                const updated = await context.read(TestDatatypes, { id: updateId }, { forUpdate: true });
                await updated.$.set({ integerVal: updateVal });
                await updated.$.save();
                throw new Error('body failed');
            }),
            /body failed/,
        );

        await Test.readonly(async context => {
            assert.ok(!(await context.exists(TestDatatypes, { id: createId })));
            assert.ok(await context.exists(TestDatatypes, { id: deleteId }));
            assert.notEqual(await (await context.read(TestDatatypes, { id: updateId })).integerVal, updateVal);
        });
    });

    it('fails if created objects are not inserted', async () => {
        const createId = 51;
        const createVal = -7;
        await assert.isRejected(
            Test.committed(async context => {
                // insert a new record
                assert.ok(!(await context.exists(TestDatatypes, { id: createId })));
                await context.create(TestDatatypes, { id: createId, integerVal: createVal });
            }),
            /transaction error: modified nodes have not been saved: TestDatatypes:_id:-1/,
        );
    });
    it('fails if modified objects are not updated', async () => {
        const updateId = 6;
        const updateVal = -9;
        await assert.isRejected(
            Test.committed(async context => {
                // update an existing record
                assert.ok(await context.exists(TestDatatypes, { id: updateId }));
                const updated = await context.read(TestDatatypes, { id: updateId }, { forUpdate: true });
                await updated.$.set({ integerVal: updateVal });
            }),
            /transaction error: modified nodes have not been saved: TestDatatypes:id:6, TestDatatypes:_id:7/,
        );
    });
    it('handles concurrent updates correctly (read / pessimistic update mode)', () =>
        pessimisticConcurrencyTest(context =>
            context.read(
                TestDatatypes,
                { id: 1 },
                {
                    forUpdate: true,
                },
            ),
        ));
    it('handles concurrent updates correctly (query / pessimistic update mode)', () =>
        pessimisticConcurrencyTest(
            async context =>
                (
                    await context
                        .query(TestDatatypes, {
                            forUpdate: true,
                            filter: { id: 1 },
                        })
                        .toArray()
                )[0],
        ));

    it('handles special rollback of transaction cache for unit tests', () =>
        Test.withContext(async context => {
            const readonly1 = await context.read(TestDatatypes, { id: 1 });

            const writable1 = await context.read(TestDatatypes, { id: 2 }, { forUpdate: true });

            assert.equal(readonly1.$.state.status, StateStatus.readonly);

            assert.equal(writable1.$.state.status, StateStatus.updatable);

            await Test.rollbackCache(context);

            assert.equal(readonly1.$.state.status, StateStatus.stale);

            assert.equal(writable1.$.state.status, StateStatus.stale);

            const readonly2 = await context.read(TestDatatypes, { id: 1 });

            assert.equal(readonly2.$.state.status, StateStatus.readonly);

            const writable2 = await context.read(TestDatatypes, { id: 2 }, { forUpdate: true });

            assert.equal(writable2.$.state.status, StateStatus.updatable);
        }));

    after(() => restoreTables());
});
