import { AnyValue, AsyncResponse } from '@sage/xtrem-async-helper';
import { assert } from 'chai';
import { Test, UserTestOptions } from '../../index';
import { Context } from '../../lib';
import {
    createApplicationWithApi,
    initTables,
    restoreConfigManager,
    restoreTables,
    setup,
    stubConfigManager,
} from '../fixtures/index';

function withUserContext<T extends AnyValue>(
    body: (context: Context) => AsyncResponse<T>,
    options: UserTestOptions,
): Promise<T> {
    return Test.withUserContext(body, options);
}

function testNatIps(expected: string) {
    return withUserContext(async context => {
        await Promise.resolve();
        let userIp = context.userIp;
        assert.strictEqual(userIp, expected);
        // should not change for a given context
        userIp = context.userIp;
        assert.strictEqual(userIp, expected);
    }, {});
}

describe('Context userIp', () => {
    before(async () => {
        await setup({ application: await createApplicationWithApi({}) });
        await initTables([]);
    });

    after(async () => {
        await restoreTables();
    });

    it("context userIp is resolved by default to '0.0.0.0'", async () => {
        stubConfigManager({ system: { natIpAdresses: undefined } });
        await testNatIps('0.0.0.0');
        restoreConfigManager();
    });

    it('context userIp is resolved and do not change inside the same context', async () => {
        stubConfigManager({ system: { natIpAdresses: '***********' } });
        await testNatIps('***********');
        restoreConfigManager();
    });

    it('context userIp is resolved with a round-robin strategy when multiple addresses are defined', async () => {
        stubConfigManager({ system: { natIpAdresses: '***********,***********,***********' } });
        await testNatIps('***********');
        await testNatIps('***********');
        await testNatIps('***********');
        await testNatIps('***********');
        restoreConfigManager();
    });
});
