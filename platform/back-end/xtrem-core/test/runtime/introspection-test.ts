import { assert } from 'chai';
import { Test } from '../../index';
import * as fixtures from '../fixtures';
import { createApplicationWithApi, restoreTables, setup, sortByKey } from '../fixtures/index';
import { TestDocument, TestDocumentLine, TestReferred } from '../fixtures/nodes';

const properties = [
    {
        code: null,
        columnType: 'integer',
        enum: null,
        isStored: true,
        isNotEmpty: false,
        isNullable: false,
        isPublished: true,
        isRequired: false,
        maxLength: 0,
        name: '_id',
        node: null,
        precision: 0,
        scale: 0,
        type: 'integer',
    },
    {
        code: null,
        columnType: 'string',
        enum: null,
        isStored: true,
        isNotEmpty: false,
        isNullable: false,
        isPublished: true,
        isRequired: false,
        maxLength: 128,
        name: '_sourceId',
        node: null,
        precision: 0,
        scale: 0,
        type: 'string',
    },
    {
        code: null,
        columnType: 'string',
        enum: null,
        isStored: true,
        isNotEmpty: false,
        isNullable: false,
        isPublished: true,
        isRequired: false,
        maxLength: 32,
        name: 'code',
        node: null,
        precision: 0,
        scale: 0,
        type: 'string',
    },
    {
        code: null,
        columnType: 'string',
        enum: null,
        isStored: true,
        isNotEmpty: false,
        isNullable: false,
        isPublished: true,
        isRequired: false,
        maxLength: 250,
        name: 'description',
        node: null,
        precision: 0,
        scale: 0,
        type: 'string',
    },
    {
        code: null,
        columnType: 'integer',
        enum: null,
        isStored: true,
        isNotEmpty: false,
        isNullable: false,
        isPublished: true,
        isRequired: false,
        maxLength: 0,
        name: 'mandatoryReference',
        precision: 0,
        scale: 0,
        type: 'reference',
    },
    {
        code: null,
        columnType: null,
        enum: null,
        isStored: false,
        isNotEmpty: false,
        isNullable: false,
        isPublished: true,
        isRequired: false,
        maxLength: 0,
        name: 'lines',
        precision: 0,
        scale: 0,
        type: 'collection',
    },
];

const indexes = [
    {
        isUnique: true,
        orderBy: {
            code: 1,
        },
    },
];

describe('Introspection', () => {
    before(async () =>
        setup({
            application: await createApplicationWithApi({ nodes: { TestDocument, TestDocumentLine, TestReferred } }),
        }),
    );
    after(() => restoreTables());
    it('can get class from table code', () =>
        Test.readonly(context => {
            const clas = context.introspection.getNodeFromTableName('test_document');
            assert.equal(clas, fixtures.nodes.TestDocument);
        }));
    it('can get property from column name', () =>
        Test.readonly(context => {
            const clas = context.introspection.getNodeFromTableName('test_document')!;
            const name = context.introspection.getPropertyNameFromColumnName(clas, 'mandatory_reference');
            assert.equal(name, 'mandatoryReference');
        }));
    it('can get a full descriptor for a node', () =>
        Test.readonly(context => {
            const descriptor = context.introspection.getNodeDescriptor(fixtures.nodes.TestDocument);
            const descriptorProperties = descriptor.properties.map(p => JSON.stringify(sortByKey(p)));
            assert.equal(descriptor.application, Test.application);
            assert.equal(descriptor.name, 'TestDocument');
            assert.equal(descriptor.tableName, 'test_document');
            assert.deepEqual(descriptor.indexes, indexes as any);
            properties.forEach(p => assert(descriptorProperties.includes(JSON.stringify(sortByKey(p)))));
        }));
});
