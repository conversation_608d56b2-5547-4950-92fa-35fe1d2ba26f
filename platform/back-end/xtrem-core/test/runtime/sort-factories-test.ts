import { assert } from 'chai';
import { decorators, Node, Reference, Test } from '../../index';
import { createApplicationWithApi, initTables, setup } from '../fixtures/index';
import { TestSysVendor, TestUser } from '../fixtures/nodes';

@decorators.node<TestAbstractNode>({
    storage: 'sql',
    isAbstract: true,
})
class TestAbstractNode extends Node {
    @decorators.referenceProperty<TestAbstractNode, 'reference'>({
        isStored: true,
        isNullable: true,
        node: () => TestOtherNode2,
    })
    readonly reference: Reference<TestOtherNode2 | null>;
}

@decorators.subNode<TestSubNode1>({
    isAbstract: true,
    extends: () => TestAbstractNode,
})
class TestSubNode1 extends TestAbstractNode {}

@decorators.subNode<TestSubNode2>({
    extends: () => TestAbstractNode,
})
class TestSubNode2 extends TestAbstractNode {}

@decorators.subNode<TestSubSubNode1A>({
    extends: () => TestSubNode1,
})
class TestSubSubNode1A extends TestSubNode1 {}

@decorators.subNode<TestSubSubNode1B>({
    extends: () => TestSubNode1,
})
class TestSubSubNode1B extends TestSubNode1 {}

@decorators.node<TestOtherNode1>({
    storage: 'sql',
})
class TestOtherNode1 extends Node {
    @decorators.referenceProperty<TestOtherNode1, 'reference'>({
        isStored: true,
        node: () => TestAbstractNode,
    })
    readonly reference: Reference<TestAbstractNode>;
}

@decorators.node<TestOtherNode2>({
    storage: 'sql',
})
class TestOtherNode2 extends Node {
    @decorators.referenceProperty<TestOtherNode2, 'reference'>({
        isStored: true,
        node: () => TestSubNode2,
    })
    readonly reference: Reference<TestSubNode2>;
}

@decorators.node<TestOtherNode3>({
    storage: 'sql',
})
class TestOtherNode3 extends Node {}

const api = {
    nodes: {
        TestAbstractNode,
        TestOtherNode1,
        TestOtherNode2,
        TestOtherNode3,
        TestSubSubNode1A,
        TestSubSubNode1B,
        TestSubNode1,
        TestSubNode2,
    },
};

describe('Sort application factories', () => {
    before(async () => {
        await setup({ application: await createApplicationWithApi(api) });
        await initTables([]);
    });

    it('factories are correctly sorted', () => {
        const names = Test.application
            .getSqlPackageFactories()
            .filter(factory => factory.name.startsWith('Test'))
            .map(factory => factory.name);
        assert.deepEqual(
            names,
            [
                TestSysVendor,
                TestUser,
                TestAbstractNode,
                // TestOtherNode3 can remain here as it does not have dependencies
                TestOtherNode3,
                TestSubNode1,
                TestSubNode2,
                // TestSubSubNodes must be after their parent (TestSubNode1)
                TestSubSubNode1A,
                TestSubSubNode1B,
                // TestOtherNodes must be after all descendants of TestAbstractNode
                TestOtherNode1,
                TestOtherNode2,
            ].map(constructor => constructor.name),
        );
    });
});
