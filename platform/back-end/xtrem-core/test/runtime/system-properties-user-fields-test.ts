import { assert } from 'chai';
import { Test } from '../../lib';
import { createApplicationWithApi, initTables, restoreTables, setup } from '../fixtures/index';
import { TestUserFields } from '../fixtures/nodes';

interface TestUserRecord {
    _id: number;
    code: string;
    description: string;
    _create_user: number;
    _update_user: number;
}

describe('User fields tests (system properties)', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi({
                nodes: { TestUserFields },
            }),
        });

        await initTables([{ nodeConstructor: TestUserFields, data: [] }]);
    });

    let userId = 0;
    let id = 0;

    it('Check setting of _create_user and _update_user for insert', () =>
        Test.withCommittedContext(
            async context => {
                const test = await context.create(TestUserFields, {
                    code: 'T01',
                    description: 'Test',
                });
                await test.$.save();
                assert.deepEqual(context.diagnoses, []);
                const result = (
                    await context.executeSql<TestUserRecord[]>(
                        `SELECT _id, code, description, _create_user, _update_user from ${context.schemaName}.test_user_fields where _id = $1`,
                        [test._id],
                    )
                ).shift()!;

                id = result._id;
                userId = Number((await context.user)?._id);
                assert.equal(result._create_user, userId);
                assert.equal(result._update_user, userId);
            },
            { config: { email: '<EMAIL>' } },
        ));

    it('Check setting of _update_user for update', () =>
        Test.withCommittedContext(
            async context => {
                const test = await context.read(
                    TestUserFields,
                    {
                        _id: id,
                    },
                    { forUpdate: true },
                );

                await test.$.set({ description: 'Updated' });
                await test.$.save();
                assert.deepEqual(context.diagnoses, []);
                const result = (
                    await context.executeSql<TestUserRecord[]>(
                        `SELECT _id, code, description, _create_user, _update_user from ${context.schemaName}.test_user_fields where _id = $1`,
                        [id],
                    )
                ).shift()!;

                assert.equal(result._create_user, userId);
                assert.equal(result._update_user, (await context.user)?._id);
            },
            { config: { email: '<EMAIL>' } },
        ));

    after(() => restoreTables());
});
