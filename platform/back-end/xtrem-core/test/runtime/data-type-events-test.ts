import { Decimal } from '@sage/xtrem-decimal';
import { assert } from 'chai';
import { assertIsRejectedWithContextDiagnose, Test } from '../../lib';
import {
    createApplicationWithApi,
    initTables,
    makeNewRecord,
    restoreConfigManager,
    restoreTables,
    setup,
    stubConfigManager,
} from '../fixtures/index';
import { TestDataTypeEvents } from '../fixtures/nodes';

async function throwsDiagnoseOnSave(node: TestDataTypeEvents, message: string): Promise<void> {
    const result = await assertIsRejectedWithContextDiagnose(node.$.save(), node.$.context, message);
    //     (async () => {
    //         try {
    //             await node.$.save();
    //         } catch (e) {
    //             if (e.extensions.diagnoses.map((d: Diagnose) => d.message).includes(message)) throw new Error(message);
    //             throw new Error(
    //                 `[${e.extensions.message}] diagnoses=[${e.extensions.diagnoses.map((d: Diagnose) => d.message)}]`,
    //             );
    //         }
    //     })(),
    //     message,
    // );
    return result;
}

describe('TestDataTypeEvents tests', () => {
    before(async () => setup({ application: await createApplicationWithApi({ nodes: { TestDataTypeEvents } }) }));

    beforeEach(() => initTables([{ nodeConstructor: TestDataTypeEvents, data: [] }]));

    it('triggers the control of the data type', async () => {
        stubConfigManager({
            textStreamContentTypes: ['text/plain'],
            binaryStreamContentTypes: ['text/plain', 'application/x-empty', 'application/pdf'],
        });

        let newRecord = makeNewRecord(1);

        // _id:1 : should fail none of the controls
        await Test.withContext(async context => {
            const newInstance = await context.create(TestDataTypeEvents, newRecord);
            /* does not throw */ await newInstance.$.save();
        });

        newRecord = makeNewRecord(2);
        // _id:2 : stringValAdvancedControls should fail the AdvancedStringDataType control
        await Test.withContext(async context => {
            const newInstance = await context.create(TestDataTypeEvents, newRecord);
            await throwsDiagnoseOnSave(
                newInstance,
                'astring_2 exceeds the maximum adaptable length allowed for this field: 2 characters.',
            );
        });

        newRecord = makeNewRecord(3);
        // _id:3 : stringValAdvancedControls should fail the StringDataType control
        await Test.withContext(async context => {
            const newInstance = await context.create(TestDataTypeEvents, newRecord);
            await throwsDiagnoseOnSave(
                newInstance,
                'string_222222222 exceeds the maximum length allowed for this field: 12 characters.',
            );
        });

        newRecord = makeNewRecord(4);
        // _id:4 : stringVal should fail the StringDataType control
        await Test.withContext(async context => {
            const newInstance = await context.create(TestDataTypeEvents, newRecord);
            await throwsDiagnoseOnSave(
                newInstance,
                `${'string_4'.repeat(20)} exceeds the maximum length allowed for this field: 32 characters.`,
            );
        });

        newRecord = makeNewRecord(5);
        // _id:5 : binary and binary1024 should pass the BinaryStreamDataType control
        await Test.withContext(async context => {
            const newInstance = await context.create(TestDataTypeEvents, newRecord);
            /* does not throw */ await newInstance.$.save();
            assert.strictEqual(context.diagnoses.length, 0);
        });

        newRecord = makeNewRecord(6);
        // _id:6 : binary1024 should fail the BinaryStreamDataType control
        await Test.withContext(async context => {
            const newInstance = await context.create(TestDataTypeEvents, newRecord);
            await throwsDiagnoseOnSave(
                newInstance,
                `Value of 'binary1024Val' exceeds the maximum length allowed: got ${newRecord.binary1024Val?.value.length} expected 1024 bytes max.`,
            );
        });

        newRecord = makeNewRecord(7);
        // _id:7 : binary1024 should fail the BinaryStreamDataType control
        await Test.withContext(async context => {
            const newInstance = await context.create(TestDataTypeEvents, newRecord);
            await throwsDiagnoseOnSave(
                newInstance,
                `Value of 'binary1024Val' exceeds the maximum length allowed: got ${newRecord.binary1024Val?.value.length} expected 1024 bytes max.`,
            );
        });

        newRecord = makeNewRecord(8);
        // _id:7 : binary1024 should fail the BinaryStreamDataType control
        await Test.withContext(async context => {
            const newInstance = await context.create(TestDataTypeEvents, newRecord);
            await throwsDiagnoseOnSave(
                newInstance,
                `Value of 'binary1024Val' exceeds the maximum length allowed: got ${newRecord.binary1024Val?.value.length} expected 33554432 bytes max.`,
            );
        });
    });

    it('finds the correct default value for the property', () =>
        Test.withContext(async context => {
            const newRecord = await context.create(TestDataTypeEvents, {
                id: 1,
                enumVal: null,
                stringVal: 'string_1',
                adaptableLength: 30,
                adaptableDefault: 'default_1',
                decimalVal: Decimal.make(0) as any,
            });

            // stringValAdvancedControls should get the data type's default
            assert.equal(await newRecord.stringValAdvancedControls, 'default_1');

            // stringValDefaultValue should get the property's default
            assert.equal(await newRecord.stringValDefaultValue, 'propDefault');
            /* does not throw */ await newRecord.$.save();
        }));

    it('correctly applies the adaptValue rule if there is one', () =>
        Test.withContext(async context => {
            const newRecord = await context.create(TestDataTypeEvents, {
                id: 1,
                enumVal: null,
                stringVal: 'string_3',
                adaptableLength: 30,
                adaptableDefault: 'default_1',
                decimalVal: Decimal.make(0.1234) as any,
            });
            await newRecord.$.save();
            const savedRecord = (
                await context.query(TestDataTypeEvents, { filter: { stringVal: 'string_3' } }).toArray()
            )[0];
            assert.equal(await savedRecord.decimalVal, 0.124);
        }));

    it('correctly controls enum values', async () => {
        await Test.withContext(async context => {
            await assert.isRejected(
                context.create(TestDataTypeEvents, {
                    id: 1,
                    enumVal: 'value7' as any, // type as any to force invalid value
                    stringVal: 'string_3',
                    adaptableLength: 30,
                    adaptableDefault: 'default_1',
                    decimalVal: Decimal.make(0.1234) as any,
                }),
                'TestDataTypeEvents.enumVal: invalid value',
            );
        });

        await Test.withContext(async context => {
            const newRecord = await context.create(TestDataTypeEvents, {
                id: 1,
                enumVal: 'value2',
                stringVal: 'string_3',
                adaptableLength: 30,
                adaptableDefault: 'default_1',
                decimalVal: Decimal.make(0.1234) as any,
            });
            /* does not throw */ await newRecord.$.save();
        });
    });

    after(async () => {
        await restoreTables();
        restoreConfigManager();
    });
});
