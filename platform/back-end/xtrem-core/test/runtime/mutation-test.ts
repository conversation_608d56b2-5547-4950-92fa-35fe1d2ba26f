import { assert } from 'chai';
import { testMutationsApplication } from '..';
import { Test } from '../../index';
import { BinaryStream, initTables, TextStream } from '../../lib';
import { clientSetup, createApplicationWithApi, enums, setup } from '../fixtures/index';
import { TestOperation } from '../fixtures/nodes';

describe('Mutation', () => {
    before(async () => {
        await setup({ application: await createApplicationWithApi({ nodes: { TestOperation } }) });
        await initTables([]);
    });

    it('should return BinaryStream value when mutation returning BinaryStream', () =>
        Test.withContext(async context => {
            const result = await TestOperation.mutationWithBinaryStreamAsResult(context, 'binary test');
            assert.equal(result instanceof BinaryStream, true);
        }));
    it('should return TextStream value when mutation returning TextStream', () =>
        Test.withContext(async context => {
            const result = await TestOperation.mutationWithTextStreamAsResult(context, 'text test');
            assert.equal(result instanceof TextStream, true);
        }));

    it('should return the value of a BinaryStream', async () => {
        const binaryContent = Buffer.from('binary test').toString('base64');
        const graph = await clientSetup({ application: await testMutationsApplication.application });
        const result = await graph
            .node('@sage/xtrem-core/testOperation')
            .mutations.mutationWithBinaryStreamAsResult(
                { value: true },
                {
                    binaryContent,
                },
            )
            .execute();
        const binaryStream = new BinaryStream(Buffer.from(result.value, 'base64'));
        assert.equal(binaryStream instanceof BinaryStream, true);
        assert.equal(binaryStream.toString(), binaryContent);
    });

    it('should return an array of enum', () =>
        Test.withContext(async context => {
            const statusList = [enums.TestEnumEnum.value1, enums.TestEnumEnum.value2, enums.TestEnumEnum.value3];
            const result = await TestOperation.mutationArrayOfEnums(context, {
                statusList,
            });
            assert.equal(result, JSON.stringify(['value1', 'value2', 'value3']));
        }));

    it('mutation without parameters', () =>
        Test.withContext(async context => {
            const result = await TestOperation.mutationWithoutParameters(context);
            assert.equal(result, 'hello');
        }));
});
