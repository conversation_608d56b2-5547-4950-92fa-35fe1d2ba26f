import { assert } from 'chai';
import { AnyValue, StringProperty, Test } from '../../lib';
import { createApplicationWithApi, initTables, restoreTables, setup } from '../fixtures/index';
import { TestCachedEncryptedValues, TestEncryptedValues } from '../fixtures/nodes';

import * as sinon from 'sinon';
import { ContextVault } from '../../lib/runtime/context-vault';

describe('Context Vault tests', () => {
    let sandbox: sinon.SinonSandbox;
    let cacheValuesSpy: sinon.SinonSpy<[category: string, value: AnyValue, properties: StringProperty[]], void>;
    let invalidateCacheSpy: sinon.SinonSpy<[category: string, key?: string | undefined], void>;

    before(async () => {
        sandbox = sinon.createSandbox();
        await setup({
            application: await createApplicationWithApi({
                nodes: { TestEncryptedValues, TestCachedEncryptedValues },
            }),
        });
    });
    beforeEach(async () => {
        cacheValuesSpy = sandbox.spy(ContextVault.prototype, 'cacheValues');
        invalidateCacheSpy = sandbox.spy(ContextVault, 'invalidateCache');
        await initTables([
            { nodeConstructor: TestEncryptedValues, data: [] },
            { nodeConstructor: TestCachedEncryptedValues, data: [] },
        ]);
    });
    afterEach(async () => {
        sandbox?.restore();
        await restoreTables();
    });

    it('encrypt password field', () =>
        Test.withContext(async context => {
            const passwordValue = 'myPlainPassword';
            const newRecord = await context.create(TestEncryptedValues, {
                id: 1,
                passwordValue,
            });
            await newRecord.$.save();

            const savedRecord = (await context.query(TestEncryptedValues).toArray())[0];

            assert.equal(await context.vault.decrypt(await savedRecord.passwordValue), passwordValue);
        }));
    it('can update an existing password', () =>
        Test.withContext(async context => {
            const passwordValue = 'myPlainPassword';
            const newRecord = await context.create(TestEncryptedValues, {
                id: 1,
                passwordValue,
            });
            await newRecord.$.save();
            const record = await context.read(
                TestEncryptedValues,
                {
                    id: 1,
                },
                { forUpdate: true },
            );

            await record.$.set({ passwordValue: `${passwordValue}_new` });
            await record.$.save();
            const newSavedRecord = (await context.query(TestEncryptedValues).toArray())[0];
            assert.equal(await context.vault.decrypt(await newSavedRecord.passwordValue), `${passwordValue}_new`);
        }));

    it('can retrieve password values with node.$.decryptValue', () =>
        Test.withContext(async context => {
            const passwordValue = 'myPlainPassword';
            const newRecord = await context.create(TestEncryptedValues, {
                id: 1,
                passwordValue,
            });
            await newRecord.$.save();

            const savedRecord = (await context.query(TestEncryptedValues).toArray())[0];

            assert.equal(await savedRecord.$.decryptValue('passwordValue'), passwordValue);
        }));

    it('can retrieve password values from cache with node.$.decryptValue', async () => {
        const passwordValue = 'myPlainPassword';
        const id = 1000;
        await Test.withCommittedContext(async context => {
            const record = await context.tryRead(TestCachedEncryptedValues, { id });
            if (record) {
                await context.delete(TestCachedEncryptedValues, {
                    id,
                });
            }
        });
        cacheValuesSpy.resetHistory();
        await Test.withCommittedContext(async context => {
            const newRecord = await context.create(TestCachedEncryptedValues, {
                id,
                passwordValue,
            });
            return newRecord.$.save();
        });

        // First read in read-only context will cache the value in the memory cache
        await Test.withReadonlyContext(async context => {
            const savedRecord = await context.tryRead(TestCachedEncryptedValues, { id });
            assert.isNotNull(savedRecord);
            assert.equal(await savedRecord.$.decryptValue('passwordValue'), passwordValue);
        });
        assert.isTrue(cacheValuesSpy.calledOnce);
        // 2nd read in read-only context will read the value from the memory cache
        // and then do not have the decrypted value in the mapped values of context vault,
        // but we should get it from the cached mapped values
        await Test.withReadonlyContext(async context => {
            const savedRecord = await context.tryRead(TestCachedEncryptedValues, { id });
            assert.isNotNull(savedRecord);
            assert.equal(await savedRecord.$.decryptValue('passwordValue'), passwordValue);
        });
        // Update the password value should invalidate the cache
        await Test.withCommittedContext(async context => {
            const node = await context.tryRead(TestCachedEncryptedValues, { id }, { forUpdate: true });
            assert.isNotNull(node);
            await node.$.set({ passwordValue: 'newPassword' });
            return node.$.save();
        });
        assert.isAtLeast(invalidateCacheSpy.callCount, 1);
        // assert.isTrue(invalidateCacheSpy.calledOnce);
        await Test.withReadonlyContext(async context => {
            const savedRecord = await context.tryRead(TestCachedEncryptedValues, { id });
            assert.isNotNull(savedRecord);
            assert.equal(await savedRecord.$.decryptValue('passwordValue'), 'newPassword');
        });
        await Test.withCommittedContext(context =>
            context.delete(TestCachedEncryptedValues, {
                id,
            }),
        );
    });
    after(async () => {
        sandbox?.restore();
        await restoreTables();
    });
});
