import { assert } from 'chai';
import { Reference, Test } from '../../index';
import { Collection, Node, decorators, integer, restoreTables } from '../../lib';
import { codeDataType } from '../fixtures/data-types/data-types';
import { createApplicationWithApi, initTables, setup } from '../fixtures/index';

// Prefix node classes with TestAvp to avoid collisions with other tests
// Avp stands for "Abstract vital parent"
@decorators.node<TestAvpDocumentLine>({
    isAbstract: true,
    storage: 'sql',
})
class TestAvpDocumentLine extends Node {
    @decorators.stringProperty<TestAvpDocumentLine, 'text'>({
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly text: Promise<string>;
}

@decorators.subNode<TestAvpPurchaseReceiptLine>({
    extends: () => TestAvpDocumentLine,
})
class TestAvpPurchaseReceiptLine extends TestAvpDocumentLine {
    @decorators.collectionProperty<TestAvpPurchaseReceiptLine, 'stockDetails'>({
        isVital: true,
        reverseReference: 'document',
        node: () => TestAvpStockDetail,
    })
    readonly stockDetails: Collection<TestAvpStockDetail>;
}

@decorators.subNode<TestAvpSalesReturnLine>({
    extends: () => TestAvpDocumentLine,
})
class TestAvpSalesReturnLine extends TestAvpDocumentLine {
    @decorators.collectionProperty<TestAvpSalesReturnLine, 'stockDetails'>({
        isVital: true,
        reverseReference: 'document',
        node: () => TestAvpStockDetail,
    })
    readonly stockDetails: Collection<TestAvpStockDetail>;
}

@decorators.node<TestAvpStockDetail>({
    storage: 'sql',
    isVitalCollectionChild: true,
})
class TestAvpStockDetail extends Node {
    @decorators.referenceProperty<TestAvpStockDetail, 'document'>({
        isStored: true,
        isVitalParent: true,
        node: () => TestAvpDocumentLine,
    })
    readonly document: Reference<TestAvpDocumentLine>;

    @decorators.integerProperty<TestAvpStockDetail, 'quantity'>({
        isStored: true,
    })
    readonly quantity: Promise<integer>;
}

describe('Polymorphic vital collection', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi({
                nodes: {
                    TestAvpDocumentLine,
                    TestAvpPurchaseReceiptLine,
                    TestAvpSalesReturnLine,
                    TestAvpStockDetail,
                },
            }),
        });
        await initTables([
            {
                nodeConstructor: TestAvpPurchaseReceiptLine,
                data: [
                    { _id: 1, text: 'purchase receipt line 1' },
                    { _id: 2, text: 'purchase receipt line 2' },
                ],
            },
            {
                nodeConstructor: TestAvpSalesReturnLine,
                data: [
                    { _id: 3, text: 'sales return line 1' },
                    { _id: 4, text: 'sales return line 2' },
                ],
            },
            {
                nodeConstructor: TestAvpStockDetail,
                data: [
                    { document: 1, _sortValue: 10, quantity: 5 },
                    { document: 1, _sortValue: 20, quantity: 7 },
                    { document: 2, _sortValue: 10, quantity: 2 },
                    { document: 3, _sortValue: 10, quantity: 20 },
                    { document: 4, _sortValue: 10, quantity: 12 },
                    { document: 4, _sortValue: 20, quantity: 18 },
                ],
            },
        ]);
    });

    after(() => restoreTables());

    it('can read collection', () =>
        Test.withContext(async context => {
            assert.deepEqual(
                await context
                    .query(TestAvpPurchaseReceiptLine)
                    .map(l => l.$.payload({ withIds: true, withoutCustomData: true }))
                    .toArray(),
                [
                    {
                        _id: 1,
                        _sourceId: '',
                        text: 'purchase receipt line 1',
                        stockDetails: [
                            { _id: 1, _sortValue: 10, _sourceId: '', quantity: 5, document: { _id: 1 } },
                            { _id: 2, _sortValue: 20, _sourceId: '', quantity: 7, document: { _id: 1 } },
                        ],
                    },
                    {
                        _id: 2,
                        _sourceId: '',
                        text: 'purchase receipt line 2',
                        stockDetails: [{ _id: 3, _sortValue: 10, _sourceId: '', quantity: 2, document: { _id: 2 } }],
                    },
                ],
            );

            assert.deepEqual(
                await context
                    .query(TestAvpSalesReturnLine)
                    .map(l => l.$.payload({ withIds: true, withoutCustomData: true }))
                    .toArray(),
                [
                    {
                        _id: 3,
                        _sourceId: '',
                        text: 'sales return line 1',
                        stockDetails: [{ _id: 4, _sortValue: 10, _sourceId: '', quantity: 20, document: { _id: 3 } }],
                    },
                    {
                        _id: 4,
                        _sourceId: '',
                        text: 'sales return line 2',
                        stockDetails: [
                            { _id: 5, _sortValue: 10, _sourceId: '', quantity: 12, document: { _id: 4 } },
                            { _id: 6, _sortValue: 20, _sourceId: '', quantity: 18, document: { _id: 4 } },
                        ],
                    },
                ],
            );
        }));

    it('can create collection', () =>
        Test.withContext(async context => {
            const line = await context.create(TestAvpPurchaseReceiptLine, {
                text: 'purchase receipt line 3',
                stockDetails: [{ quantity: 10 }, { quantity: 8 }],
            });
            await line.$.save();
            assert.deepEqual(await line.$.payload({ withIds: true, withoutCustomData: true }), {
                _id: 5,
                _sourceId: '',
                text: 'purchase receipt line 3',
                stockDetails: [
                    { _id: 7, _sortValue: 10, _sourceId: '', quantity: 10, document: { _id: 5 } },
                    { _id: 8, _sortValue: 20, _sourceId: '', quantity: 8, document: { _id: 5 } },
                ],
            });
        }));

    it('can update collection', () =>
        Test.withContext(async context => {
            const line = await context.read(TestAvpPurchaseReceiptLine, { _id: 1 }, { forUpdate: true });
            await line.stockDetails.append({ quantity: 3 });
            await line.$.save();
            assert.deepEqual(await line.$.payload({ withIds: true, withoutCustomData: true }), {
                _id: 1,
                _sourceId: '',
                text: 'purchase receipt line 1',
                stockDetails: [
                    { _id: 1, _sortValue: 10, _sourceId: '', quantity: 5, document: { _id: 1 } },
                    { _id: 2, _sortValue: 20, _sourceId: '', quantity: 7, document: { _id: 1 } },
                    { _id: 7, _sortValue: 30, _sourceId: '', quantity: 3, document: { _id: 1 } },
                ],
            });
        }));

    it('can delete', () =>
        Test.withContext(async context => {
            await context.delete(TestAvpPurchaseReceiptLine, { _id: 1 });
            const ids = (await context.query(TestAvpPurchaseReceiptLine).toArray()).map(line => line._id);
            assert.deepEqual(ids, [2]);
        }));
});
