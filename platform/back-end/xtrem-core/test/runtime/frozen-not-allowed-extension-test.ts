import { assert } from 'chai';
import { createApplicationWithApi } from '../fixtures/index';
import { TestNotAllowedFrozenCallbackChainingExtension } from '../fixtures/node-extensions/frozen-not-allowed-callback-extension';
import { TestNotAllowedFrozenValueChainingExtension } from '../fixtures/node-extensions/frozen-not-allowed-value-extension';
import { TestNotAllowedFrozenCallbackChaining } from '../fixtures/nodes/frozen-not-allowed-callback';
import { TestNotAllowedFrozenValueChaining } from '../fixtures/nodes/frozen-not-allowed-value';

describe('frozen extension chaining', () => {
    // Not allowed cases
    it('Cannot create application with invalid isFrozen override', async () => {
        await assert.isRejected(
            createApplicationWithApi({
                nodes: { TestNotAllowedFrozenCallbackChaining },
                nodeExtensions: { TestNotAllowedFrozenCallbackChainingExtension },
            }),
            'Cannot override isFrozen if already frozen in base class',
        );
    });

    it('Cannot create application with invalid isFrozen override', async () => {
        await assert.isRejected(
            createApplicationWithApi({
                nodes: { TestNotAllowedFrozenValueChaining },
                nodeExtensions: { TestNotAllowedFrozenValueChainingExtension },
            }),
            'Cannot override isFrozen if already frozen in base class',
        );
    });
});
