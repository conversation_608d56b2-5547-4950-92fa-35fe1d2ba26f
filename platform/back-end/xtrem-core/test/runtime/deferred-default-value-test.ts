import { assert } from 'chai';
import { decorators, integer, Node, Test } from '../../index';
import { NodeCreateOptions } from '../../lib/runtime';
import { createApplicationWithApi, initTables, restoreTables, setup } from '../fixtures/index';

@decorators.node<TestDeferredDefaultValue>({
    isPublished: true,
    storage: 'sql',
})
export class TestDeferredDefaultValue extends Node {
    @decorators.integerProperty<TestDeferredDefaultValue, 'deferredValue'>({
        isStored: true,
        isPublished: true,
        defaultValue() {
            return 666;
        },
        deferredDefaultValue() {
            return this._id;
        },
    })
    readonly deferredValue: Promise<integer>;
}

describe('deferred default values', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi({ nodes: { TestDeferredDefaultValue } }),
        });
        await initTables([{ nodeConstructor: TestDeferredDefaultValue, data: [] }]);
    });

    it('assigns deferred default values during commit', async () => {
        await Test.withCommittedContext(async context => {
            const node1 = await context.create(TestDeferredDefaultValue, {});
            await node1.$.save();
            const node2 = await context.create(TestDeferredDefaultValue, {});
            await node2.$.save();

            // Check that we cannot read the temporary value.
            assert.isTrue(node1.$.isValueDeferred('deferredValue'));
            await assert.isRejected(
                node1.deferredValue,
                'Cannot read property as its default value may be deferred and not yet set',
            );

            // Check the value directly from state.value. It should be the defaultValue.
            assert.equal(node1.$.getRawPropertyValue('deferredValue'), 666);
            assert.equal(node2.$.getRawPropertyValue('deferredValue'), 666);
        });

        await Test.withReadonlyContext(async context => {
            const node1 = await context.read(TestDeferredDefaultValue, { _id: 1 });
            const node2 = await context.read(TestDeferredDefaultValue, { _id: 2 });

            // deferredDefaultValue fired during commit of previous transaction.
            assert.equal(await node1.deferredValue, 1);
            assert.equal(await node2.deferredValue, 2);
        });
    });

    it('does not fire the rules if node is transient', async () => {
        await Test.withReadonlyContext(async context => {
            const testOptions = async (options: NodeCreateOptions, expected: integer): Promise<void> => {
                const node = await context.create(TestDeferredDefaultValue, {}, options);
                await context.flushDeferredActions();
                assert.equal(await node.deferredValue, expected);
            };
            // If isTransient is set defaultValue and deferredDefaultValue are both inhibited.
            // So we get the default for non nullable integer.
            await testOptions({ isTransient: true }, 0);
            // The other flags only inhibit deferredDefaultValue
            await testOptions({ isOnlyForDefaultValues: true }, 666);
            await testOptions({ isOnlyForDuplicate: true }, 666);
            await testOptions({ isOnlyForLookup: true }, 666);
        });
    });

    after(() => restoreTables());
});
