import { assert } from 'chai';
import { decorators, integer, Node, Reference, ReferenceDataType, Test } from '../../index';
import { descriptionDataType } from '../fixtures/data-types/data-types';
import { LikeTestDataTypes } from '../fixtures/data-types/reference-data-types';
import {
    childRefData,
    createApplicationWithApi,
    grandChildRefData,
    initTables,
    parentRefData,
    restoreTables,
    setup,
} from '../fixtures/index';
import { TestChildReferenceDataType, TestGrandChildReferenceDataType, TestReferenceDataType } from '../fixtures/nodes';

export const testReferenceDataTypeDefault1 = new ReferenceDataType<TestReferenceDataType, LikeTestDataTypes>({
    isDefault: true,
    reference: () => TestReferenceDataType,
    filters: {
        lookup: {
            code() {
                return this.stringVal;
            },
        },
    },
    lookup: {
        valuePath: 'code',
        helperTextPath: 'name',
        columnPaths: ['code', 'name', 'child.childCode', 'child.grandChild.grandChildCode'],
    },
});

export const testReferenceDataTypeDefault2 = new ReferenceDataType<TestReferenceDataType, LikeTestDataTypes>({
    isDefault: true,
    reference: () => TestReferenceDataType,
    filters: {
        lookup: {
            code() {
                return this.stringVal;
            },
        },
    },
    lookup: {
        valuePath: 'code',
        helperTextPath: 'name',
        columnPaths: ['code', 'name', 'child.childCode', 'child.grandChild.grandChildCode'],
    },
});

@decorators.node<TestDuplicatedDefaultReferenceDataType>({
    isPublished: true,
    storage: 'sql',
    indexes: [{ orderBy: { id: 1 }, isUnique: true, isNaturalKey: true }],
})
export class TestDuplicatedDefaultReferenceDataType extends Node {
    @decorators.integerProperty<TestDuplicatedDefaultReferenceDataType, 'id'>({
        isPublished: true,
        isStored: true,
    })
    readonly id: Promise<integer>;

    @decorators.stringProperty<TestDuplicatedDefaultReferenceDataType, 'stringVal'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly stringVal: Promise<string>;

    @decorators.referenceProperty<TestDuplicatedDefaultReferenceDataType, 'referenceDataType'>({
        isPublished: true,
        isStored: true,
        dataType: () => testReferenceDataTypeDefault1,
        node: () => TestReferenceDataType,
    })
    readonly referenceDataType: Reference<TestReferenceDataType>;

    @decorators.referenceProperty<TestDuplicatedDefaultReferenceDataType, 'referenceDataType1'>({
        isPublished: true,
        isStored: true,
        dataType: () => testReferenceDataTypeDefault2,
        node: () => TestReferenceDataType,
    })
    readonly referenceDataType1: Reference<TestReferenceDataType>;
}

describe('ReferenceDataType tests (runtime)', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi({
                nodes: {
                    TestReferenceDataType,
                    TestChildReferenceDataType,
                    TestGrandChildReferenceDataType,
                },
            }),
        });
        await initTables([
            {
                nodeConstructor: TestGrandChildReferenceDataType,
                data: grandChildRefData,
            },
            {
                nodeConstructor: TestChildReferenceDataType,
                data: childRefData,
            },
            {
                nodeConstructor: TestReferenceDataType,
                data: parentRefData,
            },
        ]);
    });

    it('Can start application and read parent data', () =>
        Test.withContext(async context => {
            const data = parentRefData;
            const nodes = await context.query(TestReferenceDataType).toArray();
            assert.equal(nodes.length, data.length, 'result length matches');
        }));

    it('Application cannot contains 2 default ReferenceDataType for the same reference node', async () => {
        await assert.isRejected(
            createApplicationWithApi({
                nodes: {
                    TestReferenceDataType,
                    TestChildReferenceDataType,
                    TestGrandChildReferenceDataType,
                    TestDuplicatedDefaultReferenceDataType,
                },
                dataTypes: { testReferenceDataTypeDefault1, testReferenceDataTypeDefault2 },
            }),
            'Default dataType testReferenceDataTypeDefault1 for TestReferenceDataType node in @sage/xtrem-core already declared as default in @sage/xtrem-core -> testReferenceDataTypeDefault2',
        );
    });
    after(() => restoreTables());
});
