import { assert } from 'chai';
import { decorators, Node, Test } from '../../index';
import { NodeDecorator, NodeStatus } from '../../lib';
import { codeDataType, descriptionDataType } from '../fixtures/data-types/data-types';
import { createApplicationWithApi, initTables, restoreTables, setup } from '../fixtures/index';
import { TestSystemComputed } from '../fixtures/nodes';

enum ActionEnum {
    create = 'create',
    update = 'update',
    delete = 'delete',
}

function expectedNodeState(): NodeStatus | undefined {
    if (TestBaseNode.action === ActionEnum.create) return NodeStatus.added;
    if (TestBaseNode.action === ActionEnum.update) return NodeStatus.modified;
    return undefined;
}

let eventsInvoked: any = {};

function testClassDecorator<T extends Node>(): NodeDecorator<T> {
    return {
        storage: 'sql',
        prepare(): void {
            eventsInvoked.prepare = true;
            assert.equal(this.$.status, expectedNodeState(), 'wrong internal state during prepare');
        },
        controlBegin(): void {
            eventsInvoked.controlBegin = true;
            assert.equal(this.$.status, expectedNodeState(), 'wrong internal state during controlBegin');
        },
        controlEnd(): void {
            eventsInvoked.controlEnd = true;
            assert.equal(this.$.status, expectedNodeState(), 'wrong internal state during controlEnd');
        },
        saveBegin(): void {
            eventsInvoked.saveBegin = true;
            assert.equal(this.$.status, expectedNodeState(), 'wrong internal state during saveBegin');
        },
        saveEnd(): void {
            eventsInvoked.saveEnd = true;
            assert.equal(this.$.status, expectedNodeState(), 'wrong internal state during saveEnd');
        },
        controlDelete(): void {
            eventsInvoked.controlDelete = true;
            assert.equal(this.$.status, NodeStatus.unchanged, 'wrong internal state during controlDelete');
        },
        deleteBegin(): void {
            eventsInvoked.deleteBegin = true;
            assert.equal(this.$.status, NodeStatus.unchanged, 'wrong internal state during delete');
        },
        deleteEnd(): void {
            eventsInvoked.deleteEnd = true;
            assert.equal(this.$.status, NodeStatus.unchanged, 'wrong internal state during delete');
        },
    };
}

@decorators.node<TestBaseNode>({
    ...testClassDecorator<TestBaseNode>(),
    isPublished: true,
    canDeleteMany: true,
    tableName: 'TestBaseNode',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
class TestBaseNode extends Node {
    @decorators.stringProperty<TestBaseNode, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestBaseNode, 'text'>({
        defaultValue(): string {
            assert.equal(TestBaseNode.action, ActionEnum.create); // TODO add to other steps that should not be called in specific situations
            assert.equal(this.$.status, NodeStatus.added, 'wrong internal state, defaultValue');
            return 'default';
        },
        prepare() {
            assert.equal(this.$.status, expectedNodeState(), 'wrong internal state, prepare');
        },
        control(): void {
            assert.equal(this.$.status, expectedNodeState(), 'wrong internal state, control');
        },
        dependsOn: ['code'],
        updatedValue(): string {
            assert.equal(this.$.status, NodeStatus.unchanged, 'wrong internal state, updatedValue');
            return 'update';
        },
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly text: Promise<string>;

    static action: ActionEnum; // create | update | delete
}

@decorators.node<TestStatusStateNode>({
    isPublished: true,
    canDeleteMany: true,
    storage: 'sql',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
class TestStatusStateNode extends Node {
    @decorators.stringProperty<TestStatusStateNode, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;
}

describe('node-state', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi({
                nodes: { TestBaseNode, TestStatusStateNode, TestSystemComputed },
            }),
        });
        await initTables([
            { nodeConstructor: TestBaseNode, data: [{ code: 'BASENODE2', text: 'default' }] },
            { nodeConstructor: TestStatusStateNode, data: [] },
            { nodeConstructor: TestSystemComputed, data: [{ id: 'COMPUTED1' }] },
        ]);
    });
    it('should have correct node state through create', async () => {
        TestBaseNode.action = ActionEnum.create;
        await Test.committed(async context => {
            // Node creation
            eventsInvoked = {};
            const baseNode = await context.create(TestBaseNode, {
                code: 'BASENODE1',
            });
            eventsInvoked = {};
            assert.equal(baseNode.$.status, NodeStatus.added);
            assert.equal(await baseNode.text, 'default');
            // Validation
            await baseNode.$.control();
            // Node modification
            await baseNode.$.set({ text: 'modified' });
            assert.equal(baseNode.$.status, NodeStatus.added);
            assert.equal(await baseNode.text, 'modified');
            // Node Save
            await baseNode.$.save();
            assert.equal(baseNode.$.status, NodeStatus.unchanged);
            assert.deepEqual(eventsInvoked, {
                prepare: true,
                controlBegin: true,
                controlEnd: true,
                saveBegin: true,
                saveEnd: true,
            });
        });
    });
    it('node state create with default test user', async () => {
        TestBaseNode.action = ActionEnum.create;
        await Test.withContext(async context => {
            const id = 'COMPUTED2';
            assert.deepEqual((await context.user)?.email, '<EMAIL>');
            await (
                await context.create(TestSystemComputed, {
                    id,
                })
            ).$.save();

            const computed = await context.read(TestSystemComputed, { id });
            assert.deepEqual(await computed.createdBy, 'Test, Unit');
        });
    });

    it('node state create <NAME_EMAIL>', async () => {
        TestBaseNode.action = ActionEnum.create;
        await Test.withContext(
            async context => {
                const before = await context.getSqlTimestamp();
                const id = 'COMPUTED2';
                assert.deepEqual((await context.user)?.email, '<EMAIL>');
                await (
                    await context.create(TestSystemComputed, {
                        id,
                    })
                ).$.save();
                const after = await context.getSqlTimestamp();

                const computed = await context.read(TestSystemComputed, { id });
                assert.deepEqual(await computed.createdBy, 'Doe, John');

                // node and postgres clocks may be a bit off. Give a bit of slack
                const slack = 10;
                assert.isAtLeast((await computed.createStamp).value, before.value - slack);
                assert.isAtMost((await computed.createStamp).value, after.value + slack);
                assert.equal((await computed.createStamp).value, (await computed.updateStamp).value);
            },
            {
                user: { email: '<EMAIL>', userName: '' },
            },
        );
    });

    it('node state update <NAME_EMAIL>', async () => {
        TestBaseNode.action = ActionEnum.create;
        await Test.withContext(
            async context => {
                const before = await context.getSqlTimestamp();
                const id = 'COMPUTED1';
                assert.deepEqual((await context.user)?.email, '<EMAIL>');
                const computed = await context.read(TestSystemComputed, { id }, { forUpdate: true });
                const createStamp = await computed.createStamp;
                assert.deepEqual(await computed.createdBy, 'Sage, Root');

                await computed.$.set({ description: 'updated' });
                await computed.$.save();
                const after = await context.getSqlTimestamp();

                assert.isAtLeast((await computed.updateStamp).value, before.value);
                assert.isAtMost((await computed.updateStamp).value, after.value + 10);
                assert.equal((await computed.createStamp).value, createStamp.value);
            },
            {
                user: { email: '<EMAIL>', userName: '' },
            },
        );
    });

    it('should have correct node state through read and update', async () => {
        eventsInvoked = {};
        await Test.committed(async context => {
            TestBaseNode.action = ActionEnum.update;
            const baseNode = await context.read(TestBaseNode, { code: 'BASENODE2' }, { forUpdate: true });
            assert.equal(baseNode.$.status, NodeStatus.unchanged, 'Node status should be unchanged');
            assert.equal(await baseNode.text, 'default');

            await baseNode.$.set({ text: 'updated' });
            assert.equal(baseNode.$.status, NodeStatus.modified, 'Node status should be modified');
            assert.equal(await baseNode.text, 'updated');

            await baseNode.$.save();
            assert.equal(baseNode.$.status, NodeStatus.unchanged);
            assert.deepEqual(eventsInvoked, {
                prepare: true,
                controlBegin: true,
                controlEnd: true,
                saveBegin: true,
                saveEnd: true,
            });
        });
    });
    it('should have correct node state through delete', async () => {
        TestBaseNode.action = ActionEnum.delete;
        await Test.committed(async context => {
            eventsInvoked = {};
            const baseNodeLoadedBeforeDelete = await context.read(
                TestBaseNode,
                { code: 'BASENODE2' },
                { forUpdate: true },
            );
            assert.isTrue(!!(await context.deleteMany(TestBaseNode, { code: 'BASENODE2' })));
            const baseNode = await context.tryRead(TestBaseNode, { code: 'BASENODE2' });
            assert.isNull(baseNode);
            // Thanks to interning process, the status of baseNodeLoadedBeforeDelete should have been set to 'invalid'
            assert.equal(baseNodeLoadedBeforeDelete.$.status, NodeStatus.invalid);
            await assert.isRejected(
                baseNodeLoadedBeforeDelete.$.set({ text: 'test' }),
                'invalid operation on stale node',
            );
            assert.deepEqual(eventsInvoked, {
                controlDelete: true,
                deleteBegin: true,
                deleteEnd: true,
            });
        });
    });

    it('delete node with no results ', async () => {
        await initTables([{ nodeConstructor: TestBaseNode, data: [{ code: 'BASENODE2', text: 'default' }] }]);
        TestBaseNode.action = ActionEnum.delete;
        await Test.committed(async context => {
            eventsInvoked = {};
            assert.isFalse(!!(await context.deleteMany(TestBaseNode, { code: 'BASENODEX' })));
            assert.isTrue(!!(await context.deleteMany(TestBaseNode, { code: 'BASENODE2' })));

            assert.deepEqual(eventsInvoked, {
                controlDelete: true,
                deleteBegin: true,
                deleteEnd: true,
            });
        });
    });

    it('Can save a node twice', async () => {
        let id: number = 0;
        await Test.committed(async context => {
            // Node creation
            const node = await context.create(TestStatusStateNode, {
                code: 'code1',
            });
            await node.$.save();
            id = node._id;
            await node.$.set({ code: 'code2' });
            await node.$.save();
        });
        // Make sure the 2nd update was correctly persisted to db (reload it from a new context)
        await Test.committed(async context => {
            const node = await context.read(TestStatusStateNode, { _id: id });
            assert.equal(await node.code, 'code2');
        });
    });

    it('should throw an error on created if an id matches a cached state', async () => {
        TestBaseNode.action = ActionEnum.create;
        const baseNodeId = await Test.committed(async context => {
            const baseNode30 = await context.create(TestBaseNode, {
                code: 'BASENODE30',
            });
            assert.equal(baseNode30.$.status, NodeStatus.added);
            await baseNode30.$.save();
            return baseNode30._id;
        });
        await assert.isRejected(
            Test.committed(async context => {
                // read with for update option to put the instance in the writable interning cache
                const baseNode30 = await context.read(TestBaseNode, { _id: baseNodeId }, { forUpdate: true });

                const baseNode31 = await context.create(TestBaseNode, {
                    _id: baseNode30._id,
                    code: 'BASENODE31',
                });

                assert.equal(baseNode31.$.status, NodeStatus.added);
                // // Node Save
                await baseNode31.$.save();
                assert.equal(baseNode31.$.status, NodeStatus.unchanged);
            }),
            `No cached state is expected for TestBaseNode key: ${baseNodeId}`,
        );
    });

    after(() => restoreTables());
});
