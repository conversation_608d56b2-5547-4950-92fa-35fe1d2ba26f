import { asyncArray } from '@sage/xtrem-async-helper';
import { assert } from 'chai';
import * as fs from 'node:fs';
import * as fsp from 'node:path';
import { MimeTypeHelper } from '../../lib/types';

interface MimeTestData {
    input: string;
    mime?: string;
}

interface MimeTestBufferData {
    input: Buffer;
    file: string;
    mime: string;
}

const emptyBuffer = Buffer.alloc(0);

const data: MimeTestData[] = [
    {
        input: `<?xml version="1.0"?>
<!DOCTYPE note SYSTEM "note.dtd">
<note>
  <to>Tove</to>
  <from>Jani</from>
  <heading>Reminder</heading>
  <body>Don't forget me this weekend!</body>
</note>`,
        mime: 'application/xml',
    },
    {
        input: `<note>
  <to>Tove</to>
  <from>Jani</from>
  <heading>Reminder</heading>
  <body>Don't forget me this weekend!</body>
</note>`,
        mime: 'application/xml',
    },
    {
        input: `<!DOCTYPE html>
<html><head>
    <title>Title of the document</title>
</head>
<body>
The content of the document......
</body>
</html>`,
        mime: 'text/html',
    },
    {
        input: `<html><head>
    <title>Title of the document</title>
</head>
<body>
The content of the document......
</body>
</html>`,
        mime: 'text/html',
    },
    { input: '<a'.repeat(60000) },
    { input: `<!-${'---><!----><!-'.repeat(14)}` },
];

const bufferData: MimeTestBufferData[] = [
    {
        file: 'visio.vsdx',
        input: emptyBuffer,
        mime: 'application/vnd.visio',
    },
    {
        file: 'word.docx',
        input: emptyBuffer,
        mime: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    },
];

bufferData.forEach(d => {
    d.input = fs.readFileSync(fsp.join(__dirname, '..', 'fixtures', 'data', 'mime-type', d.file));
});

describe('mime type helper tests (types)', () => {
    it('can guess mime from string without ReDoS', async () => {
        const maxGuessMillis = 200;
        await asyncArray(data).forEach(async d => {
            const t0 = Date.now();
            const mime = await MimeTypeHelper.guessFromString(d.input, d.mime);
            const elapsed = Date.now() - t0;
            if (elapsed > maxGuessMillis) {
                assert.fail(`guessFromString exceeds ${maxGuessMillis}ms (${elapsed}ms): ${d.input.slice(0, 30)}`);
            }
            assert.strictEqual(mime, d.mime || 'text/plain');
        });
    });

    it('can guess mime from buffer', async () => {
        const maxGuessMillis = 200;
        await asyncArray(bufferData).forEach(async d => {
            const t0 = Date.now();
            const mime = await MimeTypeHelper.guessFromBuffer(d.input);
            assert.strictEqual(mime, d.mime);
            const elapsed = Date.now() - t0;
            if (elapsed > maxGuessMillis) {
                assert.fail(`guessFromString exceeds ${maxGuessMillis}ms (${elapsed}ms)`);
            }
            assert.strictEqual(mime, d.mime || 'text/plain');
        });
    });
});
