import { asyncArray } from '@sage/xtrem-async-helper';
import { assert } from 'chai';
import { pick } from 'lodash';
import { Test } from '../../index';
import { Context, integer } from '../../lib';
import { createApplicationWithApi, initTables, restoreTables, setup } from '../fixtures/index';
import {
    TestAddressContact,
    TestBaseAddress,
    TestSupplierAddress,
    TestSupplierWithSubNodeCollection,
} from '../fixtures/nodes';

function createTestSupplierNode(len: integer): Promise<number[]> {
    return Test.committed(async context => {
        const addresses = [...Array(len).keys()].map(i => ({
            code: `ADDR-${i + 1}`,
            contacts: [...Array(len).keys()].map(j => ({ code: `CTC-${i * len + j + 1}` })),
        }));
        const parent = await context.create(TestSupplierWithSubNodeCollection, {
            code: 'SUPPLIER-1',
            addresses,
        });
        await parent.$.save();
        return parent.addresses.map(addr => addr._id).toArray();
    });
}

function checkTestSupplierNode(children: { _sortValue: number; code?: string; _id?: integer }[]): Promise<void> {
    return Test.readonly(async context => {
        const parent = await (
            await context.read(TestSupplierWithSubNodeCollection, { code: 'SUPPLIER-1' })
        ).$.payload({
            withIds: true,
        });
        const gotChildren = parent.addresses!;
        assert.equal(gotChildren.length, children.length);
        assert.deepEqual(
            gotChildren.map((child, i) => pick(child, Object.keys(children[i]))),
            children,
        );
    });
}

function readParentForUpdate(context: Context, code: string): Promise<TestSupplierWithSubNodeCollection> {
    return context.read(TestSupplierWithSubNodeCollection, { code }, { forUpdate: true });
}

describe('Vital collection on sub node', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi({
                nodes: {
                    TestBaseAddress,
                    TestSupplierAddress,
                    TestAddressContact,
                    TestSupplierWithSubNodeCollection,
                },
            }),
        });
        await initTables([
            { nodeConstructor: TestBaseAddress, data: [] },
            { nodeConstructor: TestSupplierAddress, data: [] },
            { nodeConstructor: TestAddressContact, data: [] },
            { nodeConstructor: TestSupplierWithSubNodeCollection, data: [] },
        ]);
    });

    beforeEach(() => Test.committed(context => context.deleteMany(TestSupplierWithSubNodeCollection, {})));

    it('can create', async () => {
        await createTestSupplierNode(3);
        await checkTestSupplierNode([
            { _sortValue: 10, code: 'ADDR-1' },
            { _sortValue: 20, code: 'ADDR-2' },
            { _sortValue: 30, code: 'ADDR-3' },
        ]);
        await Test.readonly(async context => {
            assert.isNotNull(await context.tryRead(TestSupplierWithSubNodeCollection, { code: 'SUPPLIER-1' }));
            assert.isNotNull(await context.tryRead(TestSupplierAddress, { code: 'ADDR-1' }));
            assert.isNotNull(await context.tryRead(TestSupplierAddress, { code: 'ADDR-2' }));
            assert.isNotNull(await context.tryRead(TestSupplierAddress, { code: 'ADDR-3' }));
        });
    });

    it('can delete', async () => {
        await createTestSupplierNode(3);
        await Test.committed(async context => (await readParentForUpdate(context, 'SUPPLIER-1')).$.delete());
        await Test.readonly(async context => {
            assert.isNull(await context.tryRead(TestSupplierWithSubNodeCollection, { code: 'SUPPLIER-1' }));
            assert.isNull(await context.tryRead(TestSupplierAddress, { code: 'ADDR-1' }));
            assert.isNull(await context.tryRead(TestSupplierAddress, { code: 'ADDR-2' }));
            assert.isNull(await context.tryRead(TestSupplierAddress, { code: 'ADDR-3' }));
        });
    });

    it('can update with new ids', async () => {
        const ids = await createTestSupplierNode(3);
        const lastId = ids[ids.length - 1];

        await Test.committed(async context => {
            const parent = await readParentForUpdate(context, 'SUPPLIER-1');
            await parent.$.set({
                addresses: [{ code: 'ADDR-A' }, { code: 'ADDR-B' }, { code: 'ADDR-C' }, { code: 'ADDR-D' }],
            });
            await parent.$.save();
        });
        await checkTestSupplierNode([
            { _sortValue: 10, code: 'ADDR-A', _id: lastId + 1 },
            { _sortValue: 20, code: 'ADDR-B', _id: lastId + 2 },
            { _sortValue: 30, code: 'ADDR-C', _id: lastId + 3 },
            { _sortValue: 40, code: 'ADDR-D', _id: lastId + 4 },
        ]);
    });

    it('can update with existing ids and new child at end', async () => {
        const ids = await createTestSupplierNode(3);
        const lastId = ids[ids.length - 1];

        await Test.committed(async context => {
            const parent = await readParentForUpdate(context, 'SUPPLIER-1');
            await parent.$.set({
                addresses: [
                    { code: 'ADDR-A', _id: ids[0] },
                    { code: 'ADDR-B', _id: ids[1] },
                    { code: 'ADDR-C', _id: ids[2] },
                    { code: 'ADDR-D' },
                ],
            });
            await parent.$.save();
        });
        await checkTestSupplierNode([
            { _sortValue: 10, code: 'ADDR-A', _id: ids[0] },
            { _sortValue: 20, code: 'ADDR-B', _id: ids[1] },
            { _sortValue: 30, code: 'ADDR-C', _id: ids[2] },
            { _sortValue: 40, code: 'ADDR-D', _id: lastId + 1 },
        ]);
    });

    it('can update with one removal and one append', async () => {
        const ids = await createTestSupplierNode(3);
        const lastId = ids[ids.length - 1];

        await Test.committed(async context => {
            const parent = await readParentForUpdate(context, 'SUPPLIER-1');
            await parent.$.set({
                addresses: [
                    // keep this comment for better formatting
                    { code: 'ADDR-A', _id: ids[0] },
                    { code: 'ADDR-C', _id: ids[2] },
                    { code: 'ADDR-D' },
                ],
            });
            await parent.$.save();
        });
        await checkTestSupplierNode([
            { _sortValue: 10, code: 'ADDR-A', _id: ids[0] },
            { _sortValue: 30, code: 'ADDR-C', _id: ids[2] },
            { _sortValue: 40, code: 'ADDR-D', _id: lastId + 1 },
        ]);
    });

    it('can update with two inserted and two appended', async () => {
        const ids = await createTestSupplierNode(3);
        const lastId = ids[ids.length - 1];

        await Test.committed(async context => {
            const parent = await readParentForUpdate(context, 'SUPPLIER-1');
            await parent.$.set({
                addresses: [
                    //
                    { code: 'ADDR-A', _id: ids[0] },
                    { code: 'ADDR-B' },
                    { code: 'ADDR-C' },
                    { code: 'ADDR-D', _id: ids[1] },
                    { _id: ids[2] },
                    { code: 'ADDR-E' },
                    { code: 'ADDR-F' },
                ],
            });
            await parent.$.save();
        });
        await checkTestSupplierNode([
            { _sortValue: 10, code: 'ADDR-A', _id: ids[0] },
            // _sortValue allocated between 0 and 1 for 2 new inserted items
            { _sortValue: 12, code: 'ADDR-B', _id: lastId + 1 },
            { _sortValue: 15, code: 'ADDR-C', _id: lastId + 2 },
            { _sortValue: 20, code: 'ADDR-D', _id: ids[1] },
            // kept old code for ids[2]
            { _sortValue: 30, code: 'ADDR-3', _id: ids[2] },
            // _sortValue allocated for appended records
            { _sortValue: 40, code: 'ADDR-E', _id: lastId + 3 },
            { _sortValue: 50, code: 'ADDR-F', _id: lastId + 4 },
        ]);
    });

    it('can reorder with 2 items moved', async () => {
        const ids = await createTestSupplierNode(6);

        await Test.committed(async context => {
            const parent = await readParentForUpdate(context, 'SUPPLIER-1');
            await parent.$.set({
                addresses: [
                    { _id: ids[0] },
                    { _id: ids[3] },
                    { _id: ids[4] },
                    { _id: ids[1] },
                    { _id: ids[2] },
                    { _id: ids[5] },
                ],
            });
            await parent.$.save();
        });
        await checkTestSupplierNode([
            { _sortValue: 10, code: 'ADDR-1', _id: ids[0] },
            { _sortValue: 40, code: 'ADDR-4', _id: ids[3] },
            { _sortValue: 50, code: 'ADDR-5', _id: ids[4] },
            { _sortValue: 52, code: 'ADDR-2', _id: ids[1] },
            { _sortValue: 55, code: 'ADDR-3', _id: ids[2] },
            { _sortValue: 60, code: 'ADDR-6', _id: ids[5] },
        ]);
    });

    it('can reverse order', async () => {
        const ids = await createTestSupplierNode(6);

        await Test.committed(async context => {
            const parent = await readParentForUpdate(context, 'SUPPLIER-1');
            await parent.$.set({
                addresses: [
                    { _id: ids[5] },
                    { _id: ids[4] },
                    { _id: ids[3] },
                    { _id: ids[2] },
                    { _id: ids[1] },
                    { _id: ids[0] },
                ],
            });
            await parent.$.save();
        });
        // Only one _sortValue is preserved: 5
        await checkTestSupplierNode([
            { _sortValue: 60, code: 'ADDR-6', _id: ids[5] },
            { _sortValue: 70, code: 'ADDR-5', _id: ids[4] },
            { _sortValue: 80, code: 'ADDR-4', _id: ids[3] },
            { _sortValue: 90, code: 'ADDR-3', _id: ids[2] },
            { _sortValue: 100, code: 'ADDR-2', _id: ids[1] },
            { _sortValue: 110, code: 'ADDR-1', _id: ids[0] },
        ]);
    });

    it('can mix reorder, insertions and deletions', async () => {
        const ids = await createTestSupplierNode(8);
        const lastId = ids[ids.length - 1];

        await Test.committed(async context => {
            const parent = await readParentForUpdate(context, 'SUPPLIER-1');
            await parent.$.set({
                addresses: [
                    { _id: ids[0] },
                    { code: 'ADDR-A' },
                    { _id: ids[1] },
                    { _id: ids[4] },
                    { code: 'ADDR-B' },
                    { code: 'ADDR-C' },
                    { _id: ids[3] },
                    { code: 'ADDR-D' },
                    { _id: ids[6] },
                    { code: 'ADDR-E' },
                ],
            });
            await parent.$.save();
        });
        await checkTestSupplierNode([
            { _sortValue: 10, code: 'ADDR-1', _id: ids[0] },
            { _sortValue: 15, code: 'ADDR-A', _id: lastId + 1 },
            { _sortValue: 20, code: 'ADDR-2', _id: ids[1] },
            { _sortValue: 50, code: 'ADDR-5', _id: ids[4] },
            { _sortValue: 52, code: 'ADDR-B', _id: lastId + 2 },
            { _sortValue: 55, code: 'ADDR-C', _id: lastId + 3 },
            { _sortValue: 60, code: 'ADDR-4', _id: ids[3] },
            { _sortValue: 65, code: 'ADDR-D', _id: lastId + 4 },
            { _sortValue: 70, code: 'ADDR-7', _id: ids[6] },
            { _sortValue: 80, code: 'ADDR-E', _id: lastId + 5 },
        ]);
    });

    it('can append a line with a partial payload', async () => {
        const ids = await createTestSupplierNode(3);
        const lastId = ids[ids.length - 1];

        await Test.committed(async context => {
            const parent = await readParentForUpdate(context, 'SUPPLIER-1');
            await parent.$.set({
                addresses: [{ _action: 'create', code: 'ADDR-A' }],
            });
            await parent.$.save();
        });
        await checkTestSupplierNode([
            { _sortValue: 10, code: 'ADDR-1', _id: ids[0] },
            { _sortValue: 20, code: 'ADDR-2', _id: ids[1] },
            { _sortValue: 30, code: 'ADDR-3', _id: ids[2] },
            { _sortValue: 40, code: 'ADDR-A', _id: lastId + 1 },
        ]);
    });

    it('can insert a line with a partial payload', async () => {
        const ids = await createTestSupplierNode(3);
        const lastId = ids[ids.length - 1];

        await Test.committed(async context => {
            const parent = await readParentForUpdate(context, 'SUPPLIER-1');
            await parent.$.set({
                addresses: [{ _action: 'create', _sortValue: 25, code: 'ADDR-A' }],
            });
            await parent.$.save();
        });
        await checkTestSupplierNode([
            { _sortValue: 10, code: 'ADDR-1', _id: ids[0] },
            { _sortValue: 20, code: 'ADDR-2', _id: ids[1] },
            { _sortValue: 25, code: 'ADDR-A', _id: lastId + 1 },
            { _sortValue: 30, code: 'ADDR-3', _id: ids[2] },
        ]);
    });

    it('can update a line with with a partial payload', async () => {
        const ids = await createTestSupplierNode(3);

        await Test.committed(async context => {
            const parent = await readParentForUpdate(context, 'SUPPLIER-1');
            await parent.$.set({
                addresses: [{ _action: 'update', _id: ids[1], code: 'ADDR-A' }],
            });
            await parent.$.save();
        });
        await checkTestSupplierNode([
            { _sortValue: 10, code: 'ADDR-1', _id: ids[0] },
            { _sortValue: 20, code: 'ADDR-A', _id: ids[1] },
            { _sortValue: 30, code: 'ADDR-3', _id: ids[2] },
        ]);
    });

    it('can reorder a line with with a partial payload', async () => {
        const ids = await createTestSupplierNode(3);

        await Test.committed(async context => {
            const parent = await readParentForUpdate(context, 'SUPPLIER-1');
            await parent.$.set({
                addresses: [{ _action: 'update', _sortValue: 40, _id: ids[1] }],
            });
            await parent.$.save();
        });
        await checkTestSupplierNode([
            { _sortValue: 10, code: 'ADDR-1', _id: ids[0] },
            { _sortValue: 30, code: 'ADDR-3', _id: ids[2] },
            { _sortValue: 40, code: 'ADDR-2', _id: ids[1] },
        ]);
    });

    it('throws if payload mixes lines with and without _action', async () => {
        const ids = await createTestSupplierNode(3);

        await assert.isRejected(
            Test.committed(async context => {
                const parent = await readParentForUpdate(context, 'SUPPLIER-1');
                await parent.$.set({
                    addresses: [{ _action: 'update', _id: ids[1] }, { code: 'ADDR-A' }],
                });
            }),
            'TestSupplierWithSubNodeCollection.addresses: invalid update data: mix of rows with and without _action',
        );
        await assert.isRejected(
            Test.committed(async context => {
                const parent = await readParentForUpdate(context, 'SUPPLIER-1');
                await parent.$.set({
                    addresses: [{ code: 'ADDR-A' }, { _action: 'update', _id: ids[1] }],
                });
            }),
            'TestSupplierWithSubNodeCollection.addresses: invalid update data: mix of rows with and without _action',
        );
    });

    it('throws if missing _sortValues are not all at the end in a partial payload', async () => {
        const ids = await createTestSupplierNode(3);

        await assert.isRejected(
            Test.committed(async context => {
                const parent = await readParentForUpdate(context, 'SUPPLIER-1');
                await parent.$.set({
                    addresses: [
                        { _action: 'create', code: 'ADDR-A' },
                        { _action: 'update', _id: ids[1] },
                    ],
                });
            }),
            'TestSupplierWithSubNodeCollection.addresses: invalid update array: some new entries without _sortValue are not at the end of the list',
        );
        await assert.isRejected(
            Test.committed(async context => {
                const parent = await readParentForUpdate(context, 'SUPPLIER-1');
                await parent.$.set({
                    addresses: [
                        { _action: 'create', code: 'ADDR-A' },
                        { _action: 'create', _sortValue: 25, code: 'ADDR-B' },
                    ],
                });
            }),
            'TestSupplierWithSubNodeCollection.addresses: invalid update array: some new entries without _sortValue are not at the end of the list',
        );
    });

    it('throws if update which creates node in collection has duplicate sort values', async () => {
        await createTestSupplierNode(3);

        await assert.isRejected(
            Test.withContext(async context => {
                const parent = await readParentForUpdate(context, 'SUPPLIER-1');
                await parent.$.set({
                    addresses: [
                        { _action: 'create', _sortValue: 200, code: 'ADDR-A' },
                        { _action: 'create', _sortValue: 200, code: 'ADDR-B' },
                    ],
                });
            }),
            'TestSupplierWithSubNodeCollection.addresses: cannot update collection: duplicate _sortValue 200',
        );
    });

    it('throws if update which creates node in collection has nodes that already exist', async () => {
        await createTestSupplierNode(3);

        await assert.isRejected(
            Test.withContext(async context => {
                const parent = await readParentForUpdate(context, 'SUPPLIER-1');
                await parent.$.set({
                    addresses: [{ _action: 'create', _sortValue: 20, code: 'ADDR-A' }],
                });
            }),
            /TestSupplierWithSubNodeCollection.addresses: cannot create entry: TestBaseAddress:_id:/,
        );
    });

    it('can filter on _sortValue', async () => {
        await createTestSupplierNode(8);

        await Test.readonly(async context => {
            const lines = await context.query(TestSupplierAddress, { filter: { _sortValue: { _lte: 50 } } }).toArray();
            assert.deepEqual(
                await asyncArray(lines)
                    .map(line => line.$.sortValue)
                    .toArray(),
                [10, 20, 30, 40, 50],
            );
        });
    });

    it('can sort on _sortValue', async () => {
        await createTestSupplierNode(3);

        await Test.readonly(async context => {
            const lines = await context.query(TestSupplierAddress, { orderBy: { _sortValue: -1 } }).toArray();
            assert.deepEqual(
                await asyncArray(lines)
                    .map(line => line.$.sortValue)
                    .toArray(),
                [30, 20, 10],
            );
        });
    });

    after(() => restoreTables());
});
