import { assert, expect } from 'chai';
import { testValidIsActiveApplication } from '..';
import { Test, ValidationSeverity } from '../../lib';
import { createApplicationWithApi, initTables, restoreTables, setup } from '../fixtures/index';
import {
    TestChildIsActive,
    TestParentIsActive,
    TestReferringValidIsActive,
    TestReferringValidIsActiveFunc,
    TestValidIsActive,
} from '../fixtures/nodes';
// Don't add these nodes to the nodes/index.ts file, it will break other tests
import { MultipleIsActive } from '../fixtures/nodes/multiple-is-active';
import { NonBooleanIsActive } from '../fixtures/nodes/non-boolean-is-active';

const referringValidIsActiveData = [
    {
        _id: 1,
        code: 'REF1',
        reference: 1,
        ignoringReference: 1,
    },
];

const validIsActiveData = [
    {
        _id: 1,
        code: 'VAL1',
        activeField: true,
    },
    {
        _id: 2,
        code: 'VAL2',
        activeField: false,
    },
    {
        _id: 3,
        code: 'VAL3',
        activeField: true,
    },
];

const onlyActiveValidIsActiveData = validIsActiveData.filter(node => node.activeField === true);

describe('provide isActive for referenceProperties', () => {
    describe('property with isActive provided', () => {
        it('should throw when creating a node with non-boolean isActive property provided', async () => {
            await assert.isRejected(
                createApplicationWithApi({ nodes: { NonBooleanIsActive } }),
                'NonBooleanIsActive.isActive: Properties that provide isActive can only be boolean',
            );
        });
        it('should throw when creating a node with multiple isActive properties provided', async () => {
            await assert.isRejected(
                createApplicationWithApi({ nodes: { MultipleIsActive } }),
                'MultipleIsActive.isActive2: isActive1: ambiguous isActive property',
            );
        });
        it('should create a node with a valid isActive property provided', () =>
            createApplicationWithApi({ nodes: { TestValidIsActive } }));
    });

    describe('isActive default control and lookups', () => {
        before(async () => {
            await setup({ application: await testValidIsActiveApplication.application });
            await initTables([
                { nodeConstructor: TestValidIsActive, data: validIsActiveData },
                { nodeConstructor: TestReferringValidIsActive, data: referringValidIsActiveData },
                { nodeConstructor: TestReferringValidIsActiveFunc, data: referringValidIsActiveData },
                { nodeConstructor: TestParentIsActive, data: [] },
                { nodeConstructor: TestChildIsActive, data: [] },
            ]);
        });

        after(() => restoreTables());

        it('control filter is executed at validation and adds a diagnose when creating with inactive references', () =>
            Test.withUncommittedContext(async context => {
                const referringValidIsActive = {
                    _id: 2,
                    code: 'REF2',
                    reference: 2,
                    ignoringReference: 2,
                };

                const newReferring = await context.create(TestReferringValidIsActive, referringValidIsActive);
                assert.notOk(await newReferring.$.control());
                assert.deepEqual(context.diagnoses, [
                    {
                        path: ['reference'],
                        severity: ValidationSeverity.error,
                        message: 'The record cannot be referenced because it is inactive.',
                    },
                ]);
            }));

        it('creates when ignoreIsActive is set and an inactive reference passed', () =>
            Test.withUncommittedContext(async context => {
                const referringValidIsActive = {
                    _id: 2,
                    code: 'REF2',
                    reference: 1,
                    ignoringReference: 2,
                };

                const newReferring = await context.create(TestReferringValidIsActive, referringValidIsActive);
                assert.ok(await newReferring.$.control());
            }));

        it('Can create inactive node with collection property', () =>
            Test.withUncommittedContext(async context => {
                const parent = await context.create(TestParentIsActive, {
                    code: 'P1',
                    activeField: false,
                    children: [{ code: 'C1' }, { code: 'C2' }],
                });

                await parent.$.save();
                assert.equal(await parent.code, 'P1');
                assert.equal(await parent.activeField, false);
                assert.equal(String(await (await parent.children.elementAt(0)).code), 'C1');
                assert.equal(String(await (await parent.children.elementAt(1)).code), 'C2');
            }));

        it('control filter is executed at validation and adds a diagnose when updating with inactive references', () =>
            Test.withUncommittedContext(async context => {
                const referring = await context.read(
                    TestReferringValidIsActive,
                    { _id: referringValidIsActiveData[0]._id },
                    { forUpdate: true },
                );
                await referring.$.set({ reference: validIsActiveData[1] as any });

                await assert.isRejected(referring.$.save(), /was not updated/);
            }));
        it('updates when ignoreIsActive computes to true an inactive reference passed', () =>
            Test.withUncommittedContext(async context => {
                const referring = await context.read(
                    TestReferringValidIsActiveFunc,
                    { _id: referringValidIsActiveData[0]._id },
                    { forUpdate: true },
                );
                await referring.$.set({ code: 'testCode' });
                await referring.$.set({ ignoringReference: validIsActiveData[1] as any });

                // HERE HERE
                /* does not throw */ await (() => referring.$.save())();
            }));
        it('updates when ignoreIsActive is set to false an inactive reference passed', () =>
            Test.withUncommittedContext(async context => {
                const referring = await context.read(
                    TestReferringValidIsActiveFunc,
                    { _id: referringValidIsActiveData[0]._id },
                    { forUpdate: true },
                );
                await referring.$.set({ code: 'testCodes' });
                await referring.$.set({ ignoringReference: validIsActiveData[1] as any });

                await assert.isRejected(referring.$.save());
            }));

        it('updates when ignoreIsActive is computed and an inactive reference passed', () =>
            Test.withUncommittedContext(async context => {
                const referring = await context.read(
                    TestReferringValidIsActive,
                    { _id: referringValidIsActiveData[0]._id },
                    { forUpdate: true },
                );
                await referring.$.set({ ignoringReference: validIsActiveData[1] as any });
                /* does not throw */ await (() => referring.$.save())();
            }));

        it('updates when ignoreIsActive is set and an inactive reference passed', () =>
            Test.withUncommittedContext(async context => {
                const referring = await context.read(
                    TestReferringValidIsActive,
                    { _id: referringValidIsActiveData[0]._id },
                    { forUpdate: true },
                );
                await referring.$.set({ ignoringReference: validIsActiveData[1] as any });
                /* does not throw */ await (() => referring.$.save())();
            }));
        it('updates when ignoreIsActive is set and an inactive reference passed', () =>
            Test.withUncommittedContext(async context => {
                const referring = await context.read(
                    TestReferringValidIsActive,
                    { _id: referringValidIsActiveData[0]._id },
                    { forUpdate: true },
                );
                await referring.$.set({ ignoringReference: validIsActiveData[1] as any });
                /* does not throw */ await (() => referring.$.save())();
            }));

        it('filters out inactive values for a reference', () =>
            Test.withReadonlyContext(async context => {
                const lookupParameters = await context.makeLookupQueryParameters(
                    TestReferringValidIsActive,
                    'reference',
                    {
                        _id: '1',
                    },
                );
                const results = await context
                    .lookup(lookupParameters)
                    .map(async (data: TestValidIsActive) => {
                        return {
                            _id: data._id,
                            code: await data.code,
                            activeField: await data.activeField,
                            _customData: {},
                        };
                    })
                    .toArray();

                assert.equal(results.length, onlyActiveValidIsActiveData.length);
                onlyActiveValidIsActiveData.forEach(activeValue => assert.deepInclude(results, activeValue));
                results.forEach(result => expect(result instanceof TestValidIsActive));
            }));

        it('does not filter out inactive values for a reference if ignoreIsActive is set', () =>
            Test.withReadonlyContext(async context => {
                const lookupParameters = await context.makeLookupQueryParameters(
                    TestReferringValidIsActive,
                    'ignoringReference',
                    {
                        _id: '1',
                    },
                );
                const results = await context
                    .lookup(lookupParameters)
                    .map(async (data: TestValidIsActive) => {
                        return {
                            _id: data._id,
                            code: await data.code,
                            activeField: await data.activeField,
                            _customData: {},
                        };
                    })
                    .toArray();

                assert.equal(results.length, validIsActiveData.length);
                validIsActiveData.forEach(value => assert.deepInclude(results, value));
                results.forEach(result => expect(result instanceof TestValidIsActive));
            }));
    });
});
