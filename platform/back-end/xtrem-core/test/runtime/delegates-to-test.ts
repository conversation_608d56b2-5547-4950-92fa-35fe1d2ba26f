import { assert } from 'chai';
import { decorators, Node, Reference, Test } from '../../index';
import { codeDataType } from '../fixtures/data-types/data-types';
import { createApplicationWithApi, initTables, restoreTables, setup } from '../fixtures/index';

@decorators.node<TestDelegatedTo>({
    isPublished: true,
    storage: 'sql',
    isContentAddressable: true,
})
class TestDelegatedTo extends Node {
    @decorators.stringProperty<TestDelegatedTo, 'text'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly text: Promise<string>;

    @decorators.booleanProperty<TestDelegatedTo, 'bool'>({
        isStored: true,
    })
    readonly bool: Promise<boolean>;
}

@decorators.node<TestDelegatesTo>({
    isPublished: true,
    storage: 'sql',
    indexes: [{ orderBy: { code: 1 }, isUnique: true, isNaturalKey: true }],
})
export class TestDelegatesTo extends Node {
    @decorators.stringProperty<TestDelegatesTo, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestDelegatesTo, 'delegate'>({
        isStored: true,
        isMutable: true,
        node: () => TestDelegatedTo,
    })
    readonly delegate: Reference<TestDelegatedTo>;

    @decorators.stringProperty<TestDelegatesTo, 'text'>({
        delegatesTo: { delegate: 'text' },
    })
    readonly text: Promise<string>;

    @decorators.booleanProperty<TestDelegatesTo, 'bool'>({
        delegatesTo: { delegate: 'bool' },
    })
    readonly bool: Promise<boolean>;
}

describe('DelegatesTo decorator attribute', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi({
                nodes: {
                    TestDelegatedTo,
                    TestDelegatesTo,
                },
            }),
        });
        // Manually create the table.
        await initTables([
            { nodeConstructor: TestDelegatedTo, data: [] },
            { nodeConstructor: TestDelegatesTo, data: [] },
        ]);
    });

    it('can create and update a node with delegated properties', async () => {
        let id1: number;
        // create a node and save it
        await Test.withCommittedContext(async context => {
            const node = await context.create(TestDelegatesTo, { code: 'CODE1', text: 'text 1', bool: true });
            await node.$.save();
            id1 = node._id;
            assert.isAbove(id1, 0);
            const payload = await node.$.payload({ withIds: true, withoutCustomData: true });
            assert.deepEqual(payload, {
                _id: id1,
                code: 'CODE1',
                delegate: { _id: 1, text: 'text 1', bool: true, _sourceId: '' },
                text: 'text 1',
                bool: true,
                _sourceId: '',
            });
        });

        // verify that the node has been saved, by reading it from another context
        await Test.withReadonlyContext(async context => {
            const payloads = await context
                .query(TestDelegatesTo, {})
                .map(node => node.$.payload({ withIds: true, withoutCustomData: true }))
                .toArray();
            assert.deepEqual(payloads, [
                {
                    _id: id1,
                    code: 'CODE1',
                    delegate: { _id: 1, text: 'text 1', bool: true, _sourceId: '' },
                    text: 'text 1',
                    bool: true,
                    _sourceId: '',
                },
            ]);
        });

        // update the node and verify that it matches
        await Test.withCommittedContext(async context => {
            const node = await context.read(TestDelegatesTo, { _id: id1 }, { forUpdate: true });
            await node.$.set({ text: 'updated text', bool: false });
            await node.$.save();
            const payload = await node.$.payload({ withIds: true, withoutCustomData: true });
            assert.deepEqual(payload, {
                _id: id1,
                code: 'CODE1',
                // _id is bumped because TestDelegatedTo node is content addressable
                delegate: { _id: 2, text: 'updated text', bool: false, _sourceId: '' },
                text: 'updated text',
                bool: false,
                _sourceId: '',
            });
        });

        // verify that the node has been saved, by reading it from another context
        await Test.withReadonlyContext(async context => {
            const node = await context.read(TestDelegatesTo, { _id: id1 });
            const payload = await node.$.payload({ withIds: true, withoutCustomData: true });
            assert.deepEqual(payload, {
                _id: id1,
                code: 'CODE1',
                delegate: { _id: 2, text: 'updated text', bool: false, _sourceId: '' },
                text: 'updated text',
                bool: false,
                _sourceId: '',
            });
        });
    });

    after(() => restoreTables());
});
