import { ValidationSeverity } from '@sage/xtrem-shared';
import { assert } from 'chai';
import { decorators, Node, Reference, Test } from '../../index';
import { codeDataType, descriptionDataType } from '../fixtures/data-types/data-types';
import { createApplicationWithApi, initTables, restoreTables, setup } from '../fixtures/index';

/** DECLARE SOME TYPES */
@decorators.node<TestOldNode>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestOldNode',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
class TestOldNode extends Node {
    @decorators.stringProperty<TestOldNode, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestOldNode, 'strVal'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly strVal: Promise<string>;

    @decorators.stringProperty<TestOldNode, 'strDefVal'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
        defaultValue: () => 'XXX',
        async control(cx): Promise<void> {
            if ((await (await this.$.old).strVal) === (await this.strVal))
                cx.addDiagnose(ValidationSeverity.error, "'strVal' must change");
        },
    })
    readonly strDefVal: Promise<string>;

    @decorators.referenceProperty<TestOldNode, 'child'>({
        isPublished: true,
        isStored: true,
        node: () => TestOldChild,
    })
    readonly child: Reference<TestOldChild>;
}

@decorators.node<TestOldChild>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestOldChild',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
class TestOldChild extends Node {
    @decorators.stringProperty<TestOldChild, 'code'>({
        dataType: () => codeDataType,
        isStored: true,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestOldChild, 'strValChild'>({
        dataType: () => descriptionDataType,
        isStored: true,
    })
    readonly strValChild: Promise<string>;
}

/** DECLARE SOME DATA */
const parentData = [
    {
        _id: 1,
        code: 'CODE1',
        strVal: 'VAL1',
        child: 1,
        strDefVal: 'XXX',
    },
    {
        _id: 2,
        code: 'CODE2',
        strVal: 'VAL2',
        child: 2,
        strDefVal: 'XXX',
    },
];

const childData = [
    {
        _id: 1,
        code: 'CODE1.1',
        strValChild: 'VAL1.1',
    },
    {
        _id: 2,
        code: 'CODE2.1',
        strValChild: 'VAL2.1',
    },
];

describe("'Old instance state' tests", () => {
    before(async () => {
        await setup({
            stubResetTables: false,
            application: await createApplicationWithApi({ nodes: { TestOldChild, TestOldNode } }),
        });
        await initTables([
            { nodeConstructor: TestOldChild, data: childData },
            { nodeConstructor: TestOldNode, data: parentData },
        ]);
    });
    it('read', () =>
        Test.uncommitted(async context => {
            const node = await context.read(TestOldNode, { code: parentData[0].code });
            assert.isDefined(await node.child); // Force the reference to be loaded
            assert.equal(await (await node.$.old).code, parentData[0].code);
            const oldNode = await node.$.old;
            await assert.isRejected(oldNode.$.set({ strVal: 'xxx' }), 'node is readonly');
            assert.equal(await (await node.$.old).strVal, parentData[0].strVal);
            assert.isDefined(await (await node.$.old).child, parentData[0].strVal);
            assert.equal(await (await (await node.$.old).child).code, childData[0].code);
            assert.equal(await (await (await node.$.old).child).strValChild, childData[0].strValChild);
        }));
    it('read (force lazy loading)', () =>
        Test.uncommitted(async context => {
            const node = await context.read(TestOldNode, { code: parentData[0].code });
            // Here, node.child has not been loaded yet. Invoking node.$.old.child should force 'node' to load the 'child' reference
            assert.isDefined(await (await node.$.old).child, parentData[0].strVal);
            assert.equal(await (await (await node.$.old).child).code, childData[0].code);
            assert.equal(await (await (await node.$.old).child).strValChild, childData[0].strValChild);
        }));
    it('create', () =>
        Test.uncommitted(async context => {
            const data = { code: 'NEW1', strVal: 'newStr1', child: { code: 'CODE1.1' } };
            const node = await context.create(TestOldNode, data);
            await assert.isRejected(node.$.old, "'old' not accessible in creation status");
        }));
    it('update (raw value)', () =>
        Test.uncommitted(async context => {
            const node = await context.read(TestOldNode, { code: parentData[0].code }, { forUpdate: true });
            await node.$.set({ strVal: 'new str' });
            // Make sure we have the right 'old value'
            assert.equal(await (await node.$.old).strVal, parentData[0].strVal);
            await node.$.set({ strVal: 'new str 2' });
            // .. even after the second update
            assert.equal(await (await node.$.old).strVal, parentData[0].strVal);
        }));
    it('update (reference)', () =>
        Test.uncommitted(async context => {
            const node = await context.read(TestOldNode, { code: parentData[0].code }, { forUpdate: true });
            assert.isNotNull(await node.child);
            await node.$.set({ child: await context.read(TestOldChild, { code: childData[1].code }) });
            // Make sure we have the right 'old value'
            assert.isNotNull(await (await node.$.old).child);
            assert.equal((await (await node.$.old).child)._id, childData[0]._id);
        }));
    it('control', () =>
        Test.uncommitted(async context => {
            const node = await context.read(TestOldNode, { code: parentData[0].code }, { forUpdate: true });
            await assert.isRejected(node.$.save(), /was not updated/);
            assert.deepEqual(node.$.context.diagnoses[0], {
                message: "'strVal' must change",
                path: ['strDefVal'],
                severity: 3,
            });
        }));

    after(() => restoreTables());
});
