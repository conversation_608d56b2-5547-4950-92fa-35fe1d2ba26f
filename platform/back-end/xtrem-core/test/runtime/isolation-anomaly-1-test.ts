import { asyncArray } from '@sage/xtrem-async-helper';
import { assert } from 'chai';
import { ConditionVariable, Context, decorators, IsolationLevel, Node, Test } from '../../index';
import { codeDataType } from '../fixtures/data-types/data-types';
import { createApplicationWithApi, initTables, restoreTables, setup } from '../fixtures/index';

@decorators.node<TestDoctor>({
    storage: 'sql',
    canDeleteMany: true,
    indexes: [{ orderBy: { name: 1 }, isUnique: true }],
})
class TestDoctor extends Node {
    @decorators.stringProperty<TestDoctor, 'name'>({
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly name: Promise<string>;

    @decorators.booleanProperty<TestDoctor, 'onCall'>({
        isStored: true,
    })
    readonly onCall: Promise<boolean>;
}

// Anomaly #1 from https://drkp.net/papers/ssi-vldb12.pdf
//
// -------------------------------------------- + -----------------------------------------
// Book Dr. Alice                               | Book Dr. Bob
// -------------------------------------------- + -----------------------------------------
// x ← SELECT COUNT(∗)                          |
//     FROM doctors WHERE on−call = true        | x ← SELECT COUNT(∗)
//                                              |     FROM doctors WHERE on−call = true
//                                              |
// IF x ≥ 2 THEN UPDATE doctors                 |
//     SET on−call = false WHERE name = Alice   | IF x ≥ 2 THEN UPDATE doctors
// COMMIT                                       |     SET on−call = false WHERE name = Bob
//                                              | COMMIT

describe('can handle serialization anomaly #1', () => {
    const names = ['Alice', 'Bob'];

    before(async () => setup({ application: await createApplicationWithApi({ nodes: { TestDoctor } }) }));
    before(() =>
        initTables([
            {
                nodeConstructor: TestDoctor,
                data: names.map(name => ({ name })),
            },
        ]),
    );

    async function releaseAllDoctors(): Promise<void> {
        await Test.withCommittedContext(async context =>
            asyncArray(await context.query(TestDoctor, { forUpdate: true }).toArray()).forEach(async doctor => {
                await doctor.$.set({ onCall: false });
                await doctor.$.save();
            }),
        );
    }

    function countAvailableDoctors(context: Context): Promise<number> {
        return context.queryCount(TestDoctor, { filter: { onCall: false } });
    }

    function bookDoctor(name: string, isolationLevel: IsolationLevel, conditions: ConditionVariable[]) {
        return (() =>
            Test.withCommittedContext(
                async context => {
                    const count = await countAvailableDoctors(context);
                    if (count <= 1) return false;

                    // Synchronization: we notify that this doctor reached this point
                    // and then we wait_ for both doctors to be notified.
                    conditions.find(condition => condition.name === name)!.notifyAll();
                    await asyncArray(conditions).forEach(condition => condition.wait());

                    const doctor = await context.read(TestDoctor, { name }, { forUpdate: true });
                    await doctor.$.set({ onCall: true });
                    await doctor.$.save();
                    return true;
                },
                { isolationLevel },
            ))();
    }

    async function testDoctors(isolation: IsolationLevel): Promise<number> {
        await releaseAllDoctors();

        const conditions = names.map(name => Test.createConditionVariable(name));

        const promises = names.map(name => bookDoctor(name, isolation, conditions));

        await Promise.all(promises);

        return Test.withCommittedContext(countAvailableDoctors);
    }

    it('succeeds with serializable isolation', async () => {
        // isolation succeeded: available count is 1
        assert.equal(await testDoctors('high'), 1);
    });

    it('fails with repeatable read isolation', async () => {
        // isolation failed: available count is 0
        assert.equal(await testDoctors('medium'), 0);
    });

    it('fails with read committed isolation', async () => {
        // isolation failed: available count is 0
        assert.equal(await testDoctors('low'), 0);
    });

    after(() => restoreTables());
});
