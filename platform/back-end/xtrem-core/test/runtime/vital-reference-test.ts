import { assert } from 'chai';
import { omit, pick } from 'lodash';
import { fixtures, testVitalReferenceApplication } from '..';
import { Test } from '../../index';
import { Context, NodeQueryFilter, integer } from '../../lib';
import { initTables, nonVitalReferenceData, restoreTables, setup } from '../fixtures/index';
import { TestNonVitalReference } from '../fixtures/nodes';

function createTestNode(withOptional: boolean, i = 1): Promise<integer> {
    return Test.committed(async context => {
        const parent = await context.create(TestVitalReferenceParent, {
            code: `P${i}`,
            mandatoryVitalRef: {
                code: `M${i}`,
                text: 'Child mandatory 1',
            },
            optionalVitalRef: withOptional
                ? {
                      code: `O${i}`,
                      text: 'Child optional 1',
                      nonVitalRef: 1,
                  }
                : undefined,
        });
        await parent.$.save();
        return parent.$.id;
    });
}

function createTransientTestNode(withOptional: boolean): Promise<fixtures.importedNodes.TestVitalReferenceParent> {
    return Test.uncommitted(context => {
        return context.create(
            TestVitalReferenceParent,
            {
                code: 'P2',
                mandatoryVitalRef: {
                    code: 'M2',
                    text: 'Child mandatory 2',
                },
                optionalVitalRef: withOptional
                    ? {
                          code: '02',
                          text: 'Chold Optional 2',
                          nonVitalRef: 1,
                      }
                    : undefined,
                nonVitalRef: {
                    _id: nonVitalReferenceData[0]._id,
                },
            },
            { isTransient: true },
        );
    });
}

interface ExpectedVitalTestNode {
    code: string;
    mandatoryVitalRef: { code?: string };
    optionalVitalRef: { code?: string; nonVitalRef?: { _id?: number; code?: string; text?: string } } | null;
}

function checkTestNode(expected: ExpectedVitalTestNode): Promise<void> {
    return Test.readonly(async context => {
        // We need to get payload with _ids to be able to test optionalVitalRef
        const parent = await (
            await context.read(TestVitalReferenceParent, { code: expected.code })
        ).$.payload({ withIds: true });
        assert.equal(parent.code, expected.code);
        assert.isObject(parent.mandatoryVitalRef);
        assert.deepEqual(
            pick(parent.mandatoryVitalRef, Object.keys(expected.mandatoryVitalRef)),
            expected.mandatoryVitalRef,
        );
        if (expected.optionalVitalRef) {
            const optionalVitalRef = pick(
                (parent as ExpectedVitalTestNode).optionalVitalRef,
                Object.keys(expected.optionalVitalRef),
            );
            const nonVitalRef = parent.optionalVitalRef?.nonVitalRef;
            if (nonVitalRef) {
                optionalVitalRef.nonVitalRef = omit(
                    await (
                        await context.read(TestNonVitalReference, pick(nonVitalRef, '_id'))
                    ).$.payload({ withIds: true }),
                    '_sourceId',
                );
            }
            assert.deepEqual(optionalVitalRef, expected.optionalVitalRef);
        } else {
            assert.isNull(parent.optionalVitalRef);
        }
    });
}

function readParentForUpdate(context: Context): Promise<fixtures.importedNodes.TestVitalReferenceParent> {
    return context.read(TestVitalReferenceParent, { code: 'P1' }, { forUpdate: true });
}

const { TestVitalReferenceChildMandatory, TestVitalReferenceChildOptional, TestVitalReferenceParent } = fixtures.nodes;

describe('Vital reference', () => {
    before(async () => {
        await setup({ application: await testVitalReferenceApplication.application });
        await initTables([
            { nodeConstructor: TestVitalReferenceChildMandatory, data: [] },
            { nodeConstructor: TestVitalReferenceChildOptional, data: [] },
            { nodeConstructor: TestVitalReferenceParent, data: [] },
            { nodeConstructor: TestNonVitalReference, data: nonVitalReferenceData },
        ]);
    });

    beforeEach(() => Test.committed(context => context.deleteMany(TestVitalReferenceParent, {})));

    it('added unique index to child table', () =>
        Test.readonly(context => {
            const desc = Test.application
                .getFactoryByConstructor(TestVitalReferenceChildMandatory)
                .table.getTableDefinition(context);
            assert.deepInclude(desc.indexes!, {
                name: 'test_vital_reference_child_mandatory_ind1',
                isUnique: true,
                columns: [
                    { name: '_tenant_id', ascending: true },
                    { name: 'parent', ascending: true },
                ],
            });
        }));

    it('can create - with optional', async () => {
        await createTestNode(true);
        await checkTestNode({
            code: 'P1',
            mandatoryVitalRef: { code: 'M1' },
            optionalVitalRef: { code: 'O1', nonVitalRef: nonVitalReferenceData[0] },
        });
    });

    it('can create - without optional', async () => {
        await createTestNode(false);
        await checkTestNode({
            code: 'P1',
            mandatoryVitalRef: { code: 'M1' },
            optionalVitalRef: null,
        });
    });

    it('can create incomplete node', () =>
        Test.uncommitted(async context => {
            const parent = await context.create(TestVitalReferenceParent, {
                code: 'P1',
            });
            await parent.$.save();
            assert.deepEqual(await parent.$.payload({ withoutCustomData: true }), {
                code: 'P1',
                mandatoryVitalRef: { code: 'P1_CHILD', parent: {}, text: '' },
                optionalVitalRef: null,
                nonVitalRef: null,
            });
        }));

    it('can create transient - with optional', async () => {
        const node = await createTransientTestNode(true);
        assert.ok(node._id < 0);
        assert.strictEqual(await node.code, 'P2');
    });

    it('can create transient - without optional', async () => {
        const node = await createTransientTestNode(false);
        assert.ok(node._id < 0);
        assert.strictEqual(await node.code, 'P2');
    });

    it('can delete', async () => {
        await createTestNode(true);
        await Test.committed(async context => (await readParentForUpdate(context)).$.delete());
        await Test.readonly(async context => {
            assert.isNull(await context.tryRead(TestVitalReferenceParent, { code: 'P1' }));
            assert.isNull(await context.tryRead(TestVitalReferenceChildMandatory, { code: 'M1' }));
            assert.isNull(await context.tryRead(TestVitalReferenceChildOptional, { code: 'O1' }));
        });
    });

    it('can update with same references', async () => {
        await createTestNode(true);
        await Test.committed(async context => {
            const parent = await readParentForUpdate(context);
            await parent.$.set({
                code: 'P2',
                mandatoryVitalRef: { code: 'M2' },
                optionalVitalRef: { code: 'O2' },
            });
            await parent.$.save();
        });
        await checkTestNode({
            code: 'P2',
            mandatoryVitalRef: { code: 'M2' },
            optionalVitalRef: { code: 'O2', nonVitalRef: nonVitalReferenceData[0] },
        });
    });

    it('can reset optional reference in update', async () => {
        await createTestNode(true);
        // check that optional exists in database
        await Test.readonly(async context => {
            assert.isNotNull(await context.tryRead(TestVitalReferenceChildOptional, { code: 'O1' }));
        });
        await Test.committed(async context => {
            const parent = await readParentForUpdate(context);
            await parent.$.set({
                code: 'P2',
                mandatoryVitalRef: { code: 'M2' },
                optionalVitalRef: null,
            });
            await parent.$.save();
        });
        await checkTestNode({
            code: 'P2',
            mandatoryVitalRef: { code: 'M2' },
            optionalVitalRef: null,
        });
        // check that optional has been deleted
        await Test.readonly(async context => {
            assert.isNull(await context.tryRead(TestVitalReferenceChildOptional, { code: 'O1' }));
        });
    });

    it('can set optional references in update', async () => {
        await createTestNode(false); // without optional
        await Test.committed(async context => {
            const parent = await readParentForUpdate(context);
            await parent.$.set({
                code: 'P2',
                optionalVitalRef: { code: 'O2', nonVitalRef: 2 },
            });
            await parent.$.save();
        });
        await checkTestNode({
            code: 'P2',
            mandatoryVitalRef: { code: 'M1' },
            optionalVitalRef: { code: 'O2', nonVitalRef: nonVitalReferenceData[1] },
        });
    });

    it('cannot reset mandatory reference in update', async () => {
        await createTestNode(true);
        await Test.uncommitted(async context => {
            const parent = await readParentForUpdate(context);
            await parent.$.set({
                code: 'P2',
                mandatoryVitalRef: null as any,
            });
            await assert.isRejected(parent.$.save(), /was not updated/);
            assert.deepEqual(context.diagnoses, [
                {
                    severity: 4,
                    path: ['mandatoryVitalRef'],
                    message: 'TestVitalReferenceParent.mandatoryVitalRef: property is required',
                },
            ]);
        });
    });

    it('can filter on vital reference', async () => {
        const id1 = await createTestNode(true, 1);
        const id2 = await createTestNode(false, 2);
        const id3 = await createTestNode(false, 3);
        await Test.withReadonlyContext(async context => {
            const testFilter = async (
                filter: NodeQueryFilter<fixtures.importedNodes.TestVitalReferenceParent>,
                expected: integer[],
            ): Promise<void> => {
                const nodes1 = await context.query(TestVitalReferenceParent, { filter }).toArray();
                assert.deepEqual(
                    nodes1.map(n => n._id),
                    expected,
                );
            };
            await testFilter({ optionalVitalRef: { _ne: null } }, [id1]);
            await testFilter({ optionalVitalRef: null }, [id2, id3]);
            await testFilter({ optionalVitalRef: { code: 'O1' } }, [id1]);
            await testFilter({ optionalVitalRef: { code: 'O2' } }, []);
            await testFilter({ mandatoryVitalRef: { code: 'M1' } }, [id1]);
            await testFilter({ mandatoryVitalRef: { code: { _ne: 'M1' } } }, [id2, id3]);
        });
    });

    it('can replace a reference in update', async () => {
        await createTestNode(true);
        // check that mandatory vital exists in db
        await Test.readonly(async context => {
            assert.isNotNull(await context.tryRead(TestVitalReferenceChildMandatory, { code: 'M1' }));
        });
        await Test.committed(async context => {
            const parent = await readParentForUpdate(context);
            // Here, we do not update the existing reference.
            // Instead we create a new reference and we assign it
            const newVitalRef = await context.create(TestVitalReferenceChildMandatory, { code: 'M2' });
            await parent.$.set({
                code: 'P2',
                mandatoryVitalRef: newVitalRef,
            });
            await parent.$.save();
        });
        await checkTestNode({
            code: 'P2',
            mandatoryVitalRef: { code: 'M2' },
            optionalVitalRef: { code: 'O1', nonVitalRef: nonVitalReferenceData[0] },
        });
        // check that original mandatory vital has been deleted
        await Test.readonly(async context => {
            assert.isNull(await context.tryRead(TestVitalReferenceChildMandatory, { code: 'M1' }));
        });
    });

    it('fires control event on vital reference', () =>
        Test.uncommitted(async context => {
            const parent = await context.create(TestVitalReferenceParent, {
                code: 'P1',
                mandatoryVitalRef: {
                    code: 'M1',
                    text: 'bad text',
                },
            });
            await assert.isRejected(parent.$.save(), /was not created/);
            assert.deepEqual(
                context.diagnoses.map(diag => diag.toString()),
                ["[mandatoryVitalRef.text]: error: value must not be equal to 'bad text'"],
            );
        }));

    it('payload exports vital references', async () => {
        await createTestNode(true);
        await Test.readonly(async context => {
            const parent = await context.read(TestVitalReferenceParent, { code: 'P1' });

            const payload = await parent.$.payload({ withIds: true, withoutCustomData: true });
            payload.mandatoryVitalRef = omit(payload.mandatoryVitalRef, ['_id', '_sourceId']);
            payload.optionalVitalRef = omit(payload.optionalVitalRef, ['_id', '_sourceId']);
            payload.optionalVitalRef.nonVitalRef = omit(payload.optionalVitalRef.nonVitalRef, ['_id', '_sourceId']);
            assert.deepEqual(payload, {
                _id: parent._id,
                _sourceId: '',
                code: 'P1',
                mandatoryVitalRef: {
                    code: 'M1',
                    text: 'Child mandatory 1',
                    parent: { _id: parent._id },
                },
                nonVitalRef: null,
                optionalVitalRef: {
                    code: 'O1',
                    text: 'Child optional 1',
                    nonVitalRef: {},
                    parent: { _id: parent._id },
                },
            });
        });
    });

    after(() => restoreTables());
});
