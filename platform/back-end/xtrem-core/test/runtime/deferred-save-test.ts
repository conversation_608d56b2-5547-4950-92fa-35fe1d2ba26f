import { assert } from 'chai';
import { Context, Node, StaticThis, Test, decorators, integer } from '../../index';
import { createApplicationWithApi, initTables, restoreTables, setup } from '../fixtures/index';

@decorators.node<TestDeferredSave1>({
    isPublished: true,
    storage: 'sql',
    async saveEnd() {
        const node2 = await this.$.context.tryRead(TestDeferredSave2, { id: await this.id }, { forUpdate: true });
        if (node2 == null) return;
        await node2.$.set({ val: (await this.val) + 1 });
        await node2.$.save({ deferred: true });
    },
    indexes: [
        {
            orderBy: { id: 1 },
            isUnique: true,
        },
    ],
})
export class TestDeferredSave1 extends Node {
    @decorators.integerProperty<TestDeferredSave1, 'id'>({
        isStored: true,
        isPublished: true,
    })
    readonly id: Promise<integer>;

    @decorators.integerProperty<TestDeferredSave1, 'val'>({
        isStored: true,
        isPublished: true,
    })
    readonly val: Promise<integer>;
}

@decorators.node<TestDeferredSave2>({
    isPublished: true,
    storage: 'sql',
    indexes: [
        {
            orderBy: { id: 1 },
            isUnique: true,
        },
    ],
    async saveEnd() {
        if (await this.forInfiniteLoop) {
            const node1 = await this.$.context.tryRead(TestDeferredSave1, { id: await this.id }, { forUpdate: true });
            if (node1 == null) return;
            await node1.$.set({ val: (await this.val) + 1 });
            // Launch an infinite loop: node1.saveEnd will trigger node2.saveEnd, which will trigger node1.saveEnd, etc.
            await node1.$.save({ deferred: true });
        }
    },
})
export class TestDeferredSave2 extends Node {
    @decorators.integerProperty<TestDeferredSave2, 'id'>({
        isStored: true,
        isPublished: true,
    })
    readonly id: Promise<integer>;

    @decorators.booleanProperty<TestDeferredSave2, 'forInfiniteLoop'>({
        isStored: true,
        isPublished: true,
        defaultValue: false,
    })
    readonly forInfiniteLoop: Promise<boolean>;

    @decorators.integerProperty<TestDeferredSave2, 'val'>({
        isStored: true,
        isPublished: true,
    })
    readonly val: Promise<integer>;
}

describe('deferred save', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi({
                nodes: { TestDeferredSave1, TestDeferredSave2 },
            }),
        });
        await initTables([
            { nodeConstructor: TestDeferredSave1, data: [] },
            { nodeConstructor: TestDeferredSave2, data: [] },
        ]);
    });

    async function getValueOfRecord<T extends Node>(
        context: Context,
        clas: StaticThis<T>,
        id: integer,
    ): Promise<integer | undefined> {
        const factory = Test.application.getFactoryByConstructor(clas);
        const result = await context.executeSql<{ val: integer }[]>(
            `SELECT val FROM ${context.schemaName}.${factory.tableName} WHERE id = $1`,
            [id],
        );

        if (result.length === 0) return undefined;

        return result[0].val;
    }

    it('check simple deferred save', async () => {
        const id = 1;
        await Test.withCommittedContext(async context => {
            const node = await context.create(TestDeferredSave1, { id, val: 1 });
            await node.$.save({ deferred: true });
            // The save was done in deferred mode, so the node should not have been saved yet: it should only be saved to database at the end of the transaction
            assert.isUndefined(
                await getValueOfRecord(context, TestDeferredSave1, id),
                'The node should not have been saved to database yet',
            );
        });
        // Make sure the node was saved to database at the end of the previous transaction
        await Test.withReadonlyContext(async context => {
            assert.equal(
                await getValueOfRecord(context, TestDeferredSave1, id),
                1,
                'The node was not written to database',
            );
        });
    });

    it('check cascaded deferred save', async () => {
        const id = 2;
        await Test.withCommittedContext(async context => {
            const node1 = await context.create(TestDeferredSave1, { id, val: 1 });
            await node1.$.save();
            const node2 = await context.create(TestDeferredSave2, { id, val: 1 });
            await node2.$.save();
        });
        await Test.withCommittedContext(async context => {
            const node1 = await context.read(TestDeferredSave1, { id }, { forUpdate: true });
            await node1.$.set({ val: 3 });
            await node1.$.save({ deferred: true });
        });

        // When the node1 was saved, it triggered the saveEnd method, which updated the node2 and should have set its value to (val+1, i.e. 4)

        // Make sure the node was saved to database at the end of the previous transaction
        await Test.withReadonlyContext(async context => {
            assert.equal(await getValueOfRecord(context, TestDeferredSave1, id), 3, 'The node1 was not updated');
            assert.equal(
                await getValueOfRecord(context, TestDeferredSave2, id),
                4,
                'The node2 was not updated (should have been done by node1.saveEnd)',
            );
        });
    });

    it('detect infinite cascaded deferred saves', async () => {
        const id = 3;
        // Note: assert.throws does not work with async functions
        try {
            await Test.withCommittedContext(async context => {
                const node1 = await context.create(TestDeferredSave1, { id, val: 1 });
                await node1.$.save();
                const node2 = await context.create(TestDeferredSave2, { id, val: 1, forInfiniteLoop: true });
                // This save should fail because it would trigger an infinite loop
                // node2.saveEnd would update node1, which would trigger node1.saveEnd, which would update node2, etc.
                await node2.$.save();
            });
            throw new Error('Infinite loop was not detected');
        } catch (e) {
            if (e.message === 'Error: Loop detected in deferred saves') throw new Error('EEEEEEEEEEEEE');
        }
    });

    after(() => restoreTables());
});
