import { assert } from 'chai';
import { datetime, decorators, Node, Reference, Test } from '../../index';
import { codeDataType, descriptionDataType } from '../fixtures/data-types/data-types';
import { createApplicationWithApi, initTables, restoreTables, setup } from '../fixtures/index';

/** DECLARE SOME TYPES */
@decorators.node<TestCustomer>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestJoinsCustomer',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
class TestCustomer extends Node {
    @decorators.stringProperty<TestCustomer, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestCustomer, 'address'>({
        isPublished: true,
        isStored: true,
        node: () => TestAddress,
    })
    readonly address: Reference<TestAddress>;
}

@decorators.node<TestAddress>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestJoinsAddress',
    indexes: [{ orderBy: { code: 1, addressType: 1 }, isUnique: true }],
})
class TestAddress extends Node {
    @decorators.stringProperty<TestAddress, 'code'>({
        dataType: () => codeDataType,
        isStored: true,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestAddress, 'text'>({
        dataType: () => descriptionDataType,
        isStored: true,
    })
    readonly text: Promise<string>;

    @decorators.referenceProperty<TestAddress, 'postalCode'>({
        isPublished: true,
        isStored: true,
        node: () => TestPostalCode,
    })
    readonly postalCode: Reference<TestPostalCode>;

    @decorators.integerProperty<TestAddress, 'addressType'>({ isStored: true })
    readonly addressType: Promise<number>;

    @decorators.datetimeProperty<TestAddress, 'dateTime'>({
        isPublished: true,
        isNullable: true,
        isStored: true,
    })
    readonly dateTime: Promise<datetime | null>;
}

@decorators.node<TestPostalCode>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestJoinsPostalCode',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
class TestPostalCode extends Node {
    @decorators.stringProperty<TestPostalCode, 'code'>({
        dataType: () => codeDataType,
        isStored: true,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestPostalCode, 'city'>({
        dataType: () => descriptionDataType,
        isStored: true,
    })
    readonly city: Promise<string>;
}

/** DECLARE SOME DATA */
const customerData = [
    {
        _id: 1,
        code: 'CUST1',
        address: 1,
    },
    {
        _id: 2,
        code: 'CUST2',
        address: 2,
    },
];

const addressData = [
    {
        _id: 1,
        code: 'ADDR1',
        text: 'Address #1',
        postalCode: 1,
        addressType: 1,
    },
    {
        _id: 2,
        code: 'ADDR2',
        text: 'Address #2',
        postalCode: 2,
        addressType: 2,
        dateTime: datetime.now(),
    },
    {
        _id: 3,
        code: 'ADDR3',
        text: 'Address #3',
        postalCode: 3,
        addressType: 3,
    },
];

const postalCode = [
    {
        _id: 1,
        code: '33160',
        city: 'Saint Medard en Jalles',
    },
    {
        _id: 2,
        code: '33680',
        city: 'Lacanau',
    },
    {
        _id: 3,
        code: '33700',
        city: 'Merignac',
    },
];

describe('sql joins tests', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi({ nodes: { TestPostalCode, TestAddress, TestCustomer } }),
        });
        await initTables([
            { nodeConstructor: TestPostalCode, data: postalCode },
            { nodeConstructor: TestAddress, data: addressData },
            { nodeConstructor: TestCustomer, data: customerData },
        ]);
    });
    it('simple join : hit (raw value filter)', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestAddress, {
                    filter: {
                        postalCode: 2, // Note : no left join should be generated for this request
                    },
                })
                .toArray();
            assert.equal(result.length, 1);
            assert.equal(await result[0].code, 'ADDR2');
        }));
    it('simple join : hit (object filter)', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestAddress, {
                    filter: {
                        postalCode: {
                            city: 'Lacanau',
                        },
                    },
                })
                .toArray();
            assert.equal(result.length, 1);
            assert.equal(await result[0].code, 'ADDR2');
        }));
    it('simple join : miss', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestAddress, {
                    filter: {
                        postalCode: {
                            city: 'Le porge',
                        },
                    },
                })
                .toArray();
            assert.equal(result.length, 0);
        }));
    it('simple join : with direct join value', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestCustomer, {
                    filter: {
                        address: 2,
                    },
                })
                .toArray();
            assert.equal(result.length, 1);
            assert.equal(await result[0].code, 'CUST2');
        }));
    it('compound join : hit', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestCustomer, {
                    filter: {
                        address: {
                            text: 'Address #2',
                        },
                    },
                })
                .toArray();
            assert.equal(result.length, 1);
            assert.equal(await result[0].code, 'CUST2');
        }));
    it('nested joins : hit', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestCustomer, {
                    filter: {
                        address: {
                            postalCode: {
                                city: 'Lacanau',
                            },
                        },
                    },
                })
                .toArray();
            assert.equal(result.length, 1);
            assert.equal(await result[0].code, 'CUST2');
        }));
    it('nested joins (2) : hit', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestCustomer, {
                    filter: {
                        code: 'CUST2',
                        address: {
                            postalCode: {
                                city: 'Lacanau',
                            },
                        },
                    },
                })
                .toArray();
            assert.equal(result.length, 1);
            assert.equal(await result[0].code, 'CUST2');
        }));
    it('nested joins : miss', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestCustomer, {
                    filter: {
                        code: 'CUST1',
                        address: {
                            postalCode: {
                                city: 'Lacanau',
                            },
                        },
                    },
                })
                .toArray();
            assert.equal(result.length, 0);
        }));
    it('nested joins (2) : miss', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestCustomer, {
                    filter: {
                        address: {
                            postalCode: {
                                city: 'Le porge',
                            },
                        },
                    },
                })
                .toArray();
            assert.equal(result.length, 0);
        }));
    it('implicit and : hit', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestCustomer, {
                    filter: {
                        address: {
                            postalCode: {
                                _in: [1, 2],
                                _ne: 1,
                                _nin: [3, 28],
                            },
                        },
                    },
                })
                .toArray();
            assert.equal(result.length, 1);
            assert.equal(await result[0].code, 'CUST2');
        }));
    it('Can set a valid reference', () =>
        Test.uncommitted(async context => {
            const address = await context.read(TestAddress, { code: 'ADDR2', addressType: 2 });
            const customer = await context.read(TestCustomer, { code: 'CUST1' }, { forUpdate: true });
            await customer.$.set({ address });
            assert.strictEqual(await (await customer.address).code, 'ADDR2');
        }));
    it('Join test on null date : _eq', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestCustomer, {
                    filter: {
                        address: {
                            dateTime: { _eq: null },
                        },
                    },
                })
                .toArray();
            assert.equal(result.length, 1);
            assert.equal(await result[0].code, 'CUST1');
        }));
    it('Join test on null date : _ne', () =>
        Test.readonly(async context => {
            const result = await context
                .query(TestCustomer, {
                    filter: {
                        address: {
                            dateTime: { _ne: null },
                        },
                    },
                })
                .toArray();
            assert.equal(result.length, 1);
            assert.equal(await result[0].code, 'CUST2');
        }));
    after(() => restoreTables());
});
