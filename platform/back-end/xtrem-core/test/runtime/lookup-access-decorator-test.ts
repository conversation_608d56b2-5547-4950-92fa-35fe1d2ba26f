import { assert } from 'chai';
import { Test } from '../../index';
import { decorators, integer, Node, StringDataType } from '../../lib';
import { createApplicationWithApi, setup } from '../fixtures/index';

@decorators.node<TestSomeNode>({
    isPublished: true,
    storage: 'sql',
})
class TestSomeNode extends Node {
    @decorators.stringProperty<TestSomeNode, 'textAvailable'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 30 }),
        lookupAccess: true,
    })
    readonly textAvailable: Promise<string>;

    @decorators.integerProperty<TestSomeNode, 'numberAvailable'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
    })
    readonly numberAvailable: Promise<integer>;

    @decorators.stringProperty<TestSomeNode, 'textNoAccess'>({
        isStored: true,
        isPublished: true,
        dataType: () => new StringDataType({ maxLength: 30 }),
    })
    readonly textNoAccess: Promise<string>;

    @decorators.integerProperty<TestSomeNode, 'numberNoAccess'>({
        isStored: true,
        isPublished: true,
    })
    readonly numberNoAccess: Promise<integer>;
}

describe('anonymizeMethod validations', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi({
                nodes: {
                    TestSomeNode,
                },
            }),
        });
    });

    it('lookupAccess default values', () => {
        const factory = Test.application.getFactoryByConstructor(TestSomeNode);
        factory.properties.forEach(prop => {
            switch (prop.name) {
                case '_id':
                case '_customData':
                case '_createUser':
                case '_createStamp':
                case '_updateUser':
                case '_updateStamp':
                case 'numberAvailable':
                case 'textAvailable':
                    assert.equal(prop.lookupAccess, true, `${prop.name} lookupAccess is available`);
                    break;
                default:
                    assert.equal(prop.lookupAccess, false, `${prop.name} lookupAccess not available`);
            }
        });
    });
});
