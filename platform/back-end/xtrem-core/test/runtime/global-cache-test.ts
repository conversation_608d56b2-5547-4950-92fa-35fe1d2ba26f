import { assert } from 'chai';
import { promisify } from 'util';
import { Collection, decorators, Node, Reference, Test } from '../../index';
import { GlobalCacheCounters } from '../../lib/cache';
import { Context } from '../../lib/runtime/context';
import { SqlQuery } from '../../lib/sql/mapper';
import { codeDataType, descriptionDataType } from '../fixtures/data-types/data-types';
import {
    createApplicationWithApi,
    initTables,
    restoreConfigManager,
    restoreTables,
    setup,
    stubConfigManager,
} from '../fixtures/index';

let computationCounter = 0;

@decorators.node<TestCachedNodeParent>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestCachedNodeParent',
    isCached: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
class TestCachedNodeParent extends Node {
    @decorators.stringProperty<TestCachedNodeParent, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestCachedNodeParent, 'text'>({
        dataType: () => descriptionDataType,
        isStored: true,
    })
    readonly text: Promise<string>;

    @decorators.collectionProperty<TestCachedNodeParent, 'children'>({
        isPublished: true,
        node: () => TestCachedNodeChild,
        reverseReference: 'parent',
        isVital: true,
    })
    readonly children: Collection<TestCachedNodeChild>;

    @decorators.referenceProperty<TestCachedNodeParent, 'reference'>({
        isPublished: true,
        isStored: true,
        node: () => TestCachedNodeRef,
        isNullable: true,
    })
    readonly reference: Reference<TestCachedNodeRef | null>;
}

@decorators.node<TestCachedNodeRef>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestCachedNodeRef',
    isCached: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
class TestCachedNodeRef extends Node {
    @decorators.stringProperty<TestCachedNodeRef, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestCachedNodeRef, 'text'>({
        dataType: () => descriptionDataType,
        isStored: true,
    })
    readonly text: Promise<string>;
}

@decorators.node<TestCachedNodeChild>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestCachedNodeChild',
    isCached: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
    isVitalCollectionChild: true,
})
class TestCachedNodeChild extends Node {
    @decorators.stringProperty<TestCachedNodeChild, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestCachedNodeChild, 'parent'>({
        isPublished: true,
        isStored: true,
        node: () => TestCachedNodeParent,
        isVitalParent: true,
    })
    readonly parent: Reference<TestCachedNodeParent>;

    @decorators.stringProperty<TestCachedNodeChild, 'text'>({
        dataType: () => descriptionDataType,
        isStored: true,
    })
    readonly text: Promise<string>;
}

const cachedParentData: any[] = [];
const cachedRefData: any[] = [];
const cachedChildData: any[] = [];
for (let i = 0; i < 10; i += 1) {
    cachedParentData.push({
        _id: i + 1,
        code: `PARENT_${i}`,
        text: `parent_${i}`,
        reference: 1 + Math.floor(i / 2),
    });
    for (let j = 0; j < 4; j += 1) {
        cachedChildData.push({ code: `CHILD_${i}_${j}`, parent: i + 1, text: `child_${i}_${j}` });
    }
}
for (let i = 0; i < cachedParentData.length / 2; i += 1) {
    cachedRefData.push({ _id: i + 1, code: `REF_${i}`, text: `ref_${i}` });
}

function computeValue(): { value: number } {
    computationCounter += 1;
    return { value: computationCounter };
}

const category = 'cate.gory';
function getCachedValue(context: Context, key: string): Promise<number | undefined> {
    return context.getCachedValue({
        category,
        key,
        getValue: computeValue,
        cacheInMemory: true,
        ttlInSeconds: 1,
    });
}

async function sleep(duration: number): Promise<void> {
    await promisify(setTimeout)(duration);
}

describe('globalCache tests', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi({
                nodes: { TestCachedNodeParent, TestCachedNodeRef, TestCachedNodeChild },
            }),
        });
        await initTables([
            { nodeConstructor: TestCachedNodeRef, data: cachedRefData },
            { nodeConstructor: TestCachedNodeParent, data: cachedParentData },
            { nodeConstructor: TestCachedNodeChild, data: cachedChildData },
        ]);
        stubConfigManager({
            textStreamContentTypes: ['application/json'],
        });
    });

    it('check expiration', () =>
        Test.withReadonlyContext(async context => {
            // The value was not cached yet, computeValue() should have been invoked
            assert.equal(await getCachedValue(context, 'test'), 1);
            // The value is cached, computeValue() should NOT have been invoked
            assert.equal(await getCachedValue(context, 'test'), 1);
            // The cached value should have expired : computeValue() should have been invoked
            await sleep(1500);
            assert.equal(await getCachedValue(context, 'test'), 2);
        }));

    it('objects can be stored in GlobalCache', () =>
        Test.withReadonlyContext(async context => {
            const activePackages = () =>
                context.getCachedValue({
                    category,
                    key: 'objects',
                    getValue: () => {
                        return {
                            value: [
                                { package: '@sage/xtrem-core' },
                                { package: '@sage/xtrem-show-case' },
                                { package: '@sage/xtrem-show-case-bundle' },
                            ],
                        };
                    },

                    cacheInMemory: true,
                    ttlInSeconds: 1,
                });
            assert.equal((await activePackages()).length, 3);
        }));

    it('can invalidate cache', () =>
        Test.withReadonlyContext(async context => {
            // Store sth in memory - it will be stored in the factory cache AND in the global cache
            // but as we are using a separated context (context2), the factory cache will be cleaned when exiting the withUncommittedContext block
            const globalCache = Test.application.globalCache;
            await Test.withUncommittedContext(context2 =>
                context2.getCachedValue({
                    category: 'test',
                    key: 'toInvalidate',
                    getValue: () => ({ value: 'toBeDeleted' }),
                    cacheInMemory: true,
                    ttlInSeconds: 3600,
                }),
            );

            const valBeforeClear = await context.getCachedValue({
                category: 'test',
                key: 'toInvalidate',
                getValue: () => {
                    // This callback should not be called as the value should be fetched from the global cache
                    throw new Error('The value could not be fetched from the global cache');
                },
                cacheInMemory: true,
                ttlInSeconds: 3600,
            });
            // The value should be fetched from the global cache
            assert.equal(valBeforeClear, 'toBeDeleted');

            globalCache.clearAll();
            context.cache.clear();

            const valAfterClear = await context.getCachedValue({
                category: 'test',
                key: 'toInvalidate',
                getValue: () => ({ value: 'newVal' }),
                cacheInMemory: true,
                ttlInSeconds: 3600,
            });
            assert.equal(valAfterClear, 'newVal');
        }));

    it('Check statistics', async () => {
        const globalCache = Test.application.globalCache;
        globalCache.clearAll();
        assert.deepEqual(globalCache.getCounters(), {
            queryCount: 0,
            missCount: 0,
            contextHitCount: 0,
            memoryHitCount: 0,
            memoryCost: 0,
            itemsCount: 0,
        });
        await Test.withReadonlyContext(async context => {
            const parent = await context.read(TestCachedNodeParent, { code: 'PARENT_0' });
            assert.deepEqual(globalCache.getCounters(), {
                // a query has been fired to get PARENT_0 from the global cache
                queryCount: 2,
                // this query failed (the cache has been invalidated)
                missCount: 2,
                contextHitCount: 0,
                memoryHitCount: 0,
                // After being loaded from db, the PARENT_0 has been cached
                itemsCount: 2,
                // Cost of PARENT_0
                memoryCost: 499,
            });
            // Force the lazy-loading of the reference REF_0
            assert.equal(await (await parent.reference)?.text, 'ref_0');
            assert.deepEqual(globalCache.getCounters(), {
                // a query has been fired to get REF_0 from the global cache
                queryCount: 4,
                // this query failed (REF_0 was not yet in the cache)
                missCount: 4,
                contextHitCount: 0,
                memoryHitCount: 0,
                // After being loaded from db, REF_0 has been cached
                itemsCount: 4,
                // Cost(PARENT_0) + Cost(REF_0)
                memoryCost: 954,
            });
            // Force the lazy-loading of the collection
            assert.equal(await parent.children.length, 4);
            // The cache should have been used to load the children
            assert.deepEqual(globalCache.getCounters(), {
                // a query has been fired to get REF_0 from the global cache
                queryCount: 5,
                // this query failed (REF_0 was not yet in the cache)
                missCount: 5,
                contextHitCount: 0,
                memoryHitCount: 0,
                // After being loaded from db, REF_0 has been cached
                itemsCount: 5,
                // Cost(PARENT_0) + Cost(REF_0) + Cost(children)
                memoryCost: 2139,
            });
        });
        await Test.withReadonlyContext(async context => {
            const parent = await context.read(TestCachedNodeParent, { code: 'PARENT_0' });
            assert.deepEqual(globalCache.getCounters(), {
                // a query has been fired to get PARENT_0 from the global cache
                queryCount: 6,
                missCount: 5,
                // the query was successfull
                contextHitCount: 0,
                memoryHitCount: 1,
                itemsCount: 5,
                // Cost(PARENT_0) + Cost(REF_0) + Cost(children)
                memoryCost: 2139,
            });
            // Force the lazy-loading of the reference REF_0
            assert.equal(await (await parent.reference)?.text, 'ref_0');
            assert.deepEqual(globalCache.getCounters(), {
                // a query has been fired to get REF_0 from the global cache
                queryCount: 7,
                missCount: 5,
                // the query was successful
                contextHitCount: 0,
                memoryHitCount: 2,
                itemsCount: 5,
                // Cost(PARENT_0) + Cost(REF_0) + Cost(children)
                memoryCost: 2139,
            });
        });
    });

    it('should intern references to globalCache', async () => {
        const globalCache = Test.application.globalCache;
        globalCache.clearAll();
        await Test.withReadonlyContext(async context => {
            const parent1 = await context.read(TestCachedNodeParent, { code: 'PARENT_0' });
            assert.deepEqual(globalCache.getCounters(), {
                // a query has been fired to get PARENT_0 from the global cache
                queryCount: 2,
                // this query failed (the cache has been invalidated)
                missCount: 2,
                contextHitCount: 0,
                memoryHitCount: 0,
                memoryCost: 499,
                itemsCount: 2,
            });
            // Force the lazy-loading of the reference REF_0
            assert.equal(await (await parent1.reference)?.text, 'ref_0');
            assert.deepEqual(globalCache.getCounters(), {
                // One more query should have been done to get REF_0
                queryCount: 4,
                // ... and it should have failed
                missCount: 4,
                contextHitCount: 0,
                memoryHitCount: 0,
                memoryCost: 954,
                itemsCount: 4, // PARENT_0 + REF_0
            });
        });
        await Test.withReadonlyContext(async context => {
            // Note : PARENT_0 and PARENT_1 share the same REF (REF_0)
            const parent1 = await context.read(TestCachedNodeParent, { code: 'PARENT_0' });
            // Force the lazy loading of parent1.reference.text
            assert.equal(await (await parent1.reference)?.text, 'ref_0');

            const parent2 = await context.read(TestCachedNodeParent, { code: 'PARENT_1' });
            const sqlReqCountBefore = SqlQuery.internalStatistics.queryCount;

            // Force the lazy-loading of the reference REF_0
            assert.equal(await (await parent2.reference)?.text, 'ref_0');

            // No query should have been fired as we already have the REF_0 in the global cache
            assert.equal(SqlQuery.internalStatistics.queryCount, sqlReqCountBefore);

            assert.deepEqual(globalCache.getCounters(), {
                // One more query should have been done to get REF_0
                queryCount: 8,
                // ... and it should have succeeded
                contextHitCount: 0,
                memoryHitCount: 2,
                missCount: 6,
                itemsCount: 6, // PARENT_0 + PARENT_1 + REF_0
                memoryCost: 1453,
            } as GlobalCacheCounters);
        });
    });

    it('Can create/update/delete a cacheable factory', async () => {
        await Test.uncommitted(async context =>
            (await context.create(TestCachedNodeParent, { code: 'NEW_PARENT' })).$.save(),
        );
        await Test.committed(async context =>
            (await context.create(TestCachedNodeParent, { code: 'NEW_PARENT' })).$.save(),
        );
        await Test.uncommitted(async context => {
            const node = await context.read(TestCachedNodeParent, { code: 'PARENT_0' }, { forUpdate: true });
            await node.$.set({ text: 'new text' });
            await node.$.save();
        });
        await Test.uncommitted(async context => {
            const node = await context.read(TestCachedNodeParent, { code: 'PARENT_0' }, { forUpdate: true });
            await node.$.delete();
        });
    });

    it('invalidateCategory', () =>
        Test.withContext(async context => {
            const globalCache = context.application.globalCache;
            const value = await getCachedValue(context, 'value');
            // The value is cached, computeValue() should NOT have been invoked
            assert.equal(await getCachedValue(context, 'value'), value);
            await globalCache.invalidateCategory(context, category);
            context.cache.invalidateCategory(context, category);
            // The cached value was deleted : computeValue() should have been invoked
            assert.equal(await getCachedValue(context, 'value'), value! + 1);
        }));

    it('correctly invalidates cache entries when a joined table is modified', () =>
        Test.uncommitted(async context => {
            const queryMatchingTexts = (texts: string[]): Promise<string[]> =>
                context
                    .query(TestCachedNodeParent, {
                        filter: { reference: { text: { _in: texts } } },
                    })
                    .map(async r => (await r.$.payload()).code!)
                    .toArray();

            let results = await queryMatchingTexts(['ref_2', 'ref_3']);
            assert.deepEqual(results, ['PARENT_4', 'PARENT_5', 'PARENT_6', 'PARENT_7']);

            // Modify the text of reference REF_2
            let ref = await context.read(TestCachedNodeRef, { code: 'REF_2' }, { forUpdate: true });
            await ref.$.set({ text: 'text_new' });
            await ref.$.save();

            // The previous save did not modify TestCachedNodeParent but it should invalidate our
            // previous query result because TestCachedNodeRef was accessed by the query filter.
            results = await queryMatchingTexts(['ref_2', 'ref_3']);
            assert.deepEqual(results, ['PARENT_6', 'PARENT_7']);

            // restore the reference text value
            ref = await context.read(TestCachedNodeRef, { code: 'REF_2' }, { forUpdate: true });
            await ref.$.set({ text: 'ref_2' });
            await ref.$.save();

            // Query again and check that we get the same results as before the change
            results = await queryMatchingTexts(['ref_2', 'ref_3']);
            assert.deepEqual(results, ['PARENT_4', 'PARENT_5', 'PARENT_6', 'PARENT_7']);
        }));

    after(async () => {
        await restoreTables();
        restoreConfigManager();
    });
});

describe('globalCache memory tests', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi({
                nodes: {},
            }),
        });
        await initTables([]);
    });

    it('Test cached value', () =>
        Test.withContext(async context => {
            let value: string = await context.getCachedValue({
                category: 'test',
                key: 'value1',
                getValue: () => ({ value: '1' }),
                cacheInMemory: true,
                ttlInSeconds: 3600,
            });
            assert.equal(value, '1');

            value = await context.getCachedValue({
                category: 'test',
                key: 'value1',
                getValue: () => ({ value: 'Something else' }),
                cacheInMemory: true,
                ttlInSeconds: 3600,
            });
            assert.equal(value, '1');
        }));

    after(() => restoreTables());
});
