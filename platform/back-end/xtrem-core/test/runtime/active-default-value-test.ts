import { assert } from 'chai';
import { Reference, Test } from '../../index';
import { Node, decorators, integer, restoreTables } from '../../lib';
import { codeDataType } from '../fixtures/data-types/data-types';
import { createApplicationWithApi, initTables, setup } from '../fixtures/index';

@decorators.node<TestParent>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
})
export class TestParent extends Node {
    @decorators.stringProperty<TestParent, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestParent, 'ref'>({
        isPublished: true,
        isStored: true,
        node: () => TestReferenced,
    })
    readonly ref: Reference<TestReferenced>;

    @decorators.referenceProperty<TestParent, 'vitalRef'>({
        isPublished: true,
        isVital: true,
        node: () => TestReferencedVital,
        reverseReference: 'parent',
    })
    readonly vitalRef: Reference<TestReferencedVital>;
}

@decorators.node<TestReferenced>({
    isPublished: true,
    storage: 'sql',
    isCached: true,
    defaultsToSingleMatch: true,
})
export class TestReferenced extends Node {
    @decorators.integerProperty<TestReferenced, 'value'>({
        isPublished: true,
        isStored: true,
    })
    readonly value: Promise<integer>;

    @decorators.booleanProperty<TestReferenced, 'isActive'>({
        isPublished: true,
        isStored: true,
        defaultValue: true,
        provides: ['isActive'],
    })
    readonly isActive: Promise<boolean>;
}

@decorators.node<TestReferencedVital>({
    isPublished: true,
    storage: 'sql',
    isVitalReferenceChild: true,
})
export class TestReferencedVital extends Node {
    @decorators.referenceProperty<TestReferencedVital, 'parent'>({
        isStored: true,
        isVitalParent: true,
        node: () => TestParent,
    })
    readonly parent: Reference<TestParent>;

    @decorators.integerProperty<TestReferencedVital, 'value'>({
        isPublished: true,
        isStored: true,
    })
    readonly value: Promise<integer>;

    @decorators.booleanProperty<TestReferencedVital, 'isActive'>({
        isPublished: true,
        isStored: true,
        defaultValue: true,
        provides: ['isActive'],
    })
    readonly isActive: Promise<boolean>;
}

describe('Single active reference defaults value', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi({
                nodes: { TestParent, TestReferenced, TestReferencedVital },
            }),
        });
        await initTables([
            { nodeConstructor: TestParent, data: [] },
            {
                nodeConstructor: TestReferenced,
                data: [
                    { value: '1', isActive: true },
                    { value: '2', isActive: false },
                ],
            },
            { nodeConstructor: TestReferencedVital, data: [] },
        ]);
    });

    it('can create a parent that defaults reference values when only one active reference', () =>
        Test.withContext(async context => {
            const parent = await context.create(TestParent, { code: 'P1' }, { isOnlyForDefaultValues: true });

            assert.instanceOf(await parent.ref, TestReferenced);
            assert.equal((await parent.ref)._id, 1);
            assert.instanceOf(await parent.vitalRef, TestReferencedVital);

            await (() => parent.$.save())();

            const p = await context.read(TestParent, { _id: 1 });

            assert.deepEqual(await p.$.payload({ withIds: true, withoutCustomData: true }), {
                _id: 1,
                _sourceId: '',
                code: 'P1',
                ref: { _id: 1 },
                vitalRef: {
                    _id: 1,
                    _sourceId: '',
                    isActive: true,
                    parent: {
                        _id: 1,
                    },
                    value: 0,
                },
            });
        }));

    after(() => restoreTables());
});
