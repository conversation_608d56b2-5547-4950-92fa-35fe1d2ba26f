import { Config<PERSON>anager } from '@sage/xtrem-config';
import { assert } from 'chai';
import * as fs from 'fs';
import * as path from 'path';
import { Decompress } from '../../lib';
import { createApplicationWithApi } from '../fixtures';
import { TestDatatypes } from '../fixtures/nodes';

const folder = path.resolve(path.join(__dirname, '../../add-ons/@acme'));
const zipPath = path.join(folder, 'xtrem-add-on-test-one.zip');
const targetPath = path.join(folder, 'xtrem-add-on-test-one');

const configAddOnFolder = path.resolve(path.join(__dirname, '../../add-ons'));
const configFolder = path.join(configAddOnFolder, 'application');
const targetConfigPath = path.join(configFolder, 'xtrem-add-on-test-one');

describe('Add ons', () => {
    it('can create an application with one addOn (new node only)', async () => {
        await Decompress.decompressZipToFolder(zipPath, folder);
        const application = await createApplicationWithApi(
            {
                nodes: { TestDatatypes },
            },
            undefined,
        );

        const packages = application.getPackages();
        assert.equal(packages.length, 2);
        assert.deepEqual(
            packages.map(p => p.name),
            ['@sage/xtrem-core', '@acme/acme-add-on-one'],
        );
        const factories = application.getAllFactories();
        assert.deepEqual(
            factories.map(f => f.name),
            ['TestDatatypes', 'TestUser', 'TestSysVendor', 'SysGlobalLock', 'AddOnOneDatatypes'],
        );

        assert.equal(packages.find(pack => pack.name === '@acme/acme-add-on-one')?.dir, targetPath);

        const serviceOptions = Object.values(application.serviceOptionsByName);
        assert.deepEqual(
            serviceOptions.map(s => `${s.packageName}/${s.name}`),
            ['@acme/acme-add-on-one/addOnServiceOption1'],
        );
        fs.rmSync(targetPath, { recursive: true, force: true });
        fs.rmSync(path.join(configAddOnFolder, 'node_modules'), { recursive: true, force: true });
    });

    it('can create an application with one addOn from config path (new node only)', async () => {
        await Decompress.decompressZipToFolder(zipPath, configFolder);
        ConfigManager.load(__dirname);
        ConfigManager.current.addOns = { folder: configAddOnFolder };
        const application = await createApplicationWithApi(
            {
                nodes: { TestDatatypes },
            },
            undefined,
        );

        const packages = application.getPackages();
        assert.equal(packages.length, 2);
        assert.deepEqual(
            packages.map(p => p.name),
            ['@sage/xtrem-core', '@acme/acme-add-on-one'],
        );
        const factories = application.getAllFactories();
        assert.deepEqual(
            factories.map(f => f.name),
            ['TestDatatypes', 'TestUser', 'TestSysVendor', 'SysGlobalLock', 'AddOnOneDatatypes'],
        );

        assert.equal(packages.find(pack => pack.name === '@acme/acme-add-on-one')?.dir, targetConfigPath);

        const serviceOptions = Object.values(application.serviceOptionsByName);
        assert.deepEqual(
            serviceOptions.map(s => `${s.packageName}/${s.name}`),
            ['@acme/acme-add-on-one/addOnServiceOption1'],
        );
        delete ConfigManager.current.addOns;
        fs.rmSync(targetConfigPath, { recursive: true, force: true });
        fs.rmSync(path.join(configAddOnFolder, 'node_modules'), { recursive: true, force: true });
    });
});
