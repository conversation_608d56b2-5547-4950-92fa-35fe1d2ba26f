PATH: XTREEM/Pending+Documentation/Transitioning+from+Etna+to+Xtrem

This page summarizes the main changes between Etna and Xtrem, to help Etna developers to adapt to the Xtrem framework.

# Database changes

The database schema has been simplified:

-   Shared tables primary key: \_id, a sequence number which is automatically incremented. This key is managed by the framework so you don't need to declare any property nor any index for it.
-   Non-shared tables primary key: \_id and \_tenant_id. `_tenant_id` is the unique identifer (nano id) allocated to each tenant.
-   In the graphql API, this property will be exposed as `_id`.
-   All references are on the relevant primary key (\_id or \_id+\_tenant*id). They are created as \_foreign keys* in the database.
-   Table names and column names are automatically derived from the node and property names, by converting them to snake_case (sales_order_line - default convention for PostgreSQL). This is built-in and you cannot override it.
-   `tableIndexes` has been renamed `indexes`.

# Alasql changes

-   CSV files must be named like their corresponding node: `sales-order-line.csv` has the data for the `SalesOrderLine` node which is implemented in `sales-order-line.ts`.

# TypeScript API changes

-   The \_id component of the primary key is exposed as `node.$.id` for now, not as `node._id`. We'll probably revisit this later and use `node._id` everywhere.
-   Filter operators are prefixed by `_` rather than `$`. So `_eq`, `_ne`, `_lte`, ...
-   No more joins in `referenceProperty` decorators (but still joins in `collectionProperty`).
-   No more `code`, `tableName`, `columnName` attributes in the decorators.
-   Controls on references can be expressed as a filter. See the _Reference Filters_ article.
-   Apr 30 - isVital flag is false by default on collections. In Etna it was automatically set to true.

We may drop a number of _special_ features, among which:

-   `scope` option in queries.
-   `composite` nodes
    Don't use these features

Localized properties have not yet been re-implemented. Define them as string properties, and leave a `// TODO: localize` comment.

# GraphQL API changes

Typical query:

```graphql
xtremSystem {
    user {
        query(first: 5, filter: "{ lastName: { _gte: 'C' } }") {
            firstName
            lastName
            email
        }
    }
}
```

Main changes are:

-   The top-level `sage { ... }` element has been removed
-   The `edges`, `pageInfo` and `totalCount` elements have been moved one level below, into a `query` element: `query { edges { ... }, pageInfo {... }, totalCount }`. The query options (`first`, `after`, `last`, `before`, `filter`, `orderBy`) are on the new `query` element.
-   `readById` is gone. The `read` operation now takes an `_id` parameter.

There is also a new `lookupQuery` element at the same level as `query` which will be used by _lookup fields_ in the UI.
