PATH: XTREEM/Pending+Documentation/Getting+Started+for+Platform+Developers

# Getting started with Xtrem Platform development

This article describes the steps to setup a new Xtrem platform development

## Cloning the repository

First step is to clone the xtrem-platform repository

```sh
git clone https://"url": "git://github.com/Sage-ERP-X3/xtrem.git
```

## Installing the npm packages

Then, install the npm packages

```sh
cd xtrem-platform
pnpm run clean:install
```

## Compiling the code

To compile the code, run:

```sh
pnpm run build
```

This step is optional (but recommended). The unit tests use `ts-node` and `ts-jest` so you can run them without compiling the code.

## Running the unit tests

To run all the unit tests, run:

```sh
pnpm test
```

You can also run this command into individual packages under `@sage`.

## Linting all the code

To lint all the files, run:

```sh
pnpm run lint
```
