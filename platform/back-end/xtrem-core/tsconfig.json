{"extends": "../../tsconfig-package.json", "include": ["index.ts", "lib/**/*", "test/**/*", "../xtrem-metrics/lib/newrelic"], "compilerOptions": {"outDir": "build", "rootDir": ".", "baseUrl": "."}, "references": [{"path": "../../shared/xtrem-async-helper"}, {"path": "../../front-end/xtrem-client"}, {"path": "../xtrem-config"}, {"path": "../../shared/xtrem-date-time"}, {"path": "../../shared/xtrem-decimal"}, {"path": "../../shared/xtrem-i18n"}, {"path": "../xtrem-log"}, {"path": "../xtrem-metrics"}, {"path": "../xtrem-postgres"}, {"path": "../../shared/xtrem-shared"}, {"path": "../xtrem-toposort"}, {"path": "../xtrem-ts-to-sql"}, {"path": "../xtrem-dts-bundle"}, {"path": "../xtrem-minify"}]}