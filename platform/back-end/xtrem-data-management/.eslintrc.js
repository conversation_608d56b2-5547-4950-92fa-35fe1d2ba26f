module.exports = {
    extends: ['../../.eslintrc-base.js'],
    plugins: ['@sage/xtrem', 'unused-imports'],
    parserOptions: {
        tsconfigRootDir: __dirname,
        project: 'tsconfig.json',
    },
    ignorePatterns: ['test/**/build'],
    overrides: [
        {
            files: ['lib/**/*.ts'],
            rules: {
                '@typescript-eslint/explicit-function-return-type': [
                    'error',
                    {
                        allowExpressions: true,
                        allowTypedFunctionExpressions: true,
                        allowHigherOrderFunctions: true,
                        allowDirectConstAssertionInArrowFunctions: true,
                    },
                ],
            },
        },
    ],
};
