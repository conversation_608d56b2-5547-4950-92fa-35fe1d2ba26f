{"extends": "../../tsconfig-package.json", "include": ["index.ts", "lib/**/*", "test/**/*"], "compilerOptions": {"outDir": "build", "rootDir": ".", "baseUrl": "."}, "references": [{"path": "../../cli/xtrem-cli-layers"}, {"path": "../../front-end/xtrem-client"}, {"path": "../xtrem-core"}, {"path": "../../shared/xtrem-decimal"}, {"path": "../xtrem-postgres"}, {"path": "../../shared/xtrem-shared"}, {"path": "../xtrem-toposort"}, {"path": "../eslint-plugin-xtrem"}, {"path": "../xtrem-dts-bundle"}, {"path": "../xtrem-minify"}]}