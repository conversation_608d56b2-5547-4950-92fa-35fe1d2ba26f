import { Context, decorators, integer, Node, Reference, ValidationSeverity } from '@sage/xtrem-core';
import { descriptionDataType } from '../data-types/_index';
import { TestOwner } from './test-pet-owner';

@decorators.node<TestAnimal>({
    isPublished: true,
    storage: 'sql',
    isAbstract: true,
    controlBegin(cx): void {
        cx.addDiagnose(ValidationSeverity.info, 'TestAnimal.controlBegin');
    },
})
export class TestAnimal extends Node {
    @decorators.stringProperty<TestAnimal, 'strFromAnimal'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly strFromAnimal: Promise<string>;

    @decorators.referenceProperty<TestAnimal, 'owner'>({
        isPublished: true,
        isStored: true,
        node: () => TestOwner,
        isNullable: true,
    })
    readonly owner: Reference<TestOwner | null>;

    async nonStaticMethod(param: integer): Promise<string> {
        return `nonStaticMethod strFromAnimal=${await this.strFromAnimal}, param=${param}`;
    }

    @decorators.mutation<typeof TestAnimal, 'staticOperation'>({
        isPublished: true,
        parameters: [{ name: 'param', type: 'integer' }],
        return: 'string',
    })
    static staticOperation(_context: Context, param: integer): string {
        return `staticOperation param=${param}`;
    }
}
