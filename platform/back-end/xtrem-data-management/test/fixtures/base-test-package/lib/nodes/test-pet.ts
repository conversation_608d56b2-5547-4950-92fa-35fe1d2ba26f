import { decorators, Node, Reference } from '@sage/xtrem-core';
import { name } from '../data-types/_index';
import { TestOwner } from './test-pet-owner';

@decorators.node<TestPet>({
    isPublished: true,
    storage: 'sql',
    isAbstract: true,
    indexes: [
        {
            orderBy: { name: +1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
})
export class TestPet extends Node {
    @decorators.stringProperty<TestPet, 'name'>({
        isPublished: true,
        isStored: true,
        dataType: () => name,
    })
    readonly name: Promise<string>;

    @decorators.referenceProperty<TestPet, 'owner'>({
        isPublished: true,
        isStored: true,
        node: () => TestOwner,
        isNullable: true,
    })
    readonly owner: Reference<TestOwner | null>;
}
