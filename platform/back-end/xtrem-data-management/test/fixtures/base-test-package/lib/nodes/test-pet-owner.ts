import { Collection, decorators, Node, Reference } from '@sage/xtrem-core';
import { name } from '../data-types/_index';
import { TestPet } from './test-pet';

@decorators.node<TestOwner>({
    isPublished: true,
    storage: 'sql',
    indexes: [
        {
            orderBy: { name: +1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
})
export class TestOwner extends Node {
    @decorators.stringProperty<TestOwner, 'name'>({
        isPublished: true,
        isStored: true,
        dataType: () => name,
    })
    readonly name: Promise<string>;

    @decorators.collectionProperty<TestOwner, 'pets'>({
        isPublished: true,
        node: () => TestPet,
        reverseReference: 'owner',
    })
    readonly pets: Collection<TestPet>;

    @decorators.referenceProperty<TestOwner, 'favoritePet'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        node: () => TestPet,
    })
    readonly favoritePet: Reference<TestPet | null>;
}
