/** @ignore */ /** */
import { decorators, nanoIdDataType, Node } from '@sage/xtrem-core';
import * as dataTypes from '../data-types/data-types';

@decorators.node<SysTenant>({
    isPublished: false,
    storage: 'sql',
    canCreate: false,
    canRead: true,
    canUpdate: true,
    canDeleteMany: false,
    isPlatformNode: true,
    isSharedByAllTenants: true,
    indexes: [
        {
            orderBy: { tenantId: +1 },
            isUnique: true,
        },
    ],
})
export class SysTenant extends Node {
    /**
     * the tenant's id
     */
    @decorators.stringProperty<SysTenant, 'tenantId'>({
        isPublished: true,
        isStored: true,
        dataType: () => nanoIdDataType,
    })
    readonly tenantId: Promise<string>;

    /**
     * the tenant's regular name
     */
    @decorators.stringProperty<SysTenant, 'name'>({
        isPublished: true,
        isStored: true,
        dataType: () => dataTypes.name,
    })
    readonly name: Promise<string>;
}
