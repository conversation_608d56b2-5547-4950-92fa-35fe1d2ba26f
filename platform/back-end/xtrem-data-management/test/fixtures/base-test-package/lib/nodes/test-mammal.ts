import { decorators, ValidationSeverity } from '@sage/xtrem-core';
import { descriptionDataType } from '../data-types/_index';
import { TestAnimal } from './test-animal';

@decorators.subNode<TestMammal>({
    extends: () => TestAnimal,
    isPublished: true,
    canDeleteMany: true,
    isAbstract: true,
    controlBegin(cx): void {
        cx.addDiagnose(ValidationSeverity.info, 'TestMammal.controlBegin');
    },
})
export class TestMammal extends TestAnimal {
    @decorators.stringProperty<TestMammal, 'strFromMammal'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly strFromMammal: Promise<string>;
}
