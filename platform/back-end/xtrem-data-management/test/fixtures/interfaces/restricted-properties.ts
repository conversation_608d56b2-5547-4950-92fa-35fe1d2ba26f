import { QueryPage, ValidationSeverity } from '@sage/xtrem-core';
import { fixtures } from '..';

export interface DiagnoseResult {
    message: string;
    path: string[];
    severity: ValidationSeverity;
}

export interface Result {
    data: { xtremCore: { testReferring: { query: QueryPage<InstanceType<typeof fixtures.nodes.TestReferring>> } } };
    extensions: {
        diagnoses: DiagnoseResult[];
    };
}
