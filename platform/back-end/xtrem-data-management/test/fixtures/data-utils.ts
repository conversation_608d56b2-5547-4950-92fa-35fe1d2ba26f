// tslint:disable: no-unused-expression
import { datetime } from '@sage/xtrem-core';
import { TableDefinition } from '@sage/xtrem-postgres';
import * as fs from 'fs';
import * as glob from 'glob';
import * as fsp from 'path';

/*
This class contains data used for tests. 

The following packages are available in /fixtures for the purpose of testing:

                 x3-root  
                    |
                x3-geography 
               /          \
        x3-geology         \
             |             x3-population
        x3-volcano         /
              \           / 
                x3-europe

Descriptors available for tests: 
- TestCity : Used to test basic behaviors of the driver and the csv-manager.
            Base package x3-geography. 
            Extended by x3-geology, x3-population and x3-europe.
- TestTexts : Mainly used to test JSON management.  
                    Base package x3-geography.
                    No extensions.
- TestLandscape,TestMountainLandscape,TestFlatLandscape,TestCoastalLandscape : Used to test subnodes and extension of base nodes.
            Base package x3-geography.
            Extended by x3-europe.
- Before running the tests, Data.createAllSplitFiles() will create all the csv files for available descriptors among
  layers/ and extension-layers/ directories, using the literal javascript objects Data.dataCity and Data.dataText. 
- Data.compareFileContents can be used to test the contents of a created or modified csv file. 
- After running the tests, csv files can be deleted by Data.deleteSplitFiles() and Data.deleteMergedFiles().
*/

export class Data {
    static readonly packagesRoot = 'test/fixtures';

    static readonly layerDir = 'layers';

    static readonly extLayerDir = 'extension-layers';

    static readonly basePackage = '@sage/x3-geography';

    static readonly layers = ['custom', 'setup'];

    static readonly descriptor = 'testCity';

    static readonly descriptors = ['testCity', 'testTexts'];

    static readonly packages = [
        '@sage/x3-geography',
        '@sage/x3-geology',
        '@sage/x3-volcano',
        '@sage/x3-population',
        '@sage/x3-europe',
        '@sage/x3-root',
    ];

    static readonly timeStampMarch = '2020-03-01T10:10:10.000Z';

    static readonly timeStampApril = '2020-04-01T10:10:10.000Z';

    static readonly fakeNow = '2020-03-15T10:10:10.000Z';

    static readonly stamps = [
        { string: '2019-11-19T17:40:20.000Z', number: '1574185220000' },
        { string: '2019-11-19T17:40:20.002Z', number: '1574185220002' },
        { string: '2019-11-19T17:40:20.003Z', number: '1574185220003' },
    ];

    static readonly systemColumns = [
        { name: '_update_tick', type: 'integer' },
        { name: '_create_stamp', type: 'datetime' },
        { name: '_create_user', type: 'integer' },
        { name: '_update_user', type: 'integer' },
        { name: '_tenant_id', type: 'string' },
    ];

    static readonly dataCity = {
        '@sage/x3-geography': {
            layers: {
                setup: [
                    {
                        code: 'C1',
                        full_name: 'Barcelona',
                        role: 'C1R0',
                        created_on: '2019-01-02',
                        stamp: Data.stamps[0].number,
                        _update_stamp: Data.timeStampMarch,
                        _id: '1',
                    },
                    {
                        code: 'C1',
                        full_name: 'Barcelona-in-April',
                        role: 'C1R0',
                        created_on: '2019-01-02',
                        stamp: Data.stamps[0].number,
                        _update_stamp: Data.timeStampApril,
                        _id: '1',
                    },
                ],
                custom: [
                    {
                        code: 'C2',
                        full_name: 'Paris',
                        role: 'C2R0',
                        created_on: '2019-01-01',
                        stamp: Data.stamps[2].number,
                        _update_stamp: Data.timeStampMarch,
                        _delete_stamp: Data.timeStampApril,
                        _id: '2',
                    },
                ],
            },
        },
        '@sage/x3-geology': {
            layers: {
                custom: [
                    {
                        code: 'C3',
                        has_volcano: 'Y',
                        full_name: 'Pompei',
                        role: 'C3R0',
                        _update_stamp: Data.timeStampMarch,
                        _id: '3',
                    },
                ],
            },
            'extension-layers': {
                setup: [
                    { has_volcano: 'N', _update_stamp: Data.timeStampMarch, _id: '1' },
                    { has_volcano: 'Y', _update_stamp: Data.timeStampApril, _id: '1' },
                ],

                custom: [{ has_volcano: 'N', _update_stamp: Data.timeStampMarch, _id: '2' }],
            },
        },
        '@sage/x3-volcano': {
            layers: {
                custom: [
                    {
                        code: 'C4',
                        full_name: 'Warsaw',
                        role: 'C4R0',
                        has_volcano: 'N',
                        _update_stamp: Data.timeStampMarch,
                        _id: '4',
                    },
                ],

                setup: [
                    {
                        code: 'C3',
                        full_name: 'Pompei-overriden',
                        role: 'C3R0',
                        has_volcano: 'Y',
                        _update_stamp: Data.timeStampMarch,
                        _id: '3',
                    },
                ],
            },
        },
        '@sage/x3-population': {
            'extension-layers': {
                setup: [
                    { demonym: 'Barcelonians', _update_stamp: Data.timeStampMarch, _id: '1' },
                    { demonym: 'Barcelonians-in-April', _update_stamp: Data.timeStampApril, _id: '1' },
                ],

                custom: [{ demonym: 'Parisians', _update_stamp: Data.timeStampMarch, _id: '2' }],
            },
        },
        '@sage/x3-europe': {
            layers: {
                setup: [
                    {
                        code: 'C5',
                        full_name: 'Pretoria',
                        role: 'C5R0',
                        created_on: '',
                        stamp: Data.stamps[1].number,
                        has_volcano: 'N',
                        demonym: 'Pretorians',
                        is_in_europe: 'N',
                        _update_stamp: Data.timeStampMarch,
                        _id: '5',
                    },
                    {
                        code: 'C6',
                        full_name: 'Tokyo',
                        role: 'C6R0',
                        created_on: '2019-06-03',
                        stamp: '',
                        has_volcano: 'Y',
                        demonym: 'Tokyoites',
                        is_in_europe: 'N',
                        _update_stamp: Data.timeStampMarch,
                        _id: '99',
                    },
                ],
            },
            'extension-layers': {
                setup: [
                    { is_in_europe: 'Y', _update_stamp: Data.timeStampMarch, _id: '1' },
                    { is_in_europe: 'Y', _update_stamp: Data.timeStampApril, _id: '1' },
                ],

                custom: [
                    { is_in_europe: 'Y', _update_stamp: Data.timeStampMarch, _id: '2' },
                    { is_in_europe: 'Y', _update_stamp: Data.timeStampMarch, _id: '4' },
                ],
            },
        },
    };

    static readonly dataTexts = {
        '@sage/x3-geography': {
            layers: {
                setup: [
                    {
                        code: 'country',
                        created_on: Date.parse('2020-04-01'),
                        stamp: datetime.parse('2020-04-01T10:10:10.000Z'),
                        text: { fra: 'pays', eng: 'country' },
                        history: [
                            { author: 'Eric', date: Date.parse('2020-04-01') },
                            { reviewer: 'Léa', date: Date.parse('2020-04-15') },
                        ],
                        _id: 1,
                    },
                    {
                        code: 'city',
                        created_on: Date.parse('2020-04-14'),
                        stamp: datetime.parse('2020-04-14T10:10:10.000Z'),
                        text: { fra: 'ville', eng: 'city' },
                        history: [{ author: 'Léa', date: Date.parse('2020-04-14') }],
                        _id: 2,
                    },
                    {
                        code: 'language',
                        created_on: Date.parse('2020-04-30'),
                        stamp: datetime.parse('2020-04-30T10:10:10.000Z'),
                        text: { fra: 'langue', eng: 'language' },
                        history: [{ author: 'Stéphane', date: Date.parse('2020-04-30') }],
                        _id: 3,
                    },
                ],
            },
        },
    };

    static readonly dataFlatLandscape = {
        '@sage/x3-geography': {
            layers: {
                custom: [
                    {
                        code: 'flat_landscape_1',
                        full_name: 'Flat Landscape 1',
                        biggest_town: 'Big Town 1',
                        _id: 1,
                    },
                    {
                        code: 'flat_landscape_2',
                        full_name: 'Flat Landscape 2',
                        biggest_town: 'Big Town 2',
                        _id: 2,
                    },
                    {
                        code: 'flat_landscape_3',
                        full_name: 'Flat Landscape 3',
                        biggest_town: 'Big Town 3',
                        _id: 3,
                    },
                ],
            },
        },
        '@sage/x3-europe': {
            layers: {
                custom: [
                    {
                        code: 'flat_landscape_101',
                        full_name: 'Flat Landscape 101',
                        biggest_town: 'Big Town 101',
                        is_in_europe: 'Y',
                        is_in_european_union: 'N',
                        _id: 101,
                    },
                    {
                        code: 'flat_landscape_102',
                        full_name: 'Flat Landscape 102',
                        biggest_town: 'Big Town 102',
                        is_in_europe: 'Y',
                        is_in_european_union: 'Y',
                        _id: 102,
                    },
                    {
                        code: 'flat_landscape_103',
                        full_name: 'Flat Landscape 103',
                        biggest_town: 'Big Town 103',
                        is_in_europe: 'Y',
                        is_in_european_union: 'Y',
                        _id: 103,
                    },
                ],
            },
        },
    };

    static readonly fullTablesRoot = fsp.join('test', 'fixtures', 'merged-test-data');

    static readonly pathMergedFileCity = fsp.join(
        'test',
        'fixtures',
        'merged-test-data',
        '@sage',
        'x3-geography',
        'test-city.csv',
    );

    static mergedRowsCity = [
        {
            $layer: 'setup',
            $package: '',
            code: 'C1',
            full_name: 'Barcelona',
            role: 'C1R0',
            created_on: '2019-01-02',
            stamp: Data.stamps[0].number,
            is_in_europe: 'Y',
            has_volcano: 'N',
            demonym: 'Barcelonians',
            unused: '',
            _update_stamp: Data.timeStampMarch,
            _delete_stamp: '',
            _id: '1',
            _layer: '',
        },
        {
            $layer: 'setup',
            $package: '',
            code: 'C1',
            full_name: 'Barcelona-in-April',
            role: 'C1R0',
            created_on: '2019-01-02',
            stamp: Data.stamps[0].number,
            is_in_europe: 'Y',
            has_volcano: 'Y',
            demonym: 'Barcelonians-in-April',
            unused: '',
            _update_stamp: Data.timeStampApril,
            _delete_stamp: '',
            _id: '1',
            _layer: '',
        },
        {
            $layer: 'custom',
            $package: '',
            code: 'C2',
            full_name: 'Paris',
            role: 'C2R0',
            created_on: '2019-01-01',
            stamp: Data.stamps[2].number,
            is_in_europe: 'Y',
            has_volcano: 'N',
            demonym: 'Parisians',
            unused: '',
            _update_stamp: Data.timeStampMarch,
            _delete_stamp: Data.timeStampApril,
            _id: '2',
            _layer: '',
        },
        {
            $layer: 'custom',
            $package: '@sage/x3-geology',
            code: 'C3',
            full_name: 'Pompei',
            role: 'C3R0',
            created_on: '',
            stamp: '',
            is_in_europe: '',
            has_volcano: 'Y',
            demonym: '',
            unused: '',
            _update_stamp: Data.timeStampMarch,
            _delete_stamp: '',
            _id: '3',
            _layer: '',
        },
        {
            $layer: 'custom',
            $package: '@sage/x3-volcano',
            code: 'C4',
            full_name: 'Warsaw',
            role: 'C4R0',
            created_on: '',
            stamp: '',
            is_in_europe: 'Y',
            has_volcano: 'N',
            demonym: '',
            unused: '',
            _update_stamp: Data.timeStampMarch,
            _delete_stamp: '',
            _id: '4',
            _layer: '',
        },
        {
            $layer: 'setup',
            $package: '@sage/x3-volcano',
            code: 'C3',
            full_name: 'Pompei-overriden',
            role: 'C3R0',
            created_on: '',
            stamp: '',
            is_in_europe: '',
            has_volcano: 'Y',
            demonym: '',
            unused: '',
            _update_stamp: Data.timeStampMarch,
            _delete_stamp: '',
            _id: '3',
            _layer: '',
        },
        {
            $layer: 'setup',
            $package: '@sage/x3-europe',
            code: 'C5',
            full_name: 'Pretoria',
            role: 'C5R0',
            created_on: '',
            stamp: Data.stamps[1].number,
            is_in_europe: 'N',
            has_volcano: 'N',
            demonym: 'Pretorians',
            unused: '',
            _update_stamp: Data.timeStampMarch,
            _delete_stamp: '',
            _id: '5',
            _layer: '',
        },
        {
            $layer: 'setup',
            $package: '@sage/x3-europe',
            code: 'C6',
            full_name: 'Tokyo',
            role: 'C6R0',
            created_on: '2019-06-03',
            stamp: '',
            is_in_europe: 'N',
            has_volcano: 'Y',
            demonym: 'Tokyoites',
            unused: '',
            _update_stamp: Data.timeStampMarch,
            _delete_stamp: '',
            _id: '99',
            _layer: '',
        },
    ];

    static readonly createDataCity = {
        '@sage/x3-geography': {
            folder: 'layers',

            setup: [
                {
                    code: 'C1',
                    full_name: 'Barcelona-in-April',
                    role: 'C1R0',
                    created_on: '2019-01-02',
                    stamp: Data.stamps[0].string,
                    _update_stamp: Data.timeStampApril,
                    _id: '1',
                },
                {
                    code: 'C3',
                    full_name: 'Pompei-overriden',
                    role: 'C3R0',
                    created_on: '',
                    stamp: '',
                    _update_stamp: Data.timeStampMarch,
                    _id: '3',
                },
                {
                    code: 'C5',
                    full_name: 'Pretoria',
                    role: 'C5R0',
                    created_on: '',
                    stamp: Data.stamps[1].string,
                    _update_stamp: Data.timeStampMarch,
                    _id: '5',
                },
                {
                    code: 'C6',
                    full_name: 'Tokyo',
                    role: 'C6R0',
                    created_on: '2019-06-03',
                    stamp: '',
                    _update_stamp: Data.timeStampMarch,
                    _id: '99',
                },
            ],

            custom: [
                {
                    code: 'C4',
                    full_name: 'Warsaw',
                    role: 'C4R0',
                    _update_stamp: Data.timeStampMarch,
                    _id: '4',
                },
            ],
        },
        '@sage/x3-geology': {
            folder: 'extension-layers',
            setup: [
                { _update_stamp: Data.timeStampApril, has_volcano: 'Y', _id: '1' },
                { _update_stamp: Data.timeStampMarch, has_volcano: 'Y', _id: '3' },
                { _update_stamp: Data.timeStampMarch, has_volcano: 'N', _id: '5' },
                { _update_stamp: Data.timeStampMarch, has_volcano: 'Y', _id: '99' },
            ],

            custom: [{ _update_stamp: Data.timeStampMarch, has_volcano: 'N', _id: '4' }],
        },
        '@sage/x3-population': {
            folder: 'extension-layers',
            setup: [
                { _update_stamp: Data.timeStampApril, demonym: 'Barcelonians-in-April', _id: '1' },
                { _update_stamp: Data.timeStampMarch, demonym: '', _id: '3' },
                { _update_stamp: Data.timeStampMarch, demonym: 'Pretorians', _id: '5' },
                { _update_stamp: Data.timeStampMarch, demonym: 'Tokyoites', _id: '99' },
            ],

            custom: [{ _update_stamp: Data.timeStampMarch, _id: '4' }],
        },
        '@sage/x3-europe': {
            folder: 'extension-layers',
            setup: [
                { _update_stamp: Data.timeStampApril, is_in_europe: 'Y', _id: '1' },
                { _update_stamp: Data.timeStampMarch, is_in_europe: '', _id: '3' },
                { _update_stamp: Data.timeStampMarch, is_in_europe: 'N', _id: '5' },
                { _update_stamp: Data.timeStampMarch, is_in_europe: 'N', _id: '99' },
            ],

            custom: [{ _update_stamp: Data.timeStampMarch, is_in_europe: 'Y', _id: '4' }],
        },
    };

    static readonly expectedTableCityDef = {
        schemaName: 'TEST',
        tableName: 'test_city',
        name: 'test_city',
        columns: [
            { name: '_id', type: 'integer', isAutoIncrement: true },
            { name: 'code', type: 'string' },
            { name: 'full_name', type: 'string' },
            { name: 'role', type: 'string', maxLength: 30 },
            { name: 'created_on', type: 'date' },
            { name: 'stamp', type: 'datetime' },
            { name: '_update_tick', type: 'integer' },
            { name: '_create_stamp', type: 'datetime' },
            { name: '_update_stamp', type: 'datetime' },
            { name: '_delete_stamp', type: 'datetime' },
            { name: '_create_user', type: 'integer' },
            { name: '_update_user', type: 'integer' },
            { name: '_tenant_id', type: 'string' },
            { name: '_sourceId', type: 'string' },
            { name: 'has_volcano', type: 'boolean' },
            { name: 'demonym', type: 'string' },
            { name: 'is_in_europe', type: 'boolean' },
            { name: 'unused', type: 'boolean' },
        ],
        indexes: [
            {
                name: 'test_city_idx1',
                columns: [
                    { ascending: true, name: 'code' },
                    { ascending: true, name: '_tenant_id' },
                ],
                isUnique: true,
            },
            {
                name: 'test_city_idx2',
                columns: [
                    { ascending: true, name: 'full_name' },
                    { ascending: true, name: '_tenant_id' },
                ],
                isUnique: true,
            },
            {
                name: 'test_city_idx3',
                columns: [
                    { ascending: true, name: '_tenant_id' },
                    { ascending: true, name: '_id' },
                ],
                isUnique: true,
            },
        ],
    } as TableDefinition;

    static readonly barcelonaInMarch = {
        code: 'C1',
        full_name: 'Barcelona',
        has_volcano: false,
        demonym: 'Barcelonians',
        is_in_europe: true,
    };

    static readonly barcelonaInApril = {
        code: 'C1',
        full_name: 'Barcelona-in-April',
        has_volcano: true,
        demonym: 'Barcelonians-in-April',
        is_in_europe: true,
    };

    static readonly queryDataCity = [
        { code: 'C3', full_name: 'Pompei-overriden', has_volcano: true, demonym: ' ', is_in_europe: null },
        { code: 'C4', full_name: 'Warsaw', has_volcano: false, demonym: ' ', is_in_europe: true },
        { code: 'C5', full_name: 'Pretoria', has_volcano: false, demonym: 'Pretorians', is_in_europe: false },
        { code: 'C6', full_name: 'Tokyo', has_volcano: true, demonym: 'Tokyoites', is_in_europe: false },
    ];

    static queryDataMarchCity = [
        Data.barcelonaInMarch,
        { code: 'C2', full_name: 'Paris', has_volcano: false, demonym: 'Parisians', is_in_europe: true },
        ...Data.queryDataCity,
    ];

    static queryDataAprilCity = [Data.barcelonaInApril, ...Data.queryDataCity];

    static readonly notNullDatesCity = [
        { code: 'C1', created_on: '2019-01-02' },
        { code: 'C6', created_on: '2019-06-03' },
    ];

    static readonly nullDatesCity = [
        { code: 'C3', created_on: '' },
        { code: 'C4', created_on: '' },
        { code: 'C5', created_on: '' },
    ];

    static readonly notNullStampsCity = [
        { code: 'C1', stamp: +Data.stamps[0].number },
        { code: 'C5', stamp: +Data.stamps[1].number },
    ];

    static readonly nullStampsCity = [
        { code: 'C3', stamp: 0 },
        { code: 'C4', stamp: 0 },
        { code: 'C6', stamp: 0 },
    ];

    public static deleteSplitFiles() {
        glob.sync(fsp.join(this.packagesRoot, '@sage/*/data/**/*.csv')).forEach(path => {
            fs.unlinkSync(path);
        });
    }

    public static completeHeaders(headers: string[]): string[] {
        headers.push(...this.systemColumns.map(col => col.name));
        return headers;
    }

    public static completeTestData() {
        this.mergedRowsCity.forEach(row =>
            this.systemColumns.forEach(col => {
                (row as any)[col.name] = '';
            }),
        );
    }

    public static deleteMergedFiles() {
        if (fs.existsSync(this.pathMergedFileCity)) fs.unlinkSync(this.pathMergedFileCity);
    }
}
