// tslint:disable: no-unused-expression
import { decorators, Node } from '@sage/xtrem-core';

import { fixtures } from '..';

const { descriptionDataType } = fixtures.dataTypes;

interface TestTextJson {
    fra: string;
    eng: string;
}

interface TestHistoryJson {
    id?: number;
    items?: { author?: string };
}

@decorators.node<TestSimpleNode>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
})
export class TestSimpleNode extends Node {
    @decorators.booleanProperty<TestSimpleNode, 'booleanVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly booleanVal: Promise<boolean | null>;

    @decorators.stringProperty<TestSimpleNode, 'stringVal'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly stringVal: Promise<string>;

    @decorators.jsonProperty<TestSimpleNode, 'text'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly text: Promise<TestTextJson>;

    @decorators.jsonProperty<TestSimpleNode, 'history'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly history: Promise<TestHistoryJson>;
}

export const simpleNodeTableData = [
    {
        booleanVal: true,
        stringVal: 'string1',
        text: { fra: 'mairie', eng: 'town hall' },
        history: { id: 1, items: [{ author: 'Eric' }, { reviewer: 'Léa' }] },
    },
    {
        booleanVal: false,
        stringVal: 'string2',
        text: { fra: 'pays', eng: 'country' },
        history: { id: 2, items: [{ author: 'Léa' }] },
    },
    {
        booleanVal: true,
        stringVal: 'string3',
        text: { fra: 'langue', eng: 'language' },
        history: { id: 3, items: [{ author: 'Stéphane' }] },
    },
];

export const simpleNodeTableName = 'xtrem_data_management_test.test_simple_node';
