import { asyncArray, Collection, decorators, integer, Node, Reference, Test, TestSysVendor } from '@sage/xtrem-core';
import { assert } from 'chai';
import { fixtures } from '../fixtures';

const { ConfigManager, createApplicationWithApi, initTables, setup } = fixtures;
const { codeDataType, descriptionDataType } = fixtures.dataTypes;

@decorators.node<TestIsOwnedByCustomerNoVendor>({
    isPublished: true,
    storage: 'sql',
    isSetupNode: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true, isNaturalKey: true }],
    hasVendorProperty: false,
})
class TestIsOwnedByCustomerNoVendor extends Node {
    @decorators.stringProperty<TestIsOwnedByCustomerNoVendor, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
        isOwnedByCustomer: true,
        isFrozen: true,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestIsOwnedByCustomerNoVendor, 'text'>({
        dataType: () => descriptionDataType,
        isStored: true,
    })
    readonly text: Promise<string>;
}

@decorators.node<TestProvidesVendor>({
    isPublished: true,
    storage: 'sql',
    indexes: [{ orderBy: { code: 1 }, isUnique: true, isNaturalKey: true }],
    isSetupNode: true,
})
class TestProvidesVendor extends Node {
    @decorators.stringProperty<TestProvidesVendor, 'code'>({
        isPublished: true,
        isStored: true,
        isFrozen: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestProvidesVendor, 'text'>({
        dataType: () => descriptionDataType,
        isStored: true,
    })
    readonly text: Promise<string>;

    @decorators.stringProperty<TestProvidesVendor, 'ignored'>({
        dataType: () => descriptionDataType,
        isStored: true,
        isOwnedByCustomer: true,
    })
    readonly ignored: Promise<string>;

    @decorators.stringProperty<TestProvidesVendor, 'dependedUpon'>({
        dataType: () => codeDataType,
        isStored: true,
    })
    readonly dependedUpon: Promise<string>;

    @decorators.stringPropertyOverride<TestProvidesVendor, 'dependsOn'>({
        dependsOn: ['dependedUpon'],
        async updatedValue() {
            // use defaultValue()
            return `Updated ${await this.dependedUpon}`;
        },
    })
    readonly dependsOn: Promise<string>;
}

@decorators.node<TestVitalVendorParent>({
    isPublished: true,
    storage: 'sql',
    indexes: [{ orderBy: { code: 1 }, isUnique: true, isNaturalKey: true }],
    isSetupNode: true,
})
class TestVitalVendorParent extends Node {
    @decorators.stringProperty<TestVitalVendorParent, 'code'>({
        isPublished: true,
        isStored: true,
        isFrozen: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestVitalVendorParent, 'parent'>({
        isPublished: true,
        isVital: true,
        node: () => TestVitalVendorReference,
        reverseReference: 'parent',
    })
    readonly parent: Reference<TestVitalVendorReference>;
}

@decorators.node<TestVitalVendorReference>({
    isPublished: true,
    storage: 'sql',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
    isVitalReferenceChild: true,
})
class TestVitalVendorReference extends Node {
    @decorators.stringProperty<TestVitalVendorReference, 'code'>({
        isPublished: true,
        isStored: true,
        isFrozen: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestVitalVendorReference, 'parent'>({
        isPublished: true,
        isVitalParent: true,
        isStored: true,
        node: () => TestVitalVendorParent,
    })
    readonly parent: Reference<TestVitalVendorParent>;
}

@decorators.node<TestVendorParent>({
    isPublished: true,
    storage: 'sql',
    indexes: [{ orderBy: { code: 1 }, isUnique: true, isNaturalKey: true }],
    isSetupNode: true,
})
class TestVendorParent extends Node {
    @decorators.stringProperty<TestVendorParent, 'code'>({
        isPublished: true,
        isStored: true,
        isFrozen: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.collectionProperty<TestVendorParent, 'lines'>({
        isPublished: true,
        isRequired: true,
        isVital: true,
        reverseReference: 'parent',
        node: () => TestVendorChild,
    })
    readonly lines: Collection<TestVendorChild>;
}

@decorators.node<TestVendorChild>({
    isPublished: true,
    storage: 'sql',
    isVitalCollectionChild: true,
})
class TestVendorChild extends Node {
    @decorators.referenceProperty<TestVendorChild, 'parent'>({
        isPublished: true,
        isStored: true,
        isVitalParent: true,
        node: () => TestVendorParent,
    })
    readonly parent: Reference<TestVendorParent>;

    @decorators.integerProperty<TestVendorChild, 'lineNumber'>({
        isPublished: true,
        isStored: true,
    })
    readonly lineNumber: Promise<integer>;

    @decorators.stringProperty<TestVendorChild, 'text'>({
        isPublished: true,
        isStored: true,
        isOwnedByCustomer: true,
        dataType: () => codeDataType,
    })
    readonly text: Promise<string>;
}

@decorators.node<TestCsvClearVendor>({
    isPublished: true,
    storage: 'sql',
    indexes: [{ orderBy: { code: 1 }, isUnique: true, isNaturalKey: true }],
    isSetupNode: true,
})
class TestCsvClearVendor extends Node {
    @decorators.stringProperty<TestCsvClearVendor, 'code'>({
        isPublished: true,
        isStored: true,
        isFrozen: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestCsvClearVendor, 'text'>({
        dataType: () => descriptionDataType,
        isStored: true,
    })
    readonly text: Promise<string>;
}

describe('vendor tests', () => {
    describe('vendor node tests', () => {
        before(() => {});
        it('should throw if property has attribute isOwnedByCustomer, but node does not have a vendor property', async () => {
            await assert.isRejected(
                createApplicationWithApi({ nodes: { TestIsOwnedByCustomerNoVendor } }, 'xtrem_data_management_test'),
                "TestIsOwnedByCustomerNoVendor: The isOwnedByCustomer attribute is only allowed if the node has a vendor property. Check properties: 'code'",
            );
        });
    });

    describe('vendor - loading layer data', () => {
        before(async () => {
            Test.application = await createApplicationWithApi(
                { nodes: { TestCsvClearVendor, TestVitalVendorParent, TestVitalVendorReference } },
                'xtrem_data_management_test',
            );

            await initTables([
                {
                    nodeConstructor: TestSysVendor,
                    data: [{ _id: 1, name: 'sage', description: 'sage' }],
                },
                {
                    nodeConstructor: TestCsvClearVendor,
                    data: [
                        { code: 'TOCLEAR', _vendor: 1, text: 'Clear vendor' },
                        { code: 'OMITTED', _vendor: 1, text: 'Omitted' },
                        { code: 'TOUPDATE', _vendor: 1, text: 'Some text' },
                        { code: 'NOVENDOR', text: 'No vendor' },
                    ],
                },
                {
                    nodeConstructor: TestVitalVendorParent,
                    data: [],
                },
                {
                    nodeConstructor: TestVitalVendorReference,
                    data: [],
                },
            ]);
        });
    });

    describe('vendor protection', () => {
        before(async () => {
            await setup({
                application: await createApplicationWithApi(
                    { nodes: { TestProvidesVendor, TestVendorChild, TestVendorParent } },
                    'xtrem_data_management_test',
                ),
            });
            await initTables([
                {
                    nodeConstructor: TestSysVendor,
                    data: [
                        {
                            _id: 1,
                            name: 'sage',
                            description: 'sage',
                        },
                    ],
                },
                {
                    nodeConstructor: TestProvidesVendor,
                    data: [
                        {
                            code: 'CODE01',
                            text: 'Description',
                            ignored: 'Ignored',
                            dependedUpon: 'dependedUpon',
                        },
                        {
                            code: 'CODE02',
                            _vendor: 1,
                            text: 'Description',
                            ignored: 'Ignored',
                            dependedUpon: 'dependedUpon',
                        },
                        {
                            code: 'CODE03',
                            text: 'Description',
                            ignored: 'Ignored',
                            dependedUpon: 'dependedUpon',
                        },
                    ],
                },
                {
                    nodeConstructor: TestVendorParent,
                    data: [
                        {
                            _id: 1,
                            code: 'CODE01',
                            _vendor: 1,
                        },
                        {
                            _id: 2,
                            code: 'CODE02',
                            _vendor: 1,
                        },
                        {
                            _id: 3,
                            code: 'CODE03',
                        },
                    ],
                },
                /** _vendor for a child where the collection is NOT owned by Customer will be set by loadLayerData
                 * so we should set it in the test data as well */
                {
                    nodeConstructor: TestVendorChild,
                    data: [
                        {
                            parent: 1,
                            _sortValue: 10,
                            lineNumber: 1,
                            text: 'Text 11',
                            _vendor: 1,
                        },
                        {
                            parent: 1,
                            _sortValue: 20,
                            lineNumber: 2,
                            text: 'Text 12',
                            _vendor: 1,
                        },
                        {
                            parent: 2,
                            _sortValue: 10,
                            lineNumber: 1,
                            text: 'Text 21',
                            _vendor: 1,
                        },
                        {
                            parent: 2,
                            _sortValue: 20,
                            lineNumber: 2,
                            text: 'Text 22',
                            _vendor: 1,
                        },
                        {
                            parent: 3,
                            _sortValue: 10,
                            lineNumber: 1,
                            text: 'Text 31',
                            _vendor: 1,
                        },
                        {
                            parent: 3,
                            _sortValue: 20,
                            lineNumber: 2,
                            text: 'Text 32',
                            _vendor: 1,
                        },
                    ],
                },
            ]);
        });

        it('can update a record which is not protected by a vendor', () =>
            Test.withContext(async context => {
                const newInstance = await context.read(TestProvidesVendor, { code: 'CODE01' }, { forUpdate: true });
                assert.equal(await newInstance.code, 'CODE01');
                assert.equal(await newInstance.text, 'Description');
                await newInstance.$.set({ text: 'New description' });
                await newInstance.$.set({ ignored: 'Was updated' });
                await newInstance.$.save();
                const updatedInstance = await context.read(TestProvidesVendor, { code: 'CODE01' });
                assert.equal(await updatedInstance.text, 'New description');
            }));

        it('can update a record which is not protected by a vendor code: updatedValue', () =>
            Test.withContext(async context => {
                const newInstance = await context.read(TestProvidesVendor, { code: 'CODE01' }, { forUpdate: true });
                assert.equal(await newInstance.code, 'CODE01');
                assert.equal(await newInstance.dependedUpon, 'dependedUpon');
                await newInstance.$.set({ dependedUpon: 'DependedUpon' });
                assert.equal(await newInstance.dependsOn, 'Updated DependedUpon');
            }));

        it('cannot update a record which is protected by a vendor', () =>
            Test.withContext(async context => {
                const newInstance = await context.read(TestProvidesVendor, { code: 'CODE02' }, { forUpdate: true });
                assert.equal(await newInstance.code, 'CODE02');
                await assert.isRejected(
                    newInstance.$.set({ text: 'New description' }),
                    'TestProvidesVendor.text: Vendor protected properties cannot be assigned a value',
                );
            }));

        it('cannot update a record which is protected by a factory code: updatedValue', () =>
            Test.withContext(async context => {
                const newInstance = await context.read(TestProvidesVendor, { code: 'CODE02' }, { forUpdate: true });
                assert.equal(await newInstance.code, 'CODE02');
                await assert.isRejected(
                    newInstance.$.set({ dependedUpon: 'DependedUpon' }),
                    'TestProvidesVendor.dependedUpon: Vendor protected properties cannot be assigned a value',
                );
            }));

        it('can update a property for which isOwnedByCustomer is set to true', () =>
            Test.withContext(async context => {
                const newInstance = await context.read(TestProvidesVendor, { code: 'CODE02' }, { forUpdate: true });
                assert.equal(await newInstance.code, 'CODE02');
                await newInstance.$.set({ ignored: 'Was updated' });
                assert.equal(await newInstance.ignored, 'Was updated');
            }));

        it('cannot insert into a vendor protected collection', () =>
            Test.withContext(async context => {
                const parent = await context.read(TestVendorParent, { code: 'CODE01' }, { forUpdate: true });
                assert.equal(await parent.code, 'CODE01');
                assert.deepEqual(await parent.lines.map(line => line._sortValue).toArray(), [10, 20]);

                await assert.isRejected(
                    parent.$.set({
                        lines: [{ _action: 'create', lineNumber: 1, text: 'Text' }],
                    }),
                    'TestVendorParent.lines: Cannot add to or delete from vendor protected collections.',
                );

                await assert.isRejected(
                    parent.lines.append({ lineNumber: 3, text: 'Text 13' }),
                    'TestVendorParent.lines: Cannot add to or delete from vendor protected collections.',
                );
            }));

        it('can update a property on a protected collection item for which the child property isOwnedByCustomer is set to true', () =>
            Test.withContext(async context => {
                const parent = await context.read(TestVendorParent, { code: 'CODE01' }, { forUpdate: true });
                assert.equal(await parent.code, 'CODE01');
                assert.deepEqual(await parent.lines.map(line => line._sortValue).toArray(), [10, 20]);

                const expectedUpdatedLines = [
                    {
                        _id: 1,
                        _sortValue: 10,
                        lineNumber: 1,
                        _sourceId: '',
                        _vendor: {
                            _id: 1,
                        },
                        parent: {
                            _id: 1,
                        },
                        text: 'Text 11 modified',
                    },
                    {
                        _id: 2,
                        _sortValue: 20,
                        lineNumber: 2,
                        _sourceId: '',
                        _vendor: {
                            _id: 1,
                        },
                        parent: {
                            _id: 1,
                        },
                        text: 'Text 12',
                    },
                ];

                const linesToUpdate = await parent.lines.toArray();
                await linesToUpdate[0].$.set({ text: 'Text 11 modified' });

                await parent.$.set({
                    lines: await asyncArray(linesToUpdate)
                        .map(async item => {
                            return {
                                _action: 'update',
                                _id: item._id,
                                _sortValue: await item._sortValue,
                                lineNumber: await item.lineNumber,
                                _sourceId: await item._sourceId,
                                parent: (await item.parent)._id,
                                text: await item.text,
                            } as const;
                        })
                        .toArray(),
                });

                assert.deepEqual(
                    (await parent.$.payload({ withIds: true, withLayer: true, withoutCustomData: true })).lines,
                    expectedUpdatedLines,
                );
                await parent.$.save();

                const result = await context.read(TestVendorParent, { code: 'CODE01' }, { forUpdate: true });
                assert.equal(await result.code, 'CODE01');
                assert.deepEqual(await parent.lines.map(line => line.text).toArray(), ['Text 11 modified', 'Text 12']);
            }));

        it('cannot update a property on a protected collection item for which the child property isOwnedByCustomer not set', () =>
            Test.withContext(async context => {
                const parent = await context.read(TestVendorParent, { code: 'CODE01' }, { forUpdate: true });
                assert.equal(await parent.code, 'CODE01');
                assert.deepEqual(await parent.lines.map(line => line._sortValue).toArray(), [10, 20]);

                const parentLine = await parent.lines.elementAt(0);
                await assert.isRejected(
                    parentLine.$.set({ lineNumber: 3 }),
                    'TestVendorChild.lineNumber: Vendor protected properties cannot be assigned a value.',
                );
            }));

        it('can update a record which is protected by a vendor when ignoreVendorProtection config parameter is set to true', () =>
            Test.withContext(async context => {
                const ignoreVendorProtection = ConfigManager.current.ignoreVendorProtection;
                ConfigManager.current.ignoreVendorProtection = true;
                const newInstance = await context.read(TestProvidesVendor, { code: 'CODE02' }, { forUpdate: true });
                assert.equal(await newInstance.code, 'CODE02');
                /* does not throw */ await newInstance.$.set({ text: 'New description' });
                ConfigManager.current.ignoreVendorProtection = ignoreVendorProtection;
            }));

        it("don't throw an exception if value wasn't changed", () =>
            Test.withContext(async context => {
                const newInstance = await context.read(TestProvidesVendor, { code: 'CODE02' }, { forUpdate: true });
                assert.equal(await newInstance.code, 'CODE02');
                await newInstance.$.set({ text: 'Description' });
            }));

        it('can delete a record which is not protected by a vendor', () =>
            Test.withContext(async context => {
                const newInstance = await context.read(TestProvidesVendor, { code: 'CODE01' }, { forUpdate: true });
                assert.equal(await newInstance.code, 'CODE01');
                await newInstance.$.delete();
            }));

        it('cannot delete a record which is protected by a vendor', () =>
            Test.withContext(async context => {
                const newInstance = await context.read(TestProvidesVendor, { code: 'CODE02' }, { forUpdate: true });
                assert.equal(await newInstance.code, 'CODE02');
                await assert.isRejected(
                    newInstance.$.delete(),
                    'The record is protected by a vendor code and cannot be deleted.',
                );
            }));

        it('cannot update a property on a protected collection item for which the child property isOwnedByCustomer not set', () =>
            Test.withContext(async context => {
                const parent = await context.read(TestVendorParent, { code: 'CODE01' }, { forUpdate: true });
                assert.equal(await parent.code, 'CODE01');
                assert.deepEqual(await parent.lines.map(line => line._sortValue).toArray(), [10, 20]);

                const parentLine = await parent.lines.elementAt(0);
                await assert.isRejected(
                    parentLine.$.set({ lineNumber: 3 }),
                    'TestVendorChild.lineNumber: Vendor protected properties cannot be assigned a value.',
                );
            }));

        it('can update a record which is protected by a vendor when ignoreVendorProtection config parameter is set to true', () =>
            Test.withContext(async context => {
                const ignoreVendorProtection = ConfigManager.current.ignoreVendorProtection;
                ConfigManager.current.ignoreVendorProtection = true;
                const newInstance = await context.read(TestProvidesVendor, { code: 'CODE02' }, { forUpdate: true });
                assert.equal(await newInstance.code, 'CODE02');
                /* does not throw */ await newInstance.$.set({ text: 'New description' });
                ConfigManager.current.ignoreVendorProtection = ignoreVendorProtection;
            }));

        it("don't throw an exception if value wasn't changed", () =>
            Test.withContext(async context => {
                const newInstance = await context.read(TestProvidesVendor, { code: 'CODE02' }, { forUpdate: true });
                assert.equal(await newInstance.code, 'CODE02');
                await newInstance.$.set({ text: 'Description' });
            }));

        it('can delete a record which is not protected by a vendor', () =>
            Test.withContext(async context => {
                const newInstance = await context.read(TestProvidesVendor, { code: 'CODE01' }, { forUpdate: true });
                assert.equal(await newInstance.code, 'CODE01');
                await newInstance.$.delete();
            }));

        it('cannot delete a record which is protected by a vendor', () =>
            Test.withContext(async context => {
                const newInstance = await context.read(TestProvidesVendor, { code: 'CODE02' }, { forUpdate: true });
                assert.equal(await newInstance.code, 'CODE02');
                await assert.isRejected(
                    newInstance.$.delete(),
                    'The record is protected by a vendor code and cannot be deleted.',
                );
            }));

        after(() => {});
    });
});
