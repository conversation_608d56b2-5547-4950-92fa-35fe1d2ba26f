import {
    asyncArray,
    Collection,
    Context,
    decorators,
    integer,
    Node,
    Reference,
    ServiceOption,
    StringDataType,
    Test,
} from '@sage/xtrem-core';
import { assert } from 'chai';
import { fixtures } from '../fixtures';

const { createApplicationWithApi, initTables, restoreTables, setup } = fixtures;
const { codeDataType } = fixtures.dataTypes;
const serviceOptions = fixtures.serviceOptions;

export const dataTypeWithOption = new StringDataType({
    maxLength: 250,
    serviceOptions: () => [serviceOptions.serviceOption1, serviceOptions.serviceOption2],
});

@decorators.node<TestServiceOptionsInNodeDecorator2>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    prepare(): void {
        this.$.context.emit('record', this, 'prepare', 'TestServiceOptionsInNodeDecorator2');
    },
    controlBegin(): void {
        this.$.context.emit('record', this, 'controlBegin', 'TestServiceOptionsInNodeDecorator2');
    },
    controlEnd(): void {
        this.$.context.emit('record', this, 'controlEnd', 'TestServiceOptionsInNodeDecorator2');
    },
    saveBegin(): void {
        this.$.context.emit('record', this, 'saveBegin', 'TestServiceOptionsInNodeDecorator2');
    },
    saveEnd(): void {
        this.$.context.emit('record', this, 'saveEnd', 'TestServiceOptionsInNodeDecorator2');
    },
    controlDelete(): void {
        this.$.context.emit('record', this, 'controlDelete', 'TestServiceOptionsInNodeDecorator2');
    },
    deleteEnd(): void {
        this.$.context.emit('record', this, 'deleteEnd', 'TestServiceOptionsInNodeDecorator2');
    },

    indexes: [{ orderBy: { id: +1 }, isUnique: true }],
    serviceOptions: () => [serviceOptions.serviceOption1, serviceOptions.serviceOption2],
})
export class TestServiceOptionsInNodeDecorator2 extends Node {
    @decorators.stringProperty<TestServiceOptionsInNodeDecorator2, 'id'>({
        dataType: () => dataTypeWithOption,
        isPublished: true,
        isStored: true,
    })
    readonly id: Promise<string>;

    @decorators.stringProperty<TestServiceOptionsInNodeDecorator2, 'name'>({
        isStored: true,
        dataType: () => codeDataType,
        control(): void {
            this.$.context.emit('record', this, 'control', 'name');
        },
    })
    readonly name: Promise<string>;
}

@decorators.node<TestServiceOptionsReference>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    prepare(): void {
        this.$.context.emit('record', this, 'prepare', 'TestServiceOptionsReference');
    },
    controlBegin(): void {
        this.$.context.emit('record', this, 'controlBegin', 'TestServiceOptionsReference');
    },
    controlEnd(): void {
        this.$.context.emit('record', this, 'controlEnd', 'TestServiceOptionsReference');
    },
    saveBegin(): void {
        this.$.context.emit('record', this, 'saveBegin', 'TestServiceOptionsReference');
    },
    saveEnd(): void {
        this.$.context.emit('record', this, 'saveEnd', 'TestServiceOptionsReference');
    },
    controlDelete(): void {
        this.$.context.emit('record', this, 'controlDelete', 'TestServiceOptionsReference');
    },
    deleteEnd(): void {
        this.$.context.emit('record', this, 'deleteEnd', 'TestServiceOptionsReference');
    },

    indexes: [{ orderBy: { id: +1 }, isUnique: true }],
})
export class TestServiceOptionsReference extends Node {
    @decorators.stringProperty<TestServiceOptionsReference, 'id'>({
        dataType: () => dataTypeWithOption,
        isPublished: true,
        isStored: true,
    })
    readonly id: Promise<string>;

    @decorators.stringProperty<TestServiceOptionsReference, 'name'>({
        isStored: true,
        dataType: () => codeDataType,
        control(): void {
            this.$.context.emit('record', this, 'control', 'name');
        },
    })
    readonly name: Promise<string>;
}

@decorators.node<TestServiceOptionInPropDecorator>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [],
})
export class TestServiceOptionInPropDecorator extends Node {
    @decorators.integerProperty<TestServiceOptionInPropDecorator, 'price'>({
        isPublished: true,
        isStored: true,
    })
    readonly price: Promise<integer>;

    @decorators.integerProperty<TestServiceOptionInPropDecorator, 'discount'>({
        serviceOptions: () => [serviceOptions.serviceOption1, serviceOptions.serviceOption2],
        isPublished: true,
        isStored: true,
        isNullable: true,
        defaultValue() {
            this.$.context.emit('record', this, 'defaultValue', 'discount');
            return 5;
        },
        prepare(): void {
            this.$.context.emit('record', this, 'prepare', 'discount');
        },
        control(): void {
            this.$.context.emit('record', this, 'control', 'discount');
        },
    })
    readonly discount: Promise<integer | null>;

    @decorators.integerProperty<TestServiceOptionInPropDecorator, 'discountedPrice'>({
        serviceOptions: () => [serviceOptions.serviceOption1, serviceOptions.serviceOption2],
        isPublished: true,
        isNullable: true,
        async getValue() {
            this.$.context.emit('record', this, 'getValue', 'discountedPrice');
            const price = await this.price;
            const discount = await this.discount;
            return discount ? price * (1 - discount / 100) : price;
        },
    })
    readonly discountedPrice: Promise<integer | null>;

    @decorators.integerProperty<TestServiceOptionInPropDecorator, 'salePrice'>({
        serviceOptions: () => [serviceOptions.serviceOption1, serviceOptions.serviceOption2],
        isPublished: true,
        isStored: true,
        isNullable: true,
        async adaptValue(val: integer): Promise<integer> {
            this.$.context.emit('record', this, 'adaptValue', 'salePrice');
            const discount = await this.discount;
            return discount ? val * (1 - discount / 100) : val;
        },
    })
    readonly salePrice: Promise<integer | null>;

    @decorators.referenceProperty<TestServiceOptionInPropDecorator, 'reference1'>({
        isStored: true,
        isNullable: true,
        node: () => TestServiceOptionsInNodeDecorator2,
        prepare(): void {
            this.$.context.emit('record', this, 'prepare', 'reference1');
        },
        control(): void {
            this.$.context.emit('record', this, 'control', 'reference1');
        },
    })
    readonly reference1: Reference<TestServiceOptionsInNodeDecorator2>;

    @decorators.referenceProperty<TestServiceOptionInPropDecorator, 'reference2'>({
        serviceOptions: () => [serviceOptions.serviceOption1, serviceOptions.serviceOption2],
        isStored: true,
        isNullable: true,
        node: () => TestServiceOptionsReference,
        prepare(): void {
            this.$.context.emit('record', this, 'prepare', 'reference2');
        },
        control(): void {
            this.$.context.emit('record', this, 'control', 'reference2');
        },
    })
    readonly reference2: Reference<TestServiceOptionsReference>;

    @decorators.stringProperty<TestServiceOptionInPropDecorator, 'comment'>({
        dataType: () => dataTypeWithOption,
        serviceOptions: () => [serviceOptions.serviceOption1, serviceOptions.serviceOption2],
        isPublished: true,
        isStored: true,
    })
    readonly comment: Promise<string>;
}

@decorators.node<DuplicatedServiceOptionsInPropDecorator>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [],
})
export class DuplicatedServiceOptionsInPropDecorator extends Node {
    @decorators.stringProperty<DuplicatedServiceOptionsInPropDecorator, 'id'>({
        dataType: () => dataTypeWithOption,
        serviceOptions: () => [serviceOptions.serviceOption1, serviceOptions.serviceOption2],
        isPublished: true,
        isStored: true,
    })
    readonly id: Promise<string>;
}

@decorators.node<DuplicateServiceOptionsInReferenceDecoratorAndTargetNode>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [],
})
export class DuplicateServiceOptionsInReferenceDecoratorAndTargetNode extends Node {
    @decorators.referenceProperty<DuplicateServiceOptionsInReferenceDecoratorAndTargetNode, 'referenceNode'>({
        node: () => TestServiceOptionsInNodeDecorator2,
        isPublished: true,
        isStored: true,
        isVital: true,
        serviceOptions: () => [serviceOptions.serviceOption1, serviceOptions.serviceOption2],
    })
    readonly referenceNode: Reference<TestServiceOptionsInNodeDecorator2>;
}

@decorators.node<DuplicateServiceOptionsInCollectionDecoratorAndTargetNode>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [],
})
export class DuplicateServiceOptionsInCollectionDecoratorAndTargetNode extends Node {
    @decorators.collectionProperty<DuplicateServiceOptionsInCollectionDecoratorAndTargetNode, 'referenceNodeLines'>({
        isPublished: true,
        isStored: true,
        isVital: true,
        node: () => TestServiceOptionsInNodeDecorator2,
        reverseReference: 'id',
        serviceOptions: () => [serviceOptions.serviceOption1, serviceOptions.serviceOption2],
    })
    readonly referenceNodeLines: Collection<TestServiceOptionsInNodeDecorator2>;
}

interface Event {
    key: string;
    event: string;
    path: string;
}
class Recorder {
    events = [] as Event[];

    add(parent: Node, event: string, path = ''): void {
        this.events.push({ key: parent.$.keyToken, event, path });
    }
}

async function checkGetSetServiceOption(
    context: Context,
    options: { enabledServiceOptions: ServiceOption[]; disabledServiceOptions: ServiceOption[] },
): Promise<void> {
    const recorder = new Recorder();
    context.on('record', recorder.add.bind(recorder));

    await asyncArray(options.enabledServiceOptions).forEach(async s =>
        assert.isTrue(await context.isServiceOptionEnabled(s)),
    );
    await asyncArray(options.disabledServiceOptions).forEach(async s =>
        assert.isFalse(await context.isServiceOptionEnabled(s)),
    );

    const node = await context.create(TestServiceOptionInPropDecorator, {});
    assert.isFalse(await node.$.isPropertyEnabled('discount'));
    const discountOldValue = (node.$.getRawPropertyValue('discount') ?? null) as number | null;

    /* does not throw */ await node.$.set({ discount: 10 });

    const discount = await node.discount;
    assert.strictEqual(discountOldValue, discount, 'value must not have changed');

    assert.isFalse(await node.$.isPropertyEnabled('salePrice'));
    const salesPriceOld = (node.$.getRawPropertyValue('salePrice') ?? null) as number | null;

    /* does not throw */ await node.$.set({ salePrice: 100 });
    const salePrice = await node.salePrice;
    assert.strictEqual(salesPriceOld, salePrice, 'value must not have changed');

    const commentOld = node.$.getRawPropertyValue('comment') ?? '';
    /* does not throw */ await node.$.set({ comment: 'this comment will not be set' });
    const comment = await node.comment;
    assert.strictEqual(commentOld, comment, 'value must not have changed');

    const payload = await node.$.payload();
    assert.isFalse(Object.keys(payload).includes('discount'));

    await node.$.save();

    const result = await context.read(TestServiceOptionInPropDecorator, { _id: node._id });

    assert.strictEqual(null, await result.discount, 'value of an inactive property must be null');
    assert.strictEqual(null, await result.salePrice, 'value of an inactive property must be null');
    assert.strictEqual('', await result.comment, 'value of an inactive string property must be empty');

    assert.equal(recorder.events.length, 0);
}

describe('ServiceOption', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi(
                {
                    nodes: {
                        TestServiceOptionsInNodeDecorator2,
                        TestServiceOptionsReference,
                        TestServiceOptionInPropDecorator,
                    },
                    serviceOptions,
                },
                'xtrem_data_management_test',
            ),
            stubResetTables: true,
        });
        await initTables([
            {
                nodeConstructor: TestServiceOptionsInNodeDecorator2,
                data: [],
            },
            {
                nodeConstructor: TestServiceOptionsReference,
                data: [],
            },
            {
                nodeConstructor: TestServiceOptionInPropDecorator,
                data: [],
            },
            {
                forceDelete: true,
                nodeConstructor: TestServiceOptionsInNodeDecorator2,
                data: [
                    { id: 'id', name: 'name' },
                    { id: 'id2', name: 'name2' },
                    { id: 'id3', name: 'name3' },
                ],
            },
        ]);
    });

    it('can create an option', () =>
        Test.withContext(
            async context => {
                assert.isTrue(await context.isServiceOptionEnabled(serviceOptions.serviceOption1));
                assert.isFalse(await context.isServiceOptionEnabled(serviceOptions.serviceOption2));
            },
            {
                testActiveServiceOptions: [serviceOptions.serviceOption1],
            },
        ));

    it('Can get/set a value of an active option', () =>
        Test.withContext(
            async context => {
                const recorder = new Recorder();
                context.on('record', recorder.add.bind(recorder));
                assert.isTrue(await context.isServiceOptionEnabled(serviceOptions.serviceOption1));
                assert.isTrue(await context.isServiceOptionEnabled(serviceOptions.serviceOption2));

                const reference1 = await context.create(TestServiceOptionsInNodeDecorator2, {
                    id: '1',
                    name: 'reference1',
                });
                await reference1.$.save();

                const reference2 = await context.create(TestServiceOptionsReference, { id: '2', name: 'reference2' });
                await reference2.$.save();

                const node = await context.create(TestServiceOptionInPropDecorator, {});
                assert.equal(await node.discount, 5);
                await node.$.set({ discount: 20 });
                assert.equal(await node.discount, 20);
                await node.$.set({ salePrice: 100 });
                assert.equal(await node.salePrice, 80);
                await node.$.set({ reference1 });
                await node.$.set({ reference2 });
                await node.$.save();

                const result = await context.read(TestServiceOptionInPropDecorator, { _id: node._id });
                assert.equal(await result.discount, 20);

                const payload = await node.$.payload();
                assert.isTrue(Object.keys(payload).includes('discount'));
                assert.equal(payload.discount, 20);
                assert.deepEqual(recorder.events, [
                    { event: 'prepare', key: '-1000000001', path: 'TestServiceOptionsInNodeDecorator2' },
                    { event: 'controlBegin', key: '-1000000001', path: 'TestServiceOptionsInNodeDecorator2' },
                    { event: 'control', key: '-1000000001', path: 'name' },
                    { event: 'controlEnd', key: '-1000000001', path: 'TestServiceOptionsInNodeDecorator2' },
                    { event: 'saveBegin', key: '-1000000001', path: 'TestServiceOptionsInNodeDecorator2' },
                    { event: 'saveEnd', key: '4', path: 'TestServiceOptionsInNodeDecorator2' },
                    { event: 'prepare', key: '-1000000002', path: 'TestServiceOptionsReference' },
                    { event: 'controlBegin', key: '-1000000002', path: 'TestServiceOptionsReference' },
                    { event: 'control', key: '-1000000002', path: 'name' },
                    { event: 'controlEnd', key: '-1000000002', path: 'TestServiceOptionsReference' },
                    { event: 'saveBegin', key: '-1000000002', path: 'TestServiceOptionsReference' },
                    { event: 'saveEnd', key: '1', path: 'TestServiceOptionsReference' },
                    { event: 'defaultValue', key: '-1000000003', path: 'discount' },
                    { event: 'adaptValue', key: '-1000000003', path: 'salePrice' },
                    { event: 'prepare', key: '-1000000003', path: 'discount' },
                    { event: 'prepare', key: '-1000000003', path: 'reference1' },
                    { event: 'prepare', key: '-1000000003', path: 'reference2' },
                    { event: 'control', key: '-1000000003', path: 'discount' },
                    { event: 'control', key: '-1000000003', path: 'reference1' },
                    { event: 'control', key: '-1000000003', path: 'reference2' },
                    { event: 'getValue', key: '1', path: 'discountedPrice' },
                ]);
            },
            {
                testActiveServiceOptions: [serviceOptions.serviceOption1, serviceOptions.serviceOption2],
            },
        ));

    it('Can set/get an inactive option without error (option1)', () =>
        Test.withContext(
            context =>
                checkGetSetServiceOption(context, {
                    enabledServiceOptions: [serviceOptions.serviceOption1],
                    disabledServiceOptions: [serviceOptions.serviceOption2],
                }),
            {
                testActiveServiceOptions: [serviceOptions.serviceOption1],
            },
        ));

    it('Can set/get an inactive option (option2)', () =>
        Test.withContext(
            context =>
                checkGetSetServiceOption(context, {
                    enabledServiceOptions: [serviceOptions.serviceOption2],
                    disabledServiceOptions: [serviceOptions.serviceOption1],
                }),
            {
                testActiveServiceOptions: [serviceOptions.serviceOption2],
            },
        ));

    it('defaultValue of an active option', () =>
        Test.withContext(
            async context => {
                assert.isTrue(await context.isServiceOptionEnabled(serviceOptions.serviceOption1));
                assert.isTrue(await context.isServiceOptionEnabled(serviceOptions.serviceOption2));

                const node = await context.create(TestServiceOptionInPropDecorator, {});
                assert.equal(await node.discount, 5);
                await node.$.save();

                const payload = await node.$.payload();
                assert.isTrue(Object.keys(payload).includes('discount'));
                assert.equal(payload.discount, 5);

                await node.$.set({ discount: 10 });
                assert.equal(await node.discount, 10);
            },
            {
                testActiveServiceOptions: [serviceOptions.serviceOption1, serviceOptions.serviceOption2],
            },
        ));

    it('defaultValue of an inactive option', () =>
        Test.withContext(
            async context => {
                assert.isTrue(await context.isServiceOptionEnabled(serviceOptions.serviceOption1));
                assert.isFalse(await context.isServiceOptionEnabled(serviceOptions.serviceOption2));

                const node = await context.create(TestServiceOptionInPropDecorator, { price: 10 });
                assert.strictEqual(null, await node.discount, 'value of an nullable inactive property must be null');
                await node.$.save();

                const result = await context.read(TestServiceOptionInPropDecorator, { _id: node._id });
                assert.equal(result._id, node._id);
                assert.strictEqual(null, await result.discount, 'value of an nullable inactive property must be null');

                const node2 = await context.create(TestServiceOptionInPropDecorator, { price: 20 });
                assert.strictEqual(null, await node2.discount, 'value of an nullable inactive property must be null');
                await node2.$.save();

                const node3 = await context.create(TestServiceOptionInPropDecorator, { price: 30 });
                assert.strictEqual(null, await node3.discount, 'value of an nullable inactive property must be null');
                await node3.$.save();

                const data = await context
                    .query(TestServiceOptionInPropDecorator, {
                        filter: {
                            discount: node.discount,
                        },
                        orderBy: { price: 1 },
                    })
                    .toArray();

                assert.equal(data.length, 3);
                // serviceOption3 is not active => id is not available and filter on name  which of type related to serviceOption 3 is ignored
                assert.equal(await data[0].price, 10);
                assert.isNull(await data[0].discount);
                assert.equal(await data[1].price, 20);
                assert.isNull(await data[1].discount);
                assert.equal(await data[2].price, 30);
                assert.isNull(await data[2].discount);
            },
            {
                testActiveServiceOptions: [serviceOptions.serviceOption1],
            },
        ));

    it('activating a high level service option activates all its technical ones', async () => {
        await Test.withContext(async context => {
            assert.isFalse(await context.isServiceOptionEnabled(serviceOptions.hiddenServiceOption1));
            assert.isFalse(await context.isServiceOptionEnabled(serviceOptions.hiddenServiceOption2));
        });

        await Test.withContext(
            async context => {
                assert.isTrue(await context.isServiceOptionEnabled(serviceOptions.hiddenServiceOption1));
                assert.isTrue(await context.isServiceOptionEnabled(serviceOptions.hiddenServiceOption2));
            },
            {
                testActiveServiceOptions: [serviceOptions.highLevelServiceOption],
            },
        );
    });

    it('rejects query with reader', () =>
        Test.withReadonlyContext(async context => {
            const queryWithReader = await context.queryWithReader(
                TestServiceOptionsInNodeDecorator2,
                { orderBy: { id: 1 } },
                reader => reader.reduce(async (r, node) => r + (await node.name), ''),
            );
            assert.equal(queryWithReader, '');
        }));

    it('query with active service options', () =>
        Test.withContext(
            async context => {
                const data = await context
                    .query(TestServiceOptionsInNodeDecorator2, {
                        filter: {
                            id: 'id',
                        },
                        orderBy: { id: 1 },
                    })
                    .toArray();

                assert.equal(data.length, 1);
                assert.equal(await data[0].name, 'name');
                assert.equal(await data[0].id, 'id');
            },
            {
                testActiveServiceOptions: [serviceOptions.serviceOption1, serviceOptions.serviceOption2],
            },
        ));

    it('returns null it the service options is inactive', () =>
        Test.withContext(async context => {
            assert.isNull(await context.tryRead(TestServiceOptionsInNodeDecorator2, { _id: 1 }));
        }));

    it('returns null it the service options is inactive', () =>
        Test.withContext(async context => {
            assert.isNull(await context.tryRead(TestServiceOptionsInNodeDecorator2, { _id: 1 }));
            await assert.isRejected(
                context.read(TestServiceOptionsInNodeDecorator2, { _id: 1 }),
                'TestServiceOptionsInNodeDecorator2 is restricted for reading',
            );
        }));

    it('cannot have the same service option twice in an application', () =>
        Test.withContext(async () => {
            await assert.isRejected(
                createApplicationWithApi(
                    {
                        nodes: {
                            TestServiceOptionsInNodeDecorator2,
                            TestServiceOptionsReference,
                            TestServiceOptionInPropDecorator,
                        },
                        serviceOptions: {
                            serviceOption1: serviceOptions.serviceOption1,
                            serviceOption1Again: serviceOptions.serviceOption1,
                        },
                    },
                    'xtrem_data_management_test',
                ),
                'Service option option1 is declared in more than one package. @sage/xtrem-core and @sage/xtrem-core',
            );
        }));

    it('test config.serviceOptions.level', async () => {
        // serviceOption3 is a 'workInProgress' service option. It requires a development configuration
        // with a worksInProgress service options level:
        Test.patchConfig({
            serviceOptions: {
                level: 'workInProgress',
            },
        });

        await Test.withContext(
            async context => {
                assert.isTrue(await context.isServiceOptionEnabled(serviceOptions.serviceOption3));
            },
            {
                testActiveServiceOptions: [serviceOptions.serviceOption3],
            },
        );

        Test.patchConfig({
            serviceOptions: {
                level: 'released',
            },
        });

        await Test.withContext(
            async context => {
                assert.isFalse(await context.isServiceOptionEnabled(serviceOptions.serviceOption3));
            },
            {
                testActiveServiceOptions: [serviceOptions.serviceOption3],
            },
        );
    });

    after(() => restoreTables());
});
