import { Context, decorators, integer, Node, Reference, Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import { fixtures } from '../fixtures';

const { createApplicationWithApi, initTables, restoreTables, setup } = fixtures;

@decorators.node<TestSelfReferencing>({
    isPublished: true,
    canDeleteMany: true,
    storage: 'sql',
    indexes: [],
})
export class TestSelfReferencing extends Node {
    @decorators.integerProperty<TestSelfReferencing, 'value'>({
        isPublished: true,
        isStored: true,
    })
    readonly value: Promise<integer>;

    @decorators.referenceProperty<TestSelfReferencing, 'ref'>({
        isPublished: true,
        isStored: true,
        node: () => TestSelfReferencing,
        defaultValue() {
            return this;
        },
    })
    readonly ref: Reference<TestSelfReferencing>;
}

describe('Self referencing node', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi(
                { nodes: { TestSelfReferencing } },
                'xtrem_data_management_test',
            ),
        });
        await initTables([{ nodeConstructor: TestSelfReferencing, data: [] }]);
    });

    async function createSelfReferencingNode(context: Context): Promise<number> {
        const node = await context.create(TestSelfReferencing, { value: 5 });
        assert.equal(await node.ref, node);
        await node.$.save();
        assert.equal(await node.ref, node);
        return node._id;
    }

    it('can insert self referencing node', () =>
        Test.withContext(async context => {
            const id = await createSelfReferencingNode(context);

            const node = await context.read(TestSelfReferencing, { _id: id });
            assert.equal(await node.ref, node);
        }));

    it('can delete self referencing node', () =>
        Test.withContext(async context => {
            const id = await createSelfReferencingNode(context);

            await context.delete(TestSelfReferencing, { _id: id });

            const found = await context.exists(TestSelfReferencing, { _id: id });
            assert.isFalse(found);
        }));

    it('can sort factories containing self references', () => {
        assert.doesNotThrow(() => {
            const factories = Test.application.getSqlPackageFactories();
            assert.isTrue(factories.some(f => f.name === TestSelfReferencing.name));
        });
    });

    after(() => restoreTables());
});
