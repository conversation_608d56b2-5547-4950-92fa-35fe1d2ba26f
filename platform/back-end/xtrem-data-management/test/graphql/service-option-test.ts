import { withoutEdges } from '@sage/xtrem-client';
import {
    Application,
    assertIsRejectedWithDiagnoses,
    Collection,
    decorators,
    integer,
    Node,
    Reference,
    StringDataType,
    Test,
} from '@sage/xtrem-core';
import { assert } from 'chai';
import { fixtures } from '../fixtures';

const { createApplicationWithApi, graphqlSetup, initTables, restoreTables, updateContext } = fixtures;
const { serviceOption1, serviceOption2, serviceOption3, serviceOption4 } = fixtures.serviceOptions;

export const dataTypeWithOption = new StringDataType({
    maxLength: 250,
    serviceOptions: () => [serviceOption3],
});

export const dataTypeWithoutOption = new StringDataType({
    maxLength: 250,
});

@decorators.node<TestServiceOptionsInNodeDecorator>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [{ orderBy: { id: +1 }, isUnique: true }],
    serviceOptions: () => [serviceOption1],
})
export class TestServiceOptionsInNodeDecorator extends Node {
    @decorators.stringProperty<TestServiceOptionsInNodeDecorator, 'id'>({
        isPublished: true,
        isStored: true,
        dataType: () => dataTypeWithoutOption,
        serviceOptions: () => [serviceOption2],
    })
    readonly id: Promise<string>;

    @decorators.stringProperty<TestServiceOptionsInNodeDecorator, 'name'>({
        dataType: () => dataTypeWithOption,
        isPublished: true,
        isStored: true,
    })
    readonly name: Promise<string>;

    @decorators.referenceProperty<TestServiceOptionsInNodeDecorator, 'ref'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        node: () => TestServiceOptionsRefNode,
    })
    readonly ref: Reference<TestServiceOptionsRefNode | null>;

    @decorators.collectionProperty<TestServiceOptionsInNodeDecorator, 'lines'>({
        isPublished: true,
        node: () => TestServiceOptionsCollectionNode,
        reverseReference: 'document',
    })
    readonly lines: Collection<TestServiceOptionsCollectionNode>;
}

@decorators.node<TestServiceOptionsRefNode>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    serviceOptions: () => [serviceOption4],
    indexes: [{ orderBy: { refId: +1 }, isUnique: true }],
})
export class TestServiceOptionsRefNode extends Node {
    @decorators.integerProperty<TestServiceOptionsRefNode, 'refId'>({
        isPublished: true,
        isStored: true,
    })
    readonly refId: Promise<integer>;
}

@decorators.node<TestServiceOptionsCollectionNode>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    serviceOptions: () => [serviceOption4],
    indexes: [{ orderBy: { lineNumber: +1, document: +1 }, isUnique: true }],
})
export class TestServiceOptionsCollectionNode extends Node {
    @decorators.integerProperty<TestServiceOptionsCollectionNode, 'lineNumber'>({
        isPublished: true,
        isStored: true,
    })
    readonly lineNumber: Promise<number>;

    @decorators.stringProperty<TestServiceOptionsCollectionNode, 'lineName'>({
        isPublished: true,
        isStored: true,
        dataType: () => dataTypeWithoutOption,
    })
    readonly lineName: Promise<string>;

    @decorators.referenceProperty<TestServiceOptionsCollectionNode, 'document'>({
        isStored: true,
        isPublished: true,
        node: () => TestServiceOptionsInNodeDecorator,
        isNullable: true,
        allowedInUniqueIndex: true,
    })
    readonly document: Reference<TestServiceOptionsInNodeDecorator>;
}

let application: Application;
let graphqlHelper: InstanceType<typeof fixtures.GraphQlHelper>;

const query = `{
    testServiceOptionsInNodeDecorator {
        query {
            edges {
                node {
                    id
                    name
                }
            }
        }
    }
}`;

const queryRef = `{
    testServiceOptionsInNodeDecorator {
        query {
            edges {
                node {
                    id
                    name
                    ref {
                        refId
                    }
                }
            }
        }
    }
}`;

const queryCol = `{
    testServiceOptionsInNodeDecorator {
        query {
            edges {
                node {
                    id
                    name
                    lines {
                        query{
                            edges{
                                node{
                                    lineNumber

                                }
                            }
                        }
                    }
                }
            }
        }
    }
}`;

const queryFiltering = `{
    testServiceOptionsInNodeDecorator {
        query (filter: "{ _or: [ { id: 'id2' } , { name: 'name' } ] }", orderBy: "{ id: 1}") {
            edges {
                node {
                    _id
                    id
                    name
                }
            }
        }
    }
}`;

describe('Query a node with an inactive service option', () => {
    before(async () => {
        updateContext();
        application = await createApplicationWithApi(
            {
                nodes: {
                    TestServiceOptionsInNodeDecorator,
                    TestServiceOptionsCollectionNode,
                    TestServiceOptionsRefNode,
                },
                serviceOptions: { serviceOption1, serviceOption2, serviceOption3, serviceOption4 },
            },
            'xtrem_data_management_test',
        );
        Test.application = application;
        await initTables([
            {
                nodeConstructor: TestServiceOptionsRefNode,
                forceDelete: true,
                data: [
                    { _id: 1, refId: 1 },
                    { _id: 2, refId: 2 },
                    { _id: 3, refId: 3 },
                ],
            },
            {
                nodeConstructor: TestServiceOptionsInNodeDecorator,
                forceDelete: true,
                data: [
                    {
                        _id: 1,
                        id: 'id',
                        name: 'name',
                        ref: 1,
                    },
                    {
                        _id: 2,
                        id: 'id2',
                        name: 'name2',
                        ref: 2,
                    },
                    {
                        _id: 3,
                        id: 'id3',
                        name: 'name3',
                        ref: 3,
                    },
                ],
            },
            {
                nodeConstructor: TestServiceOptionsCollectionNode,
                forceDelete: true,
                data: [
                    { _id: 1, _sortValue: 10, lineNumber: 1, lineName: 'lineName1', document: 1 },
                    { _id: 2, _sortValue: 20, lineNumber: 2, lineName: 'lineName2', document: 1 },
                ],
            },
        ]);
        await Test.withContext(context => Test.initializeManagers(context));
    });
    it('rejects node if its service options are not active', () =>
        assertIsRejectedWithDiagnoses(
            Test.committed(async context => {
                graphqlHelper = await graphqlSetup({
                    application,
                    context,
                });

                await graphqlHelper.query(query);
            }),
            {
                message: 'Read failed.',
                diagnoses: [
                    {
                        severity: 4,
                        message:
                            'Operation TestServiceOptionsInNodeDecorator.read is not enabled by the configuration of the application.',
                        path: [],
                    },
                ],
            },
        ));
    it('rejects property if its service options are not active', () =>
        Test.committed(
            async context => {
                graphqlHelper = await graphqlSetup({
                    application,
                    context,
                });
                const data = (
                    withoutEdges(
                        (await graphqlHelper.query<{
                            testServiceOptionsInNodeDecorator: TestServiceOptionsInNodeDecorator;
                        }>(query, { context })) as any,
                    ) as any
                ).testServiceOptionsInNodeDecorator.query[0];
                assert.isNull(data.id);
                assert.isNull(data.name);
            },
            {
                testActiveServiceOptions: [serviceOption1],
            },
        ));
    it("rejects property if its datatype's service options are not active", () =>
        Test.committed(
            async context => {
                graphqlHelper = await graphqlSetup({
                    application,
                    context,
                });
                const data = (
                    withoutEdges(
                        (await graphqlHelper.query<{
                            testServiceOptionsInNodeDecorator: TestServiceOptionsInNodeDecorator;
                        }>(query, { context })) as any,
                    ) as any
                ).testServiceOptionsInNodeDecorator.query[0];
                assert.equal(data.id, 'id');
                assert.isNull(data.name);
            },
            {
                testActiveServiceOptions: [serviceOption1, serviceOption2],
            },
        ));
    it("rejects reference property if the refered node's service options are not active", () =>
        Test.committed(
            async context => {
                graphqlHelper = await graphqlSetup({
                    application,
                    context,
                });
                const data = (
                    withoutEdges(
                        (await graphqlHelper.query<{
                            testServiceOptionsRefNode: TestServiceOptionsRefNode;
                        }>(queryRef, { context })) as any,
                    ) as any
                ).testServiceOptionsInNodeDecorator.query[0];
                assert.equal(data.id, 'id');
                assert.equal(data.name, 'name');
                assert.isNull(data.ref);
            },
            {
                testActiveServiceOptions: [serviceOption1, serviceOption2, serviceOption3],
            },
        ));
    it("rejects collection property if the refered node's service options are not active", () =>
        Test.committed(
            async context => {
                graphqlHelper = await graphqlSetup({
                    application,
                    context,
                });
                const data = (
                    withoutEdges(
                        (await graphqlHelper.query<{
                            testServiceOptionsInNodeDecorator: TestServiceOptionsInNodeDecorator;
                        }>(queryCol, { context })) as any,
                    ) as any
                ).testServiceOptionsInNodeDecorator.query[0];
                assert.equal(data.id, 'id');
                assert.equal(data.name, 'name');
                assert.isNull(data.lines);
            },
            {
                testActiveServiceOptions: [serviceOption1, serviceOption2, serviceOption3],
            },
        ));
    it('ignore property filtering if its service options are not active  - property serviceOption', () =>
        Test.withContext(
            async context => {
                graphqlHelper = await graphqlSetup({
                    application,
                    context,
                });
                const data = (
                    withoutEdges(
                        (await graphqlHelper.query<{
                            testServiceOptionsInNodeDecorator: TestServiceOptionsInNodeDecorator;
                        }>(queryFiltering, { context })) as any,
                    ) as any
                ).testServiceOptionsInNodeDecorator.query;
                assert.equal(data.length, 3);
                // serviceOption2 is not active => id is not available and filter on it is ignored
                assert.isNull(data[0].id);
                assert.isNull(data[0].name);
                assert.equal(data[0]._id, 1);
                assert.isNull(data[1].id);
                assert.isNull(data[1].name);
                assert.equal(data[1]._id, 2);
                assert.isNull(data[2].id);
                assert.isNull(data[2].name);
                assert.equal(data[2]._id, 3);
            },
            {
                testActiveServiceOptions: [serviceOption1],
            },
        ));
    it('ignore property filtering if its service options are not active  - property serviceOption', () =>
        Test.withContext(
            async context => {
                graphqlHelper = await graphqlSetup({
                    application,
                    context,
                });
                const data = (
                    withoutEdges(
                        (await graphqlHelper.query<{
                            testServiceOptionsInNodeDecorator: TestServiceOptionsInNodeDecorator;
                        }>(queryFiltering, { context })) as any,
                    ) as any
                ).testServiceOptionsInNodeDecorator.query;
                assert.equal(data.length, 1);
                // serviceOption2 is not active => id is not available and filter on it is ignored
                assert.isNull(data[0].id);
                assert.equal(data[0].name, 'name');
            },
            {
                testActiveServiceOptions: [serviceOption1, serviceOption3],
            },
        ));
    it('ignore property filtering if its service options are not active - dataType serviceOption', () =>
        Test.withContext(
            async context => {
                graphqlHelper = await graphqlSetup({
                    application,
                    context,
                });
                const data = (
                    withoutEdges(
                        (await graphqlHelper.query<{
                            testServiceOptionsInNodeDecorator: TestServiceOptionsInNodeDecorator;
                        }>(queryFiltering, { context })) as any,
                    ) as any
                ).testServiceOptionsInNodeDecorator.query;
                assert.equal(data.length, 1);
                // serviceOption3 is not active => id is not available and filter on name  which of type related to serviceOption 3 is ignored
                assert.isNull(data[0].name);
                assert.equal(data[0].id, 'id2');
            },
            {
                testActiveServiceOptions: [serviceOption1, serviceOption2],
            },
        ));
    after(() => restoreTables());
});
