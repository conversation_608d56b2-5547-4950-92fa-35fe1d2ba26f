import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import { fixtures } from '../fixtures';
import { setup } from '../fixtures/setup';

const { createApplicationWithApi, initTables, restoreTables } = fixtures;
const {
    TestDocumentLookup,
    TestReferencedDocument,
    TestReferencedDocumentDetails,
    TestReferencedDocumentOther,
    TestDocument,
    TestReferred,
    TestDocumentLine,
} = fixtures.nodes;

const documentLookupData = [
    {
        _id: 1,
        parentControlInteger: 10,
    },
    {
        _id: 2,
        parentControlInteger: 11,
    },
];

const referencedDocumentData = [
    {
        _id: 1,
        lookupString: 'str1',
        controlInteger: 2,
    },
    {
        _id: 2,
        lookupString: 'str2',
        controlInteger: 10,
    },
    {
        _id: 3,
        lookupString: 'str2',
        controlInteger: 11,
    },
    {
        _id: 4,
        lookupString: 'str2',
        controlInteger: 10,
    },
];

describe('Context raw query method', () => {
    before(async () => {
        const application = await createApplicationWithApi(
            {
                nodes: {
                    TestDocumentLookup,
                    TestReferencedDocument,
                    TestReferencedDocumentDetails,
                    TestReferencedDocumentOther,
                    TestDocument,
                    TestReferred,
                    TestDocumentLine,
                },
            },
            'xtrem_data_management_test',
        );
        await setup(application);
        await initTables([
            { nodeConstructor: fixtures.nodes.TestReferencedDocument, data: referencedDocumentData },
            { nodeConstructor: fixtures.nodes.TestReferencedDocumentDetails, data: [] },
            { nodeConstructor: fixtures.nodes.TestDocumentLookup, data: documentLookupData },
        ]);
        await Test.withContext(context => Test.initializeManagers(context));
    });

    after(() => restoreTables());

    it('should execute queries against schema', () =>
        Test.withContext(async context => {
            const documents = await context.executeGraphql<{
                xtremCore: { testDocumentLookup: { query: { edges: any[] } } };
            }>('{ xtremCore{ testDocumentLookup{query{edges{node{_id}}}} }}');
            const edges = documents.xtremCore.testDocumentLookup.query.edges;
            assert.equal(edges.length, 2);
            assert.deepEqual(edges, [{ node: { _id: '1' } }, { node: { _id: '2' } }]);
        }));
});
