import { asyncArray, Test, TextStream } from '@sage/xtrem-core';
import { assert } from 'chai';
import { TenantService } from '../../lib/tenant/tenant-service';
import { fixtures } from '../fixtures';
import { resetSchema } from './tenant-test-utils';

const { initTables, restoreTables, setup, createApplicationWithApi } = fixtures;
const {
    TestIsClearedByResetDatatypes,
    TestIsClearedByResetInClassDecorator,
    TestIsClearedByResetTooComplex,
    TestIsClearedByResetWithRef,
    TestIsClearedByResetFunctionInNode,
    TestIsClearedByResetRefNode,
} = fixtures.nodes;

const datatypesData = [...fixtures.datatypesData];
const newData = { ...datatypesData[datatypesData.length - 1] };
newData._id += 1;
newData.id += 1;
// eslint-disable-next-line @typescript-eslint/quotes
newData.textStream = TextStream.fromString(`with multiple lines\nline '1'\nline "2"\nline "3'`);
datatypesData.push(newData);

async function checkResult(tenantId: string): Promise<void> {
    await Test.application.withReadonlyContext(tenantId, async context => {
        const results1 = await context.query(TestIsClearedByResetInClassDecorator).toArray();
        assert.equal(results1.length, 0);
        const results2 = await context.query(TestIsClearedByResetDatatypes).toArray();
        assert.equal(results2.length, datatypesData.length);
        await asyncArray(results2).forEach(async (t, i) => {
            if (i === 0) {
                assert.equal(await t.shortVal, null);
            } else if (i === 2) {
                // isCLearedByReset is true only if id=2
                assert.equal(await t.shortVal, null);
            } else if (i === 16) {
                assert.equal(await t.shortVal, 985);
            } else {
                assert.equal(await t.shortVal, 1000 - i);
            }
            assert.equal(await t.id, i);
            assert.equal(await t.integerVal, 0);
            assert.equal(await t.booleanVal, null);
            assert.equal(await t.stringVal, 'test');
            assert.equal((await t.decimalVal).valueOf(), 0);
        });
        const results3 = await context.query(TestIsClearedByResetWithRef).toArray();
        await asyncArray(results3).forEach(async t => {
            assert.equal(await t.sequenceNumber, 0);
        });
        const results4 = await context.query(TestIsClearedByResetFunctionInNode).toArray();
        assert.equal(results4.length, 2);
        await asyncArray(results4).forEach(async (t, i) => {
            assert.equal(await t.code, i);
        });
    });
}

describe('reset tenant operations', () => {
    before(async () => {
        const application = await createApplicationWithApi(
            {
                nodes: {
                    TestIsClearedByResetDatatypes,
                    TestIsClearedByResetInClassDecorator,
                    TestIsClearedByResetWithRef,
                    TestIsClearedByResetFunctionInNode,
                    TestIsClearedByResetRefNode,
                },
            },
            'xtrem_data_management_test',
        );
        await setup({ application, stubResetTables: false });
        await resetSchema(application);
    });

    beforeEach(async () => {
        await initTables([
            { nodeConstructor: TestIsClearedByResetDatatypes, data: datatypesData },
            {
                nodeConstructor: TestIsClearedByResetInClassDecorator,
                data: [{ _id: 1, id: 1, integerVal: 2 }],
            },
        ]);
        await initTables([
            {
                nodeConstructor: TestIsClearedByResetWithRef,
                data: [{ ref: 2, sequenceNumber: 2 }],
            },
        ]);
        await initTables([
            {
                nodeConstructor: TestIsClearedByResetRefNode,
                data: [
                    { _id: 1, codeRef: 0 },
                    { _id: 2, codeRef: 1 },
                    { _id: 3, codeRef: 2 },
                ],
            },
        ]);
        await initTables([
            {
                nodeConstructor: TestIsClearedByResetFunctionInNode,
                data: [
                    { code: 0, ref: 1 },
                    { code: 1, ref: 2 },
                    { code: 2, ref: 3 },
                ],
            },
        ]);
    });

    it('can reset tenant operations in tables and properties', async () => {
        await TenantService.resetTenantDocuments(Test.application, {
            tenant: { id: Test.defaultTenantId },
        });
        await checkResult(Test.defaultTenantId);
    });

    afterEach(() => restoreTables());
});

describe('reset tenant operations with too complex expression', () => {
    before(async () => {
        const application = await createApplicationWithApi(
            {
                nodes: {
                    TestIsClearedByResetTooComplex,
                },
            },
            'xtrem_data_management_test',
        );
        await setup({ application, stubResetTables: false });
        await resetSchema(application);
    });

    it('raise error if expression is too complex', async () => {
        await initTables([{ nodeConstructor: TestIsClearedByResetTooComplex, data: [{ id: 1, integerVal: 1 }] }]);
        await assert.isRejected(
            TenantService.resetTenantDocuments(Test.application, {
                tenant: { id: Test.defaultTenantId },
            }),
            'Unsupported Math function',
        );
    });
    afterEach(() => restoreTables());
});
