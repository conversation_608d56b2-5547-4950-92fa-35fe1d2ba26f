import { Application, asyncArray, SchemaSqlContext } from '@sage/xtrem-core';
import { SchemaInfo } from '../../lib/tenant/schema-info';

export const resetSchema = (application: Application) =>
    application.createContextForDdl(async context => {
        const excludedTables = ['test_foreign_nullable_1'];
        const tables = await new SchemaInfo(context.application).getSortedTables({ excludedTables });

        await asyncArray(tables).forEach(async table => {
            const sqlContext = new SchemaSqlContext(context.application);
            if (await sqlContext.tableExists(table.name))
                await sqlContext.dropTable(table.name, { isCascade: true, errorsAsWarnings: false });
        });
    });
