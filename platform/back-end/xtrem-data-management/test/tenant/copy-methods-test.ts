// tslint:disable: no-unused-expression

import { AnyRecord, restoreTables, Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as fs from 'fs';
import * as fsp from 'path';
import { TenantDataExport } from '../../lib/tenant/tenant-data-export';
import { TenantDataImport } from '../../lib/tenant/tenant-data-import';
import { fixtures } from '../fixtures';
import { simpleNodeTableData, simpleNodeTableName, TestSimpleNode } from '../fixtures/nodes/test-simple-node';

const { createApplicationWithApi, initTables, setup } = fixtures;

const filePath = fsp.join(__dirname, '../fixtures/test_simple_node.csv');

// XT-6027
describe('copy to stream and copy from stream', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi({ nodes: { TestSimpleNode } }, 'xtrem_data_management_test'),
        });
        await initTables([{ nodeConstructor: TestSimpleNode, data: [] }]);
        if (fs.existsSync(filePath)) fs.unlinkSync(filePath);
    });

    afterEach(() => {
        if (fs.existsSync(filePath)) fs.unlinkSync(filePath);
    });

    after(() => restoreTables());

    it('copy to', async () => {
        await initTables([{ nodeConstructor: TestSimpleNode, data: simpleNodeTableData }]);

        await Test.committed(async context => {
            let writer = fs.createWriteStream(filePath);
            await TenantDataExport.copyToStream(context, simpleNodeTableName, writer, {});
            assert.isTrue(fs.existsSync(filePath));
            let fileContent = fs.readFileSync(filePath).toString().split('\n');
            assert.equal(fileContent.length - 1, simpleNodeTableData.length + 1); // with header
            fs.unlinkSync(filePath);
            assert.isFalse(fs.existsSync(filePath));
            // writer was ended in the last copyToStream call
            writer = fs.createWriteStream(filePath);
            await TenantDataExport.copyToStream(context, simpleNodeTableName, writer, { header: false });
            assert.isTrue(fs.existsSync(filePath));
            fileContent = fs.readFileSync(filePath).toString().split('\n');
            assert.equal(fileContent.length - 1, simpleNodeTableData.length); // without header
        });
    });

    it('copy from', async () => {
        await initTables([{ nodeConstructor: TestSimpleNode, data: simpleNodeTableData }]);
        await Test.withCommittedContext(async context => {
            const writer = fs.createWriteStream(filePath);
            await TenantDataExport.copyToStream(context, simpleNodeTableName, writer, {});
            assert.isTrue(fs.existsSync(filePath));
            await context.executeSql(`DELETE FROM ${simpleNodeTableName}`, []);
        });

        await Test.withCommittedContext(async context => {
            let rows = await context.executeSql<AnyRecord[]>(`SELECT * from ${simpleNodeTableName}`, []);
            assert.equal(rows.length, 0);
            const reader = fs.createReadStream(filePath);
            const importService = new TenantDataImport(context.application, {
                tenant: {
                    id: '777777777777777777777',
                },
                location: '',
                customer: {
                    id: 'sdmo-customer',
                },
            });
            await importService.copyFromStream(reader, simpleNodeTableName, {});
            rows = await context.executeSql(`SELECT * from ${simpleNodeTableName}`, []);
            assert.equal(rows.length, simpleNodeTableData.length);
        });
    });
});
