import { loadLayersFromCsvFiles } from '@sage/xtrem-cli-layers';
import {
    BinaryStream,
    ConfigManager,
    DatetimeRange,
    Dict,
    TenantInfo,
    Test,
    TextStream,
    Uuid,
    asyncArray,
    compareAnonymizedStrings,
    datetime,
    tenantManagerMock,
    time,
    userData,
} from '@sage/xtrem-core';
import { Decimal } from '@sage/xtrem-decimal';
import { assert } from 'chai';
import * as csvParser from 'csv-parse';
import * as csvSyncParser from 'csv-parse/sync';
import * as fs from 'fs';
import { camelCase, kebabCase, pick, uniq } from 'lodash';
import * as fsp from 'path';
import { getDummyBinaryStream } from '../../lib/tenant/tenant-data-export';
import { TenantService } from '../../lib/tenant/tenant-service';
import { fixtures } from '../fixtures';
import { TestSimpleNode } from '../fixtures/nodes/test-simple-node';
import { TestSelfReferencing } from '../runtime/self-reference-test';
import { resetSchema } from './tenant-test-utils';

const { createApplicationWithApi, initTables, restoreTables, setup, serviceOptions } = fixtures;
const { TestAnonymizeExport, TestDatatypes, TestExportValue } = fixtures.nodes;

// add missing _customData to fixtures.datatypesData
const datatypesData = fixtures.datatypesData.map(data => ({ _customData: '{}', ...data }));

const testExportValueData = [
    { _id: 1, _sourceId: '', _customData: '{}', code: '001', sensitive: 'Sensitive should not export' },
    { _id: 2, _sourceId: '', _customData: '{}', code: '002', sensitive: 'Also should not export' },
];

const testExportValueDataHidden = [
    { _id: 1, _sourceId: '', _customData: '{}', code: '001', sensitive: 'hidden' },
    { _id: 2, _sourceId: '', _customData: '{}', code: '002', sensitive: 'hidden' },
];

const testAnonymizeData = [
    {
        _id: 1,
        _sourceId: '',
        _customData: '{}',
        code: '001',
        stringRegular: 'A regular string not anonymized',
        stringFixed: 'Sensitive should not export',
        stringHash: 'To be hashed',
        stringShortHash: 'Short hash',
        stringHashLimit: 'Limit hash',
        integerRandom: 10,
        shortRandom: 11,
        integerLimitRandom: 12,
        stringCustom: 'Sensitive custom string',
        stringPerCharacter: '<EMAIL>',
        urlToAnonymize: 'https://someurl.com',
        pdf: null,
        image: null,
        textStream: TextStream.empty,
    },
    {
        _id: 2,
        _sourceId: '',
        _customData: '{}',
        code: '002',
        stringRegular: 'Another regular string not anonymized',
        stringFixed: 'Also should not export',
        stringHash: 'Another hash',
        stringShortHash: 'Another',
        stringHashLimit: 'Another limit',
        integerRandom: 20,
        shortRandom: 21,
        integerLimitRandom: 22,
        stringCustom: 'gnirts motsuc evitisneS',
        stringPerCharacter: '012-345-6789',
        urlToAnonymize: 'http://anotherurl.com',
        pdf: BinaryStream.fromBuffer(Buffer.from('Some not null value')),
        image: BinaryStream.fromBuffer(Buffer.from('Another not null value')),
        textStream: TextStream.empty,
    },
    {
        _id: 3,
        _sourceId: '',
        _customData: '{}',
        code: '003',
        stringRegular: 'A regular string with separator; not anonymized',
        stringFixed: 'More sensitive data',
        stringHash: 'Hash3',
        stringShortHash: 'More',
        stringHashLimit: 'Limit',
        integerRandom: 30,
        shortRandom: 31,
        integerLimitRandom: 32,
        stringCustom: 'AbcDef',
        stringPerCharacter: 'Abc-123;">[\'\\]$<',
        urlToAnonymize: 'www.test.com',
        pdf: null,
        image: null,
        textStream: TextStream.fromString('Abc123'),
    },
    {
        _id: 4,
        _sourceId: '',
        _customData: '{}',
        code: '004',
        stringRegular: '',
        stringFixed: '',
        stringHash: '',
        stringShortHash: '',
        stringHashLimit: '',
        integerRandom: 0,
        shortRandom: 0,
        integerLimitRandom: 0,
        stringCustom: 'DefGhi',
        stringPerCharacter: 'Def-456";quoted">[\'\\]$<',
        urlToAnonymize: 'www.test123.com',
        pdf: null,
        image: null,
        textStream: TextStream.empty,
    },
];

const testAnonymizeHidden = [
    {
        _id: 1,
        _sourceId: '',
        _customData: '{}',
        code: '001',
        stringRegular: 'A regular string not anonymized',
        stringFixed: 'FixedValue',
        stringHash: 'b22fc1b354b6ed2686e4a496d11f8db39c126195243c241e58',
        stringShortHash: '887bfa683c',
        stringHashLimit: '0745f5e40391cce',
        integerRandom: 10,
        shortRandom: 11,
        integerLimitRandom: 12,
        stringCustom: 'gnirts motsuc evitisneS',
        stringPerCharacter: '<EMAIL>',
        urlToAnonymize: 'https://someurl.com',
        pdf: null,
        image: null,
        textStream: '',
    },
    {
        _id: 2,
        _sourceId: '',
        _customData: '{}',
        code: '002',
        stringRegular: 'Another regular string not anonymized',
        stringFixed: 'FixedValue',
        stringHash: 'f1251280b06bf0342b5eed0e408dc46af4c0ed94e50f725fe6',
        stringShortHash: '2d4bfedfe4',
        stringHashLimit: 'd6291dd587dddb8',
        integerRandom: 20,
        shortRandom: 21,
        integerLimitRandom: 22,
        stringCustom: 'Sensitive custom string',
        stringPerCharacter: '012-345-6789',
        urlToAnonymize: 'http://anotherurl.com',
        pdf: getDummyBinaryStream('pdf'),
        image: getDummyBinaryStream('image'),
        textStream: '',
    },
    {
        _id: 3,
        _sourceId: '',
        _customData: '{}',
        code: '003',
        stringRegular: 'A regular string with separator; not anonymized',
        stringFixed: 'FixedValue',
        stringHash: '5a91a66f0c86b5435fe748706b99c17e6e54a17e03c2a3ef8d',
        stringShortHash: 'd47d7cb0e4',
        stringHashLimit: '674b0ed54bf7667',
        integerRandom: 30,
        shortRandom: 31,
        integerLimitRandom: 32,
        stringCustom: 'feDcbA',
        stringPerCharacter: 'Abc-123;">[\'\\]$<',
        urlToAnonymize: 'www.test.com',
        pdf: null,
        image: null,
        textStream: 'This text was anonymized.',
    },
    {
        _id: 4,
        _sourceId: '',
        _customData: '{}',
        code: '004',
        stringRegular: '',
        stringFixed: '',
        stringHash: '',
        stringShortHash: '',
        stringHashLimit: '',
        integerRandom: 0,
        shortRandom: 0,
        integerLimitRandom: 0,
        stringCustom: 'ihGfeD',
        stringPerCharacter: 'Def-456";quoted">[\'\\]$<',
        urlToAnonymize: 'www.test123.com',
        pdf: null,
        image: null,
        textStream: '',
    },
];

const systemColumns = ['_create_stamp', '_create_user', '_update_stamp', '_update_tick', '_update_user'];
const systemProperties = systemColumns.reduce((res, name) => {
    res[toPropertyName(name)] = name;
    return res;
}, {} as Dict<string>);

function toPropertyName(name: string): string {
    return `${name[0] === '_' ? '_' : ''}${camelCase(name)}`;
}

function testRecords(records: Dict<any>[], headers: string[], expectedData: Dict<any>[]) {
    const propertyNames = headers.map(name => toPropertyName(name));
    records.forEach((rec: any, i: number) => {
        headers.forEach((name, j) => {
            const propertyName = propertyNames[j];
            testCsvData(propertyName, (expectedData[i] as any)[propertyName], rec[name]);
        });
    });
}

function testCsvData(prop: string, value: any, csv: string) {
    if (systemProperties[prop]) return;

    if (value == null) {
        assert.isEmpty(csv, prop);
        return;
    }
    if (csv === '') {
        if (value instanceof TextStream) {
            assert.isEmpty(value.toString(), prop);
        } else {
            assert.isEmpty(value, prop);
        }
        return;
    }

    switch (prop) {
        // number
        case '_id':
        case 'id':
        case 'shortVal':
        case 'integerVal':
        case 'floatVal':
        case 'doubleVal':
        case 'enumVal':
            // @todo to be fixed when enum will be in string
            // assert.strictEqual(+csv, value);
            break;

        case 'decimalVal':
            // compare the string representations
            assert.strictEqual(Decimal.make(csv).toString(), value.toString());
            break;

        case 'dateVal':
        case 'dateRangeVal':
        case 'decimalRangeVal':
        case 'integerRangeVal': {
            // compare the string representations
            assert.strictEqual(csv, value.toString());
            break;
        }
        case 'integerArrayVal':
        case 'stringArrayVal':
        case 'enumArrayVal': {
            // compare the string representations
            // @todo to be fixed when enum will be in string
            // assert.strictEqual(csv, `{${value.join(',')}}`); // Postgres stores array as {1,2,3}
            break;
        }
        case 'datetimeRangeVal': {
            // the cvs representation is enclosed in double quotes
            const csvRange = DatetimeRange.parse(csv.replace(/"/g, ''));
            // compare the string representations to not fail on internal differences
            assert.strictEqual(csvRange.toString(), (value as DatetimeRange).toString());
            break;
        }

        case 'booleanVal':
            assert.strictEqual(csv === '1', value);
            break;

        // string like
        case '_sourceId':
        case 'stringVal':
        case 'stringRegular':
        case 'stringFixed':
        case 'stringHash':
        case 'stringShortHash':
        case 'stringHashLimit':
        case 'textStream': {
            assert.isNotNull(value);
            const stringVal = value.value ?? value;
            const quoted = /^"(.*)"$/.exec(csv) || [];
            assert.strictEqual(quoted[1] ?? csv, stringVal);
            break;
        }

        case 'pdf':
        case 'image':
        case 'binaryStream': {
            const hex = (/^\\x([a-f0-9]*)$/.exec(csv) || [])[1];
            assert.isNotNull(hex);
            assert.deepEqual(Buffer.from(hex, 'hex'), value.value);
            break;
        }

        case 'datetimeVal':
            // compare the string representations to not fail on internal differences
            assert.deepEqual(datetime.parse(csv).toString(), value.toString());
            break;

        case 'timeVal':
            assert.deepEqual(time.parse(csv), value);
            break;

        case 'uuidVal':
            assert.deepEqual(Uuid.fromString(csv), value);
            break;

        case 'jsonVal': {
            const quoted = /^"(\{.*\})"$/.exec(csv) || [];
            assert.deepEqual(JSON.parse((quoted[1] ?? csv).replace(/""/g, '"')), value);
            break;
        }

        case 'mailTemplate':
        case 'unsafeMailTemplate':
            assert.strictEqual(csv.replace('""', ''), value.value, `Property ${prop}`);
            break;

        case 'shortRandom':
            assert.isNumber(Number(csv));
            assert.isAtMost(Number(csv), 2 ** 31 - 1);
            break;

        case 'integerRandom':
            assert.isNumber(Number(csv));
            assert.isAtMost(Number(csv), Number.MAX_SAFE_INTEGER);
            break;

        case 'integerLimitRandom':
            assert.isNumber(Number(csv));
            assert.isAtMost(Number(csv), 500);
            break;

        case 'stringPerCharacter':
        case 'urlToAnonymize':
            assert.isTrue(
                compareAnonymizedStrings(csv, value),
                `Invalid structure for Property ${prop} (${csv} -> ${value})`,
            );
            break;

        default:
            assert.strictEqual(csv, value.toString(), `Property ${prop}`);
            break;
    }
}

interface TableExportCheckResult {
    filename: string;
    content: string;
    headers: string[];
    records: Dict<any>[];
}

function checkTableExport(
    tenantId: string,
    exportId: string,
    tableName: string,
    expectedData: any[],
): TableExportCheckResult {
    const filename = fsp.join(Test.application.dir, 'data/exports', tenantId, exportId, `${kebabCase(tableName)}.csv`);

    assert.isTrue(fs.existsSync(filename));

    const content = fs.readFileSync(filename, 'utf8');
    const headers: string[] = [];
    const records = csvSyncParser.parse(content, {
        delimiter: ';',
        columns: _headers => {
            headers.push(..._headers);
            return _headers;
        },
    }) as Dict<any>[];

    assert.isNotNull(headers, 'header must be present');
    assert.strictEqual(
        records.length,
        expectedData.length,
        `exported lines of "${tableName}" must be equal to user data lines count`,
    );
    return {
        filename,
        content,
        headers,
        records,
    };
}

function checkReferenceTenantExport(tenantsSuffix: string[]): void {
    tenantsSuffix.forEach(suffix => {
        const { app } = ConfigManager.current;
        const appPrefix = app ? `${app.replace(/_/g, '-')}--` : '';
        const directoryName = `reference-${suffix}`;
        const regexFolder = new RegExp(`^${appPrefix}${directoryName}--${Test.application.rootAbout.version}--`);

        const dirName = fs
            .readdirSync(fsp.join(Test.application.dir, 'data/exports', directoryName), {
                withFileTypes: true,
            })
            .filter(dir => dir.isDirectory() && regexFolder.test(dir.name))
            .map(dir => dir.name)
            .pop();

        const exportDirName = fsp.join(Test.application.dir, 'data/exports', directoryName, dirName! || '');
        const filename = fsp.join(exportDirName, 'test-datatypes.csv');
        assert.isTrue(fs.existsSync(filename));

        const zipNameFullPath = fsp.join(Test.application.dir, 'data/exports', directoryName, `${dirName}.zip`);
        assert.isTrue(fs.existsSync(zipNameFullPath));

        const content = fs.readFileSync(filename, 'utf8');
        const lines = content.split('\n');
        const header = lines.shift();
        assert.isNotNull(header, 'header must be present');
        assert.strictEqual(lines.pop(), '', 'last line must be empty');
        assert.strictEqual(
            lines.length,
            datatypesData.length,
            'exported lines of "test_datatypes" must be equal to datatypes data lines count',
        );

        const factory = Test.application.getFactoryByName('TestDatatypes');
        const columnNames = factory.table.columns.map(c => c.columnName);
        // We should have all columns but the order may differ
        // Exclude _create_user and _update_user from both (behavior differs when running test as only or part of all tests)
        assert.deepEqual(
            header!
                .split(/;/)
                .filter(item => !['_create_user', '_update_user'].includes(item))
                .sort(),
            uniq([...columnNames, ...systemColumns])
                .filter(item => !['_create_user', '_update_user', '_layer', '_tenant_id'].includes(item))
                .sort(),
        );

        const propertyNames = header!.split(/;/).map(name => toPropertyName(name));
        lines.forEach((l, i) => {
            const csvData = l.split(/;/);
            propertyNames.forEach((p, j) => {
                testCsvData(p, (datatypesData[i] as any)[p], csvData[j]);
            });
        });

        // remove generated directory and zipFile
        const removeFolder = (p: string) => {
            if (fs.existsSync(p)) {
                fs.readdirSync(p).forEach(file => {
                    const curPath = `${p}/${file}`;
                    if (fs.lstatSync(curPath).isDirectory()) {
                        removeFolder(curPath);
                    } else {
                        fs.unlinkSync(curPath);
                    }
                });
                fs.rmdirSync(p);
            }
        };
        removeFolder(exportDirName);
        fs.unlinkSync(zipNameFullPath);
    });
}

describe('export table for reference cluster', () => {
    before(async () => {
        const application = await createApplicationWithApi({ nodes: { TestDatatypes } }, 'xtrem_data_management_test');
        ConfigManager.load(application.dir);
        await setup({ application });
        await resetSchema(application);
        await initTables([{ nodeConstructor: TestDatatypes, data: datatypesData }]);
    });

    it('can export multiple tenants data with tenant pattern', async () => {
        // TODO: initializeManager call is necessary to get the right uuid value in this test - review this later
        await Test.withUncommittedContext(context => Test.initializeManagers(context));
        // create a list of tenant with suffix as ['data', 'demo', 'pharma', 'agro']
        const excludedTables = ['test_foreign_nullable_1'];
        const [tenantId, exportId] = [Test.defaultTenantId, 'test-export'];
        await TenantService.exportTenant(Test.application, { tenantId, exportId, excludedTables });

        const zipPath = fsp.join(Test.application.dir, 'data/exports', tenantId, `${exportId}.zip`);
        let index = 2;
        const tenantsSuffix = ['data', 'demo', 'pharma', 'agro'];
        const additionalTenants: TenantInfo[] = [];
        await asyncArray(tenantsSuffix).forEach(async suffix => {
            const newTenantId = `${index}`.repeat(21);
            const newCustomerId = `${index}`.repeat(8);
            const tenant = {
                id: newTenantId,
                name: `reference-${suffix}`,
                directoryName: `reference-${suffix}`,
                customer: { id: newCustomerId, name: `Test ${newCustomerId}` },
            };
            additionalTenants.push(tenant);
            await TenantService.importTenant(Test.application, {
                tenant,
                location: zipPath,
                customer: {
                    id: newCustomerId,
                },
                keepFiles: suffix !== tenantsSuffix[tenantsSuffix.length - 1],
            });
            index += 1;
        });

        // export all tenants with glob reference-*
        tenantManagerMock.additionalTenants = additionalTenants;

        // export all tenants in the context of a single application and bizapps
        await asyncArray(['', 'xtrem_data_management_test']).forEach(async app => {
            ConfigManager.current.app = app;

            await TenantService.exportMultiTenants(Test.application, {
                excludedTables,
                tenantSelector: 'reference-*',
            });

            checkReferenceTenantExport(tenantsSuffix);
            delete ConfigManager.current.app;
        });
    });

    after(() => restoreTables());
});

describe('export datatypes table', () => {
    before(async () => {
        const application = await createApplicationWithApi({ nodes: { TestDatatypes } }, 'xtrem_data_management_test');
        await setup({ application });
        await resetSchema(application);
        await initTables([{ nodeConstructor: TestDatatypes, data: datatypesData }]);
    });

    it('can export all tenant data of tests schema', async () => {
        // exclude invalid test tables
        const excludedTables = ['test_foreign_nullable_1'];
        const [tenantId, exportId] = [Test.defaultTenantId, 'test-export'];
        await TenantService.exportTenant(Test.application, { tenantId, exportId, excludedTables });

        const { records, headers } = checkTableExport(tenantId, exportId, 'test_datatypes', datatypesData);

        const factory = Test.application.getFactoryByName('TestDatatypes');
        const columnNames = factory.table.columns.map(c => c.columnName);
        // We should have all columns but the order may differ
        // Exclude _create_user and _update_user from both (behavior differs when running test as only or part of all tests)
        assert.deepEqual(
            headers.filter(item => !['_create_user', '_update_user'].includes(item)).sort(),
            uniq([...columnNames, ...systemColumns])
                .filter(item => !['_create_user', '_update_user', '_layer', '_tenant_id'].includes(item))
                .sort(),
        );

        testRecords(records, headers, datatypesData);
    });
    after(() => restoreTables());
});

describe('export datatypes table in demo tenant', () => {
    before(async () => {
        const application = await createApplicationWithApi(
            { nodes: { TestDatatypes }, serviceOptions },
            'xtrem_data_management_test',
        );
        await setup({ application });
        await resetSchema(application);
        await initTables([{ nodeConstructor: TestDatatypes, data: datatypesData }]);
    });

    it('can export when isDemoTenant not active', () =>
        Test.withContext(
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            async context => {
                // exclude invalid test tables
                const excludedTables = ['test_foreign_nullable_1'];
                const [tenantId, exportId] = [Test.defaultTenantId, 'test-export-demo'];
                await TenantService.exportTenant(Test.application, { tenantId, exportId, excludedTables });

                // All users are exported. The isFirstAdminUser is set to false by the export
                checkTableExport(tenantId, exportId, 'test_user', userData);
            },
        ));

    it('can export when isDemoTenant active', async () => {
        await Test.withContext(async context => {
            const isDemoTenantOption = fixtures.serviceOptions.isDemoTenant;
            await Test.application.serviceOptionManager.activateServiceOptions(context, [isDemoTenantOption]);
            try {
                // exclude invalid test tables
                const excludedTables = ['test_foreign_nullable_1'];
                const [tenantId, exportId] = [Test.defaultTenantId, 'test-export-demo'];
                await TenantService.exportTenant(Test.application, { tenantId, exportId, excludedTables });

                checkTableExport(
                    tenantId,
                    exportId,
                    'test_user',
                    userData.filter(u => u.isDemoPersona || u.userType === 'system'),
                );
            } finally {
                await Test.application.serviceOptionManager.deactivateServiceOptions(context, [isDemoTenantOption]);
            }
        });
    });

    after(() => restoreTables());
});

describe('export self referencing table', () => {
    before(async () => {
        await setup({
            application: await createApplicationWithApi(
                { nodes: { TestSelfReferencing, TestSimpleNode } },
                'xtrem_data_management_test',
            ),
        });
        await initTables([{ nodeConstructor: TestSelfReferencing, data: [] }]);
        // initTables is not smart enough to import self referencing data so we import from a layer
        await loadLayersFromCsvFiles(Test.application, ['setup'], Test.defaultTenantId, {
            onlyFactories: [Test.application.getFactoryByConstructor(TestSelfReferencing)],
        });
    });

    function parseCsv(filename: string): Promise<any[]> {
        return new Promise<any[]>((resolve, reject) => {
            fs.createReadStream(filename).pipe(
                csvParser.parse({ delimiter: ';', columns: true }, (err, records) => {
                    if (err) {
                        return reject(err);
                    }
                    return resolve(records);
                }),
            );
        });
    }

    it('can export tenant data with self references', async () => {
        const [tenantId, exportId] = [Test.defaultTenantId, 'test-export'];
        await TenantService.exportTenant(Test.application, { tenantId, exportId });

        const outputDir = fsp.join(Test.application.dir, 'data/exports', tenantId, exportId);

        const filename1 = fsp.join(outputDir, 'test-self-referencing.csv');
        assert.isTrue(fs.existsSync(filename1));

        const results1 = (await parseCsv(filename1)).map((r: any) => pick(r, ['_id', 'ref', 'value']));
        assert.deepEqual(results1, [
            { _id: '1', ref: '1', value: '10' },
            { _id: '2', ref: '2', value: '20' },
            { _id: '3', ref: '3', value: '30' },
        ]);

        const filename2 = fsp.join(outputDir, 'test-self-referencing--nullable.csv');
        assert.isTrue(fs.existsSync(filename2));

        const results2 = (await parseCsv(filename2)).map(r => pick(r, ['_id', 'ref']));
        assert.deepEqual(results2, [
            { _id: '1', ref: '1' },
            { _id: '2', ref: '3' },
            { _id: '3', ref: '2' },
        ]);
    });

    after(() => restoreTables());
});

describe('Export TestExportValue with default so sensitive data not exported', () => {
    before(async () => {
        const application = await createApplicationWithApi(
            { nodes: { TestExportValue } },
            'xtrem_data_management_test',
        );
        await setup({ application });
        await resetSchema(application);
        await initTables([
            {
                nodeConstructor: TestExportValue,
                data: testExportValueData,
            },
        ]);
    });

    it('Can export Node with exportValue properties not exported by default', async () => {
        const [tenantId, exportId, keepAllValues] = [Test.defaultTenantId, 'test-export', false];
        await TenantService.exportTenant(Test.application, { tenantId, exportId, keepAllValues });

        const { records, headers } = checkTableExport(tenantId, exportId, 'test_export_value', testExportValueData);
        testRecords(records, headers, testExportValueDataHidden);
    });
    after(() => restoreTables());

    it('Can export Node with keepAllValues to export properties with exportValue', async () => {
        const [tenantId, exportId, keepAllValues] = [Test.defaultTenantId, 'test-export', true];
        await TenantService.exportTenant(Test.application, { tenantId, exportId, keepAllValues });

        const { records, headers } = checkTableExport(tenantId, exportId, 'test_export_value', testExportValueData);
        testRecords(records, headers, testExportValueData);
    });
    after(() => restoreTables());
});

describe('Test anonymize export', () => {
    before(async () => {
        const application = await createApplicationWithApi(
            { nodes: { TestAnonymizeExport } },
            'xtrem_data_management_test',
        );
        await setup({ application });
        await resetSchema(application);
        await initTables([
            {
                nodeConstructor: TestAnonymizeExport,
                data: testAnonymizeData,
            },
        ]);
    });

    it('Can export Node without anonymizing data', async () => {
        const [tenantId, exportId, anonymize] = [Test.defaultTenantId, 'test-export', false];
        await TenantService.exportTenant(Test.application, { tenantId, exportId, anonymize });

        const { records, headers } = checkTableExport(tenantId, exportId, 'test_anonymize_export', testAnonymizeData);
        testRecords(records, headers, testAnonymizeData);
    });

    it('Can export Node and anonymize data', async () => {
        const [tenantId, exportId, anonymize] = [Test.defaultTenantId, 'test-export', true];
        await TenantService.exportTenant(Test.application, { tenantId, exportId, anonymize });
        const { records, headers } = checkTableExport(tenantId, exportId, 'test_anonymize_export', testAnonymizeData);
        testRecords(records, headers, testAnonymizeHidden);
    });
    after(() => restoreTables());
});
