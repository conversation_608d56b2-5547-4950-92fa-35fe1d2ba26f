import {
    dateRange,
    datetimeRange,
    decimalRange,
    decorators,
    integerRange,
    Node,
    Reference,
    StringDataType,
    Test,
} from '@sage/xtrem-core';
import { fixtures } from '../fixtures';

const { createApplicationWithApi, initTables, restoreApplication, setup } = fixtures;

const {
    TestAnimal,
    TestAnimalLine,
    TestBase,
    TestBaseCollectionElement,
    TestBaseReference,
    TestCat,
    TestDog,
    TestExtensionReference,
    TestFish,
    TestMammal,
    TestPetOwner,
    TestSleepBehavior,
    TestFlyBehavior,
    TestRefPropNoDataType,
    TestRefPropWithDataType,
} = fixtures.nodes;

const { BaseExtension1, TestBaseExtension2 } = fixtures.nodeExtensions;

@decorators.node<TestNewNode1>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true, isNaturalKey: true }],
    isSetupNode: true,
    isSharedByAllTenants: true,
})
class TestNewNode1 extends Node {
    @decorators.stringProperty<TestNewNode1, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 10 }),
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestNewNode1, 'reference'>({
        isPublished: true,
        isStored: true,
        node: () => TestNewNode2,
        isNullable: true,
    })
    readonly reference: Reference<TestNewNode2 | null>;
}

@decorators.node<TestNewNode2>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
    isSharedByAllTenants: true,
})
class TestNewNode2 extends Node {
    @decorators.stringProperty<TestNewNode2, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 10 }),
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestNewNode2, 'reference'>({
        isPublished: true,
        isStored: true,
        node: () => TestNewNode3,
    })
    readonly reference: Reference<TestNewNode3>;

    @decorators.stringProperty<TestNewNode2, 'testColumn'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 20 }),
    })
    readonly testColumn: Promise<string>;
}

@decorators.node<TestNewNode3>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
    isSharedByAllTenants: true,
})
class TestNewNode3 extends Node {
    @decorators.stringProperty<TestNewNode3, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 10 }),
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestNewNode3, 'reference'>({
        isPublished: true,
        isStored: true,
        node: () => TestNewNode1,
    })
    readonly reference: Reference<TestNewNode1>;
}

@decorators.node<TestNewNode4>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
    isSharedByAllTenants: true,
})
class TestNewNode4 extends Node {
    @decorators.stringProperty<TestNewNode4, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 10 }),
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestNewNode4, 'field'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 10 }),
    })
    readonly field: Promise<string>;
}

@decorators.node<TestNewNode6>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
class TestNewNode6 extends Node {
    @decorators.stringProperty<TestNewNode6, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 10 }),
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestNewNode6, 'field'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 10 }),
    })
    readonly field: Promise<string>;
}

@decorators.node<TestLoadData1>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true, isNaturalKey: true }],
    isSharedByAllTenants: true,
    isSetupNode: true,
})
class TestLoadData1 extends Node {
    @decorators.stringProperty<TestLoadData1, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 20 }),
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestLoadData1, 'text'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 30 }),
    })
    readonly text: Promise<string>;
}

@decorators.node<TestLoadData2>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
    isSharedByAllTenants: true,
})
class TestLoadData2 extends Node {
    @decorators.stringProperty<TestLoadData2, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 20 }),
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestLoadData2, 'text'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 30 }),
    })
    readonly text: Promise<string>;
}

@decorators.node<TestLoadData3>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
    isSharedByAllTenants: true,
})
class TestLoadData3 extends Node {
    @decorators.stringProperty<TestLoadData3, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 20 }),
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestLoadData3, 'text'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 30 }),
    })
    readonly text: Promise<string>;
}

@decorators.node<TestLoadRangeData>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
})
class TestLoadRangeData extends Node {
    @decorators.integerRangeProperty<TestLoadRangeData, 'integerRangeVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly integerRangeVal: Promise<integerRange | null>;

    @decorators.decimalRangeProperty<TestLoadRangeData, 'decimalRangeVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly decimalRangeVal: Promise<decimalRange | null>;

    @decorators.dateRangeProperty<TestLoadRangeData, 'dateRangeVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly dateRangeVal: Promise<dateRange | null>;

    @decorators.datetimeRangeProperty<TestLoadRangeData, 'datetimeRangeVal'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly datetimeRangeVal: Promise<datetimeRange | null>;
}

const api = {
    nodes: {
        TestBase,
        TestExtensionReference,
        TestBaseReference,
        TestBaseCollectionElement,
        TestAnimal,
        TestAnimalLine,
        TestMammal,
        TestCat,
        TestDog,
        TestFish,
        TestPetOwner,
        TestNewNode4,
        TestNewNode6,
        TestSleepBehavior,
        TestFlyBehavior,
        TestRefPropNoDataType,
        TestRefPropWithDataType,
    },
    nodeExtensions: { BaseExtension1, TestBaseExtension2 },
};

describe('Manage application', () => {
    describe('Manage application', () => {
        before(async () => {
            await setup({ application: await createApplicationWithApi(api, 'xtrem_data_management_test') });
            await initTables([]);
        });

        beforeEach(async () => {
            await restoreApplication(api, 'xtrem_data_management_test');
            const tables = Test.application.getSqlPackageFactories().map(factory => ({
                nodeConstructor: factory.nodeConstructor,
                data: [],
            }));
            await initTables(tables);
        });

        it('creates an admin user for test application', () =>
            (() =>
                Test.application.createAdminUser(
                    Test.defaultTenantId,
                    {
                        email: '<EMAIL>',
                        firstName: 'Joe',
                        lastName: 'Admin',
                        locale: 'en-US',
                    },
                    { skipWelcomeEmail: true },
                ))());

        after(async () => {
            await initTables([]);
            await restoreApplication(api, 'xtrem_data_management_test');
        });
    });

    describe('Loading of application data', () => {
        const newApi = {
            ...api,
            nodes: { ...api.nodes, TestLoadData1, TestLoadData2, TestLoadData3, TestLoadRangeData },
        };
        before(async () => {
            await setup({ application: await createApplicationWithApi(newApi, 'xtrem_data_management_test') });
            await initTables([
                // { nodeConstructor: TestLoadData1, data: [{}] },
                { nodeConstructor: TestLoadData1, data: [] },
                { nodeConstructor: TestLoadData2, data: [] },
                { nodeConstructor: TestLoadData3, data: [] },
                { nodeConstructor: TestLoadRangeData, data: [] },
            ]);
        });

        after(async () => {
            await restoreApplication(api, 'xtrem_data_management_test');
        });
    });
});
