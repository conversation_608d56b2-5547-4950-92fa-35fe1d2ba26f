import { Application, BinaryStream, Test, TextStream } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as fs from 'fs';
import * as fsp from 'path';
import { AnonymizationService } from '../../lib/anonymization/anonymization-service';
import { fixtures } from '../fixtures';
import { resetSchema } from './tenant-test-utils';

const { createApplicationWithApi, initTables, restoreTables, setup } = fixtures;
const { TestAnonymizeExport } = fixtures.nodes;

const testAnonymizeData = [
    {
        _id: 1,
        _sourceId: '',
        code: '001',
        stringFixed: 'Sensitive should not export',
        stringHash: 'To be hashed',
        stringShortHash: 'Short hash',
        stringHashLimit: 'Limit hash',
        integerRandom: 10,
        shortRandom: 11,
        integerLimitRandom: 12,
        stringCustom: 'Sensitive custom string',
        stringPerCharacter: '<EMAIL>',
        urlToAnonymize: 'https://someurl.com',
        pdf: null,
        image: null,
        textStream: TextStream.empty,
        conditionalTextStream: TextStream.empty,
    },
    {
        _id: 2,
        _sourceId: '',
        code: '002',
        stringFixed: 'Also should not export',
        stringHash: 'Another hash',
        stringShortHash: 'Another',
        stringHashLimit: 'Another limit',
        integerRandom: 20,
        shortRandom: 21,
        integerLimitRandom: 22,
        stringCustom: 'gnirts motsuc evitisneS',
        stringPerCharacter: '012-345-6789',
        urlToAnonymize: 'http://anotherurl.com',
        pdf: BinaryStream.fromBuffer(Buffer.from('Some not null value', 'base64')),
        image: BinaryStream.fromBuffer(Buffer.from('Another not null value', 'base64')),
        textStream: TextStream.empty,
        conditionalTextStream: TextStream.empty,
    },
    {
        _id: 3,
        _sourceId: '',
        code: '003',
        stringFixed: 'More sensitive data',
        stringHash: 'Hash3',
        stringShortHash: 'More',
        stringHashLimit: 'Limit',
        integerRandom: 30,
        shortRandom: 31,
        integerLimitRandom: 32,
        stringCustom: 'AbcDef',
        stringPerCharacter: "Abc-123>['\\]$<",
        urlToAnonymize: 'www.test.com',
        pdf: null,
        image: null,
        textStream: TextStream.fromString('Abc123'),
        conditionalTextStream: TextStream.fromString('Abc123'),
    },
];

const rulesArray = [
    '-- Anonymization rules',
    '-- test_anonymize_export',
    "SECURITY LABEL FOR anon ON COLUMN xtrem_data_management_test.test_anonymize_export.string_fixed IS E'MASKED WITH VALUE CASE WHEN COALESCE(\"string_fixed\", \\'\\') = \\'\\' THEN \\'\\' ELSE \\'FixedValue\\' END';",
    "SECURITY LABEL FOR anon ON COLUMN xtrem_data_management_test.test_anonymize_export.string_hash IS E'MASKED WITH VALUE CASE WHEN COALESCE(\"string_hash\", \\'\\') = \\'\\' THEN \\'\\' ELSE anon.random_string(25) END';",
    "SECURITY LABEL FOR anon ON COLUMN xtrem_data_management_test.test_anonymize_export.string_short_hash IS E'MASKED WITH VALUE CASE WHEN COALESCE(\"string_short_hash\", \\'\\') = \\'\\' THEN \\'\\' ELSE anon.random_string(5) END';",
    "SECURITY LABEL FOR anon ON COLUMN xtrem_data_management_test.test_anonymize_export.string_hash_limit IS E'MASKED WITH VALUE CASE WHEN COALESCE(\"string_hash_limit\", \\'\\') = \\'\\' THEN \\'\\' ELSE anon.random_string(15) END';",
    "SECURITY LABEL FOR anon ON COLUMN xtrem_data_management_test.test_anonymize_export.integer_random IS E'MASKED WITH VALUE CASE WHEN COALESCE(\"integer_random\", \\'\\') = \\'\\' THEN 0 ELSE FLOOR(RANDOM()*9007199254740991)::BIGINT END';",
    "SECURITY LABEL FOR anon ON COLUMN xtrem_data_management_test.test_anonymize_export.integer_limit_random IS E'MASKED WITH VALUE CASE WHEN COALESCE(\"integer_limit_random\", \\'\\') = \\'\\' THEN 0 ELSE FLOOR(RANDOM()*500)::BIGINT END';",
    "SECURITY LABEL FOR anon ON COLUMN xtrem_data_management_test.test_anonymize_export.short_random IS E'MASKED WITH VALUE CASE WHEN COALESCE(\"short_random\", \\'\\') = \\'\\' THEN 0 ELSE FLOOR(RANDOM()*2147483647)::INTEGER END';",
    "SECURITY LABEL FOR anon ON COLUMN xtrem_data_management_test.test_anonymize_export.string_per_character IS 'MASKED WITH FUNCTION xtrem_data_management_test.per_char_random(string_per_character)';",
    "SECURITY LABEL FOR anon ON COLUMN xtrem_data_management_test.test_anonymize_export.url_to_anonymize IS 'MASKED WITH FUNCTION xtrem_data_management_test.anonymize_website(url_to_anonymize)';",
    "SECURITY LABEL FOR anon ON COLUMN xtrem_data_management_test.test_anonymize_export.pdf IS 'MASKED WITH FUNCTION xtrem_data_management_test.anonymize_binary(''pdf'')';",
    "SECURITY LABEL FOR anon ON COLUMN xtrem_data_management_test.test_anonymize_export.image IS 'MASKED WITH FUNCTION xtrem_data_management_test.anonymize_binary(''image'')';",
    "SECURITY LABEL FOR anon ON COLUMN xtrem_data_management_test.test_anonymize_export.text_stream IS E'MASKED WITH VALUE CASE WHEN COALESCE(\"text_stream\", \\'\\') = \\'\\' THEN \\'\\' ELSE \\'This text was anonymized.\\' END';",
];

function checkRulesExport(application: Application) {
    const rulesFilename = fsp.join(
        __dirname,
        '../../data/exports/pg-anonymizer',
        `pg-anon-rules-v${application.version}.sql`,
    );
    const rulesTableArray = fs.readFileSync(rulesFilename).toString().split('\n');
    const rulesTable = rulesTableArray.slice(
        rulesTableArray.findIndex(element => element.includes('-- Anonymization rules')),
    );

    assert.deepEqual(rulesTable, rulesArray);
}

describe('Test anonymize export', () => {
    before(async () => {
        const application = await createApplicationWithApi(
            { nodes: { TestAnonymizeExport } },
            'xtrem_data_management_test',
        );
        await setup({ application });
        await resetSchema(application);
        await initTables([
            {
                nodeConstructor: TestAnonymizeExport,
                data: testAnonymizeData,
            },
        ]);
    });

    it('Can export Node without anonymizing data', async () => {
        await AnonymizationService.generateRules(Test.application, '');

        checkRulesExport(Test.application);
    });
    after(() => restoreTables());
});
