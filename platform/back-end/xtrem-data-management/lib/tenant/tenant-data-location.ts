import { ConfigManager, Datetime } from '@sage/xtrem-core';
import * as fsp from 'path';
import { TenantDataUtils } from './tenant-data-utils';

/**
 * Options for configuring the tenant data location.
 */
export interface TenantDataLocationOptions {
    tenantId: string;
    location?: string;
    exportId?: string;
    directoryName?: string;
    rootDir?: string;
    noTimestamp?: boolean;
    version?: string;
}

/**
 * Represents the location of tenant data for export/import management.
 */
export class TenantDataLocation {
    #appPrefix: string | undefined;

    readonly #bucketName: string | undefined;

    readonly #pathPrefix: string | undefined;

    readonly #tenantExportDumpRoot: string;

    readonly #defaultExportName: string;

    /**
     * Creates a new instance of TenantDataLocation.
     * @param options - The options for configuring the tenant data location.
     */
    constructor(private readonly options: TenantDataLocationOptions) {
        const { location, rootDir, version, noTimestamp } = options;
        [this.#bucketName, this.#pathPrefix] = (/^s3:\/\/([^/]+)(?:\/(.+))?/i.exec(location ?? '') ?? []).slice(1);
        this.#tenantExportDumpRoot = fsp.join(rootDir || '.', 'data/exports', this.directoryName);
        this.#defaultExportName = '';
        if (version && this.directoryName) {
            // Format the timestamp in UTC
            const timeStamp = noTimestamp ? '' : `--${Datetime.now().format(undefined, 'YYYY-MM-DD-HH-mm-ssZ')}`;
            this.#defaultExportName = `${this.directoryName}--${version}${timeStamp}`;
        }
    }

    /**
     * Gets the location of the tenant data.
     */
    get location(): string | undefined {
        return this.options.location;
    }

    /**
     * Gets the app prefix.
     */
    get appPrefix(): string {
        if (this.#appPrefix) return this.#appPrefix;
        const { app } = ConfigManager.current;
        this.#appPrefix = app ? `${app.replace(/_/g, '-')}--` : '';
        return this.#appPrefix;
    }

    /**
     * Gets the directory name (tenant friendly name).
     */
    get directoryName(): string {
        return this.options.directoryName || this.options.tenantId;
    }

    /**
     * Gets the bucket name.
     */
    get bucketName(): string {
        return this.#bucketName ?? '';
    }

    /**
     * Gets the path prefix of the S3 key.
     */
    get pathPrefix(): string | undefined {
        return this.#pathPrefix;
    }

    /**
     * Gets the default export name.
     */
    get defaultExportName(): string {
        return this.#defaultExportName;
    }

    /**
     * Gets the export name.
     */
    get exportName(): string {
        if (this.options.exportId) {
            return this.options.exportId;
        }
        const exportName = this.defaultExportName || this.options.tenantId;
        return `${this.appPrefix}${exportName}`;
    }

    /**
     * Gets the location where the export files will be generated before zipping.
     */
    get exportDumpPath(): string {
        return `${this.#tenantExportDumpRoot}/${this.exportName}`;
    }

    /**
     * Gets the export filename.
     */
    get exportFilename(): string {
        return `${this.exportName}.zip`;
    }

    /**
     * Gets the full name of the generated zip file on the local filesystem.
     */
    get zipTarget(): string {
        return `${this.#tenantExportDumpRoot}/${this.exportFilename}`;
    }

    /**
     * Gets the zip key for S3.
     */
    get zipKey(): string {
        const { directoryName, exportId, tenantId } = this.options;

        // Compute the s3 key following these rules (dicted by cirrus):
        // v1 (single app)
        //    - tenant        => location/tenant_id/export_id/export_id.zip
        //    - reference-xxx => location/reference-xxx/reference-xxx-40.0.40-date.zip
        // v2 (multi-apps)
        //    - tenant        => location/export_id.zip
        //    - reference-xxx => location/reference-xxx/app-reference-xxx-40.0.40-date.zip
        let subPath = '';
        if (exportId) {
            subPath = this.appPrefix ? '' : `${tenantId}/${exportId}/`;
        } else {
            subPath = `${directoryName}/`;
        }
        return `${this.pathPrefix || 'exports'}/${subPath}${this.exportFilename}`;
    }

    /**
     * Checks if the location is an S3 URI.
     * @returns True if the location is an S3 URI, false otherwise.
     */
    isS3Uri(): boolean {
        const { location } = this.options;
        return location != null && TenantDataUtils.isS3Uri(location);
    }

    /**
     * Checks the S3 location.
     * @throws Error if the location is not a valid S3 URI or bucket name is not resolved.
     */
    checkS3Location(): void {
        const { location } = this.options;
        if (!this.isS3Uri()) {
            throw new Error(`Bad location protocol, only 's3://' is supported: ${location}`);
        }
        if (!this.bucketName) throw new Error(`Bad location format: ${location}`);
    }
}
