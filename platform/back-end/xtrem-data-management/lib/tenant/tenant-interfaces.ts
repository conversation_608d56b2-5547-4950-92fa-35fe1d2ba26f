import { CopyOptions } from '@sage/xtrem-postgres';
import { TableInfo } from './schema-info';

/** @internal */
export interface TenantExportOptionsBase {
    exportId?: string;
    rootDir?: string;
    location?: string;
    excludedTables?: string[];
    directoryName?: string;
    diffCompliant?: boolean;
    noTimestamp?: boolean;
    keepAllValues?: boolean;
    anonymize?: boolean;
    keepFiles?: boolean;
    /** Max file size for export in megabytes. If exceeded, the export will be split into files up to this size. */
    maxFileSizeInMb?: number;
}

/** @internal */
export interface TenantExportOptions extends TenantExportOptionsBase {
    tenantId: string;
}

/** @internal */
export interface MultiTenantsExportOptions extends TenantExportOptionsBase {
    tenantSelector: string; // can be tenantId, tenantDirectoryName
}

/** @internal */
export interface TenantImportConfig {
    tenant: {
        id: string;
        name?: string;
    };
    location: string;
    hasTenantIncluded?: boolean;
    customer: {
        id: string;
        name?: string;
    };
    chunkSize?: number;
    keepFiles?: boolean;
}

/** @internal */
export interface ResetTenantOperationConfig {
    tenant: {
        id: string;
    };
}

/** @internal */
export interface TenantTableMetadata {
    name: string;
    dependsOn?: string[];
    isShared?: boolean;
}

/** @internal */
export interface TenantPackageMetadata {
    name: string;
    version: string;
}

/** @internal */
export interface TenantDataMetadata {
    exportId: string;
    tenantId?: string;
    directoryName?: string;
    tables?: TableInfo[];
    packages?: TenantPackageMetadata[];
    files?: string[];
}

/** @internal */
export interface TenantImportAnatomy {
    dir: string;
    metadata: TenantDataMetadata;
}

/** @internal */
export interface TenantDataOptions {
    rootPath: string;
    filename: string;
    metadata?: TenantDataMetadata;
    copy?: CopyOptions;
}

/** @internal */
export interface ColumnsDistribution {
    keyColumns?: string[];
    nullableColumns?: string[];
    notNullColumns?: string[];
}
