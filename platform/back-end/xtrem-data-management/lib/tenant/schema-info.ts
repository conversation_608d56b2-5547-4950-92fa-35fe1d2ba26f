import { Dict, parseIndexDefinition, SchemaSqlContext } from '@sage/xtrem-core';
import { topoSort } from '@sage/xtrem-toposort';
import { assert } from 'console';
import { loggers } from '../util/loggers';

/** @internal */
export interface TableInfo {
    name: string;
    isShared?: boolean;
    columns: ColumnInfo[];
    columnsByName: Dict<ColumnInfo>;
    indexes: IndexInfo[];
    dependsOn?: string[];
    dependsOnByName?: Dict<boolean>;
    weakDependencies?: Dict<string[]>;
    isTemp?: boolean;
}

/** @internal */
export interface ColumnInfo {
    name: string;
    position?: number;
    dataType: string;
    isNullable?: boolean;
    isInUniqueIndex?: boolean;
    isReference?: boolean;
    /**
     * When isReference is true, the name of the referenced table
     */
    targetTableName?: string;
    isSelfReference?: boolean;
}

/** @internal */
export interface IndexInfo {
    name: string;
    isUnique: boolean;
    columnNames: string[];
}

/** @internal */
export interface SchemaInfoOptions {
    excludedTables?: string[];
    includeAll?: boolean;
}

// TODO: maybe move to lib/sql/sql-context
/** @internal */
export class SchemaInfo extends SchemaSqlContext {
    private async topoSort(allTables: Dict<TableInfo>): Promise<TableInfo[]> {
        const sqlPool = this.connectionPool;

        const sql = `
        SELECT conrelid::regclass AS table_name
             , conname as constraint_name
             , pg_get_constraintdef(oid) as constraint_def
        FROM   pg_constraint
        WHERE  contype IN ('f')
        AND    connamespace = '${this.schemaName}'::regnamespace
        ORDER  BY conrelid::regclass::text, contype DESC;`;

        loggers.sql.info(`Getting all FK of ${this.schemaName}...`);
        const t0 = Date.now();
        let count = 0;
        await sqlPool.withConnection(cnx =>
            sqlPool.createReader(cnx, sql, []).forEach((r: any) => {
                count += 1;
                const tableName = r.table_name.split('.').pop().replaceAll('"', '');
                const table = allTables[tableName];
                // table may have been excluded
                if (!table) return;
                // Extract the FK info from its definition, ex: "FOREIGN KEY (_tenant_id, _create_user) REFERENCES xtrem."user"(_tenant_id, _id) ON DELETE RESTRICT"
                // \(([a-z_"][\w_"]*(?:,\s[a-z_"][\w_"]*)*)?\)                                         => FK columns
                // (?:[a-z_"][\w_"]*\.)?([a-z_"][\w_"]*)\s*\(([a-z_][\w_"]*(?:,\s[a-z_"][\w_"]*)*)?\)  => ref table (may be prefixed by schema name)
                // \(([a-z_][\w_"]*(?:,\s[a-z_"][\w_"]*)*)?\)                                          => referenced columns (not used for now)
                // (.*)                                                                                => the rest of the FK definition (not used for now)
                const parsed =
                    /FOREIGN KEY\s*\(([a-z_"][\w_"]*(?:,\s[a-z_"][\w_"]*)*)?\)\s*REFERENCES\s+(?:[a-z_"][\w_"]*\.)?([a-z_"][\w_"]*)\s*\(([a-z_][\w_"]*(?:,\s[a-z_"][\w_"]*)*)?\)\s*(.*)/.exec(
                        r.constraint_def,
                    ) || [];
                const [columns, referenceTable] = parsed.slice(1, -2).map(v => v.replaceAll('"', ''));
                const columnNames = columns.split(/,\s*/).filter(n => n !== '_tenant_id');
                columnNames.forEach(colName => {
                    const col = table.columnsByName[colName];
                    assert(col);
                    col.isReference = true;
                    assert(!col.targetTableName || col.targetTableName === referenceTable);
                    col.targetTableName = referenceTable;
                    // do not include nullable or self reference to avoid cycles
                    if ((col.isNullable && !col.isInUniqueIndex) || col.isSelfReference) {
                        const weakDependencies = table.weakDependencies || (table.weakDependencies = {});
                        weakDependencies[referenceTable] = weakDependencies[referenceTable] || [];
                        weakDependencies[referenceTable].push(col.name);
                        return;
                    }
                    table.dependsOn = table.dependsOn || [];
                    table.dependsOnByName = table.dependsOnByName || {};
                    if (!table.dependsOnByName[referenceTable]) {
                        table.dependsOnByName[referenceTable] = true;
                        table.dependsOn.push(referenceTable);
                    }
                });
            }),
        );
        loggers.sql.info(`Process ${count} FK of ${this.schemaName} in ${Date.now() - t0} ms`);

        const t2 = Date.now();
        try {
            loggers.sql.info(`Sorting tables of ${this.schemaName}...`);
            return topoSort(Object.values(allTables));
        } finally {
            loggers.sql.info(`Sorted tables of ${this.schemaName} in ${Date.now() - t2} ms`);
        }
    }

    async getSortedTables(options?: SchemaInfoOptions): Promise<TableInfo[]> {
        const sqlPool = this.connectionPool;
        const sql = `
SELECT table_name,
       ordinal_position as position,
       column_name,
       data_type,
       is_nullable,
       column_default
  FROM information_schema.columns
 WHERE table_schema = '${this.schemaName}'
 ORDER BY table_name,
          ordinal_position
`;
        const tables = {} as Dict<TableInfo>;
        let count = 0;

        const excludes = options?.excludedTables?.reduce((r, name) => {
            r[name] = true;
            return r;
        }, {} as Dict<boolean>);
        loggers.sql.info(`Getting all columns of ${this.schemaName}...`);
        const t0 = Date.now();
        await sqlPool.withConnection(cnx =>
            sqlPool.createReader(cnx, sql, []).forEach((r: any) => {
                if (excludes?.[r.table_name]) return;
                count += 1;
                const table =
                    tables[r.table_name] ||
                    (tables[r.table_name] = {
                        name: r.table_name,
                        columns: [],
                        indexes: [],
                        columnsByName: {},
                    });
                const col: ColumnInfo = {
                    position: r.position,
                    name: r.column_name,
                    dataType: r.data_type,
                    isNullable: r.is_nullable === 'YES',
                    isSelfReference:
                        r.column_default ===
                            `currval((pg_get_serial_sequence('${this.schemaName}.${r.table_name}'::text, '_id'::text))::regclass)` ||
                        r.column_default ===
                            `currval((pg_get_serial_sequence('${r.table_name}'::text, '_id'::text))::regclass)`,
                };
                if (col.name === 'tenant_id') table.isShared = table.isShared ?? true;
                if (col.name === '_tenant_id') table.isShared = false;
                table.columns.push(col);
                table.columnsByName[col.name] = col;
            }),
        );
        loggers.sql.info(`Got ${count} columns of ${this.schemaName} in ${Date.now() - t0} ms`);

        await this.fillIndexes(tables);
        return this.topoSort(tables);
    }

    private async fillIndexes(tables: Dict<TableInfo>): Promise<void> {
        const sqlPool = this.connectionPool;
        const sql = `
SELECT c.relname AS table_name,
       i.relname AS index_name,
       x.indisunique AS is_unique,
       pg_get_indexdef(i.oid) AS index_def
  FROM pg_index x
    JOIN pg_class c ON c.oid = x.indrelid
    JOIN pg_class i ON i.oid = x.indexrelid
    LEFT JOIN pg_namespace n ON n.oid = c.relnamespace
    LEFT JOIN pg_tablespace t ON t.oid = i.reltablespace
 WHERE (c.relkind = ANY (ARRAY['r'::"char", 'm'::"char"])) AND i.relkind = 'i'::"char"
       AND n.nspname = '${this.schemaName}'`;

        let count = 0;

        loggers.sql.info(`Getting all indexes of ${this.schemaName}...`);
        const t0 = Date.now();
        await sqlPool.withConnection(cnx =>
            sqlPool.createReader(cnx, sql, []).forEach((r: any) => {
                count += 1;
                const columnNames = parseIndexDefinition(r.index_name, r.index_def).map(c => c.name);
                const table = tables[r.table_name];
                if (table == null) return;
                table.indexes.push({
                    name: r.index_name,
                    isUnique: r.is_unique,
                    columnNames,
                });
                if (r.is_unique)
                    columnNames.forEach(c => {
                        const col = table.columnsByName[c];
                        if (col) col.isInUniqueIndex = true;
                    });
            }),
        );
        loggers.sql.info(`Got ${count} indexes of ${this.schemaName} in ${Date.now() - t0} ms`);
    }
}
