import { Application, asyncArray, Context, Datetime, garbageCollectContentAddressableTables } from '@sage/xtrem-core';
import { loggers } from '../util/loggers';
import { TenantDataExport } from './tenant-data-export';
import { TenantDataImport } from './tenant-data-import';
import { ResetTenantOperationConfig, TenantExportOptions, TenantImportConfig } from './tenant-interfaces';
import { TenantService } from './tenant-service';

const logger = loggers.tenant;

/** @internal */
export class TenantDataManager {
    /**
     * Delete all tenant data of `tenantId`
     * @param tenantId
     */
    static async deleteTenant(application: Application, tenantId: string): Promise<void> {
        logger.warn(`Delete all the records for tenant ${tenantId} on schema ${application.schemaName}`);
        await Context.tenantManager.deleteTenant(application, tenantId);
    }

    /**
     * Export all tenant data of `tenantId` using a 2 files:
     *   - File 1 - partial file with all columns except nullable references
     *   - File 2 - 2nd partial file with all nullable reference columns
     * @param tenantId
     */
    static async exportTenant(application: Application, exportConfig: TenantExportOptions): Promise<void> {
        await application.withReadonlyContext(
            exportConfig.tenantId,
            context => new TenantDataExport(exportConfig).export(context),
            { description: () => `ExportTenant(${exportConfig.tenantId})`, isolationLevel: 'medium' },
        );
    }

    /**
     * Check if the tenant already exists, if it does then throw an error.
     * @param context
     */
    private static async checkTenantExists(context: Context, tenantId: string): Promise<void> {
        const tenantList = await Context.tenantManager.listTenantsIds(context);
        if (tenantList.includes(tenantId)) throw new Error(`Tenant ${tenantId} already exists.`);
    }

    /**
     * Import the exported data of a tenant via an S3 URI or a path to zip
     * @param importConfig
     * @returns
     */
    static async importTenant(application: Application, importConfig: TenantImportConfig): Promise<void> {
        // We create the customer, tenant and root user in a first pass as we will need the root user available when loading
        // the other CSV data
        await application.asRoot.withCommittedContext(
            importConfig.tenant.id,
            async context => {
                await this.checkTenantExists(context, importConfig.tenant.id);

                await TenantService.initTenantIdentifiers(
                    context,
                    importConfig.customer.id,
                    importConfig.customer.name!,
                    importConfig.tenant.id,
                    importConfig.tenant.name!,
                );
            },
            {
                withoutTransactionUser: true,
                description: () => `initTenantIdentifiers(${importConfig.tenant.id})`,
            },
        );

        try {
            await new TenantDataImport(application, importConfig).import();
        } catch (error) {
            logger.error(error.stack);
            // Delete tenant so that it can be imported again later
            await TenantService.deleteTenant(application, importConfig.tenant.id);
            throw error;
        }
    }

    /**
     * Reset the documents bound to a tenant (salesOrders, purchaseOrders, ...).
     * This will not reset the whole tenant (for instance, the items will be kept).
     * Only the factories flagged as 'isClearedByReset' will be reset.
     */
    static async resetTenantDocuments(
        application: Application,
        resetTenantConfig: ResetTenantOperationConfig,
    ): Promise<void> {
        await application.asRoot.withCommittedContext(
            resetTenantConfig.tenant.id,
            async context => {
                await asyncArray(application.getSqlPackageFactories())
                    .filter(factory => factory.table.tableExists(context))
                    .forEach(async factory => {
                        // first pass: set to null all nullable references to isClearedByReset factories
                        const nullableColumns = factory.properties
                            .filter(property => {
                                return (
                                    property.isReferenceProperty() &&
                                    property.isStored &&
                                    !property.isInherited &&
                                    property.column &&
                                    property.targetFactory.isClearedByReset &&
                                    !property.isHardDependency
                                );
                            })
                            .map(property => `${property.column?.columnName}=NULL`);
                        if (nullableColumns.length > 0) {
                            const sql = `UPDATE ${context.schemaName}.${factory.tableName} SET ${nullableColumns.join(
                                ', ',
                            )} WHERE _tenant_id = $1`;
                            await context.executeSql(sql, [context.tenantId]);
                            // LATER await factory.invalidateCache(context);
                        }
                    });
                // delete tables tagged by isClearedByReset
                await asyncArray(
                    application
                        .getSqlPackageFactories()
                        .filter(factory => factory.isClearedByReset)
                        .reverse(),
                ).forEach(async factory => {
                    try {
                        await context.bulkDeleteSql(factory.nodeConstructor, {
                            where:
                                typeof factory.isClearedByReset === 'function' ? factory.isClearedByReset : undefined,
                        });
                    } catch (error) {
                        throw factory.logicError(error.message, error);
                    }
                });
                // reset properties tagged with isClearedByReset
                await asyncArray(
                    application.getSqlPackageFactories().filter(factory => !factory.isClearedByReset),
                ).forEach(factory =>
                    asyncArray(factory.properties).forEach(async property => {
                        if (property.isClearedByReset && property.isStored) {
                            try {
                                await context.bulkUpdate(property.factory.nodeConstructor, {
                                    set: {
                                        [property.name]: property.defaultValue,
                                    },
                                    where:
                                        typeof property.isClearedByReset === 'function'
                                            ? (property.isClearedByReset as any)
                                            : undefined,
                                });
                            } catch (error) {
                                throw factory.logicError(error.message, error);
                            }
                        }
                    }),
                );
            },
            { description: () => `resetTenantDocuments(${resetTenantConfig.tenant.id})` },
        );

        await application.asRoot.withReadonlyContext(
            resetTenantConfig.tenant.id,
            async context => {
                // garbage collect content addressable tables
                context.logger.info(`Deleting orphan records in content-addressable tables starts: ${Datetime.now()}`);
                await garbageCollectContentAddressableTables(context, resetTenantConfig.tenant.id);
                context.logger.info(`Deleting orphan records in content-addressable tables ends: ${Datetime.now()}`);
            },
            {
                description: () =>
                    `resetTenantDocuments(${resetTenantConfig.tenant.id}): Deleting orphan records in content-addressable tables`,
                source: 'customMutation',
            },
        );
    }
}
