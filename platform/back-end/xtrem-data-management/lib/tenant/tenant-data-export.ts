import {
    AnonymizeB<PERSON>ry,
    BinaryStream,
    Compress,
    Context,
    Dict,
    NodeFactory,
    Property,
    S3Bucket,
    SqlContext,
    SqlConverter,
    SqlNaturalKeyUtils,
} from '@sage/xtrem-core';
import { Connection, CopyOptions, copyTo } from '@sage/xtrem-postgres';
import { assert } from 'console';
import * as csvParser from 'csv-parse';
import { parse } from 'csv-parse';
import { stringify } from 'csv-stringify/sync';
import * as fs from 'fs';
import { createReadStream, createWriteStream } from 'fs';
import { isEmpty, isNumber, kebabCase, uniq } from 'lodash';
import * as fsp from 'path';
import { Writable, finished, pipeline } from 'stream';
import { promisify } from 'util';
import { TableInfo } from './schema-info';
import { TenantDataLocation } from './tenant-data-location';
import { UserTransformContext, getCsvFormatters, getCsvTransformerHandler } from './tenant-data-transform';
import { TenantDataUtils, defaultCopy, logger } from './tenant-data-utils';
import { TenantDataMetadata, TenantDataOptions, TenantExportOptions } from './tenant-interfaces';

function isNonEmptyStringArray(stringArray?: string[]): stringArray is string[] {
    return !isEmpty(stringArray);
}

interface CsvStreamOptions {
    rootPath: string;
    factory?: NodeFactory;
    customAnonymizeProperties: Property[] | undefined;
    supportsPersona?: boolean;
}

/** @internal */
export class TenantDataExport {
    #userTransformContext?: UserTransformContext;

    constructor(private readonly exportConfig: TenantExportOptions) {}

    get tenantId(): string {
        return this.exportConfig.tenantId;
    }

    get diffCompliant(): boolean {
        return !!this.exportConfig.diffCompliant;
    }

    get keepAllValues(): boolean {
        return !!this.exportConfig.keepAllValues;
    }

    get anonymize(): boolean {
        return !!this.exportConfig.anonymize;
    }

    /**
     * to exclude users
     */
    private static userFilter(alias?: string, referencedIds?: string[]): string {
        const aliasPrefix = alias ? `${alias}.` : '';
        // We are filtering system users and eventually all the one that are referenced.
        let whereString = `${aliasPrefix}is_demo_persona=true OR ${aliasPrefix}user_type = 'system'`;
        if (isNonEmptyStringArray(referencedIds)) {
            const ids = referencedIds.map(id => `'${id}'`);
            whereString = `${whereString} OR ${aliasPrefix}_id IN (${ids})`;
        }
        return whereString;
    }

    private static anonymizeColumn(c: string, anonymizeProperties: Property[]): string | undefined {
        const getConditionalSql = (columnName: string, value: string | null, nullSubstitute: string): string => {
            return `CASE WHEN COALESCE(${columnName}, ${nullSubstitute}) = ${nullSubstitute} THEN ${nullSubstitute} ELSE ${value} END`;
        };
        const anonymizeProperty = anonymizeProperties.find(p => p.columnName === c);
        let limitString = 0;
        let columnSql;
        if (anonymizeProperty && anonymizeProperty.decorator.anonymizeMethod !== undefined) {
            switch (anonymizeProperty.decorator.anonymizeMethod) {
                case 'fixed':
                    columnSql = getConditionalSql(
                        anonymizeProperty.columnName || '',
                        SqlConverter.convertDefaultValue(anonymizeProperty.decorator.anonymizeValue),
                        "''",
                    );
                    break;
                case 'hash':
                    if (anonymizeProperty.maxLength) {
                        limitString = anonymizeProperty.maxLength;
                    }
                    columnSql = getConditionalSql(
                        anonymizeProperty.columnName || '',
                        `ENCODE(DIGEST(${anonymizeProperty.columnName}, 'SHA256'), 'HEX')`,
                        "''",
                    );
                    break;
                case 'hashLimit':
                    if (
                        anonymizeProperty.decorator.anonymizeValue &&
                        isNumber(anonymizeProperty.decorator.anonymizeValue)
                    ) {
                        limitString = anonymizeProperty.decorator.anonymizeValue;
                    }
                    columnSql = getConditionalSql(
                        anonymizeProperty.columnName || '',
                        `ENCODE(DIGEST(${anonymizeProperty.columnName}, 'SHA256'), 'HEX')`,
                        "''",
                    );
                    break;
                case 'random':
                    if (anonymizeProperty.max) {
                        columnSql = getConditionalSql(
                            anonymizeProperty.columnName || '',
                            `FLOOR(RANDOM()*${anonymizeProperty.max})::BIGINT`,
                            '0',
                        );
                        break;
                    }

                    if (anonymizeProperty.type === 'integer') {
                        columnSql = getConditionalSql(
                            anonymizeProperty.columnName || '',
                            `FLOOR(RANDOM()*${Number.MAX_SAFE_INTEGER})::BIGINT`,
                            '0',
                        );
                        break;
                    }

                    columnSql = getConditionalSql(
                        anonymizeProperty.columnName || '',
                        `FLOOR(RANDOM()*${2 ** 31 - 1})::INTEGER`,
                        '0',
                    );
                    break;
                case 'binary':
                    columnSql = `'${getDummyBase64AsHex(
                        anonymizeProperty.decorator.anonymizeValue as AnonymizeBinary,
                    )}'`;
                    break;
                default:
            }

            if (columnSql) {
                if (anonymizeProperty.isNullable) {
                    columnSql = `CASE WHEN ${anonymizeProperty.columnName} IS NOT NULL THEN ${columnSql} ELSE NULL END`;
                }

                return `${
                    limitString > 0 ? `SUBSTRING(${columnSql}, 1, ${limitString})` : columnSql
                } AS ${SqlContext.escape(c)}`;
            }
        }

        return undefined;
    }

    private mapColumn(
        c: string,
        tableAlias: string,
        supportsPersona: boolean,
        rootUserId: string | number,
        table: TableInfo,
        exportValueProperties: Property[] | undefined,
        anonymizeProperties: Property[] | undefined,
        setEqualToId?: string[],
    ): string {
        if (setEqualToId?.includes(c)) return `_id AS ${SqlContext.escape(c)}`;

        if (supportsPersona && ['_create_user', '_update_user'].includes(c)) {
            return `${rootUserId} AS ${SqlContext.escape(c)}`;
        }

        /** If keepAllValues is not specified the default behavior is to substitute the value to export with
         * the value set in exportValue on the property decorator */
        if (!this.keepAllValues && exportValueProperties) {
            const exportValueProperty = exportValueProperties.find(p => p.columnName === c);
            if (exportValueProperty && exportValueProperty.decorator.exportValue !== undefined)
                return `${SqlConverter.convertDefaultValue(
                    exportValueProperty.decorator.exportValue,
                )} AS ${SqlContext.escape(c)}`;
        }

        /** If anonymize is specified anonymize relevant columns */
        if (this.anonymize && anonymizeProperties) {
            const anonymizeSelect = TenantDataExport.anonymizeColumn(c, anonymizeProperties);
            if (anonymizeSelect) return anonymizeSelect;
        }

        const columnInfo = table.columnsByName[c];
        if (!columnInfo) return c;

        if (columnInfo?.dataType) {
            if (columnInfo.dataType === 'boolean') {
                // We have to cast boolean to int as the copyFrom process fails with the standard values t and f.
                return `${tableAlias}.${SqlContext.escape(c)}::INT AS ${SqlContext.escape(c)}`;
            }
            return `${tableAlias}.${SqlContext.escape(c)}`;
        }
        // Case where column _id is replaced by natural key value .
        return c;
    }

    /**
     * Dump a table to file. Default is a csv file.
     *
     * @param context
     * @param table
     * @param columnNames
     * @param tenantId
     * @param options
     * @param setEqualToId: optional. Self-referencing columns to be exported with the value of the _id of the record.
     */
    private async _dumpTable(
        context: Context,
        table: TableInfo,
        columnNames: string[],
        tenantId: string,
        options: TenantDataOptions,
        setEqualToId?: string[],
    ): Promise<number> {
        assert(options.filename != null);
        const tableName = table.name;

        // use existing extension or .csv
        const ext = fsp.extname(options.filename) ? '' : '.csv';
        const filename = `${options.filename}${ext}`;
        const tableAlias = 't0';
        const supportsPersona = await context.supportsPersona();
        const where = ['_tenant_id', 'tenant_id']
            .filter(col => table.columns.map(c => c.name).includes(col))
            .map(col => `${tableAlias}.${col}='${tenantId}'`);

        const rootUserId = (await context.rootUser)._id;

        // Special case for demo tenant
        // WARN: we do not use the factory to get the isDemoPersona column name because we manage the export at the sql level
        if (
            supportsPersona &&
            tableName === this.#userTransformContext?.tableName &&
            table.columnsByName.is_demo_persona != null
        ) {
            where.push(TenantDataExport.userFilter(tableAlias, Object.keys(this.#userTransformContext?.referencedIds)));
        }

        const factory = context.application.tryGetFactoryByTableName(table.name);

        const propertiesValidForCsv = factory?.properties.filter(property => isValidCsvProperty(property, columnNames));

        /** Find properties that have an exportValue set */
        const exportValueProperties = propertiesValidForCsv?.filter(
            property => property.decorator.exportValue !== undefined,
        );

        /** Find properties that have an anonymizeMethod set */
        const anonymizeProperties = propertiesValidForCsv?.filter(
            property => property.decorator.anonymizeMethod !== undefined,
        );

        logger.verbose(() => `Building SQL request to export table ${table.name}`);

        const columnNamesToQuery = this.diffCompliant
            ? columnNames.filter(
                  // The diffCompliant option excludes _update_tick, _create_stamp _update_stamp and _vendor columns that may differ
                  c =>
                      ![
                          '_create_user',
                          '_update_user',
                          '_update_tick',
                          '_create_stamp',
                          '_update_stamp',
                          '_vendor',
                      ].includes(c),
              )
            : columnNames;

        const naturalKeysInspection = TenantDataExport._manageNaturalKeysForSharedTables(
            context,
            table,
            tableAlias,
            columnNamesToQuery,
        );

        const whereClause = where.map(e => `(${e})`).join(' AND ');

        const distinctClause = naturalKeysInspection.joins.length > 0 ? 'DISTINCT' : '';

        const orderByClause =
            this.diffCompliant || tableName === this.#userTransformContext?.tableName || !!distinctClause
                ? ` ORDER BY ${tableAlias}._id`
                : '';
        const selectClause = `SELECT ${distinctClause} ${columnNamesToQuery
            .map(c => {
                return this.mapColumn(
                    c,
                    tableAlias,
                    supportsPersona,
                    rootUserId,
                    table,
                    exportValueProperties,
                    anonymizeProperties,
                    setEqualToId,
                );
            })
            .join(',')} FROM ${context.schemaName}.${tableName} ${tableAlias} ${naturalKeysInspection.joins.join(
            '',
        )} WHERE ${whereClause} ${orderByClause}`;

        logger.verbose(() => selectClause);
        const customAnonymizeProperties = this.anonymize
            ? anonymizeProperties?.filter(
                  p =>
                      p.decorator.anonymizeMethod &&
                      (['perCharRandom', 'url'].includes(p.decorator.anonymizeMethod) ||
                          (p.decorator.anonymizeMethod === 'custom' &&
                              typeof p.decorator.anonymizeValue === 'function')),
              )
            : undefined;

        const streams = this._getCsvStreams(filename, {
            rootPath: options.rootPath,
            factory,
            customAnonymizeProperties,
            supportsPersona,
        });
        // using copy strategy is at least 2 times faster than select + transform to csv
        const count = await TenantDataExport.copyToStream(context, selectClause, streams, {
            ...defaultCopy,
            ...options?.copy,
        });

        const filePath = fsp.join(options.rootPath, filename);
        const stats = await fs.promises.stat(filePath);
        const fileSizeInBytes = stats.size;
        const maxFileSizeInBytes = (this.exportConfig.maxFileSizeInMb ?? 100) * 1024 * 1024; // 100MB
        if (fileSizeInBytes > maxFileSizeInBytes) {
            let totalSize = 0;
            let fileCounter = 1;
            let lineNumber = 1;
            let outputFile = fsp.join(options.rootPath, `${options.filename}${ext}_${fileCounter}`);
            let outputFileStream = createWriteStream(outputFile);
            let header = '';
            const parser = fs.createReadStream(filePath).pipe(
                parse({
                    quote: '"',
                    delimiter: defaultCopy.delimiter,
                    columns: true,
                    bom: true,
                }),
            );
            parser
                .on('readable', () => {
                    // eslint-disable-next-line no-constant-condition
                    while (true) {
                        const row = parser.read();
                        if (!row) break;
                        if (lineNumber === 1) {
                            // build and write the header for the first file
                            header = stringify([Object.keys(row)], {
                                quoted: false,
                                delimiter: defaultCopy.delimiter,
                            });
                            outputFileStream.write(header);
                        }
                        lineNumber += 1;
                        const data = stringify([Object.values(row)], {
                            quoted: false,
                            delimiter: defaultCopy.delimiter,
                        });
                        const dataSize = Buffer.byteLength(data);
                        totalSize += dataSize;
                        if (totalSize > maxFileSizeInBytes) {
                            outputFileStream.end();
                            if (options?.metadata?.files) {
                                options?.metadata?.files.push(`${options.filename}${ext}_${fileCounter}`);
                            }
                            fileCounter += 1;
                            totalSize = dataSize;
                            outputFile = fsp.join(options.rootPath, `${options.filename}${ext}_${fileCounter}`);
                            outputFileStream = createWriteStream(outputFile);
                            // write the header for the current file
                            outputFileStream.write(header);
                        }
                        outputFileStream.write(data);
                    }
                })
                .on('end', () => {
                    outputFileStream.end();
                    if (options?.metadata?.files) {
                        options?.metadata?.files.push(`${options.filename}${ext}_${fileCounter}`);
                    }
                    // remove the original file
                    fs.unlinkSync(filePath);
                    logger.info(`Splitted file ${filename} into ${fileCounter} files`);
                })
                .on('error', err => {
                    throw new Error(`${factory?.name}: ${err.message}`);
                });

            await promisify(finished)(parser);
        } else if (options?.metadata?.files) {
            options?.metadata?.files.push(filename);
        }

        return count;
    }

    private _getCsvStreams(filename: string, options: CsvStreamOptions): Writable | Writable[] {
        const { rootPath, factory, customAnonymizeProperties } = options;
        const filePath = fsp.join(rootPath, filename);
        const writer = createWriteStream(filePath);
        const delimiter = ';';
        const chunkSize = 100;

        if (factory == null) {
            return writer;
        }

        if (!this.#userTransformContext) {
            throw new Error('User transform context must be initialized before streaming the export');
        }
        const { metadata, transformer } = getCsvTransformerHandler(factory, customAnonymizeProperties ?? [], {
            userTransformContext: this.#userTransformContext,
            anonymize: this.anonymize,
            chunkSize,
        });
        if (transformer == null) {
            return writer;
        }

        const csvFormatters = getCsvFormatters(delimiter);

        const parser = csvParser.parse({
            delimiter,
            columns: headers => {
                metadata.headers = headers;

                const { mapOut } = metadata;
                headers.forEach((header: string) => {
                    const prop = factory.properties.find(p => p.columnName === header);
                    const type = prop?.type;
                    if (type) {
                        if (type === 'json') {
                            mapOut[header] = csvFormatters.toJson;
                        } else if (['string', 'textStream'].includes(type)) {
                            mapOut[header] = csvFormatters.toText;
                        } else if (/^\w+(?:Array|Range)$/.test(type)) {
                            mapOut[header] = csvFormatters.toRange;
                        }
                    }
                });

                return headers;
            },
        });

        return [parser, transformer, writer];
    }

    /**
     * Inspects the columns provided as a parameter
     * Replace _id with the value of the natural key
     * This function will only be used for references to shared tables.
     * @param context
     * @param table the table to process
     * @param tableAlias the table alias to be used for the table
     * @param columnNames the columns to inspect (columns from the table)
     */
    private static _manageNaturalKeysForSharedTables(
        context: Context,
        table: TableInfo,
        tableAlias: string,
        columnNames: string[],
    ): {
        /**
         * The list of aliases generated by the function
         */
        aliases: Dict<string>;
        /**
         * The list of SQL joins generated by the function to follow the references / baseTables
         */
        joins: string[];
        /**
         * The naturalKeys found within the the array of columnNames
         */
        naturalKeyColumns: string[];
    } {
        const aliases: Dict<string> = {};
        const joins: string[] = [];
        let allNaturalKeyColumns: string[] = [];
        const factory = context.application.getFactoryByTableName(table.name);

        table.columns.forEach(column => {
            if (!columnNames.includes(column.name)) return;
            if (!column.isReference) return;
            if (column.targetTableName == null) {
                throw new Error(`TargetTableName is missing for column ${table.name}.${column.name}`);
            }
            const targetFactory = context.application.getFactoryByTableName(column.targetTableName);
            if (!targetFactory.isSharedByAllTenants) return;
            const targetNaturalKey = targetFactory.naturalKey;
            if (!targetNaturalKey)
                throw new Error(`The shared factory ${targetFactory.name} does not have any natural key`);
            const targetNaturalKeyColumns = targetNaturalKey.map(
                propertyName => targetFactory.findProperty(propertyName).requiredColumnName,
            );
            allNaturalKeyColumns = [...allNaturalKeyColumns, ...targetNaturalKeyColumns];
            const sqlDetails = SqlNaturalKeyUtils.getSqlQueryPartsWithNaturalKeys(
                context.schemaName,
                factory.table,
                [column.name],
                {
                    aliases,
                    joins,
                    doNotUseArgsForConstructors: true,
                    args: [], // Constructors will be inserted in the LEFT JOIN clauses - doNotUseArgsForConstructors is true
                    tableAlias,
                },
            );
            // the columns from sqlDetails.columnNames are already part of the columnNames array,
            // for instance columnNames can contain 'operation' (as a simple _id) while sqlDetails will contain
            // a column 't2.name || '|' || t1.name || '|' || t1.action AS operation'. We have to replace the
            // already existing 'operation' (simple id) with the complex one that was computed to get the natural key
            sqlDetails.columns.forEach(col => {
                const idx = columnNames.indexOf(col.unaliased);
                if (idx !== -1) columnNames[idx] = col.aliased;
            });
        });
        return {
            naturalKeyColumns: allNaturalKeyColumns,
            aliases,
            joins,
        };
    }

    /**
     * @internal
     * Copy a table or the result of SQL query into a f-stream writer
     * @see https://www.postgresql.org/docs/current/sql-copy.html
     *
     * @param sqlOrTable
     * @param stream
     * @param options
     */
    static copyToStream(
        context: Context,
        sqlOrTable: string,
        streams: Writable | Writable[],
        options: CopyOptions,
    ): Promise<number> {
        const writableStreams = Array.isArray(streams) ? streams : [streams];
        return context.transaction.withConnection((cnx: Connection) => {
            return new Promise<number>((resolve, reject) => {
                const sql = `COPY ${sqlOrTable.includes(' ') ? `(${sqlOrTable})` : sqlOrTable} TO STDOUT`;
                const withHeader = options?.header ?? true;

                const format = options?.format ?? 'CSV';
                const delimiter = options?.delimiter ?? ';';

                const copyOptions = [`FORMAT ${format}`, `DELIMITER '${delimiter}'`, `HEADER ${withHeader}`];
                SqlContext.logger.verbose(() => `${sql} (${copyOptions.join(', ')})`);
                const reader = cnx.query(copyTo(`${sql} (${copyOptions.join(', ')})`, {}));

                pipeline([reader, ...writableStreams], (err: Error) => {
                    if (err) {
                        reject(err);
                    } else {
                        // Any cast is required because the rowCount property is not exposed
                        // but it is really set like for any other query results
                        const rowCount = (reader as any).rowCount;
                        if (withHeader && rowCount > 0) {
                            resolve(rowCount - 1);
                        }
                        resolve(rowCount);
                    }
                });
            });
        });
    }

    /**
     * Export tenant data to a folder and zip it and optionally put it on a S3 bucket.
     * The default folder is `data/exports`
     *
     * @param context the context (already set to the right tenantId)
     */
    async export(context: Context): Promise<void> {
        // current implementation zip the exported data locally then uploads the zip to S3
        // TODO: we may optimize the process by using a S3 upload stream to zip directly to S3

        const dataLocation = new TenantDataLocation({
            ...this.exportConfig,
            version: context.application.rootAbout.version,
        });

        const supportsPersona = await context.supportsPersona();
        this.#userTransformContext = new UserTransformContext(supportsPersona);

        const packages = context.application.getPackages().map(pack => ({ name: pack.name, version: pack.version }));

        const { exportDumpPath, zipTarget, location } = dataLocation;
        fs.mkdirSync(exportDumpPath, { recursive: true });

        let profiler = logger.info(`Dumping data of tenant ${this.tenantId} into ${exportDumpPath} folder`);

        const metadata: TenantDataMetadata = {
            packages,
            exportId: dataLocation.exportName,
            tenantId: this.tenantId,
            directoryName: dataLocation.directoryName,
            files: [] as string[],
        };
        let total = 0;

        try {
            const delayedTables: TableInfo[] = [];
            const tables = await TenantDataUtils.visitTenantTables(
                context,
                async (table: TableInfo) => {
                    const factory = context.application.tryGetFactoryByTableName(table.name);

                    if (!factory) {
                        logger.warn(`The table ${table.name} was not exported (no matching factory could be found)`);
                        return;
                    }

                    if (factory.isPlatformNode && !factory.isPlatformNodeExportable) {
                        logger.warn(
                            `The platform table ${table.name} was not exported (not marked as 'isPlatformNodeExportable')`,
                        );
                        return;
                    }
                    if (table.name === this.#userTransformContext?.tableName) {
                        delayedTables.push(table);
                    } else {
                        total += await this.exportTable(table, exportDumpPath, metadata, factory, context);
                    }
                },
                {
                    excludedTables: this.exportConfig.excludedTables,
                },
            );

            // eslint-disable-next-line no-restricted-syntax
            for (const table of delayedTables) {
                const factory = context.application.getFactoryByTableName(table.name);
                total += await this.exportTable(table, exportDumpPath, metadata, factory, context);
            }
            metadata.tables = tables;

            fs.writeFileSync(`${exportDumpPath}/metadata.json`, JSON.stringify(metadata), 'utf8');
            profiler.success('Dump complete');

            profiler = logger.info(`Compressing ${exportDumpPath} to ${zipTarget}`);
            await Compress.zipDirectory(exportDumpPath, zipTarget, { zlib: { level: 9 } });
            const stat = fs.statSync(zipTarget);
            logger.info(`${zipTarget} size: ${stat.size} bytes`);
            profiler.success('Compressing complete');

            if (location) {
                const { bucketName, pathPrefix, zipKey } = dataLocation;
                logger.info(`Bucket location='${location}' bucketName='${bucketName}' pathPrefix='${pathPrefix}'`);
                dataLocation.checkS3Location();
                profiler = logger.info(`Uploading ${zipTarget} to to s3://${bucketName}/${zipKey}`);
                const readStream = createReadStream(zipTarget);
                const res = await new S3Bucket(bucketName).putObject({
                    Key: zipKey,
                    Body: readStream,
                });
                logger.info(`Uploaded ${zipKey} ETag='${res.ETag?.replace(/"/g, '')}' VersionId='${res.VersionId}'`);
            }
            logger.info(
                `Exported a total of ${total} row(s) data in ${metadata.files?.length} files(s) for tenant ${this.tenantId}`,
            );
            profiler.success();
        } catch (err) {
            profiler.fail(`Failed to export tenant '${this.tenantId}', ${err.message}, ${err.stack}`);
            throw err;
        } finally {
            const { keepFiles } = this.exportConfig;
            // cleanup only if an S3 location is provided and the export succeeded:
            if (!keepFiles && location && fs.existsSync(exportDumpPath))
                TenantDataUtils.cleanup(exportDumpPath, zipTarget);
        }
    }

    private async exportTable(
        table: TableInfo,
        exportPath: string,
        metadata: TenantDataMetadata,
        factory: NodeFactory,
        context: Context,
    ): Promise<number> {
        const columns: string[] = [];
        const nullableColumns: string[] = [];
        const nonNullableSelfReferences: string[] = [];
        const keyColumns: string[] = [];
        const filter = {} as Dict<any>;
        // This has to be reviewed we are doing thing twice because it is used in the context of a tenant import or data management
        const columnsInUniqueIndex = new Set<string>();
        table.indexes?.forEach(ind => {
            if (!ind.isUnique) return;
            ind.columnNames.forEach(name => columnsInUniqueIndex.add(name));
        });
        table.columns.forEach(col => {
            const columnName = col.name;
            if (!columnName) return;
            if (['_tenant_id', 'tenant_id'].includes(columnName)) {
                // Do not include tenant id columns in exports, just filter on it
                filter[columnName] = this.tenantId;
            } else if (columnName === '_id') {
                if (!table.isShared) keyColumns.push(columnName);
            } else if (columnName === '_vendor') {
                // Keep the reference on the _vendor
                columns.push(columnName);
            } else if (!col.isNullable && col.isSelfReference) {
                nonNullableSelfReferences.push(columnName);
            } else if (col.isNullable && col.isReference && !columnsInUniqueIndex.has(col.name)) {
                nullableColumns.push(columnName);
            } else {
                columns.push(columnName);
            }
        });

        const filename = kebabCase(table.name);
        const opts = {
            rootPath: exportPath,
            filename,
            metadata,
            copy: {},
        } as TenantDataOptions;

        if (factory.naturalKey) {
            factory.naturalKey.forEach(naturalKeyPropName => {
                const naturalKeyProp = factory.findProperty(naturalKeyPropName);
                // Note: the natural key might be inherited from a base table and its columns may not
                // be declared in the current table but only in the base table.
                if (naturalKeyProp && naturalKeyProp.columnName && table.columnsByName[naturalKeyProp.columnName]) {
                    keyColumns.push(naturalKeyProp.columnName);
                }
            });
        }

        // Dump table data without nullable references.
        // Non-nullable self-references columns are present in the file, but with the value of the _id column.
        // This value will be corrected when importing the node--nullable.csv file.
        const count = await this._dumpTable(
            context,
            table,
            uniq([...keyColumns, ...columns, ...nonNullableSelfReferences]),
            this.tenantId,
            opts,
            nonNullableSelfReferences,
        );

        // There are nullable references, we dump this data in another file
        // Along with non nullable self-references, that must be present in both csv files.
        if (nullableColumns.length > 0 || nonNullableSelfReferences.length > 0) {
            await this._dumpTable(
                context,
                table,
                [...keyColumns, ...nullableColumns, ...nonNullableSelfReferences],
                this.tenantId,
                {
                    ...opts,
                    rootPath: exportPath,
                    filename: `${filename}--nullable`,
                },
            );
        }
        logger.info(`Exported ${count} row(s) of data for ${table.name}`);
        return count;
    }
}

function isValidCsvProperty(property: Property, columnNames: string[]): unknown {
    return (
        !property.isInherited && property.columnName && columnNames.includes(property.columnName) && property.isStored
    );
}

function getDummyBase64FromType(mimeType: AnonymizeBinary): string {
    switch (mimeType) {
        case 'image':
            return anonImageBase64;
        case 'pdf':
            return blankPdfBase64;
        default:
            return blankImageBase64;
    }
}

function getDummyBase64AsHex(mimeType: AnonymizeBinary): string {
    const base64String = getDummyBase64FromType(mimeType);
    const binaryData = Buffer.from(base64String, 'base64');
    return `\\x${binaryData.toString('hex')}`;
}

/** @internal */
export function getDummyBinaryStream(mimeType: AnonymizeBinary): BinaryStream {
    const base64String = getDummyBase64FromType(mimeType);
    return BinaryStream.fromBuffer(Buffer.from(base64String, 'base64'));
}

/** @internal */
export const blankPdfBase64 =
    'JVBERi0xLjUKJb/3ov4KMSAwIG9iago8PCAvVHlwZSAvT2JqU3RtIC9MZW5ndGggNzU3IC9GaWx0ZXIgL0ZsYXRlRGVjb2RlIC9OIDEwIC9GaXJzdCA2MyA+PgpzdHJlYW0KeJytVVtv2jAUft+vOI+gCRI796pC4jJatHVjBVaplwc3cYM1cCLHTOXf79gJl7J2k7YpUnB8Lt/5zvlsKLjgge+DD5SEEABNEgghTEKIIKYxxEDcIIQECI09XAPxIwqEAAlJ/O78HJxhsZEaCDgfRVbBHaZz4RoewJlvSw7OlOW8gl6v8ZWaS11hNuvlXKhiU4I1zcAZ8R8i5dcXA3AmoNUGw3F3rpisSqa4TLe7rHVcrwfOFc8EGxTPiOziEyRBl0ZBkBCIfdKN4yQKfVPN1CTQ0OBe86rYqBQr8+uNQ7G7Wj8864uZZprb8phbV9k3TFOGbwQ3+GOkZE1PHbeD+HU680GwkebDOtqUA1Zx6+/0x5dfF7fvh1eLGZKbcSWeEFGmRSZkDs6NkH1ZicPGWKhKD5dMgUdrzBGvUiVKXSiclMX8xBoXQrHG2eZRW05z7GPNbl4spMCMHIh3zLou6UZkemkH6Hm2la8/NIp/Yz19ImoaHoTJK7bQ2+2G8dsZfLQFJEKN+vtfO2hc0yaD+Y2Swz5q2qfgoWC9kDQIhpOPXw+/DuN2Me5PpifDMP3lMmNSGyfTl2Qn7MNYJhlqSuht5/K44fh2X3T7hcYsZlND32BoSFz0H7Lykot8iWeJoHqbAjR0aIDW8Yrl1X74g1rwHUK8ADpe4CNGTOrAh9plzNZitYUWsgJDCyyvdmMUK46nuBGr2fnM1vwNWU40W4m0L/MVR39npvn6EuJm9c2uDsyOZPmXHP3/SjE8pfjqsP+R4h+kNJyMZtsKM03kU2Hvii8qQxMqqLVTUNvcSbmotEJC/ax45G0jqbJc8bXpkLu/bI7g45rcXnkIZDxMqfTkcNvr8Q6iEE+IF2IH9+dgqDjTopAjc9W1RmfUpZ5LKSE+jah32248EK41kd+rlGFW0qVdAvetpdZldeY4ojF0C5XftzFkqopsk3KMSZlQBQaQqBseRdjtXLFyKdJqF9ZUVP9l0ONDM2Q4oSI3Hj8BtpbXcmVuZHN0cmVhbQplbmRvYmoKMTIgMCBvYmoKPDwgL0ZpbHRlciAvRmxhdGVEZWNvZGUgL0xlbmd0aCAyMzMgPj4Kc3RyZWFtCnicxVBNawJBDL3Pr3jH3cPGZHZ2PqD0UPoBQqG2cxMPq6624oK6gvTfd3Rse+2thDzIe0lm8gScopIE3gh5H5w1WPRqr/giHdYYtYz1oO6i0obYGuPy0E9lmeqGmQ3EWBLt2DeIvRqtKq4YgrhSRXz/GPBy/4gybrIiWblJk3L7TV7bp8W2w6nUvmgHHLrdtl10S8zPxCeO51VdvzvmMi2lchbH6iFeP/36BKnJB0434ZTIccqNms7AxFgqg2fsIZfejOliS8E0yNh4odo7eO3T63jD5NeOv/hggyNta27CP/kwUV9MdmwNZW5kc3RyZWFtCmVuZG9iagoxMyAwIG9iago8PCAvRmlsdGVyIC9GbGF0ZURlY29kZSAvTGVuZ3RoIDMyMyA+PgpzdHJlYW0KeJxdUstugzAQvPsr9pgeIh4F00gWUpVeOPSh0n4AsZfEUjGWIQf+vl5vlEo9wI53Z4fRmOzYvXTOrpB9hFn3uMJonQm4zNegEU54tk4UJRir19spvfU0eJHF5X5bVpw6N85CKcg+43BZwwa7ZzOf8EEAQPYeDAbrzrD7Pvbc6q/e/+CEboVctC0YHKPc6+DfhgkhS8v7zsS5Xbd9XPtjfG0eoUzngi3p2eDiB41hcGcUKs9bUOPYCnTm36y8rZxGfRmCUHUVqXkei1DyKeFYIj4wPkTcPCYci1BlnnAscZdxTbhinYp0KslYko5mHU24ZlyTZsOaDfUL7hfUL7lfEmb9hvQle5DkQfK3ZPLMfJn47LlJnpnTJI5hjiH/yP4xhXNLgWKi+7znr68hxOjTpafMKW3r8P5f+NnTVnp+AaJwno5lbmRzdHJlYW0KZW5kb2JqCjE0IDAgb2JqCjw8IC9GaWx0ZXIgL0ZsYXRlRGVjb2RlIC9MZW5ndGggMjIzID4+CnN0cmVhbQp4nF2Qz2rDMAzG734KHdtDcdNzCIzuksP+0GwP4NhyZmhkoziHvP1kN3QwgS0kfT/zWfrav/YUMuhPjnbADD6QY1ziyhZhxCmQai7ggs17VW87m6S0wMO2ZJx78lG1LeibDJfMGxxeXBzxqABAf7BDDjTB4fs6PFrDmtIdZ6QMZ9V14NDLc28mvZsZQVf41DuZh7ydBPtTfG0J4VLr5mHJRodLMhbZ0ISqPUt00HqJTiG5f/OdGr39MVzVTVGPkop67xeufPJpyq7M4qduohopFgLhc1kppkLV8wtTkXDIZW5kc3RyZWFtCmVuZG9iagoxNSAwIG9iago8PCAvRmlsdGVyIC9GbGF0ZURlY29kZSAvTGVuZ3RoMSA1MjI0IC9MZW5ndGggMzQ0NCA+PgpzdHJlYW0KeJzlWGtwG9d1PvcuFgssFo/FmwAJLrAUQBMgQGJJQpBEiqJIynyIIiXGAmjKIiPJUjzWSG0djdxGlmtbkYqME7vNr0ziJK2bR5vUdyHWySRWy0lc18nEyYybYad5TNROf5gzUh7TOhNPhmTPXYC00iSe6e8udYE99/mdc7/zgIAAgAxPggDaqfPLF394/lOvAri/D0AXTl16THNokgzg68FZkw9fPHv+aVv3cZQ/hOPi2Ucff/jnP/jXMI59Cdd0njuzfPru9DGc638G+wbOYYf4ReEplF9BuePc+ccu554SZ1C+jfLSoxdOLQN84asAgWGUT59fvnyRjAitKL+IsnbxD89c/Hp4uYgyYoLPgohIZfpTuoFo7eAEBbzwU6jLImSZYphEWmdQMCX8chQJ8xUYrK3Y3KDZssxeZDZfHTy+LPMWV4RGp7rGBmKmTbluY7a8yJRVU/K9Y2PeVdPuuC4yR97G7Kumx/eOyORVZvfdFO2OQBYHHbjZTdGmBLIi73budEsoSF7eLftuumQPdssea7arMRu73TvdONvNZ8NXbKLd6XJ7vPnmQ74iSg6n7FLc2z1sOEagp7ekJtVdThJIOgWBqDL9z40KCW7e0Wl68+7mXRIggV+QoEz/a8P9MAmSwMZL+Hl28w7O+ebmHZoBIKDj3T1NTbSfDnWJQrZO0IAmoNkENJtcYNKaafeuM9caHhjoT4b0jKqrkk6XPvrB6iV6YWSEbDxHLxLcaxY3XKFvQhQOQx2odQ91Ysc9RQH3DErrhLUUGFkzBfc6ixSZ4KsTty9rKso6i62ZbkH1M7HMFJU5y2AGAUWh3NNrqIY6YBQTQghP7ssLesreRman9s2nB+JeYYa4ApnW/l765ubfHYomDT26eZu0qMFdYXVh+OewtQWTyJcX6ef9aUB2+iSIACGfRrzxrbfJOfIG7IZzUC8hXtMprddjHGsnx1ousNCamUWsWV89pCDQIr7vwa6Q6jcFUuZQ/96pxlL5UkekzIp+s1UrI3JnCZFDmXWqN0morcjHYn7WiqrsJwm7URzo70tn8gT/9fcNUaMYkfJOVCoUTJBIgoSCHqqn4goNu1pCre1qMdsRlMfU+/Z7kmM990+6SEgISH454A268un+pWCHgQOFSmqUuO2K5BC6Yi1Ju+AR3fsLkfuyLe0jxEkdVCIiIbuiyTbV6RvujuRycbQKhZP4MU5fRt9pRTtZt9ZgQKu8vuLxAXFnTY+MxmizLs6HTPAUmQ8vLoz2kPDiEmtm2If6yqh4K3+RGldWtFSx66l0lqiCURyi/X15miUn7ywO5Y0/vSSG7R8UrvXNpP2+XTOla/TlTNt878xVsvkT8mBroRANd/ckYAfjJxCjgve3u4HRdMvrDZxhDi7Cvdt0Izg3+rWfI8P36BqYYfdvIZLsesYolvglIJpfnr5/YvbFCz7tsx+7Mr732uMfoy8Xev568SOvuOnuxcOPXf0bjoHAQ4jBS28ihvy9VrJL6ysuh2UlF6eM2wKi4OFSkXm4yxgqdxk9E0qGkupDxCxF39JI9+a/0JsjhPx6cPOfGvvP4ecn0Xcw8m7vj45DmM3aEP3F8hmxsaOhzk3RNzfyOC+FHH4BORyENjgIdb9lG2SxwFkcR3Qh2S8guhA3U8LaTMaN2tE0bj+axldmssq8ZRbijMWQ0sc9jUZCyQRBI0X4/ZUMy+dSj88lBkodwua3hQf2Tk/bp4+e/JOpX9nbM+0eY+Q/Tt0I5CqDbVMPlUaMZ5/PDEQcM1wvD9rtU+QW4uuBepDHBLdRBw5OdBkGYaECRhZTQUhhhARB1V8XnGq5jFAChj8yEDAiYf4p6OgvdkH3TC27hLOHJnRBfGSidnxUk+i5I+TW5neOD5HD//1ESy9Z2Dz47380YGx+0eJODu3zHNonDhnMaxaCegs3UjsayctxyGgkXWzxurMrugxlGxq907JTK4JqRTqlkE5isFg0A8j2+7Af4xIiRu9PqXXZ24JgWcDPVOR/O+JnoTLTVRNay2VTFlFWysyrMrdlXPT2/nQmraekDNo5HFH1fu7s6VIwjGFhkKQ8NLf7weM5Gm1JH56wTx0dO2GbtqFjlMsT/Z3Zsfbh0a4/2FWKu1PDrQnjYO7BA5vf023+6Fwu3tGR3e/wJRtcIuPkn9H221yVUFFGDNPhQhZ4Le08qB1FT0arO+4JtKEhWgp5hDYyN66k5npifWWdfGMhoHeSg0TdNW18F/eb2xokt639U/Ag1F3cnH40JxG2Q33USr6E6Y2jlHWTeovoEGjNEFrTgYbsWDNDHjSk6OIR0+9CCO4yi6rMUWakASeCcLiVrCiSCamWjdBr9ZTH7iVzUxMnxqoXpiYHy527q9P7XEHV65adoiB8kz7Zu7d24uwz5V+QA+GBpenRM4IkOR2yXbBZOQ858WnkRBT2QT3M0avbHuNq5qgGakxKYKphhBYsM5daBynAL1vwM/u2q4QjknWfGLzD2/6iXz5dGMorvbG2ztGJmaGZI+KR+cUrPznxvD820i45zh6aXBo4aPyZxU+OpY5+74F2qELdyW9L2TaonefObU8mTLNweRGXFw3p5LQMIS25NZNrptPLk5LCrRlHzzZB5lBVTKhNqGhOpJv9Hrb1ZTACIuX0c5PTY9NHDhybfP/MvNF1IKR10Wgh3/rjUzdK5MBGvm//Rx6qHi3H/jEQTPh3jYPFgbfJbQt3Ck40WWYRIcKJsJPz2znu38GC2LssiL3Lggg0WNBusUD8jey/zYL+YEKINIiQp9zuyIPj9y9emD60nwiCJCR7+xdi2Vw4M4oFwdf31E6c+XA5f83hdNhkW+d8tXCoQ823NmKuZ8sg/4CxqQUeA4u8JjgMgykFU+XRMlZgcasmhJbrDPKsZdUk4XcYXTWdjutWPehcxdT3Dguu0hVnMNxCm9UaEMe74na1ZqoKKudBcqum6G3EN4yzPLR5iJ4qEK5TCf/6Mp4jw8MuO3166t+wdrhPE/uD8XirZLOJ5NZffC6bGSdjG+sHexwH1MyoKjs9MtfFj/cxTfdCAIag7qMNKltKMdGwyjkJy7lggfnX0O1NJ3q/rYjlDZguXwMXNGJUoN/oN0JGSA8Fm3zuV/0PjD/77EBX3nfo8FHy1N4nntj7y+PJvekniduyI9Z99NsYD7rgQjM7+xKG0SAAcRqGmcbjY3h81iJCl2JFni6kQTvSIMFrCN8tXN0OCVCFH4IXN8DO3JrZ3tWoCBMq5nCG77wuTPP4K0jlZsDizN6pDveRvM0qD+8pFCf2VJYTgwvpUtxLj9gkT1AL9vduV43kG+NTc3Mtyb6kVTZSRzQUC7ixcLynhOQ6LuHnCsxj5mrG1BXigqgte+/7TrZGpmNrZmu05tL8/Px2jke+3ZPjhffM8ZjXDuK6CXgJ/oo4cF0GMKzy0yK2bPNrZ7l3nbfG8hIeOrHwUrUKDaYLwH/ZKWCjmJTRyj7s8cBV2CLHyDK5TJ4gf05foz/S0lqPtkf7UjKFVTP+5oLPkKNkCcevNMcDOF7eGf/9D8EzfkQ+QT6JtckL5DPNv9fw73XyOo6L77E29J47/+YjYXP8jn77Do7f9yigYs3rw9+Q/KFoD/44sU7hD1Yt4AYbehN/XNj8/wdU/2+ercEtrIvJLfo5amIuGMKuIFbo4zADU9gWYAA6YRR6YQQz7QQ8AMfAgGHohg6owB7IQgF/dSTx9xhGEAY5BoEplpmtsMlLVQb6/iizZyuDVavvSlX7PiOBfLSbkZz2A6ZkuxnNTR2tjOnVZDcTch+Iamx4tpJkw9VuZsvxpUk9+ceVH8ffqMZxXmUjfrca15NMzFbY+KWqNVCt4n5izr240M3sOTNFbuDp2o3FxTgD3EbKmR1W1/BOlyPnV7VyoZs5c9oVfsiruI3GhF0TusZs6UkGs5Xamdqyxl92x5PJarxmSUcbEj9QbqDzxX1J3NGV09601FFymOCl7GJF0w7p48uPaBXt9PsbW/B5bn4yHq3VtEO18WW9ptV06zidb86GcSbqxzvY8Bku4BqPddLgWjSZjGtrNTQDLppANO9rYkta07w5XVtrHq5rlalj8SQj1UoNFZrQa7pWm6jpy3xBYwn/6mY+fg1+xK1yBfiL/38pUONf+vIjS/dqwpcGcqhE7To32+RpvSYxbbayL75a7eaksnz2plF86y8DJ7373oZ24S3e89rJL69vf//6+c0XxG/ZPg/c+2nTy/FTeGbLDq/bKY4vi9/6Le+P0l+BTL8AOhWRcT+DSXIV4uQwuv9VOIn8PUnL+DvvZzBHXoEU/VvwkNuQIw/AHM7nfXydbr2/gWMfAL8Qg1kSxtyAa+AOMrwBZBTbc7hrJ7ZhRDXabKiH7VFs38EAiIWo+CIGqg4MYlFsl1GV3dhexSD0IQAZ17kUbDcwVOEahWF7i/+/mqVVFD6OserjO9oHufrkLkYx8atk6xojz8IUc8xWTEI+WjXHOduYDx0peBRfnqy2ISsWK8AkbYwJ2VEma2N1qUqzluBEQW5pCg4UnFNNwcWn3d8QCFOai93Yq3yZZk1h/BKKHm3sa6Bc3YKdHi9O8OzeFnlK+h/d+GmhZW5kc3RyZWFtCmVuZG9iagoxNiAwIG9iago8PCAvRmlsdGVyIC9GbGF0ZURlY29kZSAvTGVuZ3RoMSAxNTg4IC9MZW5ndGggMTE3OSA+PgpzdHJlYW0KeJxdVF1oHFUUPnf+dnf2bza7SbbZxt7pJKkys0mza2P6p4smqcmKhDbamRJt1yZtLQ0GKaEVqrUqLQOKQh+r4IsiWDyzKVaUQkARK/hSZB8KfdIHA01BRLDIbjx3dxvFgZl7v+9+53dmDjAAiMB5kME4unSah3lIB5A+J3bq2OLxhbeU/EHCf9GtHj919tgvt3d9SGdXCBsn5itza88c2A6gEgUjJ4hQP5NPER4l3Hdi4fQZ1YB+wrOEw6deOVoBkGcIzxGOLVTOLDJH3kx4kTBffHV+8ZuuSoHw+4Q/Jrc66NI9qU7ZaZRlDJJwD6q6CjbGigELrSIMBSFawgWGxhBC7ZoSB67YqBVQMaqQMGxMFq7JLTJVw5GeQIldVFAZVDG2EoSM+womVwItfFHF8KCC2kqQMO6rqK+gZiyrWjht02GYnC2rSixtq4KObNAhAqGkoHVjOaoniNYTTXW0pSY6vkGTOi7UcF1RtUg0nkgOti92XQ2FI3o0Fn/AYKmHwfbhx1Jmqj/C0mZElllKl36tuyzTuGtJA421xhpLs/TvLKNLf9Tjx1iGpetf0PN44y5pvm3clbZRuyEHwH6U7kAnbIJeWIBqRqL+9RSrmgZ2EA2tVrtpc60rClnFrjKZyGSuWAyA+pqivj40hJkaSoUgHV/FRAHTRjXTQ33tKgS9xGQLuKUW9KRTHcta1Aj3de+EINqV6sDIzu3D6R3FHY+OFAvdnQPWVq2XFTutlJXq31HslFPFVG5qptDdt3VTduDsUpmlZqbL7MLuo0deiI51PFK4tLvxOrtQn5mpv8luNJ6iD2J97/qLVMsN6RMpkG7B40RlYBQm4Fko030IRuBhGINheBL2wCQ8DwegCCXIQx+4sAtsGILNYEI3TAMgOAjpMm6bdnFqyUOwnsiiZrt7vSZ3zuM/I0sPZvPIHH4bY3YeJae83x23PDOPsvNylmNp2jWx5OVRcYSpaZmvuXdyP3k50rn13JqXs0xUbRcnlrzmgeeRP9WJzx7Ko+YEW9klis4vzc7mEMhNyAn6mlRpgwo7HSm+cyiPEYefE0G+Izcc5f5Ji6MyMIUw7frzfoWLzWjONL2c30T7W0gE1FvZGTnDJI9Rh99qlhNz+BCG7FmX833WROUkd/ncSy0XQhcXkSk09/k+f6Ji+dy3muEs4RxLpKT6BIGleQHIJtGMtLeWNc0cr/nUBjKapGyea+dmNmVJx+K1dnCLu+UDOROZ5/pU0KTlW9yf9K2KMGiZiCWPhngNHZR3ShQgNh3/K8AXi1U5eeS/lQjTtENF+BdF26bmLD+EfNrdk1vx8mJqifkHy8Uvxw8OH07u+RO2yL8J5vvDV1cfrH9/0PhIval8KmYYSC0L8ZTfXtfgB02i84p6s83/e0k0NXUpK/6/psGYmJlNlQSXaZpd3vCWEe7YGv2r6lds/R1k70IZw9NuwNh7XjAh3h4a9GFm9tPmvNdLXZ51AUN8HGV7DHU+Xg15kt0EEQL6pjYIE4iU2yAqZE+3AMNY2zhObOyqZAfyxBLBBB//GmJvrMMGkyRBYvQB9CjdfwB5P4urZW5kc3RyZWFtCmVuZG9iagoxNyAwIG9iago8PCAvVHlwZSAvWFJlZiAvTGVuZ3RoIDQ5IC9GaWx0ZXIgL0ZsYXRlRGVjb2RlIC9EZWNvZGVQYXJtcyA8PCAvQ29sdW1ucyA0IC9QcmVkaWN0b3IgMTIgPj4gL1cgWyAxIDIgMSBdIC9JbmZvIDEwIDAgUiAvUm9vdCAxMSAwIFIgL1NpemUgMTggL0lEIFs8MTgwMDQzYmQzYWM3YzQzMDYzZmUyODliMGRmZjU0ZTQ+PDE4MDA0M2JkM2FjN2M0MzA2M2ZlMjg5YjBkZmY1NGU0Pl0gPj4Kc3RyZWFtCnicY2IAAiZGBn4Q8YmBCchjJJr4z5zxnYmB0RCojakbSDCqAwm+00CC9RMDAK/QBkkKZW5kc3RyZWFtCmVuZG9iagpzdGFydHhyZWYKNjY2NQolJUVPRgo=';
/** @internal */
export const anonImageBase64 =
    '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';
/** @internal */
export const blankImageBase64 =
    '/9j/4AAQSkZJRgABAQEAkACQAAD/2wBDAAIBAQIBAQICAgICAgICAwUDAwMDAwYEBAMFBwYHBwcGBwcICQsJCAgKCAcHCg0KCgsMDAwMBwkODw0MDgsMDAz/2wBDAQICAgMDAwYDAwYMCAcIDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAz/wAARCAAKAAoDASIAAhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD9/KKKKAP/2Q==';
