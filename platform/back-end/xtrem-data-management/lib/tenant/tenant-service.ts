import { loadLayersFromCsvFiles, readSetupDataFromCsvFiles, upgradeSetupData } from '@sage/xtrem-cli-layers';
import {
    Application,
    ConfigManager,
    Context,
    Dict,
    NodeFactory,
    SqlFileDataSets,
    TenantInfoBase,
    asyncArray,
} from '@sage/xtrem-core';
import * as lodash from 'lodash';
import { minimatch } from 'minimatch';
import { loggers } from '../util/loggers';
import { TenantDataManager } from './tenant-data-manager';
import {
    MultiTenantsExportOptions,
    ResetTenantOperationConfig,
    TenantExportOptions,
    TenantImportConfig,
} from './tenant-interfaces';

export interface UpdateTenantOptions {
    tenant: {
        id: string;
        name?: string;
    };
    serviceOptions?: Dict<boolean>;
    /**
     * Should all the packages be processed ?
     */
    allPackages?: boolean;
    /**
     * List of packages to process (when allPackages is not set)
     */
    packages?: string[];
}
export interface InitTenantOptions extends UpdateTenantOptions {
    customer: {
        id: string;
        name?: string;
    };
    layers?: string[];
}

const logger = loggers.application;

export abstract class TenantService {
    /**
     * Initialize tenant data into an existing schema
     *
     * @param context
     * @param customerId: customer's id
     * @param customerName: customer's name
     * @param tenantId: tenant's id
     * @param tenantName: tenant's name
     */
    // eslint-disable-next-line class-methods-use-this
    static async initTenantIdentifiers(
        context: Context,
        customerId: string,
        customerName: string,
        tenantId: string,
        tenantName: string,
    ): Promise<void> {
        logger.debug(
            () =>
                `initTenantIdentifiers customer's id:${customerId} customer's name:${customerName} tenant's id:${tenantId}`,
        );
        await Context.tenantManager.ensureTenantExists(context, {
            customer: { id: customerId, name: customerName },
            tenant: { id: tenantId, name: tenantName },
        });
    }

    /**
     * Reload tenant layers of provided factories
     *
     * @param tenantId: tenantId (or null when reloading shared factories)
     * @package layers
     */
    static async reloadSetupData(
        application: Application,
        tenantId: string | null,
        factories: NodeFactory[],
        options: {
            /**
             * Should we always reload CSV files (i.e. skip the check on checksum) ?
             * This flag will mainly be set when recording SQL files
             */
            forceReloadOfCsv?: boolean;
            /**
             * The (optional) data to restore.
             * When not set, the data to restore will be extracted from CSV files from the local filesystem
             * When set, data will contain the data to load, for every factory to reload.
             */
            data?: SqlFileDataSets;
        },
    ): Promise<{ factory: NodeFactory; data: any[] }[]> {
        const data = options.data || (await readSetupDataFromCsvFiles(application, factories));

        await upgradeSetupData(application, tenantId, data, options);

        // TODO: this return is ignored when options.data is present. Refactor this method as 2 separate methods
        return factories.map(factory => ({ factory, data: data[factory.name]?.rows || [] }));
    }

    /**
     * Returns the list of customers (their names) to skip for a given cluster
     */
    private static _getCustomerNamesToSkip(clusterName: string): string[] {
        if (clusterName === 'anon-eu-prd') {
            // To speed-up the duration of PRs, we won't reload the setup data on all its tenants
            // The following ids are mainly _ids used for tests, internal ids (devs, pms, ...)
            return [
                'Acuity (Partner)',
                'AIG',
                'AthenaRacing',
                'cecsa',
                'CloudOpsBUX3Team',
                'counterpointtrading512',
                'COUTURIER BUSINESS INC',
                'Dominik Schaetzel (Internal)',
                'Elsabe Swanepoel',
                'fabricVision GmbH',
                'ferrotec',
                'Gerpol',
                'gmgmining',
                'Guillaume Leclere',
                'Herbert Hofmann',
                'INEOS SAILING LIMITED',
                'INFOTEM',
                'IshaChishty',
                'Kevin Quan',
                'Lorge Consulting',
                'Lorraine Fisher',
                'Marie-Jose Miquel (Internal)',
                'me-filtertechnik GmbH',
                'meyrickestate',
                'Monediere Sebastien',
                'MONT BLANC ET CHOCOLAT',
                'Paul Luu',
                'Product Engineering L3',
                'ptlsa',
                'Raghul test customer',
                'SeedCo',
                'Siyavuya',
                'smithcoopervar',
                'SOCABI',
                'therser',
                'Thierry Mehl',
                'Thorsten Himmelmann',
                'Tiger Team (Chiovonne)',
                'toyekenning',
                'Ulrich Kuckert (Internal)',
                'wildfarmed',
                'Xplor-SDMO-RB',
                'ZNG Consulting',
            ];
        }
        if (clusterName === 'cluster-ci') {
            return ['XTreeM ATP - DoNotDelete'];
        }
        return [];
    }

    /**
     * Returns the list of tenants (their ids) to skip when reloading the setup data
     * @param application
     * @param clusterName
     */
    static async getTenantIdsToSkipForSetupData(application: Application, clusterName: string): Promise<string[]> {
        if (clusterName.length === 0) return [];
        const customersToSkip = TenantService._getCustomerNamesToSkip(clusterName);
        if (customersToSkip.length === 0) return [];
        const tenantInfos = await application.withReadonlyContext(null, context =>
            Context.tenantManager.getTenantsInfo(context),
        );
        return tenantInfos
            .filter(tenantInfo => customersToSkip.includes(tenantInfo.customer.name))
            .map(tenantInfo => tenantInfo.id);
    }

    /**
     * Update the setup data for all the packages of the application
     */
    static async updateSetupData(
        application: Application,
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        options: {
            /**
             * Should we always reload CSV files (i.e. skip the check on checksum) ?
             * This flag will mainly be set when recording SQL files
             */
            forceReloadOfCsv?: boolean;
            /**
             * The (optional) name of the cluster
             */
            clusterName?: string;
        },
    ): Promise<void> {
        logger.info(`[${application.schemaName}] update setup data for application ${application.name}`);

        const tenantIdsToSkip = options.clusterName
            ? await TenantService.getTenantIdsToSkipForSetupData(application, options.clusterName)
            : [];

        if (tenantIdsToSkip.length > 0) {
            logger.warn(
                `[${application.schemaName}] ${tenantIdsToSkip.length} tenants will be skipped, based on the cluster name ${options.clusterName}: ${tenantIdsToSkip.join(',')}`,
            );
        }

        const getTenantIds = async (): Promise<string[]> => {
            const allTenantIds = await application.asRoot.withReadonlyContext(null, context =>
                Context.tenantManager.listTenantsIds(context),
            );
            if (tenantIdsToSkip.length === 0) return allTenantIds;
            return allTenantIds.filter(tenantId => !tenantIdsToSkip.includes(tenantId));
        };
        let tenantIds = await getTenantIds();

        if (tenantIds.length === 0) {
            logger.info('No tenant are registered on the cluster.');
            return;
        }

        logger.info(
            `[${application.schemaName}] the following ${tenantIds.length} tenants should be updated : ${tenantIds.join(',')}`,
        );

        // The list of already processed tenants
        const processedTenantIds: string[] = [];

        // Reloading the setup data of all the tenants might take a lot of time, especially when dealing with a cluster with many
        // tenants. A lot of things may happen during the execution, including tenant deletions and/or tenant creations.
        // That's why we can't build the list of tenants at the beginning of the update and simply loop over them.
        // Instead, on every iteration, we re-read the list of tenantIds.
        let tenantId: string | undefined = tenantIds[0];
        while (tenantId !== undefined) {
            processedTenantIds.push(tenantId);
            logger.info(`- update setup data for tenant ${tenantId}`);
            const activePackagesName = await application.asRoot.withCommittedContext(tenantId, context =>
                context.getActivePackageNames(),
            );

            const factories = application
                .getSqlPackageFactories()
                .filter(factory => !factory.isSharedByAllTenants && activePackagesName.includes(factory.package.name));
            const data = await readSetupDataFromCsvFiles(application, factories);

            await upgradeSetupData(application, tenantId, data, options);

            // Get the next tenantId to process
            tenantIds = await getTenantIds();
            tenantId = lodash.difference(tenantIds, processedTenantIds)[0];
        }

        logger.info(
            `[${application.schemaName}] the setup data were reloaded for the following ${
                processedTenantIds.length
            } tenants : ${processedTenantIds.join(',')}`,
        );
    }

    /**
     * Initializes tenant data into an existing schema
     *
     * @param  initTenantOptions: parameters
     */
    static async initTenant(application: Application, initTenantOptions: InitTenantOptions): Promise<void> {
        const contextOptions = {
            config: ConfigManager.current,
        };

        // Make sure the tenant exists in the database
        await application.asRoot.withCommittedContext(
            null,
            context =>
                Context.tenantManager.ensureTenantExists(context, {
                    tenant: {
                        id: initTenantOptions.tenant.id,
                        name: initTenantOptions.tenant.name || 'Automatically created by initTenant',
                    },
                    customer: {
                        id: initTenantOptions.customer.id,
                        name: initTenantOptions.customer.name || 'Automatically created by initTenant',
                    },
                }),
            {
                description: () => `initTenant.1(${initTenantOptions.tenant.id})`,
            },
        );

        await loadLayersFromCsvFiles(application, initTenantOptions.layers || ['setup'], initTenantOptions.tenant.id, {
            skipSharedTables: true,
            // invalidation is done below
            skipGlobalCacheInvalidation: true,
        });

        await application.asRoot.withCommittedContext(
            initTenantOptions.tenant.id,
            async context => {
                await this.initTenantIdentifiers(
                    context,
                    initTenantOptions.customer.id,
                    initTenantOptions.customer.name || `Customer ${initTenantOptions.customer.id}`,
                    initTenantOptions.tenant.id,
                    initTenantOptions.tenant.name || `Tenant ${initTenantOptions.tenant.id}`,
                );

                // create the package allocations for the tenant and activate the relevant packages
                await application.packageManager.createOrUpgradePackageAllocations(context);

                await application.serviceOptionManager.createOrUpgradeServiceOptionStates(
                    context,
                    initTenantOptions.serviceOptions,
                );

                // invalidate any cached data for the given tenant in case of delete/init with the same tenant Id
                await context.application.invalidateGlobalCache(context);
            },
            {
                description: () => `initTenant.2(${initTenantOptions.tenant.id})`,
                ...contextOptions,
            },
        );
    }

    /**
     * Updates tenant data into an existing schema
     *
     * @param tenantId: tenant's id
     * @package layers
     */
    static async updateTenant(application: Application, updateTenantOptions: UpdateTenantOptions): Promise<void> {
        await application.asRoot.withCommittedContext(
            updateTenantOptions.tenant.id,
            async context => {
                await application.packageManager.createOrUpgradePackageAllocations(
                    context,
                    updateTenantOptions.packages,
                );
                await application.serviceOptionManager.createOrUpgradeServiceOptionStates(
                    context,
                    updateTenantOptions.serviceOptions,
                );

                // invalidate any cached data for the given tenant to force reload service options on all instances
                await context.application.invalidateGlobalCache(context);
            },
            {
                config: ConfigManager.current,
                description: () => `updateTenant(${updateTenantOptions.tenant.id})`,
            },
        );
    }

    /**
     * Delete tenant data into an existing schema
     *
     * @param tenantId: tenant's id
     */
    static async deleteTenant(application: Application, tenantId: string): Promise<void> {
        await TenantDataManager.deleteTenant(application, tenantId);
    }

    /**
     * Export tenant data of an existing schema
     * @param application
     * @param exportConfig
     */
    static async exportTenant(application: Application, exportConfig: TenantExportOptions): Promise<void> {
        await TenantDataManager.exportTenant(application, { ...exportConfig, rootDir: application.dir });
    }

    /**
     * Export tenants data of an existing schema by using tenants name regex
     */
    static async exportMultiTenants(application: Application, exportConfig: MultiTenantsExportOptions): Promise<void> {
        const tenantsToProcess = await application.asRoot.withUncommittedContext(null, async context => {
            const tenants = (await Context.tenantManager.getTenantsInfo(context)).filter(tenant => {
                if (minimatch(tenant.directoryName, exportConfig.tenantSelector)) {
                    return true;
                }
                return tenant.id === exportConfig.tenantSelector;
            }) as TenantInfoBase[];

            if (tenants.length === 0) {
                logger.warn(`[${application.schemaName}] no tenants found to export.`);
            }
            if (tenants.length > 1 && exportConfig.exportId)
                throw new Error(`[${application.schemaName}] cannot supply tenant exportId with multiple exports`);

            return tenants;
        });

        await asyncArray(tenantsToProcess).forEach(async tenant => {
            const name = tenant.directoryName || tenant.id;
            logger.info(`[${application.schemaName}] exporting tenant '${name}'`);
            await TenantDataManager.exportTenant(application, {
                ...exportConfig,
                tenantId: tenant.id,
                directoryName: tenant.directoryName,
                rootDir: application.dir,
            });
            logger.info(`[${application.schemaName}] successfully exported tenant '${name}'`);
        });
    }

    /**
     * Import tenant data from S3 or local ZIP
     *
     * @param tenantId: tenant's id
     */
    static async importTenant(application: Application, importConfig: TenantImportConfig): Promise<void> {
        await TenantDataManager.importTenant(application, importConfig);
    }

    /**
     * Reset the documents bound to a tenant (salesOrders, purchaseOrders, ...).
     * This will not reset the whole tenant (for instance, the items will be kept).
     * Only the factories flagged as 'isClearedByReset' will be reset.
     */
    static async resetTenantDocuments(
        application: Application,
        resetOperationConfig: ResetTenantOperationConfig,
    ): Promise<void> {
        await TenantDataManager.resetTenantDocuments(application, resetOperationConfig);
    }
}
