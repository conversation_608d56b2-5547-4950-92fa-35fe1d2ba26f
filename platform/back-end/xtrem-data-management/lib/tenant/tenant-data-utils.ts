import { asyncArray, AsyncResponse, Context } from '@sage/xtrem-core';
import { CopyOptions } from '@sage/xtrem-postgres';
import * as fs from 'fs';
import { loggers } from '../util/loggers';
import { SchemaInfo, SchemaInfoOptions, TableInfo } from './schema-info';

/** @internal */
export const logger = loggers.tenant;

/** @internal */
export const defaultCopy = {
    delimiter: ';',
    header: true,
} as CopyOptions;

/** @internal */
export class TenantDataUtils {
    /**
     * Visit all tables (shared or not) that have tenant data and return the list of those tables.
     *
     * @param context
     * @param body
     */

    static async visitTenantTables(
        context: Context,
        body: (table: TableInfo) => AsyncResponse<void>,
        options?: SchemaInfoOptions & { reverseOrder?: boolean },
    ): Promise<TableInfo[]> {
        const tables = (await new SchemaInfo(context.application).getSortedTables(options)).filter(
            t => options?.includeAll || t.columns.some(c => c.name && ['_tenant_id', 'tenant_id'].includes(c.name)),
        );
        const sortedTables = options?.reverseOrder ? tables.reverse() : tables;
        await asyncArray(sortedTables).forEach(table => body(table));
        return sortedTables;
    }

    /**
     * check to see if location passed in an S3 location
     * Note: s3Uri looks like s3://xtrem-dev-eu-global/tenants/export_id.tgz
     * @param location
     * @returns
     */
    static isS3Uri(location: string): boolean {
        return /^s3:/i.test(location);
    }

    static cleanup(filePath: string, zipTarget: string | null): void {
        if (zipTarget) {
            logger.info(`Deleting ${zipTarget}`);
            fs.unlinkSync(zipTarget);
        }
        logger.info(`Deleting all files of ${filePath}`);
        fs.rmSync(filePath, { recursive: true });
    }
}
