// WAS IN xtrem-data-management before
/**
 * This scripts is only intended to be invoked from xrtem-cli
 */

import { extractLayerToCsvFiles } from '@sage/xtrem-cli-layers';
import {
    AnyRecord,
    Application,
    asyncArray,
    DatabaseSqlContext,
    Dict,
    Logger,
    rootUserEmail,
    Test,
} from '@sage/xtrem-core';
import { TenantService } from '../tenant/tenant-service';

interface TenantRecord {
    tenant_id: string;
    name: string;
    customer: string;
}

interface CustomerRecord {
    _id: string;
    customer_id: string;
    name: string;
}

interface UserRecord {
    _tenant_id: string;
    _update_user: number;
    _create_user: number;
    first_name: string;
    last_name: string;
    email: string;
}

export class DataManagementCli {
    static async dropDatabaseIfExists(): Promise<void> {
        await new DatabaseSqlContext().dropDatabaseIfExists();
    }

    static async dropSchemaIfExists(schemaName: string): Promise<void> {
        await new DatabaseSqlContext().dropSchemaIfExists(schemaName);
    }

    /** @internal */
    private static async createItems(
        logger: Logger,
        application: Application,
        options: {
            // The name of the table
            tableName: string;
            // The data to insert to the table
            itemsData: AnyRecord[];
            // Should the errors be thrown (false) or only logged (true) ?
            ignoreErrors: boolean;
            // Is the _id of the table an auto-incremented column ?
            autoIncrement: boolean;
            // A progres callback
            progressCb: (itemData: AnyRecord) => void;
            // this callback will be invoked if an error occurs (to log the error)
            errorCb: (itemData: AnyRecord, err: any) => void;
        },
    ): Promise<void> {
        await application.asRoot.withCommittedContext(
            null, // Will not be used as the itemsData already contain the tenantIds
            context =>
                asyncArray(options.itemsData).forEach(async itemData => {
                    const columns = options.autoIncrement
                        ? Object.keys(itemData).filter(
                              colName =>
                                  // Do not try to inject the _id (it's autoincremented and may lead to duplicate keys errors)
                                  colName !== '_id',
                          )
                        : Object.keys(itemData);

                    const params = columns.reduce(
                        (total, colName, colIdx) => {
                            total.columns.push(colName);
                            total.aliases.push(`$${colIdx + 1}`);
                            const originalVal = itemData[colName];
                            // Previously, the '_source_id' column was a nullable string but it has been
                            // moved to a not-nullable string.
                            let valToSet = colName === '_source_id' && originalVal == null ? '' : originalVal;
                            // if whoami properties are not set or are invalid
                            if (['_createUser', '_updateUser'].includes(colName)) {
                                if (valToSet == null || valToSet === '' || !Number.isFinite(valToSet)) {
                                    valToSet = 1;
                                }
                            }
                            total.args.push(valToSet);
                            return total;
                        },
                        { columns: [], aliases: [], args: [] } as {
                            columns: string[];
                            aliases: string[];
                            args: any[];
                        },
                    );
                    options.progressCb(itemData);
                    try {
                        await context.executeSql(
                            `INSERT INTO ${context.schemaName}.${options.tableName} (${params.columns.join(
                                ',',
                            )}) VALUES (${params.aliases.join(',')}) ON CONFLICT DO NOTHING`,
                            params.args,
                        );
                        // LATER await application.getFactoryByTableName(options.tableName).invalidateCache(context);
                    } catch (err) {
                        if (!options.ignoreErrors) throw err;
                        options.errorCb(itemData, err);
                    }
                }),
            { description: () => `createItems(${options.tableName})` },
        );
    }

    // TODO: Remove later - temporary function to reset the schema while the upgrade is finalized
    static async resetSchema(
        application: Application,
        options: {
            layersAsString?: string;
            doNotExitOnErrors?: true;
            logger: Logger;
            callbacks: {
                createSchema: () => Promise<void>;
                quitWithError: (err: Error) => void;
            };
        },
    ): Promise<void> {
        try {
            let tenantsData: TenantRecord[] = [];
            let customersData: CustomerRecord[] = [];
            let usersData: UserRecord[] = [];

            // Note: the 'reset' command uses SQL commands to be as robust as possible (do not rely on any manager)

            /**
             * Backup data
             */
            await application.createContextForDdl(async context => {
                try {
                    tenantsData = await context.executeSql<TenantRecord[]>(
                        `SELECT * from ${context.schemaName}.sys_tenant`,
                        [],
                    );
                    options.logger.info(`Fetched ${tenantsData.length} tenant(s)`);
                    tenantsData.forEach(tenantData => {
                        options.logger.verbose(() => `Fetched tenant ${tenantData.tenant_id}: ${tenantData.name}`);
                    });
                } catch (err) {
                    options.logger.error(`Error while fetching tenants: ${err.toString()}`);
                }
            });

            await application.createContextForDdl(async context => {
                try {
                    customersData = await context.executeSql<CustomerRecord[]>(
                        `SELECT * from ${context.schemaName}.sys_customer`,
                        [],
                    );
                    options.logger.info(`Fetched ${customersData.length} customer(s)`);

                    customersData.forEach(customerData => {
                        options.logger.verbose(
                            () => `Fetched customer ${customerData.customer_id}: ${customerData.name}`,
                        );
                    });
                } catch (err) {
                    options.logger.error(`Error while fetching customers: ${err.toString()}`);
                }
            });

            await application.createContextForDdl(async context => {
                try {
                    usersData = await context.executeSql<UserRecord[]>(`SELECT * from ${context.schemaName}.user`, []);
                    options.logger.info(`Fetched ${usersData.length} user(s)`);
                    usersData.forEach(userData => {
                        options.logger.verbose(
                            () => `Fetched user ${userData.email}: ${userData.first_name} ${userData.last_name}`,
                        );
                    });
                } catch (err) {
                    options.logger.error(`Error while fetching users: ${err.toString()}`);
                }
            });

            /**
             * Create application schema with force option (drop and recreate schema)
             */
            await options.callbacks.createSchema();

            // Make sure that we don't miss any tenant
            const actualTenantIds = tenantsData.map(tenantData => tenantData.tenant_id);
            const missingTenantIdsByFirstUser: Dict<UserRecord> = {};
            usersData.forEach(userData => {
                if (actualTenantIds.includes(userData._tenant_id)) return;
                if (missingTenantIdsByFirstUser[userData._tenant_id] != null) return;
                missingTenantIdsByFirstUser[userData._tenant_id] = userData;
            });

            const initTenantData: Dict<{
                tenantId: string;
                tenantName: string;
                customerId: string;
                customerName: string;
            }> = {};

            tenantsData.forEach(tenantData => {
                const customer = customersData.find(c => c._id === tenantData.customer);
                if (!customer) {
                    // Should never happen as the customer on the sys_tenant table is not nullable
                    // and an invalid value will break the foreign key to the sys_customer table
                    throw new Error(`Data inconsistency: could not find customer ${tenantData.customer}`);
                }
                initTenantData[tenantData.tenant_id] = {
                    tenantId: tenantData.tenant_id,
                    tenantName: tenantData.name,
                    customerId: customer.customer_id,
                    customerName: customer.name,
                };
            });

            // Remove the customer of every existing tenant from customerData
            // (We must create the missing customers as the initTenant operation will not do it for us)
            customersData = customersData.filter(
                customer => !Object.values(initTenantData).some(dta => dta.customerId === customer.customer_id),
            );

            /**
             * Restore customers
             */
            options.logger.info(`Re-create ${customersData.length} customer(s)`);
            await this.createItems(options.logger, application, {
                tableName: 'sys_customer',
                itemsData: customersData as unknown[] as AnyRecord[],
                ignoreErrors: false,
                autoIncrement: true,
                progressCb: customerData => {
                    options.logger.verbose(() => `Recreate customer ${customerData.name}`);
                },
                errorCb: (customerData, err) => {
                    options.logger.error(`Error ${err.toString()} while creating customer: ${customerData._id}`);
                },
            });

            const layers = options.layersAsString?.split(',').map(layer => layer.trim());
            /**
             * Create missing tenants
             */
            options.logger.info(`Create ${Object.keys(missingTenantIdsByFirstUser).length} missing tenants`);
            await asyncArray(Object.keys(missingTenantIdsByFirstUser)).forEach(async tenantId => {
                options.logger.verbose(() => `Create tenant ${tenantId}`);
                await TenantService.initTenant(application, {
                    tenant: {
                        id: tenantId,
                    },
                    customer: {
                        id: tenantId,
                    },
                    layers,
                    allPackages: true,
                });
            });

            /**
             * Init other tenants
             */
            options.logger.info(`Init ${tenantsData.length} existing tenants`);
            await asyncArray(tenantsData).forEach(async tenantData => {
                options.logger.info(`\t-init tenant ${tenantData.tenant_id}`);
                const initData = initTenantData[tenantData.tenant_id];
                if (initData == null) {
                    options.logger.warn(`No data for tenant ${tenantData.tenant_id}`);
                    return;
                }

                await TenantService.initTenant(application, {
                    tenant: {
                        id: initData.tenantId,
                        name: initData.tenantName,
                    },
                    customer: {
                        id: initData.customerId,
                        name: initData.customerName,
                    },
                    layers,
                    allPackages: true,
                });
            });

            // Get the list of root users per tenant
            const rootUsersByTenant = (
                await application.asRoot.withReadonlyContext(null, context =>
                    context.executeSql<{ _tenant_id: string; _id: number }[]>(
                        `SELECT _tenant_id, _id FROM ${context.schemaName}.user WHERE email=$1`,
                        [rootUserEmail],
                    ),
                )
            ).reduce((total, row) => {
                total[row._tenant_id] = row._id;
                return total;
            }, {} as Dict<number>);

            // Change the update/create users of every user to reference the root user of its tenant
            usersData.forEach(userData => {
                const rootUserId = rootUsersByTenant[userData._tenant_id];
                if (rootUserId == null)
                    throw new Error(`Could not find any root user for tenant ${userData._tenant_id}`);
                userData._update_user = rootUserId;
                userData._create_user = rootUserId;
            });

            // Restore the users
            options.logger.info(`Re-create ${usersData.length} user(s)`);
            await this.createItems(options.logger, application, {
                tableName: 'user',
                itemsData: usersData as unknown[] as AnyRecord[],
                ignoreErrors: true,
                autoIncrement: true,
                progressCb: userData => {
                    options.logger.info(`\trecreate user ${userData.email}`);
                },
                errorCb: (userData, err) => {
                    // resetSchema has to stop with an error in this case:
                    throw new Error(`Error ${err.toString()} while creating user: ${userData.email}`);
                },
            });
        } catch (e) {
            if (options.doNotExitOnErrors) throw e;
            else options.callbacks.quitWithError(e);
        }
    }

    static async extractData(application: Application, tenantId: string | undefined, layer: string): Promise<void> {
        await extractLayerToCsvFiles(application, layer, tenantId || Test.defaultTenantId);
    }
}
