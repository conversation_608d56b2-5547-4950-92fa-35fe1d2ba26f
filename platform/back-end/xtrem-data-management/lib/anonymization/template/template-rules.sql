SELECT pg_catalog.set_config('search_path', 'public', false);

CREATE EXTENSION anon CASCADE;
SELECT anon.init();


CREATE OR REPLACE FUNCTION <anonymize_schema_name_placeholder>.per_char_random(input_string TEXT, preserve_list TEXT DEFAULT '''\ "@!#$%^&*()-=_+,.?<>/|:;{}[]«»ºª~€¨£§')
RETURNS TEXT AS $$
DECLARE
  randomized_string TEXT;
  i INTEGER;
  random_char TEXT;
  ascii_value INTEGER;
BEGIN
  FOR i IN 1 .. LENGTH(input_string) LOOP
    random_char := SUBSTRING(input_string, i, 1);
    ascii_value := ASCII(random_char);
    random_char :=
      CASE
        WHEN STRPOS(preserve_list, random_char) > 0 THEN random_char
        WHEN ascii_value BETWEEN 48 AND 57 THEN CHR(48 + FLOOR(RANDOM() * 10)::INTEGER)
        WHEN ascii_value BETWEEN 65 AND 90 THEN CHR(65 + FLOOR(RANDOM() * 26)::INTEGER)
        ELSE CHR(97 + FLOOR(RANDOM() * 26)::INTEGER)
      END;
    randomized_string := COALESCE (randomized_string,'')||random_char;
  END LOOP;
RETURN COALESCE (randomized_string,'');
END;
$$
  LANGUAGE plpgsql IMMUTABLE
  PARALLEL RESTRICTED -- because random
  SECURITY INVOKER
;

CREATE OR REPLACE FUNCTION <anonymize_schema_name_placeholder>.anonymize_jsonb_value(j JSONB)
RETURNS JSONB AS $$
SELECT
     COALESCE (jsonb_object_agg(key,<anonymize_schema_name_placeholder>.per_char_random(e.value)),'{}')
FROM jsonb_each_text(j) e FULL OUTER JOIN jsonb_each_text(j) new USING (key)
$$
  LANGUAGE SQL
  VOLATILE
  PARALLEL RESTRICTED -- because random
  SECURITY INVOKER
;

CREATE OR REPLACE FUNCTION <anonymize_schema_name_placeholder>.anonymize_external_reference_array_string(j JSONB)
RETURNS JSONB AS $$
DECLARE
	arrayKey constant TEXT := 'array';
	resultVal JSONB;
BEGIN
SELECT
	jsonb_build_object(arrayKey , COALESCE(
		array_agg(
		  <anonymize_schema_name_placeholder>.anonymize_jsonb_value(value)
		),'{}')
	) into resultVal
FROM (SELECT arrayKey k,* FROM jsonb_array_elements(jsonb_extract_path(j,arrayKey))) c;
RETURN resultVal;
END;
$$
  LANGUAGE plpgsql
  VOLATILE
  PARALLEL RESTRICTED -- because random
  SECURITY INVOKER
;

CREATE OR REPLACE FUNCTION <anonymize_schema_name_placeholder>.anonymize_website(url TEXT)
RETURNS TEXT AS $$
DECLARE
	separator constant TEXT := '://';
	resultVal TEXT;
BEGIN
	SELECT
		CASE
		  WHEN STRPOS(url,separator) >0 THEN CONCAT(SUBSTRING(url,1,STRPOS(url,separator)+3), <anonymize_schema_name_placeholder>.per_char_random(SUBSTRING(url,STRPOS(url,separator)+3)))
		  ELSE <anonymize_schema_name_placeholder>.per_char_random(url)
		END
INTO resultVal;
RETURN  resultVal;
END;
$$
  LANGUAGE plpgsql
  VOLATILE
  PARALLEL RESTRICTED -- because random
  SECURITY INVOKER
;

CREATE OR REPLACE FUNCTION <anonymize_schema_name_placeholder>.anonymize_binary(type TEXT)
RETURNS BYTEA AS $$
DECLARE
	resultVal BYTEA;
BEGIN
SELECT
		CASE
      <anonymize_binary_function_placeholder>
		END
INTO resultVal;
RETURN resultVal;
END;
$$
  LANGUAGE plpgsql
  VOLATILE
  SECURITY INVOKER
;

CREATE OR REPLACE FUNCTION <anonymize_schema_name_placeholder>.anonymize_user_email(email TEXT)
RETURNS TEXT AS $$
SELECT
    CASE WHEN NULLIF(right(email,17),'@localhost.domain') IS NULL
            THEN email
            ELSE <anonymize_schema_name_placeholder>.per_char_random(email)
    END AS results
$$
  LANGUAGE SQL
  VOLATILE
  PARALLEL RESTRICTED -- because random
  SECURITY INVOKER
;

CREATE OR REPLACE FUNCTION <anonymize_schema_name_placeholder>.anonymize_rcs(rcs TEXT)
RETURNS TEXT AS $$
SELECT
    CASE WHEN NULLIF(rcs,'') IS NULL
            THEN rcs
            ELSE  CONCAT('RCS Anon A ',<anonymize_schema_name_placeholder>.per_char_random(rcs))
    END AS results
$$
  LANGUAGE SQL
  VOLATILE
  PARALLEL RESTRICTED -- because random
  SECURITY INVOKER
;
