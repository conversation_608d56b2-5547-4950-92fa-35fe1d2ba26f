import { Application, Context, NodeFactory } from '@sage/xtrem-core';
import * as fs from 'fs';
import { uniq } from 'lodash';
import * as fsp from 'path';
import { TableInfo } from '../tenant/schema-info';
import { anonImageBase64, blankImageBase64, blankPdfBase64 } from '../tenant/tenant-data-export';
import { TenantDataUtils } from '../tenant/tenant-data-utils';
import { loggers } from '../util/loggers';

const logger = loggers.application;

export const excludedSysTable = [
    'sys_tenant',
    'sys_message_history',
    'sys_notification_history',
    'sys_service_option_state',
];

const maxPgAnonymizerStringLength = 20;
const pgAnonymizerBinaryFunctionPlaceholder = '<anonymize_binary_function_placeholder>';
const pgAnonymizerSchemaNamePlaceholder = '<anonymize_schema_name_placeholder>';

export abstract class AnonymizationService {
    /**
     * Generate PG Anonymizer rules an existing code base
     */
    static async generateRules(application: Application, outputPath: string): Promise<void> {
        await application.asRoot.withReadonlyContext(null, context =>
            AnonymizationService.extractRulesFromTables(context, outputPath),
        );
        logger.info('Successfully exported rules');
    }

    /**
     * @internal
     *  Generate PG Anonymizer rules an existing code base to data/exports/pg-anonymizer
     *
     * @param context the context
     * @param outputPath the output path
     */
    static async extractRulesFromTables(context: Context, outputPath: string): Promise<void> {
        const exportPath = outputPath || 'data/exports/pg-anonymizer';
        fs.mkdirSync(exportPath, { recursive: true });

        const resultMasks: string[] = AnonymizationService.prepareTemplate(context.application.schemaName);
        resultMasks.push('-- Anonymization rules');
        await TenantDataUtils.visitTenantTables(
            context,
            (table: TableInfo) => {
                if (table.name === 'sys_csv_checksum') {
                    // Ignore for now
                    return;
                }
                const columns: string[] = [];
                const keyColumns: string[] = [];
                const columnsInUniqueIndex = new Set<string>();
                table.indexes?.forEach(ind => {
                    if (!ind.isUnique) return;
                    ind.columnNames.forEach(name => columnsInUniqueIndex.add(name));
                });
                table.columns.forEach(col => {
                    const columnName = col.name;
                    if (!columnName) return;
                    columns.push(columnName);
                });

                const factory = context.application.tryGetFactoryByTableName(table.name);

                if (factory?.naturalKey) {
                    factory.naturalKey.forEach(naturalKeyPropName => {
                        const naturalKeyProp = factory.findProperty(naturalKeyPropName);
                        // Note: the natural key might be inherited from a base table and its columns may not
                        // be declared in the current table but only in the base table.
                        if (
                            naturalKeyProp &&
                            naturalKeyProp.columnName &&
                            table.columnsByName[naturalKeyProp.columnName]
                        ) {
                            keyColumns.push(naturalKeyProp.columnName);
                        }
                    });
                }
                resultMasks.push(...AnonymizationService.visitTable(context, table, uniq([...keyColumns, ...columns])));
            },
            {
                excludedTables: excludedSysTable,
            },
        );
        fs.writeFileSync(
            `${exportPath}/pg-anon-rules-v${context.application.version}.sql`,
            resultMasks.join('\n'),
            'utf8',
        );
    }

    /**
     * @internal
     * Prepare SQL template with binary anonymization function
     *
     * @param context
     * @param table
     * @param columnNames
     */
    private static prepareTemplate(schemaName: string): string[] {
        const templateMasks: string[] = [];
        templateMasks.push(
            ...fs
                .readFileSync(fsp.join(__dirname, 'template/template-rules.sql'))
                .toString()
                .replaceAll(pgAnonymizerSchemaNamePlaceholder, schemaName)
                .split('\n'),
        );
        const binaryFunctionIndex = templateMasks.findIndex(element =>
            element.includes(pgAnonymizerBinaryFunctionPlaceholder),
        );
        if (binaryFunctionIndex > -1) {
            const binaryFunctionContent: string[] = [];
            binaryFunctionContent.push(`WHEN type = 'image' THEN decode ('${anonImageBase64}','base64')`);
            binaryFunctionContent.push(`WHEN type = 'pdf' THEN decode ('${blankPdfBase64}','base64')`);
            binaryFunctionContent.push(`ELSE decode ('${blankImageBase64}','base64')`);
            templateMasks.splice(binaryFunctionIndex, 1, ...binaryFunctionContent);
        }

        return templateMasks;
    }

    /**
     * @internal
     * Visit a table to extract rules.
     *
     * @param context
     * @param table
     * @param columnNames
     */
    private static visitTable(context: Context, table: TableInfo, columnNames: string[]): string[] {
        const schema = context.application.schemaName;
        const factory = context.application.tryGetFactoryByTableName(table.name);
        const masks: string[] = [];
        /** Find properties that have an exportValue set */
        masks.push(...AnonymizationService.extractExportValueRules(schema, factory, table, columnNames));
        /** Find properties that have an anonymizeMethod set */
        masks.push(...AnonymizationService.extractAnonymizeRules(schema, factory, table, columnNames));
        if (masks.length > 0) masks.unshift(`-- ${table.name}`);
        return masks;
    }

    /**
     * @internal
     * Visit a table to extract ExportValue rules.
     *
     * @param factory
     * @param table
     * @param columnNames
     */
    private static extractExportValueRules(
        schemaName: string,
        factory: NodeFactory | undefined,
        table: TableInfo,
        columnNames: string[],
    ): string[] {
        const masks: string[] = [];
        logger.info(`extract ExportValue rules from -- ${table.name}`);
        factory?.properties
            .filter(
                property =>
                    !property.isInherited &&
                    columnNames.includes(property.columnName || '') &&
                    property.decorator.exportValue !== undefined &&
                    property.isStored &&
                    property.columnName,
            )
            .forEach(property => {
                if (typeof property.decorator.exportValue === 'string')
                    masks.push(
                        `SECURITY LABEL FOR anon ON COLUMN ${schemaName}.${table.name}.${property.columnName} IS 'MASKED WITH VALUE ''${property.decorator.exportValue}''';`,
                    );
                else
                    masks.push(
                        `SECURITY LABEL FOR anon ON COLUMN ${schemaName}.${table.name}.${property.columnName} IS 'MASKED WITH VALUE ${property.decorator.exportValue}';`,
                    );
            });
        return masks;
    }

    /**
     * @internal
     * Visit a table to extract Anonymize rules.
     *
     * @param factory
     * @param table
     * @param columnNames
     */
    private static extractAnonymizeRules(
        schemaName: string,
        factory: NodeFactory | undefined,
        table: TableInfo,
        columnNames: string[],
    ): string[] {
        const getSecurityLabel = (
            tableName: string,
            columnName: string,
            value: string | null,
            nullSubstitute: string,
        ): string => {
            return `SECURITY LABEL FOR anon ON COLUMN ${schemaName}.${tableName}.${columnName} IS E'MASKED WITH VALUE CASE WHEN COALESCE("${columnName}", \\'\\') = \\'\\' THEN ${nullSubstitute?.replaceAll(
                "'",
                "\\'",
            )} ELSE ${value?.replaceAll("'", "\\'")} END';`;
        };
        const masks: string[] = [];
        const profiler = logger.info(`extract Anonymize rules from -- ${table.name}`);
        factory?.properties
            .filter(
                property =>
                    !property.isInherited &&
                    columnNames.includes(property.columnName || '') &&
                    property.decorator.anonymizeMethod !== undefined &&
                    property.isStored &&
                    property.columnName,
            )
            .forEach(property => {
                switch (property.decorator.anonymizeMethod) {
                    case 'fixed':
                        masks.push(
                            getSecurityLabel(
                                table.name,
                                property.columnName || '',
                                property.decorator.anonymizeValue === null
                                    ? null
                                    : `'${property.decorator.anonymizeValue}'`,
                                "''",
                            ),
                        );
                        break;
                    case 'custom':
                        if (property.columnName?.includes('tax_id'))
                            masks.push(
                                `SECURITY LABEL FOR anon ON COLUMN ${schemaName}.${table.name}.${property.columnName} IS 'MASKED WITH FUNCTION ''ZZ1AA'' || ${schemaName}.per_char_random(${property.columnName})';`,
                            );
                        else if (property.columnName && ['rcs'].includes(property.columnName))
                            masks.push(
                                `SECURITY LABEL FOR anon ON COLUMN ${schemaName}.${table.name}.${property.columnName} IS 'MASKED WITH FUNCTION ${schemaName}.anonymize_rcs(${property.columnName})';`,
                            );
                        else if (property.columnName?.toLowerCase().includes('email'))
                            masks.push(
                                `SECURITY LABEL FOR anon ON COLUMN ${schemaName}.${table.name}.${property.columnName} IS 'MASKED WITH FUNCTION ${schemaName}.anonymize_user_email(${property.columnName})';`,
                            );
                        // localizedString and external reference
                        else if (
                            (property.type === 'string' && property.isLocalized) ||
                            (property.columnName &&
                                ['site', 'company'].includes(property.columnName) &&
                                table.name.startsWith('mrp'))
                        )
                            masks.push(
                                `SECURITY LABEL FOR anon ON COLUMN ${schemaName}.${table.name}.${property.columnName} IS 'MASKED WITH FUNCTION ${schemaName}.anonymize_jsonb_value(${property.columnName})';`,
                            );
                        // external reference array
                        else if (
                            property.columnName &&
                            ['sites', 'companies'].includes(property.columnName) &&
                            table.name.startsWith('mrp')
                        )
                            masks.push(
                                `SECURITY LABEL FOR anon ON COLUMN ${schemaName}.${table.name}.${property.columnName} IS 'MASKED WITH FUNCTION ${schemaName}.anonymize_external_reference_array_string(${property.columnName})';`,
                            );
                        else if (property.columnName && ['string_custom'].includes(property.columnName))
                            // we skip this case as it exists only in test
                            logger.info('We skip this "stringCustom" case as it exists only in test');
                        else
                            profiler.fail(
                                `Failed to generate rules for table ${table.name} property ${property.columnName}`,
                            );
                        break;
                    case 'url':
                        masks.push(
                            `SECURITY LABEL FOR anon ON COLUMN ${schemaName}.${table.name}.${property.columnName} IS 'MASKED WITH FUNCTION ${schemaName}.anonymize_website(${property.columnName})';`,
                        );
                        break;
                    case 'perCharRandom':
                        masks.push(
                            `SECURITY LABEL FOR anon ON COLUMN ${schemaName}.${table.name}.${property.columnName} IS 'MASKED WITH FUNCTION ${schemaName}.per_char_random(${property.columnName})';`,
                        );
                        break;
                    case 'binary':
                        masks.push(
                            `SECURITY LABEL FOR anon ON COLUMN ${schemaName}.${table.name}.${property.columnName} IS 'MASKED WITH FUNCTION ${schemaName}.anonymize_binary(''${property.decorator.anonymizeValue}'')';`,
                        );
                        break;
                    // hash, limited to max length of property or PG Anonymizer string length
                    case 'hash':
                        masks.push(
                            getSecurityLabel(
                                table.name,
                                property.columnName || '',
                                `anon.random_string(${Math.floor(
                                    (property.maxLength || maxPgAnonymizerStringLength) / 2,
                                )})`,
                                "''",
                            ),
                        );
                        break;
                    case 'hashLimit':
                        masks.push(
                            getSecurityLabel(
                                table.name,
                                property.columnName || '',
                                `anon.random_string(${property.decorator.anonymizeValue})`,
                                "''",
                            ),
                        );
                        break;
                    case 'random':
                        if (property.max) {
                            masks.push(
                                getSecurityLabel(
                                    table.name,
                                    property.columnName || '',
                                    `FLOOR(RANDOM()*${property.max})::BIGINT`,
                                    '0',
                                ),
                            );
                            break;
                        }

                        if (property.type === 'integer') {
                            masks.push(
                                getSecurityLabel(
                                    table.name,
                                    property.columnName || '',
                                    `FLOOR(RANDOM()*${Number.MAX_SAFE_INTEGER})::BIGINT`,
                                    '0',
                                ),
                            );
                            break;
                        }

                        masks.push(
                            getSecurityLabel(
                                table.name,
                                property.columnName || '',
                                `FLOOR(RANDOM()*${2 ** 31 - 1})::INTEGER`,
                                '0',
                            ),
                        );
                        break;
                    default:
                        profiler.fail(
                            `Failed to generate rules for table ${table.name} property ${property.columnName}`,
                        );
                        throw new Error(
                            `Failed to generate rules for table ${table.name} property ${property.columnName}`,
                        );
                }
            });
        return masks;
    }
}
