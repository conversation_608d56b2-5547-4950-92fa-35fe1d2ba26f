{"name": "@sage/xtrem-config", "description": "management of xtrem config and config header", "version": "58.0.2", "license": "UNLICENSED", "author": "Sage", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/index.js", "typings": "build/package-definition.d.ts", "dependencies": {"@sage/xtrem-log": "workspace:*", "@sage/xtrem-shared": "workspace:*", "ajv": "^8.17.1", "bytes": "^3.1.2", "chokidar": "^4.0.0", "js-yaml": "^4.1.0", "lodash": "^4.17.21"}, "devDependencies": {"@sage/xtrem-dts-bundle": "workspace:*", "@sage/xtrem-minify": "workspace:*", "@types/bytes": "^3.1.2", "@types/chai": "^4.3.6", "@types/js-yaml": "^4.0.6", "@types/lodash": "^4.14.198", "@types/mocha": "^10.0.1", "@types/node": "^22.10.2", "chai": "^4.3.10", "eslint": "^8.49.0", "mocha": "^10.8.2", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "~5.8.3"}, "scripts": {"build": "tsc -b -v . && pnpm dts-bundle", "build:binary": "pnpm clean && pnpm build && xtrem-minify -c -z 'build/**/*.js'", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build junit-report*", "dts-bundle": "xtrem-dts-bundle", "lint": "eslint -c .eslintrc.js --ext .ts lib", "lint:filename": "eslint --no-eslintrc -c .eslintrc-filename.js \"**\"", "test": "cross-env TZ=CET mocha --recursive --exit \"test/**/*@(-|.)test.ts\"", "test:ci": "cross-env TZ=CET JUNIT_REPORT_PATH=junit-report-core.xml JUNIT_REPORT_NAME='xtrem-core' c8 --reporter=lcov --reporter=json --reporter=cobertura --reporter=text mocha --recursive --exit \"test/**/*@(-|.)test.ts\" --reporter mocha-jenkins-reporter"}, "gitHead": "f0406ede1639145fdb322ebdf823bc87abddf4b8"}