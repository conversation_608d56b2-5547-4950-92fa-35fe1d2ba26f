import { ColumnTypeN<PERSON>, Dict, Log<PERSON><PERSON>l, Profiler<PERSON>allback, TlsConnectionOptions } from '@sage/xtrem-shared';
import * as postgres from 'pg';
import { ColumnJsonComment, ForeignKeyJsonComment, TableJsonComment } from './json-comment';

export { DatabaseError } from 'pg-protocol';
export { ColumnTypeName };

/**
 * A marker to not convert a literal in the SQL converter, as it is not required, e.g. NOW(), currval((pg_get_serial_sequence('foo'))
 */
export const dbCommandMarker = '%%DB_COMMAND%%';

export type Connection = postgres.PoolClient;

/**
 * The precision for the decimal columns - whatever the precision is in the property decorator, decimal columns are stored as (28,10)
 */
export const decimalColumnPrecision = 28;

/**
 * The scale for the decimal columns - whatever the precision is in the property decorator, decimal columns are stored as (28,10)
 */
export const decimalColumnScale = 10;

/** @internal */
export interface PoolReaderOptions {}

/** @internal */
export interface PoolWriterOptions {}

export interface SqlExecuteOptions {
    decimalColumns?: string[];
    fetchInfo?: any; // refine later (driver dependent?)
    outputVars?: SqlColumnDesc[];
    pageSize?: number;
    /**
     * Are unsafe SQL queries allowed ?
     * An unsafe query can contain the ' character (which could be used for SQL injections)
     */
    allowUnsafe?: boolean;
}

export interface PostgresSqlExecuteOptions extends SqlExecuteOptions {
    logLevel?: LogLevel;
}

export interface PostgresPoolReaderOptions extends PoolReaderOptions {
    logLevel?: LogLevel;
    pageSize?: number;
}

export interface CopyOptions {
    to?: string;
    format?: 'csv';
    delimiter?: string;
    header?: boolean;
    headers?: string[];
}

export interface SqlColumnDesc {
    name?: string;
    type?: string;
    typtyp?: number;
}

// special one for driver describe: type is number!
export interface SqlColumnInfo {
    name?: string;
    type?: number;
    typtyp?: number;
}

export type SqlColumnsDesc = Dict<SqlColumnDesc>;

type SqlTableSchemaElement = 'columns' | 'security' | 'indexes' | 'sequences' | 'foreignKeys' | 'triggers';

export interface SqlReadTableSchemaOptions {
    skipColumns?: boolean;
    skipSecurity?: boolean;
    skipIndexes?: boolean;
    skipSequences?: boolean;
    skipForeignKeys?: boolean;
    includes?: SqlTableSchemaElement[];
    /**
     * Do we want to fetch the comments for the tables, columns, fks, ... ?
     */
    getComments?: boolean;
    $diagnoses?: {
        $severity: string;
        $message: string;
    }[];
}

export interface SqlCreateTableOptions {
    isAltering?: boolean;
    skipCommands?: boolean;
    onlyIndexes?: boolean;
    skipDrop?: boolean;
    skipCreate?: boolean;
    skipSequences?: boolean;
    skipIndexes?: boolean;
    /**
     * Should the DEFAULT clause(s) be skipped when creating table/columns ?
     */
    skipDefault?: boolean;
    dropTrigger?: boolean;
    skipTriggers?: boolean;
    skipGrant?: boolean;
    skipForeignKeys?: boolean;
    skipConstraints?: boolean;
    temp?: boolean;
    hasNotifyTriggers?: boolean;
    deferredSql?: DeferredSql;
}

export interface DeferredSql {
    alterColumns?: string[];
    updateColumns?: string[];
}

export interface EnumType {
    name: string;
    values: Dict<number>;
}

// driver types
export interface ColumnDefinition {
    name: string;
    maxLength?: number;
    isNullable?: boolean;
    default?: string | null;
    colType?: string;
    type?: ColumnTypeName;
    /** Is this a system column ? (_update_tick, ...) */
    isSystem?: boolean;
    /** Is this column auto-increment ? (i.e.: the value is assigned by the database engine when inserting) */
    isAutoIncrement?: boolean;
    /** is this column a non nullable self reference? */
    isSelfReference?: boolean;
    precision?: number;
    scale?: number;
    useFloatingPointFormat?: boolean;
    loadPath?: string;
    enumDataType?: EnumType;
    /** Is property value an encrypted string ? (only applies to string properties) */
    isEncryptedString?: boolean;
    /**
     * The comment of the SQL object (JSON format)
     */
    comment?: ColumnJsonComment;
}

export interface Hint {
    tname: string;
    abbrev: string;
    revert: boolean;
    name?: string;
    nohint?: boolean;
}

export interface Record {
    [name: string]: any;
}

export type TypedRecord<T> = Record & T;

export interface IndexColumn {
    name: string;
    ascending?: boolean;
    expression?: string;
}

export interface IndexDefinition {
    name: string;
    isUnique: boolean;
    isNaturalKey?: boolean;
    columns: IndexColumn[];
}

/**
 * The behaviour of a foreign key on 'delete' operation
 */
export type ForeignKeyDeleteBehaviour =
    /**
     * ON DELETE CASCADE
     */
    | 'cascade'
    /**
     * ON DELETE RESTRICT
     */
    | 'restrict'
    /**
     * ON DELETE NO ACTION
     */
    | 'noAction';

export interface ForeignKeyDefinition {
    /**
     * The name of the FK
     */
    name: string;
    /**
     * The name of the target table
     */
    targetTable: string;
    /**
     * The name of the target columns (from the target table)
     */
    targetColumnNames: string[];
    /**
     * The name of the columns (from the current table)
     */
    columnNames: string[];

    /**
     * The behaviour of the foreign key on 'delete' operation
     */
    onDeleteBehaviour: ForeignKeyDeleteBehaviour;

    /**
     * The comment of the SQL object (JSON format)
     */
    comment?: ForeignKeyJsonComment;

    /**
     * Is the foreign key DEFERRABLE ?
     */
    isDeferrable: boolean;
}

/**
 * Parses a foreignKey definition to extract source/target columns
 * The key definition is a result from pg_catalog.pg_get_constraintdef
 */
export function parseForeignKeyDefinition(fkDef: string): {
    sourceColumns: string[];
    targetColumns: string[];
    onDeleteBehaviour: ForeignKeyDeleteBehaviour;
    isDeferrable: boolean;
} {
    // fkDef looks like
    // FOREIGN KEY (_tenant_id, some_reference) REFERENCES [schemaName.]tableName(_tenant_id, _id) ON DELETE RESTRICT ON UPDATE CASCADE
    // Note: table name and column names may be double-quoted
    const regex =
        /FOREIGN KEY \(((?:"?\w+"?, )*"?\w+"?)\) REFERENCES (?:\w+\.)?"?\w+"?\(((?:"?\w+"?, )*"?\w+"?)\)( ON DELETE CASCADE| ON DELETE RESTRICT)?( ON UPDATE CASCADE)?/;

    const result = fkDef.match(regex);

    if (!result) {
        throw new Error(`Could not parse FK definition: '${fkDef}'`);
    }

    const split = (txt: string): string[] => {
        // Note : some special columns names (user, ..) are double quoted
        return txt.split(', ').map(colName => colName.replace(/^"(.*)"$/, '$1'));
    };

    let onDeleteBehaviour: ForeignKeyDeleteBehaviour;
    if (result[3] != null) {
        // a 'ON DELETE ...' part was parsed
        switch (result[3]) {
            case ' ON DELETE CASCADE':
                onDeleteBehaviour = 'cascade';
                break;
            case ' ON DELETE RESTRICT':
                onDeleteBehaviour = 'restrict';
                break;
            default:
                throw new Error(`Error while parsing the delete behaviour part of '${fkDef}'`);
        }
    } else {
        // ON DELETE NO ACTION is stripped from the FK definition so we get here with 'ON DELETE NO ACTION'
        onDeleteBehaviour = 'noAction';
    }

    return {
        sourceColumns: split(result[1]),
        targetColumns: split(result[2]),
        onDeleteBehaviour,
        isDeferrable: / DEFERRABLE$/.test(fkDef),
    };
}

export interface ForeignKeyOptions {
    /**
     * Will be used in the flag that the query that checks if the foreign key exists should be skipped.
     */
    skipExists?: boolean;
    skipBaseTable?: boolean;
    /**
     * Should the foreign key be created in 'UPDATE CASCADE' mode ?
     */
    withUpdateCascade?: boolean;
    /**
     * Should the missing foreign keys be created ?
     */
    dontCreateMissingFks?: boolean;
}

interface PrimaryKeyDefinition {
    columns: string[];
}

export interface NotifyOptions {
    event: NotifyEventOptions;
    sqlPayloadExpression?: string;
}

export interface NotifyEventOptions {
    created?: boolean;
    updated?: boolean;
    deleted?: boolean;
}

export interface TableDefinition {
    schemaName: string;
    tableName: string;
    columns?: ColumnDefinition[];
    indexes?: IndexDefinition[];
    primaryKey?: PrimaryKeyDefinition;
    foreignKeys?: ForeignKeyDefinition[];
    triggers?: TriggerDefinition[];
    notify?: NotifyOptions;
    isOpenAccess?: boolean;
    sequence?: number;
    lastUpdate?: Date;
    isSharedByAllTenants?: boolean;
    baseDefinition?: TableDefinition;
    /**
     * The comment of the SQL object (JSON format)
     */
    comment?: TableJsonComment;
}

export type TriggerEventType =
    | 'INSERT'
    | 'UPDATE'
    | 'DELETE'
    | 'TRUNCATE'
    | 'INSERT OR UPDATE'
    | 'DELETE OR INSERT'
    | 'DELETE OR INSERT OR UPDATE';
export type TriggerTimingType = 'BEFORE' | 'AFTER' | 'INSTEAD OF';

export interface TriggerDefinition {
    /**
     * name of the trigger
     */
    name: string;
    /**
     * When will the trigger get fired ? (before/after/...)
     */
    when: TriggerTimingType;
    /**
     * The event type (insert/update/...)
     */
    event: TriggerEventType;
    /**
     * The options of the trigger (if any)
     */
    options?: string;
    /**
     * The name of the function that the trigger will execute. Only the name of the function without any parameters
     */
    functionName: string;

    /**
     * The parameters for the function
     */
    functionParameters: string;
}

export interface TableFilter {
    key: string;
    operator: string;
    value: any;
}

export interface PoolConfig {
    connectString?: string;
    hostname: string;
    port?: number;
    database: string;
    trace?: (...args: any[]) => void;
    user: string;
    password: string;
    requestTimeout?: number;
    collation?: string;
    privilege?: number;
    /** Should all the $xx parameters be replaced by their value ? */
    mapArgsInLogs?: boolean;
    // system database user
    sysUser?: string;
    sysPassword?: string;
    sysDatabase?: string;
    connectionMaxRetries?: number;
    connectionRetryMillis?: number;
    // new ones from pg-pool
    ssl?: boolean | TlsConnectionOptions;
    idleTimeoutMillis?: number;
    max?: number;
    maxUses?: number;
    connectionTimeoutMillis?: number;
    /** Number of milliseconds before a statement in query will time out, default is no timeout. We will set the query timeout as 120% of this.
     * https://node-postgres.com/apis/client
     * https://stackoverflow.com/questions/59155572/how-to-set-query-timeout-in-relation-to-statement-timeout
     */
    statementTimeoutMillis?: number;
    /** Size of the funnel that throttles connection allocation */
    connectionAllocationFunnelSize?: number;
}

export interface DatabaseParametersType {
    template: string;
    encoding: string;
    lcCollate: string;
    lcCtype: string;
    tableSpaceName: string;
    connectionLimit: number;
}

// This interface is a subset of the real LoggerInterface.
export interface PoolLoggerInterface {
    error(message: string): void;
    warn(message: string): void;
    info(message: string): void;
    verbose(formatMessage: () => string): void;
    log(logLevel: LogLevel, formatMessage: () => string): ProfilerCallback | undefined;
}
