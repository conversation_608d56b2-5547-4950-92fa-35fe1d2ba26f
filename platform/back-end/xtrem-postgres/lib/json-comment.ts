import { Dict } from '@sage/xtrem-shared';
import { ColumnTypeName, ForeignKeyDeleteBehaviour } from './types';

export interface SqlObjectJsonComment {}

/**
 * Interface of a JSON comment for a SQL table
 */
export interface TableJsonComment extends SqlObjectJsonComment {
    /**
     * The base table
     */
    baseTable?: string;
    /**
     * The rootTable is the lowest abstract table of the subNode tree
     *  cat->mammal->animal
     * mammal will be the base table of cat
     * animal will be the root table of cat
     * for subNodes this is useful as this is the table that has the SERIAL _id column
     */
    rootTable?: string;
    /**
     * Does this table stand for a 'isSharedByAllTenants' factory ?
     */
    isSharedByAllTenants: boolean;
    /**
     * Natural key attributes
     */
    naturalKey?: string[];
    /**
     * Is this a setup node
     */
    isSetupNode?: boolean;

    /**
     * Does this table can be bound to an uploaded file (via attachment_association table) ?
     */
    hasAttachments?: boolean;

    /**
     * Does this table can be bound to a note (via sys_note_association table) ?
     */
    hasNotes?: boolean;

    /**
     * Can this table be bound to tags?
     */
    hasTags?: boolean;
}

export type ColumnTypeJsonComment = ColumnTypeName | 'reference';

/**
 * Interface of a JSON comment for a SQL column
 */
export interface ColumnJsonComment extends SqlObjectJsonComment {
    /**
     * On reference columns: the name of the target table
     */
    targetTableName?: string;

    /**
     * On reference columns: is it a self-reference column ?
     */

    isSelfReference?: boolean;

    /**
     * The basic type of the column's property
     */
    type: ColumnTypeJsonComment;

    /**
     * Is it a system column ?
     */
    isSystem: boolean;

    /**
     * When type is 'json': is the column stands for a localized text (true) or a real JSON object (false) ?
     */
    isLocalized?: boolean;

    /**
     * When type is 'string': is the string encrypted ?
     */
    isEncrypted?: boolean;

    /**
     * When type is 'enum': the name of the enum
     */
    enumTypeName?: string;

    /**
     * When type is 'integer': is it an auto-increment column ?
     */
    isAutoIncrement?: boolean;

    /**
     * When type is 'string': max-length of the string
     */
    maxLength?: number;

    /**
     * When type is 'decimal': the precision of the column
     */
    precision?: number;

    /**
     * When type is 'decimal': the scale of the column
     */
    scale?: number;
}

/**
 * Interface of a JSON comment for a SQL foreign key
 */
export interface ForeignKeyJsonComment extends SqlObjectJsonComment {
    targetTableName: string;
    /**
     * A dictionary of the columns (sourceColumn:targetColumn) used by the foreign key :
     */
    columns: Dict<string>;

    /**
     * The behaviour of the foreign key on 'delete' operation
     */
    onDeleteBehaviour: ForeignKeyDeleteBehaviour;

    /**
     * Is the foreignKey deferrable ?
     */
    isDeferrable: boolean;
}
