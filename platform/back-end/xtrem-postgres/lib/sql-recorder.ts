import { Dict } from '@sage/xtrem-shared';

export type SqlRecorderActionType = 'reload_setup_data' | 'system_upgrade' | 'metadata_upgrade';

/**
 * This interface can be used to register a recorder to a PostgresPool.
 * This recorder will be invoked on every executed command.
 * The execution of the recorder.record function must be as fast as possible (or it will degrade performances)
 */
export interface SqlRecorder {
    /**
     * Executes a body where the recorder will be paused
     * @param body
     */
    withoutRecording<T>(body: () => Promise<T>): Promise<T>;

    /**
     * Records a SQL command (and its arguments)
     */
    recordSqlCommand(command: string, args?: any[]): void;

    /**
     * Records an action
     */
    recordAction(action: SqlRecorderActionType, args?: Dict<string>): void;
}
