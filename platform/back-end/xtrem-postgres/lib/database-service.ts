import { asyncArray } from '@sage/xtrem-async-helper';
import { Dict, validator } from '@sage/xtrem-shared';
import { ConnectionPool } from './pool';
import { Connection, PoolConfig } from './types';

type PoolConfigKey = keyof PoolConfig;

export class DatabaseService {
    private _serverPool: ConnectionPool | undefined;

    private _sysPool: ConnectionPool | undefined;

    private _userPool: ConnectionPool | undefined;

    /**
     * Dictionary of services indexed by their configuration, may contain the readonly and writable services
     */
    private static _services: Dict<DatabaseService> = {};

    private constructor(readonly config: PoolConfig) {}

    static getInstance(config: PoolConfig): DatabaseService {
        const key = JSON.stringify(config);
        if (!this._services[key]) this._services[key] = new DatabaseService(config);
        return this._services[key];
    }

    static getSysPool(config: PoolConfig): ConnectionPool {
        return this.getInstance(config).sysPool;
    }

    static getPool(config: PoolConfig): ConnectionPool {
        return this.getInstance(config).userPool;
    }

    async checkThatDatabaseExists(): Promise<void> {
        if (!(await this.databaseExists(this.config.database))) throw new Error('Database not created yet');
    }

    /** Server connections are very privileged. They can create databases and users */
    // ensure connection to server
    /** @internal */
    get serverPool(): ConnectionPool {
        if (this._serverPool == null) {
            this.validateAndFixConfig(['sysUser', 'sysPassword', 'sysDatabase']);
            this._serverPool = new ConnectionPool('server', {
                ...this.config,
                user: this.config.sysUser!,
                password: this.config.sysPassword!,
                database: this.config.sysDatabase!,
            });
        }
        return this._serverPool;
    }

    /** Sys connections are privileged. Then can perform DDL operations in a database created by server connections */
    // ensure connection to server
    get sysPool(): ConnectionPool {
        if (!this._sysPool) {
            this.validateAndFixConfig(['sysUser', 'sysPassword']);
            this._sysPool = new ConnectionPool('sys', {
                ...this.config,
                user: this.config.sysUser!,
                password: this.config.sysPassword!,
            });
        }
        return this._sysPool;
    }

    /** User connections are non-privileged. They can only perform DML operations in tables created by sys connections */
    get userPool(): ConnectionPool {
        if (this._userPool == null) {
            this.validateAndFixConfig([]);
            this._userPool = new ConnectionPool('user', this.config);
        }
        return this._userPool;
    }

    private validateAndFixConfig(sysKeys: PoolConfigKey[]): void {
        const allSysKeys: PoolConfigKey[] = ['sysUser', 'sysPassword', 'sysDatabase'];
        const validatedKeys: PoolConfigKey[] = ['database', 'user'];
        const passwordKeys: PoolConfigKey[] = ['password', 'sysPassword'];
        sysKeys.forEach(k => {
            if (allSysKeys.includes(k) && !this.config[k]) {
                throw new Error(`invalid config: ${k} config missing.`);
            }
            if (!passwordKeys.includes(k))
                validator.isAlphaNumericName((this.config as any)[k], { throw: { name: k } });
        });
        validatedKeys.forEach(k => {
            validator.isAlphaNumericName((this.config as any)[k], { throw: { name: k } });
        });
        passwordKeys.forEach(k => {
            if (!(this.config as any)[k]) return;
            (this.config as any)[k] = (this.config as any)[k].replace(/'/g, "''");
        });
    }

    /**
     * Create database
     */
    async createDatabaseIfNotExists(): Promise<void> {
        const { user, database, password, sysUser, sysPassword, sysDatabase } = this.config;

        // DO NOT touch SYS database
        if (database.toLowerCase() === sysDatabase?.toLowerCase()) return;

        // Don't do anything if it exists
        if (await this.databaseExists(database)) return;

        ConnectionPool.logger.info(`Creating database ${database}`);

        await this.serverPool.withConnection(async cnx => {
            await this.createUsers(cnx, user, password, sysUser, sysPassword);
            // Aurora Notes:
            // - TABLESPACE = 'pg_default' raises an error, so use default behavior
            // - LC_COLLATE = 'und-x-icu' and LC_CTYPE = 'und-x-icu' raises the error: invalid locale name: "und-x-icu"
            // If we cannot fix the issue with the collation at database creation time, we will need to specify
            // the collation at the column level during table creation. (Tested. It works on Aurora!)
            await this.serverPool.execute(
                cnx,
                `CREATE DATABASE ${database}
                        WITH OWNER = ${user}
                        TEMPLATE = 'template0'
                        ENCODING = 'UTF8'
                        CONNECTION LIMIT = -1`,
            );
        });
    }

    databaseExists(name: string): Promise<boolean> {
        return this.serverPool.withConnection(async cnx => {
            return !!(
                await this.serverPool.execute<{ exists: boolean }[]>(
                    cnx,
                    'select exists(SELECT datname FROM pg_catalog.pg_database WHERE datname = $1);',
                    [name],
                )
            )[0].exists;
        });
    }

    /**
     * Create (if needed) the roles for 'user' and 'sysUser'
     */
    async createUsers(
        cnx: Connection,
        user: string,
        password: string,
        sysUser?: string,
        sysPassword?: string,
    ): Promise<void> {
        if (sysUser && sysPassword) {
            await this.createSysUser(cnx, sysUser, sysPassword);
        }
        if (user === sysUser) return;

        if (await this.userExists(user)) return;

        ConnectionPool.logger.info(`Create (non-sys) user ${user}`);
        await this.serverPool.execute(cnx, `CREATE USER ${user} WITH PASSWORD '${password}'`);
        await this.serverPool.execute(cnx, `GRANT ${user} TO ${sysUser}`);
    }

    /**
     * Create (if not existing) the sysUser
     */
    private async createSysUser(cnx: Connection, sysUser: string, sysPassword: string): Promise<void> {
        if (await this.userExists(sysUser)) return;
        ConnectionPool.logger.info(`Create sys user ${sysUser}`);
        await this.serverPool.execute(cnx, `CREATE USER ${sysUser} NOSUPERUSER WITH PASSWORD '${sysPassword}'`);
    }

    private userExists(user: string): Promise<boolean> {
        return this.serverPool.withConnection(async cnx => {
            return !!(
                await this.serverPool.execute<{ exists: boolean }[]>(
                    cnx,
                    'SELECT exists(SELECT * from pg_roles WHERE rolname=$1)',
                    [user],
                )
            )[0].exists;
        });
    }

    /** @internal */
    private async dropUser(user: string, sysUser: string): Promise<void> {
        // DO NOT touch SYS user
        if (user === sysUser) return;

        await this.serverPool.withConnection(async cnx => {
            if (await this.userExists(user)) {
                await this.serverPool.execute(cnx, `GRANT ${user} TO ${sysUser}`);
                await this.serverPool.execute(cnx, `REASSIGN OWNED BY ${user} TO ${sysUser};`);
                await this.serverPool.execute(cnx, `DROP OWNED BY ${user}`);
                // dropping role can fail if you have multiple databases that use that role
                try {
                    await this.serverPool.execute(cnx, `DROP ROLE ${user}`);
                } catch (e) {
                    if (
                        /role "\w+" cannot be dropped because some objects depend on it: \d+ objects in database \w+/.test(
                            e.message,
                        )
                    ) {
                        ConnectionPool.logger.warn(e.message);
                    } else {
                        throw e;
                    }
                }
            }
        });
    }

    async dropDatabaseIfExists(): Promise<void> {
        const { user, database, sysUser, sysDatabase } = this.config;

        // DO NOT touch SYS database
        if (database.toLowerCase() === sysDatabase?.toLowerCase()) return;

        // Don't do anything if it does not exist
        if (!(await this.databaseExists(database))) return;

        ConnectionPool.logger.warn(`Dropping database ${database}`);
        await this.serverPool.withConnection(async cnx => {
            await this.serverPool.execute(
                cnx,
                `SELECT pg_terminate_backend(pid)
                FROM
                    pg_stat_activity
                WHERE
                    pid <> pg_backend_pid() AND
                    pg_stat_activity.datname = $1;`,
                [database],
            );
            await this.serverPool.execute(cnx, `DROP DATABASE ${database};`);
        });
        this._serverPool = undefined;

        await this.dropUser(user, sysUser!);
    }

    /**
     * Rename a schema in database
     */
    async renameSchema(oldName: string, newName: string): Promise<void> {
        ConnectionPool.logger.info(`Rename schema ${oldName} to ${newName}`);
        const regex = new RegExp(`(${oldName})\\.(\\w+)`, 'g');

        // List of exceptions where the schema name should not be replaced
        // we don't want to translate things like "get_config('xtrem.transaction_user_id')" to "get_config('newName.transaction_user_id')"
        const exceptions = ['xtrem.transaction_user_id', 'xtrem.login_email', 'xtrem.user_email'];

        let fixesCount = 0;

        const schemaNameFixer = (match: string, part1: string, part2: string): string => {
            if (exceptions.includes(match)) return match;
            if (part1 !== oldName) {
                // Should never happen as the regEx only target the oldName
                return match;
            }
            fixesCount += 1;
            return `${newName}.${part2}`;
        };

        const pool = this.sysPool;
        await pool.withConnection(async cnx => {
            // rename the schema, it will rename all the functions from oldName.foo to newName.foo
            await pool.execute(cnx, `ALTER SCHEMA ${oldName} RENAME TO ${newName}`);
            // Get the list of all the functions defined in the schema that still contain the oldName
            const functionsToFix = (
                await pool.execute<{ name: string; definition: string }[]>(
                    cnx,
                    `SELECT p.proname name, pg_get_functiondef(p.oid) AS definition
                FROM pg_proc p
                JOIN pg_namespace n ON p.pronamespace = n.oid
                WHERE n.nspname = '${newName}'`,
                )
            ).filter(fn => fn.definition.indexOf(`${oldName}.`) !== -1);

            await asyncArray(functionsToFix).forEach(async fn => {
                await pool.execute(cnx, fn.definition.replace(regex, schemaNameFixer));
            });

            // Fix the default values
            // For instance, defaultValue of _sort_value look like (currval((pg_get_serial_sequence('xtrem.sys_service_option_to_service_option'::text, '_id'::text))::regclass) * 100)
            const defaultValuesToFix = await pool.execute<
                { table_name: string; column_name: string; column_default: string }[]
            >(
                cnx,
                `SELECT table_name, column_name, column_default
                        FROM information_schema.columns
                        WHERE table_schema='${newName}' and column_default like '%${oldName}.%'
                        ORDER BY ordinal_position;`,
            );

            await asyncArray(defaultValuesToFix).forEach(async defVal => {
                const newDefaultValue = defVal.column_default.replace(regex, schemaNameFixer);
                await pool.execute(
                    cnx,
                    `ALTER TABLE ${newName}.${defVal.table_name} ALTER COLUMN ${defVal.column_name} SET DEFAULT ${newDefaultValue}`,
                );
            });
        });

        ConnectionPool.logger.info(`Applied ${fixesCount} renamings`);
    }
}
