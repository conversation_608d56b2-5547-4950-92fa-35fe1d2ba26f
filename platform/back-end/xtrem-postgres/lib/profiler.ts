import { LogLevel, ProfilerCallback } from '@sage/xtrem-shared';
import { PoolLoggerInterface } from './types';

/** @internal */
export function createProfiler(
    messageProvider: () => string,
    mapArgsInLogs?: boolean,
    args?: any[],
    logLevel?: LogLevel,
    logger?: PoolLoggerInterface,
): ProfilerCallback | undefined {
    const objAsString = (obj: any): string => {
        if (obj == null) return 'NULL';
        if (Buffer.isBuffer(obj)) {
            const data = obj.toJSON().data;
            const dataAsStr = data.length > 20 ? `${data.slice(0, 20)}...` : data;
            return `Buf[${obj.length} bytes: ${dataAsStr}]`;
        }
        if (Array.isArray(obj)) {
            return `[${obj.map(item => objAsString(item)).join(',')}]`;
        }
        if (typeof obj === 'object') return JSON.stringify(obj);
        if (typeof obj === 'string') return `'${obj.toString()}'`;
        return obj.toString();
    };

    if (!logger) return undefined;
    let msgProvider;
    if (mapArgsInLogs) {
        msgProvider = () => {
            let message = messageProvider();
            if (args && args.length > 0) {
                message = message.replace(/\$(\d+)/g, (_, i) => objAsString(args[i]));
            }
            return `${message}`;
        };
    } else {
        msgProvider = () => {
            return `${messageProvider()}, args=${objAsString(args)}`;
        };
    }
    return logger.log(logLevel || 'verbose', msgProvider);
}
