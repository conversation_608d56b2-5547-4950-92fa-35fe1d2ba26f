PATH: XTREEM/Development+documentation/Postgresql+configuration

## Install PostgreSQL on windows

1.  Go to [https://www.postgresql.org/download](https://www.postgresql.org/download).
2.  Download the PostgreSQL installer for Windows (Version 10).
3.  Install PostgreSQL.
    1.  Specify the installation folder, choose your own or keep the default folder suggested by the PostgreSQL installer and click the Next button.
    2.  Select components to install and click the Next button.
        1.  Select at least : PostgreSQL Server + Stack Builder + Command Line Tools.
        2.  If you want to install a graphical database management client then select also pgAdmin 4.
    3.  Select the database directory to store the data. Just leave it by default or choose your own and click the Next button.
    4.  Enter the password for the database superuser (postgres).
    5.  Enter the port for PostgreSQL. Make sure that no other applications are using this port. Leave it as default if you are unsure.
    6.  Choose the default locale used by the database and click the Next button.
        1.  choose 'C'.
4.  Verify the installation

Visit [https://www.postgresqltutorial.com/install-postgresql/](https://www.postgresqltutorial.com/install-postgresql/) for detailed steps.

Check your connection to the database by executing within your terminal:

1. `psql -U postgres -d postgres -p 5432`
2. if you are connected, you can quit by executing: `\q`

NB: if psql is not found then you should add postgres to your environment variables.

## Install PosgreSQL with docker

### Basic installation and configuration

N.B: if you have already installed PostgreSQL using the windows installer you should uninstall it before.

1. Open your terminal and execute:
   `docker --version` (if a number is returned then go to step 3, otherwise go to step 2 and install docker).
2. install docker on windows [https://docs.docker.com/docker-for-windows/install/](https://docs.docker.com/docker-for-windows/install/)
3. Download PostgreSQL alpine docker image which is the one used in CI by executing:
   `docker pull postgres:15.5-alpine`
4. Launch docker instance:
   `docker run --shm-size=1g --name xtrem_postgres -e POSTGRES_PASSWORD=secret -c max_locks_per_transaction=256 -d -p 5432:5432 postgres:15.5-alpine`

Check your connection to the database by executing in your terminal:

1. `docker exec -it xtrem_postgres bash`
2. `psql -U postgres -d postgres -p 5432`
3. if you are connected, you can quit by executing: `\q`

**Note**:
On windows, you might have the following error when running the docker exec command:

> the input device is not a TTY. If you are using mintty, try prefixing the command with 'winpty'.

If this happens, run:

`winpty docker exec -it xtrem_postgres bash`

### Running in ssl mode with docker

To allow ssl connection on PostgresSQL you need additional steps and it might be simpler to create a docker compose file for this.

First, we need to generate SSL certificates for the PostgreSQL server. Open your wsl command prompt:

```sh
mkdir -p ~/docker/postgres
cd ~/docker/postgres
mkdir data
mkdir ssl
cd ssl
export PASS="pass:4testonly"
openssl req -new -text -passout $PASS -subj /CN=localhost -out server.req -keyout privkey.pem
openssl rsa -in privkey.pem -passin $PASS -out server.key
openssl req -x509 -in server.req -text -key server.key -out server.crt
unset PASS
chmod 600 server.key
sudo chown 70 server.key
cd ..
```

Then create the following `docker-compose.yml` file:

```yaml
postgres:
    container_name: xtrem_postgres
    restart: always
    image: postgres:${PG_VERSION}-alpine
    shm-size: 1g
    command: >
        -c shm_size=1g
        -c max_locks_per_transaction=256
        -c ssl=on
        -c ssl_cert_file=/var/lib/postgresql/server.crt
        -c ssl_key_file=/var/lib/postgresql/server.key

    environment:
        POSTGRES_PASSWORD: postgres
        POSTGRES_HOST_AUTH_METHOD: trust
    volumes:
        - ./data:/var/lib/postgresql/data
        - ./ssl/server.crt:/var/lib/postgresql/server.crt:ro
        - ./ssl/server.key:/var/lib/postgresql/server.key:ro
    ports:
        - '5432:5432'
```

Then, run the docker-compose command from the `~/docker/postgres` folder:

```sh
PG_VERSION="15.5"
export PG_VERSION
docker-compose up -d
```

It you want to run it without docker-compose, you do it with the command:

```
$ docker run --shm-size=1g --name xtrem_postgres -e POSTGRES_PASSWORD=secret -e POSTGRES_HOST_AUTH_METHOD=trust \
  -c max_locks_per_transaction=256 -c ssl=on -c ssl_cert_file=/var/lib/postgresql/server.crt -c ssl_key_file=/var/lib/postgresql/server.key \
  -d -p 5432:5432 postgres:15.5-alpine
```

Finally, to run xtrem with an SSL connection to the PostgresSQL server, you need to adapt your `xtrem-config.yml` by setting the `ssl` parameter to true (see below).

## Configuration file

### Latest changes

-   The sql key is not compulsory anymore in the xtrem-config.yml file.
-   `schemaName`, `folderName` and `driver` are now obsolete keys.

### Default sql values

Here are the default values that will be used if the key is absent from the file:

```yaml
storage:
    sql:
        hostname: 'localhost'
        port: 5432
        database: 'postgres'
        user: 'postgres'
        password: 'secret'
        sysDatabase: 'postgres'
        sysUser: 'postgres'
        sysPassword: 'secret'
        poolMaxIdleSeconds: 60
        max: 10
```

If `deploymentMode: development`, the default schema will be:

-   `xtrem-test` for the `pnpm run test` command,
-   `xtrem-start` for the `pnpm run manage` and `pnpm run start` commands,

If `deploymentMode: production` or ommitted, the default schema will be `xtrem` for all commands.

### Override sql values

If one of the keys has to be overridden, for instance, `password`, this key can be placed in the `xtrem-config.yml` file with the desired value, under the `sql` key:

```yaml
storage:
    sql:
        password: 'personalized_password'
        sysPassword: 'personalized_system_password'
```

The resulting configuration will be the default one except for the two overridden keys:

```yaml
storage:
    sql:
        hostname: 'localhost'
        port: 5432
        database: 'postgres'
        user: 'postgres'
        password: 'personalized_password'
        sysDatabase: 'postgres'
        sysUser: 'postgres'
        sysPassword: 'personalized_system_password'
        poolMaxIdleSeconds: 60
        max: 10
```

### Configuration for ssl connection to PostgreSQL

The connection to PostgresSQL in SSL mode can be configured in a different way depending on the configuration of the server. It can be as simple as setting the `ssl` parameter to true or may require some additional settings by providing the CA certificate of the issuer of the server certificate.

#### Simple case

```yaml
storage:
    sql:
        hostname: 'localhost'
        port: 5432
        database: 'postgres'
        user: 'postgres'
        password: 'personalized_password'
        sysDatabase: 'postgres'
        sysUser: 'postgres'
        sysPassword: 'personalized_system_password'
        poolMaxIdleSeconds: 60
        max: 10
        ssl: true
```

#### Advanced case

In some cases, especially with PostgreSQL version 13, you may need to add the root certificate in the client connection configuration.

```yaml
storage:
    sql:
        hostname: 'localhost'
        port: 5432
        database: 'postgres'
        user: 'postgres'
        password: 'personalized_password'
        sysDatabase: 'postgres'
        sysUser: 'postgres'
        sysPassword: 'personalized_system_password'
        poolMaxIdleSeconds: 60
        max: 10
        ssl:
            ca: 'path/to/root/ca/cert.pem'
```

**Note**

The `ssl` parameter accepts other standard node.js [TLS options](https://nodejs.org/docs/latest-v14.x/api/all.html#tls_tls_createsecurecontext_options).
