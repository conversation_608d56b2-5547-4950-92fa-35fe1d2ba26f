{"extends": "../../tsconfig-package.json", "include": ["index.ts", "lib/**/*", "test/**/*"], "compilerOptions": {"outDir": "build", "rootDir": ".", "baseUrl": "."}, "exclude": ["node_modules", "build"], "references": [{"path": "../../shared/xtrem-async-helper"}, {"path": "../../shared/xtrem-date-time"}, {"path": "../xtrem-log"}, {"path": "../../shared/xtrem-shared"}, {"path": "../eslint-plugin-xtrem"}, {"path": "../xtrem-config"}, {"path": "../xtrem-dts-bundle"}, {"path": "../xtrem-minify"}]}