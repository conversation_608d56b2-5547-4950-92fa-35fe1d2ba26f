{"name": "@sage/artillery-plugin-xtrem", "version": "58.0.2", "description": "XTREM plugin for Artillery", "main": "build/artillery-plugin-xtrem/index.js", "scripts": {"build": "tsc -b -v .", "build:binary": "echo 'Binary mode is not available for this package, falling back to normal build.' && pnpm build", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build junit-report*", "lint": "eslint -c .eslintrc.js --ext .ts lib test", "lint:filename": "eslint --no-eslintrc -c .eslintrc-filename.js \"**\"", "test": "mocha --recursive --exit \"test/*.ts\"", "test:ci": "cross-env JUNIT_REPORT_PATH=junit-report-artillery-plugin-xtrem.xml JUNIT_REPORT_NAME='artillery-plugin-xtrem' c8 --reporter=lcov --reporter=cobertura --reporter=json --reporter=text mocha  --recursive --exit \"test/*.ts\" --reporter mocha-jenkins-reporter"}, "dependencies": {"axios": "^1.11.0", "debug": "^4.3.7", "espree": "^10.1.0", "glob": "^11.0.0", "js-yaml": "^4.1.0", "lodash": "^4.17.21", "nanoid": "^3.3.8", "typescript": "~5.8.3"}, "keywords": [], "author": "", "license": "UNLICENSED", "devDependencies": {"@types/artillery": "^1.7.1", "@types/chai": "^4.3.6", "@types/debug": "^4.1.8", "@types/js-yaml": "^4.0.6", "@types/lodash": "^4.14.198", "@types/mocha": "^10.0.1", "@types/node": "^22.10.2", "@types/request": "^2.48.8", "c8": "^10.1.2", "chai": "^4.3.10", "cross-env": "^7.0.3", "eslint": "^8.49.0", "mocha": "^10.8.2", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0"}}