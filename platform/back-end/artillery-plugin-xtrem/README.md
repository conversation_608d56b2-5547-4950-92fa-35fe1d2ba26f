# Install artillery and xtrem plugin

```bash
pnpm i -g artillery artillery-plugin-xtrem
```

## Usage

-   Process handlebars in queries

```yaml
config:
    plugins:
        xtrem:
            processHandlebars: true
        ...
```

-   Add a login manager for a given environment

```yaml
config:
    environments:
        cluster-ci:
            xtrem:
                loginManager: 'UnsecureDevLogin'
            ...
```

-   Add graphql resources to a scenario

```yaml
scenarios:
    - xtrem:
          resources:
              - '{{ $processEnvironment.SHOWCASE_PATH }}/resources/**'
      flow: ...
```

## Options

-   resources: all the graphql queries used in this scenario. Can be glob.
-   processHandlebars: is true graphql queries can use variable inside **_{{ var1 }}_**
-   loginManager: login manager to logged server.
