module.exports = {
    // Shared functions
    withinDuration,
    notExceedsDuration,
    randomThink,
};

const { performance } = require('perf_hooks');

/*
Global variables used by withinDuration
*/
let startTime = 0;
let currentTime = 0;
let duration = 0;

/*
withinDuration checks that the execution time of the scenario doesn't exceed the duration
withinDuration cannot be used in a scenario: use notExceedsDuration instead
    example:
        whileTrue: 'notExceedsDuration'
*/
function withinDuration(context) {
    currentTime = performance.now();
    if (!startTime) {
        startTime = currentTime;
        duration = parseInt(context.vars.$processEnvironment.DURATION, 10);
    }
    return Math.floor((currentTime - startTime) / 1000) < duration;
}

/*
notExceedsDuration checks that the execution time of the scenario doesn't exceed the duration
notExceedsDuration can be used with the artillery whileTrue instruction:
    example:
        whileTrue: 'notExceedsDuration'
*/
function notExceedsDuration(context, next) {
    return next(withinDuration(context));
}

/*
randomThink makes it possible to take 0 to 1000ms to 'think' about the next operation.
randomThink can be used as a function in a scenario
    example:
        - function: 'randomThink'

 */
function randomThink(context, events, done) {
    setTimeout(
        () => {
            return done();
        },
        Math.floor(1000 * Math.random()),
    );
}
