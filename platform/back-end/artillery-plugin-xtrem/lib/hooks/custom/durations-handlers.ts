import * as artillery from 'artillery';
import { performance } from 'perf_hooks';

export function initStartTime(context: artillery.ScenarioContext) {
    if (!context.vars.startTime) {
        context.vars.duration = parseInt(context.vars.$processEnvironment.DURATION, 10);
        context.vars.continueLooping = true;
        context.vars.elapsedTime = 0;
        context.vars.startTime = performance.now();
    }
}

export function initializeDuration(
    context: artillery.ScenarioContext,
    _ee: artillery.EventEmitter,
    next: () => artillery.Next,
) {
    initStartTime(context);
    return next();
}

/**
 * Check loop execution duration.
 * continueLooping is:
 * - controlling the loop duration
 * - set to true before the loop
 * - updated before each request
 */
export function checkDuration(
    _requestParams: artillery.RequestParams,
    context: artillery.ScenarioContext<any, any>,
    _ee: artillery.EventEmitter,
    next: () => artillery.Next,
) {
    initStartTime(context);
    context.vars.elapsedTime = Math.floor((performance.now() - context.vars.startTime) / 1000);
    context.vars.continueLooping = context.vars.elapsedTime < context.vars.duration;
    return next();
}

/**
notExceedsDuration checks that the execution time of the scenario doesn't exceed the duration
notExceedsDuration can be used with the artillery whileTrue instruction:
 example: whileTrue: 'notExceedsDuration'
*/
export function notExceedsDuration(context: artillery.ScenarioContext<any, any>, next: (b: boolean) => artillery.Next) {
    return next(context.vars.continueLooping);
}
