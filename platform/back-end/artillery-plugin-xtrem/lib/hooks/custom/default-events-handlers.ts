import * as artillery from 'artillery';
import { debug } from '../../core/plugin';

export function beforeRequestHandler(
    requestParams: artillery.RequestParams,
    context: artillery.ScenarioContext,
    ee: artillery.EventEmitter,
    next: () => artillery.Next,
) {
    return next();
}

export function afterResponse<PERSON>andler(
    requestParams: artillery.RequestParams,
    response: artillery.RequestParams,
    context: artillery.ScenarioContext,
    ee: artillery.EventEmitter,
    next: () => artillery.Next,
) {
    return next(); // MUST be called for the scenario to continue
}

export function afterScenarioHandler(
    context: artillery.ScenarioContext,
    ee: artillery.EventEmitter,
    next: () => artillery.Next,
) {
    return next(); // MUST be called for the scenario to continue
}

export function beforeScenarioHandler(
    context: artillery.ScenarioContext,
    ee: artillery.EventEmitter,
    next: () => artillery.Next,
) {
    return next(); // MUST be called for the scenario to continue
}

export function throwErrorOnErrorHandler(
    requestParams: artillery.RequestParams,
    response: { statusCode: any; request: { uri: { host: any; pathname: any } }; headers: any },
) {
    debug(`**************** fail with status code: ${JSON.stringify(response.statusCode)}`);
    debug(`**************** host is: ${JSON.stringify(response.request.uri.host)}`);
    debug(`**************** path is: ${JSON.stringify(response.request.uri.pathname)}`);
    debug(`**************** response headers is: ${JSON.stringify(response.headers)}`);
    throw new Error('Stopping test');
}

export function logResponseOnErrorHandler(
    requestParams: artillery.RequestParams,
    response: { statusCode: any; request: { uri: { host: any; pathname: any } }; headers: any },
    context: artillery.ScenarioContext,
    ee: artillery.EventEmitter,
    next: () => artillery.Next,
) {
    if (response.statusCode !== 200 || response.statusCode !== 201 || response.statusCode !== 204) {
        debug(`**************** fail with status code: ${JSON.stringify(response.statusCode)}`);
        debug(`**************** host is: ${JSON.stringify(response.request.uri.host)}`);
        debug(`**************** path is: ${JSON.stringify(response.request.uri.pathname)}`);
        debug(`**************** response headers is: ${JSON.stringify(response.headers)}`);
    }
    return next(); // MUST be called for the scenario to continue
}
