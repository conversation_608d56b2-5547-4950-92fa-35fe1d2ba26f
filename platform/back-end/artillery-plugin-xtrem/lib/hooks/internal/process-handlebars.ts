import * as artillery from 'artillery';
import { Plugin } from '../../core/plugin';

export function processHandlebars(
    requestParams: any,
    context: artillery.ScenarioContext,
    ee: artillery.EventEmitter,
    next: () => artillery.Next,
) {
    if (requestParams?.json?.query) {
        requestParams.json.query = Plugin.template(requestParams.json.query, context);
    }
    return next();
}
