import * as artillery from 'artillery';
import * as _ from 'lodash';
import * as helpers from './index';

const espree = require('espree');

export function xtremPluginFunctionsBeforeRequest(
    requestParams: any,
    userContext: artillery.ScenarioContext,
    ee: artillery.EventEmitter,
    next: () => artillery.Next,
) {
    const iterate = (obj: any) => {
        Object.keys(obj).forEach(key => {
            if (typeof obj[key] === 'object' && obj[key] !== null) {
                iterate(obj[key]);
            }
            obj[key] = resolveFunc(obj[key]);
        });
    };
    iterate(requestParams);
    return next();
}

export function xtremPluginFunctionsBeforeScenario(
    userContext: artillery.ScenarioContext,
    ee: artillery.EventEmitter,
    next: () => artillery.Next,
) {
    const variables = userContext.vars;
    Object.keys(variables || {}).forEach(key => {
        const val = (variables as any)[key];
        (variables as any)[key] = resolveFunc(val);
    });
    return next();
}

export function resolveFunc(value: string) {
    let val = value;
    if (typeof val === 'string') {
        // parse env variables
        const envVariableRegex = /\$processEnvironment\.([A-Za-z0-9-_]+)\s*/g;
        if (envVariableRegex.test(val)) {
            val.match(envVariableRegex)?.forEach(envVariable => {
                const envVariableValue =
                    process.env[
                        Object.keys(process.env).find(v => envVariable.replace('$processEnvironment.', '') === v) || ''
                    ] || envVariable;
                if (envVariable && envVariableValue) val = val.replace(envVariable, envVariableValue);
            });
        }
        const funcCallRegex = /{{\s*\$xtrem\.([a-z][A-Za-z0-9]+)\(([^}{]*)\)\s*}}/;
        const match = val.match(funcCallRegex);
        if (match) {
            const functionName = match[1];
            // Avoid Espree parse error when string begin with a number
            const parameters = match[2]
                ?.split(',')
                .map((param: string) => `"${param}"`)
                .join(',');
            const syntax = espree.parse(`${functionName}(${parameters})`);
            // TODO: Use a proper schema for what we expect here
            if (syntax.body && syntax.body.length === 1 && syntax.body[0].type === 'ExpressionStatement') {
                const funcName = syntax.body[0].expression.callee.name;
                const args = _.map(syntax.body[0].expression.arguments, arg => {
                    if (arg.type === 'BinaryExpression') {
                        return arg.left.name + arg.operator + arg.right.name;
                    }
                    return arg.value || arg.name;
                });
                if (funcName in helpers) {
                    const computedValue = (helpers as any)[funcName].apply(null, args);
                    if (typeof computedValue === 'string') {
                        val = val.replace(funcCallRegex, computedValue);
                    } else {
                        val = computedValue;
                    }
                }
            }
        }
    }
    return val;
}
