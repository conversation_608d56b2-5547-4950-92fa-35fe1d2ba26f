import * as fsp from 'path';

process.env.TENANTS = '1,2';
process.env.TENANTSBIS = '3,4';

export const fullScript = {
    _environment: 'cluster-ci',
    config: {
        plugins: {
            xtrem: {},
        },
        variables: {
            generateId: ['{{ $xtrem.generateId(PREFIX_) }}'],
            arrayVal: '{{ $xtrem.makeStringArray($processEnvironment.TENANTS,$processEnvironment.TENANTSBIS) }}',
            emptyParams: '{{ $xtrem.funcWithoutParams() }}',
        },
        target: 'http://127.0.0.1:3003',
        phases: [{ duration: 10, arrivalRate: 5 }],
        payload: {
            fields: ['species', 'name'],
        },
        environments: {
            local: {
                target: 'http:local',
            },
            'cluster-ci': {
                target: 'http://cluster-ci',
                xtrem: {
                    loginManager: 'UnsecureDevLogin',
                },
            },
        },
        defaults: {
            headers: {
                'content-type': 'application/json',
                'x-auth': '987401838271002188298567',
                'x-name': '{{ name }}',
            },
            think: 2,
        },
        ensure: {
            p95: 2000,
        },
        mode: 'uniform',
    },
    scenarios: [
        {
            xtrem: {
                resources: [fsp.join(process.env.PWD!, 'test', 'fixtures', 'simple-query.graphql')],
            },
            weight: 10,
            flow: [
                { get: { url: '/pets' } },
                { think: 1 },
                { post: { url: '/pets', json: { name: '{{ name }}', species: '{{ species }}' } } },
                {
                    post: {
                        url: 'http://127.0.0.1:3003/pets',
                        body: '{"name: "{{ name }}", "species: "{{ species }}"}',
                    },
                },
                { get: { url: '/pets', headers: { 'x-auth': 'overrides-the-value-in-defaults' } } },
                {
                    post: {
                        url: '/pets',
                        json: { name: '{{ name }}', species: '{{ species }}' },
                        headers: { 'user-agent': 'artillery' },
                    },
                },
                {
                    get: {
                        url: '/api',
                        json: { query: 'simple-query.graphql' },
                    },
                },
            ],
        },
        {
            weight: 1,
            name: 'weighted scenario',
            flow: [{ get: { url: '/will404' } }],
        },
        {
            name: 'with graphql scenario',
            flow: [
                {
                    get: {
                        url: '/api',
                        json: { query: 'simple-query.graphql' },
                    },
                },
            ],
        },
        {
            flow: fsp.join(process.env.PWD!, 'test', 'fixtures', 'simple-flow.yaml'),
        },
    ],
};
