#!/usr/bin/env node
Error.stackTraceLimit = 100;

const path = require('path');
const fs = require('fs');
const os = require('os');

const root = os.platform() === 'win32' ? process.cwd().split(path.sep)[0] + '\\' : '/';

let currentDir = process.cwd();

while (currentDir !== root) {
    const localExecutable = path.resolve(currentDir, 'node_modules', '@sage', 'xtrem-dts-bundle', 'build', 'lib', 'dts-bundle.js');
    if (fs.existsSync(localExecutable)) {
        const { dtsBundle } = require(localExecutable);
        dtsBundle();
        return;
    }  else {
        currentDir = path.dirname(currentDir);
    }
}
