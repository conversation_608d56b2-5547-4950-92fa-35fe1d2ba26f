import * as fs from 'fs';
import { glob } from 'glob';
import * as path from 'path';

/**
 * dtsBundle is intended to be used from the cli: it works againt the currect directoy
 */
export const dtsBundle = () => {
    const dir = process.cwd();
    const packagePath = path.resolve(dir, 'package.json');
    const outPath = path.resolve(dir, 'build', 'package-definition.d.ts');
    dtsBundleApi(packagePath, outPath);
};

/**
 * List all d.ts files stored in a directory:
 */
function getDtsFiles(dir: string, excludes: string[]): string[] {
    // For Windows we need to use cwd, and reconstruct the full path.
    return glob
        .sync('**/*.d.ts', { cwd: dir, realpath: true, absolute: true })
        .filter(file => !excludes.some(exclude => file.startsWith(exclude)));
}

/**
 * transformDtsFiles loads a DTS file and returns an array of transformed lines that abide with
 * package-definition rules
 */
function transformDtsFiles(packageName: string, buildDir: string, dtsPath: string): string[] {
    const dtsLines = [];
    const dstDir = path.dirname(dtsPath);
    const contents = fs.readFileSync(dtsPath, 'utf-8');
    const dtsRelativePath = dtsPath.slice(buildDir.length + 1);

    // Keep a trace of the original d.ts file
    dtsLines.push(`// From file: ${packageName}/build/${dtsRelativePath}`);
    const lines = contents.split('\n');
    if (lines.length) {
        const isModule = lines[0].startsWith('declare module');
        if (isModule) {
            dtsLines.push(lines[0]);
            lines.shift();
        } else {
            // Let's declare this file as a module:
            dtsLines.push(
                `declare module '${packageName}/${dtsRelativePath.substring(0, dtsRelativePath.length - 5)}' {`,
            );
        }
        let fInternal = false;
        let countCurlyBrackets = 0;

        const lineTab = isModule ? '' : '\t';
        lines
            .map(line => {
                const mappedLine = line
                    // no declare in declare module to prevent from error: 'declare' modifier not allowed for code already in an ambient context
                    .replace(/declare ((?!\bmodule\b))/, '$1')
                    .replace(/from '(\..*)'/, (match, relativePath) => {
                        // Don't expose internal paths:
                        const fullPath = path.resolve(dstDir, relativePath);
                        let packageRelativePath = fullPath.substring(buildDir.length);
                        if (/\.c?js/.test(fullPath)) {
                            packageRelativePath = packageRelativePath.replace(/\.c?js$/, '');
                        } else if (fs.existsSync(path.resolve(fullPath, 'index.d.ts'))) {
                            packageRelativePath = `${packageRelativePath}/index`;
                        }
                        return `from '${packageName}${packageRelativePath}'`;
                    })
                    .replace(/declare module '(\..*)'/, (match, relativePath) => {
                        // Don't expose internal paths:
                        const fullPath = path.resolve(dstDir, relativePath);
                        return `declare module '${packageName}${fullPath.substring(buildDir.length)}'`;
                    })
                    .replace(/import\("@sage\/([^/]*)(\/[a-z][a-zA-Z0-9]*)*"\)\./, (match, modulePackageName) => {
                        return `import("@sage/${modulePackageName}").`;
                    });
                return `${lineTab}${mappedLine}`;
            })
            .forEach(line => {
                if (!fInternal && /(\/\/|\*)\s*@internal/.test(line)) {
                    // Next lines may contain an internal type or function
                    fInternal = true;
                    countCurlyBrackets = 0;
                    dtsLines.push(line);
                } else if (fInternal) {
                    if (/^[\t\s]+[/*]+/.test(line)) {
                        // This is a comment:
                        dtsLines.push(line);
                    } else {
                        // Do we have to filter the next line ?
                        for (let i = 0; i < line.length; i += 1) {
                            switch (line[i]) {
                                case '{':
                                    countCurlyBrackets += 1;
                                    break;
                                case '}':
                                    countCurlyBrackets -= 1;
                                    break;
                                default:
                                    break;
                            }
                        }
                        if (!countCurlyBrackets) fInternal = false;
                    }
                } else if (!/^[\t\s]*#private/.test(line)) {
                    /* #private seems to be a not documented #pragma instruction that troubelshoot typescript
                     when it appears in a package definition file
                     */
                    dtsLines.push(line);
                }
            });
        if (!isModule) dtsLines.push('}');
    }

    // For Windows we need replace \ with /
    return dtsLines.map(line => line.replaceAll(path.sep, '/'));
}

/**
 * dtsBundleApi is intended by the compiler implemented in xtrem-cli
 */
export const dtsBundleApi = (packagePath: string, outPath: string) => {
    if (fs.existsSync(outPath)) fs.unlinkSync(outPath);

    const dir = path.dirname(packagePath);
    if (!fs.existsSync(packagePath)) throw new Error(`Couldn't find package file ${packagePath}`);

    const packageContents = JSON.parse(fs.readFileSync(packagePath, 'utf-8'));
    const packageName = packageContents.name;

    const buildDir = path.resolve(dir, 'build');
    let dtsLines = [] as string[];
    /**
     * Except for the package @sage/xtrem-core we don't want types implemented in the test folder to be exported
     * in the single package definition file.
     */
    getDtsFiles(buildDir, packageName === '@sage/xtrem-core' ? [] : [path.resolve(buildDir, 'test')]).forEach(
        // eslint-disable-next-line no-return-assign
        dtsFile => (dtsLines = dtsLines.concat(transformDtsFiles(packageName, buildDir, dtsFile))),
    );

    // Let's add a final entry point for all types:
    dtsLines.push(`declare module '${packageName}' {`);
    dtsLines.push(`\texport * from '${packageName}/index';`);
    dtsLines.push('}');
    dtsLines.push('');

    console.log(`Generate single definition file ${outPath}`);
    fs.writeFileSync(outPath, dtsLines.join('\n'), 'utf-8');
};
