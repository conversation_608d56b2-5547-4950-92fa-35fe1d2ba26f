import { Application, Test, TestSysVendor, TestUser } from '@sage/xtrem-core';
import * as path from 'path';
import { GraphqlEndpointHooks } from '../../../../lib';

let application: Application;

// Stub the startWebSocketServices hook
GraphqlEndpointHooks.startWebSocketServices = () => {};

export async function getApplication(): Promise<Application> {
    if (!application)
        application = await Test.createTestApplication({
            api: { nodes: { TestSysVendor, TestUser } },
            buildDir: path.join(__dirname, '../../..'),
            startOptions: { channels: ['graphql'], services: [] },
        });
    return application;
}
