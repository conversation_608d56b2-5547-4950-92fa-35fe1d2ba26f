import { Application, Dict, LogicError } from '@sage/xtrem-core';
import { Express, Handler } from 'express';
import { GraphqlEndpointHooks, startApplication } from '../lib';
import { authMiddleware, configMiddleware } from '../lib/middlewares';

export interface HttpTestServerOptions {
    beforeAuth?: Dict<Handler>;
    afterAuth?: Dict<Handler>;
}

/**
 * Starts the test HTTP Server with optional middlewares
 */
export async function startTestHttpServer(
    testApplication: Application,
    options?: HttpTestServerOptions,
): Promise<Express> {
    let testExpressApp: Express | undefined;
    GraphqlEndpointHooks.setAuthenticationMiddlewares = expressApp => {
        const { beforeAuth, afterAuth } = options || {};
        testExpressApp = expressApp;
        if (beforeAuth) {
            Object.values(beforeAuth).forEach(route => {
                expressApp.use(route);
            });
        }
        expressApp.use(authMiddleware);
        expressApp.use(configMiddleware);
        if (afterAuth) {
            Object.values(afterAuth).forEach(route => {
                expressApp.use(route);
            });
        }
    };

    await startApplication(testApplication);
    if (!testExpressApp) throw new LogicError('testExpressApp not found');
    return testExpressApp;
}
