import { Application, ConfigManager, CustomMetrics } from '@sage/xtrem-core';
import { isDevelopmentConfig } from '@sage/xtrem-shared';
import { Express } from 'express';
import { addHealthCheckRoutes } from '../middlewares/health-check-middleware';
import { HttpEndpoint, HttpEndpointConfig } from './http-endpoint';

/**
 * Represents an endpoint for handling metrics.
 */
export class MetricsEndpoint extends HttpEndpoint {
    /**
     * Configures the Express app with the necessary middleware and routes for metrics and health checks.
     *
     * @param expressApp - The Express app instance.
     */
    configureExpressApp(expressApp: Express) {
        expressApp.use('/metrics', CustomMetrics.expressApp);
        addHealthCheckRoutes(expressApp, this.application);
    }

    /**
     * Retrieves the configuration for the metrics endpoint.
     * @returns The HTTP endpoint configuration or undefined if the metrics endpoint is disabled.
     */
    static getConfig(application: Application): HttpEndpointConfig | undefined {
        const config = ConfigManager.current;
        // Only start the endpoint if application was started by `xtrem start`
        if (application.applicationType !== 'service') return undefined;
        // In prod mode only enable it if newrelic is enabled
        if (!isDevelopmentConfig(config) && process.env.NEW_RELIC_ENABLED !== 'true') {
            return undefined;
        }
        const appMetricsUrl = config.apps?.[config.app || '']?.metricsUrl;
        const appMetricsPort = appMetricsUrl && parseInt(new URL(appMetricsUrl).port, 10);
        const port = appMetricsPort || config.server?.metricsPort || 8250;
        // For now, don't set SSL on the metrics endpoint (to be discussed)
        return { port, description: '/metrics endpoint' };
    }

    /**
     * Creates a new MetricsEndpoint instance.
     *
     * @param application - The application instance.
     * @returns A MetricsEndpoint instance if the endpoint is enabled, otherwise undefined.
     */
    static create(application: Application): MetricsEndpoint | undefined {
        const config = this.getConfig(application);
        return config && new MetricsEndpoint(application, config);
    }
}
