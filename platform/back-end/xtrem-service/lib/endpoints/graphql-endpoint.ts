import {
    Application,
    AsyncResponse,
    ConfigManager,
    FileStorageManager,
    SystemError,
    bodyLoggerMiddleware,
    graphQlApp,
    graphQlMetadata,
    loggerAllowsCompress,
    requestLoggerMiddleware,
} from '@sage/xtrem-core';
import { graphiqlMiddleware } from '@sage/xtrem-graphiql';
import { isDevelopmentConfig } from '@sage/xtrem-shared';
import { json } from 'body-parser';
import * as compression from 'compression';
import * as cookieParser from 'cookie-parser';
import { Express } from 'express';
import * as http from 'http';
import { loggers } from '../loggers';
import {
    addMakeMeBusyRoute,
    authMiddleware,
    cloudflareMiddleware,
    configMiddleware,
    csrfMiddleware,
    standaloneConfigMiddleware,
    tooBusyMiddleware,
    xtremUiPluginsMiddleware,
} from '../middlewares';
import { notFoundApp } from '../middlewares/error-handlers';
import { addHealthCheckRoutes } from '../middlewares/health-check-middleware';
import { configureHelmetApp } from '../middlewares/helmet-middleware';
import { TokenInvalidationService } from '../token-invalidation';
import { serveStaticXtremUi } from '../xtrem-ui-app';
import { HttpEndpoint, HttpEndpointConfig } from './http-endpoint';

const logger = loggers.service;

export class GraphqlEndpoint extends HttpEndpoint {
    configureExpressApp(expressApp: Express) {
        expressApp.use(cloudflareMiddleware);
        expressApp.use(requestLoggerMiddleware(loggers.http));
        expressApp.use(cookieParser());
        // set the limit to maxRequestSize, when the limit is set as a number then the value is intepretted as bytes
        expressApp.use(json({ limit: ConfigManager.getSizeLimit('maxRequestSize') }));
        expressApp.use(bodyLoggerMiddleware(loggers.http));
        if (loggerAllowsCompress(loggers.http)) {
            expressApp.use(compression());
        } else {
            logger.warn('compression middleware disabled in HTTP verbose mode');
        }

        configureHelmetApp(expressApp);

        // For compability we add the health check routes to the graphql and metrics endpoint
        // We'll reconfigure the cluster health checks to use the metrics endpoint.
        // So this code will be removed in the future.
        addHealthCheckRoutes(expressApp, this.application);

        expressApp.use(tooBusyMiddleware);

        if (this.application.startOptions.channels.includes('graphql')) {
            this.addAllGraphqlRoutes(expressApp);
        }

        return this;
    }

    private addStandardGraphqlRoutes(expressApp: Express) {
        const config = ConfigManager.current;
        FileStorageManager.addAuthenticatedRoutes(expressApp, this.application);
        expressApp.use('/graphql', (req, res) => {
            if (req.accepts('text/html')) {
                res.redirect('/explorer');
            } else {
                res.redirect('/api');
            }
        });
        expressApp.use('/explorer', csrfMiddleware, graphiqlMiddleware, graphQlApp(this.application));
        expressApp.use('/api', csrfMiddleware, graphQlApp(this.application));
        expressApp.use('/metadata', csrfMiddleware, graphiqlMiddleware, graphQlMetadata(this.application));

        expressApp.use('/login-service', (_req, res) => {
            res.json(config?.security?.loginUrl);
        });
        expressApp.use('/standalone-config', standaloneConfigMiddleware(config, this.application));
        expressApp.use('/plugins', xtremUiPluginsMiddleware(this.application));
        expressApp.get('/documentation', (_req, res) => {
            if (config.documentationServiceUrl) {
                res.redirect(config.documentationServiceUrl);
                res.end();
            } else {
                throw new Error('Documentation service URL is not configured.');
            }
        });
        if (isDevelopmentConfig(ConfigManager.current)) addMakeMeBusyRoute(expressApp);
    }

    private addNotFoundMiddleware(expressApp: Express) {
        const config = ConfigManager.current;

        // BL: Temporary override of the not-found-app with the UI application so we can easier deploy for demos
        if (config.noUi) {
            expressApp.use(notFoundApp);
        } else {
            const xtremStandaloneVersion = this.application
                .getPlatformPackages()
                .find(p => p.name === '@sage/xtrem-standalone')?.version;

            expressApp.get(
                '*path',
                serveStaticXtremUi(
                    config.deploymentMode === 'production' || config.prodUi,
                    config.newRelic,
                    xtremStandaloneVersion,
                    config.disableUiTemplateCache,
                ),
            );
        }
    }

    addAllGraphqlRoutes(expressApp: Express) {
        logger.info('Adding graphql routes to express app');
        GraphqlEndpointHooks.setAuthenticationMiddlewares(expressApp, this.application);
        this.addStandardGraphqlRoutes(expressApp);
        GraphqlEndpointHooks.addSpecialRoutes(expressApp, this.application);
        this.addNotFoundMiddleware(expressApp);
    }

    override async start(): Promise<void> {
        await super.start();
        this.application.graphqlHttpServer = this.httpServer;

        GraphqlEndpointHooks.startWebSocketServices(this.application);
    }

    static getConfig(): HttpEndpointConfig {
        const config = ConfigManager.current;
        const workerId = process.env.XTREM_WORKER_ID;
        // We set the exclusive option to true for a worker conflict as we do not want overlaps in the port allocations
        // to the server
        const exclusive = !!workerId;

        const appUrl = config.apps?.[config.app || '']?.appUrl;
        const appPort = appUrl && parseInt(new URL(appUrl).port, 10);
        // If we are in a worker process allocate the port as 0 so that a dynamic port is allocated
        const port = workerId != null ? 0 : appPort || config.server?.port;
        if (port == null) throw new Error('missing port in config');
        const ssl = config.server?.ssl;
        const sendPortToParentProcess = !!workerId;

        return { port, description: 'graphql endpoint', ssl, exclusive, sendPortToParentProcess };
    }

    static create(application: Application): GraphqlEndpoint {
        return new GraphqlEndpoint(application, this.getConfig());
    }
}

export const GraphqlEndpointHooks = {
    setAuthenticationMiddlewares(expressApp: Express, application: Application): void {
        if (!expressApp) throw new Error('expressApp not found');
        FileStorageManager.addUnauthenticatedRoutes(expressApp, application);
        expressApp.use(authMiddleware);
        expressApp.use(configMiddleware);

        TokenInvalidationService.start(ConfigManager.load(application.dir));
    },

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    addSpecialRoutes(_expressApp: Express, _application: Application): void {},

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    getRequestSource(_request: http.IncomingMessage): AsyncResponse<string> {
        throw new SystemError('getRequestSource hook not implemented');
    },

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    getRequestRoutingKey(_request: http.IncomingMessage): AsyncResponse<string> {
        throw new SystemError('getRequestRoutingKey hook not implemented');
    },

    getSourcesToSeed(): string[] {
        throw new SystemError('getSourcesToSeed hook not implemented');
    },

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    getSourceMaxWorkers(_requestSource: string): number | undefined {
        return undefined;
    },

    /**
     * Starts the websocket services.
     * This is done via a hook because x3-services should not load the xtrem-infrastucture-adapter dependency
     * @param _application
     */
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    startWebSocketServices(_application: Application): void {
        throw new SystemError('startWebSocketService hook not implemented');
    },
};
