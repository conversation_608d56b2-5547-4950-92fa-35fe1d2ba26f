import { Application, registerTlsChangeListener } from '@sage/xtrem-core';
import * as express from 'express';
import { Express } from 'express';
import * as http from 'http';
import * as https from 'https';
import { AddressInfo } from 'net';
import { TlsOptions } from 'tls';
import { loggers } from '../loggers';

export interface HttpEndpointConfig {
    port: number;
    description: string;
    ssl?: TlsOptions;
    exclusive?: boolean;
    sendPortToParentProcess?: boolean;
}

const logger = loggers.service;

/**
 * Base class for HTTP endpoints
 */
export abstract class HttpEndpoint {
    protected httpServer: http.Server | https.Server;

    protected constructor(
        readonly application: Application,
        private readonly config: HttpEndpointConfig,
    ) {}

    /** The port number for the HTTP endpoint. */
    get port(): number {
        return this.config.port;
    }

    /** The description of the HTTP endpoint. */
    get description(): string {
        return this.config.description;
    }

    /**
     * Configures the Express app with the necessary middleware, routes, etc.
     * This method should be implemented by subclasses to provide the specific configuration.
     *
     * @param expressApp The Express app instance.
     */
    protected abstract configureExpressApp(expressApp: Express): void;

    /**
     * Sends the dynamic port allocated back to the parent process via the worker instance 'message' event listener.
     * This method is intended to be used in a worker process.
     * @param port The dynamic port allocated for the worker process.
     * @param workerId The ID of the worker process.
     */
    private static sendPortToParentProcess(port: number, workerId: string | undefined): void {
        // We are in a worker process, we need send the dynamic port allocated back to the parent process via
        // the worker instance 'message' event listener
        if (workerId && process.send) {
            process.send(
                { event: 'listening', workerId: process.env.XTREM_WORKER_ID, port },
                undefined,
                undefined,
                error => {
                    if (error)
                        logger.info(
                            `Error sending message to worker parent process for worker ${workerId}, ${error?.message}`,
                        );
                },
            );
        }
    }

    /**
     * Starts the HTTP server and listens for incoming requests.
     * If SSL is enabled, it creates an HTTPS server, otherwise it creates an HTTP server.
     * If running in a worker process, it allocates a dynamic port, otherwise it uses the configured port.
     *
     * @returns A Promise that resolves when the server has started successfully.
     */
    async start(): Promise<void> {
        const expressApp = express();
        this.configureExpressApp(expressApp);
        if (this.config?.ssl /* && this.config.encrypt */) {
            this.httpServer = https.createServer(this.config.ssl, expressApp);
            registerTlsChangeListener(this.httpServer as https.Server, 'server.ssl', 'HTTPS');
        } else {
            this.httpServer = http.createServer(expressApp);
        }

        const workerId = process.env.XTREM_WORKER_ID;
        // We set the exclusive option to true for a worker conflict as we do not want overlaps in the port allocations
        // to the server
        const exclusive = !!this.config.exclusive;
        // If we are in a worker process allocate the port as 0 so that a dynamic port is allocated
        const port = workerId != null ? 0 : this.port;

        await new Promise<void>((resolve, reject) => {
            const server = this.httpServer.listen({ port, exclusive });
            server
                .on('listening', () => {
                    const serverPort = (server.address() as AddressInfo).port;
                    const workerMessage = workerId != null ? ` for worker ${workerId}` : '';
                    const tlsMesage = this.config.ssl ? ' over TLS' : '';

                    logger.info(
                        `${this.description} started on port ${serverPort || this.port}${workerMessage}${tlsMesage}`,
                    );

                    if (this.config.sendPortToParentProcess) {
                        HttpEndpoint.sendPortToParentProcess(serverPort, workerId);
                    }
                    resolve();
                })
                .on('error', err => {
                    // eslint-disable-next-line no-console
                    console.error(`Cannot start ${this.description}: ${err.stack}`);
                    reject(err);
                });
        });
    }
}
