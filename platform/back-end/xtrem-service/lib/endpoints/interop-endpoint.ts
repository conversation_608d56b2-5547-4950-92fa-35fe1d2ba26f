import { Application, ConfigManager, SystemError, interopApp } from '@sage/xtrem-core';
import { Express } from 'express';
import { HttpEndpoint, HttpEndpointConfig } from './http-endpoint';

/**
 * Represents an endpoint for interop GraphQl calls between apps.
 */
export class InteropEndpoint extends HttpEndpoint {
    /**
     * Configures the Express app with the interop middleware.
     *
     * @param expressApp - The Express app instance.
     */
    configureExpressApp(expressApp: Express) {
        expressApp.use(interopApp(this.application));
    }

    /**
     * Retrieves the configuration for the interop endpoint.
     * @returns The HTTP endpoint configuration or undefined if the configuration is missing or the endpoint is not enabled.
     */
    static getConfig(): HttpEndpointConfig | undefined {
        const config = ConfigManager.current;
        if (!config.app) return undefined;

        const appInteropUrl = config.apps?.[config.app]?.interopUrl;
        const appInteropPort = appInteropUrl ? +new URL(appInteropUrl).port : 0;
        const serverInteropPort = config.server?.interopPort ? +config.server.interopPort : 0;

        if (!appInteropPort && !serverInteropPort) {
            throw new SystemError('Missing interop port in config');
        }
        if (appInteropPort && serverInteropPort && appInteropPort !== serverInteropPort) {
            throw new SystemError(
                `Inconsitent interop ports in config: apps.yml port=${appInteropPort}, xtrem-config.yml port=${serverInteropPort}`,
            );
        }

        // No ambiguity here, either they are equal or one of them is 0
        const port = serverInteropPort || appInteropPort;
        const ssl = config.server?.ssl ? { ...config.server?.ssl } : undefined;
        if (ssl?.cert && ssl?.key) {
            // Mutual TLS: Request a certificate from a client and attempt to verify it
            ssl.requestCert = true;
        }

        return { port, description: 'interop endpoint', ssl };
    }

    /**
     * Creates a new InteropEndpoint instance.
     *
     * @param application - The application to associate with the InteropEndpoint.
     * @returns The created InteropEndpoint instance, or undefined if the endpoint is not enabled.
     */
    static create(application: Application): InteropEndpoint | undefined {
        const config = this.getConfig();
        return config && new InteropEndpoint(application, config);
    }
}
