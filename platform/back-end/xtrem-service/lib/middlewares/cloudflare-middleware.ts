import { Handler, NextFunction, Request, Response } from 'express';
import { nanoid } from 'nanoid';

export const cloudflareMiddleware: Handler = (req: Request, _res: Response, next: NextFunction) => {
    // By default, we use cloudflare ray id as eventid for tracking every api requests,
    // if it is not present, we generate our own so that we have a unique eventid
    if (!req.headers['cf-ray']) {
        req.headers['cf-ray'] = `gen-${nanoid()}`;
    }
    return next();
};
