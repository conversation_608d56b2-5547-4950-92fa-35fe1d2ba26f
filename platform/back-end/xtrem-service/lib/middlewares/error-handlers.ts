import { Handler, Request, Response } from 'express';

function requestError(req: Request, res: Response, status: number, name: string): Response {
    return res.status(status).json({
        $diagnoses: [
            {
                $severity: 'error',
                $message: `${name}: ${req.method} ${req.url}`,
                $statusCode: status,
            },
        ],
    });
}

export const unauthorized = (req: Request, res: Response): Response => {
    return requestError(req, res, 401, 'unauthorized');
};

export const forbidden = (req: Request, res: Response): Response => {
    return requestError(req, res, 403, 'forbidden');
};

export const badRequest = (req: Request, res: Response): Response => {
    return requestError(req, res, 400, 'bad request');
};

export const tooBusyError = (retryAfterInSeconds: number, req: Request, res: Response): Response => {
    res.setHeader('retry-after', retryAfterInSeconds);
    return requestError(req, res, 503, 'too busy');
};

export const notFoundApp: Handler = (req: Request, res: Response) => {
    requestError(req, res, 404, 'not found');
};
