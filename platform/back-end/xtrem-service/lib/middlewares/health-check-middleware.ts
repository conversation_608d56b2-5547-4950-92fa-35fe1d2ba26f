import { Application } from '@sage/xtrem-core';
import { Express } from 'express';

/**
 * Adds health check routes to the Express application.
 *
 * @param expressApp - The Express application.
 * @param application - The application object.
 */
export const addHealthCheckRoutes = (expressApp: Express, application: Application): void => {
    // Set a liveness HTTP endpoint that sends a simple 200 status to inform that the container is alive and healthy:
    // see: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-startup-probes/
    expressApp.get('/ping', (_req, res) => {
        res.json({});
    });

    // Set a readiness HTTP endpoint that:
    // - sends a simple 502 status when the container is being started
    // - sends a simple 200 status when container is ready to manager requests
    // see: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-startup-probes/
    expressApp.get('/ready', (_req, res) => {
        if (application.isReady) res.json({});
        else res.status(502).send();
    });
};
