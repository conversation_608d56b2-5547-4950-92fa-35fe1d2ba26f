import { Application, Logger } from '@sage/xtrem-core';
import { Config, StandaloneConfig, locales } from '@sage/xtrem-shared';
import { Request, Response } from 'express';

const logger = Logger.getLogger(__filename, 'aggrid');

const getEncryptedLicenceKey = (config: Config, res: Response): string | null => {
    if (res.locals.context && config.agGridLicenceKey) {
        const encryptedLicenceString = Buffer.from(config.agGridLicenceKey).toString('base64');
        logger.debug(() => `Encrypted licence: ${encryptedLicenceString}`);
        return encryptedLicenceString;
    }
    return null;
};

const getAvailableLocales = (application: Application, res: Response): string[] | undefined => {
    if (res.locals.context && application.mainPackage.isMain && application.mainPackage.packageJson?.xtrem?.locales) {
        const availableLocales = application.mainPackage.packageJson?.xtrem?.locales.filter(
            locale => locales.find(availableLocale => locale === availableLocale) !== undefined,
        );
        // If no locales are available or all of provided ones are malformed, return undefined to prevent the frontend from crashing
        if (availableLocales.length === 0) return undefined;
        logger.info(() => `Available locales for package ${application.mainPackage.name} : ${availableLocales}`);
        return availableLocales;
    }
    return undefined;
};

export const standaloneConfigMiddleware = (config: Config, application: Application) => {
    if (config.agGridLicenceKey) {
        logger.debug(() => `Configured ag-grid key: ${config.agGridLicenceKey}`);
    } else {
        logger.debug(() => 'No ag grid key is configured.');
    }

    return (req: Request, res: Response) => {
        if (!res.locals.context) {
            res.json(null);
            return;
        }

        const standaloneConfig: StandaloneConfig = {
            agGridLicenceKey: getEncryptedLicenceKey(config, res),
            chatbotBackendUrl: config.copilot?.serviceUrl,
            chatbotGmsClient: config.copilot?.gmsClient ?? 'sdmo_v1',
            chatbotAccessCodeLifeTimeInMinutes: config.copilot?.accessCodeLifeTimeInMinutes,
            productName: config.productName || 'Sage Distribution and Manufacturing Operations',
            app: config.app,
            pendoClusterTag: config.pendo?.clusterTag,
            locales: getAvailableLocales(application, res),
        };

        res.json(standaloneConfig);
    };
};
