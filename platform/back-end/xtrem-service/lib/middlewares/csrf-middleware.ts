import { logSecurityAlert } from '@sage/xtrem-core';
import { Maybe } from '@sage/xtrem-shared';
import { Handler, NextFunction, Request, Response } from 'express';
import { badRequest } from './error-handlers';

export const csrfMiddleware: Handler = (req: Request, res: Response, next: NextFunction) => {
    if (csrfFilter(req)) {
        badRequest(req, res);
        return;
    }
    next();
};

function reject(request: Request, reason: string): boolean {
    logSecurityAlert(request, 'csrf', reason);
    return true;
}

function csrfFilter(request: Request): boolean {
    if (request.query?.query != null) {
        // protect from csrf attack with mutation in query parameter like the following example that tries to create a user:
        // <html><head></head>
        // <body onload=document.getElementById('xsrf').submit() >
        //   <form id="xsrf" action="http://localhost:8240/api?query=mutation%20%7BxtremAuthorization%20%7Buser%20%7Bcreate%20(data%3A%20%7Bphoto%3A%20null%2C%20isActive%3A%20true%2C%20firstName%3A%20%22crsf%22%2C%20lastName%3A%20%22attack%22%2C%20email%3A%20%22csrf.attack%40example.com%22%7D)%20%7B%20_id%20isActive%20firstName%20lastName%20email%20photo%20%7Bvalue%7D%7D%7D%7D%7D" method=POST enctype="text/plain"></form>
        // </body></html>
        return reject(request, 'graphql query parameter is set');
    }
    if (request.rawHeaders.length === 0) {
        return reject(request, 'no headers');
    }
    if (request.method === 'POST' && !request.is('application/json')) {
        return reject(
            request,
            `bad content-type expecting 'application/json' got '${request.headers['content-type']}'`,
        );
    }
    // protect from flash / redirect attacks, it is a bit outdated since the removing of flash support in browser
    // see: https://blog.appsecco.com/exploiting-csrf-on-json-endpoints-with-flash-and-redirects-681d4ad6b31b
    const requestWith = request.headers['x-requested-with'];
    const requestWithArray = !requestWith ? [] : Array.isArray(requestWith) ? requestWith : [requestWith];
    if (requestWithArray.some(h => /^ShockwaveFlash\//i.test(h || ''))) {
        return reject(request, 'flash request');
    }

    return csrfFilterOrigin(request);
}

function csrfFilterOrigin(request: Request): boolean {
    if (/^api\|/.test(request.res?.locals?.auth?.auth0)) {
        // we allow the api users to bypass the csrf check
        // auth token has already been checked by the auth middleware so we can trust it
        return false;
    }

    // Verifying Same Origin with Standard Headers

    // Try to get the source from the "Origin" header, browser should always send it
    // but Edge (non chromium) do not do it for post request
    let source: Maybe<string> = request.headers.origin;
    if (source === 'null') {
        source = request.headers.referer;
        if (!source) {
            return reject(request, 'null origin with empty referer');
        }
    } else if (!source) {
        // If empty then fallback on "Referer" header
        source = request.headers.referer;
    }

    if (source) {
        const url = new URL(source);
        // When behind a proxy the x-forwarded-host header must be set to the original host
        const forwardedHost = request.headers['x-forwarded-host'];
        const hostname = (
            (Array.isArray(forwardedHost) ? forwardedHost[0] : forwardedHost) || request.headers.host
        )?.toLowerCase();
        if (url.host.toLowerCase() !== hostname) {
            return reject(
                request,
                `Url host '${url.host}' does not fully match '${hostname}'. host='${request.headers.host}', x-forwarded-host='${forwardedHost}'`,
            );
        }
    }
    return false;
}
