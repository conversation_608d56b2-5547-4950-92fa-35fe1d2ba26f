import { ConfigManager } from '@sage/xtrem-core';
import { isDevelopmentConfig } from '@sage/xtrem-shared';
import { Express, Handler, NextFunction, Request, Response } from 'express';
import { loggers } from '../loggers';
import { tooBusyError } from './error-handlers';

const toobusy = require('toobusy-js');

const defaultConfig = {
    retryAfterInSeconds: 5,
    // Our default is very high because the integration tests are failing with low values
    // The toobusy default is 70ms, see later to change it to a value closer to that one
    maxLagInMillis: 3000,
    // Our default is high because the integration tests are failing with low values
    // the toobusy default is 500ms, see later to change it to a value closer to that one
    intervalInMillis: 1000,
    smoothingFactor: toobusy.smoothingFactor(), // same as the toobusy default (0.3333....)
};

toobusy.maxLag(defaultConfig.maxLagInMillis);
toobusy.interval(defaultConfig.intervalInMillis);
toobusy.smoothingFactor(defaultConfig.smoothingFactor);

loggers.service.info(
    `Set toobusy default config: maxLag=${toobusy.maxLag()}ms, interval=${toobusy.interval()}, smoothingFactor=${toobusy.smoothingFactor()}`,
);

ConfigManager.emitter.addListener('loaded', () => {
    const tooBusyConfig = ConfigManager.current.security?.services?.tooBusy;
    if (!tooBusyConfig?.disabled) {
        toobusy.maxLag(tooBusyConfig?.maxLagInMillis || defaultConfig.maxLagInMillis);
        toobusy.interval(tooBusyConfig?.intervalInMillis || defaultConfig.intervalInMillis);
        toobusy.smoothingFactor(tooBusyConfig?.smoothingFactor || defaultConfig.smoothingFactor);
        loggers.service.info(
            `Set toobusy config: maxLag=${toobusy.maxLag()}ms, interval=${toobusy.interval()}, smoothingFactor=${toobusy.smoothingFactor()}`,
        );
    } else {
        loggers.service.warn('toobusy middleware is disabled');
    }
});

export const tooBusyMiddleware: Handler = (req: Request, res: Response, next: NextFunction) => {
    const tooBusyConfig = ConfigManager.current.security?.services?.tooBusy;
    if (!tooBusyConfig?.disabled && toobusy()) {
        tooBusyError(tooBusyConfig?.retryAfterInSeconds || defaultConfig.retryAfterInSeconds, req, res);
    } else {
        next();
    }
};

// This add an event listener on the lag event with the default lag values
// The lag event does not affect the toobusy behaviour so that we can track lags without returning a 503
toobusy.onLag((currentLag: number) => {
    loggers.service.warn(`Event loop lag detected! Latency: ${Math.floor(currentLag * 1000) / 1000}ms`);
});

export function addMakeMeBusyRoute(app: Express) {
    if (isDevelopmentConfig(ConfigManager.current) && ['1', 'true'].includes(process.env.XTREM_DEV_BUSY_ROUTE ?? '')) {
        app.get('/dev/make-me-busy/:factor', (req, res) => {
            let i = 0;
            const t0 = Date.now();
            const count = 1e6 * parseInt(req.params.factor || '1', 10);
            // eslint-disable-next-line no-plusplus
            while (i < count) i++;
            res.json({ factor: req.params.factor, elapsed: Date.now() - t0 });
        });
    }
}
