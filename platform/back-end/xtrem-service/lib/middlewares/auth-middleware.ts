import { Config<PERSON>anager, JwtClaims, Logger, logSecurityAlert, Test } from '@sage/xtrem-core';
import { addCustomAttributesToNewrelic } from '@sage/xtrem-metrics';
import { AuthConfig, Config, isDevelopmentConfig, isEnvVarTrue, SecurityConfig } from '@sage/xtrem-shared';
import axios, { AxiosRequestConfig } from 'axios';
import { Handler, NextFunction, Request, Response } from 'express';
import { IncomingHttpHeaders } from 'http';
import * as jwt from 'jsonwebtoken';
import * as jwksClient from 'jwks-rsa';
import { TokenInvalidationService } from '../token-invalidation';
import { unauthorized } from './error-handlers';

const auth0WebUserPrefix = 'auth0';
const auth0ApiPrefix = 'api';
const auth0CopilotPrefix = 'copilot';

type Auth0Type = typeof auth0ApiPrefix | typeof auth0WebUserPrefix | typeof auth0CopilotPrefix;

interface VerifiedAuth {
    authScheme?: string;
    authHeader?: string;
    token?: JwtClaims;
    auth0Type?: Auth0Type;
}

const isNewrelicDisabled = process.env.NEW_RELIC_ENABLED !== 'true';
const logger = Logger.getLogger(__filename, 'auth');

// Verify the authorization using a jwt token
// For testing, tokens can be generated using http://jwtbuilder.jamiekurtz.com/

function extractToken(authHeader: string) {
    if (!authHeader || authHeader.length < 8) return null;
    if (authHeader.indexOf('Bearer ') !== 0) return null;
    const token = /^Bearer\s+([a-zA-Z0-9_-]+\.[a-zA-Z0-9_-]+\.[a-zA-Z0-9_-]+)/.exec(authHeader);
    return token && token[1];
}

function redirectToLogin(req: Request, res: Response, security: SecurityConfig): void {
    if (req.accepts('html')) {
        if (!security.loginUrl) throw new Error('Cannot redirect: no login URL configured');
        // redirect to https://<login_service_url>?fromUrl=https://<cluster_url>/path/to/redirect
        const fromUrl = security.redirectUrl ? `?fromUrl=${encodeURIComponent(security.redirectUrl)}` : '';
        res.redirect(`${security.loginUrl}${fromUrl}`);
        return;
    }
    unauthorized(req, res);
}

// Verify the authorization
// If the verification failed, send a 404 and not 403 for security reason
export const authMiddleware: Handler = async (req: Request, res: Response, next: NextFunction) => {
    const config = ConfigManager.current;
    const { security } = config;

    res.locals.auth = {} as AuthConfig;
    if (isDevelopmentConfig(config)) {
        if (isEnvVarTrue(process.env.UNSECURE_DEV_LOGIN)) {
            if (req.path === '/unsecuredevlogin') {
                // In development mode let's provide a basic authentication for artillery:
                if (!req.query.user || !req.query.tenant) {
                    unauthorizedError('user and tenant are mandatory for unsecure dev login', req, res);
                    return;
                }
                res.locals.auth = { login: req.query.user, tenantId: req.query.tenant };
                res.cookie('access_token', `${req.query.tenant}_${res.locals.auth.login}`);
                res.cookie('access_token_sign', `${req.query.tenant}_${res.locals.auth.login}`);
                const host = req.get('host');
                if (host) {
                    res.location(`${req.protocol}://${host}`);
                    res.status(302);
                }
                res.send();
                return;
            }
            // The content of the access token is weird and should be reviewed later to have the same format as an actual access_token
            const { access_token, access_token_sign } = req.cookies;
            if (access_token && access_token_sign && access_token === access_token_sign) {
                const [tenantId, login] = access_token.split('_');
                res.locals.auth = { login, tenantId };
            } else if (req.headers.authorization) {
                const authorization = /^Basic\s+(.*)/.exec(req.headers.authorization || '');
                if (authorization) {
                    const token = Buffer.from(authorization[1], 'base64').toString();
                    if (token) {
                        res.locals.auth = {
                            login: token.split(':')[0],
                            tenantId: req.cookies.access_token.split('_')[0],
                        };
                    }
                }
            }
        }
        if (config.auth) {
            res.locals.auth.login ??= config.auth?.login;
            res.locals.auth.tenantId ??= config.auth?.tenantId;
            res.locals.auth.auth0 ??= config.auth?.auth0;
        } else {
            res.locals.auth.login ??= config.user ?? Test.defaultEmail;
            res.locals.auth.tenantId ??= config.tenantId ?? Test.defaultTenantId;
        }
        const email = res.locals.auth.login;
        res.locals.context = {
            email,
            tenantId: res.locals.auth.tenantId,
        };
        if (email) {
            const devIdMatch = /^api-(\w+)@((?:[a-zA-Z0-9-]+\.)+[a-zA-Z]{2,7})$/.exec(email);
            // '7bda53083aef4b4780ecbb73a318a966' is just a random uuid to look like an auth0 value
            res.locals.context.auth0 = devIdMatch ? `api|${devIdMatch[1]}` : 'auth0|7bda53083aef4b4780ecbb73a318a966';
        }
        sharpenNewrelicContext(auth0WebUserPrefix, res.locals.auth.tenantId, res.locals.context.auth0, req.headers);
        const devPersonaCookieName = `xtrem_${res.locals.auth.tenantId}_persona`;
        if (req.cookies[devPersonaCookieName]) res.locals.auth.persona = req.cookies[devPersonaCookieName];
    }

    if (!security || shouldIgnoreAuthentication(req, security)) {
        next();
        return;
    }

    try {
        const { token, auth0Type } = await verifyAuth(security, req);
        if (!token) {
            // no token: redirect if request is from browser otherwise fail
            if (req.accepts('html')) {
                if (!security.loginUrl) throw new Error('Cannot redirect: no login URL configured');
                redirectToLogin(req, res, security);
                return;
            }
            throw new Error('no token');
        } else {
            // Is the token invalidated by logout
            if (TokenInvalidationService.isInvalidatedToken(token)) {
                throw new Error('token has been invalidated');
            }

            verifyTokenClaims(token, config);

            // if request is from browser we check token expiration to initiate a refresh_token if necessary
            // For now we do not enable server side auto refresh.
            if (security.enableAutoRefresh && req.accepts('html')) {
                await tryAutoRefeshToken(token, security, req, res);
            }

            // if the user has not set the cookie policy preference we redirect him to the login page https://jira.sage.com/browse/XT-10838
            // only check preference for tokens generated by the login service for UI access
            // tokens for api usage start with 'api|' and don't have pref field
            if (!token.pref && token.auth0 && auth0Type === auth0WebUserPrefix) {
                redirectToLogin(req, res, security);
                return;
            }
            res.locals.context = {
                email: token.sub,
                tenantId: token.tenantId,
                pref: token.pref,
            };
            res.locals.auth = {
                ...res.locals.auth,
                login: token.sub,
                tenantId: token.tenantId,
                auth0: token.auth0,
            };

            sharpenNewrelicContext(auth0Type, token.tenantId || 'unknown', token.auth0, req.headers);

            const personaCookieName = `xtrem_${res.locals.context.tenantId}_persona`;
            if (req.cookies[personaCookieName]) res.locals.auth.persona = req.cookies[personaCookieName];

            // continue routing
            next();
        }
    } catch (e) {
        // if we have a login URL we redirect to that login page if the token has expired or the audience is wrong
        // This is done only for HTML requests, api (/api, /metadata, ...) requests will still return a 401
        if (security.loginUrl && req.accepts('html') && e instanceof jwt.JsonWebTokenError) {
            const shouldRedirectToLogin =
                e instanceof jwt.TokenExpiredError || /^jwt audience invalid\./i.test(e.message);
            if (shouldRedirectToLogin) {
                logger.warn(`Redirecting to ${security.loginUrl} request ${req.method} ${req.url} [${e.message}]`);
                // it is common to use 302 for redirect but according the web standards it is better to return a 307 for non GET request.
                // This is to not allow the browser to change the method. see https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/307
                res.redirect(req.method === 'GET' ? 302 : 307, security.loginUrl);
                return;
            }
        }

        unauthorizedError(e.message, req, res);
    }
};

/**
 * Should we ignore token access validation for the current request
 */
function shouldIgnoreAuthentication(req: Request, security: SecurityConfig | undefined): boolean {
    // ping route or login
    const isUrlToIgnore = /^\/(ping)$/.test(req.path) || /^\/login-service$/.test(req.path);

    // means no authentication service in current context
    const isCurrentConfigWithoutLoginService = !security?.loginUrl;

    return isCurrentConfigWithoutLoginService || isUrlToIgnore;
}

/**
 * Attach extra information to any newrelic metric and event that will be recorded during the request
 * @param auth0Type kind of auth0 source, 3rd party api or web user ?
 * @param tenantId tenantId related to the request
 * @param auth0TokenField token.auth0 field, format is api|<developerid> for api or auth0|email for sageid users
 * @param requestHeaders the http header of the request
 */
function sharpenNewrelicContext(
    auth0Type: Auth0Type | undefined,
    tenantId: string,
    auth0TokenField: string | undefined,
    requestHeaders: IncomingHttpHeaders,
) {
    if (isNewrelicDisabled) {
        return;
    }

    const cfRay = requestHeaders['cf-ray'] as string; // it's a string since cloudflare-middleware is executed before
    const newrelicCustomAttributes: { [key: string]: string | number | boolean } = {
        tenantId,
        requestOrigin: getNewRelicSourceFromAuth0Type(auth0Type),
        cfRay,
    };

    if ((auth0Type === auth0ApiPrefix || auth0Type === auth0CopilotPrefix) && auth0TokenField) {
        const auth0Array = auth0TokenField.split('|');
        if (auth0Array.length >= 2) {
            newrelicCustomAttributes.developerId = auth0Array[1];
        } else {
            logger.warn(
                `Unable to extract the develpperId from an api gateway call, auth0field is : ${auth0TokenField}`,
            );
            newrelicCustomAttributes.developerId = 'unknown';
        }
    }

    try {
        addCustomAttributesToNewrelic(newrelicCustomAttributes);
    } catch ({ message }) {
        // log and swallow, we don't want this event emiter to disturb the processing of the request
        logger.warn(`Failed to send custom attributes to newrelic ${message}`);
    }
}

/**
 * Can look overkill but will make sure we have a warn if we have a new auth0 prefix we don't handle yet
 * @param auth0Type
 */
function getNewRelicSourceFromAuth0Type(auth0Type: Auth0Type | undefined) {
    if (!auth0Type) {
        return 'unknown';
    }
    switch (auth0Type) {
        case auth0WebUserPrefix:
            return 'webuser';
        case auth0CopilotPrefix:
            return 'copilot';
        case auth0ApiPrefix:
            return 'apigateway';
        default:
            logger.warn(`unknown source for newrelic request context : ${auth0Type}`);
            return 'unknown';
    }
}

function unauthorizedError(message: string, req: Request, res: Response) {
    logSecurityAlert(req, 'unauthorized', message);
    return unauthorized(req, res);
}

async function verifyAuth(security: SecurityConfig, req: Request): Promise<VerifiedAuth> {
    const auth: VerifiedAuth = { authHeader: req.headers.authorization };
    let token;
    if (auth.authHeader) {
        auth.authScheme = auth.authHeader.split(' ')[0];
        token = extractToken(auth.authHeader);
        if (!token) {
            throw new Error(`Invalid token: authorization scheme ${auth.authScheme}`);
        }
    } else if (req.cookies.access_token && req.cookies.access_token_sign) {
        // Reassemble JWT token from cookies
        token = `${req.cookies.access_token}.${req.cookies.access_token_sign}`;
    } else {
        return {};
    }
    // If there is no signature, token must be rejected
    // As soon as there is a signature 'jsonwebtoken' will not allow none alg in token header
    if (token?.split('.').length !== 3) {
        throw new Error('Invalid token: must contain 3 segments');
    }

    auth.token = await verifyAuthToken(token, security);
    // 'auth0' value is like 'api|<dev-id>' or 'auth0|<id>'
    auth.auth0Type = (auth.token.auth0 || '').split('|')[0] as Auth0Type;
    if (auth.authHeader) {
        if (auth.auth0Type !== auth0ApiPrefix && auth.auth0Type !== auth0CopilotPrefix) {
            throw new Error(`Bearer authentication expects an api token, got '${auth.token.auth0}'`);
        }
    } else if (auth.auth0Type === auth0ApiPrefix || auth.auth0Type === auth0CopilotPrefix) {
        throw new Error(`Cookie authentication cannot use an api token, got '${auth.token.auth0}'`);
    }
    return auth;
}

// The only jwksClient to benefit from caching
let cachedClient: jwksClient.JwksClient;

function getJwksClient(jwksUrl: string) {
    if (!cachedClient) {
        if (!jwksUrl) throw new Error("missing 'jwksUrl' value in security config");
        cachedClient = jwksClient({
            cache: true, // default is to cache max 5 entries for 10 hours
            jwksUri: jwksUrl || '', // example: 'https://login.dev-sagextrem.com/.well-known/jwks.json'
        });
    }
    return cachedClient;
}

function verifyAuthToken(token: string, security: SecurityConfig): Promise<JwtClaims> {
    const { issuer, audience, jwksUrl } = security;
    const secretOrPublicKey = (() => {
        const client = getJwksClient(jwksUrl!);
        return (header: any, cb: (err: any, res?: any) => void) =>
            client.getSigningKey(header.kid, (err: Error | null, key: jwksClient.SigningKey) => {
                if (err) {
                    cb(err);
                } else {
                    cb(null, key.getPublicKey());
                }
            });
    })();

    const verifyOptions = {
        issuer,
        algorithm: 'RS256',
        audience,
    };
    return new Promise<JwtClaims>((resolve, reject) => {
        jwt.verify(token, secretOrPublicKey, verifyOptions, (err, result) => {
            if (err) reject(err);
            else resolve(result as JwtClaims);
        });
    });
}

async function tryAutoRefeshToken(
    token: JwtClaims,
    security: SecurityConfig,
    req: Request,
    res: Response,
): Promise<void> {
    const renewalExpire = token.exp == null ? -1 : token.exp - Math.floor(Date.now() / 1000);
    const renewalThreshold = security.renewalThreshold || 300;
    if (
        renewalExpire >= 0 &&
        renewalExpire < renewalThreshold &&
        req.cookies.refresh_token &&
        req.cookies.refresh_token_sign
    ) {
        const renewRequest: AxiosRequestConfig = {
            timeout: 500,
            headers: {
                'x-refresh-token': `${req.cookies.refresh_token}.${req.cookies.refresh_token_sign}`,
                'x-access-token': `${req.cookies.access_token}.${req.cookies.access_token_sign}`,
                connection: 'Keep-Alive',
            },
        };
        try {
            const renewalUrl = security.renewalUrl || `${security.loginUrl}/renew`;
            const resp = await axios.get(renewalUrl, renewRequest);
            // transfert only token cookies in the final response
            const cookies = resp.headers['set-cookie']!.filter((c: string) =>
                /^(?:access|refresh)_token(?:_sign)?=/.test(c),
            );
            res.set('set-cookie', cookies);
        } catch (e) {
            logger.error(`Failed to auto-refresh token: ${e.message}`);
        }
    }
}

/**
 * Verify the token claims
 * @param token the token to verify
 * @param config the current configuration
 * @returns true if the token is valid
 * @throws Error if the token does not contain the app claim
 * @throws Error if the token does not contain the apps claim
 * @throws Error if the token is not valid for the current app
 * @throws Error if the token audience does not match the security config
 * @see https://confluence.sage.com/display/XTREEM/Authentication+service+v2+-+token+validation
 */
function verifyTokenClaims(token: JwtClaims, config: Config): boolean {
    const { security, app } = config;

    // We are not in a bizapp container nothing more to check
    if (!app) {
        return true;
    }

    // token must have been created for an initial app
    if (!token.app) {
        throw new Error('token does not contain app claim');
    }
    // ensure there is an "apps" array in the token, if not then reject the request there is a configuration mismatch between old an new method (this should never happen).
    if (!token.apps) {
        throw new Error('token does not contain apps claim');
    }
    // ensure the app config value is included in the apps array of the token
    if (!token.apps.includes(app)) {
        throw new Error(`token is not valid for app '${app}'`);
    }
    // ensure the aud matches exactly the one of xtrem-security.yml (the audience in the yaml security will also include the wildcard)
    if (token.aud !== security?.audience) {
        throw new Error(
            `jwt audience invalid. It does not match security config, expected: ${security?.audience}, got: ${token.aud}`,
        );
    }
    return true;
}
