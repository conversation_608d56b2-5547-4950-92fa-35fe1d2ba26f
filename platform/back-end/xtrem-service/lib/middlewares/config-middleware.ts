import { Config<PERSON><PERSON><PERSON>, Logger } from '@sage/xtrem-core';
import { Config } from '@sage/xtrem-shared';
import { <PERSON><PERSON>, NextFunction, Request, Response } from 'express';
import * as _ from 'lodash';
import { badRequest } from './error-handlers';

const logger = Logger.getLogger(__filename, 'config');

function parseScopes(config: Config, req: Request) {
    logger.verbose(() =>
        ['x-api-app-id', 'x-api-app-name', 'x-api-scope', 'x-api-base-url']
            .map(k => `${k}=${JSON.stringify(req.header(k))}`)
            .join(' '),
    );

    const scopes = config.scope || req.header('x-api-scope') || req.header('x-mindo-scope');
    if (scopes && /\ball:readonly\b/i.test(scopes.toString())) {
        if (config.storage && !config.storage.managedExternal && !config.storage.sql)
            throw new Error('SQL config missing');
        config.graphql = config.graphql || {};
        config.graphql.isReadonly = true;
        logger.info(() => 'request is configured in read-only mode');
    }
}

function acceptService(config: Config, req: Request, res: Response) {
    if (!config.scope) return true;

    const service = req.url.split('/')[1];
    if (!service) {
        badRequest(req, res);
        return false;
    }

    // TODO: review scope management
    // const re = new RegExp(`\\bapi\\.${service}\\b`, 'i');
    // // accept only request with matching scope or graphql having a user
    // const accepted =
    //     re.test(config.scope) || (/(?:api|explorer)/.test(service) && /\buser\.[^\s]+\b/.test(config.scope));
    // if (!accepted) {
    //     forbidden(req, res, next);
    //     return false;
    // }
    return true;
}

function processLastCommitTimestamp(config: Config, req: Request, res: Response) {
    const timestampCookieName = _.snakeCase(
        `xtrem_${config.clusterId ?? ''}_${config.app ?? ''}_${res.locals.auth.tenantId}__lastCommitTimestamp`,
    );
    if (req.cookies[timestampCookieName]) {
        res.locals.lastCommitTimestamp =
            req.cookies[timestampCookieName] === 'null' ? null : req.cookies[timestampCookieName];
    }
}

function processConfig(config: Config, req: Request, res: Response, next: NextFunction) {
    logger.verbose(() => `email=${JSON.stringify(config.email)} scope=${JSON.stringify(config.scope)} `);

    parseScopes(config, req);

    processLastCommitTimestamp(config, req, res);

    if (acceptService(config, req, res)) next();
}

function processLocalConfig(res: Response): Config {
    res.locals.config = { ...ConfigManager.current, ...res.locals.context };
    return res.locals.config;
}

export const configMiddleware: Handler = (req: Request, res: Response, next: NextFunction) => {
    if (/^\/ping$/.test(req.path)) {
        next();
        return;
    }

    processConfig(processLocalConfig(res), req, res, next);
};
