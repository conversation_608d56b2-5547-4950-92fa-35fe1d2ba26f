/* eslint-disable no-console */
import {
    Application,
    ConfigManager,
    ContainerHeartbeatMonitor,
    InteropAppHealthMonitor,
    Logger,
    asyncArray,
    json5Stringify,
    sourceMapSetup,
    unhandledErrorMonitor,
} from '@sage/xtrem-core';
import { GraphqlEndpoint } from './endpoints/graphql-endpoint';
import { InteropEndpoint } from './endpoints/interop-endpoint';
import { MetricsEndpoint } from './endpoints/metrics-endpoint';
import { loggers } from './loggers';
import { TokenInvalidationService } from './token-invalidation';

const logger = loggers.service;
Error.stackTraceLimit = 100;

sourceMapSetup();

process.on('SIGINT', () => {
    TokenInvalidationService.stop();
    console.error(`process ${process.pid} killed by SIGINT`);
    process.exit(1);
});

function logApplicationInfo(application: Application) {
    const config = ConfigManager.current;
    logger.info(`Deployment mode is ${config.deploymentMode}`);
    logger.info('Framework packages:');
    application.getPlatformPackages().forEach(p => {
        logger.info(`\t- ${p.name}: ${p.version}`);
    });
    logger.info('Application packages:');
    application.getPackages().forEach(p => {
        logger.info(`\t- ${p.name}: ${p.packageJson.version}`);
    });
    if (Logger.isLogAsJson()) {
        logger.info(`Start options: ${json5Stringify(application.startOptions)}`);
    } else {
        logger.info('Start options:');
        Object.keys(application.startOptions).forEach((key: keyof typeof application.startOptions) => {
            logger.info(`\t${key}: ${application.startOptions[key]}`);
        });
    }
}

function requiresInteropService(application: Application) {
    const config = ConfigManager.current;
    return !config.storage?.managedExternal && application.startOptions.channels.join() !== 'routing';
}

const canStartHealthMonitoring = requiresInteropService;

function startContainerHeartbeatMonitor(application: Application) {
    if (canStartHealthMonitoring(application)) {
        ContainerHeartbeatMonitor.activateHeartbeatPublications(application);
        ContainerHeartbeatMonitor.activate();
    }
}

function startAppsHealthCheckMonitor(application: Application) {
    if (canStartHealthMonitoring(application)) {
        InteropAppHealthMonitor.start();
    }
}

function startUnhandledErrorMonitor() {
    const config = ConfigManager.current;
    unhandledErrorMonitor.start(config.errorMonitoringInterval, config.errorMonitoringThreshold);
}

async function startPackageServices(application: Application): Promise<void> {
    if (ConfigManager.current.storage?.managedExternal) return;
    // start services
    const startedPackages = application.servicePackagesStarted;

    await asyncArray(startedPackages.filter(pack => application.startOptions.services.includes(pack.name))).forEach(
        async pack => {
            if (pack.api.startService) {
                logger.info(() => `starting service ${pack.name}`);
                await pack.api.startService(application);
            }
        },
    );
}

export async function startApplication(application: Application): Promise<void> {
    logApplicationInfo(application);
    startUnhandledErrorMonitor();
    startContainerHeartbeatMonitor(application);
    startAppsHealthCheckMonitor(application);

    const graphqlHttpEndpoint = GraphqlEndpoint.create(application);

    await startPackageServices(application);

    await graphqlHttpEndpoint.start();
    if (requiresInteropService(application)) {
        await InteropEndpoint.create(application)?.start();
    }
    await MetricsEndpoint.create(application)?.start();

    application.setReady();
}
