import { NextFunction, Request, Response } from 'express';

export function nocache(req: Request, res: Response, next: NextFunction) {
    setNoCacheHeaders(res);
    next();
}

/**
 * Set a wide range of headers to prevent caching
 * @param res the response for which to set the headers
 * @see https://developer.mozilla.org/en-US/docs/Web/HTTP/Caching
 * @see https://github.com/helmetjs/nocache
 */
export function setNoCacheHeaders(res: Response) {
    res.setHeader('Cache-Control', 'no-store, no-cache, max-age=0, must-revalidate, proxy-revalidate');
    res.setHeader('Expires', '0');
    res.setHeader('Surrogate-Control', 'no-store');
}
