import { ConfigManager, fileExists, Logger } from '@sage/xtrem-core';
import { NewRelicConfig } from '@sage/xtrem-shared';
import { Response } from 'express';
import * as fs from 'fs/promises';
import * as handlebars from 'handlebars';
import { memoize } from 'lodash';
import * as fsp from 'path';
import { RequestWithNonce } from './middlewares/helmet-middleware';
import { setNoCacheHeaders } from './util';

const logger = Logger.getLogger(__filename, 'xtrem-ui-app');

const cachePeriod = 60 * 60 * 24 * 7; // 7 day for static resources, except the index.html file.

export interface TemplateConfig extends Partial<NewRelicConfig> {
    nonce?: string;
    version?: string;
    /**
     * The optional pendo key to use for the front-end app.
     */
    pendoApiKey?: string;
    preferences: {
        loadPendo: boolean;
        loadNewRelic: boolean;
    };
}

const compiledTemplateCache = new Map<string, (config: TemplateConfig) => string>();

const getIndexFileContent = async (
    indexPath: string,
    templateConfig: TemplateConfig,
    xtremStandaloneVersion = '',
    isTemplateCacheDisabled = false,
): Promise<string> => {
    const templateIndex = `${indexPath}_pendo=${templateConfig.preferences.loadPendo}_NR=${templateConfig.preferences.loadNewRelic}`;
    let compiledTemplate = compiledTemplateCache.get(templateIndex);
    if (!compiledTemplate || isTemplateCacheDisabled) {
        const rawIndexContent = await fs.readFile(indexPath, 'utf-8');
        compiledTemplate = handlebars.compile(rawIndexContent);
        compiledTemplateCache.set(templateIndex, compiledTemplate);
    }
    const html = compiledTemplate({
        ...templateConfig,
        version: xtremStandaloneVersion,
        pendoApiKey: ConfigManager.current.pendo?.apiKey || '23e3553b-ace0-4473-7d86-4385a4f77f78',
    });
    return html;
};

const getUiPackageJsonPath = memoize(
    ({ isProd = false, frontEndAppHeader }: { isProd: boolean; frontEndAppHeader?: string }) => {
        if (frontEndAppHeader) {
            if (/^@[a-z][a-z0-9-]*\/[a-z][a-z0-9-]*$/.test(frontEndAppHeader)) {
                try {
                    // Validate the package in the header to ensure that we don't get any injection attempts
                    const packagePath = require.resolve(`${frontEndAppHeader}/package.json`, {
                        paths: [process.cwd()],
                    });
                    if (packagePath) {
                        // eslint-disable-next-line import/no-dynamic-require, global-require
                        const packageContent = require(packagePath);
                        // Ensure that the requested package has the explicit flag for being a front-end app.
                        if (packageContent.xtrem?.isFrontEndApp) {
                            return packagePath;
                        }
                    }
                } catch (err) {
                    logger.warn(err);
                }
            }
            logger.warn(`Invalid front-end app header value: ${frontEndAppHeader}`);
        }

        if (isProd) {
            return require.resolve('@sage/xtrem-standalone/package.json');
        }

        return require.resolve('@sage/xtrem-ui/package.json');
    },
);

const getWebAppDir = memoize((packageJsonPath: string) => fsp.join(fsp.dirname(packageJsonPath), 'build'));

export const serveStaticXtremUi = (
    isProd = false,
    newRelicConfig?: NewRelicConfig,
    xtremStandaloneVersion?: string,
    isTemplateCacheDisabled = false,
) => {
    logger.info('Xtrem front-end app deployed.');

    return (req: RequestWithNonce, res: Response) => {
        const packageJsonPath = getUiPackageJsonPath({
            isProd,
            frontEndAppHeader: req.header('X-XTREM-FRONT-END-APP'),
        });
        let webAppDir = getWebAppDir(packageJsonPath);
        // dev-resources are copied over at build time
        // tests are found in local packages though
        // so the following is meant to take care of them.
        if (webAppDir.endsWith('src')) {
            webAppDir = webAppDir.replace('src', 'dev-resources');
        }

        const indexPath = fsp.join(webAppDir, 'index.html');

        // Just in case
        const preferences = getUserCookiePreference(res.locals?.context?.pref);
        if (req.path.indexOf('..') !== -1) {
            res.status(404).send();
            return;
        }
        const filePath = fsp.join(webAppDir, req.path);
        (async () => {
            if (filePath !== indexPath && (await fileExists(filePath)) && (await fs.stat(filePath)).isFile()) {
                // Static resources such as fonts are cached for a day, JS files are requested with a GET param
                if (isProd) {
                    res.set('Cache-control', `public, max-age=${cachePeriod}`);
                } else {
                    setNoCacheHeaders(res);
                }
                res.sendFile(filePath);
            } else {
                const nonce = req.nonce;
                const templateConfig = { nonce, ...newRelicConfig, preferences };
                res.type('html');
                setNoCacheHeaders(res); // The index file is never cached as it has references to the corresponding JS bundle hashes.
                res.send(
                    await getIndexFileContent(
                        indexPath,
                        templateConfig,
                        xtremStandaloneVersion,
                        isTemplateCacheDisabled,
                    ),
                );
            }
        })().catch(err => {
            logger.error(err);
            res.status(500).send();
        });
    };
};

// Check https://jira.sage.com/browse/XT-10838 for more info about OneTrust preferences
const getUserCookiePreference = (pref: number | undefined): TemplateConfig['preferences'] => {
    const preference = {
        loadPendo: false,
        loadNewRelic: false,
    };
    if (!pref) return preference;
    switch (pref) {
        case 1: // Functional
        case 5: // Functional + Marketing
            preference.loadPendo = true;
            return preference;
        // Functional + Performance
        case 3:
            preference.loadPendo = true;
            preference.loadNewRelic = true;
            return preference;
        // Functional + Performance + Marketing
        case 7:
            preference.loadPendo = true;
            preference.loadNewRelic = true;
            return preference;
        default:
            return preference;
    }
};
