{"name": "@sage/xtrem-service", "description": "Xtrem HTTP service", "version": "58.0.2", "author": "Sage", "license": "UNLICENSED", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/index.js", "typings": "build/package-definition.d.ts", "dependencies": {"@sage/xtrem-access-token": "^4.1.2", "@sage/xtrem-core": "workspace:*", "@sage/xtrem-graphiql": "workspace:*", "@sage/xtrem-metrics": "workspace:*", "@sage/xtrem-shared": "workspace:*", "@sage/xtrem-standalone": "workspace:*", "@sage/xtrem-ui": "workspace:*", "axios": "^1.11.0", "body-parser": "^2.0.0", "compression": "^1.7.1", "cookie-parser": "^1.4.7", "express": "^5.0.0", "handlebars": "^4.7.8", "helmet": "^8.0.0", "jsonwebtoken": "^9.0.0", "jwks-rsa": "^3.0.0", "lodash": "^4.17.21", "nanoid": "^3.3.8", "newrelic": "^12.25.0", "toobusy-js": "^0.5.1"}, "devDependencies": {"@sage/xtrem-dts-bundle": "workspace:*", "@sage/xtrem-minify": "workspace:*", "@types/body-parser": "^1.19.2", "@types/chai": "^4.3.6", "@types/chai-as-promised": "^7.1.6", "@types/compression": "1.8.1", "@types/cookie-parser": "^1.4.9", "@types/express": "^5.0.0", "@types/jsonwebtoken": "^9.0.0", "@types/lodash": "^4.14.198", "@types/mocha": "^10.0.1", "@types/node": "^22.10.2", "@types/sinon": "^17.0.0", "@types/supertest": "^6.0.0", "@types/ws": "^8.5.11", "c8": "^10.1.2", "chai": "^4.3.10", "chai-as-promised": "^7.1.1", "cross-env": "^7.0.3", "eslint": "^8.49.0", "mocha": "^10.8.2", "prettier": "^3.3.3", "sinon": "^21.0.0", "supertest": "^7.0.0", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "~5.8.3", "ws": "^8.18.0"}, "scripts": {"build": "tsc -b -v . && pnpm dts-bundle", "build:binary": "pnpm clean && pnpm build && xtrem-minify -c -z \"build/**/*.js\"", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build junit-report*", "dts-bundle": "xtrem-dts-bundle", "lint": "eslint -c .eslintrc.js --ext .ts lib test", "prettier:check": "prettier --list-different \"{lib,test}/**/*.ts\"", "prettier:write": "prettier --write \"{lib,test}/**/*.ts\"", "test": "mocha --recursive \"test/**/*@(-|.)test.ts\" --exit", "test:ci": "cross-env JUNIT_REPORT_PATH=junit-report-service.xml JUNIT_REPORT_NAME='xtrem-service' c8 --reporter=lcov --reporter=json --reporter=cobertura --reporter=text mocha --recursive --exit \"test/**/*@(-|.)test.ts\" --reporter mocha-jenkins-reporter", "test:ci:allDatabases": "pnpm test:ci"}, "c8": {"reporter": ["json", "lcov", "text-summary"], "extension": [".ts"], "exclude": ["**/*.d.ts", "**/*-test.ts"]}, "gitHead": "f0406ede1639145fdb322ebdf823bc87abddf4b8"}