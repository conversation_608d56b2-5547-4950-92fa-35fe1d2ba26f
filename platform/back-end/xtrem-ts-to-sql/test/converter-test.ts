/* eslint-disable func-names */
import { sourceMapSetup } from '@sage/xtrem-log';
import { Di<PERSON>, Lo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ggerInterface, ProfilerCallback } from '@sage/xtrem-shared';
import { assert } from 'chai';
import {
    BasicResolver,
    Converter,
    ConverterFactory,
    ConverterProperty,
    Dialect,
    GenericConversionResult,
    getColumnName,
    setLogger,
    sqlNumber,
} from '../lib';

sourceMapSetup();

interface TestFactory extends ConverterFactory {
    readonly properties: TestProperty[];
}

interface TestProperty extends ConverterProperty {
    readonly targetFactoryName?: string;
    readonly reverseReferenceName?: string;
}

type TestConversionResult = GenericConversionResult<TestFactory, TestProperty>;

class TestModel {
    constructor(readonly factories: TestFactory[]) {}

    findFactory(name: string): TestFactory {
        const factory = this.factories.find(f => f.name === name);
        if (!factory) throw new Error(`${name}: factory not found`);
        return factory;
    }

    // eslint-disable-next-line class-methods-use-this
    findProperty(factory: TestFactory, name: string): TestProperty {
        const property = factory.properties.find(p => p.name === name);
        if (!property) throw new Error(`${factory.name}.${name}: property not found`);
        return property;
    }

    resolveColumnName(parent: TestConversionResult, name: string): TestConversionResult {
        const parentFactory = parent.factory;
        const property = this.findProperty(parentFactory, name);
        const factory = property.targetFactoryName ? this.findFactory(property.targetFactoryName) : parentFactory;
        const resolved = {
            alias: parent.alias,
            path: `${parent.path}.${name}`,
            sql: `${parent.alias}.${getColumnName(name)}`,
            property,
            tableName: factory?.tableName,
            factory,
            reverseReferenceName: property.reverseReferenceName,
            type: property.type,
        };
        return resolved;
    }
}

const testModel = new TestModel([
    {
        name: 'Root',
        tableName: 'root_table',
        properties: [
            { name: 'name', type: 'string' },
            { name: '_id', type: 'integer' },
            { name: 'foo', type: 'string', isStored: true },
            { name: 'ref', type: 'reference', targetFactoryName: 'Ref', isStored: true },
            { name: 'nullableRef', type: 'reference', isNullable: true, targetFactoryName: 'Ref' },
            { name: 'b1', type: 'boolean' },
            { name: 'b2', type: 'boolean' },
            { name: 'i1', type: 'integer' },
            { name: 'p1', type: 'decimal' },
            { name: 'p2', type: 'decimal' },
            { name: 'p3', type: 'decimal' },
            { name: 'p4', type: 'decimal' },
            { name: 'p5', type: 'decimal' },
            { name: 'children', type: 'collection', targetFactoryName: 'Child', reverseReferenceName: 'parent' },
        ],
    },
    {
        name: 'Ref',
        tableName: 'ref_table',
        properties: [
            { name: '_id', type: 'integer' },
            { name: 'bar', type: 'string' },
            { name: 'ref2', type: 'reference', targetFactoryName: 'Ref2' },
        ],
    },
    {
        name: 'Ref2',
        tableName: 'ref_2_table',
        properties: [
            { name: 'ref3', type: 'reference', targetFactoryName: 'Ref3' },
            { name: 'b3', type: 'boolean' },
            { name: 'b4', type: 'boolean' },
            { name: 'v3', type: 'string' },
            { name: 'v4', type: 'string' },
        ],
    },
    {
        name: 'Ref3',
        tableName: 'ref_3_table',
        properties: [{ name: 'val', type: 'string' }],
    },
    {
        name: 'Child',
        tableName: 'child_table',
        properties: [
            { name: 'parent', type: 'reference', targetFactoryName: 'Root' },
            { name: 'name', type: 'string' },
            { name: 'ref', type: 'reference', targetFactoryName: 'Ref' },
        ],
    },
]);

class TestConverter extends Converter<any, TestFactory, TestProperty> {}

class TestResolver extends BasicResolver<any, TestFactory, TestProperty> {
    // eslint-disable-next-line class-methods-use-this
    override resolveColumnName(
        _cx: any,
        parent: TestConversionResult,
        name: string,
    ): GenericConversionResult<TestFactory, TestProperty> {
        return testModel.resolveColumnName(parent, name);
    }
}

function newTestConverter(dialect: Dialect = 'postgres') {
    return new TestConverter({}, testModel.factories[0], new TestResolver(), {
        dialect,
    });
}

function test(fn: () => any, expected: string, aliases = 'root_table AS t0') {
    const normalise = (s: string) => s.trim().replace(/\s+/g, ' ');
    const converter = newTestConverter();
    const result = converter.convertFunction(fn).sql;
    assert.equal(normalise(result), normalise(expected));
    if (aliases) assert.equal(normalise(converter.getTableAliases()), normalise(aliases));
    return converter;
}

function testError(fn: () => any, expected: RegExp) {
    assert.throws(() => newTestConverter().convertFunction(fn), expected);
}

// All these functions will never be invoked and are only required to make the compiler happy
const typesLib = {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    strictEq: (m1: any, m2: any) => true,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    strictNe: (m1: any, m2: any) => true,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    eq: (m1: any, m2: any) => true,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    ne: (m1: any, m2: any) => true,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    gt: (m1: any, m2: any) => true,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    gte: (m1: any, m2: any) => true,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    lt: (m1: any, m2: any) => true,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    lte: (m1: any, m2: any) => true,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    add: (m1: any, m2: any) => true,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    sub: (m1: any, m2: any) => true,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    mul: (m1: any, m2: any) => true,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    div: (m1: any, m2: any) => true,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    mod: (m1: any, m2: any) => true,
};

// Let's declare a runtime_1 namespace in order to mimic what the Typescript transpiler does
// when it builds packages.
namespace runtime_1 {
    export class Context {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        static getConfigurationValue(name: string): string {
            return '';
        }
    }
}

describe('Converter', () => {
    it('can convert literal', () => {
        test(() => {
            return 'hello';
        }, "'hello'");
    });
    it('can convert property access', () => {
        test(function (this: any) {
            return this.foo;
        }, 't0.foo');
    });

    it('can convert inline-condition', () => {
        test(function (this: any) {
            // eslint-disable-next-line prefer-template
            return this.foo + 'abc';
        }, "CONCAT(t0.foo,'abc')");
    });
    it('can convert inline-condition with numbers', () => {
        test(function (this: any) {
            return this.p1 + this.p2;
        }, '(t0.p_1 + t0.p_2)');
    });
    it('can convert inline-condition with number and string mix', () => {
        test(function (this: any) {
            // eslint-disable-next-line prefer-template
            return this.p1 + '~' + this.name + '~' + this.p2;
        }, "CONCAT(CONCAT(CONCAT(CONCAT(t0.p_1,'~'),t0.\"name\"),'~'),t0.p_2)");
    });
    describe('can manage objects', () => {
        it('simple object', () => {
            test(() => {
                return { a: 'aaa', b: 'bbb' };
            }, `"a" = 'aaa',b = 'bbb'`);
        });
        it("object with ref to 'this'", () => {
            test(function (this: any) {
                return { a: this.foo, b: 'bbb' };
            }, `"a" = t0.foo,b = 'bbb'`);
        });
    });
    describe("can manage 'Where clauses'", () => {
        it('simple clause with a string', () => {
            test(function (this: any) {
                return this.name === 'abc';
            }, `(t0."name" = 'abc')`);
        });
        it('simple clause with an integer', () => {
            test(function (this: any) {
                return this.foo === 24;
            }, '(t0.foo = 24::TEXT)');
        });
        it('clause with AND', () => {
            test(function (this: any) {
                return this.name === 'abc' && this.name !== 'bcd';
            }, `(t0."name" = 'abc') AND (t0."name" != 'bcd')`);
        });
    });
    describe('can manage typesLib functions', () => {
        it('strictEq', () => {
            test(function (this: any) {
                return typesLib.strictEq(this.name, 'abc');
            }, `(t0."name" = 'abc')`);
        });
        it('eq', () => {
            test(function (this: any) {
                return typesLib.eq(this.name, null);
            }, '(t0."name" IS NULL)');
        });
        it('strictNe', () => {
            test(function (this: any) {
                return typesLib.strictNe(this.name, 'abc');
            }, `(t0."name" != 'abc')`);
        });
        it('strictNe returning true / false', () => {
            test(function (this: any) {
                if (typesLib.strictNe(this.name, 'abc')) return false;
                return true;
            }, `(CASE WHEN (t0."name" != 'abc') THEN false ELSE true END)`);
        });
        it('strictNe returning !0 / !1', () => {
            test(function (this: any) {
                if (typesLib.strictNe(this.name, 'abc')) return !1;
                return !0;
            }, `(CASE WHEN (t0."name" != 'abc') THEN false ELSE true END)`);
        });
        it('ne', () => {
            test(function (this: any) {
                return typesLib.ne(this.name, null);
            }, '(t0."name" IS NOT NULL)');
        });
        it('lt', () => {
            test(function (this: any) {
                return typesLib.lt(this.name, 'abc');
            }, `(t0."name" < 'abc')`);
        });
        it('lte', () => {
            test(function (this: any) {
                return typesLib.lte(this.name, 'abc');
            }, `(t0."name" <= 'abc')`);
        });
        it('gt', () => {
            test(function (this: any) {
                return typesLib.gt(this.name, 'abc');
            }, `(t0."name" > 'abc')`);
        });
        it('gte', () => {
            test(function (this: any) {
                return typesLib.gte(this.name, 'abc');
            }, `(t0."name" >= 'abc')`);
        });
        it('add strings', () => {
            test(function (this: any) {
                return typesLib.add(this.name, 'abc');
            }, `CONCAT(t0."name",'abc')`);
        });
        it('add numbers', () => {
            test(function (this: any) {
                return typesLib.add(this.i1, 6);
            }, '(t0.i_1 + 6)');
            test(function (this: any) {
                return typesLib.add(this.p2, 6);
            }, '(t0.p_2 + 6::NUMERIC)');
        });
        it('sub', () => {
            test(function (this: any) {
                return typesLib.sub(this.i1, 2);
            }, '(t0.i_1 - 2)');
            test(function (this: any) {
                return typesLib.sub(this.p2, 6);
            }, '(t0.p_2 - 6::NUMERIC)');
        });
        it('mul', () => {
            test(function (this: any) {
                return typesLib.mul(this.i1, 2);
            }, '(t0.i_1 * 2)');
            test(function (this: any) {
                return typesLib.mul(this.p2, 2);
            }, '(t0.p_2 * 2::NUMERIC)');
        });
        it('div', () => {
            test(function (this: any) {
                return typesLib.div(this.i1, 2);
            }, '(t0.i_1 / 2)');
            test(function (this: any) {
                return typesLib.div(this.p2, 2);
            }, '(t0.p_2 / 2::NUMERIC)');
        });
        it('mod', () => {
            testError(function (this: any) {
                return typesLib.mod(this.name, 2); // testing that an error is thrown if trying an expression that is not managed by TypesLib
            }, /typesLib.mod: Unsupported typesLib function/);
        });
    });
    it('can convert property access (non nullable)', () => {
        test(
            function (this: any) {
                return this.ref.bar;
            },
            't1.bar',
            'root_table AS t0 INNER JOIN ref_table AS t1 ON t0."ref"=t1._id AND t0._tenant_id=t1._tenant_id',
        );
    });
    it('can convert property access (nullable)', () => {
        test(
            function (this: any) {
                return this.nullableRef.bar;
            },
            't1.bar',
            'root_table AS t0 LEFT JOIN ref_table AS t1 ON t0.nullable_ref=t1._id AND t0._tenant_id=t1._tenant_id',
        );
    });
    it('can convert optional chaining (nullable)', () => {
        test(
            function (this: any) {
                return this.nullableRef?.bar;
            },
            't1.bar',
            'root_table AS t0 LEFT JOIN ref_table AS t1 ON t0.nullable_ref=t1._id AND t0._tenant_id=t1._tenant_id',
        );
    });
    it('can convert null coalescing', () => {
        test(
            function (this: any) {
                return this.foo ?? 'bar';
            },
            "(CASE WHEN t0.foo IS NULL THEN 'bar' ELSE t0.foo END)",
            'root_table AS t0',
        );
    });
    it('can convert non-null assertion operator', () => {
        test(
            function (this: any) {
                return this.ref.bar!;
            },
            't1.bar',
            'root_table AS t0 INNER JOIN ref_table AS t1 ON t0."ref"=t1._id AND t0._tenant_id=t1._tenant_id',
        );
        testError(function (this: any) {
            return this.ref.bar !== null;
        }, /=== and !== {2}operators cannot be used with null \(use == or != instead\)/);
    });
    it('can convert deep property access', () => {
        test(
            function (this: any) {
                return this.ref.ref2.ref3.val;
            },
            't3.val',
            'root_table AS t0 INNER JOIN ref_table AS t1 ON t0."ref"=t1._id AND t0._tenant_id=t1._tenant_id INNER JOIN ref_2_table AS t2 ON t1.ref_2=t2._id AND t1._tenant_id=t2._tenant_id INNER JOIN ref_3_table AS t3 ON t2.ref_3=t3._id AND t2._tenant_id=t3._tenant_id',
        );
    });
    it('does not shortcut this._id', () => {
        test(function (this: any) {
            return this._id;
        }, 't0._id');
    });
    it('self referencing', () => {
        test(function (this: any) {
            return this;
        }, 't0._id');
    });
    it('can shortcut _id', () => {
        test(function (this: any) {
            return this.ref._id;
        }, 't0."ref"');
    });
    it('can convert string template', () => {
        test(function (this: any) {
            return `before ${this.foo} after`;
        }, "CONCAT('before ',t0.foo,' after')");
    });
    it('can convert arithmetic operations', () => {
        test(function (this: any) {
            return this.p1 + this.p2 - (this.p3 * this.p4) / this.p5;
        }, '((t0.p_1 + t0.p_2) - ((t0.p_3 * t0.p_4) / t0.p_5))');
    });
    it('can convert Math.floor', () => {
        test(function (this: any) {
            return Math.floor(this.p1);
        }, 'FLOOR(t0.p_1)');
    });
    it('can convert Math.abs', () => {
        test(function (this: any) {
            return Math.abs(this.p1);
        }, 'ABS(t0.p_1)');
    });
    it('can convert Math.round', () => {
        test(function (this: any) {
            return Math.round(this.p1);
        }, 'ROUND(t0.p_1)');
    });

    it('throws an error because it cant convert this fn', () => {
        testError(function (this: any) {
            return Math.sqrt(this.p1);
        }, /Unsupported Math function/);
    });

    it('can convert Math.max', () => {
        test(function (this: any) {
            return Math.max(this.p1, this.p2);
        }, 'GREATEST(t0.p_1,t0.p_2)');
        test(function (this: any) {
            return -Math.max(this.p1, this.p2);
        }, '(- GREATEST(t0.p_1,t0.p_2)::NUMERIC)');
        test(function (this: any) {
            return Math.max(this.p1, this.p2, this.p3);
        }, 'GREATEST(t0.p_1,t0.p_2,t0.p_3)');
    });
    it('can convert Math.min', () => {
        test(function (this: any) {
            return Math.min(this.p1, this.p2);
        }, 'LEAST(t0.p_1,t0.p_2)');
        test(function (this: any) {
            return Math.min(this.p1, this.p2, this.p3);
        }, 'LEAST(t0.p_1,t0.p_2,t0.p_3)');
    });

    it('can convert comparison operators', () => {
        test(function (this: any) {
            return this.p1 < this.p2;
        }, '(t0.p_1 < t0.p_2)');
        test(function (this: any) {
            return this.p1 <= this.p2;
        }, '(t0.p_1 <= t0.p_2)');
        test(function (this: any) {
            return this.p1 > this.p2;
        }, '(t0.p_1 > t0.p_2)');
        test(function (this: any) {
            return this.p1 >= this.p2;
        }, '(t0.p_1 >= t0.p_2)');
    });
    it('can convert equality with null', () => {
        test(function (this: any) {
            return this.p1 == null;
        }, '(t0.p_1 IS NULL)');
        test(function (this: any) {
            return this.p1 != null;
        }, '(t0.p_1 IS NOT NULL)');
        testError(function (this: any) {
            // eslint-disable-next-line eqeqeq
            return this.p1 == 'Literal';
        }, /== and != {2}operations can only be used with null/);
    });
    it('can convert equality', () => {
        test(function (this: any) {
            return this.p1 === this.p2;
        }, '(t0.p_1 = t0.p_2)');
        test(function (this: any) {
            return this.p1 !== this.p2;
        }, '(t0.p_1 != t0.p_2)');
    });
    it('can convert logical expressions', () => {
        test(function (this: any) {
            return this.b1 && this.b2;
        }, 't0.b_1 AND t0.b_2');
        test(function (this: any) {
            return this.b1 || this.b2;
        }, '(t0.b_1 OR t0.b_2)');
        testError(function (this: any) {
            return this.p1 % this.p2;
        }, /cannot convert operator: %/);
    });
    it('can convert deep and', () => {
        test(
            function (this: any) {
                return this.ref.ref2.b3 && this.ref.ref2.b4 && this.foo === 'bar';
            },
            "t2.b_3 AND t2.b_4 AND (t0.foo = 'bar')",
            'root_table AS t0 INNER JOIN ref_table AS t1 ON t0."ref"=t1._id AND t0._tenant_id=t1._tenant_id INNER JOIN ref_2_table AS t2 ON t1.ref_2=t2._id AND t1._tenant_id=t2._tenant_id',
        );
    });
    it('can convert deep or (on non nullable references)', () => {
        test(
            function (this: any) {
                return this.ref.ref2.b3 || this.ref.ref2.b4 || this.foo === 'bar';
            },
            "((t2.b_3 OR t2.b_4) OR (t0.foo = 'bar'))",
            'root_table AS t0 INNER JOIN ref_table AS t1 ON t0."ref"=t1._id AND t0._tenant_id=t1._tenant_id INNER JOIN ref_2_table AS t2 ON t1.ref_2=t2._id AND t1._tenant_id=t2._tenant_id',
        );
    });
    it('can convert deep or (mix of nullable and non nullable references)', () => {
        test(
            function (this: any) {
                return this.nullableRef.ref2.b3 || this.nullableRef.ref2.b4 || this.foo === 'bar';
            },
            "((t2.b_3 OR t2.b_4) OR (t0.foo = 'bar'))",
            'root_table AS t0 LEFT JOIN ref_table AS t1 ON t0.nullable_ref=t1._id AND t0._tenant_id=t1._tenant_id LEFT JOIN ref_2_table AS t2 ON t1.ref_2=t2._id AND t1._tenant_id=t2._tenant_id',
        );
    });
    it('can convert unary expressions', () => {
        test(function (this: any) {
            return !this.p1;
        }, '(NOT t0.p_1)');
        test(function (this: any) {
            return -this.p1;
        }, '(- t0.p_1::NUMERIC)');
        testError(function (this: any) {
            return +this.p1;
        }, /Unsupported unary operator: \+/);
    });
    it('can convert call expressions', () => {
        test(function (this: any) {
            return [2, 3].includes(this.p1);
        }, '(t0.p_1 IN (2,3))');
        testError(function (this: any) {
            return +this.p1;
        }, /Unsupported unary operator: \+/);
    });
    it('can convert a conditional ternary operator', () => {
        // The test below, although the output is incorrect, as we casting the string to a number
        // it is a valid test and the converter did what was expected, the SQL statement would fail.
        test(function (this: any) {
            return this.p1 === 'yes' ? this.p2 : this.p3;
        }, "(CASE WHEN (t0.p_1::TEXT = 'yes') THEN t0.p_2 ELSE t0.p_3 END)");
        test(function (this: any) {
            return this.b1 && this.b2 ? this.p1 / this.p2 : 0;
        }, '(CASE WHEN t0.b_1 AND t0.b_2 THEN (t0.p_1 / t0.p_2) ELSE 0::NUMERIC END)');
    });

    it('should convert an arrow function', () => {
        test(() => {
            return 'hello';
        }, "'hello'");
    });

    it('should convert a shorthand arrow function', () => {
        test(() => 'hello', "'hello'");
    });

    it('should throw an error', () => {
        // cannot convert throw to SQL, expect an error
        testError(() => {
            throw new Error('Error');
        }, /expected return statement, got ThrowStatement/);
    });

    it('can convert Context.getConfigurationValue', () => {
        test(
            () => runtime_1.Context.getConfigurationValue('serviceOptionsLevel'),
            `'${runtime_1.Context.getConfigurationValue('serviceOptionsLevel')}'`,
        );
    });

    it('should test sqlNumber function', () => {
        const slot = { factory: { name: 'factory' }, property: { name: 'property' } } as TestConversionResult;

        assert.equal(sqlNumber(slot, 12), '12');
        assert.equal(sqlNumber(slot, '12.1'), '12.1');
        assert.throws(() => sqlNumber(slot, 1 / 0), "factory.property: invalid numeric value: 'Infinity'");
    });

    it('enters withThisResultScope fn', () => {
        const converter = test(
            function (this: any) {
                return this.ref.bar;
            },
            't1.bar',
            'root_table AS t0 INNER JOIN ref_table AS t1 ON t0."ref"=t1._id AND t0._tenant_id=t1._tenant_id',
        );

        const result = { factory: { name: 'factory' }, property: { name: 'property' } } as TestConversionResult;

        assert.equal(
            converter.withThisResultScope(result, () => 'any'),
            'any',
        );
    });

    it('enters setLogger', () => {
        const nopProfilerCallback: ProfilerCallback = {
            success(): void {},
            fail(): void {},
        };

        const nopLogger: LoggerInterface = {
            log(): ProfilerCallback {
                return nopProfilerCallback;
            },
            info(): ProfilerCallback {
                return nopProfilerCallback;
            },
            warn(): ProfilerCallback {
                return nopProfilerCallback;
            },
            verbose(): ProfilerCallback {
                return nopProfilerCallback;
            },
            debug(): ProfilerCallback {
                return nopProfilerCallback;
            },
            error(): ProfilerCallback {
                return nopProfilerCallback;
            },
            do<T>(fn: () => T, onError: LoggerErrorHandler<T>): T {
                try {
                    return fn();
                } catch (err) {
                    this.error(err.stack);
                    return onError(err);
                }
            },
        };
        const logger = nopLogger;
        setLogger(logger);
    });

    it('tests convertMissingParameter fn', () => {
        test(
            function (this: any) {
                return this.ref.bar;
            },
            't1.bar',
            'root_table AS t0 INNER JOIN ref_table AS t1 ON t0."ref"=t1._id AND t0._tenant_id=t1._tenant_id',
        );
        const missing = 0;

        testError(() => {
            return missing;
        }, /'missing': variable not found/);
    });

    it('enters the or fn', () => {
        const tab1: (string | undefined)[] = ['var', 'var2', 'var3'];
        const tab2: (string | undefined)[] = [];
        const res = Converter.or(tab1);
        const res2 = Converter.or(tab2);
        assert.equal(res, '(var OR var2 OR var3)'); // when tab is not empty the returned String value is "(Var1 OR var2 OR var3)" for n number of vars. In this test n=3
        assert.equal(res2, '(1=2)'); // when tab is empty the returned string value is "(FALSE)"
    });

    it('can convert collection.every', () => {
        test(
            function (this: any) {
                return this.children.every((child: { name: string }) => child.name === 'john');
            },
            `((SELECT COUNT(*) FROM child_table AS t1  WHERE t0._id=t1.parent AND t0._tenant_id=t1._tenant_id AND NOT((t1."name" = 'john'))) = 0)`,
            'root_table AS t0',
        );
    });

    it('can convert collection.some', () => {
        test(
            function (this: any) {
                return this.children.some((child: { name: string }) => child.name === 'john');
            },
            `((SELECT COUNT(*) FROM child_table AS t1  WHERE t0._id=t1.parent AND t0._tenant_id=t1._tenant_id AND (t1."name" = 'john')) >= 1)`,
            'root_table AS t0',
        );
    });

    it('can convert collection.takeOne', () => {
        test(
            function (this: any) {
                return this.children.takeOne((child: { name: string }) => child.name === 'john').ref.bar;
            },
            't2.bar',
            `root_table AS t0
            LEFT JOIN
                LATERAL (SELECT t1.* FROM child_table AS t1
                    WHERE (t1."name" = 'john')  AND t0._id=t1.parent AND t0._tenant_id=t1._tenant_id ORDER BY t1._tenant_id, t1._id ASC LIMIT 1 )
                AS t1 ON TRUE
            INNER JOIN ref_table AS t2 ON t1."ref"=t2._id AND t1._tenant_id=t2._tenant_id`,
        );
    });

    it('can convert trim and length functions', () => {
        test(
            () => {
                return ' hello '.trim().length > 0;
            },
            "(LENGTH(TRIM(' hello ')) > 0)",
            'root_table AS t0',
        );
    });

    it('succeeds if query depth does not exceed limit', () => {
        test(
            function (this: any) {
                // This is a silly example, but it's just to test the depth limit
                return this.children.some((child1: any) =>
                    child1.parent.children.some((child2: any) => child2.name === 'john'),
                );
            },
            `((SELECT COUNT(*) FROM child_table AS t1 INNER JOIN root_table AS t2 ON t1.parent=t2._id AND t1._tenant_id=t2._tenant_id WHERE t0._id=t1.parent AND t0._tenant_id=t1._tenant_id AND ((SELECT COUNT(*) FROM child_table AS t3  WHERE t2._id=t3.parent AND t2._tenant_id=t3._tenant_id AND (t3."name" = 'john')) >= 1)) >= 1)`,
            'root_table AS t0',
        );
    });

    it('fails if query depth exceeds limit', () => {
        testError(function (this: any) {
            // This is a silly example, but it's just to test the depth limit
            return this.children.some((child1: any) =>
                child1.parent.children.every((child2: any) =>
                    child2.parent.children.some((child3: any) =>
                        child3.parent.children.some((child4: any) => child4.name === 'john'),
                    ),
                ),
            );
        }, /max sub query depth \(3\) exceeded/);
    });

    it(`can convert regex`, () => {
        const expected: Dict<string> = {
            postgres: "ARRAY_LENGTH(REGEXP_MATCH(t0.text::TEXT, '^.*tring.*$', 'i'), 1) > 0",
            oracle: "REGEXP_LIKE(TO_CHAR(t0.text),'^.*tring.*$','i')",
            sqlServer: "CONVERT(VARCHAR(max),t0.text) COLLATE Latin1_General_CI_AS LIKE '%tring%' escape '$'",
        };
        Object.keys(expected).forEach(dialect => {
            const converter = newTestConverter(dialect as Dialect);
            const result = converter.regexConverter.convertRegex('^.*tring.*$', 'i', {
                sql: 't0.text',
                type: 'string',
            }).sql;

            assert.equal(result, expected[dialect], `cannot convert regex for dialect: ${dialect}`);
        });
    });

    it(`can convert regex with argument`, () => {
        const expected: Dict<string> = {
            postgres: 'ARRAY_LENGTH(REGEXP_MATCH(t0.text::TEXT, $1, $2), 1) > 0',
            oracle: 'REGEXP_LIKE(TO_CHAR(t0.text),$1,$2)',
            sqlServer: "CONVERT(VARCHAR(max),t0.text) COLLATE Latin1_General_CI_AS LIKE $1 escape '$'",
        };
        Object.keys(expected).forEach(dialect => {
            const converter = newTestConverter(dialect as Dialect);
            const result = converter.regexConverter.convertRegex(
                '$1',
                '$2',
                {
                    sql: 't0.text',
                    type: 'string',
                },
                {
                    isParam: true,
                },
            ).sql;

            assert.equal(result, expected[dialect], `cannot convert regex for dialect: ${dialect}`);
        });
    });

    it(`can convert to char`, () => {
        const expected: Dict<string> = {
            postgres: "TO_CHAR(t0.date,'YYYY-MM-DD')",
            oracle: "TO_CHAR(t0.date,'YYYY-MM-DD')",
            sqlServer: 'CONVERT(VARCHAR,t0.date,23)',
        };
        Object.keys(expected).forEach(dialect => {
            const converter = newTestConverter(dialect as Dialect);
            const result = converter.stringConverter.convertToChar({ sql: 't0.date', type: 'date' });

            assert.equal(result, expected[dialect], `cannot convert to char dialect: ${dialect}`);
        });
    });

    it(`can convert to char to date`, () => {
        const expected: Dict<string> = {
            postgres: "TO_DATE('2023-10-10','YYYY-MM-DD')",
            oracle: "TO_DATE('2023-10-10','YYYY-MM-DD')",
            sqlServer: "CONVERT(DATE,'2023-10-10',20)",
        };
        Object.keys(expected).forEach(dialect => {
            const converter = newTestConverter(dialect as Dialect);
            const result = converter.dateConverter.convertToDate("'2023-10-10'");

            assert.equal(result, expected[dialect], `cannot convert to date dialect: ${dialect}`);
        });
    });

    it(`can convert to char to datetime/timstamp`, () => {
        const expected: Dict<string> = {
            postgres: "TO_TIMESTAMP('2023-10-10 14:30:00','YYYY-MM-DD HH24:MI:SS') AT TIME ZONE 'UTC'",
            oracle: `TO_TIMESTAMP('2023-10-10 14:30:00','YYYY-MM-DD"T"HH24:MI:SS.ff3"Z"')`,
            sqlServer: "CONVERT(DATETIME,'2023-10-10 14:30:00',127)",
        };
        Object.keys(expected).forEach(dialect => {
            const converter = newTestConverter(dialect as Dialect);
            const result = converter.dateConverter.convertToDate("'2023-10-10 14:30:00'", true);

            assert.equal(result, expected[dialect], `cannot convert to date dialect: ${dialect}`);
        });
    });
});
