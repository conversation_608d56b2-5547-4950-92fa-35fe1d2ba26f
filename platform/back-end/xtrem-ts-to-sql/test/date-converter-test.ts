/* eslint-disable func-names */
import { sourceMapSetup } from '@sage/xtrem-log';
import { assert } from 'chai';
import { snakeCase } from 'lodash';
import {
    BasicResolver,
    Converter,
    ConverterFactory,
    ConverterProperty,
    Dialect,
    GenericConversionResult,
    getColumnName,
} from '../lib';
import { DateValue } from './fixtures/fake-date';

sourceMapSetup();

interface TestFactory extends ConverterFactory {
    readonly properties: TestProperty[];
}

interface TestProperty extends ConverterProperty {
    readonly targetFactoryName?: string;
    readonly reverseReferenceName?: string;
}

type TestConversionResult = GenericConversionResult<TestFactory, TestProperty>;

class TestModel {
    constructor(readonly factories: TestFactory[]) {}

    findFactory(name: string): TestFactory {
        const factory = this.factories.find(f => f.name === name);
        if (!factory) throw new Error(`${name}: factory not found`);
        return factory;
    }

    // eslint-disable-next-line class-methods-use-this
    findProperty(factory: TestFactory, name: string): TestProperty {
        const property = factory.properties.find(p => p.name === name);
        if (!property) throw new Error(`${factory.name}.${name}: property not found`);
        return property;
    }

    resolveColumnName(parent: TestConversionResult, name: string): TestConversionResult {
        const parentFactory = parent.factory;
        const property = this.findProperty(parentFactory, name);
        const factory = property.targetFactoryName ? this.findFactory(property.targetFactoryName) : parentFactory;
        const resolved = {
            alias: parent.alias,
            path: `${parent.path}.${name}`,
            sql: `${parent.alias}.${getColumnName(name)}`,
            property,
            tableName: factory?.tableName,
            factory,
            reverseReferenceName: property.reverseReferenceName,
            type: property.type,
        };
        return resolved;
    }
}

const testModel = new TestModel([
    {
        name: 'Root',
        tableName: 'root_table',
        properties: [
            { name: 'name', type: 'string' },
            { name: '_id', type: 'integer' },
            { name: 'date', type: 'date', isStored: true },
        ],
    },
]);

class DateTestConverter extends Converter<any, TestFactory, TestProperty> {
    constructor(context: any, rootFactory: TestFactory, dialect: Dialect) {
        const resolver = new TestResolver();
        super(
            context,
            rootFactory,
            {
                resolveColumnName: (cx, parent, propertyName) => TestResolver.resolve(this, cx, parent, propertyName),
                resolveTableName: factory => resolver.resolveTableName(factory),
                resolveLiteral: value => resolver.resolveLiteral(value),
            },
            { dialect },
        );
    }
}

class TestResolver extends BasicResolver<any, TestFactory, TestProperty> {
    static makeColumnAlias(sql: string): string {
        return sql
            .split('._')
            .map(s => snakeCase(s))
            .join('__');
    }

    static resolveDate(
        sqlConverter: Converter<any, TestFactory, TestProperty>,
        result: TestConversionResult,
        name: string,
    ): TestConversionResult {
        const resolved = sqlConverter.dateConverter.convertProperty(result, name);
        return {
            ...(resolved as TestConversionResult),
            columnAlias: this.makeColumnAlias(resolved.sql),
        };
    }

    static resolve(
        sqlConverter: DateTestConverter,
        _cx: any,
        parent: TestConversionResult,
        name: string,
    ): GenericConversionResult<TestFactory, TestProperty> {
        if (parent.property?.type === 'date') return TestResolver.resolveDate(sqlConverter, parent, name);

        return testModel.resolveColumnName(parent, name);
    }
}

function newTestConverter(dialect: Dialect) {
    return new DateTestConverter({}, testModel.factories[0], dialect);
}

function test(dialect: Dialect, fn: () => any, expected: Record<Dialect, string>) {
    const aliases = 'root_table AS t0';
    const converter = newTestConverter(dialect);
    const result = converter.convertFunction(fn).sql;
    assert.equal(result, expected[dialect]);
    if (aliases) assert.equal(converter.getTableAliases().trim(), aliases);
    return converter;
}

function testError(dialect: Dialect, fn: () => any, expected: RegExp) {
    assert.throws(() => newTestConverter(dialect).convertFunction(fn), expected);
}

// All these functions will never be invoked and are only required to make the compiler happy
const typesLib = {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    strictEq: (m1: any, m2: any) => true,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    strictNe: (m1: any, m2: any) => true,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    eq: (m1: any, m2: any) => true,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    ne: (m1: any, m2: any) => true,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    gt: (m1: any, m2: any) => true,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    gte: (m1: any, m2: any) => true,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    lt: (m1: any, m2: any) => true,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    lte: (m1: any, m2: any) => true,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    add: (m1: any, m2: any) => true,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    sub: (m1: any, m2: any) => true,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    mul: (m1: any, m2: any) => true,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    div: (m1: any, m2: any) => true,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    mod: (m1: any, m2: any) => true,
};

describe('Date Converter', () => {
    (['postgres', 'sqlServer', 'oracle'] as Dialect[]).forEach(dialect => {
        const date = DateValue;

        it(`${dialect}:can convert date.today()`, () => {
            test(
                dialect,
                () => {
                    return DateValue.today();
                },
                { postgres: 'CURRENT_DATE', sqlServer: 'GETDATE()', oracle: 'SYSDATE' },
            );

            test(
                dialect,
                () => {
                    return date.today();
                },
                { postgres: 'CURRENT_DATE', sqlServer: 'GETDATE()', oracle: 'SYSDATE' },
            );
        });

        it(`${dialect}: simple clause with a date`, () => {
            test(
                dialect,
                function (this: any) {
                    return this.date === date.today();
                },
                {
                    postgres: '(t0."date" = CURRENT_DATE)',
                    sqlServer: '(t0."date" = GETDATE())',
                    oracle: '(t0."date" = SYSDATE)',
                },
            );
        });

        it(`${dialect}: strictEq`, () => {
            test(
                dialect,
                function (this: any) {
                    return typesLib.strictEq(this.date, date.today());
                },
                {
                    postgres: '(t0."date" = CURRENT_DATE)',
                    sqlServer: '(t0."date" = GETDATE())',
                    oracle: '(t0."date" = SYSDATE)',
                },
            );
        });

        it(`${dialect}: eq`, () => {
            test(
                dialect,
                function (this: any) {
                    return typesLib.eq(this.date, null);
                },
                {
                    postgres: '(t0."date" IS NULL)',
                    sqlServer: '(t0."date" IS NULL)',
                    oracle: '(t0."date" IS NULL)',
                },
            );
        });

        it(`${dialect}: strictNe`, () => {
            test(
                dialect,
                function (this: any) {
                    return typesLib.strictNe(this.date, date.today());
                },
                {
                    postgres: '(t0."date" != CURRENT_DATE)',
                    sqlServer: '(t0."date" != GETDATE())',
                    oracle: '(t0."date" != SYSDATE)',
                },
            );
        });

        it(`${dialect}: ne`, () => {
            test(
                dialect,
                function (this: any) {
                    return typesLib.ne(this.date, null);
                },
                {
                    postgres: '(t0."date" IS NOT NULL)',
                    sqlServer: '(t0."date" IS NOT NULL)',
                    oracle: '(t0."date" IS NOT NULL)',
                },
            );
        });

        it(`${dialect}: lt`, () => {
            test(
                dialect,
                function (this: any) {
                    return typesLib.lt(this.date, date.today());
                },
                {
                    postgres: '(t0."date" < CURRENT_DATE)',
                    sqlServer: '(t0."date" < GETDATE())',
                    oracle: '(t0."date" < SYSDATE)',
                },
            );
        });

        it(`${dialect}: epoch`, () => {
            test(
                dialect,
                function (this: any) {
                    return this.date.epoch === date.today().epoch;
                },
                {
                    postgres: "(date_part('epoch', t0.\"date\") = date_part('epoch', CURRENT_DATE))",
                    sqlServer:
                        "(DATEDIFF(SECOND, '1970-01-01', t0.\"date\") = DATEDIFF(SECOND, '1970-01-01', GETDATE()))",
                    oracle: "(((t0.\"date\" - TO_DATE('1970-01-01','YYYY-MM-DD')) * 86400) = ((SYSDATE - TO_DATE('1970-01-01','YYYY-MM-DD')) * 86400))",
                },
            );
        });

        it(`${dialect}: year`, () => {
            test(
                dialect,
                function (this: any) {
                    return this.date.year === date.today().year;
                },
                {
                    postgres: "(date_part('year', t0.\"date\") = date_part('year', CURRENT_DATE))",
                    sqlServer: '(DATEPART(yy FROM t0."date") = DATEPART(yy FROM GETDATE()))',
                    oracle: '(EXTRACT(YEAR FROM t0."date") = EXTRACT(YEAR FROM SYSDATE))',
                },
            );
        });

        it(`${dialect}: month`, () => {
            test(
                dialect,
                function (this: any) {
                    return this.date.month === date.today().month;
                },
                {
                    postgres: "(date_part('month', t0.\"date\") = date_part('month', CURRENT_DATE))",
                    sqlServer: '(DATEPART(mm FROM t0."date") = DATEPART(mm FROM GETDATE()))',
                    oracle: '(EXTRACT(MONTH FROM t0."date") = EXTRACT(MONTH FROM SYSDATE))',
                },
            );
        });

        it(`${dialect}: day`, () => {
            test(
                dialect,
                function (this: any) {
                    return this.date.day === date.today().day;
                },
                {
                    postgres: "(date_part('day', t0.\"date\") = date_part('day', CURRENT_DATE))",
                    sqlServer: '(DATEPART(dd FROM t0."date") = DATEPART(dd FROM GETDATE()))',
                    oracle: '(EXTRACT(DAY FROM t0."date") = EXTRACT(DAY FROM SYSDATE))',
                },
            );
        });

        it(`${dialect}: week`, () => {
            test(
                dialect,
                function (this: any) {
                    return this.date.week === date.today().week;
                },
                {
                    postgres: "(date_part('week', t0.\"date\") = date_part('week', CURRENT_DATE))",
                    sqlServer: '(DATEPART(isowk FROM t0."date") = DATEPART(isowk FROM GETDATE()))',
                    oracle: "(TO_NUMBER(TO_CHAR(t0.\"date\", 'WW')) = TO_NUMBER(TO_CHAR(SYSDATE, 'WW')))",
                },
            );
        });

        it(`${dialect}: weekDay`, () => {
            test(
                dialect,
                function (this: any) {
                    return this.date.weekDay === date.today().weekDay;
                },
                {
                    postgres: "(date_part('dow', t0.\"date\") = date_part('dow', CURRENT_DATE))",
                    sqlServer:
                        '(((DATEPART(dw FROM t0."date") + @@DATEFIRST + 6) % 7 ) = ((DATEPART(dw FROM GETDATE()) + @@DATEFIRST + 6) % 7 ))',
                    oracle: "((CASE TO_CHAR(t0.\"date\",'fmDY','nls_date_language=English') WHEN 'SUN' THEN 0 WHEN 'MON' THEN 1 WHEN 'TUE' THEN 2 WHEN 'WED' THEN 3 WHEN 'THU' THEN 4 WHEN 'FRI' THEN 5 WHEN 'SAT' THEN 6 END) = (CASE TO_CHAR(SYSDATE,'fmDY','nls_date_language=English') WHEN 'SUN' THEN 0 WHEN 'MON' THEN 1 WHEN 'TUE' THEN 2 WHEN 'WED' THEN 3 WHEN 'THU' THEN 4 WHEN 'FRI' THEN 5 WHEN 'SAT' THEN 6 END))",
                },
            );
        });

        it(`${dialect}: yearDay`, () => {
            test(
                dialect,
                function (this: any) {
                    return this.date.yearDay === date.today().yearDay;
                },
                {
                    postgres: "(date_part('doy', t0.\"date\") = date_part('doy', CURRENT_DATE))",
                    sqlServer: '(DATEPART(dy FROM t0."date") = DATEPART(dy FROM GETDATE()))',
                    oracle: "(TO_NUMBER(TO_CHAR(t0.\"date\", 'DDD')) = TO_NUMBER(TO_CHAR(SYSDATE, 'DDD')))",
                },
            );
        });

        it(`${dialect}: value`, () => {
            test(
                dialect,
                function (this: any) {
                    return this.date.value === date.today().value;
                },
                {
                    postgres:
                        "((date_part('year', t0.\"date\") * 10000 + date_part('month', t0.\"date\") * 100 + date_part('day', t0.\"date\")) = (date_part('year', CURRENT_DATE) * 10000 + date_part('month', CURRENT_DATE) * 100 + date_part('day', CURRENT_DATE)))",
                    sqlServer:
                        '((DATEPART(yy FROM t0."date") * 10000 + DATEPART(mm FROM t0."date") * 100 + DATEPART(dd FROM t0."date")) = (DATEPART(yy FROM GETDATE()) * 10000 + DATEPART(mm FROM GETDATE()) * 100 + DATEPART(dd FROM GETDATE())))',
                    oracle: '((EXTRACT(YEAR FROM t0."date") * 10000 + EXTRACT(MONTH FROM t0."date") * 100 + EXTRACT(DAY FROM t0."date")) = (EXTRACT(YEAR FROM SYSDATE) * 10000 + EXTRACT(MONTH FROM SYSDATE) * 100 + EXTRACT(DAY FROM SYSDATE)))',
                },
            );
        });

        it(`${dialect}: isLeapYear`, () => {
            test(
                dialect,
                function (this: any) {
                    return this.date.isLeapYear;
                },
                {
                    postgres:
                        "(date_part('day', make_date(date_part('year', t0.\"date\")::int, 3, 1) - '1 day'::interval) = 29)",
                    sqlServer:
                        '((DATEPART(yy FROM t0."date") % 4 = 0 AND DATEPART(yy FROM t0."date") % 100 <> 0) OR DATEPART(yy FROM t0."date") % 400 = 0)',
                    oracle: 'ADD_MONTHS(TRUNC(t0."date",\'YEAR\'),12)-TRUNC(t0."date",\'YEAR\')=366',
                },
            );
        });

        it(`${dialect}: can convert date.parse(str)`, () => {
            test(
                dialect,
                () => {
                    return DateValue.parse('2023-02-01');
                },
                {
                    postgres: "'2023-02-01'::DATE",
                    sqlServer: "CAST('2023-02-01' AS DATE)",
                    oracle: "TO_DATE('2023-02-01','YYYY-MM-DD')",
                },
            );

            test(
                dialect,
                () => {
                    return date.parse('2023-02-01');
                },
                {
                    postgres: "'2023-02-01'::DATE",
                    sqlServer: "CAST('2023-02-01' AS DATE)",
                    oracle: "TO_DATE('2023-02-01','YYYY-MM-DD')",
                },
            );
        });

        it(`${dialect}: can convert addYear`, () => {
            test(
                dialect,
                () => {
                    return DateValue.parse('2023-02-01').addYears(-1);
                },
                {
                    postgres: "('2023-02-01'::DATE + ((- 1::INT8)::INT4 || ' ' || 'year')::interval)::DATE",
                    sqlServer: "DATEADD(year, (- CAST(1 AS INTEGER)), CAST('2023-02-01' AS DATE))",
                    oracle: "ADD_MONTHS(TO_DATE('2023-02-01','YYYY-MM-DD'), (- CAST(1 AS INTEGER)) * 12)",
                },
            );

            test(
                dialect,
                () => {
                    return date.parse('2023-02-01').addYears(1);
                },
                {
                    postgres: "('2023-02-01'::DATE + (1::INT4 || ' ' || 'year')::interval)::DATE",
                    sqlServer: "DATEADD(year, 1, CAST('2023-02-01' AS DATE))",
                    oracle: "ADD_MONTHS(TO_DATE('2023-02-01','YYYY-MM-DD'), 1 * 12)",
                },
            );
        });

        it(`${dialect}: can convert addDays`, () => {
            test(
                dialect,
                () => {
                    return DateValue.parse('2023-02-01').addDays(-1);
                },
                {
                    postgres: "('2023-02-01'::DATE + (- 1::INT8)::INT4)::DATE",
                    sqlServer: "CAST('2023-02-01' AS DATE) + (- CAST(1 AS INTEGER))",
                    oracle: "TO_DATE('2023-02-01','YYYY-MM-DD') + (- CAST(1 AS INTEGER))",
                },
            );

            test(
                dialect,
                () => {
                    return date.parse('2023-02-01').addDays(1);
                },
                {
                    postgres: "('2023-02-01'::DATE + 1::INT4)::DATE",
                    sqlServer: "CAST('2023-02-01' AS DATE) + 1",
                    oracle: "TO_DATE('2023-02-01','YYYY-MM-DD') + 1",
                },
            );
        });

        it(`${dialect}: Throws an error is the date is not correct`, () => {
            testError(
                dialect,
                () => {
                    return DateValue.yesterday();
                },
                /Unsupported DateValue function/,
            );
        });

        it(`${dialect}: date to char`, () => {
            const converter = newTestConverter(dialect);
            const result = converter.stringConverter.convertToChar({ sql: 't0.date', type: 'date' });
            const expected = {
                postgres: "TO_CHAR(t0.date,'YYYY-MM-DD')",
                oracle: "TO_CHAR(t0.date,'YYYY-MM-DD')",
                sqlServer: 'CONVERT(VARCHAR,t0.date,23)',
            };
            assert.equal(result, expected[dialect]);
        });
    });
});
