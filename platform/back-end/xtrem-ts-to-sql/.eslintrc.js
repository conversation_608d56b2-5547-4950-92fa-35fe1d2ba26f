module.exports = {
    extends: ['../../.eslintrc-base.js'],
    parserOptions: {
        tsconfigRootDir: __dirname,
        project: 'tsconfig.json',
    },
    overrides: [
        {
            files: ['lib/**/*.ts'],
            rules: {
                // We enforce this explicit return types in this package
                // because it defines the framework API and this API must be
                // stable and easy to review.
                '@typescript-eslint/explicit-function-return-type': ['error', { allowExpressions: true }],
            },
        },
    ],
};
