{"name": "@sage/xtrem-ts-to-sql", "version": "58.0.2", "description": "Converter from subset of TypeScript to SQL", "author": "Sage", "license": "UNLICENSED", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/index.js", "typings": "build/package-definition.d.ts", "dependencies": {"@sage/xtrem-log": "workspace:*", "@sage/xtrem-shared": "workspace:*", "@typescript-eslint/types": "^7.10.0", "espree": "^10.1.0", "lodash": "^4.17.21"}, "devDependencies": {"@sage/xtrem-dts-bundle": "workspace:*", "@sage/xtrem-minify": "workspace:*", "@types/chai": "^4.3.6", "@types/estree": "1.0.8", "@types/lodash": "^4.14.198", "@types/mocha": "^10.0.1", "c8": "^10.1.2", "chai": "^4.3.10", "cross-env": "^7.0.3", "eslint": "^8.49.0", "mocha": "^10.8.2", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "~5.8.3"}, "engines": {"node": ">=10.0.0"}, "scripts": {"build": "tsc -b -v . && pnpm dts-bundle", "build:binary": "pnpm clean && pnpm build && xtrem-minify -c -z \"build/**/*.js\"", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build junit-report*", "dts-bundle": "xtrem-dts-bundle", "lint": "eslint -c .eslintrc.js --ext .ts lib", "lint:filename": "eslint --no-eslintrc -c .eslintrc-filename.js \"**\"", "test": "mocha --recursive --exit \"test/**/*@(-|.)test.ts\"", "test:ci": "cross-env JUNIT_REPORT_PATH=junit-report-ts-to-sql.xml JUNIT_REPORT_NAME='xtrem-ts-to-sql' c8 --reporter=lcov --reporter=json --reporter=cobertura --reporter=text mocha --exit \"test/**/*@(-|.)test.ts\" --reporter mocha-jenkins-reporter"}}