import * as estree from 'estree';

import { ConversionError } from './conversion-error';
import { Converter } from './converter';
import { ConvertibleFunctionEntry, registry } from './registry';
import { BasicResolver } from './resolver';
import { BasicConversionResult, ConversionResultType } from './types';

/**
 * Special converter for ESLint plugin
 *
 * The plugin does not have the metadata required to convert the code to SQL.
 * So it cannot check the types. This may lead to false positives in the linting.
 */
export class EsLintConverter extends Converter {
    // TEMPORARY HACK
    // We register the known functions here because this converter does not load the application object.
    static knownFunctions = [
        'xtremFinance.functions.signedAmount',
        'xtremFinanceData.functions.getFinancialSite',
        'xtremMasterData.sharedFunctions.convertAmount',
        'xtremMasterData.functions.computeDiscountChargeValue',
        'xtremMasterData.functions.computeDiscountChargeValueDeterminated',
        'xtremMasterData.functions.itemSiteCost.getStandardCostAt',
        'PurchaseInvoiceLine.getVarianceType',
        'xtremLandedCost.nodes.LandedCostLine.getTotalActualCostAmountInCompanyCurrency',
        'xtremPurchasing.functions.BaseDocument.getCurrencyDecimalDigits',
        'xtremPurchasing.functions.PurchaseCreditMemoLib.getPurchaseReceiptLineFromInvoiceLine',
        'xtremPurchasing.functions.PurchaseCreditMemoLib.getPurchaseReceiptLineFromReturnLine',
        'xtremStockData.functions.allocationLib.getLineAllocationStatus',
        'xtremTechnicalData.sharedFunctions.componentFunctions.getQuantityWithScrap',
        'xtremTechnicalData.functions.bomRevisionLib.getRevisionByDate',
        'xtremTechnicalData.functions.componentFunctions.getComponentCostDate',
        'sageX3Sales.functions.getRealClosingDate',
    ];

    static {
        EsLintConverter.knownFunctions.forEach(fullName => {
            const entry = {
                fullName,
                fn: (foo: any) => ({ sql: `eslint dummy SQL ${foo}` }),
                parameterNames: ['foo'],
            } as ConvertibleFunctionEntry;
            registry.registerConvertibleFunction(entry);
        });
    }

    // this cannot be a static initializer because the registry is empty at that point
    dummy = registry.registerEsLintMembers();

    constructor(context: any = null) {
        super(context, { name: 'DummyFactory', tableName: 'DummyTable' }, new BasicResolver(), {
            dialect: 'postgres',
        });
    }

    // eslint-disable-next-line class-methods-use-this
    protected override getCommonType(): ConversionResultType {
        // will be ignored anyways
        return 'decimal';
    }

    protected override convertRegistryFunctionCall(
        callExpression: estree.CallExpression,
        functionEntry: ConvertibleFunctionEntry,
        args: (estree.Expression | estree.SpreadElement)[],
    ): BasicConversionResult {
        if (!EsLintConverter.knownFunctions.includes(functionEntry.fullName))
            throw new ConversionError(callExpression, `Unsupported static call: ${functionEntry.fullName}`);

        args.forEach(arg => this.convertExpression(arg));
        return { type: 'unknown', sql: '<eslint sql>' };
    }
}
