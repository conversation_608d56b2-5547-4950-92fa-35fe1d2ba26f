import { Dict } from '@sage/xtrem-shared';
import { difference } from 'lodash';
import { ConversionError } from './conversion-error';
import { getLogger } from './logger';
import { Resolver } from './resolver';
import {
    Alias,
    BasicConversionResult,
    ConverterFactory,
    ConverterOptions,
    ConverterProperty,
    GenericConversionResult,
    Jo<PERSON>,
    Jo<PERSON><PERSON>omponent,
} from './types';

interface JoinOptions {
    withTenantId: boolean;
    isNullable: boolean;
}

class VariableScope {
    private readonly variables: Dict<BasicConversionResult>;

    constructor(private readonly parent: VariableScope | null = null) {
        this.variables = parent ? { ...parent.variables } : {};
    }

    set(name: string, value: BasicConversionResult): void {
        this.variables[name] = value;
    }

    peekVariable(name: string): BasicConversionResult | undefined {
        return this.variables[name];
    }

    resolve(name: string): BasicConversionResult {
        const value = this.variables[name];
        if (value === undefined) throw new ConversionError(undefined, `'${name}': variable not found`);
        return value;
    }

    push(): VariableScope {
        return new VariableScope(this);
    }

    pop(): VariableScope {
        if (!this.parent) throw new ConversionError(undefined, 'cannot pop root scope');
        return this.parent;
    }
}

interface ConverterScope<FactoryT extends ConverterFactory, PropertyT extends ConverterProperty> {
    parentScope?: ConverterScope<FactoryT, PropertyT>;
    subQueryDepth: number;
    aliases: Alias[];
    // Results are indexed by their path.
    results: Map<string, GenericConversionResult<FactoryT, PropertyT>>;
    scopeAlias: Alias;

    variableScope: VariableScope;
}

export class Walker<ContextT, FactoryT extends ConverterFactory, PropertyT extends ConverterProperty> {
    constructor(
        protected context: ContextT,
        protected rootFactory: FactoryT,
        protected resolver: Resolver<ContextT, FactoryT, PropertyT>,
        readonly options: ConverterOptions,
    ) {
        const scopeAlias = {
            alias: 't0',
            tableName: resolver.resolveTableName(rootFactory),
        };
        this.#scope = {
            subQueryDepth: 0,
            aliases: [],
            scopeAlias,
            results: new Map(),
            variableScope: new VariableScope(),
        };

        this.pushAlias(scopeAlias);

        this.thisResults = [{ factory: this.rootFactory, type: 'reference', sql: 't0._id', path: 'this', alias: 't0' }];

        this.joins = [this.convertThisExpression()];
    }

    #scope: ConverterScope<FactoryT, PropertyT>;

    protected get scopeAlias(): Alias {
        return this.#scope.scopeAlias;
    }

    protected set scopeAlias(alias: Alias) {
        this.#scope.scopeAlias = alias;
    }

    get results(): Map<string, GenericConversionResult> {
        return this.#scope.results;
    }

    get aliases(): Alias[] {
        return this.#scope.aliases;
    }

    get variableScope(): VariableScope {
        return this.#scope.variableScope;
    }

    get maxSubQueryDepth(): number {
        return this.options.maxSubQueryDepth ?? Walker.defaultMaxSubQueryDepth;
    }

    pushVariableScope(): void {
        this.#scope.variableScope = this.#scope.variableScope.push();
    }

    popVariableScope(): void {
        this.#scope.variableScope = this.#scope.variableScope.pop();
    }

    aliasIndex = 0;

    /** Sequence number for unique paths */
    pathSequence = 0;

    // The names of all the tables accessed by the query (including its subqueries)
    public readonly allTableNames: string[] = [];

    // The stack of joins.
    // This stack is flushed every time we terminate a sub query, and at the very end.
    private readonly joins: GenericConversionResult[];

    // The stack of `this` results
    // This is used if we encounter a getValue after traversing a reference.
    private readonly thisResults: GenericConversionResult[];

    /**
     * Generates a unique path.
     * @param parentPath - The parent path to generate the unique path from (for debugging purposes).
     * @returns The unique path.
     */
    getUniquePath(parentPath: string): string {
        this.pathSequence += 1;
        return `${parentPath}.__${this.pathSequence}__`;
    }

    pushAlias(alias: Alias): void {
        getLogger().debug(
            () => `push alias: ${alias.alias}, table: ${alias.tableName}, condition: ${alias.join?.condition}`,
        );
        if (this.aliases.some(a => a.alias === alias.alias)) return;
        this.aliases.push(alias);
        if (!this.allTableNames.includes(alias.tableName)) this.allTableNames.push(alias.tableName);
    }

    protected static createJoin(left: JoinComponent, right: JoinComponent, options: JoinOptions): Join {
        if (options.withTenantId) {
            left.sqls.push(`${left.alias}._tenant_id`);
            right.sqls.push(`${right.alias}._tenant_id`);
        }
        const condition = Walker.and(left.sqls.map((colVal, i) => `${colVal}=${right.sqls[i]}`));
        return {
            left,
            right,
            condition,
            isNullable: options.isNullable,
        };
    }

    protected createCollectionJoin(
        resolved: GenericConversionResult,
        reverseResolved: GenericConversionResult | null,
    ): void {
        const parent = resolved.parent;

        if (resolved.skipJoin) {
            this.scopeAlias = {
                tableName: this.resolver.resolveTableName(resolved.factory as FactoryT),
                alias: resolved.alias,
            };
            return;
        }

        if (!parent) throw new ConversionError(undefined, `${resolved.path}: cannot create collection join: no parent`);

        resolved.join = Walker.createJoin(
            {
                alias: parent.alias,
                sqls: reverseResolved ? [`${parent.alias}._id`] : [resolved.sql],
            },
            {
                alias: reverseResolved ? reverseResolved.alias || '' : resolved.alias || '',
                sqls: reverseResolved ? [reverseResolved.sql] : [`${resolved.alias}._id`],
            },
            {
                withTenantId: !resolved.factory?.isSharedByAllTenants,
                isNullable: !!resolved.property?.isNullable,
            },
        );
        this.scopeAlias = {
            tableName: this.resolver.resolveTableName(resolved.factory as FactoryT),
            alias: resolved.alias,
            join: resolved.join,
        };
    }

    // eslint-disable-next-line class-methods-use-this
    protected createReferenceJoin(
        resolved: GenericConversionResult<FactoryT, PropertyT>,
        reverseResolved: GenericConversionResult<FactoryT, PropertyT> | null,
    ): void {
        const parent = resolved.parent;
        if (!parent) throw new ConversionError(undefined, `${resolved.path}: cannot create reference join: no parent`);

        resolved.join = Walker.createJoin(
            {
                alias: parent.alias,
                sqls: reverseResolved ? [`${parent.alias}._id`] : [resolved.sql],
            },
            {
                alias: reverseResolved ? reverseResolved.alias || '' : resolved.alias || '',
                sqls: reverseResolved ? [reverseResolved.sql] : [`${resolved.alias}._id`],
            },
            {
                withTenantId: !resolved.factory?.isSharedByAllTenants,
                // We need to check if the resolved property or parent join is nullable
                // If the parent join is nullable then we need to LEFT JOIN
                // this.foo.bar
                // if foo is nullable and bar is not nullable they both need to have a LEFT JOIN
                // not just foo.
                isNullable: !!resolved.property?.isNullable || !!parent.join?.isNullable,
            },
        );
    }

    makeAliasAndJoin(resolved: GenericConversionResult): void {
        const parent = resolved.parent;
        if (!parent) throw new Error('cannot create walk aliases: no parent');
        const path = resolved.path;
        if (!path) throw new Error('cannot create walk aliases: no path');

        if (this.results.get(path)) return;

        const propertyType = resolved.property?.type;
        if (
            !propertyType ||
            propertyType === 'reference' ||
            propertyType === 'referenceArray' ||
            propertyType === 'collection'
        ) {
            // Allocate a new alias for the result
            this.aliasIndex += 1;
            resolved.alias = `t${this.aliasIndex}`;
            this.results.set(path, resolved);
        }

        const reverseResolved = resolved.reverseReferenceName
            ? this.walk(resolved, resolved.reverseReferenceName)
            : null;

        if (!propertyType || propertyType === 'reference') {
            this.createReferenceJoin(resolved as GenericConversionResult<FactoryT, PropertyT>, reverseResolved);
            this.pushAlias({
                tableName: this.resolver.resolveTableName(resolved.factory as FactoryT),
                alias: resolved.alias,
                join: resolved.join,
            });
        } else if (propertyType === 'collection') {
            this.createCollectionJoin(resolved, reverseResolved);
            this.pushAlias(this.scopeAlias);
        } else if (propertyType === 'referenceArray') {
            this.scopeAlias = {
                tableName: this.resolver.resolveTableName(resolved.factory as FactoryT),
                alias: resolved.alias,
            };
            this.pushAlias(this.scopeAlias);
        }
    }

    private _walk(
        parent: GenericConversionResult,
        name: string,
        overrides?: { factory: FactoryT },
    ): GenericConversionResult {
        const path = `${parent.path}.${name}`;

        if (this.options.beforeWalkCallback) {
            this.options.beforeWalkCallback(path);
        }

        // If we already created a result for this path, use it
        const result = this.results.get(path);
        if (result) return result;

        getLogger().debug(() => `walk: ${path} from alias=${parent.alias}, factory=${parent.factory?.name}`);

        // Shortcut this.foo._id -> this.foo, except for this._id
        // We need to exclude referenceArray properties here as this.foo[n]._id does not translate to this.foo
        if (
            name === '_id' &&
            parent.path !== 'this' &&
            parent.property?.type !== 'referenceArray' &&
            parent.property?.type !== 'collection' &&
            parent.property?.isStored
        ) {
            return { ...parent, path: `${parent.path}._id` };
        }

        if (parent.parent && !this.results.get(parent.path)) this.makeAliasAndJoin(parent);
        // _empty is used to force join generation in collection sub queries when there is no filter.
        if (name === '_empty') return parent;

        const resolved = this.resolver.resolveColumnName(
            this.context,
            parent as GenericConversionResult<FactoryT, PropertyT>,
            name,
        );
        if (parent.isNullable) resolved.isNullable = true;
        getLogger().debug(() => `resolved: path=${path}, targetFactory=${resolved.factory.name} sql=${resolved.sql}`);

        if (resolved.isInherited && !resolved.reverseReferenceName) {
            return this.walk(this.walkToSuper(parent), name, overrides || { factory: resolved.factory });
        }

        const propertyType = resolved.type && resolved.property?.type;

        if (propertyType === 'json') return { ...resolved, path };
        if (parent.type === 'dateRange' && resolved.type === 'date') return resolved;
        if (parent.type === 'datetimeRange' && resolved.type === 'datetime') return resolved;
        if (parent.type === 'integerRange' && resolved.type === 'integer') return resolved;
        if (parent.type === 'decimalRange' && resolved.type === 'decimal') return resolved;

        if (parent.type === 'integerArray' && resolved.type === 'integer') return resolved;
        if (parent.type === 'enumArray' && resolved.type === 'integer') return resolved;
        if (parent.type === 'stringArray' && resolved.type === 'string') return resolved;

        return {
            ...resolved,
            ...overrides,
            parent,
            path,
        };
    }

    protected walk(
        parent: GenericConversionResult,
        name: string,
        overrides?: { factory: FactoryT },
    ): GenericConversionResult<FactoryT, PropertyT> {
        return this._walk(parent, name, overrides) as GenericConversionResult<FactoryT, PropertyT>;
    }

    private static formatJoins(fromAlias: string, allAliases: Alias[], aliases = allAliases): string {
        const matchingAliases = aliases.filter(
            alias => alias.join?.left.alias === fromAlias || alias.join?.right.alias === fromAlias,
        );
        if (matchingAliases.length === 0) return '';

        const remainingAliases = difference(aliases, matchingAliases);

        return matchingAliases
            .map(alias => {
                const otherAlias =
                    alias.join?.left.alias === fromAlias ? alias.join?.right.alias : alias.join?.left.alias;
                let formattedJoin = otherAlias
                    ? Walker.formatJoins(otherAlias, allAliases, remainingAliases).trim() || ''
                    : '';
                formattedJoin = formattedJoin ? ` ${formattedJoin}` : '';
                const otherTableName = allAliases.find(a => a.alias === otherAlias)?.tableName;

                const leftOrInner = alias.join?.isNullable ? 'LEFT' : 'INNER';
                const onClause = `${alias.join?.condition}${formattedJoin}`;
                if (alias.join?.lateral)
                    return `${leftOrInner} JOIN LATERAL ${alias.join.lateral} AS ${alias.alias} ON ${onClause}`;

                return `${leftOrInner} JOIN ${otherTableName} AS ${otherAlias} ON ${onClause}`;
            })
            .filter(sqlPart => !!sqlPart.trim())
            .join(' ');
    }

    getTableAliases(): string {
        const scopeAlias = this.scopeAlias;
        const aliases = this.#scope.parentScope
            ? this.#scope.aliases.slice(this.#scope.parentScope.aliases.length).filter(alias => alias !== scopeAlias)
            : this.aliases;
        return `${scopeAlias.tableName} AS ${scopeAlias.alias} ${Walker.formatJoins(scopeAlias.alias, aliases)}`;
    }

    withSubQueryScope<T extends {}>(
        body: () => T,
        getJoinCondition?: () => string,
    ): T & {
        aliases: string;
        joinCondition: string;
        innerAlias: Alias;
    } {
        getLogger().debug(() => '+++ push scope');

        const parentScope = this.#scope;
        const maxSubQueryDepth = this.maxSubQueryDepth;
        if (parentScope.subQueryDepth === maxSubQueryDepth)
            throw new ConversionError(undefined, `max sub query depth (${maxSubQueryDepth}) exceeded`);
        this.#scope = {
            parentScope,
            subQueryDepth: parentScope.subQueryDepth + 1,
            results: new Map(parentScope.results),
            aliases: [...parentScope.aliases],
            scopeAlias: parentScope.scopeAlias,
            variableScope: new VariableScope(parentScope.variableScope),
        } as ConverterScope<FactoryT, PropertyT>;

        const bodyResult = body();

        if (!this.#scope.parentScope) throw new ConversionError(undefined, 'cannot pop scope: no parent scope');

        const aliases = this.getTableAliases();

        const joinCondition = getJoinCondition ? getJoinCondition() : this.scopeAlias.join?.condition || '';

        const innerAlias = this.scopeAlias;

        this.#scope = this.#scope.parentScope;

        getLogger().debug(() => `--- pop scope: joinCondition=${joinCondition} bodyResult=${bodyResult}`);

        return { aliases, ...bodyResult, joinCondition, innerAlias };
    }

    private pushAliasAndJoin(result: GenericConversionResult, join: Join): void {
        this.joins.push(result);
        this.pushAlias({
            tableName: this.resolver.resolveTableName(result.factory as FactoryT),
            alias: result.alias,
            join,
        });
    }

    private walkToSuper(parent: GenericConversionResult): GenericConversionResult {
        const path = `${parent.path}._super`;

        // If we already created it, use it
        let result = this.results.get(path);
        if (result) return result;

        getLogger().debug(() => `walk to super: ${path} from alias=${parent.alias}, factory=${parent.factory.name}`);

        const parentFactory = parent.factory;
        const superFactory = parentFactory.baseFactory;
        if (!superFactory) throw new ConversionError(undefined, `${parent.path}: no super factory`);

        // allocate an alias
        this.aliasIndex += 1;
        const alias = `t${this.aliasIndex}`;

        const join = Walker.createJoin(
            { alias: parent.alias, sqls: [`${parent.alias}._id`] },
            { alias, sqls: [`${alias}._id`] },
            {
                withTenantId: !parentFactory?.isSharedByAllTenants,
                isNullable: !!(parent.isNullable || parent.join?.isNullable),
            },
        );
        result = {
            parent: parent.parent,
            path,
            factory: superFactory,
            alias,
            type: 'reference',
            sql: `${parent.alias}._id`,
            join,
        };
        this.results.set(path, result);

        this.pushAliasAndJoin(result, join);
        return result;
    }

    convertThisExpression(isMemberOf = false): GenericConversionResult<FactoryT, PropertyT> {
        const result = this.thisResults[this.thisResults.length - 1] as GenericConversionResult<FactoryT, PropertyT>;

        // If we are accessing a this after traversing a reference, we must
        // create its join if it hasn't been already created.
        if (
            this.thisResults.length > 1 &&
            result.property?.type === 'reference' &&
            !this.joins.find(join => join.alias === result.alias)
        ) {
            if (!result.join) throw new ConversionError(undefined, `${result.path}: this result has no join`);
            this.pushAliasAndJoin(result, result.join);
        }

        // If this result is used to access a member, the sql result is just the alias
        // otherwise it is alias._id
        return isMemberOf ? { ...result, sql: result.alias } : { ...result, sql: `${result.alias}._id` };
    }

    // Overrides convertThisExpression if we
    withThisResultScope<T>(result: GenericConversionResult<FactoryT, PropertyT>, body: () => T): T {
        // Ensure that the join and alias are created before pushing the scope
        if (result.parent && !this.results.get(result.path)) this.makeAliasAndJoin(result);

        this.thisResults.push(result);
        try {
            return body();
        } finally {
            this.thisResults.pop();
        }
    }

    private static join(clauses: (string | undefined)[], sep: string, def: string): string {
        const nonEmpty = clauses.filter(s => !!s);
        if (nonEmpty.length > 1) return `${nonEmpty.join(sep)}`;
        return nonEmpty[0] ?? def;
    }

    static and(clauses: (string | undefined)[]): string {
        return Walker.join(clauses, ' AND ', '1=1');
    }

    static or(clauses: (string | undefined)[]): string {
        return `(${Walker.join(clauses, ' OR ', '1=2')})`;
    }

    /**
     * Default maximum depth of sub queries.
     *
     * Default value of 2 will limit us to 1 main query with 2 nested sub queries, for example a query on document
     * with a filter like `document.every(line => line.stockDetails.some(stock => ...))`
     *
     * This is sufficient for our current `getValue` rules but it may limit filters in GraphQL API queries.
     * We could increase it we get requests for it but we should be careful as it could lead to performance issues.
     * And better start with a low value and increase it if needed, after assessing the impact.
     */
    private static readonly defaultMaxSubQueryDepth = 3;
}
