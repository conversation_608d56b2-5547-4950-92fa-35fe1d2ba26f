import { snakeCase } from 'lodash';
import { protectedWords } from './protected-words';

export function getColumnName(s: string): string {
    const convertedColumn = s[0] === '_' ? `_${snakeCase(s.substring(1))}` : snakeCase(s);

    if (protectedWords.includes(convertedColumn)) {
        return `"${convertedColumn}"`;
    }
    return convertedColumn;
}

export const getTableName = getColumnName;

export type ConversionResultType =
    | 'boolean'
    | 'enum'
    | 'enumArray'
    | 'short'
    | 'byte'
    | 'integer'
    | 'integerRange'
    | 'integerArray'
    | 'decimalRange'
    | 'decimal'
    | 'float'
    | 'double'
    | 'string'
    | 'stringArray'
    | 'date'
    | 'dateRange'
    | 'datetimeRange'
    | 'time'
    | 'binaryStream'
    | 'binary'
    | 'textStream'
    | 'uuid'
    | 'datetime'
    | 'json'
    | 'reference'
    | 'referenceArray'
    | 'collection'
    | 'instance'
    | 'jsonReference'
    | 'unknown'
    | 'void';

// Factory is rather opaque here. All we know is its name
export interface ConverterFactory {
    name: string;
    table?: { sqlTableName: string };
    isSharedByAllTenants?: boolean;
    baseFactory?: this;
    tableName?: string;
}

export type ConverterProperty = {
    name: string;
    type: ConversionResultType;
    isNullable?: boolean;
    isStored?: boolean;
    isLocalized?: boolean;
};

export interface BasicConversionResult<
    FactoryT extends ConverterFactory = ConverterFactory,
    PropertyT extends ConverterProperty = ConverterProperty,
> {
    factory?: FactoryT;
    property?: PropertyT;
    path?: string;
    type: ConversionResultType;
    isNullable?: boolean;
    sql: string;
    extraType?: string;
}

export interface Alias {
    tableName: string;
    alias: string;
    join?: Join;
}

export interface JoinComponent {
    alias: string;
    sqls: string[];
}

export interface Join {
    left: JoinComponent;
    right: JoinComponent;
    condition: string;
    isNullable: boolean;
    lateral?: string;
}

export interface GenericConversionResult<
    FactoryT extends ConverterFactory = ConverterFactory,
    PropertyT extends ConverterProperty = ConverterProperty,
> extends BasicConversionResult<FactoryT, PropertyT> {
    alias: string;
    path: string;

    parent?: GenericConversionResult<FactoryT, PropertyT>;
    factory: FactoryT;
    reverseReferenceName?: string;
    isInherited?: boolean;
    aggregationOperator?: string;
    columnAlias?: string;

    join?: Join;
    collation?: string;
    skipJoin?: boolean;
}

export type Dialect = 'postgres' | 'oracle' | 'sqlServer';

export interface ConverterOptions {
    /** The SQL dialect (postgres, oracle, sqlServer) */
    dialect: Dialect;
    /** Maximum subquery depth that the ts-to-sql converter accepts */
    maxSubQueryDepth?: number;
    /** Disables logging - used during verification of getValue / computeValue decorators */
    quiet?: boolean;
    beforeWalkCallback?: (path: string) => void;
}
