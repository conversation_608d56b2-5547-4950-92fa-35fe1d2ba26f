import { Dict } from '@sage/xtrem-shared';
import * as estree from 'estree';
import { Converter } from './converter';
import { BasicConversionResult, GenericConversionResult } from './types';

export interface RegistryEntry {
    fullName: string;
}

export interface FunctionEntry extends RegistryEntry {}

export interface ConvertibleFunctionEntry extends FunctionEntry {
    fn: Function;
    parameterNames: string[];
    parsedFunction?: estree.ArrowFunctionExpression | estree.FunctionExpression;
}

export interface BuiltInFunctionEntry extends FunctionEntry {
    convertExpression: (
        converter: Converter,
        expression: estree.CallExpression,
        args: estree.BaseExpression[],
        fullName: string,
    ) => BasicConversionResult;
}

export interface MethodEntry extends RegistryEntry {
    convertExpression: (
        converter: Converter,
        result: GenericConversionResult,
        args: estree.BaseExpression[],
        fullName: string,
    ) => BasicConversionResult;
}

export interface PropertyEntry extends RegistryEntry {
    convertExpression: (
        converter: Converter,
        result: GenericConversionResult,
        fullName: string,
    ) => BasicConversionResult;
}

class Registry {
    private readonly functionEntries = {} as Dict<FunctionEntry>;

    private readonly methodEntries = {} as Dict<MethodEntry>;

    private readonly propertyEntries = {} as Dict<PropertyEntry>;

    // eslint-disable-next-line class-methods-use-this
    memberFullName(type: string, memberName: string): string {
        return `${type}.${memberName}`;
    }

    registerConvertibleFunction(entry: ConvertibleFunctionEntry): void {
        this.functionEntries[entry.fullName] = entry;
    }

    registerBuiltInFunctions(entries: Dict<BuiltInFunctionEntry['convertExpression']>): void {
        Object.entries(entries).forEach(([fullName, convertExpression]) => {
            this.functionEntries[fullName] = { fullName, convertExpression } as BuiltInFunctionEntry;
        });
    }

    registerMethods(type: string, entries: Dict<MethodEntry['convertExpression']>): void {
        Object.entries(entries).forEach(([methodName, convertExpression]) => {
            const fullName = this.memberFullName(type, methodName);
            this.methodEntries[fullName] = { fullName, convertExpression } as MethodEntry;
        });
    }

    registerProperties(type: string, entries: Dict<PropertyEntry['convertExpression']>): void {
        Object.entries(entries).forEach(([propertyName, convertExpression]) => {
            const fullName = this.memberFullName(type, propertyName);
            this.propertyEntries[fullName] = { fullName, convertExpression } as PropertyEntry;
        });
    }

    // eslint-disable-next-line class-methods-use-this
    private getEntry<T extends RegistryEntry>(entries: Dict<T>, fullName: string): T {
        return entries[fullName];
    }

    getFunctionEntry(fullName: string): FunctionEntry {
        return this.getEntry(this.functionEntries, fullName);
    }

    getMethodEntry(type: string, methodName: string): MethodEntry {
        return this.getEntry(this.methodEntries, this.memberFullName(type, methodName));
    }

    getPropertyEntry(type: string, propertyName: string): PropertyEntry {
        return this.getEntry(this.propertyEntries, this.memberFullName(type, propertyName));
    }

    registerEsLintMembers(): void {
        Object.entries(this.methodEntries).forEach(([fullName, entry]) => {
            this.methodEntries[fullName.replace(/^\w+/, 'unknown')] = entry;
        });
        Object.entries(this.propertyEntries).forEach(([fullName, entry]) => {
            this.propertyEntries[fullName.replace(/^\w+/, 'unknown')] = entry;
        });
    }
}

export const registry = new Registry();

function parseParameterNames(fn: Function): string[] {
    const fnStr = fn.toString();
    const start = fnStr.indexOf('(');
    const end = fnStr.indexOf(')');
    if (start < 0 || end < start) throw new Error(`Could not parse parameters for function ${fnStr}`);
    const paramsStr = fnStr.substring(start + 1, end);
    return paramsStr.split(',').map(s => s.trim());
}

/**
 * Registers a funct function to the TS to SQL converter.
 *
 * @param fullName - The full name of the SQL function.
 * @param fn - The function to be registered.
 * @returns The registered function.
 */
export function registerSqlFunction(fullName: string, fn: Function): void {
    const parameterNames = parseParameterNames(fn);
    const descriptor = { fullName, parameterNames, fn };
    registry.registerConvertibleFunction(descriptor);
}
