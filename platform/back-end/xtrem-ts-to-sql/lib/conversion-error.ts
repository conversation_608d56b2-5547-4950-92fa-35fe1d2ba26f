import * as estree from 'estree';

export class ConversionError extends Error {
    constructor(
        public node: estree.BaseNode | undefined,
        message: string,
        readonly innerError?: Error,
    ) {
        const innerMessage = innerError
            ? `: \n    [${innerError.message
                  .split('\n')
                  .map((s, i) => (i === 0 ? s : `   ${s}`))
                  .join('\n')}]`
            : '';

        const locPrefix = node?.loc ? `${node.loc.start.line}:${node.loc.start.column}: ` : '';
        super(`${locPrefix}${message}${innerMessage}`);
    }
}
