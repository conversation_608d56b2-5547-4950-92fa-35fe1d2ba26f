import { ConversionError } from './conversion-error';
import { BasicConversionResult, ConversionResultType } from './types';

/**
 * Converts date expressions to SQL.
 */
export class TypeConverter {
    constructor(private readonly dialect: string) {}

    private oracleType(type: ConversionResultType): string {
        switch (type) {
            case 'short':
                return 'SMALLINT';
            case 'integer':
                return 'INTEGER';
            case 'decimal':
                return 'DECIMAL';
            case 'float':
                return 'FLOAT';
            case 'double':
                return 'DOUBLE PRECISION';
            case 'date':
                return 'DATE';
            case 'string':
                return 'VARCHAR';
            case 'boolean':
                return 'BOOLEAN';
            default:
                throw new ConversionError(undefined, `unsupported type: ${type} for ${this.dialect}`);
        }
    }

    private sqlServerType(type: ConversionResultType): string {
        switch (type) {
            case 'short':
                return 'SMALLINT';
            case 'integer':
                return 'INTEGER';
            case 'decimal':
                return 'DECIMAL';
            case 'float':
                return 'REAL';
            case 'double':
                return 'FLOAT';
            case 'date':
                return 'DATE';
            case 'string':
                return 'VARCHAR';
            case 'boolean':
                return 'BOOLEAN';
            default:
                throw new ConversionError(undefined, `unsupported type: ${type} for ${this.dialect}`);
        }
    }

    private postgresType(type: ConversionResultType): string {
        switch (type) {
            case 'short':
                return 'INT4';
            case 'integer':
                return 'INT8';
            case 'decimal':
                return 'NUMERIC';
            case 'float':
                return 'FLOAT4';
            case 'double':
                return 'FLOAT8';
            case 'date':
                return 'DATE';
            case 'string':
                return 'TEXT';
            case 'boolean':
                return 'BOOLEAN';
            default:
                throw new ConversionError(undefined, `unsupported type: ${type} for this ${this.dialect}`);
        }
    }

    sqlType(type: ConversionResultType): string {
        switch (this.dialect) {
            case 'oracle':
                return this.oracleType(type);
            case 'sqlServer':
                return this.sqlServerType(type);
            case 'postgres':
                return this.postgresType(type);
            default:
                throw new ConversionError(undefined, `unsupported dialect: ${this.dialect}`);
        }
    }

    // eslint-disable-next-line class-methods-use-this
    castOracleDate = (result: BasicConversionResult, type: ConversionResultType): BasicConversionResult => {
        if (!type || result.property?.type === type) return result;
        const sql = `TO_DATE(${result.sql},'YYYY-MM-DD')`;
        return { ...result, type, sql };
    };

    // eslint-disable-next-line class-methods-use-this
    isNumericType(type: ConversionResultType): boolean {
        return ['short', 'integer', 'decimal', 'float', 'double'].includes(type);
    }
}
