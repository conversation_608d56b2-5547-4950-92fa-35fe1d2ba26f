import { LoggerError<PERSON><PERSON><PERSON>, LoggerInterface, ProfilerCallback } from '@sage/xtrem-shared';

const nopProfilerCallback: ProfilerCallback = {
    success(): void {},
    fail(): void {},
};

const nopLogger: LoggerInterface = {
    log(): ProfilerCallback {
        return nopProfilerCallback;
    },
    info(): ProfilerCallback {
        return nopProfilerCallback;
    },
    warn(): ProfilerCallback {
        return nopProfilerCallback;
    },
    verbose(): ProfilerCallback {
        return nopProfilerCallback;
    },
    debug(): ProfilerCallback {
        return nopProfilerCallback;
    },
    error(): ProfilerCallback {
        return nopProfilerCallback;
    },
    do<T>(fn: () => T, onError: LoggerErrorHandler<T>): T {
        try {
            return fn();
        } catch (err) {
            this.error(err.stack);
            return onError(err);
        }
    },
};

let loggerInstance = nopLogger;

export function setLogger(logger?: LoggerInterface): void {
    loggerInstance = logger || nopLogger;
}

export function getLogger(): LoggerInterface {
    return loggerInstance;
}
