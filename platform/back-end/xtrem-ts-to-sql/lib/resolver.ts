import { AnyValue } from '@sage/xtrem-shared';
import { quote } from './converter';
import { ConverterFactory, ConverterProperty, GenericConversionResult, getColumnName } from './types';

export interface Resolver<ContextT, FactoryT extends ConverterFactory, PropertyT extends ConverterProperty> {
    resolveColumnName: (
        cx: ContextT,
        parent: GenericConversionResult<FactoryT, PropertyT>,
        propertyName: string,
    ) => GenericConversionResult<FactoryT, PropertyT>;
    resolveTableName: (factory: FactoryT) => string;
    resolveLiteral: (value: AnyValue) => string;
}

export class BasicResolver<ContextT, FactoryT extends ConverterFactory, PropertyT extends ConverterProperty>
    implements Resolver<ContextT, FactoryT, PropertyT>
{
    // eslint-disable-next-line class-methods-use-this
    resolveColumnName(
        _cx: ContextT,
        parent: GenericConversionResult<FactoryT, PropertyT>,
        propertyName: string,
    ): GenericConversionResult<FactoryT, PropertyT> {
        return {
            path: parent.path ? `${parent.path}.${propertyName}` : propertyName,
            alias: parent.alias,
            factory: parent.factory,
            type: 'unknown',
            sql: `${parent.alias}.${getColumnName(propertyName)}`,
        };
    }

    // eslint-disable-next-line class-methods-use-this
    resolveTableName(factory: FactoryT): string {
        if (!factory) return 'dummy_table';
        return factory.table?.sqlTableName || factory.tableName || factory.name;
    }

    // eslint-disable-next-line class-methods-use-this
    resolveLiteral(value: AnyValue): string {
        let text: string;
        if (typeof value === 'string') {
            text = quote(value);
        } else {
            text = `${value}`;
        }
        return text;
    }
}
