import * as estree from 'estree';
import { BasicConversionResult } from '../../types';
import { DateConverter, DateFunctions, DateInterval } from './date-converter';

/**
 * Converts date expressions to SQL.
 */
export class PostgresDateConverter extends DateConverter {
    // Note: we are converting `value` to `epoch` even if they are different.
    // This is to favor efficiency but then value should only be used for comparison.
    dateSqlFields = {
        epoch: (from: string) => `date_part('epoch', ${from})`,
        year: (from: string) => `date_part('year', ${from})`,
        month: (from: string) => `date_part('month', ${from})`,
        day: (from: string) => `date_part('day', ${from})`,
        week: (from: string) => `date_part('week', ${from})`,
        weekDay: (from: string) => `date_part('dow', ${from})`,
        yearDay: (from: string) => `date_part('doy', ${from})`,
        isLeapYear: (from: string) =>
            `(date_part('day', make_date(date_part('year', ${from})::int, 3, 1) - '1 day'::interval) = 29)`,
    } as Record<DateFunctions, (from: string) => string>;

    readonly currentDate = 'CURRENT_DATE';

    convertAddIntervalCall(
        result: BasicConversionResult,
        args: estree.BaseExpression[],
        methodName: string,
        intervalName: DateInterval,
    ): BasicConversionResult {
        const [argResult] = this.convertArgs(args, ['integer'], `date.${methodName}`);
        // postgres is default SQL dialect
        const sql = `(${result.sql} + (${argResult.sql}::INT4 || ' ' || '${intervalName}')::interval)::DATE`;

        return {
            ...result,
            type: 'date',
            sql,
        };
    }

    convertAddDaysCall(result: BasicConversionResult, args: estree.BaseExpression[]): BasicConversionResult {
        const [argResult] = this.convertArgs(args, ['integer'], 'date.addDays');

        const sql = `(${result.sql} + ${argResult.sql}::INT4)::DATE`;
        return {
            ...result,
            type: 'date',
            sql,
        };
    }

    convertDaysDiffCall(result: BasicConversionResult, args: estree.BaseExpression[]): BasicConversionResult {
        const [argResult] = this.convertArgs(args, ['date'], 'date.daysDiff');
        const sql = `(${result.sql} - ${argResult.sql})::INT4`;
        return {
            ...result,
            type: 'integer',
            sql,
        };
    }

    convertDaysInMonthCall(result: BasicConversionResult, args: estree.BaseExpression[]): BasicConversionResult {
        this.convertArgs(args, [], 'date.daysInMonth');

        const sql = `(date_part('day', date_trunc('month', ${result.sql}) + interval '1 month - 1 day'))`;

        return {
            ...result,
            type: 'integer',
            sql,
        };
    }

    convertBegOfIntervalCall(
        result: BasicConversionResult,
        args: estree.BaseExpression[],
        methodName: string,
        intervalName: DateInterval,
    ): BasicConversionResult {
        this.convertArgs(args, [], `date.${methodName}`);
        const sql = `date_trunc('${intervalName}', ${result.sql})::DATE`;

        return {
            ...result,
            type: 'date',
            sql,
        };
    }

    convertEndOfIntervalCall(
        result: BasicConversionResult,
        args: estree.BaseExpression[],
        methodName: string,
        intervalName: DateInterval,
    ): BasicConversionResult {
        this.convertArgs(args, [], `date.${methodName}`);
        let delta = `1 ${intervalName}`;
        if (intervalName === 'quarter') {
            delta = '3 months';
        }

        const sql = `(date_trunc('${intervalName}', ${result.sql}) + interval '${delta} - 1 day')`;
        return {
            ...result,
            type: 'date',
            sql,
        };
    }

    /**
     * convert to char
     * @param sql
     */
    // eslint-disable-next-line class-methods-use-this
    convertToChar(result: BasicConversionResult, format?: string): string {
        // Default ISO format YYYY-MM-DD
        return `TO_CHAR(${result.sql},'${format ?? 'YYYY-MM-DD'}')`;
    }

    /**
     * convert to date/datetime UTC format
     * @param sql
     * @param dateTime - true if the conversion is to a datetime
     */
    // eslint-disable-next-line class-methods-use-this
    convertToDate(sql: string, dateTime = false): string {
        if (dateTime) {
            return `TO_TIMESTAMP(${sql},'YYYY-MM-DD HH24:MI:SS') AT TIME ZONE 'UTC'`;
        }
        return `TO_DATE(${sql},'YYYY-MM-DD')`;
    }
}
