import * as estree from 'estree';
import { ConversionError } from '../../conversion-error';
import { BasicConversionResult } from '../../types';
import { DateConverter, DateFunctions, DateInterval } from './date-converter';

/**
 * Converts date expressions to SQL.
 */
export class OracleDateConverter extends DateConverter {
    dateSqlFields = {
        epoch: (from: string) => `((${from} - TO_DATE('1970-01-01','YYYY-MM-DD')) * 86400)`, // Oracle does not have a direct way to convert date to epoch, so we convert it to seconds since 1970-01-01
        year: (from: string) => `EXTRACT(YEAR FROM ${from})`, // The year, including century, between 100 and 9999.
        month: (from: string) => `EXTRACT(MONTH FROM ${from})`, // The month, between 1 and 12.
        day: (from: string) => `EXTRACT(DAY FROM ${from})`, // The day of the month, between 1 and 31.
        week: (from: string) => `TO_NUMBER(TO_CHAR(${from}, 'WW'))`, // The week of the year, between 1 and 53.
        weekDay: (from: string) =>
            `(CASE TO_CHAR(${from},'fmDY','nls_date_language=English') WHEN 'SUN' THEN 0 WHEN 'MON' THEN 1 WHEN 'TUE' THEN 2 WHEN 'WED' THEN 3 WHEN 'THU' THEN 4 WHEN 'FRI' THEN 5 WHEN 'SAT' THEN 6 END)`, // The day of the week, between 0 (Sunday) and 6 (Saturday).
        yearDay: (from: string) => `TO_NUMBER(TO_CHAR(${from}, 'DDD'))`, // The day of the year, between 1 and 366.
        isLeapYear: (from: string) => `ADD_MONTHS(TRUNC(${from},'YEAR'),12)-TRUNC(${from},'YEAR')=366`, // Oracle does not have a direct way to check for leap year, so check if the days in the year = 366
    } as Record<DateFunctions, (from: string) => string>;

    override readonly currentDate = 'SYSDATE';

    convertAddIntervalCall(
        result: BasicConversionResult,
        args: estree.BaseExpression[],
        methodName: string,
        intervalName: DateInterval,
    ): BasicConversionResult {
        const [argResult] = this.convertArgs(args, ['integer'], `date.${methodName}`);
        // postgres is default SQL dialect
        let sql = '';
        switch (intervalName) {
            case 'year':
                sql = `ADD_MONTHS(${result.sql}, ${argResult.sql} * 12)`;
                break;
            case 'month':
                sql = `ADD_MONTHS(${result.sql}, ${argResult.sql})`;
                break;
            case 'week':
                sql = `(${result.sql}+(${argResult.sql}*7))`;
                break;
            default:
                throw new ConversionError(
                    undefined,
                    `cannot convert call to date method ${methodName} to SQL for interval ${intervalName}`,
                );
        }

        return {
            ...result,
            type: 'date',
            sql,
        };
    }

    convertAddDaysCall(result: BasicConversionResult, args: estree.BaseExpression[]): BasicConversionResult {
        const [argResult] = this.convertArgs(args, ['integer'], 'date.addDays');

        const sql = `${result.sql} + ${argResult.sql}`;
        return {
            ...result,
            type: 'date',
            sql,
        };
    }

    convertDaysDiffCall(result: BasicConversionResult, args: estree.BaseExpression[]): BasicConversionResult {
        const [argResult] = this.convertArgs(args, ['date'], 'date.daysDiff');
        const sql = `(${result.sql} - ${argResult.sql})`;

        return {
            ...result,
            type: 'integer',
            sql,
        };
    }

    convertDaysInMonthCall(result: BasicConversionResult, args: estree.BaseExpression[]): BasicConversionResult {
        this.convertArgs(args, [], 'date.daysInMonth');

        const sql = `1+TRUNC(LAST_DAY(${result.sql}))-TRUNC(${result.sql},'MM')`;

        return {
            ...result,
            type: 'integer',
            sql,
        };
    }

    convertBegOfIntervalCall(
        result: BasicConversionResult,
        args: estree.BaseExpression[],
        methodName: string,
        intervalName: DateInterval,
    ): BasicConversionResult {
        this.convertArgs(args, [], `date.${methodName}`);
        let sql = '';
        switch (intervalName) {
            case 'year':
                sql = `TRUNC(${result.sql},'YEAR')`;
                break;
            case 'month':
                sql = `TRUNC(${result.sql},'MONTH')`;
                break;
            case 'week':
                sql = `TRUNC(${result.sql},'IW')`;
                break;
            case 'quarter':
                sql = `TRUNC(${result.sql},'Q')`;
                break;
            default:
                throw new ConversionError(
                    undefined,
                    `${this.dialect}: cannot convert call to date method ${methodName} to SQL for interval ${intervalName}`,
                );
        }

        return {
            ...result,
            type: 'date',
            sql,
        };
    }

    convertEndOfIntervalCall(
        result: BasicConversionResult,
        args: estree.BaseExpression[],
        methodName: string,
        intervalName: DateInterval,
    ): BasicConversionResult {
        this.convertArgs(args, [], `date.${methodName}`);
        let sql = '';
        switch (intervalName) {
            case 'year':
                sql = `ADD_MONTHS(TRUNC(${result.sql}, 'Y'), 12) - 1`;
                break;
            case 'month':
                sql = `LAST_DAY(${result.sql})`;
                break;
            case 'week':
                sql = `TRUNC(${result.sql},'IW')+6`;
                break;
            case 'quarter':
                sql = `ADD_MONTHS(TRUNC(${result.sql},'Q'),3)-1`;
                break;
            default:
                throw new ConversionError(
                    undefined,
                    `${this.dialect}: cannot convert call to date method ${methodName} to SQL for interval ${intervalName}`,
                );
        }
        return {
            ...result,
            type: 'date',
            sql,
        };
    }

    /**
     * convert to char
     * @param sql
     */
    // eslint-disable-next-line class-methods-use-this
    convertToChar(result: BasicConversionResult, format?: string): string {
        // Default ISO format YYYY-MM-DD
        return `TO_CHAR(${result.sql},'${format ?? 'YYYY-MM-DD'}')`;
    }

    /**
     * convert to date/datetime UTC format
     * @param sql
     * @param dateTime - true if the conversion is to a datetime
     */
    // eslint-disable-next-line class-methods-use-this
    convertToDate(sql: string, dateTime = false): string {
        if (dateTime) {
            return `TO_TIMESTAMP(${sql},'YYYY-MM-DD"T"HH24:MI:SS.ff3"Z"')`;
        }
        return `TO_DATE(${sql},'YYYY-MM-DD${dateTime ? ' hh24:mi:ss' : ''}')`;
    }
}
