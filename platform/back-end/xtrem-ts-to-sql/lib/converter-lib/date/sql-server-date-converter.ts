import * as estree from 'estree';
import { ConversionError } from '../../conversion-error';
import { BasicConversionResult } from '../../types';
import { DateConverter, DateFunctions, DateInterval } from './date-converter';

/**
 * Converts date expressions to SQL.
 */
export class SqlServerDateConverter extends DateConverter {
    // Note: we are converting `value` to `epoch` even if they are different.
    // This is to favor efficiency but then value should only be used for comparison.
    dateSqlFields = {
        epoch: (from: string) => `DATEDIFF(SECOND, '1970-01-01', ${from})`,
        year: (from: string) => `DATEPART(yy FROM ${from})`,
        month: (from: string) => `DATEPART(mm FROM ${from})`,
        day: (from: string) => `DATEPART(dd FROM ${from})`,
        week: (from: string) => `DATEPART(isowk FROM ${from})`,
        weekDay: (from: string) => `((DATEPART(dw FROM ${from}) + @@DATEFIRST + 6) % 7 )`, // The day of the week, between 0 (Sunday) and 6 (Saturday).
        yearDay: (from: string) => `DATEPART(dy FROM ${from})`,
        isLeapYear: (from: string) =>
            `((${this.dateSqlFields.year(from)} % 4 = 0 AND ${this.dateSqlFields.year(from)} % 100 <> 0) OR ${this.dateSqlFields.year(from)} % 400 = 0)`, // Compute leap year
    } as Record<DateFunctions, (from: string) => string>;

    override readonly currentDate = 'GETDATE()';

    convertAddIntervalCall(
        result: BasicConversionResult,
        args: estree.BaseExpression[],
        methodName: string,
        intervalName: DateInterval,
    ): BasicConversionResult {
        const [argResult] = this.convertArgs(args, ['integer'], `date.${methodName}`);
        // postgres is default SQL dialect
        let sql = '';
        switch (intervalName) {
            case 'year':
                sql = `DATEADD(year, ${argResult.sql}, ${result.sql})`;
                break;
            case 'month':
                sql = `DATEADD(month, ${argResult.sql}, ${result.sql})`;
                break;
            case 'week':
                sql = `DATEADD(week, ${argResult.sql}, ${result.sql})`;
                break;
            default:
                throw new ConversionError(
                    undefined,
                    `${this.dialect}: cannot convert call to date method ${methodName} to SQL for interval ${intervalName}`,
                );
        }

        return {
            ...result,
            type: 'date',
            sql,
        };
    }

    convertAddDaysCall(result: BasicConversionResult, args: estree.BaseExpression[]): BasicConversionResult {
        const [argResult] = this.convertArgs(args, ['integer'], 'date.addDays');

        const sql = `${result.sql} + ${argResult.sql}`;
        return {
            ...result,
            type: 'date',
            sql,
        };
    }

    convertDaysDiffCall(result: BasicConversionResult, args: estree.BaseExpression[]): BasicConversionResult {
        const [argResult] = this.convertArgs(args, ['date'], 'date.daysDiff');
        const sql = `DATEDIFF(day, ${argResult.sql}, ${result.sql})`;

        return {
            ...result,
            type: 'integer',
            sql,
        };
    }

    convertDaysInMonthCall(result: BasicConversionResult, args: estree.BaseExpression[]): BasicConversionResult {
        this.convertArgs(args, [], 'date.daysInMonth');

        const sql = `DAY(DATEADD(DD,-1,DATEADD(MM,DATEDIFF(MM,-1,${result.sql}),0)))`;

        return {
            ...result,
            type: 'integer',
            sql,
        };
    }

    convertBegOfIntervalCall(
        result: BasicConversionResult,
        args: estree.BaseExpression[],
        methodName: string,
        intervalName: DateInterval,
    ): BasicConversionResult {
        this.convertArgs(args, [], `date.${methodName}`);
        let sql = '';
        switch (intervalName) {
            case 'year':
                sql = `DATEADD(YEAR, DATEDIFF(YEAR, 0, ${result.sql}), 0)`;
                break;
            case 'month':
                sql = `DATEADD(MONTH, DATEDIFF(MONTH, 0, ${result.sql}), 0)`;
                break;
            case 'week':
                sql = `DATEADD(WEEK, DATEDIFF(WEEK, 0, ${result.sql}), 0)`;
                break;
            case 'quarter':
                sql = `DATEADD(QUARTER, DATEDIFF(QUARTER, 0, ${result.sql}), 0)`;
                break;
            default:
                throw new ConversionError(
                    undefined,
                    `${this.dialect}: cannot convert call to date method ${methodName} to SQL for interval ${intervalName}`,
                );
        }

        return {
            ...result,
            type: 'date',
            sql,
        };
    }

    convertEndOfIntervalCall(
        result: BasicConversionResult,
        args: estree.BaseExpression[],
        methodName: string,
        intervalName: DateInterval,
    ): BasicConversionResult {
        this.convertArgs(args, [], `date.${methodName}`);
        let sql = '';
        switch (intervalName) {
            case 'year':
                sql = `DATEADD(YEAR, DATEDIFF(YEAR, 0, ${result.sql}) + 1, -1)`;
                break;
            case 'month':
                sql = `DATEADD(MONTH, DATEDIFF(MONTH, 0, ${result.sql}) + 1, -1)`;
                break;
            case 'week':
                sql = `DATEADD(WEEK, DATEDIFF(WEEK, 0, ${result.sql}) + 1, -1)`;
                break;
            case 'quarter':
                sql = `DATEADD(QUARTER, DATEDIFF(QUARTER, 0, ${result.sql}) + 1, -1)`;
                break;
            default:
                throw new ConversionError(
                    undefined,
                    `${this.dialect}: cannot convert call to date method ${methodName} to SQL for interval ${intervalName}`,
                );
        }

        return {
            ...result,
            type: 'date',
            sql,
        };
    }

    /**
     * convert to char
     * @param sql
     * @param format
     */
    // eslint-disable-next-line class-methods-use-this
    convertToChar(result: BasicConversionResult, format?: string): string {
        // Usine ISO format 23 YYYY-MM-DD-DD
        // https://docs.microsoft.com/fr-fr/sql/t-sql/functions/cast-and-convert-transact-sql?redirectedfrom=MSDN&view=sql-server-ver15
        return `CONVERT(VARCHAR,${result.sql},${format || '23'})`;
    }

    /**
     * convert to date/datetime UTC format
     * @param sql
     * @param dateTime - true if the conversion is to a datetime
     */
    // eslint-disable-next-line class-methods-use-this
    convertToDate(sql: string, dateTime = false): string {
        return `CONVERT(${dateTime ? 'DATETIME' : 'DATE'},${sql},${dateTime ? 127 : 20})`;
    }
}
