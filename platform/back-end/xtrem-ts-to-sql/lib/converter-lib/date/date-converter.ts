import * as estree from 'estree';
import { ConversionError } from '../../conversion-error';
import { Converter } from '../../converter';
import { registry } from '../../registry';
import { BasicConversionResult } from '../../types';

export type DateFunctions =
    | 'epoch'
    | 'year'
    | 'month'
    | 'day'
    | 'week'
    | 'weekDay'
    | 'yearDay'
    | 'value'
    | 'isLeapYear';

export type DateInterval = 'year' | 'month' | 'week' | 'quarter';

/**
 * Converts date expressions to SQL.
 */
export abstract class DateConverter {
    constructor(protected readonly converter: Converter) {}

    convertArgs = this.converter.convertArgs.bind(this.converter);

    get dialect(): string {
        return this.converter.dialect;
    }

    // Note: we are converting `value` to `epoch` even if they are different.
    // This is to favor efficiency but then value should only be used for comparison.
    abstract dateSqlFields: Record<DateFunctions, (from: string) => string>;

    /**
     * Retrieves the SQL expression for a date method.
     * @param field
     * @param from
     * @returns
     */
    private datePart(field: DateFunctions, from: string): BasicConversionResult {
        if (field === 'value') {
            return {
                type: 'integer',
                sql: `(${this.datePart('year', from).sql} * 10000 + ${this.datePart('month', from).sql} * 100 + ${this.datePart('day', from).sql})`,
            };
        }
        const sql = this.dateSqlFields[field](from);
        if (field === 'isLeapYear') return { type: 'boolean', sql };
        return { type: 'integer', sql };
    }

    private convertDateProperty(result: BasicConversionResult, name: string): BasicConversionResult {
        const from = result.sql;
        if (name in this.dateSqlFields || name === 'value') {
            return this.datePart(name as DateFunctions, from);
        }
        throw new ConversionError(undefined, `${result.path}.${name}: invalid date property`);
    }

    /**
     * Converts a date property to SQL.
     * @param result - The date conversion result.
     * @param memberName - The name of the date property.
     * @returns The converted property as a SQL result.
     */
    convertProperty(result: BasicConversionResult, memberName: string): BasicConversionResult {
        return this.convertDateProperty(result, memberName);
    }

    /**
     * The SQL expression to get the current date for the specific SQL dialect.
     */
    abstract currentDate: string;

    /**
     * Converts a static method call on DateValue to SQL.
     *
     * @param identifier - The name of the static method being called.
     * @param args - The arguments passed to the static method.
     * @returns The result of the conversion.
     */
    convertStaticDateParse(expression: estree.CallExpression, args: estree.BaseExpression[]): BasicConversionResult {
        if (args.length !== 1)
            throw new ConversionError(expression, `Date.parse: expected 1 argument, got ${args.length}`);

        return this.converter.cast(this.converter.convertExpression(args[0]), 'date');
    }

    convertStaticDateToday(expression: estree.CallExpression, args: estree.BaseExpression[]): BasicConversionResult {
        if (args.length !== 0)
            throw new ConversionError(expression, `Date.today: expected 0 arguments, got ${args.length}`);
        return { type: 'date', sql: this.currentDate };
    }

    /**
     * Converts a call to the compare method of Date class.
     * @param result
     * @param args
     * @returns
     */
    protected convertCompareCall(result: BasicConversionResult, args: estree.BaseExpression[]): BasicConversionResult {
        const [argResult] = this.convertArgs(args, ['date'], 'date.compare');
        return {
            ...result,
            type: 'integer',
            sql: `(${result.sql} - ${argResult.sql})`,
        };
    }

    /**
     * Converts the equality check of 2 dates.
     * @param result
     * @param args
     * @returns
     */
    private convertEqualsCall(result: BasicConversionResult, args: estree.BaseExpression[]): BasicConversionResult {
        const [argResult] = this.convertArgs(args, ['date'], 'date.equals');
        return {
            ...result,
            type: 'boolean',
            sql: `(${result.sql} = ${argResult.sql})`,
        };
    }

    /**
     * Converts a call to the isBetween method of Date class.
     * @param result
     * @param args
     * @returns
     */
    protected convertIsBetweenCall(
        result: BasicConversionResult,
        args: estree.BaseExpression[],
    ): BasicConversionResult {
        const argResults = this.convertArgs(args, ['date', 'date'], 'date.isBetween');
        return {
            ...result,
            type: 'boolean',
            sql: `(${result.sql} BETWEEN ${argResults[0].sql} AND  ${argResults[1].sql})`,
        };
    }

    /**
     * Converts a call to the isLeapYear method of Date class.
     * @param result
     * @param args
     * @returns
     */
    protected convertIsLeapYearCall(
        result: BasicConversionResult,
        args: estree.BaseExpression[],
    ): BasicConversionResult {
        this.convertArgs(args, [], 'date.isLeapYear');
        return {
            ...result,
            type: 'boolean',
            sql: this.dateSqlFields.isLeapYear(result.sql),
        };
    }

    /**
     * Converts a call to the isWorkDay method of Date class. Checking if the day of the week is Saturday or Sunday.
     * @param result
     * @param args
     * @returns
     */
    protected convertIsWorkDayCall(
        result: BasicConversionResult,
        args: estree.BaseExpression[],
    ): BasicConversionResult {
        this.convertArgs(args, [], 'date.isWorkDay');
        return {
            ...result,
            type: 'boolean',
            sql: `(${this.dateSqlFields.weekDay(result.sql)} NOT IN (0, 6))`,
        };
    }

    /**
     * Convert a call to a method that adds an interval (week, month, year) to a date.
     * @param result
     * @param args
     * @param methodName
     * @param intervalName
     * @returns
     */
    abstract convertAddIntervalCall(
        result: BasicConversionResult,
        args: estree.BaseExpression[],
        methodName: string,
        intervalName: DateInterval,
    ): BasicConversionResult;

    /**
     * Convert call to addDays method
     * @param result
     * @param args
     * @returns
     */
    abstract convertAddDaysCall(result: BasicConversionResult, args: estree.BaseExpression[]): BasicConversionResult;

    /**
     * Convert call to the daysDiff method
     * @param result
     * @param args
     * @returns
     */
    abstract convertDaysDiffCall(result: BasicConversionResult, args: estree.BaseExpression[]): BasicConversionResult;

    /**
     * Convert call to the daysInMonth method
     * @param result
     * @param args
     */
    abstract convertDaysInMonthCall(
        result: BasicConversionResult,
        args: estree.BaseExpression[],
    ): BasicConversionResult;

    /**
     * Convert call to the begOfYear, begOfMonth, begOfQuarter and begOfWeek methods
     * @param result
     * @param args
     * @param methodName
     * @param intervalName
     * @returns
     */
    abstract convertBegOfIntervalCall(
        result: BasicConversionResult,
        args: estree.BaseExpression[],
        methodName: string,
        intervalName: DateInterval,
    ): BasicConversionResult;

    /**
     * Convert call to the endOfYear, endOfMonth, endOfQuarter and endOfWeek methods
     * @param result
     * @param args
     * @param methodName
     * @param intervalName
     * @returns
     */
    abstract convertEndOfIntervalCall(
        result: BasicConversionResult,
        args: estree.BaseExpression[],
        methodName: string,
        intervalName: DateInterval,
    ): BasicConversionResult;

    /**
     * convert to char
     * @param sql
     */
    abstract convertToChar(result: BasicConversionResult, format?: string): string;

    /**
     * convert to date/datetime UTC format
     * @param sql
     */
    abstract convertToDate(sql: string, datetime?: boolean): string;

    static register(): void {
        registry.registerBuiltInFunctions({
            'DateValue.parse': (converter, expression, args) =>
                converter.dateConverter.convertStaticDateParse(expression, args),
            'DateValue.today': (converter, expression, args) =>
                converter.dateConverter.convertStaticDateToday(expression, args),

            'date.parse': (converter, expression, args) =>
                converter.dateConverter.convertStaticDateParse(expression, args),
            'date.today': (converter, expression, args) =>
                converter.dateConverter.convertStaticDateToday(expression, args),
        });

        registry.registerMethods('date', {
            compare: (converter, result, args) => converter.dateConverter.convertCompareCall(result, args),
            equals: (converter, result, args) => converter.dateConverter.convertEqualsCall(result, args),

            isBetween: (converter, result, args) => converter.dateConverter.convertIsBetweenCall(result, args),
            isLeapYear: (converter, result, args) => converter.dateConverter.convertIsLeapYearCall(result, args),
            isWorkDay: (converter, result, args) => converter.dateConverter.convertIsWorkDayCall(result, args),

            addYears: (converter, result, args) =>
                converter.dateConverter.convertAddIntervalCall(result, args, 'addYears', 'year'),
            addMonths: (converter, result, args) =>
                converter.dateConverter.convertAddIntervalCall(result, args, 'addMonths', 'month'),
            addDays: (converter, result, args) => converter.dateConverter.convertAddDaysCall(result, args),
            addWeeks: (converter, result, args) =>
                converter.dateConverter.convertAddIntervalCall(result, args, 'addWeeks', 'week'),

            daysDiff: (converter, result, args) => converter.dateConverter.convertDaysDiffCall(result, args),
            daysInMonth: (converter, result, args) => converter.dateConverter.convertDaysInMonthCall(result, args),

            begOfYear: (converter, result, args) =>
                converter.dateConverter.convertBegOfIntervalCall(result, args, 'begOfYear', 'year'),
            begOfQuarter: (converter, result, args) =>
                converter.dateConverter.convertBegOfIntervalCall(result, args, 'begOfQuarter', 'quarter'),
            begOfMonth: (converter, result, args) =>
                converter.dateConverter.convertBegOfIntervalCall(result, args, 'begOfMonth', 'month'),
            begOfWeek: (converter, result, args) =>
                converter.dateConverter.convertBegOfIntervalCall(result, args, 'begOfWeek', 'week'),

            endOfYear: (converter, result, args) =>
                converter.dateConverter.convertEndOfIntervalCall(result, args, 'endOfYear', 'year'),
            endOfQuarter: (converter, result, args) =>
                converter.dateConverter.convertEndOfIntervalCall(result, args, 'endOfQuarter', 'quarter'),
            endOfMonth: (converter, result, args) =>
                converter.dateConverter.convertEndOfIntervalCall(result, args, 'endOfMonth', 'month'),
            endOfWeek: (converter, result, args) =>
                converter.dateConverter.convertEndOfIntervalCall(result, args, 'endOfWeek', 'week'),
        });

        registry.registerProperties('date', {
            epoch: (converter, result) => converter.dateConverter.convertDateProperty(result, 'epoch'),
            year: (converter, result) => converter.dateConverter.convertDateProperty(result, 'year'),
            month: (converter, result) => converter.dateConverter.convertDateProperty(result, 'month'),
            day: (converter, result) => converter.dateConverter.convertDateProperty(result, 'day'),
            week: (converter, result) => converter.dateConverter.convertDateProperty(result, 'week'),
            weekDay: (converter, result) => converter.dateConverter.convertDateProperty(result, 'weekDay'),
            yearDay: (converter, result) => converter.dateConverter.convertDateProperty(result, 'yearDay'),
            value: (converter, result) => converter.dateConverter.convertDateProperty(result, 'value'),
            isLeapYear: (converter, result) => converter.dateConverter.convertDateProperty(result, 'isLeapYear'),
        });
    }
}
