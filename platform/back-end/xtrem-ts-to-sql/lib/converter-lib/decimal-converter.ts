import * as estree from 'estree';
import { ConversionError } from '../conversion-error';
import { Converter } from '../converter';
import { registry } from '../registry';
import { BasicConversionResult } from '../types';

/**
 * Converts date expressions to SQL.
 */
export class DecimalConverter {
    constructor(private readonly converter: Converter) {}

    /**
     * Converts a static method call on DateValue to SQL.
     *
     * @param identifier - The name of the static method being called.
     * @param args - The arguments passed to the static method.
     * @returns The result of the conversion.
     */
    convertStaticRoundAtCall(expression: estree.CallExpression, args: estree.BaseExpression[]): BasicConversionResult {
        if (args.length !== 1 && args.length !== 2)
            throw new ConversionError(expression, `Decimal.roundAt: expected 1 or 2 arguments, got ${args.length}`);
        const arg1Sql = this.converter.convertExpression(args[0]).sql;
        const arg2Sql = args.length === 2 ? `${this.converter.convertExpression(args[1]).sql}::INT4` : '0';
        return { type: 'decimal', sql: `ROUND(${arg1Sql}, ${arg2Sql})` };
    }

    static register(): void {
        registry.registerBuiltInFunctions({
            'Decimal.roundAt': (converter, expression, args) =>
                new DecimalConverter(converter).convertStaticRoundAtCall(expression, args),
        });
    }
}
