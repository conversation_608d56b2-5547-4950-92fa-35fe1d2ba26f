import * as estree from 'estree';
import { ConversionError } from '../conversion-error';
import { Converter } from '../converter';
import { registry } from '../registry';
import { BasicConversionResult } from '../types';

/**
 * Converts Math function calls to SQL.
 */
export class MathConverter {
    constructor(private readonly converter: Converter) {}

    convertMathStaticMethod(
        expression: estree.CallExpression,
        sqlName: string,
        args: estree.BaseExpression[],
    ): BasicConversionResult {
        if (args.length === 0) throw new ConversionError(expression, `${sqlName}: cannot convert to SQL: no arguments`);
        const convertedArgs = args.map(arg => this.converter.convertExpression(arg));
        const sql = `${sqlName}(${convertedArgs.map(arg => arg.sql).join(',')})`;
        return { type: convertedArgs[0].type, sql };
    }

    static register(): void {
        registry.registerBuiltInFunctions({
            'Math.abs': (converter, expression, args) =>
                new MathConverter(converter).convertMathStaticMethod(expression, 'ABS', args),
            'Math.floor': (converter, expression, args) =>
                new MathConverter(converter).convertMathStaticMethod(expression, 'FLOOR', args),
            'Math.min': (converter, expression, args) =>
                new MathConverter(converter).convertMathStaticMethod(expression, 'LEAST', args),
            'Math.max': (converter, expression, args) =>
                new MathConverter(converter).convertMathStaticMethod(expression, 'GREATEST', args),
            'Math.round': (converter, expression, args) =>
                new MathConverter(converter).convertMathStaticMethod(expression, 'ROUND', args),
        });
    }
}
