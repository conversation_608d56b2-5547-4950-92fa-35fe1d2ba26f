import * as estree from 'estree';
import { ConversionError } from '../conversion-error';
import { Converter } from '../converter';
import { registry } from '../registry';
import { BasicConversionResult } from '../types';

/**
 * Converts Math function calls to SQL.
 */
export class ContextConverter {
    private static convertConfigurationValueCallExpression(
        converter: Converter,
        expression: estree.CallExpression,
        args: estree.BaseExpression[],
    ): BasicConversionResult {
        if (args.length === 1 && args[0].type === 'Literal') {
            const name = String((args[0] as estree.Literal).value);
            // Simple quotes have to be stripped:
            return converter.convertLiteral(converter.getConfigurationValue(name));
        }
        throw new ConversionError(expression, 'Invalid getConfigurationValue call');
    }

    static register(): void {
        registry.registerBuiltInFunctions({
            'Context.getConfigurationValue': (converter, expression, args) =>
                this.convertConfigurationValueCallExpression(converter, expression, args),
        });
    }
}
