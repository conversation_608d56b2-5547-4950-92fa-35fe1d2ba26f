import * as estree from 'estree';
import { ConversionError } from '../conversion-error';
import { Converter } from '../converter';
import { registry } from '../registry';
import { BasicConversionResult } from '../types';

/**
 * Converts Math function calls to SQL.
 */
export class TypesLibConverter {
    /**
     * Mapping of typesLib.* functions to "classic" expressions
     * For instance, typesLib.strictEq(a, b) will be mapped to a === b
     */
    static convertTypesLibExpression(
        converter: Converter,
        _expression: estree.CallExpression,
        op: string,
        args: estree.BaseExpression[],
    ): BasicConversionResult {
        return converter.convertExpression({
            type: 'BinaryExpression',
            operator: op,
            left: args[0],
            right: args[1],
        } as estree.BinaryExpression);
    }

    static convertTypesLibDecimalConstructorCall(
        converter: Converter,
        expression: estree.CallExpression,
        args: estree.BaseExpression[],
    ): BasicConversionResult {
        if (args.length !== 1)
            throw new ConversionError(expression, `Decimal constructor: expected 1 argument, got ${args.length}`);
        return converter.convertExpression(args[0]);
    }

    static convertStaticMinMaxCall(
        converter: Converter,
        expression: estree.CallExpression,
        sqlName: string,
        args: estree.BaseExpression[],
    ): BasicConversionResult {
        if (args.length < 1)
            throw new ConversionError(expression, `${sqlName}: expected at least 1 argument, got ${args.length}`);
        return converter.convertNaryCallExpression(expression as estree.BaseCallExpression, sqlName);
    }

    static register(): void {
        registry.registerBuiltInFunctions({
            'typesLib.eq': (converter, expression, args) =>
                this.convertTypesLibExpression(converter, expression, '==', args),
            'typesLib.strictEq': (converter, expression, args) =>
                this.convertTypesLibExpression(converter, expression, '===', args),
            'typesLib.ne': (converter, expression, args) =>
                this.convertTypesLibExpression(converter, expression, '!=', args),
            'typesLib.strictNe': (converter, expression, args) =>
                this.convertTypesLibExpression(converter, expression, '!==', args),
            'typesLib.lt': (converter, expression, args) =>
                this.convertTypesLibExpression(converter, expression, '<', args),
            'typesLib.lte': (converter, expression, args) =>
                this.convertTypesLibExpression(converter, expression, '<=', args),
            'typesLib.gt': (converter, expression, args) =>
                this.convertTypesLibExpression(converter, expression, '>', args),
            'typesLib.gte': (converter, expression, args) =>
                this.convertTypesLibExpression(converter, expression, '>=', args),
            'typesLib.add': (converter, expression, args) =>
                this.convertTypesLibExpression(converter, expression, '+', args),
            'typesLib.sub': (converter, expression, args) =>
                this.convertTypesLibExpression(converter, expression, '-', args),
            'typesLib.mul': (converter, expression, args) =>
                this.convertTypesLibExpression(converter, expression, '*', args),
            'typesLib.div': (converter, expression, args) =>
                this.convertTypesLibExpression(converter, expression, '/', args),

            'typesLib.decimal': (converter, expression, args) =>
                this.convertTypesLibDecimalConstructorCall(converter, expression, args),
            'typesLib.min': (converter, expression, args) =>
                this.convertStaticMinMaxCall(converter, expression, 'LEAST', args),
            'typesLib.max': (converter, expression, args) =>
                this.convertStaticMinMaxCall(converter, expression, 'GREATEST', args),
        });
    }
}
