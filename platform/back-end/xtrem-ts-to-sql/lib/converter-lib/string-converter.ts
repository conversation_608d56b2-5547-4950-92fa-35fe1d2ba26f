import * as estree from 'estree';
import { ConversionError } from '../conversion-error';
import { Converter } from '../converter';
import { registry } from '../registry';
import { BasicConversionResult } from '../types';

/**
 * Converts string properties and method calls to SQL.
 */
export class StringConverter {
    constructor(readonly converter: Converter) {}

    /**
     * Converts a string property to SQL.
     * @param result - The string conversion result.
     * @param memberName - The name of the string property.
     * @returns The converted property as a SQL result.
     */
    // eslint-disable-next-line class-methods-use-this
    convertLengthProperty(result: BasicConversionResult): BasicConversionResult {
        return { type: 'integer', sql: `LENGTH(${result.sql})` };
    }

    /**
     * Converts a string method call to SQL.
     *
     * @param result - The string conversion result.
     * @param methodName - The name of the method being called.
     * @param args - The arguments passed to the method call.
     * @returns The SQL result.
     */
    // eslint-disable-next-line class-methods-use-this
    convertTrimCall(result: BasicConversionResult, args: estree.BaseExpression[]): BasicConversionResult {
        if (args.length !== 0)
            throw new ConversionError(undefined, `cannot convert trim to SQL: expected 0 args, got ${args.length}`);
        return Converter.stringResult(`TRIM(${result.sql})`);
    }

    convertToChar(result: BasicConversionResult): string {
        if (result.type === 'date') {
            return this.converter.dateConverter.convertToChar(result);
        }
        switch (this.converter.dialect) {
            case 'postgres':
                return `${result.sql}::TEXT`;
            case 'oracle':
                return `TO_CHAR(${result.sql})`;
            case 'sqlServer':
                return `CONVERT(VARCHAR(max),${result.sql})`;
            default:
                throw new ConversionError(undefined, `${this.converter.dialect}: cannot convert to char`);
        }
    }

    static register(): void {
        registry.registerMethods('string', {
            trim: (converter, result, args) => new StringConverter(converter).convertTrimCall(result, args),
        });
        registry.registerProperties('string', {
            length: (converter, result) => new StringConverter(converter).convertLengthProperty(result),
        });
    }
}
