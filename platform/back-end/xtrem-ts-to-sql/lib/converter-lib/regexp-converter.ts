import * as estree from 'estree';
import { ConversionError } from '../conversion-error';
import { Converter } from '../converter';
import { BasicConversionResult } from '../types';

/**
 * Converts regular expression method calls to SQL.
 */
export class RegExpConverter {
    constructor(private readonly converter: Converter) {}

    // eslint-disable-next-line class-methods-use-this
    private convertPostgres(source: string, flags: string, sqlString: string): BasicConversionResult {
        const sql = `ARRAY_LENGTH(REGEXP_MATCH(${sqlString}, ${source}, ${flags}), 1) > 0`;
        return { type: 'boolean', sql };
    }

    // eslint-disable-next-line class-methods-use-this
    private convertOracle(source: string, flags: string, sqlString: string): BasicConversionResult {
        const sql = `REGEXP_LIKE(${sqlString},${source},${flags})`;
        return { type: 'boolean', sql };
    }

    convertSqlServerRegexToSql(str: string): string {
        if (this.converter.dialect !== 'sqlServer') {
            return str;
        }
        const sanitizeSqlPattern = (regexStr: string, unsanitized: string): string => {
            return regexStr.replace(/(\\.|\.\*?|%|_|\*)/g, (_all, pat) => {
                if (pat[0] === '\\') return pat[1] === '$' ? '$$' : pat[1];
                if (pat[0] === '.') return pat.length === 2 ? '%' : '_';
                if (pat === '%' || pat === '_') return `$${pat}`;

                throw new Error(`${unsanitized}: regular expression cannot be converted to SQL: * must be escaped`);
            });
        };
        // remove ^ and $ markers
        const starting = str[0] === '^';
        let s = starting ? str.substring(1) : str;
        const ending = /(^|[^\\])(\\\\)*\$$/.test(str);
        s = ending ? s.substring(0, s.length - 1) : s;

        // throw if regex special chars are not escaped
        // eslint-disable-next-line no-useless-escape
        const m = /(?:^|[^\\])([\^\$\+\?\|\(\)\{\}\[\]]|\\\w)/.exec(s);
        if (m) throw new Error(`${str}: regular expression cannot be converted to SQL: ${m[1]} must be escaped`);

        // replace special chars that translate to SQL, and escape SQL pattern and escape chars
        let sql = sanitizeSqlPattern(s, str);
        // add % at beginning or end if necessary.
        if (!starting) sql = `%${sql}`;
        if (!ending) sql = `${sql}%`;
        return sql;
    }

    private convertSqlServer(
        source: string,
        flags: string,
        sqlString: string,
        options?: { isParam?: boolean },
    ): BasicConversionResult {
        // TODO: is the flag is an argument/parameter, we need to build a mechanism to fetch it from the converter, do this comparison
        // correctly in the future. Default collation is case insensitive is the flags is an argument/parameter.
        const collation =
            flags.indexOf('i') >= 0 || options?.isParam
                ? 'COLLATE Latin1_General_CI_AS'
                : 'COLLATE Latin1_General_CS_AS';
        let sql = '';
        if (options?.isParam) {
            sql = `${sqlString} ${collation} LIKE ${source} escape '$'`;
        } else {
            sql = `${sqlString} ${collation} LIKE ${this.converter.convertLiteral(this.convertSqlServerRegexToSql(source)).sql} escape '$'`;
        }
        return { type: 'boolean', sql };
    }

    convertRegex(
        regexStr: string,
        flagsStr: string,
        result: BasicConversionResult,
        options?: { isParam?: boolean },
    ): BasicConversionResult {
        const sql = this.converter.stringConverter.convertToChar(result);
        let source = '';
        let flags = flagsStr;

        if (!options?.isParam) {
            // convert regex to SQL. convertLiteral may return a SQL argument, so we need to check the dialect
            // as SQL Server we will use the raw regex string to parse to support SQL Server format and options to determin the collation
            // We will call convertLiteral in convertSqlServer passing the parsed regex string
            if (this.converter.dialect !== 'sqlServer') {
                source = this.converter.convertLiteral(regexStr).sql;
                flags = this.converter.convertLiteral(flagsStr ?? '').sql;
            }
        } else {
            source = regexStr;
        }

        switch (this.converter.dialect) {
            case 'postgres': {
                return this.convertPostgres(source, flags, sql);
            }
            case 'oracle': {
                return this.convertOracle(source, flags, sql);
            }
            case 'sqlServer':
                return this.convertSqlServer(regexStr, flagsStr, sql, options);
            default:
                throw new ConversionError(undefined, `Unsupported dialect: ${this.converter.dialect}`);
        }
    }

    /**
     * Converts a RegExp method call to SQL.
     *
     * @param expression - The method call expression node.
     * @param regexp - The regular expression to be used for conversion.
     * @param methodName - The name of the method being called.
     * @param args - The arguments passed to the method call.
     * @returns The result of the conversion.
     */
    convertMethodCall(
        expression: estree.Node,
        regexp: RegExp,
        methodName: string,
        args: estree.BaseExpression[],
    ): BasicConversionResult {
        if (methodName !== 'test') throw new ConversionError(expression, 'Unsupported RegExp method');
        if (args.length !== 1)
            throw new ConversionError(
                expression,
                `${methodName}: cannot convert to SQL: expected 1 argument, got ${args.length}`,
            );

        const arg = args[0];

        const argResult = this.converter.convertExpression(arg);

        // /some regexp/.test(exp) --> REGEXP_MATCHES(exp, regex, flags)
        const source = regexp.source;
        const flags = regexp.flags;

        return this.convertRegex(source, flags, argResult);
    }
}
