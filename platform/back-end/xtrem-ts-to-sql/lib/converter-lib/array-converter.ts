import * as estree from 'estree';
import { ConversionError } from '../conversion-error';
import { Converter } from '../converter';
import { BasicConversionResult } from '../types';

/**
 * Converts array expressions to SQL.
 */
export class ArrayConverter {
    constructor(private readonly converter: Converter) {}

    /**
     * Converts a method call on an array literal to a SQL query.
     * @param object - The array literal expression.
     * @param methodName - The name of the method being called.
     * @param args - The arguments passed to the method.
     * @returns The result of the conversion.
     */
    convertLiteralMethodCall(
        object: estree.ArrayExpression,
        methodName: string,
        args: estree.BaseExpression[],
    ): BasicConversionResult {
        if (methodName === 'includes') {
            // ['a', 'b'].includes(exp) --> exp in ('a', 'b')
            if (args.length !== 1) throw new ConversionError(object, 'includes: expected 1 argument');
            const arg = args[0];
            const values = object.elements.map(element =>
                element ? this.converter.convertExpression(element).sql : 'NULL',
            );
            const argSql = this.converter.convertExpression(arg).sql;
            return Converter.booleanResult(`(${argSql} IN (${values.join(',')}))`);
        }
        throw new ConversionError(undefined, `Unsupported array method ${methodName}`);
    }
}
