import { Dict } from '@sage/xtrem-shared';
import * as estree from 'estree';
import * as _ from 'lodash';
import { ConversionError } from '../conversion-error';
import { Converter } from '../converter';
import { registry } from '../registry';
import { Alias, BasicConversionResult, GenericConversionResult } from '../types';

export interface CollectionWhereResult extends GenericConversionResult {
    extraType: 'collectionWhere';
    whereCallback: estree.ArrowFunctionExpression;
}
/**
 * Converts collection properties and method calls to SQL.
 */
export class CollectionConverter {
    constructor(private readonly converter: Converter) {}

    private convertCollectionCallback(
        collection: GenericConversionResult,
        callback: estree.ArrowFunctionExpression,
    ): {
        callbackResult: BasicConversionResult;
        aliases: string;
        joinCondition: string;
    } {
        const { callbacksResults, aliases, joinCondition } = this.convertCollectionCallbacks(collection, {
            callback,
        });
        return { callbackResult: callbacksResults.callback, aliases, joinCondition };
    }

    private convertQuantifiedCallback(
        collection: GenericConversionResult,
        callback: estree.ArrowFunctionExpression,
        quantifier: 'every' | 'some',
    ): BasicConversionResult {
        const results = this.convertCollectionCallback(collection, callback);
        const callbackSql = results.callbackResult.sql;

        const condition = quantifier === 'every' ? `NOT(${callbackSql})` : callbackSql;
        // every x satisfies y <=> none x statisfies not y

        const subQuery = `(SELECT COUNT(*) FROM ${results.aliases} WHERE ${Converter.and([
            results.joinCondition,
            condition,
        ])})`;
        switch (quantifier) {
            case 'some':
                return Converter.booleanResult(`(${subQuery} >= 1)`);
            case 'every':
                return Converter.booleanResult(`(${subQuery} = 0)`);
            default:
                throw new ConversionError(undefined, `invalid quantifier: ${quantifier}`);
        }
    }

    private convertQuantifiedExpression(
        collection: GenericConversionResult,
        args: estree.BaseExpression[],
        quantifier: 'every' | 'some',
    ): BasicConversionResult {
        if (args.length !== 1) {
            throw new ConversionError(undefined, `cannot convert ${quantifier}: expected 1 arg, got ${args.length}`);
        }
        const arg = args[0];
        if (arg.type !== 'ArrowFunctionExpression') {
            throw new ConversionError(
                undefined,
                `cannot convert ${quantifier}: expected arrow function, got ${arg.type}`,
            );
        }
        return this.convertQuantifiedCallback(collection, arg as estree.ArrowFunctionExpression, quantifier);
    }

    convertSumCallbacks(
        collection: GenericConversionResult,
        whereCallback: estree.ArrowFunctionExpression | undefined,
        sumCallback: estree.ArrowFunctionExpression,
    ): BasicConversionResult {
        const { callbacksResults, joinCondition, aliases } = this.convertCollectionCallbacks(collection, {
            sum: sumCallback,
            ...(whereCallback && { where: whereCallback }),
        });

        const sumResult = callbacksResults.sum;
        const whereResult = callbacksResults.where;

        if (whereCallback && whereResult) {
            this.converter.checkResultType(whereCallback, whereResult, 'boolean');
        }

        const whereCondition = Converter.and([joinCondition, whereResult?.sql]);

        return {
            type: sumResult.type,
            sql: `(SELECT COALESCE(SUM(${sumResult.sql}), 0) FROM ${aliases} WHERE ${whereCondition})`,
        };
    }

    private convertSumExpression(
        collection: GenericConversionResult,
        args: estree.BaseExpression[],
    ): BasicConversionResult {
        if (args.length !== 1)
            throw new ConversionError(undefined, `cannot convert sum: expected 1 arg, got ${args.length}`);
        const arg = args[0];
        if (arg.type !== 'ArrowFunctionExpression') {
            throw new ConversionError(undefined, `cannot convert sum: expected arrow function, got ${arg.type}`);
        }

        return this.convertSumCallbacks(collection, undefined, arg as estree.ArrowFunctionExpression);
    }

    convertCollectionCallbacks(
        collection: GenericConversionResult,
        callbacks: Dict<estree.ArrowFunctionExpression>,
    ): {
        callbacksResults: Dict<BasicConversionResult>;
        aliases: string;
        joinCondition: string;
    } {
        let paramName = '';
        Object.values(callbacks).forEach(callback => {
            if (callback.params.length !== 1 || callback.params[0].type !== 'Identifier')
                throw new ConversionError(callback, `invalid parameters type is ${callback.params[0].type} `);
            if (paramName && callback.params[0].name !== paramName)
                throw new ConversionError(
                    callback,
                    `different parameter name ${paramName} - ${callback.params[0].name}`,
                );
            paramName = callback.params[0].name;
        });

        return this.converter.withSubQueryScope(() => {
            this.converter.makeAliasAndJoin(collection);
            this.converter.variableScope.set(paramName, {
                ...collection,
                type: 'reference',
                property: undefined,
                sql: collection.alias,
            });
            return {
                callbacksResults: _.mapValues(callbacks, callback =>
                    this.converter.convertArrowFunctionExpression(callback),
                ),
            };
        });
    }

    private convertTakeOneCallback(
        collection: GenericConversionResult,
        callback: estree.ArrowFunctionExpression,
    ): BasicConversionResult {
        const param = callback.params[0];
        if (!param || param.type !== 'Identifier') throw new ConversionError(param, 'invalid parameter');

        // Transform the callback to a SQL expression (callbackResult.sql)
        const { aliases, joinCondition, innerAlias, callbackResult } = this.converter.withSubQueryScope(() => {
            this.converter.makeAliasAndJoin(collection);
            this.converter.variableScope.set(param.name, {
                ...collection,
                type: 'reference',
                property: undefined,
                sql: collection.alias,
            });
            return {
                callbackResult: this.converter.convertArrowFunctionExpression(callback),
            };
        });
        this.converter.checkResultType(callback, callbackResult, 'boolean');

        // We use `SELECT *` here because we don't know yet which columns will be used in the outer scope.
        // This should not be an issue because the inner query does not move the data outside of the database.
        // The outer query will select an explicit list of columns, so the `*` will not be used in the final result.
        const lateral = `
        (SELECT ${innerAlias.alias}.*
            FROM ${aliases}
            WHERE ${callbackResult.sql}
                AND ${joinCondition}
            ORDER BY ${innerAlias.alias}._tenant_id, ${innerAlias.alias}._id  ASC
            LIMIT 1
        )`;

        // We can reuse the inner alias for the lateral alias because the inner and outer scopes don't conflict.
        // This allows us to use joinCondition as is, without having to rename its aliases.
        const lateralAlias = {
            ...innerAlias,
            // Join condition is TRUE because the condition is already in the lateral query
            join: { ...innerAlias.join, isNullable: true, lateral, condition: 'TRUE' },
        } as Alias;
        this.converter.pushAlias(lateralAlias);

        // Build the result object
        // The path could be built by hashing the parent path and the callback (after stripping pos), instead of being random,
        // but it's not worth the complexity as it is unlikely to have the exact same takeOne call twice in the same query.
        const result = {
            ...collection,
            path: this.converter.getUniquePath(collection.path),
            type: 'reference',
            isNullable: true,
            sql: `${lateralAlias.alias}._id`,
        } as const;
        this.converter.results.set(result.path, result);

        return result;
    }

    // eslint-disable-next-line class-methods-use-this
    private convertWhereExpression(
        collection: GenericConversionResult,
        args: estree.BaseExpression[],
    ): CollectionWhereResult {
        if (args.length > 1) throw new ConversionError(undefined, 'too many arguments to collection.where call');
        const callback = args[0];
        if (callback && callback.type !== 'ArrowFunctionExpression')
            throw new ConversionError(callback, 'invalid callback');

        return {
            ...collection,
            extraType: 'collectionWhere',
            whereCallback: callback as estree.ArrowFunctionExpression,
        } as CollectionWhereResult;
    }

    private convertWhereSumExpression(
        whereResult: CollectionWhereResult,
        args: estree.BaseExpression[],
    ): BasicConversionResult {
        if (args.length > 1) throw new ConversionError(undefined, 'too many arguments to sum call');
        const callback = args[0];
        if (callback && callback.type !== 'ArrowFunctionExpression')
            throw new ConversionError(callback, 'invalid callback');

        return this.convertSumCallbacks(
            whereResult,
            whereResult.whereCallback,
            callback as estree.ArrowFunctionExpression,
        );
    }

    private convertTakeOneExpression(
        collection: GenericConversionResult,
        args: estree.BaseExpression[],
    ): BasicConversionResult {
        if (args.length > 1) throw new ConversionError(undefined, 'too many arguments to sum call');
        const callback = args[0];
        if (callback && callback.type !== 'ArrowFunctionExpression')
            throw new ConversionError(callback, 'invalid callback');

        return this.convertTakeOneCallback(collection, callback as estree.ArrowFunctionExpression);
    }

    private convertLengthProperty(collection: GenericConversionResult): BasicConversionResult {
        const results = this.converter.withSubQueryScope(() => {
            this.converter.makeAliasAndJoin(collection);
            return { argResult: { type: 'boolean', sql: '1' } };
        });

        const subQuery = `(SELECT COUNT(*) FROM ${results.aliases} WHERE ${Converter.and([results.joinCondition])})`;
        return { type: 'integer', sql: subQuery };
    }

    static register(): void {
        registry.registerMethods('collection', {
            every: (converter, result, args) =>
                new CollectionConverter(converter).convertQuantifiedExpression(result, args, 'every'),
            some: (converter, result, args) =>
                new CollectionConverter(converter).convertQuantifiedExpression(result, args, 'some'),
            takeOne: (converter, result, args) =>
                new CollectionConverter(converter).convertTakeOneExpression(result, args),
            where: (converter, result, args) => new CollectionConverter(converter).convertWhereExpression(result, args),
            sum: (converter, result, args) => new CollectionConverter(converter).convertSumExpression(result, args),
        });

        registry.registerProperties('collection', {
            length: (converter, result) => new CollectionConverter(converter).convertLengthProperty(result),
        });

        registry.registerMethods('collectionWhere', {
            sum: (converter, result, args) =>
                new CollectionConverter(converter).convertWhereSumExpression(result as CollectionWhereResult, args),
        });
    }
}
