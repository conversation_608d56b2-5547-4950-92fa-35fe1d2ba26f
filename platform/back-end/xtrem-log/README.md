# xtrem-log

Logging service

## Use

The _@sage/xtrem-log_ module must be referenced in your _package.json_ file

**Important**

If the configuration (_@sage/xtrem-config_) is not loaded **before the first** use of this API, all the logs will only be written to console but no file will be written.

```ts
import { Logger } from '@sage/xtrem-core';
const logger = Logger.getLogger(__filename, 'test');
...
logger.info('my info');
logger.warn('my warning');
logger.error('my error');
logger.debug(() => 'my debug');
logger.verbose(() => 'my verbose');
...
```

**Important** : _\_\_filename_ should **always** be provided as the first argument.

Logs will always be written to the console (console.log / console.error).

If the configuration is loaded, they will also be written to a file. By default, this file will be located in a _/logs_ sub-folder of the folder that contains the configuration.

## Manually set the log level

```ts
logger.logLevel = 'verbose';
```

## Check if a logLevel is active

```ts
if (logger.isActive('error')) console.log('Errors will be logged');
```

## Configure the log levels in the xtrem-config.yml file

```yml
logs:
  outputFolder: c:\logLocation
  filenamePrefix: em-core
  domains:
    sage/xtrem-log/test:
      level: verbose
    ...
```

-   logs|outputFolder : the folder in which the logs will be written (they will be in a _logs_ subFolder). When no setting is provided, logs are written in the folder xxx/logs, where xxx is the folder that contains the _.yml_ configuration file.
-   logs|filenamePrefix : the prefix that will be used for the logs filename. Default value is _xtrem.server_.
-   logs|domains : contains all the domain definitions.
-   logs|domains|sage/xtrem-log/test|level : defines the level of the log domain _sage/xtrem-log/test_. By default, all the loggers have the _info_ level.

## Colorization

-   Errors are logged in _red_.
-   Warnings are logged in _orange_.
-   Verbose and debug logs are in _light blue_.

You can also personalize your messages with the _Colors_ static class :

```ts
import { Colors } from '@sage/xtrem-log';
..
logger.info(`My ${Colors.red('important')} information`);
```

will log _My **important** information_ with **important** in _red_.

## Profiling

The _error, warn, info, verbose & debug_ functions can be provided with 2 thresholds (low / high).

```ts
const profiler = logger.info('Process is started', 100, 500);
setTimeout(() => {
    profiler.success();
}, 400);
```

`const profiler = logger.info('Process is started', 100, 500)` logs _Process is started_ (with level _info_) and starts a profiling session for a process whose duration is expected to be lower than 100 ms.

`profiler.success()` ends the profiling session and logs (with the same level as the one use to create the _profiler_ variable, here _info_) a formatted message containing the process duration. If the process fails, you can call `profiler.fail()` to log an error.

Here, sth like _Event #5 is over (400 ms), count=1_ will be logged.

-   As the duration of the process is in the range [100, 500] - as defined by `logger.info('...', 100, 500)` -, the string _(400ms)_ will be colorized in _orange_.
-   If the process should be longer than 500 ms, the duration will be colorized in _red_.
-   If the process is faster than 100 ms, no colorization will be applied.

### Retrieving statistics

Each time a profiling session is started, some statistics are collected on the logger. You can fetch them with the `logger.statistics` accessor.
This will return an object with the following members :

-   count : the number of profiled sessions
-   totalDuration (in ms) : the overall profiled sessions durations
-   meanDuration (in ms) : the mean duration of profiled sessions
-   standardDeviation (in ms) : the standard deviation of profiled sessions
