{"name": "@sage/xtrem-log", "description": "Xtrem Logging service", "version": "58.0.2", "author": "Sage", "license": "UNLICENSED", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/index.js", "typings": "build/package-definition.d.ts", "dependencies": {"@sage/xtrem-shared": "workspace:*", "logform": "^2.5.1", "source-map-support": "^0.5.12", "triple-beam": "^1.4.1", "winston": "^3.13.0", "winston-daily-rotate-file": "^5.0.0", "winston-transport": "^4.5.0"}, "devDependencies": {"@sage/xtrem-dts-bundle": "workspace:*", "@types/chai": "^4.3.6", "@types/mocha": "^10.0.1", "@types/node": "^22.10.2", "@types/sinon": "^17.0.0", "@types/triple-beam": "^1.3.5", "c8": "^10.1.2", "chai": "^4.3.10", "cross-env": "^7.0.3", "eslint": "^8.49.0", "mocha": "^10.8.2", "sinon": "^21.0.0", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "~5.8.3"}, "scripts": {"build": "tsc -b -v . && pnpm dts-bundle", "build:binary": "echo 'Binary mode is not available for this package, falling back to normal build.' && pnpm build", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build junit-report*", "dts-bundle": "xtrem-dts-bundle", "lint": "eslint -c .eslintrc.js --ext .ts lib test", "test": "mocha --recursive --exit \"test/**/*@(-|.)test.ts\"", "test:ci": "cross-env JUNIT_REPORT_PATH=junit-report-log.xml JUNIT_REPORT_NAME='xtrem-log' c8 --reporter=lcov --reporter=json --reporter=cobertura --reporter=text mocha  --recursive --exit \"test/**/*@(-|.)ts\" --reporter mocha-jenkins-reporter"}, "c8": {"reporter": ["json", "lcov", "text-summary"], "extension": [".ts"], "exclude": ["**/*.d.ts", "**/*-test.ts", "**/colors.ts"]}, "gitHead": "f0406ede1639145fdb322ebdf823bc87abddf4b8"}