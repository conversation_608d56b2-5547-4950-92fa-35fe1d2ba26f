import { Colors, Logger } from './index';

const logger = Logger.getLogger(__filename, 'test');

function showLevels() {
    console.log('Current level = ', logger.logLevel);
    console.log('>>>> error is active = ', logger.isActive('error'));
    console.log('>>>> warn is active = ', logger.isActive('warn'));
    console.log('>>>> info is active = ', logger.isActive('info'));
    console.log('>>>> verbose is active = ', logger.isActive('verbose'));
    console.log('>>>> debug is active = ', logger.isActive('debug'));
}

logger.info(`My ${Colors.blue('information')}`);
logger.warn('My warning');
logger.error('test error');
logger.info(() => 'info from callback');
logger.verbose(() => 'should not appear');
logger.logLevel = 'verbose';
logger.verbose(() => 'should appear');
logger.logLevel = 'debug';
logger.info('should not appear');
logger.debug(() => 'debug message');
const profiler = logger.verbose(() => 'Process is started', 100, 500);
setTimeout(() => {
    profiler.success('Process is over');
    console.log('STATS ', logger.statistics);
}, 600);
showLevels();

function doSucceedsSilently() {
    logger.info('do with successful body: no stacktrace below');
    if (logger.do(() => 'hello') !== 'hello') {
        throw new Error(`logger.do return test failed`);
    }
}

function doReturnsAltResult() {
    logger.info('do returning alternate result on error: stacktrace should follow');
    if (
        logger.do(
            () => {
                throw new Error('hello');
            },
            err => '!' + err.message,
        ) !== '!hello'
    ) {
        throw new Error(`logger.do catch handler test failed`);
    }
}

function doRethrowsOriginalError() {
    logger.info('do rethrows an error: stacktrace should follow');
    try {
        logger.do(() => {
            throw new Error('hello');
        });
        throw new Error(`do did not rethrow`);
    } catch (err) {
        if (err.message !== 'hello') throw new Error(`do did not rethrow original error`);
    }
}

function doRethrowsDifferentError() {
    logger.info('do rethrows a different error: stacktrace should follow');
    try {
        logger.do(
            () => {
                throw new Error('hello');
            },
            err => {
                throw new Error(`${err.message} world`);
            },
        );
        throw new Error(`do did not rethrow`);
    } catch (err) {
        if (err.message !== 'hello world') throw new Error(`do did not rethrow new error`);
    }
}

doSucceedsSilently();
doReturnsAltResult();
doRethrowsOriginalError();
doRethrowsDifferentError();
