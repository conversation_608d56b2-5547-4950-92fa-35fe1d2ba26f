import * as fs from 'fs';
import * as path from 'path';
import { install as sourceMapInstall } from 'source-map-support';

const sourceMapSupportOptions = {
    retrieveSourceMap(source: string) {
        // source map exists in the supplied source path
        if (fs.existsSync(`${source}.map`)) {
            return {
                url: source,
                map: fs.readFileSync(`${source}.map`, 'utf8'),
            };
        }

        // get file URL from @sage position
        //  e.g. source = /home/<USER>/work/1/s/xtrem-platform/@sage/xtrem-cli/lib/cli.js
        //  fileUrl = @sage/xtrem-cli/lib/cli.js
        const fileUrl = source.substring(source.indexOf('@sage'));

        // filePath will be sth like /app/xtrem/xtrem-services/@sage/xtrem-sales/node_modules/@sage/xtrem-cli/lib/cli.js
        const filePath = path.join(process.cwd(), 'node_modules', fileUrl);

        // check if file path exists the cwd node_modules
        if (fs.existsSync(`${filePath}.map`)) {
            return {
                url: filePath,
                map: fs.readFileSync(`${filePath}.map`, 'utf8'),
            };
        }

        // rootFilePath will be sth like /app/xtrem/xtrem-services/node_modules/@sage/xtrem-cli/lib/cli.js
        const rootFilePath = path.join(process.cwd(), '..', '..', 'node_modules', fileUrl);

        // check if file path exists the root node_modules
        if (fs.existsSync(`${rootFilePath}.map`)) {
            return {
                url: rootFilePath,
                map: fs.readFileSync(`${rootFilePath}.map`, 'utf8'),
            };
        }

        // source map is not found anywhere
        return null;
    },
};

/**
 * install source-map-support with value for retrieveSourceMap function that will be used to retrieve the source map file content
 * @param options
 */
export function sourceMapSetup(options: any = {}): void {
    sourceMapInstall({ ...sourceMapSupportOptions, ...options });
}
