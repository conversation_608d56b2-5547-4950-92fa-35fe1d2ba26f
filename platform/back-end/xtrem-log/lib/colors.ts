enum ColorEnum {
    black = 30,
    red = 31,
    green = 32,
    yellow = 33,
    blue = 34,
    magenta = 35,
    cyan = 36,
    lightGray = 37,
    default = 39,
    darkGray = 90,
    lightRed = 91,
    lightGreen = 92,
    lightYellow = 93,
    lightBlue = 94,
    lightMagenta = 95,
    lightCyan = 96,
    white = 97,
}

export abstract class Colors {
    private static format(color: ColorEnum, message: string) {
        return `\x1b[${color}m${message}\x1b[${ColorEnum.default}m`;
    }

    static black(message: string) {
        return Colors.format(ColorEnum.black, message);
    }

    static red(message: string) {
        return Colors.format(ColorEnum.red, message);
    }

    static green(message: string) {
        return Colors.format(ColorEnum.green, message);
    }

    static yellow(message: string) {
        return Colors.format(ColorEnum.yellow, message);
    }

    static blue(message: string) {
        return Colors.format(ColorEnum.blue, message);
    }

    static magenta(message: string) {
        return Colors.format(ColorEnum.magenta, message);
    }

    static cyan(message: string) {
        return Colors.format(ColorEnum.cyan, message);
    }

    static white(message: string) {
        return Colors.format(ColorEnum.white, message);
    }

    static lightGray(message: string) {
        return Colors.format(ColorEnum.lightGray, message);
    }

    static default(message: string) {
        return Colors.format(ColorEnum.default, message);
    }

    static darkGray(message: string) {
        return Colors.format(ColorEnum.darkGray, message);
    }

    static lightRed(message: string) {
        return Colors.format(ColorEnum.lightRed, message);
    }

    static lightGreen(message: string) {
        return Colors.format(ColorEnum.lightGreen, message);
    }

    static lightYellow(message: string) {
        return Colors.format(ColorEnum.lightYellow, message);
    }

    static lightBlue(message: string) {
        return Colors.format(ColorEnum.lightBlue, message);
    }

    static lightMagenta(message: string) {
        return Colors.format(ColorEnum.lightMagenta, message);
    }

    static lightCyan(message: string) {
        return Colors.format(ColorEnum.lightCyan, message);
    }

    /** Removes all the color markers from a string */
    static clean(message: string) {
        return message.replace(/\\x1b\[\d+m/g, '');
    }
}
