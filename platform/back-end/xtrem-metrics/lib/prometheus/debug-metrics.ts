import { ConfigManager } from '@sage/xtrem-config';
import { AnyValue, AsyncResponse, isDevelopmentConfig, isEnvVarTrue } from '@sage/xtrem-shared';
import { allMetricsEnabled, CustomCounters } from './custom-metrics';

const debugMetricsEnabled = allMetricsEnabled || isEnvVarTrue(process.env.XTREM_DEBUG_METRICS_ENABLED);

/**
 * This class can be used to instrument some piece of code for debugging
 *
 * @example
 * ```
 * DebugMetrics.withMetrics('myDomain', 'myKey', () => ....)
 * ```
 * 
 * To enable the recording of metrics:
 * - environment variable: set either XTREM_ALL_METRICS_ENABLED or XTREM_DEBUG_METRICS_ENABLED to 1
 * - xtrem-config.yml file: set deploymentMode to 'development'
 * - xtrem-config.yml file: enable the domain you want to use:
 * ```
debugMetrics:
      myDomain: true
 * ```
 */
export class DebugMetrics {
    private static readonly counter = new CustomCounters<{ domain: string; key: string }>({
        // Do not include tenant id for now. It would be nice but would make the matrix larger.
        name: 'debug',
        help: 'Debug metrics',
        labelNames: ['domain', 'key'],
        isEnabled: debugMetricsEnabled,
    });

    private static _isEnabled(domain: string): boolean {
        if (!DebugMetrics.counter.isEnabled) return false;

        if (!isDevelopmentConfig(ConfigManager.current)) return false;

        if (ConfigManager.current.debugMetrics == null) return false;

        if (!ConfigManager.current.debugMetrics[domain]) {
            // This debug metrics are not enabled in the xtrem-config file
            return false;
        }
        return true;
    }

    static withMetrics<ResultT extends AnyValue>(
        domain: string,
        key: string,
        body: () => AsyncResponse<ResultT>,
    ): Promise<ResultT> {
        if (!DebugMetrics._isEnabled(domain)) return Promise.resolve(body());

        return DebugMetrics.counter.withMetrics(
            {
                domain,
                key,
            },
            body,
        );
    }
}
