import { ConfigManager } from '@sage/xtrem-config';
import { AnyValue, AsyncResponse, isDevelopmentConfig, isEnvVarTrue, LogicError } from '@sage/xtrem-shared';
import { HistogramConfiguration, LabelValues, Histogram as PromHistogram } from 'prom-client';
import { allMetricsEnabled } from './custom-metrics';

const extraMetricsEnabled = allMetricsEnabled || isEnvVarTrue(process.env.XTREM_EXTRA_METRICS_ENABLED);

const maxAllowed = 20;

export interface HistogramOptions<LabelNameT extends string> extends HistogramConfiguration<LabelNameT> {
    enabledInProduction?: boolean;
}

export class Histogram<LabelNameT extends string> {
    static createdCount = 0;

    readonly #isEnabled: boolean;

    #histogram: PromHistogram;

    constructor(options: HistogramOptions<LabelNameT>) {
        if (Histogram.createdCount === maxAllowed) throw new LogicError('too many histograms');
        Histogram.createdCount += 1;

        this.#histogram = new PromHistogram(options);
        this.#isEnabled =
            extraMetricsEnabled && (isDevelopmentConfig(ConfigManager.current) || !!options.enabledInProduction);
    }

    get isEnabled(): boolean {
        return this.#isEnabled;
    }

    async withMetrics<ResultT extends AnyValue>(
        labelValues: LabelValues<LabelNameT>,
        body: () => AsyncResponse<ResultT>,
    ): Promise<ResultT> {
        if (!this.isEnabled) return body();

        const end = this.#histogram.startTimer(labelValues);
        try {
            return await body();
        } finally {
            end();
        }
    }
}
