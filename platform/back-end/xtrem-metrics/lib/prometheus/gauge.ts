import { ConfigManager } from '@sage/xtrem-config';
import { AnyValue, AsyncResponse, isDevelopmentConfig, isEnvVarTrue, LogicError } from '@sage/xtrem-shared';
import { GaugeConfiguration, LabelValues, Gauge as PromGauge } from 'prom-client';
import { allMetricsEnabled } from './custom-metrics';

const extraMetricsEnabled = allMetricsEnabled || isEnvVarTrue(process.env.XTREM_EXTRA_METRICS_ENABLED);

const maxAllowed = 20;

export interface GaugeOptions<LabelNameT extends string> extends GaugeConfiguration<LabelNameT> {
    enabledInProduction?: boolean;
}

export class Gauge<LabelNameT extends string> {
    static createdCount = 0;

    readonly #isEnabled: boolean;

    #gauge: PromGauge;

    constructor(options: GaugeOptions<LabelNameT>) {
        if (Gauge.createdCount === maxAllowed) throw new LogicError('too many gauges');
        Gauge.createdCount += 1;

        this.#gauge = new PromGauge(options);
        this.#isEnabled =
            extraMetricsEnabled && (isDevelopmentConfig(ConfigManager.current) || !!options.enabledInProduction);
    }

    get isEnabled(): boolean {
        return this.#isEnabled;
    }

    async withMetrics<ResultT extends AnyValue>(
        labelValues: LabelValues<LabelNameT>,
        body: () => AsyncResponse<ResultT>,
    ): Promise<ResultT> {
        if (!this.isEnabled) return body();

        const end = this.#gauge.startTimer(labelValues);
        try {
            return await body();
        } finally {
            end();
        }
    }
}
