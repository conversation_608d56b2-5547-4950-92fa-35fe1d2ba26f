import { loggers } from '../loggers';

const logger = loggers.newRelic;

export const isNewrelicDisabled = process.env.NEW_RELIC_ENABLED !== 'true';

export function recordCustomEventToNewrelic(eventName: string, attributes: Record<string, any>): void {
    if (isNewrelicDisabled) {
        return;
    }
    try {
        // eslint-disable-next-line global-require
        require('newrelic').recordCustomEvent(eventName, attributes);
    } catch ({ message }) {
        // log and swallow, we don't want this event emitter to disturb the processing of the request
        logger.warn(`Failed to send custom event to newrelic ${message}`);
    }
}

export function addCustomAttributesToNewrelic(attributes: Record<string, any>): void {
    if (isNewrelicDisabled) {
        return;
    }
    try {
        // eslint-disable-next-line global-require
        require('newrelic').addCustomAttributes(attributes);
    } catch ({ message }) {
        // log and swallow, we don't want this event emitter to disturb the processing of the request
        logger.warn(`Failed to add custom attributes to newrelic ${message}`);
    }
}
