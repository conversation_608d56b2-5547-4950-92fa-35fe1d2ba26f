import { loggers } from '../loggers';

const logger = loggers.newRelic;
const isNewrelicEnabled = process.env.NEW_RELIC_ENABLED === 'true';

export function setWebTransactionName(callback: () => string | null): void {
    try {
        if (!isNewrelicEnabled) {
            return;
        }

        const name = callback();
        if (name) {
            // eslint-disable-next-line global-require
            require('newrelic').setTransactionName(name);
        }
    } catch ({ stack, message }) {
        // log and swallow, we don't want this event emiter to disturb the processing of the request
        logger.warn(`Failed to send transaction name to newrelic ${stack || message}`);
    }
}
