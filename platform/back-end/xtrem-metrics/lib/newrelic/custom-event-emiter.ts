import { Dict } from '@sage/xtrem-shared';
import { loggers } from '../loggers';
import { MetricsContext } from '../types';
import { isNewrelicDisabled, recordCustomEventToNewrelic } from './wrapper';

const logger = loggers.newRelic;

// As strings to it's send this way to newrelic
export enum RequestKind {
    read = 'Read',
    list = 'List',
    create = 'Create',
    delete = 'Delete',
    update = 'Update',
    execute = 'Execute', // compute something not related to a given entity
    unknown = 'Unknown',
}

export interface ThirdPartyNewrelicEvent {
    thirdParty: string;
    requestKind: RequestKind;
    tenantId: string;
    success: boolean;
    durationMs: number;
    recordKind: string; // what record you are dealing with on the 3rd party ? ex : salesOrder
    recordCount: number; // how many record were in the request, is it one or 10000 ?
    async: boolean; // was it a sync request or an asnyc request ?
}

// Make sure you update https://confluence.sage.com/display/XTREEM/Newrelic+Custom+events
// when adding a new custom event also consider if it's worth being added to the dashboard of the cluster, contact cirrus.
const newrelicEventThirdParty = 'thirdPartyRequest';
const newrelicEventXtremEvent = 'xtremEvent';

/**
 * Send to newrelic a custom event related to a 3rd party integration event that can then be used to gather metrics and create dashboards
 * @param event
 */
export function record3rdPartyEventToNewrelic(event: ThirdPartyNewrelicEvent): void {
    try {
        if (isNewrelicDisabled) {
            return;
        }

        const newrelicEventAttributes: Dict<boolean | number | string> = {
            ...event,
            success: event.success ? 1 : 0, // newrelic does not support booleans
            failure: event.success ? 0 : 1, // easier to have also a failure value to perform computation on sums
            async: event.async ? 1 : 0,
        };

        recordCustomEventToNewrelic(newrelicEventThirdParty, newrelicEventAttributes);
    } catch ({ stack, message }) {
        // log and swallow, we don't want this event emiter to disturb the processing of the request
        logger.warn(`Failed to send 3rdparty event to newrelic ${stack || message}`);
    }
}

export interface NewrelicXtremEvent {
    tenantId?: string;
    durationMs?: number;
    success: boolean;
    eventKind: string;
}

/**
 * Record execution of message, notification or async mutation to newrelic
 * @param event
 */
export function recordXtremEventToNewrelic(event: NewrelicXtremEvent): void {
    try {
        if (isNewrelicDisabled) {
            return;
        }
        if (!event.tenantId) {
            event.tenantId = 'unknown';
        }
        if (event.durationMs && event.durationMs < 0) {
            delete event.durationMs;
        }
        recordCustomEventToNewrelic(newrelicEventXtremEvent, {
            ...event,
        });
    } catch ({ stack, message }) {
        // log and swallow, we don't want this event emitter to disturb the processing of the request
        logger.warn(`Failed to send xtrem event to newrelic ${stack || message}`);
    }
}
/**
 * Record an unhandled error in NewRelic
 * @param context
 * @param err
 */
export function recordXtremErrorToNewrelic(context: MetricsContext, err: Error): void {
    try {
        if (isNewrelicDisabled) {
            return;
        }
        // eslint-disable-next-line global-require
        require('newrelic').noticeError(err, {
            cloudflareRayID: context.cloudflareRayID,
            tenantId: context.tenantId || 'unknown',
        });
    } catch ({ stack, message }) {
        // log and swallow, we don't want this event emitter to disturb the processing of the request
        logger.warn(`Failed to send xtrem error to newrelic ${stack || message}`);
    }
}
