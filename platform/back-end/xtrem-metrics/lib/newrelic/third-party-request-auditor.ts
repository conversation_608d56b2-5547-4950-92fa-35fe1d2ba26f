import { MetricsContext } from '../types';
import { record3rdPartyEventToNewrelic, RequestKind, ThirdPartyNewrelicEvent } from './custom-event-emiter';

const UNKNOWN_RECORD_KIND = 'unknown';

export interface NullableTenantId {
    tenantId: string | null;
}

/**
 * Aims at sending to newrelic some statistics related to 3rd party external calls
 */
export class ThirdPartyRequestAuditor {
    private readonly thirdParty: string;

    private readonly requestKind: RequestKind;

    private readonly recordKind: string;

    private readonly asyncRequest: boolean;

    private readonly tenantId: string;

    private readonly startedAt: Date;

    private recordCount?: number;

    private customDurationInMs: number; // to be used for async when you manually calculate the duration.

    constructor(
        context: NullableTenantId,
        thirdParty: string,
        requestKind: RequestKind,
        recordKind: string,
        async: boolean,
        recordCount?: number,
    ) {
        this.thirdParty = thirdParty;
        this.requestKind = requestKind;
        this.startedAt = new Date();
        this.recordKind = recordKind;
        this.recordCount = recordCount;
        this.asyncRequest = async;
        this.tenantId = context.tenantId || 'unknown';
    }

    public static newSingleRecordRead(
        context: MetricsContext,
        thirdPartyId: string,
        recordKind: string = UNKNOWN_RECORD_KIND,
        async = false,
    ): ThirdPartyRequestAuditor {
        return new ThirdPartyRequestAuditor(context, thirdPartyId, RequestKind.read, recordKind, async, 1);
    }

    // public static newIntacctSingleRecordRead(
    //     context: MetricsContext,
    //     recordKind: string = UNKNOWN_RECORD_KIND,
    //     async = false,
    // ): ThirdPartyRequestAuditor {
    //     return new ThirdPartyRequestAuditor(context, ThirdParty.intacct, RequestKind.read, recordKind, async, 1);
    // }

    // public static newIntacctSingleRecordCreate(
    //     context: MetricsContext,
    //     recordKind: string = UNKNOWN_RECORD_KIND,
    //     async = false,
    // ): ThirdPartyRequestAuditor {
    //     return new ThirdPartyRequestAuditor(context, ThirdParty.intacct, RequestKind.create, recordKind, async, 1);
    // }

    // public static newAvalaraSingleRecordRead(
    //     context: MetricsContext,
    //     recordKind: string = UNKNOWN_RECORD_KIND,
    // ): ThirdPartyRequestAuditor {
    //     return new ThirdPartyRequestAuditor(context, ThirdParty.avalara, RequestKind.read, recordKind, false, 1);
    // }

    public recordSuccess(recordCount?: number): void {
        this.recordResult(true, recordCount);
    }

    public recordFailure(recordCount?: number): void {
        this.recordResult(false, recordCount);
    }

    /**
     * In async mode we can't rely on the object creation to calculate request duration, this allows you to manually set it
     * When set it override the value sent to newrelic.
     * @param durationInMs how long it took from request to response in millisecond
     */
    public withCustomDuration(durationInMs: number): ThirdPartyRequestAuditor {
        this.customDurationInMs = durationInMs;
        return this;
    }

    private recordResult(success: boolean, lineCount?: number): void {
        const duration = this.customDurationInMs || new Date().getTime() - this.startedAt.getTime();
        if (lineCount != null && lineCount >= 0) {
            this.recordCount = lineCount;
        }

        const eventForNewrelic: ThirdPartyNewrelicEvent = {
            thirdParty: this.thirdParty,
            recordCount: this.recordCount === undefined ? 0 : this.recordCount,
            recordKind: this.recordKind,
            requestKind: this.requestKind,
            async: this.asyncRequest,
            tenantId: this.tenantId,
            durationMs: duration,
            success,
        };
        // Here it would be interesting to have an interface and inject either a logger or newrelic impem based on whether enabled on not is newrelic.
        record3rdPartyEventToNewrelic(eventForNewrelic);
    }
}
