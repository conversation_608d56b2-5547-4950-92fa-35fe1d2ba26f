{"extends": "../../tsconfig-base.json", "compilerOptions": {"outDir": "build", "rootDir": ".", "baseUrl": "."}, "include": ["index.ts", "application.ts", "lib/**/*", "lib-generated/**/*", "api/api.d.ts", "test/**/*.ts"], "exclude": ["lib/pages/**/*", "lib/widgets/**/*", "lib/page-extensions/**/*", "lib/page-fragments/**/*", "lib/stickers/**/*", "lib/i18n/**/*", "**/*.feature", "**/*.png", "lib/client-functions/**/*"], "references": [{"path": "../../shared/xtrem-async-helper"}, {"path": "../xtrem-config"}, {"path": "../xtrem-log"}, {"path": "../../shared/xtrem-shared"}, {"path": "../eslint-plugin-xtrem"}, {"path": "../xtrem-dts-bundle"}, {"path": "../xtrem-minify"}]}