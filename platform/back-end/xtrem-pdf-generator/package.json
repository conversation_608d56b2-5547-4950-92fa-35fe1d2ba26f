{"name": "@sage/xtrem-pdf-generator", "description": "Xtrem PDF Generator Package", "version": "58.0.2", "author": "sage", "license": "UNLICENSED", "keywords": ["pdf"], "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/index.js", "typings": "build/package-definition.d.ts", "bin": {"xpdfgen": "./bin/xpdfgen"}, "files": ["bin", "build/index.*", "build/package-definition.d.ts", "build/lib", "data"], "dependencies": {"@sage/xtrem-config": "workspace:*", "@sage/xtrem-log": "workspace:*", "@sage/xtrem-metrics": "workspace:*", "@sage/xtrem-shared": "workspace:*", "lodash": "^4.17.21", "nanoid": "^3.3.8", "pdf-lib": "^1.17.1", "prom-client": "^15.1.2", "puppeteer": "^24.6.1"}, "devDependencies": {"@sage/eslint-plugin-xtrem": "workspace:*", "@sage/xtrem-dts-bundle": "workspace:*", "@sage/xtrem-minify": "workspace:*", "@types/archiver": "^6.0.0", "@types/chai": "^4.3.6", "@types/chai-as-promised": "^7.1.6", "@types/crypto-js": "^4.2.0", "@types/lodash": "^4.14.198", "@types/mocha": "^10.0.1", "@types/node": "^22.10.2", "@types/pdf-parse": "^1.1.0", "chai": "^4.3.10", "chai-as-promised": "^7.1.1", "mocha": "^10.8.2", "pdf-parse": "^1.1.1", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "~5.8.3"}, "scripts": {"build": "tsc -b -v . && pnpm dts-bundle", "build:binary": "pnpm clean && pnpm build && xtrem-minify -c -z 'build/**/*.js'", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build", "dts-bundle": "xtrem-dts-bundle", "lint": "eslint -c .eslintrc.js --ext .ts lib test", "lint:fix": "eslint --fix -c .eslintrc.js --ext .ts lib test", "prettier:check": "prettier --list-different \"{lib,test}/**/*.ts\"", "prettier:write": "prettier --write \"{lib,test}/**/*.ts\"", "sonarqube:scanner": "pnpm test:ci && pnpm dlx sonarqube-scanner && rm -rf coverage .nyc_output .scannerwork junit-report-*.xml", "test": "mocha --trace-warnings --recursive --exit \"build/test/**/*@(-|.)test.js\"", "test:ci": "cross-env TZ=CET JUNIT_REPORT_PATH=junit-report-pdf-generator.xml JUNIT_REPORT_NAME='xtrem-pdf-generator' c8 --reporter=lcov --reporter=json --reporter=cobertura --reporter=text mocha --recursive --exit \"test/**/*@(-|.)test.ts\" --reporter mocha-jenkins-reporter"}, "c8": {"cache": false, "all": true, "extension": [".ts", ".tsx"], "sourceMap": true, "instrument": true, "reporter": ["text-summary", "clover", "json", "lcov"], "include": ["lib/**/*.ts"], "exclude": ["test/**/*", "data/**/*"]}}