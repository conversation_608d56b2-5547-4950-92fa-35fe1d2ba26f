import { ConfigManager } from '@sage/xtrem-config';
import { expect } from 'chai';
import * as parsePdf from 'pdf-parse';
import { generatePdfBuffer } from '../index';

describe('pdf utils', () => {
    before(() => {
        ConfigManager.load(__dirname);
    });

    after(() => {});

    it('should turn HTML document into PDF binary stream', async () => {
        const result = await generatePdfBuffer({
            productName: 'xtremTest',
            reportName: 'testReport',
            populatedBodyContent: '<html><head></head><body><h1>Hi there!</h1></body></html>',
            paperFormat: 'a4',
            pageOrientation: 'portrait',
        });
        expect(result).to.be.instanceOf(Buffer);
    });
    it('should generate a valid PDF binary stream', async () => {
        const generatedPdfBinaryContent = await generatePdfBuffer({
            productName: 'xtremTest',
            reportName: 'testReport',
            populatedBodyContent: '<html><head></head><body><h1>Hi there!</h1></body></html>',
            paperFormat: 'a4',
            pageOrientation: 'portrait',
        });
        const parsedPdf = await parsePdf(generatedPdfBinaryContent);
        // If the document is invalid parsePdf would throw an error.
        expect(parsedPdf.numpages).to.eq(1);
    });

    it('should generate a PDF document with the text content from the HTML document', async () => {
        const generatedPdfBinaryContent = await generatePdfBuffer({
            productName: 'xtremTest',
            reportName: 'testReport',
            populatedBodyContent: '<html><head></head><body><h1>Hi there!</h1></body></html>',
            paperFormat: 'a4',
            pageOrientation: 'portrait',
        });
        const parsedPdf = await parsePdf(generatedPdfBinaryContent);
        expect(parsedPdf.text.trim()).to.eq('Hi there!');
    });
});
