export enum ReportPageOrientationEnum {
    portrait = 1,
    landscape = 2,
}

export type ReportPageOrientation = keyof typeof ReportPageOrientationEnum;

export enum ReportPaperFormatEnum {
    letter = 1,
    legal = 2,
    tabloid = 3,
    ledger = 4,
    a0 = 5,
    a1 = 6,
    a2 = 7,
    a3 = 8,
    a4 = 9,
    a5 = 10,
    a6 = 11,
}

export type ReportPaperFormat = keyof typeof ReportPaperFormatEnum;
