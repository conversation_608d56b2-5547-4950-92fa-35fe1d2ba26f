import { Logger } from '@sage/xtrem-log';
import { BusinessRuleError, Dict } from '@sage/xtrem-shared';
import * as crypto from 'crypto';
import * as fs from 'fs/promises';
import { nanoid } from 'nanoid';
import * as os from 'os';
import * as path from 'path';
import { AFRelationship, PDFDocument, PDFHexString, PDFName, PDFString } from 'pdf-lib';
import * as puppeteer from 'puppeteer';
import { checkFileExists, sleepMillis } from './generic-utils';
import { reportHistogram } from './report-metrics';
import { ReportPageOrientation, ReportPaperFormat } from './types';

// TODO: duplicated in xtrem-pdf-generator and xtrem-reporting, should be moved to a common package
export const CM_INCH = 2.54;

// END TODO

const logger = Logger.getLogger(__filename, 'pdf-generation');

let browserInstance: puppeteer.Browser | null = null;

interface PaperFormatSizeInCm {
    width: number;
    height: number;
}

const paperFormatSize: Dict<PaperFormatSizeInCm> = {
    a0: { width: 84.1, height: 118.9 },
    a1: { width: 59.4, height: 84.1 },
    a2: { width: 42, height: 59.4 },
    a3: { width: 29.7, height: 42 },
    a4: { width: 21, height: 29.7 },
    a5: { width: 14.8, height: 21 },
    a6: { width: 10.5, height: 14.8 },
    letter: { width: 21.6, height: 27.9 },
    legal: { width: 21.6, height: 35.6 },
    ledger: { width: 27.9, height: 43.2 },
    tabloid: { width: 27.9, height: 43.2 },
};

const ensureBrowserInstance = async (reportOptions: ReportOptions): Promise<puppeteer.Browser> => {
    if (browserInstance?.connected) {
        logger.verbose(() => 'Reusing existing Chromium instance');
    } else {
        logger.verbose(() => 'Initializing Chromium');
        const browserTimeout = reportOptions?.browserTimeout ?? 180000;
        // Increase default protocol timeout to 10 minutes
        // see https://www.timsanteford.com/posts/how-to-fix-puppeteer-connection-error-protocolerror-network-enable-timed-out-in-docker/
        // see https://pptr.dev/api/puppeteer.connectoptions/#protocoltimeout
        const protocolTimeout = reportOptions?.protocolTimeout ?? 600000;
        browserInstance = await puppeteer.launch({
            // According to https://developer.chrome.com/blog/chrome-headless-shell the headless 'shell' mode should be faster and more appropriate for server-side rendering
            // But it seems that it is not working properly with puppeteer, because we do not have the page numbering in the footer
            headless: true,
            timeout: browserTimeout,
            protocolTimeout,
            args: [
                '--disable-gpu',
                '--full-memory-crash-report',
                '--unlimited-storage',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--incognito',
            ],
        });
        logger.verbose(() => 'Chromium loaded.');
        const exePath = puppeteer.executablePath();
        await browserInstance.version().then(version => {
            logger.info(`Chromium version: ${version}, from: ${exePath}`);
        });
    }

    return browserInstance;
};

const addPdfA3BMetadataHeader = (doc: PDFDocument, productName: string, documentTitle = 'SOME TITLE') => {
    const now = new Date().toISOString();
    const id = nanoid(24);

    // Source: https://github.com/Hopding/pdf-lib/pull/508#issuecomment-732740810
    const hash = crypto.createHash('sha256').update(id).digest();
    const hashArray = Array.from(new Uint8Array(hash));
    const hashHex = hashArray
        .map(b => b.toString(16).padStart(2, '0'))
        .join('')
        .substring(0, 24);

    /**
     * Source: https://github.com/Hopding/pdf-lib/issues/55#issuecomment-*********
     *
     * In this section, set some mandatory metadata to the document as well as indicating the target standard in the
     * `pdfaid:part` and `pdfaid:conformance` tags.
     *  */
    const metadataXML = `
<?xpacket begin="" id="${hashHex}"?>
    <x:xmpmeta xmlns:x="adobe:ns:meta/" x:xmptk="Adobe XMP Core 5.2-c001 63.139439, 2010/09/27-13:37:26">
        <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
            <rdf:Description rdf:about="" xmlns:dc="http://purl.org/dc/elements/1.1/">
            <dc:format>application/pdf</dc:format>
            <dc:creator>
                <rdf:Seq>
                <rdf:li>${productName}</rdf:li>
                </rdf:Seq>
            </dc:creator>
            <dc:title>
                <rdf:Alt>
                    <rdf:li xml:lang="x-default">${documentTitle}</rdf:li>
                </rdf:Alt>
            </dc:title>
            </rdf:Description>
            <rdf:Description rdf:about="" xmlns:xmp="http://ns.adobe.com/xap/1.0/">
                <xmp:CreatorTool>${productName}</xmp:CreatorTool>
                <xmp:CreateDate>${now}</xmp:CreateDate>
                <xmp:ModifyDate>${now}</xmp:ModifyDate>
                <xmp:MetadataDate>${now}</xmp:MetadataDate>
            </rdf:Description>
            <rdf:Description rdf:about="" xmlns:pdf="http://ns.adobe.com/pdf/1.3/" xmlns:pdfaid="http://www.aiim.org/pdfa/ns/id/">
                <pdf:Producer>${productName}</pdf:Producer>
                <pdfaid:part>3</pdfaid:part>
                <pdfaid:conformance>B</pdfaid:conformance>
            </rdf:Description>
        </rdf:RDF>
    </x:xmpmeta>
<?xpacket end="w"?>`.trim();

    const metadataStream = doc.context.stream(metadataXML, {
        Type: 'Metadata',
        Subtype: 'XML',
        Length: metadataXML.length,
    });

    const metadataStreamRef = doc.context.register(metadataStream);

    doc.catalog.set(PDFName.of('Metadata'), metadataStreamRef);

    const permanentDocumentId = PDFHexString.of(hashHex);
    const changingDocumentID = permanentDocumentId;
    doc.context.trailerInfo.ID = doc.context.obj([permanentDocumentId, changingDocumentID]);

    // MarkInfo specifies if the document is a tagged pdf file. => 9.7 https://www.adobe.com/content/dam/acom/en/devnet/pdf/pdfs/pdf_reference_archives/PDFReference.pdf
    const rootRef = doc.context.obj({ Marked: true });
    doc.catalog.set(PDFName.of('MarkInfo'), rootRef);

    // => 14.7.2 https://www.adobe.com/content/dam/acom/en/devnet/pdf/pdfs/PDF32000_2008.pdf
    const structTreeData = doc.context.obj({
        Type: PDFName.of('StructTreeRoot'),
    });
    const structTreeRef = doc.context.register(structTreeData);
    doc.catalog.set(PDFName.of('StructTreeRoot'), structTreeRef);
};

let colorProfile: Buffer | null = null;

const getColorProfile = async (): Promise<Buffer> => {
    if (colorProfile) {
        return colorProfile;
    }
    // Downloaded from: https://www.color.org/srgbprofiles.xalter#v2
    let profilePath = path.resolve(__dirname, '../../data', 'sRGB2014.icc');
    // if in build/lib/... (e.g. in production), we need to resolve the path to the data directory
    if (!(await checkFileExists(profilePath))) {
        profilePath = path.resolve(__dirname, '../data', 'sRGB2014.icc');
    }
    // if not we are running tests
    if (!(await checkFileExists(profilePath))) {
        logger.error(`Color profile not found at ${profilePath}`);
        throw new Error(`Color profile not found at ${profilePath}`);
    }

    try {
        colorProfile = await fs.readFile(profilePath);
    } catch (err) {
        logger.error(`Failed to read color profile from ${profilePath}:`, err);
        throw new Error(`Failed to read color profile from ${profilePath}`, { cause: err });
    }
    return colorProfile;
};

/**
 * Profile correction, we need to embed a color profile and update any color references to it to ensure
 * that the colors are rendered the same way on all devices.
 *
 * Source: https://github.com/Hopding/pdf-lib/issues/230
 * */
const addCalibratedColorProfile = async (doc: PDFDocument): Promise<void> => {
    const profile = await getColorProfile();
    const profileStream = doc.context.stream(profile, {
        Length: profile.length,
    });
    const profileStreamRef = doc.context.register(profileStream);

    const outputIntent = doc.context.obj({
        Type: 'OutputIntent',
        S: 'GTS_PDFA1',
        OutputConditionIdentifier: PDFString.of('sRGB'),
        DestOutputProfile: profileStreamRef,
    });
    const outputIntentRef = doc.context.register(outputIntent);
    doc.catalog.set(PDFName.of('OutputIntents'), doc.context.obj([outputIntentRef]));
};

/**
 * Attaches file to the document in a PDF/A-3B compliant way.
 * The AF Relationship field must be set to `Data`.
 */
const addAttachment = async (
    doc: PDFDocument,
    attachmentContent: Buffer,
    fileName: string,
    attachmentMimeType: string,
) => {
    await doc.attach(attachmentContent, fileName, {
        mimeType: attachmentMimeType,
        afRelationship: AFRelationship.Data,
    });
};

const getHeaderFooterConfiguration = (
    populatedHeaderContent?: string,
    populatedFooterContent?: string,
    leftMarginCm = 2,
    rightMarginCm = 2,
): Partial<puppeteer.PDFOptions> => {
    const baseHeaderFooterStyle = `width: 100%;padding: 0 ${rightMarginCm}cm 0 ${leftMarginCm}cm;display: block;box-sizing: border-box;font-size:10pt;font-family:'Sage UI', Helvetica, Arial, sans-serif; -webkit-print-color-adjust: exact;`;
    const config: Partial<puppeteer.PDFOptions> = {};
    config.displayHeaderFooter = true;
    config.headerTemplate = `<span class="ck ck-content" style="${baseHeaderFooterStyle}">${populatedHeaderContent || ''}</span>`;
    config.footerTemplate = `<span class="ck ck-content" style="${baseHeaderFooterStyle}">${populatedFooterContent || ''}</span>`;
    return config;
};

export interface ConvertibleToNumber {
    toNumber: () => number;
}

export interface GeneratePdfDataOptions {
    productName: string;
    attachmentMimeType?: string;
    attachmentName?: string;
    outputPath?: string;
    pageOrientation: ReportPageOrientation;
    paperFormat: ReportPaperFormat;
    populatedAttachment?: string;
    populatedBodyContent: string;
    populatedFooterContent?: string;
    populatedHeaderContent?: string;
    reportName?: string;
    // margins in centimeters, can be a number or an object with a toNumber method (ex: Decimal)
    leftMarginCm?: number | ConvertibleToNumber;
    rightMarginCm?: number | ConvertibleToNumber;
    bottomMarginCm?: number | ConvertibleToNumber;
    topMarginCm?: number | ConvertibleToNumber;
    isDefaultHeaderFooter?: boolean;
    documentTitle?: string;
    isSample?: boolean;
    reportOptions?: ReportOptions;
}

export interface ReportOptions {
    pdfTransformationTimeout?: number; // in milliseconds
    pageOpeningTimeout?: number; // in milliseconds
    maxTotalPages?: number; // maximum number of pages allowed in the report
    browserTimeout?: number; // in milliseconds
    protocolTimeout?: number; // in milliseconds
    closeBrowser?: boolean; // if true, the browser instance will be closed after the report is generated
    // If the reportOptions are not provided, the default values will be used
    [key: string]: any; // allow other options to be passed
}

const isObjectWithToNumber = (value: unknown): value is { toNumber: () => number } => {
    return (
        typeof value === 'object' &&
        value !== null &&
        'toNumber' in value &&
        typeof (value as { toNumber: () => number }).toNumber === 'function'
    );
};

function toRoundedNumber(value: number | object) {
    let val = value;
    if (typeof val === 'object') {
        if (isObjectWithToNumber(val)) {
            // If the value is an object with a toNumber method, call it
            val = val.toNumber();
        } else if (Object.prototype.hasOwnProperty.call(val.constructor.prototype, 'valueOf')) {
            // If the value is an object with a valueOf method, call it
            val = val.valueOf();
        }
    }
    if (typeof val === 'string') {
        // If the value is a string, try to convert it to a number
        val = parseFloat(val);
    }
    // If the value is still not a number, throw an error
    if (typeof val !== 'number') {
        throw new TypeError(`Expected a number, but got ${typeof val}`);
    }

    return Number.isNaN(val) ? 0 : Math.round(val * 100) / 100;
}

const _generatePdfBuffer = async ({
    productName,
    populatedBodyContent,
    populatedHeaderContent,
    populatedFooterContent,
    paperFormat,
    pageOrientation,
    outputPath,
    populatedAttachment,
    attachmentName,
    attachmentMimeType,
    leftMarginCm = 2,
    rightMarginCm = 2,
    topMarginCm = 2,
    bottomMarginCm = 2,
    documentTitle,
    reportOptions = {}, // reportOptions can be passed to override the default values
}: GeneratePdfDataOptions): Promise<Buffer> => {
    let page: puppeteer.Page | null = null;
    const tempFile = path.resolve(os.tmpdir(), `${nanoid(8)}.html`);

    const roundedLeftMarginCm = toRoundedNumber(leftMarginCm);
    const roundedRightMarginCm = toRoundedNumber(rightMarginCm);
    const roundedTopMarginCm = toRoundedNumber(topMarginCm);
    const roundedBottomMarginCm = toRoundedNumber(bottomMarginCm);

    try {
        // Generate base PDF document using Puppeteer and Chromium
        const headerFooterConfig = getHeaderFooterConfiguration(
            populatedHeaderContent,
            populatedFooterContent,
            roundedLeftMarginCm,
            roundedRightMarginCm,
        );
        // path includes the file name and extension. If the file exists, it is overwritten.
        const pdfTransformationTimeout = reportOptions?.pdfTransformationTimeout ?? 120000;
        const params: puppeteer.PDFOptions = {
            ...headerFooterConfig,
            timeout: pdfTransformationTimeout,
            format: paperFormat,
            landscape: pageOrientation === 'landscape',
            margin: {
                top: `${roundedTopMarginCm}cm`,
                bottom: `${roundedBottomMarginCm}cm`,
                left: `${roundedLeftMarginCm}cm`,
                right: `${roundedRightMarginCm}cm`,
            },
        };
        if (outputPath) {
            params.path = outputPath;
        }
        const htmlBuffer = Buffer.from(populatedBodyContent);
        logger.verbose(
            () => `HTML document char count: ${populatedBodyContent.length}, HTML buffer length: ${htmlBuffer.length}`,
        );
        logger.verbose(() => `Creating temp html file: ${tempFile}`);

        await fs.writeFile(tempFile, htmlBuffer, 'utf8');
        logger.verbose(() => 'File is ready.');
        const fileUrl = `file://${tempFile}`;
        const browser = await ensureBrowserInstance(reportOptions);
        page = await browser.newPage();
        logger.verbose(() => 'Rendering HTML in Chromium...');

        // default pdf dpi from chromium is 96dpi, so we compute the usable height and width in pixels
        const useableHeight =
            (96 *
                (getPaperHeight(paperFormat, pageOrientation === 'landscape') -
                    (roundedTopMarginCm + roundedBottomMarginCm))) /
            CM_INCH;
        const useableWidth =
            (96 *
                (getPaperWidth(paperFormat, pageOrientation === 'landscape') -
                    (roundedLeftMarginCm + roundedRightMarginCm))) /
            CM_INCH;
        // Set the viewport to the useable size so we can have the correct height of the document
        await page.setViewport({
            width: Math.ceil(useableWidth),
            height: Math.ceil(useableHeight),
        });
        const pageOpeningTimeout = reportOptions?.pageOpeningTimeout || 60000;
        await page.goto(fileUrl, { waitUntil: 'domcontentloaded', timeout: pageOpeningTimeout });
        await page.emulateMediaType('print');

        const elem = await page.$('html');
        if (elem) {
            const boundingBox = await elem.boundingBox();
            await elem.dispose();
            // Add 10% to the estimated total pages to be on the safe side
            const estimatedTotalPages = boundingBox ? Math.ceil((1.1 * boundingBox.height) / useableHeight) : 1;
            const maxTotalPages = reportOptions?.maxTotalPages || 500;
            if (estimatedTotalPages > maxTotalPages) {
                logger.verbose(() => 'Closing page');
                await page.close();
                logger.verbose(() => 'Page closed.');

                throw new Error(
                    `This report exceeds the maximum page limit: ${maxTotalPages}. Try to reduce the current number of estimated pages: (${estimatedTotalPages}).`,
                    {
                        cause: {
                            code: 'max-total-pages-reached',
                            estimatedTotalPages,
                            maxTotalPages,
                        },
                    },
                );
            }
            logger.verbose(() => 'Printing HTML report into PDF using Chromium....');
            const binaryPdfContent = await page.pdf(params);
            logger.verbose(() => `Initial PDF export is ready. PDF buffer size: ${binaryPdfContent.length}`);
            await page.close();
            logger.verbose(() => 'Temp HTML file removed.');
            logger.verbose(() => 'Adding PDF A/2-B metadata....');

            // Update generated document with additional metadata and add attachments
            const doc = await PDFDocument.load(binaryPdfContent);

            doc.setTitle(documentTitle ?? productName);
            addPdfA3BMetadataHeader(doc, productName, documentTitle);
            await addCalibratedColorProfile(doc);

            logger.verbose(() => 'PDF A/2-B metadata added.');

            if (populatedAttachment && attachmentName && attachmentMimeType) {
                logger.verbose(() => 'Adding PDF attachment....');
                const attachmentBuffer = Buffer.from(populatedAttachment);
                await addAttachment(doc, attachmentBuffer, attachmentName, attachmentMimeType);
                logger.verbose(() => 'PDF Attachment added.');
            }
            const exportedDocument = await doc.save();
            const pdfBufferWithMetadata = Buffer.from(exportedDocument);
            logger.verbose(
                () =>
                    `PDF metadata added, the document is ready. Final PDF document buffer size: ${pdfBufferWithMetadata.length}`,
            );
            return pdfBufferWithMetadata;
        }
        return Buffer.from('');
    } catch (err) {
        logger.error('Failed to generate PDF report:');
        logger.error(err);
        if (!(err instanceof BusinessRuleError)) {
            // We encounter an error other than a business rule error and an exception is thrown
            // We need to close the pages and the browser
            const pages = (await browserInstance?.pages()) || [];
            // eslint-disable-next-line no-restricted-syntax
            for (const pg of pages) {
                logger.verbose(() => `Closing page ${pg.url()}`);
                await pg.close();
                logger.verbose(() => 'Page closed');
            }
            logger.error(() => 'Failed to generate PDF report: all pages closed');
            // Disconnect the browser instance and wait for 10 seconds for safe exit
            browserInstance = null;
            await sleepMillis(10000);
        }
        throw err;
    } finally {
        logger.verbose(() => 'Removing temp file');
        const tempFileExists = await checkFileExists(tempFile);
        if (tempFileExists) {
            await fs.unlink(tempFile);
            logger.verbose(() => 'Temp file removed.');
        } else {
            logger.verbose(() => 'Temp file was not found.');
        }
        if (reportOptions.closeBrowser) {
            // If the reportOptions specify to close the browser, we close it
            await closeBrowserInstance();
        }
    }
};

function getPaperWidth(paperFormat: ReportPaperFormat, isLandscapeMode: boolean): number {
    if (!paperFormatSize[paperFormat]) throw new Error(`Unknown paper format: ${paperFormat}`);
    return isLandscapeMode ? paperFormatSize[paperFormat].height : paperFormatSize[paperFormat].width;
}

function getPaperHeight(paperFormat: ReportPaperFormat, isLandscapeMode: boolean): number {
    if (!paperFormatSize[paperFormat]) throw new Error(`Unknown paper format: ${paperFormat}`);
    return isLandscapeMode ? paperFormatSize[paperFormat].width : paperFormatSize[paperFormat].height;
}

export const generatePdfBuffer = (options: GeneratePdfDataOptions): Promise<Buffer> => {
    return reportHistogram.withMetrics({ reportName: options.reportName, method: 'generate_pdf' }, () =>
        _generatePdfBuffer(options),
    );
};

const closeBrowserInstance = async (): Promise<void> => {
    if (browserInstance) {
        logger.verbose(() => 'Closing browser instance');
        await browserInstance.close();
        browserInstance = null;
        logger.verbose(() => 'Browser instance closed');
    } else {
        logger.verbose(() => 'No browser instance to close');
    }
};
