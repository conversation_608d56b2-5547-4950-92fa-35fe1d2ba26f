import { flatten } from 'lodash';

export type InternalItemPath = string | { [K: string]: InternalItemPath[] };

export interface DependingItem<This = any, ValT = any> {
    name: string;
    fullName?: string;
    dependsOn?: InternalItemPath[];
    /**
     * The **optional** object bound to this item. name should be the name of this object
     */
    object?: any;
    updatedValue?: ValT | ((this: This, cx: any) => ValT);
}

// This root element allows us to handle disconnected graphs.
// We add it at the end of every dependency chain, to reconnect all the chains together
// We just have to be careful to not insert it into the result.
const root = { name: '???root???' } as DependingItem;

const pathNames = (path: InternalItemPath) => (typeof path === 'string' ? [path] : Object.keys(path));
export const getDependencyNames = (dependsOn: InternalItemPath[] | undefined) =>
    dependsOn ? flatten(dependsOn.map(pathNames)) : [root.name];

// Throw a meaningful error when toposort algorithm fails
function throwToposortError<T extends DependingItem>(items: T[], allItems: T[]): never {
    const explore = (path: T[], item: T): never => {
        // If path contains item.name we have found a cycle
        const i = path.findIndex(elt => elt.name === item.name);
        if (i >= 0) {
            throw new Error(
                `toposort failed: circularity detected in dependencies: ${[...path.slice(i), path[i]]
                    .map(p => `\n    ${p.fullName || p.name}`)
                    .join('->')}`,
            );
        }

        // Find a dependency of item which is inside items.
        const key = getDependencyNames(item.dependsOn).find(name => items.some(it => it.name === name));
        if (key === undefined) {
            // toposort failed because of missing dependency
            const missingName = getDependencyNames(item.dependsOn).find(name => !allItems.some(it => it.name === name));
            throw new Error(`toposort failed: missing dependency: ${item.fullName || item.name}->${missingName}`);
        }
        // Explore deeper, with this dependency added to the path.
        return explore([...path, item], items.find(it => it.name === key)!);
    };

    return explore([], items[0]);
}

export function topoSort<T extends DependingItem>(items: T[]): T[] {
    // this is a toposort but we need stable sort (order preserved on ties)
    // so we use a custom algorithm.
    const result: T[] = [];
    let todo = [...(items || []), root as T];
    const acceptedNames = new Map<string, boolean>();

    while (todo.length > 0) {
        const next = [] as typeof items;
        todo.forEach(item => {
            if (
                getDependencyNames(item.dependsOn).every((dep: string) => acceptedNames.get(dep) || dep === item.name)
            ) {
                if (item !== root) result.push(item);
                acceptedNames.set(item.name, true);
            } else {
                next.push(item);
            }
        });
        if (next.length === todo.length) {
            throwToposortError(todo, items);
        }
        todo = next;
    }
    return result;
}
