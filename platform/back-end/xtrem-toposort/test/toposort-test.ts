// Low level tests of the dependency manager logic
import { assert } from 'chai';
import { getDependencyNames, topoSort } from '../lib/toposort';

describe('Dependency manager', () => {
    it('can reduce a dependency to its first keys', () => {
        const names = getDependencyNames(['P1', { P2: [{ P3: ['P4', 'P5'] }] }, 'P6']);
        assert.deepEqual(names, ['P1', 'P2', 'P6']);
    });
    it('preserves order if no dependencies', () => {
        const sorted = topoSort([{ name: 'P1' }, { name: 'P2' }, { name: 'P3' }]).map(prop => prop.name);
        assert.deepEqual(sorted, ['P1', 'P2', 'P3']);
    });
    it('re-order if dependencies', () => {
        const sorted = topoSort([
            { name: 'P1', dependsOn: ['P3'] },
            { name: 'P2', dependsOn: ['P1'] },
            { name: 'P3' },
        ]).map(prop => prop.name);
        assert.deepEqual(sorted, ['P3', 'P1', 'P2']);
    });
    it('re-order if dependencies with ties', () => {
        const sorted = topoSort([
            { name: 'P1', dependsOn: ['P3'] },
            { name: 'P2', dependsOn: ['P3'] },
            { name: 'P3' },
        ]).map(prop => prop.name);
        assert.deepEqual(sorted, ['P3', 'P1', 'P2']);
    });
    it('re-order with deep dependencies', () => {
        const sorted = topoSort([
            { name: 'P1', dependsOn: ['P2'] },
            { name: 'P2', dependsOn: ['P3'] },
            { name: 'P3' },
        ]).map(prop => prop.name);
        assert.deepEqual(sorted, ['P3', 'P2', 'P1']);
    });
    it('re-order with complex dependencies', () => {
        const sorted = topoSort([
            { name: 'P1', dependsOn: ['P2'] },
            { name: 'P2', dependsOn: ['P3'] },
            { name: 'P3' },
            { name: 'P4', dependsOn: ['P1'] },
            { name: 'P5', dependsOn: ['P2'] },
        ]).map(prop => prop.name);
        assert.deepEqual(sorted, ['P3', 'P2', 'P5', 'P1', 'P4']);
    });
    it('re-order with complex multiple dependencies', () => {
        const sorted = topoSort([
            { name: 'P1', dependsOn: ['P3', 'P2'] },
            { name: 'P2', dependsOn: ['P3'] },
            { name: 'P3' },
            { name: 'P4', dependsOn: ['P1', 'P5'] },
            { name: 'P5', dependsOn: ['P3', 'P2'] },
        ]).map(prop => prop.name);
        assert.deepEqual(sorted, ['P3', 'P2', 'P5', 'P1', 'P4']);
    });
    it('fails if there is a missing dependency', () => {
        assert.throws(() => {
            topoSort([{ name: 'P1', dependsOn: ['P2'] }]);
        }, 'toposort failed: missing dependency: P1->P2');
    });
    it('fails if there is a circular dependency', () => {
        assert.throws(() => {
            topoSort([
                { name: 'P1', dependsOn: ['P2'] },
                { name: 'P2', dependsOn: ['P1'] },
            ]);
        }, 'toposort failed: circularity detected in dependencies: \n    P1->\n    P2->\n    P1');
    });
});
