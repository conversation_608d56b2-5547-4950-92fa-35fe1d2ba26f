import { TSESLint } from '@typescript-eslint/utils';
import { RuleTester } from '@typescript-eslint/utils/ts-eslint';
import rule, { buildComplexDataUpdateActionMemberError } from '../../../lib/rules/check-data-update-actions';

const { AST_NODE_TYPES } = TSESLint;

const options = {
    parserOptions: {},
    parser: require.resolve('@typescript-eslint/parser'),
    env: {
        es6: true,
    },
};

const ruleTester = new RuleTester(options);

ruleTester.run('check-data-actions', rule, {
    valid: [
        // prop1 is ts-to-sql compliant and prop2 is a litteral
        {
            code: `export const addSalesUnitOfMeasure = new DataUpdateAction({
                set: {
                    prop1() {
                        return this.stockUnit;
                    },
                    prop2 : 'abc',
                },
            });`,
        },
    ],
    invalid: [
        {
            // prop1 is too complex
            code: `export const addSalesUnitOfMeasure = new DataUpdateAction({
                set: {
                    prop1() {
                        return complexFunction();
                    },
                    prop2 : 'abc',
                },
            });`,
            errors: [
                {
                    messageId: 'error',
                    type: AST_NODE_TYPES.Property,
                    data: {
                        error: buildComplexDataUpdateActionMemberError('prop1'),
                    },
                },
            ],
        },
        {
            // prop1 is OK but prop2 is too complex
            code: `export const addSalesUnitOfMeasure = new DataUpdateAction({
                set: {
                    prop1() {
                        return this.stockUnit;
                    },
                    prop2() {
                        return complexFunction();
                    },
                },
            });`,
            errors: [
                {
                    messageId: 'error',
                    type: AST_NODE_TYPES.Property,
                    data: {
                        error: buildComplexDataUpdateActionMemberError('prop2'),
                    },
                },
            ],
        },
    ],
});
