import { AST_NODE_TYPES } from '@typescript-eslint/utils';
import { RuleTester } from '@typescript-eslint/utils/ts-eslint';
import rule, { buildDefaultValueNotCompliantError } from '../../../lib/rules/property-decorators-warnings';

const options = {
    parserOptions: {},
    parser: require.resolve('@typescript-eslint/parser'),
    env: {
        es6: true,
    },
};

const ruleTester = new RuleTester(options);

ruleTester.run('property-decorators-warnings', rule, {
    valid: [
        {
            // defaultValue() as a literal is OK
            code: `class Node1 {
                @decorators.stringProperty<Node1, 'prop1'>({
                    defaultValue: 'abc'
                })
                prop1: string;
            }`,
        },
        {
            // defaultValue() simple enough to be parsed by ts-to-sql
            code: `class Node1 {
                @decorators.stringProperty<Node1, 'prop1'>({
                    defaultValue() { return this.prop2 + 'xx'; }
                })
                prop1: string;
            }`,
        },
    ],
    invalid: [
        {
            // KO: defaultValue() too complex to be parsed by ts-to-sql
            code: `class Node1 {
                    @decorators.stringProperty<Node1, 'prop1'>({
                        defaultValue() { return tooComplexFunction(); }
                    })
                    prop1: string;
                }`,
            errors: [
                {
                    messageId: 'error',
                    type: AST_NODE_TYPES.CallExpression,
                    data: {
                        error: buildDefaultValueNotCompliantError('prop1'),
                    },
                },
            ],
        },
    ],
});
