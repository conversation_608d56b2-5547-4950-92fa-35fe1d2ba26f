import { AST_NODE_TYPES } from '@typescript-eslint/utils';
import { RuleTester } from '@typescript-eslint/utils/ts-eslint';
import rule from '../../../lib/rules/no-exclusive-graphql-tests';

const ruleTester = new RuleTester();

ruleTester.run('no-exclusive-graphql-tests', rule, {
    valid: [
        {
            code: '({"envConfigs": {"now": "2019-10-21T17:23:07.000Z"},"executionMode": "skip"})',
            filename: 'valid',
        },
    ],

    invalid: [
        {
            code: '({"envConfigs": {"now": "2019-10-21T17:23:07.000Z"},"executionMode": "only"})',
            filename: 'invalid',
            errors: [
                {
                    messageId: 'executionModeOnly',
                    type: AST_NODE_TYPES.Property,
                    data: { error: 'Bad executionMode' },
                },
            ],
        },
    ],
});
