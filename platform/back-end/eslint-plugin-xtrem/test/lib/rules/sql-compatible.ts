import { AST_NODE_TYPES } from '@typescript-eslint/utils';
import { RuleTester } from '@typescript-eslint/utils/ts-eslint';
import rule from '../../../lib/rules/sql-compatible';

const options = {
    parserOptions: {},
    parser: require.resolve('@typescript-eslint/parser'),
    env: {
        es6: true,
    },
};

const ruleTester = new RuleTester(options);

ruleTester.run('sql-compatible', rule, {
    valid: [
        { code: 'context.query(Foo, { filter: function() { return this.address.postalCode != null; }})' },
        { code: 'class Foo { @decorators.referenceProperty({ join: { id() { return this.bar; } } }) zoo; }' },
    ],

    invalid: [
        {
            code: 'context.query(Foo, { filter: function() { return foo(); }})',
            errors: [
                {
                    messageId: 'cannotBeConvertedToSql',
                    type: AST_NODE_TYPES.CallExpression,
                    data: { error: '1:49: Unsupported call' },
                },
            ],
        },
        {
            code: 'class Foo { @decorators.referenceProperty({ join: { id() { return bar(); } } }) zoo; }',
            errors: [
                {
                    messageId: 'cannotBeConvertedToSql',
                    type: AST_NODE_TYPES.CallExpression,
                    data: { error: '1:66: Unsupported call' },
                },
            ],
        },
    ],
});
