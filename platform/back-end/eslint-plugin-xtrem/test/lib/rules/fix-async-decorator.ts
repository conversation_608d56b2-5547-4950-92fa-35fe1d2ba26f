import { AST_NODE_TYPES } from '@typescript-eslint/utils';
import { RuleTester } from '@typescript-eslint/utils/ts-eslint';
import rule from '../../../lib/rules/fix-async-decorator';

const options = {
    parserOptions: {},
    parser: require.resolve('@typescript-eslint/parser'),
    env: {
        es6: true,
    },
};

const ruleTester = new RuleTester(options);

ruleTester.run('fix-async-decorator', rule, {
    valid: [
        {
            filename: 'test.ts',
            // OK: node property with Promise or Reference type
            code: `class Foo {
                @decorators.stringProperty({ }) readonly p1: Promise<string>;
                @decorators.referenceProperty({ }) readonly p2: Reference<Bar>;
                @decorators.referenceProperty({ }) readonly p3: Reference<Bar | null>;
            }`,
        },
    ],
    invalid: [
        {
            filename: 'test.ts',
            // KO: node property without Promise or Reference type + 1 missing readonly
            code: `class Foo {
                @decorators.stringProperty({ }) p1: string;
                @decorators.referenceProperty({ }) readonly p2: Bar;
                @decorators.referenceProperty({ }) readonly p3: Bar | null;
            }`,
            output: `class Foo {
                @decorators.stringProperty({ }) readonly p1: Promise<string>;
                @decorators.referenceProperty({ }) readonly p2: Reference<Bar>;
                @decorators.referenceProperty({ }) readonly p3: Reference<Bar | null>;
            }`,

            errors: [
                {
                    messageId: 'fixPropertyReadonly',
                    type: AST_NODE_TYPES.PropertyDefinition,
                },
                {
                    messageId: 'fixPropertyType',
                    type: AST_NODE_TYPES.TSStringKeyword,
                    data: {
                        wrapper: 'Promise',
                    },
                },
                {
                    messageId: 'fixPropertyType',
                    type: AST_NODE_TYPES.TSTypeReference,
                    data: {
                        wrapper: 'Reference',
                    },
                },
                {
                    messageId: 'fixPropertyType',
                    type: AST_NODE_TYPES.TSUnionType,
                    data: {
                        wrapper: 'Reference',
                    },
                },
            ],
        },
    ],
});
