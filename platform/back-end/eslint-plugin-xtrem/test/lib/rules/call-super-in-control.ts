import { AST_NODE_TYPES } from '@typescript-eslint/utils';
import { RuleTester } from '@typescript-eslint/utils/ts-eslint';
import rule from '../../../lib/rules/call-super-in-control';

const options = {
    parserOptions: {},
    parser: require.resolve('@typescript-eslint/parser'),

    env: {
        es6: true,
    },
};

const ruleTester = new RuleTester(options);

ruleTester.run('call-super-in-control', rule, {
    valid: [
        {
            code: ` class DataType {
                        controlValue(a){}
                    }
        
                    class ChildDataType extends DataType {                    
                        controlValue(a){
                            super.controlValue(a);
                        }
                    }`,
        },
    ],

    invalid: [
        {
            code: ` class DataType {
                        controlValue(a){}
                    }

                    class ChildDataType extends DataType {                    
                        controlValue(a){
                            console.log(a);
                        }
                    }`,
            errors: [
                {
                    messageId: 'mustCallSuper',
                    type: AST_NODE_TYPES.ClassDeclaration,
                    data: {
                        error: 'DataType: controlValue must call super.controlValue',
                    },
                },
            ],
        },
        {
            code: ` class DataType {
                        controlValue(a){}
                    }

                    class ChildDataType extends DataType {                    
                        controlValue(a){
                            super.controlValue(a);
                        }
                    }                    

                    class GrandChildDataType extends ChildDataType {                    
                        controlValue(a){
                            console.log(a);
                        }
                    }`,

            errors: [
                {
                    messageId: 'mustCallSuper',
                    type: AST_NODE_TYPES.ClassDeclaration,
                    data: {
                        error: 'DataType: controlValue must call super.controlValue',
                    },
                },
            ],
        },
    ],
});
