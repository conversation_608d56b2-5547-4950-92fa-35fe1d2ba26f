import { AST_NODE_TYPES } from '@typescript-eslint/utils';
import { RuleTester } from '@typescript-eslint/utils/ts-eslint';
import rule, {
    buildComputeValueNotCompliantWarning,
    buildGetValueNotCompliantError,
    buildIsClearedByResetNotCompliantError,
} from '../../../lib/rules/property-decorators-errors';

const options = {
    parserOptions: {},
    parser: require.resolve('@typescript-eslint/parser'),
    env: {
        es6: true,
    },
};

const ruleTester = new RuleTester(options);

ruleTester.run('property-decorators-errors', rule, {
    valid: [
        {
            // No defaultValue() is OK
            code: `class Node1 {
                    @decorators.stringProperty<Node1, 'prop1'>({
                    })
                    prop1: string;
                }`,
        },

        {
            // computeValue() is too complex to be parsed by ts-to-sql
            code: `class Node1 {
                @decorators.stringProperty<Node1, 'prop1'>({
                    computeValue() { return tooComplexFunction(); }
                })
                prop1: string;
            }`,
        },
        {
            // getValue() is simple enough to be parsed by ts-to-sql
            code: `class Node1 {
                @decorators.stringProperty<Node1, 'prop1'>({
                    getValue() { return this.member?.id; }
                })
                prop1: string;
            }`,
        },
        {
            // getValue() simple enough to be parsed by ts-to-sql
            code: `class Node1 {
                    @decorators.stringProperty<Node1, 'prop1'>({
                        getValue() { return this.prop2 + 'xx'; }
                    })
                    prop1: string;
                }`,
        },
        {
            // isClearedByReset() simple enough to be parsed by ts-to-sql
            code: `class Node1 {
                    @decorators.stringProperty<Node1, 'prop1'>({
                        isClearedByReset() { return this.prop2 + 'xx'; }
                    })
                    prop1: string;
                }`,
        },
    ],
    invalid: [
        {
            // KO: computeValue() is simple enough to be parsed by ts-to-sql and thus should be renamed to getValue
            code: `class Node1 {
                @decorators.stringProperty<Node1, 'prop1'>({
                    computeValue() { return this.prop2 + 'xx'; }
                })
                prop1: string;
            }`,
            output: `class Node1 {
                @decorators.stringProperty<Node1, 'prop1'>({
                    getValue() { return this.prop2 + 'xx'; }
                })
                prop1: string;
            }`,
            errors: [
                {
                    messageId: 'error',
                    type: AST_NODE_TYPES.Property,
                    data: {
                        error: buildComputeValueNotCompliantWarning('prop1'),
                    },
                },
            ],
        },
        {
            // KO: getValue() too complex to be parsed by ts-to-sql
            code: `class Node1 {
                @decorators.stringProperty<Node1, 'prop1'>({
                    getValue() { return tooComplexFunction(); }
                })
                prop1: string;
            }`,
            output: `class Node1 {
                @decorators.stringProperty<Node1, 'prop1'>({
                    computeValue() { return tooComplexFunction(); }
                })
                prop1: string;
            }`,
            errors: [
                {
                    messageId: 'error',
                    type: AST_NODE_TYPES.CallExpression,
                    data: {
                        error: buildGetValueNotCompliantError('prop1', new Error('3:40: Unsupported call')),
                    },
                },
            ],
        },
        {
            // KO: isClearedByReset() too complex to be parsed by ts-to-sql
            code: `class Node1 {
                @decorators.stringProperty<Node1, 'prop1'>({
                    isClearedByReset() { return tooComplexFunction(); }
                })
                prop1: string;
            }`,
            output: `class Node1 {
                @decorators.stringProperty<Node1, 'prop1'>({
                    isClearedByReset() { return tooComplexFunction(); }
                })
                prop1: string;
            }`,
            errors: [
                {
                    messageId: 'error',
                    type: AST_NODE_TYPES.Property,
                    data: {
                        error: buildIsClearedByResetNotCompliantError('prop1'),
                    },
                },
            ],
        },
    ],
});
