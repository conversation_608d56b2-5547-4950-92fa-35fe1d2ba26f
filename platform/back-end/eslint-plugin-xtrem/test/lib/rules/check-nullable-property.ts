import { AST_NODE_TYPES } from '@typescript-eslint/utils';
import { RuleTester } from '@typescript-eslint/utils/ts-eslint';
import rule, { buildMissingNullUnionError } from '../../../lib/rules/check-nullable-property';

const options = {
    parserOptions: {},
    parser: require.resolve('@typescript-eslint/parser'),
    env: {
        es6: true,
    },
};

const ruleTester = new RuleTester(options);

ruleTester.run('check-nullable-property', rule, {
    valid: [
        {
            // prop1 is nullable and its return type is string | null
            code: `class Node1 {
                    @decorators.stringProperty<Node1, 'prop1'>({
                        isNullable: true
                    })
                    prop1: string | null;
                }`,
        },
        {
            code: `class Node1 {
                    @decorators.stringProperty<Node1, 'prop1'>({
                        isNullable: true
                    })
                    prop1: Promise<string | null>;
                }`,
        },
        {
            code: `class Node1 {
                    @decorators.stringProperty<Node1, 'prop1'>({
                        isNullable: true
                    })
                    prop1: Promise<string | null>;
                }`,
        },
    ],
    invalid: [
        {
            // KO: prop1 is nullable: its return type should be string | null
            code: `class Node1 {
                    @decorators.stringProperty<Node1, 'prop1'>({
                        isNullable: true
                    })
                    prop1: string;
                }`,
            output: `class Node1 {
                    @decorators.stringProperty<Node1, 'prop1'>({
                        isNullable: true
                    })
                    prop1: string | null;
                }`,
            errors: [
                {
                    messageId: 'error',
                    type: AST_NODE_TYPES.PropertyDefinition,
                    data: {
                        error: buildMissingNullUnionError('prop1'),
                    },
                },
            ],
        },

        {
            // KO: prop1 is nullable: its return type should be string | null
            code: `class Node1 {
                    @decorators.stringProperty<Node1, 'prop1'>({
                        isNullable: true
                    })
                    prop1: Promise<string>;
                }`,
            output: `class Node1 {
                    @decorators.stringProperty<Node1, 'prop1'>({
                        isNullable: true
                    })
                    prop1: Promise<string | null>;
                }`,
            errors: [
                {
                    messageId: 'error',
                    type: AST_NODE_TYPES.PropertyDefinition,
                    data: {
                        error: buildMissingNullUnionError('prop1'),
                    },
                },
            ],
        },
    ],
});
