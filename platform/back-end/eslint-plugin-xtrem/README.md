# eslint-plugin-xtrem

Lint rules for Xtrem development

## Installation

You'll first need to install [ESLint](http://eslint.org):

```
$ pnpm i eslint --save-dev
```

Next, install `eslint-plugin-xtrem`:

```
$ pnpm install eslint-plugin-xtrem --save-dev
```

**Note:** If you installed ESLint globally (using the `-g` flag) then you must also install `eslint-plugin-xtrem` globally.

## Usage

Add `xtrem` to the plugins section of your `.eslintrc` configuration file. You can omit the `eslint-plugin-` prefix:

```json
{
    "plugins": ["xtrem"]
}
```

Then configure the rules you want to use under the rules section.

```json
{
    "rules": {
        "xtrem/rule-name": 2
    }
}
```

## Supported Rules

-   Fill in provided rules here
