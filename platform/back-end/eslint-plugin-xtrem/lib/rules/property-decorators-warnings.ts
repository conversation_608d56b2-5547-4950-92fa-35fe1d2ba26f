/**
 * @fileoverview Check property decorators
 * <AUTHOR>
 */
import { ConversionError } from '@sage/xtrem-ts-to-sql';
import type { TSESLint } from '@typescript-eslint/utils';
import { TSESTree } from '@typescript-eslint/utils';
import { getEnclosingPropertyName, getErrorWhenConverting } from './property-decorators-errors';

type FunctionExpression = TSESTree.FunctionExpression;

export function buildDefaultValueNotCompliantError(propertyName: string): string {
    return `The function ${propertyName}.defaultValue() is too complex to be parsed. You will need to create a DataUpdateAction to support upgrades (see https://confluence.sage.com/display/XTREEM/Upgrade+Engine)`;
}

function checkDefaultValueFunction(context: any, fnToTest: FunctionExpression) {
    const error = getErrorWhenConverting(fnToTest);
    if (!error) return;
    const propertyName = getEnclosingPropertyName(fnToTest);
    context.report({
        node: error instanceof ConversionError ? error.node : fnToTest,
        messageId: 'error',
        data: {
            error: buildDefaultValueNotCompliantError(propertyName),
        },
    });
}

const rule: TSESLint.RuleModule<string, any> = {
    defaultOptions: [],
    meta: {
        type: 'problem',
        docs: {
            url: '',
            description: 'Check property decorators',
            recommended: 'recommended',
        },
        fixable: 'code', // or "code" or "whitespace"
        schema: [
            // fill in your schema
        ],
        messages: {
            error: '{{error}}',
        },
    },

    create(context: any) {
        // Retrieve all the property decorators
        const decoratorsSelector = 'PropertyDefinition > Decorator > CallExpression > ObjectExpression';
        return {
            // Rule: defaultValue() functions must be "ts-to-sql compliant"
            [`${decoratorsSelector} > Property[key.name="defaultValue"][value.type='FunctionExpression'] > FunctionExpression`](
                defaultValueFunction: FunctionExpression,
            ) {
                checkDefaultValueFunction(context, defaultValueFunction);
            },
        };
    },
};

export default rule;
