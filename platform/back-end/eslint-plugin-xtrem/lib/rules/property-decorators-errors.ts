/**
 * @fileoverview Check property decorators
 * <AUTHOR>
 */
import { EsLintConverter } from '@sage/xtrem-ts-to-sql';
import type { TSESLint } from '@typescript-eslint/utils';
import { TSESTree } from '@typescript-eslint/utils';
import * as estree from 'estree';

type FunctionExpression = TSESTree.FunctionExpression;
const { AST_NODE_TYPES } = TSESTree;

export function buildGetValueNotCompliantError(propertyName: string, error: Error): string {
    return `The function ${propertyName}.getValue() is too complex to be converted to SQL:  you won't be able to use it in filter and sort operations. Detailed message: ${error.message}`;
}

export function buildIsClearedByResetNotCompliantError(propertyName: string): string {
    return `The function ${propertyName}.isClearedByReset() is too complex to be converted to SQL:  you won't be able to use it in filter and sort operations`;
}

export function buildComputeValueNotCompliantWarning(propertyName: string): string {
    return `The function ${propertyName}.computeValue() can be parsed to SQL: rename it to getValue()`;
}

export function getErrorWhenConverting(fn: FunctionExpression) {
    try {
        new EsLintConverter({}).convertFunctionExpression(fn as unknown as estree.FunctionExpression);
        return undefined;
    } catch (ex) {
        return ex;
    }
}

function checkComputeValueFunction(context: any, fnToTest: FunctionExpression) {
    // 'computeValue' functions should not be "ts-to-sql" compliant
    const propertyName = (fnToTest as any).parent.parent.parent.parent.parent.key.name as string;
    const error = getErrorWhenConverting(fnToTest);
    if (error) return;
    context.report({
        node: (fnToTest as any).parent, // fnToTest.parent: puts the warning on the function itself to allow auto-fixes.
        messageId: 'error',
        data: {
            error: buildComputeValueNotCompliantWarning(propertyName),
        },
        fix: (fixer: any) => {
            // fnToTest.parent.key is the name of the function
            return fixer.replaceText((fnToTest as any).parent.key, 'getValue');
        },
    });
}

function checkGetValueFunction(context: any, fnToTest: FunctionExpression) {
    // 'getValue' functions must be "ts-to-sql" compliant
    const error = getErrorWhenConverting(fnToTest);
    if (!error) return;
    const propertyName = getEnclosingPropertyName(fnToTest);
    context.report({
        // fnToTest.parent: puts the warning on the function itself to allow auto-fixes.
        // fnToTest.parent is better for auto-fixes but error.node is better for locating the error.
        node: error.node ?? fnToTest.parent,
        messageId: 'error',
        data: {
            error: buildGetValueNotCompliantError(propertyName, error),
        },
        fix: (fixer: any) => {
            // fnToTest.parent.key is the name of the function
            return fixer.replaceText(getEnclosingFunctionName(fnToTest), 'computeValue');
        },
    });
}

function checkIsClearedByResetFunction(context: any, fnToTest: FunctionExpression) {
    // 'isClearedByReset' functions must be "ts-to-sql" compliant
    const error = getErrorWhenConverting(fnToTest);
    if (!error) return;

    const propertyName = getEnclosingPropertyName(fnToTest);
    context.report({
        node: fnToTest.parent!, // fnToTest.parent: puts the warning on the function itself to allow auto-fixes.
        messageId: 'error',
        data: {
            error: buildIsClearedByResetNotCompliantError(propertyName),
        },
    });
}

const rule: TSESLint.RuleModule<string, any> = {
    defaultOptions: [],
    meta: {
        type: 'problem',
        docs: {
            url: '',
            description: 'Check property decorators',
            recommended: 'strict',
        },
        fixable: 'code', // or "code" or "whitespace"
        schema: [
            // fill in your schema
        ],
        messages: {
            error: '{{error}}',
        },
    },

    create(context: any) {
        // Retrieve all the property decorators
        const decoratorsSelector = 'PropertyDefinition > Decorator > CallExpression > ObjectExpression';
        return {
            // Rule: computeValue() function can be parsed to SQL: rename it to getValue()"
            [`${decoratorsSelector} > Property[key.name="computeValue"][value.type='FunctionExpression'] > FunctionExpression`](
                getValueFunction: FunctionExpression,
            ) {
                checkComputeValueFunction(context, getValueFunction);
            },
            // Rule: getValue() functions must be "ts-to-sql compliant"
            [`${decoratorsSelector} > Property[key.name="getValue"][value.type='FunctionExpression'] > FunctionExpression`](
                getValueFunction: FunctionExpression,
            ) {
                checkGetValueFunction(context, getValueFunction);
            },
            // Rule: isClearedByResetFunction() functions must be "ts-to-sql compliant"
            [`${decoratorsSelector} > Property[key.name="isClearedByReset"][value.type='FunctionExpression'] > FunctionExpression`](
                isClearedByResetFunction: FunctionExpression,
            ) {
                checkIsClearedByResetFunction(context, isClearedByResetFunction);
            },
        };
    },
};

export function getEnclosingPropertyName(fnToTest: FunctionExpression) {
    const propertyNode = fnToTest.parent!.parent!.parent!.parent!.parent;
    if (
        !(
            propertyNode &&
            (propertyNode.type === AST_NODE_TYPES.Property ||
                propertyNode.type === AST_NODE_TYPES.PropertyDefinition) &&
            propertyNode.key.type === AST_NODE_TYPES.Identifier
        )
    ) {
        throw new Error('cannot find property name');
    }
    return propertyNode.key.name;
}

function getEnclosingFunctionName(fnToTest: FunctionExpression) {
    const propertyNode = fnToTest.parent;
    if (
        !(
            propertyNode &&
            (propertyNode.type === AST_NODE_TYPES.Property ||
                propertyNode.type === AST_NODE_TYPES.PropertyDefinition) &&
            propertyNode.key.type === AST_NODE_TYPES.Identifier
        )
    ) {
        throw new Error('cannot find function name');
    }
    return propertyNode.key;
}

export default rule;
