/**
 * @fileoverview Only allow code that may be translated to SQL
 * <AUTHOR>
 */
import { ConversionError, EsLintConverter } from '@sage/xtrem-ts-to-sql';
import type { TSESLint } from '@typescript-eslint/utils';
import type { FunctionExpression } from 'estree';

const rule: TSESLint.RuleModule<string, any> = {
    defaultOptions: [],
    meta: {
        type: 'problem',
        docs: {
            url: '',
            description: 'Only allow code that may be translated to SQL',
            recommended: 'strict',
        },
        fixable: undefined, // or "code" or "whitespace"
        schema: [
            // fill in your schema
        ],

        messages: {
            cannotBeConvertedToSql: 'Cannot be converted to SQL: {{error}}.',
        },
    },

    create(context: any) {
        const queryFilterSelector =
            "CallExpression[callee.type='MemberExpression'][callee.property.name='query']" +
            ' > ObjectExpression' +
            ' > Property[key.name="filter"]' +
            ' > FunctionExpression';
        const referenceJoinSelector =
            'PropertyDefinition' +
            ' > Decorator' +
            " > CallExpression[callee.property.name='referenceProperty']" +
            ' > ObjectExpression' +
            " > Property[key.name='join']" +
            ' > ObjectExpression' +
            ' > Property' +
            ' > FunctionExpression';

        const checkFunction = (node: FunctionExpression) => {
            try {
                new EsLintConverter({}).convertFunctionExpression(node);
            } catch (ex) {
                context.report({
                    node: ex instanceof ConversionError ? ex.node : node,
                    messageId: 'cannotBeConvertedToSql',
                    data: {
                        error: ex.message,
                    },
                });
            }
        };

        return {
            [queryFilterSelector](node: FunctionExpression) {
                checkFunction(node);
            },
            [referenceJoinSelector](node: FunctionExpression) {
                checkFunction(node);
            },
        };
    },
};

export default rule;
