/**
 * @fileoverview check the executionMode setting in graphql parameters.json files
 * <AUTHOR>
 */
import type { TSESLint } from '@typescript-eslint/utils';
import type { ObjectExpression } from 'estree';

const rule: TSESLint.RuleModule<string, any> = {
    defaultOptions: [],
    meta: {
        type: 'problem',
        docs: {
            url: '',
            description: 'check the executionMode setting in graphql parameters.json files',
            recommended: 'strict',
        },
        schema: [
            // fill in your schema
        ],

        messages: {
            executionModeOnly: 'executionMode must not be set to only',
        },
    },

    create(context: any) {
        const selector = 'ObjectExpression > Property[key.value="executionMode"][value.value="only"]';
        return {
            [selector](node: ObjectExpression) {
                context.report({
                    node,
                    messageId: 'executionModeOnly',
                });
            },
        };
    },
};

export default rule;
