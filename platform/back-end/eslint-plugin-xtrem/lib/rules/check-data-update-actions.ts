/**
 * @fileoverview Check dataUpdateActions
 * <AUTHOR>
 */
import { EsLintConverter } from '@sage/xtrem-ts-to-sql';
import type { TSESLint } from '@typescript-eslint/utils';
import type { FunctionExpression } from 'estree';

export function buildComplexDataUpdateActionMemberError(memberName: string): string {
    return `The member ${memberName} is too complex to be converted into a SQL statement.`;
}

function checkIsParseable(context: any, memberFunction: FunctionExpression) {
    const memberName = (memberFunction as any).parent.key.name as string;
    try {
        new EsLintConverter(context).convertFunctionExpression(memberFunction);
    } catch (ex) {
        // ts-to-sql can't parse this function
        context.report({
            node: (memberFunction as any).parent, // Select the whole property
            messageId: 'error',
            data: {
                error: buildComplexDataUpdateActionMemberError(memberName),
            },
        });
    }
}

const rule: TSESLint.RuleModule<string, any> = {
    defaultOptions: [],
    meta: {
        type: 'problem',
        docs: {
            url: '',
            description: 'Check dataUpdateActions',
            recommended: 'strict',
        },
        fixable: undefined, // or "code" or "whitespace"
        schema: [
            // fill in your schema
        ],

        messages: {
            error: '{{error}}',
        },
    },

    create(context: any) {
        // A dataAction looks like:
        //        export const addSalesUnitOfMeasure = new DataUpdateAction({
        //            description: 'SalesUnitOfMeasure field from item node: data upgrade (->1.3.11)',
        //            node: () => xtremMasterData.nodes.Item,
        //            set: {
        //                salesUnitOfMeasure() {
        //                    return this.stockUnit;
        //                },
        //            },
        //        });
        const membersSelector =
            'NewExpression[callee.name="DataUpdateAction"] > ObjectExpression > Property[key.name="set"] > ObjectExpression > Property > FunctionExpression';
        return {
            // All the members (salesUnitOfMeasure, ...) must be ts-to-sql-compliant
            [`${membersSelector}`](functionExpression: FunctionExpression) {
                checkIsParseable(context, functionExpression);
            },
        };
    },
};

export default rule;
