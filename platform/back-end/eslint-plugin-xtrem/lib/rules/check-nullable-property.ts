/**
 * @fileoverview Check property decorators
 * <AUTHOR>
 */
import { AST_NODE_TYPES } from '@typescript-eslint/utils';
import type { TSESLint, TSESTree } from '@typescript-eslint/utils';

export function buildMissingNullUnionError(propertyName: string): string {
    return `The property ${propertyName} is declared as nullable: its return type should be xxx | null.`;
}

const rule: TSESLint.RuleModule<string, any> = {
    defaultOptions: [],
    meta: {
        type: 'problem',
        docs: {
            url: '',
            description: 'Check nullable property',
            recommended: 'recommended',
        },
        fixable: 'code',
        schema: [
            // fill in your schema
        ],

        messages: {
            error: '{{error}}',
        },
    },

    create(context) {
        // Retrieve all the property decorators
        const decoratorsSelector = 'PropertyDefinition > Decorator > CallExpression > ObjectExpression';
        return {
            // Rule: a nullable property must have a return type that looks like xxx | null
            [`${decoratorsSelector} > Property[key.name="isNullable"][value.raw="true"]`](property: TSESTree.Property) {
                // Walking up the selector to get the PropertyDefinition
                const classProperty = property.parent?.parent?.parent?.parent as TSESTree.PropertyDefinition;
                if (classProperty.key.type !== 'Identifier') return;
                let typeAnnotation = classProperty.typeAnnotation?.typeAnnotation as TSESTree.TypeNode;
                // unwrap Promise<...> and Reference<...>
                if (
                    typeAnnotation.type === 'TSTypeReference' &&
                    typeAnnotation.typeName.type === 'Identifier' &&
                    /^(Promise|Reference)$/.test(typeAnnotation.typeName.name)
                ) {
                    typeAnnotation = typeAnnotation.typeParameters?.params[0]!;
                }
                if (
                    typeAnnotation.type === AST_NODE_TYPES.TSUnionType &&
                    typeAnnotation.types[1]?.type === AST_NODE_TYPES.TSNullKeyword
                ) {
                    // That's ok
                    return;
                }
                context.report({
                    node: classProperty, // Put the error on the property name
                    messageId: 'error',
                    data: {
                        error: buildMissingNullUnionError(classProperty.key.name),
                    },
                    fix: fixer =>
                        // fnToTest.parent.key is the name of the function
                        fixer.insertTextAfter(typeAnnotation, ' | null'),
                });
            },
        };
    },
};

export default rule;
