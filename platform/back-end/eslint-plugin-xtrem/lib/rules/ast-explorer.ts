/**
 * @fileoverview AST explorer
 * <AUTHOR>
 */

import type { TSESLint } from '@typescript-eslint/utils';

const rule: TSESLint.RuleModule<string, any> = {
    defaultOptions: [],
    meta: {
        type: 'suggestion',
        docs: {
            url: '',
            description: 'ast explorer',
            recommended: 'stylistic',
        },
        fixable: undefined, // or "code" or "whitespace"
        schema: [],

        messages: {},
    },

    create(context: any) {
        const selectAll = '*';
        let currentFilename = '';

        // eslint-disable-next-line no-console
        const log = console.log;

        const logAttributes = (ident: string, item: any) => {
            Object.keys(item).forEach((key: any) => {
                if (
                    !/^(async|body|computed|declare|generator|kind|literal|loc|method|parent|range|raw|shorthand|source|static|specifiers|tokens|type|typeAnnotation|typeParameters)$/.test(
                        key,
                    )
                ) {
                    if (item[key] && typeof item[key] === 'object') {
                        if (Array.isArray(item[key])) {
                            log(`${ident}  ${key}.length: ${item[key].length}`); // Stop here!
                        } else {
                            log(`${ident}  ${key}`);
                            logAttributes(`${ident} `, item[key]);
                        }
                    } else if (item[key]) log(`${ident}  ${key}: ${item[key]}`);
                }
            });
        };
        return {
            [selectAll]: (node: any) => {
                const filename = context.getFilename();
                if (currentFilename !== filename) {
                    currentFilename = filename;
                    log(`${Array(121).join('-')}\nFile ${filename}:`);
                }
                let ident: string = '';
                for (let parent: any = node; parent; parent = parent.parent, ident += '   ');
                const position = node.loc.start;
                log(`${ident}> ${node.type} (${position.line}:${position.column + 1})`);
                logAttributes(ident, node);
            },
        };
    },
};

export default rule;
