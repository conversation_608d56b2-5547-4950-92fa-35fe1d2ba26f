/**
 * @fileoverview Check that bundle code only uses the _safe_ subset of TypeScript that we authorize
 * <AUTHOR>
 */
import type { TSESLint } from '@typescript-eslint/utils';
import type { ClassDeclaration } from 'estree';

const rule: TSESLint.RuleModule<string, any> = {
    defaultOptions: [],
    meta: {
        type: 'problem',
        docs: {
            url: '',
            description: "Force call to super when inside a data type's controlValue method",
            recommended: 'strict',
        },
        fixable: undefined, // or "code" or "whitespace"
        schema: [],

        messages: {
            mustCallSuper: 'Mandatory call to super.controlValue: {{error}}.',
        },
    },

    create(context: any) {
        const inheritFromDataType = ['DataType'] as string[];
        const hasSuperClassSelector = 'ClassDeclaration[superClass]';

        const controlValueSelector =
            'ClassDeclaration[superClass]:has(' +
            " MethodDefinition[key.name='controlValue']" +
            " FunctionExpression:not(:has(CallExpression[callee.object.type='Super'][callee.property.name='controlValue'])))";

        return {
            [hasSuperClassSelector](node: ClassDeclaration) {
                const name = (node as any).name;
                const superClassName = (node as any).superClass.name;
                if (inheritFromDataType.includes(superClassName)) inheritFromDataType.push(name);
            },

            [controlValueSelector](node: ClassDeclaration) {
                if (inheritFromDataType.includes((node as any).name)) {
                    context.report({
                        node,
                        messageId: 'mustCallSuper',
                        data: {
                            error: 'DataType: controlValue must call super.controlValue',
                        },
                    });
                }
            },
        };
    },
};

export default rule;
