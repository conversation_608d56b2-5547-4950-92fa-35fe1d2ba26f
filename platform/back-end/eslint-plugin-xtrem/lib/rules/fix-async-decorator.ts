/**
 * @fileoverview Adds Promise<...> and Reference<...> to the types of decorated properties.
 * <AUTHOR>
 */
/* TEMPORARY RULE FOR NODE 16 CONVERSION */
import type { TSESLint, TSESTree } from '@typescript-eslint/utils';

type PropertyDefinition = TSESTree.PropertyDefinition;

type MessageId = 'fixAsyncDecorator' | 'fixPropertyType' | 'fixPropertyReadonly';

const rule: TSESLint.RuleModule<MessageId, any> = {
    defaultOptions: [],
    meta: {
        type: 'suggestion',
        docs: {
            url: '',
            description: 'Prepare decorators for async/await',
            recommended: 'strict',
        },
        fixable: 'code',
        schema: [],

        messages: {
            fixAsyncDecorator: 'Fix decorator for async/await',
            fixPropertyType: 'Wrap property type with {{wrapper}}(...)',
            fixPropertyReadonly: 'Fix missing readonly property modifier',
        },
    },

    create(context) {
        return {
            Decorator(decorator) {
                if (decorator.expression.type !== 'CallExpression') return;
                if (decorator.expression.callee.type !== 'MemberExpression') return;
                if (decorator.expression.callee.object.type !== 'Identifier') return;
                if (decorator.expression.callee.object.name !== 'decorators') return;

                if (decorator.expression.callee.property.type !== 'Identifier') return;
                const decoratorName = decorator.expression.callee.property.name;

                if (decoratorName.endsWith('Property') || decoratorName.endsWith('PropertyOverride')) {
                    if (
                        decorator.parent?.type !== 'PropertyDefinition' &&
                        decorator.parent?.type !== ('ClassProperty' as any)
                    )
                        throw new Error(`${decoratorName}: invalid decorator target: ${decorator.parent?.type}`);

                    const nodeProperty = decorator.parent as PropertyDefinition;
                    if (!nodeProperty.typeAnnotation) throw new Error(`${decoratorName}: type annotation missing`);

                    if (!nodeProperty.readonly) {
                        context.report({
                            node: nodeProperty,
                            messageId: 'fixPropertyReadonly',
                            fix: fixer => {
                                return fixer.insertTextBefore(nodeProperty.key, 'readonly ');
                            },
                        });
                    }

                    if (decoratorName.startsWith('collectionProperty')) return;

                    const typeAnnotation = nodeProperty.typeAnnotation.typeAnnotation;
                    const expectedTypeWrapper = decoratorName.startsWith('referenceProperty') ? 'Reference' : 'Promise';
                    if (
                        typeAnnotation.type === 'TSTypeReference' &&
                        typeAnnotation.typeName.type === 'Identifier' &&
                        /^(Promise|Reference)$/.test(typeAnnotation.typeName.name)
                    )
                        return;

                    context.report({
                        node: typeAnnotation,
                        messageId: 'fixPropertyType',
                        data: { wrapper: expectedTypeWrapper },
                        fix: (fixer: TSESLint.RuleFixer) => {
                            const typeText = context.getSourceCode().getText(typeAnnotation);
                            return fixer.replaceText(typeAnnotation, `${expectedTypeWrapper}<${typeText}>`);
                        },
                    });
                }
            },
        };
    },
};

export default rule;
