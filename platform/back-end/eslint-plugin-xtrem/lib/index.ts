/**
 * @fileoverview Lint rules for Xtrem development
 * <AUTHOR>
 */
import { transform } from 'lodash';

const requireIndex = require('requireindex');

// import all rules in lib/rules
const transformDir = (dir: string) =>
    transform(requireIndex(`${__dirname}/${dir}`), (result: any, val: any, key: string) => {
        result[key] = val.default;
    });

const rules = transformDir('rules');

const configs = {
    'recommended-json': {
        plugins: ['@sage/xtrem'],
        rules: {
            '@sage/xtrem/no-exclusive-graphql-tests': 'error',
            '@typescript-eslint/*': 'off',
            '@typescript-eslint/quotes': 'off',
            '@typescript-eslint/semi': 'off',
            '@typescript-eslint/comma-dangle': 'off',
            '@typescript-eslint/no-unused-expressions': 'off',
        },
    },
    'recommended-ts': {
        plugins: ['@sage/xtrem'],
        rules: {
            // Enabled for debug
            // '@sage/xtrem/ast-explorer': 'error',
            '@sage/xtrem/check-nullable-property': 'warn',
            '@sage/xtrem/property-decorators-errors': 'error',
            '@sage/xtrem/property-decorators-warnings': 'warn',
            '@sage/xtrem/check-data-update-actions': 'error',
            '@sage/xtrem/sql-compatible': 'error',
            '@sage/xtrem/call-super-in-control': 'error',
        },
    },
    'ast-explorer-ts': {
        plugins: ['@sage/xtrem'],
        rules: {
            '@sage/xtrem/ast-explorer': 'warn',
        },
    },
};

export { configs, rules };
