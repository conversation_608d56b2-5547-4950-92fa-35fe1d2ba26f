{"name": "@sage/xtrem-minify", "version": "58.0.2", "description": "Build tool for Xtrem development", "keywords": ["build"], "bin": {"xtrem-minify": "./bin/xtrem-minify"}, "author": "Sage", "files": ["bin/xtrem-minify", "build/*.d.ts", "build/*.js", "build/*.map", "build/lib/*.d.ts", "build/lib/*.js", "build/lib/*.map", "package.json", "README.md", "license"], "license": "MIT", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/lib/index.js", "dependencies": {"glob": "^11.0.0", "semver": "^7.6.3", "terser": "5.43.1"}, "devDependencies": {"@types/chai": "^4.3.6", "@types/chai-as-promised": "^7.1.6", "@types/mocha": "10.0.10", "@types/semver": "^7.5.2", "@types/source-map-support": "^0.5.7", "c8": "^10.1.2", "chai": "^4.3.10", "chai-as-promised": "^7.1.1", "copyfiles": "^2.1.0", "cross-env": "^7.0.3", "eslint": "^8.49.0", "mocha": "^10.8.2", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "~5.8.3"}, "scripts": {"build": "tsc -b -v .", "build:binary": "pnpm clean && pnpm build && node ./build/lib/cli -c -z \"build/**/*.js\"  -\"build/test/**/*.js\"", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build junit-report*", "lint": "eslint -c .eslintrc.js --ext .ts lib test", "lint:filename": "eslint --no-eslintrc -c .eslintrc-filename.js \"**\"", "test": "pnpm build && cross-env TZ=CET REPORTER=custom mocha --recursive --exit \"test/**/*@(-|.)test.ts\"", "test:ci": "pnpm build && cross-env JUNIT_REPORT_PATH=junit-report-eslint-plugin-xtrem.xml JUNIT_REPORT_NAME='eslint-plugin-xtrem' c8 --reporter=lcov --reporter=json --reporter=cobertura --reporter=text mocha  --recursive --exit \"test/**/*.ts\" --reporter mocha-jenkins-reporter"}}