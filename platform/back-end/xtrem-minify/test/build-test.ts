import { assert } from 'chai';
import * as fs from 'fs';
import * as path from 'path';
import { compileFile } from '../lib';

async function testCompile(args: object) {
    const filename = path.join(__dirname, '..', 'build', 'test', 'fixtures', 'test-script.js');

    const compiled = await compileFile({ ...args, filename });

    assert.strictEqual(compiled, filename, 'Compiled output should match the input filename');

    const output = fs.readFileSync(path.join(__dirname, '..', 'build', 'test', 'fixtures', 'test-script.js'), 'utf8');
    assert.isTrue(output.length > 0, 'Output file should not be empty');
    assert.isTrue(output.startsWith('/* Copyright (c)'), 'Output file should start with a copyright comment');
    // eslint-disable-next-line import/no-dynamic-require,global-require
    const required = require(compiled);

    assert(Object.keys(required).includes('getDirectoriesFromPath'));
}

describe('Build', () => {
    let originalContent: string;
    before(() => {
        const filename = path.join(__dirname, '..', 'build', 'test', 'fixtures', 'test-script.js');
        if (fs.existsSync(filename)) {
            // save the original file
            originalContent = fs.readFileSync(filename, 'utf8');
        }
    });
    afterEach(() => {
        const filename = path.join(__dirname, '..', 'build', 'test', 'fixtures', 'test-script.js');
        if (originalContent) {
            // restore the original file
            fs.writeFileSync(filename, originalContent, 'utf8');
        }
    });

    it('Compile - minify js', async () => {
        await testCompile({ compileAsModule: true });
    });
});
