const { Interpreter } = require('../lib/interpreter');

const scripts = [
    `
const obj = {
    i: 10,
    b: () => this,
    c() {
        return this;
    },
};

obj.b(); // logs undefined, Window { /* … */ } (or the global object)
obj.c(); // logs 10, Object { /* … */ }
return obj;
`,

    `
const obj = {
    i: 10,
    // b: () => alert(this.i, this),
    b: () => alert(JSON.stringify(this)),
    c() {
        // alert(this.i, this);

        alert(JSON.stringify(this));
    },
};

obj.b(); // logs undefined, Window { /* … */ } (or the global object)
obj.c(); // logs 10, Object { /* … */ }
`,
];

// Set up 'alert' as an interface to Node's console.log.
const initFunc = (interpreter, globalObject) => {
    const wrapper = text => {
        console.log('alert:', typeof text, String(text));
    };
    interpreter.setProperty(globalObject, 'alert', interpreter.createNativeFunction(wrapper));
    interpreter.setProperty(
        globalObject,
        'queryResponse',
        interpreter.nativeToPseudo({ edges: [{ node: 'aaa bbb ccc' }] }),
    );
};

function jsEval(code) {
    try {
        const interpreter = new Interpreter(`(() => { ${code} })()`, {
            initFunc,
            timeout: 1000,
            strict: true,
            allowAsync: false,
        });
        interpreter.run();
        const nativeValue = interpreter.pseudoToNative(interpreter.value);
        console.log(`done in ${interpreter.elapsed} ms`, nativeValue);
        return nativeValue;
    } catch (e) {
        console.log(`ERROR: ${e.message}`);
        return { message: e.message };
    }
}

const values = scripts.slice(0, 10).map(sourceCode => jsEval(sourceCode));

console.log('\nvalues:', values);
