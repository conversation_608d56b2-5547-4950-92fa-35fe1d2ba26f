import { assert } from 'chai';
import { Interpreter, InterpreterOptions } from '../lib/interpreter';

const evalTimeout = 250;
const safeEval = (code: string | Function, options?: InterpreterOptions) =>
    Interpreter.safeEval(code, { timeout: evalTimeout, ...options });

describe('js interpreter array', () => {
    it('init array', () => {
        assert.deepEqual(safeEval('const a = []; return a;')?.value, []);
        assert.deepEqual(safeEval('const a = [1]; return a;')?.value, [1]);
        assert.deepEqual(safeEval('const a = ["a"]; return a;')?.value, ['a']);
        assert.deepEqual(safeEval('const a = new Array(); return a;')?.value, []);
    });

    it('populate array', () => {
        assert.deepEqual(safeEval('const years = [2021,2022,2023]; return years[2];')?.value, 2023);
        assert.deepEqual(safeEval('const years = [2021,2022,2023]; return years["2"];')?.value, 2023);
        assert.deepEqual(
            safeEval('const years = [2021,2022,2023]; years["02"] = 2002; return years["2"] === years["02"];')?.value,
            false,
        );

        let code = 'const fruits = []; fruits.push("banana", "apple", "peach");';
        const fruitsValue = () => safeEval(`${code} ${returnFruits}`)?.value;

        const returnFruits = 'return fruits;';
        let fruits;
        assert.deepEqual(fruitsValue(), ['banana', 'apple', 'peach']);

        code += ' fruits[5] = "mango";';
        fruits = fruitsValue();
        assert.deepEqual(Object.keys(fruits), ['0', '1', '2', '5']);
        // eslint-disable-next-line no-sparse-arrays
        assert.deepEqual(fruits, ['banana', 'apple', 'peach', , , 'mango']);
        assert.strictEqual(fruits.length, 6);

        code += ' fruits.length = 10;';
        fruits = fruitsValue();
        // eslint-disable-next-line no-sparse-arrays
        assert.deepEqual(fruits, ['banana', 'apple', 'peach', , , 'mango', , , , ,]);
        assert.deepEqual(Object.keys(fruits), ['0', '1', '2', '5']);
        assert.strictEqual(fruits.length, 10);
        assert.strictEqual(fruits[8], undefined);

        code += ' fruits.length = 2;';
        fruits = fruitsValue();
        assert.deepEqual(fruits, ['banana', 'apple']);
        assert.deepEqual(Object.keys(fruits), ['0', '1']);
        assert.strictEqual(fruits.length, 2);
    });
});
