import { assert } from 'chai';
import { Interpreter, InterpreterOptions } from '../lib/interpreter';

const evalTimeout = 250;
const safeEval = (code: string | Function, options?: InterpreterOptions) =>
    Interpreter.safeEval(code, { timeout: evalTimeout, ...options });

describe('js interpreter', () => {
    describe('timeout', () => {
        it('infinite while loop should throw an error', () => {
            assert.throws(() => safeEval('while(true) {}'), `Script execution timed out after ${evalTimeout}ms`);
        });

        it('infinite do while loop should throw an error', () => {
            assert.throws(() => safeEval('do {} while(true)'), `Script execution timed out after ${evalTimeout}ms`);
        });

        it('infinite for var loop should throw an error', () => {
            assert.throws(
                () => safeEval('for(var i = 0; i === 0;) {}'),
                `Script execution timed out after ${evalTimeout}ms`,
            );
        });

        it('infinite for ;; loop should throw an error', () => {
            assert.throws(() => safeEval('for(;;) {}'), `Script execution timed out after ${evalTimeout}ms`);
        });

        it('infinite recursive function should throw an error', () => {
            assert.throws(
                () => safeEval('function f() { return f();} return f();'),
                `Script execution timed out after ${evalTimeout}ms`,
            );
        });

        it('infinite recursive arrow function should throw an error', () => {
            assert.throws(
                () => safeEval('var f = () => f(); return f();'),
                `Script execution timed out after ${evalTimeout}ms`,
            );
        });
    });

    describe('node js expressions', () => {
        it('require call should throw an error', () => {
            assert.throws(() => safeEval('return require("fs");'), '[CallExpression] require is not defined');
        });

        it('node js identifiers should throw an error', () => {
            const identifiers = ['module', 'global', 'globalThis'];
            identifiers.forEach(identifier =>
                assert.throws(() => safeEval(`return ${identifier};`), `[Identifier] ${identifier} is not defined`),
            );
        });
    });

    describe('escape sandbox', () => {
        it('process.exit should throw an error', () => {
            assert.throws(
                () => safeEval('return {}.constructor.constructor("process.exit(1)");'),
                '[CallExpression] eval not allowed',
            );
            assert.throws(
                () => safeEval('return {}.constructor.constructor("process.exit(1)")();'),
                '[CallExpression] eval not allowed',
            );
        });

        it('vm2 cve should throw an error', () => {
            const cves = [
                {
                    id: 'CVE-2023-37466',
                    url: 'https://gist.github.com/leesh3288/f693061e6523c97274ad5298eb2c74e9',
                    code: `
                        async function fn() {
                            (function stack() {
                                new Error().stack;
                                stack();
                            })();
                        }
                        p = fn();
                        p.constructor = {
                        [Symbol.species]: class FakePromise {
                            constructor(executor) {
                                executor(
                                    (x) => x,
                                    (err) => { return err.constructor.constructor('return process')().mainModule.require('child_process').execSync('touch pwned'); }
                                )
                            }
                        }
                        };
                        p.then();
                        `,
                    error: 'async not allowed',
                },

                {
                    id: 'CVE-2023-37903',
                    url: 'https://gist.github.com/leesh3288/e4aa7b90417b0b0ac7bcd5b09ac7d3bd',
                    code: `
                        const customInspectSymbol = Symbol.for('nodejs.util.inspect.custom');

                        obj = {
                            [customInspectSymbol]: (depth, opt, inspect) => {
                                inspect.constructor('return process')().mainModule.require('child_process').execSync('touch pwned');
                            },
                            valueOf: undefined,
                            constructor: undefined,
                        }

                        WebAssembly.compileStreaming(obj).catch(()=>{});
                        `,
                    error: '[Identifier] Symbol is not defined',
                },
            ];

            // eslint-disable-next-line no-restricted-syntax
            for (const cve of cves) {
                assert.throws(() => safeEval(cve.code), cve.error);
            }
        });

        it('require should throw an error', () => {
            assert.throws(
                () => safeEval('return {}.constructor.constructor("require(\'fs\');")();'),
                '[CallExpression] eval not allowed',
            );
        });
    });

    describe('async code', () => {
        it('setTimeout should throw an error', () => {
            assert.throws(() => safeEval("setTimeout(() => 'string', 5000);"), '[CallExpression] task not allowed');
        });
        it('setInterval should throw an error', () => {
            assert.throws(() => safeEval("setInterval(() => 'string', 5000);"), '[CallExpression] task not allowed');
        });
    });

    describe('safe eval', () => {
        it('can safely eval code returning primitive types', () => {
            assert.strictEqual(safeEval('return "string";')?.value, 'string');
            assert.strictEqual(safeEval('return 1234;')?.value, 1234);
            assert.strictEqual(safeEval('return -5678;')?.value, -5678);
            assert.strictEqual(safeEval('return 1.1;')?.value, 1.1);
            assert.strictEqual(safeEval('return -2.7;')?.value, -2.7);
            assert.strictEqual(safeEval('return true;')?.value, true);
            assert.strictEqual(safeEval('return false;')?.value, false);
            assert.strictEqual(safeEval('return null;')?.value, null);
            assert.strictEqual(safeEval('return undefined;')?.value, undefined);
            assert.ok(Number.isNaN(safeEval('return Number.NaN;').value));

            const dateString = '2023-09-05';
            assert.strictEqual(safeEval(`return Date.parse('${dateString}');`)?.value, Date.parse(dateString));
            const datetimeString = '2023-09-05T01:02:03Z';
            assert.strictEqual(safeEval(`return Date.parse('${datetimeString}');`)?.value, Date.parse(datetimeString));
        });

        it('can safely eval code returning empty object', () => {
            assert.deepEqual(safeEval('return {};')?.value, {});
            assert.deepEqual(safeEval('return {}.constructor;')?.value, {});
            assert.deepEqual(safeEval('return {}.constructor.constructor;')?.value, {});
        });

        it('cannot use eval function', () => {
            assert.throws(() => safeEval("return eval('1 + 1')"), '[CallExpression] eval not allowed');
        });

        it('can safely eval code', () => {
            assert.strictEqual(safeEval('return {}.foo;').value, undefined);
            assert.strictEqual(safeEval('return { foo: "foo"}.foo;').value, 'foo');
            assert.strictEqual(
                safeEval(`var a = 'string-a';
            a = 'string-b';
            return a;
            `).value,
                'string-b',
            );

            assert.deepEqual(safeEval('const a = {p1: 1, p2: 2}; delete a.p1; return a;').value, { p2: 2 });
            // eslint-disable-next-line no-sparse-arrays
            assert.deepEqual(safeEval('const a = [1, 2]; delete a[0]; return a;').value, [, 2]);
        });

        it('can safely eval chain expression', () => {
            assert.strictEqual(safeEval('var a; return a?.b;').value, undefined);
            assert.strictEqual(safeEval('var a; return a?.b[0];').value, undefined);
            assert.strictEqual(safeEval('var a; return a?.b();').value, undefined);
            assert.strictEqual(safeEval('var a; return a?.[0]?.b;').value, undefined);
            assert.strictEqual(safeEval('var a={}; return a.b?.c;').value, undefined);
            assert.strictEqual(safeEval('var a = []; return a[0]?.b;').value, undefined);
            assert.strictEqual(safeEval('var a = {}; return a.b?.c;').value, undefined);
            assert.strictEqual(safeEval('function f() { return {};} ; return f()?.b;').value, undefined);
        });

        it('can safely eval capitalize active users', () => {
            const capitalizeActiveUsers = () => {
                function capitalize(word: string) {
                    return word?.replace(/^(.)/, c => c.toUpperCase());
                }

                const activeUsers = queryResponse.xtremSystem.user.query.edges.filter(edge => edge.node.isActive);

                const capitalizedUsers = activeUsers.map(edge => {
                    const pieces = edge.node.email.split('@');
                    const segments = pieces[0].split('.');
                    const firstName = capitalize(segments[0]);
                    const lastName = capitalize(segments[1]);
                    edge.node.email = `${firstName}${lastName ? `.${lastName}` : ''}@${pieces[1]}`;

                    return edge;
                });

                return capitalizedUsers;
            };

            const queryResponse = {
                xtremSystem: {
                    user: {
                        query: {
                            edges: [
                                { node: { email: '<EMAIL>', isActive: true } },
                                { node: { email: '<EMAIL>', isActive: true } },
                                { node: { email: '<EMAIL>', isActive: true } },
                                { node: { email: '<EMAIL>', isActive: true } },
                                { node: { email: '<EMAIL>', isActive: true } },
                                { node: { email: '<EMAIL>', isActive: false } },
                            ],
                        },
                    },
                },
            };

            assert.deepEqual(safeEval(capitalizeActiveUsers, { sandbox: { queryResponse } })?.value, [
                { node: { email: '<EMAIL>', isActive: true } },
                { node: { email: '<EMAIL>', isActive: true } },
                { node: { email: '<EMAIL>', isActive: true } },
                { node: { email: '<EMAIL>', isActive: true } },
                { node: { email: '<EMAIL>', isActive: true } },
            ]);
        });

        it('can modify const objects', () => {
            assert.deepEqual(
                safeEval(`const a = {};
            a.stringValue = 'string';
            return a;
            `).value,
                { stringValue: 'string' },
            );

            assert.deepEqual(
                safeEval(`const a = [];
            a.push('string 1');
            a.push('string 2');
            return a;
            `).value,
                ['string 1', 'string 2'],
            );
        });

        it('cannot re-assign const variable', () => {
            assert.throws(
                () =>
                    safeEval(`const a = 'string-a';
            a = 'string-b';
            return a;
            `),
                "[AssignmentExpression] Cannot assign to read only variable 'a'",
            );

            assert.throws(
                () =>
                    safeEval(`const a = [];
            a = [];
            return a;
            `),
                "[AssignmentExpression] Cannot assign to read only variable 'a'",
            );
        });

        it('Error', () => {
            // to investigate, errors have non enumerable properties that are not correctly handled by the pseudoToNative transformation
            const error1 = safeEval("const e = new Error('error message'); return e;").value;
            assert.deepEqual(error1, {});
            // assert.strictEqual(error1.message, 'error message');

            const error2 = safeEval("const e = new SyntaxError('syntax error message'); return e;");
            assert.deepEqual(error2.value, {});

            // assert.deepEqual(safeEval("const e = new Error('error message'); return e;").value, {
            //     message: 'error message',
            //     stack: 'Error: error message\n  at code:1:19\n  at code:1:0',
            // });
            // assert.deepEqual(safeEval("const e = new SyntaxError('syntax error message'); return e;").value, {
            //     message: 'syntax error message',
            //     stack: 'SyntaxError: syntax error message\n  at code:1:19\n  at code:1:0',
            // });
        });
    });
});
