import { assert } from 'chai';
import { JsObject } from '../lib/interpreter/js-object';

const debug = require('debug');

describe('js object', () => {
    it('init js objects', () => {
        const objectProto = new JsObject(null);
        const functionProto = new JsObject(objectProto);

        assert.deepEqual(objectProto, Object.create(null));
        assert.strictEqual(String(objectProto), '[object JsObject]');
        assert.strictEqual(objectProto.class, 'Object');

        assert.deepEqual(functionProto, Object.create(null));
        assert.strictEqual(String(functionProto), '[object JsObject]');
        assert.strictEqual(objectProto.class, 'Object');
        assert.strictEqual(functionProto.proto, objectProto);

        debug.enable('xtrem:js');
        const jsObj = new JsObject(null);
        assert.deepEqual(jsObj as any, {
            _class: 'Object',
            _getter: {},
            _properties: {},
            _proto: null,
            _setter: {},
        });
        jsObj.class = 'Foo';
        assert.strictEqual(jsObj.class, 'Foo');
        assert.strictEqual((jsObj as any)._class, 'Foo');
        jsObj.proto = functionProto;
        assert.strictEqual(jsObj.proto, functionProto);
        assert.strictEqual((jsObj as any)._proto, functionProto);

        debug.disable('xtrem:js');
        assert.deepEqual(new JsObject(null) as any, {});
    });
});
