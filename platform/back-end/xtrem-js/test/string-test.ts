import { assert } from 'chai';
import { Interpreter, InterpreterOptions } from '../lib/interpreter';

const evalTimeout = 250;
const safeEval = (code: string | Function, options?: InterpreterOptions) =>
    Interpreter.safeEval(code, { timeout: evalTimeout, ...options });

describe('js interpreter string', () => {
    it('init string', () => {
        assert.deepEqual(safeEval("return 'abc'")?.value, 'abc');
        assert.deepEqual(safeEval('return String(1)')?.value, '1');
        assert.deepEqual(safeEval('return String(+1)')?.value, '1');
        assert.deepEqual(safeEval('return String(-1)')?.value, '-1');
        assert.deepEqual(safeEval('return String(1.2)')?.value, '1.2');
        assert.deepEqual(safeEval('return String(+1.2)')?.value, '1.2');
        assert.deepEqual(safeEval('return String(-1.2)')?.value, '-1.2');
        assert.deepEqual(safeEval('return String(true)')?.value, 'true');
        assert.deepEqual(safeEval('return String(false)')?.value, 'false');

        const dateString = '2023-09-05';
        assert.strictEqual(safeEval(`return String(Date.parse('${dateString}'));`)?.value, '1693872000000');
        const datetimeString = '2023-09-05T01:02:03Z';
        assert.strictEqual(safeEval(`return String(Date.parse('${datetimeString}'));`)?.value, '1693875723000');
    });

    it('template literals', () => {
        assert.deepEqual(
            safeEval(() => {
                const s = 'abc';
                const n = 1;
                return `string:${s}, number:${n}`;
            })?.value,
            'string:abc, number:1',
        );

        assert.deepEqual(
            safeEval(() => {
                const s = 'abc';
                const n = 1;
                return `string:${s}\nnumber:${n}`;
            })?.value,
            'string:abc\nnumber:1',
        );

        assert.deepEqual(
            safeEval(() => {
                const s = 'abc';
                const n = 1;
                return `${s}, number:${n}`;
            })?.value,
            'abc, number:1',
        );

        assert.deepEqual(
            safeEval(() => {
                const s = 'abc';
                const n = 1;
                return `${s}, number:${n} end`;
            })?.value,
            'abc, number:1 end',
        );

        assert.deepEqual(
            safeEval(() => {
                const s = 'abc';
                const n = 1;
                return `${`${s}def`}, number:${n + 100} end`;
            })?.value,
            'abcdef, number:101 end',
        );
    });
});
