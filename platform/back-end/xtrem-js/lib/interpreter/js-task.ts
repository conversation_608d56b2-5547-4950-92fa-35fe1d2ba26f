import { JsObject } from './js-object';
import { JsScope } from './js-scope';
import { JsValue } from './types';

/**
 * Class for a task.
 * @param functionRef Function to call.
 * @param argsArray Array of arguments.
 * @param scope Scope for this task.
 * @param node AST node to execute.
 * @param interval Number of ms this task repeats.  -1 for no repeats.
 */
export class JsTask {
    static pidSequence = 0;

    // eslint-disable-next-line no-plusplus
    readonly pid: number = ++JsTask.pidSequence;

    time = 0;

    /**
     * Task constructor
     * @param functionRef Function to call.
     * @param argsArray Array of arguments.
     * @param scope Scope for this task.
     * @param node AST node to execute.
     * @param interval Number of ms this task repeats.  -1 for no repeats.
     */
    constructor(
        public functionRef: JsObject | undefined,
        public argsArray: JsValue[],
        public scope: JsScope,
        public node: any,
        public interval: number,
    ) {
        this.functionRef = functionRef;
        this.argsArray = argsArray;
        this.scope = scope;
        this.node = node;

        this.interval = interval;
    }
}
