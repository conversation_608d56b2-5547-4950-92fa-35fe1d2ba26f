import { Interpreter } from './interpreter';
import { Dict } from './types';

const debug = require('debug');

/**
 * For cycle detection in array to string and error conversion;
 * see spec bug github.com/tc39/ecma262/issues/289
 * Since this is for atomic actions only, it can be a class property.
 */
const toStringCycles: JsObject[] = [];

/**
 * Class for an object.
 */
export class JsObject {
    #proto: JsObject | null = null;

    #class = 'Object';

    #getter = Object.create(null);

    #setter = Object.create(null);

    #properties: Dict<any> = Object.create(null);

    data?: any;

    preventExtensions?: any;

    get proto() {
        return this.#proto;
    }

    set proto(proto) {
        this.#proto = proto;
        // eslint-disable-next-line no-prototype-builtins
        if (this.hasOwnProperty('_proto')) {
            (this as any)._proto = proto;
        }
    }

    get class() {
        return this.#class;
    }

    set class(clazz) {
        this.#class = clazz;
        // eslint-disable-next-line no-prototype-builtins
        if (this.hasOwnProperty('_class')) {
            (this as any)._class = this.#class;
        }
    }

    get getter() {
        return this.#getter;
    }

    get setter() {
        return this.#setter;
    }

    get properties() {
        return this.#properties;
    }

    /**
     * constructor for an object.
     * @param proto Prototype object or null.
     */
    constructor(proto: JsObject | null) {
        this.#proto = proto;
        if (debug.enabled('xtrem:js')) {
            (this as any)._getter = this.#getter;
            (this as any)._setter = this.#setter;
            (this as any)._properties = this.#properties;
            (this as any)._proto = this.#proto;
            (this as any)._class = this.#class;
        }
    }

    /**
     * Convert this object into a string.
     * @returns {string} String value.
     * @override
     */
    toString() {
        if (!Interpreter._currentInterpreter) {
            // Called from outside an interpreter.
            return '[object JsObject]';
        }
        if (!(this instanceof JsObject)) {
            // Primitive value.
            return String(this);
        }

        let cycles;
        if (this.class === 'Array') {
            // Array contents must not have cycles.
            cycles = toStringCycles;
            cycles.push(this);
            let strs;
            try {
                strs = [];
                // Truncate very long strings.  This is not part of the spec,
                // but it prevents hanging the interpreter for gigantic arrays.
                let maxLength = this.properties.length;
                let truncated = false;
                if (maxLength > 1024) {
                    maxLength = 1000;
                    truncated = true;
                }
                // eslint-disable-next-line no-plusplus
                for (let i = 0; i < maxLength; i++) {
                    const value = this.properties[i];
                    strs[i] = value instanceof JsObject && cycles.indexOf(value) !== -1 ? '...' : value;
                }
                if (truncated) {
                    strs.push('...');
                }
            } finally {
                cycles.pop();
            }
            return strs.join(',');
        }

        if (this.class === 'Error') {
            // Error name and message properties must not have cycles.
            cycles = toStringCycles;
            if (cycles.indexOf(this) !== -1) {
                return '[object Error]';
            }
            let name;
            let message;
            // Bug: Does not support getters and setters for name or message.
            let obj: JsObject | null = this;
            do {
                if ('name' in obj.properties) {
                    name = obj.properties.name;
                    break;
                }
                // eslint-disable-next-line no-cond-assign
            } while ((obj = obj.proto));
            obj = this;
            do {
                if ('message' in obj.properties) {
                    message = obj.properties.message;
                    break;
                }
                // eslint-disable-next-line no-cond-assign
            } while ((obj = obj.proto));
            cycles.push(this);
            try {
                name = name && String(name);
                message = message && String(message);
            } finally {
                cycles.pop();
            }
            return message ? `${name}: ${message}` : String(name);
        }

        if (this.data !== null) {
            // RegExp, Date, and boxed primitives.
            return String(this.data);
        }

        return `[object ${this.class}]`;
    }

    /**
     * Return the object's value.
     * @returns {Interpreter.Value} Value.
     * @override
     */
    valueOf() {
        if (!Interpreter._currentInterpreter) {
            // Called from outside an interpreter.
            return this;
        }
        if (this.data === undefined || this.data === null || this.data instanceof RegExp) {
            return this; // An Object, RegExp, or primitive.
        }
        if (this.data instanceof Date) {
            return this.data.valueOf(); // Milliseconds.
        }
        return this.data; // Boxed primitive.
    }
}
