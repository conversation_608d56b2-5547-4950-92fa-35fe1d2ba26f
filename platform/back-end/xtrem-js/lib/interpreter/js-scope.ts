import { JsObject } from './js-object';

/**
 * Class for a scope.
 */
export class JsScope {
    /**
     * constructor of JsScope
     * @param parentScope Parent scope.
     * @param strict True if "use strict".
     * @param object Object containing scope's variables.
     * @struct
     */
    constructor(
        public readonly parentScope: JsScope | null,
        public readonly strict: boolean,
        public readonly object: JsObject,
    ) {
        this.parentScope = parentScope;
        this.strict = strict;
        this.object = object;
    }
}
