import { JsScope } from './js-scope';

/**
 * Class for a state.
 */
export class JsState {
    done = false;

    value?: any;

    /**
     * constructor of JsState
     * @param node AST node for the state.
     * @param scope Scope object for the state.
     */
    constructor(public node: any, public scope: JsScope, public $?: any) {
        this.node = node;
        this.scope = scope;
        this.$ = $ ?? Object.create(null);
    }
}
