import { decorators, NodeExtension, Reference } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as fixtures from '..';

@decorators.nodeExtension<SiteExtension>({
    extends: () => xtremSystem.nodes.Site,
    async saveBegin() {
        if (!(await this.syncSite)) {
            await this.$.set({ syncSite: {} });
        }
    },
})
export class SiteExtension extends NodeExtension<xtremSystem.nodes.Site> {
    skipCallApi: boolean;

    getSyncStateReference() {
        return this.syncSite;
    }

    @decorators.referenceProperty<SiteExtension, 'syncSite'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'parent',
        node: () => fixtures.nodes.SiteSync,
        isNullable: true,
        excludedFromPayload: true,
    })
    readonly syncSite: Reference<fixtures.nodes.SiteSync | null>;
}
declare module '@sage/xtrem-system/lib/nodes/site' {
    export interface Site extends SiteExtension {}
}
