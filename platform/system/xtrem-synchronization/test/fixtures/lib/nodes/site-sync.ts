import type { Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremSynchronization from '../../../../lib/index';

@decorators.subNode<SiteSync>({
    extends: () => xtremSynchronization.nodes.SynchronizationState,
    canCreate: true,
    isPublished: true,
    isVitalReferenceChild: true,
    async createEnd() {
        await this.$.set({ integration: '#test' });
    },
})
export class SiteSync extends xtremSynchronization.nodes.SynchronizationState {
    override getSyncStateReference() {
        return this.parent;
    }

    @decorators.referenceProperty<SiteSync, 'parent'>({
        node: () => xtremSystem.nodes.Site,
        isPublished: true,
        isStored: true,
        isVitalParent: true,
    })
    readonly parent: Reference<xtremSystem.nodes.Site>;
}
