import { Test } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';

describe('Site node', () => {
    before(() => {});

    it('Reading Site node', () =>
        Test.withContext(async context => {
            const mySite = await context.read(xtremSystem.nodes.Site, { id: 'US001' }, { forUpdate: true });
            await mySite.$.save();

            assert.instanceOf(mySite, xtremSystem.nodes.Site);

            assert.equal(await mySite.name, 'Chem. Atlanta');

            const syncState = await mySite.getSyncStateReference();

            assert.equal(await syncState?.naturalKey, 'US001');
        }));
});
