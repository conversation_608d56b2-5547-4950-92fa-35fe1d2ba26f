import { asyncArray, Context, datetime, NodeQueryFilter } from '@sage/xtrem-core';
import * as xtremMetadata from '@sage/xtrem-metadata';
import { NotificationProgress } from '@sage/xtrem-shared';
import * as xtremSynchronization from '..';

export abstract class SynchronizationManager {
    progress: Partial<NotificationProgress>;

    private filter: NodeQueryFilter<
        xtremSynchronization.nodes.SynchronizationState,
        xtremSynchronization.nodes.SynchronizationState
    >;

    protected constructor(
        readonly context: Context,
        readonly parameters: {
            nodeFactory: xtremMetadata.nodes.MetaNodeFactory;
            integration: xtremSynchronization.nodes.ThirdPartyApplication;
        },
    ) {
        this.progress = {
            detail: 'SynchronizationManager',
            errorCount: 0,
            phase: 'build',
            successCount: 0,
            totalCount: 0,
        };
    }

    async getFilter(): Promise<
        NodeQueryFilter<
            xtremSynchronization.nodes.SynchronizationState,
            xtremSynchronization.nodes.SynchronizationState
        >
    > {
        const defaultFilter: NodeQueryFilter<
            xtremSynchronization.nodes.SynchronizationState,
            xtremSynchronization.nodes.SynchronizationState
        > = {
            integration: `#${await this.parameters.integration.$.getNaturalKeyValue()}`,
            node: `#${await this.parameters.nodeFactory.$.getNaturalKeyValue()}`,
        };

        if (this.filter) {
            return {
                _and: [this.filter, defaultFilter],
            };
        }
        return defaultFilter;
    }

    setFilter(
        filter?: NodeQueryFilter<
            xtremSynchronization.nodes.SynchronizationState,
            xtremSynchronization.nodes.SynchronizationState
        >,
    ): void {
        if (filter) {
            this.filter = filter;
        }
    }

    async getAllSynchronizationStateId(): Promise<Array<number>> {
        return (
            await this.context.select(
                xtremSynchronization.nodes.SynchronizationState,
                { _id: true },
                { filter: await this.getFilter() },
            )
        ).map(inst => inst._id);
    }

    async start(): Promise<void> {
        const sysIds = await this.getAllSynchronizationStateId();
        this.progress.totalCount = sysIds.length;
        await this.context.batch.updateProgress({ ...this.progress, phase: 'start', detail: 'started' });
        if (!this.progress.successCount) {
            this.progress.successCount = 0;
        }

        await asyncArray(sysIds).forEachParallel(2, async _id => {
            const payload = await this.synchronize(_id);

            this.progress.successCount = (this.progress.successCount || 0) + (payload.state === 'success' ? 1 : 0);
            this.progress.errorCount = (this.progress.errorCount || 0) + (payload.state === 'error' ? 1 : 0);

            if (((this.progress.successCount || 0) + (this.progress.errorCount || 0)) % 10) {
                await this.context.batch.updateProgress(this.progress);
            }
        });

        await this.context.batch.logMessage(
            'info',
            this.context.localize(
                '@sage/xtrem-synchronization/class__synchronization_manager/mass-creation-end',
                'Mass creation of {{node}} finished at {{dateTime}}.',
                { node: await this.parameters.nodeFactory.title, dateTime: datetime.now().toString() },
            ),
        );
        await this.context.batch.updateProgress(this.progress);
    }

    async synchronize(
        synchronizationStateSysId: number,
    ): Promise<xtremSynchronization.functions.SynchronizationReturn> {
        const synchronizationState = await this.context.read(xtremSynchronization.nodes.SynchronizationState, {
            _id: synchronizationStateSysId,
        });
        await this.context.batch.logMessage(
            'info',
            this.context.localize(
                '@sage/xtrem-synchronization/class__synchronization_manager/synchronizing',
                'Synchronizing {{key}}.',
                { key: await synchronizationState.sysId },
            ),
        );
        return {
            lastMessage: '',
            state: 'not',
            url: '',
            version: 1,
        };
    }
}
