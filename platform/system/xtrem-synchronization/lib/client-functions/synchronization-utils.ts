import { NodeDetails } from '@sage/xtrem-shared';

// TODO: this comes from workflow POC - should be solidified and moved to xtrem-system
export function convertParameterTypeToFieldType(parameterType: NodeDetails['type'], kind: NodeDetails['kind']): string {
    if (kind === 'ENUM') {
        return 'enum';
    }

    switch (parameterType) {
        case 'Boolean':
            return 'boolean';
        case 'String':
            return 'string';
        case 'Float':
            return 'float';
        case 'Integer':
            return 'integer';
        case 'Decimal':
            return 'Decimal';
        default:
            return 'string';
    }
}
