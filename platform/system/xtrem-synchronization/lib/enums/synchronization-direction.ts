import { EnumDataType } from '@sage/xtrem-core';

export enum SynchronizationDirectionEnum {
    /** No integrated */
    not,
    /**      * From thirdParty app to xtreem     */
    inbound,
    /**     * From Xtreem To ThirdParty     */
    outbound,
    /**      * Both Xtreemn &  thirdparty app     */
    both,
}

export type SynchronizationDirection = keyof typeof SynchronizationDirectionEnum;

export const SynchronizationDirectionDataType = new EnumDataType<SynchronizationDirection>({
    enum: SynchronizationDirectionEnum,
    filename: __filename,
});
