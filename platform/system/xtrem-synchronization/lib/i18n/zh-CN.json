{"@sage/xtrem-synchronization/activity__synchronization_state__name": "同步状态", "@sage/xtrem-synchronization/class__synchronization_manager/mass-creation-end": "{{node}}的批量创建于{{dateTime}}完成。", "@sage/xtrem-synchronization/class__synchronization_manager/synchronizing": "正在同步{{key}}。", "@sage/xtrem-synchronization/data_types__synchronization_direction_enum__name": "同步方向枚举", "@sage/xtrem-synchronization/enums__synchronization_direction__both": "两者都", "@sage/xtrem-synchronization/enums__synchronization_direction__inbound": "入库", "@sage/xtrem-synchronization/enums__synchronization_direction__not": "不", "@sage/xtrem-synchronization/enums__synchronization_direction__outbound": "出库", "@sage/xtrem-synchronization/menu_item__synchronization": "同步", "@sage/xtrem-synchronization/nodes__base_mapping__node_name": "基础匹配", "@sage/xtrem-synchronization/nodes__base_mapping__property__application": "应用程序", "@sage/xtrem-synchronization/nodes__base_mapping__property__canCreate": "可创建", "@sage/xtrem-synchronization/nodes__base_mapping__property__canDelete": "可删除", "@sage/xtrem-synchronization/nodes__base_mapping__property__canUpdate": "可更新", "@sage/xtrem-synchronization/nodes__base_mapping__property__isActive": "是激活的", "@sage/xtrem-synchronization/nodes__base_mapping__property__mappedPropertyNames": "匹配的属性名称", "@sage/xtrem-synchronization/nodes__base_mapping__property__nodeFactory": "节点工厂", "@sage/xtrem-synchronization/nodes__base_mapping__property__notificationId": "通知ID", "@sage/xtrem-synchronization/nodes__base_mapping__property__synchronizationDirection": "同步方向", "@sage/xtrem-synchronization/nodes__base_mapping__property__thirdPartyObjectName": "第三方对象名称", "@sage/xtrem-synchronization/nodes__synchronization_state__bulkMutation__forceSynchronize": "强制同步", "@sage/xtrem-synchronization/nodes__synchronization_state__bulkMutation__forceSynchronize__failed": "强制同步失败。", "@sage/xtrem-synchronization/nodes__synchronization_state__bulkMutation__resetThirdPartyId": "重置第三方ID", "@sage/xtrem-synchronization/nodes__synchronization_state__bulkMutation__resetThirdPartyId__failed": "重置第三方ID失败。", "@sage/xtrem-synchronization/nodes__synchronization_state__bulkMutation__synchronize": "同步", "@sage/xtrem-synchronization/nodes__synchronization_state__bulkMutation__synchronize__failed": "同步失败。", "@sage/xtrem-synchronization/nodes__synchronization_state__bulkMutation__updateSysId": "更新系统ID", "@sage/xtrem-synchronization/nodes__synchronization_state__bulkMutation__updateSysId__failed": "更新系统ID失败。", "@sage/xtrem-synchronization/nodes__synchronization_state__node_name": "同步状态", "@sage/xtrem-synchronization/nodes__synchronization_state__property__computedNaturalKey": "计算的自然键", "@sage/xtrem-synchronization/nodes__synchronization_state__property__creationStamp": "创建标记", "@sage/xtrem-synchronization/nodes__synchronization_state__property__difference": "差异", "@sage/xtrem-synchronization/nodes__synchronization_state__property__integration": "集成", "@sage/xtrem-synchronization/nodes__synchronization_state__property__lastMessage": "最后的消息", "@sage/xtrem-synchronization/nodes__synchronization_state__property__mapping": "匹配", "@sage/xtrem-synchronization/nodes__synchronization_state__property__naturalKey": "自然键", "@sage/xtrem-synchronization/nodes__synchronization_state__property__node": "节点", "@sage/xtrem-synchronization/nodes__synchronization_state__property__notificationTrackingIds": "通知追踪ID", "@sage/xtrem-synchronization/nodes__synchronization_state__property__pageLink": "页面链接", "@sage/xtrem-synchronization/nodes__synchronization_state__property__secondaryLink": "次要链接", "@sage/xtrem-synchronization/nodes__synchronization_state__property__secondaryPageLink": "次要页面链接", "@sage/xtrem-synchronization/nodes__synchronization_state__property__secondaryText": "次要文本", "@sage/xtrem-synchronization/nodes__synchronization_state__property__state": "状态", "@sage/xtrem-synchronization/nodes__synchronization_state__property__sysId": "系统ID", "@sage/xtrem-synchronization/nodes__synchronization_state__property__sysIdLink": "系统ID链接", "@sage/xtrem-synchronization/nodes__synchronization_state__property__updateStamp": "更新标记", "@sage/xtrem-synchronization/nodes__synchronization_state__property__url": "URL", "@sage/xtrem-synchronization/nodes__synchronization_state__property__version": "版本", "@sage/xtrem-synchronization/nodes__sys_synchronization_client__node_name": "系统同步客户", "@sage/xtrem-synchronization/nodes__third_party_application__asyncMutation__asyncExport": "导出", "@sage/xtrem-synchronization/nodes__third_party_application__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-synchronization/nodes__third_party_application__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-synchronization/nodes__third_party_application__node_name": "第三方应用", "@sage/xtrem-synchronization/nodes__third_party_application__property__id": "ID", "@sage/xtrem-synchronization/nodes__third_party_application__property__isActive": "是激活的", "@sage/xtrem-synchronization/nodes__third_party_application__property__name": "名称", "@sage/xtrem-synchronization/not-implemented": "没有对{{integrationName}}实施同步。{{isForce}}", "@sage/xtrem-synchronization/package__name": "同步", "@sage/xtrem-synchronization/pages__integration____navigationPanel__bulkActions__title": "同步", "@sage/xtrem-synchronization/pages__integration____navigationPanel__bulkActions__title__2": "强制同步", "@sage/xtrem-synchronization/pages__integration____navigationPanel__dropdownActions__title": "重置Sage Intacct ID", "@sage/xtrem-synchronization/pages__integration____navigationPanel__dropdownActions__title__2": "通知历史", "@sage/xtrem-synchronization/pages__integration____navigationPanel__dropdownActions__title__3": "最后的消息", "@sage/xtrem-synchronization/pages__integration____navigationPanel__dropdownActions__title__4": "最后的消息", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line_4__title": "状态", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line_5__title": "URL", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line10__title": "差异", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line11__title": "链接", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line12__title": "链接", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line13__title": "第三方应用", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line2__title": "存储键", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line3__title": "密钥", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line4__title": "状态", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line5__title": "URL", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line6__title": "最后的消息", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line7__title": "已创建", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line8__title": "已更新", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line9__title": "版本", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__secondaryLink__title": "次要的", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__title__title": "对象", "@sage/xtrem-synchronization/pages__integration____navigationPanel__optionsMenu__title": "全部", "@sage/xtrem-synchronization/pages__integration____navigationPanel__optionsMenu__title__2": "主数据", "@sage/xtrem-synchronization/pages__integration____navigationPanel__optionsMenu__title__3": "财务", "@sage/xtrem-synchronization/pages__integration____objectTypePlural": "集成", "@sage/xtrem-synchronization/pages__integration____objectTypeSingular": "集成", "@sage/xtrem-synchronization/pages__integration____title": "集成", "@sage/xtrem-synchronization/pages__integration__info____title": "信息", "@sage/xtrem-synchronization/pages__integration__lastMessage____title": "最后的消息", "@sage/xtrem-synchronization/pages__integration__moreInfos____title": "更多信息", "@sage/xtrem-synchronization/pages__integration__node____columns__title__title": "标题", "@sage/xtrem-synchronization/pages__integration__node____title": "最后的消息", "@sage/xtrem-synchronization/permission__force_synchronize__name": "强制同步", "@sage/xtrem-synchronization/permission__read__name": "读取", "@sage/xtrem-synchronization/permission__synchronize__name": "同步", "@sage/xtrem-synchronization/synchronize-finish": "批量同步完成。", "@sage/xtrem-synchronization/synchronize-finish-status": "{{success}}元素同步成功。{{error}}元素出现错误。", "@sage/xtrem-synchronization/synchronize-history": "历史记录", "@sage/xtrem-synchronization/synchronize-reset-third-party-id-not-allowed": "在此状态下不允许重置第三方ID"}