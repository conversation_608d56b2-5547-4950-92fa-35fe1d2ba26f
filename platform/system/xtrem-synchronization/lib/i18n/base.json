{"@sage/xtrem-synchronization/activity__synchronization_state__name": "Synchronization state", "@sage/xtrem-synchronization/class__synchronization_manager/mass-creation-end": "Mass creation of {{node}} finished at {{dateTime}}.", "@sage/xtrem-synchronization/class__synchronization_manager/synchronizing": "Synchronizing {{key}}.", "@sage/xtrem-synchronization/data_types__synchronization_direction_enum__name": "Synchronization direction enum", "@sage/xtrem-synchronization/enums__synchronization_direction__both": "Both", "@sage/xtrem-synchronization/enums__synchronization_direction__inbound": "Inbound", "@sage/xtrem-synchronization/enums__synchronization_direction__not": "Not", "@sage/xtrem-synchronization/enums__synchronization_direction__outbound": "Outbound", "@sage/xtrem-synchronization/menu_item__synchronization": "Synchronization", "@sage/xtrem-synchronization/nodes__base_mapping__node_name": "Base mapping", "@sage/xtrem-synchronization/nodes__base_mapping__property__application": "Application", "@sage/xtrem-synchronization/nodes__base_mapping__property__canCreate": "Can create", "@sage/xtrem-synchronization/nodes__base_mapping__property__canDelete": "Can delete", "@sage/xtrem-synchronization/nodes__base_mapping__property__canUpdate": "Can update", "@sage/xtrem-synchronization/nodes__base_mapping__property__isActive": "Is active", "@sage/xtrem-synchronization/nodes__base_mapping__property__mappedPropertyNames": "Mapped property names", "@sage/xtrem-synchronization/nodes__base_mapping__property__nodeFactory": "Node factory", "@sage/xtrem-synchronization/nodes__base_mapping__property__notificationId": "Notification id", "@sage/xtrem-synchronization/nodes__base_mapping__property__synchronizationDirection": "Synchronization direction", "@sage/xtrem-synchronization/nodes__base_mapping__property__thirdPartyObjectName": "Third party object name", "@sage/xtrem-synchronization/nodes__synchronization_state__bulkMutation__forceSynchronize": "Force synchronize", "@sage/xtrem-synchronization/nodes__synchronization_state__bulkMutation__forceSynchronize__failed": "Force synchronize failed.", "@sage/xtrem-synchronization/nodes__synchronization_state__bulkMutation__resetThirdPartyId": "Reset third party id", "@sage/xtrem-synchronization/nodes__synchronization_state__bulkMutation__resetThirdPartyId__failed": "Reset third party id failed.", "@sage/xtrem-synchronization/nodes__synchronization_state__bulkMutation__synchronize": "Synchronize", "@sage/xtrem-synchronization/nodes__synchronization_state__bulkMutation__synchronize__failed": "Synchronize failed.", "@sage/xtrem-synchronization/nodes__synchronization_state__bulkMutation__updateSysId": "Update sys id", "@sage/xtrem-synchronization/nodes__synchronization_state__bulkMutation__updateSysId__failed": "Update sys id failed.", "@sage/xtrem-synchronization/nodes__synchronization_state__node_name": "Synchronization state", "@sage/xtrem-synchronization/nodes__synchronization_state__property__computedNaturalKey": "Computed natural key", "@sage/xtrem-synchronization/nodes__synchronization_state__property__creationStamp": "Creation stamp", "@sage/xtrem-synchronization/nodes__synchronization_state__property__difference": "Difference", "@sage/xtrem-synchronization/nodes__synchronization_state__property__integration": "Integration", "@sage/xtrem-synchronization/nodes__synchronization_state__property__lastMessage": "Last message", "@sage/xtrem-synchronization/nodes__synchronization_state__property__mapping": "Mapping", "@sage/xtrem-synchronization/nodes__synchronization_state__property__naturalKey": "Natural key", "@sage/xtrem-synchronization/nodes__synchronization_state__property__node": "Node", "@sage/xtrem-synchronization/nodes__synchronization_state__property__notificationTrackingIds": "Notification tracking ids", "@sage/xtrem-synchronization/nodes__synchronization_state__property__pageLink": "Page link", "@sage/xtrem-synchronization/nodes__synchronization_state__property__secondaryPageLink": "Secondary page link", "@sage/xtrem-synchronization/nodes__synchronization_state__property__state": "State", "@sage/xtrem-synchronization/nodes__synchronization_state__property__sysId": "Sys id", "@sage/xtrem-synchronization/nodes__synchronization_state__property__sysIdLink": "Sys id link", "@sage/xtrem-synchronization/nodes__synchronization_state__property__updateStamp": "Update stamp", "@sage/xtrem-synchronization/nodes__synchronization_state__property__url": "Url", "@sage/xtrem-synchronization/nodes__synchronization_state__property__version": "Version", "@sage/xtrem-synchronization/nodes__third_party_application__asyncMutation__asyncExport": "Export", "@sage/xtrem-synchronization/nodes__third_party_application__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-synchronization/nodes__third_party_application__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-synchronization/nodes__third_party_application__node_name": "Third party application", "@sage/xtrem-synchronization/nodes__third_party_application__property__id": "Id", "@sage/xtrem-synchronization/nodes__third_party_application__property__isActive": "Is active", "@sage/xtrem-synchronization/nodes__third_party_application__property__name": "Name", "@sage/xtrem-synchronization/not-implemented": "Synchronize isn't implemented for {{integrationName}}. {{isForce}}", "@sage/xtrem-synchronization/package__name": "Sage xtrem synchronization", "@sage/xtrem-synchronization/pages__integration____navigationPanel__bulkActions__title": "Sychronize", "@sage/xtrem-synchronization/pages__integration____navigationPanel__bulkActions__title__2": "Force sychronize", "@sage/xtrem-synchronization/pages__integration____navigationPanel__dropdownActions__title": "Reset Sage Intacct ID", "@sage/xtrem-synchronization/pages__integration____navigationPanel__dropdownActions__title__2": "Notification history", "@sage/xtrem-synchronization/pages__integration____navigationPanel__dropdownActions__title__3": "Last message", "@sage/xtrem-synchronization/pages__integration____navigationPanel__dropdownActions__title__4": "Last message", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line_4__title": "Status", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line_5__title": "URL", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line10__title": "Differences", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line13__title": "Third party app", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line2__title": "Stored Key", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line3__title": "Key", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line6__title": "Last message", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line7__title": "Created", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line8__title": "Updated", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line9__title": "Version", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__secondaryLink__title": "secondary", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__title__title": "Object", "@sage/xtrem-synchronization/pages__integration____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-synchronization/pages__integration____navigationPanel__optionsMenu__title__2": "Master data", "@sage/xtrem-synchronization/pages__integration____navigationPanel__optionsMenu__title__3": "Finance", "@sage/xtrem-synchronization/pages__integration____objectTypePlural": "Integrations", "@sage/xtrem-synchronization/pages__integration____objectTypeSingular": "Integration", "@sage/xtrem-synchronization/pages__integration____title": "Integration", "@sage/xtrem-synchronization/pages__integration__info____title": "Info", "@sage/xtrem-synchronization/pages__integration__lastMessage____title": "Last message", "@sage/xtrem-synchronization/pages__integration__moreInfos____title": "More info", "@sage/xtrem-synchronization/pages__integration__node____columns__title__title": "title", "@sage/xtrem-synchronization/pages__integration__node____title": "Last message", "@sage/xtrem-synchronization/permission__force_synchronize__name": "Force synchronize", "@sage/xtrem-synchronization/permission__read__name": "Read", "@sage/xtrem-synchronization/permission__synchronize__name": "Synchronize", "@sage/xtrem-synchronization/synchronize-finish": "Mass synchronization done.", "@sage/xtrem-synchronization/synchronize-finish-status": "{{success}} elements synchronized successfully. {{error}} elements with errors.", "@sage/xtrem-synchronization/synchronize-history": "History", "@sage/xtrem-synchronization/synchronize-reset-third-party-id-not-allowed": "Reset third party id is not allowed in this state"}