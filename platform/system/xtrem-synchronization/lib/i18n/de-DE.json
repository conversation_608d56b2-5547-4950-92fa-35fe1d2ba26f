{"@sage/xtrem-synchronization/activity__synchronization_state__name": "Status Synchronisation", "@sage/xtrem-synchronization/class__synchronization_manager/mass-creation-end": "Massenerstellung von {{node}} beendet am {{dateTime}}.", "@sage/xtrem-synchronization/class__synchronization_manager/synchronizing": "Synchronisiere {{key}}.", "@sage/xtrem-synchronization/data_types__synchronization_direction_enum__name": "Enum Richtung Synchronisierung", "@sage/xtrem-synchronization/enums__synchronization_direction__both": "<PERSON><PERSON>", "@sage/xtrem-synchronization/enums__synchronization_direction__inbound": "Ein<PERSON><PERSON>", "@sage/xtrem-synchronization/enums__synchronization_direction__not": "<PERSON><PERSON>", "@sage/xtrem-synchronization/enums__synchronization_direction__outbound": "Ausgehend", "@sage/xtrem-synchronization/menu_item__synchronization": "Synchronisierung", "@sage/xtrem-synchronization/nodes__base_mapping__node_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-synchronization/nodes__base_mapping__property__application": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-synchronization/nodes__base_mapping__property__canCreate": "<PERSON>nn erstellen", "@sage/xtrem-synchronization/nodes__base_mapping__property__canDelete": "<PERSON><PERSON>", "@sage/xtrem-synchronization/nodes__base_mapping__property__canUpdate": "Kann aktualisieren", "@sage/xtrem-synchronization/nodes__base_mapping__property__isActive": "Ist aktiv", "@sage/xtrem-synchronization/nodes__base_mapping__property__mappedPropertyNames": "Zugeordnete Eigenschaftennamen", "@sage/xtrem-synchronization/nodes__base_mapping__property__nodeFactory": "Standard Node", "@sage/xtrem-synchronization/nodes__base_mapping__property__notificationId": "Benachrichtigungs-ID", "@sage/xtrem-synchronization/nodes__base_mapping__property__synchronizationDirection": "Richtung Synchronisierung", "@sage/xtrem-synchronization/nodes__base_mapping__property__thirdPartyObjectName": "Objektname Drittanbieter", "@sage/xtrem-synchronization/nodes__synchronization_state__bulkMutation__forceSynchronize": "Synchronisierung erzwingen", "@sage/xtrem-synchronization/nodes__synchronization_state__bulkMutation__forceSynchronize__failed": "Synchronisierung erzwingen fehlgeschlagen.", "@sage/xtrem-synchronization/nodes__synchronization_state__bulkMutation__resetThirdPartyId": "Drittanbieter-<PERSON>", "@sage/xtrem-synchronization/nodes__synchronization_state__bulkMutation__resetThirdPartyId__failed": "Drittanbieter-ID zurücksetzen fehlgeschlagen.", "@sage/xtrem-synchronization/nodes__synchronization_state__bulkMutation__synchronize": "Synchroni<PERSON><PERSON>", "@sage/xtrem-synchronization/nodes__synchronization_state__bulkMutation__synchronize__failed": "Synchronisieren fehlgeschlagen.", "@sage/xtrem-synchronization/nodes__synchronization_state__bulkMutation__updateSysId": "Aktualisierung System-ID", "@sage/xtrem-synchronization/nodes__synchronization_state__bulkMutation__updateSysId__failed": "Aktualisierung System-ID fehlgeschlagen.", "@sage/xtrem-synchronization/nodes__synchronization_state__node_name": "Status Synchronisation", "@sage/xtrem-synchronization/nodes__synchronization_state__property__computedNaturalKey": "Berechneter natürlicher Schlüssel", "@sage/xtrem-synchronization/nodes__synchronization_state__property__creationStamp": "<PERSON><PERSON><PERSON>", "@sage/xtrem-synchronization/nodes__synchronization_state__property__difference": "Unterschied", "@sage/xtrem-synchronization/nodes__synchronization_state__property__integration": "Integration", "@sage/xtrem-synchronization/nodes__synchronization_state__property__lastMessage": "Letzte Meldung", "@sage/xtrem-synchronization/nodes__synchronization_state__property__mapping": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-synchronization/nodes__synchronization_state__property__naturalKey": "Natürlicher Schlüssel", "@sage/xtrem-synchronization/nodes__synchronization_state__property__node": "Node", "@sage/xtrem-synchronization/nodes__synchronization_state__property__notificationTrackingIds": "IDs Benachrichtigungsverfolgung", "@sage/xtrem-synchronization/nodes__synchronization_state__property__pageLink": "Seitenverknüpfung", "@sage/xtrem-synchronization/nodes__synchronization_state__property__secondaryLink": "Sekundärer Link", "@sage/xtrem-synchronization/nodes__synchronization_state__property__secondaryPageLink": "Sekundärer Seitenlink", "@sage/xtrem-synchronization/nodes__synchronization_state__property__secondaryText": "Sekundärer Text", "@sage/xtrem-synchronization/nodes__synchronization_state__property__state": "Status", "@sage/xtrem-synchronization/nodes__synchronization_state__property__sysId": "System-ID", "@sage/xtrem-synchronization/nodes__synchronization_state__property__sysIdLink": "Verknüpfung System-ID", "@sage/xtrem-synchronization/nodes__synchronization_state__property__updateStamp": "Stempel Aktualisieren", "@sage/xtrem-synchronization/nodes__synchronization_state__property__url": "URL", "@sage/xtrem-synchronization/nodes__synchronization_state__property__version": "Version", "@sage/xtrem-synchronization/nodes__sys_synchronization_client__node_name": "Client Systemsynchronisation", "@sage/xtrem-synchronization/nodes__third_party_application__asyncMutation__asyncExport": "Export", "@sage/xtrem-synchronization/nodes__third_party_application__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-synchronization/nodes__third_party_application__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-synchronization/nodes__third_party_application__node_name": "Drittan<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-synchronization/nodes__third_party_application__property__id": "ID", "@sage/xtrem-synchronization/nodes__third_party_application__property__isActive": "Ist aktiv", "@sage/xtrem-synchronization/nodes__third_party_application__property__name": "Name", "@sage/xtrem-synchronization/not-implemented": "Synchronisierung für {{integrationName}} nicht implementiert. {{isForce}}", "@sage/xtrem-synchronization/package__name": "Synchronisierung", "@sage/xtrem-synchronization/pages__integration____navigationPanel__bulkActions__title": "Synchroni<PERSON><PERSON>", "@sage/xtrem-synchronization/pages__integration____navigationPanel__bulkActions__title__2": "Synchronisierung erzwingen", "@sage/xtrem-synchronization/pages__integration____navigationPanel__dropdownActions__title": "ID Sage Intacct zurücksetzen", "@sage/xtrem-synchronization/pages__integration____navigationPanel__dropdownActions__title__2": "Benachrichtigungsverlauf", "@sage/xtrem-synchronization/pages__integration____navigationPanel__dropdownActions__title__3": "Letzte Meldung", "@sage/xtrem-synchronization/pages__integration____navigationPanel__dropdownActions__title__4": "Letzte Meldung", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line_4__title": "Status", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line_5__title": "URL", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line10__title": "Unterschiede", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line11__title": "Verknüpfung", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line12__title": "Verknüpfung", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line13__title": "Drittan<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line2__title": "Gespeicherter Schlüssel", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line3__title": "Schlüssel", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line4__title": "Status", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line5__title": "URL", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line6__title": "Letzte Meldung", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line7__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line8__title": "<PERSON>ktual<PERSON><PERSON>", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line9__title": "Version", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__secondaryLink__title": "Sekundär", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__title__title": "Objekt", "@sage/xtrem-synchronization/pages__integration____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-synchronization/pages__integration____navigationPanel__optionsMenu__title__2": "Stammdaten", "@sage/xtrem-synchronization/pages__integration____navigationPanel__optionsMenu__title__3": "Fin<PERSON>zen", "@sage/xtrem-synchronization/pages__integration____objectTypePlural": "Integrationen", "@sage/xtrem-synchronization/pages__integration____objectTypeSingular": "Integration", "@sage/xtrem-synchronization/pages__integration____title": "Integration", "@sage/xtrem-synchronization/pages__integration__info____title": "Info", "@sage/xtrem-synchronization/pages__integration__lastMessage____title": "Letzte Meldung", "@sage/xtrem-synchronization/pages__integration__moreInfos____title": "Weitere Informationen", "@sage/xtrem-synchronization/pages__integration__node____columns__title__title": "Titel", "@sage/xtrem-synchronization/pages__integration__node____title": "Letzte Meldung", "@sage/xtrem-synchronization/permission__force_synchronize__name": "Synchronisierung erzwingen", "@sage/xtrem-synchronization/permission__read__name": "<PERSON><PERSON>", "@sage/xtrem-synchronization/permission__synchronize__name": "Synchroni<PERSON><PERSON>", "@sage/xtrem-synchronization/synchronize-finish": "Massensynchronisierung durchgeführt.", "@sage/xtrem-synchronization/synchronize-finish-status": "{{success}} Elemente erfolgreich synchronisiert. {{error}} Elemente mit Fehlern.", "@sage/xtrem-synchronization/synchronize-history": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-synchronization/synchronize-reset-third-party-id-not-allowed": "Drittanbieter-ID zurücksetzen in diesem Status nicht zulässig"}