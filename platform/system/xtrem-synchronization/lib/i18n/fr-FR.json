{"@sage/xtrem-synchronization/activity__synchronization_state__name": "État de synchronisation", "@sage/xtrem-synchronization/class__synchronization_manager/mass-creation-end": "La création en masse de {{node}} s'est terminé à {{dateTime}}.", "@sage/xtrem-synchronization/class__synchronization_manager/synchronizing": "{{key}} de synchronisation", "@sage/xtrem-synchronization/data_types__synchronization_direction_enum__name": "Enum du sens de synchronisation", "@sage/xtrem-synchronization/enums__synchronization_direction__both": "Les deux", "@sage/xtrem-synchronization/enums__synchronization_direction__inbound": "Ré<PERSON>", "@sage/xtrem-synchronization/enums__synchronization_direction__not": "Non synchronisé", "@sage/xtrem-synchronization/enums__synchronization_direction__outbound": "Émission", "@sage/xtrem-synchronization/menu_item__synchronization": "Synchronisation", "@sage/xtrem-synchronization/nodes__base_mapping__node_name": "Mapping de base", "@sage/xtrem-synchronization/nodes__base_mapping__property__application": "Application", "@sage/xtrem-synchronization/nodes__base_mapping__property__canCreate": "Création", "@sage/xtrem-synchronization/nodes__base_mapping__property__canDelete": "Suppression", "@sage/xtrem-synchronization/nodes__base_mapping__property__canUpdate": "Mise à jour", "@sage/xtrem-synchronization/nodes__base_mapping__property__isActive": "Active", "@sage/xtrem-synchronization/nodes__base_mapping__property__mappedPropertyNames": "Noms de propriétés mappées", "@sage/xtrem-synchronization/nodes__base_mapping__property__nodeFactory": "Livré node", "@sage/xtrem-synchronization/nodes__base_mapping__property__notificationId": "Code de notification", "@sage/xtrem-synchronization/nodes__base_mapping__property__synchronizationDirection": "Sens de synchronisation", "@sage/xtrem-synchronization/nodes__base_mapping__property__thirdPartyObjectName": "Nom d'objet tiers", "@sage/xtrem-synchronization/nodes__synchronization_state__bulkMutation__forceSynchronize": "Forcer la synchronisation", "@sage/xtrem-synchronization/nodes__synchronization_state__bulkMutation__forceSynchronize__failed": "La synchronisation forcée a échoué.", "@sage/xtrem-synchronization/nodes__synchronization_state__bulkMutation__resetThirdPartyId": "Réinitialiser l'ID tiers", "@sage/xtrem-synchronization/nodes__synchronization_state__bulkMutation__resetThirdPartyId__failed": "Échec de la réinitialisation de l'ID tiers.", "@sage/xtrem-synchronization/nodes__synchronization_state__bulkMutation__synchronize": "Synchroniser", "@sage/xtrem-synchronization/nodes__synchronization_state__bulkMutation__synchronize__failed": "La synchronisation a échoué.", "@sage/xtrem-synchronization/nodes__synchronization_state__bulkMutation__updateSysId": "Mettre à jour code sys", "@sage/xtrem-synchronization/nodes__synchronization_state__bulkMutation__updateSysId__failed": "La mise à jour de l'ID système a échoué.", "@sage/xtrem-synchronization/nodes__synchronization_state__node_name": "État de synchronisation", "@sage/xtrem-synchronization/nodes__synchronization_state__property__computedNaturalKey": "Clé naturelle calculée", "@sage/xtrem-synchronization/nodes__synchronization_state__property__creationStamp": "Horodatage de création", "@sage/xtrem-synchronization/nodes__synchronization_state__property__difference": "<PERSON>ff<PERSON><PERSON><PERSON>", "@sage/xtrem-synchronization/nodes__synchronization_state__property__integration": "Intégration", "@sage/xtrem-synchronization/nodes__synchronization_state__property__lastMessage": "<PERSON><PERSON> message", "@sage/xtrem-synchronization/nodes__synchronization_state__property__mapping": "Mapping", "@sage/xtrem-synchronization/nodes__synchronization_state__property__naturalKey": "Clé naturelle", "@sage/xtrem-synchronization/nodes__synchronization_state__property__node": "Node", "@sage/xtrem-synchronization/nodes__synchronization_state__property__notificationTrackingIds": "Codes de suivi de notification", "@sage/xtrem-synchronization/nodes__synchronization_state__property__pageLink": "<PERSON><PERSON> <PERSON> page", "@sage/xtrem-synchronization/nodes__synchronization_state__property__secondaryLink": "Lien secondaire", "@sage/xtrem-synchronization/nodes__synchronization_state__property__secondaryPageLink": "<PERSON>n de page secondaire", "@sage/xtrem-synchronization/nodes__synchronization_state__property__secondaryText": "Texte secondaire", "@sage/xtrem-synchronization/nodes__synchronization_state__property__state": "État", "@sage/xtrem-synchronization/nodes__synchronization_state__property__sysId": "Code système", "@sage/xtrem-synchronization/nodes__synchronization_state__property__sysIdLink": "Lien code système", "@sage/xtrem-synchronization/nodes__synchronization_state__property__updateStamp": "Mettre à jour horodatage", "@sage/xtrem-synchronization/nodes__synchronization_state__property__url": "URL", "@sage/xtrem-synchronization/nodes__synchronization_state__property__version": "Version", "@sage/xtrem-synchronization/nodes__sys_synchronization_client__node_name": "Client synchronisation système", "@sage/xtrem-synchronization/nodes__third_party_application__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-synchronization/nodes__third_party_application__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-synchronization/nodes__third_party_application__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-synchronization/nodes__third_party_application__node_name": "Application tierce", "@sage/xtrem-synchronization/nodes__third_party_application__property__id": "Code", "@sage/xtrem-synchronization/nodes__third_party_application__property__isActive": "Active", "@sage/xtrem-synchronization/nodes__third_party_application__property__name": "Nom", "@sage/xtrem-synchronization/not-implemented": "La synchronisation n'est pas mise en oeuvre pour {{integrationName}}. {{isForce}}", "@sage/xtrem-synchronization/package__name": "Synchronisation", "@sage/xtrem-synchronization/pages__integration____navigationPanel__bulkActions__title": "Synchroniser", "@sage/xtrem-synchronization/pages__integration____navigationPanel__bulkActions__title__2": "Forcer la sychronisation", "@sage/xtrem-synchronization/pages__integration____navigationPanel__dropdownActions__title": "Réinitialiser l'ID Sage Intacct", "@sage/xtrem-synchronization/pages__integration____navigationPanel__dropdownActions__title__2": "Historique de notification", "@sage/xtrem-synchronization/pages__integration____navigationPanel__dropdownActions__title__3": "<PERSON><PERSON> message", "@sage/xtrem-synchronization/pages__integration____navigationPanel__dropdownActions__title__4": "<PERSON><PERSON> message", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line_4__title": "Statut", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line_5__title": "URL", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line10__title": "Différences", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line11__title": "<PERSON><PERSON>", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line12__title": "<PERSON><PERSON>", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line13__title": "Application tierce", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line2__title": "Clé stockée", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line3__title": "Clé", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line4__title": "Statut", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line5__title": "URL", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line6__title": "<PERSON><PERSON> message", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line7__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line8__title": "Mis à jour", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__line9__title": "Version", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__secondaryLink__title": "Secondaire", "@sage/xtrem-synchronization/pages__integration____navigationPanel__listItem__title__title": "Objet", "@sage/xtrem-synchronization/pages__integration____navigationPanel__optionsMenu__title": "Tous", "@sage/xtrem-synchronization/pages__integration____navigationPanel__optionsMenu__title__2": "Données de base", "@sage/xtrem-synchronization/pages__integration____navigationPanel__optionsMenu__title__3": "Finance", "@sage/xtrem-synchronization/pages__integration____objectTypePlural": "Intégrations", "@sage/xtrem-synchronization/pages__integration____objectTypeSingular": "Intégration", "@sage/xtrem-synchronization/pages__integration____title": "Intégration", "@sage/xtrem-synchronization/pages__integration__info____title": "Infos", "@sage/xtrem-synchronization/pages__integration__lastMessage____title": "<PERSON><PERSON> message", "@sage/xtrem-synchronization/pages__integration__moreInfos____title": "Plus d'infos", "@sage/xtrem-synchronization/pages__integration__node____columns__title__title": "Titre", "@sage/xtrem-synchronization/pages__integration__node____title": "<PERSON><PERSON> message", "@sage/xtrem-synchronization/permission__force_synchronize__name": "Forcer la synchronisation", "@sage/xtrem-synchronization/permission__read__name": "Lecture", "@sage/xtrem-synchronization/permission__synchronize__name": "Synchroniser", "@sage/xtrem-synchronization/synchronize-finish": "Synchronisation en masse réalisée", "@sage/xtrem-synchronization/synchronize-finish-status": "{{success}} éléments synchronisés. {{error}} éléments avec erreurs.", "@sage/xtrem-synchronization/synchronize-history": "Historique", "@sage/xtrem-synchronization/synchronize-reset-third-party-id-not-allowed": "La réinitialisation de l'ID tiers n'est pas autorisé avec ce statut."}