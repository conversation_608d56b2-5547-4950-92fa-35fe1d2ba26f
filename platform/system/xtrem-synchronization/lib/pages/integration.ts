import { communication } from '@sage/xtrem-communication/build/lib/menu-items/communication';
import { MetaNodeFactory } from '@sage/xtrem-metadata-api';
import { GraphApi, SynchronizationState } from '@sage/xtrem-synchronization-api';
import * as ui from '@sage/xtrem-ui';
import { IntegrationDifference, LinkToPage } from '../shared-functions';

@ui.decorators.page<Integration, SynchronizationState>({
    title: 'Integration',
    objectTypeSingular: 'Integration',
    objectTypePlural: 'Integrations',
    menuItem: communication,
    node: '@sage/xtrem-synchronization/SynchronizationState',
    mode: 'tabs',
    module: 'xtrem-synchronization',
    navigationPanel: {
        onSelect() {
            return true;
        },
        bulkActions: [
            {
                mutation: 'synchronize',
                title: 'Sychronize',
                buttonType: 'secondary',
                icon: 'sync',
                isDestructive: false,
            },
            {
                mutation: 'forceSynchronize',
                title: 'Force sychronize',
                buttonType: 'secondary',
                icon: 'sync',
                isDestructive: false,
            },
        ],
        dropdownActions: [
            {
                title: 'Reset Sage Intacct ID',
                icon: 'error_square',
                isHidden(_rowId, data) {
                    if (data.integration?.id !== 'intacct') {
                        return true;
                    }
                    return data.state !== 'error';
                },
                async onClick(_rowId, data) {
                    await this.$.graph
                        .node('@sage/xtrem-synchronization/SynchronizationState')
                        .asyncOperations.resetThirdPartyId.runToCompletion(true, {
                            filter: JSON.stringify({ _id: data._id }),
                        })
                        .execute();
                    await this.$.refreshNavigationPanel(false);
                },
            },
            {
                title: 'Notification history',
                icon: 'factory',
                isHidden(_rowId, data) {
                    return !data.notificationTrackingIds?.length;
                },
                async onClick(_rowId, data) {
                    await this.$.dialog.page(
                        '@sage/xtrem-communication/SysNotificationState',
                        {
                            [ui.QUERY_PARAM_CUSTOM_MAIN_LIST_FILTER]: JSON.stringify({
                                notificationId: { _in: data.notificationTrackingIds ?? [] },
                            }),
                        },
                        { fullScreen: true, resolveOnCancel: true, isMainListDisplayedInDialog: true },
                    );
                },
            },
            {
                title: 'Last message',
                icon: 'error_square',
                isHidden(_rowId, data) {
                    return data.state !== 'error';
                },
                async onClick(_rowId, data) {
                    await this.$.dialog.message('error', 'Last message', data.lastMessage || '');
                },
            },
            {
                title: 'Last message',
                icon: 'disputed',
                isHidden(_rowId, data) {
                    return data.state !== 'desynchronized';
                },
                async onClick(_rowId, data) {
                    const diff = JSON.parse(data.difference || '{ differences: [] }')
                        .differences as IntegrationDifference[];
                    if (diff.length)
                        await this.$.dialog.message(
                            'error',
                            'Differences',
                            diff
                                .map(
                                    difference =>
                                        `${difference.name}: ${difference.thirdPartyValue}/${difference.xtreemValue}`,
                                )
                                .join('\n'),
                        );
                },
            },
        ],
        listItem: {
            notificationHistory: ui.nestedFields.technical({ bind: 'notificationTrackingIds' }),
            integrationId: ui.nestedFields.technical({ bind: { integration: { id: true } } }),
            title: ui.nestedFields.text({ bind: { node: { title: true } }, title: 'Object' }),
            line2: ui.nestedFields.text({
                bind: 'naturalKey',
                title: 'Stored Key',
                isHiddenOnMainField: true,
            }),
            line3: ui.nestedFields.link({
                bind: 'computedNaturalKey',
                title: 'Key',
                onClick(_id, row) {
                    return this.$.dialog.page(
                        row.pageLink ?? '',
                        { _id: row.sysIdLink ?? '' },
                        { fullScreen: true, resolveOnCancel: true },
                    );
                },
                canFilter: false,
            }),
            line_4: ui.nestedFields.label({
                bind: 'state',
                title: 'Status',
                optionType: '@sage/xtrem-communication/IntegrationState',
            }),
            line_5: ui.nestedFields.text({ bind: 'url', title: 'URL', isHidden: true }),
            line6: ui.nestedFields.text({ bind: 'lastMessage', title: 'Last message', isHiddenOnMainField: true }),
            line7: ui.nestedFields.text({ bind: 'creationStamp', title: 'Created', isHiddenOnMainField: true }),
            line8: ui.nestedFields.text({ bind: 'updateStamp', title: 'Updated', isHiddenOnMainField: true }),
            line9: ui.nestedFields.text({ bind: 'version', title: 'Version', isHiddenOnMainField: true }),
            line10: ui.nestedFields.text({ bind: 'difference', title: 'Differences', isHiddenOnMainField: true }),
            line11: ui.nestedFields.technical({ bind: 'pageLink' }),
            line12: ui.nestedFields.technical({ bind: 'sysIdLink' }),
            secondaryLink: ui.nestedFields.link({
                bind: 'secondaryPageLink',
                title: 'secondary',
                map(value) {
                    return (JSON.parse(value) as LinkToPage).text;
                },
                onClick(_id, row) {
                    const { page, parameters } = JSON.parse(row.secondaryPageLink) as LinkToPage;
                    return this.$.dialog.page(page, parameters, { fullScreen: true, resolveOnCancel: true });
                },
            }),
            line13: ui.nestedFields.link({
                bind: { integration: { name: true } },
                title: 'Third party app',
                onClick(_id, row) {
                    this.$.router.goToExternal(row.url);
                },
            }),
        },
        optionsMenu: [
            {
                title: 'All',
                graphQLFilter: {},
            },
            {
                title: 'Master data',
                graphQLFilter: {
                    node: { package: { name: '@sage/xtrem-master-data' } },
                },
            },
            {
                title: 'Finance',
                graphQLFilter: {
                    node: { package: { name: '@sage/xtrem-finance' } },
                },
            },
        ],
    },
})
export class Integration extends ui.Page<GraphApi> {
    @ui.decorators.section<Integration>({
        isHidden: true,
        title: 'More info',
    })
    moreInfos: ui.containers.Section;

    @ui.decorators.block<Integration>({
        parent() {
            return this.moreInfos;
        },
        title: 'Info',
    })
    info: ui.containers.Block;

    @ui.decorators.textAreaField<Integration>({
        parent() {
            return this.info;
        },
        title: 'Last message',
    })
    lastMessage: ui.fields.TextArea;

    @ui.decorators.referenceField<Integration, MetaNodeFactory>({
        title: 'Last message',
        isHidden: true,
        node: '@sage/xtrem-metadata/MetaNodeFactory',
        valueField: '_id',
        columns: [ui.nestedFields.text({ bind: 'title', title: 'title' })],
    })
    node: ui.fields.Reference<MetaNodeFactory>;
}
