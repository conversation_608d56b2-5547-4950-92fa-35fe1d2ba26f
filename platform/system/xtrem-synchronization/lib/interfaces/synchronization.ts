import { Node, Reference } from '@sage/xtrem-core';
import * as xtremSynchronization from '..';

/** Synchronized node */
export interface SynchronizationNode extends Node {
    skipCallApi: boolean;
    getSyncStateReference(): Promise<xtremSynchronization.nodes.SynchronizationState | null>;
}

/** Synchronized node line */
export interface SynchronizationNodeLine extends Node {
    getSyncStateLine(): Promise<ThirdPartySynchronizationNodeLine | null>;
}

export interface ThirdPartySynchronizationNode extends Node {}

export interface ThirdPartySynchronizationNodeLine extends Node {
    excludeRecord?: Promise<boolean>;
    readonly documentLine: Reference<SynchronizationNodeLine>;
}
