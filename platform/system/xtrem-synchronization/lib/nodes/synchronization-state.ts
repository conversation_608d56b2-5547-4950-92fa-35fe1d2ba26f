import * as xtremCommunication from '@sage/xtrem-communication';
import {
    AnyValue,
    AsyncResponse,
    BusinessRuleError,
    Context,
    Diagnose,
    Node,
    NodeCreateData,
    Reference,
    SystemError,
    ValidationSeverity,
    datetime,
    decorators,
    integer,
} from '@sage/xtrem-core';
import * as xtremMetadata from '@sage/xtrem-metadata';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremSynchronization from '..';
import { IntegrationDifference, LinkToPage } from '../shared-functions';

@decorators.node<SynchronizationState>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canRead: true,
    isAbstract: true,
    indexes: [{ orderBy: { integration: 1, node: 1, sysId: 1 }, isUnique: true, isNaturalKey: true }],
    async createEnd() {
        const reference = await this.getSyncStateReference();
        await this.$.set({ node: reference.$.factory.name, naturalKey: await reference.$.getNaturalKeyValue() });
    },
    async saveBegin() {
        if (!(await this.naturalKey)) {
            const reference = await this.getSyncStateReference();
            await this.$.set({ naturalKey: await reference.$.getNaturalKeyValue() });
        }
    },
})
export class SynchronizationState
    extends Node
    implements xtremSynchronization.interfaces.ThirdPartySynchronizationNode
{
    async getMapping(): Reference<xtremSynchronization.nodes.BaseMapping> {
        throw new SystemError(` ${await (await this.node).name} : getMapping not implemented`);
    }

    async getSyncStateReference(): Reference<xtremSynchronization.interfaces.SynchronizationNode> {
        throw new SystemError(` ${await (await this.node).name} : getReference not implemented`);
    }

    @decorators.referenceProperty<SynchronizationState, 'integration'>({
        isPublished: true,
        isStored: true,
        node: () => xtremSynchronization.nodes.ThirdPartyApplication,
        lookupAccess: true,
    })
    readonly integration: Reference<xtremSynchronization.nodes.ThirdPartyApplication>;

    @decorators.referenceProperty<SynchronizationState, 'mapping'>({
        isPublished: true,
        node: () => xtremSynchronization.nodes.BaseMapping,
        computeValue() {
            return this.getMapping();
        },
        lookupAccess: true,
    })
    readonly mapping: Reference<xtremSynchronization.nodes.BaseMapping>;

    @decorators.referenceProperty<SynchronizationState, 'node'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMetadata.nodes.MetaNodeFactory,
        lookupAccess: true,
    })
    readonly node: Reference<xtremMetadata.nodes.MetaNodeFactory>;

    @decorators.stringProperty<SynchronizationState, 'sysId'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        // Because of derredDefaultValue must be only on creation.
        excludedFromPayload: true,
        dataType: () => xtremSystem.dataTypes.id,
        defaultValue() {
            /** Temporary value to not have empty string */
            return xtremSystem.functions.generateNanoId();
        },
        async deferredDefaultValue() {
            const reference = await this.getSyncStateReference();
            if (!reference._id) {
                throw new SystemError(`No reference _id for ${await (await this.node).title}`);
            }
            return reference._id.toString();
        },
        lookupAccess: true,
    })
    readonly sysId: Promise<string>;

    @decorators.stringProperty<SynchronizationState, 'computedNaturalKey'>({
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.bigId,
        async computeValue() {
            return (await this.getSyncStateReference()).$.getNaturalKeyValue();
        },
        lookupAccess: true,
    })
    readonly computedNaturalKey: Promise<string>;

    @decorators.stringProperty<SynchronizationState, 'naturalKey'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.bigId,
        defaultValue: '',
    })
    readonly naturalKey: Promise<string>;

    @decorators.enumProperty<SynchronizationState, 'state'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremCommunication.enums.IntegrationStateDataType,
        defaultValue: () => 'not',
        lookupAccess: true,
    })
    readonly state: Promise<xtremCommunication.enums.IntegrationState>;

    @decorators.stringProperty<SynchronizationState, 'url'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.url,
        lookupAccess: true,
    })
    readonly url: Promise<string>;

    /** Handle source finance documents   */
    @decorators.jsonProperty<SynchronizationState, 'secondaryPageLink'>({
        isStored: true,
        isPublished: true,
        defaultValue: { page: '', parameters: {}, text: '' },
    })
    readonly secondaryPageLink: Promise<LinkToPage>;

    @decorators.stringProperty<SynchronizationState, 'lastMessage'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.description,
        lookupAccess: true,
    })
    readonly lastMessage: Promise<string>;

    @decorators.stringArrayProperty<SynchronizationState, 'notificationTrackingIds'>({
        isStored: true,
        isPublished: true,
        defaultValue: [],
        lookupAccess: true,
    })
    readonly notificationTrackingIds: Promise<string[]>;

    @decorators.datetimeProperty<SynchronizationState, 'creationStamp'>({
        isStored: true,
        isPublished: true,
    })
    readonly creationStamp: Promise<datetime>;

    @decorators.datetimeProperty<SynchronizationState, 'updateStamp'>({
        isStored: true,
        isPublished: true,
    })
    readonly updateStamp: Promise<datetime>;

    /** Last integrated version */
    @decorators.integerProperty<SynchronizationState, 'version'>({
        isStored: true,
        isPublished: true,
        defaultValue: 0,
    })
    readonly version: Promise<integer>;

    /** differences if state is desynchronized */
    @decorators.jsonProperty<SynchronizationState, 'difference'>({
        isStored: true,
        isPublished: true,
        defaultValue: { differences: [] },
    })
    readonly difference: Promise<{ differences: IntegrationDifference[] }>;

    @decorators.stringProperty<SynchronizationState, 'pageLink'>({
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.url,
        computeValue() {
            return this.getPageUrl();
        },
        lookupAccess: true,
    })
    readonly pageLink: Promise<string>;

    /** Will be overrided on businessEntityAddress customer & supplier to have a link to businessEntity  */
    @decorators.stringProperty<SynchronizationState, 'sysIdLink'>({
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.id,
        async computeValue() {
            return `${(await this.getSyncStateReference())._id}`;
        },
        lookupAccess: true,
    })
    readonly sysIdLink: Promise<string>;

    async getPageUrl(): Promise<string> {
        const node = await this.node;
        const packageName = await (await node.package).name;
        return `${packageName}/${await node.name}`;
    }

    async synchronizeMethod(parameters: {
        isForce: boolean;
    }): Promise<{ state: xtremCommunication.enums.IntegrationState; diagnoses?: Diagnose[] }> {
        throw new BusinessRuleError(
            this.$.context.localize(
                '@sage/xtrem-synchronization/not-implemented',
                "Synchronize isn't implemented for {{integrationName}}. {{isForce}}",
                {
                    integrationName: await (await this.integration).name,
                    ...parameters,
                },
            ),
        );
    }

    /** return a boolean to know if we continue */
    async before(): Promise<boolean> {
        await this.$.context.logger.debugAsync(
            async () => `${await this.$.getNaturalKeyValue()} before not implemented`,
        );
        return true;
    }

    async after(after: {
        integrationState: xtremCommunication.enums.IntegrationState;
        error: BusinessRuleError | null;
    }): Promise<void> {
        await this.$.context.logger.debugAsync(
            async () => ` ${after.integrationState} ${await this.$.getNaturalKeyValue()} after not implemented `,
        );
    }

    // eslint-disable-next-line require-await
    protected static async withWritableContext<T extends AnyValue>(
        context: Context,
        body: (ctx: Context) => AsyncResponse<T>,
    ): Promise<T> {
        if (context.isWritable) {
            return body(context);
        }
        return context.runInWritableContext(writableContext => body(writableContext));
    }

    async setPayload(payload: NodeCreateData<SynchronizationState>): Promise<void> {
        await SynchronizationState.withWritableContext(this.$.context, async context => {
            const state = this.$.isReadonly
                ? await context.read(SynchronizationState, { _id: this._id }, { forUpdate: true })
                : this;
            await state.$.set(payload);
            await state.$.save();
        });
    }

    async setMessage(message: string): Promise<void> {
        await this.setPayload({ lastMessage: message });
    }

    async setError(lastMessage: string): Promise<void> {
        await this.setPayload({ lastMessage, state: 'error' });
    }

    async synchronizeBulkMutation(parameters: {
        isForce: boolean;
    }): Promise<xtremCommunication.enums.IntegrationState> {
        const notificationTrackingIds = [...(await this.notificationTrackingIds)];
        notificationTrackingIds.push(this.$.context.batch.trackingId);
        await this.setPayload({ notificationTrackingIds });

        let error: BusinessRuleError | null = null;

        try {
            if (!(await this.before())) {
                return 'not';
            }
        } catch (integrationError) {
            this.$.context.logger.error(integrationError);
            if (!(integrationError instanceof BusinessRuleError)) {
                throw integrationError;
            }
            await this.setError(integrationError.message);
            return 'error';
        }

        let integrationState: xtremCommunication.enums.IntegrationState;

        try {
            const synchronize = await this.synchronizeMethod(parameters);
            integrationState = synchronize.state;

            /** Error management */
            if (synchronize.diagnoses?.some(diag => diag.severity === ValidationSeverity.error)) {
                error = new BusinessRuleError(synchronize.diagnoses.map(diag => diag.message).join('\n'));
            }
        } catch (integrationError) {
            this.$.context.logger.error(integrationError);
            if (!(integrationError instanceof BusinessRuleError)) {
                throw integrationError;
            }
            error = integrationError;
            integrationState = 'error';
            await this.setError(integrationError?.message);
        }
        await this.after({ integrationState, error });

        return integrationState;
    }

    @decorators.bulkMutation<typeof SynchronizationState, 'synchronize'>({
        isPublished: true,
        async onComplete(context: Context, stateArray: xtremCommunication.enums.IntegrationState[]) {
            const length = {
                success: stateArray.filter(state => state === 'success').length,
                error: stateArray.filter(state => state === 'error').length,
                pending: stateArray.filter(state => state === 'pending').length,
                total: stateArray.length,
            };

            await context.notifyUser({
                title: context.localize('@sage/xtrem-synchronization/synchronize-finish', 'Mass synchronization done.'),
                description: context.localize(
                    '@sage/xtrem-synchronization/synchronize-finish-status',
                    '{{success}} elements synchronized successfully. {{error}} elements with errors.',
                    length,
                ),
                icon: 'sync',
                level: length.success === length.total ? 'success' : 'error',
                shouldDisplayToast: true,
                actions: [
                    {
                        link: context.batch.notificationStateLink,
                        style: 'link',
                        title: context.localize('@sage/xtrem-synchronization/synchronize-history', 'History'),
                    },
                ],
            });
        },
    })
    static synchronize(
        _context: Context,
        synchronizationState: SynchronizationState,
    ): Promise<xtremCommunication.enums.IntegrationState> {
        return synchronizationState.synchronizeBulkMutation({ isForce: false });
    }

    @decorators.bulkMutation<typeof SynchronizationState, 'forceSynchronize'>({
        isPublished: true,
    })
    static forceSynchronize(
        _context: Context,
        synchronizationState: SynchronizationState,
    ): Promise<xtremCommunication.enums.IntegrationState> {
        return synchronizationState.synchronizeBulkMutation({ isForce: true });
    }

    @decorators.bulkMutation<typeof SynchronizationState, 'resetThirdPartyId'>({
        isPublished: true,
    })
    static async resetThirdPartyId(context: Context, synchronizationState: SynchronizationState): Promise<boolean> {
        if (['pending', 'success', 'desynchronised'].includes(await synchronizationState.state)) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-synchronization/synchronize-reset-third-party-id-not-allowed',
                    'Reset third party id is not allowed in this state',
                ),
            );
        }
        return synchronizationState.resetThirdPartyIdBulkMutation();
    }

    async resetThirdPartyIdBulkMutation(): Promise<boolean> {
        await this.$.context.logger.debugAsync(
            async () => `${await this.$.getNaturalKeyValue()} resetThirdPartyIdBulkMutation not implemented`,
        );
        return false;
    }

    @decorators.bulkMutation<typeof SynchronizationState, 'updateSysId'>({
        isPublished: true,
    })
    static async updateSysId(_context: Context, synchronizationState: SynchronizationState): Promise<boolean> {
        await synchronizationState.updateLink();
        return true;
    }

    async updateLink(): Promise<void> {
        const reference = await this.getSyncStateReference();
        const sysId = reference._id.toString();
        const naturalKey = await reference.$.getNaturalKeyValue();
        if ((await this.sysId) === sysId && naturalKey === (await this.naturalKey)) {
            return;
        }
        const nodeName = await (await this.node).name;
        await this.$.context.batch.logMessage('warning', `${nodeName} ${await this.naturalKey} link updated`);
        await this.setPayload({ sysId, naturalKey });
    }
}
