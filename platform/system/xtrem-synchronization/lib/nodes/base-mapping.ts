import { BusinessRuleError, Node, Reference, decorators, nanoIdDataType } from '@sage/xtrem-core';
import * as xtremMetadata from '@sage/xtrem-metadata';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremSynchronization from '..';

@decorators.node<BaseMapping>({
    storage: 'sql',
    isPublished: true,
    isAbstract: true,
    isSetupNode: true,
    indexes: [
        { orderBy: { application: 1, thirdPartyObjectName: 1, nodeFactory: 1 }, isUnique: true, isNaturalKey: true },
    ],
})
export class BaseMapping extends Node {
    @decorators.referenceProperty<BaseMapping, 'application'>({
        isStored: true,
        isPublished: true,
        node: () => xtremSynchronization.nodes.ThirdPartyApplication,
    })
    readonly application: Reference<xtremSynchronization.nodes.ThirdPartyApplication>;

    @decorators.referenceProperty<BaseMapping, 'nodeFactory'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMetadata.nodes.MetaNodeFactory,
        lookupAccess: true,
    })
    readonly nodeFactory: Reference<xtremMetadata.nodes.MetaNodeFactory>;

    @decorators.enumProperty<BaseMapping, 'synchronizationDirection'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremSynchronization.enums.SynchronizationDirectionDataType,
        defaultValue: 'not',
        lookupAccess: true,
    })
    readonly synchronizationDirection: Promise<xtremSynchronization.enums.SynchronizationDirection>;

    @decorators.stringProperty<BaseMapping, 'thirdPartyObjectName'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
        lookupAccess: true,
    })
    readonly thirdPartyObjectName: Promise<string>;

    @decorators.booleanProperty<BaseMapping, 'isActive'>({
        isStored: true,
        isPublished: true,
        defaultValue: true,
        provides: ['isActive'],
        isOwnedByCustomer: true,
    })
    readonly isActive: Promise<boolean>;

    @decorators.booleanProperty<BaseMapping, 'canDelete'>({
        isStored: true,
        isPublished: true,
        defaultValue: true,
        lookupAccess: true,
    })
    readonly canDelete: Promise<boolean>;

    @decorators.booleanProperty<BaseMapping, 'canCreate'>({
        isStored: true,
        isPublished: true,
        defaultValue: true,
        lookupAccess: true,
    })
    readonly canCreate: Promise<boolean>;

    @decorators.booleanProperty<BaseMapping, 'canUpdate'>({
        isStored: true,
        isPublished: true,
        defaultValue: true,
        lookupAccess: true,
    })
    readonly canUpdate: Promise<boolean>;

    /** notificationId of the current process that process mass integration     */
    @decorators.stringProperty<BaseMapping, 'notificationId'>({
        isStored: true,
        isPublished: true,
        dataType: () => nanoIdDataType,
        defaultValue: '',
    })
    readonly notificationId: Promise<string>;

    @decorators.stringArrayProperty<BaseMapping, 'mappedPropertyNames'>({
        isPublished: true,
        computeValue() {
            return this.getMappedPropertyNames();
        },
    })
    readonly mappedPropertyNames: Promise<string[]>;

    async getMappedPropertyNames(): Promise<string[]> {
        throw new BusinessRuleError(`not implemented ${await this.$.getNaturalKeyValue()}`);
    }
}
