import { decorators, Node } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';

@decorators.node<ThirdPartyApplication>({
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    indexes: [{ orderBy: { id: 1 }, isUnique: true, isNaturalKey: true }],
    isSetupNode: true,
})
export class ThirdPartyApplication extends Node {
    @decorators.stringProperty<ThirdPartyApplication, 'id'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.id,
        lookupAccess: true,
    })
    readonly id: Promise<string>;

    @decorators.stringProperty<ThirdPartyApplication, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.localizedName,
        lookupAccess: true,
    })
    readonly name: Promise<string>;

    @decorators.booleanProperty<ThirdPartyApplication, 'isActive'>({
        isStored: true,
        isPublished: true,
        defaultValue: true,
        provides: ['isActive'],
        isOwnedByCustomer: true,
        lookupAccess: true,
    })
    readonly isActive: Promise<boolean>;
}
