import { Activity } from '@sage/xtrem-core';
import * as xtremMetadata from '@sage/xtrem-metadata';
import { SynchronizationState } from '../nodes/synchronization-state';

export const synchronizationState = new Activity({
    description: 'Synchronization state',
    node: () => SynchronizationState,
    __filename,
    permissions: ['read', 'synchronize', 'forceSynchronize'],
    operationGrants: {
        read: [
            {
                operations: ['lookup'],
                on: [() => xtremMetadata.nodes.MetaNodeFactory],
            },
        ],
        synchronize: [
            {
                operations: ['read'],
                on: [() => SynchronizationState],
            },
            {
                operations: ['lookup'],
                on: [() => xtremMetadata.nodes.MetaNodeFactory],
            },
        ],
        forceSynchronize: [
            {
                operations: ['read'],
                on: [() => SynchronizationState],
            },
            {
                operations: ['lookup'],
                on: [() => xtremMetadata.nodes.MetaNodeFactory],
            },
        ],
        updateSysId: [
            {
                operations: ['read'],
                on: [() => SynchronizationState],
            },
            {
                operations: ['lookup'],
                on: [() => xtremMetadata.nodes.MetaNodeFactory],
            },
        ],
    },
});
