declare module '@sage/xtrem-synchronization-api-partial' {
    import type { Package as SageXtremAuthorization$Package } from '@sage/xtrem-authorization-api';
    import type { Package as SageXtremCommunication$Package } from '@sage/xtrem-communication-api';
    import type { MetaNodeFactory, Package as SageXtremMetadata$Package } from '@sage/xtrem-metadata-api';
    import type { Package as SageXtremSystem$Package, SysVendor, User } from '@sage/xtrem-system-api';
    import type {
        AggregateQueryOperation,
        AggregateReadOperation,
        AsyncOperation,
        ClientNode,
        ClientNodeInput,
        CreateOperation,
        GetDefaultsOperation,
        GetDuplicateOperation,
        QueryOperation,
        ReadOperation,
        UpdateByIdOperation,
        UpdateOperation,
        integer,
    } from '@sage/xtrem-client';
    export interface SynchronizationDirection$Enum {
        not: 0;
        inbound: 1;
        outbound: 2;
        both: 3;
    }
    export type SynchronizationDirection = keyof SynchronizationDirection$Enum;
    export interface BaseMapping extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        application: ThirdPartyApplication;
        nodeFactory: MetaNodeFactory;
        synchronizationDirection: SynchronizationDirection;
        thirdPartyObjectName: string;
        isActive: boolean;
        canDelete: boolean;
        canCreate: boolean;
        canUpdate: boolean;
        notificationId: string;
        mappedPropertyNames: string[];
    }
    export interface BaseMappingInput extends ClientNodeInput {
        _vendor?: integer | string;
        _constructor?: string;
        application?: integer | string;
        nodeFactory?: integer | string;
        synchronizationDirection?: SynchronizationDirection;
        thirdPartyObjectName?: string;
        isActive?: boolean | string;
        canDelete?: boolean | string;
        canCreate?: boolean | string;
        canUpdate?: boolean | string;
        notificationId?: string;
    }
    export interface BaseMappingBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        application: ThirdPartyApplication;
        nodeFactory: MetaNodeFactory;
        synchronizationDirection: SynchronizationDirection;
        thirdPartyObjectName: string;
        isActive: boolean;
        canDelete: boolean;
        canCreate: boolean;
        canUpdate: boolean;
        notificationId: string;
        mappedPropertyNames: string[];
    }
    export interface BaseMapping$Lookups {
        _vendor: QueryOperation<SysVendor>;
        application: QueryOperation<ThirdPartyApplication>;
        nodeFactory: QueryOperation<MetaNodeFactory>;
    }
    export interface BaseMapping$Operations {
        lookups(dataOrId: string | { data: BaseMappingInput }): BaseMapping$Lookups;
    }
    export interface SynchronizationState extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        integration: ThirdPartyApplication;
        mapping: BaseMapping;
        node: MetaNodeFactory;
        sysId: string;
        computedNaturalKey: string;
        naturalKey: string;
        state: IntegrationState;
        url: string;
        secondaryPageLink: string;
        lastMessage: string;
        notificationTrackingIds: string[];
        creationStamp: string;
        updateStamp: string;
        version: integer;
        difference: string;
        pageLink: string;
        sysIdLink: string;
    }
    export interface SynchronizationStateInput extends ClientNodeInput {
        _constructor?: string;
        integration?: integer | string;
        node?: integer | string;
        sysId?: string;
        naturalKey?: string;
        state?: IntegrationState;
        url?: string;
        secondaryPageLink?: string;
        lastMessage?: string;
        notificationTrackingIds?: string[];
        creationStamp?: string;
        updateStamp?: string;
        version?: integer | string;
        difference?: string;
    }
    export interface SynchronizationStateBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        _constructor: string;
        integration: ThirdPartyApplication;
        mapping: BaseMapping;
        node: MetaNodeFactory;
        sysId: string;
        computedNaturalKey: string;
        naturalKey: string;
        state: IntegrationState;
        url: string;
        secondaryPageLink: any;
        lastMessage: string;
        notificationTrackingIds: string[];
        creationStamp: string;
        updateStamp: string;
        version: integer;
        difference: any;
        pageLink: string;
        sysIdLink: string;
    }
    export interface SynchronizationState$AsyncOperations {
        synchronize: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        forceSynchronize: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        resetThirdPartyId: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        updateSysId: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
    }
    export interface SynchronizationState$Lookups {
        integration: QueryOperation<ThirdPartyApplication>;
        mapping: QueryOperation<BaseMapping>;
        node: QueryOperation<MetaNodeFactory>;
    }
    export interface SynchronizationState$Operations {
        query: QueryOperation<SynchronizationState>;
        read: ReadOperation<SynchronizationState>;
        aggregate: {
            read: AggregateReadOperation<SynchronizationState>;
            query: AggregateQueryOperation<SynchronizationState>;
        };
        create: CreateOperation<SynchronizationStateInput, SynchronizationState>;
        getDuplicate: GetDuplicateOperation<SynchronizationState>;
        asyncOperations: SynchronizationState$AsyncOperations;
        lookups(dataOrId: string | { data: SynchronizationStateInput }): SynchronizationState$Lookups;
        getDefaults: GetDefaultsOperation<SynchronizationState>;
    }
    export interface ThirdPartyApplication extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        isActive: boolean;
    }
    export interface ThirdPartyApplicationInput extends ClientNodeInput {
        _vendor?: integer | string;
        id?: string;
        name?: string;
        isActive?: boolean | string;
    }
    export interface ThirdPartyApplicationBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        isActive: boolean;
    }
    export interface ThirdPartyApplication$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface ThirdPartyApplication$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface ThirdPartyApplication$Operations {
        query: QueryOperation<ThirdPartyApplication>;
        read: ReadOperation<ThirdPartyApplication>;
        aggregate: {
            read: AggregateReadOperation<ThirdPartyApplication>;
            query: AggregateQueryOperation<ThirdPartyApplication>;
        };
        create: CreateOperation<ThirdPartyApplicationInput, ThirdPartyApplication>;
        getDuplicate: GetDuplicateOperation<ThirdPartyApplication>;
        update: UpdateOperation<ThirdPartyApplicationInput, ThirdPartyApplication>;
        updateById: UpdateByIdOperation<ThirdPartyApplicationInput, ThirdPartyApplication>;
        asyncOperations: ThirdPartyApplication$AsyncOperations;
        lookups(dataOrId: string | { data: ThirdPartyApplicationInput }): ThirdPartyApplication$Lookups;
        getDefaults: GetDefaultsOperation<ThirdPartyApplication>;
    }
    export interface Package {
        '@sage/xtrem-synchronization/BaseMapping': BaseMapping$Operations;
        '@sage/xtrem-synchronization/SynchronizationState': SynchronizationState$Operations;
        '@sage/xtrem-synchronization/ThirdPartyApplication': ThirdPartyApplication$Operations;
    }
    export interface GraphApi
        extends Package,
            SageXtremAuthorization$Package,
            SageXtremCommunication$Package,
            SageXtremMetadata$Package,
            SageXtremSystem$Package {}
}
declare module '@sage/xtrem-synchronization-api' {
    export type * from '@sage/xtrem-synchronization-api-partial';
}
declare module '@sage/xtrem-authorization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-synchronization-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-communication-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-synchronization-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-metadata-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-synchronization-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-synchronization-api';
    export interface GraphApi extends GraphApiExtension {}
}
