{"@sage/xtrem-synchronization": [{"topic": "SynchronizationState/forceSynchronize/start", "queue": "interop", "sourceFileName": "synchronization-state.ts"}, {"topic": "SynchronizationState/resetThirdPartyId/start", "queue": "interop", "sourceFileName": "synchronization-state.ts"}, {"topic": "SynchronizationState/synchronize/start", "queue": "interop", "sourceFileName": "synchronization-state.ts"}, {"topic": "SynchronizationState/updateSysId/start", "queue": "interop", "sourceFileName": "synchronization-state.ts"}, {"topic": "ThirdPartyApplication/asyncExport/start", "queue": "import-export", "sourceFileName": "third-party-application.ts"}]}