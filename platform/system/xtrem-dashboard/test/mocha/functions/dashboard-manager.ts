import * as xtremAuthorization from '@sage/xtrem-authorization';
import { Test } from '@sage/xtrem-core';
import { assert, expect } from 'chai';
import { afterEach, beforeEach, describe, it } from 'mocha';
import * as sinon from 'sinon';
import { DashboardManager } from '../../../lib/functions/dashboard-manager';

describe('functions/dashboard-manager', () => {
    let dashboardManager: DashboardManager;

    beforeEach(() => {
        dashboardManager = new DashboardManager();
    });

    afterEach(() => {
        sinon.restore();
    });

    describe('accessToOperation', () => {
        it('should call xtrem-authorization', () =>
            Test.withContext(async context => {
                const getUserAccessFor = sinon
                    .stub(xtremAuthorization.services.sysAccessRightsManager, 'getUserAccessFor')
                    .resolves({
                        sites: null,
                        accessCodes: null,
                        status: 'authorized',
                    });
                await dashboardManager.accessToOperation('update', context);
                assert.equal(getUserAccessFor.callCount, 1);
            }));

        it('should call xtrem-authorization multiple times', () =>
            Test.withContext(async context => {
                const getUserAccessFor = sinon
                    .stub(xtremAuthorization.services.sysAccessRightsManager, 'getUserAccessFor')
                    .resolves({
                        sites: null,
                        accessCodes: null,
                        status: 'authorized',
                    });
                await dashboardManager.accessToOperation(['create', 'update'], context);
                assert.equal(getUserAccessFor.callCount, 2);
            }));

        it('should return false when the operation is not authorized', () =>
            Test.withContext(async context => {
                sinon.stub(xtremAuthorization.services.sysAccessRightsManager, 'getUserAccessFor').resolves({
                    sites: null,
                    accessCodes: null,
                    status: 'unauthorized',
                });
                const result = await dashboardManager.accessToOperation('delete', context);
                assert.equal(result, false);
            }));

        it('should return false when the operation is not available', () =>
            Test.withContext(async context => {
                sinon.stub(xtremAuthorization.services.sysAccessRightsManager, 'getUserAccessFor').resolves({
                    sites: null,
                    accessCodes: null,
                    status: 'unavailable',
                });
                const result = await dashboardManager.accessToOperation('update', context);
                assert.equal(result, false);
            }));

        it('should return false when any of the operations is not authorized', () =>
            Test.withContext(async context => {
                sinon
                    .stub(xtremAuthorization.services.sysAccessRightsManager, 'getUserAccessFor')
                    .onCall(0)
                    .resolves({
                        sites: null,
                        accessCodes: null,
                        status: 'authorized',
                    })
                    .onCall(1)
                    .resolves({ sites: null, accessCodes: null, status: 'unauthorized' });
                const result = await dashboardManager.accessToOperation(['read', 'update'], context);
                assert.equal(result, false);
            }));

        it('should return true when the operation is authorized', () =>
            Test.withContext(async context => {
                sinon.stub(xtremAuthorization.services.sysAccessRightsManager, 'getUserAccessFor').resolves({
                    sites: null,
                    accessCodes: null,
                    status: 'authorized',
                });
                const result = await dashboardManager.accessToOperation('delete', context);
                assert.equal(result, true);
            }));

        it('should return true when all the operations are authorized', () =>
            Test.withContext(async context => {
                sinon
                    .stub(xtremAuthorization.services.sysAccessRightsManager, 'getUserAccessFor')
                    .onCall(0)
                    .resolves({
                        sites: null,
                        accessCodes: null,
                        status: 'authorized',
                    })
                    .onCall(1)
                    .resolves({ sites: null, accessCodes: null, status: 'authorized' });
                const result = await dashboardManager.accessToOperation(['update', 'delete'], context);
                assert.equal(result, true);
            }));
    });

    describe('accessToOperationOrFail', () => {
        it('should call accessToOperation', () =>
            Test.withContext(async context => {
                sinon.stub(xtremAuthorization.services.sysAccessRightsManager, 'getUserAccessFor').resolves({
                    sites: null,
                    accessCodes: null,
                    status: 'authorized',
                });
                const accessToOperation = sinon.spy(dashboardManager, 'accessToOperation');
                await dashboardManager.accessToOperationOrFail('update', context);
                assert.equal(accessToOperation.calledOnceWith('update', context), true);
            }));

        it('should throw exception when accessToOperation returns false', () =>
            Test.withContext(context => {
                sinon.stub(dashboardManager, 'accessToOperation').resolves(false);
                // eslint-disable-next-line @typescript-eslint/no-floating-promises
                expect(dashboardManager.accessToOperationOrFail('update', context)).rejectedWith('Unauthorized');
            }));
    });
});
