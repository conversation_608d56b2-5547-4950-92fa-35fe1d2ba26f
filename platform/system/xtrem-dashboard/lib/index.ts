import { CoreHooks } from '@sage/xtrem-core';
import * as activities from './activities/_index';
import * as enums from './enums/index';
import * as classes from './functions/dashboard-manager';
import * as nodes from './nodes/_index';

export { activities, classes, enums, nodes };

export function updateContext() {
    CoreHooks.createDashboardManager = () => new classes.DashboardManager();
}

updateContext();
