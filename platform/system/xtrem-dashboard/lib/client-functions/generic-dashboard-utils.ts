import { Dict, GraphQLTypes, filterGraphqlMapping } from '@sage/xtrem-shared';
import * as ui from '@sage/xtrem-ui';
import { camelCase, escapeRegExp, merge, startCase } from 'lodash';

export enum FilterType {
    EQUALS = 'equals',
    LESS_THAN = 'lessThan',
    LESS_THAN_EQUAL = 'lessThanOrEqual',
    RANGE = 'inRange',
    GREATER_THAN = 'greaterThan',
    GREATER_THAN_EQUAL = 'greaterThanOrEqual',
    CONTAINS = 'contains',
    STARTS_WITH = 'startsWith',
    ENDS_WITH = 'endsWith',
}

export enum GraphqlTypes {
    Enum = 'Enum',
    Id = 'Id', // Yes, it is lowercase 'd'
    String = 'String',
    Int = 'Int',
    Float = 'Float',
    Decimal = 'Decimal',
    Boolean = 'Boolean',
    Date = 'Date',
    DateTime = 'datetime',
    IntReference = 'IntReference', // Non-vital references
    IntOrString = 'IntOrString',
    InputStream = '_InputStream',
    Json = 'Json',
}

export const PATH_JOIN_TOKEN = ' > ';

export interface FilterEntry {
    _id: string;
    property: string;
    type: FilterableNodeProperty;
    enumValues?: string[];
    method?: string;
    textFilterValue?: string;
    numericFilterValue?: number;
    selectFilterValue?: string;
}

export interface FilterableNodeProperty {
    type: string;
    referredNode: string | null;
    enumValues?: string[];
}

const pascalCase = (input: string) => startCase(camelCase(input)).replace(/ /g, '');

export const getAvailableFiltersForPropertyType = (propertyType: GraphQLTypes) => {
    return filterGraphqlMapping[propertyType] ?? [];
};

export const getNodeList = async (pageInstance: ui.Page): Promise<string[]> => {
    const query = `{
        __type(name: "RootQueryType") {
          fields {
            name
            type {
              fields {
                name
              }
            }
          }
        }
      }`;

    const response = await pageInstance.$.graph.raw(query);

    return response.__type.fields.reduce((prevValue: string[], currentValue: any) => {
        const pck = currentValue.name;

        currentValue.type.fields.forEach((node: { name: string }) => {
            prevValue.push(`@sage/${pck}/${pascalCase(node.name)}`);
        });
        return prevValue;
    }, [] as string[]);
};

export const getEnumInputValue = async (pageInstance: ui.Page, type: string): Promise<string[]> => {
    const enumType = type.replace('_EnumInput', '');
    const query = `{
        __type(name: "${enumType}") {
          enumValues{
            name
          }
        }
      }`;
    const response = await pageInstance.$.graph.raw(query);
    return response.__type?.enumValues ? response.__type.enumValues.map((e: any) => e.name) : [];
};

export const getFilterableNodeProperties = async (
    pageInstance: ui.Page,
    nodeName: string,
): Promise<Dict<FilterableNodeProperty>> => {
    const nodeList = await getNodeList(pageInstance);
    const formattedNodeName = pascalCase(nodeName.split('/').pop()!).replace(/ /g, '');
    const query = `{
        inputType: __type(name: "${formattedNodeName}_Input") {
          inputFields {
            name
            type {
                name
            }
          }
        }
        outputType: __type(name: "${formattedNodeName}") {
            fields {
              name
              type {
                name
              }
            }
          }
    }`;

    const findReferredNode = (propertyName: string): string | null => {
        const typeName = response.outputType.fields.find((p: any) => p.name === propertyName)?.type.name;
        if (!typeName) {
            return null;
        }
        return nodeList.find(n => n.split('/').pop() === typeName) || null;
    };

    const response = await pageInstance.$.graph.raw(query);
    const result: Dict<FilterableNodeProperty> = {};

    // eslint-disable-next-line no-restricted-syntax
    for (const currentValue of response.inputType.inputFields) {
        const typeName: string = currentValue.type.name;
        if (typeName) {
            const formattedItem: FilterableNodeProperty = {
                type: typeName,
                referredNode: findReferredNode(currentValue.name),
            };
            if (typeName.indexOf('_EnumInput') !== -1) {
                formattedItem.enumValues = await getEnumInputValue(pageInstance, typeName);
            }

            result[currentValue.name] = formattedItem;
        }
    }

    return result;
};

export const generateGraphQLFilter = (settingsFilters: FilterEntry[]) => {
    return settingsFilters.reduce((prevValue: any, currentValue: any) => {
        const property = currentValue.property;
        const type = currentValue.propertyType;
        let filterValue = {};

        if (type === GraphqlTypes.String) {
            switch (type) {
                case FilterType.CONTAINS:
                    filterValue = { _regex: escapeRegExp(currentValue.textFilterValue), _options: 'i' };
                    break;
                case FilterType.STARTS_WITH:
                    filterValue = { _regex: `^${escapeRegExp(currentValue.textFilterValue)}`, _options: 'i' };
                    break;
                case FilterType.ENDS_WITH:
                    filterValue = { _regex: `${escapeRegExp(currentValue.textFilterValue)}$`, _options: 'i' };
                    break;
                default:
                    filterValue = { _eq: currentValue.textFilterValue };
            }
        } else if (
            type === GraphqlTypes.Int ||
            type === GraphqlTypes.IntOrString ||
            type === GraphqlTypes.Decimal ||
            type === GraphqlTypes.Float
        ) {
            switch (type) {
                case FilterType.LESS_THAN:
                    filterValue = { _lt: Number(currentValue.numericFilterValue) };
                    break;
                case FilterType.LESS_THAN_EQUAL:
                    filterValue = { _lte: Number(currentValue.numericFilterValue) };
                    break;

                case FilterType.GREATER_THAN:
                    filterValue = { _gt: Number(currentValue.numericFilterValue) };
                    break;
                case FilterType.GREATER_THAN_EQUAL:
                    filterValue = { _gte: Number(currentValue.numericFilterValue) };
                    break;
                default:
                    filterValue = { _eq: Number(currentValue.numericFilterValue) };
            }
        } else if (type.indexOf('_EnumInput') !== -1) {
            filterValue = { _eq: String(currentValue.selectFilterValue) };
        }

        const currentFilter = property
            .split(PATH_JOIN_TOKEN)
            .reverse()
            .reduce((prevFilter: any, currentKey: string) => {
                return { [currentKey]: prevFilter };
            }, filterValue);

        return merge(currentFilter, prevValue);
    }, {});
};
