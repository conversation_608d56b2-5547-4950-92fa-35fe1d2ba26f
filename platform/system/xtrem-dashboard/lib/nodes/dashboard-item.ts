import { Collection, decorators, Node, Reference } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremDashboard from '../../index';

@decorators.node<DashboardItem>({
    storage: 'sql',
    canDelete: true,
    canRead: true,
    canSearch: true,
    isPublished: false,
    isVitalCollectionChild: true,
})
export class DashboardItem extends Node {
    @decorators.referenceProperty<DashboardItem, 'dashboard'>({
        isStored: true,
        isPublished: true,
        node: () => xtremDashboard.nodes.Dashboard,
        isVitalParent: true,
    })
    readonly dashboard: Reference<xtremDashboard.nodes.Dashboard>;

    @decorators.stringProperty<DashboardItem, 'type'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.shortDescription,
    })
    readonly type: Promise<string>;

    @decorators.collectionProperty<DashboardItem, 'positions'>({
        isPublished: true,
        reverseReference: 'dashboardItem',
        node: () => xtremDashboard.nodes.DashboardItemPosition,
        isVital: true,
    })
    readonly positions: Collection<xtremDashboard.nodes.DashboardItemPosition>;

    @decorators.jsonProperty<DashboardItem, 'settings'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly settings: Promise<Object | null>;
}
