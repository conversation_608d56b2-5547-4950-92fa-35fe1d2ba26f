import { decorators, integer, Node, Reference } from '@sage/xtrem-core';
import * as xtremDashboard from '../../index';

@decorators.node<DashboardItemPosition>({
    storage: 'sql',
    canDelete: true,
    canRead: true,
    canSearch: true,
    isPublished: false,
    indexes: [{ orderBy: { dashboardItem: 1, breakpoint: 1 }, isUnique: true, isNaturalKey: true }],
    isVitalCollectionChild: true,
})
export class DashboardItemPosition extends Node {
    @decorators.referenceProperty<DashboardItemPosition, 'dashboardItem'>({
        isStored: true,
        isPublished: true,
        node: () => xtremDashboard.nodes.DashboardItem,
        isVitalParent: true,
    })
    readonly dashboardItem: Reference<xtremDashboard.nodes.DashboardItem>;

    @decorators.enumProperty<DashboardItemPosition, 'breakpoint'>({
        dataType: () => xtremDashboard.enums.BreakpointDataType,
        isStored: true,
        isPublished: true,
        isNullable: false,
    })
    readonly breakpoint: Promise<xtremDashboard.enums.Breakpoint>;

    @decorators.integerProperty<DashboardItemPosition, 'x'>({
        isStored: true,
        isPublished: true,
        async control(ctx, val) {
            await ctx.error.if(val).is.negative();
        },
    })
    readonly x: Promise<integer>;

    @decorators.integerProperty<DashboardItemPosition, 'y'>({
        isStored: true,
        isPublished: true,
        async control(ctx, val) {
            await ctx.error.if(val).is.negative();
        },
    })
    readonly y: Promise<integer>;

    @decorators.integerProperty<DashboardItemPosition, 'w'>({
        isStored: true,
        isPublished: true,
        async control(ctx, val) {
            await ctx.error.if(val).is.less.than(1);
        },
    })
    readonly w: Promise<integer>;

    @decorators.integerProperty<DashboardItemPosition, 'h'>({
        isStored: true,
        isPublished: true,
        async control(ctx, val) {
            await ctx.error.if(val).is.less.than(1);
        },
    })
    readonly h: Promise<integer>;
}
