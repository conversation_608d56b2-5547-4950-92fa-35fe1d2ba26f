import { decorators, Node } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';

@decorators.node<WidgetCategory>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    indexes: [{ orderBy: { id: 1 }, isUnique: true, isNaturalKey: true }],
})
export class WidgetCategory extends Node {
    @decorators.stringProperty<WidgetCategory, 'title'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        dataType: () => xtremSystem.dataTypes.localizedShortDescription,
    })
    readonly title: Promise<string>;

    @decorators.stringProperty<WidgetCategory, 'id'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        dataType: () => xtremSystem.dataTypes.code,
    })
    readonly id: Promise<string>;
}
