import { Collection, decorators, Node, Reference, useDefaultValue } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremDashboard from '../../index';

@decorators.node<Dashboard>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    canDuplicate: true,
    isSetupNode: true,
    indexes: [{ orderBy: { id: 1, owner: 1 }, isUnique: true, isNaturalKey: true }],
})
export class Dashboard extends Node {
    @decorators.stringProperty<Dashboard, 'id'>({
        isStored: true,
        duplicatedValue: useDefaultValue,
        defaultValue() {
            return xtremSystem.functions.generateNanoId();
        },
        dataType: () => xtremSystem.dataTypes.id,
    })
    readonly id: Promise<string>;

    @decorators.stringProperty<Dashboard, 'title'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        dataType: () => xtremSystem.dataTypes.localizedShortDescription,
    })
    readonly title: Promise<string>;

    @decorators.stringProperty<Dashboard, 'description'>({
        isStored: true,
        isPublished: true,
        defaultValue: '',
        dataType: () => xtremSystem.dataTypes.localizedDescription,
    })
    readonly description: Promise<string>;

    @decorators.stringProperty<Dashboard, 'listIcon'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly listIcon: Promise<string>;

    @decorators.referenceProperty<Dashboard, 'owner'>({
        isStored: true,
        isNullable: true,
        isPublished: true,
        allowedInUniqueIndex: true,
        node: () => xtremSystem.nodes.User,
    })
    readonly owner: Reference<xtremSystem.nodes.User | null>;

    @decorators.collectionProperty<Dashboard, 'items'>({
        isPublished: true,
        reverseReference: 'dashboard',
        node: () => xtremDashboard.nodes.DashboardItem,
        isVital: true,
    })
    readonly items: Collection<xtremDashboard.nodes.DashboardItem>;

    /**
     * Indicates which group the dashboard belongs to. `home` is the default group.
     */
    @decorators.stringProperty<Dashboard, 'group'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
        defaultValue: 'home',
    })
    readonly group: Promise<string>;

    /**
     * Indicates weather the dashboard is selected or if it has no owner it indicates that it is the default dashboard template
     */
    @decorators.booleanProperty<Dashboard, 'isSelected'>({
        isStored: true,
        isPublished: true,
    })
    readonly isSelected: Promise<boolean>;

    get children(): Promise<xtremDashboard.nodes.DashboardItem[]> {
        return this.items.toArray();
    }
}
