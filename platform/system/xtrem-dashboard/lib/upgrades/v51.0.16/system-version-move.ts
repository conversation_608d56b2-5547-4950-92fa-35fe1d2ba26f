import { CustomSqlAction } from '@sage/xtrem-system';

export const systemVersionMove = new CustomSqlAction({
    description: 'Moving SystemVersion from masterData to system package ',
    body: async helper => {
        await helper.executeSql(`UPDATE ${helper.schemaName}.dashboard_item
            SET type = '@sage/xtrem-system/SystemVersion'
            WHERE type =  '@sage/xtrem-master-data/SystemVersion'`);
    },
});
