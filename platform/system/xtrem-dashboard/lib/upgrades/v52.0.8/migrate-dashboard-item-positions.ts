import { CustomSqlAction } from '@sage/xtrem-system';

export const migrateDashboardItemPositions = new CustomSqlAction({
    description: 'Migrate dashboard item positions',
    fixes: {
        tables: ['dashboard_item'],
    },
    body: async helper => {
        await helper.executeSql(`
            DO $$ DECLARE
                positionRecord RECORD;
                BEGIN
                    -- There we are droping the constraints to avoid the constraint violation
                    SET CONSTRAINTS ALL DEFERRED;
                    FOR positionRecord IN (SELECT _id, x, y, _tenant_id, _create_user, _create_stamp, _update_user, _update_stamp FROM ${helper.schemaName}.dashboard_item)
                    LOOP
                        INSERT INTO ${helper.schemaName}.dashboard_item_position (dashboard_item, x, y, breakpoint, _tenant_id, _create_user, _create_stamp, _update_user, _update_stamp)
                        VALUES (positionRecord._id, positionRecord.x, positionRecord.y, 'xxs', positionRecord._tenant_id, positionRecord._create_user, positionRecord._create_stamp, positionRecord._update_user, positionRecord._update_stamp);

                        INSERT INTO ${helper.schemaName}.dashboard_item_position (dashboard_item, x, y, breakpoint, _tenant_id, _create_user, _create_stamp, _update_user, _update_stamp)
                        VALUES (positionRecord._id, positionRecord.x, positionRecord.y, 'xs', positionRecord._tenant_id, positionRecord._create_user, positionRecord._create_stamp, positionRecord._update_user, positionRecord._update_stamp);

                        INSERT INTO ${helper.schemaName}.dashboard_item_position (dashboard_item, x, y, breakpoint, _tenant_id, _create_user, _create_stamp, _update_user, _update_stamp)
                        VALUES (positionRecord._id, positionRecord.x, positionRecord.y, 'sm', positionRecord._tenant_id, positionRecord._create_user, positionRecord._create_stamp, positionRecord._update_user, positionRecord._update_stamp);

                        INSERT INTO ${helper.schemaName}.dashboard_item_position (dashboard_item, x, y, breakpoint, _tenant_id, _create_user, _create_stamp, _update_user, _update_stamp)
                        VALUES (positionRecord._id, positionRecord.x, positionRecord.y, 'md', positionRecord._tenant_id, positionRecord._create_user, positionRecord._create_stamp, positionRecord._update_user, positionRecord._update_stamp);

                        INSERT INTO ${helper.schemaName}.dashboard_item_position (dashboard_item, x, y, breakpoint, _tenant_id, _create_user, _create_stamp, _update_user, _update_stamp)
                        VALUES (positionRecord._id, positionRecord.x, positionRecord.y, 'lg', positionRecord._tenant_id, positionRecord._create_user, positionRecord._create_stamp, positionRecord._update_user, positionRecord._update_stamp);
                    END LOOP;
            END $$;
            `);
    },
});
