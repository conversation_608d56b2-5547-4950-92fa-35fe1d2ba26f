import { CustomSqlAction } from '@sage/xtrem-system';

export const migrateDashboardItemSize = new CustomSqlAction({
    description: 'Migrate dashboard item widths and heights',
    fixes: {
        tables: ['dashboard_item', 'dashboard_item_position'],
    },
    body: async helper => {
        await helper.executeSql(`
            DO $$ DECLARE
                dashboardItemRecord RECORD;
                BEGIN
                    -- There we are droping the constraints to avoid the constraint violation
                    SET CONSTRAINTS ALL DEFERRED;
                    FOR dashboardItemRecord IN (SELECT _id, w, h FROM ${helper.schemaName}.dashboard_item)
                    LOOP
                        UPDATE ${helper.schemaName}.dashboard_item_position
                        SET w = dashboardItemRecord.w, h = dashboardItemRecord.h
                        WHERE dashboard_item = dashboardItemRecord._id;
                    END LOOP;
            END $$;
            `);
    },
});
