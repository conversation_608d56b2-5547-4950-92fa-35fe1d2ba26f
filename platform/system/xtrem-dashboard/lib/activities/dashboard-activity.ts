import { Activity } from '@sage/xtrem-core';
import { Dashboard } from '../nodes/dashboard';

export const dashboardActivity = new Activity({
    description: 'Dashboard Activity',
    node: () => Dashboard,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        manage: [
            {
                operations: ['read', 'create', 'update', 'delete'],
                on: [() => Dashboard],
            },
        ],
    },
});
