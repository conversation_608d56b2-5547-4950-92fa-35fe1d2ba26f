import * as xtremAuthorization from '@sage/xtrem-authorization';
import {
    Context,
    DashboardManager as IDashboardManager,
    NodeCreateData,
    SystemError,
    UserAccess,
    UserInfo,
} from '@sage/xtrem-core';
import { Dashboard, DashboardItem, Dict } from '@sage/xtrem-shared';
import * as xtremSystem from '@sage/xtrem-system';
import { castArray, difference, isNil, isNumber, omit } from 'lodash';
import * as xtremDashboard from '../../index';

type Operation = 'lookup' | 'read' | 'create' | 'update' | 'delete';
export class DashboardManager implements IDashboardManager {
    // eslint-disable-next-line class-methods-use-this
    private readonly isValidNewId = (id: unknown): boolean => isNil(id) || (isNumber(Number(id)) && Number(id) < 0);

    // eslint-disable-next-line class-methods-use-this
    readonly serializeDashboard = async (
        dashboard: xtremDashboard.nodes.Dashboard,
        isTemplate = false,
    ): Promise<(Dashboard & { _id: string }) & { isTemplate: boolean }> => {
        const payload = await dashboard.$.payloadAsAny<Dashboard & { _id: string }>({
            withIds: true,
        });
        const items = await dashboard.items.toArray();
        const children = await Promise.all(
            items.map(async c => {
                const dashboardItem = await c.$.payloadAsAny<Promise<DashboardItem> & { _id: string }>({
                    withIds: true,
                });
                return {
                    ...dashboardItem,
                    settings: JSON.stringify(dashboardItem.settings || {}),
                };
            }),
        );
        return { ...payload, children, isTemplate };
    };

    // Try to find any existing record with the same title and an optional number at the end, it increments the number.
    // eslint-disable-next-line class-methods-use-this
    private readonly calculateNextTitle = async (
        context: Context,
        user: xtremSystem.nodes.User,
        group: string,
        currentTitle: string,
    ): Promise<string> => {
        // Here we try to get the title without any additional numbers at the end
        const strippedTitle = (currentTitle.match(/^(.*)(?:\s\d+)$/)?.[1] ?? currentTitle).trim();
        // Then we search for any matching titles with numbers at the end, ordered decreasing so we can just take the last one and increase it by one
        const queryResult = context.query(xtremDashboard.nodes.Dashboard, {
            filter: {
                owner: user,
                group,
                title: { _or: [{ _eq: strippedTitle }, { _regex: `^${strippedTitle} [0-9]+$`, _options: 'i' }] },
            },
            orderBy: { title: -1 },
            first: 1,
        });
        const resultLength = await queryResult.length;
        // If no identical previous titles were found, we just use the title as is
        if (resultLength === 0) {
            return strippedTitle;
        }
        // If we have a matching record, we use that
        const latestTitle = (await (await queryResult.at(0))?.title) || strippedTitle;
        // Grab the number from the end if it's there, if no number found, we set 1, so the first copy will start with 2
        const currentNumber = Number(latestTitle.match(/ (\d+)$/)?.[1] ?? 1);
        const nextNumber = currentNumber + 1;
        return `${strippedTitle} ${nextNumber}`;
    };

    // eslint-disable-next-line class-methods-use-this
    async getUserInfo(context: Context): Promise<UserInfo> {
        const userInfo = await context.user;
        if (!userInfo) {
            throw new SystemError('current context does not have a valid user');
        }
        return userInfo;
    }

    async getSelectedDashboard(context: Context, group = 'home'): Promise<Dashboard | null> {
        const userInfo = await this.getUserInfo(context);
        if (!userInfo._id) {
            return null;
        }
        const selectedDashboard = await context
            .query(xtremDashboard.nodes.Dashboard, {
                filter: { owner: { _id: userInfo._id }, group, isSelected: true },
                first: 1,
            })
            .at(0);
        if (selectedDashboard != null) {
            // The getters are not called by the serializer of `payload` so we need to do it.
            return this.serializeDashboard(selectedDashboard);
        }

        const firstDashboard = await context
            .query(xtremDashboard.nodes.Dashboard, {
                filter: { owner: { _id: userInfo._id }, group },
                first: 1,
                orderBy: { _createStamp: 1 },
            })
            .at(0);
        if (firstDashboard != null) {
            // The getters are not called by the serializer of `payload` so we need to do it.
            return this.serializeDashboard(firstDashboard);
        }

        // Find default dashboard template for the group
        const defaultDashboardTemplate = await context
            .query(xtremDashboard.nodes.Dashboard, { filter: { owner: null, group, isSelected: true }, first: 1 })
            .at(0);

        if (defaultDashboardTemplate != null) {
            // The getters are not called by the serializer of `payload` so we need to do it.
            return this.serializeDashboard(defaultDashboardTemplate, true);
        }
        return null;
    }

    setSelectedDashboard = async (
        context: Context,
        selectedDashboardId: string,
        group = 'home',
    ): Promise<Dashboard> => {
        await this.accessToOperationOrFail(['read', 'update'], context);
        const userInfo = await this.getUserInfo(context);
        const owner = await context.tryRead(xtremSystem.nodes.User, { _id: userInfo._id });
        if (!owner) {
            throw new Error('user not found');
        }
        if (isNil(selectedDashboardId)) {
            throw new Error('Selected dashboard id is undefined');
        }

        const selectedDashboard = await context.tryRead(xtremDashboard.nodes.Dashboard, {
            owner,
            group,
            _id: selectedDashboardId,
        });
        if (!selectedDashboard) {
            throw new Error('This dashboard cannot be selected.');
        }

        const result = context.query(xtremDashboard.nodes.Dashboard, {
            filter: { owner, group },
            forUpdate: true,
        });

        await result.forEach(async (record: xtremDashboard.nodes.Dashboard) => {
            // TODO: Optimize with bulk update
            await record.$.set({ isSelected: String(record._id) === selectedDashboardId });
            await record.$.save();
        });

        return this.serializeDashboard(selectedDashboard);
    };

    updateDashboardItemSettings = async (
        context: Context,
        dashboardItemId: string,
        settings: Object,
    ): Promise<void> => {
        await this.accessToOperationOrFail(['read', 'update'], context);
        const dashboardItem = await context.read(
            xtremDashboard.nodes.DashboardItem,
            { _id: dashboardItemId },
            { forUpdate: true },
        );
        if ((await (await dashboardItem.dashboard).owner)?._id !== (await context.user)?._id) {
            throw Error('User can only modify its own dashboards.');
        }
        await dashboardItem.$.set({ settings });
        await dashboardItem.$.save();
    };

    updateDashboardLayout = async (context: Context, dashboardId: string, layout: Dashboard): Promise<Dashboard> => {
        await this.accessToOperationOrFail(['read', 'create'], context);
        const contextUser: UserInfo | null = await context.user;
        if (!contextUser) {
            throw Error('No user found.');
        }
        const user = await context.read(xtremSystem.nodes.User, { _id: contextUser._id });
        const dashboard = await context.read(xtremDashboard.nodes.Dashboard, { _id: dashboardId }, { forUpdate: true });
        if (!user || !dashboard) {
            throw Error('No dashboard found for this user.');
        }

        const owner = await dashboard.owner;
        if (owner && owner._id !== contextUser._id) {
            throw Error('User can only update its own dashboards.');
        }

        // Read existing dashboard items from the database
        const existingDashboardItems = await dashboard.items.toArray();
        await Promise.all(existingDashboardItems.map(d => d.positions));
        // Get ids of both, already existing dashboard items and new coming dashboard items
        const existingIds: string[] = existingDashboardItems.map(i => String(i._id));
        const children = layout.children || [];
        const newIds = children.map(i => i._id);

        // check which ids are not in the new layout which were in the old layout
        // so we can remove them
        const idsToRemove = difference(existingIds, newIds);
        const idsToAdd = difference(newIds, existingIds);
        // If an ID passed in that is not in the existing set and not a negative temporary ID, then we should throw an exception
        const filteredIds = idsToAdd.filter(id => !this.isValidNewId(id));
        if (filteredIds.length > 0) {
            throw new Error(`Invalid widget IDs: ${filteredIds}`);
        }
        children.sort((a, b): number => Number(b._id) - Number(a._id));

        const updatedItems: NodeCreateData<
            xtremDashboard.nodes.DashboardItem & {
                _sortValue?: number;
                _action: 'create' | 'update' | 'delete';
            }
        >[] = [];

        // Remove dashboard items which are not in the new layout
        for (let i = 0; i < existingDashboardItems.length; i += 1) {
            const item = existingDashboardItems[i];
            // Remove type conversion once https://jira.sage.com/browse/XT-35992 is fixed
            if (idsToRemove.includes(String(item._id))) {
                updatedItems.push({
                    _id: item._id,
                    _action: 'delete',
                });
            }
        }

        // eslint-disable-next-line no-restricted-syntax
        for (const item of children) {
            // if the dashboard item coming from new layout does not have an id then it is a new item
            // so just added to layout.
            if (this.isValidNewId(item._id)) {
                const itemToInsert = omit(item, '_id');
                updatedItems.push({
                    ...itemToInsert,
                    positions: item.positions,
                    _action: 'create',
                });
            }

            // If the item already existed update it with new value coming from new layout.
            // Remove type conversion once https://jira.sage.com/browse/XT-35992 is fixed
            const existingDashboardItem = existingDashboardItems.find(ei => Number(ei._id) === Number(item._id));

            if (existingDashboardItem) {
                const positions = await item.positions.reduce(async (acc, position) => {
                    const element = await existingDashboardItem.positions.find(
                        async p => (await p.breakpoint) === position.breakpoint,
                    );
                    (await acc).push({
                        ...(element && { _id: element._id }),
                        x: position.x,
                        y: position.y,
                        w: position.w,
                        h: position.h,
                        breakpoint: position.breakpoint,
                        _action: element ? 'update' : 'create',
                    });
                    return acc;
                }, Promise.resolve<{ _id?: number; w: number; h: number; x: number; y: number; breakpoint: 'xxs' | 'xs' | 'sm' | 'md' | 'lg'; _action: 'create' | 'update' }[]>([]));
                updatedItems.push({
                    ...item,
                    positions,
                    _id: Number(item._id),
                    _action: 'update' as const,
                });
            }
        }

        if (!owner) {
            /**
             * If we have no owner, it's a factory template, so we duplicate it and set the owner to the current user
             * We cannot rely on "duplicate" to take care of _action properties so we handle itesms separately after duplicating.
             */
            const clonedDashboard = await dashboard.$.duplicate();
            await clonedDashboard.$.set({
                title: layout.title,
                description: layout.description,
                listIcon: layout.icon,
                // Discard _action properties and set negative IDs
                items: updatedItems.reduce(
                    (acc, curr) => {
                        if (curr._action === 'delete') {
                            return acc;
                        }
                        acc.push({
                            ...omit(curr, '_action'),
                            _id: curr._id ? -curr._id : undefined,
                            positions: curr.positions?.map(p => ({
                                ...omit(p, '_action'),
                                _id: p._id ? -p._id : undefined,
                            })),
                        });
                        return acc;
                    },
                    [] as NodeCreateData<
                        xtremDashboard.nodes.DashboardItem & {
                            _sortValue?: number;
                            _action: 'create' | 'update' | 'delete';
                        }
                    >[],
                ),
                owner: contextUser._id,
            });
            await clonedDashboard.$.save();
            dashboard.$.context.transaction.skipUnsavedCheck = true;
            return this.serializeDashboard(clonedDashboard);
        }
        await dashboard.$.set({
            title: layout.title,
            description: layout.description,
            listIcon: layout.icon,
            items: updatedItems,
        });
        // Finally save and return the updated dashboard
        await dashboard.$.save();
        return this.serializeDashboard(dashboard);
    };

    createDashboard = async (context: Context, group = 'home'): Promise<Dashboard> => {
        await this.accessToOperationOrFail('create', context);
        const user = await context.read(
            xtremSystem.nodes.User,
            { _id: (await context.user)?._id },
            { forUpdate: true },
        );
        const title = context.localize('@sage/xtrem-dashboard/create-dashboard-title', 'New dashboard');
        const nextTitle = await this.calculateNextTitle(context, user, group, title);

        const dashboard = await context.create(xtremDashboard.nodes.Dashboard, {
            owner: user,
            title: nextTitle,
            group,
        });

        await dashboard.$.save();
        return this.serializeDashboard(dashboard);
    };

    cloneDashboard = async (context: Context, dashboardId: string): Promise<Dashboard> => {
        await this.accessToOperationOrFail(['read', 'create'], context);
        if (!dashboardId) {
            throw Error('Dashboard id is required.');
        }
        const contextUser = await context.user;
        if (!contextUser) {
            throw Error('No user found.');
        }
        const user = await context.read(xtremSystem.nodes.User, { _id: contextUser._id }, { forUpdate: true });

        const dashboard = await context.read(xtremDashboard.nodes.Dashboard, { _id: dashboardId });
        const owner = await dashboard.owner;
        const group = await dashboard.group;
        if (owner && owner._id !== contextUser._id) {
            throw Error('User can only clone its own dashboards or factory ones.');
        }
        const clonedDashboard = await dashboard.$.duplicate();
        const dashboardTitle = await clonedDashboard.title;
        const nextTitle = await this.calculateNextTitle(context, user, group, dashboardTitle);
        await clonedDashboard.$.set({ owner: contextUser._id, title: nextTitle });
        await clonedDashboard.$.save();
        return this.serializeDashboard(clonedDashboard);
    };

    deleteDashboard = async (context: Context, dashboardId: string): Promise<void> => {
        await this.accessToOperationOrFail('delete', context);
        if (!dashboardId) {
            throw Error('dashboardId is not defined.');
        }

        const dashboard = await context.read(xtremDashboard.nodes.Dashboard, { _id: dashboardId }, { forUpdate: true });

        const user = await context.read(
            xtremSystem.nodes.User,
            { _id: (await context.user)?._id },
            { forUpdate: true },
        );

        if (!user) {
            throw Error('user is not defined');
        }

        const owner = await dashboard.owner;

        // If there's no owner it's a factory template.
        if (!owner) {
            throw Error('Factory templates cannot be deleted.');
        }

        if (owner._id !== user._id) {
            throw Error('User can only delete its own dashboards.');
        }

        await dashboard.$.delete();
    };

    // eslint-disable-next-line class-methods-use-this
    addDashboardItem = (): Promise<void> => {
        throw new Error('Method not implemented.');
    };

    // eslint-disable-next-line class-methods-use-this
    getDashboardList = async (context: Context, group = 'home'): Promise<Dict<string>> => {
        const owner = (await context.user)?._id;
        if (!owner) {
            throw new Error('No user found');
        }
        const dashboards = await context
            .query(xtremDashboard.nodes.Dashboard, { filter: { owner, group }, orderBy: { _createStamp: 1 } })
            .toArray();
        const resolvedTitles = await Promise.all(
            dashboards.map(async currentValue => {
                return { _id: currentValue._id, title: await currentValue.title };
            }),
        );
        return resolvedTitles.reduce<Dict<string>>(
            (previousValue, currentValue) => ({ ...previousValue, [currentValue._id]: currentValue.title }),
            {},
        );
    };

    // eslint-disable-next-line class-methods-use-this
    getFactoryDashboardList = async (context: Context, group = 'home'): Promise<Dashboard[]> => {
        const dashboards = await context
            .query(xtremDashboard.nodes.Dashboard, { filter: { owner: null, group } })
            .toArray();
        return dashboards as unknown as Dashboard[];
    };

    // eslint-disable-next-line class-methods-use-this
    getWidgetCategory = async (
        context: Context,
        widget: { _id: string; id: string },
    ): Promise<{ key: string; title: string }> => {
        const category = await context.tryRead(xtremDashboard.nodes.WidgetCategory, { id: widget.id });
        if (!category) {
            // If the category doesn't exist, return just the id as a label.
            return { key: widget.id, title: widget.id };
        }

        return {
            key: await category.id,
            title: await category.title,
        };
    };

    // eslint-disable-next-line class-methods-use-this
    getWidgetCategories = async (context: Context): Promise<{ key: string; title: string }[]> => {
        const categories = await context.query(xtremDashboard.nodes.WidgetCategory).toArray();
        if (!categories) {
            return [];
        }
        const resolvedWidgetCategories = await Promise.all(
            categories.map(async currentValue => ({ key: await currentValue.id, title: await currentValue.title })),
        );
        return resolvedWidgetCategories;
    };

    canEditDashboards = (context: Context): Promise<boolean> => {
        return this.accessToOperation('update', context);
    };

    // eslint-disable-next-line class-methods-use-this
    accessToOperation = async (operation: Operation | Array<Operation>, context: Context): Promise<boolean> => {
        const operations = castArray(operation);
        const results = await Promise.all(
            operations.map(async o => {
                const access: UserAccess = await xtremAuthorization.services.sysAccessRightsManager.getUserAccessFor(
                    context,
                    'Dashboard',
                    o,
                );
                return access && access.status === 'authorized';
            }),
        );
        return results.every(v => v);
    };

    accessToOperationOrFail = async (operation: Operation | Array<Operation>, context: Context): Promise<void> => {
        const access = await this.accessToOperation(operation, context);
        if (!access) {
            throw new Error('Unauthorized');
        }
    };
}
