{"@sage/xtrem-dashboard/activity__dashboard_activity__name": "", "@sage/xtrem-dashboard/create-dashboard-title": "New dashboard", "@sage/xtrem-dashboard/data_types__breakpoint_enum__name": "", "@sage/xtrem-dashboard/enums__breakpoint__lg": "", "@sage/xtrem-dashboard/enums__breakpoint__md": "", "@sage/xtrem-dashboard/enums__breakpoint__sm": "", "@sage/xtrem-dashboard/enums__breakpoint__xs": "", "@sage/xtrem-dashboard/enums__breakpoint__xxs": "", "@sage/xtrem-dashboard/nodes__dashboard__asyncMutation__asyncExport": "", "@sage/xtrem-dashboard/nodes__dashboard__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-dashboard/nodes__dashboard__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-dashboard/nodes__dashboard__node_name": "Dashboard", "@sage/xtrem-dashboard/nodes__dashboard__property__description": "Description", "@sage/xtrem-dashboard/nodes__dashboard__property__group": "", "@sage/xtrem-dashboard/nodes__dashboard__property__id": "", "@sage/xtrem-dashboard/nodes__dashboard__property__isSelected": "", "@sage/xtrem-dashboard/nodes__dashboard__property__items": "Items", "@sage/xtrem-dashboard/nodes__dashboard__property__listIcon": "List icon", "@sage/xtrem-dashboard/nodes__dashboard__property__owner": "Owner", "@sage/xtrem-dashboard/nodes__dashboard__property__title": "Title", "@sage/xtrem-dashboard/nodes__dashboard_item__asyncMutation__asyncExport": "", "@sage/xtrem-dashboard/nodes__dashboard_item__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-dashboard/nodes__dashboard_item__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-dashboard/nodes__dashboard_item__node_name": "Dashboard item", "@sage/xtrem-dashboard/nodes__dashboard_item__property__dashboard": "Dashboard", "@sage/xtrem-dashboard/nodes__dashboard_item__property__positions": "", "@sage/xtrem-dashboard/nodes__dashboard_item__property__settings": "Settings", "@sage/xtrem-dashboard/nodes__dashboard_item__property__type": "", "@sage/xtrem-dashboard/nodes__dashboard_item_position__asyncMutation__asyncExport": "", "@sage/xtrem-dashboard/nodes__dashboard_item_position__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-dashboard/nodes__dashboard_item_position__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-dashboard/nodes__dashboard_item_position__node_name": "", "@sage/xtrem-dashboard/nodes__dashboard_item_position__property__breakpoint": "", "@sage/xtrem-dashboard/nodes__dashboard_item_position__property__dashboardItem": "", "@sage/xtrem-dashboard/nodes__dashboard_item_position__property__h": "", "@sage/xtrem-dashboard/nodes__dashboard_item_position__property__w": "", "@sage/xtrem-dashboard/nodes__dashboard_item_position__property__x": "", "@sage/xtrem-dashboard/nodes__dashboard_item_position__property__y": "", "@sage/xtrem-dashboard/nodes__widget_category__asyncMutation__asyncExport": "", "@sage/xtrem-dashboard/nodes__widget_category__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-dashboard/nodes__widget_category__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-dashboard/nodes__widget_category__node_name": "Widget category", "@sage/xtrem-dashboard/nodes__widget_category__property__id": "", "@sage/xtrem-dashboard/nodes__widget_category__property__setupId": "Setup ID", "@sage/xtrem-dashboard/nodes__widget_category__property__title": "Title", "@sage/xtrem-dashboard/package__name": "", "@sage/xtrem-dashboard/pages__generic_tile_widget_settings____title": "Widget settings", "@sage/xtrem-dashboard/pages__generic_tile_widget_settings__aggregatedProperty____helperText": "This property is used by the aggregation function to calculate the tile value.", "@sage/xtrem-dashboard/pages__generic_tile_widget_settings__aggregatedProperty____title": "Aggregated property", "@sage/xtrem-dashboard/pages__generic_tile_widget_settings__aggregationMethod____helperText": "Determines the calculation function of the tile value", "@sage/xtrem-dashboard/pages__generic_tile_widget_settings__aggregationMethod____title": "Aggregation method", "@sage/xtrem-dashboard/pages__generic_tile_widget_settings__aggregationSection____title": "Data aggregation", "@sage/xtrem-dashboard/pages__generic_tile_widget_settings__color____helperText": "This is to be replaced with a color picker", "@sage/xtrem-dashboard/pages__generic_tile_widget_settings__color____title": "Color", "@sage/xtrem-dashboard/pages__generic_tile_widget_settings__entity____helperText": "The main entity which is used to calculate the tile value", "@sage/xtrem-dashboard/pages__generic_tile_widget_settings__entity____title": "Entity", "@sage/xtrem-dashboard/pages__generic_tile_widget_settings__filters____columns__title": "Property", "@sage/xtrem-dashboard/pages__generic_tile_widget_settings__filters____columns__title__2": "Method", "@sage/xtrem-dashboard/pages__generic_tile_widget_settings__filters____columns__title__3": "Filter value", "@sage/xtrem-dashboard/pages__generic_tile_widget_settings__filters____columns__title__4": "Filter value", "@sage/xtrem-dashboard/pages__generic_tile_widget_settings__filters____columns__title__5": "Filter value", "@sage/xtrem-dashboard/pages__generic_tile_widget_settings__filters____columns__title__6": "Filter value", "@sage/xtrem-dashboard/pages__generic_tile_widget_settings__filters____columns__title__7": "Filter value", "@sage/xtrem-dashboard/pages__generic_tile_widget_settings__filters____dropdownActions__title": "Remove", "@sage/xtrem-dashboard/pages__generic_tile_widget_settings__filters____title": "Filters", "@sage/xtrem-dashboard/pages__generic_tile_widget_settings__filterSection____title": "Filters", "@sage/xtrem-dashboard/pages__generic_tile_widget_settings__filtersMessage____content": "The filters restrict the items the aggregation functions run on.", "@sage/xtrem-dashboard/pages__generic_tile_widget_settings__icon____helperText": "The icon of the tile", "@sage/xtrem-dashboard/pages__generic_tile_widget_settings__icon____title": "Icon", "@sage/xtrem-dashboard/pages__generic_tile_widget_settings__save____title": "Save settings", "@sage/xtrem-dashboard/pages__generic_tile_widget_settings__section____title": "Basic settings", "@sage/xtrem-dashboard/pages__generic_tile_widget_settings__title____helperText": "The title is displayed below the value of the tile.", "@sage/xtrem-dashboard/pages__generic_tile_widget_settings__title____title": "Widget title", "@sage/xtrem-dashboard/pages__property_browser____title": "Property selection", "@sage/xtrem-dashboard/pages__property_browser__filtersMessage____content": "This dialog enables you to deep search properties in your data structure.", "@sage/xtrem-dashboard/pages__property_browser__ok____title": "OK", "@sage/xtrem-dashboard/pages__property_browser__properties____columns__title": "Property", "@sage/xtrem-dashboard/permission__manage__name": "", "@sage/xtrem-dashboard/permission__read__name": ""}