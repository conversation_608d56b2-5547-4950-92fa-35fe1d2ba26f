import * as ui from '@sage/xtrem-ui';
import {
    getFilterableNodeProperties,
    GraphqlTypes,
    PATH_JOIN_TOKEN,
    FilterableNodeProperty,
} from '../client-functions/generic-dashboard-utils';
import { Dict } from '@sage/xtrem-shared';

@ui.decorators.page<PropertyBrowser>({
    title: 'Property selection',
    mode: 'tabs',
    isTransient: true,
    async onLoad() {
        let contextNode = String(this.$.queryParameters.contextNode);
        const value = this.$.queryParameters.value ? String(this.$.queryParameters.value) : '';
        let options = await getFilterableNodeProperties(this, contextNode);
        if (value) {
            const propertiesValue = [];
            const pathComponents = value.split(PATH_JOIN_TOKEN);

            for (let i = 0; i < pathComponents.length; i += 1) {
                const _id = `-${i + 1}`;
                const pathComponent = pathComponents[i];
                const type = options[pathComponent];
                if (type.referredNode && type.type === GraphqlTypes.IntReference) {
                    propertiesValue.push({ _id, property: pathComponent, type, options });
                    contextNode = type.referredNode;
                    options = await getFilterableNodeProperties(this, contextNode);
                } else {
                    propertiesValue.push({ _id, property: pathComponent, type });
                    break;
                }
            }
            this.properties.value = propertiesValue;
        } else {
            this.properties.value = [{ _id: '-1', options }];
        }
    },
    businessActions() {
        return [this.ok];
    },
})
export class PropertyBrowser extends ui.Page {
    private topLevelProperties: Dict<FilterableNodeProperty>;

    @ui.decorators.section<PropertyBrowser>({
        isTitleHidden: true,
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<PropertyBrowser>({
        parent() {
            return this.mainSection;
        },
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.messageField<PropertyBrowser>({
        parent() {
            return this.mainBlock;
        },
        content: 'This dialog enables you to deep search properties in your data structure.',
    })
    filtersMessage: ui.fields.Message;

    @ui.decorators.podCollectionField<PropertyBrowser>({
        parent() {
            return this.mainBlock;
        },
        canAddRecord: false,
        fetchesDefaults: false,
        isTitleHidden: true,
        isTransient: true,
        recordWidth: 'extra-large',
        columns: [
            ui.nestedFields.select<PropertyBrowser>({
                bind: 'property' as any,
                title: 'Property',
                width: 'small',
                isTitleHidden: true,
                isReadOnly(value: any, rowValue: any) {
                    return this.properties.value.pop()!._id !== rowValue._id;
                },
                async onChange(_id: any, rowValue: any) {
                    if (rowValue.property) {
                        const type = rowValue.options[rowValue.property];
                        if (type.referredNode && type.type === GraphqlTypes.IntReference) {
                            const nextOptions = await getFilterableNodeProperties(this, type.referredNode);
                            this.properties.addRecord({ options: nextOptions, type });
                        }
                        this.properties.addOrUpdateRecordValue({ ...rowValue, type });
                    }
                },
                options(_id: any, rowValue: any) {
                    return Object.keys(rowValue.options);
                },
            }),
            ui.nestedFields.icon<PropertyBrowser>({
                bind: 'remove' as any,
                width: 'small',
                isTitleHidden: true,
                map() {
                    return 'delete';
                },
                isHidden(value: any, rowValue: any) {
                    const properties = this.properties.value;
                    return properties.length > 1 && properties.pop()!._id !== rowValue._id;
                },
                onClick(_id: string) {
                    this.properties.removeRecord(_id);
                },
            }),
        ],
    })
    properties: ui.fields.PodCollection;

    @ui.decorators.pageAction<PropertyBrowser>({
        title: 'OK',
        async onClick() {
            const properties = this.properties.value;
            const value = properties.map((p: any) => p.property).join(PATH_JOIN_TOKEN);
            const propertyDetails = this.properties.value.pop();
            this.$.finish({
                value,
                type: propertyDetails.type,
            });
        },
    })
    ok: ui.PageAction;
}
