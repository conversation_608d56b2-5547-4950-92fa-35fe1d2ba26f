import { Dict } from '@sage/xtrem-shared';
import * as ui from '@sage/xtrem-ui';
import {
    FilterableNodeProperty,
    getAvailableFiltersForPropertyType,
    getFilterableNodeProperties,
    getNodeList,
} from '../client-functions/generic-dashboard-utils';

export interface FilterEntry {
    _id: string;
    property: string;
    method?: string;
    textFilterValue?: number;
    numericFilterValue?: number;
}

@ui.decorators.page<GenericTileWidgetSettings>({
    title: 'Widget settings',
    mode: 'tabs',
    isTransient: true,
    async onLoad() {
        const nodes = await getNodeList(this);
        this.entity.options = nodes;
        this.entity.value = this.$.queryParameters.entity ? String(this.$.queryParameters.entity) : null;
        this.filterSection.isHidden = !this.entity.value;
        this.aggregationSection.isHidden = !this.entity.value;
        if (this.entity.value) {
            this.nodeProperties = await getFilterableNodeProperties(this, this.entity.value);
        }

        this.aggregationMethod.value = this.$.queryParameters.aggregationMethod
            ? String(this.$.queryParameters.aggregationMethod)
            : 'count';
        this.aggregatedProperty.value = this.$.queryParameters.aggregatedProperty
            ? String(this.$.queryParameters.aggregatedProperty)
            : null;
        this.title.value = this.$.queryParameters.title ? String(this.$.queryParameters.title) : null;
        this.icon.value = this.$.queryParameters.icon ? String(this.$.queryParameters.icon) : null;
        this.iconPreview.value = this.$.queryParameters.icon ? String(this.$.queryParameters.icon) : null;
        this.color.value = this.$.queryParameters.color ? String(this.$.queryParameters.color) : null;
        this.filters.value = this.$.queryParameters.filters ? JSON.parse(String(this.$.queryParameters.filters)) : [];
    },
    businessActions() {
        return [this.save];
    },
})
export class GenericTileWidgetSettings extends ui.Page {
    private nodeProperties: Dict<FilterableNodeProperty> = {};

    @ui.decorators.section<GenericTileWidgetSettings>({
        isTitleHidden: true,
        title: 'Basic settings',
    })
    section: ui.containers.Section;

    @ui.decorators.block<GenericTileWidgetSettings>({
        parent() {
            return this.section;
        },
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.textField<GenericTileWidgetSettings>({
        parent() {
            return this.fieldBlock;
        },
        isFullWidth: true,
        title: 'Widget title',
        helperText: 'The title is displayed under the value of the tile',
        isMandatory: true,
    })
    title: ui.fields.Text;

    @ui.decorators.selectField<GenericTileWidgetSettings>({
        parent() {
            return this.fieldBlock;
        },
        isFullWidth: true,
        isMandatory: true,
        title: 'Entity',
        async onChange() {
            if (this.entity.value) {
                this.filterSection.isHidden = false;
                this.aggregationSection.isHidden = false;
                this.nodeProperties = await getFilterableNodeProperties(this, this.entity.value);
                this.filters.value = [];
            } else {
                this.filterSection.isHidden = true;
                this.aggregationSection.isHidden = true;
                this.nodeProperties = {};
                this.filters.value = [];
            }
        },
        helperText: 'The main entity which is used to calculate the tile value',
    })
    entity: ui.fields.Select;

    @ui.decorators.selectField<GenericTileWidgetSettings>({
        parent() {
            return this.fieldBlock;
        },
        title: 'Icon',
        width: 'small',
        isMandatory: true,
        options: [
            'add',
            'alert',
            'analysis',
            'arrow_down',
            'arrow_left',
            'arrow_left_boxed',
            'arrow_left_right_small',
            'arrow_left_small',
            'arrow_right',
            'arrow_right_small',
            'arrow_up',
            'attach',
            'bank',
            'basket',
            'basket_with_squares',
            'bin',
            'blocked',
            'blocked_square',
            'block_arrow_right',
            'bold',
            'boxed_shapes',
            'bulk_destroy',
            'bullet_list',
            'bullet_list_dotted',
            'bullet_list_numbers',
            'business',
            'calendar',
            'calendar_today',
            'call',
            'camera',
            'card_view',
            'caret_down',
            'caret_left',
            'caret_right',
            'caret_up',
            'caret_large_down',
            'caret_large_left',
            'caret_large_right',
            'caret_large_up',
            'cart',
            'chat',
            'chart_bar',
            'chart_line',
            'chart_pie',
            'chat_notes',
            'chevron_down',
            'chevron_left',
            'chevron_right',
            'chevron_up',
            'chevron_down_thick',
            'chevron_left_thick',
            'chevron_right_thick',
            'chevron_up_thick',
            'circle_with_dots',
            'circles_connection',
            'clock',
            'close',
            'coins',
            'collaborate',
            'computer_clock',
            'connect',
            'copy',
            'credit_card',
            'credit_card_slash',
            'cross',
            'cross_circle',
            'csv',
            'delete',
            'delivery',
            'disputed',
            'disconnect',
            'document_right_align',
            'document_tick',
            'document_vertical_lines',
            'download',
            'drag',
            'drag_vertical',
            'draft',
            'dropdown',
            'duplicate',
            'edit',
            'edited',
            'email',
            'email_switch',
            'ellipsis_horizontal',
            'ellipsis_vertical',
            'error',
            'error_square',
            'euro',
            'expand',
            'factory',
            'favourite',
            'favourite_lined',
            'fax',
            'feedback',
            'file_excel',
            'file_generic',
            'file_image',
            'file_pdf',
            'file_word',
            'files_leaning',
            'filter',
            'filter_new',
            'fit_height',
            'fit_width',
            'flag',
            'folder',
            'gift',
            'graph',
            'grid',
            'help',
            'hide',
            'home',
            'image',
            'in_progress',
            'in_transit',
            'individual',
            'info',
            'italic',
            'key',
            'ledger',
            'ledger_arrow_left',
            'ledger_arrow_right',
            'link',
            'list_view',
            'locked',
            'location',
            'logout',
            'lookup',
            'marker',
            'message',
            'messages',
            'minus',
            'minus_large',
            'mobile',
            'money_bag',
            'pause',
            'pause_circle',
            'pdf',
            'people',
            'people_switch',
            'person',
            'person_info',
            'person_tick',
            'phone',
            'play',
            'play_circle',
            'plus',
            'plus_large',
            'pound',
            'print',
            'progress',
            'progressed',
            'question',
            'refresh',
            'refresh_clock',
            'remove',
            'sage_coin',
            'save',
            'scan',
            'search',
            'services',
            'settings',
            'share',
            'shop',
            'sort_down',
            'sort_up',
            'spanner',
            'split',
            'split_container',
            'square_dot',
            'squares_nine',
            'stacked_boxes',
            'stacked_squares',
            'submitted',
            'sync',
            'tag',
            'three_boxes',
            'tick',
            'tick_circle',
            'unlocked',
            'upload',
            'uploaded',
            'video',
            'view',
            'warning',
        ],
        helperText: 'The icon of the tile',
        onChange() {
            this.iconPreview.value = this.icon.value;
        },
    })
    icon: ui.fields.Select;

    @ui.decorators.iconField<GenericTileWidgetSettings>({
        parent() {
            return this.fieldBlock;
        },
        width: 'small',
        isTitleHidden: true,
    })
    iconPreview: ui.fields.Icon;

    @ui.decorators.textField<GenericTileWidgetSettings>({
        title: 'Color',
        parent() {
            return this.fieldBlock;
        },
        isFullWidth: true,
        helperText: 'This is to be replaced with a color picker',
    })
    color: ui.fields.Text;

    @ui.decorators.section<GenericTileWidgetSettings>({
        title: 'Data aggregation',
        isTitleHidden: true,
        isHidden: true,
    })
    aggregationSection: ui.containers.Section;

    @ui.decorators.block<GenericTileWidgetSettings>({
        parent() {
            return this.aggregationSection;
        },
    })
    aggregationBlock: ui.containers.Block;

    @ui.decorators.selectField<GenericTileWidgetSettings>({
        parent() {
            return this.aggregationBlock;
        },
        isFullWidth: true,
        isMandatory: true,
        title: 'Aggregation method',
        options: ['count', 'sum', 'max', 'avg', 'min'],
        onChange() {
            this.aggregatedProperty.isHidden = this.aggregationMethod.value === 'count';
        },
        helperText: 'Determines the calculation function of the tile value',
    })
    aggregationMethod: ui.fields.Select;

    @ui.decorators.selectField<GenericTileWidgetSettings>({
        parent() {
            return this.aggregationBlock;
        },
        isHidden: true,
        isFullWidth: true,
        title: 'Aggregated Property',
        options() {
            return Object.keys(this.nodeProperties);
        },
        onChange() {
            this.iconPreview.value = this.icon.value;
        },
        helperText: 'This property is used by the aggregation function to calcualte the tile value.',
    })
    aggregatedProperty: ui.fields.Select;

    @ui.decorators.section<GenericTileWidgetSettings>({
        isTitleHidden: true,
        title: 'Filters',
        isHidden: true,
    })
    filterSection: ui.containers.Section;

    @ui.decorators.block<GenericTileWidgetSettings>({
        parent() {
            return this.filterSection;
        },
    })
    filterBlock: ui.containers.Block;

    @ui.decorators.messageField<GenericTileWidgetSettings>({
        parent() {
            return this.filterBlock;
        },
        content: 'The filters restrict the items that the aggregation functions run on.',
    })
    filtersMessage: ui.fields.Message;

    @ui.decorators.podCollectionField({
        parent() {
            return this.filterBlock;
        },
        canAddRecord: true,
        title: 'Filters',
        fetchesDefaults: false,
        isTitleHidden: true,
        isTransient: true,
        recordWidth: 'extra-large',
        recordTitle(_value: any, rowValue: any) {
            return rowValue?.property || 'N/A';
        },
        dropdownActions: [
            {
                icon: 'delete',
                title: 'Remove',
                onClick(rowId: any) {
                    this.filters.removeRecord(rowId);
                },
            },
        ],
        columns: [
            ui.nestedFields.text({
                bind: 'property' as any,
                title: 'Property',
                isReadOnly: true,
            }),
            ui.nestedFields.icon({
                bind: 'editIcon' as any,
                map() {
                    return 'edit';
                },
                async onClick(value: any, rowValue: any) {
                    const result = await this.$.dialog.page(
                        '@sage/xtrem-dashboard/PropertyBrowser',
                        { contextNode: this.entity.value, value: rowValue.property },
                        {
                            rightAligned: true,
                            size: 'medium',
                        },
                    );

                    const filterOptions = getAvailableFiltersForPropertyType(result.type.type);
                    this.filters.addOrUpdateRecordValue({
                        _id: rowValue._id,
                        property: result.value,
                        propertyType: result.type.type,
                        enumValues: result.type.enumValues,
                        method: filterOptions[0],
                    });
                },
            }),
            ui.nestedFields.select({
                bind: 'method' as any,
                title: 'Method',
                width: 'small',
                isFullWidth: true,
                isReadOnly(value: any, rowValue: any) {
                    if (
                        !rowValue.property ||
                        !rowValue.propertyType ||
                        getAvailableFiltersForPropertyType(rowValue.propertyType).length < 2
                    ) {
                        return true;
                    }
                    return false;
                },
                options(value: any, rowValue: any) {
                    return getAvailableFiltersForPropertyType(rowValue.propertyType) as string[];
                },
            }),
            ui.nestedFields.text({
                bind: 'textFilterValue' as any,
                title: 'Filter Value',
                width: 'small',
                isFullWidth: true,
                isHidden(value: any, rowValue: any) {
                    if (rowValue.propertyType && rowValue.propertyType === 'String') {
                        return false;
                    }
                    return true;
                },
            }),
            ui.nestedFields.numeric({
                bind: 'numericFilterValue' as any,
                title: 'Filter Value',
                width: 'small',
                isFullWidth: true,
                scale(value: any, rowValue: any) {
                    if (rowValue.propertyType === 'Decimal') {
                        return 4;
                    }

                    return 0;
                },
                isHidden(value: any, rowValue: any) {
                    if (!rowValue.property || !rowValue.propertyType) {
                        return true;
                    }
                    if (rowValue.propertyType === 'IntOrString') {
                        return false;
                    }
                    if (rowValue.propertyType === 'Decimal') {
                        return false;
                    }

                    return true;
                },
            }),
            ui.nestedFields.date({
                bind: 'dateFilterValue' as any,
                title: 'Filter Value',
                width: 'small',
                isFullWidth: true,
                isHidden(value: any, rowValue: any) {
                    if (rowValue.property && rowValue.propertyType === 'Date') {
                        return false;
                    }

                    return true;
                },
            }),
            ui.nestedFields.checkbox({
                bind: 'booleanFilterValue' as any,
                title: 'Filter Value',
                width: 'small',
                isFullWidth: true,
                isReversed: true,
                isHidden(value: any, rowValue: any) {
                    if (rowValue.property && rowValue.propertyType === 'Boolean') {
                        return false;
                    }

                    return true;
                },
            }),
            ui.nestedFields.select({
                bind: 'selectFilterValue' as any,
                title: 'Filter Value',
                width: 'small',
                isFullWidth: true,
                options(value: any, rowValue: any) {
                    if (rowValue.propertyType && rowValue.propertyType.indexOf('_EnumInput') !== -1) {
                        return rowValue.enumValues || [];
                    }
                    return [];
                },
                isHidden(value: any, rowValue: any) {
                    if (!rowValue.property || !rowValue.propertyType) {
                        return true;
                    }
                    if (rowValue.propertyType.indexOf('_EnumInput') !== -1) {
                        return false;
                    }

                    return true;
                },
            }),
        ],
    })
    filters: ui.fields.PodCollection;

    @ui.decorators.pageAction<GenericTileWidgetSettings>({
        title: 'Save Settings',
        async onClick() {
            const result = await this.$.page.validate();
            if (result.length === 0) {
                this.$.finish({
                    entity: this.entity.value,
                    aggregationMethod: this.aggregationMethod.value,
                    aggregatedProperty: this.aggregatedProperty.value,
                    title: this.title.value,
                    icon: this.icon.value,
                    color: this.color.value,
                    filters: JSON.stringify(this.filters.value),
                });
            } else {
                this.$.showToast('Validation errors', { type: 'error' });
            }
        },
    })
    save: ui.PageAction;
}
