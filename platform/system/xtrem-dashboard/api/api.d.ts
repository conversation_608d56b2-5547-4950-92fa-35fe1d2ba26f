declare module '@sage/xtrem-dashboard-api-partial' {
    import type { Package as SageXtremAuthorization$Package } from '@sage/xtrem-authorization-api';
    import type { Package as SageXtremCommunication$Package } from '@sage/xtrem-communication-api';
    import type { Package as SageXtremSystem$Package, SysVendor, User } from '@sage/xtrem-system-api';
    import type {
        AggregateQueryOperation,
        AggregateReadOperation,
        AsyncOperation,
        ClientNode,
        ClientNodeInput,
        CreateOperation,
        DeleteOperation,
        DuplicateOperation,
        GetDefaultsOperation,
        GetDuplicateOperation,
        QueryOperation,
        ReadOperation,
        UpdateByIdOperation,
        UpdateOperation,
        integer,
    } from '@sage/xtrem-client';
    export interface Breakpoint$Enum {
        xxs: 0;
        xs: 1;
        sm: 2;
        md: 3;
        lg: 4;
    }
    export type Breakpoint = keyof Breakpoint$Enum;
    export interface Dashboard extends ClientNode {
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        title: string;
        description: string;
        listIcon: string;
        owner: User;
        group: string;
        isSelected: boolean;
    }
    export interface DashboardInput extends ClientNodeInput {
        _vendor?: integer | string;
        title?: string;
        description?: string;
        listIcon?: string;
        owner?: integer | string;
        group?: string;
        isSelected?: boolean | string;
    }
    export interface DashboardBinding extends ClientNode {
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        title: string;
        description: string;
        listIcon: string;
        owner: User;
        group: string;
        isSelected: boolean;
    }
    export interface Dashboard$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface Dashboard$Lookups {
        _vendor: QueryOperation<SysVendor>;
        owner: QueryOperation<User>;
    }
    export interface Dashboard$Operations {
        query: QueryOperation<Dashboard>;
        read: ReadOperation<Dashboard>;
        aggregate: {
            read: AggregateReadOperation<Dashboard>;
            query: AggregateQueryOperation<Dashboard>;
        };
        create: CreateOperation<DashboardInput, Dashboard>;
        getDuplicate: GetDuplicateOperation<Dashboard>;
        duplicate: DuplicateOperation<string, DashboardInput, Dashboard>;
        update: UpdateOperation<DashboardInput, Dashboard>;
        updateById: UpdateByIdOperation<DashboardInput, Dashboard>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: Dashboard$AsyncOperations;
        lookups(dataOrId: string | { data: DashboardInput }): Dashboard$Lookups;
        getDefaults: GetDefaultsOperation<Dashboard>;
    }
    export interface WidgetCategory extends ClientNode {
        _updateUser: User;
        _createUser: User;
        title: string;
        id: string;
    }
    export interface WidgetCategoryInput extends ClientNodeInput {
        title?: string;
        id?: string;
    }
    export interface WidgetCategoryBinding extends ClientNode {
        _updateUser: User;
        _createUser: User;
        title: string;
        id: string;
    }
    export interface WidgetCategory$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface WidgetCategory$Operations {
        query: QueryOperation<WidgetCategory>;
        read: ReadOperation<WidgetCategory>;
        aggregate: {
            read: AggregateReadOperation<WidgetCategory>;
            query: AggregateQueryOperation<WidgetCategory>;
        };
        create: CreateOperation<WidgetCategoryInput, WidgetCategory>;
        getDuplicate: GetDuplicateOperation<WidgetCategory>;
        update: UpdateOperation<WidgetCategoryInput, WidgetCategory>;
        updateById: UpdateByIdOperation<WidgetCategoryInput, WidgetCategory>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: WidgetCategory$AsyncOperations;
        getDefaults: GetDefaultsOperation<WidgetCategory>;
    }
    export interface Package {
        '@sage/xtrem-dashboard/Dashboard': Dashboard$Operations;
        '@sage/xtrem-dashboard/WidgetCategory': WidgetCategory$Operations;
    }
    export interface GraphApi
        extends Package,
            SageXtremAuthorization$Package,
            SageXtremCommunication$Package,
            SageXtremSystem$Package {}
}
declare module '@sage/xtrem-dashboard-api' {
    export type * from '@sage/xtrem-dashboard-api-partial';
}
declare module '@sage/xtrem-authorization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-dashboard-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-communication-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-dashboard-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-dashboard-api';
    export interface GraphApi extends GraphApiExtension {}
}
