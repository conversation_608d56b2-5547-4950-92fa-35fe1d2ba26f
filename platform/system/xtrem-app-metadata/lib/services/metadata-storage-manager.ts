/* eslint-disable class-methods-use-this */
/* eslint-disable @typescript-eslint/no-unused-vars */
import {
    AnyR<PERSON>ord,
    AnyRecordWithId,
    AnyV<PERSON>ue,
    AsyncArrayReader,
    AsyncGenericReader,
    AsyncReader,
    Context,
    DateValue,
    Dict,
    Extend,
    ExternalStorageManager,
    Logger,
    Node,
    NodeExternalQueryOptions,
    NodeFactory,
    OrderBy,
    OrderByClause,
    Property,
    PropertyAndValue,
    RecordPaging,
    Uuid,
    ValidationContext,
    asyncArray,
    fastHash,
    friendlyJsonParse,
    funnel,
    isScalar,
} from '@sage/xtrem-core';
import { DateRange, Datetime, DatetimeRange, Time } from '@sage/xtrem-date-time';
import { MetadataHelper } from './metadata-helper';
import { MetaArtifacts } from './metadata-interfaces';

enum StorageStatus {
    notStarted,
    done,
    reload,
}

export class MetadataStorageManager<T extends Node> implements ExternalStorageManager<T> {
    factory: NodeFactory;

    private readonly logger = Logger.getLogger(__filename, 'x3-metadata');

    private notAllowedError(operation: string): Error {
        return new Error(`${this.factory.name}:${operation} is not allowed`);
    }

    private get naturalKey(): string[] {
        if (!this.factory.naturalKey) {
            throw new Error(`${this.factory.name}: natural key is not defined`);
        }

        return this.factory.naturalKey;
    }

    insert(_node: Extend<T>, _cx: ValidationContext): Promise<AnyRecordWithId> {
        throw this.notAllowedError('insert');
    }

    update(_node: Extend<T>, _cx: ValidationContext): Promise<AnyRecord[]> {
        throw this.notAllowedError('update');
    }

    delete(_node: Extend<T>, _cx: ValidationContext): Promise<number> {
        throw this.notAllowedError('delete');
    }

    static getRecordValue(factory: NodeFactory, _id: string): MetaArtifacts | undefined {
        return MetadataHelper.dataByNode[factory.name].find(record => record._id === _id);
    }

    static getPropertyValue(context: Context, property: Property, value: AnyRecord): AnyValue {
        let propertyValue = value?.[property.name];
        if (property.isReferenceProperty() && typeof propertyValue === 'string') {
            return MetadataStorageManager.getRecordValue(property.targetFactory, propertyValue);
        }

        if (property.isReferenceArrayProperty() || property.isCollectionProperty()) {
            if (Array.isArray(propertyValue)) {
                return propertyValue.map(v => MetadataStorageManager.getRecordValue(property.targetFactory, v));
            }
        }

        if (property.isEnumProperty() && typeof propertyValue === 'string') {
            propertyValue = property.dataType.numberValue(propertyValue);
        }

        if (property.isStringProperty() && property.isLocalized && propertyValue && typeof propertyValue === 'object') {
            propertyValue = (propertyValue as AnyRecord)[context.currentLocale];
        }

        return propertyValue;
    }

    query(context: Context, options: NodeExternalQueryOptions<T>): AsyncReader<any> {
        let count = 0;
        const factoryName = this.factory.name;
        const factory = context.application.getFactoryByName(factoryName);
        let rawRecords: any[] | undefined;

        const getPagedResult = async (): Promise<{ items: AnyRecord[]; totalCount: number }> => {
            await MetadataStorageManager.ensureItemsCached(context);

            let factoryNameToFilterBy: string | undefined;

            // We are querying a collection of MetaNodeFactory, we need to filter the records by the factory,
            // to reduce the number of records to be processed when applying the paging options
            if (options.collection && options.collection.sourceNode.$.factory.name === 'MetaNodeFactory') {
                factoryNameToFilterBy = (await options.collection.sourceNode.$.get('name')) as string;
            }

            const records = (MetadataHelper.dataByNode[factoryName] ?? []).filter(rec => {
                if (!('factory' in rec) || factoryNameToFilterBy === undefined) return true;
                return rec.factory === factoryNameToFilterBy;
            }) as any[];

            return RecordPaging.applyPagingOptions(
                factory,
                context,
                await asyncArray(records)
                    .map(async record => {
                        Object.keys(record).forEach(k => {
                            const property = factory.properties.find(p => p.name === k);
                            if (
                                property &&
                                property.isStringProperty() &&
                                property.isLocalized &&
                                typeof record[k] === 'object'
                            ) {
                                record[k] = record[k][context.currentLocale];
                            }
                        });
                        const isActive = await MetadataHelper.getIsActiveValue(context, factory, record);
                        if (isActive != null) {
                            record.isActive = isActive;
                        }
                        return record;
                    })
                    .toArray(),
                MetadataStorageManager.getPropertyValue,
                options,
            );
        };

        // totalCount requested
        if (options.count) {
            return new AsyncArrayReader(async () => {
                return [(await getPagedResult()).totalCount ?? 0];
            });
        }

        const read = async (): Promise<any> => {
            if (!rawRecords) {
                rawRecords = (await getPagedResult()).items;
            }

            if (count > rawRecords.length - 1) return undefined;
            if (count === 0) {
                this.logger.verbose(() => 'query result ready');
            }
            const val = rawRecords[count];
            if (val !== undefined) {
                count += 1;
            }

            return val;
        };

        const stop = (): void => {
            context.logger.verbose(() => `${count} records read`);
        };

        // reader that will be returning the mapped response array
        return new AsyncGenericReader({
            read,
            stop,
        });
    }

    mapRecordIn(record: any): any {
        return record;
    }

    mapAggregateRecordIn(_record: any): any {
        throw new Error('NYI: metadata aggregate');
    }

    getReferenceJoin(propertyName: string): Dict<string | ((this: Extend<T>) => any)> {
        return { _id: propertyName };
    }

    getCollectionJoin(propertyName: string): Dict<string | ((this: Extend<T>) => any)> {
        if (this.factory.name === 'MetaNodeFactory' && ['properties', 'operations'].includes(propertyName)) {
            return { factory: 'name' };
        }

        return {};
    }

    getJoinValues(_node: Extend<T>, data: any, propertyName: string): Dict<any> {
        if (
            propertyName === 'definingPackage' ||
            propertyName === 'package' ||
            propertyName === 'factory' ||
            propertyName === 'extends' ||
            propertyName === 'targetFactory' ||
            propertyName === 'dataType'
        ) {
            return {
                name: data[propertyName],
            };
        }

        if (propertyName === 'reverseReference') {
            return {
                name: data[propertyName],
                factory: data.targetFactory,
            };
        }

        if (propertyName === 'properties' || propertyName === 'operations') {
            return {
                factory: data.name,
            };
        }

        return {};
    }

    private getPropertyFromPath(path: string[]): Property {
        let currentFactory = this.factory;
        let result: Property | undefined;

        path.forEach((key, i) => {
            const property = currentFactory.properties.find(prop => prop.name === key);
            if (!property || (i !== path.length - 1 && !property.isReferenceProperty()))
                throw new Error(`Invalid path: ${path.join('.')}`);
            result = property;
            if (i === path.length - 1) return;
            if (!property.isReferenceProperty()) throw new Error(`Invalid path: ${path.join('.')}`);
            currentFactory = property.targetFactory;
        });
        if (!result) throw new Error(`Invalid path: ${path.join('.')}`);
        return result;
    }

    private convertOrderBy(orderBy: any): OrderByClause[] {
        const clauses: OrderByClause[] = [];
        const convertOne = (prevPath: string[], obj: any): void => {
            Object.keys(obj).forEach(key => {
                const path = [...prevPath, key];
                const val = obj[key];

                if (val && typeof val === 'object') convertOne(path, val);
                else if (val === -1 || val === 1) {
                    const property = this.getPropertyFromPath(path);
                    clauses.push({
                        property,
                        path,
                        direction: val,
                        sql: '',
                        columnAlias: '',
                    });
                } else throw new Error(`${path}: invalid value: ${val})`);
            });
        };
        convertOne([], orderBy || {});
        return clauses;
    }

    parseOrderBy(_context: Context, orderBy: OrderBy<Node> | undefined): OrderByClause[] {
        return this.convertOrderBy(orderBy);
    }

    private static safeParseInt(property: Property, str: string): number {
        const val = Number(str);
        if (!Number.isInteger(val)) throw new Error(`column ${property.name}: invalid integer value ${str}`);
        return val;
    }

    private static safeParseFloat(property: Property, str: string): number {
        const val = Number(str);
        if (!Number.isFinite(val)) throw new Error(`column ${property.name}: invalid number value ${str}`);
        return val;
    }

    private static parsePropertyValue(property: Property, str: string): AnyValue {
        const { type } = property;
        if (type === 'string' || type === 'reference' || type === 'enum') return str;
        if (!str) return null;

        switch (type) {
            case 'float':
            case 'double':
                return this.safeParseFloat(property, str);
            case 'integer':
            case 'short':
                return this.safeParseInt(property, str);
            case 'decimal':
                return this.safeParseFloat(property, str);
            case 'boolean': {
                return String(str) === 'true';
            }
            case 'date':
                return DateValue.parse(str);
            case 'dateRange':
                return DateRange.parse(str);
            case 'datetimeRange':
                return DatetimeRange.parse(str);
            case 'time':
                return Time.parse(str);
            case 'datetime':
                return Datetime.parse(str);
            case 'uuid':
                return Uuid.fromString(str);
            default:
                throw new Error(`column ${property.name}: unsupported type ${type}`);
        }
    }

    /** @internal */
    private static cursorChecksum(str: string): string {
        const s = str.replace(/'/g, '"'); // [1,'10.36'] and [1,"10.36"] must have the same cursor checksum
        const hash = fastHash(s.replace(/'/g, '"')) % 100;
        return `#${hash.toString().padStart(2, '0')}`;
    }

    private static removeCursorChecksum(value: string): string {
        if (!value) return value;
        const trimmed = value.substring(0, value.length - 3);
        const checksum = this.cursorChecksum(trimmed);
        if (!value.endsWith(checksum)) throw new Error(`${value}: invalid cursor value (checksum verification failed)`);
        return trimmed;
    }

    private static parseCursorValues(orderByClauses: OrderByClause[], paramValues: string[]): PropertyAndValue[] {
        if (orderByClauses.length === 0) throw new Error('Cannot parse cursor: missing orderBy');
        if (paramValues.length > orderByClauses.length) throw new Error('Cannot parse cursor: too many values');
        if (paramValues.length < orderByClauses.length) throw new Error('Cannot parse cursor: too few values');

        return orderByClauses.map((orderByClause, i) => {
            return {
                property: orderByClause.property,
                value: this.parsePropertyValue(orderByClause.property, paramValues[i]),
            };
        });
    }

    parseCursor(orderByClauses: OrderByClause[], value: string): PropertyAndValue[] {
        const withoutChecksum = MetadataStorageManager.removeCursorChecksum(value);
        return MetadataStorageManager.parseCursorValues(orderByClauses, friendlyJsonParse(withoutChecksum));
    }

    // Offset transient _id so that it does not interfere with generated ids from payload
    private _lastTransientId = -1000000000;

    allocateTransientId(): string {
        this._lastTransientId -= 1;
        return String(this._lastTransientId);
    }

    /**
     * Get the transient value for the specific type of the property passed
     * @param context
     * @param propertyName
     * @returns
     */
    private getTransientValue(context: Context, propertyName: string): any {
        const property = this.factory.properties.find(prop => prop.name === propertyName);
        const type = property?.type || 'unknown';
        switch (type) {
            case 'string':
            case 'reference':
                return String(context.allocateTransientId());
            case 'float':
            case 'double':
            case 'integer':
            case 'short':
            case 'decimal':
                return context.allocateTransientId();
            default:
                throw new Error(`Transient value for ${type} is not supported`);
        }
    }

    getKeyValues(context: Context, values: any, options?: { allocateTransient: boolean }): Dict<any> {
        if (isScalar(values)) {
            if (this.naturalKey.length > 1) throw new Error(`${this.factory.name} incomplete key passed for read.`);
            return { [this.naturalKey[0]]: values };
        }
        return this.factory.keyProperties.reduce((r, k) => {
            r[k.name] = values[k.name];
            if (options?.allocateTransient && r[k.name] === undefined) {
                r[k.name] = this.getTransientValue(context, k.name);
            }
            return r;
        }, {} as Dict<any>);
    }

    isKeyPropertyTransient(_propertyName: string, _value: any): boolean {
        return false;
    }

    isReverseReferenceProperty(_propertyName: string): boolean {
        return false;
    }

    get defaultOrderBy(): OrderBy<Node> {
        return this.naturalKey.reduce((r, k) => {
            r[k] = 1;
            return r;
        }, {} as Dict<any>);
    }

    /**
     * Gets the formatted key values a string concatenated by `~`
     * This will be used for the _id value
     *
     * @param context
     * @param values
     * @returns
     */
    getId(_context: Context, values: Dict<any>): string {
        return this.naturalKey.reduce((r, k, i) => {
            const currVal = String(values[k]);
            if (this.factory.keyProperties.length === 1 || i === 0) return currVal;
            return `${r}|${currVal}`;
        }, '');
    }

    canCreate(_canCreate: boolean): boolean {
        return false;
    }

    canUpdate(_canUpdate: boolean): boolean {
        return false;
    }

    canDelete(_canDelete: boolean): boolean {
        return false;
    }

    canDeleteMany(_canDeleteMany: boolean): boolean {
        return false;
    }

    private static readonly ensureItemsCachedFunnel = funnel(1);

    static progress = StorageStatus.notStarted;

    static invalidateCache(): void {
        this.progress = StorageStatus.reload;
    }

    private static async ensureItemsCached(context: Context): Promise<void> {
        if (this.progress === StorageStatus.done) {
            return;
        }
        await this.ensureItemsCachedFunnel(async () => {
            // We don't want to execute this function in parallel
            if (this.progress !== StorageStatus.done) {
                await MetadataHelper.loadMetadataStorage(context);

                this.progress = StorageStatus.done;
            }
        });
    }
}
