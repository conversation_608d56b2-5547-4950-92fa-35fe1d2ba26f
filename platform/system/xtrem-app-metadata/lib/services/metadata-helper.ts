import { Context, Dict, NodeFactory, PlainOperationDecorator, friendlyOperationSignature } from '@sage/xtrem-core';
import * as i18n from '@sage/xtrem-i18n';
import { localizedText } from '@sage/xtrem-i18n';
import { supportedLocales } from '@sage/xtrem-shared';
import * as _ from 'lodash';
import { MetaOperationAction } from '../enums/meta-operation-action';
import { MetaOperationKind } from '../enums/meta-operation-kind';
import { MetaPropertyType } from '../enums/meta-property-type';
import { MetaStorage } from '../enums/meta-storage';
import {
    MetaArtifacts,
    MetaDataType,
    MetaNodeFactory,
    MetaNodeOperation,
    MetaNodeProperty,
    MetaPackage,
    MetaServiceOption,
} from './metadata-interfaces';

export class MetadataHelper {
    private static getLocalizedTitles(key: string, name: string): Dict<string> {
        return _.zipObject(
            supportedLocales,
            supportedLocales.map(locale => {
                try {
                    return localizedText(key, name, {}, locale);
                } catch {
                    return _.startCase(name);
                }
            }),
        );
    }

    static dataByNode: Dict<MetaArtifacts[]> = {};

    static packages: Dict<MetaPackage>;

    static async loadPackages(context: Context): Promise<void> {
        const activePackagesName = await context.getActivePackageNames();
        const { application } = context;

        this.dataByNode.MetaPackage = [];
        this.packages = _.mapValues(application.packagesByName, p => {
            const pack = {
                _id: p.name,
                name: p.name,
                title: this.getLocalizedTitles(p.getLocalizedTitleKey(), p.name),
                isActive: activePackagesName.includes(p.name),
            };
            this.dataByNode.MetaPackage.push(pack);
            return pack;
        });
    }

    static serviceOptions: Dict<MetaServiceOption>;

    static loadServiceOptions(context: Context): void {
        const { application } = context;
        const serviceOptions = application.serviceOptionsByName;

        this.dataByNode.MetaServiceOption = [];
        this.serviceOptions = _.mapValues(serviceOptions, (serviceOption, name) => {
            const result = {
                ...serviceOption,
                _id: name,
                name,
                package: serviceOption.packageName,
                title: this.getLocalizedTitles(serviceOption.getLocalizedTitleKey(), name),
            };
            this.dataByNode.MetaServiceOption.push(result);
            return result;
        });
    }

    static dataTypes: Dict<MetaDataType>;

    static loadDatatypes(context: Context): void {
        const { application } = context;
        const localize = (localizationKey: string, name: string): Dict<string> =>
            this.getLocalizedTitles(localizationKey, name) || { base: name };

        this.dataByNode.MetaDataType = [];
        this.dataTypes = _.mapValues(application.dataTypes, (dataType, dataTypeName) => {
            const attributes = dataType.getMetaData({
                application,
                localize,
            });
            const result = {
                _id: dataTypeName,
                definingPackage: dataType.pack || '',
                name: dataTypeName,
                title: this.getLocalizedTitles(dataType.getLocalizedTitleKey(), dataTypeName),
                type: dataType.type,
                attributes: {
                    ...dataType?.dataTypeOptions?.(),
                    ...attributes,
                },
                serviceOptionNames: dataType.serviceOptions?.map(serviceOption => serviceOption.name) || null,
            };

            this.dataByNode.MetaDataType.push(result);

            return result;
        });
    }

    static factories: Dict<MetaNodeFactory>;

    static properties: Dict<MetaNodeProperty>;

    static operations: Dict<MetaNodeOperation>;

    private static getOperationLocalizedTitleKey(factory: NodeFactory, op: PlainOperationDecorator): string {
        const packageName = op.definingPackage?.name || '';
        return i18n.getKey({
            isExtension: packageName !== factory.package.name,
            nodeName: factory.name,
            packageName,
            operation: { name: op.name, kind: op.operationKind },
        });
    }

    static loadNodeFactories(context: Context): void {
        const { application } = context;
        const nodeFactories = application.getAllFactories();

        this.dataByNode.MetaNodeFactory = [];
        this.dataByNode.MetaNodeProperty = [];
        this.dataByNode.MetaNodeOperation = [];

        this.factories = {};
        this.properties = {};
        this.operations = {};

        nodeFactories.forEach(factory => {
            const operations = [...factory.mutations, ...factory.queries];

            this.factories[factory.name] = {
                _id: factory.name,
                name: factory.name,
                title: this.getLocalizedTitles(factory.getLocalizedTitleKey(), factory.name),
                package: factory.package.name,
                isPublished: factory.isPublished,
                isAbstract: factory.isAbstract,
                isCached: factory.isCached,
                extends: null, // TODO: implement later
                storage: factory.storage as MetaStorage,
                isSharedByAllTenants: factory.isSharedByAllTenants,
                isSetupNode: factory.isSetupNode,
                isPlatformNode: factory.isPlatformNode,
                isVitalReferenceChild: factory.isVitalReferenceChild,
                isVitalCollectionChild: factory.isVitalCollectionChild,
                isCustomizable: factory.isCustomizable,
                isSynchronizable: factory.isSynchronizable,
                isSynchronized: factory.isSynchronized,
                serviceOptionNames: factory.serviceOptions?.map(serviceOption => serviceOption.name) || null,
                properties: factory.properties.map(property => `${factory.name}|${property.name}`),
                operations: operations.map(operation => `${factory.name}|${operation.name}`),
                naturalKey: factory.naturalKey ?? null,
            };

            this.dataByNode.MetaNodeFactory.push(this.factories[factory.name]);

            factory.properties.forEach(property => {
                this.properties[`${factory.name}|${property.name}`] = {
                    _id: `${factory.name}|${property.name}`,
                    factory: factory.name,
                    name: property.name,
                    title: this.getLocalizedTitles(property.getLocalizedTitleKey(), property.name),
                    package: property.definingPackage.name ?? factory.package.name,
                    serviceOptionNames: property.serviceOptions?.map(serviceOption => serviceOption.name) || null,
                    type: property.type as MetaPropertyType,
                    dataType: property.dataType?.name,
                    dependencyIndex: property.dependencyIndex || -1,
                    isPublished: property.isPublished,
                    isStored: property.isStored,
                    isTransientInput: property.isTransientInput,
                    isStoredOutput: property.isStoredOutput,
                    isRequired: property.isRequired,
                    isNullable: property.isNullable,
                    targetFactory: property.isForeignNodeProperty() ? property.targetFactory.name : null,
                    reverseReference: property.reverseReference ?? null,
                    isVital: property.isVital,
                    isVitalParent: property.isVitalParent,
                    isInherited: property.isInherited,
                    canSort: property.canSort,
                    canFilter: property.canFilter,
                    isOnInputType: property.isOnInputType,
                    isOnOutputType: property.isOnOutputType,
                    isMutable: property.isMutable,
                };

                this.dataByNode.MetaNodeProperty.push(this.properties[`${factory.name}|${property.name}`]);
            });

            operations.forEach(operation => {
                this.operations[`${factory.name}|${operation.name}`] = {
                    _id: `${factory.name}|${operation.name}`,
                    factory: factory.name,
                    name: operation.name,
                    title: this.getLocalizedTitles(
                        this.getOperationLocalizedTitleKey(factory, operation),
                        operation.name,
                    ),
                    package: operation.definingPackage?.name ?? factory.package.name,
                    serviceOptionNames: operation.serviceOptions?.().map(serviceOption => serviceOption.name) || null,
                    kind: operation.operationKind as MetaOperationKind,
                    action: operation.action ? (operation.action as MetaOperationAction) : null,
                    isPublished: !!operation.isPublished,
                    isSchedulable: !!operation.isSchedulable,
                    isMutation: !!factory.mutations.find(op => op.name === operation.name),
                    parameters: operation.parameters,
                    return: operation.return,
                    signature: friendlyOperationSignature(operation),
                    topic: '', // TODO: implement later
                };
                this.dataByNode.MetaNodeOperation.push(this.operations[`${factory.name}|${operation.name}`]);
            });
        });
    }

    static async getIsActiveValue(
        context: Context,
        factory: NodeFactory,
        value: MetaArtifacts,
    ): Promise<boolean | undefined> {
        if (factory.propertiesByName.isActive == null) return undefined;

        const { serviceOptionsByName } = context.application;

        const isServiceOptionActive = (serviceOptionName: string): boolean => {
            const serviceOption = serviceOptionsByName[serviceOptionName];
            return serviceOption ? context.isServiceOptionActiveSync(serviceOption) : false;
        };

        // TODO: Review if there are performance issues, we should be caching in the getActivePackageNames and isServiceOptionEnabled methods
        switch (factory.name) {
            case 'MetaPackage': {
                const activePackagesName = await context.getActivePackageNames();
                return activePackagesName.includes(value.name);
            }
            case 'MetaServiceOption': {
                return isServiceOptionActive(value.name);
            }
            case 'MetaDataType':
            case 'MetaNodeFactory':
            case 'MetaNodeProperty':
            case 'MetaNodeOperation': {
                const val = value as MetaDataType | MetaNodeFactory | MetaNodeProperty | MetaNodeOperation;
                const { serviceOptionNames } = val;
                if (!serviceOptionNames || serviceOptionNames.length === 0) return true;
                return Promise.resolve(serviceOptionNames.every(isServiceOptionActive));
            }
            default:
                return undefined;
        }
    }

    static async loadMetadataStorage(context: Context): Promise<void> {
        await this.loadPackages(context);
        this.loadServiceOptions(context);
        this.loadDatatypes(context);
        this.loadNodeFactories(context);
    }
}
