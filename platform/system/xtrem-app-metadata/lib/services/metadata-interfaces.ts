import {
    DecimalDataTypeOptions,
    EnumDataTypeOptions,
    FriendlyParameter,
    JsonDataTypeOptions,
    Node,
    OperationReturn,
    PlainParameter,
    ReferenceDataTypeOptions,
    StreamDataTypeOptions,
    StringDataTypeOptions,
} from '@sage/xtrem-core';
import { AnyValue } from '@sage/xtrem-shared';
import { MetaOperationAction } from '../enums/meta-operation-action';
import { MetaOperationKind } from '../enums/meta-operation-kind';
import { MetaPropertyType } from '../enums/meta-property-type';
import { MetaStorage } from '../enums/meta-storage';

export interface MetaPackage {
    _id: string;
    name: string;
    title: AnyValue;
}

export type DataTypeAttributes =
    | (
          | StringDataTypeOptions
          | DecimalDataTypeOptions
          | JsonDataTypeOptions
          | StreamDataTypeOptions
          | EnumDataTypeOptions
          | ReferenceDataTypeOptions<Node, Node>
      )
    | null;

export interface MetaServiceOption {
    _id: string;
    name: string;
    title: AnyValue;
    package: string;
    isSubscribable: boolean;
    isHidden: boolean;
}

export interface MetaDataType {
    _id: string;
    definingPackage: string;
    name: string;
    title: AnyValue;
    type: MetaPropertyType;
    attributes: DataTypeAttributes | null;
    serviceOptionNames: string[] | null;
}

export interface MetaNodeFactory {
    _id: string;
    name: string;
    title: AnyValue;
    package: string;
    isPublished: boolean;
    isAbstract: boolean;
    extends: string | null;
    storage: MetaStorage;
    isSharedByAllTenants: boolean;
    isSetupNode: boolean;
    isPlatformNode: boolean;
    isVitalReferenceChild: boolean;
    isVitalCollectionChild: boolean;
    isCustomizable: boolean;
    isSynchronizable: boolean;
    isSynchronized: boolean;
    serviceOptionNames: string[] | null;
    properties: string[];
    operations: string[];
    isCached: boolean;
    naturalKey: string[] | null;
}

export interface MetaNodeProperty {
    _id: string;
    factory: string;
    name: string;
    title: AnyValue;
    package: string;
    serviceOptionNames: string[] | null;
    type: MetaPropertyType;
    dataType?: string;
    dependencyIndex: number;
    isPublished: boolean;
    isStored: boolean;
    isTransientInput: boolean;
    isStoredOutput: boolean;
    isRequired: boolean;
    isNullable: boolean;
    targetFactory: string | null;
    reverseReference: string | null;
    isVital: boolean;
    isVitalParent: boolean;
    isInherited: boolean;
    canSort: boolean;
    canFilter: boolean;
    isOnInputType: boolean;
    isOnOutputType: boolean;
    isMutable: boolean;
}

export interface MetaNodeOperation {
    _id: string;
    factory: string;
    name: string;
    title: AnyValue;
    package: string;
    serviceOptionNames: string[] | null;
    kind: MetaOperationKind;
    action: MetaOperationAction | null;
    isPublished: boolean;
    isSchedulable: boolean;
    isMutation: boolean;
    parameters: PlainParameter[];
    return: OperationReturn<typeof Node, any>;
    signature: { parameters: FriendlyParameter[]; return: FriendlyParameter };
    topic: string;
}

export type MetaArtifacts =
    | MetaPackage
    | MetaServiceOption
    | MetaDataType
    | MetaNodeFactory
    | MetaNodeProperty
    | MetaNodeOperation;
