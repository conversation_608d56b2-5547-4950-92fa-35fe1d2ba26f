{"@sage/xtrem-app-metadata/data_types__meta_operation_action_enum__name": "", "@sage/xtrem-app-metadata/data_types__meta_operation_kind_enum__name": "", "@sage/xtrem-app-metadata/data_types__meta_property_type_enum__name": "", "@sage/xtrem-app-metadata/data_types__meta_storage_enum__name": "", "@sage/xtrem-app-metadata/data_types__metadata_name__name": "", "@sage/xtrem-app-metadata/data_types__metadata_title__name": "", "@sage/xtrem-app-metadata/enums__meta_operation_action__requestUserNotification": "", "@sage/xtrem-app-metadata/enums__meta_operation_action__start": "", "@sage/xtrem-app-metadata/enums__meta_operation_action__stop": "", "@sage/xtrem-app-metadata/enums__meta_operation_action__track": "", "@sage/xtrem-app-metadata/enums__meta_operation_kind__asyncMutation": "", "@sage/xtrem-app-metadata/enums__meta_operation_kind__asyncTrackerQuery": "", "@sage/xtrem-app-metadata/enums__meta_operation_kind__bulkMutation": "", "@sage/xtrem-app-metadata/enums__meta_operation_kind__mutation": "", "@sage/xtrem-app-metadata/enums__meta_operation_kind__query": "", "@sage/xtrem-app-metadata/enums__meta_property_type__binary": "", "@sage/xtrem-app-metadata/enums__meta_property_type__binaryStream": "", "@sage/xtrem-app-metadata/enums__meta_property_type__boolean": "", "@sage/xtrem-app-metadata/enums__meta_property_type__byte": "", "@sage/xtrem-app-metadata/enums__meta_property_type__collection": "", "@sage/xtrem-app-metadata/enums__meta_property_type__date": "", "@sage/xtrem-app-metadata/enums__meta_property_type__dateRange": "", "@sage/xtrem-app-metadata/enums__meta_property_type__datetime": "", "@sage/xtrem-app-metadata/enums__meta_property_type__datetimeRange": "", "@sage/xtrem-app-metadata/enums__meta_property_type__decimal": "", "@sage/xtrem-app-metadata/enums__meta_property_type__decimalRange": "", "@sage/xtrem-app-metadata/enums__meta_property_type__double": "", "@sage/xtrem-app-metadata/enums__meta_property_type__enum": "", "@sage/xtrem-app-metadata/enums__meta_property_type__enumArray": "", "@sage/xtrem-app-metadata/enums__meta_property_type__float": "", "@sage/xtrem-app-metadata/enums__meta_property_type__integer": "", "@sage/xtrem-app-metadata/enums__meta_property_type__integerArray": "", "@sage/xtrem-app-metadata/enums__meta_property_type__integerRange": "", "@sage/xtrem-app-metadata/enums__meta_property_type__json": "", "@sage/xtrem-app-metadata/enums__meta_property_type__jsonReference": "", "@sage/xtrem-app-metadata/enums__meta_property_type__reference": "", "@sage/xtrem-app-metadata/enums__meta_property_type__referenceArray": "", "@sage/xtrem-app-metadata/enums__meta_property_type__short": "", "@sage/xtrem-app-metadata/enums__meta_property_type__string": "", "@sage/xtrem-app-metadata/enums__meta_property_type__stringArray": "", "@sage/xtrem-app-metadata/enums__meta_property_type__textStream": "", "@sage/xtrem-app-metadata/enums__meta_property_type__time": "", "@sage/xtrem-app-metadata/enums__meta_property_type__uuid": "", "@sage/xtrem-app-metadata/enums__meta_storage__external": "", "@sage/xtrem-app-metadata/enums__meta_storage__json": "", "@sage/xtrem-app-metadata/enums__meta_storage__sql": "", "@sage/xtrem-app-metadata/nodes__meta_data_type__node_name": "", "@sage/xtrem-app-metadata/nodes__meta_data_type__property__attributes": "", "@sage/xtrem-app-metadata/nodes__meta_data_type__property__definingPackage": "", "@sage/xtrem-app-metadata/nodes__meta_data_type__property__isActive": "", "@sage/xtrem-app-metadata/nodes__meta_data_type__property__name": "", "@sage/xtrem-app-metadata/nodes__meta_data_type__property__serviceOptionNames": "", "@sage/xtrem-app-metadata/nodes__meta_data_type__property__serviceOptions": "", "@sage/xtrem-app-metadata/nodes__meta_data_type__property__title": "", "@sage/xtrem-app-metadata/nodes__meta_data_type__property__type": "", "@sage/xtrem-app-metadata/nodes__meta_node_factory__node_name": "", "@sage/xtrem-app-metadata/nodes__meta_node_factory__property__customFields": "", "@sage/xtrem-app-metadata/nodes__meta_node_factory__property__extends": "", "@sage/xtrem-app-metadata/nodes__meta_node_factory__property__isAbstract": "", "@sage/xtrem-app-metadata/nodes__meta_node_factory__property__isActive": "", "@sage/xtrem-app-metadata/nodes__meta_node_factory__property__isCustomizable": "", "@sage/xtrem-app-metadata/nodes__meta_node_factory__property__isPlatformNode": "", "@sage/xtrem-app-metadata/nodes__meta_node_factory__property__isPublished": "", "@sage/xtrem-app-metadata/nodes__meta_node_factory__property__isSetupNode": "", "@sage/xtrem-app-metadata/nodes__meta_node_factory__property__isSharedByAllTenants": "", "@sage/xtrem-app-metadata/nodes__meta_node_factory__property__isSynchronizable": "", "@sage/xtrem-app-metadata/nodes__meta_node_factory__property__isSynchronized": "", "@sage/xtrem-app-metadata/nodes__meta_node_factory__property__isVitalCollectionChild": "", "@sage/xtrem-app-metadata/nodes__meta_node_factory__property__isVitalReferenceChild": "", "@sage/xtrem-app-metadata/nodes__meta_node_factory__property__name": "", "@sage/xtrem-app-metadata/nodes__meta_node_factory__property__naturalKey": "", "@sage/xtrem-app-metadata/nodes__meta_node_factory__property__operations": "", "@sage/xtrem-app-metadata/nodes__meta_node_factory__property__package": "", "@sage/xtrem-app-metadata/nodes__meta_node_factory__property__properties": "", "@sage/xtrem-app-metadata/nodes__meta_node_factory__property__serviceOptionNames": "", "@sage/xtrem-app-metadata/nodes__meta_node_factory__property__serviceOptions": "", "@sage/xtrem-app-metadata/nodes__meta_node_factory__property__storage": "", "@sage/xtrem-app-metadata/nodes__meta_node_factory__property__title": "", "@sage/xtrem-app-metadata/nodes__meta_node_operation__node_name": "", "@sage/xtrem-app-metadata/nodes__meta_node_operation__property__action": "", "@sage/xtrem-app-metadata/nodes__meta_node_operation__property__factory": "", "@sage/xtrem-app-metadata/nodes__meta_node_operation__property__isActive": "", "@sage/xtrem-app-metadata/nodes__meta_node_operation__property__isMutation": "", "@sage/xtrem-app-metadata/nodes__meta_node_operation__property__isPublished": "", "@sage/xtrem-app-metadata/nodes__meta_node_operation__property__isSchedulable": "", "@sage/xtrem-app-metadata/nodes__meta_node_operation__property__kind": "", "@sage/xtrem-app-metadata/nodes__meta_node_operation__property__name": "", "@sage/xtrem-app-metadata/nodes__meta_node_operation__property__package": "", "@sage/xtrem-app-metadata/nodes__meta_node_operation__property__parameters": "", "@sage/xtrem-app-metadata/nodes__meta_node_operation__property__return": "", "@sage/xtrem-app-metadata/nodes__meta_node_operation__property__serviceOptionNames": "", "@sage/xtrem-app-metadata/nodes__meta_node_operation__property__serviceOptions": "", "@sage/xtrem-app-metadata/nodes__meta_node_operation__property__signature": "", "@sage/xtrem-app-metadata/nodes__meta_node_operation__property__title": "", "@sage/xtrem-app-metadata/nodes__meta_node_operation__property__topic": "", "@sage/xtrem-app-metadata/nodes__meta_node_property__node_name": "", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__canFilter": "", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__canSort": "", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__dataType": "", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__dependencyIndex": "", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__factory": "", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__isActive": "", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__isInherited": "", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__isMutable": "", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__isNullable": "", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__isOnInputType": "", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__isOnOutputType": "", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__isPublished": "", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__isRequired": "", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__isStored": "", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__isStoredOutput": "", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__isTransientInput": "", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__isVital": "", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__isVitalParent": "", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__name": "", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__package": "", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__reverseReference": "", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__serviceOptionNames": "", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__serviceOptions": "", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__targetFactory": "", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__title": "", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__type": "", "@sage/xtrem-app-metadata/nodes__meta_package__node_name": "", "@sage/xtrem-app-metadata/nodes__meta_package__property__isActive": "", "@sage/xtrem-app-metadata/nodes__meta_package__property__name": "", "@sage/xtrem-app-metadata/nodes__meta_package__property__title": "", "@sage/xtrem-app-metadata/nodes__meta_service_option__node_name": "", "@sage/xtrem-app-metadata/nodes__meta_service_option__property__isActive": "", "@sage/xtrem-app-metadata/nodes__meta_service_option__property__isHidden": "", "@sage/xtrem-app-metadata/nodes__meta_service_option__property__isSubscribable": "", "@sage/xtrem-app-metadata/nodes__meta_service_option__property__name": "", "@sage/xtrem-app-metadata/nodes__meta_service_option__property__package": "", "@sage/xtrem-app-metadata/nodes__meta_service_option__property__title": "", "@sage/xtrem-app-metadata/package__name": "", "@sage/xtrem-app-metadata/xtrem-object-invalid": ""}