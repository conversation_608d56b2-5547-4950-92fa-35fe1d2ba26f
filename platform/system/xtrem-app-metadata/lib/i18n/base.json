{"@sage/xtrem-app-metadata/data_types__meta_operation_action_enum__name": "Meta operation action enum", "@sage/xtrem-app-metadata/data_types__meta_operation_kind_enum__name": "Meta operation kind enum", "@sage/xtrem-app-metadata/data_types__meta_property_type_enum__name": "Meta property type enum", "@sage/xtrem-app-metadata/data_types__meta_storage_enum__name": "Meta storage enum", "@sage/xtrem-app-metadata/data_types__metadata_name__name": "Metadata name", "@sage/xtrem-app-metadata/data_types__metadata_title__name": "Metadata title", "@sage/xtrem-app-metadata/enums__meta_operation_action__requestUserNotification": "Request user notification", "@sage/xtrem-app-metadata/enums__meta_operation_action__start": "Start", "@sage/xtrem-app-metadata/enums__meta_operation_action__stop": "Stop", "@sage/xtrem-app-metadata/enums__meta_operation_action__track": "Track", "@sage/xtrem-app-metadata/enums__meta_operation_kind__asyncMutation": "Async mutation", "@sage/xtrem-app-metadata/enums__meta_operation_kind__asyncTrackerQuery": "Async tracker query", "@sage/xtrem-app-metadata/enums__meta_operation_kind__bulkMutation": "Bulk mutation", "@sage/xtrem-app-metadata/enums__meta_operation_kind__mutation": "Mutation", "@sage/xtrem-app-metadata/enums__meta_operation_kind__query": "Query", "@sage/xtrem-app-metadata/enums__meta_property_type__binary": "Binary", "@sage/xtrem-app-metadata/enums__meta_property_type__binaryStream": "Binary stream", "@sage/xtrem-app-metadata/enums__meta_property_type__boolean": "Boolean", "@sage/xtrem-app-metadata/enums__meta_property_type__byte": "Byte", "@sage/xtrem-app-metadata/enums__meta_property_type__collection": "Collection", "@sage/xtrem-app-metadata/enums__meta_property_type__date": "Date", "@sage/xtrem-app-metadata/enums__meta_property_type__dateRange": "Date range", "@sage/xtrem-app-metadata/enums__meta_property_type__datetime": "Datetime", "@sage/xtrem-app-metadata/enums__meta_property_type__datetimeRange": "Datetime range", "@sage/xtrem-app-metadata/enums__meta_property_type__decimal": "Decimal", "@sage/xtrem-app-metadata/enums__meta_property_type__decimalRange": "Decimal range", "@sage/xtrem-app-metadata/enums__meta_property_type__double": "Double", "@sage/xtrem-app-metadata/enums__meta_property_type__enum": "Enum", "@sage/xtrem-app-metadata/enums__meta_property_type__enumArray": "Enum array", "@sage/xtrem-app-metadata/enums__meta_property_type__float": "Float", "@sage/xtrem-app-metadata/enums__meta_property_type__integer": "Integer", "@sage/xtrem-app-metadata/enums__meta_property_type__integerArray": "Integer array", "@sage/xtrem-app-metadata/enums__meta_property_type__integerRange": "Integer range", "@sage/xtrem-app-metadata/enums__meta_property_type__json": "Json", "@sage/xtrem-app-metadata/enums__meta_property_type__jsonReference": "Json reference", "@sage/xtrem-app-metadata/enums__meta_property_type__reference": "Reference", "@sage/xtrem-app-metadata/enums__meta_property_type__referenceArray": "Reference array", "@sage/xtrem-app-metadata/enums__meta_property_type__short": "Short", "@sage/xtrem-app-metadata/enums__meta_property_type__string": "String", "@sage/xtrem-app-metadata/enums__meta_property_type__stringArray": "String array", "@sage/xtrem-app-metadata/enums__meta_property_type__textStream": "Text stream", "@sage/xtrem-app-metadata/enums__meta_property_type__time": "Time", "@sage/xtrem-app-metadata/enums__meta_property_type__uuid": "<PERSON><PERSON>", "@sage/xtrem-app-metadata/enums__meta_storage__external": "External", "@sage/xtrem-app-metadata/enums__meta_storage__json": "Json", "@sage/xtrem-app-metadata/enums__meta_storage__sql": "Sql", "@sage/xtrem-app-metadata/nodes__meta_data_type__node_name": "Meta data type", "@sage/xtrem-app-metadata/nodes__meta_data_type__property__attributes": "Attributes", "@sage/xtrem-app-metadata/nodes__meta_data_type__property__definingPackage": "Defining package", "@sage/xtrem-app-metadata/nodes__meta_data_type__property__isActive": "Is active", "@sage/xtrem-app-metadata/nodes__meta_data_type__property__name": "Name", "@sage/xtrem-app-metadata/nodes__meta_data_type__property__serviceOptionNames": "Service option names", "@sage/xtrem-app-metadata/nodes__meta_data_type__property__serviceOptions": "Service options", "@sage/xtrem-app-metadata/nodes__meta_data_type__property__title": "Title", "@sage/xtrem-app-metadata/nodes__meta_data_type__property__type": "Type", "@sage/xtrem-app-metadata/nodes__meta_node_factory__node_name": "Meta node factory", "@sage/xtrem-app-metadata/nodes__meta_node_factory__property__customFields": "Custom fields", "@sage/xtrem-app-metadata/nodes__meta_node_factory__property__extends": "Extends", "@sage/xtrem-app-metadata/nodes__meta_node_factory__property__isAbstract": "Is abstract", "@sage/xtrem-app-metadata/nodes__meta_node_factory__property__isActive": "Is active", "@sage/xtrem-app-metadata/nodes__meta_node_factory__property__isCustomizable": "Is customizable", "@sage/xtrem-app-metadata/nodes__meta_node_factory__property__isPlatformNode": "Is platform node", "@sage/xtrem-app-metadata/nodes__meta_node_factory__property__isPublished": "Is published", "@sage/xtrem-app-metadata/nodes__meta_node_factory__property__isSetupNode": "Is setup node", "@sage/xtrem-app-metadata/nodes__meta_node_factory__property__isSharedByAllTenants": "Is shared by all tenants", "@sage/xtrem-app-metadata/nodes__meta_node_factory__property__isSynchronizable": "Is synchronizable", "@sage/xtrem-app-metadata/nodes__meta_node_factory__property__isSynchronized": "Is synchronized", "@sage/xtrem-app-metadata/nodes__meta_node_factory__property__isVitalCollectionChild": "Is vital collection child", "@sage/xtrem-app-metadata/nodes__meta_node_factory__property__isVitalReferenceChild": "Is vital reference child", "@sage/xtrem-app-metadata/nodes__meta_node_factory__property__name": "Name", "@sage/xtrem-app-metadata/nodes__meta_node_factory__property__naturalKey": "Natural key", "@sage/xtrem-app-metadata/nodes__meta_node_factory__property__operations": "Operations", "@sage/xtrem-app-metadata/nodes__meta_node_factory__property__package": "Package", "@sage/xtrem-app-metadata/nodes__meta_node_factory__property__properties": "Properties", "@sage/xtrem-app-metadata/nodes__meta_node_factory__property__serviceOptionNames": "Service option names", "@sage/xtrem-app-metadata/nodes__meta_node_factory__property__serviceOptions": "Service options", "@sage/xtrem-app-metadata/nodes__meta_node_factory__property__storage": "Storage", "@sage/xtrem-app-metadata/nodes__meta_node_factory__property__title": "Title", "@sage/xtrem-app-metadata/nodes__meta_node_operation__node_name": "Meta node operation", "@sage/xtrem-app-metadata/nodes__meta_node_operation__property__action": "Action", "@sage/xtrem-app-metadata/nodes__meta_node_operation__property__factory": "Factory", "@sage/xtrem-app-metadata/nodes__meta_node_operation__property__isActive": "Is active", "@sage/xtrem-app-metadata/nodes__meta_node_operation__property__isMutation": "Is mutation", "@sage/xtrem-app-metadata/nodes__meta_node_operation__property__isPublished": "Is published", "@sage/xtrem-app-metadata/nodes__meta_node_operation__property__isSchedulable": "Is schedulable", "@sage/xtrem-app-metadata/nodes__meta_node_operation__property__kind": "Kind", "@sage/xtrem-app-metadata/nodes__meta_node_operation__property__name": "Name", "@sage/xtrem-app-metadata/nodes__meta_node_operation__property__package": "Package", "@sage/xtrem-app-metadata/nodes__meta_node_operation__property__parameters": "Parameters", "@sage/xtrem-app-metadata/nodes__meta_node_operation__property__return": "Return", "@sage/xtrem-app-metadata/nodes__meta_node_operation__property__serviceOptionNames": "Service option names", "@sage/xtrem-app-metadata/nodes__meta_node_operation__property__serviceOptions": "Service options", "@sage/xtrem-app-metadata/nodes__meta_node_operation__property__signature": "Signature", "@sage/xtrem-app-metadata/nodes__meta_node_operation__property__title": "Title", "@sage/xtrem-app-metadata/nodes__meta_node_operation__property__topic": "Topic", "@sage/xtrem-app-metadata/nodes__meta_node_property__node_name": "Meta node property", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__canFilter": "Can filter", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__canSort": "Can sort", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__dataType": "Data type", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__dependencyIndex": "Dependency index", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__factory": "Factory", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__isActive": "Is active", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__isInherited": "Is inherited", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__isMutable": "Is mutable", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__isNullable": "Is nullable", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__isOnInputType": "Is on input type", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__isOnOutputType": "Is on output type", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__isPublished": "Is published", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__isRequired": "Is required", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__isStored": "Is stored", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__isStoredOutput": "Is stored output", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__isTransientInput": "Is transient input", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__isVital": "Is vital", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__isVitalParent": "Is vital parent", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__name": "Name", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__package": "Package", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__reverseReference": "Reverse reference", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__serviceOptionNames": "Service option names", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__serviceOptions": "Service options", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__targetFactory": "Target factory", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__title": "Title", "@sage/xtrem-app-metadata/nodes__meta_node_property__property__type": "Type", "@sage/xtrem-app-metadata/nodes__meta_package__node_name": "Meta package", "@sage/xtrem-app-metadata/nodes__meta_package__property__isActive": "Is active", "@sage/xtrem-app-metadata/nodes__meta_package__property__name": "Name", "@sage/xtrem-app-metadata/nodes__meta_package__property__title": "Title", "@sage/xtrem-app-metadata/nodes__meta_service_option__node_name": "Meta service option", "@sage/xtrem-app-metadata/nodes__meta_service_option__property__isActive": "Is active", "@sage/xtrem-app-metadata/nodes__meta_service_option__property__isHidden": "Is hidden", "@sage/xtrem-app-metadata/nodes__meta_service_option__property__isSubscribable": "Is subscribable", "@sage/xtrem-app-metadata/nodes__meta_service_option__property__name": "Name", "@sage/xtrem-app-metadata/nodes__meta_service_option__property__package": "Package", "@sage/xtrem-app-metadata/nodes__meta_service_option__property__title": "Title", "@sage/xtrem-app-metadata/package__name": "Sage xtrem app metadata", "@sage/xtrem-app-metadata/xtrem-object-invalid": "{{xtremObject}} is not a node."}