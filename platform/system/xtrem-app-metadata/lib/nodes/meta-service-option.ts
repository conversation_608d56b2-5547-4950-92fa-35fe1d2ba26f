import { decorators, Node, Reference } from '@sage/xtrem-core';
import * as xtremAppMetadata from '../index';
import { MetadataStorageManager } from '../services/metadata-storage-manager';

@decorators.node<MetaServiceOption>({
    isPublished: true,
    storage: 'external',
    externalStorageManager: new MetadataStorageManager(),
    isSharedByAllTenants: true,
    isCached: true,
    isPlatformNode: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDeleteMany: true,
    keyPropertyNames: ['name'],
    indexes: [
        {
            orderBy: { name: +1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    isFrozen: true,
})
export class MetaServiceOption extends Node {
    @decorators.stringProperty<MetaServiceOption, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremAppMetadata.dataTypes.metadataName,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<MetaServiceOption, 'title'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremAppMetadata.dataTypes.metadataTitle,
    })
    readonly title: Promise<string>;

    @decorators.referenceProperty<MetaServiceOption, 'package'>({
        isStored: true,
        isPublished: true,
        node: () => xtremAppMetadata.nodes.MetaPackage,
    })
    readonly package: Reference<xtremAppMetadata.nodes.MetaPackage>;

    @decorators.booleanProperty<MetaServiceOption, 'isSubscribable'>({
        isStored: true,
        isPublished: true,
    })
    readonly isSubscribable: Promise<boolean>;

    @decorators.booleanProperty<MetaServiceOption, 'isActive'>({
        isStored: true,
        isPublished: true,
    })
    readonly isActive: Promise<boolean>;

    @decorators.booleanProperty<MetaServiceOption, 'isHidden'>({
        isStored: true,
        isPublished: true,
    })
    readonly isHidden: Promise<boolean>;
}
