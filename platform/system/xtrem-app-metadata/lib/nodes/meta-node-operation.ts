import { decorators, Node, PlainParameter, Reference } from '@sage/xtrem-core';
import * as xtremAppMetadata from '..';
import { MetadataStorageManager } from '../services/metadata-storage-manager';

@decorators.node<MetaNodeOperation>({
    isPublished: true,
    storage: 'external',
    externalStorageManager: new MetadataStorageManager(),
    isSharedByAllTenants: true,
    isCached: true,
    isPlatformNode: true,
    isVitalCollectionChild: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDeleteMany: true,
    keyPropertyNames: ['factory', 'name', 'action'],
    indexes: [
        {
            orderBy: { factory: +1, name: +1, action: +1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    isFrozen: true,
})
export class MetaNodeOperation extends Node {
    @decorators.referenceProperty<MetaNodeOperation, 'factory'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremAppMetadata.nodes.MetaNodeFactory,
        lookupAccess: true,
    })
    readonly factory: Reference<xtremAppMetadata.nodes.MetaNodeFactory>;

    @decorators.stringProperty<MetaNodeOperation, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremAppMetadata.dataTypes.metadataName,
        lookupAccess: true,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<MetaNodeOperation, 'title'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremAppMetadata.dataTypes.metadataTitle,
        lookupAccess: true,
    })
    readonly title: Promise<string>;

    @decorators.referenceProperty<MetaNodeOperation, 'package'>({
        isStored: true,
        isPublished: true,
        node: () => xtremAppMetadata.nodes.MetaPackage,
        lookupAccess: true,
    })
    readonly package: Reference<xtremAppMetadata.nodes.MetaPackage>;

    @decorators.jsonProperty<MetaNodeOperation, 'serviceOptionNames'>({
        isStored: true,
        isPublished: true,
    })
    readonly serviceOptionNames: Promise<string[]>;

    @decorators.referenceArrayProperty<MetaNodeOperation, 'serviceOptions'>({
        isPublished: true,
        dependsOn: ['serviceOptionNames'],
        node: () => xtremAppMetadata.nodes.MetaServiceOption,
        async computeValue() {
            const serviceOptionNames = Object.values((await this.serviceOptionNames) ?? {});
            if (!serviceOptionNames || serviceOptionNames.length === 0) return [];
            return this.$.context
                .query(xtremAppMetadata.nodes.MetaServiceOption, {
                    filter: { name: { _in: serviceOptionNames } },
                })
                .toArray();
        },
    })
    readonly serviceOptions: Promise<xtremAppMetadata.nodes.MetaServiceOption[]>;

    @decorators.booleanProperty<MetaNodeOperation, 'isActive'>({
        isStored: true,
        isPublished: true,
        provides: ['isActive'],
    })
    readonly isActive: Promise<boolean>;

    @decorators.enumProperty<MetaNodeOperation, 'kind'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremAppMetadata.enums.operationKindDataType,
    })
    readonly kind: Promise<xtremAppMetadata.enums.MetaOperationKind>;

    @decorators.enumProperty<MetaNodeOperation, 'action'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        allowedInUniqueIndex: true,
        dataType: () => xtremAppMetadata.enums.operationActionDataType,
    })
    readonly action: Promise<xtremAppMetadata.enums.MetaOperationAction | null>;

    @decorators.booleanProperty<MetaNodeOperation, 'isPublished'>({
        isStored: true,
        isPublished: true,
    })
    readonly isPublished: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeOperation, 'isSchedulable'>({
        isStored: true,
        isPublished: true,
    })
    readonly isSchedulable: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeOperation, 'isMutation'>({
        isStored: true,
        isPublished: true,
    })
    readonly isMutation: Promise<boolean>;

    @decorators.jsonProperty<MetaNodeOperation, 'parameters'>({
        isPublished: true,
        async getValue() {
            return (await this.signature).parameters;
        },
    })
    readonly parameters: Promise<PlainParameter[]>;

    @decorators.jsonProperty<MetaNodeOperation, 'return'>({
        isPublished: true,
        async getValue() {
            return (await this.signature).return;
        },
    })
    readonly return: Promise<object>;

    @decorators.jsonProperty<MetaNodeOperation, 'signature'>({
        isStored: true,
        isPublished: true,
    })
    readonly signature: Promise<{ parameters: PlainParameter[]; return: object }>;

    @decorators.stringProperty<MetaNodeOperation, 'topic'>({
        isPublished: true,
        dataType: () => xtremAppMetadata.dataTypes.metadataName,
        async getValue() {
            return `${await (await this.factory).name}/${await this.name}`;
        },
    })
    readonly topic: Promise<string>;
}
