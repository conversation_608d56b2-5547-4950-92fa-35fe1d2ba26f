import { BusinessRuleError, Collection, CoreHooks, decorators, Dict, Node, Reference } from '@sage/xtrem-core';
import { MetaCustomField } from '@sage/xtrem-shared';
import * as xtremAppMetadata from '../index';
import { MetadataStorageManager } from '../services/metadata-storage-manager';

@decorators.node<MetaNodeFactory>({
    isPublished: true,
    storage: 'external',
    externalStorageManager: new MetadataStorageManager(),
    isSharedByAllTenants: true,
    isCached: true,
    isPlatformNode: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDeleteMany: true,
    keyPropertyNames: ['name'],
    indexes: [
        {
            orderBy: { name: +1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    isFrozen: true,
})
export class MetaNodeFactory extends Node {
    @decorators.stringProperty<MetaNodeFactory, 'name'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremAppMetadata.dataTypes.metadataName,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<MetaNodeFactory, 'title'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremAppMetadata.dataTypes.metadataTitle,
    })
    readonly title: Promise<string>;

    @decorators.referenceProperty<MetaNodeFactory, 'package'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremAppMetadata.nodes.MetaPackage,
    })
    readonly package: Reference<xtremAppMetadata.nodes.MetaPackage>;

    @decorators.booleanProperty<MetaNodeFactory, 'isActive'>({
        isStored: true,
        isPublished: true,
        provides: ['isActive'],
    })
    readonly isActive: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeFactory, 'isPublished'>({
        isStored: true,
        isPublished: true,
    })
    readonly isPublished: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeFactory, 'isAbstract'>({
        isStored: true,
        isPublished: true,
    })
    readonly isAbstract: Promise<boolean>;

    @decorators.referenceProperty<MetaNodeFactory, 'extends'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => MetaNodeFactory,
    })
    readonly extends: Reference<MetaNodeFactory | null>;

    @decorators.enumProperty<MetaNodeFactory, 'storage'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremAppMetadata.enums.storageDataType,
    })
    readonly storage: Promise<xtremAppMetadata.enums.MetaStorage>;

    @decorators.booleanProperty<MetaNodeFactory, 'isSharedByAllTenants'>({
        isStored: true,
        isPublished: true,
    })
    readonly isSharedByAllTenants: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeFactory, 'isSetupNode'>({
        isStored: true,
        isPublished: true,
    })
    readonly isSetupNode: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeFactory, 'isPlatformNode'>({
        isStored: true,
        isPublished: true,
    })
    readonly isPlatformNode: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeFactory, 'isVitalReferenceChild'>({
        isStored: true,
        isPublished: true,
    })
    readonly isVitalReferenceChild: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeFactory, 'isVitalCollectionChild'>({
        isStored: true,
        isPublished: true,
    })
    readonly isVitalCollectionChild: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeFactory, 'isCustomizable'>({
        isStored: true,
        isPublished: true,
    })
    readonly isCustomizable: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeFactory, 'isSynchronizable'>({
        isStored: true,
        isPublished: true,
    })
    readonly isSynchronizable: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeFactory, 'isSynchronized'>({
        isStored: true,
        isPublished: true,
    })
    readonly isSynchronized: Promise<boolean>;

    @decorators.jsonProperty<MetaNodeFactory, 'serviceOptionNames'>({
        isStored: true,
        isPublished: true,
    })
    readonly serviceOptionNames: Promise<Dict<string>>;

    @decorators.referenceArrayProperty<MetaNodeFactory, 'serviceOptions'>({
        isPublished: true,
        node: () => xtremAppMetadata.nodes.MetaServiceOption,
        isNullable: true,
        async computeValue() {
            const serviceOptionNames = Object.values((await this.serviceOptionNames) ?? {});
            if (!serviceOptionNames || serviceOptionNames.length === 0) return [];
            return this.$.context.query(xtremAppMetadata.nodes.MetaServiceOption, {
                filter: { name: { _in: serviceOptionNames } },
            });
        },
    })
    readonly serviceOptions: Promise<xtremAppMetadata.nodes.MetaServiceOption[] | null>;

    @decorators.jsonProperty<MetaNodeFactory, 'naturalKey'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly naturalKey: Promise<string[] | null>;

    @decorators.jsonProperty<MetaNodeFactory, 'customFields'>({
        isPublished: true,
        async computeValue() {
            if (CoreHooks.customizationManager) {
                const name = await this.name;
                const factory = this.$.context.application.tryToGetFactoryByName(name);
                if (!factory) return [];
                const { fullName } = factory;
                const customFieldDict = await CoreHooks.customizationManager.getCustomFields(this.$.context, [
                    fullName,
                ]);
                return customFieldDict?.[fullName] ?? [];
            }
            return [];
        },
    })
    readonly customFields: Promise<MetaCustomField[]>;

    // canXxx flags
    // indexes

    @decorators.collectionProperty<MetaNodeFactory, 'properties'>({
        isPublished: true,
        isVital: true,
        node: () => xtremAppMetadata.nodes.MetaNodeProperty,
        reverseReference: 'factory',
    })
    readonly properties: Collection<xtremAppMetadata.nodes.MetaNodeProperty>;

    @decorators.collectionProperty<MetaNodeFactory, 'operations'>({
        isPublished: true,
        isVital: true,
        node: () => xtremAppMetadata.nodes.MetaNodeOperation,
        reverseReference: 'factory',
    })
    readonly operations: Collection<xtremAppMetadata.nodes.MetaNodeOperation>;

    async getNode(): Promise<typeof Node> {
        const node = this.$.context.application.tryToGetFactoryByName(await this.name)?.nodeConstructor;
        if (!node) {
            throw new BusinessRuleError(
                this.$.context.localize(
                    '@sage/xtrem-app-metadata/xtrem-object-invalid',
                    '{{xtremObject}} is not a node.',
                    {
                        xtremObject: await this.name,
                    },
                ),
            );
        }
        return node;
    }
}
