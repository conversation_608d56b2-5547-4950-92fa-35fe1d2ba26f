import { decorators, Node } from '@sage/xtrem-core';
import * as xtremAppMetadata from '../index';
import { MetadataStorageManager } from '../services/metadata-storage-manager';

@decorators.node<MetaPackage>({
    isPublished: true,
    storage: 'external',
    externalStorageManager: new MetadataStorageManager(),
    isSharedByAllTenants: true,
    isCached: true,
    isPlatformNode: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDeleteMany: true,
    keyPropertyNames: ['name'],
    indexes: [
        {
            orderBy: { name: +1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    isFrozen: true,
})
export class MetaPackage extends Node {
    @decorators.stringProperty<MetaPackage, 'name'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremAppMetadata.dataTypes.metadataName,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<MetaPackage, 'title'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremAppMetadata.dataTypes.metadataTitle,
    })
    readonly title: Promise<string>;

    @decorators.booleanProperty<MetaPackage, 'isActive'>({
        isStored: true,
        isPublished: true,
    })
    readonly isActive: Promise<boolean>;
}
