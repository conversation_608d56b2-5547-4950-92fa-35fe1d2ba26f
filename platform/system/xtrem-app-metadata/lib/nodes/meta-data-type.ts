import { Dict, Node, Reference, decorators } from '@sage/xtrem-core';
import * as xtremAppMetadata from '../index';
import { DataTypeAttributes } from '../services/metadata-interfaces';
import { MetadataStorageManager } from '../services/metadata-storage-manager';

@decorators.node<MetaDataType>({
    isPublished: true,
    storage: 'external',
    externalStorageManager: new MetadataStorageManager(),
    isSharedByAllTenants: true,
    isCached: true,
    isPlatformNode: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDeleteMany: true,
    keyPropertyNames: ['name'],
    indexes: [
        {
            orderBy: { name: +1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    isFrozen: true,
})
export class MetaDataType extends Node {
    @decorators.referenceProperty<MetaDataType, 'definingPackage'>({
        isStored: true,
        isPublished: true,
        node: () => xtremAppMetadata.nodes.MetaPackage,
    })
    readonly definingPackage: Reference<xtremAppMetadata.nodes.MetaPackage>;

    @decorators.stringProperty<MetaDataType, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremAppMetadata.dataTypes.metadataName,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<MetaDataType, 'title'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremAppMetadata.dataTypes.metadataTitle,
    })
    readonly title: Promise<string>;

    @decorators.enumProperty<MetaDataType, 'type'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremAppMetadata.enums.MetaPropertyTypeDataType,
    })
    readonly type: Promise<xtremAppMetadata.enums.MetaPropertyType>;

    @decorators.jsonProperty<MetaDataType, 'attributes'>({
        isStored: true,
        isNullable: true,
        isPublished: true,
    })
    readonly attributes: Promise<DataTypeAttributes | null>;

    @decorators.jsonProperty<MetaDataType, 'serviceOptionNames'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly serviceOptionNames: Promise<Dict<string> | null>;

    @decorators.referenceArrayProperty<MetaDataType, 'serviceOptions'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremAppMetadata.nodes.MetaServiceOption,
        async computeValue() {
            const serviceOptionNames = Object.values((await this.serviceOptionNames) ?? {});
            if (!serviceOptionNames || serviceOptionNames.length === 0) return [];

            return this.$.context.query(xtremAppMetadata.nodes.MetaServiceOption, {
                filter: { name: { _in: serviceOptionNames } },
            });
        },
    })
    readonly serviceOptions: Promise<xtremAppMetadata.nodes.MetaServiceOption[] | null>;

    @decorators.booleanProperty<MetaDataType, 'isActive'>({
        isStored: true,
        isPublished: true,
        provides: ['isActive'],
    })
    readonly isActive: Promise<boolean>;
}
