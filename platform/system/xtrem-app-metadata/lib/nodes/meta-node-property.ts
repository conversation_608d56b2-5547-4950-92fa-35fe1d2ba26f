import { decorators, Dict, integer, Node, Reference } from '@sage/xtrem-core';
import * as xtremAppMetadata from '../index';
import { MetadataStorageManager } from '../services/metadata-storage-manager';
import { MetaDataType } from './meta-data-type';

@decorators.node<MetaNodeProperty>({
    isPublished: true,
    storage: 'external',
    externalStorageManager: new MetadataStorageManager(),
    isSharedByAllTenants: true,
    isCached: true,
    isPlatformNode: true,
    isVitalCollectionChild: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDeleteMany: true,
    keyPropertyNames: ['factory', 'name'],
    indexes: [
        {
            orderBy: { factory: +1, name: +1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    isFrozen: true,
})
export class MetaNodeProperty extends Node {
    @decorators.referenceProperty<MetaNodeProperty, 'factory'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremAppMetadata.nodes.MetaNodeFactory,
    })
    readonly factory: Reference<xtremAppMetadata.nodes.MetaNodeFactory>;

    @decorators.stringProperty<MetaNodeProperty, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremAppMetadata.dataTypes.metadataName,
        lookupAccess: true,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<MetaNodeProperty, 'title'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremAppMetadata.dataTypes.metadataTitle,
        lookupAccess: true,
    })
    readonly title: Promise<string>;

    @decorators.referenceProperty<MetaNodeProperty, 'package'>({
        isStored: true,
        isPublished: true,
        node: () => xtremAppMetadata.nodes.MetaPackage,
        lookupAccess: true,
    })
    readonly package: Reference<xtremAppMetadata.nodes.MetaPackage>;

    @decorators.jsonProperty<MetaNodeProperty, 'serviceOptionNames'>({
        isStored: true,
        isPublished: true,
    })
    readonly serviceOptionNames: Promise<Dict<string>>;

    @decorators.referenceArrayProperty<MetaNodeProperty, 'serviceOptions'>({
        isPublished: true,
        node: () => xtremAppMetadata.nodes.MetaServiceOption,
        async computeValue() {
            const serviceOptionNames = Object.values((await this.serviceOptionNames) ?? {});
            if (!serviceOptionNames || serviceOptionNames.length === 0) return [];
            return this.$.context.query(xtremAppMetadata.nodes.MetaServiceOption, {
                filter: { name: { _in: serviceOptionNames } },
            });
        },
    })
    readonly serviceOptions: Promise<xtremAppMetadata.nodes.MetaServiceOption[]>;

    @decorators.booleanProperty<MetaNodeProperty, 'isActive'>({
        isStored: true,
        isPublished: true,
        provides: ['isActive'],
    })
    readonly isActive: Promise<boolean>;

    @decorators.enumProperty<MetaNodeProperty, 'type'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremAppMetadata.enums.MetaPropertyTypeDataType,
        lookupAccess: true,
    })
    readonly type: Promise<xtremAppMetadata.enums.MetaPropertyType>;

    @decorators.referenceProperty<MetaNodeProperty, 'dataType'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => MetaDataType,
    })
    readonly dataType: Reference<MetaDataType>;

    @decorators.integerProperty<MetaNodeProperty, 'dependencyIndex'>({
        isStored: true,
        isPublished: true,
    })
    readonly dependencyIndex: Promise<integer>;

    @decorators.booleanProperty<MetaNodeProperty, 'isPublished'>({
        isStored: true,
        isPublished: true,
    })
    readonly isPublished: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeProperty, 'isStored'>({
        isStored: true,
        isPublished: true,
    })
    readonly isStored: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeProperty, 'isTransientInput'>({
        isStored: true,
        isPublished: true,
    })
    readonly isTransientInput: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeProperty, 'isStoredOutput'>({
        isStored: true,
        isPublished: true,
    })
    readonly isStoredOutput: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeProperty, 'isRequired'>({
        isStored: true,
        isPublished: true,
    })
    readonly isRequired: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeProperty, 'isNullable'>({
        isStored: true,
        isPublished: true,
    })
    readonly isNullable: Promise<boolean>;

    @decorators.referenceProperty<MetaNodeProperty, 'targetFactory'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremAppMetadata.nodes.MetaNodeFactory,
    })
    readonly targetFactory: Reference<xtremAppMetadata.nodes.MetaNodeFactory | null>;

    @decorators.referenceProperty<MetaNodeProperty, 'reverseReference'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => MetaNodeProperty,
    })
    readonly reverseReference: Reference<MetaNodeProperty | null>;

    @decorators.booleanProperty<MetaNodeProperty, 'isVital'>({
        isStored: true,
        isPublished: true,
    })
    readonly isVital: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeProperty, 'isVitalParent'>({
        isStored: true,
        isPublished: true,
    })
    readonly isVitalParent: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeProperty, 'isInherited'>({
        isStored: true,
        isPublished: true,
    })
    readonly isInherited: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeProperty, 'canSort'>({
        isStored: true,
        isPublished: true,
    })
    readonly canSort: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeProperty, 'canFilter'>({
        isStored: true,
        isPublished: true,
    })
    readonly canFilter: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeProperty, 'isOnInputType'>({
        isStored: true,
        isPublished: true,
    })
    readonly isOnInputType: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeProperty, 'isOnOutputType'>({
        isStored: true,
        isPublished: true,
    })
    readonly isOnOutputType: Promise<boolean>;

    @decorators.booleanProperty<MetaNodeProperty, 'isMutable'>({
        isStored: true,
        isPublished: true,
    })
    readonly isMutable: Promise<boolean>;
}
