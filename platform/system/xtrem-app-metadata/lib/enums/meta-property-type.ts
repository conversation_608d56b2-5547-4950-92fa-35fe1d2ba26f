import { EnumDataType } from '@sage/xtrem-core';

export enum MetaPropertyTypeEnum {
    boolean = 1,

    string = 2,

    byte = 3,
    short = 4,
    integer = 5,
    decimal = 6,
    float = 7,
    double = 8,

    enum = 9,

    date = 10,
    time = 11,
    datetime = 12,

    uuid = 13,
    binaryStream = 14,
    textStream = 15,
    json = 16,

    reference = 17,
    collection = 18,
    jsonReference = 19,

    integerArray = 20,
    enumArray = 21,
    referenceArray = 22,
    stringArray = 23,

    integerRange = 24,
    decimalRange = 25,
    dateRange = 26,
    datetimeRange = 27,
    binary = 28,
}

export type MetaPropertyType = keyof typeof MetaPropertyTypeEnum;

export const MetaPropertyTypeDataType = new EnumDataType<MetaPropertyType>({
    enum: MetaPropertyTypeEnum,
    filename: __filename,
});
