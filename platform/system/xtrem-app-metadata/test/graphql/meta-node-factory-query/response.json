{"data": {"xtremAppMetadata": {"metaNodeFactory": {"query": {"edges": [{"cursor": "[\"MetaDataType\"]#24", "node": {"name": "MetaDataType", "title": "Meta data type", "package": {"name": "@sage/xtrem-app-metadata", "title": "Sage xtrem app metadata", "isActive": true}, "isActive": true, "isPublished": true, "isPlatformNode": true, "naturalKey": "[\"name\"]", "properties": {"query": {"edges": [{"node": {"name": "_createStamp", "type": "datetime", "dataType": null, "title": "Create stamp", "package": {"name": "@sage/xtrem-app-metadata"}, "isActive": true, "isNullable": false, "isRequired": false, "isPublished": false, "isStored": true, "isStoredOutput": false, "isTransientInput": false, "isVital": false, "isVitalParent": false, "targetFactory": null, "isInherited": false, "canSort": true, "canFilter": true, "isOnInputType": false, "isOnOutputType": false, "isMutable": false}}, {"node": {"name": "_etag", "type": "string", "dataType": {"name": "_etagDataType"}, "title": "Etag", "package": {"name": "@sage/xtrem-app-metadata"}, "isActive": true, "isNullable": false, "isRequired": false, "isPublished": true, "isStored": false, "isStoredOutput": false, "isTransientInput": false, "isVital": false, "isVitalParent": false, "targetFactory": null, "isInherited": false, "canSort": false, "canFilter": false, "isOnInputType": false, "isOnOutputType": true, "isMutable": false}}, {"node": {"name": "_id", "type": "string", "dataType": null, "title": "_id", "package": {"name": "@sage/xtrem-app-metadata"}, "isActive": true, "isNullable": false, "isRequired": false, "isPublished": true, "isStored": false, "isStoredOutput": false, "isTransientInput": false, "isVital": false, "isVitalParent": false, "targetFactory": null, "isInherited": false, "canSort": false, "canFilter": false, "isOnInputType": false, "isOnOutputType": true, "isMutable": false}}, {"node": {"name": "_sourceId", "type": "string", "dataType": {"name": "_sourceIdDataType"}, "title": "Source id", "package": {"name": "@sage/xtrem-app-metadata"}, "isActive": true, "isNullable": false, "isRequired": false, "isPublished": true, "isStored": false, "isStoredOutput": false, "isTransientInput": true, "isVital": false, "isVitalParent": false, "targetFactory": null, "isInherited": false, "canSort": false, "canFilter": false, "isOnInputType": true, "isOnOutputType": false, "isMutable": false}}, {"node": {"name": "_updateStamp", "type": "datetime", "dataType": null, "title": "Update stamp", "package": {"name": "@sage/xtrem-app-metadata"}, "isActive": true, "isNullable": false, "isRequired": false, "isPublished": false, "isStored": true, "isStoredOutput": false, "isTransientInput": false, "isVital": false, "isVitalParent": false, "targetFactory": null, "isInherited": false, "canSort": true, "canFilter": true, "isOnInputType": false, "isOnOutputType": false, "isMutable": false}}, {"node": {"name": "attributes", "type": "json", "dataType": null, "title": "Attributes", "package": {"name": "@sage/xtrem-app-metadata"}, "isActive": true, "isNullable": true, "isRequired": false, "isPublished": true, "isStored": true, "isStoredOutput": false, "isTransientInput": false, "isVital": false, "isVitalParent": false, "targetFactory": null, "isInherited": false, "canSort": true, "canFilter": true, "isOnInputType": true, "isOnOutputType": true, "isMutable": false}}, {"node": {"name": "definingPackage", "type": "reference", "dataType": null, "title": "Defining package", "package": {"name": "@sage/xtrem-app-metadata"}, "isActive": true, "isNullable": false, "isRequired": false, "isPublished": true, "isStored": true, "isStoredOutput": false, "isTransientInput": false, "isVital": false, "isVitalParent": false, "targetFactory": {"name": "MetaPackage"}, "isInherited": false, "canSort": true, "canFilter": true, "isOnInputType": true, "isOnOutputType": true, "isMutable": false}}, {"node": {"name": "isActive", "type": "boolean", "dataType": null, "title": "Is active", "package": {"name": "@sage/xtrem-app-metadata"}, "isActive": true, "isNullable": false, "isRequired": false, "isPublished": true, "isStored": true, "isStoredOutput": false, "isTransientInput": false, "isVital": false, "isVitalParent": false, "targetFactory": null, "isInherited": false, "canSort": true, "canFilter": true, "isOnInputType": true, "isOnOutputType": true, "isMutable": false}}, {"node": {"name": "name", "type": "string", "dataType": {"name": "metadataName"}, "title": "Name", "package": {"name": "@sage/xtrem-app-metadata"}, "isActive": true, "isNullable": false, "isRequired": false, "isPublished": true, "isStored": true, "isStoredOutput": false, "isTransientInput": false, "isVital": false, "isVitalParent": false, "targetFactory": null, "isInherited": false, "canSort": true, "canFilter": true, "isOnInputType": true, "isOnOutputType": true, "isMutable": false}}, {"node": {"name": "serviceOptionNames", "type": "json", "dataType": null, "title": "Service option names", "package": {"name": "@sage/xtrem-app-metadata"}, "isActive": true, "isNullable": true, "isRequired": false, "isPublished": true, "isStored": true, "isStoredOutput": false, "isTransientInput": false, "isVital": false, "isVitalParent": false, "targetFactory": null, "isInherited": false, "canSort": true, "canFilter": true, "isOnInputType": true, "isOnOutputType": true, "isMutable": false}}, {"node": {"name": "serviceOptions", "type": "referenceArray", "dataType": null, "title": "Service options", "package": {"name": "@sage/xtrem-app-metadata"}, "isActive": true, "isNullable": true, "isRequired": false, "isPublished": true, "isStored": false, "isStoredOutput": false, "isTransientInput": false, "isVital": false, "isVitalParent": false, "targetFactory": {"name": "MetaServiceOption"}, "isInherited": false, "canSort": false, "canFilter": false, "isOnInputType": false, "isOnOutputType": true, "isMutable": false}}, {"node": {"name": "title", "type": "string", "dataType": {"name": "metadataTitle"}, "title": "Title", "package": {"name": "@sage/xtrem-app-metadata"}, "isActive": true, "isNullable": false, "isRequired": false, "isPublished": true, "isStored": true, "isStoredOutput": false, "isTransientInput": false, "isVital": false, "isVitalParent": false, "targetFactory": null, "isInherited": false, "canSort": true, "canFilter": true, "isOnInputType": true, "isOnOutputType": true, "isMutable": false}}, {"node": {"name": "type", "type": "enum", "dataType": {"name": "MetaPropertyTypeDataType"}, "title": "Type", "package": {"name": "@sage/xtrem-app-metadata"}, "isActive": true, "isNullable": false, "isRequired": false, "isPublished": true, "isStored": true, "isStoredOutput": false, "isTransientInput": false, "isVital": false, "isVitalParent": false, "targetFactory": null, "isInherited": false, "canSort": true, "canFilter": true, "isOnInputType": true, "isOnOutputType": true, "isMutable": false}}]}}, "operations": {"query": {"edges": []}}, "customFields": "[]"}}, {"cursor": "[\"TestOperation\"]#67", "node": {"name": "TestOperation", "title": "Test Operation", "package": {"name": "@sage/xtrem-app-metadata", "title": "Sage xtrem app metadata", "isActive": true}, "isActive": true, "isPublished": true, "isPlatformNode": false, "naturalKey": null, "properties": {"query": {"edges": [{"node": {"name": "_etag", "type": "string", "dataType": {"name": "_etagDataType"}, "title": "Etag", "package": {"name": "@sage/xtrem-app-metadata"}, "isActive": true, "isNullable": false, "isRequired": false, "isPublished": true, "isStored": false, "isStoredOutput": false, "isTransientInput": false, "isVital": false, "isVitalParent": false, "targetFactory": null, "isInherited": false, "canSort": false, "canFilter": false, "isOnInputType": false, "isOnOutputType": true, "isMutable": false}}, {"node": {"name": "_id", "type": "string", "dataType": null, "title": "_id", "package": {"name": "@sage/xtrem-app-metadata"}, "isActive": true, "isNullable": false, "isRequired": false, "isPublished": true, "isStored": false, "isStoredOutput": false, "isTransientInput": false, "isVital": false, "isVitalParent": false, "targetFactory": null, "isInherited": false, "canSort": false, "canFilter": false, "isOnInputType": false, "isOnOutputType": true, "isMutable": false}}, {"node": {"name": "_sourceId", "type": "string", "dataType": {"name": "_sourceIdDataType"}, "title": "Source id", "package": {"name": "@sage/xtrem-app-metadata"}, "isActive": true, "isNullable": false, "isRequired": false, "isPublished": true, "isStored": false, "isStoredOutput": false, "isTransientInput": true, "isVital": false, "isVitalParent": false, "targetFactory": null, "isInherited": false, "canSort": false, "canFilter": false, "isOnInputType": true, "isOnOutputType": false, "isMutable": false}}, {"node": {"name": "code", "type": "string", "dataType": null, "title": "Code", "package": {"name": "@sage/xtrem-app-metadata"}, "isActive": true, "isNullable": false, "isRequired": false, "isPublished": true, "isStored": false, "isStoredOutput": false, "isTransientInput": false, "isVital": false, "isVitalParent": false, "targetFactory": null, "isInherited": false, "canSort": false, "canFilter": false, "isOnInputType": false, "isOnOutputType": true, "isMutable": false}}, {"node": {"name": "value", "type": "integer", "dataType": null, "title": "Value", "package": {"name": "@sage/xtrem-app-metadata"}, "isActive": true, "isNullable": false, "isRequired": false, "isPublished": true, "isStored": false, "isStoredOutput": false, "isTransientInput": false, "isVital": false, "isVitalParent": false, "targetFactory": null, "isInherited": false, "canSort": false, "canFilter": false, "isOnInputType": false, "isOnOutputType": true, "isMutable": false}}]}}, "operations": {"query": {"edges": [{"node": {"name": "mutationArrayOfEnums", "title": "Mutation Array Of Enums", "kind": "mutation", "action": null, "package": {"name": "@sage/xtrem-app-metadata"}, "parameters": "[{\"name\":\"objectWithArrayOfEnums\",\"type\":\"object\",\"isMandatory\":false,\"properties\":{\"statusList\":{\"type\":\"array\",\"item\":{\"type\":\"enum\"}}}}]", "return": "{\"type\":\"string\"}", "signature": "{\"parameters\":[{\"name\":\"objectWithArrayOfEnums\",\"type\":\"object\",\"isMandatory\":false,\"properties\":{\"statusList\":{\"type\":\"array\",\"item\":{\"type\":\"enum\"}}}}],\"return\":{\"type\":\"string\"}}", "isActive": true, "isMutation": true}}, {"node": {"name": "mutationArrayOfInstanceReturningString", "title": "Mutation Array Of Instance Returning String", "kind": "mutation", "action": null, "package": {"name": "@sage/xtrem-app-metadata"}, "parameters": "[{\"name\":\"instanceArray\",\"type\":\"array\",\"item\":{\"type\":\"instance\",\"node\":\"TestOperation\"}}]", "return": "\"string\"", "signature": "{\"parameters\":[{\"name\":\"instanceArray\",\"type\":\"array\",\"item\":{\"type\":\"instance\",\"node\":\"TestOperation\"}}],\"return\":\"string\"}", "isActive": true, "isMutation": true}}, {"node": {"name": "mutationReturningNode", "title": "Mutation Returning Node", "kind": "mutation", "action": null, "package": {"name": "@sage/xtrem-app-metadata"}, "parameters": "[{\"name\":\"code\",\"type\":\"string\",\"isMandatory\":true},{\"name\":\"intVal\",\"type\":\"integer\"}]", "return": "{\"type\":\"instance\",\"node\":\"TestOperation\"}", "signature": "{\"parameters\":[{\"name\":\"code\",\"type\":\"string\",\"isMandatory\":true},{\"name\":\"intVal\",\"type\":\"integer\"}],\"return\":{\"type\":\"instance\",\"node\":\"TestOperation\"}}", "isActive": true, "isMutation": true}}, {"node": {"name": "mutationReturningString", "title": "Mutation Returning String", "kind": "mutation", "action": null, "package": {"name": "@sage/xtrem-app-metadata"}, "parameters": "[{\"name\":\"code\",\"type\":\"string\"},{\"name\":\"stringVal\",\"type\":\"string\"},{\"name\":\"intVal\",\"type\":\"integer\"}]", "return": "\"string\"", "signature": "{\"parameters\":[{\"name\":\"code\",\"type\":\"string\"},{\"name\":\"stringVal\",\"type\":\"string\"},{\"name\":\"intVal\",\"type\":\"integer\"}],\"return\":\"string\"}", "isActive": true, "isMutation": true}}, {"node": {"name": "mutationStartedReadonlyRunningWithWritable", "title": "Mutation Started Readonly Running With Writable", "kind": "mutation", "action": null, "package": {"name": "@sage/xtrem-app-metadata"}, "parameters": "[{\"name\":\"reference\",\"type\":\"reference\",\"node\":\"TestOperationReference\"}]", "return": "{\"type\":\"string\"}", "signature": "{\"parameters\":[{\"name\":\"reference\",\"type\":\"reference\",\"node\":\"TestOperationReference\"}],\"return\":{\"type\":\"string\"}}", "isActive": true, "isMutation": true}}, {"node": {"name": "mutationWithBinaryStreamAsResult", "title": "Mutation With Binary Stream As Result", "kind": "mutation", "action": null, "package": {"name": "@sage/xtrem-app-metadata"}, "parameters": "[{\"name\":\"binaryContent\",\"type\":\"string\",\"isMandatory\":true}]", "return": "{\"type\":\"binaryStream\"}", "signature": "{\"parameters\":[{\"name\":\"binaryContent\",\"type\":\"string\",\"isMandatory\":true}],\"return\":{\"type\":\"binaryStream\"}}", "isActive": true, "isMutation": true}}, {"node": {"name": "mutationWithNodeParameter", "title": "Mutation With Node Parameter", "kind": "mutation", "action": null, "package": {"name": "@sage/xtrem-app-metadata"}, "parameters": "[{\"name\":\"arg\",\"type\":\"instance\",\"isMandatory\":true,\"node\":\"TestOperation\"}]", "return": "{\"type\":\"instance\",\"node\":\"TestOperation\"}", "signature": "{\"parameters\":[{\"name\":\"arg\",\"type\":\"instance\",\"isMandatory\":true,\"node\":\"TestOperation\"}],\"return\":{\"type\":\"instance\",\"node\":\"TestOperation\"}}", "isActive": true, "isMutation": true}}, {"node": {"name": "mutationWithNonWritableReferenceParameter", "title": "Mutation With Non Writable Reference Parameter", "kind": "mutation", "action": null, "package": {"name": "@sage/xtrem-app-metadata"}, "parameters": "[{\"name\":\"reference\",\"type\":\"reference\",\"node\":\"TestOperationReference\"}]", "return": "{\"type\":\"string\"}", "signature": "{\"parameters\":[{\"name\":\"reference\",\"type\":\"reference\",\"node\":\"TestOperationReference\"}],\"return\":{\"type\":\"string\"}}", "isActive": true, "isMutation": true}}, {"node": {"name": "mutationWithoutParameters", "title": "Mutation Without Parameters", "kind": "mutation", "action": null, "package": {"name": "@sage/xtrem-app-metadata"}, "parameters": "[]", "return": "{\"type\":\"string\"}", "signature": "{\"parameters\":[],\"return\":{\"type\":\"string\"}}", "isActive": true, "isMutation": true}}, {"node": {"name": "mutationWithTextStreamAsResult", "title": "Mutation With Text Stream As Result", "kind": "mutation", "action": null, "package": {"name": "@sage/xtrem-app-metadata"}, "parameters": "[{\"name\":\"textContent\",\"type\":\"string\",\"isMandatory\":true}]", "return": "{\"type\":\"textStream\"}", "signature": "{\"parameters\":[{\"name\":\"textContent\",\"type\":\"string\",\"isMandatory\":true}],\"return\":{\"type\":\"textStream\"}}", "isActive": true, "isMutation": true}}, {"node": {"name": "mutationWithWritableReferenceParameter", "title": "Mutation With Writable Reference Parameter", "kind": "mutation", "action": null, "package": {"name": "@sage/xtrem-app-metadata"}, "parameters": "[{\"name\":\"reference\",\"type\":\"reference\",\"node\":\"TestOperationReference\",\"isWritable\":true}]", "return": "{\"type\":\"string\"}", "signature": "{\"parameters\":[{\"name\":\"reference\",\"type\":\"reference\",\"node\":\"TestOperationReference\",\"isWritable\":true}],\"return\":{\"type\":\"string\"}}", "isActive": true, "isMutation": true}}, {"node": {"name": "queryReturningArrayOfObjects", "title": "Query Returning Array Of Objects", "kind": "query", "action": null, "package": {"name": "@sage/xtrem-app-metadata"}, "parameters": "[{\"name\":\"len\",\"type\":\"integer\"}]", "return": "{\"type\":\"array\",\"item\":{\"type\":\"object\",\"properties\":{\"index\":\"integer\",\"text\":\"string\"}}}", "signature": "{\"parameters\":[{\"name\":\"len\",\"type\":\"integer\"}],\"return\":{\"type\":\"array\",\"item\":{\"type\":\"object\",\"properties\":{\"index\":\"integer\",\"text\":\"string\"}}}}", "isActive": true, "isMutation": false}}, {"node": {"name": "queryReturningSimpleArray", "title": "Query Returning Simple Array", "kind": "query", "action": null, "package": {"name": "@sage/xtrem-app-metadata"}, "parameters": "[{\"name\":\"len\",\"type\":\"integer\"}]", "return": "{\"type\":\"array\",\"item\":\"string\"}", "signature": "{\"parameters\":[{\"name\":\"len\",\"type\":\"integer\"}],\"return\":{\"type\":\"array\",\"item\":\"string\"}}", "isActive": true, "isMutation": false}}, {"node": {"name": "queryReturningString", "title": "Query Returning String", "kind": "query", "action": null, "package": {"name": "@sage/xtrem-app-metadata"}, "parameters": "[{\"name\":\"code\",\"type\":\"string\"},{\"name\":\"stringVal\",\"type\":\"string\"},{\"name\":\"intVal\",\"type\":\"integer\"},{\"name\":\"enumVal\",\"type\":\"enum\"}]", "return": "\"string\"", "signature": "{\"parameters\":[{\"name\":\"code\",\"type\":\"string\"},{\"name\":\"stringVal\",\"type\":\"string\"},{\"name\":\"intVal\",\"type\":\"integer\"},{\"name\":\"enumVal\",\"type\":\"enum\"}],\"return\":\"string\"}", "isActive": true, "isMutation": false}}, {"node": {"name": "queryWithComplexInput", "title": "Query With Complex Input", "kind": "query", "action": null, "package": {"name": "@sage/xtrem-app-metadata"}, "parameters": "[{\"name\":\"object\",\"type\":\"object\",\"isMandatory\":true,\"properties\":{\"simple\":\"boolean\",\"mandatory\":{\"type\":\"string\",\"isMandatory\":true},\"nullable\":{\"type\":\"string\",\"isNullable\":true}}},{\"name\":\"optionalObjects\",\"type\":\"array\",\"item\":{\"type\":\"object\",\"properties\":{\"nestedStrings\":{\"type\":\"array\",\"item\":\"string\"},\"flag\":\"boolean\"}}}]", "return": "\"string\"", "signature": "{\"parameters\":[{\"name\":\"object\",\"type\":\"object\",\"isMandatory\":true,\"properties\":{\"simple\":\"boolean\",\"mandatory\":{\"type\":\"string\",\"isMandatory\":true},\"nullable\":{\"type\":\"string\",\"isNullable\":true}}},{\"name\":\"optionalObjects\",\"type\":\"array\",\"item\":{\"type\":\"object\",\"properties\":{\"nestedStrings\":{\"type\":\"array\",\"item\":\"string\"},\"flag\":\"boolean\"}}}],\"return\":\"string\"}", "isActive": true, "isMutation": false}}, {"node": {"name": "queryWithComplexOutput", "title": "Query With Complex Output", "kind": "query", "action": null, "package": {"name": "@sage/xtrem-app-metadata"}, "parameters": "[{\"name\":\"object\",\"type\":\"object\",\"isMandatory\":true,\"properties\":{\"simple\":\"boolean\",\"mandatory\":{\"type\":\"string\",\"isMandatory\":true},\"nullable\":{\"type\":\"string\",\"isNullable\":true}}},{\"name\":\"optionalObjects\",\"type\":\"array\",\"item\":{\"type\":\"object\",\"properties\":{\"nestedStrings\":{\"type\":\"array\",\"item\":\"string\"},\"flag\":\"boolean\"}}}]", "return": "{\"type\":\"object\",\"properties\":{\"object\":{\"type\":\"object\",\"isMandatory\":true,\"properties\":{\"simple\":\"boolean\",\"mandatory\":{\"type\":\"string\",\"isMandatory\":true},\"nullable\":{\"type\":\"string\",\"isNullable\":true}}},\"optionalObjects\":{\"type\":\"array\",\"item\":{\"type\":\"object\",\"properties\":{\"nestedStrings\":{\"type\":\"array\",\"item\":\"string\"},\"flag\":\"boolean\"}}}}}", "signature": "{\"parameters\":[{\"name\":\"object\",\"type\":\"object\",\"isMandatory\":true,\"properties\":{\"simple\":\"boolean\",\"mandatory\":{\"type\":\"string\",\"isMandatory\":true},\"nullable\":{\"type\":\"string\",\"isNullable\":true}}},{\"name\":\"optionalObjects\",\"type\":\"array\",\"item\":{\"type\":\"object\",\"properties\":{\"nestedStrings\":{\"type\":\"array\",\"item\":\"string\"},\"flag\":\"boolean\"}}}],\"return\":{\"type\":\"object\",\"properties\":{\"object\":{\"type\":\"object\",\"isMandatory\":true,\"properties\":{\"simple\":\"boolean\",\"mandatory\":{\"type\":\"string\",\"isMandatory\":true},\"nullable\":{\"type\":\"string\",\"isNullable\":true}}},\"optionalObjects\":{\"type\":\"array\",\"item\":{\"type\":\"object\",\"properties\":{\"nestedStrings\":{\"type\":\"array\",\"item\":\"string\"},\"flag\":\"boolean\"}}}}}}", "isActive": true, "isMutation": false}}, {"node": {"name": "queryWithOptionalArgs", "title": "Query With Optional Args", "kind": "query", "action": null, "package": {"name": "@sage/xtrem-app-metadata"}, "parameters": "[{\"name\":\"option1\",\"type\":\"string\",\"isMandatory\":false},{\"name\":\"option2\",\"type\":\"string\",\"isMandatory\":false},{\"name\":\"option3\",\"type\":\"string\",\"isMandatory\":false}]", "return": "\"string\"", "signature": "{\"parameters\":[{\"name\":\"option1\",\"type\":\"string\",\"isMandatory\":false},{\"name\":\"option2\",\"type\":\"string\",\"isMandatory\":false},{\"name\":\"option3\",\"type\":\"string\",\"isMandatory\":false}],\"return\":\"string\"}", "isActive": true, "isMutation": false}}, {"node": {"name": "queryWithReferences", "title": "Query With References", "kind": "query", "action": null, "package": {"name": "@sage/xtrem-app-metadata"}, "parameters": "[{\"name\":\"reference\",\"type\":\"reference\",\"node\":\"TestOperationReference\"},{\"name\":\"nullableReference\",\"type\":\"reference\",\"node\":\"TestOperationReference\",\"isNullable\":true},{\"name\":\"arrayOfReferences\",\"type\":\"array\",\"item\":{\"type\":\"reference\",\"node\":\"TestOperationReference\"}},{\"name\":\"arrayOfNullableReferences\",\"type\":\"array\",\"item\":{\"type\":\"reference\",\"node\":\"TestOperationReference\",\"isNullable\":true}},{\"name\":\"nested\",\"type\":\"object\",\"properties\":{\"reference\":{\"type\":\"reference\",\"node\":\"TestOperationReference\"},\"nullableReference\":{\"type\":\"reference\",\"node\":\"TestOperationReference\",\"isNullable\":true},\"arrayOfReferences\":{\"type\":\"array\",\"item\":{\"type\":\"reference\",\"node\":\"TestOperationReference\"}},\"arrayOfNullableReferences\":{\"type\":\"array\",\"item\":{\"type\":\"reference\",\"node\":\"TestOperationReference\",\"isNullable\":true}}}}]", "return": "{\"type\":\"object\",\"properties\":{\"reference\":{\"type\":\"reference\",\"node\":\"TestOperationReference\"},\"nullableReference\":{\"type\":\"reference\",\"node\":\"TestOperationReference\",\"isNullable\":true},\"arrayOfReferences\":{\"type\":\"array\",\"item\":{\"type\":\"reference\",\"node\":\"TestOperationReference\"}},\"arrayOfNullableReferences\":{\"type\":\"array\",\"item\":{\"type\":\"reference\",\"node\":\"TestOperationReference\",\"isNullable\":true}},\"nested\":{\"type\":\"object\",\"properties\":{\"reference\":{\"type\":\"reference\",\"node\":\"TestOperationReference\"},\"nullableReference\":{\"type\":\"reference\",\"node\":\"TestOperationReference\",\"isNullable\":true},\"arrayOfReferences\":{\"type\":\"array\",\"item\":{\"type\":\"reference\",\"node\":\"TestOperationReference\"}},\"arrayOfNullableReferences\":{\"type\":\"array\",\"item\":{\"type\":\"reference\",\"node\":\"TestOperationReference\",\"isNullable\":true}}}}}}", "signature": "{\"parameters\":[{\"name\":\"reference\",\"type\":\"reference\",\"node\":\"TestOperationReference\"},{\"name\":\"nullableReference\",\"type\":\"reference\",\"node\":\"TestOperationReference\",\"isNullable\":true},{\"name\":\"arrayOfReferences\",\"type\":\"array\",\"item\":{\"type\":\"reference\",\"node\":\"TestOperationReference\"}},{\"name\":\"arrayOfNullableReferences\",\"type\":\"array\",\"item\":{\"type\":\"reference\",\"node\":\"TestOperationReference\",\"isNullable\":true}},{\"name\":\"nested\",\"type\":\"object\",\"properties\":{\"reference\":{\"type\":\"reference\",\"node\":\"TestOperationReference\"},\"nullableReference\":{\"type\":\"reference\",\"node\":\"TestOperationReference\",\"isNullable\":true},\"arrayOfReferences\":{\"type\":\"array\",\"item\":{\"type\":\"reference\",\"node\":\"TestOperationReference\"}},\"arrayOfNullableReferences\":{\"type\":\"array\",\"item\":{\"type\":\"reference\",\"node\":\"TestOperationReference\",\"isNullable\":true}}}}],\"return\":{\"type\":\"object\",\"properties\":{\"reference\":{\"type\":\"reference\",\"node\":\"TestOperationReference\"},\"nullableReference\":{\"type\":\"reference\",\"node\":\"TestOperationReference\",\"isNullable\":true},\"arrayOfReferences\":{\"type\":\"array\",\"item\":{\"type\":\"reference\",\"node\":\"TestOperationReference\"}},\"arrayOfNullableReferences\":{\"type\":\"array\",\"item\":{\"type\":\"reference\",\"node\":\"TestOperationReference\",\"isNullable\":true}},\"nested\":{\"type\":\"object\",\"properties\":{\"reference\":{\"type\":\"reference\",\"node\":\"TestOperationReference\"},\"nullableReference\":{\"type\":\"reference\",\"node\":\"TestOperationReference\",\"isNullable\":true},\"arrayOfReferences\":{\"type\":\"array\",\"item\":{\"type\":\"reference\",\"node\":\"TestOperationReference\"}},\"arrayOfNullableReferences\":{\"type\":\"array\",\"item\":{\"type\":\"reference\",\"node\":\"TestOperationReference\",\"isNullable\":true}}}}}}}", "isActive": true, "isMutation": false}}]}}, "customFields": "[]"}}]}}}}}