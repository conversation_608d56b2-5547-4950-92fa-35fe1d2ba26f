{
    xtremAppMetadata {
        metaNodeFactory {
            query(filter: "{ name: { _in: ['MetaDataType','TestOperation'] } }") {
                edges {
                    cursor
                    node {
                        name
                        title
                        package {
                            name
                            title
                            isActive
                        }
                        isActive
                        isPublished
                        isPlatformNode
                        naturalKey
                        properties {
                            query {
                                edges {
                                    node {
                                        name
                                        type
                                        dataType {
                                            name
                                        }
                                        title
                                        package {
                                            name
                                        }
                                        isActive
                                        isNullable
                                        isRequired
                                        isPublished
                                        isStored
                                        isStoredOutput
                                        isTransientInput
                                        isVital
                                        isVitalParent
                                        targetFactory {
                                            name
                                        }
                                        isInherited
                                        canSort
                                        canFilter
                                        isOnInputType
                                        isOnOutputType
                                        isMutable
                                    }
                                }
                            }
                        }
                        operations {
                            query {
                                edges {
                                    node {
                                        name
                                        title
                                        kind
                                        action
                                        package {
                                            name
                                        }
                                        parameters
                                        return
                                        signature
                                        isActive
                                        isMutation
                                    }
                                }
                            }
                        }
                        customFields
                    }
                }
            }
        }
    }
}
