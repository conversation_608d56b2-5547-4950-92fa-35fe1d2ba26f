{"data": {"xtremAppMetadata": {"metaDataType": {"query": {"edges": [{"cursor": "[\"_constructorDataType\"]#42", "node": {"definingPackage": {"isActive": true, "name": "@sage/xtrem-app-metadata", "title": "Sage xtrem app metadata"}, "isActive": true, "name": "_constructorDataType", "serviceOptions": [], "title": "Constructor Data Type", "type": "string"}}, {"cursor": "[\"_etagDataType\"]#10", "node": {"definingPackage": {"isActive": true, "name": "@sage/xtrem-app-metadata", "title": "Sage xtrem app metadata"}, "isActive": true, "name": "_etagDataType", "serviceOptions": [], "title": "Etag Data Type", "type": "string"}}, {"cursor": "[\"_jsonDataType\"]#27", "node": {"definingPackage": {"isActive": true, "name": "@sage/xtrem-app-metadata", "title": "Sage xtrem app metadata"}, "isActive": true, "name": "_jsonDataType", "serviceOptions": [], "title": "Json Data Type", "type": "json"}}, {"cursor": "[\"_sourceIdDataType\"]#76", "node": {"definingPackage": {"isActive": true, "name": "@sage/xtrem-app-metadata", "title": "Sage xtrem app metadata"}, "isActive": true, "name": "_sourceIdDataType", "serviceOptions": [], "title": "Source Id Data Type", "type": "string"}}, {"cursor": "[\"_syncTickDataType\"]#09", "node": {"definingPackage": {"isActive": true, "name": "@sage/xtrem-app-metadata", "title": "Sage xtrem app metadata"}, "isActive": true, "name": "_syncTickDataType", "serviceOptions": [], "title": "Sync Tick Data Type", "type": "decimal"}}]}}}}}