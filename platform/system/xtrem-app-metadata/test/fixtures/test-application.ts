/* eslint-disable class-methods-use-this */
// The `xtrem test` command checks for a test/fixtures/test-application.ts file (this file)
// and combines its exports with the lib/index.ts exports before running the mocha unit tests.
import {
    Application,
    Context,
    TenantInfo,
    TenantManager,
    TestSysVendor,
    TestUser,
    asyncArray,
    getTestFixtures,
} from '@sage/xtrem-core';
import * as dataTypes from './data-types/_index';
import * as testNodes from './nodes/_index';
import * as serviceOptions from './service-options';

const fixtures = getTestFixtures();

fixtures.updateContext();

const { TestOperation, TestOperationReference } = fixtures.nodes;

class TestTenantManager implements TenantManager {
    initializeManager() {}

    async ensureTenantExists(
        context: Context,
        options: {
            customer: { id: string; name: string };
            tenant: { id: string; name: string };
        },
    ): Promise<void> {
        await context.application.asRoot.withCommittedContext(
            options.tenant.id,
            async createUserContext => {
                let sysTenant = await createUserContext.tryRead(
                    testNodes.SysTenant,
                    { tenantId: options.tenant.id },
                    { forUpdate: true },
                );
                if (!sysTenant) {
                    sysTenant = await createUserContext.create(testNodes.SysTenant, {
                        tenantId: options.tenant.id,
                        name: options.tenant.name,
                    });
                    await sysTenant.$.save();
                }

                // Create initial required users for tenant
                await Context.accessRightsManager.createRequiredUsers(createUserContext);
            },
            { withoutTransactionUser: true },
        );

        await context.application.asRoot.withCommittedContext(options.tenant.id, async createUserContext => {
            if (!(await createUserContext.tryRead(TestUser, { email: '<EMAIL>' }))) {
                const createUser = await createUserContext.create(TestUser, {
                    email: '<EMAIL>',
                    firstName: 'Unit',
                    lastName: 'Test',
                    isActive: true,
                    isAdministrator: true,
                });
                await createUser.$.save();
            }
        });
    }

    async listTenantsIds(context: Context): Promise<string[]> {
        const tenants = await context.query(testNodes.SysTenant).toArray();
        return asyncArray(tenants)
            .map(tenant => tenant.tenantId)
            .toArray();
    }

    async getTenantsInfo(context: Context, tenantId?: string): Promise<TenantInfo[]> {
        const queryOptions = tenantId ? { filter: { tenantId } } : undefined;
        const tenants = await context.query(testNodes.SysTenant, queryOptions as any).toArray();

        return asyncArray(tenants)
            .map(async tenant => {
                return {
                    id: await tenant.tenantId,
                    name: await tenant.name,
                    directoryName: '',
                    customer: {
                        id: await tenant.tenantId,
                        name: await tenant.name,
                    },
                };
            })
            .toArray();
    }

    async deleteTenant(application: Application, tenantId: string): Promise<void> {
        // Delete all the non-shared tables, starting from the leaves of the dependency tree
        const factories = application.getAllSortedFactories().reverse();

        await application.withCommittedContext(null, async context => {
            await context.executeSql('SET CONSTRAINTS ALL DEFERRED', []);

            await asyncArray(factories).forEach(async factory => {
                if (factory.isSharedByAllTenants) return;
                if (factory.storage !== 'sql') return;
                if (!(await factory.table.tableExists(context))) return;

                await context.executeSql<{ updateCount: number }>(
                    `DELETE FROM ${context.application.schemaName}.${factory.table.name} WHERE _tenant_id=$1`,
                    [tenantId],
                );
            });

            // All the tables that are not shared by all tenants have been deleted
            // Delete the sys_tenant entry
            await context.executeSql<{ updateCount: number }>(
                `DELETE FROM ${context.application.schemaName}.sys_tenant WHERE tenant_id=$1`,
                [tenantId],
            );
        });
    }
}

Context.tenantManager = new TestTenantManager();

const nodes = { TestUser, TestSysVendor, TestOperation, TestOperationReference, ...testNodes };

export { dataTypes, nodes, serviceOptions };
