declare module '@sage/xtrem-app-metadata-api-partial' {
    import type {
        AggregateQueryOperation,
        AggregateReadOperation,
        ClientCollection,
        ClientNode,
        ClientNodeInput,
        GetDefaultsOperation,
        QueryOperation,
        ReadOperation,
        VitalClientNode,
        VitalClientNodeInput,
        integer,
    } from '@sage/xtrem-client';
    export interface MetaOperationAction$Enum {
        start: 1;
        stop: 2;
        track: 3;
        requestUserNotification: 4;
    }
    export type MetaOperationAction = keyof MetaOperationAction$Enum;
    export interface MetaOperationKind$Enum {
        query: 1;
        mutation: 2;
        asyncMutation: 3;
        asyncTrackerQuery: 4;
        bulkMutation: 5;
    }
    export type MetaOperationKind = keyof MetaOperationKind$Enum;
    export interface MetaPropertyType$Enum {
        boolean: 1;
        string: 2;
        byte: 3;
        short: 4;
        integer: 5;
        decimal: 6;
        float: 7;
        double: 8;
        enum: 9;
        date: 10;
        time: 11;
        datetime: 12;
        uuid: 13;
        binaryStream: 14;
        textStream: 15;
        json: 16;
        reference: 17;
        collection: 18;
        jsonReference: 19;
        integerArray: 20;
        enumArray: 21;
        referenceArray: 22;
        stringArray: 23;
        integerRange: 24;
        decimalRange: 25;
        dateRange: 26;
        datetimeRange: 27;
        binary: 28;
    }
    export type MetaPropertyType = keyof MetaPropertyType$Enum;
    export interface MetaStorage$Enum {
        sql: 1;
        json: 2;
        external: 3;
    }
    export type MetaStorage = keyof MetaStorage$Enum;
    export interface MetaDataType extends ClientNode {
        definingPackage: MetaPackage;
        name: string;
        title: string;
        type: MetaPropertyType;
        attributes: string;
        serviceOptionNames: string;
        serviceOptions: MetaServiceOption[];
        isActive: boolean;
    }
    export interface MetaDataTypeInput extends ClientNodeInput {
        definingPackage?: string;
        name?: string;
        title?: string;
        type?: MetaPropertyType;
        attributes?: string;
        serviceOptionNames?: string;
        isActive?: boolean | string;
    }
    export interface MetaDataTypeBinding extends ClientNode {
        definingPackage: MetaPackage;
        name: string;
        title: string;
        type: MetaPropertyType;
        attributes: any;
        serviceOptionNames: any;
        serviceOptions: MetaServiceOption[];
        isActive: boolean;
    }
    export interface MetaDataType$Lookups {
        definingPackage: QueryOperation<MetaPackage>;
    }
    export interface MetaDataType$Operations {
        query: QueryOperation<MetaDataType>;
        read: ReadOperation<MetaDataType>;
        aggregate: {
            read: AggregateReadOperation<MetaDataType>;
            query: AggregateQueryOperation<MetaDataType>;
        };
        lookups(dataOrId: string | { data: MetaDataTypeInput }): MetaDataType$Lookups;
        getDefaults: GetDefaultsOperation<MetaDataType>;
    }
    export interface MetaNodeFactory extends ClientNode {
        name: string;
        title: string;
        package: MetaPackage;
        isActive: boolean;
        isPublished: boolean;
        isAbstract: boolean;
        extends: MetaNodeFactory;
        storage: MetaStorage;
        isSharedByAllTenants: boolean;
        isSetupNode: boolean;
        isPlatformNode: boolean;
        isVitalReferenceChild: boolean;
        isVitalCollectionChild: boolean;
        isCustomizable: boolean;
        isSynchronizable: boolean;
        isSynchronized: boolean;
        serviceOptionNames: string;
        serviceOptions: MetaServiceOption[];
        naturalKey: string;
        customFields: string;
        properties: ClientCollection<MetaNodeProperty>;
        operations: ClientCollection<MetaNodeOperation>;
    }
    export interface MetaNodeFactoryInput extends ClientNodeInput {
        name?: string;
        title?: string;
        package?: string;
        isActive?: boolean | string;
        isPublished?: boolean | string;
        isAbstract?: boolean | string;
        extends?: string;
        storage?: MetaStorage;
        isSharedByAllTenants?: boolean | string;
        isSetupNode?: boolean | string;
        isPlatformNode?: boolean | string;
        isVitalReferenceChild?: boolean | string;
        isVitalCollectionChild?: boolean | string;
        isCustomizable?: boolean | string;
        isSynchronizable?: boolean | string;
        isSynchronized?: boolean | string;
        serviceOptionNames?: string;
        naturalKey?: string;
        properties?: Partial<MetaNodePropertyInput>[];
        operations?: Partial<MetaNodeOperationInput>[];
    }
    export interface MetaNodeFactoryBinding extends ClientNode {
        name: string;
        title: string;
        package: MetaPackage;
        isActive: boolean;
        isPublished: boolean;
        isAbstract: boolean;
        extends: MetaNodeFactory;
        storage: MetaStorage;
        isSharedByAllTenants: boolean;
        isSetupNode: boolean;
        isPlatformNode: boolean;
        isVitalReferenceChild: boolean;
        isVitalCollectionChild: boolean;
        isCustomizable: boolean;
        isSynchronizable: boolean;
        isSynchronized: boolean;
        serviceOptionNames: any;
        serviceOptions: MetaServiceOption[];
        naturalKey: any;
        customFields: any;
        properties: ClientCollection<MetaNodePropertyBinding>;
        operations: ClientCollection<MetaNodeOperationBinding>;
    }
    export interface MetaNodeFactory$Lookups {
        package: QueryOperation<MetaPackage>;
        extends: QueryOperation<MetaNodeFactory>;
    }
    export interface MetaNodeFactory$Operations {
        query: QueryOperation<MetaNodeFactory>;
        read: ReadOperation<MetaNodeFactory>;
        aggregate: {
            read: AggregateReadOperation<MetaNodeFactory>;
            query: AggregateQueryOperation<MetaNodeFactory>;
        };
        lookups(dataOrId: string | { data: MetaNodeFactoryInput }): MetaNodeFactory$Lookups;
        getDefaults: GetDefaultsOperation<MetaNodeFactory>;
    }
    export interface MetaNodeOperation extends VitalClientNode {
        factory: MetaNodeFactory;
        name: string;
        title: string;
        package: MetaPackage;
        serviceOptionNames: string;
        serviceOptions: MetaServiceOption[];
        isActive: boolean;
        kind: MetaOperationKind;
        action: MetaOperationAction;
        isPublished: boolean;
        isSchedulable: boolean;
        isMutation: boolean;
        parameters: string;
        return: string;
        signature: string;
        topic: string;
    }
    export interface MetaNodeOperationInput extends VitalClientNodeInput {
        name?: string;
        title?: string;
        package?: string;
        serviceOptionNames?: string;
        isActive?: boolean | string;
        kind?: MetaOperationKind;
        action?: MetaOperationAction;
        isPublished?: boolean | string;
        isSchedulable?: boolean | string;
        isMutation?: boolean | string;
        signature?: string;
    }
    export interface MetaNodeOperationBinding extends VitalClientNode {
        factory: MetaNodeFactory;
        name: string;
        title: string;
        package: MetaPackage;
        serviceOptionNames: any;
        serviceOptions: MetaServiceOption[];
        isActive: boolean;
        kind: MetaOperationKind;
        action: MetaOperationAction;
        isPublished: boolean;
        isSchedulable: boolean;
        isMutation: boolean;
        parameters: any;
        return: any;
        signature: any;
        topic: string;
    }
    export interface MetaNodeOperation$Lookups {
        package: QueryOperation<MetaPackage>;
    }
    export interface MetaNodeOperation$Operations {
        query: QueryOperation<MetaNodeOperation>;
        read: ReadOperation<MetaNodeOperation>;
        aggregate: {
            read: AggregateReadOperation<MetaNodeOperation>;
            query: AggregateQueryOperation<MetaNodeOperation>;
        };
        lookups(dataOrId: string | { data: MetaNodeOperationInput }): MetaNodeOperation$Lookups;
        getDefaults: GetDefaultsOperation<MetaNodeOperation>;
    }
    export interface MetaNodeProperty extends VitalClientNode {
        factory: MetaNodeFactory;
        name: string;
        title: string;
        package: MetaPackage;
        serviceOptionNames: string;
        serviceOptions: MetaServiceOption[];
        isActive: boolean;
        type: MetaPropertyType;
        dataType: MetaDataType;
        dependencyIndex: integer;
        isPublished: boolean;
        isStored: boolean;
        isTransientInput: boolean;
        isStoredOutput: boolean;
        isRequired: boolean;
        isNullable: boolean;
        targetFactory: MetaNodeFactory;
        reverseReference: MetaNodeProperty;
        isVital: boolean;
        isVitalParent: boolean;
        isInherited: boolean;
        canSort: boolean;
        canFilter: boolean;
        isOnInputType: boolean;
        isOnOutputType: boolean;
        isMutable: boolean;
    }
    export interface MetaNodePropertyInput extends VitalClientNodeInput {
        name?: string;
        title?: string;
        package?: string;
        serviceOptionNames?: string;
        isActive?: boolean | string;
        type?: MetaPropertyType;
        dataType?: string;
        dependencyIndex?: integer | string;
        isPublished?: boolean | string;
        isStored?: boolean | string;
        isTransientInput?: boolean | string;
        isStoredOutput?: boolean | string;
        isRequired?: boolean | string;
        isNullable?: boolean | string;
        targetFactory?: string;
        reverseReference?: string;
        isVital?: boolean | string;
        isVitalParent?: boolean | string;
        isInherited?: boolean | string;
        canSort?: boolean | string;
        canFilter?: boolean | string;
        isOnInputType?: boolean | string;
        isOnOutputType?: boolean | string;
        isMutable?: boolean | string;
    }
    export interface MetaNodePropertyBinding extends VitalClientNode {
        factory: MetaNodeFactory;
        name: string;
        title: string;
        package: MetaPackage;
        serviceOptionNames: any;
        serviceOptions: MetaServiceOption[];
        isActive: boolean;
        type: MetaPropertyType;
        dataType: MetaDataType;
        dependencyIndex: integer;
        isPublished: boolean;
        isStored: boolean;
        isTransientInput: boolean;
        isStoredOutput: boolean;
        isRequired: boolean;
        isNullable: boolean;
        targetFactory: MetaNodeFactory;
        reverseReference: MetaNodeProperty;
        isVital: boolean;
        isVitalParent: boolean;
        isInherited: boolean;
        canSort: boolean;
        canFilter: boolean;
        isOnInputType: boolean;
        isOnOutputType: boolean;
        isMutable: boolean;
    }
    export interface MetaNodeProperty$Lookups {
        package: QueryOperation<MetaPackage>;
        dataType: QueryOperation<MetaDataType>;
        targetFactory: QueryOperation<MetaNodeFactory>;
        reverseReference: QueryOperation<MetaNodeProperty>;
    }
    export interface MetaNodeProperty$Operations {
        query: QueryOperation<MetaNodeProperty>;
        read: ReadOperation<MetaNodeProperty>;
        aggregate: {
            read: AggregateReadOperation<MetaNodeProperty>;
            query: AggregateQueryOperation<MetaNodeProperty>;
        };
        lookups(dataOrId: string | { data: MetaNodePropertyInput }): MetaNodeProperty$Lookups;
        getDefaults: GetDefaultsOperation<MetaNodeProperty>;
    }
    export interface MetaPackage extends ClientNode {
        name: string;
        title: string;
        isActive: boolean;
    }
    export interface MetaPackageInput extends ClientNodeInput {
        name?: string;
        title?: string;
        isActive?: boolean | string;
    }
    export interface MetaPackageBinding extends ClientNode {
        name: string;
        title: string;
        isActive: boolean;
    }
    export interface MetaPackage$Operations {
        query: QueryOperation<MetaPackage>;
        read: ReadOperation<MetaPackage>;
        aggregate: {
            read: AggregateReadOperation<MetaPackage>;
            query: AggregateQueryOperation<MetaPackage>;
        };
        getDefaults: GetDefaultsOperation<MetaPackage>;
    }
    export interface MetaServiceOption extends ClientNode {
        name: string;
        title: string;
        package: MetaPackage;
        isSubscribable: boolean;
        isActive: boolean;
        isHidden: boolean;
    }
    export interface MetaServiceOptionInput extends ClientNodeInput {
        name?: string;
        title?: string;
        package?: string;
        isSubscribable?: boolean | string;
        isActive?: boolean | string;
        isHidden?: boolean | string;
    }
    export interface MetaServiceOptionBinding extends ClientNode {
        name: string;
        title: string;
        package: MetaPackage;
        isSubscribable: boolean;
        isActive: boolean;
        isHidden: boolean;
    }
    export interface MetaServiceOption$Lookups {
        package: QueryOperation<MetaPackage>;
    }
    export interface MetaServiceOption$Operations {
        query: QueryOperation<MetaServiceOption>;
        read: ReadOperation<MetaServiceOption>;
        aggregate: {
            read: AggregateReadOperation<MetaServiceOption>;
            query: AggregateQueryOperation<MetaServiceOption>;
        };
        lookups(dataOrId: string | { data: MetaServiceOptionInput }): MetaServiceOption$Lookups;
        getDefaults: GetDefaultsOperation<MetaServiceOption>;
    }
    export interface Package {
        '@sage/xtrem-app-metadata/MetaDataType': MetaDataType$Operations;
        '@sage/xtrem-app-metadata/MetaNodeFactory': MetaNodeFactory$Operations;
        '@sage/xtrem-app-metadata/MetaNodeOperation': MetaNodeOperation$Operations;
        '@sage/xtrem-app-metadata/MetaNodeProperty': MetaNodeProperty$Operations;
        '@sage/xtrem-app-metadata/MetaPackage': MetaPackage$Operations;
        '@sage/xtrem-app-metadata/MetaServiceOption': MetaServiceOption$Operations;
    }
    export interface GraphApi extends Package {}
}
declare module '@sage/xtrem-app-metadata-api' {
    export type * from '@sage/xtrem-app-metadata-api-partial';
}
