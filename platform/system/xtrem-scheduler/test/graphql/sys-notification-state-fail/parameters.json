{"Test creating a SysNotificationState - fail  : wrong locale ": {"executionMode": "normal", "envConfigs": {"today": "2021-11-04"}, "input": {"properties": {"notificationId": "1234", "operationName": "dummyNode.operation", "topic": "dummy<PERSON><PERSON><PERSON>", "status": "running", "locale": "zzzz"}}, "output": {"diagnoses": [{"message": "Locale value is invalid: (zzzz).", "path": ["locale"], "severity": 3}]}}, "LogEntry with data ": {"executionMode": "skip", "envConfigs": {"today": "2021-11-04"}, "input": {"properties": {"notificationId": "1234", "operationName": "dummyNode.operation", "topic": "dummy<PERSON><PERSON><PERSON>", "status": "running", "locale": "en-US", "logs": [{"level": "info", "data": "{}"}]}}, "output": {"diagnoses": [{"message": "Locale value is invalid: (zzzz).", "path": ["locale"], "severity": 3}]}}}