mutation {
    xtremScheduler {
        sysJobSchedule {
            create(
                data: {
                    operation: "#SysNotificationState|simulateAsyncMutation|start"
                    description: "My async test mutation"
                    executionUser: "<EMAIL>"
                    cronSchedule: "* * * * *"
                    id: "async mutation 1"
                    parameterValues: "{\"id\":\"1234\",\"delayInSec\":\"100\"}"
                    timeZone: "Europe/Paris"
                }
            ) {
                id
            }
        }
    }
}
