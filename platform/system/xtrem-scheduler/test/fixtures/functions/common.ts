import { Context, Diagnose, Dict, NodeCreateData, Test, asyncArray, retry } from '@sage/xtrem-core';
import * as xtremMetadata from '@sage/xtrem-metadata';
import * as xtremScheduler from '../../../lib';
import { JobManager } from '../../../lib/services/job-manager';

export function controlScheduleData(
    operation: xtremMetadata.nodes.MetaNodeOperation,
    parameterValues: Dict<string> = {},
    cronPattern: string = '* * * * *',
): Promise<{ valid: boolean; diagnoses: Diagnose[] }> {
    return Test.withContext(async context => {
        const scheduleData = {
            operation,
            parameterValues,
            cronSchedule: cronPattern,
            timeZone: 'Europe/Paris',
        } as NodeCreateData<xtremScheduler.nodes.SysJobSchedule>;

        const newSchedule = await context.create(xtremScheduler.nodes.SysJobSchedule, scheduleData);
        return {
            valid: await newSchedule.$.control(),
            diagnoses: newSchedule.$.context.diagnoses,
        };
    });
}

export function createScheduleData(
    operations: xtremMetadata.nodes.MetaNodeOperation[],
    parameterValues: Dict<string> = {},
    cronPattern: string = '* * * * *',
): Promise<xtremScheduler.nodes.SysJobSchedule[]> {
    return Test.withCommittedContext(context =>
        asyncArray(operations)
            .map(async operation => {
                const scheduleData = {
                    operation,
                    parameterValues,
                    cronSchedule: cronPattern,
                    timeZone: 'Europe/Paris',
                } as NodeCreateData<xtremScheduler.nodes.SysJobSchedule>;

                const newSchedule = await context.create(xtremScheduler.nodes.SysJobSchedule, scheduleData);
                await newSchedule.$.save();

                return newSchedule;
            })
            .toArray(),
    );
}

export async function createJobSchedule(
    context: Context,
    id?: string | undefined,
): Promise<xtremScheduler.nodes.SysJobSchedule> {
    const operation = await getAsyncMutationOperation(context);

    const scheduleData: NodeCreateData<xtremScheduler.nodes.SysJobSchedule> = {
        operation,
        parameterValues: { param1: 'test' },
        cronSchedule: '* * * * *',
        timeZone: 'Europe/Paris',
        id,
    };

    const newSchedule = await context.create(xtremScheduler.nodes.SysJobSchedule, scheduleData);
    await newSchedule.$.save();

    return newSchedule;
}

export function getAsyncMutationOperation(context: Context) {
    return context.read(xtremMetadata.nodes.MetaNodeOperation, {
        factory: '#TestJob',
        name: 'asyncMutation',
        action: 'start',
    });
}

export async function createBulkJobSchedule(context: Context): Promise<xtremScheduler.nodes.SysJobSchedule> {
    const operation = await getAsyncMutationOperation(context);

    const scheduleData: NodeCreateData<xtremScheduler.nodes.SysJobSchedule> = {
        operation,
        parameterValues: { param1: 'bulk' },
        cronSchedule: '* * * * *',
        timeZone: 'Europe/Paris',
    };

    const newSchedule = await context.create(xtremScheduler.nodes.SysJobSchedule, scheduleData);
    await newSchedule.$.save();

    return newSchedule;
}

export function getBulkMutationOperation(context: Context) {
    return context.read(xtremMetadata.nodes.MetaNodeOperation, {
        factory: '#TestJob',
        name: 'bulkMutationTest',
        action: 'start',
    });
}

export async function cleanJobsAfter() {
    JobManager.cleanCronTasks();
    await Test.withCommittedContext(async context => {
        await JobManager.interruptAllJobs(context);
        await retry(
            () =>
                context.deleteMany(xtremScheduler.nodes.SysJobSchedule, {
                    _not: {
                        id: {
                            _in: [
                                'purgeHistory_1',
                                'purgeSyncHistory_1',
                                'purgeSysClientNotification_1',
                                'purgeSysClientNotification_2',
                                'purgeSysClientNotification_3',
                                'purgeSysClientNotification_4',
                                'purgeContentAddressableTables',
                                'bulkMutationTest_1',
                            ],
                        },
                    },
                }),
            { maxTries: 6, delayBeforeRetry: 10000 },
        );
    });
}
