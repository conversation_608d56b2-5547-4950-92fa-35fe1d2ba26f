{"@sage/xtrem-scheduler": [{"topic": "TestJob/job1", "queue": "routing", "sourceFileName": "test-job.ts"}, {"topic": "TestJob/bulk1", "queue": "routing", "sourceFileName": "test-job.ts"}, {"topic": "TestJob/bulkMutationTest/start", "queue": "routing", "sourceFileName": "test-job.ts"}, {"topic": "SysJobExecution/updateStatus", "queue": "routing", "sourceFileName": "sys-job-execution.ts"}, {"topic": "SysJobSchedule/jobScheduleReset", "queue": "routing", "sourceFileName": "sys-job-schedule.ts"}, {"topic": "SysJobSchedule/jobScheduleDelete", "queue": "routing", "sourceFileName": "sys-job-schedule.ts"}, {"topic": "SysNotificationState/updateStatus", "queue": "routing", "sourceFileName": "sys-job-schedule.ts"}]}