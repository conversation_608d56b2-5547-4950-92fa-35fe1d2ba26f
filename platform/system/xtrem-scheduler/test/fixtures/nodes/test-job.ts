import { Context, decorators, integer, Node } from '@sage/xtrem-core';

export interface TestJobPayload {
    text: string;
}

@decorators.node<TestJob>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [{ orderBy: { id: 1 }, isUnique: true }],
})
export class TestJob extends Node {
    @decorators.integerProperty<TestJob, 'id'>({
        isPublished: true,
        isStored: true,
    })
    readonly id: Promise<integer>;

    @decorators.asyncMutation<typeof TestJob, 'asyncMutation'>({
        isPublished: true,
        isSchedulable: true,
        parameters: [{ name: 'param1', type: 'integer' }],
        return: 'integer',
    })
    static async asyncMutation(context: Context, param1: integer): Promise<integer> {
        context.logger.debug(() => `starting asyncMutation with param:${param1}`);
        await context.notify('TestJob/job1', {});
        return 1;
    }

    @decorators.asyncMutation<typeof TestJob, 'asyncMutationWithObjectParam'>({
        isPublished: true,
        isSchedulable: true,
        parameters: [
            {
                name: 'objectParam',
                type: 'object',
                properties: {
                    objectParam1: 'integer',
                },
            },
        ],
        return: 'integer',
    })
    static async asyncMutationWithObjectParam(
        context: Context,
        objectParam: { objectParam1: integer },
    ): Promise<integer> {
        context.logger.debug(() => `starting asyncMutation with param: ${JSON.stringify(objectParam)}`);
        await context.notify('TestJob/job3', {});
        return 1;
    }

    @decorators.bulkMutation<typeof TestJob, 'bulkMutationTest'>({
        isPublished: true,
        isSchedulable: true,
        parameters: [{ name: 'param1', type: 'string' }],
    })
    static bulkMutationTest(context: Context, _job: TestJob, param1: string): Promise<boolean> {
        context.logger.debug(() => `starting bulkMutationTest with param:${param1}`);
        return Promise.resolve(true);
    }

    @decorators.notificationListener<typeof TestJob>({
        topic: 'TestJob/job1',
    })
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    static async onNotification1(context: Context, payload: TestJobPayload): Promise<void> {
        const replyTopic = context.getContextValue('replyTopic') || 'SysJobExecution/updateStatus';
        const replyId = context.getContextValue('replyId');
        await context.reply(replyTopic, { status: 'done' }, { replyId });
    }

    @decorators.notificationListener<typeof TestJob>({
        topic: 'TestJob/bulk1',
    })
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    static async onBulkNotification1(context: Context, _payload: TestJobPayload): Promise<void> {
        const replyTopic = context.getContextValue('replyTopic') || 'SysJobExecution/updateStatus';
        const replyId = context.getContextValue('replyId');
        await context.reply(replyTopic, { status: 'done' }, { replyId });
    }
}
