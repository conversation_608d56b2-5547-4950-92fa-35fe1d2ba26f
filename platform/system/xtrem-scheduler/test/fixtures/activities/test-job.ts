import { Activity } from '@sage/xtrem-core';
import { TestJob } from '../nodes/test-job';

export const asyncMutationActivity = new Activity({
    description: 'Test job',
    node: () => TestJob,
    __filename: 'test-job',
    permissions: ['read', 'create', 'update', 'delete'],
    operationGrants: {
        read: [
            {
                operations: ['async_mutation'],
            },
        ],
    },
});
