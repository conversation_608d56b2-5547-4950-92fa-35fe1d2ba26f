import * as xtremAuthorization from '@sage/xtrem-authorization';
import * as xtremCommunication from '@sage/xtrem-communication';
import { NodeCreateData, Test, UserInfo } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremScheduler from '../../../lib';
import { JobManager } from '../../../lib/services/job-manager';
import { getAsyncMutationOperation } from '../../fixtures/functions/common';

describe('Notification state', () => {
    it('Create update & Controls ', () =>
        Test.withContext(async context => {
            const operation = await getAsyncMutationOperation(context);

            const executionData: NodeCreateData<xtremCommunication.nodes.SysNotificationState> = {
                operation,
                parameterValues: { param1: 'test' },
            };

            const newExecution = await context.create(xtremCommunication.nodes.SysNotificationState, executionData);
            await newExecution.$.save();

            assert.equal(await (await newExecution.operation)?.name, 'asyncMutation');
            assert.deepEqual(await newExecution.parameterValues, { param1: 'test' });
            assert.equal(await newExecution.status, 'pending');

            const existingExecution = await context.read(
                xtremCommunication.nodes.SysNotificationState,
                { _id: newExecution._id },
                { forUpdate: true },
            );

            await existingExecution.logs.append({ level: 'info', data: { test: 1 } });

            assert.notOk(await existingExecution.$.control());
            assert.deepEqual(context.diagnoses, [
                {
                    message: 'You can set data result only when the level is result.',
                    path: ['logs', '-1000000002', 'data'],
                    severity: 3,
                },
            ]);

            await existingExecution.logs.delete(0);

            await existingExecution.$.set({ status: 'running' });
            await existingExecution.logs.append({ level: 'info', message: 'test' });

            await existingExecution.$.save();

            assert.equal(await existingExecution.status, 'running');
        }));
    /** Actually when doing a bulkUpdate we only update the base not the cache  */
    it('On task completed ', () =>
        Test.withContext(async context => {
            const operation = await getAsyncMutationOperation(context);

            const executionData: NodeCreateData<xtremCommunication.nodes.SysNotificationState> = {
                operation,
                parameterValues: { param1: 'test' },
            };

            const newExecution = await context.create(xtremCommunication.nodes.SysNotificationState, executionData);
            await newExecution.$.save();

            await xtremScheduler.nodeExtensions.SysNotificationStateExtension.onTaskComplete(context, {
                status: 'success',
                result: { message: 'Task completed' },
                jobExecutionId: newExecution._id,
            });

            /** forUpdate is used to force the reload from the database  */
            const updatedExecution = await context.read(
                xtremCommunication.nodes.SysNotificationState,
                {
                    _id: newExecution._id,
                },
                { forUpdate: true },
            );

            assert.equal(await updatedExecution.status, 'success');
            assert.deepEqual(await updatedExecution.result, { message: 'Task completed' });
        }));

    it('Job cannot be executed by unauthorized user', async () => {
        await Test.withUserContext(
            async context => {
                const operation = await getAsyncMutationOperation(context);
                // cannot modify executionUser if current user is not admin
                await assert.isRejected(
                    JobManager.executeAndCreateJob(context, {
                        operation,
                        parameterValues: { param1: 'test' },
                        executionUser: (await context.user) as UserInfo,
                        topic: await operation.topic,
                    }),
                    'You cannot perform this operation TestJob.asyncMutation',
                );
            },
            { user: '<EMAIL>' },
            [xtremAuthorization.serviceOptions.authorizationServiceOption, xtremSystem.serviceOptions.DevTools],
        );
    });
});
