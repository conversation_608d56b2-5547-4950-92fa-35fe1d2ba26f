import * as xtremCommunication from '@sage/xtrem-communication';
import { Dict, ObjectType, OperationType, Test, TypeCache } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as timers from 'timers/promises';
import * as xtremScheduler from '../../../lib';
import { JobManager } from '../../../lib/services/job-manager';
import { createBulkJobSchedule, createJobSchedule } from '../../fixtures/functions/common';

describe('SysJobSchedule', () => {
    it('Can create and update a job schedule', () =>
        Test.withContext(async context => {
            const { _id } = await createJobSchedule(context);

            const existingSchedule = await context.read(
                xtremScheduler.nodes.SysJobSchedule,
                { _id },
                { forUpdate: true },
            );

            assert.equal(await (await existingSchedule.operation).name, 'asyncMutation');
            assert.deepEqual(await existingSchedule.parameterValues, { param1: 'test' });
            assert.equal(await existingSchedule.cronSchedule, '* * * * *');
            assert.equal(await existingSchedule.executionLocale, context.currentLocale);

            await existingSchedule.$.set({ cronSchedule: '0 0 * * Mon' });
            await existingSchedule.$.save();

            assert.equal(await existingSchedule.cronSchedule, '0 0 * * Mon');
        }));
    it('Can follow an execution created from a schedule & purge the execution', () =>
        Test.withContext(async context => {
            const schedule = await createJobSchedule(context);
            const operation = await schedule.operation;

            const { _id, notificationId } = await JobManager.executeAndCreateJob(context, {
                scheduleId: schedule._id,
                operation,
                parameterValues: {
                    param1: 'test',
                } as Dict<string>,
                topic: await operation.topic,
            });

            assert.isNotNull(notificationId);

            const jobExec = await context
                .query(xtremCommunication.nodes.SysNotificationState, {
                    filter: { schedule: schedule._id },
                })
                .toArray();

            assert.equal(jobExec.length, 1);
            assert.equal(await jobExec[0].notificationId, notificationId);
            assert.equal(jobExec[0]._id, _id);

            const user = await context.user;

            await xtremCommunication.nodes.SysNotificationState.upsert(context, {
                envelope: {
                    payload: {},
                    attributes: {
                        notificationId,
                        topic: await operation.topic,
                        tenantId: context.tenantId!,
                        userEmail: user?.email!,
                        login: '',
                        locale: user?.locale!,
                        originId: '',
                        resumeToken: '',
                    },
                },
                status: 'success',
            });

            const numberOfExecutionDeleted = await xtremScheduler.nodes.SysJobSchedule.purgeExecution(
                context,
                schedule,
            );
            assert.equal(numberOfExecutionDeleted, 1);
        }));

    it('Cannot create twice the same schedule', () =>
        Test.withContext(async context => {
            const schedule = await createJobSchedule(context);
            const operation = await schedule.operation;

            await JobManager.executeAndCreateJob(context, {
                scheduleId: schedule._id,
                operation,
                parameterValues: {
                    param1: 'test',
                } as Dict<string>,
                topic: await operation.topic,
            });

            try {
                await JobManager.executeAndCreateJob(context, {
                    scheduleId: schedule._id,
                    operation,
                    parameterValues: {
                        param1: 'test',
                    } as Dict<string>,
                    topic: await operation.topic,
                });
                assert.fail('Should have thrown an error');
            } catch (e) {
                assert.strictEqual(e.message, 'An error has occurred. Please contact your administrator.');
                assert.match(e.innerError?.message, /1 scheduled job \d+ already running or pending/);
            }
        }));

    it.skip('Can follow a bulk mutation execution created from a schedule & purge the execution', () =>
        Test.withContext(async context => {
            const schedule = await createBulkJobSchedule(context);
            const operation = await schedule.operation;

            const { _id, notificationId } = await JobManager.executeAndCreateJob(context, {
                scheduleId: schedule._id,
                operation,
                parameterValues: {
                    param1: 'bulk1',
                } as Dict<string>,
                topic: await operation.topic,
            });

            assert.isNotNull(notificationId);

            let jobExec = await context
                .query(xtremCommunication.nodes.SysNotificationState, {
                    filter: { schedule: schedule._id },
                })
                .toArray();
            assert.equal(jobExec.length, 1);
            assert.equal(await jobExec[0].notificationId, notificationId);
            assert.equal(jobExec[0]._id, _id);

            const user = await context.user;

            await timers.setTimeout(3000);

            jobExec = await context
                .query(xtremCommunication.nodes.SysNotificationState, {
                    filter: { schedule: schedule._id },
                })
                .toArray();

            await xtremCommunication.nodes.SysNotificationState.upsert(context, {
                envelope: {
                    payload: {},
                    attributes: {
                        notificationId,
                        topic: await operation.topic,
                        tenantId: context.tenantId!,
                        userEmail: user?.email!,
                        login: '',
                        locale: user?.locale!,
                        originId: '',
                        resumeToken: '',
                    },
                },
                status: 'success',
            });

            const numberOfExecutionDeleted = await xtremScheduler.nodes.SysJobSchedule.purgeExecution(
                context,
                schedule,
            );
            assert.equal(numberOfExecutionDeleted, 1);
        }));

    it('Async mutation with object parameter', () =>
        Test.withContext(async context => {
            const graphqlProperty = OperationType.makeParameterProperty(
                new TypeCache(context.application),
                'TestJob',
                {
                    type: 'object',
                    name: undefined,
                    properties: {
                        objectParam: {
                            name: 'objectParam',
                            type: 'object',
                            isMandatory: true,
                            properties: {
                                objectParam1: 'integer',
                            },
                        },
                    },
                },
                'asyncMutationWithObjectParam',
                'parameter',
            );

            const res = await ObjectType.getObjectInputValue(
                context,
                {
                    objectParam: JSON.stringify({ objectParam1: 2 }),
                },
                graphqlProperty.targetFactory,
            );

            assert.deepEqual(res, { objectParam: { objectParam1: 2 } });
        }));
    it('Can create two job schedules', () =>
        Test.withContext(async context => {
            const { _id } = await createJobSchedule(context, 'asyncMutation_2');

            const existingSchedule = await context.read(xtremScheduler.nodes.SysJobSchedule, { _id });

            assert.equal(await existingSchedule.id, 'asyncMutation_2');

            const _id2 = (await createJobSchedule(context))._id;

            const existingSchedule2 = await context.read(xtremScheduler.nodes.SysJobSchedule, { _id: _id2 });

            assert.equal(await existingSchedule2.id, 'asyncMutation_3');
        }));

    it('Should throw error when stopping while the status is not equal to running', () =>
        Test.withContext(async context => {
            const schedule = await createBulkJobSchedule(context);
            const operation = await schedule.operation;

            const { _id } = await JobManager.executeAndCreateJob(context, {
                scheduleId: schedule._id,
                operation,
                parameterValues: {
                    param1: 'bulk1',
                } as Dict<string>,
                topic: await operation.topic,
            });

            assert.isNotNull(_id);

            await timers.setTimeout(3000);

            await assert.isRejected(
                xtremScheduler.nodeExtensions.SysNotificationStateExtension.stop(context, String(_id), 'reason'),
                'This process cannot be stopped, as it is no longer running.',
            );
        }));
});
