import * as xtremCommunication from '@sage/xtrem-communication';
import { ConditionVariable, Context, Dict, Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremScheduler from '../../../lib';
import { JobManager } from '../../../lib/services/job-manager';
import {
    cleanJobsAfter,
    controlScheduleData,
    createScheduleData,
    getAsyncMutationOperation,
    getBulkMutationOperation,
} from '../../fixtures/functions/common';
import * as testApplication from '../../fixtures/test-application';

import sinon = require('sinon');

const sandbox = sinon.createSandbox();

describe('JobManager', () => {
    before(async () => {
        await xtremScheduler.startService(Test.application);
    });

    after(async () => {
        await cleanJobsAfter();
    });

    it('Can create a job schedule from async operation', () =>
        Test.withContext(async context => {
            const operation = await getAsyncMutationOperation(context);

            const tasks = JobManager.scheduledJobs[context.tenantId!];

            assert.isArray(tasks);
            assert.equal(tasks.length, 8);

            const scheduledOperations = await createScheduleData([operation], {
                param1: 'test',
            } as Dict<string>);

            assert.equal(scheduledOperations.length, 1);
        }));

    it('Control should failed with invalid cron pattern', () =>
        Test.withContext(async context => {
            const operation = await getAsyncMutationOperation(context);

            const control = await controlScheduleData(
                operation,
                {
                    param1: 'test',
                } as Dict<string>,
                'a b c d e f',
            );

            assert.isFalse(control.valid, 'The schedule should not be valid with an invalid cron pattern');
            assert.isArray(control.diagnoses);
            assert.isTrue(control.diagnoses.length > 0, 'There should be at least one diagnose');
            assert.isTrue(
                control.diagnoses.some(d => d.message.includes('Schedule value is invalid')),
                'There should be a diagnose with "Schedule value is invalid" message',
            );
        }));

    it('Can create a job schedule from bulk operation', async () => {
        const bulkMutationTest = {
            method: testApplication.nodes.TestJob.bulkMutationTest,
            condition: Test.createConditionVariable('bulkMutationTest'),
        } as {
            method: (context: Context, job: testApplication.nodes.TestJob, param1: string) => Promise<boolean>;
            condition: ConditionVariable;
            param1: string;
            result?: boolean;
        };
        await Test.withCommittedContext(async context => {
            const job1 = await context.create(testApplication.nodes.TestJob, { id: 1 });
            await job1.$.save();
        });

        const { method } = bulkMutationTest;
        sandbox
            .stub(testApplication.nodes.TestJob, 'bulkMutationTest')
            .callsFake(async (context: Context, job: testApplication.nodes.TestJob, param1: string) => {
                bulkMutationTest.result = await method(context, job, param1);
                bulkMutationTest.param1 = param1;
                setImmediate(() => bulkMutationTest.condition.notifyAll());
                return bulkMutationTest.result;
            });

        await Test.withContext(async context => {
            const operation = await getBulkMutationOperation(context);

            // the TestJob/asyncMutation task is schedule every minutes (* * * * *)  so if that test starts at an exact minute
            // we have to exclude that task.
            const tasks = JobManager.scheduledJobs[context.tenantId!].filter(t => t.topic !== 'TestJob/asyncMutation');

            const expectedTasksCount = 8;
            assert.isArray(tasks);
            if (tasks.length !== expectedTasksCount) {
                // eslint-disable-next-line no-console
                console.log(`Wrong tasks count: ${tasks.map(t => t.topic).join(',')}`);
            }
            assert.equal(tasks.length, expectedTasksCount);

            const scheduledOperations = await createScheduleData(
                [operation],
                {
                    filter: '{id:1}',
                    param1: 'bulk1',
                } as Dict<string>,
                '* * * * * *',
            );

            assert.equal(scheduledOperations.length, 1);
            await bulkMutationTest.condition.wait();
            assert.equal(bulkMutationTest.result, true);
            assert.equal(bulkMutationTest.param1, 'bulk1');
        });
        sinon.restore();
    });

    it('Can create a job execution from operation', () =>
        Test.withContext(async context => {
            const operation = await getAsyncMutationOperation(context);

            const { _id, notificationId } = await JobManager.executeAndCreateJob(context, {
                operation,
                parameterValues: {
                    param1: 'test',
                } as Dict<string>,
                topic: await operation.topic,
            });

            assert.isNotNull(notificationId);

            const jobExec = await context.read(xtremCommunication.nodes.SysNotificationState, { _id });

            assert.equal(await (await jobExec.operation)?.name, 'asyncMutation');
        }));
});
