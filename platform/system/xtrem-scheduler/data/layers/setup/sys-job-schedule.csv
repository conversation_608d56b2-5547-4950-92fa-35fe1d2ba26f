"id";"execution_user";"_vendor";"operation";"description";"is_active";"execution_locale";"parameter_values";"cron_schedule";"time_zone"
"purgeHistory_1";"<EMAIL>";"sage";"SysNotificationState|purgeHistory|start";"Purge history - 3 months";"Y";"en-US";"{""unit"":""months"",""duration"":""3""}";"0 1 1 * *";"Europe/Paris"
"purgeSyncHistory_1";"<EMAIL>";"sage";"SysNotificationState|purgeHistory|start";"Purge sync history - 2 weeks";"Y";"en-US";"file:parameter-values--purge-sync-history-1-root-localhost-domain.json";"30 1 * * *";"Europe/Paris"
"purgeSysClientNotification_1";"<EMAIL>";"sage";"SysClientNotification|purgeSysClientNotification|start";"""info"" client notification will automatically be deleted after seven days";"Y";"en-US";"{""unit"":""days"",""level"":""info"",""duration"":""7""}";"0 0 * * *";"Europe/Paris"
"purgeSysClientNotification_2";"<EMAIL>";"sage";"SysClientNotification|purgeSysClientNotification|start";"""success"" client notification will automatically be deleted after seven days";"Y";"en-US";"{""unit"":""days"",""level"":""success"",""duration"":""7""}";"0 0 * * *";"Europe/Paris"
"purgeSysClientNotification_3";"<EMAIL>";"sage";"SysClientNotification|purgeSysClientNotification|start";"""warning"" client notification will automatically be deleted after seven days";"Y";"en-US";"{""unit"":""days"",""level"":""warning"",""duration"":""7""}";"0 0 * * *";"Europe/Paris"
"purgeSysClientNotification_4";"<EMAIL>";"sage";"SysClientNotification|purgeSysClientNotification|start";"""error"" client notification will automatically be deleted after seven days";"Y";"en-US";"{""unit"":""days"",""level"":""error"",""duration"":""7""}";"0 0 * * *";"Europe/Paris"
"purgeContentAddressableTables";"<EMAIL>";"sage";"SysChore|purgeContentAddressableTables|start";"Content-addressable tables are automatically purged once a month at 3.10AM on the 28th";"Y";"en-US";"{}";"10 3 28 * *";"Europe/Paris"
