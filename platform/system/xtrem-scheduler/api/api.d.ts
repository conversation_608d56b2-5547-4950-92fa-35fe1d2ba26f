declare module '@sage/xtrem-scheduler-api-partial' {
    import type { Package as SageXtremAuditing$Package } from '@sage/xtrem-auditing-api';
    import type { Package as SageXtremAuthorization$Package } from '@sage/xtrem-authorization-api';
    import type {
        Package as SageXtremCommunication$Package,
        SysNotificationLogEntry,
        SysNotificationLogEntryBinding,
        SysNotificationLogEntryInput,
        SysNotificationState,
        SysNotificationStateInput,
    } from '@sage/xtrem-communication-api';
    import type { Package as SageXtremCustomization$Package } from '@sage/xtrem-customization-api';
    import type { Package as SageXtremDashboard$Package } from '@sage/xtrem-dashboard-api';
    import type {
        MetaNodeFactory,
        MetaNodeOperation,
        Package as SageXtremMetadata$Package,
    } from '@sage/xtrem-metadata-api';
    import type { Package as SageXtremRouting$Package } from '@sage/xtrem-routing-api';
    import type { Package as SageXtremSystem$Package, SysVendor, User } from '@sage/xtrem-system-api';
    import type { Package as SageXtremWorkflow$Package } from '@sage/xtrem-workflow-api';
    import type {
        AggregateQueryOperation,
        AggregateReadOperation,
        AsyncOperation,
        ClientCollection,
        ClientNode,
        ClientNodeInput,
        CreateOperation,
        DeleteOperation,
        GetDefaultsOperation,
        GetDuplicateOperation,
        Operation as Node$Operation,
        QueryOperation,
        ReadOperation,
        UpdateByIdOperation,
        UpdateOperation,
        integer,
    } from '@sage/xtrem-client';
    export interface JobStatus$Enum {
        new: 0;
        pending: 1;
        running: 2;
        error: 3;
        done: 4;
        stopRequested: 5;
        stopped: 6;
        interruptRequested: 7;
        interrupted: 8;
        notResponding: 9;
    }
    export type JobStatus = keyof JobStatus$Enum;
    export interface ParameterType$Enum {
        string: 0;
        float: 1;
        double: 2;
        integer: 3;
        integerRange: 4;
        integerArray: 5;
        enumArray: 6;
        referenceArray: 7;
        stringArray: 8;
        decimalRange: 9;
        decimal: 10;
        enum: 11;
        boolean: 12;
        date: 13;
        dateRange: 14;
        datetimeRange: 15;
        time: 16;
        datetime: 17;
        reference: 18;
        uuid: 19;
    }
    export type ParameterType = keyof ParameterType$Enum;
    export interface SysChore extends ClientNode {}
    export interface SysChoreInput extends ClientNodeInput {}
    export interface SysChoreBinding extends ClientNode {}
    export interface SysChore$AsyncOperations {
        purgeContentAddressableTables: AsyncOperation<{}, boolean>;
    }
    export interface SysChore$Operations {
        asyncOperations: SysChore$AsyncOperations;
    }
    export interface SysJobSchedule extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        operation: MetaNodeOperation;
        description: string;
        isActive: boolean;
        startStamp: string;
        endStamp: string;
        executionUser: User;
        executionLocale: string;
        parameterValues: string;
        cronSchedule: string;
        cronTranslated: string;
        nextExecutionStamp: string;
        timeZone: string;
        executions: ClientCollection<SysNotificationState>;
        id: string;
    }
    export interface SysJobScheduleInput extends ClientNodeInput {
        _vendor?: integer | string;
        operation?: integer | string;
        description?: string;
        isActive?: boolean | string;
        startStamp?: string;
        endStamp?: string;
        executionUser?: integer | string;
        executionLocale?: string;
        parameterValues?: string;
        cronSchedule?: string;
        timeZone?: string;
        id?: string;
    }
    export interface SysJobScheduleBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        operation: MetaNodeOperation;
        description: string;
        isActive: boolean;
        startStamp: string;
        endStamp: string;
        executionUser: User;
        executionLocale: string;
        parameterValues: any;
        cronSchedule: string;
        cronTranslated: string;
        nextExecutionStamp: string;
        timeZone: string;
        executions: ClientCollection<SysNotificationState>;
        id: string;
    }
    export interface SysJobSchedule$Queries {
        timezones: Node$Operation<{}, string[]>;
        cronInfo: Node$Operation<{}, string[]>;
    }
    export interface SysJobSchedule$Mutations {
        purgeExecution: Node$Operation<
            {
                schedule?: string;
                allStatus?: boolean | string;
            },
            integer
        >;
    }
    export interface SysJobSchedule$AsyncOperations {
        bulkDelete: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SysJobSchedule$Lookups {
        _vendor: QueryOperation<SysVendor>;
        operation: QueryOperation<MetaNodeOperation>;
        executionUser: QueryOperation<User>;
    }
    export interface SysJobSchedule$Operations {
        query: QueryOperation<SysJobSchedule>;
        read: ReadOperation<SysJobSchedule>;
        aggregate: {
            read: AggregateReadOperation<SysJobSchedule>;
            query: AggregateQueryOperation<SysJobSchedule>;
        };
        queries: SysJobSchedule$Queries;
        create: CreateOperation<SysJobScheduleInput, SysJobSchedule>;
        getDuplicate: GetDuplicateOperation<SysJobSchedule>;
        update: UpdateOperation<SysJobScheduleInput, SysJobSchedule>;
        updateById: UpdateByIdOperation<SysJobScheduleInput, SysJobSchedule>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: SysJobSchedule$Mutations;
        asyncOperations: SysJobSchedule$AsyncOperations;
        lookups(dataOrId: string | { data: SysJobScheduleInput }): SysJobSchedule$Lookups;
        getDefaults: GetDefaultsOperation<SysJobSchedule>;
    }
    export interface SysClientNotificationExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        level: SysClientNotificationLevel;
        description: string;
        title: string;
        icon: string;
        isRead: boolean;
        shouldDisplayToast: boolean;
        recipient: User;
    }
    export interface SysClientNotificationInputExtension {
        level?: SysClientNotificationLevel;
        description?: string;
        title?: string;
        icon?: string;
        isRead?: boolean | string;
        shouldDisplayToast?: boolean | string;
        recipient?: integer | string;
    }
    export interface SysClientNotificationBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        level: SysClientNotificationLevel;
        description: string;
        title: string;
        icon: string;
        isRead: boolean;
        shouldDisplayToast: boolean;
        recipient: User;
    }
    export interface SysClientNotificationExtension$AsyncOperations {
        purgeSysClientNotification: AsyncOperation<
            {
                duration: integer | string;
                unit: string;
                level: SysClientNotificationLevel;
            },
            integer
        >;
    }
    export interface SysClientNotificationExtension$Operations {
        asyncOperations: SysClientNotificationExtension$AsyncOperations;
    }
    export interface SysNotificationStateExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        notificationId: string;
        isRead: boolean;
        originId: string;
        operationName: string;
        parameterValues: string;
        topic: string;
        replyId: string;
        locale: string;
        status: NotificationStatus;
        result: string;
        message: string;
        progress: string;
        progressBarPercent: string;
        timeStarted: string;
        timeEnded: string;
        notificationContext: string;
        user: User;
        isUserNotificationRequested: boolean;
        logs: ClientCollection<SysNotificationLogEntry>;
        operation: MetaNodeOperation;
        schedule: SysJobSchedule;
        isScheduled: boolean;
    }
    export interface SysNotificationStateInputExtension {
        notificationId?: string;
        isRead?: boolean | string;
        originId?: string;
        operationName?: string;
        parameterValues?: string;
        topic?: string;
        replyId?: string;
        locale?: string;
        status?: NotificationStatus;
        result?: string;
        message?: string;
        progress?: string;
        timeStarted?: string;
        timeEnded?: string;
        notificationContext?: string;
        user?: integer | string;
        isUserNotificationRequested?: boolean | string;
        logs?: Partial<SysNotificationLogEntryInput>[];
        operation?: integer | string;
        schedule?: integer | string;
    }
    export interface SysNotificationStateBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        notificationId: string;
        isRead: boolean;
        originId: string;
        operationName: string;
        parameterValues: any;
        topic: string;
        replyId: string;
        locale: string;
        status: NotificationStatus;
        result: any;
        message: string;
        progress: any;
        progressBarPercent: string;
        timeStarted: string;
        timeEnded: string;
        notificationContext: any;
        user: User;
        isUserNotificationRequested: boolean;
        logs: ClientCollection<SysNotificationLogEntryBinding>;
        operation: MetaNodeOperation;
        schedule: SysJobSchedule;
        isScheduled: boolean;
    }
    export interface SysNotificationStateExtension$Mutations {
        execute: Node$Operation<
            {
                jobExecution?: {
                    operation?: integer | string;
                    parameterValues?: string;
                };
            },
            integer
        >;
        stop: Node$Operation<
            {
                jobExecutionSysId?: string;
                reason?: string;
            },
            boolean
        >;
        notRespondingJob: Node$Operation<
            {
                duration?: integer | string;
                unit?: string;
            },
            integer
        >;
    }
    export interface SysNotificationStateExtension$AsyncOperations {
        markAsRead: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        bulkStop: AsyncOperation<
            {
                filter?: string;
                reason?: string;
            },
            boolean
        >;
        purgeHistory: AsyncOperation<
            {
                duration: integer | string;
                unit: string;
                operationNames?: string[];
            },
            integer
        >;
        simulateAsyncMutation: AsyncOperation<
            {
                id: string;
                delayInSec: integer | string;
                throwAt?: integer | string;
            },
            {
                result: string;
            }
        >;
    }
    export interface SysNotificationStateExtension$Lookups {
        schedule: QueryOperation<SysJobSchedule>;
    }
    export interface SysNotificationStateExtension$Operations {
        mutations: SysNotificationStateExtension$Mutations;
        asyncOperations: SysNotificationStateExtension$AsyncOperations;
        lookups(dataOrId: string | { data: SysNotificationStateInput }): SysNotificationStateExtension$Lookups;
        getDefaults: GetDefaultsOperation<SysNotificationState>;
    }
    export interface Package {
        '@sage/xtrem-scheduler/SysChore': SysChore$Operations;
        '@sage/xtrem-scheduler/SysJobSchedule': SysJobSchedule$Operations;
    }
    export interface GraphApi
        extends Package,
            SageXtremAuditing$Package,
            SageXtremAuthorization$Package,
            SageXtremCommunication$Package,
            SageXtremCustomization$Package,
            SageXtremDashboard$Package,
            SageXtremMetadata$Package,
            SageXtremRouting$Package,
            SageXtremSystem$Package,
            SageXtremWorkflow$Package {}
}
declare module '@sage/xtrem-scheduler-api' {
    export type * from '@sage/xtrem-scheduler-api-partial';
}
declare module '@sage/xtrem-auditing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-scheduler-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-authorization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-scheduler-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-communication-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-scheduler-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-customization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-scheduler-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-dashboard-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-scheduler-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-metadata-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-scheduler-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-routing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-scheduler-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-scheduler-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-workflow-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-scheduler-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type {
        SysClientNotificationBindingExtension,
        SysClientNotificationExtension,
        SysClientNotificationExtension$AsyncOperations,
        SysClientNotificationExtension$Operations,
        SysClientNotificationInputExtension,
    } from '@sage/xtrem-scheduler-api';
    export interface SysClientNotification extends SysClientNotificationExtension {}
    export interface SysClientNotificationBinding extends SysClientNotificationBindingExtension {}
    export interface SysClientNotificationInput extends SysClientNotificationInputExtension {}
    export interface SysClientNotification$AsyncOperations extends SysClientNotificationExtension$AsyncOperations {}
    export interface SysClientNotification$Operations extends SysClientNotificationExtension$Operations {}
}
declare module '@sage/xtrem-communication-api-partial' {
    import type {
        SysNotificationStateBindingExtension,
        SysNotificationStateExtension,
        SysNotificationStateExtension$AsyncOperations,
        SysNotificationStateExtension$Lookups,
        SysNotificationStateExtension$Mutations,
        SysNotificationStateExtension$Operations,
        SysNotificationStateInputExtension,
    } from '@sage/xtrem-scheduler-api';
    export interface SysNotificationState extends SysNotificationStateExtension {}
    export interface SysNotificationStateBinding extends SysNotificationStateBindingExtension {}
    export interface SysNotificationStateInput extends SysNotificationStateInputExtension {}
    export interface SysNotificationState$Lookups extends SysNotificationStateExtension$Lookups {}
    export interface SysNotificationState$Mutations extends SysNotificationStateExtension$Mutations {}
    export interface SysNotificationState$AsyncOperations extends SysNotificationStateExtension$AsyncOperations {}
    export interface SysNotificationState$Operations extends SysNotificationStateExtension$Operations {}
}
