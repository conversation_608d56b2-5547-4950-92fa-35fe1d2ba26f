import { StringDataType, ValidationContext } from '@sage/xtrem-core';
import { CronExpressionParser } from 'cron-parser';
import { SysJobSchedule } from '../nodes/sys-job-schedule';

export class CronScheduleDataType<T> extends StringDataType<T> {
    /**
     * Cron schedule string control
     * https://www.npmjs.com/package/cron-schedule)
         ┌────────────── second (optional)\
         │ ┌──────────── minute\
         │ │ ┌────────── hour\
         │ │ │ ┌──────── day of month\
         │ │ │ │ ┌────── month\
         │ │ │ │ │ ┌──── day of week\
         │ │ │ │ │ │\
         │ │ │ │ │ │\
        `* * * * * *`
     */

    public override async controlValue(node: T, cx: ValidationContext, val: string): Promise<void> {
        await super.controlValue(node, cx, val);
        try {
            CronExpressionParser.parse(val);
        } catch (err: any) {
            cx.error.addLocalized(
                '@sage/xtrem-scheduler/data-types/cron_schedule__invalid_schedule',
                'Schedule value is invalid: ({{value}}).',
                {
                    value: err && err.message ? err.message : String(err),
                },
            );
        }
    }
}

export const cronSchedule = new CronScheduleDataType<SysJobSchedule>({ maxLength: 128 });
