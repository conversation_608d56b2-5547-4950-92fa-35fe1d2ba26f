import { StringDataType, ValidationContext } from '@sage/xtrem-core';
import * as momentTimezone from 'moment-timezone';
import { SysJobSchedule } from '../nodes/sys-job-schedule';

export class TimezoneDataType<T> extends StringDataType<T> {
    /**
     * https://momentjs.com/timezone/docs/
     */
    public override async controlValue(node: T, cx: ValidationContext, val: string): Promise<void> {
        await super.controlValue(node, cx, val);
        const isValid = !!momentTimezone.tz.zone(val);
        if (!isValid)
            cx.error.addLocalized(
                '@sage/xtrem-scheduler/data-types/timezone__invalid_timezone',
                'Timezone value is invalid: ({{value}}).',
                {
                    value: val,
                },
            );
    }
}

export const timezoneSchedule = new TimezoneDataType<SysJobSchedule>({ maxLength: 128 });
