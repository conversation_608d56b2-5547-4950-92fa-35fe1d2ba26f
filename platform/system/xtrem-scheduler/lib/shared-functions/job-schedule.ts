import { Datetime } from '@sage/xtrem-date-time';
import { CronExpressionParser } from 'cron-parser';

export enum NextExecutionError {
    cronStringStartDateEmpty,
    noNextExecution,
    parse,
}

export function nextExecution(setup: {
    cronString: string;
    startDate?: Datetime;
    stopDate?: Datetime;
    tz?: string;
}): Datetime | NextExecutionError {
    if (setup.cronString === '') {
        if (setup.startDate) {
            return setup.startDate;
        }
        return NextExecutionError.cronStringStartDateEmpty;
    }

    const cronExpression = CronExpressionParser.parse(setup.cronString, {
        ...(setup.startDate && { startDate: setup.startDate?.toJsDate() }),
        ...(setup.stopDate && { stopDate: setup.stopDate?.toJsDate() }),
        tz: setup.tz || Intl?.DateTimeFormat()?.resolvedOptions()?.timeZone,
        currentDate: Datetime.now().format('base', 'YYYY-MM-DD HH:mm:ss'),
    });

    if (!cronExpression.hasNext()) {
        return NextExecutionError.noNextExecution;
    }

    try {
        const isoDatetime = cronExpression.next().toISOString();
        if (!isoDatetime) {
            return NextExecutionError.noNextExecution;
        }
        return Datetime.parse(isoDatetime);
    } catch (ex) {
        return NextExecutionError.parse;
    }
}
