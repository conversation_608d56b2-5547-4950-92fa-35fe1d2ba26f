import { EnumDataType } from '@sage/xtrem-core';

export enum ParameterTypeEnum {
    string,
    float,
    double,
    integer,
    integerRange,
    integerArray,
    enumArray,
    referenceArray,
    stringArray,
    decimalRange,
    decimal,
    enum,
    boolean,
    date,
    dateRange,
    datetimeRange,
    time,
    datetime,
    reference,
    uuid,
}

export type ParameterType = keyof typeof ParameterTypeEnum;

export const ParameterTypeDataType = new EnumDataType<ParameterType>({
    enum: ParameterTypeEnum,
    filename: __filename,
});
