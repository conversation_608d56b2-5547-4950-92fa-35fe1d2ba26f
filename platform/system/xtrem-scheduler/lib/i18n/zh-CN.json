{" @sage/xtrem-scheduler/nodes__sys-job-schedule__once": "一次", "@sage/xtrem-scheduler/activity__sys_job_schedule__name": "系统工作排程", "@sage/xtrem-scheduler/data_types__cron_schedule__name": "定时排程", "@sage/xtrem-scheduler/data_types__job_status_enum__name": "工作状态枚举", "@sage/xtrem-scheduler/data_types__parameter_type_enum__name": "参数类型枚举", "@sage/xtrem-scheduler/data_types__timezone_schedule__name": "时区排程", "@sage/xtrem-scheduler/data-types/cron_schedule__invalid_schedule": "排程值错误：{{value}}。", "@sage/xtrem-scheduler/data-types/timezone__invalid_timezone": "时区值错误：{{value}}。", "@sage/xtrem-scheduler/enums__job_status__done": "已完成", "@sage/xtrem-scheduler/enums__job_status__error": "错误", "@sage/xtrem-scheduler/enums__job_status__interrupted": "已中断", "@sage/xtrem-scheduler/enums__job_status__interruptRequested": "中断请求", "@sage/xtrem-scheduler/enums__job_status__new": "新建", "@sage/xtrem-scheduler/enums__job_status__notResponding": "无响应", "@sage/xtrem-scheduler/enums__job_status__pending": "待处理", "@sage/xtrem-scheduler/enums__job_status__running": "运行中", "@sage/xtrem-scheduler/enums__job_status__stopped": "已终止", "@sage/xtrem-scheduler/enums__job_status__stopRequested": "终止请求", "@sage/xtrem-scheduler/enums__log_severity__error": "错误", "@sage/xtrem-scheduler/enums__log_severity__exception": "异常", "@sage/xtrem-scheduler/enums__log_severity__info": "信息", "@sage/xtrem-scheduler/enums__log_severity__test": "测试", "@sage/xtrem-scheduler/enums__log_severity__warning": "警告", "@sage/xtrem-scheduler/enums__parameter_type__boolean": "布尔型", "@sage/xtrem-scheduler/enums__parameter_type__date": "日期", "@sage/xtrem-scheduler/enums__parameter_type__dateRange": "Date范围", "@sage/xtrem-scheduler/enums__parameter_type__datetime": "Datetime", "@sage/xtrem-scheduler/enums__parameter_type__datetimeRange": "Datetime范围", "@sage/xtrem-scheduler/enums__parameter_type__decimal": "decimal", "@sage/xtrem-scheduler/enums__parameter_type__decimalRange": "Decimal范围", "@sage/xtrem-scheduler/enums__parameter_type__double": "Double", "@sage/xtrem-scheduler/enums__parameter_type__enum": "Enum", "@sage/xtrem-scheduler/enums__parameter_type__enumArray": "Enum数列", "@sage/xtrem-scheduler/enums__parameter_type__float": "Float", "@sage/xtrem-scheduler/enums__parameter_type__integer": "Integer", "@sage/xtrem-scheduler/enums__parameter_type__integerArray": "Integer数列", "@sage/xtrem-scheduler/enums__parameter_type__integerRange": "Integer范围", "@sage/xtrem-scheduler/enums__parameter_type__reference": "Reference", "@sage/xtrem-scheduler/enums__parameter_type__referenceArray": "Reference数列", "@sage/xtrem-scheduler/enums__parameter_type__string": "String", "@sage/xtrem-scheduler/enums__parameter_type__stringArray": "String数列", "@sage/xtrem-scheduler/enums__parameter_type__time": "Time", "@sage/xtrem-scheduler/enums__parameter_type__uuid": "UUID", "@sage/xtrem-scheduler/job-manager_started-by-scheduler": "由排程程序启动：{{description}}", "@sage/xtrem-scheduler/node-extensions__sys_client_notification_extension__asyncMutation__purgeSysClientNotification": "清除系统客户端通知", "@sage/xtrem-scheduler/node-extensions__sys_client_notification_extension__asyncMutation__purgeSysClientNotification__failed": "清除系统客户端通知失败。", "@sage/xtrem-scheduler/node-extensions__sys_client_notification_extension__asyncMutation__purgeSysClientNotification__parameter__duration": "持续时间", "@sage/xtrem-scheduler/node-extensions__sys_client_notification_extension__asyncMutation__purgeSysClientNotification__parameter__level": "层级", "@sage/xtrem-scheduler/node-extensions__sys_client_notification_extension__asyncMutation__purgeSysClientNotification__parameter__unit": "单位", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__purgeHistory": "清除历史", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__purgeHistory__failed": "清除历史记录失败。", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__purgeHistory__parameter__duration": "持续时间", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__purgeHistory__parameter__operationNames": "工序名称", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__purgeHistory__parameter__unit": "单位", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__simulateAsyncMutation": "模拟异步突变", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__simulateAsyncMutation__failed": "模拟异步变更失败。", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__simulateAsyncMutation__parameter__delayInSec": "延迟几秒", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__simulateAsyncMutation__parameter__id": "ID", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__simulateAsyncMutation__parameter__throwAt": "投向", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__bulkMutation__bulkStop": "批量停止", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__bulkMutation__bulkStop__failed": "批量停止失败。", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__bulkMutation__bulkStop__parameter__reason": "原因", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__bulkMutation__markAsRead": "标记为已读", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__bulkMutation__markAsRead__failed": "标记为已读失败。", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__execute": "执行", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__execute__failed": "执行失败。", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__execute__parameter__jobExecution": "工作执行", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__notRespondingJob": "作业无响应", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__notRespondingJob__failed": "未响应的作业执行失败。", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__notRespondingJob__parameter__duration": "持续时间", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__notRespondingJob__parameter__unit": "单位", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__stop": "停止", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__stop__failed": "停止失败。", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__stop__parameter__jobExecutionSysId": "工作执行系统ID", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__stop__parameter__reason": "原因", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__property__isScheduled": "已排程", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__property__schedule": "排程", "@sage/xtrem-scheduler/nodes__sys_chore__asyncMutation__purgeContentAddressableTables": "清除内容可寻址表", "@sage/xtrem-scheduler/nodes__sys_chore__asyncMutation__purgeContentAddressableTables__failed": "清除内容可寻址表失败。", "@sage/xtrem-scheduler/nodes__sys_chore__node_name": "系统任务", "@sage/xtrem-scheduler/nodes__sys_job_schedule__asyncMutation__asyncExport": "导出", "@sage/xtrem-scheduler/nodes__sys_job_schedule__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-scheduler/nodes__sys_job_schedule__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-scheduler/nodes__sys_job_schedule__bulkMutation__bulkDelete": "批量删除", "@sage/xtrem-scheduler/nodes__sys_job_schedule__mutation__purgeExecution": "清除执行", "@sage/xtrem-scheduler/nodes__sys_job_schedule__mutation__purgeExecution__failed": "清除执行失败。", "@sage/xtrem-scheduler/nodes__sys_job_schedule__mutation__purgeExecution__parameter__allStatus": "所有状态", "@sage/xtrem-scheduler/nodes__sys_job_schedule__mutation__purgeExecution__parameter__schedule": "排程", "@sage/xtrem-scheduler/nodes__sys_job_schedule__node_name": "系统工作排程", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__cronSchedule": "定时排程", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__cronTranslated": "定时翻译", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__description": "描述", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__endStamp": "结束标记", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__executionLocale": "执行区域设置", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__executions": "执行", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__executionUser": "执行用户", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__id": "ID", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__isActive": "是激活的", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__nextExecutionStamp": "下一个执行标志", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__operation": "工序", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__parameterValues": "参数值", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__startStamp": "开始标记", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__sysJobDefinition": "系统工作定义", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__timeZone": "时区", "@sage/xtrem-scheduler/nodes__sys_job_schedule__query__cronInfo": "定时信息", "@sage/xtrem-scheduler/nodes__sys_job_schedule__query__cronInfo__failed": "定时信息失败。", "@sage/xtrem-scheduler/nodes__sys_job_schedule__query__timezones": "时区", "@sage/xtrem-scheduler/nodes__sys_job_schedule__query__timezones__failed": "时区失败。", "@sage/xtrem-scheduler/nodes__sys-job-execution_cron-or-start-date": "需要开始日期或定时字符串 ", "@sage/xtrem-scheduler/nodes__sys-job-execution_no-next-execution": "该工作将不会被执行。", "@sage/xtrem-scheduler/nodes__sys-job-execution_operation-schedulable": "此工序无法排程", "@sage/xtrem-scheduler/nodes__sys-job-execution_parsing-error": "解析下一个排程时出现问题。", "@sage/xtrem-scheduler/nodes__sys-job-schedule__execution_user_not_authorized": "您只能将执行用户分配给您自己。", "@sage/xtrem-scheduler/nodes__sys-job-schedule__no-cron-expression-no-startStamp": "如果未定义定时表达式，则需要开始日期时间。", "@sage/xtrem-scheduler/nodes__sys-job-schedule__once": "一次", "@sage/xtrem-scheduler/nodes_sys_notification_state_cannot_stop_process_not_running": "此进程无法停止，因为它已停止运行。", "@sage/xtrem-scheduler/package__name": "排程程序", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension____navigationPanel__listItem__line1__title": "排程", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension____navigationPanel__listItem__line2__title": "工序", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension____navigationPanel__listItem__line7__title": "周期性的", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__execute____title": "执行", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__operation____columns__title__factory__name": "节点", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__operation____columns__title__isSchedulable": "可排程", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__operation____columns__title__name": "工序", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__operation____title": "工序", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__schedule____title": "排程", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__schedulePage____title": "排程", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__stop____title": "停止", "@sage/xtrem-scheduler/pages__operation_panel____title": "工序面板", "@sage/xtrem-scheduler/pages__operation_panel__generalSection____title": "常规", "@sage/xtrem-scheduler/pages__operation_panel__parameters____columns__title__name": "名称", "@sage/xtrem-scheduler/pages__operation_panel__parameters____columns__title__value": "值", "@sage/xtrem-scheduler/pages__operation_panel__parameters____title": "参数", "@sage/xtrem-scheduler/pages__sys_job_definition____navigationPanel__listItem__title__title": "名称", "@sage/xtrem-scheduler/pages__sys_job_definition____objectTypePlural": "批次任务", "@sage/xtrem-scheduler/pages__sys_job_definition____objectTypeSingular": "批次任务", "@sage/xtrem-scheduler/pages__sys_job_definition____title": "批次任务", "@sage/xtrem-scheduler/pages__sys_job_definition__description____title": "描述", "@sage/xtrem-scheduler/pages__sys_job_definition__generalSection____title": "常规", "@sage/xtrem-scheduler/pages__sys_job_definition__isActive____title": "激活的", "@sage/xtrem-scheduler/pages__sys_job_definition__name____title": "名称", "@sage/xtrem-scheduler/pages__sys_job_execution____navigationPanel__listItem__line__title": "节点", "@sage/xtrem-scheduler/pages__sys_job_execution____navigationPanel__listItem__line2__title": "程序包", "@sage/xtrem-scheduler/pages__sys_job_execution____navigationPanel__listItem__line3__title": "已开始", "@sage/xtrem-scheduler/pages__sys_job_execution____navigationPanel__listItem__line4__title": "结束", "@sage/xtrem-scheduler/pages__sys_job_execution____navigationPanel__listItem__title__title": "名称", "@sage/xtrem-scheduler/pages__sys_job_execution____navigationPanel__optionsMenu__title": "全部", "@sage/xtrem-scheduler/pages__sys_job_execution____objectTypePlural": "工作执行", "@sage/xtrem-scheduler/pages__sys_job_execution____objectTypeSingular": "工作执行", "@sage/xtrem-scheduler/pages__sys_job_execution____title": "工作执行", "@sage/xtrem-scheduler/pages__sys_job_execution__execute____title": "", "@sage/xtrem-scheduler/pages__sys_job_execution__generalSection____title": "常规", "@sage/xtrem-scheduler/pages__sys_job_execution__logs____columns__title__lineNumber": "#", "@sage/xtrem-scheduler/pages__sys_job_execution__logs____columns__title__severity": "严重程度", "@sage/xtrem-scheduler/pages__sys_job_execution__logs____columns__title__text": "消息", "@sage/xtrem-scheduler/pages__sys_job_execution__logs____columns__title__timestamp": "时间", "@sage/xtrem-scheduler/pages__sys_job_execution__logs____title": "日志", "@sage/xtrem-scheduler/pages__sys_job_execution__logsSection____title": "日志", "@sage/xtrem-scheduler/pages__sys_job_execution__operation____columns__title": "节点", "@sage/xtrem-scheduler/pages__sys_job_execution__operation____columns__title__isSchedulable": "可排程", "@sage/xtrem-scheduler/pages__sys_job_execution__operation____columns__title__name": "工序", "@sage/xtrem-scheduler/pages__sys_job_execution__operation____columns__title__name__2": "", "@sage/xtrem-scheduler/pages__sys_job_execution__operation____title": "工序", "@sage/xtrem-scheduler/pages__sys_job_execution__parameters____columns__title__name": "名称", "@sage/xtrem-scheduler/pages__sys_job_execution__parameters____columns__title__value": "值", "@sage/xtrem-scheduler/pages__sys_job_execution__parameters____title": "参数", "@sage/xtrem-scheduler/pages__sys_job_execution__status____title": "状态", "@sage/xtrem-scheduler/pages__sys_job_execution__timeEnded____title": "结束时间", "@sage/xtrem-scheduler/pages__sys_job_execution__timeStarted____title": "开始时间", "@sage/xtrem-scheduler/pages__sys_job_execution__trackingId____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__bulkActions__title": "删除", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__line_5__title": "下一个排程", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__line1__title": "用户", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__line2Right__title": "定时排程", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__line3__title": "激活的", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__line3Right__title": "工序", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__line5__title": "下一个排程", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__title__title": "ID", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__titleRight__title": "描述", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__optionsMenu__title": "全部", "@sage/xtrem-scheduler/pages__sys_job_schedule____objectTypePlural": "工作排程", "@sage/xtrem-scheduler/pages__sys_job_schedule____objectTypeSingular": "工作排程", "@sage/xtrem-scheduler/pages__sys_job_schedule____title": "工作排程", "@sage/xtrem-scheduler/pages__sys_job_schedule__createCrud____title": "创建", "@sage/xtrem-scheduler/pages__sys_job_schedule__cronSchedule____title": "定时表达式", "@sage/xtrem-scheduler/pages__sys_job_schedule__cronTranslated____title": "翻译定时", "@sage/xtrem-scheduler/pages__sys_job_schedule__description____title": "描述", "@sage/xtrem-scheduler/pages__sys_job_schedule__endStamp____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule__execute____title": "现在运行", "@sage/xtrem-scheduler/pages__sys_job_schedule__executeSection____title": "执行历史", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____columns__title___id": "明细", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____columns__title__notificationId": "明细", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____columns__title__status": "状态", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____columns__title__timeEnded": "结束", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____columns__title__timeStarted": "开始", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____dropdownActions__title": "停止", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____title": "已执行工作", "@sage/xtrem-scheduler/pages__sys_job_schedule__executionUser____columns__title__email": "Email", "@sage/xtrem-scheduler/pages__sys_job_schedule__executionUser____columns__title__firstName": "名称", "@sage/xtrem-scheduler/pages__sys_job_schedule__executionUser____columns__title__lastName": "姓氏", "@sage/xtrem-scheduler/pages__sys_job_schedule__executionUser____title": "执行用户", "@sage/xtrem-scheduler/pages__sys_job_schedule__generalSection____title": "常规", "@sage/xtrem-scheduler/pages__sys_job_schedule__id____title": "ID", "@sage/xtrem-scheduler/pages__sys_job_schedule__isActive____title": "激活的", "@sage/xtrem-scheduler/pages__sys_job_schedule__operation____columns__title__description": "工序", "@sage/xtrem-scheduler/pages__sys_job_schedule__operation____columns__title__factory__name": "节点", "@sage/xtrem-scheduler/pages__sys_job_schedule__operation____columns__title__isSchedulable": "可排程", "@sage/xtrem-scheduler/pages__sys_job_schedule__operation____columns__title__title": "工序", "@sage/xtrem-scheduler/pages__sys_job_schedule__operation____title": "工序", "@sage/xtrem-scheduler/pages__sys_job_schedule__parameters____columns__title__name": "名称", "@sage/xtrem-scheduler/pages__sys_job_schedule__parameters____columns__title__value": "值", "@sage/xtrem-scheduler/pages__sys_job_schedule__parameters____title": "参数", "@sage/xtrem-scheduler/pages__sys_job_schedule__purgeExecution____title": "清除执行", "@sage/xtrem-scheduler/pages__sys_job_schedule__refreshExecutedJobs____title": "刷新", "@sage/xtrem-scheduler/pages__sys_job_schedule__refreshExecutions____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule__startStamp____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule__timeZone____title": "时区", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard____title": "新的批次任务", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__atJobHour____placeholder": "时", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__atJobHour____title": "在某时", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__atJobMinute____placeholder": "分", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__batch_task_scheduled": "已排程批次任务：{{0}}", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__batch_task_started": "批次任务已启动", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__cronString____title": "定时表达式", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__daily": "每天", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__days____title": "日", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__defineSection____title": "定义", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__friday": "周五", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__invalid_date": "无效日期", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__invalid_date_with_argument": "无效日期：{{0}}", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__job____columns__title__description": "工序", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__job____columns__title__factory__name": "来源", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__job____columns__title__name": "Operation", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__job____columns__title__title": "工序", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__job____title": "工作定义", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobDate____title": "开始日期", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobHour____placeholder": "时", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobHour____title": "开始时间", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobMinute____placeholder": "分", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobParameters____columns__title__name": "参数", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobParameters____columns__title__value": "值", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobParameters____title": "参数", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobSchedule____title": "排程", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__monday": "周一", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__monthly": "每月", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__needs_to_be_a_number": "需要是一个数字。", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__needs_to_be_either_true_or_false": "需要是下列之一：{{0}}，{{1}}。", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__needs_to_be_in_the_future": "需要在未来成为", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__needs_to_be_in_the_future_with_argument": "需要在未来成为：{{0}}", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__now": "Now", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__once": "Once", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__reccurence____title": "周期", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__recurrence-not-set": "您需要设置频率。", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__recurring": "周期性的", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__run_now": "现在运行", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__run_once": "运行一次", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__saturday": "周六", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__scheduleSection____title": "排程", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__settingsSection____title": "设置", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__stop_date_must_be_later_start_date": "停止日期需要晚于开始日期。", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__stopJobDate____title": "停止日期", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__stopJobHour____placeholder": "时", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__stopJobHour____title": "停止时间", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__stopJobMinute____placeholder": "分", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__sunday": "周日", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__thursday": "周四", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__tuesday": "周二", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__wednesday": "周三", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__weekly": "每周", "@sage/xtrem-scheduler/pages__sys_notification_state__ask_by_user": "用户提问", "@sage/xtrem-scheduler/pages__workflow_event_schedule____title": "触发配置", "@sage/xtrem-scheduler/pages__workflow_event_schedule__cronSchedule____title": "定时表达式", "@sage/xtrem-scheduler/pages__workflow_event_schedule__endHour____infoMessage": "格式：hh：mm", "@sage/xtrem-scheduler/pages__workflow_event_schedule__endHour____title": "结束时间", "@sage/xtrem-scheduler/pages__workflow_event_schedule__endStamp____title": "结束日期", "@sage/xtrem-scheduler/pages__workflow_event_schedule__mainSection____title": "排程", "@sage/xtrem-scheduler/pages__workflow_event_schedule__startHour____infoMessage": "格式：hh：mm", "@sage/xtrem-scheduler/pages__workflow_event_schedule__startHour____title": "开始时间", "@sage/xtrem-scheduler/pages__workflow_event_schedule__startStamp____title": "开始日期", "@sage/xtrem-scheduler/pages__workflow_event_schedule__timeZone____title": "时区", "@sage/xtrem-scheduler/pages-confirm-cancel": "取消", "@sage/xtrem-scheduler/pages-confirm-purge": "删除", "@sage/xtrem-scheduler/permission__manage__name": "管理", "@sage/xtrem-scheduler/permission__read__name": "读取", "@sage/xtrem-scheduler/purge-sys-execute-history": "您将删除执行历史。", "@sage/xtrem-scheduler/purge-sys-message-history-title": "删除历史", "@sage/xtrem-scheduler/stickers__notification_sticker____title": "通知中心", "@sage/xtrem-scheduler/stickers__notification_sticker__block1____title": "版块 1", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____columns__title__lastMessage": "最后的消息", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____columns__title__notificationId": "明细", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____columns__title__operationName": "名称", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____columns__title__status": "状态", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____columns__title__timeEnded": "结束", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____columns__title__timeStarted": "开始", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____emptyStateText": "无通知", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____title": "已执行工作", "@sage/xtrem-scheduler/stickers__notification_sticker__isEnabled____title": "启用通知", "@sage/xtrem-scheduler/stickers__notification_sticker__markAsRead____title": "标记为已读", "@sage/xtrem-scheduler/stickers__notification_sticker__section1____title": "POC：通知中心", "@sage/xtrem-scheduler/sys-client-notification__number_of_records_purged": "{{numberDeleted}}记录已删除。", "@sage/xtrem-scheduler/sys-job-definition/validate-parameters-reference-type": "参数{{parameterName}}是参考类型，但没有指定节点名称。", "@sage/xtrem-scheduler/sys-job-schedule/invalid-parameters-value": "参数{{failedParsedParameterNames}}的值无效。", "@sage/xtrem-scheduler/sys-job-schedule/missing-parameters": "工作{{jobDefinitionName}}缺少必填参数{{missingParameterNames}}。", "@sage/xtrem-scheduler/sys-job-schedule/validate-parameters": "{{jobDefinitionName}}工作排程包含的参数比工作定义的更多。", "@sage/xtrem-scheduler/sys-notification-state__bulkStop_only_pending-running": "您只能停止运行中或待处理的作业。", "@sage/xtrem-scheduler/sys-notification-state__number_of_records_purged": "{{numberDeleted}}记录已删除"}