{" @sage/xtrem-scheduler/nodes__sys-job-schedule__once": "Une fois", "@sage/xtrem-scheduler/activity__sys_job_schedule__name": "Planification des tâches système", "@sage/xtrem-scheduler/data_types__cron_schedule__name": "Planification Cron", "@sage/xtrem-scheduler/data_types__job_status_enum__name": "Enum du statut de tâche", "@sage/xtrem-scheduler/data_types__parameter_type_enum__name": "Enum de type de paramètre", "@sage/xtrem-scheduler/data_types__timezone_schedule__name": "Calendrier fuseaux horaires", "@sage/xtrem-scheduler/data-types/cron_schedule__invalid_schedule": "La valeur de planification est incorrecte : ({{value}}).", "@sage/xtrem-scheduler/data-types/timezone__invalid_timezone": "La valeur du fuseau horaire est incorrecte : {{value}}.", "@sage/xtrem-scheduler/enums__job_status__done": "Effectuée", "@sage/xtrem-scheduler/enums__job_status__error": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/enums__job_status__interrupted": "Interrompue", "@sage/xtrem-scheduler/enums__job_status__interruptRequested": "Interruption requise", "@sage/xtrem-scheduler/enums__job_status__new": "Nouvelle", "@sage/xtrem-scheduler/enums__job_status__notResponding": "Pas de réponse", "@sage/xtrem-scheduler/enums__job_status__pending": "En attente", "@sage/xtrem-scheduler/enums__job_status__running": "En cours", "@sage/xtrem-scheduler/enums__job_status__stopped": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/enums__job_status__stopRequested": "<PERSON><PERSON><PERSON><PERSON> requis", "@sage/xtrem-scheduler/enums__log_severity__error": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/enums__log_severity__exception": "Exception", "@sage/xtrem-scheduler/enums__log_severity__info": "Info", "@sage/xtrem-scheduler/enums__log_severity__test": "Test", "@sage/xtrem-scheduler/enums__log_severity__warning": "Avertissement", "@sage/xtrem-scheduler/enums__parameter_type__boolean": "Booléen", "@sage/xtrem-scheduler/enums__parameter_type__date": "Date", "@sage/xtrem-scheduler/enums__parameter_type__dateRange": "<PERSON><PERSON> de <PERSON>", "@sage/xtrem-scheduler/enums__parameter_type__datetime": "Date / Heure", "@sage/xtrem-scheduler/enums__parameter_type__datetimeRange": "Borne date / heure", "@sage/xtrem-scheduler/enums__parameter_type__decimal": "Décimale", "@sage/xtrem-scheduler/enums__parameter_type__decimalRange": "Borme de décimales", "@sage/xtrem-scheduler/enums__parameter_type__double": "Double", "@sage/xtrem-scheduler/enums__parameter_type__enum": "Enum", "@sage/xtrem-scheduler/enums__parameter_type__enumArray": "Enum array", "@sage/xtrem-scheduler/enums__parameter_type__float": "Flottant", "@sage/xtrem-scheduler/enums__parameter_type__integer": "Nombre entier", "@sage/xtrem-scheduler/enums__parameter_type__integerArray": "Tableau de nombres entiers", "@sage/xtrem-scheduler/enums__parameter_type__integerRange": "Borne de nombres entiers", "@sage/xtrem-scheduler/enums__parameter_type__reference": "Référence", "@sage/xtrem-scheduler/enums__parameter_type__referenceArray": "Tableau de références", "@sage/xtrem-scheduler/enums__parameter_type__string": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/enums__parameter_type__stringArray": "Tableau de chaînes de caractères", "@sage/xtrem-scheduler/enums__parameter_type__time": "<PERSON><PERSON>", "@sage/xtrem-scheduler/enums__parameter_type__uuid": "UUID", "@sage/xtrem-scheduler/job-manager_started-by-scheduler": "Lancée par le planificateur : {{description}}", "@sage/xtrem-scheduler/node-extensions__sys_client_notification_extension__asyncMutation__purgeSysClientNotification": "Purger notification client système", "@sage/xtrem-scheduler/node-extensions__sys_client_notification_extension__asyncMutation__purgeSysClientNotification__failed": "Échec purge notification client système.", "@sage/xtrem-scheduler/node-extensions__sys_client_notification_extension__asyncMutation__purgeSysClientNotification__parameter__duration": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/node-extensions__sys_client_notification_extension__asyncMutation__purgeSysClientNotification__parameter__level": "Niveau", "@sage/xtrem-scheduler/node-extensions__sys_client_notification_extension__asyncMutation__purgeSysClientNotification__parameter__unit": "Unité", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__purgeHistory": "Purger historique", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__purgeHistory__failed": "La purge de l'historique a échoué.", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__purgeHistory__parameter__duration": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__purgeHistory__parameter__operationNames": "Noms des opérations", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__purgeHistory__parameter__unit": "Unité", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__simulateAsyncMutation": "Simuler mutation asynchrone", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__simulateAsyncMutation__failed": "La mutation de simulation d'asynch a <PERSON><PERSON>.", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__simulateAsyncMutation__parameter__delayInSec": "Retard en secondes", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__simulateAsyncMutation__parameter__id": "Code", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__simulateAsyncMutation__parameter__throwAt": "<PERSON><PERSON>", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__bulkMutation__bulkStop": "<PERSON><PERSON><PERSON><PERSON> de <PERSON>e", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__bulkMutation__bulkStop__failed": "L'arrêt en masse a échoué.", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__bulkMutation__bulkStop__parameter__reason": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__bulkMutation__markAsRead": "Marquer comme lu", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__bulkMutation__markAsRead__failed": "Marquer comme lu a <PERSON>.", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__execute": "Exécuter", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__execute__failed": "L'exécution a échoué.", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__execute__parameter__jobExecution": "Exécution de tâche", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__notRespondingJob": "Pas de réponse de la tâche", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__notRespondingJob__failed": "Une tâche sans réponse a échoué.", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__notRespondingJob__parameter__duration": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__notRespondingJob__parameter__unit": "Unité", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__stop": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__stop__failed": "<PERSON>'arr<PERSON>t a échoué.", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__stop__parameter__jobExecutionSysId": "ID système exécution de tâches", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__stop__parameter__reason": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__property__isScheduled": "Est planifié", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__property__schedule": "Planification", "@sage/xtrem-scheduler/nodes__sys_chore__asyncMutation__purgeContentAddressableTables": "Purger le contenu des tables addressables", "@sage/xtrem-scheduler/nodes__sys_chore__asyncMutation__purgeContentAddressableTables__failed": "La purge du contenu des tables adressables a échoué.", "@sage/xtrem-scheduler/nodes__sys_chore__node_name": "Procédures système", "@sage/xtrem-scheduler/nodes__sys_job_schedule__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-scheduler/nodes__sys_job_schedule__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-scheduler/nodes__sys_job_schedule__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-scheduler/nodes__sys_job_schedule__bulkMutation__bulkDelete": "Suppression de masse", "@sage/xtrem-scheduler/nodes__sys_job_schedule__mutation__purgeExecution": "Exécution épuration", "@sage/xtrem-scheduler/nodes__sys_job_schedule__mutation__purgeExecution__failed": "L'exécution de la purge a échoué.", "@sage/xtrem-scheduler/nodes__sys_job_schedule__mutation__purgeExecution__parameter__allStatus": "Tous les statuts", "@sage/xtrem-scheduler/nodes__sys_job_schedule__mutation__purgeExecution__parameter__schedule": "Planification", "@sage/xtrem-scheduler/nodes__sys_job_schedule__node_name": "Planification des tâches système", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__cronSchedule": "Planification Cron", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__cronTranslated": "<PERSON><PERSON> traduite", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__description": "Description", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__endStamp": "Horodatage de fin", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__executionLocale": "Paramètres régionaux d'exécution", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__executions": "Exécutions", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__executionUser": "Utilisateur d'exécution", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__id": "Code", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__isActive": "Active", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__nextExecutionStamp": "Horodatage d'exécution suivant", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__operation": "Opération", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__parameterValues": "Valeurs de paramètres", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__startStamp": "Démarrer horodatage", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__sysJobDefinition": "Définition tâche système", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__timeZone": "<PERSON><PERSON> ho<PERSON>", "@sage/xtrem-scheduler/nodes__sys_job_schedule__query__cronInfo": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/nodes__sys_job_schedule__query__cronInfo__failed": "Les informations Cron ont échoué.", "@sage/xtrem-scheduler/nodes__sys_job_schedule__query__timezones": "Fuseaux horaires", "@sage/xtrem-scheduler/nodes__sys_job_schedule__query__timezones__failed": "Les fuseaux horaires ont échoué.", "@sage/xtrem-scheduler/nodes__sys-job-execution_cron-or-start-date": "Une date de début ou une chaîne cron est obligatoire. ", "@sage/xtrem-scheduler/nodes__sys-job-execution_no-next-execution": "Cette tâche ne sera pas exécutée.", "@sage/xtrem-scheduler/nodes__sys-job-execution_operation-schedulable": "L'opération ne peut pas être planifiée.", "@sage/xtrem-scheduler/nodes__sys-job-execution_parsing-error": "Problème lors de l'analyse de la planification suivante", "@sage/xtrem-scheduler/nodes__sys-job-schedule__execution_user_not_authorized": "Vous pouvez uniquement vous affecter à vous-même l'utilisateur d'exécution.", "@sage/xtrem-scheduler/nodes__sys-job-schedule__no-cron-expression-no-startStamp": "Vous avez besoin d'une date / heure si l'expression Cron n'est pas définie.", "@sage/xtrem-scheduler/nodes__sys-job-schedule__once": "Une fois", "@sage/xtrem-scheduler/nodes_sys_notification_state_cannot_stop_process_not_running": "Impossible d'interrompre ce processus car il n'est plus en cours d'exécution.", "@sage/xtrem-scheduler/package__name": "Planificateur", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension____navigationPanel__listItem__line1__title": "Planification", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension____navigationPanel__listItem__line2__title": "Opération", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension____navigationPanel__listItem__line7__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__execute____title": "Exécuter", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__operation____columns__title__factory__name": "Node", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__operation____columns__title__isSchedulable": "Planifier", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__operation____columns__title__name": "Opération", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__operation____title": "Opération", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__schedule____title": "Planification", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__schedulePage____title": "Planification", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__stop____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages__operation_panel____title": "Volet des opérations", "@sage/xtrem-scheduler/pages__operation_panel__generalSection____title": "Général", "@sage/xtrem-scheduler/pages__operation_panel__parameters____columns__title__name": "Nom", "@sage/xtrem-scheduler/pages__operation_panel__parameters____columns__title__value": "<PERSON><PERSON>", "@sage/xtrem-scheduler/pages__operation_panel__parameters____title": "Paramètres", "@sage/xtrem-scheduler/pages__sys_job_definition____navigationPanel__listItem__title__title": "Nom", "@sage/xtrem-scheduler/pages__sys_job_definition____objectTypePlural": "Tâches batch", "@sage/xtrem-scheduler/pages__sys_job_definition____objectTypeSingular": "Tâche batch", "@sage/xtrem-scheduler/pages__sys_job_definition____title": "Tâche batch", "@sage/xtrem-scheduler/pages__sys_job_definition__description____title": "Description", "@sage/xtrem-scheduler/pages__sys_job_definition__generalSection____title": "Général", "@sage/xtrem-scheduler/pages__sys_job_definition__isActive____title": "Active", "@sage/xtrem-scheduler/pages__sys_job_definition__name____title": "Nom", "@sage/xtrem-scheduler/pages__sys_job_execution____navigationPanel__listItem__line__title": "Node", "@sage/xtrem-scheduler/pages__sys_job_execution____navigationPanel__listItem__line2__title": "Package", "@sage/xtrem-scheduler/pages__sys_job_execution____navigationPanel__listItem__line3__title": "D<PERSON>but", "@sage/xtrem-scheduler/pages__sys_job_execution____navigationPanel__listItem__line4__title": "Fin", "@sage/xtrem-scheduler/pages__sys_job_execution____navigationPanel__listItem__title__title": "Nom", "@sage/xtrem-scheduler/pages__sys_job_execution____navigationPanel__optionsMenu__title": "Toutes", "@sage/xtrem-scheduler/pages__sys_job_execution____objectTypePlural": "Exécution de tâches", "@sage/xtrem-scheduler/pages__sys_job_execution____objectTypeSingular": "Exécution de tâche", "@sage/xtrem-scheduler/pages__sys_job_execution____title": "Exécution de tâche", "@sage/xtrem-scheduler/pages__sys_job_execution__execute____title": "", "@sage/xtrem-scheduler/pages__sys_job_execution__generalSection____title": "Général", "@sage/xtrem-scheduler/pages__sys_job_execution__logs____columns__title__lineNumber": "#", "@sage/xtrem-scheduler/pages__sys_job_execution__logs____columns__title__severity": "Gravité", "@sage/xtrem-scheduler/pages__sys_job_execution__logs____columns__title__text": "Message", "@sage/xtrem-scheduler/pages__sys_job_execution__logs____columns__title__timestamp": "<PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_execution__logs____title": "Traces", "@sage/xtrem-scheduler/pages__sys_job_execution__logsSection____title": "Traces", "@sage/xtrem-scheduler/pages__sys_job_execution__operation____columns__title": "Node", "@sage/xtrem-scheduler/pages__sys_job_execution__operation____columns__title__isSchedulable": "Planifier", "@sage/xtrem-scheduler/pages__sys_job_execution__operation____columns__title__name": "Opération", "@sage/xtrem-scheduler/pages__sys_job_execution__operation____columns__title__name__2": "", "@sage/xtrem-scheduler/pages__sys_job_execution__operation____title": "Opération", "@sage/xtrem-scheduler/pages__sys_job_execution__parameters____columns__title__name": "Nom", "@sage/xtrem-scheduler/pages__sys_job_execution__parameters____columns__title__value": "<PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_execution__parameters____title": "Paramètres", "@sage/xtrem-scheduler/pages__sys_job_execution__status____title": "Statut", "@sage/xtrem-scheduler/pages__sys_job_execution__timeEnded____title": "Heure de fin", "@sage/xtrem-scheduler/pages__sys_job_execution__timeStarted____title": "<PERSON><PERSON> d<PERSON>", "@sage/xtrem-scheduler/pages__sys_job_execution__trackingId____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__bulkActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__line_5__title": "Planification hebdomadaire", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__line1__title": "Utilisa<PERSON>ur", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__line2Right__title": "Planification Cron", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__line3__title": "Actif", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__line3Right__title": "Opération", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__line5__title": "Planification hebdomadaire", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__title__title": "Code", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__titleRight__title": "Description", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__optionsMenu__title": "Toutes", "@sage/xtrem-scheduler/pages__sys_job_schedule____objectTypePlural": "Planification des tâches", "@sage/xtrem-scheduler/pages__sys_job_schedule____objectTypeSingular": "Planification de tâche", "@sage/xtrem-scheduler/pages__sys_job_schedule____title": "Planification des tâches", "@sage/xtrem-scheduler/pages__sys_job_schedule__createCrud____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule__cronSchedule____title": "Expression Cron", "@sage/xtrem-scheduler/pages__sys_job_schedule__cronTranslated____title": "<PERSON><PERSON> traduite", "@sage/xtrem-scheduler/pages__sys_job_schedule__description____title": "Description", "@sage/xtrem-scheduler/pages__sys_job_schedule__endStamp____title": "Date de fin", "@sage/xtrem-scheduler/pages__sys_job_schedule__execute____title": "Exécuter maintenant", "@sage/xtrem-scheduler/pages__sys_job_schedule__executeSection____title": "Exécuter l'historique", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____columns__title___id": "Détails", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____columns__title__notificationId": "Détails", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____columns__title__status": "Statut", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____columns__title__timeEnded": "Fin", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____columns__title__timeStarted": "D<PERSON>but", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____title": "Tâches exécutées", "@sage/xtrem-scheduler/pages__sys_job_schedule__executionUser____columns__title__email": "E-mail", "@sage/xtrem-scheduler/pages__sys_job_schedule__executionUser____columns__title__firstName": "Prénom", "@sage/xtrem-scheduler/pages__sys_job_schedule__executionUser____columns__title__lastName": "Nom de famille", "@sage/xtrem-scheduler/pages__sys_job_schedule__executionUser____title": "Utilisateur d'exécution", "@sage/xtrem-scheduler/pages__sys_job_schedule__generalSection____title": "Général", "@sage/xtrem-scheduler/pages__sys_job_schedule__id____title": "Code", "@sage/xtrem-scheduler/pages__sys_job_schedule__isActive____title": "Actif", "@sage/xtrem-scheduler/pages__sys_job_schedule__operation____columns__title__description": "Opération", "@sage/xtrem-scheduler/pages__sys_job_schedule__operation____columns__title__factory__name": "Node", "@sage/xtrem-scheduler/pages__sys_job_schedule__operation____columns__title__isSchedulable": "Planifier", "@sage/xtrem-scheduler/pages__sys_job_schedule__operation____columns__title__title": "Opération", "@sage/xtrem-scheduler/pages__sys_job_schedule__operation____title": "Opération", "@sage/xtrem-scheduler/pages__sys_job_schedule__parameters____columns__title__name": "Nom", "@sage/xtrem-scheduler/pages__sys_job_schedule__parameters____columns__title__value": "<PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule__parameters____title": "Paramètres", "@sage/xtrem-scheduler/pages__sys_job_schedule__purgeExecution____title": "Exécution épuration", "@sage/xtrem-scheduler/pages__sys_job_schedule__refreshExecutedJobs____title": "Actualiser", "@sage/xtrem-scheduler/pages__sys_job_schedule__refreshExecutions____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule__startStamp____title": "Date de début", "@sage/xtrem-scheduler/pages__sys_job_schedule__timeZone____title": "<PERSON><PERSON> ho<PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard____title": "Nouvelle tâche batch", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__atJobHour____placeholder": "<PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__atJobHour____title": "Heure d'exécution", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__atJobMinute____placeholder": "Minute", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__batch_task_scheduled": "Tâche batch programmée : {{0}}", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__batch_task_started": "Tâche batch démarrée", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__cronString____title": "Expression Cron", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__daily": "Tous les jours", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__days____title": "Jours", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__defineSection____title": "Définir", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__friday": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__invalid_date": "Date invalide", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__invalid_date_with_argument": "Date invalide : {{0}}", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__job____columns__title__description": "Opération", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__job____columns__title__factory__name": "Origine", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__job____columns__title__name": "Operation", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__job____columns__title__title": "Opération", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__job____title": "Définition tâche", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobDate____title": "Date de début", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobHour____placeholder": "<PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobHour____title": "<PERSON><PERSON> d<PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobMinute____placeholder": "Minute", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobParameters____columns__title__name": "Paramètre", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobParameters____columns__title__value": "<PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobParameters____title": "Paramètres", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobSchedule____title": "Planification", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__monday": "<PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__monthly": "Tous les mois", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__needs_to_be_a_number": "Doit être un nombre.", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__needs_to_be_either_true_or_false": "Doit être une des valeurs suivantes : {{0}}, {{1}}.", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__needs_to_be_in_the_future": "Doit être dans le futur.", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__needs_to_be_in_the_future_with_argument": "Doit être dans le futur : {{0}}", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__now": "Now", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__once": "Once", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__reccurence____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__recurrence-not-set": "<PERSON><PERSON> devez définir la fréquence.", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__recurring": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__run_now": "Exécuter maintenant", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__run_once": "Exécuter une fois", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__saturday": "<PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__scheduleSection____title": "Planification", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__settingsSection____title": "Paramétrage", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__stop_date_must_be_later_start_date": "La date d'arrêt doit être postérieure à la date de début.", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__stopJobDate____title": "Date d'arrêt", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__stopJobHour____placeholder": "<PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__stopJobHour____title": "<PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__stopJobMinute____placeholder": "Minute", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__sunday": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__thursday": "<PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__tuesday": "<PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__wednesday": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__weekly": "Toutes les semaines", "@sage/xtrem-scheduler/pages__sys_notification_state__ask_by_user": "Demandée par l'utilisateur", "@sage/xtrem-scheduler/pages__workflow_event_schedule____title": "Configuration de déclenchement", "@sage/xtrem-scheduler/pages__workflow_event_schedule__cronSchedule____title": "Expression Cron", "@sage/xtrem-scheduler/pages__workflow_event_schedule__endHour____infoMessage": "Format : hh : mm", "@sage/xtrem-scheduler/pages__workflow_event_schedule__endHour____title": "Heure de fin", "@sage/xtrem-scheduler/pages__workflow_event_schedule__endStamp____title": "Date de fin", "@sage/xtrem-scheduler/pages__workflow_event_schedule__mainSection____title": "Planification", "@sage/xtrem-scheduler/pages__workflow_event_schedule__startHour____infoMessage": "Format : hh : mm", "@sage/xtrem-scheduler/pages__workflow_event_schedule__startHour____title": "<PERSON><PERSON> d<PERSON>", "@sage/xtrem-scheduler/pages__workflow_event_schedule__startStamp____title": "Date de début", "@sage/xtrem-scheduler/pages__workflow_event_schedule__timeZone____title": "<PERSON><PERSON> ho<PERSON>", "@sage/xtrem-scheduler/pages-confirm-cancel": "Annuler", "@sage/xtrem-scheduler/pages-confirm-purge": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/permission__manage__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/permission__read__name": "Lecture", "@sage/xtrem-scheduler/purge-sys-execute-history": "Vous êtes sur le point de supprimer l'historique d'exécution {{role}}.", "@sage/xtrem-scheduler/purge-sys-message-history-title": "Effacer l'historique", "@sage/xtrem-scheduler/stickers__notification_sticker____title": "Centre de notification", "@sage/xtrem-scheduler/stickers__notification_sticker__block1____title": "Bloc 1", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____columns__title__lastMessage": "<PERSON><PERSON> message", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____columns__title__notificationId": "Détails", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____columns__title__operationName": "Nom", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____columns__title__status": "Statut", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____columns__title__timeEnded": "Fin", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____columns__title__timeStarted": "D<PERSON>but", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____emptyStateText": "Pas de notifications", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____title": "Tâches exécutées", "@sage/xtrem-scheduler/stickers__notification_sticker__isEnabled____title": "Activer les notifications", "@sage/xtrem-scheduler/stickers__notification_sticker__markAsRead____title": "Marquer comme lu", "@sage/xtrem-scheduler/stickers__notification_sticker__section1____title": "POC : centre de notification", "@sage/xtrem-scheduler/sys-client-notification__number_of_records_purged": "{{numberDeleted}} enregistrements supprimés.", "@sage/xtrem-scheduler/sys-job-definition/validate-parameters-reference-type": "Le paramètre {{parameterName}} est de type référence, mais aucune nom de node n'est spécifié.", "@sage/xtrem-scheduler/sys-job-schedule/invalid-parameters-value": "Valeur invalide pour le paramètre {{failedParsedParameterNames}}.", "@sage/xtrem-scheduler/sys-job-schedule/missing-parameters": "Paramètre obligatoire {{missingParameterNames}} absent de la tâche {{jobDefinitionName}}.", "@sage/xtrem-scheduler/sys-job-schedule/validate-parameters": "Le calendrier des tâches {{jobDefinitionName}} contient plus de paramètres que la définition de la tâche.", "@sage/xtrem-scheduler/sys-notification-state__bulkStop_only_pending-running": "Vous pouvez uniquement arrêter une tâche en cours ou en attente.", "@sage/xtrem-scheduler/sys-notification-state__number_of_records_purged": "{{numberDeleted}} enregistrements supprimés"}