{" @sage/xtrem-scheduler/nodes__sys-job-schedule__once": "Uma só vez", "@sage/xtrem-scheduler/activity__sys_job_schedule__name": "<PERSON><PERSON><PERSON><PERSON> do \"job\" de sistema", "@sage/xtrem-scheduler/data_types__cron_schedule__name": "Agendamento do Cron", "@sage/xtrem-scheduler/data_types__job_status_enum__name": "Enum status job", "@sage/xtrem-scheduler/data_types__parameter_type_enum__name": "Enum tipo de parâmetro", "@sage/xtrem-scheduler/data_types__timezone_schedule__name": "Agendamento de fuso horário", "@sage/xtrem-scheduler/data-types/cron_schedule__invalid_schedule": "O valor do agendamento está incorreto: ({{value}}).", "@sage/xtrem-scheduler/data-types/timezone__invalid_timezone": "O valor do fuso horário está incorreto: ({{value}}).", "@sage/xtrem-scheduler/enums__job_status__done": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/enums__job_status__error": "Erro", "@sage/xtrem-scheduler/enums__job_status__interrupted": "Interrompido", "@sage/xtrem-scheduler/enums__job_status__interruptRequested": "Interrupção solicitada", "@sage/xtrem-scheduler/enums__job_status__new": "Novo", "@sage/xtrem-scheduler/enums__job_status__notResponding": "Não responde", "@sage/xtrem-scheduler/enums__job_status__pending": "Pendente", "@sage/xtrem-scheduler/enums__job_status__running": "Em execução", "@sage/xtrem-scheduler/enums__job_status__stopped": "Parado", "@sage/xtrem-scheduler/enums__job_status__stopRequested": "Paragem solicitada", "@sage/xtrem-scheduler/enums__log_severity__error": "Erro", "@sage/xtrem-scheduler/enums__log_severity__exception": "Exceção", "@sage/xtrem-scheduler/enums__log_severity__info": "Info", "@sage/xtrem-scheduler/enums__log_severity__test": "<PERSON>e", "@sage/xtrem-scheduler/enums__log_severity__warning": "Aviso", "@sage/xtrem-scheduler/enums__parameter_type__boolean": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/enums__parameter_type__date": "Data", "@sage/xtrem-scheduler/enums__parameter_type__dateRange": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/enums__parameter_type__datetime": "Data-hora", "@sage/xtrem-scheduler/enums__parameter_type__datetimeRange": "Intervalo de data e hora", "@sage/xtrem-scheduler/enums__parameter_type__decimal": "Décimal", "@sage/xtrem-scheduler/enums__parameter_type__decimalRange": "Intervalo decimal", "@sage/xtrem-scheduler/enums__parameter_type__double": "Dobro", "@sage/xtrem-scheduler/enums__parameter_type__enum": "enum", "@sage/xtrem-scheduler/enums__parameter_type__enumArray": "Matriz de enumeração", "@sage/xtrem-scheduler/enums__parameter_type__float": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/enums__parameter_type__integer": "Inteiro", "@sage/xtrem-scheduler/enums__parameter_type__integerArray": "<PERSON><PERSON>", "@sage/xtrem-scheduler/enums__parameter_type__integerRange": "Intervalo inteiro", "@sage/xtrem-scheduler/enums__parameter_type__reference": "Referência", "@sage/xtrem-scheduler/enums__parameter_type__referenceArray": "<PERSON><PERSON>", "@sage/xtrem-scheduler/enums__parameter_type__string": "<PERSON><PERSON> (String)", "@sage/xtrem-scheduler/enums__parameter_type__stringArray": "<PERSON><PERSON> de <PERSON>", "@sage/xtrem-scheduler/enums__parameter_type__time": "Tempo", "@sage/xtrem-scheduler/enums__parameter_type__uuid": "UUID", "@sage/xtrem-scheduler/job-manager_started-by-scheduler": "Iniciado pelo planeador: {{description}}", "@sage/xtrem-scheduler/node-extensions__sys_client_notification_extension__asyncMutation__purgeSysClientNotification": "Eliminar notificação de cliente do sistema", "@sage/xtrem-scheduler/node-extensions__sys_client_notification_extension__asyncMutation__purgeSysClientNotification__failed": "Falha na notificação do cliente do sistema de expurgo.", "@sage/xtrem-scheduler/node-extensions__sys_client_notification_extension__asyncMutation__purgeSysClientNotification__parameter__duration": "Duração", "@sage/xtrem-scheduler/node-extensions__sys_client_notification_extension__asyncMutation__purgeSysClientNotification__parameter__level": "Nível", "@sage/xtrem-scheduler/node-extensions__sys_client_notification_extension__asyncMutation__purgeSysClientNotification__parameter__unit": "Unidade", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__purgeHistory": "Hist<PERSON><PERSON><PERSON> da página", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__purgeHistory__failed": "O histórico de expurgo falhou.", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__purgeHistory__parameter__duration": "duração", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__purgeHistory__parameter__operationNames": "Nomes das operações", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__purgeHistory__parameter__unit": "Unidade", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__simulateAsyncMutation": "Simular mutação assíncrona", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__simulateAsyncMutation__failed": "A simulação da mutação assíncrona falhou.", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__simulateAsyncMutation__parameter__delayInSec": "Atraso em segundos", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__simulateAsyncMutation__parameter__id": "ID", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__simulateAsyncMutation__parameter__throwAt": "<PERSON><PERSON> para", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__bulkMutation__bulkStop": "Bulk stop", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__bulkMutation__bulkStop__failed": "A paragem global falhou.", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__bulkMutation__bulkStop__parameter__reason": "Motivo", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__bulkMutation__markAsRead": "Marcar como lido", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__bulkMutation__markAsRead__failed": "Falha na marcação como lido.", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__execute": "Executar", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__execute__failed": "A execução falhou.", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__execute__parameter__jobExecution": "Execução de tarefa", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__notRespondingJob": "Ta<PERSON><PERSON> sem resposta", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__notRespondingJob__failed": "A tarefa (job) não está a responder e falhou.", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__notRespondingJob__parameter__duration": "Duração", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__notRespondingJob__parameter__unit": "Unidade", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__stop": "<PERSON><PERSON>", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__stop__failed": "A paragem falhou.", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__stop__parameter__jobExecutionSysId": "ID do sistema de execução de tarefas", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__stop__parameter__reason": "Motivo", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__property__isScheduled": "Está programado", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__property__schedule": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/nodes__sys_chore__asyncMutation__purgeContentAddressableTables": "Limpar tabelas de conteúdo endere<PERSON>", "@sage/xtrem-scheduler/nodes__sys_chore__asyncMutation__purgeContentAddressableTables__failed": "Expurgar tabelas de conteúdo endere<PERSON>ável.", "@sage/xtrem-scheduler/nodes__sys_chore__node_name": "Tarefa do sistema", "@sage/xtrem-scheduler/nodes__sys_job_schedule__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-scheduler/nodes__sys_job_schedule__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-scheduler/nodes__sys_job_schedule__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-scheduler/nodes__sys_job_schedule__bulkMutation__bulkDelete": "Eliminação em massa (bulk)", "@sage/xtrem-scheduler/nodes__sys_job_schedule__mutation__purgeExecution": "Execução da purga", "@sage/xtrem-scheduler/nodes__sys_job_schedule__mutation__purgeExecution__failed": "A execução do expurgo falhou.", "@sage/xtrem-scheduler/nodes__sys_job_schedule__mutation__purgeExecution__parameter__allStatus": "Todos os status", "@sage/xtrem-scheduler/nodes__sys_job_schedule__mutation__purgeExecution__parameter__schedule": "<PERSON><PERSON><PERSON><PERSON> (Schedule)", "@sage/xtrem-scheduler/nodes__sys_job_schedule__node_name": "<PERSON><PERSON><PERSON><PERSON> do \"job\" de sistema", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__cronSchedule": "Hor<PERSON>rio <PERSON>ron", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__cronTranslated": "Cron traduzido", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__description": "Descrição", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__endStamp": "Carimbo final (timestamp)", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__executionLocale": "Execução locale", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__executions": "Execuções", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__executionUser": "Utilizador de execução", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__id": "ID", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__isActive": "Está ativo", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__nextExecutionStamp": "Carimbo da próxima execução", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__operation": "Operação", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__parameterValues": "Valores parâmetros", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__startStamp": "In<PERSON>ar <PERSON>", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__sysJobDefinition": "Definição do job de sistema", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__timeZone": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/nodes__sys_job_schedule__query__cronInfo": "Infos sobre o Cron", "@sage/xtrem-scheduler/nodes__sys_job_schedule__query__cronInfo__failed": "A informação do Cron falhou.", "@sage/xtrem-scheduler/nodes__sys_job_schedule__query__timezones": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/nodes__sys_job_schedule__query__timezones__failed": "Falha nos fusos horários (timezones).", "@sage/xtrem-scheduler/nodes__sys-job-execution_cron-or-start-date": "É necessária uma data de início ou uma cadeia cron ", "@sage/xtrem-scheduler/nodes__sys-job-execution_no-next-execution": "Esta tarefa não será executada.", "@sage/xtrem-scheduler/nodes__sys-job-execution_operation-schedulable": "A operação não pode ser programada", "@sage/xtrem-scheduler/nodes__sys-job-execution_parsing-error": "Problema durante a análise do planeamento posterior.", "@sage/xtrem-scheduler/nodes__sys-job-schedule__execution_user_not_authorized": "Só pode atribuir o usuário de execução a si próprio.", "@sage/xtrem-scheduler/nodes__sys-job-schedule__no-cron-expression-no-startStamp": "É necessária uma data de início se a expressão cron não estiver definida.", "@sage/xtrem-scheduler/nodes__sys-job-schedule__once": "Uma só vez", "@sage/xtrem-scheduler/nodes_sys_notification_state_cannot_stop_process_not_running": "Este processo não pode ser interrompido, uma vez que já não está a ser executado.", "@sage/xtrem-scheduler/package__name": "Agendamento (Scheduler)", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension____navigationPanel__listItem__line1__title": "Programação (Scheduler)", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension____navigationPanel__listItem__line2__title": "Operação", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension____navigationPanel__listItem__line7__title": "Recorrente", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__execute____title": "Executar", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__operation____columns__title__factory__name": "<PERSON><PERSON>", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__operation____columns__title__isSchedulable": "Pode programar", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__operation____columns__title__name": "Operação", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__operation____title": "Operação", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__schedule____title": "<PERSON><PERSON><PERSON><PERSON> (Schedule)", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__schedulePage____title": "Programação (Scheduler)", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__stop____title": "<PERSON><PERSON>", "@sage/xtrem-scheduler/pages__operation_panel____title": "Painel de operações", "@sage/xtrem-scheduler/pages__operation_panel__generalSection____title": "G<PERSON>", "@sage/xtrem-scheduler/pages__operation_panel__parameters____columns__title__name": "Nome", "@sage/xtrem-scheduler/pages__operation_panel__parameters____columns__title__value": "Valor", "@sage/xtrem-scheduler/pages__operation_panel__parameters____title": "Parâmetros", "@sage/xtrem-scheduler/pages__sys_job_definition____navigationPanel__listItem__title__title": "Nome", "@sage/xtrem-scheduler/pages__sys_job_definition____objectTypePlural": "Ta<PERSON>fa batch", "@sage/xtrem-scheduler/pages__sys_job_definition____objectTypeSingular": "Ta<PERSON>fa batch", "@sage/xtrem-scheduler/pages__sys_job_definition____title": "Ta<PERSON>fa batch", "@sage/xtrem-scheduler/pages__sys_job_definition__description____title": "Descrição", "@sage/xtrem-scheduler/pages__sys_job_definition__generalSection____title": "G<PERSON>", "@sage/xtrem-scheduler/pages__sys_job_definition__isActive____title": "Ativo", "@sage/xtrem-scheduler/pages__sys_job_definition__name____title": "Nome", "@sage/xtrem-scheduler/pages__sys_job_execution____navigationPanel__listItem__line__title": "<PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_execution____navigationPanel__listItem__line2__title": "Embalagem", "@sage/xtrem-scheduler/pages__sys_job_execution____navigationPanel__listItem__line3__title": "Iniciado", "@sage/xtrem-scheduler/pages__sys_job_execution____navigationPanel__listItem__line4__title": "Fim", "@sage/xtrem-scheduler/pages__sys_job_execution____navigationPanel__listItem__title__title": "Nome", "@sage/xtrem-scheduler/pages__sys_job_execution____navigationPanel__optionsMenu__title": "<PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_execution____objectTypePlural": "Execução de tarefas", "@sage/xtrem-scheduler/pages__sys_job_execution____objectTypeSingular": "Execução de tarefa", "@sage/xtrem-scheduler/pages__sys_job_execution____title": "Execução de tarefa", "@sage/xtrem-scheduler/pages__sys_job_execution__execute____title": "", "@sage/xtrem-scheduler/pages__sys_job_execution__generalSection____title": "G<PERSON>", "@sage/xtrem-scheduler/pages__sys_job_execution__logs____columns__title__lineNumber": "#", "@sage/xtrem-scheduler/pages__sys_job_execution__logs____columns__title__severity": "Severidade", "@sage/xtrem-scheduler/pages__sys_job_execution__logs____columns__title__text": "Mensagem", "@sage/xtrem-scheduler/pages__sys_job_execution__logs____columns__title__timestamp": "Tempo", "@sage/xtrem-scheduler/pages__sys_job_execution__logs____title": "Rastros (logs)", "@sage/xtrem-scheduler/pages__sys_job_execution__logsSection____title": "Rastros (logs)", "@sage/xtrem-scheduler/pages__sys_job_execution__operation____columns__title": "<PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_execution__operation____columns__title__isSchedulable": "Pode programar", "@sage/xtrem-scheduler/pages__sys_job_execution__operation____columns__title__name": "Operação", "@sage/xtrem-scheduler/pages__sys_job_execution__operation____columns__title__name__2": "", "@sage/xtrem-scheduler/pages__sys_job_execution__operation____title": "Operação", "@sage/xtrem-scheduler/pages__sys_job_execution__parameters____columns__title__name": "Nome", "@sage/xtrem-scheduler/pages__sys_job_execution__parameters____columns__title__value": "Valor", "@sage/xtrem-scheduler/pages__sys_job_execution__parameters____title": "Parâmetros", "@sage/xtrem-scheduler/pages__sys_job_execution__status____title": "Status", "@sage/xtrem-scheduler/pages__sys_job_execution__timeEnded____title": "Hora fim", "@sage/xtrem-scheduler/pages__sys_job_execution__timeStarted____title": "Hora iní<PERSON>", "@sage/xtrem-scheduler/pages__sys_job_execution__trackingId____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__bulkActions__title": "Eliminar", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__line_5__title": "Próximo agendamento", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__line1__title": "Utilizador", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__line2Right__title": "Hor<PERSON>rio <PERSON>ron", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__line3__title": "Ativo", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__line3Right__title": "Operação", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__line5__title": "<PERSON>nda da semana", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__title__title": "ID", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__titleRight__title": "Descrição", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__optionsMenu__title": "<PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule____objectTypePlural": "Execução de tarefas", "@sage/xtrem-scheduler/pages__sys_job_schedule____objectTypeSingular": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule__createCrud____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule__cronSchedule____title": "Expressão Cron", "@sage/xtrem-scheduler/pages__sys_job_schedule__cronTranslated____title": "Tradução do cron", "@sage/xtrem-scheduler/pages__sys_job_schedule__description____title": "Descrição", "@sage/xtrem-scheduler/pages__sys_job_schedule__endStamp____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule__execute____title": "Executar agora", "@sage/xtrem-scheduler/pages__sys_job_schedule__executeSection____title": "Executar histó<PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____columns__title___id": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____columns__title__notificationId": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____columns__title__status": "Status", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____columns__title__timeEnded": "Fim", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____columns__title__timeStarted": "Iniciar", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____dropdownActions__title": "<PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____title": "Tare<PERSON>s executadas", "@sage/xtrem-scheduler/pages__sys_job_schedule__executionUser____columns__title__email": "E-mail", "@sage/xtrem-scheduler/pages__sys_job_schedule__executionUser____columns__title__firstName": "Nome", "@sage/xtrem-scheduler/pages__sys_job_schedule__executionUser____columns__title__lastName": "Sobrenome", "@sage/xtrem-scheduler/pages__sys_job_schedule__executionUser____title": "Utilizador de execução", "@sage/xtrem-scheduler/pages__sys_job_schedule__generalSection____title": "G<PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule__id____title": "ID", "@sage/xtrem-scheduler/pages__sys_job_schedule__isActive____title": "Ativo", "@sage/xtrem-scheduler/pages__sys_job_schedule__operation____columns__title__description": "Operação", "@sage/xtrem-scheduler/pages__sys_job_schedule__operation____columns__title__factory__name": "<PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule__operation____columns__title__isSchedulable": "Pode programar", "@sage/xtrem-scheduler/pages__sys_job_schedule__operation____columns__title__title": "Operação", "@sage/xtrem-scheduler/pages__sys_job_schedule__operation____title": "Operação", "@sage/xtrem-scheduler/pages__sys_job_schedule__parameters____columns__title__name": "Nome", "@sage/xtrem-scheduler/pages__sys_job_schedule__parameters____columns__title__value": "Valor", "@sage/xtrem-scheduler/pages__sys_job_schedule__parameters____title": "Parâmetros", "@sage/xtrem-scheduler/pages__sys_job_schedule__purgeExecution____title": "Execução da limpeza (depuração)", "@sage/xtrem-scheduler/pages__sys_job_schedule__refreshExecutedJobs____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule__refreshExecutions____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule__startStamp____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule__timeZone____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard____title": "Nova tarefa batch", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__atJobHour____placeholder": "<PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__atJobHour____title": "No momento", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__atJobMinute____placeholder": "Min<PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__batch_task_scheduled": "Tarefa em batch agendada: {{0}}", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__batch_task_started": "Tarefa batch iniciada", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__cronString____title": "Expressão Cron", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__daily": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__days____title": "<PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__defineSection____title": "Definir", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__friday": "Sexta", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__invalid_date": "Data inválida", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__invalid_date_with_argument": "Data inválida: {{0}}", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__job____columns__title__description": "Operação", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__job____columns__title__factory__name": "Fonte", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__job____columns__title__name": "Operation", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__job____columns__title__title": "Operação", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__job____title": "Definição do job", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobDate____title": "Data de início", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobHour____placeholder": "<PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobHour____title": "Hora iní<PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobMinute____placeholder": "Min<PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobParameters____columns__title__name": "Parâmetro", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobParameters____columns__title__value": "Valor", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobParameters____title": "Parâmetros", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobSchedule____title": "Programação", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__monday": "Segunda", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__monthly": "<PERSON><PERSON> mês", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__needs_to_be_a_number": "Precisa de ser um número.", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__needs_to_be_either_true_or_false": "<PERSON>e ser um dos seguintes: {{0}}, {{1}}.", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__needs_to_be_in_the_future": "Precisa de ser no futuro", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__needs_to_be_in_the_future_with_argument": "Precisa de ser no futuro: {{0}}", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__now": "Now", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__once": "Once", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__reccurence____title": "Recorrente", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__recurrence-not-set": "É necessário definir a frequência.", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__recurring": "Recorrente", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__run_now": "Executar agora", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__run_once": "Executar uma vez", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__saturday": "Sábado", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__scheduleSection____title": "Programação", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__settingsSection____title": "Configurações", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__stop_date_must_be_later_start_date": "A data de paragem tem de ser posterior à data de início.", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__stopJobDate____title": "Data de paragem", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__stopJobHour____placeholder": "<PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__stopJobHour____title": "<PERSON>ra de paragem", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__stopJobMinute____placeholder": "Min<PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__sunday": "Domingo", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__thursday": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__tuesday": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__wednesday": "Quarta", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__weekly": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_notification_state__ask_by_user": "Pedido pelo utilizador", "@sage/xtrem-scheduler/pages__workflow_event_schedule____title": "Configuração de ativação (Trigger)", "@sage/xtrem-scheduler/pages__workflow_event_schedule__cronSchedule____title": "Expressão Cron", "@sage/xtrem-scheduler/pages__workflow_event_schedule__endHour____infoMessage": "Formato: hh:mm", "@sage/xtrem-scheduler/pages__workflow_event_schedule__endHour____title": "Hora fim", "@sage/xtrem-scheduler/pages__workflow_event_schedule__endStamp____title": "Data final", "@sage/xtrem-scheduler/pages__workflow_event_schedule__mainSection____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages__workflow_event_schedule__startHour____infoMessage": "Formato: hh:mm", "@sage/xtrem-scheduler/pages__workflow_event_schedule__startHour____title": "Hora iní<PERSON>", "@sage/xtrem-scheduler/pages__workflow_event_schedule__startStamp____title": "Data de início", "@sage/xtrem-scheduler/pages__workflow_event_schedule__timeZone____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages-confirm-cancel": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages-confirm-purge": "Eliminar", "@sage/xtrem-scheduler/permission__manage__name": "Gestão", "@sage/xtrem-scheduler/permission__read__name": "<PERSON>r", "@sage/xtrem-scheduler/purge-sys-execute-history": "Está prestes a eliminar o histórico de execução.", "@sage/xtrem-scheduler/purge-sys-message-history-title": "Eliminar o histórico", "@sage/xtrem-scheduler/stickers__notification_sticker____title": "Centro de notificações", "@sage/xtrem-scheduler/stickers__notification_sticker__block1____title": "Bloco", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____columns__title__lastMessage": "Última mensagem", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____columns__title__notificationId": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____columns__title__operationName": "Nome", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____columns__title__status": "Status", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____columns__title__timeEnded": "Fim", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____columns__title__timeStarted": "Início", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____emptyStateText": "Sem notificações", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____title": "Tare<PERSON>s executadas", "@sage/xtrem-scheduler/stickers__notification_sticker__isEnabled____title": "Ativar notificações", "@sage/xtrem-scheduler/stickers__notification_sticker__markAsRead____title": "Marcar como lido", "@sage/xtrem-scheduler/stickers__notification_sticker__section1____title": "POC: Centro de notificações", "@sage/xtrem-scheduler/sys-client-notification__number_of_records_purged": "{{numberDeleted}} registos eliminados.", "@sage/xtrem-scheduler/sys-job-definition/validate-parameters-reference-type": "O parâmetro {{{parameterName}} é do tipo de referência, mas nenhum nodeName foi especificado.", "@sage/xtrem-scheduler/sys-job-schedule/invalid-parameters-value": "Valor inválido para parâmetro {{failedParsedParameterNames}}.", "@sage/xtrem-scheduler/sys-job-schedule/missing-parameters": "Parâmetro obrigatório {{missingParameterNames}} em falta para o Job {{jobDefinitionName}}.", "@sage/xtrem-scheduler/sys-job-schedule/validate-parameters": "O agendamento do job {{jobDefinitionName}} contém mais parâmetros do que a definição do trabalho.", "@sage/xtrem-scheduler/sys-notification-state__bulkStop_only_pending-running": "Só é possível parar uma tarefa que esteja em execução ou pendente.", "@sage/xtrem-scheduler/sys-notification-state__number_of_records_purged": "{{numberDeleted}} registos eliminados"}