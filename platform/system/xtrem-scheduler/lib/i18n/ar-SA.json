{"@sage/xtrem-scheduler/activity__sys_job_schedule__name": "", "@sage/xtrem-scheduler/data_types__cron_schedule__name": "", "@sage/xtrem-scheduler/data_types__job_status_enum__name": "", "@sage/xtrem-scheduler/data_types__parameter_type_enum__name": "", "@sage/xtrem-scheduler/data_types__timezone_schedule__name": "", "@sage/xtrem-scheduler/data-types/cron_schedule__invalid_schedule": "", "@sage/xtrem-scheduler/data-types/timezone__invalid_timezone": "", "@sage/xtrem-scheduler/enums__job_status__done": "", "@sage/xtrem-scheduler/enums__job_status__error": "", "@sage/xtrem-scheduler/enums__job_status__interrupted": "", "@sage/xtrem-scheduler/enums__job_status__interruptRequested": "", "@sage/xtrem-scheduler/enums__job_status__new": "", "@sage/xtrem-scheduler/enums__job_status__notResponding": "", "@sage/xtrem-scheduler/enums__job_status__pending": "", "@sage/xtrem-scheduler/enums__job_status__running": "", "@sage/xtrem-scheduler/enums__job_status__stopped": "", "@sage/xtrem-scheduler/enums__job_status__stopRequested": "", "@sage/xtrem-scheduler/enums__parameter_type__boolean": "", "@sage/xtrem-scheduler/enums__parameter_type__date": "", "@sage/xtrem-scheduler/enums__parameter_type__dateRange": "", "@sage/xtrem-scheduler/enums__parameter_type__datetime": "", "@sage/xtrem-scheduler/enums__parameter_type__datetimeRange": "", "@sage/xtrem-scheduler/enums__parameter_type__decimal": "", "@sage/xtrem-scheduler/enums__parameter_type__decimalRange": "", "@sage/xtrem-scheduler/enums__parameter_type__double": "", "@sage/xtrem-scheduler/enums__parameter_type__enum": "", "@sage/xtrem-scheduler/enums__parameter_type__enumArray": "", "@sage/xtrem-scheduler/enums__parameter_type__float": "", "@sage/xtrem-scheduler/enums__parameter_type__integer": "", "@sage/xtrem-scheduler/enums__parameter_type__integerArray": "", "@sage/xtrem-scheduler/enums__parameter_type__integerRange": "", "@sage/xtrem-scheduler/enums__parameter_type__reference": "", "@sage/xtrem-scheduler/enums__parameter_type__referenceArray": "", "@sage/xtrem-scheduler/enums__parameter_type__string": "", "@sage/xtrem-scheduler/enums__parameter_type__stringArray": "", "@sage/xtrem-scheduler/enums__parameter_type__time": "", "@sage/xtrem-scheduler/enums__parameter_type__uuid": "", "@sage/xtrem-scheduler/job-manager_started-by-scheduler": "", "@sage/xtrem-scheduler/node-extensions__sys_client_notification_extension__asyncMutation__purgeSysClientNotification": "", "@sage/xtrem-scheduler/node-extensions__sys_client_notification_extension__asyncMutation__purgeSysClientNotification__failed": "", "@sage/xtrem-scheduler/node-extensions__sys_client_notification_extension__asyncMutation__purgeSysClientNotification__parameter__duration": "", "@sage/xtrem-scheduler/node-extensions__sys_client_notification_extension__asyncMutation__purgeSysClientNotification__parameter__level": "", "@sage/xtrem-scheduler/node-extensions__sys_client_notification_extension__asyncMutation__purgeSysClientNotification__parameter__unit": "", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__purgeHistory": "", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__purgeHistory__failed": "", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__purgeHistory__parameter__duration": "", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__purgeHistory__parameter__operationNames": "", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__purgeHistory__parameter__unit": "", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__simulateAsyncMutation": "", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__simulateAsyncMutation__failed": "", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__simulateAsyncMutation__parameter__delayInSec": "", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__simulateAsyncMutation__parameter__id": "", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__simulateAsyncMutation__parameter__throwAt": "", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__bulkMutation__bulkStop": "", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__bulkMutation__bulkStop__failed": "", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__bulkMutation__bulkStop__parameter__reason": "", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__bulkMutation__markAsRead": "", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__bulkMutation__markAsRead__failed": "", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__execute": "", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__execute__failed": "", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__execute__parameter__jobExecution": "", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__notRespondingJob": "", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__notRespondingJob__failed": "", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__notRespondingJob__parameter__duration": "", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__notRespondingJob__parameter__unit": "", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__stop": "", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__stop__failed": "", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__stop__parameter__jobExecutionSysId": "", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__stop__parameter__reason": "", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__property__isScheduled": "", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__property__schedule": "", "@sage/xtrem-scheduler/nodes__sys_chore__asyncMutation__purgeContentAddressableTables": "", "@sage/xtrem-scheduler/nodes__sys_chore__asyncMutation__purgeContentAddressableTables__failed": "", "@sage/xtrem-scheduler/nodes__sys_chore__node_name": "", "@sage/xtrem-scheduler/nodes__sys_job_schedule__asyncMutation__asyncExport": "", "@sage/xtrem-scheduler/nodes__sys_job_schedule__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-scheduler/nodes__sys_job_schedule__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-scheduler/nodes__sys_job_schedule__bulkMutation__bulkDelete": "", "@sage/xtrem-scheduler/nodes__sys_job_schedule__mutation__purgeExecution": "", "@sage/xtrem-scheduler/nodes__sys_job_schedule__mutation__purgeExecution__failed": "", "@sage/xtrem-scheduler/nodes__sys_job_schedule__mutation__purgeExecution__parameter__allStatus": "", "@sage/xtrem-scheduler/nodes__sys_job_schedule__mutation__purgeExecution__parameter__schedule": "", "@sage/xtrem-scheduler/nodes__sys_job_schedule__node_name": "", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__cronSchedule": "", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__cronTranslated": "", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__description": "", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__endStamp": "", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__executionLocale": "", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__executions": "", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__executionUser": "", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__id": "", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__isActive": "", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__nextExecutionStamp": "", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__operation": "", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__parameterValues": "", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__startStamp": "", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__timeZone": "", "@sage/xtrem-scheduler/nodes__sys_job_schedule__query__cronInfo": "", "@sage/xtrem-scheduler/nodes__sys_job_schedule__query__cronInfo__failed": "", "@sage/xtrem-scheduler/nodes__sys_job_schedule__query__timezones": "", "@sage/xtrem-scheduler/nodes__sys_job_schedule__query__timezones__failed": "", "@sage/xtrem-scheduler/nodes__sys-job-execution_cron-or-start-date": "", "@sage/xtrem-scheduler/nodes__sys-job-execution_no-next-execution": "", "@sage/xtrem-scheduler/nodes__sys-job-execution_operation-schedulable": "", "@sage/xtrem-scheduler/nodes__sys-job-execution_parsing-error": "", "@sage/xtrem-scheduler/nodes__sys-job-schedule__execution_user_not_authorized": "", "@sage/xtrem-scheduler/nodes__sys-job-schedule__no-cron-expression-no-startStamp": "", "@sage/xtrem-scheduler/nodes__sys-job-schedule__once": "", "@sage/xtrem-scheduler/nodes_sys_notification_state_cannot_stop_process_not_running": "", "@sage/xtrem-scheduler/package__name": "", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension____navigationPanel__listItem__line1__title": "", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension____navigationPanel__listItem__line2__title": "", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension____navigationPanel__listItem__line7__title": "", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__execute____title": "", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__operation____columns__title__factory__name": "", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__operation____columns__title__isSchedulable": "", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__operation____columns__title__name": "", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__operation____title": "", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__schedule____title": "", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__schedulePage____title": "", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__stop____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__bulkActions__title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__line_5__title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__line1__title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__line2Right__title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__line3__title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__line3Right__title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__title__title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__titleRight__title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__optionsMenu__title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule____objectTypePlural": "", "@sage/xtrem-scheduler/pages__sys_job_schedule____objectTypeSingular": "", "@sage/xtrem-scheduler/pages__sys_job_schedule____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule__createCrud____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule__cronSchedule____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule__cronTranslated____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule__description____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule__endStamp____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule__execute____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule__executeSection____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____columns__title___id": "", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____columns__title__status": "", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____columns__title__timeEnded": "", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____columns__title__timeStarted": "", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____dropdownActions__title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule__executionUser____columns__title__email": "", "@sage/xtrem-scheduler/pages__sys_job_schedule__executionUser____columns__title__firstName": "", "@sage/xtrem-scheduler/pages__sys_job_schedule__executionUser____columns__title__lastName": "", "@sage/xtrem-scheduler/pages__sys_job_schedule__executionUser____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule__generalSection____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule__id____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule__isActive____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule__operation____columns__title__factory__name": "", "@sage/xtrem-scheduler/pages__sys_job_schedule__operation____columns__title__isSchedulable": "", "@sage/xtrem-scheduler/pages__sys_job_schedule__operation____columns__title__title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule__operation____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule__parameters____columns__title__name": "", "@sage/xtrem-scheduler/pages__sys_job_schedule__parameters____columns__title__value": "", "@sage/xtrem-scheduler/pages__sys_job_schedule__parameters____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule__purgeExecution____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule__refreshExecutedJobs____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule__startStamp____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule__timeZone____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__atJobHour____placeholder": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__atJobHour____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__atJobMinute____placeholder": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__batch_task_scheduled": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__batch_task_started": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__cronString____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__daily": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__days____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__defineSection____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__friday": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__invalid_date": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__invalid_date_with_argument": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__job____columns__title__factory__name": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__job____columns__title__title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__job____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobDate____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobHour____placeholder": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobHour____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobMinute____placeholder": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobParameters____columns__title__name": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobParameters____columns__title__value": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobParameters____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobSchedule____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__monday": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__monthly": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__needs_to_be_a_number": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__needs_to_be_either_true_or_false": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__needs_to_be_in_the_future": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__needs_to_be_in_the_future_with_argument": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__reccurence____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__recurrence-not-set": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__recurring": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__run_now": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__run_once": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__saturday": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__scheduleSection____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__settingsSection____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__stop_date_must_be_later_start_date": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__stopJobDate____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__stopJobHour____placeholder": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__stopJobHour____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__stopJobMinute____placeholder": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__sunday": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__thursday": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__tuesday": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__wednesday": "", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__weekly": "", "@sage/xtrem-scheduler/pages__sys_notification_state__ask_by_user": "", "@sage/xtrem-scheduler/pages__workflow_event_schedule____title": "", "@sage/xtrem-scheduler/pages__workflow_event_schedule__cronSchedule____title": "", "@sage/xtrem-scheduler/pages__workflow_event_schedule__endHour____infoMessage": "", "@sage/xtrem-scheduler/pages__workflow_event_schedule__endHour____title": "", "@sage/xtrem-scheduler/pages__workflow_event_schedule__endStamp____title": "", "@sage/xtrem-scheduler/pages__workflow_event_schedule__mainSection____title": "", "@sage/xtrem-scheduler/pages__workflow_event_schedule__startHour____infoMessage": "", "@sage/xtrem-scheduler/pages__workflow_event_schedule__startHour____title": "", "@sage/xtrem-scheduler/pages__workflow_event_schedule__startStamp____title": "", "@sage/xtrem-scheduler/pages__workflow_event_schedule__timeZone____title": "", "@sage/xtrem-scheduler/pages-confirm-cancel": "", "@sage/xtrem-scheduler/pages-confirm-purge": "", "@sage/xtrem-scheduler/permission__manage__name": "", "@sage/xtrem-scheduler/permission__read__name": "", "@sage/xtrem-scheduler/purge-sys-execute-history": "", "@sage/xtrem-scheduler/purge-sys-message-history-title": "", "@sage/xtrem-scheduler/stickers__notification_sticker____title": "", "@sage/xtrem-scheduler/stickers__notification_sticker__block1____title": "", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____columns__title__lastMessage": "", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____columns__title__notificationId": "", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____columns__title__operationName": "", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____columns__title__status": "", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____columns__title__timeEnded": "", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____columns__title__timeStarted": "", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____emptyStateText": "", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____title": "", "@sage/xtrem-scheduler/stickers__notification_sticker__isEnabled____title": "", "@sage/xtrem-scheduler/stickers__notification_sticker__markAsRead____title": "", "@sage/xtrem-scheduler/stickers__notification_sticker__section1____title": "", "@sage/xtrem-scheduler/sys-client-notification__number_of_records_purged": "", "@sage/xtrem-scheduler/sys-job-schedule/missing-parameters": "", "@sage/xtrem-scheduler/sys-job-schedule/validate-parameters": "", "@sage/xtrem-scheduler/sys-notification-state__bulkStop_only_pending-running": "", "@sage/xtrem-scheduler/sys-notification-state__number_of_records_purged": ""}