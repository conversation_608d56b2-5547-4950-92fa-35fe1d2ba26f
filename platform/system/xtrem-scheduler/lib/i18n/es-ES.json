{" @sage/xtrem-scheduler/nodes__sys-job-schedule__once": "Ejecutar una vez", "@sage/xtrem-scheduler/activity__sys_job_schedule__name": "Programación de trabajo de sistema", "@sage/xtrem-scheduler/data_types__cron_schedule__name": "Programación CRON", "@sage/xtrem-scheduler/data_types__job_status_enum__name": "Job status enum", "@sage/xtrem-scheduler/data_types__parameter_type_enum__name": "Parameter type enum", "@sage/xtrem-scheduler/data_types__timezone_schedule__name": "Timezone schedule", "@sage/xtrem-scheduler/data-types/cron_schedule__invalid_schedule": "El valor de la programación {{value}} no es correcto.", "@sage/xtrem-scheduler/data-types/timezone__invalid_timezone": "El valor de la zona horaria {{value}} no es correcto.", "@sage/xtrem-scheduler/enums__job_status__done": "Finalizado", "@sage/xtrem-scheduler/enums__job_status__error": "Con errores", "@sage/xtrem-scheduler/enums__job_status__interrupted": "Interrumpido", "@sage/xtrem-scheduler/enums__job_status__interruptRequested": "Solicitud de interrupción", "@sage/xtrem-scheduler/enums__job_status__new": "Nuevo", "@sage/xtrem-scheduler/enums__job_status__notResponding": "Sin respuesta", "@sage/xtrem-scheduler/enums__job_status__pending": "Pendiente", "@sage/xtrem-scheduler/enums__job_status__running": "En curso", "@sage/xtrem-scheduler/enums__job_status__stopped": "Cancelado", "@sage/xtrem-scheduler/enums__job_status__stopRequested": "Solicitud de cancelación", "@sage/xtrem-scheduler/enums__log_severity__error": "Error", "@sage/xtrem-scheduler/enums__log_severity__exception": "Excepción", "@sage/xtrem-scheduler/enums__log_severity__info": "Información", "@sage/xtrem-scheduler/enums__log_severity__test": "Prueba", "@sage/xtrem-scheduler/enums__log_severity__warning": "Aviso", "@sage/xtrem-scheduler/enums__parameter_type__boolean": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/enums__parameter_type__date": "<PERSON><PERSON>", "@sage/xtrem-scheduler/enums__parameter_type__dateRange": "<PERSON><PERSON>", "@sage/xtrem-scheduler/enums__parameter_type__datetime": "<PERSON><PERSON> y hora", "@sage/xtrem-scheduler/enums__parameter_type__datetimeRange": "<PERSON><PERSON> de fecha y hora", "@sage/xtrem-scheduler/enums__parameter_type__decimal": "Decimal", "@sage/xtrem-scheduler/enums__parameter_type__decimalRange": "Rango de decimales", "@sage/xtrem-scheduler/enums__parameter_type__double": "Doble", "@sage/xtrem-scheduler/enums__parameter_type__enum": "Enumeración", "@sage/xtrem-scheduler/enums__parameter_type__enumArray": "Matriz de enumeraciones", "@sage/xtrem-scheduler/enums__parameter_type__float": "Flotante", "@sage/xtrem-scheduler/enums__parameter_type__integer": "Número entero", "@sage/xtrem-scheduler/enums__parameter_type__integerArray": "<PERSON><PERSON> de números enteros", "@sage/xtrem-scheduler/enums__parameter_type__integerRange": "Rango de números enteros", "@sage/xtrem-scheduler/enums__parameter_type__reference": "Referencia", "@sage/xtrem-scheduler/enums__parameter_type__referenceArray": "<PERSON><PERSON>", "@sage/xtrem-scheduler/enums__parameter_type__string": "Cadena", "@sage/xtrem-scheduler/enums__parameter_type__stringArray": "<PERSON><PERSON>den<PERSON>", "@sage/xtrem-scheduler/enums__parameter_type__time": "Tiempo", "@sage/xtrem-scheduler/enums__parameter_type__uuid": "UUID", "@sage/xtrem-scheduler/job-manager_started-by-scheduler": "El programador ha iniciado {{description}}.", "@sage/xtrem-scheduler/node-extensions__sys_client_notification_extension__asyncMutation__purgeSysClientNotification": "Depurar notificación de cliente de sistema", "@sage/xtrem-scheduler/node-extensions__sys_client_notification_extension__asyncMutation__purgeSysClientNotification__failed": "Error al depurar la notificación del cliente de sistema.", "@sage/xtrem-scheduler/node-extensions__sys_client_notification_extension__asyncMutation__purgeSysClientNotification__parameter__duration": "Duración", "@sage/xtrem-scheduler/node-extensions__sys_client_notification_extension__asyncMutation__purgeSysClientNotification__parameter__level": "<PERSON><PERSON>", "@sage/xtrem-scheduler/node-extensions__sys_client_notification_extension__asyncMutation__purgeSysClientNotification__parameter__unit": "Unidad", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__purgeHistory": "Depurar historial", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__purgeHistory__failed": "Error al depurar el historial", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__purgeHistory__parameter__duration": "Duración", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__purgeHistory__parameter__operationNames": "Nombres de operaciones", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__purgeHistory__parameter__unit": "Unidad", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__simulateAsyncMutation": "Simular mutación asincrónica", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__simulateAsyncMutation__failed": "Error al simular la mutación asincrónica", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__simulateAsyncMutation__parameter__delayInSec": "Retraso en segundos", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__simulateAsyncMutation__parameter__id": "Id.", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__simulateAsyncMutation__parameter__throwAt": "<PERSON><PERSON>o", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__bulkMutation__bulkStop": "Cancelar en masa", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__bulkMutation__bulkStop__failed": "Error al cancelar en masa", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__bulkMutation__bulkStop__parameter__reason": "Motivo", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__bulkMutation__markAsRead": "Marcar como leído", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__bulkMutation__markAsRead__failed": "Error al marcar como leído", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__execute": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__execute__failed": "Error de ejecución", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__execute__parameter__jobExecution": "Ejecución de trabajo", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__notRespondingJob": "Trabajo sin respuesta", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__notRespondingJob__failed": "Error de trabajo sin respuesta", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__notRespondingJob__parameter__duration": "Duración", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__notRespondingJob__parameter__unit": "Unidad", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__stop": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__stop__failed": "Error de cancelación", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__stop__parameter__jobExecutionSysId": "Id. de sistema de ejecución de trabajo", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__stop__parameter__reason": "Motivo", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__property__isScheduled": "Programada", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__property__schedule": "Programación", "@sage/xtrem-scheduler/nodes__sys_chore__asyncMutation__purgeContentAddressableTables": "Depurar contenido de tablas direccionables", "@sage/xtrem-scheduler/nodes__sys_chore__asyncMutation__purgeContentAddressableTables__failed": "Error al depurar el contenido de las tablas direccionables", "@sage/xtrem-scheduler/nodes__sys_chore__node_name": "Procedimientos de sistema", "@sage/xtrem-scheduler/nodes__sys_job_schedule__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-scheduler/nodes__sys_job_schedule__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-scheduler/nodes__sys_job_schedule__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-scheduler/nodes__sys_job_schedule__bulkMutation__bulkDelete": "Eliminar en masa", "@sage/xtrem-scheduler/nodes__sys_job_schedule__mutation__purgeExecution": "Depurar ejecución", "@sage/xtrem-scheduler/nodes__sys_job_schedule__mutation__purgeExecution__failed": "Error al depurar la ejecución", "@sage/xtrem-scheduler/nodes__sys_job_schedule__mutation__purgeExecution__parameter__allStatus": "<PERSON><PERSON>", "@sage/xtrem-scheduler/nodes__sys_job_schedule__mutation__purgeExecution__parameter__schedule": "Programación", "@sage/xtrem-scheduler/nodes__sys_job_schedule__node_name": "Programación de trabajo de sistema", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__cronSchedule": "Programación CRON", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__cronTranslated": "Descripción de CRON", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__description": "Descripción", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__endStamp": "Fin", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__executionLocale": "Parámetros regionales de ejecución", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__executions": "Ejecuciones", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__executionUser": "Usuario de ejecución", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__id": "Id.", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__isActive": "Activa", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__nextExecutionStamp": "Siguiente ejecución", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__operation": "Operación", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__parameterValues": "Valores de parámetro", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__startStamp": "<PERSON><PERSON>o", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__sysJobDefinition": "Definición de trabajo de sistema", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__timeZone": "Zona horaria", "@sage/xtrem-scheduler/nodes__sys_job_schedule__query__cronInfo": "Información CRON", "@sage/xtrem-scheduler/nodes__sys_job_schedule__query__cronInfo__failed": "Error de información CRON", "@sage/xtrem-scheduler/nodes__sys_job_schedule__query__timezones": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/nodes__sys_job_schedule__query__timezones__failed": "<PERSON><PERSON>r de zonas horarias", "@sage/xtrem-scheduler/nodes__sys-job-execution_cron-or-start-date": "Falta la fecha de inicio o una cadena CRON. ", "@sage/xtrem-scheduler/nodes__sys-job-execution_no-next-execution": "El trabajo no se ejecutará.", "@sage/xtrem-scheduler/nodes__sys-job-execution_operation-schedulable": "La operación no se puede programar.", "@sage/xtrem-scheduler/nodes__sys-job-execution_parsing-error": "Ha habido un error al analizar la siguiente programación.", "@sage/xtrem-scheduler/nodes__sys-job-schedule__execution_user_not_authorized": "Solo puedes asignarte a ti como usuario de ejecución.", "@sage/xtrem-scheduler/nodes__sys-job-schedule__no-cron-expression-no-startStamp": "La fecha y la hora de inicio son obligatorias si la expresión CRON no se ha definido.", "@sage/xtrem-scheduler/nodes__sys-job-schedule__once": "Ejecutar una vez", "@sage/xtrem-scheduler/nodes_sys_notification_state_cannot_stop_process_not_running": "El proceso no se puede cancelar porque ya no se está ejecutando.", "@sage/xtrem-scheduler/package__name": "Programador", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension____navigationPanel__listItem__line1__title": "Programación", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension____navigationPanel__listItem__line2__title": "Operación", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension____navigationPanel__listItem__line7__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__execute____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__operation____columns__title__factory__name": "Nodo", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__operation____columns__title__isSchedulable": "Programable", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__operation____columns__title__name": "Operación", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__operation____title": "Operación", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__schedule____title": "Programación", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__schedulePage____title": "Programación", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__stop____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages__operation_panel____title": "Panel de operación", "@sage/xtrem-scheduler/pages__operation_panel__generalSection____title": "General", "@sage/xtrem-scheduler/pages__operation_panel__parameters____columns__title__name": "Nombre", "@sage/xtrem-scheduler/pages__operation_panel__parameters____columns__title__value": "Valor", "@sage/xtrem-scheduler/pages__operation_panel__parameters____title": "Parámetros", "@sage/xtrem-scheduler/pages__sys_job_definition____navigationPanel__listItem__title__title": "Nombre", "@sage/xtrem-scheduler/pages__sys_job_definition____objectTypePlural": "<PERSON><PERSON><PERSON> por lotes", "@sage/xtrem-scheduler/pages__sys_job_definition____objectTypeSingular": "<PERSON><PERSON> por lotes", "@sage/xtrem-scheduler/pages__sys_job_definition____title": "<PERSON><PERSON> por lotes", "@sage/xtrem-scheduler/pages__sys_job_definition__description____title": "Descripción", "@sage/xtrem-scheduler/pages__sys_job_definition__generalSection____title": "General", "@sage/xtrem-scheduler/pages__sys_job_definition__isActive____title": "Activa", "@sage/xtrem-scheduler/pages__sys_job_definition__name____title": "Nombre", "@sage/xtrem-scheduler/pages__sys_job_execution____navigationPanel__listItem__line__title": "Nodo", "@sage/xtrem-scheduler/pages__sys_job_execution____navigationPanel__listItem__line2__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_execution____navigationPanel__listItem__line3__title": "<PERSON><PERSON>o", "@sage/xtrem-scheduler/pages__sys_job_execution____navigationPanel__listItem__line4__title": "Fin", "@sage/xtrem-scheduler/pages__sys_job_execution____navigationPanel__listItem__title__title": "Nombre", "@sage/xtrem-scheduler/pages__sys_job_execution____navigationPanel__optionsMenu__title": "<PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_execution____objectTypePlural": "Ejecución de trabajos", "@sage/xtrem-scheduler/pages__sys_job_execution____objectTypeSingular": "Ejecución de trabajo", "@sage/xtrem-scheduler/pages__sys_job_execution____title": "Ejecución de trabajo", "@sage/xtrem-scheduler/pages__sys_job_execution__execute____title": "", "@sage/xtrem-scheduler/pages__sys_job_execution__generalSection____title": "General", "@sage/xtrem-scheduler/pages__sys_job_execution__logs____columns__title__lineNumber": "#", "@sage/xtrem-scheduler/pages__sys_job_execution__logs____columns__title__severity": "Gravedad", "@sage/xtrem-scheduler/pages__sys_job_execution__logs____columns__title__text": "Men<PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_execution__logs____columns__title__timestamp": "<PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_execution__logs____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_execution__logsSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_execution__operation____columns__title": "Nodo", "@sage/xtrem-scheduler/pages__sys_job_execution__operation____columns__title__isSchedulable": "Programable", "@sage/xtrem-scheduler/pages__sys_job_execution__operation____columns__title__name": "Operación", "@sage/xtrem-scheduler/pages__sys_job_execution__operation____columns__title__name__2": "", "@sage/xtrem-scheduler/pages__sys_job_execution__operation____title": "Operación", "@sage/xtrem-scheduler/pages__sys_job_execution__parameters____columns__title__name": "Nombre", "@sage/xtrem-scheduler/pages__sys_job_execution__parameters____columns__title__value": "Valor", "@sage/xtrem-scheduler/pages__sys_job_execution__parameters____title": "Parámetros", "@sage/xtrem-scheduler/pages__sys_job_execution__status____title": "Estado", "@sage/xtrem-scheduler/pages__sys_job_execution__timeEnded____title": "Hora de fin", "@sage/xtrem-scheduler/pages__sys_job_execution__timeStarted____title": "Hora de inicio", "@sage/xtrem-scheduler/pages__sys_job_execution__trackingId____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__bulkActions__title": "Eliminar", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__line_5__title": "Siguiente programación", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__line1__title": "Usuario", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__line2Right__title": "Programación CRON", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__line3__title": "Activa", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__line3Right__title": "Operación", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__line5__title": "Siguiente programación", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__title__title": "Id.", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__titleRight__title": "Descripción", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__optionsMenu__title": "<PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule____objectTypePlural": "Programación de tareas", "@sage/xtrem-scheduler/pages__sys_job_schedule____objectTypeSingular": "Programación de tarea", "@sage/xtrem-scheduler/pages__sys_job_schedule____title": "Programación de tarea", "@sage/xtrem-scheduler/pages__sys_job_schedule__createCrud____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule__cronSchedule____title": "Expresión CRON", "@sage/xtrem-scheduler/pages__sys_job_schedule__cronTranslated____title": "Descripción de CRON", "@sage/xtrem-scheduler/pages__sys_job_schedule__description____title": "Descripción", "@sage/xtrem-scheduler/pages__sys_job_schedule__endStamp____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule__execute____title": "<PERSON><PERSON><PERSON><PERSON> ahora", "@sage/xtrem-scheduler/pages__sys_job_schedule__executeSection____title": "Historial", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____columns__title___id": "Detalles", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____columns__title__notificationId": "Detalles", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____columns__title__status": "Estado", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____columns__title__timeEnded": "Fin", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____columns__title__timeStarted": "<PERSON><PERSON>o", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____dropdownActions__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____title": "<PERSON><PERSON><PERSON> e<PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule__executionUser____columns__title__email": "E-mail", "@sage/xtrem-scheduler/pages__sys_job_schedule__executionUser____columns__title__firstName": "Nombre", "@sage/xtrem-scheduler/pages__sys_job_schedule__executionUser____columns__title__lastName": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule__executionUser____title": "Usuario", "@sage/xtrem-scheduler/pages__sys_job_schedule__generalSection____title": "General", "@sage/xtrem-scheduler/pages__sys_job_schedule__id____title": "Id.", "@sage/xtrem-scheduler/pages__sys_job_schedule__isActive____title": "Activa", "@sage/xtrem-scheduler/pages__sys_job_schedule__operation____columns__title__description": "Operación", "@sage/xtrem-scheduler/pages__sys_job_schedule__operation____columns__title__factory__name": "Nodo", "@sage/xtrem-scheduler/pages__sys_job_schedule__operation____columns__title__isSchedulable": "Programable", "@sage/xtrem-scheduler/pages__sys_job_schedule__operation____columns__title__title": "Operación", "@sage/xtrem-scheduler/pages__sys_job_schedule__operation____title": "Operación", "@sage/xtrem-scheduler/pages__sys_job_schedule__parameters____columns__title__name": "Nombre", "@sage/xtrem-scheduler/pages__sys_job_schedule__parameters____columns__title__value": "Valor", "@sage/xtrem-scheduler/pages__sys_job_schedule__parameters____title": "Parámetros", "@sage/xtrem-scheduler/pages__sys_job_schedule__purgeExecution____title": "Depurar ejecución", "@sage/xtrem-scheduler/pages__sys_job_schedule__refreshExecutedJobs____title": "Actualizar", "@sage/xtrem-scheduler/pages__sys_job_schedule__refreshExecutions____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule__startStamp____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule__timeZone____title": "Zona horaria", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard____title": "Nueva tarea por lotes", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__atJobHour____placeholder": "<PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__atJobHour____title": "Hora de ejecución", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__atJobMinute____placeholder": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__batch_task_scheduled": "Tarea por lotes programada: {{0}}", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__batch_task_started": "La tarea por lotes se ha iniciado.", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__cronString____title": "Expresión CRON", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__daily": "Diaria", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__days____title": "Días", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__defineSection____title": "Definir", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__friday": "Viernes", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__invalid_date": "La fecha no es válida.", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__invalid_date_with_argument": "La fecha no es válida: {{0}}", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__job____columns__title__description": "Operación", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__job____columns__title__factory__name": "Origen", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__job____columns__title__name": "Operation", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__job____columns__title__title": "Operación", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__job____title": "Definición de tarea", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobDate____title": "Fecha de inicio", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobHour____placeholder": "<PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobHour____title": "Hora de inicio", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobMinute____placeholder": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobParameters____columns__title__name": "Parámetro", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobParameters____columns__title__value": "Valor", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobParameters____title": "Parámetros", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobSchedule____title": "Programación", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__monday": "<PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__monthly": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__needs_to_be_a_number": "Este valor debe ser un número.", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__needs_to_be_either_true_or_false": "Este valor debe ser uno de los siguientes: {{0}}, {{1}}.", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__needs_to_be_in_the_future": "Esta fecha debe ser futura.", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__needs_to_be_in_the_future_with_argument": "Esta fecha debe ser futura: {{0}}", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__now": "Now", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__once": "Once", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__reccurence____title": "Periodicidad", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__recurrence-not-set": "Define la periodicidad.", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__recurring": "Periódica", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__run_now": "<PERSON><PERSON><PERSON><PERSON> ahora", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__run_once": "Ejecutar una vez", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__saturday": "Sábado", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__scheduleSection____title": "Programar", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__settingsSection____title": "Configurar", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__stop_date_must_be_later_start_date": "La fecha de fin debe ser posterior a la fecha de inicio.", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__stopJobDate____title": "<PERSON><PERSON> de fin", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__stopJobHour____placeholder": "<PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__stopJobHour____title": "Hora de fin", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__stopJobMinute____placeholder": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__sunday": "Domingo", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__thursday": "<PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__tuesday": "<PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__wednesday": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__weekly": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_notification_state__ask_by_user": "Solicitud de usuario", "@sage/xtrem-scheduler/pages__workflow_event_schedule____title": "Configuración de desencadenante", "@sage/xtrem-scheduler/pages__workflow_event_schedule__cronSchedule____title": "Expresión CRON", "@sage/xtrem-scheduler/pages__workflow_event_schedule__endHour____infoMessage": "Formato: hh:mm", "@sage/xtrem-scheduler/pages__workflow_event_schedule__endHour____title": "Hora de fin", "@sage/xtrem-scheduler/pages__workflow_event_schedule__endStamp____title": "<PERSON><PERSON> de fin", "@sage/xtrem-scheduler/pages__workflow_event_schedule__mainSection____title": "Programación", "@sage/xtrem-scheduler/pages__workflow_event_schedule__startHour____infoMessage": "Formato: hh:mm", "@sage/xtrem-scheduler/pages__workflow_event_schedule__startHour____title": "Hora de inicio", "@sage/xtrem-scheduler/pages__workflow_event_schedule__startStamp____title": "Fecha de inicio", "@sage/xtrem-scheduler/pages__workflow_event_schedule__timeZone____title": "Zona horaria", "@sage/xtrem-scheduler/pages-confirm-cancel": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages-confirm-purge": "Eliminar", "@sage/xtrem-scheduler/permission__manage__name": "Gestionar", "@sage/xtrem-scheduler/permission__read__name": "<PERSON><PERSON>", "@sage/xtrem-scheduler/purge-sys-execute-history": "¿Quieres eliminar el historial?", "@sage/xtrem-scheduler/purge-sys-message-history-title": "Eliminar historial", "@sage/xtrem-scheduler/stickers__notification_sticker____title": "Centro de notificaciones", "@sage/xtrem-scheduler/stickers__notification_sticker__block1____title": "Bloque 1", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____columns__title__lastMessage": "<PERSON>lt<PERSON>", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____columns__title__notificationId": "Detalles", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____columns__title__operationName": "Nombre", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____columns__title__status": "Estado", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____columns__title__timeEnded": "Fin", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____columns__title__timeStarted": "<PERSON><PERSON>o", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____emptyStateText": "Sin notificaciones", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____title": "<PERSON><PERSON><PERSON> e<PERSON>", "@sage/xtrem-scheduler/stickers__notification_sticker__isEnabled____title": "Habilitar notificaciones", "@sage/xtrem-scheduler/stickers__notification_sticker__markAsRead____title": "Marcar como leído", "@sage/xtrem-scheduler/stickers__notification_sticker__section1____title": "POC: Notification center", "@sage/xtrem-scheduler/sys-client-notification__number_of_records_purged": "Registros eliminados: {{numberDeleted}}", "@sage/xtrem-scheduler/sys-job-definition/validate-parameters-reference-type": "El parámetro {{parameterName}} es de referencia, pero no se ha definido el nodo nodeName.", "@sage/xtrem-scheduler/sys-job-schedule/invalid-parameters-value": "El valor del parámetro {{failedParsedParameterNames}} no es válido.", "@sage/xtrem-scheduler/sys-job-schedule/missing-parameters": "La tarea {{jobDefinitionName}} debe tener el parámetro {{missingParameterNames}} asignado.", "@sage/xtrem-scheduler/sys-job-schedule/validate-parameters": "La programación de la tarea {{jobDefinitionName}} tiene más parámetros que su definición.", "@sage/xtrem-scheduler/sys-notification-state__bulkStop_only_pending-running": "Solo puedes cancelar un trabajo en curso o pendiente.", "@sage/xtrem-scheduler/sys-notification-state__number_of_records_purged": "Registros eliminados: {{numberDeleted}}"}