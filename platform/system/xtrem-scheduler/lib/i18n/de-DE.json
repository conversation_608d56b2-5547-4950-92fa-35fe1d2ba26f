{" @sage/xtrem-scheduler/nodes__sys-job-schedule__once": "Ein<PERSON>ig", "@sage/xtrem-scheduler/activity__sys_job_schedule__name": "Job-Zeitplan System", "@sage/xtrem-scheduler/data_types__cron_schedule__name": "Cron-Zeitplan", "@sage/xtrem-scheduler/data_types__job_status_enum__name": "Enum Job-Status", "@sage/xtrem-scheduler/data_types__parameter_type_enum__name": "Enum Parametertyp", "@sage/xtrem-scheduler/data_types__timezone_schedule__name": "Plan Zeitzone", "@sage/xtrem-scheduler/data-types/cron_schedule__invalid_schedule": "Falscher Zeitplanwert: {{value}}.", "@sage/xtrem-scheduler/data-types/timezone__invalid_timezone": "Falscher Zeitzonenwert: ({{value}}).", "@sage/xtrem-scheduler/enums__job_status__done": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/enums__job_status__error": "<PERSON><PERSON>", "@sage/xtrem-scheduler/enums__job_status__interrupted": "Unterbrochen", "@sage/xtrem-scheduler/enums__job_status__interruptRequested": "Unterbrechen angefordert", "@sage/xtrem-scheduler/enums__job_status__new": "<PERSON>eu", "@sage/xtrem-scheduler/enums__job_status__notResponding": "Antwortet nicht", "@sage/xtrem-scheduler/enums__job_status__pending": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/enums__job_status__running": "Läuft", "@sage/xtrem-scheduler/enums__job_status__stopped": "Ang<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/enums__job_status__stopRequested": "<PERSON><PERSON><PERSON> an<PERSON>", "@sage/xtrem-scheduler/enums__log_severity__error": "<PERSON><PERSON>", "@sage/xtrem-scheduler/enums__log_severity__exception": "Ausnahme", "@sage/xtrem-scheduler/enums__log_severity__info": "Info", "@sage/xtrem-scheduler/enums__log_severity__test": "Test", "@sage/xtrem-scheduler/enums__log_severity__warning": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/enums__parameter_type__boolean": "Boolean", "@sage/xtrem-scheduler/enums__parameter_type__date": "Datum", "@sage/xtrem-scheduler/enums__parameter_type__dateRange": "Datumsbereich", "@sage/xtrem-scheduler/enums__parameter_type__datetime": "Datum/Uhrzeit (Datetime)", "@sage/xtrem-scheduler/enums__parameter_type__datetimeRange": "Datums- und Uhrzeitbereich", "@sage/xtrem-scheduler/enums__parameter_type__decimal": "Dezimal", "@sage/xtrem-scheduler/enums__parameter_type__decimalRange": "Dezimalbereich", "@sage/xtrem-scheduler/enums__parameter_type__double": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/enums__parameter_type__enum": "Enum", "@sage/xtrem-scheduler/enums__parameter_type__enumArray": "Enum-<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/enums__parameter_type__float": "Float", "@sage/xtrem-scheduler/enums__parameter_type__integer": "Integer", "@sage/xtrem-scheduler/enums__parameter_type__integerArray": "Integer-Array", "@sage/xtrem-scheduler/enums__parameter_type__integerRange": "Integer-Bereich", "@sage/xtrem-scheduler/enums__parameter_type__reference": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/enums__parameter_type__referenceArray": "Referenz-Array", "@sage/xtrem-scheduler/enums__parameter_type__string": "String", "@sage/xtrem-scheduler/enums__parameter_type__stringArray": "String-<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/enums__parameter_type__time": "Zeit", "@sage/xtrem-scheduler/enums__parameter_type__uuid": "UUID", "@sage/xtrem-scheduler/job-manager_started-by-scheduler": "Gestartet durch Planer: {{description}}", "@sage/xtrem-scheduler/node-extensions__sys_client_notification_extension__asyncMutation__purgeSysClientNotification": "Benachrichtigung System-Client leeren", "@sage/xtrem-scheduler/node-extensions__sys_client_notification_extension__asyncMutation__purgeSysClientNotification__failed": "Benachrichtigung System-Client leeren fehlgeschlagen.", "@sage/xtrem-scheduler/node-extensions__sys_client_notification_extension__asyncMutation__purgeSysClientNotification__parameter__duration": "<PERSON><PERSON>", "@sage/xtrem-scheduler/node-extensions__sys_client_notification_extension__asyncMutation__purgeSysClientNotification__parameter__level": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/node-extensions__sys_client_notification_extension__asyncMutation__purgeSysClientNotification__parameter__unit": "Einheit", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__purgeHistory": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__purgeHistory__failed": "<PERSON><PERSON><PERSON><PERSON> leeren fehlgeschlagen.", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__purgeHistory__parameter__duration": "<PERSON><PERSON>", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__purgeHistory__parameter__operationNames": "Vorgangsnamen", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__purgeHistory__parameter__unit": "Einheit", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__simulateAsyncMutation": "Asynchrone Mutation simulieren", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__simulateAsyncMutation__failed": "Asynchrone Mutation simulieren fehlgeschlagen.", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__simulateAsyncMutation__parameter__delayInSec": "Verzögerung in Sekunden", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__simulateAsyncMutation__parameter__id": "ID", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__simulateAsyncMutation__parameter__throwAt": "Ausgeben nach", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__bulkMutation__bulkStop": "Massenanhalten", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__bulkMutation__bulkStop__failed": "Massenanhalten fehlgeschlagen.", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__bulkMutation__bulkStop__parameter__reason": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__bulkMutation__markAsRead": "Als gelesen markieren", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__bulkMutation__markAsRead__failed": "Als gelesen markieren fehlgeschlagen.", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__execute": "Ausführen", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__execute__failed": "Ausführen fehlgeschlagen.", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__execute__parameter__jobExecution": "Ausführung Job", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__notRespondingJob": "<PERSON> antwortet nicht", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__notRespondingJob__failed": "<PERSON> antwortet nicht fehlgeschlagen.", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__notRespondingJob__parameter__duration": "<PERSON><PERSON>", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__notRespondingJob__parameter__unit": "Einheit", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__stop": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__stop__failed": "Anhalten fehlgeschlagen.", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__stop__parameter__jobExecutionSysId": "System-ID Job-Ausführung", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__stop__parameter__reason": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__property__isScheduled": "<PERSON><PERSON>", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__property__schedule": "Planen", "@sage/xtrem-scheduler/nodes__sys_chore__asyncMutation__purgeContentAddressableTables": "Inhalt adressierbare Tabellen bereinigen", "@sage/xtrem-scheduler/nodes__sys_chore__asyncMutation__purgeContentAddressableTables__failed": "Inhalt adressierbare Tabellen bereinigen fehlgeschlagen.", "@sage/xtrem-scheduler/nodes__sys_chore__node_name": "System-Aufgaben", "@sage/xtrem-scheduler/nodes__sys_job_log_entry__property__severity": "Dringlichkeit", "@sage/xtrem-scheduler/nodes__sys_job_schedule__asyncMutation__asyncExport": "Export", "@sage/xtrem-scheduler/nodes__sys_job_schedule__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-scheduler/nodes__sys_job_schedule__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-scheduler/nodes__sys_job_schedule__bulkMutation__bulkDelete": "Massenlöschen", "@sage/xtrem-scheduler/nodes__sys_job_schedule__mutation__purgeExecution": "Ausführung leeren", "@sage/xtrem-scheduler/nodes__sys_job_schedule__mutation__purgeExecution__failed": "Ausführung leeren fehlgeschlagen.", "@sage/xtrem-scheduler/nodes__sys_job_schedule__mutation__purgeExecution__parameter__allStatus": "Alle Statuswerte", "@sage/xtrem-scheduler/nodes__sys_job_schedule__mutation__purgeExecution__parameter__schedule": "Planen", "@sage/xtrem-scheduler/nodes__sys_job_schedule__node_name": "Job-Zeitplan System", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__cronSchedule": "Cron-Zeitplan", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__cronTranslated": "Cron übersetzt", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__description": "Bezeichnung", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__endStamp": "Stempel Ende", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__executionLocale": "Gebietsschema Ausführung", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__executions": "Ausführungen", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__executionUser": "Benutzer der Ausführung", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__id": "ID", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__isActive": "Ist aktiv", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__nextExecutionStamp": "Stempel nächste Ausführung", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__operation": "Vorgang", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__parameterValues": "Parameterwerte", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__startStamp": "Stempel Start", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__sysJobDefinition": "System Job-Definition", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__timeZone": "Zeitzone", "@sage/xtrem-scheduler/nodes__sys_job_schedule__query__cronInfo": "Cron-Info", "@sage/xtrem-scheduler/nodes__sys_job_schedule__query__cronInfo__failed": "Cron-Info fehlgeschlagen.", "@sage/xtrem-scheduler/nodes__sys_job_schedule__query__timezones": "Zeitzonen", "@sage/xtrem-scheduler/nodes__sys_job_schedule__query__timezones__failed": "Zeitzonen fehlgeschlagen.", "@sage/xtrem-scheduler/nodes__sys-job-execution_cron-or-start-date": "Ein Startdatum oder ein Cron-String ist erforderlich ", "@sage/xtrem-scheduler/nodes__sys-job-execution_no-next-execution": "Dieser Job wird nicht ausgeführt.", "@sage/xtrem-scheduler/nodes__sys-job-execution_operation-schedulable": "<PERSON>ser Vorgang kann nicht geplant werden", "@sage/xtrem-scheduler/nodes__sys-job-execution_parsing-error": "Problem beim <PERSON> des nächsten Zeitplans.", "@sage/xtrem-scheduler/nodes__sys-job-schedule__execution_user_not_authorized": "<PERSON>e können den Benutzer der Ausführung nur sich selbst zuweisen.", "@sage/xtrem-scheduler/nodes__sys-job-schedule__no-cron-expression-no-startStamp": "<PERSON><PERSON> benöti<PERSON> einen Start-Datetime, wenn der Cron-Ausdruck nicht definiert ist.", "@sage/xtrem-scheduler/nodes__sys-job-schedule__once": "Ein<PERSON>ig", "@sage/xtrem-scheduler/nodes_sys_notification_state_cannot_stop_process_not_running": "<PERSON>ser Prozess kann nicht angehalten werden, da er nicht mehr läuft.", "@sage/xtrem-scheduler/package__name": "Planer", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension____navigationPanel__listItem__line1__title": "Plan", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension____navigationPanel__listItem__line2__title": "Vorgang", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension____navigationPanel__listItem__line7__title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__execute____title": "Ausführen", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__operation____columns__title__factory__name": "Node", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__operation____columns__title__isSchedulable": "<PERSON>nn planen", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__operation____columns__title__name": "Vorgang", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__operation____title": "Vorgang", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__schedule____title": "Planen", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__schedulePage____title": "Planen", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__stop____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages__operation_panel____title": "Vorgangsbereich", "@sage/xtrem-scheduler/pages__operation_panel__generalSection____title": "Allgemein", "@sage/xtrem-scheduler/pages__operation_panel__parameters____columns__title__name": "Name", "@sage/xtrem-scheduler/pages__operation_panel__parameters____columns__title__value": "Wert", "@sage/xtrem-scheduler/pages__operation_panel__parameters____title": "Parameter", "@sage/xtrem-scheduler/pages__sys_job_definition____navigationPanel__listItem__title__title": "Name", "@sage/xtrem-scheduler/pages__sys_job_definition____objectTypePlural": "Batchaufgaben", "@sage/xtrem-scheduler/pages__sys_job_definition____objectTypeSingular": "Batchaufgabe", "@sage/xtrem-scheduler/pages__sys_job_definition____title": "Batchaufgabe", "@sage/xtrem-scheduler/pages__sys_job_definition__description____title": "Bezeichnung", "@sage/xtrem-scheduler/pages__sys_job_definition__generalSection____title": "Allgemein", "@sage/xtrem-scheduler/pages__sys_job_definition__isActive____title": "Aktiv", "@sage/xtrem-scheduler/pages__sys_job_definition__name____title": "Name", "@sage/xtrem-scheduler/pages__sys_job_execution____navigationPanel__listItem__line__title": "Node", "@sage/xtrem-scheduler/pages__sys_job_execution____navigationPanel__listItem__line2__title": "<PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_execution____navigationPanel__listItem__line3__title": "Gestartet", "@sage/xtrem-scheduler/pages__sys_job_execution____navigationPanel__listItem__line4__title": "<PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_execution____navigationPanel__listItem__title__title": "Name", "@sage/xtrem-scheduler/pages__sys_job_execution____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-scheduler/pages__sys_job_execution____objectTypePlural": "Ausführung Jobs", "@sage/xtrem-scheduler/pages__sys_job_execution____objectTypeSingular": "Ausführung Job", "@sage/xtrem-scheduler/pages__sys_job_execution____title": "Ausführung Job", "@sage/xtrem-scheduler/pages__sys_job_execution__execute____title": "Ausführen", "@sage/xtrem-scheduler/pages__sys_job_execution__generalSection____title": "Allgemein", "@sage/xtrem-scheduler/pages__sys_job_execution__logs____columns__title__lineNumber": "#", "@sage/xtrem-scheduler/pages__sys_job_execution__logs____columns__title__severity": "Dringlichkeit", "@sage/xtrem-scheduler/pages__sys_job_execution__logs____columns__title__text": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_execution__logs____columns__title__timestamp": "Zeit", "@sage/xtrem-scheduler/pages__sys_job_execution__logs____title": "Protokolle", "@sage/xtrem-scheduler/pages__sys_job_execution__logsSection____title": "Protokolle", "@sage/xtrem-scheduler/pages__sys_job_execution__operation____columns__title": "Node", "@sage/xtrem-scheduler/pages__sys_job_execution__operation____columns__title__isSchedulable": "<PERSON>nn planen", "@sage/xtrem-scheduler/pages__sys_job_execution__operation____columns__title__name": "Vorgang", "@sage/xtrem-scheduler/pages__sys_job_execution__operation____columns__title__name__2": "Vorgang", "@sage/xtrem-scheduler/pages__sys_job_execution__operation____title": "Vorgang", "@sage/xtrem-scheduler/pages__sys_job_execution__parameters____columns__title__name": "Name", "@sage/xtrem-scheduler/pages__sys_job_execution__parameters____columns__title__value": "Wert", "@sage/xtrem-scheduler/pages__sys_job_execution__parameters____title": "Parameter", "@sage/xtrem-scheduler/pages__sys_job_execution__status____title": "Status", "@sage/xtrem-scheduler/pages__sys_job_execution__timeEnded____title": "Endzeit", "@sage/xtrem-scheduler/pages__sys_job_execution__timeStarted____title": "Startzeit", "@sage/xtrem-scheduler/pages__sys_job_execution__trackingId____title": "Rückmeldungs-ID", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__bulkActions__title": "Löschen", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__line_5__title": "Nächster Plan", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__line1__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__line2Right__title": "Cron-Zeitplan", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__line3__title": "Aktiv", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__line3Right__title": "Vorgang", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__line5__title": "Nächster Plan", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__title__title": "ID", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__titleRight__title": "Bezeichnung", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-scheduler/pages__sys_job_schedule____objectTypePlural": "Jobs-Zeitplan", "@sage/xtrem-scheduler/pages__sys_job_schedule____objectTypeSingular": "Job-Zeitplan", "@sage/xtrem-scheduler/pages__sys_job_schedule____title": "Job-Zeitplan", "@sage/xtrem-scheduler/pages__sys_job_schedule__createCrud____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule__cronSchedule____title": "Cron-Ausdruck", "@sage/xtrem-scheduler/pages__sys_job_schedule__cronTranslated____title": "Cron übersetzt", "@sage/xtrem-scheduler/pages__sys_job_schedule__description____title": "Bezeichnung", "@sage/xtrem-scheduler/pages__sys_job_schedule__endStamp____title": "Enddatum", "@sage/xtrem-scheduler/pages__sys_job_schedule__execute____title": "Jetzt ausführen", "@sage/xtrem-scheduler/pages__sys_job_schedule__executeSection____title": "Ausführungsverlauf", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____columns__title___id": "Details", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____columns__title__notificationId": "Details", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____columns__title__status": "Status", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____columns__title__timeEnded": "<PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____columns__title__timeStarted": "Start", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____dropdownActions__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____title": "Ausgeführte Jobs", "@sage/xtrem-scheduler/pages__sys_job_schedule__executionUser____columns__title__email": "E-Mail", "@sage/xtrem-scheduler/pages__sys_job_schedule__executionUser____columns__title__firstName": "Name", "@sage/xtrem-scheduler/pages__sys_job_schedule__executionUser____columns__title__lastName": "Nachname", "@sage/xtrem-scheduler/pages__sys_job_schedule__executionUser____title": "Benutzer der Ausführung", "@sage/xtrem-scheduler/pages__sys_job_schedule__generalSection____title": "Allgemein", "@sage/xtrem-scheduler/pages__sys_job_schedule__id____title": "ID", "@sage/xtrem-scheduler/pages__sys_job_schedule__isActive____title": "Aktiv", "@sage/xtrem-scheduler/pages__sys_job_schedule__operation____columns__title__description": "Vorgang", "@sage/xtrem-scheduler/pages__sys_job_schedule__operation____columns__title__factory__name": "Node", "@sage/xtrem-scheduler/pages__sys_job_schedule__operation____columns__title__isSchedulable": "<PERSON>nn planen", "@sage/xtrem-scheduler/pages__sys_job_schedule__operation____columns__title__title": "Vorgang", "@sage/xtrem-scheduler/pages__sys_job_schedule__operation____title": "Vorgang", "@sage/xtrem-scheduler/pages__sys_job_schedule__parameters____columns__title__name": "Name", "@sage/xtrem-scheduler/pages__sys_job_schedule__parameters____columns__title__value": "Wert", "@sage/xtrem-scheduler/pages__sys_job_schedule__parameters____title": "Parameter", "@sage/xtrem-scheduler/pages__sys_job_schedule__purgeExecution____title": "Ausführung leeren", "@sage/xtrem-scheduler/pages__sys_job_schedule__refreshExecutedJobs____title": "Aktualisieren", "@sage/xtrem-scheduler/pages__sys_job_schedule__refreshExecutions____title": "", "@sage/xtrem-scheduler/pages__sys_job_schedule__startStamp____title": "Startdatum", "@sage/xtrem-scheduler/pages__sys_job_schedule__timeZone____title": "Zeitzone", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard____title": "Neue Batchaufgabe", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__atJobHour____placeholder": "Stunde", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__atJobHour____title": "Uhrzeit", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__atJobMinute____placeholder": "Minute", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__batch_task_scheduled": "Batchaufgabe wurde geplant: {{0}}", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__batch_task_started": "Batchaufgabe wurde gestartet", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__cronString____title": "Cron-Ausdruck", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__daily": "Tä<PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__days____title": "Tage", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__defineSection____title": "De<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__friday": "Freitag", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__invalid_date": "Ungültiges Datum", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__invalid_date_with_argument": "Ungültiges Datum: {{0}}", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__job____columns__title__description": "Vorgang", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__job____columns__title__factory__name": "<PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__job____columns__title__title": "Vorgang", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__job____title": "Job-Definition", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobDate____title": "Startdatum", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobHour____placeholder": "Stunde", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobHour____title": "Startzeit", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobMinute____placeholder": "Minute", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobParameters____columns__title__name": "Parameter", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobParameters____columns__title__value": "Wert", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobParameters____title": "Parameter", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobSchedule____title": "Plan", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__monday": "Montag", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__monthly": "<PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__needs_to_be_a_number": "Muss eine <PERSON> sein.", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__needs_to_be_either_true_or_false": "Muss einer der folgenden Werte sein: {{0}}, {{1}}.", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__needs_to_be_in_the_future": "Muss in der Zukunft liegen", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__needs_to_be_in_the_future_with_argument": "Muss in der Zukunft liegen: {{0}}", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__reccurence____title": "Wiederholung", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__recurrence-not-set": "<PERSON>e müssen die Wiederholung setzen.", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__recurring": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__run_now": "Jetzt ausführen", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__run_once": "Einmalig ausführen", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__saturday": "Samstag", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__scheduleSection____title": "Planen", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__settingsSection____title": "Einstellungen", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__stop_date_must_be_later_start_date": "Das Enddatum muss nach dem Startdatum liegen.", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__stopJobDate____title": "Enddatum", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__stopJobHour____placeholder": "Stunde", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__stopJobHour____title": "Endzeit", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__stopJobMinute____placeholder": "Minute", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__sunday": "Sonntag", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__thursday": "Don<PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__tuesday": "Dienstag", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__wednesday": "Mittwoch", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__weekly": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages__sys_notification_state__ask_by_user": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/pages__workflow_event_schedule____title": "Konfiguration Auslöser", "@sage/xtrem-scheduler/pages__workflow_event_schedule__cronSchedule____title": "Cron-Ausdruck", "@sage/xtrem-scheduler/pages__workflow_event_schedule__endHour____infoMessage": "Format: hh:mm", "@sage/xtrem-scheduler/pages__workflow_event_schedule__endHour____title": "Endzeit", "@sage/xtrem-scheduler/pages__workflow_event_schedule__endStamp____title": "Enddatum", "@sage/xtrem-scheduler/pages__workflow_event_schedule__mainSection____title": "Planen", "@sage/xtrem-scheduler/pages__workflow_event_schedule__startHour____infoMessage": "Format: hh:mm", "@sage/xtrem-scheduler/pages__workflow_event_schedule__startHour____title": "Startzeit", "@sage/xtrem-scheduler/pages__workflow_event_schedule__startStamp____title": "Startdatum", "@sage/xtrem-scheduler/pages__workflow_event_schedule__timeZone____title": "Zeitzone", "@sage/xtrem-scheduler/pages-confirm-cancel": "Abbrechen", "@sage/xtrem-scheduler/pages-confirm-purge": "Löschen", "@sage/xtrem-scheduler/permission__manage__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/permission__read__name": "<PERSON><PERSON>", "@sage/xtrem-scheduler/purge-sys-execute-history": "<PERSON><PERSON> sind dabei, den Ausführungsverlauf zu löschen.", "@sage/xtrem-scheduler/purge-sys-message-history-title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-scheduler/stickers__notification_sticker____title": "Benachrichtigungs-Center", "@sage/xtrem-scheduler/stickers__notification_sticker__block1____title": "Block 1", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____columns__title__lastMessage": "Letzte Meldung", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____columns__title__notificationId": "Details", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____columns__title__operationName": "Name", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____columns__title__status": "Status", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____columns__title__timeEnded": "<PERSON><PERSON>", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____columns__title__timeStarted": "Start", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____emptyStateText": "<PERSON><PERSON>", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____title": "Ausgeführte Jobs", "@sage/xtrem-scheduler/stickers__notification_sticker__isEnabled____title": "Benachrichtigungen aktivieren", "@sage/xtrem-scheduler/stickers__notification_sticker__markAsRead____title": "Als gelesen markieren", "@sage/xtrem-scheduler/stickers__notification_sticker__section1____title": "POC: Benachrichtigungs-Center", "@sage/xtrem-scheduler/sys-client-notification__number_of_records_purged": "{{numberDeleted}} Datensätze gelöscht.", "@sage/xtrem-scheduler/sys-job-definition/validate-parameters-reference-type": "Der Parameter {{parameterName}} ist vom Typ Referenz, es ist jedoch kein nodeName angegeben.", "@sage/xtrem-scheduler/sys-job-schedule/invalid-parameters-value": "Ungültiger Wert für den Parameter {{failedParsedParameterNames}}.", "@sage/xtrem-scheduler/sys-job-schedule/missing-parameters": "Der erforderliche Parameter {{missingParameterNames}} fehlt für den Job {{jobDefinitionName}}.", "@sage/xtrem-scheduler/sys-job-schedule/validate-parameters": "Der Zeitplan für den Job {{jobDefinitionName}} umfasst mehr Parameter als die Job-Definition.", "@sage/xtrem-scheduler/sys-notification-state__bulkStop_only_pending-running": "Sie können nur einen laufenden oder ausstehenden Job anhalten.", "@sage/xtrem-scheduler/sys-notification-state__number_of_records_purged": "{{numberDeleted}} Datensätze gelöscht"}