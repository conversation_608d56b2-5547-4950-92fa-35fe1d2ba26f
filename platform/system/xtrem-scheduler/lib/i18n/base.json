{"@sage/xtrem-scheduler/activity__sys_job_schedule__name": "Sys job schedule", "@sage/xtrem-scheduler/data_types__cron_schedule__name": "Cron schedule", "@sage/xtrem-scheduler/data_types__job_status_enum__name": "Job status enum", "@sage/xtrem-scheduler/data_types__parameter_type_enum__name": "Parameter type enum", "@sage/xtrem-scheduler/data_types__timezone_schedule__name": "Timezone schedule", "@sage/xtrem-scheduler/data-types/cron_schedule__invalid_schedule": "Schedule value is invalid: ({{value}}).", "@sage/xtrem-scheduler/data-types/timezone__invalid_timezone": "Timezone value is invalid: ({{value}}).", "@sage/xtrem-scheduler/enums__job_status__done": "Done", "@sage/xtrem-scheduler/enums__job_status__error": "Error", "@sage/xtrem-scheduler/enums__job_status__interrupted": "Interrupted", "@sage/xtrem-scheduler/enums__job_status__interruptRequested": "Interrupt requested", "@sage/xtrem-scheduler/enums__job_status__new": "New", "@sage/xtrem-scheduler/enums__job_status__notResponding": "Not responding", "@sage/xtrem-scheduler/enums__job_status__pending": "Pending", "@sage/xtrem-scheduler/enums__job_status__running": "Running", "@sage/xtrem-scheduler/enums__job_status__stopped": "Stopped", "@sage/xtrem-scheduler/enums__job_status__stopRequested": "Stop requested", "@sage/xtrem-scheduler/enums__parameter_type__boolean": "Boolean", "@sage/xtrem-scheduler/enums__parameter_type__date": "Date", "@sage/xtrem-scheduler/enums__parameter_type__dateRange": "Date range", "@sage/xtrem-scheduler/enums__parameter_type__datetime": "Datetime", "@sage/xtrem-scheduler/enums__parameter_type__datetimeRange": "Datetime range", "@sage/xtrem-scheduler/enums__parameter_type__decimal": "Decimal", "@sage/xtrem-scheduler/enums__parameter_type__decimalRange": "Decimal range", "@sage/xtrem-scheduler/enums__parameter_type__double": "Double", "@sage/xtrem-scheduler/enums__parameter_type__enum": "Enum", "@sage/xtrem-scheduler/enums__parameter_type__enumArray": "Enum array", "@sage/xtrem-scheduler/enums__parameter_type__float": "Float", "@sage/xtrem-scheduler/enums__parameter_type__integer": "Integer", "@sage/xtrem-scheduler/enums__parameter_type__integerArray": "Integer array", "@sage/xtrem-scheduler/enums__parameter_type__integerRange": "Integer range", "@sage/xtrem-scheduler/enums__parameter_type__reference": "Reference", "@sage/xtrem-scheduler/enums__parameter_type__referenceArray": "Reference array", "@sage/xtrem-scheduler/enums__parameter_type__string": "String", "@sage/xtrem-scheduler/enums__parameter_type__stringArray": "String array", "@sage/xtrem-scheduler/enums__parameter_type__time": "Time", "@sage/xtrem-scheduler/enums__parameter_type__uuid": "<PERSON><PERSON>", "@sage/xtrem-scheduler/job-manager_started-by-scheduler": "Started by scheduler: {{description}}", "@sage/xtrem-scheduler/node-extensions__sys_client_notification_extension__asyncMutation__purgeSysClientNotification": "Purge sys client notification", "@sage/xtrem-scheduler/node-extensions__sys_client_notification_extension__asyncMutation__purgeSysClientNotification__failed": "Purge sys client notification failed.", "@sage/xtrem-scheduler/node-extensions__sys_client_notification_extension__asyncMutation__purgeSysClientNotification__parameter__duration": "Duration", "@sage/xtrem-scheduler/node-extensions__sys_client_notification_extension__asyncMutation__purgeSysClientNotification__parameter__level": "Level", "@sage/xtrem-scheduler/node-extensions__sys_client_notification_extension__asyncMutation__purgeSysClientNotification__parameter__unit": "Unit", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__purgeHistory": "Purge history", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__purgeHistory__failed": "Purge history failed.", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__purgeHistory__parameter__duration": "Duration", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__purgeHistory__parameter__operationNames": "Operation names", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__purgeHistory__parameter__unit": "Unit", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__simulateAsyncMutation": "Simulate async mutation", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__simulateAsyncMutation__failed": "Simulate async mutation failed.", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__simulateAsyncMutation__parameter__delayInSec": "Delay in sec", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__simulateAsyncMutation__parameter__id": "Id", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__asyncMutation__simulateAsyncMutation__parameter__throwAt": "Throw at", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__bulkMutation__bulkStop": "Bulk stop", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__bulkMutation__bulkStop__failed": "Bulk stop failed.", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__bulkMutation__bulkStop__parameter__reason": "Reason", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__bulkMutation__markAsRead": "<PERSON> as read", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__bulkMutation__markAsRead__failed": "<PERSON> as read failed.", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__execute": "Execute", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__execute__failed": "Execute failed.", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__execute__parameter__jobExecution": "Job execution", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__notRespondingJob": "Not responding job", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__notRespondingJob__failed": "Not responding job failed.", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__notRespondingJob__parameter__duration": "Duration", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__notRespondingJob__parameter__unit": "Unit", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__stop": "Stop", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__stop__failed": "Stop failed.", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__stop__parameter__jobExecutionSysId": "Job execution sys id", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__mutation__stop__parameter__reason": "Reason", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__property__isScheduled": "Is scheduled", "@sage/xtrem-scheduler/node-extensions__sys_notification_state_extension__property__schedule": "Schedule", "@sage/xtrem-scheduler/nodes__sys_chore__asyncMutation__purgeContentAddressableTables": "Purge content addressable tables", "@sage/xtrem-scheduler/nodes__sys_chore__asyncMutation__purgeContentAddressableTables__failed": "Purge content addressable tables failed.", "@sage/xtrem-scheduler/nodes__sys_chore__node_name": "Sys chore", "@sage/xtrem-scheduler/nodes__sys_job_schedule__asyncMutation__asyncExport": "Export", "@sage/xtrem-scheduler/nodes__sys_job_schedule__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-scheduler/nodes__sys_job_schedule__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-scheduler/nodes__sys_job_schedule__bulkMutation__bulkDelete": "Bulk delete", "@sage/xtrem-scheduler/nodes__sys_job_schedule__mutation__purgeExecution": "Purge execution", "@sage/xtrem-scheduler/nodes__sys_job_schedule__mutation__purgeExecution__failed": "Purge execution failed.", "@sage/xtrem-scheduler/nodes__sys_job_schedule__mutation__purgeExecution__parameter__allStatus": "All status", "@sage/xtrem-scheduler/nodes__sys_job_schedule__mutation__purgeExecution__parameter__schedule": "Schedule", "@sage/xtrem-scheduler/nodes__sys_job_schedule__node_name": "Sys job schedule", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__cronSchedule": "Cron schedule", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__cronTranslated": "<PERSON><PERSON> translated", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__description": "Description", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__endStamp": "End stamp", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__executionLocale": "Execution locale", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__executions": "Executions", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__executionUser": "Execution user", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__id": "Id", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__isActive": "Is active", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__nextExecutionStamp": "Next execution stamp", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__operation": "Operation", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__parameterValues": "Parameter values", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__startStamp": "Start stamp", "@sage/xtrem-scheduler/nodes__sys_job_schedule__property__timeZone": "Time zone", "@sage/xtrem-scheduler/nodes__sys_job_schedule__query__cronInfo": "Cron info", "@sage/xtrem-scheduler/nodes__sys_job_schedule__query__cronInfo__failed": "Cron info failed.", "@sage/xtrem-scheduler/nodes__sys_job_schedule__query__timezones": "Timezones", "@sage/xtrem-scheduler/nodes__sys_job_schedule__query__timezones__failed": "Timezones failed.", "@sage/xtrem-scheduler/nodes__sys-job-execution_cron-or-start-date": "A start date or cron string is needed ", "@sage/xtrem-scheduler/nodes__sys-job-execution_no-next-execution": "This job will not be executed.", "@sage/xtrem-scheduler/nodes__sys-job-execution_operation-schedulable": "The operation isn't schedulable", "@sage/xtrem-scheduler/nodes__sys-job-execution_parsing-error": "Issue while parsing the next schedule.", "@sage/xtrem-scheduler/nodes__sys-job-schedule__execution_user_not_authorized": "You can only assign the execution user to yourself", "@sage/xtrem-scheduler/nodes__sys-job-schedule__no-cron-expression-no-startStamp": "You need a start datetime if cron expression is not define.", "@sage/xtrem-scheduler/nodes__sys-job-schedule__once": "Once", "@sage/xtrem-scheduler/nodes_sys_notification_state_cannot_stop_process_not_running": "This process cannot be stopped, as it is no longer running.", "@sage/xtrem-scheduler/package__name": "Sage xtrem scheduler", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension____navigationPanel__listItem__line1__title": "Schedule", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension____navigationPanel__listItem__line2__title": "Operation", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension____navigationPanel__listItem__line7__title": "Recurrent", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__execute____title": "Execute", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__operation____columns__title__factory__name": "Node", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__operation____columns__title__isSchedulable": "can schedule", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__operation____columns__title__name": "Operation", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__operation____title": "Operation", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__schedule____title": "Schedule", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__schedulePage____title": "Schedule", "@sage/xtrem-scheduler/page-extensions__sys_notification_state_extension__stop____title": "Stop", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__bulkActions__title": "Delete", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__line_5__title": "Next schedule", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__line1__title": "User", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__line2Right__title": "Cron schedule", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__line3__title": "Active", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__line3Right__title": "Operation", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__title__title": "ID", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__listItem__titleRight__title": "Description", "@sage/xtrem-scheduler/pages__sys_job_schedule____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-scheduler/pages__sys_job_schedule____objectTypePlural": "Jobs schedule", "@sage/xtrem-scheduler/pages__sys_job_schedule____objectTypeSingular": "Job schedule", "@sage/xtrem-scheduler/pages__sys_job_schedule____title": "Job schedule", "@sage/xtrem-scheduler/pages__sys_job_schedule__createCrud____title": "Create", "@sage/xtrem-scheduler/pages__sys_job_schedule__cronSchedule____title": "Cron expression", "@sage/xtrem-scheduler/pages__sys_job_schedule__cronTranslated____title": "Translated cron", "@sage/xtrem-scheduler/pages__sys_job_schedule__description____title": "Description", "@sage/xtrem-scheduler/pages__sys_job_schedule__endStamp____title": "End date", "@sage/xtrem-scheduler/pages__sys_job_schedule__execute____title": "Run now", "@sage/xtrem-scheduler/pages__sys_job_schedule__executeSection____title": "Execute history", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____columns__title___id": "Details", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____columns__title__status": "status", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____columns__title__timeEnded": "End", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____columns__title__timeStarted": "Start", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____dropdownActions__title": "Stop", "@sage/xtrem-scheduler/pages__sys_job_schedule__executions____title": "Executed jobs", "@sage/xtrem-scheduler/pages__sys_job_schedule__executionUser____columns__title__email": "Email", "@sage/xtrem-scheduler/pages__sys_job_schedule__executionUser____columns__title__firstName": "Name", "@sage/xtrem-scheduler/pages__sys_job_schedule__executionUser____columns__title__lastName": "Last name", "@sage/xtrem-scheduler/pages__sys_job_schedule__executionUser____title": "Execution user", "@sage/xtrem-scheduler/pages__sys_job_schedule__generalSection____title": "General", "@sage/xtrem-scheduler/pages__sys_job_schedule__id____title": "ID", "@sage/xtrem-scheduler/pages__sys_job_schedule__isActive____title": "Active", "@sage/xtrem-scheduler/pages__sys_job_schedule__operation____columns__title__factory__name": "Node", "@sage/xtrem-scheduler/pages__sys_job_schedule__operation____columns__title__isSchedulable": "can schedule", "@sage/xtrem-scheduler/pages__sys_job_schedule__operation____columns__title__title": "Operation", "@sage/xtrem-scheduler/pages__sys_job_schedule__operation____title": "Operation", "@sage/xtrem-scheduler/pages__sys_job_schedule__parameters____columns__title__name": "Name", "@sage/xtrem-scheduler/pages__sys_job_schedule__parameters____columns__title__value": "Value", "@sage/xtrem-scheduler/pages__sys_job_schedule__parameters____title": "Parameters", "@sage/xtrem-scheduler/pages__sys_job_schedule__purgeExecution____title": "Purge execution", "@sage/xtrem-scheduler/pages__sys_job_schedule__refreshExecutedJobs____title": "Refresh", "@sage/xtrem-scheduler/pages__sys_job_schedule__startStamp____title": "Start date", "@sage/xtrem-scheduler/pages__sys_job_schedule__timeZone____title": "Time zone", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard____title": "New batch task", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__atJobHour____placeholder": "Hour", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__atJobHour____title": "At time", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__atJobMinute____placeholder": "Minute", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__batch_task_scheduled": "Batch task scheduled: {{0}}", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__batch_task_started": "Batch task started", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__cronString____title": "Cron expression", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__daily": "Daily", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__days____title": "Days", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__defineSection____title": "Define", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__friday": "Friday", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__invalid_date": "Invalid date", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__invalid_date_with_argument": "Invalid date: {{0}}", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__job____columns__title__factory__name": "Source", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__job____columns__title__title": "Operation", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__job____title": "Job definition", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobDate____title": "Start date", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobHour____placeholder": "Hour", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobHour____title": "Start time", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobMinute____placeholder": "Minute", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobParameters____columns__title__name": "Parameter", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobParameters____columns__title__value": "Value", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobParameters____title": "Parameters", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__jobSchedule____title": "Schedule", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__monday": "Monday", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__monthly": "Monthly", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__needs_to_be_a_number": "Needs to be a number.", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__needs_to_be_either_true_or_false": "Needs to be one of the following: {{0}}, {{1}}.", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__needs_to_be_in_the_future": "Needs to be in the future", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__needs_to_be_in_the_future_with_argument": "Needs to be in the future: {{0}}", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__reccurence____title": "Recurrence", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__recurrence-not-set": "You need to set the frequency.", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__recurring": "Recurring", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__run_now": "Run now", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__run_once": "Run once", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__saturday": "Saturday", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__scheduleSection____title": "Schedule", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__settingsSection____title": "Settings", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__stop_date_must_be_later_start_date": "The stop date needs to be later than the start date.", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__stopJobDate____title": "Stop date", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__stopJobHour____placeholder": "Hour", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__stopJobHour____title": "Stop time", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__stopJobMinute____placeholder": "Minute", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__sunday": "Sunday", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__thursday": "Thursday", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__tuesday": "Tuesday", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__wednesday": "Wednesday", "@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__weekly": "Weekly", "@sage/xtrem-scheduler/pages__sys_notification_state__ask_by_user": "Asked by user", "@sage/xtrem-scheduler/pages__workflow_event_schedule____title": "Trigger configuration", "@sage/xtrem-scheduler/pages__workflow_event_schedule__cronSchedule____title": "Cron expression", "@sage/xtrem-scheduler/pages__workflow_event_schedule__endHour____infoMessage": "Format: hh:mm", "@sage/xtrem-scheduler/pages__workflow_event_schedule__endHour____title": "End time", "@sage/xtrem-scheduler/pages__workflow_event_schedule__endStamp____title": "End date", "@sage/xtrem-scheduler/pages__workflow_event_schedule__mainSection____title": "Schedule", "@sage/xtrem-scheduler/pages__workflow_event_schedule__startHour____infoMessage": "Format: hh:mm", "@sage/xtrem-scheduler/pages__workflow_event_schedule__startHour____title": "Start time", "@sage/xtrem-scheduler/pages__workflow_event_schedule__startStamp____title": "Start date", "@sage/xtrem-scheduler/pages__workflow_event_schedule__timeZone____title": "Time zone", "@sage/xtrem-scheduler/pages-confirm-cancel": "Cancel", "@sage/xtrem-scheduler/pages-confirm-purge": "Delete", "@sage/xtrem-scheduler/permission__manage__name": "Manage", "@sage/xtrem-scheduler/permission__read__name": "Read", "@sage/xtrem-scheduler/purge-sys-execute-history": "You are about to delete the execute history.", "@sage/xtrem-scheduler/purge-sys-message-history-title": "Delete history", "@sage/xtrem-scheduler/stickers__notification_sticker____title": "Notification center", "@sage/xtrem-scheduler/stickers__notification_sticker__block1____title": "Block 1", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____columns__title__lastMessage": "Last message", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____columns__title__notificationId": "Details", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____columns__title__operationName": "Name", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____columns__title__status": "status", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____columns__title__timeEnded": "End", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____columns__title__timeStarted": "Start", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____emptyStateText": "No notifications", "@sage/xtrem-scheduler/stickers__notification_sticker__executions____title": "Executed jobs", "@sage/xtrem-scheduler/stickers__notification_sticker__isEnabled____title": "Enable notifications", "@sage/xtrem-scheduler/stickers__notification_sticker__markAsRead____title": "<PERSON> as read", "@sage/xtrem-scheduler/stickers__notification_sticker__section1____title": "POC: Notification center", "@sage/xtrem-scheduler/sys-client-notification__number_of_records_purged": "{{numberDeleted}} records deleted.", "@sage/xtrem-scheduler/sys-job-schedule/missing-parameters": "Mandatory parameter {{missingParameterNames}} missing for Job {{jobDefinitionName}}.", "@sage/xtrem-scheduler/sys-job-schedule/validate-parameters": "The {{jobDefinitionName}} job schedule contains more parameters than the job definition.", "@sage/xtrem-scheduler/sys-notification-state__bulkStop_only_pending-running": "You can only stop a job that is running or pending.", "@sage/xtrem-scheduler/sys-notification-state__number_of_records_purged": "{{numberDeleted}} records deleted"}