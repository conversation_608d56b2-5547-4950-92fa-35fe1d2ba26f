import * as xtremCommunication from '@sage/xtrem-communication';
import { Activity } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { SysJobSchedule } from '../nodes/sys-job-schedule';

export const sysJobSchedule = new Activity({
    description: 'Job schedule',
    node: () => SysJobSchedule,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        read: [
            {
                operations: ['activeServiceOptions'],
                on: [() => xtremSystem.nodes.UserPreferences],
            },
            {
                operations: ['timezones', 'cronInfo'],
                on: [() => SysJobSchedule],
            },
        ],
        manage: [
            {
                operations: ['read', 'create', 'update', 'delete', 'purgeExecution', 'timezones', 'cronInfo'],
                on: [() => SysJobSchedule],
            },
            {
                operations: ['activeServiceOptions'],
                on: [() => xtremSystem.nodes.UserPreferences],
            },
            {
                operations: ['read', 'execute'],
                on: [() => xtremCommunication.nodes.SysNotificationState],
            },
        ],
    },
});
