import * as xtremCommunication from '@sage/xtrem-communication';
import { Context, decorators, NodeExtension } from '@sage/xtrem-core';
import { NotificationLevel } from '@sage/xtrem-shared';
import * as xtremSystem from '@sage/xtrem-system';

@decorators.nodeExtension<SysClientNotificationExtension>({
    extends: () => xtremSystem.nodes.SysClientNotification,
})
export class SysClientNotificationExtension extends NodeExtension<xtremSystem.nodes.SysClientNotification> {
    @decorators.asyncMutation<typeof SysClientNotificationExtension, 'purgeSysClientNotification'>({
        isPublished: true,
        isSchedulable: true,
        parameters: [
            { name: 'duration', type: 'integer', isMandatory: true },
            { name: 'unit', type: 'string', isMandatory: true },
            {
                name: 'level',
                type: 'enum',
                dataType: () => xtremSystem.enums.sysClientNotificationLevelDataType,
                isMandatory: true,
            },
        ],
        return: { type: 'integer' },
    })
    static async purgeSysClientNotification(
        context: Context,
        duration: number, // we expect a positive number and we add the minus sign by ourself
        unit: Intl.RelativeTimeFormatUnit,
        level: NotificationLevel,
    ): Promise<number> {
        const numberDeleted = await context.deleteMany(xtremSystem.nodes.SysClientNotification, {
            _createStamp: { _lte: xtremCommunication.functions.dateAddUnit(-duration, unit) },
            level,
        });
        await context.batch.logMessage(
            'result',
            context.localize(
                '@sage/xtrem-scheduler/sys-client-notification__number_of_records_purged',
                '{{numberDeleted}} records deleted.',
                { numberDeleted },
            ),
        );
        return numberDeleted;
    }
}

declare module '@sage/xtrem-system/lib/nodes/sys-client-notification' {
    export interface SysClientNotification extends SysClientNotificationExtension {}
}
