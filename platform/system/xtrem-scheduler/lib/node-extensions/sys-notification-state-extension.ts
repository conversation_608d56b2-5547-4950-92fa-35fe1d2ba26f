import * as xtremCommunication from '@sage/xtrem-communication';
import { BusinessRuleError, Context, datetime, decorators, NodeExtension, Reference } from '@sage/xtrem-core';
import * as xtremMetadata from '@sage/xtrem-metadata';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremScheduler from '../index';
import { JobManager } from '../services/job-manager';

@decorators.nodeExtension<SysNotificationStateExtension>({
    extends: () => xtremCommunication.nodes.SysNotificationState,
})
export class SysNotificationStateExtension extends NodeExtension<xtremCommunication.nodes.SysNotificationState> {
    @decorators.referenceProperty<SysNotificationStateExtension, 'schedule'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremScheduler.nodes.SysJobSchedule,
    })
    readonly schedule: Reference<xtremScheduler.nodes.SysJobSchedule | null>;

    @decorators.booleanProperty<SysNotificationStateExtension, 'isScheduled'>({
        isStored: false,
        isPublished: true,
        getValue() {
            return !!this.schedule;
        },
    })
    readonly isScheduled: Promise<boolean>;

    @decorators.mutation<typeof SysNotificationStateExtension, 'execute'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'jobExecution',
                type: 'object',
                properties: {
                    operation: { type: 'reference', node: () => xtremMetadata.nodes.MetaNodeOperation },
                    parameterValues: { type: 'string' },
                },
            },
        ],
        return: 'integer',
    })
    static async execute(
        context: Context,
        jobExecution: { operation: xtremMetadata.nodes.MetaNodeOperation; parameterValues: string },
    ): Promise<number> {
        const { _id } = await JobManager.executeAndCreateJob(context, {
            operation: jobExecution.operation,
            parameterValues: JSON.parse(jobExecution.parameterValues),
            topic: await jobExecution.operation.topic,
        });

        return _id;
    }

    @decorators.notificationListener<typeof SysNotificationStateExtension>({
        topic: 'SysNotificationState/updateStatus',
    })
    static async onTaskComplete(
        context: Context,
        payload: {
            status: xtremCommunication.enums.NotificationStatus;
            result: any;
            jobExecutionId?: number;
            errorMessage?: string;
        },
    ): Promise<void> {
        const replyId = context.getContextValue('replyId') || '';

        const timeEnded = xtremCommunication.functions.finalStatuses.includes(payload.status)
            ? datetime.now()
            : undefined;

        if (payload.jobExecutionId || replyId) {
            const update = await context.bulkUpdate(xtremCommunication.nodes.SysNotificationState, {
                set: { status: payload.status, message: payload.errorMessage, result: payload.result || {}, timeEnded },
                where: payload.jobExecutionId ? { _id: payload.jobExecutionId } : { notificationId: replyId },
            });
            if (!update) {
                context.logger.warn(`No SysNotificationState for ${payload.jobExecutionId || replyId}`);
            }
        }
    }

    @decorators.bulkMutation<typeof SysNotificationStateExtension, 'markAsRead'>({
        isPublished: true,
        serviceOptions: () => [xtremCommunication.serviceOptions.notificationCenter],
    })
    static async markAsRead(
        _context: Context,
        notification: xtremCommunication.nodes.SysNotificationState,
    ): Promise<boolean> {
        await notification.$.set({ isRead: true });
        await notification.$.save();
        return true;
    }

    @decorators.bulkMutation<typeof SysNotificationStateExtension, 'bulkStop'>({
        isPublished: true,

        parameters: [
            {
                name: 'reason',
                type: 'string',
            },
        ],
    })
    static async bulkStop(
        _context: Context,
        jobExecution: xtremCommunication.nodes.SysNotificationState,
        reason: string,
    ): Promise<void> {
        if (!['pending', 'running'].includes(await jobExecution.status)) {
            throw new BusinessRuleError(
                jobExecution.$.context.localize(
                    '@sage/xtrem-scheduler/sys-notification-state__bulkStop_only_pending-running',
                    'You can only stop a job that is running or pending.',
                ),
            );
        }
        await JobManager.stopOrInterruptJob(jobExecution, 'stopRequested', reason);
    }

    @decorators.mutation<typeof SysNotificationStateExtension, 'stop'>({
        isPublished: true,
        parameters: [
            { name: 'jobExecutionSysId', type: 'string' },
            { name: 'reason', type: 'string' },
        ],
        return: 'boolean',
    })
    static async stop(context: Context, jobExecutionSysId: string, reason: string): Promise<boolean> {
        const jobExecution = await context.read(
            xtremCommunication.nodes.SysNotificationState,
            { _id: jobExecutionSysId },
            { forUpdate: true },
        );

        if ((await jobExecution.status) !== 'running') {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-scheduler/nodes_sys_notification_state_cannot_stop_process_not_running',
                    'This process cannot be stopped, as it is no longer running.',
                ),
            );
        }

        await JobManager.stopOrInterruptJob(jobExecution, 'stopRequested', reason);

        return true;
    }

    /** Will pass all running job in notResponding   */
    @decorators.mutation<typeof SysNotificationStateExtension, 'notRespondingJob'>({
        isPublished: true,
        parameters: [
            { name: 'duration', type: 'integer' },
            { name: 'unit', type: 'string' },
        ],
        return: 'integer',
    })
    static notRespondingJob(context: Context, duration?: number, unit?: Intl.RelativeTimeFormatUnit): Promise<number> {
        return context.bulkUpdate(xtremCommunication.nodes.SysNotificationState, {
            set: { status: 'notResponding' },
            where: {
                timeEnded: { _lte: xtremCommunication.functions.dateAddUnit(-+(duration || 0), unit || 'seconds') },
                status: { _nin: xtremCommunication.functions.finalStatuses },
            },
        });
    }

    @decorators.asyncMutation<typeof SysNotificationStateExtension, 'purgeHistory'>({
        isPublished: true,
        isSchedulable: true,
        parameters: [
            { name: 'duration', type: 'integer', isMandatory: true },
            { name: 'unit', type: 'string', isMandatory: true },
            { name: 'operationNames', type: 'array', item: { type: 'string' } },
        ],
        return: { type: 'integer' },
    })
    static async purgeHistory(
        context: Context,
        duration: number,
        unit: Intl.RelativeTimeFormatUnit,
        operationNames?: string[],
    ): Promise<number> {
        const numberDeleted = await context.deleteMany(xtremCommunication.nodes.SysNotificationState, {
            timeEnded: { _lte: xtremCommunication.functions.dateAddUnit(-+duration, unit) },
            status: { _in: xtremCommunication.functions.finalStatuses },
            operationName: operationNames?.length ? { _in: operationNames } : undefined,
        });
        await context.batch.logMessage(
            'info',
            context.localize(
                '@sage/xtrem-scheduler/sys-notification-state__number_of_records_purged',
                '{{numberDeleted}} records deleted',
                { numberDeleted },
            ),
        );
        return numberDeleted;
    }

    /**
     *
     * @param context
     * @param id Name of the async simulation
     * @param delayInSec duration time
     * @param throwAt will throw at this second
     * @returns result as a string
     */
    @decorators.asyncMutation<typeof SysNotificationStateExtension, 'simulateAsyncMutation'>({
        isPublished: true,
        isSchedulable: true,
        parameters: [
            { name: 'id', type: 'string', isMandatory: true },
            { name: 'delayInSec', type: 'integer', isMandatory: true },
            { name: 'throwAt', type: 'integer' },
        ],
        return: { type: 'object', properties: { result: 'string' } },
        serviceOptions: () => [xtremSystem.serviceOptions.DevTools],
        async onError(context, envelope, error) {
            await context.batch.updateProgress({ phase: 'error', detail: error.message });
            context.logger.error(`Async mutation ${envelope.attributes.topic} failed with error: ${error.message}`);
        },
    })
    static async simulateAsyncMutation(
        context: Context,
        id: string,
        delayInSec: number,
        throwAt?: number,
    ): Promise<{ result: string }> {
        context.logger.info(`Simulated async mutation is starting for id ${id}`);
        await context.batch.logMessage('info', `Mutation start ${Date.now().toString()}`);
        const progress = await context.batch.getProgress();

        const { successCount } = progress;

        await context.batch.updateProgress({
            phase: 'start',
            totalCount: delayInSec,
            errorCount: 0,
            detail: 'started',
            successCount,
        });

        const remainingDelayInSec = delayInSec - successCount;
        const now = Date.now();

        let i = successCount;
        while (Date.now() - now <= remainingDelayInSec * 1000) {
            if (await context.batch.isStopRequested()) {
                await context.batch.logMessage('warning', ` Stop requested at ${Date.now().toString()}`);
                await context.batch.confirmStop();
                return { result: `Stop requested at ${i}` };
            }
            i += 1;
            if (throwAt && i === +throwAt) {
                throw new BusinessRuleError('Async mutation throw an error ');
            }

            // Updating progress every 10s
            if (i % 10 === 0) {
                await context.batch.updateProgress({ phase: 'processing', successCount: i.valueOf() });
            }

            context.logger.info(
                `${context.batch.trackingId} - ${i}/${delayInSec} Simulated async mutation is running for id ${id}`,
            );
            await new Promise(resolve => {
                setTimeout(resolve, 1000);
            });
        }
        await context.batch.logMessage('info', `Mutation end ${Date.now().toString()}`);
        context.logger.info(`Simulated async mutation has ended for id ${id}`);

        await context.batch.updateProgress({ phase: 'finish', successCount: i });

        return { result: `Waited for completion of ${id} for ${delayInSec} seconds` };
    }
}

declare module '@sage/xtrem-communication/lib/nodes/sys-notification-state' {
    export interface SysNotificationState extends SysNotificationStateExtension {}
}
