import * as xtremCommunication from '@sage/xtrem-communication';
import { ActivityExtension } from '@sage/xtrem-core';

export const sysNotificationStateExtension = new ActivityExtension({
    extends: xtremCommunication.activities.sysNotificationState,
    __filename,
    permissions: [],
    operationGrants: {
        update: [
            {
                operations: [
                    'markAsRead',
                    'bulkStop',
                    'stop',
                    'notRespondingJob',
                    'purgeHistory',
                    'simulateAsyncMutation',
                ],
                on: [() => xtremCommunication.nodes.SysNotificationState],
            },
        ],
    },
});
