import { edgesSelector, WithoutEdges, withoutEdges } from '@sage/xtrem-client';
import { NotificationStatus, SysNotificationState } from '@sage/xtrem-communication-api';
import { datetime } from '@sage/xtrem-date-time';
import { GraphApi } from '@sage/xtrem-scheduler-api';
import { NotificationProgress } from '@sage/xtrem-shared';
import * as ui from '@sage/xtrem-ui';
import { getNotificationType } from '../client-functions/notification';

const sleepMillis = (millis: number): Promise<void> =>
    new Promise(resolve => {
        setTimeout(resolve, millis);
    });

interface NotificationStateWithLastMessage extends SysNotificationState {
    lastMessage: string;
}

@ui.decorators.sticker<NotificationSticker>({
    title: 'Notification center',
    icon: 'bullet_list_dotted',
    access: { node: '@sage/xtrem-communication/SysNotificationState', bind: 'isRead' },
    businessActions() {
        return [this.markAsRead];
    },
    isActive() {
        return true;
    },
    async onOpen() {
        this.executions.value = await this.getSysNotificationState();
    },
    async onLoad() {
        while (this.isLoopExecuted) {
            if (this.isEnabled.value) await this.loop();
            await sleepMillis(6000);
        }
        this.isEnabled.value = false;
    },
})
export class NotificationSticker extends ui.Sticker<GraphApi> {
    notificationStateEtag: { _id: string; _etag: string }[] = [];

    isLoopExecuted = true;

    @ui.decorators.section<NotificationSticker>({
        title: 'POC: Notification center',
    })
    section1: ui.containers.Section;

    @ui.decorators.block<NotificationSticker>({
        title: 'Block 1',
        isTitleHidden: true,
        parent() {
            return this.section1;
        },
    })
    block1: ui.containers.Block;

    @ui.decorators.tableField<NotificationSticker, NotificationStateWithLastMessage>({
        title: 'Executed jobs',
        isReadOnly: true,
        canFilter: false,
        canSelect: false,
        displayMode: ui.fields.TableDisplayMode.compact,
        emptyStateText: 'No notifications',
        parent() {
            return this.block1;
        },
        columns: [
            ui.nestedFields.technical({ bind: '_id' }),
            ui.nestedFields.technical({ bind: 'progress' }),
            ui.nestedFields.text({ bind: 'operationName', title: 'Name' }),
            ui.nestedFields.text({ bind: 'status', title: 'status' }),
            ui.nestedFields.text({ bind: 'lastMessage', title: 'Last message' }),
            ui.nestedFields.label({
                bind: 'timeStarted',
                title: 'Start',
                isHiddenDesktop: true,
                isHiddenMobile: true,
                map(_id, row) {
                    return datetime.parse(row.timeStarted).format(this.$.locale || undefined, 'YYYY-MM-DD HH:mm:ss');
                },
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
            }),
            ui.nestedFields.label({
                bind: 'timeEnded',
                title: 'End',
                isHiddenDesktop: true,
                isHiddenMobile: true,
                map(_id, row) {
                    return datetime.parse(row.timeStarted).format(this.$.locale || undefined, 'YYYY-MM-DD HH:mm:ss');
                },
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
            }),
            ui.nestedFields.link({
                bind: 'notificationId',
                title: 'Details',
                onClick(id, row) {
                    this.$.router.goTo('@sage/xtrem-communication/SysNotificationState', {
                        _id: row._id,
                    });
                },
            }),
        ],
    })
    executions: ui.fields.Table<NotificationStateWithLastMessage>;

    @ui.decorators.switchField<NotificationSticker>({
        title: 'Enable notifications',
        parent() {
            return this.block1;
        },
        onChange() {
            this.isLoopExecuted = this.isEnabled.value || false;
        },
    })
    isEnabled: ui.fields.Switch;

    @ui.decorators.pageAction<NotificationSticker>({
        title: 'Mark as read',
        async onClick() {
            await this.markAsReadMethod();
        },
    })
    markAsRead: ui.PageAction;

    async markAsReadMethod(): Promise<void> {
        if (this.notificationStateEtag.length) {
            const sysIdArray = this.notificationStateEtag.map(state => state._id);
            await this.$.graph
                .node('@sage/xtrem-communication/SysNotificationState')
                .asyncOperations.markAsRead.runToCompletion(true, {
                    filter: JSON.stringify({ _id: { _in: sysIdArray } }),
                })
                .execute();
            await this.loop();
            this.notificationStateEtag = [];
            this.markAsRead.isDisabled = true;
        }
    }

    async loop(): Promise<void> {
        const notifications = await this.getSysNotificationState();

        notifications.forEach(notification => {
            const progress: NotificationProgress = JSON.parse(notification.progress);
            const curentNotif = this.notificationStateEtag.find(({ _id }) => _id === notification._id);
            if ((curentNotif && curentNotif._etag !== notification._etag) || !curentNotif) {
                const message = `##### ${notification.operationName}
                ${notification.status}: ${progress.successCount} success ${progress.errorCount} error /${progress.totalCount} total
                ${notification.lastMessage}`;
                this.$.showToast(`${message}`, {
                    language: 'markdown',
                    timeout: 6000,
                    type: getNotificationType(notification.status),
                });
                if (!curentNotif) {
                    this.notificationStateEtag.push({ _id: notification._id, _etag: notification._etag || '' });
                } else {
                    curentNotif._etag = notification._etag || '';
                }
            }
        });
    }

    async getSysNotificationState(): Promise<
        WithoutEdges<{
            _id: string;
            operationName: string;
            status: NotificationStatus;
            _etag?: string;
            message: string;
            lastMessage: string;
            notificationId: string;
            progress: string;
        }>[]
    > {
        return withoutEdges(
            await this.$.graph
                .node('@sage/xtrem-communication/SysNotificationState')
                .query(
                    edgesSelector(
                        {
                            _id: true,
                            _etag: true,
                            operationName: true,
                            message: true,
                            status: true,
                            timeStarted: true,
                            timeEnded: true,
                            notificationId: true,
                            progress: true,
                            logs: {
                                query: edgesSelector(
                                    {
                                        level: true,
                                        message: true,
                                    },
                                    { orderBy: { timestamp: -1 }, first: 1 },
                                ),
                            },
                        },
                        { filter: { user: `#${this.$.username}`, isRead: false } },
                    ),
                )
                .execute(),
        ).map(notificationState => {
            return {
                ...notificationState,
                lastMessage: notificationState.logs.query[0].message,
            };
        });
    }
}
