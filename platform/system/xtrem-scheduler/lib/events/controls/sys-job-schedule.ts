import { ValidationContext } from '@sage/xtrem-core';
import * as xtremScheduler from '../..';

/** Schedule for Job ${await schedule.name} has more parameters than the job definition */
export async function validateParameters(
    schedule: xtremScheduler.nodes.SysJobSchedule,
    cx: ValidationContext,
): Promise<void> {
    const scheduleParameterValues = await schedule.parameterValues;
    const operation = await schedule.operation;
    const definitionParamters = await operation.parameters;

    const hasMoreParameters =
        Object.entries(scheduleParameterValues).length > Object.entries(definitionParamters).length;

    await cx.error
        .withMessage(
            '@sage/xtrem-scheduler/sys-job-schedule/validate-parameters',
            'The {{jobDefinitionName}} job schedule contains more parameters than the job definition.',
            async () => {
                return { jobDefinitionName: await (await schedule.operation).name };
            },
        )
        .if(hasMoreParameters)
        .is.true();
}

/** Mandatory parameter ${await defParam.name} missing for Job sysJobDefinitionname */
export async function missingParameters(
    schedule: xtremScheduler.nodes.SysJobSchedule,
    cx: ValidationContext,
): Promise<void> {
    const scheduleParameterValues = await schedule.parameterValues;
    const definitionParameters = await (await schedule.operation).parameters;
    const jobDefinitionName = await (await schedule.operation).name;

    const missingParameterNameArray = Object.values(definitionParameters)
        .filter(
            definitionParameterValue =>
                definitionParameterValue.isMandatory && !scheduleParameterValues[definitionParameterValue.name],
        )
        .map(parameter => parameter.name);

    await cx.error
        .withMessage(
            '@sage/xtrem-scheduler/sys-job-schedule/missing-parameters',
            'Mandatory parameter {{missingParameterNames}} missing for Job {{jobDefinitionName}}.',
            () => {
                return {
                    jobDefinitionName,
                    missingParameterNames: missingParameterNameArray.join(' '),
                };
            },
        )
        .if(!!missingParameterNameArray.length)
        .is.true();
}

export async function cronOrStartDate(
    schedule: xtremScheduler.nodes.SysJobSchedule,
    cx: ValidationContext,
): Promise<void> {
    await cx.error
        .withMessage(
            '@sage/xtrem-scheduler/nodes__sys-job-execution_cron-or-start-date',
            'A start date or cron string is needed ',
        )
        .if((await schedule.cronSchedule) === '' && !(await schedule.startStamp))
        .is.true();
}

export async function haveNextExecution(
    schedule: xtremScheduler.nodes.SysJobSchedule,
    cx: ValidationContext,
): Promise<void> {
    const startStamp = await schedule.startStamp;
    const endStamp = await schedule.endStamp;

    switch (
        xtremScheduler.sharedFunctions.nextExecution({
            cronString: await schedule.cronSchedule,
            tz: await schedule.timeZone,
            ...(startStamp && { startDate: startStamp }),
            ...(endStamp && { endDate: endStamp }),
        })
    ) {
        case xtremScheduler.sharedFunctions.NextExecutionError.cronStringStartDateEmpty:
            cx.error.addLocalized(
                '@sage/xtrem-scheduler/nodes__sys-job-execution_cron-or-start-date',
                'A start date or cron string is needed ',
            );
            break;
        case xtremScheduler.sharedFunctions.NextExecutionError.noNextExecution:
            cx.error.addLocalized(
                '@sage/xtrem-scheduler/nodes__sys-job-execution_no-next-execution',
                'This job will not be executed.',
            );
            break;
        case xtremScheduler.sharedFunctions.NextExecutionError.parse:
            cx.error.addLocalized(
                '@sage/xtrem-scheduler/nodes__sys-job-execution_parsing-error',
                'Issue while parsing the next schedule.',
            );
            break;
        default:
            break;
    }
}
