import { ValidationContext } from '@sage/xtrem-core';
import * as xtremMetadata from '@sage/xtrem-metadata';

export async function controlOperationSchedulable(
    cx: ValidationContext,
    operation: xtremMetadata.nodes.MetaNodeOperation,
): Promise<void> {
    await cx.error
        .withMessage(
            '@sage/xtrem-scheduler/nodes__sys-job-execution_operation-schedulable',
            "The operation isn't schedulable",
        )
        .if(await operation.isSchedulable)
        .is.false();
}
