import { date } from '@sage/xtrem-date-time';
import { GraphApi } from '@sage/xtrem-metadata-api';
import * as ui from '@sage/xtrem-ui';
import { WorkflowDefinition as WorkflowDefinitionApi } from '@sage/xtrem-workflow-api';
import * as cronstrue from 'cronstrue';
import { getTimeZones } from '../client-functions/common';

@ui.decorators.page<WorkflowEventSchedule, WorkflowDefinitionApi>({
    title: 'Trigger configuration',
    mode: 'tabs',
    businessActions() {
        return [this.$standardDialogConfirmationAction];
    },
    async onLoad() {
        this.timeZone.options = await getTimeZones(this.$.graph);
        this.updateHumanFriendlySchedule();
    },
})
export class WorkflowEventSchedule extends ui.Page<GraphApi> {
    private updateHumanFriendlySchedule(): void {
        const formattedValue = this.cronSchedule.value
            ? cronstrue.toString(this.cronSchedule.value, { locale: this.$.locale?.substring(0, 2) || 'en' })
            : '';
        this.cronSchedule.helperText = formattedValue;
        this.subtitle.value = formattedValue;
    }

    @ui.decorators.section<WorkflowEventSchedule>({
        isTitleHidden: true,
        title: 'Schedule',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<WorkflowEventSchedule>({
        parent() {
            return this.mainSection;
        },
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.textField<WorkflowEventSchedule>({})
    subtitle: ui.fields.Text;

    @ui.decorators.textField<WorkflowEventSchedule>({
        parent() {
            return this.mainBlock;
        },
        title: 'Cron expression',
        isFullWidth: true,
        onChange() {
            this.updateHumanFriendlySchedule();
        },
    })
    cronSchedule: ui.fields.Text;

    @ui.decorators.dateField<WorkflowEventSchedule>({
        parent() {
            return this.mainBlock;
        },
        title: 'Start date',
        minDate: date.today(),
    })
    startStamp: ui.fields.Date;

    @ui.decorators.textField<WorkflowEventSchedule>({
        parent() {
            return this.mainBlock;
        },
        title: 'Start time',
        width: 'small',
        isTransient: true,
        validation: /^((0[0-9]|1[0-9]|2[0-3]):[0-5][0-9])|$/,
        infoMessage: 'Format: hh:mm',
    })
    startHour: ui.fields.Text;

    @ui.decorators.dateField<WorkflowEventSchedule>({
        parent() {
            return this.mainBlock;
        },
        title: 'End date',
        minDate: date.today(),
        isHidden() {
            return this.cronSchedule.value === '';
        },
    })
    endStamp: ui.fields.Date;

    @ui.decorators.textField<WorkflowEventSchedule>({
        parent() {
            return this.mainBlock;
        },
        title: 'End time',
        width: 'small',
        isTransient: true,
        validation: /^((0[0-9]|1[0-9]|2[0-3]):[0-5][0-9])|$/,
        infoMessage: 'Format: hh:mm',
        isHidden() {
            return this.cronSchedule.value === '';
        },
    })
    endHour: ui.fields.Text;

    @ui.decorators.selectField<WorkflowEventSchedule>({
        parent() {
            return this.mainBlock;
        },
        title: 'Time zone',
        isFullWidth: true,
    })
    timeZone: ui.fields.Select;
}
