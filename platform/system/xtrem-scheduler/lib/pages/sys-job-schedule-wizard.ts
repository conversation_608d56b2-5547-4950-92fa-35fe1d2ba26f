import * as xtremDateTime from '@sage/xtrem-date-time';
import { MetaNodeOperation, MetaOperationAction } from '@sage/xtrem-metadata-api';
import { GraphApi, SysJobSchedule } from '@sage/xtrem-scheduler-api';
import * as ui from '@sage/xtrem-ui';
import { Parameter } from '../client-functions/interfaces/parameters';
import { ScheduleWizard } from '../client-functions/interfaces/schedule';
import {
    convertAnyValueToParameterString,
    formatDateTime,
    getDateTime,
    getParametersFromOperationAndValues,
    transformParametersNameValue,
} from '../client-functions/job-execution';
import {
    dateTimeValidation,
    dayOfTheWeek,
    generateCronString,
    getNextExcution,
    scheduleValidation,
    scheduleValidationError,
    startEndDateValidation,
} from '../client-functions/schedule-wizard';

const now = ui.localize('@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__run_now', 'Run now');
const once = ui.localize('@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__run_once', 'Run once');
const recurring = ui.localize('@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__recurring', 'Recurring');
const daily = ui.localize('@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__daily', 'Daily');
const weekly = ui.localize('@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__weekly', 'Weekly');
const monthly = ui.localize('@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__monthly', 'Monthly');

@ui.decorators.page<SysJobScheduleWizard>({
    title: 'New batch task',
    mode: 'wizard',
    isTransient: true,
    priority: 100,
    businessActions() {
        return [this.$standardCancelAction];
    },
    async onLoad() {
        this.parameters.jobSchedule = this.$.queryParameters.jobSchedule
            ? JSON.parse(this.$.queryParameters.jobSchedule.toString())
            : [now, once, recurring];

        this.parameters.operationKey = this.$.queryParameters.operationKey?.toString();

        this.parameters.filter = this.$.queryParameters.filter?.toString();

        this.parameters.additionalParameters = this.$.queryParameters.additionalParameters
            ? JSON.parse(this.$.queryParameters.additionalParameters.toString())
            : {};

        if (this.parameters.operationKey) {
            const operation = await this.job.fetchSuggestions('');
            if (operation.length) {
                this.job.value = operation[0];
                this.onJobChange();
            }
        }

        this.scheduleSection.isHidden = !!this.$.queryParameters.isScheduleHidden;
        this.defineSection.isHidden = !!this.$.queryParameters.isOperationHidden;
        this.settingsSection.isHidden = !!this.$.queryParameters.isParametersHidden;

        this.jobSchedule.options = this.parameters.jobSchedule;
        this.jobDate.value = xtremDateTime.DateValue.today().toString();
        this.jobHour.value = xtremDateTime.Time.now().hour;
        this.jobMinute.value = xtremDateTime.Time.now().minute;

        if (this.$.isServiceOptionEnabled('devTools')) {
            this.cronString.isHidden = false;
        }
    },
})
export class SysJobScheduleWizard extends ui.Page<GraphApi, SysJobSchedule> {
    parameters: ScheduleWizard = { jobSchedule: [now, once, recurring] };

    /**  Schedule step */
    @ui.decorators.section<SysJobScheduleWizard>({
        title: 'Schedule',
        isTitleHidden: true,
        validation() {
            return scheduleValidation(this);
        },
    })
    scheduleSection: ui.containers.Section<SysJobScheduleWizard>;

    /** Now Once Recurring  */
    @ui.decorators.block<SysJobScheduleWizard>({
        parent() {
            return this.scheduleSection;
        },
        isTitleHidden: true,
    })
    scheduleBlock: ui.containers.Block<SysJobScheduleWizard>;

    @ui.decorators.radioField<SysJobScheduleWizard>({
        title: 'Schedule',
        parent() {
            return this.scheduleBlock;
        },
        isMandatory: true,
        options: [now, once, recurring],
        isFullWidth: true,
    })
    jobSchedule: ui.fields.Radio<string, SysJobScheduleWizard>;

    @ui.decorators.textField<SysJobScheduleWizard>({
        title: 'Cron expression',
        parent() {
            return this.scheduleBlock;
        },
        isHidden: true,
        isFullWidth: true,
        onChange() {
            const nextExec = getNextExcution(this);
            this.cronString.helperText = scheduleValidationError(nextExec) || nextExec.toString();
        },
    })
    cronString: ui.fields.Text;

    @ui.decorators.radioField<SysJobScheduleWizard>({
        title: 'Recurrence',
        parent() {
            return this.scheduleBlock;
        },
        isMandatory: true,
        options: [daily, weekly],
        size: 'small',
        isHidden() {
            return [now, once, null].includes(this.jobSchedule.value);
        },
        isFullWidth: true,
    })
    reccurence: ui.fields.Radio<string, SysJobScheduleWizard>;

    @ui.decorators.multiDropdownField<SysJobScheduleWizard>({
        title: 'Days',
        parent() {
            return this.scheduleBlock;
        },
        isMandatory() {
            return this.reccurence.value === weekly;
        },
        options: dayOfTheWeek,
        isFullWidth: true,
        isHidden() {
            return [now, once, null].includes(this.jobSchedule.value) || [daily, null].includes(this.reccurence.value);
        },
        onChange() {
            this.cronString.value = generateCronString(this);
        },
    })
    days: ui.fields.MultiDropdown<SysJobScheduleWizard>;

    @ui.decorators.numericField<SysJobScheduleWizard>({
        title: 'At time',
        icon: 'clock',
        size: 'small',
        placeholder: 'Hour',
        scale: 0,
        parent() {
            return this.scheduleBlock;
        },
        isHidden() {
            return [now, once, null].includes(this.jobSchedule.value) || this.reccurence.value === null;
        },
        isMandatory() {
            return [daily, weekly, monthly].includes(this.reccurence.value || '');
        },
        min: 0,
        max: 23,
        onChange() {
            this.cronString.value = generateCronString(this);
        },
    })
    atJobHour: ui.fields.Numeric<SysJobScheduleWizard>;

    @ui.decorators.numericField<SysJobScheduleWizard>({
        icon: 'clock',
        size: 'small',
        isTitleHidden: true,
        placeholder: 'Minute',
        scale: 0,
        parent() {
            return this.scheduleBlock;
        },
        isHidden() {
            return [now, once, null].includes(this.jobSchedule.value) || this.reccurence.value === null;
        },
        isMandatory() {
            return [daily, weekly, monthly].includes(this.reccurence.value || '');
        },
        min: 0,
        max: 59,
        onChange() {
            this.cronString.value = generateCronString(this);
        },
    })
    atJobMinute: ui.fields.Numeric<SysJobScheduleWizard>;

    @ui.decorators.dateField<SysJobScheduleWizard>({
        title: 'Start date',
        size: 'small',
        minDate: xtremDateTime.date.today(),
        parent() {
            return this.scheduleBlock;
        },
        isHidden() {
            return [now, null].includes(this.jobSchedule.value) && this.reccurence.value === null;
        },
        isMandatory() {
            return this.jobSchedule.value === once;
        },
        validation(date: string) {
            return this.jobSchedule.value === once
                ? dateTimeValidation({ date, hour: this.jobHour.value, minute: this.jobMinute.value })
                : undefined;
        },
        onChange() {
            if (this.stopJobDate.isDirty) {
                this.stopJobDate.validate();
            }
            const nextExec = getNextExcution(this);
            this.cronString.helperText = scheduleValidationError(nextExec) || nextExec.toString();
        },
    })
    jobDate: ui.fields.Date<SysJobScheduleWizard>;

    @ui.decorators.separatorField<SysJobScheduleWizard>({
        parent() {
            return this.scheduleBlock;
        },
        isHidden() {
            //  We don't have access to this.jobSchedule.value
            // return [now, once, null].includes(this.jobSchedule.value);
            return false;
        },
    })
    separatorStart: ui.fields.Separator;

    @ui.decorators.numericField<SysJobScheduleWizard>({
        title: 'Start time',
        icon: 'clock',
        size: 'small',
        placeholder: 'Hour',
        scale: 0,
        parent() {
            return this.scheduleBlock;
        },
        isHidden() {
            return [now, null].includes(this.jobSchedule.value) && this.reccurence.value === null;
        },
        onChange() {
            if (this.jobDate.isDirty) {
                this.jobDate.validate();
            }
            if (this.stopJobDate.isDirty) {
                this.stopJobDate.validate();
            }
            const nextExec = getNextExcution(this);
            this.cronString.helperText = scheduleValidationError(nextExec) || nextExec.toString();
        },
        isMandatory() {
            return this.jobSchedule.value === once;
        },
        min: 0,
        max: 23,
    })
    jobHour: ui.fields.Numeric<SysJobScheduleWizard>;

    @ui.decorators.numericField<SysJobScheduleWizard>({
        icon: 'clock',
        size: 'small',
        isTitleHidden: true,
        placeholder: 'Minute',
        scale: 0,
        parent() {
            return this.scheduleBlock;
        },
        isHidden() {
            return [now, null].includes(this.jobSchedule.value) && this.reccurence.value === null;
        },
        onChange() {
            if (this.jobDate.isDirty) {
                this.jobDate.validate();
            }
            if (this.stopJobDate.isDirty) {
                this.stopJobDate.validate();
            }
            const nextExec = getNextExcution(this);
            this.cronString.helperText = scheduleValidationError(nextExec) || nextExec.toString();
        },
        isMandatory() {
            return this.jobSchedule.value === once;
        },
        min: 0,
        max: 59,
    })
    jobMinute: ui.fields.Numeric<SysJobScheduleWizard>;

    @ui.decorators.dateField<SysJobScheduleWizard>({
        title: 'Stop date',
        size: 'small',
        minDate: xtremDateTime.date.today(),
        parent() {
            return this.scheduleBlock;
        },
        isHidden() {
            return [now, once, null].includes(this.jobSchedule.value) || this.reccurence.value === null;
        },
        validation(date: string) {
            let validation: string | undefined;
            if (date || this.stopJobHour.value || this.stopJobMinute.value) {
                validation = dateTimeValidation({
                    date,
                    hour: this.stopJobHour.value,
                    minute: this.stopJobMinute.value,
                });
                if (!validation && this.jobDate.value) {
                    validation = startEndDateValidation(
                        { date: this.jobDate.value, hour: this.jobHour.value, minute: this.jobMinute.value },
                        { date, hour: this.stopJobHour.value, minute: this.stopJobMinute.value },
                    );
                }
            }
            return validation;
        },
        onChange() {
            const nextExec = getNextExcution(this);
            this.cronString.helperText = scheduleValidationError(nextExec) || nextExec.toString();
        },
    })
    stopJobDate: ui.fields.Date<SysJobScheduleWizard>;

    @ui.decorators.separatorField<SysJobScheduleWizard>({
        parent() {
            return this.scheduleBlock;
        },
        isHidden() {
            //  We don't have access to this.jobSchedule.value
            // return [now, once, null].includes(this.jobSchedule.value);
            return false;
        },
    })
    separatorStop: ui.fields.Separator;

    @ui.decorators.numericField<SysJobScheduleWizard>({
        title: 'Stop time',
        icon: 'clock',
        size: 'small',
        placeholder: 'Hour',
        scale: 0,
        parent() {
            return this.scheduleBlock;
        },
        isHidden() {
            return [now, once, null].includes(this.jobSchedule.value) || this.reccurence.value === null;
        },
        onChange() {
            if (this.stopJobDate.isDirty) {
                this.stopJobDate.validate();
            }
            const nextExec = getNextExcution(this);
            this.cronString.helperText = scheduleValidationError(nextExec) || nextExec.toString();
        },
        min: 0,
        max: 23,
    })
    stopJobHour: ui.fields.Numeric<SysJobScheduleWizard>;

    @ui.decorators.numericField<SysJobScheduleWizard>({
        icon: 'clock',
        size: 'small',
        isTitleHidden: true,
        placeholder: 'Minute',
        scale: 0,
        parent() {
            return this.scheduleBlock;
        },
        isHidden() {
            return [now, once, null].includes(this.jobSchedule.value) || this.reccurence.value === null;
        },
        onChange() {
            if (this.stopJobDate.isDirty) {
                this.stopJobDate.validate();
            }
            const nextExec = getNextExcution(this);
            this.cronString.helperText = scheduleValidationError(nextExec) || nextExec.toString();
        },
        min: 0,
        max: 59,
    })
    stopJobMinute: ui.fields.Numeric<SysJobScheduleWizard>;

    /** Job select section */
    @ui.decorators.section<SysJobScheduleWizard>({
        title: 'Define',
        isTitleHidden: true,
    })
    defineSection: ui.containers.Section<SysJobScheduleWizard>;

    /** Job select block */
    @ui.decorators.block<SysJobScheduleWizard>({
        parent() {
            return this.defineSection;
        },
        isTitleHidden: true,
    })
    defineBlock: ui.containers.Block<SysJobScheduleWizard>;

    @ui.decorators.referenceField<SysJobScheduleWizard, MetaNodeOperation>({
        bind: 'operation',
        node: '@sage/xtrem-metadata/MetaNodeOperation',
        title: 'Job definition',
        parent() {
            return this.defineBlock;
        },
        isMandatory: true,
        valueField: 'title',
        columns: [
            ui.nestedFields.text({ bind: { factory: { name: true } }, title: 'Source' }),
            ui.nestedFields.text({ bind: 'title', title: 'Operation' }),
            ui.nestedFields.technical({ bind: 'parameters' }),
            ui.nestedFields.technical({ bind: 'serviceOptionNames' }),
        ],
        filter() {
            if (this.parameters.operationKey) {
                this.job.isReadOnly = true;
                const [nodeName, name, action] = this.parameters.operationKey.split('|');
                return { factory: { name: nodeName }, name, action: action as MetaOperationAction };
            }
            return {
                // This must be replaced by _containedBy
                // XT-47470 enhancement request
                serviceOptionNames: {
                    _regex: `^\\[${this.$.getEnabledServiceOptions()
                        .map(serviceOption => `("${serviceOption}")?`)
                        .join(',?')}\\]$`,
                },
                kind: { _in: ['asyncMutation', 'bulkMutation'] },
                name: { _nin: ['bulkDelete', 'asyncExport'] },
                action: 'start',
                // only get schedulable jobs if job schedule is set to once or recurring
                ...((this.jobSchedule.value === once || this.jobSchedule.value === recurring) && {
                    isSchedulable: true,
                }),
            };
        },
        async onChange() {
            this.onJobChange();
        },
    })
    job: ui.fields.Reference<MetaNodeOperation, SysJobScheduleWizard>;

    onJobChange(): void {
        const parameters = getParametersFromOperationAndValues(
            this.job.value,
            this.jobParameterValues.value || undefined,
        );
        this.jobParameters.value = parameters.map(p => ({ ...p, value: 'random' }));
        parameters.forEach(p => {
            // the parameters in the table does not support the array type, so need to convert it to a comma separated string
            const value =
                p.name === 'filter'
                    ? this.parameters.filter
                    : convertAnyValueToParameterString(p, this.parameters.additionalParameters[p.name]);
            this.jobParameters.addOrUpdateRecordValue({ ...p, value });
        });
        // hide settings section if job has no parameters
        this.settingsSection.isHidden = this.jobParameters.value.length === 0;
    }

    /**  Settings step */
    @ui.decorators.section<SysJobScheduleWizard>({
        title: 'Settings',
        isTitleHidden: true,
        validation() {
            try {
                if (this.jobSchedule.value === once && this.jobDate.value) {
                    const dateTime = getDateTime({
                        date: this.jobDate.value,
                        hour: this.jobHour.value,
                        minute: this.jobMinute.value,
                    });
                    if (dateTime.compare(xtremDateTime.datetime.now()) < 0) {
                        return ui.localize(
                            '@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__needs_to_be_in_the_future_with_argument',
                            'Needs to be in the future: {{0}}',
                            [formatDateTime(dateTime.toJsDate())],
                        );
                    }
                }
            } catch (err) {
                return ui.localize(
                    '@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__invalid_date_with_argument',
                    'Invalid date: {{0}}',
                    [this.jobDate.value],
                );
            }
            return undefined;
        },
    })
    settingsSection: ui.containers.Section<SysJobScheduleWizard>;

    @ui.decorators.block<SysJobScheduleWizard>({
        parent() {
            return this.settingsSection;
        },
        isTitleHidden: true,
    })
    settingsBlock: ui.containers.Block<SysJobScheduleWizard>;

    @ui.decorators.tableField<SysJobScheduleWizard, Parameter>({
        bind: 'parameters',
        title: 'Parameters',
        mainField: 'name',
        canSelect: false,
        canResizeColumns: true,
        canUserHideColumns: false,
        parent() {
            return this.settingsBlock;
        },
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Parameter', isReadOnly: true }),
            ui.nestedFields.text({
                bind: 'value',
                title: 'Value',
                isMandatory(rowValue: Parameter) {
                    return Boolean(rowValue?.isMandatory);
                },
                validation(cellValue, rowValue) {
                    // Handle some basic validation
                    switch (rowValue.type) {
                        case 'integer':
                            if (Number.isNaN(Number(cellValue))) {
                                return ui.localize(
                                    '@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__needs_to_be_a_number',
                                    'Needs to be a number.',
                                );
                            }
                            break;
                        case 'boolean':
                            if (cellValue !== 'true' && cellValue !== 'false') {
                                return ui.localize(
                                    '@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__needs_to_be_either_true_or_false',
                                    'Needs to be one of the following: {{0}}, {{1}}.',
                                    ['true', 'false'],
                                );
                            }
                            break;
                        default:
                            break;
                    }
                    return undefined;
                },
            }),
        ],
    })
    jobParameters: ui.fields.Table<Parameter, SysJobScheduleWizard>;

    @ui.decorators.labelField<SysJobScheduleWizard>({
        bind: 'parameterValues',
        isHidden: true,
    })
    jobParameterValues: ui.fields.Label;

    // Values returned to parent page (i.e. SysJobSchedule)
    getSerializedValues(): ui.plugin.Dict<any> {
        this.$.page.validate();
        return {
            ...this.$.values,
            parameterValues: transformParametersNameValue(this.jobParameters.value),
        };
    }
}
