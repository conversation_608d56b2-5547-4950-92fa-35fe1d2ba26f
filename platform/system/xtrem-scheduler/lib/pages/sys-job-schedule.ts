import { SysNotificationState } from '@sage/xtrem-communication-api';
import { scheduler } from '@sage/xtrem-communication/build/lib/menu-items/scheduler';
import { datetime } from '@sage/xtrem-date-time';
import { MetaNodeOperation } from '@sage/xtrem-metadata-api';
import { GraphApi } from '@sage/xtrem-scheduler-api';
import { User } from '@sage/xtrem-system-api';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import { CurrentUser, getExecutionUser } from '@sage/xtrem-system/lib/client-functions/user';
import * as utils from '@sage/xtrem-system/lib/client-functions/utils';
import * as ui from '@sage/xtrem-ui';
import * as cronstrue from 'cronstrue';
import { getTimeZones } from '../client-functions/common';
import { Parameter } from '../client-functions/interfaces/parameters';
import { getParametersFromOperationAndValues, transformParametersNameValue } from '../client-functions/job-execution';
import {
    getEndStamp,
    getStartStamp,
    loadParametersFromOperationAndValues,
    optionsForPurge,
    scheduleWizard,
} from '../client-functions/job-schedule';

@ui.decorators.page<SysJobSchedule>({
    title: 'Job schedule',
    objectTypeSingular: 'Job schedule',
    objectTypePlural: 'Jobs schedule',
    priority: 100,
    menuItem: scheduler,
    mode: 'tabs',
    node: '@sage/xtrem-scheduler/SysJobSchedule',
    navigationPanel: {
        bulkActions: [
            {
                mutation: 'bulkDelete',
                title: 'Delete',
                buttonType: 'tertiary',
                icon: 'delete',
                isDestructive: true,
            },
        ],
        listItem: {
            title: ui.nestedFields.text({ bind: { id: true }, title: 'ID' }),
            titleRight: ui.nestedFields.text({ bind: { description: true }, title: 'Description', canFilter: false }),
            line1: ui.nestedFields.text({ bind: { executionUser: { email: true } }, title: 'User' }),
            line3Right: ui.nestedFields.text({
                bind: { operation: { title: true } },
                title: 'Operation',
                canFilter: false,
            }),
            line3: ui.nestedFields.checkbox({ bind: { isActive: true }, title: 'Active' }),
            line2Right: ui.nestedFields.text({ bind: 'cronTranslated', title: 'Cron schedule', canFilter: false }),
            line_5: ui.nestedFields.label({
                bind: 'nextExecutionStamp',
                title: 'Next schedule',
                canFilter: false, // Computed property
                map(_id, row) {
                    return datetime
                        .parse(row.nextExecutionStamp)
                        .format(this.$.locale || undefined, 'YYYY-MM-DD HH:mm:ss');
                },
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
            }),
            // Enhancement request XT-50032  uncomment after resolve
            // line6: ui.nestedFields.aggregate({
            //     canFilter: false,
            //     title: 'Last run',
            //     bind: 'executions',
            //     aggregateOn: 'timeStarted',
            //     aggregationMethod: 'max',
            //     scale: 20,
            // }),
        },
        optionsMenu: [
            {
                title: 'All',
                graphQLFilter: {},
            },
        ],
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
        });
    },
    createAction() {
        return this.createCrud;
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction],
        });
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction, this.execute];
    },
    async onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
        });
        this.timeZone.options = await getTimeZones(this.$.graph);
        this.cronSchedule.helperText = this.cronTranslated.value || '';

        if (!this.timeZone.value) {
            const currentTimeZone = Intl?.DateTimeFormat()?.resolvedOptions()?.timeZone;
            this.timeZone.value = currentTimeZone;
        }

        if (this.cronSchedule.value === '') {
            this.cronSchedule.isHidden = true;
        }

        if (this.endStamp.value) {
            this.endStamp.value = datetime.parse(this.endStamp.value.toString());
        }

        if (this.startStamp.value) {
            this.startStamp.value = datetime.parse(this.startStamp.value.toString());
        }

        if (this.operation.value) {
            this.parameterNames = loadParametersFromOperationAndValues(
                this.operation.value,
                this.parameterValues.value || '',
            );
            this.parameters.value = this.parameterNames;
        }

        this.$standardDeleteAction.isHidden = this.executions.value.length !== 0;

        this.currentUser = await getExecutionUser(this);
        this.executionUser.isReadOnly = !this.currentUser?.isAdministrator;

        if (this.$.isServiceOptionEnabled('devTools')) {
            this.execute.isHidden = false;
        }
    },
})
export class SysJobSchedule extends ui.Page<GraphApi> {
    currentUser: CurrentUser | null;

    parameterNames: Parameter[];

    defaultCronSchedule = '* * * * *';

    getSerializedValues(): ui.plugin.Dict<any> {
        const { values } = this.$;
        const parameterValues = transformParametersNameValue(this.parameters.value);
        delete values._vendor;
        if (this._vendor.value) {
            delete values.parameterValues;
            return {
                ...values,
                endStamp: getEndStamp(this),
                startStamp: getStartStamp(this),
            };
        }
        return {
            ...values,
            parameterValues,
            endStamp: getEndStamp(this),
            startStamp: getStartStamp(this),
        };
    }

    @ui.decorators.pageAction<SysJobSchedule>({
        async onClick() {
            await scheduleWizard(this);
        },
        icon: 'create',
        title: 'Create',
        buttonType: 'primary',
    })
    createCrud: ui.PageAction;

    @ui.decorators.pageAction<SysJobSchedule>({
        async onClick() {
            const notificationStateSysId = await this.$.graph
                .node('@sage/xtrem-communication/SysNotificationState')
                .mutations.execute(true, {
                    jobExecution: {
                        operation: this.operation.value?._id,
                        parameterValues: this.parameterValues.value || '{}',
                    },
                })
                .execute();
            await this.$.dialog.page(
                '@sage/xtrem-communication/SysNotificationState',
                { _id: notificationStateSysId.toString() },
                { fullScreen: true, resolveOnCancel: true },
            );
        },
        isHidden: true,
        icon: 'play',
        title: 'Run now',
        buttonType: 'primary',
    })
    execute: ui.PageAction;

    @ui.decorators.section<SysJobSchedule>({
        title: 'General',
        isTitleHidden: true,
    })
    generalSection: ui.containers.Section;

    @ui.decorators.section<SysJobSchedule>({
        title: 'Execute history',
        isTitleHidden: true,
    })
    executeSection: ui.containers.Section;

    @ui.decorators.block<SysJobSchedule>({
        parent() {
            return this.generalSection;
        },
    })
    generalBlock: ui.containers.Block;

    @ui.decorators.block<SysJobSchedule>({
        parent() {
            return this.executeSection;
        },
    })
    executeBlock: ui.containers.Block;

    @ui.decorators.switchField<SysJobSchedule>({
        parent() {
            return this.generalBlock;
        },
        title: 'Active',
        width: 'small',
    })
    isActive: ui.fields.Switch;

    @ui.decorators.separatorField<SysJobSchedule>({
        parent() {
            return this.generalBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    separatorFromIsActive: ui.fields.Separator;

    @ui.decorators.textField<SysJobSchedule>({
        parent() {
            return this.generalBlock;
        },
        width: 'small',
        title: 'ID',
        isReadOnly: true,
    })
    id: ui.fields.Text;

    @ui.decorators.textField<SysJobSchedule>({
        isHidden: true,
        bind: { _vendor: { name: true } },
    })
    _vendor: ui.fields.Text;

    @ui.decorators.referenceField<SysJobSchedule, MetaNodeOperation>({
        node: '@sage/xtrem-metadata/MetaNodeOperation',
        title: 'Operation',
        isReadOnly: true,
        width: 'medium',
        parent() {
            return this.generalBlock;
        },
        valueField: 'title',
        columns: [
            ui.nestedFields.text({ bind: 'title', title: 'Operation' }),
            ui.nestedFields.text({ bind: { factory: { name: true } }, title: 'Node' }),
            ui.nestedFields.technical({ bind: 'parameters' }),
            ui.nestedFields.technical({ bind: { _id: true } }),
            ui.nestedFields.checkbox({ bind: 'isSchedulable', title: 'can schedule' }),
        ],
        filter: {
            kind: 'asyncMutation',
            action: 'start',
        },
        async onChange() {
            await this.id.fetchDefault();
            this.parameters.value = getParametersFromOperationAndValues(
                this.operation.value,
                this.parameterValues.value || '',
            );
        },
    })
    operation: ui.fields.Reference<MetaNodeOperation>;

    @ui.decorators.referenceField<SysJobSchedule, User>({
        parent() {
            return this.generalBlock;
        },
        title: 'Execution user',
        node: '@sage/xtrem-system/User',
        width: 'medium',
        valueField: 'email',
        columns: [
            ui.nestedFields.text({ title: 'Email', bind: 'email' }),
            ui.nestedFields.text({ title: 'Name', bind: 'firstName' }),
            ui.nestedFields.text({ title: 'Last name', bind: 'lastName' }),
        ],
    })
    executionUser: ui.fields.Reference<User>;

    @ui.decorators.textField<SysJobSchedule>({})
    _id: ui.fields.Text;

    @ui.decorators.textField<SysJobSchedule>({
        parent() {
            return this.generalBlock;
        },
        title: 'Description',
        width: 'large',
    })
    description: ui.fields.Text;

    @ui.decorators.textField<SysJobSchedule>({
        parent() {
            return this.generalBlock;
        },
        title: 'Cron expression',
        width: 'large',
        isHidden() {
            return !this.currentUser?.isAdministrator;
        },
        onChange() {
            this.cronSchedule.helperText = this.cronSchedule.value
                ? cronstrue.toString(this.cronSchedule.value, { locale: this.$.locale?.substring(0, 2) || 'en' })
                : '';
        },
    })
    cronSchedule: ui.fields.Text;

    @ui.decorators.textField<SysJobSchedule>({
        title: 'Translated cron',
        width: 'large',
        parent() {
            return this.generalBlock;
        },
        isHidden() {
            return !!this.currentUser?.isAdministrator;
        },
    })
    cronTranslated: ui.fields.Text;

    @ui.decorators.dateTimeField<SysJobSchedule>({
        parent() {
            return this.generalBlock;
        },
        title: 'Start date',
        size: 'large',
        minDate: new Date(),
    })
    startStamp: ui.fields.Datetime;

    @ui.decorators.dateTimeField<SysJobSchedule>({
        parent() {
            return this.generalBlock;
        },
        title: 'End date',
        size: 'large',
        minDate: new Date(),
        isHidden() {
            return this.cronSchedule.value === '';
        },
    })
    endStamp: ui.fields.Datetime;

    @ui.decorators.selectField<SysJobSchedule>({
        parent() {
            return this.generalBlock;
        },
        title: 'Time zone',
        isHidden: true,
    })
    timeZone: ui.fields.Select;

    @ui.decorators.labelField<SysJobSchedule>({
        isHidden: true,
    })
    parameterValues: ui.fields.Label;

    @ui.decorators.tableField<SysJobSchedule, Parameter>({
        title: 'Parameters',
        isTransient: true,
        canSelect: false,
        parent() {
            return this.generalSection;
        },
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Name', isReadOnly: true }),
            ui.nestedFields.text({ bind: 'value', title: 'Value' }),
        ],
    })
    parameters: ui.fields.Table<Parameter>;

    @ui.decorators.pageAction<SysJobSchedule>({
        title: 'Purge execution',
        icon: 'cross',
        async onClick() {
            if (
                this._id.value &&
                (await utils.confirmDialogToBoolean(
                    this.$.dialog.confirmation(
                        'warn',
                        ui.localize('@sage/xtrem-scheduler/purge-sys-message-history-title', 'Delete history'),
                        ui.localize(
                            '@sage/xtrem-scheduler/purge-sys-execute-history',
                            'You are about to delete the execute history.',
                        ),
                        optionsForPurge,
                    ),
                ))
            ) {
                await this.$.graph
                    .node('@sage/xtrem-scheduler/SysJobSchedule')
                    .mutations.purgeExecution(true, { schedule: this._id.value })
                    .execute();
                await this.executions.refresh();
                this.$standardDeleteAction.isHidden = this.executions.value.length !== 0;
            }
        },
    })
    purgeExecution: ui.PageAction;

    @ui.decorators.pageAction<SysJobSchedule>({
        icon: 'refresh',
        title: 'Refresh',
        async onClick() {
            await this.executions.refresh();
            this.$standardDeleteAction.isHidden = this.executions.value.length !== 0;
        },
    })
    refreshExecutedJobs: ui.PageAction;

    @ui.decorators.tableField<SysJobSchedule, SysNotificationState>({
        title: 'Executed jobs',
        canSelect: false,
        displayMode: ui.fields.TableDisplayMode.compact,
        parent() {
            return this.executeSection;
        },
        columns: [
            ui.nestedFields.technical({ bind: '_id' }),
            ui.nestedFields.text({ bind: 'status', title: 'status', isReadOnly: true }),
            ui.nestedFields.label({
                bind: 'timeStarted',
                title: 'Start',
                map(_id, row) {
                    return datetime.parse(row.timeStarted).format(this.$.locale || undefined, 'YYYY-MM-DD HH:mm:ss');
                },
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
            }),
            ui.nestedFields.label({
                bind: 'timeEnded',
                title: 'End',

                map(_id, row) {
                    return datetime.parse(row.timeStarted).format(this.$.locale || undefined, 'YYYY-MM-DD HH:mm:ss');
                },
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
            }),
            ui.nestedFields.link({
                bind: '_id',
                title: 'Details',
                page: '@sage/xtrem-communication/SysNotificationState',
                queryParameters(_value, rowData) {
                    return {
                        _id: rowData?._id as string,
                    };
                },
            }),
        ],
        fieldActions() {
            return [this.refreshExecutedJobs, this.purgeExecution];
        },
        dropdownActions: [
            {
                title: 'Stop',
                icon: 'cross_circle',
                async onClick(recordId, job) {
                    await this.$.graph
                        .node('@sage/xtrem-communication/SysNotificationState')
                        .mutations.stop(true, {
                            jobExecutionSysId: job._id,
                            reason: ui.localize(
                                '@sage/xtrem-scheduler/pages__sys_notification_state__ask_by_user',
                                'Asked by user',
                            ),
                        })
                        .execute();
                    this.executions.refresh();

                    this.$standardDeleteAction.isHidden = this.executions.value.length !== 0;
                },
            },
        ],
    })
    executions: ui.fields.Table<SysNotificationState>;
}
