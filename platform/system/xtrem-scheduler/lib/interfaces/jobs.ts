import { AnyRecord, datetime, PubSubPayload, UserInfo } from '@sage/xtrem-core';
import * as xtremMetadata from '@sage/xtrem-metadata';
import * as cron from 'node-schedule';

export interface JobDefinitionParameters {
    type: string;
    isMandatory: boolean;
    nodeName?: string;
    defaultValue?: string;
}

export interface Job {
    operation: xtremMetadata.nodes.MetaNodeOperation;
    parameterValues: AnyRecord;
    topic: string;
}

export interface JobSchedule extends Job {
    scheduleId: number;
    cronSchedule: string;
    timeZone: string;
    task?: cron.Job;
    executionUser: UserInfo;
    executionLocale: string;
    startStamp: datetime | null;
    endStamp: datetime | null;
    login: UserInfo;
}

export interface JobSchedulePayload extends JobSchedule, PubSubPayload, AnyRecord {}
