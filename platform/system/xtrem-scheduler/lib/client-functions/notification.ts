import { NotificationStatus } from '@sage/xtrem-communication-api';
import * as ui from '@sage/xtrem-ui';

export function getNotificationType(status: NotificationStatus): ui.ToastType {
    switch (status) {
        case 'error':
            return 'error';
        case 'running':
        case 'pending':
            return 'info';
        case 'success':
            return 'success';
        default:
            return 'warning';
    }
}
