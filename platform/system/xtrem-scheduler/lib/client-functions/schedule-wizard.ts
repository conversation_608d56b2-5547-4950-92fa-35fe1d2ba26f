import * as xtremDateTime from '@sage/xtrem-date-time';
import * as ui from '@sage/xtrem-ui';
import { isNumber } from 'lodash';
import { SysJobScheduleWizard } from '../pages/sys-job-schedule-wizard';
import { nextExecution, NextExecutionError } from '../shared-functions/job-schedule';
import { getDateTime } from './job-execution';
import { DateTimeParameter } from './interfaces/parameters';

export function dateTimeValidation(datetimeValue: DateTimeParameter): string | undefined {
    try {
        const dateTime = getDateTime(datetimeValue);
        if (dateTime.compare(xtremDateTime.datetime.now()) < 0) {
            return ui.localize(
                '@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__needs_to_be_in_the_future',
                'Needs to be in the future',
            );
        }
    } catch (err) {
        return ui.localize('@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__invalid_date', 'Invalid date');
    }
    return undefined;
}

export function startEndDateValidation(
    startDateTimeValue: DateTimeParameter,
    endDateTimeValue: DateTimeParameter,
): string | undefined {
    try {
        const startDateTime = getDateTime(startDateTimeValue);
        const endDateTime = getDateTime(endDateTimeValue);
        if (startDateTime.compare(endDateTime) > 0) {
            return ui.localize(
                '@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__stop_date_must_be_later_start_date',
                'The stop date needs to be later than the start date.',
            );
        }
    } catch (err) {
        return ui.localize('@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__invalid_date', 'Invalid date');
    }
    return undefined;
}

export const monday = ui.localize('@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__monday', 'Monday');

export const tuesday = ui.localize('@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__tuesday', 'Tuesday');

export const wednesday = ui.localize('@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__wednesday', 'Wednesday');

export const thursday = ui.localize('@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__thursday', 'Thursday');

export const friday = ui.localize('@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__friday', 'Friday');

export const saturday = ui.localize('@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__saturday', 'Saturday');

export const sunday = ui.localize('@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__sunday', 'Sunday');

export const dayOfTheWeek = [monday, tuesday, wednesday, thursday, friday, saturday, sunday];

function transformDays(days: string[]): string[] {
    return days.map(day => {
        switch (day) {
            case monday:
                return 'MON';
            case tuesday:
                return 'TUE';
            case wednesday:
                return 'WED';
            case thursday:
                return 'THU';
            case friday:
                return 'FRI';
            case saturday:
                return 'SAT';
            case sunday:
                return 'SUN';
            default:
                return '';
        }
    });
}

export const daily = ui.localize('@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__daily', 'Daily');

export const weekly = ui.localize('@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__weekly', 'Weekly');

export const monthly = ui.localize('@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__monthly', 'Monthly');

export const recurrenceNotSet = ui.localize(
    '@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__recurrence-not-set',
    'You need to set the frequency.',
);

const cronSeparator = { list: ',', range: '-', step: '/' };

enum TypeSeparator {
    list,
    range,
    step,
}

interface HourMinuteParameters {
    hour: number[];
    minute: number[];
    hourMinutetype: TypeSeparator;
}

interface DailyParamters extends HourMinuteParameters {
    day: string[];
}

interface CronObject {
    minute?: string;
    hour?: string;
    /** month day */
    monthDay?: string;
    month?: string;
    /** week day */
    day?: string;
}

function joinCron(cron: CronObject): string {
    const { minute, hour, monthDay, month, day } = cron;
    return `${minute || '*'} ${hour || '*'} ${monthDay || '*'} ${month || '*'} ${day || '*'}`;
}

export function generateCronString(wizard: SysJobScheduleWizard): string {
    switch (wizard.reccurence.value) {
        case daily:
            /**
             * TODO : add case every minutes or hours
             * case at
             */
            if (isNumber(wizard.atJobHour.value) && isNumber(wizard.atJobMinute.value)) {
                return joinCron(
                    dailyCronString({
                        hour: [wizard.atJobHour.value],
                        minute: [wizard.atJobMinute.value],
                        hourMinutetype: TypeSeparator.list,
                    }),
                );
            }
            break;
        case weekly:
            if (isNumber(wizard.atJobHour.value) && isNumber(wizard.atJobMinute.value) && wizard.days.value) {
                return joinCron(
                    weeklyCronString({
                        hour: [wizard.atJobHour.value],
                        minute: [wizard.atJobMinute.value],
                        hourMinutetype: TypeSeparator.list,
                        day: wizard.days.value,
                    }),
                );
            }
            break;
        case monthly:
            break;
        default:
            throw new Error(recurrenceNotSet);
    }

    return '';
}

export function dailyCronString(parameters: HourMinuteParameters): CronObject {
    return { minute: parameters.minute.join(cronSeparator.list), hour: parameters.hour.join(cronSeparator.list) };
}

export function weeklyCronString(parameters: DailyParamters): CronObject {
    const daysNumber = transformDays(parameters.day);
    return { ...dailyCronString(parameters), day: daysNumber.join(',') };
}

export function scheduleValidationError(
    nextExecutionResult: xtremDateTime.Datetime | NextExecutionError,
): string | undefined {
    switch (nextExecutionResult) {
        case NextExecutionError.cronStringStartDateEmpty:
            return ui.localize(
                '@sage/xtrem-scheduler/nodes__sys-job-schedule__no-cron-expression-no-startStamp',
                'You need a start datetime if cron expression is not define.',
            );
        case NextExecutionError.noNextExecution:
            return ui.localize(
                '@sage/xtrem-scheduler/nodes__sys-job-execution_no-next-execution',
                'This job will not be executed.',
            );
        case NextExecutionError.parse:
            return ui.localize(
                '@sage/xtrem-scheduler/nodes__sys-job-execution_parsing-error',
                'Issue while parsing the next schedule.',
            );
        default:
            return undefined;
    }
}

export function getNextExcution(wizard: SysJobScheduleWizard): xtremDateTime.Datetime | NextExecutionError {
    const startStamp =
        wizard.jobDate.value && wizard.jobHour.value && wizard.jobMinute.value
            ? getDateTime({
                  date: wizard.jobDate.value,
                  hour: wizard.jobHour.value,
                  minute: wizard.jobMinute.value,
              })
            : undefined;
    const endStamp =
        wizard.stopJobDate.value && wizard.stopJobHour.value && wizard.stopJobMinute.value
            ? getDateTime({
                  date: wizard.stopJobDate.value,
                  hour: wizard.stopJobHour.value,
                  minute: wizard.stopJobMinute.value,
              })
            : undefined;

    return nextExecution({
        cronString: wizard.cronString.value || '',
        ...(endStamp && { endDate: endStamp }),
        ...(startStamp && { startDate: startStamp }),
    });
}

export function scheduleValidation(wizard: SysJobScheduleWizard): string | undefined {
    return scheduleValidationError(getNextExcution(wizard));
}
