import * as xtremDateTime from '@sage/xtrem-date-time';
import { MetaNodeOperation } from '@sage/xtrem-metadata-api';
import * as ui from '@sage/xtrem-ui';
import { get, set } from 'lodash';
import { Parameter } from './interfaces/parameters';

export function getParametersFromOperationAndValues(
    operation: ui.PartialNode<MetaNodeOperation> | null,
    values?: string,
): Parameter[] {
    if (!operation?.parameters) {
        return [];
    }
    const parameters = JSON.parse(operation.parameters) as Pick<Parameter, 'name' | 'type' | 'isMandatory'>[];
    return parameters.map((parameter, index) => {
        return { ...parameter, _id: index.toString(), value: get(JSON.parse(values ?? '{}'), parameter.name, '') };
    });
}

export function convertAnyValueToParameterString(parameter: Parameter, value: any): string {
    if (!value) {
        return value;
    }
    switch (parameter.type) {
        case 'reference':
            return value.replace(/^#/, '');
        case 'array': {
            if (!Array.isArray(value)) {
                throw new Error(`Parameter ${parameter.name} must be an array`);
            }
            const itemType = parameter.item?.type;
            if (itemType === 'string') {
                return value.join(',');
            }
            if (itemType === 'reference') {
                return value.map(v => v.replace(/^#/, '')).join(',');
            }
            return JSON.stringify(value);
        }
        default:
            if (typeof value !== 'string') {
                return JSON.stringify(value);
            }
            return value;
    }
}

export function convertParameterStringToValue(
    parameter: ui.PartialCollectionValue<Parameter>,
): string | string[] | undefined {
    const { value, type } = parameter;
    if (type === 'array' && typeof value === 'string') {
        const itemType = parameter.item?.type;
        if (itemType === 'reference') {
            return value
                .split(',')
                .map(v => v.trim())
                .map(v => (v[0] === '#' ? v : `#${v}`));
        }
        if (itemType === 'string') {
            return value.split(',').map(v => v.trim());
        }
        return JSON.parse(value);
    }
    if (type === 'reference' && typeof value === 'string') {
        return value[0] === '#' ? value : `#${value}`;
    }
    return value;
}

export function transformParametersNameValue(parametersNameValue: ui.PartialCollectionValue<Parameter>[]): string {
    const parameterValues = {};
    if (parametersNameValue) {
        parametersNameValue.forEach(parameterNameValue => {
            if (parameterNameValue.name && parameterNameValue.value) {
                set(parameterValues, parameterNameValue.name, convertParameterStringToValue(parameterNameValue));
            }
        });
    }
    return JSON.stringify(parameterValues);
}

/** Must be deleted after datetimeField is delivered - XT-48039  */
export function getDateTime({
    date,
    hour,
    minute,
}: {
    date: string;
    hour?: number | null;
    minute?: number | null;
}): xtremDateTime.Datetime {
    const xtremDate = xtremDateTime.date.parse(date);
    const jsDate = xtremDate.toJsDate();
    jsDate.setHours(hour ?? 0);
    jsDate.setMinutes(minute ?? 0);
    return xtremDateTime.datetime.fromJsDate(jsDate);
}

export function formatDateTime(date: Date): string {
    return new Intl.DateTimeFormat(Intl.DateTimeFormat().resolvedOptions().locale, {
        dateStyle: 'full',
        timeStyle: 'long',
        timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    }).format(date);
}

/** Run now - localized */
export const now = ui.localize('@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__run_now', 'Run now');
/** Run once - localized */
export const once = ui.localize('@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__run_once', 'Run once');
/** Recurring - localized */
export const recurring = ui.localize('@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__recurring', 'Recurring');
