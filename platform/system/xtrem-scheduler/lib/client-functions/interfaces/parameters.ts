export interface Parameter {
    _id: string;
    name: string;
    item?: {
        node: string;
        type: string;
    };
    value: string;
    type: 'string' | 'integer' | 'boolean' | 'reference' | 'array';
    isMandatory?: boolean;
}

export interface DateTimeParameter {
    date: string;
    hour: number | null;
    minute: number | null;
}

export interface ActiveServiceOptions {
    optionName: string;
}
