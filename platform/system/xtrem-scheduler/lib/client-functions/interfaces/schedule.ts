import { Parameter } from './parameters';

export type JobScheduleWizard = RunNow | RunOnce | Recurring;

/** need to be refactor when datetime field is available  */
export interface Task {
    operation: string;
    parameters: Parameter[];
    jobSchedule: string;
}

export interface RunNow extends Task {}
export interface RunOnce extends Task {
    jobDate: string;
    jobHour: number;
    jobMinute: number;
}

export interface Recurring extends Task {
    cronString: string;
    jobDate: string;
    jobHour: number;
    jobMinute: number;
    stopJobDate: string;
    stopJobHour: number;
    stopJobMinute: number;
}

export interface ScheduleWizard {
    /* now once or recurring from job-execution */
    jobSchedule: string[];
    /** Skip operation if operationKey is define  */
    operationKey?: string;
    /** Filter for bulks  */
    filter?: string;
    /** additionnals filters  */
    additionalParameters?: any;

    isScheduleHidden?: true;
    isOperationHidden?: true;
    isParametersHidden?: true;
}
