import { Dict } from '@sage/xtrem-client';
import { datetime } from '@sage/xtrem-date-time';
import { MetaNodeOperation } from '@sage/xtrem-metadata-api';
import { SysJobScheduleBinding, SysJobScheduleInput } from '@sage/xtrem-scheduler-api';
import type { PageWithAccessToNodes } from '@sage/xtrem-ui';
import * as ui from '@sage/xtrem-ui';
import { get } from 'lodash';
import { SysJobSchedule } from '../pages/sys-job-schedule';
import { Parameter } from './interfaces/parameters';
import { JobScheduleWizard, Recurring, RunNow, RunOnce, ScheduleWizard } from './interfaces/schedule';
import {
    convertAnyValueToParameterString,
    formatDateTime,
    getDateTime,
    now,
    once,
    recurring,
    transformParametersNameValue,
} from './job-execution';

export function getEndStamp(schedulePage: SysJobSchedule): string | null {
    if (schedulePage.endStamp.value) {
        return schedulePage.endStamp.value.toString();
    }
    return null;
}

export function getStartStamp(schedulePage: SysJobSchedule): string | null {
    if (schedulePage.startStamp.value) {
        return schedulePage.startStamp.value.toString();
    }
    return null;
}

type Page = PageWithAccessToNodes<
    ['@sage/xtrem-communication/SysNotificationState', '@sage/xtrem-scheduler/SysJobSchedule']
>;

/** execute a sysNotification state  */
async function runNow(schedulePage: Page, dialogResult: RunNow, options?: { noRedirect?: boolean }): Promise<void> {
    const notificationStateSysId = await schedulePage.$.graph
        .node('@sage/xtrem-communication/SysNotificationState')
        .mutations.execute(true, {
            jobExecution: {
                operation: dialogResult.operation,
                parameterValues: transformParametersNameValue(dialogResult.parameters),
            },
        })
        .execute();
    schedulePage.$.showToast(
        ui.localize('@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__batch_task_started', 'Batch task started'),
    );
    if (!options?.noRedirect) {
        schedulePage.$.router.goTo('@sage/xtrem-communication/SysNotificationState', { _id: notificationStateSysId });
    }
}

function dateInTheFuture(schedulePage: Page, compareDate: datetime.Datetime): void {
    if (compareDate.compare(datetime.now()) < 0) {
        schedulePage.$.showToast(
            ui.localize(
                '@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__needs_to_be_in_the_future_with_argument',
                'Needs to be in the future: {{0}}',
                [formatDateTime(compareDate.toJsDate())],
            ),
        );
    }
}

async function createSysJobSchedule(
    schedulePage: Page,
    data: SysJobScheduleInput,
    options?: { noRedirect?: boolean },
): Promise<void> {
    const jobSchedule = await schedulePage.$.graph
        .node('@sage/xtrem-scheduler/SysJobSchedule')
        .create(
            { _id: true, nextExecutionStamp: true },
            {
                data,
            },
        )
        .execute();

    if (jobSchedule) {
        await goToCreatedschedule(schedulePage, jobSchedule, options);
    }
}

async function goToCreatedschedule(
    schedulePage: Page,
    job: Partial<SysJobScheduleBinding>,
    options?: { noRedirect?: boolean },
): Promise<void> {
    if (job._id) {
        const nextExecution = datetime.Datetime.parse(job.nextExecutionStamp || '', 'base');

        schedulePage.$.showToast(
            ui.localize(
                '@sage/xtrem-scheduler/pages__sys_job_schedule_wizard__batch_task_scheduled',
                'Batch task scheduled: {{0}}',
                [formatDateTime(nextExecution.toJsDate())],
            ),
        );
        if (!options?.noRedirect) {
            if (schedulePage._pageMetadata.screenId === 'SysJobSchedule') {
                await schedulePage.$.router.selectRecord(job._id?.toString());
                await schedulePage.$.refreshNavigationPanel();
            } else {
                schedulePage.$.router.goTo('@sage/xtrem-scheduler/SysJobSchedule', { _id: job._id });
            }
        }
    }
}

async function runOnce(schedulePage: Page, dialogResult: RunOnce, options?: { noRedirect?: boolean }): Promise<void> {
    const startDate = getDateTime({
        date: dialogResult.jobDate,
        hour: dialogResult.jobHour,
        minute: dialogResult.jobMinute,
    });

    dateInTheFuture(schedulePage, startDate);

    await createSysJobSchedule(
        schedulePage,
        {
            operation: dialogResult.operation,
            startStamp: startDate.toString(),
            timeZone: Intl?.DateTimeFormat()?.resolvedOptions()?.timeZone,
            parameterValues: transformParametersNameValue(dialogResult.parameters),
        },
        options,
    );
}

export async function reccuring(
    schedulePage: Page,
    dialogResult: Recurring,
    options?: { noRedirect?: boolean },
): Promise<void> {
    const startStamp =
        dialogResult.jobDate && dialogResult.jobHour && dialogResult.jobMinute
            ? getDateTime({
                  date: dialogResult.jobDate,
                  hour: dialogResult.jobHour,
                  minute: dialogResult.jobMinute,
              })
            : undefined;
    const endStamp =
        dialogResult.stopJobDate && dialogResult.stopJobHour && dialogResult.stopJobMinute
            ? getDateTime({
                  date: dialogResult.stopJobDate,
                  hour: dialogResult.stopJobHour,
                  minute: dialogResult.stopJobMinute,
              })
            : undefined;

    await createSysJobSchedule(
        schedulePage,
        {
            operation: dialogResult.operation,
            ...(startStamp && { startStamp: startStamp?.toString() }),
            ...(endStamp && { endStamp: endStamp?.toString() }),
            cronSchedule: dialogResult.cronString,
            timeZone: Intl?.DateTimeFormat()?.resolvedOptions()?.timeZone,
            parameterValues: transformParametersNameValue(dialogResult.parameters),
        },
        options,
    );
}

export async function scheduleWizard(
    schedulePage: Page,
    parameters?: ScheduleWizard,
    options?: { noRedirect?: boolean },
): Promise<void> {
    const dictParam: Dict<string> = {};

    Object.entries(parameters || {}).forEach(([key, value]) => {
        dictParam[key] = typeof value === 'string' ? value : JSON.stringify(value);
    });

    const dialogResult: JobScheduleWizard = await schedulePage.$.dialog.page(
        '@sage/xtrem-scheduler/SysJobScheduleWizard',
        dictParam,
        {
            height: 580,
        },
    );

    switch (dialogResult.jobSchedule) {
        case now:
            await runNow(schedulePage, dialogResult as RunNow, options);
            break;
        case once:
            await runOnce(schedulePage, dialogResult as RunOnce, options);
            break;
        case recurring:
            await reccuring(schedulePage, dialogResult as Recurring, options);
            break;
        default:
            break;
    }
}

export const optionsForPurge: ui.dialogs.DialogOptions = {
    acceptButton: {
        text: ui.localize('@sage/xtrem-scheduler/pages-confirm-purge', 'Delete'),
    },
    cancelButton: {
        text: ui.localize('@sage/xtrem-scheduler/pages-confirm-cancel', 'Cancel'),
    },
    resolveOnCancel: false,
};

export function loadParametersFromOperationAndValues(
    operation: ui.PartialNode<MetaNodeOperation> | null,
    values: string,
): Parameter[] {
    const valuesObject = JSON.parse(values);
    if (!operation?.parameters) {
        return [];
    }
    const parameters = JSON.parse(operation.parameters) as Parameter[];
    return parameters.map((parameter, index) => {
        const valueParameter = get(valuesObject, parameter.name, '');
        return {
            ...parameter,
            _id: index.toString(),
            value: convertAnyValueToParameterString(parameter, valueParameter),
        };
    });
}
