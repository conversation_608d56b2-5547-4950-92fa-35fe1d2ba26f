import { CustomSqlAction } from '@sage/xtrem-system';

export const deleteContentAddressableGcScheduleEntries = new CustomSqlAction({
    description: 'Delete schedule entries for garbage collection of  content addressable tables',
    body: async helper => {
        await helper.executeSql(`
            DELETE FROM ${helper.schemaName}.sys_notification_state st
                USING ${helper.schemaName}.sys_job_schedule sched
                WHERE
                    st._tenant_id = sched._tenant_id
                AND st.schedule = sched._id
                AND sched.id = 'purgeContentAddressableTables';

            DELETE FROM ${helper.schemaName}.sys_job_schedule sched
                WHERE sched.id = 'purgeContentAddressableTables';
            `);
    },
});
