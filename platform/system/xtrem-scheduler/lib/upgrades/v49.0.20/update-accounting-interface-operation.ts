import { CustomSqlAction } from '@sage/xtrem-system';

export const updateAccountingInterfaceOperationOnSchedulledJobs = new CustomSqlAction({
    description:
        'Update the schedulled jobs for the new finance operation because mutation was moved to a different node',
    body: async helper => {
        await helper.executeSql(`
            DO $$
            DECLARE old_operation RECORD;
            BEGIN FOR old_operation IN
                    SELECT operations._id, operations.action FROM ${helper.schemaName}.meta_node_operation operations
                    INNER JOIN ${helper.schemaName}.meta_node_factory node_factory
                    ON operations.factory = node_factory._id
                    WHERE operations.name = 'createJournalsFromAccountingStagingJob'
                    AND node_factory.name = 'AccountingStaging'

                LOOP
                    UPDATE ${helper.schemaName}.sys_job_schedule sjs
                        SET operation = new_operation._id
                        FROM
                            (SELECT new_operations._id, new_operations.action FROM ${helper.schemaName}.meta_node_operation new_operations
                            INNER JOIN ${helper.schemaName}.meta_node_factory node_factory
                            ON new_operations.factory = node_factory._id
                            WHERE new_operations.name = 'createJournalsFromAccountingStagingJob'
                            AND node_factory.name = 'AccountingInterfaceListener'
                            AND old_operation.action = new_operations.action) as new_operation
                        WHERE sjs.operation = old_operation._id;
            END LOOP;
            END; $$
            `);
    },
});
