import { MetaNodeOperation } from '@sage/xtrem-metadata-api';
import { MetaNodeFactory } from '@sage/xtrem-metadata/build/lib/pages/meta-node-factory';
import { GraphApi } from '@sage/xtrem-scheduler-api';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<MetaNodeFactoryExtension>({
    extends: '@sage/xtrem-metadata/MetaNodeFactory',
})
export class MetaNodeFactoryExtension extends ui.PageExtension<MetaNodeFactory, GraphApi> {
    @ui.decorators.tableFieldOverride<MetaNodeFactoryExtension, MetaNodeOperation>({
        onRowSelected(recordId: string, rowItem) {
            this.$.showToast(`Select  ${recordId} , ${rowItem}`, { type: 'success' });
        },
    })
    operations: ui.fields.Table<MetaNodeOperation>;
}

declare module '@sage/xtrem-metadata/build/lib/pages/meta-node-factory' {
    interface MetaNodeFactory extends MetaNodeFactoryExtension {}
}
