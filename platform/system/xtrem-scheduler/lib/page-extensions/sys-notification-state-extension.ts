import { SysNotificationState } from '@sage/xtrem-communication/build/lib/pages/sys-notification-state';
import { MetaNodeOperation } from '@sage/xtrem-metadata-api';
import { GraphApi, SysJobSchedule } from '@sage/xtrem-scheduler-api';
import * as ui from '@sage/xtrem-ui';
import { getParametersFromOperationAndValues, transformParametersNameValue } from '../client-functions/job-execution';

@ui.decorators.pageExtension<SysNotificationStateExtension>({
    extends: '@sage/xtrem-communication/SysNotificationState',
    navigationPanel: {
        listItem: {
            line1: ui.nestedFields.text({ bind: { schedule: { description: true } }, title: 'Schedule' }),
            line2: ui.nestedFields.text({ bind: { operation: { title: true } }, title: 'Operation', canFilter: false }),
            line7: ui.nestedFields.checkbox({ bind: 'isScheduled', title: 'Recurrent' }),
        },
    },
    onLoad() {
        this.operationName.isHidden = !!this.operation.value;
    },
    businessActions() {
        return [this.execute, this.stop];
    },
})
export class SysNotificationStateExtension extends ui.PageExtension<SysNotificationState, GraphApi> {
    @ui.decorators.textFieldOverride<SysNotificationStateExtension>({
        isHidden: true,
    })
    operationName: ui.fields.Text;

    @ui.decorators.referenceField<SysNotificationStateExtension, SysJobSchedule>({
        node: '@sage/xtrem-scheduler/SysJobSchedule',
        title: 'Schedule',
        isHidden: true,
        valueField: '_id',
        columns: [ui.nestedFields.technical({ bind: 'description' })],
    })
    schedule: ui.fields.Reference<SysJobSchedule>;

    @ui.decorators.linkField<SysNotificationStateExtension>({
        isTransient: true,
        parent() {
            return this.generalBlock;
        },
        title: 'Schedule',
        isHidden() {
            return !this.schedule.value?._id;
        },
        map() {
            if (this.schedule.value?._id) {
                const { _id, description } = this.schedule.value;
                return description && description.length > 0 ? description : _id;
            }
            return '';
        },
        onClick() {
            if (this.schedule.value?._id) {
                this.$.router.goTo('@sage/xtrem-scheduler/SysJobSchedule', {
                    _id: this.schedule.value._id,
                });
            }
        },
    })
    schedulePage: ui.fields.Link;

    @ui.decorators.referenceField<SysNotificationStateExtension, MetaNodeOperation>({
        node: '@sage/xtrem-metadata/MetaNodeOperation',
        title: 'Operation',
        parent() {
            return this.generalBlock;
        },
        valueField: 'title',
        canFilter: true,
        isReadOnly() {
            return !!this.notificationId.value;
        },
        columns: [
            ui.nestedFields.text({ bind: { factory: { name: true } }, title: 'Node' }),
            ui.nestedFields.text({ bind: 'name', title: 'Operation' }),
            ui.nestedFields.technical({ bind: '_id' }),
            ui.nestedFields.technical({ bind: 'parameters' }),
            ui.nestedFields.checkbox({ bind: 'isSchedulable', title: 'can schedule' }),
        ],
        filter: {
            kind: { _in: ['asyncMutation', 'bulkMutation'] },
            name: { _nin: ['bulkDelete'] },
            action: 'start',
        },
        onChange() {
            this.operationName.value = `${this.operation.value?.factory?.name}.${this.operation.value?.name}`;
            this.parameters.value = getParametersFromOperationAndValues(
                this.operation.value,
                this.parameterValues.value || '',
            );
        },
    })
    operation: ui.fields.Reference<MetaNodeOperation>;

    @ui.decorators.pageAction<SysNotificationStateExtension>({
        title: 'Stop',
        buttonType: 'primary',
        isHidden() {
            return !this._id.value || !['pending', 'running'].includes(this.status.value || '');
        },
        async onClick() {
            if (!this._id.value) return;
            await this.$.graph
                .node('@sage/xtrem-communication/SysNotificationState')
                .mutations.stop(true, {
                    jobExecutionSysId: this._id.value,
                    reason: ui.localize(
                        '@sage/xtrem-scheduler/pages__sys_notification_state__ask_by_user',
                        'Asked by user',
                    ),
                })
                .execute();
            await this.$.router.refresh();
        },
    })
    stop: ui.PageAction;

    @ui.decorators.pageAction<SysNotificationStateExtension>({
        title: 'Execute',
        buttonType: 'primary',
        isHidden() {
            return !!this._id.value;
        },
        async onClick() {
            const sysId = await this.$.graph
                .node('@sage/xtrem-communication/SysNotificationState')
                .mutations.execute(true, {
                    jobExecution: {
                        operation: this.operation.value?._id,
                        parameterValues: transformParametersNameValue(this.parameters.value),
                    },
                })
                .execute();
            if (sysId) {
                await this.$.router.selectRecord(sysId.toString(), true);
                await this.$.refreshNavigationPanel();
            }
        },
    })
    execute: ui.PageAction;
}

declare module '@sage/xtrem-communication/build/lib/pages/sys-notification-state' {
    interface SysNotificationState extends SysNotificationStateExtension {}
}
