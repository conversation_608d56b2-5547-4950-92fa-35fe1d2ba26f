import * as xtremCommunication from '@sage/xtrem-communication';
import {
    AccessRights,
    Application,
    asyncArray,
    Context,
    datetime,
    Dict,
    NodeCreateData,
    PubSub,
    withAdvisoryLock,
} from '@sage/xtrem-core';
import * as xtremMetadata from '@sage/xtrem-metadata';
import { nanoid } from 'nanoid';
import * as cronSchedule from 'node-schedule';
import { Job, JobSchedule, JobSchedulePayload } from '../interfaces';
import { SysJobSchedule } from '../nodes/sys-job-schedule';
import { logger } from './utils';

/** @internal */
export abstract class JobManager {
    /**
     * @internal
     * list of  scheduled tasks per tenant
     */
    static scheduledJobs: Dict<JobSchedule[]> = {};

    static async createJob(context: Context, jobInput: JobSchedule | Job, trackingId: string): Promise<number> {
        const scheduledBy =
            'scheduleId' in jobInput ? await context.tryRead(SysJobSchedule, { _id: jobInput.scheduleId }) : null;

        const logs: NodeCreateData<xtremCommunication.nodes.SysNotificationLogEntry>[] = scheduledBy
            ? [
                  {
                      level: 'info',
                      message: context.localize(
                          '@sage/xtrem-scheduler/job-manager_started-by-scheduler',
                          'Started by scheduler: {{description}}',
                          { description: (await scheduledBy?.description) || '' },
                      ),
                  },
              ]
            : [];

        const executionData: NodeCreateData<xtremCommunication.nodes.SysNotificationState> = {
            operation: jobInput.operation,
            operationName: jobInput.topic.replace('/', '.'),
            schedule: 'scheduleId' in jobInput ? jobInput.scheduleId : undefined,
            parameterValues: jobInput.parameterValues,
            timeStarted: datetime.now(),
            notificationId: trackingId,
            status: 'pending',
            logs,
        };
        logger.debug(() => `Create job ${JSON.stringify(executionData)}`);
        const newExecution = await context.create(xtremCommunication.nodes.SysNotificationState, executionData);
        await newExecution.$.save();
        return newExecution._id;
    }

    /**
     * Execute the job and create the sysExecutionJob to track it
     * @returns _id of sysExecutionJob & notification to track the job
     */
    static executeAndCreateJob(
        context: Context,
        jobInput: JobSchedule | Job,
    ): Promise<{ _id: number; notificationId: string }> {
        const schedule = 'scheduleId' in jobInput ? jobInput.scheduleId : undefined;
        const advisoryLockId = 2539403613623621; // random number
        return context.runInWritableContext(childContext =>
            // use advisory lock to prevent concurrent execution of the same job
            withAdvisoryLock(childContext, advisoryLockId, async bodyContext => {
                // if it is a scheduled job, check if there is already a pending or running job with the same parameters to not duplicate it
                if (schedule) {
                    const filter = {
                        status: { _in: ['pending', 'running'] },
                        operation: { _id: jobInput.operation._id },
                        schedule,
                        parameterValues: jobInput.parameterValues,
                    };
                    const notificationStates = bodyContext.query(xtremCommunication.nodes.SysNotificationState, {
                        filter,
                    });
                    const stateCount = await notificationStates.length;
                    if (stateCount > 0) {
                        throw new Error(`${stateCount} scheduled job ${schedule} already running or pending`);
                    }
                }
                const { notificationId } = await JobManager.executeJob(bodyContext, jobInput);
                const _id = await this.createJob(bodyContext, jobInput, notificationId);

                return { _id, notificationId };
            }),
        );
    }

    private static async executeJob(
        context: Context,
        jobInput: JobSchedule | Job,
    ): Promise<{ notificationId: string }> {
        const { nodeName, startTopic, operationName } = await JobManager.getOperationInfo(context, jobInput.operation);

        await AccessRights.checkOperationAccessRight(context, {
            nodeName,
            operationName,
            isPropertyAccess: false,
            isMutation: true,
        });

        logger.debug(() => `Notify ${startTopic} ${JSON.stringify(jobInput.parameterValues)}`);

        const notificationId = await context.notify(startTopic, jobInput.parameterValues, {
            replyTopic: 'SysNotificationState/updateStatus',
        });

        return { notificationId };
    }

    private static async getOperationInfo(
        context: Context,
        operation: xtremMetadata.nodes.MetaNodeOperation,
    ): Promise<{
        nodeName: string;
        topic: string;
        startTopic: string;
        operationName: string;
    }> {
        const operationSelect = await context.select(
            xtremMetadata.nodes.MetaNodeOperation,
            { name: true, topic: true, factory: { name: true } },
            { filter: { _id: operation._id }, first: 1 },
        );

        if (operationSelect.length <= 0) {
            throw new Error('No operation');
        }

        const nodeName = operationSelect[0].factory.name;
        const topic = operationSelect[0].topic;
        const startTopic = `${topic}/start`;
        const operationName = operationSelect[0].name;

        return {
            nodeName,
            topic,
            startTopic,
            operationName,
        };
    }

    /**
     * Schedule a job
     * @param application
     * @param jobSchedule
     * @param tenantId
     */
    private static scheduleJob(application: Application, jobSchedule: JobSchedule, tenantId: string): void {
        const topic = jobSchedule.topic;

        /** In case of passed as a notification parameter
         * the stamp is converted as a string,
         * there we format back to datetime  */
        const startStamp =
            typeof jobSchedule.startStamp === 'string'
                ? datetime.parse(jobSchedule.startStamp)
                : jobSchedule.startStamp;
        const endStamp =
            typeof jobSchedule.endStamp === 'string' ? datetime.parse(jobSchedule.endStamp) : jobSchedule.endStamp;

        const scheduleParameters =
            jobSchedule.cronSchedule !== ''
                ? ({
                      rule: jobSchedule.cronSchedule,
                      start: startStamp?.toJsDate(),
                      end: endStamp?.toJsDate(),
                      tz: jobSchedule.timeZone,
                  } as cronSchedule.RecurrenceSpecDateRange)
                : startStamp?.toJsDate();

        if (!scheduleParameters) {
            logger.error('No scheduleParameters available');
            return;
        }

        logger.info(
            `Registering scheduled job to cron: ${tenantId}/${topic} [${JSON.stringify(
                jobSchedule.parameterValues,
            )}] [${JSON.stringify(scheduleParameters)}] `,
        );

        const task = cronSchedule.scheduleJob(scheduleParameters, () => {
            logger.info(
                `\nScheduled job started: ${tenantId}/${topic} [${JSON.stringify(jobSchedule.parameterValues)}] [${
                    jobSchedule.cronSchedule
                }][${jobSchedule.timeZone}]\n`,
            );
            application
                .withReadonlyContext(tenantId, context => this.executeAndCreateJob(context, jobSchedule), {
                    userEmail: jobSchedule.executionUser.email,
                    locale: jobSchedule.executionLocale,
                    description: () => `JobManager.executeAndCreateJob(${jobSchedule.scheduleId})`,
                    source: 'routing',
                    auth: { login: jobSchedule.login.email },
                })
                .catch(error => {
                    this.handleError(error, application, jobSchedule, tenantId, topic);
                });
        });
        this.scheduledJobs[tenantId].push({ ...jobSchedule, task });
    }

    /**
     * Schedule jobs for specified tenant
     * Using https://www.npmjs.com/package/node-cron)
     * @param application
     * @param tenantId
     */
    private static async scheduleTenantJobs(application: Application, tenantId: string): Promise<void> {
        logger.info(`Scheduling jobs for tenant: ${tenantId}`);
        this.scheduledJobs[tenantId] = [];

        await application.asRoot.withReadonlyContext(tenantId, async context => {
            await context
                .query(SysJobSchedule, { filter: { isActive: true } })
                .forEach(async jobSchedule =>
                    this.scheduleJob(application, await jobSchedule.getJobSchedulePayload(), tenantId),
                );
        });
    }

    /** Stops or interrupts a batch job */
    static async stopOrInterruptJob(
        jobExecution: xtremCommunication.nodes.SysNotificationState,
        status: xtremCommunication.enums.NotificationStatus, // must be stopRequested or interruptRequested
        reason: string,
    ): Promise<void> {
        await jobExecution.$.set({ status, timeEnded: datetime.now(), message: reason });
        await jobExecution.$.save();
        // TODO: add a log entry with the reason and notify the user
    }

    /** Interrupts all the batch jobs which are currently running */
    static async interruptAllJobs(context: Context, reason = 'unknown'): Promise<void> {
        const statuses: xtremCommunication.enums.NotificationStatus[] = ['pending', 'running', 'notResponding'];
        await context
            .query(xtremCommunication.nodes.SysNotificationState, {
                filter: { status: { _in: statuses } },
                forUpdate: true,
            })
            .forEach(jobExecution => this.stopOrInterruptJob(jobExecution, 'interruptRequested', reason));
    }

    /**
     * @internal
     * Stop all scheduled jobs and reset in memory list cron jobs
     */
    static cleanCronTasks(): void {
        logger.info('Cleaning all running jobs');

        // We could clear all tasks directly from the list of task from node-cron
        // but it would be safer to just stop the tasks in our list before clearing, as we may use node-cron in other places.
        Object.keys(this.scheduledJobs).forEach(tenantId => {
            this.scheduledJobs[tenantId].forEach(job => {
                if (job.task) job.task.cancel();
            });
        });

        this.scheduledJobs = {};
    }

    /**
     * @internal
     * Stop all scheduled jobs and reset in memory list cron jobs
     */
    static cronTaskInfos(): string[] {
        logger.info('Infos for cron task ');
        const scheduledJobs = cronSchedule.scheduledJobs;
        const jobs = Object.keys(cronSchedule.scheduledJobs).map(key => scheduledJobs[key]);

        return jobs.map(job => {
            const nextInvocation = job.nextInvocation();
            const info = `${job.name} ${nextInvocation} `;
            logger.info(info);
            return info;
        });
    }

    /**
     * Reset the individual schedule
     * @internal
     * @param application
     * @returns
     */
    static onScheduleReset(application: Application) {
        return (payload: JobSchedulePayload): void => {
            if (payload.tenantId) {
                const topic = payload.topic;
                logger.info(
                    `Resetting scheduled job: ${topic} [${JSON.stringify(payload.parameterValues)}] [${
                        payload.cronSchedule
                    }]`,
                );
                const cronJobIndex = (this.scheduledJobs[payload.tenantId] || []).findIndex(
                    job => job.scheduleId === payload.scheduleId,
                );
                if (cronJobIndex >= 0) {
                    this.scheduledJobs[payload.tenantId][cronJobIndex].task?.cancel();
                    this.scheduledJobs[payload.tenantId].splice(cronJobIndex, 1);
                }
                this.scheduleJob(application, payload, payload.tenantId);
            }
        };
    }

    /**
     * Stop the deleted job
     * @internal
     * @returns
     */
    static onScheduleDelete() {
        return (payload: JobSchedulePayload): void => {
            if (payload.tenantId) {
                logger.info(
                    `Deleting scheduled job: ${payload.tenantId}/${payload.topic} [${JSON.stringify(
                        payload.parameterValues,
                    )}] [${payload.cronSchedule}]`,
                );
                const cronJobIndex = (this.scheduledJobs[payload.tenantId] || []).findIndex(
                    job => job.scheduleId === payload.scheduleId,
                );
                if (cronJobIndex >= 0) {
                    this.scheduledJobs[payload.tenantId][cronJobIndex].task?.cancel();
                    this.scheduledJobs[payload.tenantId].splice(cronJobIndex, 1);
                }
            }
        };
    }

    /** start the scheduler service and deploy all cron jobs */
    static async start(application: Application): Promise<void> {
        logger.info('Starting scheduler service');
        this.cleanCronTasks();

        if (await PubSub.isSubscribed('job_schedule_reset')) await PubSub.unsubscribe('job_schedule_reset');
        await PubSub.subscribe('job_schedule_reset', this.onScheduleReset(application));

        if (await PubSub.isSubscribed('job_schedule_delete')) await PubSub.unsubscribe('job_schedule_delete');
        await PubSub.subscribe('job_schedule_delete', this.onScheduleDelete());

        const tenantIds = await application.asRoot.withReadonlyContext(null, context =>
            Context.tenantManager.listTenantsIds(context),
        );

        await asyncArray(tenantIds).forEach(tenantId => this.scheduleTenantJobs(application, tenantId));
    }

    // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
    static handleError(
        error: any,
        application: Application,
        jobSchedule: JobSchedule,
        tenantId: string,
        topic: string,
    ) {
        application.asRoot
            .withCommittedContext(
                tenantId,
                async context => {
                    logger.error(() => `scheduleJob Error: \n ${error}`);
                    await (
                        await context.create(xtremCommunication.nodes.SysNotificationState, {
                            operationName: topic.replace('/', '.') || '',
                            notificationId: nanoid(),
                            status: 'error',
                            message: error.message,
                            schedule: jobSchedule.scheduleId,
                            topic,
                            logs: [{ level: 'error', message: error.message }],
                        })
                    ).$.save();
                },
                { description: () => `JobManager.create SysNotificationState(${jobSchedule.scheduleId})` },
            )
            .catch(notifyError => {
                logger.error(
                    `Error: JobManager.create SysNotificationState(${jobSchedule.scheduleId}) - ${error}. Subsequently, send Error Notification failed : \n ${notifyError.stack}. `,
                );
            });
    }
}
