import { Context, Datetime, decorators, garbageCollectContentAddressableTables, Node } from '@sage/xtrem-core';

@decorators.node<SysChore>({
    isPublished: true,
})
export class SysChore extends Node {
    @decorators.asyncMutation<typeof SysChore, 'purgeContentAddressableTables'>({
        // This method trigges very high CPU usage. So we don't want to publish it nor schedule it yet.
        isPublished: true,
        isSchedulable: true,
        parameters: [],
        return: 'boolean',
        startsReadOnly: true,
    })
    static async purgeContentAddressableTables(context: Context): Promise<boolean> {
        context.logger.info(`Deleting orphan records in content-addressable tables`);
        await context.batch.logMessage(
            'info',
            `Garbage collection of content-addressable tables for tenant ${context.tenantId} starts: ${Datetime.now()}`,
        );
        await garbageCollectContentAddressableTables(context, context.tenantId ?? undefined);
        await context.batch.logMessage(
            'info',
            `Garbage collection of content-addressable tables for tenant ${context.tenantId} ends: ${Datetime.now()}`,
        );
        return true;
    }
}
