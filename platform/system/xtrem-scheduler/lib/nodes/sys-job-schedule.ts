import * as xtremCommunication from '@sage/xtrem-communication';
import {
    BusinessRuleError,
    Collection,
    Context,
    datetime,
    decorators,
    Node,
    PubSub,
    Reference,
    supportReadonlyUserEmail,
    supportUserEmail,
} from '@sage/xtrem-core';
import * as xtremMetadata from '@sage/xtrem-metadata';
import { Dict } from '@sage/xtrem-shared';
import * as xtremSystem from '@sage/xtrem-system';
import cronstrue from 'cronstrue';
import * as momentTimezone from 'moment-timezone';
import * as xtremScheduler from '..';
import { cronSchedule } from '../data-types/cron-schedule-data-type';
import { timezoneSchedule } from '../data-types/timezone-data-type';
import { JobManager } from '../services/job-manager';

const jobScheduleReset = 'SysJobSchedule/jobScheduleReset';
const jobScheduleDelete = 'SysJobSchedule/jobScheduleDelete';

@decorators.node<SysJobSchedule>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDeleteMany: true,
    canBulkDelete: true,
    canDelete: true,
    isSetupNode: true,
    indexes: [{ orderBy: { id: 1, executionUser: 1 }, isUnique: true, isNaturalKey: true }],
    async getFilters(context) {
        const currentUser = await context.user;
        return currentUser?.isAdministrator ||
            [supportReadonlyUserEmail, supportUserEmail].includes(currentUser?.email || '')
            ? []
            : [{ executionUser: currentUser?._id }];
    },
    async controlBegin(cx) {
        await xtremScheduler.events.controls.schedule.validateParameters(this, cx);
        await xtremScheduler.events.controls.schedule.missingParameters(this, cx);
        await xtremScheduler.events.controls.schedule.cronOrStartDate(this, cx);
    },
    async saveEnd() {
        const endStamp = await this.endStamp;
        const jobScheduleTopic =
            (await this.isActive) && (endStamp === null || datetime.now().compare(endStamp) < 0)
                ? jobScheduleReset
                : jobScheduleDelete;

        await this.$.context.notify(jobScheduleTopic, await this.getJobSchedulePayload());
    },
    async deleteEnd() {
        await this.$.context.notify(jobScheduleDelete, await this.getJobSchedulePayload());
    },
})
export class SysJobSchedule extends Node {
    async getJobSchedulePayload(): Promise<xtremScheduler.interfaces.JobSchedulePayload> {
        return {
            cronSchedule: await this.cronSchedule,
            executionUser: await (await this.executionUser).toUserInfo(),
            executionLocale: await this.executionLocale,
            topic: await (await this.operation).topic,
            operation: await this.operation,
            parameterValues: await this.parameterValues,
            scheduleId: this._id,
            tenantId: this.$.context.tenantId,
            timeZone: await this.timeZone,
            startStamp: await this.startStamp,
            endStamp: await this.endStamp,
            login: await ((await this.$.createdBy) as xtremSystem.nodes.User).toUserInfo(),
        };
    }

    @decorators.stringProperty<SysJobSchedule, 'id'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        dataType: () => xtremSystem.dataTypes.setupIdDataType,
        dependsOn: ['operation'],
        async defaultValue() {
            const operation = await this.operation;
            const count = await this.$.context.queryCount(SysJobSchedule, { filter: { operation } });

            const operationName = await operation.name;
            let i = 1;
            let id = `${operationName}_${count + i}`;

            const user = `#${(await this.$.context.user)?.email}`;
            while (await this.$.context.exists(SysJobSchedule, { id, executionUser: user })) {
                i += 1;
                id = `${operationName}_${count + i}`;
            }

            return id;
        },
    })
    readonly id: Promise<string>;

    @decorators.referenceProperty<SysJobSchedule, 'operation'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMetadata.nodes.MetaNodeOperation,
        async control(cx, val) {
            await xtremScheduler.events.controls.scheduleProperties.controlOperationSchedulable(cx, val);
        },
        filters: {
            lookup: {
                async serviceOptionNames() {
                    const activeServiceOptions = await this.$.context.activeServiceOptions;
                    const activeServiceOptionNames = activeServiceOptions.map(
                        activeServiceOption => activeServiceOption.name,
                    );
                    return { _containedBy: activeServiceOptionNames };
                },
            },
        },
    })
    readonly operation: Reference<xtremMetadata.nodes.MetaNodeOperation>;

    @decorators.stringProperty<SysJobSchedule, 'description'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.description,
    })
    readonly description: Promise<string>;

    @decorators.booleanProperty<SysJobSchedule, 'isActive'>({
        isStored: true,
        isPublished: true,
        defaultValue: true,
        provides: ['isActive'],
        isOwnedByCustomer: true,
    })
    readonly isActive: Promise<boolean>;

    @decorators.datetimeProperty<SysJobSchedule, 'startStamp'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        defaultValue: null,
    })
    readonly startStamp: Promise<datetime | null>;

    @decorators.datetimeProperty<SysJobSchedule, 'endStamp'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        defaultValue: null,
    })
    readonly endStamp: Promise<datetime | null>;

    @decorators.referenceProperty<SysJobSchedule, 'executionUser'>({
        isStored: true,
        isPublished: true,
        async isFrozen() {
            const currentUser = await this.$.context.user;
            if (currentUser?.isAdministrator) return false;
            return true;
        },
        async defaultValue() {
            return this.$.context.read(xtremSystem.nodes.User, { email: (await this.$.context.user)?.email });
        },
        node: () => xtremSystem.nodes.User,
        async control(cx, val) {
            const currentUser = await this.$.context.user;
            if (!currentUser?.isAdministrator && currentUser?._id !== val._id) {
                cx.error.addLocalized(
                    '@sage/xtrem-scheduler/nodes__sys-job-schedule__execution_user_not_authorized',
                    'You can only assign the execution user to yourself',
                );
            }
        },
    })
    readonly executionUser: Reference<xtremSystem.nodes.User>;

    @decorators.stringProperty<SysJobSchedule, 'executionLocale'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.localeDatatype,
        async isFrozen() {
            const currentUser = await this.$.context.user;
            if (currentUser?.isAdministrator) return false;
            return true;
        },
        defaultValue() {
            return this.$.context.currentLocale;
        },
    })
    readonly executionLocale: Promise<string>;

    @decorators.jsonProperty<SysJobSchedule, 'parameterValues'>({
        isStored: true,
        isPublished: true,
    })
    readonly parameterValues: Promise<Dict<string>>;

    /**
     * Cron schedule string
     * https://www.npmjs.com/package/node-cron)
         ┌────────────── second (optional)\
         │ ┌──────────── minute\
         │ │ ┌────────── hour\
         │ │ │ ┌──────── day of month\
         │ │ │ │ ┌────── month\
         │ │ │ │ │ ┌──── day of week\
         │ │ │ │ │ │\
         │ │ │ │ │ │\
        `* * * * * *`
     */
    @decorators.stringProperty<SysJobSchedule, 'cronSchedule'>({
        isStored: true,
        isPublished: true,
        dataType: () => cronSchedule,
        defaultValue: '',
    })
    readonly cronSchedule: Promise<string>;

    @decorators.stringProperty<SysJobSchedule, 'cronTranslated'>({
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.description,
        async computeValue() {
            const cronSring = await this.cronSchedule;
            if (cronSring === '') {
                return this.$.context.localize('@sage/xtrem-scheduler/nodes__sys-job-schedule__once', 'Once');
            }
            const user = await this.$.context.user;
            const locale = user?.locale?.substring(0, 2) || 'en';
            return cronstrue.toString(cronSring, { locale });
        },
    })
    readonly cronTranslated: Promise<string>;

    @decorators.datetimeProperty<SysJobSchedule, 'nextExecutionStamp'>({
        isPublished: true,
        isNullable: true,
        async computeValue() {
            const startStamp = await this.startStamp;
            const endStamp = await this.endStamp;
            const cronString = await this.cronSchedule;
            if (cronString === '') {
                if (!startStamp) {
                    throw new BusinessRuleError(
                        this.$.context.localize(
                            '@sage/xtrem-scheduler/nodes__sys-job-schedule__no-cron-expression-no-startStamp',
                            'You need a start datetime if cron expression is not define.',
                        ),
                    );
                }
                return startStamp;
            }
            const nextExecution = xtremScheduler.sharedFunctions.nextExecution({
                cronString,
                tz: await this.timeZone,
                ...(startStamp && { startDate: startStamp }),
                ...(endStamp && { endDate: endStamp }),
            });
            if (typeof nextExecution === 'number') {
                return null;
            }
            return nextExecution;
        },
    })
    readonly nextExecutionStamp: Promise<datetime | null>;

    @decorators.stringProperty<SysJobSchedule, 'timeZone'>({
        isStored: true,
        isPublished: true,
        dataType: () => timezoneSchedule,
    })
    readonly timeZone: Promise<string>;

    @decorators.collectionProperty<SysJobSchedule, 'executions'>({
        isPublished: true,
        node: () => xtremCommunication.nodes.SysNotificationState,
        getFilter() {
            return { schedule: this._id };
        },
    })
    readonly executions: Collection<xtremCommunication.nodes.SysNotificationState>;

    @decorators.mutation<typeof SysJobSchedule, 'purgeExecution'>({
        isPublished: true,
        parameters: [
            {
                name: 'schedule',
                type: 'reference',
                node: () => SysJobSchedule,
            },
            {
                name: 'allStatus',
                type: 'boolean',
                isMandatory: false,
            },
        ],
        return: 'integer',
    })
    static purgeExecution(context: Context, schedule: SysJobSchedule, allStatus = false): Promise<number> {
        const status: xtremCommunication.enums.NotificationStatus[] = allStatus
            ? ['error', 'interrupted', 'stopped', 'success']
            : ['success'];

        return context.deleteMany(xtremCommunication.nodes.SysNotificationState, {
            schedule,
            status: { _in: status },
        });
    }

    @decorators.query<typeof SysJobSchedule, 'timezones'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'array',
            item: {
                type: 'string',
            },
            isMandatory: true,
        },
    })
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    static timezones(_context: Context): string[] {
        const timezones = momentTimezone.tz.names();
        if (!timezones) return [];

        return timezones;
    }

    @decorators.query<typeof SysJobSchedule, 'cronInfo'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'array',
            item: {
                type: 'string',
            },
        },
        serviceOptions: () => [xtremSystem.serviceOptions.DevTools],
    })
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    static cronInfo(_context: Context): string[] {
        return JobManager.cronTaskInfos();
    }

    @decorators.notificationListener<typeof SysJobSchedule>({
        topic: 'SysJobSchedule/jobScheduleReset',
    })
    static async onScheduleReset(
        context: Context,
        payload: xtremScheduler.interfaces.JobSchedulePayload,
    ): Promise<void> {
        await PubSub.publish(context, 'job_schedule_reset', payload);
    }

    @decorators.notificationListener<typeof SysJobSchedule>({
        topic: 'SysJobSchedule/jobScheduleDelete',
    })
    static async onScheduleDelete(
        context: Context,
        payload: xtremScheduler.interfaces.JobSchedulePayload,
    ): Promise<void> {
        await PubSub.publish(context, 'job_schedule_delete', payload);
    }
}
