import { CoreHooks } from '@sage/xtrem-core';
import * as xtremMetadata from '@sage/xtrem-metadata';
import { JobManager } from '../services/job-manager';

Object.assign(CoreHooks.communicationManager, {
    async startAsyncMutation(context, factoryName, mutationName, parameterValues) {
        const operation = await context.read(xtremMetadata.nodes.MetaNodeOperation, {
            _id: `#${factoryName}|${mutationName}|start`,
        });
        const startTopic = `${await operation.topic}/start`;
        const trackingId = await context.notify(startTopic, parameterValues);

        await JobManager.createJob(
            context,
            {
                operation,
                parameterValues,
                topic: await operation.topic,
            },
            trackingId,
        );
        return trackingId;
    },
} as Partial<typeof CoreHooks.communicationManager>);
