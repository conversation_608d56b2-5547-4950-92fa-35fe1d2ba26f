@xtrem_authorization
Feature: smoke-test-static

    #Case without navigation panel full width
    Scenario Outline: std \ <Page> \ desktop \ check each page loads without any problems
        Given the user opens the application on a desktop using the following link: "<Page>"
        Then the "<Title>" titled page is displayed
        # Opening pages having extensions in the current package
        Examples:
            | Page                                           | Title                    |
            | @sage/xtrem-authorization/SupportAccessHistory | Support access           |
            | @sage/xtrem-authorization/User                 | Users                    |
            | @sage/xtrem-authorization/GroupRoleSiteList    | Authorization group list |
            | @sage/xtrem-authorization/RoleList             | Roles                    |
            | @sage/xtrem-authorization/UserList             | User list                |
            | @sage/xtrem-authorization/SiteGroupList        | Site groups              |
