{"data": {"xtremAuthorization": {"testLookupAccessParent": {"query": {"edges": [{"node": {"name": "Parent 1", "description": "P1 description", "noLookup": "P1 no lookup", "otherNoLookup": "P1 other no lookup", "lookupAccessReference": {"description": "R1 description", "name": "Reference 1", "noLookup": "R1 no lookup"}, "lookupAccessCollection": {"query": {"edges": [{"node": {"description": "P1 I1 description", "name": "P1 item 1", "noLookup": "P1 I1 no lookup"}}, {"node": {"description": "P1 I2 description", "name": "P1 item 2", "noLookup": "P1 I2 no lookup"}}, {"node": {"description": "P1 I3 description", "name": "P1 item 3", "noLookup": "P1 I3 no lookup"}}]}}}}]}}}}}