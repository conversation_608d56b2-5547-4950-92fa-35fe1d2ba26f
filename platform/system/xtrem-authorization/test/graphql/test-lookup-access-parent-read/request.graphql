{
    xtremAuthorization {
        testLookupAccessParent {
            query(filter: "{name: 'Parent 1'}") {
                edges {
                    node {
                        name
                        description
                        noLookup
                        otherNoLookup
                        lookupAccessReference {
                            name
                            description
                            noLookup
                        }
                        lookupAccessCollection {
                            query {
                                edges {
                                    node {
                                        name
                                        description
                                        noLookup
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
