mutation {
    xtremAuthorization {
        groupRoleSite {
            create(data:{ 
            {{#each properties}}
              {{#unless this.[0]}}
               {{@key}} : {{this}}
              {{else}}
               {{@key}} : "{{this}}"
              {{/unless}}
             {{/each}}
             {{#each arrayPropertiesRole}}
               {{@key}} :
               [
                {{#each this}}
                {
                 {{#each this}}
                  {{#unless this.[0]}}
                   {{@key}} : {{this}}
                  {{else}}
                   {{@key}} : "{{this}}"
                  {{/unless}}
                 {{/each}}
                }
                {{/each}}
               ]
             {{/each}}
             {{#each arrayPropertiesSite}}
               {{@key}} :
               [
                {{#each this}}
                {
                 {{#each this}}
                  {{#unless this.[0]}}
                   {{@key}} : {{this}}
                  {{else}}
                   {{@key}} : "{{this}}"
                  {{/unless}}
                 {{/each}}
                }
                {{/each}}
               ]
             {{/each}}
             })
             {
                id
            }
        }
    }
}
