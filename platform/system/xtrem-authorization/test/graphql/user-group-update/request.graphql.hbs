mutation {
    xtremSystem {
        user {
            update(data:{ 
            {{#each properties}}
              {{#unless this.[0]}}
               {{@key}} : {{this}}
              {{else}}
               {{@key}} : "{{this}}"
              {{/unless}}
             {{/each}}
             {{#each arrayProperties}}
               {{@key}} :
               [
                {{#each this}}
                {
                 {{#each this}}
                  {{#unless this.[0]}}
                   {{@key}} : {{this}}
                  {{else}}
                   {{@key}} : "{{this}}"
                  {{/unless}}
                 {{/each}}
                }
                {{/each}}
               ]
             {{/each}}
             })
             {
                email
                authorizationGroup {
                query {
                  edges {
                    node {
                      group {
                        name
                        groupRoles {
                          query {
                            edges {
                              node {
                                role {
                                  name
                                }
                              }
                            }
                          }
                        }
                        groupSites {
                          query {
                            edges {
                              node {
                                siteGroup {
                                  name
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
        }
    }
}
