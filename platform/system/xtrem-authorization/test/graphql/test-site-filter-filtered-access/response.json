{"data": {"xtremAuthorization": {"testSiteFilterNode": {"query": {"edges": [{"node": {"code": "SITE1R1", "site": {"id": "US001"}, "referencedSite": {"id": "US002"}}}, {"node": {"code": "SITE1R2A1", "site": {"id": "US001"}, "referencedSite": {"id": null}}}, {"node": {"code": "SITE2R1", "site": {"id": "US002"}, "referencedSite": {"id": "US001"}}}]}}}}, "errors": [{"extensions": {"code": "authorization-error"}, "locations": [{"column": 29, "line": 12}], "message": "Site: Unauthorized: id: US003", "path": ["xtremAuthorization", "testSiteFilterNode", "query", "edges", 1, "node", "referencedSite", "id"]}]}