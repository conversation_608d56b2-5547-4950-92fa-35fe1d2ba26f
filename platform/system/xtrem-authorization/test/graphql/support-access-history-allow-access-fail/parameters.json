{"Number parameter should be greater than 0": {"input": {"forTime": -1, "units": "minutes", "isReadOnlyAccess": true}, "output": {"errors": [{"message": "Allow access failed.", "locations": [{"line": 4, "column": 13}], "path": ["xtremAuthorization", "supportAccessHistory", "allowAccess"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "message": "The number must be greater than 0.", "path": []}]}}]}}, "There is already an open session": {"input": {"forTime": 2, "units": "minutes", "isReadOnlyAccess": true}, "envConfigs": {"now": "2022-10-28T10:12:30Z"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "There is already an open session. Contact your system administrator.", "path": [], "severity": 4}]}, "locations": [{"column": 13, "line": 4}], "message": "Allow access failed.", "path": ["xtremAuthorization", "supportAccessHistory", "allowAccess"]}]}}}