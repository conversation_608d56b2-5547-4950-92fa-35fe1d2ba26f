mutation {
    xtremAuthorization {
        supportAccessHistory {
            allowAccess(forTime: {{forTime}}, units: {{units}}, isReadOnlyAccess: {{isReadOnlyAccess}}) {
                _sourceId
                _id
                status
                startTime
                endTime
                isReadOnlyAccess
                _createStamp
                _updateStamp
            }
        }
    }
}
