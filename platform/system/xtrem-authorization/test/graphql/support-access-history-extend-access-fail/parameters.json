{"Number parameter should be greater than 0": {"input": {"forTime": -1, "units": "minutes"}, "output": {"errors": [{"message": "Extend access failed.", "locations": [{"line": 4, "column": 13}], "path": ["xtremAuthorization", "supportAccessHistory", "extendAccess"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "message": "The number must be greater than 0.", "path": []}]}}]}}, "There is already an open session": {"input": {"forTime": 2, "units": "minutes"}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "There is no open session to extend the access. Contact your system administrator.", "path": [], "severity": 4}]}, "locations": [{"column": 13, "line": 4}], "message": "Extend access failed.", "path": ["xtremAuthorization", "supportAccessHistory", "extendAccess"]}]}}}