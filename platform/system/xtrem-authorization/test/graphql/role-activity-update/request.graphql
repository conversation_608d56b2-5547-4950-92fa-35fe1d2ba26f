mutation {
    xtremAuthorization {
        roleActivity {
            roleActivitiesCreateUpdate(
                roleActivities: [
                    {
                        activity: "#dummyTest"
                        permissions: ["lookup", "read"]
                        isActive: true
                        hasAllPermissions: false
                    }
                    {
                        activity: "#dummyTest2"
                        permissions: ["lookup", "read", "create"]
                        isActive: true
                        hasAllPermissions: false
                    }
                    {
                        activity: "#dummyTest3"
                        permissions: ["lookup", "read", "create", "update"]
                        isActive: true
                        hasAllPermissions: false
                    }
                    {
                        activity: "#dummyTest4"
                        permissions: ["lookup", "read", "create", "update", "delete"]
                        isActive: false
                        hasAllPermissions: false
                    }
                ]
                roleSysId: "#009"
            )
        }
    }
}
