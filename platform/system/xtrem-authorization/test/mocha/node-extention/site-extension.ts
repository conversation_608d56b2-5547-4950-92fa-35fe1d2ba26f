import { Test } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremAuthorization from '../../../index';

describe('Delete site', () => {
    before(() => {});
    it('Delete site and make sure reference in siteGroupToSite is also deleted', () =>
        Test.withContext(async context => {
            const site = await context.read(xtremSystem.nodes.Site, { id: 'US007' }, { forUpdate: true });
            const siteGroupToSites = await context
                .query(xtremAuthorization.nodes.SiteGroupToSite, {
                    filter: { site: site._id },
                })
                .toArray();
            assert.equal(siteGroupToSites.length, 1);
            const siteId = site._id;

            await site.$.delete();

            assert.isFalse(await context.exists(xtremSystem.nodes.Site, { id: 'US007' }));
            assert.isFalse(await context.exists(xtremAuthorization.nodes.SiteGroupToSite, { site: siteId }));
        }));
});
