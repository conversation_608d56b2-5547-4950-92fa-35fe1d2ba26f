import { Test } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremAuthorization from '../../../index';

describe('User extension', () => {
    before(() => {});
    it('Setting and removing user billing role', () =>
        Test.withContext(async context => {
            const user = await context.read(
                xtremSystem.nodes.User,
                { email: '<EMAIL>' },
                { forUpdate: true },
            );

            // Confirm no billing role exists
            assert.isFalse(await context.exists(xtremAuthorization.nodes.UserBillingRole, { user: user._id }));

            // Get billing role
            const billingRole = await context.read(xtremAuthorization.nodes.Role, { id: 'Test billing role' });
            const newUserBillingRole = await context.create(xtremAuthorization.nodes.UserBillingRole, {
                user: user._id,
                role: billingRole,
            });

            await user.$.set({ billingRole: newUserBillingRole });
            await user.$.save();

            // Confirm billing role exists
            assert.isTrue(await context.exists(xtremAuthorization.nodes.UserBillingRole, { user: user._id }));

            const userAfter = await context.read(xtremSystem.nodes.User, { email: '<EMAIL>' });

            assert.isNotNull(userAfter);
            assert.isNotNull(await userAfter.billingRole);
            if (userAfter && userAfter.billingRole) {
                assert.equal((await (await userAfter.billingRole).role)._id, billingRole._id);
            }

            // Set billing role to no Role should delete UserBillingRole
            await user.$.set({ billingRole: null });
            await user.$.save();

            assert.isFalse(await context.exists(xtremAuthorization.nodes.UserBillingRole, { user: user._id }));
        }));
});
