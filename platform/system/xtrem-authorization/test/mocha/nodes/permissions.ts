import { AccessRights, asyncArray, Context, Dict, Test, UserAccess } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as _ from 'lodash';
import { Role } from '../../../lib/nodes';
import { authorizationServiceOption } from '../../../lib/service-options/authorization-service-option';
import { SysAccessRightsManager } from '../../../lib/services/access-rights-manager';
import { dummyTest2, dummyTest4 } from '../../fixtures/activities';

// Use this to debug missing / wrong keys when adding / changing permissions
// const logger = Logger.getLogger(__filename, 'authorization');

const compareSites = async (context: Context, siteIds: (string | number)[], expected: string[], message?: string) => {
    const sites = await context
        .query(xtremSystem.nodes.Site, { filter: { _id: { _in: siteIds || [] } } })
        .map(s => s.id)
        .toArray();
    assert.deepEqual(sites, expected, message);
};

/**
 * Example:
 * Role = TEST_ACCOUNTANT
 * Role Activities
 *  "dummyTest"
        "read", "lookup", "lookup" on TestActivityNode
    "dummyTest2"
        "lookup","read","create","test" on TestActivityNode2
    "dummyTest'US003'"
        "lookup","read","create","update"  on TestActivityNode3
    "dummyTest'US004'" is inactive, so no access to TestActivityNode4
 */
const accountantRole: Dict<string[]> = {
    lookup: ['TestActivityNode', 'TestActivityNode2', 'TestActivityNode3'],
    read: ['TestActivityNode', 'TestActivityNode2', 'TestActivityNode3'],
    create: ['TestActivityNode2', 'TestActivityNode3'],
    update: ['TestActivityNode3'],
    test: ['TestActivityNode2'],
};

const cfoRole: Dict<string[]> = {
    lookup: ['TestActivityNode', 'TestActivityNode2', 'TestActivityNode3', 'TestSiteFilterNode'],
    read: ['TestActivityNode', 'TestActivityNode2', 'TestActivityNode3'],
    create: ['TestActivityNode', 'TestActivityNode2', 'TestActivityNode3'],
    update: ['TestActivityNode', 'TestActivityNode3'],
    delete: ['TestActivityNode'],
    test: ['TestActivityNode2'],
};

const procurementManagerRole: Dict<string[]> = {
    read: ['TestActivityNode', 'TestActivityNode2', 'TestActivityNode3'],
    lookup: ['TestActivityNode', 'TestActivityNode2', 'TestActivityNode3', 'TestSiteFilterNode'],
};

const expectedOperationAccess: Dict<Dict<string[]>> = {
    'SysTenant.getTenantInformation': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SysTenant.resetTenantDocuments': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SysCustomer.lookup': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SysDeviceToken.create': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SysDeviceToken.delete': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SysDeviceToken.lookup': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SysDeviceToken.read': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SysDeviceToken.update': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SysNotificationHistory.all': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'Role.read': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'RoleToRole.read': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'RoleActivity.read': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'User.read': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'User.sendBulkWelcomeMail': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'UserGroup.read': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'UserPreferences.read': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'UserNavigation.read': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SiteGroup.read': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SiteGroupToSite.read': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SiteGroupToSiteGroup.read': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'GroupRoleSite.read': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'GroupRole.read': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'GroupSite.read': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'Role.all': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'User.all': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'Role.lookup': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'RoleToRole.lookup': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'RoleActivity.lookup': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SysVendor.lookup': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'Activity.lookup': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'User.lookup': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'UserGroup.lookup': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'UserPreferences.lookup': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'UserNavigation.lookup': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'GroupRoleSite.lookup': {
        Admin: [],
        TESTONESITE: ['UnitTestActive'],
        Support_Access: [],
        Support_Access_Read_Only: [],
    },
    'GroupRole.lookup': {
        Admin: [],
        TESTONESITE: ['UnitTestActive'],
        Support_Access: [],
        Support_Access_Read_Only: [],
    },
    'GroupSite.lookup': {
        Admin: [],
        TESTONESITE: ['UnitTestActive'],
        Support_Access: [],
        Support_Access_Read_Only: [],
    },
    'SiteGroup.lookup': {
        Admin: [],
        TESTONESITE: ['UnitTestActive'],
        Support_Access: [],
        Support_Access_Read_Only: [],
    },
    'SiteGroupToSite.lookup': {
        Admin: [],
        TESTONESITE: ['UnitTestActive'],
        Support_Access: [],
        Support_Access_Read_Only: [],
    },
    'SiteGroupToSiteGroup.lookup': {
        Admin: [],
        TESTONESITE: ['UnitTestActive'],
        Support_Access: [],
        Support_Access_Read_Only: [],
    },
    'Site.lookup': {
        Admin: [],
        TESTONESITE: ['UnitTestActive'],
        TEST2: ['US001', 'US002'],
        Support_Access: [],
        Support_Access_Read_Only: [],
    },
    'Site.read': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'Site.create': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'Site.update': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'Site.delete': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'Role.create': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'RoleToRole.create': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'RoleActivity.create': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'User.create': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'UserGroup.create': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'UserPreferences.create': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'UserNavigation.create': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SiteGroup.create': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SiteGroupToSite.create': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SiteGroupToSiteGroup.create': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'GroupRoleSite.create': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'GroupRole.create': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'GroupSite.create': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'Role.update': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'RoleToRole.update': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'RoleActivity.update': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'User.update': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'UserGroup.update': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'UserPreferences.update': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'UserNavigation.update': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SiteGroup.update': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SiteGroupToSite.update': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SiteGroupToSiteGroup.update': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'GroupRoleSite.update': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'GroupRole.update': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'GroupSite.update': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'Role.delete': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'RoleToRole.delete': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'RoleActivity.delete': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SiteGroup.delete': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SiteGroupToSite.delete': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SiteGroupToSiteGroup.delete': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'GroupRoleSite.delete': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'GroupRole.delete': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'GroupSite.delete': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'RoleActivity.roleActivitiesCreateUpdate': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SupportAccessHistory.checkSupportAccessOpen': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'TestActivityNode.read': {
        MANUF: ['US012', 'US013'],
        SALES: ['US001', 'US002', 'US003', 'US004', 'US005', 'US006', 'US007'],
        TEST1: [],
        TEST2: ['US001', 'US002'],
    },
    'TestActivityNode.lookup': {
        MANUF: ['US012', 'US013'],
        SALES: ['US001', 'US002', 'US003', 'US004', 'US005', 'US006', 'US007'],
        TEST1: [],
        TEST2: ['US001', 'US002'],
    },
    'TestActivityNode2.read': {
        MANUF: ['US012', 'US013'],
        SALES: ['US001', 'US002', 'US003', 'US004', 'US005', 'US006', 'US007'],
        TEST1: [],
        TEST2: ['US001', 'US002'],
    },
    'TestActivityNode3.read': {
        MANUF: ['US012', 'US013'],
        SALES: ['US001', 'US002', 'US003', 'US004', 'US005', 'US006', 'US007'],
        TEST2: ['US001', 'US002'],
    },
    'TestActivityNode2.lookup': {
        MANUF: ['US012', 'US013'],
        SALES: ['US001', 'US002', 'US003', 'US004', 'US005', 'US006', 'US007'],
        TEST1: [],
        TEST2: ['US001', 'US002'],
    },
    'TestActivityNode3.lookup': {
        MANUF: ['US012', 'US013'],
        SALES: ['US001', 'US002', 'US003', 'US004', 'US005', 'US006', 'US007'],
        TEST2: ['US001', 'US002'],
    },
    'TestSiteFilterNode.lookup': { MANUF: ['US012', 'US013'], TEST2: ['US001', 'US002'] },
    'TestActivityNode.create': { MANUF: ['US012', 'US013'], TEST2: ['US001', 'US002'] },
    'TestActivityNode2.create': {
        MANUF: ['US012', 'US013'],
        SALES: ['US001', 'US002', 'US003', 'US004', 'US005', 'US006', 'US007'],
        TEST1: [],
        TEST2: ['US001', 'US002'],
    },
    'TestActivityNode.update': { MANUF: ['US012', 'US013'], TEST2: ['US001', 'US002'] },
    'TestActivityNode.delete': { MANUF: ['US012', 'US013'], TEST2: ['US001', 'US002'] },
    'TestActivityNode4.read': {
        SALES: ['US001', 'US002', 'US003', 'US004', 'US005', 'US006', 'US007'],
        TEST2: ['US001', 'US002'],
    },
    'TestActivityNode2.test': { SALES: ['US001', 'US002', 'US003', 'US004', 'US005', 'US006', 'US007'], TEST1: [] },
    'TestActivityNode4.lookup': {
        SALES: ['US001', 'US002', 'US003', 'US004', 'US005', 'US006', 'US007'],
        TEST2: ['US001', 'US002'],
    },
    'TestActivityNode3.create': {
        SALES: ['US001', 'US002', 'US003', 'US004', 'US005', 'US006', 'US007'],
        TEST2: ['US001', 'US002'],
    },
    'TestActivityNode4.create': {
        SALES: ['US001', 'US002', 'US003', 'US004', 'US005', 'US006', 'US007'],
        TEST2: ['US001', 'US002'],
    },
    'TestActivityNode3.update': {
        SALES: ['US001', 'US002', 'US003', 'US004', 'US005', 'US006', 'US007'],
        TEST2: ['US001', 'US002'],
    },
    'TestActivityNode4.update': {
        SALES: ['US001', 'US002', 'US003', 'US004', 'US005', 'US006', 'US007'],
        TEST2: ['US001', 'US002'],
    },
    'TestActivityNode4.delete': {
        SALES: ['US001', 'US002', 'US003', 'US004', 'US005', 'US006', 'US007'],
        TEST2: ['US001', 'US002'],
    },
    'TestParentNode.lookup': { TESTLOOKUPREAD: [] },
    'TestParentNode.read': { TESTLOOKUPREAD: [] },
    'TestChildNode.lookup': { TESTLOOKUPREAD: [] },
    'TestChildNode.read': { TESTLOOKUPREAD: [] },
    'TestLookupAccessNoRead.lookup': { TESTLOOKUP: [] },
    'TestLookupAccessParent.lookup': { TESTLOOKUP: [], TESTLOOKUPREAD: [] },
    'TestLookupAccessParent.read': { TESTLOOKUPREAD: [] },
    'TestLookupAccessVitalCollection.lookup': { TESTLOOKUP: [], TESTLOOKUPREAD: [] },
    'TestLookupAccessVitalCollection.read': { TESTLOOKUPREAD: [] },
    'TestLookupAccessVitalReference.lookup': { TESTLOOKUP: [], TESTLOOKUPREAD: [] },
    'TestLookupAccessVitalReference.read': { TESTLOOKUPREAD: [] },
    'TestSiteFilterNode.read': { TEST2: ['US001', 'US002'] },
    'RestrictedNodeUserGrant.lookup': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SupportAccessHistory.allowAccess': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SupportAccessHistory.extendAccess': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SupportAccessHistory.lookup': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SupportAccessHistory.read': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SysNotificationHistory.lookup': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SysNotificationHistory.read': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SupportAccessHistory.revokeAccess': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'Company.create': { Admin: [], Support_Access: [] },
    'Company.delete': { Admin: [], Support_Access: [] },
    'Company.lookup': { Admin: [], TESTONESITE: ['UnitTestActive'], Support_Access: [], Support_Access_Read_Only: [] },
    'Company.read': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'Company.update': { Admin: [], Support_Access: [] },
    'SysClientUserSettings.create': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SysClientUserSettings.lookup': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SysClientUserSettings.read': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SysClientUserSettings.update': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SysNotificationState.read': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SysNotificationLogEntry.read': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SysNotificationState.lookup': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SysNotificationLogEntry.lookup': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'UserBillingRole.create': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'UserBillingRole.lookup': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'UserBillingRole.read': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'UserBillingRole.update': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SysNotificationLogEntry.create': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SysNotificationLogEntry.delete': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SysNotificationLogEntry.update': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SysNotificationState.create': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SysNotificationState.delete': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SysNotificationState.update': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SysServiceOption.lookup': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SysServiceOptionState.lookup': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SysServiceOptionState.read': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SysServiceOptionState.update': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SysServiceOptionToServiceOption.lookup': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SysTag.create': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SysTag.delete': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SysTag.lookup': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SysTag.read': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'SysTag.update': { Admin: [], Support_Access: [], Support_Access_Read_Only: [] },
    'TestSiteReference.create': {
        Admin: [],
        TESTONESITE: ['UnitTestActive'],
        Support_Access: [],
    },
    'TestSiteReference.lookup': {
        Admin: [],
        TESTONESITE: ['UnitTestActive'],
        Support_Access: [],
        Support_Access_Read_Only: [],
    },
    'TestSiteReference.read': {
        Admin: [],
        TESTONESITE: ['UnitTestActive'],
        Support_Access: [],
        Support_Access_Read_Only: [],
    },
    'TestSiteReference.update': {
        Admin: [],
        TESTONESITE: ['UnitTestActive'],
        Support_Access: [],
    },
    'TestSiteReference.delete': {
        Admin: [],
        TESTONESITE: ['UnitTestActive'],
        Support_Access: [],
    },
};

const expectedUserAccessDonaldDuck: Dict<string[]> = {
    'Site.lookup': ['US001', 'US002'],
    'TestActivityNode.lookup': [],
    'TestActivityNode4.lookup': ['US001', 'US002'],
    'TestActivityNode.read': [],
    'TestActivityNode2.lookup': [],
    'TestActivityNode2.read': [],
    'TestActivityNode3.lookup': ['US001', 'US002'],
    'TestActivityNode3.read': ['US001', 'US002'],
    'TestActivityNode4.read': ['US001', 'US002'],
    'TestActivityNode.create': ['US001', 'US002'],
    'TestActivityNode2.create': [],
    'TestActivityNode3.create': ['US001', 'US002'],
    'TestActivityNode4.create': ['US001', 'US002'],
    'TestActivityNode.update': ['US001', 'US002'],
    'TestActivityNode3.update': ['US001', 'US002'],
    'TestActivityNode4.update': ['US001', 'US002'],
    'TestActivityNode.delete': ['US001', 'US002'],
    'TestActivityNode4.delete': ['US001', 'US002'],
    'TestSiteFilterNode.read': ['US001', 'US002'],
    'TestSiteFilterNode.lookup': ['US001', 'US002'],
    'TestActivityNode2.test': [],
};

const expectedUserAccessJaneDoe: Dict<string[]> = {
    'Site.lookup': ['US001', 'US002'],
    'TestActivityNode.lookup': ['US001', 'US002'],
    'TestActivityNode4.lookup': ['US001', 'US002'],
    'TestActivityNode.read': ['US001', 'US002'],
    'TestActivityNode2.read': ['US001', 'US002'],
    'TestActivityNode2.lookup': ['US001', 'US002'],
    'TestActivityNode3.read': ['US001', 'US002'],
    'TestActivityNode3.lookup': ['US001', 'US002'],
    'TestActivityNode4.read': ['US001', 'US002'],
    'TestActivityNode.create': ['US001', 'US002'],
    'TestActivityNode2.create': ['US001', 'US002'],
    'TestActivityNode3.create': ['US001', 'US002'],
    'TestActivityNode4.create': ['US001', 'US002'],
    'TestActivityNode.update': ['US001', 'US002'],
    'TestActivityNode3.update': ['US001', 'US002'],
    'TestActivityNode4.update': ['US001', 'US002'],
    'TestActivityNode.delete': ['US001', 'US002'],
    'TestActivityNode4.delete': ['US001', 'US002'],
    'TestSiteFilterNode.read': ['US001', 'US002'],
    'TestSiteFilterNode.lookup': ['US001', 'US002'],
};

const expectedUserAccessBillingRole: Dict<string[]> = {
    'Site.lookup': ['US001', 'US002'],
    'TestActivityNode.lookup': [],
    'TestActivityNode.read': [],
    'TestActivityNode.create': ['US001', 'US002'],
    'TestActivityNode.update': ['US001', 'US002'],
    'TestSiteFilterNode.read': ['US001', 'US002'],
    'TestSiteFilterNode.lookup': ['US001', 'US002'],
};

describe('Role permissions', () => {
    it('Get Role permissions', () =>
        Test.withContext(async context => {
            const role = await context.read(Role, { _id: '#001' });
            const permissions = await SysAccessRightsManager.getRoleFlattenedPermissions(context, role);
            assert.hasAllDeepKeys(permissions, accountantRole);
            Object.keys(permissions).forEach(key => {
                assert.sameMembers(
                    permissions[key],
                    accountantRole[key],
                    `Member mismatch ${key}(${permissions[key]} vs. ${accountantRole[key]})`,
                );
            });
        }));

    it('Permissions Role 2 - Includes Role 1 (Activity with grants - all permissions)', () =>
        Test.withContext(async context => {
            const role = await context.read(Role, { _id: '#002' });
            const permissions = await SysAccessRightsManager.getRoleFlattenedPermissions(context, role);
            assert.hasAllDeepKeys(permissions, cfoRole);
            Object.keys(permissions).forEach(key => {
                assert.sameMembers(permissions[key], cfoRole[key], `Grant mismatch on ${key}`);
            });
        }));

    it('Get Role permissions - Role 3 (Activity with grants - limited permissions)', () =>
        Test.withContext(async context => {
            const role = await context.read(Role, { _id: '#003' });
            const permissions = await SysAccessRightsManager.getRoleFlattenedPermissions(context, role);
            assert.hasAllDeepKeys(permissions, procurementManagerRole);
            Object.keys(permissions).forEach(key => {
                assert.sameMembers(permissions[key], procurementManagerRole[key]);
            });
        }));
});

describe('Group permissions', () => {
    it('Get operations for all Groups (GroupRoleSite)', () =>
        Test.withContext(async context => {
            const allOperationAccess = await SysAccessRightsManager.getAllOperationAccess(context);
            assert.hasAllDeepKeys(allOperationAccess, expectedOperationAccess);
            await asyncArray(Object.keys(allOperationAccess)).forEach(async key => {
                // Use this to debug missing / wrong keys when adding / changing permissions
                // console.log(`key: ${key}`);
                // console.log(`Actual: ${JSON.stringify(allOperationAccess[key])}`);

                assert.isDefined(expectedOperationAccess[key]);

                await asyncArray(Object.keys(allOperationAccess[key])).forEach(async key2 => {
                    const expected = expectedOperationAccess[key][key2];
                    const siteIds = allOperationAccess[key][key2];
                    await compareSites(context, siteIds, expected, `Site mismatch ${key}->${key2}->${siteIds}`);
                });
            });
        }));
});

describe('User group permissions', () => {
    it('Check access for Donald Duck', () =>
        Test.withContext(
            async context => {
                const allUserAccess = await SysAccessRightsManager.getUserAccess(context);
                assert.hasAllDeepKeys(allUserAccess, expectedUserAccessDonaldDuck);
                await asyncArray(Object.keys(allUserAccess)).forEach(async key => {
                    // Use this to debug missing / wrong keys when adding / changing permissions
                    // logger.debug(() => `key: ${key}`);
                    // logger.debug(() => `Actual: ${JSON.stringify(allUserAccess[key])}`);

                    await compareSites(
                        context,
                        allUserAccess[key],
                        expectedUserAccessDonaldDuck[key],
                        `Site mismatch ${key}(${JSON.stringify(allUserAccess[key])} vs. ${JSON.stringify(
                            expectedUserAccessDonaldDuck[key],
                        )})`,
                    );
                });
            },
            {
                user: { email: '<EMAIL>' },
                testActiveServiceOptions: [authorizationServiceOption],
            },
        ));

    it('Check access for Jane Doe', () =>
        Test.withContext(
            async context => {
                const allUserAccess = await SysAccessRightsManager.getUserAccess(context);
                assert.hasAllDeepKeys(allUserAccess, expectedUserAccessJaneDoe);
                await asyncArray(Object.keys(allUserAccess)).forEach(async key => {
                    // Use this to debug missing / wrong keys when adding / changing permissions
                    // logger.debug(() => `key: ${key}`);
                    // logger.debug(() => `Actual: ${JSON.stringify(allUserAccess[key])}`);
                    await compareSites(
                        context,
                        allUserAccess[key],
                        expectedUserAccessJaneDoe[key],
                        `Site mismatch ${key}(${JSON.stringify(allUserAccess[key])} vs. ${JSON.stringify(
                            expectedUserAccessDonaldDuck[key],
                        )})`,
                    );
                });
            },
            {
                user: { email: '<EMAIL>' },
                testActiveServiceOptions: [authorizationServiceOption],
            },
        ));

    it('Current user should have full access to TestActivityNode2.create', async () => {
        const checkNode = 'TestActivityNode2';
        const checkOperation = 'create';
        await Test.withContext(
            async context => {
                const userAccess = await Context.accessRightsManager.getUserAccessFor(
                    context,
                    checkNode,
                    checkOperation,
                );
                assert.deepEqual(userAccess, { sites: null, accessCodes: null, status: 'authorized' });
            },
            {
                user: { email: '<EMAIL>' },
                testActiveServiceOptions: [authorizationServiceOption],
            },
        );
    });

    it('Current user should have access to TestActivityNode2.asyncExport by inheriting read access', async () => {
        const checkNode = 'TestActivityNode2';
        const checkOperation = 'asyncExport';
        await Test.withContext(
            async context => {
                const userAccess = await Context.accessRightsManager.getUserAccessFor(
                    context,
                    checkNode,
                    checkOperation,
                );
                assert.deepEqual(userAccess, { sites: null, accessCodes: null, status: 'authorized' });
            },
            {
                user: { email: '<EMAIL>' },
                testActiveServiceOptions: [authorizationServiceOption],
            },
        );
    });

    it('Current user should have limited access to TestActivityNode4.delete', async () => {
        const checkNode = 'TestActivityNode4';
        const checkOperation = 'delete';
        await Test.withContext(
            async context => {
                const userAccess = await Context.accessRightsManager.getUserAccessFor(
                    context,
                    checkNode,
                    checkOperation,
                );

                await compareSites(context, userAccess.sites || [], ['US001', 'US002']);
                assert.deepEqual(_.omit(userAccess, ['sites']), { accessCodes: null, status: 'authorized' });
            },
            {
                user: { email: '<EMAIL>' },
                testActiveServiceOptions: [authorizationServiceOption],
            },
        );
    });

    it('Current user should not have access to TestActivityNode.delete', async () => {
        const checkNode = 'TestActivityNode2';
        const checkOperation = 'delete';
        await Test.withContext(
            async context => {
                const userAccess = await Context.accessRightsManager.getUserAccessFor(
                    context,
                    checkNode,
                    checkOperation,
                );
                assert.equal(userAccess.status, 'unauthorized');
            },
            {
                user: { email: '<EMAIL>' },
                testActiveServiceOptions: [authorizationServiceOption],
            },
        );
    });

    it('Check isAuthorized should fail', () =>
        Test.withContext(
            async context => {
                assert.equal(await context.isAuthorized(dummyTest2, 'update'), false);
            },
            {
                user: { email: '<EMAIL>' },
                testActiveServiceOptions: [authorizationServiceOption],
            },
        ));

    it('Check isAuthorized should fail', () =>
        Test.withContext(
            async context => {
                assert.equal(await context.isAuthorized(dummyTest2, 'delete'), false);
            },
            {
                user: { email: '<EMAIL>' },
                testActiveServiceOptions: [authorizationServiceOption],
            },
        ));

    it('Check isAuthorized should pass', () =>
        Test.withContext(
            async context => {
                assert.equal(await context.isAuthorized(dummyTest4, 'delete'), true);
            },
            {
                user: { email: '<EMAIL>' },
                testActiveServiceOptions: [authorizationServiceOption],
            },
        ));
});

describe('Check billing role permissions', () => {
    it('Check access for user Billing Role', () =>
        Test.withContext(
            async context => {
                const allUserAccess = await SysAccessRightsManager.getUserAccess(context);
                assert.hasAllDeepKeys(allUserAccess, expectedUserAccessBillingRole);
                await asyncArray(Object.keys(allUserAccess)).forEach(async key => {
                    // Use this to debug missing / wrong keys when adding / changing permissions
                    // logger.debug(() => `key: ${key}`);
                    // logger.debug(() => `Actual: ${JSON.stringify(allUserAccess[key])}`);

                    await compareSites(
                        context,
                        allUserAccess[key],
                        expectedUserAccessBillingRole[key],
                        `Site mismatch ${key}(${JSON.stringify(allUserAccess[key])} vs. ${JSON.stringify(
                            expectedUserAccessBillingRole[key],
                        )})`,
                    );
                });
            },
            {
                user: { email: '<EMAIL>' },
                testActiveServiceOptions: [authorizationServiceOption],
            },
        ));
});

describe('Attachment permissions', () => {
    const operationsToCheck: {
        operation: string;
        expected: UserAccess;
    }[] = [
        { operation: 'create', expected: { sites: null, accessCodes: null, status: 'authorized' } },
        { operation: 'read', expected: { sites: [], accessCodes: [], status: 'authorized' } },
        { operation: 'update', expected: { sites: null, accessCodes: null, status: 'authorized' } },
        { operation: 'delete', expected: { sites: [], accessCodes: [], status: 'unauthorized' } },
        { operation: 'asyncExport', expected: { sites: [], accessCodes: [], status: 'unauthorized' } },
    ];

    operationsToCheck.forEach(operationToCheck => {
        it(`can perform ${operationToCheck.operation} on TestAttachment node if user has ${operationToCheck.operation} on a node which has attachments`, () =>
            Test.withContext(
                async context => {
                    const nodeName = 'TestAttachment';

                    if (operationToCheck.expected.status === 'unauthorized') {
                        await assert.isRejected(
                            AccessRights.checkOperationAccessRight(context, {
                                nodeName,
                                operationName: operationToCheck.operation,
                            }),
                            `You cannot perform this operation TestAttachment.${operationToCheck.operation}`,
                        );
                    } else {
                        await AccessRights.checkOperationAccessRight(context, {
                            nodeName,
                            operationName: operationToCheck.operation,
                        });
                    }
                },
                {
                    user: { email: '<EMAIL>' },
                    testActiveServiceOptions: [authorizationServiceOption],
                },
            ));
    });

    it(`allows user access to _attachment collection`, () =>
        Test.withContext(
            async context => {
                const checkNode = 'TestActivityNode2';
                const userAccess = await Context.accessRightsManager.getUserAccessFor(
                    context,
                    checkNode,
                    '_attachments',
                );
                assert.deepEqual(userAccess, { sites: null, accessCodes: null, status: 'authorized' });
            },
            {
                user: { email: '<EMAIL>' },
                testActiveServiceOptions: [authorizationServiceOption],
            },
        ));
    it(`allows user access to _notes collection`, () =>
        Test.withContext(
            async context => {
                const checkNode = 'TestActivityNode6';
                const userAccess = await Context.accessRightsManager.getUserAccessFor(context, checkNode, '_notes');
                assert.deepEqual(userAccess, { sites: null, accessCodes: null, status: 'authorized' });
            },
            {
                user: { email: '<EMAIL>' },
            },
        ));
});
