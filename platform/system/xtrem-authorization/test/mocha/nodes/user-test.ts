import {
    adminDemoPersona,
    AnyValue,
    AsyncResponse,
    Context,
    Cookie,
    CookieO<PERSON>s,
    Dict,
    NodeCreateData,
    NodeStatus,
    Test,
    UserData,
    UserTestOptions,
    ValidationSeverity,
} from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import { GroupRoleSite, Role, UserBillingRole, UserGroup } from '../../../lib/nodes';

import * as sinon from 'sinon';

const sandbox = sinon.createSandbox();

const defaultTenantCookieName = `xtrem_${Test.defaultTenantId}_persona`;

function withUserContext<T extends AnyValue>(
    body: (context: Context) => AsyncResponse<T>,
    options: UserTestOptions,
): Promise<T> {
    return Test.withUserContext(context => body(context), options, [xtremSystem.serviceOptions.isDemoTenant]);
}

describe('User node', () => {
    const adminUser: UserData = {
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Roe',
        locale: 'en-US',
    };

    before(() => Test.application.createAdminUser(`${Test.defaultTenantId}`, adminUser, { isFirstAdminUser: true }));

    after(() => {
        return Test.application.asRoot.withCommittedContext(Test.defaultTenantId, async context => {
            const user = await context.tryRead(xtremSystem.nodes.User, { email: adminUser.email }, { forUpdate: true });
            if (user) {
                await user.$.delete();
            }
        });
    });

    let userCreatedSpy: sinon.SinonSpy<
        [context: Context, options: xtremSystem.UserCreateModifyOptions],
        Promise<string>
    >;
    let userModifiedSpy: sinon.SinonSpy<
        [context: Context, options: xtremSystem.UserCreateModifyOptions],
        Promise<string>
    >;
    let userDeletedSpy: sinon.SinonSpy<[context: Context, oprions: xtremSystem.UserDeleteOptions], Promise<string>>;

    beforeEach(() => {
        userCreatedSpy = sandbox.spy(xtremSystem.UserEventsManager, 'userCreated');
        userModifiedSpy = sandbox.spy(xtremSystem.UserEventsManager, 'userModified');
        userDeletedSpy = sandbox.spy(xtremSystem.UserEventsManager, 'userDeleted');
    });

    afterEach(() => {
        sandbox.restore();
    });

    it('Can create user node', () =>
        Test.withContext(async context => {
            const newUser = await context.create(xtremSystem.nodes.User, {
                email: '<EMAIL>',
                firstName: 'Ricardo',
                lastName: 'Marques',
            });
            assert.isTrue(await newUser.$.control());
        }));
    it('Cannot create user with same email using the same case', () =>
        Test.withContext(
            async context => {
                const newUser1 = await context.create(xtremSystem.nodes.User, {
                    email: '<EMAIL>',
                    firstName: 'Ricardo',
                    lastName: 'Marques',
                });
                await newUser1.$.save();

                const newUser2 = await context.create(xtremSystem.nodes.User, {
                    email: '<EMAIL>',
                    firstName: 'Ricardo',
                    lastName: 'Marques2',
                });
                await assert.isRejected(
                    newUser2.$.save(),
                    `duplicate key value violates unique constraint "user_ind0": Key (_tenant_id, email)=(${Test.defaultTenantId}, <EMAIL>) already exists`,
                );
            },
            {
                skipMocks: true,
            },
        ));
    it('Cannot create user with same email using a different case', () =>
        Test.withContext(
            async context => {
                const newUser1 = await context.create(xtremSystem.nodes.User, {
                    email: '<EMAIL>',
                    firstName: 'Ricardo',
                    lastName: 'Marques',
                });
                await newUser1.$.save();

                const newUser2 = await context.create(xtremSystem.nodes.User, {
                    email: '<EMAIL>',
                    firstName: 'Ricardo',
                    lastName: 'Marques2',
                });
                await assert.isRejected(
                    newUser2.$.save(),
                    `duplicate key value violates unique constraint "user_ind0": Key (_tenant_id, email)=(${Test.defaultTenantId}, <EMAIL>) already exists`,
                );
            },
            {
                skipMocks: true,
            },
        ));
    it('Can send notification to Cirrus', async () => {
        // A notification must be sent on every CRUD operation on users
        // see @sage\xtrem-system\lib\nodes\user.ts
        const oldIsActive = true;
        const newIsActive = false;
        let id: number;
        await Test.withContext(async context => {
            const email = '<EMAIL>';
            // Create a user
            const newUser = await context.create(xtremSystem.nodes.User, {
                email,
                firstName: 'David',
                lastName: 'Dupond',
                isActive: oldIsActive,
                preferences: { isExternal: true },
            });
            await newUser.$.save();
            id = newUser._id;

            assert.deepEqual(newUser.$.context.diagnoses, []);

            assert.notEqual(userCreatedSpy.callCount, 0, 'The multi-tenant manager was not notified (create)');
            assert.isTrue(userCreatedSpy.calledOnce, 'The multi-tenant manager was notified more than once (create)');

            assert.deepEqual(userCreatedSpy.getCall(0).args[1], {
                userId: id,
                status: NodeStatus.added,
                email,
                isActive: oldIsActive,
                billingRole: 'businessUser',
                external: true,
                operatorHash: undefined,
            });

            // Get billing role
            const billingRole = await context.read(Role, { id: 'Operational User' });

            // Update the newly created user, including a billing role
            const userToUpdate = await context.read(xtremSystem.nodes.User, { _id: id }, { forUpdate: true });
            await userToUpdate.$.set({ isActive: newIsActive, preferences: { isExternal: false } });

            const newUserBillingRole = await context.create(UserBillingRole, {
                user: userToUpdate._id,
                role: billingRole,
            });
            await userToUpdate.$.set({ billingRole: newUserBillingRole });
            await userToUpdate.$.save();

            assert.notEqual(userModifiedSpy.callCount, 0, 'The multi-tenant manager was not notified (update)');
            assert.isTrue(userModifiedSpy.calledOnce, 'The multi-tenant manager was notified more than once (update)');

            assert.deepEqual(userModifiedSpy.getCall(0).args[1], {
                userId: id,
                status: NodeStatus.modified,
                email,
                isActive: newIsActive,
                billingRole: 'operationalUser',
                external: false,
                operatorHash: undefined,
            });

            // Delete the newly created user
            const userToDelete = await context.read(xtremSystem.nodes.User, { _id: id }, { forUpdate: true });
            await userToDelete.$.delete({ skipControls: true });

            assert.notEqual(userDeletedSpy.callCount, 0, 'The multi-tenant manager was not notified (delete)');
            assert.isTrue(userDeletedSpy.calledOnce, 'The multi-tenant manager was notified more than once (delete)');

            assert.deepEqual(userDeletedSpy.getCall(0).args[1], { userId: id, status: NodeStatus.unchanged, email });
        });
    });

    it('auto create admin persona if it does not exist', async () => {
        const cookies = {} as Dict<Cookie>;
        const login = '<EMAIL>';
        await withUserContext(
            async context => {
                await context.delete(xtremSystem.nodes.User, { email: adminDemoPersona.email });
                const adminPersona = await context.tryRead(xtremSystem.nodes.User, { email: adminDemoPersona.email });
                assert.isNull(adminPersona);
                const user = (await context.user)!;
                assert.equal(user.email, adminDemoPersona.email);
                assert.equal(user.userName, adminDemoPersona.userName);
                const loginUser = await context.loginUser;
                assert.isNotNull(loginUser);
                assert.equal(loginUser?.email, login);
                assert.equal(loginUser?.userName, login);
            },
            {
                auth: { login },
                cookie: (name: string, val: string, options: CookieOptions) => {
                    cookies[name] = { value: val, options };
                },
            },
        );
        await Test.withContext(
            async context => {
                const adminPersona = await context.tryRead(xtremSystem.nodes.User, { email: adminDemoPersona.email });
                assert.isNotNull(adminPersona);
                assert.equal(await adminPersona!.email, adminDemoPersona.email);
            },
            { testActiveServiceOptions: [xtremSystem.serviceOptions.isDemoTenant] },
        );
        assert.deepEqual(cookies[defaultTenantCookieName], {
            value: '<EMAIL>',
            options: { httpOnly: true, sameSite: 'lax', secure: false },
        });
    });

    it('cannot use cookie admin persona for a login user without admin rights to set a user as admin', async () => {
        const cookies = {} as Dict<Cookie>;
        const login = '<EMAIL>';
        const persona = '<EMAIL>';
        await assert.isRejected(
            withUserContext(
                async context => {
                    assert.isFalse((await context.user)!.isAdministrator);
                    const userToUpdate = await context.read(
                        xtremSystem.nodes.User,
                        { email: login },
                        { forUpdate: true },
                    );
                    await userToUpdate.$.set({ isAdministrator: true });
                    await userToUpdate.$.save();
                },
                {
                    user: '<EMAIL>',
                    auth: { login, persona },
                    cookie: (name: string, val: string, options: CookieOptions) => {
                        cookies[name] = { value: val, options };
                    },
                },
            ),
            `The login id '<EMAIL>' does not have the '<EMAIL>' persona, and no 'administrator' persona is available for this user.`,
        );
    });

    it('notify demo persona changes', async () => {
        let id: number;
        await Test.withContext(
            async context => {
                const email = '<EMAIL>';
                // Create a user
                const newUser = await context.create(xtremSystem.nodes.User, {
                    email,
                    firstName: 'Mad',
                    lastName: 'Max',
                    isActive: true,
                });
                await newUser.$.save();
                id = newUser._id;

                // Update the newly created user, switching to demo persona
                let userToUpdate = await context.read(xtremSystem.nodes.User, { _id: id }, { forUpdate: true });
                await userToUpdate.$.set({ isDemoPersona: true });
                await userToUpdate.$.save();

                assert.notEqual(
                    userDeletedSpy.callCount,
                    0,
                    'The multi-tenant manager was not notified (update to demo persona)',
                );
                assert.isTrue(
                    userDeletedSpy.calledOnce,
                    'The multi-tenant manager was notified more than once (update to demo persona)',
                );

                // This is not an actual delete, but the user is no longer a login user so we notify the infra to delete it
                assert.deepEqual(userDeletedSpy.getCall(0).args[1], {
                    userId: id,
                    status: NodeStatus.modified,
                    email,
                });

                // Update the newly created user, switching back to normal user
                userToUpdate = await context.read(xtremSystem.nodes.User, { _id: id }, { forUpdate: true });
                await userToUpdate.$.set({ isDemoPersona: false });
                await userToUpdate.$.save();

                assert.notEqual(
                    userModifiedSpy.callCount,
                    0,
                    'The multi-tenant manager was not notified (modified: update back to normal)',
                );
                assert.isTrue(
                    userModifiedSpy.calledOnce,
                    'The multi-tenant manager was notified more than once (modified: update back to normal)',
                );
                assert.deepEqual(userModifiedSpy.getCall(0).args[1], {
                    userId: id,
                    status: NodeStatus.modified,
                    email,
                    isActive: true,
                    billingRole: 'businessUser',
                    external: false,
                    operatorHash: undefined,
                });
            },
            { testActiveServiceOptions: [xtremSystem.serviceOptions.isDemoTenant] },
        );
    });

    it('Cannot create a second admin persona user', () =>
        Test.withContext(
            async context => {
                const adminPersona = await context.tryRead(xtremSystem.nodes.User, { email: adminDemoPersona.email });
                assert.isNotNull(adminPersona);
                const adminPersonaData = { ...adminDemoPersona } as NodeCreateData<xtremSystem.nodes.User>;
                adminPersonaData.email = '<EMAIL>';
                delete adminPersonaData._id;
                const admin2Persona = await context.create(xtremSystem.nodes.User, adminPersonaData);
                await assert.isRejected(admin2Persona.$.save());
                assert.deepEqual(context.diagnoses, [
                    {
                        severity: ValidationSeverity.error,
                        path: [],
                        message: 'Cannot create an admin demo persona other than the default.',
                    },
                ]);
            },
            { testActiveServiceOptions: [xtremSystem.serviceOptions.isDemoTenant] },
        ));

    it('Cannot deactivate the admin demo persona.', () =>
        Test.withContext(
            async context => {
                const adminPersona = await context.tryRead(
                    xtremSystem.nodes.User,
                    { email: adminDemoPersona.email },
                    { forUpdate: true },
                );
                assert.isNotNull(adminPersona);
                if (adminPersona) {
                    await adminPersona.$.set({ isActive: false });
                    await assert.isRejected(adminPersona.$.save());
                    assert.deepEqual(context.diagnoses, [
                        {
                            severity: ValidationSeverity.error,
                            path: [],
                            message: 'Cannot deactivate the Admin demo persona.',
                        },
                    ]);
                }
            },
            { testActiveServiceOptions: [xtremSystem.serviceOptions.isDemoTenant] },
        ));

    it('Cannot update a system user', () =>
        Test.withContext(async context => {
            const sysUsers = await context
                .query(xtremSystem.nodes.User, {
                    filter: { userType: { _eq: 'system' } },
                    forUpdate: true,
                })
                .toArray();
            const sysUser1 = sysUsers[0];

            await sysUser1.$.set({ firstName: 'sage1' });
            await assert.isRejected(sysUser1.$.save());
            assert.deepEqual(context.diagnoses, [
                {
                    severity: ValidationSeverity.error,
                    path: [],
                    message: 'Cannot create/update a system user',
                },
            ]);
        }));

    it('Update a first admin user', () =>
        Test.withContext(async context => {
            const adminUsers = await context
                .query(xtremSystem.nodes.User, {
                    filter: { isFirstAdminUser: { _eq: true } },
                    forUpdate: true,
                })
                .toArray();
            const adminUser1 = adminUsers[0];
            await adminUser1.$.set({
                firstName: 'FirstUpdate',
            });
            await adminUser1.$.save();
            assert.deepEqual(adminUser1.$.context.diagnoses, []);
        }));

    it('Cannot create a first admin user', () =>
        Test.withContext(async context => {
            const appUser1 = await context.create(xtremSystem.nodes.User, {
                firstName: 'First',
                lastName: 'Last',
                email: '<EMAIL>',
                isFirstAdminUser: true,
            });

            await assert.isRejected(appUser1.$.save());
            assert.deepEqual(context.diagnoses, [
                {
                    severity: ValidationSeverity.error,
                    path: [],
                    message: 'A first admin user is already defined. You cannot create another one.',
                },
            ]);
        }));
});

describe('API users', () => {
    ['1234567890', '8c8f77f8-019b-49ad-8ca0-ddd326ace63b'].forEach(appId =>
        it('Can create api user', () =>
            Test.withContext(
                async context => {
                    const apiUser = await context.create(xtremSystem.nodes.User, {
                        email: `api-${appId}@localhost.domain`,
                        firstName: 'api',
                        lastName: appId.substr(0, 30),
                        isApiUser: true,
                    });

                    await apiUser.$.save();
                },
                {
                    skipMocks: true,
                },
            )),
    );

    it('Cannot create standard user with api email', () => {
        const email = '<EMAIL>';
        return Test.withContext(
            async context => {
                const apiUser = await context.create(xtremSystem.nodes.User, {
                    email,
                    firstName: 'api',
                    lastName: '1234567890',
                });
                await assert.isRejected(apiUser.$.save(), 'The record was not created.');
                const diagnoses = apiUser.$.context.diagnoses;
                assert.strictEqual(diagnoses.length, 1);
                assert.strictEqual(diagnoses[0].message, 'API Email address format is reserved for api users.');
            },
            {
                skipMocks: true,
            },
        );
    });

    [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
    ].forEach(email =>
        it(`Cannot create api user incorrect email '${email}'`, () =>
            Test.withContext(
                async context => {
                    const apiUser = await context.create(xtremSystem.nodes.User, {
                        email,
                        firstName: 'api',
                        lastName: '1234567890',
                        isApiUser: true,
                    });
                    await assert.isRejected(apiUser.$.save(), 'The record was not created.');
                    const diagnoses = apiUser.$.context.diagnoses;
                    assert.strictEqual(diagnoses.length, 1);
                    assert.isTrue(diagnoses[0].message.includes('Invalid api user email address'));
                },
                {
                    skipMocks: true,
                },
            )),
    );

    it('Cannot create api user with admin flag', () =>
        Test.withContext(
            async context => {
                const apiUser = await context.create(xtremSystem.nodes.User, {
                    email: '<EMAIL>',
                    firstName: 'api',
                    lastName: '1234567890',
                    isApiUser: true,
                    isAdministrator: true,
                });
                await assert.isRejected(apiUser.$.save(), 'The record was not created.');
                assert.deepEqual(apiUser.$.context.diagnoses, [
                    {
                        message: 'An api user cannot be an admin',
                        path: [],
                        severity: 3,
                    },
                ]);
            },
            {
                skipMocks: true,
            },
        ));

    it('Cannot create api user with demo persona flag', () =>
        Test.withContext(
            async context => {
                const apiUser = await context.create(xtremSystem.nodes.User, {
                    email: '<EMAIL>',
                    firstName: 'api',
                    lastName: '1234567890',
                    isApiUser: true,
                    isDemoPersona: true,
                });
                await assert.isRejected(apiUser.$.save(), 'The record was not created.');
                assert.deepEqual(apiUser.$.context.diagnoses, [
                    {
                        message: 'An api user cannot be a demo persona',
                        path: [],
                        severity: 3,
                    },
                ]);
            },
            {
                skipMocks: true,
                testActiveServiceOptions: [xtremSystem.serviceOptions.isDemoTenant],
            },
        ));
});

describe('Admin user controls', () => {
    it('Admin user cannot turn off their own admin rights', () =>
        Test.withContext(
            async context => {
                const currentUser = await context.read(
                    xtremSystem.nodes.User,
                    { email: '<EMAIL>' },
                    { forUpdate: true },
                );

                await currentUser.$.set({ isAdministrator: false });
                await assert.isRejected(currentUser.$.save());
                assert.deepEqual(context.diagnoses, [
                    {
                        severity: ValidationSeverity.error,
                        path: [],
                        message: 'You are not allowed to modify your own administrator rights.',
                    },
                ]);
            },
            {
                user: { email: '<EMAIL>' },
            },
        ));

    it('User who is not admin cannot change auth group', () =>
        Test.withContext(
            async context => {
                const currentUser = await context.read(
                    xtremSystem.nodes.User,
                    { email: '<EMAIL>' },
                    { forUpdate: true },
                );

                const groupRoleSite = await context.read(GroupRoleSite, { id: 'Admin' });

                const userGroup: NodeCreateData<UserGroup> = {
                    isActive: true,
                    group: groupRoleSite._id,
                };

                await currentUser.authorizationGroup.append(userGroup);
                await assert.isRejected(currentUser.$.save());
                assert.deepEqual(context.diagnoses, [
                    {
                        severity: ValidationSeverity.error,
                        path: [],
                        message: 'You are not allowed to modify your own authorization group.',
                    },
                ]);
            },
            {
                user: { email: '<EMAIL>' },
            },
        ));
});

describe('Send welcome email', () => {
    it('Send welcome email to user', () =>
        Test.withContext(
            async context => {
                const currentUser = await context.read(
                    xtremSystem.nodes.User,
                    { email: '<EMAIL>' },
                    { forUpdate: true },
                );

                const mailSent = await xtremSystem.nodes.User.sendWelcomeMail(context, [currentUser], false);
                assert.equal(mailSent, true);
            },
            {
                mocks: ['axios'],
                scenario: 'welcome-mail-user',
                directory: __dirname,
            },
        ));
});
