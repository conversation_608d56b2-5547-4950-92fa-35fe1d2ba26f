import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremAuthorization from '../../../index';

describe('Test role activity CRUD', () => {
    before(() => {});

    it('Role Activity - create and update role activities', () =>
        Test.withContext(async context => {
            const role = await context.read(xtremAuthorization.nodes.Role, {
                id: '001',
            });
            const dummyTest5Activity = await context.read(xtremAuthorization.nodes.Activity, { name: 'dummyTest5' });
            const dummyTest4Activity = await context.read(xtremAuthorization.nodes.Activity, { name: 'dummyTest4' });

            const accountantRoleSysId = role._id;
            const dummyTest4ActivitySysId = dummyTest4Activity._id;

            const roleActivity = await context
                .query(xtremAuthorization.nodes.RoleActivity, {
                    filter: { role: accountantRoleSysId, activity: dummyTest4ActivitySysId },
                })
                .at(0);

            assert.instanceOf(roleActivity, xtremAuthorization.nodes.RoleActivity);
            assert.equal(await roleActivity!.isActive, false);
            assert.deepEqual(await roleActivity!.permissions, ['read']);

            const updateRoleActivities = [
                {
                    activity: dummyTest5Activity,
                    permissions: ['create'],
                    isActive: true,
                    hasAllPermissions: false,
                },
                {
                    activity: await roleActivity!.activity,
                    permissions: ['lookup', 'read'],
                    isActive: true,
                    hasAllPermissions: false,
                },
            ];

            const updateRoleActivity = await xtremAuthorization.nodes.RoleActivity.roleActivitiesCreateUpdate(
                context,
                updateRoleActivities,
                role._id.toString(),
                '',
                '',
            );

            assert.isTrue(updateRoleActivity);

            const updatedRoleActivity = await context
                .query(xtremAuthorization.nodes.RoleActivity, {
                    filter: {
                        role: '#001',
                        activity: '#dummyTest4',
                    },
                    first: 1,
                })
                .at(0);

            assert.instanceOf(updatedRoleActivity, xtremAuthorization.nodes.RoleActivity);
            assert.equal(await updatedRoleActivity!.isActive, true);
            assert.deepEqual(await updatedRoleActivity!.permissions, ['lookup', 'read']);
        }));

    it('Role Activity - create new role with activities', () =>
        Test.withContext(async context => {
            const activityDummy = await context.read(xtremAuthorization.nodes.Activity, { name: 'dummyTest5' });
            const activitySecondDummy = await context.read(xtremAuthorization.nodes.Activity, { name: 'dummyTest3' });

            const updateRoleActivities = [
                {
                    activity: activityDummy,
                    permissions: ['create'],
                    isActive: true,
                    hasAllPermissions: false,
                },
                {
                    activity: activitySecondDummy,
                    permissions: ['lookup', 'read'],
                    isActive: true,
                    hasAllPermissions: false,
                },
            ];

            const updateRoleActivity = await xtremAuthorization.nodes.RoleActivity.roleActivitiesCreateUpdate(
                context,
                updateRoleActivities,
                null,
                'Dev',
                'Developer',
            );
            assert.isTrue(updateRoleActivity);

            const role = await context.read(xtremAuthorization.nodes.Role, { id: 'Dev' });

            const devRoleSysId = role._id;

            assert.equal(await role.name, 'Developer');

            const createdRoleActivity = await context
                .query(xtremAuthorization.nodes.RoleActivity, {
                    filter: {
                        role: devRoleSysId,
                        activity: activitySecondDummy._id,
                    },
                    first: 1,
                })
                .at(0);

            assert.instanceOf(createdRoleActivity, xtremAuthorization.nodes.RoleActivity);
            assert.equal(await createdRoleActivity!.isActive, true);
            assert.deepEqual(await createdRoleActivity!.permissions, ['lookup', 'read']);
        }));
});
