import { Test } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremAuthorization from '../../../index';

describe('node Role', () => {
    before(() => {});

    it('Create role', () => {
        return Test.withContext(
            async context => {
                const t0 = await context.getSqlTimestamp();
                assert.deepEqual((await context.user)!.email, '<EMAIL>');

                await (
                    await context.create(xtremAuthorization.nodes.Role, {
                        id: '102',
                        name: 'test Create ',
                    })
                ).$.save();
                const t1 = await context.getSqlTimestamp();

                const myRole = await context.read(xtremAuthorization.nodes.Role, {
                    id: '102',
                });
                /**
                 * if I open without the forUpdate the user is root sage - Bug XT-12553
                 */
                assert.deepEqual(
                    await ((await myRole.$.createdBy) as xtremSystem.nodes.User).displayName,
                    '<PERSON>, <PERSON>',
                );

                assert.isAtLeast((await myRole.$.createStamp).value, t0.value);
                assert.isAtMost((await myRole.$.createStamp).value, t1.value);
            },
            {
                user: { email: '<EMAIL>' },
            },
        );
    });
    it('Update role', () => {
        return Test.withContext(
            async context => {
                const t0 = await context.getSqlTimestamp();
                // assert.deepEqual(context.user.email, '<EMAIL>');
                const myRole = await context.read(
                    xtremAuthorization.nodes.Role,
                    {
                        id: '001',
                    },
                    { forUpdate: true },
                );
                await myRole.$.set({ name: 'Chief accountant' });
                await myRole.$.save();
                const t1 = await context.getSqlTimestamp();

                const myRoleUpdated = await context.read(
                    xtremAuthorization.nodes.Role,
                    {
                        id: '001',
                    },
                    { forUpdate: true },
                );

                assert.deepEqual(await myRole.name, 'Chief accountant');
                /**
                 * _updateStamp is not updated after
                 */
                assert.isAtLeast((await myRoleUpdated.$.updateStamp).value, t0.value);
                assert.isAtMost((await myRoleUpdated.$.updateStamp).value, t1.value);

                /**
                 * must be Marcus Vinicius da Cruz e Mello Moraes
                 */
                assert.deepEqual(
                    await ((await myRole.$.updatedBy) as xtremSystem.nodes.User).displayName,
                    'da Cruz e Mello Moraes, Marcus Vinicius',
                    await ((await myRole.$.updatedBy) as xtremSystem.nodes.User).displayName,
                );
            },
            {
                user: { email: '<EMAIL>' },
            },
        );
    });
});
