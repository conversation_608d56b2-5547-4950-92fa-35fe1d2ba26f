import { asyncArray, Test } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import { TestRestrictedNode } from '../../fixtures/nodes/test-restricted-node';

describe('Restricted node', () => {
    it('can change result of queries by granting and revoking access', () =>
        Test.withContext(
            async context => {
                const user = await xtremSystem.nodes.User.fromContext(context);

                // Initiallly, test user is granted access to:
                // - OBJ2 (with event1 and event2)
                // - OBJ3 (with event1 only)
                const objects = await context.query(TestRestrictedNode, {}).toArray();
                assert.deepEqual(
                    await asyncArray(objects)
                        .map(obj => obj.code)
                        .toArray(),
                    ['OBJ2', 'OBJ3'],
                );

                // revoke event1 grant on both objects
                await user.revokeAccessToObjects(objects, 'event1');

                // Prefetcher must be reset because we modified the authorization filers.
                // and prefetched collections are not valid anymore.
                // In real life, security filter are not modified in the middle of a transaction.
                Test.resetPrefetcher(context);

                // query should only return OBJ2 now
                assert.deepEqual(
                    await asyncArray(await context.query(TestRestrictedNode, {}).toArray())
                        .map(obj => obj.code)
                        .toArray(),
                    ['OBJ2'],
                );

                // read should only return OBJ2, too.
                assert.isNotNull(await context.tryRead(TestRestrictedNode, { code: 'OBJ2' }));
                assert.isNull(await context.tryRead(TestRestrictedNode, { code: 'OBJ3' }));

                // revoke event2 grant to both objects (will do nothing on OBJ3)
                await user.revokeAccessToObjects(objects, 'event2');

                // query should return an empty array now
                assert.deepEqual(
                    await asyncArray(await context.query(TestRestrictedNode, {}).toArray())
                        .map(obj => obj.code)
                        .toArray(),
                    [],
                );

                // grant event3 on both objects
                await user.grantAccessToObjects(objects, 'event3', ['*']);

                // query should now return both objects
                assert.deepEqual(
                    await asyncArray(await context.query(TestRestrictedNode, {}).toArray())
                        .map(obj => obj.code)
                        .toArray(),
                    ['OBJ2', 'OBJ3'],
                );

                // remove all grants on OBJ2
                await user.revokeAccessToObjects([objects[0]], 'event3');

                // query should only return OBJ3 now
                assert.deepEqual(
                    await asyncArray(await context.query(TestRestrictedNode, {}).toArray())
                        .map(obj => obj.code)
                        .toArray(),
                    ['OBJ3'],
                );
            },
            { skipMocks: true },
        ));

    it('can query the grants from a user or an object', () =>
        Test.withContext(
            async context => {
                const user = await xtremSystem.nodes.User.fromContext(context);

                const objectGrants = await asyncArray(await user.objectGrants.toArray())
                    .map(async grant => ({
                        object: await ((await grant.object) as TestRestrictedNode).code,
                        accessMap: await grant.accessMap,
                    }))
                    .toArray();

                assert.deepEqual(objectGrants, [
                    {
                        object: 'OBJ2',
                        accessMap: { event1: [], event2: [] },
                    },
                    {
                        object: 'OBJ3',
                        accessMap: { event1: [] },
                    },
                ]);

                const obj2 = await context.read(TestRestrictedNode, { code: 'OBJ2' });
                const userGrants = await asyncArray(await obj2.userGrants.toArray())
                    .map(async grant => ({
                        user: await (await grant.user).email,
                        accessMap: await grant.accessMap,
                    }))
                    .toArray();

                assert.deepEqual(userGrants, [
                    {
                        user: '<EMAIL>',
                        accessMap: { event1: [], event2: [] },
                    },
                ]);
            },
            { skipMocks: true },
        ));

    it('grants access to the user who creates the object', () =>
        Test.withContext(
            async context => {
                const obj = await context.create(TestRestrictedNode, { code: 'JUSTCREATED' });

                await obj.$.save();

                const userGrants = await asyncArray(await obj.userGrants.toArray())
                    .map(async grant => ({
                        user: await (await grant.user).email,
                        accessMap: await grant.accessMap,
                    }))
                    .toArray();

                assert.deepEqual(userGrants, [
                    {
                        user: '<EMAIL>',
                        accessMap: { created: ['update', 'delete'] },
                    },
                ]);
            },
            { skipMocks: true },
        ));

    it('can modify list of mutations granted to a user', () =>
        Test.withContext(
            async context => {
                const user = await xtremSystem.nodes.User.fromContext(context);

                const obj = await context.read(TestRestrictedNode, { code: 'OBJ2' });

                const getAccessMap = async () =>
                    asyncArray(await obj.userGrants.toArray())
                        .map(grant => grant.accessMap)
                        .toArray();

                assert.deepEqual(await getAccessMap(), [{ event1: [], event2: [] }]);

                await user.grantAccessToObjects([obj], 'event2', ['update']);
                assert.deepEqual(await getAccessMap(), [{ event1: [], event2: ['update'] }]);

                await user.grantAccessToObjects([obj], 'event3', ['update']);
                assert.deepEqual(await getAccessMap(), [{ event1: [], event2: ['update'], event3: ['update'] }]);

                await user.grantAccessToObjects([obj], 'event2', ['delete']);
                await user.grantAccessToObjects([obj], 'event3', ['delete']);
                assert.deepEqual(await getAccessMap(), [
                    { event1: [], event2: ['delete', 'update'], event3: ['delete', 'update'] },
                ]);

                await user.revokeAccessToObjects([obj], 'event2', ['*']);
                assert.deepEqual(await getAccessMap(), [{ event1: [], event2: [], event3: ['delete', 'update'] }]);

                await user.revokeAccessToObjects([obj], 'event3', ['update']);
                assert.deepEqual(await getAccessMap(), [{ event1: [], event2: [], event3: ['delete'] }]);

                await user.revokeAccessToObjects([obj], 'event2');
                assert.deepEqual(await getAccessMap(), [{ event1: [], event3: ['delete'] }]);
            },
            { skipMocks: true },
        ));

    it('chceck that update and delete are controlled', () =>
        Test.withContext(
            async context => {
                const user = await xtremSystem.nodes.User.fromContext(context);

                const obj = await context.read(TestRestrictedNode, { code: 'OBJ2' }, { forUpdate: true });

                const getAccessMap = async () =>
                    asyncArray(await obj.userGrants.toArray())
                        .map(grant => grant.accessMap)
                        .toArray();

                assert.deepEqual(await getAccessMap(), [{ event1: [], event2: [] }]);

                await obj.$.set({ value: 5 });
                await assert.isRejected(obj.$.save(), 'The record was not updated.');
                assert.deepEqual(context.diagnoses, [
                    { message: 'Operation is not allowed on this object', severity: 4, path: [] },
                ]);

                await user.grantAccessToObjects([obj], 'mutationsTest', ['*']);
                /* does not throw */ await (() => obj.$.save())();

                await user.revokeAccessToObjects([obj], 'mutationsTest');
                await obj.$.set({ value: 10 });
                await assert.isRejected(obj.$.save(), 'The record was not updated.');
                assert.deepEqual(context.diagnoses, [
                    { message: 'Operation is not allowed on this object', severity: 4, path: [] },
                ]);

                await user.grantAccessToObjects([obj], 'mutationsTest', ['update']);
                /* does not throw */ await (() => obj.$.save())();

                await assert.isRejected(obj.$.delete(), 'The record was not deleted.');
                assert.deepEqual(context.diagnoses, [
                    { message: 'Operation is not allowed on this object', severity: 3, path: [] },
                ]);

                await user.grantAccessToObjects([obj], 'mutationsTest', ['delete']);
                /* does not throw */ await (() => obj.$.save())();
            },
            { skipMocks: true },
        ));
});
