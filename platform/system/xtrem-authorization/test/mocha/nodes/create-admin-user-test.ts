import { Test, UserData } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import { nodes } from '../../../lib';

describe('Create admin user', () => {
    const adminUser: UserData = {
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Roe',
        locale: 'en-US',
    };

    after(() => {
        return Test.application.asRoot.withCommittedContext(Test.defaultTenantId, async context => {
            const user = await context.tryRead(xtremSystem.nodes.User, { email: adminUser.email }, { forUpdate: true });
            if (user) {
                await user.$.delete();
            }
        });
    });

    it('can create an admin user', () =>
        Test.withContext(async context => {
            /* does not throw */ await (() => Test.application.createAdminUser(`${Test.defaultTenantId}`, adminUser))();
            const user = await context.read(xtremSystem.nodes.User, { email: '<EMAIL>' });
            assert.strictEqual(await user.firstName, 'John');
            assert.strictEqual(await user.lastName, 'Roe');
            const groupRoleSite = await context.read(nodes.GroupRoleSite, { id: 'Admin' });

            // Check user is added to ADMIN authorization group
            assert.strictEqual(
                await user.authorizationGroup.some(
                    async authorizationGroup => (await authorizationGroup.group)._id === groupRoleSite._id,
                ),
                true,
            );
        }));
});
