import { Test } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremAuthorization from '../../../index';

describe('SiteGroup Creation ', () => {
    before(() => {});

    it('Create Company', () =>
        Test.withContext(async context => {
            await (
                await context.create(xtremSystem.nodes.Company, {
                    id: 'totoCie',
                })
            ).$.save();
            // Check if SiteGroup hasBeen Created
            const sitegroup = await context.read(xtremAuthorization.nodes.SiteGroup, { id: 'totoCie' });

            assert.equal(await sitegroup.name, 'totoCie');
        }));
    it('Create Site', () =>
        Test.withContext(async context => {
            await (
                await context.create(xtremSystem.nodes.Site, {
                    id: 'totSite',
                    name: 'totSite name',
                    description: 'totSite description',
                    legalCompany: await context.read(xtremSystem.nodes.Company, { id: 'US001' }),
                })
            ).$.save();

            const sitegroup = await context.read(xtremAuthorization.nodes.SiteGroup, { id: 'US001' });

            /**
             *  Desactivated because the dataset on xtrem-system is wrong - waiting for correction
             */
            // assert.equal(sitegroup.name, 'US Process Manufacturing');
            assert.equal(await sitegroup.sites.length, 3);
            assert.isNotNull(await sitegroup.sites.find(async site => (await (await site.site).id) === 'totSite'));
        }));
});
