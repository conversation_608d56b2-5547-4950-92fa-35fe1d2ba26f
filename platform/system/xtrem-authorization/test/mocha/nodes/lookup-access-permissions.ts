import { Context, Dict, Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import { authorizationServiceOption } from '../../../lib/service-options/authorization-service-option';
import { SysAccessRightsManager } from '../../../lib/services/access-rights-manager';
import { lookupAccessParentTest } from '../../fixtures/activities';

const expectedUserAccessLookupAccess: Dict<string[]> = {
    'TestLookupAccessNoRead.lookup': [],
    'TestLookupAccessParent.lookup': [],
    'TestLookupAccessVitalCollection.lookup': [],
    'TestLookupAccessVitalReference.lookup': [],
};

describe('Lookup access permissions', () => {
    it('Check access <NAME_EMAIL>', () =>
        Test.withContext(
            async context => {
                const allUserAccess = await SysAccessRightsManager.getUserAccess(context);
                assert.hasAllDeepKeys(allUserAccess, expectedUserAccessLookupAccess);
            },
            {
                user: { email: '<EMAIL>' },
                testActiveServiceOptions: [authorizationServiceOption],
            },
        ));

    it('Should have lookup access to TestLookupAccessParent', async () => {
        const checkNode = 'TestLookupAccessParent';
        const checkOperation = 'lookup';
        await Test.withContext(
            async context => {
                const userAccess = await Context.accessRightsManager.getUserAccessFor(
                    context,
                    checkNode,
                    checkOperation,
                );
                assert.deepEqual(userAccess, { sites: null, accessCodes: null, status: 'authorized' });
            },
            {
                user: { email: '<EMAIL>' },
                testActiveServiceOptions: [authorizationServiceOption],
            },
        );
    });

    it('Should have lookup access to TestLookupAccessVitalReference', async () => {
        const checkNode = 'TestLookupAccessVitalReference';
        const checkOperation = 'lookup';
        await Test.withContext(
            async context => {
                const userAccess = await Context.accessRightsManager.getUserAccessFor(
                    context,
                    checkNode,
                    checkOperation,
                );
                assert.deepEqual(userAccess, { sites: null, accessCodes: null, status: 'authorized' });
            },
            {
                user: { email: '<EMAIL>' },
                testActiveServiceOptions: [authorizationServiceOption],
            },
        );
    });

    it('Should have lookup access to TestLookupAccessVitalCollection', async () => {
        const checkNode = 'TestLookupAccessVitalCollection';
        const checkOperation = 'lookup';
        await Test.withContext(
            async context => {
                const userAccess = await Context.accessRightsManager.getUserAccessFor(
                    context,
                    checkNode,
                    checkOperation,
                );
                assert.deepEqual(userAccess, { sites: null, accessCodes: null, status: 'authorized' });
            },
            {
                user: { email: '<EMAIL>' },
                testActiveServiceOptions: [authorizationServiceOption],
            },
        );
    });

    it('Should have lookup access to TestLookupAccessNoRead', async () => {
        const checkNode = 'TestLookupAccessNoRead';
        const checkOperation = 'lookup';
        await Test.withContext(
            async context => {
                const userAccess = await Context.accessRightsManager.getUserAccessFor(
                    context,
                    checkNode,
                    checkOperation,
                );
                assert.deepEqual(userAccess, { sites: null, accessCodes: null, status: 'authorized' });
            },
            {
                user: { email: '<EMAIL>' },
                testActiveServiceOptions: [authorizationServiceOption],
            },
        );
    });

    it('Should not have read access to TestLookupAccessNoRead', async () => {
        const checkNode = 'TestLookupAccessNoRead';
        const checkOperation = 'read';
        await Test.withContext(
            async context => {
                const userAccess = await Context.accessRightsManager.getUserAccessFor(
                    context,
                    checkNode,
                    checkOperation,
                );
                assert.deepEqual(userAccess, { sites: [], accessCodes: [], status: 'unauthorized' });
            },
            {
                user: { email: '<EMAIL>' },
                testActiveServiceOptions: [authorizationServiceOption],
            },
        );
    });

    it('Check isAuthorized for read operation should fail', () =>
        Test.withContext(
            async context => {
                assert.equal(await context.isAuthorized(lookupAccessParentTest, 'read'), false);
            },
            {
                user: { email: '<EMAIL>' },
                testActiveServiceOptions: [authorizationServiceOption],
            },
        ));

    it('Check isAuthorized for update operation should fail', () =>
        Test.withContext(
            async context => {
                assert.equal(await context.isAuthorized(lookupAccessParentTest, 'update'), false);
            },
            {
                user: { email: '<EMAIL>' },
                testActiveServiceOptions: [authorizationServiceOption],
            },
        ));
});
