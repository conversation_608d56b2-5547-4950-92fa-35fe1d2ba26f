import { Test } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremAuthorization from '../../../index';

describe('Activity delete', () => {
    before(() => {});

    it('Cannot delete Activity', () =>
        Test.withContext(async context => {
            const shared = await context.read(xtremAuthorization.nodes.Activity, { _id: '#dummyTest' });
            await assert.isRejected(shared.$.delete(), /cannot delete .*: bad status: readonly/);
        }));

    it('Read Activity', () =>
        Test.withContext(async context => {
            const activity = await context.read(xtremAuthorization.nodes.Activity, { _id: '#dummyTest' });
            const packageVersion = await context.read(xtremSystem.nodes.SysPackVersion, {
                _id: '#@sage/xtrem-authorization',
            });
            assert.equal(await activity.name, 'dummyTest');
            assert.equal(await activity.description, 'Dummy test');
            assert.equal(await activity.package, packageVersion);
        }));
});
