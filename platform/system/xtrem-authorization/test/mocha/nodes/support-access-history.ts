import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremAuthorization from '../../../index';

describe('SupportAccessHistory delete', () => {
    before(() => {});

    it('Cannot delete SupportAccessHistory', () =>
        Test.withContext(async context => {
            const supportAccessHistoryRecord = await context.read(xtremAuthorization.nodes.SupportAccessHistory, {
                _id: '1',
            });
            await assert.isRejected(supportAccessHistoryRecord.$.delete(), /cannot delete .*: bad status: readonly/);
        }));
});

describe('SupportAccessHistory read', () => {
    before(() => {});
    it('Read SupportAccessHistory', () =>
        Test.withContext(async context => {
            const supportAccessHistoryRecord = await context.read(xtremAuthorization.nodes.SupportAccessHistory, {
                _id: '1',
            });
            assert.equal(await supportAccessHistoryRecord.isReadOnlyAccess, true);
            assert.equal((await supportAccessHistoryRecord.startTime).value, 1666951931000);
        }));
});

describe('SupportAccessHistory update', () => {
    before(() => {});
    it('Update SupportAccessHistory', () =>
        Test.withContext(async context => {
            const supportAccessHistoryRecord = await context.read(xtremAuthorization.nodes.SupportAccessHistory, {
                _id: '1',
            });
            await assert.isRejected(
                supportAccessHistoryRecord.$.set({ isReadOnlyAccess: false }),
                /SupportAccessHistory: node is readonly/,
            );
        }));
});

async function pickAccess(accessHistory: xtremAuthorization.nodes.SupportAccessHistory | undefined): Promise<{
    startTime?: string;
    endTime?: string;
    isReadOnlyAccess?: boolean;
}> {
    return {
        startTime: (await accessHistory?.startTime)?.toString(),
        endTime: (await accessHistory?.endTime)?.toString(),
        isReadOnlyAccess: await accessHistory?.isReadOnlyAccess,
    };
}

describe('SupportAccessHistory allowAccess mutation', () => {
    before(() => {});
    it('allowAccess during 2 minutes from actual date', () =>
        Test.withContext(
            async context => {
                assert.deepEqual(
                    await pickAccess(
                        await xtremAuthorization.nodes.SupportAccessHistory.allowAccess(context, 2, 'minutes', true),
                    ),
                    {
                        startTime: '2022-10-31T10:00:00.000Z',
                        endTime: '2022-10-31T10:02:00.000Z',
                        isReadOnlyAccess: true,
                    },
                );
            },
            { now: '2022-10-31T10:00:00Z' },
        ));

    it('allowAccess during 2 hours from actual date', () =>
        Test.withContext(
            async context => {
                assert.deepEqual(
                    await pickAccess(
                        await xtremAuthorization.nodes.SupportAccessHistory.allowAccess(context, 2, 'hours', true),
                    ),
                    {
                        startTime: '2022-10-31T10:00:00.000Z',
                        endTime: '2022-10-31T12:00:00.000Z',
                        isReadOnlyAccess: true,
                    },
                );
            },
            { now: '2022-10-31T10:00:00Z' },
        ));
    it('allowAccess during 2 days from actual date', () =>
        Test.withContext(
            async context => {
                assert.deepEqual(
                    await pickAccess(
                        await xtremAuthorization.nodes.SupportAccessHistory.allowAccess(context, 2, 'days', true),
                    ),
                    {
                        startTime: '2022-10-31T10:00:00.000Z',
                        endTime: '2022-11-02T10:00:00.000Z',
                        isReadOnlyAccess: true,
                    },
                );
            },
            { now: '2022-10-31T10:00:00Z' },
        ));
});

describe('SupportAccessHistory extendAccess mutation', () => {
    before(() => {});
    it('extendAccess with 2 hours', () =>
        Test.withContext(
            async context => {
                const supportAccessHistoryRecord = await xtremAuthorization.nodes.SupportAccessHistory.allowAccess(
                    context,
                    2,
                    'days',
                    true,
                );
                assert.equal(await supportAccessHistoryRecord!.status, 'open');
                assert.equal((await supportAccessHistoryRecord!.endTime).toJSON(), '2022-11-02T10:00:00.000Z');

                const extendAccess = await xtremAuthorization.nodes.SupportAccessHistory.extendAccess(
                    context,
                    2,
                    'hours',
                );
                assert.equal((await extendAccess!.endTime).toJSON(), '2022-11-02T12:00:00.000Z');
            },
            { now: '2022-10-31T10:00:00Z' },
        ));

    it('extendAccess with 2 days', () =>
        Test.withContext(
            async context => {
                const supportAccessHistoryRecord = await xtremAuthorization.nodes.SupportAccessHistory.allowAccess(
                    context,
                    2,
                    'days',
                    true,
                );

                assert.equal(await supportAccessHistoryRecord!.status, 'open');
                assert.equal((await supportAccessHistoryRecord!.endTime).toJSON(), '2022-11-02T10:00:00.000Z');

                const extendAccess = await xtremAuthorization.nodes.SupportAccessHistory.extendAccess(
                    context,
                    2,
                    'days',
                );
                assert.equal((await extendAccess!.endTime).toJSON(), '2022-11-04T10:00:00.000Z');
            },
            { now: '2022-10-31T10:00:00Z' },
        ));

    it('extendAccess with 2 minutes', () =>
        Test.withContext(
            async context => {
                const supportAccessHistoryRecord = await xtremAuthorization.nodes.SupportAccessHistory.allowAccess(
                    context,
                    2,
                    'days',
                    true,
                );

                assert.equal(await supportAccessHistoryRecord!.status, 'open');
                assert.equal((await supportAccessHistoryRecord!.endTime).toJSON(), '2022-11-02T10:00:00.000Z');
                const extendAccess = await xtremAuthorization.nodes.SupportAccessHistory.extendAccess(
                    context,
                    2,
                    'minutes',
                );
                assert.equal((await extendAccess!.endTime).toJSON(), '2022-11-02T10:02:00.000Z');
            },
            { now: '2022-10-31T10:00:00Z' },
        ));
});

describe('SupportAccessHistory revokeAccess mutation', () => {
    before(() => {});
    it('revokeAccess', () =>
        Test.withContext(
            async context => {
                const supportAccessHistoryRecord = await xtremAuthorization.nodes.SupportAccessHistory.allowAccess(
                    context,
                    2,
                    'hours',
                    true,
                );
                assert.equal(await supportAccessHistoryRecord!.status, 'open');

                const revokeAccess = await xtremAuthorization.nodes.SupportAccessHistory.revokeAccess(context);
                assert.equal((await revokeAccess!.endTime).toJSON(), '2022-10-31T10:00:00.000Z');
                assert.equal(await revokeAccess!.status, 'closed');
            },
            { now: '2022-10-31T10:00:00Z' },
        ));
});

describe('checkSupportAccessOpen query', () => {
    before(() => {});
    it('session open with 30 days', () =>
        Test.withContext(
            async context => {
                const supportAccessHistoryRecord = await xtremAuthorization.nodes.SupportAccessHistory.allowAccess(
                    context,
                    30,
                    'days',
                    true,
                );
                assert.equal(await supportAccessHistoryRecord!.status, 'open');
                assert.equal((await supportAccessHistoryRecord!.endTime).toJSON(), '2022-11-30T10:00:00.000Z');

                const checkSupportAccess =
                    await xtremAuthorization.nodes.SupportAccessHistory.checkSupportAccessOpen(context);

                assert.equal(checkSupportAccess.isOpen, true);
                assert.equal(await checkSupportAccess.timeToClose, 2147483647);
            },
            { now: '2022-10-31T10:00:00Z' },
        ));

    it('session open with 2 days', () =>
        Test.withContext(
            async context => {
                const supportAccessHistoryRecord = await xtremAuthorization.nodes.SupportAccessHistory.allowAccess(
                    context,
                    2,
                    'days',
                    true,
                );
                assert.equal(await supportAccessHistoryRecord!.status, 'open');
                assert.equal((await supportAccessHistoryRecord!.endTime).toJSON(), '2022-11-02T10:00:00.000Z');

                const checkSupportAccess =
                    await xtremAuthorization.nodes.SupportAccessHistory.checkSupportAccessOpen(context);

                assert.equal(checkSupportAccess.isOpen, true);
                assert.equal(await checkSupportAccess.timeToClose, 172800000);
            },
            { now: '2022-10-31T10:00:00Z' },
        ));
});
