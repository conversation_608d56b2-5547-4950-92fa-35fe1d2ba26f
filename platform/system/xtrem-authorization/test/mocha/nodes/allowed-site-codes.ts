import { Context, Test } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import { authorizationServiceOption } from '../../../lib/service-options/authorization-service-option';

describe('Authorization sites test', () => {
    it('Current user should have full site access to TestActivityNode2.create', async () => {
        const checkNode = 'TestActivityNode2';
        const checkOperation = 'create';
        await Test.withContext(
            async context => {
                const siteCodes = (
                    await Context.accessRightsManager.getUserAccessFor(context, checkNode, checkOperation)
                ).sites;
                assert.deepEqual(siteCodes, null);
            },
            {
                user: { email: '<EMAIL>' },
                testActiveServiceOptions: [authorizationServiceOption],
            },
        );
    });

    it('Current user should have limited site access to TestActivityNode4.delete', async () => {
        const checkNode = 'TestActivityNode4';
        const checkOperation = 'delete';
        await Test.withContext(
            async context => {
                const siteIds = (await Context.accessRightsManager.getUserAccessFor(context, checkNode, checkOperation))
                    .sites;
                assert.equal(siteIds?.length, 2);
                const sites = await context
                    .query(xtremSystem.nodes.Site, { filter: { _id: { _in: siteIds || [] } } })
                    .map(s => s.id)
                    .toArray();
                assert.deepEqual(sites, ['US001', 'US002']);
            },
            {
                user: { email: '<EMAIL>' },
                testActiveServiceOptions: [authorizationServiceOption],
            },
        );
    });

    it('Current user should not have any site access to TestActivityNode2.delete', async () => {
        const checkNode = 'TestActivityNode2';
        const checkOperation = 'delete';
        await Test.withContext(
            async context => {
                const siteCodes = (
                    await Context.accessRightsManager.getUserAccessFor(context, checkNode, checkOperation)
                ).sites;
                assert.deepEqual(siteCodes, []);
            },
            {
                user: { email: '<EMAIL>' },
                testActiveServiceOptions: [authorizationServiceOption],
            },
        );
    });

    it('Current user should not have any site access to TestSiteFilterNode.read', async () => {
        const checkNode = 'TestSiteFilterNode';
        const checkOperation = 'read';
        await Test.withContext(
            async context => {
                const siteCodes = (
                    await Context.accessRightsManager.getUserAccessFor(context, checkNode, checkOperation)
                ).sites;
                assert.deepEqual(siteCodes, []);
            },
            {
                user: { email: '<EMAIL>' },
                testActiveServiceOptions: [authorizationServiceOption],
            },
        );
    });

    it('One Site user should only have access to UnitTestActive', async () => {
        const checkNode = 'TestSiteReference';
        const checkOperation = 'create';
        await Test.withContext(
            async context => {
                const siteIds = (await Context.accessRightsManager.getUserAccessFor(context, checkNode, checkOperation))
                    .sites;
                assert.equal(siteIds?.length, 1);
                const sites = await context
                    .query(xtremSystem.nodes.Site, { filter: { _id: { _in: siteIds || [] } } })
                    .map(s => s.id)
                    .toArray();
                assert.deepEqual(sites, ['UnitTestActive']);
            },
            {
                user: { email: '<EMAIL>' },
                testActiveServiceOptions: [authorizationServiceOption],
            },
        );
    });
});
