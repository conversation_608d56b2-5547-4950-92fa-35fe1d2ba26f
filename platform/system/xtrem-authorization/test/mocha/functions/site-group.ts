import { Test } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremAuthorization from '../../../index';

describe('SiteGroup functions', () => {
    before(() => {});

    it('Delete site Group - from company S2 ', () =>
        Test.withContext(async context => {
            const companyS2 = await context.read(xtremSystem.nodes.Company, {
                id: 'S2',
            });
            assert.isTrue(await xtremAuthorization.functions.deleteSiteGroup(companyS2));
            assert.isFalse(await context.exists(xtremAuthorization.nodes.SiteGroup, { id: 'S2' }));
        }));
    it('Delete site Group - from company S2 ', () =>
        Test.withContext(async context => {
            const companyS4 = await context.read(xtremSystem.nodes.Company, {
                id: 'S4',
            });
            assert.isFalse(await context.exists(xtremAuthorization.nodes.SiteGroup, { id: 'S4' }));
            assert.isTrue(await xtremAuthorization.functions.deleteSiteGroup(companyS4));
        }));
    it('Delete site Group - from company S2 ', () =>
        Test.withContext(async context => {
            const siteS4 = await context.read(xtremSystem.nodes.Site, {
                id: 'ETS01-S04',
            });
            assert.isFalse(await context.exists(xtremAuthorization.nodes.SiteGroup, { id: 'S4' }));
            await xtremAuthorization.functions.creationOfSiteGroupToSite(siteS4);
            assert.isTrue(await context.exists(xtremAuthorization.nodes.SiteGroup, { id: 'S4' }));
        }));
});
