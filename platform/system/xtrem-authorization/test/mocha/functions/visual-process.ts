import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremAuthorization from '../../../index';

const siteGroup004Chart = {
    acts: {},
    layersArr: [
        {
            xpropsArr: [
                {
                    xstrokeProps: {
                        xend: { xtype: 'none' },
                        xstart: { xtype: 'none' },
                        xalpha: 100,
                        xcolor: '#CCD6DB',
                        xthickness: 1,
                        xtype: 'solidstroke',
                    },
                    xfillProps: { xgtype: 'linear', xalpha: 100, xcolor: '#E6EBED', xtype: 'solidfill' },
                    xanchors: [
                        { x: 20, y: 20 },
                        { x: '338.66666***********', y: 20 },
                        { x: '338.66666***********', y: 80 },
                        { x: 20, y: 80 },
                    ],
                    xtextFormat: {
                        letterSpacing: 0,
                        display: 'block',
                        blockIndent: 0,
                        leading: 0,
                        indent: 0,
                        rightMargin: 5,
                        leftMargin: 5,
                        align: 'left',
                        url: '',
                        color: 0,
                        size: 16,
                        font: 'Arial',
                    },
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 20, xtop: 42 },
                    xcaptionSize: { xwidth: '318.66666***********', xheight: 60 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'global' },
                    xdrawBehaviorCode: 'K_API_RECT',
                    xshapeType: 'apishape',
                    uniqueID: '8fa497d9-19a6-4aad-9ab8-6c60e28f77da',
                },
                {
                    xanchors: [
                        { x: 78, y: 40 },
                        { x: '396.66666***********', y: 40 },
                        { x: '396.66666***********', y: 60 },
                        { x: 78, y: 60 },
                    ],
                    xtextFormat: {
                        letterSpacing: 0,
                        display: 'block',
                        blockIndent: 0,
                        leading: 0,
                        indent: 0,
                        rightMargin: 5,
                        leftMargin: 5,
                        align: 'left',
                        url: '',
                        color: 0,
                        size: 16,
                        font: 'Arial',
                    },
                    xtext: 'US Process Manufacturi...',
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 78, xtop: 62 },
                    xcaptionSize: { xwidth: '318.66666***********', xheight: 60 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'global' },
                    xdrawBehaviorCode: 'K_API_RECT',
                    xshapeType: 'apishape',
                    uniqueID: 'bd69cca0-3486-427f-9a6f-7ffb84dd779c',
                },
                {
                    xsize: { xwidth: 36, xheight: 36 },
                    xcenter: { x: 0, y: 0 },
                    eltid: 'building-1',
                    bibid: 'business-icons',
                    xactionProps: { xtype: 'none' },
                    xlinkProps: { xtype: 'none' },
                    xanchors: [
                        { x: 32, y: 32 },
                        { x: 68, y: 32 },
                        { x: 68, y: 68 },
                        { x: 32, y: 68 },
                    ],
                    xtext: '',
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 0, xtop: 0 },
                    xcaptionSize: { xwidth: 0, xheight: 0 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'none' },
                    xdrawBehaviorCode: null,
                    xshapeType: 'fileshape',
                    uniqueID: 'a02c4a7c-4ada-4140-b3a5-0b6aad27fa7b',
                },
                {
                    xstrokeProps: {
                        xend: { xtype: 'none' },
                        xstart: { xtype: 'none' },
                        xalpha: 100,
                        xcolor: '#CCD6DB',
                        xthickness: 1,
                        xtype: 'solidstroke',
                    },
                    xfillProps: { xgtype: 'linear', xalpha: 100, xcolor: '#E6EBED', xtype: 'solidfill' },
                    xanchors: [
                        { x: 80, y: 100 },
                        { x: 260, y: 100 },
                        { x: 260, y: 160 },
                        { x: 80, y: 160 },
                    ],
                    xtextFormat: {
                        letterSpacing: 0,
                        display: 'block',
                        blockIndent: 0,
                        leading: 0,
                        indent: 0,
                        rightMargin: 5,
                        leftMargin: 5,
                        align: 'left',
                        url: '',
                        color: 0,
                        size: 16,
                        font: 'Arial',
                    },
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 80, xtop: 122 },
                    xcaptionSize: { xwidth: 180, xheight: 60 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'global' },
                    xdrawBehaviorCode: 'K_API_RECT',
                    xshapeType: 'apishape',
                    uniqueID: '52b39f75-449a-4119-a651-f11a61224dc6',
                },
                {
                    xanchors: [
                        { x: 138, y: 120 },
                        { x: 318, y: 120 },
                        { x: 318, y: 140 },
                        { x: 138, y: 140 },
                    ],
                    xtextFormat: {
                        letterSpacing: 0,
                        display: 'block',
                        blockIndent: 0,
                        leading: 0,
                        indent: 0,
                        rightMargin: 5,
                        leftMargin: 5,
                        align: 'left',
                        url: '',
                        color: 0,
                        size: 16,
                        font: 'Arial',
                    },
                    xtext: 'US005',
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 138, xtop: 142 },
                    xcaptionSize: { xwidth: 180, xheight: 60 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'global' },
                    xdrawBehaviorCode: 'K_API_RECT',
                    xshapeType: 'apishape',
                    uniqueID: 'c40f9563-0066-42ef-9525-13293b29b694',
                },
                {
                    xsize: { xwidth: 36, xheight: 36 },
                    xcenter: { x: 0, y: 0 },
                    eltid: 'factory',
                    bibid: 'business-icons',
                    xactionProps: { xtype: 'none' },
                    xlinkProps: { xtype: 'none' },
                    xanchors: [
                        { x: 92, y: 112 },
                        { x: 128, y: 112 },
                        { x: 128, y: 148 },
                        { x: 92, y: 148 },
                    ],
                    xtext: '',
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 0, xtop: 0 },
                    xcaptionSize: { xwidth: 0, xheight: 0 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'none' },
                    xdrawBehaviorCode: null,
                    xshapeType: 'fileshape',
                    uniqueID: 'ee5b6b73-b296-4a07-a2fe-81bf92161caf',
                },
                {
                    xstrokeProps: {
                        xend: { xtype: 'none' },
                        xstart: { xtype: 'none' },
                        xalpha: 100,
                        xcolor: '#CCD6DB',
                        xthickness: 1,
                        xtype: 'solidstroke',
                    },
                    xfillProps: { xgtype: 'linear', xalpha: 100, xcolor: '#E6EBED', xtype: 'solidfill' },
                    xanchors: [
                        { x: 80, y: 180 },
                        { x: 260, y: 180 },
                        { x: 260, y: 240 },
                        { x: 80, y: 240 },
                    ],
                    xtextFormat: {
                        letterSpacing: 0,
                        display: 'block',
                        blockIndent: 0,
                        leading: 0,
                        indent: 0,
                        rightMargin: 5,
                        leftMargin: 5,
                        align: 'left',
                        url: '',
                        color: 0,
                        size: 16,
                        font: 'Arial',
                    },
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 80, xtop: 202 },
                    xcaptionSize: { xwidth: 180, xheight: 60 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'global' },
                    xdrawBehaviorCode: 'K_API_RECT',
                    xshapeType: 'apishape',
                    uniqueID: '51e701b0-b7e9-422f-80a5-c892bd7dcd2c',
                },
                {
                    xanchors: [
                        { x: 138, y: 200 },
                        { x: 318, y: 200 },
                        { x: 318, y: 220 },
                        { x: 138, y: 220 },
                    ],
                    xtextFormat: {
                        letterSpacing: 0,
                        display: 'block',
                        blockIndent: 0,
                        leading: 0,
                        indent: 0,
                        rightMargin: 5,
                        leftMargin: 5,
                        align: 'left',
                        url: '',
                        color: 0,
                        size: 16,
                        font: 'Arial',
                    },
                    xtext: 'US006',
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 138, xtop: 222 },
                    xcaptionSize: { xwidth: 180, xheight: 60 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'global' },
                    xdrawBehaviorCode: 'K_API_RECT',
                    xshapeType: 'apishape',
                    uniqueID: '45b35f91-1289-48ae-8dcf-d377002028db',
                },
                {
                    xsize: { xwidth: 36, xheight: 36 },
                    xcenter: { x: 0, y: 0 },
                    eltid: 'factory',
                    bibid: 'business-icons',
                    xactionProps: { xtype: 'none' },
                    xlinkProps: { xtype: 'none' },
                    xanchors: [
                        { x: 92, y: 192 },
                        { x: 128, y: 192 },
                        { x: 128, y: 228 },
                        { x: 92, y: 228 },
                    ],
                    xtext: '',
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 0, xtop: 0 },
                    xcaptionSize: { xwidth: 0, xheight: 0 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'none' },
                    xdrawBehaviorCode: null,
                    xshapeType: 'fileshape',
                    uniqueID: 'f1d9468b-519b-4a92-9221-424068b7ae51',
                },
                {
                    xstrokeProps: {
                        xend: { xtype: 'none' },
                        xstart: { xtype: 'none' },
                        xalpha: 100,
                        xcolor: '#CCD6DB',
                        xthickness: 1,
                        xtype: 'solidstroke',
                    },
                    xfillProps: { xgtype: 'linear', xalpha: 100, xcolor: '#E6EBED', xtype: 'solidfill' },
                    xanchors: [
                        { x: 80, y: 260 },
                        { x: 260, y: 260 },
                        { x: 260, y: 320 },
                        { x: 80, y: 320 },
                    ],
                    xtextFormat: {
                        letterSpacing: 0,
                        display: 'block',
                        blockIndent: 0,
                        leading: 0,
                        indent: 0,
                        rightMargin: 5,
                        leftMargin: 5,
                        align: 'left',
                        url: '',
                        color: 0,
                        size: 16,
                        font: 'Arial',
                    },
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 80, xtop: 282 },
                    xcaptionSize: { xwidth: 180, xheight: 60 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'global' },
                    xdrawBehaviorCode: 'K_API_RECT',
                    xshapeType: 'apishape',
                    uniqueID: 'bce4fd78-64eb-4ccf-88db-7251bb5709ee',
                },
                {
                    xanchors: [
                        { x: 138, y: 280 },
                        { x: 318, y: 280 },
                        { x: 318, y: 300 },
                        { x: 138, y: 300 },
                    ],
                    xtextFormat: {
                        letterSpacing: 0,
                        display: 'block',
                        blockIndent: 0,
                        leading: 0,
                        indent: 0,
                        rightMargin: 5,
                        leftMargin: 5,
                        align: 'left',
                        url: '',
                        color: 0,
                        size: 16,
                        font: 'Arial',
                    },
                    xtext: 'US007',
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 138, xtop: 302 },
                    xcaptionSize: { xwidth: 180, xheight: 60 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'global' },
                    xdrawBehaviorCode: 'K_API_RECT',
                    xshapeType: 'apishape',
                    uniqueID: '18b09893-6ea8-4bf4-8fb3-f85764b57f0e',
                },
                {
                    xsize: { xwidth: 36, xheight: 36 },
                    xcenter: { x: 0, y: 0 },
                    eltid: 'factory',
                    bibid: 'business-icons',
                    xactionProps: { xtype: 'none' },
                    xlinkProps: { xtype: 'none' },
                    xanchors: [
                        { x: 92, y: 272 },
                        { x: 128, y: 272 },
                        { x: 128, y: 308 },
                        { x: 92, y: 308 },
                    ],
                    xtext: '',
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 0, xtop: 0 },
                    xcaptionSize: { xwidth: 0, xheight: 0 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'none' },
                    xdrawBehaviorCode: null,
                    xshapeType: 'fileshape',
                    uniqueID: '9882582e-111b-4ed2-95c5-5ca999aa05e1',
                },
            ],
            alpha: 100,
            visible: true,
            lock: false,
            id: 0,
        },
    ],
    reachedGroupNum: 1,
    contentSize: { xheight: 340, xwidth: '570.6666666666666496' },
    docDims: { xheight: 340, xwidth: '570.6666666666666496', xtop: 0, xleft: 0 },
    currentLayerId: 1,
};

const siteGroupSiteGroupingChart = {
    acts: {},
    layersArr: [
        {
            xpropsArr: [
                {
                    xstrokeProps: {
                        xend: { xtype: 'none' },
                        xstart: { xtype: 'none' },
                        xalpha: 100,
                        xcolor: '#CCD6DB',
                        xthickness: 1,
                        xtype: 'solidstroke',
                    },
                    xfillProps: { xgtype: 'linear', xalpha: 100, xcolor: '#E6EBED', xtype: 'solidfill' },
                    xanchors: [
                        { x: 20, y: 20 },
                        { x: '338.66666***********', y: 20 },
                        { x: '338.66666***********', y: 80 },
                        { x: 20, y: 80 },
                    ],
                    xtextFormat: {
                        letterSpacing: 0,
                        display: 'block',
                        blockIndent: 0,
                        leading: 0,
                        indent: 0,
                        rightMargin: 5,
                        leftMargin: 5,
                        align: 'left',
                        url: '',
                        color: 0,
                        size: 16,
                        font: 'Arial',
                    },
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 20, xtop: 42 },
                    xcaptionSize: { xwidth: '318.66666***********', xheight: 60 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'global' },
                    xdrawBehaviorCode: 'K_API_RECT',
                    xshapeType: 'apishape',
                    uniqueID: '85618fae-b991-4495-8f3c-1f43b05b6ca5',
                },
                {
                    xanchors: [
                        { x: 78, y: 40 },
                        { x: '396.66666***********', y: 40 },
                        { x: '396.66666***********', y: 60 },
                        { x: 78, y: 60 },
                    ],
                    xtextFormat: {
                        letterSpacing: 0,
                        display: 'block',
                        blockIndent: 0,
                        leading: 0,
                        indent: 0,
                        rightMargin: 5,
                        leftMargin: 5,
                        align: 'left',
                        url: '',
                        color: 0,
                        size: 16,
                        font: 'Arial',
                    },
                    xtext: 'Site group Portland/De...',
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 78, xtop: 62 },
                    xcaptionSize: { xwidth: '318.66666***********', xheight: 60 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'global' },
                    xdrawBehaviorCode: 'K_API_RECT',
                    xshapeType: 'apishape',
                    uniqueID: 'afb8c046-51c6-4fdc-b2e0-ca196ab4d778',
                },
                {
                    xsize: { xwidth: 36, xheight: 36 },
                    xcenter: { x: 0, y: 0 },
                    eltid: 'building-1',
                    bibid: 'business-icons',
                    xactionProps: { xtype: 'none' },
                    xlinkProps: { xtype: 'none' },
                    xanchors: [
                        { x: 32, y: 32 },
                        { x: 68, y: 32 },
                        { x: 68, y: 68 },
                        { x: 32, y: 68 },
                    ],
                    xtext: '',
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 0, xtop: 0 },
                    xcaptionSize: { xwidth: 0, xheight: 0 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'none' },
                    xdrawBehaviorCode: null,
                    xshapeType: 'fileshape',
                    uniqueID: 'cac81d7d-90ce-41c6-9a7c-f6121d6a31df',
                },
                {
                    xstrokeProps: {
                        xend: { xtype: 'none' },
                        xstart: { xtype: 'none' },
                        xalpha: 100,
                        xcolor: '#CCD6DB',
                        xthickness: 1,
                        xtype: 'solidstroke',
                    },
                    xfillProps: { xgtype: 'linear', xalpha: 100, xcolor: '#E6EBED', xtype: 'solidfill' },
                    xanchors: [
                        { x: 80, y: 100 },
                        { x: 260, y: 100 },
                        { x: 260, y: 160 },
                        { x: 80, y: 160 },
                    ],
                    xtextFormat: {
                        letterSpacing: 0,
                        display: 'block',
                        blockIndent: 0,
                        leading: 0,
                        indent: 0,
                        rightMargin: 5,
                        leftMargin: 5,
                        align: 'left',
                        url: '',
                        color: 0,
                        size: 16,
                        font: 'Arial',
                    },
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 80, xtop: 122 },
                    xcaptionSize: { xwidth: 180, xheight: 60 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'global' },
                    xdrawBehaviorCode: 'K_API_RECT',
                    xshapeType: 'apishape',
                    uniqueID: 'cb4b7ff9-c289-408e-8399-2994b3d9aec4',
                },
                {
                    xanchors: [
                        { x: 138, y: 120 },
                        { x: 318, y: 120 },
                        { x: 318, y: 140 },
                        { x: 138, y: 140 },
                    ],
                    xtextFormat: {
                        letterSpacing: 0,
                        display: 'block',
                        blockIndent: 0,
                        leading: 0,
                        indent: 0,
                        rightMargin: 5,
                        leftMargin: 5,
                        align: 'left',
                        url: '',
                        color: 0,
                        size: 16,
                        font: 'Arial',
                    },
                    xtext: 'US009',
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 138, xtop: 142 },
                    xcaptionSize: { xwidth: 180, xheight: 60 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'global' },
                    xdrawBehaviorCode: 'K_API_RECT',
                    xshapeType: 'apishape',
                    uniqueID: '6413d793-0d2d-4628-b635-52cc6d5cd6ec',
                },
                {
                    xsize: { xwidth: 36, xheight: 36 },
                    xcenter: { x: 0, y: 0 },
                    eltid: 'factory',
                    bibid: 'business-icons',
                    xactionProps: { xtype: 'none' },
                    xlinkProps: { xtype: 'none' },
                    xanchors: [
                        { x: 92, y: 112 },
                        { x: 128, y: 112 },
                        { x: 128, y: 148 },
                        { x: 92, y: 148 },
                    ],
                    xtext: '',
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 0, xtop: 0 },
                    xcaptionSize: { xwidth: 0, xheight: 0 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'none' },
                    xdrawBehaviorCode: null,
                    xshapeType: 'fileshape',
                    uniqueID: '3034dfb4-7d7c-49f4-9146-f35d184cf00d',
                },
            ],
            alpha: 100,
            visible: true,
            lock: false,
            id: 0,
        },
    ],
    reachedGroupNum: 1,
    contentSize: { xheight: 180, xwidth: '613.333333333333312' },
    docDims: { xheight: 180, xwidth: '613.333333333333312', xtop: 0, xleft: 0 },
    currentLayerId: 1,
};

const siteGroupCompanyGroupingChart = {
    acts: {},
    layersArr: [
        {
            xpropsArr: [
                {
                    xstrokeProps: {
                        xend: { xtype: 'none' },
                        xstart: { xtype: 'none' },
                        xalpha: 100,
                        xcolor: '#CCD6DB',
                        xthickness: 1,
                        xtype: 'solidstroke',
                    },
                    xfillProps: { xgtype: 'linear', xalpha: 100, xcolor: '#E6EBED', xtype: 'solidfill' },
                    xanchors: [
                        { x: 20, y: 20 },
                        { x: '338.66666***********', y: 20 },
                        { x: '338.66666***********', y: 80 },
                        { x: 20, y: 80 },
                    ],
                    xtextFormat: {
                        letterSpacing: 0,
                        display: 'block',
                        blockIndent: 0,
                        leading: 0,
                        indent: 0,
                        rightMargin: 5,
                        leftMargin: 5,
                        align: 'left',
                        url: '',
                        color: 0,
                        size: 16,
                        font: 'Arial',
                    },
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 20, xtop: 42 },
                    xcaptionSize: { xwidth: '318.66666***********', xheight: 60 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'global' },
                    xdrawBehaviorCode: 'K_API_RECT',
                    xshapeType: 'apishape',
                    uniqueID: '7744ee1d-c679-4738-9d24-cbddd7b37970',
                },
                {
                    xanchors: [
                        { x: 78, y: 40 },
                        { x: '396.66666***********', y: 40 },
                        { x: '396.66666***********', y: 60 },
                        { x: 78, y: 60 },
                    ],
                    xtextFormat: {
                        letterSpacing: 0,
                        display: 'block',
                        blockIndent: 0,
                        leading: 0,
                        indent: 0,
                        rightMargin: 5,
                        leftMargin: 5,
                        align: 'left',
                        url: '',
                        color: 0,
                        size: 16,
                        font: 'Arial',
                    },
                    xtext: 'Company group US001/US...',
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 78, xtop: 62 },
                    xcaptionSize: { xwidth: '318.66666***********', xheight: 60 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'global' },
                    xdrawBehaviorCode: 'K_API_RECT',
                    xshapeType: 'apishape',
                    uniqueID: '72c59383-c201-49b6-b9f1-9dc433cc2635',
                },
                {
                    xsize: { xwidth: 36, xheight: 36 },
                    xcenter: { x: 0, y: 0 },
                    eltid: 'building-1',
                    bibid: 'business-icons',
                    xactionProps: { xtype: 'none' },
                    xlinkProps: { xtype: 'none' },
                    xanchors: [
                        { x: 32, y: 32 },
                        { x: 68, y: 32 },
                        { x: 68, y: 68 },
                        { x: 32, y: 68 },
                    ],
                    xtext: '',
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 0, xtop: 0 },
                    xcaptionSize: { xwidth: 0, xheight: 0 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'none' },
                    xdrawBehaviorCode: null,
                    xshapeType: 'fileshape',
                    uniqueID: '718a8fa7-fe80-49b9-83b3-e702cae3e3d7',
                },
                {
                    xstrokeProps: {
                        xend: { xtype: 'none' },
                        xstart: { xtype: 'none' },
                        xalpha: 100,
                        xcolor: '#CCD6DB',
                        xthickness: 1,
                        xtype: 'solidstroke',
                    },
                    xfillProps: { xgtype: 'linear', xalpha: 100, xcolor: '#E6EBED', xtype: 'solidfill' },
                    xanchors: [
                        { x: 80, y: 100 },
                        { x: 260, y: 100 },
                        { x: 260, y: 160 },
                        { x: 80, y: 160 },
                    ],
                    xtextFormat: {
                        letterSpacing: 0,
                        display: 'block',
                        blockIndent: 0,
                        leading: 0,
                        indent: 0,
                        rightMargin: 5,
                        leftMargin: 5,
                        align: 'left',
                        url: '',
                        color: 0,
                        size: 16,
                        font: 'Arial',
                    },
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 80, xtop: 122 },
                    xcaptionSize: { xwidth: 180, xheight: 60 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'global' },
                    xdrawBehaviorCode: 'K_API_RECT',
                    xshapeType: 'apishape',
                    uniqueID: 'c7252428-be81-4ab8-af58-54c766597218',
                },
                {
                    xanchors: [
                        { x: 138, y: 120 },
                        { x: 318, y: 120 },
                        { x: 318, y: 140 },
                        { x: 138, y: 140 },
                    ],
                    xtextFormat: {
                        letterSpacing: 0,
                        display: 'block',
                        blockIndent: 0,
                        leading: 0,
                        indent: 0,
                        rightMargin: 5,
                        leftMargin: 5,
                        align: 'left',
                        url: '',
                        color: 0,
                        size: 16,
                        font: 'Arial',
                    },
                    xtext: 'US001',
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 138, xtop: 142 },
                    xcaptionSize: { xwidth: 180, xheight: 60 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'global' },
                    xdrawBehaviorCode: 'K_API_RECT',
                    xshapeType: 'apishape',
                    uniqueID: '70bb7625-21d7-44bd-a5b9-7365f7af86f4',
                },
                {
                    xsize: { xwidth: 36, xheight: 36 },
                    xcenter: { x: 0, y: 0 },
                    eltid: 'building-1',
                    bibid: 'business-icons',
                    xactionProps: { xtype: 'none' },
                    xlinkProps: { xtype: 'none' },
                    xanchors: [
                        { x: 92, y: 112 },
                        { x: 128, y: 112 },
                        { x: 128, y: 148 },
                        { x: 92, y: 148 },
                    ],
                    xtext: '',
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 0, xtop: 0 },
                    xcaptionSize: { xwidth: 0, xheight: 0 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'none' },
                    xdrawBehaviorCode: null,
                    xshapeType: 'fileshape',
                    uniqueID: 'add6bfaf-5135-4ab8-8fb4-b707cd62ed9c',
                },
                {
                    xstrokeProps: {
                        xend: { xtype: 'none' },
                        xstart: { xtype: 'none' },
                        xalpha: 100,
                        xcolor: '#CCD6DB',
                        xthickness: 1,
                        xtype: 'solidstroke',
                    },
                    xfillProps: { xgtype: 'linear', xalpha: 100, xcolor: '#E6EBED', xtype: 'solidfill' },
                    xanchors: [
                        { x: 80, y: 340 },
                        { x: 260, y: 340 },
                        { x: 260, y: 400 },
                        { x: 80, y: 400 },
                    ],
                    xtextFormat: {
                        letterSpacing: 0,
                        display: 'block',
                        blockIndent: 0,
                        leading: 0,
                        indent: 0,
                        rightMargin: 5,
                        leftMargin: 5,
                        align: 'left',
                        url: '',
                        color: 0,
                        size: 16,
                        font: 'Arial',
                    },
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 80, xtop: 362 },
                    xcaptionSize: { xwidth: 180, xheight: 60 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'global' },
                    xdrawBehaviorCode: 'K_API_RECT',
                    xshapeType: 'apishape',
                    uniqueID: 'a76519f1-2d00-44a3-acf0-cae2c247d3d0',
                },
                {
                    xanchors: [
                        { x: 138, y: 360 },
                        { x: 318, y: 360 },
                        { x: 318, y: 380 },
                        { x: 138, y: 380 },
                    ],
                    xtextFormat: {
                        letterSpacing: 0,
                        display: 'block',
                        blockIndent: 0,
                        leading: 0,
                        indent: 0,
                        rightMargin: 5,
                        leftMargin: 5,
                        align: 'left',
                        url: '',
                        color: 0,
                        size: 16,
                        font: 'Arial',
                    },
                    xtext: 'US002',
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 138, xtop: 382 },
                    xcaptionSize: { xwidth: 180, xheight: 60 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'global' },
                    xdrawBehaviorCode: 'K_API_RECT',
                    xshapeType: 'apishape',
                    uniqueID: '7602c891-abd6-4e2b-a720-f9d7cf2faa1a',
                },
                {
                    xsize: { xwidth: 36, xheight: 36 },
                    xcenter: { x: 0, y: 0 },
                    eltid: 'building-1',
                    bibid: 'business-icons',
                    xactionProps: { xtype: 'none' },
                    xlinkProps: { xtype: 'none' },
                    xanchors: [
                        { x: 92, y: 352 },
                        { x: 128, y: 352 },
                        { x: 128, y: 388 },
                        { x: 92, y: 388 },
                    ],
                    xtext: '',
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 0, xtop: 0 },
                    xcaptionSize: { xwidth: 0, xheight: 0 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'none' },
                    xdrawBehaviorCode: null,
                    xshapeType: 'fileshape',
                    uniqueID: '35d028eb-b171-4531-b0a2-dd690bf3d437',
                },
                {
                    xstrokeProps: {
                        xend: { xtype: 'none' },
                        xstart: { xtype: 'none' },
                        xalpha: 100,
                        xcolor: '#CCD6DB',
                        xthickness: 1,
                        xtype: 'solidstroke',
                    },
                    xfillProps: { xgtype: 'linear', xalpha: 100, xcolor: '#E6EBED', xtype: 'solidfill' },
                    xanchors: [
                        { x: 80, y: 580 },
                        { x: 260, y: 580 },
                        { x: 260, y: 640 },
                        { x: 80, y: 640 },
                    ],
                    xtextFormat: {
                        letterSpacing: 0,
                        display: 'block',
                        blockIndent: 0,
                        leading: 0,
                        indent: 0,
                        rightMargin: 5,
                        leftMargin: 5,
                        align: 'left',
                        url: '',
                        color: 0,
                        size: 16,
                        font: 'Arial',
                    },
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 80, xtop: 602 },
                    xcaptionSize: { xwidth: 180, xheight: 60 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'global' },
                    xdrawBehaviorCode: 'K_API_RECT',
                    xshapeType: 'apishape',
                    uniqueID: '7bd6f234-3db7-4332-ba50-40795048e5c7',
                },
                {
                    xanchors: [
                        { x: 138, y: 600 },
                        { x: 318, y: 600 },
                        { x: 318, y: 620 },
                        { x: 138, y: 620 },
                    ],
                    xtextFormat: {
                        letterSpacing: 0,
                        display: 'block',
                        blockIndent: 0,
                        leading: 0,
                        indent: 0,
                        rightMargin: 5,
                        leftMargin: 5,
                        align: 'left',
                        url: '',
                        color: 0,
                        size: 16,
                        font: 'Arial',
                    },
                    xtext: 'US004',
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 138, xtop: 622 },
                    xcaptionSize: { xwidth: 180, xheight: 60 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'global' },
                    xdrawBehaviorCode: 'K_API_RECT',
                    xshapeType: 'apishape',
                    uniqueID: '41d24c8b-b908-4bc1-8687-6b6fa4201e69',
                },
                {
                    xsize: { xwidth: 36, xheight: 36 },
                    xcenter: { x: 0, y: 0 },
                    eltid: 'building-1',
                    bibid: 'business-icons',
                    xactionProps: { xtype: 'none' },
                    xlinkProps: { xtype: 'none' },
                    xanchors: [
                        { x: 92, y: 592 },
                        { x: 128, y: 592 },
                        { x: 128, y: 628 },
                        { x: 92, y: 628 },
                    ],
                    xtext: '',
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 0, xtop: 0 },
                    xcaptionSize: { xwidth: 0, xheight: 0 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'none' },
                    xdrawBehaviorCode: null,
                    xshapeType: 'fileshape',
                    uniqueID: '8b8c4bde-e90d-4391-ab18-9284e9637361',
                },
                {
                    xstrokeProps: {
                        xend: { xtype: 'none' },
                        xstart: { xtype: 'none' },
                        xalpha: 100,
                        xcolor: '#CCD6DB',
                        xthickness: 1,
                        xtype: 'solidstroke',
                    },
                    xfillProps: { xgtype: 'linear', xalpha: 100, xcolor: '#E6EBED', xtype: 'solidfill' },
                    xanchors: [
                        { x: 80, y: 900 },
                        { x: 260, y: 900 },
                        { x: 260, y: 960 },
                        { x: 80, y: 960 },
                    ],
                    xtextFormat: {
                        letterSpacing: 0,
                        display: 'block',
                        blockIndent: 0,
                        leading: 0,
                        indent: 0,
                        rightMargin: 5,
                        leftMargin: 5,
                        align: 'left',
                        url: '',
                        color: 0,
                        size: 16,
                        font: 'Arial',
                    },
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 80, xtop: 922 },
                    xcaptionSize: { xwidth: 180, xheight: 60 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'global' },
                    xdrawBehaviorCode: 'K_API_RECT',
                    xshapeType: 'apishape',
                    uniqueID: 'a771daef-e5ca-40d4-8328-28e75928602c',
                },
                {
                    xanchors: [
                        { x: 138, y: 920 },
                        { x: 318, y: 920 },
                        { x: 318, y: 940 },
                        { x: 138, y: 940 },
                    ],
                    xtextFormat: {
                        letterSpacing: 0,
                        display: 'block',
                        blockIndent: 0,
                        leading: 0,
                        indent: 0,
                        rightMargin: 5,
                        leftMargin: 5,
                        align: 'left',
                        url: '',
                        color: 0,
                        size: 16,
                        font: 'Arial',
                    },
                    xtext: 'US007',
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 138, xtop: 942 },
                    xcaptionSize: { xwidth: 180, xheight: 60 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'global' },
                    xdrawBehaviorCode: 'K_API_RECT',
                    xshapeType: 'apishape',
                    uniqueID: 'bdd655f0-70e9-4317-ad49-d4dcb0abaa3b',
                },
                {
                    xsize: { xwidth: 36, xheight: 36 },
                    xcenter: { x: 0, y: 0 },
                    eltid: 'building-1',
                    bibid: 'business-icons',
                    xactionProps: { xtype: 'none' },
                    xlinkProps: { xtype: 'none' },
                    xanchors: [
                        { x: 92, y: 912 },
                        { x: 128, y: 912 },
                        { x: 128, y: 948 },
                        { x: 92, y: 948 },
                    ],
                    xtext: '',
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 0, xtop: 0 },
                    xcaptionSize: { xwidth: 0, xheight: 0 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'none' },
                    xdrawBehaviorCode: null,
                    xshapeType: 'fileshape',
                    uniqueID: 'e4a9497c-e083-420e-a6bc-3ce1883af915',
                },
            ],
            alpha: 100,
            visible: true,
            lock: false,
            id: 0,
        },
        {
            xpropsArr: [
                {
                    xstrokeProps: {
                        xend: { xtype: 'none' },
                        xstart: { xtype: 'none' },
                        xalpha: 100,
                        xcolor: '#CCD6DB',
                        xthickness: 1,
                        xtype: 'solidstroke',
                    },
                    xfillProps: { xgtype: 'linear', xalpha: 100, xcolor: '#E6EBED', xtype: 'solidfill' },
                    xanchors: [
                        { x: 140, y: 180 },
                        { x: 320, y: 180 },
                        { x: 320, y: 240 },
                        { x: 140, y: 240 },
                    ],
                    xtextFormat: {
                        letterSpacing: 0,
                        display: 'block',
                        blockIndent: 0,
                        leading: 0,
                        indent: 0,
                        rightMargin: 5,
                        leftMargin: 5,
                        align: 'left',
                        url: '',
                        color: 0,
                        size: 16,
                        font: 'Arial',
                    },
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 140, xtop: 202 },
                    xcaptionSize: { xwidth: 180, xheight: 60 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'global' },
                    xdrawBehaviorCode: 'K_API_RECT',
                    xshapeType: 'apishape',
                    uniqueID: 'c38ce14c-6025-4809-8660-2f4b0ffdae75',
                },
                {
                    xanchors: [
                        { x: 198, y: 200 },
                        { x: 378, y: 200 },
                        { x: 378, y: 220 },
                        { x: 198, y: 220 },
                    ],
                    xtextFormat: {
                        letterSpacing: 0,
                        display: 'block',
                        blockIndent: 0,
                        leading: 0,
                        indent: 0,
                        rightMargin: 5,
                        leftMargin: 5,
                        align: 'left',
                        url: '',
                        color: 0,
                        size: 16,
                        font: 'Arial',
                    },
                    xtext: 'US001',
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 198, xtop: 222 },
                    xcaptionSize: { xwidth: 180, xheight: 60 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'global' },
                    xdrawBehaviorCode: 'K_API_RECT',
                    xshapeType: 'apishape',
                    uniqueID: '92ab5343-4435-48b7-825b-deab6d215b98',
                },
                {
                    xsize: { xwidth: 36, xheight: 36 },
                    xcenter: { x: 0, y: 0 },
                    eltid: 'factory',
                    bibid: 'business-icons',
                    xactionProps: { xtype: 'none' },
                    xlinkProps: { xtype: 'none' },
                    xanchors: [
                        { x: 152, y: 192 },
                        { x: 188, y: 192 },
                        { x: 188, y: 228 },
                        { x: 152, y: 228 },
                    ],
                    xtext: '',
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 0, xtop: 0 },
                    xcaptionSize: { xwidth: 0, xheight: 0 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'none' },
                    xdrawBehaviorCode: null,
                    xshapeType: 'fileshape',
                    uniqueID: '023f2dfb-e927-4294-b2df-df7128b4f49c',
                },
                {
                    xstrokeProps: {
                        xend: { xtype: 'none' },
                        xstart: { xtype: 'none' },
                        xalpha: 100,
                        xcolor: '#CCD6DB',
                        xthickness: 1,
                        xtype: 'solidstroke',
                    },
                    xfillProps: { xgtype: 'linear', xalpha: 100, xcolor: '#E6EBED', xtype: 'solidfill' },
                    xanchors: [
                        { x: 140, y: 260 },
                        { x: 320, y: 260 },
                        { x: 320, y: 320 },
                        { x: 140, y: 320 },
                    ],
                    xtextFormat: {
                        letterSpacing: 0,
                        display: 'block',
                        blockIndent: 0,
                        leading: 0,
                        indent: 0,
                        rightMargin: 5,
                        leftMargin: 5,
                        align: 'left',
                        url: '',
                        color: 0,
                        size: 16,
                        font: 'Arial',
                    },
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 140, xtop: 282 },
                    xcaptionSize: { xwidth: 180, xheight: 60 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'global' },
                    xdrawBehaviorCode: 'K_API_RECT',
                    xshapeType: 'apishape',
                    uniqueID: '30899c39-6326-4d52-b7bb-da71caeb64f3',
                },
                {
                    xanchors: [
                        { x: 198, y: 280 },
                        { x: 378, y: 280 },
                        { x: 378, y: 300 },
                        { x: 198, y: 300 },
                    ],
                    xtextFormat: {
                        letterSpacing: 0,
                        display: 'block',
                        blockIndent: 0,
                        leading: 0,
                        indent: 0,
                        rightMargin: 5,
                        leftMargin: 5,
                        align: 'left',
                        url: '',
                        color: 0,
                        size: 16,
                        font: 'Arial',
                    },
                    xtext: 'US002',
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 198, xtop: 302 },
                    xcaptionSize: { xwidth: 180, xheight: 60 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'global' },
                    xdrawBehaviorCode: 'K_API_RECT',
                    xshapeType: 'apishape',
                    uniqueID: '45e53753-bc71-44e4-8cfe-fa2e8c121caf',
                },
                {
                    xsize: { xwidth: 36, xheight: 36 },
                    xcenter: { x: 0, y: 0 },
                    eltid: 'factory',
                    bibid: 'business-icons',
                    xactionProps: { xtype: 'none' },
                    xlinkProps: { xtype: 'none' },
                    xanchors: [
                        { x: 152, y: 272 },
                        { x: 188, y: 272 },
                        { x: 188, y: 308 },
                        { x: 152, y: 308 },
                    ],
                    xtext: '',
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 0, xtop: 0 },
                    xcaptionSize: { xwidth: 0, xheight: 0 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'none' },
                    xdrawBehaviorCode: null,
                    xshapeType: 'fileshape',
                    uniqueID: 'ef33878e-66af-424b-a84c-b0bf18eb2d93',
                },
                {
                    xstrokeProps: {
                        xend: { xtype: 'none' },
                        xstart: { xtype: 'none' },
                        xalpha: 100,
                        xcolor: '#CCD6DB',
                        xthickness: 1,
                        xtype: 'solidstroke',
                    },
                    xfillProps: { xgtype: 'linear', xalpha: 100, xcolor: '#E6EBED', xtype: 'solidfill' },
                    xanchors: [
                        { x: 140, y: 420 },
                        { x: 320, y: 420 },
                        { x: 320, y: 480 },
                        { x: 140, y: 480 },
                    ],
                    xtextFormat: {
                        letterSpacing: 0,
                        display: 'block',
                        blockIndent: 0,
                        leading: 0,
                        indent: 0,
                        rightMargin: 5,
                        leftMargin: 5,
                        align: 'left',
                        url: '',
                        color: 0,
                        size: 16,
                        font: 'Arial',
                    },
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 140, xtop: 442 },
                    xcaptionSize: { xwidth: 180, xheight: 60 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'global' },
                    xdrawBehaviorCode: 'K_API_RECT',
                    xshapeType: 'apishape',
                    uniqueID: 'b7ba7252-b9f2-4789-8ee2-f77607e71bfe',
                },
                {
                    xanchors: [
                        { x: 198, y: 440 },
                        { x: 378, y: 440 },
                        { x: 378, y: 460 },
                        { x: 198, y: 460 },
                    ],
                    xtextFormat: {
                        letterSpacing: 0,
                        display: 'block',
                        blockIndent: 0,
                        leading: 0,
                        indent: 0,
                        rightMargin: 5,
                        leftMargin: 5,
                        align: 'left',
                        url: '',
                        color: 0,
                        size: 16,
                        font: 'Arial',
                    },
                    xtext: 'US004',
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 198, xtop: 462 },
                    xcaptionSize: { xwidth: 180, xheight: 60 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'global' },
                    xdrawBehaviorCode: 'K_API_RECT',
                    xshapeType: 'apishape',
                    uniqueID: '28e0772c-c73d-4e53-8019-179d30bd313f',
                },
                {
                    xsize: { xwidth: 36, xheight: 36 },
                    xcenter: { x: 0, y: 0 },
                    eltid: 'factory',
                    bibid: 'business-icons',
                    xactionProps: { xtype: 'none' },
                    xlinkProps: { xtype: 'none' },
                    xanchors: [
                        { x: 152, y: 432 },
                        { x: 188, y: 432 },
                        { x: 188, y: 468 },
                        { x: 152, y: 468 },
                    ],
                    xtext: '',
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 0, xtop: 0 },
                    xcaptionSize: { xwidth: 0, xheight: 0 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'none' },
                    xdrawBehaviorCode: null,
                    xshapeType: 'fileshape',
                    uniqueID: 'fdaefd17-e5f0-406c-8893-bf0ddd9b6c58',
                },
                {
                    xstrokeProps: {
                        xend: { xtype: 'none' },
                        xstart: { xtype: 'none' },
                        xalpha: 100,
                        xcolor: '#CCD6DB',
                        xthickness: 1,
                        xtype: 'solidstroke',
                    },
                    xfillProps: { xgtype: 'linear', xalpha: 100, xcolor: '#E6EBED', xtype: 'solidfill' },
                    xanchors: [
                        { x: 140, y: 500 },
                        { x: 320, y: 500 },
                        { x: 320, y: 560 },
                        { x: 140, y: 560 },
                    ],
                    xtextFormat: {
                        letterSpacing: 0,
                        display: 'block',
                        blockIndent: 0,
                        leading: 0,
                        indent: 0,
                        rightMargin: 5,
                        leftMargin: 5,
                        align: 'left',
                        url: '',
                        color: 0,
                        size: 16,
                        font: 'Arial',
                    },
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 140, xtop: 522 },
                    xcaptionSize: { xwidth: 180, xheight: 60 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'global' },
                    xdrawBehaviorCode: 'K_API_RECT',
                    xshapeType: 'apishape',
                    uniqueID: '29144168-8565-47d7-95d2-689bd5a332bc',
                },
                {
                    xanchors: [
                        { x: 198, y: 520 },
                        { x: 378, y: 520 },
                        { x: 378, y: 540 },
                        { x: 198, y: 540 },
                    ],
                    xtextFormat: {
                        letterSpacing: 0,
                        display: 'block',
                        blockIndent: 0,
                        leading: 0,
                        indent: 0,
                        rightMargin: 5,
                        leftMargin: 5,
                        align: 'left',
                        url: '',
                        color: 0,
                        size: 16,
                        font: 'Arial',
                    },
                    xtext: 'US003',
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 198, xtop: 542 },
                    xcaptionSize: { xwidth: 180, xheight: 60 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'global' },
                    xdrawBehaviorCode: 'K_API_RECT',
                    xshapeType: 'apishape',
                    uniqueID: 'e02091b9-e0fe-4774-a96d-20c5dee0c924',
                },
                {
                    xsize: { xwidth: 36, xheight: 36 },
                    xcenter: { x: 0, y: 0 },
                    eltid: 'factory',
                    bibid: 'business-icons',
                    xactionProps: { xtype: 'none' },
                    xlinkProps: { xtype: 'none' },
                    xanchors: [
                        { x: 152, y: 512 },
                        { x: 188, y: 512 },
                        { x: 188, y: 548 },
                        { x: 152, y: 548 },
                    ],
                    xtext: '',
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 0, xtop: 0 },
                    xcaptionSize: { xwidth: 0, xheight: 0 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'none' },
                    xdrawBehaviorCode: null,
                    xshapeType: 'fileshape',
                    uniqueID: 'd71aed2a-72d3-4d7c-b69d-ddc7282b3d73',
                },
                {
                    xstrokeProps: {
                        xend: { xtype: 'none' },
                        xstart: { xtype: 'none' },
                        xalpha: 100,
                        xcolor: '#CCD6DB',
                        xthickness: 1,
                        xtype: 'solidstroke',
                    },
                    xfillProps: { xgtype: 'linear', xalpha: 100, xcolor: '#E6EBED', xtype: 'solidfill' },
                    xanchors: [
                        { x: 140, y: 660 },
                        { x: 320, y: 660 },
                        { x: 320, y: 720 },
                        { x: 140, y: 720 },
                    ],
                    xtextFormat: {
                        letterSpacing: 0,
                        display: 'block',
                        blockIndent: 0,
                        leading: 0,
                        indent: 0,
                        rightMargin: 5,
                        leftMargin: 5,
                        align: 'left',
                        url: '',
                        color: 0,
                        size: 16,
                        font: 'Arial',
                    },
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 140, xtop: 682 },
                    xcaptionSize: { xwidth: 180, xheight: 60 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'global' },
                    xdrawBehaviorCode: 'K_API_RECT',
                    xshapeType: 'apishape',
                    uniqueID: '956a7c29-ece8-4254-8c44-eb47bbc86301',
                },
                {
                    xanchors: [
                        { x: 198, y: 680 },
                        { x: 378, y: 680 },
                        { x: 378, y: 700 },
                        { x: 198, y: 700 },
                    ],
                    xtextFormat: {
                        letterSpacing: 0,
                        display: 'block',
                        blockIndent: 0,
                        leading: 0,
                        indent: 0,
                        rightMargin: 5,
                        leftMargin: 5,
                        align: 'left',
                        url: '',
                        color: 0,
                        size: 16,
                        font: 'Arial',
                    },
                    xtext: 'US005',
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 198, xtop: 702 },
                    xcaptionSize: { xwidth: 180, xheight: 60 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'global' },
                    xdrawBehaviorCode: 'K_API_RECT',
                    xshapeType: 'apishape',
                    uniqueID: 'd554d34c-8d2e-460d-9ca5-884fd2a48323',
                },
                {
                    xsize: { xwidth: 36, xheight: 36 },
                    xcenter: { x: 0, y: 0 },
                    eltid: 'factory',
                    bibid: 'business-icons',
                    xactionProps: { xtype: 'none' },
                    xlinkProps: { xtype: 'none' },
                    xanchors: [
                        { x: 152, y: 672 },
                        { x: 188, y: 672 },
                        { x: 188, y: 708 },
                        { x: 152, y: 708 },
                    ],
                    xtext: '',
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 0, xtop: 0 },
                    xcaptionSize: { xwidth: 0, xheight: 0 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'none' },
                    xdrawBehaviorCode: null,
                    xshapeType: 'fileshape',
                    uniqueID: '7145fe6a-7764-44c0-a879-54e60ed54b54',
                },
                {
                    xstrokeProps: {
                        xend: { xtype: 'none' },
                        xstart: { xtype: 'none' },
                        xalpha: 100,
                        xcolor: '#CCD6DB',
                        xthickness: 1,
                        xtype: 'solidstroke',
                    },
                    xfillProps: { xgtype: 'linear', xalpha: 100, xcolor: '#E6EBED', xtype: 'solidfill' },
                    xanchors: [
                        { x: 140, y: 740 },
                        { x: 320, y: 740 },
                        { x: 320, y: 800 },
                        { x: 140, y: 800 },
                    ],
                    xtextFormat: {
                        letterSpacing: 0,
                        display: 'block',
                        blockIndent: 0,
                        leading: 0,
                        indent: 0,
                        rightMargin: 5,
                        leftMargin: 5,
                        align: 'left',
                        url: '',
                        color: 0,
                        size: 16,
                        font: 'Arial',
                    },
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 140, xtop: 762 },
                    xcaptionSize: { xwidth: 180, xheight: 60 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'global' },
                    xdrawBehaviorCode: 'K_API_RECT',
                    xshapeType: 'apishape',
                    uniqueID: '073c8a14-df76-4fff-b498-c059bf9714ea',
                },
                {
                    xanchors: [
                        { x: 198, y: 760 },
                        { x: 378, y: 760 },
                        { x: 378, y: 780 },
                        { x: 198, y: 780 },
                    ],
                    xtextFormat: {
                        letterSpacing: 0,
                        display: 'block',
                        blockIndent: 0,
                        leading: 0,
                        indent: 0,
                        rightMargin: 5,
                        leftMargin: 5,
                        align: 'left',
                        url: '',
                        color: 0,
                        size: 16,
                        font: 'Arial',
                    },
                    xtext: 'US006',
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 198, xtop: 782 },
                    xcaptionSize: { xwidth: 180, xheight: 60 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'global' },
                    xdrawBehaviorCode: 'K_API_RECT',
                    xshapeType: 'apishape',
                    uniqueID: '7212d988-0234-44d3-8fef-aef4905d5f7f',
                },
                {
                    xsize: { xwidth: 36, xheight: 36 },
                    xcenter: { x: 0, y: 0 },
                    eltid: 'factory',
                    bibid: 'business-icons',
                    xactionProps: { xtype: 'none' },
                    xlinkProps: { xtype: 'none' },
                    xanchors: [
                        { x: 152, y: 752 },
                        { x: 188, y: 752 },
                        { x: 188, y: 788 },
                        { x: 152, y: 788 },
                    ],
                    xtext: '',
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 0, xtop: 0 },
                    xcaptionSize: { xwidth: 0, xheight: 0 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'none' },
                    xdrawBehaviorCode: null,
                    xshapeType: 'fileshape',
                    uniqueID: '14ade0e6-ef2a-429e-bda6-4b5185c2a1da',
                },
                {
                    xstrokeProps: {
                        xend: { xtype: 'none' },
                        xstart: { xtype: 'none' },
                        xalpha: 100,
                        xcolor: '#CCD6DB',
                        xthickness: 1,
                        xtype: 'solidstroke',
                    },
                    xfillProps: { xgtype: 'linear', xalpha: 100, xcolor: '#E6EBED', xtype: 'solidfill' },
                    xanchors: [
                        { x: 140, y: 820 },
                        { x: 320, y: 820 },
                        { x: 320, y: 880 },
                        { x: 140, y: 880 },
                    ],
                    xtextFormat: {
                        letterSpacing: 0,
                        display: 'block',
                        blockIndent: 0,
                        leading: 0,
                        indent: 0,
                        rightMargin: 5,
                        leftMargin: 5,
                        align: 'left',
                        url: '',
                        color: 0,
                        size: 16,
                        font: 'Arial',
                    },
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 140, xtop: 842 },
                    xcaptionSize: { xwidth: 180, xheight: 60 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'global' },
                    xdrawBehaviorCode: 'K_API_RECT',
                    xshapeType: 'apishape',
                    uniqueID: '58cdb1ef-4db2-44c0-8f7e-36553c517a86',
                },
                {
                    xanchors: [
                        { x: 198, y: 840 },
                        { x: 378, y: 840 },
                        { x: 378, y: 860 },
                        { x: 198, y: 860 },
                    ],
                    xtextFormat: {
                        letterSpacing: 0,
                        display: 'block',
                        blockIndent: 0,
                        leading: 0,
                        indent: 0,
                        rightMargin: 5,
                        leftMargin: 5,
                        align: 'left',
                        url: '',
                        color: 0,
                        size: 16,
                        font: 'Arial',
                    },
                    xtext: 'US007',
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 198, xtop: 862 },
                    xcaptionSize: { xwidth: 180, xheight: 60 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'global' },
                    xdrawBehaviorCode: 'K_API_RECT',
                    xshapeType: 'apishape',
                    uniqueID: '13920d02-8312-451d-8f5c-2daf36a9aaeb',
                },
                {
                    xsize: { xwidth: 36, xheight: 36 },
                    xcenter: { x: 0, y: 0 },
                    eltid: 'factory',
                    bibid: 'business-icons',
                    xactionProps: { xtype: 'none' },
                    xlinkProps: { xtype: 'none' },
                    xanchors: [
                        { x: 152, y: 832 },
                        { x: 188, y: 832 },
                        { x: 188, y: 868 },
                        { x: 152, y: 868 },
                    ],
                    xtext: '',
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 0, xtop: 0 },
                    xcaptionSize: { xwidth: 0, xheight: 0 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'none' },
                    xdrawBehaviorCode: null,
                    xshapeType: 'fileshape',
                    uniqueID: '3708cb0f-c855-4368-b674-f0632faa59a5',
                },
                {
                    xstrokeProps: {
                        xend: { xtype: 'none' },
                        xstart: { xtype: 'none' },
                        xalpha: 100,
                        xcolor: '#CCD6DB',
                        xthickness: 1,
                        xtype: 'solidstroke',
                    },
                    xfillProps: { xgtype: 'linear', xalpha: 100, xcolor: '#E6EBED', xtype: 'solidfill' },
                    xanchors: [
                        { x: 140, y: 980 },
                        { x: 320, y: 980 },
                        { x: 320, y: 1040 },
                        { x: 140, y: 1040 },
                    ],
                    xtextFormat: {
                        letterSpacing: 0,
                        display: 'block',
                        blockIndent: 0,
                        leading: 0,
                        indent: 0,
                        rightMargin: 5,
                        leftMargin: 5,
                        align: 'left',
                        url: '',
                        color: 0,
                        size: 16,
                        font: 'Arial',
                    },
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 140, xtop: 1002 },
                    xcaptionSize: { xwidth: 180, xheight: 60 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'global' },
                    xdrawBehaviorCode: 'K_API_RECT',
                    xshapeType: 'apishape',
                    uniqueID: 'c48cc570-a385-42f1-b996-399e1b05f26d',
                },
                {
                    xanchors: [
                        { x: 198, y: 1000 },
                        { x: 378, y: 1000 },
                        { x: 378, y: 1020 },
                        { x: 198, y: 1020 },
                    ],
                    xtextFormat: {
                        letterSpacing: 0,
                        display: 'block',
                        blockIndent: 0,
                        leading: 0,
                        indent: 0,
                        rightMargin: 5,
                        leftMargin: 5,
                        align: 'left',
                        url: '',
                        color: 0,
                        size: 16,
                        font: 'Arial',
                    },
                    xtext: 'US010',
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 198, xtop: 1022 },
                    xcaptionSize: { xwidth: 180, xheight: 60 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'global' },
                    xdrawBehaviorCode: 'K_API_RECT',
                    xshapeType: 'apishape',
                    uniqueID: 'd9596f23-8f9b-44a9-be31-b1d7d9db8850',
                },
                {
                    xsize: { xwidth: 36, xheight: 36 },
                    xcenter: { x: 0, y: 0 },
                    eltid: 'factory',
                    bibid: 'business-icons',
                    xactionProps: { xtype: 'none' },
                    xlinkProps: { xtype: 'none' },
                    xanchors: [
                        { x: 152, y: 992 },
                        { x: 188, y: 992 },
                        { x: 188, y: 1028 },
                        { x: 152, y: 1028 },
                    ],
                    xtext: '',
                    xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
                    xcaptionPos: { xleft: 0, xtop: 0 },
                    xcaptionSize: { xwidth: 0, xheight: 0 },
                    xcaptionDeltaPos: { x: 0, y: 0 },
                    xshadowProps: { xtype: 'none' },
                    xdrawBehaviorCode: null,
                    xshapeType: 'fileshape',
                    uniqueID: '3b50f75a-d71d-43b4-8672-57fa618d9fde',
                },
            ],
            alpha: 100,
            visible: true,
            lock: false,
            id: 0,
        },
    ],
    reachedGroupNum: 1,
    contentSize: { xheight: 1140, xwidth: '997.3333333333332896' },
    docDims: { xheight: 1140, xwidth: '997.3333333333332896', xtop: 0, xleft: 0 },
    currentLayerId: 1,
};

// This function is used to remove the 'uniqueID' key
const removeKey = (obj: typeof siteGroup004Chart, keyToRemove: string = 'uniqueID') =>
    JSON.parse(JSON.stringify(obj, (key, val) => (key === keyToRemove ? undefined : val)));

describe('SiteGroup visual process', () => {
    before(() => {});

    it('Compute visual process chart for site-group US004 (_id: 3)', () =>
        Test.withContext(async context => {
            const siteGroup = await context.read(xtremAuthorization.nodes.SiteGroup, {
                id: 'US004',
            });
            assert.deepEqual(
                removeKey(JSON.parse((await siteGroup.hierarchyChartContent).value)),
                removeKey(siteGroup004Chart),
            );
        }));

    it('Compute visual process chart for site-group SiteGrouping (_id: 14)', () =>
        Test.withContext(async context => {
            const siteGroup = await context.read(xtremAuthorization.nodes.SiteGroup, {
                id: 'SiteGrouping',
            });
            assert.deepEqual(
                removeKey(JSON.parse((await siteGroup.hierarchyChartContent).value)),
                removeKey(siteGroupSiteGroupingChart),
            );
        }));

    it('Compute visual process chart for site-group CompanyGrouping (_id: 10)', () =>
        Test.withContext(async context => {
            const siteGroup = await context.read(xtremAuthorization.nodes.SiteGroup, {
                id: 'CompanyGrouping',
            });
            assert.deepEqual(
                removeKey(JSON.parse((await siteGroup.hierarchyChartContent).value)),
                removeKey(siteGroupCompanyGroupingChart),
            );
        }));
});
//
