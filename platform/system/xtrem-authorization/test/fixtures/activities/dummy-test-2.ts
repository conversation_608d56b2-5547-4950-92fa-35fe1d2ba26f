import { Activity } from '@sage/xtrem-core';
import { TestActivityNode2 } from '../nodes';

export const dummyTest2 = new Activity({
    description: 'Dummy test 2',
    node: () => TestActivityNode2,
    __filename: 'dummy-test-2',
    permissions: ['read', 'create', 'update', 'delete'],
    operationGrants: {
        read: [
            {
                operations: ['test'],
            },
        ],
    },
});
