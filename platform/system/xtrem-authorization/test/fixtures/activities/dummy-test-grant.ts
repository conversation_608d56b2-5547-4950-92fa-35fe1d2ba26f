import { Activity } from '@sage/xtrem-core';
import { TestActivityNode, TestActivityNode2, TestActivityNode3, TestSiteFilterNode } from '../nodes';

export const dummyTestGrant = new Activity({
    description: 'Dummy test grant',
    node: () => TestActivityNode,
    __filename: 'dummy-test-grant',
    permissions: ['read', 'create', 'update', 'delete'],
    operationGrants: {
        lookup: [
            {
                operations: ['lookup'],
                on: [() => TestSiteFilterNode],
            },
        ],
        read: [
            {
                operations: ['read'],
                on: [() => TestActivityNode2, () => TestActivityNode3],
            },
        ],
        create: [
            {
                operations: ['create'],
                on: [() => TestActivityNode2],
            },
        ],
    },
});
