import { AttachmentManager, CoreHooks, StaticThis } from '@sage/xtrem-core';
import * as activities from './activities';
import * as nodes from './nodes';

class MockAttachmentManager implements AttachmentManager {
    private constructor() {
        // enforce singleton
    }

    // eslint-disable-next-line class-methods-use-this
    getAttachmentNode(): StaticThis<nodes.TestAttachmentAssociation> {
        return nodes.TestAttachmentAssociation;
    }

    /** @internal */
    static readonly instance = new MockAttachmentManager();
}

export function updateContext() {
    CoreHooks.getAttachmentManager = () => MockAttachmentManager.instance;
}

updateContext();
export { activities, nodes };
