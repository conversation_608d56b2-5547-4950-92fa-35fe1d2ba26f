import { Context, decorators, Node } from '@sage/xtrem-core';
import { stringDataType } from '../../../lib/data-types/data-types';

@decorators.node<TestActivityNode6>({
    isPublished: true,
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canDelete: true,
    canUpdate: true,
    hasNotes: true,
    indexes: [{ orderBy: { name: +1 }, isUnique: true }],
})
export class TestActivityNode6 extends Node {
    @decorators.stringProperty<TestActivityNode6, 'name'>({
        isPublished: true,
        isStored: true,
        dataType: () => stringDataType,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<TestActivityNode6, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => stringDataType,
    })
    readonly description: Promise<string>;

    @decorators.mutation<typeof TestActivityNode6, 'test'>({
        isPublished: true,
        parameters: [],
        return: 'string',
    })
    static test(
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        context: Context,
    ): string {
        return 'test';
    }
}
