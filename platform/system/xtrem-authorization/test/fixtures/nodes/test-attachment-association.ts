import { decorators, Node, Reference } from '@sage/xtrem-core';
import { stringDataType } from '../../../lib/data-types/data-types';
import { TestAttachment } from './test-attachment';

@decorators.node<TestAttachmentAssociation>({
    isPublished: true,
    storage: 'sql',
    isCached: true,
    isPlatformNode: true,
    indexes: [{ orderBy: { sourceNodeName: +1, sourceNodeId: +1 }, isUnique: true }],
})
export class TestAttachmentAssociation extends Node {
    @decorators.stringProperty<TestAttachmentAssociation, 'sourceNodeName'>({
        isStored: true,
        isPublished: true,
        dataType: () => stringDataType,
    })
    readonly sourceNodeName: Promise<string>;

    @decorators.integerProperty<TestAttachmentAssociation, 'sourceNodeId'>({
        isStored: true,
        isPublished: true,
    })
    readonly sourceNodeId: Promise<number>;

    @decorators.stringProperty<TestAttachmentAssociation, 'title'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        dataType: () => stringDataType,
    })
    readonly title: Promise<string>;

    @decorators.stringProperty<TestAttachmentAssociation, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => stringDataType,
    })
    readonly description: Promise<string>;

    @decorators.booleanProperty<TestAttachmentAssociation, 'isProtected'>({
        isStored: true,
        isPublished: true,
        defaultValue: false,
    })
    readonly isProtected: Promise<boolean>;

    @decorators.referenceProperty<TestAttachmentAssociation, 'attachment'>({
        isStored: true,
        isPublished: true,
        node: () => TestAttachment,
        isRequired: true,
    })
    readonly attachment: Reference<TestAttachment>;
}
