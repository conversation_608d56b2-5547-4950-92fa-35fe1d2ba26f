import { decorators, Node } from '@sage/xtrem-core';
import { stringDataType } from '../../../lib/data-types/data-types';

@decorators.node<TestLookupAccessNoRead>({
    isPublished: true,
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canDelete: true,
    canUpdate: true,
    denyReadOnLookupOnlyAccess: true,
    indexes: [{ orderBy: { name: +1 }, isUnique: true, isNaturalKey: true }],
})
export class TestLookupAccessNoRead extends Node {
    @decorators.stringProperty<TestLookupAccessNoRead, 'name'>({
        isPublished: true,
        isStored: true,
        lookupAccess: true,
        dataType: () => stringDataType,
    })
    readonly name: Promise<string>;
}
