import { decorators, Node, Reference } from '@sage/xtrem-core';
import { stringDataType } from '../../../lib/data-types/data-types';
import { TestLookupAccessParent } from './test-lookup-access-parent';

@decorators.node<TestLookupAccessVitalReference>({
    isPublished: true,
    storage: 'sql',
    isVitalReferenceChild: true,
    indexes: [{ orderBy: { name: +1 }, isUnique: true, isNaturalKey: true }],
})
export class TestLookupAccessVitalReference extends Node {
    @decorators.referenceProperty<TestLookupAccessVitalReference, 'referenceParent'>({
        isPublished: true,
        isStored: true,
        isVitalParent: true,
        node: () => TestLookupAccessParent,
    })
    readonly referenceParent: Reference<TestLookupAccessParent>;

    @decorators.stringProperty<TestLookupAccessVitalReference, 'name'>({
        isPublished: true,
        isStored: true,
        dataType: () => stringDataType,
        lookupAccess: true,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<TestLookupAccessVitalReference, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => stringDataType,
        lookupAccess: true,
    })
    readonly description: Promise<string>;

    @decorators.stringProperty<TestLookupAccessVitalReference, 'noLookup'>({
        isStored: true,
        isPublished: true,
        dataType: () => stringDataType,
    })
    readonly noLookup: Promise<string>;
}
