import { decorators, Node, Reference } from '@sage/xtrem-core';
import { stringDataType } from '../../../lib/data-types/data-types';
import { TestLookupAccessParent } from './test-lookup-access-parent';

@decorators.node<TestLookupAccessVitalCollection>({
    isPublished: true,
    storage: 'sql',
    isVitalCollectionChild: true,
    indexes: [{ orderBy: { collectionParent: 1, name: 1 }, isUnique: true, isNaturalKey: true }],
})
export class TestLookupAccessVitalCollection extends Node {
    @decorators.referenceProperty<TestLookupAccessVitalCollection, 'collectionParent'>({
        isPublished: true,
        isStored: true,
        isVitalParent: true,
        node: () => TestLookupAccessParent,
    })
    readonly collectionParent: Reference<TestLookupAccessParent>;

    @decorators.stringProperty<TestLookupAccessVitalCollection, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => stringDataType,
        lookupAccess: true,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<TestLookupAccessVitalCollection, 'description'>({
        isStored: true,
        isPublished: true,
        dataType: () => stringDataType,
        lookupAccess: true,
    })
    readonly description: Promise<string>;

    @decorators.stringProperty<TestLookupAccessVitalCollection, 'noLookup'>({
        isStored: true,
        isPublished: true,
        dataType: () => stringDataType,
    })
    readonly noLookup: Promise<string>;
}
