import { decorators, integer } from '@sage/xtrem-core';
import { stringDataType } from '../../../lib/data-types/data-types';
import { RestrictedNode } from '../../../lib/nodes/restricted-node';

@decorators.subNode<TestRestrictedNode>({
    extends: () => RestrictedNode,
    isPublished: true,
    canRead: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestRestrictedNode extends RestrictedNode {
    @decorators.stringProperty<TestRestrictedNode, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => stringDataType,
    })
    readonly code: Promise<string>;

    @decorators.integerProperty<TestRestrictedNode, 'value'>({
        isPublished: true,
        isStored: true,
    })
    readonly value: Promise<integer>;
}
