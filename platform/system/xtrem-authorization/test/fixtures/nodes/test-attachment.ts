import {
    asyncArray,
    Context,
    datetime,
    decimal,
    decorators,
    Node,
    NodeCreateData,
    UserAccess,
    ValidationSeverity,
} from '@sage/xtrem-core';
import { pick } from 'lodash';
import { stringDataType } from '../../../lib/data-types/data-types';

@decorators.node<TestAttachment>({
    isPublished: true,
    storage: 'sql',
    isCached: true,
    isPlatformNode: true,
    canDeleteMany: true,
    authorizedBy: (context, propertyOrOperation) =>
        TestAttachment.getUserAccessForAttachmentNode(context, propertyOrOperation),
    indexes: [{ orderBy: { key: +1 }, isUnique: true }],
})
export class TestAttachment extends Node {
    @decorators.stringProperty<TestAttachment, 'key'>({
        isStored: true,
        isPublished: true,
        dataType: () => stringDataType,
    })
    readonly key: Promise<string>;

    @decorators.stringProperty<TestAttachment, 'filename'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => stringDataType,
    })
    readonly filename: Promise<string>;

    @decorators.stringProperty<TestAttachment, 'mimeType'>({
        isStored: true,
        isPublished: true,
        dataType: () => stringDataType,
    })
    readonly mimeType: Promise<string>;

    @decorators.datetimeProperty<TestAttachment, 'lastModified'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        lookupAccess: true,
    })
    readonly lastModified: Promise<datetime | null>;

    @decorators.datetimeProperty<TestAttachment, 'lastDownloadDate'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        lookupAccess: true,
    })
    readonly lastDownloadDate: Promise<datetime | null>;

    @decorators.datetimeProperty<TestAttachment, 'orphanedDate'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly orphanedDate: Promise<datetime | null>;

    @decorators.integerProperty<TestAttachment, 'contentLength'>({
        isStored: true,
        isPublished: true,
    })
    readonly contentLength: Promise<decimal>;

    @decorators.stringProperty<TestAttachment, 'status'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => stringDataType,
        async adaptValue(val) {
            const status = await this.status;
            if (val === 'uploaded' && status !== 'created') {
                return this.status;
            }
            return val;
        },
        defaultValue: () => {
            return 'created';
        },
    })
    readonly status: Promise<string>;

    @decorators.stringProperty<TestAttachment, 'rejectReason'>({
        isStored: true,
        isPublished: true,
        dataType: () => stringDataType,
    })
    readonly rejectReason: Promise<string>;

    @decorators.stringProperty<TestAttachment, 'uploadUrl'>({
        isPublished: true,
        isStored: true,
        dataType: () => stringDataType,
        dependsOn: ['key', 'status'],
    })
    readonly uploadUrl: Promise<string>;

    @decorators.stringProperty<TestAttachment, 'downloadUrl'>({
        isPublished: true,
        dataType: () => stringDataType,
        dependsOn: ['key', 'status'],
        isStored: true,
    })
    readonly downloadUrl: Promise<string>;

    @decorators.mutation<typeof TestAttachment, 'create'>({
        isPublished: true,
        parameters: [
            {
                name: 'data',
                type: 'object',
                properties: {
                    _id: { type: 'string' },
                    filename: { type: 'string', isMandatory: true },
                    mimeType: { type: 'string', isMandatory: true },
                    lastModified: { type: 'datetime', isMandatory: true },
                    contentLength: { type: 'integer', isMandatory: true },
                    status: { type: 'string', isMandatory: true },
                },
                isMandatory: true,
            },
        ],
        return: {
            type: 'reference',
            node: () => TestAttachment,
        },
    })
    static async create(context: Context, data: NodeCreateData<TestAttachment>): Promise<TestAttachment> {
        // pick only expected values to get rid of status
        const file = await context.create(
            TestAttachment,
            pick(data, ['filename', 'mimeType', 'lastModified', 'contentLength']),
        );

        await file.$.save();
        return file;
    }

    @decorators.mutation<typeof TestAttachment, 'update'>({
        isPublished: true,
        parameters: [
            {
                name: 'data',
                type: 'object',
                properties: {
                    _id: { type: 'string', isMandatory: true },
                    filename: { type: 'string' },
                    mimeType: { type: 'string' },
                    lastModified: { type: 'datetime' },
                    contentLength: { type: 'integer' },
                    status: { type: 'string' },
                },
                isMandatory: true,
            },
        ],
        return: {
            type: 'reference',
            node: () => TestAttachment,
        },
    })
    static async update(context: Context, data: NodeCreateData<TestAttachment>): Promise<TestAttachment> {
        // can only update status
        const { _id, status } = data;
        const file = await context.read(TestAttachment, { _id }, { forUpdate: true });
        const fileStatus = await file.status;
        switch (fileStatus) {
            case status:
                // no change required
                break;
            case 'created':
                if (status === 'uploaded' || status === 'uploadFailed') {
                    await file.$.set({ status });
                    await file.$.save();
                }
                break;
            default:
                context.diagnoses.push({
                    message: context.localize(
                        '@sage/xtrem-upload/nodes__uploaded_file__status-unchanged',
                        "The status cannot change from '{{from}}' to '{{to}}'.",
                        {
                            from: fileStatus,
                            to: status,
                        },
                    ),
                    path: [],
                    severity: ValidationSeverity.warn,
                });
                break;
        }
        return file;
    }

    static async getUserAccessForAttachmentNode(context: Context, propertyOrOperation: string): Promise<UserAccess> {
        const attachmentFactory = context.application.getFactoryByConstructor(TestAttachment);

        const attachmentProperty =
            attachmentFactory.properties.find(p => p.isPublished && p.name === propertyOrOperation) ??
            attachmentFactory.publishedSystemProperties[propertyOrOperation.substring(1)];
        // if the operation is a published property of the UploadedFile node then it is authorized
        if (attachmentProperty != null) return { sites: null, accessCodes: null, status: 'authorized' };

        // if the operation on the attachment node is not a standard operation, then it is not authorized
        if (['create', 'update'].includes(propertyOrOperation)) {
            // List of all nodes with attachments
            const factoriesWithAttachments = context.application.getAllFactories().filter(f => f.hasAttachments);

            // current user has create/update/delete access to any node with attachments
            // then they have full access to the same operation on the Attachments node
            if (
                await asyncArray(factoriesWithAttachments).some(async f => {
                    return (
                        (await Context.accessRightsManager.getUserAccessFor(context, f.name, 'create')).status ===
                            'authorized' ||
                        (await Context.accessRightsManager.getUserAccessFor(context, f.name, 'update')).status ===
                            'authorized'
                    );
                })
            ) {
                return { sites: null, accessCodes: null, status: 'authorized' };
            }
        }

        // Resolve read as lookup
        if (propertyOrOperation === 'read') {
            const readAccess = await Context.accessRightsManager.getUserAccessFor(
                context,
                'TestAttachment',
                propertyOrOperation,
            );
            if (readAccess.status === 'authorized') {
                return readAccess;
            }
            return Context.accessRightsManager.getUserAccessFor(context, 'TestAttachment', 'lookup');
        }

        return { sites: null, accessCodes: null, status: 'unauthorized' };
    }
}
