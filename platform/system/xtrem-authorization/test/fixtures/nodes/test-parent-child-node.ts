import { Collection, decorators, Node, Reference } from '@sage/xtrem-core';
import { stringDataType } from '../../../lib/data-types/data-types';

@decorators.node<TestParentNode>({
    isPublished: true,
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canDelete: true,
    canUpdate: true,
    indexes: [{ orderBy: { name: +1 }, isUnique: true, isNaturalKey: true }],
})
export class TestParentNode extends Node {
    @decorators.stringProperty<TestParentNode, 'name'>({
        isPublished: true,
        isStored: true,
        dataType: () => stringDataType,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<TestParentNode, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => stringDataType,
    })
    readonly description: Promise<string>;

    @decorators.collectionProperty<TestParentNode, 'children'>({
        isPublished: true,
        reverseReference: 'parent',
        isVital: true,
        node: () => TestChildNode,
    })
    readonly children: Collection<TestChildNode>;
}

@decorators.node<TestChildNode>({
    isPublished: true,
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canDelete: true,
    canUpdate: true,
    isVitalCollectionChild: true,
    indexes: [{ orderBy: { name: +1 }, isUnique: true, isNaturalKey: true }],
})
export class TestChildNode extends Node {
    @decorators.referenceProperty<TestChildNode, 'parent'>({
        isStored: true,
        isPublished: true,
        node: () => TestParentNode,
        isVitalParent: true,
    })
    readonly parent: Reference<TestParentNode>;

    @decorators.stringProperty<TestChildNode, 'name'>({
        isPublished: true,
        isStored: true,
        dataType: () => stringDataType,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<TestChildNode, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => stringDataType,
    })
    readonly description: Promise<string>;
}
