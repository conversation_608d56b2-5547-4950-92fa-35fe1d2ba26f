import { Collection, decorators, Node, Reference } from '@sage/xtrem-core';
import { TestLookupAccessVitalCollection, TestLookupAccessVitalReference } from '.';
import { stringDataType } from '../../../lib/data-types/data-types';

@decorators.node<TestLookupAccessParent>({
    isPublished: true,
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canDelete: true,
    canUpdate: true,
    indexes: [{ orderBy: { name: +1 }, isUnique: true, isNaturalKey: true }],
})
export class TestLookupAccessParent extends Node {
    @decorators.stringProperty<TestLookupAccessParent, 'name'>({
        isPublished: true,
        isStored: true,
        lookupAccess: true,
        dataType: () => stringDataType,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<TestLookupAccessParent, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => stringDataType,
        lookupAccess: true,
    })
    readonly description: Promise<string>;

    @decorators.stringProperty<TestLookupAccessParent, 'noLookup'>({
        isPublished: true,
        isStored: true,
        dataType: () => stringDataType,
    })
    readonly noLookup: Promise<string>;

    @decorators.stringProperty<TestLookupAccessParent, 'otherNoLookup'>({
        isPublished: true,
        isStored: true,
        dataType: () => stringDataType,
    })
    readonly otherNoLookup: Promise<string>;

    @decorators.referenceProperty<TestLookupAccessParent, 'lookupAccessReference'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'referenceParent',
        node: () => TestLookupAccessVitalReference,
        isNullable: true,
    })
    readonly lookupAccessReference: Reference<TestLookupAccessVitalReference> | null;

    @decorators.collectionProperty<TestLookupAccessParent, 'lookupAccessCollection'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'collectionParent',
        node: () => TestLookupAccessVitalCollection,
    })
    readonly lookupAccessCollection: Collection<TestLookupAccessVitalCollection>;
}
