import { Context, decorators, Node } from '@sage/xtrem-core';
import { stringDataType } from '../../../lib/data-types/data-types';

@decorators.node<TestActivityNode2>({
    isPublished: true,
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canDelete: true,
    canUpdate: true,
    hasAttachments: true,
    indexes: [{ orderBy: { name: +1 }, isUnique: true }],
})
export class TestActivityNode2 extends Node {
    @decorators.stringProperty<TestActivityNode2, 'name'>({
        isPublished: true,
        isStored: true,
        dataType: () => stringDataType,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<TestActivityNode2, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => stringDataType,
    })
    readonly description: Promise<string>;

    @decorators.mutation<typeof TestActivityNode2, 'test'>({
        isPublished: true,
        parameters: [],
        return: 'string',
    })
    static test(
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        context: Context,
    ): string {
        return 'test';
    }
}
