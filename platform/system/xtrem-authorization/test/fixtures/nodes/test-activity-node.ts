import { decorators, Node } from '@sage/xtrem-core';
import { stringDataType } from '../../../lib/data-types/data-types';

@decorators.node<TestActivityNode>({
    isPublished: true,
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canDelete: true,
    canUpdate: true,
    indexes: [{ orderBy: { name: +1 }, isUnique: true }],
})
export class TestActivityNode extends Node {
    @decorators.stringProperty<TestActivityNode, 'name'>({
        isPublished: true,
        isStored: true,
        dataType: () => stringDataType,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<TestActivityNode, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => stringDataType,
    })
    readonly description: Promise<string>;
}
