import { decorators, Node } from '@sage/xtrem-core';
import { stringDataType } from '../../../lib/data-types/data-types';

@decorators.node<TestActivityNode4>({
    isPublished: true,
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canDelete: true,
    canUpdate: true,
    indexes: [{ orderBy: { name: +1 }, isUnique: true }],
})
export class TestActivityNode4 extends Node {
    @decorators.stringProperty<TestActivityNode4, 'name'>({
        isPublished: true,
        isStored: true,
        dataType: () => stringDataType,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<TestActivityNode4, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => stringDataType,
    })
    readonly description: Promise<string>;
}
