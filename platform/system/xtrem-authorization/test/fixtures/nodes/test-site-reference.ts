import { Node, Reference, decorators } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { stringDataType } from '../../../lib/data-types/data-types';

@decorators.node<TestSiteReference>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
})
export class TestSiteReference extends Node {
    @decorators.stringProperty<TestSiteReference, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => stringDataType,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestSiteReference, 'site'>({
        isPublished: true,
        isStored: true,
        node: () => xtremSystem.nodes.Site,
        provides: ['site'],
    })
    readonly site: Reference<xtremSystem.nodes.Site>;
}
