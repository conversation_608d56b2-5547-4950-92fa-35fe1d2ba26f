import { Node, Reference, decorators } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { stringDataType } from '../../../lib/data-types/data-types';

@decorators.node<TestSiteFilterNode>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestSiteFilterNode',
    canRead: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestSiteFilterNode extends Node {
    @decorators.stringProperty<TestSiteFilterNode, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => stringDataType,
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestSiteFilterNode, 'site'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        node: () => xtremSystem.nodes.Site,
        provides: ['site'],
    })
    readonly site: Reference<xtremSystem.nodes.Site>;

    @decorators.referenceProperty<TestSiteFilterNode, 'referencedSite'>({
        isStored: true,
        isPublished: true,
        isRequired: false,
        node: () => xtremSystem.nodes.Site,
    })
    readonly referencedSite: Reference<xtremSystem.nodes.Site>;
}
