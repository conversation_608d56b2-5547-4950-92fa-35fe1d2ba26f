import { integer } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as nodes from '../nodes';
import { loggers } from '../loggers';

const logger = loggers.siteGroupVisualProcess;

const indent = 60;
const cardHeight = 60;
const cardPadding = 20;

const maxSiteGroupLevels = 5;

const siteGroupIcon = 'building-1';
const siteIcon = 'factory';

// JSON format for the TextStream :
interface VisualProcessTemplate {
    acts: {};
    layersArr: {
        xpropsArr: any[];
        alpha: number;
        visible: boolean;
        lock: boolean;
        id: number;
    }[];
    reachedGroupNum: number;
    contentSize: {
        xheight: number;
        xwidth: number;
    };
    docDims: {
        xheight: number;
        xwidth: number;
        xtop: number;
        xleft: number;
    };
    currentLayerId: number;
}

interface VisualProcessTemplateWork {
    document: VisualProcessTemplate;
    lastYAxis: integer;
}

const emptyVisualProcessTemplate: VisualProcessTemplateWork = {
    document: xtremSystem.functions.getEmptyDocument(0, 0),
    lastYAxis: 0,
};

/**
 * Returns a document containing a tree of sites/site groups for the supplied siteGroup
 * @param siteGroup Instance of nodes.SiteGroup
 */
export const generateSiteGroupTreeChart = async (siteGroup: nodes.SiteGroup): Promise<string> => {
    return JSON.stringify((await internalGenerateSiteGroupTreeChart(siteGroup)).document);
};

/**
 * Internal function that computes recursively the site group organization chart
 * @param siteGroup Instance of nodes.SiteGroup
 * @param level Is the recursive level in the site groups tree
 * @param visualProcessDocument Is the organization chart that is being generated
 */
const internalGenerateSiteGroupTreeChart = async (
    siteGroup: nodes.SiteGroup,
    level: integer = 0,
    visualProcessDocument: VisualProcessTemplateWork = emptyVisualProcessTemplate,
): Promise<VisualProcessTemplateWork> => {
    if (level > maxSiteGroupLevels - 1) {
        return visualProcessDocument;
    }
    if (level === 0) {
        visualProcessDocument.lastYAxis = 0;
        visualProcessDocument.document = await generateSiteGroupTreeChartContainer(siteGroup);
    }

    const cardX = (levelIndent: integer = 0) => indent * levelIndent + cardPadding;
    const cardY = (levelYAxis: integer = 0) => cardHeight + cardPadding * 2 + (cardHeight + cardPadding) * levelYAxis;
    const name = (title: string = '') => xtremSystem.functions.htmlEscape(title);

    const addEmptyLayersArr = (levelLayer: integer = 0) => {
        if (!visualProcessDocument.document.layersArr[levelLayer]) {
            visualProcessDocument.document.layersArr.push({
                xpropsArr: [] as any[],
                alpha: 100,
                visible: true,
                lock: false,
                id: 0,
            });
        }
    };

    const addEntryLayersArr = (
        title: string,
        icon: string,
        x: number,
        y: number,
        entryLevel: integer = 0,
        current: boolean = false,
    ) =>
        visualProcessDocument.document.layersArr[entryLevel].xpropsArr.push(
            ...xtremSystem.functions.createRectangle(
                xtremSystem.functions.truncateCardTitle(name(title)),
                icon,
                x,
                y,
                current,
                xtremSystem.functions.calcCardWidth(xtremSystem.functions.truncateCardTitle(name(title)).length),
            ),
        );

    if ((await siteGroup.sites.toArray()) && (await siteGroup.sites.length) > 0) {
        // Generate a card for each site
        await siteGroup.sites.forEach(async site => {
            addEmptyLayersArr(level);
            // Add the rectangle and the icon to the document's layer.
            addEntryLayersArr(
                await (
                    await site.site
                ).id,
                siteIcon,
                cardX(level + 1),
                cardY(visualProcessDocument.lastYAxis),
                level,
            );
            visualProcessDocument.lastYAxis += 1;
        });
    }

    if ((await siteGroup.siteGroups.toArray()) && (await siteGroup.siteGroups.length) > 0) {
        // Generate a card for each site
        await siteGroup.siteGroups.forEach(async siteGrp => {
            addEmptyLayersArr(level);
            // Add the rectangle and the icon to the document's layer.
            addEntryLayersArr(
                await (
                    await siteGrp.siteGroup
                ).id,
                siteGroupIcon,
                cardX(level + 1),
                cardY(visualProcessDocument.lastYAxis),
                level,
            );
            visualProcessDocument.lastYAxis += 1;
            // eslint-disable-next-line no-param-reassign
            visualProcessDocument = await internalGenerateSiteGroupTreeChart(
                await siteGrp.siteGroup,
                level + 1,
                visualProcessDocument,
            );
        });
    }
    return visualProcessDocument;
};

const computeSiteGroupMaxHeight = async (
    siteGroup: nodes.SiteGroup,
    currentLevel = 1,
    maxLevels = maxSiteGroupLevels,
): Promise<{ siteGroupMaxHeight: integer }> => {
    return {
        siteGroupMaxHeight:
            currentLevel <= maxLevels
                ? ((await siteGroup.sites.length) + 1) * (cardHeight + cardPadding) +
                  (await siteGroup.siteGroups.reduce(
                      async (total, siteGrp) =>
                          total +
                          (await computeSiteGroupMaxHeight(await siteGrp.siteGroup, currentLevel + 1))
                              .siteGroupMaxHeight,
                      cardPadding,
                  ))
                : cardPadding,
    };
};

const computeSiteGroupMaxWidth = async (
    siteGroup: nodes.SiteGroup,
    currentLevel = 1,
    maxLevels = maxSiteGroupLevels,
): Promise<{ siteGroupMaxWidth: integer }> => {
    const currentSiteGroupNameLength = Math.max(
        cardPadding,
        xtremSystem.functions.calcCardWidth((await siteGroup.name).length) - cardPadding,
    );
    return {
        siteGroupMaxWidth: Math.max(
            currentSiteGroupNameLength +
                (await siteGroup.sites.reduce(
                    async (width, site) =>
                        Math.max(width, xtremSystem.functions.calcMaxWidth(width, (await (await site.site).id).length)),
                    cardPadding,
                )),
            currentLevel <= maxLevels
                ? currentSiteGroupNameLength +
                      (await siteGroup.siteGroups.reduce(
                          async (width, siteGrp) =>
                              Math.max(
                                  width,
                                  (await computeSiteGroupMaxWidth(await siteGrp.siteGroup, currentLevel + 1))
                                      .siteGroupMaxWidth,
                              ),
                          cardPadding,
                      ))
                : 0,
        ),
    };
};

const generateSiteGroupTreeChartContainer = async (siteGroup: nodes.SiteGroup) => {
    const { siteGroupMaxWidth } = await computeSiteGroupMaxWidth(siteGroup);
    const { siteGroupMaxHeight } = await computeSiteGroupMaxHeight(siteGroup);

    await logger.debugAsync(async () => `SiteGroup ${await siteGroup.name}`);
    logger.debug(() => `siteGroupMaxHeight = ${siteGroupMaxHeight}`);
    logger.debug(() => `siteGroupMaxWidth = ${siteGroupMaxWidth}`);

    const chartContainerWidth = siteGroupMaxWidth;
    const chartContainerHeight = siteGroupMaxHeight;

    const name = xtremSystem.functions.htmlEscape(await siteGroup.name);

    logger.debug(() => `SiteGroup Card Width = ${xtremSystem.functions.calcCardWidth(name.length)}`);
    logger.debug(() => `maxWidth = ${chartContainerWidth}`);
    logger.debug(() => `maxHeight = ${chartContainerHeight}`);

    const document = xtremSystem.functions.getEmptyDocument(chartContainerWidth, chartContainerHeight);

    // Add the siteGroup rectangle to the top
    document.layersArr[0].xpropsArr.push(
        ...xtremSystem.functions.createRectangle(
            xtremSystem.functions.truncateCardTitle(name),
            siteGroupIcon,
            cardPadding,
            cardPadding,
            false,
            xtremSystem.functions.calcCardWidth(xtremSystem.functions.truncateCardTitle(name).length),
        ),
    );

    return document;
};
