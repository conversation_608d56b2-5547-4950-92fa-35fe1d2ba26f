import * as xtremSystem from '@sage/xtrem-system';
import { loggers } from '../loggers';
import * as nodes from '../nodes';

const logger = loggers.authorization;

/**
 * Creation of the SiteGroup Linked to the Company
 * @param company
 */
export async function creationOfSiteGroup(company: xtremSystem.nodes.Company): Promise<nodes.SiteGroup> {
    const siteGroup = await company.$.context.create(nodes.SiteGroup, {
        isActive: true,
        id: await company.id,
        name: await company.id,
        isLegalCompany: true,
    });
    await siteGroup.$.save();
    await logger.debugAsync(async () => `Created : ${await siteGroup.id} ${await siteGroup.name}`);
    return siteGroup;
}

/**
 * Get the SiteGroup linked to the company
 * Todo : if No SiteGroup for this Company we have to create one !
 */
async function getSiteGroup(company: xtremSystem.nodes.Company): Promise<nodes.SiteGroup | null> {
    return company.$.context.tryRead(nodes.SiteGroup, { id: await company.id }, { forUpdate: true });
}

/**
 * try Delete the siteGroup created for the company
 */
export async function deleteSiteGroup(company: xtremSystem.nodes.Company): Promise<boolean> {
    const siteGroup = await company.$.context.tryRead(nodes.SiteGroup, { id: await company.id }, { forUpdate: true });
    if (!siteGroup) return true;
    return siteGroup.$.tryDelete();
}

/**
 *  Return the SiteGroup linked to the company,
 * @param company
 */
async function getCreateSiteGroup(company: xtremSystem.nodes.Company): Promise<nodes.SiteGroup> {
    const siteGroup = await getSiteGroup(company);

    if (siteGroup) {
        return siteGroup;
    }

    await creationOfSiteGroup(company);
    return (await getSiteGroup(company))!;
}

/**
 * Add the site on the collection of Site of the SiteGroup
 * @param site
 */
export async function creationOfSiteGroupToSite(site: xtremSystem.nodes.Site): Promise<void> {
    const siteGroup = await getCreateSiteGroup(await site.legalCompany);
    await siteGroup.sites.append({ site, isValid: false });
    await siteGroup.$.save();
    await logger.debugAsync(
        async () =>
            `Added : ${await site.id} to ${await siteGroup.name} contains : ${await siteGroup.sites.length} sites `,
    );
}

/**
 * Delete all sites from group
 * @param site
 */
export function deleteOfSiteGroupToSite(site: xtremSystem.nodes.Site): Promise<boolean> {
    return site.$.context
        .query(
            nodes.SiteGroupToSite,

            {
                filter: { site: site._id },

                forUpdate: true,
            },
        )
        .every(siteGroupToSite => siteGroupToSite.$.tryDelete());
}
