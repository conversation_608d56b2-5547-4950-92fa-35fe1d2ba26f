/* eslint-disable @typescript-eslint/no-unused-vars */
import {
    AccessRightsManager,
    Activity,
    ActivityInfo,
    adminDemoPersona,
    AnyFilterObject,
    asyncArray,
    AsyncResponse,
    Context,
    CreateAdminUserOptions,
    Dict,
    FlattenedPermission,
    NodeCreateData,
    NodeFactory,
    Property,
    rootUserEmail,
    SystemError,
    UserAccess,
    UserData,
    UserInfo,
    UserNavigationInfo,
} from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as _ from 'lodash';
import { loggers } from '../loggers';
import {
    Activity as ActivityNode,
    GroupRoleSite,
    Role,
    RoleActivity,
    SiteGroup,
    UserBillingRole,
    UserGroup,
} from '../nodes';
import { authorizationServiceOption } from '../service-options/authorization-service-option';

const logger = loggers.authorization;
const User = xtremSystem.nodes.User;

interface UserRecord {
    _id: string | number;
    email: string;
    is_active?: boolean;
    first_name?: string;
    last_name?: string;
    is_administrator?: boolean;
    is_api_user?: boolean;
    is_demo_persona?: boolean;
    operator_id?: string;
}
export interface ActivityCsvExportDefinition {
    name: string;
    description: string;
    permissions: string[];
    filename: string;
}

/**
 * GroupAccess is a Dict with GroupRoleSite.id as key and an array of Site._id as value
 * Used to flatten and store all sites related to a GroupRoleSite
 */
export type GroupAccess = Dict<number[]>;

/**
 * The key of OperationAccess is <Node.name>.<operation> with GroupAccess as value
 * Used to store GroupAccess per operation
 */
export type OperationAccess = Dict<GroupAccess>;

/**
 * CalculatedUserAccess is a Dict with <Node.name>.<operation> as key and an array of Site._id as value
 */
export type CalculatedUserAccess = Dict<number[]>;

const userAccess = {
    unavailable: { sites: [], accessCodes: [], status: 'unavailable' } as UserAccess,
    unauthorized: { sites: [], accessCodes: [], status: 'unauthorized' } as UserAccess,
    full: { sites: null, accessCodes: null, status: 'authorized' } as UserAccess,
};

Object.freeze(userAccess);

const rootUser: NodeCreateData<xtremSystem.nodes.User> = {
    email: rootUserEmail,
    firstName: 'Root',
    lastName: 'Sage',
    isActive: true,
    isAdministrator: true,
    userType: 'system',
};

// The root user is a system user and should not be modified
Object.freeze(rootUser);

export class SysAccessRightsManager implements AccessRightsManager {
    /**
     * Get current user's access rights to a specific operation
     * @param context
     * @param nodeName Name of the node for the operation
     * @param propertyOrOperation Operation name
     * @returns
     *  An array of Site ids to which the user has access, or an empty array if no restriction on Sites
     *  Returns undefined if no access to the operation
     */
    async getUserAccessFor(context: Context, nodeName: string, propertyOrOperation: string): Promise<UserAccess> {
        // First look if the bound element carries a serviceOption or is conditioned by a package being enabled.
        // If so and if the serviceOption or package is disabled, return status 'unavailable'
        const factory = context.application.getFactoryByName(nodeName);

        // pack allocation does not contain the hidden package so we need to check the active packages
        const isPackActive = (await context.getActivePackageNames()).includes(factory.package.name);
        if (!isPackActive || !(await factory.isEnabledByServiceOptions(context))) return userAccess.unavailable;

        // otherwise call the authorization manager to find if the user is authorized or not on the bound node/property/method,
        // and then add an entry with status 'authorized' or 'unauthorized'
        // Access statuses:
        //   - unavailable: it belongs to package which is not active or it is controlled by a service option
        //                  which is not active in the current tenant.
        //                  when checking access on a property, check factory and property service options.
        //   - unauthorized: it is available but the current user is not Authorized for the operation.
        //   - authorized: access granted
        let operation = propertyOrOperation;
        const nodeOperation =
            factory.mutations.find(mutation => mutation.name === operation) ||
            factory.queries.find(query => query.name === operation);
        const property = factory.propertiesByName[operation];
        if (nodeOperation != null) {
            if (
                nodeOperation.serviceOptions &&
                !(await asyncArray(nodeOperation.serviceOptions()).every(serviceOption =>
                    context.isServiceOptionEnabled(serviceOption),
                ))
            ) {
                return userAccess.unavailable;
            }
        } else if (property != null) {
            // if the property is a lookupAccess property of the MetaNodeFactory node then it is authorized
            // otherwise continue to check the access rights
            if (nodeName === 'MetaNodeFactory' && property.isPublished && property.lookupAccess) return userAccess.full;

            const propertyAccess = await SysAccessRightsManager.getUserAccessForProperty(context, factory, property);
            if (propertyAccess) return propertyAccess;

            // resolve required access to a lookup operation for properties that have lookupAccess defined
            // as well as the vital graph of the node, ie. vital parent, references and collections,
            // otherwise require at least read access for other properties
            operation = property.lookupAccess ? 'lookup' : 'read';
        }

        if ((await context.user)?.isAdministrator) return userAccess.full;

        //  check if the service option that controls authorization is active, if not then grant all access
        // TODO: remove later
        if (!(await this.isAuthorizationServiceOptionEnabled(context))) return userAccess.full;

        // Check User access to granted operations
        const calculatedUserAccess = await SysAccessRightsManager.getUserAccess(context);
        // Access to asyncExport is inherited if the user has access to read
        if (operation === 'asyncExport') {
            if (!factory.canExport) return userAccess.unauthorized;
            operation = 'read';
        }
        const access = calculatedUserAccess[`${nodeName}.${operation}`];
        // TODO: Remove once access is settled
        if (!context.isNodeAccessControlled(factory) && !access) {
            logger.warn(() => `${nodeName} is not access controlled`);
            return userAccess.full;
        }

        if (access) {
            return {
                sites: access.length === 0 ? null : access.map(id => id.toString()),
                accessCodes: null,
                status: 'authorized',
            };
        }
        return userAccess.unauthorized;
    }

    private static async getUserAccessForProperty(
        context: Context,
        factory: NodeFactory,
        property: Property,
    ): Promise<UserAccess | undefined> {
        if (!(await property.isEnabledByServiceOptions(context))) return userAccess.unavailable;

        // The user is accessing the _attachments property, if they have access to perform and action on the node,
        // then they have full access to the _attachments property
        if (factory.hasAttachments && property.name === '_attachments') return userAccess.full;
        // The user is accessing the _notes property, if they have access to perform and action on the node,
        // then they have full access to the _notes property
        if (factory.hasNotes && property.name === '_notes') return userAccess.full;
        // The user is accessing the _factory property, if the node is readable, then they have access to the _factory property
        if (factory.canRead && property.name === '_factory') return userAccess.full;
        return undefined;
    }

    // eslint-disable-next-line class-methods-use-this, require-await
    async isAccessCodeAvailable(context: Context, accessCode: string): Promise<boolean> {
        // TODO: Property level access to be implemented
        return true;
    }

    async getOperationSecurityFilter(
        context: Context,
        factory: NodeFactory,
        operation: string,
    ): Promise<AnyFilterObject | undefined> {
        if (factory.storage === 'external') return undefined;

        if (factory.name === this.getUserNode().name) {
            const access = await Context.accessRightsManager.getUserAccessFor(context, factory.name, operation);
            return access.status === 'authorized' ? undefined : { _id: (await context.user)?._id };
        }
        return undefined;
    }

    // eslint-disable-next-line class-methods-use-this
    async createAdminUser(context: Context, data: UserData, options?: CreateAdminUserOptions): Promise<void> {
        let adminUser = await context.tryRead(User, { email: data.email });
        // if the user is the first admin user, we need to update the existing first admin user to false
        if (options?.isFirstAdminUser) {
            const sql = `UPDATE ${context.schemaName}.user SET is_first_admin_user = false WHERE _tenant_id=$1 AND is_first_admin_user=true`;
            await context.executeSql(sql, [context.tenantId]);
        }

        if (!adminUser) {
            adminUser = await context.create(User, {
                ...data,
                isAdministrator: true,
                isFirstAdminUser: options?.isFirstAdminUser,
            });

            // Try to get groupRoleSite from setup delivered ADMIN
            const groupRoleSite = await context.tryRead(GroupRoleSite, { id: 'Admin' });
            if (groupRoleSite) {
                // The groupSite is present
                const userGroup: NodeCreateData<UserGroup> = {
                    isActive: true,
                    group: groupRoleSite._id,
                };
                // Add admin user group to user
                await adminUser.authorizationGroup.append(userGroup);
            }

            await adminUser.$.save();
        } else {
            const sql = `UPDATE ${context.schemaName}.user SET is_first_admin_user = $1, is_administrator = true WHERE email=$2 AND _tenant_id=$3`;
            await context.executeSql(sql, [options?.isFirstAdminUser === true, data.email, context.tenantId]);
        }

        if (!options?.skipWelcomeEmail) {
            await User.sendWelcomeMail(context, [adminUser], true);
            await logger.infoAsync(async () => `Send welcome mail for ${(await adminUser?.email) || ''}`);
        }
    }

    // eslint-disable-next-line class-methods-use-this
    async createRequiredUsers(context: Context): Promise<void> {
        // shallow copy of the root user object to avoid modifying the original
        const rootUserCopy = { ...rootUser };
        if (!(await context.tryRead(User, { email: rootUserCopy.email }))) {
            const userType = rootUserCopy.userType;
            // we cannot save or update a system user so we first create it as an 'application' user
            if (userType === 'system') {
                rootUserCopy.userType = 'application';
            }
            const createUser = await context.create(User, rootUserCopy);
            await createUser.$.save();
            // then update to force its _id to 1 and set its userType back to 'system'
            if (userType === 'system') {
                const sql = `UPDATE ${context.schemaName}.user SET _id = 1, _create_user = 1, _update_user = 1, user_type = $1 WHERE email = $2 AND _tenant_id = $3`;
                await context.executeSql(sql, ['system', rootUserCopy.email, context.tenantId]);
            }
        }
    }

    // eslint-disable-next-line class-methods-use-this
    async ensureAdminPersonaCreated(context: Context): Promise<UserInfo> {
        const adminPersona = await context.tryRead(User, { email: adminDemoPersona.email });
        if (adminPersona) {
            return adminPersona.toUserInfo();
        }
        const user = await context.create(User, {
            ..._.omit(adminDemoPersona, '_id'),
            email: adminDemoPersona.email,
        });
        // Try to get groupRoleSite from setup delivered ADMIN
        const groupRoleSite = await context.tryRead(GroupRoleSite, { id: 'Admin' });
        if (groupRoleSite) {
            // The groupSite is present
            const userGroup: NodeCreateData<UserGroup> = {
                isActive: true,
                group: groupRoleSite._id,
            };
            // Add admin user group to user
            await user.authorizationGroup.append(userGroup);
        }
        await user.$.save();
        // we can log persona email, they are fake email
        logger.info(
            `${(context as any).debugPath}) Created admin persona ${adminDemoPersona._id} with email '${
                adminDemoPersona.email
            }'`,
        );
        return user.toUserInfo();
    }

    // eslint-disable-next-line class-methods-use-this
    private async getUserFromDatabase(context: Context, email: string): Promise<UserInfo> {
        // PERFORMANCE: Use executeSql to avoid the overhead of the ORM
        const userInfo = (
            await context.executeSql<UserRecord[]>(
                `SELECT
                _id, email, is_active, first_name, last_name,
                 is_administrator, is_api_user, is_demo_persona, operator_id
            FROM ${context.schemaName}.user WHERE _tenant_id=$1 AND email = $2`,
                [context.tenantId, email],
            )
        ).shift();
        return (
            userInfo
                ? {
                      _id: userInfo._id,
                      email: userInfo.email,
                      isActive: userInfo.is_active,
                      firstName: userInfo.first_name,
                      lastName: userInfo.last_name,
                      isAdministrator: userInfo.is_administrator,
                      isApiUser: userInfo.is_api_user,
                      isDemoPersona: userInfo.is_demo_persona,
                      userName: userInfo.email,
                      isOperatorUser: !!userInfo.operator_id,
                  }
                : {}
        ) as UserInfo;
    }

    getUser(context: Context, email: string): Promise<UserInfo> {
        return context.getCachedValue({
            category: 'AUTHORIZATION',
            key: `USERINFO:${email}`,
            getValue: async () => ({
                value: await this.getUserFromDatabase(context, email),
            }),
            cacheInMemory: true,
            ttlInSeconds: 300, // 5 minutes
        });
    }

    // eslint-disable-next-line class-methods-use-this
    async getCurrentUser(context: Context): Promise<UserInfo> {
        // For security, the default cannot be the root user, the context needs to be correctly initialized
        try {
            const user = await context.user;
            if (!user) return await context.rootUser;
            return { _id: String(user._id), email: user.email };
        } catch {
            return context.rootUser;
        }
    }

    // eslint-disable-next-line class-methods-use-this
    getUserNode() {
        return User;
    }

    async getPermissions(context: Context, activity: string): Promise<string[]> {
        const permissions = [] as string[];
        const objActivity = context.getActivities()[activity] as Activity;
        if (!objActivity) {
            logger.warn(` ${activity}, trying to get permissions for an activity that does not exist.`);
            return [];
        }

        if ((await context.user)?.isAdministrator) return Object.keys(objActivity.flattenedPermissions);

        await asyncArray(Object.keys(objActivity.flattenedPermissions)).forEach(async permission => {
            const userPermissions = await this.getUserAccessFor(context, objActivity.node.name, permission);
            if (userPermissions.status === 'authorized') permissions.push(permission);
        });
        return permissions;
    }

    /**
     * Get the complete list of permissions for a given role
     * @param context
     * @param role
     * @returns
     */
    static async getRoleFlattenedPermissions(context: Context, role: Role): Promise<FlattenedPermission> {
        const permissions: FlattenedPermission = {};
        const activities = context.getActivities();

        const processedRoles: number[] = [];

        const walk = async (r: Role): Promise<void> => {
            if (processedRoles.includes(r._id)) return;

            // If the role is inactive nothing to do
            if (!(await r.isActive)) {
                processedRoles.push(r._id);
                return;
            }

            await r.activities.forEach(async roleActivity => {
                // If the role activity is inactive nothing to do
                if (!(await roleActivity.isActive)) return;
                const activity = activities[await (await roleActivity.activity).name];

                if (!activity) {
                    logger.warn(
                        `Activity ${await (
                            await roleActivity.activity
                        ).name}, referenced from Role ${await r.name}, was not loaded by the application.`,
                    );
                    return;
                }
                const { flattenedPermissions } = activity;

                // get the permissions allocated to the role activity
                const roleActivityPermissions = [...((await roleActivity.getPermissions) || [])];

                // Add all permissions granted by the each role activity
                roleActivityPermissions?.forEach(permission => {
                    const operationNodeDict = flattenedPermissions[permission] || {};

                    // add the permissions per operation and list of nodes
                    Object.keys(operationNodeDict).forEach(operation => {
                        if (permissions[operation] == null) {
                            permissions[operation] = [...operationNodeDict[operation]];
                        } else {
                            operationNodeDict[operation].forEach(node => {
                                if (!permissions[operation].some(n => n === node)) {
                                    permissions[operation].push(node);
                                }
                            });
                        }
                    });
                });

                processedRoles.push(r._id);

                // Recursively add sub roles
                await r.roles.forEach(async subRole => walk(await subRole.role));
            });
        };

        await walk(role);

        return { ...permissions };
    }

    /**
     * Returns all the sites related to a GroupRoleSite
     */
    static async getSites(groupRoleSite: GroupRoleSite): Promise<number[]> {
        const result: Set<number> = new Set();
        const processed: Set<number> = new Set();

        const walk = async (siteGroup: SiteGroup): Promise<void> => {
            if (processed.has(siteGroup._id)) return;
            processed.add(siteGroup._id);

            await siteGroup.sites.forEach(async siteGroupSite => {
                result.add((await siteGroupSite.site)._id);
            });
            await siteGroup.siteGroups.forEach(async siteGroupToSiteGroup =>
                walk(await siteGroupToSiteGroup.siteGroup),
            );
        };

        await groupRoleSite.groupSites.forEach(async groupSite => walk(await groupSite.siteGroup));

        return Array.from(result);
    }

    /**
     * calculate access rights for all roles
     * @param context
     * @returns
     */
    static async calculateAllOperationAccess(context: Context): Promise<OperationAccess> {
        const accessRights: OperationAccess = {};
        const groupRoleSites = await context.query(GroupRoleSite).toArray();
        const groupRoleSiteSites: GroupAccess = {};
        const rolePermissions: Map<number, FlattenedPermission> = new Map();

        await asyncArray(groupRoleSites).forEach(groupRoleSite =>
            groupRoleSite.groupRoles.forEach(async groupRole => {
                if (!rolePermissions.has((await groupRole.role)._id))
                    rolePermissions.set(
                        (await groupRole.role)._id,
                        await this.getRoleFlattenedPermissions(context, await groupRole.role),
                    );
                const rolePermission = rolePermissions.get((await groupRole.role)._id)!;

                await asyncArray(Object.keys(rolePermission)).forEach(operationKey =>
                    asyncArray(rolePermission[operationKey]).forEach(async node => {
                        const operation = `${node}.${operationKey}`;
                        if (accessRights[operation] && accessRights[operation][await groupRoleSite.id]) return;
                        if (!accessRights[operation]) accessRights[operation] = {};
                        if (!groupRoleSiteSites[await groupRoleSite.id])
                            groupRoleSiteSites[await groupRoleSite.id] = await this.getSites(groupRoleSite);

                        if (!accessRights[operation][await groupRoleSite.id]) {
                            accessRights[operation][await groupRoleSite.id] =
                                groupRoleSiteSites[await groupRoleSite.id];
                        } else {
                            accessRights[operation][await groupRoleSite.id] = _.uniq([
                                ...accessRights[operation][await groupRoleSite.id],
                                ...groupRoleSiteSites[await groupRoleSite.id],
                            ]);
                        }
                    }),
                );
            }),
        );
        return accessRights;
    }

    /**
     * get object of access rights for all roles
     * @param context
     * @returns
     */
    static getAllOperationAccess(context: Context): Promise<OperationAccess> {
        return context.getCachedValue({
            category: 'AUTHORIZATION',
            key: 'ALL_OPERATION_ACCESS',
            getValue: async () => ({ value: await this.calculateAllOperationAccess(context) }),
            cacheInMemory: true,
            ttlInSeconds: 3600,
        });
    }

    /**
     * Calculate the access of the current user based on the allocate group role sites
     * @param context
     * @returns
     */
    static async calculateUserAccess(context: Context): Promise<CalculatedUserAccess> {
        const result: CalculatedUserAccess = {}; // Empty object means no access

        const userGroupRoleSites = await context
            .query(UserGroup, { filter: { user: (await context.user)?._id } })
            .map(async userGroup => (await userGroup.group).id)
            .toArray();

        if (userGroupRoleSites.length > 0) {
            const operationGroups = await this.getAllOperationAccess(context);

            Object.keys(operationGroups).forEach(operation => {
                let allSites = false;
                userGroupRoleSites.forEach(userGroupRoleSite => {
                    if (allSites) return;
                    if (operationGroups[operation][userGroupRoleSite]) {
                        if (operationGroups[operation][userGroupRoleSite].length === 0) {
                            allSites = true;
                            result[operation] = [];
                            return;
                        }
                        result[operation] = [
                            ...(result[operation] || []),
                            ...operationGroups[operation][userGroupRoleSite],
                        ];
                    }
                });
                if (!allSites && result[operation]) result[operation] = Array.from(new Set(result[operation]));
            });

            const billingRole = await context.tryRead(UserBillingRole, { user: (await context.user)?._id });

            // If there is a billing role for the user, intersect his rights with the billing role
            if (billingRole) {
                const billingRolePermissions = await this.getRoleFlattenedPermissions(context, await billingRole.role);

                // The flattened permissions is a dictionary with the operation (create, read etc) as the key and an array of node names as the value.
                // The calculated permissions is keyed on the full operation, eg. NodeName.operation with a list of sites to which the user has access as the value.
                //
                // To get the intersection of rights, we iterate the flattened permissions per operation per node and for each combination we check if that
                // operation permission exists and copy the calculated rights to the intersected rights

                const intersect: CalculatedUserAccess = {};
                if (billingRolePermissions) {
                    await asyncArray(Object.keys(billingRolePermissions)).forEach(operationKey =>
                        asyncArray(billingRolePermissions[operationKey]).forEach(node => {
                            const operation = `${node}.${operationKey}`;
                            if (result[operation]) {
                                intersect[operation] = result[operation];
                            }
                        }),
                    );
                    return intersect;
                }
            }
        }
        return result;
    }

    // eslint-disable-next-line class-methods-use-this
    invalidateAuthorizationCache(context: Context): Promise<void> {
        return SysAccessRightsManager.invalidateUserAccessCache(context);
    }

    /**
     * Invalidate the User Access related cache for the current tenant
     * @param context
     */
    static invalidateUserAccessCache(context: Context): Promise<void> {
        return context.invalidateCachedCategory('AUTHORIZATION', {});
    }

    /**
     * Get the current users access to the their allocated roles
     * @param context
     * @returns
     */
    static async getUserAccess(context: Context): Promise<CalculatedUserAccess> {
        const cached = await context.getCachedValue({
            category: 'AUTHORIZATION', // global cache is tenant specific therefore the tenantId is not required in the category
            key: `USER:${(await context.user)!._id.toString()}`,

            getValue: async () => ({ value: await this.calculateUserAccess(context) }),
            cacheInMemory: true,
            ttlInSeconds: 3600,
        });
        return cached;
    }

    // eslint-disable-next-line class-methods-use-this
    private async activityIsUpToDate(
        dbActivity: ActivityNode,
        activityData: NodeCreateData<ActivityNode> & { package: { name: string } },
    ): Promise<boolean> {
        return (
            (await dbActivity.name) === activityData.name &&
            (await (await dbActivity.package).name) === activityData.package.name &&
            (await dbActivity.description) === activityData.description &&
            (await dbActivity.permissions).join() === activityData.permissions!.join()
        );
    }

    // eslint-disable-next-line class-methods-use-this
    async createActivities(context: Context): Promise<void> {
        if (!context.withoutTransactionUser) {
            const user = await context.user;
            if (user?.email !== rootUserEmail) {
                throw new SystemError('Cannot create activities: must be a root user');
            }
        }

        const activitiesInDatabase = await context.query(ActivityNode, {}).toArray();

        await asyncArray(Object.values(context.application.activities)).forEach(async activity => {
            const dbActivity = await asyncArray(activitiesInDatabase).find(
                async act => (await act.name) === activity.name,
            );

            if (!dbActivity) {
                logger.info(`Creating activity ${activity.name}`);
                const activityData = {
                    name: activity.name,
                    package: { name: activity.package },
                    description: activity.description,
                    permissions: activity.permissions,
                };
                await (await context.create(ActivityNode, activityData)).$.save();
            }
        });
    }

    async updateActivities(context: Context): Promise<void> {
        if (!context.withoutTransactionUser) {
            const user = await context.user;
            if (user?.email !== rootUserEmail) {
                throw new SystemError('Cannot update activties: must be a root user');
            }
        }

        const activitiesInDatabase = await context.query(ActivityNode, { forUpdate: true }).toArray();

        await asyncArray(Object.values(context.application.activities)).forEach(async activity => {
            const dbActivity = await asyncArray(activitiesInDatabase).find(
                async act => (await act.name) === activity.name,
            );

            if (dbActivity) {
                logger.info(`Creating activity ${activity.name}`);

                const activityData = {
                    name: activity.name,
                    package: { name: activity.package },
                    description: activity.description,
                    permissions: activity.permissions,
                };
                if (await this.activityIsUpToDate(dbActivity, activityData)) return;
                await dbActivity.$.set(activityData);
                await dbActivity.$.save({ useUpsert: true });
            }
        });
    }

    // eslint-disable-next-line class-methods-use-this
    async deleteActivities(context: Context): Promise<void> {
        const activitiesInDatabase = await context.query(ActivityNode, { forUpdate: true }).toArray();
        const extraDbActivities = await asyncArray(activitiesInDatabase)
            .filter(async dbActivity => context.application.activities[await dbActivity.name] == null)
            .toArray();

        // Remove extra role_activities that no longer have the activity in the current application,
        // this is to avoid FK issues during import in another cluster
        if (extraDbActivities.length > 0) {
            logger.info(
                `Deleting [${await asyncArray(extraDbActivities)
                    .map(a => a.name)
                    .toArray()}] activities and the linked role activities for ${context.tenantId}`,
            );
            const extraActivityIds = extraDbActivities.map(a => a._id);
            const roleActivityFactory = context.application.getFactoryByConstructor(RoleActivity);
            // we have to use a sql statement because we need to remove the role activities of every tenants
            const sql = `DELETE FROM ${context.schemaName}.${roleActivityFactory.tableName} WHERE ${
                roleActivityFactory.propertiesByName.activity.columnName
            } in (${extraActivityIds.join(',')});`;
            await context.executeSql(sql, []);
            await context.deleteMany(ActivityNode, { _id: { _in: extraActivityIds } });
        }
    }

    // eslint-disable-next-line class-methods-use-this
    getActivityNode() {
        return ActivityNode;
    }

    // eslint-disable-next-line class-methods-use-this
    async getActivitiesInfo(context: Context): Promise<ActivityInfo[]> {
        const activitiesInDatabase = await context.query(ActivityNode).toArray();

        return (await asyncArray(activitiesInDatabase)
            .map(async activity => {
                return { _id: activity._id, name: await activity.name };
            })
            .toArray()) as ActivityInfo[];
    }

    // eslint-disable-next-line class-methods-use-this
    supportsPersona(context: Context): AsyncResponse<boolean> {
        return context.isServiceOptionEnabled(xtremSystem.serviceOptions.isDemoTenant);
    }

    // eslint-disable-next-line class-methods-use-this
    async getPersonaUser(context: Context, email: string): Promise<UserInfo | null> {
        const user = (await context.query(User, { filter: { email, isDemoPersona: true } }).toArray())[0];
        return user ? user.toUserInfo() : null;
    }

    async getDemoPersonas(context: Context): Promise<UserInfo[]> {
        if (!(await context.supportsPersona())) return [];
        const loginUser = await context.loginUser;
        if (!loginUser) return [];
        const isAdmin = loginUser.isAdministrator || loginUser.isFirstAdminUser;
        const allPersonas = await context
            .query(User, { filter: { isDemoPersona: true } })
            .map(user => user.toUserInfo())
            .toArray();
        let adminPersonas: UserInfo[] = [];
        if (isAdmin) {
            // keep only the builtin admin persona and ignore all others
            adminPersonas = allPersonas.filter(p => p.email === adminDemoPersona.email);
            if (adminPersonas.length === 0) {
                // We need to run the creation of the admin persona in a detached context using the root user
                // to prevent from cycle in the persona resolution
                const adminPersona = await context.application.asRoot.withCommittedContext(
                    context.tenantId,
                    ctx => this.ensureAdminPersonaCreated(ctx),
                    {
                        description: () => 'AccessRightManager.ensureAdminPersonaCreated',
                    },
                );
                adminPersonas.push(adminPersona);
            }
        }
        const personas = allPersonas.filter(p => !p.isAdministrator);
        return [...adminPersonas, ...personas].filter(p => p.isActive);
    }

    // eslint-disable-next-line class-methods-use-this
    async getUserNavigation(context: Context): Promise<UserNavigationInfo> {
        const userId = (await context.user)?._id;
        const userNavigation = await context.tryRead(xtremSystem.nodes.UserNavigation, { user: userId });
        return { history: await userNavigation?.history, bookmarks: await userNavigation?.bookmarks };
    }

    /**
     * Returns whether the 'Authorization access control' service option is enabled
     */
    // eslint-disable-next-line class-methods-use-this
    isAuthorizationServiceOptionEnabled(context: Context): Promise<boolean> {
        return context.isServiceOptionEnabled(authorizationServiceOption);
    }
}

export const sysAccessRightsManager: SysAccessRightsManager = new SysAccessRightsManager();
