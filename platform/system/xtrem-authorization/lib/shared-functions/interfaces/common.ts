/**
 *  Interface for role-activity mutation
 */
export interface RoleActivityMutation {
    activity: string;
    permissions: string[];
    isActive: boolean;
    hasAllPermissions: boolean;
}

export interface RoleActivityPod {
    _id: string;
    description: string;
    isActive: boolean;
    allPermissions: string[];
    permissions: string[];
}
export interface AllRoles {
    _id: string;
    name: string;
    id: string;
    isBillingRole: boolean;
    createdBy: string;
    createStamp: string;
    updatedBy: string;
    updateStamp: string;
}

export interface AllUsers {
    _id: string;
    displayName: string;
    email: string;
    isWelcomeMailSent: boolean;
    groupDisplay: string;
    createdBy: string;
    createStamp: string;
    updatedBy: string;
    updateStamp: string;
}

export interface UserGroups {
    group: { _id: string; id: string; name: string };
}
