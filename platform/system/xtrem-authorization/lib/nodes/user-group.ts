import { datetime, decorators, Node, Reference } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { dataTypes } from '@sage/xtrem-system';
import * as xtremAuthorization from '../index';

@decorators.node<UserGroup>({
    package: 'xtrem-authorization',
    storage: 'sql',
    isPublished: true,
    isVitalCollectionChild: true,
    isCached: true,
    canRead: true,
    indexes: [
        {
            orderBy: { user: +1, group: +1 },
            isUnique: true,
        },
    ],
    async saveEnd() {
        await xtremAuthorization.services.SysAccessRightsManager.invalidateUserAccessCache(this.$.context);
    },
})
export class UserGroup extends Node {
    @decorators.booleanProperty<UserGroup, 'isActive'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        defaultValue: true,
    })
    readonly isActive: Promise<boolean>;

    @decorators.referenceProperty<UserGroup, 'user'>({
        isPublished: true,
        lookupAccess: true,
        isStored: true,
        isVitalParent: true,
        node: () => xtremSystem.nodes.User,
    })
    readonly user: Reference<xtremSystem.nodes.User>;

    @decorators.referenceProperty<UserGroup, 'group'>({
        isPublished: true,
        lookupAccess: true,
        isStored: true,
        node: () => xtremAuthorization.nodes.GroupRoleSite,
    })
    readonly group: Reference<xtremAuthorization.nodes.GroupRoleSite>;

    /**
     * Enhancement request for createdBy updatedBy  updateStamp createStamp  : https://jira.sage.com/browse/XT-12554
     */
    /**
     * createdBy user displayName
     */
    @decorators.stringProperty<UserGroup, 'createdBy'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => dataTypes.name,
        async computeValue() {
            return ((await this.$.createdBy) as xtremSystem.nodes.User).displayName;
        },
    })
    readonly createdBy: Promise<string>;

    /**
     * updatedBy user displayName
     */
    @decorators.stringProperty<UserGroup, 'updatedBy'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => dataTypes.name,
        async computeValue() {
            return ((await this.$.updatedBy) as xtremSystem.nodes.User).displayName;
        },
    })
    readonly updatedBy: Promise<string>;

    /**
     * Last update datetime
     */
    @decorators.datetimeProperty<UserGroup, 'updateStamp'>({
        isPublished: true,
        lookupAccess: true,
        computeValue() {
            return this.$.updateStamp;
        },
    })
    readonly updateStamp: Promise<datetime>;

    /**
     * Create datetime
     */
    @decorators.datetimeProperty<UserGroup, 'createStamp'>({
        isPublished: true,
        lookupAccess: true,
        computeValue() {
            return this.$.createStamp;
        },
    })
    readonly createStamp: Promise<datetime>;
}
