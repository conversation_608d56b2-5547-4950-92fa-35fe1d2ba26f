import { decorators, Node, Reference } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { nameArrayDataType } from '../data-types/data-types';
import { SysAccessRightsManager } from '../services/access-rights-manager';

@decorators.node<Activity>({
    package: 'xtrem-authorization',
    storage: 'sql',
    isCached: true,
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [
        {
            orderBy: { name: +1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    isSharedByAllTenants: true,
    isPlatformNode: true,
    async saveEnd() {
        await SysAccessRightsManager.invalidateUserAccessCache(this.$.context);
    },
})
export class Activity extends Node {
    @decorators.stringProperty<Activity, 'name'>({
        isStored: true,
        isFrozen: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<Activity, 'description'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.description,
    })
    readonly description: Promise<string>;

    @decorators.stringArrayProperty<Activity, 'permissions'>({
        isPublished: true,
        lookupAccess: true,
        isStored: true,
        dataType: () => nameArrayDataType,
    })
    readonly permissions: Promise<string[]>;

    @decorators.referenceProperty<Activity, 'package'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremSystem.nodes.SysPackVersion,
    })
    readonly package: Reference<xtremSystem.nodes.SysPackVersion>;
}
