import { asyncArray, Context, decorators, Node, Reference } from '@sage/xtrem-core';
import * as _ from 'lodash';
import { nameArrayDataType } from '../data-types/data-types';
import * as xtremAuthorization from '../index';
import { loggers } from '../loggers';

const logger = loggers.roleActivity;

export interface RoleActivityParameter {
    activity: xtremAuthorization.nodes.Activity;
    permissions: string[];
    isActive: boolean;
    hasAllPermissions: boolean;
    _sortValue?: number;
}

@decorators.node<RoleActivity>({
    package: 'xtrem-authorization',
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canDelete: true,
    isVitalCollectionChild: true,
    isCached: true,
    indexes: [{ orderBy: { role: +1, activity: +1 }, isUnique: true, isNaturalKey: true }],
    async saveEnd() {
        await xtremAuthorization.services.SysAccessRightsManager.invalidateUserAccessCache(this.$.context);
    },
})
export class RoleActivity extends Node {
    @decorators.referenceProperty<RoleActivity, 'role'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremAuthorization.nodes.Role,
        isVitalParent: true,
    })
    readonly role: Reference<xtremAuthorization.nodes.Role>;

    @decorators.referenceProperty<RoleActivity, 'activity'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        node: () => xtremAuthorization.nodes.Activity,
    })
    readonly activity: Reference<xtremAuthorization.nodes.Activity>;

    @decorators.booleanProperty<RoleActivity, 'hasAllPermissions'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        defaultValue: false,
    })
    readonly hasAllPermissions: Promise<boolean>;

    @decorators.stringArrayProperty<RoleActivity, 'permissions'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: false,
        dataType: () => nameArrayDataType,
    })
    readonly permissions: Promise<string[] | null>;

    @decorators.stringArrayProperty<RoleActivity, 'getPermissions'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => nameArrayDataType,
        async getValue() {
            return (await this.hasAllPermissions) ? (await this.activity).permissions : this.permissions;
        },
    })
    readonly getPermissions: Promise<string[] | null>;

    @decorators.booleanProperty<RoleActivity, 'isActive'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        provides: ['isActive'],
    })
    readonly isActive: Promise<boolean>;

    /**
     * This method creates or updates role-activity'
     * @param context
     * @param activities
     * @param role
     * @param roleId
     * @param roleName
     */
    @decorators.mutation<typeof RoleActivity, 'roleActivitiesCreateUpdate'>({
        isPublished: true,
        parameters: [
            {
                name: 'roleActivities',
                type: 'array',
                item: {
                    type: 'object',
                    properties: {
                        activity: {
                            type: 'reference',
                            node: () => xtremAuthorization.nodes.Activity,
                        },
                        permissions: {
                            type: 'array',
                            item: 'string',
                        },
                        isActive: 'boolean',
                        hasAllPermissions: 'boolean',
                    },
                },
            },
            {
                name: 'roleSysId',
                type: 'string',
                isMandatory: false,
                isNullable: true,
            },
            {
                name: 'roleId',
                type: 'string',
                isMandatory: false,
            },
            {
                name: 'roleName',
                type: 'string',
                isMandatory: false,
            },
        ],
        return: {
            type: 'boolean',
        },
    })
    static async roleActivitiesCreateUpdate(
        context: Context,
        roleActivities: RoleActivityParameter[],
        roleSysId: string | null,
        roleId: string,
        roleName: string,
    ): Promise<boolean> {
        logger.debug(
            () => `roleActivitiesCreateUpdate- data/param ${roleSysId} : ${roleActivities} - ${roleId} - ${roleId} `,
        );

        let roleSetup: xtremAuthorization.nodes.Role;
        let roleActivityData: RoleActivityParameter[] = roleActivities;

        if (roleSysId === null || roleSysId === '') {
            roleSetup = await context.create(xtremAuthorization.nodes.Role, {
                id: roleId,
                name: roleName,
                activities: roleActivityData,
            });
            await roleSetup.$.save();
        } else {
            roleSetup = await context.read(
                xtremAuthorization.nodes.Role,
                {
                    _id: roleSysId.startsWith('#') ? roleSysId : parseInt(roleSysId, 10),
                },
                { forUpdate: true },
            );

            const roleActivityArray = await roleSetup.activities.toArray();
            roleActivityData = await asyncArray(roleActivities)
                .map(async roleActivity => {
                    const found = await asyncArray(roleActivityArray).find(
                        async savedRoleActivity =>
                            (await (await savedRoleActivity.activity).name) === (await roleActivity.activity.name),
                    );

                    if (found) {
                        roleActivity._sortValue = await found._sortValue;
                    }
                    return roleActivity;
                })
                .toArray();

            roleActivityData = _.sortBy(roleActivityData, roleActivity => {
                return roleActivity._sortValue == null ? Number.MAX_SAFE_INTEGER : roleActivity._sortValue;
            });

            await roleSetup.$.set({ activities: roleActivityData });
            await roleSetup.$.save();
        }

        return true;
    }
}
