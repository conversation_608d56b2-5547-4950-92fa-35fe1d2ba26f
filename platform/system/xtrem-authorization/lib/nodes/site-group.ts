import { Collection, decorators, Node, TextStream, useDefaultValue } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremAuthorization from '../index';

@decorators.node<SiteGroup>({
    package: 'xtrem-authorization',
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    canDuplicate: true,
    isCached: true,
    indexes: [
        {
            orderBy: { id: +1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    notifies: ['created', 'updated', 'deleted'],
    async saveEnd() {
        await xtremAuthorization.services.SysAccessRightsManager.invalidateUserAccessCache(this.$.context);
    },
})
export class SiteGroup extends Node {
    @decorators.stringProperty<SiteGroup, 'id'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.id,
        duplicateRequiresPrompt: true,
        duplicatedValue: '',
    })
    readonly id: Promise<string>;

    @decorators.booleanProperty<SiteGroup, 'isActive'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        defaultValue: true,
        duplicateRequiresPrompt: true,
    })
    readonly isActive: Promise<boolean>;

    @decorators.stringProperty<SiteGroup, 'name'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.name,
        duplicateRequiresPrompt: true,
        duplicatedValue: '',
    })
    readonly name: Promise<string>;

    @decorators.booleanProperty<SiteGroup, 'isLegalCompany'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dependsOn: ['id'],
        async defaultValue() {
            return this.$.context.exists(xtremSystem.nodes.Company, { id: await this.id });
        },
        duplicatedValue: useDefaultValue,
    })
    readonly isLegalCompany: Promise<boolean>;

    @decorators.textStreamProperty<SiteGroup, 'hierarchyChartContent'>({
        isPublished: true,
        lookupAccess: true,
        async computeValue() {
            return TextStream.fromString(await xtremAuthorization.functions.generateSiteGroupTreeChart(this));
        },
    })
    readonly hierarchyChartContent: Promise<TextStream>;

    @decorators.collectionProperty<SiteGroup, 'sites'>({
        isPublished: true,
        lookupAccess: true,
        reverseReference: 'siteGroup',
        isVital: true,
        node: () => xtremAuthorization.nodes.SiteGroupToSite,
    })
    readonly sites: Collection<xtremAuthorization.nodes.SiteGroupToSite>;

    @decorators.collectionProperty<SiteGroup, 'siteGroups'>({
        isPublished: true,
        lookupAccess: true,
        reverseReference: 'siteGroupOrigin',
        isVital: true,
        node: () => xtremAuthorization.nodes.SiteGroupToSiteGroup,
    })
    readonly siteGroups: Collection<xtremAuthorization.nodes.SiteGroupToSiteGroup>;

    /**
     * createdBy user displayName
     */
    @decorators.stringProperty<SiteGroup, 'createdBy'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.name,
        async computeValue() {
            return ((await this.$.createdBy) as xtremSystem.nodes.User).displayName;
        },
    })
    readonly createdBy: Promise<string>;

    /**
     * updatedBy user displayName
     */
    @decorators.stringProperty<SiteGroup, 'updatedBy'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.name,
        async computeValue() {
            return ((await this.$.updatedBy) as xtremSystem.nodes.User).displayName;
        },
    })
    readonly updatedBy: Promise<string>;
}
