import { AuthorizationError, Collection, decorators, Node } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { difference, uniq } from 'lodash';
import * as xtremAuthorization from '../index';
import { NormalizedRestrictedOperationName, RestrictedOperationName } from './restricted-node-user-grant';

@decorators.node<RestrictedNode>({
    storage: 'sql',
    isAbstract: true,
    isPublished: true,
    canRead: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    async getFilters(context) {
        return [{ userGrants: { _atLeast: 1, user: (await context.user)?._id } }];
    },
    async controlBegin() {
        if (this.$.status === 'modified') {
            await this.checkIfOperationIsAllowed('update');
        }
    },
    async saveEnd() {
        if (this.$.status === 'added') {
            await this.grantUserAccess(await xtremSystem.nodes.User.fromContext(this.$.context), 'created', ['*']);
        }
    },
    async controlDelete() {
        await this.checkIfOperationIsAllowed('delete');
    },
})
export class RestrictedNode extends Node {
    @decorators.collectionProperty<RestrictedNode, 'userGrants'>({
        node: () => xtremAuthorization.nodes.RestrictedNodeUserGrant,
        reverseReference: 'object',
    })
    readonly userGrants: Collection<xtremAuthorization.nodes.RestrictedNodeUserGrant>;

    // eslint-disable-next-line class-methods-use-this
    private normalizeMutationNames(mutationNames: RestrictedOperationName[]): NormalizedRestrictedOperationName[] {
        return (
            mutationNames.includes('*') ? ['update', 'delete'] : mutationNames
        ) as NormalizedRestrictedOperationName[];
    }

    /**
     * Grants access to a user on a restricted node instance
     * @param user the user we are granting access to
     * @param grantingEvent the event which granted these rights
     * @param mutationNames optional list of mutations that are being granted to the user.
     *
     * If mutationNames is an array containing zero or more strings in 'update', 'delete' or '*'.
     * If mutationNames is omitted or set to `[]`, queries will be granted but mutations won't.
     * The ['*'] wildcard is a shortcut for ['update', 'delete']
     */
    async grantUserAccess(
        user: xtremSystem.nodes.User,
        grantingEvent: string,
        mutationNames: RestrictedOperationName[] = [],
    ): Promise<void> {
        let userGrant = await this.$.context.tryRead(
            xtremAuthorization.nodes.RestrictedNodeUserGrant,
            { user, object: this },
            { forUpdate: true },
        );
        if (userGrant) {
            const oldOperationNames = (await userGrant.accessMap)[grantingEvent] || [];
            const accessMap = {
                ...(await userGrant.accessMap),
                [grantingEvent]: uniq([...this.normalizeMutationNames(mutationNames), ...oldOperationNames]),
            };
            await userGrant.$.set({ accessMap });
        } else {
            const accessMap = { [grantingEvent]: this.normalizeMutationNames(mutationNames) };
            userGrant = await this.$.context.create(xtremAuthorization.nodes.RestrictedNodeUserGrant, {
                user,
                object: this,
                accessMap,
            });
        }
        await userGrant.$.save();
    }

    /**
     * Revokes access to a user on a restricted node instance
     * @param user the user we are revoking access to
     * @param grantingEvent the event which granted these rights
     * @param mutationNames optional list of mutations that will be revoked.
     *
     * If `mutationNames` is omitted, all the operations granted by the granting event (mutations and also queries)
     * will be revoked.
     */
    async revokeUserAccess(
        user: xtremSystem.nodes.User,
        grantingEvent: string,
        mutationNames?: RestrictedOperationName[],
    ): Promise<void> {
        const userGrant = await this.$.context.tryRead(
            xtremAuthorization.nodes.RestrictedNodeUserGrant,
            { user, object: this },
            { forUpdate: true },
        );
        if (!userGrant) return;

        const accessMap = { ...(await userGrant.accessMap) };
        if (accessMap[grantingEvent] == null) return;

        const remainingOperationNames = mutationNames
            ? difference(accessMap[grantingEvent], this.normalizeMutationNames(mutationNames))
            : undefined;

        if (remainingOperationNames != null) accessMap[grantingEvent] = remainingOperationNames;
        else delete accessMap[grantingEvent];

        if (Object.keys(accessMap).length > 0) {
            await userGrant.$.set({ accessMap });
            await userGrant.$.save();
        } else {
            await userGrant.$.delete();
        }
    }

    private async isOperationAllowed(operationName: 'update' | 'delete'): Promise<boolean> {
        const user = await xtremSystem.nodes.User.fromContext(this.$.context);
        const userGrant = await this.$.context.tryRead(
            xtremAuthorization.nodes.RestrictedNodeUserGrant,
            { user, object: this },
            { forUpdate: true },
        );
        if (!userGrant) return false;

        return Object.values(await userGrant.accessMap).some(access => access.includes(operationName));
    }

    private async checkIfOperationIsAllowed(operationName: 'update' | 'delete'): Promise<void> {
        if (!(await this.isOperationAllowed(operationName)))
            throw new AuthorizationError(
                this.$.context.localize(
                    '@sage/xtrem-authorization/operation-not-allowed-on-object',
                    'Operation is not allowed on this object',
                ),
            );
    }
}
