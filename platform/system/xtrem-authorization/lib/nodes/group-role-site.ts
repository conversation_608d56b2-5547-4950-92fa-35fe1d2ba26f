import { Collection, datetime, decorators, Node } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremAuthorization from '../index';

@decorators.node<GroupRoleSite>({
    package: 'xtrem-authorization',
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    isSetupNode: true,
    isCached: true,
    indexes: [
        {
            orderBy: { id: +1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    async saveEnd() {
        await xtremAuthorization.services.SysAccessRightsManager.invalidateUserAccessCache(this.$.context);
    },
})
export class GroupRoleSite extends Node {
    /**
     * the UserGroup id
     */
    @decorators.stringProperty<GroupRoleSite, 'id'>({
        isPublished: true,
        lookupAccess: true,
        isStored: true,
        isFrozen: true,
        dataType: () => xtremSystem.dataTypes.setupIdDataType,
    })
    readonly id: Promise<string>;

    /**
     * the UserGroup name
     */
    @decorators.stringProperty<GroupRoleSite, 'name'>({
        isPublished: true,
        lookupAccess: true,
        isStored: true,
        dataType: () => xtremSystem.dataTypes.localizedName,
    })
    readonly name: Promise<string>;

    @decorators.collectionProperty<GroupRoleSite, 'groupRoles'>({
        isPublished: true,
        lookupAccess: true,
        isVital: true,
        reverseReference: 'groupRoleSite',
        node: () => xtremAuthorization.nodes.GroupRole,
    })
    readonly groupRoles: Collection<xtremAuthorization.nodes.GroupRole>;

    @decorators.stringProperty<GroupRoleSite, 'groupRolesDisplay'>({
        isPublished: true,
        lookupAccess: true,
        dependsOn: [{ groupRoles: ['role'] }],
        computeValue() {
            return this.groupRoles.map(async element => (await element.role).name).join();
        },
    })
    readonly groupRolesDisplay: Promise<string>;

    @decorators.collectionProperty<GroupRoleSite, 'groupSites'>({
        isPublished: true,
        lookupAccess: true,
        isVital: true,
        reverseReference: 'groupRoleSite',
        node: () => xtremAuthorization.nodes.GroupSite,
    })
    readonly groupSites: Collection<xtremAuthorization.nodes.GroupSite>;

    @decorators.stringProperty<GroupRoleSite, 'groupSitesDisplay'>({
        isPublished: true,
        lookupAccess: true,
        dependsOn: [{ groupSites: ['siteGroup'] }],
        computeValue() {
            return this.groupSites.map(async element => (await element.siteGroup).name).join();
        },
    })
    readonly groupSitesDisplay: Promise<string>;

    /**
     * Enhancement request for createdBy updatedBy  updateStamp createStamp  : https://jira.sage.com/browse/XT-12554
     */
    /**
     * createdBy user displayName
     */
    @decorators.stringProperty<GroupRoleSite, 'createdBy'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.name,
        async computeValue() {
            return ((await this.$.createdBy) as xtremSystem.nodes.User).displayName;
        },
    })
    readonly createdBy: Promise<string>;

    /**
     * updatedBy user displayName
     */
    @decorators.stringProperty<GroupRoleSite, 'updatedBy'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.name,
        async computeValue() {
            return ((await this.$.updatedBy) as xtremSystem.nodes.User).displayName;
        },
    })
    readonly updatedBy: Promise<string>;

    /**
     * Last update datetime
     */
    @decorators.datetimeProperty<GroupRoleSite, 'updateStamp'>({
        isPublished: true,
        lookupAccess: true,
        computeValue() {
            return this.$.updateStamp;
        },
    })
    readonly updateStamp: Promise<datetime>;

    /**
     * Create datetime
     */
    @decorators.datetimeProperty<GroupRoleSite, 'createStamp'>({
        isPublished: true,
        lookupAccess: true,
        computeValue() {
            return this.$.createStamp;
        },
    })
    readonly createStamp: Promise<datetime>;
}
