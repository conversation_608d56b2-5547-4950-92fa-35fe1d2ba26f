import { Node, Reference, decorators } from '@sage/xtrem-core';
import * as xtremAuthorization from '../index';

@decorators.node<RoleToRole>({
    package: 'xtrem-authorization',
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canDelete: true,
    isVitalCollectionChild: true,
    indexes: [
        {
            orderBy: { roleOrigin: +1, role: +1 },
            isUnique: true,
        },
    ],
    async saveEnd() {
        await xtremAuthorization.services.SysAccessRightsManager.invalidateUserAccessCache(this.$.context);
    },
})
export class RoleToRole extends Node {
    @decorators.referenceProperty<RoleToRole, 'roleOrigin'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremAuthorization.nodes.Role,
        isVitalParent: true,
    })
    readonly roleOrigin: Reference<xtremAuthorization.nodes.Role>;

    @decorators.referenceProperty<RoleToRole, 'role'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremAuthorization.nodes.Role,
    })
    readonly role: Reference<xtremAuthorization.nodes.Role>;
}
