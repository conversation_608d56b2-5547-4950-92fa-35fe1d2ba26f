import { Node, Reference, decorators } from '@sage/xtrem-core';
import * as xtremAuthorization from '../index';

@decorators.node<GroupSite>({
    package: 'xtrem-authorization',
    storage: 'sql',
    isPublished: true,
    canRead: true,
    isVitalCollectionChild: true,
    indexes: [
        {
            orderBy: { groupRoleSite: +1, siteGroup: +1 },
            isUnique: true,
        },
    ],
    async saveEnd() {
        await xtremAuthorization.services.SysAccessRightsManager.invalidateUserAccessCache(this.$.context);
    },
})
export class GroupSite extends Node {
    @decorators.referenceProperty<GroupSite, 'groupRoleSite'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremAuthorization.nodes.GroupRoleSite,
        isVitalParent: true,
    })
    readonly groupRoleSite: Reference<xtremAuthorization.nodes.GroupRoleSite>;

    @decorators.referenceProperty<GroupSite, 'siteGroup'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremAuthorization.nodes.SiteGroup,
    })
    readonly siteGroup: Reference<xtremAuthorization.nodes.SiteGroup>;
}
