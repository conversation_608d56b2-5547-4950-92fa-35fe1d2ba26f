import { Collection, Context, datetime, decorators, LocalizeLocale, Node } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremAuthorization from '../index';

@decorators.node<Role>({
    package: 'xtrem-authorization',
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    isSetupNode: true,
    isCached: true,
    canDuplicate: true,
    indexes: [{ orderBy: { id: +1 }, isUnique: true, isNaturalKey: true }],
    notifies: ['created', 'updated', 'deleted'],
    async saveEnd() {
        await xtremAuthorization.services.SysAccessRightsManager.invalidateUserAccessCache(this.$.context);
    },
})
export class Role extends Node {
    @decorators.booleanProperty<Role, 'isActive'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        defaultValue: true,
    })
    readonly isActive: Promise<boolean>;

    @decorators.stringProperty<Role, 'name'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.localizedName,
        duplicateRequiresPrompt: true,
        duplicatedValue: '',
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<Role, 'description'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.description,
        defaultValue: '',
    })
    readonly description: Promise<string>;

    @decorators.stringProperty<Role, 'id'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.id,
        duplicateRequiresPrompt: true,
        duplicatedValue: '',
    })
    readonly id: Promise<string>;

    @decorators.booleanProperty<Role, 'isBillingRole'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        defaultValue: false,
    })
    readonly isBillingRole: Promise<boolean>;

    @decorators.collectionProperty<Role, 'roles'>({
        isPublished: true,
        lookupAccess: true,
        reverseReference: 'roleOrigin',
        isVital: true,
        node: () => xtremAuthorization.nodes.RoleToRole,
    })
    readonly roles: Collection<xtremAuthorization.nodes.RoleToRole>;

    @decorators.collectionProperty<Role, 'activities'>({
        isPublished: true,
        lookupAccess: true,
        reverseReference: 'role',
        isVital: true,
        node: () => xtremAuthorization.nodes.RoleActivity,
    })
    readonly activities: Collection<xtremAuthorization.nodes.RoleActivity>;

    /**
     * createdBy user displayName
     */
    @decorators.stringProperty<Role, 'createdBy'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.name,
        async computeValue() {
            return ((await this.$.createdBy) as xtremSystem.nodes.User).displayName;
        },
    })
    readonly createdBy: Promise<string>;

    /**
     * updatedBy user displayName
     */
    @decorators.stringProperty<Role, 'updatedBy'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.name,
        async computeValue() {
            return ((await this.$.updatedBy) as xtremSystem.nodes.User).displayName;
        },
    })
    readonly updatedBy: Promise<string>;

    /**
     *  get All Entities of the current instance
     *  TODO : datetime ?
     * @param context
     * @returns
     */
    @decorators.query<typeof Role, 'all'>({
        isPublished: true,
        // TODO: TYPO in name
        parameters: [{ name: 'isActive', type: 'boolean', isMandatory: true }],
        return: {
            type: 'array',
            item: {
                type: 'object',
                properties: {
                    _id: 'string',
                    name: 'string',
                    id: 'string',
                    isBillingRole: 'boolean',
                    createdBy: 'string',
                    createStamp: 'string',
                    updatedBy: 'string',
                    updateStamp: 'string',
                    isVendor: 'string',
                },
            },
            isMandatory: true,
        },
    })
    static all(context: Context, isActive: boolean): Promise<xtremAuthorization.sharedFunctions.interfaces.AllRoles[]> {
        return context
            .query(Role, { filter: { isActive } })
            .map(async (element: Role) => {
                return {
                    _id: String(element._id),
                    name: await element.name,
                    id: await element.id,
                    isBillingRole: await element.isBillingRole,
                    createdBy: await ((await element.$.createdBy) as xtremSystem.nodes.User).displayName,
                    createStamp: ((await element.$.createStamp) as datetime).format(
                        context.currentLocale as LocalizeLocale,
                        'DD-MM-YYYY HH:mm:ss',
                    ),
                    updatedBy: await ((await element.$.updatedBy) as xtremSystem.nodes.User).displayName,
                    updateStamp: ((await element.$.updateStamp) as datetime).format(
                        context.currentLocale as LocalizeLocale,
                        'DD-MM-YYYY HH:mm:ss',
                    ),
                    isVendor: (await element._vendor) ? '1' : '0',
                };
            })
            .toArray();
    }
}
