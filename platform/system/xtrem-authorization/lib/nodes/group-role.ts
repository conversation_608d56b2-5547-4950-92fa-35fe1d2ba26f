import { Node, Reference, decorators } from '@sage/xtrem-core';
import * as xtremAuthorization from '../index';

@decorators.node<GroupRole>({
    package: 'xtrem-authorization',
    storage: 'sql',
    isPublished: true,
    canRead: true,
    isVitalCollectionChild: true,
    indexes: [
        {
            orderBy: { groupRoleSite: +1, role: +1 },
            isUnique: true,
        },
    ],
    async saveEnd() {
        await xtremAuthorization.services.SysAccessRightsManager.invalidateUserAccessCache(this.$.context);
    },
})
export class GroupRole extends Node {
    @decorators.referenceProperty<GroupRole, 'groupRoleSite'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremAuthorization.nodes.GroupRoleSite,
        isVitalParent: true,
    })
    readonly groupRoleSite: Reference<xtremAuthorization.nodes.GroupRoleSite>;

    @decorators.referenceProperty<GroupRole, 'role'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremAuthorization.nodes.Role,
    })
    readonly role: Reference<xtremAuthorization.nodes.Role>;
}
