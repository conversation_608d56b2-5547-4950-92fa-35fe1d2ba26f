import { decorators, Node, Reference } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremAuthorization from '../index';

@decorators.node<UserBillingRole>({
    package: 'xtrem-authorization',
    storage: 'sql',
    isPublished: true,
    isVitalReferenceChild: true,
    canRead: true,
    indexes: [
        {
            orderBy: { user: +1 },
            isUnique: true,
        },
    ],
    async saveEnd() {
        await xtremAuthorization.services.SysAccessRightsManager.invalidateUserAccessCache(this.$.context);
    },
})
export class UserBillingRole extends Node {
    @decorators.referenceProperty<UserBillingRole, 'user'>({
        isPublished: true,
        lookupAccess: true,
        isStored: true,
        isVitalParent: true,
        node: () => xtremSystem.nodes.User,
    })
    readonly user: Reference<xtremSystem.nodes.User>;

    @decorators.referenceProperty<UserBillingRole, 'role'>({
        isPublished: true,
        lookupAccess: true,
        isStored: true,
        node: () => xtremAuthorization.nodes.Role,
    })
    readonly role: Reference<xtremAuthorization.nodes.Role>;
}
