import { BusinessRuleError, Context, datetime, decorators, integer, Node } from '@sage/xtrem-core';
import { UserEventsManager } from '@sage/xtrem-system';
import * as xtremAuthorization from '../index';

@decorators.node<SupportAccessHistory>({
    package: 'xtrem-authorization',
    storage: 'sql',
    isPublished: true,
    canDelete: false,
    canCreate: false,
    canRead: true,
    canUpdate: false,
})
export class SupportAccessHistory extends Node {
    @decorators.enumProperty<SupportAccessHistory, 'status'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremAuthorization.enums.SupportAccessHistoryStatusDataType,
        async computeValue() {
            if ((await this.startTime).equals(await this.endTime) || (await this.endTime).equals(datetime.now())) {
                return 'closed';
            }
            if (datetime.now().isBetween(await this.startTime, await this.endTime)) {
                return 'open';
            }
            return 'closed';
        },
    })
    readonly status: Promise<xtremAuthorization.enums.SupportAccessHistoryStatus>;

    @decorators.integerProperty<SupportAccessHistory, 'timeToClose'>({
        isPublished: true,
        lookupAccess: true,
        async computeValue() {
            // For a large number of milliseconds. integer values shouldn't pass the max 2147483647
            return (await this.endTime).millisDiff(datetime.now()) > 2147483647
                ? 2147483647
                : (await this.endTime).millisDiff(datetime.now());
        },
    })
    readonly timeToClose: Promise<integer>;

    @decorators.datetimeProperty<SupportAccessHistory, 'startTime'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isFrozen: true,
        defaultValue: () => datetime.now(),
    })
    readonly startTime: Promise<datetime>;

    @decorators.datetimeProperty<SupportAccessHistory, 'endTime'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        async control(cx, val) {
            if (val.compare(await this.startTime) < 0) {
                cx.error.addLocalized(
                    '@sage/xtrem-authorization/nodes__support_access__end_time_must_be_greater_than_start_time',
                    'The end time must be greater than the start time.',
                );
            }
        },
    })
    readonly endTime: Promise<datetime>;

    @decorators.booleanProperty<SupportAccessHistory, 'isReadOnlyAccess'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: false,
    })
    readonly isReadOnlyAccess: Promise<boolean>;

    static getOpenSupportAccess(context: Context) {
        return context
            .query(SupportAccessHistory, {})
            .filter(async supportAccess => (await supportAccess.status) === 'open')
            .toArray();
    }

    @decorators.query<typeof SupportAccessHistory, 'checkSupportAccessOpen'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'object',
            properties: {
                isOpen: {
                    type: 'boolean',
                },
                timeToClose: {
                    type: 'integer',
                },
            },
        },
    })
    static async checkSupportAccessOpen(context: Context) {
        const filterSession = await SupportAccessHistory.getOpenSupportAccess(context);
        return {
            isOpen: filterSession.length > 0,
            timeToClose: filterSession.length > 0 ? filterSession[0].timeToClose : 0,
        };
    }

    static calculateEndDateTime(
        startDate: datetime,
        forTime: number,
        units: xtremAuthorization.enums.SupportAccessUnit,
    ): datetime {
        switch (units) {
            case 'minutes': {
                return startDate.addMinutes(forTime);
            }
            case 'hours': {
                return startDate.addHours(forTime);
            }
            case 'days': {
                return startDate.addDays(forTime);
            }
            default: {
                return startDate;
            }
        }
    }

    @decorators.mutation<typeof SupportAccessHistory, 'allowAccess'>({
        isPublished: true,
        parameters: [
            {
                name: 'forTime',
                type: 'integer',
            },
            {
                name: 'units',
                type: 'enum',
                dataType: () => xtremAuthorization.enums.SupportAccessUnitDataType,
            },
            {
                name: 'isReadOnlyAccess',
                type: 'boolean',
            },
        ],
        return: {
            type: 'instance',
            node: () => xtremAuthorization.nodes.SupportAccessHistory,
        },
    })
    static async allowAccess(
        context: Context,
        forTime: number,
        units: xtremAuthorization.enums.SupportAccessUnit,
        isReadOnlyAccess: boolean,
    ): Promise<SupportAccessHistory | undefined> {
        if (forTime <= 0) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-authorization/nodes__support_access__number_must_be_greater_than_0',
                    'The number must be greater than 0.',
                ),
            );
        }

        if ((await SupportAccessHistory.getOpenSupportAccess(context)).length > 0) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-authorization/nodes__support_access__cannot_allow_support_access_because_there_is_a_open_session',
                    'There is already an open session. Contact your system administrator.',
                ),
            );
        }
        const endTime = SupportAccessHistory.calculateEndDateTime(datetime.now(), forTime, units);
        const enableSupportAccess = await UserEventsManager.enableSupportAccess(context, {
            validUntil: endTime.toJsDate(),
            readOnlyAccess: isReadOnlyAccess,
        });
        if (enableSupportAccess) {
            const supportAccess = await context.create(SupportAccessHistory, {
                isReadOnlyAccess,
                endTime,
            });
            await supportAccess.$.save();
            return supportAccess;
        }

        return undefined;
    }

    @decorators.mutation<typeof SupportAccessHistory, 'extendAccess'>({
        isPublished: true,
        parameters: [
            {
                name: 'forTime',
                type: 'integer',
            },
            {
                name: 'units',
                type: 'enum',
                dataType: () => xtremAuthorization.enums.SupportAccessUnitDataType,
            },
        ],
        return: {
            type: 'instance',
            node: () => SupportAccessHistory,
        },
    })
    static async extendAccess(
        context: Context,
        forTime: number,
        units: xtremAuthorization.enums.SupportAccessUnit,
    ): Promise<xtremAuthorization.nodes.SupportAccessHistory | undefined> {
        if (forTime <= 0) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-authorization/nodes__support_access__number_must_be_greater_than_0',
                    'The number must be greater than 0.',
                ),
            );
        }
        const filterSession = await SupportAccessHistory.getOpenSupportAccess(context);

        if (filterSession.length === 0) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-authorization/nodes__support_access__there_is_no_open_session_to_extend_the_access',
                    'There is no open session to extend the access. Contact your system administrator.',
                ),
            );
        }
        const openSession = await context.read(
            SupportAccessHistory,
            { _id: filterSession[0]._id },
            { forUpdate: true },
        );
        const endTime = SupportAccessHistory.calculateEndDateTime(await openSession.endTime, forTime, units);
        const enableSupportAccess = await UserEventsManager.enableSupportAccess(context, {
            validUntil: endTime.toJsDate(),
            readOnlyAccess: await openSession.isReadOnlyAccess,
        });
        if (enableSupportAccess) {
            await openSession.$.set({
                endTime,
            });
            await openSession.$.save();
            return openSession;
        }

        return undefined;
    }

    @decorators.mutation<typeof SupportAccessHistory, 'revokeAccess'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'instance',
            node: () => SupportAccessHistory,
        },
    })
    static async revokeAccess(context: Context): Promise<xtremAuthorization.nodes.SupportAccessHistory | undefined> {
        const filterSession = await SupportAccessHistory.getOpenSupportAccess(context);

        if (filterSession.length === 0) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-authorization/nodes__support_access__there_is_no_open_session_to_revoke_the_access',
                    'There is no open session to revoke the access. Contact your system administrator.',
                ),
            );
        }
        const disableSupportAccess = await UserEventsManager.disableSupportAccess(context);
        if (disableSupportAccess) {
            const openSession = await context.read(
                SupportAccessHistory,
                { _id: filterSession[0]._id },
                { forUpdate: true },
            );

            await openSession.$.set({ endTime: datetime.now() });
            await openSession.$.save();
            return openSession;
        }

        return undefined;
    }
}
