import { Node, Reference, decorators } from '@sage/xtrem-core';
import * as xtremAuthorization from '../index';

@decorators.node<SiteGroupToSiteGroup>({
    package: 'xtrem-authorization',
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    isVitalCollectionChild: true,
    indexes: [
        {
            orderBy: { siteGroupOrigin: +1, siteGroup: +1 },
            isUnique: true,
        },
    ],
    async saveEnd() {
        await xtremAuthorization.services.SysAccessRightsManager.invalidateUserAccessCache(this.$.context);
    },
})
export class SiteGroupToSiteGroup extends Node {
    @decorators.referenceProperty<SiteGroupToSiteGroup, 'siteGroupOrigin'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremAuthorization.nodes.SiteGroup,
        isVitalParent: true,
    })
    readonly siteGroupOrigin: Reference<xtremAuthorization.nodes.SiteGroup>;

    @decorators.referenceProperty<SiteGroupToSiteGroup, 'siteGroup'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremAuthorization.nodes.SiteGroup,
        // TODO: Need to filter on siteGroups having _id <> then the parent's _id
        // filters: {
        //     control: {
        //     },
        // },
    })
    readonly siteGroup: Reference<xtremAuthorization.nodes.SiteGroup>;
}
