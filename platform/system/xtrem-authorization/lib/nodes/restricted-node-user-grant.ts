import { decorators, Node, Reference } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremAuthorization from '../index';

export type NormalizedRestrictedOperationName = 'update' | 'delete';
export type RestrictedOperationName = NormalizedRestrictedOperationName | '*';

export interface RestrictedNodeUserAccessMap {
    [event: string]: NormalizedRestrictedOperationName[];
}

@decorators.node<RestrictedNodeUserGrant>({
    storage: 'sql',
    // This node contains sensitive security info. We exclude it from the GraphQL API, for now
    canRead: true,
    canCreate: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [
        { orderBy: { object: 1, user: 1 }, isUnique: true },
        { orderBy: { user: 1, object: 1 }, isUnique: true },
    ],
})
export class RestrictedNodeUserGrant extends Node {
    @decorators.referenceProperty<RestrictedNodeUserGrant, 'user'>({
        isPublished: true,
        lookupAccess: true,
        isStored: true,
        node: () => xtremSystem.nodes.User,
    })
    readonly user: Reference<xtremSystem.nodes.User>;

    @decorators.referenceProperty<RestrictedNodeUserGrant, 'object'>({
        isPublished: true,
        lookupAccess: true,
        isStored: true,
        node: () => xtremAuthorization.nodes.RestrictedNode,
    })
    readonly object: Reference<xtremAuthorization.nodes.RestrictedNode>;

    @decorators.jsonProperty<RestrictedNodeUserGrant, 'accessMap'>({
        isPublished: true,
        lookupAccess: true,
        isStored: true,
    })
    readonly accessMap: Promise<RestrictedNodeUserAccessMap>;
}
