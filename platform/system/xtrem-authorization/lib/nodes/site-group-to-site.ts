import { date, decorators, Node, Reference, useDefaultValue } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremAuthorization from '../index';

@decorators.node<SiteGroupToSite>({
    package: 'xtrem-authorization',
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    canDuplicate: true,
    isVitalCollectionChild: true,
    indexes: [
        {
            orderBy: { siteGroup: +1, site: +1 },
            isUnique: true,
        },
    ],
    async saveEnd() {
        await xtremAuthorization.services.SysAccessRightsManager.invalidateUserAccessCache(this.$.context);
    },
})
export class SiteGroupToSite extends Node {
    @decorators.referenceProperty<SiteGroupToSite, 'siteGroup'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremAuthorization.nodes.SiteGroup,
        isVitalParent: true,
    })
    readonly siteGroup: Reference<xtremAuthorization.nodes.SiteGroup>;

    @decorators.referenceProperty<SiteGroupToSite, 'site'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremSystem.nodes.Site,
        provides: ['site'],
        // TODO: Need to filter on sites that are not already part of one of parent's site groups (if any)
        // filters: {
        //     control: {
        //     },
        // },
    })
    readonly site: Reference<xtremSystem.nodes.Site>;

    @decorators.booleanProperty<SiteGroupToSite, 'isValid'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        defaultValue: false,
    })
    readonly isValid: Promise<boolean>;

    @decorators.dateProperty<SiteGroupToSite, 'dateAdd'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        defaultValue: () => date.today(),
        duplicatedValue: useDefaultValue,
    })
    readonly dateAdd: Promise<date>;
}
