import { noop } from 'lodash';
import * as ui from '@sage/xtrem-ui';
import { ClientDiagnoseSeverity } from '@sage/xtrem-client';

/**
 * Wraps dialog calls to catch the close and exit action as a noop function
 * @param panelPromise to be wrapped
 * @returns the modified data or void
 */
export async function catchPanelCrossQuitButtonAsNoop<Data extends any>(
    panelPromise: Promise<Data>,
): Promise<void | Data> {
    return (await Promise.all([panelPromise.catch(() => noop())]))[0];
}

export function formatError(page: ui.Page, error: string | (Error & { errors: Array<any> })): string {
    page.$.loader.isHidden = true;
    if (typeof error === 'string') {
        return error;
    }
    if (error.errors?.length) {
        const errorMessageLines: string[] = [];
        error.errors.forEach(e => {
            errorMessageLines.push(`**${e.message}**`);
            if (e.extensions?.diagnoses) {
                e.extensions.diagnoses.forEach((d: any) => {
                    if (
                        d.severity === ClientDiagnoseSeverity.error ||
                        d.severity === ClientDiagnoseSeverity.exception
                    ) {
                        errorMessageLines.push(` - ${d.message}`);
                    }
                });
            }
        });
        if (errorMessageLines.length === 1) {
            return `${errorMessageLines[0].replace(/\*/g, '')}`;
        }
        return errorMessageLines.join('\n');
    }

    return error.message;
}
