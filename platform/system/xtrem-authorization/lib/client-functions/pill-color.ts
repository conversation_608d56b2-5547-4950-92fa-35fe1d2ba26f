import { SupportAccessHistoryStatus } from '@sage/xtrem-authorization-api';

import { ColoredElement } from '@sage/xtrem-system-api';
import {
    colorfulPillPattern,
    colorfulPillPatternDefaulted,
} from '@sage/xtrem-system/build/lib/client-functions/color-pattern';

function integrationStateColor(status: SupportAccessHistoryStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'open':
            return colorfulPillPattern.filledPositive[coloredElement];
        case 'closed':
            return colorfulPillPattern.filledClosing[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

export function getLabelColorByStatus(
    enumEntry: string,
    status?: SupportAccessHistoryStatus | null,
): {
    backgroundColor: string;
    borderColor: string;
    textColor: string;
} {
    const getColor = (coloredElement: ColoredElement) => {
        switch (enumEntry) {
            case 'SupportAccessStatus':
                return integrationStateColor(status as SupportAccessHistoryStatus, coloredElement);
            default:
                return colorfulPillPatternDefaulted(coloredElement);
        }
    };

    return {
        backgroundColor: getColor('backgroundColor'),
        borderColor: getColor('borderColor'),
        textColor: getColor('textColor'),
    };
}
