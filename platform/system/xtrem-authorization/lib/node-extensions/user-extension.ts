import '@sage/xtrem-communication';
import {
    adminDemoPersona,
    asyncArray,
    Collection,
    Context,
    CoreHooks,
    datetime,
    decorators,
    LocalizeLocale,
    NodeExtension,
    NodeStatus,
    Reference,
    rootUserEmail,
    SystemError,
} from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { UserEventsManager } from '@sage/xtrem-system';
import { loggers } from '../loggers';
import { RestrictedNode } from '../nodes/restricted-node';
import { RestrictedNodeUserGrant, RestrictedOperationName } from '../nodes/restricted-node-user-grant';
import { UserBillingRole } from '../nodes/user-billing-role';
import { UserGroup } from '../nodes/user-group';
import { AllUsers } from '../shared-functions/interfaces';

const logger = loggers.authorization;

@decorators.nodeExtension<UserExtension>({
    extends: () => xtremSystem.nodes.User,
    async controlBegin(cx): Promise<void> {
        if (!this.$.context.withoutTransactionUser) {
            await cx.error
                .withMessage(
                    '@sage/xtrem-authorization/nodes__user__cannot_change_own_rights',
                    'You are not allowed to modify your own authorization group.',
                )
                .if(
                    this._id === (await this.$.context.user)?._id &&
                        !(await this.$.context.user)?.isAdministrator &&
                        (await this.authorizationGroup.some(line =>
                            [NodeStatus.modified, NodeStatus.deleted, NodeStatus.added].includes(line.$.status),
                        )),
                )
                .is.true();
        }
    },
    async saveEnd() {
        const tenantId = this.$.context.tenantId;
        if (!tenantId) {
            throw new SystemError('no tenant in the current context');
        }

        // Skip notifications for persona users if the service option is available
        // Also skip for admin user because it can be created during option activation and then isDemoPersona getter
        // will throw an error because it depends on that option.
        const email = await this.email;
        if (email === adminDemoPersona.email || email === rootUserEmail) {
            logger.info(`User change notification: tenant=${tenantId}, id=${this._id} skipped!`);
            return;
        }
        const isActive = await this.isActive;
        const isDemoPersona = await this.isDemoPersona;
        const preferences = await this.preferences;
        const external = preferences ? await preferences.isExternal : false;
        const operatorHash = (await this.operatorId) || undefined;

        let billingRole = 'businessUser';

        if (operatorHash) {
            billingRole = 'operator';
        } else if ((await (await (await this.billingRole)?.role)?.id) === 'Operational User') {
            billingRole = 'operationalUser';
        }

        switch (this.$.status) {
            case NodeStatus.added: {
                if (!(await this.isFirstAdminUser) && !isDemoPersona) {
                    await UserEventsManager.userCreated(this.$.context, {
                        userId: this._id,
                        status: this.$.status,
                        email,
                        isActive,
                        billingRole,
                        external,
                        operatorHash,
                    });
                }
                break;
            }
            case NodeStatus.modified: {
                const old = await this.$.old;
                const wasDemoPersona = await old.isDemoPersona;
                if (isDemoPersona && !wasDemoPersona) {
                    // user is no longer a login user so we notify the infra to delete it
                    await UserEventsManager.userDeleted(this.$.context, {
                        userId: this._id,
                        status: this.$.status,
                        email,
                    });
                } else if (
                    (await old.isActive) !== isActive ||
                    (wasDemoPersona && !isDemoPersona) ||
                    ((await this.isOperatorUser) && operatorHash)
                ) {
                    await UserEventsManager.userModified(this.$.context, {
                        userId: this._id,
                        status: this.$.status,
                        email,
                        isActive,
                        billingRole,
                        external,
                        operatorHash,
                    });
                }
                break;
            }
            default:
                break;
        }
    },
})
export class UserExtension extends NodeExtension<xtremSystem.nodes.User> {
    @decorators.referenceProperty<UserExtension, 'billingRole'>({
        isPublished: true,
        lookupAccess: true,
        isVital: true,
        reverseReference: 'user',
        node: () => UserBillingRole,
        isNullable: true,
    })
    readonly billingRole: Reference<UserBillingRole> | null;

    @decorators.collectionProperty<UserExtension, 'authorizationGroup'>({
        isPublished: true,
        lookupAccess: true,
        isVital: true,
        reverseReference: 'user',
        node: () => UserGroup,
    })
    readonly authorizationGroup: Collection<UserGroup>;

    @decorators.stringProperty<UserExtension, 'groupDisplay'>({
        isPublished: true,
        lookupAccess: true,
        dependsOn: [{ authorizationGroup: ['group'] }],
        async computeValue() {
            return (await this.authorizationGroup.map(async element => (await element.group).name).toArray()).join();
        },
    })
    readonly groupDisplay: Promise<string>;

    @decorators.collectionProperty<UserExtension, 'objectGrants'>({
        reverseReference: 'user',
        node: () => RestrictedNodeUserGrant,
    })
    readonly objectGrants: Collection<RestrictedNodeUserGrant>;

    /**
     *  get All Entities of the current instance
     *  TODO : datetime ?
     * @param context
     * @returns
     */
    @decorators.query<typeof UserExtension, 'all'>({
        isPublished: true,
        // TODO: TYPO in name
        parameters: [{ name: 'isActive', type: 'boolean', isMandatory: true }],
        return: {
            type: 'array',
            item: {
                type: 'object',
                properties: {
                    _id: 'string',
                    name: 'string',
                    displayName: 'string',
                    email: 'string',
                    isWelcomeMailSent: 'boolean',
                    groupDisplay: 'string',
                    createdBy: 'string',
                    createStamp: 'string',
                    updatedBy: 'string',
                    updateStamp: 'string',
                },
            },
            isMandatory: true,
        },
    })
    static all(context: Context, isActive: boolean): Promise<AllUsers[]> {
        return context
            .query(xtremSystem.nodes.User, { filter: { isActive } })
            .map(async (element: xtremSystem.nodes.User) => {
                const createdBy = (await element.$.createdBy) as xtremSystem.nodes.User;
                const updatedBy = (await element.$.updatedBy) as xtremSystem.nodes.User;
                return {
                    _id: String(element._id),
                    displayName: await element.displayName,
                    email: await element.email,
                    isWelcomeMailSent:
                        (await (
                            await context.tryRead(xtremSystem.nodes.UserPreferences, { user: element._id })
                        )?.isWelcomeMailSent) || false,
                    groupDisplay: await element.groupDisplay,
                    createdBy: await createdBy.displayName,
                    createStamp: ((await element.$.createStamp) as datetime).format(
                        context.currentLocale as LocalizeLocale,
                        'DD-MM-YYYY HH:mm:ss',
                    ),
                    updatedBy: await updatedBy.displayName,
                    updateStamp: ((await element.$.updateStamp) as datetime).format(
                        context.currentLocale as LocalizeLocale,
                        'DD-MM-YYYY HH:mm:ss',
                    ),
                };
            })
            .toArray();
    }

    /**
     * Grants the user access a list of restricted node instances
     * @param objects the objects to which access will be granted
     * @param grantingEvent the event which granted these rights
     * @param mutationNames optional list of mutations that are being granted on these objects.
     *
     * If mutationNames is an array containing zero or more strings in 'update', 'delete' or '*'.
     * If mutationNames is omitted or set to `[]`, queries will be granted but mutations won't.
     * The ['*'] wildcard is a shortcut for ['update', 'delete']
     */
    async grantAccessToObjects(
        objects: RestrictedNode[],
        grantingEvent: string,
        mutationNames?: RestrictedOperationName[],
    ): Promise<void> {
        await asyncArray(objects).forEach(object =>
            object.grantUserAccess(this as unknown as xtremSystem.nodes.User, grantingEvent, mutationNames),
        );
    }

    /**
     * Revokes access to a list of restricted node instances for this user
     * @param objects the objects to which access will be granted
     * @param grantingEvent the event which granted these rights
     * @param mutationNames optional list of mutations that will be revoked.
     *
     * If `mutationNames` is omitted, all operations granted by the granting event (mutations and also queries)
     * will be revoked.
     */
    async revokeAccessToObjects(
        objects: RestrictedNode[],
        grantingEvent: string,
        mutationNames?: RestrictedOperationName[],
    ): Promise<void> {
        await asyncArray(objects).forEach(object =>
            object.revokeUserAccess(this as unknown as xtremSystem.nodes.User, grantingEvent, mutationNames),
        );
    }

    @decorators.bulkMutation<typeof UserExtension, 'sendBulkWelcomeMail'>({
        isPublished: true,
    })
    static async sendBulkWelcomeMail(context: Context, user: xtremSystem.nodes.User): Promise<void> {
        await CoreHooks.communicationManager.notify(context, 'onboardingMail', { users: [user], isAdmin: false });
    }
}
declare module '@sage/xtrem-system/lib/nodes/user' {
    export interface User extends UserExtension {}
}
