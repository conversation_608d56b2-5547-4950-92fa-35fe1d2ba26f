import { BusinessRuleError, Collection, decorators, NodeExtension, NodeStatus } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremAuthorization from '../index';

@decorators.nodeExtension<SiteExtension>({
    extends: () => xtremSystem.nodes.Site,
    async saveEnd() {
        if (this.$.status === NodeStatus.added) {
            await xtremAuthorization.functions.creationOfSiteGroupToSite(this as xtremSystem.nodes.Site);
        }
    },
    async deleteBegin() {
        if (!(await xtremAuthorization.functions.deleteOfSiteGroupToSite(this as xtremSystem.nodes.Site))) {
            throw new BusinessRuleError(
                this.$.context.localize(
                    '@sage/xtrem-authorization/cannot-delete-site-from-site-group',
                    'The current site cannot be deleted from the linked site groups',
                ),
            );
        }
    },
})
export class SiteExtension extends NodeExtension<xtremSystem.nodes.Site> {
    @decorators.collectionProperty<SiteExtension, 'siteGroups'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremAuthorization.nodes.SiteGroup,
        getFilter() {
            return { sites: { _atLeast: 1, site: this } };
        },
    })
    readonly siteGroups: Collection<xtremAuthorization.nodes.SiteGroup>;

    @decorators.collectionProperty<SiteExtension, 'groupRoleSites'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremAuthorization.nodes.GroupRoleSite,
        getFilter() {
            return { groupSites: { _atLeast: 1, siteGroup: { sites: { _atLeast: 1, site: this } } } };
        },
    })
    readonly groupRoleSites: Collection<xtremAuthorization.nodes.GroupRoleSite>;
}
declare module '@sage/xtrem-system/lib/nodes/site' {
    export interface Site extends SiteExtension {}
}
