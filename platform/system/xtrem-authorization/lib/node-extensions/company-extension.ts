import { BusinessRuleError, decorators, NodeExtension, NodeStatus } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremAuthorization from '../index';

@decorators.nodeExtension<CompanyExtension>({
    extends: () => xtremSystem.nodes.Company,
    async deleteBegin() {
        if (!(await xtremAuthorization.functions.deleteSiteGroup(this as xtremSystem.nodes.Company))) {
            throw new BusinessRuleError(
                this.$.context.localize(
                    '@sage/xtrem-authorization/cannot-delete-site-group',
                    'Linked site group cannot be deleted',
                ),
            );
        }
    },
    async saveEnd() {
        if (this.$.status === NodeStatus.added) {
            await xtremAuthorization.functions.creationOfSiteGroup(this as xtremSystem.nodes.Company);
        }
    },
})
export class CompanyExtension extends NodeExtension<xtremSystem.nodes.Company> {}
declare module '@sage/xtrem-system/lib/nodes/company' {
    export interface Company extends CompanyExtension {}
}
