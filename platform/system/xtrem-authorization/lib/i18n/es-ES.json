{"@sage/xtrem-authorization/activity__group_role_site__name": "Rol de grupo de plantas", "@sage/xtrem-authorization/activity__role__name": "Rol", "@sage/xtrem-authorization/activity__site_group__name": "Grupo de plantas", "@sage/xtrem-authorization/activity__support_access_history__name": "Historial de accesos a soporte", "@sage/xtrem-authorization/activity__user__name": "Usuario", "@sage/xtrem-authorization/cannot-delete-site-from-site-group": "La planta no se puede eliminar de los grupos de plantas asociados.", "@sage/xtrem-authorization/cannot-delete-site-group": "Los grupos de plantas vinculados no se pueden eliminar.", "@sage/xtrem-authorization/data_types__name_array_data_type__name": "Name array data type", "@sage/xtrem-authorization/data_types__string_data_type__name": "String data type", "@sage/xtrem-authorization/data_types__support_access_history_status_enum__name": "Support access history status enum", "@sage/xtrem-authorization/data_types__support_access_unit_enum__name": "Support access unit enum", "@sage/xtrem-authorization/data_types__user_type_enum__name": "User type enum", "@sage/xtrem-authorization/delete-confirmation": "Registro eliminado", "@sage/xtrem-authorization/delete-dialog-content": "¿Quieres eliminar este grupo?", "@sage/xtrem-authorization/delete-group": "Confirmar eliminación", "@sage/xtrem-authorization/duplication-confirmation": "El registro se ha duplicado.", "@sage/xtrem-authorization/enums__support_access_history_status__closed": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/enums__support_access_history_status__open": "<PERSON>bie<PERSON>o", "@sage/xtrem-authorization/enums__support_access_unit__days": "Días", "@sage/xtrem-authorization/enums__support_access_unit__hours": "<PERSON><PERSON>", "@sage/xtrem-authorization/enums__support_access_unit__minutes": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/enums__user_type__application": "Aplicación", "@sage/xtrem-authorization/enums__user_type__system": "Sistema", "@sage/xtrem-authorization/menu_item__support": "Soporte", "@sage/xtrem-authorization/menu_item__user-data": "Usuarios y seguridad", "@sage/xtrem-authorization/node-extensions__site_extension__property__groupRoleSites": "Rol de grupo de plantas", "@sage/xtrem-authorization/node-extensions__site_extension__property__siteGroups": "Grupos de plantas", "@sage/xtrem-authorization/node-extensions__user_extension__bulkMutation__sendBulkWelcomeMail": "Enviar e-mails de bienvenida en masa", "@sage/xtrem-authorization/node-extensions__user_extension__bulkMutation__sendBulkWelcomeMail__failed": "Error al enviar los e-mails de bienvenida en masa", "@sage/xtrem-authorization/node-extensions__user_extension__property__authorizationGroup": "Grupo de autorización", "@sage/xtrem-authorization/node-extensions__user_extension__property__billingRole": "Rol de facturación", "@sage/xtrem-authorization/node-extensions__user_extension__property__groupDisplay": "Visualización de grupo", "@sage/xtrem-authorization/node-extensions__user_extension__property__objectGrants": "Object grants", "@sage/xtrem-authorization/node-extensions__user_extension__query__all": "Todos", "@sage/xtrem-authorization/node-extensions__user_extension__query__all__failed": "<PERSON>rror al consultar todos", "@sage/xtrem-authorization/node-extensions__user_extension__query__all__parameter__isActive": "Activos", "@sage/xtrem-authorization/nodes__activity__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-authorization/nodes__activity__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-authorization/nodes__activity__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-authorization/nodes__activity__node_name": "Actividad", "@sage/xtrem-authorization/nodes__activity__property__description": "Descripción", "@sage/xtrem-authorization/nodes__activity__property__name": "Nombre", "@sage/xtrem-authorization/nodes__activity__property__package": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/nodes__activity__property__permissions": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/nodes__group_role__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-authorization/nodes__group_role__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-authorization/nodes__group_role__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-authorization/nodes__group_role__node_name": "Rol de grupo", "@sage/xtrem-authorization/nodes__group_role__property__groupRoleSite": "Rol de grupo de plantas", "@sage/xtrem-authorization/nodes__group_role__property__role": "Rol", "@sage/xtrem-authorization/nodes__group_role_site__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-authorization/nodes__group_role_site__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-authorization/nodes__group_role_site__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-authorization/nodes__group_role_site__node_name": "Rol de grupo de plantas", "@sage/xtrem-authorization/nodes__group_role_site__property__createdBy": "<PERSON><PERSON>o por", "@sage/xtrem-authorization/nodes__group_role_site__property__createStamp": "Creación", "@sage/xtrem-authorization/nodes__group_role_site__property__groupRoles": "Roles de grupo", "@sage/xtrem-authorization/nodes__group_role_site__property__groupRolesDisplay": "Visualización de roles de grupo", "@sage/xtrem-authorization/nodes__group_role_site__property__groupSites": "Grupos de plantas", "@sage/xtrem-authorization/nodes__group_role_site__property__groupSitesDisplay": "Visualización de grupo de plantas", "@sage/xtrem-authorization/nodes__group_role_site__property__id": "Id.", "@sage/xtrem-authorization/nodes__group_role_site__property__name": "Nombre", "@sage/xtrem-authorization/nodes__group_role_site__property__updatedBy": "Actualizado por", "@sage/xtrem-authorization/nodes__group_role_site__property__updateStamp": "Actualización", "@sage/xtrem-authorization/nodes__group_site__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-authorization/nodes__group_site__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-authorization/nodes__group_site__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-authorization/nodes__group_site__node_name": "Grupo de plantas", "@sage/xtrem-authorization/nodes__group_site__property__groupRoleSite": "Rol de grupo de plantas", "@sage/xtrem-authorization/nodes__group_site__property__siteGroup": "Grupo de plantas", "@sage/xtrem-authorization/nodes__restricted_node__node_name": "Nodo restringido", "@sage/xtrem-authorization/nodes__restricted_node__property__userGrants": "Permisos de usuario", "@sage/xtrem-authorization/nodes__restricted_node_user_grant__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-authorization/nodes__restricted_node_user_grant__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-authorization/nodes__restricted_node_user_grant__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-authorization/nodes__restricted_node_user_grant__node_name": "Nodo restringido basado en permisos de usuario", "@sage/xtrem-authorization/nodes__restricted_node_user_grant__property__accessMap": "Mapa de accesos", "@sage/xtrem-authorization/nodes__restricted_node_user_grant__property__object": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/nodes__restricted_node_user_grant__property__user": "Usuario", "@sage/xtrem-authorization/nodes__role__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-authorization/nodes__role__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-authorization/nodes__role__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-authorization/nodes__role__node_name": "Rol", "@sage/xtrem-authorization/nodes__role__property__activities": "Actividades", "@sage/xtrem-authorization/nodes__role__property__createdBy": "<PERSON><PERSON>o por", "@sage/xtrem-authorization/nodes__role__property__description": "Descripción", "@sage/xtrem-authorization/nodes__role__property__id": "Id.", "@sage/xtrem-authorization/nodes__role__property__isActive": "Activo", "@sage/xtrem-authorization/nodes__role__property__isBillingRole": "Rol de facturación", "@sage/xtrem-authorization/nodes__role__property__name": "Nombre", "@sage/xtrem-authorization/nodes__role__property__roles": "Roles", "@sage/xtrem-authorization/nodes__role__property__setupId": "Id. de parametrización", "@sage/xtrem-authorization/nodes__role__property__updatedBy": "Actualizado por", "@sage/xtrem-authorization/nodes__role__query__all": "Todos", "@sage/xtrem-authorization/nodes__role__query__all__failed": "<PERSON>rror al consultar todos", "@sage/xtrem-authorization/nodes__role__query__all__parameter__isActive": "Activos", "@sage/xtrem-authorization/nodes__role_activity__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-authorization/nodes__role_activity__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-authorization/nodes__role_activity__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-authorization/nodes__role_activity__mutation__roleActivitiesCreateUpdate": "Role activities create update", "@sage/xtrem-authorization/nodes__role_activity__mutation__roleActivitiesCreateUpdate__failed": "Role activities create update failed.", "@sage/xtrem-authorization/nodes__role_activity__mutation__roleActivitiesCreateUpdate__parameter__roleActivities": "Actividades de rol", "@sage/xtrem-authorization/nodes__role_activity__mutation__roleActivitiesCreateUpdate__parameter__roleId": "Id. de rol", "@sage/xtrem-authorization/nodes__role_activity__mutation__roleActivitiesCreateUpdate__parameter__roleName": "Nombre de rol", "@sage/xtrem-authorization/nodes__role_activity__mutation__roleActivitiesCreateUpdate__parameter__roleSysId": "Id. de sistema de rol", "@sage/xtrem-authorization/nodes__role_activity__node_name": "Actividad de rol", "@sage/xtrem-authorization/nodes__role_activity__property__activity": "Actividad", "@sage/xtrem-authorization/nodes__role_activity__property__getPermissions": "Obtener permisos", "@sage/xtrem-authorization/nodes__role_activity__property__hasAllPermissions": "Con todos los permisos", "@sage/xtrem-authorization/nodes__role_activity__property__isActive": "Activa", "@sage/xtrem-authorization/nodes__role_activity__property__permissions": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/nodes__role_activity__property__role": "Rol", "@sage/xtrem-authorization/nodes__role_to_role__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-authorization/nodes__role_to_role__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-authorization/nodes__role_to_role__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-authorization/nodes__role_to_role__node_name": "<PERSON><PERSON><PERSON> entre <PERSON>", "@sage/xtrem-authorization/nodes__role_to_role__property__role": "Rol", "@sage/xtrem-authorization/nodes__role_to_role__property__roleOrigin": "Origen de rol", "@sage/xtrem-authorization/nodes__site_group__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-authorization/nodes__site_group__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-authorization/nodes__site_group__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-authorization/nodes__site_group__node_name": "Grupo de plantas", "@sage/xtrem-authorization/nodes__site_group__property__createdBy": "<PERSON><PERSON>o por", "@sage/xtrem-authorization/nodes__site_group__property__hierarchyChartContent": "Contenido de gráfico de jerarquías", "@sage/xtrem-authorization/nodes__site_group__property__id": "Id.", "@sage/xtrem-authorization/nodes__site_group__property__isActive": "Activo", "@sage/xtrem-authorization/nodes__site_group__property__isLegalCompany": "Sociedad", "@sage/xtrem-authorization/nodes__site_group__property__name": "Nombre", "@sage/xtrem-authorization/nodes__site_group__property__siteGroups": "Grupos de plantas", "@sage/xtrem-authorization/nodes__site_group__property__sites": "Plantas", "@sage/xtrem-authorization/nodes__site_group__property__updatedBy": "Actualizado por", "@sage/xtrem-authorization/nodes__site_group_to_site__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-authorization/nodes__site_group_to_site__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-authorization/nodes__site_group_to_site__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-authorization/nodes__site_group_to_site__node_name": "Relación entre grupo de plantas y planta", "@sage/xtrem-authorization/nodes__site_group_to_site__property__dateAdd": "Fecha de adición", "@sage/xtrem-authorization/nodes__site_group_to_site__property__isValid": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/nodes__site_group_to_site__property__site": "Planta", "@sage/xtrem-authorization/nodes__site_group_to_site__property__siteGroup": "Grupo de plantas", "@sage/xtrem-authorization/nodes__site_group_to_site_group__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-authorization/nodes__site_group_to_site_group__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-authorization/nodes__site_group_to_site_group__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-authorization/nodes__site_group_to_site_group__node_name": "Relación entre grupos de plantas", "@sage/xtrem-authorization/nodes__site_group_to_site_group__property__siteGroup": "Grupo de plantas", "@sage/xtrem-authorization/nodes__site_group_to_site_group__property__siteGroupOrigin": "Origen de grupo de plantas", "@sage/xtrem-authorization/nodes__support_access__cannot_allow_support_access_because_there_is_a_open_session": "Hay una sesión abierta. Contacta con el administrador del sistema.", "@sage/xtrem-authorization/nodes__support_access__end_time_must_be_greater_than_start_time": "Introduce una hora de fin posterior a la hora de inicio.", "@sage/xtrem-authorization/nodes__support_access__number_must_be_greater_than_0": "Introduce un número superior a 0.", "@sage/xtrem-authorization/nodes__support_access__there_is_no_open_session_to_extend_the_access": "No hay ninguna sesión abierta para ampliar el acceso. Contacta con el administrador del sistema.", "@sage/xtrem-authorization/nodes__support_access__there_is_no_open_session_to_revoke_the_access": "No hay ninguna sesión abierta para revocar el acceso. Contacta con el administrador del sistema.", "@sage/xtrem-authorization/nodes__support_access_history__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-authorization/nodes__support_access_history__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-authorization/nodes__support_access_history__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-authorization/nodes__support_access_history__mutation__allowAccess": "<PERSON><PERSON><PERSON> acc<PERSON>o", "@sage/xtrem-authorization/nodes__support_access_history__mutation__allowAccess__failed": "Error al permitir el acceso", "@sage/xtrem-authorization/nodes__support_access_history__mutation__allowAccess__parameter__forTime": "For time", "@sage/xtrem-authorization/nodes__support_access_history__mutation__allowAccess__parameter__isReadOnlyAccess": "Acceso de solo lectura", "@sage/xtrem-authorization/nodes__support_access_history__mutation__allowAccess__parameter__units": "Unidades", "@sage/xtrem-authorization/nodes__support_access_history__mutation__extendAccess": "Ampliar acceso", "@sage/xtrem-authorization/nodes__support_access_history__mutation__extendAccess__failed": "Error al ampliar el acceso", "@sage/xtrem-authorization/nodes__support_access_history__mutation__extendAccess__parameter__forTime": "For time", "@sage/xtrem-authorization/nodes__support_access_history__mutation__extendAccess__parameter__units": "Unidades", "@sage/xtrem-authorization/nodes__support_access_history__mutation__revokeAccess": "Revocar acceso", "@sage/xtrem-authorization/nodes__support_access_history__mutation__revokeAccess__failed": "Error al revocar el acceso", "@sage/xtrem-authorization/nodes__support_access_history__node_name": "Historial de accesos a soporte", "@sage/xtrem-authorization/nodes__support_access_history__property__endTime": "Hora de fin", "@sage/xtrem-authorization/nodes__support_access_history__property__isReadOnlyAccess": "Acceso de solo lectura", "@sage/xtrem-authorization/nodes__support_access_history__property__startTime": "Hora de inicio", "@sage/xtrem-authorization/nodes__support_access_history__property__status": "Estado", "@sage/xtrem-authorization/nodes__support_access_history__property__timeToClose": "Tiempo restante", "@sage/xtrem-authorization/nodes__support_access_history__query__checkSupportAccessOpen": "Check support access open", "@sage/xtrem-authorization/nodes__support_access_history__query__checkSupportAccessOpen__failed": "Check support access open failed.", "@sage/xtrem-authorization/nodes__user__cannot_change_own_rights": "No puedes modificar tu grupo de autorización.", "@sage/xtrem-authorization/nodes__user_billing_role__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-authorization/nodes__user_billing_role__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-authorization/nodes__user_billing_role__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-authorization/nodes__user_billing_role__node_name": "Rol de facturación de usuario", "@sage/xtrem-authorization/nodes__user_billing_role__property__role": "Rol", "@sage/xtrem-authorization/nodes__user_billing_role__property__user": "Usuario", "@sage/xtrem-authorization/nodes__user_group__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-authorization/nodes__user_group__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-authorization/nodes__user_group__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-authorization/nodes__user_group__node_name": "Grupo de usuarios", "@sage/xtrem-authorization/nodes__user_group__property__createdBy": "<PERSON><PERSON>o por", "@sage/xtrem-authorization/nodes__user_group__property__createStamp": "Creación", "@sage/xtrem-authorization/nodes__user_group__property__group": "Grupo", "@sage/xtrem-authorization/nodes__user_group__property__isActive": "Activo", "@sage/xtrem-authorization/nodes__user_group__property__updatedBy": "Actualizada por", "@sage/xtrem-authorization/nodes__user_group__property__updateStamp": "Actualización", "@sage/xtrem-authorization/nodes__user_group__property__user": "Usuario", "@sage/xtrem-authorization/operation-not-allowed-on-object": "Esta operación no es compatible con este objeto.", "@sage/xtrem-authorization/package__name": "Autorización", "@sage/xtrem-authorization/pages__group_list____title": "Grupos", "@sage/xtrem-authorization/pages__group_list__addNewGroup____title": "Nuevo grupo", "@sage/xtrem-authorization/pages__group_list__groups____columns__title": "Creación", "@sage/xtrem-authorization/pages__group_list__groups____columns__title___id": "Id.", "@sage/xtrem-authorization/pages__group_list__groups____columns__title__2": "Actualización", "@sage/xtrem-authorization/pages__group_list__groups____columns__title__id": "Id.", "@sage/xtrem-authorization/pages__group_list__groups____columns__title__name": "Nombre", "@sage/xtrem-authorization/pages__group_list__groups____dropdownActions__title": "Eliminar rol", "@sage/xtrem-authorization/pages__group_list__groupsBlock____title": "Grupos de usuarios", "@sage/xtrem-authorization/pages__group_role_site____objectTypePlural": "Grupos de autorización", "@sage/xtrem-authorization/pages__group_role_site____objectTypeSingular": "Grupo de autorización", "@sage/xtrem-authorization/pages__group_role_site____subtitle": "Crea un grupo para dar derechos de acceso a los datos y las actividades a los usuarios.", "@sage/xtrem-authorization/pages__group_role_site____title": "Grupo de autorización", "@sage/xtrem-authorization/pages__group_role_site___id____title": "Id.", "@sage/xtrem-authorization/pages__group_role_site__allRoles____title": "<PERSON><PERSON><PERSON><PERSON><PERSON> to<PERSON>", "@sage/xtrem-authorization/pages__group_role_site__allSiteGroup____title": "<PERSON><PERSON><PERSON><PERSON><PERSON> to<PERSON>", "@sage/xtrem-authorization/pages__group_role_site__associatedUserSection____title": "Usuarios", "@sage/xtrem-authorization/pages__group_role_site__confirm____title": "Guardar", "@sage/xtrem-authorization/pages__group_role_site__customSave____title": "Guardar", "@sage/xtrem-authorization/pages__group_role_site__customSaveAction____title": "Guardar", "@sage/xtrem-authorization/pages__group_role_site__editGroupRoleSite____title": "Editar grupo de autorización", "@sage/xtrem-authorization/pages__group_role_site__groupInfoBlock____title": "Información de grupo", "@sage/xtrem-authorization/pages__group_role_site__groupList____title": "Ver grupos de autorización", "@sage/xtrem-authorization/pages__group_role_site__groupRoleBlock____title": "Roles de usuarios", "@sage/xtrem-authorization/pages__group_role_site__groupRoles____columns__title__role___id": "Id.", "@sage/xtrem-authorization/pages__group_role_site__groupRoles____columns__title__role__description": "Descripción", "@sage/xtrem-authorization/pages__group_role_site__groupRoles____columns__title__role__id": "Id.", "@sage/xtrem-authorization/pages__group_role_site__groupRoles____columns__title__role__name": "Nombre", "@sage/xtrem-authorization/pages__group_role_site__groupRoles____title": "Roles", "@sage/xtrem-authorization/pages__group_role_site__groupRolesActivities____columns__title__role": "Nombre", "@sage/xtrem-authorization/pages__group_role_site__groupRolesActivities____columns__title__role___id": "Id.", "@sage/xtrem-authorization/pages__group_role_site__groupRolesActivities____columns__title__role__description": "Descripción", "@sage/xtrem-authorization/pages__group_role_site__groupRolesActivities____columns__title__role__id": "Id.", "@sage/xtrem-authorization/pages__group_role_site__groupRolesActivities____dropdownActions__title": "Actividades", "@sage/xtrem-authorization/pages__group_role_site__groupRolesActivities____title": "Roles y actividades", "@sage/xtrem-authorization/pages__group_role_site__groupRolesMultiReference____columns__title___id": "Id.", "@sage/xtrem-authorization/pages__group_role_site__groupRolesMultiReference____columns__title__id": "Id.", "@sage/xtrem-authorization/pages__group_role_site__groupRolesMultiReference____columns__title__name": "Nombre", "@sage/xtrem-authorization/pages__group_role_site__groupRolesMultiReference____lookupDialogTitle": "Se<PERSON><PERSON><PERSON>r roles de usuarios", "@sage/xtrem-authorization/pages__group_role_site__groupSiteBlock____title": "Grupos de plantas", "@sage/xtrem-authorization/pages__group_role_site__groupSites____columns__title__siteGroup___id": "Id.", "@sage/xtrem-authorization/pages__group_role_site__groupSites____columns__title__siteGroup__id": "Id.", "@sage/xtrem-authorization/pages__group_role_site__groupSites____columns__title__siteGroup__name": "Nombre", "@sage/xtrem-authorization/pages__group_role_site__groupSites____title": "Plantas", "@sage/xtrem-authorization/pages__group_role_site__groupSitesMultiReference____columns__title___id": "Id.", "@sage/xtrem-authorization/pages__group_role_site__groupSitesMultiReference____columns__title__id": "Id.", "@sage/xtrem-authorization/pages__group_role_site__groupSitesMultiReference____columns__title__name": "Nombre", "@sage/xtrem-authorization/pages__group_role_site__groupSitesMultiReference____lookupDialogTitle": "Seleccionar grupos de plantas", "@sage/xtrem-authorization/pages__group_role_site__id____title": "Id.", "@sage/xtrem-authorization/pages__group_role_site__infoSection____title": "General", "@sage/xtrem-authorization/pages__group_role_site__name____title": "Nombre", "@sage/xtrem-authorization/pages__group_role_site__rolesActivitiesSection____title": "Roles y actividades", "@sage/xtrem-authorization/pages__group_role_site__users____columns__title": "Creación", "@sage/xtrem-authorization/pages__group_role_site__users____columns__title___id": "Id.", "@sage/xtrem-authorization/pages__group_role_site__users____columns__title__2": "Actualización", "@sage/xtrem-authorization/pages__group_role_site__users____columns__title__displayName": "Nombre de usuario", "@sage/xtrem-authorization/pages__group_role_site__users____columns__title__isActive": "Activo", "@sage/xtrem-authorization/pages__group_role_site__users____title": "Usuarios", "@sage/xtrem-authorization/pages__group_role_site_list____title": "Lista de grupos de autorización", "@sage/xtrem-authorization/pages__group_role_site_list__addNewGroup____title": "Añadir grupo", "@sage/xtrem-authorization/pages__group_role_site_list__groups____columns__title": "Creación", "@sage/xtrem-authorization/pages__group_role_site_list__groups____columns__title___id": "Id.", "@sage/xtrem-authorization/pages__group_role_site_list__groups____columns__title__2": "Actualización", "@sage/xtrem-authorization/pages__group_role_site_list__groups____columns__title__id": "Id.", "@sage/xtrem-authorization/pages__group_role_site_list__groups____columns__title__name": "Nombre", "@sage/xtrem-authorization/pages__group_role_site_list__groups____dropdownActions__title": "<PERSON><PERSON>", "@sage/xtrem-authorization/pages__group_role_site_list__groups____dropdownActions__title__2": "Eliminar", "@sage/xtrem-authorization/pages__group_role_site_list__groups____title": "Grupo de autorización", "@sage/xtrem-authorization/pages__group_role_site_list__groupsBlock____title": "Grupos de usuarios", "@sage/xtrem-authorization/pages__group_role_site_new_group_title": "Nuevo grupo de autorización", "@sage/xtrem-authorization/pages__new_group__group_created": "Grupo de autorización {{newGroupId}} creado", "@sage/xtrem-authorization/pages__new_group_panel____subtitle": "Crea un grupo para dar derechos de acceso a los datos y las actividades a los usuarios.", "@sage/xtrem-authorization/pages__new_group_panel____title": "Nuevo grupo", "@sage/xtrem-authorization/pages__new_group_panel__allRoles____title": "Todos los roles", "@sage/xtrem-authorization/pages__new_group_panel__allSiteAndSiteGroup____title": "Todas las plantas/grupos de plantas", "@sage/xtrem-authorization/pages__new_group_panel__cancelAction____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__new_group_panel__groupInfoBlock____title": "Información de grupo", "@sage/xtrem-authorization/pages__new_group_panel__id____title": "Id.", "@sage/xtrem-authorization/pages__new_group_panel__name____title": "Nombre", "@sage/xtrem-authorization/pages__new_group_panel__saveNewGroup____title": "Guardar", "@sage/xtrem-authorization/pages__new_group_panel__selectionSection____title": "Asociación de roles y plantas/grupos de plantas", "@sage/xtrem-authorization/pages__new_group_panel__selectRole____columns__title___id": "Id.", "@sage/xtrem-authorization/pages__new_group_panel__selectRole____columns__title__id": "Id.", "@sage/xtrem-authorization/pages__new_group_panel__selectRole____columns__title__name": "Nombre", "@sage/xtrem-authorization/pages__new_group_panel__selectSiteAndSiteGroup____columns__title___id": "Id.", "@sage/xtrem-authorization/pages__new_group_panel__selectSiteAndSiteGroup____columns__title__id": "Id.", "@sage/xtrem-authorization/pages__new_group_panel__selectSiteAndSiteGroup____columns__title__name": "Nombre", "@sage/xtrem-authorization/pages__operator_user_panel____title": "Restablecer código de operador", "@sage/xtrem-authorization/pages__operator_user_panel__cancel____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__operator_user_panel__confirm____title": "Confirmar", "@sage/xtrem-authorization/pages__operator_user_panel__operatorCodeBlock____title": "Bloque de código de operador", "@sage/xtrem-authorization/pages__operator_user_panel__operatorCodeInput____title": "Nuevo código de operador", "@sage/xtrem-authorization/pages__operator_user_panel__operatorCodeSection____title": "Sección de código de operador", "@sage/xtrem-authorization/pages__role_detail____subtitle": "Rol", "@sage/xtrem-authorization/pages__role_detail____title": "Rol", "@sage/xtrem-authorization/pages__role_detail__activitySection____title": "Actividades", "@sage/xtrem-authorization/pages__role_detail__cancel____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__role_detail__confirm____title": "Guardar", "@sage/xtrem-authorization/pages__role_detail__not_updated": "Ha habido un error al actualizar los permisos.", "@sage/xtrem-authorization/pages__role_detail__packageBlock1____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock10____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock11____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock12____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock13____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock14____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock15____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock16____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock17____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock18____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock19____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock2____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock20____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock3____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock4____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock5____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock6____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock7____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock8____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock9____title": "", "@sage/xtrem-authorization/pages__role_detail_updated": "Permisos actualizados", "@sage/xtrem-authorization/pages__role_list____title": "Roles", "@sage/xtrem-authorization/pages__role_list__addNewRole____title": "<PERSON><PERSON><PERSON> rol", "@sage/xtrem-authorization/pages__role_list__duplicate": "Duplicar", "@sage/xtrem-authorization/pages__role_list__field____columns__title___id": "Id.", "@sage/xtrem-authorization/pages__role_list__field____columns__title__id": "Id.", "@sage/xtrem-authorization/pages__role_list__field____columns__title__name": "Nombre", "@sage/xtrem-authorization/pages__role_list__field____dropdownActions__title": "Editar rol", "@sage/xtrem-authorization/pages__role_list__field____dropdownActions__title__2": "Clonar rol", "@sage/xtrem-authorization/pages__role_list__field____dropdownActions__title__3": "Ver grupos", "@sage/xtrem-authorization/pages__role_list__field____dropdownActions__title__4": "Eliminar rol", "@sage/xtrem-authorization/pages__role_list__fieldBlock____title": "Líneas de rol", "@sage/xtrem-authorization/pages__role_list__id____title": "Id.", "@sage/xtrem-authorization/pages__role_list__name____title": "Nombre", "@sage/xtrem-authorization/pages__role_list__roles____columns__title___id": "Id.", "@sage/xtrem-authorization/pages__role_list__roles____columns__title__createdByUserAndStamp": "Creación", "@sage/xtrem-authorization/pages__role_list__roles____columns__title__id": "Id.", "@sage/xtrem-authorization/pages__role_list__roles____columns__title__isBillingRole": "Facturación", "@sage/xtrem-authorization/pages__role_list__roles____columns__title__name": "Nombre", "@sage/xtrem-authorization/pages__role_list__roles____columns__title__updateByUserAndStamp": "Actualización", "@sage/xtrem-authorization/pages__role_list__roles____dropdownActions__title": "<PERSON><PERSON>", "@sage/xtrem-authorization/pages__role_list__roles____dropdownActions__title__2": "Eliminar", "@sage/xtrem-authorization/pages__role_list__roles____dropdownActions__title__3": "Duplicar", "@sage/xtrem-authorization/pages__role_list__rolesBlock____title": "Líneas de rol", "@sage/xtrem-authorization/pages__role_list__roleSection____title": "Rol", "@sage/xtrem-authorization/pages__role_setup": "<PERSON>ol creado", "@sage/xtrem-authorization/pages__role_setup____title": "Nuevo rol", "@sage/xtrem-authorization/pages__role_setup__cancel____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__role_setup__confirm____title": "Guardar", "@sage/xtrem-authorization/pages__role_setup__duplicated_id": "Este identificador ya existe. Introduce otro.", "@sage/xtrem-authorization/pages__role_setup__id____title": "Id.", "@sage/xtrem-authorization/pages__role_setup__name____title": "Nombre", "@sage/xtrem-authorization/pages__role_setup__next____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__role_setup__packageBlock1____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock10____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock11____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock12____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock13____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock14____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock15____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock16____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock17____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock18____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock19____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock2____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock20____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock3____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock4____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock5____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock6____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock7____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock8____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock9____title": "", "@sage/xtrem-authorization/pages__role_setup__packageSelect____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__role_setup__roleBlock____title": "Información de rol", "@sage/xtrem-authorization/pages__role_setup_failed": "Ha habido un error al crear el rol.", "@sage/xtrem-authorization/pages__site_group____subtitle": "Grupo de plantas", "@sage/xtrem-authorization/pages__site_group____title": "Grupo de plantas", "@sage/xtrem-authorization/pages__site_group___id____title": "Id.", "@sage/xtrem-authorization/pages__site_group__associatedAuthorizationGroupBlock____title": "Grupo de autorización", "@sage/xtrem-authorization/pages__site_group__associatedAuthorizationGroupSection____title": "Grupos de usuarios", "@sage/xtrem-authorization/pages__site_group__chartBlock____title": "Organización", "@sage/xtrem-authorization/pages__site_group__confirm____title": "Guardar", "@sage/xtrem-authorization/pages__site_group__customSave____title": "Guardar", "@sage/xtrem-authorization/pages__site_group__customSaveAction____title": "Guardar", "@sage/xtrem-authorization/pages__site_group__editSiteGroup____title": "Editar grupo de plantas", "@sage/xtrem-authorization/pages__site_group__generalBlock____title": "Grupo de plantas", "@sage/xtrem-authorization/pages__site_group__generalSection____title": "General", "@sage/xtrem-authorization/pages__site_group__groups____columns__title": "Creación", "@sage/xtrem-authorization/pages__site_group__groups____columns__title___id": "Id.", "@sage/xtrem-authorization/pages__site_group__groups____columns__title__2": "Actualización", "@sage/xtrem-authorization/pages__site_group__groups____columns__title__id": "Id.", "@sage/xtrem-authorization/pages__site_group__groups____columns__title__name": "Nombre", "@sage/xtrem-authorization/pages__site_group__groups____title": "Grupo de autorización", "@sage/xtrem-authorization/pages__site_group__hierarchyChartContent____title": "Organización", "@sage/xtrem-authorization/pages__site_group__id____title": "Id.", "@sage/xtrem-authorization/pages__site_group__isActive____title": "Activo", "@sage/xtrem-authorization/pages__site_group__isLegalCompany____title": "Sociedad", "@sage/xtrem-authorization/pages__site_group__name____title": "Nombre", "@sage/xtrem-authorization/pages__site_group__siteGroupList____title": "Lista de grupo de plantas", "@sage/xtrem-authorization/pages__site_group__siteGroups____columns__title__siteGroup___id": "Id.", "@sage/xtrem-authorization/pages__site_group__siteGroups____columns__title__siteGroup__id": "Id.", "@sage/xtrem-authorization/pages__site_group__siteGroups____columns__title__siteGroup__name": "Nombre", "@sage/xtrem-authorization/pages__site_group__siteGroupsBlock____title": "Grupos de plantas", "@sage/xtrem-authorization/pages__site_group__siteGroupsMultiReference____columns__title___id": "Id.", "@sage/xtrem-authorization/pages__site_group__siteGroupsMultiReference____columns__title__id": "Id.", "@sage/xtrem-authorization/pages__site_group__siteGroupsMultiReference____columns__title__name": "Nombre", "@sage/xtrem-authorization/pages__site_group__siteGroupsMultiReference____lookupDialogTitle": "Seleccionar grupos de plantas", "@sage/xtrem-authorization/pages__site_group__sites____columns__title__site___id": "Id.", "@sage/xtrem-authorization/pages__site_group__sites____columns__title__site__id": "Id.", "@sage/xtrem-authorization/pages__site_group__sites____columns__title__site__name": "Nombre", "@sage/xtrem-authorization/pages__site_group__sitesBlock____title": "Plantas", "@sage/xtrem-authorization/pages__site_group__sitesMultiReference____columns__title___id": "Id.", "@sage/xtrem-authorization/pages__site_group__sitesMultiReference____columns__title__id": "Id.", "@sage/xtrem-authorization/pages__site_group__sitesMultiReference____columns__title__name": "Nombre", "@sage/xtrem-authorization/pages__site_group__sitesMultiReference____lookupDialogTitle": "Seleccionar plantas", "@sage/xtrem-authorization/pages__site_group__toggleChart____helperText": "Permite alternar entre la visualización de tabla o de gráfico", "@sage/xtrem-authorization/pages__site_group__toggleChart____title": "Opciones de visualización", "@sage/xtrem-authorization/pages__site_group__toggleChartBlock____title": "Alternar entre gráfico y tabla", "@sage/xtrem-authorization/pages__site_group_chart": "Gráfico", "@sage/xtrem-authorization/pages__site_group_grid": "Tabla", "@sage/xtrem-authorization/pages__site_group_list____title": "Grupos de plantas", "@sage/xtrem-authorization/pages__site_group_list__addNewSiteGroup____title": "Nuevo grupo de plantas", "@sage/xtrem-authorization/pages__site_group_list__createSite____title": "Crear planta", "@sage/xtrem-authorization/pages__site_group_list__createSiteGroup____title": "Crear grupo de plantas", "@sage/xtrem-authorization/pages__site_group_list__fieldBlock____title": "Grupo de plantas", "@sage/xtrem-authorization/pages__site_group_list__groupList____title": "Lista de grupo de plantas", "@sage/xtrem-authorization/pages__site_group_list__section____title": "Grupos de plantas", "@sage/xtrem-authorization/pages__site_group_list__siteGroups____levels__columns__title___id": "Id.", "@sage/xtrem-authorization/pages__site_group_list__siteGroups____levels__columns__title__id": "Id.", "@sage/xtrem-authorization/pages__site_group_list__siteGroups____levels__columns__title__id__2": "Id.", "@sage/xtrem-authorization/pages__site_group_list__siteGroups____levels__columns__title__name": "Nombre", "@sage/xtrem-authorization/pages__site_group_list__siteGroups____levels__columns__title__name__2": "Nombre", "@sage/xtrem-authorization/pages__site_group_list__siteGroups____levels__dropdownActions__title": "<PERSON><PERSON>", "@sage/xtrem-authorization/pages__site_group_list__siteGroups____levels__dropdownActions__title__2": "Eliminar", "@sage/xtrem-authorization/pages__site_group_new_title": "Nuevo grupo de plantas", "@sage/xtrem-authorization/pages__support_access_history____title": "Acceso a soporte", "@sage/xtrem-authorization/pages__support_access_history__accessHistory____columns__title___id": "Id.", "@sage/xtrem-authorization/pages__support_access_history__accessHistory____columns__title__endTime": "Hora de fin", "@sage/xtrem-authorization/pages__support_access_history__accessHistory____columns__title__isReadOnlyAccess": "Acceso de solo lectura", "@sage/xtrem-authorization/pages__support_access_history__accessHistory____columns__title__startTime": "Hora de inicio", "@sage/xtrem-authorization/pages__support_access_history__accessHistory____columns__title__status": "Estado", "@sage/xtrem-authorization/pages__support_access_history__accessHistory____title": "Historial de accesos a soporte", "@sage/xtrem-authorization/pages__support_access_history__allow_access_button_text": "<PERSON><PERSON><PERSON> acc<PERSON>o", "@sage/xtrem-authorization/pages__support_access_history__description____content": "Especifica la duración del acceso del usuario.", "@sage/xtrem-authorization/pages__support_access_history__dummy____content": "", "@sage/xtrem-authorization/pages__support_access_history__extend_access_button_text": "Ampliar acceso", "@sage/xtrem-authorization/pages__support_access_history__forTime____title": "Número", "@sage/xtrem-authorization/pages__support_access_history__infoSection____title": "General", "@sage/xtrem-authorization/pages__support_access_history__isReadOnlyAccess____title": "Acceso de solo lectura", "@sage/xtrem-authorization/pages__support_access_history__isSupportAccessOpen____title": "Acceso de solo lectura", "@sage/xtrem-authorization/pages__support_access_history__please_fill_the_number_and_unit_field": "Introduce un número y una unidad.", "@sage/xtrem-authorization/pages__support_access_history__revoke_access_button_text": "Revocar acceso", "@sage/xtrem-authorization/pages__support_access_history__supportAccessBlock____title": "Permitir acceso a soporte de Sage", "@sage/xtrem-authorization/pages__support_access_history__supportAccessHistoryBlock____title": "Historial de accesos a soporte", "@sage/xtrem-authorization/pages__support_access_history__unit____title": "Unidades", "@sage/xtrem-authorization/pages__user____navigationPanel__bulkActions__title": "Enviar e-mail de bienvenida", "@sage/xtrem-authorization/pages__user____navigationPanel__listItem__image__title": "Imagen", "@sage/xtrem-authorization/pages__user____navigationPanel__listItem__line2__title": "E-mail", "@sage/xtrem-authorization/pages__user____navigationPanel__listItem__line3__title": "Usuario de API", "@sage/xtrem-authorization/pages__user____navigationPanel__listItem__title__title": "Nombre", "@sage/xtrem-authorization/pages__user____navigationPanel__listItem__titleRight__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__user____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-authorization/pages__user____navigationPanel__optionsMenu__title__2": "Interactivos", "@sage/xtrem-authorization/pages__user____navigationPanel__optionsMenu__title__3": "Aplicación de terceros", "@sage/xtrem-authorization/pages__user____objectTypePlural": "Usuarios", "@sage/xtrem-authorization/pages__user____objectTypeSingular": "Usuario", "@sage/xtrem-authorization/pages__user____subtitle": "Usuario", "@sage/xtrem-authorization/pages__user____title": "Usuarios", "@sage/xtrem-authorization/pages__user___id____title": "Id.", "@sage/xtrem-authorization/pages__user__associatedRoles____columns__title___id": "Id.", "@sage/xtrem-authorization/pages__user__associatedRoles____columns__title__createdBy": "Creación", "@sage/xtrem-authorization/pages__user__associatedRoles____columns__title__id": "Id.", "@sage/xtrem-authorization/pages__user__associatedRoles____columns__title__name": "Nombre", "@sage/xtrem-authorization/pages__user__associatedRoles____columns__title__updatedBy": "Actualización", "@sage/xtrem-authorization/pages__user__associatedRoles____title": "Roles y actividades", "@sage/xtrem-authorization/pages__user__associatedSiteGroups____columns__title___id": "Id.", "@sage/xtrem-authorization/pages__user__associatedSiteGroups____columns__title__createdBy": "Creación", "@sage/xtrem-authorization/pages__user__associatedSiteGroups____columns__title__id": "Id.", "@sage/xtrem-authorization/pages__user__associatedSiteGroups____columns__title__name": "Nombre", "@sage/xtrem-authorization/pages__user__associatedSiteGroups____columns__title__updatedBy": "Actualización", "@sage/xtrem-authorization/pages__user__associatedSiteGroups____title": "Grupos de plantas", "@sage/xtrem-authorization/pages__user__associatedSiteGroupsSection____title": "Grupos de plantas", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__columns__group__id__title": "Id.", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__columns__group__id__title__2": "Nombre", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__columns__group__id__title__3": "Id.", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__title__group___id": "Id.", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__title__group__groupRolesDisplay": "Roles", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__title__group__groupSitesDisplay": "Grupo de plantas", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__title__group__id": "Id.", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__title__group__name": "Nombre", "@sage/xtrem-authorization/pages__user__authorizationGroup____title": "Grupos de autorización", "@sage/xtrem-authorization/pages__user__editUser____title": "<PERSON>ar usuario", "@sage/xtrem-authorization/pages__user__email____title": "E-mail", "@sage/xtrem-authorization/pages__user__firstName____title": "Nombre", "@sage/xtrem-authorization/pages__user__generalSection____title": "General", "@sage/xtrem-authorization/pages__user__importExportDateFormat____helperText": "Valor por defecto ", "@sage/xtrem-authorization/pages__user__importExportDateFormat____title": "Formato de fecha de importación/exportación", "@sage/xtrem-authorization/pages__user__importExportDelimiter____helperText": "Valor por defecto ", "@sage/xtrem-authorization/pages__user__importExportDelimiter____title": "Separador de importación/exportación", "@sage/xtrem-authorization/pages__user__isActive____title": "Activo", "@sage/xtrem-authorization/pages__user__isAdministrator____title": "Administrador", "@sage/xtrem-authorization/pages__user__isApiUser____title": "Usuario de API", "@sage/xtrem-authorization/pages__user__isDemoPersona____title": "Perfil para demostraciones", "@sage/xtrem-authorization/pages__user__isExternal____title": "Externo", "@sage/xtrem-authorization/pages__user__isOperatorUser____title": "Autenticación de PIN", "@sage/xtrem-authorization/pages__user__isWelcomeMailSent____title": "Enviar e-mail de bienvenida", "@sage/xtrem-authorization/pages__user__lastName____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__user__operatorCode____title": "Código PIN", "@sage/xtrem-authorization/pages__user__preferencesBlock____title": "Preferencias", "@sage/xtrem-authorization/pages__user__preferencesSection____title": "Preferencias", "@sage/xtrem-authorization/pages__user__resetOperatorCodePage____title": "Código PIN", "@sage/xtrem-authorization/pages__user__role____columns__title__id": "Id.", "@sage/xtrem-authorization/pages__user__role____columns__title__name": "Nombre", "@sage/xtrem-authorization/pages__user__role____lookupDialogTitle": "Seleccionar rol de facturación", "@sage/xtrem-authorization/pages__user__role____title": "Rol de facturación", "@sage/xtrem-authorization/pages__user__rolesActivitiesSection____title": "Roles y actividades", "@sage/xtrem-authorization/pages__user__save____title": "Guardar", "@sage/xtrem-authorization/pages__user__send_welcome_mail": "Enviar e-mail", "@sage/xtrem-authorization/pages__user__send_welcome_mail_button": "Enviar", "@sage/xtrem-authorization/pages__user__send_welcome_mail_dialog_content": "¿Quieres enviar un e-mail de bienvenida al usuario?", "@sage/xtrem-authorization/pages__user__userAuthorizationInformationBlock____title": "Grupo de autorización", "@sage/xtrem-authorization/pages__user__userGroups____columns__title___id": "Id.", "@sage/xtrem-authorization/pages__user__userGroups____columns__title__id": "Id.", "@sage/xtrem-authorization/pages__user__userGroups____columns__title__name": "Nombre", "@sage/xtrem-authorization/pages__user__userGroups____lookupDialogTitle": "Seleccionar grupo de autorización", "@sage/xtrem-authorization/pages__user__userGroups____title": "Grupo de autorización", "@sage/xtrem-authorization/pages__user__userInformationBlock____title": "Información de usuario", "@sage/xtrem-authorization/pages__user__userList____title": "Consulta de usuarios", "@sage/xtrem-authorization/pages__user__userPhotoBlock____title": "Foto", "@sage/xtrem-authorization/pages__user_group_list____title": "Grupos de usuarios", "@sage/xtrem-authorization/pages__user_group_list__groupList____title": "Lista de grupos", "@sage/xtrem-authorization/pages__user_group_list__title": "Grupos de usuarios", "@sage/xtrem-authorization/pages__user_group_list__users____columns__title": "Creación", "@sage/xtrem-authorization/pages__user_group_list__users____columns__title___id": "Id.", "@sage/xtrem-authorization/pages__user_group_list__users____columns__title__2": "Actualización", "@sage/xtrem-authorization/pages__user_group_list__users____columns__title__displayName": "Nombre de usuario", "@sage/xtrem-authorization/pages__user_group_list__users____columns__title__isActive": "Activo", "@sage/xtrem-authorization/pages__user_group_list__usersBlock____title": "Grupos de usuarios", "@sage/xtrem-authorization/pages_role_list_delete_confirmation": "Confirmar eliminación", "@sage/xtrem-authorization/pages_role_list_delete_message": "¿Quieres eliminar el rol {{role}}?", "@sage/xtrem-authorization/pages-confirm-cancel": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/permission__all__name": "Todos", "@sage/xtrem-authorization/permission__allow_access__name": "<PERSON><PERSON><PERSON> acc<PERSON>o", "@sage/xtrem-authorization/permission__create__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/permission__delete__name": "Eliminar", "@sage/xtrem-authorization/permission__extend_access__name": "Ampliar acceso", "@sage/xtrem-authorization/permission__is_support_access_open__name": "Acceso a soporte", "@sage/xtrem-authorization/permission__lookup__name": "Buscar", "@sage/xtrem-authorization/permission__manage__name": "Gestionar", "@sage/xtrem-authorization/permission__read__name": "<PERSON><PERSON>", "@sage/xtrem-authorization/permission__revoke_access__name": "Revocar acceso", "@sage/xtrem-authorization/permission__update__name": "Actualizar", "@sage/xtrem-authorization/reset-operator-code": "Restablecer", "@sage/xtrem-authorization/role_detail_page_is_not_read_only": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/role_detail_page_is_read_only": "Aceptar", "@sage/xtrem-authorization/service_options__authorization_service_option__name": "Opción de servicio de autorización", "@sage/xtrem-authorization/shared_common_package_xtrem_inventory": "Inventario", "@sage/xtrem-authorization/stickers__demo_persona_sticker____title": "Perfil para demostraciones", "@sage/xtrem-authorization/stickers__demo_persona_sticker__section____title": "Perfil", "@sage/xtrem-authorization/stickers__demo_persona_sticker__selectionPersona____title": "Perfil para demostraciones"}