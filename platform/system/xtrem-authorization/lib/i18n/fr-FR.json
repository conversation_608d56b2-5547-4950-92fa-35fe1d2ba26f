{"@sage/xtrem-authorization/activity__group_role_site__name": "Groupe de rôles site", "@sage/xtrem-authorization/activity__role__name": "R<PERSON><PERSON>", "@sage/xtrem-authorization/activity__site_group__name": "Groupe de sites", "@sage/xtrem-authorization/activity__support_access_history__name": "Historique d'accès support", "@sage/xtrem-authorization/activity__user__name": "Utilisa<PERSON>ur", "@sage/xtrem-authorization/cannot-delete-site-from-site-group": "Le site ne peut pas être retiré des groupes de sites associés.", "@sage/xtrem-authorization/cannot-delete-site-group": "Un groupe de sites lié ne peut pas être supprimé.", "@sage/xtrem-authorization/data_types__name_array_data_type__name": "Type de données tableau nom", "@sage/xtrem-authorization/data_types__string_data_type__name": "Type de données chaîne", "@sage/xtrem-authorization/data_types__support_access_history_status_enum__name": "Enum statut historique accès support", "@sage/xtrem-authorization/data_types__support_access_unit_enum__name": "Enum unité accès support", "@sage/xtrem-authorization/data_types__user_type_enum__name": "Enum type utilisateur", "@sage/xtrem-authorization/delete-confirmation": "Enregistrement supprimé", "@sage/xtrem-authorization/delete-dialog-content": "Vous allez supprimer ce groupe", "@sage/xtrem-authorization/delete-group": "Confirmer la <PERSON>", "@sage/xtrem-authorization/duplication-confirmation": "Enregistre<PERSON> dupliqué", "@sage/xtrem-authorization/enums__support_access_history_status__closed": "<PERSON><PERSON>", "@sage/xtrem-authorization/enums__support_access_history_status__open": "Ouvert", "@sage/xtrem-authorization/enums__support_access_unit__days": "Jours", "@sage/xtrem-authorization/enums__support_access_unit__hours": "<PERSON><PERSON>", "@sage/xtrem-authorization/enums__support_access_unit__minutes": "Minutes", "@sage/xtrem-authorization/enums__user_type__application": "Application", "@sage/xtrem-authorization/enums__user_type__system": "Système", "@sage/xtrem-authorization/menu_item__support": "Support", "@sage/xtrem-authorization/menu_item__user-data": "Utilisateurs et sécurité", "@sage/xtrem-authorization/node-extensions__site_extension__property__groupRoleSites": "Groupe de rôles site", "@sage/xtrem-authorization/node-extensions__site_extension__property__siteGroups": "Groupes de sites", "@sage/xtrem-authorization/node-extensions__user_extension__bulkMutation__sendBulkWelcomeMail": "Envoyer e-mail de bienvenue en masse", "@sage/xtrem-authorization/node-extensions__user_extension__bulkMutation__sendBulkWelcomeMail__failed": "Échec envoi e-mail de bienvenue en masse.", "@sage/xtrem-authorization/node-extensions__user_extension__property__authorizationGroup": "Groupe d'autorisations", "@sage/xtrem-authorization/node-extensions__user_extension__property__billingRole": "Rôle de facturation", "@sage/xtrem-authorization/node-extensions__user_extension__property__groupDisplay": "Affichage groupe", "@sage/xtrem-authorization/node-extensions__user_extension__property__objectGrants": "Objet permissions", "@sage/xtrem-authorization/node-extensions__user_extension__query__all": "<PERSON>ut", "@sage/xtrem-authorization/node-extensions__user_extension__query__all__failed": "Échec complet.", "@sage/xtrem-authorization/node-extensions__user_extension__query__all__parameter__isActive": "Active", "@sage/xtrem-authorization/nodes__activity__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-authorization/nodes__activity__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-authorization/nodes__activity__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-authorization/nodes__activity__node_name": "Activité", "@sage/xtrem-authorization/nodes__activity__property__description": "Description", "@sage/xtrem-authorization/nodes__activity__property__name": "Nom", "@sage/xtrem-authorization/nodes__activity__property__package": "Package", "@sage/xtrem-authorization/nodes__activity__property__permissions": "Permissions", "@sage/xtrem-authorization/nodes__group_role__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-authorization/nodes__group_role__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-authorization/nodes__group_role__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-authorization/nodes__group_role__node_name": "Groupe de rôles", "@sage/xtrem-authorization/nodes__group_role__property__groupRoleSite": "Groupe de rôles site", "@sage/xtrem-authorization/nodes__group_role__property__role": "R<PERSON><PERSON>", "@sage/xtrem-authorization/nodes__group_role_site__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-authorization/nodes__group_role_site__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-authorization/nodes__group_role_site__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-authorization/nodes__group_role_site__node_name": "Groupe de rôles site", "@sage/xtrem-authorization/nodes__group_role_site__property__createdBy": "C<PERSON><PERSON> par", "@sage/xtrem-authorization/nodes__group_role_site__property__createStamp": "<PERSON><PERSON><PERSON> horodatage", "@sage/xtrem-authorization/nodes__group_role_site__property__groupRoles": "Groupe de rôles", "@sage/xtrem-authorization/nodes__group_role_site__property__groupRolesDisplay": "Affichage de groupes de rôles", "@sage/xtrem-authorization/nodes__group_role_site__property__groupSites": "Groupes de sites", "@sage/xtrem-authorization/nodes__group_role_site__property__groupSitesDisplay": "Affichage groupe de sites", "@sage/xtrem-authorization/nodes__group_role_site__property__id": "Code", "@sage/xtrem-authorization/nodes__group_role_site__property__name": "Nom", "@sage/xtrem-authorization/nodes__group_role_site__property__updatedBy": "Mis à jour par", "@sage/xtrem-authorization/nodes__group_role_site__property__updateStamp": "Mettre à jour horodatage", "@sage/xtrem-authorization/nodes__group_site__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-authorization/nodes__group_site__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-authorization/nodes__group_site__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-authorization/nodes__group_site__node_name": "Groupe de sites", "@sage/xtrem-authorization/nodes__group_site__property__groupRoleSite": "Groupe de rôles site", "@sage/xtrem-authorization/nodes__group_site__property__siteGroup": "Groupe de sites", "@sage/xtrem-authorization/nodes__restricted_node__node_name": "Node restreint", "@sage/xtrem-authorization/nodes__restricted_node__property__userGrants": "Droits utilisateurs", "@sage/xtrem-authorization/nodes__restricted_node_user_grant__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-authorization/nodes__restricted_node_user_grant__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-authorization/nodes__restricted_node_user_grant__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-authorization/nodes__restricted_node_user_grant__node_name": "Node restreint sur la base des permissions utilisateurs", "@sage/xtrem-authorization/nodes__restricted_node_user_grant__property__accessMap": "Carte d'a<PERSON>", "@sage/xtrem-authorization/nodes__restricted_node_user_grant__property__object": "Objet", "@sage/xtrem-authorization/nodes__restricted_node_user_grant__property__user": "Utilisa<PERSON>ur", "@sage/xtrem-authorization/nodes__role__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-authorization/nodes__role__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-authorization/nodes__role__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-authorization/nodes__role__node_name": "R<PERSON><PERSON>", "@sage/xtrem-authorization/nodes__role__property__activities": "Activités", "@sage/xtrem-authorization/nodes__role__property__createdBy": "C<PERSON><PERSON> par", "@sage/xtrem-authorization/nodes__role__property__description": "Description", "@sage/xtrem-authorization/nodes__role__property__id": "Code", "@sage/xtrem-authorization/nodes__role__property__isActive": "Actif", "@sage/xtrem-authorization/nodes__role__property__isBillingRole": "Rôle de facturation", "@sage/xtrem-authorization/nodes__role__property__name": "Nom", "@sage/xtrem-authorization/nodes__role__property__roles": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/nodes__role__property__setupId": "Code paramétrage", "@sage/xtrem-authorization/nodes__role__property__updatedBy": "Mis à jour par", "@sage/xtrem-authorization/nodes__role__query__all": "<PERSON>ut", "@sage/xtrem-authorization/nodes__role__query__all__failed": "Échec complet.", "@sage/xtrem-authorization/nodes__role__query__all__parameter__isActive": "Actif", "@sage/xtrem-authorization/nodes__role_activity__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-authorization/nodes__role_activity__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-authorization/nodes__role_activity__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-authorization/nodes__role_activity__mutation__roleActivitiesCreateUpdate": "Activités du rôle création et mise à jour", "@sage/xtrem-authorization/nodes__role_activity__mutation__roleActivitiesCreateUpdate__failed": "La mise à jour de la création des activités du rôle a échoué.", "@sage/xtrem-authorization/nodes__role_activity__mutation__roleActivitiesCreateUpdate__parameter__roleActivities": "Activités du rôle", "@sage/xtrem-authorization/nodes__role_activity__mutation__roleActivitiesCreateUpdate__parameter__roleId": "Code du rôle", "@sage/xtrem-authorization/nodes__role_activity__mutation__roleActivitiesCreateUpdate__parameter__roleName": "Nom du rôle", "@sage/xtrem-authorization/nodes__role_activity__mutation__roleActivitiesCreateUpdate__parameter__roleSysId": "Code système du rôle", "@sage/xtrem-authorization/nodes__role_activity__node_name": "Activité du rôle", "@sage/xtrem-authorization/nodes__role_activity__property__activity": "Activité", "@sage/xtrem-authorization/nodes__role_activity__property__getPermissions": "Obt<PERSON><PERSON> les permissions", "@sage/xtrem-authorization/nodes__role_activity__property__hasAllPermissions": "Avec toutes les autorisations", "@sage/xtrem-authorization/nodes__role_activity__property__isActive": "Active", "@sage/xtrem-authorization/nodes__role_activity__property__permissions": "Permissions", "@sage/xtrem-authorization/nodes__role_activity__property__role": "R<PERSON><PERSON>", "@sage/xtrem-authorization/nodes__role_to_role__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-authorization/nodes__role_to_role__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-authorization/nodes__role_to_role__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-authorization/nodes__role_to_role__node_name": "Rôle à rôle", "@sage/xtrem-authorization/nodes__role_to_role__property__role": "R<PERSON><PERSON>", "@sage/xtrem-authorization/nodes__role_to_role__property__roleOrigin": "Origine du rôle", "@sage/xtrem-authorization/nodes__site_group__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-authorization/nodes__site_group__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-authorization/nodes__site_group__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-authorization/nodes__site_group__node_name": "Groupe de sites", "@sage/xtrem-authorization/nodes__site_group__property__createdBy": "C<PERSON><PERSON> par", "@sage/xtrem-authorization/nodes__site_group__property__hierarchyChartContent": "Contenu du graphique hiérarchique", "@sage/xtrem-authorization/nodes__site_group__property__id": "Code", "@sage/xtrem-authorization/nodes__site_group__property__isActive": "Actif", "@sage/xtrem-authorization/nodes__site_group__property__isLegalCompany": "Société", "@sage/xtrem-authorization/nodes__site_group__property__name": "Nom", "@sage/xtrem-authorization/nodes__site_group__property__siteGroups": "Groupes de sites", "@sage/xtrem-authorization/nodes__site_group__property__sites": "Sites", "@sage/xtrem-authorization/nodes__site_group__property__updatedBy": "Mis à jour par", "@sage/xtrem-authorization/nodes__site_group_to_site__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-authorization/nodes__site_group_to_site__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-authorization/nodes__site_group_to_site__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-authorization/nodes__site_group_to_site__node_name": "Groupe de site en site", "@sage/xtrem-authorization/nodes__site_group_to_site__property__dateAdd": "Date d'ajout", "@sage/xtrem-authorization/nodes__site_group_to_site__property__isValid": "Valide", "@sage/xtrem-authorization/nodes__site_group_to_site__property__site": "Site", "@sage/xtrem-authorization/nodes__site_group_to_site__property__siteGroup": "Groupe de sites", "@sage/xtrem-authorization/nodes__site_group_to_site_group__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-authorization/nodes__site_group_to_site_group__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-authorization/nodes__site_group_to_site_group__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-authorization/nodes__site_group_to_site_group__node_name": "Groupe de site en groupe de site", "@sage/xtrem-authorization/nodes__site_group_to_site_group__property__siteGroup": "Groupe de sites", "@sage/xtrem-authorization/nodes__site_group_to_site_group__property__siteGroupOrigin": "Origine groupe de sites", "@sage/xtrem-authorization/nodes__support_access__cannot_allow_support_access_because_there_is_a_open_session": "Une session est déjà ouverte. Contactez votre administrateur système.", "@sage/xtrem-authorization/nodes__support_access__end_time_must_be_greater_than_start_time": "Renseignez une heure de fin postérieure à l'heure de début.", "@sage/xtrem-authorization/nodes__support_access__number_must_be_greater_than_0": "Renseignez un numéro supérieur à 0.", "@sage/xtrem-authorization/nodes__support_access__there_is_no_open_session_to_extend_the_access": "Aucune session n'est ouverte pour étendre l'accès. Contactez votre administrateur système.", "@sage/xtrem-authorization/nodes__support_access__there_is_no_open_session_to_revoke_the_access": "Aucune session n'est ouverte pour révoquer l'accès. Contactez votre administrateur système.", "@sage/xtrem-authorization/nodes__support_access_history__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-authorization/nodes__support_access_history__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-authorization/nodes__support_access_history__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-authorization/nodes__support_access_history__mutation__allowAccess": "Accorder l'accès", "@sage/xtrem-authorization/nodes__support_access_history__mutation__allowAccess__failed": "L'autorisation d'accès a échoué.", "@sage/xtrem-authorization/nodes__support_access_history__mutation__allowAccess__parameter__forTime": "Pour l'heure", "@sage/xtrem-authorization/nodes__support_access_history__mutation__allowAccess__parameter__isReadOnlyAccess": "Accès en lecture seule", "@sage/xtrem-authorization/nodes__support_access_history__mutation__allowAccess__parameter__units": "Unités", "@sage/xtrem-authorization/nodes__support_access_history__mutation__extendAccess": "É<PERSON>re l'accès", "@sage/xtrem-authorization/nodes__support_access_history__mutation__extendAccess__failed": "L'extension d'accès a échoué.", "@sage/xtrem-authorization/nodes__support_access_history__mutation__extendAccess__parameter__forTime": "Pour l'heure", "@sage/xtrem-authorization/nodes__support_access_history__mutation__extendAccess__parameter__units": "Unités", "@sage/xtrem-authorization/nodes__support_access_history__mutation__revokeAccess": "Révoquer l'accès", "@sage/xtrem-authorization/nodes__support_access_history__mutation__revokeAccess__failed": "La révocation d'accès a échoué.", "@sage/xtrem-authorization/nodes__support_access_history__node_name": "Historique d'accès support", "@sage/xtrem-authorization/nodes__support_access_history__property__endTime": "Heure de fin", "@sage/xtrem-authorization/nodes__support_access_history__property__isReadOnlyAccess": "Accès en lecture seule", "@sage/xtrem-authorization/nodes__support_access_history__property__startTime": "<PERSON><PERSON> d<PERSON>", "@sage/xtrem-authorization/nodes__support_access_history__property__status": "Statut", "@sage/xtrem-authorization/nodes__support_access_history__property__timeToClose": "Heure de clôture", "@sage/xtrem-authorization/nodes__support_access_history__query__checkSupportAccessOpen": "Contrôle d'ouverture de l'accès au support", "@sage/xtrem-authorization/nodes__support_access_history__query__checkSupportAccessOpen__failed": "Le contrôle d'ouverture de l'accès au support  a échoué.", "@sage/xtrem-authorization/nodes__user__cannot_change_own_rights": "Vous n'êtes pas autorisé à modifier votre propre groupe d'autorisations.", "@sage/xtrem-authorization/nodes__user_billing_role__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-authorization/nodes__user_billing_role__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-authorization/nodes__user_billing_role__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-authorization/nodes__user_billing_role__node_name": "Rôle de facturation utilisateur", "@sage/xtrem-authorization/nodes__user_billing_role__property__role": "R<PERSON><PERSON>", "@sage/xtrem-authorization/nodes__user_billing_role__property__user": "Utilisa<PERSON>ur", "@sage/xtrem-authorization/nodes__user_group__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-authorization/nodes__user_group__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-authorization/nodes__user_group__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-authorization/nodes__user_group__node_name": "Groupe d'utilisateurs", "@sage/xtrem-authorization/nodes__user_group__property__createdBy": "C<PERSON><PERSON> par", "@sage/xtrem-authorization/nodes__user_group__property__createStamp": "<PERSON><PERSON><PERSON> horodatage", "@sage/xtrem-authorization/nodes__user_group__property__group": "Groupe", "@sage/xtrem-authorization/nodes__user_group__property__isActive": "Actif", "@sage/xtrem-authorization/nodes__user_group__property__updatedBy": "Mis à jour par", "@sage/xtrem-authorization/nodes__user_group__property__updateStamp": "Mettre à jour horodatage", "@sage/xtrem-authorization/nodes__user_group__property__user": "Utilisa<PERSON>ur", "@sage/xtrem-authorization/operation-not-allowed-on-object": "L'opération n'est pas autorisée pour cet objet.", "@sage/xtrem-authorization/package__name": "Autorisation", "@sage/xtrem-authorization/pages__group_list____title": "Groupes", "@sage/xtrem-authorization/pages__group_list__addNewGroup____title": "Nouveau groupe", "@sage/xtrem-authorization/pages__group_list__groups____columns__title": "C<PERSON><PERSON> par", "@sage/xtrem-authorization/pages__group_list__groups____columns__title___id": "ID", "@sage/xtrem-authorization/pages__group_list__groups____columns__title__2": "Mis à jour par", "@sage/xtrem-authorization/pages__group_list__groups____columns__title__id": "ID", "@sage/xtrem-authorization/pages__group_list__groups____columns__title__name": "Nom", "@sage/xtrem-authorization/pages__group_list__groups____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON> le rôle", "@sage/xtrem-authorization/pages__group_list__groupsBlock____title": "Groupes utilisateurs", "@sage/xtrem-authorization/pages__group_role_site____objectTypePlural": "Groupes d'autorisations", "@sage/xtrem-authorization/pages__group_role_site____objectTypeSingular": "Groupe d'autorisations", "@sage/xtrem-authorization/pages__group_role_site____subtitle": "Créer un groupe pour accorder les droits d'activité et d'accès aux utilisateurs.", "@sage/xtrem-authorization/pages__group_role_site____title": "Groupe d'autorisations", "@sage/xtrem-authorization/pages__group_role_site___id____title": "Code", "@sage/xtrem-authorization/pages__group_role_site__allRoles____title": "Sélectionner tous les rôles", "@sage/xtrem-authorization/pages__group_role_site__allSiteGroup____title": "Sélectionner tous les groupes", "@sage/xtrem-authorization/pages__group_role_site__associatedUserSection____title": "Utilisateurs", "@sage/xtrem-authorization/pages__group_role_site__confirm____title": "Enregistrer", "@sage/xtrem-authorization/pages__group_role_site__customSave____title": "Enregistrer", "@sage/xtrem-authorization/pages__group_role_site__customSaveAction____title": "Enregistrer", "@sage/xtrem-authorization/pages__group_role_site__editGroupRoleSite____title": "Modifier le groupe d'autorisations", "@sage/xtrem-authorization/pages__group_role_site__groupInfoBlock____title": "Information groupe", "@sage/xtrem-authorization/pages__group_role_site__groupList____title": "Voir les groupes d'autorisations", "@sage/xtrem-authorization/pages__group_role_site__groupRoleBlock____title": "Rôles utilisateurs", "@sage/xtrem-authorization/pages__group_role_site__groupRoles____columns__title__role___id": "Code", "@sage/xtrem-authorization/pages__group_role_site__groupRoles____columns__title__role__description": "Description", "@sage/xtrem-authorization/pages__group_role_site__groupRoles____columns__title__role__id": "Code", "@sage/xtrem-authorization/pages__group_role_site__groupRoles____columns__title__role__name": "Nom", "@sage/xtrem-authorization/pages__group_role_site__groupRoles____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__group_role_site__groupRolesActivities____columns__title__role": "Nom", "@sage/xtrem-authorization/pages__group_role_site__groupRolesActivities____columns__title__role___id": "Code", "@sage/xtrem-authorization/pages__group_role_site__groupRolesActivities____columns__title__role__description": "Description", "@sage/xtrem-authorization/pages__group_role_site__groupRolesActivities____columns__title__role__id": "Code", "@sage/xtrem-authorization/pages__group_role_site__groupRolesActivities____dropdownActions__title": "Activités", "@sage/xtrem-authorization/pages__group_role_site__groupRolesActivities____title": "Rôles et activités", "@sage/xtrem-authorization/pages__group_role_site__groupRolesMultiReference____columns__title___id": "Code", "@sage/xtrem-authorization/pages__group_role_site__groupRolesMultiReference____columns__title__id": "Code", "@sage/xtrem-authorization/pages__group_role_site__groupRolesMultiReference____columns__title__name": "Nom", "@sage/xtrem-authorization/pages__group_role_site__groupRolesMultiReference____lookupDialogTitle": "Sélectionner les rôles utilisateurs", "@sage/xtrem-authorization/pages__group_role_site__groupSiteBlock____title": "Groupes de sites", "@sage/xtrem-authorization/pages__group_role_site__groupSites____columns__title__siteGroup___id": "Code", "@sage/xtrem-authorization/pages__group_role_site__groupSites____columns__title__siteGroup__id": "Code", "@sage/xtrem-authorization/pages__group_role_site__groupSites____columns__title__siteGroup__name": "Nom", "@sage/xtrem-authorization/pages__group_role_site__groupSites____title": "Sites", "@sage/xtrem-authorization/pages__group_role_site__groupSitesMultiReference____columns__title___id": "Code", "@sage/xtrem-authorization/pages__group_role_site__groupSitesMultiReference____columns__title__id": "Code", "@sage/xtrem-authorization/pages__group_role_site__groupSitesMultiReference____columns__title__name": "Nom", "@sage/xtrem-authorization/pages__group_role_site__groupSitesMultiReference____lookupDialogTitle": "Sélectionner les groupes de sites", "@sage/xtrem-authorization/pages__group_role_site__id____title": "Code", "@sage/xtrem-authorization/pages__group_role_site__infoSection____title": "Général", "@sage/xtrem-authorization/pages__group_role_site__name____title": "Nom", "@sage/xtrem-authorization/pages__group_role_site__rolesActivitiesSection____title": "Rôles et activités", "@sage/xtrem-authorization/pages__group_role_site__users____columns__title": "C<PERSON><PERSON> par", "@sage/xtrem-authorization/pages__group_role_site__users____columns__title___id": "Code", "@sage/xtrem-authorization/pages__group_role_site__users____columns__title__2": "Mis à jour par", "@sage/xtrem-authorization/pages__group_role_site__users____columns__title__displayName": "Nom d'utilisateur", "@sage/xtrem-authorization/pages__group_role_site__users____columns__title__isActive": "Actif", "@sage/xtrem-authorization/pages__group_role_site__users____title": "Utilisateurs", "@sage/xtrem-authorization/pages__group_role_site_list____title": "Liste des groupes d'autorisation", "@sage/xtrem-authorization/pages__group_role_site_list__addNewGroup____title": "Ajouter un groupe", "@sage/xtrem-authorization/pages__group_role_site_list__groups____columns__title": "C<PERSON><PERSON> par", "@sage/xtrem-authorization/pages__group_role_site_list__groups____columns__title___id": "Code", "@sage/xtrem-authorization/pages__group_role_site_list__groups____columns__title__2": "Mis à jour par", "@sage/xtrem-authorization/pages__group_role_site_list__groups____columns__title__id": "Code", "@sage/xtrem-authorization/pages__group_role_site_list__groups____columns__title__name": "Nom", "@sage/xtrem-authorization/pages__group_role_site_list__groups____dropdownActions__title": "Modifier", "@sage/xtrem-authorization/pages__group_role_site_list__groups____dropdownActions__title__2": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__group_role_site_list__groups____title": "Groupe d'autorisations", "@sage/xtrem-authorization/pages__group_role_site_list__groupsBlock____title": "Groupes d'utilisateurs", "@sage/xtrem-authorization/pages__group_role_site_new_group_title": "Nouveau groupe d'autorisations", "@sage/xtrem-authorization/pages__new_group__group_created": "Groupe d'autorisation {{newGroupId}} créé", "@sage/xtrem-authorization/pages__new_group_panel____subtitle": "Créer un groupe pour accorder les droits d'accès et d'activité aux utilisateurs.", "@sage/xtrem-authorization/pages__new_group_panel____title": "Nouveau groupe", "@sage/xtrem-authorization/pages__new_group_panel__allRoles____title": "To<PERSON> les rôles", "@sage/xtrem-authorization/pages__new_group_panel__allSiteAndSiteGroup____title": "Tous les sites / groupes de sites", "@sage/xtrem-authorization/pages__new_group_panel__cancelAction____title": "Annuler", "@sage/xtrem-authorization/pages__new_group_panel__groupInfoBlock____title": "Information groupe", "@sage/xtrem-authorization/pages__new_group_panel__id____title": "ID", "@sage/xtrem-authorization/pages__new_group_panel__name____title": "Nom", "@sage/xtrem-authorization/pages__new_group_panel__saveNewGroup____title": "Enregistrer", "@sage/xtrem-authorization/pages__new_group_panel__selectionSection____title": "Associer des rôles et des sites / groupes de sites", "@sage/xtrem-authorization/pages__new_group_panel__selectRole____columns__title___id": "ID", "@sage/xtrem-authorization/pages__new_group_panel__selectRole____columns__title__id": "ID", "@sage/xtrem-authorization/pages__new_group_panel__selectRole____columns__title__name": "Nom", "@sage/xtrem-authorization/pages__new_group_panel__selectSiteAndSiteGroup____columns__title___id": "ID", "@sage/xtrem-authorization/pages__new_group_panel__selectSiteAndSiteGroup____columns__title__id": "ID", "@sage/xtrem-authorization/pages__new_group_panel__selectSiteAndSiteGroup____columns__title__name": "Nom", "@sage/xtrem-authorization/pages__operator_user_panel____title": "Réinitialiser code opérateur", "@sage/xtrem-authorization/pages__operator_user_panel__cancel____title": "Annuler", "@sage/xtrem-authorization/pages__operator_user_panel__confirm____title": "Confirmer", "@sage/xtrem-authorization/pages__operator_user_panel__operatorCodeBlock____title": "Bloc code opérateur", "@sage/xtrem-authorization/pages__operator_user_panel__operatorCodeInput____title": "Nouveau code opérateur", "@sage/xtrem-authorization/pages__operator_user_panel__operatorCodeSection____title": "Section code opérateur", "@sage/xtrem-authorization/pages__role_detail____subtitle": "R<PERSON><PERSON>", "@sage/xtrem-authorization/pages__role_detail____title": "R<PERSON><PERSON>", "@sage/xtrem-authorization/pages__role_detail__activitySection____title": "Activités", "@sage/xtrem-authorization/pages__role_detail__cancel____title": "Annuler", "@sage/xtrem-authorization/pages__role_detail__confirm____title": "Enregistrer", "@sage/xtrem-authorization/pages__role_detail__not_updated": "Les permissions n'ont pas été mises à jour.", "@sage/xtrem-authorization/pages__role_detail__packageBlock1____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock10____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock11____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock12____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock13____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock14____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock15____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock16____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock17____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock18____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock19____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock2____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock20____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock3____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock4____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock5____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock6____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock7____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock8____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock9____title": "", "@sage/xtrem-authorization/pages__role_detail_updated": "Permissions mises à jour", "@sage/xtrem-authorization/pages__role_list____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__role_list__addNewRole____title": "Ajouter un rôle", "@sage/xtrem-authorization/pages__role_list__duplicate": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__role_list__field____columns__title___id": "ID", "@sage/xtrem-authorization/pages__role_list__field____columns__title__id": "ID", "@sage/xtrem-authorization/pages__role_list__field____columns__title__name": "Nom", "@sage/xtrem-authorization/pages__role_list__field____dropdownActions__title": "Modifier le rôle", "@sage/xtrem-authorization/pages__role_list__field____dropdownActions__title__2": "<PERSON><PERSON> le rôle", "@sage/xtrem-authorization/pages__role_list__field____dropdownActions__title__3": "Voir les groupes", "@sage/xtrem-authorization/pages__role_list__field____dropdownActions__title__4": "<PERSON><PERSON><PERSON><PERSON> le rôle", "@sage/xtrem-authorization/pages__role_list__fieldBlock____title": "Lignes du rôle", "@sage/xtrem-authorization/pages__role_list__id____title": "Code", "@sage/xtrem-authorization/pages__role_list__name____title": "Nom", "@sage/xtrem-authorization/pages__role_list__roles____columns__title___id": "Code", "@sage/xtrem-authorization/pages__role_list__roles____columns__title__createdByUserAndStamp": "C<PERSON><PERSON> par", "@sage/xtrem-authorization/pages__role_list__roles____columns__title__id": "Code", "@sage/xtrem-authorization/pages__role_list__roles____columns__title__isBillingRole": "Rôle de facturation", "@sage/xtrem-authorization/pages__role_list__roles____columns__title__name": "Nom", "@sage/xtrem-authorization/pages__role_list__roles____columns__title__updateByUserAndStamp": "Mis à jour par", "@sage/xtrem-authorization/pages__role_list__roles____dropdownActions__title": "Modifier", "@sage/xtrem-authorization/pages__role_list__roles____dropdownActions__title__2": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__role_list__roles____dropdownActions__title__3": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__role_list__rolesBlock____title": "Lignes du rôle", "@sage/xtrem-authorization/pages__role_list__roleSection____title": "R<PERSON><PERSON>", "@sage/xtrem-authorization/pages__role_setup": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__role_setup____title": "Nouveau rôle", "@sage/xtrem-authorization/pages__role_setup__cancel____title": "Annuler", "@sage/xtrem-authorization/pages__role_setup__confirm____title": "Enregistrer", "@sage/xtrem-authorization/pages__role_setup__duplicated_id": "Le code saisi existe déjà. Sélectionnez un autre code.", "@sage/xtrem-authorization/pages__role_setup__id____title": "Code", "@sage/xtrem-authorization/pages__role_setup__name____title": "Nom", "@sage/xtrem-authorization/pages__role_setup__next____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__role_setup__packageBlock1____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock10____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock11____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock12____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock13____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock14____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock15____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock16____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock17____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock18____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock19____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock2____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock20____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock3____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock4____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock5____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock6____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock7____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock8____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock9____title": "", "@sage/xtrem-authorization/pages__role_setup__packageSelect____title": "Packages", "@sage/xtrem-authorization/pages__role_setup__roleBlock____title": "Information du rôle", "@sage/xtrem-authorization/pages__role_setup_failed": "Le rôle n'a pas été créé.", "@sage/xtrem-authorization/pages__site_group____subtitle": "Groupe de sites", "@sage/xtrem-authorization/pages__site_group____title": "Groupe de sites", "@sage/xtrem-authorization/pages__site_group___id____title": "Code", "@sage/xtrem-authorization/pages__site_group__associatedAuthorizationGroupBlock____title": "Groupe d'autorisation", "@sage/xtrem-authorization/pages__site_group__associatedAuthorizationGroupSection____title": "Groupes d'utilisateurs", "@sage/xtrem-authorization/pages__site_group__chartBlock____title": "Organisation", "@sage/xtrem-authorization/pages__site_group__confirm____title": "Enregistrer", "@sage/xtrem-authorization/pages__site_group__customSave____title": "Enregistrer", "@sage/xtrem-authorization/pages__site_group__customSaveAction____title": "Enregistrer", "@sage/xtrem-authorization/pages__site_group__editSiteGroup____title": "Modifier le groupe de sites", "@sage/xtrem-authorization/pages__site_group__generalBlock____title": "Groupe de sites", "@sage/xtrem-authorization/pages__site_group__generalSection____title": "Général", "@sage/xtrem-authorization/pages__site_group__groups____columns__title": "C<PERSON><PERSON> par", "@sage/xtrem-authorization/pages__site_group__groups____columns__title___id": "Code", "@sage/xtrem-authorization/pages__site_group__groups____columns__title__2": "Mis à jour par", "@sage/xtrem-authorization/pages__site_group__groups____columns__title__id": "Code", "@sage/xtrem-authorization/pages__site_group__groups____columns__title__name": "Nom", "@sage/xtrem-authorization/pages__site_group__groups____title": "Groupe d'autorisations", "@sage/xtrem-authorization/pages__site_group__hierarchyChartContent____title": "Organisation", "@sage/xtrem-authorization/pages__site_group__id____title": "Code", "@sage/xtrem-authorization/pages__site_group__isActive____title": "Actif", "@sage/xtrem-authorization/pages__site_group__isLegalCompany____title": "Société", "@sage/xtrem-authorization/pages__site_group__name____title": "Nom", "@sage/xtrem-authorization/pages__site_group__siteGroupList____title": "Liste de groupes de sites", "@sage/xtrem-authorization/pages__site_group__siteGroups____columns__title__siteGroup___id": "Code", "@sage/xtrem-authorization/pages__site_group__siteGroups____columns__title__siteGroup__id": "Code", "@sage/xtrem-authorization/pages__site_group__siteGroups____columns__title__siteGroup__name": "Nom", "@sage/xtrem-authorization/pages__site_group__siteGroupsBlock____title": "Groupes de sites", "@sage/xtrem-authorization/pages__site_group__siteGroupsMultiReference____columns__title___id": "Code", "@sage/xtrem-authorization/pages__site_group__siteGroupsMultiReference____columns__title__id": "Code", "@sage/xtrem-authorization/pages__site_group__siteGroupsMultiReference____columns__title__name": "Nom", "@sage/xtrem-authorization/pages__site_group__siteGroupsMultiReference____lookupDialogTitle": "Sélectionner les groupes de sites", "@sage/xtrem-authorization/pages__site_group__sites____columns__title__site___id": "Code", "@sage/xtrem-authorization/pages__site_group__sites____columns__title__site__id": "Code", "@sage/xtrem-authorization/pages__site_group__sites____columns__title__site__name": "Nom", "@sage/xtrem-authorization/pages__site_group__sitesBlock____title": "Sites", "@sage/xtrem-authorization/pages__site_group__sitesMultiReference____columns__title___id": "Code", "@sage/xtrem-authorization/pages__site_group__sitesMultiReference____columns__title__id": "Code", "@sage/xtrem-authorization/pages__site_group__sitesMultiReference____columns__title__name": "Nom", "@sage/xtrem-authorization/pages__site_group__sitesMultiReference____lookupDialogTitle": "Sélectionner les sites", "@sage/xtrem-authorization/pages__site_group__toggleChart____helperText": "Autoriser les utilisateurs à choisir le mode tableau ou graphique", "@sage/xtrem-authorization/pages__site_group__toggleChart____title": "Options d'affichage", "@sage/xtrem-authorization/pages__site_group__toggleChartBlock____title": "Affichage graphique/tableau", "@sage/xtrem-authorization/pages__site_group_chart": "Graphique", "@sage/xtrem-authorization/pages__site_group_grid": "Table", "@sage/xtrem-authorization/pages__site_group_list____title": "Groupes de sites", "@sage/xtrem-authorization/pages__site_group_list__addNewSiteGroup____title": "Nouveau groupe de sites", "@sage/xtrem-authorization/pages__site_group_list__createSite____title": "Créer un site", "@sage/xtrem-authorization/pages__site_group_list__createSiteGroup____title": "Créer un groupe de sites", "@sage/xtrem-authorization/pages__site_group_list__fieldBlock____title": "Groupe de sites", "@sage/xtrem-authorization/pages__site_group_list__groupList____title": "Liste de groupes de sites", "@sage/xtrem-authorization/pages__site_group_list__section____title": "Groupes de sites", "@sage/xtrem-authorization/pages__site_group_list__siteGroups____levels__columns__title___id": "Code", "@sage/xtrem-authorization/pages__site_group_list__siteGroups____levels__columns__title__id": "Code", "@sage/xtrem-authorization/pages__site_group_list__siteGroups____levels__columns__title__id__2": "Code", "@sage/xtrem-authorization/pages__site_group_list__siteGroups____levels__columns__title__name": "Nom", "@sage/xtrem-authorization/pages__site_group_list__siteGroups____levels__columns__title__name__2": "Nom", "@sage/xtrem-authorization/pages__site_group_list__siteGroups____levels__dropdownActions__title": "Modifier", "@sage/xtrem-authorization/pages__site_group_list__siteGroups____levels__dropdownActions__title__2": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__site_group_new_title": "Nouveau groupe de sites", "@sage/xtrem-authorization/pages__support_access_history____title": "Accès support", "@sage/xtrem-authorization/pages__support_access_history__accessHistory____columns__title___id": "Code", "@sage/xtrem-authorization/pages__support_access_history__accessHistory____columns__title__endTime": "Heure de fin", "@sage/xtrem-authorization/pages__support_access_history__accessHistory____columns__title__isReadOnlyAccess": "Accès en lecture seule", "@sage/xtrem-authorization/pages__support_access_history__accessHistory____columns__title__startTime": "<PERSON><PERSON> d<PERSON>", "@sage/xtrem-authorization/pages__support_access_history__accessHistory____columns__title__status": "Statut", "@sage/xtrem-authorization/pages__support_access_history__accessHistory____title": "Historique d'accès support", "@sage/xtrem-authorization/pages__support_access_history__allow_access_button_text": "Accorder l'accès", "@sage/xtrem-authorization/pages__support_access_history__description____content": "Spécifier la durée de l'accès utilisateur.", "@sage/xtrem-authorization/pages__support_access_history__dummy____content": "", "@sage/xtrem-authorization/pages__support_access_history__extend_access_button_text": "É<PERSON>re l'accès", "@sage/xtrem-authorization/pages__support_access_history__forTime____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__support_access_history__infoSection____title": "Général", "@sage/xtrem-authorization/pages__support_access_history__isReadOnlyAccess____title": "Accès en lecture seule", "@sage/xtrem-authorization/pages__support_access_history__isSupportAccessOpen____title": "Accès en lecture seule", "@sage/xtrem-authorization/pages__support_access_history__please_fill_the_number_and_unit_field": "Renseignez un numéro et une unité.", "@sage/xtrem-authorization/pages__support_access_history__revoke_access_button_text": "Révoquer l'accès", "@sage/xtrem-authorization/pages__support_access_history__supportAccessBlock____title": "Autoriser l'accès support Sage", "@sage/xtrem-authorization/pages__support_access_history__supportAccessHistoryBlock____title": "Historique d'accès support", "@sage/xtrem-authorization/pages__support_access_history__unit____title": "Unités", "@sage/xtrem-authorization/pages__user____navigationPanel__bulkActions__title": "Envoyer e-mail de bienvenue", "@sage/xtrem-authorization/pages__user____navigationPanel__listItem__image__title": "Image", "@sage/xtrem-authorization/pages__user____navigationPanel__listItem__line2__title": "E-mail", "@sage/xtrem-authorization/pages__user____navigationPanel__listItem__line3__title": "Utilisateur API", "@sage/xtrem-authorization/pages__user____navigationPanel__listItem__title__title": "Prénom", "@sage/xtrem-authorization/pages__user____navigationPanel__listItem__titleRight__title": "Nom", "@sage/xtrem-authorization/pages__user____navigationPanel__optionsMenu__title": "Tous", "@sage/xtrem-authorization/pages__user____navigationPanel__optionsMenu__title__2": "Interactifs", "@sage/xtrem-authorization/pages__user____navigationPanel__optionsMenu__title__3": "Applications tierces", "@sage/xtrem-authorization/pages__user____objectTypePlural": "Utilisateurs", "@sage/xtrem-authorization/pages__user____objectTypeSingular": "Utilisa<PERSON>ur", "@sage/xtrem-authorization/pages__user____subtitle": "Utilisa<PERSON>ur", "@sage/xtrem-authorization/pages__user____title": "Utilisateurs", "@sage/xtrem-authorization/pages__user___id____title": "Code", "@sage/xtrem-authorization/pages__user__associatedRoles____columns__title___id": "Code", "@sage/xtrem-authorization/pages__user__associatedRoles____columns__title__createdBy": "C<PERSON><PERSON> par", "@sage/xtrem-authorization/pages__user__associatedRoles____columns__title__createdByUserAndStamp": "C<PERSON><PERSON> par", "@sage/xtrem-authorization/pages__user__associatedRoles____columns__title__id": "Code", "@sage/xtrem-authorization/pages__user__associatedRoles____columns__title__name": "Nom", "@sage/xtrem-authorization/pages__user__associatedRoles____columns__title__updateByUserAndStamp": "Modifié par", "@sage/xtrem-authorization/pages__user__associatedRoles____columns__title__updatedBy": "Mis à jour par", "@sage/xtrem-authorization/pages__user__associatedRoles____title": "Rôles et activités", "@sage/xtrem-authorization/pages__user__associatedSiteGroups____columns__title___id": "Code", "@sage/xtrem-authorization/pages__user__associatedSiteGroups____columns__title__createdBy": "C<PERSON><PERSON> par", "@sage/xtrem-authorization/pages__user__associatedSiteGroups____columns__title__createdByUserAndStamp": "C<PERSON><PERSON> par", "@sage/xtrem-authorization/pages__user__associatedSiteGroups____columns__title__id": "Code", "@sage/xtrem-authorization/pages__user__associatedSiteGroups____columns__title__name": "Nom", "@sage/xtrem-authorization/pages__user__associatedSiteGroups____columns__title__updateByUserAndStamp": "Modifié par", "@sage/xtrem-authorization/pages__user__associatedSiteGroups____columns__title__updatedBy": "Mis à jour par", "@sage/xtrem-authorization/pages__user__associatedSiteGroups____title": "Groupes de sites", "@sage/xtrem-authorization/pages__user__associatedSiteGroupsSection____title": "Groupes de sites", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__columns__group__id__title": "Code", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__columns__group__id__title__2": "Nom", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__columns__group__id__title__3": "Code", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__title__group___id": "Code", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__title__group__groupRolesDisplay": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__title__group__groupSitesDisplay": "Groupe de sites", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__title__group__id": "Code", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__title__group__name": "Nom", "@sage/xtrem-authorization/pages__user__authorizationGroup____title": "Groupes d'autorisations", "@sage/xtrem-authorization/pages__user__editUser____title": "Modifier l'utilisateur", "@sage/xtrem-authorization/pages__user__email____title": "E-mail", "@sage/xtrem-authorization/pages__user__firstName____title": "Prénom", "@sage/xtrem-authorization/pages__user__generalSection____title": "Général", "@sage/xtrem-authorization/pages__user__idBlock____title": "Code", "@sage/xtrem-authorization/pages__user__importExportDateFormat____helperText": "La valeur par défaut sera ", "@sage/xtrem-authorization/pages__user__importExportDateFormat____title": "Format de date d'import/export", "@sage/xtrem-authorization/pages__user__importExportDelimiter____helperText": "La valeur par défaut sera ", "@sage/xtrem-authorization/pages__user__importExportDelimiter____title": "Séparateur import/export", "@sage/xtrem-authorization/pages__user__isActive____title": "Actif", "@sage/xtrem-authorization/pages__user__isAdministrator____title": "Administrateur", "@sage/xtrem-authorization/pages__user__isApiUser____title": "Utilisateur API", "@sage/xtrem-authorization/pages__user__isDemoPersona____title": "Persona de démo", "@sage/xtrem-authorization/pages__user__isExternal____title": "Externe", "@sage/xtrem-authorization/pages__user__isOperatorUser____title": "Authentification PIN", "@sage/xtrem-authorization/pages__user__isWelcomeMailSent____title": "Envoyer un e-mail de bienvenue", "@sage/xtrem-authorization/pages__user__lastName____title": "Nom", "@sage/xtrem-authorization/pages__user__operatorCode____title": "Code PIN", "@sage/xtrem-authorization/pages__user__preferencesBlock____title": "Préférences", "@sage/xtrem-authorization/pages__user__preferencesSection____title": "Préférences", "@sage/xtrem-authorization/pages__user__resetOperatorCodePage____title": "Code PIN", "@sage/xtrem-authorization/pages__user__role____columns__title__id": "Code", "@sage/xtrem-authorization/pages__user__role____columns__title__name": "Nom", "@sage/xtrem-authorization/pages__user__role____lookupDialogTitle": "Sélectionner un rôle de facturation", "@sage/xtrem-authorization/pages__user__role____title": "Rôle de facturation", "@sage/xtrem-authorization/pages__user__rolesActivitiesSection____title": "Rôles et activités", "@sage/xtrem-authorization/pages__user__save____title": "Enregistrer", "@sage/xtrem-authorization/pages__user__send_welcome_mail": "Envoyer un e-mail de bienvenue", "@sage/xtrem-authorization/pages__user__send_welcome_mail_button": "Envoyer", "@sage/xtrem-authorization/pages__user__send_welcome_mail_dialog_content": "Vous allez envoyer un e-mail de bienvenue à l'utilisateur.", "@sage/xtrem-authorization/pages__user__userAuthorizationInformationBlock____title": "Groupe d'autorisations", "@sage/xtrem-authorization/pages__user__userGroups____columns__title___id": "Code", "@sage/xtrem-authorization/pages__user__userGroups____columns__title__id": "Code", "@sage/xtrem-authorization/pages__user__userGroups____columns__title__name": "Nom", "@sage/xtrem-authorization/pages__user__userGroups____lookupDialogTitle": "Sélectionner le groupe d'autorisations", "@sage/xtrem-authorization/pages__user__userGroups____title": "Groupe d'autorisations", "@sage/xtrem-authorization/pages__user__userInformationBlock____title": "Informations utilisateurs", "@sage/xtrem-authorization/pages__user__userList____title": "Afficher les utilisateurs", "@sage/xtrem-authorization/pages__user__userPhotoBlock____title": "Photo", "@sage/xtrem-authorization/pages__user_group_list____title": "Groupes utilisateurs", "@sage/xtrem-authorization/pages__user_group_list__groupList____title": "Liste des groupes", "@sage/xtrem-authorization/pages__user_group_list__title": "Groupes utilisateurs", "@sage/xtrem-authorization/pages__user_group_list__users____columns__title": "C<PERSON><PERSON> par", "@sage/xtrem-authorization/pages__user_group_list__users____columns__title___id": "ID", "@sage/xtrem-authorization/pages__user_group_list__users____columns__title__2": "Mis à jour par", "@sage/xtrem-authorization/pages__user_group_list__users____columns__title__displayName": "Nom utilisateur", "@sage/xtrem-authorization/pages__user_group_list__users____columns__title__isActive": "Actif", "@sage/xtrem-authorization/pages__user_group_list__usersBlock____title": "Groupes utilisateurs", "@sage/xtrem-authorization/pages_role_list_delete_confirmation": "Confirmer la <PERSON>", "@sage/xtrem-authorization/pages_role_list_delete_message": "Vous allez supprimer le rôle {{role}}.", "@sage/xtrem-authorization/pages-confirm-cancel": "Annuler", "@sage/xtrem-authorization/permission__all__name": "Tous", "@sage/xtrem-authorization/permission__allow_access__name": "Accorder l'accès", "@sage/xtrem-authorization/permission__create__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/permission__delete__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/permission__extend_access__name": "É<PERSON>re l'accès", "@sage/xtrem-authorization/permission__is_support_access_open__name": "Ouverture de l'accès au support", "@sage/xtrem-authorization/permission__lookup__name": "Recherche", "@sage/xtrem-authorization/permission__manage__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/permission__read__name": "Lecture", "@sage/xtrem-authorization/permission__revoke_access__name": "Révoquer l'accès", "@sage/xtrem-authorization/permission__update__name": "Mettre à jour", "@sage/xtrem-authorization/reset-operator-code": "Réinitialiser", "@sage/xtrem-authorization/role_detail_page_is_not_read_only": "Annuler", "@sage/xtrem-authorization/role_detail_page_is_read_only": "OK", "@sage/xtrem-authorization/service_options__authorization_service_option__name": "Option de service d'autorisation", "@sage/xtrem-authorization/stickers__demo_persona_sticker____title": "Persona de démo", "@sage/xtrem-authorization/stickers__demo_persona_sticker__section____title": "<PERSON>a", "@sage/xtrem-authorization/stickers__demo_persona_sticker__selectionPersona____title": "Persona de démo"}