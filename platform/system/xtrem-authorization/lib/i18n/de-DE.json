{"@sage/xtrem-authorization/activity__group_role_site__name": "Gruppenrollenstandort", "@sage/xtrem-authorization/activity__role__name": "<PERSON><PERSON>", "@sage/xtrem-authorization/activity__site_group__name": "Standortgruppe", "@sage/xtrem-authorization/activity__support_access_history__name": "Verlauf Support-Zugriff", "@sage/xtrem-authorization/activity__user__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/cannot-delete-site-from-site-group": "Der Standort kann nicht aus den verknüpften Standortgruppen gelöscht werden.", "@sage/xtrem-authorization/cannot-delete-site-group": "Eine verknüpfte Standortgruppe kann nicht gelöscht werden.", "@sage/xtrem-authorization/data_types__name_array_data_type__name": "Datentyp Name-Array", "@sage/xtrem-authorization/data_types__string_data_type__name": "Datentyp Zei<PERSON>kette", "@sage/xtrem-authorization/data_types__support_access_history_status_enum__name": "Enum Status Verlauf <PERSON>", "@sage/xtrem-authorization/data_types__support_access_unit_enum__name": "Enum Einheit Supportzugriff", "@sage/xtrem-authorization/data_types__user_type_enum__name": "<PERSON><PERSON>", "@sage/xtrem-authorization/delete-confirmation": "Datensatz <PERSON>", "@sage/xtrem-authorization/delete-dialog-content": "<PERSON>e sind dabei, diese Gruppe zu löschen.", "@sage/xtrem-authorization/delete-group": "Löschen bestätigen", "@sage/xtrem-authorization/duplication-confirmation": "Datensatz dupliziert", "@sage/xtrem-authorization/enums__support_access_history_status__closed": "Geschlossen", "@sage/xtrem-authorization/enums__support_access_history_status__open": "<PERSON>en", "@sage/xtrem-authorization/enums__support_access_unit__days": "Tage", "@sage/xtrem-authorization/enums__support_access_unit__hours": "Stunden", "@sage/xtrem-authorization/enums__support_access_unit__minutes": "Minuten", "@sage/xtrem-authorization/enums__user_type__application": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/enums__user_type__system": "System", "@sage/xtrem-authorization/menu_item__support": "Support", "@sage/xtrem-authorization/menu_item__user-data": "Benutzer und Sicherheit", "@sage/xtrem-authorization/node-extensions__site_extension__property__groupRoleSites": "Gruppenrollenstandorte", "@sage/xtrem-authorization/node-extensions__site_extension__property__siteGroups": "Standortgruppen", "@sage/xtrem-authorization/node-extensions__user_extension__bulkMutation__sendBulkWelcomeMail": "Massen-Willkommens-E-Mail senden", "@sage/xtrem-authorization/node-extensions__user_extension__bulkMutation__sendBulkWelcomeMail__failed": "Massen-Willkommens-E-Mail senden fehlgeschlagen.", "@sage/xtrem-authorization/node-extensions__user_extension__property__authorizationGroup": "Autorisierungsgruppe", "@sage/xtrem-authorization/node-extensions__user_extension__property__billingRole": "Fakturierungsrolle", "@sage/xtrem-authorization/node-extensions__user_extension__property__groupDisplay": "Anzeige Gruppe", "@sage/xtrem-authorization/node-extensions__user_extension__property__objectGrants": "Berechtigungen Objekt", "@sage/xtrem-authorization/node-extensions__user_extension__query__all": "Alle", "@sage/xtrem-authorization/node-extensions__user_extension__query__all__failed": "Alle fehlgeschlagen.", "@sage/xtrem-authorization/node-extensions__user_extension__query__all__parameter__isActive": "Aktiv", "@sage/xtrem-authorization/nodes__activity__asyncMutation__asyncExport": "Export", "@sage/xtrem-authorization/nodes__activity__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-authorization/nodes__activity__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__activity__node_name": "Aktivität", "@sage/xtrem-authorization/nodes__activity__property__description": "Bezeichnung", "@sage/xtrem-authorization/nodes__activity__property__name": "Name", "@sage/xtrem-authorization/nodes__activity__property__package": "<PERSON><PERSON>", "@sage/xtrem-authorization/nodes__activity__property__permissions": "Berechtigungen", "@sage/xtrem-authorization/nodes__group_role__asyncMutation__asyncExport": "Export", "@sage/xtrem-authorization/nodes__group_role__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-authorization/nodes__group_role__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__group_role__node_name": "Gruppenrolle", "@sage/xtrem-authorization/nodes__group_role__property__groupRoleSite": "Standortgruppenrolle", "@sage/xtrem-authorization/nodes__group_role__property__role": "<PERSON><PERSON>", "@sage/xtrem-authorization/nodes__group_role_site__asyncMutation__asyncExport": "Export", "@sage/xtrem-authorization/nodes__group_role_site__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-authorization/nodes__group_role_site__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__group_role_site__node_name": "Standortgruppenrolle", "@sage/xtrem-authorization/nodes__group_role_site__property__createdBy": "<PERSON><PERSON><PERSON> von", "@sage/xtrem-authorization/nodes__group_role_site__property__createStamp": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/nodes__group_role_site__property__groupRoles": "Gruppenrollen", "@sage/xtrem-authorization/nodes__group_role_site__property__groupRolesDisplay": "Anzeige Gruppenrollen", "@sage/xtrem-authorization/nodes__group_role_site__property__groupSites": "Standortgruppen", "@sage/xtrem-authorization/nodes__group_role_site__property__groupSitesDisplay": "Anzeige Standortgruppe", "@sage/xtrem-authorization/nodes__group_role_site__property__id": "ID", "@sage/xtrem-authorization/nodes__group_role_site__property__name": "Name", "@sage/xtrem-authorization/nodes__group_role_site__property__updatedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON> von", "@sage/xtrem-authorization/nodes__group_role_site__property__updateStamp": "Stempel Aktualisieren", "@sage/xtrem-authorization/nodes__group_site__asyncMutation__asyncExport": "Export", "@sage/xtrem-authorization/nodes__group_site__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-authorization/nodes__group_site__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__group_site__node_name": "Standortgruppe", "@sage/xtrem-authorization/nodes__group_site__property__groupRoleSite": "Standortgruppenrolle", "@sage/xtrem-authorization/nodes__group_site__property__siteGroup": "Standortgruppe", "@sage/xtrem-authorization/nodes__restricted_node__node_name": "Eingeschränkter Node", "@sage/xtrem-authorization/nodes__restricted_node__property__userGrants": "Benutzerberechtigungen", "@sage/xtrem-authorization/nodes__restricted_node_user_grant__asyncMutation__asyncExport": "Export", "@sage/xtrem-authorization/nodes__restricted_node_user_grant__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-authorization/nodes__restricted_node_user_grant__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__restricted_node_user_grant__node_name": "Eingeschränkter Node basierend auf Benutzerberechtigungen", "@sage/xtrem-authorization/nodes__restricted_node_user_grant__property__accessMap": "Zugriffs-Map", "@sage/xtrem-authorization/nodes__restricted_node_user_grant__property__object": "Objekt", "@sage/xtrem-authorization/nodes__restricted_node_user_grant__property__user": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/nodes__role__asyncMutation__asyncExport": "Export", "@sage/xtrem-authorization/nodes__role__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-authorization/nodes__role__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__role__node_name": "<PERSON><PERSON>", "@sage/xtrem-authorization/nodes__role__property__activities": "Aktivitäten", "@sage/xtrem-authorization/nodes__role__property__createdBy": "<PERSON><PERSON><PERSON> von", "@sage/xtrem-authorization/nodes__role__property__description": "Bezeichnung", "@sage/xtrem-authorization/nodes__role__property__id": "ID", "@sage/xtrem-authorization/nodes__role__property__isActive": "Aktiv", "@sage/xtrem-authorization/nodes__role__property__isBillingRole": "Fakturierungsrolle", "@sage/xtrem-authorization/nodes__role__property__name": "Name", "@sage/xtrem-authorization/nodes__role__property__roles": "<PERSON><PERSON>", "@sage/xtrem-authorization/nodes__role__property__setupId": "ID Einstellungen", "@sage/xtrem-authorization/nodes__role__property__updatedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON> von", "@sage/xtrem-authorization/nodes__role__query__all": "Alle", "@sage/xtrem-authorization/nodes__role__query__all__failed": "Alle fehlgeschlagen.", "@sage/xtrem-authorization/nodes__role__query__all__parameter__isActive": "Aktiv", "@sage/xtrem-authorization/nodes__role_activity__asyncMutation__asyncExport": "Export", "@sage/xtrem-authorization/nodes__role_activity__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-authorization/nodes__role_activity__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__role_activity__mutation__roleActivitiesCreateUpdate": "Rollenaktivitäten erstellen/aktualisieren", "@sage/xtrem-authorization/nodes__role_activity__mutation__roleActivitiesCreateUpdate__failed": "Rollenaktivitäten erstellen/aktualisieren fehlgeschlagen.", "@sage/xtrem-authorization/nodes__role_activity__mutation__roleActivitiesCreateUpdate__parameter__roleActivities": "Rollenaktivitäten", "@sage/xtrem-authorization/nodes__role_activity__mutation__roleActivitiesCreateUpdate__parameter__roleId": "ID Rolle", "@sage/xtrem-authorization/nodes__role_activity__mutation__roleActivitiesCreateUpdate__parameter__roleName": "Rollenname", "@sage/xtrem-authorization/nodes__role_activity__mutation__roleActivitiesCreateUpdate__parameter__roleSysId": "System-ID Rolle", "@sage/xtrem-authorization/nodes__role_activity__node_name": "Rollenaktivität", "@sage/xtrem-authorization/nodes__role_activity__property__activity": "Aktivität", "@sage/xtrem-authorization/nodes__role_activity__property__getPermissions": "Berechtigungen erhalten", "@sage/xtrem-authorization/nodes__role_activity__property__hasAllPermissions": "<PERSON>t allen Berechtigungen", "@sage/xtrem-authorization/nodes__role_activity__property__isActive": "Aktiv", "@sage/xtrem-authorization/nodes__role_activity__property__permissions": "Berechtigungen", "@sage/xtrem-authorization/nodes__role_activity__property__role": "<PERSON><PERSON>", "@sage/xtrem-authorization/nodes__role_to_role__asyncMutation__asyncExport": "Export", "@sage/xtrem-authorization/nodes__role_to_role__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-authorization/nodes__role_to_role__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__role_to_role__node_name": "<PERSON><PERSON> zu Rolle", "@sage/xtrem-authorization/nodes__role_to_role__property__role": "<PERSON><PERSON>", "@sage/xtrem-authorization/nodes__role_to_role__property__roleOrigin": "Rollenursprung", "@sage/xtrem-authorization/nodes__site_group__asyncMutation__asyncExport": "Export", "@sage/xtrem-authorization/nodes__site_group__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-authorization/nodes__site_group__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__site_group__node_name": "Standortgruppe", "@sage/xtrem-authorization/nodes__site_group__property__createdBy": "<PERSON><PERSON><PERSON> von", "@sage/xtrem-authorization/nodes__site_group__property__hierarchyChartContent": "Inhalt Hierarchiediagramm", "@sage/xtrem-authorization/nodes__site_group__property__id": "ID", "@sage/xtrem-authorization/nodes__site_group__property__isActive": "Aktiv", "@sage/xtrem-authorization/nodes__site_group__property__isLegalCompany": "Unternehmen", "@sage/xtrem-authorization/nodes__site_group__property__name": "Name", "@sage/xtrem-authorization/nodes__site_group__property__siteGroups": "Standortgruppen", "@sage/xtrem-authorization/nodes__site_group__property__sites": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/nodes__site_group__property__updatedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON> von", "@sage/xtrem-authorization/nodes__site_group_to_site__asyncMutation__asyncExport": "Export", "@sage/xtrem-authorization/nodes__site_group_to_site__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-authorization/nodes__site_group_to_site__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__site_group_to_site__node_name": "Standortgruppe zu Standort", "@sage/xtrem-authorization/nodes__site_group_to_site__property__dateAdd": "<PERSON><PERSON>", "@sage/xtrem-authorization/nodes__site_group_to_site__property__isValid": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/nodes__site_group_to_site__property__site": "<PERSON><PERSON>", "@sage/xtrem-authorization/nodes__site_group_to_site__property__siteGroup": "Standortgruppe", "@sage/xtrem-authorization/nodes__site_group_to_site_group__asyncMutation__asyncExport": "Export", "@sage/xtrem-authorization/nodes__site_group_to_site_group__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-authorization/nodes__site_group_to_site_group__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__site_group_to_site_group__node_name": "Standortgruppe zu Standortgruppe", "@sage/xtrem-authorization/nodes__site_group_to_site_group__property__siteGroup": "Standortgruppe", "@sage/xtrem-authorization/nodes__site_group_to_site_group__property__siteGroupOrigin": "Ursprung Standortgruppe", "@sage/xtrem-authorization/nodes__support_access__cannot_allow_support_access_because_there_is_a_open_session": "Es gibt bereits eine offene Sitzung. Kontaktieren Sie Ihren Systemadministrator.", "@sage/xtrem-authorization/nodes__support_access__end_time_must_be_greater_than_start_time": "Erfassen Sie eine Endzeit, die nach der Startzeit liegt.", "@sage/xtrem-authorization/nodes__support_access__number_must_be_greater_than_0": "Erfassen Sie eine Zahl größer als 0.", "@sage/xtrem-authorization/nodes__support_access__there_is_no_open_session_to_extend_the_access": "Es gibt keine offene Sitzung, für die der Zugriff verlängert werden kann. Kontaktieren Sie Ihren Systemadministrator.", "@sage/xtrem-authorization/nodes__support_access__there_is_no_open_session_to_revoke_the_access": "<PERSON>s gibt keine offene Sitzung, für die der Zugriff widerrufen werden kann. Kontaktieren Sie Ihren Systemadministrator.", "@sage/xtrem-authorization/nodes__support_access_history__asyncMutation__asyncExport": "Export", "@sage/xtrem-authorization/nodes__support_access_history__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-authorization/nodes__support_access_history__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__support_access_history__mutation__allowAccess": "Zugriff gewähren", "@sage/xtrem-authorization/nodes__support_access_history__mutation__allowAccess__failed": "Zugriff gewähren fehlgeschlagen.", "@sage/xtrem-authorization/nodes__support_access_history__mutation__allowAccess__parameter__forTime": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/nodes__support_access_history__mutation__allowAccess__parameter__isReadOnlyAccess": "Lesezugriff", "@sage/xtrem-authorization/nodes__support_access_history__mutation__allowAccess__parameter__units": "Einheiten", "@sage/xtrem-authorization/nodes__support_access_history__mutation__extendAccess": "Zugriff verlängern", "@sage/xtrem-authorization/nodes__support_access_history__mutation__extendAccess__failed": "Zugriff verlängern fehlgeschlagen.", "@sage/xtrem-authorization/nodes__support_access_history__mutation__extendAccess__parameter__forTime": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/nodes__support_access_history__mutation__extendAccess__parameter__units": "Einheiten", "@sage/xtrem-authorization/nodes__support_access_history__mutation__revokeAccess": "Zugriff widerrufen", "@sage/xtrem-authorization/nodes__support_access_history__mutation__revokeAccess__failed": "Zugriff widerrufen fehlgeschlagen.", "@sage/xtrem-authorization/nodes__support_access_history__node_name": "Verlauf Support-Zugriff", "@sage/xtrem-authorization/nodes__support_access_history__property__endTime": "Endzeit", "@sage/xtrem-authorization/nodes__support_access_history__property__isReadOnlyAccess": "Lesezugriff", "@sage/xtrem-authorization/nodes__support_access_history__property__startTime": "Startzeit", "@sage/xtrem-authorization/nodes__support_access_history__property__status": "Status", "@sage/xtrem-authorization/nodes__support_access_history__property__timeToClose": "Verbleibende Zeit", "@sage/xtrem-authorization/nodes__support_access_history__query__checkSupportAccessOpen": "Prüfung Support-Zugriff offen", "@sage/xtrem-authorization/nodes__support_access_history__query__checkSupportAccessOpen__failed": "Prüfung Support-Zugriff offen fehlgeschlagen.", "@sage/xtrem-authorization/nodes__user__cannot_change_own_rights": "<PERSON>e sind nicht dazu berechtigt, Ihre eigene Autorisierungsgruppe zu ändern.", "@sage/xtrem-authorization/nodes__user_billing_role__asyncMutation__asyncExport": "Export", "@sage/xtrem-authorization/nodes__user_billing_role__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-authorization/nodes__user_billing_role__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__user_billing_role__node_name": "Benutzerrolle Fakturierung", "@sage/xtrem-authorization/nodes__user_billing_role__property__role": "<PERSON><PERSON>", "@sage/xtrem-authorization/nodes__user_billing_role__property__user": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/nodes__user_group__asyncMutation__asyncExport": "Export", "@sage/xtrem-authorization/nodes__user_group__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-authorization/nodes__user_group__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__user_group__node_name": "Benutzergruppe", "@sage/xtrem-authorization/nodes__user_group__property__createdBy": "<PERSON><PERSON><PERSON> von", "@sage/xtrem-authorization/nodes__user_group__property__createStamp": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/nodes__user_group__property__group": "Gruppieren", "@sage/xtrem-authorization/nodes__user_group__property__isActive": "Aktiv", "@sage/xtrem-authorization/nodes__user_group__property__updatedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON> von", "@sage/xtrem-authorization/nodes__user_group__property__updateStamp": "Stempel Aktualisieren", "@sage/xtrem-authorization/nodes__user_group__property__user": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/operation-not-allowed-on-object": "Der Vorgang in diesem Objekt ist nicht zulässig.", "@sage/xtrem-authorization/package__name": "Autorisierung", "@sage/xtrem-authorization/pages__group_list____title": "Gruppen", "@sage/xtrem-authorization/pages__group_list__addNewGroup____title": "Neue Gruppe", "@sage/xtrem-authorization/pages__group_list__groups____columns__title": "<PERSON><PERSON><PERSON> von", "@sage/xtrem-authorization/pages__group_list__groups____columns__title___id": "ID", "@sage/xtrem-authorization/pages__group_list__groups____columns__title__2": "<PERSON><PERSON><PERSON><PERSON><PERSON> von", "@sage/xtrem-authorization/pages__group_list__groups____columns__title__id": "ID", "@sage/xtrem-authorization/pages__group_list__groups____columns__title__name": "Name", "@sage/xtrem-authorization/pages__group_list__groups____dropdownActions__title": "Rolle löschen", "@sage/xtrem-authorization/pages__group_list__groupsBlock____title": "Benutzergruppen", "@sage/xtrem-authorization/pages__group_role_site____objectTypePlural": "Autorisierungsgruppen", "@sage/xtrem-authorization/pages__group_role_site____objectTypeSingular": "Autorisierungsgruppe", "@sage/xtrem-authorization/pages__group_role_site____subtitle": "<PERSON><PERSON><PERSON>n Sie eine Gruppe, um den Benutzern Zugriffsrechte auf Aktivitäten und Daten zu gewähren.", "@sage/xtrem-authorization/pages__group_role_site____title": "Autorisierungsgruppe", "@sage/xtrem-authorization/pages__group_role_site___id____title": "ID", "@sage/xtrem-authorization/pages__group_role_site__allRoles____title": "Alle Rollen auswählen", "@sage/xtrem-authorization/pages__group_role_site__allSiteGroup____title": "Alle Standortgruppen auswählen", "@sage/xtrem-authorization/pages__group_role_site__associatedUserSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__group_role_site__confirm____title": "Speichern", "@sage/xtrem-authorization/pages__group_role_site__customSave____title": "Speichern", "@sage/xtrem-authorization/pages__group_role_site__customSaveAction____title": "Speichern", "@sage/xtrem-authorization/pages__group_role_site__editGroupRoleSite____title": "Autorisierungsgruppe bearbeiten", "@sage/xtrem-authorization/pages__group_role_site__groupInfoBlock____title": "Gruppeninformationen", "@sage/xtrem-authorization/pages__group_role_site__groupList____title": "Autorisierungsgruppen anzeigen", "@sage/xtrem-authorization/pages__group_role_site__groupRoleBlock____title": "Benutzerrollen", "@sage/xtrem-authorization/pages__group_role_site__groupRoles____columns__title__role___id": "ID", "@sage/xtrem-authorization/pages__group_role_site__groupRoles____columns__title__role__description": "Bezeichnung", "@sage/xtrem-authorization/pages__group_role_site__groupRoles____columns__title__role__id": "ID", "@sage/xtrem-authorization/pages__group_role_site__groupRoles____columns__title__role__name": "Name", "@sage/xtrem-authorization/pages__group_role_site__groupRoles____title": "<PERSON><PERSON>", "@sage/xtrem-authorization/pages__group_role_site__groupRolesActivities____columns__title__role": "Name", "@sage/xtrem-authorization/pages__group_role_site__groupRolesActivities____columns__title__role___id": "ID", "@sage/xtrem-authorization/pages__group_role_site__groupRolesActivities____columns__title__role__description": "Bezeichnung", "@sage/xtrem-authorization/pages__group_role_site__groupRolesActivities____columns__title__role__id": "ID", "@sage/xtrem-authorization/pages__group_role_site__groupRolesActivities____dropdownActions__title": "Aktivitäten", "@sage/xtrem-authorization/pages__group_role_site__groupRolesActivities____title": "Rollen und Aktivitäten", "@sage/xtrem-authorization/pages__group_role_site__groupRolesMultiReference____columns__title___id": "ID", "@sage/xtrem-authorization/pages__group_role_site__groupRolesMultiReference____columns__title__id": "ID", "@sage/xtrem-authorization/pages__group_role_site__groupRolesMultiReference____columns__title__name": "Name", "@sage/xtrem-authorization/pages__group_role_site__groupRolesMultiReference____lookupDialogTitle": "Benutzerrollen auswählen", "@sage/xtrem-authorization/pages__group_role_site__groupSiteBlock____title": "Standortgruppen", "@sage/xtrem-authorization/pages__group_role_site__groupSites____columns__title__siteGroup___id": "ID", "@sage/xtrem-authorization/pages__group_role_site__groupSites____columns__title__siteGroup__id": "ID", "@sage/xtrem-authorization/pages__group_role_site__groupSites____columns__title__siteGroup__name": "Name", "@sage/xtrem-authorization/pages__group_role_site__groupSites____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__group_role_site__groupSitesMultiReference____columns__title___id": "ID", "@sage/xtrem-authorization/pages__group_role_site__groupSitesMultiReference____columns__title__id": "ID", "@sage/xtrem-authorization/pages__group_role_site__groupSitesMultiReference____columns__title__name": "Name", "@sage/xtrem-authorization/pages__group_role_site__groupSitesMultiReference____lookupDialogTitle": "Standortgruppen auswählen", "@sage/xtrem-authorization/pages__group_role_site__id____title": "ID", "@sage/xtrem-authorization/pages__group_role_site__infoSection____title": "Allgemein", "@sage/xtrem-authorization/pages__group_role_site__name____title": "Name", "@sage/xtrem-authorization/pages__group_role_site__rolesActivitiesSection____title": "Rollen und Aktivitäten", "@sage/xtrem-authorization/pages__group_role_site__users____columns__title": "<PERSON><PERSON><PERSON> von", "@sage/xtrem-authorization/pages__group_role_site__users____columns__title___id": "ID", "@sage/xtrem-authorization/pages__group_role_site__users____columns__title__2": "<PERSON><PERSON><PERSON><PERSON><PERSON> von", "@sage/xtrem-authorization/pages__group_role_site__users____columns__title__displayName": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__group_role_site__users____columns__title__isActive": "Aktiv", "@sage/xtrem-authorization/pages__group_role_site__users____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__group_role_site_list____title": "Autorisierungsgruppenliste", "@sage/xtrem-authorization/pages__group_role_site_list__addNewGroup____title": "Gruppe hinzufügen", "@sage/xtrem-authorization/pages__group_role_site_list__groups____columns__title": "<PERSON><PERSON><PERSON> von", "@sage/xtrem-authorization/pages__group_role_site_list__groups____columns__title___id": "ID", "@sage/xtrem-authorization/pages__group_role_site_list__groups____columns__title__2": "<PERSON><PERSON><PERSON><PERSON><PERSON> von", "@sage/xtrem-authorization/pages__group_role_site_list__groups____columns__title__id": "ID", "@sage/xtrem-authorization/pages__group_role_site_list__groups____columns__title__name": "Name", "@sage/xtrem-authorization/pages__group_role_site_list__groups____dropdownActions__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__group_role_site_list__groups____dropdownActions__title__2": "Löschen", "@sage/xtrem-authorization/pages__group_role_site_list__groups____title": "Autorisierungsgruppe", "@sage/xtrem-authorization/pages__group_role_site_list__groupsBlock____title": "Benutzergruppen", "@sage/xtrem-authorization/pages__group_role_site_new_group_title": "Neue Autorisierungsgruppe", "@sage/xtrem-authorization/pages__new_group__group_created": "Autorisierungsgruppe {{newGroupId}} erstellt", "@sage/xtrem-authorization/pages__new_group_panel____subtitle": "<PERSON><PERSON><PERSON>n Sie eine Gruppe, um den Benutzern Zugriffsrechte auf Aktivitäten und Daten zu gewähren.", "@sage/xtrem-authorization/pages__new_group_panel____title": "Neue Gruppe", "@sage/xtrem-authorization/pages__new_group_panel__allRoles____title": "Alle Rollen", "@sage/xtrem-authorization/pages__new_group_panel__allSiteAndSiteGroup____title": "Alle Standorte/Standortgruppen", "@sage/xtrem-authorization/pages__new_group_panel__cancelAction____title": "Abbrechen", "@sage/xtrem-authorization/pages__new_group_panel__groupInfoBlock____title": "Gruppeninformationen", "@sage/xtrem-authorization/pages__new_group_panel__id____title": "ID", "@sage/xtrem-authorization/pages__new_group_panel__name____title": "Name", "@sage/xtrem-authorization/pages__new_group_panel__saveNewGroup____title": "Speichern", "@sage/xtrem-authorization/pages__new_group_panel__selectionSection____title": "Rollen und Standorte/Standortgruppen zuweisen", "@sage/xtrem-authorization/pages__new_group_panel__selectRole____columns__title___id": "ID", "@sage/xtrem-authorization/pages__new_group_panel__selectRole____columns__title__id": "ID", "@sage/xtrem-authorization/pages__new_group_panel__selectRole____columns__title__name": "Name", "@sage/xtrem-authorization/pages__new_group_panel__selectSiteAndSiteGroup____columns__title___id": "ID", "@sage/xtrem-authorization/pages__new_group_panel__selectSiteAndSiteGroup____columns__title__id": "ID", "@sage/xtrem-authorization/pages__new_group_panel__selectSiteAndSiteGroup____columns__title__name": "Name", "@sage/xtrem-authorization/pages__operator_user_panel____title": "Anwendercode zurücksetzen", "@sage/xtrem-authorization/pages__operator_user_panel__cancel____title": "Abbrechen", "@sage/xtrem-authorization/pages__operator_user_panel__confirm____title": "Bestätigen", "@sage/xtrem-authorization/pages__operator_user_panel__operatorCodeBlock____title": "Block Anwendercode", "@sage/xtrem-authorization/pages__operator_user_panel__operatorCodeInput____title": "Neuer Anwendercode", "@sage/xtrem-authorization/pages__operator_user_panel__operatorCodeSection____title": "Bereich Anwendercode", "@sage/xtrem-authorization/pages__role_detail____subtitle": "<PERSON><PERSON>", "@sage/xtrem-authorization/pages__role_detail____title": "<PERSON><PERSON>", "@sage/xtrem-authorization/pages__role_detail__activitySection____title": "Aktivitäten", "@sage/xtrem-authorization/pages__role_detail__cancel____title": "Abbrechen", "@sage/xtrem-authorization/pages__role_detail__confirm____title": "Speichern", "@sage/xtrem-authorization/pages__role_detail__not_updated": "Aktualisierung der Berechtigungen fehlgeschlagen.", "@sage/xtrem-authorization/pages__role_detail__packageBlock1____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock10____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock11____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock12____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock13____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock14____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock15____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock16____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock17____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock18____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock19____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock2____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock20____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock3____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock4____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock5____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock6____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock7____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock8____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock9____title": "", "@sage/xtrem-authorization/pages__role_detail_updated": "Berechtigungen aktualisiert.", "@sage/xtrem-authorization/pages__role_list____title": "<PERSON><PERSON>", "@sage/xtrem-authorization/pages__role_list__addNewRole____title": "<PERSON><PERSON> hinzufügen", "@sage/xtrem-authorization/pages__role_list__duplicate": "Duplizieren", "@sage/xtrem-authorization/pages__role_list__field____columns__title___id": "ID", "@sage/xtrem-authorization/pages__role_list__field____columns__title__id": "ID", "@sage/xtrem-authorization/pages__role_list__field____columns__title__name": "Name", "@sage/xtrem-authorization/pages__role_list__field____dropdownActions__title": "Role bearbeiten", "@sage/xtrem-authorization/pages__role_list__field____dropdownActions__title__2": "<PERSON><PERSON> klonen", "@sage/xtrem-authorization/pages__role_list__field____dropdownActions__title__3": "Gruppen anzeigen", "@sage/xtrem-authorization/pages__role_list__field____dropdownActions__title__4": "Rolle löschen", "@sage/xtrem-authorization/pages__role_list__fieldBlock____title": "Rollenzeilen", "@sage/xtrem-authorization/pages__role_list__id____title": "ID", "@sage/xtrem-authorization/pages__role_list__name____title": "Name", "@sage/xtrem-authorization/pages__role_list__roles____columns__title___id": "ID", "@sage/xtrem-authorization/pages__role_list__roles____columns__title__createdByUserAndStamp": "<PERSON><PERSON><PERSON> von", "@sage/xtrem-authorization/pages__role_list__roles____columns__title__id": "ID", "@sage/xtrem-authorization/pages__role_list__roles____columns__title__isBillingRole": "Fakturierungsrolle", "@sage/xtrem-authorization/pages__role_list__roles____columns__title__name": "Name", "@sage/xtrem-authorization/pages__role_list__roles____columns__title__updateByUserAndStamp": "<PERSON><PERSON><PERSON><PERSON><PERSON> von", "@sage/xtrem-authorization/pages__role_list__roles____dropdownActions__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__role_list__roles____dropdownActions__title__2": "Löschen", "@sage/xtrem-authorization/pages__role_list__roles____dropdownActions__title__3": "Duplizieren", "@sage/xtrem-authorization/pages__role_list__rolesBlock____title": "Rollenzeilen", "@sage/xtrem-authorization/pages__role_list__roleSection____title": "<PERSON><PERSON>", "@sage/xtrem-authorization/pages__role_setup": "<PERSON><PERSON> erste<PERSON>t", "@sage/xtrem-authorization/pages__role_setup____title": "Neue Rolle", "@sage/xtrem-authorization/pages__role_setup__cancel____title": "Abbrechen", "@sage/xtrem-authorization/pages__role_setup__confirm____title": "Speichern", "@sage/xtrem-authorization/pages__role_setup__duplicated_id": "Die erfasste ID existiert bereits. Wählen Sie eine andere ID aus.", "@sage/xtrem-authorization/pages__role_setup__id____title": "ID", "@sage/xtrem-authorization/pages__role_setup__name____title": "Name", "@sage/xtrem-authorization/pages__role_setup__next____title": "Fortfahren", "@sage/xtrem-authorization/pages__role_setup__packageBlock1____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock10____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock11____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock12____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock13____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock14____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock15____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock16____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock17____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock18____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock19____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock2____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock20____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock3____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock4____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock5____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock6____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock7____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock8____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock9____title": "", "@sage/xtrem-authorization/pages__role_setup__packageSelect____title": "<PERSON><PERSON>", "@sage/xtrem-authorization/pages__role_setup__roleBlock____title": "Rolleninformationen", "@sage/xtrem-authorization/pages__role_setup_failed": "Erstellung der Rolle fehlgeschlagen.", "@sage/xtrem-authorization/pages__site_group____subtitle": "Standortgruppe", "@sage/xtrem-authorization/pages__site_group____title": "Standortgruppe", "@sage/xtrem-authorization/pages__site_group___id____title": "ID", "@sage/xtrem-authorization/pages__site_group__associatedAuthorizationGroupBlock____title": "Autorisierungsgruppe", "@sage/xtrem-authorization/pages__site_group__associatedAuthorizationGroupSection____title": "Benutzergruppen", "@sage/xtrem-authorization/pages__site_group__chartBlock____title": "Organisation", "@sage/xtrem-authorization/pages__site_group__confirm____title": "Speichern", "@sage/xtrem-authorization/pages__site_group__customSave____title": "Speichern", "@sage/xtrem-authorization/pages__site_group__customSaveAction____title": "Speichern", "@sage/xtrem-authorization/pages__site_group__editSiteGroup____title": "Standortgruppe bearbeiten", "@sage/xtrem-authorization/pages__site_group__generalBlock____title": "Standortgruppe", "@sage/xtrem-authorization/pages__site_group__generalSection____title": "Allgemein", "@sage/xtrem-authorization/pages__site_group__groups____columns__title": "<PERSON><PERSON><PERSON> von", "@sage/xtrem-authorization/pages__site_group__groups____columns__title___id": "ID", "@sage/xtrem-authorization/pages__site_group__groups____columns__title__2": "<PERSON><PERSON><PERSON><PERSON><PERSON> von", "@sage/xtrem-authorization/pages__site_group__groups____columns__title__id": "ID", "@sage/xtrem-authorization/pages__site_group__groups____columns__title__name": "Name", "@sage/xtrem-authorization/pages__site_group__groups____title": "Autorisierungsgruppe", "@sage/xtrem-authorization/pages__site_group__hierarchyChartContent____title": "Organisation", "@sage/xtrem-authorization/pages__site_group__id____title": "ID", "@sage/xtrem-authorization/pages__site_group__isActive____title": "Aktiv", "@sage/xtrem-authorization/pages__site_group__isLegalCompany____title": "Unternehmen", "@sage/xtrem-authorization/pages__site_group__name____title": "Name", "@sage/xtrem-authorization/pages__site_group__siteGroupList____title": "Standortgruppenliste", "@sage/xtrem-authorization/pages__site_group__siteGroups____columns__title__siteGroup___id": "ID", "@sage/xtrem-authorization/pages__site_group__siteGroups____columns__title__siteGroup__id": "ID", "@sage/xtrem-authorization/pages__site_group__siteGroups____columns__title__siteGroup__name": "Name", "@sage/xtrem-authorization/pages__site_group__siteGroupsBlock____title": "Standortgruppen", "@sage/xtrem-authorization/pages__site_group__siteGroupsMultiReference____columns__title___id": "ID", "@sage/xtrem-authorization/pages__site_group__siteGroupsMultiReference____columns__title__id": "ID", "@sage/xtrem-authorization/pages__site_group__siteGroupsMultiReference____columns__title__name": "Name", "@sage/xtrem-authorization/pages__site_group__siteGroupsMultiReference____lookupDialogTitle": "Standortgruppen auswählen", "@sage/xtrem-authorization/pages__site_group__sites____columns__title__site___id": "ID", "@sage/xtrem-authorization/pages__site_group__sites____columns__title__site__id": "ID", "@sage/xtrem-authorization/pages__site_group__sites____columns__title__site__name": "Name", "@sage/xtrem-authorization/pages__site_group__sitesBlock____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__site_group__sitesMultiReference____columns__title___id": "ID", "@sage/xtrem-authorization/pages__site_group__sitesMultiReference____columns__title__id": "ID", "@sage/xtrem-authorization/pages__site_group__sitesMultiReference____columns__title__name": "Name", "@sage/xtrem-authorization/pages__site_group__sitesMultiReference____lookupDialogTitle": "Standorte auswählen", "@sage/xtrem-authorization/pages__site_group__toggleChart____helperText": "Benutzer dürfen die Tabellen- oder Diagrammanzeige aktivieren oder deaktivieren", "@sage/xtrem-authorization/pages__site_group__toggleChart____title": "Anzeigeoptionen", "@sage/xtrem-authorization/pages__site_group__toggleChartBlock____title": "Diagramm/Tabelle aktivieren/deaktivieren", "@sage/xtrem-authorization/pages__site_group_chart": "Diagramm", "@sage/xtrem-authorization/pages__site_group_grid": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__site_group_list____title": "Standortgruppen", "@sage/xtrem-authorization/pages__site_group_list__addNewSiteGroup____title": "Neue Standortgruppe", "@sage/xtrem-authorization/pages__site_group_list__createSite____title": "Standort erstellen", "@sage/xtrem-authorization/pages__site_group_list__createSiteGroup____title": "Standortgruppe erstellen", "@sage/xtrem-authorization/pages__site_group_list__fieldBlock____title": "Standortgruppe", "@sage/xtrem-authorization/pages__site_group_list__groupList____title": "Standortgruppenliste", "@sage/xtrem-authorization/pages__site_group_list__section____title": "Standortgruppen", "@sage/xtrem-authorization/pages__site_group_list__siteGroups____levels__columns__title___id": "ID", "@sage/xtrem-authorization/pages__site_group_list__siteGroups____levels__columns__title__id": "ID", "@sage/xtrem-authorization/pages__site_group_list__siteGroups____levels__columns__title__id__2": "ID", "@sage/xtrem-authorization/pages__site_group_list__siteGroups____levels__columns__title__name": "Name", "@sage/xtrem-authorization/pages__site_group_list__siteGroups____levels__columns__title__name__2": "Name", "@sage/xtrem-authorization/pages__site_group_list__siteGroups____levels__dropdownActions__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__site_group_list__siteGroups____levels__dropdownActions__title__2": "Löschen", "@sage/xtrem-authorization/pages__site_group_new_title": "Neue Standortgruppe", "@sage/xtrem-authorization/pages__support_access_history____title": "Support-<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__support_access_history__accessHistory____columns__title___id": "ID", "@sage/xtrem-authorization/pages__support_access_history__accessHistory____columns__title__endTime": "Endzeit", "@sage/xtrem-authorization/pages__support_access_history__accessHistory____columns__title__isReadOnlyAccess": "Lesezugriff", "@sage/xtrem-authorization/pages__support_access_history__accessHistory____columns__title__startTime": "Startzeit", "@sage/xtrem-authorization/pages__support_access_history__accessHistory____columns__title__status": "Status", "@sage/xtrem-authorization/pages__support_access_history__accessHistory____title": "Verlauf Support-Zugriff", "@sage/xtrem-authorization/pages__support_access_history__allow_access_button_text": "Zugriff gewähren", "@sage/xtrem-authorization/pages__support_access_history__description____content": "Geben Sie die Dauer des Benutzerzugriffs an.", "@sage/xtrem-authorization/pages__support_access_history__dummy____content": "", "@sage/xtrem-authorization/pages__support_access_history__extend_access_button_text": "Zugriff verlängern", "@sage/xtrem-authorization/pages__support_access_history__forTime____title": "<PERSON><PERSON>", "@sage/xtrem-authorization/pages__support_access_history__infoSection____title": "Allgemein", "@sage/xtrem-authorization/pages__support_access_history__isReadOnlyAccess____title": "Lesezugriff", "@sage/xtrem-authorization/pages__support_access_history__isSupportAccessOpen____title": "Lesezugriff", "@sage/xtrem-authorization/pages__support_access_history__please_fill_the_number_and_unit_field": "Erfassen Sie eine Zahl und eine Einheit.", "@sage/xtrem-authorization/pages__support_access_history__revoke_access_button_text": "Zugriff widerrufen", "@sage/xtrem-authorization/pages__support_access_history__supportAccessBlock____title": "Support-Zugriff durch Sage gewähren", "@sage/xtrem-authorization/pages__support_access_history__supportAccessHistoryBlock____title": "Verlauf Support-Zugriff", "@sage/xtrem-authorization/pages__support_access_history__unit____title": "Einheiten", "@sage/xtrem-authorization/pages__user____navigationPanel__bulkActions__title": "Willkommens-E-Mail senden", "@sage/xtrem-authorization/pages__user____navigationPanel__listItem__image__title": "Bild", "@sage/xtrem-authorization/pages__user____navigationPanel__listItem__line2__title": "E-Mail", "@sage/xtrem-authorization/pages__user____navigationPanel__listItem__line3__title": "API-<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__user____navigationPanel__listItem__title__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__user____navigationPanel__listItem__titleRight__title": "Nachname", "@sage/xtrem-authorization/pages__user____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-authorization/pages__user____navigationPanel__optionsMenu__title__2": "Interaktiv", "@sage/xtrem-authorization/pages__user____navigationPanel__optionsMenu__title__3": "Drittan<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__user____objectTypePlural": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__user____objectTypeSingular": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__user____subtitle": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__user____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__user___id____title": "ID", "@sage/xtrem-authorization/pages__user__associatedRoles____columns__title___id": "ID", "@sage/xtrem-authorization/pages__user__associatedRoles____columns__title__createdBy": "<PERSON><PERSON><PERSON> von", "@sage/xtrem-authorization/pages__user__associatedRoles____columns__title__createdByUserAndStamp": "<PERSON><PERSON><PERSON> von", "@sage/xtrem-authorization/pages__user__associatedRoles____columns__title__id": "ID", "@sage/xtrem-authorization/pages__user__associatedRoles____columns__title__name": "Name", "@sage/xtrem-authorization/pages__user__associatedRoles____columns__title__updateByUserAndStamp": "<PERSON><PERSON><PERSON><PERSON><PERSON> von", "@sage/xtrem-authorization/pages__user__associatedRoles____columns__title__updatedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON> von", "@sage/xtrem-authorization/pages__user__associatedRoles____title": "Rollen und Aktivitäten", "@sage/xtrem-authorization/pages__user__associatedSiteGroups____columns__title___id": "ID", "@sage/xtrem-authorization/pages__user__associatedSiteGroups____columns__title__createdBy": "<PERSON><PERSON><PERSON> von", "@sage/xtrem-authorization/pages__user__associatedSiteGroups____columns__title__createdByUserAndStamp": "<PERSON><PERSON><PERSON> von", "@sage/xtrem-authorization/pages__user__associatedSiteGroups____columns__title__id": "ID", "@sage/xtrem-authorization/pages__user__associatedSiteGroups____columns__title__name": "Name", "@sage/xtrem-authorization/pages__user__associatedSiteGroups____columns__title__updateByUserAndStamp": "<PERSON><PERSON><PERSON><PERSON><PERSON> von", "@sage/xtrem-authorization/pages__user__associatedSiteGroups____columns__title__updatedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON> von", "@sage/xtrem-authorization/pages__user__associatedSiteGroups____title": "Standortgruppen", "@sage/xtrem-authorization/pages__user__associatedSiteGroupsSection____title": "Standortgruppen", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__columns__group__id__title": "ID", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__columns__group__id__title__2": "Name", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__columns__group__id__title__3": "ID", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__title__group___id": "ID", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__title__group__groupRolesDisplay": "<PERSON><PERSON>", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__title__group__groupSitesDisplay": "Standortgruppe", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__title__group__id": "ID", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__title__group__name": "Name", "@sage/xtrem-authorization/pages__user__authorizationGroup____title": "Autorisierungsgruppen", "@sage/xtrem-authorization/pages__user__editUser____title": "<PERSON><PERSON><PERSON> bearbeiten", "@sage/xtrem-authorization/pages__user__email____title": "E-Mail", "@sage/xtrem-authorization/pages__user__firstName____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__user__generalSection____title": "Allgemein", "@sage/xtrem-authorization/pages__user__idBlock____title": "ID", "@sage/xtrem-authorization/pages__user__importExportDateFormat____helperText": "Standard<PERSON><PERSON> wird sein ", "@sage/xtrem-authorization/pages__user__importExportDateFormat____title": "Datumsformat Import/Export", "@sage/xtrem-authorization/pages__user__importExportDelimiter____helperText": "Standard<PERSON><PERSON> wird sein ", "@sage/xtrem-authorization/pages__user__importExportDelimiter____title": "Trennzeichen Import/Export", "@sage/xtrem-authorization/pages__user__isActive____title": "Aktiv", "@sage/xtrem-authorization/pages__user__isAdministrator____title": "Administrator", "@sage/xtrem-authorization/pages__user__isApiUser____title": "API-<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__user__isDemoPersona____title": "Demo-Persona", "@sage/xtrem-authorization/pages__user__isExternal____title": "Extern", "@sage/xtrem-authorization/pages__user__isOperatorUser____title": "Pin-Authentifizierung", "@sage/xtrem-authorization/pages__user__isWelcomeMailSent____title": "Willkommens-E-Mail senden", "@sage/xtrem-authorization/pages__user__lastName____title": "Nachname", "@sage/xtrem-authorization/pages__user__list_bulk_send_welcome_mail": "E-Mail senden", "@sage/xtrem-authorization/pages__user__operatorCode____title": "PIN-Code", "@sage/xtrem-authorization/pages__user__preferencesBlock____title": "Einstellungen", "@sage/xtrem-authorization/pages__user__preferencesSection____title": "Einstellungen", "@sage/xtrem-authorization/pages__user__resetOperatorCodePage____title": "PIN-Code", "@sage/xtrem-authorization/pages__user__role____columns__title__id": "ID", "@sage/xtrem-authorization/pages__user__role____columns__title__name": "Name", "@sage/xtrem-authorization/pages__user__role____lookupDialogTitle": "Fakturierungsrolle auswählen", "@sage/xtrem-authorization/pages__user__role____title": "Fakturierungsrolle", "@sage/xtrem-authorization/pages__user__rolesActivitiesSection____title": "Rollen und Aktivitäten", "@sage/xtrem-authorization/pages__user__save____title": "Speichern", "@sage/xtrem-authorization/pages__user__send_welcome_mail": "E-Mail senden", "@sage/xtrem-authorization/pages__user__send_welcome_mail_button": "Senden", "@sage/xtrem-authorization/pages__user__send_welcome_mail_dialog_content": "<PERSON><PERSON> sind dabei, eine Willkommens-E-Mail an den Benutzer zu senden.", "@sage/xtrem-authorization/pages__user__userAuthorizationInformationBlock____title": "Autorisierungsgruppe", "@sage/xtrem-authorization/pages__user__userGroups____columns__title___id": "ID", "@sage/xtrem-authorization/pages__user__userGroups____columns__title__id": "ID", "@sage/xtrem-authorization/pages__user__userGroups____columns__title__name": "Name", "@sage/xtrem-authorization/pages__user__userGroups____lookupDialogTitle": "Autorisierungsgruppe auswählen", "@sage/xtrem-authorization/pages__user__userGroups____title": "Autorisierungsgruppe", "@sage/xtrem-authorization/pages__user__userInformationBlock____title": "Benutzerinformationen", "@sage/xtrem-authorization/pages__user__userList____title": "Benutzer anzeigen", "@sage/xtrem-authorization/pages__user__userPhotoBlock____title": "Foto", "@sage/xtrem-authorization/pages__user_group_list____title": "Benutzergruppen", "@sage/xtrem-authorization/pages__user_group_list__groupList____title": "Gruppenliste", "@sage/xtrem-authorization/pages__user_group_list__title": "Benutzergruppen", "@sage/xtrem-authorization/pages__user_group_list__users____columns__title": "<PERSON><PERSON><PERSON> von", "@sage/xtrem-authorization/pages__user_group_list__users____columns__title___id": "ID", "@sage/xtrem-authorization/pages__user_group_list__users____columns__title__2": "<PERSON><PERSON><PERSON><PERSON><PERSON> von", "@sage/xtrem-authorization/pages__user_group_list__users____columns__title__displayName": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__user_group_list__users____columns__title__isActive": "Aktiv", "@sage/xtrem-authorization/pages__user_group_list__usersBlock____title": "Benutzergruppen", "@sage/xtrem-authorization/pages__user_list____title": "Benutzerliste", "@sage/xtrem-authorization/pages__user_list__addNewUser____title": "Benutzer hinzufügen", "@sage/xtrem-authorization/pages__user_list__fieldBlock____title": "Benutzerzeilen", "@sage/xtrem-authorization/pages__user_list__sendBulkWelcomeMail____title": "Willkommens-E-Mail senden", "@sage/xtrem-authorization/pages__user_list__users____columns__title___id": "ID", "@sage/xtrem-authorization/pages__user_list__users____columns__title__createdBy": "<PERSON><PERSON><PERSON> von", "@sage/xtrem-authorization/pages__user_list__users____columns__title__displayName": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__user_list__users____columns__title__email": "E-Mail", "@sage/xtrem-authorization/pages__user_list__users____columns__title__groupDisplay": "Autorisierungsgruppe", "@sage/xtrem-authorization/pages__user_list__users____columns__title__isWelcomeMailSent": "Willkommens-E-Mail gesendet", "@sage/xtrem-authorization/pages__user_list__users____columns__title__updatedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON> von", "@sage/xtrem-authorization/pages__user_list__users____dropdownActions__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__user_list_bulk_send__welcome_mail_dialog_content": "<PERSON><PERSON> sind dabei, eine Willkommens-E-Mail an alle ausgewählten Benutzer zu senden.", "@sage/xtrem-authorization/pages_role_list_delete_confirmation": "Löschen bestätigen", "@sage/xtrem-authorization/pages_role_list_delete_message": "<PERSON><PERSON> sind dabei, die Rolle {{role}} zu löschen.", "@sage/xtrem-authorization/pages_user_list_bulk_send__welcome_mail_button": "Senden", "@sage/xtrem-authorization/pages_user_list_bulk_send_success": "Willkommens-E-Mails gesendet", "@sage/xtrem-authorization/pages-confirm-cancel": "Abbrechen", "@sage/xtrem-authorization/permission__all__name": "Alle", "@sage/xtrem-authorization/permission__allow_access__name": "Zugriff gewähren", "@sage/xtrem-authorization/permission__create__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/permission__delete__name": "Löschen", "@sage/xtrem-authorization/permission__extend_access__name": "Zugriff verlängern", "@sage/xtrem-authorization/permission__is_support_access_open__name": "Ist Support-Zugriff offen", "@sage/xtrem-authorization/permission__lookup__name": "<PERSON><PERSON>", "@sage/xtrem-authorization/permission__manage__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/permission__read__name": "<PERSON><PERSON>", "@sage/xtrem-authorization/permission__revoke_access__name": "Zugriff widerrufen", "@sage/xtrem-authorization/permission__update__name": "Aktualisieren", "@sage/xtrem-authorization/reset-operator-code": "Z<PERSON>ücksetzen", "@sage/xtrem-authorization/role_detail_page_is_not_read_only": "Abbrechen", "@sage/xtrem-authorization/role_detail_page_is_read_only": "OK", "@sage/xtrem-authorization/service_options__authorization_service_option__name": "Autorisierungsdienstoption", "@sage/xtrem-authorization/shared_common_package_xtrem_inventory": "Bestand", "@sage/xtrem-authorization/stickers__demo_persona_sticker____title": "Demo-Persona", "@sage/xtrem-authorization/stickers__demo_persona_sticker__section____title": "<PERSON>a", "@sage/xtrem-authorization/stickers__demo_persona_sticker__selectionPersona____title": "Demo-Persona"}