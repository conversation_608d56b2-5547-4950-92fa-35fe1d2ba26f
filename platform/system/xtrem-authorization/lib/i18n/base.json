{"@sage/xtrem-authorization/activity__group_role_site__name": "Group role site", "@sage/xtrem-authorization/activity__role__name": "Role", "@sage/xtrem-authorization/activity__site_group__name": "Site group", "@sage/xtrem-authorization/activity__support_access_history__name": "Support access history", "@sage/xtrem-authorization/activity__user__name": "User", "@sage/xtrem-authorization/cannot-delete-site-from-site-group": "The current site cannot be deleted from the linked site groups", "@sage/xtrem-authorization/cannot-delete-site-group": "Linked site group cannot be deleted", "@sage/xtrem-authorization/data_types__name_array_data_type__name": "Name array data type", "@sage/xtrem-authorization/data_types__string_data_type__name": "String data type", "@sage/xtrem-authorization/data_types__support_access_history_status_enum__name": "Support access history status enum", "@sage/xtrem-authorization/data_types__support_access_unit_enum__name": "Support access unit enum", "@sage/xtrem-authorization/data_types__user_type_enum__name": "User type enum", "@sage/xtrem-authorization/delete-confirmation": "Record deleted", "@sage/xtrem-authorization/delete-dialog-content": "You are about to delete this group.", "@sage/xtrem-authorization/delete-group": "Confirm deletion", "@sage/xtrem-authorization/duplication-confirmation": "Record duplicated", "@sage/xtrem-authorization/enums__support_access_history_status__closed": "Closed", "@sage/xtrem-authorization/enums__support_access_history_status__open": "Open", "@sage/xtrem-authorization/enums__support_access_unit__days": "Days", "@sage/xtrem-authorization/enums__support_access_unit__hours": "Hours", "@sage/xtrem-authorization/enums__support_access_unit__minutes": "Minutes", "@sage/xtrem-authorization/enums__user_type__application": "Application", "@sage/xtrem-authorization/enums__user_type__system": "System", "@sage/xtrem-authorization/menu_item__support": "Support", "@sage/xtrem-authorization/menu_item__user-data": "Users and security", "@sage/xtrem-authorization/node-extensions__site_extension__property__groupRoleSites": "Group role sites", "@sage/xtrem-authorization/node-extensions__site_extension__property__siteGroups": "Site groups", "@sage/xtrem-authorization/node-extensions__user_extension__bulkMutation__sendBulkWelcomeMail": "Send bulk welcome mail", "@sage/xtrem-authorization/node-extensions__user_extension__bulkMutation__sendBulkWelcomeMail__failed": "Send bulk welcome mail failed.", "@sage/xtrem-authorization/node-extensions__user_extension__property__authorizationGroup": "Authorization group", "@sage/xtrem-authorization/node-extensions__user_extension__property__billingRole": "Billing role", "@sage/xtrem-authorization/node-extensions__user_extension__property__groupDisplay": "Group display", "@sage/xtrem-authorization/node-extensions__user_extension__property__objectGrants": "Object grants", "@sage/xtrem-authorization/node-extensions__user_extension__query__all": "All", "@sage/xtrem-authorization/node-extensions__user_extension__query__all__failed": "All failed.", "@sage/xtrem-authorization/node-extensions__user_extension__query__all__parameter__isActive": "Is active", "@sage/xtrem-authorization/nodes__activity__asyncMutation__asyncExport": "Export", "@sage/xtrem-authorization/nodes__activity__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-authorization/nodes__activity__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__activity__node_name": "Activity", "@sage/xtrem-authorization/nodes__activity__property__description": "Description", "@sage/xtrem-authorization/nodes__activity__property__name": "Name", "@sage/xtrem-authorization/nodes__activity__property__package": "Package", "@sage/xtrem-authorization/nodes__activity__property__permissions": "Permissions", "@sage/xtrem-authorization/nodes__group_role__asyncMutation__asyncExport": "Export", "@sage/xtrem-authorization/nodes__group_role__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-authorization/nodes__group_role__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__group_role__node_name": "Group role", "@sage/xtrem-authorization/nodes__group_role__property__groupRoleSite": "Group role site", "@sage/xtrem-authorization/nodes__group_role__property__role": "Role", "@sage/xtrem-authorization/nodes__group_role_site__asyncMutation__asyncExport": "Export", "@sage/xtrem-authorization/nodes__group_role_site__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-authorization/nodes__group_role_site__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__group_role_site__node_name": "Group role site", "@sage/xtrem-authorization/nodes__group_role_site__property__createdBy": "Created by", "@sage/xtrem-authorization/nodes__group_role_site__property__createStamp": "Create stamp", "@sage/xtrem-authorization/nodes__group_role_site__property__groupRoles": "Group roles", "@sage/xtrem-authorization/nodes__group_role_site__property__groupRolesDisplay": "Group roles display", "@sage/xtrem-authorization/nodes__group_role_site__property__groupSites": "Group sites", "@sage/xtrem-authorization/nodes__group_role_site__property__groupSitesDisplay": "Group sites display", "@sage/xtrem-authorization/nodes__group_role_site__property__id": "Id", "@sage/xtrem-authorization/nodes__group_role_site__property__name": "Name", "@sage/xtrem-authorization/nodes__group_role_site__property__updatedBy": "Updated by", "@sage/xtrem-authorization/nodes__group_role_site__property__updateStamp": "Update stamp", "@sage/xtrem-authorization/nodes__group_site__asyncMutation__asyncExport": "Export", "@sage/xtrem-authorization/nodes__group_site__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-authorization/nodes__group_site__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__group_site__node_name": "Group site", "@sage/xtrem-authorization/nodes__group_site__property__groupRoleSite": "Group role site", "@sage/xtrem-authorization/nodes__group_site__property__siteGroup": "Site group", "@sage/xtrem-authorization/nodes__restricted_node__node_name": "Restricted node", "@sage/xtrem-authorization/nodes__restricted_node__property__userGrants": "User grants", "@sage/xtrem-authorization/nodes__restricted_node_user_grant__asyncMutation__asyncExport": "Export", "@sage/xtrem-authorization/nodes__restricted_node_user_grant__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-authorization/nodes__restricted_node_user_grant__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__restricted_node_user_grant__node_name": "Restricted node user grant", "@sage/xtrem-authorization/nodes__restricted_node_user_grant__property__accessMap": "Access map", "@sage/xtrem-authorization/nodes__restricted_node_user_grant__property__object": "Object", "@sage/xtrem-authorization/nodes__restricted_node_user_grant__property__user": "User", "@sage/xtrem-authorization/nodes__role__asyncMutation__asyncExport": "Export", "@sage/xtrem-authorization/nodes__role__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-authorization/nodes__role__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__role__node_name": "Role", "@sage/xtrem-authorization/nodes__role__property__activities": "Activities", "@sage/xtrem-authorization/nodes__role__property__createdBy": "Created by", "@sage/xtrem-authorization/nodes__role__property__description": "Description", "@sage/xtrem-authorization/nodes__role__property__id": "Id", "@sage/xtrem-authorization/nodes__role__property__isActive": "Is active", "@sage/xtrem-authorization/nodes__role__property__isBillingRole": "Is billing role", "@sage/xtrem-authorization/nodes__role__property__name": "Name", "@sage/xtrem-authorization/nodes__role__property__roles": "Roles", "@sage/xtrem-authorization/nodes__role__property__updatedBy": "Updated by", "@sage/xtrem-authorization/nodes__role__query__all": "All", "@sage/xtrem-authorization/nodes__role__query__all__failed": "All failed.", "@sage/xtrem-authorization/nodes__role__query__all__parameter__isActive": "Is active", "@sage/xtrem-authorization/nodes__role_activity__asyncMutation__asyncExport": "Export", "@sage/xtrem-authorization/nodes__role_activity__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-authorization/nodes__role_activity__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__role_activity__mutation__roleActivitiesCreateUpdate": "Role activities create update", "@sage/xtrem-authorization/nodes__role_activity__mutation__roleActivitiesCreateUpdate__failed": "Role activities create update failed.", "@sage/xtrem-authorization/nodes__role_activity__mutation__roleActivitiesCreateUpdate__parameter__roleActivities": "Role activities", "@sage/xtrem-authorization/nodes__role_activity__mutation__roleActivitiesCreateUpdate__parameter__roleId": "Role id", "@sage/xtrem-authorization/nodes__role_activity__mutation__roleActivitiesCreateUpdate__parameter__roleName": "Role name", "@sage/xtrem-authorization/nodes__role_activity__mutation__roleActivitiesCreateUpdate__parameter__roleSysId": "Role sys id", "@sage/xtrem-authorization/nodes__role_activity__node_name": "Role activity", "@sage/xtrem-authorization/nodes__role_activity__property__activity": "Activity", "@sage/xtrem-authorization/nodes__role_activity__property__getPermissions": "Get permissions", "@sage/xtrem-authorization/nodes__role_activity__property__hasAllPermissions": "Has all permissions", "@sage/xtrem-authorization/nodes__role_activity__property__isActive": "Is active", "@sage/xtrem-authorization/nodes__role_activity__property__permissions": "Permissions", "@sage/xtrem-authorization/nodes__role_activity__property__role": "Role", "@sage/xtrem-authorization/nodes__role_to_role__asyncMutation__asyncExport": "Export", "@sage/xtrem-authorization/nodes__role_to_role__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-authorization/nodes__role_to_role__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__role_to_role__node_name": "Role to role", "@sage/xtrem-authorization/nodes__role_to_role__property__role": "Role", "@sage/xtrem-authorization/nodes__role_to_role__property__roleOrigin": "Role origin", "@sage/xtrem-authorization/nodes__site_group__asyncMutation__asyncExport": "Export", "@sage/xtrem-authorization/nodes__site_group__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-authorization/nodes__site_group__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__site_group__node_name": "Site group", "@sage/xtrem-authorization/nodes__site_group__property__createdBy": "Created by", "@sage/xtrem-authorization/nodes__site_group__property__hierarchyChartContent": "Hierarchy chart content", "@sage/xtrem-authorization/nodes__site_group__property__id": "Id", "@sage/xtrem-authorization/nodes__site_group__property__isActive": "Is active", "@sage/xtrem-authorization/nodes__site_group__property__isLegalCompany": "Is legal company", "@sage/xtrem-authorization/nodes__site_group__property__name": "Name", "@sage/xtrem-authorization/nodes__site_group__property__siteGroups": "Site groups", "@sage/xtrem-authorization/nodes__site_group__property__sites": "Sites", "@sage/xtrem-authorization/nodes__site_group__property__updatedBy": "Updated by", "@sage/xtrem-authorization/nodes__site_group_to_site__asyncMutation__asyncExport": "Export", "@sage/xtrem-authorization/nodes__site_group_to_site__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-authorization/nodes__site_group_to_site__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__site_group_to_site__node_name": "Site group to site", "@sage/xtrem-authorization/nodes__site_group_to_site__property__dateAdd": "Date add", "@sage/xtrem-authorization/nodes__site_group_to_site__property__isValid": "Is valid", "@sage/xtrem-authorization/nodes__site_group_to_site__property__site": "Site", "@sage/xtrem-authorization/nodes__site_group_to_site__property__siteGroup": "Site group", "@sage/xtrem-authorization/nodes__site_group_to_site_group__asyncMutation__asyncExport": "Export", "@sage/xtrem-authorization/nodes__site_group_to_site_group__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-authorization/nodes__site_group_to_site_group__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__site_group_to_site_group__node_name": "Site group to site group", "@sage/xtrem-authorization/nodes__site_group_to_site_group__property__siteGroup": "Site group", "@sage/xtrem-authorization/nodes__site_group_to_site_group__property__siteGroupOrigin": "Site group origin", "@sage/xtrem-authorization/nodes__support_access__cannot_allow_support_access_because_there_is_a_open_session": "There is already an open session. Contact your system administrator.", "@sage/xtrem-authorization/nodes__support_access__end_time_must_be_greater_than_start_time": "The end time must be greater than the start time.", "@sage/xtrem-authorization/nodes__support_access__number_must_be_greater_than_0": "The number must be greater than 0.", "@sage/xtrem-authorization/nodes__support_access__there_is_no_open_session_to_extend_the_access": "There is no open session to extend the access. Contact your system administrator.", "@sage/xtrem-authorization/nodes__support_access__there_is_no_open_session_to_revoke_the_access": "There is no open session to revoke the access. Contact your system administrator.", "@sage/xtrem-authorization/nodes__support_access_history__asyncMutation__asyncExport": "Export", "@sage/xtrem-authorization/nodes__support_access_history__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-authorization/nodes__support_access_history__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__support_access_history__mutation__allowAccess": "Allow access", "@sage/xtrem-authorization/nodes__support_access_history__mutation__allowAccess__failed": "Allow access failed.", "@sage/xtrem-authorization/nodes__support_access_history__mutation__allowAccess__parameter__forTime": "For time", "@sage/xtrem-authorization/nodes__support_access_history__mutation__allowAccess__parameter__isReadOnlyAccess": "Is read only access", "@sage/xtrem-authorization/nodes__support_access_history__mutation__allowAccess__parameter__units": "Units", "@sage/xtrem-authorization/nodes__support_access_history__mutation__extendAccess": "Extend access", "@sage/xtrem-authorization/nodes__support_access_history__mutation__extendAccess__failed": "Extend access failed.", "@sage/xtrem-authorization/nodes__support_access_history__mutation__extendAccess__parameter__forTime": "For time", "@sage/xtrem-authorization/nodes__support_access_history__mutation__extendAccess__parameter__units": "Units", "@sage/xtrem-authorization/nodes__support_access_history__mutation__revokeAccess": "Revoke access", "@sage/xtrem-authorization/nodes__support_access_history__mutation__revokeAccess__failed": "Revoke access failed.", "@sage/xtrem-authorization/nodes__support_access_history__node_name": "Support access history", "@sage/xtrem-authorization/nodes__support_access_history__property__endTime": "End time", "@sage/xtrem-authorization/nodes__support_access_history__property__isReadOnlyAccess": "Is read only access", "@sage/xtrem-authorization/nodes__support_access_history__property__startTime": "Start time", "@sage/xtrem-authorization/nodes__support_access_history__property__status": "Status", "@sage/xtrem-authorization/nodes__support_access_history__property__timeToClose": "Time to close", "@sage/xtrem-authorization/nodes__support_access_history__query__checkSupportAccessOpen": "Check support access open", "@sage/xtrem-authorization/nodes__support_access_history__query__checkSupportAccessOpen__failed": "Check support access open failed.", "@sage/xtrem-authorization/nodes__user__cannot_change_own_rights": "You are not allowed to modify your own authorization group.", "@sage/xtrem-authorization/nodes__user_billing_role__asyncMutation__asyncExport": "Export", "@sage/xtrem-authorization/nodes__user_billing_role__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-authorization/nodes__user_billing_role__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__user_billing_role__node_name": "User billing role", "@sage/xtrem-authorization/nodes__user_billing_role__property__role": "Role", "@sage/xtrem-authorization/nodes__user_billing_role__property__user": "User", "@sage/xtrem-authorization/nodes__user_group__asyncMutation__asyncExport": "Export", "@sage/xtrem-authorization/nodes__user_group__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-authorization/nodes__user_group__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__user_group__node_name": "User group", "@sage/xtrem-authorization/nodes__user_group__property__createdBy": "Created by", "@sage/xtrem-authorization/nodes__user_group__property__createStamp": "Create stamp", "@sage/xtrem-authorization/nodes__user_group__property__group": "Group", "@sage/xtrem-authorization/nodes__user_group__property__isActive": "Is active", "@sage/xtrem-authorization/nodes__user_group__property__updatedBy": "Updated by", "@sage/xtrem-authorization/nodes__user_group__property__updateStamp": "Update stamp", "@sage/xtrem-authorization/nodes__user_group__property__user": "User", "@sage/xtrem-authorization/operation-not-allowed-on-object": "Operation is not allowed on this object", "@sage/xtrem-authorization/package__name": "Sage xtrem authorization", "@sage/xtrem-authorization/pages__group_role_site____objectTypePlural": "Authorization groups", "@sage/xtrem-authorization/pages__group_role_site____objectTypeSingular": "Authorization group", "@sage/xtrem-authorization/pages__group_role_site____subtitle": "Create a new group to grant users activity and data access rights", "@sage/xtrem-authorization/pages__group_role_site____title": "Authorization group", "@sage/xtrem-authorization/pages__group_role_site___id____title": "ID", "@sage/xtrem-authorization/pages__group_role_site__allRoles____title": "Select all roles", "@sage/xtrem-authorization/pages__group_role_site__allSiteGroup____title": "Select all site groups", "@sage/xtrem-authorization/pages__group_role_site__associatedUserSection____title": "Users", "@sage/xtrem-authorization/pages__group_role_site__confirm____title": "Save", "@sage/xtrem-authorization/pages__group_role_site__customSave____title": "Save", "@sage/xtrem-authorization/pages__group_role_site__editGroupRoleSite____title": "Edit authorization group", "@sage/xtrem-authorization/pages__group_role_site__groupInfoBlock____title": "Group information", "@sage/xtrem-authorization/pages__group_role_site__groupList____title": "View authorization groups", "@sage/xtrem-authorization/pages__group_role_site__groupRoleBlock____title": "User roles", "@sage/xtrem-authorization/pages__group_role_site__groupRoles____columns__title__role___id": "ID", "@sage/xtrem-authorization/pages__group_role_site__groupRoles____columns__title__role__description": "Description", "@sage/xtrem-authorization/pages__group_role_site__groupRoles____columns__title__role__id": "ID", "@sage/xtrem-authorization/pages__group_role_site__groupRoles____columns__title__role__name": "Name", "@sage/xtrem-authorization/pages__group_role_site__groupRoles____title": "Roles", "@sage/xtrem-authorization/pages__group_role_site__groupRolesActivities____columns__title__role": "Name", "@sage/xtrem-authorization/pages__group_role_site__groupRolesActivities____columns__title__role___id": "ID", "@sage/xtrem-authorization/pages__group_role_site__groupRolesActivities____columns__title__role__description": "Description", "@sage/xtrem-authorization/pages__group_role_site__groupRolesActivities____columns__title__role__id": "ID", "@sage/xtrem-authorization/pages__group_role_site__groupRolesActivities____dropdownActions__title": "Activities", "@sage/xtrem-authorization/pages__group_role_site__groupRolesActivities____title": "Roles and activities", "@sage/xtrem-authorization/pages__group_role_site__groupRolesMultiReference____columns__title___id": "_id", "@sage/xtrem-authorization/pages__group_role_site__groupRolesMultiReference____columns__title__id": "ID", "@sage/xtrem-authorization/pages__group_role_site__groupRolesMultiReference____columns__title__name": "Name", "@sage/xtrem-authorization/pages__group_role_site__groupRolesMultiReference____lookupDialogTitle": "Select user roles", "@sage/xtrem-authorization/pages__group_role_site__groupSiteBlock____title": "Site groups", "@sage/xtrem-authorization/pages__group_role_site__groupSites____columns__title__siteGroup___id": "ID", "@sage/xtrem-authorization/pages__group_role_site__groupSites____columns__title__siteGroup__id": "ID", "@sage/xtrem-authorization/pages__group_role_site__groupSites____columns__title__siteGroup__name": "Name", "@sage/xtrem-authorization/pages__group_role_site__groupSites____title": "Sites", "@sage/xtrem-authorization/pages__group_role_site__groupSitesMultiReference____columns__title___id": "ID", "@sage/xtrem-authorization/pages__group_role_site__groupSitesMultiReference____columns__title__id": "ID", "@sage/xtrem-authorization/pages__group_role_site__groupSitesMultiReference____columns__title__name": "Name", "@sage/xtrem-authorization/pages__group_role_site__groupSitesMultiReference____lookupDialogTitle": "Select site groups", "@sage/xtrem-authorization/pages__group_role_site__id____title": "ID", "@sage/xtrem-authorization/pages__group_role_site__infoSection____title": "General", "@sage/xtrem-authorization/pages__group_role_site__name____title": "Name", "@sage/xtrem-authorization/pages__group_role_site__rolesActivitiesSection____title": "Roles and activities", "@sage/xtrem-authorization/pages__group_role_site__users____columns__title": "Created by", "@sage/xtrem-authorization/pages__group_role_site__users____columns__title___id": "ID", "@sage/xtrem-authorization/pages__group_role_site__users____columns__title__2": "Updated by", "@sage/xtrem-authorization/pages__group_role_site__users____columns__title__displayName": "User name", "@sage/xtrem-authorization/pages__group_role_site__users____columns__title__isActive": "Active", "@sage/xtrem-authorization/pages__group_role_site__users____title": "Users", "@sage/xtrem-authorization/pages__group_role_site_list____title": "Authorization group list", "@sage/xtrem-authorization/pages__group_role_site_list__addNewGroup____title": "Add group", "@sage/xtrem-authorization/pages__group_role_site_list__groups____columns__title": "Created by", "@sage/xtrem-authorization/pages__group_role_site_list__groups____columns__title___id": "ID", "@sage/xtrem-authorization/pages__group_role_site_list__groups____columns__title__2": "Updated by", "@sage/xtrem-authorization/pages__group_role_site_list__groups____columns__title__id": "ID", "@sage/xtrem-authorization/pages__group_role_site_list__groups____columns__title__name": "Name", "@sage/xtrem-authorization/pages__group_role_site_list__groups____dropdownActions__title": "Edit authorization group", "@sage/xtrem-authorization/pages__group_role_site_list__groups____dropdownActions__title__2": "Delete authorization group", "@sage/xtrem-authorization/pages__group_role_site_list__groups____title": "Authorization group", "@sage/xtrem-authorization/pages__group_role_site_new_group_title": "New authorization group", "@sage/xtrem-authorization/pages__operator_user_panel____title": "Reset operator code", "@sage/xtrem-authorization/pages__operator_user_panel__cancel____title": "Cancel", "@sage/xtrem-authorization/pages__operator_user_panel__confirm____title": "Confirm", "@sage/xtrem-authorization/pages__operator_user_panel__operatorCodeBlock____title": "Operator code block", "@sage/xtrem-authorization/pages__operator_user_panel__operatorCodeInput____title": "New operator code", "@sage/xtrem-authorization/pages__operator_user_panel__operatorCodeSection____title": "Operator code section", "@sage/xtrem-authorization/pages__role_detail____subtitle": "Role", "@sage/xtrem-authorization/pages__role_detail____title": "Role", "@sage/xtrem-authorization/pages__role_detail__activitySection____title": "Activities", "@sage/xtrem-authorization/pages__role_detail__cancel____title": "Cancel", "@sage/xtrem-authorization/pages__role_detail__confirm____title": "Save", "@sage/xtrem-authorization/pages__role_detail__not_updated": "Permissions update failed.", "@sage/xtrem-authorization/pages__role_detail_updated": "Permissions updated.", "@sage/xtrem-authorization/pages__role_list____title": "Roles", "@sage/xtrem-authorization/pages__role_list__addNewRole____title": "Add role", "@sage/xtrem-authorization/pages__role_list__duplicate": "Duplicate", "@sage/xtrem-authorization/pages__role_list__id____title": "ID", "@sage/xtrem-authorization/pages__role_list__name____title": "Name", "@sage/xtrem-authorization/pages__role_list__roles____columns__title___id": "ID", "@sage/xtrem-authorization/pages__role_list__roles____columns__title__createdByUserAndStamp": "Created by", "@sage/xtrem-authorization/pages__role_list__roles____columns__title__id": "ID", "@sage/xtrem-authorization/pages__role_list__roles____columns__title__isBillingRole": "Is billing role", "@sage/xtrem-authorization/pages__role_list__roles____columns__title__name": "Name", "@sage/xtrem-authorization/pages__role_list__roles____columns__title__updateByUserAndStamp": "Updated by", "@sage/xtrem-authorization/pages__role_list__roles____dropdownActions__title": "Edit role", "@sage/xtrem-authorization/pages__role_list__roles____dropdownActions__title__2": "Delete role", "@sage/xtrem-authorization/pages__role_list__roles____dropdownActions__title__3": "Duplicate", "@sage/xtrem-authorization/pages__role_list__rolesBlock____title": "Role lines", "@sage/xtrem-authorization/pages__role_list__roleSection____title": "Role", "@sage/xtrem-authorization/pages__role_setup": "Role created", "@sage/xtrem-authorization/pages__role_setup____title": "New role", "@sage/xtrem-authorization/pages__role_setup__cancel____title": "Cancel", "@sage/xtrem-authorization/pages__role_setup__confirm____title": "Save", "@sage/xtrem-authorization/pages__role_setup__duplicated_id": "The entered ID already exists. Select another ID.", "@sage/xtrem-authorization/pages__role_setup__id____title": "ID", "@sage/xtrem-authorization/pages__role_setup__name____title": "Name", "@sage/xtrem-authorization/pages__role_setup__next____title": "Continue", "@sage/xtrem-authorization/pages__role_setup__packageSelect____title": "Packages", "@sage/xtrem-authorization/pages__role_setup__roleBlock____title": "Role information", "@sage/xtrem-authorization/pages__role_setup_failed": "Role creation failed.", "@sage/xtrem-authorization/pages__site_group____subtitle": "Site group", "@sage/xtrem-authorization/pages__site_group____title": "Site group", "@sage/xtrem-authorization/pages__site_group___id____title": "ID", "@sage/xtrem-authorization/pages__site_group__associatedAuthorizationGroupSection____title": "User groups", "@sage/xtrem-authorization/pages__site_group__chartBlock____title": "Organization", "@sage/xtrem-authorization/pages__site_group__confirm____title": "Save", "@sage/xtrem-authorization/pages__site_group__customSave____title": "Save", "@sage/xtrem-authorization/pages__site_group__editSiteGroup____title": "Edit site group", "@sage/xtrem-authorization/pages__site_group__generalBlock____title": "Site group", "@sage/xtrem-authorization/pages__site_group__generalSection____title": "General", "@sage/xtrem-authorization/pages__site_group__groups____columns__title": "Created by", "@sage/xtrem-authorization/pages__site_group__groups____columns__title___id": "ID", "@sage/xtrem-authorization/pages__site_group__groups____columns__title__2": "Updated by", "@sage/xtrem-authorization/pages__site_group__groups____columns__title__id": "ID", "@sage/xtrem-authorization/pages__site_group__groups____columns__title__name": "Name", "@sage/xtrem-authorization/pages__site_group__groups____title": "Authorization group", "@sage/xtrem-authorization/pages__site_group__hierarchyChartContent____title": "Organization", "@sage/xtrem-authorization/pages__site_group__id____title": "ID", "@sage/xtrem-authorization/pages__site_group__isActive____title": "Active", "@sage/xtrem-authorization/pages__site_group__isLegalCompany____title": "Legal company", "@sage/xtrem-authorization/pages__site_group__name____title": "Name", "@sage/xtrem-authorization/pages__site_group__siteGroupList____title": "Site group list", "@sage/xtrem-authorization/pages__site_group__siteGroups____columns__title__siteGroup___id": "ID", "@sage/xtrem-authorization/pages__site_group__siteGroups____columns__title__siteGroup__id": "ID", "@sage/xtrem-authorization/pages__site_group__siteGroups____columns__title__siteGroup__name": "Name", "@sage/xtrem-authorization/pages__site_group__siteGroupsBlock____title": "Site groups", "@sage/xtrem-authorization/pages__site_group__siteGroupsMultiReference____columns__title___id": "_id", "@sage/xtrem-authorization/pages__site_group__siteGroupsMultiReference____columns__title__id": "ID", "@sage/xtrem-authorization/pages__site_group__siteGroupsMultiReference____columns__title__name": "Name", "@sage/xtrem-authorization/pages__site_group__siteGroupsMultiReference____lookupDialogTitle": "Select site groups", "@sage/xtrem-authorization/pages__site_group__sites____columns__title__site___id": "ID", "@sage/xtrem-authorization/pages__site_group__sites____columns__title__site__id": "ID", "@sage/xtrem-authorization/pages__site_group__sites____columns__title__site__name": "Name", "@sage/xtrem-authorization/pages__site_group__sitesBlock____title": "Sites", "@sage/xtrem-authorization/pages__site_group__sitesMultiReference____columns__title___id": "_id", "@sage/xtrem-authorization/pages__site_group__sitesMultiReference____columns__title__id": "ID", "@sage/xtrem-authorization/pages__site_group__sitesMultiReference____columns__title__name": "Name", "@sage/xtrem-authorization/pages__site_group__sitesMultiReference____lookupDialogTitle": "Select sites", "@sage/xtrem-authorization/pages__site_group__toggleChart____helperText": "Allow user to toggle grid or chart display", "@sage/xtrem-authorization/pages__site_group__toggleChart____title": "Display options", "@sage/xtrem-authorization/pages__site_group__toggleChartBlock____title": "Toggle chart/grid", "@sage/xtrem-authorization/pages__site_group_chart": "Chart", "@sage/xtrem-authorization/pages__site_group_grid": "Grid", "@sage/xtrem-authorization/pages__site_group_list____title": "Site groups", "@sage/xtrem-authorization/pages__site_group_list__createSite____title": "Create site", "@sage/xtrem-authorization/pages__site_group_list__createSiteGroup____title": "Create site group", "@sage/xtrem-authorization/pages__site_group_list__fieldBlock____title": "Site group", "@sage/xtrem-authorization/pages__site_group_list__groupList____title": "Site group list", "@sage/xtrem-authorization/pages__site_group_list__section____title": "Site groups", "@sage/xtrem-authorization/pages__site_group_list__siteGroups____levels__columns__title___id": "ID", "@sage/xtrem-authorization/pages__site_group_list__siteGroups____levels__columns__title__id": "ID", "@sage/xtrem-authorization/pages__site_group_list__siteGroups____levels__columns__title__id__2": "ID", "@sage/xtrem-authorization/pages__site_group_list__siteGroups____levels__columns__title__name": "Name", "@sage/xtrem-authorization/pages__site_group_list__siteGroups____levels__columns__title__name__2": "Name", "@sage/xtrem-authorization/pages__site_group_list__siteGroups____levels__dropdownActions__title": "Edit site group", "@sage/xtrem-authorization/pages__site_group_list__siteGroups____levels__dropdownActions__title__2": "Delete site group", "@sage/xtrem-authorization/pages__site_group_new_title": "New site group", "@sage/xtrem-authorization/pages__support_access_history____title": "Support access", "@sage/xtrem-authorization/pages__support_access_history__accessHistory____columns__title___id": "ID", "@sage/xtrem-authorization/pages__support_access_history__accessHistory____columns__title__endTime": "End time", "@sage/xtrem-authorization/pages__support_access_history__accessHistory____columns__title__isReadOnlyAccess": "Read-only access", "@sage/xtrem-authorization/pages__support_access_history__accessHistory____columns__title__startTime": "Start time", "@sage/xtrem-authorization/pages__support_access_history__accessHistory____columns__title__status": "Status", "@sage/xtrem-authorization/pages__support_access_history__accessHistory____title": "Support access history", "@sage/xtrem-authorization/pages__support_access_history__allow_access_button_text": "Allow access", "@sage/xtrem-authorization/pages__support_access_history__description____content": "Specify how long access should be granted from the current time.", "@sage/xtrem-authorization/pages__support_access_history__dummy____content": "", "@sage/xtrem-authorization/pages__support_access_history__extend_access_button_text": "Extend access", "@sage/xtrem-authorization/pages__support_access_history__forTime____title": "Number", "@sage/xtrem-authorization/pages__support_access_history__infoSection____title": "General", "@sage/xtrem-authorization/pages__support_access_history__isReadOnlyAccess____title": "Read-only access", "@sage/xtrem-authorization/pages__support_access_history__isSupportAccessOpen____title": "Support access open", "@sage/xtrem-authorization/pages__support_access_history__please_fill_the_number_and_unit_field": "Enter the number and units fields.", "@sage/xtrem-authorization/pages__support_access_history__revoke_access_button_text": "Revoke access", "@sage/xtrem-authorization/pages__support_access_history__supportAccessBlock____title": "Allow Sage support access", "@sage/xtrem-authorization/pages__support_access_history__supportAccessHistoryBlock____title": "Support access history", "@sage/xtrem-authorization/pages__support_access_history__unit____title": "Units", "@sage/xtrem-authorization/pages__user____navigationPanel__bulkActions__title": "Send welcome mail", "@sage/xtrem-authorization/pages__user____navigationPanel__listItem__image__title": "Image", "@sage/xtrem-authorization/pages__user____navigationPanel__listItem__line2__title": "Email", "@sage/xtrem-authorization/pages__user____navigationPanel__listItem__line3__title": "API user", "@sage/xtrem-authorization/pages__user____navigationPanel__listItem__title__title": "First name", "@sage/xtrem-authorization/pages__user____navigationPanel__listItem__titleRight__title": "Last name", "@sage/xtrem-authorization/pages__user____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-authorization/pages__user____navigationPanel__optionsMenu__title__2": "Interactive", "@sage/xtrem-authorization/pages__user____navigationPanel__optionsMenu__title__3": "Third-Party", "@sage/xtrem-authorization/pages__user____objectTypePlural": "Users", "@sage/xtrem-authorization/pages__user____objectTypeSingular": "User", "@sage/xtrem-authorization/pages__user____subtitle": "User", "@sage/xtrem-authorization/pages__user____title": "Users", "@sage/xtrem-authorization/pages__user___id____title": "ID", "@sage/xtrem-authorization/pages__user__associatedRoles____columns__title___id": "ID", "@sage/xtrem-authorization/pages__user__associatedRoles____columns__title__createdBy": "Created by", "@sage/xtrem-authorization/pages__user__associatedRoles____columns__title__id": "ID", "@sage/xtrem-authorization/pages__user__associatedRoles____columns__title__name": "Name", "@sage/xtrem-authorization/pages__user__associatedRoles____columns__title__updatedBy": "Updated by", "@sage/xtrem-authorization/pages__user__associatedRoles____title": "Roles and activities", "@sage/xtrem-authorization/pages__user__associatedSiteGroups____columns__title___id": "ID", "@sage/xtrem-authorization/pages__user__associatedSiteGroups____columns__title__createdBy": "Created by", "@sage/xtrem-authorization/pages__user__associatedSiteGroups____columns__title__id": "ID", "@sage/xtrem-authorization/pages__user__associatedSiteGroups____columns__title__name": "Name", "@sage/xtrem-authorization/pages__user__associatedSiteGroups____columns__title__updatedBy": "Updated by", "@sage/xtrem-authorization/pages__user__associatedSiteGroups____title": "Site groups", "@sage/xtrem-authorization/pages__user__associatedSiteGroupsSection____title": "Site groups", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__columns__group__id__title": "ID", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__columns__group__id__title__2": "Name", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__columns__group__id__title__3": "ID", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__title__group___id": "_id", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__title__group__groupRolesDisplay": "Roles", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__title__group__groupSitesDisplay": "Site group", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__title__group__id": "ID", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__title__group__name": "Name", "@sage/xtrem-authorization/pages__user__authorizationGroup____title": "Authorization groups", "@sage/xtrem-authorization/pages__user__editUser____title": "Edit user", "@sage/xtrem-authorization/pages__user__email____title": "Email", "@sage/xtrem-authorization/pages__user__firstName____title": "First name", "@sage/xtrem-authorization/pages__user__generalSection____title": "General", "@sage/xtrem-authorization/pages__user__importExportDateFormat____helperText": "Default value will be ", "@sage/xtrem-authorization/pages__user__importExportDateFormat____title": "Import/export date format", "@sage/xtrem-authorization/pages__user__importExportDelimiter____helperText": "Default value will be ", "@sage/xtrem-authorization/pages__user__importExportDelimiter____title": "Import/export delimiter", "@sage/xtrem-authorization/pages__user__isActive____title": "Active", "@sage/xtrem-authorization/pages__user__isAdministrator____title": "Administrator", "@sage/xtrem-authorization/pages__user__isApiUser____title": "API user", "@sage/xtrem-authorization/pages__user__isDemoPersona____title": "Demo persona", "@sage/xtrem-authorization/pages__user__isExternal____title": "External", "@sage/xtrem-authorization/pages__user__isOperatorUser____title": "Pin authentication", "@sage/xtrem-authorization/pages__user__isWelcomeMailSent____title": "Send welcome email", "@sage/xtrem-authorization/pages__user__lastName____title": "Last name", "@sage/xtrem-authorization/pages__user__operatorCode____title": "PIN code", "@sage/xtrem-authorization/pages__user__preferencesBlock____title": "Preferences", "@sage/xtrem-authorization/pages__user__preferencesSection____title": "Preferences", "@sage/xtrem-authorization/pages__user__resetOperatorCodePage____title": "PIN code", "@sage/xtrem-authorization/pages__user__role____columns__title__id": "ID", "@sage/xtrem-authorization/pages__user__role____columns__title__name": "Name", "@sage/xtrem-authorization/pages__user__role____lookupDialogTitle": "Select billing role", "@sage/xtrem-authorization/pages__user__role____title": "Billing Role", "@sage/xtrem-authorization/pages__user__rolesActivitiesSection____title": "Roles and activities", "@sage/xtrem-authorization/pages__user__save____title": "Save", "@sage/xtrem-authorization/pages__user__send_welcome_mail": "Send email", "@sage/xtrem-authorization/pages__user__send_welcome_mail_button": "Send", "@sage/xtrem-authorization/pages__user__send_welcome_mail_dialog_content": "You are about to send a welcome email to the user.", "@sage/xtrem-authorization/pages__user__userAuthorizationInformationBlock____title": "Authorization group", "@sage/xtrem-authorization/pages__user__userGroups____columns__title___id": "ID", "@sage/xtrem-authorization/pages__user__userGroups____columns__title__id": "ID", "@sage/xtrem-authorization/pages__user__userGroups____columns__title__name": "Name", "@sage/xtrem-authorization/pages__user__userGroups____lookupDialogTitle": "Select authorization group", "@sage/xtrem-authorization/pages__user__userGroups____title": "Authorization group", "@sage/xtrem-authorization/pages__user__userInformationBlock____title": "User information", "@sage/xtrem-authorization/pages__user__userList____title": "View users", "@sage/xtrem-authorization/pages__user__userPhotoBlock____title": "Photo", "@sage/xtrem-authorization/pages_role_list_delete_confirmation": "Confirm deletion", "@sage/xtrem-authorization/pages_role_list_delete_message": "Are you sure you would like to delete the {{role}} role ?", "@sage/xtrem-authorization/pages-confirm-cancel": "Cancel", "@sage/xtrem-authorization/permission__create__name": "Create", "@sage/xtrem-authorization/permission__manage__name": "Manage", "@sage/xtrem-authorization/permission__read__name": "Read", "@sage/xtrem-authorization/permission__update__name": "Update", "@sage/xtrem-authorization/reset-operator-code": "Reset", "@sage/xtrem-authorization/role_detail_page_is_not_read_only": "Cancel", "@sage/xtrem-authorization/role_detail_page_is_read_only": "Ok", "@sage/xtrem-authorization/service_options__authorization_service_option__name": "Authorization service option", "@sage/xtrem-authorization/stickers__demo_persona_sticker____title": "<PERSON><PERSON>", "@sage/xtrem-authorization/stickers__demo_persona_sticker__section____title": "<PERSON>a", "@sage/xtrem-authorization/stickers__demo_persona_sticker__selectionPersona____title": "Demo persona"}