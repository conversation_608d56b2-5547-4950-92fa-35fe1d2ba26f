import { GraphApi } from '@sage/xtrem-system-api';
import { Dict } from '@sage/xtrem-shared';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.sticker<DemoPersonaSticker>({
    title: 'Demo Persona',
    icon: 'person',
    isActive() {
        return true;
    },
    access: {
        node: '@sage/xtrem-system/User',
        bind: 'demoPersonas',
    },
    async onOpen() {
        const demoPersonas = (await this.$.graph
            .node('@sage/xtrem-system/User')
            .queries.demoPersonas({ displayName: true, email: true }, false)
            .execute()) as { displayName: string; email: string }[];
        this.selectionPersona.options = demoPersonas.map(node => node.email);
        this.personasDict = demoPersonas.reduce((r, k) => {
            r[k.email] = `${k.displayName} (${k.email})`;
            return r;
        }, {} as Dict<string>);
    },
})
export class DemoPersonaSticker extends ui.Sticker<GraphApi> {
    private personasDict: Dict<string> = {};

    @ui.decorators.section<DemoPersonaSticker>({
        title: 'Persona',
    })
    section: ui.containers.Section;

    @ui.decorators.block<DemoPersonaSticker>({
        parent() {
            return this.section;
        },
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.selectField<DemoPersonaSticker>({
        parent() {
            return this.mainBlock;
        },
        title: 'Demo persona',
        isFullWidth: true,
        isMandatory: true,
        map(value) {
            return this.personasDict[value];
        },
        onChange() {
            this.applyButton.isDisabled = !this.selectionPersona.value;
        },
        access: {
            bind: 'demoPersonas',
            node: '@sage/xtrem-system/User',
        },
    })
    selectionPersona: ui.fields.Select;

    @ui.decorators.block<DemoPersonaSticker>({
        parent() {
            return this.section;
        },
    })
    buttonBlock: ui.containers.Block;

    @ui.decorators.buttonField<DemoPersonaSticker>({
        isDisabled: true,
        map() {
            return 'Apply';
        },
        async onClick() {
            await this.$.graph.raw(
                `mutation{xtremSystem{user{setDemoPersona(email:"${this.selectionPersona.value}")}}}`,
            );
            this.$.finish();
            // request a hard refresh to reload the full window and then update profile details, navigation menu, etc.
            this.$.router.hardRefresh();
        },
        parent() {
            return this.buttonBlock;
        },
    })
    applyButton: ui.fields.Button;
}
