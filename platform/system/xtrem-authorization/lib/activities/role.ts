import { Activity } from '@sage/xtrem-core';
import { Activity as ActivityGrant, Role, RoleActivity } from '../nodes';

export const role = new Activity({
    description: 'Role',
    node: () => Role,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        read: [
            { operations: ['all'], on: [() => Role] },
            { operations: ['lookup'], on: [() => ActivityGrant] },
        ],

        manage: [
            { operations: ['create', 'update', 'delete', 'all'], on: [() => Role] },
            { operations: ['roleActivitiesCreateUpdate'], on: [() => RoleActivity] },
            { operations: ['lookup'], on: [() => ActivityGrant] },
        ],
    },
});
