import { Activity } from '@sage/xtrem-core';
import { SupportAccessHistory } from '../nodes/support-access-history';

export const supportAccessHistory = new Activity({
    description: 'Support access history',
    node: () => SupportAccessHistory,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        read: [{ operations: ['checkSupportAccessOpen'], on: [() => SupportAccessHistory] }],
        manage: [
            {
                operations: ['allowAccess', 'extendAccess', 'revokeAccess'],
                on: [() => SupportAccessHistory],
            },
        ],
    },
});
