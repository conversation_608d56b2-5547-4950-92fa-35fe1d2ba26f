import { Activity } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';

const { User } = xtremSystem.nodes;

export const user = new Activity({
    description: 'User',
    node: () => User,
    __filename,
    permissions: ['read', 'create', 'update'],
    operationGrants: {
        read: [{ operations: ['all'], on: [() => User] }],
        create: [{ operations: ['all', 'sendBulkWelcomeMail'], on: [() => User] }],
        update: [{ operations: ['all', 'sendBulkWelcomeMail'], on: [() => User] }],
    },
});
