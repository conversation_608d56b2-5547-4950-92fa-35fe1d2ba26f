import { Activity } from '@sage/xtrem-core';
import { SiteGroup, SiteGroupToSite, SiteGroupToSiteGroup, GroupRoleSite } from '../nodes';
import * as xtremSystem from '@sage/xtrem-system';

const { User, Site, Company } = xtremSystem.nodes;

export const siteGroup = new Activity({
    description: 'Site groups',
    node: () => SiteGroup,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        read: [{ operations: ['lookup'], on: [() => SiteGroupToSite, () => SiteGroupToSiteGroup, () => User] }],
        manage: [
            { operations: ['create', 'update', 'delete'], on: [() => SiteGroup] },
            {
                operations: ['lookup'],
                on: [
                    () => SiteGroupToSite,
                    () => SiteGroupToSiteGroup,
                    () => User,
                    () => GroupRoleSite,
                    () => Site,
                    () => Company,
                ],
            },
        ],
    },
});
