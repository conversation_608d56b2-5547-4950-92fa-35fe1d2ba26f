import { Graph<PERSON><PERSON> } from '@sage/xtrem-authorization-api';
import * as ui from '@sage/xtrem-ui';
import * as utils from '../client-functions/utils';
import { userData } from '../menu-items/user-data';

/**
 * for all role query // duplicate of interface common roles
 */
export interface AllRoles {
    _id: string;
    name: string;
    id: string;
    isBillingRole: boolean;
    createdBy: string;
    createStamp: string;
    updatedBy: string;
    updateStamp: string;
    isVendor: string;
}
@ui.decorators.page<RoleList>({
    module: 'system',
    isTransient: true,
    title: 'Roles',
    menuItem: userData,
    priority: 200,
    access: { node: '@sage/xtrem-authorization/Role' },
    async onLoad() {
        await this.populateRoleList();

        if (this.$.isDirty) {
            this.$.setPageClean();
        }
    },
    businessActions() {
        return [this.addNewRole];
    },
    onError(error: string | (Error & { errors: Array<any> })) {
        return utils.formatError(this, error);
    },
})
export class RoleList extends ui.Page<GraphApi> {
    @ui.decorators.section<RoleList>({
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<RoleList>({
        parent() {
            return this.section;
        },
        title: 'Role lines',
        isTitleHidden: true,
    })
    rolesBlock: ui.containers.Block;

    @ui.decorators.tableField<RoleList>({
        pageSize: 20,
        hasSearchBoxMobile: true,
        isTitleHidden: true,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'ID',
                isHidden: true,
                isHiddenDesktop: true,
                isReadOnly: true,
            }),
            ui.nestedFields.link({
                title: 'Name',
                bind: 'name',
                onClick(_id, rowData: AllRoles) {
                    return this.openRoleDetailDialog(_id, rowData.name, rowData.isVendor);
                },
            }),
            ui.nestedFields.text({
                bind: 'id',
                title: 'ID',
                isReadOnly: true,
            }),
            ui.nestedFields.checkbox({
                bind: 'isBillingRole',
                title: 'Is billing role',
                isReadOnly: true,
            }),
            ui.nestedFields.label({
                bind: 'createdByUserAndStamp',
                map(_fieldValue, rowData) {
                    return `${rowData.createdBy || ''} ${rowData.createStamp || ''}`;
                },
                isTransient: true,
                title: 'Created by',
            }),
            ui.nestedFields.label({
                bind: 'updateByUserAndStamp',
                map(_fieldValue, rowData) {
                    return `${rowData.updatedBy || ''} ${rowData.updateStamp || ''}`;
                },
                isTransient: true,
                title: 'Updated by',
            }),
            ui.nestedFields.technical({ bind: 'isVendor' }),
        ],
        orderBy: {
            _id: 1,
        },
        parent() {
            return this.rolesBlock;
        },
        dropdownActions: [
            {
                icon: 'edit',
                title: 'Edit role',
                onClick(id, rowData: AllRoles) {
                    return this.openRoleDetailDialog(id, rowData.name, rowData.isVendor);
                },
                isDisabled(id, rowData: AllRoles) {
                    return rowData.isVendor === '1';
                },
            },
            {
                icon: 'delete',
                title: 'Delete role',
                isDisabled(id, rowData: AllRoles) {
                    return rowData.isVendor === '1';
                },
                async onClick(_rowId: string, rowData: AllRoles) {
                    const options: ui.dialogs.DialogOptions = {
                        acceptButton: {
                            text: 'Yes',
                        },
                        cancelButton: {
                            text: 'No',
                        },
                    };
                    if (
                        await this.$.dialog
                            .confirmation(
                                'warn',
                                ui.localize(
                                    '@sage/xtrem-authorization/pages_role_list_delete_confirmation',
                                    'Confirm deletion',
                                ),
                                ui.localize(
                                    '@sage/xtrem-authorization/pages_role_list_delete_message',
                                    'Are you sure you would like to delete the {{role}} role ?',
                                    { role: rowData.name },
                                ),
                                options,
                            )
                            .then(() => true)
                            .catch(() => false)
                    ) {
                        this.$.loader.display();
                        try {
                            await this.$.graph.delete({
                                _id: rowData._id,
                                nodeName: '@sage/xtrem-authorization/Role',
                            });
                            this.roles.removeRecord(_rowId);
                            this.$.showToast(
                                ui.localize('@sage/xtrem-authorization/delete-confirmation', 'Record deleted'),
                                { type: 'success' },
                            );
                        } catch (e) {
                            this.$.showToast(e.message, { timeout: 0, type: 'error' });
                        }
                        this.$.setPageClean();
                        this.$.loader.hide();
                    }
                },
            },

            {
                icon: 'duplicate',
                title: 'Duplicate',
                async onClick(_rowId: string, rowData: AllRoles) {
                    this._id.value = Number(rowData._id);
                    this.roleSection.isHidden = false;
                    await this.$.dialog.custom('info', this.roleSection, {
                        cancelButton: { isHidden: true },
                        acceptButton: { isHidden: true },
                        resolveOnCancel: true,
                    });
                    this.roleSection.isHidden = true;
                },
            },
        ],
    })
    roles: ui.fields.Table<AllRoles>;

    @ui.decorators.section<RoleList>({
        title: 'Role',
        isHidden: true,
    })
    roleSection: ui.containers.Section;

    @ui.decorators.block<RoleList>({
        parent() {
            return this.roleSection;
        },
    })
    roleBlock: ui.containers.Block;

    @ui.decorators.numericField<RoleList>({
        isHidden: true,
    })
    _id: ui.fields.Numeric;

    @ui.decorators.textField<RoleList>({
        parent() {
            return this.roleBlock;
        },
        title: 'ID',
        isMandatory: true,
    })
    id: ui.fields.Text;

    @ui.decorators.textField<RoleList>({
        parent() {
            return this.roleBlock;
        },
        title: 'Name',
        isMandatory: true,
    })
    name: ui.fields.Text;

    @ui.decorators.buttonField<RoleList>({
        parent() {
            return this.roleBlock;
        },
        map() {
            return ui.localize('@sage/xtrem-authorization/pages__role_list__duplicate', 'Duplicate');
        },
        async onClick() {
            const validation = await this.$.page.validate();
            if (!validation.length) {
                await this.$.graph.duplicate({
                    nodeName: '@sage/xtrem-authorization/Role',
                    _id: this._id.value,
                    values: { id: this.id.value, name: this.name.value },
                });
                this.roleSection.isHidden = true;

                this.$.showToast(
                    ui.localize('@sage/xtrem-authorization/duplication-confirmation', 'Record duplicated'),
                    { type: 'success' },
                );

                this.$.setPageClean();
                this.$.router.refresh();
            }
        },
    })
    duplicate: ui.fields.Button;

    @ui.decorators.pageAction<RoleList>({
        title: 'Add role',
        isHidden: false,
        isDisabled: false,
        onError() {
            // Intentionally left empty
        },
        async onClick() {
            await this.openRoleSetupDialog();
            this.$.setPageClean();
            this.$.router.refresh();
        },
    })
    addNewRole: ui.PageAction;

    /**
     * Populate the roles table
     */
    private async populateRoleList() {
        this.roles.value = (await this.$.graph
            .node('@sage/xtrem-authorization/Role')
            .queries.all(
                {
                    _id: true,
                    name: true,
                    id: true,
                    isBillingRole: true,
                    createdBy: true,
                    updatedBy: true,
                    createStamp: true,
                    updateStamp: true,
                    isVendor: true,
                },
                { isActive: true },
            )
            .execute()) as AllRoles[];
    }

    private async openRoleDetailDialog(roleSysId: string, roleName: string, isVendor: string) {
        return this.$.dialog.page(
            '@sage/xtrem-authorization/RoleDetail',
            { roleSysId, roleName, isReadOnly: isVendor },
            {
                size: 'extra-large',
                resolveOnCancel: true,
            },
        );
    }

    private async openRoleSetupDialog() {
        return this.$.dialog.page(
            '@sage/xtrem-authorization/RoleSetup',
            {
                roleSysId: 0,
                roleName: '',
            },
            {
                rightAligned: true,
                size: 'extra-large',
                resolveOnCancel: true,
            },
        );
    }
}
