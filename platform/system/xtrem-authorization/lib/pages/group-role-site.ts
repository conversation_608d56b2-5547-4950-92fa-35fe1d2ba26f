import { Graph<PERSON><PERSON> } from '@sage/xtrem-authorization-api';
import { withoutEdges } from '@sage/xtrem-client';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
    setOrderOfPageBusinessActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<GroupRoleSite>({
    module: 'system',
    title: 'Authorization group',
    objectTypeSingular: 'Authorization group',
    objectTypePlural: 'Authorization groups',
    subtitle: 'Create a new group to grant users activity and data access rights',
    node: '@sage/xtrem-authorization/GroupRoleSite',
    mode: 'tabs',
    createAction() {
        return this.$standardNewAction;
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction],
        });
    },
    headerQuickActions() {
        return [this.$standardDuplicateAction];
    },
    businessActions() {
        return setOrderOfPageBusinessActions({
            save: this.customSave,
            cancel: this.$standardCancelAction,
            businessActions: [this.confirm, this.groupList, this.editGroupRoleSite],
        });
    },

    async onLoad() {
        this.groupSitesMultiReference.value = this.groupSites.value.map(element => {
            return { _id: element.siteGroup._id, id: element.siteGroup.id, name: element.siteGroup.name };
        });

        this.groupRolesMultiReference.value = this.groupRoles.value.map(element => {
            return { _id: element.role._id, id: element.role.id, name: element.role.name };
        });

        this.groupRolesActivities.value = this.groupRoles.value;

        if (this._id) {
            this.groupRoles.isHidden = false;
            this.groupSites.isHidden = false;
        }

        if (this.$.queryParameters.page === 'detailGroupRoleSite') {
            this.users.isDisabled = false;
            this.users.isHidden = false;
            await this.setAuthorizationGroupList(this._id.value);
        } else {
            setApplicativePageCrudActions({ page: this, save: this.customSave, cancel: this.$standardCancelAction });
        }

        if (
            // TODO - Error on build when includes is used instead of || operator
            // ['userListPanelDetail', 'userListPanelNewUser', 'editUser'].includes(this.$.queryParameters.page)
            this.$.queryParameters.page === 'newGroupRoleSite' ||
            this.$.queryParameters.page === 'detailGroupRoleSite' ||
            this.$.queryParameters.page === 'editGroupRoleSite'
        ) {
            this.setPanel(this.$.queryParameters.page);
        } else {
            setApplicativePageCrudActions({ page: this, save: this.customSave, cancel: this.$standardCancelAction });
        }
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.customSave,
            cancel: this.$standardCancelAction,
        });
    },
})
export class GroupRoleSite extends ui.Page<GraphApi> {
    @ui.decorators.section<GroupRoleSite>({
        isTitleHidden: true,
        isOpen: true,
        title: 'General',
    })
    infoSection: ui.containers.Section;

    @ui.decorators.block<GroupRoleSite>({
        parent() {
            return this.infoSection;
        },
        title: 'Group information',
        width: 'extra-large',
    })
    groupInfoBlock: ui.containers.Block;

    @ui.decorators.textField<GroupRoleSite>({
        parent() {
            return this.groupInfoBlock;
        },

        title: 'ID',
        isHidden: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.textField<GroupRoleSite>({
        parent() {
            return this.groupInfoBlock;
        },
        title: 'ID',
        maxLength: 24,
        isMandatory: true,
    })
    id: ui.fields.Text;

    @ui.decorators.textField<GroupRoleSite>({
        parent() {
            return this.groupInfoBlock;
        },
        title: 'Name',
        maxLength: 250,
        isMandatory: true,
        width: 'large',
    })
    name: ui.fields.Text;

    @ui.decorators.block<GroupRoleSite>({
        parent() {
            return this.infoSection;
        },
        title: 'User roles',
        isTitleHidden: false,
        width: 'small',
    })
    groupRoleBlock: ui.containers.Block;

    @ui.decorators.block<GroupRoleSite>({
        parent() {
            return this.infoSection;
        },
        title: 'Site groups',
        isTitleHidden: false,
        width: 'small',
    })
    groupSiteBlock: ui.containers.Block;

    @ui.decorators.checkboxField<GroupRoleSite>({
        isTransient: true,
        parent() {
            return this.groupRoleBlock;
        },
        title: 'Select all roles',
        async onChange() {
            if (this.allRoles.value) {
                await this.populateGroupRoleTableAll();
                await this.clearGroupRolesTable();
                await this.populateGroupRolesTable();
            } else {
                this.groupRolesMultiReference.value = [];
                this.clearGroupRolesTable();
            }

            this.groupRolesMultiReference.isReadOnly = this.allRoles.value;
        },
    })
    allRoles: ui.fields.Checkbox;

    @ui.decorators.multiReferenceField<GroupRoleSite>({
        isTransient: true,
        lookupDialogTitle: 'Select user roles',
        minLookupCharacters: 0,
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Name', canFilter: true }),
            ui.nestedFields.text({ bind: 'id', title: 'ID', canFilter: true }),
            ui.nestedFields.text({ bind: '_id', title: '_id', isHidden: true }),
        ],
        node: '@sage/xtrem-authorization/Role',
        onClick() {},
        parent() {
            return this.groupRoleBlock;
        },
        isFullWidth: true,
        valueField: 'name',
        helperTextField: 'id',
        orderBy: { _id: 1 },
        onChange() {
            this.clearGroupRolesTable();
            this.populateGroupRolesTable();
        },
    })
    groupRolesMultiReference: ui.fields.MultiReference;

    @ui.decorators.tableField<GroupRoleSite>({
        node: '@sage/xtrem-authorization/GroupRole',
        canSelect: false,
        title: 'Roles',
        isHidden: true,
        isChangeIndicatorDisabled: true,
        orderBy: { _id: 1 },
        parent() {
            return this.groupRoleBlock;
        },
        columns: [
            ui.nestedFields.reference<GroupRoleSite>({
                node: '@sage/xtrem-authorization/Role',
                bind: 'role',
                title: 'ID',
                isHidden: true,
                isReadOnly: true,
                valueField: '_id',
            }),
            ui.nestedFields.reference<GroupRoleSite>({
                node: '@sage/xtrem-authorization/Role',
                bind: 'role',
                title: 'Name',
                isHidden: false,
                isReadOnly: true,
                valueField: 'name',
            }),
            ui.nestedFields.reference<GroupRoleSite>({
                node: '@sage/xtrem-authorization/Role',
                bind: 'role',
                title: 'ID',
                isHidden: false,
                isReadOnly: true,
                valueField: 'id',
            }),
            ui.nestedFields.reference<GroupRoleSite>({
                node: '@sage/xtrem-authorization/Role',
                bind: 'role',
                title: 'Description',
                isHidden: false,
                isReadOnly: true,
                valueField: 'description',
            }),
        ],
    })
    groupRoles: ui.fields.Table;

    @ui.decorators.checkboxField<GroupRoleSite>({
        isTransient: true,
        parent() {
            return this.groupSiteBlock;
        },
        title: 'Select all site groups',
        async onChange() {
            if (this.allSiteGroup.value) {
                await this.populateGroupSitesTableAll();
                await this.clearGroupSitesTable();
                await this.populateGroupSitesTable();
            } else {
                this.groupSitesMultiReference.value = [];
                this.clearGroupSitesTable();
            }

            this.groupSitesMultiReference.isReadOnly = this.allSiteGroup.value;
        },
    })
    allSiteGroup: ui.fields.Checkbox;

    @ui.decorators.multiReferenceField<GroupRoleSite>({
        isTransient: true,
        lookupDialogTitle: 'Select site groups',
        minLookupCharacters: 0,
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Name', canFilter: true }),
            ui.nestedFields.text({ bind: 'id', title: 'ID', canFilter: true }),
            ui.nestedFields.text({ bind: '_id', title: 'ID', isHidden: true }),
        ],
        node: '@sage/xtrem-authorization/SiteGroup',
        onClick() {},
        parent() {
            return this.groupSiteBlock;
        },
        isFullWidth: true,
        valueField: 'name',
        helperTextField: 'id',
        onChange() {
            this.clearGroupSitesTable();
            this.populateGroupSitesTable();
        },
    })
    groupSitesMultiReference: ui.fields.MultiReference;

    @ui.decorators.tableField<GroupRoleSite>({
        node: '@sage/xtrem-authorization/GroupSite',
        canSelect: false,
        title: 'Sites',
        isHidden: true,
        isChangeIndicatorDisabled: true,
        orderBy: { _id: 1 },
        parent() {
            return this.groupSiteBlock;
        },
        columns: [
            ui.nestedFields.reference<GroupRoleSite>({
                node: '@sage/xtrem-authorization/SiteGroup',
                bind: 'siteGroup',
                title: 'ID',
                isHidden: true,
                isReadOnly: true,
                valueField: '_id',
            }),
            ui.nestedFields.reference<GroupRoleSite>({
                node: '@sage/xtrem-authorization/SiteGroup',
                bind: 'siteGroup',
                title: 'Name',
                isHidden: false,
                isReadOnly: true,
                valueField: 'name',
            }),
            ui.nestedFields.reference<GroupRoleSite>({
                node: '@sage/xtrem-authorization/SiteGroup',
                bind: 'siteGroup',
                title: 'ID',
                isHidden: false,
                isReadOnly: true,
                valueField: 'id',
            }),
        ],
    })
    groupSites: ui.fields.Table;

    @ui.decorators.pageAction<GroupRoleSite>({
        title: 'Save',
        async onClick() {
            this.groupRoles.isHidden = false;
            this.groupSites.isHidden = false;
            await this.$standardSaveAction.execute();
        },
    })
    customSave: ui.PageAction;

    @ui.decorators.pageAction<GroupRoleSite>({
        title: 'Save',
        isHidden: true,
        isDisabled: true,
        onClick() {
            return this.$standardSaveAction.execute(true);
        },
    })
    confirm: ui.PageAction;

    @ui.decorators.pageAction<GroupRoleSite>({
        title: 'View authorization groups',
        isHidden: true,
        isDisabled: true,
        onClick() {
            this.$.router.goTo('@sage/xtrem-authorization/GroupRoleSiteList', {});
        },
    })
    groupList: ui.PageAction;

    @ui.decorators.pageAction<GroupRoleSite>({
        title: 'Edit authorization group',
        isHidden: true,
        isDisabled: true,
        onClick() {
            this.$.router.goTo('@sage/xtrem-authorization/GroupRoleSite', {
                _id: this._id.value,
                page: 'editGroupRoleSite',
            });
        },
    })
    editGroupRoleSite: ui.PageAction;

    @ui.decorators.section<GroupRoleSite>({
        isTitleHidden: true,
        isOpen: true,
        title: 'Roles and activities',
    })
    rolesActivitiesSection: ui.containers.Section;

    @ui.decorators.tableField<GroupRoleSite>({
        node: '@sage/xtrem-authorization/GroupRole',
        title: 'Roles and activities',
        canSelect: false,
        orderBy: { _id: 1 },
        isTransient: true,
        parent() {
            return this.rolesActivitiesSection;
        },
        columns: [
            ui.nestedFields.reference<GroupRoleSite>({
                node: '@sage/xtrem-authorization/Role',
                bind: 'role',
                title: 'ID',
                isHidden: true,
                isReadOnly: true,
                valueField: '_id',
            }),
            ui.nestedFields.link({
                bind: 'role',
                title: 'Name',
                map(value: any, rowData: any) {
                    return String(rowData.role.name);
                },
                async onClick(_id, rowData: any) {
                    return this.openRoleDetailDialog(rowData.role._id, rowData.role.name);
                },
            }),
            ui.nestedFields.reference<GroupRoleSite>({
                node: '@sage/xtrem-authorization/Role',
                bind: 'role',
                title: 'ID',
                isHidden: false,
                isReadOnly: true,
                valueField: 'id',
            }),
            ui.nestedFields.reference<GroupRoleSite>({
                node: '@sage/xtrem-authorization/Role',
                bind: 'role',
                title: 'Description',
                isHidden: false,
                isReadOnly: true,
                valueField: 'description',
            }),
        ],
        dropdownActions: [
            {
                icon: 'lookup',
                title: 'Activities',
                async onClick(id, rowData: any) {
                    return this.openRoleDetailDialog(rowData.role._id, rowData.role.name);
                },
            },
        ],
    })
    groupRolesActivities: ui.fields.Table;

    @ui.decorators.section<GroupRoleSite>({
        isTitleHidden: true,
        isOpen: true,
        title: 'Users',
    })
    associatedUserSection: ui.containers.Section;

    @ui.decorators.tableField<GroupRoleSite>({
        pageSize: 20,
        title: 'Users',
        node: '@sage/xtrem-authorization/UserGroup',
        hasSearchBoxMobile: true,
        isTransient: true,
        isDisabled: true,
        isHidden: true,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'ID',
                isHidden: true,
                isHiddenDesktop: true,
                isReadOnly: true,
            }),
            ui.nestedFields.text<GroupRoleSite>({
                bind: 'displayName',
                title: 'User name',
                isReadOnly: true,
            }),
            ui.nestedFields.checkbox<GroupRoleSite>({
                bind: 'isActive',
                title: 'Active',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'createdByUser' as any,
                isTransient: true,
                title: 'Created by',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'updatedByUser' as any,
                isTransient: true,
                title: 'Updated by',
                isReadOnly: true,
            }),
        ],
        orderBy: {
            _id: 1,
        },
        parent() {
            return this.associatedUserSection;
        },
    })
    users: ui.fields.Table;

    private async populateGroupRoleTableAll() {
        this.groupRolesMultiReference.value = withoutEdges(
            await this.$.graph
                .node('@sage/xtrem-authorization/Role')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            _id: true,
                            id: true,
                            name: true,
                        },
                        {
                            filter: { isActive: true },
                            first: 500,
                        },
                    ),
                )
                .execute(),
        );
    }

    private async populateGroupSitesTableAll() {
        this.groupSitesMultiReference.value = withoutEdges(
            await this.$.graph
                .node('@sage/xtrem-authorization/SiteGroup')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            _id: true,
                            id: true,
                            name: true,
                        },
                        {
                            first: 500,
                        },
                    ),
                )
                .execute(),
        );
    }

    /**
     * Payload for create record
     */
    getValues(): { id: string; name: string; groupRoles: { role: string }[]; groupSites: { siteGroup: string }[] } {
        const dataRoles: { role: string }[] = this.groupRolesMultiReference.value.map(element => {
            return {
                role: String(element._id),
            };
        });

        const dataSiteGroups: { siteGroup: string }[] = this.groupSitesMultiReference.value.map(element => {
            return {
                siteGroup: String(element._id),
            };
        });

        return {
            id: this.id.value,
            name: this.name.value,
            groupRoles: dataRoles,
            groupSites: dataSiteGroups,
        };
    }

    private async clearGroupRolesTable() {
        this.groupRoles.value.forEach((gridLine: { _id: string; id: string; name: string; description: string }) => {
            this.groupRoles.removeRecord(gridLine._id);
        });
    }

    private async populateGroupRolesTable() {
        this.groupRolesMultiReference.value
            .map(element => {
                return {
                    role: {
                        _id: element._id,
                        id: element.id,
                        name: element.name,
                    },
                };
            })
            .forEach((gridLine: { role: { _id: string; id: string; name: string } }) => {
                this.groupRoles.addOrUpdateRecordValue(gridLine);
            });
    }

    clearGroupSitesTable() {
        this.groupSites.value.forEach((gridLine: { _id: string; id: string; name: string }) => {
            this.groupSites.removeRecord(gridLine._id);
        });
    }

    private async populateGroupSitesTable() {
        this.groupSitesMultiReference.value
            .map(element => {
                return {
                    siteGroup: {
                        _id: element._id,
                        id: element.id,
                        name: element.name,
                    },
                };
            })
            .forEach((gridLine: { siteGroup: { _id: string; id: string; name: string } }) => {
                this.groupSites.addOrUpdateRecordValue(gridLine);
            });
    }

    private setPanel(page: string) {
        this.$.page.subtitle = '';

        if (page !== 'newGroupRoleSite') {
            this.groupList.isDisabled = false;
            this.groupList.isHidden = false;
        }

        if (page === 'newGroupRoleSite') {
            this.editGroupRoleSite.isDisabled = true;
            this.editGroupRoleSite.isHidden = true;
            this.groupList.isDisabled = true;
            this.groupList.isHidden = true;
            this.associatedUserSection.isDisabled = true;
            this.associatedUserSection.isHidden = true;

            this.$standardNewAction.isDisabled = true;
            this.$standardNewAction.isHidden = true;
            this.$standardCancelAction.isDisabled = true;
            this.$standardCancelAction.isHidden = true;
            this.$standardDeleteAction.isDisabled = true;
            this.$standardDeleteAction.isHidden = true;
            this.customSave.isDisabled = true;
            this.customSave.isHidden = true;

            this.confirm.isHidden = false;
            this.confirm.isDisabled = false;
            this.$.page.title = `${ui.localize(
                '@sage/xtrem-authorization/pages__group_role_site_new_group_title',
                'New authorization group',
            )}`;
        }

        if (page === 'detailGroupRoleSite') {
            this.editGroupRoleSite.isDisabled = false;
            this.editGroupRoleSite.isHidden = false;
            this.$standardNewAction.isDisabled = true;
            this.$standardNewAction.isHidden = true;
            this.$standardDeleteAction.isDisabled = true;
            this.$standardDeleteAction.isHidden = true;
            this.$standardCancelAction.isDisabled = true;
            this.$standardCancelAction.isHidden = true;
            this.customSave.isDisabled = true;
            this.customSave.isHidden = true;
            this.allRoles.isHidden = true;
            this.allSiteGroup.isHidden = true;
            this.groupSitesMultiReference.isHidden = true;
            this.groupRolesMultiReference.isHidden = true;
            this.detailRoleGroupSiteReadOnly();
            this.groupRoles.isHidden = false;
            this.groupSites.isHidden = false;
        }
    }

    private detailRoleGroupSiteReadOnly() {
        this.id.isReadOnly = true;
        this.name.isReadOnly = true;
        this.allRoles.isReadOnly = true;
        this.groupRolesMultiReference.isReadOnly = true;
        this.groupSitesMultiReference.isReadOnly = true;
    }

    /**
     * Populate the groups table
     *@param groupId
     */
    private async setAuthorizationGroupList(groupId: string) {
        const result = withoutEdges(
            await this.$.graph
                .node('@sage/xtrem-authorization/UserGroup')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            _id: true,
                            isActive: true,
                            user: {
                                _id: true,
                                firstName: true,
                                lastName: true,
                                displayName: true,
                                email: true,
                            },
                            group: {
                                _id: true,
                                id: true,
                                name: true,
                            },
                            createdBy: true,
                            createStamp: true,
                            updatedBy: true,
                            updateStamp: true,
                        },
                        {
                            filter: { isActive: true, group: groupId },
                            first: 500,
                        },
                    ),
                )
                .execute(),
        );
        this.users.value = result.map(element => {
            return {
                ...element,
                displayName: element.user.displayName,
                createdByUser: `${element.createdBy} ${element.createStamp}`,
                updatedByUser: `${element.updatedBy} ${element.updateStamp}`,
            };
        });
    }

    async openRoleDetailDialog(roleSysId: string, roleName: string) {
        return this.$.dialog.page(
            '@sage/xtrem-authorization/RoleDetail',
            { roleSysId, roleName, isReadOnly: '1' },
            {
                size: 'extra-large',
                resolveOnCancel: true,
            },
        );
    }
}
