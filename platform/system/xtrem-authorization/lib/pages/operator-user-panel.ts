import type { GraphApi } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<OperatorUserPanel>({
    title: 'Reset operator code',
    mode: 'default',
    module: 'authorization',
    isTransient: true,
    businessActions() {
        return [this.cancel, this.confirm];
    },
    onLoad() {
        this.operatorCodeInput.value = String(this.$.queryParameters.code);
        this.$.setPageClean();
        this.confirm.isDisabled = true;
    },
    onDirtyStateUpdated(isDirty: boolean) {
        this.confirm.isDisabled = !isDirty;
    },
})
export class OperatorUserPanel extends ui.Page<GraphApi> {
    @ui.decorators.pageAction<OperatorUserPanel>({
        title: 'Confirm',
        async onClick() {
            const validation = await this.$.page.validate();
            if (validation.length === 0) {
                this.$.finish(this.operatorCodeInput.value);
            } else {
                this.$.showToast(validation.join('\n'), { type: 'error' });
            }
        },
    })
    confirm: ui.PageAction;

    @ui.decorators.pageAction<OperatorUserPanel>({
        title: 'Cancel',
        buttonType: 'tertiary',
        onClick() {
            this.$.finish();
        },
    })
    cancel: ui.PageAction;

    @ui.decorators.section<OperatorUserPanel>({
        isTitleHidden: true,
        title: 'Operator code section',
    })
    operatorCodeSection: ui.containers.Section;

    @ui.decorators.block<OperatorUserPanel>({
        parent() {
            return this.operatorCodeSection;
        },
        isTitleHidden: true,
        title: 'Operator code block',
    })
    operatorCodeBlock: ui.containers.Block;

    @ui.decorators.textField<OperatorUserPanel>({
        parent() {
            return this.operatorCodeBlock;
        },
        title: 'New operator code',
        minLength: 4,
        maxLength: 300,
        isTransient: true,
        isFullWidth: true,
        isMandatory: true,
    })
    operatorCodeInput: ui.fields.Text;
}
