import {
    Graph<PERSON><PERSON>,
    SupportAccessHistory as SupportAccessHistoryNode,
    SupportAccessUnit,
} from '@sage/xtrem-authorization-api';
import { extractEdges } from '@sage/xtrem-client';
import * as ui from '@sage/xtrem-ui';
import * as PillColorSupportAccessHistoryStatus from '../client-functions/pill-color';
import * as utils from '../client-functions/utils';
import { support } from '../menu-items/support';

const defaultAccessUnit: SupportAccessUnit = 'days';

@ui.decorators.page<SupportAccessHistory>({
    module: 'system',
    title: 'Support access',
    mode: 'default',
    isTransient: true,
    menuItem: support,
    access: { node: '@sage/xtrem-authorization/SupportAccessHistory' },
    async onLoad() {
        await this.loadSupportAccessHistory();
        this.$.setPageClean();
    },
    onError(error: string | (Error & { errors: Array<any> })) {
        return utils.formatError(this, error);
    },
})
export class SupportAccessHistory extends ui.Page<GraphApi> {
    @ui.decorators.section<SupportAccessHistory>({
        isTitleHidden: true,
        isOpen: true,
        title: 'General',
    })
    infoSection: ui.containers.Section;

    @ui.decorators.block<SupportAccessHistory>({
        parent() {
            return this.infoSection;
        },
        title: 'Allow Sage support access',
        width: 'extra-large',
    })
    supportAccessBlock: ui.containers.Block;

    @ui.decorators.staticContentField<SupportAccessHistory>({
        parent() {
            return this.supportAccessBlock;
        },
        isTitleHidden: true,
        isFullWidth: true,
        content: 'Specify how long access should be granted from the current time.',
    })
    description: ui.fields.StaticContent;

    @ui.decorators.numericField<SupportAccessHistory>({
        parent() {
            return this.supportAccessBlock;
        },
        title: 'Number',
    })
    forTime: ui.fields.Numeric;

    @ui.decorators.dropdownListField<SupportAccessHistory>({
        parent() {
            return this.supportAccessBlock;
        },
        title: 'Units',
        optionType: '@sage/xtrem-authorization/SupportAccessUnit',
    })
    unit: ui.fields.DropdownList;

    getUnit(): SupportAccessUnit {
        return (this.unit.value as SupportAccessUnit) ?? defaultAccessUnit;
    }

    @ui.decorators.checkboxField<SupportAccessHistory>({
        parent() {
            return this.supportAccessBlock;
        },
        title: 'Read-only access',
        isDisabled() {
            return !!this.isSupportAccessOpen.value;
        },
    })
    isReadOnlyAccess: ui.fields.Checkbox;

    @ui.decorators.staticContentField<SupportAccessHistory>({
        parent() {
            return this.supportAccessBlock;
        },
        isTitleHidden: true,
        isFullWidth: true,
        content: '',
    })
    dummy: ui.fields.StaticContent;

    @ui.decorators.checkboxField<SupportAccessHistory>({
        parent() {
            return this.supportAccessBlock;
        },
        title: 'Support access open',
        isHidden: true,
    })
    isSupportAccessOpen: ui.fields.Checkbox;

    @ui.decorators.buttonField<SupportAccessHistory>({
        parent() {
            return this.supportAccessBlock;
        },
        isDisabled() {
            return !!this.isSupportAccessOpen.value;
        },
        map: () =>
            ui.localize(
                '@sage/xtrem-authorization/pages__support_access_history__allow_access_button_text',
                'Allow access',
            ),

        async onClick() {
            if (this.validateAllowExtend()) {
                await this.$.graph
                    .node('@sage/xtrem-authorization/SupportAccessHistory')
                    .mutations.allowAccess(
                        { status: true },
                        {
                            forTime: this.forTime.value ?? '',
                            units: this.getUnit(),
                            isReadOnlyAccess: this.isReadOnlyAccess.value ? this.isReadOnlyAccess.value : false,
                        },
                    )
                    .execute();
                this.$.router.refresh(true);
            }
        },
    })
    allowAccess: ui.fields.Button;

    @ui.decorators.buttonField<SupportAccessHistory>({
        parent() {
            return this.supportAccessBlock;
        },
        map: () =>
            ui.localize(
                '@sage/xtrem-authorization/pages__support_access_history__extend_access_button_text',
                'Extend access',
            ),

        isDisabled() {
            return !this.isSupportAccessOpen.value;
        },
        async onClick() {
            if (this.validateAllowExtend()) {
                await this.$.graph
                    .node('@sage/xtrem-authorization/SupportAccessHistory')
                    .mutations.extendAccess(
                        { status: true },
                        { forTime: this.forTime.value ?? '', units: this.getUnit() },
                    )
                    .execute();
                this.$.router.refresh(true);
            }
        },
    })
    extendAccess: ui.fields.Button;

    @ui.decorators.buttonField<SupportAccessHistory>({
        parent() {
            return this.supportAccessBlock;
        },
        map: () =>
            ui.localize(
                '@sage/xtrem-authorization/pages__support_access_history__revoke_access_button_text',
                'Revoke access',
            ),

        isDisabled() {
            return !this.isSupportAccessOpen.value;
        },
        async onClick() {
            await this.$.graph
                .node('@sage/xtrem-authorization/SupportAccessHistory')
                .mutations.revokeAccess({ status: true }, {})
                .execute();
            this.$.router.refresh(true);
        },
    })
    revokeAccess: ui.fields.Button;

    @ui.decorators.block<SupportAccessHistory>({
        parent() {
            return this.infoSection;
        },
        title: 'Support access history',
        isTitleHidden: true,
        width: 'extra-large',
    })
    supportAccessHistoryBlock: ui.containers.Block;

    @ui.decorators.tableField<SupportAccessHistory, SupportAccessHistoryNode>({
        title: 'Support access history',
        node: '@sage/xtrem-authorization/SupportAccessHistory',
        isReadOnly: true,
        displayMode: ui.fields.TableDisplayMode.compact,
        isFullWidth: true,
        canUserHideColumns: false,
        canSelect: false,
        canFilter: false,
        parent() {
            return this.supportAccessHistoryBlock;
        },
        columns: [
            ui.nestedFields.text({ bind: '_id', title: 'ID', isHidden: true }),
            ui.nestedFields.text({ bind: 'startTime', title: 'Start time' }),
            ui.nestedFields.text({ bind: 'endTime', title: 'End time' }),
            ui.nestedFields.checkbox({ bind: 'isReadOnlyAccess', title: 'Read-only access' }),
            ui.nestedFields.label({
                bind: 'status',
                title: 'Status',
                optionType: '@sage/xtrem-authorization/SupportAccessHistoryStatus',
                style: (_id, rowValue) =>
                    PillColorSupportAccessHistoryStatus.getLabelColorByStatus('SupportAccessStatus', rowValue.status),
            }),
        ],
    })
    accessHistory: ui.fields.Table<SupportAccessHistoryNode>;

    private async loadSupportAccessHistory() {
        const edgesSelector = ui.queryUtils.edgesSelector(
            {
                _id: true,
                startTime: true,
                endTime: true,
                status: true,
                isReadOnlyAccess: true,
            },
            {
                orderBy: { _id: -1 },
                first: 500,
            },
        );
        const details = extractEdges(
            await this.$.graph.node('@sage/xtrem-authorization/SupportAccessHistory').query(edgesSelector).execute(),
        );

        this.accessHistory.value = details.length > 0 ? details : [];

        const supportAccessOpen = await this.callCheckSupportAccessOpen();
        this.isSupportAccessOpen.value = supportAccessOpen.isOpen;
        this.allowAccess.isDisabled = this.isSupportAccessOpen.value;
        this.revokeAccess.isDisabled = !this.isSupportAccessOpen.value;
        this.extendAccess.isDisabled = !this.isSupportAccessOpen.value;
        this.isReadOnlyAccess.isDisabled = this.isSupportAccessOpen.value;

        // When the session expires and the user is in this page, the page will refresh automatically so that the table and buttons are updated.
        if (supportAccessOpen.isOpen) {
            setTimeout(() => {
                this.$.router.refresh(true);
            }, supportAccessOpen.timeToClose);
        }
    }

    callCheckSupportAccessOpen() {
        return this.$.graph
            .node('@sage/xtrem-authorization/SupportAccessHistory')
            .queries.checkSupportAccessOpen({ isOpen: true, timeToClose: true }, {})
            .execute();
    }

    validateAllowExtend(): boolean {
        if (!this.forTime.value || !this.unit.value) {
            this.$.showToast(
                ui.localize(
                    '@sage/xtrem-authorization/pages__support_access_history__please_fill_the_number_and_unit_field',
                    'Enter the number and units fields.',
                ),
                { timeout: 0, type: 'error' },
            );
            return false;
        }
        return true;
    }
}
