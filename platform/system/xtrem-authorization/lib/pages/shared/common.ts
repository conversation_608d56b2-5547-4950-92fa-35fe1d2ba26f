import { Dict } from '@sage/xtrem-shared';
import { extractEdges, querySelector } from '@sage/xtrem-client';
import { GraphApi, Role, Activity } from '@sage/xtrem-authorization-api';
import { MetaActivity, MetaPackage } from '@sage/xtrem-metadata-api';
import * as ui from '@sage/xtrem-ui';

export const getActivities = async (page: ui.Page<GraphApi>) => {
    const metaActivities = await extractEdges<MetaActivity>(
        await page.$.graph
            // TODO: replace by metadata query on MetaActivity
            .node('@sage/xtrem-metadata/MetaActivity')
            .query(
                ui.queryUtils.edgesSelector<MetaActivity>(
                    {
                        _id: true,
                        name: true,
                        title: true,
                        package: {
                            _id: true,
                            name: true,
                        },
                        permissions: querySelector(
                            {
                                _id: true,
                                name: true,
                                title: true,
                            },
                            {
                                filter: {
                                    isActive: true,
                                },
                            },
                        ),
                    },
                    {
                        filter: {
                            isActive: true,
                        },
                        first: 500,
                    },
                ),
            )
            .execute(),
    );

    // To manage possible desynchronization between metaActivity and Activity
    // we need the _id of Activity
    const activities = await extractEdges<Activity>(
        await page.$.graph
            .node('@sage/xtrem-authorization/Activity')
            .query(
                ui.queryUtils.edgesSelector<Activity>(
                    {
                        _id: true,
                        name: true,
                    },
                    {
                        first: 500,
                    },
                ),
            )
            .execute(),
    );

    return metaActivities.length
        ? metaActivities.map(metaActivity => {
              return {
                  ...metaActivity,
                  activityId: activities.find(activity => activity.name === metaActivity.name)?._id,
              };
          })
        : [];
};

export const isRoleIdExist = async (page: ui.Page<GraphApi>, newRoleId: string) => {
    const roles = await extractEdges<Role>(
        await page.$.graph
            .node('@sage/xtrem-authorization/Role')
            .query(
                ui.queryUtils.edgesSelector<Role>(
                    {
                        _id: true,
                    },
                    {
                        first: 1,
                        filter: { id: { _eq: newRoleId } },
                    },
                ),
            )
            .execute(),
    );
    return !!roles.length;
};

export const getPackages = async (page: Dict<any>) =>
    extractEdges<MetaPackage>(
        await page.$.graph
            .node('@sage/xtrem-metadata/MetaPackage')
            .query(
                ui.queryUtils.edgesSelector<MetaPackage>(
                    {
                        _id: true,
                        name: true,
                        title: true,
                    },
                    {
                        first: 500,
                    },
                ),
            )
            .execute(),
    );

export function confirmDialogWithAcceptButtonText(
    page: ui.Page<GraphApi>,
    title: string,
    message: string,
    acceptButtonText: string,
) {
    const options = {
        acceptButton: {
            text: acceptButtonText,
        },
        cancelButton: {
            text: ui.localize('@sage/xtrem-authorization/pages-confirm-cancel', 'Cancel'),
        },
    };
    return page.$.dialog
        .confirmation('warn', title, message, options)
        .then(() => true)
        .catch(() => false);
}

export type SiteGroupPage = 'siteGroupDetail' | 'editSiteGroup' | 'newSiteGroup' | undefined;

export type UserPage = 'userListPanelDetail' | 'userListPanelNewUser' | 'editUser' | undefined;

/** userListPanelDetail userListPanelNewUser editUser */
export const allUserPages: UserPage[] = ['userListPanelDetail', 'userListPanelNewUser', 'editUser'];

/** siteGroupDetail editSiteGroup newSiteGroup */
export const allSiteGroupPages: SiteGroupPage[] = ['siteGroupDetail', 'editSiteGroup', 'newSiteGroup'];
