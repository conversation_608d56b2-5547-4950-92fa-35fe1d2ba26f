import { Dict, extractEdges, querySelector } from '@sage/xtrem-client';
import * as ui from '@sage/xtrem-ui';
import { userData } from '../menu-items/user-data';

@ui.decorators.page<SiteGroupList>({
    module: 'system',
    isTransient: true,
    title: 'Site groups',
    node: '@sage/xtrem-authorization/SiteGroup',
    menuItem: userData,
    access: { node: '@sage/xtrem-authorization/SiteGroup' },
    async onLoad() {
        await this.setSiteGroup();
        this.$.setPageClean();
    },
    businessActions() {
        return [this.createSiteGroup, this.createSite];
    },
})
export class SiteGroupList extends ui.Page {
    @ui.decorators.section<SiteGroupList>({
        title: 'Site groups',
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.block<SiteGroupList>({
        parent() {
            return this.section;
        },
        title: 'Site group',
        isTitleHidden: true,
    })
    fieldBlock: ui.containers.Block;

    @ui.decorators.nestedGridField<SiteGroupList>({
        bind: 'siteGroups',
        canFilter: true,
        canActivate: true,
        isChangeIndicatorDisabled: true,
        levels: [
            {
                node: '@sage/xtrem-authorization/SiteGroupToSiteGroup',
                childProperty: 'siteGroup',
                columns: [
                    ui.nestedFields.text({
                        bind: 'name',
                        title: 'Name',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.link({
                        bind: 'id',
                        title: 'ID',
                        onClick(rowId: string, rowData: any) {
                            this.$.router.goTo('@sage/xtrem-authorization/SiteGroup', {
                                _id: rowData._id,
                                page: 'siteGroupDetail',
                            });
                        },
                    }),
                ],
                dropdownActions: [
                    {
                        icon: 'edit',
                        title: 'Edit site group',
                        async onClick(rowId: any, rowData: any) {
                            this.$.router.goTo('@sage/xtrem-authorization/SiteGroup', {
                                _id: rowData._id,
                                page: 'editSiteGroup',
                            });
                        },
                    },
                    {
                        icon: 'delete',
                        title: 'Delete site group',
                        async onClick(rowId: any, rowData: any) {
                            const options: ui.dialogs.DialogOptions = {
                                acceptButton: {
                                    text: 'Delete',
                                },
                                cancelButton: {
                                    text: 'Cancel',
                                },
                            };
                            if (
                                await this.$.dialog
                                    .confirmation(
                                        'warn',
                                        ui.localize('@sage/xtrem-authorization/delete-group', 'Confirm deletion'),
                                        ui.localize(
                                            '@sage/xtrem-authorization/delete-dialog-content',
                                            'You are about to delete this group.',
                                        ),
                                        options,
                                    )
                                    .then(() => true)
                                    .catch(() => false)
                            ) {
                                this.$.loader.display();
                                try {
                                    await this.$.graph.delete({
                                        _id: rowData._id,
                                        nodeName: '@sage/xtrem-authorization/SiteGroup',
                                    });
                                    this.siteGroups.removeRecord(rowId, 0);
                                    this.$.showToast(
                                        ui.localize('@sage/xtrem-authorization/delete-confirmation', 'Record deleted'),
                                        { type: 'success' },
                                    );
                                } catch (e) {
                                    this.$.showToast(e.message, { timeout: 0, type: 'error' });
                                }
                                this.$.setPageClean();
                                this.$.loader.hide();
                            }
                        },
                    },
                ],
            },
            {
                node: '@sage/xtrem-authorization/SiteGroup',
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        title: 'ID',
                        isReadOnly: true,
                        isHidden: true,
                    }),
                    ui.nestedFields.text({
                        bind: 'name',
                        title: 'Name',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.text({
                        bind: 'id',
                        title: 'ID',
                        isReadOnly: true,
                    }),
                ],
            },
        ],
        parent() {
            return this.fieldBlock;
        },
    })
    siteGroups: ui.fields.NestedGrid;

    @ui.decorators.pageAction<SiteGroupList>({
        title: 'Site group list',
        isHidden: false,
        isDisabled: false,
        onClick() {
            this.$.router.goTo('@sage/xtrem-authorization/SiteGroupList', {});
        },
    })
    groupList: ui.PageAction;

    @ui.decorators.pageAction<SiteGroupList>({
        title: 'Create site group',
        isHidden: false,
        isDisabled: false,
        onError() {
            // Intentionally left empty
        },
        async onClick() {
            try {
                await this.$.dialog.page(
                    '@sage/xtrem-authorization/SiteGroup',
                    {
                        page: 'newSiteGroup',
                    },
                    {
                        rightAligned: true,
                        size: 'large',
                    },
                );
            } catch (error) {
                if (error) {
                    this.$.showToast(error.message, { timeout: 10000, type: 'error' });
                }
            }

            await this.setSiteGroup();
            this.$.setPageClean();
        },
    })
    createSiteGroup: ui.PageAction;

    @ui.decorators.pageAction<SiteGroupList>({
        title: 'Create site',
        isHidden: false,
        isDisabled: false,
        onError() {
            // Intentionally left empty
        },
        async onClick() {
            try {
                await this.$.dialog.page(
                    '@sage/xtrem-master-data/Site',
                    {
                        page: 'newSite',
                    },
                    {
                        rightAligned: true,
                        size: 'large',
                    },
                );
            } catch (error) {
                if (error) {
                    this.$.showToast(error.message, { timeout: 10000, type: 'error' });
                }
            }

            await this.setSiteGroup();
            this.$.setPageClean();
        },
    })
    createSite: ui.PageAction;

    private async setSiteGroup() {
        const siteGroups = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-authorization/SiteGroup')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            _id: true,
                            id: true,
                            isActive: true,
                            isLegalCompany: true,
                            name: true,
                            siteGroups: querySelector({
                                siteGroup: {
                                    _id: true,
                                    id: true,
                                    isActive: true,
                                    isLegalCompany: true,
                                    name: true,
                                    sites: querySelector({
                                        site: {
                                            _id: true,
                                            id: true,
                                            name: true,
                                            description: true,
                                            isActive: true,
                                            legalCompany: {
                                                id: true,
                                            },
                                        },
                                    }),
                                },
                            }),
                            sites: querySelector({
                                _id: true,
                                site: {
                                    _id: true,
                                    id: true,
                                    name: true,
                                    description: true,
                                    isActive: true,
                                    legalCompany: {
                                        id: true,
                                    },
                                },
                            }),
                        },
                        {
                            filter: {
                                isActive: true,
                            },
                        },
                    ),
                )
                .execute(),
        );

        siteGroups.forEach((siteGroupLine: Partial<Dict<any>>) => {
            this.siteGroups.addOrUpdateRecordValue(siteGroupLine, 0, undefined);
            siteGroupLine.siteGroups.forEach((siteGroupSite: Partial<Dict<any>>) => {
                this.siteGroups.addOrUpdateRecordValue(siteGroupSite.siteGroup, 1, siteGroupLine._id);
            });
        });
    }
}
