import { Activity, GraphApi } from '@sage/xtrem-authorization-api';
import { Dict } from '@sage/xtrem-client';
import * as ui from '@sage/xtrem-ui';
import { RoleActivityMutation, RoleActivityPod } from '../shared-functions/interfaces';
import { getActivities, getPackages, isRoleIdExist } from './shared/common';

interface ActivityWithTransientFields extends Activity {
    isActive: boolean;
    /**   To manage desync between metaActivity and Activity node */
    activityId: string;
}
@ui.decorators.page<RoleSetup>({
    mode: 'default',
    title: 'New role',
    isTransient: true,
    businessActions() {
        return [this.cancel, this.confirm, this.next];
    },
    async onLoad() {
        this.roleSysId = Number(this.$.queryParameters.roleSysId);
        await this.loadPackages();
        this.$.setPageClean();
    },
})
export class RoleSetup extends ui.Page<GraphApi> {
    private roleSysId: number;

    private numberPackages: number;

    private packageNames: string[];

    @ui.decorators.section<RoleSetup>({
        isTitleHidden: true,
    })
    roleSection: ui.containers.Section;

    @ui.decorators.block<RoleSetup>({
        parent() {
            return this.roleSection;
        },
        title: 'Role information',
        width: 'large',
    })
    roleBlock: ui.containers.Block;

    @ui.decorators.textField<RoleSetup>({
        parent() {
            return this.roleBlock;
        },
        title: 'ID',
        isMandatory: true,
        width: 'large',
        async validation(val) {
            if (await isRoleIdExist(this, val)) {
                return ui.localize(
                    '@sage/xtrem-authorization/pages__role_setup__duplicated_id',
                    'The entered ID already exists. Select another ID.',
                );
            }
            return undefined;
        },
    })
    id: ui.fields.Text;

    @ui.decorators.textField<RoleSetup>({
        parent() {
            return this.roleBlock;
        },
        title: 'Name',
        isMandatory: true,
        width: 'large',
    })
    name: ui.fields.Text;

    @ui.decorators.multiDropdownField<RoleSetup>({
        isTransient: true,
        title: 'Packages',
        isMandatory: true,
        parent() {
            return this.roleBlock;
        },
        options() {
            return this.packageNames;
        },
        width: 'large',
    })
    packageSelect: ui.fields.MultiDropdown;

    @ui.decorators.section<RoleSetup>({
        isTitleHidden: true,
        mode: 'accordion',
    })
    activitySection: ui.containers.Section;

    @ui.decorators.block<RoleSetup>({
        width: 'large',
        parent() {
            return this.activitySection;
        },
        isHidden() {
            return true;
        },
        title() {
            return '';
        },
    })
    packageBlock1: ui.containers.Block;

    @ui.decorators.podCollectionField<RoleSetup, ActivityWithTransientFields>({
        node: '@sage/xtrem-authorization/Activity',
        parent() {
            return this.packageBlock1;
        },
        recordWidth: 'large',
        isTransient: true,
        columns: [
            ui.nestedFields.text({ bind: '_id', isHidden: true }),
            ui.nestedFields.label({
                bind: 'description',
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
                isTitleHidden: true,
            }),
            ui.nestedFields.switch({
                bind: 'isActive',
                isTransient: true,
                onChange(rowId: any) {
                    this.setSelection(rowId, 0);
                },
            }),
            ui.nestedFields.multiDropdown({
                bind: 'permissions',
                width: 'large',
                options(value: string[], rowData: RoleActivityPod) {
                    return rowData.allPermissions ? rowData.allPermissions : null;
                },
                map(value: any, rowValue: any) {
                    return this.permissionMap[rowValue.name][value];
                },
            }),
        ],
    })
    activities1: ui.fields.PodCollection<Activity>;

    @ui.decorators.block<RoleSetup>({
        width: 'large',
        parent() {
            return this.activitySection;
        },
        isHidden() {
            return true;
        },
        title() {
            return '';
        },
    })
    packageBlock2: ui.containers.Block;

    @ui.decorators.podCollectionField<RoleSetup, ActivityWithTransientFields>({
        node: '@sage/xtrem-authorization/Activity',
        parent() {
            return this.packageBlock2;
        },
        recordWidth: 'large',
        isTransient: true,
        columns: [
            ui.nestedFields.text({ bind: '_id', isHidden: true }),
            ui.nestedFields.label({
                bind: 'description',
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
                isTitleHidden: true,
            }),
            ui.nestedFields.switch({
                bind: 'isActive',
                isTransient: true,
                onChange(rowId: any) {
                    this.setSelection(rowId, 1);
                },
            }),
            ui.nestedFields.multiDropdown({
                bind: 'permissions',
                width: 'large',
                options(value: string[], rowData: RoleActivityPod) {
                    return rowData.allPermissions ? rowData.allPermissions : null;
                },
                map(value: any, rowValue: any) {
                    return this.permissionMap[rowValue.name][value];
                },
            }),
        ],
    })
    activities2: ui.fields.PodCollection<Activity>;

    @ui.decorators.block<RoleSetup>({
        width: 'large',
        parent() {
            return this.activitySection;
        },
        isHidden() {
            return true;
        },
        title() {
            return '';
        },
    })
    packageBlock3: ui.containers.Block;

    @ui.decorators.podCollectionField<RoleSetup, ActivityWithTransientFields>({
        node: '@sage/xtrem-authorization/Activity',
        parent() {
            return this.packageBlock3;
        },
        recordWidth: 'large',
        isTransient: true,
        columns: [
            ui.nestedFields.text({ bind: '_id', isHidden: true }),
            ui.nestedFields.label({
                bind: 'description',
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
                isTitleHidden: true,
            }),
            ui.nestedFields.switch({
                bind: 'isActive',
                isTransient: true,
                onChange(rowId: any) {
                    this.setSelection(rowId, 2);
                },
            }),
            ui.nestedFields.multiDropdown({
                bind: 'permissions',
                width: 'large',
                options(value: string[], rowData: RoleActivityPod) {
                    return rowData.allPermissions ? rowData.allPermissions : null;
                },
                map(value: any, rowValue: any) {
                    return this.permissionMap[rowValue.name][value];
                },
            }),
        ],
    })
    activities3: ui.fields.PodCollection<Activity>;

    @ui.decorators.block<RoleSetup>({
        width: 'large',
        parent() {
            return this.activitySection;
        },
        isHidden() {
            return true;
        },
        title() {
            return '';
        },
    })
    packageBlock4: ui.containers.Block;

    @ui.decorators.podCollectionField<RoleSetup, ActivityWithTransientFields>({
        node: '@sage/xtrem-authorization/Activity',
        parent() {
            return this.packageBlock4;
        },
        recordWidth: 'large',
        isTransient: true,
        columns: [
            ui.nestedFields.text({ bind: '_id', isHidden: true }),
            ui.nestedFields.label({
                bind: 'description',
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
                isTitleHidden: true,
            }),
            ui.nestedFields.switch({
                bind: 'isActive',
                isTransient: true,
                onChange(rowId: any) {
                    this.setSelection(rowId, 3);
                },
            }),
            ui.nestedFields.multiDropdown({
                bind: 'permissions',
                width: 'large',
                options(value: string[], rowData: RoleActivityPod) {
                    return rowData.allPermissions ? rowData.allPermissions : null;
                },
                map(value: any, rowValue: any) {
                    return this.permissionMap[rowValue.name][value];
                },
            }),
        ],
    })
    activities4: ui.fields.PodCollection<Activity>;

    @ui.decorators.block<RoleSetup>({
        width: 'large',
        parent() {
            return this.activitySection;
        },
        isHidden() {
            return true;
        },
        title() {
            return '';
        },
    })
    packageBlock5: ui.containers.Block;

    @ui.decorators.podCollectionField<RoleSetup, ActivityWithTransientFields>({
        node: '@sage/xtrem-authorization/Activity',
        parent() {
            return this.packageBlock5;
        },
        recordWidth: 'large',
        isTransient: true,
        columns: [
            ui.nestedFields.text({ bind: '_id', isHidden: true }),
            ui.nestedFields.label({
                width: 'small',
                bind: 'description',
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
                isTitleHidden: true,
            }),
            ui.nestedFields.switch({
                bind: 'isActive',
                isTransient: true,
                onChange(rowId: any) {
                    this.setSelection(rowId, 4);
                },
            }),
            ui.nestedFields.multiDropdown({
                bind: 'permissions',
                width: 'large',
                options(value: string[], rowData: RoleActivityPod) {
                    return rowData.allPermissions ? rowData.allPermissions : null;
                },
                map(value: any, rowValue: any) {
                    return this.permissionMap[rowValue.name][value];
                },
            }),
        ],
    })
    activities5: ui.fields.PodCollection<Activity>;

    @ui.decorators.block<RoleSetup>({
        width: 'large',
        parent() {
            return this.activitySection;
        },
        isHidden() {
            return true;
        },
        title() {
            return '';
        },
    })
    packageBlock6: ui.containers.Block;

    @ui.decorators.podCollectionField<RoleSetup, ActivityWithTransientFields>({
        node: '@sage/xtrem-authorization/Activity',
        parent() {
            return this.packageBlock6;
        },
        recordWidth: 'large',
        isTransient: true,
        columns: [
            ui.nestedFields.text({ bind: '_id', isHidden: true }),
            ui.nestedFields.label({
                bind: 'description',
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
                isTitleHidden: true,
            }),
            ui.nestedFields.switch({
                bind: 'isActive',
                isTransient: true,
                onChange(rowId: any) {
                    this.setSelection(rowId, 5);
                },
            }),
            ui.nestedFields.multiDropdown({
                bind: 'permissions',
                width: 'large',
                options(value: string[], rowData: RoleActivityPod) {
                    return rowData.allPermissions ? rowData.allPermissions : null;
                },
                map(value: any, rowValue: any) {
                    return this.permissionMap[rowValue.name][value];
                },
            }),
        ],
    })
    activities6: ui.fields.PodCollection<Activity>;

    @ui.decorators.block<RoleSetup>({
        width: 'large',
        parent() {
            return this.activitySection;
        },
        isHidden() {
            return true;
        },
        title() {
            return '';
        },
    })
    packageBlock7: ui.containers.Block;

    @ui.decorators.podCollectionField<RoleSetup, ActivityWithTransientFields>({
        node: '@sage/xtrem-authorization/Activity',
        parent() {
            return this.packageBlock7;
        },
        recordWidth: 'large',
        isTransient: true,
        columns: [
            ui.nestedFields.text({ bind: '_id', isHidden: true }),
            ui.nestedFields.label({
                width: 'small',
                bind: 'description',
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
                isTitleHidden: true,
            }),
            ui.nestedFields.switch({
                bind: 'isActive',
                isTransient: true,
                onChange(rowId: any) {
                    this.setSelection(rowId, 6);
                },
            }),
            ui.nestedFields.multiDropdown({
                bind: 'permissions',
                width: 'large',
                options(value: string[], rowData: RoleActivityPod) {
                    return rowData.allPermissions ? rowData.allPermissions : null;
                },
                map(value: any, rowValue: any) {
                    return this.permissionMap[rowValue.name][value];
                },
            }),
        ],
    })
    activities7: ui.fields.PodCollection<Activity>;

    @ui.decorators.block<RoleSetup>({
        width: 'large',
        parent() {
            return this.activitySection;
        },
        isHidden() {
            return true;
        },
        title() {
            return '';
        },
    })
    packageBlock8: ui.containers.Block;

    @ui.decorators.podCollectionField<RoleSetup, ActivityWithTransientFields>({
        node: '@sage/xtrem-authorization/Activity',
        parent() {
            return this.packageBlock8;
        },
        recordWidth: 'large',
        isTransient: true,
        columns: [
            ui.nestedFields.text({ bind: '_id', isHidden: true }),
            ui.nestedFields.label({
                width: 'small',
                bind: 'description',
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
                isTitleHidden: true,
            }),
            ui.nestedFields.switch({
                bind: 'isActive',
                isTransient: true,
                onChange(rowId: any) {
                    this.setSelection(rowId, 7);
                },
            }),
            ui.nestedFields.multiDropdown({
                bind: 'permissions',
                width: 'large',
                options(value: string[], rowData: RoleActivityPod) {
                    return rowData.allPermissions ? rowData.allPermissions : null;
                },
                map(value: any, rowValue: any) {
                    return this.permissionMap[rowValue.name][value];
                },
            }),
        ],
    })
    activities8: ui.fields.PodCollection<Activity>;

    @ui.decorators.block<RoleSetup>({
        width: 'large',
        parent() {
            return this.activitySection;
        },
        isHidden() {
            return true;
        },
        title() {
            return '';
        },
    })
    packageBlock9: ui.containers.Block;

    @ui.decorators.podCollectionField<RoleSetup, ActivityWithTransientFields>({
        node: '@sage/xtrem-authorization/Activity',
        parent() {
            return this.packageBlock9;
        },
        recordWidth: 'large',
        isTransient: true,
        columns: [
            ui.nestedFields.text({ bind: '_id', isHidden: true }),
            ui.nestedFields.label({
                width: 'small',
                bind: 'description',
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
                isTitleHidden: true,
            }),
            ui.nestedFields.switch({
                bind: 'isActive',
                isTransient: true,
                onChange(rowId: any) {
                    this.setSelection(rowId, 8);
                },
            }),
            ui.nestedFields.multiDropdown({
                bind: 'permissions',
                width: 'large',
                options(value: string[], rowData: RoleActivityPod) {
                    return rowData.allPermissions ? rowData.allPermissions : null;
                },
                map(value: any, rowValue: any) {
                    return this.permissionMap[rowValue.name][value];
                },
            }),
        ],
    })
    activities9: ui.fields.PodCollection<Activity>;

    @ui.decorators.block<RoleSetup>({
        width: 'large',
        parent() {
            return this.activitySection;
        },
        isHidden() {
            return true;
        },
        title() {
            return '';
        },
    })
    packageBlock10: ui.containers.Block;

    @ui.decorators.podCollectionField<RoleSetup, ActivityWithTransientFields>({
        node: '@sage/xtrem-authorization/Activity',
        parent() {
            return this.packageBlock10;
        },
        recordWidth: 'large',
        isTransient: true,
        columns: [
            ui.nestedFields.text({ bind: '_id', isHidden: true }),
            ui.nestedFields.label({
                width: 'small',
                bind: 'description',
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
                isTitleHidden: true,
            }),
            ui.nestedFields.switch({
                bind: 'isActive',
                isTransient: true,
                onChange(rowId: any) {
                    this.setSelection(rowId, 9);
                },
            }),
            ui.nestedFields.multiDropdown({
                bind: 'permissions',
                width: 'large',
                options(value: string[], rowData: RoleActivityPod) {
                    return rowData.allPermissions ? rowData.allPermissions : null;
                },
                map(value: any, rowValue: any) {
                    return this.permissionMap[rowValue.name][value];
                },
            }),
        ],
    })
    activities10: ui.fields.PodCollection<Activity>;

    @ui.decorators.block<RoleSetup>({
        width: 'large',
        parent() {
            return this.activitySection;
        },
        isHidden() {
            return true;
        },
        title() {
            return '';
        },
    })
    packageBlock11: ui.containers.Block;

    @ui.decorators.podCollectionField<RoleSetup, ActivityWithTransientFields>({
        node: '@sage/xtrem-authorization/Activity',
        parent() {
            return this.packageBlock11;
        },
        recordWidth: 'large',
        isTransient: true,
        columns: [
            ui.nestedFields.text({ bind: '_id', isHidden: true }),
            ui.nestedFields.label({
                bind: 'description',
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
                isTitleHidden: true,
            }),
            ui.nestedFields.switch({
                bind: 'isActive',
                isTransient: true,
                onChange(rowId: any) {
                    this.setSelection(rowId, 10);
                },
            }),
            ui.nestedFields.multiDropdown({
                bind: 'permissions',
                width: 'large',
                options(value: string[], rowData: RoleActivityPod) {
                    return rowData.allPermissions ? rowData.allPermissions : null;
                },
                map(value: any, rowValue: any) {
                    return this.permissionMap[rowValue.name][value];
                },
            }),
        ],
    })
    activities11: ui.fields.PodCollection<Activity>;

    @ui.decorators.block<RoleSetup>({
        width: 'large',
        parent() {
            return this.activitySection;
        },
        isHidden() {
            return true;
        },
        title() {
            return '';
        },
    })
    packageBlock12: ui.containers.Block;

    @ui.decorators.podCollectionField<RoleSetup, ActivityWithTransientFields>({
        node: '@sage/xtrem-authorization/Activity',
        parent() {
            return this.packageBlock12;
        },
        recordWidth: 'large',
        isTransient: true,
        columns: [
            ui.nestedFields.text({ bind: '_id', isHidden: true }),
            ui.nestedFields.label({
                bind: 'description',
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
                isTitleHidden: true,
            }),
            ui.nestedFields.switch({
                bind: 'isActive',
                isTransient: true,
                onChange(rowId: any) {
                    this.setSelection(rowId, 11);
                },
            }),
            ui.nestedFields.multiDropdown({
                bind: 'permissions',
                width: 'large',
                options(value: string[], rowData: RoleActivityPod) {
                    return rowData.allPermissions ? rowData.allPermissions : null;
                },
                map(value: any, rowValue: any) {
                    return this.permissionMap[rowValue.name][value];
                },
            }),
        ],
    })
    activities12: ui.fields.PodCollection<Activity>;

    @ui.decorators.block<RoleSetup>({
        width: 'large',
        parent() {
            return this.activitySection;
        },
        isHidden() {
            return true;
        },
        title() {
            return '';
        },
    })
    packageBlock13: ui.containers.Block;

    @ui.decorators.podCollectionField<RoleSetup, ActivityWithTransientFields>({
        node: '@sage/xtrem-authorization/Activity',
        parent() {
            return this.packageBlock13;
        },
        recordWidth: 'large',
        isTransient: true,
        columns: [
            ui.nestedFields.text({ bind: '_id', isHidden: true }),
            ui.nestedFields.label({
                bind: 'description',
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
                isTitleHidden: true,
            }),
            ui.nestedFields.switch({
                bind: 'isActive',
                isTransient: true,
                onChange(rowId: any) {
                    this.setSelection(rowId, 12);
                },
            }),
            ui.nestedFields.multiDropdown({
                bind: 'permissions',
                width: 'large',
                options(value: string[], rowData: RoleActivityPod) {
                    return rowData.allPermissions ? rowData.allPermissions : null;
                },
                map(value: any, rowValue: any) {
                    return this.permissionMap[rowValue.name][value];
                },
            }),
        ],
    })
    activities13: ui.fields.PodCollection<Activity>;

    @ui.decorators.block<RoleSetup>({
        width: 'large',
        parent() {
            return this.activitySection;
        },
        isHidden() {
            return true;
        },
        title() {
            return '';
        },
    })
    packageBlock14: ui.containers.Block;

    @ui.decorators.podCollectionField<RoleSetup, ActivityWithTransientFields>({
        node: '@sage/xtrem-authorization/Activity',
        parent() {
            return this.packageBlock14;
        },
        recordWidth: 'large',
        isTransient: true,
        columns: [
            ui.nestedFields.text({ bind: '_id', isHidden: true }),
            ui.nestedFields.label({
                bind: 'description',
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
                isTitleHidden: true,
            }),
            ui.nestedFields.switch({
                bind: 'isActive',
                isTransient: true,
                onChange(rowId: any) {
                    this.setSelection(rowId, 13);
                },
            }),
            ui.nestedFields.multiDropdown({
                bind: 'permissions',
                width: 'large',
                options(value: string[], rowData: RoleActivityPod) {
                    return rowData.allPermissions ? rowData.allPermissions : null;
                },
                map(value: any, rowValue: any) {
                    return this.permissionMap[rowValue.name][value];
                },
            }),
        ],
    })
    activities14: ui.fields.PodCollection<Activity>;

    @ui.decorators.block<RoleSetup>({
        width: 'large',
        parent() {
            return this.activitySection;
        },
        isHidden() {
            return true;
        },
        title() {
            return '';
        },
    })
    packageBlock15: ui.containers.Block;

    @ui.decorators.podCollectionField<RoleSetup, ActivityWithTransientFields>({
        node: '@sage/xtrem-authorization/Activity',
        parent() {
            return this.packageBlock15;
        },
        recordWidth: 'large',
        isTransient: true,
        pageSize: 100,
        columns: [
            ui.nestedFields.text({ bind: '_id', isHidden: true }),
            ui.nestedFields.label({
                bind: 'description',
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
                isTitleHidden: true,
            }),
            ui.nestedFields.switch({
                bind: 'isActive',
                isTransient: true,
                onChange(rowId: any) {
                    this.setSelection(rowId, 14);
                },
            }),
            ui.nestedFields.multiDropdown({
                bind: 'permissions',
                width: 'large',
                options(value: string[], rowData: RoleActivityPod) {
                    return rowData.allPermissions ? rowData.allPermissions : null;
                },
                map(value: any, rowValue: any) {
                    return this.permissionMap[rowValue.name][value];
                },
            }),
        ],
    })
    activities15: ui.fields.PodCollection<Activity>;

    @ui.decorators.block<RoleSetup>({
        width: 'large',
        parent() {
            return this.activitySection;
        },
        isHidden() {
            return true;
        },
        title() {
            return '';
        },
    })
    packageBlock16: ui.containers.Block;

    @ui.decorators.podCollectionField<RoleSetup, ActivityWithTransientFields>({
        node: '@sage/xtrem-authorization/Activity',
        parent() {
            return this.packageBlock16;
        },
        recordWidth: 'large',
        isTransient: true,
        pageSize: 100,
        columns: [
            ui.nestedFields.text({ bind: '_id', isHidden: true }),
            ui.nestedFields.label({
                bind: 'description',
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
                isTitleHidden: true,
            }),
            ui.nestedFields.switch({
                bind: 'isActive',
                isTransient: true,
                onChange(rowId: any) {
                    this.setSelection(rowId, 15);
                },
            }),
            ui.nestedFields.multiDropdown({
                bind: 'permissions',
                width: 'large',
                options(value: string[], rowData: RoleActivityPod) {
                    return rowData.allPermissions ? rowData.allPermissions : null;
                },
                map(value: any, rowValue: any) {
                    return this.permissionMap[rowValue.name][value];
                },
            }),
        ],
    })
    activities16: ui.fields.PodCollection<Activity>;

    @ui.decorators.block<RoleSetup>({
        width: 'large',
        parent() {
            return this.activitySection;
        },
        isHidden() {
            return true;
        },
        title() {
            return '';
        },
    })
    packageBlock17: ui.containers.Block;

    @ui.decorators.podCollectionField<RoleSetup, ActivityWithTransientFields>({
        node: '@sage/xtrem-authorization/Activity',
        parent() {
            return this.packageBlock17;
        },
        recordWidth: 'large',
        isTransient: true,
        pageSize: 100,
        columns: [
            ui.nestedFields.text({ bind: '_id', isHidden: true }),
            ui.nestedFields.label({
                bind: 'description',
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
                isTitleHidden: true,
            }),
            ui.nestedFields.switch({
                bind: 'isActive',
                isTransient: true,
                onChange(rowId: any) {
                    this.setSelection(rowId, 16);
                },
            }),
            ui.nestedFields.multiDropdown({
                bind: 'permissions',
                width: 'large',
                options(value: string[], rowData: RoleActivityPod) {
                    return rowData.allPermissions ? rowData.allPermissions : null;
                },
                map(value: any, rowValue: any) {
                    return this.permissionMap[rowValue.name][value];
                },
            }),
        ],
    })
    activities17: ui.fields.PodCollection<Activity>;

    @ui.decorators.block<RoleSetup>({
        width: 'large',
        parent() {
            return this.activitySection;
        },
        isHidden() {
            return true;
        },
        title() {
            return '';
        },
    })
    packageBlock18: ui.containers.Block;

    @ui.decorators.podCollectionField<RoleSetup, ActivityWithTransientFields>({
        node: '@sage/xtrem-authorization/Activity',
        parent() {
            return this.packageBlock18;
        },
        recordWidth: 'large',
        isTransient: true,
        pageSize: 100,
        columns: [
            ui.nestedFields.text({ bind: '_id', isHidden: true }),
            ui.nestedFields.label({
                bind: 'description',
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
                isTitleHidden: true,
            }),
            ui.nestedFields.switch({
                bind: 'isActive',
                isTransient: true,
                onChange(rowId: any) {
                    this.setSelection(rowId, 17);
                },
            }),
            ui.nestedFields.multiDropdown({
                bind: 'permissions',
                width: 'large',
                options(value: string[], rowData: RoleActivityPod) {
                    return rowData.allPermissions ? rowData.allPermissions : null;
                },
                map(value: any, rowValue: any) {
                    return this.permissionMap[rowValue.name][value];
                },
            }),
        ],
    })
    activities18: ui.fields.PodCollection<Activity>;

    @ui.decorators.block<RoleSetup>({
        width: 'large',
        parent() {
            return this.activitySection;
        },
        isHidden() {
            return true;
        },
        title() {
            return '';
        },
    })
    packageBlock19: ui.containers.Block;

    @ui.decorators.podCollectionField<RoleSetup, ActivityWithTransientFields>({
        node: '@sage/xtrem-authorization/Activity',
        parent() {
            return this.packageBlock19;
        },
        recordWidth: 'large',
        isTransient: true,
        pageSize: 100,
        columns: [
            ui.nestedFields.text({ bind: '_id', isHidden: true }),
            ui.nestedFields.label({
                bind: 'description',
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
                isTitleHidden: true,
            }),
            ui.nestedFields.switch({
                bind: 'isActive',
                isTransient: true,
                onChange(rowId: any) {
                    this.setSelection(rowId, 18);
                },
            }),
            ui.nestedFields.multiDropdown({
                bind: 'permissions',
                width: 'large',
                options(value: string[], rowData: RoleActivityPod) {
                    return rowData.allPermissions ? rowData.allPermissions : null;
                },
                map(value: any, rowValue: any) {
                    return this.permissionMap[rowValue.name][value];
                },
            }),
        ],
    })
    activities19: ui.fields.PodCollection<Activity>;

    @ui.decorators.block<RoleSetup>({
        width: 'large',
        parent() {
            return this.activitySection;
        },
        isHidden() {
            return true;
        },
        title() {
            return '';
        },
    })
    packageBlock20: ui.containers.Block;

    @ui.decorators.podCollectionField<RoleSetup, ActivityWithTransientFields>({
        node: '@sage/xtrem-authorization/Activity',
        parent() {
            return this.packageBlock20;
        },
        recordWidth: 'large',
        isTransient: true,
        pageSize: 100,
        columns: [
            ui.nestedFields.text({ bind: '_id', isHidden: true }),
            ui.nestedFields.label({
                bind: 'description',
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
                isTitleHidden: true,
            }),
            ui.nestedFields.switch({
                bind: 'isActive',
                isTransient: true,
                onChange(rowId: any) {
                    this.setSelection(rowId, 19);
                },
            }),
            ui.nestedFields.multiDropdown({
                bind: 'permissions',
                width: 'large',
                options(value: string[], rowData: RoleActivityPod) {
                    return rowData.allPermissions ? rowData.allPermissions : null;
                },
                map(value: any, rowValue: any) {
                    return this.permissionMap[rowValue.name][value];
                },
            }),
        ],
    })
    activities20: ui.fields.PodCollection<Activity>;

    @ui.decorators.block<RoleSetup>({
        width: 'large',
        parent() {
            return this.activitySection;
        },
        isHidden() {
            return true;
        },
        title() {
            return '';
        },
    })
    packageBlock21: ui.containers.Block;

    @ui.decorators.podCollectionField<RoleSetup, ActivityWithTransientFields>({
        node: '@sage/xtrem-authorization/Activity',
        parent() {
            return this.packageBlock21;
        },
        recordWidth: 'large',
        isTransient: true,
        pageSize: 100,
        columns: [
            ui.nestedFields.text({ bind: '_id', isHidden: true }),
            ui.nestedFields.label({
                bind: 'description',
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
                isTitleHidden: true,
            }),
            ui.nestedFields.switch({
                bind: 'isActive',
                isTransient: true,
                onChange(rowId: any) {
                    this.setSelection(rowId, 20);
                },
            }),
            ui.nestedFields.multiDropdown({
                bind: 'permissions',
                width: 'large',
                options(value: string[], rowData: RoleActivityPod) {
                    return rowData.allPermissions ? rowData.allPermissions : null;
                },
                map(value: any, rowValue: any) {
                    return this.permissionMap[rowValue.name][value];
                },
            }),
        ],
    })
    activities21: ui.fields.PodCollection<Activity>;

    @ui.decorators.block<RoleSetup>({
        width: 'large',
        parent() {
            return this.activitySection;
        },
        isHidden() {
            return true;
        },
        title() {
            return '';
        },
    })
    packageBlock22: ui.containers.Block;

    @ui.decorators.podCollectionField<RoleSetup, ActivityWithTransientFields>({
        node: '@sage/xtrem-authorization/Activity',
        parent() {
            return this.packageBlock22;
        },
        recordWidth: 'large',
        isTransient: true,
        pageSize: 100,
        columns: [
            ui.nestedFields.text({ bind: '_id', isHidden: true }),
            ui.nestedFields.label({
                bind: 'description',
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
                isTitleHidden: true,
            }),
            ui.nestedFields.switch({
                bind: 'isActive',
                isTransient: true,
                onChange(rowId: any) {
                    this.setSelection(rowId, 21);
                },
            }),
            ui.nestedFields.multiDropdown({
                bind: 'permissions',
                width: 'large',
                options(value: string[], rowData: RoleActivityPod) {
                    return rowData.allPermissions ? rowData.allPermissions : null;
                },
                map(value: any, rowValue: any) {
                    return this.permissionMap[rowValue.name][value];
                },
            }),
        ],
    })
    activities22: ui.fields.PodCollection<Activity>;

    @ui.decorators.block<RoleSetup>({
        width: 'large',
        parent() {
            return this.activitySection;
        },
        isHidden() {
            return true;
        },
        title() {
            return '';
        },
    })
    packageBlock23: ui.containers.Block;

    @ui.decorators.podCollectionField<RoleSetup, ActivityWithTransientFields>({
        node: '@sage/xtrem-authorization/Activity',
        parent() {
            return this.packageBlock23;
        },
        recordWidth: 'large',
        isTransient: true,
        pageSize: 100,
        columns: [
            ui.nestedFields.text({ bind: '_id', isHidden: true }),
            ui.nestedFields.label({
                bind: 'description',
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
                isTitleHidden: true,
            }),
            ui.nestedFields.switch({
                bind: 'isActive',
                isTransient: true,
                onChange(rowId: any) {
                    this.setSelection(rowId, 22);
                },
            }),
            ui.nestedFields.multiDropdown({
                bind: 'permissions',
                width: 'large',
                options(value: string[], rowData: RoleActivityPod) {
                    return rowData.allPermissions ? rowData.allPermissions : null;
                },
                map(value: any, rowValue: any) {
                    return this.permissionMap[rowValue.name][value];
                },
            }),
        ],
    })
    activities23: ui.fields.PodCollection<Activity>;

    @ui.decorators.block<RoleSetup>({
        width: 'large',
        parent() {
            return this.activitySection;
        },
        isHidden() {
            return true;
        },
        title() {
            return '';
        },
    })
    packageBlock24: ui.containers.Block;

    @ui.decorators.podCollectionField<RoleSetup, ActivityWithTransientFields>({
        node: '@sage/xtrem-authorization/Activity',
        parent() {
            return this.packageBlock24;
        },
        recordWidth: 'large',
        isTransient: true,
        pageSize: 100,
        columns: [
            ui.nestedFields.text({ bind: '_id', isHidden: true }),
            ui.nestedFields.label({
                bind: 'description',
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
                isTitleHidden: true,
            }),
            ui.nestedFields.switch({
                bind: 'isActive',
                isTransient: true,
                onChange(rowId: any) {
                    this.setSelection(rowId, 23);
                },
            }),
            ui.nestedFields.multiDropdown({
                bind: 'permissions',
                width: 'large',
                options(value: string[], rowData: RoleActivityPod) {
                    return rowData.allPermissions ? rowData.allPermissions : null;
                },
                map(value: any, rowValue: any) {
                    return this.permissionMap[rowValue.name][value];
                },
            }),
        ],
    })
    activities24: ui.fields.PodCollection<Activity>;

    @ui.decorators.block<RoleSetup>({
        width: 'large',
        parent() {
            return this.activitySection;
        },
        isHidden() {
            return true;
        },
        title() {
            return '';
        },
    })
    packageBlock25: ui.containers.Block;

    @ui.decorators.podCollectionField<RoleSetup, ActivityWithTransientFields>({
        node: '@sage/xtrem-authorization/Activity',
        parent() {
            return this.packageBlock25;
        },
        recordWidth: 'large',
        isTransient: true,
        pageSize: 100,
        columns: [
            ui.nestedFields.text({ bind: '_id', isHidden: true }),
            ui.nestedFields.label({
                bind: 'description',
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
                isTitleHidden: true,
            }),
            ui.nestedFields.switch({
                bind: 'isActive',
                isTransient: true,
                onChange(rowId: any) {
                    this.setSelection(rowId, 24);
                },
            }),
            ui.nestedFields.multiDropdown({
                bind: 'permissions',
                width: 'large',
                options(value: string[], rowData: RoleActivityPod) {
                    return rowData.allPermissions ? rowData.allPermissions : null;
                },
                map(value: any, rowValue: any) {
                    return this.permissionMap[rowValue.name][value];
                },
            }),
        ],
    })
    activities25: ui.fields.PodCollection<Activity>;

    @ui.decorators.block<RoleSetup>({
        width: 'large',
        parent() {
            return this.activitySection;
        },
        isHidden() {
            return true;
        },
        title() {
            return '';
        },
    })
    packageBlock26: ui.containers.Block;

    @ui.decorators.podCollectionField<RoleSetup, ActivityWithTransientFields>({
        node: '@sage/xtrem-authorization/Activity',
        parent() {
            return this.packageBlock26;
        },
        recordWidth: 'large',
        isTransient: true,
        pageSize: 100,
        columns: [
            ui.nestedFields.text({ bind: '_id', isHidden: true }),
            ui.nestedFields.label({
                bind: 'description',
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
                isTitleHidden: true,
            }),
            ui.nestedFields.switch({
                bind: 'isActive',
                isTransient: true,
                onChange(rowId: any) {
                    this.setSelection(rowId, 25);
                },
            }),
            ui.nestedFields.multiDropdown({
                bind: 'permissions',
                width: 'large',
                options(value: string[], rowData: RoleActivityPod) {
                    return rowData.allPermissions ? rowData.allPermissions : null;
                },
                map(value: any, rowValue: any) {
                    return this.permissionMap[rowValue.name][value];
                },
            }),
        ],
    })
    activities26: ui.fields.PodCollection<Activity>;

    @ui.decorators.block<RoleSetup>({
        width: 'large',
        parent() {
            return this.activitySection;
        },
        isHidden() {
            return true;
        },
        title() {
            return '';
        },
    })
    packageBlock27: ui.containers.Block;

    @ui.decorators.podCollectionField<RoleSetup, ActivityWithTransientFields>({
        node: '@sage/xtrem-authorization/Activity',
        parent() {
            return this.packageBlock27;
        },
        recordWidth: 'large',
        isTransient: true,
        pageSize: 100,
        columns: [
            ui.nestedFields.text({ bind: '_id', isHidden: true }),
            ui.nestedFields.label({
                bind: 'description',
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
                isTitleHidden: true,
            }),
            ui.nestedFields.switch({
                bind: 'isActive',
                isTransient: true,
                onChange(rowId: any) {
                    this.setSelection(rowId, 26);
                },
            }),
            ui.nestedFields.multiDropdown({
                bind: 'permissions',
                width: 'large',
                options(value: string[], rowData: RoleActivityPod) {
                    return rowData.allPermissions ? rowData.allPermissions : null;
                },
                map(value: any, rowValue: any) {
                    return this.permissionMap[rowValue.name][value];
                },
            }),
        ],
    })
    activities27: ui.fields.PodCollection<Activity>;

    @ui.decorators.block<RoleSetup>({
        width: 'large',
        parent() {
            return this.activitySection;
        },
        isHidden() {
            return true;
        },
        title() {
            return '';
        },
    })
    packageBlock28: ui.containers.Block;

    @ui.decorators.podCollectionField<RoleSetup, ActivityWithTransientFields>({
        node: '@sage/xtrem-authorization/Activity',
        parent() {
            return this.packageBlock28;
        },
        recordWidth: 'large',
        isTransient: true,
        pageSize: 100,
        columns: [
            ui.nestedFields.text({ bind: '_id', isHidden: true }),
            ui.nestedFields.label({
                bind: 'description',
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
                isTitleHidden: true,
            }),
            ui.nestedFields.switch({
                bind: 'isActive',
                isTransient: true,
                onChange(rowId: any) {
                    this.setSelection(rowId, 27);
                },
            }),
            ui.nestedFields.multiDropdown({
                bind: 'permissions',
                width: 'large',
                options(value: string[], rowData: RoleActivityPod) {
                    return rowData.allPermissions ? rowData.allPermissions : null;
                },
                map(value: any, rowValue: any) {
                    return this.permissionMap[rowValue.name][value];
                },
            }),
        ],
    })
    activities28: ui.fields.PodCollection<Activity>;

    @ui.decorators.block<RoleSetup>({
        width: 'large',
        parent() {
            return this.activitySection;
        },
        isHidden() {
            return true;
        },
        title() {
            return '';
        },
    })
    packageBlock29: ui.containers.Block;

    @ui.decorators.podCollectionField<RoleSetup, ActivityWithTransientFields>({
        node: '@sage/xtrem-authorization/Activity',
        parent() {
            return this.packageBlock29;
        },
        recordWidth: 'large',
        isTransient: true,
        pageSize: 100,
        columns: [
            ui.nestedFields.text({ bind: '_id', isHidden: true }),
            ui.nestedFields.label({
                bind: 'description',
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
                isTitleHidden: true,
            }),
            ui.nestedFields.switch({
                bind: 'isActive',
                isTransient: true,
                onChange(rowId: any) {
                    this.setSelection(rowId, 28);
                },
            }),
            ui.nestedFields.multiDropdown({
                bind: 'permissions',
                width: 'large',
                options(value: string[], rowData: RoleActivityPod) {
                    return rowData.allPermissions ? rowData.allPermissions : null;
                },
                map(value: any, rowValue: any) {
                    return this.permissionMap[rowValue.name][value];
                },
            }),
        ],
    })
    activities29: ui.fields.PodCollection<Activity>;

    @ui.decorators.block<RoleSetup>({
        width: 'large',
        parent() {
            return this.activitySection;
        },
        isHidden() {
            return true;
        },
        title() {
            return '';
        },
    })
    packageBlock30: ui.containers.Block;

    @ui.decorators.podCollectionField<RoleSetup, ActivityWithTransientFields>({
        node: '@sage/xtrem-authorization/Activity',
        parent() {
            return this.packageBlock30;
        },
        recordWidth: 'large',
        isTransient: true,
        pageSize: 100,
        columns: [
            ui.nestedFields.text({ bind: '_id', isHidden: true }),
            ui.nestedFields.label({
                bind: 'description',
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
                isTitleHidden: true,
            }),
            ui.nestedFields.switch({
                bind: 'isActive',
                isTransient: true,
                onChange(rowId: any) {
                    this.setSelection(rowId, 29);
                },
            }),
            ui.nestedFields.multiDropdown({
                bind: 'permissions',
                width: 'large',
                options(value: string[], rowData: RoleActivityPod) {
                    return rowData.allPermissions ? rowData.allPermissions : null;
                },
                map(value: any, rowValue: any) {
                    return this.permissionMap[rowValue.name][value];
                },
            }),
        ],
    })
    activities30: ui.fields.PodCollection<Activity>;

    @ui.decorators.block<RoleSetup>({
        width: 'large',
        parent() {
            return this.activitySection;
        },
        isHidden() {
            return true;
        },
        title() {
            return '';
        },
    })
    packageBlock31: ui.containers.Block;

    @ui.decorators.podCollectionField<RoleSetup, ActivityWithTransientFields>({
        node: '@sage/xtrem-authorization/Activity',
        parent() {
            return this.packageBlock31;
        },
        recordWidth: 'large',
        isTransient: true,
        pageSize: 100,
        columns: [
            ui.nestedFields.text({ bind: '_id', isHidden: true }),
            ui.nestedFields.label({
                bind: 'description',
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
                isTitleHidden: true,
            }),
            ui.nestedFields.switch({
                bind: 'isActive',
                isTransient: true,
                onChange(rowId: any) {
                    this.setSelection(rowId, 30);
                },
            }),
            ui.nestedFields.multiDropdown({
                bind: 'permissions',
                width: 'large',
                options(value: string[], rowData: RoleActivityPod) {
                    return rowData.allPermissions ? rowData.allPermissions : null;
                },
                map(value: any, rowValue: any) {
                    return this.permissionMap[rowValue.name][value];
                },
            }),
        ],
    })
    activities31: ui.fields.PodCollection<Activity>;

    @ui.decorators.block<RoleSetup>({
        width: 'large',
        parent() {
            return this.activitySection;
        },
        isHidden() {
            return true;
        },
        title() {
            return '';
        },
    })
    packageBlock32: ui.containers.Block;

    @ui.decorators.podCollectionField<RoleSetup, ActivityWithTransientFields>({
        node: '@sage/xtrem-authorization/Activity',
        parent() {
            return this.packageBlock32;
        },
        recordWidth: 'large',
        isTransient: true,
        pageSize: 100,
        columns: [
            ui.nestedFields.text({ bind: '_id', isHidden: true }),
            ui.nestedFields.label({
                bind: 'description',
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
                isTitleHidden: true,
            }),
            ui.nestedFields.switch({
                bind: 'isActive',
                isTransient: true,
                onChange(rowId: any) {
                    this.setSelection(rowId, 31);
                },
            }),
            ui.nestedFields.multiDropdown({
                bind: 'permissions',
                width: 'large',
                options(value: string[], rowData: RoleActivityPod) {
                    return rowData.allPermissions ? rowData.allPermissions : null;
                },
                map(value: any, rowValue: any) {
                    return this.permissionMap[rowValue.name][value];
                },
            }),
        ],
    })
    activities32: ui.fields.PodCollection<Activity>;

    @ui.decorators.block<RoleSetup>({
        width: 'large',
        parent() {
            return this.activitySection;
        },
        isHidden() {
            return true;
        },
        title() {
            return '';
        },
    })
    packageBlock33: ui.containers.Block;

    @ui.decorators.podCollectionField<RoleSetup, ActivityWithTransientFields>({
        node: '@sage/xtrem-authorization/Activity',
        parent() {
            return this.packageBlock33;
        },
        recordWidth: 'large',
        isTransient: true,
        pageSize: 100,
        columns: [
            ui.nestedFields.text({ bind: '_id', isHidden: true }),
            ui.nestedFields.label({
                bind: 'description',
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
                isTitleHidden: true,
            }),
            ui.nestedFields.switch({
                bind: 'isActive',
                isTransient: true,
                onChange(rowId: any) {
                    this.setSelection(rowId, 32);
                },
            }),
            ui.nestedFields.multiDropdown({
                bind: 'permissions',
                width: 'large',
                options(value: string[], rowData: RoleActivityPod) {
                    return rowData.allPermissions ? rowData.allPermissions : null;
                },
                map(value: any, rowValue: any) {
                    return this.permissionMap[rowValue.name][value];
                },
            }),
        ],
    })
    activities33: ui.fields.PodCollection<Activity>;

    @ui.decorators.block<RoleSetup>({
        width: 'large',
        parent() {
            return this.activitySection;
        },
        isHidden() {
            return true;
        },
        title() {
            return '';
        },
    })
    packageBlock34: ui.containers.Block;

    @ui.decorators.podCollectionField<RoleSetup, ActivityWithTransientFields>({
        node: '@sage/xtrem-authorization/Activity',
        parent() {
            return this.packageBlock34;
        },
        recordWidth: 'large',
        isTransient: true,
        pageSize: 100,
        columns: [
            ui.nestedFields.text({ bind: '_id', isHidden: true }),
            ui.nestedFields.label({
                bind: 'description',
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
                isTitleHidden: true,
            }),
            ui.nestedFields.switch({
                bind: 'isActive',
                isTransient: true,
                onChange(rowId: any) {
                    this.setSelection(rowId, 33);
                },
            }),
            ui.nestedFields.multiDropdown({
                bind: 'permissions',
                width: 'large',
                options(value: string[], rowData: RoleActivityPod) {
                    return rowData.allPermissions ? rowData.allPermissions : null;
                },
                map(value: any, rowValue: any) {
                    return this.permissionMap[rowValue.name][value];
                },
            }),
        ],
    })
    activities34: ui.fields.PodCollection<Activity>;

    @ui.decorators.block<RoleSetup>({
        width: 'large',
        parent() {
            return this.activitySection;
        },
        isHidden() {
            return true;
        },
        title() {
            return '';
        },
    })
    packageBlock35: ui.containers.Block;

    @ui.decorators.podCollectionField<RoleSetup, ActivityWithTransientFields>({
        node: '@sage/xtrem-authorization/Activity',
        parent() {
            return this.packageBlock35;
        },
        recordWidth: 'large',
        isTransient: true,
        pageSize: 100,
        columns: [
            ui.nestedFields.text({ bind: '_id', isHidden: true }),
            ui.nestedFields.label({
                bind: 'description',
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
                isTitleHidden: true,
            }),
            ui.nestedFields.switch({
                bind: 'isActive',
                isTransient: true,
                onChange(rowId: any) {
                    this.setSelection(rowId, 34);
                },
            }),
            ui.nestedFields.multiDropdown({
                bind: 'permissions',
                width: 'large',
                options(value: string[], rowData: RoleActivityPod) {
                    return rowData.allPermissions ? rowData.allPermissions : null;
                },
                map(value: any, rowValue: any) {
                    return this.permissionMap[rowValue.name][value];
                },
            }),
        ],
    })
    activities35: ui.fields.PodCollection<Activity>;

    @ui.decorators.block<RoleSetup>({
        width: 'large',
        parent() {
            return this.activitySection;
        },
        isHidden() {
            return true;
        },
        title() {
            return '';
        },
    })
    packageBlock36: ui.containers.Block;

    @ui.decorators.podCollectionField<RoleSetup, ActivityWithTransientFields>({
        node: '@sage/xtrem-authorization/Activity',
        parent() {
            return this.packageBlock36;
        },
        recordWidth: 'large',
        isTransient: true,
        pageSize: 100,
        columns: [
            ui.nestedFields.text({ bind: '_id', isHidden: true }),
            ui.nestedFields.label({
                bind: 'description',
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
                isTitleHidden: true,
            }),
            ui.nestedFields.switch({
                bind: 'isActive',
                isTransient: true,
                onChange(rowId: any) {
                    this.setSelection(rowId, 35);
                },
            }),
            ui.nestedFields.multiDropdown({
                bind: 'permissions',
                width: 'large',
                options(value: string[], rowData: RoleActivityPod) {
                    return rowData.allPermissions ? rowData.allPermissions : null;
                },
                map(value: any, rowValue: any) {
                    return this.permissionMap[rowValue.name][value];
                },
            }),
        ],
    })
    activities36: ui.fields.PodCollection<Activity>;

    @ui.decorators.block<RoleSetup>({
        width: 'large',
        parent() {
            return this.activitySection;
        },
        isHidden() {
            return true;
        },
        title() {
            return '';
        },
    })
    packageBlock37: ui.containers.Block;

    @ui.decorators.podCollectionField<RoleSetup, ActivityWithTransientFields>({
        node: '@sage/xtrem-authorization/Activity',
        parent() {
            return this.packageBlock37;
        },
        recordWidth: 'large',
        isTransient: true,
        pageSize: 100,
        columns: [
            ui.nestedFields.text({ bind: '_id', isHidden: true }),
            ui.nestedFields.label({
                bind: 'description',
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
                isTitleHidden: true,
            }),
            ui.nestedFields.switch({
                bind: 'isActive',
                isTransient: true,
                onChange(rowId: any) {
                    this.setSelection(rowId, 36);
                },
            }),
            ui.nestedFields.multiDropdown({
                bind: 'permissions',
                width: 'large',
                options(value: string[], rowData: RoleActivityPod) {
                    return rowData.allPermissions ? rowData.allPermissions : null;
                },
                map(value: any, rowValue: any) {
                    return this.permissionMap[rowValue.name][value];
                },
            }),
        ],
    })
    activities37: ui.fields.PodCollection<Activity>;

    @ui.decorators.block<RoleSetup>({
        width: 'large',
        parent() {
            return this.activitySection;
        },
        isHidden() {
            return true;
        },
        title() {
            return '';
        },
    })
    packageBlock38: ui.containers.Block;

    @ui.decorators.podCollectionField<RoleSetup, ActivityWithTransientFields>({
        node: '@sage/xtrem-authorization/Activity',
        parent() {
            return this.packageBlock38;
        },
        recordWidth: 'large',
        isTransient: true,
        pageSize: 100,
        columns: [
            ui.nestedFields.text({ bind: '_id', isHidden: true }),
            ui.nestedFields.label({
                bind: 'description',
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
                isTitleHidden: true,
            }),
            ui.nestedFields.switch({
                bind: 'isActive',
                isTransient: true,
                onChange(rowId: any) {
                    this.setSelection(rowId, 37);
                },
            }),
            ui.nestedFields.multiDropdown({
                bind: 'permissions',
                width: 'large',
                options(value: string[], rowData: RoleActivityPod) {
                    return rowData.allPermissions ? rowData.allPermissions : null;
                },
                map(value: any, rowValue: any) {
                    return this.permissionMap[rowValue.name][value];
                },
            }),
        ],
    })
    activities38: ui.fields.PodCollection<Activity>;

    @ui.decorators.block<RoleSetup>({
        width: 'large',
        parent() {
            return this.activitySection;
        },
        isHidden() {
            return true;
        },
        title() {
            return '';
        },
    })
    packageBlock39: ui.containers.Block;

    @ui.decorators.podCollectionField<RoleSetup, ActivityWithTransientFields>({
        node: '@sage/xtrem-authorization/Activity',
        parent() {
            return this.packageBlock39;
        },
        recordWidth: 'large',
        isTransient: true,
        pageSize: 100,
        columns: [
            ui.nestedFields.text({ bind: '_id', isHidden: true }),
            ui.nestedFields.label({
                bind: 'description',
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
                isTitleHidden: true,
            }),
            ui.nestedFields.switch({
                bind: 'isActive',
                isTransient: true,
                onChange(rowId: any) {
                    this.setSelection(rowId, 38);
                },
            }),
            ui.nestedFields.multiDropdown({
                bind: 'permissions',
                width: 'large',
                options(value: string[], rowData: RoleActivityPod) {
                    return rowData.allPermissions ? rowData.allPermissions : null;
                },
                map(value: any, rowValue: any) {
                    return this.permissionMap[rowValue.name][value];
                },
            }),
        ],
    })
    activities39: ui.fields.PodCollection<Activity>;

    @ui.decorators.block<RoleSetup>({
        width: 'large',
        parent() {
            return this.activitySection;
        },
        isHidden() {
            return true;
        },
        title() {
            return '';
        },
    })
    packageBlock40: ui.containers.Block;

    @ui.decorators.podCollectionField<RoleSetup, ActivityWithTransientFields>({
        node: '@sage/xtrem-authorization/Activity',
        parent() {
            return this.packageBlock40;
        },
        recordWidth: 'large',
        isTransient: true,
        pageSize: 100,
        columns: [
            ui.nestedFields.text({ bind: '_id', isHidden: true }),
            ui.nestedFields.label({
                bind: 'description',
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
                isTitleHidden: true,
            }),
            ui.nestedFields.switch({
                bind: 'isActive',
                isTransient: true,
                onChange(rowId: any) {
                    this.setSelection(rowId, 39);
                },
            }),
            ui.nestedFields.multiDropdown({
                bind: 'permissions',
                width: 'large',
                options(value: string[], rowData: RoleActivityPod) {
                    return rowData.allPermissions ? rowData.allPermissions : null;
                },
                map(value: any, rowValue: any) {
                    return this.permissionMap[rowValue.name][value];
                },
            }),
        ],
    })
    activities40: ui.fields.PodCollection<Activity>;

    @ui.decorators.pageAction<RoleSetup>({
        title: 'Save',
        isHidden: true,
        async onClick() {
            const validation = await this.$.page.validate();
            if (validation.length === 0) {
                this.$.loader.isHidden = false;
                let allRoleActivities: RoleActivityMutation[] = [];

                const activityPods = this.getNumberOfKeys('activities');

                for (let packageCounter = 0; packageCounter <= this.numberPackages; packageCounter += 1) {
                    const activityPod = activityPods[packageCounter] as keyof RoleSetup;

                    const roleActivities = (this[activityPod] as ui.fields.PodCollection).value.map(
                        (line: ActivityWithTransientFields) => {
                            return {
                                activity: line.activityId ? line.activityId : line._id,
                                permissions: line.permissions,
                                isActive: !!(line.permissions && line.permissions.length > 0),
                                hasAllPermissions: false, // TODO: Add checkbox for all permissions to screen
                            };
                        },
                    );

                    allRoleActivities = [...allRoleActivities, ...roleActivities].filter(
                        role => role.hasAllPermissions || role.isActive || role.permissions?.length > 0,
                    );
                }

                try {
                    const result = await this.$.graph
                        .node('@sage/xtrem-authorization/RoleActivity')
                        .mutations.roleActivitiesCreateUpdate(true, {
                            roleActivities: allRoleActivities,
                            roleSysId: this.roleSysId === 0 ? null : this.roleSysId.toString(),
                            roleId: this.id.value,
                            roleName: this.name.value,
                        })
                        .execute();

                    if (result) {
                        this.$.showToast(ui.localize('@sage/xtrem-authorization/pages__role_setup', 'Role created'), {
                            type: 'success',
                        });
                    } else {
                        this.$.showToast(
                            ui.localize('@sage/xtrem-authorization/pages__role_setup_failed', 'Role creation failed.'),
                            { type: 'error' },
                        );
                    }

                    if (this.$.isDirty) {
                        this.$.setPageClean();
                    }

                    this.$.finish({ result });
                } catch (error) {
                    this.$.loader.isHidden = true;
                    this.$.showToast(error.message, { timeout: 10000, type: 'error' });
                }
            } else {
                this.$.showToast(validation.join('\n'), { type: 'error' });
            }
            this.$.loader.isHidden = true;
        },
    })
    confirm: ui.PageAction;

    @ui.decorators.pageAction<RoleSetup>({
        title: 'Continue',

        async onClick() {
            const validation = await this.$.page.validate();
            if (validation.length === 0) {
                this.roleSection.isHidden = true;
                this.roleBlock.isHidden = true;
                this.showSelectPackages();
                this.next.isHidden = true;
                this.confirm.isHidden = false;
            } else {
                this.$.showToast(validation.join('\n'), { type: 'error' });
            }
        },
    })
    next: ui.PageAction;

    @ui.decorators.pageAction<RoleSetup>({
        title: 'Cancel',
        buttonType: 'tertiary',
        onClick() {
            this.$.finish();
        },
    })
    cancel: ui.PageAction;

    /**
     * @param component : Returns an array of strings of components found on the page that starts with the @param
     */
    getNumberOfKeys(component: string) {
        return Object.keys(Object.getPrototypeOf(this)).filter(key => key.startsWith(component));
    }

    permissionMap: Dict<Dict<string>> = {};

    async loadPackages() {
        const packages = await getPackages(this);
        const activities = await getActivities(this);
        let numberPackages = -1;
        const packageNames: string[] = [];
        const packageBlocks = this.getNumberOfKeys('packageBlock');
        const activityPods = this.getNumberOfKeys('activities');

        packages.forEach((packageDetails: Partial<Dict<any>>) => {
            const packageActivities = activities.filter((activity: any) => activity.package._id === packageDetails._id);
            if (packageActivities.length > 0 && packageBlocks.length > numberPackages) {
                numberPackages += 1;

                const packageBlock = packageBlocks[numberPackages] as keyof RoleSetup;
                const packageName = packageDetails.title;
                packageNames.push(packageName);
                (this[packageBlock] as ui.containers.Block).title = packageName;
            }
            packageActivities.forEach((activity: any) => {
                if (activityPods.length > numberPackages) {
                    this.permissionMap[activity.name] = {};
                    activity.permissions.forEach((permission: any) => {
                        this.permissionMap[activity.name][permission.name] = permission.title;
                    });
                    activity.description = activity.title;
                    activity.allPermissions = activity.permissions.map((permission: any) => permission.name);
                    activity.permissions = null;
                    activity.isActive = false;
                    const activityPod = activityPods[numberPackages] as keyof RoleSetup;
                    (this[activityPod] as ui.fields.PodCollection).addOrUpdateRecordValue(activity);
                }
            });
        });

        this.numberPackages = numberPackages;
        this.packageNames = packageNames;
    }

    async showSelectPackages() {
        const packageBlocks = this.getNumberOfKeys('packageBlock');

        for (let blockCounter = 0; blockCounter < packageBlocks.length; blockCounter += 1) {
            const packageBlock = packageBlocks[blockCounter] as keyof RoleSetup;
            const blockTitle = (this[packageBlock] as ui.containers.Block).title;

            if (this.packageSelect.value.includes(blockTitle)) {
                (this[packageBlock] as ui.containers.Block).isHidden = false;
            }
        }
    }

    /**
     * Updates row permissions in pod collection if you activate/deactive
     * @param rowId : Identifies the row which is being activiated/deactivted
     * @param activityPodNumber : Identifies the pod
     *
     */
    setSelection(rowId: any, activityPodNumber: number) {
        const activityPods = this.getNumberOfKeys('activities');
        const activityPod = activityPods[activityPodNumber] as keyof RoleSetup;

        const rowData = (this[activityPod] as ui.fields.PodCollection).getRecordValue(rowId);

        rowData.permissions = rowData.isActive ? rowData.allPermissions : null;
        (this[activityPod] as ui.fields.PodCollection).addOrUpdateRecordValue(rowData);
    }
}
