import { GraphApi, GroupRoleSite, SiteGroupToSite, SiteGroupToSiteGroup } from '@sage/xtrem-authorization-api';
import { withoutEdges } from '@sage/xtrem-client';
import { Site } from '@sage/xtrem-system-api';
import {
    setApplicativePageCrudActions,
    setOrderOfPageBusinessActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import { getApplicativePageTitle } from '@sage/xtrem-system/build/lib/client-functions/applicative-page-title';
import * as ui from '@sage/xtrem-ui';
import { SiteGroupPage, allSiteGroupPages } from './shared/common';

@ui.decorators.page<SiteGroup>({
    title: 'Site group',
    subtitle: 'Site group',
    node: '@sage/xtrem-authorization/SiteGroup',
    module: 'system',
    mode: 'tabs',
    priority: 500,
    onLoad() {
        this.$.page.title = getApplicativePageTitle(this.name.value);

        this.siteGroupsMultiReference.value = this.siteGroups.value.map(element => {
            return { _id: element.siteGroup._id, id: element.siteGroup.id, name: element.siteGroup.name };
        });
        this.sitesMultiReference.value = this.sites.value.map(element => {
            return { _id: element.site._id, id: element.site.id, name: element.site.name };
        });

        this.toggleChartBlock.isHidden = !this.$.recordId;
        this.toggleChart.value = `${ui.localize('@sage/xtrem-authorization/pages__site_group_grid', 'Grid')}`;
        const page = `${this.$.queryParameters.page}` as SiteGroupPage;

        if (allSiteGroupPages.includes(page)) {
            this.setPanel(page);
        } else {
            setApplicativePageCrudActions({ page: this, save: this.customSave, cancel: this.$standardCancelAction });
        }
        this.setGroupRoleSiteTable(this._id.value);
        this.$.setPageClean();
    },
    navigationPanel: {
        listItem: {
            titleRight: ui.nestedFields.text({ bind: 'name' }),
            title: ui.nestedFields.text({ bind: 'id' }),
        },
    },
    businessActions() {
        return setOrderOfPageBusinessActions({
            save: this.customSave,
            cancel: this.$standardCancelAction,
            businessActions: [this.confirm, this.siteGroupList, this.editSiteGroup],
        });
    },

    createAction() {
        return this.$standardNewAction;
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction],
        });
    },
    headerQuickActions() {
        return [this.$standardDuplicateAction];
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.customSave,
            cancel: this.$standardCancelAction,
        });
    },
})
export class SiteGroup extends ui.Page<GraphApi> {
    @ui.decorators.section<SiteGroup>({
        title: 'General',
    })
    generalSection: ui.containers.Section;

    @ui.decorators.section<SiteGroup>({
        title: 'User groups',
    })
    associatedAuthorizationGroupSection: ui.containers.Section;

    @ui.decorators.block<SiteGroup>({
        parent() {
            return this.generalSection;
        },
        title: 'Site group',
        isTitleHidden: true,
        width: 'large',
    })
    generalBlock: ui.containers.Block;

    @ui.decorators.block<SiteGroup>({
        title: 'Toggle chart/grid',
        isTitleHidden: true,
        parent() {
            return this.generalSection;
        },
        width: 'large',
    })
    toggleChartBlock: ui.containers.Block;

    @ui.decorators.block<SiteGroup>({
        parent() {
            return this.generalSection;
        },
        title: 'Site groups',
        width: 'large',
    })
    siteGroupsBlock: ui.containers.Block;

    @ui.decorators.block<SiteGroup>({
        parent() {
            return this.generalSection;
        },
        title: 'Sites',
        width: 'large',
    })
    sitesBlock: ui.containers.Block;

    @ui.decorators.block<SiteGroup>({
        title: 'Organization',
        isTitleHidden: true,
        parent() {
            return this.generalSection;
        },
        width: 'large',
        isHidden: true,
    })
    chartBlock: ui.containers.Block;

    @ui.decorators.textField<SiteGroup>({
        parent() {
            return this.generalBlock;
        },
        title: 'ID',
        isHidden: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.switchField<SiteGroup>({
        parent() {
            return this.generalBlock;
        },
        title: 'Active',
        width: 'small',
    })
    isActive: ui.fields.Switch;

    @ui.decorators.separatorField<SiteGroup>({
        parent() {
            return this.generalBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    separatorFromIsActive: ui.fields.Separator;

    @ui.decorators.textField<SiteGroup>({
        parent() {
            return this.generalBlock;
        },
        title: 'Name',
        isMandatory: true,
    })
    name: ui.fields.Text;

    @ui.decorators.textField<SiteGroup>({
        parent() {
            return this.generalBlock;
        },
        title: 'ID',
        isMandatory: true,
    })
    id: ui.fields.Text;

    @ui.decorators.checkboxField<SiteGroup>({
        parent() {
            return this.generalBlock;
        },
        title: 'Legal company',
        width: 'small',
        isReadOnly: true,
    })
    isLegalCompany: ui.fields.Checkbox;

    @ui.decorators.toggleField<SiteGroup>({
        parent() {
            return this.toggleChartBlock;
        },
        title: 'Display options',
        width: 'large',
        isTransient: true,
        helperText: 'Allow user to toggle grid or chart display',
        onChange() {
            this.chartBlock.isHidden = !this.chartBlock.isHidden;
            this.siteGroupsBlock.isHidden = !this.chartBlock.isHidden;
            this.sitesBlock.isHidden = !this.chartBlock.isHidden;
        },
        options: [
            `${ui.localize('@sage/xtrem-authorization/pages__site_group_grid', 'Grid')}`,
            `${ui.localize('@sage/xtrem-authorization/pages__site_group_chart', 'Chart')}`,
        ],
    })
    toggleChart: ui.fields.Toggle;

    @ui.decorators.multiReferenceField<SiteGroup>({
        isTransient: true,
        lookupDialogTitle: 'Select site groups',
        minLookupCharacters: 0,
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Name', canFilter: true }),
            ui.nestedFields.text({ bind: 'id', title: 'ID', canFilter: true }),
            ui.nestedFields.text({ bind: '_id', title: '_id', isHidden: true }),
        ],
        node: '@sage/xtrem-authorization/SiteGroup',
        onClick() {},
        parent() {
            return this.siteGroupsBlock;
        },
        isFullWidth: true,
        valueField: 'name',
        helperTextField: 'id',
        onChange() {
            this.siteGroups.isHidden = true;
            this.clearSiteGroupsTable();
            this.populateSiteGroupsTable();
        },
        isReadOnly() {
            return this.isLegalCompany.value;
        },
    })
    siteGroupsMultiReference: ui.fields.MultiReference;

    /** Hidden - technical  */
    @ui.decorators.tableField<SiteGroup, SiteGroupToSiteGroup>({
        node: '@sage/xtrem-authorization/SiteGroupToSiteGroup',
        bind: 'siteGroups',
        canSelect: false,
        isHidden: true,
        parent() {
            return this.siteGroupsBlock;
        },
        columns: [
            ui.nestedFields.reference({
                bind: 'siteGroup',
                title: 'ID',
                node: '@sage/xtrem-authorization/SiteGroup',
                valueField: '_id',
                size: 'small',
                isReadOnly: true,
                isHidden: true,
            }),
            ui.nestedFields.reference({
                bind: 'siteGroup',
                title: 'Name',
                node: '@sage/xtrem-authorization/SiteGroup',
                valueField: 'name',
                size: 'small',
                isReadOnly: true,
            }),
            ui.nestedFields.reference({
                bind: 'siteGroup',
                title: 'ID',
                node: '@sage/xtrem-authorization/SiteGroup',
                valueField: 'id',
                size: 'small',
                isReadOnly: true,
            }),
        ],
        isDisabled() {
            return this.isLegalCompany.value;
        },
    })
    siteGroups: ui.fields.Table<SiteGroupToSiteGroup>;

    @ui.decorators.multiReferenceField<SiteGroup, Site>({
        isTransient: true,
        lookupDialogTitle: 'Select sites',
        minLookupCharacters: 0,
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Name', canFilter: true }),
            ui.nestedFields.text({ bind: 'id', title: 'ID', canFilter: true }),
            ui.nestedFields.text({ bind: '_id', title: '_id', isHidden: true }),
        ],
        node: '@sage/xtrem-system/Site',
        onClick() {},
        parent() {
            return this.sitesBlock;
        },
        isFullWidth: true,
        valueField: 'name',
        helperTextField: 'id',
        onChange() {
            this.sites.isHidden = true;
            this.clearSitesTable();
            this.populateSitesTable();
        },
        isReadOnly() {
            return this.isLegalCompany.value;
        },
    })
    sitesMultiReference: ui.fields.MultiReference;

    /** Hidden - technical  */
    @ui.decorators.tableField<SiteGroup, SiteGroupToSite>({
        node: '@sage/xtrem-authorization/SiteGroupToSite',
        bind: 'sites',
        isHidden: true,
        canSelect: false,
        pageSize: 200,
        parent() {
            return this.sitesBlock;
        },
        columns: [
            ui.nestedFields.reference({
                bind: 'site',
                title: 'ID',
                node: '@sage/xtrem-system/Site',
                valueField: '_id',
                size: 'small',
                isHidden: true,
            }),
            ui.nestedFields.reference({
                bind: 'site',
                title: 'Name',
                node: '@sage/xtrem-system/Site',
                valueField: 'name',
                size: 'small',
                isReadOnly: true,
            }),
            ui.nestedFields.reference({
                bind: 'site',
                title: 'ID',
                node: '@sage/xtrem-system/Site',
                valueField: 'id',
                size: 'small',
                isReadOnly: true,
            }),
        ],
        isDisabled() {
            return this.isLegalCompany.value;
        },
    })
    sites: ui.fields.Table<SiteGroupToSite>;

    @ui.decorators.visualProcessField<SiteGroup>({
        parent() {
            return this.chartBlock;
        },
        title: 'Organization',
        isReadOnly: true,
        isFullWidth: true,
    })
    hierarchyChartContent: ui.fields.VisualProcess;

    @ui.decorators.tableField<SiteGroup, GroupRoleSite>({
        pageSize: 20,
        title: 'Authorization group',
        width: 'large',
        node: '@sage/xtrem-authorization/GroupRoleSite',
        isTitleHidden: false,
        canSelect: false,
        isTransient: true,
        columns: [
            ui.nestedFields.link({
                bind: 'name',
                title: 'Name',
                page: '@sage/xtrem-authorization/GroupRoleSite',
                queryParameters(_value, rowData) {
                    return {
                        _id: rowData._id,
                        groupName: rowData.name,
                        page: 'detailGroupRoleSite',
                    };
                },
            }),
            ui.nestedFields.text({
                bind: '_id',
                title: 'ID',
                isHidden: true,
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'id',
                title: 'ID',
                isHidden: true,
            }),
            ui.nestedFields.text({
                bind: 'createdByUser' as any,
                title: 'Created by',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'updatedByUser' as any,
                title: 'Updated by',
                isReadOnly: true,
            }),
        ],
        orderBy: {
            _id: 1,
        },
        parent() {
            return this.associatedAuthorizationGroupSection;
        },
    })
    groups: ui.fields.Table<GroupRoleSite>;

    @ui.decorators.pageAction<SiteGroup>({
        title: 'Site group list',
        isHidden: true,
        isDisabled: true,
        onClick() {
            this.$.router.goTo('@sage/xtrem-authorization/SiteGroupList', {});
        },
    })
    siteGroupList: ui.PageAction;

    @ui.decorators.pageAction<SiteGroup>({
        title: 'Edit site group',
        isHidden: true,
        isDisabled: true,
        onClick() {
            this.$.router.goTo('@sage/xtrem-authorization/SiteGroup', {
                _id: this._id.value,
                page: 'editSiteGroup',
            });
        },
    })
    editSiteGroup: ui.PageAction;

    @ui.decorators.pageAction<SiteGroup>({
        title: 'Save',
        async onClick() {
            delete this.$.values.hierarchyChartContent;
            this.siteGroups.isHidden = false;
            this.sites.isHidden = false;
            await this.$standardSaveAction.execute();
        },
    })
    customSave: ui.PageAction;

    @ui.decorators.pageAction<SiteGroup>({
        title: 'Save',
        isHidden: true,
        isDisabled: true,
        onClick() {
            delete this.$.values.hierarchyChartContent;
            this.siteGroups.isHidden = false;
            this.sites.isHidden = false;
            return this.$standardSaveAction.execute(true);
        },
    })
    confirm: ui.PageAction;

    private clearSiteGroupsTable() {
        this.siteGroups.value.forEach((gridLine: any) => {
            this.siteGroups.removeRecord(gridLine._id);
        });
    }

    private populateSiteGroupsTable() {
        this.siteGroupsMultiReference.value
            .map(element => {
                return {
                    siteGroup: {
                        _id: element._id,
                        id: element.id,
                        name: element.name,
                    },
                };
            })
            .forEach((gridLine: any) => {
                this.siteGroups.addOrUpdateRecordValue(gridLine);
            });
    }

    private clearSitesTable() {
        this.sites.value.forEach((gridLine: any) => {
            this.sites.removeRecord(gridLine._id);
        });
    }

    private populateSitesTable() {
        this.sitesMultiReference.value
            .map(element => {
                return {
                    site: {
                        _id: element._id,
                        id: element.id,
                        name: element.name,
                    },
                };
            })
            .forEach((gridLine: any) => {
                this.sites.addOrUpdateRecordValue(gridLine);
            });
    }

    private setPanel(page: SiteGroupPage) {
        switch (page) {
            case 'siteGroupDetail':
                this.siteGroupList.isDisabled = false;
                this.siteGroupList.isHidden = false;
                this.editSiteGroup.isDisabled = false;
                this.editSiteGroup.isHidden = false;
                this.sites.isHidden = false;
                this.siteGroups.isHidden = false;
                this.$standardCancelAction.isDisabled = true;
                this.$standardCancelAction.isHidden = true;
                this.customSave.isDisabled = true;
                this.customSave.isHidden = true;
                this.$standardNewAction.isDisabled = true;
                this.$standardNewAction.isHidden = true;
                this.$standardDeleteAction.isDisabled = true;
                this.$standardDeleteAction.isHidden = true;
                this.detailSiteGroupReadOnly();
                break;
            case 'editSiteGroup':
                this.siteGroupList.isHidden = false;
                this.siteGroups.isHidden = true;
                this.siteGroups.isDisabled = true;
                this.siteGroups.isHidden = true;
                this.sites.isDisabled = true;
                this.sites.isHidden = true;
                break;
            case 'newSiteGroup':
                this.siteGroups.isDisabled = true;
                this.siteGroups.isHidden = true;
                this.sites.isDisabled = true;
                this.sites.isHidden = true;

                this.$standardNewAction.isDisabled = true;
                this.$standardNewAction.isHidden = true;
                this.$standardCancelAction.isDisabled = true;
                this.$standardCancelAction.isHidden = true;
                this.$standardDeleteAction.isDisabled = true;
                this.$standardDeleteAction.isHidden = true;
                this.customSave.isDisabled = true;
                this.customSave.isHidden = true;

                this.confirm.isHidden = false;
                this.confirm.isDisabled = false;
                this.$.page.subtitle = '';
                this.$.page.title = `${ui.localize(
                    '@sage/xtrem-authorization/pages__site_group_new_title',
                    'New site group',
                )}`;

                break;
            default:
                break;
        }
    }

    private detailSiteGroupReadOnly() {
        this.isActive.isReadOnly = true;
        this.id.isReadOnly = true;
        this.name.isReadOnly = true;
        this.isLegalCompany.isReadOnly = true;
        this.siteGroupsMultiReference.isReadOnly = true;
        this.sitesMultiReference.isReadOnly = true;
    }

    /**
     * Populate the groups table
     */
    private async setGroupRoleSiteTable(groupId: string) {
        const result = withoutEdges(
            await this.$.graph
                .node('@sage/xtrem-authorization/GroupRoleSite')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            _id: true,
                            id: true,
                            name: true,
                            createdBy: true,
                            createStamp: true,
                            updatedBy: true,
                            updateStamp: true,
                        },
                        {
                            first: 500,
                            filter: {
                                groupSites: {
                                    _atLeast: 1,
                                    siteGroup: {
                                        _id: { _eq: groupId },
                                    },
                                },
                            },
                        },
                    ),
                )
                .execute(),
        );

        this.groups.value = result.map(element => {
            return {
                _id: element._id,
                id: element.id,
                name: element.name,
                createdByUser: `${element.createdBy} ${element.createStamp}`,
                updatedByUser: `${element.updatedBy} ${element.updateStamp}`,
            };
        });
    }
}
