import { Graph<PERSON>pi, GroupRoleSite, Role, UserGroup } from '@sage/xtrem-authorization-api';
import { extractEdges, querySelector } from '@sage/xtrem-client';
import {
    setApplicativePageCrudActions,
    setOrderOfPageBusinessActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import * as authorizationFilters from '../client-functions/filters';
import { userData } from '../menu-items/user-data';
import { UserPage, allUserPages, confirmDialogWithAcceptButtonText } from './shared/common';
import { DefaultNameIdNode } from './shared/interfaces';

@ui.decorators.page<User>({
    title: 'Users',
    subtitle: 'User',
    objectTypeSingular: 'User',
    objectTypePlural: 'Users',
    idField() {
        if (!this.firstName.value || !this.lastName.value) {
            return null;
        }
        return `${this.firstName.value} ${this.lastName.value}`;
    },
    node: '@sage/xtrem-system/User',
    access: {
        bind: '$create',
    },
    module: 'system',
    menuItem: userData,
    hasAttachmentsSection: true,
    mode: 'tabs',
    priority: 100,
    navigationPanel: {
        bulkActions: [
            {
                id: 'sendBulkWelcomeMail',
                title: 'Send welcome mail',
                mutation: 'sendBulkWelcomeMail',
                icon: 'email',
            },
        ],
        listItem: {
            image: ui.nestedFields.image({ bind: 'photo', title: 'Image', placeholderMode: 'Initials' }),
            title: ui.nestedFields.text({ bind: 'firstName', title: 'First name' }),
            titleRight: ui.nestedFields.text({ bind: 'lastName', title: 'Last name' }),
            line2: ui.nestedFields.text({ bind: 'email', title: 'Email', canFilter: true }),
            line3: ui.nestedFields.switch({
                bind: 'isApiUser',
                title: 'API user',
                canFilter: true,
                isHiddenDesktop: true,
            }),
        },
        optionsMenu: [
            {
                title: 'All',
                graphQLFilter: authorizationFilters.user.allUsers,
            },
            {
                title: 'Interactive',
                graphQLFilter: authorizationFilters.user.interactiveUsers,
            },
            {
                title: 'Third-Party',
                graphQLFilter: authorizationFilters.user.apiUsers,
            },
        ],
    },
    async onLoad() {
        this.page = this.$.queryParameters.page as UserPage;
        if (this.page && allUserPages.includes(this.page)) {
            this.setPanel(this.page);
        } else {
            setApplicativePageCrudActions({ page: this, save: this.save, cancel: this.$standardCancelAction });
        }
        await this.setUserGroups();
        await this.initAssociatedTabs();
        this.setDefaultOperatorSettings();
        this.$.setPageClean();
    },
    businessActions() {
        return setOrderOfPageBusinessActions({
            save: this.save,
            cancel: this.$standardCancelAction,
            businessActions: [this.userList, this.editUser],
        });
    },
    createAction() {
        return this.$standardNewAction;
    },
    headerQuickActions() {
        return [this.$standardDuplicateAction];
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            dropDownBusinessActions: [],
        });
    },
    onDirtyStateUpdated(isDirty: boolean) {
        if (!['userListPanelDetail'].includes(this.page || '')) {
            setApplicativePageCrudActions({ page: this, isDirty, save: this.save, cancel: this.$standardCancelAction });
        }
    },
})
export class User extends ui.Page<GraphApi> {
    /** From queryParameters
     *  - userListPanelDetail from user list on click displayName
     *  - userListPanelNewUser from user list add user
     *  - editUser
     *  - undefined for normal page */
    page: UserPage;

    @ui.decorators.section<User>({
        title: 'General',
        isTitleHidden: true,
    })
    generalSection: ui.containers.Section;

    @ui.decorators.block<User>({
        parent() {
            return this.generalSection;
        },
        title: 'User information',
        isTitleHidden: true,
        width: 'large',
    })
    userInformationBlock: ui.containers.Block;

    @ui.decorators.block<User>({
        parent() {
            return this.generalSection;
        },
        title: 'Photo',
        width: 'small',
    })
    userPhotoBlock: ui.containers.Block;

    @ui.decorators.block<User>({
        parent() {
            return this.generalSection;
        },
        title: 'Authorization group',
        width: 'extra-large',
        isTitleHidden: true,
    })
    userAuthorizationInformationBlock: ui.containers.Block;

    @ui.decorators.textField<User>({
        title: 'ID',
        isHidden: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.switchField<User>({
        parent() {
            return this.userInformationBlock;
        },
        title: 'Active',
        width: 'small',
    })
    isActive: ui.fields.Switch;

    @ui.decorators.separatorField<User>({
        parent() {
            return this.userInformationBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    separatorFromIsActive: ui.fields.Separator;

    @ui.decorators.textField<User>({
        parent() {
            return this.userInformationBlock;
        },
        title: 'First name',
        isMandatory: true,
        width: 'medium',
    })
    firstName: ui.fields.Text;

    @ui.decorators.textField<User>({
        parent() {
            return this.userInformationBlock;
        },
        title: 'Last name',
        isMandatory: true,
        width: 'medium',
    })
    lastName: ui.fields.Text;

    @ui.decorators.separatorField<User>({
        parent() {
            return this.userInformationBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    separatorFromLastName: ui.fields.Separator;

    @ui.decorators.textField<User>({
        parent() {
            return this.userInformationBlock;
        },
        title: 'Email',
        isMandatory: true,
        width: 'medium',
        isReadOnly() {
            //  Once a user has been created, the Email field should not be enabled:
            return !!this.$.recordId;
        },
    })
    email: ui.fields.Text;

    @ui.decorators.switchField<User>({
        parent() {
            return this.userInformationBlock;
        },
        bind: 'isDemoPersona',
        title: 'Demo persona',
        width: 'small',
    })
    isDemoPersona: ui.fields.Switch;

    @ui.decorators.switchField<User>({
        parent() {
            return this.userInformationBlock;
        },
        bind: 'isApiUser',
        title: 'API user',
        width: 'small',
        onChange() {
            this.isOperatorUser.isDisabled = this.isApiUser.value ?? false;
        },
    })
    isApiUser: ui.fields.Switch;

    @ui.decorators.switchField<User>({
        parent() {
            return this.userInformationBlock;
        },
        title: 'Send welcome email',
        width: 'small',
        isReadOnly() {
            return !!this.$.recordId;
        },
        bind: { preferences: { isWelcomeMailSent: true } },
        async onChange() {
            if (
                this.isWelcomeMailSent.value &&
                !(await confirmDialogWithAcceptButtonText(
                    this,
                    ui.localize('@sage/xtrem-authorization/pages__user__send_welcome_mail', 'Send email'),
                    ui.localize(
                        '@sage/xtrem-authorization/pages__user__send_welcome_mail_dialog_content',
                        'You are about to send a welcome email to the user.',
                    ),
                    ui.localize('@sage/xtrem-authorization/pages__user__send_welcome_mail_button', 'Send'),
                ))
            ) {
                this.isWelcomeMailSent.value = false;
            }
        },
    })
    isWelcomeMailSent: ui.fields.Switch;

    @ui.decorators.switchField<User>({
        parent() {
            return this.userInformationBlock;
        },
        title: 'Pin authentication',
        bind: 'isOperatorUser',
        width: 'medium',
        onChange() {
            this.operatorCode.isDisabled = !this.isOperatorUser.value;
            this.resetOperatorCodePage.isDisabled = !this.isOperatorUser.value;

            /**
             * If isOperatorUser is switched on we disable isApiUser & isWelcomeMailSent and toggle to false
             * If isOperatorUser is switched off we enable isApiUser & isWelcomeMailSent
             */
            if (this.isOperatorUser.value) {
                this.isApiUser.value = false;
                this.isApiUser.isDisabled = true;

                this.isWelcomeMailSent.value = false;
                this.isWelcomeMailSent.isDisabled = true;
            } else {
                this.isApiUser.isDisabled = false;
                this.isWelcomeMailSent.isDisabled = false;
            }
        },
        isReadOnly() {
            return !!this.$.recordId;
        },
        isHidden() {
            return !this.$.isServiceOptionEnabled('sysDeviceToken');
        },
    })
    isOperatorUser: ui.fields.Switch;

    @ui.decorators.textField<User>({
        parent() {
            return this.userInformationBlock;
        },
        title: 'PIN code',
        width: 'medium',
        bind: 'operatorCode',
        minLength: 4,
        isTransientInput: true,
        isMandatory() {
            return this.isOperatorUser.value ?? false;
        },
        isHidden() {
            if (this.$.isServiceOptionEnabled('sysDeviceToken') && !this.$.recordId) {
                return false;
            }
            return true;
        },
    })
    operatorCode: ui.fields.Text;

    @ui.decorators.buttonField<User>({
        title: 'PIN code',
        isTransient: true,
        map() {
            return ui.localize('@sage/xtrem-authorization/reset-operator-code', 'Reset');
        },
        parent() {
            return this.userInformationBlock;
        },
        async onClick() {
            const operatorCode = await this.$.dialog.page(
                '@sage/xtrem-authorization/OperatorUserPanel',
                { code: this.operatorCode.value ?? '' },
                { size: 'medium', resolveOnCancel: true },
            );

            if (typeof operatorCode === 'string') {
                this.operatorCode.value = operatorCode;
                this.operatorCode.isDirty = true;
            }
        },
        isHidden() {
            if (this.$.isServiceOptionEnabled('sysDeviceToken') && this.$.recordId) {
                return false;
            }
            return true;
        },
    })
    resetOperatorCodePage: ui.fields.Button;

    @ui.decorators.switchField<User>({
        parent() {
            return this.userAuthorizationInformationBlock;
        },
        title: 'Administrator',
        width: 'small',
    })
    isAdministrator: ui.fields.Switch;

    @ui.decorators.switchField<User>({
        parent() {
            return this.userAuthorizationInformationBlock;
        },
        title: 'External',
        width: 'small',
        bind: { preferences: { isExternal: true } },
    })
    isExternal: ui.fields.Switch;

    @ui.decorators.referenceField<User>({
        parent() {
            return this.userAuthorizationInformationBlock;
        },
        node: '@sage/xtrem-authorization/Role',
        title: 'Billing Role',
        valueField: 'name',
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select billing role',
        bind: { billingRole: { role: true } },
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
            ui.nestedFields.text({ bind: 'id', title: 'ID' }),
        ],
        helperTextField: 'id',
        orderBy: { _id: 1 },
        filter: {
            isBillingRole: true,
        },
    })
    role: ui.fields.Reference<Role>;

    @ui.decorators.multiReferenceField<User, GroupRoleSite>({
        node: '@sage/xtrem-authorization/GroupRoleSite',
        bind: 'userGroups',
        valueField: 'name',
        title: 'Authorization group',
        lookupDialogTitle: 'Select authorization group',
        isTransient: true,
        parent() {
            return this.userAuthorizationInformationBlock;
        },
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Name', canFilter: true }),
            ui.nestedFields.text({ bind: 'id', title: 'ID', canFilter: true }),
            ui.nestedFields.text({ bind: '_id', title: 'ID', isHidden: true }),
        ],
        onChange() {
            this.clearGrid();

            this.userGroups.value.forEach(element => {
                this.authorizationGroup.addOrUpdateRecordValue({
                    group: {
                        _id: element._id,
                        id: element.id,
                        name: element.name,
                        groupRolesDisplay: element.groupRolesDisplay,
                        groupSitesDisplay: element.groupSitesDisplay,
                    },
                });
            });
        },
    })
    userGroups: ui.fields.MultiReference<GroupRoleSite>;

    @ui.decorators.separatorField<User>({
        parent() {
            return this.userInformationBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    separatorFromUserGroups: ui.fields.Separator;

    @ui.decorators.tableField<User, UserGroup>({
        node: '@sage/xtrem-authorization/UserGroup',
        bind: 'authorizationGroup',
        title: 'Authorization groups',
        canSelect: false,
        isHidden: false,
        isChangeIndicatorDisabled: true,
        parent() {
            return this.generalSection;
        },
        columns: [
            ui.nestedFields.reference({
                bind: 'group',
                title: '_id',
                node: '@sage/xtrem-authorization/GroupRoleSite',
                valueField: '_id',
                size: 'small',
                isReadOnly: true,
                isHidden: true,
            }),
            ui.nestedFields.reference({
                bind: 'group',
                title: 'Name',
                node: '@sage/xtrem-authorization/GroupRoleSite',
                valueField: 'name',
                size: 'small',
                isReadOnly: true,
            }),
            ui.nestedFields.reference({
                bind: 'group',
                title: 'ID',
                node: '@sage/xtrem-authorization/GroupRoleSite',
                valueField: 'id',
                size: 'small',
                isReadOnly: true,
                columns: [
                    ui.nestedFields.text({ bind: '_id', title: 'ID', isHidden: true }),
                    ui.nestedFields.text({ bind: 'name', title: 'Name', canFilter: true }),
                    ui.nestedFields.text({ bind: 'id', title: 'ID', canFilter: true }),
                ],
            }),
            ui.nestedFields.reference({
                bind: 'group',
                title: 'Roles',
                node: '@sage/xtrem-authorization/GroupRoleSite',
                valueField: 'groupRolesDisplay',
                size: 'small',
                isReadOnly: true,
            }),
            ui.nestedFields.reference({
                bind: 'group',
                title: 'Site group',
                node: '@sage/xtrem-authorization/GroupRoleSite',
                valueField: 'groupSitesDisplay',
                size: 'small',
                isReadOnly: true,
            }),
        ],
    })
    authorizationGroup: ui.fields.Table<UserGroup>;

    clearGrid() {
        this.authorizationGroup.value.forEach((gridLine: ui.PartialCollectionValue<UserGroup>) => {
            this.authorizationGroup.removeRecord(gridLine._id);
        });

        this.associatedSiteGroups.value = [];
        this.associatedRoles.value = [];
    }

    @ui.decorators.imageField<User>({
        parent() {
            return this.userPhotoBlock;
        },
    })
    photo: ui.fields.Image;

    @ui.decorators.section<User>({
        isTitleHidden: true,
        isOpen: true,
        title: 'Roles and activities',
    })
    rolesActivitiesSection: ui.containers.Section;

    /** Table Roles from UserGroup */
    @ui.decorators.tableField<User, DefaultNameIdNode>({
        pageSize: 20,
        title: 'Roles and activities',
        hasSearchBoxMobile: true,
        isTransient: true,
        isChangeIndicatorDisabled: true,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'ID',
                isHidden: true,
                isHiddenDesktop: true,
                isReadOnly: true,
            }),
            ui.nestedFields.link({
                title: 'Name',
                bind: 'name',
                // can't type rowData with DefaultNameIdNode because it's an object
                async onClick(_id: string, rowData: any) {
                    await this.openRoleDetail(_id, (rowData as DefaultNameIdNode).name || '');
                },
            }),
            ui.nestedFields.text({
                bind: 'id',
                title: 'ID',
                isReadOnly: true,
            }),
            ui.nestedFields.label({
                bind: 'createdBy',
                map(_fieldValue, rowData) {
                    return `${rowData.createdBy || ''} ${rowData.createStamp || ''}`;
                },
                isTransient: true,
                title: 'Created by',
            }),
            ui.nestedFields.label({
                bind: 'updatedBy',
                map(_fieldValue, rowData) {
                    return `${rowData.updatedBy || ''} ${rowData.updateStamp || ''}`;
                },
                isTransient: true,
                title: 'Updated by',
            }),
        ],
        orderBy: {
            _id: 1,
        },
        parent() {
            return this.rolesActivitiesSection;
        },
    })
    associatedRoles: ui.fields.Table<DefaultNameIdNode>;

    @ui.decorators.section<User>({
        isTitleHidden: true,
        isOpen: true,
        title: 'Site groups',
    })
    associatedSiteGroupsSection: ui.containers.Section;

    /** Table site Groups from UserGroup */
    @ui.decorators.tableField<User, DefaultNameIdNode>({
        pageSize: 20,
        title: 'Site groups',
        hasSearchBoxMobile: true,
        isTransient: true,
        isChangeIndicatorDisabled: true,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'ID',
                isHidden: true,
                isHiddenDesktop: true,
                isReadOnly: true,
            }),
            ui.nestedFields.link({
                title: 'Name',
                bind: 'name',
                async onClick(_id) {
                    await this.openSiteGroupDetail(_id);
                },
            }),
            ui.nestedFields.text({
                bind: 'id',
                title: 'ID',
                isReadOnly: true,
            }),
            ui.nestedFields.label({
                bind: 'createdBy',
                map(_fieldValue, rowData) {
                    return `${rowData.createdBy || ''} ${rowData.createStamp || ''}`;
                },
                isTransient: true,
                title: 'Created by',
            }),
            ui.nestedFields.label({
                bind: 'updatedBy',
                map(_fieldValue, rowData) {
                    return `${rowData.updatedBy || ''} ${rowData.updateStamp || ''}`;
                },
                isTransient: true,
                title: 'Updated by',
            }),
        ],
        orderBy: {
            _id: 1,
        },
        parent() {
            return this.associatedSiteGroupsSection;
        },
    })
    associatedSiteGroups: ui.fields.Table<DefaultNameIdNode>;

    @ui.decorators.section<User>({
        isTitleHidden: true,
        isOpen: true,
        title: 'Preferences',
    })
    preferencesSection: ui.containers.Section;

    @ui.decorators.block<User>({
        parent() {
            return this.preferencesSection;
        },
        title: 'Preferences',
        isTitleHidden: true,
        width: 'large',
    })
    preferencesBlock: ui.containers.Block;

    @ui.decorators.textField<User>({
        title: 'Import/export delimiter',
        bind: { preferences: { importExportDelimiter: true } },
        parent() {
            return this.preferencesBlock;
        },
        helperText: 'Default value will be ',
    })
    importExportDelimiter: ui.fields.Text;

    @ui.decorators.dropdownListField<User>({
        parent() {
            return this.preferencesBlock;
        },
        bind: { preferences: { importExportDateFormat: true } },
        title: 'Import/export date format',
        optionType: '@sage/xtrem-system/UserImportExportDateFormat',
        helperText: 'Default value will be ',
    })
    importExportDateFormat: ui.fields.DropdownList;

    @ui.decorators.pageAction<User>({
        title: 'View users',
        isHidden: true,
        isDisabled: true,
        onClick() {
            this.$.router.goTo('@sage/xtrem-authorization/UserList', {});
        },
    })
    userList: ui.PageAction;

    @ui.decorators.pageAction<User>({
        title: 'Edit user',
        isHidden: true,
        isDisabled: true,
        onClick() {
            if (this._id.value) {
                this.$.router.goTo('@sage/xtrem-system/User', { _id: this._id.value, page: 'editUser' });
            }
        },
    })
    editUser: ui.PageAction;

    @ui.decorators.pageAction<User>({
        title: 'Save',
        access: { bind: '$create' },
        async onClick() {
            const isInTunnel = this.$.isInDialog && !!this.$.queryParameters[ui.QUERY_PARAM_TUNNEL_SEGMENTS];
            await this.$standardSaveAction.execute(true);

            /**
             * Corner case condition: This makes sure that the operatorCode that has
             * already been used to compute the hash of the operatorId will not trigger
             * the isOperatorCodeUnique check which will restrict operator user
             */
            if (this.operatorCode.value) {
                this.operatorCode.value = '';
            }

            if (!isInTunnel) {
                await this.initAssociatedTabs();
                this.$.setPageClean();
            }
        },
    })
    save: ui.PageAction;

    async setUserGroups() {
        this.userGroups.value = this.authorizationGroup.value.map(element => {
            return { _id: element?.group?._id, id: element?.group?.id, name: element?.group?.name };
        });
    }

    async initAssociatedTabs() {
        if (!this._id.value) {
            const defaultPreferencesValues = await this.$.graph
                .node('@sage/xtrem-system/UserPreferences')
                .getDefaults(
                    {
                        importExportDelimiter: true,
                        importExportDateFormat: true,
                    },
                    {
                        data: {},
                    },
                )
                .execute();
            this.importExportDelimiter.value = defaultPreferencesValues.importExportDelimiter;
            this.importExportDelimiter.helperText = '';
            this.importExportDateFormat.value = defaultPreferencesValues.importExportDateFormat;
            this.importExportDateFormat.helperText = '';
            return;
        }
        this.importExportDelimiter.helperText = this.importExportDelimiter.value
            ? ''
            : `${this.importExportDelimiter.helperText} ";".`;
        this.importExportDateFormat.helperText = this.importExportDateFormat.value
            ? ''
            : `${this.importExportDateFormat.helperText} "${ui.localizeEnumMember('@sage/xtrem-system/UserImportExportDateFormat', 'isoDash')}".`;
        const rolesResult = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-authorization/UserGroup')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            group: {
                                groupRoles: querySelector({
                                    role: {
                                        _id: true,
                                        id: true,
                                        name: true,
                                        createdBy: true,
                                        _createStamp: true,
                                        updatedBy: true,
                                        _updateStamp: true,
                                    },
                                }),
                                groupSites: querySelector({
                                    siteGroup: {
                                        _id: true,
                                        id: true,
                                        name: true,
                                        createdBy: true,
                                        _createStamp: true,
                                        updatedBy: true,
                                        _updateStamp: true,
                                    },
                                }),
                            },
                        },
                        {
                            filter: { user: this._id.value },
                            first: 500,
                        },
                    ),
                )
                .execute(),
        );
        rolesResult.forEach(element =>
            element.group.groupRoles.forEach(element2 => {
                this.associatedRoles.addOrUpdateRecordValue({
                    _id: element2.role._id,
                    id: element2.role.id,
                    name: element2.role.name,
                    createdBy: `${element2.role.createdBy || ''} ${element2.role._createStamp || ''}`,
                    updatedBy: `${element2.role.updatedBy || ''} ${element2.role._updateStamp || ''}`,
                } as DefaultNameIdNode);
            }),
        );

        rolesResult.forEach(element =>
            element.group.groupSites.forEach(element2 => {
                this.associatedSiteGroups.addOrUpdateRecordValue({
                    _id: element2.siteGroup._id,
                    id: element2.siteGroup.id,
                    name: element2.siteGroup.name,
                    createdBy: `${element2.siteGroup.createdBy || ''} ${element2.siteGroup._createStamp || ''}`,
                    updatedBy: `${element2.siteGroup.updatedBy || ''} ${element2.siteGroup._updateStamp || ''}`,
                } as DefaultNameIdNode);
            }),
        );
    }

    setDefaultOperatorSettings() {
        this.operatorCode.value = '';

        this.isApiUser.isDisabled = this.isOperatorUser.value ?? false;
        this.isOperatorUser.isDisabled = this.isApiUser.value ?? false;

        this.operatorCode.isDisabled = !this.isOperatorUser.value;
        this.resetOperatorCodePage.isDisabled = !this.isOperatorUser.value;
    }

    getSerializedValues() {
        const { values } = this.$;
        if (values.preferences != null && values.preferences.importExportDelimiter === null)
            // delete null value to kick in defaultValue on backend
            delete values.preferences.importExportDelimiter;
        return values;
    }

    private setPanel(page: UserPage) {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.save,
            cancel: this.$standardCancelAction,
        });
        this.userList.isDisabled = false;
        this.userList.isHidden = false;
        this.userAuthorizationInformationBlock.isTitleHidden = true;

        switch (page) {
            case 'userListPanelNewUser': // New User only
                break;
            case 'userListPanelDetail': // Detail of a user readOnly
                this.disableAndHideStandardActions();
                this.editUser.isDisabled = false;
                this.editUser.isHidden = false;
                this.userAuthorizationInformationBlock.isTitleHidden = false;
                this.save.isDisabled = true;
                this.save.isHidden = true;
                this.authorizationGroup.isHidden = false;
                this.userGroups.isHidden = true;
                this.readOnly();
                break;
            case 'editUser':
                break;
            default:
                break;
        }
    }

    private readOnly() {
        this.isActive.isReadOnly = true;
        this.firstName.isReadOnly = true;
        this.lastName.isReadOnly = true;
        this.email.isReadOnly = true;
        this.userGroups.isReadOnly = true;
        this.photo.isReadOnly = true;
    }

    private disableAndHideStandardActions() {
        this.$standardNewAction.isDisabled = true;
        this.$standardNewAction.isHidden = true;
        this.$standardCancelAction.isDisabled = true;
        this.$standardCancelAction.isHidden = true;
    }

    async openRoleDetail(roleSysId: string, roleName: string) {
        await this.$.dialog.page(
            '@sage/xtrem-authorization/RoleDetail',
            { roleSysId, roleName, isReadOnly: '0' },
            {
                size: 'extra-large',
                resolveOnCancel: true,
            },
        );
    }

    async openSiteGroupDetail(siteGroupId: string) {
        await this.$.dialog.page(
            '@sage/xtrem-authorization/SiteGroup',
            {
                _id: siteGroupId,
                page: 'siteGroupDetail',
            },
            { fullScreen: true, resolveOnCancel: true },
        );
    }
}
