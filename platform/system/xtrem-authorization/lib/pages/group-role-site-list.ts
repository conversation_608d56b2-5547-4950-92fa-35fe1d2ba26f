import { withoutEdges } from '@sage/xtrem-client';
import { GraphApi } from '@sage/xtrem-authorization-api';
import * as ui from '@sage/xtrem-ui';
import { userData } from '../menu-items/user-data';

@ui.decorators.page<GroupRoleSiteList>({
    module: 'system',
    isTransient: true,
    title: 'Authorization group list',
    menuItem: userData,
    access: { node: '@sage/xtrem-authorization/GroupRoleSite' },
    priority: 200,
    async onLoad() {
        this.setGroupRoleSiteTable();
    },
    businessActions() {
        return [this.addNewGroup];
    },
})
export class GroupRoleSiteList extends ui.Page<GraphApi> {
    @ui.decorators.section<GroupRoleSiteList>({
        isTitleHidden: true,
    })
    section: ui.containers.Section;

    @ui.decorators.tableField<GroupRoleSiteList>({
        pageSize: 20,
        title: 'Authorization group',
        node: '@sage/xtrem-authorization/GroupRoleSite',
        canSelect: false,
        columns: [
            ui.nestedFields.text({
                bind: '_id',
                title: 'ID',
                isHidden: true,
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'id',
                title: 'ID',
                isHidden: true,
            }),
            ui.nestedFields.link({
                bind: 'name',
                title: 'Name',
                page: '@sage/xtrem-authorization/GroupRoleSite',
                queryParameters(_value, rowData) {
                    return {
                        _id: rowData._id,
                        groupName: rowData.name,
                        page: 'detailGroupRoleSite',
                    };
                },
            }),
            ui.nestedFields.text({
                bind: 'createdByUser' as any,
                isTransient: true,
                title: 'Created by',
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                bind: 'updatedByUser' as any,
                isTransient: true,
                title: 'Updated by',
                isReadOnly: true,
            }),
        ],
        orderBy: {
            _id: 1,
        },
        parent() {
            return this.section;
        },
        dropdownActions: [
            {
                icon: 'edit',
                title: 'Edit authorization group',
                async onClick(rowId: any, rowData: any) {
                    this.$.router.goTo('@sage/xtrem-authorization/GroupRoleSite', {
                        _id: rowData._id,
                        page: 'editGroupRoleSite',
                    });
                },
            },
            {
                icon: 'delete',
                title: 'Delete authorization group',
                async onClick(rowId: any, rowData: any) {
                    const options: ui.dialogs.DialogOptions = {
                        acceptButton: {
                            text: 'Delete',
                        },
                        cancelButton: {
                            text: 'Cancel',
                        },
                    };
                    if (
                        await this.$.dialog
                            .confirmation(
                                'warn',
                                ui.localize('@sage/xtrem-authorization/delete-group', 'Confirm deletion'),
                                ui.localize(
                                    '@sage/xtrem-authorization/delete-dialog-content',
                                    'You are about to delete this group.',
                                ),
                                options,
                            )
                            .then(() => true)
                            .catch(() => false)
                    ) {
                        this.$.loader.display();
                        try {
                            await this.$.graph.delete({
                                _id: rowData._id,
                                nodeName: '@sage/xtrem-authorization/GroupRoleSite',
                            });
                            this.groups.removeRecord(rowId);
                            this.$.showToast(
                                ui.localize('@sage/xtrem-authorization/delete-confirmation', 'Record deleted'),
                                { type: 'success' },
                            );
                        } catch (e) {
                            this.$.showToast(e.message, { timeout: 0, type: 'error' });
                        }
                        this.$.loader.hide();
                    }
                },
            },
        ],
    })
    groups: ui.fields.Table;

    @ui.decorators.pageAction<GroupRoleSiteList>({
        title: 'Add group',
        isHidden: false,
        isDisabled: false,
        onError() {
            // Intentionally left empty
        },
        async onClick() {
            try {
                await this.$.dialog.page(
                    '@sage/xtrem-authorization/GroupRoleSite',
                    {
                        page: 'newGroupRoleSite',
                    },
                    {
                        rightAligned: true,
                        size: 'large',
                    },
                );
            } catch (error) {
                if (error) {
                    this.$.showToast(error.message, { timeout: 10000, type: 'error' });
                }
            }

            this.setGroupRoleSiteTable();
        },
    })
    addNewGroup: ui.PageAction;

    /**
     * Populate the groups table
     */
    private async setGroupRoleSiteTable() {
        const result = withoutEdges(
            await this.$.graph
                .node('@sage/xtrem-authorization/GroupRoleSite')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            _id: true,
                            id: true,
                            name: true,
                            createdBy: true,
                            createStamp: true,
                            updatedBy: true,
                            updateStamp: true,
                        },
                        {
                            first: 500,
                        },
                    ),
                )
                .execute(),
        );

        this.groups.value = result.map(element => {
            return {
                _id: element._id,
                id: element.id,
                name: element.name,
                createdByUser: `${element.createdBy} ${element.createStamp}`,
                updatedByUser: `${element.updatedBy} ${element.updateStamp}`,
            };
        });

        this.$.setPageClean();
    }
}
