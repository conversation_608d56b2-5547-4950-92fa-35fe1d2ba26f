{"@sage/xtrem-authorization": [{"topic": "Activity/asyncExport/start", "queue": "import-export", "sourceFileName": "activity.ts"}, {"topic": "GroupRole/asyncExport/start", "queue": "import-export", "sourceFileName": "group-role.ts"}, {"topic": "GroupRoleSite/asyncExport/start", "queue": "import-export", "sourceFileName": "group-role-site.ts"}, {"topic": "GroupSite/asyncExport/start", "queue": "import-export", "sourceFileName": "group-site.ts"}, {"topic": "RestrictedNodeUserGrant/asyncExport/start", "queue": "import-export", "sourceFileName": "restricted-node-user-grant.ts"}, {"topic": "Role/asyncExport/start", "queue": "import-export", "sourceFileName": "role.ts"}, {"topic": "RoleActivity/asyncExport/start", "queue": "import-export", "sourceFileName": "role-activity.ts"}, {"topic": "RoleToRole/asyncExport/start", "queue": "import-export", "sourceFileName": "role-to-role.ts"}, {"topic": "SiteGroup/asyncExport/start", "queue": "import-export", "sourceFileName": "site-group.ts"}, {"topic": "SiteGroupToSite/asyncExport/start", "queue": "import-export", "sourceFileName": "site-group-to-site.ts"}, {"topic": "SiteGroupToSiteGroup/asyncExport/start", "queue": "import-export", "sourceFileName": "site-group-to-site-group.ts"}, {"topic": "SupportAccessHistory/asyncExport/start", "queue": "import-export", "sourceFileName": "support-access-history.ts"}, {"topic": "User/sendBulkWelcomeMail/start", "queue": "authorization", "sourceFileName": "user-extension.ts"}, {"topic": "UserBillingRole/asyncExport/start", "queue": "import-export", "sourceFileName": "user-billing-role.ts"}, {"topic": "UserGroup/asyncExport/start", "queue": "import-export", "sourceFileName": "user-group.ts"}]}