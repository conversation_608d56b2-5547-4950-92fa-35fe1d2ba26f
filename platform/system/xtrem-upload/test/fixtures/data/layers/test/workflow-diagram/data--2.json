{"edges": [{"id": "entity-created-1--out-1", "source": "entity-created-1", "target": "transfer-attachment-1", "sourceHandle": "out"}], "nodes": [{"id": "entity-created-1", "data": {"localizedTitle": {"en-US": "Test attachment document created", "fr-FR": "Pièce jointe de test créée"}, "topic": "TestAttachmentDocument/created", "subtitle": "@sage/xtrem-upload/TestAttachmentDocument", "conditions": [], "entityName": "@sage/xtrem-upload/TestAttachmentDocument", "stepVariables": [{"node": "TestAttachmentDocument", "path": "testAttachmentDocument._id", "type": "IntReference", "title": "Test Attachment Document / 🆔"}]}, "type": "entity-created", "width": 250, "height": 102, "origin": [0.5, 0], "dragging": false, "position": {"x": 540, "y": -40}, "selected": false, "positionAbsolute": {"x": 540, "y": -40}}, {"id": "transfer-attachment-1", "data": {"toPath": "testAttachmentDocument._id", "fromPath": "testAttachmentDocument.customer._id", "fromTags": ["#contract", "#transfer"], "subtitle": null, "stepVariables": [{"node": "TestAttachmentCustomer", "path": "testAttachmentDocument.customer._id", "type": "IntOrString", "title": "Test Attachment Document / Customer / 🆔"}, {"path": "transferredCount.success", "type": "Boolean", "title": "Attachment transferred"}], "failIfNotFound": true, "localizedTitle": {"base": "", "en-US": ""}, "outputVariableName": "transferredCount", "mainVariablesSummary": "- Test Attachment Document  / **🆔**\n- Test Attachment Document  /  Customer  / **🆔**", "mainSelectVariablesButton": null}, "type": "transfer-attachment", "width": 250, "height": 86, "dragging": false, "position": {"x": 540, "y": 160}, "selected": false}]}