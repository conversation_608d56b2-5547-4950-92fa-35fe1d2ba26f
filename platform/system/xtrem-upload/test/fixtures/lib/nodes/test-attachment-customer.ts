import { Node, StringDataType, decorators } from '@sage/xtrem-core';

@decorators.node<TestAttachmentCustomer>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    hasAttachments: true,
    indexes: [{ orderBy: { code: +1 }, isUnique: true, isNaturalKey: true }],
})
export class TestAttachmentCustomer extends Node {
    @decorators.stringProperty<TestAttachmentCustomer, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 32 }),
    })
    readonly code: Promise<string>;
}
