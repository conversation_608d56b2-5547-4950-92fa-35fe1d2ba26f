import { Node, StringDataType, decorators } from '@sage/xtrem-core';

/**
 * Test abstract class with no attachments
 */
@decorators.node<TestBaseNoAttachments>({
    isPublished: true,
    storage: 'sql',
    isAbstract: true,
    indexes: [{ orderBy: { code: +1 }, isUnique: true }],
})
export class TestBaseNoAttachments extends Node {
    @decorators.stringProperty<TestBaseNoAttachments, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 32 }),
    })
    readonly code: Promise<string>;
}

/**
 * Test sub node with attachments, where base node has no attachments
 */
@decorators.subNode<TestBaseNoAttachmentsSubNodeHas>({
    extends: () => TestBaseNoAttachments,
    isPublished: true,
    hasAttachments: true,
})
export class TestBaseNoAttachmentsSubNodeHas extends TestBaseNoAttachments {}

/**
 * Test abstract class with attachments
 */
@decorators.node<TestBaseHasAttachments>({
    isPublished: true,
    storage: 'sql',
    isAbstract: true,
    hasAttachments: true,
    indexes: [{ orderBy: { code: +1 }, isUnique: true }],
})
export class TestBaseHasAttachments extends Node {
    @decorators.stringProperty<TestBaseHasAttachments, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 32 }),
    })
    readonly code: Promise<string>;
}

/**
 * Test sub node with no hasAttachments decorator, where base node has attachments, defaults from base node
 */
@decorators.subNode<TestBaseHasAttachmentsSubNodeHas>({
    extends: () => TestBaseHasAttachments,
    isPublished: true,
})
export class TestBaseHasAttachmentsSubNodeHas extends TestBaseHasAttachments {}
