import { Node, Reference, StringDataType, decorators } from '@sage/xtrem-core';
import { TestAttachmentCustomer } from './test-attachment-customer';

@decorators.node<TestAttachmentDocument>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    hasAttachments: true,
    indexes: [{ orderBy: { code: +1 }, isUnique: true }],
    notifies: ['created', 'updated', 'deleted'],
})
export class TestAttachmentDocument extends Node {
    @decorators.stringProperty<TestAttachmentDocument, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 32 }),
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestAttachmentDocument, 'customer'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        node: () => TestAttachmentCustomer,
    })
    readonly customer: Reference<TestAttachmentCustomer>;
}
