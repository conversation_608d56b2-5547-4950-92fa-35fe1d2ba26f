import { Node, StringDataType, decorators } from '@sage/xtrem-core';

@decorators.node<NodeNoAttachments>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    tableName: 'NodeNoAttachments',
    indexes: [{ orderBy: { code: +1 }, isUnique: true }],
})
export class NodeNoAttachments extends Node {
    @decorators.stringProperty<NodeNoAttachments, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 32 }),
    })
    readonly code: Promise<string>;
}

@decorators.node<NodeHasAttachments>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    tableName: 'NodeHasAttachments',
    hasAttachments: true,
    indexes: [{ orderBy: { code: +1 }, isUnique: true }],
})
export class NodeHasAttachments extends Node {
    @decorators.stringProperty<NodeHasAttachments, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 32 }),
    })
    readonly code: Promise<string>;
}
