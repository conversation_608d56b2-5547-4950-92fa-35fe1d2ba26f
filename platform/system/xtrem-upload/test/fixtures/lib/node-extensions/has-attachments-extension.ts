import { decorators, NodeExtension } from '@sage/xtrem-core';
import { NodeHasAttachments, NodeNoAttachments } from '../nodes/has-attachments';

/**
 * Test node extension, where node does not have attachments
 */
@decorators.nodeExtension<NodeNoAttachmentsExtension>({
    extends: () => NodeNoAttachments,
    hasAttachments: true,
})
export class NodeNoAttachmentsExtension extends NodeExtension<NodeNoAttachments> {}
declare module '../nodes/has-attachments' {
    export interface NodeNoAttachments extends NodeNoAttachmentsExtension {}
}

/**
 * Test node extension, where node does not have attachments
 */
@decorators.nodeExtension<NodeHasAttachmentsExtension>({
    extends: () => NodeHasAttachments,
    hasAttachments: false,
})
export class NodeHasAttachmentsExtension extends NodeExtension<NodeHasAttachments> {}
declare module '../nodes/has-attachments' {
    export interface NodeHasAttachments extends NodeHasAttachmentsExtension {}
}
