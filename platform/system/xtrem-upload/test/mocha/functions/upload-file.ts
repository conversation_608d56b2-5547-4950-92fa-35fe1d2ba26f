import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremUpload from '../../../lib/index';

describe('Upload File', () => {
    it('Create upload file', () =>
        Test.withContext(async context => {
            const filename = 'filename';
            const filenameExtension = 'txt';
            const objectKeyPrefix = 'test';
            const timestamp = '2024-08-30-15-52-10';
            const mimeType = 'text/txt';

            const uploadFileResult = await xtremUpload.functions.createUploadedFile(context, {
                kind: 'upload',
                filename,
                filenameExtension,
                objectKeyPrefix,
                timestamp,
                mimeType,
            });

            const expectedResult = {
                result: {
                    key: 'test/filename-2024-08-30-15-52-10.txt',
                    filename: 'filename-2024-08-30-15-52-10.txt',
                    mimeType: 'text/txt',
                    lastModified: null,
                    contentLength: 0,
                    status: 'verified',
                    rejectReason: '',
                    canSkipAntivirusScan: true,
                    owner: 6,
                    replyTopic: 'UploadedFile/processUpload',
                    uploadUrl:
                        '/xtrem-dev/xtrem/platform/system/xtrem-upload/tmp/tenant-data/777777777777777777777/uploads/test/filename-2024-08-30-15-52-10.txt',
                    downloadUrl: '', // changes on every call
                },
                objectKey: 'test/filename-2024-08-30-15-52-10.txt',
            };

            assert.equal(uploadFileResult.objectKey, expectedResult.objectKey);
            assert.equal(uploadFileResult.result.key, expectedResult.result.key);
            assert.equal(uploadFileResult.result.filename, expectedResult.result.filename);
            assert.equal(uploadFileResult.result.mimeType, expectedResult.result.mimeType);
            assert.equal(uploadFileResult.result.status, expectedResult.result.status);
            assert.equal(uploadFileResult.result.rejectReason, expectedResult.result.rejectReason);
            assert.equal(uploadFileResult.result.replyTopic, expectedResult.result.replyTopic);
        }));
});
