import { Test } from '@sage/xtrem-core';
import { UploadedFile } from '../../../lib/nodes/uploaded-file';
import { TestAttachmentDocument } from '../../fixtures/lib/nodes/test-attachment-document';

describe('Attachment manager', () => {
    before(async () => {
        await Test.withCommittedContext(
            async context => {
                // create an attachment
                const attachment = await context.create(UploadedFile, {
                    filename: 'my-file.pdf',
                    key: 'key-1',
                    kind: 'attachment',
                });
                await attachment.$.save();

                // create a second attachment
                const attachment2 = await context.create(UploadedFile, {
                    filename: 'my-file-2.pdf',
                    key: 'key-2',
                    kind: 'attachment',
                });
                await attachment2.$.save();

                // create a test document
                const testDocument = await context.create(TestAttachmentDocument, {
                    code: 'code 1',
                });
                await testDocument.$.save();
            },
            { auth: { login: '<EMAIL>' } },
        );
    });
    // TODO: Unit tests to be implemented with _attachments system property refactoring
    // it('can list, create, update and delete attachment', () =>
    //     Test.withCommittedContext(
    //         async context => {
    //             // read nodes
    //             const attachment = await context.read(UploadedFile, {
    //                 key: 'key-1',
    //                 kind: 'attachment',
    //             });
    //             const attachment2 = await context.read(UploadedFile, {
    //                 key: 'key-2',
    //                 kind: 'attachment',
    //             });
    //             const testDocument = await context.read(TestAttachmentDocument, {
    //                 code: 'code 1',
    //             });

    //             // create the manager
    //             const attachmentManager = CoreHooks.getAttachmentManager();

    //         },
    //         { auth: { login: '<EMAIL>' } },
    //     ));
});
