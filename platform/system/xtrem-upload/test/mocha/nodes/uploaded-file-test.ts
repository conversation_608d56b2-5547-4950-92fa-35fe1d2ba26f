import '@sage/xtrem-communication';
import { datetime, Test, ValidationSeverity } from '@sage/xtrem-core';
import { FileTimeToLive } from '@sage/xtrem-file-storage';
import { InfrastructureHelper } from '@sage/xtrem-infrastructure-adapter';
import axios from 'axios';
import { assert } from 'chai';
import * as fs from 'fs';
import * as fsp from 'path';
import * as sinon from 'sinon';
import { UploadedFile } from '../../../lib/nodes/uploaded-file';
import { graphqlSetup } from '../../fixtures/setup';

describe('UploadedFile node', () => {
    const idsToDelete: number[] = [];
    it('Check default status value', () =>
        Test.withContext(async context => {
            const newUpload = await context.create(UploadedFile, {
                filename: 'my-file.pdf',
                kind: 'upload',
            });
            assert.equal(await newUpload.status, 'created');
        }));
    it('Check key generate nanoid', () =>
        Test.withContext(async context => {
            const newUpload = await context.create(UploadedFile, {
                filename: 'my-file.pdf',
                status: 'created',
                kind: 'upload',
            });
            assert.equal((await newUpload.key).length, 21);
        }));

    it('get uploadUrl after created and status is created', () =>
        Test.withContext(async context => {
            const result = await context.create(UploadedFile, {
                filename: 'my-file.pdf',
                lastModified: datetime.now(),
                contentLength: 1000,
                mimeType: 'application/text',
                kind: 'upload',
            });
            await result.$.save();
            idsToDelete.push(result._id);
            assert.isTrue(
                (await result.uploadUrl).endsWith(`tenant-data/${context.tenantId}/uploads/${await result.key}`),
            );
            assert.equal(await result.downloadUrl, '');
        }));

    it('does not return uploadUrl when status is not created', () =>
        Test.withContext(async context => {
            const result = await context.create(UploadedFile, {
                filename: 'my-file.pdf',
                lastModified: datetime.now(),
                contentLength: 1000,
                mimeType: 'application/text',
                status: 'created',
                kind: 'upload',
            });
            await result.$.save();
            await result.$.set({ status: 'verified' });
            await result.$.save();
            idsToDelete.push(result._id);
            assert.equal(await result.uploadUrl, '');
        }));

    it('return an uploadUrl when status is verified and a async context exists', () =>
        Test.withContext(async context => {
            const result = await context.create(UploadedFile, {
                filename: 'my-file.pdf',
                lastModified: datetime.now(),
                contentLength: 1000,
                mimeType: 'application/text',
                status: 'created',
                kind: 'upload',
            });
            await result.$.save();
            idsToDelete.push(result._id);
            assert.isTrue(
                (await result.uploadUrl).endsWith(`tenant-data/${context.tenantId}/uploads/${await result.key}`),
            );

            await result.$.set({ status: 'verified' });

            await result.$.save();
            const url = new URL(await result.downloadUrl);
            assert.strictEqual(url.pathname, '/download');
            const targetData = url.searchParams.get('t');
            const target = JSON.parse(await context.vault.decrypt64(targetData));
            assert.deepEqual(target, { nodeName: 'UploadedFile', _id: result._id });
        }));

    it('can only return result if user is the owner', async () => {
        const id = await Test.withCommittedContext(async context => {
            const result = await context.create(UploadedFile, {
                filename: 'my-file.pdf',
                lastModified: datetime.now(),
                contentLength: 1000,
                mimeType: 'application/text',
                status: 'created',
                kind: 'upload',
            });
            await result.$.save();
            idsToDelete.push(result._id);
            return result._id;
        });

        await Test.withReadonlyContext(async context => {
            const result = await context
                .query(UploadedFile, { filter: { kind: 'upload', filename: 'my-file.pdf' } })
                .toArray();
            assert.equal(result.length, 1);
            assert.equal(result[0]._id, id);
        });

        await Test.withReadonlyContext(
            async context => {
                const result = await context.read(UploadedFile, { _id: id });
                await assert.isRejected(result.uploadUrl, 'You cannot upload a file.');
            },
            { userEmail: '<EMAIL>' },
        );
    });

    it('cannot upload with content length that exceeds the configured maxUploadSize', async () => {
        await Test.withContext(async context => {
            const result = await context.create(UploadedFile, {
                filename: 'my-file.pdf',
                lastModified: datetime.now(),
                contentLength: 1073741825,
                mimeType: 'application/text',
                status: 'created',
                kind: 'upload',
            });
            await assert.isRejected(result.$.save());
            assert.equal(context.severity, ValidationSeverity.error);
            assert.deepEqual(context.diagnoses, [
                {
                    message: 'The file size (1073741825) exceeds the maximum length allowed (1073741824 bytes).',
                    path: ['contentLength'],
                    severity: 3,
                },
            ]);
        });
    });

    it('cannot create upload with status different of created', async () => {
        await Test.withContext(async context => {
            const result = await context.create(UploadedFile, {
                filename: 'test.csv',
                lastModified: datetime.now(),
                contentLength: 100,
                mimeType: 'text/csv',
                status: 'verified',
                kind: 'upload',
            });
            await assert.isRejected(result.$.save(), 'Invalid status: verified');
        });
    });

    it('graphql mutation can create file', async () => {
        await Test.withContext(async context => {
            const graphqlHelper = await graphqlSetup({ context, application: Test.application });

            const result = await graphqlHelper.mutation(
                `{
                    uploadedFile {
                        create(data: {filename: "test.csv", mimeType: "text/csv", lastModified: "2023-01-01T11:12:13.456Z", contentLength: "67", kind: "upload"}) {
                            key uploadUrl downloadUrl status
                        }
                    }
                 }`,
            );
            const data = (result as any).uploadedFile?.create;
            assert.isNotNull(data);
            assert.isNotEmpty(data.key);
            assert.isNotEmpty(data.uploadUrl);
            assert.isEmpty(data.downloadUrl);
            assert.strictEqual(data.status, 'created');
        });
    });

    it('graphql mutation can mark as uploaded', async () => {
        let _id = 0;
        await Test.withCommittedContext(
            async context => {
                const result = await context.create(UploadedFile, {
                    filename: 'test.pdf',
                    lastModified: datetime.now(),
                    contentLength: 678,
                    mimeType: 'text/csv',
                    status: 'created',
                    kind: 'upload',
                });
                await result.$.save();
                idsToDelete.push(result._id);

                _id = result._id;
            },
            {
                userEmail: '<EMAIL>',
            },
        );
        await Test.withCommittedContext(async context => {
            const graphqlHelper = await graphqlSetup({ context, application: Test.application });

            const result = await graphqlHelper.mutation(
                `{
                    uploadedFile {
                        update(data: {_id: "${_id}", status: "uploaded"}) {
                            _id uploadUrl downloadUrl status
                        }
                    }
                 }`,
            );
            const data = (result as any).uploadedFile?.update;
            assert.isNotNull(data);
            assert.strictEqual(data._id, `${_id}`);
            assert.isEmpty(data.uploadUrl);
            assert.isEmpty(data.downloadUrl);
            assert.strictEqual(data.status, 'uploaded');
        });
    });

    it('create upload record with content', async () => {
        const axiosPutStub = sinon.stub(axios, 'put');
        axiosPutStub.resolves({ status: 200, statusText: 'OK', errors: [] });
        await Test.withContext(async context => {
            const uploadedFile = await UploadedFile.createAndPopulateUploadedFile(context, {
                kind: 'upload',
                filename: 'test.csv',
                type: 'text/csv',
                replyTopic: 'UploadedFile/InfrastructureComplete',
                data: '{ "data": "test" }',
                canSkipAntivirusScan: true,
            });
            assert.equal(await uploadedFile.status, 'uploaded');
        });
        axiosPutStub.restore();
    });

    it('test download of uploaded file', async () => {
        await Test.withContext(async context => {
            const result = await context.create(UploadedFile, {
                filename: 'test.csv',
                lastModified: datetime.now(),
                contentLength: 100,
                mimeType: 'text/csv',
                status: 'created',
                kind: 'upload',
            });
            await result.$.save();

            await InfrastructureHelper.createFile(
                context,
                await result.key,
                `Generated file - ${result.filename}`,
                '{ "data": "test" }',
                FileTimeToLive.Expire10Days,
            );

            await assert.isRejected(
                result.downloadFileContent(context),
                `UploadedFile:downloadFileContent Request file ${result._id} has not been verified`,
            );

            await result.$.set({
                status: 'verified',
            });
            await result.$.save();
            idsToDelete.push(result._id);

            const content = await result.downloadFileContent(context);
            assert.deepEqual(JSON.parse(content.toString()), { data: 'test' });

            const oldReadFile = InfrastructureHelper.readFile;
            InfrastructureHelper.readFile = sinon.stub().resolves(undefined);
            try {
                await assert.isRejected(result.downloadFileContent(context), 'Could not download UploadedFile');
            } finally {
                InfrastructureHelper.readFile = oldReadFile;
            }
        });
    });

    it('fail upload record with binary content with canSkipAntivirusScan=true', async () => {
        const axiosPutStub = sinon.stub(axios, 'put');
        axiosPutStub.resolves({ status: 200, statusText: 'OK', errors: [] });
        await Test.withContext(async context => {
            let uploadedFile = await UploadedFile.createAndPopulateUploadedFile(context, {
                kind: 'upload',
                filename: 'empty.pdf',
                type: 'application/pdf',
                replyTopic: 'UploadedFile/InfrastructureComplete',
                data: fs
                    .readFileSync(
                        fsp.join(process.env.PWD!, 'test', 'fixtures', 'example-files', 'binary', 'empty.pdf'),
                    )
                    .toString(),
                canSkipAntivirusScan: true,
            });
            assert.equal(await uploadedFile.status, 'uploadFailed');

            uploadedFile = await UploadedFile.createAndPopulateUploadedFile(context, {
                kind: 'upload',
                filename: 'empty.png',
                type: 'image/*',
                replyTopic: 'UploadedFile/InfrastructureComplete',
                data: fs
                    .readFileSync(
                        fsp.join(process.env.PWD!, 'test', 'fixtures', 'example-files', 'binary', 'empty.png'),
                    )
                    .toString(),
                canSkipAntivirusScan: true,
            });
            assert.equal(await uploadedFile.status, 'uploadFailed');

            uploadedFile = await UploadedFile.createAndPopulateUploadedFile(context, {
                kind: 'upload',
                filename: 'test.csv',
                type: 'text/csv',
                replyTopic: 'UploadedFile/InfrastructureComplete',
                data: fs
                    .readFileSync(fsp.join(process.env.PWD!, 'test', 'fixtures', 'example-files', 'text', 'text.csv'))
                    .toString(),
                canSkipAntivirusScan: true,
            });
            assert.equal(await uploadedFile.status, 'uploaded');
        });
        axiosPutStub.restore();
    });

    it('create upload record with content fails upload', async () => {
        const oldCreateFile = InfrastructureHelper.createFile;
        InfrastructureHelper.createFile = sinon.stub().resolves(null);
        await Test.withContext(async context => {
            const uploadedFile = await UploadedFile.createAndPopulateUploadedFile(context, {
                kind: 'upload',
                filename: 'test.csv',
                type: 'text/csv',
                replyTopic: 'UploadedFile/InfrastructureComplete',
                data: '{ "data": "test" }',
                canSkipAntivirusScan: true,
            });
            assert.equal(await uploadedFile.status, 'uploadFailed');
            InfrastructureHelper.createFile = oldCreateFile;
        });
    });

    after(() =>
        Test.withCommittedContext(context =>
            context.deleteMany(UploadedFile, {
                kind: 'upload',
                _id: { _in: idsToDelete },
            }),
        ),
    );
});
