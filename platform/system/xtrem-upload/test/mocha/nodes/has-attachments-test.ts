import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import { updateContext } from '../../../index';

updateContext();

describe('Attachments - hasAttachments', () => {
    it('Base node no attachments', () =>
        Test.withCommittedContext(context => {
            // Base node TestBaseNoAttachments with hasAttachments = false
            // No attachments collection expected
            const checkFactory = context.application.getFactoryByName('TestBaseNoAttachments');
            assert.isFalse(checkFactory.hasAttachments, 'Expected hasAttachments to be false');
            assert.isUndefined(checkFactory.propertiesByName._attachments, 'No attachments collection expected');
        }));

    it('Base node no attachments, sub node has', () =>
        Test.withCommittedContext(context => {
            // Base node name is TestBaseNoAttachments with hasAttachments = false
            // Sub node overrides this to true
            const checkFactory = context.application.getFactoryByName('TestBaseNoAttachmentsSubNodeHas');
            assert.isTrue(checkFactory.hasAttachments, 'Expected hasAttachments to be true');
            assert.isDefined(checkFactory.propertiesByName._attachments, 'Attachments collection expected');
        }));

    it('Base node has attachments (abstract class should not have attachments collection)', () =>
        Test.withCommittedContext(context => {
            // Base node TestBaseHasAttachments with hasAttachments = true
            // Abstract class should not have attachments
            const checkFactory = context.application.getFactoryByName('TestBaseHasAttachments');
            assert.isTrue(checkFactory.hasAttachments, 'Expected hasAttachments to be true');
            assert.isUndefined(checkFactory.propertiesByName._attachments, 'No attachments collection expected');
        }));

    it('Base node has attachments, sub node has', () =>
        Test.withCommittedContext(context => {
            // Base node TestBaseHasAttachments with hasAttachments = true
            // Sub node does not specify hasAttachments, should default to true
            const checkFactory = context.application.getFactoryByName('TestBaseHasAttachmentsSubNodeHas');
            assert.isTrue(checkFactory.hasAttachments, 'Expected hasAttachments to be true');
            assert.isDefined(checkFactory.propertiesByName._attachments, 'Attachments collection expected');
        }));

    it('Node extension has attachments', () =>
        Test.withCommittedContext(context => {
            // Node original name is NodeNoAttachments with hasAttachments = false, extension sets this to true
            const checkFactory = context.application.getFactoryByName('NodeNoAttachments');
            assert.isTrue(checkFactory.hasAttachments, 'Expected hasAttachments to be true');
            assert.isDefined(checkFactory.propertiesByName._attachments, 'Attachments collection expected');
        }));

    it('Node extension has no attachments', () =>
        Test.withCommittedContext(context => {
            // Node original name is NodeHasAttachments with hasAttachments = true, extension sets this to false
            const checkFactory = context.application.getFactoryByName('NodeHasAttachments');
            assert.isFalse(checkFactory.hasAttachments, 'Expected hasAttachments to be true');
            assert.isUndefined(checkFactory.propertiesByName._attachments, 'Attachments collection expected');
        }));
});
