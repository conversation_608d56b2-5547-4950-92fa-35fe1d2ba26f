import { assert } from 'chai';
// import * as timers from 'timers/promises';
import { Context, Test } from '@sage/xtrem-core';
import * as xtremUpload from '../../../lib';
import { TestAttachmentDocument } from '../../fixtures/lib/nodes/test-attachment-document';

async function iniCreate(
    context: Context,
    isProtected = false,
): Promise<{ docId: number; attachments: { _id: number; attachmentId: number }[] }[]> {
    // create an attachment
    const attachment = await context.create(xtremUpload.nodes.UploadedFile, {
        filename: 'my-file.pdf',
        key: 'key-1',
        mimeType: 'application/pdf',
        kind: 'attachment',
    });
    await attachment.$.save();

    assert.equal(
        await attachment.uploadUrl,
        `${context.application.tmpDir}/tenant-data/777777777777777777777/uploads/key-1`,
    );

    // create a second attachment
    const attachment2 = await context.create(xtremUpload.nodes.UploadedFile, {
        filename: 'my-file-2.pdf',
        key: 'key-2',
        mimeType: 'application/pdf',
        kind: 'attachment',
    });
    await attachment2.$.save();

    assert.equal(
        await attachment2.uploadUrl,
        `${context.application.tmpDir}/tenant-data/777777777777777777777/uploads/key-2`,
    );

    // create a third attachment
    const attachmentN = await context.create(xtremUpload.nodes.UploadedFile, {
        filename: 'my-file-N.pdf',
        key: 'key-N',
        mimeType: 'application/pdf',
        kind: 'attachment',
    });
    await attachmentN.$.save();

    // create a test document
    const testDocument = await context.create(TestAttachmentDocument, {
        code: 'code 1',
        _attachments: [
            {
                title: 'attachment title',
                attachment: { _id: attachment._id },
                isProtected,
            },
            {
                title: 'attachment title 2',
                attachment: { _id: attachment2._id },
                isProtected,
            },
        ],
    });
    await testDocument.$.save();

    // create a test document
    const testDocument2 = await context.create(TestAttachmentDocument, {
        code: 'code 2',
        _attachments: [
            {
                title: 'attachment title',
                attachment: { _id: attachmentN._id },
                isProtected,
            },
        ],
    });
    await testDocument2.$.save();

    const attachments = await testDocument._attachments
        .map(async a => ({
            _id: a._id,
            attachmentId: (await a.attachment)._id,
        }))
        .toArray();
    assert.equal(attachments.length, 2);
    await testDocument._attachments.forEach(async attachmentAssociation => {
        assert.equal(await attachmentAssociation.sourceNodeName, 'TestAttachmentDocument');
        assert.equal(await attachmentAssociation.sourceNodeId, testDocument._id);
    });

    const attachments2 = await testDocument2._attachments
        .map(async a => ({
            _id: a._id,
            attachmentId: (await a.attachment)._id,
        }))
        .toArray();
    assert.equal(attachments2.length, 1);
    await testDocument2._attachments.forEach(async attachmentAssociation => {
        assert.equal(await attachmentAssociation.sourceNodeName, 'TestAttachmentDocument');
        assert.equal(await attachmentAssociation.sourceNodeId, testDocument2._id);
    });

    return [
        { docId: testDocument._id, attachments },
        { docId: testDocument2._id, attachments: attachments2 },
    ];
}

describe('Attachment Associations', () => {
    it('can create attachment association via _attachment collection of document', () =>
        Test.withContext(context => iniCreate(context)));

    it('can update an attachment', async () => {
        await Test.withContext(async context => {
            const attachmentsDocuments = await iniCreate(context);
            const { docId, attachments } = attachmentsDocuments[0];
            const { docId: doc2Id } = attachmentsDocuments[1];

            // create a third attachment
            const attachment3 = await context.create(xtremUpload.nodes.UploadedFile, {
                filename: 'my-file-3.pdf',
                key: 'key-3',
                mimeType: 'application/pdf',
                kind: 'attachment',
            });
            await attachment3.$.save();

            const document = await context.read(TestAttachmentDocument, { _id: docId }, { forUpdate: true });
            // update the attachment association on the test document
            await document.$.set({
                _attachments: [
                    {
                        _id: attachments[1]._id,
                        title: 'attachment title 3',
                        attachment: { _id: attachment3._id },
                        _action: 'update',
                    },
                ],
            });
            await document.$.save();

            const document2 = await context.read(TestAttachmentDocument, { _id: doc2Id }, { forUpdate: true });

            await document._attachments.forEach(async attachmentAssociation => {
                assert.equal(await attachmentAssociation.sourceNodeName, 'TestAttachmentDocument');
                assert.equal(await attachmentAssociation.sourceNodeId, docId);
            });

            // check if we add a new attachment, the other document is not affected
            await document2._attachments.forEach(async attachmentAssociation => {
                assert.equal(await attachmentAssociation.sourceNodeName, 'TestAttachmentDocument');
                assert.equal(await attachmentAssociation.sourceNodeId, doc2Id);
            });

            assert.isTrue(
                await document._attachments.some(async attachmentAssociation => {
                    return (await attachmentAssociation.attachment)._id === attachment3._id;
                }),
            );

            const queryResult = await context
                .query(TestAttachmentDocument, {
                    filter: { _attachments: { _atLeast: 1, attachment: attachment3._id } },
                })
                .toArray();
            assert.equal(queryResult.length, 1);
        });
    });

    it('can delete an attachment via update of collection', async () => {
        await Test.withContext(async context => {
            const attachmentsDocuments = await iniCreate(context);
            const { docId, attachments } = attachmentsDocuments[0];
            const { docId: doc2Id } = attachmentsDocuments[1];

            const document = await context.read(TestAttachmentDocument, { _id: docId }, { forUpdate: true });
            // delete the attachment association on the test document
            await document.$.set({
                _attachments: [
                    {
                        _id: attachments[1]._id,
                        _action: 'delete',
                    },
                ],
            });
            await document.$.save();

            assert.equal(await document._attachments.length, 1);

            await document._attachments.forEach(async attachmentAssociation => {
                assert.equal(await attachmentAssociation.sourceNodeName, 'TestAttachmentDocument');
                assert.equal(await attachmentAssociation.sourceNodeId, docId);
            });

            const document2 = await context.read(TestAttachmentDocument, { _id: doc2Id }, { forUpdate: true });

            // check if we delete an attachment, the other document is not affected
            await document2._attachments.forEach(async attachmentAssociation => {
                assert.equal(await attachmentAssociation.sourceNodeName, 'TestAttachmentDocument');
                assert.equal(await attachmentAssociation.sourceNodeId, doc2Id);
            });
        });
    });

    it('cannot update a protected attachment', async () => {
        await Test.withContext(async context => {
            const attachmentsDocuments = await iniCreate(context, true);
            const { docId, attachments } = attachmentsDocuments[0];

            // create a third attachment
            const attachment3 = await context.create(xtremUpload.nodes.UploadedFile, {
                filename: 'my-file-3.pdf',
                key: 'key-3',
                mimeType: 'application/pdf',
                kind: 'attachment',
            });
            await attachment3.$.save();

            const document = await context.read(TestAttachmentDocument, { _id: docId }, { forUpdate: true });
            // update the protected attachment association on the test document
            await document.$.set({
                _attachments: [
                    {
                        _id: attachments[1]._id,
                        title: 'attachment title 3',
                        attachment: { _id: attachment3._id },
                        _action: 'update',
                    },
                ],
            });
            await assert.isRejected(document.$.save(), 'The record was not updated.');

            assert.deepEqual(
                context.diagnoses.map(d => d.message),
                ['You cannot update a protected attachment.'],
            );
        });
    });

    it('cannot delete a protected attachment', async () => {
        await Test.withContext(async context => {
            const attachmentsDocuments = await iniCreate(context, true);
            const { docId, attachments } = attachmentsDocuments[0];

            const document = await context.read(TestAttachmentDocument, { _id: docId }, { forUpdate: true });
            await assert.isRejected(
                document.$.set({
                    _attachments: [
                        {
                            _id: attachments[1]._id,
                            _action: 'delete',
                        },
                    ],
                }),
                'The record was not deleted.',
            );
            assert.deepEqual(context.diagnoses, [
                {
                    severity: 3,
                    message: 'You cannot delete a protected attachment.',
                    path: ['_attachments', '10'],
                },
            ]);
        });
    });
});
