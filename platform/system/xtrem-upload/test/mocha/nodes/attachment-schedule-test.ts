import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as timers from 'timers/promises';
import { updateContext } from '../../../index';
import { AttachmentAssociation } from '../../../lib/nodes/attachment-association';
import { UploadedFile } from '../../../lib/nodes/uploaded-file';
import { TestAttachmentDocument } from '../../fixtures/lib/nodes/test-attachment-document';

updateContext();

describe('Attachments', () => {
    const codeForNewDocument = 'code 1';
    let idOfNewDocument: number;
    before(() =>
        Test.withCommittedContext(async context => {
            // create an attachment
            const attachment = await context.create(UploadedFile, {
                filename: 'my-file.pdf',
                key: 'key-1',
                mimeType: 'application/pdf',
                kind: 'attachment',
            });
            await attachment.$.save();

            // create a second attachment
            const attachment2 = await context.create(UploadedFile, {
                filename: 'my-file-2.pdf',
                key: 'key-2',
                mimeType: 'application/pdf',
                kind: 'attachment',
            });
            await attachment2.$.save();

            // create a test document
            const testDocument = await context.create(TestAttachmentDocument, {
                code: codeForNewDocument,
            });
            await testDocument.$.save();

            idOfNewDocument = testDocument._id;

            // create a second attachment
            const attachmentAssociation = await context.create(AttachmentAssociation, {
                sourceNodeName: 'TestAttachmentDocument',
                sourceNodeId: testDocument._id,
                title: 'attachment title',
                attachment: { _id: attachment2._id },
            });
            await attachmentAssociation.$.save();
        }),
    );

    it('Set orphaned', () =>
        Test.withCommittedContext(async context => {
            await UploadedFile.setOrphanedAttachments(context);

            const attachment1 = (
                await context
                    .query(UploadedFile, {
                        filter: {
                            key: 'key-1',
                            kind: 'attachment',
                        },
                    })
                    .toArray()
            )[0];

            const attachment2 = (
                await context
                    .query(UploadedFile, {
                        filter: {
                            key: 'key-2',
                            kind: 'attachment',
                        },
                    })
                    .toArray()
            )[0];

            assert.isNotNull(await attachment1.orphanedDate);
            assert.isNull(await attachment2.orphanedDate);
        }));

    it('Purge orphaned', () =>
        Test.withCommittedContext(async context => {
            await timers.setTimeout(2000);

            await UploadedFile.purgeOrphanedAttachments(context, 1, 'seconds');

            const attachment1 = await context
                .query(UploadedFile, {
                    filter: {
                        key: 'key-1',
                        kind: 'attachment',
                    },
                })
                .toArray();

            const attachment2 = (
                await context
                    .query(UploadedFile, {
                        filter: {
                            key: 'key-2',
                            kind: 'attachment',
                        },
                    })
                    .toArray()
            )[0];

            assert.equal(attachment1.length, 0);
            assert.isNull(await attachment2.orphanedDate);
        }));

    it('Test delete trigger', async () => {
        const checkAssociationCount = async (count: number) => {
            await Test.withCommittedContext(async context => {
                const associations = await context
                    .query(AttachmentAssociation, {
                        filter: {
                            sourceNodeName: 'TestAttachmentDocument',
                            sourceNodeId: idOfNewDocument, // Only test the attachments for the document created in the Before()
                        },
                    })
                    .toArray();
                assert.equal(associations.length, count);
            });
        };

        await checkAssociationCount(1);

        // Delete test document created by first test of this file
        await Test.withCommittedContext(context =>
            context.delete(TestAttachmentDocument, { code: codeForNewDocument }),
        );

        await checkAssociationCount(0);
    });

    it('cannot attach with unauthorized mimetype', () =>
        Test.withContext(async context => {
            // create an attachment
            const attachment = await context.create(UploadedFile, {
                filename: 'my-wrong-mime-type-file.pdf',
                key: 'key',
                mimeType: 'my-wrong-mime/type',
                kind: 'attachment',
            });

            await assert.isRejected(attachment.$.save(), 'The record was not created.');
        }));

    it('mimeType should not be empty', () =>
        Test.withContext(async context => {
            // create an attachment
            const attachment = await context.create(UploadedFile, {
                filename: 'my-empty-mime-type-file.pdf',
                key: 'key',
                kind: 'attachment',
            });

            await assert.isRejected(attachment.$.save(), 'The record was not created.');
        }));
});
