envConfigs:
  testActiveServiceOptions:
    - workflow
scenarios:
  Can transfer an attachemnt from a customer to its document:
    workflow: 'attachmentTransferActionTest'
    startEvent:
      topic: 'TestAttachmentDocument/created'
      payload: { code: 'CODE1', customer: '#ForTransfer' }
    expectedResult:
      status: 'success'
      variables:
        # Only the uploadedFiles with 'transfer' AND 'contract' should have been transferred
        transferredCount: 2

  Fail if no attachment found:
    workflow: 'attachmentTransferActionTest'
    startEvent:
      topic: 'TestAttachmentDocument/created'
      payload: { code: 'CODE2', customer: '#ForTransferWithError' }
    expectedResult:
      status: 'error'
      errors: [{ message: 'Source attachment not found.', stepId: 'transfer-attachment-1' }]
