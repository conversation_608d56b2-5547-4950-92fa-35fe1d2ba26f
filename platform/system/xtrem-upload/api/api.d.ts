declare module '@sage/xtrem-upload-api-partial' {
    import type { Package as SageXtremAuditing$Package } from '@sage/xtrem-auditing-api';
    import type {
        Package as SageXtremAuthorization$Package,
        UserBillingRole,
        UserBillingRoleBinding,
        UserBillingRoleInput,
        UserGroup,
        UserGroupBinding,
        UserGroupInput,
    } from '@sage/xtrem-authorization-api';
    import type { Package as SageXtremCommunication$Package } from '@sage/xtrem-communication-api';
    import type { Package as SageXtremCustomization$Package } from '@sage/xtrem-customization-api';
    import type { Package as SageXtremDashboard$Package } from '@sage/xtrem-dashboard-api';
    import type { MetaNodeFactory, Package as SageXtremMetadata$Package } from '@sage/xtrem-metadata-api';
    import type { Package as SageXtremRouting$Package } from '@sage/xtrem-routing-api';
    import type { Package as SageXtremScheduler$Package } from '@sage/xtrem-scheduler-api';
    import type {
        Package as SageXtremSystem$Package,
        Site,
        SysClientUserSettings,
        SysClientUserSettingsBinding,
        SysClientUserSettingsInput,
        SysTag,
        User,
        UserPreferences,
        UserPreferencesBinding,
        UserPreferencesInput,
    } from '@sage/xtrem-system-api';
    import type { Package as SageXtremWorkflow$Package } from '@sage/xtrem-workflow-api';
    import type {
        AggregateQueryOperation,
        AggregateReadOperation,
        AsyncOperation,
        BinaryStream,
        ClientCollection,
        ClientNode,
        ClientNodeInput,
        GetDefaultsOperation,
        Operation as Node$Operation,
        QueryOperation,
        ReadOperation,
        decimal,
        integer,
    } from '@sage/xtrem-client';
    export interface UploadStatus$Enum {
        created: 0;
        uploaded: 1;
        uploadFailed: 2;
        verified: 3;
        rejected: 4;
    }
    export type UploadStatus = keyof UploadStatus$Enum;
    export interface UploadedFileKind$Enum {
        upload: 0;
        attachment: 1;
    }
    export type UploadedFileKind = keyof UploadedFileKind$Enum;
    export interface AttachmentAssociation extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        sourceNodeName: string;
        sourceNodeId: integer;
        title: string;
        description: string;
        attachment: UploadedFile;
        isProtected: boolean;
    }
    export interface AttachmentAssociationInput extends ClientNodeInput {
        sourceNodeName?: string;
        sourceNodeId?: integer | string;
        title?: string;
        description?: string;
        attachment?: integer | string;
        isProtected?: boolean | string;
    }
    export interface AttachmentAssociationBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        sourceNodeName: string;
        sourceNodeId: integer;
        title: string;
        description: string;
        attachment: UploadedFile;
        isProtected: boolean;
    }
    export interface AttachmentAssociation$Operations {
        query: QueryOperation<AttachmentAssociation>;
        read: ReadOperation<AttachmentAssociation>;
        aggregate: {
            read: AggregateReadOperation<AttachmentAssociation>;
            query: AggregateQueryOperation<AttachmentAssociation>;
        };
        getDefaults: GetDefaultsOperation<AttachmentAssociation>;
    }
    export interface UploadedFile extends ClientNode {
        _factory: MetaNodeFactory;
        _tags: SysTag[];
        _updateUser: User;
        _createUser: User;
        key: string;
        filename: string;
        mimeType: string;
        lastModified: string;
        lastDownloadDate: string;
        orphanedDate: string;
        contentLength: string;
        kind: UploadedFileKind;
        status: UploadStatus;
        rejectReason: string;
        expirationDate: string;
        replyTopic: string;
        uploadUrl: string;
        downloadUrl: string;
    }
    export interface UploadedFileInput extends ClientNodeInput {
        _tags?: (integer | string)[];
        key?: string;
        filename?: string;
        mimeType?: string;
        lastModified?: string;
        lastDownloadDate?: string;
        orphanedDate?: string;
        contentLength?: decimal | string;
        kind?: UploadedFileKind;
        status?: UploadStatus;
        rejectReason?: string;
        expirationDate?: string;
        replyTopic?: string;
    }
    export interface UploadedFileBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _tags: SysTag[];
        _updateUser: User;
        _createUser: User;
        key: string;
        filename: string;
        mimeType: string;
        lastModified: string;
        lastDownloadDate: string;
        orphanedDate: string;
        contentLength: string;
        kind: UploadedFileKind;
        status: UploadStatus;
        rejectReason: string;
        expirationDate: string;
        replyTopic: string;
        uploadUrl: string;
        downloadUrl: string;
    }
    export interface UploadedFile$Mutations {
        create: Node$Operation<
            {
                data: {
                    _id?: string;
                    filename: string;
                    mimeType: string;
                    _tags?: string[];
                    lastModified: string;
                    contentLength: integer | string;
                    status?: UploadStatus;
                    kind?: UploadedFileKind;
                };
            },
            UploadedFile
        >;
        update: Node$Operation<
            {
                data: {
                    _id: string;
                    _tags?: string[];
                    filename?: string;
                    mimeType?: string;
                    lastModified?: string;
                    contentLength?: integer | string;
                    status?: UploadStatus;
                    kind?: UploadedFileKind;
                };
            },
            UploadedFile
        >;
        copyAttachments: Node$Operation<
            {
                from: {
                    nodeName: string;
                    nodeId: string;
                };
                fromTagsNaturalKeys: string[];
                to: {
                    nodeName: string;
                    nodeId: string;
                };
                failIfNotFound?: boolean | string;
            },
            integer
        >;
        attachToNode: Node$Operation<
            {
                sourceId?: string;
                to: {
                    nodeName: string;
                    nodeId: string;
                };
                failIfNotFound?: boolean | string;
                tagsToAddToPrintingNaturalKeys?: string[];
            },
            integer
        >;
    }
    export interface UploadedFile$AsyncOperations {
        purge: AsyncOperation<
            {
                duration: integer | string;
                unit: string;
            },
            boolean
        >;
    }
    export interface UploadedFile$Operations {
        query: QueryOperation<UploadedFile>;
        read: ReadOperation<UploadedFile>;
        aggregate: {
            read: AggregateReadOperation<UploadedFile>;
            query: AggregateQueryOperation<UploadedFile>;
        };
        mutations: UploadedFile$Mutations;
        asyncOperations: UploadedFile$AsyncOperations;
        getDefaults: GetDefaultsOperation<UploadedFile>;
    }
    export interface CompanyExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        sites: ClientCollection<Site>;
        _attachments: ClientCollection<AttachmentAssociation>;
    }
    export interface CompanyInputExtension {
        id?: string;
        isActive?: boolean | string;
        _attachments?: Partial<AttachmentAssociationInput>[];
    }
    export interface CompanyBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        sites: ClientCollection<Site>;
        _attachments: ClientCollection<AttachmentAssociation>;
    }
    export interface UserExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        email: string;
        firstName: string;
        lastName: string;
        isActive: boolean;
        photo: BinaryStream;
        displayName: string;
        userType: UserType;
        isAdministrator: boolean;
        isDemoPersona: boolean;
        isApiUser: boolean;
        preferences: UserPreferences;
        clientSettings: ClientCollection<SysClientUserSettings>;
        billingRole: UserBillingRole;
        authorizationGroup: ClientCollection<UserGroup>;
        groupDisplay: string;
        _attachments: ClientCollection<AttachmentAssociation>;
        isOperatorUser: boolean;
    }
    export interface UserInputExtension {
        email?: string;
        firstName?: string;
        lastName?: string;
        isActive?: boolean | string;
        photo?: BinaryStream;
        userType?: UserType;
        isAdministrator?: boolean | string;
        isDemoPersona?: boolean | string;
        isApiUser?: boolean | string;
        operatorCode?: string;
        preferences?: UserPreferencesInput;
        clientSettings?: Partial<SysClientUserSettingsInput>[];
        billingRole?: UserBillingRoleInput;
        authorizationGroup?: Partial<UserGroupInput>[];
        _attachments?: Partial<AttachmentAssociationInput>[];
        isOperatorUser?: boolean | string;
    }
    export interface UserBindingExtension {
        _factory: MetaNodeFactory;
        _updateUser: User;
        _createUser: User;
        email: string;
        firstName: string;
        lastName: string;
        isActive: boolean;
        photo: BinaryStream;
        displayName: string;
        userType: UserType;
        isAdministrator: boolean;
        isDemoPersona: boolean;
        isApiUser: boolean;
        operatorCode: string;
        preferences: UserPreferencesBinding;
        clientSettings: ClientCollection<SysClientUserSettingsBinding>;
        billingRole: UserBillingRoleBinding;
        authorizationGroup: ClientCollection<UserGroupBinding>;
        groupDisplay: string;
        _attachments: ClientCollection<AttachmentAssociation>;
        isOperatorUser: boolean;
    }
    export interface Package {
        '@sage/xtrem-upload/AttachmentAssociation': AttachmentAssociation$Operations;
        '@sage/xtrem-upload/UploadedFile': UploadedFile$Operations;
    }
    export interface GraphApi
        extends Package,
            SageXtremAuditing$Package,
            SageXtremAuthorization$Package,
            SageXtremCommunication$Package,
            SageXtremCustomization$Package,
            SageXtremDashboard$Package,
            SageXtremMetadata$Package,
            SageXtremRouting$Package,
            SageXtremScheduler$Package,
            SageXtremSystem$Package,
            SageXtremWorkflow$Package {}
}
declare module '@sage/xtrem-upload-api' {
    export type * from '@sage/xtrem-upload-api-partial';
}
declare module '@sage/xtrem-auditing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-upload-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-authorization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-upload-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-communication-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-upload-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-customization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-upload-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-dashboard-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-upload-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-metadata-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-upload-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-routing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-upload-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-scheduler-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-upload-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-upload-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-workflow-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-upload-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type {
        CompanyBindingExtension,
        CompanyExtension,
        CompanyInputExtension,
        UserBindingExtension,
        UserExtension,
        UserInputExtension,
    } from '@sage/xtrem-upload-api';
    export interface Company extends CompanyExtension {}
    export interface CompanyBinding extends CompanyBindingExtension {}
    export interface CompanyInput extends CompanyInputExtension {}
    export interface User extends UserExtension {}
    export interface UserBinding extends UserBindingExtension {}
    export interface UserInput extends UserInputExtension {}
}
