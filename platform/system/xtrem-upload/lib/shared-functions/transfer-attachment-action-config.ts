import { WorkflowVariable } from '@sage/xtrem-shared';
import type { WorkflowGraphqlActionConfig } from '@sage/xtrem-workflow';

export interface TransferAttachmentActionConfig extends WorkflowGraphqlActionConfig {
    /** The path to the source node _id we are transferring the attachment from */
    fromPath: string;

    /**
     * The natural key of the tags used to filter the attachments to transfer.
     */
    fromTags: string[];

    /**
     * The natural key of the tags to add to the printing.
     * Note: these tags will only be used when the fromPath refers to an uploaded file with a TTL (printing)
     */
    tagsToAddToPrinting: string[];

    /** The path to the destination node _id we are transferring the attachment to */
    toPath: string;

    stepVariables?: WorkflowVariable[];
}
