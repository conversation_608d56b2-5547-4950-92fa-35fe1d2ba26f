import { decorators, Node, Reference } from '@sage/xtrem-core';
import { AttachmentAssociationNode } from '@sage/xtrem-shared';
import * as xtremSystem from '@sage/xtrem-system';
import { UploadedFile } from './uploaded-file';

@decorators.node<AttachmentAssociation>({
    isPublished: true,
    storage: 'sql',
    isCached: true,
    isPlatformNode: true,
    isPlatformNodeExportable: true,
    indexes: [{ orderBy: { sourceNodeName: +1, sourceNodeId: +1, attachment: +1 }, isUnique: true }],
    canExport: false,
    canRead: true,
    async controlDelete(cx): Promise<void> {
        // This will work as we will delete the orphaned attachment association via a trigger on the database and not via the node
        if (await this.isProtected) {
            cx.error.add(
                this.$.context.localize(
                    '@sage/xtrem-upload/attachment_is_protected_delete',
                    'You cannot delete a protected attachment.',
                ),
            );
        }
    },
})
export class AttachmentAssociation extends Node implements AttachmentAssociationNode {
    @decorators.stringProperty<AttachmentAssociation, 'sourceNodeName'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly sourceNodeName: Promise<string>;

    @decorators.integerProperty<AttachmentAssociation, 'sourceNodeId'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
    })
    readonly sourceNodeId: Promise<number>;

    @decorators.stringProperty<AttachmentAssociation, 'title'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly title: Promise<string>;

    @decorators.stringProperty<AttachmentAssociation, 'description'>({
        isPublished: true,
        isStored: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.description,
    })
    readonly description: Promise<string>;

    @decorators.referenceProperty<AttachmentAssociation, 'attachment'>({
        isStored: true,
        isPublished: true,
        node: () => UploadedFile,
        isRequired: true,
        canLookup: false,
        lookupAccess: true,
        async control(cx) {
            const attachment = await this.attachment;
            if (!this.$.isNew) {
                const oldAttachment = await (await this.$.old).attachment;
                if ((await this.isProtected) && attachment._id !== oldAttachment._id)
                    cx.error.add(
                        this.$.context.localize(
                            '@sage/xtrem-upload/attachment_is_protected_update',
                            'You cannot update a protected attachment.',
                        ),
                    );
            }
        },
    })
    readonly attachment: Reference<UploadedFile>;

    @decorators.booleanProperty<AttachmentAssociation, 'isProtected'>({
        isStored: true,
        isPublished: true,
        defaultValue: false,
        lookupAccess: true,
    })
    readonly isProtected: Promise<boolean>;
}
