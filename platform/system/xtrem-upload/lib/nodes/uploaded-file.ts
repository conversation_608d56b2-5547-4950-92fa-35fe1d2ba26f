import * as xtremCommunication from '@sage/xtrem-communication';
import {
    AccessRights,
    AsyncResponse,
    AuthorizationError,
    ConfigManager,
    Context,
    CoreHooks,
    FileStorageManager,
    Node,
    NodeCreateData,
    SystemError,
    UserAccess,
    ValidationSeverity,
    asyncArray,
    datetime,
    decimal,
    decorators,
    rootUserEmail,
} from '@sage/xtrem-core';
import { FileTimeToLive } from '@sage/xtrem-file-storage';
import { InfrastructureHelper } from '@sage/xtrem-infrastructure-adapter';
import {
    InitialNotification,
    UploadedFileNode,
    attachmentsMimeTypes,
    attachmentsMimeTypesByExtention,
    toInteger,
} from '@sage/xtrem-shared';
import * as xtremSystem from '@sage/xtrem-system';
import * as fs from 'fs';
import { isBinaryFileSync } from 'isbinaryfile';
import * as _ from 'lodash';
import { nanoid } from 'nanoid';
import * as fsp from 'path';
import { UploadStatus, UploadStatusEnum, uploadStatusDataType } from '../enums/upload-status';
import { UploadedFileKind, UploadedFileKindDataType } from '../enums/uploaded-file-kind';
import { loggers } from '../loggers';
import { AttachmentAssociation } from './attachment-association';

export interface UploadedFileScanResult {
    contextValue: any;
    contextId: string;
    result: string;
    reason: string;
}

interface AttachmentTransferFromTo {
    nodeName: string;
    nodeId: string;
}

const logger = loggers.uploads;
@decorators.node<UploadedFile>({
    isPublished: true,
    storage: 'sql',
    isPlatformNode: true,
    canRead: true,
    canDeleteMany: true,
    canExport: false,
    isPlatformNodeExportable: true,
    hasTags: true,
    indexes: [{ orderBy: { key: +1 }, isUnique: true }],
    async saveBegin() {
        const canSkipAntivirusScan = (await this.kind) === 'upload' && (await this.canSkipAntivirusScan);
        if (canSkipAntivirusScan) return;
        // The UI is updating the file status, we may reconsider this but in the meantime we protect the status
        // from being changed back to uploaded when the infra has already set it to verified.
        if (this.$.isNew) {
            if ((await this.status) !== 'created') {
                throw this.$.factory.logicError(`Invalid status: ${await this.status}`);
            }
        } else {
            const old = await this.$.old;
            const oldStatus = await old.status;
            if (oldStatus === 'verified') {
                await this.$.set({ status: oldStatus });
            }
        }
    },
    authorizedBy: (context, propertyOrOperation, args) =>
        UploadedFile.getUserAccess(context, propertyOrOperation, args),
})
export class UploadedFile extends Node implements UploadedFileNode {
    @decorators.stringProperty<UploadedFile, 'key'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.url,
        defaultValue: () => {
            return nanoid();
        },
    })
    readonly key: Promise<string>;

    /**
     * The full key for accessing the content of the uploadedFile when using
     * InfrastructureHelper.copyFileToFolder and InfrastructureHelper.readFile functions.
     */
    @decorators.stringProperty<UploadedFile, 'fullKeyForContent'>({
        async getValue() {
            if ((await this.kind) === 'upload') {
                // Uploads already have the folder in their key 'print-output/xxx', ...
                return this.key;
            }
            // Note: the attachments are stored in the 'attachments' folder but their key does not contain the folder name
            return `attachments/${await this.key}`;
        },
    })
    readonly fullKeyForContent: Promise<string>;

    @decorators.stringProperty<UploadedFile, 'filename'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly filename: Promise<string>;

    @decorators.stringProperty<UploadedFile, 'mimeType'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.mimeType,
        async control(cx, val) {
            if ((await this.kind) === 'upload') return;
            if (!attachmentsMimeTypes.includes(val)) {
                cx.addDiagnose(
                    ValidationSeverity.error,
                    cx.localize(
                        '@sage/xtrem-upload/nodes__attachment__attachment_mime_type_not_authorized',
                        'file type is not authorized.',
                    ),
                );
            }
        },
    })
    readonly mimeType: Promise<string>;

    @decorators.datetimeProperty<UploadedFile, 'lastModified'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
    })
    readonly lastModified: Promise<datetime | null>;

    @decorators.datetimeProperty<UploadedFile, 'lastDownloadDate'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        lookupAccess: true,
    })
    readonly lastDownloadDate: Promise<datetime | null>;

    @decorators.datetimeProperty<UploadedFile, 'orphanedDate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
    })
    readonly orphanedDate: Promise<datetime | null>;

    @decorators.decimalProperty<UploadedFile, 'contentLength'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.contentLengthDecimalDataType,
        control(cx, val) {
            const maxUploadSize = ConfigManager.getSizeLimit('maxUploadSize');
            if (val > maxUploadSize) {
                cx.addDiagnose(
                    ValidationSeverity.error,
                    cx.localize(
                        '@sage/xtrem-upload/nodes__uploaded_file__max_file_size',
                        'The file size ({{val}}) exceeds the maximum length allowed ({{maxUploadSize}} bytes).',
                        { val, maxUploadSize },
                    ),
                );
            }
        },
    })
    readonly contentLength: Promise<decimal>;

    @decorators.enumProperty<UploadedFile, 'kind'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isRequired: true,
        dataType: () => UploadedFileKindDataType,
    })
    readonly kind: Promise<UploadedFileKind>;

    @decorators.enumProperty<UploadedFile, 'status'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => uploadStatusDataType,
        async adaptValue(val) {
            const status = await this.status;
            if (val === 'uploaded' && status !== 'created') {
                return this.status;
            }
            return val;
        },
        defaultValue: () => {
            return 'created';
        },
    })
    readonly status: Promise<UploadStatus>;

    @decorators.stringProperty<UploadedFile, 'rejectReason'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.description,
    })
    readonly rejectReason: Promise<string>;

    @decorators.stringProperty<UploadedFile, 'uploadUrl'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.url,
        dependsOn: ['key', 'status', 'owner', 'canSkipAntivirusScan', 'kind'],
        async computeValue() {
            const kind = await this.kind;
            const useAsyncContext = kind === 'attachment' || !(await this.canSkipAntivirusScan);
            const key = await this.key;
            if (useAsyncContext) {
                const user = await this.$.context.user;
                const canAccess = user?.email === rootUserEmail || (await this.owner) === String(user?._id);

                if (!canAccess)
                    throw new AuthorizationError(
                        this.$.context.localize(
                            '@sage/xtrem-upload/user-not-allowed-to-upload',
                            'You cannot upload a file.',
                        ),
                    );

                // TODO: throw a detailed message already uploaded or could not be uploaded with the error.
                // We can only request an upload URL if the status is created, otherwise the file upload has completed or failed already
                if ((await this.status) !== 'created') return '';

                // NOTE: This getValue calls infrastructure methods, and someone who has access to create on this node will (import execution access for instance),
                // have access to spam the create UploadedFile graphql request and get many presigned upload URLs for this tenant.
                // We need to consider metering this node per tenant user
                const asyncContext = await InfrastructureHelper.getAsyncContext(key);
                if (!asyncContext) {
                    // Create the async context if it does not exist
                    if (kind === 'upload') {
                        // If the UploadedFile transaction fails the context will expire in 15 minutes and the context will be deleted
                        // by the infrastructure
                        await InfrastructureHelper.createAsyncContext(
                            this.$.context,
                            'CONTEXT_FILE_UPLOAD',
                            { replyTopic: 'UploadedFile/InfrastructureComplete' },
                            {
                                contextId: key,
                                expireDatetimeIso: datetime.now().addMinutes(15).toString(),
                                s3FileTTL: FileTimeToLive.Expire10Days,
                            },
                        );
                    } else {
                        // If the Attachment transaction fails the context will expire in 15 minutes and the context will be deleted
                        // by the infrastructure
                        const extension = fsp.extname(await this.filename);
                        const extensionMimeTypes = attachmentsMimeTypesByExtention[extension.slice(1)] ?? [];
                        const mimeType = _.uniq([await this.mimeType, ...extensionMimeTypes]);
                        await InfrastructureHelper.createAsyncContext(
                            this.$.context,
                            'CONTEXT_ATTACHMENT_UPLOAD',
                            { replyTopic: 'UploadedFile/InfrastructureComplete' },
                            {
                                contextId: key,
                                expireDatetimeIso: datetime.now().addMinutes(15).toString(),
                                mimeType,
                                s3FileTTL: FileTimeToLive.NoTTL,
                            },
                        );
                    }
                }
            }

            if (kind === 'upload') return InfrastructureHelper.getUploadPresignedUrl(this.$.context, key);
            return InfrastructureHelper.generateAttachmentUploadPresignedUrl(this.$.context, key);
        },
    })
    readonly uploadUrl: Promise<string>;

    @decorators.booleanProperty<UploadedFile, 'canSkipAntivirusScan'>({
        isStored: true,
        isPublished: false,
    })
    readonly canSkipAntivirusScan: Promise<boolean>;

    @decorators.stringProperty<UploadedFile, 'downloadUrl'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.url,
        dependsOn: ['key', 'status', 'owner'],
        async computeValue() {
            if (!(await UploadedFile.canGetDownloadUrl(this))) return Promise.resolve('');
            return FileStorageManager.getOnDemandDownloadUrl(this);
        },
    })
    readonly downloadUrl: Promise<string>;

    @decorators.datetimeProperty<UploadedFile, 'expirationDate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
    })
    readonly expirationDate: Promise<datetime>;

    // Note: We store the owner as a string, as we do not have the User node in xtrem-system, we type as string as integers
    // cannot store the large numbers in _id
    @decorators.stringProperty<UploadedFile, 'owner'>({
        isStored: true,
        isPublished: false,
        async defaultValue() {
            return String((await this.$.context.user)?._id) || '';
        },
        dataType: () => xtremSystem.dataTypes.id,
    })
    readonly owner: Promise<string>;

    @decorators.stringProperty<UploadedFile, 'replyTopic'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.shortDescription,
        defaultValue: 'UploadedFile/processUpload',
    })
    readonly replyTopic: Promise<string>;

    @decorators.notificationListener<typeof UploadedFile>({
        topic: 'UploadedFile/InfrastructureComplete',
    })
    static async onInfrastructureComplete(context: Context, envelope: UploadedFileScanResult): Promise<void> {
        const uploadedFile = await context.tryRead(UploadedFile, { key: envelope.contextId }, { forUpdate: true });
        if (!uploadedFile) {
            logger.error(`Uploaded file ${envelope.contextId} not found`);
            return;
        }

        const kind = await uploadedFile.kind;

        const status = await uploadedFile.status;
        logger.info(`[uploaded ${uploadedFile._id}] onInfrastrastructureComplete initial status ${status}`);

        // See https://confluence.sage.com/pages/viewpage.action?spaceKey=XTREEM&title=Async+Context+Module
        if (envelope.result !== 'success') {
            let rejectReason = envelope.reason;
            if (!rejectReason) rejectReason = `Upload rejected: ${envelope.result}`;
            await uploadedFile.$.set({ status: 'rejected', rejectReason });
        } else {
            await uploadedFile.$.set({ status: 'verified' });
        }

        if (kind === 'upload') {
            const asyncContext = await InfrastructureHelper.getAsyncContext(envelope.contextId);
            const foundMimeType = asyncContext?.contextInfra?.foundMimeType;
            if (foundMimeType) {
                await uploadedFile.$.set({ mimeType: foundMimeType });
            }
        }

        await uploadedFile.$.save();

        const updatedStatus = await uploadedFile.status;
        logger.info(`[uploaded ${uploadedFile._id}] onInfrastructureComplete updated status ${updatedStatus}`);

        if (kind === 'upload') {
            await CoreHooks.communicationManager.notify(context, await uploadedFile.replyTopic, {
                uploadedFileId: uploadedFile._id,
                status: updatedStatus,
            });
        } else {
            await UploadedFile.sendUserNotification(context, uploadedFile);
        }
    }

    @decorators.mutation<typeof UploadedFile, 'create'>({
        isPublished: true,
        parameters: [
            {
                name: 'data',
                type: 'object',
                properties: {
                    _id: { type: 'string' },
                    filename: { type: 'string', isMandatory: true },
                    mimeType: { type: 'string', isMandatory: true },
                    _tags: { type: 'array', item: 'string' },
                    lastModified: { type: 'datetime', isMandatory: true },
                    contentLength: { type: 'integer', isMandatory: true },
                    status: { type: 'enum', dataType: () => uploadStatusDataType },
                    kind: { type: 'enum', dataType: () => UploadedFileKindDataType },
                },
                isMandatory: true,
            },
        ],
        return: {
            type: 'reference',
            node: () => UploadedFile,
        },
    })
    static async create(context: Context, data: NodeCreateData<UploadedFile>): Promise<UploadedFile> {
        // pick only expected values to get rid of status
        const file = await context.create(UploadedFile, _.omit(data, ['status']));

        await file.$.save();
        return file;
    }

    @decorators.mutation<typeof UploadedFile, 'update'>({
        isPublished: true,
        parameters: [
            {
                name: 'data',
                type: 'object',
                properties: {
                    _id: { type: 'string', isMandatory: true },
                    _tags: { type: 'array', item: 'string' },
                    filename: { type: 'string' },
                    mimeType: { type: 'string' },
                    lastModified: { type: 'datetime' },
                    contentLength: { type: 'integer' },
                    status: { type: 'enum', dataType: () => uploadStatusDataType },
                    kind: { type: 'enum', dataType: () => UploadedFileKindDataType },
                },
                isMandatory: true,
            },
        ],
        return: {
            type: 'reference',
            node: () => UploadedFile,
        },
    })
    static async update(context: Context, data: NodeCreateData<UploadedFile>): Promise<UploadedFile> {
        // can only update status
        const { _id, status, _tags } = data;
        const file = await context.read(UploadedFile, { _id }, { forUpdate: true });
        const fileStatus = await file.status;
        if (status == null && _tags) {
            await file.$.set({ _tags });
            await file.$.save();
            return file;
        }
        switch (fileStatus) {
            case status:
                // no change required
                break;
            case 'created':
                if (status === 'uploaded' || status === 'uploadFailed') {
                    await file.$.set({ status });
                    await file.$.save();
                }
                break;
            default:
                context.diagnoses.push({
                    message: context.localize(
                        '@sage/xtrem-upload/nodes__uploaded_file__status-unchanged',
                        "The status cannot change from '{{from}}' to '{{to}}'.",
                        {
                            from: fileStatus,
                            to: status,
                        },
                    ),
                    path: [],
                    severity: ValidationSeverity.warn,
                });
                break;
        }
        return file;
    }

    /**
     * Checks if a download URL can be obtained for the given `UploadedFile` node.
     * @param node - The `UploadedFile` node to check.
     * @returns A promise that resolves to a boolean indicating whether a download URL can be obtained.
     */
    static async canGetDownloadUrl(node: UploadedFile): Promise<boolean> {
        const kind = await node.kind;

        if (kind === 'upload') {
            const context = node.$.context;
            const user = await context.user;
            const canAccess = user?.email === rootUserEmail || (await node.owner) === String(user?._id);
            if (!canAccess) return false;
        }

        const canSkipAntivirusScan = await node.canSkipAntivirusScan;
        if (canSkipAntivirusScan) return true;

        // If the file is not verified then there cannot be a download URL
        if ((await node.status) !== 'verified') return false;

        if (kind === 'attachment') return true;

        const key = await node.key;
        const asyncContext = await InfrastructureHelper.getAsyncContext(key);
        if (!asyncContext) {
            // return nothing here, if someone is requesting a download URL and the async context is not found, then
            // 2 things may have happend,
            // 1. the context expired and was deleted by the infrastructure
            // 2. the uploaded file has been processed and the async context has been deleted by us
            return false;
        }
        return true;
    }

    static async createAndPopulateUploadedFile(
        context: Context,
        file: {
            kind: UploadedFileKind;
            filename: string;
            type: string;
            replyTopic: string;
            data: string;
            canSkipAntivirusScan: boolean;
        },
    ): Promise<UploadedFile> {
        const uploadedFile = await context.create(UploadedFile, {
            kind: file.kind,
            contentLength: file.data.length,
            filename: file.filename,
            lastModified: datetime.now(),
            expirationDate: datetime.now().addDays(10),
            mimeType: file.type,
            canSkipAntivirusScan: file.canSkipAntivirusScan,
            replyTopic: file.replyTopic,
        });
        await uploadedFile.$.save();

        const buffer = Buffer.from(file.data);
        // We need to inspect the stream and cancel the upload if we find any binary characters in the file content
        if (file.canSkipAntivirusScan && isBinaryFileSync(buffer)) {
            await uploadedFile.$.set({ status: 'uploadFailed' });
        } else {
            const uploadUrl = await uploadedFile.uploadUrl;
            logger.debug(
                () => `UploadedFile:createAndPopulateUploadedFile uploadUrl (${uploadUrl}) data (${file.data})`,
            );

            const fileCreated = await InfrastructureHelper.createFile(
                context,
                await uploadedFile.fullKeyForContent,
                `Generated file - ${file.filename}`,
                buffer,
                FileTimeToLive.Expire10Days,
            );

            if (fileCreated) {
                logger.debug(() => `UploadedFile:createAndPopulateUploadedFile fileCreated (${fileCreated.ETag})`);
                await uploadedFile.$.set({ status: 'uploaded' });
            } else {
                await uploadedFile.$.set({ status: 'uploadFailed' });
            }
        }
        await uploadedFile.$.save();
        return uploadedFile;
    }

    /**
     * Download the content of the uploaded file.
     * @param context
     * @returns
     */
    async downloadFileContent(context: Context): Promise<Buffer> {
        const fullKey = await this.fullKeyForContent;
        logger.debug(() => `[uploadedFile: downloadFileContent id ${this._id}, fullKey ${fullKey}]`);
        if (!(await this.canSkipAntivirusScan) && (await this.status) !== 'verified') {
            throw new SystemError(`UploadedFile:downloadFileContent Request file ${this._id} has not been verified`);
        }

        try {
            const downloadResponse = await InfrastructureHelper.readFile(context, fullKey);
            if (downloadResponse != null && downloadResponse.body != null) {
                const content = await downloadResponse.body.transformToByteArray();
                logger.debug(() => `uploadedFile: downloadFileContent body ${content}`);
                return Buffer.from(content);
            }
        } catch (error) {
            throw new SystemError(`UploadedFile:downloadFileContent Error downloading file ${this._id}`, error);
        }
        throw new SystemError(`Could not download UploadedFile(${this._id})`);
    }

    /**
     * Download the uploaded file to the local filesystem
     *
     * @param localFilename the local filename to copy the uploadedFile to
     * @returns
     */
    async downloadTo(context: Context, localFilename: string): Promise<void> {
        await logger.verboseAsync(
            async () => `Downloading uploadedFile(${this._id}/${await this.fullKeyForContent}) to ${localFilename}`,
        );
        const content = await this.downloadFileContent(context);
        await fs.promises.writeFile(localFilename, content);
    }

    static canCopyAttachments(
        _options: { action: string; trackingId: string },
        context: Context,
        from: AttachmentTransferFromTo,
        _fromTagsNaturalKeys: string[],
        to: AttachmentTransferFromTo,
    ): AsyncResponse<UserAccess | undefined> {
        if (from === undefined || to === undefined) return undefined;

        return AccessRights.getUserAccessOnOperations(context, [
            { nodeName: from.nodeName, operationName: 'read' },
            { nodeName: to.nodeName, operationName: 'update' },
        ]);
    }

    /**
     * Copy attachments from one node to another.
     * @param context - The context.
     * @param from - The source node.
     * @param fromTagsNaturalKeys - The natural keys of the tags used to filter the attachments to transfer.
     * @param to - The destination node.
     * @param failIfNotFound - Whether to fail if no source attachment is found.
     */
    @decorators.mutation<typeof UploadedFile, 'copyAttachments'>({
        isPublished: true,
        authorizedBy: 'canCopyAttachments',
        parameters: [
            {
                name: 'from',
                type: 'object',
                isMandatory: true,
                properties: {
                    nodeName: { type: 'string', isMandatory: true },
                    nodeId: { type: 'string', isMandatory: true },
                },
            },
            {
                name: 'fromTagsNaturalKeys',
                type: 'array',
                item: {
                    type: 'string',
                },
                isMandatory: true,
            },
            {
                name: 'to',
                type: 'object',
                isMandatory: true,
                properties: {
                    nodeName: { type: 'string', isMandatory: true },
                    nodeId: { type: 'string', isMandatory: true },
                },
            },
            { name: 'failIfNotFound', type: 'boolean' },
        ],
        return: { type: 'integer' },
    })
    static async copyAttachments(
        context: Context,
        from: AttachmentTransferFromTo,
        fromTagsNaturalKeys: string[],
        to: AttachmentTransferFromTo,
        failIfNotFound = false,
    ): Promise<number> {
        const allAssociations = await context
            .query(AttachmentAssociation, {
                filter: {
                    sourceNodeName: from.nodeName,
                    sourceNodeId: from.nodeId,
                },
            })
            .toArray();

        const fromAttachementsOrNulls = await asyncArray(allAssociations)
            .filter(async asso => {
                const uploadedFile = await asso.attachment;
                const tags = await uploadedFile._tags;
                // Build the list of natural keys of the tags
                const tagsNks = await asyncArray(tags)
                    .map(async tag => `#${await tag.name}` as string)
                    .toArray();
                // only keep the uploadedFile where tagsNks contains all the fromTagsNaturalKeys
                return fromTagsNaturalKeys.every(tagNk => tagsNks.includes(tagNk));
            })
            .toArray();
        const fromAttachements = fromAttachementsOrNulls.filter(asso => asso);

        if (fromAttachements.length === 0) {
            if (failIfNotFound) {
                context.diagnoses.push({
                    message: context.localize(
                        '@sage/xtrem-upload/nodes__attachement__source-attachment-not-found',
                        'Source attachment not found.',
                    ),
                    path: [],
                    severity: ValidationSeverity.error,
                });
            }
            return 0;
        }

        // Create new attachments
        await asyncArray(fromAttachements).forEach(async (fromAssociation: AttachmentAssociation) => {
            const toAssociation = await context.create(AttachmentAssociation, {
                sourceNodeName: to.nodeName,
                sourceNodeId: toInteger(to.nodeId),
                title: await fromAssociation.title,
                description: await fromAssociation.description,
                attachment: await fromAssociation.attachment,
            });
            await toAssociation.$.save();
        });
        return fromAttachements.length;
    }

    static canAttachToNode(
        _options: { action: string; trackingId: string },
        context: Context,
        _sourceId: string,
        to: AttachmentTransferFromTo,
    ): AsyncResponse<UserAccess | undefined> {
        if (to === undefined) return undefined;

        return AccessRights.getUserAccessOnOperations(context, [
            { nodeName: 'UploadedFile', operationName: 'read' },
            { nodeName: to.nodeName, operationName: 'update' },
        ]);
    }

    /**
     * Attach an uploadedFile to a node.
     * @param context - The context.
     * @param sourceId - The id of the uploadedFile to attach.
     * @param to - The destination node.
     * @param failIfNotFound - Whether to fail if no source attachment is found.
     * @param tagsToAddToPrintingNaturalKeys - The (optional) natural keys of the tags to add to the new UploadedFile.
     * Note: tagsToAddToPrintingNaturalKeys will only be used when the sourceId refers to an uploaded file with a TTL (printing)
     */
    @decorators.mutation<typeof UploadedFile, 'attachToNode'>({
        isPublished: true,
        authorizedBy: 'canAttachToNode',
        parameters: [
            {
                name: 'sourceId',
                type: 'string',
            },
            {
                name: 'to',
                type: 'object',
                isMandatory: true,
                properties: {
                    nodeName: { type: 'string', isMandatory: true },
                    nodeId: { type: 'string', isMandatory: true },
                },
            },
            { name: 'failIfNotFound', type: 'boolean' },
            {
                name: 'tagsToAddToPrintingNaturalKeys',
                type: 'array',
                item: {
                    type: 'string',
                },
                isMandatory: false,
            },
        ],
        return: { type: 'integer' },
    })
    static async attachToNode(
        context: Context,
        sourceId: string,
        to: AttachmentTransferFromTo,
        failIfNotFound = false,
        tagsToAddToPrintingNaturalKeys?: string[],
    ): Promise<number> {
        const uploadedFile = await context.tryRead(UploadedFile, { _id: sourceId });
        if (uploadedFile == null) {
            if (failIfNotFound) {
                context.diagnoses.push({
                    message: context.localize(
                        '@sage/xtrem-upload/nodes__attachement__source-attachment-not-found',
                        'Source attachment not found.',
                    ),
                    path: [],
                    severity: ValidationSeverity.error,
                });
            }
            return 0;
        }

        // Check if the uploadedFile has a TTL set
        let attachment = uploadedFile;
        const kind = await uploadedFile.kind;
        if (kind === 'upload') {
            // Uploads have a TTL, as we want to bind the uploadedFile to a node, we need an uploadedFile with NoTTL.
            // For instance, the uploadedFile is a report generated by a 'Print' action. It has a TTL of 10 days
            // but now, we want to attach it to a SalesOrder. The doc we are attaching is not a temporary doc anymore.

            // Duplicate the upload into an attachment
            attachment = await uploadedFile.$.duplicate({
                kind: 'attachment',
                status: 'created',
                // The new attachments will be stored in the 'attachments' prefix, in S3
                key: nanoid(),
            });
            if (tagsToAddToPrintingNaturalKeys?.length) {
                const tagsToAdd = await context
                    .query(xtremSystem.nodes.SysTag, {
                        filter: { _id: { _in: tagsToAddToPrintingNaturalKeys } },
                    })
                    .toArray();
                const allTags = await attachment._tags;
                tagsToAdd.forEach(tag => allTags.push(tag));
                await attachment.$.set({ _tags: allTags });
            }
            await attachment.$.save();
            // We can't set directly the status to verified (see SaveBegin)
            await attachment.$.set({ status: 'verified' });
            await attachment.$.save();

            // Note: simply saving the attachment did not create any file (either on S3 or locally when in devMode)
            // This job will be done by the copyFileToFolder function
            await InfrastructureHelper.copyFileToFolder(
                context,
                await uploadedFile.fullKeyForContent,
                await attachment.fullKeyForContent,
                FileTimeToLive.NoTTL,
            );
            logger.info(
                `Uploaded file (${await uploadedFile.key}/${kind}) duplicated to ${await attachment.key}/${await attachment.kind}`,
            );
        }

        // Create the new attachment
        const toAssociation = await context.create(AttachmentAssociation, {
            sourceNodeName: to.nodeName,
            sourceNodeId: toInteger(to.nodeId),
            title: await uploadedFile.filename,
            description: '',
            attachment,
        });

        await toAssociation.$.save();
        return 1;
    }

    @decorators.asyncMutation<typeof UploadedFile, 'purge'>({
        isPublished: true,
        isSchedulable: true,
        startsReadOnly: true,
        parameters: [
            { name: 'duration', type: 'integer', isMandatory: true },
            { name: 'unit', type: 'string', isMandatory: true },
        ],
        return: { type: 'boolean' },
    })
    static async purge(context: Context, duration: number, unit: Intl.RelativeTimeFormatUnit): Promise<boolean> {
        await context.runInWritableContext(async writableContext => {
            await this.setOrphanedAttachments(writableContext);
        });

        await context.runInWritableContext(async writableContext => {
            await this.purgeOrphanedAttachments(writableContext, duration, unit);
        });

        return true;
    }

    /**
     * @internal
     */
    static purgeOrphanedAttachments(
        context: Context,
        duration: number,
        unit: Intl.RelativeTimeFormatUnit,
    ): Promise<number> {
        return context.deleteMany(UploadedFile, {
            orphanedDate: { _lte: xtremCommunication.functions.dateAddUnit(-duration, unit), _ne: null },
            kind: 'attachment',
        });
    }

    /**
     * @internal
     */
    static async setOrphanedAttachments(context: Context): Promise<number> {
        const attachmentsIds = await Promise.all(
            (await context.query(AttachmentAssociation, { filter: { attachment: { _ne: null } } }).toArray()).map(
                async (attachmentAssociation: AttachmentAssociation) => (await attachmentAssociation.attachment)._id,
            ),
        );
        return context.bulkUpdate(UploadedFile, {
            set: { orphanedDate: datetime.now() },
            where: {
                _id: {
                    _nin: attachmentsIds,
                },
                kind: 'attachment',
            },
        });
    }

    private static async sendUserNotification(context: Context, attachment: UploadedFile): Promise<void> {
        const level = (await attachment.status) === UploadStatusEnum.rejected.toString() ? 'error' : 'success';
        let title = '';
        let description = '';
        let actions: InitialNotification['actions'] = [];
        let icon: InitialNotification['icon'];
        if (level === 'error') {
            title = context.localize('@sage/xtrem-upload/file-attachment-title-error', 'File attachment error.');
            description = context.localize(
                '@sage/xtrem-upload/upload-finish-status-error',
                'The attachment {{fileName}} did not pass the security check.',
                { fileName: await attachment.filename },
            );
            icon = 'alert';
            logger.error(`Attachment ${attachment._id} failed to upload: ${await attachment.rejectReason}`);
        } else {
            title = context.localize('@sage/xtrem-upload/file-attachment-title-success', 'File attachment success.');
            description = context.localize(
                '@sage/xtrem-upload/upload-finish-status-success',
                'File {{fileName}} uploaded successfully.',
                { fileName: await attachment.filename },
            );
            icon = 'download';
            actions = [
                {
                    link: await attachment.downloadUrl,
                    style: 'link',
                    title: context.localize(
                        '@sage/xtrem-upload/notification-action-title-attachment-download-url',
                        'Attachment Download Url',
                    ),
                },
            ];
        }

        const payload: InitialNotification = {
            title,
            description,
            icon,
            level,
            shouldDisplayToast: true,
            actions,
        };
        await context.notifyUser(payload);
    }

    static async getUserAccess(context: Context, propertyOrOperation: string, args: any): Promise<UserAccess> {
        const attachmentFactory = context.application.getFactoryByConstructor(UploadedFile);

        const attachmentProperty =
            attachmentFactory.properties.find(p => p.isPublished && p.name === propertyOrOperation) ??
            attachmentFactory.publishedSystemProperties[propertyOrOperation.substring(1)];
        // if the operation is a published property of the UploadedFile node then it is authorized
        if (attachmentProperty != null) return { sites: null, accessCodes: null, status: 'authorized' };

        if (args?.data && ['create', 'update'].includes(propertyOrOperation)) {
            let kind = args.data.kind as string;

            if (!kind) {
                if (!args.data._id) throw new Error('Missing kind or _id');
                kind = await (await context.read(UploadedFile, { _id: args.data._id })).kind;
            }

            if (kind === 'attachment') {
                // if the operation on the attachment node is not a standard operation, then it is not authorized
                // List of all nodes with attachments
                const factoriesWithAttachments = context.application.getAllFactories().filter(f => f.hasAttachments);

                // current user has create/update/delete access to any node with attachments
                // then they have full access to the same operation on the Attachments node
                if (
                    await asyncArray(factoriesWithAttachments).some(async f => {
                        return (
                            (await Context.accessRightsManager.getUserAccessFor(context, f.name, 'create')).status ===
                                'authorized' ||
                            (await Context.accessRightsManager.getUserAccessFor(context, f.name, 'update')).status ===
                                'authorized'
                        );
                    })
                ) {
                    return { sites: null, accessCodes: null, status: 'authorized' };
                }

                return { sites: null, accessCodes: null, status: 'unauthorized' };
            }
        }

        return Context.accessRightsManager.getUserAccessFor(context, 'UploadedFile', propertyOrOperation);
    }
}

FileStorageManager.registerDownloadHandler(UploadedFile, async (node: UploadedFile) => {
    const kind = await node.kind;
    if (!(await UploadedFile.canGetDownloadUrl(node))) return Promise.resolve('');
    const key = await node.key;
    const filename = await node.filename;

    if (kind === 'upload') {
        return InfrastructureHelper.getDownloadPresignedUrl(node.$.context, key, {
            ttlSeconds: 60,
            filename,
        });
    }
    return InfrastructureHelper.generateAttachmentDownloadPresignedUrl(node.$.context, key, 60, filename);
});
