import { ConfigManager, Context, NodePayloadData } from '@sage/xtrem-core';
import { promisify } from 'util';
import { UploadedFileKind } from '../enums/uploaded-file-kind';
import { UploadedFile } from '../nodes/uploaded-file';

export async function createUploadedFile(
    context: Context,
    parameters: {
        kind: UploadedFileKind;
        filename: string;
        filenameExtension: string;
        objectKeyPrefix: string;
        timestamp: string;
        mimeType: string;
    },
): Promise<{ result: NodePayloadData<UploadedFile>; objectKey: string }> {
    const { filename, filenameExtension, objectKeyPrefix, timestamp, mimeType } = parameters;
    const maxRetryUploadedFileCreation = ConfigManager.current.exportCsv?.maxRetryUploadedFileCreation ?? 3;

    const createUploadedFileNode = async (key: string, keyFilename: string): Promise<NodePayloadData<UploadedFile>> => {
        const uploadFileNode = await context.create(UploadedFile, {
            kind: parameters.kind,
            key,
            status: 'verified',
            canSkipAntivirusScan: true,
            filename: keyFilename,
            mimeType,
        });
        await uploadFileNode.$.save();
        return uploadFileNode.$.payload();
    };
    let count = 0;
    let success = false;
    let result: NodePayloadData<UploadedFile> = {};
    let { _filename, _objectKey } = getFilenameAndObjectKey({
        filename,
        filenameExtension,
        objectKeyPrefix,
        timestamp,
    });

    while (!success && count <= maxRetryUploadedFileCreation) {
        const fileExist = await context.tryRead(UploadedFile, { key: _objectKey });
        if (!fileExist) {
            result = await createUploadedFileNode(_objectKey, _filename);
            success = true;
        } else {
            count += 1;
            await promisify(setTimeout)(1000); // awaits 1 second
            _filename = getFilename({ filename, filenameExtension, timestamp, count });
            _objectKey = getObjectKey(_filename, objectKeyPrefix);
        }
    }

    if (!success) throw new Error(`UploadedFile creation has failed after ${maxRetryUploadedFileCreation} retries`);
    return { result, objectKey: _objectKey };
}

function getFilenameAndObjectKey(parameters: {
    filename: string;
    filenameExtension: string;
    objectKeyPrefix: string;
    timestamp: string;
    count?: number;
}): { _filename: string; _objectKey: string } {
    const { filename, filenameExtension, objectKeyPrefix, timestamp, count } = parameters;
    const _filename = getFilename({ filename, filenameExtension, timestamp, count });
    return {
        _filename,
        _objectKey: getObjectKey(_filename, objectKeyPrefix),
    };
}

function getFilename(parameters: {
    filename: string;
    filenameExtension: string;
    timestamp: string;
    count?: number;
}): string {
    const countString = parameters.count ? `-(${parameters.count.toString()})` : '';
    return `${parameters.filename}-${parameters.timestamp}${countString}.${parameters.filenameExtension}`;
}

function getObjectKey(_filename: string, objectKeyPrefix: string): string {
    return `${objectKeyPrefix}/${_filename}`;
}
