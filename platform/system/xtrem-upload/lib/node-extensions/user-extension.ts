import { decorators, NodeExtension } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';

@decorators.nodeExtension<UserExtension>({
    extends: () => xtremSystem.nodes.User,
    hasAttachments: true,
})
export class UserExtension extends NodeExtension<xtremSystem.nodes.User> {}

declare module '@sage/xtrem-system/lib/nodes/user' {
    export interface User extends UserExtension {}
}
