import { decorators, NodeExtension } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';

@decorators.nodeExtension<CompanyExtension>({
    extends: () => xtremSystem.nodes.Company,
    hasAttachments: true,
})
export class CompanyExtension extends NodeExtension<xtremSystem.nodes.Company> {}

declare module '@sage/xtrem-system/lib/nodes/company' {
    export interface Company extends CompanyExtension {}
}
