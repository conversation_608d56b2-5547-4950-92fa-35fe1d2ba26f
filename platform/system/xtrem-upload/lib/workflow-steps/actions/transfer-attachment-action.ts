import { Logger, ValidationContext, WorkflowStepDescriptor } from '@sage/xtrem-core';
import { GraphqlAction, Request } from '@sage/xtrem-workflow';
import { TransferAttachmentActionConfig } from '../../shared-functions';

const logger = Logger.getLogger(__filename, 'transfer-attachment');
export class TransferAttachmentAction extends GraphqlAction<TransferAttachmentActionConfig> {
    override buildRequest(): Request {
        const { fromPath, fromTags, tagsToAddToPrinting, toPath, failIfNotFound } = this.resolveConfig();
        // Note: fromTags and tagsToAddToPrinting contains the natural keys of the tags

        const fromNodeName = this.getVariableDefinition(fromPath).node || '';
        const fromId = this.getVariableValue(fromPath);
        const toNodeName = this.getVariableDefinition(toPath).node || '';
        const toId = this.getVariableValue(toPath);

        if (fromNodeName === 'UploadedFile') {
            // Attach an uploadedFile to a node
            logger.info(`Attaching uploadedFile ${fromId} to node ${toNodeName}/${toId}`);
            return {
                operationType: 'mutation',
                packageName: 'xtremUpload',
                nodeName: 'uploadedFile',
                operationName: 'attachToNode',
                args: `(
                        sourceId: "${fromId}",
                        to: { nodeName: "${toNodeName}", nodeId: "${toId}" },
                        tagsToAddToPrintingNaturalKeys: ${JSON.stringify(tagsToAddToPrinting || [])},
                        failIfNotFound: ${failIfNotFound},
                    )`,
                selector: '',
                variables: [],
                outputPath: '',
            };
        }

        // Copy attachments from a node to another node
        logger.info(`Copying attachments from ${fromNodeName}/${fromId} to ${toNodeName}/${toId}`);
        return {
            operationType: 'mutation',
            packageName: 'xtremUpload',
            nodeName: 'uploadedFile',
            operationName: 'copyAttachments',
            args: `(
                        from: { nodeName: "${fromNodeName}", nodeId: "${fromId}" },
                        fromTagsNaturalKeys: ${JSON.stringify(fromTags || [])},
                        to: { nodeName: "${toNodeName}", nodeId: "${toId}" },
                        failIfNotFound: ${failIfNotFound},
                    )`,
            selector: '',
            variables: [],
            outputPath: '',
        };
    }

    override async control(cx: ValidationContext): Promise<void> {
        await super.control(cx);
        const fromDef = this.getVariableDefinition(this.rawConfig.fromPath);

        const checkNodeWithAttachments = (
            nodeName: string,
            formatError: (localizedNodeName: string) => string,
        ): void => {
            const node = this.application.getFactoryByName(nodeName);
            if (!node.hasAttachments) {
                const localizedNodeName = cx.localize(node.getLocalizedTitleKey(), '');
                cx.error.add(formatError(localizedNodeName));
            }
        };

        // The 'from' can be either an uploadedFile or a node with attachments
        if (fromDef.type === 'IntReference' && fromDef.node === 'UploadedFile') {
            // An uploadedFile is a valid 'from'
        } else {
            // Ensure that the 'from' is a node with attachments
            const fromNodeName = fromDef.node;
            if (fromNodeName == null) {
                cx.error.add(cx.localize('@sage/xtrem-upload/workflow-transfer-no-from-path', "'from' is missing"));
            } else {
                // The 'from' must refer to a node with attachments
                checkNodeWithAttachments(fromNodeName, localizedNodeName =>
                    cx.localize(
                        '@sage/xtrem-upload/workflow-transfer-from-without-attachments',
                        "The 'from' instance '{{nodeName}}' doesn't have attachments",
                        { nodeName: localizedNodeName },
                    ),
                );
            }
        }
        const toNodeName = this.getVariableDefinition(this.rawConfig.toPath).node;
        if (toNodeName == null) {
            cx.error.add(cx.localize('@sage/xtrem-upload/workflow-transfer-no-to-path', "'to' is missing"));
        } else {
            // The 'to' must refer to a node with attachments
            checkNodeWithAttachments(toNodeName, localizedNodeName =>
                cx.localize(
                    '@sage/xtrem-upload/workflow-transfer-to-without-attachments',
                    "The 'to' instance '{{nodeName}}' doesn't have attachments",
                    { nodeName: localizedNodeName },
                ),
            );
        }
    }

    static override readonly descriptor = {
        type: 'action',
        key: 'transfer-attachment',
        title: '',
        description: '',
        ui: {
            icon: 'connected',
            color: '#335b70ff',
            configurationPage: '@sage/xtrem-upload/WorkflowActionTransferAttachment',
        },
    } as WorkflowStepDescriptor<TransferAttachmentActionConfig>;
}
