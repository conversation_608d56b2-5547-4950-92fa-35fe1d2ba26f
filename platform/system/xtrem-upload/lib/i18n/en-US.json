{"@sage/xtrem-upload/activity__attachment__name": "Attachment", "@sage/xtrem-upload/attachment_is_protected_delete": "You cannot delete a protected attachment.", "@sage/xtrem-upload/attachment_is_protected_update": "You cannot update a protected attachment.", "@sage/xtrem-upload/data_types__upload_status_enum__name": "Upload status enum", "@sage/xtrem-upload/data_types__uploaded_file_kind_enum__name": "Uploaded file kind enum", "@sage/xtrem-upload/enums__upload_status__created": "Created", "@sage/xtrem-upload/enums__upload_status__rejected": "Rejected", "@sage/xtrem-upload/enums__upload_status__uploaded": "Uploaded", "@sage/xtrem-upload/enums__upload_status__uploadFailed": "Upload failed", "@sage/xtrem-upload/enums__upload_status__verified": "Verified", "@sage/xtrem-upload/enums__uploaded_file_kind__attachment": "Attachment", "@sage/xtrem-upload/enums__uploaded_file_kind__upload": "Upload", "@sage/xtrem-upload/file-attachment-title-error": "File attachment error.", "@sage/xtrem-upload/file-attachment-title-success": "File attachment success.", "@sage/xtrem-upload/nodes__attachement__source-attachment-not-found": "Source attachment not found.", "@sage/xtrem-upload/nodes__attachment__attachment_mime_type_not_authorized": "The file type is not authorized.", "@sage/xtrem-upload/nodes__attachment_association__node_name": "Attachment association", "@sage/xtrem-upload/nodes__attachment_association__property__attachment": "Attachment", "@sage/xtrem-upload/nodes__attachment_association__property__description": "Description", "@sage/xtrem-upload/nodes__attachment_association__property__isProtected": "Is protected", "@sage/xtrem-upload/nodes__attachment_association__property__sourceNodeId": "Source node ID", "@sage/xtrem-upload/nodes__attachment_association__property__sourceNodeName": "Source node name", "@sage/xtrem-upload/nodes__attachment_association__property__title": "Title", "@sage/xtrem-upload/nodes__uploaded_file__asyncMutation__purge": "Purge", "@sage/xtrem-upload/nodes__uploaded_file__asyncMutation__purge__failed": "Purge failed.", "@sage/xtrem-upload/nodes__uploaded_file__asyncMutation__purge__parameter__duration": "Duration", "@sage/xtrem-upload/nodes__uploaded_file__asyncMutation__purge__parameter__unit": "Unit", "@sage/xtrem-upload/nodes__uploaded_file__max_file_size": "The file size ({{val}}) exceeds the maximum length allowed ({{maxUploadSize}} bytes).", "@sage/xtrem-upload/nodes__uploaded_file__mutation__attachToNode": "Attach to node", "@sage/xtrem-upload/nodes__uploaded_file__mutation__attachToNode__failed": "Attach to node failed.", "@sage/xtrem-upload/nodes__uploaded_file__mutation__attachToNode__parameter__failIfNotFound": "Fail if not found", "@sage/xtrem-upload/nodes__uploaded_file__mutation__attachToNode__parameter__sourceId": "Source ID", "@sage/xtrem-upload/nodes__uploaded_file__mutation__attachToNode__parameter__tagsToAddToPrintingNaturalKeys": "Tags to add to printing natural keys", "@sage/xtrem-upload/nodes__uploaded_file__mutation__attachToNode__parameter__to": "To", "@sage/xtrem-upload/nodes__uploaded_file__mutation__copyAttachments": "Copy attachments", "@sage/xtrem-upload/nodes__uploaded_file__mutation__copyAttachments__failed": "Copy attachments failed.", "@sage/xtrem-upload/nodes__uploaded_file__mutation__copyAttachments__parameter__failIfNotFound": "Fail if not found", "@sage/xtrem-upload/nodes__uploaded_file__mutation__copyAttachments__parameter__from": "From", "@sage/xtrem-upload/nodes__uploaded_file__mutation__copyAttachments__parameter__fromTagsNaturalKeys": "From tags natural keys", "@sage/xtrem-upload/nodes__uploaded_file__mutation__copyAttachments__parameter__to": "To", "@sage/xtrem-upload/nodes__uploaded_file__mutation__create": "Create", "@sage/xtrem-upload/nodes__uploaded_file__mutation__create__failed": "Create failed.", "@sage/xtrem-upload/nodes__uploaded_file__mutation__create__parameter__data": "Data", "@sage/xtrem-upload/nodes__uploaded_file__mutation__transfer": "Transfer", "@sage/xtrem-upload/nodes__uploaded_file__mutation__transfer__parameter__failIfNotFound": "Fail if not found", "@sage/xtrem-upload/nodes__uploaded_file__mutation__transfer__parameter__from": "From", "@sage/xtrem-upload/nodes__uploaded_file__mutation__transfer__parameter__fromTagsNaturalKeys": "From tags natural keys", "@sage/xtrem-upload/nodes__uploaded_file__mutation__transfer__parameter__to": "To", "@sage/xtrem-upload/nodes__uploaded_file__mutation__update": "Update", "@sage/xtrem-upload/nodes__uploaded_file__mutation__update__failed": "Update failed.", "@sage/xtrem-upload/nodes__uploaded_file__mutation__update__parameter__data": "Data", "@sage/xtrem-upload/nodes__uploaded_file__node_name": "Uploaded file", "@sage/xtrem-upload/nodes__uploaded_file__property__canSkipAntivirusScan": "Can skip antivirus scan", "@sage/xtrem-upload/nodes__uploaded_file__property__contentLength": "Content length", "@sage/xtrem-upload/nodes__uploaded_file__property__downloadUrl": "Download URL", "@sage/xtrem-upload/nodes__uploaded_file__property__expirationDate": "Expiration date", "@sage/xtrem-upload/nodes__uploaded_file__property__filename": "File name", "@sage/xtrem-upload/nodes__uploaded_file__property__fullKeyForContent": "Full key for content", "@sage/xtrem-upload/nodes__uploaded_file__property__key": "Key", "@sage/xtrem-upload/nodes__uploaded_file__property__kind": "Kind", "@sage/xtrem-upload/nodes__uploaded_file__property__lastDownloadDate": "Last download date", "@sage/xtrem-upload/nodes__uploaded_file__property__lastModified": "Last modified", "@sage/xtrem-upload/nodes__uploaded_file__property__mimeType": "Mime type", "@sage/xtrem-upload/nodes__uploaded_file__property__orphanedDate": "Orphaned date", "@sage/xtrem-upload/nodes__uploaded_file__property__owner": "Owner", "@sage/xtrem-upload/nodes__uploaded_file__property__rejectReason": "Reject reason", "@sage/xtrem-upload/nodes__uploaded_file__property__replyTopic": "Reply topic", "@sage/xtrem-upload/nodes__uploaded_file__property__status": "Status", "@sage/xtrem-upload/nodes__uploaded_file__property__uploadUrl": "Upload URL", "@sage/xtrem-upload/nodes__uploaded_file__status-unchanged": "The status cannot change from '{{from}}' to '{{to}}'.", "@sage/xtrem-upload/nodes__uploaded-file__bad-status": "The expected status is '{{status}}'.", "@sage/xtrem-upload/notification-action-title-attachment-download-url": "Attachment download URL", "@sage/xtrem-upload/package__name": "Upload", "@sage/xtrem-upload/pages__workflow_action_transfer_attachment____subtitle": "Share attachment", "@sage/xtrem-upload/pages__workflow_action_transfer_attachment____title": "Action configuration", "@sage/xtrem-upload/pages__workflow_action_transfer_attachment__failIfNotFound____title": "Fail if no attachments found", "@sage/xtrem-upload/pages__workflow_action_transfer_attachment__fromPath____title": "From", "@sage/xtrem-upload/pages__workflow_action_transfer_attachment__fromTags____helperText": "Only attachments with the selected tags are shared. Leave blank to share all attachments.", "@sage/xtrem-upload/pages__workflow_action_transfer_attachment__fromTags____lookupDialogTitle": "Select tags", "@sage/xtrem-upload/pages__workflow_action_transfer_attachment__fromTags____placeholder": "Select tags", "@sage/xtrem-upload/pages__workflow_action_transfer_attachment__fromTags____title": "Tags", "@sage/xtrem-upload/pages__workflow_action_transfer_attachment__mainSection____title": "General", "@sage/xtrem-upload/pages__workflow_action_transfer_attachment__outputVariableName____title": "Output variable name", "@sage/xtrem-upload/pages__workflow_action_transfer_attachment__tagsToAddToPrinting____helperText": "The selected tags are added to the print.", "@sage/xtrem-upload/pages__workflow_action_transfer_attachment__tagsToAddToPrinting____lookupDialogTitle": "Select tags", "@sage/xtrem-upload/pages__workflow_action_transfer_attachment__tagsToAddToPrinting____placeholder": "Select tags", "@sage/xtrem-upload/pages__workflow_action_transfer_attachment__tagsToAddToPrinting____title": "Tags to attach to the print.", "@sage/xtrem-upload/pages__workflow_action_transfer_attachment__title____helperText": "Title displayed in the workflow diagram.", "@sage/xtrem-upload/pages__workflow_action_transfer_attachment__title____title": "Action title", "@sage/xtrem-upload/pages__workflow_action_transfer_attachment__toPath____title": "To", "@sage/xtrem-upload/permission__manage__name": "Manage", "@sage/xtrem-upload/permission__read__name": "Read", "@sage/xtrem-upload/upload-finish-status-error": "The attachment {{fileName}} did not pass the security check.", "@sage/xtrem-upload/upload-finish-status-success": "File {{fileName}} uploaded successfully.", "@sage/xtrem-upload/user-not-allowed-to-upload": "You cannot upload a file.", "@sage/xtrem-upload/workflow-transfer-from-without-attachments": "The 'from' instance '{{nodeName}}' doesn't have attachments", "@sage/xtrem-upload/workflow-transfer-no-from-path": "'from' is missing", "@sage/xtrem-upload/workflow-transfer-no-to-path": "'to' is missing", "@sage/xtrem-upload/workflow-transfer-to-without-attachments": "The 'to' instance '{{nodeName}}' doesn't have attachments"}