import { CustomSqlAction } from '@sage/xtrem-system';

export const attachmentsToUploadedFile = new CustomSqlAction({
    description: 'Move the existing attachments to the uploaded_file table',
    fixes: {
        notNullableColumns: [
            {
                table: 'uploaded_file',
                column: 'kind',
            },
            {
                table: 'attachment_association',
                column: 'attachment',
            },
        ],
    },
    body: async helper => {
        await helper.executeSql(`
DO $$
DECLARE
	rec RECORD;
	newId  BIGINT;
	fixedCount BIGINT;
	totalAssociationCount BIGINT;
	totalAttachmentCount BIGINT;
	remainingCount BIGINT;
	associationCountToProcess BIGINT;
	attachmentCountToProcess BIGINT;

BEGIN
	SELECT count(*) FROM ${helper.schemaName}.attachment_association INTO associationCountToProcess;
	SELECT count(*) FROM ${helper.schemaName}.attachment INTO attachmentCountToProcess;
	RAISE WARNING 'Fixing % attachements and % associations', attachmentCountToProcess, associationCountToProcess;

    totalAssociationCount = 0;
    totalAttachmentCount = 0;
    -- First: fix the existing record in the uploaded_file table to 'upload'
    UPDATE ${helper.schemaName}.uploaded_file SET kind='upload' WHERE TRUE;

    -- Now, copy the attachments to the uploaded_file table
    FOR rec IN SELECT * FROM ${helper.schemaName}.attachment ORDER BY _tenant_id, _id
    LOOP
	    INSERT INTO ${helper.schemaName}.uploaded_file (
            _tenant_id,
            kind,
            key,
            filename,
            mime_type,
            last_modified,
            last_download_date,
            orphaned_date,
            content_length,
            status,
            reject_reason,
            can_skip_antivirus_scan,
            owner,
            reply_topic,
            _create_user,
            _update_user,
            _create_stamp,
            _update_stamp,
            _update_tick,
            _source_id
        )
        VALUES (
            rec._tenant_id,
            'attachment',
            rec.key,
            rec.filename,
            rec.mime_type,
            rec.last_modified,
            rec.last_download_date,
            rec.orphaned_date,
            rec.content_length,
            rec.status,
            rec.reject_reason,
            false,
            '',
            '',
            rec._create_user,
            rec._update_user,
            rec._create_stamp,
            rec._update_stamp,
            rec._update_tick,
            rec._source_id
        ) RETURNING _id INTO newId;

        RAISE WARNING '  %: attachment % cloned (new id=%)', rec._tenant_id, rec._id, newId;

	    -- fix attachment_associations
	    UPDATE ${helper.schemaName}.attachment_association SET attachment = newId WHERE attachment = rec._id  AND _tenant_id = rec._tenant_id;
	    GET DIAGNOSTICS fixedCount = ROW_COUNT;
	    totalAssociationCount = totalAssociationCount + fixedCount;
	    IF fixedCount > 0 THEN
	        RAISE WARNING '    % association(s) fixed', fixedCount;
	    END IF;

	    -- now delete the attachment
	    DELETE FROM ${helper.schemaName}.attachment WHERE _id = rec._id AND _tenant_id = rec._tenant_id;
	    totalAttachmentCount = totalAttachmentCount + 1;
    END LOOP;
    RAISE WARNING 'Fixed % attachments, % associations', totalAttachmentCount, totalAssociationCount;
	IF (attachmentCountToProcess <> totalAttachmentCount) THEN
		RAISE EXCEPTION 'Something went wrong, some attachments were not processed (expected %, got %)', attachmentCountToProcess, totalAttachmentCount;
	END IF;
	IF (associationCountToProcess <> totalAssociationCount) THEN
		RAISE EXCEPTION 'Something went wrong, some associations were not processed (expected %, got %)', associationCountToProcess, totalAssociationCount;
	END IF;
END; $$`);
    },
});
