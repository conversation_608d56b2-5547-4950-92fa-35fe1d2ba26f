import { asyncArray } from '@sage/xtrem-async-helper';
import { getTextForLocale, LocalizedText, mergeLocalizedText, titleCase, WorkflowVariable } from '@sage/xtrem-shared';
import { SysTag } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import { GraphApi } from '@sage/xtrem-upload-api';
import { WorkflowConfigPageData } from '@sage/xtrem-workflow';
import { WorkflowDefinition as WorkflowDefinitionApi } from '@sage/xtrem-workflow-api';
import {
    getInputData,
    NodeDetailsFetcher,
    resolveSysTagsFromNaturalKeys,
    variableIsReferenceToNodeWithAttachments,
    variableIsReferenceToUploadedFile,
} from '@sage/xtrem-workflow/build/lib/client-functions';
import { omit } from 'lodash';

import {
    getDynamicSelectProperties,
    getStepVariablesForSerialization,
    setOptionsForDynamicSelect,
    tidyVariables,
    transformVariablesFromSerialization,
    validateDynamicVariablesPickers,
} from '@sage/xtrem-workflow/lib/client-functions/variable-utils';

interface WorkflowVariableEx extends WorkflowVariable {
    /**
     * Returns whether the variable is a reference to a node with attachments
     */
    hasAttachments?: boolean;
    /**
     * Returns whether the variable is an uploaded file
     */
    isUploadedFile?: boolean;
}

@ui.decorators.page<WorkflowActionTransferAttachment, WorkflowDefinitionApi>({
    title: 'Action configuration',
    subtitle: 'Share attachment',
    mode: 'tabs',
    businessActions() {
        return [this.$standardDialogConfirmationAction];
    },
    async onLoad() {
        await this._onLoad();
    },
})
export class WorkflowActionTransferAttachment extends ui.Page<GraphApi> {
    private currentVariables: WorkflowVariableEx[] = [];

    private oldRootPaths: string[];

    private _nodeDetailsFetcher = new NodeDetailsFetcher();

    @ui.decorators.section<WorkflowActionTransferAttachment>({
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<WorkflowActionTransferAttachment>({
        parent() {
            return this.mainSection;
        },
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.textField<WorkflowActionTransferAttachment>({
        title: 'Action title',
        helperText: 'Title displayed in the workflow diagram.',
        isFullWidth: true,
        isTransient: true,
        parent() {
            return this.mainBlock;
        },
    })
    title: ui.fields.Text;

    // Note: localizedTitle is a JSON object that contains the translations (something like { 'en-US': 'Hello', 'fr-FR': 'Bonjour' })
    // title is only editing the translation for the current locale
    @ui.decorators.technicalJsonField<WorkflowActionTransferAttachment>({})
    localizedTitle: ui.fields.TechnicalJson<LocalizedText>;

    @ui.decorators.textField<WorkflowActionTransferAttachment>({})
    subtitle: ui.fields.Text;

    @ui.decorators.technicalJsonField<WorkflowActionTransferAttachment>({})
    stepVariables: ui.fields.TechnicalJson<WorkflowVariable[]>;

    @ui.decorators.dynamicSelectField<WorkflowActionTransferAttachment>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
        isMandatory: true,
        title: 'From',
        ...getDynamicSelectProperties<WorkflowActionTransferAttachment, WorkflowVariableEx>({
            mode: 'select',
            getVariables: page => page.currentVariables,
            selectableVariableFilter: (_page, variable) =>
                WorkflowActionTransferAttachment._getVariablesFilterForFromPath(variable),
            getOldRootPaths: page => page.oldRootPaths,
            updateCurrentVariables: (page, variables) => page._updateCurrentVariables(variables),
        }),
    })
    fromPath: ui.fields.DynamicSelect;

    @ui.decorators.multiReferenceField<WorkflowActionTransferAttachment, SysTag>({
        node: '@sage/xtrem-system/SysTag',
        title: 'Tags',
        helperText: 'Only attachments with the selected tags are shared. Leave blank to share all attachments.',
        placeholder: 'Select tags',
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select tags',
        valueField: 'name',
        isFullWidth: true,
        helperTextField: 'description',
        isHidden() {
            return !this.tagsFiltersVisible();
        },
        columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'description' })],
        parent() {
            return this.mainBlock;
        },
    })
    fromTags: ui.fields.MultiReference<SysTag>;

    @ui.decorators.checkboxField<WorkflowActionTransferAttachment>({
        parent() {
            return this.mainBlock;
        },
        title: 'Fail if no attachments found',
        isFullWidth: true,
        isHidden() {
            return !this.tagsFiltersVisible();
        },
    })
    failIfNotFound: ui.fields.Checkbox;

    @ui.decorators.dynamicSelectField<WorkflowActionTransferAttachment>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
        isMandatory: true,
        title: 'To',
        ...getDynamicSelectProperties<WorkflowActionTransferAttachment, WorkflowVariableEx>({
            mode: 'select',
            getVariables: page => page.currentVariables,
            selectableVariableFilter: (_page, variable) =>
                WorkflowActionTransferAttachment._getVariablesFilterForToPath(variable),
            getOldRootPaths: page => page.oldRootPaths,
            updateCurrentVariables: (page, variables) => page._updateCurrentVariables(variables),
        }),
    })
    toPath: ui.fields.DynamicSelect;

    @ui.decorators.multiReferenceField<WorkflowActionTransferAttachment, SysTag>({
        node: '@sage/xtrem-system/SysTag',
        title: 'Tags to attach to the print.',
        helperText: 'The selected tags are added to the print.',
        placeholder: 'Select tags',
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select tags',
        valueField: 'name',
        isFullWidth: true,
        helperTextField: 'description',
        isHidden() {
            return !this.tagsToAddToPrintingVisible();
        },
        columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'description' })],
        parent() {
            return this.mainBlock;
        },
    })
    tagsToAddToPrinting: ui.fields.MultiReference<SysTag>;

    @ui.decorators.textField<WorkflowActionTransferAttachment>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
        isMandatory: true,
        title: 'Output variable name',
    })
    outputVariableName: ui.fields.Text;

    private async _onLoad(): Promise<void> {
        if (!this.outputVariableName.value) {
            this.outputVariableName.value = 'sharedAttachment';
        }
        const { oldRootPaths } = getInputData(this);
        this.oldRootPaths = oldRootPaths;
        this.stepVariables.value = transformVariablesFromSerialization(this.stepVariables.value ?? []);
        await this._updateCurrentVariables([]);
        this._setOptionsForDynamicSelects();

        // Here, this.fromTags.value contains an array with the natural keys of the tags
        // We have to resolve the keys to the actual tags
        this.fromTags.value = await resolveSysTagsFromNaturalKeys(this, this.fromTags.value as string[]);
        // Same for tagsToAddToPrinting
        this.tagsToAddToPrinting.value = await resolveSysTagsFromNaturalKeys(
            this,
            this.tagsToAddToPrinting.value as string[],
        );

        const locale = this.$.locale;
        this.title.value = getTextForLocale(this.localizedTitle.value ?? {}, locale);

        await this._validateFields();
    }

    /**
     * Validate the fields of the page.
     */
    private async _validateFields(): Promise<void> {
        await validateDynamicVariablesPickers([this.fromPath, this.toPath]);
    }

    /**
     * Set the options for the dynamic select fields.
     */
    private _setOptionsForDynamicSelects(): void {
        setOptionsForDynamicSelect(
            this.fromPath,
            this.currentVariables.filter(WorkflowActionTransferAttachment._getVariablesFilterForFromPath),
        );
        setOptionsForDynamicSelect(
            this.toPath,
            this.currentVariables.filter(WorkflowActionTransferAttachment._getVariablesFilterForToPath),
        );
    }

    private async _updateCurrentVariables(variables: WorkflowVariable[]): Promise<void> {
        const { inputVariables } = getInputData(this);
        this.currentVariables = tidyVariables([...inputVariables, ...this.stepVariables.value, ...variables]);

        await asyncArray(this.currentVariables).forEach(async variable => {
            variable.hasAttachments = await variableIsReferenceToNodeWithAttachments(
                variable,
                this._nodeDetailsFetcher,
            );
            variable.isUploadedFile = variableIsReferenceToUploadedFile(variable);
        });

        this._setOptionsForDynamicSelects();
    }

    /**
     * Returns the paths of the variables used by the action.
     */
    private _getUsedVariablePaths(): string[] {
        if (this.fromPath.value == null) throw new Error('fromPath is not set');
        if (this.toPath.value == null) throw new Error('toPath is not set');
        return [this.fromPath.value, this.toPath.value];
    }

    getSerializedValues(): WorkflowConfigPageData<any> {
        const outputPath = this.outputVariableName.value || 'sharedAttachments';
        const stepVariables = [
            ...getStepVariablesForSerialization(this.currentVariables, this._getUsedVariablePaths()),
            {
                path: outputPath,
                title: titleCase(outputPath),
                type: 'Int',
            },
        ];

        const locale = this.$.locale;
        const localizedTitle = mergeLocalizedText(this.localizedTitle.value ?? {}, this.title.value ?? '', locale);

        // the tags are stored as natural keys
        const fromTags = this.fromTags.value.map(tag => `#${tag.name}`);

        // the tags are stored as natural keys
        const tagsToAddToPrinting = this.tagsToAddToPrinting.value.map(tag => `#${tag.name}`);

        return {
            ...omit(this.$.values, ['title']),
            stepVariables,
            fromTags,
            tagsToAddToPrinting,
            localizedTitle,
        };
    }

    /**
     * Should the filters based on tags be visible?
     * @returns
     */
    private tagsFiltersVisible(): boolean {
        const variable = this.currentVariables.find(v => v.path === this.fromPath.value);
        return !!variable?.hasAttachments;
    }

    /**
     * Should the fields to select the tags to add to the printing be visible?
     * @returns
     */
    private tagsToAddToPrintingVisible(): boolean {
        const variable = this.currentVariables.find(v => v.path === this.fromPath.value);
        return !!variable?.isUploadedFile;
    }

    /**
     * The filter to apply to the variables list for the "From" path
     */
    private static _getVariablesFilterForFromPath(variable: WorkflowVariableEx): boolean {
        return !!variable.hasAttachments || !!variable.isUploadedFile;
    }

    /**
     * The filter to apply to the variables list for the "To" path
     */
    private static _getVariablesFilterForToPath(variable: WorkflowVariableEx): boolean {
        return !!variable.hasAttachments;
    }
}
