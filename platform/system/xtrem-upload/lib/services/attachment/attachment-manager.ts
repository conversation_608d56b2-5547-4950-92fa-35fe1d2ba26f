import { AttachmentManager, StaticThis } from '@sage/xtrem-core';
import * as xtremUpload from '../../index';

export class UploadAttachmentManager implements AttachmentManager {
    private constructor() {
        // enforce singleton
    }

    // eslint-disable-next-line class-methods-use-this
    getAttachmentNode(): StaticThis<xtremUpload.nodes.AttachmentAssociation> {
        return xtremUpload.nodes.AttachmentAssociation;
    }

    /** @internal */
    static readonly instance = new UploadAttachmentManager();
}
