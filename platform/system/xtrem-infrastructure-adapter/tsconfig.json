{"extends": "../../tsconfig-base.json", "compilerOptions": {"outDir": "build", "rootDir": ".", "baseUrl": "."}, "include": ["index.ts", "application.ts", "lib/**/*", "lib-generated/**/*", "api/api.d.ts", "test/**/*.ts"], "exclude": ["lib/pages/**/*", "lib/widgets/**/*", "lib/page-extensions/**/*", "lib/page-fragments/**/*", "lib/stickers/**/*", "lib/i18n/**/*", "**/*.feature", "**/*.png", "lib/client-functions/**/*"], "references": [{"path": "../../back-end/xtrem-core"}, {"path": "../../shared/xtrem-shared"}, {"path": "../../back-end/eslint-plugin-xtrem"}, {"path": "../../back-end/xtrem-minify"}]}