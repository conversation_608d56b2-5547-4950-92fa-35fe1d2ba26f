/* eslint-disable class-methods-use-this */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable max-classes-per-file */
import { Application, getTestFixtures, initTables, Test } from '@sage/xtrem-core';
import * as path from 'path';
import { install as sourceMapsSetup } from 'source-map-support';

sourceMapsSetup();

export async function setup(): Promise<void> {
    const fixtures = getTestFixtures();
    fixtures.updateContext();
    await Application.createDbSchema('xtrem_infrastructure_adapter_test');
    const application = await Test.createTestApplication({
        api: fixtures,
        buildDir: path.join(__dirname, '..'),
    });
    await Test.setup(application);
    await initTables([]);
}
