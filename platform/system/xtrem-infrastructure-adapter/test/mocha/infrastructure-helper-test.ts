import { ASYNC_CONTEXT_CONST, AsyncContext } from '@sage/async-context-provider';
import { ConfigManager, Context, Test } from '@sage/xtrem-core';
import { FileTimeToLive, TenantDataType } from '@sage/xtrem-file-storage';
import { assert } from 'chai';
import * as fs from 'fs';
import { nanoid } from 'nanoid';
import * as fsp from 'path';
import { InfrastructureHelper } from '../../lib';
import { setup } from '../fixtures/setup';

const contextId = nanoid();

async function getAsyncContext(context: Context): Promise<AsyncContext | undefined> {
    let asyncContext = await InfrastructureHelper.getAsyncContext(contextId);
    if (!context) {
        await InfrastructureHelper.createAsyncContext(
            context,
            'CONTEXT_FILE_UPLOAD',
            { replyTopic: 'myTopic' },
            { contextId, s3FileTTL: FileTimeToLive.Expire10Days },
        );
        asyncContext = await InfrastructureHelper.getAsyncContext(contextId);
    }

    return asyncContext;
}

/**
 * Clean up files created by tests
 */
function cleanLocalFiles(context: Context): void {
    const dirToDelete = fsp.join(context.application.tmpDir, context.tenantId as string);
    fs.rmSync(dirToDelete, { recursive: true });
}

describe('Infrastructure Helper', () => {
    before(async () => {
        await setup();
        Test.patchConfig({ clusterId: 'DummyCluster' });
    });

    it('can create, get and delete async context', () =>
        Test.withContext(async context => {
            await InfrastructureHelper.createAsyncContext(
                context,
                'CONTEXT_FILE_UPLOAD',
                { replyTopic: 'myTopic' },
                { contextId, s3FileTTL: FileTimeToLive.Expire10Days },
            );
            let asyncContext = await InfrastructureHelper.getAsyncContext(contextId);
            assert.isDefined(asyncContext);
            assert.equal(asyncContext?.contextKind, ASYNC_CONTEXT_CONST.CONTEXT_FILE_UPLOAD);
            assert.deepEqual(asyncContext?.contextXtrem, {
                replyTopic: 'myTopic',
                user: (await context.user)?._id,
            });
            await InfrastructureHelper.deleteAsyncContext(contextId);
            asyncContext = await InfrastructureHelper.getAsyncContext(contextId);
            assert.isUndefined(asyncContext);
        }));

    it('can get async upload URL', () =>
        Test.withContext(async context => {
            await getAsyncContext(context);

            const S3Url = await InfrastructureHelper.getUploadPresignedUrl(context, contextId);

            assert.isDefined(S3Url);
            assert.isTrue(
                S3Url.endsWith(`tenant-data/${context.tenantId}/uploads/${contextId}`),
                `${S3Url} does not end with tenant-data/${context.tenantId}/uploads/${contextId}`,
            );
        }));

    it('can get async download URL', () =>
        Test.withContext(async context => {
            await getAsyncContext(context);

            const S3Url = await InfrastructureHelper.getDownloadPresignedUrl(context, `report-output/${contextId}`);

            assert.isDefined(S3Url);
            assert.isTrue(
                S3Url.endsWith(`tenant-data/${context.tenantId}/uploads/report-output/${contextId}`),
                `${S3Url} does not end with tenant-data/${context.tenantId}/uploads/report-output/${contextId}`,
            );
        }));

    it('can create a file on S3', () =>
        Test.withContext(async context => {
            await getAsyncContext(context);

            const file = await InfrastructureHelper.createFile(
                context,
                `report-output/${contextId}`,
                'Test data',
                Buffer.from('Test', 'base64'),
                FileTimeToLive.Expire1Day,
            );

            assert.isDefined(file);
            assert.equal(file.ETag, 'dummyEtag');
        }));

    it('can read an upload result file from S3', () =>
        Test.withContext(async context => {
            await getAsyncContext(context);

            await InfrastructureHelper.createFile(
                context,
                `uploads/${contextId}`,
                'Test data',
                Buffer.from('Test'),
                FileTimeToLive.Expire1Day,
            );

            // /777777777777777777777/uploads/${contextId}
            const file = await InfrastructureHelper.readAsyncUploadResult(context, contextId, 'CONTEXT_FILE_UPLOAD');

            assert.isDefined(file);
            assert.deepEqual(file?.tagData, {
                TagSet: [
                    { Key: 'description', Value: 'Test data' },
                    { Key: 'expiration', Value: 'expire1day' },
                ],
            });

            const result = await file?.body?.transformToString();
            assert.equal(result, 'Test');
        }));

    it('can read a file on S3', () =>
        Test.withContext(async context => {
            await getAsyncContext(context);
            const objectKey = `report-output/${contextId}`;
            await InfrastructureHelper.createFile(
                context,
                objectKey,
                'Test data',
                Buffer.from('Test'),
                FileTimeToLive.Expire1Day,
            );

            const file = await InfrastructureHelper.readFile(context, objectKey);

            assert.isDefined(file);
            assert.deepEqual(file?.tagData, {
                TagSet: [
                    { Key: 'description', Value: 'Test data' },
                    { Key: 'expiration', Value: 'expire1day' },
                ],
            });

            const result = await file?.body?.transformToString();
            assert.equal(result, 'Test');
        }));

    it('can read a file on S3', () =>
        Test.withContext(async context => {
            const list = await InfrastructureHelper.list(TenantDataType.DATA, context);
            assert.isTrue(list.Contents.length > 0);
            // Example:
            // {
            //     IsTruncated: false,
            //     Contents: [
            //       {
            //         Key: 'report-output',
            //         LastModified: '2020-06-08T08:52:12.000Z',
            //         ETag: 'dummyETag'
            //       },
            //       {
            //         Key: 'uploads',
            //         LastModified: '2020-06-08T08:52:12.000Z',
            //         ETag: 'dummyETag'
            //       }
            //     ],
            //     Prefix: '777777777777777777777',
            //     Name: '/home/<USER>/xtrem2/platform/system/xtrem-infrastructure-adapter',
            //     KeyCount: 2
            //   }
        }));

    it('can get list of tenant apps (local dev only)', async () => {
        const apps = await InfrastructureHelper.getTenantApps(Test.defaultTenantId);
        if (ConfigManager.current.apps) {
            assert.deepEqual(apps, ['sdmo', 'shopfloor', 'showcase_stock', 'showcase_sales', 'x3_connector']);
        } else {
            assert.equal(apps, null);
        }
    });

    after(async () => {
        await Test.withContext(context => cleanLocalFiles(context));
    });
});
