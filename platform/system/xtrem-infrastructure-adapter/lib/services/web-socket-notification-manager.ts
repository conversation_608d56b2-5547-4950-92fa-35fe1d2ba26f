import { ConfigManager, Context, Logger, Test, UiBroadcastMessage } from '@sage/xtrem-core';
import { Config, NotificationPayload } from '@sage/xtrem-shared';
import { MessageTarget, WebsocketAWSImpl } from '@sage/xtrem-websocket-emiter';
import { BroadcastMessageTarget } from '@sage/xtrem-websocket-emiter/out/business/websocketRepository';
import * as cookie from 'cookie';
import * as http from 'http';
import * as https from 'https';
import { jwtDecode } from 'jwt-decode';
import { Server, WebSocket } from 'ws';
import { WebSocketEmitterRepository } from './web-socket-emitter-repository';

const logger = Logger.getLogger(__filename, 'web-socket-notification');

export class WebSocketNotificationManager {
    private static readonly webSocketNotificationClients: Map<string, WebSocket> = new Map();

    private static webSocketServer: Server<typeof WebSocket>;

    static identifyDevelopmentUser(req: Request): string {
        const config = ConfigManager.current;

        const cookies = (req as any).headers?.cookie as string;
        if (cookies) {
            const parsedCookies = cookie.parse(cookies);
            if (parsedCookies.access_token) {
                const decodedToken = jwtDecode(parsedCookies.access_token);
                if (decodedToken?.sub) {
                    return decodedToken.sub as string; // Assuming 'sub' contains the user email
                }
            }
        }

        if (config.email) {
            return config.email;
        }

        return Test.defaultEmail;
    }

    static startDevelopmentService(httpServer: http.Server | https.Server): void {
        const config = ConfigManager.current;
        if (config.deploymentMode !== 'development') return;

        WebSocketNotificationManager.webSocketServer = new WebSocket.Server({ noServer: true });

        WebSocketNotificationManager.webSocketServer.on('connection', (ws: WebSocket, req: Request) => {
            const clientEmail = WebSocketNotificationManager.identifyDevelopmentUser(req);
            const webSocketKey = (req as any).headers['sec-websocket-key'] as string; // Or any other unique client identifier
            const clientId = `${clientEmail}-${webSocketKey}`;

            WebSocketNotificationManager.addWebSocketNotificationClient(ws as any, clientId);

            ws.on('message', (message: { toString: () => any }) => {
                logger.info(`Received message from client ${clientId}: ${message.toString()}`);
            });

            ws.on('close', () => {
                logger.info(`Client ${clientId} disconnected`);
                WebSocketNotificationManager.deleteWebSocketNotificationClient(clientId);
            });

            ws.send(`Connection established with ${clientId}`);
        });

        // Custom middleware to handle WebSocket connections for the "websocket." subdomain.
        httpServer.on('upgrade', (request, socket, head) => {
            const host = request.headers.host;
            if (host?.startsWith('websocket.')) {
                WebSocketNotificationManager.webSocketServer.handleUpgrade(request, socket, head, ws => {
                    WebSocketNotificationManager.webSocketServer.emit('connection', ws, request);
                });
            } else {
                socket.destroy();
            }
        });
    }

    private static addWebSocketNotificationClient(ws: WebSocket, clientId: string): void {
        if (WebSocketNotificationManager.webSocketNotificationClients.get(clientId)) return;
        WebSocketNotificationManager.webSocketNotificationClients.set(clientId, ws);
    }

    private static deleteWebSocketNotificationClient(clientId: string): void {
        WebSocketNotificationManager.webSocketNotificationClients.delete(clientId);
    }

    private static getWebSocketNotificationClient(userEmail: string): WebSocket[] | undefined {
        const matchingClients: WebSocket[] = [];
        WebSocketNotificationManager.webSocketNotificationClients.forEach((value, key) => {
            if (key.startsWith(userEmail)) {
                matchingClients.push(value);
            }
        });
        return matchingClients;
    }

    public static getWebSocketNotificationClients(): WebSocket[] | undefined {
        return [...WebSocketNotificationManager.webSocketNotificationClients.values()];
    }

    static async sendUserNotification(
        context: Context,
        category: 'test' | 'userNotification' | 'tracking' | 'asyncMutationComplete',
        payload?: NotificationPayload,
    ): Promise<void> {
        const config = ConfigManager.current;
        // web socket notifications must be sent using the login user which is different from the persona user on demo tenants
        const user = await context.loginUser;
        const userEmail = user?.email;
        const userId = user?._id;
        if (!userEmail) {
            logger.error(`Cannot send notification: No valid user email found in context for user id ${userId}`);
            return;
        }
        const tenantId = context.tenantId;
        if (!tenantId) {
            logger.error(`Cannot send notification: No valid tenantId found in context for user id ${userId}`);
            return;
        }
        const notification = {
            category,
            payload,
        };

        if (config.deploymentMode === 'development') {
            if (!WebSocketNotificationManager.webSocketServer && !context.testMode) {
                logger.error('No WebServer started. Please run WebSocketNotificationManager.startDevelopmentService');
                return;
            }
            const clientWebSockets = WebSocketNotificationManager.getWebSocketNotificationClient(userEmail);
            if (clientWebSockets?.length === 0) {
                logger.warn(`No websocket clients connected for user ${userId}.`);
            }
            clientWebSockets?.forEach(ws => {
                ws?.send(JSON.stringify(notification));
            });
            return;
        }
        if (config.deploymentMode === 'production') {
            const sendTo = {
                userId: userEmail,
                tenantId,
                sendToAllMatchingUserSessions: true,
            } as MessageTarget;
            const webSocketHelper = WebSocketNotificationManager.webSocketHelper(config);
            if (
                await webSocketHelper.isUserOfTenantConnected({
                    userId: userEmail,
                    tenantId,
                })
            ) {
                await webSocketHelper.sendUserNotification(sendTo, notification);
            } else {
                logger.warn(() => `Notification not sent: user ${userId} not connected`);
            }
        }
    }

    /**
     * Broadcast a message to all users
     */
    static async broadcast(payload: UiBroadcastMessage): Promise<void> {
        const tenantId = payload.tenantId;
        if (!tenantId) {
            logger.error(`Cannot broadcast message: No valid tenantId found.`);
            return;
        }

        const config = ConfigManager.current;

        if (config.deploymentMode === 'development') {
            if (!WebSocketNotificationManager.webSocketServer) {
                logger.error('No WebServer started. Please run WebSocketBroadcastManager.startService');
                return;
            }
            const clientWebSockets = WebSocketNotificationManager.getWebSocketNotificationClients();
            if (clientWebSockets?.length === 0) {
                logger.warn(`No websocket clients connected for broadcast.`);
            }

            const developmentConfig = {
                dynamoDBTableName: '',
                clusterId: config.clusterId ?? '',
                app: ConfigManager.current.app ?? 'sdmo',
                websocketSource: '',
                websocketQueueUrl: '',
                awsConfigOverride: { region: process.env.AWS_REGION || 'eu-west-1' },
            };

            const webSocketHelper = new WebSocketEmitterRepository(developmentConfig);
            const sendTo: BroadcastMessageTarget = { tenantId };

            await webSocketHelper.broadcastMessage(sendTo, payload);

            return;
        }

        if (config.deploymentMode === 'production') {
            const clientNotificationsWebSocketConfig = config.webSocket?.clientNotifications;
            if (!clientNotificationsWebSocketConfig) {
                logger.error('Web socket request config is missing for clientNotifications');
                return;
            }

            try {
                const webSocketHelper = WebSocketNotificationManager.webSocketHelper(config);
                const sendTo: BroadcastMessageTarget = { tenantId };

                await webSocketHelper.broadcastMessage(sendTo, payload);
            } catch (error) {
                logger.error(`Broadcasting message error: ${error.message}`);
            }
        }
    }

    private static webSocketHelper(config: Config): WebsocketAWSImpl.WebsocketAWSRepository {
        const clientNotificationsWebSocketConfig = config.webSocket?.clientNotifications;
        if (!clientNotificationsWebSocketConfig)
            throw new Error(`Web socket request config is missing for clientNotifications`);

        // If any config is not provided we will an invalid value and the connection should throw
        return new WebsocketAWSImpl.WebsocketAWSRepository({
            app: ConfigManager.current.app ?? 'sdmo',
            source: clientNotificationsWebSocketConfig?.request?.source,
            clusterId: config.clusterId ?? '',
            dynamoDBTableName: clientNotificationsWebSocketConfig?.dynamoDbTableName ?? '',
            websocketQueueUrl: clientNotificationsWebSocketConfig?.request?.websocketQueueUrl ?? '',
            awsConfigOverride: { region: process.env.AWS_REGION || 'eu-west-1' },
        });
    }
}
