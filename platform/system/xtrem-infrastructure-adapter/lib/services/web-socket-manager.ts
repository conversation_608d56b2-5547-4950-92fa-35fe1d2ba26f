import {
    AnyValue,
    Application,
    async<PERSON>ireAndForget,
    ConfigManager,
    CoreHooks,
    Di<PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    SystemError,
    with<PERSON><PERSON><PERSON>,
} from '@sage/xtrem-core';
import { WebSocketConfig } from '@sage/xtrem-shared';
import { WebsocketAWSImpl } from '@sage/xtrem-websocket-emiter';
import { nanoid } from 'nanoid';
import { WebSocket } from 'ws';

const axios = Mocker.get('axios', require);

export interface RestResponseInfo {
    uploadUrl: string;
    confirmUrl: string;
}

const logger = Logger.getLogger(__filename, 'web-socket');

export interface WebSocketRequestData {
    tenantId: string;
    developerId: string;
    request: string;
    locale: string;
    type: string;
    source: string;
    replyId: string;
    replyTopic: string;
    attributes: Dict<AnyValue>;
}

// TODO: XT-42850 remove the exclusion of this file from the c8 entry in package.json when websockets are integrated
export class WebSocketManager {
    private static readonly wsClients: Map<string, WebSocket> = new Map();

    /**
     * Start web socket client for the specified config key
     * @param application
     * @param key
     */
    static start(application: Application, key: string): void {
        if (this.wsClients.has(key)) {
            logger.error(`Websocket client already started : ${key}`);
            return;
        }
        const config = ConfigManager.current.webSocket && ConfigManager.current.webSocket[key];
        if (!config) {
            logger.error(`Web socket config is missing for ${key}`);
            return;
        }
        WebSocketManager.createWebSocketClient(application, key, config);
    }

    /**
     * Fetch the upload url and confirm url for the specified contextId
     * @param restResponseEndpoint
     * @param contextId
     * @returns
     */
    private static getUploadUrl(restResponseEndpoint: string, contextId: string): Promise<RestResponseInfo> {
        logger.info(`Fetching an upload url for contextid : ${contextId}`);
        return withRethrow(
            async () => {
                const urlResponse = (await axios.post(`${restResponseEndpoint}${contextId}`)) as any;
                if (urlResponse?.status === 201) {
                    logger.info(`Upload url response : ${JSON.stringify(urlResponse.data)}`);

                    return {
                        uploadUrl: urlResponse.data.uploadUrl,
                        confirmUrl: urlResponse.data.confirmUrl,
                    };
                }
                throw new Error(`API to get presigned url returned with a ${urlResponse.status} status`);
            },
            error => {
                logger.error(`Error while fetching upload URL: \n${error.message}`);
                return new SystemError('Error while fetching upload URL', error);
            },
        );
    }

    /**
     * send the response message to the upload URL and confirm the upload with a post to the confirm URL
     * @param restResponseEndpoint
     * @param contextId
     * @param response
     */
    private static async sendResponsePayload(
        restResponseEndpoint: string,
        contextId: string,
        response: string,
    ): Promise<void> {
        // get url of S3
        const uploadsUrls = await this.getUploadUrl(restResponseEndpoint, contextId);

        // upload to s3
        await withRethrow(
            async () => {
                const axiosResponse = (await axios.put(uploadsUrls.uploadUrl, response)) as any;

                if (axiosResponse.status !== 200) {
                    logger.error(`upload to s3 failed: ${axiosResponse.status}`);
                }
            },
            err => {
                logger.error(`upload to s3 failed: ${err.message}`);
                return new SystemError(`upload to s3 failed: uploadUrl=${uploadsUrls.uploadUrl}`, err);
            },
        );
        logger.info('upload to s3 done');

        // then notify upload has been done.

        await withRethrow(
            async () => {
                const axiosResponseUploadDone = (await axios.post(uploadsUrls.confirmUrl)) as any;
                if (axiosResponseUploadDone.status !== 200) {
                    logger.error(`Fail to confirm upload done : ${axiosResponseUploadDone.status}`);
                }
            },
            err => {
                logger.error(`Fail to confirm upload done: ${err.message}`);
                return new SystemError(`Fail to confirm upload done: confirmUrl=${uploadsUrls.confirmUrl}`, err);
            },
        );

        logger.info(`Confirmation of upload completed, end of processing ${contextId}`);
    }

    private static createWebSocketClient(application: Application, key: string, config: WebSocketConfig): void {
        if (!config.response) {
            logger.error(`Web socket response config is missing for ${key}`);
            return;
        }

        const client = new WebSocket(config.response.wsEndPoint, {
            headers: {
                Authorization: config.response.authToken,
            },
            perMessageDeflate: false,
        });

        client.on('open', () => {
            logger.info(`Connected to ${key} web socket endpoint`);
        });

        client.on('close', () => {
            logger.info(`Server sent close event, reconnecting to ${key}`);
            WebSocketManager.createWebSocketClient(application, key, config);
        });

        client.on('error', () => {
            logger.info(`Server sent error event, reconnecting to ${key}`);
            setTimeout(() => WebSocketManager.createWebSocketClient(application, key, config), 5000); // auto reconnect after 5 seconds in case of error
        });

        const processMessage = async (data: any): Promise<void> => {
            logger.info(`received(${key}): ${data}`);
            let parsedMessage: any = null;

            parsedMessage = JSON.parse(data.toString());
            const requiredAttributes = [
                'contextId',
                'tenantId',
                'developerId',
                'source',
                'type',
                'request',
                'replyId',
                'replyTopic',
            ];
            const missingAttributes = requiredAttributes.filter(attributeName => !parsedMessage[attributeName]);
            if (missingAttributes.length > 0)
                throw new Error(`Message missing, ${missingAttributes} : ${parsedMessage}`);

            logger.info(
                `Message format valid, contextId : ${parsedMessage.contextId}, tenantId : ${parsedMessage.tenantId}`,
            );

            // Get the response to the request using the CoreHooks
            // There should be a handler registered for the specified key
            // The key could be reporting, mrp, recruitment, etc. WAIT - AWAIT
            if (!CoreHooks.webSocketHandlers[key]) throw new Error(`Web socket handler missing for key : ${key}`);
            const response = JSON.stringify(
                await CoreHooks.webSocketHandlers[key].getResponse(application, parsedMessage),
            );
            logger.info(`response = ${response}`);
            if (config.response) {
                await WebSocketManager.sendResponsePayload(
                    config.response.restResponseEndpoint,
                    parsedMessage.contextId,
                    response,
                );
            } else {
                logger.error(`Web socket response config is missing for ${key}`);
            }
        };

        client.on('message', (data: any) =>
            asyncFireAndForget(() => processMessage(data), 'Error sending response message', logger),
        );

        this.wsClients.set(key, client);
    }

    private static getRegion(): string {
        return process.env.AWS_REGION || 'eu-west-1';
    }

    /**
     * Send a message to a web socket
     */
    static async send(
        key: string,
        data: WebSocketRequestData,
        tenantId: string,
        applicationId: string,
        options?: { force?: boolean },
    ): Promise<string> {
        logger.info(`Websocket - sending message on key ${key}`);
        const config = ConfigManager.current;
        const webSocketConfig = config.webSocket && config.webSocket[key];
        if (!webSocketConfig?.request) {
            throw new Error(`Web socket request config is missing for ${key}`);
        }

        logger.info(
            `Searching if app ${applicationId} is connected for tenant ${tenantId}  (cluster context ${config.clusterId})`,
        );

        // If any config is not provided we will an invalid value and the connection should throw
        const websocketHelper = new WebsocketAWSImpl.WebsocketAWSRepository({
            app: ConfigManager.current.app || '',
            source: webSocketConfig.request.source,
            clusterId: config.clusterId || '',
            dynamoDBTableName: config.asyncContextTableName || '',
            websocketQueueUrl: webSocketConfig.request.websocketQueueUrl,
            awsConfigOverride: { region: this.getRegion() },
        });

        // TODO: XT-26109 In the next itteration we will use the async context API to create a context
        // See https://confluence.sage.com/display/XTREEM/Async+Context+Library

        const contextId = nanoid(); // Important used for tracking of the request, may be rename to controlId later

        const payload = { ...data, contextId };
        if (
            await websocketHelper.isThirdPartyAppConnected({
                tenantId,
                applicationId,
            })
        ) {
            logger.info(`3rd party app connected, message : ${JSON.stringify(payload)}`);
            await websocketHelper.sendThirdPartyAppNotification(
                {
                    applicationId,
                    tenantId,
                },
                payload,
            );
            logger.info(`Message sent: ${contextId}`);
            return contextId;
        }

        logger.error('The 3rd party app is not connected via websocket at the moment');
        if (options?.force) {
            logger.warn(`Forcing message sending to user anyway : ${JSON.stringify(payload)}`);
            await websocketHelper.sendThirdPartyAppNotification(
                {
                    applicationId,
                    tenantId,
                },
                payload,
            );
            logger.warn(`Message sent, forced, not sure it will be received: ${contextId}`);
            return contextId;
        }

        throw new Error('Web socket request not sent, 3rd party app not connected.');
    }

    static startService(application: Application): void {
        const webSockets = application.startOptions.webSockets || [];
        if (webSockets.length > 0) {
            logger.info(`Starting web sockets: ${webSockets}`);
            webSockets.forEach(webSocket => {
                WebSocketManager.start(application, webSocket);
            });
        }
    }
}
