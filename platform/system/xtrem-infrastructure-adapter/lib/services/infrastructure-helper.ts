import {
    ASYNC_CONTEXT_CONST,
    AsyncContext,
    AsyncContextBuilder,
    AsyncContextXtremRepository,
} from '@sage/async-context-provider';
import { ConfigManager, Context, Logger } from '@sage/xtrem-core';
import {
    FileStorageHandler,
    FileTimeToLive,
    S3BodyGet,
    S3BodyPut,
    TenantDataType,
    getFileStorageHandler,
} from '@sage/xtrem-file-storage';
import { TenantAppHelper } from '@sage/xtrem-infra';
import { findUp } from 'find-up';
import path = require('path');

export type BaseContextValueType = { replyTopic: string };

export interface FileList {
    IsTruncated: boolean;
    Contents: {
        Key: string;
        LastModified: string;
        ETag: string;
    }[];
    Prefix: string;
    Name: string;
    KeyCount: number;
}

export class InfrastructureHelper {
    private static logger = new Logger(__filename, 'infrastructure');

    static getLogger(): Logger {
        if (this.logger) return this.logger;
        this.logger = new Logger(__filename, 'infrastructure');
        return this.logger;
    }

    /**
     * The test async context object has a Map to cache the contexts, so we need to store it in a static for unit tests
     */
    private static testAsyncContextXtremRepository: AsyncContextXtremRepository;

    /**
     * @returns
     */
    private static getAsyncContextXtremRepository(): AsyncContextXtremRepository {
        const config = ConfigManager.current;
        if (config.deploymentMode === 'development') {
            if (!this.testAsyncContextXtremRepository)
                this.testAsyncContextXtremRepository = AsyncContextBuilder.getLocalDevImplementation();
            return this.testAsyncContextXtremRepository;
        }

        if (!config.asyncContextTableName) throw new Error('Missing config: asyncContextTableName.');

        return AsyncContextBuilder.getCloudImplementation({
            dynamoDBTableName: config.asyncContextTableName,
            awsConfigOverride: {
                region: config.aws?.region || process.env.AWS_REGION || 'eu-west-1',
            },
        });
    }

    private static getTenantId(context: Context): string {
        if (!context.tenantId) throw new Error('Tenant ID is missing from context.');
        return context.tenantId;
    }

    private static getClusterId(): string {
        const config = ConfigManager.current;
        if (config.clusterId) return config.clusterId;
        return config.deploymentMode === 'development' ? 'development-cluster' : config.clusterId || '';
    }

    /**
     * Create an async context
     * @param context
     * @param contextKind
     * @param contextValue
     * @param options
     * @returns nanoId - contextId
     */
    static async createAsyncContext<ContextValueT extends BaseContextValueType>(
        context: Context,
        contextKind: keyof typeof ASYNC_CONTEXT_CONST,
        contextValue: ContextValueT,
        options: {
            contextId?: string;
            expireDatetimeIso?: string;
            responseQueueName?: string;
            mimeType?: string | string[];
            s3FileTTL: FileTimeToLive;
        },
    ): Promise<string> {
        const asyncContextXtremRepository = this.getAsyncContextXtremRepository();

        return asyncContextXtremRepository.createAsyncContext({
            cluster: this.getClusterId(),
            app: ConfigManager.current.app,
            tenantId: this.getTenantId(context),
            // We should use the context kind provided by in the API in ASYNC_CONTEXT_CONST, if we need more kinds we
            // request them.
            contextKind: ASYNC_CONTEXT_CONST[contextKind],
            contextXtrem: { ...contextValue, user: (await context.user)?._id },
            // Default is 15 min
            expireDatetimeIso: options?.expireDatetimeIso,
            // Do we need a message in the infrastructure queue for timeOut, set to true
            notifyTimeout: true,
            // If the contextId is not passed the API will set the contextId to a nanoId and resolve the Promise to this value
            nanoId: options?.contextId,
            // If we want the response on the another queue, not the default infrastructure queue
            responseQueueName: options?.responseQueueName,
            s3FileTTL: options.s3FileTTL,
            // pass mimeType to the context
            contextInfra: {
                mimeType: options?.mimeType,
            },
        });
    }

    /**
     * Get an async context for a specified tenant and contextId
     * @param context
     * @param contextId
     * @returns
     */
    static getAsyncContext(contextId: string): Promise<AsyncContext | undefined> {
        const asyncContextXtremRepository = this.getAsyncContextXtremRepository();
        return asyncContextXtremRepository.getAsyncContext(contextId);
    }

    /**
     * delete an async context
     * @param context
     * @param contextId
     * @returns
     */
    static deleteAsyncContext(contextId: string): Promise<void> {
        const asyncContextXtremRepository = this.getAsyncContextXtremRepository();
        return asyncContextXtremRepository.deleteAsyncContext(contextId);
    }

    /**
     * Get a file storage handler to perform actions on tenant specific S3 buckets
     * @returns
     */
    private static getFileStorageHandler(context: Context): FileStorageHandler {
        const logger = this.getLogger();
        const config = ConfigManager.current;
        if (config?.s3Storage?.localBasePath && ConfigManager.current.deploymentMode !== 'development') {
            throw Error('localBasePath is allowed only in development mode');
        }
        const fileStorageHandler = getFileStorageHandler({
            bucket: config.s3Storage?.s3ClusterBucket,
            app: config.app,
            maxListFiles: 1000,
            awsConfigOverride: {
                region: config.aws?.region || process.env.AWS_REGION || 'eu-west-1',
            },
            logger: logger.info.bind(logger),
            localBasePath: config?.s3Storage?.localBasePath || path.join(context.application.tmpDir),
        });
        if (context.testMode) fileStorageHandler.setUnitTestMode(true);
        return fileStorageHandler;
    }

    private static isPureDevelopmentConfig(context: Context): boolean {
        return ConfigManager.current.deploymentMode === 'development' && !context.testMode;
    }

    private static getDevUrl(type: string, objectKey: string, origin: string, filename?: string): string {
        const logger = this.getLogger();
        const config = ConfigManager.current;
        const key = Buffer.from(JSON.stringify({ objectKey })).toString('base64');
        const host = config.security?.redirectUrl || `http://localhost:${config.server?.port || '8240'}`;
        let url = `${host}/dev/${type}/${key}`;
        if (filename) {
            url = `${url}?filename=${encodeURIComponent(filename)}`;
        }
        logger.info(`[Dev] upload url ${origin} for '${objectKey}': ${url}`);
        return url;
    }

    /**
     * Usually before you want to generate a nanoid and use it will async context, see InfrastructureHelper.createAsyncContext
     * returns a presigned url that can be used to upload the attachment.
     * location would be : s3://bucket-of-cluster/tenant-data/{app}/{tenant-id}/attachments/{async-context-id}
     * @param context
     * @param objectKey
     * @param ttlSeconds
     * @returns
     */
    static generateAttachmentDownloadPresignedUrl(
        context: Context,
        objectKey: string,
        urlTimeToLiveInSec?: number,
        filename?: string,
    ): Promise<string> {
        const fileStorageHandler = this.getFileStorageHandler(context);

        if (this.isPureDevelopmentConfig(context)) {
            return Promise.resolve(
                this.getDevUrl('attachments', objectKey, 'AttachmentDownloadPresignedUrl', filename),
            );
        }
        return fileStorageHandler.generateAttachmentDownloadPresignedUrl(
            this.getTenantId(context),
            objectKey,
            urlTimeToLiveInSec,
            filename,
        );
    }

    /**
     * Copy a file from a place to another one
     * @param context The context
     * @param sourceObjectKey the objectKey of the file to copy
     * @param targetObjectKey the target objectKey of the new file
     * @param targetTimeToLive the time to live of the new file
     */
    static async copyFileToFolder(
        context: Context,
        sourceObjectKey: string,
        targetObjectKey: string,
        targetTimeToLive: FileTimeToLive,
    ): Promise<void> {
        const fileStorageHandler = this.getFileStorageHandler(context);
        await fileStorageHandler.copyFileToFolder(
            this.getTenantId(context),
            sourceObjectKey,
            targetObjectKey,
            targetTimeToLive,
        );
    }

    /**
     * This will return a url signed that points to : s3://bucket-of-cluster/tenant-data/{app}/{tenant-id}/attachments/{objectKey}
     * @param objectKey
     * @param ttlSeconds
     * @returns
     */
    static generateAttachmentUploadPresignedUrl(
        context: Context,
        objectKey: string,
        fileStorageSetting?: { ttlSeconds?: number },
    ): Promise<string> {
        const fileStorageHandler = this.getFileStorageHandler(context);

        if (this.isPureDevelopmentConfig(context)) {
            return Promise.resolve(this.getDevUrl('attachments', objectKey, 'AttachmentUploadPresignedUrl'));
        }
        return fileStorageHandler.generateAttachmentUploadPresignedUrl(
            this.getTenantId(context),
            objectKey,
            fileStorageSetting?.ttlSeconds,
        );
    }

    /**
     * Usually before you want to generate a nanoid and use it will async context, see InfrastructureHelper.createAsyncContext
     * returns a presigned url that can be used to upload the file.
     * location would be : s3://cluster-bucket/dirty/tenants-data/{tenant-id}/uploads/{async-context-id}
     * @param context
     * @param objectKey
     * @param ttlSeconds
     * @returns
     */
    static getUploadPresignedUrl(context: Context, objectKey: string, urlTimeToLiveInSec?: number): Promise<string> {
        const fileStorageHandler = this.getFileStorageHandler(context);

        if (this.isPureDevelopmentConfig(context)) {
            return Promise.resolve(this.getDevUrl('uploads', objectKey, 'UploadPresignedUrl'));
        }
        return fileStorageHandler.generateUploadPresignedUrl(this.getTenantId(context), objectKey, urlTimeToLiveInSec);
    }

    /**
     * This will return a url signed that points to : s3://cluster-bucket/tenants-data/{tenant-id}/{objectKey}
     * @param objectKey
     * @param ttlSeconds
     * @returns
     */
    static getDownloadPresignedUrl(
        context: Context,
        objectKey: string,
        fileStorageSetting?: { ttlSeconds?: number; filename?: string },
    ): Promise<string> {
        const fileStorageHandler = this.getFileStorageHandler(context);

        if (this.isPureDevelopmentConfig(context)) {
            return Promise.resolve(
                this.getDevUrl('uploads', objectKey, 'DownloadPresignedUrl', fileStorageSetting?.filename),
            );
        }
        return fileStorageHandler.generateDownloadPresignedUrl(
            this.getTenantId(context),
            objectKey,
            fileStorageSetting?.ttlSeconds,
            fileStorageSetting?.filename,
        );
    }

    /**
     * Save under /cluster-bucket/tenant-data/tenantid/...
     * Description should be a tag
     * Expiration should be a tag (we will manage the cleanup later)
     *
     * e.g. objectKey: '/report-output/myReport.pdf'
     * @param context
     * @param objectKey
     * @param description
     * @param data
     * @param fileTimeToLive
     * @returns
     */
    static createFile(
        context: Context,
        objectKey: string,
        description: string,
        data: S3BodyPut,
        fileTimeToLive: FileTimeToLive,
    ): Promise<{ ETag: string }> {
        const fileStorageHandler = this.getFileStorageHandler(context);

        return fileStorageHandler.createFile(this.getTenantId(context), objectKey, description, data, fileTimeToLive);
    }

    /**
     * Read result of async upload for the specified contextId
     * This is after virus scan is complete and the file is moved out of the dirty location to the upload location.
     * @param context
     * @param contextId
     * @param contextKind The contextKind determines the folder we read the file from
     * @returns
     */
    static async readAsyncUploadResult(
        context: Context,
        contextId: string,
        contextKind: keyof typeof ASYNC_CONTEXT_CONST,
    ): Promise<
        | {
              tagData: any;
              body: S3BodyGet;
          }
        | undefined
    > {
        const fileStorageHandler = this.getFileStorageHandler(context);

        const asyncResult = await fileStorageHandler.readAsyncContextResult(
            this.getTenantId(context),
            contextId,
            ASYNC_CONTEXT_CONST[contextKind],
        );

        return asyncResult ?? undefined;
    }

    /**
     * Read a file from the tenants folder on the clusters S3 bucket
     * /cluster-bucket/tenant-data/tenantid/objectKey
     * e.g. objectKey: '/uploads/myFile.txt'
     * @param context
     * @param objectKey
     * @returns
     */
    static async readFile(
        context: Context,
        objectKey: string,
    ): Promise<
        | {
              tagData: any;
              body: S3BodyGet;
          }
        | undefined
    > {
        const fileStorageHandler = this.getFileStorageHandler(context);

        const readResult = await fileStorageHandler.readFile(this.getTenantId(context), objectKey);

        return readResult ?? undefined;
    }

    /**
     * List the file for a given tenant on a given clusters bucket
     * Handle the max file(1000) with a next token (continuationToken)
     * @param context
     * @param continuationToken  to use only if you have more than 1000 values and get the following ones
     * @returns
     */
    static list(tenantDataType: TenantDataType, context: Context, continuationToken?: string): Promise<FileList> {
        const fileStorageHandler = this.getFileStorageHandler(context);

        return fileStorageHandler.list(tenantDataType, this.getTenantId(context), continuationToken);
    }

    static async getTenantApps(tenantId: string): Promise<string[] | null> {
        const { clusterId } = ConfigManager.current;
        const tenantAppHelper = await InfrastructureHelper.getTenantAppHelper();

        return tenantAppHelper
            ? tenantAppHelper.getAppsOfTenant({ tenantId, cluster: clusterId })
            : Promise.resolve(null);
    }

    static async getTenantAppHelper(): Promise<TenantAppHelper | null> {
        let tenantAppHelper: TenantAppHelper;
        const { aws, xtremDeploymentCoreTableName, clusterId } = ConfigManager.current;
        if (aws && clusterId && xtremDeploymentCoreTableName) {
            tenantAppHelper = TenantAppHelper.getCloudImplementation(aws, xtremDeploymentCoreTableName, clusterId);
        } else if (ConfigManager.current.deploymentMode === 'development') {
            const appsYmlPath = await findUp('apps.yml', { cwd: __dirname });
            if (appsYmlPath) {
                tenantAppHelper = TenantAppHelper.getLocalDeveloperImplementation(appsYmlPath);
            } else {
                this.logger.warn(`apps.yml file not found, cannot get tenant apps. Lookup started at ${__dirname}`);
                return Promise.resolve(null);
            }
        } else {
            this.logger.warn('Incomplete configuration, cannot get tenant apps.');
            return Promise.resolve(null);
        }
        return Promise.resolve(tenantAppHelper ?? null);
    }

    static async getEmailDomainOfTenant(tenantId: string): Promise<string | null> {
        const tenantAppHelper = await InfrastructureHelper.getTenantAppHelper();
        if (tenantAppHelper) {
            return Promise.resolve((await tenantAppHelper.getEmailDomainOfTenant({ tenantId })) ?? null);
        }
        return Promise.resolve(null);
    }
}
