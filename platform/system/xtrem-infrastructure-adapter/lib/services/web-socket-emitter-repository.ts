import { WebsocketAWSRepositoryConfig, WebsocketRepository } from '@sage/xtrem-websocket-emiter';
import {
    BroadcastMessageTarget,
    ConnectedThirdPartyAppCriteria,
    ConnectedUserCriteria,
    MessageTarget,
    MessageThirdPartyAppTarget,
} from '@sage/xtrem-websocket-emiter/out/business/websocketRepository';
import { WebSocketNotificationManager } from './web-socket-notification-manager';

const defaultSourceValue = 'xtrem';
const maxMessageSizeInBytes = 1024 * 128;
/** *
 * Local mode implementation for xtrem-websocket-emitter WebsocketAWSRepository
 * https://github.com/Sage-ERP-X3/xtrem-deployment/blob/master/npm_modules/xtrem-websocket-emiter/src/business/impl/websocketAWSRepository.ts
 * This is done in order to mimic the usage of the library locally
 */
export class WebSocketEmitterRepository extends WebsocketRepository {
    private readonly config: WebsocketAWSRepositoryConfig;

    constructor(config: WebsocketAWSRepositoryConfig) {
        super();
        this.config = config;

        if (!this.config.source) {
            this.config.source = defaultSourceValue;
        }
    }

    // eslint-disable-next-line class-methods-use-this
    async broadcastMessage(sendTo: BroadcastMessageTarget, payload: any): Promise<void> {
        const messageContentStringified = JSON.stringify(payload);
        const messageSize = Buffer.byteLength(Buffer.from(messageContentStringified, 'utf-8'));
        if (messageSize > maxMessageSizeInBytes) {
            throw new Error(
                'Cannot send the notification message, the payload size is too important (max payload is 128 KB)',
            );
        }

        try {
            const clientWebSockets = WebSocketNotificationManager.getWebSocketNotificationClients();

            clientWebSockets?.forEach(ws => {
                ws?.send(JSON.stringify(payload));
            });

            await Promise.resolve();
        } catch (err) {
            const msg = { message: '', stack: '' };
            if (err instanceof Error) {
                msg.message = err.message;
                msg.stack = err.stack || '';
            }
            throw new Error(
                `Failed to broadcast message for tenant : ${sendTo.tenantId}. Err ${msg.stack !== '' ? msg.stack : msg.message}`,
            );
        }
    }

    // eslint-disable-next-line require-await, class-methods-use-this
    async isUserOfTenantConnected(
        connectedUserFilter: ConnectedUserCriteria,
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        _acrossAllApps = false,
    ): Promise<boolean> {
        throw new Error('Method not implemented.');
    }

    // eslint-disable-next-line require-await, class-methods-use-this, @typescript-eslint/no-unused-vars
    async sendUserNotification(_sendTo: MessageTarget, _payload: any): Promise<void> {
        throw new Error('Method not implemented.');
    }

    // eslint-disable-next-line require-await, class-methods-use-this
    async isThirdPartyAppConnected(
        connectedAppFilter: ConnectedThirdPartyAppCriteria,
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        _acrossAllApps = false,
    ): Promise<boolean> {
        throw new Error('Method not implemented.');
    }

    // eslint-disable-next-line require-await, class-methods-use-this, @typescript-eslint/no-unused-vars
    async sendThirdPartyAppNotification(_sendTo: MessageThirdPartyAppTarget, _payload: any): Promise<void> {
        throw new Error('Method not implemented.');
    }
}
