declare module '@sage/xtrem-integration-api-partial' {
    import type { Package as SageXtremSystem$Package, User } from '@sage/xtrem-system-api';
    import type {
        AggregateQueryOperation,
        AggregateReadOperation,
        ClientNode,
        ClientNodeInput,
        CreateOperation,
        DeleteOperation,
        GetDefaultsOperation,
        GetDuplicateOperation,
        Operation as Node$Operation,
        QueryOperation,
        ReadOperation,
        UpdateByIdOperation,
        UpdateOperation,
        integer,
    } from '@sage/xtrem-client';
    export interface Sage100De extends ClientNode {
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
    }
    export interface Sage100DeInput extends ClientNodeInput {
        isActive?: boolean | string;
    }
    export interface Sage100DeBinding extends ClientNode {
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
    }
    export interface Sage100De$Mutations {
        createOrGetDefaultRecord: Node$Operation<{}, integer>;
    }
    export interface Sage100De$Operations {
        query: QueryOperation<Sage100De>;
        read: ReadOperation<Sage100De>;
        aggregate: {
            read: AggregateReadOperation<Sage100De>;
            query: AggregateQueryOperation<Sage100De>;
        };
        create: CreateOperation<Sage100DeInput, Sage100De>;
        getDuplicate: GetDuplicateOperation<Sage100De>;
        update: UpdateOperation<Sage100DeInput, Sage100De>;
        updateById: UpdateByIdOperation<Sage100DeInput, Sage100De>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: Sage100De$Mutations;
        getDefaults: GetDefaultsOperation<Sage100De>;
    }
    export interface Package {
        '@sage/xtrem-integration/Sage100De': Sage100De$Operations;
    }
    export interface GraphApi extends Package, SageXtremSystem$Package {}
}
declare module '@sage/xtrem-integration-api' {
    export type * from '@sage/xtrem-integration-api-partial';
}
declare module '@sage/xtrem-system-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-integration-api';
    export interface GraphApi extends GraphApiExtension {}
}
