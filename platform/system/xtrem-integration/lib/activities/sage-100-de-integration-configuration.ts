import { Activity } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { Sage100De } from '../nodes/sage-100-de';

export const sage100DeIntegrationConfiguration = new Activity({
    description: 'Sage 100 DE integration configuration',
    node: () => Sage100De,
    __filename,
    permissions: ['manage'],
    operationGrants: {
        manage: [
            {
                operations: ['read', 'update', 'createOrGetDefaultRecord'],
                on: [() => Sage100De],
            },
            {
                operations: ['getTenantInformation'],
                on: [() => xtremSystem.nodes.SysTenant],
            },
        ],
    },
});
