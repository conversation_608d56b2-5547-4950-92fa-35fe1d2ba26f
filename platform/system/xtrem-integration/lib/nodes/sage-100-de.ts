import { Context, decorators, Node } from '@sage/xtrem-core';
import * as xtremIntegration from '../index';

@decorators.node<Sage100De>({
    package: 'xtrem-integration',
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    serviceOptions: () => [xtremIntegration.serviceOptions.sage100DeIntegrationServiceOption],
})
export class Sage100De extends Node {
    @decorators.booleanProperty<Sage100De, 'isActive'>({
        isStored: true,
        isPublished: true,
    })
    readonly isActive: Promise<boolean>;

    /**
     *  Create or returns the default configuration
     * @returns boolean
     */
    @decorators.mutation<typeof Sage100De, 'createOrGetDefaultRecord'>({
        isPublished: true,
        parameters: [],
        return: { type: 'integer' },
    })
    static async createOrGetDefaultRecord(context: Context): Promise<number> {
        const configurationByTenantArray = await context.query(xtremIntegration.nodes.Sage100De, {}).toArray();
        if (configurationByTenantArray.length === 0) {
            const configurationByTenant = await context.create(xtremIntegration.nodes.Sage100De, {
                isActive: false,
            });
            await configurationByTenant.$.save();
            return configurationByTenant._id;
        }
        return configurationByTenantArray[0]._id;
    }
}
