import { GraphApi, Sage100De as Sage100DeNode } from '@sage/xtrem-integration-api';
import * as ui from '@sage/xtrem-ui';
import { sage100De } from '../menu-items/sage-100-de';

@ui.decorators.page<Sage100De, Sage100DeNode>({
    title: 'Integration Sage 100 DE',
    menuItem: sage100De,
    navigationPanel: undefined,
    mode: 'tabs',
    module: 'xtrem-integration',
    node: '@sage/xtrem-integration/Sage100De',
    businessActions() {
        return [this.manage, this.rotate];
    },
    async defaultEntry() {
        const recordId = await this.$.graph
            .node('@sage/xtrem-integration/Sage100De')
            .mutations.createOrGetDefaultRecord(true, {})
            .execute();
        return recordId.toString();
    },
    async onLoad() {
        const tenantInformation = await this.getTenantInformation();
        this.tenantId.value = tenantInformation.tenantId;
        this.manageUi(this.isActive.value ?? false);
    },
})
export class Sage100De extends ui.Page<GraphApi, Sage100DeNode> {
    @ui.decorators.section<Sage100De>({
        title: 'API gateway information',
    })
    apiGateway: ui.containers.Section;

    @ui.decorators.block<Sage100De>({
        parent() {
            return this.apiGateway;
        },
        width: 'large',
    })
    switchBlock: ui.containers.Block;

    @ui.decorators.switchField<Sage100De>({
        parent() {
            return this.switchBlock;
        },
        title: 'Active',
        width: 'large',
        isReadOnly: true,
    })
    isActive: ui.fields.Switch;

    @ui.decorators.block<Sage100De>({
        parent() {
            return this.apiGateway;
        },
        title: 'API gateway information',
        width: 'large',
    })
    info: ui.containers.Block;

    @ui.decorators.textField<Sage100De>({
        parent() {
            return this.info;
        },
        title: 'Tenant ID',
        isReadOnly: true,
        isTransient: true,
    })
    tenantId: ui.fields.Text;

    @ui.decorators.separatorField<Sage100De>({
        parent() {
            return this.info;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    separatorFromTenantId: ui.fields.Separator;

    @ui.decorators.textField<Sage100De>({
        parent() {
            return this.info;
        },
        title: 'Client ID',
        isReadOnly: true,
        isTransient: true,
    })
    clientId: ui.fields.Text;

    @ui.decorators.textField<Sage100De>({
        parent() {
            return this.info;
        },
        title: 'Client secret',
        isPassword: true,
        isReadOnly: true,
        isTransient: true,
    })
    clientSecret: ui.fields.Text;

    @ui.decorators.pageAction<Sage100De>({
        buttonType: 'primary',
        onError(err) {
            this.$.showToast(
                ui.localize(
                    '@sage/xtrem-integration/sage_100_de_activation_failed',
                    'Sage 100 DE activation failed: {{errorMessage}}',
                    { errorMessage: err },
                ),
                { type: 'error' },
            );
        },
        async onClick() {
            this.isActive.value = !this.isActive.value;
            await this.$standardSaveAction.execute(true);
            this.manageUi(this.isActive.value);
            if (this.isActive.value) {
                // If activated, we keep the info block ready for extraction
                this.info.isHidden = false;
            }
        },
    })
    manage: ui.PageAction;

    @ui.decorators.pageAction<Sage100De>({
        title: 'Rotate pairing information',
        buttonType: 'primary',
        onError(err) {
            this.$.showToast(
                ui.localize(
                    '@sage/xtrem-integration/sage_100_de_pairing_rotation_failed',
                    'Sage 100 DE pairing rotation failed: {{errorMessage}}',
                    { errorMessage: err },
                ),
                { type: 'error' },
            );
        },
        async onClick() {
            // NYI: Implement the logic to rotate the pairing information, just unhide the info block now
            this.info.isHidden = false;
        },
    })
    rotate: ui.PageAction;

    async getTenantInformation(): Promise<{ tenantId: string }> {
        const tenantInformation = await this.$.graph
            .node('@sage/xtrem-system/SysTenant')
            .queries.getTenantInformation({ tenantId: true }, {})
            .execute();
        if (tenantInformation) {
            return tenantInformation;
        }
        return { tenantId: '' };
    }

    manageUi(isActive: boolean): void {
        if (isActive) {
            this.info.isHidden = true;
            this.manage.title = ui.localize(
                '@sage/xtrem-integration/sage_100_de_deactivation',
                'Deactivate Sage 100 DE pairing',
            );
            this.rotate.isHidden = false;
        } else {
            this.info.isHidden = false;
            this.manage.title = ui.localize(
                '@sage/xtrem-integration/sage_100_de_activation',
                'Activate Sage 100 DE pairing',
            );
            this.rotate.isHidden = true;
        }
    }
}
