{"name": "@sage/xtrem-integration", "description": "XTREM integration package for external products", "version": "58.0.2", "xtrem": {"isPlatform": true, "isService": true}, "keywords": ["external", "integration", "xtrem"], "author": "Sage", "license": "UNLICENSED", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/index.js", "files": ["build/*.*", "build/lib", "data"], "typings": "build/package-definition.d.ts", "dependencies": {"@sage/xtrem-core": "workspace:*", "@sage/xtrem-shared": "workspace:*", "@sage/xtrem-system": "workspace:*", "@sage/xtrem-ui": "workspace:*", "graphql": "16.1.0-experimental-stream-defer.6", "lodash": "^4.17.21", "semver": "^7.6.3"}, "devDependencies": {"@sage/xtrem-cli": "workspace:*", "@sage/xtrem-cli-bundle-dev": "workspace:*", "@types/lodash": "^4.14.198", "@types/semver": "^7.5.2", "@types/sinon": "^17.0.0", "eslint": "^8.49.0", "sinon": "^21.0.0"}, "scripts": {"build": "xtrem compile", "build:api": "xtrem build --only-api-client", "build:binary": "xtrem compile --binary --prod", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build", "initTenant": "xtrem tenant --init '********************************************************************************************************************************************************************************************************'", "lint": "xtrem lint", "load:test:data": "xtrem layers --load setup,test", "extract:test:data": "xtrem layers --extract test", "sonarqube:scanner": "pnpm test:ci && pnpm dlx sonarqube-scanner && rm -rf coverage .nyc_output .scannerwork junit-report-*.xml", "start": "xtrem start", "test": "xtrem test --unit --graphql --layers=test", "test:ci": "xtrem test --unit --ci --layers=test", "xtrem": "xtrem"}, "c8": {"cache": false, "all": true, "extension": [".ts", ".tsx"], "sourceMap": true, "instrument": true, "reporter": ["text-summary", "clover", "json", "lcov"], "include": ["lib/**/*.ts"], "exclude": ["test/**/*", "data/**/*"]}}