{"extends": "../../tsconfig-base.json", "compilerOptions": {"outDir": "build", "rootDir": ".", "baseUrl": "."}, "include": ["index.ts", "application.ts", "lib/**/*", "test/**/*.ts", "test/**/*.json", "api/api.d.ts"], "exclude": ["lib/pages/**/*", "lib/widgets/**/*", "lib/page-extensions/**/*", "lib/page-fragments/**/*", "lib/stickers/**/*", "lib/i18n/**/*", "**/*.feature", "**/*.png", "lib/client-functions/**/*", "test/fixtures/bundles/@sage/*/build/*", "test/fixtures/bundles/tmp/@sage/biodegradability-test/**/*"], "references": [{"path": "../../cli/xtrem-cli-layers"}, {"path": "../../front-end/xtrem-client"}, {"path": "../../back-end/xtrem-core"}, {"path": "../../back-end/xtrem-data-management"}, {"path": "../../shared/xtrem-date-time"}, {"path": "../../shared/xtrem-decimal"}, {"path": "../xtrem-infrastructure-adapter"}, {"path": "../../back-end/xtrem-log"}, {"path": "../../back-end/xtrem-postgres"}, {"path": "../../shared/xtrem-shared"}, {"path": "../../front-end/xtrem-ui"}, {"path": "../../back-end/eslint-plugin-xtrem"}, {"path": "../../cli/xtrem-cli"}, {"path": "../../back-end/xtrem-dts-bundle"}, {"path": "api"}]}