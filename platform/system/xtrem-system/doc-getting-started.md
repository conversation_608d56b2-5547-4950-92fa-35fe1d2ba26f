PATH: XTREEM/Development+documentation/Getting+Started+for+Applicative+Developers

** Important when transitioning from Etna to Xtree **: read the "Transitioning from Etna to Xtrem" article.

# Getting started with Xtrem Services development

This article describes the steps to setup a new Xtrem services development environment

## Cloning the repository

First step is to clone the xtrem-platform repository

```sh
git clone https://github.com/Sage-ERP-X3/xtrem-services.git
```

## Installing the npm packages

Then, install the npm packages

```sh
cd xtrem-platform
pnpm run clean:install
```

The install will install and compile the code.

## Compiling the code

The install step compiles all the code.
But you can also run a manual compile pass, to check that everything is ok before creating a PR

```sh
pnpm run build
```

The unit tests use `ts-node` and `ts-jest` so you can run them without compiling the code.

## Running the unit tests

To run all the unit tests, run:

```sh
pnpm test
```

You can also run this command into individual packages under `@sage`.

## Linting all the code

To lint all the files, run:

```sh
pnpm run lint
```
