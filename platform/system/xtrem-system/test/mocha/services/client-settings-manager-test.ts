import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import { clientSettingsManager } from '../../../lib/services/client-settings-manager';

describe('ClientSettingsManager tests', () => {
    it('should create a new client setting entry', async () => {
        await Test.withContext(async context => {
            const sysClientUserSettings = await clientSettingsManager.createClientSetting(
                context,
                'ScreenTestId',
                'ElementTestId',
                '{}',
                'Title',
                'Description',
            );

            assert.deepEqual(await sysClientUserSettings.elementId, 'ElementTestId');
            assert.deepEqual(await sysClientUserSettings.title, 'Title');
            assert.deepEqual(await sysClientUserSettings.description, 'Description');
        });
    });

    it('should select the last created setting entry', async () => {
        await Test.withContext(async context => {
            const screenId = 'TestView';
            const elementId = 'Name';

            const testSetting = await clientSettingsManager.createClientSetting(
                context,
                screenId,
                elementId,
                '{}',
                'Title 1',
                'Description 2',
            );

            assert.deepEqual(await testSetting.isSelected, true);

            await clientSettingsManager.createClientSetting(
                context,
                screenId,
                elementId,
                '{}',
                'Title 2',
                'Description 2',
            );

            const selectSettings = await clientSettingsManager.getVariantsForElement(context, screenId, elementId);

            assert.deepEqual(await selectSettings[0].isSelected, false);
            assert.deepEqual(await selectSettings[1].isSelected, true);
        });
    });

    it('should update a client setting entry', async () => {
        await Test.withContext(async context => {
            const sysClientUserSetting = await clientSettingsManager.createClientSetting(
                context,
                'ScreenTestId',
                'ElementTestId',
                '{}',
                'Title',
                'Description',
            );

            assert.deepEqual(await sysClientUserSetting.title, 'Title');
            assert.deepEqual(await sysClientUserSetting.description, 'Description');

            const updatedSysClientUserSetting = await clientSettingsManager.updateClientSetting(
                context,
                sysClientUserSetting._id.toString(),
                '{}',
                'Title 1',
                'Description 1',
            );

            assert.deepEqual(await updatedSysClientUserSetting.title, 'Title 1');
            assert.deepEqual(await updatedSysClientUserSetting.description, 'Description 1');
        });
    });

    it('should retrieve the specific settings', async () => {
        await Test.withContext(async context => {
            const screenId = 'ScreenViewId';
            const elementId = 'ElementViewId';

            const testSetting = await clientSettingsManager.createClientSetting(
                context,
                screenId,
                elementId,
                '{}',
                'Title 1',
                'Description 2',
            );

            const secondTestSetting = await clientSettingsManager.createClientSetting(
                context,
                screenId,
                elementId,
                '{}',
                'Title 2',
                'Description 2',
            );

            const selectSettings = await clientSettingsManager.getVariantsForElement(context, screenId, elementId);

            assert.deepEqual(selectSettings.length, 2);
            assert.deepEqual(await testSetting.title, await selectSettings[0].title);
            assert.deepEqual(await secondTestSetting.title, await selectSettings[1].title);
        });
    });

    it('should select the client setting and return it', async () => {
        await Test.withContext(async context => {
            const screenId = 'TestScreenId';
            const elementId = 'TestElementId';

            const testSetting = await clientSettingsManager.createClientSetting(
                context,
                screenId,
                elementId,
                '{}',
                'Title 1',
                'Description 1',
            );

            const secondTestSetting = await clientSettingsManager.createClientSetting(
                context,
                screenId,
                elementId,
                '{}',
                'Title 2',
                'Description 2',
            );

            assert.deepEqual(await secondTestSetting.isSelected, true);

            const selectSetting = await clientSettingsManager.selectClientSetting(
                context,
                screenId,
                elementId,
                testSetting._id.toString(),
            );

            assert.deepEqual(await selectSetting.title, 'Title 1');
            assert.deepEqual(await selectSetting.description, 'Description 1');
            assert.deepEqual(await selectSetting.isSelected, true);
        });
    });

    it('should delete the client setting and return true', async () => {
        await Test.withContext(async context => {
            const screenId = 'DeleteScreenId';
            const elementId = 'DeleteElementId';

            const testSetting = await clientSettingsManager.createClientSetting(
                context,
                screenId,
                elementId,
                '{}',
                'Delete Title 1',
                'Delete Description 1',
            );

            const selectSetting = await clientSettingsManager.deleteClientSetting(context, testSetting._id.toString());

            assert.deepEqual(selectSetting, true);
        });
    });
    it('should get active user setting from artifact Id', async () => {
        await Test.withContext(async context => {
            const screenId = 'ArtifactsId';
            const elementId = 'ArticfactsElementId';

            await clientSettingsManager.createClientSetting(
                context,
                screenId,
                elementId,
                '{}',
                'Artifact Title 1',
                'Artifact Description 1',
            );

            const artifactSettingsList = await clientSettingsManager.getActiveClientSettingsForArtifact(
                context,
                screenId,
            );

            assert.deepEqual(artifactSettingsList.length, 1);
            assert.deepEqual(await artifactSettingsList[0].title, 'Artifact Title 1');
        });
    });
    it('should not get active user setting from invlaid artifact Id', async () => {
        await Test.withContext(async context => {
            const screenId = 'ArtifactsId';

            const artifactSettingsList = await clientSettingsManager.getActiveClientSettingsForArtifact(
                context,
                screenId,
            );
            assert.deepEqual(artifactSettingsList.length, 0);
        });
    });
});
