import { assertIsRejectedWithDiagnoses, Context, Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import { serviceOptions } from '../../fixtures/lib/index';
import { showCaseOptionMutation } from '../../fixtures/lib/service-options/show-case-option-mutation';
import { graphqlSetup } from '../../fixtures/setup';

// Exclude the service options with change events from the tests
const serviceOptionsToTest = Object.values(serviceOptions).filter(option => !option.onEnabled && !option.onDisabled);

// TODO: XT-803 I do not make sense of this test.
describe('ServiceOption', () => {
    it('can activate/deactivate service options on tests by passing testOption testActiveServiceOptions to Test.withContext', async () => {
        await Test.withContext(
            async context => {
                assert.isTrue(await context.isServiceOptionEnabled(serviceOptions.showCaseDiscountOption));
                assert.isTrue(await context.isServiceOptionEnabled(serviceOptions.showCaseActiveByDefault));

                if (Context.getConfigurationValue('serviceOptionsLevel') === 'released') {
                    assert.isFalse(await context.isServiceOptionEnabled(serviceOptions.showCaseOption2));
                } else {
                    assert.isTrue(await context.isServiceOptionEnabled(serviceOptions.showCaseOption2));
                }
            },
            {
                testActiveServiceOptions: Object.values(serviceOptionsToTest),
            },
        );
    });

    it('activates propagate activation/deactivation of service options', async () => {
        await Test.withContext(async context => {
            await context.serviceOptionManager.activateServiceOptions(context, Object.values(serviceOptionsToTest));
            // Verify that all options are available
            assert.isTrue(await context.isServiceOptionEnabled(serviceOptions.activatesOption1));
            assert.isTrue(await context.isServiceOptionEnabled(serviceOptions.activatesOption2));
            assert.isTrue(await context.isServiceOptionEnabled(serviceOptions.activatedOption1));
            assert.isTrue(await context.isServiceOptionEnabled(serviceOptions.activatedOption2));
            assert.isTrue(await context.isServiceOptionEnabled(serviceOptions.activatedOption3));
        });

        // Deactivate serviceOptions.activatedOption1
        // => serviceOptions.activatesOption1 is deactivated
        // => serviceOptions.activatedOption1 is deactivated
        // => serviceOptions.activatedOption2 is activated
        await Test.withContext(async context => {
            await context.serviceOptionManager.deactivateServiceOptions(context, [serviceOptions.activatedOption1]);
            assert.isFalse(await context.isServiceOptionEnabled(serviceOptions.activatesOption1));
            assert.isFalse(await context.isServiceOptionEnabled(serviceOptions.activatedOption1));
            assert.isTrue(await context.isServiceOptionEnabled(serviceOptions.activatedOption2));
        });

        // Deactivate serviceOptions.activatesOption1
        // => serviceOptions.activatesOption1 is deactivated
        // => serviceOptions.activatedOption1 is still activated
        // => serviceOptions.activatedOption2 is still activated
        await Test.withContext(async context => {
            await context.serviceOptionManager.activateServiceOptions(context, Object.values(serviceOptionsToTest));
            await context.serviceOptionManager.deactivateServiceOptions(context, [serviceOptions.activatesOption1]);
            assert.isFalse(await context.isServiceOptionEnabled(serviceOptions.activatesOption1));
            assert.isTrue(await context.isServiceOptionEnabled(serviceOptions.activatedOption1));
            assert.isTrue(await context.isServiceOptionEnabled(serviceOptions.activatedOption2));
            assert.isTrue(await context.isServiceOptionEnabled(serviceOptions.activatedOption3));
        });

        await Test.withContext(async context => {
            await context.serviceOptionManager.activateServiceOptions(context, Object.values(serviceOptionsToTest));
            await context.serviceOptionManager.deactivateServiceOptions(context, [
                serviceOptions.activatesOption1,
                serviceOptions.activatesOption2,
            ]);
            // Deactivate serviceOptions.activatedOption3
            // => serviceOptions.activatesOption1 is deactivated
            // => serviceOptions.activatesOption2 is deactivated
            // => serviceOptions.activatedOption1 is activated
            // => serviceOptions.activatedOption2 is activated
            // => serviceOptions.activatedOption3 is deactivated
            assert.isFalse(await context.isServiceOptionEnabled(serviceOptions.activatesOption1));
            assert.isFalse(await context.isServiceOptionEnabled(serviceOptions.activatesOption2));
            assert.isTrue(await context.isServiceOptionEnabled(serviceOptions.activatedOption1));
            assert.isTrue(await context.isServiceOptionEnabled(serviceOptions.activatedOption2));
            assert.isTrue(await context.isServiceOptionEnabled(serviceOptions.activatedOption3));
        });

        await Test.withContext(
            async context => {
                await context.serviceOptionManager.deactivateServiceOptions(
                    context,
                    Object.values(serviceOptionsToTest),
                );
                // Verify that all options are not available
                assert.isFalse(await context.isServiceOptionEnabled(serviceOptions.activatesOption1));
                assert.isFalse(await context.isServiceOptionEnabled(serviceOptions.activatesOption2));
                assert.isFalse(await context.isServiceOptionEnabled(serviceOptions.activatedOption1));
                assert.isFalse(await context.isServiceOptionEnabled(serviceOptions.activatedOption2));
                assert.isFalse(await context.isServiceOptionEnabled(serviceOptions.activatedOption3));
            },
            { testActiveServiceOptions: [] },
        );

        await Test.withContext(async context => {
            await context.serviceOptionManager.activateServiceOptions(context, [serviceOptions.activatedOption1]);
            // Activate serviceOptions.activatedOption1
            // => serviceOptions.activatesOption1 is deactivated
            // => serviceOptions.activatesOption2 is deactivated
            // => serviceOptions.activatedOption1 is activated
            // => serviceOptions.activatedOption2 is deactivated
            // => serviceOptions.activatedOption3 is deactivated
            assert.isFalse(await context.isServiceOptionEnabled(serviceOptions.activatesOption1));
            assert.isFalse(await context.isServiceOptionEnabled(serviceOptions.activatesOption2));
            assert.isTrue(await context.isServiceOptionEnabled(serviceOptions.activatedOption1));
            assert.isFalse(await context.isServiceOptionEnabled(serviceOptions.activatedOption2));
            assert.isFalse(await context.isServiceOptionEnabled(serviceOptions.activatedOption3));
        });
    });
});

describe('Service options in custom mutations', () => {
    it('executes operation if service option is enabled', async () => {
        await Test.withContext(
            async context => {
                const graphqlHelper = await graphqlSetup({ context, application: Test.application });

                const staticOperationWithServiceOption = await graphqlHelper.mutation(
                    '{ testProduct { staticOperationWithServiceOption(param: 6) } }',
                    { context },
                );

                assert.deepEqual(staticOperationWithServiceOption, {
                    testProduct: { staticOperationWithServiceOption: 'staticOperation param=6' },
                });

                const staticQueryOperationWithServiceOption = await graphqlHelper.query(
                    '{ testProduct { staticQueryOperationWithServiceOption(param: 6) } }',
                    { context },
                );

                assert.deepEqual(staticQueryOperationWithServiceOption, {
                    testProduct: { staticQueryOperationWithServiceOption: 'staticOperation param=6' },
                });
            },
            { testActiveServiceOptions: [showCaseOptionMutation], user: { email: '<EMAIL>' } },
        );
    });

    it('rejects operation if service option is not enabled', async () => {
        await Test.withContext(
            async context => {
                const graphqlHelper = await graphqlSetup({ context, application: Test.application });

                await assertIsRejectedWithDiagnoses(
                    graphqlHelper.mutation('{ testProduct { staticOperationWithServiceOption(param: 6) } }', {
                        context,
                    }),
                    {
                        message: 'Static operation with service option failed.',
                        diagnoses: [
                            {
                                severity: 4,
                                message:
                                    'Operation TestProduct.staticOperationWithServiceOption is not enabled by the configuration of the application.',
                                path: [],
                            },
                        ],
                    },
                );

                await assertIsRejectedWithDiagnoses(
                    graphqlHelper.query('{ testProduct { staticQueryOperationWithServiceOption(param: 6) } }', {
                        context,
                    }),
                    {
                        message: 'Static query operation with service option failed.',
                        diagnoses: [
                            {
                                severity: 4,
                                message:
                                    'Operation TestProduct.staticQueryOperationWithServiceOption is not enabled by the configuration of the application.',
                                path: [],
                            },
                        ],
                    },
                );
            },
            { testActiveServiceOptions: [] },
        );
    });
});
