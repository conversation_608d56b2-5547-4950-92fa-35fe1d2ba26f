import { Application, Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as fsp from 'path';
import { inc, ReleaseType } from 'semver';
import { SysPackageManager } from '../../../lib/services/sys-package-manager';
import { UpgradeSqlSchema } from '../../../lib/services/upgrade';

// Use a different schema to avoid messing up with Test.application
const schemaName = 'xtrem_manage_application_test';

export async function createLocalTestApplication() {
    const buildDir = fsp.join(__dirname, '../../../../build');
    const application = await Test.createCliApplication({
        applicationType: 'test',
        buildDir,
        schemaName,
    });
    return application;
}

function bump(version: string, release: ReleaseType): string {
    const bumped = inc(version, release);
    if (!bumped) assert.fail('semver inc failed');
    return bumped;
}

describe('Upgrade version checks', () => {
    beforeEach(async () => {
        await Application.dropDbSchema(schemaName);
        await Application.createDbSchema(schemaName);
    });

    it('does not upgrade if major version moves backwards', async () => {
        const application = await createLocalTestApplication();
        const packageManager = SysPackageManager.fromApplication(application);
        await packageManager.createSqlSchemaAndTables();
        const bumpMajor = bump(application.version, 'major');
        const oldVersion = application.mainPackage.version;
        await application.withCommittedContext(null, context =>
            packageManager.setCurrentVersion(context, application.mainPackage, bumpMajor),
        );
        await assert.isRejected(
            UpgradeSqlSchema.upgrade(application),
            `Package @sage/xtrem-system@${bumpMajor} can't be downgraded to ${oldVersion}`,
        );
    });

    it('does not upgrade if minor version moves backwards', async () => {
        const application = await createLocalTestApplication();
        const packageManager = SysPackageManager.fromApplication(application);
        await packageManager.createSqlSchemaAndTables();
        const bumpMinor = bump(application.version, 'minor');
        const oldVersion = application.mainPackage.version;
        await application.withCommittedContext(null, context =>
            packageManager.setCurrentVersion(context, application.mainPackage, bumpMinor),
        );
        await assert.isRejected(
            UpgradeSqlSchema.upgrade(application),
            `Package @sage/xtrem-system@${bumpMinor} can't be downgraded to ${oldVersion}`,
        );
    });
});
