import {
    Application,
    ConditionVariable,
    Context,
    CoreHooks,
    NodeCreateData,
    Test,
    UiBroadcaster,
} from '@sage/xtrem-core';
import { WebSocketNotificationManager } from '@sage/xtrem-infrastructure-adapter';
import { NotificationAction } from '@sage/xtrem-shared';
import { assert, expect } from 'chai';
import * as sinon from 'sinon';
import * as xtremSystem from '../../../lib/index';

function reinitializeUiBroadcaster(context: Context): void {
    const anyApplication = context.application as any;
    if (anyApplication.uiBroadcaster) {
        anyApplication.uiBroadcaster.close();
    }
    // hack to reinitialize the broadcaster getter
    (context.application as any) = {
        uiBroadcaster: new UiBroadcaster(context.application, {
            timeout: 10,
        }),
    };
}

function createBroacastCondition(): Promise<void> {
    return new Promise((resolve, reject) => {
        Application.emitter.once('uiBroadcastCompleted', () => {
            resolve(undefined);
        });
        Application.emitter.once('uiBroadcastError', (message: string) => {
            reject(message);
        });
    });
}

const sandbox = sinon.createSandbox();

describe('Notification manager', () => {
    before(async () => {
        await Test.withCommittedContext(
            async context => {
                const action = (index: number, notificationId: number) => {
                    return {
                        title: `action ${index}`,
                        link: `link action ${index}`,
                        style: 'primary',
                        clientNotifications: notificationId,
                    };
                };

                const data = [
                    {
                        description: 'test notification 1',
                        icon: 'icon1',
                        level: 'info',
                        isRead: false,
                        recipient: (await context.user)?._id,
                        title: 'Test notification 1',
                        shouldDisplayToast: false,
                    },
                    {
                        description: 'test notification 2',
                        icon: 'icon1',
                        level: 'info',
                        isRead: false,
                        recipient: (await context.user)?._id,
                        title: 'Test notification 2',
                        shouldDisplayToast: false,
                    },
                    {
                        description: 'test notification 3',
                        icon: 'icon1',
                        level: 'info',
                        isRead: false,
                        recipient: context.read(xtremSystem.nodes.User, { email: '<EMAIL>' }),
                        title: 'Test notification 3',
                        shouldDisplayToast: false,
                    },
                ];
                await Promise.all(
                    data.map(async notification => {
                        const newNotification = await context.create(
                            xtremSystem.nodes.SysClientNotification,
                            notification as any,
                        );
                        await newNotification.$.save();
                    }),
                );
                const notifications = context
                    .query(xtremSystem.nodes.SysClientNotification, {
                        forUpdate: true,
                    })
                    .toArray();

                await Promise.all(
                    (await notifications).map(
                        async (notification: xtremSystem.nodes.SysClientNotification, idx: number) => {
                            const newNotificationAction = await context.create(
                                xtremSystem.nodes.SysClientNotificationAction,
                                action(
                                    idx,
                                    notification._id,
                                ) as NodeCreateData<xtremSystem.nodes.SysClientNotificationAction>,
                            );
                            await newNotificationAction.$.save();
                        },
                    ),
                );
            },
            { auth: { login: '<EMAIL>' } },
        );
    });
    it('should get only connected user notifications', () =>
        Test.withContext(
            async context => {
                const notifManager = CoreHooks.createNotificationManager(context.application);
                const allNotifications = await notifManager.getUserNotifications(context);
                assert.equal(allNotifications.length, 2);
            },
            { user: { email: '<EMAIL>' } },
        ));
    it('should return _id when get user notifications actions', () =>
        Test.withContext(
            async context => {
                const notifManager = CoreHooks.createNotificationManager(context.application);
                const allNotifications = await notifManager.getUserNotifications(context);
                await Promise.all(
                    allNotifications.map(async notification => {
                        (await notification.actions).forEach((action: NotificationAction) =>
                            expect(action).to.have.property('_id'),
                        );
                    }),
                );
            },
            { user: { email: '<EMAIL>' } },
        ));
    it('can mark as all notification as read', () =>
        Test.withContext(
            async context => {
                const notifManager = CoreHooks.createNotificationManager(context.application);
                await notifManager.markAllRead(context);
                const allNotifications = await notifManager.getUserNotifications(context);
                assert.isTrue(await allNotifications[0].isRead);
                assert.isTrue(await allNotifications[1].isRead);
            },
            { user: { email: '<EMAIL>' } },
        ));
    it('can mark one notification as read', () =>
        Test.withContext(
            async context => {
                const notifManager = CoreHooks.createNotificationManager(context.application);
                let allNotifications = await notifManager.getUserNotifications(context);
                await notifManager.markRead(context, String(allNotifications[0]._id));
                allNotifications = await notifManager.getUserNotifications(context);

                assert.isTrue(await allNotifications[0].isRead);
                assert.isFalse(await allNotifications[1].isRead);
            },
            { user: { email: '<EMAIL>' } },
        ));

    it('can delete one notification as read', () =>
        Test.withContext(
            async context => {
                const notifManager = CoreHooks.createNotificationManager(context.application);
                const allNotifications = await notifManager.getUserNotifications(context);
                await notifManager.delete(context, String(allNotifications[0]._id));
                assert.equal((await notifManager.getUserNotifications(context)).length, 1);
            },
            { user: { email: '<EMAIL>' } },
        ));

    it('can purge notifications', () =>
        Test.withContext(
            async context => {
                const notifManager = CoreHooks.createNotificationManager(context.application);
                await context.deleteMany(xtremSystem.nodes.SysClientNotification, {});
                assert.equal((await notifManager.getUserNotifications(context)).length, 0);
            },
            { user: { email: '<EMAIL>' } },
        ));
});

describe('Broadcast manager', () => {
    const broadcastMessageStub = sinon.stub();
    let broadcastSpy: sinon.SinonSpy;

    beforeEach(() => {
        broadcastSpy = sandbox.spy(WebSocketNotificationManager, 'broadcast');

        sandbox
            .stub(WebSocketNotificationManager as any, 'webSocketHelper')
            .returns({ broadcastMessage: broadcastMessageStub });
    });

    it('should broadcast one message', () =>
        Test.withContext(
            async context => {
                reinitializeUiBroadcaster(context);

                const condition = createBroacastCondition();
                context.broadcastToAllUsers({ category: 'node/test', payload: 'test_payload' });
                assert.equal(broadcastSpy.callCount, 0);
                await condition;
                assert.equal(broadcastSpy.callCount, 1);
            },
            { user: { email: '<EMAIL>' } },
        ));

    it('should not broadcast the same message twice if it is part of the same batch', () =>
        Test.withContext(
            async context => {
                reinitializeUiBroadcaster(context);

                const condition = createBroacastCondition();
                context.broadcastToAllUsers({ category: 'node/test', payload: 'test_payload_twice' });
                context.broadcastToAllUsers({ category: 'node/test', payload: 'test_payload_twice' });

                await condition;
                assert.equal(broadcastSpy.callCount, 1);
            },
            { user: { email: '<EMAIL>' } },
        ));

    it('should broadcast 5 messages', () =>
        Test.withContext(
            async context => {
                reinitializeUiBroadcaster(context);

                const condition = createBroacastCondition();

                context.broadcastToAllUsers({ category: 'node/test', payload: 'test_payload_1' });
                context.broadcastToAllUsers({ category: 'node/test', payload: 'test_payload_2' });
                context.broadcastToAllUsers({ category: 'node/test', payload: 'test_payload_3' });
                context.broadcastToAllUsers({ category: 'node/test', payload: 'test_payload_4' });
                context.broadcastToAllUsers({ category: 'node/test', payload: 'test_payload_5' });

                assert.equal(broadcastSpy.callCount, 0);
                await condition;
                assert.equal(broadcastSpy.callCount, 5);
            },
            { user: { email: '<EMAIL>' } },
        ));

    it('should fail to broadcast custom message if commit is not successful', async () => {
        // The condition to be notified when the rollback occurs
        const rollBackCondition = new ConditionVariable();
        await Test.withContext(
            context => {
                reinitializeUiBroadcaster(context);
                const rollbackModifiedCachedCategories = (context as any).rollbackModifiedCachedCategories;
                // rollbackModifiedCachedCategories is not exposed in the context, so we need the any cast.
                sandbox.stub(context as any, 'rollbackModifiedCachedCategories').callsFake(() => {
                    rollbackModifiedCachedCategories.call(context);
                    rollBackCondition.notifyAll();
                });

                context.broadcastToAllUsers({
                    category: 'node/test',
                    payload: 'test_custom_message',
                    afterCommit: true,
                });
            },
            { user: { email: '<EMAIL>' } },
        );
        await rollBackCondition.wait();
        assert.equal(broadcastSpy.callCount, 0);
    });

    afterEach(() => {
        sandbox.restore();
        sandbox.resetHistory();
    });
});
