import { asyncArray, Context, Test } from '@sage/xtrem-core';
import { TenantService } from '@sage/xtrem-data-management';
import { assert } from 'chai';

describe('customers', () => {
    // To be restored
    it('initTenantIdentifiers', () =>
        Test.withContext(async context => {
            const tenants = [] as any[];
            [...Array(10)].map((ai, i) => {
                const customer = { id: `${i}`, name: `customer ${i}` };
                [...Array(3)].map((aj, j) =>
                    tenants.push({
                        id: `${'0'.repeat(19)}${customer.id + j}`,
                        name: `tenant ${j} for customer ${customer.id + j}}`,
                        customer,
                    }),
                );
                return customer;
            });
            await asyncArray(tenants).forEach(tenant =>
                TenantService.initTenantIdentifiers(
                    context,
                    tenant.customer.id,
                    tenant.customer.name,
                    tenant.id,
                    tenant.name,
                ),
            );
            const ids = await Context.tenantManager.listTenantsIds(context);
            assert.equal(ids.length, tenants.length + 1); // +1 because the database also contains the default tenant '77777....777'
            tenants.forEach(tenant => assert.isTrue(!!ids.find(id => id === tenant.id)));
        }));
});
