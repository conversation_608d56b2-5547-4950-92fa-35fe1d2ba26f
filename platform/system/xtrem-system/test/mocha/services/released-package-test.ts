// THIS WAS IN core runtime tests
import { Application, Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import { ReleaseType, inc } from 'semver';
import { PackageInfo, SysPackageManager } from '../../../lib/services/sys-package-manager';

interface HackData extends PackageInfo {}

const withHackedPackage = async (
    application: Application,
    changes: HackData,
    body: () => PromiseLike<void>,
): Promise<void> => {
    const packageManager = SysPackageManager.fromApplication(application);
    const oldData: HackData = {
        name: application.mainPackage.name,
        version: application.mainPackage.version,
        isReleased: application.mainPackage.isReleased,
        isHidden: application.mainPackage.isHidden,
    };
    await Test.withCommittedContext(context =>
        packageManager.updatePackageVersion(context, { ...changes, name: application.mainPackage.name }),
    );
    try {
        await body();
    } finally {
        await Test.withCommittedContext(context => packageManager.updatePackageVersion(context, oldData));
    }
};

function bump(version: string, release: ReleaseType): string {
    const bumped = inc(version, release);
    if (!bumped) assert.fail('semver inc failed');
    return bumped;
}

describe('Released packages', () => {
    it('is valid if packages have not been altered', () =>
        Test.withContext(context => Test.application.packageManager.validatePackageVersions(context)));

    it('is invalid if a package moves from release to unreleased', () =>
        Test.withContext(context =>
            withHackedPackage(
                context.application,
                {
                    name: context.application.mainPackage.name,
                    version: context.application.mainPackage.version,
                    isHidden: context.application.mainPackage.isHidden,
                    isReleased: !context.application.mainPackage.isReleased,
                },
                () =>
                    assert.isRejected(
                        Test.application.packageManager.validatePackageVersions(context),
                        'Package released status mismatch: @sage/xtrem-system',
                    ),
            ),
        ));

    it('is invalid if an unreleased package version is changed', () =>
        Test.withContext(context =>
            withHackedPackage(
                context.application,
                {
                    name: context.application.mainPackage.name,
                    version: bump(context.application.mainPackage.version, 'major'),
                    isHidden: context.application.mainPackage.isHidden,
                    isReleased: context.application.mainPackage.isReleased,
                },

                () =>
                    assert.isRejected(
                        Test.application.packageManager.validatePackageVersions(context),
                        context.application.mainPackage.isReleased
                            ? /Package version mismatch on released package \(major\): @sage\/xtrem-system \d+\.\d+\.\d+ -> \d+\.\d+\.\d+, please hot-upgrade/
                            : /Package version mismatch: @sage\/xtrem-system \d+\.\d+\.\d+ -> \d+\.\d+\.\d+, please upgrade./,
                    ),
            ),
        ));
});
