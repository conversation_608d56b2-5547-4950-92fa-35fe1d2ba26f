/* eslint-disable no-console */
import { Context, Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremSystem from '../../../lib/index';

async function clearLocales(): Promise<void> {
    await Test.withCommittedContext(context => context.deleteMany(xtremSystem.nodes.Locale, {}));
}

describe('Localization manager', () => {
    // Since we are using application.withReadonlyContext, we need to ensure that the table gets created, should we run a .only test
    before(() => Test.withContext(() => console.log('Loading test schema'), { locale: 'en-US' }));
    beforeEach(() => clearLocales());
    it('creates an entry for the tenant with createTenantLocale', () =>
        Test.withContext(async context => {
            await Context.localizationManager.createTenantLocale(context, 'fr-FR');
            const readLocale = await context.read(xtremSystem.nodes.Locale, { id: 'fr-FR' });
            assert.equal(await readLocale.id, 'fr-FR');
            assert.equal(await readLocale.language, 'fr');
        }));
    it('returns the xtrem default locale, if none is set for a tenant', () =>
        Test.withReadonlyContext(context => {
            assert.equal(context.defaultLocale, 'base');
        }));
    it.skip('returns the tenant default locale, if it is set', async () => {
        await Test.withCommittedContext(context => Context.localizationManager.createTenantLocale(context, 'fr-FR'));
        await Test.withReadonlyContext(async context => {
            assert.equal(context.defaultLocale, 'fr-FR');
            assert.equal(await Context.localizationManager.isMasterLocale(context), true);
        });
    });
    it.skip('it updates the default locale, and set the new master locales', async () => {
        await Test.withCommittedContext(async context => {
            const newLocale = await context.create(xtremSystem.nodes.Locale, {
                id: 'fr-FR',
            });
            await newLocale.$.save();
        });
        // 'fr-FR' is set as default locale and master locale (for french)
        await Test.withReadonlyContext(async context => {
            assert.equal(context.defaultLocale, 'fr-FR');
            assert.equal(await Context.localizationManager.isMasterLocale(context), true);
        });
        await Test.withCommittedContext(async context => {
            const newLocale = await context.create(xtremSystem.nodes.Locale, {
                id: 'en-GB',
                isDefaultLocale: true,
            });
            await newLocale.$.save();
        });
        // // 'en-GB' is set as default locale and master locale (for english)
        await Test.withReadonlyContext(async context => {
            assert.equal(context.defaultLocale, 'en-GB');
            assert.equal(await Context.localizationManager.isMasterLocale(context), true);
        });
        await Test.withReadonlyContext(
            async context => {
                assert.equal(context.defaultLocale, 'en-GB');
                assert.equal(context.currentLocale, 'fr-FR');
                assert.equal(await Context.localizationManager.isMasterLocale(context), true);
            },
            { config: Test.configForTest(), locale: 'fr-FR' },
        );
        await Test.withCommittedContext(async context => {
            const newLocale = await context.create(xtremSystem.nodes.Locale, {
                id: 'en-US',
            });
            await newLocale.$.save();
        });
        // 'en-GB' is set as default locale and master locale (for english)
        await Test.withReadonlyContext(
            async context => {
                assert.equal(context.defaultLocale, 'en-GB');
                assert.equal(context.currentLocale, 'en-US');
                assert.equal(await Context.localizationManager.isMasterLocale(context), false);
            },
            { config: Test.configForTest(), locale: 'en-US' },
        );
    });
    afterEach(() => clearLocales());
});
