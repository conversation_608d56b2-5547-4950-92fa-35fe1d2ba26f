import {
    AnyR<PERSON>ord,
    Application,
    asyncArray,
    ConfigManager,
    Context,
    CoreHooks,
    Dict,
    EnumSqlContext,
    getSqlCurrvalOfIdSequence,
    isSystemColumn,
    NodeFactory,
    readTableSchema,
    rootUserEmail,
    SchemaSqlContext,
    Test,
    UserInfo,
} from '@sage/xtrem-core';
import {
    ColumnDefinition,
    ForeignKeyDefinition,
    IndexColumn,
    IndexDefinition,
    TableDefinition,
    TriggerDefinition,
} from '@sage/xtrem-postgres';
import { pascalCase } from '@sage/xtrem-shared';
import { assert } from 'chai';
import { spawn } from 'child_process';
import * as fs from 'fs';
import { dump } from 'js-yaml';
import * as lodash from 'lodash';
import * as fsp from 'path';
import { SysCustomSqlHistory } from '../../../lib/nodes/sys-custom-sql-history';
import { SysPackVersion } from '../../../lib/nodes/sys-pack-version';
import { User } from '../../../lib/nodes/user';
import { updateContext } from '../../fixtures/setup';

/**
 * This test relies on 2 packages:
 * - xtrem-upgrade-test-v1 : in version 1.0.0
 * - xtrem-upgrade-test-v2 : in version 2.0.0
 *
 * Both packages work on the same nodes with some differences in their definition.
 */

const getNpmCommandName = () => (process.platform === 'win32' ? 'npm.cmd' : 'npm');

const packageNameV1 = 'xtrem-upgrade-test-v1';
const packageNameV2 = 'xtrem-upgrade-test-v2';
const packageNameV1ReleasedValidHotUpgrade = 'xtrem-upgrade-test-v1-released-valid';
const packageNameV1ReleasedInvalidUpgrade = 'xtrem-upgrade-test-v1-released-invalid';
let lastOutput: string = '';

const mainTenantId = '7'.repeat(21);

const upgradeSchemaName = 'xtrem_upgrade_test';

function tailLogLine(msg: string) {
    if (msg.endsWith('\n')) return msg.substring(0, msg.length - 1);
    return msg;
}

function getPackageFolder(packageName: string): string {
    const sageFolder = fsp.join(__dirname, '../../../../../../upgrade-test');
    return fsp.join(sageFolder, packageName.split('/').pop()!);
}

type SemVerCompatibilityLevel = 'major' | 'minor' | 'patch';

function executeNpmCommand(
    packageName: string,
    params: string[],
    options?: {
        port?: number;
        semVerCompatibilityLevel?: SemVerCompatibilityLevel;
    },
): Promise<void> {
    // Copy a clone of the current configuration to the folder of the package with some slight differences
    const workingFolder = getPackageFolder(packageName);
    const configToMerge: any = {
        server: {
            port: options?.port || 8241, // Do no collide with the port 8240 (use by this app),
        },
    };
    if (options?.semVerCompatibilityLevel != null) {
        configToMerge.semVerCompatibilityLevel = options.semVerCompatibilityLevel;
    }
    const cfg = lodash.merge(ConfigManager.current, configToMerge);

    // Test will run in a separate process so we set the logs.disabled config flag
    // in the generated config file if logs are disabled for tests.
    if (cfg?.logs?.disabledForTests) {
        cfg.logs.disabled = true;
    }
    // generate the temporary config file for the spawned process.
    fs.writeFileSync(fsp.join(workingFolder, 'xtrem-config.yml'), dump(cfg));

    return new Promise((resolve, reject) => {
        const proc = spawn(getNpmCommandName(), params, {
            env: { ...process.env, XTREM_SCHEMA_NAME: upgradeSchemaName },
            cwd: workingFolder,
        });

        proc.stdout.on('data', (data: any) => {
            lastOutput = tailLogLine(data.toString());
            // eslint-disable-next-line no-console
            console.log(`${packageName}>> ${tailLogLine(data.toString())}`);
        });

        proc.stderr.on('data', (data: any) => {
            // eslint-disable-next-line no-console
            console.log(`${packageName}>> ${tailLogLine(data.toString())}`);
        });

        proc.on('close', (code: any) => {
            // eslint-disable-next-line no-console
            console.log(`${packageName}>> Exited with code ${code}`);
            if (code === 0) {
                resolve();
                return;
            }
            reject(new Error(`Package ${packageName} failed with code ${code}`));
        });
    });
}

function ensureEqual<T>(path: string, expected: T, current: T, fieldName: keyof T): void {
    const expectedVal = expected[fieldName];
    const currentVal = current[fieldName];
    if (Number.isNaN(currentVal)) {
        if (expectedVal == null) return; // Consider as OK
    }

    // try {
    if (currentVal && typeof currentVal === 'object') {
        assert.deepEqual(currentVal, expectedVal, `${path}: ${String(fieldName)} mismatch`);
    } else {
        assert.equal(currentVal, expectedVal, `${path}: ${String(fieldName)} mismatch`);
    }
    // } catch (err) {
    //     console.log(`>>>> path: ${path}`);
    //     console.log(`>>>> expected`, expectedVal);
    //     console.log(`>>>> current`, currentVal);
    //     throw err;
    // }
}

function ensureArrayEqual<T>(
    path: string,
    expectedArray: T[],
    currentArray: T[],
    idFieldName?: keyof T,
    checkFunction?: (subPath: string, expectedItem: T, currentItem: T) => void,
): void {
    const indexArrayById = (items: T[]): Dict<T> => {
        return items.reduce((total, item, idx) => {
            switch (idFieldName) {
                case '$id':
                    total[idx] = item;
                    break;
                case undefined: {
                    // Index by the item itself (should only be used for string[])
                    total[item as any as string] = item;
                    break;
                }
                default: {
                    // Index by idFieldName
                    total[item[idFieldName] as any as string] = item;
                }
            }
            return total;
        }, {} as Dict<T>);
    };
    const expectedsById = indexArrayById(expectedArray);
    const currentsById = indexArrayById(currentArray);
    // Quick check: compare only the ids
    assert.deepEqual(
        Object.keys(currentsById).sort(),
        Object.keys(expectedsById).sort(),
        `${path} ${idFieldName?.toString()} do not match`,
    );

    // Deep check: compare every items
    if (checkFunction) {
        Object.keys(expectedsById).forEach(expectedId => {
            const expected = expectedsById[expectedId];
            const current = currentsById[expectedId];
            checkFunction(`${path}/${expectedId}`, expected, current);
        });
    }
}

/**
 * Returns the system FKs that are declared on every tables
 */
function getSystemForeignKeys(tableDef: TableDefinition, options?: { hasVendor?: boolean }): ForeignKeyDefinition[] {
    const foreignKeys: ForeignKeyDefinition[] = tableDef.comment?.baseTable
        ? []
        : [
              {
                  name: `${tableDef.tableName}__create_user_fk`,
                  targetTable: 'user',
                  targetColumnNames: [],
                  columnNames: [],
                  comment: {
                      targetTableName: 'user',
                      columns: { _tenant_id: '_tenant_id', _create_user: '_id' },
                      onDeleteBehaviour: 'restrict',
                      isDeferrable: true,
                  },
                  onDeleteBehaviour: 'restrict',
                  isDeferrable: true,
              },
              {
                  name: `${tableDef.tableName}__update_user_fk`,
                  targetTable: 'user',
                  targetColumnNames: [],
                  columnNames: [],
                  comment: {
                      targetTableName: 'user',
                      columns: { _tenant_id: '_tenant_id', _update_user: '_id' },
                      onDeleteBehaviour: 'restrict',
                      isDeferrable: true,
                  },
                  onDeleteBehaviour: 'restrict',
                  isDeferrable: true,
              },
          ];
    if (options?.hasVendor) {
        foreignKeys.push({
            name: `${tableDef.tableName}__vendor_fk`,
            targetTable: 'sys_vendor',
            targetColumnNames: [],
            columnNames: [],
            comment: {
                targetTableName: 'sys_vendor',
                columns: { _vendor: '_id' },
                onDeleteBehaviour: 'noAction',
                isDeferrable: true,
            },
            onDeleteBehaviour: 'noAction',
            isDeferrable: true,
        });
    }
    if (!tableDef.isSharedByAllTenants) {
        foreignKeys.push({
            name: `${tableDef.tableName}__tenant_id_fk`,
            targetTable: 'sys_tenant',
            targetColumnNames: ['tenant_id'],
            columnNames: [],
            comment: {
                targetTableName: 'sys_tenant',
                columns: { _tenant_id: 'tenant_id' },
                onDeleteBehaviour: 'restrict',
                isDeferrable: true,
            },
            onDeleteBehaviour: 'restrict',
            isDeferrable: true,
        });
    }
    return foreignKeys;
}

/**
 * Ensures that a table has been dropped
 */
async function checkTableDropped(context: Context, tableName: string) {
    const sqlContext = new SchemaSqlContext(context.application);
    assert(!(await sqlContext.tableExists(tableName)), `Table ${tableName} still exists`);
}

// Check that a table matches a given definition and given data
async function checkTable(
    context: Context,
    options: {
        /** The reference table definition */
        tableDef: TableDefinition;
        /** The reference data */
        data?: Dict<any>[];
        orderBy?: string[];
        /**
         * By default, all the non-system columns will be compared but it may happen that we
         * need to compare system columns as well
         */
        extraColumnsForDataComparison?: string[];
        hasVendor?: boolean;
        doNotCheckTableDef?: true;
    },
): Promise<void> {
    if (!options.doNotCheckTableDef) {
        // Check schema
        await _checkMetadata(context, options.tableDef, options.hasVendor);
    }
    if (options.data) {
        await _checkData(context, {
            tableDef: options.tableDef,
            data: options.data,
            orderBy: options.orderBy,
            extraColumnsForDataComparison: options.extraColumnsForDataComparison,
            hasVendor: options.hasVendor,
        });
    }
}

// Check metadata (db content vs expected content)
async function _checkMetadata(
    context: Context,
    /** The reference table definition */
    tableDef: TableDefinition,
    /** The reference data */
    hasVendor?: boolean,
) {
    const currentTableDef = await readTableDef(context, tableDef.tableName);
    if (tableDef.foreignKeys != null) {
        // Add the system foreign keys
        tableDef.foreignKeys = [
            ...getSystemForeignKeys(tableDef, {
                hasVendor,
            }),
            ...tableDef.foreignKeys,
        ];
    }

    ensureEqual(`table(${tableDef.tableName})`, tableDef, currentTableDef, 'tableName');
    if (tableDef.comment != null) {
        ensureEqual(`table(${tableDef.tableName})`, tableDef, currentTableDef, 'comment');
    }
    if (tableDef.columns != null) {
        ensureArrayEqual(
            `table(${tableDef.tableName})/columns`,
            tableDef.columns,
            currentTableDef.columns || [],
            'name',
            compareColumnDef,
        );
    }
    if (tableDef.indexes != null) {
        ensureArrayEqual(
            `table(${tableDef.tableName})/indexes`,
            tableDef.indexes,
            currentTableDef.indexes || [],
            'name',
            compareIndexDef,
        );
    }

    if (tableDef.foreignKeys != null) {
        ensureArrayEqual(
            `table(${tableDef.tableName})/foreignKeys`,
            tableDef.foreignKeys,
            currentTableDef.foreignKeys || [],
            'name',
            compareForeignKeyDef,
        );
    }

    if (tableDef.triggers != null) {
        if (!tableDef.comment?.baseTable)
            tableDef.triggers = [
                ...(tableDef.triggers || []),
                ...[
                    {
                        name: 'insert_table',
                        when: 'BEFORE',
                        event: 'INSERT',
                        functionName: `${upgradeSchemaName}.insert_table`,
                    } as TriggerDefinition,
                    {
                        name: 'update_table',
                        when: 'BEFORE',
                        event: 'UPDATE',
                        functionName: `${upgradeSchemaName}.update_table`,
                    } as TriggerDefinition,
                ],
            ];
        ensureArrayEqual(
            `table(${tableDef.tableName})/triggers`,
            lodash.sortBy(tableDef.triggers, ['name']),
            lodash.sortBy(currentTableDef.triggers || [], ['name']),
            'name',
            compareTriggerDef,
        );
    }
}

// Check data (db content vs expected content)
async function _checkData(
    context: Context,
    options: {
        /** The reference table definition */
        tableDef: TableDefinition;
        /** The reference data */
        data: Dict<any>[];
        orderBy?: string[];
        /**
         * By default, all the non-system columns will be compared but it may happen that we
         * need to compare system columns as well
         */
        extraColumnsForDataComparison?: string[];
        hasVendor?: boolean;
    },
) {
    const columnNames = options.extraColumnsForDataComparison || [];
    if (options.tableDef.columns != null) columnNames.push(...(options.tableDef.columns || []).map(col => col.name));

    // Read all the data from the table (in raw mode: needed conversions will be applied later)
    const parts: string[] = [
        `SELECT ${lodash.uniq(columnNames).join(',')} FROM ${context.schemaName}.${options.tableDef.tableName}`,
    ];
    const args: any[] = [];
    if (!options.tableDef.isSharedByAllTenants) {
        parts.push('WHERE _tenant_id=$1');
        args.push(context.tenantId);
    }
    if (options.orderBy) parts.push(`ORDER BY ${options.orderBy.join()}`);
    const rawData = await context.executeSql<AnyRecord[]>(parts.join(' '), args);

    // Rows should be sorted by the PK (in options.data AND in currentData)
    ensureArrayEqual(
        `table(${options.tableDef.tableName})/DATA`,
        options.data,
        rawData,
        '$id',
        (subPath: string, expectedRow: Dict<any>, currentRow: Dict<any>) => {
            assert.deepEqual(currentRow, expectedRow, `${subPath}: data mismatch`);
        },
    );
}

function compareColumnDef(path: string, expected: ColumnDefinition, current: ColumnDefinition): void {
    ensureEqual(path, expected, current, 'name');
    ensureEqual(path, expected, current, 'colType');
    ensureEqual(path, expected, current, 'default');
    ensureEqual(path, expected, current, 'isAutoIncrement');
    ensureEqual(path, expected, current, 'isNullable');
    ensureEqual(path, expected, current, 'enumDataType');

    if (expected.maxLength != null) ensureEqual(path, expected, current, 'maxLength');
    if (expected.precision != null) ensureEqual(path, expected, current, 'precision');
    if (expected.scale != null) ensureEqual(path, expected, current, 'scale');
    if (expected.comment != null) ensureEqual(path, expected, current, 'comment');
    ensureEqual(path, expected, current, 'type');
}

function compareIndexDef(path: string, expected: IndexDefinition, current: IndexDefinition): void {
    ensureEqual(path, expected, current, 'name');
    ensureEqual(path, expected, current, 'isUnique');
    ensureArrayEqual(
        `${path}/index(${expected.name})/columns`,
        expected.columns || [],
        current.columns || [],
        'name',
        (subPath: string, c1: IndexColumn, c2: IndexColumn) => {
            ensureEqual(subPath, c1, c2, 'ascending');
        },
    );
}

function compareForeignKeyDef(path: string, expected: ForeignKeyDefinition, current: ForeignKeyDefinition): void {
    ensureEqual(path, expected, current, 'name');
    ensureEqual(path, expected, current, 'targetTable');
    ensureEqual(path, expected, current, 'onDeleteBehaviour');
    ensureEqual(path, expected, current, 'isDeferrable');
    ensureArrayEqual(`${path}/FK(${expected.name})/columns`, expected.columnNames || [], current.columnNames || []);
    ensureArrayEqual(
        `${path}/FK(${expected.name})/targetColumns`,
        expected.targetColumnNames || [],
        current.targetColumnNames || [],
    );
    ensureEqual(path, expected, current, 'comment');
}

function compareTriggerDef(path: string, expected: TriggerDefinition, current: TriggerDefinition): void {
    ensureEqual(path, expected, current, 'name');
    ensureEqual(path, expected, current, 'event');
    ensureEqual(path, expected, current, 'when');
    ensureEqual(path, expected, current, 'functionName');
}

async function readTableDef(context: Context, tableName: string): Promise<TableDefinition> {
    const tableDef = await readTableSchema(context, tableName, { includes: ['triggers'], getComments: true });
    const showColumn = (colName: string): boolean => !isSystemColumn(colName);

    // Remove all the system columns from the definitions. No need to compare them as they are set by the framework
    tableDef.columns = tableDef.columns?.filter(col => showColumn(col.name));
    tableDef.indexes?.forEach(idx => {
        idx.columns = idx.columns.filter(col => showColumn(col.name));
    });
    tableDef.foreignKeys?.forEach(fk => {
        fk.columnNames = fk.columnNames.filter(showColumn);
        fk.targetColumnNames = fk.targetColumnNames.filter(showColumn);
    });

    // No need to compare the PK, it is set by the framework
    delete tableDef.primaryKey;
    return tableDef;
}

function deleteTempConfig(packageName: string) {
    // delete temp config if it is already generated from another test.
    const workingFolder = getPackageFolder(packageName);
    if (fs.existsSync(fsp.join(workingFolder, 'xtrem-config.yml')))
        fs.unlinkSync(fsp.join(workingFolder, 'xtrem-config.yml'));
}

async function checkTablesAfterUpgradeToV2(testApplication: Application): Promise<void> {
    await testApplication.asRoot.withReadonlyContext(Test.defaultTenantId, async context => {
        // Check upgrade_drop_table table
        await checkTableDropped(context, 'upgrade_drop_table');

        // Check update_constructor_with_base_node table data
        await checkTable(context, {
            tableDef: {
                schemaName: upgradeSchemaName,
                tableName: 'update_constructor_with_base_node',
                columns: [
                    {
                        name: '_id',
                        isNullable: false,
                        type: 'integer',
                        default: '',
                    },
                    {
                        name: '_constructor',
                        isNullable: false,
                        type: 'string',
                        default: '',
                    },
                    {
                        name: 'str_base',
                        isNullable: false,
                        type: 'string',
                        default: '',
                    },
                ],
            },
            doNotCheckTableDef: true,
            data: [
                { _id: 6, _constructor: 'UpdateConstructorWithSubNodeLevel2New', str_base: '' },
                { _id: 7, _constructor: 'UpdateConstructorWithSubNodeLevel2New', str_base: '' },
                { _id: 8, _constructor: 'UpdateConstructorWithSubNodeLevel2New', str_base: '' },
            ],
        });

        // Check update_constructor_with_sub_node_level_1_new table data
        await checkTable(context, {
            tableDef: {
                schemaName: upgradeSchemaName,
                tableName: 'update_constructor_with_sub_node_level_1_new',
                columns: [
                    {
                        name: '_id',
                        isNullable: false,
                        type: 'integer',
                        default: '',
                    },
                    {
                        name: '_constructor',
                        isNullable: false,
                        type: 'string',
                        default: '',
                    },
                    {
                        name: 'name',
                        isNullable: false,
                        type: 'string',
                        default: '',
                    },
                ],
                triggers: [],
            },
            doNotCheckTableDef: true,
            data: [
                { _id: 6, _constructor: 'UpdateConstructorWithSubNodeLevel2New', name: '' },
                { _id: 7, _constructor: 'UpdateConstructorWithSubNodeLevel2New', name: '' },
                { _id: 8, _constructor: 'UpdateConstructorWithSubNodeLevel2New', name: '' },
            ],
        });
        // platform/upgrade-test/xtrem-upgrade-test-v2-base/lib/upgrades/vlatest/rename-abstract-node-update-constructor.ts
        await checkTableDropped(context, 'update_constructor_with_sub_node_level_1');

        // Check update_constructor_with_sub_node_level_2_new table data
        await checkTable(context, {
            tableDef: {
                schemaName: upgradeSchemaName,
                tableName: 'update_constructor_with_sub_node_level_2_new',
                columns: [
                    {
                        name: '_id',
                        isNullable: false,
                        type: 'integer',
                        default: '',
                    },
                    {
                        name: 'element',
                        isNullable: false,
                        type: 'string',
                        default: '',
                    },
                ],
                triggers: [],
            },
            doNotCheckTableDef: true,
            data: [
                { _id: 6, element: 'element 1' },
                { _id: 7, element: 'element 2' },
                { _id: 8, element: 'element 3' },
            ],
        });
        // platform/upgrade-test/xtrem-upgrade-test-v2-base/lib/upgrades/vlatest/rename-node-update-constructor.ts
        await checkTableDropped(context, 'update_constructor_with_sub_node_level_2');

        // Check upgrade_delete_columns table
        await checkTable(context, {
            tableDef: {
                schemaName: upgradeSchemaName,
                tableName: 'upgrade_delete_columns',
                columns: [
                    {
                        name: 'name_1',
                        isNullable: false,
                        type: 'string',
                        default: '',
                        comment: { type: 'string', maxLength: 250, isSystem: false },
                    },
                ],
                comment: { isSharedByAllTenants: false },
                indexes: [
                    {
                        name: 'upgrade_delete_columns_ind0',
                        isUnique: true,
                        columns: [{ name: 'name_1', ascending: true }],
                    },
                ],
            },
            data: [
                { name_1: 'Hagenes Inc' },
                { name_1: 'Kemmer-Hagenes' },
                { name_1: 'Littel LLC' },
                { name_1: 'MacGyver-Pollich' },
                { name_1: 'Steuber and Sons' },
                { name_1: 'Will Group' },
            ],
            orderBy: ['name_1'],
        });

        // Check upgrade_add_columns table
        //
        // The following columns should have been set by the autoDataAction
        // - new_mandatory_string: set to its type's default
        // - new_mandatory_boolean: set to its type's default
        // - new_mandatory_boolean_with_true_as_default: set to 'true' by its defaultValue property
        // - new_mandatory_string_with_default_value : set by its defaultValue() function
        // - new_mandatory_date_with_default: set to 2000-01-02 by its defaultValue property
        // - new_mandatory_datetime_with_default: set to 2000-01-02T03:04:05 by its defaultValue property
        // The following columns should have been updated by the custom dataAction:
        // - ref_1
        // - ref_2
        // - new_mandatory_string
        // - new_nullable_integer

        // checkTable compares dates as string
        const dateDefaultValue = '2000-01-02';
        // We use TIMESTAMPTZ type for datetime. So we need a 'Z' suffix here.
        const datetimeDefaultValue = new Date('2000-01-02T03:04:05Z');

        await checkTable(context, {
            tableDef: {
                schemaName: upgradeSchemaName,
                tableName: 'upgrade_add_columns',
                columns: [
                    {
                        name: 'name_1',
                        isNullable: false,
                        type: 'string',
                        default: '',
                        comment: { type: 'string', isSystem: false, maxLength: 250 },
                    },
                    {
                        name: 'value_1',
                        isNullable: false,
                        type: 'integer',
                        comment: { type: 'integer', isSystem: false },
                    },
                    {
                        name: 'value_2',
                        isNullable: false,
                        type: 'integer',
                        comment: { type: 'integer', isSystem: false },
                    },
                    {
                        name: 'new_mandatory_string',
                        isNullable: false,
                        type: 'string',
                        default: '',
                        comment: { type: 'string', isSystem: false, maxLength: 250 },
                    },
                    {
                        name: 'new_mandatory_boolean',
                        isNullable: false,
                        type: 'boolean',
                        comment: { type: 'boolean', isSystem: false },
                    },
                    {
                        name: 'new_mandatory_boolean_with_true_as_default',
                        isNullable: false,
                        type: 'boolean',
                        comment: { type: 'boolean', isSystem: false },
                    },
                    {
                        name: 'new_nullable_integer',
                        isNullable: true,
                        type: 'integer',
                        comment: { type: 'integer', isSystem: false },
                    },
                    {
                        name: 'new_mandatory_string_with_default_value',
                        isNullable: false,
                        type: 'string',
                        default: '',
                        comment: { type: 'string', isSystem: false, maxLength: 250 },
                    },
                    {
                        name: 'new_mandatory_date_with_default',
                        isNullable: false,
                        type: 'date',
                        comment: { type: 'date', isSystem: false },
                    },
                    {
                        name: 'new_mandatory_datetime_with_default',
                        isNullable: false,
                        type: 'datetime',
                        comment: { type: 'datetime', isSystem: false },
                    },
                    {
                        name: 'ref_1',
                        isNullable: false,
                        type: 'integer',
                        comment: {
                            type: 'reference',
                            isSystem: false,
                            targetTableName: 'upgrade_referenced',
                            isSelfReference: false,
                        },
                    },
                    {
                        name: 'ref_2',
                        isNullable: false,
                        type: 'integer',
                        comment: {
                            type: 'reference',
                            isSystem: false,
                            targetTableName: 'upgrade_referenced',
                            isSelfReference: false,
                        },
                    },
                    {
                        name: 'new_enum_with_default',
                        isNullable: true,
                        type: 'enum',
                        enumDataType: {
                            name: 'upgrade_datatypes_enum_type_enum',
                            values: {},
                        },
                        comment: {
                            type: 'enum',
                            isSystem: false,
                            enumTypeName: 'upgrade_datatypes_enum_type_enum',
                        },
                    },
                    {
                        name: 'new_prop_on_renamed_enum',
                        isNullable: true,
                        type: 'enum',
                        enumDataType: {
                            name: 'upgrade_renamed_enum_v_2_type_enum',
                            values: {},
                        },
                        comment: {
                            type: 'enum',
                            isSystem: false,
                            enumTypeName: 'upgrade_renamed_enum_v_2_type_enum',
                        },
                    },
                ],
                comment: { isSharedByAllTenants: false },
                indexes: [
                    {
                        name: 'upgrade_add_columns_ind0',
                        isUnique: false,
                        columns: [{ name: 'name_1', ascending: false }],
                    },
                    {
                        name: 'upgrade_add_columns_ind1',
                        isUnique: true,
                        columns: [
                            { name: 'name_1', ascending: true },
                            { name: 'new_mandatory_string', ascending: true },
                        ],
                    },
                ],
                foreignKeys: [
                    {
                        name: 'upgrade_add_columns_ref_1_fk',
                        targetTable: 'upgrade_referenced',
                        targetColumnNames: [],
                        columnNames: ['ref_1'],
                        comment: {
                            targetTableName: 'upgrade_referenced',
                            columns: { _tenant_id: '_tenant_id', ref_1: '_id' },
                            onDeleteBehaviour: 'restrict',
                            isDeferrable: true,
                        },
                        onDeleteBehaviour: 'restrict',
                        isDeferrable: true,
                    },
                    {
                        name: 'upgrade_add_columns_ref_2_fk',
                        targetTable: 'upgrade_referenced',
                        targetColumnNames: [],
                        columnNames: ['ref_2'],
                        comment: {
                            targetTableName: 'upgrade_referenced',
                            columns: { _tenant_id: '_tenant_id', ref_2: '_id' },
                            onDeleteBehaviour: 'restrict',
                            isDeferrable: true,
                        },
                        onDeleteBehaviour: 'restrict',
                        isDeferrable: true,
                    },
                ],
            },
            data: [
                {
                    name_1: 'Hagenes Inc',
                    value_1: 2,
                    value_2: 4,
                    new_mandatory_string: 'new Hagenes Inc',
                    new_mandatory_boolean: false,
                    new_mandatory_boolean_with_true_as_default: true,
                    new_mandatory_string_with_default_value: 'defaultValue Hagenes Inc',
                    new_mandatory_date_with_default: dateDefaultValue,
                    new_mandatory_datetime_with_default: datetimeDefaultValue,
                    new_nullable_integer: null,
                    ref_1: 1,
                    ref_2: 2,
                    new_enum_with_default: null,
                    new_prop_on_renamed_enum: null,
                },
                {
                    name_1: 'Kemmer-Hagenes',
                    value_1: 6,
                    value_2: 12,
                    new_mandatory_string: 'new Kemmer-Hagenes',
                    new_mandatory_boolean: false,
                    new_mandatory_boolean_with_true_as_default: true,
                    new_mandatory_string_with_default_value: 'defaultValue Kemmer-Hagenes',
                    new_mandatory_date_with_default: dateDefaultValue,
                    new_mandatory_datetime_with_default: datetimeDefaultValue,
                    new_nullable_integer: null,
                    ref_1: 1,
                    ref_2: 2,
                    new_enum_with_default: 'value1',
                    new_prop_on_renamed_enum: null,
                },
                {
                    name_1: 'Littel LLC',
                    value_1: 1,
                    value_2: 2,
                    new_mandatory_string: 'new Littel LLC',
                    new_mandatory_boolean: false,
                    new_mandatory_boolean_with_true_as_default: true,
                    new_mandatory_string_with_default_value: 'defaultValue Littel LLC',
                    new_mandatory_date_with_default: dateDefaultValue,
                    new_mandatory_datetime_with_default: datetimeDefaultValue,
                    new_nullable_integer: null,
                    ref_1: 1,
                    ref_2: 2,
                    new_enum_with_default: null,
                    new_prop_on_renamed_enum: null,
                },
                {
                    name_1: 'MacGyver-Pollich',
                    value_1: 4,
                    value_2: 8,
                    new_mandatory_string: 'new MacGyver-Pollich',
                    new_mandatory_boolean: false,
                    new_mandatory_boolean_with_true_as_default: true,
                    new_mandatory_string_with_default_value: 'defaultValue MacGyver-Pollich',
                    new_mandatory_date_with_default: dateDefaultValue,
                    new_mandatory_datetime_with_default: datetimeDefaultValue,
                    new_nullable_integer: null,
                    ref_1: 1,
                    ref_2: 2,
                    new_enum_with_default: null,
                    new_prop_on_renamed_enum: null,
                },
                {
                    name_1: 'Steuber and Sons',
                    value_1: 3,
                    value_2: 6,
                    new_mandatory_string: 'new Steuber and Sons',
                    new_mandatory_boolean: false,
                    new_mandatory_boolean_with_true_as_default: true,
                    new_mandatory_string_with_default_value: 'defaultValue Steuber and Sons',
                    new_mandatory_date_with_default: dateDefaultValue,
                    new_mandatory_datetime_with_default: datetimeDefaultValue,
                    new_nullable_integer: null,
                    ref_1: 1,
                    ref_2: 2,
                    new_enum_with_default: null,
                    new_prop_on_renamed_enum: null,
                },
                {
                    name_1: 'Will Group',
                    value_1: 5,
                    value_2: 10,
                    new_mandatory_string: 'new Will Group',
                    new_mandatory_boolean: false,
                    new_mandatory_boolean_with_true_as_default: true,
                    new_mandatory_string_with_default_value: 'defaultValue Will Group',
                    new_mandatory_date_with_default: dateDefaultValue,
                    new_mandatory_datetime_with_default: datetimeDefaultValue,
                    new_nullable_integer: null,
                    ref_1: 1,
                    ref_2: 2,
                    new_enum_with_default: 'value1',
                    new_prop_on_renamed_enum: null,
                },
            ],
            orderBy: ['name_1'],
        });

        // Check upgrade_rename_columns table
        await checkTable(context, {
            tableDef: {
                schemaName: upgradeSchemaName,
                tableName: 'upgrade_rename_columns',
                columns: [
                    {
                        name: 'name_v_2',
                        isNullable: false,
                        type: 'string',
                        default: '',
                        comment: { type: 'string', isSystem: false, maxLength: 250 },
                    },
                    {
                        name: 'ref_2',
                        isNullable: false,
                        type: 'integer',
                        comment: {
                            type: 'reference',
                            isSystem: false,
                            targetTableName: 'upgrade_referenced',
                            isSelfReference: false,
                        },
                    },
                ],
                foreignKeys: [
                    {
                        // Make sure the foreign key was renamed as well
                        name: 'upgrade_rename_columns_ref_2_fk',
                        targetTable: 'upgrade_referenced',
                        targetColumnNames: [],
                        columnNames: ['ref_2'],
                        comment: {
                            targetTableName: 'upgrade_referenced',
                            columns: { _tenant_id: '_tenant_id', ref_2: '_id' },
                            onDeleteBehaviour: 'restrict',
                            isDeferrable: true,
                        },
                        onDeleteBehaviour: 'restrict',
                        isDeferrable: true,
                    },
                ],
                comment: { isSharedByAllTenants: false },
            },
            data: [
                { name_v_2: 'Hagenes Inc', ref_2: 1 },
                { name_v_2: 'Kemmer-Hagenes', ref_2: 1 },
                { name_v_2: 'Littel LLC', ref_2: 1 },
                { name_v_2: 'MacGyver-Pollich', ref_2: 1 },
                { name_v_2: 'Steuber and Sons', ref_2: 1 },
                { name_v_2: 'Will Group', ref_2: 1 },
            ],
            orderBy: ['name_v_2'],
        });

        // Check upgrade_update_columns table
        await checkTable(context, {
            tableDef: {
                schemaName: upgradeSchemaName,
                tableName: 'upgrade_update_columns',
                columns: [
                    {
                        name: 'name_1',
                        isNullable: false,
                        type: 'string',
                        default: '',
                        comment: { type: 'string', isSystem: false, maxLength: 50 },
                    },
                    {
                        name: 'value_2',
                        isNullable: true,
                        type: 'integer',
                        comment: { type: 'integer', isSystem: false },
                    },
                    {
                        name: 'value_3',
                        isNullable: false,
                        type: 'integer',
                        comment: { type: 'integer', isSystem: false },
                    },
                    {
                        name: 'self_ref_to_non_nullable',
                        isNullable: false,
                        type: 'integer',
                        default: getSqlCurrvalOfIdSequence(`${context.schemaName}.upgrade_update_columns`),
                        comment: {
                            type: 'reference',
                            isSystem: false,
                            targetTableName: 'upgrade_update_columns',
                            isSelfReference: true,
                        },
                    },
                    {
                        name: 'self_ref_to_nullable',
                        isNullable: true,
                        type: 'integer',
                        comment: {
                            type: 'reference',
                            isSystem: false,
                            targetTableName: 'upgrade_update_columns',
                            isSelfReference: false,
                        },
                    },
                ],
                comment: { isSharedByAllTenants: false },
                foreignKeys: [
                    {
                        name: 'upgrade_update_columns_self_ref_to_non_nullable_fk',
                        targetTable: 'upgrade_update_columns',
                        targetColumnNames: [],
                        columnNames: ['self_ref_to_non_nullable'],
                        comment: {
                            targetTableName: 'upgrade_update_columns',
                            columns: { _tenant_id: '_tenant_id', self_ref_to_non_nullable: '_id' },
                            onDeleteBehaviour: 'restrict',
                            isDeferrable: true,
                        },
                        onDeleteBehaviour: 'restrict',
                        isDeferrable: true,
                    },
                    {
                        name: 'upgrade_update_columns_self_ref_to_nullable_fk',
                        targetTable: 'upgrade_update_columns',
                        targetColumnNames: [],
                        columnNames: ['self_ref_to_nullable'],
                        comment: {
                            targetTableName: 'upgrade_update_columns',
                            columns: { _tenant_id: '_tenant_id', self_ref_to_nullable: '_id' },
                            onDeleteBehaviour: 'noAction',
                            isDeferrable: true,
                        },
                        onDeleteBehaviour: 'noAction',
                        isDeferrable: true,
                    },
                ],
                indexes: [
                    {
                        name: 'upgrade_update_columns_ind0',
                        isUnique: true,
                        columns: [{ name: 'name_1', ascending: true }],
                    },
                    {
                        name: 'upgrade_update_columns_ind1',
                        isUnique: false,
                        columns: [
                            { name: 'name_1', ascending: true },
                            { name: 'value_2', ascending: true },
                        ],
                    },
                    {
                        name: 'upgrade_update_columns_ind2',
                        isUnique: true,
                        columns: [
                            { name: 'name_1', ascending: false },
                            { name: 'value_2', ascending: true },
                            { name: 'value_3', ascending: true },
                        ],
                    },
                    {
                        name: 'upgrade_update_columns_ind3',
                        isUnique: false,
                        columns: [
                            { name: 'name_1', ascending: true },
                            { name: 'value_2', ascending: true },
                            { name: 'value_3', ascending: false },
                        ],
                    },
                ],
            },
            data: [
                {
                    name_1: 'Hagenes Inc',
                    value_2: 2,
                    value_3: 0,
                    self_ref_to_non_nullable: 1,
                    self_ref_to_nullable: 1,
                },
                {
                    name_1: 'Kemmer-Hagenes',
                    value_2: 6,
                    value_3: 0,
                    self_ref_to_non_nullable: 1,
                    self_ref_to_nullable: 1,
                },
                {
                    name_1: 'Littel LLC',
                    value_2: 1,
                    value_3: 0,
                    self_ref_to_non_nullable: 1,
                    self_ref_to_nullable: 1,
                },
                {
                    name_1: 'MacGyver-Pollich',
                    value_2: 4,
                    value_3: 0,
                    self_ref_to_non_nullable: 1,
                    self_ref_to_nullable: 1,
                },
                {
                    name_1: 'Steuber and Sons',
                    value_2: 3,
                    value_3: 0,
                    self_ref_to_non_nullable: 1,
                    self_ref_to_nullable: 1,
                },
                {
                    name_1: 'Will Group',
                    value_2: 5,
                    value_3: 0,
                    self_ref_to_non_nullable: 1,
                    self_ref_to_nullable: 1,
                },
            ],
            orderBy: ['name_1'],
        });

        // Check upgrade_rename_table_v_2 table
        // The table upgrade_rename_table_v_1 was renamed to upgrade_rename_table_v_2
        // The column upgrade_rename_table_v_1.name was renamed to upgrade_rename_table_v_2.name_2
        await checkTable(context, {
            tableDef: {
                schemaName: upgradeSchemaName,
                tableName: 'upgrade_rename_table_v_2',
                columns: [
                    {
                        name: 'name_2',
                        isNullable: false,
                        type: 'string',
                        default: '',
                        comment: { type: 'string', isSystem: false, maxLength: 250 },
                    },
                    {
                        name: 'ref',
                        isNullable: false,
                        type: 'integer',
                        comment: {
                            type: 'reference',
                            isSystem: false,
                            targetTableName: 'upgrade_referenced',
                            isSelfReference: false,
                        },
                    },
                ],
                comment: { isSharedByAllTenants: false },
                indexes: [
                    {
                        name: 'upgrade_rename_table_v_2_ind0',
                        isUnique: true,
                        columns: [{ name: 'name_2', ascending: true }],
                    },
                ],
                foreignKeys: [
                    {
                        // Make sure the foreign key was renamed as well
                        name: 'upgrade_rename_table_v_2_ref_fk',
                        targetTable: 'upgrade_referenced',
                        targetColumnNames: [],
                        columnNames: ['ref'],
                        comment: {
                            targetTableName: 'upgrade_referenced',
                            columns: { _tenant_id: '_tenant_id', ref: '_id' },
                            onDeleteBehaviour: 'restrict',
                            isDeferrable: true,
                        },
                        onDeleteBehaviour: 'restrict',
                        isDeferrable: true,
                    },
                ],
            },
            data: [
                { name_2: 'Hagenes Inc', ref: 1 },
                { name_2: 'Kemmer-Hagenes', ref: 1 },
                { name_2: 'Littel LLC', ref: 1 },
                { name_2: 'MacGyver-Pollich', ref: 1 },
                { name_2: 'Steuber and Sons', ref: 1 },
                { name_2: 'Will Group', ref: 1 },
            ],
            orderBy: ['name_2'],
        });
        // platform/upgrade-test/xtrem-upgrade-test-v2-base/lib/upgrades/vlatest/rename-table-schema.ts
        await checkTableDropped(context, 'upgrade_rename_table_v_1');

        // Check upgrade_rename_table_with_notify_v_2 table
        // The table upgrade_rename_table_with_notify_v_1 was renamed to upgrade_rename_table_with_notify_v_2
        await checkTable(context, {
            tableDef: {
                schemaName: upgradeSchemaName,
                tableName: 'upgrade_rename_table_with_notify_v_2',
                columns: [
                    {
                        name: 'name',
                        isNullable: false,
                        type: 'string',
                        default: '',
                        comment: { type: 'string', isSystem: false, maxLength: 250 },
                    },
                ],
                comment: {
                    isSharedByAllTenants: false,
                },
                triggers: [],
            },
            data: [],
        });
        // platform/upgrade-test/xtrem-upgrade-test-v2-base/lib/upgrades/vlatest/rename-table-schema.ts
        await checkTableDropped(context, 'upgrade_rename_table_with_notify_v_1');

        await checkTable(context, {
            tableDef: {
                schemaName: upgradeSchemaName,
                tableName: 'upgrade_notify_updated',
                columns: [
                    {
                        name: 'name',
                        isNullable: false,
                        type: 'string',
                        default: '',
                        comment: { type: 'string', isSystem: false, maxLength: 250 },
                    },
                ],
                comment: { isSharedByAllTenants: false },
                triggers: [],
            },
            data: [],
        });

        // The table 'upgrade_custom_sql_v_2' should have been created from the table 'upgrade_custom_sql_v_1'
        // platform/upgrade-test/xtrem-upgrade-test-v2-base/lib/upgrades/vlatest/custom-sql-action-v1-to-v2.ts
        await checkTable(context, {
            tableDef: {
                schemaName: upgradeSchemaName,
                tableName: 'upgrade_custom_sql_v_2',
                columns: [
                    {
                        name: 'string_2',
                        isNullable: false,
                        type: 'string',
                        default: '',
                        comment: { type: 'string', isSystem: false, maxLength: 250 },
                    },
                    {
                        name: 'int_2',
                        isNullable: false,
                        type: 'integer',
                        comment: { type: 'integer', isSystem: false },
                    },
                    {
                        name: 'bool_2',
                        isNullable: false,
                        type: 'boolean',
                        comment: { type: 'boolean', isSystem: false },
                    },
                ],
                comment: { isSharedByAllTenants: false },
            },
            data: [
                { string_2: 'V2aaa', int_2: 2, bool_2: false },
                { string_2: 'V2bbb', int_2: 4, bool_2: true },
                { string_2: 'V2ccc', int_2: 6, bool_2: false },
                { string_2: 'V2ddd', int_2: 8, bool_2: true },
                { string_2: 'V2eee', int_2: 10, bool_2: false },
                { string_2: 'V2fff', int_2: 12, bool_2: true },
            ],
            orderBy: ['string_2'],
        });
        // platform/upgrade-test/xtrem-upgrade-test-v2-base/lib/upgrades/vlatest/index.ts
        await checkTableDropped(context, 'upgrade_custom_sql_v_1');

        // The table 'upgrade_custom_sql_updated' should have been updated by
        // the CustomSqlAction 'custom-sql-action-updated'
        await checkTable(context, {
            tableDef: {
                schemaName: upgradeSchemaName,
                tableName: 'upgrade_custom_sql_updated',
                columns: [
                    {
                        name: 'str_prop',
                        isNullable: false,
                        type: 'string',
                        default: '',
                        comment: { type: 'string', isSystem: false, maxLength: 250 },
                    },
                    {
                        name: 'int_prop',
                        isNullable: false,
                        type: 'integer',
                        comment: { type: 'integer', isSystem: false },
                    },
                    {
                        name: 'bool_prop',
                        isNullable: false,
                        type: 'boolean',
                        comment: { type: 'boolean', isSystem: false },
                    },
                    {
                        name: 'ref_prop',
                        isNullable: false,
                        type: 'integer',
                        comment: {
                            type: 'reference',
                            isSystem: false,
                            targetTableName: 'upgrade_custom_sql_v_2',
                            isSelfReference: false,
                        },
                    },
                ],
                comment: { isSharedByAllTenants: false },
                foreignKeys: [
                    {
                        name: 'upgrade_custom_sql_updated_ref_prop_fk',
                        targetTable: 'upgrade_custom_sql_v_2',
                        targetColumnNames: [],
                        columnNames: ['ref_prop'],
                        comment: {
                            targetTableName: 'upgrade_custom_sql_v_2',
                            columns: { _tenant_id: '_tenant_id', ref_prop: '_id' },
                            onDeleteBehaviour: 'restrict',
                            isDeferrable: true,
                        },
                        onDeleteBehaviour: 'restrict',
                        isDeferrable: true,
                    },
                ],
            },
            data: [
                { str_prop: 'updated_aaa', int_prop: 3, bool_prop: false, ref_prop: 6 },
                { str_prop: 'updated_bbb', int_prop: 6, bool_prop: true, ref_prop: 7 },
                { str_prop: 'updated_ccc', int_prop: 9, bool_prop: false, ref_prop: 8 },
                { str_prop: 'updated_ddd', int_prop: 12, bool_prop: true, ref_prop: 9 },
                { str_prop: 'updated_eee', int_prop: 15, bool_prop: false, ref_prop: 10 },
                { str_prop: 'updated_fff', int_prop: 18, bool_prop: true, ref_prop: 11 },
            ],
            orderBy: ['str_prop'],
        });

        // The FK from table 'upgrade_ref_to_custom_sql_updated' should have been
        // kept by the CustomSqlAction 'custom-sql-action-updated'
        await checkTable(context, {
            tableDef: {
                schemaName: upgradeSchemaName,
                tableName: 'upgrade_ref_to_custom_sql_updated',
                columns: [
                    {
                        name: 'ref',
                        isNullable: false,
                        type: 'integer',
                        comment: {
                            type: 'reference',
                            isSystem: false,
                            targetTableName: 'upgrade_custom_sql_updated',
                            isSelfReference: false,
                        },
                    },
                ],
                comment: {
                    isSharedByAllTenants: false,
                },
                foreignKeys: [
                    {
                        name: 'upgrade_ref_to_custom_sql_updated_ref_fk',
                        targetTable: 'upgrade_custom_sql_updated',
                        targetColumnNames: [],
                        columnNames: ['ref'],
                        comment: {
                            targetTableName: 'upgrade_custom_sql_updated',
                            columns: { _tenant_id: '_tenant_id', ref: '_id' },
                            onDeleteBehaviour: 'restrict',
                            isDeferrable: true,
                        },
                        onDeleteBehaviour: 'restrict',
                        isDeferrable: true,
                    },
                ],
            },
            data: [{ ref: 1 }, { ref: 2 }, { ref: 3 }, { ref: 4 }, { ref: 5 }, { ref: 6 }],
            orderBy: ['ref'],
        });
        // check enum upgrade
        await checkTable(context, {
            tableDef: {
                schemaName: upgradeSchemaName,
                tableName: 'upgrade_rename_enum',
                columns: [
                    {
                        name: 'enum_test',
                        type: 'enum',
                        isNullable: true,
                        enumDataType: {
                            name: 'test_upgrade_enum_type_enum',
                            values: {},
                        },
                        comment: {
                            type: 'enum',
                            isSystem: false,
                            enumTypeName: 'test_upgrade_enum_type_enum',
                        },
                    },
                    {
                        name: 'enum_test_datatype',
                        type: 'enum',
                        isNullable: false,
                        enumDataType: {
                            name: 'test_upgrade_enum_change_datatype_type_enum',
                            values: {},
                        },
                        comment: {
                            type: 'enum',
                            isSystem: false,
                            enumTypeName: 'test_upgrade_enum_change_datatype_type_enum',
                        },
                    },
                    {
                        name: 'new_enum_prop',
                        type: 'enum',
                        isNullable: false,
                        enumDataType: {
                            name: 'test_upgrade_enum_new_prop_type_enum',
                            values: {},
                        },
                        comment: {
                            type: 'enum',
                            isSystem: false,
                            enumTypeName: 'test_upgrade_enum_new_prop_type_enum',
                        },
                    },
                ],
                comment: { isSharedByAllTenants: false },
            },
            data: [
                { enum_test: 'value1V2', enum_test_datatype: 'value1V2Datatype', new_enum_prop: 'value1' },
                { enum_test: 'value1V2', enum_test_datatype: 'value1V2Datatype', new_enum_prop: 'value1' },
                { enum_test: 'value2V2', enum_test_datatype: 'value1V2Datatype', new_enum_prop: 'value1' },
                { enum_test: 'value3V2', enum_test_datatype: 'value1V2Datatype', new_enum_prop: 'value1' },
                { enum_test: 'value6V1', enum_test_datatype: 'value1V2Datatype', new_enum_prop: 'value1' },
                { enum_test: 'value6V1', enum_test_datatype: 'value1V2Datatype', new_enum_prop: 'value1' },
                { enum_test: null, enum_test_datatype: 'value1V2Datatype', new_enum_prop: 'value1' },
            ],
            orderBy: ['enum_test'],
        });

        await checkTable(context, {
            tableDef: {
                schemaName: upgradeSchemaName,
                tableName: 'upgrade_alter_columns',
                columns: [
                    {
                        name: 'string_to_ref',
                        type: 'integer',
                        isNullable: false,
                        comment: {
                            type: 'reference',
                            isSystem: false,
                            targetTableName: 'upgrade_referenced',
                            isSelfReference: false,
                        },
                    },
                    {
                        name: 'string_to_longer_string',
                        type: 'string',
                        default: '',
                        isNullable: false,
                        maxLength: 250,
                        comment: { type: 'string', isSystem: false, maxLength: 250 },
                    },
                    {
                        name: 'string_to_shorter_string',
                        type: 'string',
                        default: '',
                        isNullable: false,
                        maxLength: 7,
                        comment: { type: 'string', isSystem: false, maxLength: 7 },
                    },
                    // note: the length of the 'stringToPassword' property is 100 but the
                    // size of the column is 233 - encrypted values are longer : 1+2*(len+16)
                    {
                        name: 'string_to_password',
                        type: 'string',
                        default: '',
                        isNullable: false,
                        maxLength: 233,
                        comment: { type: 'string', isSystem: false, isEncrypted: true, maxLength: 100 },
                    },
                    {
                        name: 'password_to_password',
                        type: 'string',
                        default: '',
                        isNullable: false,
                        maxLength: 133,
                        comment: { type: 'string', isSystem: false, isEncrypted: true, maxLength: 50 },
                    },
                    {
                        name: 'string_to_text_stream',
                        type: 'textStream',
                        isNullable: false,
                        comment: { type: 'textStream', isSystem: false },
                    },
                    {
                        name: 'string_to_localized_string',
                        type: 'json',
                        isNullable: false,
                        comment: { type: 'string', isSystem: false, isLocalized: true, maxLength: 50 },
                    },
                    {
                        name: 'localized_string_to_string',
                        type: 'string',
                        default: '',
                        isNullable: false,
                        comment: { type: 'string', isSystem: false, maxLength: 50 },
                    },
                    {
                        name: 'string_to_integer',
                        type: 'integer',
                        isNullable: false,
                        comment: { type: 'integer', isSystem: false },
                    },
                    {
                        name: 'integer_to_decimal',
                        type: 'decimal',
                        isNullable: false,
                        comment: { type: 'decimal', precision: 9, scale: 3, isSystem: false },
                    },
                    {
                        name: 'integer_to_reference',
                        type: 'integer',
                        isNullable: false,
                        comment: {
                            type: 'reference',
                            isSystem: false,
                            targetTableName: 'upgrade_referenced',
                            isSelfReference: false,
                        },
                    },
                    {
                        name: 'integer_to_string',
                        isNullable: false,
                        type: 'string',
                        default: '',
                        comment: { type: 'string', isSystem: false, maxLength: 50 },
                    },
                    {
                        name: 'datetime_to_date',
                        isNullable: true,
                        type: 'date',
                        comment: { type: 'date', isSystem: false },
                    },
                    {
                        name: 'date_to_datetime',
                        isNullable: true,
                        type: 'datetime',
                        comment: { type: 'datetime', isSystem: false },
                    },
                    {
                        name: 'renamed_enum',
                        isNullable: false,
                        type: 'enum',
                        enumDataType: { name: 'upgrade_renamed_enum_v_2_type_enum', values: {} },
                        comment: {
                            type: 'enum',
                            isSystem: false,
                            enumTypeName: 'upgrade_renamed_enum_v_2_type_enum',
                        },
                    },
                ],
                comment: { isSharedByAllTenants: false },

                foreignKeys: [
                    {
                        name: 'upgrade_alter_columns_string_to_ref_fk',
                        targetTable: 'upgrade_referenced',
                        targetColumnNames: [],
                        columnNames: ['string_to_ref'],
                        comment: {
                            targetTableName: 'upgrade_referenced',
                            columns: { _tenant_id: '_tenant_id', string_to_ref: '_id' },
                            onDeleteBehaviour: 'restrict',
                            isDeferrable: true,
                        },
                        onDeleteBehaviour: 'restrict',
                        isDeferrable: true,
                    },
                    {
                        name: 'upgrade_alter_columns_integer_to_reference_fk',
                        targetTable: 'upgrade_referenced',
                        targetColumnNames: [],
                        columnNames: ['integer_to_reference'],
                        comment: {
                            targetTableName: 'upgrade_referenced',
                            columns: { _tenant_id: '_tenant_id', integer_to_reference: '_id' },
                            onDeleteBehaviour: 'restrict',
                            isDeferrable: true,
                        },
                        onDeleteBehaviour: 'restrict',
                        isDeferrable: true,
                    },
                ],
            },
            data: [
                {
                    string_to_ref: 1,
                    string_to_longer_string: 'strVal1',
                    string_to_shorter_string: 'string1',
                    string_to_password: '',
                    password_to_password: '',
                    string_to_text_stream: 'stringToTextStream1',
                    string_to_localized_string: { en: 'stringToLocalizedString1' },
                    localized_string_to_string: 'localizedStringToString1',
                    integer_to_decimal: '1.0000000000',
                    integer_to_reference: 3,
                    string_to_integer: 0,
                    integer_to_string: '10',
                    datetime_to_date: '2000-01-02', // Was 2000-01-02T03:04:05.000Z in the csv
                    date_to_datetime: new Date('2001-02-03T00:00:00.000Z'), // was 2001-02-03 in the csv
                    renamed_enum: 'value1V2', // See upgrade-renamed-enum-v1-to-v2.ts for details
                },
                {
                    string_to_ref: 1,
                    string_to_longer_string: 'strVal2',
                    string_to_shorter_string: 'string2',
                    string_to_password: '',
                    password_to_password: '',
                    string_to_text_stream: 'stringToTextStream2',
                    string_to_localized_string: { en: 'stringToLocalizedString2\\n escaped LF' },
                    localized_string_to_string: 'localizedStringToString2\\n escaped LF',
                    integer_to_decimal: '2.0000000000',
                    integer_to_reference: 3,
                    string_to_integer: 1,
                    integer_to_string: '20',
                    datetime_to_date: '2002-03-04', // Was 2002-03-04T05:06:07.000Z in the csv
                    date_to_datetime: new Date('2003-04-05T00:00:00.000Z'), // was 2003-04-05 in the csv
                    renamed_enum: 'value4V2', // See upgrade-renamed-enum-v1-to-v2.ts for details
                },
                {
                    string_to_ref: 1,
                    string_to_longer_string: 'strVal3',
                    string_to_shorter_string: 'string3',
                    string_to_password: '',
                    password_to_password: '',
                    string_to_text_stream: 'stringToTextStream3',
                    string_to_localized_string: { en: 'stringToLocalizedString3\nLF' },
                    localized_string_to_string: 'localizedStringToString3\nLF',
                    integer_to_decimal: '3.0000000000',
                    integer_to_reference: 3,
                    string_to_integer: 2,
                    integer_to_string: '30',
                    datetime_to_date: '2004-05-06', // Was 2004-05-06T07:08:09.000Z in the csv
                    date_to_datetime: new Date('2005-06-07T00:00:00.000Z'), // was 2005-06-07 in the csv
                    renamed_enum: 'value3V1', // See upgrade-renamed-enum-v1-to-v2.ts for details
                },
                {
                    string_to_ref: 1,
                    string_to_longer_string: 'strVal4',
                    string_to_shorter_string: 'string4',
                    string_to_password: '',
                    password_to_password: '',
                    string_to_text_stream: 'stringToTextStream4',
                    string_to_localized_string: { en: 'stringToLocalizedString4' },
                    localized_string_to_string: 'localizedStringToString4',
                    integer_to_decimal: '4.0000000000',
                    integer_to_reference: 3,
                    string_to_integer: 3,
                    integer_to_string: '40',
                    datetime_to_date: '2006-07-08', // Was 2006-07-08T09:10:11.000Z in the csv
                    date_to_datetime: new Date('2007-08-09T00:00:00.000Z'), // was 2007-08-09 in the csv
                    renamed_enum: 'value1V2', // See upgrade-renamed-enum-v1-to-v2.ts for details
                },
                {
                    string_to_ref: 1,
                    string_to_longer_string: 'strVal5',
                    string_to_shorter_string: 'string5',
                    string_to_password: '',
                    password_to_password: '',
                    string_to_text_stream: 'stringToTextStream5',
                    string_to_localized_string: { en: 'stringToLocalizedString5' },
                    localized_string_to_string: 'localizedStringToString5',
                    integer_to_decimal: '5.0000000000',
                    integer_to_reference: 3,
                    string_to_integer: 4,
                    integer_to_string: '50',
                    datetime_to_date: '2008-09-10', // Was 2008-09-10T11:12:13.000Z in the csv
                    date_to_datetime: new Date('2009-10-11T00:00:00.000Z'), // was 2009-10-11 in the csv
                    renamed_enum: 'value4V2', // See upgrade-renamed-enum-v1-to-v2.ts for details
                },
                {
                    string_to_ref: 1,
                    string_to_longer_string: 'strVal6',
                    string_to_shorter_string: 'string6',
                    string_to_password: '',
                    password_to_password: '',
                    string_to_text_stream: 'stringToTextStream6',
                    string_to_localized_string: { en: 'stringToLocalizedString6' },
                    localized_string_to_string: 'localizedStringToString6',
                    integer_to_decimal: '6.0000000000',
                    integer_to_reference: 3,
                    string_to_integer: 5,
                    integer_to_string: '60',
                    datetime_to_date: '2010-11-12', // Was 2010-11-12T13:14:15.000Z in the csv
                    date_to_datetime: new Date('2011-12-13T00:00:00.000Z'), // was 2011-12-13 in the csv
                    renamed_enum: 'value3V1', // See upgrade-renamed-enum-v1-to-v2.ts for details
                },
            ],
            orderBy: ['string_to_longer_string'],
        });

        // check upgrade of node-extensions
        await checkTable(context, {
            tableDef: {
                schemaName: 'xtrem_core_test',
                tableName: 'upgrade_node_to_extend',
                columns: [
                    {
                        name: 'name_1',
                        isNullable: false,
                        type: 'string',
                        default: '',
                        comment: { type: 'string', isSystem: false, maxLength: 250 },
                    },
                    {
                        name: 'name_2',
                        isNullable: false,
                        type: 'string',
                        default: '',
                        comment: { type: 'string', isSystem: false, maxLength: 250 },
                    },
                ],
                comment: { isSharedByAllTenants: false },
            },
            data: [
                { name_1: 'Hagenes Inc', name_2: 'HAGENES INC' },
                { name_1: 'Kemmer-Hagenes', name_2: 'KEMMER-HAGENES' },
                { name_1: 'Littel LLC', name_2: 'LITTEL LLC' },
                { name_1: 'MacGyver-Pollich', name_2: 'MACGYVER-POLLICH' },
                { name_1: 'Steuber and Sons', name_2: 'STEUBER AND SONS' },
                { name_1: 'Will Group', name_2: 'WILL GROUP' },
            ],
            orderBy: ['name_1'],
        });

        await checkTable(context, {
            tableDef: {
                schemaName: upgradeSchemaName,
                tableName: 'upgrade_change_base_node',
                columns: [
                    {
                        name: 'str_prop',
                        isNullable: false,
                        type: 'string',
                        default: '',
                        comment: { type: 'string', isSystem: false, maxLength: 250 },
                    },
                ],
                comment: {
                    isSharedByAllTenants: false,
                    baseTable: 'upgrade_abstract_node_2',
                    rootTable: 'upgrade_abstract_node_2',
                },

                foreignKeys: [
                    {
                        name: 'upgrade_change_base_node__id_fk',
                        targetTable: 'upgrade_abstract_node_2',
                        targetColumnNames: [],
                        columnNames: [],
                        comment: {
                            targetTableName: 'upgrade_abstract_node_2',
                            columns: { _tenant_id: '_tenant_id', _id: '_id' },
                            onDeleteBehaviour: 'cascade',
                            isDeferrable: true,
                        },
                        onDeleteBehaviour: 'cascade',
                        isDeferrable: true,
                    },
                ],
                triggers: [
                    {
                        name: 'base_delete',
                        when: 'AFTER',
                        event: 'DELETE',
                        functionName: `${upgradeSchemaName}.upgrade_change_base_node_base_delete`,
                        functionParameters: '',
                    },
                ],
            },
            data: [
                { str_prop: 'str_prop_1' },
                { str_prop: 'str_prop_2' },
                { str_prop: 'str_prop_1' },
                { str_prop: 'str_prop_2' },
                { str_prop: 'str_prop_3' },
            ],
        });
        // platform/upgrade-test/xtrem-upgrade-test-v2-base/lib/upgrades/vlatest/index.ts
        await checkTableDropped(context, 'upgrade_abstract_node_1');

        // Check upgrade on baseNodes/subNodes : baseNode
        await checkTable(context, {
            tableDef: {
                schemaName: upgradeSchemaName,
                tableName: 'upgrade_base_node',
                columns: [
                    {
                        name: '_constructor',
                        isNullable: false,
                        type: 'string',
                        default: '',
                        comment: { type: 'string', isSystem: true, maxLength: 100 },
                    },
                    {
                        name: 'str_base',
                        isNullable: false,
                        type: 'string',
                        default: '',
                        comment: { type: 'string', isSystem: false, maxLength: 250 },
                    },
                    {
                        name: 'int_base',
                        isNullable: false,
                        type: 'integer',
                        comment: { type: 'integer', isSystem: false },
                    },
                ],
                comment: { isSharedByAllTenants: false },
            },
            // int_base has been set by action 'custom-sql-action-base-node'
            // Notes:
            // - there should no longer be any record with _constructor = 'UpgradeDeleteTableWithBaseNode' as the table was deleted
            // _ids [1, 2, 3, 4, 5] come from platform/upgrade-test/xtrem-upgrade-test-v1/data/layers/setup/upgrade-sub-node-1.csv
            // _ids [6,7,8] come from platform/upgrade-test/xtrem-upgrade-test-v1/data/layers/setup/upgrade-sub-node-2.csv
            data: [
                { _id: 1, _constructor: 'UpgradeSubNode1', str_base: 'base name 1', int_base: 3 },
                { _id: 2, _constructor: 'UpgradeSubNode1', str_base: 'base name 2', int_base: 3 },
                { _id: 3, _constructor: 'UpgradeSubNode1', str_base: 'base name 3', int_base: 3 },
                { _id: 4, _constructor: 'UpgradeSubNode1', str_base: 'base name 4', int_base: 3 },
                { _id: 5, _constructor: 'UpgradeSubNode1', str_base: 'base name 5', int_base: 3 },
                { _id: 6, _constructor: 'UpgradeSubNode2', str_base: 'base name 1', int_base: 3 },
                { _id: 7, _constructor: 'UpgradeSubNode2', str_base: 'base name 2', int_base: 3 },
                { _id: 8, _constructor: 'UpgradeSubNode2', str_base: 'base name 3', int_base: 3 },
            ],
            orderBy: ['_id'],
            extraColumnsForDataComparison: ['_id'],
        });

        // Check upgrade on baseNodes/subNodes : subNode1
        await checkTable(context, {
            tableDef: {
                schemaName: upgradeSchemaName,
                tableName: 'upgrade_sub_node_1',
                columns: [
                    {
                        name: 'str_sub_1',
                        isNullable: false,
                        type: 'string',
                        default: '',
                        comment: { type: 'string', isSystem: false, maxLength: 250 },
                    },
                    {
                        name: 'int_sub_1',
                        isNullable: false,
                        type: 'integer',
                        comment: { type: 'integer', isSystem: false },
                    },
                ],
                comment: {
                    isSharedByAllTenants: false,
                    baseTable: 'upgrade_base_node',

                    rootTable: 'upgrade_base_node',
                },

                foreignKeys: [
                    {
                        name: 'upgrade_sub_node_1__id_fk',
                        targetTable: 'upgrade_base_node',
                        targetColumnNames: [],
                        columnNames: [],
                        comment: {
                            targetTableName: 'upgrade_base_node',
                            columns: { _tenant_id: '_tenant_id', _id: '_id' },
                            onDeleteBehaviour: 'cascade',
                            isDeferrable: true,
                        },
                        onDeleteBehaviour: 'cascade',
                        isDeferrable: true,
                    },
                ],
                triggers: [
                    {
                        name: 'base_delete',
                        when: 'AFTER',
                        event: 'DELETE',
                        functionName: `${upgradeSchemaName}.upgrade_sub_node_1_base_delete`,
                        functionParameters: '',
                    },
                ],
            },
            // int_sub_1 has been set by action 'custom-sql-action-sub-node-1'
            data: [
                { str_sub_1: 'sub name 1.1', int_sub_1: 1 },
                { str_sub_1: 'sub name 1.2', int_sub_1: 1 },
                { str_sub_1: 'sub name 1.3', int_sub_1: 1 },
                { str_sub_1: 'sub name 1.4', int_sub_1: 1 },
                { str_sub_1: 'sub name 1.5', int_sub_1: 1 },
            ],
            orderBy: ['str_sub_1'],
        });

        // Check upgrade on baseNodes/subNodes : subNode2
        await checkTable(context, {
            tableDef: {
                schemaName: upgradeSchemaName,
                tableName: 'upgrade_sub_node_2',
                columns: [
                    {
                        name: 'str_sub_2',
                        isNullable: false,
                        type: 'string',
                        default: '',
                        comment: { type: 'string', isSystem: false, maxLength: 250 },
                    },
                    {
                        name: 'int_sub_2',
                        isNullable: false,
                        type: 'integer',
                        comment: { type: 'integer', isSystem: false },
                    },
                ],
                comment: {
                    isSharedByAllTenants: false,
                    baseTable: 'upgrade_base_node',

                    rootTable: 'upgrade_base_node',
                },

                foreignKeys: [
                    {
                        name: 'upgrade_sub_node_2__id_fk',
                        targetTable: 'upgrade_base_node',
                        targetColumnNames: [],
                        columnNames: [],
                        comment: {
                            targetTableName: 'upgrade_base_node',
                            columns: { _tenant_id: '_tenant_id', _id: '_id' },
                            onDeleteBehaviour: 'cascade',
                            isDeferrable: true,
                        },
                        onDeleteBehaviour: 'cascade',
                        isDeferrable: true,
                    },
                ],
                triggers: [
                    {
                        name: 'base_delete',
                        when: 'AFTER',
                        event: 'DELETE',
                        functionName: `${upgradeSchemaName}.upgrade_sub_node_2_base_delete`,
                        functionParameters: '',
                    },
                ],
            },
            // int_sub_2 has been set by action 'custom-sql-action-sub-node-2'
            data: [
                { str_sub_2: 'sub name 2.1', int_sub_2: 2 },
                { str_sub_2: 'sub name 2.2', int_sub_2: 2 },
                { str_sub_2: 'sub name 2.3', int_sub_2: 2 },
            ],
            orderBy: ['str_sub_2'],
        });

        // Check that the renaming of table upgrade_rename_table_with_attachments_1 to upgrade_rename_table_with_attachments_2
        await checkTable(context, {
            tableDef: {
                schemaName: upgradeSchemaName,
                tableName: 'upgrade_rename_table_with_attachments_2',
            },
            // Note: here, we just check that the table was successfuly renamed
        });
        await checkTableDropped(context, 'upgrade_rename_table_with_attachments_1');

        // platform/upgrade-test/xtrem-upgrade-test-v2-base/lib/upgrades/vlatest/drop-upgrade-delete-table-with-attachments.ts
        await checkTableDropped(context, 'upgrade_delete_table_with_attachments');

        // platform/upgrade-test/xtrem-upgrade-test-v2-base/lib/upgrades/vlatest/drop-upgrade-delete-table-with-base-node.ts
        await checkTableDropped(context, 'upgrade_delete_table_with_base_node');

        // Check upgrade on upgrade_reload_from_csv: data should have been updated by
        // reloading platform/upgrade-test/xtrem-upgrade-test-v2/data/layers/setup/upgrade-reload-from-csv.csv
        await checkTable(context, {
            hasVendor: true,
            tableDef: {
                schemaName: upgradeSchemaName,
                tableName: 'upgrade_reload_from_csv',
                columns: [
                    {
                        name: 'name',
                        isNullable: false,
                        type: 'string',
                        default: '',
                        comment: { type: 'string', isSystem: false, maxLength: 250 },
                    },
                    {
                        name: 'val',
                        isNullable: false,
                        type: 'string',
                        default: '',
                        comment: { type: 'string', isSystem: false, maxLength: 250 },
                    },
                ],
                indexes: [
                    {
                        name: 'upgrade_reload_from_csv_ind0',
                        isUnique: true,
                        columns: [{ name: 'name', ascending: true }],
                    },
                ],
                comment: {
                    isSetupNode: true,
                    isSharedByAllTenants: false,
                    naturalKey: ['name'],
                },
            },
            data: [
                { name: 'name1', val: '2.1' }, // Updated because of vendor
                { name: 'name2', val: '1.2' }, // Not updated, because no vendor in CSV
                { name: 'name3', val: '1.3' }, // Not updated, because no vendor in CSV
                { name: 'name4', val: '2.4' }, // Newly inserted by upgrade
            ],
            orderBy: ['name'],
        });

        assert.isFalse(await new EnumSqlContext(context.application).enumTypeExists('upgrade_rename_enum_bis_enum'));

        // Make sure that no changes were applied to upgrade_datatypes table (no changes between V1 / V2)
        await checkTable(context, {
            tableDef: {
                schemaName: upgradeSchemaName,
                tableName: 'upgrade_datatypes',
                columns: [
                    {
                        name: 'id',
                        isNullable: false,
                        type: 'integer',
                        comment: { type: 'integer', isSystem: false },
                    },
                    {
                        name: 'boolean_val',
                        isNullable: true,
                        type: 'boolean',
                        comment: { type: 'boolean', isSystem: false },
                    },
                    {
                        name: 'short_val',
                        isNullable: true,
                        type: 'short',
                        comment: { type: 'short', isSystem: false },
                    },
                    {
                        name: 'integer_val',
                        isNullable: true,
                        type: 'integer',
                        comment: { type: 'integer', isSystem: false },
                    },
                    {
                        name: 'integer_range_val',
                        isNullable: true,
                        type: 'integerRange',
                        comment: { type: 'integerRange', isSystem: false },
                    },
                    {
                        name: 'decimal_range_val',
                        isNullable: true,
                        type: 'decimalRange',
                        comment: { type: 'decimalRange', isSystem: false },
                    },
                    {
                        name: 'enum_val',
                        isNullable: true,
                        type: 'enum',
                        enumDataType: { name: 'upgrade_datatypes_enum_type_enum', values: {} },
                        comment: {
                            type: 'enum',
                            isSystem: false,
                            enumTypeName: 'upgrade_datatypes_enum_type_enum',
                        },
                    },
                    {
                        name: 'string_val',
                        isNullable: false,
                        type: 'string',
                        default: '',
                        comment: { type: 'string', isSystem: false, maxLength: 250 },
                    },
                    {
                        name: 'decimal_val',
                        isNullable: false,
                        type: 'decimal',
                        comment: { type: 'decimal', precision: 9, scale: 3, isSystem: false },
                    },
                    {
                        name: 'float_val',
                        isNullable: true,
                        type: 'float',
                        comment: { type: 'float', isSystem: false },
                    },
                    {
                        name: 'double_val',
                        isNullable: true,
                        type: 'double',
                        comment: { type: 'double', isSystem: false },
                    },
                    {
                        name: 'date_val',
                        isNullable: true,
                        type: 'date',
                        comment: { type: 'date', isSystem: false },
                    },
                    {
                        name: 'date_range_val',
                        isNullable: true,
                        type: 'dateRange',
                        comment: { type: 'dateRange', isSystem: false },
                    },
                    {
                        name: 'datetime_range_val',
                        isNullable: true,
                        type: 'datetimeRange',
                        comment: { type: 'datetimeRange', isSystem: false },
                    },
                    {
                        name: 'time_val',
                        isNullable: true,
                        type: 'time',
                        comment: { type: 'time', isSystem: false },
                    },
                    {
                        name: 'datetime_val',
                        isNullable: true,
                        type: 'datetime',
                        comment: { type: 'datetime', isSystem: false },
                    },
                    {
                        name: 'binary_stream',
                        isNullable: true,
                        type: 'binary',
                        comment: { type: 'binaryStream', isSystem: false },
                    },
                    {
                        name: 'text_stream',
                        isNullable: false,
                        type: 'textStream',
                        comment: { type: 'textStream', isSystem: false },
                    },
                    {
                        name: 'mail_template',
                        isNullable: false,
                        type: 'textStream',
                        comment: { type: 'textStream', isSystem: false },
                    },
                    {
                        name: 'uuid_val',
                        isNullable: true,
                        type: 'uuid',
                        comment: { type: 'uuid', isSystem: false },
                    },
                    {
                        name: 'json_val',
                        isNullable: true,
                        type: 'json',
                        comment: { type: 'json', isSystem: false },
                    },
                    {
                        name: 'integer_array_val',
                        isNullable: true,
                        type: 'integerArray',
                        comment: { type: 'integerArray', isSystem: false },
                    },
                    {
                        name: 'enum_array_val',
                        isNullable: true,
                        type: 'enumArray',
                        enumDataType: { name: '_upgrade_datatypes_enum_for_array_type_enum', values: {} },
                        comment: {
                            type: 'enumArray',
                            isSystem: false,
                            enumTypeName: 'upgrade_datatypes_enum_for_array_type_enum',
                        },
                    },
                    {
                        name: 'string_array_val',
                        isNullable: false,
                        type: 'stringArray',
                        comment: { type: 'stringArray', isSystem: false },
                    },
                ],
                comment: { isSharedByAllTenants: false },

                foreignKeys: [],
            },
            data: [],
        });

        assert.strictEqual(
            await new EnumSqlContext(context.application).getEnumAttributes('test_upgrade_enum_type_enum'),
            '{value1V2,value2V2,value21V2,value3V2,value31V2,value6V1}',
        );

        // check upgrade csv with factory
        await checkTable(context, {
            hasVendor: true,
            tableDef: {
                schemaName: upgradeSchemaName,
                tableName: 'upgrade_reload_from_vendor_csv',
                columns: [
                    {
                        name: 'id',
                        type: 'string',
                        default: '',
                        isNullable: false,
                        comment: { type: 'string', isSystem: false, maxLength: 10 },
                    },
                    {
                        name: 'country',
                        type: 'string',
                        default: '',
                        isNullable: false,
                        comment: { type: 'string', isSystem: false, maxLength: 250 },
                    },
                    {
                        name: 'is_owned_by_customer_property',
                        type: 'string',
                        default: '',
                        isNullable: false,
                        comment: { type: 'string', isSystem: false, maxLength: 250 },
                    },
                    {
                        name: 'is_owned_by_customer_reference',
                        type: 'integer',
                        default: null,
                        isNullable: true,
                        comment: {
                            isSelfReference: false,
                            targetTableName: 'upgrade_referenced',
                            type: 'reference',
                            isSystem: false,
                        },
                    },
                ],
                comment: {
                    naturalKey: ['id'],
                    isSharedByAllTenants: false,
                    isSetupNode: true,
                },

                indexes: [
                    {
                        name: 'upgrade_reload_from_vendor_csv_ind0',
                        isUnique: true,
                        columns: [{ name: 'id', ascending: true }],
                    },
                ],
                foreignKeys: [
                    {
                        name: 'upgra_db5a1dd0a352170fb22909d7647f061f_by_customer_reference_fk',
                        targetTable: 'upgrade_referenced',
                        targetColumnNames: [],
                        columnNames: ['is_owned_by_customer_reference'],
                        comment: {
                            targetTableName: 'upgrade_referenced',
                            columns: { _tenant_id: '_tenant_id', is_owned_by_customer_reference: '_id' },
                            onDeleteBehaviour: 'noAction',
                            isDeferrable: true,
                        },
                        onDeleteBehaviour: 'noAction',
                        isDeferrable: true,
                    },
                ],
            },
            data: [
                {
                    id: '1',
                    country: 'france', // property was not updated, because vendor in CSV is empty
                    is_owned_by_customer_property: 'euro', // property was not updated by upgrade upsert as it is isOwnedByCustomer:true
                    is_owned_by_customer_reference: 1,
                },
                {
                    id: '2',
                    country: 'usa',
                    is_owned_by_customer_property: 'dollar',
                    is_owned_by_customer_reference: 2,
                },
                {
                    id: '3',
                    country: 'france',
                    is_owned_by_customer_property: 'euro',
                    is_owned_by_customer_reference: 3, // reference value is changed in CSV but is unchanged as it is isOwnedByCustomer:true
                },
            ],
            orderBy: ['id'],
        });

        // check upgrade service options
        await checkTable(context, {
            hasVendor: false,
            doNotCheckTableDef: true,
            tableDef: {
                schemaName: upgradeSchemaName,
                tableName: 'sys_service_option',
                isSharedByAllTenants: true,
                columns: [
                    { name: 'package' },
                    { name: 'option_name' },
                    { name: 'description' },
                    { name: 'status' },
                    { name: 'is_hidden' },
                ],
                comment: { isSharedByAllTenants: true },

                indexes: [],
                foreignKeys: [],
            },
            orderBy: ['option_name'],
            data: [
                {
                    package: '@sage/xtrem-auditing',
                    option_name: 'auditing',
                    description: 'Enable Auditing feature',
                    status: 'released',
                    is_hidden: false,
                },
                {
                    package: '@sage/xtrem-auditing',
                    option_name: 'auditingOption',
                    description: 'Auditing option',
                    status: 'workInProgress',
                    is_hidden: true,
                },
                {
                    package: '@sage/xtrem-authorization',
                    option_name: 'authorizationServiceOption',
                    description: 'Authorization access control',
                    status: 'released',
                    is_hidden: false,
                },
                {
                    package: '@sage/xtrem-system',
                    option_name: 'changelog',
                    description: 'allow to display changelog in the app',
                    status: 'experimental',
                    is_hidden: false,
                },
                {
                    package: '@sage/xtrem-system',
                    option_name: 'devTools',
                    description: 'enable dev-only features',
                    status: 'experimental',
                    is_hidden: false,
                },
                {
                    package: '@sage/xtrem-system',
                    option_name: 'isDemoTenant',
                    description: 'Is a demo tenant',
                    status: 'released',
                    is_hidden: false,
                },
                {
                    package: '@sage/xtrem-communication',
                    option_name: 'notificationCenter',
                    description: 'Notification center',
                    status: 'experimental',
                    is_hidden: false,
                },
                {
                    package: '@sage/xtrem-interop',
                    option_name: 'synchronizationServiceOption',
                    description: 'Synchronization',
                    status: 'released',
                    is_hidden: false,
                },
                {
                    package: '@sage/xtrem-system',
                    option_name: 'sysDeviceToken',
                    description: 'Enable PIN code authentication feature',
                    status: 'released',
                    is_hidden: false,
                },
                {
                    package: '@sage/xtrem-system',
                    option_name: 'tags',
                    description: 'Enable Tags feature',
                    status: 'released',
                    is_hidden: false,
                },
                {
                    package: '@sage/xtrem-upgrade-test-base',
                    option_name: 'upgradeServiceOption1',
                    description: 'upgrade service option 1',
                    status: 'released',
                    is_hidden: true,
                },
                {
                    package: '@sage/xtrem-upgrade-test-base',
                    option_name: 'upgradeServiceOption2',
                    description: 'upgrade service option 2',
                    status: 'released', // updated from experimental to released
                    is_hidden: true,
                },
                {
                    package: '@sage/xtrem-upgrade-test-base',
                    option_name: 'upgradeServiceOption3',
                    description: 'upgrade service option 3',
                    status: 'released',
                    is_hidden: false, // isSubscribable is true so isHidden is changed to false
                },
                {
                    package: '@sage/xtrem-upgrade-test-base',
                    option_name: 'upgradeServiceOption4',
                    description: 'upgrade service option 4',
                    status: 'released',
                    is_hidden: false,
                },
                {
                    package: '@sage/xtrem-upgrade-test-base',
                    option_name: 'upgradeServiceOptionNew1',
                    description: 'upgrade service option new 1',
                    status: 'released',
                    is_hidden: true,
                },
                {
                    package: '@sage/xtrem-upgrade-test-base',
                    option_name: 'upgradeServiceOptionNew2',
                    description: 'upgrade service option new 2',
                    status: 'released',
                    is_hidden: true,
                },
                {
                    package: '@sage/xtrem-workflow',
                    option_name: 'workflow',
                    description: 'Enable Workflow feature',
                    status: 'released',
                    is_hidden: false,
                },
                {
                    package: '@sage/xtrem-workflow',
                    option_name: 'workflowAdvanced',
                    description: 'Workflow advanced features (not yet released)',
                    status: 'workInProgress',
                    is_hidden: false,
                },
                {
                    package: '@sage/xtrem-workflow',
                    option_name: 'workflowOption',
                    description: 'Workflow option (obsolete)',
                    status: 'workInProgress',
                    is_hidden: true,
                },
            ],
        });

        // check upgrade service option states
        await checkTable(context, {
            hasVendor: false,
            doNotCheckTableDef: true,
            tableDef: {
                schemaName: upgradeSchemaName,
                tableName: 'sys_service_option_state',
                columns: [{ name: 'is_activable' }, { name: 'is_active' }, { name: 'service_option' }],
                comment: { isSharedByAllTenants: false },
                indexes: [],
                foreignKeys: [],
            },
            data: [
                {
                    is_activable: true,
                    is_active: false,
                    service_option: 1,
                },
                {
                    is_activable: true,
                    is_active: false,
                    service_option: 2,
                },
                {
                    is_activable: true,
                    is_active: false,
                    service_option: 3,
                },
                {
                    is_activable: true,
                    is_active: false,
                    service_option: 4, // sysTag
                },
                {
                    is_activable: true,
                    is_active: false,
                    service_option: 5, // NotificationCenter
                },
                {
                    is_activable: true,
                    is_active: false,
                    service_option: 6, // changelog
                },
                {
                    is_activable: true,
                    is_active: false,
                    service_option: 7, // synchronizationServiceOption
                },
                {
                    is_activable: true,
                    is_active: false,
                    service_option: 8,
                },
                {
                    is_activable: true,
                    is_active: false,
                    service_option: 9,
                },
                {
                    is_activable: true,
                    is_active: false,
                    service_option: 10,
                },
                {
                    is_activable: true,
                    is_active: false,
                    service_option: 11,
                },
                {
                    is_activable: false,
                    is_active: false,
                    service_option: 12,
                },
                {
                    is_activable: true,
                    is_active: true,
                    service_option: 13, // upgradeServiceOptionNew1 is activated by it's parent upgradeServiceOption4
                },
                {
                    is_activable: true,
                    is_active: true,
                    service_option: 14, // upgradeServiceOptionNew2 state is loaded by active fu to isActiveByDefault
                },
                {
                    is_activable: true,
                    is_active: true,
                    service_option: 15, // upgradeServiceOptionNew2 state is loaded by active fu to isActiveByDefault
                },
                {
                    is_activable: true,
                    is_active: true,
                    service_option: 16, // upgradeServiceOptionNew2 state is loaded by active fu to isActiveByDefault
                },
                {
                    is_activable: true,
                    is_active: true,
                    service_option: 17, // upgradeServiceOptionNew2 state is loaded by active fu to isActiveByDefault
                },
                {
                    is_activable: true,
                    is_active: true,
                    service_option: 19, // upgradeServiceOptionNew2 state is loaded by active fu to isActiveByDefault
                },
                {
                    is_activable: true,
                    is_active: true,
                    service_option: 20, // upgradeServiceOptionNew2 state is loaded by active fu to isActiveByDefault
                },
            ],
            orderBy: ['service_option'],
        });

        // check upgrade service option states
        await checkTable(context, {
            hasVendor: false,
            tableDef: {
                schemaName: upgradeSchemaName,
                tableName: 'upgrade_vital_parent',
                columns: [
                    {
                        name: 'code',
                        type: 'string',
                        isNullable: false,
                        default: '',
                        comment: { type: 'string', isSystem: false, maxLength: 250 },
                    },
                ],
                comment: { isSharedByAllTenants: false },
                indexes: [],
                foreignKeys: [],
            },
            data: [],
        });

        // check upgrade service option states
        await checkTable(context, {
            hasVendor: false,
            tableDef: {
                schemaName: upgradeSchemaName,
                tableName: 'upgrade_vital_child',
                columns: [
                    {
                        name: 'code',
                        type: 'string',
                        isNullable: false,
                        default: '',
                        comment: { type: 'string', isSystem: false, maxLength: 250 },
                    },
                    {
                        name: 'parent',
                        type: 'integer',
                        isNullable: false,
                        comment: {
                            isSelfReference: false,
                            isSystem: false,
                            targetTableName: 'upgrade_vital_parent',
                            type: 'reference',
                        },
                    },
                    {
                        name: '_sort_value',
                        type: 'integer',
                        isNullable: false,
                        default: `(currval((pg_get_serial_sequence('${upgradeSchemaName}.upgrade_vital_child'::text, '_id'::text))::regclass) * 100)`,
                        comment: { type: 'integer', isSystem: false },
                    },
                ],
                comment: { isSharedByAllTenants: false },
                indexes: [
                    {
                        name: 'upgrade_vital_child_ind0',
                        isUnique: true,
                        columns: [
                            { name: 'parent', ascending: true },
                            { name: '_sort_value', ascending: true },
                        ],
                    },
                ],
                foreignKeys: [
                    {
                        name: 'upgrade_vital_child_parent_fk',
                        targetTable: 'upgrade_vital_parent',
                        targetColumnNames: [],
                        columnNames: ['parent'],
                        comment: {
                            targetTableName: 'upgrade_vital_parent',
                            columns: { _tenant_id: '_tenant_id', parent: '_id' },
                            onDeleteBehaviour: 'cascade',
                            isDeferrable: true,
                        },
                        onDeleteBehaviour: 'cascade',
                        isDeferrable: true,
                    },
                ],
            },
            data: [],
        });

        // Check upgrade_rename_vital_child_v_2 table
        // The table upgrade_rename_vital_child_v_1 was renamed to upgrade_rename_vital_child_v_2
        await checkTable(context, {
            tableDef: {
                schemaName: upgradeSchemaName,
                tableName: 'upgrade_rename_vital_child_v_2',
                columns: [
                    {
                        name: 'parent',
                        isNullable: false,
                        type: 'integer',
                        comment: {
                            type: 'reference',
                            isSystem: false,
                            targetTableName: 'upgrade_rename_vital_parent',
                            isSelfReference: false,
                        },
                    },
                    {
                        name: 'code',
                        isNullable: false,
                        type: 'string',
                        default: '',
                        comment: { type: 'string', isSystem: false, maxLength: 250 },
                    },
                    {
                        name: '_sort_value',
                        type: 'integer',
                        isNullable: false,
                        default: `(currval((pg_get_serial_sequence('${upgradeSchemaName}.upgrade_rename_vital_child_v_2'::text, '_id'::text))::regclass) * 100)`,
                        comment: { type: 'integer', isSystem: false },
                    },
                ],
                comment: { isSharedByAllTenants: false },
                foreignKeys: [
                    {
                        // Make sure the foreign key to parent was renamed as well
                        name: 'upgrade_rename_vital_child_v_2_parent_fk',
                        targetTable: 'upgrade_rename_vital_parent',
                        targetColumnNames: [],
                        columnNames: ['parent'],
                        comment: {
                            targetTableName: 'upgrade_rename_vital_parent',
                            columns: { _tenant_id: '_tenant_id', parent: '_id' },
                            onDeleteBehaviour: 'cascade',
                            isDeferrable: true,
                        },
                        onDeleteBehaviour: 'cascade',
                        isDeferrable: true,
                    },
                ],
            },
            data: [],
        });
        // platform/upgrade-test/xtrem-upgrade-test-v2-base/lib/upgrades/vlatest/index.ts
        await checkTableDropped(context, 'upgrade_rename_vital_child_v_1');

        // Check upgrade on upgrade_reload_csv_vendor_parent: data should have been updated by
        // reloading platform/upgrade-test/xtrem-upgrade-test-v2/data/layers/setup/upgrade-reload-csv-vendor-parent.csv
        await checkTable(context, {
            hasVendor: true,
            tableDef: {
                schemaName: upgradeSchemaName,
                tableName: 'upgrade_reload_csv_vendor_parent',
                columns: [
                    {
                        name: 'id',
                        isNullable: false,
                        type: 'string',
                        default: '',
                        comment: { type: 'string', isSystem: false, maxLength: 50 },
                    },
                    {
                        name: 'some_string',
                        isNullable: false,
                        type: 'string',
                        default: '',
                        comment: { type: 'string', isSystem: false, maxLength: 250 },
                    },
                ],
                indexes: [
                    {
                        name: 'upgrade_reload_csv_vendor_parent_ind0',
                        isUnique: true,
                        columns: [{ name: 'id', ascending: true }],
                    },
                ],
                comment: {
                    isSetupNode: true,
                    isSharedByAllTenants: false,
                    naturalKey: ['id'],
                },
            },
            data: [
                { id: 'HAS_VENDOR', some_string: 'Has vendor - updated' }, // Updated because of vendor
                { id: 'NO_VENDOR', some_string: 'No vendor' }, // Not updated, because no vendor in CSV
            ],
            orderBy: ['id'],
        });

        // Check upgrade on upgrade_reload_csv_vendor_child: data should have been updated by
        // reloading platform/upgrade-test/xtrem-upgrade-test-v2/data/layers/setup/upgrade-reload-csv-vendor-child.csv
        await checkTable(context, {
            hasVendor: true,
            tableDef: {
                schemaName: upgradeSchemaName,
                tableName: 'upgrade_reload_csv_vendor_child',
                columns: [
                    {
                        name: 'parent',
                        type: 'integer',
                        isNullable: false,
                        comment: {
                            isSelfReference: false,
                            isSystem: false,
                            targetTableName: 'upgrade_reload_csv_vendor_parent',
                            type: 'reference',
                        },
                    },
                    {
                        name: '_sort_value',
                        type: 'integer',
                        isNullable: false,
                        default: `(currval((pg_get_serial_sequence('${upgradeSchemaName}.upgrade_reload_csv_vendor_child'::text, '_id'::text))::regclass) * 100)`,
                        comment: { type: 'integer', isSystem: false },
                    },
                    {
                        name: 'id',
                        isNullable: false,
                        type: 'string',
                        default: '',
                        comment: { type: 'string', isSystem: false, maxLength: 50 },
                    },
                    {
                        name: 'child_string',
                        isNullable: false,
                        type: 'string',
                        default: '',
                        comment: { type: 'string', isSystem: false, maxLength: 250 },
                    },
                ],
                indexes: [
                    {
                        name: 'upgrade_reload_csv_vendor_child_ind0',
                        isUnique: true,
                        columns: [
                            { name: 'parent', ascending: true },
                            { name: '_sort_value', ascending: true },
                        ],
                    },
                ],
                foreignKeys: [
                    {
                        name: 'upgrade_reload_csv_vendor_child_parent_fk',
                        targetTable: 'upgrade_reload_csv_vendor_parent',
                        targetColumnNames: [],
                        columnNames: ['parent'],
                        comment: {
                            targetTableName: 'upgrade_reload_csv_vendor_parent',
                            columns: { _tenant_id: '_tenant_id', parent: '_id' },
                            onDeleteBehaviour: 'cascade',
                            isDeferrable: true,
                        },
                        onDeleteBehaviour: 'cascade',
                        isDeferrable: true,
                    },
                ],
                comment: {
                    isSetupNode: true,
                    isSharedByAllTenants: false,
                    naturalKey: ['parent', '_sortValue'],
                },
            },
            data: [
                { parent: 1, _sort_value: 10, id: 'PARENT_VENDOR', child_string: 'Parent has vendor - updated' }, // Updated because of vendor
                { parent: 2, _sort_value: 20, id: 'PARENT_NO_VENDOR', child_string: 'Parent no vendor' }, // Not updated, because no vendor in CSV
                {
                    parent: 2,
                    _sort_value: 30,
                    id: 'PARENT_NO_VENDOR',
                    child_string: 'Parent no vendor different sort value',
                }, // Not updated, because no vendor in CSV and as the vital parent already existed the new line with sort value 300 not loaded
            ],
            orderBy: ['parent', '_sort_value'],
        });

        // Ensure that the CustomSqlAction was executed on the table upgrade_data_custom_sql_action
        // see platform/upgrade-test/xtrem-upgrade-test-v2-base/lib/upgrades/v1.1.2/data-custom-sql-action.ts
        // int_2 should be equal to int_1 * 2
        await checkTable(context, {
            tableDef: {
                schemaName: upgradeSchemaName,
                tableName: 'upgrade_data_custom_sql_action',
                columns: [
                    {
                        name: 'int_1',
                        type: 'integer',
                        isNullable: false,
                    },
                    {
                        name: 'int_2',
                        type: 'integer',
                        isNullable: false,
                    },
                ],
                comment: {
                    isSharedByAllTenants: false,
                },
            },
            data: [
                { int_1: 1, int_2: 2 },
                { int_1: 2, int_2: 4 },
                { int_1: 3, int_2: 6 },
                { int_1: 4, int_2: 8 },
            ],

            orderBy: ['int_1'],
        });

        await checkTable(context, {
            tableDef: {
                schemaName: upgradeSchemaName,
                tableName: 'upgrade_change_hierarchy_levels',
                columns: [
                    {
                        name: '_constructor',
                        isNullable: false,
                        type: 'string',
                        default: '',
                        comment: { type: 'string', isSystem: true, maxLength: 100 },
                    },
                    {
                        name: 'str',
                        isNullable: false,
                        type: 'string',
                        default: '',
                        comment: { type: 'string', isSystem: false, maxLength: 250 },
                    },
                ],
                comment: {
                    isSharedByAllTenants: false,
                    baseTable: 'upgrade_base_node',
                    rootTable: 'upgrade_base_node',
                },
            },
        });

        // platform/upgrade-test/xtrem-upgrade-test-v2-base/lib/upgrades/vlatest/drop-upgrade-base-node-2.ts
        await checkTableDropped(context, 'upgrade_base_node_2');

        // platform/upgrade-test/xtrem-upgrade-test-v1-released-invalid/lib/upgrades/vlatest/drop-table-schema.ts
        await checkTableDropped(context, 'upgrade_delete_table');

        await checkTable(context, {
            tableDef: {
                schemaName: upgradeSchemaName,
                tableName: 'upgrade_remove_base_node',
                columns: [
                    {
                        name: 'str_prop',
                        isNullable: false,
                        type: 'string',
                        default: '',
                        comment: { type: 'string', isSystem: false, maxLength: 250 },
                    },
                ],
                comment: {
                    isSharedByAllTenants: false,
                },
            },
        });

        // platform/upgrade-test/xtrem-upgrade-test-v2-base/lib/upgrades/vlatest/drop-upgrade-base-node-to-delete.ts
        await checkTableDropped(context, 'upgrade_base_node_to_delete');

        await checkFunctions(context);

        // Check that all the expected tables are there
        await asyncArray(testApplication.getSqlPackageFactories()).forEach(async factory => {
            assert.isTrue(await factory.table.tableExists(context), `Table ${factory.tableName} does not exist`);
        });
    });
}

async function checkAfterUpgradeToV2(application: Application, metadataBeforeUpgrade: MetadataSummary): Promise<void> {
    /**
     * Check that every action (schema + data) was successful
     */
    await checkTablesAfterUpgradeToV2(application);

    await checkMetadataAfterUpgradeToV2(application, metadataBeforeUpgrade);

    await application.asRoot.withReadonlyContext(Test.defaultTenantId, async context => {
        // Activity loaded after upgrade
        const activities = await context.executeSql<AnyRecord[]>(
            `select * from ${context.schemaName}.activity WHERE package in (SELECT spv._id FROM ${context.schemaName}.sys_pack_version spv WHERE  spv.name LIKE $1)`,
            ['%xtrem-upgrade-test%'],
        );

        assert.equal(activities.length, 2);
        assert.deepEqual(
            activities.map(activity => {
                return {
                    name: activity.name,
                    description: activity.description,
                    permissions: activity.permissions,
                };
            }),
            [
                {
                    description: 'A new activity',
                    name: 'newActivity',
                    permissions: ['read', 'create', 'update', 'delete'],
                },
                {
                    description: 'Activity to update(updated)',
                    name: 'updateActivity',
                    permissions: ['read', 'create', 'update'],
                },
            ],
        );

        // Active service options after upgrade
        assert.deepEqual(
            (await context.activeServiceOptions).map(s => s.name),
            [
                'synchronizationServiceOption',
                'upgradeServiceOption1',
                'upgradeServiceOption2',
                'upgradeServiceOption3',
                'upgradeServiceOption4',
                'upgradeServiceOptionNew1',
                'upgradeServiceOptionNew2',
            ],
        );
    });
}

/**
 * Check the source code of some functions
 */
async function checkFunctionSources(context: Context, expectedFunctions: Dict<string>): Promise<void> {
    const fnRecords = await context.executeSql<{ proname: string; prosrc: string }[]>(
        `SELECT proname, prosrc FROM pg_proc p
            INNER JOIN pg_namespace ns ON p.pronamespace = ns.oid
            WHERE ns.nspname = $1;
            `,
        [context.schemaName],
    );

    const trimSource = (source: string): string => {
        return source.replace(/\s/g, '');
    };

    fnRecords.forEach(fnRecord => {
        if (!expectedFunctions[fnRecord.proname]) {
            // the source of this function does not need to be checked
            return;
        }
        assert.equal(
            trimSource(fnRecord.prosrc),
            trimSource(expectedFunctions[fnRecord.proname]),
            `Source code mismatch for function ${fnRecord.proname}`,
        );
    });
}

/**
 * Check the source code of some functions
 */
async function checkFunctions(context: Context): Promise<void> {
    await checkFunctionSources(context, {
        /**
         * The base node of the UpgradeChangeBaseNode node has been changed from
         * UpgradeAbstractNode1 to UpgradeAbstractNode2
         */
        upgrade_change_base_node_base_delete: `BEGIN
                EXECUTE 'DELETE FROM ${context.schemaName}.upgrade_abstract_node_2 WHERE _id = '
                    || OLD._id || ' AND _tenant_id = ''' || OLD._tenant_id || '''';
                RETURN OLD;
            END;`,
    });
}

function hasBulkMutation(factory: NodeFactory, name: string) {
    const { decorators } = factory;
    const ctor = factory.nodeConstructor as { bulkDelete?: Function; bulkUpdate?: Function };
    return (
        decorators[`can${pascalCase(name)}`] &&
        ctor.bulkDelete?.name === `bound ${name}` &&
        // we should have at least the start and stop actions
        decorators.mutations?.filter((m: any) => m.name === name).length > 1
    );
}

function patchCommunicationManager() {
    const oldExtendClassDecorator = CoreHooks.communicationManager.extendClassDecorator;
    CoreHooks.communicationManager.extendClassDecorator = (factory: NodeFactory) => {
        // the factory has already been loaded by a previous upgrade application and the static function is already there
        if (hasBulkMutation(factory, 'bulkDelete')) {
            return;
        }
        oldExtendClassDecorator?.(factory);
    };
}

async function createUpgradeTestApplication(packageName: string): Promise<Application> {
    if (packageName.endsWith('-v2')) {
        patchCommunicationManager();
    }
    const application = await Test.createCliApplication({
        buildDir: fsp.join(getPackageFolder(packageName), 'build'),
        schemaName: upgradeSchemaName,
    });
    await application.asRoot.withCommittedContext(Test.defaultTenantId, context =>
        Test.ensureTestTenantExists(context),
    );
    return application;
}

async function setApplicationVersionInDatabase(application: Application, version: string): Promise<void> {
    // We can't know exactly what the version of xtrem-upgrade-test-v1 will be. So we set it to an arbitrary
    // This version will only be used to check that the v0.9.1 upgrade actions won't be launched.
    await application.asRoot.withCommittedContext(Test.defaultTenantId, async context => {
        await context.bulkUpdate(SysPackVersion, {
            set: {
                version,
            },
            /* istanbul ignore next */
            async where() {
                return (
                    (await this.name) === '@sage/xtrem-upgrade-test' ||
                    (await this.name) === '@sage/xtrem-upgrade-test-base'
                );
            },
        });
        /*
        const sqlConverter = new SqlConverter(context, application.getFactoryByConstructor(SysPackVersion));
        const updateSql = sqlConverter.getUpdateSetCommand<SysPackVersion>({
            set: {
                version,
            },
            /* istanbul ignore next *
            where() {
                return this.name === '@sage/xtrem-upgrade-test' || this.name === '@sage/xtrem-upgrade-test-base';
            },
        });
        await context.executeSql(updateSql, sqlConverter.args);
        */
    });
}

/**
 * Initializes the application in v1
 */
async function initApplicationV1(): Promise<Application> {
    // create the tables for package xtrem-upgrade-test-v1
    await executeNpmCommand(packageNameV1, ['run', 'initSchema']);
    const testApplicationV1 = await createUpgradeTestApplication('@sage/xtrem-upgrade-test-v1');

    // No activities in table
    await testApplicationV1.asRoot.withReadonlyContext(Test.defaultTenantId, async context => {
        const activities = await context.executeSql<AnyRecord[]>(
            `select * from ${context.schemaName}.activity WHERE package in (SELECT spv._id FROM ${context.schemaName}.sys_pack_version spv WHERE  spv.name LIKE $1)`,
            ['%xtrem-upgrade-test%'],
        );

        assert.equal(activities.length, 2);

        assert.deepEqual(
            activities.map(activity => {
                return {
                    name: activity.name,
                    description: activity.description,
                    permissions: activity.permissions,
                };
            }),
            [
                {
                    description: 'Activity to delete',
                    name: 'deleteActivity',
                    permissions: ['read', 'create', 'update', 'delete'],
                },
                {
                    description: 'Activity to update',
                    name: 'updateActivity',
                    permissions: ['read', 'create', 'update', 'delete'],
                },
            ],
        );
    });

    await testApplicationV1.asRoot.withCommittedContext(Test.defaultTenantId, async context => {
        // package artifacts are usually loaded by the client side, via the metadata schema, as our application has no artifacts
        // artificially load an artifact here to test that the package artifacts are deleted after the upgrade
        // The package artifacts will be reloaded when the application is started again and a page is navigated to
        // ** To review or remove: Artifacts are no longer stored in DB, we should add a page in the application instead **
        // await (
        //     context.application.packageManager.createPackArtifact(context, {
        //         bundleId: '@sage/xtrem-upgrade-test',
        //         path: 'some path',
        //         content: new TextStream('some content'),
        //         dataType: 'meta',
        //     }),
        // );

        //  activate service option upgradeServiceOption4
        // const serviceOption = testApplicationV1.serviceOptions.upgradeServiceOption4;

        await context.serviceOptionManager.createOrUpdateServiceOptions(context);
        await context.serviceOptionManager.deleteObsoleteServiceOptions(context);
        await context.serviceOptionManager.createOrUpgradeServiceOptionStates(context, { upgradeServiceOption4: true });

        assert.deepEqual((await context.activeServiceOptions).map(s => s.name).sort(), [
            'synchronizationServiceOption',
            'upgradeServiceOption1',
            'upgradeServiceOption2',
            'upgradeServiceOption3',
            'upgradeServiceOption4',
            'upgradeServiceOptionToDelete',
        ]);
    });

    return testApplicationV1;
}

async function executeUpgradeProcess(packageName: string): Promise<void> {
    await executeNpmCommand(
        packageName,
        process.env.XTREM_CI === '1' ? ['run', 'upgrade-schema:ci'] : ['run', 'upgrade-schema'],
    );
}

interface MetadataEntry {
    _id: string;
    checksum: string;
    is_active: boolean;
}

type MetadataSummary = Dict<MetadataEntry[]>;

function readMetadataEntries(context: Context, tableName: string): Promise<MetadataEntry[]> {
    return context.executeSql(`SELECT _id, checksum, is_active FROM ${context.schemaName}.${tableName}`, []);
}

function readMetadataSummaries(application: Application): Promise<MetadataSummary> {
    return application.asRoot.withReadonlyContext(null, async context => {
        return {
            metaNodeFactories: await readMetadataEntries(context, 'meta_node_factory'),
            metaNodeProperties: await readMetadataEntries(context, 'meta_node_property'),
            metaNodeOperations: await readMetadataEntries(context, 'meta_node_operation'),
        };
    });
}

function getMetadataChanges(
    before: MetadataSummary,
    after: MetadataSummary,
): Dict<{ total: number; added: number; updated: number; deleted: number }> {
    const keys = Object.keys(after);
    return lodash.zipObject(
        keys,
        keys.map(name => {
            const entriesBefore = before[name];
            const entriesAfter = after[name];
            const total = entriesAfter.length;
            const added = entriesAfter.filter(
                entry => entry.is_active && !entriesBefore.some(e => e._id === entry._id),
            ).length;
            const updated = entriesAfter.filter(
                entry =>
                    entry.is_active && entriesBefore.some(e => e._id === entry._id && e.checksum !== entry.checksum),
            ).length;
            const deleted =
                entriesAfter.filter(entry => !entry.is_active).length -
                entriesBefore.filter(entry => !entry.is_active).length;

            return { total, added, updated, deleted };
        }),
    );
}

async function checkMetadataBeforeUpgradeToV2(application: Application): Promise<MetadataSummary> {
    // Restore if needed (to test changes in metadata)
    if (!false) {
        return {};
    }

    const before = { metaNodeFactories: [], metaNodeProperties: [], metaNodeOperations: [] };
    const after = await readMetadataSummaries(application);
    assert.deepEqual(getMetadataChanges(before, after), {
        // These numbers must be adjusted when we add nodes, properties or operations to the system package
        // Adjust them as long as the variations are small.
        // Big changes could indicate a bug in the metadata upgrade logic
        metaNodeFactories: { total: 117, added: 117, updated: 0, deleted: 0 },
        metaNodeProperties: { total: 628, added: 628, updated: 0, deleted: 0 },
        metaNodeOperations: { total: 373, added: 373, updated: 0, deleted: 0 },
    });
    return after;
}

async function checkMetadataAfterUpgradeToV2(application: Application, before: MetadataSummary): Promise<void> {
    // Restore if needed (to test changes in metadata)
    if (!false) {
        return;
    }

    const after = await readMetadataSummaries(application);
    assert.deepEqual(getMetadataChanges(before, after), {
        // Same comment as above. Adjust if changes are small
        metaNodeFactories: { total: 125, added: 8, updated: 2, deleted: 9 },
        metaNodeProperties: { total: 669, added: 41, updated: 27, deleted: 25 },
        metaNodeOperations: { total: 394, added: 21, updated: 0, deleted: 15 },
    });
}

/**
 * Returns the folder where the SQL files are generated, for the provided package
 */
function getSqlFolderForPackage(packageName: string): string {
    return fsp.join(getPackageFolder(packageName), 'sql');
}

/**
 * Removes all the existing files from the folder where the SQL files are generated, for the provided package
 * For conveniency, this function will return the folder.
 */
function clearSqlFolderForPackage(packageName: string): string {
    const sqlFolder = getSqlFolderForPackage(packageName);
    if (!fs.existsSync(sqlFolder)) return sqlFolder;
    fs.readdirSync(sqlFolder).forEach(filename => {
        const fullPath = fsp.join(sqlFolder, filename);
        // eslint-disable-next-line no-console
        console.log(`Dropping SQL file ${fullPath}`);
        fs.unlinkSync(fullPath);
    });
    return sqlFolder;
}

/**
 * Upgrade an application and record a SQL file.
 * The function will return the name of the generated SQL file
 */
async function upgradeAndRecord(packageName: string): Promise<string> {
    // delete all the existing SQL files
    const sqlFolder = clearSqlFolderForPackage(packageName);

    const options = {
        semVerCompatibilityLevel: 'major' as SemVerCompatibilityLevel,
    };

    // run the 'record' command to create a new SQL file
    await executeNpmCommand(
        packageName,
        ['run', 'xtrem', '--', 'upgrade', '--run', '--record', '--skip-vacuum'],
        options,
    );

    // Ensure that a SQL file was generated
    const files = fs.readdirSync(sqlFolder);
    if (files.length === 0) {
        throw new Error('No SQL file was generated');
    }
    if (files.length > 1) {
        throw new Error(`More than 1 SQL file was generated: \n${files.join('\n')}`);
    }
    const sqlFile = fsp.join(sqlFolder, files[0]);

    // Note: the SQL file should only contain one command that matches the script
    // platform/upgrade-test/xtrem-upgrade-test-v1-released-valid/lib/upgrades/vlatest/valid-data-action.ts

    // eslint-disable-next-line no-console
    console.log(`The SQL file was successfully generated : ${sqlFile}`);

    return sqlFile;
}

async function _executeCustomSqlScript(
    app: Application,
    packageName: string,
    scriptName: string,
    options: {
        dryRun: boolean;
        shouldPass: boolean;
        expectedResult?: any;
        expectedError?: string;
    },
): Promise<string> {
    let passed = false;
    const scriptLocation = fsp.join('custom-sql', scriptName);
    try {
        const args = [
            'run',
            'xtrem',
            '--',
            'tenant',
            '--execute-custom-sql',
            `--location=${scriptLocation}`,
            `--tenants="${mainTenantId}"`,
        ];
        if (options.dryRun) args.push('--dry-run');
        await executeNpmCommand(packageName, args);
        passed = true;
    } catch (err) {
        if (options.shouldPass) throw new Error(`The custom SQL script ${scriptName} failed`);
    }
    if (passed && !options.shouldPass) {
        throw new Error(`The custom SQL script ${scriptName} should have failed`);
    }
    if (options.expectedResult === undefined && options.expectedError === undefined) return scriptLocation;
    const result = await app.asRoot.withReadonlyContext(mainTenantId, async context => {
        const histories = context.query(SysCustomSqlHistory, {
            filter: { scriptPath: scriptLocation },
            orderBy: { startDateTime: -1 },
        });
        const history = await histories.at(0);
        return history?.result;
    });
    if (result == null) throw new Error(`The script ${scriptName} did not return any result in db`);
    if (options.expectedResult !== undefined) {
        assert.deepEqual(result.output, options.expectedResult, `Custom SQL script ${scriptName}: invalid result`);
    }
    if (options.expectedError !== undefined) {
        assert.equal(result.error, options.expectedError, `Custom SQL script ${scriptName}: invalid error`);
    }

    return scriptLocation;
}

describe('Upgrade tests', () => {
    before(() => {
        deleteTempConfig('xtrem-upgrade-test-v2');
    });

    it('init v1 and apply data patches', async () => {
        await initApplicationV1();
        const testApplicationV2 = await createUpgradeTestApplication('@sage/xtrem-upgrade-test-v2');

        const secondTenantId = '000000000000000000001';

        // Create a second tenant '000....0001' to test that typescript data-patches are executed on all tenants
        await testApplicationV2.asRoot.withCommittedContext(null, context =>
            Context.tenantManager.ensureTenantExists(context, {
                customer: {
                    id: secondTenantId,
                    name: 'Customer for tests (automatic creation)',
                },
                tenant: {
                    id: secondTenantId,
                    name: 'test-tenant #2',
                },
            }),
        );

        await testApplicationV2.asRoot.withCommittedContext(secondTenantId, async context => {
            const userInConfig = ConfigManager.current.user;
            if (userInConfig && userInConfig !== rootUserEmail) {
                // Create the user in the 2nd tenantId
                // If userInConfig === <EMAIL>, it was already created when creating the tenant
                const userData: Partial<UserInfo> = {
                    email: userInConfig,
                    firstName: 'from config',
                    lastName: 'from config',
                };
                await (await context.create(CoreHooks.sysManager.getUserNode(), userData as any)).$.save();
            }
        });

        // The scenario is:
        // The app has been upgraded to version 2.0.155
        // A new image (hotfix) is delivered in version 2.0.158
        // We apply the data patches
        // - v2.0.155 must NOT be applied
        // - v2.0.157 must be applied
        // - vlatest must be applied
        await setApplicationVersionInDatabase(testApplicationV2, '2.0.155');
        testApplicationV2.mainPackage.packageJson.version = '2.0.158';
        // LATER DataPatchEngine.applyDataPatches(testApplicationV2);
        // LATER checkTablesAfterDataPatches(testApplicationV2);

        // Remove the 000....00001 tenant
        await Context.tenantManager.deleteTenant(Test.application, secondTenantId);
    });

    it('upgrade v1 -> v2 (and record)', async () => {
        const testApplicationV1 = await initApplicationV1();

        // We can't know exactly what the version of xtrem-upgrade-test-v1 will be. So we set it to an arbitrary
        // This version will only be used to check that the v0.9.1 upgrade actions won't be launched.
        await setApplicationVersionInDatabase(testApplicationV1, '0.9.1');

        // Check and record metadata key figures before upgrade
        const metadataBeforeUpgrade = await checkMetadataBeforeUpgradeToV2(testApplicationV1);

        // Upgrade the application from v1 to v2 and record the SQL file.
        // This file will be used in the 'upgrade v1 -> v2 (from SQL file)' step
        await upgradeAndRecord(packageNameV2);

        // Upgrade the version (from 1.0.0 to 2.0.0)
        await executeUpgradeProcess(packageNameV2);

        const testApplicationV2 = await createUpgradeTestApplication('@sage/xtrem-upgrade-test-v2');

        // Check if everything is OK after the upgrade
        await checkAfterUpgradeToV2(testApplicationV2, metadataBeforeUpgrade);

        /**
         * Step 4 : Check that psql functions can be invoked
         */
        await testApplicationV2.asRoot.withReadonlyContext(Test.defaultTenantId, async context => {
            const nanoid = await context.executeSql<AnyRecord[]>(`select ${context.schemaName}.nanoid()`, []);
            assert.equal((nanoid[0].nanoid as string)?.length, 21);
        });
    });

    /**
     * Important: this step can only be executed if 'upgrade v1 -> v2 (and record)' was successfully executed
     */
    it('upgrade v1 -> v2 (from SQL file)', async () => {
        // Upgrade from v1 to v2, using the SQL file that was generated by the 'upgrade v1 -> v2 (and record)' step
        const testApplicationV1 = await initApplicationV1();

        // We can't know exactly what the version of xtrem-upgrade-test-v1 will be. So we set it to an arbitrary
        // This version will only be used to check that the v0.9.1 upgrade actions won't be launched.
        await setApplicationVersionInDatabase(testApplicationV1, '0.9.1');

        const testApplicationV2 = await createUpgradeTestApplication('@sage/xtrem-upgrade-test-v2');
        const options = {
            semVerCompatibilityLevel: 'major' as SemVerCompatibilityLevel,
        };

        // upgrade by replaying the SQL file
        await executeNpmCommand(
            packageNameV2,
            ['run', 'xtrem', '--', 'upgrade', '--run', '--prod', '--skip-vacuum', '--metrics=local'],
            options,
        );

        /**
         * Check that every action (schema + data) was successful
         */
        await checkTablesAfterUpgradeToV2(testApplicationV2);
    });

    it('Upgrade v1 -> v1-released (valid hot upgrade)', async () => {
        const testApplicationV1 = await initApplicationV1();
        const dbVersions = await testApplicationV1.asRoot.withUncommittedContext(null, context =>
            context.executeSql<{ version: string }[]>(
                `SELECT version from ${context.schemaName}.sys_pack_version WHERE name=$1`,
                [testApplicationV1.packageName],
            ),
        );
        // We don't know exactly what the version of xtrem-upgrade-test-v1 will be (it will be sth like 1.0.x, where x depends on the
        // number of times the patch-release was executed)
        const startupVersion = dbVersions[0].version;

        const sqlFile = await upgradeAndRecord(packageNameV1ReleasedValidHotUpgrade);

        // Restore the version (in db) of the main package to make sure the new SQL will be replayed
        const releasedApplication = await createUpgradeTestApplication(`@sage/${packageNameV1ReleasedValidHotUpgrade}`);
        await releasedApplication.asRoot.withCommittedContext(null, async context => {
            await context.executeSql(`UPDATE ${context.schemaName}.sys_pack_version SET version=$1 WHERE name=$2`, [
                startupVersion,
                releasedApplication.packageName,
            ]);
        });

        // Reset columns that should be updated by the new SQL file
        // see platform/upgrade-test/xtrem-upgrade-test-v1-released-valid/lib/upgrades/vlatest/valid-data-action.ts
        await releasedApplication.asRoot.withCommittedContext(null, async context => {
            await context.executeSql(`UPDATE ${context.schemaName}.upgrade_update_columns SET value_3 = 1`, []);
        });

        const options = {
            semVerCompatibilityLevel: 'major' as SemVerCompatibilityLevel,
        };

        // Run the hot upgrade
        try {
            await executeNpmCommand(
                packageNameV1ReleasedValidHotUpgrade,
                process.env.XTREM_CI === '1' ? ['run', 'upgrade:hot:ci'] : ['run', 'upgrade:hot'],
                options,
            );
        } catch (err) {
            throw new Error(`Could not test hot upgrades : ${err.stack}`);
        } finally {
            // eslint-disable-next-line no-console
            console.log(`Deleting SQL file ${sqlFile}`);
            fs.unlinkSync(sqlFile);
        }

        // Check if the SQL file was correctly executed
        await releasedApplication.asRoot.withUncommittedContext(null, async context => {
            // see platform/upgrade-test/xtrem-upgrade-test-v1-released-valid/lib/upgrades/vlatest/valid-data-action.ts
            const errors = await context.executeSql<{ _id: string }[]>(
                `SELECT _id FROM ${context.schemaName}.upgrade_update_columns WHERE value_3 != value_2 * 2`,
                [],
            );
            if (errors.length > 0) throw new Error(`The SQL file ${sqlFile} was not replayed correctly.`);

            const versions = await context.executeSql<{ version: string }[]>(
                `SELECT version FROM ${context.schemaName}.sys_pack_version WHERE name=$1`,
                [releasedApplication.packageName],
            );
            const expectedVersion = releasedApplication.mainPackage.packageJson.version;
            if (!versions || versions.length !== 1) throw new Error('Could not get version after hot upgrade');
            if (versions[0].version !== expectedVersion)
                throw new Error(
                    `Versions mismatch after hot upgrade, got=${versions[0].version}, expected=${expectedVersion}`,
                );
        });
    });

    it('Upgrade v1 -> v1-released (invalid classic upgrade)', async () => {
        await initApplicationV1();

        lastOutput = '';
        await assert.isRejected(executeUpgradeProcess(packageNameV1ReleasedInvalidUpgrade));

        const expectedErrors = [
            'is released. Cannot create new table new_in_v_1_released_node',
            'is released. Cannot create column value_2 of table upgrade_add_columns',
            'is released. Cannot update column string_to_longer_string of table upgrade_alter_columns. Inconsistencies: string[50]->string[250]',
            'is released. Cannot update column integer_to_decimal of table upgrade_alter_columns. Inconsistencies: integer->decimal',
            'is released. Cannot update column decimal_val of table upgrade_datatypes. Inconsistencies: nullable false -> true',
            'is released. Cannot update column float_val of table upgrade_datatypes. Inconsistencies: nullable true -> false',
            'is released. Cannot drop column name_2 of table upgrade_node_to_extend',
            'is released. Cannot rename column foo_1 of table upgrade_rename_columns',
            "is released. The action UpgradeRenameColumns: rename property 'foo1' to 'foo2' cannot be executed as it would cause schema changes",
            "is released. The action UpgradeAddColumns: rename node 'foo1' to 'UpgradeAddColumns' cannot be executed as it would cause schema changes",
            'is released. The action Invalid Schema action cannot be executed as it would cause schema changes',
            'is released. The action Drop table upgrade_delete_table cannot be executed as it would cause schema changes',
        ];

        expectedErrors.forEach(expectedError => {
            assert(lastOutput.includes(expectedError), `Did not detect: ${expectedError}`);
        });
    });

    it('test custom sql scripts', async () => {
        const app = await initApplicationV1();
        const tableName = 'node_for_custom_sql_script';

        const secondTenantId = '0000000000-CUSTOM-SQL';

        // Create a second tenant '000....0001'
        await app.asRoot.withCommittedContext(null, context =>
            Context.tenantManager.ensureTenantExists(context, {
                customer: {
                    id: secondTenantId,
                    name: 'Customer for tests (automatic creation)',
                },
                tenant: {
                    id: secondTenantId,
                    name: 'test-tenant #2',
                },
            }),
        );

        // Retrieve the id of root user that was created for the 2nd tenant
        const userId = await app.asRoot.withCommittedContext(
            secondTenantId,
            async context => (await context.query(User).at(0))?._id,
        );

        // Copy the rows from 777...777.node_for_custom_sql_script to 000...001.node_for_custom_sql_script
        await app.asRoot.withCommittedContext(null, async context => {
            await context.executeSql(
                `
                CREATE TEMP TABLE tmp (like ${upgradeSchemaName}.${tableName});
                INSERT INTO tmp SELECT * FROM ${upgradeSchemaName}.${tableName};
                UPDATE tmp SET _tenant_id = '${secondTenantId}';
                UPDATE tmp SET _create_user = ${userId};
                UPDATE tmp SET _update_user = ${userId};
                INSERT INTO ${upgradeSchemaName}.${tableName} SELECT * from tmp;
                `,
                [],
                { allowUnsafe: true },
            );
        });

        const getRecordsCount = (tenantId: string) =>
            app.asRoot.withCommittedContext(null, async context => {
                const result = await context.executeSql<{ count: number }[]>(
                    `SELECT COUNT(*) count FROM ${upgradeSchemaName}.${tableName} WHERE _tenant_id=$1`,
                    [tenantId],
                );
                return result[0].count;
            });

        /**
         * TEST DRY MODE = TRUE
         */
        assert.equal(
            await getRecordsCount(mainTenantId),
            5,
            `The test data were not loaded correctly for table ${tableName}`,
        );
        await _executeCustomSqlScript(app, packageNameV1, 'valid-script-delete.sql', {
            dryRun: true,
            shouldPass: true,
            expectedResult: [{ _id: 3 }],
        });
        // No row should have beed deleted as we set the dryRun parameter
        assert.equal(await getRecordsCount(mainTenantId), 5, "The 'delete' script was not executed in dry mode");

        /**
         * TEST INVALID SCRIPTS
         */
        await _executeCustomSqlScript(app, packageNameV1, 'invalid-script-without-schema-name-marker.sql', {
            dryRun: false,
            shouldPass: false,
        });
        await _executeCustomSqlScript(app, packageNameV1, 'invalid-script-without-tenant-id-marker.sql', {
            dryRun: false,
            shouldPass: false,
        });
        await _executeCustomSqlScript(app, packageNameV1, 'invalid-script-error-in-sql.sql', {
            dryRun: false,
            shouldPass: false,
            expectedError: `Script custom-sql/invalid-script-error-in-sql.sql failed on tenant ${mainTenantId} : Error: relation "${upgradeSchemaName}.invalid_table_name" does not exist`,
        });
        /**
         * TEST VALID SCRIPTS (DRY MODE = FALSE)
         */
        await _executeCustomSqlScript(app, packageNameV1, 'valid-script-select.sql', {
            dryRun: false,
            shouldPass: true,
            expectedResult: [{ count: 5 }],
        });
        await _executeCustomSqlScript(app, packageNameV1, 'valid-script-update.sql', {
            dryRun: false,
            shouldPass: true,
            expectedResult: [{ _id: 1 }],
        });
        await _executeCustomSqlScript(app, packageNameV1, 'valid-script-delete.sql', {
            dryRun: false,
            shouldPass: true,
            expectedResult: [{ _id: 3 }],
        });

        // On the second execution, no row should be deleted (already deleted by previous call)
        await _executeCustomSqlScript(app, packageNameV1, 'valid-script-delete.sql', {
            dryRun: false,
            shouldPass: true,
            expectedResult: { updateCount: 0 },
        });

        // Ensure that the data was correctly updated on tenant 777...777
        await app.asRoot.withReadonlyContext(mainTenantId, async context => {
            await checkTable(context, {
                tableDef: {
                    schemaName: upgradeSchemaName,
                    tableName,
                    columns: [{ name: 'name' }, { name: 'value' }],
                },
                doNotCheckTableDef: true,
                data: [
                    { name: 'row1', value: 2 }, // value was updated by script valid-script-update.sql
                    { name: 'row2', value: 2 },
                    // 'row3' was deleted by script valid-script-delete.sql
                    { name: 'row4', value: 4 },
                    { name: 'row5', value: 5 },
                ],
                orderBy: ['name'],
            });
        });
        // Ensure no data was updated on tenant 000...001
        await app.asRoot.withReadonlyContext(secondTenantId, async context => {
            await checkTable(context, {
                tableDef: {
                    schemaName: upgradeSchemaName,
                    tableName,
                    columns: [{ name: 'name' }, { name: 'value' }],
                },
                doNotCheckTableDef: true,
                data: [
                    { name: 'row1', value: 1 },
                    { name: 'row2', value: 2 },
                    { name: 'row3', value: 3 },
                    { name: 'row4', value: 4 },
                    { name: 'row5', value: 5 },
                ],
                orderBy: ['name'],
            });
        });
    });

    after(() => {
        deleteTempConfig(packageNameV1ReleasedValidHotUpgrade);
        deleteTempConfig(packageNameV1ReleasedInvalidUpgrade);
        clearSqlFolderForPackage(packageNameV2);
        // Reset the access rights manager back to the mock
        updateContext();
    });
});
