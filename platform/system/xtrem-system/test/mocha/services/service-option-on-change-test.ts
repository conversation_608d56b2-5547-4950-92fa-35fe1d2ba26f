import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import { serviceOptions } from '../../fixtures/lib/index';

describe('Service option change events', () => {
    it('Service option onEnable should throw', async () => {
        await Test.withContext(async context => {
            // Verify service option is disabled
            assert.isFalse(await context.isServiceOptionEnabled(serviceOptions.optionOnEnableError));

            await assert.isRejected(
                context.serviceOptionManager.activateServiceOptions(context, [serviceOptions.optionOnEnableError]),
                'The record was not updated.',
            );

            // Verify service option was not changed
            assert.isFalse(await context.isServiceOptionEnabled(serviceOptions.optionOnEnableError));
        });
    });

    it('Service option onDisabled should throw', async () => {
        await Test.withContext(async context => {
            // Verify service option is enabled
            assert.isTrue(await context.isServiceOptionEnabled(serviceOptions.optionOnDisableError));

            await assert.isRejected(
                context.serviceOptionManager.deactivateServiceOptions(context, [serviceOptions.optionOnDisableError]),
                'The record was not updated.',
            );

            // Verify service option was not changed
            assert.isTrue(await context.isServiceOptionEnabled(serviceOptions.optionOnDisableError));
        });
    });
});
