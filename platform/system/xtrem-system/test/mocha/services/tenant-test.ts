import { Context, Test } from '@sage/xtrem-core';
import { TenantService } from '@sage/xtrem-data-management';
import { assert } from 'chai';

describe('Tenants', () => {
    it('can get tenant info', () =>
        Test.withContext(async context => {
            const tenants = await Context.tenantManager.getTenantsInfo(context, Test.defaultTenantId);
            const tenantInfo = tenants[0];
            assert.equal(tenantInfo.name, 'Tenant for tests (automatic creation)');
        }));

    it('cand delete tenant', async () => {
        const tenantId = await Test.withContext(async context => {
            const customer = { id: 'customer-test', name: 'customer test' };
            const tenant = {
                id: 'tenant-test',
                name: 'tenant test',
                customer,
            };
            await TenantService.initTenantIdentifiers(
                context,
                tenant.customer.id,
                tenant.customer.name,
                tenant.id,
                tenant.name,
            );
            const ids = await Context.tenantManager.listTenantsIds(context);
            assert.isTrue(!!ids.find(id => id === tenant.id));
            return tenant.id;
        });

        await Context.tenantManager.deleteTenant(Test.application, tenantId);

        await Test.withReadonlyContext(async context => {
            const ids = await Context.tenantManager.listTenantsIds(context);
            assert.isFalse(!!ids.find(id => id === tenantId));
        });
    });
});
