/* eslint-disable no-console */
import { CoreHooks, Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import { SysDataValidationReport } from '../../../lib/nodes/sys-data-validation-report';

describe('DataValidation manager', () => {
    it('test validators count', () => {
        const validators = CoreHooks.getDataValidationManager().getValidators();
        // There should only be 1 validator, the one that was registered in the fixtures
        // for DataValidationNode node
        assert.equal(validators.length, 1);
    });
    it('test validators', async () => {
        await CoreHooks.getDataValidationManager().buildReports(Test.application);

        await Test.withContext(async context => {
            const reports = await context.query(SysDataValidationReport).toArray();
            assert.equal(reports.length, 1);
            const report = reports[0];
            const lines = report.lines;
            const linesCount = await lines.length;
            assert.equal(linesCount, 2, `Expected 2 lines, got ${linesCount}`);
            let line = await lines.at(0);
            assert.deepEqual(await line?.$.payload(), {
                _sortValue: 10,
                _customData: {},
                report: {},
                nodeName: 'DataValidationNode',
                nodeId: 1,
                message: 'Integer value must be even.',
                path: 'intValue',
                severity: 'error',
                extraInfo: { intValue: 1 },
            } as any);
            line = await lines.at(1);
            assert.deepEqual(await line?.$.payload(), {
                _sortValue: 20,
                _customData: {},
                report: {},
                nodeName: 'DataValidationNode',
                nodeId: 3,
                message: 'Integer value must be even.',
                path: 'intValue',
                severity: 'error',
                extraInfo: { intValue: 3 },
            } as any);
        });
    });
});
