import { ConfigManager, Context, NodeStatus, Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremSystem from '../../../lib/index';

import { UserEventHandler } from '@sage/xtrem-user-event';
import * as sinon from 'sinon';

const sandbox = sinon.createSandbox();

type UserEventCreatedModifiedArgs = Parameters<typeof UserEventHandler.prototype.userCreated>;

type UserEventDeletedArgs = Parameters<typeof UserEventHandler.prototype.userDeleted>;

type EnableSupportAccessArgs = Parameters<typeof UserEventHandler.prototype.enableSupportAccess>;

type DisableSupportAccessArgs = Parameters<typeof UserEventHandler.prototype.disableSupportAccess>;

describe('User events manager', () => {
    let userCreatedSpy: sinon.SinonSpy<UserEventCreatedModifiedArgs, Promise<string>>;
    let userModifiedSpy: sinon.SinonSpy<UserEventCreatedModifiedArgs, Promise<string>>;
    let userDeletedSpy: sinon.SinonSpy<UserEventDeletedArgs, Promise<string>>;
    let enableSupportAccessSpy: sinon.SinonSpy<EnableSupportAccessArgs, Promise<string>>;
    let disableSupportAccessSpy: sinon.SinonSpy<DisableSupportAccessArgs, Promise<string>>;

    before(() => {});

    beforeEach(() => {
        userCreatedSpy = sandbox.spy(UserEventHandler.prototype, 'userCreated');
        userModifiedSpy = sandbox.spy(UserEventHandler.prototype, 'userModified');
        userDeletedSpy = sandbox.spy(UserEventHandler.prototype, 'userDeleted');
        enableSupportAccessSpy = sandbox.spy(UserEventHandler.prototype, 'enableSupportAccess');
        disableSupportAccessSpy = sandbox.spy(UserEventHandler.prototype, 'disableSupportAccess');
    });

    afterEach(() => {
        sandbox.restore();
    });

    const locale = 'en-US';
    const appIds = ['sdmo'];

    async function testUserCreated(context: Context, appId: string): Promise<void> {
        const oldAppId = ConfigManager.current.app;
        ConfigManager.current.app = appId;
        const email = '<EMAIL>';
        const billingRole = 'businessUser';
        await xtremSystem.UserEventsManager.userCreated(context, {
            userId: 1000,
            status: NodeStatus.added,
            email,
            isActive: true,
            billingRole,
        });
        assert(userCreatedSpy.calledOnce);
        assert(
            userCreatedSpy.calledWithExactly(
                appId,
                Test.defaultTenantId,
                email,
                locale,
                true,
                billingRole,
                undefined,
                undefined,
            ),
        );
        ConfigManager.current.app = oldAppId;
    }

    async function testUserModified(context: Context, appId: string): Promise<void> {
        const oldAppId = ConfigManager.current.app;
        ConfigManager.current.app = appId;
        const email = '<EMAIL>';
        const billingRole = 'operationalUser';
        await xtremSystem.UserEventsManager.userModified(context, {
            userId: 1,
            status: NodeStatus.modified,
            email,
            isActive: true,
            billingRole,
        });
        assert(userModifiedSpy.calledOnce);
        assert(
            userModifiedSpy.calledWithExactly(
                appId,
                Test.defaultTenantId,
                email,
                locale,
                true,
                billingRole,
                undefined,
                undefined,
            ),
        );
        ConfigManager.current.app = oldAppId;
    }

    async function testUserDeleted(context: Context, appId: string | undefined): Promise<void> {
        const oldAppId = ConfigManager.current.app;
        ConfigManager.current.app = appId;
        const email = '<EMAIL>';
        await xtremSystem.UserEventsManager.userDeleted(context, {
            userId: 1,
            status: NodeStatus.deleted,
            email,
        });
        assert(userDeletedSpy.calledOnce);
        assert(userDeletedSpy.calledWithExactly(appId, Test.defaultTenantId, email));
        ConfigManager.current.app = oldAppId;
    }

    async function testEnableSupportAccess(context: Context, appId: string | undefined): Promise<void> {
        const oldAppId = ConfigManager.current.app;
        const user = await context.user;
        const email = user?.email || '';
        ConfigManager.current.app = appId;
        const validUntil = new Date();
        await xtremSystem.UserEventsManager.enableSupportAccess(context, {
            validUntil,
        });
        assert(enableSupportAccessSpy.calledOnce);
        assert(enableSupportAccessSpy.calledWithExactly(appId, Test.defaultTenantId, email, validUntil, undefined));
        ConfigManager.current.app = oldAppId;
    }

    async function testDisableSupportAccess(context: Context, appId: string | undefined): Promise<void> {
        const oldAppId = ConfigManager.current.app;
        const user = await context.user;
        const email = user?.email || '';
        ConfigManager.current.app = appId;
        await xtremSystem.UserEventsManager.disableSupportAccess(context);
        assert(disableSupportAccessSpy.calledOnce);
        assert(disableSupportAccessSpy.calledWithExactly(appId, Test.defaultTenantId, email));
        ConfigManager.current.app = oldAppId;
    }

    // eslint-disable-next-line no-restricted-syntax
    for (const appId of appIds) {
        it(`userCreated infra event (app: ${appId})`, () =>
            Test.withContext(context => testUserCreated(context, appId), { locale }));
        it(`userModified infra event (app: ${appId})`, () =>
            Test.withContext(context => testUserModified(context, appId), { locale }));
        it(`userDeleted infra event (app: ${appId})`, () =>
            Test.withContext(context => testUserDeleted(context, appId), { locale }));
        it(`enableSupportAccess infra event (app: ${appId})`, () =>
            Test.withContext(context => testEnableSupportAccess(context, appId), { locale }));
        it(`disableSupportAccess infra event (app: ${appId})`, () =>
            Test.withContext(context => testDisableSupportAccess(context, appId), { locale }));
    }
});
