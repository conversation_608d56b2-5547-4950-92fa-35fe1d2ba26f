import { Test } from '@sage/xtrem-core';
import axios from 'axios';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremSystem from '../../../index';
import { updateContext } from '../../fixtures/setup';

describe('Test request-generic', () => {
    let axiosGetStub: sinon.SinonStub;
    let axiosPutStub: sinon.SinonStub;
    let axiosPostStub: sinon.SinonStub;

    before(() => {
        updateContext();

        axiosGetStub = sinon.stub(axios, 'get');
        axiosPutStub = sinon.stub(axios, 'put');
        axiosPostStub = sinon.stub(axios, 'post');
    });

    after(() => {
        axiosGetStub.restore();
        axiosPutStub.restore();
        axiosPostStub.restore();
    });

    it('should handle successful response - 202', async () => {
        axiosGetStub.resolves({ status: 202, statusText: 'Accepted', errors: [] });

        await Test.withContext(async () => {
            const request = new xtremSystem.classes.RequestGeneric({ url: 'http://sage.com' });
            const result = await request.execute();

            assert.equal(result.status, 202);
            assert.equal(result.statusText, 'Accepted');
            assert.equal(result.errors.length, 0);
            assert.equal(result.data, undefined);
            assert.equal(result.headers, undefined);
        });
    });

    it('should handle error response - 404', async () => {
        axiosGetStub.rejects({
            response: {
                status: 404,
                statusText: 'Not found',
                data: { errors: [] },
            },
        });

        await Test.withContext(async () => {
            const request = new xtremSystem.classes.RequestGeneric({ url: 'http://sage.com' });
            await assert.isRejected(request.execute(), '');
        });
    });

    it('should handle error response - 500', async () => {
        axiosGetStub.rejects({
            response: {
                status: 500,
                statusText: 'Internal error',
                data: { errors: [] },
            },
        });

        await Test.withContext(async () => {
            const request = new xtremSystem.classes.RequestGeneric({ url: 'http://sage.com' });

            await assert.isRejected(request.execute(), 'Internal error');
        });
    });

    it('should handle no response', async () => {
        axiosGetStub.rejects({
            status: 888,
            request: {
                url: 'http://sage.com',
                data: { errors: [] },
            },
            message: 'No response',
        });
        await Test.withContext(async () => {
            const request = new xtremSystem.classes.RequestGeneric({ url: 'http://sage.com' });

            await assert.isRejected(request.execute(), 'No response');
        });
    });

    it('should handle request not successful', async () => {
        axiosGetStub.rejects(new Error('Something went wrong'));

        await Test.withContext(async () => {
            const request = new xtremSystem.classes.RequestGeneric({ url: 'http://sage.com' });

            await assert.isRejected(request.execute(), 'Something went wrong');
        });
    });

    it('should handle successful put', async () => {
        axiosPutStub.resolves({ status: 200, statusText: 'OK', errors: [] });

        await Test.withContext(async () => {
            const request = new xtremSystem.classes.RequestGeneric({ method: 'PUT', url: 'http://sage.com' });
            const result = await request.execute();

            assert.equal(result.status, 200);
            assert.equal(result.statusText, 'OK');
            assert.equal(result.errors.length, 0);
            assert.equal(result.data, undefined);
            assert.equal(result.headers, undefined);
        });
    });
    it('should handle successful post', async () => {
        axiosPostStub.resolves({ status: 200, statusText: 'OK', errors: [] });

        await Test.withContext(async () => {
            const request = new xtremSystem.classes.RequestGeneric({ method: 'POST', url: 'http://sage.com' });
            const result = await request.execute();

            assert.equal(result.status, 200);
            assert.equal(result.statusText, 'OK');
            assert.equal(result.errors.length, 0);
            assert.equal(result.data, undefined);
            assert.equal(result.headers, undefined);
        });
    });

    it('should handle unknown method', async () => {
        axiosGetStub.resolves({ status: 200, statusText: 'OK', errors: [] });

        await Test.withContext(async () => {
            const request = new xtremSystem.classes.RequestGeneric({
                method: 'PATCH',
                url: 'http://sage.com',
            });
            await assert.isRejected(request.execute(), 'Invalid method (PATCH)');
        });
    });

    it('should handle blank Url', async () => {
        await Test.withContext(() => {
            const request = new xtremSystem.classes.RequestGeneric();
            assert.throws(() => request.execute(), 'No URL to request');
        });
    });
});
