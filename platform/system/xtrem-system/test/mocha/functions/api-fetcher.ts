import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremSystem from '../../../index';

describe('Axios UT', () => {
    it('api fetcher', () =>
        Test.withContext(
            async context => {
                const result = await xtremSystem.functions.apiFetcher<{ base: string }>(
                    'https://api.exchangeratesapi.io/latest',
                    context,
                );
                assert.isNotNull(result);
                assert.deepEqual(result.base, 'EUR');
            },
            {
                /**
                 * To create get-eur.json file you have to add testAttributes: { createMockData: true }, and to delete the mocks p
                 */
                mocks: ['axios'],
                scenario: 'get-eur',
                directory: __dirname,
            },
        ));
    it('api fetcher fail', async () => {
        await assert.isRejected(xtremSystem.functions.apiFetcher('https://noUrl/latest'));
    });
    //
    /**
     * This test passes, but causes the locale tests to fail. (I assume because the config changes leave lasting side-effects???)
     * Skip for now - TODO : See to reactivate it !
     * No axios response file created
     */
    it.skip('api fetcher with config', async () => {
        let maximumTriesCalled = false;
        Test.patchConfig({
            packages: {
                '@sage/xtrem-system': {
                    retry: {
                        get maximumTries() {
                            maximumTriesCalled = true; // Confirms that the value was loaded from config.
                            return 3;
                        },
                        timeoutBetweenTries: 500,
                    },
                },
            },
        });
        await Test.withContext(
            async context => {
                const result = await xtremSystem.functions.apiFetcher<{ base: string }>(
                    'https://api.exchangeratesapi.io/latest',
                    context,
                );
                assert.isNotNull(result);
                assert.deepEqual(result.base, 'EUR');
                assert.equal(maximumTriesCalled, true);
            },
            {
                mocks: ['axios'],
                scenario: 'apiFetcher',
                directory: __dirname,
            },
        );
    });
});
