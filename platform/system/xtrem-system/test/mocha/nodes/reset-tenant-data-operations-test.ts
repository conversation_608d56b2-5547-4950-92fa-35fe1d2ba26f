import { Context, Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremSystem from '../../../lib/index';

describe('resetTenantDocuments should add an error to context when failing', () => {
    it('should fail resetting tenant data documents and call addDiagnoseAtPath on context', () =>
        Test.withContext(async (context: Context) => {
            await xtremSystem.nodes.SysTenant.resetTenantDocuments(context, '');
            assert.deepEqual(context.diagnoses.length, 1);
        }));
});
