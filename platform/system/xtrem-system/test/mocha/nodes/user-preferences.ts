import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremSystem from '../../../lib/index';

describe('User preference node', () => {
    it('Return all service options that are on', () =>
        Test.withContext(async context => {
            assert.deepEqual(await xtremSystem.nodes.UserPreferences.activeServiceOptions(context), [
                {
                    optionName: 'optionOnDisableError',
                },
                {
                    optionName: 'showCaseActiveByDefault',
                },
                {
                    optionName: 'showCaseOptionMutation',
                },
            ]);
        }));
});
