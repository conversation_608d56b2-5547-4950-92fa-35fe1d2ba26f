import { ConfigManager, Test } from '@sage/xtrem-core';
import { AuthenticationServiceConfig } from '@sage/xtrem-shared/lib/config';
import { assert } from 'chai';
import * as path from 'path';
import * as xtremSystem from '../../../lib/index';
import { Sinon } from '../../helpers';

describe('SysDeviceToken node', () => {
    beforeEach(() => {
        ConfigManager.current.authentication = <AuthenticationServiceConfig>{
            ssl: {
                key: path.join(__dirname, '../../../../test/mocks/authcrt/tls.key'),
                cert: path.join(__dirname, '../../../../test/mocks/authcrt/tls.crt'),
                ca: path.join(__dirname, '../../../../test/mocks/authcrt/ca.crt'),
            },
        };

        Sinon.registerMocks([
            {
                type: 'stub',
                reference: xtremSystem.functions.deviceTokenLib,
                name: 'getTokenExpirationDate',
                returns: '1772212853',
            },
        ]);
    });

    afterEach(() => {
        Sinon.removeMocks();
    });

    it('mutation createDeviceToken - fails when deviceToken cookie is already created', () =>
        Test.withContext(
            context => {
                Sinon.registerMocks([
                    {
                        type: 'stub',
                        reference: xtremSystem.functions.deviceTokenLib,
                        name: 'generateTokenId',
                        returns: 'nbi5wpZooOlPzX2JvTcNj',
                    },
                ]);

                // manually overridding the context so that I create the same conditions as a real request
                (context as any).request.cookies = (context as any).request.cookies ?? {};
                (context as any).request.cookies[`device_token_${context.tenantId}`] = 'jwtDeviceTokenCookieValue';

                assert.throws(
                    () => xtremSystem.nodes.SysDeviceToken.createDeviceToken(context),
                    'Current device already has a token created.',
                );
            },
            {
                testActiveServiceOptions: [xtremSystem.serviceOptions.sysDeviceToken],
            },
        ));

    it('mutation createDeviceToken - fails when authentication config files are missing', () =>
        Test.withContext(
            async context => {
                if (ConfigManager.current.authentication) {
                    ConfigManager.current.authentication.ssl.ca = '';
                }

                Sinon.registerMocks([
                    {
                        type: 'stub',
                        reference: xtremSystem.functions.deviceTokenLib,
                        name: 'generateTokenId',
                        returns: 'qwi5wpZooOlPzX2JvTcNj',
                    },
                ]);

                // manually overridding the context so that I create the same conditions as a real request
                (context as any).isHttp = () => true;

                await assert.isRejected(
                    xtremSystem.nodes.SysDeviceToken.createDeviceToken(context),
                    Error,
                    'Request failed with status code 401 Unauthorized request to a protected API',
                );
            },
            {
                mocks: ['axios'],
                scenario: 'device-token-creation-auth-error',
                directory: __dirname.concat('../../../mocks'),
                testActiveServiceOptions: [xtremSystem.serviceOptions.sysDeviceToken],
            },
        ));

    it('mutation createDeviceToken - fails when calling api/device - unauthorized', () =>
        Test.withContext(
            async context => {
                // manually overridding the context so that I create the same conditions as a real request
                (context as any).isHttp = () => true;

                Sinon.registerMocks([
                    {
                        type: 'stub',
                        reference: xtremSystem.functions.deviceTokenLib,
                        name: 'generateTokenId',
                        returns: 'qwi5wpZooOlPzX2JvTcNj',
                    },
                ]);

                await assert.isRejected(
                    xtremSystem.nodes.SysDeviceToken.createDeviceToken(context),
                    Error,
                    'Request failed with status code 401',
                );
            },
            {
                mocks: ['axios'],
                scenario: 'device-token-creation-auth-error',
                directory: __dirname.concat('../../../mocks'),
                testActiveServiceOptions: [xtremSystem.serviceOptions.sysDeviceToken],
            },
        ));

    it('mutation createDeviceToken - isFulfilled', () =>
        Test.withContext(
            async context => {
                // manually overridding the context so that I create the same conditions as a real request
                (context as any).isHttp = () => true;
                (context as any).response.cookie = () => true;

                Sinon.registerMocks([
                    {
                        type: 'stub',
                        reference: xtremSystem.functions.deviceTokenLib,
                        name: 'generateTokenId',
                        returns: 'nbi5wpZooOlPzX2JvTcNj',
                    },
                ]);

                await assert.isFulfilled(xtremSystem.nodes.SysDeviceToken.createDeviceToken(context));
            },
            {
                mocks: ['axios'],
                scenario: 'device-token-creation',
                directory: __dirname.concat('../../../mocks'),
                testActiveServiceOptions: [xtremSystem.serviceOptions.sysDeviceToken],
            },
        ));

    it('mutation deleteDeviceToken - isFulfilled', () =>
        Test.withContext(
            async context => {
                // manually overridding the context so that I create the same conditions as a real request
                (context as any).isHttp = () => true;
                (context as any).response.cookie = () => true;

                const tokenId = 'nbi5wpZooOlPzX2JvTcNj';

                await assert.isFulfilled(xtremSystem.nodes.SysDeviceToken.deleteDeviceToken(context, tokenId));
            },
            {
                mocks: ['axios'],
                scenario: 'device-token-deletion',
                directory: __dirname.concat('../../../mocks'),
                testActiveServiceOptions: [xtremSystem.serviceOptions.sysDeviceToken],
            },
        ));
});
