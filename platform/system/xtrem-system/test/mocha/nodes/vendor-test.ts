import { Test } from '@sage/xtrem-core';
import { assert, expect } from 'chai';
import { SysVendor } from '../../../lib/nodes/sys-vendor';

describe('_vendor property tests', () => {
    it('SysVendor should not contain _vendor column', () =>
        Test.withContext(async context => {
            const newRecord = await context.create(SysVendor, {
                name: 'ACME',
                description: 'ACME corp.',
            });
            await newRecord.$.save();
            const savedRecord = (await context.query(SysVendor).toArray())[0];
            expect(await savedRecord.$.payload()).to.not.have.property('_vendor');
        }));

    it('factories with hasVendorProperty should have _vendor property', () =>
        Test.withContext(context => {
            const factories = context.application.getAllFactories().filter(f => f.hasVendorProperty);
            expect(factories.length).to.be.greaterThan(0);
            factories.forEach(factory => {
                assert.isNotNull(factory.propertiesByName._vendor);
            });
        }));

    it('non platform or shared factories which are setup nodes and have a naturalKey should have _vendor property', () =>
        Test.withContext(context => {
            const factories = context.application
                .getAllFactories()
                .filter(f => !(f.isSharedByAllTenants || f.isPlatformNode) && f.isSetupNode);
            expect(factories.length).to.be.greaterThan(0);
            factories.forEach(factory => {
                assert.isNotNull(factory.propertiesByName._vendor);
            });
        }));

    it('platform or shared factories should not have _vendor property', () =>
        Test.withContext(context => {
            const factories = context.application
                .getAllFactories()
                .filter(f => f.isSharedByAllTenants || f.isPlatformNode);
            expect(factories.length).to.be.greaterThan(0);
            factories.forEach(factory => {
                assert.isUndefined(factory.propertiesByName._vendor);
            });
        }));
});
