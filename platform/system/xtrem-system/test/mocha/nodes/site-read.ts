import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremSystem from '../../../lib/index';

describe('Site node', () => {
    before(() => {});

    it('Reading Site node', () =>
        Test.withContext(async context => {
            const mySite = await context.read(xtremSystem.nodes.Site, { id: 'US001' });
            assert.instanceOf(mySite, xtremSystem.nodes.Site);
            assert.equal(await mySite.name, 'Chem. Atlanta');
            assert.equal(await mySite.description, 'US Chemicals Atlanta');
            assert.strictEqual(await mySite.isActive, true);
        }));
});
