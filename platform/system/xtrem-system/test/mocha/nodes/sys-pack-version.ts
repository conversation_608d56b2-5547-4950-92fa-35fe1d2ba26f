import { ConfigManager, Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremSystem from '../../../index';
import { SysPackageManager } from '../../../lib/services/sys-package-manager';

describe('Package custom query', () => {
    it('Use the custom query to get the tenantId and version without sumologic link', async () => {
        Test.patchConfig({
            clusterId: 'cluster-ci',
            packages: {
                '@sage/xtrem-system': {
                    unsafeDevTroubleshooting: false,
                },
            },
        });

        await Test.withContext(async context => {
            const packageManager = SysPackageManager.fromContext(context);
            await packageManager.updatePackageVersion(context, {
                name: '@sage/xtrem-system',
                version: '1.0.0',
                isHidden: true,
                isReleased: false,
            });
            const tenantInformation = await xtremSystem.nodes.SysTenant.getTenantInformation(context);
            assert.equal(tenantInformation.version, context.application.rootAbout.version);
            assert.equal(tenantInformation.tenantId, '777777777777777777777');
            assert.equal(tenantInformation.sumologicLink, '');
        });
    });

    it('Use the custom query to get the tenantId and version  with sumologic link', async () => {
        Test.patchConfig({
            packages: {
                '@sage/xtrem-system': {
                    unsafeDevTroubleshooting: true,
                },
            },
        });
        ConfigManager.current.clusterId = 'cluster-ci';
        process.env.XTREM_ENV = 'local';
        await Test.withContext(async context => {
            const packageManager = SysPackageManager.fromContext(context);
            await packageManager.updatePackageVersion(context, {
                name: '@sage/xtrem-system',
                version: '1.0.0',
                isHidden: true,
                isReleased: false,
            });
            const tenantInformation = await xtremSystem.nodes.SysTenant.getTenantInformation(context);
            assert.equal(tenantInformation.version, context.application.rootAbout.version);
            assert.equal(tenantInformation.tenantId, '777777777777777777777');
            assert.equal(
                tenantInformation.sumologicLink,
                'https://service.sumologic.com/ui/#/search/create?query=cluster=%22xtrem_local%22%20namespace=%22cluster-ci%22%20_index=infrequent_ephemeral%20%7C%20where%20log.tenantId=%22777777777777777777777%22&startTime=-15m',
            );
        });
    });
});
