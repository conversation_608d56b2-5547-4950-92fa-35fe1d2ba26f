import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import { SysClientUserSettings } from '../../../lib/nodes/sys-client-user-settings';
import { User } from '../../../lib/nodes/user';

describe('SysClientUserSettings tests', () => {
    it('create sysClientUserSettings', async () => {
        await Test.withContext(async context => {
            const user = await context.read(User, { email: '<EMAIL>' });
            const screenId = 'TestView';
            const elementId = 'Name';
            const isSelected = true;

            const sysClientUserSettings = await context.create(SysClientUserSettings, {
                user,
                screenId,
                elementId,
                isSelected,
            });
            assert.isTrue(await sysClientUserSettings.$.trySave());
        });
    });

    it('create sysClientUserSettings with default user', async () => {
        await Test.withContext(async context => {
            const screenId = 'TestView';
            const elementId = 'Name';
            const isSelected = true;

            const sysClientUserSettings = await context.create(SysClientUserSettings, {
                screenId,
                elementId,
                isSelected,
            });
            assert.isTrue(await sysClientUserSettings.$.trySave());
        });
    });

    it('create sysClientUserSettings fails', async () => {
        await Test.withContext(async context => {
            const user = await context.read(User, { email: '<EMAIL>' });
            const screenId = 'TestView';
            const elementId = 'Name';
            const isSelected = true;

            const sysClientUserSettings = await context.create(SysClientUserSettings, {
                user,
                screenId,
                elementId,
                isSelected,
            });
            assert.isTrue(await sysClientUserSettings.$.trySave());

            const sysClientUserSettingsDuplicate = await context.create(SysClientUserSettings, {
                user,
                screenId,
                elementId,
                isSelected,
            });
            assert.isFalse(await sysClientUserSettingsDuplicate.$.trySave());
            assert.deepEqual(context.diagnoses, [
                {
                    message: 'You can only select one.',
                    path: ['isSelected'],
                    severity: 3,
                },
            ]);
        });
    });
});
