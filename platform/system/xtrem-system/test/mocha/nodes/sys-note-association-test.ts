import { Context, Test, TextStream } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremSystem from '../../../lib';
import { TestNotesDocument } from '../../fixtures/lib/nodes/test-notes-document';

async function initSysNote(context: Context): Promise<{ docId: number; notes: { _id: number }[] }[]> {
    // create a sys note
    const note = await context.create(xtremSystem.nodes.SysNote, {
        title: 'My Notes 1',
        content: TextStream.fromString('<html><head><title>Notes 1</title></head></html>', 'text/html'),
    });
    await note.$.save();

    assert.equal((await note.content).value, '<html><head><title>Notes 1</title></head></html>', 'text/html');

    // create second sys note
    const note2 = await context.create(xtremSystem.nodes.SysNote, {
        title: 'My Notes 2',
        content: TextStream.fromString('<html><head><title>Notes 2</title></head></html>', 'text/html'),
    });
    await note2.$.save();

    assert.equal((await note2.content).value, '<html><head><title>Notes 2</title></head></html>', 'text/html');

    // create third sys note
    const note3 = await context.create(xtremSystem.nodes.SysNote, {
        title: 'My Notes 3',
        content: TextStream.fromString('<html><head><title>Notes 3</title></head></html>', 'text/html'),
    });
    await note3.$.save();

    assert.equal((await note3.content).value, '<html><head><title>Notes 3</title></head></html>', 'text/html');

    // create a test document
    const testDocument = await context.create(TestNotesDocument, {
        code: 'code 1',
        _notes: [
            {
                note: note._id,
            },
            {
                note: note2._id,
            },
        ],
    });
    await testDocument.$.save();

    // create a test document
    const testDocument2 = await context.create(TestNotesDocument, {
        code: 'code 2',
        _notes: [
            {
                note: note3._id,
            },
        ],
    });
    await testDocument2.$.save();

    const notes = await testDocument._notes
        .map(a => ({
            _id: a._id,
        }))
        .toArray();
    assert.equal(notes.length, 2);
    await testDocument._notes.forEach(async sysNoteAssociation => {
        assert.equal(await sysNoteAssociation.sourceNodeName, 'TestNotesDocument');
        assert.equal(await sysNoteAssociation.sourceNodeId, testDocument._id);
    });

    const notes2 = await testDocument2._notes
        .map(a => ({
            _id: a._id,
        }))
        .toArray();
    assert.equal(notes2.length, 1);
    await testDocument2._notes.forEach(async sysNoteAssociation => {
        assert.equal(await sysNoteAssociation.sourceNodeName, 'TestNotesDocument');
        assert.equal(await sysNoteAssociation.sourceNodeId, testDocument2._id);
    });
    return [
        { docId: testDocument._id, notes },
        { docId: testDocument2._id, notes: notes2 },
    ];
}

describe('Notes Node Associations', () => {
    it('can create notes association via _notes collection of document', () =>
        Test.withContext(context => initSysNote(context)));

    it('can update a document', async () => {
        await Test.withContext(async context => {
            const notesDocuments = await initSysNote(context);
            const { docId, notes: notesAssociation } = notesDocuments[0];
            const { docId: doc2Id } = notesDocuments[1];

            // create fourth note
            const note4 = await context.create(xtremSystem.nodes.SysNote, {
                title: 'My Note 4',
                content: TextStream.fromString('<html><body>note 4</body></html>', 'text/html'),
            });
            await note4.$.save();

            const document = await context.read(TestNotesDocument, { _id: docId }, { forUpdate: true });
            // update the notes association on the test document
            await document.$.set({
                _notes: [
                    {
                        _id: notesAssociation[1]._id,
                        note: note4._id,
                        _action: 'update',
                    },
                ],
            });
            await document.$.save();

            const updatedDocument = await context.read(TestNotesDocument, { _id: docId }, { forUpdate: true });
            const updatedDocument2 = await context.read(TestNotesDocument, { _id: doc2Id }, { forUpdate: true });

            await updatedDocument._notes.forEach(async notesAssociationInner => {
                assert.equal(await notesAssociationInner.sourceNodeName, 'TestNotesDocument');
                assert.equal(await notesAssociationInner.sourceNodeId, docId);
            });

            // check if we add a new attachment, the other document is not affected
            await updatedDocument2._notes.forEach(async notesAssociationInner => {
                assert.equal(await notesAssociationInner.sourceNodeName, 'TestNotesDocument');
                assert.equal(await notesAssociationInner.sourceNodeId, doc2Id);
            });

            assert.isTrue(
                await updatedDocument._notes.some(async notesAssociationInner => {
                    return (await notesAssociationInner.note)._id === note4._id;
                }),
            );

            const queryResult = await context
                .query(TestNotesDocument, {
                    filter: { _notes: { _atLeast: 1, note: note4._id } },
                })
                .toArray();
            assert.equal(queryResult.length, 1);
        });
    });

    it('can delete a note via update of collection', async () => {
        await Test.withContext(async context => {
            const notesDocuments = await initSysNote(context);
            const { docId, notes } = notesDocuments[0];
            const { docId: doc2Id } = notesDocuments[1];

            const document = await context.read(TestNotesDocument, { _id: docId }, { forUpdate: true });
            // delete the attachment association on the test document
            await document.$.set({
                _notes: [
                    {
                        _id: notes[1]._id,
                        _action: 'delete',
                    },
                ],
            });
            await document.$.save();

            assert.equal(await document._notes.length, 1);

            await document._notes.forEach(async notesAssociation => {
                assert.equal(await notesAssociation.sourceNodeName, 'TestNotesDocument');
                assert.equal(await notesAssociation.sourceNodeId, docId);
            });

            const document2 = await context.read(TestNotesDocument, { _id: doc2Id }, { forUpdate: true });

            // check if we delete a note, the other document is not affected
            await document2._notes.forEach(async notesAssociation => {
                assert.equal(await notesAssociation.sourceNodeName, 'TestNotesDocument');
                assert.equal(await notesAssociation.sourceNodeId, doc2Id);
            });
        });
    });
});
