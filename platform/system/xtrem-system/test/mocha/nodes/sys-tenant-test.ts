import { Context, Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import { nanoid } from 'nanoid';
import { SysCustomer } from '../../../lib/nodes/sys-customer';
import { SysTenant } from '../../../lib/nodes/sys-tenant';

async function createSysCustomer(context: Context, customerId: string): Promise<SysCustomer> {
    const customer = await context.create(SysCustomer, {
        name: 'Sys Customer',
        customerId,
    });
    await customer.$.save();
    return customer;
}

describe('SysTenant property tests', () => {
    it("tenant's name should contain only latin characters", async () => {
        const newTenantId = nanoid();
        await Test.withUncommittedContext(async context => {
            const newSysCustomer = await createSysCustomer(context, newTenantId);
            const newSysTenant = await context.create(SysTenant, {
                tenantId: newTenantId,
                name: '平仮名',
                customer: newSysCustomer,
            });
            await assert.isRejected(newSysTenant.$.save(), 'The record was not created.');
        });
    });
    it("tenant's directory name should be in kebab ", async () => {
        const newTenantId = nanoid();
        await Test.withUncommittedContext(async context => {
            const newSysCustomer = await createSysCustomer(context, newTenantId);
            const newSysTenant = await context.create(SysTenant, {
                tenantId: newTenantId,
                name: 'sys tenant',
                customer: newSysCustomer,
                directoryName: 'non kebab case',
            });
            await assert.isRejected(newSysTenant.$.save(), 'The record was not created.');
        });
    });
    it("tenant's directoryName should be converted to kebab case", async () => {
        const newTenantId = '8'.repeat(21);
        await Test.withUncommittedContext(async context => {
            const newSysCustomer = await createSysCustomer(context, newTenantId);
            const newSysTenant = await context.create(SysTenant, {
                tenantId: newTenantId,
                name: 'my awesome tenant',
                customer: newSysCustomer,
            });
            await newSysTenant.$.save();

            assert.equal(
                await (
                    await context.read(SysTenant, {
                        tenantId: newTenantId,
                    })
                ).directoryName,
                'my-awesome-tenant',
            );
        });
    });

    it("tenant's directoryName should be unique", async () => {
        const newTenantId1 = nanoid();
        const newTenantId2 = nanoid();
        await Test.withContext(async context => {
            const newSysCustomer = await createSysCustomer(context, newTenantId1);

            const newSysTenant = await context.create(SysTenant, {
                tenantId: newTenantId1,
                name: 'my awesome tenant',
                customer: newSysCustomer,
            });
            await newSysTenant.$.save();

            const secondSysTenant = await context.create(SysTenant, {
                tenantId: newTenantId2,
                name: 'my awesome tenant',
                customer: newSysCustomer,
            });

            await assert.isRejected(
                secondSysTenant.$.save(),
                'duplicate key value violates unique constraint "sys_tenant_ind1": Key (directory_name)=(my-awesome-tenant) already exists.',
            );
        });
    });
});
