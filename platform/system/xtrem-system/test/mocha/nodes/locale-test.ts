import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremSystem from '../../../lib/index';

describe('Locale node', () => {
    it('Throws an error if wrong locale format is supplied', () =>
        Test.withContext(async context => {
            await assert.isRejected(
                context.create(xtremSystem.nodes.Locale, { id: 'wrong' }),
                'Locale wrong not supported',
            );
        }));
    it('creates an entry and sets the language', () =>
        Test.withContext(async context => {
            const newLocale = await context.create(xtremSystem.nodes.Locale, {
                id: 'jp-JP',
            });
            await newLocale.$.save();
            const readLocale = await context.read(xtremSystem.nodes.Locale, { _id: newLocale._id });
            assert.equal(await readLocale.id, 'jp-JP');
            assert.equal(await readLocale.language, 'jp');
            assert.equal(await readLocale.isDefaultLocale, false);
            assert.equal(await readLocale.isLanguageMasterLocale, true);
        }));
    it('updates the default locale to the last entry set as default', () =>
        Test.withContext(async context => {
            let readLocale = await context.read(xtremSystem.nodes.Locale, { id: 'en-US' });
            assert.equal(await readLocale.id, 'en-US');
            assert.equal(await readLocale.isDefaultLocale, true);
            assert.equal(await readLocale.isLanguageMasterLocale, true);
            const newLocale = await context.create(xtremSystem.nodes.Locale, {
                id: 'en-AU',
                isDefaultLocale: true,
            });
            await newLocale.$.save();
            readLocale = await context.read(xtremSystem.nodes.Locale, { id: 'en-US' });
            assert.equal(await readLocale.id, 'en-US');
            assert.equal(await readLocale.isDefaultLocale, false);
            assert.equal(await readLocale.isLanguageMasterLocale, false);
            readLocale = await context.read(xtremSystem.nodes.Locale, { id: 'en-AU' });
            assert.equal(await readLocale.id, 'en-AU');
            assert.equal(await readLocale.isDefaultLocale, true);
            assert.equal(await readLocale.isLanguageMasterLocale, true);
        }));
    it('updates the master language locale to the last entry set for a language', () =>
        Test.withContext(async context => {
            let readLocale = await context.read(xtremSystem.nodes.Locale, { id: 'en-US' });
            assert.equal(await readLocale.id, 'en-US');
            assert.equal(await readLocale.isLanguageMasterLocale, true);
            let newLocale = await context.create(xtremSystem.nodes.Locale, {
                id: 'en-AU',
                isDefaultLocale: true,
                isLanguageMasterLocale: true,
            });
            await newLocale.$.save();
            newLocale = await context.create(xtremSystem.nodes.Locale, {
                id: 'jp-JP',
            });
            await newLocale.$.save();
            readLocale = await context.read(xtremSystem.nodes.Locale, { id: 'en-US' });
            assert.equal(await readLocale.id, 'en-US');
            assert.equal(await readLocale.isLanguageMasterLocale, false);
            readLocale = await context.read(xtremSystem.nodes.Locale, { id: 'en-AU' });
            assert.equal(await readLocale.id, 'en-AU');
            assert.equal(await readLocale.isLanguageMasterLocale, true);
            readLocale = await context.read(xtremSystem.nodes.Locale, { id: 'jp-JP' });
            assert.equal(await readLocale.id, 'jp-JP');
            assert.equal(await readLocale.isLanguageMasterLocale, true);
        }));
    it('throws if the master language locale is different from the default locale, for the same language', () =>
        Test.withContext(async context => {
            await assert.isRejected(
                context.create(xtremSystem.nodes.Locale, {
                    id: 'wrong',
                }),
            );
        }));
});
