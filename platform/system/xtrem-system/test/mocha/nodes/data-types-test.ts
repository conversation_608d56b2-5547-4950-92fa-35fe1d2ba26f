import { ConfigManager, Diagnose, Test } from '@sage/xtrem-core';
import { DeploymentMode, ValidationSeverity } from '@sage/xtrem-shared';
import { assert } from 'chai';
import { TestSystemDataTypes } from '../../fixtures/lib/nodes/system-data-types';

async function testUrl(
    url: string,
    deploymentMode: DeploymentMode | undefined,
    expectedDiagnoses?: Diagnose[],
    expectedUrl?: string,
) {
    const currentDeploymentMode = ConfigManager.current.deploymentMode;
    ConfigManager.current.deploymentMode = deploymentMode;
    await Test.withContext(async context => {
        const node = await context.create(TestSystemDataTypes, {
            code: 'test-url',
            url,
            baseUrl: url,
        });
        const valid = await node.$.control();
        assert.equal(valid, expectedDiagnoses == null || expectedDiagnoses.length === 0);
        assert.deepEqual(node.$.context.diagnoses, expectedDiagnoses ?? []);
        const expectedSavedUrl = expectedUrl ?? url;
        if (expectedSavedUrl) {
            assert.equal(await node.url, expectedSavedUrl);
            if (URL.canParse(expectedSavedUrl)) {
                const parsedUrl = new URL(expectedSavedUrl);
                assert.equal(await node.baseUrl, parsedUrl.origin === 'null' ? expectedSavedUrl : parsedUrl.origin);
            } else {
                assert.equal(await node.baseUrl, expectedSavedUrl);
            }
        }
    });
    ConfigManager.current.deploymentMode = currentDeploymentMode;
}

function getDiagnoses(message: string, message2?: string): Diagnose[] {
    return [
        {
            severity: ValidationSeverity.error,
            path: ['url'],
            message,
        },
        {
            severity: ValidationSeverity.error,
            path: ['baseUrl'],
            message: message2 ?? message,
        },
    ];
}

describe('System DataTypes', () => {
    describe('Dev mode', () => {
        const deploymentMode = 'development';
        it('Https Url protocol is allowed', () =>
            testUrl('https://example.com', deploymentMode, [], 'https://example.com/'));
        it('Https Url protocol with path is allowed', () =>
            testUrl('https://example.com/xtrem/api', deploymentMode, [], 'https://example.com/xtrem/api'));
        it('Http Url protocol is allowed', () =>
            testUrl('http://example.com', deploymentMode, [], 'http://example.com/'));
        it('Http Url protocol with path is allowed', () =>
            testUrl('http://example.com/xtrem/api', deploymentMode, [], 'http://example.com/xtrem/api'));
        it('malformed Https Url protocol is allowed', () =>
            testUrl('https:/example.com', deploymentMode, [], 'https://example.com/'));
        it('malformed Http Url protocol is allowed', () =>
            testUrl('http:/example.com', deploymentMode, [], 'http://example.com/'));
        it('malformed wrong protocol is not allowed', () =>
            testUrl(
                'wrong-protocol:/example.com',
                deploymentMode,
                getDiagnoses("Url 'wrong-protocol:/example.com' got protocol wrong-protocol, expecting http, https."),
            ));
        it('wrong protocol is not allowed', () =>
            testUrl(
                'wrong-protocol://example.com',
                deploymentMode,
                getDiagnoses("Url 'wrong-protocol://example.com' got protocol wrong-protocol, expecting http, https."),
            ));
        it('invalid url is not allowed', () =>
            testUrl('/path/is/invalid', deploymentMode, getDiagnoses("Incorrect URL '/path/is/invalid'.")));
    });

    describe('Prod mode', () => {
        const deploymentMode = 'production';
        it('Https Url protocol is allowed', () =>
            testUrl('https://example.com', deploymentMode, [], 'https://example.com/'));
        it('Https Url protocol with path is allowed', () =>
            testUrl('https://example.com/xtrem/api', deploymentMode, [], 'https://example.com/xtrem/api'));
        it('Http Url protocol is not allowed', () =>
            testUrl(
                'http://example.com',
                deploymentMode,
                getDiagnoses(
                    "Url 'http://example.com/' got protocol http, expecting https.",
                    "Url 'http://example.com' got protocol http, expecting https.",
                ),
                'http://example.com/',
            ));
        it('Http Url protocol with path is allowed', () =>
            testUrl(
                'http://example.com/xtrem/api',
                deploymentMode,
                getDiagnoses(
                    "Url 'http://example.com/xtrem/api' got protocol http, expecting https.",
                    "Url 'http://example.com' got protocol http, expecting https.",
                ),
                'http://example.com/xtrem/api',
            ));
        it('malformed Https Url protocol is allowed', () =>
            testUrl('https:/example.com', deploymentMode, [], 'https://example.com/'));
        it('malformed Http Url protocol is not allowed', () =>
            testUrl(
                'http:/example.com',
                deploymentMode,
                getDiagnoses(
                    "Url 'http://example.com/' got protocol http, expecting https.",
                    "Url 'http://example.com' got protocol http, expecting https.",
                ),
                'http://example.com/',
            ));
        it('wrong protocol is not allowed', () =>
            testUrl(
                'wrong-protocol://example.com',
                deploymentMode,
                getDiagnoses("Url 'wrong-protocol://example.com' got protocol wrong-protocol, expecting https."),
            ));
        it('invalid url is not allowed', () =>
            testUrl('/path/is/invalid', deploymentMode, getDiagnoses("Incorrect URL '/path/is/invalid'.")));
    });
});
