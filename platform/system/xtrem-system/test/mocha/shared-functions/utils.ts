import { assert } from 'chai';
import { linkToPage } from '../../../lib/shared-functions';

describe('Utils', () => {
    it('linkToPage', () => {
        const targetPage = '@sage/xtrem-purchasing/PurchaseOrder';
        const _id = '12345';

        const linkToPageActualResult = linkToPage(targetPage, _id);
        const linkToPageExpectedResult = `${targetPage}/${btoa(JSON.stringify({ _id }))}`;

        assert.deepStrictEqual(linkToPageActualResult, linkToPageExpectedResult);
    });
});
