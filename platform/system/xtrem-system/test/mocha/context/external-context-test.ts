import { Context, Test, ValidationSeverity } from '@sage/xtrem-core';
import { assert } from 'chai';

describe('context required methods to be external and visible in the .d.ts file', () => {
    it('should be able to see and access the addDiagnoseAtPath method', () =>
        Test.withContext((context: Context) => {
            assert.exists(context.addDiagnoseAtPath);
            assert.equal(0, context.diagnoses.length);
            context.addDiagnoseAtPath(ValidationSeverity.error, [''], '');
            assert.equal(1, context.diagnoses.length);
        }));
});
