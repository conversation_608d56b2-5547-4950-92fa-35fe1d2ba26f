{"Create an User with missing email error": {"input": {"properties": {"email": "", "firstName": "<PERSON>", "lastName": "<PERSON>"}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "string cannot be empty", "path": ["email"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremSystem", "user", "create"]}]}}, "Create an User with missing first name error": {"input": {"properties": {"email": "<EMAIL>", "firstName": "", "lastName": "<PERSON>"}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "string cannot be empty", "path": ["firstName"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremSystem", "user", "create"]}]}}, "Create an User with missing last name error": {"input": {"properties": {"email": "<EMAIL>", "firstName": "<PERSON>", "lastName": ""}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "string cannot be empty", "path": ["lastName"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremSystem", "user", "create"]}]}}, "Create a User with an already existing email error": {"input": {"properties": {"email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON> <PERSON> e Mello <PERSON>"}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The operation failed because the record already exists.", "path": ["email"], "severity": 4}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremSystem", "user", "create"]}]}}, "Create an User with existing operatorId": {"input": {"properties": {"email": "<EMAIL>", "firstName": "User2", "lastName": "Operator", "operatorCode": "1q2w3e"}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"severity": 3, "path": ["operatorCode"], "message": "An operator PIN code needs to be unique."}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremSystem", "user", "create"]}]}, "envConfigs": {"testActiveServiceOptions": ["sysDeviceToken"]}}}