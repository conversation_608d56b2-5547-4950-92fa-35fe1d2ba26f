{"Update a User with missing first name error": {"input": {"properties": {"_id": "#<EMAIL>", "firstName": ""}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "string cannot be empty", "path": ["firstName"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not updated.", "path": ["xtremSystem", "user", "update"]}]}}, "Update a User with missing last name error": {"input": {"properties": {"_id": "#<EMAIL>", "firstName": "<PERSON>", "lastName": ""}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "string cannot be empty", "path": ["lastName"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not updated.", "path": ["xtremSystem", "user", "update"]}]}}, "Update a User with an already existing email error": {"input": {"properties": {"_id": "#<EMAIL>", "email": "<EMAIL>"}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "User.email: cannot set value on frozen property", "path": ["email"], "severity": 4}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not updated.", "path": ["xtremSystem", "user", "update"]}]}}, "Update an Operator User with pincode less than four error": {"input": {"properties": {"_id": "#<EMAIL>", "operatorCode": "12"}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "An operator PIN code needs to have a minimum of 4 characters.", "path": ["operatorCode"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not updated.", "path": ["xtremSystem", "user", "update"]}]}, "envConfigs": {"testActiveServiceOptions": ["sysDeviceToken"]}}}