/*
 *
 * This helper provides a simple way to manage sinon spies, mocks and stubs,
 * It provides a single api to register & restore your functions/methods in bulk.
 *
 */

import { Dict } from '@sage/xtrem-core';
import * as _ from 'lodash';
import { SinonMock, SinonSpy, SinonStub, spy, stub } from 'sinon';

interface AnyClass extends Function {
    new (...args: any[]): any;
}

type AnyMock = SinonSpy | SinonStub | SinonMock;

interface Mock {
    isMockActivated: boolean;
    isMethod?: true;
    isStatic?: true;
    reference?: any;
    type?: 'spy' | 'stub' | 'mock';
    mock: AnyMock | null;
    returns?: any | null;
}

interface EnabledMock extends Mock {
    isMockActivated: true;
    mock: AnyMock;
    returns?: any;
}

interface DeactivatedMock extends Mock {
    isMockActivated: false;
    mock: null;
    returns?: null;
}

interface MethodMock extends Mock {
    reference: AnyClass;
    class: AnyClass;
    isMethod: true;
}

interface StaticMethodMock extends MethodMock {
    isStatic: true;
}

interface FunctionMock extends Mock {
    reference: any;
    isMethod: undefined;
}

type MockDeclaration<T extends Mock> = Omit<T, 'isMockActivated' | 'mock' | 'reference'> & {
    name?: string;
    class?: any;
    reference?: any;
};

type MockRegisterObject<T extends Mock> = Omit<MockDeclaration<T>, 'name'> & {
    name: string;
};

type MethodMockRegisterObjects = MockRegisterObject<MethodMock>;

type StaticMethodMockRegisterObjects = MockRegisterObject<StaticMethodMock>;

type FunctionMockRegisterObjects = MockRegisterObject<FunctionMock>;

const mocks: Dict<Mock> = {};

function mockIsActivated(mock: Mock): mock is EnabledMock & Mock {
    return mock.isMockActivated;
}

function isMethodMockRegisterObject(
    registerObject: MockDeclaration<Mock>,
): registerObject is MethodMockRegisterObjects {
    return !!registerObject.isMethod;
}

function isStaticMethodMockRegisterObject(
    registerObject: MockDeclaration<MethodMock>,
): registerObject is StaticMethodMockRegisterObjects {
    return !!registerObject.isStatic;
}

function isFunctionMockRegisterObject(
    registerObject: MockDeclaration<Mock>,
): registerObject is FunctionMockRegisterObjects {
    return !registerObject.isMethod;
}

function storeMockData(name: string, registerObject: EnabledMock) {
    mocks[name] = registerObject;
}

function registerMethod(mockName: string, mockDeclaration: MockDeclaration<MethodMock>): EnabledMock {
    let reference = mockDeclaration.class as AnyClass;

    const methodName = mockName as keyof typeof reference;

    delete (mockDeclaration as MockDeclaration<Mock>).class;

    if (!isStaticMethodMockRegisterObject(mockDeclaration)) {
        reference = reference.prototype;
    }

    let mock: SinonSpy | SinonStub | SinonMock;

    switch (mockDeclaration.type) {
        case 'spy':
            mock = spy(reference, methodName);
            break;
        case 'stub':
            mock = stub(reference, methodName);

            if (mockDeclaration.returns) {
                (mock as SinonStub).returns(mockDeclaration.returns);
            }
            break;
        default:
            throw new Error("This mock type isn't implemented for methods");
    }

    const mockData: EnabledMock = {
        ...mockDeclaration,
        isMockActivated: true,
        mock,
        reference,
    };

    storeMockData(mockName, mockData);

    return mockData;
}

function registerFunction(mockName: string, mockDeclaration: MockDeclaration<FunctionMock>): EnabledMock {
    let mock: SinonSpy | SinonStub | SinonMock;

    switch (mockDeclaration.type) {
        case 'spy':
            mock = spy(mockDeclaration.reference, mockName);
            break;
        case 'stub':
            mock = stub(mockDeclaration.reference, mockName);

            if (mockDeclaration.returns) {
                (mock as SinonStub).returns(mockDeclaration.returns);
            }
            break;
        default:
            throw new Error('No mock type given');
    }

    const mockData: EnabledMock = {
        ...mockDeclaration,
        isMockActivated: true,
        mock,
    };

    storeMockData(mockName, mockData);

    return mockData;
}

/**
 * Retrieve the mock registered for a function
 * @param mockName the name of the mocked function
 * @returns the mock corresponding
 */
export function getMockData(mockName: keyof typeof mocks): Mock {
    const mock = mocks[mockName];

    if (!mock) {
        throw new Error('This mock is not active.');
    }

    return mock;
}

/**
 * Register in bulk several method/function using Sinon spies, mocks and/or stubs.
 * @param mocksToRegister the list of mocks to register. Each element could have
 *  - name (Mandatory) => the name of the function
 *  - class => the class if the function is a method
 *  - reference => the reference in which the function can be found if it's not a method
 * @returns the list of mock registered
 */
export function registerMocks<AnyMockData extends Mock>(mocksToRegister: MockRegisterObject<AnyMockData>[]) {
    return mocksToRegister.map((mockRegisterObject: MockRegisterObject<AnyMockData>) => {
        let mockData: Mock;

        const mockName = mockRegisterObject.name;
        const mockDeclaration: MockDeclaration<Mock> = mockRegisterObject;
        delete mockDeclaration.name;

        if (_.hasIn(mocks, mockName) && mockIsActivated(mocks[mockName])) {
            console.warn(`[${mockName}] - This mock is already registered and active.`);
            mockData = mocks[mockName];
        } else if (isMethodMockRegisterObject(mockDeclaration)) {
            mockData = registerMethod(mockName, mockDeclaration);
        } else if (isFunctionMockRegisterObject(mockDeclaration)) {
            mockData = registerFunction(mockName, mockDeclaration);
        } else {
            throw new Error(`[${mockName}] - Unsupported mock request.`);
        }

        return mockData;
    });
}

/**
 * Restores the mocked functions back to their normal state
 * @param stubsToRestore the list of function names you want to restore, it can also accept 'all' instead
 */
export function removeMocks(stubsToRestore?: string[]) {
    const list = stubsToRestore ?? Object.keys(mocks);
    list.forEach(stubName => {
        const stubToRestore = mocks[stubName];
        if (mockIsActivated(stubToRestore)) {
            stubToRestore.mock.restore();
            mocks[stubName] = {
                ...stubToRestore,
                isMockActivated: false,
                mock: null,
            } as DeactivatedMock;
        }
    });
}
