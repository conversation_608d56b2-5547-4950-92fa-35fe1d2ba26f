export const crudData: {
    pageDefinition: {
        canClose: boolean;
        canDelete: boolean;
        canSave: boolean;
        canCreate: boolean;
        isPanel: boolean;
        recordId?: string;
        isDirty: boolean;
    };
    buttonsStatus: {
        close?: { isDisabled: boolean; isHidden: boolean };
        delete?: { isDisabled: boolean; isHidden: boolean };
        save?: { isDisabled: boolean; isHidden: boolean };
        create?: { isDisabled: boolean; isHidden: boolean };
    };
}[] = [
    // Test cases
    // | Close | Delete | Save | Create | Panel | recordId | dirty
    // |   Y   |    Y   |   Y  |    Y   |   Y   |    '1'   |   N
    // |   Y   |    Y   |   Y  |    Y   |   Y   |    '1'   |   Y
    // |   Y   |    Y   |   Y  |    Y   |   N   |     /    |   N
    // |   Y   |    Y   |   Y  |    Y   |   N   |    '1'   |   N
    // |   Y   |    Y   |   Y  |    Y   |   N   |    '1'   |   Y
    // |   Y   |    Y   |   Y  |    Y   |   N   |     /    |   Y
    // |   Y   |    N   |   Y  |    Y   |   N   |     /    |   Y

    {
        pageDefinition: {
            // | Close | Delete | Save | Create | Panel | recordId | dirty
            // |   Y   |    Y   |   Y  |    Y   |   Y   |    '1'   |   N
            // All buttons exist
            canClose: true,
            canDelete: true,
            canSave: true,
            canCreate: true,
            // it's open as a panel
            isPanel: true,
            // A record is loaded
            recordId: '1',
            // no modification has been done yet
            isDirty: false,
        },
        buttonsStatus: {
            // | Close | Delete | Save | Create |
            // |   X   |        |   x  |        |
            // Close button is enabled
            close: { isDisabled: false, isHidden: false },
            // Delete button is not displayed
            delete: { isDisabled: true, isHidden: true },
            // Save button is displayed but disabled
            save: { isDisabled: true, isHidden: false },
            // Create button is not displayed
            create: { isDisabled: true, isHidden: true },
        },
    },
    {
        pageDefinition: {
            // | Close | Delete | Save | Create | Panel | recordId | dirty
            // |   Y   |    Y   |   Y  |    Y   |   Y   |    '1'   |   Y
            canClose: true,
            canDelete: true,
            canSave: true,
            canCreate: true,
            isPanel: true,
            recordId: '1',
            isDirty: true,
        },
        buttonsStatus: {
            // | Close | Delete | Save | Create |
            // |   X   |        |   x  |        |
            close: { isDisabled: false, isHidden: false },
            delete: { isDisabled: true, isHidden: true },
            save: { isDisabled: false, isHidden: false },
            create: { isDisabled: true, isHidden: true },
        },
    },
    {
        pageDefinition: {
            // | Close | Delete | Save | Create | Panel | recordId | dirty
            // |   Y   |    Y   |   Y  |    Y   |   N   |     /    |   N
            canClose: true,
            canDelete: true,
            canSave: true,
            canCreate: true,
            isPanel: false,
            recordId: undefined,
            isDirty: false,
        },
        buttonsStatus: {
            // | Close | Delete | Save | Create |
            // |   x   |    x   |   x  |    X   |
            close: { isDisabled: true, isHidden: false },
            delete: { isDisabled: true, isHidden: false },
            save: { isDisabled: true, isHidden: false },
            create: { isDisabled: false, isHidden: false },
        },
    },
    {
        pageDefinition: {
            // | Close | Delete | Save | Create | Panel | recordId | dirty
            // |   Y   |    Y   |   Y  |    Y   |    N  |    '1'   |   N
            canClose: true,
            canDelete: true,
            canSave: true,
            canCreate: true,
            isPanel: false,
            recordId: '1',
            isDirty: false,
        },
        buttonsStatus: {
            // | Close | Delete | Save | Create |
            // |   x   |    X   |   x  |    X   |
            close: { isDisabled: true, isHidden: false },
            delete: { isDisabled: false, isHidden: false },
            save: { isDisabled: true, isHidden: false },
            create: { isDisabled: false, isHidden: false },
        },
    },
    {
        pageDefinition: {
            // | Close | Delete | Save | Create | Panel | recordId | dirty
            // |   Y   |    Y   |   Y  |    Y   |    N  |    '1'   |   Y
            canClose: true,
            canDelete: true,
            canSave: true,
            canCreate: true,
            isPanel: false,
            recordId: '1',
            isDirty: true,
        },
        buttonsStatus: {
            // | Close | Delete | Save | Create |
            // |   X   |    X   |   X  |    x   |
            close: { isDisabled: false, isHidden: false },
            delete: { isDisabled: false, isHidden: false },
            save: { isDisabled: false, isHidden: false },
            create: { isDisabled: true, isHidden: false },
        },
    },
    {
        pageDefinition: {
            // | Close | Delete | Save | Create | Panel | recordId | dirty
            // |   Y   |    Y   |   Y  |    Y   |    N  |     /    |   Y
            canClose: true,
            canDelete: true,
            canSave: true,
            canCreate: true,
            isPanel: false,
            recordId: undefined,
            isDirty: true,
        },
        buttonsStatus: {
            // | Close | Delete | Save | Create |
            // |   X   |    x   |   X  |    x   |
            close: { isDisabled: false, isHidden: false },
            delete: { isDisabled: true, isHidden: false },
            save: { isDisabled: false, isHidden: false },
            create: { isDisabled: true, isHidden: false },
        },
    },
    {
        pageDefinition: {
            // | Close | Delete | Save | Create | Panel | recordId | dirty
            // |   Y   |    N   |   Y  |    Y   |   N   |     /    |   Y
            canClose: true,
            canDelete: false,
            canSave: true,
            canCreate: true,
            isPanel: false,
            recordId: undefined,
            isDirty: true,
        },
        buttonsStatus: {
            // | Close | Delete | Save | Create |
            // |   X   |        |   X  |    x   |
            close: { isDisabled: false, isHidden: false },
            save: { isDisabled: false, isHidden: false },
            create: { isDisabled: true, isHidden: false },
        },
    },
];
