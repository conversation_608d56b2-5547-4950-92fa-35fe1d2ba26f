import {
    accessRightsManagerMock,
    AnyRecord,
    AnyValue,
    Application,
    ConfigManager,
    Context,
    Dict,
    initTables,
    Test,
} from '@sage/xtrem-core';
import { assert } from 'chai';
import { ExecutionResult, graphql, GraphQLSchema } from 'graphql';
import * as fsp from 'path';

export { ConfigManager, initTables };

Context.accessRightsManager = accessRightsManagerMock;

export function createApplicationWithApi(api: any): Promise<Application> {
    const buildDir = fsp.parse(__dirname).dir;
    const testApplicationDir = fsp.join(__dirname, '../fixtures');
    return Test.createTestApplication({ api, buildDir, testApplicationDir });
}

interface SetupOptions {
    stubResetTables?: boolean;
    application: Application;
    activatesPackages?: string[];
    testActivePackages?: Dict<boolean>;
}

export function updateContext() {
    Context.accessRightsManager = accessRightsManagerMock;
}

export interface GraphQlSetupOptions {
    userEmail?: string;
    rootQueryType?: string;
    context?: Context;
}

export interface GraphQlResponse<T> {
    data: { [key: string]: T };
}

export class GraphQlHelper {
    constructor(private schema: GraphQLSchema) {}

    private execute<T extends AnyValue>(q: string, options?: GraphQlSetupOptions): Promise<ExecutionResult<T>> {
        return Test.readonly(
            async context => {
                const result = (await graphql({
                    schema: this.schema,
                    source: q,
                    contextValue: options?.context || context,
                })) as ExecutionResult<T>;
                if (result.errors) throw result.errors[0];
                return result;
            },
            {
                user: options?.userEmail ? { email: options?.userEmail } : undefined,
            },
        );
    }

    private static unwrap<T extends AnyValue>(result: ExecutionResult<AnyRecord>, rootQueryType: string): T {
        assert.isObject(result);
        assert.isObject(result.data);
        assert.isObject(result.data![rootQueryType]);
        return result.data![rootQueryType] as T;
    }

    async query<T extends AnyValue>(q: string, options?: GraphQlSetupOptions): Promise<T> {
        const rootQueryType = options?.rootQueryType || 'xtremSystem';
        const result = await this.execute<AnyRecord>(`{ ${rootQueryType} ${q} }`, options);
        return GraphQlHelper.unwrap<T>(result, rootQueryType);
    }

    async mutation<T extends AnyValue>(q: string, options?: GraphQlSetupOptions): Promise<T> {
        const rootQueryType = options?.rootQueryType || 'xtremSystem';
        const result = await this.execute<AnyRecord>(`mutation { ${rootQueryType} ${q} }`, options);
        return GraphQlHelper.unwrap<T>(result, rootQueryType);
    }
}

interface GraphqlSetupOptions extends SetupOptions {
    context?: Context;
}

export async function graphqlSetup(graphqlSetupOptions: GraphqlSetupOptions): Promise<GraphQlHelper> {
    const schema = await Test.application.getGraphQLSchema(graphqlSetupOptions?.context);
    return new GraphQlHelper(schema!);
}
