{"name": "@sage/biodegradability-test", "description": "Biodegradability Test (bundle)", "version": "37.0.14", "xtrem": {"isBundle": true}, "keywords": ["xtrem-application-package"], "author": "Sage", "license": "UNLICENSED", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/index.js", "files": ["build", "data"], "typings": "build/package-definition.d.ts", "dependencies": {"@sage/xtrem-cli": "workspace:*", "@sage/xtrem-core": "workspace:*", "@sage/xtrem-shared": "workspace:*", "eslint": "^8.49.0"}, "devDependencies": {"@sage/eslint-plugin-xtrem": "workspace:*", "@sage/xtrem-show-case-api": "workspace:*", "@types/chai": "^4.3.6", "@types/mocha": "^10.0.1", "@types/node": "^22.10.2", "c8": "^10.1.2", "chai": "^4.3.10", "eslint": "^8.49.0", "mocha": "^10.2.0", "typescript": "~5.4.5"}, "scripts": {"build": "xtrem compile", "build:cache": "turbo run build", "build:references": "tsc -b -v .", "clean": "rm -rf build", "lint": "xtrem lint", "start": "xtrem start", "test": "xtrem test --unit || exit 0 || xtrem test --integration", "test:ci": "echo 'no tests'", "test:integration": "xtrem test --integration", "xtrem": "xtrem", "xtrem:debug": "node --inspect-brk ../../@sage/xtrem-cli/build/lib/cli.js"}}