import { decimal, decorators, Node } from '@sage/xtrem-core';
import { defaultDecimalDataType, descriptionDataType } from '../data-types/data-types';

@decorators.node<TestBiodegradabilityCategory>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
})
export class TestBiodegradabilityCategory extends Node {
    @decorators.stringProperty<TestBiodegradabilityCategory, 'description'>({
        isStored: true,
        isPublished: true,
        dataType: () => descriptionDataType,
    })
    readonly description: Promise<string>;

    @decorators.decimalProperty<TestBiodegradabilityCategory, 'energyToProduce'>({
        isStored: true,
        isPublished: true,
        dataType: () => defaultDecimalDataType,
    })
    readonly energyToProduce: Promise<decimal>;

    @decorators.decimalProperty<TestBiodegradabilityCategory, 'percentageOfDegradability'>({
        isStored: true,
        isPublished: true,
        defaultValue: () => 50,
        dataType: () => defaultDecimalDataType,
    })
    readonly percentageOfDegradability: Promise<decimal>;

    @decorators.booleanProperty<TestBiodegradabilityCategory, 'localSourcing'>({
        isStored: true,
        isPublished: true,
    })
    readonly localSourcing: Promise<boolean>;
}
