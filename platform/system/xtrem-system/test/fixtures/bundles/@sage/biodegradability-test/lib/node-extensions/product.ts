import { decorators, NodeExtension, Reference } from '@sage/xtrem-core';
import { TestProduct } from '../../../../../lib/nodes/_index';
import { TestBiodegradabilityCategory } from '../nodes/_index';

@decorators.nodeExtension<TestProductExtension>({
    extends: () => TestProduct,
})
export class TestProductExtension extends NodeExtension<TestProduct> {
    @decorators.referenceProperty<TestProductExtension, 'biodegradabilityCategory'>({
        isStored: true,
        isNullable: true,
        isPublished: true,
        node: () => TestBiodegradabilityCategory,
    })
    readonly biodegradabilityCategory: Reference<TestBiodegradabilityCategory | null>;
}

declare module '../../../../../lib/nodes/product' {
    interface TestProduct extends TestProductExtension {}
}
