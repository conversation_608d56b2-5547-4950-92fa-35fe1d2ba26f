/******/ (function (modules) {
    // webpackBootstrap
    /******/ // The module cache
    /******/ var installedModules = {};
    /******/
    /******/ // The require function
    /******/ function __webpack_require__(moduleId) {
        /******/
        /******/ // Check if module is in cache
        /******/ if (installedModules[moduleId]) {
            /******/ return installedModules[moduleId].exports;
            /******/
        }
        /******/ // Create a new module (and put it into the cache)
        /******/ var module = (installedModules[moduleId] = {
            /******/ i: moduleId,
            /******/ l: false,
            /******/ exports: {},
            /******/
        });
        /******/
        /******/ // Execute the module function
        /******/ modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);
        /******/
        /******/ // Flag the module as loaded
        /******/ module.l = true;
        /******/
        /******/ // Return the exports of the module
        /******/ return module.exports;
        /******/
    }
    /******/
    /******/
    /******/ // expose the modules object (__webpack_modules__)
    /******/ __webpack_require__.m = modules;
    /******/
    /******/ // expose the module cache
    /******/ __webpack_require__.c = installedModules;
    /******/
    /******/ // define getter function for harmony exports
    /******/ __webpack_require__.d = function (exports, name, getter) {
        /******/ if (!__webpack_require__.o(exports, name)) {
            /******/ Object.defineProperty(exports, name, { enumerable: true, get: getter });
            /******/
        }
        /******/
    };
    /******/
    /******/ // define __esModule on exports
    /******/ __webpack_require__.r = function (exports) {
        /******/ if (typeof Symbol !== 'undefined' && Symbol.toStringTag) {
            /******/ Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
            /******/
        }
        /******/ Object.defineProperty(exports, '__esModule', { value: true });
        /******/
    };
    /******/
    /******/ // create a fake namespace object
    /******/ // mode & 1: value is a module id, require it
    /******/ // mode & 2: merge all properties of value into the ns
    /******/ // mode & 4: return value when already ns object
    /******/ // mode & 8|1: behave like require
    /******/ __webpack_require__.t = function (value, mode) {
        /******/ if (mode & 1) value = __webpack_require__(value);
        /******/ if (mode & 8) return value;
        /******/ if (mode & 4 && typeof value === 'object' && value && value.__esModule) return value;
        /******/ var ns = Object.create(null);
        /******/ __webpack_require__.r(ns);
        /******/ Object.defineProperty(ns, 'default', { enumerable: true, value: value });
        /******/ if (mode & 2 && typeof value != 'string')
            for (var key in value)
                __webpack_require__.d(
                    ns,
                    key,
                    function (key) {
                        return value[key];
                    }.bind(null, key),
                );
        /******/ return ns;
        /******/
    };
    /******/
    /******/ // getDefaultExport function for compatibility with non-harmony modules
    /******/ __webpack_require__.n = function (module) {
        /******/ var getter =
            module && module.__esModule
                ? /******/ function getDefault() {
                      return module['default'];
                  }
                : /******/ function getModuleExports() {
                      return module;
                  };
        /******/ __webpack_require__.d(getter, 'a', getter);
        /******/ return getter;
        /******/
    };
    /******/
    /******/ // Object.prototype.hasOwnProperty.call
    /******/ __webpack_require__.o = function (object, property) {
        return Object.prototype.hasOwnProperty.call(object, property);
    };
    /******/
    /******/ // __webpack_public_path__
    /******/ __webpack_require__.p = '';
    /******/
    /******/
    /******/ // Load entry module and return exports
    /******/ return __webpack_require__((__webpack_require__.s = './lib/pages/biodegradability-category.ts'));
    /******/
})(
    /************************************************************************/
    /******/ {
        /***/ './lib/pages/biodegradability-category.ts':
            /*!************************************************!*\
  !*** ./lib/pages/biodegradability-category.ts ***!
  \************************************************/
            /*! no static exports found */
            /***/ function (module, exports, __webpack_require__) {
                'use strict';
                eval(
                    '\r\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n};\r\nObject.defineProperty(exports, "__esModule", { value: true });\r\nexports.BiodegradabilityCategory = void 0;\r\nconst ui = __webpack_require__(/*! @sage/xtrem-ui */ "@sage/xtrem-ui");\r\nlet BiodegradabilityCategory = class BiodegradabilityCategory extends ui.Page {\r\n};\r\n__decorate([\r\n    ui.decorators.section({})\r\n], BiodegradabilityCategory.prototype, "section", void 0);\r\n__decorate([\r\n    ui.decorators.block({\r\n        parent() {\r\n            return this.section;\r\n        },\r\n    })\r\n], BiodegradabilityCategory.prototype, "block", void 0);\r\n__decorate([\r\n    ui.decorators.textField({\r\n        parent() {\r\n            return this.block;\r\n        },\r\n        title: ui.localize("@sage/xtrem-show-case-bundle/pages__biodegradability_category__description____title", "Description"),\r\n    })\r\n], BiodegradabilityCategory.prototype, "description", void 0);\r\n__decorate([\r\n    ui.decorators.numericField({\r\n        parent() {\r\n            return this.block;\r\n        },\r\n        title: ui.localize("@sage/xtrem-show-case-bundle/pages__biodegradability_category__percentageOfDegradability____title", "Percentage Of Degradability"),\r\n    })\r\n], BiodegradabilityCategory.prototype, "percentageOfDegradability", void 0);\r\n__decorate([\r\n    ui.decorators.checkboxField({\r\n        parent() {\r\n            return this.block;\r\n        },\r\n        title: ui.localize("@sage/xtrem-show-case-bundle/pages__biodegradability_category__localSourcing____title", "Local Sourcing"),\r\n    })\r\n], BiodegradabilityCategory.prototype, "localSourcing", void 0);\r\nBiodegradabilityCategory = __decorate([\r\n    ui.decorators.page({\r\n        authorizationCode: \'SHCPRDT\',\r\n        module: \'show-case-bundle\',\r\n        title: ui.localize("@sage/xtrem-show-case-bundle/pages__biodegradability_category____title", "Sample page - Biodegradability Category"),\r\n        node: \'@sage/xtrem-show-case-bundle/BiodegradabilityCategory\',\r\n        category: \'SHOWCASE\',\r\n        onLoad() {\r\n            this.$.storage.set(\'filterNavPanelItems\', true);\r\n        },\r\n        navigationPanel: {\r\n            listItem: {\r\n                title: ui.nestedFields.text({ bind: \'description\', canFilter: false }),\r\n            },\r\n        },\r\n    })\r\n], BiodegradabilityCategory);\r\nexports.BiodegradabilityCategory = BiodegradabilityCategory;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///./lib/pages/biodegradability-category.ts\n',
                );

                /***/
            },

        /***/ '@sage/xtrem-ui':
            /*!*********************************!*\
  !*** external "@sage/xtrem-ui" ***!
  \*********************************/
            /*! no static exports found */
            /***/ function (module, exports) {
                eval(
                    'module.exports = require("@sage/xtrem-ui");//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vZXh0ZXJuYWwgXCJAc2FnZS94dHJlbS11aVwiPzRiNTAiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEiLCJmaWxlIjoiQHNhZ2UveHRyZW0tdWkuanMiLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoXCJAc2FnZS94dHJlbS11aVwiKTsiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///@sage/xtrem-ui\n',
                );

                /***/
            },

        /******/
    },
);
