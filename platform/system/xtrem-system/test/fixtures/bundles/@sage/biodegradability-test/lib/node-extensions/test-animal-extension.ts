import {
    Context,
    decorators,
    integer,
    NodeExtension,
    SubNodeExtension1,
    SubNodeExtension2,
    ValidationSeverity,
} from '@sage/xtrem-core';
import { TestAnimal, TestDog, TestMammal } from '../../../../../lib/nodes/_index';
import { descriptionDataType } from '../data-types/_index';

@decorators.nodeExtension<TestAnimalExtension>({
    extends: () => TestAnimal,
    controlBegin(cx): void {
        cx.addDiagnose(ValidationSeverity.info, 'Bundle-TestAnimal.controlBegin');
    },
})
export class TestAnimalExtension extends NodeExtension<TestAnimal> {
    @decorators.stringProperty<TestAnimalExtension, 'addedByBundle'>({
        isStored: true,
        isPublished: true,
        dataType: () => descriptionDataType,
    })
    readonly addedByBundle: Promise<string>;

    @decorators.stringPropertyOverride<TestAnimalExtension, 'strFromAnimal'>({
        async control(cx) {
            if ((await this.strFromAnimal) === 'Spot') cx.addDiagnose(ValidationSeverity.error, 'Bad dog');
        },
    })
    readonly strFromAnimal: Promise<string | null>;

    async bundleNonStaticMethod(param: integer): Promise<string> {
        return `Bundle-nonStaticMethod strFromAnimal=${await this.strFromAnimal}, param=${param}`;
    }

    @decorators.mutation<typeof TestAnimalExtension, 'bundleStaticOperation'>({
        isPublished: true,
        parameters: [{ name: 'param', type: 'integer' }],
        return: 'string',
    })
    static bundleStaticOperation(_context: Context, param: integer): string {
        return `Bundle-staticOperation param=${param}`;
    }
}

declare module '../../../../../lib/nodes/subclassing' {
    interface TestAnimal extends TestAnimalExtension {}
}

@decorators.subNodeExtension1<TestMammal>({
    extends: () => TestMammal,
})
export class TestMammalExtension extends SubNodeExtension1<TestMammal> {
    @decorators.booleanProperty<TestMammalExtension, 'isWild'>({
        isPublished: true,
        isStored: true,
    })
    readonly isWild: Promise<boolean>;
}

declare module '../../../../../lib/nodes/subclassing' {
    interface TestMammal extends TestMammalExtension {}
}

@decorators.subNodeExtension2<TestDog>({
    extends: () => TestDog,
})
export class TestDogExtension extends SubNodeExtension2<TestDog> {
    @decorators.booleanProperty<TestDogExtension, 'isNice'>({
        isPublished: true,
        isStored: true,
    })
    readonly isNice: Promise<boolean>;
}

declare module '../../../../../lib/nodes/subclassing' {
    interface TestDog extends TestDogExtension {}
}
