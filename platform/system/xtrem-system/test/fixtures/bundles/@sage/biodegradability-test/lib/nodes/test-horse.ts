import { decorators } from '@sage/xtrem-core';
import { TestMammal } from '../../../../../lib/nodes/_index';
import { descriptionDataType } from '../data-types/_index';

@decorators.subNode<TestHorse>({
    extends: () => TestMammal,
    isPublished: true,
})
export class TestHorse extends TestMammal {
    @decorators.stringProperty<TestHorse, 'strFromHorse'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly strFromHorse: Promise<string>;
}
