import { BinaryStreamDataType, DecimalDataType, StringArrayDataType, StringDataType } from '@sage/xtrem-core';

export const codeDataType = new StringDataType({ maxLength: 32 });
export const descriptionDataType = new StringDataType({ maxLength: 250 });
export const descriptionArrayDataType = new StringArrayDataType({ maxLength: 250 });
export const defaultDecimalDataType = new DecimalDataType({ precision: 9, scale: 3 });
export const bundleIdDataType = new StringDataType({ maxLength: 100 });
export const bundlePathType = new StringDataType({ maxLength: 1024 });
export const bundleVersionDataType = new StringDataType({ maxLength: 50 });
export const notificationDataDataType = new BinaryStreamDataType({ maxLength: 8 * 1024 });
export const name = new StringDataType({ maxLength: 80 });
