import { decorators, NodeExtension } from '@sage/xtrem-core';
import { codeDataType } from '../data-types/data-types';
import { TestBundleBaseNode } from '../nodes/bundle-base-node';

@decorators.nodeExtension<TestBundleExtensionNode>({
    extends: () => TestBundleBaseNode,
    async prepare(): Promise<void> {
        if ((await this.baseStr) === 'BadFromExt-Node')
            throw new Error('Invalid value from @sage/test-bundle (node level)');
    },
})
export class TestBundleExtensionNode extends NodeExtension<TestBundleBaseNode> {
    @decorators.stringPropertyOverride<TestBundleExtensionNode, 'baseStr'>({
        defaultValue(): string {
            return 'FROMEXT';
        },
        async control(cx): Promise<void> {
            await cx.error.if(await this.baseStr).is.equal.to('BadFromExt-Prop');
        },
    })
    readonly baseStr: Promise<string>;

    @decorators.stringProperty<TestBundleExtensionNode, 'extStr'>({
        isPublished: true,
        dataType: () => codeDataType,
        isStored: true,
    })
    readonly extStr: Promise<string>;

    @decorators.integerProperty<TestBundleExtensionNode, 'extInt'>({
        isPublished: true,
        isStored: true,
        isNullable: false,
    })
    readonly extInt: Promise<number>;
}

declare module '../nodes/bundle-base-node' {
    export interface TestBundleBaseNode extends TestBundleExtensionNode {}
}
