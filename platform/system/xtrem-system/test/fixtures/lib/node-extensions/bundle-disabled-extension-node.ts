import { decorators, NodeExtension } from '@sage/xtrem-core';
import { codeDataType } from '../data-types/data-types';
import { TestBundleBaseNode } from '../nodes/bundle-base-node';

@decorators.nodeExtension<TestBundleDisabledExtensionNode>({
    extends: () => TestBundleBaseNode,
    prepare(): void {
        throw new Error('Code from disabled bundle was executed (node level)');
    },
})
export class TestBundleDisabledExtensionNode extends NodeExtension<TestBundleBaseNode> {
    @decorators.stringPropertyOverride<TestBundleDisabledExtensionNode, 'baseStr'>({
        defaultValue(): string {
            return 'FROMDISABLED';
        },
        control(): void {
            throw new Error('Code from disabled bundle was executed (prop level)');
        },
    })
    readonly baseStr: Promise<string>;

    @decorators.stringProperty<TestBundleDisabledExtensionNode, 'disabledExtStr'>({
        isPublished: true,
        dataType: () => codeDataType,
        isStored: true,
    })
    readonly disabledExtStr: Promise<string>;
}

declare module '../nodes/bundle-base-node' {
    export interface TestBundleBaseNode extends TestBundleDisabledExtensionNode {}
}
