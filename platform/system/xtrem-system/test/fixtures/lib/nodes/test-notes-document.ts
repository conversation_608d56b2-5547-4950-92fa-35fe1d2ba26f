import { Node, Reference, StringDataType, decorators } from '@sage/xtrem-core';
import { TestNotesCustomer } from './test-notes-customer';

@decorators.node<TestNotesDocument>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    hasNotes: true,
    indexes: [{ orderBy: { code: +1 }, isUnique: true }],
    notifies: ['created', 'updated', 'deleted'],
})
export class TestNotesDocument extends Node {
    @decorators.stringProperty<TestNotesDocument, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 32 }),
    })
    readonly code: Promise<string>;

    @decorators.referenceProperty<TestNotesDocument, 'customer'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        node: () => TestNotesCustomer,
    })
    readonly customer: Reference<TestNotesCustomer>;
}
