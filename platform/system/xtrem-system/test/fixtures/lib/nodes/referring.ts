import { Node, Reference, decorators } from '@sage/xtrem-core';
import { codeDataType, descriptionDataType } from '../data-types/data-types';
import { TestReferred } from './referred';

@decorators.node<TestReferring>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    tableName: 'TestReferring',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestReferring extends Node {
    @decorators.stringProperty<TestReferring, 'code'>({
        isStored: true,
        isPublished: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestReferring, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly description: Promise<string>;

    @decorators.stringProperty<TestReferring, 'restricted'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
        accessCode: 'RESTRICTED',
    })
    readonly restricted: Promise<string>;

    @decorators.referenceProperty<TestReferring, 'reference'>({
        isPublished: true,
        isStored: true,
        node: () => TestReferred,
        isNullable: true,
    })
    readonly reference: Reference<TestReferred | null>;

    @decorators.referenceArrayProperty<TestReferring, 'referenceArray'>({
        isPublished: true,
        isStored: true,
        onDelete: 'restrict',
        node: () => TestReferred,
        isNullable: true,
    })
    readonly referenceArray: Promise<TestReferred[] | null>;
}

@decorators.node<TestMandatoryReferring>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestMandatoryReferring',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestMandatoryReferring extends Node {
    @decorators.stringProperty<TestMandatoryReferring, 'code'>({
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestMandatoryReferring, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly description: Promise<string>;

    @decorators.stringProperty<TestMandatoryReferring, 'restricted'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
        accessCode: 'RESTRICTED',
    })
    readonly restricted: Promise<string>;

    @decorators.referenceProperty<TestMandatoryReferring, 'reference'>({
        isPublished: true,
        isStored: true,
        node: () => TestReferred,
    })
    readonly reference: Reference<TestReferred>;
}
