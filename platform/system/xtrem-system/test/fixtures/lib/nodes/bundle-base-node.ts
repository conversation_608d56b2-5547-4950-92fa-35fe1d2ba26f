import { decorators, Node } from '@sage/xtrem-core';
import { codeDataType } from '../data-types/_index';

@decorators.node<TestBundleBaseNode>({
    isPublished: true,
    storage: 'sql',
    canDeleteMany: true,
    async prepare(): Promise<void> {
        if ((await this.baseStr) === 'BadFromBase-Node') throw new Error('Invalid value from base (node decorator)');
    },
})
export class TestBundleBaseNode extends Node {
    @decorators.stringProperty<TestBundleBaseNode, 'baseStr'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
        defaultValue(): string {
            let at = 0;
            let toValue: string = '';
            const forValues = {
                F: 'F',
                R: 'R',
                O: 'O',
                M: 'M',
                B: 'B',
                A: 'A',
                S: 'S',
                E: 'E,',
            };

            const expectedValue = Object.keys(forValues).join('');
            const fromValue = expectedValue;

            const init = () => {
                at = 0;
                toValue = '';
            };

            const loop = (): boolean => toValue !== expectedValue;

            const next = () => {
                toValue += fromValue[at];
                at += 1;
            };

            const add = (c: string) => {
                toValue += c;
            };

            for (init(); loop(); ) {
                next();
            }

            if (loop()) {
                init();
                for (; loop(); ) next();
            }

            if (loop()) {
                init();
                for (; loop(); next());
            }

            if (loop()) {
                init();
                while (loop()) {
                    next();
                }
            }

            /* eslint-disable */
            init();
            for (const character in forValues) add(character);

            init();
            for (const character in forValues) add(character);
            /* eslint-enable */

            init();
            while (loop()) {
                next();
            }

            init();
            while (loop()) next();

            init();
            do {
                next();
            } while (loop());

            init();
            do next();
            while (loop());

            init();
            Object.keys(forValues).forEach(key => {
                add(key);
            });

            init();
            Object.keys(forValues).forEach(key => {
                switch (key) {
                    case 'F':
                        add('F');
                        return;
                    case 'R': {
                        add('R');
                        return;
                    }
                    case 'O':
                        add('O');
                        return;
                    case 'M':
                        add('M');
                        return;
                    case 'B':
                        add('B');
                        return;
                    case 'A':
                        add('A');
                        return;
                    case 'S':
                        add('S');
                        return;
                    case 'E':
                        add('E');
                        return;
                    default:
                        add(key);
                }
            });

            return toValue;
        },

        async control(cx): Promise<void> {
            await cx.error.if(await this.baseStr).is.equal.to('BadFromBase-Prop');
        },
    })
    readonly baseStr: Promise<string>;

    @decorators.integerProperty<TestBundleBaseNode, 'intValue'>({
        isPublished: true,
        isStored: true,
        isNullable: false,
    })
    readonly intValue: Promise<number>;
}
