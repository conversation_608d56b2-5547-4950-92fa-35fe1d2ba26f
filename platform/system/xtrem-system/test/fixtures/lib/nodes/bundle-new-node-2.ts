import { decorators, Node, StringDataType } from '@sage/xtrem-core';

/**
 * This node emulates a node that has been added by a bundle
 */
@decorators.node<TestBundleNewNode2>({
    isPublished: true,
    storage: 'sql',
})
export class TestBundleNewNode2 extends Node {
    @decorators.stringProperty<TestBundleNewNode2, 'strValue'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 100 }),
    })
    readonly strValue: Promise<string>;

    @decorators.integerProperty<TestBundleNewNode2, 'intValue'>({
        isPublished: true,
        isStored: true,
    })
    readonly intValue: Promise<number>;
}
