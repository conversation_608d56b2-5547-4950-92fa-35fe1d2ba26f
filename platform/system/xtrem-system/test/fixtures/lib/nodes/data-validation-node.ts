import { decorators, integer, Node } from '@sage/xtrem-core';
import { descriptionDataType } from '../data-types/data-types';

/**
 * Node used to test data validation
 */
@decorators.node<DataValidationNode>({
    isPublished: true,
    storage: 'sql',
    tableName: 'DataValidationNode',
})
export class DataValidationNode extends Node {
    @decorators.stringProperty<DataValidationNode, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly description: Promise<string>;

    @decorators.integerProperty<DataValidationNode, 'intValue'>({
        isPublished: true,
        isStored: true,
    })
    readonly intValue: Promise<integer>;
}
