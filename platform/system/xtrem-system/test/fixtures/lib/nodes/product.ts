import {
    Collection,
    Context,
    decimal,
    decorators,
    integer,
    Node,
    Reference,
    ValidationContext,
} from '@sage/xtrem-core';
import { defaultDecimalDataType, descriptionDataType } from '../data-types/data-types';
import { showCaseOptionMutation } from '../service-options/show-case-option-mutation';

@decorators.node<TestProduct>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
    controlEnd(cx: ValidationContext) {
        cx.localize('@sage/xtrem-system/productKey', 'product value');
        cx.localize('@sage/xtrem-system/productKey2', 'product value {{num}}', { num: 2 });
        cx.info.add('product validation successful');
    },
})
export class TestProduct extends Node {
    @decorators.stringProperty<TestProduct, 'product'>({
        isStored: true,
        isPublished: true,
        dataType: () => descriptionDataType,
    })
    readonly product: Promise<string>;

    @decorators.stringProperty<TestProduct, 'description'>({
        isStored: true,
        isPublished: true,
        dataType: () => descriptionDataType,
    })
    readonly description: Promise<string>;

    @decorators.booleanProperty<TestProduct, 'hotProduct'>({
        isStored: true,
        isPublished: true,
    })
    readonly hotProduct: Promise<boolean>;

    @decorators.integerProperty<TestProduct, 'qty'>({
        isStored: true,
        isPublished: true,
        defaultValue() {
            return 1;
        },
    })
    readonly qty: Promise<integer>;

    @decorators.integerProperty<TestProduct, 'st'>({
        isStored: true,
        isPublished: true,
        defaultValue() {
            return 1;
        },
    })
    readonly st: Promise<integer>;

    @decorators.decimalProperty<TestProduct, 'listPrice'>({
        isStored: true,
        isPublished: true,
        dataType: () => defaultDecimalDataType,
    })
    readonly listPrice: Promise<decimal>;

    @decorators.integerProperty<TestProduct, 'progress'>({
        isStored: true,
        isNullable: true,
        isPublished: true,
    })
    readonly progress: Promise<integer>;

    @decorators.decimalProperty<TestProduct, 'netPrice'>({
        isStored: true,
        isPublished: true,
        dataType: () => defaultDecimalDataType,
    })
    readonly netPrice: Promise<decimal>;

    @decorators.decimalProperty<TestProduct, 'tax'>({
        isStored: true,
        isPublished: true,
        dataType: () => defaultDecimalDataType,
    })
    readonly tax: Promise<decimal>;

    @decorators.decimalProperty<TestProduct, 'amount'>({
        isStored: true,
        isPublished: true,
        dataType: () => defaultDecimalDataType,
    })
    readonly amount: Promise<decimal>;

    @decorators.referenceProperty<TestProduct, 'collection'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => TestProductCollection,
    })
    readonly collection: Reference<TestProductCollection>;

    // eslint-disable-next-line require-await
    @decorators.mutation<typeof TestProduct, 'staticOperationWithServiceOption'>({
        serviceOptions: () => [showCaseOptionMutation],
        isPublished: true,
        parameters: [{ name: 'param', type: 'integer' }],
        return: 'string',
    })
    static async staticOperationWithServiceOption(_context: Context, param: integer): Promise<string> {
        return `staticOperation param=${param}`;
    }

    // eslint-disable-next-line require-await
    @decorators.query<typeof TestProduct, 'staticQueryOperationWithServiceOption'>({
        serviceOptions: () => [showCaseOptionMutation],
        isPublished: true,
        parameters: [{ name: 'param', type: 'integer' }],
        return: 'string',
    })
    static async staticQueryOperationWithServiceOption(_context: Context, param: integer): Promise<string> {
        return `staticOperation param=${param}`;
    }
}

@decorators.node<TestProductCollection>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
})
export class TestProductCollection extends Node {
    @decorators.stringProperty<TestProductCollection, 'description'>({
        isStored: true,
        isPublished: true,
        dataType: () => descriptionDataType,
    })
    readonly description: Promise<string>;

    @decorators.collectionProperty<TestProductCollection, 'products'>({
        isPublished: true,
        node: () => TestProduct,
        reverseReference: 'collection',
    })
    readonly products: Collection<TestProduct>;

    @decorators.referenceProperty<TestProductCollection, 'reference'>({
        isPublished: true,
        isStored: true,
        node: () => TestReferencedByCollection,
    })
    readonly reference: Reference<TestReferencedByCollection>;
}

@decorators.node<TestProductCollectionReference>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
})
export class TestProductCollectionReference extends Node {
    @decorators.stringProperty<TestProductCollectionReference, 'description'>({
        isStored: true,
        isPublished: true,
        dataType: () => descriptionDataType,
    })
    readonly description: Promise<string>;

    @decorators.referenceProperty<TestProductCollectionReference, 'collection'>({
        isPublished: true,
        isStored: true,
        node: () => TestProductCollection,
    })
    readonly collection: Reference<TestProductCollection>;
}

@decorators.node<TestReferencedByCollection>({
    storage: 'sql',
    canCreate: true,
    canDelete: true,
    canRead: true,
    canSearch: true,
    canUpdate: true,
    isPublished: true,
})
export class TestReferencedByCollection extends Node {
    @decorators.stringProperty<TestReferencedByCollection, 'description'>({
        isStored: true,
        isPublished: true,
        dataType: () => descriptionDataType,
    })
    readonly description: Promise<string>;
}
