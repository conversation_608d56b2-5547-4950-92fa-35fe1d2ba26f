import { TestDocumentLine } from './document-lines';
import { Reference, Collection, decorators, Node } from '@sage/xtrem-core';
import { codeDataType, descriptionDataType } from '../data-types/data-types';
import { TestReferred } from './referred';

@decorators.node<TestDocument>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    tableName: 'TestDocument',
    indexes: [{ orderBy: { code: +1 }, isUnique: true }],
})
export class TestDocument extends Node {
    @decorators.stringProperty<TestDocument, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestDocument, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly description: Promise<string>;

    @decorators.referenceProperty<TestDocument, 'mandatoryReference'>({
        isPublished: true,
        isStored: true,
        node: () => TestReferred,
    })
    readonly mandatoryReference: Reference<TestReferred>;

    @decorators.collectionProperty<TestDocument, 'lines'>({
        isPublished: true,
        isVital: true,
        node: () => TestDocumentLine,
        reverseReference: 'document',
    })
    readonly lines: Collection<TestDocumentLine>;
}
