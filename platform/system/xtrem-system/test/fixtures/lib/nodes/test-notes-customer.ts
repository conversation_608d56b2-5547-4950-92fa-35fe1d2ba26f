import { Node, StringDataType, decorators } from '@sage/xtrem-core';

@decorators.node<TestNotesCustomer>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canUpdate: true,
    hasNotes: true,
    indexes: [{ orderBy: { code: +1 }, isUnique: true, isNaturalKey: true }],
})
export class TestNotesCustomer extends Node {
    @decorators.stringProperty<TestNotesCustomer, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 32 }),
    })
    readonly code: Promise<string>;
}
