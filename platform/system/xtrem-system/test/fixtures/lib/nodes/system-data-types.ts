import { ConfigManager, decorators, Dict, Node } from '@sage/xtrem-core';
import { isDevelopmentConfig } from '@sage/xtrem-shared';
import { UrlDataType } from '../../../../lib/data-types';
import { codeDataType } from '../data-types/data-types';

const cachedTypes = {} as Dict<UrlDataType<unknown>>;

const typeKey = (key: string) => (isDevelopmentConfig(ConfigManager.current) ? `dev-${key}` : `prod-${key}`);

// For test only: Arrow functions to return a different datatype dependending on the deployment environment (prod or dev)
const httpUrlDataTypeArrow = () => {
    const key = typeKey('url');
    if (!cachedTypes[key]) {
        cachedTypes[key] = new UrlDataType({ maxLength: 200, allowedProtocols: ['http', 'https'] });
    }
    return cachedTypes[key];
};

const httpBaseUrlDataTypeArrow = () => {
    const key = typeKey('baseUrl');
    if (!cachedTypes[key]) {
        cachedTypes[key] = new UrlDataType({
            maxLength: 200,
            allowedProtocols: ['http', 'https'],
            skrinkToOrigin: true,
        });
    }
    return cachedTypes[key];
};

@decorators.node<TestSystemDataTypes>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestSystemDataTypes',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestSystemDataTypes extends Node {
    @decorators.stringProperty<TestSystemDataTypes, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<TestSystemDataTypes, 'url'>({
        isPublished: true,
        isStored: true,
        dataType: httpUrlDataTypeArrow,
    })
    readonly url: Promise<string>;

    @decorators.stringProperty<TestSystemDataTypes, 'baseUrl'>({
        isPublished: true,
        isStored: true,
        dataType: httpBaseUrlDataTypeArrow,
    })
    readonly baseUrl: Promise<string>;
}
