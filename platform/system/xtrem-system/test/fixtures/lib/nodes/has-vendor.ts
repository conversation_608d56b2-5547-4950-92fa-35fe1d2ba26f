import { decorators, Node } from '@sage/xtrem-core';
import { codeDataType, descriptionDataType } from '../data-types/data-types';

@decorators.node<HasVendor>({
    isPublished: true,
    storage: 'sql',
    isSetupNode: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true, isNaturalKey: true }],
})
export class <PERSON><PERSON><PERSON><PERSON> extends Node {
    @decorators.stringProperty<HasVendor, 'code'>({
        isPublished: true,
        isStored: true,
        isFrozen: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    @decorators.stringProperty<HasVendor, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly description: Promise<string>;
}
