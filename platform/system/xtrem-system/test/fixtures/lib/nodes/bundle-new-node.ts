import { decorators, Node, StringDataType } from '@sage/xtrem-core';

/**
 * This node emulates a node that has been added by a bundle
 */
@decorators.node<TestBundleNewNode>({
    isPublished: true,
    storage: 'sql',
    canDeleteMany: true,
})
export class TestBundleNewNode extends Node {
    @decorators.stringProperty<TestBundleNewNode, 'strValue'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 100 }),
    })
    readonly strValue: Promise<string>;

    @decorators.integerProperty<TestBundleNewNode, 'intValue'>({
        isPublished: true,
        isStored: true,
    })
    readonly intValue: Promise<number>;
}
