import { Collection, Context, decorators, integer, Node, Reference, ValidationSeverity } from '@sage/xtrem-core';
import { descriptionDataType } from '../data-types/_index';

@decorators.node<TestAnimal>({
    isPublished: true,
    storage: 'sql',
    isAbstract: true,
    controlBegin(cx): void {
        cx.addDiagnose(ValidationSeverity.info, 'TestAnimal.controlBegin');
    },
})
export class TestAnimal extends Node {
    @decorators.stringProperty<TestAnimal, 'strFromAnimal'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly strFromAnimal: Promise<string>;

    @decorators.referenceProperty<TestAnimal, 'owner'>({
        isPublished: true,
        isStored: true,
        node: () => TestPetOwner,
        isNullable: true,
    })
    readonly owner: Reference<TestPetOwner | null>;

    async nonStaticMethod(param: integer): Promise<string> {
        return `nonStaticMethod strFromAnimal=${await this.strFromAnimal}, param=${param}`;
    }

    @decorators.mutation<typeof TestAnimal, 'staticOperation'>({
        isPublished: true,
        parameters: [{ name: 'param', type: 'integer' }],
        return: 'string',
    })
    static staticOperation(_context: Context, param: integer): string {
        return `staticOperation param=${param}`;
    }
}

@decorators.subNode<TestMammal>({
    extends: () => TestAnimal,
    isPublished: true,
    canDeleteMany: true,
    isAbstract: true,
    controlBegin(cx): void {
        cx.addDiagnose(ValidationSeverity.info, 'TestMammal.controlBegin');
    },
})
export class TestMammal extends TestAnimal {
    @decorators.stringProperty<TestMammal, 'strFromMammal'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly strFromMammal: Promise<string>;
}

@decorators.subNode<TestDog>({
    extends: () => TestMammal,
    isPublished: true,
    canDeleteMany: true,
    controlBegin(cx): void {
        cx.addDiagnose(ValidationSeverity.info, 'TestDog.controlBegin');
    },
})
export class TestDog extends TestMammal {
    @decorators.stringProperty<TestDog, 'strFromDog'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly strFromDog: Promise<string>;
}

@decorators.subNode<TestCat>({
    extends: () => TestMammal,
    isPublished: true,
})
export class TestCat extends TestMammal {
    @decorators.stringProperty<TestCat, 'strFromCat'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly strFromCat: Promise<string>;
}

@decorators.subNode<TestFish>({
    extends: () => TestAnimal,
    isPublished: true,
})
export class TestFish extends TestAnimal {
    @decorators.stringProperty<TestFish, 'strFromFish'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly strFromFish: Promise<string>;
}

@decorators.node<TestPetOwner>({
    isPublished: true,
    canDeleteMany: true,
    storage: 'sql',
    controlBegin(cx): void {
        cx.addDiagnose(ValidationSeverity.info, 'TestAnimal.controlBegin');
    },
})
export class TestPetOwner extends Node {
    @decorators.collectionProperty<TestPetOwner, 'pets'>({
        isPublished: true,
        node: () => TestAnimal,
        reverseReference: 'owner',
    })
    readonly pets: Collection<TestAnimal>;

    @decorators.referenceProperty<TestPetOwner, 'favoritePet'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        node: () => TestAnimal,
    })
    readonly favoritePet: Reference<TestAnimal | null>;
}
