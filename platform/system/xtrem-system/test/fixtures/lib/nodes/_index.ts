// export * from './document';
// export * from './document-lines';
// export * from './product';
// export * from './referred';
// export * from './referring';

import { CoreHooks, InstanceDataValidationResultLine } from '@sage/xtrem-core';
import { DataValidationNode } from './data-validation-node';

export * from './bundle-base-node';
export * from './data-validation-node';
export * from './has-vendor';
export * from './product';
export * from './subclassing';
export * from './system-data-types';
export * from './test-notes-customer';
export * from './test-notes-document';

// Install a dummy data validator for the DataValidationNode node
CoreHooks.getDataValidationManager().registerValidator(
    DataValidationNode,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async (_context, instance) => {
        const intValue = await instance.intValue;
        const errors: InstanceDataValidationResultLine[] = [];
        if (intValue == null || intValue % 2 !== 0) {
            errors.push({
                message: 'Integer value must be even.',
                severity: 'error',
                extraInfo: { intValue },
                path: 'intValue',
            });
        }
        return errors;
    },
);
