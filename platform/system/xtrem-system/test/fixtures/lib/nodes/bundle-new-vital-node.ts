import { Reference, decorators, Node, ValidationSeverity } from '@sage/xtrem-core';
import { TestBundleNewNode } from './bundle-new-node';

/**
 * This node emulates a node that has been added by a bundle
 */
@decorators.node<TestBundleNewVitalNode>({
    isPublished: true,
    storage: 'sql',
    canDeleteMany: true,
    isVitalCollectionChild: true,
    async controlDelete(cx): Promise<void> {
        if ((await (await this.parent).strValue) === 'BlockChildDeletion') {
            cx.addDiagnose(ValidationSeverity.error, 'Cannot delete this vital child (invalid value from parent)');
        }
    },
})
export class TestBundleNewVitalNode extends Node {
    @decorators.referenceProperty<TestBundleNewVitalNode, 'parent'>({
        isStored: true,
        isPublished: true,
        isNullable: false,
        node: () => TestBundleNewNode,
        isVitalParent: true,
    })
    readonly parent: Reference<TestBundleNewNode>;
}
