// The `xtrem test` command checks for a test/fixtures/test-application.ts file (this file)
// and combines its exports with the lib/index.ts exports before running the mocha unit tests.
import { getTestFixtures, TestUser } from '@sage/xtrem-core';
import { updateContext } from '../setup';
import * as testDataTypes from './data-types/_index';
import * as testNodes from './nodes/_index';
import * as serviceOptions from './service-options/_index';

const fixtures = getTestFixtures();

updateContext();

const dataTypes = { ...testDataTypes, ...fixtures.dataTypes };
const nodes = { ...testNodes, TestUser };

export { dataTypes, nodes, serviceOptions };
