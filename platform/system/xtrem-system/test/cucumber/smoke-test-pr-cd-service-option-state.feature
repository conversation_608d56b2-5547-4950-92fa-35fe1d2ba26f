Feature: smoke-test-pr-cd-service-option-state

    Scenario: filter service options
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-system/ServiceOptionState"
        Then the "Service options" titled page is displayed

        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "Name" labelled column in the table field with value "devTools"
        And the user selects the row 1 of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field

        Then the "Service option devTools" titled page is displayed
