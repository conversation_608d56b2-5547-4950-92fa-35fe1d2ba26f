Feature: smoke-test-static

    #Case without navigation panel full width
    Scenario Outline: std \ <Page> \ desktop \ check each page loads without any problems
        Given the user opens the application on a desktop using the following link: "<Page>"
        Then the "<Title>" titled page is displayed

        Examples:
            | Page                                    | Title              |
            | @sage/xtrem-system/TenantInformation    | Tenant information |
            | @sage/xtrem-system/ServiceOptionState   | Service options    |
            | @sage/xtrem-synchronization/Integration | Integrations       |
