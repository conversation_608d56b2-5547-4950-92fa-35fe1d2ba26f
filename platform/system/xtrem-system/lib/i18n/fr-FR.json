{"@sage/xtrem-system/activity__company__name": "Société", "@sage/xtrem-system/activity__service_option_state__name": "État de l'option de service", "@sage/xtrem-system/activity__site__name": "Site", "@sage/xtrem-system/activity__sys_device_token__name": "Jeton appareil système", "@sage/xtrem-system/activity__sys_note__name": "Note sys", "@sage/xtrem-system/activity__sys_tag__name": "Étiquette sys", "@sage/xtrem-system/activity__tenant__name": "Tenant", "@sage/xtrem-system/bundle-activated": "Bundle activé", "@sage/xtrem-system/bundle-deactivated": "Bundle désactivé", "@sage/xtrem-system/cannot-deactivate-admin-demo-persona": "Impossible d'activer le persona de dé<PERSON>.", "@sage/xtrem-system/confirm-activate-service-option-message": "Vous êtes sur le point d'activer l'option de service : {{serviceOptionName}}.", "@sage/xtrem-system/confirm-activate-service-option-title": "Activer option de service", "@sage/xtrem-system/confirm-deactivate-service-option-message": "Vous êtes sur le point de désactiver l'option de service : {{serviceOptionName}}.", "@sage/xtrem-system/confirm-deactivate-service-option-title": "Désactiver option de service", "@sage/xtrem-system/create-confirmation": "Enregistrement créé", "@sage/xtrem-system/data_types__big_id__name": "Code volumineux", "@sage/xtrem-system/data_types__big_url__name": "URL volumineux", "@sage/xtrem-system/data_types__bundle_id_data_type__name": "Type de données des ID bundle", "@sage/xtrem-system/data_types__bundle_path_type__name": "Type de chemin bundle", "@sage/xtrem-system/data_types__bundle_version_data_type__name": "Type de données version bundle", "@sage/xtrem-system/data_types__checksum_data_type__name": "Type de données checksum", "@sage/xtrem-system/data_types__code__name": "Code", "@sage/xtrem-system/data_types__code_data_type__name": "Type de données code", "@sage/xtrem-system/data_types__color__name": "Color", "@sage/xtrem-system/data_types__colored_element_enum__name": "Enum élément de couleur", "@sage/xtrem-system/data_types__company__name": "Société", "@sage/xtrem-system/data_types__content_length_decimal_data_type__name": "Type de données décimale longueur contenu", "@sage/xtrem-system/data_types__decimal__name": "Décimale", "@sage/xtrem-system/data_types__default_decimal_data_type__name": "Type de données décimale par défaut", "@sage/xtrem-system/data_types__description__name": "Description", "@sage/xtrem-system/data_types__description_array_data_type__name": "Type de données tableau description", "@sage/xtrem-system/data_types__description_data_type__name": "Type de données description", "@sage/xtrem-system/data_types__email__name": "E-mail", "@sage/xtrem-system/data_types__folder__name": "Dossier", "@sage/xtrem-system/data_types__http_base_url__name": "URL de base HTTP", "@sage/xtrem-system/data_types__http_url__name": "URL HTTP", "@sage/xtrem-system/data_types__icon__name": "Icône", "@sage/xtrem-system/data_types__id__name": "Code", "@sage/xtrem-system/data_types__locale_datatype__name": "Type de données paramètres régionaux", "@sage/xtrem-system/data_types__locale_id_data_type__name": "Type de données code paramètres régionaux", "@sage/xtrem-system/data_types__localized_description__name": "Description localisée", "@sage/xtrem-system/data_types__localized_name__name": "Nom localisé", "@sage/xtrem-system/data_types__localized_short_description__name": "Description courte localisée", "@sage/xtrem-system/data_types__localized_title__name": "Titre localisé", "@sage/xtrem-system/data_types__mime_type__name": "Type MIME", "@sage/xtrem-system/data_types__name__name": "Nom", "@sage/xtrem-system/data_types__nanoid__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/data_types__note_content_data_type__name": "Type données contenu de note", "@sage/xtrem-system/data_types__notification_data_data_type__name": "Type de données données notification", "@sage/xtrem-system/data_types__passphrase__name": "Phrase secrète", "@sage/xtrem-system/data_types__password__name": "Mot de passe", "@sage/xtrem-system/data_types__quantity_data_type__name": "Type de données quantité", "@sage/xtrem-system/data_types__rate__name": "<PERSON><PERSON>", "@sage/xtrem-system/data_types__rgb_color__name": "Couleur RGB", "@sage/xtrem-system/data_types__service_option_status_enum__name": "Enum état de l'option de service", "@sage/xtrem-system/data_types__setup_id_data_type__name": "Type de données ID paramétrage", "@sage/xtrem-system/data_types__short_description__name": "Description courte", "@sage/xtrem-system/data_types__single_character__name": "Caractère unique", "@sage/xtrem-system/data_types__site__name": "Site", "@sage/xtrem-system/data_types__sys_client_notification_action_style_enum__name": "Enum du style d'action de notification client système", "@sage/xtrem-system/data_types__sys_client_notification_level_enum__name": "Enum du niveau de notification client système", "@sage/xtrem-system/data_types__title__name": "Titre", "@sage/xtrem-system/data_types__url__name": "URL", "@sage/xtrem-system/data_types__user__name": "Utilisa<PERSON>ur", "@sage/xtrem-system/data_types__user_import_export_date_format_enum__name": "Enum du format de date d'import export de l'utilisateur", "@sage/xtrem-system/data_types__user_type_enum__name": "Enum type utilisateur", "@sage/xtrem-system/data_types__uuid__name": "UUID", "@sage/xtrem-system/data_types__version__name": "Version", "@sage/xtrem-system/data-types/locale_datatype__invalid_locale": "La valeur des paramètres régionaux est incorrecte : ({{locale}}).", "@sage/xtrem-system/data-types/rgb__invalid_rgb": "Cette valeur n'est pas un code couleur RGB : \"{{value}}\".", "@sage/xtrem-system/data-types/url_datatype__invalid_protocol": "URL '{{url}}' obtenu protocole {{protocol}}, en attente de {{allowedProtocols}}.", "@sage/xtrem-system/data-types/url_datatype__invalid_url": "URL incorrect '{{url}}'.", "@sage/xtrem-system/delete-confirmation": "Enregistrement supprimé", "@sage/xtrem-system/delete-dialog-content": "Vous êtes sur le point de supprimer cet enregistrement. Confirmer ?", "@sage/xtrem-system/delete-dialog-title": "Confirmer la <PERSON>", "@sage/xtrem-system/delete-user-forbidden": "Les utilisateurs ne peuvent pas être supprimés.", "@sage/xtrem-system/delete-view-warning-title": "Supprimer la vue", "@sage/xtrem-system/delete-view-warning-title-message": "Vous allez supprimer cette vue.", "@sage/xtrem-system/duplicate_first_admin": "Un utilisateur admin est déjà défini. Vous n'êtes pas autorisé à en créer une autre.", "@sage/xtrem-system/enums__colored_element__backgroundColor": "<PERSON><PERSON><PERSON> de fond", "@sage/xtrem-system/enums__colored_element__borderColor": "<PERSON><PERSON><PERSON> de bordure", "@sage/xtrem-system/enums__colored_element__textColor": "Couleur de texte", "@sage/xtrem-system/enums__service_option_status__experimental": "Expérimentale", "@sage/xtrem-system/enums__service_option_status__released": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/enums__service_option_status__workInProgress": "En cours", "@sage/xtrem-system/enums__service_option_type__experimental": "Expérimentale", "@sage/xtrem-system/enums__service_option_type__released": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/enums__service_option_type__workInProgress": "En cours", "@sage/xtrem-system/enums__sys_client_notification_action_style__link": "<PERSON><PERSON>", "@sage/xtrem-system/enums__sys_client_notification_action_style__primary": "Principal", "@sage/xtrem-system/enums__sys_client_notification_action_style__secondary": "Secondaire", "@sage/xtrem-system/enums__sys_client_notification_action_style__tertiary": "Tertiaire", "@sage/xtrem-system/enums__sys_client_notification_level__approval": "Approbation", "@sage/xtrem-system/enums__sys_client_notification_level__error": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/enums__sys_client_notification_level__info": "Infos", "@sage/xtrem-system/enums__sys_client_notification_level__success": "Su<PERSON>ès", "@sage/xtrem-system/enums__sys_client_notification_level__warning": "Avertissement", "@sage/xtrem-system/enums__user_import_export_date_format__europeanDash": "dd-mm-yyyy", "@sage/xtrem-system/enums__user_import_export_date_format__europeanSlash": "dd/mm/yyyy", "@sage/xtrem-system/enums__user_import_export_date_format__isoDash": "yyyy-mm-dd", "@sage/xtrem-system/enums__user_import_export_date_format__isoSlash": "yyyy/mm/dd", "@sage/xtrem-system/enums__user_import_export_date_format__usDash": "mm-dd-yyyy", "@sage/xtrem-system/enums__user_import_export_date_format__usSlash": "mm/dd/yyyy", "@sage/xtrem-system/enums__user_type__application": "Application", "@sage/xtrem-system/enums__user_type__system": "Système", "@sage/xtrem-system/error-packages-cannot-be": "Les packages ne peuvent pas être désactivés.", "@sage/xtrem-system/error-packages-cannot-be-deactivated": "Les packages ne peuvent pas être désactivés.", "@sage/xtrem-system/failed-to-save-view": "E<PERSON>ur lors de la sauvegarde de la vue", "@sage/xtrem-system/invalid-admin-demo-persona": "Impossible de créer un persona de démo Admin différent de celui par défaut.", "@sage/xtrem-system/invalid-api-user-email": "L'adresse e-mail de l'utilisateur API est incorrecte ({{value}}). Utiliser le format : api-<devid>@localhost.domain", "@sage/xtrem-system/invalid-api-user-is-admin": "Un utilisateur API ne peut pas avoir un rôle d'administrateur.", "@sage/xtrem-system/invalid-api-user-is-demo-persona": "Un utilisateur API ne peut pas être une persona de démo.", "@sage/xtrem-system/invalid-email": "Adresse e-mail incorrecte : ({{value}})", "@sage/xtrem-system/invalid-operator-pin-code": "Un code PIN d'opérateur doit avoir un minimum de 4 caractères.", "@sage/xtrem-system/invalid-operator-pincode-not-unique": "Un code PIN opérateur doit être unique.", "@sage/xtrem-system/invalid-operator-user-email": "Adresse e-mail utilisateur opérationnel invalide. Attendue : opérateur-<nom>@localhost.domain", "@sage/xtrem-system/invalid-operator-user-is-admin": "Un utilisateur opérationnel ne peut pas être d'administrateur.", "@sage/xtrem-system/invalid-operator-user-is-demo-persona": "Un utilisateur opérationnel ne peut pas être une persona de démo.", "@sage/xtrem-system/is-active/node-id-combination-not-active": "{{nodeName}} {{dataId}} est inactif.", "@sage/xtrem-system/language-master-locale-different-from-default": "Les paramètres régionaux principaux et par défaut doivent correspondre pour une même langue.", "@sage/xtrem-system/menu_item__administration": "Administration", "@sage/xtrem-system/menu_item__application-data": "Configuration de l'application", "@sage/xtrem-system/menu_item__bundle-activation": "Activation de bundle", "@sage/xtrem-system/menu_item__integrations": "", "@sage/xtrem-system/menu_item__service-option": "Options de service", "@sage/xtrem-system/menu_item__user": "Utilisateurs", "@sage/xtrem-system/menu_item__user-data": "Utilisateurs et sécurité", "@sage/xtrem-system/nodes__company__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-system/nodes__company__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-system/nodes__company__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-system/nodes__company__node_name": "Société", "@sage/xtrem-system/nodes__company__property__id": "Code", "@sage/xtrem-system/nodes__company__property__isActive": "Active", "@sage/xtrem-system/nodes__company__property__sites": "Sites", "@sage/xtrem-system/nodes__locale__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-system/nodes__locale__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-system/nodes__locale__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-system/nodes__locale__node_name": "Paramètres régionaux", "@sage/xtrem-system/nodes__locale__property__id": "Code", "@sage/xtrem-system/nodes__locale__property__isDefaultLocale": "Paramètres régionaux par défaut", "@sage/xtrem-system/nodes__locale__property__isLanguageMasterLocale": "Langue des paramètres régionaux maîtres", "@sage/xtrem-system/nodes__locale__property__language": "<PERSON><PERSON>", "@sage/xtrem-system/nodes__site__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-system/nodes__site__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-system/nodes__site__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-system/nodes__site__node_name": "Site", "@sage/xtrem-system/nodes__site__property__description": "Description", "@sage/xtrem-system/nodes__site__property__id": "Code", "@sage/xtrem-system/nodes__site__property__isActive": "Actif", "@sage/xtrem-system/nodes__site__property__legalCompany": "Société", "@sage/xtrem-system/nodes__site__property__linkedSites": "Sites liés", "@sage/xtrem-system/nodes__site__property__name": "Nom", "@sage/xtrem-system/nodes__sys_changelog__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-system/nodes__sys_changelog__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-system/nodes__sys_changelog__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-system/nodes__sys_changelog__node_name": "Journal des modifications système", "@sage/xtrem-system/nodes__sys_changelog__property__changeDate": "Changer date", "@sage/xtrem-system/nodes__sys_changelog__property__hash": "Hash", "@sage/xtrem-system/nodes__sys_changelog__property__message": "Message", "@sage/xtrem-system/nodes__sys_client_notification__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-system/nodes__sys_client_notification__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-system/nodes__sys_client_notification__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-system/nodes__sys_client_notification__node_name": "Notification client système", "@sage/xtrem-system/nodes__sys_client_notification__property__actions": "Actions", "@sage/xtrem-system/nodes__sys_client_notification__property__description": "Description", "@sage/xtrem-system/nodes__sys_client_notification__property__icon": "Icône", "@sage/xtrem-system/nodes__sys_client_notification__property__isRead": "Lecture", "@sage/xtrem-system/nodes__sys_client_notification__property__level": "Niveau", "@sage/xtrem-system/nodes__sys_client_notification__property__recipient": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__sys_client_notification__property__shouldDisplayToast": "<PERSON><PERSON><PERSON> afficher message toast", "@sage/xtrem-system/nodes__sys_client_notification__property__title": "Titre", "@sage/xtrem-system/nodes__sys_client_notification_action__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-system/nodes__sys_client_notification_action__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-system/nodes__sys_client_notification_action__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-system/nodes__sys_client_notification_action__node_name": "Action de notification client système", "@sage/xtrem-system/nodes__sys_client_notification_action__property__clientNotifications": "Notifications de licence", "@sage/xtrem-system/nodes__sys_client_notification_action__property__link": "<PERSON><PERSON>", "@sage/xtrem-system/nodes__sys_client_notification_action__property__style": "Style", "@sage/xtrem-system/nodes__sys_client_notification_action__property__title": "Titre", "@sage/xtrem-system/nodes__sys_client_user_settings__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-system/nodes__sys_client_user_settings__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-system/nodes__sys_client_user_settings__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-system/nodes__sys_client_user_settings__node_name": "Paramétrage utilisateur client système", "@sage/xtrem-system/nodes__sys_client_user_settings__property__content": "Contenu", "@sage/xtrem-system/nodes__sys_client_user_settings__property__description": "Description", "@sage/xtrem-system/nodes__sys_client_user_settings__property__elementId": "ID élément", "@sage/xtrem-system/nodes__sys_client_user_settings__property__isSelected": "Sélection", "@sage/xtrem-system/nodes__sys_client_user_settings__property__screenId": "ID écran", "@sage/xtrem-system/nodes__sys_client_user_settings__property__title": "Titre", "@sage/xtrem-system/nodes__sys_client_user_settings__property__user": "Utilisa<PERSON>ur", "@sage/xtrem-system/nodes__sys_csv_checksum__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-system/nodes__sys_csv_checksum__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-system/nodes__sys_csv_checksum__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-system/nodes__sys_csv_checksum__node_name": "Somme de contrôle CSV système", "@sage/xtrem-system/nodes__sys_csv_checksum__property__checksum": "Somme de contrôle", "@sage/xtrem-system/nodes__sys_csv_checksum__property__factoryName": "Nom livré", "@sage/xtrem-system/nodes__sys_csv_checksum__property__tenantId": "Code tenant", "@sage/xtrem-system/nodes__sys_custom_record__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-system/nodes__sys_custom_record__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-system/nodes__sys_custom_record__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-system/nodes__sys_custom_record__node_name": "Enregistrement du client système", "@sage/xtrem-system/nodes__sys_custom_record__property__bundleId": "Code bundle", "@sage/xtrem-system/nodes__sys_custom_record__property__factoryName": "Nom livré", "@sage/xtrem-system/nodes__sys_custom_sql_history__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-system/nodes__sys_custom_sql_history__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-system/nodes__sys_custom_sql_history__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-system/nodes__sys_custom_sql_history__node_name": "Historique SQL personnalisé système", "@sage/xtrem-system/nodes__sys_custom_sql_history__property__dryRun": "Test", "@sage/xtrem-system/nodes__sys_custom_sql_history__property__endDateTime": "Heure date de fin", "@sage/xtrem-system/nodes__sys_custom_sql_history__property__result": "Résultat", "@sage/xtrem-system/nodes__sys_custom_sql_history__property__scriptContent": "Contenu du script", "@sage/xtrem-system/nodes__sys_custom_sql_history__property__scriptPath": "Chemin du <PERSON>", "@sage/xtrem-system/nodes__sys_custom_sql_history__property__startDateTime": "Heure date de début", "@sage/xtrem-system/nodes__sys_customer__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-system/nodes__sys_customer__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-system/nodes__sys_customer__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-system/nodes__sys_customer__node_name": "Client système", "@sage/xtrem-system/nodes__sys_customer__property__customerId": "Code client", "@sage/xtrem-system/nodes__sys_customer__property__name": "Nom", "@sage/xtrem-system/nodes__sys_data_validation_report__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_data_validation_report__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-system/nodes__sys_data_validation_report__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-system/nodes__sys_data_validation_report__node_name": "Édition de validation des données système", "@sage/xtrem-system/nodes__sys_data_validation_report__property__lines": "Lines", "@sage/xtrem-system/nodes__sys_data_validation_report_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_data_validation_report_line__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-system/nodes__sys_data_validation_report_line__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-system/nodes__sys_data_validation_report_line__node_name": "Ligne d'édition de validation des données système", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__extraInfo": "Infos supplémentaires", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__message": "Message", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__nodeId": "Code du node", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__nodeName": "Nom du node", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__path": "Chemin", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__report": "Édition", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__severity": "Gravité", "@sage/xtrem-system/nodes__sys_device_token__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-system/nodes__sys_device_token__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-system/nodes__sys_device_token__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-system/nodes__sys_device_token__mutation__createDeviceToken": "<PERSON><PERSON><PERSON> jeton appareil", "@sage/xtrem-system/nodes__sys_device_token__mutation__createDeviceToken__failed": "La création du jeton de l'appareil a échoué.", "@sage/xtrem-system/nodes__sys_device_token__mutation__deleteDeviceToken": "Supprimer jeton appareil", "@sage/xtrem-system/nodes__sys_device_token__mutation__deleteDeviceToken__failed": "La suppression du jeton de l'appareil a échoué.", "@sage/xtrem-system/nodes__sys_device_token__mutation__deleteDeviceToken__parameter__tokenId": "ID jeton", "@sage/xtrem-system/nodes__sys_device_token__node_name": "Jeton appareil système", "@sage/xtrem-system/nodes__sys_device_token__property__expiration": "Expiration", "@sage/xtrem-system/nodes__sys_device_token__property__loginTestUrl": "URL test connexion", "@sage/xtrem-system/nodes__sys_device_token__property__loginUrl": "URL connexion", "@sage/xtrem-system/nodes__sys_device_token__property__name": "Nom", "@sage/xtrem-system/nodes__sys_device_token__property__tokenId": "ID jeton", "@sage/xtrem-system/nodes__sys_note__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-system/nodes__sys_note__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-system/nodes__sys_note__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-system/nodes__sys_note__node_name": "Note sys", "@sage/xtrem-system/nodes__sys_note__property__content": "Contenu", "@sage/xtrem-system/nodes__sys_note__property__title": "Intitulé", "@sage/xtrem-system/nodes__sys_note_association__node_name": "Association note sys", "@sage/xtrem-system/nodes__sys_note_association__property__note": "Note", "@sage/xtrem-system/nodes__sys_note_association__property__sourceNodeId": "Code node source", "@sage/xtrem-system/nodes__sys_note_association__property__sourceNodeName": "Nom node source", "@sage/xtrem-system/nodes__sys_pack_allocation__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-system/nodes__sys_pack_allocation__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-system/nodes__sys_pack_allocation__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-system/nodes__sys_pack_allocation__mutation__activate": "Activer", "@sage/xtrem-system/nodes__sys_pack_allocation__mutation__activate__failed": "L'activation a échoué.", "@sage/xtrem-system/nodes__sys_pack_allocation__mutation__activate__parameter__packageId": "ID package", "@sage/xtrem-system/nodes__sys_pack_allocation__mutation__deactivate": "Désactiver", "@sage/xtrem-system/nodes__sys_pack_allocation__mutation__deactivate__failed": "La désactivation a échoué.", "@sage/xtrem-system/nodes__sys_pack_allocation__mutation__deactivate__parameter__packageId": "ID package", "@sage/xtrem-system/nodes__sys_pack_allocation__node_name": "Allocation du pack système", "@sage/xtrem-system/nodes__sys_pack_allocation__property__isActivable": "Activable", "@sage/xtrem-system/nodes__sys_pack_allocation__property__isActive": "Actif", "@sage/xtrem-system/nodes__sys_pack_allocation__property__package": "Package", "@sage/xtrem-system/nodes__sys_pack_allocation__property__status": "Statut", "@sage/xtrem-system/nodes__sys_pack_version__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-system/nodes__sys_pack_version__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-system/nodes__sys_pack_version__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-system/nodes__sys_pack_version__node_name": "Version du pack système", "@sage/xtrem-system/nodes__sys_pack_version__property__isBundle": "Bundle", "@sage/xtrem-system/nodes__sys_pack_version__property__isHidden": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__sys_pack_version__property__isReleased": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__sys_pack_version__property__isUpgradeBundle": "Mettre à niveau le bundle", "@sage/xtrem-system/nodes__sys_pack_version__property__name": "Nom", "@sage/xtrem-system/nodes__sys_pack_version__property__sqlSchemaVersion": "Version schéma SQL", "@sage/xtrem-system/nodes__sys_pack_version__property__version": "Version", "@sage/xtrem-system/nodes__sys_patch_history__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-system/nodes__sys_patch_history__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-system/nodes__sys_patch_history__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-system/nodes__sys_patch_history__node_name": "Historique du pack système", "@sage/xtrem-system/nodes__sys_patch_history__property__packageName": "Nom du package", "@sage/xtrem-system/nodes__sys_patch_history__property__patchName": "Nom du patch", "@sage/xtrem-system/nodes__sys_patch_history__property__result": "Résultat", "@sage/xtrem-system/nodes__sys_patch_history__property__version": "Version", "@sage/xtrem-system/nodes__sys_service_option__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-system/nodes__sys_service_option__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-system/nodes__sys_service_option__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-system/nodes__sys_service_option__node_name": "Option de service système", "@sage/xtrem-system/nodes__sys_service_option__property__childServiceOptions": "Options de service secondaires", "@sage/xtrem-system/nodes__sys_service_option__property__description": "Description", "@sage/xtrem-system/nodes__sys_service_option__property__isActiveByDefault": "Active par défaut", "@sage/xtrem-system/nodes__sys_service_option__property__isHidden": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__sys_service_option__property__optionName": "Nom de l'option", "@sage/xtrem-system/nodes__sys_service_option__property__package": "Package", "@sage/xtrem-system/nodes__sys_service_option__property__parentServiceOptions": "Options de service parent", "@sage/xtrem-system/nodes__sys_service_option__property__status": "Statut", "@sage/xtrem-system/nodes__sys_service_option_state__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-system/nodes__sys_service_option_state__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-system/nodes__sys_service_option_state__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-system/nodes__sys_service_option_state__node_name": "Statut de l'option de service système", "@sage/xtrem-system/nodes__sys_service_option_state__property__imageStatus": "Statut image", "@sage/xtrem-system/nodes__sys_service_option_state__property__isActivable": "Activable", "@sage/xtrem-system/nodes__sys_service_option_state__property__isActive": "Active", "@sage/xtrem-system/nodes__sys_service_option_state__property__isAvailable": "Disponible", "@sage/xtrem-system/nodes__sys_service_option_state__property__isHiddenByConfig": "Caché par défaut", "@sage/xtrem-system/nodes__sys_service_option_state__property__isPackageActive": "Package actif", "@sage/xtrem-system/nodes__sys_service_option_state__property__isReadyToUse": "Prêt à l'emploi", "@sage/xtrem-system/nodes__sys_service_option_state__property__serviceOption": "Option de service", "@sage/xtrem-system/nodes__sys_service_option_to_service_option__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-system/nodes__sys_service_option_to_service_option__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-system/nodes__sys_service_option_to_service_option__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-system/nodes__sys_service_option_to_service_option__node_name": "Option de service système en option de service", "@sage/xtrem-system/nodes__sys_service_option_to_service_option__property__childServiceOption": "Option de service secondaire", "@sage/xtrem-system/nodes__sys_service_option_to_service_option__property__parentServiceOption": "Option de service parent", "@sage/xtrem-system/nodes__sys_tag__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-system/nodes__sys_tag__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-system/nodes__sys_tag__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-system/nodes__sys_tag__node_name": "Étiquette sys", "@sage/xtrem-system/nodes__sys_tag__property__color": "Color", "@sage/xtrem-system/nodes__sys_tag__property__description": "Description", "@sage/xtrem-system/nodes__sys_tag__property__name": "Nom", "@sage/xtrem-system/nodes__sys_tag__property__rgbColor": "Couleur RGB", "@sage/xtrem-system/nodes__sys_tenant__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-system/nodes__sys_tenant__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-system/nodes__sys_tenant__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-system/nodes__sys_tenant__mutation__resetTenantDocuments": "Réinitialiser les documents du tenant", "@sage/xtrem-system/nodes__sys_tenant__mutation__resetTenantDocuments__failed": "La réinitialisation des documents du tenant a échoué.", "@sage/xtrem-system/nodes__sys_tenant__mutation__resetTenantDocuments__parameter__tenantId": "Code du tenant", "@sage/xtrem-system/nodes__sys_tenant__node_name": "Tenant système", "@sage/xtrem-system/nodes__sys_tenant__property__customer": "Client", "@sage/xtrem-system/nodes__sys_tenant__property__directoryName": "Nom du répertoire", "@sage/xtrem-system/nodes__sys_tenant__property__name": "Nom", "@sage/xtrem-system/nodes__sys_tenant__property__tenantId": "Code tenant", "@sage/xtrem-system/nodes__sys_tenant__query__getTenantInformation": "Obtenir les informations du tenant", "@sage/xtrem-system/nodes__sys_tenant__query__getTenantInformation__failed": "La récupération des informations de tenant a échoué.", "@sage/xtrem-system/nodes__sys_tenant__tenant_directory_name_should_be_in_kebab_case": "Indiquer le nom du répertoire du tenant en kebab-case.", "@sage/xtrem-system/nodes__sys_tenant__tenant_name_characters_not_authorized": "Le nom du tenant contient des caractères interdits.", "@sage/xtrem-system/nodes__sys_upgrade__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-system/nodes__sys_upgrade__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-system/nodes__sys_upgrade__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-system/nodes__sys_upgrade__node_name": "Mise à niveau système", "@sage/xtrem-system/nodes__sys_upgrade__property__bundleId": "Code bundle", "@sage/xtrem-system/nodes__sys_upgrade__property__managedItems": "Éléments gérés", "@sage/xtrem-system/nodes__sys_vendor__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-system/nodes__sys_vendor__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-system/nodes__sys_vendor__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-system/nodes__sys_vendor__node_name": "Fournisseur système", "@sage/xtrem-system/nodes__sys_vendor__property__description": "Description", "@sage/xtrem-system/nodes__sys_vendor__property__name": "Nom", "@sage/xtrem-system/nodes__sys-client-user-settings": "Vous pouvez uniquement en sélectionner un.", "@sage/xtrem-system/nodes__sys-layer__name-must-be-kebab-case": "Le nom doit être saisi en police kebab.", "@sage/xtrem-system/nodes__user__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-system/nodes__user__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-system/nodes__user__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-system/nodes__user__cannot_modify_own_rights": "Vous n'êtes pas autorisé à modifier vos propres droits administrateur.", "@sage/xtrem-system/nodes__user__mutation__logPageVisit": "Log visite page", "@sage/xtrem-system/nodes__user__mutation__logPageVisit__failed": "Le log de la visite de la page a échoué.", "@sage/xtrem-system/nodes__user__mutation__logPageVisit__parameter__path": "Chemin", "@sage/xtrem-system/nodes__user__mutation__sendWelcomeMail": "Envoyer un e-mail de bienvenue", "@sage/xtrem-system/nodes__user__mutation__sendWelcomeMail__failed": "L'envoi d'e-mail de bienvenue a échoué.", "@sage/xtrem-system/nodes__user__mutation__sendWelcomeMail__parameter__isAdmin": "Admin", "@sage/xtrem-system/nodes__user__mutation__sendWelcomeMail__parameter__users": "Utilisateurs", "@sage/xtrem-system/nodes__user__mutation__setDemoPersona": "<PERSON><PERSON><PERSON><PERSON> le persona de démo", "@sage/xtrem-system/nodes__user__mutation__setDemoPersona__failed": "La définition des persona de démo a échoué.", "@sage/xtrem-system/nodes__user__mutation__setDemoPersona__parameter__email": "E-mail", "@sage/xtrem-system/nodes__user__mutation__updateClientSettings": "Mettre à jour le paramétrage client", "@sage/xtrem-system/nodes__user__mutation__updateClientSettings__failed": "La mise à jour du paramétrage client a échoué.", "@sage/xtrem-system/nodes__user__mutation__updateClientSettings__parameter__clientSettings": "Paramètres client", "@sage/xtrem-system/nodes__user__node_name": "Utilisa<PERSON>ur", "@sage/xtrem-system/nodes__user__only_admin_can_add_admin": "Seul un utilisateur administrateur peut ajouter ou modifier les droits d'administrateur d'un autre utilisateur.", "@sage/xtrem-system/nodes__user__property__clientSettings": "Paramètres client", "@sage/xtrem-system/nodes__user__property__displayName": "Nom d'affichage", "@sage/xtrem-system/nodes__user__property__email": "E-mail", "@sage/xtrem-system/nodes__user__property__firstName": "Prénom", "@sage/xtrem-system/nodes__user__property__isActive": "Actif", "@sage/xtrem-system/nodes__user__property__isAdministrator": "Administrateur", "@sage/xtrem-system/nodes__user__property__isApiUser": "Utilisateur API", "@sage/xtrem-system/nodes__user__property__isDemoPersona": "Persona de démo", "@sage/xtrem-system/nodes__user__property__isFirstAdminUser": "Premier utilisateur admin", "@sage/xtrem-system/nodes__user__property__isOperatorUser": "Utilisateur opérationnel", "@sage/xtrem-system/nodes__user__property__lastName": "Nom de famille", "@sage/xtrem-system/nodes__user__property__navigation": "Navigation", "@sage/xtrem-system/nodes__user__property__operatorCode": "ID opérateur", "@sage/xtrem-system/nodes__user__property__operatorId": "ID opérateur", "@sage/xtrem-system/nodes__user__property__photo": "Photo", "@sage/xtrem-system/nodes__user__property__preferences": "Préférences", "@sage/xtrem-system/nodes__user__property__setupId": "Code paramétrage", "@sage/xtrem-system/nodes__user__property__userType": "Type utilisateur", "@sage/xtrem-system/nodes__user__query__demoPersonas": "Personas de démo", "@sage/xtrem-system/nodes__user__query__demoPersonas__failed": "Les personas de démo ont échoué.", "@sage/xtrem-system/nodes__user_navigation__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-system/nodes__user_navigation__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-system/nodes__user_navigation__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-system/nodes__user_navigation__node_name": "Navigation utilisateur", "@sage/xtrem-system/nodes__user_navigation__property__bookmarks": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__user_navigation__property__history": "Historique", "@sage/xtrem-system/nodes__user_navigation__property__user": "Utilisa<PERSON>ur", "@sage/xtrem-system/nodes__user_preferences__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-system/nodes__user_preferences__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-system/nodes__user_preferences__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-system/nodes__user_preferences__node_name": "Préférences utilisateur", "@sage/xtrem-system/nodes__user_preferences__property__importExportDateFormat": "Format de date d'import/export", "@sage/xtrem-system/nodes__user_preferences__property__importExportDelimiter": "Séparateur import/export", "@sage/xtrem-system/nodes__user_preferences__property__isExternal": "Externe", "@sage/xtrem-system/nodes__user_preferences__property__isWelcomeMailSent": "E-mail de bienvenue envoyé", "@sage/xtrem-system/nodes__user_preferences__property__user": "Utilisa<PERSON>ur", "@sage/xtrem-system/nodes__user_preferences__query__activeServiceOptions": "Options de service actives", "@sage/xtrem-system/nodes__user_preferences__query__activeServiceOptions__failed": "Les options de service actives ont échoué.", "@sage/xtrem-system/package__name": "Système", "@sage/xtrem-system/package-activated": "Package activé", "@sage/xtrem-system/package-deactivated": "Package désactivé", "@sage/xtrem-system/pages__client_user_settings_edit____title": "<PERSON><PERSON><PERSON> une vue", "@sage/xtrem-system/pages__client_user_settings_edit__generalSection____title": "Général", "@sage/xtrem-system/pages__client_user_settings_edit__save____title": "Enregistrer", "@sage/xtrem-system/pages__client_user_settings_edit__title____title": "Nom", "@sage/xtrem-system/pages__client_user_settings_list____title": "<PERSON><PERSON><PERSON> les vues", "@sage/xtrem-system/pages__client_user_settings_list__clientSettings____columns__title__title": "Nom", "@sage/xtrem-system/pages__client_user_settings_list__clientSettings____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__client_user_settings_list__clientSettings____dropdownActions__title__remove": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__client_user_settings_list__generalSection____title": "Général", "@sage/xtrem-system/pages__client_user_settings_list__save____title": "Enregistrer", "@sage/xtrem-system/pages__delete_page_dialog_content": "Vous allez supprimer cet enregistrement.", "@sage/xtrem-system/pages__delete_page_dialog_title": "Confirmer la <PERSON>", "@sage/xtrem-system/pages__pack_allocation____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-system/pages__pack_allocation____navigationPanel__optionsMenu__title__2": "Packages", "@sage/xtrem-system/pages__pack_allocation____navigationPanel__optionsMenu__title__3": "Bundles", "@sage/xtrem-system/pages__pack_allocation____title": "Activation de package", "@sage/xtrem-system/pages__pack_allocation__closePage____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__pack_allocation__generalSection____title": "Général", "@sage/xtrem-system/pages__pack_allocation__isActivable____title": "Disponible", "@sage/xtrem-system/pages__pack_allocation__isActive____title": "Actif", "@sage/xtrem-system/pages__pack_allocation__package____title": "Package", "@sage/xtrem-system/pages__pack_allocation__version____title": "Version", "@sage/xtrem-system/pages__pack_version____navigationPanel__optionsMenu__title": "Bundles", "@sage/xtrem-system/pages__pack_version____navigationPanel__optionsMenu__title__2": "Packages", "@sage/xtrem-system/pages__pack_version____navigationPanel__optionsMenu__title__3": "<PERSON>ut", "@sage/xtrem-system/pages__pack_version____title": "Activation de bundle", "@sage/xtrem-system/pages__pack_version__closePage____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__pack_version__generalSection____title": "Général", "@sage/xtrem-system/pages__pack_version__isBundle____title": "Bundle", "@sage/xtrem-system/pages__pack_version__package____title": "Package", "@sage/xtrem-system/pages__pack_version__version____title": "Version", "@sage/xtrem-system/pages__service_option_state____navigationPanel__dropdownActions__title": "Activer", "@sage/xtrem-system/pages__service_option_state____navigationPanel__dropdownActions__title__2": "Désactiver", "@sage/xtrem-system/pages__service_option_state____navigationPanel__listItem__line_4__title": "Statut", "@sage/xtrem-system/pages__service_option_state____navigationPanel__listItem__line2__title": "Description", "@sage/xtrem-system/pages__service_option_state____navigationPanel__listItem__line3__title": "Active", "@sage/xtrem-system/pages__service_option_state____navigationPanel__listItem__line4__title": "Statut", "@sage/xtrem-system/pages__service_option_state____navigationPanel__listItem__title__title": "Nom", "@sage/xtrem-system/pages__service_option_state____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-system/pages__service_option_state____objectTypePlural": "Options de service", "@sage/xtrem-system/pages__service_option_state____objectTypeSingular": "Option de service", "@sage/xtrem-system/pages__service_option_state____title": "Options de service", "@sage/xtrem-system/pages__service_option_state__cancel____title": "Annuler", "@sage/xtrem-system/pages__service_option_state__closePage____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__service_option_state__description____title": "Description", "@sage/xtrem-system/pages__service_option_state__generalSection____title": "Général", "@sage/xtrem-system/pages__service_option_state__isActivable____title": "Disponible", "@sage/xtrem-system/pages__service_option_state__isActive____title": "Actif", "@sage/xtrem-system/pages__service_option_state__isAvailable____title": "Activable", "@sage/xtrem-system/pages__service_option_state__isHiddenByConfig____title": "Active", "@sage/xtrem-system/pages__service_option_state__optionName____title": "Nom", "@sage/xtrem-system/pages__service_option_state__reset_tenant_data_done": "Données du tenant réinitialisées", "@sage/xtrem-system/pages__service_option_state__save____title": "Enregistrer", "@sage/xtrem-system/pages__service_option_state__serviceOption____title": "Option de service", "@sage/xtrem-system/pages__service_option_state__status____title": "Statut", "@sage/xtrem-system/pages__sys_changelog____navigationPanel__listItem__line2__title": "Message", "@sage/xtrem-system/pages__sys_changelog____navigationPanel__listItem__line3__title": "Changer horodatage", "@sage/xtrem-system/pages__sys_changelog____navigationPanel__listItem__title__title": "Hash", "@sage/xtrem-system/pages__sys_changelog____title": "Journal des modifications", "@sage/xtrem-system/pages__sys_device_token____navigationPanel__listItem__tokenId__title": "Code", "@sage/xtrem-system/pages__sys_device_token____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-system/pages__sys_device_token____title": "Jeton appareil", "@sage/xtrem-system/pages__sys_device_token__createDeviceToken____title": "Enregistrer", "@sage/xtrem-system/pages__sys_device_token__deleteDeviceToken____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__sys_device_token__deviceTokenBlock____title": "Information de jeton appareil", "@sage/xtrem-system/pages__sys_device_token__expiration____title": "Expiration", "@sage/xtrem-system/pages__sys_device_token__loginTestUrl____title": "Lien de test appareil", "@sage/xtrem-system/pages__sys_device_token__loginUrl____title": "Lien de connexion appareil", "@sage/xtrem-system/pages__sys_device_token__name____title": "Nom", "@sage/xtrem-system/pages__sys_device_token__tokenId____title": "<PERSON><PERSON>", "@sage/xtrem-system/pages__sys_layer____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-system/pages__sys_layer____title": "Couche de don<PERSON>", "@sage/xtrem-system/pages__sys_layer___id____title": "ID", "@sage/xtrem-system/pages__sys_layer__dataLayerInformation____title": "Informations sur la couche de données", "@sage/xtrem-system/pages__sys_layer__deleteLayerAction____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__sys_layer__description____title": "Description", "@sage/xtrem-system/pages__sys_layer__exportLayer____title": "Couche d'export", "@sage/xtrem-system/pages__sys_layer__generalSection____title": "Général", "@sage/xtrem-system/pages__sys_layer__name____title": "Nom de la couche de donn<PERSON>", "@sage/xtrem-system/pages__sys_pack_allocation____navigationPanel__listItem__lineRight__title": "Version", "@sage/xtrem-system/pages__sys_pack_allocation____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-system/pages__sys_pack_allocation____navigationPanel__optionsMenu__title__2": "Packages", "@sage/xtrem-system/pages__sys_pack_allocation____title": "Activation de packages", "@sage/xtrem-system/pages__sys_pack_allocation__closePage____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__sys_pack_allocation__generalSection____title": "Général", "@sage/xtrem-system/pages__sys_pack_allocation__isActivable____title": "Disponible", "@sage/xtrem-system/pages__sys_pack_allocation__isActive____title": "Actif", "@sage/xtrem-system/pages__sys_pack_allocation__package____title": "Package", "@sage/xtrem-system/pages__sys_pack_allocation__version____title": "Version", "@sage/xtrem-system/pages__sys_tag____navigationPanel__listItem__line2__title": "Description", "@sage/xtrem-system/pages__sys_tag____navigationPanel__listItem__rgbColor__title": "Couleur RGB", "@sage/xtrem-system/pages__sys_tag____navigationPanel__listItem__title__title": "Nom", "@sage/xtrem-system/pages__sys_tag____title": "Étiquette", "@sage/xtrem-system/pages__sys_tag__description____title": "Description", "@sage/xtrem-system/pages__sys_tag__generalSection____title": "Général", "@sage/xtrem-system/pages__sys_tag__name____title": "Nom", "@sage/xtrem-system/pages__sys_tag__rgbColor____title": "Couleur RGB", "@sage/xtrem-system/pages__tenant_information____title": "Informations du tenant", "@sage/xtrem-system/pages__tenant_information__cancel_reset_tenant_data_button_text": "Annuler", "@sage/xtrem-system/pages__tenant_information__confirm_reset_tenant_data_button_text": "Confirmer réinitialisation données tenant", "@sage/xtrem-system/pages__tenant_information__generalSection____title": "Général", "@sage/xtrem-system/pages__tenant_information__reset_tenant_data_button_text": "Réinitialiser données tenant", "@sage/xtrem-system/pages__tenant_information__resetDataDescription____content": "La réinitialisation des données du tenant va supprimer tous les mouvements (par exemple, les commandes de vente et d'achat, le niveau et les mouvements de stock).\nCette opération est irréversible.", "@sage/xtrem-system/pages__tenant_information__resetTenantId____helperText": "Renseignez le code tenant à effacer.", "@sage/xtrem-system/pages__tenant_information__resetTenantId____title": "Confirmer le code tenant", "@sage/xtrem-system/pages__tenant_information__resetTenantSection____title": "Réinitialiser", "@sage/xtrem-system/pages__tenant_information__sumologic_button_text": "Visualiser les traces Sumo Logic", "@sage/xtrem-system/pages__tenant_information__sumologicLink____helperText": "Vous devez être connecté à Sumo Logic avant de pouvoir visualiser les traces.", "@sage/xtrem-system/pages__tenant_information__tenantId____title": "Code tenant", "@sage/xtrem-system/pages__tenant_information__tenantVersion____title": "Version tenant", "@sage/xtrem-system/pages__user____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-system/pages__user____title": "Utilisa<PERSON>ur", "@sage/xtrem-system/pages__user___id____title": "ID", "@sage/xtrem-system/pages__user__email____title": "E-mail", "@sage/xtrem-system/pages__user__firstName____title": "Prénom", "@sage/xtrem-system/pages__user__generalSection____title": "Général", "@sage/xtrem-system/pages__user__idBlock____title": "ID", "@sage/xtrem-system/pages__user__isActive____title": "Actif", "@sage/xtrem-system/pages__user__lastName____title": "Nom de famille", "@sage/xtrem-system/pages__user__userInformationBlock____title": "Informations utilisateurs", "@sage/xtrem-system/pages__user__userPhotoBlock____title": "Photo", "@sage/xtrem-system/pages-confirm-cancel": "Annuler", "@sage/xtrem-system/pages-confirm-delete": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/permission__create__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/permission__delete__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/permission__get_tenant_information__name": "Obtenir les informations du tenant", "@sage/xtrem-system/permission__read__name": "Lecture", "@sage/xtrem-system/permission__reset_tenant_documents__name": "Réinitialiser les documents du tenant", "@sage/xtrem-system/permission__update__name": "Mettre à jour", "@sage/xtrem-system/productKey": "Valeur article", "@sage/xtrem-system/productKey2": "Valeur article {{num}}", "@sage/xtrem-system/reserved-api-user-email": "L'adresse e-mail {{value}} est réservée aux utilisateurs API.", "@sage/xtrem-system/reserved-operator-user-email": "Le format de l'adresse e-mail de l'opérateur est réservé aux utilisateurs opérationnels.", "@sage/xtrem-system/reset-tenant-data-not-admin": "Seules les administrateurs peuvent réinitialiser les données des tenants.", "@sage/xtrem-system/reset-tenant-data-not-admin-with-id": "L'utilisateur {{userId}} a tenté de réinitialiser le tenant.", "@sage/xtrem-system/reset-tenant-data-tenant-id-mismatched": "Le code tenant {{tenantId}} ne correspond pas au tenant actuel {{currentId}}.", "@sage/xtrem-system/service_options__changelog__name": "Journal des modifications", "@sage/xtrem-system/service_options__dev_tools__name": "Outils de développement", "@sage/xtrem-system/service_options__is_demo_tenant__name": "Tenant de <PERSON>", "@sage/xtrem-system/service_options__sys_device_token__name": "Jeton appareil système", "@sage/xtrem-system/service_options__sys_tag__name": "Étiquette sys", "@sage/xtrem-system/statusIcon": "Statut", "@sage/xtrem-system/sys_device_token_creation_error": "Échec de création du jeton d'appareil : {{errorMessage}}", "@sage/xtrem-system/sys_device_token_deletion_error": "Échec de suppression du jeton d'appareil : {{errorMessage}}", "@sage/xtrem-system/sys_device_token_device_token_already_created": "L'appareil courant a déjà un jeton créé.", "@sage/xtrem-system/system-user-forbidden": "Il est impossible de créer ou d'actualiser les utilisateurs système.", "@sage/xtrem-system/update-confirmation": "Enregistrement mis à jour", "@sage/xtrem-system/widgets__system_version____title": "Version système"}