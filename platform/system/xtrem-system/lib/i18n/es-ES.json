{"@sage/xtrem-system/activity__company__name": "Sociedad", "@sage/xtrem-system/activity__service_option_state__name": "Estado de opción de servicio", "@sage/xtrem-system/activity__site__name": "Planta", "@sage/xtrem-system/activity__sys_device_token__name": "Token de dispositivo de sistema", "@sage/xtrem-system/activity__sys_note__name": "Nota de sistema", "@sage/xtrem-system/activity__sys_tag__name": "Etiqueta de sistema", "@sage/xtrem-system/activity__tenant__name": "Instancia", "@sage/xtrem-system/bundle-activated": "Grupo funcional activado", "@sage/xtrem-system/bundle-deactivated": "Grupo funcional desactivado", "@sage/xtrem-system/cannot-deactivate-admin-demo-persona": "El perfil administrador para las demostraciones no se puede desactivar.", "@sage/xtrem-system/confirm-activate-service-option-message": "¿Quieres activar la opción de servicio \"{{serviceOptionName}}\"?", "@sage/xtrem-system/confirm-activate-service-option-title": "Activar opción de servicio", "@sage/xtrem-system/confirm-deactivate-service-option-message": "¿Quieres desactivar la opción de servicio \"{{serviceOptionName}}\"?", "@sage/xtrem-system/confirm-deactivate-service-option-title": "Desactivar opción de servicio", "@sage/xtrem-system/create-confirmation": "<PERSON><PERSON> c<PERSON>o", "@sage/xtrem-system/data_types__big_id__name": "Big ID", "@sage/xtrem-system/data_types__big_url__name": "Big URL", "@sage/xtrem-system/data_types__bundle_id_data_type__name": "Bundle ID data type", "@sage/xtrem-system/data_types__bundle_path_type__name": "Bundle path type", "@sage/xtrem-system/data_types__bundle_version_data_type__name": "Versión", "@sage/xtrem-system/data_types__checksum_data_type__name": "Checksum data type", "@sage/xtrem-system/data_types__code__name": "Código", "@sage/xtrem-system/data_types__code_data_type__name": "Code data type", "@sage/xtrem-system/data_types__color__name": "Color", "@sage/xtrem-system/data_types__colored_element_enum__name": "Colored element enum", "@sage/xtrem-system/data_types__company__name": "Sociedad", "@sage/xtrem-system/data_types__content_length_decimal_data_type__name": "Content length decimal data type", "@sage/xtrem-system/data_types__decimal__name": "Decimal", "@sage/xtrem-system/data_types__default_decimal_data_type__name": "Default decimal data type", "@sage/xtrem-system/data_types__description__name": "Descripción", "@sage/xtrem-system/data_types__description_array_data_type__name": "Description array data type", "@sage/xtrem-system/data_types__description_data_type__name": "Description data type", "@sage/xtrem-system/data_types__email__name": "E-mail", "@sage/xtrem-system/data_types__folder__name": "Carpeta", "@sage/xtrem-system/data_types__http_base_url__name": "HTTP base URL", "@sage/xtrem-system/data_types__http_url__name": "HTTP URL", "@sage/xtrem-system/data_types__icon__name": "Icono", "@sage/xtrem-system/data_types__id__name": "Id.", "@sage/xtrem-system/data_types__locale_datatype__name": "Locale datatype", "@sage/xtrem-system/data_types__locale_id_data_type__name": "Locale ID data type", "@sage/xtrem-system/data_types__localized_description__name": "Localized description", "@sage/xtrem-system/data_types__localized_name__name": "Localized name", "@sage/xtrem-system/data_types__localized_short_description__name": "Localized short description", "@sage/xtrem-system/data_types__localized_title__name": "Localized title", "@sage/xtrem-system/data_types__mime_type__name": "Tipo MIME", "@sage/xtrem-system/data_types__name__name": "Nombre", "@sage/xtrem-system/data_types__nanoid__name": "Nanoid", "@sage/xtrem-system/data_types__note_content_data_type__name": "Note content data type", "@sage/xtrem-system/data_types__notification_data_data_type__name": "Notification data data type", "@sage/xtrem-system/data_types__passphrase__name": "Passphrase", "@sage/xtrem-system/data_types__password__name": "Contraseña", "@sage/xtrem-system/data_types__quantity_data_type__name": "Quantity data type", "@sage/xtrem-system/data_types__rate__name": "Tipo de cambio", "@sage/xtrem-system/data_types__rgb_color__name": "Color RGB", "@sage/xtrem-system/data_types__service_option_status_enum__name": "Estado de opción de servicio", "@sage/xtrem-system/data_types__setup_id_data_type__name": "Setup ID data type", "@sage/xtrem-system/data_types__short_description__name": "Descripción corta", "@sage/xtrem-system/data_types__single_character__name": "Single character", "@sage/xtrem-system/data_types__site__name": "Planta", "@sage/xtrem-system/data_types__sys_client_notification_action_style_enum__name": "Acción de notificación de cliente de sistema", "@sage/xtrem-system/data_types__sys_client_notification_level_enum__name": "Acción de notificación de cliente de sistema", "@sage/xtrem-system/data_types__title__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/data_types__url__name": "URL", "@sage/xtrem-system/data_types__user__name": "Usuario", "@sage/xtrem-system/data_types__user_import_export_date_format_enum__name": "User import export date format enum", "@sage/xtrem-system/data_types__user_type_enum__name": "User type enum", "@sage/xtrem-system/data_types__uuid__name": "UUID", "@sage/xtrem-system/data_types__version__name": "Versión", "@sage/xtrem-system/data-types/locale_datatype__invalid_locale": "Los parámetros regionales \"{{locale}}\" no son válid<PERSON>.", "@sage/xtrem-system/data-types/rgb__invalid_rgb": "El valor \"{{value}}\" no es un código de color RGB válido.", "@sage/xtrem-system/data-types/url_datatype__invalid_protocol": "El protocolo \"{{protocol}}\" de la URL \"{{url}}\" no es uno de los esperados: {{allowedProtocols}}.", "@sage/xtrem-system/data-types/url_datatype__invalid_url": "Incorrect URL '{{url}}'.", "@sage/xtrem-system/delete-confirmation": "El registro se ha eliminado.", "@sage/xtrem-system/delete-dialog-content": "Vas a eliminar este registro. ¿Confirmar?", "@sage/xtrem-system/delete-dialog-title": "Confirmar eliminación", "@sage/xtrem-system/delete-user-forbidden": "Los usuarios no se pueden eliminar.", "@sage/xtrem-system/delete-view-warning-title": "Eliminar vista", "@sage/xtrem-system/delete-view-warning-title-message": "¿Quieres eliminar esta vista?", "@sage/xtrem-system/duplicate_first_admin": "Ya hay un administrador. No puedes crear otro.", "@sage/xtrem-system/enums__colored_element__backgroundColor": "Color de fondo", "@sage/xtrem-system/enums__colored_element__borderColor": "Color de borde", "@sage/xtrem-system/enums__colored_element__textColor": "Color de texto", "@sage/xtrem-system/enums__service_option_status__experimental": "Experimental", "@sage/xtrem-system/enums__service_option_status__released": "Lanzada", "@sage/xtrem-system/enums__service_option_status__workInProgress": "Trabajo en curso", "@sage/xtrem-system/enums__service_option_type__experimental": "Experimental", "@sage/xtrem-system/enums__service_option_type__released": "Lanzado", "@sage/xtrem-system/enums__service_option_type__workInProgress": "Trabajo en curso", "@sage/xtrem-system/enums__sys_client_notification_action_style__link": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/enums__sys_client_notification_action_style__primary": "Principal", "@sage/xtrem-system/enums__sys_client_notification_action_style__secondary": "Secundaria", "@sage/xtrem-system/enums__sys_client_notification_action_style__tertiary": "Terciaria", "@sage/xtrem-system/enums__sys_client_notification_level__approval": "Aprobación", "@sage/xtrem-system/enums__sys_client_notification_level__error": "Error", "@sage/xtrem-system/enums__sys_client_notification_level__info": "Información", "@sage/xtrem-system/enums__sys_client_notification_level__success": "Confirmación", "@sage/xtrem-system/enums__sys_client_notification_level__warning": "Aviso", "@sage/xtrem-system/enums__user_import_export_date_format__europeanDash": "dd-mm-yyyy", "@sage/xtrem-system/enums__user_import_export_date_format__europeanSlash": "dd/mm/yyyy", "@sage/xtrem-system/enums__user_import_export_date_format__isoDash": "yyyy-mm-dd", "@sage/xtrem-system/enums__user_import_export_date_format__isoSlash": "yyyy/mm/dd", "@sage/xtrem-system/enums__user_import_export_date_format__usDash": "mm-dd-yyyy", "@sage/xtrem-system/enums__user_import_export_date_format__usSlash": "mm/dd/yyyy", "@sage/xtrem-system/enums__user_type__application": "Aplicación", "@sage/xtrem-system/enums__user_type__system": "Sistema", "@sage/xtrem-system/error-packages-cannot-be": "Los paquetes no se pueden desactivar.", "@sage/xtrem-system/error-packages-cannot-be-deactivated": "Los paquetes no se pueden desactivar.", "@sage/xtrem-system/failed-to-save-view": "Ha habido un error al guardar la vista.", "@sage/xtrem-system/invalid-admin-demo-persona": "Solo puede haber un perfil administrador para las demostraciones.", "@sage/xtrem-system/invalid-api-user-email": "La dirección de e-mail {{value}} del usuario de la API no es correcta. Debe tener el formato \"api-<devid>@hostlocal.dominio\"", "@sage/xtrem-system/invalid-api-user-is-admin": "Un usuario de API no puede ser un administrador.", "@sage/xtrem-system/invalid-api-user-is-demo-persona": "Un usuario de API no puede tener un perfil para demostraciones.", "@sage/xtrem-system/invalid-email": "La dirección de e-mail {{value}} no es correcta", "@sage/xtrem-system/invalid-operator-pin-code": "El código PIN del operador debe tener un mínimo de 4 caracteres.", "@sage/xtrem-system/invalid-operator-pincode-not-unique": "El código PIN del operador debe ser único.", "@sage/xtrem-system/invalid-operator-user-email": "La dirección de e-mail del operador no es válida. Debe detener el formato \"<nombre>@hostlocal.dominio\".", "@sage/xtrem-system/invalid-operator-user-is-admin": "Un operador no puede ser un administrador.", "@sage/xtrem-system/invalid-operator-user-is-demo-persona": "Un operador no puede tener un perfil para demostraciones.", "@sage/xtrem-system/is-active/node-id-combination-not-active": "{{nodeName}} {{dataId}} no está activo.", "@sage/xtrem-system/language-master-locale-different-from-default": "Los parámetros regionales maestros deben coincidir con los definidos por defecto para el mismo idioma.", "@sage/xtrem-system/menu_item__administration": "Administración", "@sage/xtrem-system/menu_item__application-data": "Configuración de aplicación", "@sage/xtrem-system/menu_item__bundle-activation": "Activación de grupo funcional", "@sage/xtrem-system/menu_item__integrations": "", "@sage/xtrem-system/menu_item__service-option": "Opciones de servicio", "@sage/xtrem-system/menu_item__user": "Usuarios", "@sage/xtrem-system/menu_item__user-data": "Usuarios y seguridad", "@sage/xtrem-system/nodes__company__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__company__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__company__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-system/nodes__company__node_name": "Sociedad", "@sage/xtrem-system/nodes__company__property__id": "Id.", "@sage/xtrem-system/nodes__company__property__isActive": "Activa", "@sage/xtrem-system/nodes__company__property__sites": "Plantas", "@sage/xtrem-system/nodes__locale__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__locale__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__locale__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-system/nodes__locale__node_name": "Parámetros regionales", "@sage/xtrem-system/nodes__locale__property__id": "Id.", "@sage/xtrem-system/nodes__locale__property__isDefaultLocale": "Parámetros regionales por defecto", "@sage/xtrem-system/nodes__locale__property__isLanguageMasterLocale": "Idioma de parámetros regionales maestros", "@sage/xtrem-system/nodes__locale__property__language": "Idioma", "@sage/xtrem-system/nodes__site__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__site__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__site__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-system/nodes__site__node_name": "Planta", "@sage/xtrem-system/nodes__site__property__description": "Descripción", "@sage/xtrem-system/nodes__site__property__id": "Id.", "@sage/xtrem-system/nodes__site__property__isActive": "Activa", "@sage/xtrem-system/nodes__site__property__legalCompany": "Sociedad", "@sage/xtrem-system/nodes__site__property__linkedSites": "Plantas vinculadas", "@sage/xtrem-system/nodes__site__property__name": "Nombre", "@sage/xtrem-system/nodes__sys_changelog__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__sys_changelog__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__sys_changelog__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-system/nodes__sys_changelog__node_name": "Registro de cambios de sistema", "@sage/xtrem-system/nodes__sys_changelog__property__changeDate": "<PERSON><PERSON> de camb<PERSON>", "@sage/xtrem-system/nodes__sys_changelog__property__hash": "Hash", "@sage/xtrem-system/nodes__sys_changelog__property__message": "Men<PERSON><PERSON>", "@sage/xtrem-system/nodes__sys_client_notification__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__sys_client_notification__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__sys_client_notification__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-system/nodes__sys_client_notification__node_name": "Notificación de cliente de sistema", "@sage/xtrem-system/nodes__sys_client_notification__property__actions": "Acciones", "@sage/xtrem-system/nodes__sys_client_notification__property__description": "Descripción", "@sage/xtrem-system/nodes__sys_client_notification__property__icon": "Icono", "@sage/xtrem-system/nodes__sys_client_notification__property__isRead": "Lectura", "@sage/xtrem-system/nodes__sys_client_notification__property__level": "<PERSON><PERSON>", "@sage/xtrem-system/nodes__sys_client_notification__property__recipient": "Destinatario", "@sage/xtrem-system/nodes__sys_client_notification__property__shouldDisplayToast": "Mostrar notificación de sistema", "@sage/xtrem-system/nodes__sys_client_notification__property__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__sys_client_notification_action__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__sys_client_notification_action__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__sys_client_notification_action__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-system/nodes__sys_client_notification_action__node_name": "Acción de notificación de cliente de sistema", "@sage/xtrem-system/nodes__sys_client_notification_action__property__clientNotifications": "Notificaciones de cliente", "@sage/xtrem-system/nodes__sys_client_notification_action__property__link": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__sys_client_notification_action__property__style": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__sys_client_notification_action__property__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__sys_client_user_settings__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__sys_client_user_settings__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__sys_client_user_settings__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-system/nodes__sys_client_user_settings__node_name": "Configuración de usuario de cliente de sistema", "@sage/xtrem-system/nodes__sys_client_user_settings__property__content": "Contenido", "@sage/xtrem-system/nodes__sys_client_user_settings__property__description": "Descripción", "@sage/xtrem-system/nodes__sys_client_user_settings__property__elementId": "Id. de elemento", "@sage/xtrem-system/nodes__sys_client_user_settings__property__isSelected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__sys_client_user_settings__property__screenId": "<PERSON><PERSON><PERSON> <PERSON>", "@sage/xtrem-system/nodes__sys_client_user_settings__property__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__sys_client_user_settings__property__user": "Usuario", "@sage/xtrem-system/nodes__sys_csv_checksum__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__sys_csv_checksum__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__sys_csv_checksum__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-system/nodes__sys_csv_checksum__node_name": "Suma de comprobación de CSV de sistema", "@sage/xtrem-system/nodes__sys_csv_checksum__property__checksum": "Suma de comprobación", "@sage/xtrem-system/nodes__sys_csv_checksum__property__factoryName": "Nombre estándar", "@sage/xtrem-system/nodes__sys_csv_checksum__property__tenantId": "Id. de instancia", "@sage/xtrem-system/nodes__sys_custom_record__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__sys_custom_record__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__sys_custom_record__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-system/nodes__sys_custom_record__node_name": "Registro personalizado de sistema", "@sage/xtrem-system/nodes__sys_custom_record__property__bundleId": "Id. de grupo funcional", "@sage/xtrem-system/nodes__sys_custom_record__property__factoryName": "Nombre estándar", "@sage/xtrem-system/nodes__sys_custom_sql_history__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__sys_custom_sql_history__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__sys_custom_sql_history__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-system/nodes__sys_custom_sql_history__node_name": "Historial de SQL personalizado de sistema", "@sage/xtrem-system/nodes__sys_custom_sql_history__property__dryRun": "<PERSON><PERSON>la<PERSON><PERSON>", "@sage/xtrem-system/nodes__sys_custom_sql_history__property__endDateTime": "<PERSON><PERSON> y hora de fin", "@sage/xtrem-system/nodes__sys_custom_sql_history__property__result": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__sys_custom_sql_history__property__scriptContent": "Contenido de script", "@sage/xtrem-system/nodes__sys_custom_sql_history__property__scriptPath": "<PERSON><PERSON> script", "@sage/xtrem-system/nodes__sys_custom_sql_history__property__startDateTime": "Fecha y hora de inicio", "@sage/xtrem-system/nodes__sys_customer__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__sys_customer__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__sys_customer__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-system/nodes__sys_customer__node_name": "Cliente de sistema", "@sage/xtrem-system/nodes__sys_customer__property__customerId": "Id. de cliente", "@sage/xtrem-system/nodes__sys_customer__property__name": "Nombre", "@sage/xtrem-system/nodes__sys_data_validation_report__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__sys_data_validation_report__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__sys_data_validation_report__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-system/nodes__sys_data_validation_report__node_name": "Informe de validación de datos de sistema", "@sage/xtrem-system/nodes__sys_data_validation_report__property__lines": "Líneas", "@sage/xtrem-system/nodes__sys_data_validation_report_line__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__sys_data_validation_report_line__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__sys_data_validation_report_line__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-system/nodes__sys_data_validation_report_line__node_name": "Línea de informe de validación de datos de sistema", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__extraInfo": "Información adicional", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__message": "Men<PERSON><PERSON>", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__nodeId": "<PERSON><PERSON><PERSON> de nodo", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__nodeName": "Nombre de nodo", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__path": "<PERSON><PERSON>", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__report": "Informe", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__severity": "Gravedad", "@sage/xtrem-system/nodes__sys_device_token__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__sys_device_token__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__sys_device_token__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-system/nodes__sys_device_token__mutation__createDeviceToken": "Crear token de dispositivo", "@sage/xtrem-system/nodes__sys_device_token__mutation__createDeviceToken__failed": "Error al crear el token del dispositivo", "@sage/xtrem-system/nodes__sys_device_token__mutation__deleteDeviceToken": "Eliminar token de dispositivo", "@sage/xtrem-system/nodes__sys_device_token__mutation__deleteDeviceToken__failed": "Error al eliminar el token del dispositivo", "@sage/xtrem-system/nodes__sys_device_token__mutation__deleteDeviceToken__parameter__tokenId": "Id. de token", "@sage/xtrem-system/nodes__sys_device_token__node_name": "Token de dispositivo de sistema", "@sage/xtrem-system/nodes__sys_device_token__property__expiration": "Caducidad", "@sage/xtrem-system/nodes__sys_device_token__property__loginTestUrl": "URL de prueba de inicio de sesión", "@sage/xtrem-system/nodes__sys_device_token__property__loginUrl": "URL de inicio de sesión", "@sage/xtrem-system/nodes__sys_device_token__property__name": "Nombre", "@sage/xtrem-system/nodes__sys_device_token__property__tokenId": "Id. de token", "@sage/xtrem-system/nodes__sys_note__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__sys_note__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__sys_note__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-system/nodes__sys_note__node_name": "Nota de sistema", "@sage/xtrem-system/nodes__sys_note__property__content": "Contenido", "@sage/xtrem-system/nodes__sys_note__property__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__sys_note_association__node_name": "Asociación de nota de sistema", "@sage/xtrem-system/nodes__sys_note_association__property__note": "<PERSON>a", "@sage/xtrem-system/nodes__sys_note_association__property__sourceNodeId": "Id. de nodo de origen", "@sage/xtrem-system/nodes__sys_note_association__property__sourceNodeName": "Nombre de nodo de origen", "@sage/xtrem-system/nodes__sys_pack_allocation__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__sys_pack_allocation__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__sys_pack_allocation__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-system/nodes__sys_pack_allocation__mutation__activate": "Activar", "@sage/xtrem-system/nodes__sys_pack_allocation__mutation__activate__failed": "Error de activación", "@sage/xtrem-system/nodes__sys_pack_allocation__mutation__activate__parameter__packageId": "<PERSON><PERSON><PERSON> <PERSON> paque<PERSON>", "@sage/xtrem-system/nodes__sys_pack_allocation__mutation__deactivate": "Desactivar", "@sage/xtrem-system/nodes__sys_pack_allocation__mutation__deactivate__failed": "Error de desactivación", "@sage/xtrem-system/nodes__sys_pack_allocation__mutation__deactivate__parameter__packageId": "<PERSON><PERSON><PERSON> <PERSON> paque<PERSON>", "@sage/xtrem-system/nodes__sys_pack_allocation__node_name": "Asignación de paquete de sistema", "@sage/xtrem-system/nodes__sys_pack_allocation__property__isActivable": "Se puede activar", "@sage/xtrem-system/nodes__sys_pack_allocation__property__isActive": "Activa", "@sage/xtrem-system/nodes__sys_pack_allocation__property__package": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__sys_pack_allocation__property__status": "Estado", "@sage/xtrem-system/nodes__sys_pack_version__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__sys_pack_version__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__sys_pack_version__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-system/nodes__sys_pack_version__node_name": "Versión de paquete de sistema", "@sage/xtrem-system/nodes__sys_pack_version__property__isBundle": "Grupo funcional", "@sage/xtrem-system/nodes__sys_pack_version__property__isHidden": "Oculta", "@sage/xtrem-system/nodes__sys_pack_version__property__isReleased": "Lanzada", "@sage/xtrem-system/nodes__sys_pack_version__property__isUpgradeBundle": "Actualiza el grupo funcional", "@sage/xtrem-system/nodes__sys_pack_version__property__name": "Nombre", "@sage/xtrem-system/nodes__sys_pack_version__property__sqlSchemaVersion": "Versión de esquema SQL", "@sage/xtrem-system/nodes__sys_pack_version__property__version": "Versión", "@sage/xtrem-system/nodes__sys_patch_history__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__sys_patch_history__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__sys_patch_history__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-system/nodes__sys_patch_history__node_name": "Historial de parches de sistema", "@sage/xtrem-system/nodes__sys_patch_history__property__packageName": "Nombre de paquete", "@sage/xtrem-system/nodes__sys_patch_history__property__patchName": "Nombre de parche", "@sage/xtrem-system/nodes__sys_patch_history__property__result": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__sys_patch_history__property__version": "Versión", "@sage/xtrem-system/nodes__sys_service_option__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__sys_service_option__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__sys_service_option__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-system/nodes__sys_service_option__node_name": "Opción de servicio de sistema", "@sage/xtrem-system/nodes__sys_service_option__property__childServiceOptions": "Opciones de servicio secundario", "@sage/xtrem-system/nodes__sys_service_option__property__description": "Descripción", "@sage/xtrem-system/nodes__sys_service_option__property__isActiveByDefault": "Activo por defecto", "@sage/xtrem-system/nodes__sys_service_option__property__isHidden": "Oculta", "@sage/xtrem-system/nodes__sys_service_option__property__optionName": "Nombre de opción", "@sage/xtrem-system/nodes__sys_service_option__property__package": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__sys_service_option__property__parentServiceOptions": "Opciones de servicio primario", "@sage/xtrem-system/nodes__sys_service_option__property__status": "Estado", "@sage/xtrem-system/nodes__sys_service_option_state__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__sys_service_option_state__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__sys_service_option_state__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-system/nodes__sys_service_option_state__node_name": "Estado de opción de servicio de sistema", "@sage/xtrem-system/nodes__sys_service_option_state__property__imageStatus": "Estado de imagen", "@sage/xtrem-system/nodes__sys_service_option_state__property__isActivable": "Se puede activar", "@sage/xtrem-system/nodes__sys_service_option_state__property__isActive": "Activo", "@sage/xtrem-system/nodes__sys_service_option_state__property__isAvailable": "Disponible", "@sage/xtrem-system/nodes__sys_service_option_state__property__isHiddenByConfig": "Oculta por defecto", "@sage/xtrem-system/nodes__sys_service_option_state__property__isPackageActive": "Paquete activo", "@sage/xtrem-system/nodes__sys_service_option_state__property__isReadyToUse": "Lista para usar", "@sage/xtrem-system/nodes__sys_service_option_state__property__serviceOption": "Opción de servicio", "@sage/xtrem-system/nodes__sys_service_option_to_service_option__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__sys_service_option_to_service_option__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__sys_service_option_to_service_option__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-system/nodes__sys_service_option_to_service_option__node_name": "Relación entre opción de servicio de sistema y opción de sistema", "@sage/xtrem-system/nodes__sys_service_option_to_service_option__property__childServiceOption": "Opción de servicio secundario", "@sage/xtrem-system/nodes__sys_service_option_to_service_option__property__parentServiceOption": "Opción de servicio primario", "@sage/xtrem-system/nodes__sys_tag__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__sys_tag__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__sys_tag__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-system/nodes__sys_tag__node_name": "Etiqueta de sistema", "@sage/xtrem-system/nodes__sys_tag__property__color": "Color", "@sage/xtrem-system/nodes__sys_tag__property__description": "Descripción", "@sage/xtrem-system/nodes__sys_tag__property__name": "Nombre", "@sage/xtrem-system/nodes__sys_tag__property__rgbColor": "Color RGB", "@sage/xtrem-system/nodes__sys_tenant__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__sys_tenant__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__sys_tenant__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-system/nodes__sys_tenant__mutation__resetTenantDocuments": "Restablecer documentos de instancia", "@sage/xtrem-system/nodes__sys_tenant__mutation__resetTenantDocuments__failed": "Error al restablecer los documentos de la instancia", "@sage/xtrem-system/nodes__sys_tenant__mutation__resetTenantDocuments__parameter__tenantId": "Id. de instancia", "@sage/xtrem-system/nodes__sys_tenant__node_name": "Instancia de sistema", "@sage/xtrem-system/nodes__sys_tenant__property__customer": "Cliente", "@sage/xtrem-system/nodes__sys_tenant__property__directoryName": "Nombre de directorio", "@sage/xtrem-system/nodes__sys_tenant__property__name": "Nombre", "@sage/xtrem-system/nodes__sys_tenant__property__tenantId": "Id. de instancia", "@sage/xtrem-system/nodes__sys_tenant__query__getTenantInformation": "Obtener información de instancia", "@sage/xtrem-system/nodes__sys_tenant__query__getTenantInformation__failed": "Error al obtener la información de la instancia", "@sage/xtrem-system/nodes__sys_tenant__tenant_directory_name_should_be_in_kebab_case": "Utiliza guiones para separar las palabras que componen el nombre del directorio de la instancia.", "@sage/xtrem-system/nodes__sys_tenant__tenant_name_characters_not_authorized": "El nombre de la instancia tiene caracteres que no están autorizados.", "@sage/xtrem-system/nodes__sys_upgrade__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__sys_upgrade__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__sys_upgrade__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-system/nodes__sys_upgrade__node_name": "Actualización de sistema", "@sage/xtrem-system/nodes__sys_upgrade__property__bundleId": "Id. de grupo funcional", "@sage/xtrem-system/nodes__sys_upgrade__property__managedItems": "Elementos gestionados", "@sage/xtrem-system/nodes__sys_vendor__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__sys_vendor__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__sys_vendor__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-system/nodes__sys_vendor__node_name": "<PERSON><PERSON><PERSON><PERSON> de <PERSON>ste<PERSON>", "@sage/xtrem-system/nodes__sys_vendor__property__description": "Descripción", "@sage/xtrem-system/nodes__sys_vendor__property__name": "Nombre", "@sage/xtrem-system/nodes__sys-client-user-settings": "Solo puedes seleccionar una.", "@sage/xtrem-system/nodes__sys-layer__name-must-be-kebab-case": "Utiliza guiones para separar las palabras que componen el nombre.", "@sage/xtrem-system/nodes__user__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__user__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__user__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-system/nodes__user__cannot_modify_own_rights": "No puedes modificar tus permisos de administrador.", "@sage/xtrem-system/nodes__user__mutation__logPageVisit": "Visitar página de registro", "@sage/xtrem-system/nodes__user__mutation__logPageVisit__failed": "Error al visitar la página de registro", "@sage/xtrem-system/nodes__user__mutation__logPageVisit__parameter__path": "<PERSON><PERSON>", "@sage/xtrem-system/nodes__user__mutation__sendWelcomeMail": "Enviar e-mail de bienvenida", "@sage/xtrem-system/nodes__user__mutation__sendWelcomeMail__failed": "Error al enviar el e-mail de bienvenida", "@sage/xtrem-system/nodes__user__mutation__sendWelcomeMail__parameter__isAdmin": "Administrador", "@sage/xtrem-system/nodes__user__mutation__sendWelcomeMail__parameter__users": "Usuarios", "@sage/xtrem-system/nodes__user__mutation__setDemoPersona": "Definir perfil para demostraciones", "@sage/xtrem-system/nodes__user__mutation__setDemoPersona__failed": "Error al definir el perfil para las demostraciones", "@sage/xtrem-system/nodes__user__mutation__setDemoPersona__parameter__email": "E-mail", "@sage/xtrem-system/nodes__user__mutation__updateClientSettings": "Actualizar configuración de cliente", "@sage/xtrem-system/nodes__user__mutation__updateClientSettings__failed": "Error al actualizar la configuración del cliente", "@sage/xtrem-system/nodes__user__mutation__updateClientSettings__parameter__clientSettings": "Configuración de cliente", "@sage/xtrem-system/nodes__user__node_name": "Usuario", "@sage/xtrem-system/nodes__user__only_admin_can_add_admin": "Solo un administrador puede añadir o modificar los permisos de otro administrador.", "@sage/xtrem-system/nodes__user__property__clientSettings": "Configuración de cliente", "@sage/xtrem-system/nodes__user__property__displayName": "Nombre visible", "@sage/xtrem-system/nodes__user__property__email": "E-mail", "@sage/xtrem-system/nodes__user__property__firstName": "Nombre", "@sage/xtrem-system/nodes__user__property__isActive": "Activo", "@sage/xtrem-system/nodes__user__property__isAdministrator": "Administrador", "@sage/xtrem-system/nodes__user__property__isApiUser": "Usuario de API", "@sage/xtrem-system/nodes__user__property__isDemoPersona": "Perfil para demostraciones", "@sage/xtrem-system/nodes__user__property__isFirstAdminUser": "Primer usuario administrador", "@sage/xtrem-system/nodes__user__property__isOperatorUser": "Operador", "@sage/xtrem-system/nodes__user__property__lastName": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__user__property__navigation": "Navegación", "@sage/xtrem-system/nodes__user__property__operatorCode": "Código de operador", "@sage/xtrem-system/nodes__user__property__operatorId": "Id. de operador", "@sage/xtrem-system/nodes__user__property__photo": "Foto", "@sage/xtrem-system/nodes__user__property__preferences": "Preferencias", "@sage/xtrem-system/nodes__user__property__setupId": "Id. de parametrización", "@sage/xtrem-system/nodes__user__property__userType": "Tipo de usuario", "@sage/xtrem-system/nodes__user__query__demoPersonas": "Perfiles para demostraciones", "@sage/xtrem-system/nodes__user__query__demoPersonas__failed": "Error de perfiles para demostraciones", "@sage/xtrem-system/nodes__user_navigation__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__user_navigation__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__user_navigation__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-system/nodes__user_navigation__node_name": "Navegación de usuario", "@sage/xtrem-system/nodes__user_navigation__property__bookmarks": "Marcadores", "@sage/xtrem-system/nodes__user_navigation__property__history": "Historial", "@sage/xtrem-system/nodes__user_navigation__property__user": "Usuario", "@sage/xtrem-system/nodes__user_preferences__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__user_preferences__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__user_preferences__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-system/nodes__user_preferences__node_name": "Preferencias de usuario", "@sage/xtrem-system/nodes__user_preferences__property__importExportDateFormat": "Formato de fecha de importación/exportación", "@sage/xtrem-system/nodes__user_preferences__property__importExportDelimiter": "Separador de importación/exportación", "@sage/xtrem-system/nodes__user_preferences__property__isExternal": "Externo", "@sage/xtrem-system/nodes__user_preferences__property__isWelcomeMailSent": "E-mail de bienvenida enviado", "@sage/xtrem-system/nodes__user_preferences__property__user": "Usuario", "@sage/xtrem-system/nodes__user_preferences__query__activeServiceOptions": "Opciones de servicio activas", "@sage/xtrem-system/nodes__user_preferences__query__activeServiceOptions__failed": "Error de opciones de servicio activas", "@sage/xtrem-system/package__name": "Sistema", "@sage/xtrem-system/package-activated": "Paquete activado", "@sage/xtrem-system/package-deactivated": "Paquete desactivado", "@sage/xtrem-system/pages__client_user_settings_edit____title": "Crear vista", "@sage/xtrem-system/pages__client_user_settings_edit__generalSection____title": "General", "@sage/xtrem-system/pages__client_user_settings_edit__save____title": "Guardar", "@sage/xtrem-system/pages__client_user_settings_edit__title____title": "Nombre", "@sage/xtrem-system/pages__client_user_settings_list____title": "Gestionar vistas", "@sage/xtrem-system/pages__client_user_settings_list__clientSettings____columns__title__title": "Nombre", "@sage/xtrem-system/pages__client_user_settings_list__clientSettings____dropdownActions__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__client_user_settings_list__clientSettings____dropdownActions__title__remove": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__client_user_settings_list__generalSection____title": "General", "@sage/xtrem-system/pages__client_user_settings_list__save____title": "Guardar", "@sage/xtrem-system/pages__delete_page_dialog_content": "¿Quieres eliminar este registro?", "@sage/xtrem-system/pages__delete_page_dialog_title": "Confirmar eliminación", "@sage/xtrem-system/pages__pack_allocation____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-system/pages__pack_allocation____navigationPanel__optionsMenu__title__2": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__pack_allocation____navigationPanel__optionsMenu__title__3": "Grupos funcionales", "@sage/xtrem-system/pages__pack_allocation____title": "Activación de paquete", "@sage/xtrem-system/pages__pack_allocation__closePage____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__pack_allocation__generalSection____title": "General", "@sage/xtrem-system/pages__pack_allocation__isActivable____title": "Disponible", "@sage/xtrem-system/pages__pack_allocation__isActive____title": "Activo", "@sage/xtrem-system/pages__pack_allocation__package____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__pack_allocation__version____title": "Versión", "@sage/xtrem-system/pages__pack_version____navigationPanel__optionsMenu__title": "Grupos funcionales", "@sage/xtrem-system/pages__pack_version____navigationPanel__optionsMenu__title__2": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__pack_version____navigationPanel__optionsMenu__title__3": "Todos", "@sage/xtrem-system/pages__pack_version____title": "Activación de grupo funcional", "@sage/xtrem-system/pages__pack_version__closePage____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__pack_version__generalSection____title": "General", "@sage/xtrem-system/pages__pack_version__isBundle____title": "Grupo funcional", "@sage/xtrem-system/pages__pack_version__package____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__pack_version__version____title": "Versión", "@sage/xtrem-system/pages__service_option_state____navigationPanel__dropdownActions__title": "Activar", "@sage/xtrem-system/pages__service_option_state____navigationPanel__dropdownActions__title__2": "Desactivar", "@sage/xtrem-system/pages__service_option_state____navigationPanel__listItem__line_4__title": "Estado", "@sage/xtrem-system/pages__service_option_state____navigationPanel__listItem__line2__title": "Descripción", "@sage/xtrem-system/pages__service_option_state____navigationPanel__listItem__line3__title": "Activa", "@sage/xtrem-system/pages__service_option_state____navigationPanel__listItem__line4__title": "Estado", "@sage/xtrem-system/pages__service_option_state____navigationPanel__listItem__title__title": "Nombre", "@sage/xtrem-system/pages__service_option_state____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-system/pages__service_option_state____objectTypePlural": "Opciones de servicio", "@sage/xtrem-system/pages__service_option_state____objectTypeSingular": "Opción de servicio", "@sage/xtrem-system/pages__service_option_state____title": "Opciones de servicio", "@sage/xtrem-system/pages__service_option_state__cancel____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__service_option_state__closePage____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__service_option_state__description____title": "Descripción", "@sage/xtrem-system/pages__service_option_state__generalSection____title": "General", "@sage/xtrem-system/pages__service_option_state__isActivable____title": "Disponible", "@sage/xtrem-system/pages__service_option_state__isActive____title": "Activa", "@sage/xtrem-system/pages__service_option_state__isAvailable____title": "Disponible", "@sage/xtrem-system/pages__service_option_state__isHiddenByConfig____title": "Activo", "@sage/xtrem-system/pages__service_option_state__optionName____title": "Nombre", "@sage/xtrem-system/pages__service_option_state__reset_tenant_data_done": "Los datos de la instancia se han restablecido.", "@sage/xtrem-system/pages__service_option_state__save____title": "Guardar", "@sage/xtrem-system/pages__service_option_state__serviceOption____title": "Opción de servicio", "@sage/xtrem-system/pages__service_option_state__status____title": "Estado", "@sage/xtrem-system/pages__sys_changelog____navigationPanel__listItem__line2__title": "Men<PERSON><PERSON>", "@sage/xtrem-system/pages__sys_changelog____navigationPanel__listItem__line3__title": "Hora de cambio", "@sage/xtrem-system/pages__sys_changelog____navigationPanel__listItem__title__title": "Hash", "@sage/xtrem-system/pages__sys_changelog____title": "Registro de cambios", "@sage/xtrem-system/pages__sys_device_token____navigationPanel__listItem__tokenId__title": "Id.", "@sage/xtrem-system/pages__sys_device_token____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-system/pages__sys_device_token____title": "Token de dispositivo", "@sage/xtrem-system/pages__sys_device_token__createDeviceToken____title": "Guardar", "@sage/xtrem-system/pages__sys_device_token__deleteDeviceToken____title": "Eliminar", "@sage/xtrem-system/pages__sys_device_token__deviceTokenBlock____title": "Información de token de dispositivo", "@sage/xtrem-system/pages__sys_device_token__expiration____title": "Caducidad", "@sage/xtrem-system/pages__sys_device_token__loginTestUrl____title": "Vínculo de prueba de dispositivo", "@sage/xtrem-system/pages__sys_device_token__loginUrl____title": "Vínculo de inicio de sesión en dispositivo", "@sage/xtrem-system/pages__sys_device_token__name____title": "Nombre", "@sage/xtrem-system/pages__sys_device_token__tokenId____title": "Token", "@sage/xtrem-system/pages__sys_layer____navigationPanel__optionsMenu__title": "<PERSON><PERSON>", "@sage/xtrem-system/pages__sys_layer____title": "Capa de datos", "@sage/xtrem-system/pages__sys_layer___id____title": "Id.", "@sage/xtrem-system/pages__sys_layer__dataLayerInformation____title": "Información de capa de datos", "@sage/xtrem-system/pages__sys_layer__deleteLayerAction____title": "Eliminar", "@sage/xtrem-system/pages__sys_layer__description____title": "Descripción", "@sage/xtrem-system/pages__sys_layer__exportLayer____title": "Exportar capa", "@sage/xtrem-system/pages__sys_layer__generalSection____title": "General", "@sage/xtrem-system/pages__sys_layer__name____title": "Nombre de capa de datos", "@sage/xtrem-system/pages__sys_pack_allocation____navigationPanel__listItem__lineRight__title": "Versión", "@sage/xtrem-system/pages__sys_pack_allocation____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-system/pages__sys_pack_allocation____navigationPanel__optionsMenu__title__2": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__sys_pack_allocation____title": "Activación de paquete", "@sage/xtrem-system/pages__sys_pack_allocation__closePage____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__sys_pack_allocation__generalSection____title": "General", "@sage/xtrem-system/pages__sys_pack_allocation__isActivable____title": "Disponible", "@sage/xtrem-system/pages__sys_pack_allocation__isActive____title": "Activa", "@sage/xtrem-system/pages__sys_pack_allocation__package____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__sys_pack_allocation__version____title": "Versión", "@sage/xtrem-system/pages__sys_tag____navigationPanel__listItem__line2__title": "Descripción", "@sage/xtrem-system/pages__sys_tag____navigationPanel__listItem__rgbColor__title": "Color RGB", "@sage/xtrem-system/pages__sys_tag____navigationPanel__listItem__title__title": "Nombre", "@sage/xtrem-system/pages__sys_tag____title": "Etiqueta", "@sage/xtrem-system/pages__sys_tag__description____title": "Descripción", "@sage/xtrem-system/pages__sys_tag__generalSection____title": "General", "@sage/xtrem-system/pages__sys_tag__name____title": "Nombre", "@sage/xtrem-system/pages__sys_tag__rgbColor____title": "Color RGB", "@sage/xtrem-system/pages__tenant_information____title": "Información de instancia", "@sage/xtrem-system/pages__tenant_information__cancel_reset_tenant_data_button_text": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__tenant_information__confirm_reset_tenant_data_button_text": "Confirmar restablecimiento de datos de instancia", "@sage/xtrem-system/pages__tenant_information__generalSection____title": "General", "@sage/xtrem-system/pages__tenant_information__reset_tenant_data_button_text": "Restablecer datos de instancia", "@sage/xtrem-system/pages__tenant_information__resetDataDescription____content": "¿Quieres restablecer los datos de la instancia y eliminar todos los movimientos (como los pedidos de venta y de compra o los movimientos y el nivel de stock)? Esta acción no se puede revertir.", "@sage/xtrem-system/pages__tenant_information__resetTenantId____helperText": "Introduce el id. de la instancia que se va a eliminar.", "@sage/xtrem-system/pages__tenant_information__resetTenantId____title": "Confirmar id. de instancia", "@sage/xtrem-system/pages__tenant_information__resetTenantSection____title": "Restablecer", "@sage/xtrem-system/pages__tenant_information__sumologic_button_text": "Ver trazas de Sumo Logic", "@sage/xtrem-system/pages__tenant_information__sumologicLink____helperText": "Inicia sesión en Sumo Logic para poder ver las trazas.", "@sage/xtrem-system/pages__tenant_information__tenantId____title": "Id. de instancia", "@sage/xtrem-system/pages__tenant_information__tenantVersion____title": "Versión de instancia", "@sage/xtrem-system/pages__user____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-system/pages__user____title": "Usuario", "@sage/xtrem-system/pages__user___id____title": "Id.", "@sage/xtrem-system/pages__user__email____title": "E-mail", "@sage/xtrem-system/pages__user__firstName____title": "Nombre", "@sage/xtrem-system/pages__user__generalSection____title": "General", "@sage/xtrem-system/pages__user__idBlock____title": "Id.", "@sage/xtrem-system/pages__user__isActive____title": "Activo", "@sage/xtrem-system/pages__user__lastName____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__user__userInformationBlock____title": "Información de usuario", "@sage/xtrem-system/pages__user__userPhotoBlock____title": "Foto", "@sage/xtrem-system/pages-confirm-cancel": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages-confirm-delete": "Eliminar", "@sage/xtrem-system/permission__create__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/permission__delete__name": "Eliminar", "@sage/xtrem-system/permission__get_tenant_information__name": "Obtener información de instancia", "@sage/xtrem-system/permission__read__name": "<PERSON><PERSON>", "@sage/xtrem-system/permission__reset_tenant_documents__name": "Restablecer documentos de instancia", "@sage/xtrem-system/permission__update__name": "Actualizar", "@sage/xtrem-system/productKey": "Valor de producto", "@sage/xtrem-system/productKey2": "Valor de producto {{num}}", "@sage/xtrem-system/reserved-api-user-email": "La dirección de e-mail {{value}} está reservada para los usuarios de la API.", "@sage/xtrem-system/reserved-operator-user-email": "El formato de la dirección de e-mail del operador está reservado para los operadores.", "@sage/xtrem-system/reset-tenant-data-not-admin": "Solo los administradores pueden restablecer los datos de una instancia.", "@sage/xtrem-system/reset-tenant-data-not-admin-with-id": "El usuario {{userId}} ha intentado restablecer los datos de la instancia.", "@sage/xtrem-system/reset-tenant-data-tenant-id-mismatched": "El id. de la instancia {{tenantId}} no coincide con el de la instancia activa: {{currentId}}.", "@sage/xtrem-system/service_options__changelog__name": "Registro de cambios", "@sage/xtrem-system/service_options__dev_tools__name": "Herramientas de desarrollo", "@sage/xtrem-system/service_options__is_demo_tenant__name": "Instancia de demostración", "@sage/xtrem-system/service_options__sys_device_token__name": "Token de dispositivo de sistema", "@sage/xtrem-system/service_options__sys_tag__name": "Etiqueta de sistema", "@sage/xtrem-system/statusIcon": "Estado", "@sage/xtrem-system/sys_device_token_creation_error": "Error al crear el token del dispositivo. {{errorMessage}}", "@sage/xtrem-system/sys_device_token_deletion_error": "Error al eliminar el token del dispositivo. {{errorMessage}}", "@sage/xtrem-system/sys_device_token_device_token_already_created": "Este dispositivo ya tiene un token.", "@sage/xtrem-system/system-user-forbidden": "Los usuarios del sistema no se pueden crear ni actualizar.", "@sage/xtrem-system/update-confirmation": "Registro actualizado", "@sage/xtrem-system/widgets__system_version____title": "Versión del sistema"}