{"@sage/xtrem-system/activity__company__name": "公司", "@sage/xtrem-system/activity__service_option_state__name": "服务选项状态", "@sage/xtrem-system/activity__site__name": "地点", "@sage/xtrem-system/activity__sys_device_token__name": "系统设备令牌", "@sage/xtrem-system/activity__sys_note__name": "系统注释", "@sage/xtrem-system/activity__sys_tag__name": "系统标识", "@sage/xtrem-system/activity__tenant__name": "租户", "@sage/xtrem-system/bundle-activated": "软件集已激活", "@sage/xtrem-system/bundle-deactivated": "软件集已停用", "@sage/xtrem-system/cannot-deactivate-admin-demo-persona": "无法取消激活管理员演示人物。", "@sage/xtrem-system/confirm-activate-service-option-message": "您将激活服务选项：{{serviceOptionName}}。", "@sage/xtrem-system/confirm-activate-service-option-title": "激活服务选项", "@sage/xtrem-system/confirm-deactivate-service-option-message": "您将取消激活服务选项：{{serviceOptionName}}。", "@sage/xtrem-system/confirm-deactivate-service-option-title": "取消激活服务选项", "@sage/xtrem-system/create-confirmation": "记录已创建", "@sage/xtrem-system/data_types__big_id__name": "重要ID", "@sage/xtrem-system/data_types__big_url__name": "大型URL", "@sage/xtrem-system/data_types__bundle_id_data_type__name": "软件集ID数据类型", "@sage/xtrem-system/data_types__bundle_path_type__name": "软件集路径类型", "@sage/xtrem-system/data_types__bundle_version_data_type__name": "软件集版本数据类型", "@sage/xtrem-system/data_types__checksum_data_type__name": "校验和数据类型", "@sage/xtrem-system/data_types__code__name": "代码", "@sage/xtrem-system/data_types__code_data_type__name": "代码数据类型", "@sage/xtrem-system/data_types__color__name": "Color", "@sage/xtrem-system/data_types__colored_element_enum__name": "颜色元素枚举", "@sage/xtrem-system/data_types__company__name": "公司", "@sage/xtrem-system/data_types__content_length_decimal_data_type__name": "内容长度小数数据类型", "@sage/xtrem-system/data_types__decimal__name": "小数", "@sage/xtrem-system/data_types__default_decimal_data_type__name": "默认小数数据类型", "@sage/xtrem-system/data_types__description__name": "描述", "@sage/xtrem-system/data_types__description_array_data_type__name": "描述列数据类型", "@sage/xtrem-system/data_types__description_data_type__name": "描述数据类型", "@sage/xtrem-system/data_types__email__name": "Email", "@sage/xtrem-system/data_types__folder__name": "账套", "@sage/xtrem-system/data_types__http_base_url__name": "HTTP基础URL", "@sage/xtrem-system/data_types__http_url__name": "HTTP URL", "@sage/xtrem-system/data_types__icon__name": "图标", "@sage/xtrem-system/data_types__id__name": "ID", "@sage/xtrem-system/data_types__locale_datatype__name": "区域设置数据类型", "@sage/xtrem-system/data_types__locale_id_data_type__name": "区域设置ID数据类型", "@sage/xtrem-system/data_types__localized_description__name": "本地化描述", "@sage/xtrem-system/data_types__localized_name__name": "本地化名称", "@sage/xtrem-system/data_types__localized_short_description__name": "本地化简短描述", "@sage/xtrem-system/data_types__localized_title__name": "本地化标题", "@sage/xtrem-system/data_types__mime_type__name": "Mime类型", "@sage/xtrem-system/data_types__name__name": "名称", "@sage/xtrem-system/data_types__nanoid__name": "Nanoid", "@sage/xtrem-system/data_types__note_content_data_type__name": "注释内容数据类型", "@sage/xtrem-system/data_types__notification_data_data_type__name": "通知数据的数据类型", "@sage/xtrem-system/data_types__passphrase__name": "口令短语", "@sage/xtrem-system/data_types__password__name": "密码", "@sage/xtrem-system/data_types__quantity_data_type__name": "数量数据类型", "@sage/xtrem-system/data_types__rate__name": "比率", "@sage/xtrem-system/data_types__rgb_color__name": "RGB颜色", "@sage/xtrem-system/data_types__service_option_status_enum__name": "服务选项状态枚举", "@sage/xtrem-system/data_types__setup_id_data_type__name": "设置ID数据类型", "@sage/xtrem-system/data_types__short_description__name": "简短描述", "@sage/xtrem-system/data_types__single_character__name": "单个字符", "@sage/xtrem-system/data_types__site__name": "地点", "@sage/xtrem-system/data_types__sys_client_notification_action_style_enum__name": "系统客户端通知操作枚举", "@sage/xtrem-system/data_types__sys_client_notification_level_enum__name": "系统客户端通知操作枚举", "@sage/xtrem-system/data_types__title__name": "标题", "@sage/xtrem-system/data_types__url__name": "URL", "@sage/xtrem-system/data_types__user__name": "用户", "@sage/xtrem-system/data_types__user_import_export_date_format_enum__name": "用户导入导出日期格式枚举", "@sage/xtrem-system/data_types__user_type_enum__name": "用户类型枚举", "@sage/xtrem-system/data_types__uuid__name": "UUID", "@sage/xtrem-system/data_types__version__name": "版本", "@sage/xtrem-system/data-types/locale_datatype__invalid_locale": "区域设置值无效：（{{locale}}）。", "@sage/xtrem-system/data-types/rgb__invalid_rgb": "该值不是RGB颜色代码：“{{value}}”。", "@sage/xtrem-system/data-types/url_datatype__invalid_protocol": "URL“{{url}}”具有协议{{protocol}}，期望是{{allowedProtocols}}。", "@sage/xtrem-system/data-types/url_datatype__invalid_url": "URL“{{url}}”错误。", "@sage/xtrem-system/delete-confirmation": "记录已删除", "@sage/xtrem-system/delete-dialog-content": "您将要删除该记录。确认吗？", "@sage/xtrem-system/delete-dialog-title": "确认删除", "@sage/xtrem-system/delete-user-forbidden": "无法删除用户。", "@sage/xtrem-system/delete-view-warning-title": "删除视图", "@sage/xtrem-system/delete-view-warning-title-message": "您将删除这个视图。", "@sage/xtrem-system/duplicate_first_admin": "已经定义了一个管理员用户。您不能再创建另一个。", "@sage/xtrem-system/enums__colored_element__backgroundColor": "背景颜色", "@sage/xtrem-system/enums__colored_element__borderColor": "边框颜色", "@sage/xtrem-system/enums__colored_element__textColor": "文本颜色", "@sage/xtrem-system/enums__service_option_status__experimental": "实验的", "@sage/xtrem-system/enums__service_option_status__released": "已发布", "@sage/xtrem-system/enums__service_option_status__workInProgress": "工作处理中", "@sage/xtrem-system/enums__service_option_type__experimental": "试验性", "@sage/xtrem-system/enums__service_option_type__released": "已发布", "@sage/xtrem-system/enums__service_option_type__workInProgress": "进行中", "@sage/xtrem-system/enums__sys_client_notification_action_style__link": "链接", "@sage/xtrem-system/enums__sys_client_notification_action_style__primary": "主要的", "@sage/xtrem-system/enums__sys_client_notification_action_style__secondary": "次要的", "@sage/xtrem-system/enums__sys_client_notification_action_style__tertiary": "第三级的", "@sage/xtrem-system/enums__sys_client_notification_level__approval": "审批", "@sage/xtrem-system/enums__sys_client_notification_level__error": "错误", "@sage/xtrem-system/enums__sys_client_notification_level__info": "信息", "@sage/xtrem-system/enums__sys_client_notification_level__success": "成功", "@sage/xtrem-system/enums__sys_client_notification_level__warning": "警告", "@sage/xtrem-system/enums__user_import_export_date_format__europeanDash": "dd-mm-yyyy", "@sage/xtrem-system/enums__user_import_export_date_format__europeanSlash": "dd/mm/yyyy", "@sage/xtrem-system/enums__user_import_export_date_format__isoDash": "yyyy-mm-dd", "@sage/xtrem-system/enums__user_import_export_date_format__isoSlash": "yyyy/mm/dd", "@sage/xtrem-system/enums__user_import_export_date_format__usDash": "mm-dd-yyyy", "@sage/xtrem-system/enums__user_import_export_date_format__usSlash": "mm/dd/yyyy", "@sage/xtrem-system/enums__user_type__application": "应用程序", "@sage/xtrem-system/enums__user_type__system": "系统", "@sage/xtrem-system/error-packages-cannot-be": "程序包无法停用。", "@sage/xtrem-system/error-packages-cannot-be-deactivated": "程序包无法停用。", "@sage/xtrem-system/failed-to-save-view": "保存视图时出错。", "@sage/xtrem-system/invalid-admin-demo-persona": "无法创建默认之外的管理员演示人物。", "@sage/xtrem-system/invalid-api-user-email": "API用户email地址（{{value}}）错误。使用以下格式：api-<devid>@localhost.domain", "@sage/xtrem-system/invalid-api-user-is-admin": "API用户不能是管理员。", "@sage/xtrem-system/invalid-api-user-is-demo-persona": "API用户不能是演示角色。", "@sage/xtrem-system/invalid-email": "无效的email地址：（{{value}}）", "@sage/xtrem-system/invalid-operator-pin-code": "操作员PIN代码至少需要4个字符。", "@sage/xtrem-system/invalid-operator-pincode-not-unique": "操作员PIN代码必须是唯一的。", "@sage/xtrem-system/invalid-operator-user-email": "操作员用户电子邮件地址无效。应为：operator-< name > @ localhost . domain", "@sage/xtrem-system/invalid-operator-user-is-admin": "操作员用户不能是管理员", "@sage/xtrem-system/invalid-operator-user-is-demo-persona": "操作员用户不能是演示角色", "@sage/xtrem-system/is-active/node-id-combination-not-active": "未激活的{{nodeName}} {{dataId}}", "@sage/xtrem-system/language-master-locale-different-from-default": "主要区域设置必须与同一语言的默认区域设置一致。", "@sage/xtrem-system/menu_item__administration": "管理", "@sage/xtrem-system/menu_item__application-data": "应用配置", "@sage/xtrem-system/menu_item__bundle-activation": "软件集激活", "@sage/xtrem-system/menu_item__integrations": "", "@sage/xtrem-system/menu_item__service-option": "服务选项", "@sage/xtrem-system/menu_item__user": "用户", "@sage/xtrem-system/menu_item__user-data": "用户与安全", "@sage/xtrem-system/nodes__company__asyncMutation__asyncExport": "导出", "@sage/xtrem-system/nodes__company__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-system/nodes__company__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__company__node_name": "公司", "@sage/xtrem-system/nodes__company__property__id": "ID", "@sage/xtrem-system/nodes__company__property__isActive": "激活的", "@sage/xtrem-system/nodes__company__property__sites": "地点", "@sage/xtrem-system/nodes__locale__asyncMutation__asyncExport": "导出", "@sage/xtrem-system/nodes__locale__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-system/nodes__locale__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__locale__node_name": "区域设置", "@sage/xtrem-system/nodes__locale__property__id": "ID", "@sage/xtrem-system/nodes__locale__property__isDefaultLocale": "默认区域设置", "@sage/xtrem-system/nodes__locale__property__isLanguageMasterLocale": "主要区域设置的语言", "@sage/xtrem-system/nodes__locale__property__language": "语言", "@sage/xtrem-system/nodes__site__asyncMutation__asyncExport": "导出", "@sage/xtrem-system/nodes__site__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-system/nodes__site__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__site__node_name": "地点", "@sage/xtrem-system/nodes__site__property__description": "描述", "@sage/xtrem-system/nodes__site__property__id": "ID", "@sage/xtrem-system/nodes__site__property__isActive": "激活的", "@sage/xtrem-system/nodes__site__property__legalCompany": "合法企业", "@sage/xtrem-system/nodes__site__property__linkedSites": "连接地点", "@sage/xtrem-system/nodes__site__property__name": "名称", "@sage/xtrem-system/nodes__sys_changelog__asyncMutation__asyncExport": "导出", "@sage/xtrem-system/nodes__sys_changelog__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-system/nodes__sys_changelog__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_changelog__node_name": "系统变更日志", "@sage/xtrem-system/nodes__sys_changelog__property__changeDate": "变更日期", "@sage/xtrem-system/nodes__sys_changelog__property__hash": "哈希", "@sage/xtrem-system/nodes__sys_changelog__property__message": "消息", "@sage/xtrem-system/nodes__sys_client_notification__asyncMutation__asyncExport": "导出", "@sage/xtrem-system/nodes__sys_client_notification__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-system/nodes__sys_client_notification__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_client_notification__node_name": "系统客户端通知", "@sage/xtrem-system/nodes__sys_client_notification__property__actions": "操作", "@sage/xtrem-system/nodes__sys_client_notification__property__description": "描述", "@sage/xtrem-system/nodes__sys_client_notification__property__icon": "图标", "@sage/xtrem-system/nodes__sys_client_notification__property__isRead": "已读取", "@sage/xtrem-system/nodes__sys_client_notification__property__level": "层级", "@sage/xtrem-system/nodes__sys_client_notification__property__recipient": "收货人", "@sage/xtrem-system/nodes__sys_client_notification__property__shouldDisplayToast": "应显示通知弹窗", "@sage/xtrem-system/nodes__sys_client_notification__property__title": "标题", "@sage/xtrem-system/nodes__sys_client_notification_action__asyncMutation__asyncExport": "导出", "@sage/xtrem-system/nodes__sys_client_notification_action__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-system/nodes__sys_client_notification_action__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_client_notification_action__node_name": "系统客户端通知操作", "@sage/xtrem-system/nodes__sys_client_notification_action__property__clientNotifications": "客户端通知", "@sage/xtrem-system/nodes__sys_client_notification_action__property__link": "链接", "@sage/xtrem-system/nodes__sys_client_notification_action__property__style": "样式", "@sage/xtrem-system/nodes__sys_client_notification_action__property__title": "标题", "@sage/xtrem-system/nodes__sys_client_user_settings__asyncMutation__asyncExport": "导出", "@sage/xtrem-system/nodes__sys_client_user_settings__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-system/nodes__sys_client_user_settings__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_client_user_settings__node_name": "系统客户端用户设置", "@sage/xtrem-system/nodes__sys_client_user_settings__property__content": "内容", "@sage/xtrem-system/nodes__sys_client_user_settings__property__description": "描述", "@sage/xtrem-system/nodes__sys_client_user_settings__property__elementId": "元素ID", "@sage/xtrem-system/nodes__sys_client_user_settings__property__isSelected": "已选择", "@sage/xtrem-system/nodes__sys_client_user_settings__property__screenId": "屏幕ID", "@sage/xtrem-system/nodes__sys_client_user_settings__property__title": "标题", "@sage/xtrem-system/nodes__sys_client_user_settings__property__user": "用户", "@sage/xtrem-system/nodes__sys_csv_checksum__asyncMutation__asyncExport": "导出", "@sage/xtrem-system/nodes__sys_csv_checksum__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-system/nodes__sys_csv_checksum__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_csv_checksum__node_name": "系统CSV校验和", "@sage/xtrem-system/nodes__sys_csv_checksum__property__checksum": "校验和", "@sage/xtrem-system/nodes__sys_csv_checksum__property__factoryName": "工厂名称", "@sage/xtrem-system/nodes__sys_csv_checksum__property__tenantId": "租户ID", "@sage/xtrem-system/nodes__sys_custom_record__asyncMutation__asyncExport": "导出", "@sage/xtrem-system/nodes__sys_custom_record__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-system/nodes__sys_custom_record__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_custom_record__node_name": "系统自定义记录", "@sage/xtrem-system/nodes__sys_custom_record__property__bundleId": "软件集ID", "@sage/xtrem-system/nodes__sys_custom_record__property__factoryName": "工厂名称", "@sage/xtrem-system/nodes__sys_custom_sql_history__asyncMutation__asyncExport": "导出", "@sage/xtrem-system/nodes__sys_custom_sql_history__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-system/nodes__sys_custom_sql_history__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_custom_sql_history__node_name": "系统自定义SQL历史", "@sage/xtrem-system/nodes__sys_custom_sql_history__property__dryRun": "试运行", "@sage/xtrem-system/nodes__sys_custom_sql_history__property__endDateTime": "结束日期时间", "@sage/xtrem-system/nodes__sys_custom_sql_history__property__result": "结果", "@sage/xtrem-system/nodes__sys_custom_sql_history__property__scriptContent": "脚本内容", "@sage/xtrem-system/nodes__sys_custom_sql_history__property__scriptPath": "脚本路径", "@sage/xtrem-system/nodes__sys_custom_sql_history__property__startDateTime": "开始日期时间", "@sage/xtrem-system/nodes__sys_customer__asyncMutation__asyncExport": "导出", "@sage/xtrem-system/nodes__sys_customer__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-system/nodes__sys_customer__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_customer__node_name": "系统客户", "@sage/xtrem-system/nodes__sys_customer__property__customerId": "客户ID", "@sage/xtrem-system/nodes__sys_customer__property__name": "名称", "@sage/xtrem-system/nodes__sys_data_validation_report__asyncMutation__asyncExport": "导出", "@sage/xtrem-system/nodes__sys_data_validation_report__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-system/nodes__sys_data_validation_report__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_data_validation_report__node_name": "系统数据审核报表", "@sage/xtrem-system/nodes__sys_data_validation_report__property__lines": "行", "@sage/xtrem-system/nodes__sys_data_validation_report_line__asyncMutation__asyncExport": "导出", "@sage/xtrem-system/nodes__sys_data_validation_report_line__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-system/nodes__sys_data_validation_report_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_data_validation_report_line__node_name": "系统数据审核报表行", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__extraInfo": "补充信息", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__message": "消息", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__nodeId": "节点ID", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__nodeName": "节点名称", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__path": "路径", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__report": "报表", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__severity": "严重程度", "@sage/xtrem-system/nodes__sys_device_token__asyncMutation__asyncExport": "导出", "@sage/xtrem-system/nodes__sys_device_token__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-system/nodes__sys_device_token__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_device_token__mutation__createDeviceToken": "创建设备令牌", "@sage/xtrem-system/nodes__sys_device_token__mutation__createDeviceToken__failed": "创建设备令牌失败。", "@sage/xtrem-system/nodes__sys_device_token__mutation__deleteDeviceToken": "删除设备令牌", "@sage/xtrem-system/nodes__sys_device_token__mutation__deleteDeviceToken__failed": "删除设备令牌失败。", "@sage/xtrem-system/nodes__sys_device_token__mutation__deleteDeviceToken__parameter__tokenId": "令牌ID", "@sage/xtrem-system/nodes__sys_device_token__node_name": "系统设备令牌", "@sage/xtrem-system/nodes__sys_device_token__property__expiration": "到期", "@sage/xtrem-system/nodes__sys_device_token__property__loginTestUrl": "登录测试URL", "@sage/xtrem-system/nodes__sys_device_token__property__loginUrl": "登录URL", "@sage/xtrem-system/nodes__sys_device_token__property__name": "名称", "@sage/xtrem-system/nodes__sys_device_token__property__tokenId": "令牌ID", "@sage/xtrem-system/nodes__sys_note__asyncMutation__asyncExport": "导出", "@sage/xtrem-system/nodes__sys_note__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-system/nodes__sys_note__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_note__node_name": "系统注释", "@sage/xtrem-system/nodes__sys_note__property__content": "内容", "@sage/xtrem-system/nodes__sys_note__property__title": "标题", "@sage/xtrem-system/nodes__sys_note_association__node_name": "系统注释关联", "@sage/xtrem-system/nodes__sys_note_association__property__note": "注释", "@sage/xtrem-system/nodes__sys_note_association__property__sourceNodeId": "源节点ID", "@sage/xtrem-system/nodes__sys_note_association__property__sourceNodeName": "源节点名称", "@sage/xtrem-system/nodes__sys_pack_allocation__asyncMutation__asyncExport": "导出", "@sage/xtrem-system/nodes__sys_pack_allocation__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-system/nodes__sys_pack_allocation__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_pack_allocation__mutation__activate": "激活", "@sage/xtrem-system/nodes__sys_pack_allocation__mutation__activate__failed": "激活失败。", "@sage/xtrem-system/nodes__sys_pack_allocation__mutation__activate__parameter__packageId": "程序包ID", "@sage/xtrem-system/nodes__sys_pack_allocation__mutation__deactivate": "取消激活", "@sage/xtrem-system/nodes__sys_pack_allocation__mutation__deactivate__failed": "取消激活失败。", "@sage/xtrem-system/nodes__sys_pack_allocation__mutation__deactivate__parameter__packageId": "程序包ID", "@sage/xtrem-system/nodes__sys_pack_allocation__node_name": "系统程序包分配", "@sage/xtrem-system/nodes__sys_pack_allocation__property__isActivable": "可激活", "@sage/xtrem-system/nodes__sys_pack_allocation__property__isActive": "激活的", "@sage/xtrem-system/nodes__sys_pack_allocation__property__package": "程序包", "@sage/xtrem-system/nodes__sys_pack_allocation__property__status": "状态", "@sage/xtrem-system/nodes__sys_pack_version__asyncMutation__asyncExport": "导出", "@sage/xtrem-system/nodes__sys_pack_version__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-system/nodes__sys_pack_version__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_pack_version__node_name": "系统程序包版本", "@sage/xtrem-system/nodes__sys_pack_version__property__isBundle": "软件集", "@sage/xtrem-system/nodes__sys_pack_version__property__isHidden": "隐藏", "@sage/xtrem-system/nodes__sys_pack_version__property__isReleased": "已发布", "@sage/xtrem-system/nodes__sys_pack_version__property__isUpgradeBundle": "升级软件集", "@sage/xtrem-system/nodes__sys_pack_version__property__name": "名称", "@sage/xtrem-system/nodes__sys_pack_version__property__sqlSchemaVersion": "SQL架构版本", "@sage/xtrem-system/nodes__sys_pack_version__property__version": "版本", "@sage/xtrem-system/nodes__sys_patch_history__asyncMutation__asyncExport": "导出", "@sage/xtrem-system/nodes__sys_patch_history__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-system/nodes__sys_patch_history__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_patch_history__node_name": "系统补丁历史", "@sage/xtrem-system/nodes__sys_patch_history__property__packageName": "程序包名称", "@sage/xtrem-system/nodes__sys_patch_history__property__patchName": "补丁名称", "@sage/xtrem-system/nodes__sys_patch_history__property__result": "结果", "@sage/xtrem-system/nodes__sys_patch_history__property__version": "版本", "@sage/xtrem-system/nodes__sys_service_option__asyncMutation__asyncExport": "导出", "@sage/xtrem-system/nodes__sys_service_option__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-system/nodes__sys_service_option__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_service_option__node_name": "系统服务选项", "@sage/xtrem-system/nodes__sys_service_option__property__childServiceOptions": "子服务选项", "@sage/xtrem-system/nodes__sys_service_option__property__description": "描述", "@sage/xtrem-system/nodes__sys_service_option__property__isActiveByDefault": "默认激活", "@sage/xtrem-system/nodes__sys_service_option__property__isHidden": "隐藏", "@sage/xtrem-system/nodes__sys_service_option__property__optionName": "选项名称", "@sage/xtrem-system/nodes__sys_service_option__property__package": "程序包", "@sage/xtrem-system/nodes__sys_service_option__property__parentServiceOptions": "主服务选项", "@sage/xtrem-system/nodes__sys_service_option__property__status": "状态", "@sage/xtrem-system/nodes__sys_service_option_state__asyncMutation__asyncExport": "导出", "@sage/xtrem-system/nodes__sys_service_option_state__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-system/nodes__sys_service_option_state__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_service_option_state__node_name": "系统服务选项状态", "@sage/xtrem-system/nodes__sys_service_option_state__property__imageStatus": "图像状态", "@sage/xtrem-system/nodes__sys_service_option_state__property__isActivable": "可激活", "@sage/xtrem-system/nodes__sys_service_option_state__property__isActive": "激活的", "@sage/xtrem-system/nodes__sys_service_option_state__property__isAvailable": "可用", "@sage/xtrem-system/nodes__sys_service_option_state__property__isHiddenByConfig": "默认隐藏", "@sage/xtrem-system/nodes__sys_service_option_state__property__isPackageActive": "激活程序包", "@sage/xtrem-system/nodes__sys_service_option_state__property__isReadyToUse": "随时可用", "@sage/xtrem-system/nodes__sys_service_option_state__property__serviceOption": "服务选项", "@sage/xtrem-system/nodes__sys_service_option_to_service_option__asyncMutation__asyncExport": "导出", "@sage/xtrem-system/nodes__sys_service_option_to_service_option__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-system/nodes__sys_service_option_to_service_option__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_service_option_to_service_option__node_name": "系统服务选项到服务选项", "@sage/xtrem-system/nodes__sys_service_option_to_service_option__property__childServiceOption": "子服务选项", "@sage/xtrem-system/nodes__sys_service_option_to_service_option__property__parentServiceOption": "主服务选项", "@sage/xtrem-system/nodes__sys_tag__asyncMutation__asyncExport": "导出", "@sage/xtrem-system/nodes__sys_tag__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-system/nodes__sys_tag__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_tag__node_name": "系统标识", "@sage/xtrem-system/nodes__sys_tag__property__color": "Color", "@sage/xtrem-system/nodes__sys_tag__property__description": "描述", "@sage/xtrem-system/nodes__sys_tag__property__name": "名称", "@sage/xtrem-system/nodes__sys_tag__property__rgbColor": "RGB颜色", "@sage/xtrem-system/nodes__sys_tenant__asyncMutation__asyncExport": "导出", "@sage/xtrem-system/nodes__sys_tenant__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-system/nodes__sys_tenant__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_tenant__mutation__resetTenantDocuments": "重置租户文档", "@sage/xtrem-system/nodes__sys_tenant__mutation__resetTenantDocuments__failed": "重置租户文档失败。", "@sage/xtrem-system/nodes__sys_tenant__mutation__resetTenantDocuments__parameter__tenantId": "租户ID", "@sage/xtrem-system/nodes__sys_tenant__node_name": "系统租户", "@sage/xtrem-system/nodes__sys_tenant__property__customer": "客户", "@sage/xtrem-system/nodes__sys_tenant__property__directoryName": "目录名称", "@sage/xtrem-system/nodes__sys_tenant__property__name": "名称", "@sage/xtrem-system/nodes__sys_tenant__property__tenantId": "租户ID", "@sage/xtrem-system/nodes__sys_tenant__query__getTenantInformation": "获取租户信息", "@sage/xtrem-system/nodes__sys_tenant__query__getTenantInformation__failed": "获取租户信息失败。", "@sage/xtrem-system/nodes__sys_tenant__tenant_directory_name_should_be_in_kebab_case": "写入租户目录名时以短横线隔开。", "@sage/xtrem-system/nodes__sys_tenant__tenant_name_characters_not_authorized": "租户名称包含禁用的字符。", "@sage/xtrem-system/nodes__sys_upgrade__asyncMutation__asyncExport": "导出", "@sage/xtrem-system/nodes__sys_upgrade__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-system/nodes__sys_upgrade__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_upgrade__node_name": "系统升级", "@sage/xtrem-system/nodes__sys_upgrade__property__bundleId": "软件集ID", "@sage/xtrem-system/nodes__sys_upgrade__property__managedItems": "管理项目", "@sage/xtrem-system/nodes__sys_vendor__asyncMutation__asyncExport": "导出", "@sage/xtrem-system/nodes__sys_vendor__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-system/nodes__sys_vendor__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_vendor__node_name": "系统供应商", "@sage/xtrem-system/nodes__sys_vendor__property__description": "描述", "@sage/xtrem-system/nodes__sys_vendor__property__name": "名称", "@sage/xtrem-system/nodes__sys-client-user-settings": "您只能选择一个。", "@sage/xtrem-system/nodes__sys-layer__name-must-be-kebab-case": "名称必须以连字符分隔。", "@sage/xtrem-system/nodes__user__asyncMutation__asyncExport": "导出", "@sage/xtrem-system/nodes__user__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-system/nodes__user__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__user__cannot_modify_own_rights": "不允许您修改自己的管理员权限。", "@sage/xtrem-system/nodes__user__mutation__logPageVisit": "记录页面访问", "@sage/xtrem-system/nodes__user__mutation__logPageVisit__failed": "日志页面访问失败。", "@sage/xtrem-system/nodes__user__mutation__logPageVisit__parameter__path": "路径", "@sage/xtrem-system/nodes__user__mutation__sendWelcomeMail": "发送欢迎电子邮件", "@sage/xtrem-system/nodes__user__mutation__sendWelcomeMail__failed": "欢迎邮件发送失败。", "@sage/xtrem-system/nodes__user__mutation__sendWelcomeMail__parameter__isAdmin": "管理员", "@sage/xtrem-system/nodes__user__mutation__sendWelcomeMail__parameter__users": "用户", "@sage/xtrem-system/nodes__user__mutation__setDemoPersona": "设置演示人物", "@sage/xtrem-system/nodes__user__mutation__setDemoPersona__failed": "设置演示角色失败。", "@sage/xtrem-system/nodes__user__mutation__setDemoPersona__parameter__email": "Email", "@sage/xtrem-system/nodes__user__mutation__updateClientSettings": "更新客户端设置", "@sage/xtrem-system/nodes__user__mutation__updateClientSettings__failed": "更新客户端设置失败。", "@sage/xtrem-system/nodes__user__mutation__updateClientSettings__parameter__clientSettings": "客户端设置", "@sage/xtrem-system/nodes__user__node_name": "用户", "@sage/xtrem-system/nodes__user__only_admin_can_add_admin": "只有管理员用户可以添加或修改其他用户的管理员权限。", "@sage/xtrem-system/nodes__user__property__clientSettings": "客户端设置", "@sage/xtrem-system/nodes__user__property__displayName": "显示名称", "@sage/xtrem-system/nodes__user__property__email": "Email", "@sage/xtrem-system/nodes__user__property__firstName": "名字", "@sage/xtrem-system/nodes__user__property__isActive": "激活的", "@sage/xtrem-system/nodes__user__property__isAdministrator": "管理员", "@sage/xtrem-system/nodes__user__property__isApiUser": "API用户", "@sage/xtrem-system/nodes__user__property__isDemoPersona": "演示人物", "@sage/xtrem-system/nodes__user__property__isFirstAdminUser": "第一个管理员用户", "@sage/xtrem-system/nodes__user__property__isOperatorUser": "是操作员用户", "@sage/xtrem-system/nodes__user__property__lastName": "姓氏", "@sage/xtrem-system/nodes__user__property__navigation": "导航", "@sage/xtrem-system/nodes__user__property__operatorCode": "操作员代码", "@sage/xtrem-system/nodes__user__property__operatorId": "操作员代码", "@sage/xtrem-system/nodes__user__property__photo": "照片", "@sage/xtrem-system/nodes__user__property__preferences": "首选项", "@sage/xtrem-system/nodes__user__property__setupId": "设置ID", "@sage/xtrem-system/nodes__user__property__userType": "用户类型", "@sage/xtrem-system/nodes__user__query__demoPersonas": "演示人物", "@sage/xtrem-system/nodes__user__query__demoPersonas__failed": "演示角色失败。", "@sage/xtrem-system/nodes__user_navigation__asyncMutation__asyncExport": "导出", "@sage/xtrem-system/nodes__user_navigation__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-system/nodes__user_navigation__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__user_navigation__node_name": "用户导航", "@sage/xtrem-system/nodes__user_navigation__property__bookmarks": "书签", "@sage/xtrem-system/nodes__user_navigation__property__history": "历史", "@sage/xtrem-system/nodes__user_navigation__property__user": "用户", "@sage/xtrem-system/nodes__user_preferences__asyncMutation__asyncExport": "导出", "@sage/xtrem-system/nodes__user_preferences__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-system/nodes__user_preferences__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__user_preferences__node_name": "用户首选项", "@sage/xtrem-system/nodes__user_preferences__property__importExportDateFormat": "导入/导出日期格式", "@sage/xtrem-system/nodes__user_preferences__property__importExportDelimiter": "导入/导出分隔符", "@sage/xtrem-system/nodes__user_preferences__property__isExternal": "是外部的", "@sage/xtrem-system/nodes__user_preferences__property__isWelcomeMailSent": "已发送欢迎邮件", "@sage/xtrem-system/nodes__user_preferences__property__user": "用户", "@sage/xtrem-system/nodes__user_preferences__query__activeServiceOptions": "激活服务选项", "@sage/xtrem-system/nodes__user_preferences__query__activeServiceOptions__failed": "激活服务选项失败。", "@sage/xtrem-system/package__name": "系统", "@sage/xtrem-system/package-activated": "程序包已激活", "@sage/xtrem-system/package-deactivated": "程序包已取消激活", "@sage/xtrem-system/pages__client_user_settings_edit____title": "创建视图", "@sage/xtrem-system/pages__client_user_settings_edit__generalSection____title": "常规", "@sage/xtrem-system/pages__client_user_settings_edit__save____title": "保存", "@sage/xtrem-system/pages__client_user_settings_edit__title____title": "名称", "@sage/xtrem-system/pages__client_user_settings_list____title": "管理视图", "@sage/xtrem-system/pages__client_user_settings_list__clientSettings____columns__title__title": "名称", "@sage/xtrem-system/pages__client_user_settings_list__clientSettings____dropdownActions__title": "移除", "@sage/xtrem-system/pages__client_user_settings_list__clientSettings____dropdownActions__title__remove": "移除", "@sage/xtrem-system/pages__client_user_settings_list__generalSection____title": "常规", "@sage/xtrem-system/pages__client_user_settings_list__save____title": "保存", "@sage/xtrem-system/pages__delete_page_dialog_content": "您将要删除该记录。", "@sage/xtrem-system/pages__delete_page_dialog_title": "确认删除", "@sage/xtrem-system/pages__pack_allocation____navigationPanel__optionsMenu__title": "全部", "@sage/xtrem-system/pages__pack_allocation____navigationPanel__optionsMenu__title__2": "程序包", "@sage/xtrem-system/pages__pack_allocation____navigationPanel__optionsMenu__title__3": "软件集", "@sage/xtrem-system/pages__pack_allocation____title": "程序包激活", "@sage/xtrem-system/pages__pack_allocation__closePage____title": "关闭", "@sage/xtrem-system/pages__pack_allocation__generalSection____title": "常规", "@sage/xtrem-system/pages__pack_allocation__isActivable____title": "可用", "@sage/xtrem-system/pages__pack_allocation__isActive____title": "激活的", "@sage/xtrem-system/pages__pack_allocation__package____title": "程序包", "@sage/xtrem-system/pages__pack_allocation__version____title": "版本", "@sage/xtrem-system/pages__pack_version____navigationPanel__optionsMenu__title": "软件集", "@sage/xtrem-system/pages__pack_version____navigationPanel__optionsMenu__title__2": "程序包", "@sage/xtrem-system/pages__pack_version____navigationPanel__optionsMenu__title__3": "全部", "@sage/xtrem-system/pages__pack_version____title": "软件集激活", "@sage/xtrem-system/pages__pack_version__closePage____title": "关闭", "@sage/xtrem-system/pages__pack_version__generalSection____title": "常规", "@sage/xtrem-system/pages__pack_version__isBundle____title": "软件集", "@sage/xtrem-system/pages__pack_version__package____title": "程序包", "@sage/xtrem-system/pages__pack_version__version____title": "版本", "@sage/xtrem-system/pages__service_option_state____navigationPanel__dropdownActions__title": "激活", "@sage/xtrem-system/pages__service_option_state____navigationPanel__dropdownActions__title__2": "取消激活", "@sage/xtrem-system/pages__service_option_state____navigationPanel__listItem__line_4__title": "状态", "@sage/xtrem-system/pages__service_option_state____navigationPanel__listItem__line2__title": "描述", "@sage/xtrem-system/pages__service_option_state____navigationPanel__listItem__line3__title": "激活的", "@sage/xtrem-system/pages__service_option_state____navigationPanel__listItem__line4__title": "状态", "@sage/xtrem-system/pages__service_option_state____navigationPanel__listItem__title__title": "名称", "@sage/xtrem-system/pages__service_option_state____navigationPanel__optionsMenu__title": "全部", "@sage/xtrem-system/pages__service_option_state____objectTypePlural": "服务选项", "@sage/xtrem-system/pages__service_option_state____objectTypeSingular": "服务选项", "@sage/xtrem-system/pages__service_option_state____title": "服务选项", "@sage/xtrem-system/pages__service_option_state__cancel____title": "取消", "@sage/xtrem-system/pages__service_option_state__closePage____title": "关闭", "@sage/xtrem-system/pages__service_option_state__description____title": "描述", "@sage/xtrem-system/pages__service_option_state__generalSection____title": "常规", "@sage/xtrem-system/pages__service_option_state__isActivable____title": "可用", "@sage/xtrem-system/pages__service_option_state__isActive____title": "激活的", "@sage/xtrem-system/pages__service_option_state__isAvailable____title": "可用", "@sage/xtrem-system/pages__service_option_state__isHiddenByConfig____title": "激活的", "@sage/xtrem-system/pages__service_option_state__optionName____title": "名称", "@sage/xtrem-system/pages__service_option_state__reset_tenant_data_done": "完成租户数据重置", "@sage/xtrem-system/pages__service_option_state__save____title": "保存", "@sage/xtrem-system/pages__service_option_state__serviceOption____title": "服务选项", "@sage/xtrem-system/pages__service_option_state__status____title": "状态", "@sage/xtrem-system/pages__sys_changelog____navigationPanel__listItem__line2__title": "消息", "@sage/xtrem-system/pages__sys_changelog____navigationPanel__listItem__line3__title": "变更时间戳", "@sage/xtrem-system/pages__sys_changelog____navigationPanel__listItem__title__title": "哈希", "@sage/xtrem-system/pages__sys_changelog____title": "变更日志", "@sage/xtrem-system/pages__sys_device_token____navigationPanel__listItem__tokenId__title": "ID", "@sage/xtrem-system/pages__sys_device_token____navigationPanel__optionsMenu__title": "全部", "@sage/xtrem-system/pages__sys_device_token____title": "设备令牌", "@sage/xtrem-system/pages__sys_device_token__createDeviceToken____title": "保存", "@sage/xtrem-system/pages__sys_device_token__deleteDeviceToken____title": "删除", "@sage/xtrem-system/pages__sys_device_token__deviceTokenBlock____title": "设备令牌信息", "@sage/xtrem-system/pages__sys_device_token__expiration____title": "到期", "@sage/xtrem-system/pages__sys_device_token__loginTestUrl____title": "设备测试链接", "@sage/xtrem-system/pages__sys_device_token__loginUrl____title": "设备登录链接", "@sage/xtrem-system/pages__sys_device_token__name____title": "名称", "@sage/xtrem-system/pages__sys_device_token__tokenId____title": "令牌", "@sage/xtrem-system/pages__sys_layer____navigationPanel__optionsMenu__title": "全部", "@sage/xtrem-system/pages__sys_layer____title": "数据层", "@sage/xtrem-system/pages__sys_layer___id____title": "ID", "@sage/xtrem-system/pages__sys_layer__dataLayerInformation____title": "数据层信息", "@sage/xtrem-system/pages__sys_layer__deleteLayerAction____title": "删除", "@sage/xtrem-system/pages__sys_layer__description____title": "描述", "@sage/xtrem-system/pages__sys_layer__exportLayer____title": "导出层", "@sage/xtrem-system/pages__sys_layer__generalSection____title": "常规", "@sage/xtrem-system/pages__sys_layer__name____title": "数据层名称", "@sage/xtrem-system/pages__sys_pack_allocation____navigationPanel__listItem__lineRight__title": "版本", "@sage/xtrem-system/pages__sys_pack_allocation____navigationPanel__optionsMenu__title": "全部", "@sage/xtrem-system/pages__sys_pack_allocation____navigationPanel__optionsMenu__title__2": "程序包", "@sage/xtrem-system/pages__sys_pack_allocation____title": "程序包激活", "@sage/xtrem-system/pages__sys_pack_allocation__closePage____title": "关闭", "@sage/xtrem-system/pages__sys_pack_allocation__generalSection____title": "常规", "@sage/xtrem-system/pages__sys_pack_allocation__isActivable____title": "可用", "@sage/xtrem-system/pages__sys_pack_allocation__isActive____title": "激活的", "@sage/xtrem-system/pages__sys_pack_allocation__package____title": "程序包", "@sage/xtrem-system/pages__sys_pack_allocation__version____title": "版本", "@sage/xtrem-system/pages__sys_tag____navigationPanel__listItem__line2__title": "描述", "@sage/xtrem-system/pages__sys_tag____navigationPanel__listItem__rgbColor__title": "RGB颜色", "@sage/xtrem-system/pages__sys_tag____navigationPanel__listItem__title__title": "名称", "@sage/xtrem-system/pages__sys_tag____title": "标识", "@sage/xtrem-system/pages__sys_tag__description____title": "描述", "@sage/xtrem-system/pages__sys_tag__generalSection____title": "常规", "@sage/xtrem-system/pages__sys_tag__name____title": "名称", "@sage/xtrem-system/pages__sys_tag__rgbColor____title": "RGB颜色", "@sage/xtrem-system/pages__tenant_information____title": "租户信息", "@sage/xtrem-system/pages__tenant_information__cancel_reset_tenant_data_button_text": "取消", "@sage/xtrem-system/pages__tenant_information__confirm_reset_tenant_data_button_text": "确认重新设置租户数据", "@sage/xtrem-system/pages__tenant_information__generalSection____title": "常规", "@sage/xtrem-system/pages__tenant_information__reset_tenant_data_button_text": "重新设置租户数据", "@sage/xtrem-system/pages__tenant_information__resetDataDescription____content": "重置租户数据将删除所有的交易，（例如销售和采购订单，存货水平和交易）。\n该操作不可恢复。确定吗？", "@sage/xtrem-system/pages__tenant_information__resetTenantId____helperText": "请输入要删除的租户ID", "@sage/xtrem-system/pages__tenant_information__resetTenantId____title": "确认租户ID", "@sage/xtrem-system/pages__tenant_information__resetTenantSection____title": "重置", "@sage/xtrem-system/pages__tenant_information__sumologic_button_text": "查看Sumo Logic日志", "@sage/xtrem-system/pages__tenant_information__sumologicLink____helperText": "您需要登录Sumo Logic才能查看日志。", "@sage/xtrem-system/pages__tenant_information__tenantId____title": "租户ID", "@sage/xtrem-system/pages__tenant_information__tenantVersion____title": "租户版本", "@sage/xtrem-system/pages__user____navigationPanel__optionsMenu__title": "全部", "@sage/xtrem-system/pages__user____title": "用户", "@sage/xtrem-system/pages__user___id____title": "ID", "@sage/xtrem-system/pages__user__email____title": "Email", "@sage/xtrem-system/pages__user__firstName____title": "名字", "@sage/xtrem-system/pages__user__generalSection____title": "常规", "@sage/xtrem-system/pages__user__idBlock____title": "ID", "@sage/xtrem-system/pages__user__isActive____title": "激活的", "@sage/xtrem-system/pages__user__lastName____title": "姓氏", "@sage/xtrem-system/pages__user__userInformationBlock____title": "用户信息", "@sage/xtrem-system/pages__user__userPhotoBlock____title": "照片", "@sage/xtrem-system/pages-confirm-cancel": "取消", "@sage/xtrem-system/pages-confirm-delete": "删除", "@sage/xtrem-system/permission__create__name": "创建", "@sage/xtrem-system/permission__delete__name": "删除", "@sage/xtrem-system/permission__get_tenant_information__name": "获取租户信息", "@sage/xtrem-system/permission__read__name": "读取", "@sage/xtrem-system/permission__reset_tenant_documents__name": "重置租户文档", "@sage/xtrem-system/permission__update__name": "更新", "@sage/xtrem-system/productKey": "产品值", "@sage/xtrem-system/productKey2": "产品值{{num}}", "@sage/xtrem-system/reserved-api-user-email": "为API用户保留Email地址{{value}}。", "@sage/xtrem-system/reserved-operator-user-email": "操作员电子邮件地址格式仅供操作员用户使用。", "@sage/xtrem-system/reset-tenant-data-not-admin": "只有管理员可以重置租户数据", "@sage/xtrem-system/reset-tenant-data-not-admin-with-id": "用户{{userId}}尝试重置租户", "@sage/xtrem-system/reset-tenant-data-tenant-id-mismatched": "租户{{tenantId}}的ID与有效租户{{currentId}}不匹配。", "@sage/xtrem-system/service_options__changelog__name": "变更日志", "@sage/xtrem-system/service_options__dev_tools__name": "开发工具", "@sage/xtrem-system/service_options__is_demo_tenant__name": "是演示租户", "@sage/xtrem-system/service_options__sys_device_token__name": "系统设备令牌", "@sage/xtrem-system/service_options__sys_tag__name": "系统标识", "@sage/xtrem-system/statusIcon": "状态", "@sage/xtrem-system/sys_device_token_creation_error": "设备令牌创建失败：{{errorMessage}}", "@sage/xtrem-system/sys_device_token_deletion_error": "设备令牌删除失败：{{errorMessage}}", "@sage/xtrem-system/sys_device_token_device_token_already_created": "当前设备已经创建了令牌。", "@sage/xtrem-system/system-user-forbidden": "无法创建或更新系统用户。", "@sage/xtrem-system/update-confirmation": "记录已更新", "@sage/xtrem-system/widgets__system_version____title": "系统版本"}