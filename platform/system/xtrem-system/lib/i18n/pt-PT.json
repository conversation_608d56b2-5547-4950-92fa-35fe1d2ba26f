{"@sage/xtrem-system/activity__company__name": "Sociedade", "@sage/xtrem-system/activity__service_option_state__name": "Status da opção de serviço", "@sage/xtrem-system/activity__site__name": "Estab.", "@sage/xtrem-system/activity__sys_device_token__name": "Token de dispositivo de sistema", "@sage/xtrem-system/activity__sys_note__name": "<PERSON>a sistema", "@sage/xtrem-system/activity__sys_tag__name": "Etiqueta do sistema (Sys tag)", "@sage/xtrem-system/activity__tenant__name": "Tenant", "@sage/xtrem-system/bundle-activated": "Pacote (bundle) ativado", "@sage/xtrem-system/bundle-deactivated": "Pacote (bundle) desativado", "@sage/xtrem-system/cannot-deactivate-admin-demo-persona": "Não é possível desativar a persona de demonstração Admin.", "@sage/xtrem-system/confirm-activate-service-option-message": "Está prestes a ativar a opção de serviço: {{serviceOptionName}}.", "@sage/xtrem-system/confirm-activate-service-option-title": "Ativar a opção de serviço", "@sage/xtrem-system/confirm-deactivate-service-option-message": "Está prestes a desativar a opção de serviço: {{serviceOptionName}}.", "@sage/xtrem-system/confirm-deactivate-service-option-title": "Desativar a opção de serviço", "@sage/xtrem-system/create-confirmation": "<PERSON>tro criado", "@sage/xtrem-system/data_types__big_id__name": "Grande ID (Big ID)", "@sage/xtrem-system/data_types__big_url__name": "Grande URL", "@sage/xtrem-system/data_types__bundle_id_data_type__name": "Tipo de dados do Bundle ID", "@sage/xtrem-system/data_types__bundle_path_type__name": "Tipo de caminho do Bundle", "@sage/xtrem-system/data_types__bundle_version_data_type__name": "Tipo de dados da versão do Bundle", "@sage/xtrem-system/data_types__checksum_data_type__name": "Tipo de dados do checksum", "@sage/xtrem-system/data_types__code__name": "Código", "@sage/xtrem-system/data_types__code_data_type__name": "Código tipo de dados", "@sage/xtrem-system/data_types__color__name": "Color", "@sage/xtrem-system/data_types__colored_element_enum__name": "Enum do elemento colorido", "@sage/xtrem-system/data_types__company__name": "Sociedade", "@sage/xtrem-system/data_types__content_length_decimal_data_type__name": "Comprimento do conteúdo do tipo de dados decimal", "@sage/xtrem-system/data_types__decimal__name": "Décimal", "@sage/xtrem-system/data_types__default_decimal_data_type__name": "Tipo de dados decimal por padrão", "@sage/xtrem-system/data_types__description__name": "Descrição", "@sage/xtrem-system/data_types__description_array_data_type__name": "Descrição tipo de dados da matriz (array)", "@sage/xtrem-system/data_types__description_data_type__name": "Descrição do tipo de dados", "@sage/xtrem-system/data_types__email__name": "E-mail", "@sage/xtrem-system/data_types__folder__name": "Do<PERSON>r (folder)", "@sage/xtrem-system/data_types__http_base_url__name": "HTTP base URL", "@sage/xtrem-system/data_types__http_url__name": "HTTP URL", "@sage/xtrem-system/data_types__icon__name": "Ícone", "@sage/xtrem-system/data_types__id__name": "ID", "@sage/xtrem-system/data_types__locale_datatype__name": "Tipo de dados de locale", "@sage/xtrem-system/data_types__locale_id_data_type__name": "Tipo de dados do Locale ID", "@sage/xtrem-system/data_types__localized_description__name": "Descrição localizada", "@sage/xtrem-system/data_types__localized_name__name": "Nome localizado", "@sage/xtrem-system/data_types__localized_short_description__name": "Descrição curta localizada", "@sage/xtrem-system/data_types__localized_title__name": "Título localizado", "@sage/xtrem-system/data_types__mime_type__name": "Tipo MIME", "@sage/xtrem-system/data_types__name__name": "Nome", "@sage/xtrem-system/data_types__nanoid__name": "Nanoide", "@sage/xtrem-system/data_types__note_content_data_type__name": "Tipo de dados do conteúdo da nota", "@sage/xtrem-system/data_types__notification_data_data_type__name": "Tipo de dados de notificação", "@sage/xtrem-system/data_types__passphrase__name": "Frase secreta", "@sage/xtrem-system/data_types__password__name": "Palavra-chave", "@sage/xtrem-system/data_types__quantity_data_type__name": "Tipo de dados de quantidade", "@sage/xtrem-system/data_types__rate__name": "Taxa", "@sage/xtrem-system/data_types__rgb_color__name": "Cor RGB", "@sage/xtrem-system/data_types__service_option_status_enum__name": "Enum. status da opção de serviço", "@sage/xtrem-system/data_types__setup_id_data_type__name": "Tipo de dados da ID de configuração", "@sage/xtrem-system/data_types__short_description__name": "Descrição curta", "@sage/xtrem-system/data_types__single_character__name": "Caractere único", "@sage/xtrem-system/data_types__site__name": "Estabelecimento", "@sage/xtrem-system/data_types__sys_client_notification_action_style_enum__name": "Estilo de ação enum de notificação do cliente do sistema", "@sage/xtrem-system/data_types__sys_client_notification_level_enum__name": "Enum do nível de notificação do cliente do sistema", "@sage/xtrem-system/data_types__title__name": "Titulo", "@sage/xtrem-system/data_types__url__name": "URL", "@sage/xtrem-system/data_types__user__name": "Utilizador", "@sage/xtrem-system/data_types__user_import_export_date_format_enum__name": "Formato de data de exportação de importação do utilizador enum", "@sage/xtrem-system/data_types__user_type_enum__name": "Tipo de utilizador enum", "@sage/xtrem-system/data_types__uuid__name": "UUID", "@sage/xtrem-system/data_types__version__name": "Vers<PERSON>", "@sage/xtrem-system/data-types/locale_datatype__invalid_locale": "O valor regional (locale) é inválido: ({{locale}}).", "@sage/xtrem-system/data-types/rgb__invalid_rgb": "Este valor não é um código de cor RGB: \"{{value}}\".", "@sage/xtrem-system/data-types/url_datatype__invalid_protocol": "URL '{{url}}' obteve o protocolo {{protocol}}, esperando {{allowedProtocols}}.", "@sage/xtrem-system/data-types/url_datatype__invalid_url": "URL incorreto '{{url}}'.", "@sage/xtrem-system/delete-confirmation": "<PERSON><PERSON> eliminado", "@sage/xtrem-system/delete-dialog-content": "Est<PERSON> prestes a eliminar este registro. Confirma?", "@sage/xtrem-system/delete-dialog-title": "Confirma a eliminação", "@sage/xtrem-system/delete-user-forbidden": "Usuários não podem ser eliminados.", "@sage/xtrem-system/delete-view-warning-title": "Eliminar vista", "@sage/xtrem-system/delete-view-warning-title-message": "Est<PERSON> prestes a eliminar esta vista.", "@sage/xtrem-system/duplicate_first_admin": "Já está definido um utilizador admin.. Não é possível criar outro.", "@sage/xtrem-system/enums__colored_element__backgroundColor": "Cor de fundo", "@sage/xtrem-system/enums__colored_element__borderColor": "<PERSON><PERSON> <PERSON>", "@sage/xtrem-system/enums__colored_element__textColor": "Cor do texto", "@sage/xtrem-system/enums__service_option_status__experimental": "Experimental", "@sage/xtrem-system/enums__service_option_status__released": "Lançado", "@sage/xtrem-system/enums__service_option_status__workInProgress": "Trabalho em curso", "@sage/xtrem-system/enums__service_option_type__experimental": "Experimental", "@sage/xtrem-system/enums__service_option_type__released": "Liberado", "@sage/xtrem-system/enums__service_option_type__workInProgress": "Trabalho em curso", "@sage/xtrem-system/enums__sys_client_notification_action_style__link": "Link", "@sage/xtrem-system/enums__sys_client_notification_action_style__primary": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/enums__sys_client_notification_action_style__secondary": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/enums__sys_client_notification_action_style__tertiary": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/enums__sys_client_notification_level__approval": "Aprovação", "@sage/xtrem-system/enums__sys_client_notification_level__error": "Erro", "@sage/xtrem-system/enums__sys_client_notification_level__info": "Info", "@sage/xtrem-system/enums__sys_client_notification_level__success": "Sucesso", "@sage/xtrem-system/enums__sys_client_notification_level__warning": "Aviso", "@sage/xtrem-system/enums__user_import_export_date_format__europeanDash": "dd-mm-yyyy", "@sage/xtrem-system/enums__user_import_export_date_format__europeanSlash": "dd/mm/yyyy", "@sage/xtrem-system/enums__user_import_export_date_format__isoDash": "yyyy-mm-dd", "@sage/xtrem-system/enums__user_import_export_date_format__isoSlash": "yyyy/mm/dd", "@sage/xtrem-system/enums__user_import_export_date_format__usDash": "mm-dd-yyyy", "@sage/xtrem-system/enums__user_import_export_date_format__usSlash": "mm/dd/yyyy", "@sage/xtrem-system/enums__user_type__application": "Aplicação", "@sage/xtrem-system/enums__user_type__system": "Sistema", "@sage/xtrem-system/error-packages-cannot-be": "As embalagens não podem ser desativadas.", "@sage/xtrem-system/error-packages-cannot-be-deactivated": "As embalagens não podem ser desativadas.", "@sage/xtrem-system/failed-to-save-view": "Ocorreu um erro ao guardar a vista.", "@sage/xtrem-system/invalid-admin-demo-persona": "Não é possível criar uma persona de demonstração de administrador que não seja a predefinida.", "@sage/xtrem-system/invalid-api-user-email": "O endereço de e-mail do utilizador da API {{value}} está incorrecto. O endereço de e-mail do utilizador da API {{value}} está incorrecto", "@sage/xtrem-system/invalid-api-user-is-admin": "Um utilizador de API não pode ser um administrador.", "@sage/xtrem-system/invalid-api-user-is-demo-persona": "Um utilizador de API não pode ser uma \"persona\" de demonstração.", "@sage/xtrem-system/invalid-email": "Endereço de e-mail incorreto: ({{value}})", "@sage/xtrem-system/invalid-operator-pin-code": "O código PIN de um operador tem de ter, no mínimo, 4 caracteres.", "@sage/xtrem-system/invalid-operator-pincode-not-unique": "O código PIN do operador deve ser único.", "@sage/xtrem-system/invalid-operator-user-email": "Endereço de e-mail do utilizador do operador inválido. Esperado: operator-<name>@localhost.domain", "@sage/xtrem-system/invalid-operator-user-is-admin": "Um utilizador operador não pode ser um administrador", "@sage/xtrem-system/invalid-operator-user-is-demo-persona": "Um utilizador operador não pode ser uma persona de demonstração", "@sage/xtrem-system/is-active/node-id-combination-not-active": "Inativo {{nodeName}} {{dataId}}", "@sage/xtrem-system/language-master-locale-different-from-default": "A localidade master deve corresponder à localidade padrão para o mesmo idioma.", "@sage/xtrem-system/menu_item__administration": "Administração", "@sage/xtrem-system/menu_item__application-data": "Configuração do aplicativo", "@sage/xtrem-system/menu_item__bundle-activation": "Ativação do pacote (bundle)", "@sage/xtrem-system/menu_item__integrations": "", "@sage/xtrem-system/menu_item__service-option": "Opções serviços", "@sage/xtrem-system/menu_item__user": "Usuários", "@sage/xtrem-system/menu_item__user-data": "Usuários e segurança", "@sage/xtrem-system/nodes__company__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__company__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__company__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__company__node_name": "Sociedade", "@sage/xtrem-system/nodes__company__property__id": "ID", "@sage/xtrem-system/nodes__company__property__isActive": "Ativo", "@sage/xtrem-system/nodes__company__property__sites": "Estabelecimentos", "@sage/xtrem-system/nodes__locale__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__locale__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__locale__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__locale__node_name": "Parametros regionais (Locale)", "@sage/xtrem-system/nodes__locale__property__id": "ID", "@sage/xtrem-system/nodes__locale__property__isDefaultLocale": "Locale predefinido", "@sage/xtrem-system/nodes__locale__property__isLanguageMasterLocale": "Idioma do locale mestre", "@sage/xtrem-system/nodes__locale__property__language": "Idioma", "@sage/xtrem-system/nodes__site__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__site__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__site__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__site__node_name": "Estabelecimento", "@sage/xtrem-system/nodes__site__property__description": "Descrição", "@sage/xtrem-system/nodes__site__property__id": "ID", "@sage/xtrem-system/nodes__site__property__isActive": "Ativo", "@sage/xtrem-system/nodes__site__property__legalCompany": "Sociedade jurídica", "@sage/xtrem-system/nodes__site__property__linkedSites": "Sites ligados", "@sage/xtrem-system/nodes__site__property__name": "Nome", "@sage/xtrem-system/nodes__sys_changelog__asyncMutation__asyncExport": "Exportação", "@sage/xtrem-system/nodes__sys_changelog__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__sys_changelog__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_changelog__node_name": "Registo de alterações do sistema (changelog)", "@sage/xtrem-system/nodes__sys_changelog__property__changeDate": "Data de modificação", "@sage/xtrem-system/nodes__sys_changelog__property__hash": "Hash", "@sage/xtrem-system/nodes__sys_changelog__property__message": "Mensagem", "@sage/xtrem-system/nodes__sys_client_notification__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__sys_client_notification__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__sys_client_notification__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_client_notification__node_name": "Notificação do cliente do sistema", "@sage/xtrem-system/nodes__sys_client_notification__property__actions": "Ações", "@sage/xtrem-system/nodes__sys_client_notification__property__description": "Descrição", "@sage/xtrem-system/nodes__sys_client_notification__property__icon": "Ícone", "@sage/xtrem-system/nodes__sys_client_notification__property__isRead": "Foi lido", "@sage/xtrem-system/nodes__sys_client_notification__property__level": "Nível", "@sage/xtrem-system/nodes__sys_client_notification__property__recipient": "Recipiente", "@sage/xtrem-system/nodes__sys_client_notification__property__shouldDisplayToast": "<PERSON><PERSON> apresentar \"toast\"", "@sage/xtrem-system/nodes__sys_client_notification__property__title": "Titulo", "@sage/xtrem-system/nodes__sys_client_notification_action__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__sys_client_notification_action__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__sys_client_notification_action__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_client_notification_action__node_name": "Ação de notificação do cliente do sistema", "@sage/xtrem-system/nodes__sys_client_notification_action__property__clientNotifications": "Notificações do cliente", "@sage/xtrem-system/nodes__sys_client_notification_action__property__link": "Link", "@sage/xtrem-system/nodes__sys_client_notification_action__property__style": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__sys_client_notification_action__property__title": "Titulo", "@sage/xtrem-system/nodes__sys_client_user_settings__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__sys_client_user_settings__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__sys_client_user_settings__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_client_user_settings__node_name": "Definições do utilizador cliente do sistema", "@sage/xtrem-system/nodes__sys_client_user_settings__property__content": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__sys_client_user_settings__property__description": "Descrição", "@sage/xtrem-system/nodes__sys_client_user_settings__property__elementId": "ID do elemento", "@sage/xtrem-system/nodes__sys_client_user_settings__property__isSelected": "Está selecionado(a)", "@sage/xtrem-system/nodes__sys_client_user_settings__property__screenId": "ID da ecrã", "@sage/xtrem-system/nodes__sys_client_user_settings__property__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__sys_client_user_settings__property__user": "Utilizador", "@sage/xtrem-system/nodes__sys_csv_checksum__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__sys_csv_checksum__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__sys_csv_checksum__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_csv_checksum__node_name": "Sistema CSV checksum", "@sage/xtrem-system/nodes__sys_csv_checksum__property__checksum": "Checksum", "@sage/xtrem-system/nodes__sys_csv_checksum__property__factoryName": "Nome de fábrica", "@sage/xtrem-system/nodes__sys_csv_checksum__property__tenantId": "ID do tenant", "@sage/xtrem-system/nodes__sys_custom_record__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__sys_custom_record__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__sys_custom_record__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_custom_record__node_name": "Registo personalizado de sistema", "@sage/xtrem-system/nodes__sys_custom_record__property__bundleId": "ID do pacote", "@sage/xtrem-system/nodes__sys_custom_record__property__factoryName": "Nome de fábrica", "@sage/xtrem-system/nodes__sys_custom_sql_history__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__sys_custom_sql_history__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__sys_custom_sql_history__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_custom_sql_history__node_name": "Histórico de SQL personalizado do sistema", "@sage/xtrem-system/nodes__sys_custom_sql_history__property__dryRun": "Dry run", "@sage/xtrem-system/nodes__sys_custom_sql_history__property__endDateTime": "Data e hora de fim", "@sage/xtrem-system/nodes__sys_custom_sql_history__property__result": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__sys_custom_sql_history__property__scriptContent": "Conteúdo <PERSON>uião (Script)", "@sage/xtrem-system/nodes__sys_custom_sql_history__property__scriptPath": "<PERSON><PERSON><PERSON> do script", "@sage/xtrem-system/nodes__sys_custom_sql_history__property__startDateTime": "Data e hora de início", "@sage/xtrem-system/nodes__sys_customer__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__sys_customer__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__sys_customer__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_customer__node_name": "Sistema do cliente", "@sage/xtrem-system/nodes__sys_customer__property__customerId": "ID do cliente", "@sage/xtrem-system/nodes__sys_customer__property__name": "Nome", "@sage/xtrem-system/nodes__sys_data_validation_report__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__sys_data_validation_report__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__sys_data_validation_report__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_data_validation_report__node_name": "Relatório de validação de dados do sistema", "@sage/xtrem-system/nodes__sys_data_validation_report__property__lines": "<PERSON><PERSON>", "@sage/xtrem-system/nodes__sys_data_validation_report_line__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__sys_data_validation_report_line__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__sys_data_validation_report_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_data_validation_report_line__node_name": "Linha de relatório de validação de dados do sistema", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__extraInfo": "<PERSON><PERSON>s adicionais", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__message": "Mensagem", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__nodeId": "ID do Nó (node)", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__nodeName": "Nome do nó (node)", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__path": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__report": "Relat<PERSON><PERSON>", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__severity": "Severidade", "@sage/xtrem-system/nodes__sys_device_token__asyncMutation__asyncExport": "Exportação", "@sage/xtrem-system/nodes__sys_device_token__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__sys_device_token__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_device_token__mutation__createDeviceToken": "Criar token de dispositivo", "@sage/xtrem-system/nodes__sys_device_token__mutation__createDeviceToken__failed": "Falha ao criar o token do dispositivo.", "@sage/xtrem-system/nodes__sys_device_token__mutation__deleteDeviceToken": "Eliminar o token de dispositivo", "@sage/xtrem-system/nodes__sys_device_token__mutation__deleteDeviceToken__failed": "Falha ao eliminar o token do dispositivo.", "@sage/xtrem-system/nodes__sys_device_token__mutation__deleteDeviceToken__parameter__tokenId": "Token ID", "@sage/xtrem-system/nodes__sys_device_token__node_name": "Token de dispositivo de sistema", "@sage/xtrem-system/nodes__sys_device_token__property__expiration": "Expiração", "@sage/xtrem-system/nodes__sys_device_token__property__loginTestUrl": "URL login de teste", "@sage/xtrem-system/nodes__sys_device_token__property__loginUrl": "URL de login", "@sage/xtrem-system/nodes__sys_device_token__property__name": "Nome", "@sage/xtrem-system/nodes__sys_device_token__property__tokenId": "Token ID", "@sage/xtrem-system/nodes__sys_note__asyncMutation__asyncExport": "Exportação", "@sage/xtrem-system/nodes__sys_note__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__sys_note__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_note__node_name": "<PERSON>a sistema", "@sage/xtrem-system/nodes__sys_note__property__content": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__sys_note__property__title": "Titulo", "@sage/xtrem-system/nodes__sys_note_association__node_name": "Associação de notas de sistema", "@sage/xtrem-system/nodes__sys_note_association__property__note": "<PERSON>a", "@sage/xtrem-system/nodes__sys_note_association__property__sourceNodeId": "ID do nó de origem", "@sage/xtrem-system/nodes__sys_note_association__property__sourceNodeName": "Nome do nó de origem", "@sage/xtrem-system/nodes__sys_pack_allocation__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__sys_pack_allocation__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__sys_pack_allocation__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_pack_allocation__mutation__activate": "Ativar", "@sage/xtrem-system/nodes__sys_pack_allocation__mutation__activate__failed": "A ativação falhou.", "@sage/xtrem-system/nodes__sys_pack_allocation__mutation__activate__parameter__packageId": "ID do pacote", "@sage/xtrem-system/nodes__sys_pack_allocation__mutation__deactivate": "Desativado", "@sage/xtrem-system/nodes__sys_pack_allocation__mutation__deactivate__failed": "A desativação falhou", "@sage/xtrem-system/nodes__sys_pack_allocation__mutation__deactivate__parameter__packageId": "ID do pacote", "@sage/xtrem-system/nodes__sys_pack_allocation__node_name": "Atribuição de pacotes de sistema", "@sage/xtrem-system/nodes__sys_pack_allocation__property__isActivable": "Activável", "@sage/xtrem-system/nodes__sys_pack_allocation__property__isActive": "Ativo", "@sage/xtrem-system/nodes__sys_pack_allocation__property__package": "Embalagem", "@sage/xtrem-system/nodes__sys_pack_allocation__property__status": "Status", "@sage/xtrem-system/nodes__sys_pack_version__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__sys_pack_version__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__sys_pack_version__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_pack_version__node_name": "Versão do pacote do sistema", "@sage/xtrem-system/nodes__sys_pack_version__property__isBundle": "<PERSON><PERSON>", "@sage/xtrem-system/nodes__sys_pack_version__property__isHidden": "<PERSON>scondi<PERSON>", "@sage/xtrem-system/nodes__sys_pack_version__property__isReleased": "Lançado", "@sage/xtrem-system/nodes__sys_pack_version__property__isUpgradeBundle": "Pacote de actualização", "@sage/xtrem-system/nodes__sys_pack_version__property__name": "Nome", "@sage/xtrem-system/nodes__sys_pack_version__property__sqlSchemaVersion": "Versão do esquema SQL", "@sage/xtrem-system/nodes__sys_pack_version__property__version": "Vers<PERSON>", "@sage/xtrem-system/nodes__sys_patch_history__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__sys_patch_history__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__sys_patch_history__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_patch_history__node_name": "Histórico de patches do sistema", "@sage/xtrem-system/nodes__sys_patch_history__property__packageName": "Nome do pacote", "@sage/xtrem-system/nodes__sys_patch_history__property__patchName": "Nome do patch", "@sage/xtrem-system/nodes__sys_patch_history__property__result": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__sys_patch_history__property__version": "Vers<PERSON>", "@sage/xtrem-system/nodes__sys_service_option__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__sys_service_option__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__sys_service_option__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_service_option__node_name": "Opção de serviço de sistema", "@sage/xtrem-system/nodes__sys_service_option__property__childServiceOptions": "Opções de serviços dependentes", "@sage/xtrem-system/nodes__sys_service_option__property__description": "Descrição", "@sage/xtrem-system/nodes__sys_service_option__property__isActiveByDefault": "Activo por padrão", "@sage/xtrem-system/nodes__sys_service_option__property__isHidden": "<PERSON>scondi<PERSON>", "@sage/xtrem-system/nodes__sys_service_option__property__optionName": "Nome da opção", "@sage/xtrem-system/nodes__sys_service_option__property__package": "Embalagem", "@sage/xtrem-system/nodes__sys_service_option__property__parentServiceOptions": "Opções de serviço principal", "@sage/xtrem-system/nodes__sys_service_option__property__status": "Status", "@sage/xtrem-system/nodes__sys_service_option_state__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__sys_service_option_state__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__sys_service_option_state__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_service_option_state__node_name": "Status da opção de serviço do sistema", "@sage/xtrem-system/nodes__sys_service_option_state__property__imageStatus": "Status da imagem", "@sage/xtrem-system/nodes__sys_service_option_state__property__isActivable": "Activável", "@sage/xtrem-system/nodes__sys_service_option_state__property__isActive": "Ativo", "@sage/xtrem-system/nodes__sys_service_option_state__property__isAvailable": "Disponível", "@sage/xtrem-system/nodes__sys_service_option_state__property__isHiddenByConfig": "Escondido por padrão", "@sage/xtrem-system/nodes__sys_service_option_state__property__isPackageActive": "Pacote activo", "@sage/xtrem-system/nodes__sys_service_option_state__property__isReadyToUse": "Pronto a usar", "@sage/xtrem-system/nodes__sys_service_option_state__property__serviceOption": "Opção de serviço", "@sage/xtrem-system/nodes__sys_service_option_to_service_option__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__sys_service_option_to_service_option__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__sys_service_option_to_service_option__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_service_option_to_service_option__node_name": "Opção de serviço de sistema para opção de serviço", "@sage/xtrem-system/nodes__sys_service_option_to_service_option__property__childServiceOption": "Opção de serviço dependente", "@sage/xtrem-system/nodes__sys_service_option_to_service_option__property__parentServiceOption": "Opção de serviço principal", "@sage/xtrem-system/nodes__sys_tag__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__sys_tag__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__sys_tag__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_tag__node_name": "Etiqueta do sistema (Sys tag)", "@sage/xtrem-system/nodes__sys_tag__property__color": "Color", "@sage/xtrem-system/nodes__sys_tag__property__description": "Descrição", "@sage/xtrem-system/nodes__sys_tag__property__name": "Nome", "@sage/xtrem-system/nodes__sys_tag__property__rgbColor": "Cor RGB", "@sage/xtrem-system/nodes__sys_tenant__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__sys_tenant__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__sys_tenant__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_tenant__mutation__resetTenantDocuments": "Redefinir documentos do \"tenant\"", "@sage/xtrem-system/nodes__sys_tenant__mutation__resetTenantDocuments__failed": "A reposição dos documentos do tenant falhou.", "@sage/xtrem-system/nodes__sys_tenant__mutation__resetTenantDocuments__parameter__tenantId": "ID do tenant", "@sage/xtrem-system/nodes__sys_tenant__node_name": "<PERSON>ant de siste<PERSON>", "@sage/xtrem-system/nodes__sys_tenant__property__customer": "Cliente", "@sage/xtrem-system/nodes__sys_tenant__property__directoryName": "Nome do diretório", "@sage/xtrem-system/nodes__sys_tenant__property__name": "Nome", "@sage/xtrem-system/nodes__sys_tenant__property__tenantId": "ID do tenant", "@sage/xtrem-system/nodes__sys_tenant__query__getTenantInformation": "Obter informações sobre o Tennant", "@sage/xtrem-system/nodes__sys_tenant__query__getTenantInformation__failed": "Falha na obtenção de informações do tenant.", "@sage/xtrem-system/nodes__sys_tenant__tenant_directory_name_should_be_in_kebab_case": "Escreva o nome do diretório do tenant em maiúsculas e minúsculas.", "@sage/xtrem-system/nodes__sys_tenant__tenant_name_characters_not_authorized": "O nome do tenant contém caracteres não autorizados.", "@sage/xtrem-system/nodes__sys_upgrade__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__sys_upgrade__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__sys_upgrade__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_upgrade__node_name": "Actualização do sistema", "@sage/xtrem-system/nodes__sys_upgrade__property__bundleId": "ID do pacote", "@sage/xtrem-system/nodes__sys_upgrade__property__managedItems": "Artigos administrados", "@sage/xtrem-system/nodes__sys_vendor__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__sys_vendor__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__sys_vendor__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_vendor__node_name": "Fornecedor do sistema", "@sage/xtrem-system/nodes__sys_vendor__property__description": "Descrição", "@sage/xtrem-system/nodes__sys_vendor__property__name": "Nome", "@sage/xtrem-system/nodes__sys-client-user-settings": "Só é possível selecionar um.", "@sage/xtrem-system/nodes__sys-layer__name-must-be-kebab-case": "O nome deve ser escrito em kebab-case.", "@sage/xtrem-system/nodes__user__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__user__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__user__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__user__cannot_modify_own_rights": "Não está autorizado a modificar os seus próprios direitos de administrador.", "@sage/xtrem-system/nodes__user__mutation__logPageVisit": "Registar a visita à página", "@sage/xtrem-system/nodes__user__mutation__logPageVisit__failed": "Falha na visita à página de log.", "@sage/xtrem-system/nodes__user__mutation__logPageVisit__parameter__path": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__user__mutation__sendWelcomeMail": "Enviar e-mail de boas-vindas", "@sage/xtrem-system/nodes__user__mutation__sendWelcomeMail__failed": "Falha no envio do e-mail de boas-vindas.", "@sage/xtrem-system/nodes__user__mutation__sendWelcomeMail__parameter__isAdmin": "Admin", "@sage/xtrem-system/nodes__user__mutation__sendWelcomeMail__parameter__users": "Utilizadores", "@sage/xtrem-system/nodes__user__mutation__setDemoPersona": "Definir a persona de demonstração", "@sage/xtrem-system/nodes__user__mutation__setDemoPersona__failed": "A definição da persona de demonstração falhou.", "@sage/xtrem-system/nodes__user__mutation__setDemoPersona__parameter__email": "E-mail", "@sage/xtrem-system/nodes__user__mutation__updateClientSettings": "Atualizar as definições do cliente", "@sage/xtrem-system/nodes__user__mutation__updateClientSettings__failed": "Falha na atualização das definições do cliente.", "@sage/xtrem-system/nodes__user__mutation__updateClientSettings__parameter__clientSettings": "Definições do cliente", "@sage/xtrem-system/nodes__user__node_name": "Utilizador", "@sage/xtrem-system/nodes__user__only_admin_can_add_admin": "Apenas um utilizador administrador pode adicionar ou modificar os direitos de administrador de outro utilizador.", "@sage/xtrem-system/nodes__user__property__clientSettings": "Definições do cliente", "@sage/xtrem-system/nodes__user__property__displayName": "Nome de exibição", "@sage/xtrem-system/nodes__user__property__email": "E-mail", "@sage/xtrem-system/nodes__user__property__firstName": "Primeiro nome", "@sage/xtrem-system/nodes__user__property__isActive": "Ativo", "@sage/xtrem-system/nodes__user__property__isAdministrator": "Administrador", "@sage/xtrem-system/nodes__user__property__isApiUser": "Utilizador de API", "@sage/xtrem-system/nodes__user__property__isDemoPersona": "Persona de demonstração", "@sage/xtrem-system/nodes__user__property__isFirstAdminUser": "Primeiro utiliz. admin", "@sage/xtrem-system/nodes__user__property__isOperatorUser": "É um utilizador operador", "@sage/xtrem-system/nodes__user__property__lastName": "Sobrenome", "@sage/xtrem-system/nodes__user__property__navigation": "Navegação", "@sage/xtrem-system/nodes__user__property__operatorCode": "Código de operador", "@sage/xtrem-system/nodes__user__property__operatorId": "ID do operador", "@sage/xtrem-system/nodes__user__property__photo": "Foto", "@sage/xtrem-system/nodes__user__property__preferences": "preferencias", "@sage/xtrem-system/nodes__user__property__setupId": "ID configuração", "@sage/xtrem-system/nodes__user__property__userType": "Tipo de utilizador", "@sage/xtrem-system/nodes__user__query__demoPersonas": "Personas de demonstração", "@sage/xtrem-system/nodes__user__query__demoPersonas__failed": "As personas de demonstração falharam.", "@sage/xtrem-system/nodes__user_navigation__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__user_navigation__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__user_navigation__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__user_navigation__node_name": "Navegação do utilizador", "@sage/xtrem-system/nodes__user_navigation__property__bookmarks": "Marcadores", "@sage/xtrem-system/nodes__user_navigation__property__history": "Hist<PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__user_navigation__property__user": "Utilizador", "@sage/xtrem-system/nodes__user_preferences__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-system/nodes__user_preferences__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-system/nodes__user_preferences__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__user_preferences__node_name": "Preferências de utilizador", "@sage/xtrem-system/nodes__user_preferences__property__importExportDateFormat": "Importação/exportação formato de data", "@sage/xtrem-system/nodes__user_preferences__property__importExportDelimiter": "Delimitador de importação/exportação", "@sage/xtrem-system/nodes__user_preferences__property__isExternal": "É externo", "@sage/xtrem-system/nodes__user_preferences__property__isWelcomeMailSent": "Correio de boas-vindas enviado", "@sage/xtrem-system/nodes__user_preferences__property__user": "Utilizador", "@sage/xtrem-system/nodes__user_preferences__query__activeServiceOptions": "Opções do Active Service", "@sage/xtrem-system/nodes__user_preferences__query__activeServiceOptions__failed": "As opções de serviço ativas falharam.", "@sage/xtrem-system/package__name": "Sistema", "@sage/xtrem-system/package-activated": "Embalagem ativada", "@sage/xtrem-system/package-deactivated": "Embalagem desativada", "@sage/xtrem-system/pages__client_user_settings_edit____title": "Criar uma vista", "@sage/xtrem-system/pages__client_user_settings_edit__generalSection____title": "G<PERSON>", "@sage/xtrem-system/pages__client_user_settings_edit__save____title": "Guardar", "@sage/xtrem-system/pages__client_user_settings_edit__title____title": "Nome", "@sage/xtrem-system/pages__client_user_settings_list____title": "Gerir vistas.", "@sage/xtrem-system/pages__client_user_settings_list__clientSettings____columns__title__title": "Nome", "@sage/xtrem-system/pages__client_user_settings_list__clientSettings____dropdownActions__title": "Eliminar", "@sage/xtrem-system/pages__client_user_settings_list__clientSettings____dropdownActions__title__remove": "Eliminar", "@sage/xtrem-system/pages__client_user_settings_list__generalSection____title": "G<PERSON>", "@sage/xtrem-system/pages__client_user_settings_list__save____title": "Guardar", "@sage/xtrem-system/pages__delete_page_dialog_content": "Está prestes a eliminar este registro.", "@sage/xtrem-system/pages__delete_page_dialog_title": "Confirmar elimina<PERSON>", "@sage/xtrem-system/pages__pack_allocation____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-system/pages__pack_allocation____navigationPanel__optionsMenu__title__2": "Embalagens", "@sage/xtrem-system/pages__pack_allocation____navigationPanel__optionsMenu__title__3": "Pacotes (bundles)", "@sage/xtrem-system/pages__pack_allocation____title": "Ativação da embalagem", "@sage/xtrem-system/pages__pack_allocation__closePage____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__pack_allocation__generalSection____title": "G<PERSON>", "@sage/xtrem-system/pages__pack_allocation__isActivable____title": "Disponível", "@sage/xtrem-system/pages__pack_allocation__isActive____title": "Ativo", "@sage/xtrem-system/pages__pack_allocation__package____title": "Embalagem", "@sage/xtrem-system/pages__pack_allocation__version____title": "Vers<PERSON>", "@sage/xtrem-system/pages__pack_version____navigationPanel__optionsMenu__title": "Pacotes (bundles)", "@sage/xtrem-system/pages__pack_version____navigationPanel__optionsMenu__title__2": "Embalagens", "@sage/xtrem-system/pages__pack_version____navigationPanel__optionsMenu__title__3": "Todos", "@sage/xtrem-system/pages__pack_version____title": "Ativação do pacote (bundle)", "@sage/xtrem-system/pages__pack_version__closePage____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__pack_version__generalSection____title": "G<PERSON>", "@sage/xtrem-system/pages__pack_version__isBundle____title": "Agrupar", "@sage/xtrem-system/pages__pack_version__package____title": "Embalagem", "@sage/xtrem-system/pages__pack_version__version____title": "Vers<PERSON>", "@sage/xtrem-system/pages__service_option_state____navigationPanel__dropdownActions__title": "Ativar", "@sage/xtrem-system/pages__service_option_state____navigationPanel__dropdownActions__title__2": "Desativado", "@sage/xtrem-system/pages__service_option_state____navigationPanel__listItem__line_4__title": "Status", "@sage/xtrem-system/pages__service_option_state____navigationPanel__listItem__line2__title": "Descrição", "@sage/xtrem-system/pages__service_option_state____navigationPanel__listItem__line3__title": "Ativo", "@sage/xtrem-system/pages__service_option_state____navigationPanel__listItem__line4__title": "Status", "@sage/xtrem-system/pages__service_option_state____navigationPanel__listItem__title__title": "Nome", "@sage/xtrem-system/pages__service_option_state____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-system/pages__service_option_state____objectTypePlural": "Opções de serviços", "@sage/xtrem-system/pages__service_option_state____objectTypeSingular": "Opção de serviço", "@sage/xtrem-system/pages__service_option_state____title": "Opções de serviços", "@sage/xtrem-system/pages__service_option_state__cancel____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__service_option_state__closePage____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__service_option_state__description____title": "Descrição", "@sage/xtrem-system/pages__service_option_state__generalSection____title": "G<PERSON>", "@sage/xtrem-system/pages__service_option_state__isActivable____title": "Disponível", "@sage/xtrem-system/pages__service_option_state__isActive____title": "Ativo", "@sage/xtrem-system/pages__service_option_state__isAvailable____title": "Disponível", "@sage/xtrem-system/pages__service_option_state__isHiddenByConfig____title": "Ativo", "@sage/xtrem-system/pages__service_option_state__optionName____title": "Nome", "@sage/xtrem-system/pages__service_option_state__reset_tenant_data_done": "Reinicialização dos dados do \"Tenant\" executado", "@sage/xtrem-system/pages__service_option_state__save____title": "Guardar", "@sage/xtrem-system/pages__service_option_state__serviceOption____title": "Opção de serviço", "@sage/xtrem-system/pages__service_option_state__status____title": "Status", "@sage/xtrem-system/pages__sys_changelog____navigationPanel__listItem__line2__title": "Mensagem", "@sage/xtrem-system/pages__sys_changelog____navigationPanel__listItem__line3__title": "Alterar o carimbo de data/hora (timestamp)", "@sage/xtrem-system/pages__sys_changelog____navigationPanel__listItem__title__title": "Hash", "@sage/xtrem-system/pages__sys_changelog____title": "Registo de alterações (Changelog)", "@sage/xtrem-system/pages__sys_device_token____navigationPanel__listItem__tokenId__title": "ID", "@sage/xtrem-system/pages__sys_device_token____navigationPanel__optionsMenu__title": "<PERSON><PERSON>", "@sage/xtrem-system/pages__sys_device_token____title": "Token de dispositivo", "@sage/xtrem-system/pages__sys_device_token__createDeviceToken____title": "Guardar", "@sage/xtrem-system/pages__sys_device_token__deleteDeviceToken____title": "Eliminar", "@sage/xtrem-system/pages__sys_device_token__deviceTokenBlock____title": "Informações sobre o token do dispositivo", "@sage/xtrem-system/pages__sys_device_token__expiration____title": "validade", "@sage/xtrem-system/pages__sys_device_token__loginTestUrl____title": "Link de teste do dispositivo", "@sage/xtrem-system/pages__sys_device_token__loginUrl____title": "Link de login do dispositivo", "@sage/xtrem-system/pages__sys_device_token__name____title": "Nome", "@sage/xtrem-system/pages__sys_device_token__tokenId____title": "Tokens", "@sage/xtrem-system/pages__sys_layer____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-system/pages__sys_layer____title": "Data layer", "@sage/xtrem-system/pages__sys_layer___id____title": "ID", "@sage/xtrem-system/pages__sys_layer__dataLayerInformation____title": "Informação da camada de dados", "@sage/xtrem-system/pages__sys_layer__deleteLayerAction____title": "Eliminar", "@sage/xtrem-system/pages__sys_layer__description____title": "Descrição", "@sage/xtrem-system/pages__sys_layer__exportLayer____title": "Camada de exportação", "@sage/xtrem-system/pages__sys_layer__generalSection____title": "G<PERSON>", "@sage/xtrem-system/pages__sys_layer__name____title": "Nome da camada de dados", "@sage/xtrem-system/pages__sys_pack_allocation____navigationPanel__listItem__lineRight__title": "Vers<PERSON>", "@sage/xtrem-system/pages__sys_pack_allocation____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-system/pages__sys_pack_allocation____navigationPanel__optionsMenu__title__2": "Embalagens", "@sage/xtrem-system/pages__sys_pack_allocation____title": "Ativação da embalagem", "@sage/xtrem-system/pages__sys_pack_allocation__closePage____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__sys_pack_allocation__generalSection____title": "G<PERSON>", "@sage/xtrem-system/pages__sys_pack_allocation__isActivable____title": "Disponível", "@sage/xtrem-system/pages__sys_pack_allocation__isActive____title": "Ativo", "@sage/xtrem-system/pages__sys_pack_allocation__package____title": "Embalagem", "@sage/xtrem-system/pages__sys_pack_allocation__version____title": "Vers<PERSON>", "@sage/xtrem-system/pages__sys_tag____navigationPanel__listItem__line2__title": "Descrição", "@sage/xtrem-system/pages__sys_tag____navigationPanel__listItem__rgbColor__title": "Cor RGB", "@sage/xtrem-system/pages__sys_tag____navigationPanel__listItem__title__title": "Nome", "@sage/xtrem-system/pages__sys_tag____title": "Etiqueta", "@sage/xtrem-system/pages__sys_tag__description____title": "Descrição", "@sage/xtrem-system/pages__sys_tag__generalSection____title": "G<PERSON>", "@sage/xtrem-system/pages__sys_tag__name____title": "Nome", "@sage/xtrem-system/pages__sys_tag__rgbColor____title": "Cor RGB", "@sage/xtrem-system/pages__tenant_information____title": "Informação do Tenant", "@sage/xtrem-system/pages__tenant_information__cancel_reset_tenant_data_button_text": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__tenant_information__confirm_reset_tenant_data_button_text": "Confirmar a reposição dos dados do tenant", "@sage/xtrem-system/pages__tenant_information__generalSection____title": "G<PERSON>", "@sage/xtrem-system/pages__tenant_information__reset_tenant_data_button_text": "Redefinir os dados do tenant", "@sage/xtrem-system/pages__tenant_information__resetDataDescription____content": "A reposição dos dados do \"tenant\" eliminará todos os movimentos (tais como encomendas de compra e venda, níveis de stock e movimentos) \nEsta operação não pode ser revertida. Deseja continuar?", "@sage/xtrem-system/pages__tenant_information__resetTenantId____helperText": "Por favor, digite o ID do tenant para apagar", "@sage/xtrem-system/pages__tenant_information__resetTenantId____title": "Confirmação Tenant ID", "@sage/xtrem-system/pages__tenant_information__resetTenantSection____title": "Reinicializar", "@sage/xtrem-system/pages__tenant_information__sumologic_button_text": "Visualizar logs Sumo Logic", "@sage/xtrem-system/pages__tenant_information__sumologicLink____helperText": "Tem de estar ligado à Sumologic antes de poder visualizar os registos.", "@sage/xtrem-system/pages__tenant_information__tenantId____title": "ID do tenant", "@sage/xtrem-system/pages__tenant_information__tenantVersion____title": "Versão do tenant", "@sage/xtrem-system/pages__user____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-system/pages__user____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__user___id____title": "ID", "@sage/xtrem-system/pages__user__email____title": "E-mail", "@sage/xtrem-system/pages__user__firstName____title": "Nome", "@sage/xtrem-system/pages__user__generalSection____title": "G<PERSON>", "@sage/xtrem-system/pages__user__idBlock____title": "ID", "@sage/xtrem-system/pages__user__isActive____title": "Ativo", "@sage/xtrem-system/pages__user__lastName____title": "Sobrenome", "@sage/xtrem-system/pages__user__userInformationBlock____title": "Informação usuário", "@sage/xtrem-system/pages__user__userPhotoBlock____title": "Foto", "@sage/xtrem-system/pages-confirm-cancel": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages-confirm-delete": "Eliminar", "@sage/xtrem-system/permission__create__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/permission__delete__name": "Eliminar", "@sage/xtrem-system/permission__get_tenant_information__name": "Obter informações sobre o Tennant", "@sage/xtrem-system/permission__read__name": "<PERSON>r", "@sage/xtrem-system/permission__reset_tenant_documents__name": "Redefinir documentos do \"tenant\"", "@sage/xtrem-system/permission__update__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/productKey": "Valor do artigo", "@sage/xtrem-system/productKey2": "Valor do artigo {{num}}", "@sage/xtrem-system/reserved-api-user-email": "E-mail ({{value}}) reservado para utilizadores de API.", "@sage/xtrem-system/reserved-operator-user-email": "O formato do endereço de e-mail do operador está reservado aos utilizadores operadores.", "@sage/xtrem-system/reset-tenant-data-not-admin": "Apenas os administradores podem repor os dados do \"Tenant\"", "@sage/xtrem-system/reset-tenant-data-not-admin-with-id": "O utilizador tentou repôr(reset)  o \"Tenant\" {{userId}}", "@sage/xtrem-system/reset-tenant-data-tenant-id-mismatched": "O tenant ID '{{tenantId}}'' não coincide com o tenant atual'{{currentId}}'", "@sage/xtrem-system/service_options__changelog__name": "Registo de alterações (Changelog)", "@sage/xtrem-system/service_options__dev_tools__name": "Ferramentas de desenvolvimento", "@sage/xtrem-system/service_options__is_demo_tenant__name": "Tenant de demonstração", "@sage/xtrem-system/service_options__sys_device_token__name": "Token de dispositivo de sistema", "@sage/xtrem-system/service_options__sys_tag__name": "Etiqueta do sistema (Sys tag)", "@sage/xtrem-system/statusIcon": "Status", "@sage/xtrem-system/sys_device_token_creation_error": "A criação do token de dispositivo falhou: {{errorMessage}}", "@sage/xtrem-system/sys_device_token_deletion_error": "A eliminação do token do dispositivo falhou: {{errorMessage}}", "@sage/xtrem-system/sys_device_token_device_token_already_created": "O dispositivo atual já tem um token criado.", "@sage/xtrem-system/system-user-forbidden": "Os utilizadores do sistema não podem ser criados ou atualizados.", "@sage/xtrem-system/update-confirmation": "Registro atualizado", "@sage/xtrem-system/widgets__system_version____title": "Versão do sistema"}