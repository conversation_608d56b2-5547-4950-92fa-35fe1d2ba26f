{"@sage/xtrem-system/activity__company__name": "Company", "@sage/xtrem-system/activity__service_option_state__name": "Service option state", "@sage/xtrem-system/activity__site__name": "Site", "@sage/xtrem-system/activity__sys_device_token__name": "System device token", "@sage/xtrem-system/activity__sys_note__name": "Sys note", "@sage/xtrem-system/activity__sys_tag__name": "Sys tag", "@sage/xtrem-system/activity__tenant__name": "Tenant", "@sage/xtrem-system/bundle-activated": "", "@sage/xtrem-system/bundle-deactivated": "", "@sage/xtrem-system/cannot-deactivate-admin-demo-persona": "<PERSON><PERSON> deactivate the Admin demo persona.", "@sage/xtrem-system/confirm-activate-service-option-message": "You are about to activate service option: {{serviceOptionName}}.", "@sage/xtrem-system/confirm-activate-service-option-title": "Activate service option", "@sage/xtrem-system/confirm-deactivate-service-option-message": "You are about to deactivate service option: {{serviceOptionName}}.", "@sage/xtrem-system/confirm-deactivate-service-option-title": "Deactivate service option", "@sage/xtrem-system/create-confirmation": "", "@sage/xtrem-system/data_types__big_id__name": "Big ID", "@sage/xtrem-system/data_types__big_url__name": "Big URL", "@sage/xtrem-system/data_types__bundle_id_data_type__name": "Bundle ID data type", "@sage/xtrem-system/data_types__bundle_path_type__name": "Bundle path type", "@sage/xtrem-system/data_types__bundle_version_data_type__name": "Bundle version data type", "@sage/xtrem-system/data_types__checksum_data_type__name": "Checksum data type", "@sage/xtrem-system/data_types__code__name": "Code", "@sage/xtrem-system/data_types__code_data_type__name": "Code data type", "@sage/xtrem-system/data_types__color__name": "Color", "@sage/xtrem-system/data_types__colored_element_enum__name": "Coloured element enum", "@sage/xtrem-system/data_types__company__name": "Company", "@sage/xtrem-system/data_types__content_length_decimal_data_type__name": "Content length decimal data type", "@sage/xtrem-system/data_types__decimal__name": "Decimal", "@sage/xtrem-system/data_types__default_decimal_data_type__name": "Default decimal data type", "@sage/xtrem-system/data_types__description__name": "Description", "@sage/xtrem-system/data_types__description_array_data_type__name": "Description array data type", "@sage/xtrem-system/data_types__description_data_type__name": "Description data type", "@sage/xtrem-system/data_types__email__name": "Email", "@sage/xtrem-system/data_types__folder__name": "Folder", "@sage/xtrem-system/data_types__http_base_url__name": "HTTP base URL", "@sage/xtrem-system/data_types__http_url__name": "HTTP URL", "@sage/xtrem-system/data_types__icon__name": "Icon", "@sage/xtrem-system/data_types__id__name": "ID", "@sage/xtrem-system/data_types__locale_datatype__name": "Locale datatype", "@sage/xtrem-system/data_types__locale_id_data_type__name": "Locale ID data type", "@sage/xtrem-system/data_types__localized_description__name": "Localised description", "@sage/xtrem-system/data_types__localized_name__name": "Localised name", "@sage/xtrem-system/data_types__localized_short_description__name": "Localised short description", "@sage/xtrem-system/data_types__localized_title__name": "Localised title", "@sage/xtrem-system/data_types__mime_type__name": "Mime type", "@sage/xtrem-system/data_types__name__name": "Name", "@sage/xtrem-system/data_types__nanoid__name": "Nanoid", "@sage/xtrem-system/data_types__note_content_data_type__name": "Note content data type", "@sage/xtrem-system/data_types__notification_data_data_type__name": "Notification data data type", "@sage/xtrem-system/data_types__passphrase__name": "Passphrase", "@sage/xtrem-system/data_types__password__name": "Password", "@sage/xtrem-system/data_types__quantity_data_type__name": "Quantity data type", "@sage/xtrem-system/data_types__rate__name": "Rate", "@sage/xtrem-system/data_types__rgb_color__name": "RGB color", "@sage/xtrem-system/data_types__service_option_status_enum__name": "Service option status enum", "@sage/xtrem-system/data_types__setup_id_data_type__name": "Setup ID data type", "@sage/xtrem-system/data_types__short_description__name": "Short description", "@sage/xtrem-system/data_types__single_character__name": "Single character", "@sage/xtrem-system/data_types__site__name": "Site", "@sage/xtrem-system/data_types__sys_client_notification_action_style_enum__name": "System client notification action style enum", "@sage/xtrem-system/data_types__sys_client_notification_level_enum__name": "System client notification level enum", "@sage/xtrem-system/data_types__title__name": "Title", "@sage/xtrem-system/data_types__url__name": "URL", "@sage/xtrem-system/data_types__user__name": "User", "@sage/xtrem-system/data_types__user_import_export_date_format_enum__name": "User import export date format enum", "@sage/xtrem-system/data_types__user_type_enum__name": "User type enum", "@sage/xtrem-system/data_types__uuid__name": "UUID", "@sage/xtrem-system/data_types__version__name": "Version", "@sage/xtrem-system/data-types/locale_datatype__invalid_locale": "Locale value is invalid: ({{locale}}).", "@sage/xtrem-system/data-types/rgb__invalid_rgb": "This value is not an RGB color code: \"{{value}}\".", "@sage/xtrem-system/data-types/url_datatype__invalid_protocol": "URL '{{url}}' got protocol {{protocol}}, expecting {{allowedProtocols}}.", "@sage/xtrem-system/data-types/url_datatype__invalid_url": "Incorrect URL '{{url}}'.", "@sage/xtrem-system/delete-confirmation": "Record deleted", "@sage/xtrem-system/delete-dialog-content": "You are about to delete this group.", "@sage/xtrem-system/delete-dialog-title": "", "@sage/xtrem-system/delete-user-forbidden": "", "@sage/xtrem-system/delete-view-warning-title": "Delete view", "@sage/xtrem-system/delete-view-warning-title-message": "You are about to delete this view.", "@sage/xtrem-system/duplicate_first_admin": "An admin user is already defined. You cannot create another one.", "@sage/xtrem-system/enums__colored_element__backgroundColor": "Background colour", "@sage/xtrem-system/enums__colored_element__borderColor": "Border colour", "@sage/xtrem-system/enums__colored_element__textColor": "Text colour", "@sage/xtrem-system/enums__service_option_status__experimental": "Experimental", "@sage/xtrem-system/enums__service_option_status__released": "Released", "@sage/xtrem-system/enums__service_option_status__workInProgress": "Work in progress", "@sage/xtrem-system/enums__service_option_type__experimental": "", "@sage/xtrem-system/enums__service_option_type__released": "", "@sage/xtrem-system/enums__service_option_type__workInProgress": "", "@sage/xtrem-system/enums__sys_client_notification_action_style__link": "Link", "@sage/xtrem-system/enums__sys_client_notification_action_style__primary": "Primary", "@sage/xtrem-system/enums__sys_client_notification_action_style__secondary": "Secondary", "@sage/xtrem-system/enums__sys_client_notification_action_style__tertiary": "Tertiary", "@sage/xtrem-system/enums__sys_client_notification_level__approval": "Approval", "@sage/xtrem-system/enums__sys_client_notification_level__error": "Error", "@sage/xtrem-system/enums__sys_client_notification_level__info": "Info", "@sage/xtrem-system/enums__sys_client_notification_level__success": "Success", "@sage/xtrem-system/enums__sys_client_notification_level__warning": "Warning", "@sage/xtrem-system/enums__user_import_export_date_format__europeanDash": "dd-mm-yyyy", "@sage/xtrem-system/enums__user_import_export_date_format__europeanSlash": "dd/mm/yyyy", "@sage/xtrem-system/enums__user_import_export_date_format__isoDash": "yyyy-mm-dd", "@sage/xtrem-system/enums__user_import_export_date_format__isoSlash": "yyyy/mm/dd", "@sage/xtrem-system/enums__user_import_export_date_format__usDash": "mm-dd-yyyy", "@sage/xtrem-system/enums__user_import_export_date_format__usSlash": "mm/dd/yyyy", "@sage/xtrem-system/enums__user_type__application": "Application", "@sage/xtrem-system/enums__user_type__system": "System", "@sage/xtrem-system/error-packages-cannot-be": "", "@sage/xtrem-system/error-packages-cannot-be-deactivated": "", "@sage/xtrem-system/failed-to-save-view": "An error occurred while saving the view.", "@sage/xtrem-system/invalid-admin-demo-persona": "Cannot create an admin demo persona other than the default.", "@sage/xtrem-system/invalid-api-user-email": "The API user email address {{value}} is incorrect. Use the format: api-<devid>@localhost.domain", "@sage/xtrem-system/invalid-api-user-is-admin": "An API user cannot be an admin user.", "@sage/xtrem-system/invalid-api-user-is-demo-persona": "An API user cannot be a demo persona.", "@sage/xtrem-system/invalid-email": "Incorrect email address: ({{value}})", "@sage/xtrem-system/invalid-operator-pin-code": "An operator PIN code needs to have a minimum of 4 characters.", "@sage/xtrem-system/invalid-operator-pincode-not-unique": "An operator PIN code needs to be unique.", "@sage/xtrem-system/invalid-operator-user-email": "Invalid operator user email address. Expected: operator-<name>@localhost.domain", "@sage/xtrem-system/invalid-operator-user-is-admin": "An operator user cannot be an admin", "@sage/xtrem-system/invalid-operator-user-is-demo-persona": "An operator user cannot be a demo persona", "@sage/xtrem-system/is-active/node-id-combination-not-active": "Inactive {{nodeName}} {{dataId}}", "@sage/xtrem-system/language-master-locale-different-from-default": "The master locale must match the default locale for the same language.", "@sage/xtrem-system/menu_item__administration": "Administration", "@sage/xtrem-system/menu_item__application-data": "Application configuration", "@sage/xtrem-system/menu_item__bundle-activation": "", "@sage/xtrem-system/menu_item__integrations": "", "@sage/xtrem-system/menu_item__service-option": "", "@sage/xtrem-system/menu_item__user": "", "@sage/xtrem-system/menu_item__user-data": "Users and security", "@sage/xtrem-system/nodes__company__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__company__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__company__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__company__node_name": "Company", "@sage/xtrem-system/nodes__company__property__id": "ID", "@sage/xtrem-system/nodes__company__property__isActive": "Active", "@sage/xtrem-system/nodes__company__property__sites": "Sites", "@sage/xtrem-system/nodes__locale__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__locale__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__locale__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__locale__node_name": "Locale", "@sage/xtrem-system/nodes__locale__property__id": "ID", "@sage/xtrem-system/nodes__locale__property__isDefaultLocale": "Default locale", "@sage/xtrem-system/nodes__locale__property__isLanguageMasterLocale": "Language of master locale", "@sage/xtrem-system/nodes__locale__property__language": "Language", "@sage/xtrem-system/nodes__site__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__site__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__site__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__site__node_name": "Site", "@sage/xtrem-system/nodes__site__property__description": "Description", "@sage/xtrem-system/nodes__site__property__id": "ID", "@sage/xtrem-system/nodes__site__property__isActive": "Active", "@sage/xtrem-system/nodes__site__property__legalCompany": "Legal company", "@sage/xtrem-system/nodes__site__property__linkedSites": "Linked sites", "@sage/xtrem-system/nodes__site__property__name": "Name", "@sage/xtrem-system/nodes__sys_changelog__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_changelog__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__sys_changelog__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_changelog__node_name": "System changelog", "@sage/xtrem-system/nodes__sys_changelog__property__changeDate": "Change date", "@sage/xtrem-system/nodes__sys_changelog__property__hash": "Hash", "@sage/xtrem-system/nodes__sys_changelog__property__message": "Message", "@sage/xtrem-system/nodes__sys_client_notification__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_client_notification__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__sys_client_notification__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_client_notification__node_name": "System client notification", "@sage/xtrem-system/nodes__sys_client_notification__property__actions": "Actions", "@sage/xtrem-system/nodes__sys_client_notification__property__description": "Description", "@sage/xtrem-system/nodes__sys_client_notification__property__icon": "Icon", "@sage/xtrem-system/nodes__sys_client_notification__property__isRead": "Is read", "@sage/xtrem-system/nodes__sys_client_notification__property__level": "Level", "@sage/xtrem-system/nodes__sys_client_notification__property__recipient": "Recipient", "@sage/xtrem-system/nodes__sys_client_notification__property__shouldDisplayToast": "Should display toast", "@sage/xtrem-system/nodes__sys_client_notification__property__title": "Title", "@sage/xtrem-system/nodes__sys_client_notification_action__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_client_notification_action__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__sys_client_notification_action__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_client_notification_action__node_name": "System client notification action", "@sage/xtrem-system/nodes__sys_client_notification_action__property__clientNotifications": "Client notifications", "@sage/xtrem-system/nodes__sys_client_notification_action__property__link": "Link", "@sage/xtrem-system/nodes__sys_client_notification_action__property__style": "Style", "@sage/xtrem-system/nodes__sys_client_notification_action__property__title": "Title", "@sage/xtrem-system/nodes__sys_client_user_settings__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_client_user_settings__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__sys_client_user_settings__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_client_user_settings__node_name": "System client user settings", "@sage/xtrem-system/nodes__sys_client_user_settings__property__content": "Content", "@sage/xtrem-system/nodes__sys_client_user_settings__property__description": "Description", "@sage/xtrem-system/nodes__sys_client_user_settings__property__elementId": "Element ID", "@sage/xtrem-system/nodes__sys_client_user_settings__property__isSelected": "Is selected", "@sage/xtrem-system/nodes__sys_client_user_settings__property__screenId": "Screen ID", "@sage/xtrem-system/nodes__sys_client_user_settings__property__title": "Title", "@sage/xtrem-system/nodes__sys_client_user_settings__property__user": "User", "@sage/xtrem-system/nodes__sys_csv_checksum__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_csv_checksum__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__sys_csv_checksum__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_csv_checksum__node_name": "System CSV checksum", "@sage/xtrem-system/nodes__sys_csv_checksum__property__checksum": "Checksum", "@sage/xtrem-system/nodes__sys_csv_checksum__property__factoryName": "Factory name", "@sage/xtrem-system/nodes__sys_csv_checksum__property__tenantId": "Tenant ID", "@sage/xtrem-system/nodes__sys_custom_record__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_custom_record__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__sys_custom_record__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_custom_record__node_name": "System custom record", "@sage/xtrem-system/nodes__sys_custom_record__property__bundleId": "Bundle ID", "@sage/xtrem-system/nodes__sys_custom_record__property__factoryName": "Factory name", "@sage/xtrem-system/nodes__sys_custom_sql_history__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_custom_sql_history__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__sys_custom_sql_history__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_custom_sql_history__node_name": "System custom SQL history", "@sage/xtrem-system/nodes__sys_custom_sql_history__property__dryRun": "Dry run", "@sage/xtrem-system/nodes__sys_custom_sql_history__property__endDateTime": "End date time", "@sage/xtrem-system/nodes__sys_custom_sql_history__property__result": "Result", "@sage/xtrem-system/nodes__sys_custom_sql_history__property__scriptContent": "Script content", "@sage/xtrem-system/nodes__sys_custom_sql_history__property__scriptPath": "Script path", "@sage/xtrem-system/nodes__sys_custom_sql_history__property__startDateTime": "Start date time", "@sage/xtrem-system/nodes__sys_customer__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_customer__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__sys_customer__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_customer__node_name": "System customer", "@sage/xtrem-system/nodes__sys_customer__property__customerId": "Customer ID", "@sage/xtrem-system/nodes__sys_customer__property__name": "Name", "@sage/xtrem-system/nodes__sys_data_validation_report__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_data_validation_report__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__sys_data_validation_report__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_data_validation_report__node_name": "System data validation report", "@sage/xtrem-system/nodes__sys_data_validation_report__property__lines": "Lines", "@sage/xtrem-system/nodes__sys_data_validation_report_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_data_validation_report_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__sys_data_validation_report_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_data_validation_report_line__node_name": "System data validation report line", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__extraInfo": "Extra info", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__message": "Message", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__nodeId": "Node ID", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__nodeName": "Node name", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__path": "Path", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__report": "Report", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__severity": "Severity", "@sage/xtrem-system/nodes__sys_device_token__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_device_token__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__sys_device_token__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_device_token__mutation__createDeviceToken": "Create device token", "@sage/xtrem-system/nodes__sys_device_token__mutation__createDeviceToken__failed": "Create device token failed.", "@sage/xtrem-system/nodes__sys_device_token__mutation__deleteDeviceToken": "Delete device token", "@sage/xtrem-system/nodes__sys_device_token__mutation__deleteDeviceToken__failed": "Delete device token failed.", "@sage/xtrem-system/nodes__sys_device_token__mutation__deleteDeviceToken__parameter__tokenId": "Token ID", "@sage/xtrem-system/nodes__sys_device_token__node_name": "System device token", "@sage/xtrem-system/nodes__sys_device_token__property__expiration": "Expiry", "@sage/xtrem-system/nodes__sys_device_token__property__loginTestUrl": "Login test URL", "@sage/xtrem-system/nodes__sys_device_token__property__loginUrl": "Login URL", "@sage/xtrem-system/nodes__sys_device_token__property__name": "Name", "@sage/xtrem-system/nodes__sys_device_token__property__tokenId": "Token ID", "@sage/xtrem-system/nodes__sys_note__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_note__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__sys_note__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_note__node_name": "Sys note", "@sage/xtrem-system/nodes__sys_note__property__content": "Content", "@sage/xtrem-system/nodes__sys_note__property__title": "Title", "@sage/xtrem-system/nodes__sys_note_association__node_name": "Sys note association", "@sage/xtrem-system/nodes__sys_note_association__property__note": "Note", "@sage/xtrem-system/nodes__sys_note_association__property__sourceNodeId": "Source node ID", "@sage/xtrem-system/nodes__sys_note_association__property__sourceNodeName": "Source node name", "@sage/xtrem-system/nodes__sys_pack_allocation__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_pack_allocation__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__sys_pack_allocation__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_pack_allocation__mutation__activate": "Activate", "@sage/xtrem-system/nodes__sys_pack_allocation__mutation__activate__failed": "Activate failed.", "@sage/xtrem-system/nodes__sys_pack_allocation__mutation__activate__parameter__packageId": "Package ID", "@sage/xtrem-system/nodes__sys_pack_allocation__mutation__deactivate": "Deactivate", "@sage/xtrem-system/nodes__sys_pack_allocation__mutation__deactivate__failed": "Deactivate failed.", "@sage/xtrem-system/nodes__sys_pack_allocation__mutation__deactivate__parameter__packageId": "Package ID", "@sage/xtrem-system/nodes__sys_pack_allocation__node_name": "System pack allocation", "@sage/xtrem-system/nodes__sys_pack_allocation__property__isActivable": "Can be activated", "@sage/xtrem-system/nodes__sys_pack_allocation__property__isActive": "Active", "@sage/xtrem-system/nodes__sys_pack_allocation__property__package": "Package", "@sage/xtrem-system/nodes__sys_pack_allocation__property__status": "Status", "@sage/xtrem-system/nodes__sys_pack_version__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_pack_version__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__sys_pack_version__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_pack_version__node_name": "System pack version", "@sage/xtrem-system/nodes__sys_pack_version__property__isBundle": "Bundle", "@sage/xtrem-system/nodes__sys_pack_version__property__isHidden": "Hidden", "@sage/xtrem-system/nodes__sys_pack_version__property__isReleased": "Released", "@sage/xtrem-system/nodes__sys_pack_version__property__isUpgradeBundle": "Upgrade bundle", "@sage/xtrem-system/nodes__sys_pack_version__property__name": "Name", "@sage/xtrem-system/nodes__sys_pack_version__property__sqlSchemaVersion": "SQL schema version", "@sage/xtrem-system/nodes__sys_pack_version__property__version": "Version", "@sage/xtrem-system/nodes__sys_patch_history__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_patch_history__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__sys_patch_history__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_patch_history__node_name": "System patch history", "@sage/xtrem-system/nodes__sys_patch_history__property__packageName": "Package name", "@sage/xtrem-system/nodes__sys_patch_history__property__patchName": "Patch name", "@sage/xtrem-system/nodes__sys_patch_history__property__result": "Result", "@sage/xtrem-system/nodes__sys_patch_history__property__version": "Version", "@sage/xtrem-system/nodes__sys_service_option__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_service_option__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__sys_service_option__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_service_option__node_name": "System service option", "@sage/xtrem-system/nodes__sys_service_option__property__childServiceOptions": "Child service options", "@sage/xtrem-system/nodes__sys_service_option__property__description": "Description", "@sage/xtrem-system/nodes__sys_service_option__property__isActiveByDefault": "Active by default", "@sage/xtrem-system/nodes__sys_service_option__property__isHidden": "Hidden", "@sage/xtrem-system/nodes__sys_service_option__property__optionName": "Option name", "@sage/xtrem-system/nodes__sys_service_option__property__package": "Package", "@sage/xtrem-system/nodes__sys_service_option__property__parentServiceOptions": "Parent service options", "@sage/xtrem-system/nodes__sys_service_option__property__status": "Status", "@sage/xtrem-system/nodes__sys_service_option_state__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_service_option_state__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__sys_service_option_state__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_service_option_state__node_name": "System service option status", "@sage/xtrem-system/nodes__sys_service_option_state__property__imageStatus": "Image status", "@sage/xtrem-system/nodes__sys_service_option_state__property__isActivable": "Can be activated", "@sage/xtrem-system/nodes__sys_service_option_state__property__isActive": "Active", "@sage/xtrem-system/nodes__sys_service_option_state__property__isAvailable": "Available", "@sage/xtrem-system/nodes__sys_service_option_state__property__isHiddenByConfig": "Hidden by default", "@sage/xtrem-system/nodes__sys_service_option_state__property__isPackageActive": "Active package", "@sage/xtrem-system/nodes__sys_service_option_state__property__isReadyToUse": "Ready to use", "@sage/xtrem-system/nodes__sys_service_option_state__property__serviceOption": "Service option", "@sage/xtrem-system/nodes__sys_service_option_to_service_option__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_service_option_to_service_option__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__sys_service_option_to_service_option__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_service_option_to_service_option__node_name": "System service option to service option", "@sage/xtrem-system/nodes__sys_service_option_to_service_option__property__childServiceOption": "Child service option", "@sage/xtrem-system/nodes__sys_service_option_to_service_option__property__parentServiceOption": "Parent service option", "@sage/xtrem-system/nodes__sys_tag__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_tag__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__sys_tag__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_tag__node_name": "Sys tag", "@sage/xtrem-system/nodes__sys_tag__property__color": "Color", "@sage/xtrem-system/nodes__sys_tag__property__description": "Description", "@sage/xtrem-system/nodes__sys_tag__property__name": "Name", "@sage/xtrem-system/nodes__sys_tag__property__rgbColor": "RGB colour", "@sage/xtrem-system/nodes__sys_tenant__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_tenant__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__sys_tenant__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_tenant__mutation__resetTenantDocuments": "Reset tenant documents", "@sage/xtrem-system/nodes__sys_tenant__mutation__resetTenantDocuments__failed": "Reset tenant documents failed.", "@sage/xtrem-system/nodes__sys_tenant__mutation__resetTenantDocuments__parameter__tenantId": "Tenant ID", "@sage/xtrem-system/nodes__sys_tenant__node_name": "System tenant", "@sage/xtrem-system/nodes__sys_tenant__property__customer": "Customer", "@sage/xtrem-system/nodes__sys_tenant__property__directoryName": "Directory name", "@sage/xtrem-system/nodes__sys_tenant__property__name": "Name", "@sage/xtrem-system/nodes__sys_tenant__property__tenantId": "Tenant ID", "@sage/xtrem-system/nodes__sys_tenant__query__getTenantInformation": "Get tenant information", "@sage/xtrem-system/nodes__sys_tenant__query__getTenantInformation__failed": "Get tenant information failed.", "@sage/xtrem-system/nodes__sys_tenant__tenant_directory_name_should_be_in_kebab_case": "Write the tenant directory name in kebab-case.", "@sage/xtrem-system/nodes__sys_tenant__tenant_name_characters_not_authorized": "The tenant name contains unauthorised characters.", "@sage/xtrem-system/nodes__sys_upgrade__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_upgrade__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__sys_upgrade__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_upgrade__node_name": "System upgrade", "@sage/xtrem-system/nodes__sys_upgrade__property__bundleId": "Bundle ID", "@sage/xtrem-system/nodes__sys_upgrade__property__managedItems": "Managed items", "@sage/xtrem-system/nodes__sys_vendor__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_vendor__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__sys_vendor__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_vendor__node_name": "System vendor", "@sage/xtrem-system/nodes__sys_vendor__property__description": "Description", "@sage/xtrem-system/nodes__sys_vendor__property__name": "Name", "@sage/xtrem-system/nodes__sys-client-user-settings": "You can only select one.", "@sage/xtrem-system/nodes__sys-layer__name-must-be-kebab-case": "", "@sage/xtrem-system/nodes__user__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__user__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__user__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__user__cannot_modify_own_rights": "You are not allowed to modify your own administrator rights.", "@sage/xtrem-system/nodes__user__mutation__logPageVisit": "Log page visit", "@sage/xtrem-system/nodes__user__mutation__logPageVisit__failed": "Log page visit failed.", "@sage/xtrem-system/nodes__user__mutation__logPageVisit__parameter__path": "Path", "@sage/xtrem-system/nodes__user__mutation__sendWelcomeMail": "Send welcome email", "@sage/xtrem-system/nodes__user__mutation__sendWelcomeMail__failed": "Send welcome mail failed.", "@sage/xtrem-system/nodes__user__mutation__sendWelcomeMail__parameter__isAdmin": "Admin", "@sage/xtrem-system/nodes__user__mutation__sendWelcomeMail__parameter__users": "Users", "@sage/xtrem-system/nodes__user__mutation__setDemoPersona": "Set demo persona", "@sage/xtrem-system/nodes__user__mutation__setDemoPersona__failed": "Set demo persona failed.", "@sage/xtrem-system/nodes__user__mutation__setDemoPersona__parameter__email": "Email", "@sage/xtrem-system/nodes__user__mutation__updateClientSettings": "Update client settings", "@sage/xtrem-system/nodes__user__mutation__updateClientSettings__failed": "Update client settings failed.", "@sage/xtrem-system/nodes__user__mutation__updateClientSettings__parameter__clientSettings": "Client settings", "@sage/xtrem-system/nodes__user__node_name": "User", "@sage/xtrem-system/nodes__user__only_admin_can_add_admin": "Only an administrator user can add or modify another user's administrator rights.", "@sage/xtrem-system/nodes__user__property__clientSettings": "Client settings", "@sage/xtrem-system/nodes__user__property__displayName": "Display name", "@sage/xtrem-system/nodes__user__property__email": "Email", "@sage/xtrem-system/nodes__user__property__firstName": "First name", "@sage/xtrem-system/nodes__user__property__isActive": "Active", "@sage/xtrem-system/nodes__user__property__isAdministrator": "Administrator", "@sage/xtrem-system/nodes__user__property__isApiUser": "API user", "@sage/xtrem-system/nodes__user__property__isDemoPersona": "Demo persona", "@sage/xtrem-system/nodes__user__property__isFirstAdminUser": "First admin user", "@sage/xtrem-system/nodes__user__property__isOperatorUser": "Is operator user", "@sage/xtrem-system/nodes__user__property__lastName": "Last name", "@sage/xtrem-system/nodes__user__property__navigation": "Navigation", "@sage/xtrem-system/nodes__user__property__operatorCode": "Operator code", "@sage/xtrem-system/nodes__user__property__operatorId": "Operator ID", "@sage/xtrem-system/nodes__user__property__photo": "Photo", "@sage/xtrem-system/nodes__user__property__preferences": "Preferences", "@sage/xtrem-system/nodes__user__property__setupId": "Setup ID", "@sage/xtrem-system/nodes__user__property__userType": "User type", "@sage/xtrem-system/nodes__user__query__demoPersonas": "Demo personas", "@sage/xtrem-system/nodes__user__query__demoPersonas__failed": "Demo personas failed.", "@sage/xtrem-system/nodes__user_navigation__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__user_navigation__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__user_navigation__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__user_navigation__node_name": "User navigation", "@sage/xtrem-system/nodes__user_navigation__property__bookmarks": "Bookmarks", "@sage/xtrem-system/nodes__user_navigation__property__history": "History", "@sage/xtrem-system/nodes__user_navigation__property__user": "User", "@sage/xtrem-system/nodes__user_preferences__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__user_preferences__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__user_preferences__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__user_preferences__node_name": "User preferences", "@sage/xtrem-system/nodes__user_preferences__property__importExportDateFormat": "Import/export date format", "@sage/xtrem-system/nodes__user_preferences__property__importExportDelimiter": "Import/export delimiter", "@sage/xtrem-system/nodes__user_preferences__property__isExternal": "Is external", "@sage/xtrem-system/nodes__user_preferences__property__isWelcomeMailSent": "Welcome mail sent", "@sage/xtrem-system/nodes__user_preferences__property__user": "User", "@sage/xtrem-system/nodes__user_preferences__query__activeServiceOptions": "Active service options", "@sage/xtrem-system/nodes__user_preferences__query__activeServiceOptions__failed": "Active service options failed.", "@sage/xtrem-system/package__name": "System", "@sage/xtrem-system/package-activated": "Package activated", "@sage/xtrem-system/package-deactivated": "Package deactivated", "@sage/xtrem-system/pages__client_user_settings_edit____title": "Create a view", "@sage/xtrem-system/pages__client_user_settings_edit__generalSection____title": "General", "@sage/xtrem-system/pages__client_user_settings_edit__save____title": "Save", "@sage/xtrem-system/pages__client_user_settings_edit__title____title": "Name", "@sage/xtrem-system/pages__client_user_settings_list____title": "Manage views", "@sage/xtrem-system/pages__client_user_settings_list__clientSettings____columns__title__title": "Name", "@sage/xtrem-system/pages__client_user_settings_list__clientSettings____dropdownActions__title": "Remove", "@sage/xtrem-system/pages__client_user_settings_list__clientSettings____dropdownActions__title__remove": "Remove", "@sage/xtrem-system/pages__client_user_settings_list__generalSection____title": "General", "@sage/xtrem-system/pages__client_user_settings_list__save____title": "Save", "@sage/xtrem-system/pages__delete_page_dialog_content": "You are about to delete this record.", "@sage/xtrem-system/pages__delete_page_dialog_title": "Confirm delete", "@sage/xtrem-system/pages__pack_allocation____navigationPanel__optionsMenu__title": "", "@sage/xtrem-system/pages__pack_allocation____navigationPanel__optionsMenu__title__2": "", "@sage/xtrem-system/pages__pack_allocation____navigationPanel__optionsMenu__title__3": "", "@sage/xtrem-system/pages__pack_allocation____title": "", "@sage/xtrem-system/pages__pack_allocation__closePage____title": "", "@sage/xtrem-system/pages__pack_allocation__generalSection____title": "", "@sage/xtrem-system/pages__pack_allocation__isActivable____title": "", "@sage/xtrem-system/pages__pack_allocation__isActive____title": "", "@sage/xtrem-system/pages__pack_allocation__package____title": "", "@sage/xtrem-system/pages__pack_allocation__version____title": "", "@sage/xtrem-system/pages__pack_version____navigationPanel__optionsMenu__title": "", "@sage/xtrem-system/pages__pack_version____navigationPanel__optionsMenu__title__2": "", "@sage/xtrem-system/pages__pack_version____navigationPanel__optionsMenu__title__3": "", "@sage/xtrem-system/pages__pack_version____title": "", "@sage/xtrem-system/pages__pack_version__closePage____title": "", "@sage/xtrem-system/pages__pack_version__generalSection____title": "", "@sage/xtrem-system/pages__pack_version__isBundle____title": "", "@sage/xtrem-system/pages__pack_version__package____title": "", "@sage/xtrem-system/pages__pack_version__version____title": "", "@sage/xtrem-system/pages__service_option_state____navigationPanel__dropdownActions__title": "Activate", "@sage/xtrem-system/pages__service_option_state____navigationPanel__dropdownActions__title__2": "Deactivate", "@sage/xtrem-system/pages__service_option_state____navigationPanel__listItem__line_4__title": "Status", "@sage/xtrem-system/pages__service_option_state____navigationPanel__listItem__line2__title": "Description", "@sage/xtrem-system/pages__service_option_state____navigationPanel__listItem__line3__title": "Active", "@sage/xtrem-system/pages__service_option_state____navigationPanel__listItem__line4__title": "Status", "@sage/xtrem-system/pages__service_option_state____navigationPanel__listItem__title__title": "Name", "@sage/xtrem-system/pages__service_option_state____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-system/pages__service_option_state____objectTypePlural": "Service options", "@sage/xtrem-system/pages__service_option_state____objectTypeSingular": "Service option", "@sage/xtrem-system/pages__service_option_state____title": "Service options", "@sage/xtrem-system/pages__service_option_state__cancel____title": "", "@sage/xtrem-system/pages__service_option_state__closePage____title": "", "@sage/xtrem-system/pages__service_option_state__description____title": "Description", "@sage/xtrem-system/pages__service_option_state__generalSection____title": "General", "@sage/xtrem-system/pages__service_option_state__isActivable____title": "Available", "@sage/xtrem-system/pages__service_option_state__isActive____title": "Active", "@sage/xtrem-system/pages__service_option_state__isAvailable____title": "Available", "@sage/xtrem-system/pages__service_option_state__isHiddenByConfig____title": "Active", "@sage/xtrem-system/pages__service_option_state__optionName____title": "Name", "@sage/xtrem-system/pages__service_option_state__reset_tenant_data_done": "Tenant data reset done", "@sage/xtrem-system/pages__service_option_state__save____title": "Save", "@sage/xtrem-system/pages__service_option_state__serviceOption____title": "Service option", "@sage/xtrem-system/pages__service_option_state__status____title": "Status", "@sage/xtrem-system/pages__sys_changelog____navigationPanel__listItem__line2__title": "Message", "@sage/xtrem-system/pages__sys_changelog____navigationPanel__listItem__line3__title": "Change timestamp", "@sage/xtrem-system/pages__sys_changelog____navigationPanel__listItem__title__title": "Hash", "@sage/xtrem-system/pages__sys_changelog____title": "Changelog", "@sage/xtrem-system/pages__sys_device_token____navigationPanel__listItem__tokenId__title": "ID", "@sage/xtrem-system/pages__sys_device_token____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-system/pages__sys_device_token____title": "Device token", "@sage/xtrem-system/pages__sys_device_token__createDeviceToken____title": "Save", "@sage/xtrem-system/pages__sys_device_token__deleteDeviceToken____title": "Delete", "@sage/xtrem-system/pages__sys_device_token__deviceTokenBlock____title": "Device token information", "@sage/xtrem-system/pages__sys_device_token__expiration____title": "Expiry", "@sage/xtrem-system/pages__sys_device_token__loginTestUrl____title": "Device test link", "@sage/xtrem-system/pages__sys_device_token__loginUrl____title": "Device login link", "@sage/xtrem-system/pages__sys_device_token__name____title": "Name", "@sage/xtrem-system/pages__sys_device_token__tokenId____title": "Token", "@sage/xtrem-system/pages__sys_layer____navigationPanel__optionsMenu__title": "", "@sage/xtrem-system/pages__sys_layer____title": "", "@sage/xtrem-system/pages__sys_layer___id____title": "", "@sage/xtrem-system/pages__sys_layer__dataLayerInformation____title": "", "@sage/xtrem-system/pages__sys_layer__deleteLayerAction____title": "", "@sage/xtrem-system/pages__sys_layer__description____title": "", "@sage/xtrem-system/pages__sys_layer__exportLayer____title": "", "@sage/xtrem-system/pages__sys_layer__generalSection____title": "", "@sage/xtrem-system/pages__sys_layer__name____title": "", "@sage/xtrem-system/pages__sys_pack_allocation____navigationPanel__listItem__lineRight__title": "Version", "@sage/xtrem-system/pages__sys_pack_allocation____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-system/pages__sys_pack_allocation____navigationPanel__optionsMenu__title__2": "Packages", "@sage/xtrem-system/pages__sys_pack_allocation____title": "Package activation", "@sage/xtrem-system/pages__sys_pack_allocation__closePage____title": "", "@sage/xtrem-system/pages__sys_pack_allocation__generalSection____title": "General", "@sage/xtrem-system/pages__sys_pack_allocation__isActivable____title": "Available", "@sage/xtrem-system/pages__sys_pack_allocation__isActive____title": "Active", "@sage/xtrem-system/pages__sys_pack_allocation__package____title": "Package", "@sage/xtrem-system/pages__sys_pack_allocation__version____title": "Version", "@sage/xtrem-system/pages__sys_tag____navigationPanel__listItem__line2__title": "Description", "@sage/xtrem-system/pages__sys_tag____navigationPanel__listItem__rgbColor__title": "RGB color", "@sage/xtrem-system/pages__sys_tag____navigationPanel__listItem__title__title": "Name", "@sage/xtrem-system/pages__sys_tag____title": "Tag", "@sage/xtrem-system/pages__sys_tag__description____title": "Description", "@sage/xtrem-system/pages__sys_tag__generalSection____title": "General", "@sage/xtrem-system/pages__sys_tag__name____title": "Name", "@sage/xtrem-system/pages__sys_tag__rgbColor____title": "RGB color", "@sage/xtrem-system/pages__tenant_information____title": "Tenant information", "@sage/xtrem-system/pages__tenant_information__cancel_reset_tenant_data_button_text": "Cancel", "@sage/xtrem-system/pages__tenant_information__confirm_reset_tenant_data_button_text": "Confirm reset tenant data", "@sage/xtrem-system/pages__tenant_information__generalSection____title": "General", "@sage/xtrem-system/pages__tenant_information__reset_tenant_data_button_text": "Reset tenant data", "@sage/xtrem-system/pages__tenant_information__resetDataDescription____content": "Reset tenant data will delete all the movements (such as sales and purchase orders, stock level and movements).\nThis operation cannot be reverted. Are you sure?", "@sage/xtrem-system/pages__tenant_information__resetTenantId____helperText": "Please type the tenant ID to delete", "@sage/xtrem-system/pages__tenant_information__resetTenantId____title": "Confirm Tenant ID", "@sage/xtrem-system/pages__tenant_information__resetTenantSection____title": "Reset", "@sage/xtrem-system/pages__tenant_information__sumologic_button_text": "View Sumo Logic logs", "@sage/xtrem-system/pages__tenant_information__sumologicLink____helperText": "You need to be logged in to Sumo Logic before you can view the logs.", "@sage/xtrem-system/pages__tenant_information__tenantId____title": "Tenant ID", "@sage/xtrem-system/pages__tenant_information__tenantVersion____title": "Tenant version", "@sage/xtrem-system/pages__user____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-system/pages__user____title": "Users", "@sage/xtrem-system/pages__user___id____title": "ID", "@sage/xtrem-system/pages__user__email____title": "Email", "@sage/xtrem-system/pages__user__firstName____title": "First name", "@sage/xtrem-system/pages__user__generalSection____title": "General", "@sage/xtrem-system/pages__user__idBlock____title": "ID", "@sage/xtrem-system/pages__user__isActive____title": "Active", "@sage/xtrem-system/pages__user__lastName____title": "Last name", "@sage/xtrem-system/pages__user__userInformationBlock____title": "User information", "@sage/xtrem-system/pages__user__userPhotoBlock____title": "Photo", "@sage/xtrem-system/pages-confirm-cancel": "Cancel", "@sage/xtrem-system/pages-confirm-delete": "Delete", "@sage/xtrem-system/permission__create__name": "Create", "@sage/xtrem-system/permission__delete__name": "Delete", "@sage/xtrem-system/permission__get_tenant_information__name": "Get tenant information", "@sage/xtrem-system/permission__read__name": "Read", "@sage/xtrem-system/permission__reset_tenant_documents__name": "Reset tenant documents", "@sage/xtrem-system/permission__update__name": "Update", "@sage/xtrem-system/productKey": "Product value", "@sage/xtrem-system/productKey2": "Product value {{num}}", "@sage/xtrem-system/reserved-api-user-email": "Email address {{value}} is reserved for API users.", "@sage/xtrem-system/reserved-operator-user-email": "Operator email address format is reserved for operator users.", "@sage/xtrem-system/reset-tenant-data-not-admin": "Only administrators can reset tenant data", "@sage/xtrem-system/reset-tenant-data-not-admin-with-id": "Reset tenant was attempted by user {{userId}}", "@sage/xtrem-system/reset-tenant-data-tenant-id-mismatched": "The {{tenantId}} tenant ID does not match the active tenant ({{currentId}}).", "@sage/xtrem-system/service_options__changelog__name": "Changelog", "@sage/xtrem-system/service_options__dev_tools__name": "Development tools", "@sage/xtrem-system/service_options__is_demo_tenant__name": "Is demo tenant", "@sage/xtrem-system/service_options__sys_device_token__name": "System device token", "@sage/xtrem-system/service_options__sys_tag__name": "Sys tag", "@sage/xtrem-system/statusIcon": "", "@sage/xtrem-system/sys_device_token_creation_error": "Device token creation failed: {{errorMessage}}", "@sage/xtrem-system/sys_device_token_deletion_error": "Device token deletion failed: {{errorMessage}}", "@sage/xtrem-system/sys_device_token_device_token_already_created": "Current device already has a token created.", "@sage/xtrem-system/system-user-forbidden": "System users cannot be created or updated.", "@sage/xtrem-system/update-confirmation": "", "@sage/xtrem-system/widgets__system_version____title": "System version"}