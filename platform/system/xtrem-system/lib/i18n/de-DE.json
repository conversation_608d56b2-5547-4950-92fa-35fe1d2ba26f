{"@sage/xtrem-system/activity__company__name": "Unternehmen", "@sage/xtrem-system/activity__service_option_state__name": "Status Dienstoption", "@sage/xtrem-system/activity__site__name": "<PERSON><PERSON>", "@sage/xtrem-system/activity__sys_device_token__name": "System-Geräte-Token", "@sage/xtrem-system/activity__sys_note__name": "Systemnotiz", "@sage/xtrem-system/activity__sys_tag__name": "System-Tag", "@sage/xtrem-system/activity__tenant__name": "Tenant", "@sage/xtrem-system/bundle-activated": "Bundle aktiviert", "@sage/xtrem-system/bundle-deactivated": "Bundle deaktiviert", "@sage/xtrem-system/cannot-deactivate-admin-demo-persona": "Die Admin-Demo-Persona kann nicht aktiviert werden.", "@sage/xtrem-system/confirm-activate-service-option-message": "<PERSON>e sind dabei, die Dienstoption {{serviceOptionName}} zu aktivieren.", "@sage/xtrem-system/confirm-activate-service-option-title": "Dienstoption aktivieren", "@sage/xtrem-system/confirm-deactivate-service-option-message": "<PERSON>e sind dabei, die Dienstoption {{serviceOptionName}} zu deaktivieren.", "@sage/xtrem-system/confirm-deactivate-service-option-title": "Dienstoption deaktivieren", "@sage/xtrem-system/create-confirmation": "Datensatz erstellt", "@sage/xtrem-system/data_types__big_id__name": "Big ID", "@sage/xtrem-system/data_types__big_url__name": "Big URL", "@sage/xtrem-system/data_types__bundle_id_data_type__name": "Datentyp Bundle-ID", "@sage/xtrem-system/data_types__bundle_path_type__name": "Pfadtyp Bundle", "@sage/xtrem-system/data_types__bundle_version_data_type__name": "Datentyp Bundle-Version", "@sage/xtrem-system/data_types__checksum_data_type__name": "Datentyp <PERSON>", "@sage/xtrem-system/data_types__code__name": "Code", "@sage/xtrem-system/data_types__code_data_type__name": "Datentyp Code", "@sage/xtrem-system/data_types__color__name": "Color", "@sage/xtrem-system/data_types__colored_element_enum__name": "Enum farbiges Element", "@sage/xtrem-system/data_types__company__name": "Unternehmen", "@sage/xtrem-system/data_types__content_length_decimal_data_type__name": "Datentype Dezimal Länge Inhalt", "@sage/xtrem-system/data_types__decimal__name": "Dezimal", "@sage/xtrem-system/data_types__default_decimal_data_type__name": "Datentyp Standard Dezimal", "@sage/xtrem-system/data_types__description__name": "Bezeichnung", "@sage/xtrem-system/data_types__description_array_data_type__name": "Datentyp A<PERSON>y <PERSON>", "@sage/xtrem-system/data_types__description_data_type__name": "Datentyp <PERSON>", "@sage/xtrem-system/data_types__email__name": "E-Mail", "@sage/xtrem-system/data_types__folder__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/data_types__http_base_url__name": "HTTP-Basis-URL", "@sage/xtrem-system/data_types__http_url__name": "HTTP-URL", "@sage/xtrem-system/data_types__icon__name": "Symbol", "@sage/xtrem-system/data_types__id__name": "ID", "@sage/xtrem-system/data_types__locale_datatype__name": "Datentyp Gebietsschema", "@sage/xtrem-system/data_types__locale_id_data_type__name": "Datentyp ID Gebietsschema", "@sage/xtrem-system/data_types__localized_description__name": "Lokalisierte Bezeichnung", "@sage/xtrem-system/data_types__localized_name__name": "Lokalisierter Name", "@sage/xtrem-system/data_types__localized_short_description__name": "Lokalisierte Kurzbezeichnung", "@sage/xtrem-system/data_types__localized_title__name": "Lokalisierter Titel", "@sage/xtrem-system/data_types__mime_type__name": "MIME-Typ", "@sage/xtrem-system/data_types__name__name": "Name", "@sage/xtrem-system/data_types__nanoid__name": "Nanoid", "@sage/xtrem-system/data_types__note_content_data_type__name": "Datentyp Inhalt Notiz", "@sage/xtrem-system/data_types__notification_data_data_type__name": "Datentyp Benachrichtigungsdaten", "@sage/xtrem-system/data_types__passphrase__name": "Passphrase", "@sage/xtrem-system/data_types__password__name": "Passwort", "@sage/xtrem-system/data_types__quantity_data_type__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/data_types__rate__name": "Satz", "@sage/xtrem-system/data_types__rgb_color__name": "RGB-Farbe", "@sage/xtrem-system/data_types__service_option_status_enum__name": "Enum Status Dienstoption", "@sage/xtrem-system/data_types__setup_id_data_type__name": "Datentyp SEtup-ID", "@sage/xtrem-system/data_types__short_description__name": "Kurzbezeichnung", "@sage/xtrem-system/data_types__single_character__name": "Einzelnes Zeichen", "@sage/xtrem-system/data_types__site__name": "<PERSON><PERSON>", "@sage/xtrem-system/data_types__sys_client_notification_action_style_enum__name": "Enum Stil Aktion Benachrichtigung System-Client", "@sage/xtrem-system/data_types__sys_client_notification_level_enum__name": "Enum Ebene Aktion Benachrichtigung System-Client", "@sage/xtrem-system/data_types__title__name": "Titel", "@sage/xtrem-system/data_types__url__name": "URL", "@sage/xtrem-system/data_types__user__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/data_types__user_import_export_date_format_enum__name": "Enum Datumsformat Import/Export Benutzer", "@sage/xtrem-system/data_types__user_type_enum__name": "<PERSON><PERSON>", "@sage/xtrem-system/data_types__uuid__name": "UUID", "@sage/xtrem-system/data_types__version__name": "Version", "@sage/xtrem-system/data-types/locale_datatype__invalid_locale": "Der Gebietsschemawert ist ungültig: ({{locale}}).", "@sage/xtrem-system/data-types/rgb__invalid_rgb": "Dieser Wert ist kein RGB-Farbcode: \"{{value}}\".", "@sage/xtrem-system/data-types/url_datatype__invalid_protocol": "URL '{{url}}' er<PERSON><PERSON> {{protocol}}, erwartet {{allowedProtocols}}.", "@sage/xtrem-system/data-types/url_datatype__invalid_url": "Falsche URL '{{url}}'.", "@sage/xtrem-system/delete-confirmation": "Datensatz <PERSON>", "@sage/xtrem-system/delete-dialog-content": "<PERSON>e sind dabei, diesen Datensatz zu löschen. Bestätigen?", "@sage/xtrem-system/delete-dialog-title": "Löschen bestätigen", "@sage/xtrem-system/delete-user-forbidden": "Benutzer können nicht gelöscht werden.", "@sage/xtrem-system/delete-view-warning-title": "Ansicht löschen", "@sage/xtrem-system/delete-view-warning-title-message": "<PERSON>e sind dabei, diese <PERSON><PERSON><PERSON> zu löschen.", "@sage/xtrem-system/duplicate_first_admin": "Es ist bereits ein Admin-Benutzer definiert. Sie können keinen weiteren erstellen.", "@sage/xtrem-system/enums__colored_element__backgroundColor": "Hintergrundfarbe", "@sage/xtrem-system/enums__colored_element__borderColor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/enums__colored_element__textColor": "Textfarbe", "@sage/xtrem-system/enums__service_option_status__experimental": "Experimentell", "@sage/xtrem-system/enums__service_option_status__released": "Freigegeben", "@sage/xtrem-system/enums__service_option_status__workInProgress": "Work-In-Progress", "@sage/xtrem-system/enums__service_option_type__experimental": "Experimentell", "@sage/xtrem-system/enums__service_option_type__released": "Freigegeben", "@sage/xtrem-system/enums__service_option_type__workInProgress": "In Bearbeitung", "@sage/xtrem-system/enums__sys_client_notification_action_style__link": "Verknüpfung", "@sage/xtrem-system/enums__sys_client_notification_action_style__primary": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/enums__sys_client_notification_action_style__secondary": "Sekundär", "@sage/xtrem-system/enums__sys_client_notification_action_style__tertiary": "Tertiär", "@sage/xtrem-system/enums__sys_client_notification_level__approval": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/enums__sys_client_notification_level__error": "<PERSON><PERSON>", "@sage/xtrem-system/enums__sys_client_notification_level__info": "Info", "@sage/xtrem-system/enums__sys_client_notification_level__success": "Erfolgreich", "@sage/xtrem-system/enums__sys_client_notification_level__warning": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/enums__user_import_export_date_format__europeanDash": "dd-mm-yyyy", "@sage/xtrem-system/enums__user_import_export_date_format__europeanSlash": "dd/mm/yyyy", "@sage/xtrem-system/enums__user_import_export_date_format__isoDash": "yyyy-mm-dd", "@sage/xtrem-system/enums__user_import_export_date_format__isoSlash": "yyyy/mm/dd", "@sage/xtrem-system/enums__user_import_export_date_format__usDash": "mm-dd-yyyy", "@sage/xtrem-system/enums__user_import_export_date_format__usSlash": "mm/dd/yyyy", "@sage/xtrem-system/enums__user_type__application": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/enums__user_type__system": "System", "@sage/xtrem-system/error-packages-cannot-be": "Pakete können nicht deaktiviert werden.", "@sage/xtrem-system/error-packages-cannot-be-deactivated": "Pakete können nicht deaktiviert werden", "@sage/xtrem-system/failed-to-save-view": "<PERSON><PERSON>rn der Ansicht ist ein Fehler aufgetreten.", "@sage/xtrem-system/invalid-admin-demo-persona": "<PERSON>s kann keine andere Admin-Demo-Persona als der Standard erstellt werden.", "@sage/xtrem-system/invalid-api-user-email": "Die API-Benutzer-E-Mail-Adresse {{value}} ist nicht korrekt. Verwenden Sie dieses Format: api-<devid>@localhost.domain", "@sage/xtrem-system/invalid-api-user-is-admin": "Ein API-Benutz<PERSON> kann kein Admin sein.", "@sage/xtrem-system/invalid-api-user-is-demo-persona": "Ein API-Ben<PERSON>er kann keine Demo-Persona sein.", "@sage/xtrem-system/invalid-email": "E-Mail-Ad<PERSON>e nicht korrekt: ({{value}})", "@sage/xtrem-system/invalid-operator-pin-code": "Ein Anwender-PIN-Code muss aus mindestens 4 Zeichen bestehen.", "@sage/xtrem-system/invalid-operator-pincode-not-unique": "Ein Operator-PIN-Code muss eindeutig sein.", "@sage/xtrem-system/invalid-operator-user-email": "Ungültige Anwender-Benutzer-E-Mail-Adresse. Erwartet: Anwenderr-<Name>@localhost.domain", "@sage/xtrem-system/invalid-operator-user-is-admin": "Ein Anwender-<PERSON><PERSON><PERSON> kann kein <PERSON>min sein.", "@sage/xtrem-system/invalid-operator-user-is-demo-persona": "Ein Anwender-<PERSON><PERSON><PERSON> kann keine Demo-Persona sein.", "@sage/xtrem-system/is-active/node-id-combination-not-active": "{{nodeName}} {{dataId}} inaktiv", "@sage/xtrem-system/language-master-locale-different-from-default": "Das Hauptgebietsschema muss dem Standardgebietsschema für die gleiche Sprache entsprechen.", "@sage/xtrem-system/menu_item__administration": "Administration", "@sage/xtrem-system/menu_item__application-data": "Konfiguration der Anwendung", "@sage/xtrem-system/menu_item__bundle-activation": "Bundle-Aktivierung", "@sage/xtrem-system/menu_item__integrations": "", "@sage/xtrem-system/menu_item__service-option": "Serviceoptionen", "@sage/xtrem-system/menu_item__user": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/menu_item__user-data": "Benutzer und Sicherheit", "@sage/xtrem-system/nodes__company__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__company__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__company__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__company__node_name": "Unternehmen", "@sage/xtrem-system/nodes__company__property__id": "ID", "@sage/xtrem-system/nodes__company__property__isActive": "Aktiv", "@sage/xtrem-system/nodes__company__property__sites": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__locale__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__locale__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__locale__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__locale__node_name": "Gebietsschema", "@sage/xtrem-system/nodes__locale__property__id": "ID", "@sage/xtrem-system/nodes__locale__property__isDefaultLocale": "Standardgebietsschema", "@sage/xtrem-system/nodes__locale__property__isLanguageMasterLocale": "Sprache Hauptgebietsschema", "@sage/xtrem-system/nodes__locale__property__language": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__site__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__site__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__site__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__site__node_name": "<PERSON><PERSON>", "@sage/xtrem-system/nodes__site__property__description": "Bezeichnung", "@sage/xtrem-system/nodes__site__property__id": "ID", "@sage/xtrem-system/nodes__site__property__isActive": "Aktiv", "@sage/xtrem-system/nodes__site__property__legalCompany": "Unternehmen", "@sage/xtrem-system/nodes__site__property__linkedSites": "Verknüpfte Standorte", "@sage/xtrem-system/nodes__site__property__name": "Name", "@sage/xtrem-system/nodes__sys_changelog__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_changelog__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__sys_changelog__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_changelog__node_name": "Änderungsprotokoll System", "@sage/xtrem-system/nodes__sys_changelog__property__changeDate": "Änderungsdatum", "@sage/xtrem-system/nodes__sys_changelog__property__hash": "Hash", "@sage/xtrem-system/nodes__sys_changelog__property__message": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__sys_client_notification__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_client_notification__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__sys_client_notification__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_client_notification__node_name": "Benachrichtigung System-Client", "@sage/xtrem-system/nodes__sys_client_notification__property__actions": "Aktionen", "@sage/xtrem-system/nodes__sys_client_notification__property__description": "Bezeichnung", "@sage/xtrem-system/nodes__sys_client_notification__property__icon": "Symbol", "@sage/xtrem-system/nodes__sys_client_notification__property__isRead": "Is<PERSON>", "@sage/xtrem-system/nodes__sys_client_notification__property__level": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__sys_client_notification__property__recipient": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__sys_client_notification__property__shouldDisplayToast": "Soll Toast anzeigen", "@sage/xtrem-system/nodes__sys_client_notification__property__title": "Titel", "@sage/xtrem-system/nodes__sys_client_notification_action__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_client_notification_action__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__sys_client_notification_action__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_client_notification_action__node_name": "Aktion Benachrichtigung System-Client", "@sage/xtrem-system/nodes__sys_client_notification_action__property__clientNotifications": "Client-Benachrichtigungen", "@sage/xtrem-system/nodes__sys_client_notification_action__property__link": "Verknüpfung", "@sage/xtrem-system/nodes__sys_client_notification_action__property__style": "Stil", "@sage/xtrem-system/nodes__sys_client_notification_action__property__title": "Titel", "@sage/xtrem-system/nodes__sys_client_user_settings__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_client_user_settings__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__sys_client_user_settings__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_client_user_settings__node_name": "Einstellungen Benutzer System-Client", "@sage/xtrem-system/nodes__sys_client_user_settings__property__content": "Inhalt", "@sage/xtrem-system/nodes__sys_client_user_settings__property__description": "Bezeichnung", "@sage/xtrem-system/nodes__sys_client_user_settings__property__elementId": "ID Element", "@sage/xtrem-system/nodes__sys_client_user_settings__property__isSelected": "Ist ausgewählt", "@sage/xtrem-system/nodes__sys_client_user_settings__property__screenId": "ID Maske", "@sage/xtrem-system/nodes__sys_client_user_settings__property__title": "Titel", "@sage/xtrem-system/nodes__sys_client_user_settings__property__user": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__sys_csv_checksum__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_csv_checksum__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__sys_csv_checksum__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_csv_checksum__node_name": "CSV-Prüfsumme System", "@sage/xtrem-system/nodes__sys_csv_checksum__property__checksum": "Prü<PERSON>umme", "@sage/xtrem-system/nodes__sys_csv_checksum__property__factoryName": "Standardname", "@sage/xtrem-system/nodes__sys_csv_checksum__property__tenantId": "Tenant-ID", "@sage/xtrem-system/nodes__sys_custom_record__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_custom_record__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__sys_custom_record__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_custom_record__node_name": "Benutzerdefinierter Datensatz System", "@sage/xtrem-system/nodes__sys_custom_record__property__bundleId": "Bundle-ID", "@sage/xtrem-system/nodes__sys_custom_record__property__factoryName": "Standardname", "@sage/xtrem-system/nodes__sys_custom_sql_history__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_custom_sql_history__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__sys_custom_sql_history__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_custom_sql_history__node_name": "Systembezogener SQL-Verlauf", "@sage/xtrem-system/nodes__sys_custom_sql_history__property__dryRun": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__sys_custom_sql_history__property__endDateTime": "Datum / Uhrzeit Ende", "@sage/xtrem-system/nodes__sys_custom_sql_history__property__result": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__sys_custom_sql_history__property__scriptContent": "Skriptinhalt", "@sage/xtrem-system/nodes__sys_custom_sql_history__property__scriptPath": "Skriptpfad", "@sage/xtrem-system/nodes__sys_custom_sql_history__property__startDateTime": "Datum / Uhrzeit Start", "@sage/xtrem-system/nodes__sys_customer__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_customer__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__sys_customer__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_customer__node_name": "Kunde System", "@sage/xtrem-system/nodes__sys_customer__property__customerId": "Kunden-ID", "@sage/xtrem-system/nodes__sys_customer__property__name": "Name", "@sage/xtrem-system/nodes__sys_data_validation_report__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_data_validation_report__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__sys_data_validation_report__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_data_validation_report__node_name": "Report Freigabe Systemdaten", "@sage/xtrem-system/nodes__sys_data_validation_report__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__sys_data_validation_report_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_data_validation_report_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__sys_data_validation_report_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_data_validation_report_line__node_name": "Reportzeile Freigabe Systemdaten", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__extraInfo": "Zusätzliche Info", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__message": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__nodeId": "Node-ID", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__nodeName": "Node-Name", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__path": "Pfad", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__report": "Report", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__severity": "Dringlichkeit", "@sage/xtrem-system/nodes__sys_device_token__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_device_token__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__sys_device_token__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_device_token__mutation__createDeviceToken": "Geräte-Token erstellen", "@sage/xtrem-system/nodes__sys_device_token__mutation__createDeviceToken__failed": "Geräte-Token erstellen fehlgeschlagen.", "@sage/xtrem-system/nodes__sys_device_token__mutation__deleteDeviceToken": "Geräte-Token löschen", "@sage/xtrem-system/nodes__sys_device_token__mutation__deleteDeviceToken__failed": "Geräte-Token löschen fehlgeschlagen.", "@sage/xtrem-system/nodes__sys_device_token__mutation__deleteDeviceToken__parameter__tokenId": "Token-ID", "@sage/xtrem-system/nodes__sys_device_token__node_name": "System-Geräte-Token", "@sage/xtrem-system/nodes__sys_device_token__property__expiration": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__sys_device_token__property__loginTestUrl": "Test-Anmelde-URL", "@sage/xtrem-system/nodes__sys_device_token__property__loginUrl": "Anmelde-URL", "@sage/xtrem-system/nodes__sys_device_token__property__name": "Name", "@sage/xtrem-system/nodes__sys_device_token__property__tokenId": "Token-ID", "@sage/xtrem-system/nodes__sys_note__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_note__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__sys_note__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_note__node_name": "Systemnotiz", "@sage/xtrem-system/nodes__sys_note__property__content": "Inhalt", "@sage/xtrem-system/nodes__sys_note__property__title": "Titel", "@sage/xtrem-system/nodes__sys_note_association__node_name": "Verbindung Systemnotiz", "@sage/xtrem-system/nodes__sys_note_association__property__note": "Notiz", "@sage/xtrem-system/nodes__sys_note_association__property__sourceNodeId": "ID Ursprungsnode", "@sage/xtrem-system/nodes__sys_note_association__property__sourceNodeName": "Name Ursprungsnode", "@sage/xtrem-system/nodes__sys_pack_allocation__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_pack_allocation__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__sys_pack_allocation__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_pack_allocation__mutation__activate": "Aktivieren", "@sage/xtrem-system/nodes__sys_pack_allocation__mutation__activate__failed": "Aktivieren fehlgeschlagen.", "@sage/xtrem-system/nodes__sys_pack_allocation__mutation__activate__parameter__packageId": "Paket-ID", "@sage/xtrem-system/nodes__sys_pack_allocation__mutation__deactivate": "Deaktivieren", "@sage/xtrem-system/nodes__sys_pack_allocation__mutation__deactivate__failed": "Deaktivieren fehlgeschlagen.", "@sage/xtrem-system/nodes__sys_pack_allocation__mutation__deactivate__parameter__packageId": "Paket-ID", "@sage/xtrem-system/nodes__sys_pack_allocation__node_name": "Paketzuweisung System", "@sage/xtrem-system/nodes__sys_pack_allocation__property__isActivable": "Aktivierbar", "@sage/xtrem-system/nodes__sys_pack_allocation__property__isActive": "Aktiv", "@sage/xtrem-system/nodes__sys_pack_allocation__property__package": "<PERSON><PERSON>", "@sage/xtrem-system/nodes__sys_pack_allocation__property__status": "Status", "@sage/xtrem-system/nodes__sys_pack_version__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_pack_version__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__sys_pack_version__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_pack_version__node_name": "Paketversion System", "@sage/xtrem-system/nodes__sys_pack_version__property__isBundle": "Bundle", "@sage/xtrem-system/nodes__sys_pack_version__property__isHidden": "Verborgen", "@sage/xtrem-system/nodes__sys_pack_version__property__isReleased": "Freigegeben", "@sage/xtrem-system/nodes__sys_pack_version__property__isUpgradeBundle": "Upgrade Bundle", "@sage/xtrem-system/nodes__sys_pack_version__property__name": "Name", "@sage/xtrem-system/nodes__sys_pack_version__property__sqlSchemaVersion": "SQL-Schemaversion", "@sage/xtrem-system/nodes__sys_pack_version__property__version": "Version", "@sage/xtrem-system/nodes__sys_patch_history__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_patch_history__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__sys_patch_history__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_patch_history__node_name": "Patch-Verlauf System", "@sage/xtrem-system/nodes__sys_patch_history__property__packageName": "Paketname", "@sage/xtrem-system/nodes__sys_patch_history__property__patchName": "Patch-Name", "@sage/xtrem-system/nodes__sys_patch_history__property__result": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__sys_patch_history__property__version": "Version", "@sage/xtrem-system/nodes__sys_service_option__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_service_option__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__sys_service_option__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_service_option__node_name": "Dienstoption System", "@sage/xtrem-system/nodes__sys_service_option__property__childServiceOptions": "Untergeordnete Dienstoptionen", "@sage/xtrem-system/nodes__sys_service_option__property__description": "Bezeichnung", "@sage/xtrem-system/nodes__sys_service_option__property__isActiveByDefault": "Standardmäßig aktiv", "@sage/xtrem-system/nodes__sys_service_option__property__isHidden": "Verborgen", "@sage/xtrem-system/nodes__sys_service_option__property__optionName": "Optionsname", "@sage/xtrem-system/nodes__sys_service_option__property__package": "<PERSON><PERSON>", "@sage/xtrem-system/nodes__sys_service_option__property__parentServiceOptions": "Übergeordnete Dienstoptionen", "@sage/xtrem-system/nodes__sys_service_option__property__status": "Status", "@sage/xtrem-system/nodes__sys_service_option_state__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_service_option_state__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__sys_service_option_state__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_service_option_state__node_name": "Status Dienstoption System", "@sage/xtrem-system/nodes__sys_service_option_state__property__imageStatus": "Status Bild", "@sage/xtrem-system/nodes__sys_service_option_state__property__isActivable": "Aktivierbar", "@sage/xtrem-system/nodes__sys_service_option_state__property__isActive": "Aktiv", "@sage/xtrem-system/nodes__sys_service_option_state__property__isAvailable": "Verfügbar", "@sage/xtrem-system/nodes__sys_service_option_state__property__isHiddenByConfig": "Standardmäßig verborgen", "@sage/xtrem-system/nodes__sys_service_option_state__property__isPackageActive": "Aktives Paket", "@sage/xtrem-system/nodes__sys_service_option_state__property__isReadyToUse": "Bereit zur Verwendung", "@sage/xtrem-system/nodes__sys_service_option_state__property__serviceOption": "Dienstoption", "@sage/xtrem-system/nodes__sys_service_option_to_service_option__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_service_option_to_service_option__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__sys_service_option_to_service_option__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_service_option_to_service_option__node_name": "Dienstoption zu Dienstoption System", "@sage/xtrem-system/nodes__sys_service_option_to_service_option__property__childServiceOption": "Untergeordnete Dienstoption", "@sage/xtrem-system/nodes__sys_service_option_to_service_option__property__parentServiceOption": "Übergeordnete Dienstoption", "@sage/xtrem-system/nodes__sys_tag__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_tag__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__sys_tag__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_tag__node_name": "System-Tag", "@sage/xtrem-system/nodes__sys_tag__property__color": "Color", "@sage/xtrem-system/nodes__sys_tag__property__description": "Bezeichnung", "@sage/xtrem-system/nodes__sys_tag__property__name": "Name", "@sage/xtrem-system/nodes__sys_tenant__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_tenant__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__sys_tenant__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_tenant__mutation__resetTenantDocuments": "Dokumente zum Zurücksetzen des Tenants", "@sage/xtrem-system/nodes__sys_tenant__mutation__resetTenantDocuments__failed": "Dokumente zum Zurücksetzen des Tenants fehlgeschlagen.", "@sage/xtrem-system/nodes__sys_tenant__mutation__resetTenantDocuments__parameter__tenantId": "Tenant-ID", "@sage/xtrem-system/nodes__sys_tenant__node_name": "System-Tenant", "@sage/xtrem-system/nodes__sys_tenant__property__customer": "Kunde", "@sage/xtrem-system/nodes__sys_tenant__property__directoryName": "Verzeichnisname", "@sage/xtrem-system/nodes__sys_tenant__property__name": "Name", "@sage/xtrem-system/nodes__sys_tenant__property__tenantId": "Tenant-ID", "@sage/xtrem-system/nodes__sys_tenant__query__getTenantInformation": "Tenant-<PERSON><PERSON> a<PERSON>", "@sage/xtrem-system/nodes__sys_tenant__query__getTenantInformation__failed": "Tenant-Informationen abrufen fehlgeschlagen.", "@sage/xtrem-system/nodes__sys_tenant__tenant_directory_name_should_be_in_kebab_case": "Verwenden Sie für das Tenant-Verzeichnis die Kebab-Case-Schreibweise (mit Bindestrich).", "@sage/xtrem-system/nodes__sys_tenant__tenant_name_characters_not_authorized": "Der Tenant-Name enthält nicht zulässige Zeichen.", "@sage/xtrem-system/nodes__sys_upgrade__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_upgrade__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__sys_upgrade__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_upgrade__node_name": "System-Upgrade", "@sage/xtrem-system/nodes__sys_upgrade__property__bundleId": "Bundle-ID", "@sage/xtrem-system/nodes__sys_upgrade__property__managedItems": "Verwaltete Elemente", "@sage/xtrem-system/nodes__sys_vendor__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__sys_vendor__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__sys_vendor__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__sys_vendor__node_name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__sys_vendor__property__description": "Bezeichnung", "@sage/xtrem-system/nodes__sys_vendor__property__name": "Name", "@sage/xtrem-system/nodes__sys-client-user-settings": "<PERSON><PERSON> eine Auswahl möglich.", "@sage/xtrem-system/nodes__sys-layer__name-must-be-kebab-case": "Der Name muss mit Kebab-Case-Schreibweise geschrieben werden.", "@sage/xtrem-system/nodes__user__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__user__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__user__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__user__cannot_modify_own_rights": "<PERSON>e sind nicht dazu berechtigt, Ihre eigenen Administratorrechte zu ändern.", "@sage/xtrem-system/nodes__user__mutation__logPageVisit": "<PERSON><PERSON>ch <PERSON>", "@sage/xtrem-system/nodes__user__mutation__logPageVisit__failed": "Besuch Protokollseite fehlgeschlagen.", "@sage/xtrem-system/nodes__user__mutation__logPageVisit__parameter__path": "Pfad", "@sage/xtrem-system/nodes__user__mutation__sendWelcomeMail": "Willkommens-E-Mail senden", "@sage/xtrem-system/nodes__user__mutation__sendWelcomeMail__failed": "Willkommens-E-Mail senden fehlgeschlagen.", "@sage/xtrem-system/nodes__user__mutation__sendWelcomeMail__parameter__isAdmin": "Admin", "@sage/xtrem-system/nodes__user__mutation__sendWelcomeMail__parameter__users": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__user__mutation__setDemoPersona": "<PERSON><PERSON>-<PERSON><PERSON> setzen", "@sage/xtrem-system/nodes__user__mutation__setDemoPersona__failed": "Demo-<PERSON><PERSON> setzen fehlgeschlagen.", "@sage/xtrem-system/nodes__user__mutation__setDemoPersona__parameter__email": "E-Mail", "@sage/xtrem-system/nodes__user__mutation__updateClientSettings": "Client-Einstellungen aktualisieren", "@sage/xtrem-system/nodes__user__mutation__updateClientSettings__failed": "Client-Einstellungen aktualisieren fehlgeschlagen.", "@sage/xtrem-system/nodes__user__mutation__updateClientSettings__parameter__clientSettings": "Client-Einstellungen", "@sage/xtrem-system/nodes__user__node_name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__user__only_admin_can_add_admin": "<PERSON>ur ein Administrator kann die Administratorrechte eines anderen Benutzers hinzufügen oder ändern.", "@sage/xtrem-system/nodes__user__property__clientSettings": "Client-Einstellungen", "@sage/xtrem-system/nodes__user__property__displayName": "Anzeigename", "@sage/xtrem-system/nodes__user__property__email": "E-Mail", "@sage/xtrem-system/nodes__user__property__firstName": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__user__property__isActive": "Aktiv", "@sage/xtrem-system/nodes__user__property__isAdministrator": "Administrator", "@sage/xtrem-system/nodes__user__property__isApiUser": "API-<PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__user__property__isDemoPersona": "Demo-Persona", "@sage/xtrem-system/nodes__user__property__isFirstAdminUser": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__user__property__isOperatorUser": "<PERSON><PERSON>-<PERSON><PERSON>", "@sage/xtrem-system/nodes__user__property__lastName": "Nachname", "@sage/xtrem-system/nodes__user__property__navigation": "Navigation", "@sage/xtrem-system/nodes__user__property__operatorCode": "Anwender-Code", "@sage/xtrem-system/nodes__user__property__operatorId": "Anwender-ID", "@sage/xtrem-system/nodes__user__property__photo": "Foto", "@sage/xtrem-system/nodes__user__property__preferences": "Einstellungen", "@sage/xtrem-system/nodes__user__property__setupId": "ID Einstellungen", "@sage/xtrem-system/nodes__user__property__userType": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__user__query__demoPersonas": "Demo-Personas", "@sage/xtrem-system/nodes__user__query__demoPersonas__failed": "Demo-Personas fehlgeschlagen.", "@sage/xtrem-system/nodes__user_navigation__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__user_navigation__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__user_navigation__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__user_navigation__node_name": "Benutzernavigation", "@sage/xtrem-system/nodes__user_navigation__property__bookmarks": "Lesezeichen", "@sage/xtrem-system/nodes__user_navigation__property__history": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__user_navigation__property__user": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__user_preferences__asyncMutation__asyncExport": "Export", "@sage/xtrem-system/nodes__user_preferences__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-system/nodes__user_preferences__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-system/nodes__user_preferences__node_name": "Benutzerpräferenzen", "@sage/xtrem-system/nodes__user_preferences__property__importExportDateFormat": "Datumsformat Import/Export", "@sage/xtrem-system/nodes__user_preferences__property__importExportDelimiter": "Trennzeichen Import/Export", "@sage/xtrem-system/nodes__user_preferences__property__isExternal": "Ist extern", "@sage/xtrem-system/nodes__user_preferences__property__isWelcomeMailSent": "Willkommens-E-Mail gesendet", "@sage/xtrem-system/nodes__user_preferences__property__user": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/nodes__user_preferences__query__activeServiceOptions": "Aktive Dienstoptionen", "@sage/xtrem-system/nodes__user_preferences__query__activeServiceOptions__failed": "Aktive Dienstoptionen fehlgeschlagen.", "@sage/xtrem-system/package__name": "System", "@sage/xtrem-system/package-activated": "Paket aktiviert", "@sage/xtrem-system/package-deactivated": "<PERSON><PERSON>", "@sage/xtrem-system/pages__client_user_settings_edit____title": "Ansicht erstellen", "@sage/xtrem-system/pages__client_user_settings_edit__generalSection____title": "Allgemein", "@sage/xtrem-system/pages__client_user_settings_edit__save____title": "Speichern", "@sage/xtrem-system/pages__client_user_settings_edit__title____title": "Name", "@sage/xtrem-system/pages__client_user_settings_list____title": "Ansichten verwalten", "@sage/xtrem-system/pages__client_user_settings_list__clientSettings____columns__title__title": "Name", "@sage/xtrem-system/pages__client_user_settings_list__clientSettings____dropdownActions__title": "Entfernen", "@sage/xtrem-system/pages__client_user_settings_list__clientSettings____dropdownActions__title__remove": "Entfernen", "@sage/xtrem-system/pages__client_user_settings_list__generalSection____title": "Allgemein", "@sage/xtrem-system/pages__client_user_settings_list__save____title": "Speichern", "@sage/xtrem-system/pages__delete_page_dialog_content": "<PERSON><PERSON> sind dabei, diesen Datensatz zu löschen.", "@sage/xtrem-system/pages__delete_page_dialog_title": "Löschen bestätigen", "@sage/xtrem-system/pages__pack_allocation____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-system/pages__pack_allocation____navigationPanel__optionsMenu__title__2": "<PERSON><PERSON>", "@sage/xtrem-system/pages__pack_allocation____navigationPanel__optionsMenu__title__3": "Bundles", "@sage/xtrem-system/pages__pack_allocation____title": "Paketaktivierung", "@sage/xtrem-system/pages__pack_allocation__closePage____title": "Schließen", "@sage/xtrem-system/pages__pack_allocation__generalSection____title": "Allgemein", "@sage/xtrem-system/pages__pack_allocation__isActivable____title": "Aktivierbar", "@sage/xtrem-system/pages__pack_allocation__isActive____title": "Aktiv", "@sage/xtrem-system/pages__pack_allocation__package____title": "<PERSON><PERSON>", "@sage/xtrem-system/pages__pack_allocation__version____title": "Version", "@sage/xtrem-system/pages__pack_version____navigationPanel__optionsMenu__title": "Bundles", "@sage/xtrem-system/pages__pack_version____navigationPanel__optionsMenu__title__2": "<PERSON><PERSON>", "@sage/xtrem-system/pages__pack_version____navigationPanel__optionsMenu__title__3": "Alle", "@sage/xtrem-system/pages__pack_version____title": "Bundle-Aktivierung", "@sage/xtrem-system/pages__pack_version__closePage____title": "Schließen", "@sage/xtrem-system/pages__pack_version__generalSection____title": "Allgemein", "@sage/xtrem-system/pages__pack_version__isBundle____title": "Bundle", "@sage/xtrem-system/pages__pack_version__package____title": "<PERSON><PERSON>", "@sage/xtrem-system/pages__pack_version__version____title": "Version", "@sage/xtrem-system/pages__service_option_state____navigationPanel__dropdownActions__title": "Aktivieren", "@sage/xtrem-system/pages__service_option_state____navigationPanel__dropdownActions__title__2": "Deaktivieren", "@sage/xtrem-system/pages__service_option_state____navigationPanel__listItem__line_4__title": "Status", "@sage/xtrem-system/pages__service_option_state____navigationPanel__listItem__line2__title": "Bezeichnung", "@sage/xtrem-system/pages__service_option_state____navigationPanel__listItem__line3__title": "Aktiv", "@sage/xtrem-system/pages__service_option_state____navigationPanel__listItem__line4__title": "Status", "@sage/xtrem-system/pages__service_option_state____navigationPanel__listItem__title__title": "Name", "@sage/xtrem-system/pages__service_option_state____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-system/pages__service_option_state____objectTypePlural": "Dienstoptionen", "@sage/xtrem-system/pages__service_option_state____objectTypeSingular": "Dienstoption", "@sage/xtrem-system/pages__service_option_state____title": "Dienstoptionen", "@sage/xtrem-system/pages__service_option_state__cancel____title": "Abbrechen", "@sage/xtrem-system/pages__service_option_state__closePage____title": "Schließen", "@sage/xtrem-system/pages__service_option_state__description____title": "Bezeichnung", "@sage/xtrem-system/pages__service_option_state__generalSection____title": "Allgemein", "@sage/xtrem-system/pages__service_option_state__isActivable____title": "Verfügbar", "@sage/xtrem-system/pages__service_option_state__isActive____title": "Aktiv", "@sage/xtrem-system/pages__service_option_state__isAvailable____title": "Verfügbar", "@sage/xtrem-system/pages__service_option_state__isHiddenByConfig____title": "Aktiv", "@sage/xtrem-system/pages__service_option_state__optionName____title": "Name", "@sage/xtrem-system/pages__service_option_state__reset_tenant_data_done": "Zurücksetzen der Tenant-Daten durchgeführt", "@sage/xtrem-system/pages__service_option_state__save____title": "Speichern", "@sage/xtrem-system/pages__service_option_state__serviceOption____title": "Dienstoption", "@sage/xtrem-system/pages__service_option_state__status____title": "Status", "@sage/xtrem-system/pages__sys_changelog____navigationPanel__listItem__line2__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__sys_changelog____navigationPanel__listItem__line3__title": "Zeitstempel Änderung", "@sage/xtrem-system/pages__sys_changelog____navigationPanel__listItem__title__title": "Hash", "@sage/xtrem-system/pages__sys_changelog____title": "Änderungsprotokoll", "@sage/xtrem-system/pages__sys_device_token____navigationPanel__listItem__tokenId__title": "ID", "@sage/xtrem-system/pages__sys_device_token____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-system/pages__sys_device_token____title": "Geräte-Token", "@sage/xtrem-system/pages__sys_device_token__createDeviceToken____title": "Speichern", "@sage/xtrem-system/pages__sys_device_token__deleteDeviceToken____title": "Löschen", "@sage/xtrem-system/pages__sys_device_token__deviceTokenBlock____title": "Informationen Geräte-Token", "@sage/xtrem-system/pages__sys_device_token__expiration____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__sys_device_token__loginTestUrl____title": "Testlink Gerät", "@sage/xtrem-system/pages__sys_device_token__loginUrl____title": "Anmeldelink Gerät", "@sage/xtrem-system/pages__sys_device_token__name____title": "Name", "@sage/xtrem-system/pages__sys_device_token__tokenId____title": "Token", "@sage/xtrem-system/pages__sys_layer____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-system/pages__sys_layer____title": "Datenschicht", "@sage/xtrem-system/pages__sys_layer___id____title": "ID", "@sage/xtrem-system/pages__sys_layer__dataLayerInformation____title": "<PERSON><PERSON> Datenschicht", "@sage/xtrem-system/pages__sys_layer__deleteLayerAction____title": "Löschen", "@sage/xtrem-system/pages__sys_layer__description____title": "Bezeichnung", "@sage/xtrem-system/pages__sys_layer__exportLayer____title": "Schicht exportieren", "@sage/xtrem-system/pages__sys_layer__generalSection____title": "Allgemein", "@sage/xtrem-system/pages__sys_layer__name____title": "Name <PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__sys_pack_allocation____navigationPanel__listItem__lineRight__title": "Version", "@sage/xtrem-system/pages__sys_pack_allocation____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-system/pages__sys_pack_allocation____navigationPanel__optionsMenu__title__2": "<PERSON><PERSON>", "@sage/xtrem-system/pages__sys_pack_allocation____title": "Paketaktivierung", "@sage/xtrem-system/pages__sys_pack_allocation__closePage____title": "Schließen", "@sage/xtrem-system/pages__sys_pack_allocation__generalSection____title": "Allgemein", "@sage/xtrem-system/pages__sys_pack_allocation__isActivable____title": "Verfügbar", "@sage/xtrem-system/pages__sys_pack_allocation__isActive____title": "Aktiv", "@sage/xtrem-system/pages__sys_pack_allocation__package____title": "<PERSON><PERSON>", "@sage/xtrem-system/pages__sys_pack_allocation__version____title": "Version", "@sage/xtrem-system/pages__sys_tag____navigationPanel__listItem__line2__title": "Bezeichnung", "@sage/xtrem-system/pages__sys_tag____navigationPanel__listItem__title__title": "Name", "@sage/xtrem-system/pages__sys_tag____title": "Tag", "@sage/xtrem-system/pages__sys_tag__description____title": "Bezeichnung", "@sage/xtrem-system/pages__sys_tag__generalSection____title": "Allgemein", "@sage/xtrem-system/pages__sys_tag__name____title": "Name", "@sage/xtrem-system/pages__tenant_information____title": "Tenant-<PERSON><PERSON>", "@sage/xtrem-system/pages__tenant_information__cancel_reset_tenant_data_button_text": "Abbrechen", "@sage/xtrem-system/pages__tenant_information__confirm_reset_tenant_data_button_text": "Zurücksetzen der Tenant-Daten bestätigen", "@sage/xtrem-system/pages__tenant_information__generalSection____title": "Allgemein", "@sage/xtrem-system/pages__tenant_information__reset_tenant_data_button_text": "Tenant-<PERSON><PERSON>", "@sage/xtrem-system/pages__tenant_information__resetDataDescription____content": "Durch Zurücksetzen der Tenant-Daten werden alle Bewegungen, wie Verkaufsaufträge, Bestellungen, Bestandsebene und Bewegungen gelöscht.\nDieser Vorgang kann nicht rückgängig gemacht werden. Sind Sie sicher?", "@sage/xtrem-system/pages__tenant_information__resetTenantId____helperText": "Bitte erfassen Sie die zu löschende Tenant-ID", "@sage/xtrem-system/pages__tenant_information__resetTenantId____title": "Tenant-ID bestätigen", "@sage/xtrem-system/pages__tenant_information__resetTenantSection____title": "Z<PERSON>ücksetzen", "@sage/xtrem-system/pages__tenant_information__sumologic_button_text": "Sumo Logic-Protokolle anzeigen", "@sage/xtrem-system/pages__tenant_information__sumologicLink____helperText": "<PERSON><PERSON> müssen sich bei Sumo Logic anmelden, bevor Sie die Protokolle sehen können.", "@sage/xtrem-system/pages__tenant_information__tenantId____title": "Tenant-ID", "@sage/xtrem-system/pages__tenant_information__tenantVersion____title": "Tenant-Version", "@sage/xtrem-system/pages__user____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-system/pages__user____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__user___id____title": "ID", "@sage/xtrem-system/pages__user__email____title": "E-Mail", "@sage/xtrem-system/pages__user__firstName____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__user__generalSection____title": "Allgemein", "@sage/xtrem-system/pages__user__idBlock____title": "ID", "@sage/xtrem-system/pages__user__isActive____title": "Aktiv", "@sage/xtrem-system/pages__user__lastName____title": "Nachname", "@sage/xtrem-system/pages__user__userInformationBlock____title": "Benutzerinformationen", "@sage/xtrem-system/pages__user__userPhotoBlock____title": "Foto", "@sage/xtrem-system/pages-confirm-cancel": "Abbrechen", "@sage/xtrem-system/pages-confirm-delete": "Löschen", "@sage/xtrem-system/permission__create__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/permission__delete__name": "Löschen", "@sage/xtrem-system/permission__get_tenant_information__name": "Tenant-<PERSON><PERSON> a<PERSON>", "@sage/xtrem-system/permission__read__name": "<PERSON><PERSON>", "@sage/xtrem-system/permission__reset_tenant_documents__name": "Dokumente zum Zurücksetzen des Tenants", "@sage/xtrem-system/permission__update__name": "Aktualisieren", "@sage/xtrem-system/productKey": "Produktwert", "@sage/xtrem-system/productKey2": "Produktwert {{num}}", "@sage/xtrem-system/reserved-api-user-email": "Die E-Mail-Adresse {{value}} ist für API-Benutzer reserviert.", "@sage/xtrem-system/reserved-operator-user-email": "Das Format der Anwender-E-Mail-Adresse ist für Anwender-Benutzer reserviert.", "@sage/xtrem-system/reset-tenant-data-not-admin": "Nur Administratoren können Tenant-Daten zurücksetzen", "@sage/xtrem-system/reset-tenant-data-not-admin-with-id": "<PERSON><PERSON><PERSON> {{userId}} hat versucht, einen Tenant zurückzusetzen", "@sage/xtrem-system/reset-tenant-data-tenant-id-mismatched": "Die Tenant-ID {{tenantId}} entspricht nicht dem aktuellen Tenant ({{currentId}}).", "@sage/xtrem-system/service_options__changelog__name": "Änderungsprotokoll", "@sage/xtrem-system/service_options__dev_tools__name": "Entwicklungswerkzeuge", "@sage/xtrem-system/service_options__is_demo_tenant__name": "Ist Demo-Tenant", "@sage/xtrem-system/service_options__sys_device_token__name": "System-Geräte-Token", "@sage/xtrem-system/service_options__sys_tag__name": "System-Tag", "@sage/xtrem-system/statusIcon": "Status", "@sage/xtrem-system/sys_device_token_creation_error": "Erstellung des Geräte-Token fehlgeschlagen: {{errorMessage}}", "@sage/xtrem-system/sys_device_token_deletion_error": "Löschen des Geräte-Token fehlgeschlagen: {{errorMessage}}", "@sage/xtrem-system/sys_device_token_device_token_already_created": "<PERSON>ür das aktuelle Gerät wurde bereits ein Token erstellt.", "@sage/xtrem-system/system-user-forbidden": "Systembenutzer können nicht erstellt oder aktualisiert werden.", "@sage/xtrem-system/update-confirmation": "Datensatz aktualisiert", "@sage/xtrem-system/widgets__system_version____title": "Systemversion"}