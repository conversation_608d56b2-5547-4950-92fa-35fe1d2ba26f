{"@sage/xtrem-system/activity__company__name": "", "@sage/xtrem-system/activity__service_option_state__name": "", "@sage/xtrem-system/activity__site__name": "", "@sage/xtrem-system/activity__sys_device_token__name": "", "@sage/xtrem-system/activity__sys_note__name": "", "@sage/xtrem-system/activity__sys_tag__name": "", "@sage/xtrem-system/activity__tenant__name": "", "@sage/xtrem-system/bundle-activated": "Aktywowano pakiet", "@sage/xtrem-system/bundle-deactivated": "Dezaktywowano pakiet", "@sage/xtrem-system/cannot-deactivate-admin-demo-persona": "", "@sage/xtrem-system/confirm-activate-service-option-message": "", "@sage/xtrem-system/confirm-activate-service-option-title": "", "@sage/xtrem-system/confirm-deactivate-service-option-message": "", "@sage/xtrem-system/confirm-deactivate-service-option-title": "", "@sage/xtrem-system/create-confirmation": "Utworzono rekord", "@sage/xtrem-system/data_types__big_id__name": "", "@sage/xtrem-system/data_types__big_url__name": "", "@sage/xtrem-system/data_types__bundle_id_data_type__name": "", "@sage/xtrem-system/data_types__bundle_path_type__name": "", "@sage/xtrem-system/data_types__bundle_version_data_type__name": "", "@sage/xtrem-system/data_types__checksum_data_type__name": "", "@sage/xtrem-system/data_types__code__name": "", "@sage/xtrem-system/data_types__code_data_type__name": "", "@sage/xtrem-system/data_types__colored_element_enum__name": "", "@sage/xtrem-system/data_types__company__name": "", "@sage/xtrem-system/data_types__content_length_decimal_data_type__name": "", "@sage/xtrem-system/data_types__decimal__name": "", "@sage/xtrem-system/data_types__default_decimal_data_type__name": "", "@sage/xtrem-system/data_types__description__name": "", "@sage/xtrem-system/data_types__description_array_data_type__name": "", "@sage/xtrem-system/data_types__description_data_type__name": "", "@sage/xtrem-system/data_types__email__name": "", "@sage/xtrem-system/data_types__folder__name": "", "@sage/xtrem-system/data_types__http_base_url__name": "", "@sage/xtrem-system/data_types__http_url__name": "", "@sage/xtrem-system/data_types__icon__name": "", "@sage/xtrem-system/data_types__id__name": "", "@sage/xtrem-system/data_types__locale_datatype__name": "", "@sage/xtrem-system/data_types__locale_id_data_type__name": "", "@sage/xtrem-system/data_types__localized_description__name": "", "@sage/xtrem-system/data_types__localized_name__name": "", "@sage/xtrem-system/data_types__localized_short_description__name": "", "@sage/xtrem-system/data_types__localized_title__name": "", "@sage/xtrem-system/data_types__mime_type__name": "", "@sage/xtrem-system/data_types__name__name": "", "@sage/xtrem-system/data_types__nanoid__name": "", "@sage/xtrem-system/data_types__note_content_data_type__name": "", "@sage/xtrem-system/data_types__notification_data_data_type__name": "", "@sage/xtrem-system/data_types__passphrase__name": "", "@sage/xtrem-system/data_types__password__name": "", "@sage/xtrem-system/data_types__quantity_data_type__name": "", "@sage/xtrem-system/data_types__rate__name": "", "@sage/xtrem-system/data_types__rgb_color__name": "", "@sage/xtrem-system/data_types__service_option_status_enum__name": "", "@sage/xtrem-system/data_types__setup_id_data_type__name": "", "@sage/xtrem-system/data_types__short_description__name": "", "@sage/xtrem-system/data_types__single_character__name": "", "@sage/xtrem-system/data_types__site__name": "", "@sage/xtrem-system/data_types__sys_client_notification_action_style_enum__name": "", "@sage/xtrem-system/data_types__sys_client_notification_level_enum__name": "", "@sage/xtrem-system/data_types__title__name": "", "@sage/xtrem-system/data_types__url__name": "", "@sage/xtrem-system/data_types__user__name": "", "@sage/xtrem-system/data_types__user_import_export_date_format_enum__name": "", "@sage/xtrem-system/data_types__user_type_enum__name": "", "@sage/xtrem-system/data_types__uuid__name": "", "@sage/xtrem-system/data_types__version__name": "", "@sage/xtrem-system/data-types/locale_datatype__invalid_locale": "Locale value is invalid: ({{locale}}).", "@sage/xtrem-system/data-types/rgb__invalid_rgb": "", "@sage/xtrem-system/data-types/url_datatype__invalid_protocol": "", "@sage/xtrem-system/data-types/url_datatype__invalid_url": "", "@sage/xtrem-system/delete-confirmation": "Us<PERSON>ę<PERSON> rekord", "@sage/xtrem-system/delete-dialog-content": "Usuniesz ten rekord. Kontynuować?", "@sage/xtrem-system/delete-dialog-title": "Potwierdź usuwanie", "@sage/xtrem-system/delete-user-forbidden": "<PERSON>e można usunąć użytkowników.", "@sage/xtrem-system/delete-view-warning-title": "", "@sage/xtrem-system/delete-view-warning-title-message": "", "@sage/xtrem-system/duplicate_first_admin": "An admin user is already defined. You cannot create another one.", "@sage/xtrem-system/enums__colored_element__backgroundColor": "Background color", "@sage/xtrem-system/enums__colored_element__borderColor": "Border color", "@sage/xtrem-system/enums__colored_element__textColor": "Text color", "@sage/xtrem-system/enums__service_option_status__experimental": "", "@sage/xtrem-system/enums__service_option_status__released": "", "@sage/xtrem-system/enums__service_option_status__workInProgress": "", "@sage/xtrem-system/enums__service_option_type__experimental": "Eksperymantalny", "@sage/xtrem-system/enums__service_option_type__released": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/enums__service_option_type__workInProgress": "Praca w toku", "@sage/xtrem-system/enums__sys_client_notification_action_style__link": "", "@sage/xtrem-system/enums__sys_client_notification_action_style__primary": "", "@sage/xtrem-system/enums__sys_client_notification_action_style__secondary": "", "@sage/xtrem-system/enums__sys_client_notification_action_style__tertiary": "", "@sage/xtrem-system/enums__sys_client_notification_level__approval": "", "@sage/xtrem-system/enums__sys_client_notification_level__error": "", "@sage/xtrem-system/enums__sys_client_notification_level__info": "", "@sage/xtrem-system/enums__sys_client_notification_level__success": "", "@sage/xtrem-system/enums__sys_client_notification_level__warning": "", "@sage/xtrem-system/enums__user_import_export_date_format__europeanDash": "dd-mm-yyyy", "@sage/xtrem-system/enums__user_import_export_date_format__europeanSlash": "dd/mm/yyyy", "@sage/xtrem-system/enums__user_import_export_date_format__isoDash": "yyyy-mm-dd", "@sage/xtrem-system/enums__user_import_export_date_format__isoSlash": "yyyy/mm/dd", "@sage/xtrem-system/enums__user_import_export_date_format__usDash": "mm-dd-yyyy", "@sage/xtrem-system/enums__user_import_export_date_format__usSlash": "mm/dd/yyyy", "@sage/xtrem-system/enums__user_type__application": "Application", "@sage/xtrem-system/enums__user_type__system": "System", "@sage/xtrem-system/error-packages-cannot-be": "<PERSON><PERSON> można dezaktywować pakietów.", "@sage/xtrem-system/error-packages-cannot-be-deactivated": "<PERSON><PERSON> można dezaktywować pakietów.", "@sage/xtrem-system/failed-to-save-view": "", "@sage/xtrem-system/invalid-admin-demo-persona": "", "@sage/xtrem-system/invalid-api-user-email": "The API user email address {{value}} is incorrect. Use this format: api-<devid>@localhost.domain", "@sage/xtrem-system/invalid-api-user-is-admin": "An API user cannot be an admin.", "@sage/xtrem-system/invalid-api-user-is-demo-persona": "An API user cannot be a demo persona.", "@sage/xtrem-system/invalid-email": "Incorrect email address: ({{value}})", "@sage/xtrem-system/invalid-operator-pin-code": "", "@sage/xtrem-system/invalid-operator-pincode-not-unique": "", "@sage/xtrem-system/invalid-operator-user-email": "", "@sage/xtrem-system/invalid-operator-user-is-admin": "", "@sage/xtrem-system/invalid-operator-user-is-demo-persona": "", "@sage/xtrem-system/is-active/node-id-combination-not-active": "Nieaktywny {{nodeName}} {{dataId}}", "@sage/xtrem-system/language-master-locale-different-from-default": "Główne ustawienia regionalne muszą odpowiadać domyślnym ustawieniom regionalnym dla tego samego języka.", "@sage/xtrem-system/menu_item__administration": "Administracja", "@sage/xtrem-system/menu_item__application-data": "Konfiguracja aplikacji", "@sage/xtrem-system/menu_item__bundle-activation": "Aktywacja pakietu", "@sage/xtrem-system/menu_item__integrations": "", "@sage/xtrem-system/menu_item__service-option": "<PERSON><PERSON><PERSON>i", "@sage/xtrem-system/menu_item__user": "Użytkownicy", "@sage/xtrem-system/menu_item__user-data": "Użytkownicy i bezpieczeństwo", "@sage/xtrem-system/nodes__company__asyncMutation__asyncExport": "", "@sage/xtrem-system/nodes__company__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-system/nodes__company__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-system/nodes__company__node_name": "Company", "@sage/xtrem-system/nodes__company__property__id": "ID", "@sage/xtrem-system/nodes__company__property__isActive": "Active", "@sage/xtrem-system/nodes__company__property__sites": "Sites", "@sage/xtrem-system/nodes__locale__asyncMutation__asyncExport": "", "@sage/xtrem-system/nodes__locale__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-system/nodes__locale__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-system/nodes__locale__node_name": "Locale", "@sage/xtrem-system/nodes__locale__property__id": "ID", "@sage/xtrem-system/nodes__locale__property__isDefaultLocale": "Default locale", "@sage/xtrem-system/nodes__locale__property__isLanguageMasterLocale": "Language of master locale", "@sage/xtrem-system/nodes__locale__property__language": "Language", "@sage/xtrem-system/nodes__site__asyncMutation__asyncExport": "", "@sage/xtrem-system/nodes__site__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-system/nodes__site__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-system/nodes__site__node_name": "Site", "@sage/xtrem-system/nodes__site__property__description": "Description", "@sage/xtrem-system/nodes__site__property__id": "ID", "@sage/xtrem-system/nodes__site__property__isActive": "Active", "@sage/xtrem-system/nodes__site__property__legalCompany": "Legal company", "@sage/xtrem-system/nodes__site__property__linkedSites": "Linked sites", "@sage/xtrem-system/nodes__site__property__name": "Name", "@sage/xtrem-system/nodes__sys_changelog__asyncMutation__asyncExport": "", "@sage/xtrem-system/nodes__sys_changelog__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-system/nodes__sys_changelog__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-system/nodes__sys_changelog__node_name": "", "@sage/xtrem-system/nodes__sys_changelog__property__changeDate": "", "@sage/xtrem-system/nodes__sys_changelog__property__hash": "", "@sage/xtrem-system/nodes__sys_changelog__property__message": "", "@sage/xtrem-system/nodes__sys_client_notification__asyncMutation__asyncExport": "", "@sage/xtrem-system/nodes__sys_client_notification__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-system/nodes__sys_client_notification__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-system/nodes__sys_client_notification__node_name": "", "@sage/xtrem-system/nodes__sys_client_notification__property__actions": "", "@sage/xtrem-system/nodes__sys_client_notification__property__description": "", "@sage/xtrem-system/nodes__sys_client_notification__property__icon": "", "@sage/xtrem-system/nodes__sys_client_notification__property__isRead": "", "@sage/xtrem-system/nodes__sys_client_notification__property__level": "", "@sage/xtrem-system/nodes__sys_client_notification__property__recipient": "", "@sage/xtrem-system/nodes__sys_client_notification__property__shouldDisplayToast": "", "@sage/xtrem-system/nodes__sys_client_notification__property__title": "", "@sage/xtrem-system/nodes__sys_client_notification_action__asyncMutation__asyncExport": "", "@sage/xtrem-system/nodes__sys_client_notification_action__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-system/nodes__sys_client_notification_action__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-system/nodes__sys_client_notification_action__node_name": "", "@sage/xtrem-system/nodes__sys_client_notification_action__property__clientNotifications": "", "@sage/xtrem-system/nodes__sys_client_notification_action__property__link": "", "@sage/xtrem-system/nodes__sys_client_notification_action__property__style": "", "@sage/xtrem-system/nodes__sys_client_notification_action__property__title": "", "@sage/xtrem-system/nodes__sys_client_user_settings__asyncMutation__asyncExport": "", "@sage/xtrem-system/nodes__sys_client_user_settings__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-system/nodes__sys_client_user_settings__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-system/nodes__sys_client_user_settings__node_name": "", "@sage/xtrem-system/nodes__sys_client_user_settings__property__content": "", "@sage/xtrem-system/nodes__sys_client_user_settings__property__description": "", "@sage/xtrem-system/nodes__sys_client_user_settings__property__elementId": "", "@sage/xtrem-system/nodes__sys_client_user_settings__property__isSelected": "", "@sage/xtrem-system/nodes__sys_client_user_settings__property__screenId": "", "@sage/xtrem-system/nodes__sys_client_user_settings__property__title": "", "@sage/xtrem-system/nodes__sys_client_user_settings__property__user": "", "@sage/xtrem-system/nodes__sys_csv_checksum__asyncMutation__asyncExport": "", "@sage/xtrem-system/nodes__sys_csv_checksum__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-system/nodes__sys_csv_checksum__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-system/nodes__sys_csv_checksum__node_name": "System CSV checksum", "@sage/xtrem-system/nodes__sys_csv_checksum__property__checksum": "Checksum", "@sage/xtrem-system/nodes__sys_csv_checksum__property__factoryName": "Factory name", "@sage/xtrem-system/nodes__sys_csv_checksum__property__tenantId": "Tenant ID", "@sage/xtrem-system/nodes__sys_custom_record__asyncMutation__asyncExport": "", "@sage/xtrem-system/nodes__sys_custom_record__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-system/nodes__sys_custom_record__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-system/nodes__sys_custom_record__node_name": "", "@sage/xtrem-system/nodes__sys_custom_record__property__bundleId": "Bundle ID", "@sage/xtrem-system/nodes__sys_custom_record__property__factoryName": "Factory name", "@sage/xtrem-system/nodes__sys_custom_sql_history__asyncMutation__asyncExport": "", "@sage/xtrem-system/nodes__sys_custom_sql_history__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-system/nodes__sys_custom_sql_history__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-system/nodes__sys_custom_sql_history__node_name": "", "@sage/xtrem-system/nodes__sys_custom_sql_history__property__dryRun": "", "@sage/xtrem-system/nodes__sys_custom_sql_history__property__endDateTime": "", "@sage/xtrem-system/nodes__sys_custom_sql_history__property__result": "", "@sage/xtrem-system/nodes__sys_custom_sql_history__property__scriptContent": "", "@sage/xtrem-system/nodes__sys_custom_sql_history__property__scriptPath": "", "@sage/xtrem-system/nodes__sys_custom_sql_history__property__startDateTime": "", "@sage/xtrem-system/nodes__sys_customer__asyncMutation__asyncExport": "", "@sage/xtrem-system/nodes__sys_customer__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-system/nodes__sys_customer__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-system/nodes__sys_customer__node_name": "System customer", "@sage/xtrem-system/nodes__sys_customer__property__customerId": "Customer ID", "@sage/xtrem-system/nodes__sys_customer__property__name": "Name", "@sage/xtrem-system/nodes__sys_data_validation_report__asyncMutation__asyncExport": "", "@sage/xtrem-system/nodes__sys_data_validation_report__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-system/nodes__sys_data_validation_report__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-system/nodes__sys_data_validation_report__node_name": "", "@sage/xtrem-system/nodes__sys_data_validation_report__property__lines": "", "@sage/xtrem-system/nodes__sys_data_validation_report_line__asyncMutation__asyncExport": "", "@sage/xtrem-system/nodes__sys_data_validation_report_line__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-system/nodes__sys_data_validation_report_line__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-system/nodes__sys_data_validation_report_line__node_name": "", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__extraInfo": "", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__message": "", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__nodeId": "", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__nodeName": "", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__path": "", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__report": "", "@sage/xtrem-system/nodes__sys_data_validation_report_line__property__severity": "", "@sage/xtrem-system/nodes__sys_device_token__asyncMutation__asyncExport": "", "@sage/xtrem-system/nodes__sys_device_token__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-system/nodes__sys_device_token__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-system/nodes__sys_device_token__mutation__createDeviceToken": "", "@sage/xtrem-system/nodes__sys_device_token__mutation__createDeviceToken__failed": "", "@sage/xtrem-system/nodes__sys_device_token__mutation__deleteDeviceToken": "", "@sage/xtrem-system/nodes__sys_device_token__mutation__deleteDeviceToken__failed": "", "@sage/xtrem-system/nodes__sys_device_token__mutation__deleteDeviceToken__parameter__tokenId": "", "@sage/xtrem-system/nodes__sys_device_token__node_name": "", "@sage/xtrem-system/nodes__sys_device_token__property__expiration": "", "@sage/xtrem-system/nodes__sys_device_token__property__loginTestUrl": "", "@sage/xtrem-system/nodes__sys_device_token__property__loginUrl": "", "@sage/xtrem-system/nodes__sys_device_token__property__name": "", "@sage/xtrem-system/nodes__sys_device_token__property__tokenId": "", "@sage/xtrem-system/nodes__sys_note__asyncMutation__asyncExport": "", "@sage/xtrem-system/nodes__sys_note__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-system/nodes__sys_note__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-system/nodes__sys_note__node_name": "", "@sage/xtrem-system/nodes__sys_note__property__content": "", "@sage/xtrem-system/nodes__sys_note__property__title": "", "@sage/xtrem-system/nodes__sys_note_association__node_name": "", "@sage/xtrem-system/nodes__sys_note_association__property__note": "", "@sage/xtrem-system/nodes__sys_note_association__property__sourceNodeId": "", "@sage/xtrem-system/nodes__sys_note_association__property__sourceNodeName": "", "@sage/xtrem-system/nodes__sys_pack_allocation__asyncMutation__asyncExport": "", "@sage/xtrem-system/nodes__sys_pack_allocation__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-system/nodes__sys_pack_allocation__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-system/nodes__sys_pack_allocation__mutation__activate": "Activate", "@sage/xtrem-system/nodes__sys_pack_allocation__mutation__activate__failed": "", "@sage/xtrem-system/nodes__sys_pack_allocation__mutation__activate__parameter__packageId": "", "@sage/xtrem-system/nodes__sys_pack_allocation__mutation__deactivate": "Deactivate", "@sage/xtrem-system/nodes__sys_pack_allocation__mutation__deactivate__failed": "", "@sage/xtrem-system/nodes__sys_pack_allocation__mutation__deactivate__parameter__packageId": "", "@sage/xtrem-system/nodes__sys_pack_allocation__node_name": "System pack allocation", "@sage/xtrem-system/nodes__sys_pack_allocation__property__isActivable": "Activable", "@sage/xtrem-system/nodes__sys_pack_allocation__property__isActive": "Active", "@sage/xtrem-system/nodes__sys_pack_allocation__property__package": "Package", "@sage/xtrem-system/nodes__sys_pack_allocation__property__status": "Status", "@sage/xtrem-system/nodes__sys_pack_version__asyncMutation__asyncExport": "", "@sage/xtrem-system/nodes__sys_pack_version__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-system/nodes__sys_pack_version__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-system/nodes__sys_pack_version__node_name": "System pack version", "@sage/xtrem-system/nodes__sys_pack_version__property__isBundle": "Bundle", "@sage/xtrem-system/nodes__sys_pack_version__property__isHidden": "Hidden", "@sage/xtrem-system/nodes__sys_pack_version__property__isReleased": "Released", "@sage/xtrem-system/nodes__sys_pack_version__property__isUpgradeBundle": "Upgrade bundle", "@sage/xtrem-system/nodes__sys_pack_version__property__name": "Name", "@sage/xtrem-system/nodes__sys_pack_version__property__sqlSchemaVersion": "SQL schema version", "@sage/xtrem-system/nodes__sys_pack_version__property__version": "Version", "@sage/xtrem-system/nodes__sys_patch_history__asyncMutation__asyncExport": "", "@sage/xtrem-system/nodes__sys_patch_history__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-system/nodes__sys_patch_history__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-system/nodes__sys_patch_history__node_name": "System patch history", "@sage/xtrem-system/nodes__sys_patch_history__property__packageName": "Package name", "@sage/xtrem-system/nodes__sys_patch_history__property__patchName": "Patch name", "@sage/xtrem-system/nodes__sys_patch_history__property__result": "Result", "@sage/xtrem-system/nodes__sys_patch_history__property__version": "Version", "@sage/xtrem-system/nodes__sys_service_option__asyncMutation__asyncExport": "", "@sage/xtrem-system/nodes__sys_service_option__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-system/nodes__sys_service_option__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-system/nodes__sys_service_option__node_name": "System service option", "@sage/xtrem-system/nodes__sys_service_option__property__childServiceOptions": "Child service options", "@sage/xtrem-system/nodes__sys_service_option__property__description": "Description", "@sage/xtrem-system/nodes__sys_service_option__property__isActiveByDefault": "Active by default", "@sage/xtrem-system/nodes__sys_service_option__property__isHidden": "Hidden", "@sage/xtrem-system/nodes__sys_service_option__property__optionName": "Option name", "@sage/xtrem-system/nodes__sys_service_option__property__package": "Package", "@sage/xtrem-system/nodes__sys_service_option__property__parentServiceOptions": "Parent service options", "@sage/xtrem-system/nodes__sys_service_option__property__status": "Status", "@sage/xtrem-system/nodes__sys_service_option_state__asyncMutation__asyncExport": "", "@sage/xtrem-system/nodes__sys_service_option_state__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-system/nodes__sys_service_option_state__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-system/nodes__sys_service_option_state__node_name": "System service option status", "@sage/xtrem-system/nodes__sys_service_option_state__property__imageStatus": "Image status", "@sage/xtrem-system/nodes__sys_service_option_state__property__isActivable": "Activable", "@sage/xtrem-system/nodes__sys_service_option_state__property__isActive": "Active", "@sage/xtrem-system/nodes__sys_service_option_state__property__isAvailable": "Available", "@sage/xtrem-system/nodes__sys_service_option_state__property__isHiddenByConfig": "Hidden by default", "@sage/xtrem-system/nodes__sys_service_option_state__property__isPackageActive": "Active package", "@sage/xtrem-system/nodes__sys_service_option_state__property__isReadyToUse": "Ready to use", "@sage/xtrem-system/nodes__sys_service_option_state__property__serviceOption": "Service option", "@sage/xtrem-system/nodes__sys_service_option_to_service_option__asyncMutation__asyncExport": "", "@sage/xtrem-system/nodes__sys_service_option_to_service_option__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-system/nodes__sys_service_option_to_service_option__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-system/nodes__sys_service_option_to_service_option__node_name": "System service option to service option", "@sage/xtrem-system/nodes__sys_service_option_to_service_option__property__childServiceOption": "Child service option", "@sage/xtrem-system/nodes__sys_service_option_to_service_option__property__parentServiceOption": "Parent service option", "@sage/xtrem-system/nodes__sys_tag__asyncMutation__asyncExport": "", "@sage/xtrem-system/nodes__sys_tag__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-system/nodes__sys_tag__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-system/nodes__sys_tag__node_name": "", "@sage/xtrem-system/nodes__sys_tag__property__description": "", "@sage/xtrem-system/nodes__sys_tag__property__name": "", "@sage/xtrem-system/nodes__sys_tenant__asyncMutation__asyncExport": "", "@sage/xtrem-system/nodes__sys_tenant__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-system/nodes__sys_tenant__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-system/nodes__sys_tenant__mutation__resetTenantDocuments": "", "@sage/xtrem-system/nodes__sys_tenant__mutation__resetTenantDocuments__failed": "", "@sage/xtrem-system/nodes__sys_tenant__mutation__resetTenantDocuments__parameter__tenantId": "", "@sage/xtrem-system/nodes__sys_tenant__node_name": "System tenant", "@sage/xtrem-system/nodes__sys_tenant__property__customer": "Customer", "@sage/xtrem-system/nodes__sys_tenant__property__directoryName": "Directory name", "@sage/xtrem-system/nodes__sys_tenant__property__name": "Name", "@sage/xtrem-system/nodes__sys_tenant__property__tenantId": "Tenant ID", "@sage/xtrem-system/nodes__sys_tenant__query__getTenantInformation": "", "@sage/xtrem-system/nodes__sys_tenant__query__getTenantInformation__failed": "", "@sage/xtrem-system/nodes__sys_tenant__tenant_directory_name_should_be_in_kebab_case": "Napisz nazwę katalogu dzierżawcy przy użyciu kebab-case.", "@sage/xtrem-system/nodes__sys_tenant__tenant_name_characters_not_authorized": "Nazwa dzierżawcy zawiera niedozwolone znaki.", "@sage/xtrem-system/nodes__sys_upgrade__asyncMutation__asyncExport": "", "@sage/xtrem-system/nodes__sys_upgrade__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-system/nodes__sys_upgrade__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-system/nodes__sys_upgrade__node_name": "System upgrade", "@sage/xtrem-system/nodes__sys_upgrade__property__bundleId": "Bundle ID", "@sage/xtrem-system/nodes__sys_upgrade__property__managedItems": "Managed items", "@sage/xtrem-system/nodes__sys_vendor__asyncMutation__asyncExport": "", "@sage/xtrem-system/nodes__sys_vendor__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-system/nodes__sys_vendor__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-system/nodes__sys_vendor__node_name": "System vendor", "@sage/xtrem-system/nodes__sys_vendor__property__description": "Description", "@sage/xtrem-system/nodes__sys_vendor__property__name": "Name", "@sage/xtrem-system/nodes__sys-client-user-settings": "", "@sage/xtrem-system/nodes__sys-layer__name-must-be-kebab-case": "Nazwa musi być zapisana z myślnikami zamiast spacji (kebab-case).", "@sage/xtrem-system/nodes__user__asyncMutation__asyncExport": "", "@sage/xtrem-system/nodes__user__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-system/nodes__user__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-system/nodes__user__cannot_modify_own_rights": "You are not allowed to modify your own administrator rights.", "@sage/xtrem-system/nodes__user__mutation__logPageVisit": "Log page visit", "@sage/xtrem-system/nodes__user__mutation__logPageVisit__failed": "", "@sage/xtrem-system/nodes__user__mutation__logPageVisit__parameter__path": "Path", "@sage/xtrem-system/nodes__user__mutation__sendWelcomeMail": "Send welcome email", "@sage/xtrem-system/nodes__user__mutation__sendWelcomeMail__failed": "", "@sage/xtrem-system/nodes__user__mutation__sendWelcomeMail__parameter__isAdmin": "Admin", "@sage/xtrem-system/nodes__user__mutation__sendWelcomeMail__parameter__users": "Users", "@sage/xtrem-system/nodes__user__mutation__setDemoPersona": "Set demo persona", "@sage/xtrem-system/nodes__user__mutation__setDemoPersona__failed": "", "@sage/xtrem-system/nodes__user__mutation__setDemoPersona__parameter__email": "Email", "@sage/xtrem-system/nodes__user__mutation__updateClientSettings": "", "@sage/xtrem-system/nodes__user__mutation__updateClientSettings__failed": "", "@sage/xtrem-system/nodes__user__mutation__updateClientSettings__parameter__clientSettings": "", "@sage/xtrem-system/nodes__user__node_name": "User", "@sage/xtrem-system/nodes__user__only_admin_can_add_admin": "Only an administrator user can add or modify another user's administrator rights.", "@sage/xtrem-system/nodes__user__property__clientSettings": "", "@sage/xtrem-system/nodes__user__property__displayName": "", "@sage/xtrem-system/nodes__user__property__email": "Email", "@sage/xtrem-system/nodes__user__property__firstName": "First name", "@sage/xtrem-system/nodes__user__property__isActive": "Active", "@sage/xtrem-system/nodes__user__property__isAdministrator": "Administrator", "@sage/xtrem-system/nodes__user__property__isApiUser": "API user", "@sage/xtrem-system/nodes__user__property__isDemoPersona": "Demo persona", "@sage/xtrem-system/nodes__user__property__isFirstAdminUser": "First admin user", "@sage/xtrem-system/nodes__user__property__isOperatorUser": "", "@sage/xtrem-system/nodes__user__property__lastName": "Last name", "@sage/xtrem-system/nodes__user__property__navigation": "Navigation", "@sage/xtrem-system/nodes__user__property__operatorCode": "", "@sage/xtrem-system/nodes__user__property__operatorId": "", "@sage/xtrem-system/nodes__user__property__photo": "Photo", "@sage/xtrem-system/nodes__user__property__preferences": "Preferences", "@sage/xtrem-system/nodes__user__property__setupId": "Setup ID", "@sage/xtrem-system/nodes__user__property__userType": "User type", "@sage/xtrem-system/nodes__user__query__demoPersonas": "Demo personas", "@sage/xtrem-system/nodes__user__query__demoPersonas__failed": "", "@sage/xtrem-system/nodes__user_navigation__asyncMutation__asyncExport": "", "@sage/xtrem-system/nodes__user_navigation__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-system/nodes__user_navigation__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-system/nodes__user_navigation__node_name": "User navigation", "@sage/xtrem-system/nodes__user_navigation__property__bookmarks": "Bookmarks", "@sage/xtrem-system/nodes__user_navigation__property__history": "History", "@sage/xtrem-system/nodes__user_navigation__property__user": "User", "@sage/xtrem-system/nodes__user_preferences__asyncMutation__asyncExport": "", "@sage/xtrem-system/nodes__user_preferences__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-system/nodes__user_preferences__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-system/nodes__user_preferences__node_name": "User preferences", "@sage/xtrem-system/nodes__user_preferences__property__importExportDateFormat": "", "@sage/xtrem-system/nodes__user_preferences__property__importExportDelimiter": "", "@sage/xtrem-system/nodes__user_preferences__property__isExternal": "", "@sage/xtrem-system/nodes__user_preferences__property__isWelcomeMailSent": "Welcome mail sent", "@sage/xtrem-system/nodes__user_preferences__property__user": "User", "@sage/xtrem-system/nodes__user_preferences__query__activeServiceOptions": "", "@sage/xtrem-system/nodes__user_preferences__query__activeServiceOptions__failed": "", "@sage/xtrem-system/package__name": "System", "@sage/xtrem-system/package-activated": "Aktywowano pakiet", "@sage/xtrem-system/package-deactivated": "Dezaktywowano pakiet", "@sage/xtrem-system/pages__client_user_settings_edit____title": "", "@sage/xtrem-system/pages__client_user_settings_edit__generalSection____title": "", "@sage/xtrem-system/pages__client_user_settings_edit__save____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__client_user_settings_edit__title____title": "", "@sage/xtrem-system/pages__client_user_settings_list____title": "", "@sage/xtrem-system/pages__client_user_settings_list__clientSettings____columns__title__title": "", "@sage/xtrem-system/pages__client_user_settings_list__clientSettings____dropdownActions__title__remove": "", "@sage/xtrem-system/pages__client_user_settings_list__generalSection____title": "", "@sage/xtrem-system/pages__client_user_settings_list__save____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__delete_page_dialog_content": "", "@sage/xtrem-system/pages__delete_page_dialog_title": "", "@sage/xtrem-system/pages__pack_allocation____navigationPanel__optionsMenu__title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__pack_allocation____navigationPanel__optionsMenu__title__2": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__pack_allocation____navigationPanel__optionsMenu__title__3": "<PERSON><PERSON>", "@sage/xtrem-system/pages__pack_allocation____title": "Aktywacja pakietu", "@sage/xtrem-system/pages__pack_allocation__closePage____title": "Zamknij", "@sage/xtrem-system/pages__pack_allocation__generalSection____title": "Ogólne", "@sage/xtrem-system/pages__pack_allocation__isActivable____title": "Dostępny", "@sage/xtrem-system/pages__pack_allocation__isActive____title": "Aktywny", "@sage/xtrem-system/pages__pack_allocation__package____title": "<PERSON><PERSON>", "@sage/xtrem-system/pages__pack_allocation__version____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__pack_version____navigationPanel__optionsMenu__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__pack_version____navigationPanel__optionsMenu__title__2": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__pack_version____navigationPanel__optionsMenu__title__3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__pack_version____title": "Aktywacja pakietu", "@sage/xtrem-system/pages__pack_version__closePage____title": "Zamknij", "@sage/xtrem-system/pages__pack_version__generalSection____title": "Ogólne", "@sage/xtrem-system/pages__pack_version__isBundle____title": "<PERSON><PERSON>", "@sage/xtrem-system/pages__pack_version__package____title": "<PERSON><PERSON>", "@sage/xtrem-system/pages__pack_version__version____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__service_option_state____navigationPanel__dropdownActions__title": "", "@sage/xtrem-system/pages__service_option_state____navigationPanel__dropdownActions__title__2": "", "@sage/xtrem-system/pages__service_option_state____navigationPanel__listItem__line_4__title": "", "@sage/xtrem-system/pages__service_option_state____navigationPanel__listItem__line2__title": "Description", "@sage/xtrem-system/pages__service_option_state____navigationPanel__listItem__line3__title": "Active", "@sage/xtrem-system/pages__service_option_state____navigationPanel__listItem__title__title": "Name", "@sage/xtrem-system/pages__service_option_state____navigationPanel__optionsMenu__title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__service_option_state____objectTypePlural": "Service options", "@sage/xtrem-system/pages__service_option_state____objectTypeSingular": "Service option", "@sage/xtrem-system/pages__service_option_state____title": "<PERSON><PERSON><PERSON>i", "@sage/xtrem-system/pages__service_option_state__cancel____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__service_option_state__closePage____title": "Zamknij", "@sage/xtrem-system/pages__service_option_state__reset_tenant_data_done": "Tenant data reset done", "@sage/xtrem-system/pages__sys_changelog____navigationPanel__listItem__line2__title": "", "@sage/xtrem-system/pages__sys_changelog____navigationPanel__listItem__line3__title": "", "@sage/xtrem-system/pages__sys_changelog____navigationPanel__listItem__title__title": "", "@sage/xtrem-system/pages__sys_changelog____title": "", "@sage/xtrem-system/pages__sys_device_token____navigationPanel__listItem__tokenId__title": "", "@sage/xtrem-system/pages__sys_device_token____navigationPanel__optionsMenu__title": "", "@sage/xtrem-system/pages__sys_device_token____title": "", "@sage/xtrem-system/pages__sys_device_token__createDeviceToken____title": "", "@sage/xtrem-system/pages__sys_device_token__deleteDeviceToken____title": "", "@sage/xtrem-system/pages__sys_device_token__deviceTokenBlock____title": "", "@sage/xtrem-system/pages__sys_device_token__expiration____title": "", "@sage/xtrem-system/pages__sys_device_token__loginTestUrl____title": "", "@sage/xtrem-system/pages__sys_device_token__loginUrl____title": "", "@sage/xtrem-system/pages__sys_device_token__name____title": "", "@sage/xtrem-system/pages__sys_device_token__tokenId____title": "", "@sage/xtrem-system/pages__sys_layer____navigationPanel__optionsMenu__title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__sys_layer____title": "Warstwa danych", "@sage/xtrem-system/pages__sys_layer___id____title": "ID", "@sage/xtrem-system/pages__sys_layer__dataLayerInformation____title": "Informacje o warstwie danych", "@sage/xtrem-system/pages__sys_layer__deleteLayerAction____title": "Usuń", "@sage/xtrem-system/pages__sys_layer__description____title": "Opis", "@sage/xtrem-system/pages__sys_layer__exportLayer____title": "Eksportuj warstwę", "@sage/xtrem-system/pages__sys_layer__generalSection____title": "Ogólne", "@sage/xtrem-system/pages__sys_layer__name____title": "Nazwa warstwy danych", "@sage/xtrem-system/pages__sys_pack_allocation____navigationPanel__listItem__lineRight__title": "Version", "@sage/xtrem-system/pages__sys_pack_allocation____navigationPanel__optionsMenu__title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__sys_pack_allocation____navigationPanel__optionsMenu__title__2": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__sys_pack_allocation____title": "Aktywacja pakietu", "@sage/xtrem-system/pages__sys_pack_allocation__closePage____title": "Zamknij", "@sage/xtrem-system/pages__sys_pack_allocation__generalSection____title": "Ogólne", "@sage/xtrem-system/pages__sys_pack_allocation__isActivable____title": "Dostępny", "@sage/xtrem-system/pages__sys_pack_allocation__isActive____title": "Aktywny", "@sage/xtrem-system/pages__sys_pack_allocation__package____title": "<PERSON><PERSON>", "@sage/xtrem-system/pages__sys_pack_allocation__version____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__sys_tag____navigationPanel__listItem__line2__title": "", "@sage/xtrem-system/pages__sys_tag____navigationPanel__listItem__title__title": "", "@sage/xtrem-system/pages__sys_tag____title": "", "@sage/xtrem-system/pages__sys_tag__description____title": "", "@sage/xtrem-system/pages__sys_tag__generalSection____title": "", "@sage/xtrem-system/pages__sys_tag__name____title": "", "@sage/xtrem-system/pages__tenant_information____title": "Tenant information", "@sage/xtrem-system/pages__tenant_information__cancel_reset_tenant_data_button_text": "", "@sage/xtrem-system/pages__tenant_information__confirm_reset_tenant_data_button_text": "", "@sage/xtrem-system/pages__tenant_information__generalSection____title": "General", "@sage/xtrem-system/pages__tenant_information__reset_tenant_data_button_text": "", "@sage/xtrem-system/pages__tenant_information__resetDataDescription____content": "Reset tenant data will delete all the movements (such as sales and purchase orders, stock level and movements).\nThis operation cannot be reverted. Are you sure?", "@sage/xtrem-system/pages__tenant_information__resetTenantId____helperText": "Please type the tenant ID to delete", "@sage/xtrem-system/pages__tenant_information__resetTenantId____title": "Confirm Tenant ID", "@sage/xtrem-system/pages__tenant_information__resetTenantSection____title": "Reset", "@sage/xtrem-system/pages__tenant_information__sumologic_button_text": "View Sumo Logic logs", "@sage/xtrem-system/pages__tenant_information__sumologicLink____helperText": "You need to be logged in to Sumo Logic before you can view the logs.", "@sage/xtrem-system/pages__tenant_information__tenantId____title": "Tenant ID", "@sage/xtrem-system/pages__tenant_information__tenantVersion____title": "Tenant version", "@sage/xtrem-system/pages__user____navigationPanel__optionsMenu__title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__user____title": "Użytkownik", "@sage/xtrem-system/pages__user___id____title": "ID", "@sage/xtrem-system/pages__user__email____title": "E-mail", "@sage/xtrem-system/pages__user__firstName____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-system/pages__user__generalSection____title": "Ogólne", "@sage/xtrem-system/pages__user__idBlock____title": "ID", "@sage/xtrem-system/pages__user__isActive____title": "Aktywny", "@sage/xtrem-system/pages__user__lastName____title": "Nazwisko", "@sage/xtrem-system/pages__user__userInformationBlock____title": "Informacje o użytkowniku", "@sage/xtrem-system/pages__user__userPhotoBlock____title": "Zdjęcie", "@sage/xtrem-system/pages-confirm-cancel": "", "@sage/xtrem-system/pages-confirm-delete": "", "@sage/xtrem-system/permission__create__name": "", "@sage/xtrem-system/permission__delete__name": "", "@sage/xtrem-system/permission__get_tenant_information__name": "", "@sage/xtrem-system/permission__read__name": "", "@sage/xtrem-system/permission__reset_tenant_documents__name": "", "@sage/xtrem-system/permission__update__name": "", "@sage/xtrem-system/productKey": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-system/productKey2": "<PERSON><PERSON><PERSON><PERSON> art<PERSON> {{num}}", "@sage/xtrem-system/reserved-api-user-email": "Email address {{value}} is reserved for API users.", "@sage/xtrem-system/reserved-operator-user-email": "", "@sage/xtrem-system/reset-tenant-data-not-admin": "", "@sage/xtrem-system/reset-tenant-data-not-admin-with-id": "", "@sage/xtrem-system/reset-tenant-data-tenant-id-mismatched": "The {{tenantId}} tenant ID does not match the active tenant ({{currentId}}).", "@sage/xtrem-system/service_options__changelog__name": "", "@sage/xtrem-system/service_options__dev_tools__name": "", "@sage/xtrem-system/service_options__is_demo_tenant__name": "", "@sage/xtrem-system/service_options__sys_device_token__name": "", "@sage/xtrem-system/service_options__sys_tag__name": "", "@sage/xtrem-system/statusIcon": "Status", "@sage/xtrem-system/sys_device_token_creation_error": "", "@sage/xtrem-system/sys_device_token_deletion_error": "", "@sage/xtrem-system/sys_device_token_device_token_already_created": "", "@sage/xtrem-system/system-user-forbidden": "System users cannot be created or updated.", "@sage/xtrem-system/update-confirmation": "Zaktualizowano rekord", "@sage/xtrem-system/widgets__system_version____title": ""}