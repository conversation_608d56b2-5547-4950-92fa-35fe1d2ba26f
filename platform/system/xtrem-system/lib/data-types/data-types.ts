import {
    BinaryStreamDataType,
    DecimalDataType,
    StringDataType,
    TextStreamDataType,
    ValidationContext,
} from '@sage/xtrem-core';
import type { IconType } from 'carbon-react/esm/components/icon/icon-type';
import * as crypto from 'crypto';
import { nanoid as newNanoid } from 'nanoid';
import { UrlDataType } from './url-data-type';

export const code = new StringDataType({ maxLength: 24 });

/** With isStoredEncrypted:true  */
export const password = new StringDataType({ maxLength: 250 });
export const passphrase = new StringDataType({ maxLength: 1024 });
export const url = new StringDataType({ maxLength: 200 });
export const httpUrl = new UrlDataType({ maxLength: 200, allowedProtocols: ['https', 'http'] });
export const httpBaseUrl = new UrlDataType({
    maxLength: 200,
    allowedProtocols: ['https', 'http'],
    skrinkToOrigin: true,
});
export const bigUrl = new StringDataType({ maxLength: 20000 });
/** maxLength:80 */
export const shortDescription = new StringDataType({ maxLength: 80 });
export const localizedShortDescription = new StringDataType({ maxLength: 80, isLocalized: true });
/** maxLength:4000 */
export const description = new StringDataType({ maxLength: 4000 });
export const localizedDescription = new StringDataType({ maxLength: 4000, isLocalized: true });

export const id = new StringDataType({ maxLength: 30 });
export const bigId = new StringDataType({ maxLength: 300 });
export const uuid = new StringDataType({ maxLength: 36 });
/** maxLength:80 */
export const folder = new StringDataType({ maxLength: 80 });
export const name = new StringDataType({ maxLength: 80 });
export const localizedName = new StringDataType({ maxLength: 200, isLocalized: true });
export const nanoid = new StringDataType({ maxLength: 21, defaultValue: () => newNanoid() });
export const version = new StringDataType({ maxLength: 20 });

export const decimal = new DecimalDataType({ precision: 10, scale: 3 });
export const rate = new DecimalDataType({ precision: 5, scale: 2 });
export const email = new StringDataType({ maxLength: 100 });
export const quantityDataType = new DecimalDataType({ precision: 10, scale: 5 });
export const bundleIdDataType = new StringDataType({ maxLength: 100 });
export const bundlePathType = new StringDataType({ maxLength: 1024 });
export const bundleVersionDataType = new StringDataType({ maxLength: 50 });
export const notificationDataDataType = new BinaryStreamDataType({ maxLength: 8 * 1024 });
export const noteContentDataType = new TextStreamDataType({
    maxLength: 3000,
    allowedContentTypes: ['text/html', 'text/plain'],
});
export const setupIdDataType = new StringDataType({ maxLength: 128 });
export const checksumDataType = new StringDataType({ maxLength: 32 });

export const mimeType = new StringDataType({ maxLength: 255 });
export const contentLengthDecimalDataType = new DecimalDataType({ precision: 10, scale: 0 });

export const title = new StringDataType({ maxLength: 255 });
export const localizedTitle = new StringDataType({ maxLength: 255, isLocalized: true });

export const icon = new StringDataType<unknown, IconType>({ maxLength: 32 });

export const singleCharacter = new StringDataType({ maxLength: 1 });

/**
 * A datatype for RGB color codes
 * Notes:
 * - without the leading #
 * - 8 characters long (6 for the color, 2 for the alpha channel)
 */
class RgbDataType<T> extends StringDataType<T> {
    constructor() {
        super({
            maxLength: 8,
            defaultValue: () => {
                // Create a new random RGB color
                return Array.from({ length: 6 }, () => crypto.randomInt(0, 16).toString(16).toUpperCase()).join('');
            },
        });
    }

    public override async controlValue(node: T, cx: ValidationContext, val: string): Promise<void> {
        await super.controlValue(node, cx, val);
        // Check if the value is a valid hexadecimal color code
        if (!/^[0-9A-Fa-f]{8}$/.test(val)) {
            cx.error.addLocalized(
                '@sage/xtrem-system/data-types/rgb__invalid_rgb',
                'This value is not an RGB color code: "{{value}}".',
                {
                    value: val,
                },
            );
        }
    }
}

// A color code in hexadecimal format
export const rgbColor = new RgbDataType();
