import { ConfigManager, StringDataType, StringDataTypeOptions, ValidationContext } from '@sage/xtrem-core';
import { isDevelopmentConfig } from '@sage/xtrem-shared';

export interface UrlDataTypeOptions<T = unknown> extends StringDataTypeOptions<T> {
    allowedProtocols: string[];
    skrinkToOrigin?: boolean;
}

export class UrlDataType<T> extends StringDataType<T> {
    readonly allowedProtocols: string[];

    constructor(options: UrlDataTypeOptions<T>) {
        super(options);
        // In development, we allow http protocol
        this.allowedProtocols = options.allowedProtocols.filter(
            protocol => protocol !== 'http' || isDevelopmentConfig(ConfigManager.current),
        );
        Object.freeze(this.allowedProtocols);
    }

    public override adaptValue(_node: T, url: string): string {
        if (URL.canParse(url)) {
            // will automatically fix malformed urls like 'http:/example.com' to 'http://example.com'
            const parsed = new URL(url);
            const adapted = (this.options as UrlDataTypeOptions<T>).skrinkToOrigin ? parsed.origin : parsed.href;
            return adapted === 'null' ? url : adapted;
        }
        return url;
    }

    public override async controlValue(node: T, cx: ValidationContext, url: string): Promise<void> {
        await super.controlValue(node, cx, url);

        const canParseUrl = URL.canParse(url);
        await cx.error
            .withMessage('@sage/xtrem-system/data-types/url_datatype__invalid_url', "Incorrect URL '{{url}}'.", () => ({
                url,
            }))
            .if(canParseUrl)
            .is.false();

        if (canParseUrl) {
            const parsed = new URL(url);
            const protocol = parsed.protocol.replace(/:$/, '');
            await cx.error
                .withMessage(
                    '@sage/xtrem-system/data-types/url_datatype__invalid_protocol',
                    "Url '{{url}}' got protocol {{protocol}}, expecting {{allowedProtocols}}.",
                    () => ({ url, protocol, allowedProtocols: this.allowedProtocols.join(', ') }),
                )
                .if(this.allowedProtocols.includes(protocol))
                .is.false();
        }
    }
}
