import { LocalizeLocale, StringDataType, ValidationContext } from '@sage/xtrem-core';
import { supportedLocales } from '@sage/xtrem-shared';

export const localeIdDataType = new StringDataType({ maxLength: 5 });

export class LocaleDataType<T> extends StringDataType<T> {
    public override async controlValue(node: T, cx: ValidationContext, locale: string): Promise<void> {
        await super.controlValue(node, cx, locale);
        await cx.error
            .withMessage(
                '@sage/xtrem-system/data-types/locale_datatype__invalid_locale',
                'Locale value is invalid: ({{locale}}).',
                () => ({ locale }),
            )
            .if(supportedLocales.includes(locale as LocalizeLocale))
            .is.false();
    }
}

export const localeDatatype = new LocaleDataType({ maxLength: 5 });
