import { Application, Logger } from '@sage/xtrem-core';
import { SystemSqlUpgrade, SystemSqlUpgradeExecuteSql } from '../../services/upgrade/system-sql-upgrade';

/**
 * Drop and recreate all invalid foreign keys without NOT VALID
 * Where creation fails, delete the invalid orphan records and then recreate the FK
 */
export class FixInvalidFks extends SystemSqlUpgrade {
    constructor() {
        super('Fix invalid foreign keys');
    }

    /**
     * Execution of the upgrade action
     */
    // eslint-disable-next-line class-methods-use-this
    async execute(
        application: Application,
        options: { logger: Logger; executeSql: SystemSqlUpgradeExecuteSql },
    ): Promise<void> {
        await options.executeSql(
            `DO $$

            DECLARE
                iFixed integer := 0;
                iCount integer := 0;
                
                curInvalidFks CURSOR FOR SELECT
                    connamespace::regnamespace AS schema_name,
                    conname AS fk_name,
                    conrelid::regclass AS table_name,
                    confrelid::regclass AS rel_table_name,
                    conkey AS constraint_key,
                    confkey AS reference_key,
                    pg_get_constraintdef(t1.oid) AS fk_def,
                    COALESCE('xtrem.' || (obj_description(t2.oid)::json->'rootTable')::TEXT, conrelid::regclass::TEXT) AS rootTable
                FROM pg_constraint t1
                JOIN pg_class t2 ON t2.oid = t1.conrelid
                WHERE contype = 'f' AND convalidated <> TRUE  AND connamespace::regnamespace::TEXT = '${application.schemaName}';
                
            BEGIN
                FOR rec IN curInvalidFks LOOP
                    BEGIN
                        EXECUTE format('ALTER TABLE %s DROP CONSTRAINT IF EXISTS %s;', rec.table_name, rec.fk_name);
            
                        EXECUTE format('ALTER TABLE %s ADD CONSTRAINT %s %s;', rec.table_name, rec.fk_name, replace(rec.fk_def, ' NOT VALID', ''));
                        
                        RAISE WARNING 'Validated %', rec.fk_name;		
                    EXCEPTION
                        WHEN foreign_key_violation THEN BEGIN
                            RAISE WARNING 'Foreign key violation: %', rec.fk_name;
                        
                            DECLARE curFkCols CURSOR (KEY TEXT) FOR 
                                SELECT 
                                    conrelid::regclass AS source_table,
                                    confrelid::regclass AS foreign_table,
                                    '"' || t1.attname || '"' AS source_col,
                                    '"' || t2.attname || '"' AS foreign_col
                                FROM pg_constraint t
                                JOIN pg_attribute t1 ON t1.attrelid = t.conrelid
                                JOIN pg_attribute t2 ON t2.attrelid = t.confrelid AND t2.attnum = confkey[array_position(conkey, t1.attnum)]
                                WHERE contype = 'f' AND convalidated <> TRUE AND t1.attnum = ANY(t.conkey) AND t.conname = KEY;
                            
                                sSQL TEXT := '';
                                sourceCols TEXT := '';
                                foreignCols TEXT := '';
                            
                            BEGIN
                                FOR recFk IN curFkCols (rec.fk_name) LOOP
                                    IF sourceCols = '' THEN
                                        sourceCols = recFk.source_col;
                                        foreignCols = recFk.foreign_col;
                                    ELSE
                                        sourceCols = sourceCols || ', ' || recFk.source_col;
                                        foreignCols = foreignCols || ', ' || recFk.foreign_col;
                                    END IF;
                                END LOOP;
                                
                                sSQL = 'DELETE FROM ' || rec.rootTable || ' WHERE _id IN (SELECT _id FROM ' ||
                                        rec.table_name || ' WHERE (' || sourceCols || ') NOT IN (SELECT ' || 
                                        foreignCols || ' FROM ' || rec.rel_table_name || '))';
                            
                                RAISE WARNING 'DELETE invalid data: %', sSQL;
                                EXECUTE sSQL;
                            
                                -- Recreate FK
                                RAISE WARNING 'Recreate foreign key: %', rec.fk_name;
                                EXECUTE format('ALTER TABLE %s DROP CONSTRAINT IF EXISTS %s;', rec.table_name, rec.fk_name);
                                EXECUTE format('ALTER TABLE %s ADD CONSTRAINT %s %s;', rec.table_name, rec.fk_name, replace(rec.fk_def, ' NOT VALID', ''));
                                iFixed = iFixed + 1;
                            END;
                        END;
                    END;
                END LOOP;
            
                SELECT count(*) INTO iCount
                FROM pg_constraint
                WHERE contype = 'f' AND convalidated <> TRUE;
                
                RAISE WARNING 'Invalid Foreign keys fixed: %', iFixed;
                RAISE WARNING 'Invalid Foreign keys remaining: %', iCount;
            
            END$$;`,
            [],
        );
    }
}
