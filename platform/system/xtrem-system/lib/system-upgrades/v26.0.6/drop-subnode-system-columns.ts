import { Application, asyncArray, Logger } from '@sage/xtrem-core';
import { SystemSqlUpgrade, SystemSqlUpgradeExecuteSql } from '../../services/upgrade/system-sql-upgrade';

/**
 * Drop all inheriited system columns on subnodes
 */
export class DropSubnodeSystemColumns extends SystemSqlUpgrade {
    constructor() {
        super('Drop all inheriited system columns on subnodes');
    }

    /**
     * Execution of the upgrade action
     */
    // eslint-disable-next-line class-methods-use-this
    async execute(
        application: Application,
        options: { logger: Logger; executeSql: SystemSqlUpgradeExecuteSql },
    ): Promise<void> {
        const subFactories = application.getSqlPackageFactories().filter(factory => !!factory.baseFactory);
        await asyncArray(subFactories).forEach(async factory => {
            const inheritedSystemColumns = [
                ...['_create_user', '_update_user', '_create_stamp', '_update_stamp', '_update_tick', '_source_id'],
            ];

            if (factory.hasVendorProperty && (factory.rootVendorFactory?.name || factory.name) !== factory.name)
                inheritedSystemColumns.push('_vendor');

            if (
                factory.isVitalCollectionChild &&
                (factory.rootCollectionFactory?.name || factory.name) !== factory.name
            )
                inheritedSystemColumns.push('_sort_value');

            const fullTableName = `${application.schemaName}.${factory.table.name}`;
            const dropStatements = inheritedSystemColumns.map(columnName => `DROP COLUMN IF EXISTS ${columnName}`);
            options.logger.warn(
                `Attempting to drop columns ${inheritedSystemColumns.join()} from ${factory.table.name}`,
            );
            let sql = `ALTER TABLE IF EXISTS ${fullTableName} ${dropStatements.join()};`;

            await options.executeSql(sql, []);

            options.logger.warn(`Attempting to drop trigger insert_table on ${factory.table.name}`);
            sql = `DROP TRIGGER IF EXISTS insert_table ON ${fullTableName};`;
            await options.executeSql(sql, []);

            options.logger.warn(`Attempting to drop trigger update_table on ${factory.table.name}`);
            sql = `DROP TRIGGER IF EXISTS update_table ON ${fullTableName};`;
            await options.executeSql(sql, []);
        });
    }
}
