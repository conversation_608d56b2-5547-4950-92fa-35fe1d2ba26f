import { Application, asyncArray, Logger } from '@sage/xtrem-core';
import { SystemSqlUpgrade, SystemSqlUpgradeExecuteSql } from '../../services/upgrade/system-sql-upgrade';

/**
 * Update all the create and update stamp column defaults
 */
export class AddSystemStampDefault extends SystemSqlUpgrade {
    constructor() {
        super('Add default on _create_stamp and _update_stamp colunms');
    }

    /**
     * Execution of the upgrade action
     */
    // eslint-disable-next-line class-methods-use-this
    async execute(
        application: Application,
        options: { logger: Logger; executeSql: SystemSqlUpgradeExecuteSql },
    ): Promise<void> {
        const stampColumns = ['_update_stamp', '_create_stamp'];
        const rootFactories = application
            .getSqlPackageFactories()
            .filter(factory => factory.table.columns.some(col => stampColumns.includes(col.columnName)));

        await asyncArray(rootFactories).forEach(async factory => {
            const factoryStampColumns = factory.table.columns.filter(col => stampColumns.includes(col.columnName));

            const dropStatements = factoryStampColumns.map(col => `ALTER COLUMN ${col.columnName} SET DEFAULT now()`);
            options.logger.warn(
                `Adding default to ${factoryStampColumns.map(col => col.columnName).join()} from ${factory.table.name}`,
            );
            const sql = `ALTER TABLE IF EXISTS ${application.schemaName}.${
                factory.table.name
            } ${dropStatements.join()};`;

            await options.executeSql(sql, []);
        });
    }
}
