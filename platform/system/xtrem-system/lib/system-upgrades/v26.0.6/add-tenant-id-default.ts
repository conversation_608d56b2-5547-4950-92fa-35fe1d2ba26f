import { Application, asyncArray, Logger } from '@sage/xtrem-core';
import { SystemSqlUpgrade, SystemSqlUpgradeExecuteSql } from '../../services/upgrade/system-sql-upgrade';

/**
 * Update all the _tenant_id column defaults
 */
export class AddTenantIdDefault extends SystemSqlUpgrade {
    constructor() {
        super('Add default on _tenant_id colunm');
    }

    /**
     * Execution of the upgrade action
     */
    // eslint-disable-next-line class-methods-use-this
    async execute(
        application: Application,
        options: { logger: Logger; executeSql: SystemSqlUpgradeExecuteSql },
    ): Promise<void> {
        const nonSharedFactories = application
            .getSqlPackageFactories()
            .filter(factory => !factory.isSharedByAllTenants);

        await asyncArray(nonSharedFactories).forEach(async factory => {
            options.logger.warn(`Adding default to _tenant_id from ${factory.table.name}`);
            const sql = `ALTER TABLE IF EXISTS ${application.schemaName}.${factory.table.name} ALTER COLUMN _tenant_id SET DEFAULT '';`;

            await options.executeSql(sql, []);
        });
    }
}
