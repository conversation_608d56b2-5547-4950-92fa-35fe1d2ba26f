import { Application, Logger } from '@sage/xtrem-core';
import { SystemSqlUpgrade, SystemSqlUpgradeExecuteSql } from '../../services/upgrade/system-sql-upgrade';

/**
 * Fix column comments for children of setup nodes
 */
export class FixColumnComments extends SystemSqlUpgrade {
    constructor() {
        super('Fix vital children setup nodes auto-increment column comments');
    }

    /**
     * Execution of the upgrade action
     */
    // eslint-disable-next-line class-methods-use-this
    async execute(
        application: Application,
        options: { logger: Logger; executeSql: SystemSqlUpgradeExecuteSql },
    ): Promise<void> {
        options.logger.warn('Fix vital children setup nodes auto-increment column comments');
        await options.executeSql(
            `do $$ declare
                                comment_command RECORD;

                begin
                                for comment_command in (
                select
                    'comment on column ' || c.table_schema || '.' || c.table_name ||'.'||c.column_name|| ' is ''' || replace(pgd.description, ',' || chr(10)|| '  "isAutoIncrement": true', '')|| '''' new_comment,
                    c.table_schema,
                    c.table_name,
                    c.column_name,
                    c.column_default ,
                    pgd.description
                from
                    pg_catalog.pg_statio_all_tables as st
                inner join pg_catalog.pg_description pgd on
                    (
                    pgd.objoid = st.relid
                )
                inner join information_schema.columns c on
                    (
                    pgd.objsubid = c.ordinal_position
                        and
                    c.table_schema = st.schemaname
                        and
                    c.table_name = st.relname
                )
                where
                    pgd.description like '%"isAutoIncrement": true%'
                    and c.column_default is null                    
                    and st.schemaname = '${application.schemaName}'
                )
                                loop
                                execute comment_command.new_comment;
                end loop;
                end $$;`,
            [],
        );
    }
}
