import { Application, Logger, readTableSchemas } from '@sage/xtrem-core';
import { SystemSqlUpgrade, SystemSqlUpgradeExecuteSql } from '../../services/upgrade/system-sql-upgrade';

/**
 * Update all the datetime precision column to 3
 */
export class ModifySystemStampPrecision extends SystemSqlUpgrade {
    constructor() {
        super('modify datetime_precision on datetime columns');
    }

    /**
     * Execution of the upgrade action
     */
    async execute(
        application: Application,
        options: { logger: Logger; executeSql: SystemSqlUpgradeExecuteSql },
    ): Promise<void> {
        await options.executeSql(await this.generateSql(application, options), []);
    }

    /**
     * Generate the SQL to be executed
     */
    // eslint-disable-next-line class-methods-use-this
    async generateSql(
        application: Application,
        options: { logger: Logger; executeSql: SystemSqlUpgradeExecuteSql },
    ): Promise<string> {
        const tablesList = await application.withReadonlyContext(null, context =>
            readTableSchemas(context, [], { skipForeignKeys: true, skipIndexes: true, skipSequences: true }),
        );

        const result: string[] = [];

        Object.values(tablesList).forEach(tableDef => {
            if (tableDef.columns == null) return;
            const datetimeColumns = tableDef.columns.filter(col => col.type === 'datetime');
            if (datetimeColumns.length === 0) return;
            const alterStatements = datetimeColumns.map(
                col => `ALTER COLUMN ${col.name} TYPE TIMESTAMPTZ(3) USING ${col.name}`,
            );
            options.logger.warn(
                `modify datetime_precision on ${datetimeColumns.map(col => col.name).join()} from ${tableDef.tableName}`,
            );
            const sql = `ALTER TABLE IF EXISTS ${application.schemaName}.${tableDef.tableName} ${alterStatements.join()}`;
            result.push(sql);
        });
        return result.join(';');
    }
}
