import { Application, Logger } from '@sage/xtrem-core';
import { SystemSqlUpgrade, SystemSqlUpgradeExecuteSql } from '../../services/upgrade/system-sql-upgrade';

/**
 * Convert all the _ids to bigserial (i.e int8 with a sequence as a default)
 * This process is not really right because _id of sub-tables should not be bigserials but int8
 * but never mind, the upgrade engine will drop these unrequired sequences
 */
export class ConvertIdsToAutoincrement extends SystemSqlUpgrade {
    constructor() {
        super('Convert the _ids columns to auto-increment');
    }

    /**
     * Execution of the upgrade action
     */
    // eslint-disable-next-line class-methods-use-this
    async execute(
        application: Application,
        options: { logger: Logger; executeSql: SystemSqlUpgradeExecuteSql },
    ): Promise<void> {
        await options.executeSql(
            `DO $$ 
DECLARE r RECORD;
    descr VARCHAR;
BEGIN 
    DROP PROCEDURE if exists ${application.schemaName}.set_sequence(character varying);
    CREATE OR REPLACE PROCEDURE ${application.schemaName}.set_sequence(table_name varchar)  LANGUAGE plpgsql AS
    $function$
        DECLARE
            max_val INT8;
            table_owner VARCHAR(255);
            sequence_name VARCHAR;
            full_table_name VARCHAR;
        begin
            RAISE WARNING 'convert %._id', table_name;
            SELECT '${application.schemaName}.' || table_name INTO full_table_name;
            SELECT full_table_name || '__id_seq' INTO sequence_name;
            
            -- create (if needed) the sequence
            EXECUTE 'CREATE SEQUENCE IF NOT EXISTS ' || sequence_name;
            SELECT tableowner INTO table_owner FROM pg_catalog.pg_tables WHERE schemaname = '${application.schemaName}' AND tablename =  table_name;
            EXECUTE 'ALTER SEQUENCE ' || sequence_name || ' OWNER TO ' || table_owner;
                                
            -- use the sequence as a default for the column
            EXECUTE 'ALTER TABLE ' || full_table_name || ' ALTER COLUMN _id SET DEFAULT nextval(''' || sequence_name || ''')';
            EXECUTE 'ALTER SEQUENCE ' || sequence_name || ' OWNED BY ' || full_table_name || '._id';

            -- set the new nextval of the sequence
            EXECUTE format('SELECT COALESCE(max(_id), 0) from ${application.schemaName}.%I', table_name) INTO max_val;
            EXECUTE 'ALTER SEQUENCE ' || sequence_name || ' RESTART WITH ' || (max_val + 1);
            
            -- update the comment
            EXECUTE 'COMMENT ON COLUMN ' || full_table_name || '._id IS ''{"type": "integer", "isSystem": true, "isAutoIncrement": true}''';
    END;
    $function$;
                
    -- update the _id column of all the tables
    FOR r IN (SELECT table_name FROM information_schema.columns WHERE table_schema = '${application.schemaName}' AND column_name = '_id')
    LOOP
        EXECUTE format('SELECT obj_description(''${application.schemaName}.%I''::regclass)', r.table_name) INTO descr;
        if (descr like '%"baseTable": "%') then
            -- sub-table : drop default and drop the sequence
            EXECUTE 'ALTER TABLE ${application.schemaName}.' || r.table_name || ' ALTER COLUMN _id DROP DEFAULT';
            EXECUTE 'DROP SEQUENCE IF EXISTS ${application.schemaName}.' || r.table_name || '__id_seq';
            -- update the comment
            EXECUTE 'COMMENT ON COLUMN ${application.schemaName}.' || r.table_name || '._id IS ''{"type": "integer", "isSystem": true}''';
        else
            -- create the sequence on _id
            CALL ${application.schemaName}.set_sequence(r.table_name::varchar);     	
        end if;
    END LOOP;
    DROP PROCEDURE ${application.schemaName}.set_sequence(character varying);
END $$;`,
            [],
        );
    }
}
