import { GraphApi, SysDeviceToken as SysDeviceTokenNode } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '../client-functions/applicative-crud-actions';
import { applicationData } from '../menu-items/application-data';

@ui.decorators.page<SysDeviceToken, SysDeviceTokenNode>({
    title: 'Device Token',
    module: 'xtrem-system',
    node: '@sage/xtrem-system/SysDeviceToken',
    businessActions() {
        return [this.$standardCancelAction, this.createDeviceToken];
    },
    onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.createDeviceToken,
            cancel: this.$standardCancelAction,
            remove: this.deleteDeviceToken,
        });
    },
    skipDirtyCheck: true,
    createAction() {
        return this.$standardNewAction;
    },
    navigationPanel: {
        listItem: {
            tokenId: ui.nestedFields.text({ bind: 'tokenId', title: 'Id' }),
            title: ui.nestedFields.text({ bind: 'name' }),
            expiration: ui.nestedFields.text({ bind: 'expiration' }),
        },
        optionsMenu: [{ title: 'All', page: '@sage/xtrem-system/SysDeviceToken' }],
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.deleteDeviceToken],
        });
    },
    menuItem: applicationData,
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.createDeviceToken,
            cancel: this.$standardCancelAction,
            remove: this.deleteDeviceToken,
        });
    },
})
export class SysDeviceToken extends ui.Page<GraphApi, SysDeviceTokenNode> {
    @ui.decorators.section<SysDeviceToken>({
        isTitleHidden: true,
    })
    generalSection: ui.containers.Section;

    @ui.decorators.block<SysDeviceToken>({
        parent() {
            return this.generalSection;
        },
        title: 'Device token information',
        width: 'large',
    })
    deviceTokenBlock: ui.containers.Block;

    @ui.decorators.textField<SysDeviceToken>({
        parent() {
            return this.deviceTokenBlock;
        },
        title: 'Name',
        width: 'large',
        isMandatory: true,
    })
    name: ui.fields.Text;

    @ui.decorators.dateField<SysDeviceToken>({
        parent() {
            return this.deviceTokenBlock;
        },
        title: 'Expiration',
        bind: 'expiration',
        isHidden: true,
    })
    expiration: ui.fields.Date;

    @ui.decorators.textField<SysDeviceToken>({
        parent() {
            return this.deviceTokenBlock;
        },
        title: 'Token',
        bind: 'tokenId',
        isHidden: true,
    })
    tokenId: ui.fields.Text;

    @ui.decorators.linkField<SysDeviceToken>({
        parent() {
            return this.deviceTokenBlock;
        },
        title: 'Device Login Link',
        isFullWidth: true,
        isHidden() {
            return !this.$.recordId;
        },
        onClick() {
            if (this.loginUrl.value) {
                this.$.router.goToExternal(this.loginUrl.value);
            }
        },
    })
    loginUrl: ui.fields.Link;

    @ui.decorators.linkField<SysDeviceToken>({
        parent() {
            return this.deviceTokenBlock;
        },
        title: 'Device Test Link',
        isFullWidth: true,
        isHidden() {
            return !this.$.recordId;
        },
        onClick() {
            if (this.loginTestUrl.value) {
                this.$.router.goToExternal(this.loginTestUrl.value);
            }
        },
    })
    loginTestUrl: ui.fields.Link;

    @ui.decorators.pageAction<SysDeviceToken>({
        title: 'Save',
        onError(err) {
            this.$.showToast(
                ui.localize(
                    '@sage/xtrem-system/sys_device_token_creation_error',
                    'Device token creation failed: {{errorMessage}}',
                    { errorMessage: err },
                ),
                { type: 'error' },
            );
        },
        async onClick() {
            if (!this.tokenId.value) {
                const response = await this.$.graph
                    .node('@sage/xtrem-system/SysDeviceToken')
                    .mutations.createDeviceToken({ tokenId: true, expires: true }, {})
                    .execute();

                if (response) {
                    this.tokenId.value = response.tokenId;
                    this.expiration.value = response.expires;

                    await this.$standardSaveAction.execute(true);
                }
            } else {
                // Through this action we update the name of the device token
                await this.$standardSaveAction.execute(true);
            }
        },
    })
    createDeviceToken: ui.PageAction;

    @ui.decorators.pageAction<SysDeviceToken>({
        title: 'Delete',
        icon: 'bin',
        iconColor: ui.tokens.colorsSemanticNegative500,
        onError(err) {
            this.$.showToast(
                ui.localize(
                    '@sage/xtrem-system/sys_device_token_deletion_error',
                    'Device token deletion failed: {{errorMessage}}',
                    { errorMessage: err },
                ),
                { type: 'error' },
            );
        },
        async onClick() {
            const response = await this.$.graph
                .node('@sage/xtrem-system/SysDeviceToken')
                .mutations.deleteDeviceToken(true, {
                    tokenId: this.tokenId.value ?? undefined,
                })
                .execute();

            if (response) {
                await this.$standardDeleteAction.execute(true);
            }
        },
    })
    deleteDeviceToken: ui.PageAction;
}
