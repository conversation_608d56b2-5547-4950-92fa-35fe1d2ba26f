import { GraphApi } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import { administration } from '../menu-items/administration';

@ui.decorators.page<TenantInformation>({
    title: 'Tenant information',
    module: 'system',
    isTransient: true,
    skipDirtyCheck: true,
    access: { node: '@sage/xtrem-system/SysTenant', bind: 'getTenantInformation' },
    mode: 'tabs',
    menuItem: administration,
    async onLoad() {
        const tenantInformation = await this.getTenantInformation();
        this.tenantId.value = tenantInformation.tenantId;
        this.tenantVersion.value = tenantInformation.version;
        this.sumologicLinkText = tenantInformation.sumologicLink;
        this.resetTenantId.value = null;
    },
})
export class TenantInformation extends ui.Page<GraphApi> {
    sumologicLinkText: string;

    @ui.decorators.section<TenantInformation>({
        title: 'General',
        isTitleHidden: true,
    })
    generalSection: ui.containers.Section;

    @ui.decorators.block<TenantInformation>({
        parent() {
            return this.generalSection;
        },
    })
    generalBlock: ui.containers.Block;

    @ui.decorators.staticContentField<TenantInformation>({
        parent() {
            return this.generalBlock;
        },
        title: 'Tenant ID',
        width: 'medium',
    })
    tenantId: ui.fields.StaticContent;

    @ui.decorators.staticContentField<TenantInformation>({
        parent() {
            return this.generalBlock;
        },
        title: 'Tenant version',
        width: 'small',
    })
    tenantVersion: ui.fields.StaticContent;

    @ui.decorators.buttonField<TenantInformation>({
        parent() {
            return this.generalBlock;
        },
        onClick() {
            this.$.router.goToExternal(this.sumologicLinkText);
        },
        isHidden() {
            return !this.sumologicLinkText;
        },
        map() {
            return ui.localize(
                '@sage/xtrem-system/pages__tenant_information__sumologic_button_text',
                'View Sumo Logic logs',
            );
        },
        helperText: 'You need to be logged in to Sumo Logic before you can view the logs.',
    })
    sumologicLink: ui.fields.Button;

    async getTenantInformation(): Promise<{ version: string; tenantId: string; sumologicLink: string }> {
        const tenantInformation = await this.$.graph
            .node('@sage/xtrem-system/SysTenant')
            .queries.getTenantInformation({ version: true, tenantId: true, sumologicLink: true }, {})
            .execute();
        if (tenantInformation) {
            return tenantInformation;
        }
        return { version: '', tenantId: '', sumologicLink: '' };
    }

    @ui.decorators.section<TenantInformation>({
        title: 'Reset',
        isTitleHidden: true,
        access: { node: '@sage/xtrem-system/SysTenant', bind: 'resetTenantDocuments' },
    })
    resetTenantSection: ui.containers.Section;

    @ui.decorators.block<TenantInformation>({
        width: 'large',
        parent() {
            return this.resetTenantSection;
        },
    })
    tenantBlock: ui.containers.Block;

    @ui.decorators.staticContentField<TenantInformation>({
        isTransient: true,
        isFullWidth: true,
        parent() {
            return this.tenantBlock;
        },
        content:
            'Reset tenant data will delete all the movements (such as sales and purchase orders, stock level and movements).\nThis operation cannot be reverted. Are you sure?',
    })
    resetDataDescription: ui.fields.StaticContent;

    @ui.decorators.buttonField<TenantInformation>({
        parent() {
            return this.tenantBlock;
        },
        isTransient: true,
        map() {
            return ui.localize(
                '@sage/xtrem-system/pages__tenant_information__reset_tenant_data_button_text',
                'Reset tenant data',
            );
        },
        onClick() {
            this.setResetTenantDataConfirmVisibility(true);
        },
    })
    resetTenantData: ui.fields.Button;

    @ui.decorators.textField<TenantInformation>({
        parent() {
            return this.tenantBlock;
        },
        isTransient: true,
        isFullWidth: true,
        isMandatory: true,
        isHidden: true,
        title: 'Confirm Tenant ID',
        helperText: 'Please type the tenant ID to delete',
    })
    resetTenantId: ui.fields.Text;

    @ui.decorators.buttonField<TenantInformation>({
        parent() {
            return this.tenantBlock;
        },
        isTransient: true,
        map() {
            return ui.localize(
                '@sage/xtrem-system/pages__tenant_information__confirm_reset_tenant_data_button_text',
                'Confirm reset tenant data',
            );
        },
        isHidden: true,
        async onClick() {
            // We have to manually trigger the validation of the resetTenantId field because it's transient
            if ((await this.resetTenantId.validate()) != null) return;
            const result = await this.$.graph.raw(
                `mutation{xtremSystem{sysTenant{resetTenantDocuments(tenantId:"${this.resetTenantId.value}")}}}`,
            );
            ui.console.log('resetTenantDocuments', result);
            if ((!result.diagnoses.length || result.diagnoses.length === 0) && !result.message) {
                this.$.showToast(
                    ui.localize(
                        '@sage/xtrem-system/pages__service_option_state__reset_tenant_data_done',
                        'Tenant data reset done',
                    ),
                    { type: 'success' },
                );
                this.setResetTenantDataConfirmVisibility(false);
            } else {
                this.$.showToast(
                    ui.localize(
                        '@sage/xtrem-system/pages__service_option_state__reset_tenant_data_error',
                        result.diagnoses[0].message,
                    ),
                    { type: 'error' },
                );
            }
        },
    })
    confirmResetTenantData: ui.fields.Button;

    @ui.decorators.buttonField<TenantInformation>({
        parent() {
            return this.tenantBlock;
        },
        isTransient: true,
        isHidden: true,
        map() {
            return ui.localize(
                '@sage/xtrem-system/pages__tenant_information__cancel_reset_tenant_data_button_text',
                'Cancel',
            );
        },
        async onClick() {
            this.resetTenantId.value = null;
            this.setResetTenantDataConfirmVisibility(false);
        },
    })
    cancelResetTenantData: ui.fields.Button;

    setResetTenantDataConfirmVisibility(isVisible: boolean) {
        this.resetTenantData.isHidden = isVisible;
        this.confirmResetTenantData.isHidden = !isVisible;
        this.cancelResetTenantData.isHidden = !isVisible;
        this.resetTenantId.isHidden = !isVisible;
    }
}
