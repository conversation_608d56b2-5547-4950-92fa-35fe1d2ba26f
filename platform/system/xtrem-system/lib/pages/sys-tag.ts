import { GraphApi } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import { setApplicativePageCrudActions } from '../client-functions/applicative-crud-actions';
import { applicationData } from '../menu-items/application-data';

@ui.decorators.page<SysTag>({
    node: '@sage/xtrem-system/SysTag',
    title: 'Tag',
    mode: 'default',
    module: 'system',
    menuItem: applicationData,
    headerDropDownActions() {
        return [this.$standardDeleteAction];
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            actions: [],
        });
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            actions: [],
        });
    },
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'name', title: 'Name' }),
            line2: ui.nestedFields.text({ bind: 'description', title: 'Description' }),
        },
    },
    createAction() {
        return this.$standardNewAction;
    },
})
export class SysTag extends ui.Page<GraphApi> {
    @ui.decorators.section<SysTag>({
        title: 'General',
        isTitleHidden: true,
    })
    generalSection: ui.containers.Section;

    @ui.decorators.block<SysTag>({
        parent() {
            return this.generalSection;
        },
    })
    generalBlock: ui.containers.Block;

    @ui.decorators.textField<SysTag>({
        parent() {
            return this.generalBlock;
        },
        title: 'Name',
    })
    name: ui.fields.Text;

    @ui.decorators.textField<SysTag>({
        parent() {
            return this.generalBlock;
        },
        title: 'Description',
    })
    description: ui.fields.Text;
}
