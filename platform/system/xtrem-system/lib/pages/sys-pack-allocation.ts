import { GraphApi, SysPackVersion } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<SysPackAllocation>({
    title: 'Package Activation',
    node: '@sage/xtrem-system/SysPackAllocation',
    module: 'system',
    // menuItem: applicationData,
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.reference({
                bind: 'package',
                valueField: 'name',
                node: '@sage/xtrem-system/SysPackVersion',
                tunnelPage: undefined,
            }),
            line2: ui.nestedFields.checkbox({ bind: 'isActive' }),
            lineRight: ui.nestedFields.reference({
                title: 'Version',
                bind: 'package',
                valueField: 'version',
                node: '@sage/xtrem-system/SysPackVersion',
                tunnelPage: undefined,
            }),
        },
        optionsMenu: [
            {
                title: 'All',
                graphQLFilter: {
                    _and: [{ isActivable: true }, { package: { isHidden: { _eq: false } } }],
                },
            },
            {
                title: 'Packages',
                graphQLFilter: {
                    _and: [{ isActivable: true }, { package: { isHidden: { _eq: false } } }],
                },
            },
        ],
    },
})
export class SysPackAllocation extends ui.Page<GraphApi> {
    @ui.decorators.section<SysPackAllocation>({
        title: 'General',
        isTitleHidden: true,
    })
    generalSection: ui.containers.Section;

    @ui.decorators.block<SysPackAllocation>({
        parent() {
            return this.generalSection;
        },
    })
    generalBlock: ui.containers.Block;

    @ui.decorators.switchField<SysPackAllocation>({
        parent() {
            return this.generalBlock;
        },
        title: 'Active',
        width: 'small',
        async onChange() {
            // In onChange isActive has the new expected value
            const mutation = this.isActive.value
                ? this.$.graph
                      .node('@sage/xtrem-system/SysPackAllocation')
                      .mutations.activate(true, { packageId: this.package.value?.name })
                : this.$.graph
                      .node('@sage/xtrem-system/SysPackAllocation')
                      .mutations.deactivate(true, { packageId: this.package.value?.name });

            await mutation
                .execute()
                .then(value =>
                    value
                        ? this.$.showToast(ui.localize('@sage/xtrem-system/package-activated', 'Package activated'), {
                              type: 'success',
                          })
                        : this.$.showToast(
                              ui.localize('@sage/xtrem-system/package-deactivated', 'Package deactivated'),
                              { type: 'error' },
                          ),
                )
                .catch(e => {
                    this.$.showToast(e.message, { timeout: 0, type: 'error' });
                });
            this.$.setPageClean();
        },
    })
    isActive: ui.fields.Switch;

    @ui.decorators.separatorField<SysPackAllocation>({
        parent() {
            return this.generalBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    isActiveSeparator: ui.fields.Separator;

    @ui.decorators.switchField<SysPackAllocation>({
        parent() {
            return this.generalBlock;
        },
        title: 'Is activatable',
        width: 'small',
        isReadOnly: true,
        isHidden: true,
    })
    isActivable: ui.fields.Switch;

    @ui.decorators.separatorField<SysPackAllocation>({
        parent() {
            return this.generalBlock;
        },
        isFullWidth: true,
        isHidden: true,
    })
    separatorFromIsBundle: ui.fields.Separator;

    @ui.decorators.referenceField<SysPackAllocation, SysPackVersion>({
        parent() {
            return this.generalBlock;
        },
        bind: 'package',
        node: '@sage/xtrem-system/SysPackVersion',
        title: 'Package',
        valueField: 'name',
        isReadOnly: true,
        width: 'large',
    })
    package: ui.fields.Reference;

    @ui.decorators.referenceField<SysPackAllocation, SysPackVersion>({
        parent() {
            return this.generalBlock;
        },
        bind: 'package',
        node: '@sage/xtrem-system/SysPackVersion',
        title: 'Version',
        valueField: 'version',
        isReadOnly: true,
        width: 'large',
    })
    version: ui.fields.Reference;

    @ui.decorators.separatorField<SysPackAllocation>({
        parent() {
            return this.generalBlock;
        },
        isHidden: true,
    })
    separatorFromBundle: ui.fields.Separator;
}
