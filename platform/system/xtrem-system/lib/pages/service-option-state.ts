import { GraphApi, SysServiceOptionState } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import * as PillColorServiceOptionStatus from '../client-functions/pill-color';
import { applicationData } from '../menu-items/application-data';

@ui.decorators.page<ServiceOptionState, SysServiceOptionState>({
    title: 'Service Options',
    objectTypeSingular: 'Service Option',
    objectTypePlural: 'Service Options',
    node: '@sage/xtrem-system/SysServiceOptionState',
    menuItem: applicationData,
    priority: 100,
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({
                bind: { serviceOption: { optionName: true } },
                title: 'Name',
            }),
            line2: ui.nestedFields.text({
                bind: { serviceOption: { description: true } },
                title: 'Description',
            }),
            line3: ui.nestedFields.checkbox({
                bind: 'isActive',
                title: 'Active',
            }),
            line_4: ui.nestedFields.label({
                bind: { serviceOption: { status: true } },
                title: 'Status',
                optionType: '@sage/xtrem-system/ServiceOptionStatus',
                style: (_id, rowValue) =>
                    PillColorServiceOptionStatus.getLabelColorByStatus(
                        'ServiceOptionStatus',
                        rowValue.serviceOption?.status,
                    ),
            }),
        },
        onSelect() {
            return true;
        },
        dropdownActions: [
            {
                title: 'Activate',
                icon: 'tick_circle',
                isHidden(_id, rowData) {
                    return rowData.isActive;
                },
                async onClick(_id, rowData): Promise<void> {
                    try {
                        await this.$.dialog.confirmation(
                            'info',
                            ui.localize(
                                '@sage/xtrem-system/confirm-activate-service-option-title',
                                'Activate service option',
                            ),
                            ui.localize(
                                '@sage/xtrem-system/confirm-activate-service-option-message',
                                'You are about to activate service option: {{serviceOptionName}}.',
                                { serviceOptionName: rowData.serviceOption?.optionName },
                            ),
                        );
                    } catch {
                        return;
                    }
                    await this.$.graph.update({ _id, values: { isActive: true } });
                    this.$.router.hardRefresh();
                },
            },
            {
                title: 'Deactivate',
                icon: 'cross_circle',
                isHidden(_id, rowData) {
                    return !rowData.isActive;
                },
                async onClick(_id, rowData): Promise<void> {
                    try {
                        await this.$.dialog.confirmation(
                            'info',
                            ui.localize(
                                '@sage/xtrem-system/confirm-deactivate-service-option-title',
                                'Deactivate service option',
                            ),
                            ui.localize(
                                '@sage/xtrem-system/confirm-deactivate-service-option-message',
                                'You are about to deactivate service option: {{serviceOptionName}}.',
                                { serviceOptionName: rowData.serviceOption?.optionName },
                            ),
                        );
                    } catch {
                        return;
                    }
                    await this.$.graph.update({ _id, values: { isActive: false } });
                    this.$.router.hardRefresh();
                },
            },
        ],
        optionsMenu: [
            {
                title: 'All',
                graphQLFilter: {
                    serviceOption: { isHidden: { _eq: false } },
                    isActivable: { _eq: true },
                    isHiddenByConfig: { _eq: false },
                },
            },
        ],
    },
})
export class ServiceOptionState extends ui.Page<GraphApi, SysServiceOptionState> {}
