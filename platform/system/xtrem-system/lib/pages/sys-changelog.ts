import { GraphApi } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import { administration } from '../menu-items/administration';

@ui.decorators.page<SysChangelog>({
    title: 'Changelog',
    node: '@sage/xtrem-system/SysChangelog',
    module: 'system',
    menuItem: administration,
    navigationPanel: {
        onSelect() {
            return true;
        },
        orderBy: {
            changeDate: -1,
        },
        listItem: {
            title: ui.nestedFields.text({
                bind: 'hash',
                title: 'Hash',
            }),
            line2: ui.nestedFields.text({
                bind: 'message',
                title: 'Message',
            }),
            line3: ui.nestedFields.text({
                bind: 'changeDate',
                title: 'Change timestamp',
            }),
        },
    },
})
export class SysChangelog extends ui.Page<GraphApi> {}
