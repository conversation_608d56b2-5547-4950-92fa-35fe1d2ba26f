import { GraphApi, SysClientUserSettings } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import { setApplicativePageCrudActions } from '../client-functions/applicative-crud-actions';
import { confirmDialogToBoolean } from '../client-functions/utils';

@ui.decorators.page<ClientUserSettingsList>({
    node: '@sage/xtrem-system/User',
    access: {
        bind: 'updateClientSettings',
    },
    module: 'system',
    title: 'Manage views',
    businessActions() {
        return [this.$standardCancelAction, this.save];
    },
    onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.save,
            cancel: this.$standardCancelAction,
            actions: [],
        });
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.save,
            cancel: this.$standardCancelAction,
            actions: [],
        });
    },
    async defaultEntry() {
        const result = await this.$.graph.raw('{userInfo{_id}}', true, true);
        return result.userInfo._id;
    },
})
export class ClientUserSettingsList extends ui.Page<GraphApi> {
    @ui.decorators.pageAction({
        title: 'Save',
        access: {
            bind: 'updateClientSettings',
        },
        onError() {
            return ui.localize('@sage/xtrem-system/failed-to-save-view', 'An error occurred while saving the view.');
        },
        async onClick() {
            const validationResults = await this.$.page.validateWithDetails(true);
            if (validationResults.blockingErrors.length !== 0) {
                this.$.showValidationToast(validationResults);
                return;
            }

            const that = this as any;
            await this.$.graph
                .node('@sage/xtrem-system/User')
                .mutations.updateClientSettings(
                    {
                        clientSettings: {
                            query: {
                                __args: {
                                    filter: JSON.stringify({
                                        screenId: that.$.queryParameters.screenId,
                                        elementId: that.$.queryParameters.elementId,
                                    }),
                                    first: 1,
                                    orderBy: JSON.stringify({ _id: -1 }),
                                },
                                edges: { node: { _id: true } },
                            },
                        },
                    },
                    {
                        clientSettings: this.$.values.clientSettings,
                    },
                )
                .execute();

            this.$.finish();
        },
    })
    save: ui.PageAction;

    @ui.decorators.section<ClientUserSettingsList>({
        title: 'General',
        isTitleHidden: true,
    })
    generalSection: ui.containers.Section;

    @ui.decorators.tableField<ClientUserSettingsList, SysClientUserSettings>({
        parent() {
            return this.generalSection;
        },

        isTitleHidden: true,
        canSelect: false,
        mobileCard: null,
        orderBy: { title: 1 },
        columns: [
            ui.nestedFields.text({
                bind: 'title',
                title: 'Name',
                isMandatory: true,
            }),
            ui.nestedFields.text({ bind: 'description' }),
            ui.nestedFields.text({ bind: '_id', isHidden: true }),
            ui.nestedFields.text({ bind: 'content', isHidden: true }),
        ],
        filter() {
            const that = this as any;
            return {
                screenId: { _eq: that.$.queryParameters.screenId },
                elementId: { _eq: that.$.queryParameters.elementId },
            };
        },
        dropdownActions: [
            {
                id: 'remove',
                title: 'Remove',
                icon: 'delete',
                async onClick(recordId) {
                    try {
                        const result = await confirmDialogToBoolean(
                            this.$.dialog.confirmation(
                                'warn',
                                ui.localize('@sage/xtrem-system/delete-view-warning-title', 'Delete view'),
                                ui.localize(
                                    '@sage/xtrem-system/delete-view-warning-title-message',
                                    'You are about to delete this view.',
                                ),
                                {
                                    acceptButton: {
                                        isDestructive: true,
                                    },
                                },
                            ),
                        );
                        if (result) this.clientSettings.removeRecord(recordId);
                    } catch (error) {
                        // Intentionally left blank
                    }
                },
            },
        ],
    })
    clientSettings: ui.fields.Table;
}
