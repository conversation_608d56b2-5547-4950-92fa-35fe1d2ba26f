import { GraphApi, SysClientUserSettings } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import { setApplicativePageCrudActions } from '../client-functions/applicative-crud-actions';

@ui.decorators.page<ClientUserSettingsEdit>({
    title: 'Create a view',
    node: '@sage/xtrem-system/SysClientUserSettings',
    businessActions() {
        return [this.$standardCancelAction, this.save];
    },
    onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.save,
            cancel: this.$standardCancelAction,
            actions: [],
        });
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.save,
            cancel: this.$standardCancelAction,
            actions: [],
        });
    },
})
export class ClientUserSettingsEdit extends ui.Page<GraphApi, SysClientUserSettings> {
    @ui.decorators.pageAction({
        title: 'Save',
        onError() {
            return ui.localize('@sage/xtrem-system/failed-to-save-view', 'An error occurred while saving the view.');
        },
        async onClick() {
            const validationResults = await this.$.page.validateWithDetails(true);
            if (validationResults.blockingErrors.length !== 0) {
                this.$.showValidationToast(validationResults);
                return;
            }

            const updateQueryResult = await this.$.graph
                .node('@sage/xtrem-system/User')
                .mutations.updateClientSettings(
                    {
                        clientSettings: {
                            query: {
                                __args: {
                                    filter: JSON.stringify({
                                        screenId: this.screenId.value,
                                        elementId: this.elementId.value,
                                    }),
                                    first: 1,
                                    orderBy: JSON.stringify({ _id: -1 }),
                                },
                                edges: { node: { _id: true } },
                            },
                        },
                    },
                    {
                        clientSettings: [
                            {
                                _action: 'create',
                                screenId: this.screenId.value || '',
                                elementId: this.elementId.value || '',
                                title: this.title.value || '',
                                description: this.description.value || '',
                                content: this.content.value || '',
                            },
                        ],
                    },
                )
                .execute();

            this.$.finish({
                _id: updateQueryResult.clientSettings.query.edges[0].node._id,
            });
        },
    })
    save: ui.PageAction;

    @ui.decorators.section<ClientUserSettingsEdit>({
        title: 'General',
        isTitleHidden: true,
    })
    generalSection: ui.containers.Section;

    @ui.decorators.block<ClientUserSettingsEdit>({
        width: 'large',
        parent() {
            return this.generalSection;
        },
    })
    generalBlock: ui.containers.Block;

    @ui.decorators.textField<ClientUserSettingsEdit>({
        parent() {
            return this.generalBlock;
        },
        isFullWidth: true,
        isMandatory: true,
        title: 'Name',
    })
    title: ui.fields.Text;

    @ui.decorators.textAreaField<ClientUserSettingsEdit>({
        parent() {
            return this.generalBlock;
        },
        isFullWidth: true,
        rows: 6,
    })
    description: ui.fields.TextArea;

    @ui.decorators.textField<ClientUserSettingsEdit>({})
    content: ui.fields.Text;

    @ui.decorators.textField<ClientUserSettingsEdit>({})
    screenId: ui.fields.Text;

    @ui.decorators.textField<ClientUserSettingsEdit>({})
    elementId: ui.fields.Text;
}
