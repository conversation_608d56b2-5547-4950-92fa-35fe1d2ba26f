import { ConfigManager, Context, NodeStatus, SystemError, getRegion } from '@sage/xtrem-core';
import { UserEventHandler, getUserEventHandler } from '@sage/xtrem-user-event';
import { loggers } from '../loggers';

const logger = loggers.userEvents;

export interface UserEventOptions {
    userId: number;
    status: NodeStatus;
    email: string;
}

export interface UserCreateModifyOptions extends UserEventOptions {
    isActive: boolean;
    billingRole: string;
    external?: boolean;
    operatorHash?: string;
}

export interface UserDeleteOptions extends UserEventOptions {}

export interface SupportAccessOptions {
    validUntil: Date;
    readOnlyAccess?: boolean;
}

export class UserEventsManager {
    private static region: string | undefined;

    private static get handler(): UserEventHandler {
        if (this.region == null) {
            this.region = getRegion();
        }

        return getUserEventHandler({ region: this.region });
    }

    static userCreated(context: Context, options: UserCreateModifyOptions): Promise<string> {
        const { userId, status, email, isActive, billingRole, external, operatorHash } = options;
        const { tenantId, currentLocale } = context;
        if (!tenantId) {
            throw new SystemError('no tenant in the current context');
        }
        logger.info(
            `User change notification: app=${ConfigManager.current.app}, tenant=${tenantId}, id=${userId}, state=${status}, isActive=${isActive}, region=${UserEventsManager.region}`,
        );
        return this.handler.userCreated(
            ConfigManager.current.app ?? 'sdmo',
            tenantId,
            email,
            currentLocale,
            isActive,
            billingRole,
            external,
            operatorHash,
        );
    }

    static userModified(context: Context, options: UserCreateModifyOptions): Promise<string> {
        const { userId, status, email, isActive, billingRole, external, operatorHash } = options;
        const { tenantId, currentLocale } = context;
        if (!tenantId) {
            throw new SystemError('no tenant in the current context');
        }
        logger.info(
            `User change notification: app=${ConfigManager.current.app}, tenant=${tenantId}, id=${userId}, state=${status}, isActive=${isActive}, region=${UserEventsManager.region}`,
        );
        return this.handler.userModified(
            ConfigManager.current.app ?? 'sdmo',
            tenantId,
            email,
            currentLocale,
            isActive,
            billingRole,
            external,
            operatorHash,
        );
    }

    static userDeleted(context: Context, options: UserDeleteOptions): Promise<string> {
        const { userId, status, email } = options;
        const { tenantId } = context;
        if (!tenantId) {
            throw new SystemError('no tenant in the current context');
        }
        logger.info(
            `User change notification: tenant=${tenantId}, id=${userId}, state=${status}, region=${UserEventsManager.region}`,
        );
        return this.handler.userDeleted(ConfigManager.current.app, tenantId, email);
    }

    static async enableSupportAccess(context: Context, options: SupportAccessOptions): Promise<string> {
        const { validUntil, readOnlyAccess } = options;
        const email = (await context.user)?.email || '';
        if (!context.tenantId) {
            throw new SystemError('no tenant in the current context');
        }
        return this.handler.enableSupportAccess(
            ConfigManager.current.app,
            context.tenantId,
            email,
            validUntil,
            readOnlyAccess,
        );
    }

    static async disableSupportAccess(context: Context): Promise<string> {
        const email = (await context.user)?.email || '';
        if (!context.tenantId) {
            throw new SystemError('no tenant in the current context');
        }
        return this.handler.disableSupportAccess(ConfigManager.current.app, context.tenantId, email);
    }
}
