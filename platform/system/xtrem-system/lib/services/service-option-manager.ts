/* eslint-disable class-methods-use-this */
import { asyncArray, Context, DebugMetrics, Dict, ServiceOption, ServiceOptionManager } from '@sage/xtrem-core';
import { LogicError } from '@sage/xtrem-shared';
import * as _ from 'lodash';
import { ServiceOptionStatus } from '../enums/_index';
import * as xtremSystem from '../index';
import { loggers } from '../loggers';

const logger = loggers.serviceOption;

// This interface describes that critical data that we need to compute if a service option is active or not.
// This information is cached into the context and global caches.
interface ServiceOptionSummary {
    name: string;
    status: ServiceOptionStatus;
    isActivable: boolean;
    isActive: boolean;
}

export class SysServiceOptionManager extends ServiceOptionManager {
    private static isSummaryEnabled(summary: ServiceOptionSummary): boolean {
        return summary.isActive && summary.isActivable && this.isServiceOptionStatusEnabled(summary.status);
    }

    // Returns the cached summaries of service option and service option state data
    // Methods that test if service options are active or not should fetch the service option states with this method
    private getCachedSummaries(context: Context): Promise<Dict<ServiceOptionSummary>> {
        return DebugMetrics.withMetrics('ServiceOption', 'summaries.fetched', () =>
            context.getCachedValue({
                category: context.getNodeCacheCategory(xtremSystem.nodes.SysServiceOptionState),
                key: 'ServiceOptionState.summaries',
                getValue: () =>
                    DebugMetrics.withMetrics('ServiceOption', 'summaries.missed', async () => {
                        const summaries = await context.select(
                            xtremSystem.nodes.SysServiceOptionState,
                            { serviceOption: { optionName: true, status: true }, isActivable: true, isActive: true },
                            { filter: {} },
                        );
                        return {
                            value: _.zipObject(
                                summaries.map(state => state.serviceOption.optionName),
                                summaries.map(state => ({
                                    name: state.serviceOption.optionName,
                                    status: state.serviceOption.status,
                                    isActivable: state.isActivable,
                                    isActive: state.isActive,
                                })),
                            ),
                        };
                    }),
                cacheInMemory: true,
                isolateInContext: false,
                ttlInSeconds: 3600,
            }),
        );
    }

    async isServiceOptionEnabled(context: Context, serviceOption: ServiceOption): Promise<boolean> {
        const summaries = await this.getCachedSummaries(context);
        const summary = summaries[serviceOption.name];
        if (!summary) {
            logger.debug(() => `The service option ${serviceOption.name} was not found.`);
            return false;
        }
        const isActive = SysServiceOptionManager.isSummaryEnabled(summary);
        logger.debug(() => `The service option ${serviceOption.name} isActive: ${isActive}`);
        return isActive;
    }

    async getEnabledServiceOptions(context: Context): Promise<ServiceOption[]> {
        return Object.values(await this.getCachedSummaries(context))
            .filter(summary => SysServiceOptionManager.isSummaryEnabled(summary))
            .map(summary => context.application.serviceOptionsByName[summary.name])
            .filter(serviceOption => !!serviceOption);
    }

    override async setServiceOptionActiveState(context: Context, serviceOption: ServiceOption, isActive: boolean) {
        const serviceOptionState = await context.read(
            xtremSystem.nodes.SysServiceOptionState,
            { serviceOption: `#${serviceOption.name}` },
            { forUpdate: true },
        );
        if ((await serviceOptionState.isActive) === isActive) return;

        await serviceOptionState.$.set({ isActive });
        await serviceOptionState.$.save();
    }

    /**
     * Creates missing service options (existing in the code but not in the database) and update existing ones (existing both in code and in db).
     */
    override async createOrUpdateServiceOptions(context: Context): Promise<void> {
        const serviceOptionsInDb = (
            await context.select(xtremSystem.nodes.SysServiceOption, { _id: true, optionName: true }, { filter: {} })
        ).reduce((total, serviceOption) => {
            total[serviceOption.optionName] = true;
            return total;
        }, {} as Dict<boolean>);

        logger.info('Create or update service options...');
        await asyncArray(Object.values(this.serviceOptionsByName)).forEach(async serviceOptionInCode => {
            const payload = {
                description: serviceOptionInCode.description,
                status: serviceOptionInCode.status,
                isHidden: serviceOptionInCode.isHidden,
                package: serviceOptionInCode.packageName,
                isActiveByDefault: serviceOptionInCode.isActiveByDefault,
            };
            const serviceOptionExistsInDb = !!serviceOptionsInDb[serviceOptionInCode.name];
            if (serviceOptionExistsInDb) {
                logger.info(`\t- updating service option ${serviceOptionInCode.name}`);
                await context.bulkUpdate(xtremSystem.nodes.SysServiceOption, {
                    set: payload,
                    where: { optionName: serviceOptionInCode.name },
                });
            } else {
                logger.info(`\t- creating service option ${serviceOptionInCode.name}`);
                const newServiceOption = await context.create(xtremSystem.nodes.SysServiceOption, {
                    ...payload,
                    optionName: serviceOptionInCode.name,
                });
                await newServiceOption.$.save();
            }
        });
    }

    /**
     * Deletes the obsolete service options (existing in the database but not in the code).
     */
    override async deleteObsoleteServiceOptions(context: Context): Promise<void> {
        logger.info('Delete obsolete service options...');
        const serviceOptionsInDb = await context.select(
            xtremSystem.nodes.SysServiceOption,
            { _id: true, optionName: true },
            { filter: {} },
        );
        // Delete the obsolete SysServiceOption records
        await asyncArray(serviceOptionsInDb.reverse())
            .filter(
                serviceOptionInDb =>
                    !Object.values(this.serviceOptionsByName).some(
                        serviceOptionInCode => serviceOptionInCode.name === serviceOptionInDb.optionName,
                    ),
            )
            .forEach(async obsoleteServiceOptionInDb => {
                logger.info(`\t- deleting obsolete service options ${obsoleteServiceOptionInDb.optionName}`);
                // We need to delete the service option states first on every tenant, as there is no cascading constraint
                // from service option to service option state
                // Note: here, we can't use the context.delete method because it would be bound to the current tenant
                // (SysServiceOptionState is not sharedByAllTenants) and if we loop over the existing tenants,
                // it will not work when we record the SQL files (the tenants will not be the same when recording
                // the file and when the file is applied).
                await context.executeSql(
                    `DELETE FROM ${context.schemaName}.sys_service_option_state WHERE service_option = $1`,
                    [obsoleteServiceOptionInDb._id],
                );
                await context.delete(xtremSystem.nodes.SysServiceOption, { _id: obsoleteServiceOptionInDb._id });
            });
    }

    override async createOrUpgradeServiceOptionStates(
        context: Context,
        serviceOptionActiveFlags?: Dict<boolean>,
    ): Promise<void> {
        const oldStates = await context.select(
            xtremSystem.nodes.SysServiceOptionState,
            { _id: true, isActive: true, serviceOption: { _id: true, optionName: true } },
            { filter: {} },
        );

        const sysServiceOptions = await context.select(
            xtremSystem.nodes.SysServiceOption,
            { _id: true, optionName: true },
            { filter: {} },
        );

        // We have to push the options to activate into a list, and activate them after the create/update loop
        // to ensure that their dependencies exist when we activate them.
        const activateList = [] as ServiceOption[];

        // Create the missing SysServiceOptionState records and collect the list of active options
        await asyncArray(this.sortedServiceOptions).forEach(async serviceOption => {
            const index = oldStates.findIndex(state => state.serviceOption.optionName === serviceOption.name);

            if (index < 0) {
                const isActive =
                    typeof serviceOptionActiveFlags?.[serviceOption.name] === 'boolean'
                        ? serviceOptionActiveFlags[serviceOption.name]
                        : serviceOption.isActiveByDefault;

                const sysServiceOption = sysServiceOptions.find(option => option.optionName === serviceOption.name);
                if (!sysServiceOption)
                    throw new LogicError(`${serviceOption.name}: service option not found in SysServiceOption table`);

                // Set active to false initially, to force propagation of true active to parent.
                const newServiceOptionState = await context.create(xtremSystem.nodes.SysServiceOptionState, {
                    serviceOption: sysServiceOption._id,
                    isActivable: true,
                    isActive: false,
                });
                await newServiceOptionState.$.save();
                if (isActive) activateList.push(serviceOption);
            } else {
                const oldState = oldStates[index];
                const isActive =
                    typeof serviceOptionActiveFlags?.[oldState.serviceOption.optionName] === 'boolean'
                        ? serviceOptionActiveFlags[serviceOption.name]
                        : oldState.isActive;

                if (isActive) activateList.push(serviceOption);
            }
        });

        // Delete the obsolete SysServiceOptionState records
        await asyncArray(oldStates)
            .filter(
                oldState =>
                    !this.sortedServiceOptions.some(
                        serviceOption => serviceOption.name === oldState.serviceOption.optionName,
                    ),
            )
            .forEach(async oldState => {
                await context.delete(xtremSystem.nodes.SysServiceOptionState, { _id: oldState._id });
            });

        if (activateList.length > 0) await this.activateServiceOptions(context, activateList);
    }
}
