import { Application, AsyncResponse, Context, funnel, Logger } from '@sage/xtrem-core';
import { PatchHistoryResult, SysPatchHistory } from '../../nodes/sys-patch-history';
import { loggers } from '../loggers';

/**
 * Abstract base class for data-patches.
 */
export abstract class BaseDataPatch {
    // will be assigned by the data-patch engine
    version: string;

    // will be assigned by the data-patch engine
    packageName: string;

    static patchHistoryFunnel = funnel(1);

    constructor(readonly name: string) {}

    /**
     * Executes the body of a data-patch and manages the related SysPatchHistory
     * @param writableContext
     * @param body
     */
    protected async executeAndHistorize(writableContext: Context, body: () => AsyncResponse<void>): Promise<void> {
        const payload = {
            packageName: this.packageName,
            patchName: this.name,
            version: this.version,
        };
        let result: PatchHistoryResult = 'running';
        try {
            await body();
            result = 'success';
        } catch (err) {
            result = 'failure';
        }

        // Critical section: all the data-patches will be run in parallel
        await BaseDataPatch.patchHistoryFunnel(async () => {
            let patchHistory = await writableContext.tryRead(SysPatchHistory, payload, { forUpdate: true });
            if (!patchHistory) {
                patchHistory = await writableContext.create(SysPatchHistory, payload);
            }
            await patchHistory.$.set({ result });
            await patchHistory.$.save();
        });
    }

    /**
     * Execute the data-patch
     * @param application
     * @internal
     */
    abstract executeFromApplication(application: Application): Promise<void>;

    // eslint-disable-next-line class-methods-use-this
    get logger(): Logger {
        return loggers.upgrade;
    }
}
