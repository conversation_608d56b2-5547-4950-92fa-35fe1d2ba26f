import { Application, asyncArray, Context, Package } from '@sage/xtrem-core';
import * as lodash from 'lodash';
import * as fsp from 'path';
import { SysPatchHistory } from '../../nodes/sys-patch-history';
import { loggers } from '../loggers';
import { UpgradePackContext } from '../upgrade/upgrade-pack-context';
import { BaseDataPatch } from './base-data-patch';

const logger = loggers.upgrade;

export abstract class DataPatchEngine {
    /**
     * Returns the list of dataPatches that should be applied, i.e. the list of dataPatches that are
     * in lib/patches/vX.Y.Z folders where X.Y.Z is in ]sysPackVersion(services-main); package.version(services-main)]
     * @returns
     */
    private static async _getDataPatchesToApply(context: Context, pack: Package): Promise<BaseDataPatch[]> {
        const allDataPatches: BaseDataPatch[] = [];
        const versionsToApply = await UpgradePackContext.getUpgradesToApply(pack, 'data-patches', false);
        if (versionsToApply.versions.length === 0) {
            logger.info(() => `\t- ${pack.name}@${pack.packageJson.version}: no data-patch`);
            return allDataPatches;
        }
        logger.info(
            () =>
                `\t- ${pack.name}@${pack.packageJson.version}: found ${versionsToApply.versions.length} dataPatches from folder ${versionsToApply.rootFolder}`,
        );
        versionsToApply.versions.forEach(version => {
            const indexFilename = fsp.join(versionsToApply.rootFolder, `v${version}`, 'index');
            logger.verbose(() => `\t- retrieve dataPatches from ${indexFilename}`);
            // eslint-disable-next-line import/no-dynamic-require, global-require
            const mod = require(indexFilename);
            if (!mod.dataPatches) {
                logger.verbose(() => `The file ${indexFilename} does not contain any 'dataPatches' constant.`);
                return;
            }
            const dataPatches = mod.dataPatches as BaseDataPatch[];
            dataPatches.forEach(p => {
                p.version = version;
                p.packageName = pack.name;
                allDataPatches.push(p);
            });
        });

        // Get all the failed data-patches
        const failedDataPatches = await context
            .query(SysPatchHistory, {
                filter: { packageName: pack.name, result: 'failure' },
                orderBy: { version: 1 },
            })
            .toArray();
        if (failedDataPatches.length > 0) {
            // Some data-patches failed: the list of data-patches is valid ONLY if it
            // contains ALL the previously failed patches
            const failedPatches = await asyncArray(failedDataPatches)
                .map(async p => `${await p.patchName}@${await p.version}`)
                .toArray();
            const planedPatches = allDataPatches.map(p => `${p.name}@${p.version}`);
            const missingPatches = lodash.difference(failedPatches, planedPatches);
            if (missingPatches.length > 0) {
                throw new Error(
                    `The following data-patches previously failed on ${pack.name} and must be reapplied ${missingPatches}`,
                );
            }
        }

        // Now, filter all the already applied data-patches

        // Get the list of successfully applied data-patches
        const successPatches = await context
            .query(SysPatchHistory, {
                filter: { packageName: pack.name, result: 'success' },
                orderBy: { version: 1 },
            })
            .map(async p => `${await p.patchName}@${await p.version}` as string)
            .toArray();

        // Only keep data-patches that were not executed yet
        return allDataPatches.filter(p => {
            const key = `${p.name}@${p.version}`;
            return !successPatches.includes(key);
        });
    }

    /**
     * Runs all the post-processes (rename the 'vlatest' folders with the version of each package, ...)
     */
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    static postProcess(application: Application): void {
        logger.info("Renaming 'latest' folders");
        // STDEN TODO
    }

    /**
     * Execute a set of data-patches
     * @param application
     * @param dataPatches
     */
    private static async _executeDataPatches(application: Application, dataPatches: BaseDataPatch[]): Promise<void> {
        await asyncArray(dataPatches).forEach(dataPatch => dataPatch.executeFromApplication(application));
    }

    static async applyDataPatches(application: Application): Promise<void> {
        logger.info(`Applying data-patches on application ${application.name}@${application.version}`);

        const packages = application.getPackages();
        await application.asRoot.withReadonlyContext(
            null,
            context =>
                asyncArray(packages).forEach(async pack => {
                    const patches = await DataPatchEngine._getDataPatchesToApply(context, pack);
                    await DataPatchEngine._executeDataPatches(application, patches);
                }),
            { description: () => 'ApplyDataPatches' },
        );
    }
}
