import { Application, Context } from '@sage/xtrem-core';
import { BaseDataPatch } from './base-data-patch';

/**
 * Abstract base class for Typescript data-patches.
 * To create a typescript data-patch, simply inherit from this class and override the `execute` function.
 * The execute function will be invoked as many time as needed:
 * - isSharedByAllTenants=true: invoked only once.
 * - isSharedByAllTenants=false: invoked once per tenant.
 */
export abstract class TypescriptDataPatch extends BaseDataPatch {
    /**
     *
     * @param name The name of the data-patch
     * @param isSharedByAllTenants Will this data-patch manage sharedByAllTenants tables ?
     */
    constructor(
        name: string,
        readonly isSharedByAllTenants: boolean = false,
    ) {
        super(name);
    }

    /**
     * Execute the data-patch
     * @param application
     * @internal
     */
    async executeFromApplication(application: Application): Promise<void> {
        const tenantIds: (string | null)[] = await application.asRoot.withReadonlyContext(
            null, // The context will only be used to query sys_tenant, which is shared
            // eslint-disable-next-line require-await
            async context => (this.isSharedByAllTenants ? [null] : Context.tenantManager.listTenantsIds(context)),
            { description: () => 'TypescriptDataPatch.listTenantIds' },
        );

        const allPromises = tenantIds.map(tenantId =>
            application.asRoot.withCommittedContext(
                tenantId,
                context => {
                    this.logger.info(`Run ${this.name}@${this.version} on tenant ${tenantId}`);
                    return this.executeAndHistorize(context, () => this.execute(context));
                },
                { description: () => `TypescriptDataPatch.executeAndHistorize(${tenantId})` },
            ),
        );

        await Promise.all(allPromises);
    }

    /**
     * Body of the typescript data-patch (to be defined by inheritors)
     * @param context a writable context to perform queries
     */
    protected abstract execute(context: Context): Promise<void>;
}
