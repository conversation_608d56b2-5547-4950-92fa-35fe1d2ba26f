import { Application } from '@sage/xtrem-core';
import { BaseDataPatch } from './base-data-patch';

/**
 * Executes a SQL command and return the raw (directly from postgres) result
 */
export type SqlRunner = {
    execute: (sqlCommand: string, args?: any[]) => Promise<any>;
    schemaName: string;
};

/**
 * Abstract base class for SQL data-patches.
 * To create a SQL data-patch, simply inherit from this class and override the `execute` function.
 * SQL data-patches are only allowed to execute SQL comands, they can't run any operation on nodes (they don't
 * have any context).
 * SQL data-patches must run global statements, through all the tenants (a SQL data-patch will only be
 * invoked once, not once per tenant)
 */
export abstract class SqlDataPatch extends BaseDataPatch {
    /**
     * Execute the data-patch
     * @param application
     * @internal
     */
    async executeFromApplication(application: Application): Promise<void> {
        this.logger.info(`Run ${this.name}`);
        await application.asRoot.withCommittedContext(
            null, // The context will only be used to run SQL queries
            async committedContext => {
                const pool = committedContext.sqlPool; // Use classic (not-priviledged) user to prevent schema changes
                await this.executeAndHistorize(committedContext, () =>
                    pool.withConnection(cnx =>
                        this.execute({
                            execute: (sqlCommand: string, args?: any[]) => pool.execute(cnx, sqlCommand, args),
                            schemaName: committedContext.schemaName,
                        }),
                    ),
                );
            },
            { description: () => 'SqlDataPatch.executeFromApplication' },
        );
    }

    /**
     * Body of the SQL data-patch (to be defined by inheritors)
     *
     * @param sqlRunner the function to use to execute SQL commands
     */
    protected abstract execute(sqlRunner: SqlRunner): Promise<void>;
}
