import { Application, Context, HotUpgradeManager, UpgradeMetricsType, withAdvisoryLock } from '@sage/xtrem-core';
import { loggers } from '../loggers';
import { SysPackageManager } from './sys-package-manager';
import { UpgradeSqlSchema } from './upgrade/upgrade-sql-schema';

/**
 * Upgrade manager
 */
export class SysHotUpgradeManager implements HotUpgradeManager {
    /**
     * Is a hot-upgrade needed ?
     * @param context
     * @returns
     */
    private static async _needsToExecuteHotUpgrade(context: Context): Promise<boolean> {
        const mainPackage = context.application.mainPackage;
        const packageVersion = mainPackage.packageJson.version;
        // Clean the package's cache to make sure we will get the exact version in db
        const packageManager = SysPackageManager.fromContext(context);
        const dbVersion = await packageManager.getCurrentVersion(mainPackage, context);
        if (dbVersion == null) throw new Error(`Could not retrieve the version of package ${mainPackage.packageName}`);
        return packageVersion !== dbVersion;
    }

    /**
     * Executes the hot upgrade of an application.
     * Can only be used on a released application.
     * @param application
     */
    // eslint-disable-next-line class-methods-use-this
    async executeHotUpgrade(application: Application, metrics?: UpgradeMetricsType): Promise<void> {
        const mainPackage = application.mainPackage;
        await application.asRoot.withCommittedContext(
            null,
            async context => {
                if (!(await SysHotUpgradeManager._needsToExecuteHotUpgrade(context))) {
                    loggers.upgrade.info(
                        `No hot-upgrade is required, package ${mainPackage.name} is already in version ${mainPackage.packageJson.version}`,
                    );
                    return;
                }
                // Note: hot upgrades are not triggered by any CLI command.
                // When a new image is created, it will be deployed in a classic way.
                // There may be more than 1 container running on the same database but only one
                // of them must execute the hot-upgrade. That's why we use an advisory lock to prevent
                // concurrent hot-upgrades.
                //
                // Double check strategy :
                // - 1st check before acquiring the lock
                // - 2nd check after acquiring the lock
                await withAdvisoryLock(context, 4854484059434301, async () => {
                    if (!(await SysHotUpgradeManager._needsToExecuteHotUpgrade(context))) {
                        // The 1st check outside the advisory lock passed so a hot-upgrade was required
                        // but now, the 2nd check fails, that means that another container with the same new image
                        // has already taken care of this hot-upgrade. The current container has nothing more to do
                        loggers.upgrade.info(
                            'A hot-upgrade was required but it was already managed by another container',
                        );
                        return;
                    }
                    await UpgradeSqlSchema.executeHotUpgrade(application, metrics);
                });
            },
            {
                description: () => 'executeHotUpgrade',
            },
        );
    }
}
