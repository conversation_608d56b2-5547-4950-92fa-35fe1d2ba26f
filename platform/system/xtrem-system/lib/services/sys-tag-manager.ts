import { ServiceOption, TagManager } from '@sage/xtrem-core';
import { SysTag } from '../nodes/sys-tag';
import { sysTag } from '../service-options/sys-tag';

/**
 * Tag manager
 */
export class SysTagManager implements TagManager {
    /**
     * Returns the node for tags
     */
    // eslint-disable-next-line class-methods-use-this
    getTagNode() {
        return SysTag;
    }

    /**
     * Returns the serviceOption for tags
     */
    // eslint-disable-next-line class-methods-use-this
    getServiceOption(): ServiceOption {
        return sysTag;
    }
}
