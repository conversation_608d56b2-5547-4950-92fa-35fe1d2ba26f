import { Context, CsvChecksumManager, datetime } from '@sage/xtrem-core';
import * as crypto from 'crypto';
import { SysCsvChecksum } from '../nodes/_index';

/**
 * Management of CSV's checksum.
 * Every time a CSV file is loaded, its checksum is stored (sys_csv_checksum table). This allow
 * the system to skip the next loading of the same CSV file, if it was not changed.
 */
export class SysCsvChecksumManager implements CsvChecksumManager {
    /**
     * Return the checksum of the last successfully loaded Csv for a specific factory
     */
    // eslint-disable-next-line class-methods-use-this
    async getLastAppliedChecksum(context: Context, factoryName: string): Promise<string | undefined> {
        const last = await context.tryRead(SysCsvChecksum, {
            factoryName,
            tenantId: context.tenantId || '',
        });
        if (!last) return undefined;
        return last.checksum;
    }

    /**
     * Store the checksum of the last successfully loaded Csv for a specific factory
     */
    // eslint-disable-next-line class-methods-use-this
    async setLastAppliedChecksum(context: Context, factoryName: string, checksum: string): Promise<void> {
        const checksumFactory = context.application.getFactoryByConstructor(SysCsvChecksum);
        // We need to do an explicit INSERT ... ON CONFLICT ... DO UPDATE  here, as the _id for a specific factory can be different on different clusters and upsert with retry
        // wiil prioritize the _id as the on conflict check, and this raises the risk of updating an old factory with a new one. To be safe we construct the insert statement here.

        const sql = `INSERT INTO ${context.application.schemaName}.${checksumFactory.tableName} (_source_id,factory_name,checksum,tenant_id,_update_tick,_create_stamp,_update_stamp)
                    VALUES ($1,$2,$3,$4,$5,$6,$7)
                    ON CONFLICT (tenant_id,factory_name)
                    DO UPDATE SET (_source_id,factory_name,checksum,tenant_id,_update_tick,_update_stamp) = ($1,$2,$3,$4,$5,$7)
                    RETURNING _create_stamp,_update_stamp,_update_tick,_id`;

        const nowValue = datetime.now().toString();
        const args = [
            '',
            factoryName,
            checksum,
            context.tenantId || '', // The tenant will be null for shared factories
            1,
            nowValue,
            nowValue,
        ];
        await context.executeSql(sql, args);
    }

    /**
     * Compute the checksum of the content of a CSV file
     */
    // eslint-disable-next-line class-methods-use-this
    computeChecksum(csvContent: string): string {
        return crypto.createHash('md5').update(csvContent, 'utf8').digest('hex');
    }
}
