import {
    Application,
    asyncArray,
    Context,
    enums as coreEnums,
    DataValidationManager,
    DataValidator,
    InstanceDataValidationResult,
    Node,
    StaticThis,
    ValidationSeverity,
} from '@sage/xtrem-core';
import { loggers } from '../loggers';
import { SysDataValidationReport } from '../nodes/sys-data-validation-report';

export class SysDataValidationManager implements DataValidationManager {
    private readonly _validators: {
        validator: DataValidator;
        nodeClass: StaticThis<Node>;
    }[] = [];

    /**
     * Registers a data validator for a specific Node class.
     * @param nodeClass The Node class to register the validator for.
     * @param validator The data validator to register. If not provided, a default validator will be used.
     */
    registerValidator<T extends Node>(nodeClass: StaticThis<T>, validator?: DataValidator<T>): void {
        this._validators.push({
            nodeClass,
            validator: validator || defaultDataValidator,
        });
    }

    /**
     * Retrieves all registered data validators.
     */
    getValidators(): DataValidator[] {
        return this._validators.map(v => v.validator);
    }

    /**
     * Builds the validation reports for the given application (one report per tenant)
     */
    async buildReports(application: Application): Promise<void> {
        const tenantIds = await application.withReadonlyContext(null, context =>
            Context.tenantManager.listTenantsIds(context),
        );

        await asyncArray(tenantIds)
            .map(tenantId => {
                loggers.dataValidation.info(`Generating data validation report for tenant ${tenantId}`);
                return application.withCommittedContext(tenantId, context => this.buildReport(context));
            })
            .toArray();
    }

    /**
     * Builds a validation report for the given writable context (will only concern the tenant of the writable context).
     * @param writableContext The writable context to generate the report for.
     * @returns The _id of the generated validation report.
     */
    async buildReport(writableContext: Context): Promise<number> {
        const report = await writableContext.create(SysDataValidationReport, {});

        let totalInstancesCount = 0;
        let totalInstancesCountInError = 0;
        await asyncArray(this._validators).forEach(async validator => {
            const instances = await writableContext.unsafeWithRootUser(() =>
                writableContext.query(validator.nodeClass).toArray(),
            );
            let errorsCount = 0;
            let instancesCount = 0;
            let instancesInErrorCount = 0;
            loggers.dataValidation.info(
                `\t- generating data validation report for ${instances.length} instance(s) of ${validator.nodeClass.name}`,
            );
            await asyncArray(instances).forEach(async instance => {
                instancesCount += 1;
                totalInstancesCount += 1;
                let result: InstanceDataValidationResult | undefined;
                try {
                    writableContext.resetDiagnoses();
                    result = await validator.validator(writableContext, instance);
                } catch (error) {
                    result = [
                        {
                            message: error.message,
                            severity: 'error',
                            path: '',
                        },
                    ];
                }
                if (result.length === 0) return;
                loggers.dataValidation.warn(
                    `\t\t- data validation found ${result.length} error(s) for instance ${instance.$.factory.name}/${instance._id}`,
                );
                instancesInErrorCount += 1;
                totalInstancesCountInError += 1;
                errorsCount += result.length;
                await asyncArray(result).forEach(async resultLine => {
                    loggers.dataValidation.verbose(
                        () =>
                            `\t\t\t- ${resultLine.severity}: ${resultLine.message}, extraInfo: ${JSON.stringify(resultLine.extraInfo)}`,
                    );
                    await report.lines.append({
                        nodeName: instance.$.factory.name,
                        nodeId: instance._id,
                        message: resultLine.message,
                        extraInfo: resultLine.extraInfo,
                        severity: resultLine.severity,
                        path: resultLine.path,
                    });
                });
            });
            if (errorsCount > 0) {
                loggers.dataValidation.warn(
                    `\t- data validation found ${instancesInErrorCount} error(s) for ${validator.nodeClass.name}, ${instancesCount} instance(s) checked, ${errorsCount} errors found.`,
                );
            } else {
                loggers.dataValidation.info(
                    `\t- data validation found no errors for ${validator.nodeClass.name}, ${instancesCount} instance(s) checked.`,
                );
            }
        });

        loggers.dataValidation.info(
            `Data validation report generated for tenant ${writableContext.tenantId}: ${totalInstancesCount} instance(s) checked, ${totalInstancesCountInError} instance(s) in error.`,
        );

        await report.$.save();

        return report._id;
    }

    /** @internal */
    static readonly instance = new SysDataValidationManager();
}

const defaultDataValidator: DataValidator<Node> = async (context, instance) => {
    if (await instance.$.control()) return [];

    const mapSeverity = (severity: ValidationSeverity): coreEnums.MessageSeverity => {
        switch (severity) {
            case ValidationSeverity.error:
            case ValidationSeverity.exception:
                return 'error';
            case ValidationSeverity.warn:
                return 'warning';
            default:
                return 'info';
        }
    };

    return context.diagnoses.map(diagnosis => ({
        message: diagnosis.message,
        severity: mapSeverity(diagnosis.severity),
        path: diagnosis.path.join('/'),
    }));
};
