import { <PERSON><PERSON>ana<PERSON>, <PERSON>aticThis } from '@sage/xtrem-core';
import { SysNoteAssociation } from '../nodes/sys-note-association';

/**
 * Note manager
 */
export class SysNoteManager implements NoteManager {
    private constructor() {
        // enforce singleton
    }

    // eslint-disable-next-line class-methods-use-this
    getNoteNode(): StaticThis<SysNoteAssociation> {
        return SysNoteAssociation;
    }

    /** @internal */
    static readonly instance = new SysNoteManager();
}
