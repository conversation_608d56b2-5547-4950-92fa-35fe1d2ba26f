import { Context, LocalizationManager } from '@sage/xtrem-core';
import * as xtremSystem from '../index';

export const sysLocalizationManager: LocalizationManager = {
    initializeManager() {},

    async getDefaultTenantLocale(context: Context): Promise<string> {
        // This method may throw an error during provisioning and will be handled in xtrem-platform
        return (
            (await (
                await context
                    .query(xtremSystem.nodes.Locale, {
                        filter: { isDefaultLocale: true },
                    })
                    .toArray()
            )[0]?.id) || 'base'
        );
    },

    async isMasterLocale(context: Context): Promise<boolean> {
        if (context.currentLocale === 'base') return false;
        const locale = await context.tryRead(xtremSystem.nodes.Locale, { id: context.currentLocale });
        return locale ? locale.isLanguageMasterLocale : false;
    },

    async createTenantLocale(context: Context, locale: string): Promise<void> {
        if (!(await context.tryRead(xtremSystem.nodes.Locale, { id: locale }))) {
            const newLocale = await context.create(xtremSystem.nodes.Locale, {
                id: locale,
                isDefaultLocale: true,
                isLanguageMasterLocale: true,
            });
            await newLocale.$.save();
        }
    },
};
