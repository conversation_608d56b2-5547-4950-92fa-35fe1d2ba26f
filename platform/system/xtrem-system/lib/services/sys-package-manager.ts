/* eslint-disable class-methods-use-this */
import {
    Application,
    asyncArray,
    ConfigManager,
    Context,
    CreateSchemaOptions,
    DebugMetrics,
    Dict,
    Node,
    NodeFactory,
    Package,
    PackageManager,
    sortFactories,
    StaticThis,
} from '@sage/xtrem-core';
import { LogicError } from '@sage/xtrem-shared';
import * as _ from 'lodash';
import * as semver from 'semver';
import * as xtremSystem from '../index';
import { loggers } from '../loggers';
import {
    SysCustomer,
    SysCustomRecord,
    SysPackAllocation,
    SysPackVersion,
    SysTenant,
    SysUpgrade,
    User,
} from '../nodes/_index';
import { SchemaManager } from './schema-manager';

const logger = loggers.packageManager;

export interface PackageInfo {
    name: string;
    version: string;
    isHidden: boolean;
    isReleased: boolean;
    sqlSchemaVersion?: string;
}

// This interface describes that critical data that we need to compute if a package is active or not.
// This information is cached into the context and global caches.
interface PackageSummary {
    name: string;
    isHidden: boolean;
    isActive: boolean;
}

export class SysPackageManager extends PackageManager {
    // Returns the cached summaries of package version and service allocation data
    // Methods that test if packages are active or not should fetch the package allocations with this method
    private getCachedVersions(context: Context): Promise<Dict<{ version: string; isReleased: boolean }>> {
        return DebugMetrics.withMetrics('Package', 'versions.fetched', () =>
            context.getCachedValue({
                category: context.getNodeCacheCategory(xtremSystem.nodes.SysPackVersion),
                key: 'SysPackVersion.summaries',
                getValue: () =>
                    DebugMetrics.withMetrics('Package', 'versions.missed', async () => {
                        const summaries = await context.select(
                            xtremSystem.nodes.SysPackVersion,
                            { name: true, version: true, isReleased: true },
                            { filter: {} },
                        );
                        return {
                            value: _.zipObject(
                                summaries.map(summary => summary.name),
                                summaries.map(summary => ({
                                    version: summary.version,
                                    isReleased: summary.isReleased,
                                })),
                            ),
                        };
                    }),
                cacheInMemory: true,
                isolateInContext: false,
                ttlInSeconds: 3600,
            }),
        );
    }

    // Returns the cached summaries of package version and service allocation data
    // Methods that test if packages are active or not should fetch the package allocations with this method
    private getCachedSummaries(context: Context): Promise<Dict<PackageSummary>> {
        return DebugMetrics.withMetrics('Package', 'summaries.fetched', () =>
            context.getCachedValue({
                category: context.getNodeCacheCategory(xtremSystem.nodes.SysPackAllocation),
                key: 'SysPackAllocation.summaries',
                getValue: () =>
                    DebugMetrics.withMetrics('Package', 'summaries.missed', async () => {
                        const summaries = await context.select(
                            xtremSystem.nodes.SysPackAllocation,
                            { package: { name: true, version: true, isHidden: true }, isActive: true },
                            { filter: {} },
                        );
                        return {
                            value: _.zipObject(
                                summaries.map(summary => summary.package.name),
                                summaries.map(summary => ({
                                    name: summary.package.name,
                                    version: summary.package.version,
                                    isHidden: summary.package.isHidden,
                                    isActive: summary.isActive,
                                })),
                            ),
                        };
                    }),
                cacheInMemory: true,
                isolateInContext: false,
                ttlInSeconds: 3600,
            }),
        );
    }

    async hasPackageVersions(context: Context): Promise<boolean> {
        return Object.keys(await this.getCachedVersions(context)).length > 0;
    }

    override async getCurrentVersion(pack: Package, context?: Context): Promise<string | undefined> {
        if (!context) return this.application.withReadonlyContext(null, ctx => this.getCurrentVersion(pack, ctx));

        const versions = await this.getCachedVersions(context);
        return versions[pack.name]?.version;
    }

    async setCurrentVersion(context: Context, pack: Package, version: string): Promise<void> {
        const updated = await context.bulkUpdate(xtremSystem.nodes.SysPackVersion, {
            set: {
                version,
                isHidden: pack.isHidden,
                isReleased: pack.isReleased,
                sqlSchemaVersion: pack.sqlSchemaVersion,
            },
            where: {
                name: pack.name,
            },
        });
        if (updated !== 1) throw new Error(`${pack.name}: update version failed: ${updated}`);
    }

    async isPackageEnabled(context: Context, packageName: string): Promise<boolean> {
        const summaries = await this.getCachedSummaries(context);
        const summary = summaries[packageName];
        if (!summary) {
            logger.debug(() => `Package ${packageName} was not found.`);
            return false;
        }
        const isActive = summary.isActive;
        logger.debug(() => `Package ${packageName} isActive: ${isActive}`);
        return isActive;
    }

    override async getActivePackageNames(context: Context): Promise<string[]> {
        return Object.values(await this.getCachedSummaries(context))
            .filter(summary => summary.isActive)
            .map(summary => summary.name);
    }

    async setSysPackAllocationActiveState(context: Context, packageName: string, isActive: boolean) {
        const sysPackAllocation = await context.read(
            xtremSystem.nodes.SysPackAllocation,
            { package: `#${packageName}` },
            { forUpdate: true },
        );
        if ((await sysPackAllocation.isActive) === isActive) return;

        await sysPackAllocation.$.set({ isActive });
        await sysPackAllocation.$.save();
    }

    private async deleteObsoleteSysPackAllocations(context: Context, packVersionId: number): Promise<void> {
        // Custom SQL is simpler and faster because it handles all tenants at once
        await context.executeSql(`DELETE FROM ${context.schemaName}.sys_pack_allocation WHERE package = $1`, [
            packVersionId,
        ]);
    }

    private async deleteObsoleteActivities(context: Context, packVersionId: number): Promise<void> {
        // Custom SQL is simpler and faster because it handles all tenants at once

        // There is a foreign key between role_activity and actvitiy, we first need to delete
        // all the role activities that are linked to any obsolete activity
        await context.executeSql(
            `DELETE FROM ${context.schemaName}.role_activity WHERE activity IN (SELECT _id FROM ${context.schemaName}.activity WHERE package = $1)`,
            [packVersionId],
        );
        // Now, we can delete the obsolete activities
        await context.executeSql(`DELETE FROM ${context.schemaName}.activity WHERE package = $1`, [packVersionId]);
    }

    async createPackageVersion(context: Context, pack: PackageInfo, override?: { version: string }): Promise<void> {
        const newPackVersion = await context.create(xtremSystem.nodes.SysPackVersion, {
            name: pack.name,
            version: override ? override.version : pack.version,
            isBundle: false,
            isHidden: pack.isHidden,
            isReleased: pack.isReleased,
            sqlSchemaVersion: pack.sqlSchemaVersion,
        });
        await newPackVersion.$.save();
    }

    async updatePackageVersion(context: Context, pack: PackageInfo): Promise<void> {
        const updated = await context.bulkUpdate(xtremSystem.nodes.SysPackVersion, {
            set: {
                version: pack.version,
                isBundle: false,
                isHidden: pack.isHidden,
                isReleased: pack.isReleased,
                sqlSchemaVersion: pack.sqlSchemaVersion,
            },
            where: { name: pack.name },
        });
        if (updated !== 1) throw new LogicError(`${pack.name}: update SysPackVersion failed`);
    }

    async createOrUpgradePackageVersions(context: Context): Promise<void> {
        const oldPackVersions = await context.select(
            xtremSystem.nodes.SysPackVersion,
            { _id: true, name: true },
            { filter: {} },
        );
        const newPackages = Object.values(this.application.getPackages());
        // Create the missing SysPackVersion records
        await asyncArray(newPackages).forEach(async pack => {
            const oldPackVersion = oldPackVersions.find(pv => pv.name === pack.name);
            if (oldPackVersion) {
                await this.updatePackageVersion(context, pack);
            } else {
                await this.createPackageVersion(context, pack);
            }
        });

        // Delete the obsolete SysPackVersion records
        await asyncArray(oldPackVersions.reverse())
            .filter(oldPackVersion => !newPackages.some(pack => pack.name === oldPackVersion.name))
            .forEach(async oldPackVersion => {
                await this.deleteObsoleteActivities(context, oldPackVersion._id);
                await this.deleteObsoleteSysPackAllocations(context, oldPackVersion._id);
                await context.delete(xtremSystem.nodes.SysPackVersion, { _id: oldPackVersion._id });
            });
    }

    async updateAllPackageVersions(): Promise<void> {
        await this.application.asRoot.withCommittedContext(
            null,
            context =>
                asyncArray(this.application.getPackages()).forEach(async pack => {
                    await context.bulkUpdate(xtremSystem.nodes.SysPackVersion, {
                        set: { version: pack.version },
                        where: { name: pack.name },
                    });
                    loggers.upgrade.warn(`\t-${pack.name}: is now in version ${pack.packageJson.version}`);
                }),
            { description: () => 'updateAllPackageVersions', unsafeApplyToAllTenants: true },
        );
    }

    /** Activates a single package */
    private async activatePackage(context: Context, pack: Package, visited = {} as Dict<boolean>): Promise<void> {
        if (visited[pack.name]) return;
        visited[pack.name] = true;

        if (!(await this.isPackageEnabled(context, pack.name))) {
            await this.setSysPackAllocationActiveState(context, pack.name, true);
        }

        // find all the packages that are activated by this one, and activate them (recursively)
        const activatedPackages = pack.directDependencies.filter(dependency => dependency.isActiveByDefault);

        if (activatedPackages.length > 0) {
            await asyncArray(activatedPackages).forEach(async activatedPackage => {
                await this.activatePackage(context, activatedPackage, visited);
            });
        }
    }

    /** Activates an array of package names */
    async activatePackages(context: Context, packageNames: string[]): Promise<string[]> {
        const visited = {} as Dict<boolean>;
        const activeBefore = await this.getActivePackageNames(context);
        await asyncArray(packageNames).forEach(async packageName => {
            await this.activatePackage(context, this.findPackageByName(packageName), visited);
        });
        return _.difference(await this.getActivePackageNames(context), activeBefore);
    }

    override async createOrUpgradePackageAllocations(context: Context, activePackageNames?: string[]): Promise<void> {
        const oldPackAllocations = await context.select(
            xtremSystem.nodes.SysPackAllocation,
            { _id: true, isActive: true, package: { _id: true, name: true } },
            { filter: {} },
        );

        const sysPackVersions = await context.select(
            xtremSystem.nodes.SysPackVersion,
            { _id: true, name: true },
            { filter: {} },
        );

        const newPackages = Object.values(this.application.getPackages());

        // We have to push the pack allocations to activate into a list, and activate them after the create/update loop
        // to ensure that their dependencies exist when we activate them.
        const activateList = [] as string[];

        // Create the missing SysPackAllocation records and collect the list of active options
        await asyncArray(newPackages).forEach(async pack => {
            const index = oldPackAllocations.findIndex(
                oldPackAllocation => oldPackAllocation.package.name === pack.name,
            );

            if (index < 0) {
                const isActive = pack.isActiveByDefault || !!activePackageNames?.includes(pack.name);

                const sysPackVersion = sysPackVersions.find(pv => pv.name === pack.name);
                if (!sysPackVersion) throw new LogicError(`${pack.name}: package not found in SysPackVersion table`);

                // Set active to false initially, to force propagation of true active to parent.
                const newSysPackAllocation = await context.create(xtremSystem.nodes.SysPackAllocation, {
                    package: sysPackVersion._id,
                    isActivable: true,
                    isActive: false,
                });
                await newSysPackAllocation.$.save();
                if (isActive) activateList.push(pack.name);
            } else {
                const oldPackAllocation = oldPackAllocations[index];
                const isActive = oldPackAllocation.isActive || !!activePackageNames?.includes(pack.name);

                if (isActive) activateList.push(pack.name);
            }
        });

        // Delete the obsolete SysPackAllocation records
        await asyncArray(oldPackAllocations)
            .filter(oldPackAllocation => !newPackages.some(pack => pack.name === oldPackAllocation.package.name))
            .forEach(async oldPackAllocation => {
                await context.delete(xtremSystem.nodes.SysPackAllocation, { _id: oldPackAllocation._id });
            });

        if (activateList.length > 0) await this.activatePackages(context, activateList);
    }

    /** Get the array of service options that activate a given option */
    private getActivatingPackages(context: Context, pack: Package): Promise<Package[]> {
        const activatedByPackages = Object.values(this.application.getPackages()).filter(activatedBy =>
            activatedBy.directDependencies.some(dep => dep === pack),
        );
        return asyncArray(activatedByPackages)
            .filter(activatedBy => this.isPackageEnabled(context, activatedBy.name))
            .toArray();
    }

    /** Deactivates a single service option */
    private async deactivatePackage(context: Context, pack: Package): Promise<boolean> {
        const activatingPackages = await this.getActivatingPackages(context, pack);
        if (pack.isHidden && activatingPackages.length > 0) {
            // We cannot deactivate it because it is hidden and still activated by other active packages
            // TODO: add a diagnose
            return false;
        }

        const changeIt = await this.isPackageEnabled(context, pack.name);
        if (changeIt) {
            await this.setSysPackAllocationActiveState(context, pack.name, false);
        }

        if (pack.isHidden) {
            // try to deactivate its hidden parent
            // will only succeed if all its siblings are also inactive.
            await asyncArray(pack.directDependencies).forEach(async dep => {
                await this.deactivatePackage(context, dep);
            });
        } else if (activatingPackages.length > 0) {
            // We are deactivating a visible package
            // Deactivate all the packages that depend on it (they should all be visible)
            await asyncArray(activatingPackages).forEach(async activatingPackage => {
                await this.deactivatePackage(context, activatingPackage);
            });
        }
        return changeIt;
    }

    /** Deactivates an array of service options */
    async deactivatePackages(context: Context, packageNames: string[]): Promise<void> {
        // We have to start from the leaves of the dependency tree, to ensure that all requested options can be
        // deleted in a single pass.
        const packages = this.application.getPackages().filter(pack => packageNames.includes(pack.name));
        await asyncArray(packages).forEach(async pack => {
            await this.deactivatePackage(context, pack);
        });
    }

    static fromApplication(application: Application): SysPackageManager {
        const packageManager = application.packageManager;
        if (!(packageManager instanceof SysPackageManager)) throw new LogicError('invalid package manager class');
        return packageManager;
    }

    static fromContext(context: Context): SysPackageManager {
        return this.fromApplication(context.application);
    }

    static getSysNodeConstructors(): StaticThis<Node>[] {
        return [SysPackVersion, SysPackAllocation, SysCustomRecord, SysUpgrade, SysCustomer, SysTenant, User];
    }

    getFactories(): NodeFactory[] {
        return sortFactories(
            SysPackageManager.getSysNodeConstructors().map(node => this.application.getFactoryByConstructor(node)),
        );
    }

    async initPackageManagerTables(context: Context): Promise<void> {
        await asyncArray(this.getFactories()).forEach(async factory => {
            // do not create foreign keys now because we may have references to non sys tables that are not yet created (ex: dashboard from user)
            await factory.ensureTableExists(context, {
                skipDrop: true,
                skipForeignKeys: true,
            });
            await factory.ensureAllTableColumnsExists(context);
        });
    }

    async validatePackageVersions(context?: Context): Promise<void> {
        // verify version of packages
        const config = ConfigManager.current;
        let semVerCompatibilityLevel: semver.ReleaseType;
        if (config.semVerCompatibilityLevel != null) {
            semVerCompatibilityLevel = config.semVerCompatibilityLevel;
        } else {
            semVerCompatibilityLevel = config.deploymentMode === 'production' ? 'minor' : 'patch';
        }

        const allowedVersionDiffs: semver.ReleaseType[] = [];

        switch (semVerCompatibilityLevel) {
            case 'minor':
                allowedVersionDiffs.push('patch');
                break;
            case 'major':
                allowedVersionDiffs.push('patch');
                allowedVersionDiffs.push('minor');
                break;
            default:
                break;
        }

        const packVersions = context
            ? await this.getCachedVersions(context)
            : await this.application.asRoot.withReadonlyContext(null, readonlyContext =>
                  this.getCachedVersions(readonlyContext),
              );

        this.application.getPackages().forEach(pack => {
            const packVersion = packVersions[pack.name];
            // If package is new, do not compare it.
            if (!packVersion) return;

            // We are not allowed to move from a released env version to a non-released version
            if (packVersion.isReleased !== pack.isReleased) {
                throw new Error(`Package released status mismatch: ${pack.name} - ${pack.dir}`);
            }
            const managedExternal = config.storage?.managedExternal;
            const currentVersion = packVersion.version || (managedExternal ? pack.packageJson.version : '0.0.0');
            const packVersionDiff = semver.diff(currentVersion, pack.packageJson.version);

            if (packVersionDiff) {
                // there is a version diff and the package is not released so an upgrade needs to happen
                if (!pack.isReleased)
                    throw new Error(
                        `Package version mismatch: ${pack.name} ${currentVersion} -> ${pack.packageJson.version}, please upgrade.`,
                    );

                // The package is released, there is a version diff and the diff is on level above the semVerCompatibilityLevel
                // Example: diff = minor and semVerCompatibilityLevel = patch (no diff is not allowed)
                //       or diff = patch and semVerCompatibilityLevel = minor, then the diff is allowed
                if (!allowedVersionDiffs.includes(packVersionDiff))
                    throw new Error(
                        `Package version mismatch on released package (${packVersionDiff}): ${pack.name} ${currentVersion} -> ${pack.packageJson.version}, please hot-upgrade`,
                    );

                // the package is released and the version difference is allowed.
                // The container can be started but we raise an error in the logs to inform that a hot-upgrade is required
                // When a new container will be started to run the hot-upgrade, it will fix the pack_versions
                logger.error(
                    `Package version mismatch: ${pack.name} ${currentVersion} -> ${pack.packageJson.version}, a hot-upgrade is required.`,
                );
            }
        });
    }

    /**
     * Create the SQL schema (if needed) and all the tables
     */
    override createSqlSchemaAndTables(options: CreateSchemaOptions = {}): Promise<boolean> {
        return SchemaManager.createSqlSchemaAndTables(this.application, options);
    }

    /**
     * Make sure the schema for the application exists (and create it if needed)
     */
    override ensureSchemaExists(options: CreateSchemaOptions = {}): Promise<void> {
        return SchemaManager.ensureSchemaExists(this.application.schemaName, options);
    }
}
