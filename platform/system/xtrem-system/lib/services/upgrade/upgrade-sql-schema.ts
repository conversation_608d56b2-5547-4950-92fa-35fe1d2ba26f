import type { UpgradeOptions } from '@sage/xtrem-cli';
import {
    AnyValue,
    Application,
    asyncArray,
    AsyncResponse,
    checkAndSyncFactoryValuesHash,
    ColumnInconsistency,
    ColumnInconsistencyDefaultValue,
    ColumnInconsistencyNullable,
    ColumnInconsistencyType,
    Context,
    CoreHooks,
    createExtensions,
    createFunctions,
    getDecorators,
    getTenantIdList,
    ModifyTableSqlContext,
    Node,
    NodeFactory,
    Package,
    Property,
    readTablesForSchema,
    ReferenceProperty,
    SchemaSqlContext,
    SqlConverter,
    Stream,
    SystemProperties,
    Table,
    Test,
    TriggerBuilder,
    UpgradeMetricsType,
} from '@sage/xtrem-core';
import { clsContext, withClsContext } from '@sage/xtrem-log';
import { ColumnDefinition, dbCommandMarker, IndexDefinition, TableDefinition } from '@sage/xtrem-postgres';
import { Dict } from '@sage/xtrem-shared';
import * as fs from 'fs';
import * as lodash from 'lodash';
import * as fsp from 'path';
import * as semver from 'semver';
import { loggers } from '../loggers';
import { SysPackageManager } from '../sys-package-manager';
import {
    CustomSqlAction,
    ReloadSetupCsvAction,
    SchemaDropTableAction,
    SchemaRenameNodeAction,
    SchemaRenamePropertyAction,
    SchemaUpgradeAction,
    UnsafeCustomSqlAction,
    UpgradeAction,
} from './actions';
import { ConvertColumnTypeAction } from './actions/convert-column-type-action';
import { DataUpdateAction } from './actions/data-update-action';
import { SchemaEnumUpgradeHelper } from './actions/schema-enum-upgrade-helper';
import { SystemSqlUpgrade } from './system-sql-upgrade';
import { CommandsRecorder } from './upgrade-commands-recorder';
import { UpgradeContext } from './upgrade-context';
import { UpgradePackContext, UpgradeSchemaChanges } from './upgrade-pack-context';
import { UpgradeSuite } from './upgrade-suite';

const logger = loggers.upgrade;

/**
 * @internal
 */
export type PreUpgradeContext = {
    /**
     * The list of already checked enums
     */
    checkedEnums: Dict<boolean>;
};

/**
 * Upgrades the database schema.
 * Mainly called from xtrem-cli
 *
 * @param options.force forces the SQL schema to be upgraded even for up-to-date packages
 */
export async function upgradeSqlSchemaFromCli(application: Application, options: UpgradeOptions): Promise<void> {
    await UpgradeSqlSchema.upgrade(application, {
        force: !!options.force,
        mode: options.mode,
        fullReloadOfSetupLayer: !!options.fullReloadOfSetupLayer,
        metrics: options.metrics,
        skipVacuum: options.skipVacuum,
        checkSchema: options.checkSchema,
        clusterName: options.clusterName,
    });
}

/**
 * Type for indexes to delete
 */
type IndexToDelete = {
    reason: 'obsolete' | 'changed';
    /**
     * The current definition in db
     */
    currentDefinition: IndexDefinition;
    /**
     * When reason is 'changed', the expected definition from the source code
     */
    expectedDefinition?: IndexDefinition;
};

/**
 * @internal
 */
export abstract class UpgradeSqlSchema {
    /**
     * Execute a body, providing it with an upgrade context
     * @param application
     * @param options
     * @param body
     */
    private static async _withUpgradeContext(
        application: Application,
        options: UpgradeOptions,
        body: (upgradeContext: UpgradeContext) => AsyncResponse<void>,
    ): Promise<void> {
        const upgradeContext = await UpgradeContext.create(application, options);

        await upgradeContext.registerPoolRecorders();
        await upgradeContext.withUncommittedContext(context => this._initSystemTables(context));
        try {
            await body(upgradeContext);
            await upgradeContext.dispose();
        } catch (error) {
            logger.error(error.stack);
            throw error;
        } finally {
            upgradeContext.unregisterPoolRecorders();
        }
    }

    /**
     * @disabled_internal
     * Initialize system tables that are not part of the main application.
     * @param context
     */
    // STUFF FROM xtrem-core/application.ts
    private static async _initSystemTables(context: Context): Promise<void> {
        const packageManager = SysPackageManager.fromContext(context);
        await packageManager.initPackageManagerTables(context);
        Test.initializeManagers(context);
    }

    static preservedInsertFunctionTables = [
        'activity',
        'sys_service_option',
        'sys_service_option_state',
        'sys_pack_version',
    ];

    /**
     * Execute the hot upgrade of an application.
     * Note : The hot upgrade is executed while the cluster is on-line. It CANNOT contain any schema change.
     * This function can only be used on released packages.
     * @param application
     * @metrics should metrics be generated when replaying SQL files ?
     */
    static async executeHotUpgrade(application: Application, metrics?: UpgradeMetricsType): Promise<void> {
        if (!application.mainPackage.isReleased) {
            loggers.upgrade.error('Hot upgrades can only be executed on a released application');
            return;
        }
        const packageManager = SysPackageManager.fromApplication(application);
        const fromVersion = await packageManager.getCurrentVersion(application.mainPackage);
        if (!fromVersion) throw new Error(`Could not retrieve version of package ${application.mainPackage.name}`);

        const toVersion = application.mainPackage.packageJson.version;
        if (fromVersion === toVersion) {
            loggers.upgrade.info(`No hot-upgrade to execute, The application is already up-to-date ${fromVersion}`);
            return;
        }
        if (semver.gt(fromVersion, toVersion)) {
            throw new Error(
                `Cannot hot-upgrade from ${fromVersion} to ${toVersion}. Versions mismatch. Please use a image with a version greater than ${fromVersion}.`,
            );
        }

        loggers.upgrade.warn('******************************************************************************');
        loggers.upgrade.warn('**');
        loggers.upgrade.warn(
            `** Running hot upgrade of ${application.mainPackage.name} from ${fromVersion} to ${toVersion}`,
        );
        loggers.upgrade.warn('**');
        loggers.upgrade.warn('******************************************************************************');
        const upgradeOptions: UpgradeOptions = {
            force: false,
            mode: 'replayOnly',
            fullReloadOfSetupLayer: false,
            metrics,
        };
        await UpgradeSqlSchema._withUpgradeContext(application, upgradeOptions, async upgradeContext => {
            await UpgradeSqlSchema._createOrUpdateActivities(upgradeContext);
            await upgradeContext.replayRecordedCommands();
            await UpgradeSqlSchema._deleteObsoleteActivities(upgradeContext);
            await UpgradeSqlSchema.fixInteropTransformationsActivation(upgradeContext);

            if (this.forceFullCsvReload(upgradeContext)) {
                await UpgradeSqlSchema._reloadFullSetupLayer(upgradeContext);
            }
        });
        loggers.upgrade.warn(`Application was hot-upgraded to version ${toVersion}`);

        await packageManager.updateAllPackageVersions();
    }

    /**
     * Upgrade an existing database schema.
     * Caution: this function will upgrade the schema for ALL the applications and ALL the tenants.
     * It is intended to be called when the server is offline.
     *
     * @param options.force forces the SQL schema to be upgraded even if its version is up-to-date
     */
    static async upgrade(
        application: Application,
        options: UpgradeOptions = {
            force: false,
            mode: 'replayOnly',
            fullReloadOfSetupLayer: false,
        },
    ): Promise<void> {
        await withClsContext(async () => {
            await UpgradeSqlSchema._withUpgradeContext(application, options, async upgradeContext => {
                await UpgradeSqlSchema._upgrade(upgradeContext);
                if (upgradeContext.options.checkSchema) {
                    logger.info(`[${application.schemaName}] the SQL schema was successfully checked.`);
                    return;
                }
                await UpgradeSqlSchema._postUpgrade(upgradeContext);
                logger.info(`[${application.schemaName}] the SQL schema was successfully upgraded.`);
            });
        }, {});
    }

    /**
     * Returns all the SQL system upgrades to be executed.
     */
    private static async _getSystemSqlUpgrades(upgradeContext: UpgradeContext): Promise<SystemSqlUpgrade[]> {
        const application = upgradeContext.application;

        // The SQL system upgrades are only allowed for the xtrem-system package.
        const systemPackageName = '@sage/xtrem-system';
        const systemPack = application.getPackages().find(p => p.name === systemPackageName);
        if (!systemPack) throw new Error(`${systemPackageName} is not registered !`);

        // System upgrades are located in the folder xtrem-system/lib/system-upgrades.
        const sourcesFolder = fsp.join(systemPack.dir, 'lib', 'system-upgrades');
        const binFolder = fsp.join(systemPack.dir, 'build', 'lib', 'system-upgrades');

        const packVersion = systemPack.packageJson.version;
        // Note : do not use upgradeContext.application.mainPackage.getCurrentVersion() here to get the current version in db.
        // We are looking for SYSTEM upgrades and the packageManager might be broken by something that will be fixed by a
        // system upgrade. As this system was not executed yet, the bundle manager might not work
        const sqlContext = new SchemaSqlContext(application);
        const dbVersion = await application.asRoot.withReadonlyContext(null, async context => {
            const versions = (await sqlContext.tableExists('sys_pack_version'))
                ? await context.executeSql<{ version: string }[]>(
                      `SELECT version FROM ${application.schemaName}.sys_pack_version WHERE name=$1`,
                      [systemPackageName],
                  )
                : [{ version: '0.0.0' }];
            if (versions.length === 0) throw new Error(`Could not retrieve version of package ${systemPackageName}`);
            return versions[0].version;
        });

        const upgradesToApply = UpgradePackContext.getUpgradesFromRange(
            sourcesFolder,
            binFolder,
            dbVersion,
            packVersion,
        );
        let upgrades: SystemSqlUpgrade[] = [];
        upgradesToApply.versions.forEach(version => {
            const indexFilename = `${upgradesToApply.rootFolder}/v${version}/index`;
            loggers.upgrade.verbose(() => `\t- retrieve system upgrades from ${indexFilename}`);
            // eslint-disable-next-line import/no-dynamic-require, global-require
            const mod = require(indexFilename);
            if (!mod.systemUpgrades) {
                loggers.upgrade.verbose(
                    () => `The file ${indexFilename} does not contain any 'systemUpgrades' constant.`,
                );
                return;
            }
            const folderUpgrades = mod.systemUpgrades as SystemSqlUpgrade[];
            folderUpgrades.forEach(u => {
                u.version = version;
            });
            upgrades = [...upgrades, ...folderUpgrades];
        });

        return upgrades;
    }

    /**
     * Executes all the required SQL system upgrade.
     *
     * @param application
     */
    private static async _runSystemSqlUpgrades(upgradeContext: UpgradeContext): Promise<void> {
        const systemUpgrades = await UpgradeSqlSchema._getSystemSqlUpgrades(upgradeContext);

        const sysPool = new SchemaSqlContext(upgradeContext.application).connectionPool;
        await asyncArray(systemUpgrades).forEach(async systemUpgrade => {
            await upgradeContext.withCommittedContext(async context => {
                const sqlRecorder = context.sqlPool.sqlRecorder as CommandsRecorder;
                logger.warn(`Executing system upgrade (${systemUpgrade.version}) : ${systemUpgrade.description}`);
                if (sqlRecorder && upgradeContext.isRecording) {
                    // register an action to the recorder so that the generated SQL file will contain an
                    // action to execute the system upgrade when the SQL file will be replayed
                    let versionToRecord = systemUpgrade.version;
                    if (versionToRecord === 'latest') {
                        // The record is done when running the release-patch. If the action is a new one, it will still
                        // be in the 'latest' version. The release-patch will then rename all the 'vlatest' folders
                        // to vx.y.z where x.y.z is the current version of the application (from the package.json)
                        versionToRecord = upgradeContext.application.mainPackage.packageJson.version;
                    }
                    sqlRecorder.recordAction('system_upgrade', { version: versionToRecord });
                }
                await SystemSqlUpgrade.executeSystemUpgrade(upgradeContext.application, systemUpgrade, {
                    sysPool,
                    logger,
                });
            });
        });
    }

    /**
     * Executes a body on all the tenants
     */
    static async executeActionOnAllTenants(
        application: Application,
        options: {
            /**
             * Is the action executed while we are recording SQL files ?
             */
            forRecording: boolean;
            /**
             * Should the contexts provided to the body be read-only ?
             */
            useReadonlyContexts: boolean;
        },
        body: (context: Context) => AsyncResponse<AnyValue>,
    ): Promise<void> {
        const tenantIds = await application.asRoot.withReadonlyContext(null, (context: Context) =>
            Context.tenantManager.listTenantsIds(context),
        );

        const runBody = (tenantId: string): AsyncResponse<AnyValue> => {
            if (options.useReadonlyContexts)
                return application.asRoot.withReadonlyContext(tenantId, (context: Context) => body(context));
            return application.asRoot.withCommittedContext(tenantId, (context: Context) => body(context));
        };

        if (options.forRecording) {
            if (tenantIds.length === 0) throw new Error('No tenandIds are defined');
            // We are running an 'upgrade --run --record' command and we are recording the SQL files
            // that will be replayed on the real clusters (cluster-ci/cluster-cu, ...)
            // This command is probably running on the patch-release pipeline
            // There is no way to know what will be the tenants on the real cluster
            // so the body is executed on the current tenant (probably 000...0000)
            // but in the SQL file, it will be recorded with a special %%ALL_TENANTS%% tag
            // that will be replaced by a loop on all the tenants on the real cluster
            // The current context is not set to any tenant, so to make the SQL command pass on the current cluster,
            // we have to run it against a tenant (the 1st one will be fine)
            clsContext().executeActionOnAllTenants = true;
            try {
                await runBody(tenantIds[0]);
            } finally {
                clsContext().executeActionOnAllTenants = false;
            }
        } else {
            // we are probably in an 'upgrade --test' command
            // We can loop over all the tenants of the cluster and execute the body
            await asyncArray(tenantIds).forEach(async tenantId => {
                await runBody(tenantId);
            });
        }
    }

    /**
     * Fix the activation of sysNodeTransformations at the end of upgrades
     */
    private static async fixInteropTransformationsActivation(upgradeContext: UpgradeContext): Promise<void> {
        logger.info('Fix activation of interop transformations');
        await upgradeContext.withCommittedContext(context =>
            CoreHooks.interopManager.fixInteropTransformationsActivation(context, { logger }),
        );
    }

    /**
     * Create the missing activities and update existing ones
     *
     * @param upgradeContext
     */
    private static _createOrUpdateActivities(upgradeContext: UpgradeContext): Promise<void> {
        // The creation of the new activities must not be recorded
        // as they would contain static package_ids
        return upgradeContext.withoutRecording(() => {
            // update the content of sys_activity table
            logger.info('Upgrade activities');
            // Update the activities in the Activity table
            return upgradeContext.withCommittedContext<void>(async context => {
                await Context.accessRightsManager.createActivities(context);
                await Context.accessRightsManager.updateActivities(context);
            });
        });
    }

    /**
     * Delete old activities in the activity table.
     *
     * @param upgradeContext
     */
    private static _deleteObsoleteActivities(upgradeContext: UpgradeContext): Promise<void> {
        // The deletion of the activities must not be recorded as it is relative to the current application
        logger.info('Post upgrade activities');
        // Delete the activities in the Activity table that don't exist in the application.
        // This will also delete the role activities referencing the activity
        return upgradeContext.withCommittedContext<void>(async context => {
            await Context.accessRightsManager.deleteActivities(context);
        });
    }

    /**
     * Returns whether all the setup data should be reloaded from CSVs at the very end of the upgrade
     */
    private static forceFullCsvReload(upgradeContext: UpgradeContext) {
        if (upgradeContext.options.fullReloadOfSetupLayer) return true;
        return false;
    }

    /**
     * Upgrade an existing database schema.
     * Caution: this function will upgrade the schema for ALL the applications and ALL the tenants.
     * It is intended to be called when the server is offline.
     *
     * @param options.force forces the SQL schema to be upgraded even if its version is up-to-date
     */
    private static async _upgrade(upgradeContext: UpgradeContext): Promise<void> {
        const application = upgradeContext.application;

        await upgradeContext.withoutRecording(async () => {
            await CoreHooks.metadataManager.upgradeMetadata(upgradeContext.application, { onlyCreate: true });
        });

        if (upgradeContext.options.checkSchema) {
            // Here, we don't want to upgrade anything, we just want to check that the SQL schema
            // is in sync with the code.
            // Consider all the packages as released, this way, if any schema changes is required, an error will be raised
            application.getPackages().forEach(pack => {
                if (pack.packageJson.xtrem) pack.packageJson.xtrem.isReleased = true;
            });
        }
        // Drop all the insert functions and recreate the ones needed by upgrade actions
        await this._dropAllInsertFunctions(upgradeContext);
        await this._createInsertFunctions(
            upgradeContext,
            this.preservedInsertFunctionTables
                // Do not recreate the activity insert function in test mode bc activity is in xtrem-authorization package
                .filter(table => !(application.applicationType === 'test' && table === 'activity'))
                .map(t => application.getFactoryByTableName(t)),
        );

        // Check the main package: no need to go further if it's not OK
        // We won't check the schema changes for now because we may not be in the right version if some SQL
        // files first need to be replayed
        await UpgradeSqlSchema._checkPackage(upgradeContext, application.mainPackage, {
            skipSchemaChanges: true,
            force: upgradeContext.options.force,
        });
        await UpgradeSqlSchema._ensureAllPackagesAreDeclared(upgradeContext);
        UpgradeSqlSchema._raiseCheckErrorsIfAny(upgradeContext);
        const packageManager = upgradeContext.packageManager;

        logger.info(
            `[${application.schemaName}] upgrade ${application.mainPackage.name}@${await packageManager.getCurrentVersion(
                application.mainPackage,
            )} to ${application.mainPackage.packageJson.version}, mode=${upgradeContext.options.mode}, check=${!!upgradeContext.options.checkSchema}`,
        );

        // update the content of sys_activity table (new activities are not recorded in SQL files)
        await UpgradeSqlSchema._createOrUpdateActivities(upgradeContext);

        if (upgradeContext.options.mode !== 'upgradeOnly') {
            // if the main package is released, we should still replay, in case we need to manually add a sql file for execution
            // or we upgrade to a new release.
            await upgradeContext.replayRecordedCommands();
        }

        if (upgradeContext.options.mode === 'replayOnly') {
            // in this mode, nothing more has to be done, deletion/re-creation of triggers/functions are already part
            // of the SQL files that were just replayed.
            return;
        }

        const checkResult = await UpgradeSqlSchema._checkUpgrade(upgradeContext);

        await UpgradeSqlSchema._runSystemSqlUpgrades(upgradeContext);

        // If the main package is released, no schema actions are allowed
        if (!application.mainPackage.isReleased) {
            await UpgradeSqlSchema._ensureExtensionsCreated(upgradeContext);
            await UpgradeSqlSchema._ensureFunctionsCreated(upgradeContext);
        }

        await UpgradeSqlSchema._upgradePacksAndFactories(upgradeContext, checkResult.factoriesToProcess);
    }

    /**
     * Force a reloading of all the setup CSV files
     */
    private static async _reloadFullSetupLayer(upgradeContext: UpgradeContext): Promise<void> {
        const setupFactories = upgradeContext.application
            .getSqlPackageFactories()
            .filter(factory => factory.isSetupNode);
        logger.warn(
            `[${upgradeContext.application.schemaName}] reload all the CSV files from the setup layer : \n\t -${setupFactories
                .map(factory => factory.fullName)
                .join('\n\t -')}`,
        );
        await ReloadSetupCsvAction.reloadCsv(upgradeContext.application, setupFactories, {
            forRecording: false,
        });
    }

    /**
     * Executes a ReloadSetupCsvAction to reload the csv files that were updated since the generation of the
     * last SQL file.
     */
    private static async _reloadUpdatedCsvSetupFiles(upgradeContext: UpgradeContext): Promise<void> {
        logger.warn(
            `[${upgradeContext.application.schemaName}] reload all the updated CSV files from the setup layer since the last upgrade`,
        );
        const factoriesAndSha = await upgradeContext.withUncommittedContext(() =>
            upgradeContext.getFactoriesOfUpdatedSetupCsvFiles(),
        );
        const messageWithFactoriesToReload = `[${factoriesAndSha.factories
            .map(f => f.name)
            .join(',')}], lastSha=${factoriesAndSha.lastSha}`;

        const expectedSetupDataToReload = upgradeContext.actionsDependingOnSetupData;
        const errors: string[] = [];
        Object.entries(expectedSetupDataToReload).forEach(([factoryName, actions]) => {
            // Any factory referenced by the 'dependsOnSetupData' property of an action must be detected as 'to be reloaded' (changes must have
            // been made to its setup data)
            if (factoriesAndSha.factories.some(factory => factory.name === factoryName)) return;
            errors.push(
                `The factory '${factoryName}' is referenced by the 'dependsOnSetupData' attribute of some actions but no changes were detected in its setup data. Actions are : \n - ${actions.map(a => a.description).join('\n - ')}`,
            );
        });
        if (errors.length > 0) {
            throw new Error(
                `${errors.join('\n')}\nYou need to either remove the 'dependsOnSetupData' attribute from the actions or update the setup data of these factories.\nUpdated setup data are ${messageWithFactoriesToReload}`,
            );
        }
        if (factoriesAndSha.factories.length === 0) {
            logger.warn(
                `[${upgradeContext.application.schemaName}] no updated setup CSV file was detected since last generation, gitHead was ${factoriesAndSha.lastSha}`,
            );
            return;
        }
        // The factories need to be topo-sorted
        const allSortedFactories = upgradeContext.application.getSqlPackageFactories();
        factoriesAndSha.factories = lodash.sortBy(factoriesAndSha.factories, factory => {
            return allSortedFactories.indexOf(factory);
        });
        logger.warn(`Fire an automatic action to reload setup data for factories ${messageWithFactoriesToReload}`);
        const reloadAction = new ReloadSetupCsvAction({
            nodes: factoriesAndSha.factories.map(f => () => f.nodeConstructor),
        });
        reloadAction.setClusterName(upgradeContext.options.clusterName);
        reloadAction.version = 'auto';
        await upgradeContext.preExecuteAction(reloadAction);
        await upgradeContext.executeAction(reloadAction);
    }

    /**
     * Make sure all the packages are declared in the sys_pack_version table
     */
    private static async _ensureAllPackagesAreDeclared(upgradeContext: UpgradeContext): Promise<void> {
        const defaultVersion = (await upgradeContext.getPackContext(upgradeContext.application.mainPackage).versions)
            .sysPackVersion;
        await asyncArray(upgradeContext.packages).forEach(async pack => {
            const versions = await upgradeContext.getPackContext(pack).versions;
            if (versions.sysPackVersion === '0.0.0') {
                await upgradeContext.withCommittedContext(async context => {
                    logger.warn(
                        `[${upgradeContext.application.schemaName}] missing package ${pack.packageName}' was initialized to version ${defaultVersion}`,
                    );
                    await upgradeContext.packageManager.createPackageVersion(context, pack, {
                        version: defaultVersion,
                    });
                    versions.sysPackVersion = defaultVersion;
                });
            }
        });
    }

    /**
     * Post-upgrade process.
     * All the actions run within this function will not be recored to SQL files.
     * The _postUpgrade function is always executed, even when only replaying SQL files.
     */
    private static async _postUpgrade(upgradeContext: UpgradeContext): Promise<void> {
        await upgradeContext.withoutRecording(async () => {
            await CoreHooks.metadataManager.upgradeMetadata(upgradeContext.application);
        });

        // Make sure that all the service options are existing in db before creating any missing serviceOptionState
        await UpgradeSqlSchema.createOrUpdateServiceOptions(upgradeContext);
        if (this.forceFullCsvReload(upgradeContext)) {
            await UpgradeSqlSchema._reloadFullSetupLayer(upgradeContext);
        } else if (upgradeContext.options.mode !== 'replayOnly') {
            await UpgradeSqlSchema._reloadUpdatedCsvSetupFiles(upgradeContext);
        }

        // Note: if a recorder is active, the changes in the SQL comments MUST be recorded
        // into the new SQL file. This way, the comments will be up-to-date after replaying the SQL files
        // Tests in PR pipeline do the following steps :
        // - restore a backup (cluster-cu for instance)
        // - run an upgrade with --test flag. This will
        //    - replay the SQL files
        //    - run the upgrade engine and apply all the changes from the current PR (actions, changes, ...)
        //      At this step, the comments need to be up-to-date, otherwise, changes from the last SQL file
        //      will be detected (as the upgrade engine relies on comments to detect changes)
        await upgradeContext.writeCommentsOnSqlObjects(upgradeContext.factoriesToUpgrade, true);
        upgradeContext.stopRecording();

        if (upgradeContext.options.mode === 'replayAndRecord') {
            await this.executePostRecordActions(upgradeContext);
        }

        // Recreate all insert functions
        await UpgradeSqlSchema._dropAllInsertFunctions(upgradeContext);
        await UpgradeSqlSchema._createInsertFunctions(upgradeContext, upgradeContext.factoriesToUpgrade);

        await UpgradeSqlSchema._checkPackAllocations(upgradeContext);

        await UpgradeSqlSchema._deleteObsoleteServiceOptions(upgradeContext);
        await UpgradeSqlSchema._checkServiceOptionStates(upgradeContext);
        await UpgradeSqlSchema._deleteObsoleteActivities(upgradeContext);
        await UpgradeSqlSchema.fixInteropTransformationsActivation(upgradeContext);

        await upgradeContext.ensureSchemaIsOk();

        await UpgradeSqlSchema._updateDedicatedFunctionsForTriggers(upgradeContext);

        // Check and sync the values hash of all the content addressable factories
        await UpgradeSqlSchema._checkAndSyncContentAddressable(upgradeContext.application);

        await CoreHooks.getDataValidationManager().buildReports(upgradeContext.application);

        logger.info(`${upgradeContext.packages.length} were upgraded.`);
        await upgradeContext.withCommittedContext(
            context =>
                asyncArray(upgradeContext.packages).forEach(async pack => {
                    const packContext = upgradeContext.getPackContext(pack);
                    if (packContext.isUpToDate) return;
                    await logger.infoAsync(
                        async () =>
                            `\t- ${pack.name}: ${(await packContext.versions).sysPackVersion} -> ${
                                pack.packageJson.version
                            }`,
                    );
                    await upgradeContext.packageManager.setCurrentVersion(context, pack, pack.version);
                }),
            { unsafeApplyToAllTenants: true },
        );

        if (upgradeContext.options.skipVacuum) {
            logger.info('\t- VACUUM ANALYZE: Skipped');
        } else {
            // Note : don't run the 'VACUUM ANALYZE' command when running the unit-tests: they are run
            // in parallel and it may happen that another unit-test is updating the schema at the same moment
            // This would lead to a 'relation with OID xxxx does not exist' error

            // For more infos, please check https://www.postgresql.org/docs/current/sql-vacuum.html
            await upgradeContext.application.createContextForDdl(async context => {
                logger.info('\t- VACUUM ANALYZE: Start');
                const result = await context.executeSql('VACUUM ANALYZE;', []);
                logger.info(`\t- VACUUM ANALYZE: Result - ${JSON.stringify(result)}`);
            });
        }
    }

    /**
     * Update the script of all the dedicated functions used by triggers
     * @param upgradeContext
     */
    private static async _updateDedicatedFunctionsForTriggers(upgradeContext: UpgradeContext): Promise<void> {
        if (upgradeContext.options.mode) logger.info(`Updating scripts for trigger functions`);
        upgradeContext.clearTableDefinitionsCache();
        await upgradeContext.withCommittedContext(async context => {
            await asyncArray(upgradeContext.factoriesToUpgrade).forEach(async factory => {
                const triggers = factory.table.getTableDefinition(context).triggers;
                if (triggers == null) return;
                await asyncArray(triggers).forEach(async triggerDef => {
                    if (triggerDef.functionName == null) return;
                    const builder = TriggerBuilder.getBuilderForTrigger(triggerDef);
                    if (!builder.useDedicatedFunction) return;
                    if (factory.tableName == null) return;
                    const tableDef = factory.table.getTableDefinition(context);
                    const sqlContext = new ModifyTableSqlContext(context.application, tableDef);
                    const src = builder.getSqlToCreateDedicatedFunction(tableDef);
                    if (src == null) throw new Error(`No source code for function ${triggerDef.functionName}`);
                    logger.info(`\t- update script for function ${triggerDef.functionName}`);
                    await sqlContext.executeSqlStatement({
                        sql: src,
                    });
                });
            });
        });
    }

    /**
     * Execute the postRecordAction of all the 'vlatest' upgradeSuites
     * @param upgradeContext
     * @returns
     */
    static async executePostRecordActions(upgradeContext: UpgradeContext): Promise<void> {
        if (upgradeContext.options.mode !== 'replayAndRecord') return;
        const allUpgradeSuites: { suite: UpgradeSuite; pack: Package }[] = [];
        await asyncArray(upgradeContext.packages).forEach(async pack => {
            (await upgradeContext.getPackContext(pack).getUpgradeSuites()).forEach(s => {
                if (s.version !== 'latest') return;
                if (s.suite.postRecordAction == null) return;
                allUpgradeSuites.push({ suite: s.suite, pack });
            });
        });
        if (allUpgradeSuites.length === 0) return;
        await this.executeActionOnAllTenants(
            upgradeContext.application,
            {
                forRecording: false,
                useReadonlyContexts: true,
            },
            async context => {
                await asyncArray(allUpgradeSuites).forEach(async s => {
                    if (s.suite.postRecordAction == null) return;
                    logger.info(`Execute postRecordAction for package ${s.pack.name}`);
                    await s.suite.postRecordAction(context);
                });
            },
        );
    }

    /**
     * Creates missing service options (existing in the code but not in the database) and update existing ones (existing both in code and in db).
     */
    static createOrUpdateServiceOptions(upgradeContext: UpgradeContext): Promise<void> {
        return upgradeContext.application.asRoot.withCommittedContext(
            null,
            context => context.serviceOptionManager.createOrUpdateServiceOptions(context),
            { unsafeApplyToAllTenants: true },
        );
    }

    /**
     * Deletes the obsolete service options (existing in the database but not in the code).
     */
    private static _deleteObsoleteServiceOptions(upgradeContext: UpgradeContext): Promise<void> {
        return upgradeContext.application.asRoot.withCommittedContext(null, context =>
            context.serviceOptionManager.deleteObsoleteServiceOptions(context),
        );
    }

    private static _checkServiceOptionStates(upgradeContext: UpgradeContext): Promise<void> {
        return this.executeActionOnAllTenants(
            upgradeContext.application,
            {
                forRecording: false,
                useReadonlyContexts: false,
            },
            context => context.serviceOptionManager.createOrUpgradeServiceOptionStates(context),
        );
    }

    /**
     * Ensure that a sysPackAllocation exists for every packages
     * @param upgradeContext
     */
    private static async _checkPackAllocations(upgradeContext: UpgradeContext): Promise<void> {
        const packageManager = SysPackageManager.fromApplication(upgradeContext.application);

        const tenantIds = await upgradeContext.withCommittedContext(
            async context => {
                await packageManager.createOrUpgradePackageVersions(context);
                return Context.tenantManager.listTenantsIds(context);
            },
            { unsafeApplyToAllTenants: true },
        );

        await asyncArray(tenantIds).forEach(async tenantId => {
            await upgradeContext.withCommittedContext(
                async context => {
                    await packageManager.createOrUpgradePackageAllocations(context);
                },
                { tenantId },
            );
        });
    }

    static async _ensureFunctionsCreated(upgradeContext: UpgradeContext): Promise<void> {
        await upgradeContext.withCommittedContext(() =>
            createFunctions(upgradeContext.application.schemaName, sql =>
                upgradeContext.sysSchemaSqlContext.executeSqlStatement({ sql }),
            ),
        );
    }

    static async _ensureExtensionsCreated(upgradeContext: UpgradeContext): Promise<void> {
        await upgradeContext.withCommittedContext(() =>
            createExtensions(sql => upgradeContext.sysSchemaSqlContext.executeSqlStatement({ sql })),
        );
    }

    /**
     * Rename the 'vlatest' folders of upgrades (upgrade suites, system upgrades)
     * Mainly called from xtrem-cli
     */
    static renameLatestFolders(repositoryRootFolder: string): void {
        logger.info(`Renaming 'latest' folders from ${repositoryRootFolder}`);
        UpgradeSqlSchema._renameLatestFolders(repositoryRootFolder, {
            folderPatterns: ['lib/upgrades', 'build/lib/upgrades', 'lib/system-upgrades', 'build/lib/system-upgrades'],
            depth: 0,
            maxDepth: 3,
        });
    }

    private static _renameLatestFolders(
        folder: string,
        options: {
            folderPatterns: string[];
            versionToApply?: string;
            depth: number;
            maxDepth: number;
        },
    ): void {
        if (options.depth > options.maxDepth) return;

        logger.verbose(() => `Scan ${folder}`);

        let versionToApply = options.versionToApply;
        const packageJsonPath = fsp.join(folder, 'package.json');
        if (fs.existsSync(packageJsonPath)) {
            // get the version to apply
            // eslint-disable-next-line import/no-dynamic-require, global-require
            versionToApply = require(packageJsonPath).version;
        }

        options.folderPatterns.forEach(folderPattern => {
            const vlatestPath = fsp.join(folder, folderPattern, 'vlatest');
            if (!fs.existsSync(vlatestPath)) return;
            const versionPath = fsp.join(folder, folderPattern, `v${versionToApply}`);
            logger.info(`\t- renaming ${vlatestPath} to ${versionPath}`);
            if (fs.existsSync(versionPath)) {
                throw new Error(`The folder '${versionPath}' already exists`);
            }
            fs.renameSync(vlatestPath, versionPath);
            // That's over for this branch
        });

        const entries = fs.readdirSync(folder);
        entries.forEach(filename => {
            if (filename === '.' || filename === '..') return;
            const fullPath = fsp.join(folder, filename);
            const stats = fs.statSync(fullPath);
            if (!stats.isDirectory()) return;
            if (['upgrade-test', 'node_modules', '.git', 'tmp'].includes(filename)) {
                // do not rename the 'vlatest' folders from the packages used to unit-test the upgrades
                // don't scan node_modules folders
                // don't scan .git folders
                // don't scan tmp folders
                return;
            }
            UpgradeSqlSchema._renameLatestFolders(fullPath, {
                folderPatterns: options.folderPatterns,
                versionToApply,
                depth: options.depth + 1,
                maxDepth: options.maxDepth,
            });
        });
    }

    /**
     * Raises the errors detected by the _checkPackage function
     * @param upgradeContext
     */
    private static _raiseCheckErrorsIfAny(upgradeContext: UpgradeContext): void {
        if (upgradeContext.issues.some(issue => issue.severity === 'error')) {
            throw new Error(
                `Could not upgrade schema:\n${upgradeContext.issues.map(
                    issue => `\t- ${issue.severity}:${issue.message}\n`,
                )}`,
            );
        }
    }

    /**
     * Some global checks that must be done before the upgrade
     * @param upgradeContext
     */
    private static async _checkUpgrade(upgradeContext: UpgradeContext): Promise<{ factoriesToProcess: NodeFactory[] }> {
        const factories = upgradeContext.factoriesToUpgrade;
        const mainPackage = upgradeContext.application.mainPackage;

        logger.info(
            `Upgrading ${mainPackage.name}@${await upgradeContext.packageManager.getCurrentVersion(mainPackage)} -> ${
                mainPackage.packageJson.version
            }, mode = ${upgradeContext.options.mode}, check=${!!upgradeContext.options.checkSchema}`,
        );
        logger.verbose(() => `force=${upgradeContext.options.force}`);

        let factoriesToProcess: NodeFactory[];
        switch (upgradeContext.schemaChangesRestriction) {
            case 'released':
                logger.warn('The application is released, no factory will be upgraded.');
                factoriesToProcess = [];
                break;
            case 'checkSchema':
                logger.warn('The schema will not be upgraded but only checked.');
                factoriesToProcess = factories;
                break;
            default:
                factoriesToProcess = factories;
                break;
        }

        const packages = upgradeContext.packages;

        logger.info(`Checking ${packages.length} packages`);

        await asyncArray(packages).forEach(pack =>
            UpgradeSqlSchema._checkPackage(upgradeContext, pack, {
                skipSchemaChanges: false,
                force: upgradeContext.options.force,
            }),
        );

        UpgradeSqlSchema._raiseCheckErrorsIfAny(upgradeContext);

        // Do not check the factories if we already have an issue with a package
        const dt0 = Date.now();
        logger.info(`Check ${factoriesToProcess.length} factories`);

        await upgradeContext.withUncommittedContext(context =>
            asyncArray(factoriesToProcess).forEach(factory =>
                UpgradeSqlSchema._checkFactory(context, upgradeContext, factory),
            ),
        );
        logger.verbose(() => `${factoriesToProcess.length} factories checked in ${Date.now() - dt0} ms`);

        upgradeContext.issues
            .filter(issue => issue.severity === 'warning')
            .forEach(warning => {
                logger.warn(warning.message);
            });

        UpgradeSqlSchema._raiseCheckErrorsIfAny(upgradeContext);

        // Before starting to run anything, we have to verify ALL the actions
        await upgradeContext.withUncommittedContext(async context => {
            (await upgradeContext.getAllActionsToExecute()).forEach(actionAndVersion =>
                actionAndVersion.action.verify(context),
            );
        });

        await UpgradeSqlSchema.checkTablesWithoutFactories(upgradeContext);
        return { factoriesToProcess };
    }

    private static async checkTablesWithoutFactories(upgradeContext: UpgradeContext): Promise<void> {
        if (upgradeContext.application.mainPackage.isReleased) {
            // We don't expect any table not bound to a factory in release mode
            // and it would make the unit-tests to fail
            return;
        }

        const allActions = (await upgradeContext.getAllActionsToExecute()).map(
            actionAndVersion => actionAndVersion.action,
        );

        await upgradeContext.withUncommittedContext(async context => {
            const tablesWithoutFactories = await upgradeContext.getTablesInSchemaWithoutFactory(context);
            logger.warn(`Tables without factory:${tablesWithoutFactories}`);
            const tablesToDrop: string[] = [];
            tablesWithoutFactories.forEach(tableName => {
                if (
                    !allActions.some(action => {
                        if (action instanceof SchemaDropTableAction && action.options.tableName === tableName) {
                            tablesToDrop.push(tableName);
                            return true;
                        }
                        if (action instanceof SchemaRenameNodeAction) return action.oldTableName === tableName;
                        return false;
                    })
                ) {
                    throw new Error(
                        `The table ${tableName} is in the schema but no factory was found for it, you need to declare either a SchemaDropTableAction or a SchemaRenameNodeAction`,
                    );
                }
            });
            await asyncArray(tablesToDrop).forEach(async tableName => {
                const tableDef = await upgradeContext.getTableDefinition(context, tableName);
                if (tableDef.comment?.hasAttachments) {
                    // The obsolete table could have attachments, a custom SQL action must be declared to manage them
                    if (
                        !allActions.some(action => {
                            if (action instanceof CustomSqlAction) {
                                return action.params.fixes?.attachments?.some(fix => fix.table === tableName);
                            }
                            return false;
                        })
                    ) {
                        throw new Error(
                            `The obsolete table ${tableName} had attachments, you need to declare a CustomSqlAction with a fixes/attachments that refers to the table`,
                        );
                    }
                }
            });
        });
    }

    /**
     * Upgrades a bunch of factories
     * @param factories the factories to upgrade
     */
    private static async _upgradePacksAndFactories(
        upgradeContext: UpgradeContext,
        factoriesToProcess: NodeFactory[],
    ): Promise<void> {
        const packages = upgradeContext.packages;
        if (!packages.some(pack => !upgradeContext.getPackContext(pack).isUpToDate)) {
            // All the packages are up-to-date: nothing to do ....
            logger.info('All the packages are up-to-date');
            return;
        }

        logger.info(
            `Pre-${UpgradeSqlSchema._upgradeOrCheckPrefix(upgradeContext)} ${factoriesToProcess.length} factories`,
        );

        await UpgradeSqlSchema._preExecuteActions(upgradeContext);

        // The pre-execution of some actions may have changed the schema, we thus have
        // to recompute all the factory changes. For instance, when a CustomSqlAction has
        // some tables to renameAndDrop, the table was renamed to temp_xxx and a brand new table
        // has already been created, with all the new columns while changes detected in the
        // _checkFactory may contains some changes related to these new columns.
        upgradeContext.resetFactoryChanges();

        const preUpgradeContext: PreUpgradeContext = {
            checkedEnums: {},
        };

        await asyncArray(factoriesToProcess).forEach(factory =>
            UpgradeSqlSchema._preUpgradeFactory(upgradeContext, factory, preUpgradeContext),
        );

        UpgradeSqlSchema._raiseCheckErrorsIfAny(upgradeContext);

        await UpgradeSqlSchema._executeActions(upgradeContext);

        UpgradeSqlSchema._raiseCheckErrorsIfAny(upgradeContext);

        logger.info(`${UpgradeSqlSchema._upgradeOrCheckPrefix(upgradeContext)} ${factoriesToProcess.length} factories`);
        await asyncArray(factoriesToProcess).forEach(factory =>
            UpgradeSqlSchema._upgradeFactory(upgradeContext, factory),
        );

        // Clear the cache of table definitions so that the schema will be read, with all the modifications
        // made by the preUpgradeFactories
        upgradeContext.clearTableDefinitionsCache();

        logger.info(
            `Post-${UpgradeSqlSchema._upgradeOrCheckPrefix(upgradeContext)} ${factoriesToProcess.length} factories`,
        );
        await asyncArray(factoriesToProcess.slice().reverse()).forEach(factory =>
            UpgradeSqlSchema._postUpgradeFactory(upgradeContext, factory),
        );

        await UpgradeSqlSchema._executeDropNodesActions(upgradeContext);

        UpgradeSqlSchema._raiseCheckErrorsIfAny(upgradeContext);

        await UpgradeSqlSchema._postExecuteActions(upgradeContext);
    }

    /**
     * Checks whether a package can be upgraded
     */
    private static async _checkPackage(
        upgradeContext: UpgradeContext,
        pack: Package,
        options: {
            skipSchemaChanges: boolean;
            force: boolean;
        },
    ): Promise<void> {
        const packContext = upgradeContext.getPackContext(pack);
        const versions = await packContext.versions;

        packContext.isUpToDate = false;
        if (semver.gt(versions.sysPackVersion, versions.packageJsonVersion)) {
            upgradeContext.issues.push({
                message: `Package ${pack.packageName}@${versions.sysPackVersion} can't be downgraded to ${versions.packageJsonVersion}`,
                severity: 'error',
            });
            return;
        }
        if (semver.eq(versions.sysPackVersion, versions.packageJsonVersion)) {
            // already up-to-date
            if (!options.force) {
                logger.info(`Package ${pack.packageName}@${versions.sysPackVersion} is already up-to-date`);
                packContext.isUpToDate = true;
                return;
            }
            // In 'force' mode, we want to check (and fix if needed) the sql schema
            logger.verbose(
                () =>
                    `Package ${pack.packageName}@${versions.sysPackVersion} is already up-to-date but --force was set`,
            );
        }

        // We are here so package is not up to date, set packContext of extended nodes to not up to date, to not miss
        // factories updated by node extensions.
        pack.nodeExtensions.forEach(nodeExtension => {
            // get the factory from the application
            const decorators = getDecorators(nodeExtension as any);
            const extendedCtor = decorators.nodeExtension.extends();
            const factory = upgradeContext.application.getFactoryByConstructor(extendedCtor);
            // get the extended factories package packContext
            const extPackContext = upgradeContext.getPackContext(factory.package);
            if (extPackContext.isUpToDate) {
                extPackContext.isUpToDate = false;
                logger.info(
                    `Package '${pack.packageName}' extends a node in ${factory.package.name}. The factories of ${factory.package.name} will be checked.`,
                );
            }
        });

        logger.info(
            `\t- check package '${pack.packageName}' for upgrade ${versions.sysPackVersion} -> ${versions.packageJsonVersion}`,
        );
        if (options.skipSchemaChanges) return;

        // Display the actions for the package
        const actionsAndVersions = await packContext.getActionsToExecute();
        actionsAndVersions.forEach(actionAndVersion => {
            const actionType = actionAndVersion.action instanceof SchemaUpgradeAction ? 'schema' : 'data';
            logger.info(`\t\t- ${actionType}(${actionAndVersion.version}): ${actionAndVersion.action.description}`);
        });

        await packContext.checkSchemaChangesRestrictions();
    }

    /**
     * Checks whether a factory can be upgraded
     */
    private static async _checkFactory(
        context: Context,
        upgradeContext: UpgradeContext,
        factory: NodeFactory,
    ): Promise<void> {
        const packContext = upgradeContext.getPackContext(factory.package);
        if (packContext.isUpToDate) {
            logger.verbose(
                () =>
                    `\t- factory '${factory.fullName}' was skipped, package '${packContext.pack.packageName}' is up-to-date`,
            );
            return;
        }

        logger.info(`\t- check factory '${factory.fullName}'`);
        const schemaChanges = await packContext.getFactoryChanges(context, factory);

        const tableName = schemaChanges.renameTable?.from || factory.tableName;

        const checkNewProperty = async (property: Property, oldColumnDefinition?: ColumnDefinition): Promise<void> => {
            if (property.isNullable) {
                // nothing to check
                return;
            }
            if (oldColumnDefinition && !oldColumnDefinition.isNullable) {
                // The property is not nullable but it has been changed from a column that was
                // already not nullable. We can consider there is nothing to check.
                return;
            }
            if (property instanceof ReferenceProperty && property.defaultValue === undefined) {
                // A data action must be provided for not-nullable references

                const actionAndVersion = (await upgradeContext.getAllActionsForFactory(factory)).find(av => {
                    if (av.action instanceof DataUpdateAction) {
                        // We are looking for a DataUpdateAction:
                        // - with no where clause (to be sure that all the rows will be set)
                        // - that will set the current property
                        // All the members in the 'set' must be 'ts-to-sql' compliant
                        if (av.action.options.where) return false;
                        if (av.action.options.set[property.name] === undefined) return false;
                        return true;
                    }
                    if (av.action instanceof CustomSqlAction || av.action instanceof UnsafeCustomSqlAction) {
                        const actionFixes = av.action.params.fixes;
                        if (actionFixes?.notNullableColumns) {
                            // Look for a fix to adress this nullable property
                            return actionFixes.notNullableColumns.find(fix => fix.column === property.columnName);
                        }
                        return false;
                    }
                    return false;
                });
                if (!actionAndVersion) {
                    // We could not find any dataUpdateAction that will take care of this nullable reference
                    upgradeContext.issues.push({
                        message: `${factory.package.name}/${factory.name}.${property.name}: a non-nullable reference must have a DataUpdateAction (with no where clause).`,
                        severity: 'error',
                    });
                }
            }
        };

        if (schemaChanges.isNewTable) {
            logger.info('\t\t- new table');
        } else {
            if (schemaChanges.renameTable) {
                logger.info(
                    `\t\t- rename table '${schemaChanges.renameTable.from}' to '${schemaChanges.renameTable.to}'`,
                );
            }
            const baseTableChanges = schemaChanges.baseTableChanged;
            if (baseTableChanges) {
                if (baseTableChanges.from && baseTableChanges.to) {
                    logger.info(
                        `\t\t- base table was changed from '${baseTableChanges.from}' to '${baseTableChanges.to}'`,
                    );
                } else if (baseTableChanges.from) {
                    logger.info(`\t\t- base table (${baseTableChanges.from}) was removed`);
                } else {
                    logger.info(`\t\t- base table was set to ${baseTableChanges.to}`);
                }
            }

            schemaChanges.columns.renamed.forEach(renamed => {
                logger.info(`\t\t- column ${tableName}.${renamed.from} renamed to '${renamed.to}'`);
            });
            await asyncArray(schemaChanges.columns.updated).forEach(async updated => {
                logger.info(
                    `\t\t- property ${factory.name}.${updated.new.name} updated (${updated.updateTypes
                        .map(u => u.description)
                        .join(', ')})`,
                );
                await checkNewProperty(updated.new, updated.old);
            });
            schemaChanges.columns.obsolete.forEach(col => {
                logger.info(`\t\t- column ${tableName}.${col.name} deleted`);
            });
            await asyncArray(schemaChanges.missingProperties).forEach(async prop => {
                logger.info(`\t\t- property ${factory.name}.${prop.name} added`);
                await checkNewProperty(prop);
            });
            schemaChanges.triggers.new.forEach(triggerDef => {
                logger.info(
                    `\t\t- trigger ${triggerDef.name} (${triggerDef.functionName}@${triggerDef.when} ${triggerDef.event}) added`,
                );
            });
            schemaChanges.triggers.obsolete.forEach(triggerDef => {
                logger.info(
                    `\t\t- trigger ${triggerDef.name} (${triggerDef.functionName}@${triggerDef.when} ${triggerDef.event}) deleted`,
                );
            });
        }

        // Create some automatic data actions
        if (schemaChanges.columns.updated.length > 0) {
            let sqlConverter: SqlConverter | undefined;
            await asyncArray(schemaChanges.columns.updated).forEach(updated =>
                asyncArray(updated.updateTypes).forEach(async inconsistency => {
                    if (inconsistency instanceof ColumnInconsistencyNullable) {
                        if (updated.old.isNullable && !updated.new.isNullable) {
                            // We need to check if there is a customDataAction that handle this change
                            // Temporay waiting for platform enhancement
                            // Enhancement request : https://jira.sage.com/browse/XT-66651
                            const actionAndVersion = (await upgradeContext.getAllActionsForFactory(factory)).find(
                                av => {
                                    if (
                                        av.action instanceof CustomSqlAction ||
                                        av.action instanceof UnsafeCustomSqlAction
                                    ) {
                                        const actionFixes = av.action.params.fixes;
                                        if (actionFixes?.notNullableColumns) {
                                            // Look for a fix to adress this nullable property
                                            return actionFixes.notNullableColumns.find(
                                                fix => fix.column === updated.new.columnName,
                                            );
                                        }
                                        return false;
                                    }
                                    return false;
                                },
                            );
                            if (actionAndVersion) {
                                return;
                            }

                            // Nullable column -> not-nullable: we need an automatic dataAction to ensure
                            // that the db will not contain any null (otherwise, the post-upgrade would
                            // fail when attempting to set the NOT NULL constraint)
                            if (!sqlConverter) sqlConverter = new SqlConverter(context, factory);
                            const defVal = UpgradeSqlSchema._getValueForAutomaticDataAction(
                                factory,
                                sqlConverter,
                                updated.new,
                            );
                            const defValObj = { [updated.new.name]: defVal };
                            // Note: automatic data actions must be executed BEFORE the custom actions
                            let whereBody = `return this.${updated.new.name} == null`;
                            if (updated.new.isStringProperty() && !updated.new.isLocalized) {
                                whereBody = `${whereBody} || this.${updated.new.name} === ''`;
                            }
                            await packContext.registerAutomaticAction(
                                new DataUpdateAction({
                                    description: `Auto data update action for column ${factory.tableName}.${updated.old.name} (replace null values)`,
                                    node: () => factory.nodeConstructor,
                                    /* istanbul ignore next */
                                    // eslint-disable-next-line @typescript-eslint/no-implied-eval
                                    where: new Function(whereBody) as any,
                                    set: defValObj,
                                }),
                            );
                        }
                    } else if (inconsistency instanceof ColumnInconsistencyType) {
                        await packContext.registerAutomaticAction(
                            new ConvertColumnTypeAction(inconsistency, {
                                node: () => factory.nodeConstructor,
                                oldColumn: updated.old,
                                newProperty: updated.new,
                            }),
                        );
                    }
                }),
            );
        }
    }

    /**
     * Executes an automatically created (by the upgrade engine) action
     * @param context
     * @param action
     */
    private static async _executeAutomaticAction(
        upgradeContext: UpgradeContext,
        context: Context,
        automaticAction: UpgradeAction,
    ): Promise<void> {
        logger.info(`Execute automatic action (${automaticAction.kind}): ${automaticAction.description}`);
        await upgradeContext.preExecuteAction(automaticAction, context);
        await upgradeContext.executeAction(automaticAction, context);
        await upgradeContext.postExecuteAction(automaticAction, context);
    }

    /**
     * Returns a string representation of a IndexDefinition
     */
    private static _indexDefinitionToString(idxDef: IndexDefinition): string {
        return `${idxDef.name}(${idxDef.columns.map(col => col.name).join(',')})${idxDef.isUnique ? '/UNIQUE' : ''}`;
    }

    /**
     * Pre-upgrades a factory:
     * - create new columns (with no constraint)
     * - remove the constraints on objects to be deleted (but don't delete the objects)
     * - ...
     * @param upgradeContext
     * @param factory
     */
    private static async _preUpgradeFactory(
        upgradeContext: UpgradeContext,
        factory: NodeFactory,
        preUpgradeContext: PreUpgradeContext,
    ): Promise<void> {
        const packContext = upgradeContext.getPackContext(factory.package);
        if (packContext.isUpToDate) return;

        logger.info(`\t- pre-${UpgradeSqlSchema._upgradeOrCheckPrefix(upgradeContext)} factory '${factory.fullName}'`);

        await upgradeContext.withCommittedContext(async context => {
            // Apply the changes to the schema
            const schemaChanges = await packContext.getFactoryChanges(context, factory);

            const skipConstraints = context.testMode;
            if (schemaChanges.isNewTable) {
                // Create the table with no constraints
                logger.info(`\t\t -creating new table: ${factory.table.name}`);
                await factory.table.createTable(context, {
                    skipConstraints,
                    skipForeignKeys: true,
                    skipDrop: true,
                    skipTriggers: false,
                });
                packContext.createdFactories[factory.name] = true;
                return;
            }

            const { outOfDateColumns, columnsToAlter } = UpgradeSqlSchema._manageUpdatedColumns(schemaChanges);

            await asyncArray(schemaChanges.columns.obsolete).forEach(async colDef => {
                outOfDateColumns.push(colDef);
                if (!colDef.isNullable) {
                    // Remove the 'NULL' constraint for the column
                    await factory.table.alterColumns(context, [
                        {
                            definition: colDef,
                            inconsistencies: [new ColumnInconsistencyNullable(false, true)],
                        },
                    ]);
                }
            });

            await factory.table.alterColumns(context, columnsToAlter);

            const indexesToDelete: Dict<IndexToDelete> = {};

            // Read the current table schema
            const tableSchema = await upgradeContext.getTableDefinition(context, factory.table.name);

            // Remove obsolete constraints
            if (outOfDateColumns.length) {
                // Drop all the contraints that reference obsolete or out-of-date columns
                await asyncArray(outOfDateColumns).forEach(async colDef => {
                    // Drop the foreign keys
                    const fksToDelete =
                        tableSchema.foreignKeys?.filter(fk => fk.columnNames.includes(colDef.name)) || [];
                    await asyncArray(fksToDelete).forEach(fk => factory.table.dropForeignKey(context, fk.name));
                    // Drop the indexes
                    tableSchema.indexes?.forEach(idx => {
                        if (idx.columns.some(col => col.name === colDef.name)) {
                            indexesToDelete[idx.name] = { reason: 'changed', currentDefinition: idx };
                        }
                    });
                });
            }

            // Check that indexes are still valid
            UpgradeSqlSchema._checkIndexesToDelete(context, factory, tableSchema, indexesToDelete);
            await asyncArray(Object.entries(indexesToDelete)).forEach(async ([idxName, details]) => {
                // The index might need to be recreated because of a change in its definition
                let message: string;
                if (details.reason === 'changed') {
                    if (details.expectedDefinition) {
                        message = `drop index with invalid definition on table ${factory.table.name}: got ${UpgradeSqlSchema._indexDefinitionToString(details.currentDefinition)}, expected ${UpgradeSqlSchema._indexDefinitionToString(details.expectedDefinition)}`;
                    } else {
                        message = `drop index with invalid definition on table ${factory.table.name}: ${UpgradeSqlSchema._indexDefinitionToString(details.currentDefinition)}`;
                    }
                } else {
                    message = `drop obsolete index on table: ${factory.table.name} - ${UpgradeSqlSchema._indexDefinitionToString(details.currentDefinition)}`;
                }
                loggers.upgrade.info(`\t\t- ${message}`);
                if (
                    await upgradeContext.raiseErrorIfSchemaChangesAreForbidden(
                        `Cannot drop index ${factory.table.name}.${idxName}`,
                    )
                )
                    return;
                await factory.table.dropIndex(context, idxName);
            });

            // upgrade enum types.
            await SchemaEnumUpgradeHelper.upgradePropertiesEnumType(
                upgradeContext,
                context,
                factory,
                preUpgradeContext,
            );

            // Create the missing columns (with no constraint - constraints will be set in the postUpgrade steps)
            await UpgradeSqlSchema._createMissingColumnsWithoutConstraints(
                upgradeContext,
                context,
                factory,
                schemaChanges.missingProperties,
            );
        });
    }

    /**
     * Create the missing columns, without any constraints
     * @param schemaChanges
     * @param context
     * @param upgradeContext
     * @param factory
     */
    private static async _createMissingColumnsWithoutConstraints(
        upgradeContext: UpgradeContext,
        context: Context,
        factory: NodeFactory,
        missingProperties: Property[],
    ): Promise<void> {
        if (missingProperties.length === 0) return;
        const sqlConverter = new SqlConverter(context, factory);
        loggers.upgrade.info(`\t\t- add missing columns: ${missingProperties.map(p => p.columnName)}`);

        await UpgradeSqlSchema._addColumnsToSql(context, upgradeContext, factory.table, missingProperties);

        // Generate an automatic dataAction to set a value for all the new columns
        const valuesForAutomaticSet: Dict<any> = {};

        const actionsForFactory = (await upgradeContext.getAllActionsForFactory(factory)).map(av => av.action);

        missingProperties.forEach(prop => {
            if (
                actionsForFactory
                    .filter(action => action instanceof CustomSqlAction || action instanceof UnsafeCustomSqlAction)
                    .some((action: CustomSqlAction | UnsafeCustomSqlAction) => {
                        const fixes = action.params.fixes;
                        if (fixes == null) return false;
                        return fixes.notNullableColumns?.find(
                            f => f.table === factory.tableName && f.column === prop.columnName,
                        );
                    })
            ) {
                // A custom SQL action will provide a value for the column
                return;
            }

            if (
                actionsForFactory
                    .filter(action => action instanceof DataUpdateAction)
                    .some((action: DataUpdateAction<Node>) => (action.options.set as any)[prop.name] !== undefined)
            ) {
                // A dataAction will provide a value for the column
                return;
            }

            const defVal = UpgradeSqlSchema._getValueForAutomaticDataAction(factory, sqlConverter, prop);
            if (defVal != null) {
                valuesForAutomaticSet[prop.name] = defVal;
            }
        });
        // Create an automatic data update action that will update the column for new properties
        // with either their defaultValue() or the default value of their domain.
        await asyncArray(Object.keys(valuesForAutomaticSet)).forEach(async propName => {
            const prop = missingProperties.find(p => p.name === propName)!;
            let whereBody = `return this.${propName} == null`;
            if (prop.isStringProperty() && !prop.isLocalized) {
                whereBody = `${whereBody} || this.${propName} === ''`;
            }
            await UpgradeSqlSchema._executeAutomaticAction(
                upgradeContext,
                context,
                new DataUpdateAction({
                    description: `Auto data action for property ${factory.name}.${propName}`,
                    node: () => factory.nodeConstructor,
                    // we need to instantiate a Function object here because we are compiling directly with with xtrem-minify and not xtrem-cli.
                    // The resulting dummy source will be spaces and will not be resolved correctly in convertFunction in ts to sql
                    /* istanbul ignore next */
                    // eslint-disable-next-line @typescript-eslint/no-implied-eval
                    where: new Function(whereBody) as any,
                    set: { [propName]: valuesForAutomaticSet[propName] },
                }),
            );
        });
    }

    /**
     * Recompute the hash column of content addressable node
     * @param upgradeContext
     */
    private static async _checkAndSyncContentAddressable(application: Application): Promise<void> {
        logger?.info(`Checking and syncing values hash`);
        const sysTenantIds = await getTenantIdList(application);
        await asyncArray(sysTenantIds).forEach(async tenantId => {
            await application.withReadonlyContext(
                tenantId,
                async context => {
                    await asyncArray(
                        application.getAllFactories().filter(factory => factory.isContentAddressable),
                    ).forEach(async factory => {
                        // If the table is new or if there is no change in the factory, we skip the hash recalculation
                        if (!(await factory.table.tableExists(context))) return;
                        logger?.info(
                            `Tenant ${tenantId}- Checking and syncing values hash of ${context.schemaName}.${factory.tableName}`,
                        );
                        await checkAndSyncFactoryValuesHash(context, factory, logger);
                    });
                },
                { source: 'customMutation' }, // in runInWritableContext we only allow certain sources (see ContextSource from context.ts)
            );
        });

        logger?.info(`Finished checking and syncing values hash`);
    }

    /**
     * Check the indexes that need to be deleted
     * @param tableSchema
     * @param indexesToDelete
     * @param factory
     * @param context
     */
    private static _checkIndexesToDelete(
        context: Context,
        factory: NodeFactory,
        tableSchema: TableDefinition,
        indexesToDelete: Dict<IndexToDelete>,
    ) {
        tableSchema?.indexes?.forEach(idx => {
            if (indexesToDelete[idx.name]) {
                // We already planned to delete this index
                return;
            }
            const nodeIndex = factory.indexes?.find(i => i.name === idx.name);
            if (!nodeIndex) {
                // This index exists in the db but not in the code
                indexesToDelete[idx.name] = { reason: 'obsolete', currentDefinition: idx };
                return;
            }
            const tenantIdColumn = SystemProperties.tenantIdColumn(factory);
            const expectedIndex = factory.table.getIndexDefinition(context.schemaName, nodeIndex, tenantIdColumn);
            if (!UpgradeSqlSchema._checkIndex(idx, expectedIndex)) {
                // The index exists in both the database and the code but its definition is wrong
                indexesToDelete[idx.name] = {
                    reason: 'changed',
                    expectedDefinition: expectedIndex,
                    currentDefinition: idx,
                };
            }
        });
    }

    /**
     * Inspect the updated columns and compute whether they need to be updated
     * @param schemaChanges
     */
    private static _manageUpdatedColumns(schemaChanges: UpgradeSchemaChanges): {
        outOfDateColumns: ColumnDefinition[];
        columnsToAlter: { definition: ColumnDefinition; inconsistencies: ColumnInconsistency[] }[];
    } {
        const outOfDateColumns: ColumnDefinition[] = [];
        const columnsToAlter: { definition: ColumnDefinition; inconsistencies: ColumnInconsistency[] }[] = [];

        schemaChanges.columns.updated.forEach(updated => {
            // The column will be upgraded, remove all the constraints that use it.
            outOfDateColumns.push(updated.old);
            updated.updateTypes.forEach(inconsistency => {
                if (inconsistency instanceof ColumnInconsistencyNullable) {
                    if (!updated.old.isNullable && updated.new.isNullable) {
                        // Not-nullable column -> nullable column
                        // We have to remove the 'NOT NULL' constraint
                        columnsToAlter.push({
                            definition: { ...updated.old, isNullable: true },
                            inconsistencies: [inconsistency],
                        });
                    }
                } else if (inconsistency instanceof ColumnInconsistencyType) {
                    // Nothing to do here
                } else if (inconsistency instanceof ColumnInconsistencyDefaultValue) {
                    // No need for any extra-action, the job will be done by the alterColumns
                    columnsToAlter.push({
                        definition: updated.old,
                        inconsistencies: [inconsistency],
                    });
                } else {
                    throw new Error(`Unsupported inconsistency type ${inconsistency.description}`);
                }
            });
        });
        return {
            outOfDateColumns,
            columnsToAlter,
        };
    }

    /**
     * Pre-execute all the actions, from all the packages
     */
    private static async _preExecuteActions(upgradeContext: UpgradeContext): Promise<void> {
        const allActions = await upgradeContext.getAllActionsToExecute();

        await asyncArray(allActions).forEach(async actionAndVersion => {
            const action = actionAndVersion.action;
            action.version = actionAndVersion.version;
            await upgradeContext.preExecuteAction(action);
        });
    }

    /**
     * Execute all the actions (except the drop node actions), from all the packages
     */
    private static async _executeActions(upgradeContext: UpgradeContext): Promise<void> {
        const getActionScore = (action: UpgradeAction): number => {
            if (action instanceof SchemaRenameNodeAction) {
                // Renaming of tables must be executed first because some other actions
                // may refer to a renamed table and they will probably use factory.tableName
                // to execute SQL queries (factory.tableName will return the NEW tablename)
                return 0;
            }
            if (action instanceof SchemaRenamePropertyAction) return 1;
            if (action.isAutomaticAction) {
                // Automatic actions (automatically created by the upgrade engine) must be executed
                // before actions declared in an UpgradeSuite
                if (action instanceof ConvertColumnTypeAction) {
                    // The conversion of column type must be processed at the very end
                    // as some other actions may refer to the old column type.
                    // For instance, when we reduce the size of a string column, there
                    // will be a first customSqlAction that will be in charge of truncating existing data
                    // Then, the column will be shrinked (by the ConvertColumnTypeAction)
                    return 10000;
                }
                return 100;
            }
            // All the other actions will have the same score so the order from the upgradeSuite
            // will be preserved
            return 1000;
        };

        // Sort the actions to execute:
        // - rename tables
        // - rename columns
        // - automatic actions (generated by the upgrade engine)
        // - user-defined actions (from an upgrade suite)
        const allActions = (await upgradeContext.getAllActionsToExecute())
            .map(actionAndVersion => actionAndVersion.action)
            .filter(action => !(action instanceof SchemaDropTableAction))
            .sort((action1, action2) => getActionScore(action1) - getActionScore(action2));
        await asyncArray(allActions).forEach(async action => {
            await upgradeContext.executeAction(action);
        });
    }

    /**
     * Execute all the actions to drop tables, from all the packages
     */
    private static async _executeDropNodesActions(upgradeContext: UpgradeContext): Promise<void> {
        const dropActions = (await upgradeContext.getAllActionsToExecute())
            .map(actionAndVersion => actionAndVersion.action)
            .filter(action => action instanceof SchemaDropTableAction);
        await asyncArray(dropActions).forEach(async action => {
            await upgradeContext.executeAction(action);
        });
    }

    private static truncateNames(names: string[], cutoff: number): string {
        let truncatedNames = names.slice(0, cutoff).join(', ');
        if (names.length > cutoff) truncatedNames += `, ...`;
        return truncatedNames;
    }

    private static _createInsertFunctions(upgradeContext: UpgradeContext, factories: NodeFactory[]): Promise<void> {
        const truncatedNames = this.truncateNames(
            factories.map(f => f.name),
            3,
        );
        logger?.info(`Creating SQL insert functions for ${factories.length} factories: ${truncatedNames}`);

        return upgradeContext.withoutRecording(() =>
            asyncArray(factories)
                .filter(factory => factory.hasSqlInsertFunction)
                .forEach(factory =>
                    upgradeContext.withCommittedContext(context => factory.table.createSqlInsertFunction(context)),
                ),
        );
    }

    private static _dropAllInsertFunctions(upgradeContext: UpgradeContext): Promise<void> {
        return upgradeContext.withoutRecording(() =>
            upgradeContext.withCommittedContext(async context => {
                const tableNames = await readTablesForSchema(context);

                const truncatedNames = this.truncateNames(tableNames, 3);
                logger?.info(`Dropping SQL insert functions for ${tableNames.length} tables: ${truncatedNames}`);

                await asyncArray(tableNames).forEach(tableName => Table.dropSqlInsertFunction(context, tableName));
            }),
        );
    }

    /**
     * Post-execute all the actions, from all the packages
     */
    private static async _postExecuteActions(upgradeContext: UpgradeContext): Promise<void> {
        const allActions = await upgradeContext.getAllActionsToExecute();
        await asyncArray(allActions).forEach(async actionAndVersion => {
            const action = actionAndVersion.action;
            await upgradeContext.postExecuteAction(action);
        });
    }

    /**
     * Create all the missing triggers
     * @param context
     * @param factory
     * @param schemaChanges
     */
    private static async _createMissingTriggers(
        context: Context,
        factory: NodeFactory,
        schemaChanges: UpgradeSchemaChanges,
    ): Promise<void> {
        if (schemaChanges.triggers.new.length) {
            logger.info(`\t\t- creating ${schemaChanges.triggers.new.length} new triggers on ${factory.name}`);
            schemaChanges.triggers.new.forEach(t => {
                logger.info(`\t\t\t- ${t.name} (${t.functionName}@${t.when} ${t.event})`);
            });
            await factory.table.createTriggers(context, schemaChanges.triggers.new);
        }
    }

    private static async _upgradeFactory(upgradeContext: UpgradeContext, factory: NodeFactory): Promise<void> {
        const packContext = upgradeContext.getPackContext(factory.package);
        logger.info(`\t- ${UpgradeSqlSchema._upgradeOrCheckPrefix(upgradeContext)} factory '${factory.fullName}'`);
        const schemaChanges = await upgradeContext.withUncommittedContext(context =>
            packContext.getFactoryChanges(context, factory),
        );

        if (schemaChanges.triggers.obsolete.length) {
            logger.info(
                `\t\t- dropping ${schemaChanges.triggers.obsolete.length} obsolete triggers on ${factory.name}`,
            );
            schemaChanges.triggers.obsolete.forEach(t => {
                logger.info(`\t\t\t- ${t.name} (${t.functionName}@${t.when} ${t.event})`);
            });
            await upgradeContext.withCommittedContext(context =>
                factory.table.dropTriggers(context, schemaChanges.triggers.obsolete),
            );
        }

        await upgradeContext.withCommittedContext(context =>
            this._createMissingTriggers(context, factory, schemaChanges),
        );
    }

    private static _upgradeOrCheckPrefix(upgradeContext: UpgradeContext): string {
        return upgradeContext.options.checkSchema ? 'check' : 'upgrade';
    }

    private static async _postUpgradeFactory(upgradeContext: UpgradeContext, factory: NodeFactory): Promise<void> {
        const packContext = upgradeContext.getPackContext(factory.package);
        if (packContext.isUpToDate) return;

        logger.info(`\t- post-${UpgradeSqlSchema._upgradeOrCheckPrefix(upgradeContext)} factory '${factory.fullName}'`);
        await upgradeContext.withCommittedContext(async context => {
            const schemaChanges = await packContext.getFactoryChanges(context, factory);

            // Read the current table schema
            const tableSchema = await upgradeContext.getTableDefinition(context, factory.table.name);

            if (context.testMode) {
                // Create the foreign keys in db from the FKs declared in the factory.table object
                logger.info(`\t\t- creating foreign keys on table ${factory.table.name}.`);
                await factory.table.fixForeignKeys(context, tableSchema, {
                    skipExists: true,
                    skipBaseTable: true,
                });
                // Note: verify will throw an exception if sth is wrong
                await factory.verifyTable(context, { doNotRecurseOnReferences: true, compareToSqlSchema: true });
                return;
            }

            // TODO: packContext.getSchemaChanges(context, factory) should also include changes on indexes/foreignKeys
            // so that we could avoid this second readTableSchema. _postUpgradeFactory should only rely on these changes
            // and should not need to read the schema
            const propsToUpdate = [] as { property: Property; inconsistencies: ColumnInconsistency[] }[];
            const propsByColName = {} as Dict<Property>;
            const colsToDelete = [] as string[];

            // Check all the columns
            await asyncArray(factory.properties.filter(UpgradeSqlSchema._hasColumn)).forEach(async prop => {
                propsByColName[prop.requiredColumnName] = prop;
                const colDef = tableSchema.columns?.find(col => prop.columnName === col.name);
                if (!colDef) {
                    throw new Error(`Column ${factory.table.name}.${prop.columnName} does not exist`);
                }
                if (!packContext.createdFactories[factory.name]) {
                    if (
                        prop.isReferenceProperty() &&
                        !factory.table.foreignKeys?.some(fk => fk.columnNames.includes(prop.requiredColumnName))
                    ) {
                        // We are missing a foreign key
                        const referenceFactory = prop.targetFactory;
                        const foreignKey = factory.createForeignKey(referenceFactory, prop);
                        if (foreignKey) {
                            factory.table.foreignKeys.push(foreignKey);
                        }
                    }
                }
                const inconsistencies = await packContext.checkColumn(context, factory, colDef, prop);
                if (inconsistencies.length) {
                    propsToUpdate.push({ property: prop, inconsistencies });
                }
            });

            if (!packContext.createdFactories[factory.name]) {
                schemaChanges.columns.obsolete.forEach(colDef => {
                    colsToDelete.push(colDef.name);
                });
            }

            // Create the foreign keys in db from the FKs declared in the factory.table object
            const fks = await factory.table.fixForeignKeys(context, tableSchema, {
                skipBaseTable: true,
            });
            if (fks.createdFks > 0) logger.info(`\t\t- ${fks.createdFks} fks created on table ${factory.table.name}.`);
            if (fks.recreatedFks > 0)
                logger.info(`\t\t- ${fks.recreatedFks} fks re-created on table ${factory.table.name}.`);
            if (fks.renamedFks > 0) logger.info(`\t\t- ${fks.renamedFks} fks renamed on table ${factory.table.name}.`);

            if (colsToDelete.length > 0) {
                logger.info(`\t\t- dropping columns ${colsToDelete.join()} on table ${factory.table.name}.`);
                await factory.table.dropColumns(context, colsToDelete);
            }

            if (propsToUpdate.length > 0) {
                // Add all the constraints on the columns to update
                await UpgradeSqlSchema._alterColumnsToSql(context, factory.table, propsToUpdate);
            }

            // Create missing indexes
            // Missing indexes may be new indexes added to the node or out-of-date
            // indexes that were removed by the _preUpgradeFactory function
            const tenantIdColumn = SystemProperties.tenantIdColumn(factory);
            await asyncArray(factory.getSelfIndexes()).forEach(async index => {
                const indexDef = tableSchema.indexes?.find(idx => idx.name === index.name);
                if (indexDef) return;
                logger.info(
                    `\t\t- creating index ${index.name} on table ${factory.table.name} [${Object.keys(index.orderBy)}].`,
                );
                if (
                    await upgradeContext.raiseErrorIfSchemaChangesAreForbidden(
                        `Cannot create missing index ${factory.table.name}.${index.name}`,
                    )
                )
                    return;
                await factory.table.createIndex(
                    context,
                    factory.table.getIndexDefinition(context.schemaName, index, tenantIdColumn),
                );
            });

            // For some reason, the table may miss some triggers.
            // For instance, if there was a CustomSqlAction on this table, the current table was renamed to temp_xxx and
            // a brand new table was created without any trigger. Here, schemaChanges.triggers.new will not contain anything
            // because the table (that was renamed to temp_xxx) already had all the triggers but the newly created one does not
            // have any trigger. (schemaChanges are computed before the execution of any action)
            UpgradePackContext.computeTriggerChanges(context, factory, tableSchema, schemaChanges);
            await this._createMissingTriggers(context, factory, schemaChanges);

            // Note: verify will throw an exception if sth is wrong
            // Note: we don't need to recurse on references here because we already process the factories in the
            // right topology order. This highly improves the performances of the post-upgrade process.
            await factory.verifyTable(context, { doNotRecurseOnReferences: true, compareToSqlSchema: true });
        });
    }

    private static _getRealTable(rootTable: Table, columnName: string): Table {
        if (rootTable.columns.some(col => col.columnName === columnName)) return rootTable;
        if (rootTable.baseTable) return UpgradeSqlSchema._getRealTable(rootTable.baseTable, columnName);
        throw new Error(`Could not find the real table for column ${rootTable.name}.${columnName}`);
    }

    /**
     * Create (in SQL) the columns for the given properties
     */
    private static async _addColumnsToSql(
        context: Context,
        upgradeContext: UpgradeContext,
        table: Table,
        properties: Property[],
    ): Promise<void> {
        // Group the columns per table (some colDefs may be inherited from base tables)
        const colsByTable = {} as Dict<{
            table: Table;
            colDefs: ColumnDefinition[];
            tableNameToUse?: string;
        }>;

        await asyncArray(properties).forEach(async prop => {
            const realTable = UpgradeSqlSchema._getRealTable(table, prop.requiredColumnName);
            if (!colsByTable[realTable.name]) {
                colsByTable[realTable.name] = {
                    table: realTable,
                    colDefs: [],
                };

                /*
                 * We need to check the real table to see if it has been renamed
                 * If it has been renamed then we need to pass the old table name as the tableNameToUse as we have not
                 * executed the rename table action as yet
                 */
                const realFactory = realTable.factory;
                const realSchemaChanges = await upgradeContext
                    .getPackContext(realFactory.package)
                    .getFactoryChanges(context, realFactory);
                if (realSchemaChanges.renameTable)
                    colsByTable[realTable.name].tableNameToUse = realSchemaChanges.renameTable.from;
            }
            const column = realTable.columns.find(col => col.columnName === prop.columnName);
            if (!column) throw new Error(`Trying to create invalid column ${realTable.name}.${prop.columnName}`);
            const colDef = realTable.getColumnDefinition(column);
            colsByTable[realTable.name].colDefs.push(colDef);
        });

        await asyncArray(Object.values(colsByTable)).forEach(d =>
            d.table.addColumns(context, d.colDefs, {
                skipConstraints: true,
                tableNameToUse: d.tableNameToUse,
            }),
        );
    }

    /**
     * Runs all the 'alter ... '  sql statements to fix the given inconsistencies
     */
    private static async _alterColumnsToSql(
        context: Context,
        table: Table,
        items: { property: Property; inconsistencies?: ColumnInconsistency[] }[],
    ): Promise<void> {
        // Group the columns per table (some colDefs may be inherited from base tables)
        const colsByTable = {} as Dict<{
            table: Table;
            columns: { definition: ColumnDefinition; inconsistencies: ColumnInconsistency[] }[];
        }>;
        items.forEach(item => {
            const realTable = UpgradeSqlSchema._getRealTable(table, item.property.requiredColumnName);
            if (!colsByTable[realTable.name]) {
                colsByTable[realTable.name] = {
                    table: realTable,
                    columns: [],
                };
            }
            const column = realTable.columns.find(col => col.columnName === item.property.columnName);
            if (!column)
                throw new Error(`Trying to upgrade inexisting column ${realTable.name}.${item.property.columnName}`);
            const colDef = realTable.getColumnDefinition(column);
            // When updating the column in db, we want to rely on the 'isNullable' status of the property (the 'isNullable'
            // status of the column may have been upgraded in the _preUpgrade steps)
            colDef.isNullable = item.property.isNullable;
            colsByTable[realTable.name].columns.push({
                definition: colDef,
                inconsistencies:
                    item.inconsistencies?.filter(
                        // All the 'ALTER TABLE...ALTER COLUMNS' for type inconsistencies have
                        // already been processed by a ConvertColumnTypeAction action
                        inconsistency => !(inconsistency instanceof ColumnInconsistencyType),
                    ) || [],
            });
        });
        await asyncArray(Object.values(colsByTable)).forEach(d => d.table.alterColumns(context, d.columns));
    }

    /**
     * Returns whether a property should be bound to a SQL column
     */
    private static _hasColumn(prop: Property): boolean {
        if (!prop.isStored) return false;
        if (prop.isInherited) return false;
        return true;
    }

    /**
     * Checks whether an NodeIndexDefinition is aligned with an IndexDefinition
     */
    private static _checkIndex(currentIndex: IndexDefinition, expectedIndex: IndexDefinition): boolean {
        const cleanupExpression = (exp?: string): string | undefined => {
            if (exp == null) return exp;
            // When we set COALESCE(value_3, (- (2::bigint ^ 62::bigint))::bigint) on a index
            // we could get COALESCE(value_3, (- (2::bigint::double precision ^ 62::bigint::double precision))::bigint) from the metadata
            // Same, postgres could add many (, ((, )), ) to the expression. The easiest is to ignore all the parenthesis
            return exp.replace(/(::bigint|::double precision|\(|\))/g, '');
        };

        if (currentIndex.name !== expectedIndex.name) return false;
        // Note: expectedIndex.isUnique can be undefined (default is false)
        if (currentIndex.isUnique !== !!expectedIndex.isUnique) return false;
        const currentColumns = [...currentIndex.columns];
        if (currentColumns.length !== expectedIndex.columns.length) return false;
        if (
            !currentColumns.every((col, idx) => {
                const expectedColumn = expectedIndex.columns[idx];
                if (col.name !== expectedColumn.name) return false;
                if (col.ascending !== expectedColumn.ascending) return false;
                if (cleanupExpression(col.expression) !== cleanupExpression(expectedColumn.expression)) return false;
                return true;
            })
        ) {
            return false;
        }
        return true;
    }

    /**
     * Returns the value (or the function that will return a value) to be assigned to a property when generating
     * an automatic dataUpdateAction
     */
    private static _getValueForAutomaticDataAction(
        factory: NodeFactory,
        sqlConverter: SqlConverter,
        prop: Property,
    ): any {
        // The property has an explicit SQL default
        if (prop.sqlAttributes?.default) return `${prop.sqlAttributes.default} /*${dbCommandMarker}*/`;

        const defVal = prop.defaultValue;
        if (defVal === undefined) {
            if (prop.isNullable) {
                // If the column is nullable, we don't have any issue: the db will contain null values
                return undefined;
            }
            logger.warn(
                `The property ${factory.name}.${prop.name} is not nullable and does not provide any defaultValue: the default value for its type will be used.`,
            );
            return prop.getTypeDefaultValue();
        }
        if (typeof defVal !== 'function') {
            if (defVal instanceof Stream) {
                // Special case for Streams. For instance, for TextStreams, We have to send '' to the db instead of {value:''}
                return defVal.toString();
            }
            // defaultValue returns a constant (written as "defaultValue: true")
            return defVal;
        }
        try {
            // Check if ts-to-sql is able to parse this function
            sqlConverter.convertFunction(defVal as () => any);
            return defVal;
        } catch (err) {
            // ts-to-sql is not able to manage the defaultValue() function.
            if (!prop.isNullable) {
                // The new column is not nullable so the _postUpgrade step will fail when setting the 'NOT NULL'
                // constraint.
                throw new Error(
                    `The defaultValue function of property ${factory.name}.${prop.name} is too complex for automatic upgrade.`,
                );
            }
        }
        return undefined;
    }
}
