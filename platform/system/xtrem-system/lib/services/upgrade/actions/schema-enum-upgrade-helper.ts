import { Application, asyncArray, Context, EnumDataType, EnumSqlContext, NodeFactory } from '@sage/xtrem-core';
import * as _ from 'lodash';
import { parse as parsePgArray } from 'postgres-array';
import { loggers } from '../../loggers';
import { UpgradeContext } from '../upgrade-context';
import { ActionAndVersion } from '../upgrade-pack-context';
import { PreUpgradeContext } from '../upgrade-sql-schema';
import { SchemaEnumRenameAction } from './schema-enum-rename-action';
import { SchemaEnumUpgradeAction } from './schema-enum-upgrade-action';

/**
 * Helper abstract class that centralizes methods used for enums during an upgrade.
 */
export abstract class SchemaEnumUpgradeHelper {
    /**
     * get the datatype name of an enum
     * @param enumName
     * @returns
     */
    static getTypeName(enumName: string): string {
        return `${_.snakeCase(enumName)}_enum`;
    }

    /**
     * On Postgres create all the new enum datatypes and add new attributes to existing enum datatypes, for the passed in factory.
     * @param context
     * @param factory
     */
    static async upgradePropertiesEnumType(
        upgradeContext: UpgradeContext,
        context: Context,
        factory: NodeFactory,
        preUpgradeContext: PreUpgradeContext,
    ): Promise<void> {
        await this._addNewEnumOrAddAttributes(upgradeContext, context, factory, {
            addNewEnumAttributes: true,
            addNewEnum: true,
            preUpgradeContext,
        });
    }

    static async getEnumAttributes(sqlContext: EnumSqlContext, enumTypeName: string): Promise<string[]> {
        return parsePgArray(await sqlContext.getEnumAttributes(enumTypeName), v => v);
    }

    /**
     * Method used to check if there are any enum related updates for the factory passed in. The method iterates through the enum and enumArray properties of the factory,
     * if the enum datatype exists on Postgres then we compare the values on the Postgres datatype to the typescript enum values. If there is a difference in the values or the enum data type
     * does not exist on Postgres then we return true.
     * @param application
     * @param factory
     * @returns
     */
    static checkEnumProperties(application: Application, factory: NodeFactory): Promise<boolean> {
        return asyncArray(
            factory.properties.filter(prop => (prop.type === 'enum' || prop.type === 'enumArray') && prop.isStored),
        )
            .map(async prop => {
                const enumDataType = prop.dataType as EnumDataType;
                const enumTypeName = SchemaEnumUpgradeHelper.getTypeName(enumDataType.enumName());
                const sqlContext = new EnumSqlContext(application);
                const enumAttributes = enumDataType.values;
                const enumExist = await sqlContext.enumTypeExists(enumTypeName);

                if (enumExist) {
                    const currentEnumAttributes = await SchemaEnumUpgradeHelper.getEnumAttributes(
                        sqlContext,
                        enumTypeName,
                    );

                    // add new enum attributes
                    if (!_.isEqual(enumAttributes, currentEnumAttributes)) {
                        const attributesToAdd: {
                            newValue: string;
                            position?: { after?: string | undefined; before?: string };
                        }[] = [];
                        for (let i = 0; i < enumAttributes.length; i += 1) {
                            const oldAttributeValue = currentEnumAttributes[i];
                            const newEnumAttributeValue = enumAttributes[i];
                            if (newEnumAttributeValue !== oldAttributeValue) {
                                if (currentEnumAttributes.indexOf(newEnumAttributeValue) < 0) {
                                    currentEnumAttributes.splice(i, 0, newEnumAttributeValue);
                                    attributesToAdd.push({
                                        newValue: newEnumAttributeValue,
                                        position: {
                                            before: currentEnumAttributes[i + 1],
                                        },
                                    });
                                }
                            }
                        }

                        return attributesToAdd.length > 0;
                    }
                }

                return !enumExist;
            })
            .some(val => val);
    }

    /**
     * add new attribute to an existing enum, or, add new enum in case where the enum of an existing property is replaced with a new enum
     * @internal
     * @param context
     * @param factory
     * @param options
     */
    private static async _addNewEnumOrAddAttributes(
        upgradeContext: UpgradeContext,
        context: Context,
        factory: NodeFactory,
        options: { addNewEnum?: boolean; addNewEnumAttributes?: boolean; preUpgradeContext: PreUpgradeContext },
    ): Promise<void> {
        let allActions: ActionAndVersion[] | undefined;

        const getAllActions = async (): Promise<ActionAndVersion[]> => {
            if (allActions) return allActions;
            allActions = await upgradeContext.getAllActionsToExecute();
            return allActions;
        };

        await asyncArray(
            factory.properties.filter(prop => (prop.type === 'enum' || prop.type === 'enumArray') && prop.isStored),
        ).forEach(async prop => {
            const enumDataType = prop.dataType as EnumDataType;
            const enumName = enumDataType.enumName();
            if (options.preUpgradeContext.checkedEnums[enumName]) {
                // This enum was already processed.
                return;
            }
            options.preUpgradeContext.checkedEnums[enumName] = true;
            let enumTypeName = SchemaEnumUpgradeHelper.getTypeName(enumName);
            const sqlContext = new EnumSqlContext(context.application);
            const enumAttributes = enumDataType.values;
            let enumExists = await sqlContext.enumTypeExists(enumTypeName);
            if (!enumExists) {
                const renamingActionAndVersion = (await getAllActions()).find(actionAndVersion => {
                    if (!(actionAndVersion.action instanceof SchemaEnumRenameAction)) return false;
                    return actionAndVersion.action.dataType.enumName() === enumName;
                });
                if (renamingActionAndVersion) {
                    const renamingAction = renamingActionAndVersion.action as SchemaEnumRenameAction;
                    // The enum does not exist but we must not create it as it will be created by the renaming of
                    // an already existing enum
                    enumExists = true;
                    // Any missing member must be added to the 'old' enum (the one that is currently existing in
                    // database and will be renamed later
                    enumTypeName = renamingAction.oldEnumName;
                }
            }
            if (enumExists && options.addNewEnumAttributes) {
                const currentEnumAttributes = await SchemaEnumUpgradeHelper.getEnumAttributes(sqlContext, enumTypeName);
                if (_.isEqual(enumAttributes, currentEnumAttributes)) return;

                const attributesToAdd: {
                    newValue: string;
                    position?: { after?: string | undefined; before?: string };
                }[] = [];
                const upgradeAction = (await getAllActions()).find(actionAndVersion => {
                    if (!(actionAndVersion.action instanceof SchemaEnumUpgradeAction)) return false;
                    return actionAndVersion.action.dataType.enumName() === enumName;
                })?.action as SchemaEnumUpgradeAction;

                for (let i = 0; i < enumAttributes.length; i += 1) {
                    const oldAttributeValue = currentEnumAttributes[i];
                    const newEnumAttributeValue = enumAttributes[i];
                    // eslint-disable-next-line no-continue
                    if (newEnumAttributeValue === oldAttributeValue) continue;

                    // eslint-disable-next-line no-continue
                    if (currentEnumAttributes.indexOf(newEnumAttributeValue) !== -1) continue;

                    if (
                        upgradeAction &&
                        upgradeAction.options.valuesMapping[oldAttributeValue] === newEnumAttributeValue
                    ) {
                        // This member does not exist but we must not create it, it will be
                        // created by the SchemaEnumUpgradeAction when renaming the member 'oldAttributeValue'
                        // to 'newEnumAttributeValue'
                        currentEnumAttributes[i] = newEnumAttributeValue;
                        // eslint-disable-next-line no-continue
                        continue;
                    }
                    currentEnumAttributes.splice(i, 0, newEnumAttributeValue);
                    attributesToAdd.push({
                        newValue: newEnumAttributeValue,
                        position: {
                            before: currentEnumAttributes[i + 1],
                        },
                    });
                }

                await asyncArray(attributesToAdd).forEach(
                    async (attribute: { newValue: string; position?: { after?: string; before?: string } }) => {
                        loggers.upgrade.verbose(
                            () =>
                                `Add attribute ${attribute.newValue} to ${enumTypeName} ${
                                    attribute.position?.before
                                        ? `before ${attribute.position?.before}`
                                        : 'at the last position.'
                                }`,
                        );
                        await sqlContext.addAttributesToEnum(enumTypeName, attribute);
                    },
                );
            }

            if (!enumExists && options.addNewEnum) {
                await sqlContext.createEnumTypes([enumDataType.getEnumType()]);
            }
        });
    }
}
