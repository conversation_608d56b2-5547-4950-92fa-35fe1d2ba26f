/* istanbul ignore file */
import { AnyRecord, asyncArray, Context, Node, SchemaSqlContext, SystemProperties } from '@sage/xtrem-core';
import { nameToSqlName } from '@sage/xtrem-shared';
import { loggers } from '../../loggers';
import { DataUpgradeAction } from './data-upgrade-action';
import { UpgradeActionExecuteOptions, UpgradeActionOptions } from './upgrade-action';

interface EncryptPropertiesActionOptions<This extends Node> extends UpgradeActionOptions {
    node: () => { new (): This };
    properties: (keyof This)[];
}
export class EncryptPropertiesAction<This extends Node> extends DataUpgradeAction<This> {
    #propertiesToEncrypt: string[];

    constructor(readonly options: EncryptPropertiesActionOptions<This>) {
        super({
            ...options,
            description: `${options.node().name}: Encrypt properties ${options.properties.join(',')}.`,
        });
        this.#propertiesToEncrypt = (options.properties as string[]).map(prop => nameToSqlName(prop));
    }

    /**
     * The upgrade process
     */
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    protected async _execute(context: Context, options: UpgradeActionExecuteOptions): Promise<void> {
        // eslint-disable-next-line no-constant-condition
        if (!0) {
            // This action does not work : see https://jira.sage.com/browse/XT-23690
            throw new Error('Not implemented yet');
        }
        const factory = context.application.getFactoryByConstructor(this.options.node());

        loggers.upgrade.info(
            `Table ${factory.tableName} properties to be encrypted: '${this.#propertiesToEncrypt.join(',')}'`,
        );

        const sqlContext = new SchemaSqlContext(context.application);

        const fullTableName = `${context.schemaName}.${factory.tableName}`;

        const tableIdx = [SystemProperties.idColumn(factory).columnName];
        if (!factory.isSharedByAllTenants) tableIdx.push(SystemProperties.tenantIdColumn(factory).columnName);

        const tableIdxParams = tableIdx.map((property, index) => `${property} = $${index + 1}`).join(' AND ');

        const propertiesToEncryptParams = this.#propertiesToEncrypt
            .map((property, index) => `${property} = $${index + tableIdx.length + 1}`)
            .join(',');

        const sqlSelectRows = `SELECT ${[...tableIdx, ...this.#propertiesToEncrypt].join(',')} from ${fullTableName}`;
        loggers.upgrade.info(`${sqlSelectRows} ---- sqlSelectRows`);

        const rows = await sqlContext.executeSqlStatement<AnyRecord[]>({ sql: sqlSelectRows });

        await asyncArray(rows).forEach(async (row: any) => {
            const sqlUpdate = `UPDATE ${fullTableName} SET ${propertiesToEncryptParams} WHERE ${tableIdxParams};`;
            const args = [
                ...tableIdx.map(property => row[property]),
                ...this.#propertiesToEncrypt.map(property => `${context.vault.encrypt(row[property])}`),
            ];
            await sqlContext.executeSqlStatement({
                sql: sqlUpdate,
                args,
            });
        });

        loggers.upgrade.info(
            `Table ${factory.tableName} encrypted properties (${this.#propertiesToEncrypt.join(
                ',',
            )}) have been updated.`,
        );
    }
}
