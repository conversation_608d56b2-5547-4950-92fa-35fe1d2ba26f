/* eslint-disable class-methods-use-this */
import { asyncArray, Context, CoreHooks, makeName63, Node, NodeFactory, SchemaSqlContext } from '@sage/xtrem-core';
import { Dict, nameToSqlName } from '@sage/xtrem-shared';
import { loggers } from '../../loggers';
import { SchemaUpgradeAction } from './schema-upgrade-action';
import { UpgradeActionExecuteOptions, UpgradeActionOptions } from './upgrade-action';

interface SchemaRenameNodeActionOptions<This extends Node> extends UpgradeActionOptions {
    node: () => { new (): This };
    oldNodeName: string;
}

/**
 * Action to rename a node
 */
export class SchemaRenameNodeAction<This extends Node> extends SchemaUpgradeAction<This> {
    /**
     * The old name of the node
     */
    readonly oldNodeName: string;

    /**
     * The old name of the table
     */
    readonly oldTableName: string;

    /**
     * The new name of the table
     */
    readonly newTableName: string;

    /**
     * The new name of the node
     */
    readonly newNodeName: string;

    /**
     *
     * @param node The node
     * @param oldNodeName The old name of the node
     */
    constructor(options: SchemaRenameNodeActionOptions<This>) {
        super({ ...options, description: '' });
        this.oldNodeName = options.oldNodeName;
        this.newNodeName = (options.node() as any).name;
        this._description = `${this.newNodeName}: rename node '${this.oldNodeName}' to '${this.newNodeName}'`;
        this.oldTableName = nameToSqlName(this.oldNodeName);
        this.newTableName = nameToSqlName(this.newNodeName);
    }

    /**
     * Pre-execute the action (invoked before any schema upgrade)
     */
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    protected override async _preExecute(context: Context): Promise<void> {
        /* stub */
    }

    /**
     *  update the _constructor of abstract nodes when concrete node is renamed
     */
    protected async _updateConstructor(
        context: Context,
        factory: NodeFactory,
        schemaSqlContext: SchemaSqlContext,
    ): Promise<void> {
        if (factory.isAbstract || factory.baseFactory == null) return;

        const updateConstructors: string[] = [];
        const updateBaseFactory = (bFactory: NodeFactory) => {
            if (bFactory.baseFactory) {
                updateBaseFactory(bFactory.baseFactory);
            }
            const concreteNodeName = factory.name;
            const baseTable = bFactory.tableName;
            updateConstructors.push(`UPDATE ${context.schemaName}.${baseTable}
                 SET _constructor = '${concreteNodeName}'
                 WHERE _constructor = '${this.oldNodeName}'`);
            loggers.upgrade.info(`\t- update _constructor on table ${baseTable} with ${concreteNodeName}.`);
        };
        updateBaseFactory(factory.baseFactory);
        const updateSQL = updateConstructors.join(';');
        loggers.upgrade.debug(() => `Execute SQL command to update _constructor: ${updateSQL}`);
        await schemaSqlContext.executeSqlStatement({ sql: updateSQL });
    }

    /**
     * The upgrade process
     */
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    protected async _execute(context: Context, options: UpgradeActionExecuteOptions): Promise<void> {
        const factory = this.getFactory(context.application)!;
        const tableName = factory.table.name;

        if (!tableName) {
            throw new Error('This upgrade operation can be executed only inside a factory context.');
        }
        // the *_fk_set_null triggers contain a call with the table name, so we need to recreate it
        let sql = `ALTER TABLE IF EXISTS ${context.schemaName}.${this.oldTableName} RENAME TO ${tableName};`;
        const schemaSqlContext = new SchemaSqlContext(context.application);

        loggers.upgrade.debug(() => `Execute SQL command: ${sql}`);
        await schemaSqlContext.executeSqlStatement({ sql });

        // we need to drop all foreign key contraints as they have the incorrect names,they will be
        // recreated with the correct names
        loggers.upgrade.info(`\t- dropping foreign keys on table ${tableName}.`);
        await factory.table.dropAllForeignKeys(context);
        // We need to refill the foreign keys to ensure that the foreign key names are correct, for later when we recreate them
        factory.table.foreignKeys = factory.getForeignKeys();

        // The primary key constraint will still have the name of the old table in its name, so we have to find it and alter the name

        const primaryKeyName = makeName63(`${tableName}_PK`);

        sql = `SELECT table_name, constraint_name
                FROM information_schema.table_constraints tc
                WHERE constraint_type = 'PRIMARY KEY'
                AND constraint_name <> $1
                AND table_name = $2
                AND table_schema = $3;`;

        let rows: Dict<any>[] = [];
        loggers.upgrade.debug(() => `Execute SQL command: ${sql}`);
        rows = await schemaSqlContext.executeSqlStatement({
            sql,
            args: [primaryKeyName, tableName, context.schemaName],
        });

        await asyncArray(rows).forEach(async (row: any) => {
            // When renaming a primary key, renaming the primary keys index will rename the constraint as well
            // See: https://www.postgresql.org/docs/11/sql-alterindex.html
            sql = `DO $$
                    BEGIN
                        ALTER INDEX ${context.schemaName}."${row.constraint_name}" RENAME TO "${primaryKeyName}";
                    EXCEPTION
                        WHEN OTHERS THEN
                            ALTER INDEX ${context.schemaName}."${String(
                                row.constraint_name,
                            ).toLowerCase()}" RENAME TO "${primaryKeyName}";
                    END $$;
                    `;
            loggers.upgrade.debug(() => `Execute SQL command: ${sql}`);
            loggers.upgrade.info(
                `\t- renaming primary key ${row.constraint_name} on table ${tableName} to ${primaryKeyName}.`,
            );

            await schemaSqlContext.executeSqlStatement({ sql });
        });

        // update _contructor of abstract nodes when concrete node is renamed
        await this._updateConstructor(context, factory, schemaSqlContext);

        // Drop all the indexes of the table, their names are incorrect
        // Simply renaming the indexes would not be enough as their definition may no longer be valid
        // if the index was updated AND the table was renamed.
        // They will be recreated and the end of the upgrade process (in the postUpgrade)
        sql = `SELECT c.indexname FROM
                    pg_indexes c
                WHERE
                    c.schemaname = $1
                    AND c.tablename = $2
                    AND c.indexname NOT LIKE '%_PK'
                ORDER BY
                    c.tablename,
                    c.indexname;`;

        loggers.upgrade.debug(() => `Execute SQL command: ${sql}`);
        rows = await schemaSqlContext.executeSqlStatement({ sql, args: [context.schemaName, tableName] });

        await asyncArray(rows).forEach(async row => {
            sql = `DROP INDEX IF EXISTS ${context.schemaName}."${row.indexname}";`;
            loggers.upgrade.info(`\t- drop index ${row.indexname} on table ${tableName}.`);
            loggers.upgrade.debug(() => `Execute SQL command: ${sql}`);
            await schemaSqlContext.executeSqlStatement({ sql });
        });

        // Rename the sequence for the _id
        await schemaSqlContext.executeSqlStatement({
            sql: `ALTER SEQUENCE IF EXISTS ${context.schemaName}.${this.oldTableName}__id_seq RENAME TO ${this.newTableName}__id_seq;`,
        });

        // Update all the columns that could use the old table for a sequence
        rows = await schemaSqlContext.executeSqlStatement({
            sql: `SELECT c.table_name, c.column_name, c.column_default
            FROM information_schema.columns c
            WHERE c.table_schema = '${context.schemaName}' AND c.column_default LIKE '%''${context.schemaName}.${this.oldTableName}''%'`,
        });

        sql = rows
            .map(row => {
                const newDefaultValue = (row.column_default as string).replace(this.oldTableName, this.newTableName);
                loggers.upgrade.info(
                    `\t- update the default value of ${row.table_name}.${row.column_name} to ${newDefaultValue}`,
                );
                return `ALTER TABLE ${context.schemaName}.${row.table_name} ALTER COLUMN "${row.column_name}" SET DEFAULT ${newDefaultValue}`;
            })
            .join(';');
        if (sql.length > 0) {
            await schemaSqlContext.executeSqlStatement({ sql });
        }

        if (context.application.tryToGetFactoryByName('AttachmentAssociation')) {
            // The attachments are enabled on the application, we have to bind all the associations to the new table
            await schemaSqlContext.executeSqlStatement({
                sql: `UPDATE ${context.schemaName}.attachment_association SET source_node_name=$1 WHERE source_node_name=$2`,
                args: [this.newNodeName, this.oldNodeName],
            });
        }

        await this._fixReferencesToMetaNodeFactory(schemaSqlContext);
    }

    /**
     * Fix the references to the MetaNodeFactory. Will update the references to the old node to the new node
     */
    private async _fixReferencesToMetaNodeFactory(schemaSqlContext: SchemaSqlContext): Promise<void> {
        const metaNodeFactoryConstructor = CoreHooks.metadataManager.getMetaNodeFactoryConstructor();
        if (metaNodeFactoryConstructor == null) {
            throw new Error('metadataManager not initialized');
        }
        const metaNodeFactory = schemaSqlContext.application.getFactoryByConstructor(metaNodeFactoryConstructor);
        // get the list of references to the MetaNodeFactory
        const allSql = metaNodeFactory.referringProperties
            .map(referringProperty => {
                const property = referringProperty.property;
                if (!property.isStored) return null;
                if (property.isInherited) return null;
                if (property.definingPackage.name === '@sage/xtrem-metadata') {
                    // We must not update a MetaNodeOperation, MetaNodeProperty, ...
                    return null;
                }

                const targetFactory = referringProperty.targetFactory;
                if (targetFactory.storage !== 'sql') return null;
                return `UPDATE ${schemaSqlContext.schemaName}.${targetFactory.requiredTableName} table_to_fix \
SET ${property.requiredColumnName} = new_factory._id \
FROM (SELECT _id FROM ${schemaSqlContext.schemaName}.meta_node_factory WHERE name='${this.newNodeName}') new_factory \
WHERE table_to_fix.${property.requiredColumnName} = (SELECT _id FROM ${schemaSqlContext.schemaName}.meta_node_factory WHERE name='${this.oldNodeName}' AND is_active=false);`;
            })
            .filter(s => s != null);

        await schemaSqlContext.executeSqlStatement({ sql: allSql.join('\n') });
    }
}
