/* eslint-disable class-methods-use-this */
import { Context, Node } from '@sage/xtrem-core';
import { SchemaUpgradeAction } from './schema-upgrade-action';
import { UpgradeActionExecuteOptions, UpgradeActionOptions } from './upgrade-action';

interface SchemaAllowShorterStringActionOptions<This extends Node> extends UpgradeActionOptions {
    node: () => { new (): This };
    propertyName: Exclude<keyof This, '$' | '_id' | '_customData' | '_etag' | '_sortValue' | '_sourceId' | '_vendor'>;
    allowedShorterLength: number;
}

/**
 * This action does not do anything.
 * It is only a marker used to allow a string property to be upgraded to a shorter string.
 *
 * WARNING : shrinking an existing column IS VERY DANGEROUS. It could fail if some data are too
 * long in the table.
 * It's really difficult to be 100% sure that reducing a string column will pass on customers' databases.
 * The best is to register another action (CustomSqlAction, ...) to truncate properly existing data before
 * the column is upgraded to its lower length.
 *
 * @example
 *       export const allowShorterString = new SchemaAllowShorterStringAction({
 *           node: () => UpgradeAlterColumns,
 *           propertyName: 'stringToShorterString',
 *           allowedShorterLength: 7,
 *       });
 *
 */
export class SchemaAllowShorterStringAction<This extends Node> extends SchemaUpgradeAction<This> {
    /**
     *
     * @param propertyName The name of the property that can be upgraded to a shorter length
     * @param allowedShorterLength The new allowed length
     */
    constructor(readonly options: SchemaAllowShorterStringActionOptions<This>) {
        super({
            ...options,
            description: `Allow property ${options.node().name}.${String(options.propertyName)} to be shrinked to ${
                options.allowedShorterLength
            } characters`,
        });
    }

    /**
     * The upgrade process
     */
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    protected async _execute(_context: Context, _options: UpgradeActionExecuteOptions): Promise<void> {
        // Nothing to do
    }
}
