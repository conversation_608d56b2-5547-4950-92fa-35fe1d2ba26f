/* istanbul ignore file */
import { AsyncResponse, Context, EnumDataType, EnumSqlContext } from '@sage/xtrem-core';
import { snakeCase } from 'lodash';
import { loggers } from '../../loggers';
import { SchemaUpgradeAction } from './schema-upgrade-action';
import { UpgradeActionExecuteOptions, UpgradeActionOptions } from './upgrade-action';

export interface EnumRenameOptions extends UpgradeActionOptions {
    description: string;
    dataType: EnumDataType<any>;
    oldEnumName: string;
}

export class SchemaEnumRenameAction extends SchemaUpgradeAction<undefined> {
    /**
     * The enum's datatype
     */
    readonly dataType: EnumDataType;

    /**
     * The old name of the enum
     */
    readonly oldEnumName: string;

    /**
     * @param options
     */
    constructor(options: EnumRenameOptions) {
        super({ ...options, node: undefined });
        this.dataType = options.dataType;
        this.oldEnumName = options.oldEnumName;
    }

    /**
     * Pre-execute the action (invoked before any schema upgrade)
     */
    protected override async _preExecute(context: Context): Promise<void> {
        // We have to rename the enum type in the pre-execute so that any new column that would refer to the newEnum can be created
        const newEnumName = `${snakeCase(this.dataType.enumName())}_enum`;
        loggers.upgrade.info(() => `Rename enum type from ${this.oldEnumName} to ${this.dataType.name}`);
        await new EnumSqlContext(context.application).renameEnumType(this.oldEnumName, newEnumName);
    }

    /**
     * The upgrade process
     */
    // eslint-disable-next-line @typescript-eslint/no-unused-vars, class-methods-use-this
    protected _execute(_context: Context, _options: UpgradeActionExecuteOptions): AsyncResponse<void> {
        // Nothing to do, everything was done in the pre-execute
    }
}
