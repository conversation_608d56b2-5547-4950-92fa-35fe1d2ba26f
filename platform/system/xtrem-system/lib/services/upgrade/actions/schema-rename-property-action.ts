import { Context, Node, SchemaSqlContext } from '@sage/xtrem-core';
import { nameToSqlName } from '@sage/xtrem-shared';
import { loggers } from '../../loggers';
import { SchemaUpgradeAction } from './schema-upgrade-action';
import { UpgradeActionExecuteOptions, UpgradeActionOptions } from './upgrade-action';

interface SchemaRenamePropertyActionOptions<This extends Node> extends UpgradeActionOptions {
    node: () => { new (): This };
    oldPropertyName: string;
    newPropertyName: string;
}

/**
 * An action to rename a property when creating upgradeSuites
 */
export class SchemaRenamePropertyAction<This extends Node> extends SchemaUpgradeAction<This> {
    /**
     * The old name of the property
     */
    readonly oldPropertyName: string;

    /**
     * The new name of the property
     */
    readonly newPropertyName: string;

    /**
     * The old name of the column
     */
    readonly oldColumnName: string;

    /**
     * The new name of the column
     */
    readonly newColumnName: string;

    /**
     *
     * @param node The node
     * @param oldPropertyName The old name of the property
     * @param newPropertyName The new name of the property
     */
    constructor(options: SchemaRenamePropertyActionOptions<This>) {
        super({
            ...options,
            description: `${options.node().name}: rename property '${options.oldPropertyName}' to '${
                options.newPropertyName
            }'`,
        });
        this.oldPropertyName = options.oldPropertyName;
        this.newPropertyName = options.newPropertyName;
        this.oldColumnName = nameToSqlName(this.oldPropertyName);
        this.newColumnName = nameToSqlName(this.newPropertyName);
    }

    /**
     * The upgrade process
     */
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    protected async _execute(context: Context, options: UpgradeActionExecuteOptions): Promise<void> {
        // Rename the column
        const tableName = this.getFactory(context.application)?.tableName;
        let sql = `ALTER TABLE ${context.schemaName}.${tableName} RENAME 
            ${this.oldColumnName} TO ${this.newColumnName}`;
        loggers.upgrade.debug(() => `Execute SQL command: ${sql}`);
        await new SchemaSqlContext(context.application).executeSqlStatement({ sql });

        const newProp = this.getFactory(context.application)?.propertiesByName[this.newPropertyName];
        if (newProp?.isReferenceProperty()) {
            // The table should have a FK for this property, try to rename it (ignore error if the FK does not exist)
            //
            sql = `DO
            $$
            BEGIN
                ALTER TABLE ${context.schemaName}.${tableName} RENAME CONSTRAINT "${tableName}_${this.oldColumnName}_fk" TO "${tableName}_${this.newColumnName}_fk";
            exception 
                   when undefined_object then
            end;
            $$`;
            await new SchemaSqlContext(context.application).executeSqlStatement({ sql });
        }
    }
}
