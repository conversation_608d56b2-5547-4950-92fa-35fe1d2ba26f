import { Application, asyncArray, Context, Node, NodeFactory, StaticThis } from '@sage/xtrem-core';
import { TenantService } from '@sage/xtrem-data-management';
import * as lodash from 'lodash';
import { loggers } from '../../loggers';
import { CommandsRecorder } from '../upgrade-commands-recorder';
import { UpgradeSqlSchema } from '../upgrade-sql-schema';
import { DataUpgradeAction } from './data-upgrade-action';
import { UpgradeActionExecuteOptions, UpgradeActionOptions } from './upgrade-action';

type NodeConstructors = (() => {
    new (): Node;
})[];

interface ReloadSetupCsvActionOptions extends UpgradeActionOptions {
    nodes: NodeConstructors;
}

/**
 * DO NOT USE : INTERNAL USE ONLY !!
 *
 * Node : this class should be declared as internal, unfortunately, the upgrade unit-tests are using it
 *
 * This DATA action can be used to reload a SETUP CSV file
 */
export class ReloadSetupCsvAction extends DataUpgradeAction<Node> {
    readonly #nodes: NodeConstructors;

    /**
     * The (optional) name of the cluster
     */
    private _clusterName = '';

    /**
     * Creates a new action to reload setup data for provided nodes from CSV files.
     * This action need to be registered in an upgrade suite:
     * export const upgradeSuite = new UpgradeSuite({
     *   actions: [new ReloadCsvAction([() => MailTemplate, () => ReportTemplate])],
     * });
     *
     * @param nodes The list of nodes to be reloaded
     */
    constructor(readonly options: ReloadSetupCsvActionOptions) {
        super({
            ...options,
            node: undefined,
            description: `Reload setup layer for factories ${options.nodes.map(n => n().name)}`,
        });
        this.#nodes = options.nodes;
    }

    private static async _reloadSetupData(
        context: Context,
        tenantId: string | null,
        factories: NodeFactory[],
        options: {
            /**
             * Should we always reload CSV files (i.e. skip the check on checksum) ?
             * This flag will mainly be set when recording SQL files
             */
            forceReloadOfCsv?: boolean;
            /**
             * Is the action running while we are recording SQL files ?
             */
            forRecording: boolean;
        },
    ): Promise<void> {
        const sqlRecorder = context.sqlPool.sqlRecorder as CommandsRecorder | undefined;
        if (options.forRecording && sqlRecorder == null) {
            throw new Error('No active recorder');
        }
        if (!options.forRecording && sqlRecorder != null) {
            throw new Error('There should not be any active recorder');
        }

        const body = async (): Promise<string[]> => {
            const factoriesWithData: string[] = [];
            const setupDataSets = await TenantService.reloadSetupData(
                context.application,
                tenantId,
                factories,
                options,
            );
            // When recording, we will have to save the data into the SQL file
            if (sqlRecorder) {
                await asyncArray(setupDataSets).forEach(dataSet => {
                    if (dataSet.data.length === 0) {
                        loggers.upgrade.warn(`\t- skipped factory ${dataSet.factory.name}: empty csv file`);
                        return;
                    }
                    factoriesWithData.push(dataSet.factory.name);
                    sqlRecorder.registerDataSet(
                        dataSet.factory,
                        dataSet.data.map(record => lodash.omit(record, ['_id', '$layer', '_tenant_id'])),
                    );
                });
            }
            return factoriesWithData;
        };

        if (sqlRecorder) {
            const factoriesWithData = await sqlRecorder.withoutRecording(body);
            factoriesWithData.forEach(factoryName => {
                sqlRecorder.recordAction('reload_setup_data', { factory: factoryName });
            });
            return;
        }
        await body();
    }

    /**
     * INTERNAL USE ONLY !!!!!!
     * Set the (optional) name of the cluster.
     * @internal
     */
    setClusterName(clusterName?: string): void {
        this._clusterName = clusterName || '';
    }

    /** @internal */
    static async reloadCsv(
        application: Application,
        factories: NodeFactory[],
        options: UpgradeActionExecuteOptions & {
            /**
             * The (optional) name of the cluster
             */
            clusterName?: string;
        },
    ): Promise<void> {
        const clusterName = options.clusterName ?? '';

        const tenantIdsToSkip =
            clusterName.length === 0
                ? []
                : await TenantService.getTenantIdsToSkipForSetupData(application, clusterName);

        if (tenantIdsToSkip.length > 0) {
            loggers.upgrade.warn(
                `${tenantIdsToSkip.length} tenants will be skipped (based on the cluster name '${clusterName}')`,
            );
        }

        const sharedFactories = factories.filter(f => f.isSharedByAllTenants);
        const nonSharedFactories = factories.filter(f => !f.isSharedByAllTenants);

        if (sharedFactories.length > 0) {
            loggers.upgrade.info(
                `Reload setup layer for ${sharedFactories.length} shared factories ${sharedFactories.map(
                    f => f.name,
                )} - recording mode is ${options.forRecording}`,
            );

            await application.withCommittedContext(null, async context => {
                // We are using null as a tenantId as shared factories are not bound to any tenant.
                await ReloadSetupCsvAction._reloadSetupData(context, null, sharedFactories, {
                    forceReloadOfCsv: true,
                    forRecording: options.forRecording,
                });
            });
        }

        if (nonSharedFactories.length > 0) {
            loggers.upgrade.info(
                `Reload setup layer for ${nonSharedFactories.length} non-shared factories ${nonSharedFactories.map(
                    f => f.name,
                )} - recording mode is ${options.forRecording}`,
            );
            await UpgradeSqlSchema.executeActionOnAllTenants(
                application,
                {
                    forRecording: options.forRecording,
                    useReadonlyContexts: false,
                },
                async ctxOnTenant => {
                    if (tenantIdsToSkip.includes(ctxOnTenant.tenantId || '')) {
                        loggers.upgrade.warn(
                            `- skip reload setup layer for factories ${nonSharedFactories.map(f => f.name)}, tenant = ${
                                ctxOnTenant.tenantId
                            } from the cluster name ${clusterName}`,
                        );
                        return;
                    }
                    loggers.upgrade.verbose(
                        () =>
                            `- reload setup layer for factories ${nonSharedFactories.map(f => f.name)}, tenant = ${
                                ctxOnTenant.tenantId
                            }`,
                    );
                    if (!ctxOnTenant.tenantId) {
                        throw new Error('TenantId is not provided');
                    }
                    await ReloadSetupCsvAction._reloadSetupData(ctxOnTenant, ctxOnTenant.tenantId, nonSharedFactories, {
                        forceReloadOfCsv: true,
                        forRecording: options.forRecording,
                    });
                },
            );
        }
    }

    /**
     * The upgrade process
     */
    protected async _execute(context: Context, options: UpgradeActionExecuteOptions): Promise<void> {
        const factories = this.#nodes.map(n => context.application.getFactoryByConstructor(n() as StaticThis<Node>));
        await ReloadSetupCsvAction.reloadCsv(context.application, factories, {
            ...options,
            clusterName: this._clusterName,
        });
    }
}
