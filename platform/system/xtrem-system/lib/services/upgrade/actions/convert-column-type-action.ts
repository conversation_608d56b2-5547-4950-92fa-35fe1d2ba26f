import {
    ColumnInconsistencyType,
    Context,
    Node,
    parseColumnDefinition,
    Property,
    SchemaSqlContext,
    SqlConverter,
} from '@sage/xtrem-core';
import { ColumnDefinition } from '@sage/xtrem-postgres';
import { loggers } from '../../loggers';
import { SchemaUpgradeAction } from './schema-upgrade-action';
import { UpgradeActionExecuteOptions, UpgradeActionOptions } from './upgrade-action';

/**
 * The list of supported conversions.
 * This list does not include them but all the conversions A->A are also supported (no type change)
 * note: some A->A conversions must be part of this list as the may need some extra checks to be validated.
 */
export type SupportedTypeConversions =
    | 'reference->reference' // extra checks needed to validate the conversion
    | 'string->string' // extra checks needed to validate the conversion
    | 'string->textStream'
    | 'integer->reference'
    | 'integer->string'
    | 'string->reference'
    | 'string->integer'
    | 'string->encryptedString'
    | 'string->localizedString'
    | 'localizedString->string'
    | 'localizedString->localizedString' // extra checks needed to validate the conversion
    | 'encryptedString->encryptedString' // extra checks needed to validate the conversion
    | 'integer->decimal'
    | 'integerArray->referenceArray'
    | 'binary->binaryStream'
    | 'enum->enum' // extra checks needed to validate the conversion
    | 'enumArray->enumArray' // extra checks needed to validate the conversion
    | 'autoIncrement->integer'
    | 'integer->autoIncrement'
    | 'date->datetime'
    | 'datetime->date'
    | 'decimal->decimal'; // Need to check that the column definition is (28,10)

interface ConvertColumnTypeActionOptions<This extends Node> extends UpgradeActionOptions {
    node: () => { new (): This };
    oldColumn: ColumnDefinition;
    newProperty: Property;
}

/**
 * An automatic action to change the type of a column
 * @internal
 */
export class ConvertColumnTypeAction<This extends Node> extends SchemaUpgradeAction<This> {
    readonly oldColumn: ColumnDefinition;

    readonly newProperty: Property;

    private readonly _conversionType: SupportedTypeConversions;

    /**
     * Should the conversion be done during the preUpgrade ?
     * (i.e. before running the other actions).
     * Some type conversions must be done in the pre-upgrade phase (for instance when the size of a string column is increased)
     */
    readonly processDuringPreUpgrade: boolean;

    constructor(
        readonly columnInconsistency: ColumnInconsistencyType,
        options: ConvertColumnTypeActionOptions<This>,
    ) {
        super({
            ...options,
            description: `${options.node().name}: retype ${options.oldColumn.name} from ${options.oldColumn.type} to ${
                options.newProperty.isLocalized ? 'localized ' : ''
            }${options.newProperty.type}' (${columnInconsistency.options.conversionType})`,
        });
        this.oldColumn = options.oldColumn;
        this.newProperty = options.newProperty;
        this._conversionType = columnInconsistency.options.conversionType as SupportedTypeConversions;
        this._checkConversion();
        this.processDuringPreUpgrade = this._shouldBeProcessedDuringPreUpgrade();
    }

    private _shouldBeProcessedDuringPreUpgrade(): boolean {
        if (this._conversionType !== 'string->string') return false;
        if ((this.newProperty.maxLength || 0) > (this.oldColumn.maxLength || 0)) {
            // We increase the size of the column, we need to run the conversion during the preUpgrade so that SQL scripts can use the new (longer) size
            return true;
        }
        return false;
    }

    // If bases type differs (string -> integer for instance but not a string[50] -> string[100])
    // there must be a defaultValue on the property.
    // Exceptions:
    //  string->textStream: the string is not altered by the upgrade
    private _requiresDefaultValue(): boolean {
        switch (this._conversionType) {
            case 'string->textStream':
            case 'string->encryptedString':
            case 'localizedString->string':
            case 'integer->decimal':
            case 'integer->reference':
            case 'reference->reference':
            case 'string->localizedString':
            case 'integer->string':
            case 'string->integer':
            case 'integer->autoIncrement':
            case 'autoIncrement->integer':
            case 'date->datetime':
            case 'datetime->date':
            case 'decimal->decimal':
                return false;
            default:
                if (this.columnInconsistency.options.fromType === this.columnInconsistency.options.toType) {
                    return false;
                }
        }
        return true;
    }

    private _checkConversion(): void {
        switch (this._conversionType) {
            case 'string->reference':
            case 'string->integer':
            case 'string->textStream':
            case 'integer->decimal':
            case 'integer->string':
            case 'string->localizedString':
            case 'localizedString->string':
            case 'string->encryptedString':
            case 'reference->reference':
            case 'integer->reference':
            case 'autoIncrement->integer':
            case 'integer->autoIncrement':
            case 'datetime->date':
            case 'date->datetime':
            case 'decimal->decimal':
                // Add here all new supported conversions
                break;
            case 'localizedString->localizedString':
            case 'encryptedString->encryptedString':
            case 'string->string': {
                // Note: we should only allow to extend the storage size, not to reduce it
                // but when a SchemaAllowShorterStringAction action is registered, shrinking a column is allowed
                break;
            }
            default:
                throw new Error(
                    `${this.node?.().name}.${this.oldColumn.name} (${this._conversionType}): unsupported conversion`,
                );
        }

        if (this._requiresDefaultValue()) {
            // The base type differs (string -> integer for instance but not a string[50] -> string[100])
            // There must be a defaultValue on the property
            if (this.newProperty.defaultValue === undefined) {
                throw new Error(
                    `${this.node!().name}.${this.oldColumn.name} (${
                        this._conversionType
                    }): cannot change type of column - defaultValue decorator is missing.`,
                );
            }

            // and this default value must return a constant (functions are not supported for now)
            // LATER allow to use a simple (translatable to SQL) defaultValue and use it
            // as an expression when altering the column.
            if (typeof this.newProperty.defaultValue === 'function') {
                throw new Error(
                    `${this.node!().name}.${this.oldColumn.name} (${
                        this._conversionType
                    }): cannot change type - defaultValue must return a constant (functions are not supported yet)`,
                );
            }
        }
    }

    /**
     * The conversion process
     */
    protected async _processConversion(context: Context): Promise<void> {
        const factory = this.getFactory(context.application);
        if (factory == null) throw new Error(`Factory not found for ${this.node?.().name}`);
        const tableName = factory.tableName;
        if (tableName == null) throw new Error(`Could not resolve table name for factory ${factory.name}`);
        loggers.upgrade.info(`\t- execute ${this.description}`);

        const newColDef = factory.table.getColumnDefinition(this.newProperty.column!);
        const parseResult = parseColumnDefinition(context.schemaName, factory.requiredTableName, newColDef);

        let using: string | undefined;
        if (this._requiresDefaultValue()) {
            const defVal = this.newProperty.defaultValue;
            // LATER allow to use a simple (translatable to SQL) defaultValue function
            using = SqlConverter.convertDefaultValue(defVal);
        }
        if (this._conversionType === 'string->localizedString') {
            // to_json takes care of escaping chars like newlines, ...
            using = `('{"en":' || to_json(${this.newProperty.columnName}) || '}')::json`;
        }
        if (this._conversionType === 'string->integer') {
            using = `${this.newProperty.columnName}::INT8`;
        }
        if (this._conversionType === 'localizedString->string') {
            // We are using the value from 'en'
            using = `${this.newProperty.columnName}->>'en'`;
        }

        if (this._conversionType === 'integer->autoIncrement') {
            const resolvedTableName = `${context.schemaName}.${factory.tableName}`;
            const columnName = this.oldColumn.name;
            const resolvedSequenceName = `${context.schemaName}.${factory.tableName}_${columnName}_seq`;
            // We cannot update a column type to serial8/bigserial with an alter column, we need to perform the
            // sequence of actions below, postgres will resolve the column type to bigserial
            // See https://www.postgresql.org/docs/8.3/datatype-numeric.html#DATATYPE-SERIAL
            const sql = `DO $$
                DECLARE
                    max_val INT8;
                    table_owner VARCHAR(255);
                BEGIN
                    CREATE SEQUENCE IF NOT EXISTS ${resolvedSequenceName};

                    SELECT tableowner INTO table_owner FROM pg_catalog.pg_tables WHERE schemaname = '${context.schemaName}' AND tablename =  '${factory.tableName}';

                    EXECUTE 'ALTER SEQUENCE ${resolvedSequenceName} OWNER TO ' || table_owner;

                    ALTER TABLE ${resolvedTableName} ALTER COLUMN ${columnName} SET DEFAULT nextval('${resolvedSequenceName}');

                    ALTER SEQUENCE ${resolvedSequenceName} OWNED BY ${resolvedTableName}.${columnName};

                    SELECT max(${columnName}) INTO max_val from ${resolvedTableName};
                    if (max_val IS NOT NULL) then
                        EXECUTE 'ALTER SEQUENCE ${resolvedSequenceName} RESTART WITH ' || (max_val + 1);
                    end if;
                END $$;`;

            const schemaSqlContext = new SchemaSqlContext(context.application);
            await schemaSqlContext.executeSqlStatement({ sql });
            return;
        }

        let propIsReallyAutoincrement = this.newProperty.isAutoIncrement;
        if (factory.baseFactory) {
            // sub-factories do no use auto-incremented _id (their id is set from the base class)
            // Should be reviewed : prop.isAutoIncrement should return false for theses properties
            propIsReallyAutoincrement = false;
        }
        const dropDefault = this.oldColumn.comment?.isAutoIncrement && !propIsReallyAutoincrement;
        await new SchemaSqlContext(context.application).updateColumnType(
            factory.requiredTableName,
            this.oldColumn.name,
            parseResult.typeName,
            factory.table.getColumnDefinition(this.newProperty.column!).comment!,
            {
                using,
                dropDefault,
            },
        );
    }

    /**
     * The upgrade process
     */
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    protected async _execute(context: Context, options: UpgradeActionExecuteOptions): Promise<void> {
        if (this.processDuringPreUpgrade) {
            // The conversion was already done by the _preExecute function
            return;
        }
        await this._processConversion(context);
    }

    /**
     * Pre-execute the action (invoked before any schema upgrade)
     */
    protected override async _preExecute(context: Context): Promise<void> {
        if (!this.processDuringPreUpgrade) {
            // The conversion will be done by the _execute function
            return;
        }
        await this._processConversion(context);
    }
}
