import { BulkUpdateOptions, Context, Node, SqlConverter } from '@sage/xtrem-core';
import { DataUpgradeAction } from './data-upgrade-action';
import { UpgradeActionExecuteOptions, UpgradeActionOptions } from './upgrade-action';

interface DataUpdateActionOptions<This extends Node> extends BulkUpdateOptions<This>, UpgradeActionOptions {
    /**
     * The node that will be updated by the action
     *
     * Example:
     * ```
     *       node: () => DocumentLine,
     * ```
     */
    node: () => { new (): This };

    /** The description of the action */
    description: string;
}

/**
 * Upgrade action to execute 'UPDATE ... SET' commands
 */
export class DataUpdateAction<This extends Node> extends DataUpgradeAction<This> {
    constructor(readonly options: DataUpdateActionOptions<This>) {
        super(options);
    }

    /**
     * The upgrade process
     */
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    protected async _execute(context: Context, options: UpgradeActionExecuteOptions): Promise<void> {
        const { set, where } = this.options;
        const nodeConstructor = this.getFactory(context.application)?.nodeConstructor;
        if (!nodeConstructor)
            throw new Error(`dataUpdateAction(${this.description}): cannot execute: no node constructor`);
        // TODO: fix this any type
        await context.withLocalizedTextAsJson(() => context.bulkUpdate(nodeConstructor, { set, where: where as any }));
    }

    /**
     * Verifies that the action can be executed.
     * Note : inconsistencies will result in thrown errors
     */

    override verify(context: Context): void {
        let converter: SqlConverter | undefined;
        // All the members in the 'set' must be 'ts-to-sql' compliant
        Object.entries(this.options.set).forEach((memberName, memberVal) => {
            if (typeof memberVal === 'function') {
                try {
                    converter = converter || new SqlConverter(context, this.getFactory(context.application)!);
                    converter!.convertFunction(memberVal);
                } catch (ex) {
                    throw new Error(
                        `dataUpdateAction(${this.description}): member ${memberName} is too complex to be converted into a SQL statement`,
                    );
                }
            }
        });
    }
}
