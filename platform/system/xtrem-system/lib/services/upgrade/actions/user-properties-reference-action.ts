/* istanbul ignore file */
import { Context, NodeFactory } from '@sage/xtrem-core';
import { DataUpgradeAction } from './data-upgrade-action';
import { UpgradeActionExecuteOptions } from './upgrade-action';

/**
 * This action is to change _create_user and _update_user columns from integer to user reference and fix the invalid references for the given list of tables
 */
export class UserPropertiesReferenceAction extends DataUpgradeAction<undefined> {
    constructor(
        readonly options: {
            tables: string[];
        },
    ) {
        super({
            node: undefined,
            description: `Change _create_user and _update_user to reference in ${options.tables}`,
        });
    }

    /**
     * The upgrade process
     */
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    protected async _execute(context: Context, _options: UpgradeActionExecuteOptions): Promise<void> {
        if (!this._preExecute) throw new Error('');

        const tables = this.options.tables || [];
        // eslint-disable-next-line no-restricted-syntax
        for (const tableName of tables) {
            await UserPropertiesReferenceAction.fixTableUserReferences(context, tableName);
        }
    }

    /**
     * @internal
     *
     * Returns whether the current action concerns a given factory
     */
    override concernsFactory(factory: NodeFactory): boolean {
        if (!factory.tableName) return false;

        const tables = this.options.tables;
        if (tables?.includes(factory.tableName)) {
            return true;
        }

        return false;
    }

    private static async fixTableUserReferences(context: Context, tableName: string): Promise<void> {
        if (!/^"?[[a-z][a-z0-9_]*"?$/.test(tableName)) {
            throw new Error(`Invalid table name: ${tableName}`);
        }
        const { schemaName } = context;
        const sqlUpdate = (columnName: string) => `UPDATE ${schemaName}.${tableName} u1
        SET ${columnName} = (
            SELECT _id
              FROM ${schemaName}."user" u2
             WHERE u2.email = $1
               AND u1."_tenant_id" = u2."_tenant_id"
        )
        WHERE NOT EXISTS(
            SELECT 1
              FROM ${schemaName}."user" u2
             WHERE u2._id = u1.${columnName}
               AND u1."_tenant_id" = u2."_tenant_id"
        );`;

        await context.executeSql(sqlUpdate('_create_user'), ['<EMAIL>']);
        await context.executeSql(sqlUpdate('_update_user'), ['<EMAIL>']);
    }
}
