import { asyncArray, Context, Dict, EnumDataType, EnumSqlContext } from '@sage/xtrem-core';
import * as _ from 'lodash';
import { parse as parsePgArray } from 'postgres-array';
import { loggers } from '../../loggers';
import { SchemaEnumUpgradeHelper } from './schema-enum-upgrade-helper';
import { SchemaUpgradeAction } from './schema-upgrade-action';
import { UpgradeActionExecuteOptions, UpgradeActionOptions } from './upgrade-action';

interface EnumValuesToRename {
    oldValue: string;
    newValue: string;
}

interface DeleteEnumMemberAttribute {
    /**
     * The name of the enum member to delete
     */
    memberName: string;
    /**
     * The name of the enum member to use as a replacement for the deleted member
     * If null, no replacement will be done (but the upgrade will fail if the member to delete is used)
     */
    replacement: string | null;
}
interface EnumUpgradeActions extends UpgradeActionOptions {
    description: string;
    dataType: EnumDataType<any>;
    valuesMapping: Dict<string | null>;
    /**
     * The (optional) names of the members that should be dropped with their optional replacement
     */
    membersToDelete?: Dict<string | null>;
}

export class SchemaEnumUpgradeAction extends SchemaUpgradeAction<undefined> {
    /**
     * Enum datatype
     */
    dataType: EnumDataType;

    /**
     * attributes to rename
     */
    private _attributesToRename: EnumValuesToRename[];

    /**
     * attributes to delete
     */
    private readonly _membersToDelete: DeleteEnumMemberAttribute[];

    /**
     *
     * @param options
     */
    constructor(readonly options: EnumUpgradeActions) {
        super({ ...options, node: undefined });
        this.dataType = options.dataType;
        this._membersToDelete =
            options.membersToDelete == null
                ? []
                : Object.entries(options.membersToDelete).map(([memberName, replacement]) => {
                      return {
                          memberName,
                          replacement,
                      };
                  });
    }

    private async _parseValuesMapping(context: Context): Promise<void> {
        const attrsToRename: EnumValuesToRename[] = [];
        const currentEnumAttributes = parsePgArray(
            await new EnumSqlContext(context.application).getEnumAttributes(this.typeName),
            v => v,
        );

        Object.entries(this.options.valuesMapping).forEach(([key, value]) => {
            if (value == null) return;
            if (!currentEnumAttributes.includes(value)) {
                // This member is missing, it has to be added
                attrsToRename.push({ oldValue: key, newValue: value });
            }
        });
        this._attributesToRename = _.uniqBy(attrsToRename, 'newValue');
    }

    /**
     * The upgrade process
     */
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    protected async _execute(context: Context, options: UpgradeActionExecuteOptions): Promise<void> {
        await this.renameAttribute(context);
    }

    get enumAttributes(): string[] {
        return Object.keys(this.dataType.enum).filter(value => Number.isNaN(Number(value)));
    }

    get typeName(): string {
        return SchemaEnumUpgradeHelper.getTypeName(this.dataType.enumName());
    }

    /**
     * rename an enum attribute
     * @param context
     * @private
     */
    private async renameAttribute(context: Context): Promise<void> {
        await this._parseValuesMapping(context);
        if (this._attributesToRename.length === 0) return;
        loggers.upgrade.verbose(
            () =>
                `Rename attributes ${this._attributesToRename.map(val => val.oldValue).join(',')} from enum type ${
                    this.typeName
                } to ${this._attributesToRename.map(val => val.newValue).join(',')} respectively.`,
        );
        const sqlContext = new EnumSqlContext(context.application);
        await asyncArray(this._attributesToRename).forEach(attributeToRename =>
            sqlContext.renameEnumAttributes(this.typeName, attributeToRename),
        );
    }

    /**
     * delete attributes from an enum
     * @param context
     * @private
     */
    private async _deleteAttributes(context: Context): Promise<void> {
        if (this._membersToDelete.length === 0) return;
        loggers.upgrade.verbose(() => {
            const membersAsString = this._membersToDelete.map(m => `${m.memberName} -> ${m.replacement}`).join(', ');
            return `Delete members from ${this.typeName} (${membersAsString})`;
        });
        await new EnumSqlContext(context.application).dropEnumAttributes(this.typeName, this._membersToDelete);
    }

    /**
     * clean up database from unused enum types
     * @param context
     * @private
     */
    // eslint-disable-next-line class-methods-use-this
    private async _cleanUpSchemaEnumTypes(context: Context): Promise<void> {
        const sqlContext = new EnumSqlContext(context.application);
        const enumTypesList = (await sqlContext.listAllEnumTypes()).map(value => value.enumTypeName);
        const enumTypesInUse = (await sqlContext.listEnumTypesUsage()).map(value => value.enumTypeName);
        await asyncArray(_.difference(enumTypesList, enumTypesInUse)).forEach(async (enumTypeName: string) => {
            loggers.upgrade.warn(() => `Drop unused enum type ${enumTypeName}.`);
            await sqlContext.dropEnum(enumTypeName);
        });
    }

    /**
     * Post-execute the action (invoked after all the schema upgrades)
     * Clean up database from unused enum types and attributes (invoked after all the schema upgrades)
     */
    protected override async _postExecute(context: Context): Promise<void> {
        await this._deleteAttributes(context);
        await this._cleanUpSchemaEnumTypes(context);
    }
}
