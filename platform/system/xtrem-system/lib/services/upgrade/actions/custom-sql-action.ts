/* eslint-disable class-methods-use-this */
import { Context, Node, NodeFactory, StaticThis, SystemProperties } from '@sage/xtrem-core';
import { AnyValue } from '@sage/xtrem-shared';
import { DataUpgradeAction } from './data-upgrade-action';
import { UpgradeActionExecuteOptions, UpgradeActionOptions } from './upgrade-action';

export type CustomSqlActionBody = (helper: CustomSqlActionHelper) => Promise<void>;

export interface CustomSqlActionHelper {
    /* the schema to use when building the SQL statements */
    schemaName: string;
    /**
     * The function to use to execute a sql commands
     */
    executeSql<T extends AnyValue = AnyValue[]>(sql: string, args?: any[]): Promise<T>;
    /**
     * Returns the list of system columns(comma separated): _tenantId, _id, _updateUser, ... for a given node.
     */
    getSystemColumnNames(node: StaticThis<Node>): string;
}

export interface CustomSqlActionParameter extends UpgradeActionOptions {
    description: string;

    /**
     * Override the default 2000ms max allowed duration where and exception is thrown if a query exceeds this
     * You can also set this to 0 to bypass the duration check for queries that are expected to run for a long time
     */
    maxAllowedDurationMs?: number;

    /**
     * Describes the fixes expected from this action
     */
    fixes?: {
        /**
         * The list of tables updated by the action
         */
        tables?: string[];
        /**
         * The nullable columns fixed by the action
         */
        notNullableColumns?: {
            table: string;
            column: string;
        }[];
        /**
         * The list of tables for which the script will manage the attachments
         */
        attachments?: {
            table: string;
        }[];
    };

    /**
     * Main step of the action.
     * Inside this function, please use _options.helpers.executeSql_ to execute SQL statements.
     * @param options.schemaName the schema to use when building the SQL statements
     * @param options.helpers set of functions
     */
    body: CustomSqlActionBody;
}

/**
 * Action to execute custom SQL statements to manipulate data only.
 * The body of this action will be executed with a classic pool (not a sys pool) so this action
 * cannot be used to run DDL queries.
 */
export class CustomSqlAction extends DataUpgradeAction<undefined> {
    /**
     * The max allowed duration (in ms) for a query
     */
    #maxAllowedDurationMs = 300_000;

    constructor(readonly params: CustomSqlActionParameter) {
        super({ ...params, node: undefined });
        if (params.maxAllowedDurationMs !== undefined) this.#maxAllowedDurationMs = params.maxAllowedDurationMs;
    }

    /**
     * @internal
     *
     * Returns whether the current action concerns a given factory
     */
    override concernsFactory(factory: NodeFactory): boolean {
        // Note: SQL actions are not directly bound to a specific factory
        // but its fixes / tablesToRenameAndDrop are.
        if (!factory.tableName) return false;

        const fixes = this.params.fixes;
        if (fixes) {
            if (fixes.tables?.includes(factory.tableName)) {
                return true;
            }
            if (fixes.notNullableColumns?.some(f => f.table === factory.tableName)) {
                return true;
            }
        }

        return false;
    }

    private _getBodyHelper(context: Context): CustomSqlActionHelper {
        return {
            schemaName: context.schemaName,
            executeSql: <T extends AnyValue = AnyValue[]>(sql: string, args: any[]): Promise<T> =>
                context.executeSql<T>(sql, args),
            getSystemColumnNames: node => CustomSqlAction.getSystemColumnNames(context, node),
        };
    }

    /**
     * Returns the list of system columns(comma separated): _tenantId, _id, _updateUser, ... for a given node.
     * If the node is omitted, the main node (used in the constructor) will be used.
     */
    static getSystemColumnNames(context: Context, node: StaticThis<Node>): string {
        const factory = context.application.getFactoryByConstructor(node);
        const colNames = [
            ...SystemProperties.getSystemProperties(factory),
            SystemProperties.sourceIdProperty(factory),
            SystemProperties.idProperty(factory),
        ]
            .filter(sysProp => sysProp.isStored && !sysProp.isInherited)
            .map(sysProp => sysProp.columnName);

        if (!factory.isSharedByAllTenants) {
            colNames.push(SystemProperties.tenantIdColumn(factory).columnName);
        }
        return colNames.join(',');
    }

    /**
     * The upgrade process
     */
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    protected async _execute(context: Context, _options: UpgradeActionExecuteOptions): Promise<void> {
        if (!this._preExecute) throw new Error('');
        const dt0 = Date.now();

        await this.params.body(this._getBodyHelper(context));

        const duration = Date.now() - dt0;
        if (this.#maxAllowedDurationMs > 0 && duration > this.#maxAllowedDurationMs) {
            throw new Error(
                `The data action ${this.description} was too long to execute. Duration was ${duration} ms, max=${this.#maxAllowedDurationMs} ms`,
            );
        }
    }
}
