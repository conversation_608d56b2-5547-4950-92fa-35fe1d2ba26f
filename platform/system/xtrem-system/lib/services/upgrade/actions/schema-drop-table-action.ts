import { Context, SchemaSqlContext, readTableSchema } from '@sage/xtrem-core';
import { TableDefinition } from '@sage/xtrem-postgres';
import { pascalCase } from '@sage/xtrem-shared';
import { loggers } from '../../loggers';
import { SchemaUpgradeAction } from './schema-upgrade-action';
import { UpgradeActionExecuteOptions, UpgradeActionOptions } from './upgrade-action';

interface SchemaDropTableActionOptions extends UpgradeActionOptions {
    tableName: string;
}

/**
 * An action to drop a table
 */
export class SchemaDropTableAction extends SchemaUpgradeAction<undefined> {
    /**
     * @param tableName The name of the table to drop
     */
    constructor(readonly options: SchemaDropTableActionOptions) {
        super({ ...options, node: undefined, description: `Drop table ${options.tableName}` });
    }

    /**
     * Verifies that the action can be executed.
     * Note : inconsistencies will result in thrown errors
     */
    override verify(context: Context): void {
        // A table cannot be dropped if it maches an existing node
        const factory = context.application.tryGetFactoryByTableName(this.options.tableName);
        if (factory) {
            throw new Error(
                `The table ${this.options.tableName} cannot be dropped as it's bound to the factory ${factory.fullName}`,
            );
        }
    }

    /**
     * The upgrade process
     */
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    protected async _execute(context: Context, options: UpgradeActionExecuteOptions): Promise<void> {
        const sqlContext = new SchemaSqlContext(context.application);
        let tableDefinition: TableDefinition | undefined;
        try {
            // Retrieve the comment of the table to drop to check if it had a base table
            tableDefinition = await readTableSchema(context, this.options.tableName, {
                getComments: true,
                skipColumns: true,
                skipForeignKeys: true,
                skipIndexes: true,
                skipSequences: true,
                skipSecurity: true,
            });
        } catch (error) {
            // This could happen when the table does not exist
            loggers.upgrade.info(
                () =>
                    `Note: the table '${this.options.tableName}' does not exist in the database but the SQL command to drop it will still be recorded.`,
            );
        }

        if (tableDefinition) {
            const comment = tableDefinition.comment;
            if (comment && comment.rootTable != null) {
                await sqlContext.executeSqlStatement({
                    sql: `DELETE FROM ${context.schemaName}.${comment.rootTable} WHERE _constructor = $1`,
                    args: [pascalCase(this.options.tableName)],
                });
            }
        }

        const sql = `DROP TABLE IF EXISTS ${context.schemaName}.${this.options.tableName}`;
        loggers.upgrade.debug(() => `Execute SQL command: ${sql}`);
        await sqlContext.executeSqlStatement({ sql });
    }
}
