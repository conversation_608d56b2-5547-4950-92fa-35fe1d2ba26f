import { Context, CoreHooks, Dict, Node } from '@sage/xtrem-core';
import { CommandsRecorder } from '../upgrade-commands-recorder';
import { DataUpgradeAction } from './data-upgrade-action';
import { UpgradeActionExecuteOptions } from './upgrade-action';

/**
 * An action to force the upgrade of metadata (operations, factories, properties, activities and activity permissions)
 */
export class UpgradeMetadataAction extends DataUpgradeAction<Node> {
    constructor(readonly fullReload?: boolean) {
        super({
            node: undefined,
            description: 'Upgrade metadata (operations, factories, properties)',
        });
    }

    /**
     * The upgrade process
     */

    protected async _execute(context: Context, options: UpgradeActionExecuteOptions): Promise<void> {
        const sqlRecorder = context.sqlPool.sqlRecorder as CommandsRecorder | undefined;
        if (options.forRecording && sqlRecorder == null) {
            throw new Error('No active recorder');
        }
        if (!options.forRecording && sqlRecorder != null) {
            throw new Error('There should not be any active recorder');
        }

        if (sqlRecorder) {
            // We are recording a new SQL file, we have to register the action
            // The will be executed when replaying this file
            const args = {} as Dict<string>;
            if (this.fullReload) args.fullReload = 'true';
            sqlRecorder.recordAction('metadata_upgrade', args);
        } else if (this.fullReload)
            // We are testing and there is an upgrade action that wants to reload all the metadata.
            await CoreHooks.metadataManager.upgradeMetadata(context.application, { fullReload: this.fullReload });
    }
}
