import { Context, Dict, EnumSqlContext, Node, readTableSchema } from '@sage/xtrem-core';
import { SchemaEnumUpgradeHelper } from './schema-enum-upgrade-helper';
import { SchemaUpgradeAction } from './schema-upgrade-action';
import { UpgradeActionExecuteOptions, UpgradeActionOptions } from './upgrade-action';

/**
 * description -
 * node -
 * propertyName -
 * valuesMapping -
 *
 */
interface EnumPropertyDatatypeUpgradeActions<This extends Node> extends UpgradeActionOptions {
    /**
     * Description of the action
     */
    description: string;
    /**
     * Node that the enum property belongs to.
     */
    node: () => { new (): This };
    /**
     * Name of the enum property
     */
    propertyName: keyof This;
    /**
     * An object that provides the mapping from the old enum datatype values to new enum datatype values of the property. All old enum values are required as a key in the object,
     * if any are not provided, an error is thrown.
     * The new value, has to be a value from the new enum datatype or null, if an invalid value is set, an error is thrown.
     * Example:
     * {
     *  oldEnumValue1: 'newEnumValue1',
     *  oldEnumValue2: 'newEnumValue2',
     *  oldEnumValue3: 'newEnumValue1',
     * }
     */
    valuesMapping: Dict<string | null>;
}

/**

 */
export class SchemaEnumPropertyDatatypeUpgradeAction<This extends Node> extends SchemaUpgradeAction<This> {
    /**
     * propertyName
     */
    readonly propertyName: keyof This;

    /**
     * valuesMapping
     */
    readonly valuesMapping: Dict<string | null>;

    /**
     *
     * This schema action will be required, if the datatype on an enum property has changed.
     *
     * Enum property with old datatype:
     * ```ts
     * @decorators.enumProperty<MyNode, 'myEnum'>({
     *       isStored: true,
     *       isPublished: true,
     *       isRequired: true,
     *       dataType: () => oldEnumDatatype,
     *   })
     *   documentType: oldEnum;
     * ```
     *
     * Enum property with new datatype:
     * ```ts
     * @decorators.enumProperty<MyNode, 'myEnum'>({
     *       isStored: true,
     *       isPublished: true,
     *       isRequired: true,
     *       dataType: () => newEnumDatatype,
     *   })
     *   documentType: newEnum;
     * ```
     *
     * Example:
     * ```ts
     * export const updateMyEnumDatatype = new SchemaEnumPropertyDatatypeUpgradeAction({
     *                                               description: 'Upgrade enum property myEnum of node MyNode',
     *                                               node: () => MyNode,
     *                                               propertyName: 'myEnum',
     *                                               valuesMapping: {
     *                                                   oldEnumValue1: 'newEnumValue1',
     *                                                   oldEnumValue2: 'newEnumValue2',
     *                                                   oldEnumValue3: 'newEnumValue1',
     *                                               },
     *                                           });
     * ```
     * @param options
     */
    constructor(options: EnumPropertyDatatypeUpgradeActions<This>) {
        super(options);

        this.propertyName = options.propertyName;

        this.valuesMapping = options.valuesMapping;
    }

    /**
     * The upgrade process
     *
     * When executing this schema action the following steps are done
     * - Verify the keys and values of the valuesMapping provided
     * - Alter the enum property column, changing the type to the property enum datatype. In the using clause of the alter column statement, we set the current value of the column based on the
     * values in valuesMapping.
     */
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    protected async _execute(context: Context, options: UpgradeActionExecuteOptions): Promise<void> {
        const factory = this.getFactory(context.application)!;
        const property = factory.findProperty(this.propertyName as string);
        const enumDataType =
            property.isEnumProperty() || property.isEnumArrayProperty() ? property.dataType : undefined;
        if (!enumDataType)
            throw new Error(
                `${factory.name}.${property.name} (${this.description}): property needs to be an enum or enumArray.`,
            );
        const enumName = enumDataType.enumName();
        const sqlContext = new EnumSqlContext(context.application);

        // We need to the read the table schema definition here, before we convert the column type to VARCHAR
        const tableSchema = await readTableSchema(context, factory.table.name, {
            skipSecurity: true,
            skipSequences: true,
        });

        const colDef = tableSchema.columns!.find(col => property.columnName === col.name);

        const currentEnumAttributes = await SchemaEnumUpgradeHelper.getEnumAttributes(
            sqlContext,
            colDef!.enumDataType!.name,
        );

        const valueMappingKeys = Object.keys(this.valuesMapping);

        const missingMappingValues = currentEnumAttributes.filter(attribute => !valueMappingKeys.includes(attribute));

        // Verify all values of the old enum is catered for in the keys value mapping
        if (missingMappingValues.length > 0) {
            throw new Error(
                `${factory.name}.${property.name} (${this.description}): enum value mapping provided has missing attributes from the old enum (${missingMappingValues}).`,
            );
        }

        // Verify that the values of the value mapping exist in the new enum datatype
        const badValues = Object.values(this.valuesMapping).filter(
            value => value !== null && !enumDataType.values.includes(value),
        );
        if (badValues.length > 0) {
            throw new Error(
                `${factory.name}.${property.name} (${this.description}): enum value mapping provided has values that do not exist in ${enumName} (${badValues}).`,
            );
        }

        if (!property.isNullable && Object.values(this.valuesMapping).some(value => value === null)) {
            throw new Error(
                `${factory.name}.${property.name} (${this.description}): enum value mapping provided has null value for a property that is not nullable.`,
            );
        }

        // update column type to new enum datatype

        const enumType = `${context.schemaName}.${
            property.isEnumArrayProperty() ? '_' : ''
        }${SchemaEnumUpgradeHelper.getTypeName(enumName)}`;

        const caseStatement = `CASE ${property.columnName}::TEXT ${valueMappingKeys
            .map(key => {
                return `WHEN '${key}' THEN ${
                    this.valuesMapping[key] === null ? 'NULL' : `'${this.valuesMapping[key]}'`
                }`;
            })
            .join(' ')} ELSE ${property.columnName}::TEXT END`;

        const using = `(${caseStatement})::${enumType}`;

        /**
         * Example:
            ALTER TABLE the_table
            ALTER COLUMN the_column
                SET DATA TYPE the_new_enum
                USING (
                CASE the_column::text
                    WHEN 'old_value1' THEN 'new_value1'
                    WHEN 'old_value2' THEN 'new_value2'
                    WHEN [...]
                END
                )::the_new_enum;
         */

        await sqlContext.updateColumnType(
            factory.requiredTableName,
            property.requiredColumnName,
            enumType,
            factory.table.getColumnDefinition(property.column!).comment!,
            {
                using,
            },
        );
    }
}
