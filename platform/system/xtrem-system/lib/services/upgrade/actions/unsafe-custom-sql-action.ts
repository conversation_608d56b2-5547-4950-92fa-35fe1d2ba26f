import { AnyValue, asyncArray, Context, NodeFactory, SchemaSqlContext, UpdateSqlContext } from '@sage/xtrem-core';
import { loggers } from '../../loggers';
import { CustomSqlAction, CustomSqlActionHelper, CustomSqlActionParameter } from './custom-sql-action';
import { SchemaUpgradeAction } from './schema-upgrade-action';
import { ForeignKeyDefinition, UpgradeAction, UpgradeActionExecuteOptions } from './upgrade-action';

interface UnsafeCustomSqlActionParameter extends CustomSqlActionParameter {
    /**
     * The (optional) list of tables concerned by this upgrade action.
     * CAUTION: tables will be:
     * - renamed to temp_xxxx when the action is run (before the execute)
     * - deleted when the action is over
     */
    tableNamesToRenameAndDrop?: string[];
}

interface UnsafeCustomSqlActionHelper extends CustomSqlActionHelper {}

/**
 * Action to execute custom SQL statements (data and/or schema queries).
 * CAUTION: this action will use a sys-pool, so it can be used to run DDL queries (and may break the schema of the database)
 * If you need so much power, please use a CustomAction instead.
 *
 * A list of table names can be provided. CAUTION: any of these tables will be renamed to temp_xxxx before the action is executed
 * and then dropped: MAKE sure to copy all their contents to other tables.
 *
 * Overriding classes will be able to execute SQL statements in the executeSql function (the function runSqlCommand will be used).
 */
export class UnsafeCustomSqlAction extends SchemaUpgradeAction<undefined> {
    #foreignKeysToRestore: ForeignKeyDefinition[] = [];

    /**
     * The max allowed duration (in ms) for a query
     */
    #maxAllowedDurationMs = 2000;

    constructor(readonly params: UnsafeCustomSqlActionParameter) {
        super({ ...params, node: undefined });
        if (params.maxAllowedDurationMs !== undefined) this.#maxAllowedDurationMs = params.maxAllowedDurationMs;
    }

    /**
     * @internal
     *
     * Returns whether the current action concerns a given factory
     */
    override concernsFactory(factory: NodeFactory): boolean {
        // Note: SQL actions are not directly bound to a specific factory
        // but its fixes / tablesToRenameAndDrop are.
        if (!factory.tableName) return false;

        const tableNames = this.params.tableNamesToRenameAndDrop;
        if (tableNames?.includes(factory.tableName)) {
            return true;
        }
        const fixes = this.params.fixes;
        if (fixes) {
            if (fixes.tables?.includes(factory.tableName)) {
                return true;
            }
            if (fixes.notNullableColumns?.some(f => f.table === factory.tableName)) {
                return true;
            }
        }

        return false;
    }

    /**
     * The function to use to execute sql commands
     */
    // eslint-disable-next-line class-methods-use-this
    private _executeSql<T extends AnyValue | void>(context: Context, sql: string, args?: any[]): Promise<T> {
        loggers.upgrade.warn(`Run custom SQL query : \n${sql} / ${args}`);
        return new UpdateSqlContext(context.application).executeSqlStatement<T>({ sql, args });
    }

    private _getBodyHelper(context: Context): UnsafeCustomSqlActionHelper {
        return {
            schemaName: context.schemaName,
            executeSql: async <T extends AnyValue | void>(sql: string, args: any[]): Promise<T> => {
                const dt0 = Date.now();
                const result = await this._executeSql<T>(context, sql, args);
                const duration = Date.now() - dt0;
                if (this.#maxAllowedDurationMs > 0 && duration > this.#maxAllowedDurationMs) {
                    throw new Error(
                        `The query was too long to execute. Duration was ${duration} ms, max=${this.#maxAllowedDurationMs} ms`,
                    );
                }
                return result;
            },
            getSystemColumnNames: node => CustomSqlAction.getSystemColumnNames(context, node),
        };
    }

    /**
     * The upgrade process
     */
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    protected async _execute(context: Context, _options: UpgradeActionExecuteOptions): Promise<void> {
        if (!this._preExecute) throw new Error('');
        await this.params.body(this._getBodyHelper(context));
    }

    /**
     * Pre-execute the action (invoked before any schema upgrade)
     */
    protected override async _preExecute(context: Context): Promise<void> {
        // rename the tables
        const schemaSqlContext = new SchemaSqlContext(context.application);
        const tableNamesToKeep: string[] = [];
        if (this.params.tableNamesToRenameAndDrop == null) return;
        await asyncArray(this.params.tableNamesToRenameAndDrop).forEach(async tableName => {
            const factory = context.application.getAllFactories().find(f => f.tableName === tableName);
            if (!factory) {
                throw new Error(`No factory is bound to table '${tableName}'`);
            }

            // Get the list of FKs that refers to the table
            // They are going to be deleted (see below) and will have to be re-created
            // in the postExecute function
            try {
                this.#foreignKeysToRestore = await UpgradeAction.getForeignKeysReferencingTable(
                    schemaSqlContext,
                    tableName,
                );
                // We will only reach this line if the table exists
                tableNamesToKeep.push(tableName);
            } catch (err) {
                // This table does not exist. It will happen when running the unit-tests
                // if the customSqlAction refers to a table that is defined in another
                // package
                return;
            }

            // Rename the table itself
            loggers.upgrade.info(
                `\t- rename table ${tableName} to temp_${tableName} and recreate it with its new format`,
            );
            let sql = `ALTER TABLE ${context.schemaName}.${tableName} RENAME TO temp_${tableName}`;
            await schemaSqlContext.executeSqlStatement({ sql });
            // And now, drop all the constraints of the renamed table
            // Note: this will not only drop the constraint on the table itself (PK, indexes, ...) but also
            // the FKs from other table that may refer a column from this table (they will be re-created
            // in the postProcess step)
            sql = `DO $$
                    DECLARE constraintRecord RECORD;
                    BEGIN
                        FOR constraintRecord IN (
                            SELECT con.conname
                                FROM pg_catalog.pg_constraint con
                                        INNER JOIN pg_catalog.pg_class rel
                                                ON rel.oid = con.conrelid
                                        INNER JOIN pg_catalog.pg_namespace nsp
                                                ON nsp.oid = connamespace
                                WHERE nsp.nspname = '${context.schemaName}'
                                        AND rel.relname = 'temp_${tableName}')
                        LOOP
                            EXECUTE 'ALTER TABLE ${context.schemaName}.temp_${tableName} DROP CONSTRAINT IF EXISTS ' || quote_ident(constraintRecord.conname) || ' CASCADE';
                        END LOOP;
                    END $$;`;
            await schemaSqlContext.executeSqlStatement({ sql });
            // Drop all the indexes from the temp_xxx table (there would be name collision when creating the new table)
            sql = `DO $$
                    DECLARE indexRecord RECORD;
                    BEGIN
                        FOR indexRecord IN (
                            SELECT indexname
                            FROM pg_indexes
                            WHERE schemaname = '${context.schemaName}' and tablename='temp_${tableName}'
                        )
                        LOOP
                           EXECUTE 'DROP INDEX IF EXISTS ${context.schemaName}.' || quote_ident(indexRecord.indexname);
                        END LOOP;
                    END $$;`;
            await schemaSqlContext.executeSqlStatement({ sql });
            // The table xxx (with the 'old' format) was renamed to a temp_xxx table.
            // We now have to re-create xxx with its 'new' format (don't create any constraint or
            // triggers: they will be created in the post_xxx steps)
            await factory.table.createTable(context, {
                skipConstraints: true,
                skipForeignKeys: true,
                skipDrop: true,
                skipTriggers: true,
            });
        });
        // Only keep the valid tables
        this.params.tableNamesToRenameAndDrop = tableNamesToKeep;
    }

    /**
     * Post-execute the action (invoked after all the schema upgrades)
     */
    protected override async _postExecute(context: Context): Promise<void> {
        loggers.upgrade.info(`Post-execute (${this.version}): '${this.description}'`);
        const schemaSqlContext = new SchemaSqlContext(context.application);
        const tableNames = this.params.tableNamesToRenameAndDrop;
        if (tableNames == null || tableNames.length === 0) return;

        loggers.upgrade.info(`\t - dropping temporary tables for SQL action ${this.description}`);
        await asyncArray(tableNames).forEach(async tableName => {
            loggers.upgrade.info(`\t\t- temp_${tableName}`);
            const sql = `DROP TABLE ${context.schemaName}.temp_${tableName}`;
            await schemaSqlContext.executeSqlStatement({ sql });
        });

        // Now, we have to recreate all the FKs that may have been dropped in the
        // preExecute step and make them refer the new table
        // Node: the constraint may already exist if the table was created by
        // the upgrade process.
        await asyncArray(this.#foreignKeysToRestore).forEach(fkDef =>
            UpgradeAction.createForeignKey(schemaSqlContext, fkDef),
        );

        await asyncArray(tableNames).forEach(async tableName => {
            const factory = context.application.getAllFactories().find(f => f.tableName === tableName);
            //  if the table is a subNode, then the sequence exists on the root factory table
            const factoryToUse = factory?.rootFactory || factory;
            if (factoryToUse && (factoryToUse.naturalKey || []).length === 0) {
                const sql = `
                        DO $$
                        DECLARE
                            sequence_name VARCHAR;
                            max_value INT8;
                            sql VARCHAR;
                        BEGIN
                          SELECT pg_get_serial_sequence('${context.schemaName}.${factoryToUse?.table.name}', '_id') INTO sequence_name;
                          SELECT COALESCE(max(_id),0) max_val INTO max_value FROM ${context.schemaName}.${factoryToUse?.table.name};
                          sql = format('ALTER SEQUENCE '||sequence_name||' RESTART WITH '||(max_value+1));
                          EXECUTE sql;
                        END $$;`;
                await schemaSqlContext.executeSqlStatement({ sql });
            }
        });
    }
}
