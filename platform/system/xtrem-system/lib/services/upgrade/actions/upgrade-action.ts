import {
    AnyRecord,
    Application,
    AsyncResponse,
    Context,
    Node,
    NodeFactory,
    SchemaSqlContext,
    StaticThis,
} from '@sage/xtrem-core';
import { UpgradeContext } from '..';
import { loggers } from '../../loggers';

/**
 * ForeignKey definition (to be used with getForeignKeysReferencingTable/createForeignKey functions)
 */
export interface ForeignKeyDefinition {
    targetTable: string;
    /** The SQL definition of the foreign key */
    definition: string;
    name: string;
    sourceTable: string;
}

/**
 * Options for execution of actions
 */
export type UpgradeActionExecuteOptions = {
    /**
     * Is the action executed while we are recording SQL files ?
     */
    forRecording: boolean;

    /**
     * The (optional) context to use to execute the action. If none is provided, a new one will be created.
     */
    context?: Context;
};

/**
 * The base interface that MUST be implemented by options provided to actions' constructor
 */
export interface UpgradeActionOptions {
    /**
     * The (optional) list of factories for which the setup data must be up-to-date before the action is executed
     */
    dependsOnSetupData?: string[];
}

interface UpgradeActionOptionsWithNode<This extends Node | unknown = unknown> extends UpgradeActionOptions {
    node: (() => { new (): This }) | undefined;
    description: string;
}

/**
 * Base class for upgrade actions.
 * Inheritors must override the _execute() function and may (if needed) override _preExecute/_postExecute
 */
export abstract class UpgradeAction<This extends Node | unknown = unknown> {
    private _preExecutionDone = false;

    private _postExecutionDone = false;

    private _executionDone = false;

    /**
     * Indicates whether this action was automatically created by the upgrade engine (true) or
     * comes from an UpgradeSuite (false)
     */
    isAutomaticAction = false;

    /**
     * @internal
     * Internal management: do not set it manually
     */
    version: string;

    protected _description: string;

    /**
     * The description of the upgrade action
     * @param description
     * @param dependsOnSetupData The (optional) list of factories for which the setup data must be up-to-date before the action is executed
     */
    constructor(private readonly _options: UpgradeActionOptionsWithNode<This>) {
        this._description = _options.description;
    }

    protected get node(): (() => { new (): This }) | undefined {
        return this._options.node;
    }

    get description(): string {
        return this._description;
    }

    /**
     * The (optional) list of factories for which the setup data must be up-to-date before the action is executed
     */
    get dependsOnSetupData(): string[] | undefined {
        return this._options.dependsOnSetupData;
    }

    /** @internal */
    getFactory(application: Application): NodeFactory | undefined {
        if (this.node == null) return undefined;
        return application.getFactoryByConstructor(this.node() as StaticThis<Node>);
    }

    /**
     * Is the action a data or schema action ?
     */
    abstract get kind(): 'data' | 'schema';

    /**
     * Returns whether the action was pre-executed
     */
    get preExecutionDone(): boolean {
        return this._preExecutionDone;
    }

    /**
     * Verifies that the action can be executed.
     * Note : inconsistencies will result in thrown errors
     */
    // eslint-disable-next-line class-methods-use-this, @typescript-eslint/no-unused-vars
    verify(_context: Context): void {
        // Nothing to do
    }

    /**
     * Pre-execute the action (invoked before any schema upgrade)
     */
    async preExecute(upgradeContext: UpgradeContext, options: UpgradeActionExecuteOptions): Promise<void> {
        if (this._preExecutionDone) {
            throw new Error(`Cannot pre-execute action '${this.description}': was already pre-executed`);
        }
        loggers.upgrade.verbose(() => `Pre-execute action (${this.kind}@${this.version}): ${this.description}`);
        await this.executeWithContext(upgradeContext, options, context => this._preExecute(context));

        loggers.upgrade.debug(() => `<<<< pre-execute action (${this.kind}@${this.version}): ${this.description}`);
        this._preExecutionDone = true;
    }

    /**
     * Pre-execute the action (invoked before any schema upgrade)
     */
    // eslint-disable-next-line class-methods-use-this, @typescript-eslint/no-unused-vars
    protected _preExecute(context: Context): AsyncResponse<void> {
        /* stub */
    }

    /**
     * Execute the action
     */
    async execute(upgradeContext: UpgradeContext, options: UpgradeActionExecuteOptions): Promise<void> {
        if (!this.preExecutionDone) {
            throw new Error(`Cannot execute action '${this.description}': was not pre-executed`);
        }
        if (this._executionDone) {
            throw new Error(`Cannot execute action '${this.description}': was already executed`);
        }
        loggers.upgrade.info(`Execute action (${this.kind}@${this.version}): ${this.description}`);
        await this.executeFromUpgradeContext(upgradeContext, options);
        loggers.upgrade.debug(() => `<<<< execute action (${this.kind}@${this.version}): ${this.description}`);
        this._executionDone = true;
    }

    /**
     * Executes a body either in the context from the options (if provided) or from a new context
     * @param upgradeContext
     * @param options
     * @param body
     */
    // eslint-disable-next-line class-methods-use-this
    protected async executeWithContext(
        upgradeContext: UpgradeContext,
        options: UpgradeActionExecuteOptions,
        body: (context: Context) => AsyncResponse<void>,
    ): Promise<void> {
        if (options.context) {
            await body(options.context);
        } else {
            await upgradeContext.withCommittedContext(context => body(context));
        }
    }

    /**
     * Execute the action from an upgradeContext
     * @param upgradeContext
     * @param options
     */
    protected async executeFromUpgradeContext(
        upgradeContext: UpgradeContext,
        options: UpgradeActionExecuteOptions,
    ): Promise<void> {
        await this.executeWithContext(upgradeContext, options, context => this._execute(context, options));
    }

    /**
     * The upgrade process (to be implemented by inheritors)
     */

    protected abstract _execute(context: Context, options: UpgradeActionExecuteOptions): AsyncResponse<void>;

    /**
     * Returns whether the action was executed
     */
    get executionDone(): boolean {
        return this._executionDone;
    }

    /**
     * Post-execute the action (invoked after all the schema upgrades)
     */
    async postExecute(upgradeContext: UpgradeContext, options: UpgradeActionExecuteOptions): Promise<void> {
        if (!this.preExecutionDone) {
            throw new Error(`Cannot post-execute action '${this.description}': was not pre-executed`);
        }
        if (!this.executionDone) {
            throw new Error(`Cannot post-execute action '${this.description}': was not executed`);
        }
        if (this._postExecutionDone) {
            throw new Error(`Cannot post-execute action '${this.description}': was already post-executed`);
        }
        loggers.upgrade.verbose(() => `Post-execute action (${this.kind}@${this.version}): ${this.description}`);
        await this.executeWithContext(upgradeContext, options, context => this._postExecute(context));

        loggers.upgrade.debug(() => `<<<< post-execute action (${this.kind}@${this.version}): ${this.description}`);
        this._postExecutionDone = true;
    }

    /**
     * Post-execute the action (invoked after all the schema upgrades)
     */
    // eslint-disable-next-line class-methods-use-this, @typescript-eslint/no-unused-vars
    protected async _postExecute(context: Context): Promise<void> {
        /* stub */
    }

    /**
     * Returns whether the action was post-executed
     */
    get postExecutionDone(): boolean {
        return this._postExecutionDone;
    }

    /**
     * @internal
     *
     * Returns whether the current action concerns a given factory
     */
    concernsFactory(factory: NodeFactory): boolean {
        return this.getFactory(factory.application) === factory;
    }

    /**
     * Returns the list of foreignKeys that references a given table
     * @param schemaContext
     * @param tableName
     */

    protected static async getForeignKeysReferencingTable(
        schemaContext: SchemaSqlContext,
        tableName: string,
    ): Promise<ForeignKeyDefinition[]> {
        // Get the list of FKs that refers to the table
        // They are going to be deleted (see below) and will have to be re-created
        // in the postExecute function
        const sql = `SELECT
                            r.conname conname,
                            pg_catalog.pg_get_constraintdef(r.oid, false) condef
                            ,rel.relname relname
                        FROM
                            pg_catalog.pg_constraint r
                        INNER JOIN pg_catalog.pg_class rel ON rel.oid = r.conrelid
                    WHERE
                        r.confrelid = '${schemaContext.schemaName}.${tableName}'::regclass;`;
        // Note: the executeSqlStatement will fail if the table does not exist
        return (await schemaContext.executeSqlStatement<AnyRecord[]>({ sql })).map((row: any) => {
            return {
                targetTable: tableName,
                definition: row.condef as string,
                name: row.conname as string,
                sourceTable: row.relname as string,
            };
        });
    }

    protected static async createForeignKey(
        schemaContext: SchemaSqlContext,
        fkDef: ForeignKeyDefinition,
    ): Promise<void> {
        await schemaContext.executeSqlStatement({
            sql: UpgradeAction.getSqlToCreateForeignKey(schemaContext.schemaName, fkDef, true),
        });
    }

    protected static getSqlToCreateForeignKey(
        schemaName: string,
        fkDef: ForeignKeyDefinition,
        withDo: boolean,
    ): string {
        // Note: some errors are "allowed" when re-creating the FKs:
        // -duplicate_object: will be raised when the sql action work from a table to itself. The existing
        //       table xxx will have been renamed to temp_xxx and a new table xxx will be created with the same FKs.
        // -undefined_column: will be raised when the original FK was refering a column that does no longer
        //       exist in the new tables definition
        const sql = `
          BEGIN
              ALTER TABLE IF EXISTS ${schemaName}.${fkDef.sourceTable} ADD CONSTRAINT ${fkDef.name} ${fkDef.definition};
          EXCEPTION
            WHEN duplicate_object THEN RAISE NOTICE 'Constraint ${fkDef.sourceTable}.${fkDef.name} already exists';
            WHEN undefined_column THEN RAISE NOTICE 'Could not create constraint ${fkDef.sourceTable}.${fkDef.name}';
          END;`;
        if (!withDo) return sql;
        return `
        DO $$
        BEGIN
        ${sql}
        END $$;`;
    }

    /**
     * Returns the SQL command to execute to drop a foreign key
     */
    protected static getSqlToDropForeignKey(schemaName: string, tableName: string, fkName: string): string {
        return `ALTER TABLE ${schemaName}.${tableName} DROP CONSTRAINT IF EXISTS ${fkName};`;
    }
}
