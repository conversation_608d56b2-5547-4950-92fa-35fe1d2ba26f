/* eslint-disable class-methods-use-this */
import { Context, Node } from '@sage/xtrem-core';
import { SchemaUpgradeAction } from './schema-upgrade-action';
import { UpgradeActionExecuteOptions, UpgradeActionOptions } from './upgrade-action';

interface SchemaAllowStringToIntegerActionOptions<This extends Node> extends UpgradeActionOptions {
    node: () => { new (): This };
    propertyName: Exclude<keyof This, '$' | '_id' | '_customData' | '_etag' | '_sortValue' | '_sourceId' | '_vendor'>;
}

/**
 * This action does not do anything.
 * It is only a marker used to allow a string property to be upgraded to an integer property.
 *
 * WARNING : This action should be done only if you are sure that the string column is empty in all environment.
 * It's really difficult to be 100% sure that reducing a string column will pass on customers' databases.
 * The best is to register another action (CustomSqlAction, ...) to manage data that are not integer-compliant  before
 * the column is upgraded to integer.
 *
 * @example
 *       export const allowShorterString = new SchemaAllowStringToIntegerAction({
 *           node: () => UpgradeAlterColumns,
 *           propertyName: 'stringToInteger',
 *       });
 *
 */
export class SchemaAllowStringToIntegerAction<This extends Node> extends SchemaUpgradeAction<This> {
    /**
     *
     * @param propertyName The name of the string property that can be upgraded to integer
     */
    constructor(readonly options: SchemaAllowStringToIntegerActionOptions<This>) {
        super({
            ...options,
            description: `Allow string property ${options.node().name}.${String(
                options.propertyName,
            )} to be converted to integer property`,
        });
    }

    /**
     * The upgrade process
     */
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    protected async _execute(_context: Context, _options: UpgradeActionExecuteOptions): Promise<void> {
        // Nothing to do
    }
}
