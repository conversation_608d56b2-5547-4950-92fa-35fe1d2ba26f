import { Application, FixSchema, S3Manager, SchemaSqlContext } from '@sage/xtrem-core';
import * as fs from 'fs';
import * as os from 'os';
import * as fsp from 'path';
import { loggers } from '../loggers';

const logger = loggers.upgrade;

const tempFolder = os.tmpdir();

function deleteDumpFiles(inputPath: string, outputPath: string): void {
    logger.info('Clean up dump files');
    if (fs.existsSync(inputPath)) fs.unlinkSync(inputPath);
    if (fs.existsSync(outputPath)) fs.unlinkSync(outputPath);
    logger.info('Successfully cleaned dump files');
}

/**
 * Fix the order of columns
 * - Dump the application schema
 * - Rename the application schema to back it up
 * - Update the schema dump file correcting the column order on the create table statements
 * - Restore the application schema from the edited dump
 * - Drop the backup schema
 * - Delete the dump files
 * @param application
 */
export async function fixColumnOrder(application: Application): Promise<void> {
    const originalSchemaName = application.schemaName;
    const backupSchemaName = `${originalSchemaName}_backup`;
    const inputFileName = 'sql.dump';
    const inputPath = fsp.join(tempFolder, inputFileName);
    const outputFileName = 'sql.fixed.dump';
    const outputPath = fsp.join(tempFolder, outputFileName);
    try {
        // Dump the schema to the temp folder of the OS
        logger.info(`Dumping schema ${originalSchemaName}`);
        await S3Manager.dumpSchemaToFile(tempFolder, inputFileName, originalSchemaName);
        logger.info('Successfully dumped schema');

        const schemaContext = new SchemaSqlContext(application);

        // Rename the application schema to back it up
        logger.info(`Backup - Rename  schema ${originalSchemaName} to ${backupSchemaName}`);
        await schemaContext.renameSchema(originalSchemaName, backupSchemaName);
        logger.info('Successfully renamed schema');

        // Before this try block for any error, the rollback just to delete dump files
        // The catch of this try block needs to restore the schema to the one that was backed up.
        try {
            // Fix the column order in the dump file
            logger.info(`Fixing table order on schema ${originalSchemaName}`);
            await FixSchema.fixColumnOrder(tempFolder, inputFileName, outputFileName);
            logger.info('Successfully fixed table order on schema');

            // Restore the schema from the edited dump
            logger.info(`Restoring schema ${originalSchemaName}`);
            await S3Manager.restoreFromDumpFile(tempFolder, outputPath, originalSchemaName);
            logger.info('Successfully restored schema');

            // Drop the backup schema as the new schema is good
            logger.info(`Dropping backup schema ${backupSchemaName}`);
            await schemaContext.dropSchema(backupSchemaName);
            logger.info('Successfully dropped backup schema');
        } catch (postRenameError) {
            // Recover from error:
            // - drop the bad schema and rename backup back to application schema name
            await schemaContext.dropSchema(originalSchemaName);
            await schemaContext.renameSchema(backupSchemaName, originalSchemaName);
            // rethrow to terminate process
            throw postRenameError;
        }
        deleteDumpFiles(inputPath, outputPath);
    } catch (e) {
        deleteDumpFiles(inputPath, outputPath);
        logger.error(`Fixing of column order has failed : \n ${e.stack}`);
        throw e;
    }
}
