import { UpgradeMetadata } from '@sage/xtrem-cli-layers';
import {
    Application,
    asyncArray,
    ConfigManager,
    Context,
    CoreHooks,
    getFactoryDependsOn,
    NodeFactory,
    S3Helper,
    S3ObjectInfo,
    SchemaSqlContext,
    SqlFileDataRow,
    SqlFileDataSet,
    SqlFileDataSets,
    SystemError,
    UpgradeMetricsType,
} from '@sage/xtrem-core';
import { TenantService } from '@sage/xtrem-data-management';
import { SqlRecorder, SqlRecorderActionType } from '@sage/xtrem-postgres';
import { Dict } from '@sage/xtrem-shared';
import * as fs from 'fs';
import * as fsp from 'path';
import * as prettier from 'prettier';
import * as semver from 'semver';
import { loggers } from '../loggers';
import { UpgradeAction } from './actions';
import { GitWrapper } from './git-wrapper';
import { SystemSqlUpgrade } from './system-sql-upgrade';
import { UpgradeContext } from './upgrade-context';
import { UpgradeSqlSchema } from './upgrade-sql-schema';

const logger = loggers.upgrade;

/**
 * An entry of a SQL file (either a UpgradeRecordedSqlQuery or a UpgradeRecordedAction)
 */
interface UpgradeRecordedEntry {
    /**
     * The (optional) list of (setup data) factories that must be up-to-date for this query
     */
    dependsOnSetupData?: string[];
}

/**
 * A recorded SQL query
 */
export interface UpgradeRecordedSqlQuery extends UpgradeRecordedEntry {
    /**
     * Does the SQL query require 'admin' pool to get executed
     */
    isSysPool: boolean;
    /**
     * The SQL query to execute, an array of strings if multi-line, a string otherwise.
     */
    sql: string | string[];
    /**
     * The arguments for the SQL querry
     */
    args?: any[];

    /**
     * The (optional) description of the action at the origin of this command
     */
    actionDescription?: string;
}

/**
 * A recorded action
 */
export interface UpgradeRecordedAction extends UpgradeRecordedEntry {
    /**
     * The action
     */
    action: SqlRecorderActionType;

    /**
     * The (optional) arguments for the action
     */
    args?: Dict<string>;

    /**
     * Should this action be executed as soon as it is read from the SQL file (true) - or can it be postponed
     * to the very last required moment (mainly used for reload_setup_data actions) ?
     */
    immediate?: boolean;
}

/**
 * A recorded command (SQL query or action)
 */
export type UpgradeRecordedCommand = UpgradeRecordedSqlQuery | UpgradeRecordedAction;

/**
 * Information for a SQL file
 */
type SqlFileInfo = {
    /**
     * The filename (relative to the sqlRecordsFolder of the upgradeContext)
     */
    relativeFilename: string;
    /**
     * The 'fromVersion' of the file
     */
    fromVersion: string;
    /**
     * The 'toVersion' of the file
     */
    toVersion: string;
};

export type UpgradeRecordedSqlFile = {
    /**
     * The 'from' version of the SQL file
     */
    fromVersion: string;
    /**
     * The 'to' version of the SQL file
     */
    toVersion: string;
    /**
     * The list of commands to replay
     */
    commands: UpgradeRecordedCommand[];
    /**
     * the git HEAD when the file was generated (before the commit of the file itself)
     */
    gitHead: string;

    /**
     * The data to be reloaded (indexed by factory name)
     */
    data: SqlFileDataSets;
};

export interface CommandsRecorder extends SqlRecorder {
    /**
     * Register a data set for a factory that will have to be written to the SQL file
     */
    registerDataSet(factory: NodeFactory, data: any[]): void;
}

/**
 * Records sql queries/actions into a sqlxxxxxx.json file
 */
export class UpgradeCommandsRecorder {
    /**
     * Is the recorder paused ?
     */
    private _isPaused = false;

    private static readonly _schemaMarker = '%%SCHEMA_NAME%%';

    private readonly _schemaRegEx: RegExp;

    private _sqlFileContent: UpgradeRecordedSqlFile;

    private _fromVersion: string;

    /**
     * The action currently executed
     */
    currentAction?: UpgradeAction;

    /**
     * The list of factories whose setup data will be embedded in the SQL file
     */
    private readonly _setupDataToEmbed: string[] = [];

    private constructor(private readonly upgradeContext: UpgradeContext) {
        this._schemaRegEx = new RegExp(upgradeContext.application.schemaName, 'g');
        this._fromVersion = upgradeContext.initialVersion;
    }

    private async init(): Promise<UpgradeCommandsRecorder> {
        await this._resetCommands();
        return this;
    }

    static create(upgradeContext: UpgradeContext): Promise<UpgradeCommandsRecorder> {
        return new UpgradeCommandsRecorder(upgradeContext).init();
    }

    private async _resetCommands(): Promise<void> {
        if (this._sqlFileContent == null) {
            logger.info(
                `SQL recorder: start, initialVersion = ${this.upgradeContext.application.mainPackage.name}@${this._fromVersion}`,
            );
        } else {
            logger.info(
                `SQL recorder: reset initialVersion to ${this.upgradeContext.application.mainPackage.name}@${this._fromVersion}`,
            );
        }
        this._sqlFileContent = {
            fromVersion: this._fromVersion,
            toVersion: this.upgradeContext.application.mainPackage.packageJson.version,
            gitHead: await GitWrapper.getHead(),
            commands: [],
            data: {},
        };
    }

    /**
     * Executes a body where the recorder will be paused
     */
    async withoutRecording<T>(body: () => Promise<T>): Promise<T> {
        if (this._isPaused) {
            return body();
        }
        this._isPaused = true;
        logger.debug(() => 'Recorder is paused');
        try {
            return await body();
        } finally {
            logger.debug(() => 'Recorder is resumed');
            this._isPaused = false;
        }
    }

    /**
     * Returns whether the recorder is paused (or recording)
     */
    isPaused(): boolean {
        return this._isPaused;
    }

    /**
     * Register a data set for a factory that will have to be written to the SQL file
     */
    registerDataSet(factory: NodeFactory, dataSet: any[]): void {
        const naturalKey = factory.naturalKey;
        if (!naturalKey)
            throw new Error(`Only setup data with a natural key can be recorder, factory was ${factory.name}`);
        const metadata = UpgradeMetadata.getFactoryMetadata(factory, {
            skipInheritedProperties: false,
            skipNullableColumns: false,
        });

        if (dataSet.length === 0) {
            this._sqlFileContent.data[factory.name] = {
                metadata,
                rows: [],
            };
            return;
        }
        // note : dataSet is a list of objects { first_name:"John", last_name:"DOE"}
        const dataToWrite: SqlFileDataSet = {
            metadata,
            rows: dataSet.reduce((total, row) => {
                // dataSet is a list of objects { first_name:"John", last_name:"DOE"}
                // map it to an array of values : ["John", "DOE"]
                const mappedRow = metadata.columns.map(column => row[column.name]);
                total.push(mappedRow);
                return total;
            }, []),
        };

        const alreadyRegisteredData = this._sqlFileContent.data[factory.name];
        if (alreadyRegisteredData) {
            if (JSON.stringify(alreadyRegisteredData) !== JSON.stringify(dataToWrite)) {
                throw new Error(
                    `Logic error: data for factory ${factory.name} are already set (with different content).`,
                );
            }
        }

        // Get the headers from the first line
        this._sqlFileContent.data[factory.name] = dataToWrite;
    }

    /**
     * Record an action (it will then be flushed to a sqlxxx.json file)
     * @param action the action
     * @param args the parameters of the action
     */
    recordAction(action: SqlRecorderActionType, args?: Dict<string>): void {
        if (action === 'reload_setup_data' && args) {
            // Keep track of the factory: the setup data for this factory will be embedded in the SQL file (see dispose)
            this._setupDataToEmbed.push(args.factory);
        }
        this._sqlFileContent.commands.push({
            action,
            args,
        });
    }

    /**
     * Record a SQL query (it will then be flushed to a sqlxxx.json file)
     * @param isSysPool does the SQL query needs the sysPool to be replayed ?
     * @param sqlQuery the SQL query
     * @param args the args for the SQL query
     */
    recordSqlQuery(isSysPool: boolean, sqlQuery: string, args?: any[]): void {
        if (this._isPaused) return;
        if (sqlQuery === '') return;
        try {
            if (/^select /i.test(sqlQuery)) {
                if (!sqlQuery.includes('** FORCE RECORD **')) return;
            }

            if (sqlQuery.includes("** DON'T RECORD **")) return;
            if (sqlQuery.includes('-- ** CONFIGURATION **')) return;

            let argsToSet = args;
            if (argsToSet) {
                argsToSet = JSON.parse(
                    JSON.stringify(args).replace(this._schemaRegEx, UpgradeCommandsRecorder._schemaMarker),
                );
            }

            // Replace the schema name in the JSON (SQL + arguments) with a marker
            // This will allow to record SQL from a schema and replay SQL into another
            // schema. This implies that the schema name must be cryptic enough to avoid mistakes
            // For instance, Azure pipelines will use xxxxyyyyzzzz as a schema name.
            const queryToRecord = sqlQuery.replace(this._schemaRegEx, UpgradeCommandsRecorder._schemaMarker);

            // Record the query as an array of strings (one per line) if it contains newlines.
            // This makes it easier to read the JSON files that contain multi-line SQL scripts.
            const sql = queryToRecord.includes('\n') ? queryToRecord.split('\n') : queryToRecord;

            const entry = {
                isSysPool,
                sql,
                args: argsToSet,
            } as UpgradeRecordedSqlQuery;
            if (this.currentAction != null) {
                if (this.currentAction.dependsOnSetupData) {
                    entry.dependsOnSetupData = this.currentAction.dependsOnSetupData;
                }
                entry.actionDescription = this.currentAction.description;
            }

            this._sqlFileContent.commands.push(entry);
        } catch (err) {
            throw new SystemError(`Could not record SQL query ${err.message}`, err);
        }
    }

    /**
     * Ends the recording session. From now, no more commands will be added to the SQL file
     */
    endSession(): void {
        this._isPaused = true;
    }

    async reset(): Promise<void> {
        await this._resetCommands();
    }

    private _getPrettierOptions(): prettier.Options {
        const configFilename = '.prettierrc';
        let folder = this.upgradeContext.application.dir;
        // eslint-disable-next-line no-constant-condition
        while (true) {
            const fullPath = fsp.join(folder, configFilename);
            if (fs.existsSync(fullPath)) {
                return JSON.parse(fs.readFileSync(fullPath, 'utf-8')) as prettier.Options;
            }
            const parentFolder = fsp.join(folder, '..');
            if (parentFolder === folder) {
                // We have reached the root of the file system...
                throw new Error(
                    `Could not find any .prettierrc file, starting from ${this.upgradeContext.application.dir}`,
                );
            }
            folder = parentFolder;
        }
    }

    /**
     * Persist all the recorded commands to a SQL file
     */
    async dispose(): Promise<void> {
        // ensure we have data for all expected factories
        const missingFactories = this._setupDataToEmbed.filter(
            factoryName => this._sqlFileContent.data[factoryName] == null,
        );
        if (missingFactories.length > 0) {
            throw new Error(`Data are missing for factories ${missingFactories.join(',')}`);
        }

        logger.info('Formatting SQL file (prettier)');
        const sqlFilename = this._getUpgradeFilename(this._fromVersion);
        const prettierConfig: prettier.Options = {
            ...this._getPrettierOptions(),
            ...{
                // We need to provide a dummy filename with a .json extension so that
                // prettier will use the right configuration
                filepath: sqlFilename,
            },
        };
        const formatted = await prettier.format(JSON.stringify(this._sqlFileContent), prettierConfig);

        logger.info(`Writing SQL file ${sqlFilename}`);
        await fs.promises.writeFile(sqlFilename, formatted, { encoding: 'utf8' });
    }

    /**
     * Returns the filename used to store commands
     */
    private _getUpgradeFilename(fromVersion: string): string {
        return fsp.join(
            this.upgradeContext.sqlRecordsFolder,
            `sqlCommands-${this.upgradeContext.application.mainPackage.name
                .replace(/@/g, '')
                .replace(/[/,\\]/g, '-')}@${fromVersion}-${
                this.upgradeContext.application.mainPackage.packageJson.version
            }.json`,
        );
    }

    private static _parseUpgradeFilename(relativeFilename: string): SqlFileInfo {
        const match = relativeFilename.match(/sqlCommands-([a-z0-9-]+)@(\d+\.\d+\.\d+)-(\d+\.\d+\.\d+)\.json$/);
        if (!match) throw new Error(`Could not parse filename ${relativeFilename}`);
        return {
            relativeFilename,
            fromVersion: match[2],
            toVersion: match[3],
        };
    }

    /**
     * Throw an error when a hole is found in the chain of SQL files.
     */
    private static _throwMissingFilesError(
        fromVersionToCheck: string,
        toVersionToCheck: string,
        sqlFilesToReplay: SqlFileInfo[],
        parsedFiles: SqlFileInfo[],
    ): void {
        if (semver.major(fromVersionToCheck) + 1 !== semver.major(toVersionToCheck)) {
            throw new Error(
                `Could not replay commands, some files are missing between ${fromVersionToCheck} and ${toVersionToCheck}, available files are: \n\t- ${sqlFilesToReplay
                    .map(r => r.relativeFilename)
                    .join('\n\t- ')}`,
            );
        }
        // Version change: when a release branch is created (v11 for instance),
        // the branch is created from master. the package P1 has a given version
        // (11.0.50 for instance) but then, some hotfixes will be installed to the
        // cluster (11.0.51, 11.0.52, ...., 11.0.57 for instance)
        // No SQL files are generated for hotfixes (in the sample, there won't be
        // any SQL file from 11.0.51 to 11.0.57). When the cluster will be upgraded
        // on the next release, it will be in v11.0.57 (fromVersionToCheck), toVersion=12.x.y
        const fileToApply = parsedFiles.find(
            r => r.toVersion === toVersionToCheck && semver.lt(r.fromVersion, fromVersionToCheck),
        );
        if (fileToApply) {
            // We could find a SQL file for 11.0.50 -> 12.x.y, we are now sure
            // that versions [11.0.51...11.0.57] were only hotfixes
            logger.warn(`Versions from ${fileToApply.fromVersion} to ${fromVersionToCheck} will be ignored.`);
        }
    }

    /**
     * Returns the list of Sql files to apply to migrate an application from  a specific version
     * (default will be the application current version - from sysPackVersion)
     * to its version (from its package.json).
     */
    static async getFilenamesToReplay(
        upgradeContext: UpgradeContext,
        options: {
            fromVersion?: string;
            dontValidate?: boolean;
        } = {},
    ): Promise<SqlFileInfo[]> {
        const currentVersion =
            options.fromVersion ||
            (await upgradeContext.packageManager.getCurrentVersion(upgradeContext.application.mainPackage)) ||
            '0.0.0';
        const sqlFolder = upgradeContext.sqlRecordsFolder;
        if (!fs.existsSync(sqlFolder)) {
            fs.mkdirSync(sqlFolder);
        }

        const allFolderFiles = fs.readdirSync(sqlFolder);
        const parsedFiles = allFolderFiles.map(filename => this._parseUpgradeFilename(filename));
        const sqlFilesToReplay = parsedFiles
            .filter(info => semver.gte(info.fromVersion, currentVersion))
            .sort((i1, i2) => semver.compare(i1.fromVersion, i2.fromVersion));

        if (sqlFilesToReplay.length === 0 && upgradeContext.options.mode === 'replayOnly') {
            throw new Error(`Could not find any SQL files to replay in the folder ${sqlFolder}`);
        }

        if (options.dontValidate) return sqlFilesToReplay;

        // The first SQL files to replay must match the current version
        if (sqlFilesToReplay.length > 0 && sqlFilesToReplay[0].fromVersion !== currentVersion) {
            UpgradeCommandsRecorder._throwMissingFilesError(
                currentVersion,
                sqlFilesToReplay[0].fromVersion,
                sqlFilesToReplay,
                parsedFiles,
            );
        }

        // Check if we don't have "holes" in the list of files
        // results[n].fromVersion must be equal to results[n-1].toVersion
        for (let i = 1; i < sqlFilesToReplay.length; i += 1) {
            if (sqlFilesToReplay[i].fromVersion !== sqlFilesToReplay[i - 1].toVersion) {
                UpgradeCommandsRecorder._throwMissingFilesError(
                    sqlFilesToReplay[i - 1].toVersion,
                    sqlFilesToReplay[i].fromVersion,
                    sqlFilesToReplay,
                    parsedFiles,
                );
            }
        }

        if (upgradeContext.options.mode !== 'replayOnly') return sqlFilesToReplay;
        // When we are replaying the sql queries, we need to have all the scripts

        // When in 'record' mode, we will apply all the available scripts and then, the upgrade engine will
        // run (and record) the missing migrations.

        if (
            sqlFilesToReplay[sqlFilesToReplay.length - 1].toVersion !==
            upgradeContext.application.mainPackage.packageJson.version
        ) {
            UpgradeCommandsRecorder._throwMissingFilesError(
                sqlFilesToReplay[sqlFilesToReplay.length - 1].toVersion,
                upgradeContext.application.mainPackage.packageJson.version,
                sqlFilesToReplay,
                parsedFiles,
            );
        }
        return sqlFilesToReplay;
    }

    /**
     * Returns whether a recorded command stands for a SQL command
     */
    private static _isRecordedSqlQuery(command: UpgradeRecordedCommand): command is UpgradeRecordedSqlQuery {
        return (command as UpgradeRecordedSqlQuery).sql != null;
    }

    /**
     * Returns whether a recorded command stands for an action
     */
    private static _isRecordedAction(command: UpgradeRecordedCommand): command is UpgradeRecordedAction {
        return !UpgradeCommandsRecorder._isRecordedSqlQuery(command);
    }

    /**
     * Execute a recorded SQL query
     * @param context
     * @param sqlQuery the SQL query to execute
     * @param sqlFileInfoRelativeFilename the (relative) filename of the SQL file that contains the sql query to execute
     * @param performanceMonitor the monitor to log performances
     */
    private static async _executeRecordedSqlQuery(
        context: Context,
        sqlQuery: UpgradeRecordedSqlQuery,
        sqlFileInfoRelativeFilename: string,
        performanceMonitor: PerformanceMonitor,
    ): Promise<void> {
        const pool = sqlQuery.isSysPool ? new SchemaSqlContext(context.application).connectionPool : context.sqlPool;
        const sql = Array.isArray(sqlQuery.sql) ? sqlQuery.sql.join('\n') : sqlQuery.sql;
        logger.debug(() => sql);
        await performanceMonitor.monitor(
            async () => {
                try {
                    await pool.withConnection(cnx => pool.execute(cnx, sql, sqlQuery.args));
                } catch (err) {
                    throw new Error(
                        `Could not replay file ${sqlFileInfoRelativeFilename}\n\t- sqlQuery = ${sql}\n\t- args= ${JSON.stringify(
                            sqlQuery.args || {},
                            null,
                            '\t',
                        )}\n\t- error: ${err.stack}`,
                    );
                }
            },
            () => {
                return {
                    sqlFile: sqlFileInfoRelativeFilename,
                    sqlStatement: sql,
                    usesSysPool: sqlQuery.isSysPool,
                    tenantId: '',
                };
            },
        );
    }

    /**
     * Execute a recorded action
     */
    private static async _executeRecordedAction(
        application: Application,
        action: UpgradeRecordedAction,
        sqlFileRelativeFilename: string,
        performanceMonitor: PerformanceMonitor,
    ): Promise<void> {
        if (action.action === 'system_upgrade') {
            if (!action.args || !action.args.version)
                throw new Error("'version' argument is missing for 'system_upgrade' action");
            await UpgradeCommandsRecorder._executeSystemUpgradeAction(
                application,
                action.args.version,
                sqlFileRelativeFilename,
                performanceMonitor,
            );
            return;
        }
        if (action.action === 'metadata_upgrade') {
            const options = { fullReload: Boolean(action.args?.fullReload) };
            await CoreHooks.metadataManager.upgradeMetadata(application, options);
            return;
        }
        throw new Error(`Unsupported action type: ${action.action}`);
    }

    /**
     * Execute a system upgrade
     * @param application
     * @param version
     * @param sqlFileInfoRelativeFilename the (relative) filename of the SQL file that contains the reload actions to execute
     * @param performanceMonitor the monitor to log performances
     */
    private static async _executeSystemUpgradeAction(
        application: Application,
        version: string,
        sqlFileRelativeFilename: string,
        performanceMonitor: PerformanceMonitor,
    ): Promise<void> {
        const indexFilename = fsp.join(__dirname, '../../system-upgrades', `v${version}`, 'index');
        logger.info(`Execute system upgrade ${version} from file ${indexFilename}`);

        // eslint-disable-next-line import/no-dynamic-require, global-require
        const mod = require(indexFilename);
        if (!mod.systemUpgrades) {
            throw new Error(`The file ${indexFilename} does not contain any 'systemUpgrades' constant.`);
        }
        const systemUpgrades = mod.systemUpgrades as SystemSqlUpgrade[];

        await performanceMonitor.monitor(
            async () => {
                await asyncArray(systemUpgrades).forEach(async systemUpgrade => {
                    logger.warn(`Replaying system SQL upgrade (${version}): ${systemUpgrade.description}`);
                    systemUpgrade.version = version;
                    await SystemSqlUpgrade.executeSystemUpgrade(application, systemUpgrade, {
                        logger,
                    });
                });
            },
            () => {
                return {
                    sqlFile: sqlFileRelativeFilename,
                    actionType: 'system_upgrade',
                    actionArgs: { version },
                };
            },
        );
    }

    /**
     * Execute a set of 'reload_setup_data' actions to reload the setup data for factories
     *
     * @param context
     * @param sqlFileInfoRelativeFilename the (relative) filename of the SQL file that contains the reload actions to execute
     * @param setupDataToReload the setup data to reload (they were stored in the SQL file)
     * @param allTenantIds
     * @param reloadActions the 'reload_setup_data' that describes the factory we want to reload setup data for
     * @param performanceMonitor the monitor to log performances
     */
    private static async _reloadSetupData(
        context: Context,
        sqlFileRelativeFilename: string,
        setupDataToReload: SqlFileDataSets,
        allTenantIds: string[],
        factoryNames: string[],
        performanceMonitor: PerformanceMonitor,
    ): Promise<void> {
        const factories = [] as NodeFactory[];
        const data = factoryNames.reduce((r, factoryName) => {
            const sqlData = setupDataToReload[factoryName];
            sqlData.metadata.name = factoryName;
            const columnNames = sqlData.metadata.columns.map(column => column.name);
            const dataForFactory = sqlData.rows.reduce((total, values) => {
                const row: SqlFileDataRow = {};
                columnNames.forEach((columnName, idx) => {
                    row[columnName] = values[idx]?.type === 'Buffer' ? Buffer.from(values[idx].data) : values[idx];
                });
                total.push(row);
                return total;
            }, []) as SqlFileDataRow[];

            factories.push(context.application.getFactoryByName(factoryName));
            r[factoryName] = {
                metadata: sqlData.metadata,
                rows: dataForFactory,
            };
            return r;
        }, {} as SqlFileDataSets);

        await asyncArray(allTenantIds).forEach(async tenantId => {
            logger.info(`Reload data for factories ${Object.keys(data).join()} on tenant ${tenantId}`);
            await performanceMonitor.monitor(
                async () => {
                    await TenantService.reloadSetupData(context.application, tenantId, factories, {
                        data,
                    });
                },
                () => {
                    return {
                        sqlFile: sqlFileRelativeFilename,
                        actionType: 'reload_setup_data',
                        actionArgs: {},
                        factories: factories.map(f => f.name),
                        tenantId,
                    };
                },
            );
        });
    }

    /**
     * Reload setup data for a set of factories
     * @param context
     * @param sqlFileRelativeFilename
     * @param setupDataToReload all the up-to-date data to be reloaded
     * @param allTenantIds the list of all the tenantIds
     * @param factoryNamesToReload the names of the factories to be reloaded
     * @param performanceMonitor the performance monitor
     * @returns
     */
    private static async _reloadSetupDataForFactories(
        upgradeContext: UpgradeContext,
        context: Context,
        sqlFileRelativeFilename: string,
        setupDataToReload: SqlFileDataSets,
        allTenantIds: string[],
        factoryNamesToReload: string[],
        performanceMonitor: PerformanceMonitor,
    ): Promise<void> {
        logger.info(`Reloading setup data for ${factoryNamesToReload}`);

        const tenantsToFilter = upgradeContext.options.clusterName
            ? await TenantService.getTenantIdsToSkipForSetupData(
                  context.application,
                  upgradeContext.options.clusterName,
              )
            : [];

        if (tenantsToFilter.length > 0) {
            logger.warn(
                `${tenantsToFilter.length} out of ${allTenantIds.length} tenants will be skipped, based on the cluster name ${upgradeContext.options.clusterName}`,
            );
        }

        const filteredTenantIds =
            tenantsToFilter.length === 0 ? allTenantIds : allTenantIds.filter(t => !tenantsToFilter.includes(t));
        // Factories for which data are available
        const allFactoriesWithData = Object.keys(setupDataToReload).reduce((total, factoryName) => {
            const factory = context.application.tryToGetFactoryByName(factoryName);
            if (factory == null) {
                // Note: It could happen that the factory could not be resolved
                // For instance, we are in version 36.0.33 and we are replaying the 36.0.12-36.0.13 sql file that contains
                // a reload action for factory F but factory F was dropped in version 36.0.17.
                logger.warn(`\t- factory ${factoryName} will not be reloaded (no longer exists in the model)`);
                return total;
            }
            total[factoryName] = factory;
            return total;
        }, {} as Dict<NodeFactory>);

        // Compute the sorted list (by dependencies) of all the factories that need to be reloaded
        // Example :
        // - there are data for A, B and C in setupDataToReload
        // - B depends on A
        // - factoryNamesToReload=['B']
        // we have to re-load A before loading B (to make sure the references will be OK)
        // what we want at the end is sortedFactoriesToReload=['A', 'B']
        const sortedFactoriesToReload: string[] = [];
        factoryNamesToReload.forEach(factoryNameToReload => {
            const factory = allFactoriesWithData[factoryNameToReload];
            if (factory == null) {
                // Either no data for this factory or the factory no longer exists
                return;
            }
            if (sortedFactoriesToReload.includes(factory.name)) {
                // This factory was already processed as a dependency of a previous factory in the list
                return;
            }
            sortedFactoriesToReload.push(factory.name);
            // Get all the dependencies of the factory (in our example, getFactoryDependsOn(A) will return ['B'])
            const dependsOn = getFactoryDependsOn(factory, Object.values(allFactoriesWithData));
            dependsOn.forEach(factoryName => {
                if (setupDataToReload[factoryName] == null) {
                    // We don't have any data for this factory
                    // This is not an error, getFactoryDependsOn will return all the dependsOn of the factory
                    return;
                }
                if (!sortedFactoriesToReload.includes(factoryName)) sortedFactoriesToReload.push(factoryName);
            });
        });

        if (sortedFactoriesToReload.length === 0) {
            logger.info(`Reload of factories ${factoryNamesToReload} skipped, already in sync`);
            return;
        }

        if (sortedFactoriesToReload.length > 1) {
            // Get the names of all the factories of the application (sorted by dependencies)
            const applicationSortedFactoryNames = context.application.getAllSortedFactories().map(f => f.name);
            // Sort the factories to reload, according to their order in the application factories
            sortedFactoriesToReload.sort(
                (factoryName1: string, factoryName2: string) =>
                    applicationSortedFactoryNames.indexOf(factoryName1) -
                    applicationSortedFactoryNames.indexOf(factoryName2),
            );
        }

        logger.info(`Reloading setup data for factories = ${sortedFactoriesToReload}`);

        await UpgradeCommandsRecorder._reloadSetupData(
            context,
            sqlFileRelativeFilename,
            setupDataToReload,
            filteredTenantIds,
            sortedFactoriesToReload,
            performanceMonitor,
        );
        // Un-reference the re-loaded factories
        sortedFactoriesToReload.forEach(factoryName => {
            delete setupDataToReload[factoryName];
        });
    }

    /**
     * Replay the recorded commands (SQL queries/actions) required to move from the version in db to
     * the current version (from the package.json)
     */
    static async replayRecordedCommands(
        upgradeContext: UpgradeContext,
        recorder?: UpgradeCommandsRecorder,
    ): Promise<void> {
        const currentDbVersion =
            (await upgradeContext.packageManager.getCurrentVersion(upgradeContext.application.mainPackage)) || '0.0.0';
        const packageVersion = upgradeContext.application.mainPackage.packageJson.version;
        if (currentDbVersion === packageVersion) {
            logger.info(
                `No need to replay any SQL, ${upgradeContext.application.mainPackage.name}@${currentDbVersion} is already up-to-date`,
            );
            return;
        }

        const infos = await UpgradeCommandsRecorder.getFilenamesToReplay(upgradeContext);
        if (infos.length === 0) {
            logger.info(
                `Note: could not find any SQL files to replay in the folder ${upgradeContext.sqlRecordsFolder}`,
            );
            return;
        }

        const { packages, packageManager } = upgradeContext;
        // Keep track of the current version of every package (they will probably be updated byt the SQL scripts)
        const oldVersions = await asyncArray(packages).reduce(async (total, pack) => {
            total[pack.name] = (await packageManager.getCurrentVersion(pack)) || '0.0.0';
            return total;
        }, {} as Dict<string>);

        logger.info(`Replaying SQL files ${infos[0].fromVersion} -> ${infos[infos.length - 1].toVersion}`);

        const performanceMonitor = new PerformanceMonitor(
            currentDbVersion,
            packageVersion,
            upgradeContext.options.metrics,
        );

        // Data for factories to reload (indexed by factoryName)
        const setupDataToReload: Dict<SqlFileDataSet> = {};

        let lastSuccessfulVersion: string | undefined;
        await asyncArray(infos).forEach(async sqlFileInfo => {
            lastSuccessfulVersion = await UpgradeCommandsRecorder._replaySqlFile(
                upgradeContext,
                sqlFileInfo,
                setupDataToReload,
                performanceMonitor,
            );
            // Reset the cache after every SQL file, to be safe.
            // This fixes a caching bug on the User node, after the conversion of cyrb53 ids to normal ids.
            // TODO: improve this. Maybe a custom upgrade action to invalidate the cache only when necessary.
            upgradeContext.application.clearGlobalCache();
        });

        // The metadata must be up-to-date before reloading any setup data
        // For instance SysJobSchedules refer to MetaNodeOperations
        await CoreHooks.metadataManager.upgradeMetadata(upgradeContext.application);

        Object.entries(setupDataToReload).forEach(([factoryName, dataSet]) => {
            dataSet.metadata.name = factoryName;
        });

        // Make sure all the service options are existing in db before reloading the setup data
        // (some setup data - sysServiceOptionState for instance - may refer to service options)
        await UpgradeSqlSchema.createOrUpdateServiceOptions(upgradeContext);

        // Re-load all the remaining setup data
        if (Object.keys(setupDataToReload).length > 0) {
            await upgradeContext.application.asRoot.withCommittedContext(
                null,
                async context => {
                    const allTenantIds = await Context.tenantManager.listTenantsIds(context);
                    await UpgradeCommandsRecorder._reloadSetupDataForFactories(
                        upgradeContext,
                        context,
                        'final reload of remaining factories',
                        setupDataToReload,
                        allTenantIds,
                        Object.keys(setupDataToReload),
                        performanceMonitor,
                    );
                },
                { description: () => `Final reload of setup data ${Object.keys(setupDataToReload).join()}` },
            );
        }
        await performanceMonitor.dispose();

        if (lastSuccessfulVersion) {
            const version = lastSuccessfulVersion;
            await upgradeContext.application.asRoot.withCommittedContext(
                // Note : this tenantId won't be used as the SQL files contains markers that
                // stand for the tenant and will be replaced
                null,
                async context => {
                    await asyncArray(packages).forEach(pack =>
                        packageManager.setCurrentVersion(context, pack, version),
                    );
                },
            );
            if (recorder) {
                // Refresh the 'fromVersion' of the recorder
                // If the upgrade had to manage version 24.0.4 to 24.0.9
                // and some SQL files were replayed from 24.0.4 to 24.0.7
                // then the file to create will be xxxx-24.0.8-24.0.9
                recorder._fromVersion = lastSuccessfulVersion;
            }
            upgradeContext.initialVersion = lastSuccessfulVersion;
        }

        logger.info(() => 'All the packages were bumped from SQL files:');
        await asyncArray(packages).forEach(
            async p =>
                // eslint-disable-next-line no-void
                void (await logger.infoAsync(
                    async () => `\t- ${p.name}@${oldVersions[p.name]} -> ${await packageManager.getCurrentVersion(p)}`,
                )),
        );
    }

    /**
     * Replays a SQL file
     */
    private static async _replaySqlFile(
        upgradeContext: UpgradeContext,
        sqlFileInfo: SqlFileInfo,
        setupDataToReload: Dict<SqlFileDataSet>,
        performanceMonitor: PerformanceMonitor,
    ): Promise<string> {
        const loadSqlFile = (sqlFilename: string): UpgradeRecordedSqlFile => {
            const regEx = new RegExp(UpgradeCommandsRecorder._schemaMarker, 'g');
            const schemaName = upgradeContext.application.schemaName;
            const content = fs.readFileSync(fsp.join(upgradeContext.sqlRecordsFolder, sqlFilename)).toString();
            // in the SQL file, all the schema names are replaced with a marker (UpgradeCommandsRecorder._schemaMarker)
            // We have to replace this marker with the current schema name
            return JSON.parse(content.replace(regEx, schemaName)) as UpgradeRecordedSqlFile;
        };

        // Warning: keep the call listTenantsIds outside of the next committed context
        // otherwise, the connection will block while replaying SQL commands
        const allTenantIds = await upgradeContext.application.asRoot.withUncommittedContext(null, context =>
            Context.tenantManager.listTenantsIds(context),
        );

        await upgradeContext.application.asRoot.withCommittedContext(
            // Note : this tenantId won't be used as the SQL files contains markers that
            // stand for the tenant and will be replaced
            null,
            async context => {
                // Note : the list of tenants must be recomputed for every SQL file (the previous SQL file may have updated the tenants)
                // For instance, in the past, we added a command in a SQL file to drop the '77.77' tenants
                logger.info(`\t- replaying SQL file ${sqlFileInfo.relativeFilename}`);
                const sqlFile = loadSqlFile(sqlFileInfo.relativeFilename);
                const commands = sqlFile.commands;

                let lastActionDescription: string | undefined;

                await asyncArray(commands).forEach(async command => {
                    let factoriesToReloadNow: string[] = [];
                    let skipAction = false;

                    if (UpgradeCommandsRecorder._isRecordedAction(command)) {
                        if (command.action === 'reload_setup_data') {
                            if (command.args?.factory == null) {
                                throw new Error("'factory' argument is missing for 'reload_setup_data' action");
                            }
                            // This setup data for this factory will only be reloaded when required
                            const factoryName = command.args.factory;
                            // Here, 2 cases:
                            // - it can be the first reload action for this factory : we store the data to reload
                            // - we already had an action for this factory : we replace the old values to reload with the new ones
                            const dataForFactory = sqlFile.data[factoryName];
                            if (dataForFactory == null) {
                                throw new Error(
                                    `Data are missing for factory ${factoryName} in the SQL file ${sqlFileInfo.relativeFilename}`,
                                );
                            }
                            setupDataToReload[factoryName] = dataForFactory;
                            if (command.immediate) {
                                // This reload action cannot be executed later
                                factoriesToReloadNow.push(factoryName);
                            } else {
                                logger.info(`\t\t- deferring reload data for factory ${factoryName}`);
                            }
                            // Do not execute the reload action now: it has alread been:
                            // - either queued if immediate is false
                            // - registered for immediate execution (through factoriesToReloadNow)
                            skipAction = true;
                        }
                    }

                    if (command.dependsOnSetupData != null) {
                        factoriesToReloadNow = [...factoriesToReloadNow, ...command.dependsOnSetupData];
                    }

                    if (factoriesToReloadNow.length > 0) {
                        // The metadata must be up-to-date before reloading any setup data
                        // For instance SysJobSchedules refer to MetaNodeOperations
                        await CoreHooks.metadataManager.upgradeMetadata(upgradeContext.application);
                        // Some (setup data) factories MUST BE reloaded now, so that the command succeeds
                        await UpgradeCommandsRecorder._reloadSetupDataForFactories(
                            upgradeContext,
                            context,
                            'dependency of action',
                            setupDataToReload,
                            allTenantIds,
                            factoriesToReloadNow,
                            performanceMonitor,
                        );
                    }

                    if (skipAction) {
                        // Do not execute the action now (it has been queued and will be executed when required)
                        return;
                    }

                    if (UpgradeCommandsRecorder._isRecordedSqlQuery(command)) {
                        const actionDescription = command.actionDescription;
                        if (actionDescription !== lastActionDescription) {
                            lastActionDescription = actionDescription;
                            if (command.actionDescription) {
                                logger.info(`\t\t- replaying action '${command.actionDescription}'`);
                            }
                        }
                        await UpgradeCommandsRecorder._executeRecordedSqlQuery(
                            context,
                            command,
                            sqlFileInfo.relativeFilename,
                            performanceMonitor,
                        );
                    } else if (UpgradeCommandsRecorder._isRecordedAction(command)) {
                        await UpgradeCommandsRecorder._executeRecordedAction(
                            context.application,
                            command,
                            sqlFileInfo.relativeFilename,
                            performanceMonitor,
                        );
                    } else {
                        throw new Error(
                            `Could not replay command (undetermined type) ${JSON.stringify(command, null, '\t')}`,
                        );
                    }
                });

                logger.info(() => `\t- replayed ${sqlFileInfo.relativeFilename}: ${commands.length} commands`);
            },
            { description: () => `replay SQL file: ${sqlFileInfo.relativeFilename}` },
        );
        return sqlFileInfo.toVersion;
    }
}

/**
 * An entry line that will be logged by the performance monitor
 */
type PerformanceMonitorEntry = {
    sqlStatement?: string;
    // Only valid when sqlStatement is set: was the sqlStatement executed on a system (or classic) pool ?
    usesSysPool?: boolean;
    // Mandatory valid when sqlStatement is not set
    actionType?: SqlRecorderActionType;
    // Only valid when actionType is set
    actionArgs?: Dict<string>;
    /**
     * The tenant concerned by this entry
     */
    tenantId?: string;
    /**
     * The name of the factories concerned by this entry
     * only valid when actionType === 'reload_setup_data'
     */
    factories?: string[];
    /**
     * The name of the SQL file that contains the command
     */
    sqlFile: string;
};

class PerformanceMonitor {
    private readonly _localRun: boolean = true;

    private readonly _filename: string;

    private readonly _entries: {
        duration: {
            /**
             * Total duration of the whole upgrade (ms)
             */
            total: number;
            /**
             * Total duration spent reloading setup data (ms)
             */
            reloadSetupData: number;
            /**
             * Total duration spent executing system upgrades (ms)
             */
            systemUpgrades: number;
            /**
             * Total duration spent upgrading the metadata (ms)
             */
            metadataUpgrade: number;
            /**
             * Total duration spent executing SQL queries (ms)
             */
            sql: number;
        };
        entries: (PerformanceMonitorEntry & { startTime: string; duration: number })[];
    } = {
        duration: {
            total: 0,
            reloadSetupData: 0,
            systemUpgrades: 0,
            metadataUpgrade: 0,
            sql: 0,
        },
        entries: [],
    };

    constructor(
        fromVersion: string,
        toVersion: string,
        readonly metrics?: UpgradeMetricsType,
    ) {
        if (metrics == null) return;
        const clusterId = ConfigManager.current.clusterId;
        this._localRun = clusterId == null;
        this._filename = `upgrade-metrics-${
            this._localRun ? 'local' : clusterId
        }-${fromVersion}-${toVersion}-${new Date().toISOString().replace(/[:.]/g, '_')}`;
    }

    private static async _uploadFile(fullPath: string, s3Key: string): Promise<void> {
        const s3Bucket = ConfigManager.current.s3Storage?.s3ClusterBucket;
        if (s3Bucket == null) {
            logger.warn("The config is missing 's3Storage.s3ClusterBucket' parameter, could not upload metrics to S3");
            return;
        }
        const objInfo: S3ObjectInfo = {
            bucketName: s3Bucket,
            folder: ConfigManager.current.s3Storage?.s3UpgradeMetricsFolder || 'upgradeMetrics',
            key: s3Key,
        };
        const s3Uri = S3Helper.buildS3Uri(objInfo);
        logger.info(`Uploading upgrade metrics to ${s3Uri}`);
        await S3Helper.upload(fullPath, objInfo);
        logger.info(`The metrics file has been uploaded to ${s3Uri}`);
    }

    private async _writeCsvFile(folder: string): Promise<void> {
        const separator = '\t';
        const filename = `${this._filename}.csv`;
        const fullPath = fsp.join(folder, filename);

        const stream = fs.createWriteStream(fullPath);
        // Headers:
        // - start time
        // - duration
        // - sqlFile
        // - command type : sql / action
        // - sql pool (classic / system) (when entry type is 'sql')
        // - action type (when entry type is 'action')
        // - action arguments (when entry type is 'action')
        // - tenants
        // - factories
        // - sql statement (when entry type is 'sql')
        stream.write(`start_time${separator}`);
        stream.write(`duration_ms${separator}`);
        stream.write(`sql_file${separator}`);
        stream.write(`command_type${separator}`);
        stream.write(`sql_pool${separator}`);
        stream.write(`action_type${separator}`);
        stream.write(`action_arguments${separator}`);
        stream.write(`tenants${separator}`);
        stream.write(`factories${separator}`);
        stream.write('sql_statement');
        stream.write('\n');

        this._entries.entries.forEach(entry => {
            stream.write(`${entry.startTime}${separator}`);
            stream.write(`${entry.duration}${separator}`);
            stream.write(`${entry.sqlFile}${separator}`);
            stream.write(`${entry.sqlStatement != null ? 'sql' : 'action'}${separator}`);
            if (entry.usesSysPool == null) {
                stream.write(separator);
            } else {
                stream.write(`${entry.usesSysPool ? 'system' : 'classic'}${separator}`);
            }
            stream.write(`${entry.actionType ?? ''}${separator}`);
            stream.write(`${entry.actionArgs == null ? '' : JSON.stringify(entry.actionArgs)}${separator}`);
            stream.write(`${entry.tenantId ?? ''}${separator}`);
            stream.write(`${entry.factories || ''}${separator}`);
            stream.write(`${entry.sqlStatement || ''}`);
            stream.write('\n');
        });
        await new Promise<void>(resolve => {
            stream.end(() => resolve());
        });
        logger.info(`The metrics file was written to ${fullPath}`);
        if (this.metrics === 's3') {
            await PerformanceMonitor._uploadFile(fullPath, filename);
        }
    }

    private async _writeJsonFile(folder: string): Promise<void> {
        const filename = `${this._filename}.json`;
        const fullPath = fsp.join(folder, filename);
        fs.writeFileSync(fullPath, JSON.stringify(this._entries, null, '\t'));
        logger.info(`The metrics file was written to ${fullPath}`);
        if (this.metrics === 's3') {
            await PerformanceMonitor._uploadFile(fullPath, filename);
        }
    }

    async dispose(): Promise<void> {
        if (this.metrics == null) return;
        // store the CSV/JSON file at the root of the repo
        const folder = fsp.join(__dirname, '../../../../../../../');

        await this._writeCsvFile(folder);
        await this._writeJsonFile(folder);
    }

    private static _checkEntry(content: PerformanceMonitorEntry): void {
        if (content.sqlFile == null) throw new Error('"SqlFile" is missing');
        if (content.sqlStatement != null) {
            if (content.usesSysPool == null) throw new Error('"usesSysPool" is missing');
            if (content.actionType != null) throw new Error('unexpected "actionType" argument');
            if (content.actionArgs != null) throw new Error('unexpected "actionArgs" argument');
            if (content.factories != null) throw new Error('unexpected "factories" argument');
            return;
        }
        if (content.usesSysPool != null) throw new Error('unexpected "usesSysPool" argument');
        if (content.actionType == null) throw new Error('"actionType" is missing');
        if (content.actionArgs == null) throw new Error('"actionArgs" is missing');
        if (content.tenantId != null) {
            if (content.factories == null) throw new Error('"tenantId" is missing');
        }
    }

    async monitor(body: () => Promise<void>, contentProvider: () => PerformanceMonitorEntry): Promise<void> {
        const dt0 = Date.now();
        await body();
        const dt1 = Date.now();
        const entry = contentProvider();
        PerformanceMonitor._checkEntry(entry);
        const duration = dt1 - dt0;
        this._entries.duration.total += duration;
        switch (entry.actionType) {
            case 'system_upgrade':
                this._entries.duration.systemUpgrades += duration;
                break;
            case 'reload_setup_data':
                this._entries.duration.reloadSetupData += duration;
                this._entries.entries.push({
                    ...entry,
                    ...{
                        startTime: new Date(dt0).toISOString(),
                        duration,
                        sqlStatement: `${
                            entry.sqlStatement == null
                                ? ''
                                : entry.sqlStatement.replace(/\t/g, '  ').replace(/\n/g, '\\n')
                        }`,
                    },
                });
                break;
            case 'metadata_upgrade':
                this._entries.duration.metadataUpgrade += duration;
                break;
            default:
                this._entries.duration.sql += duration;
                break;
        }
    }
}
