import type { UpgradeOptions } from '@sage/xtrem-cli';
import {
    AnyValue,
    Application,
    asyncArray,
    AsyncResponse,
    ConfigManager,
    Context,
    ContextOptions,
    DatabaseManager,
    ModifyTableSqlContext,
    NodeFactory,
    Package,
    readTableSchemas,
    readTablesForSchema,
    SchemaSqlContext,
} from '@sage/xtrem-core';
import { ConnectionPool, DatabaseService, SqlObjectJsonComment, TableDefinition } from '@sage/xtrem-postgres';
import { Dict, LogicError, pascalCase } from '@sage/xtrem-shared';
import * as fs from 'fs';
import * as lodash from 'lodash';
import * as fsp from 'path';
import * as semver from 'semver';
import { loggers } from '../loggers';
import { SysPackageManager } from '../sys-package-manager';
import { UpgradeAction } from './actions';
import { GitWrapper } from './git-wrapper';
import { CommandsRecorder, UpgradeCommandsRecorder } from './upgrade-commands-recorder';
import { ActionAndVersion, UpgradePackContext } from './upgrade-pack-context';

const logger = loggers.upgrade;

interface UpgradeContextOptions extends ContextOptions {
    tenantId?: string;
}

// @internal
export type SchemaChangesRestrictionType =
    /**
     * No restriction: schema changes are allowed
     */
    | 'none'
    /**
     * Schema changes are forbidden: the main package of the application is released
     */
    | 'released'
    /**
     * Schema changes are forbidden: we are checking the SQL schema consitency
     */
    | 'checkSchema';

/**
 * The context of an upgrade
 *
 * @internal
 */
export class UpgradeContext {
    private readonly _packContextsByPackName: Dict<UpgradePackContext> = {};

    private _sqlCommandsRecorder?: UpgradeCommandsRecorder;

    private _sysSchemaSqlContext?: SchemaSqlContext;

    private _tableDefinitions: Dict<TableDefinition> | undefined;

    private _packages: Package[] | undefined;

    /**
     * The version of the main package BEFORE the upgrade
     */
    private _initialVersion: string;

    /**
     * The restriction applied to schema changes
     */
    private readonly _schemaChangesRestriction: SchemaChangesRestrictionType;

    readonly issues: {
        message: string;
        severity: 'warning' | 'error';
    }[] = [];

    /**
     * The list of factories to be upgraded
     */
    readonly factoriesToUpgrade: NodeFactory[];

    /**
     * The list of setup data that were referenced by the dependsOnSetupData of an action
     * List of actions, indexed by the factoryName
     */
    private _setupDataReferencedByActions: Dict<UpgradeAction[]> = {};

    /**
     *
     * @param application
     * @param upgradeMode replayOnly / replayAndUpgrade / replayAndRecord / upgradeOnly
     */
    private constructor(
        readonly application: Application,
        readonly options: UpgradeOptions,
    ) {
        this.factoriesToUpgrade = application.getSqlPackageFactories();
        // We have to store the current version of the main package before it's upgraded (it will be updated by the upgrade process)
        if (options.checkSchema) {
            this._schemaChangesRestriction = 'checkSchema';
        } else if (application.mainPackage.isReleased) {
            this._schemaChangesRestriction = 'released';
        } else {
            this._schemaChangesRestriction = 'none';
        }
        if (
            options.checkSchema || // Only check, no vaccum needed
            application.applicationType === 'test' ||
            options.mode !== 'replayOnly' // // we are only interested in the vacuum when we are upgrading prod environments
        ) {
            options.skipVacuum = true;
        }
    }

    private async init(): Promise<void> {
        try {
            this._initialVersion =
                (await this.packageManager.getCurrentVersion(this.application.mainPackage)) || '0.0.0';
        } catch (err) {
            // getCurrentVersion might fail if the sysPackVersion table does not exist yet
            this._initialVersion = '0.0.0';
        }
    }

    static async create(application: Application, options: UpgradeOptions): Promise<UpgradeContext> {
        const upgradeContext = new UpgradeContext(application, options);
        await upgradeContext.init();
        return upgradeContext;
    }

    /**
     * Registers an action with a 'dependsOnSetupData' set
     */
    registerActionWithDependsOnSetupData(action: UpgradeAction): void {
        if (!action.dependsOnSetupData) return;
        action.dependsOnSetupData.forEach(factoryName => {
            let actions = this._setupDataReferencedByActions[factoryName];
            if (actions == null) {
                actions = [];
                this._setupDataReferencedByActions[factoryName] = actions;
            }
            actions.push(action);
        });
    }

    /**
     * Returns the list of actions that depend on setup data
     */
    get actionsDependingOnSetupData(): Dict<UpgradeAction[]> {
        return this._setupDataReferencedByActions;
    }

    /**
     * The version of the main package BEFORE the upgrade
     */
    get initialVersion(): string {
        return this._initialVersion;
    }

    /**
     * Set the version that will be used as a start version for the upgrade engine
     * @internal
     */
    set initialVersion(value: string) {
        this._initialVersion = value;
    }

    /**
     * Returns the actual restriction on schema changes
     */
    public get schemaChangesRestriction(): SchemaChangesRestrictionType {
        return this._schemaChangesRestriction;
    }

    /**
     * Formats a error message, according to the current restrictions on schema changes
     */
    async getSchemaChangeViolationError(message: string, packContext?: UpgradePackContext): Promise<string> {
        switch (this.schemaChangesRestriction) {
            case 'released':
                if (packContext)
                    return `Package ${packContext.pack.packageName}@${(await packContext.versions).sysPackVersion} is released. ${message || ''}`;
                return `Application ${this.application.mainPackage.name} is released. ${message || ''}`;

            case 'checkSchema':
                return `The schema is being checked. ${message || ''}`;
            default:
                throw new LogicError('No restriction on schema changes are currently applied');
        }
    }

    /**
     * Raise an error if the schema changes are forbidden
     * @returns true if an error was detected
     */
    public async raiseErrorIfSchemaChangesAreForbidden(
        message: string,
        packContext?: UpgradePackContext,
    ): Promise<boolean> {
        switch (this.schemaChangesRestriction) {
            case 'released':
            case 'checkSchema':
                this.issues.push({
                    message: await this.getSchemaChangeViolationError(message, packContext),
                    severity: 'error',
                });
                return true;
            default:
                return false;
        }
    }

    /**
     * Clears the cache of table definitions (will be invoked between the pre and the post upgrade steps)
     */
    clearTableDefinitionsCache(): void {
        this._tableDefinitions = undefined;
    }

    /**
     * Clears the cache of table definitions (will be invoked between the pre and the post upgrade steps)
     */
    resetFactoryChanges(): void {
        this.clearTableDefinitionsCache();
        this.packages.forEach(pack => {
            this.getPackContext(pack).resetFactoryChanges();
        });
    }

    /**
     * Returns the package manager as a SysPackageManager
     */
    get packageManager(): SysPackageManager {
        return SysPackageManager.fromApplication(this.application);
    }

    /**
     * Returns the list of packages to upgrade
     */
    get packages(): Package[] {
        if (this._packages == null) {
            this._packages = this.application.getPackages();
        }
        return this._packages;
    }

    /**
     * Returns all the actions that must be executed within all packages
     */
    async getAllActionsToExecute(): Promise<ActionAndVersion[]> {
        const allActions: ActionAndVersion[] = [];
        await asyncArray(this.packages).forEach(async pack => {
            const packContext = this.getPackContext(pack);
            (await packContext.getActionsToExecute()).forEach(actionAndVersion => {
                allActions.push(actionAndVersion);
            });
        });
        return allActions;
    }

    /**
     * Returns all the actions related to a factory (will look from factory' package and from packages of extensions as well).
     */
    async getAllActionsForFactory(factory: NodeFactory): Promise<ActionAndVersion[]> {
        const actions: ActionAndVersion[] = [];
        const packagesToCheck = lodash.uniq([factory.package, ...UpgradeContext._getAllExtendingPackages(factory)]);
        await asyncArray(packagesToCheck).forEach(async pack => {
            const packContext = this.getPackContext(pack);
            (await packContext.getActionsToExecute(factory)).forEach(av => actions.push(av));
        });
        return lodash.uniq(actions);
    }

    /**
     * Returns all the package that define an extension of a given factory
     */
    private static _getAllExtendingPackages(factory: NodeFactory): Package[] {
        if (!factory.extensions) return [];
        return lodash.uniq(factory.extensions.map(ext => NodeFactory.getExtensionPackage(ext)));
    }

    /**
     * Returns the name of the tables that exist in the database but do not have
     * any factory declared in the application.
     */
    async getTablesInSchemaWithoutFactory(context: Context): Promise<string[]> {
        const tablesInSchema = await readTablesForSchema(context);
        return tablesInSchema.filter(tableName => !this.application.tryGetFactoryByTableName(tableName));
    }

    /**
     * Verify if the tables in the schema has a corresponding factory
     * @param context
     */
    private async _verifyTablesInSchema(context: Context): Promise<void> {
        logger.info('Verifying tables in schema vs. factories in application');
        const tablesWithoutFactories = await this.getTablesInSchemaWithoutFactory(context);

        if (tablesWithoutFactories.length > 0) {
            throw new Error(
                `The following tables exist in the database schema, but do not exist in the application:${tablesWithoutFactories.join()}. A SchemaDropTableAction or SchemaRenameNodeAction is probably missing.`,
            );
        }
    }

    /**
     * Ensures the schema is in sync with the node decorators.
     * This may be usefull when we are upgrading in prod mode an invalid database where some
     * objects (columns, foreignKeys, ...) are missing (In prod mode, we are only replaying SQL files)
     */
    async ensureSchemaIsOk(): Promise<void> {
        this.clearTableDefinitionsCache();
        await this.withCommittedContext(async context => {
            await this._fixForeignKeys(context, { dontCreateMissingFks: false });

            // Verify schema tables only if we recording and the application is not released
            if (this.options.mode === 'replayAndRecord' && !this.application.mainPackage.isReleased)
                await this._verifyTablesInSchema(context);
        });
    }

    /**
     * Ensure that all the foreign keys defined in the model exist in the database and that they have the right name
     */
    private async _fixForeignKeys(context: Context, options: { dontCreateMissingFks: boolean }): Promise<void> {
        logger.info('Fix foreign keys');
        await asyncArray(this.packages).forEach(pack =>
            asyncArray(pack.factories.filter(f => f.storage === 'sql')).forEach(async f => {
                const currentTableDef = await this.getTableDefinition(context, f.requiredTableName);
                if (currentTableDef) {
                    await f.table.fixForeignKeys(context, currentTableDef, options);
                }
            }),
        );
    }

    /**
     * Read the table definition for a set of tables
     * @param context
     * @param tableNames [] to read the table definitions for all the tables
     * @returns
     */
    private static _readTableDefinitions(
        context: Context | null,
        tableNames: string[],
    ): Promise<Dict<TableDefinition>> {
        if (context == null) throw new Error('No context provided');
        return readTableSchemas(context, tableNames, {
            skipSecurity: true,
            skipSequences: true,
            includes: ['triggers'],
            getComments: true,
        });
    }

    /**
     * Returns the table definition of a given table (from its name).
     * CAUTION: this function uses a cache and may not reflect the exact schema. It will return the table
     * definition of the table as it was the first time it was queried.
     * @param context
     * @param tableName
     * @returns
     */
    async getTableDefinition(context: Context | null, tableName: string): Promise<TableDefinition> {
        if (!this._tableDefinitions) {
            this._tableDefinitions = await UpgradeContext._readTableDefinitions(context, []);
        }
        const tableDef = this._tableDefinitions[tableName];
        if (tableDef == null) {
            // Will happen when the table is new (a factory exists but its table does not exist -yet- in db)
            return {
                schemaName: this.application.schemaName,
                tableName,
                columns: [],
                foreignKeys: [],
                indexes: [],
                triggers: [],
            };
        }
        return tableDef;
    }

    /**
     * Reload the table definition of a table (and cache it). Can be used to refresh the cache when a modification
     * was made to the schema.
     */
    async reloadTableDefinition(context: Context, tableName: string): Promise<TableDefinition> {
        if (!this._tableDefinitions) {
            // The cache was not initialized yet
            return this.getTableDefinition(context, tableName);
        }
        const tableDef = (await UpgradeContext._readTableDefinitions(context, [tableName]))[tableName];
        this._tableDefinitions[tableName] = tableDef;
        return tableDef;
    }

    /**
     * Write the comments on all SQL objects (tables, columns, ...)
     * @param factories the factories to write the comments for
     * @param schemaWasUpdated was the schema already updated ?
     * @param newTableNames when schemaWasUpdated is false, the list of table renamings that will be processed by the schema update
     */
    async writeCommentsOnSqlObjects(
        factories: NodeFactory[],
        schemaWasUpdated: boolean,
        newTableNames?: Dict<string>,
    ): Promise<void> {
        // It's very long to run in slow mode. So, let's first try in fast mode
        // If we are luck, we will save a lot of time (about 30 s) - we will be luck if we run
        // against a database where the upgrade does not have anything to add
        const computeNewTableNamesToUse = async (): Promise<Dict<string>> => {
            if (schemaWasUpdated) return {};
            // The schema has not been updated yet, so tables were not renamed yet.
            // For every table that will be renamed, the comment must be set to the old table name (not the one
            // that matches factory.tableName as it does not exist yet).
            const mappings: Dict<string> = {};
            await this.createContextForDdl(
                context =>
                    asyncArray(this.packages).forEach(async pack => {
                        const allPackChanges = await this.getPackContext(pack).getAllFactoryChanges(context);
                        Object.keys(allPackChanges).forEach(factoryName => {
                            const change = allPackChanges[factoryName];
                            if (change.renameTable) {
                                mappings[change.renameTable.to] = change.renameTable.from;
                            }
                        });
                    }),
                { description: () => 'writeCommentsOnSqlObjects.getAllFactoryChanges' },
            );
            return mappings;
        };

        const newTableNamesToUse = newTableNames || (await computeNewTableNamesToUse());

        // Make sure the table definitions are loaded
        await this.withUncommittedContext(context => this.getTableDefinition(context, 'dummy'));

        if (factories.length === 1) {
            logger.info(`Writing comments for table '${this.application.schemaName}.${factories[0].tableName}`);
        } else {
            logger.info(`Writing comments for ALL the SQL objects on schema ${this.application.schemaName}`);
        }

        try {
            // It's very long to run in slow mode. So, let's first try in fast mode
            // approx duration: in fast mode : 160ms to write 4723 comments (366 tables / 4357 columns)
            // in slow mode, it's much longer (about 30s) as we execute the queries one by one but it will only happen once.
            // If we are luck, we will save a lot of time (about 30 s) - we will be luck if we run
            // against a database where the upgrade does not have anything to add
            await this._writeCommentsOnSqlObjects(factories, false, newTableNamesToUse);
            return;
        } catch (err) {
            logger.info('Writing comments in fast mode failed, retry with slow mode');
        }

        // Bad luck, we have to run it in slow mode
        await this._writeCommentsOnSqlObjects(factories, true, newTableNamesToUse);
    }

    /**
     * Write the comments on all SQL objects (tables, columns, ...)
     * The slowerButSaferMode is very slow but much safer as if a comment can't be writter, the others will still be written
     * @param slowerButSaferMode Should all the comments be written one by one (true) or in a single request (false).
     */
    private async _writeCommentsOnSqlObjects(
        factories: NodeFactory[],
        slowerButSaferMode: boolean,
        newTableNames: Dict<string>,
    ): Promise<void> {
        const commands: string[] = [];
        const registerIfNeeded = (
            actualComment: SqlObjectJsonComment | undefined,
            expectedComment: SqlObjectJsonComment,
            command: () => string,
        ): void => {
            if (actualComment != null) {
                if (JSON.stringify(actualComment) === JSON.stringify(expectedComment)) {
                    // The comment is already up-to-date
                    return;
                }
            }
            commands.push(command());
        };

        await asyncArray(factories).forEach(async factory => {
            const tableName = factory.requiredTableName;
            const tableDef = await this.getTableDefinition(null, tableName);
            const tableNameToUse = newTableNames[tableName] || tableName;
            let expectedComment: SqlObjectJsonComment = factory.table.getJsonComment();
            registerIfNeeded(tableDef.comment, expectedComment, () =>
                ModifyTableSqlContext.getSqlToComment(
                    'table',
                    {
                        schemaName: this.application.schemaName,
                        tableName: tableNameToUse,
                    },
                    expectedComment,
                ),
            );
            factory.table.columns.forEach(col => {
                expectedComment = col.getJsonComment();
                const colDef = (tableDef.columns || []).find(c => c.name === col.columnName);
                registerIfNeeded(colDef?.comment, expectedComment, () =>
                    ModifyTableSqlContext.getSqlToComment(
                        'column',
                        {
                            schemaName: this.application.schemaName,
                            tableName: tableNameToUse,
                            columnName: col.columnName,
                        },
                        expectedComment,
                    ),
                );
            });
            // Note: it's useless to try write the comment for FKs for a table that will be renamed because it will
            // probably fail as the table name has been fixed but not the name of the FK.
            // Nevermind, at the end of the upgrade, all the updated comments will be written - with no renaming management
            // so we will then have tableNameToUse === factory.tableName (and will execute the following else).
            if (tableNameToUse === factory.tableName) {
                factory.table.foreignKeys.forEach(fk => {
                    expectedComment = fk.getJsonComment();
                    const fkDef = (tableDef.foreignKeys || []).find(f => f.name === fk.name);
                    registerIfNeeded(fkDef?.comment, expectedComment, () =>
                        ModifyTableSqlContext.getSqlToComment(
                            'foreignKey',
                            {
                                schemaName: this.application.schemaName,
                                tableName: tableNameToUse,
                                fkName: fk.name,
                            },
                            expectedComment,
                        ),
                    );
                });
            }
        });

        // All the required SQL commands have been registered to the 'commands' array
        if (slowerButSaferMode) {
            // Slow mode: execute the SQL queries, 1 by one
            await asyncArray(commands).forEach(async command => {
                try {
                    await this.sysSchemaSqlContext.executeSqlStatement({ sql: command });
                } catch (err) {
                    logger.warn(`Could not set comment ${command}: ${err.stack}`);
                }
            });
        } else {
            // Fast mode: execute the commands by group of 1000
            await asyncArray(lodash.chunk(commands, 1000)).forEach(chunk =>
                this.sysSchemaSqlContext.executeSqlStatement({ sql: chunk.join(';') }),
            );
        }
    }

    /**
     * Are the SQL statements run against the database or are they recorded to a SQL file ?
     */
    get isRecording(): boolean {
        if (!this._sqlCommandsRecorder) return false;
        return !this._sqlCommandsRecorder.isPaused();
    }

    /**
     * Executes a body where the recorder will be paused
     * @param body
     */
    withoutRecording<T>(body: () => Promise<T>): Promise<T> {
        if (this._sqlCommandsRecorder) return this._sqlCommandsRecorder.withoutRecording(body);
        return body();
    }

    /**
     * Ends the recording session. From now, no more SQL commands will be added to the SQL file
     */
    stopRecording(): void {
        this._sqlCommandsRecorder?.endSession();
    }

    /**
     * Pre-execute an action.
     */
    async preExecuteAction(action: UpgradeAction, context?: Context): Promise<void> {
        await action.preExecute(this, { forRecording: this.isRecording, context });
    }

    /**
     * Execute an action.
     */
    async executeAction(action: UpgradeAction, context?: Context): Promise<void> {
        if (this._sqlCommandsRecorder) {
            this._sqlCommandsRecorder.currentAction = action;
        }
        try {
            await action.execute(this, { forRecording: this.isRecording, context });
        } finally {
            if (this._sqlCommandsRecorder) {
                this._sqlCommandsRecorder.currentAction = undefined;
            }
        }
    }

    /**
     * Post-execute an action.
     */
    async postExecuteAction(action: UpgradeAction, context?: Context): Promise<void> {
        await action.postExecute(this, { forRecording: this.isRecording, context });
    }

    /**
     * Returns the folder that contains SQL records
     */
    get sqlRecordsFolder(): string {
        return fsp.join(this.application.mainPackage.dir, 'sql');
    }

    get sysSchemaSqlContext(): SchemaSqlContext {
        if (!this._sysSchemaSqlContext) {
            this._sysSchemaSqlContext = new SchemaSqlContext(this.application);
        }
        return this._sysSchemaSqlContext;
    }

    /**
     * Should be called at the end of the upgrade process
     */
    async dispose(): Promise<void> {
        this._tableDefinitions = {};
        if (this._sqlCommandsRecorder) await this._sqlCommandsRecorder.dispose();
    }

    private static _getConnectionPools(): { classic: ConnectionPool; system: ConnectionPool } {
        const sqlConfig = ConfigManager.current.storage?.sql;
        if (!sqlConfig) throw new Error('SQL config is not set');

        return { classic: DatabaseManager.getPool(sqlConfig), system: DatabaseService.getSysPool(sqlConfig) };
    }

    /**
     * Register (if needed) the recorders that will be used to generate SQL files
     * @param context
     */
    async registerPoolRecorders(): Promise<void> {
        if (this.options.mode !== 'replayAndRecord') {
            // No recorder required
            return;
        }

        if (this._sqlCommandsRecorder != null) throw new Error('Logic error: recorder were already registered.');
        const commandsRecorder = await UpgradeCommandsRecorder.create(this);
        this._sqlCommandsRecorder = commandsRecorder;

        const createRecorder = (useSysPool: boolean): CommandsRecorder => {
            return {
                recordSqlCommand: (sql, args) => commandsRecorder.recordSqlQuery(useSysPool, sql, args),
                recordAction: (action, args) => commandsRecorder.recordAction(action, args),
                registerDataSet: (factory, dataSet) => commandsRecorder.registerDataSet(factory, dataSet),
                withoutRecording: bodyWithNoRecorder => commandsRecorder.withoutRecording(bodyWithNoRecorder),
            };
        };
        const pools = UpgradeContext._getConnectionPools();
        pools.classic.sqlRecorder = createRecorder(false);
        pools.system.sqlRecorder = createRecorder(true);
    }

    /**
     * Un-register the recorders that were used to generate SQL files
     */

    unregisterPoolRecorders(): void {
        if (this.options.mode !== 'replayAndRecord') {
            // No recorder was registered
            return;
        }
        const pools = UpgradeContext._getConnectionPools();
        pools.classic.sqlRecorder = undefined;
        pools.system.sqlRecorder = undefined;
    }

    withCommittedContext<T extends AnyValue | void>(
        body: (context: Context) => AsyncResponse<T>,
        options?: UpgradeContextOptions,
    ): Promise<T> {
        return this.application.withCommittedContext(options?.tenantId ?? null, body, {
            ...options,
            withoutTransactionUser: options?.tenantId == null,
        });
    }

    withUncommittedContext<T extends AnyValue | void>(
        body: (context: Context) => AsyncResponse<T>,
        options?: UpgradeContextOptions,
    ): Promise<T> {
        return this.application.withUncommittedContext(options?.tenantId ?? null, body, {
            ...options,
            withoutTransactionUser: options?.tenantId == null,
        });
    }

    createContextForDdl<T extends AnyValue>(
        body: (context: Context) => AsyncResponse<T>,
        options?: ContextOptions,
    ): Promise<T> {
        if (this.application.mainPackage.isReleased)
            throw new Error('Cannot create a context for DDL queries on a released application');
        return this.application.createContextForDdl(body, options);
    }

    getPackContext(pack: Package): UpgradePackContext {
        const key = pack.dir;
        let ctx = this._packContextsByPackName[key];
        if (!ctx) {
            ctx = new UpgradePackContext(pack, this);
            this._packContextsByPackName[key] = ctx;
        }
        return ctx;
    }

    /**
     * Replay the recorded commands required to move from the version in db to the version from the package.json
     */
    async replayRecordedCommands(): Promise<void> {
        await UpgradeCommandsRecorder.replayRecordedCommands(this, this._sqlCommandsRecorder);
        if (this._sqlCommandsRecorder) {
            // The version on the packages may have changed, we have to reset the recorder
            // so that its filename will be correct.
            // For instance, if we upgrade from 6.0.0 to 7.0.0 , the sqlRecorder was first bound to a
            // a <NAME_EMAIL>. If we replayed the SQL from 6.0.0 to 6.0.45
            // the the file must <NAME_EMAIL>
            await this._sqlCommandsRecorder.reset();
        }
    }

    /**
     * Retrieves the git HEAD of the last generated SQL file
     */
    private async _retrieveGitHeadOfLastGeneratedSqlFile(): Promise<string> {
        // First step is to retrieve the last generated SQL file
        // - we can't rely on file dates as it may happen that we manually fix them
        // - if the current version 1.2.3, we can't assume that the previous SQL file is 1.2.2-1.2.3 as
        //   the generation of the previous SQL might have failed, so it will be 1.2.1-1.2.3
        // We have to get a short list of the previous SQL files (1 major version back),
        // sort them by descending version and get the first file that matches 'toVersion==currentVersion' (it
        // should be the first file). If the current version is 20.0.31, we will inspect the range [19.0.31, 20.0.31]
        const startVersion = `${semver.major(this._initialVersion) - 1}.${semver.minor(
            this._initialVersion,
        )}.${semver.patch(this._initialVersion)}`;
        const allFiles = (
            await UpgradeCommandsRecorder.getFilenamesToReplay(this, {
                fromVersion: startVersion,
                dontValidate: true,
            })
        ).reverse();
        let lastSqlFile = allFiles.find(info => info.toVersion === this._initialVersion);
        if (!lastSqlFile) {
            lastSqlFile = allFiles[0];
            if (!lastSqlFile) {
                const defaultHead = await GitWrapper.getHead();
                logger.warn(`Could not retrieve the last SQL file, will use current HEAD : ${defaultHead}`);
                return defaultHead;
            }
            logger.warn(
                `Could not retrieve the exact last SQL file (version=${this._initialVersion}), will use ${lastSqlFile.relativeFilename} instead`,
            );
        }
        // Load the SQL file to get its gitHead. Note : older SQL files did not have the gitHead attribute
        const content = fs.readFileSync(fsp.join(this.sqlRecordsFolder, lastSqlFile.relativeFilename)).toString();
        const gitHead = JSON.parse(content).gitHead as string;
        if (gitHead == null) {
            const defaultHead = await GitWrapper.getHead();
            logger.warn(`Could not retrieve the HEAD from the last SQL file, will use current HEAD : ${defaultHead}`);
            return defaultHead;
        }
        return gitHead;
    }

    /**
     * Returns the (recursive) list of dependent factories.
     * For instance, if a vital collection child node is provided, the vital parent factory will be returned
     */
    static async _getDependentFactories(factories: NodeFactory[]): Promise<NodeFactory[]> {
        const vitalParentFactories = factories.filter(f => f.isVitalCollectionChild).map(f => f.vitalParentFactory);
        if (vitalParentFactories.length === 0) {
            return [];
        }
        // recursively add vital parent factories
        // Note : dependent factories must be placed first in the list (they need to be loaded first)
        return [...(await UpgradeContext._getDependentFactories(vitalParentFactories)), ...vitalParentFactories];
    }

    /**
     * Returns the factories for which a setup CSV file was updated since the last SQL file was generated.
     */
    async getFactoriesOfUpdatedSetupCsvFiles(): Promise<{
        lastSha: string;
        factories: NodeFactory[];
    }> {
        const fromSha = await this._retrieveGitHeadOfLastGeneratedSqlFile();

        logger.info(`Fetch the list of setup data to reload (fromSha = ${fromSha})`);
        const toSha = await GitWrapper.getHead();

        // The next lines can be uncommented to force the reloading of specific factories (debug purpose) - but NEVER COMMIT IT !!!!
        // if (true) {
        //     return {
        //         lastSha: toSha,
        //         factories: [this.application.getFactoryByName('User')],
        //     };
        // }

        if (toSha === fromSha) {
            return {
                lastSha: fromSha,
                factories: [],
            };
        }
        const factories = lodash.uniq(
            (await GitWrapper.listUpdatedFiles(fromSha, toSha))
                .map(filename => {
                    // try to match a setup CSV file (like .../setup/factoryName.csv)
                    let match = filename.match(/\/setup\/([\w,-]+)\.csv$/);
                    if (!match) {
                        // try to match an external file of a setup CSV file (like .../setup/factoryName/externalFile.whatever)
                        match = filename.match(/\/setup\/([\w,-]+)\/[\w,-]+\.[\w,-]+$/);
                        if (match == null) return null;
                    }
                    const factoryName = pascalCase(match[1]);
                    const factory = this.application.tryToGetFactoryByName(factoryName);
                    if (factory == null)
                        logger.warn(`Could not resolve factory ${factoryName} from CSV file ${filename}`);
                    return factory;
                })
                .filter(e => e) as NodeFactory[],
        );

        const vitalParentFactories = await UpgradeContext._getDependentFactories(factories);
        // Note : dependent factories must be placed first in the list (they need to be loaded first)
        const allFactories = lodash.uniq([...vitalParentFactories, ...factories]);

        return {
            lastSha: fromSha,
            factories: allFactories,
        };
    }
}
