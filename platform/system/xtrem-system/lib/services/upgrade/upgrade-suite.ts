import { Context, Node } from '@sage/xtrem-core';
import * as SemVer from 'semver';
import { DataUpgradeAction } from './actions/data-upgrade-action';
import { SchemaUpgradeAction } from './actions/schema-upgrade-action';
import { UpgradeAction } from './actions/upgrade-action';

type UpgradeSuitePostRecordBody = (context: Context) => Promise<void>;

export class UpgradeSuite {
    /**
     * The upgrade actions to be processed
     */
    readonly actions: UpgradeAction[];

    /**
     *
     * @param options parameters
     */
    constructor(
        private readonly options: {
            /**
             * @deprecated Use 'actions' instead
             */
            dataActions?: DataUpgradeAction<Node>[];
            /**
             * @deprecated Use 'actions' instead
             */
            schemaActions?: SchemaUpgradeAction<Node | undefined>[];
            actions?: UpgradeAction[];

            /**
             * An optional body that must be executed after the recording of a new SQL file
             * It's the right place to check if the actions did the right things
             */
            postRecordAction?: UpgradeSuitePostRecordBody;
        },
    ) {
        if (options.dataActions || options.schemaActions) {
            // Migration code
            if (options.actions)
                throw new Error("'dataActions'/'schemaActions' are deprecated and cannot be combined with 'actions'");
            this.actions = [...(options.schemaActions || []), ...(options.dataActions || [])];
        } else {
            this.actions = options.actions || [];
        }
    }

    get postRecordAction(): UpgradeSuitePostRecordBody | undefined {
        return this.options.postRecordAction;
    }
}

/**
 * Compares 2 versions
 * @return
 * - 0 if `version1` == `version2`
 * - 1 if `version1` is greater
 * - -1 if `version2` is greater.
 */
export function versionsComparer(version1: string, version2: string): number {
    if (version1 === 'latest') return 1;
    if (version2 === 'latest') return -1;
    return SemVer.compare(version1, version2);
}
