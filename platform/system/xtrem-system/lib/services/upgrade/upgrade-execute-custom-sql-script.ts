import { Application, AsyncResponse, Context, S3Helper } from '@sage/xtrem-core';
import { Datetime } from '@sage/xtrem-date-time';
import * as fs from 'fs';
import * as os from 'os';
import * as fsp from 'path';
import { SysCustomSqlHistory, SysCustomSqlHistoryResult } from '../../nodes/sys-custom-sql-history';
import { loggers } from '../loggers';

const logger = loggers.upgrade;

type ExecuteWithContext = (tenantId: string, body: (context: Context) => AsyncResponse<void>) => Promise<void>;

type ScriptExecutionResult = SysCustomSqlHistoryResult & {
    tenantId: string;
};

/**
 * This class can be used to execute custom SQL scripts.
 * Custom SQL scripts can be either local files or files from S3
 */
export abstract class UpgradeExecuteCustomSqlScript {
    static readonly #tenantIdRegex = /%%TENANT_ID%%/g;

    static readonly #schemaNameRegex = /%%SCHEMA_NAME%%/g;

    /**
     * Returns the regular expression of a RegExp
     */
    private static _getMarker(regex: RegExp): string {
        return /^\/([^/]+)\//.exec(regex.toString())?.[1] ?? '';
    }

    /**
     * Download a script from S3 to a local file (in the temp folder)
     * @param application the application
     * @param s3Uri the S3 URI of the script to download
     * @returns the full path of the local file
     */
    private static async _downloadFromS3(application: Application, s3Uri: string): Promise<string> {
        const scriptVersion = s3Uri.match(/-(\d+\.\d+\.\d+)\.sql$/);
        if (scriptVersion == null) {
            throw new Error(
                `Invalid S3 URI: the name of the script must be formatted with a version : xxxxxxx-aa.bb.cc.sql, got ${s3Uri}`,
            );
        }
        const appVersion = application.mainPackage.version;
        if (appVersion !== scriptVersion[1]) {
            throw new Error(
                `Version mismatch: the script ${s3Uri} can't be applied to this application (script version is ${scriptVersion[1]}, application version=${appVersion})`,
            );
        }
        const s3Info = S3Helper.parseS3Uri(s3Uri);
        const localFilename = fsp.join(os.tmpdir(), s3Info.key);
        logger.info(`Downloading ${s3Uri} to ${localFilename}`);
        await S3Helper.download(s3Info, localFilename);
        return localFilename;
    }

    /**
     * Read the content of a script from its path
     * @param path
     */
    private static async _readScriptContent(application: Application, path: string): Promise<string> {
        let pathToUse: string;
        if (path.startsWith('s3://')) {
            pathToUse = await UpgradeExecuteCustomSqlScript._downloadFromS3(application, path);
        } else {
            pathToUse = path;
        }
        return fs.readFileSync(pathToUse).toString();
    }

    /**
     * Execute a SQL script on a specific tenant and returns whether the script succeeded.
     * @param application the application
     * @param tenantId the tenant id
     * @param createContextMethod The function to use to create the context
     * @param script The script to execute (WITH ALL THE MARKERS RESOLVED)
     */
    private static async _executeScriptOnTenant(
        application: Application,
        tenantId: string,
        createContextMethod: ExecuteWithContext,
        script: {
            path: string;
            content: string;
            dryRun: boolean;
        },
    ): Promise<ScriptExecutionResult> {
        const startDateTime = Datetime.now();
        const historyId = await application.withCommittedContext(tenantId, async context => {
            const historyLine = await context.create(SysCustomSqlHistory, {
                scriptPath: script.path,
                scriptContent: script.content,
                startDateTime,
                dryRun: script.dryRun,
            });
            await historyLine.$.save();
            return historyLine._id;
        });

        const result: SysCustomSqlHistoryResult = {};
        logger.info(`Execute script ${script.path} on tenant ${tenantId}, dryMode = ${script.dryRun}`);
        await createContextMethod(tenantId, async (context: Context) => {
            try {
                // add the tenant as a comment to the script to execute so that, if it fails, we will
                // have the tenantId in the logged error
                const scriptToExecute = `-- TENANT: ${tenantId}\n${script.content}`;
                result.output = await context.executeSql(scriptToExecute, [], {
                    allowUnsafe: true, // Skip SQL injection warning (tenantId markers are replaced with a 'xxx' string)
                });
                logger.info(`\tResult for tenant ${tenantId}: ${JSON.stringify(result.output)}`);
            } catch (err) {
                // Do not throw an error now, we need to update the historyLine with the error
                result.error = `Script ${script.path} failed on tenant ${tenantId} : ${err.toString()}`;
            }
        });
        const endDateTime = Datetime.now();
        // Update the historyLine to set its endDateTime and its result
        await application.withCommittedContext(tenantId, async context => {
            const historyLine = await context.read(SysCustomSqlHistory, { _id: historyId }, { forUpdate: true });
            await historyLine.$.set({ endDateTime, result });
            await historyLine.$.save();
        });
        if (result.error) {
            // Do not throw any error here. A global error will be thrown at the end of the process
            logger.error(
                `Script ${script.path} failed on tenant ${tenantId}, dryMode = ${script.dryRun}, error was ${result.error}`,
            );
        } else {
            logger.info(
                `Script ${script.path} successfully executed on tenant ${tenantId}, dryMode = ${
                    script.dryRun
                }, duration=${endDateTime.value - startDateTime.value} ms`,
            );
        }
        return { ...{ tenantId }, ...result };
    }

    /**
     * Execute a custom SQL script
     * @param path the path to the script to execute
     * @param tenantIds the tenantIds on which the scripts has to be executed
     * @param dryRun run in dry mode ? (no commit)
     */
    static async executeCustomSqlScript(
        application: Application,
        path: string,
        tenantIds: string[],
        dryRun: boolean,
    ): Promise<void> {
        // Load the script
        const scriptWithMarkers = await UpgradeExecuteCustomSqlScript._readScriptContent(application, path);
        const tenantIdRegex = UpgradeExecuteCustomSqlScript.#tenantIdRegex;
        const schemaNameRegex = UpgradeExecuteCustomSqlScript.#schemaNameRegex;
        if (!tenantIdRegex.test(scriptWithMarkers)) {
            throw new Error(
                `The script ${path} does not include any ${UpgradeExecuteCustomSqlScript._getMarker(
                    tenantIdRegex,
                )} marker.`,
            );
        }
        if (!schemaNameRegex.test(scriptWithMarkers)) {
            throw new Error(
                `The script ${path} does not include any ${UpgradeExecuteCustomSqlScript._getMarker(
                    schemaNameRegex,
                )} marker.`,
            );
        }

        const scriptWithSchemaName = scriptWithMarkers.replace(schemaNameRegex, application.schemaName);

        logger.info(`Execute script ${path} on tenants [${tenantIds}], dryMode = ${dryRun}`);
        const createContextMethod = dryRun ? application.withUncommittedContext : application.withCommittedContext;
        const executeMethod = createContextMethod.bind(application) as ExecuteWithContext;
        const results = await Promise.all(
            tenantIds.map(tenantId => {
                const scriptWithTenantId = scriptWithSchemaName.replace(tenantIdRegex, `'${tenantId}'`);
                return UpgradeExecuteCustomSqlScript._executeScriptOnTenant(application, tenantId, executeMethod, {
                    path,
                    content: scriptWithTenantId,
                    dryRun,
                });
            }),
        );
        const failedExecutions = results.filter(result => result.error != null);
        if (failedExecutions.length > 0) {
            throw new Error(`The script ${path} failed for tenants: [${failedExecutions.map(f => f.tenantId)}]`);
        }
        logger.info(`Script ${path} was successfully executed on tenants [${tenantIds}], dryMode = ${dryRun}`);
    }
}
