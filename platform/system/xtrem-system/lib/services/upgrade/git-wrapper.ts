import { Config<PERSON>anager } from '@sage/xtrem-core';
import { ChildProcess, spawn, SpawnOptionsWithoutStdio } from 'child_process';
import { loggers } from '../loggers';

type ProcessResult = {
    /**
     * The stdout of the process
     */
    stdout: string;
    /**
     * The stderr of the process
     */
    stderr: string;
    /**
     * The exit code of the process
     */
    exitCode: number;
};

const logger = loggers.git;

/**
 * A git wrapper to execute git operations.
 *
 * Note :this wrapper does not manage credentials and assumes they are already set in the system.
 */
export abstract class GitWrapper {
    /**
     * Returns the HEAD of repository
     */
    public static async getHead(): Promise<string> {
        const result = await GitWrapper._executeGitCommand(['rev-parse', 'HEAD']);
        if (result.exitCode !== 0)
            throw new Error(`Could not get HEAD: exitCode=${result.exitCode}, error = ${result.stderr}`);
        return result.stdout;
    }

    public static async deepenFetch(depth: number, additionalOptions?: string[]): Promise<string> {
        const command = ['fetch', '--deepen', `${depth}`];
        if (additionalOptions && additionalOptions.length > 0) {
            command.push(...additionalOptions);
        }
        // On CI, fetch requires a shell to read password
        const result = await GitWrapper._executeGitCommand(command, { shell: true });
        if (result.exitCode !== 0)
            throw new Error(`Could not deepen fetch: exitCode=${result.exitCode}, error = ${result.stderr}`);
        return result.stdout;
    }

    /**
     * Returns the list of files that were updated between 2 shas.
     * Note: filenames are relative to the root of the git repo.
     */
    public static listUpdatedFiles(fromSha: string, toSha: string): Promise<string[]> {
        return GitWrapper._listUpdatedFiles(fromSha, toSha);
    }

    /**
     * Returns the list of files that were updated between 2 shas.
     * This function tries to deepen shallow clone by slices of 500 more commits
     * Note: filenames are relative to the root of the git repo.
     */
    private static async _listUpdatedFiles(fromSha: string, toSha: string, tries = 0): Promise<string[]> {
        const result = await GitWrapper._executeGitCommand(['diff', '--name-only', `${fromSha}..${toSha}`]);
        if (result.exitCode !== 0) {
            const isCI = ConfigManager.current.env?.isCI;
            if (isCI && tries < 4 && /Invalid revision range/i.test(result.stderr)) {
                // On shallow clone we may not have enough history, so we try to fetch more
                await GitWrapper.deepenFetch(500, GitWrapper._getFetchOptions(isCI));
                return GitWrapper._listUpdatedFiles(fromSha, toSha, tries + 1);
            }
            throw new Error(
                `Could not get list of updated files [${fromSha}..${toSha}]: exitCode=${result.exitCode}, error = ${result.stderr}`,
            );
        }
        return result.stdout.split('\n').filter(e => e);
    }

    private static _getFetchOptions(isCI = false): string[] {
        if (!isCI) {
            return [];
        }
        // On CI, we use same fetch options as in pipelines/shared/manual-git-clone.yml
        const {
            BUILD_SOURCEBRANCH: buildSourceBranch,
            SYSTEM_PULLREQUEST_PULLREQUESTNUMBER: prNumber,
            SYSTEM_PULLREQUEST_SOURCEBRANCH: prSourceBranch,
            SYSTEM_PULLREQUEST_TARGETBRANCH: prTargetBranch,
        } = process.env;
        const fetchOptions = ['--no-tags', '--force', '--prune', '--progress', '--no-recurse-submodules', 'origin'];
        if (!prSourceBranch) {
            fetchOptions.push(`'+${buildSourceBranch?.replace(/refs\/heads\//, '')}'`);
        } else if (prNumber) {
            fetchOptions.push(`'+refs/pull/${prNumber}/merge:refs/remotes/pull/${prNumber}/merge'`);
            fetchOptions.push(`'+${prTargetBranch}'`);
            fetchOptions.push(`'+${prSourceBranch}'`);
        }
        return fetchOptions;
    }

    /**
     * Executes the provided process and returns the result of its execution (stdout, stderr, exitCode)
     */
    private static _getProcessResult(process: ChildProcess): Promise<ProcessResult> {
        const parts: {
            stdout: string[];
            stderr: string[];
        } = { stdout: [], stderr: [] };

        const stringify = (lines: string[]): string => {
            const allLines = lines.join('');
            if (!allLines.endsWith('\n')) return allLines;
            return allLines.substring(0, allLines.length - 1);
        };
        return new Promise<ProcessResult>(resolve => {
            if (process.stdout)
                process.stdout.on('data', (data: any) => {
                    parts.stdout.push((data as Buffer).toString());
                });
            if (process.stderr)
                process.stderr.on('data', (data: any) => {
                    parts.stderr.push((data as Buffer).toString());
                });
            process.on('close', (code: number) => {
                resolve({
                    stdout: stringify(parts.stdout),
                    stderr: stringify(parts.stderr),
                    exitCode: code,
                });
            });
        });
    }

    /**
     * Executes a git command and returns the result of its execution (stdout, stderr, exitCode)
     */
    private static _executeGitCommand(
        command: string[],
        spawnOptions?: SpawnOptionsWithoutStdio,
    ): Promise<ProcessResult> {
        logger.verbose(() => `Execute git command git ${command.join(' ')}`);
        const proc = spawn('git', command, spawnOptions);
        return GitWrapper._getProcessResult(proc);
    }
}
