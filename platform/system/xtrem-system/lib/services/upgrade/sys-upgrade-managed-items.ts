import { Dict } from '@sage/xtrem-shared';

/**
 * Describes the nodes/nodeExtension managed by an upgrade bundle
 */
export type SysUpgradeManagedItems = {
    /**
     * The list of nodes (or extension) that are fully managed by the upgrade bundle (bundleId is set on the node's decorator)
     */
    fullNodes: {
        name: string;
        /** The name (if any) of the extended node. Only set for nodeExtensions */
        extendedNode?: string;
    }[];
    /** The list of properties managed by the upgrade bundle (bundleId is set on the property's decorator) */
    partialNodes: {
        /**
         *  The name of the node (or extension node)
         */
        name: string;
        /** The name (if any) of the extended node. Only set for nodeExtensions */
        extendedNode?: string;
        /** The names of the properties */
        propertyNames: Dict<string>;
    }[];
};
