import {
    asyncArray,
    ColumnInconsistency,
    ColumnInconsistencyDefaultValue,
    ColumnInconsistencyNullable,
    ColumnInconsistencyType,
    Context,
    Dict,
    dynamicImport,
    EnumDataType,
    getEncryptedStringStorageSize,
    NodeFactory,
    Package,
    Property,
    ReferenceProperty,
} from '@sage/xtrem-core';
import {
    ColumnDefinition,
    ColumnJsonComment,
    decimalColumnPrecision,
    decimalColumnScale,
    TableDefinition,
    TriggerDefinition,
} from '@sage/xtrem-postgres';
import * as fs from 'fs';
import * as lodash from 'lodash';
import * as fsp from 'path';
import { UpgradeSuite, versionsComparer } from '.';
import { loggers } from '../loggers';
import { SysPackageManager } from '../sys-package-manager';
import {
    ReloadSetupCsvAction,
    SchemaAllowShorterStringAction,
    SchemaAllowStringToIntegerAction,
    SchemaEnumPropertyDatatypeUpgradeAction,
    SchemaEnumRenameAction,
    SchemaRenameNodeAction,
    SchemaRenamePropertyAction,
    UpgradeAction,
} from './actions';
import { SupportedTypeConversions } from './actions/convert-column-type-action';
import { SchemaEnumUpgradeHelper } from './actions/schema-enum-upgrade-helper';
import { UpgradeContext } from './upgrade-context';

/**
 * @internal
 */
export type SuiteToApply = {
    suite: UpgradeSuite;
    version: string;
};

/**
 * @internal
 */
export type ActionAndVersion = {
    version: string;
    action: UpgradeAction;
};

/**
 * Schema changes for a table
 * A schema change is something that differs from what we have in the database and what we have in the decorators.
 * @internal
 */
export interface UpgradeSchemaChanges {
    /**
     * Is it a new table ?
     */
    isNewTable: boolean;
    missingProperties: Property[];
    /**
     * How was the table renamed ? (only set when the table was renamed)
     */
    renameTable?: {
        /**
         * The old table name
         */
        from: string;
        /**
         * The new table name
         */
        to: string;
    };
    /**
     * How the base table was changed ?
     */
    baseTableChanged?: {
        /**
         * The name of the old base table (might be null if the table did not have any base class before)
         */
        from?: string;
        /**
         * The name of the new base table (might be null if the table does no longer have any base class)
         */
        to?: string;
    };
    /**
     * Columns related changes
     */
    columns: {
        renamed: {
            from: string;
            to: string;
        }[];
        obsolete: ColumnDefinition[];
        updated: {
            updateTypes: ColumnInconsistency[];
            old: ColumnDefinition;
            new: Property;
        }[];
    };
    /**
     * Triggers related changes
     */
    triggers: {
        /**
         * Obsolete triggers to delete
         */
        obsolete: TriggerDefinition[];
        /**
         * New triggers to create
         */
        new: TriggerDefinition[];
    };
}

interface PackContextVersion {
    /** The version of the package (from the package.json file) */
    packageJsonVersion: string;
    /** The version of the package (from the sysPackVersion table) */
    sysPackVersion: string;
}

/**
 * @internal
 */
export class UpgradePackContext {
    private _schemaChangesByTableName: Dict<UpgradeSchemaChanges> = {};

    /**
     * The list of actions to be executed
     */
    private _actions: ActionAndVersion[] | undefined;

    private _upgradeSuites: SuiteToApply[];

    isUpToDate = false;

    factoriesToPostUpdate: Dict<boolean> = {};

    private _versions?: PackContextVersion;

    /**
     * List of the factories whose table was created by the upgrade process
     * Index: factory.name
     */
    createdFactories: Dict<boolean> = {};

    readonly options: {
        /** Should the upgrade be executed even if the versions match (i.e. the package seems to be up-to-date) */
        force: boolean;
    };

    constructor(
        readonly pack: Package,
        readonly upgradeContext: UpgradeContext,
    ) {
        this.options = upgradeContext.options;
    }

    get versions(): Promise<PackContextVersion> {
        return (async () => {
            if (!this._versions) {
                this._versions = {
                    sysPackVersion: (await this.upgradeContext.packageManager.getCurrentVersion(this.pack)) || '0.0.0',
                    packageJsonVersion: this.pack.packageJson.version,
                };
            }
            return this._versions;
        })();
    }

    /**
     * Returns the lists of subFolders that match a version that needs to be applied to upgrade a package.
     * For instance, sysPackVersion(p) = 1.2.3, package.version(p) = 1.3.7. This function will return the name
     * of every subFolder (located in package's folder + folderSuffix) that matches the pattern vX.Y.Z where
     * "1.2.3" < X.Y.Z <= "1.3.7". In the sample, it may return something like:
     * {
     *    rootFolder: '.....',
     *    versions: ['1.2.9', '1.3.1', '1.3.4', '1.3.7'] // Versions are sorted
     * }
     * @param pack
     * @param folderSuffix
     * @param force
     * @returns
     */

    static async getUpgradesToApply(
        pack: Package,
        upgradeType: 'upgrades' | 'data-patches',
        force: boolean,
    ): Promise<{ rootFolder: string; versions: string[] }> {
        const packageJsonVersion = pack.packageJson.version;
        const currentVersion =
            (await SysPackageManager.fromApplication(pack.application).getCurrentVersion(pack)) || '0.0.0';

        const sourcesFolder = fsp.join(pack.dir, 'lib', upgradeType);
        const binFolder = fsp.join(pack.dir, 'build', 'lib', upgradeType);

        if (packageJsonVersion === currentVersion) {
            if (!force) {
                return { rootFolder: binFolder, versions: [] };
            }
            // Force mode: load the upgrade suites even if the versions match
        }

        return UpgradePackContext.getUpgradesFromRange(sourcesFolder, binFolder, currentVersion, packageJsonVersion);
    }

    /**
     * Returns the lists of subFolders that match a version that needs to be applied to upgrade a package.
     * For instance, sysPackVersion(p) = 1.2.3, package.version(p) = 1.3.7. This function will return the name
     * of every subFolder (located in package's folder + folderSuffix) that matches the pattern vX.Y.Z where
     * "1.2.3" < X.Y.Z <= "1.3.7". In the sample, it may return something like:
     * {
     *    rootFolder: '.....',
     *    versions: ['1.2.9', '1.3.1', '1.3.4', '1.3.7'] // Versions are sorted
     * }
     * @returns
     * @param sourcesFolder the sources folder
     * @param binariesFolder the binaries folder
     * @param dbVersion the current version in database
     * @param packageVersion the version of the package.json
     */
    static getUpgradesFromRange(
        sourcesFolder: string,
        binariesFolder: string,
        dbVersion: string,
        packageVersion: string,
    ): { rootFolder: string; versions: string[] } {
        if (!fs.existsSync(sourcesFolder)) {
            loggers.upgrade.verbose(() => `No upgrades for package ${sourcesFolder}`);
            return { rootFolder: binariesFolder, versions: [] };
        }
        // We get the list of upgrades to apply from the source folder (lib/upgrades, not from build/lib/upgrades)
        const versions = fs
            .readdirSync(sourcesFolder, { withFileTypes: true })
            .filter(file => {
                if (!file.isDirectory()) return false;
                if (!/v(\d+\.\d+\.\d+|latest)/.test(file.name)) return false;
                if (!fs.existsSync(fsp.join(sourcesFolder, file.name, 'index.ts'))) {
                    // The folder exists but without upgradeSuite : skip it
                    return false;
                }
                if (!fs.existsSync(fsp.join(binariesFolder, file.name, 'index.js'))) {
                    // This upgrade exists in the source folder but not in the build folder
                    // This happens when the package was not built correctly
                    throw new Error(
                        `The upgrade from file ${file.name} can't be applied: it was found in ${sourcesFolder} but not in ${binariesFolder}.`,
                    );
                }
                return true;
            })
            .map(folder => folder.name.substring(1)) // remove the leading 'v'
            .filter(version => {
                if (version === 'latest') return true;
                // Only keep the versions greater than the current db version
                // Only keep the versions lower than the version in the package.json file
                return versionsComparer(version, dbVersion) > 0 && versionsComparer(version, packageVersion) <= 0;
            })
            .sort(versionsComparer);
        return { rootFolder: binariesFolder, versions };
    }

    /**
     * Returns the list of upgrade suites to apply
     */
    async getUpgradeSuites(): Promise<SuiteToApply[]> {
        if (this._upgradeSuites !== undefined) return this._upgradeSuites;

        const loadUpgradeSuites = async (): Promise<SuiteToApply[]> => {
            if (this.pack.name === '@sage/xtrem-core') {
                // No upgrade suite for xtrem-core: do not risk to load xtrem-core twice
                return [];
            }
            loggers.upgrade.verbose(() => `Loading upgrade suites for package ${this.pack.name}`);

            const versionsToUpgrade = await UpgradePackContext.getUpgradesToApply(
                this.pack,
                'upgrades',
                this.options.force,
            );

            return (
                await asyncArray(versionsToUpgrade.versions)
                    .map(async version => {
                        const indexFilename = fsp.join(versionsToUpgrade.rootFolder, `v${version}`, 'index');
                        loggers.upgrade.verbose(() => `\t- retrieve actions from ${indexFilename}`);
                        const mod = this.pack.isModule
                            ? await dynamicImport(`${indexFilename}.js`)
                            : // eslint-disable-next-line import/no-dynamic-require, global-require
                              require(indexFilename);
                        if (!mod.upgradeSuite) {
                            loggers.upgrade.verbose(
                                () => `The file ${indexFilename} does not contain any upgradeSuite constant.`,
                            );
                            return undefined;
                        }
                        const upgradeSuite = mod.upgradeSuite as UpgradeSuite;

                        if (loggers.upgrade.isActive('verbose')) {
                            loggers.upgrade.verbose(() => `\t\t- ${upgradeSuite.actions.length} actions:`);
                            upgradeSuite.actions.forEach(action => {
                                loggers.upgrade.verbose(() => `\t\t\t- ${action.description}`);
                            });
                        }

                        return {
                            suite: upgradeSuite,
                            version,
                        } as SuiteToApply;
                    })
                    .toArray()
            ).filter(s => s) as SuiteToApply[];
        };

        this._upgradeSuites = await loadUpgradeSuites();
        return this._upgradeSuites;
    }

    // eslint-disable-next-line class-methods-use-this
    private _getFromTypeLabel(factory: NodeFactory, fromType: string, colDef: ColumnDefinition) {
        if (!colDef.comment) {
            throw new Error(`Comment is missing on column ${factory.requiredTableName}.${colDef.name}`);
        }
        let fromTypeLabel = fromType;
        // eslint-disable-next-line default-case
        switch (fromType) {
            case 'string':
            case 'localizedString':
            case 'encryptedString':
                fromTypeLabel = `${fromType}[${colDef.comment.maxLength}]`;
                break;
            case 'reference':
                fromTypeLabel = `${fromType}[${colDef.comment.targetTableName}]`;
                break;
        }
        return fromTypeLabel;
    }

    /**
     * Tracks types inconsistencies between the db content and the factories
     * @param context
     * @param factory
     * @param colDef
     * @param property
     * @param inconsistencies
     * @returns
     */
    private async _checkColumnType(
        factory: NodeFactory,
        colDef: ColumnDefinition,
        property: Property,
        inconsistencies: ColumnInconsistency[],
    ): Promise<void> {
        if (!colDef.comment) {
            throw new Error(`Comment is missing on column ${factory.tableName}.${colDef.name}`);
        }

        let fromType = colDef.comment.type as string;
        if (colDef.comment.isAutoIncrement) fromType = 'autoIncrement';
        if (colDef.comment.isLocalized) fromType = 'localizedString';
        if (colDef.comment.isEncrypted) fromType = 'encryptedString';

        let toType = property.type as string;
        if (property.isAutoIncrement) toType = 'autoIncrement';
        if (property.isStringProperty()) {
            if (property.isStoredEncrypted) toType = 'encryptedString';
            if (property.isLocalized) toType = 'localizedString';
        }

        const fromTypeLabel = this._getFromTypeLabel(factory, fromType, colDef);

        const toTypeLabel = this._getToTypeLabel(toType, property);
        const conversionType: SupportedTypeConversions = `${fromType}->${toType}` as SupportedTypeConversions;

        // Note: in the following switch/case
        // - 'return' means that no type inconsistency will be registered
        // - 'break' will register a type inconsistency
        switch (conversionType) {
            case 'date->datetime':
            case 'datetime->date':
            case 'autoIncrement->integer':
            case 'integer->autoIncrement':
            case 'integer->decimal':
            case 'string->encryptedString':
            case 'string->textStream':
            case 'string->reference':
            case 'integer->string':
            case 'string->localizedString':
            case 'localizedString->string':
                // Add here all the allowed conversion that don't need extra checks
                // A type conversion will be registered for this property
                break;
            case 'integerArray->referenceArray':
                // That's ok, references array are stored as integers array (_int8): no conversion required
                return;
            case 'binary->binaryStream':
                // OK : photos for instance: no conversion required
                return;
            case 'reference->reference': {
                await this._manageReferenceToReferencePropertyUpgrade(factory, property, {
                    columnComment: colDef.comment,
                    inconsistencies,
                    fromType,
                    toType,
                    conversionType,
                });
                return;
            }
            case 'integer->reference': {
                await this._manageIntegerToReferencePropertyUpgrade(
                    factory,
                    property,
                    inconsistencies,
                    fromType,
                    toType,
                    conversionType,
                );
                return;
            }
            case 'string->integer': {
                if (!(await this._manageStringToIntegerPropertyUpgrade(factory, fromType, toType, property))) return; // No conversion
                // A conversion will be registered
                break;
            }
            case 'encryptedString->encryptedString':
            case 'string->string': {
                if (!(await this._manageStringToStringPropertyUpgrade(factory, fromType, toType, colDef, property)))
                    return; // No conversion
                // A conversion will be registered
                break;
            }
            case 'enum->enum':
            case 'enumArray->enumArray': {
                await this._manageEnumToEnumPropertyUpgrade(factory, colDef, property);
                return;
            }
            case 'decimal->decimal': {
                // Decimal columns should be declared as (28,10) in the database, whatever what the precision/scale are in the property decorator
                UpgradePackContext._manageDecimalToDecimalPropertyUpgrade(
                    colDef,
                    inconsistencies,
                    fromType,
                    toType,
                    conversionType,
                );
                // We don't care about any change in the precision/scale of the property
                // as all the decimal columns are store as decimal (28,10) in the database.
                return;
            }
            default:
                if (fromType === toType) return;
                throw new Error(
                    `${property.factory.name}.${property.name}: invalid conversionType : ${conversionType}`,
                );
        }

        inconsistencies.push(
            new ColumnInconsistencyType(`${fromTypeLabel}->${toTypeLabel}`, {
                fromType,
                toType,
                conversionType,
            }),
        );
    }

    // eslint-disable-next-line class-methods-use-this
    private _getToTypeLabel(toType: string, property: Property) {
        let toTypeLabel = toType;
        // eslint-disable-next-line default-case
        switch (toType) {
            case 'string':
            case 'localizedString':
            case 'encryptedString':
                toTypeLabel = `${toType}[${property.maxLength}]`;
                break;
            case 'reference':
                toTypeLabel = `${toType}[${(property as ReferenceProperty).targetFactory.tableName}]`;
                break;
        }
        return toTypeLabel;
    }

    /**
     * Returns whether a columnDefinition is in sync with a property
     */
    async checkColumn(
        context: Context,
        factory: NodeFactory,
        colDef: ColumnDefinition,
        property: Property,
    ): Promise<ColumnInconsistency[]> {
        const inconsistencies = [] as ColumnInconsistency[];
        if (!!colDef.isNullable !== !!property.isNullable) {
            inconsistencies.push(new ColumnInconsistencyNullable(!!colDef.isNullable, !!property.isNullable));
        }

        let defValFromDecorators: string | undefined;
        if (property.column?.default != null) {
            defValFromDecorators = property.column?.default;
        } else if (property.isStringProperty() && !property.isLocalized) {
            // System properties defaults should be set on sqlAttributes of Column
            defValFromDecorators = '';
        }
        // note: colDef.default will be null if not set
        const defValFromDb = colDef.default != null ? colDef.default : undefined;
        if (defValFromDb !== defValFromDecorators) {
            if (!(await this._shouldSkipInconsistency(defValFromDb, defValFromDecorators, context))) {
                inconsistencies.push(new ColumnInconsistencyDefaultValue(defValFromDb, defValFromDecorators));
            }
        }
        await this._checkColumnType(factory, colDef, property, inconsistencies);
        return inconsistencies;
    }

    private async _shouldSkipInconsistency(
        defValFromDb: string | undefined,
        defValFromDecorators: string | undefined,
        context: Context,
    ) {
        let skip = false;
        if (defValFromDb != null && defValFromDecorators != null) {
            // the type of the property was changed from an enum(or enumArray) to another one
            const allActions = await this.getActionsToExecute();
            if (
                allActions.some(actionAndVersion => {
                    if (!(actionAndVersion.action instanceof SchemaRenameNodeAction)) return false;
                    const action = actionAndVersion.action;
                    if (
                        defValFromDb.indexOf(`'${context.schemaName}.${action.oldTableName}'`) &&
                        defValFromDecorators?.indexOf(`'${context.schemaName}.${action.newTableName}'`)
                    ) {
                        // The changes was detected because the node will be renamed
                        // for instance
                        // from pg_get_serial_sequence('xtrem_upgrade_test.upgrade_rename_vital_child_v_1'::text, '_id'::text)
                        // to pg_get_serial_sequence('xtrem_upgrade_test.upgrade_rename_vital_child_v_2'::text, '_id'::text)
                        // while UpgradeRenameVitalChildV1 will be renamed to UpgradeRenameVitalChildV2
                        return true;
                    }
                    return false;
                })
            ) {
                // Ok, an action will manage it
                skip = true;
            }
        }
        return skip;
    }

    /**
     * Manage a (string->string) or (encryptedString->encryptedString) conversion of property type
     * @param factory
     * @param colDef
     * @param property
     * @returns
     */
    private async _manageStringToStringPropertyUpgrade(
        factory: NodeFactory,
        fromType: string,
        toType: string,
        colDef: ColumnDefinition,
        property: Property,
    ): Promise<boolean> {
        if (colDef.maxLength == null || property.maxLength == null) return false; // No conversion
        let maxLength = property.maxLength;
        if (property.isStringProperty() && property.isStoredEncrypted) {
            maxLength = getEncryptedStringStorageSize(maxLength);
        }
        if (maxLength === colDef.maxLength) {
            if (fromType === toType) {
                // Nothing changed: no conversion to register
                return false;
            }
            // The size is the same but the type changed (string->localizedString for instance)
            // We have to register a conversion for this column
            return true;
        }
        if (maxLength >= colDef.maxLength) return true;
        if (
            !(await this.getActionsToExecute(factory)).some(actionAndVersion => {
                if (!(actionAndVersion.action instanceof SchemaAllowShorterStringAction)) return false;
                const allowShorterAction = actionAndVersion.action;
                const options = allowShorterAction.options;
                if (options.propertyName !== property.name) return false;
                if (options.allowedShorterLength !== maxLength) {
                    throw new Error(
                        `String length mismatch: an action allows the property ${factory.name}.${property.name} to be shrinked to ${options.allowedShorterLength} characters but the current length defined in the factory is ${maxLength}.`,
                    );
                }
                return true;
            })
        ) {
            throw new Error(
                `${property.factory.name}.${property.name}: Cannot decrease string length : ${colDef.maxLength} -> ${maxLength}`,
            );
        }
        return true; // register the conversion
    }

    /**
     * Manage a (string->integer) conversion of property type
     * @param factory
     * @param property
     * @returns
     */
    private async _manageStringToIntegerPropertyUpgrade(
        factory: NodeFactory,
        fromType: string,
        toType: string,
        property: Property,
    ): Promise<boolean> {
        if (
            !(await this.getActionsToExecute(factory)).some(actionAndVersion => {
                if (!(actionAndVersion.action instanceof SchemaAllowStringToIntegerAction)) return false;
                const allowedStringToIntegerConversionAction = actionAndVersion.action;
                const options = allowedStringToIntegerConversionAction.options;
                if (options.propertyName !== property.name) return false;
                return true;
            })
        ) {
            throw new Error(`${property.factory.name}.${property.name}: Cannot convert: ${fromType} -> ${toType}`);
        }
        return true; // register the conversion
    }

    private async _manageReferenceToReferencePropertyUpgrade(
        factory: NodeFactory,
        property: Property,
        params: {
            columnComment: ColumnJsonComment;
            inconsistencies: ColumnInconsistency[];
            fromType: string;
            toType: string;
            conversionType: string;
        },
    ): Promise<void> {
        // Here, we are sure that the column stands for a reference
        // We have to check that the target of the reference is still the same
        const refProperty = property as ReferenceProperty;
        if (params.columnComment.targetTableName === refProperty.targetFactory.tableName) {
            return;
        }

        // The property was changed to ref(A) to ref(B)
        // We have to check if a rename action exists to rename A to B
        if (
            (await this.getActionsToExecute()).some(actionAndVersion => {
                const action = actionAndVersion.action;
                if (!(action instanceof SchemaRenameNodeAction)) return false;
                if (action.newNodeName !== refProperty.targetFactory.name) return false;
                if (action.oldTableName !== params.columnComment.targetTableName) return false;
                return true;
            })
        ) {
            // It's OK, no inconsistency here. The table will be renamed and everything will be fine.
            return;
        }

        // The target table of the reference has changed, the user MUST provide
        // an action to map the values in the columns of the existing rows
        // (even if this action does nothing)
        // Note: we can only check an action for the factory exists but we can't check that it will manage the property
        if ((await this.getActionsToExecute(factory)).length === 0) {
            throw new Error(
                `${property.factory.name}.${property.name}: the target of the reference has changed (from ${
                    params.columnComment.targetTableName //
                } to ${
                    refProperty.targetFactory.tableName
                }), you need to declare an action to map the existing values to the new target.`,
            );
        }
        params.inconsistencies.push(
            new ColumnInconsistencyType(
                `reference(${params.columnComment.targetTableName}) -> reference(${refProperty.targetFactory.tableName})`,
                params,
            ),
        );
    }

    /**
     * Manage a (enum->enum) or (enumArray->enumArray) conversion of property type
     * @param factory
     * @param colDef
     * @param property
     * @returns
     */
    private async _manageEnumToEnumPropertyUpgrade(
        factory: NodeFactory,
        colDef: ColumnDefinition,
        property: Property,
    ): Promise<void> {
        const enumTypeName = SchemaEnumUpgradeHelper.getTypeName((property.dataType as EnumDataType).enumName());
        if (!colDef.enumDataType)
            throw new Error(`Logic error: could not get the enum type for column ${factory.tableName}.${colDef.name}`);
        const currentEnumName = colDef.enumDataType.name;
        if (currentEnumName === `${colDef.type === 'enumArray' ? '_' : ''}${enumTypeName}`) {
            // The type of the property did not change
            return;
        }
        // the type of the property was changed from an enum(or enumArray) to another one
        const allActions = await this.getActionsToExecute();
        if (
            allActions.some(actionAndVersion => {
                if (!(actionAndVersion.action instanceof SchemaEnumPropertyDatatypeUpgradeAction)) return false;
                return actionAndVersion.action.propertyName === property.name;
            })
        ) {
            // Ok, an action will manage it
            return;
        }
        if (
            allActions.some(actionAndVersion => {
                if (!(actionAndVersion.action instanceof SchemaEnumRenameAction)) return false;
                return (
                    actionAndVersion.action.dataType === property.dataType &&
                    currentEnumName === actionAndVersion.action.oldEnumName
                );
            })
        ) {
            // Ok, the enum was renamed, an action will manage it
            return;
        }
        throw new Error(
            `${property.factory.name}.${property.name}: Enum property datatype has changed. SchemaEnumPropertyDatatypeUpgradeAction is required.`,
        );
    }

    /**
     * Manage a (decimal->decimal) conversion of property type
     * @param colDef
     * @param inconsistencies
     * @param fromType
     * @param toType
     * @param conversionType
     */
    private static _manageDecimalToDecimalPropertyUpgrade(
        colDef: ColumnDefinition,
        inconsistencies: ColumnInconsistency[],
        fromType: string,
        toType: string,
        conversionType: string,
    ): void {
        if (colDef.precision !== decimalColumnPrecision || colDef.scale !== decimalColumnScale) {
            inconsistencies.push(
                new ColumnInconsistencyType(
                    `decimal(${colDef.precision},${colDef.scale}) -> decimal(${decimalColumnPrecision},${decimalColumnScale})`,
                    {
                        fromType,
                        toType,
                        conversionType,
                    },
                ),
            );
        }
    }

    /**
     * Manage a (integer->reference) conversion of property type
     * @param property
     * @param context
     * @param factory
     * @param inconsistencies
     * @param fromType
     * @param toType
     * @param conversionType
     */
    private async _manageIntegerToReferencePropertyUpgrade(
        factory: NodeFactory,
        property: Property,
        inconsistencies: ColumnInconsistency[],
        fromType: string,
        toType: string,
        conversionType: string,
    ): Promise<void> {
        const refProperty = property as ReferenceProperty;
        // The user MUST provide an action to map the existing 'integer' values in the columns
        // of the existing rows to an existing record in the referenced table
        // (even if this action does nothing, if there are no data in the column)
        // Note: we can only check an action for the factory exists but we can't check that it will manage the property
        if ((await this.getActionsToExecute(factory)).length === 0) {
            throw new Error(
                `${property.factory.name}.${property.name}: the property is now a reference to ${
                    refProperty.targetFactory.tableName //
                }: you need to declare an action to map the existing values to the referenced table.`,
            );
        }
        inconsistencies.push(
            new ColumnInconsistencyType(`integer -> reference(${refProperty.targetFactory.tableName})`, {
                fromType,
                toType,
                conversionType,
            }),
        );
    }

    /**
     * Reset the factory changes: they will be recomputed when needed
     */
    resetFactoryChanges(): void {
        this._schemaChangesByTableName = {};
    }

    /**
     * Returns the schema changes for a given factory
     */
    async getFactoryChanges(context: Context, factory: NodeFactory): Promise<UpgradeSchemaChanges> {
        const key = factory.requiredTableName;
        let schemaChanges = this._schemaChangesByTableName[key];
        if (!schemaChanges) {
            schemaChanges = await this._computeFactorySchemaChanges(context, factory);
            this._schemaChangesByTableName[key] = schemaChanges;
        }
        return schemaChanges;
    }

    /**
     * Returns the schema changes for all the factories
     */
    getAllFactoryChanges(context: Context): Promise<Dict<UpgradeSchemaChanges>> {
        return asyncArray(this.upgradeContext.factoriesToUpgrade)
            .filter(factory => factory.package === this.pack)
            .reduce(async (total, factory) => {
                total[factory.requiredTableName] = await this.getFactoryChanges(context, factory);
                return total;
            }, {} as Dict<UpgradeSchemaChanges>);
    }

    /**
     * Returns the list of actions required to upgrade a package
     */
    async getActionsToExecute(factory: NodeFactory | undefined = undefined): Promise<ActionAndVersion[]> {
        if (this._actions === undefined) {
            const upgradeSuites = await this.getUpgradeSuites();
            const actions: ActionAndVersion[] = [];
            upgradeSuites.forEach(us => {
                us.suite.actions.forEach(action => {
                    if (action.dependsOnSetupData != null && action.dependsOnSetupData.length > 0) {
                        this.upgradeContext.registerActionWithDependsOnSetupData(action);
                        const nodesToReload = action.dependsOnSetupData.map(
                            factoryName => () => this.pack.application.getFactoryByName(factoryName).nodeConstructor,
                        );
                        loggers.upgrade.info(
                            `Registering auto-reload of setup data for factories ${action.dependsOnSetupData} for action ${action.description}`,
                        );
                        // This action requires some setup data to be up-to-date,
                        // so, we register a ReloadSetupCsvAction to be executed BEFORE the action
                        actions.push({
                            version: us.version,
                            action: new ReloadSetupCsvAction({
                                nodes: nodesToReload,
                            }),
                        });
                    }
                    actions.push({
                        version: us.version,
                        action,
                    });
                });
            });
            this._actions = actions;
        }
        if (factory) {
            return this._actions.filter(actionAndVersion => actionAndVersion.action.concernsFactory(factory));
        }
        return this._actions;
    }

    /**
     * Register an automatic action (created by the upgrade engine itself) for execution
     */
    async registerAutomaticAction(automaticAction: UpgradeAction): Promise<void> {
        // Make sure the actions have been loaded before appending anything to this._actions
        await this.getActionsToExecute();
        automaticAction.version = 'auto';
        automaticAction.isAutomaticAction = true;
        // We use unshift to make sure that automatic actions will be executed before any action provided
        // by the applicative teams. The order of automatic actions is not important.
        this._actions?.unshift({
            action: automaticAction,
            version: 'auto',
        });
    }

    /**
     * Compute the schema changes relative to triggers
     */
    static computeTriggerChanges(
        context: Context,
        factory: NodeFactory,
        tableDef: TableDefinition,
        changes: UpgradeSchemaChanges,
    ): void {
        const schemaRegEx = new RegExp(`^${context.schemaName}.`);

        const indexTriggers = (triggers: TriggerDefinition[]): Dict<TriggerDefinition> => {
            return triggers.reduce((obj, trigger) => {
                // Note: sometimes, the function name will be prefixed by the schemaName, but not always
                // To avoid false mismatches, we just get rid of it.
                const functionName = trigger.functionName.replace(schemaRegEx, '');
                obj[`${trigger.name}/${trigger.event}/${trigger.when}/${functionName}/${trigger.functionParameters}`] =
                    trigger;
                return obj;
            }, {} as Dict<TriggerDefinition>);
        };

        const triggersFromCode = factory.table.getTableDefinition(context).triggers || [];
        const factoryTriggersByKey = indexTriggers(triggersFromCode);
        const tableTriggersByKey = indexTriggers(tableDef.triggers || []);

        const newKeys = lodash.difference(Object.keys(factoryTriggersByKey), Object.keys(tableTriggersByKey));
        const keysToDelete = lodash.difference(Object.keys(tableTriggersByKey), Object.keys(factoryTriggersByKey));

        // We have to check that the common triggers (the ones that currently exist in the database AND in
        // the factory metadata) are still up-to-date.
        const commonKeys = lodash.intersection(Object.keys(factoryTriggersByKey), Object.keys(tableTriggersByKey));
        if (commonKeys.length > 0) {
            commonKeys.forEach(commonKey => {
                const tableTrigger = tableTriggersByKey[commonKey];
                const factoryTrigger = factoryTriggersByKey[commonKey];
                if (tableTrigger.functionName !== factoryTrigger.functionName) {
                    // Replace the trigger with its new definition
                    keysToDelete.push(commonKey);
                    newKeys.push(commonKey);
                }
            });
        }

        changes.triggers.new = newKeys.map(key => factoryTriggersByKey[key]);
        changes.triggers.obsolete = keysToDelete.map(key => tableTriggersByKey[key]);

        const baseChanges = changes.baseTableChanged;
        if (baseChanges) {
            if (baseChanges.from) {
                if (baseChanges.to == null) {
                    // The factory had a base node but not anymore
                    // There is nothing more to do. Its 'base_delete' trigger is already registered in changes.triggers.obsolete
                } else {
                    // When a factory A inherits from another factory B, a 'base_delete' trigger is generated on table A to delete
                    // the record from the base B when a record is deleted in table A.
                    // Here, the base node of A has been changed from B to C. We need to drop the
                    // 'base_delete' trigger (the one that points to B) and recreate it (the new one will point to C).
                    const triggersToDrop = tableDef.triggers?.filter(trigger => trigger.name === 'base_delete');
                    if (triggersToDrop?.length) {
                        if (triggersToDrop.length > 1) {
                            // Not allowed as there can be only one base node (no multiple inheritance)
                            throw new Error(
                                `Inconsitency error: found multiple 'delete' triggers for table ${factory.tableName}`,
                            );
                        }
                        const triggerToDrop = triggersToDrop[0];
                        changes.triggers.obsolete.push(triggerToDrop);
                        changes.triggers.new.push(triggerToDrop);
                    }
                }
            }
        }
    }

    /**
     * Compute the schema changes relative to the provided factory. If no factory is set, then
     * the changes for the current package will be computed.
     * A schema change is something that differs from what we have in the database and what we have in the decorators.
     * @param context
     * @param factory
     * @returns
     */
    private async _computeFactorySchemaChanges(context: Context, factory: NodeFactory): Promise<UpgradeSchemaChanges> {
        const result: UpgradeSchemaChanges = {
            isNewTable: false,
            missingProperties: [],
            columns: {
                obsolete: [],
                updated: [],
                renamed: [],
            },
            triggers: {
                obsolete: [],
                new: [],
            },
        };

        const upgradeSuites = await this.getUpgradeSuites();

        let tableNameToReadSchemaFrom = factory.requiredTableName;

        if (!(await factory.table.tableExists(context))) {
            // The table does not exists: 2 possible cases:
            // #1: this is a new table
            // #2: the table has been renamed

            UpgradePackContext._processNewOrRenamedTable(upgradeSuites, factory, result);

            if (result.renameTable) {
                // case #2: the table was renamed, we must continue because some other actions may need to be applied
                // to the table (new columns, ...)
                // We will have to read the schema from the existing table
                await this.upgradeContext.raiseErrorIfSchemaChangesAreForbidden(
                    `Cannot rename table ${factory.table.name}.`,
                    this,
                );
                tableNameToReadSchemaFrom = result.renameTable.from;
            } else {
                // case #1: this is a new table
                result.isNewTable = true;
                await this.upgradeContext.raiseErrorIfSchemaChangesAreForbidden(
                    `Cannot create new table ${factory.table.name}.`,
                    this,
                );
                return result;
            }
        }

        // Read the current table schema
        const tableSchema = await this.upgradeContext.getTableDefinition(context, tableNameToReadSchemaFrom);
        if (!tableSchema.comment) throw new Error(`Missing comments on SQL table ${factory.tableName}`);

        // Parse all the upgrade suites to fetch the columns to rename
        // Note: getting the renamed columns is only an information to ease some actions but the real
        // renaming will be performed by the SchemaRenamePropertyAction action
        await this._detectColumnsToRename(upgradeSuites, factory, result);

        const oldBaseTable = tableSchema.comment.baseTable;
        const newBaseTable = factory.table.baseTable?.name;
        if (oldBaseTable !== newBaseTable) {
            result.baseTableChanged = {
                from: oldBaseTable,
                to: newBaseTable,
            };
        }

        const columnsToRename = result.columns.renamed.reduce(
            (total, renamed) => {
                total.newNames.push(renamed.to);
                total.oldNames.push(renamed.from);
                return total;
            },
            { newNames: [] as string[], oldNames: [] as string[] },
        );

        await this._detectColumnsToUpdateOrCreate(factory, tableSchema, columnsToRename.newNames, result, context);
        await this._detectColumnsToDelete(tableSchema, factory, columnsToRename.oldNames, result);
        UpgradePackContext.computeTriggerChanges(context, factory, tableSchema, result);

        if (this.pack.isReleased && (await SchemaEnumUpgradeHelper.checkEnumProperties(context.application, factory))) {
            await this.upgradeContext.raiseErrorIfSchemaChangesAreForbidden(
                `Table ${factory.table.name} has enums types that have been updated.`,
                this,
            );
        }
        return result;
    }

    /**
     * Detects the columns that need to be renamed
     * @param upgradeSuites
     * @param context
     * @param factory
     * @param result
     */
    private async _detectColumnsToRename(
        upgradeSuites: SuiteToApply[],
        factory: NodeFactory,
        result: UpgradeSchemaChanges,
    ) {
        await asyncArray(upgradeSuites).forEach(async upgradeSuite => {
            const actionsToCheck = upgradeSuite.suite.actions.filter(
                action => action instanceof SchemaRenamePropertyAction && action.concernsFactory(factory),
            );
            await asyncArray(actionsToCheck).forEach(async (action: SchemaRenamePropertyAction<any>) => {
                const previousRename = result.columns.renamed.find(r => r.to === action.oldColumnName);
                if (previousRename) {
                    // We already encoutered a renaming action that concerns this column
                    // For instance:
                    // - v1.0.2: rename 'oldCol' to 'colTemp' (previousRename)
                    // - v1.0.3: rename 'colTemp' to 'newCol' (current action)
                    // Combine these 2 renaming actions to a single one: rename 'oldCol' to 'newCol'
                    previousRename.to = action.newColumnName;
                } else {
                    await this.upgradeContext.raiseErrorIfSchemaChangesAreForbidden(
                        `Cannot rename column ${action.oldColumnName} of table ${factory.table.name}.`,
                        this,
                    );
                    result.columns.renamed.push({
                        from: action.oldColumnName,
                        to: action.newColumnName,
                    });
                }
            });
        });
    }

    /**
     * Detect changes about a inexistant table (i.e. a table for which a factory exists but no table in the current db)
     * There could be 2 reasons :
     *   - the table is a new one
     *   - the table already exists but it was renamed
     * @param upgradeSuites
     * @param context
     * @param factory
     * @param result
     */
    private static _processNewOrRenamedTable(
        upgradeSuites: SuiteToApply[],
        factory: NodeFactory,
        result: UpgradeSchemaChanges,
    ) {
        const renameTableActions: SchemaRenameNodeAction<any>[] = [];

        // Parse all the upgrade suites to retrieve the tables to rename
        // Note: getting the renamed tables is only an information to ease some actions but the real
        // renaming will be performed by the SchemaRenameNodeAction action
        upgradeSuites.forEach(upgradeSuite => {
            upgradeSuite.suite.actions
                .filter(action => action instanceof SchemaRenameNodeAction && action.concernsFactory(factory))
                .forEach(action => renameTableActions.push(action as SchemaRenameNodeAction<any>));
        });
        renameTableActions.forEach(action => {
            const previousTableRename = result.renameTable;
            if (previousTableRename) {
                // We already encountered a renaming action that concerns this table
                // For instance:
                // - v1.0.2: rename 'table1' to 'table2' (previousTableRename)
                // - v1.0.3: rename 'table2' to 'table3' (current action)
                // Combine these 2 renaming actions to a single one: rename 'table1' to 'table3'
                previousTableRename.from = action.oldTableName;
            } else {
                result.renameTable = {
                    from: action.oldTableName,
                    to: factory.table.name,
                };
            }
        });
    }

    /**
     * Detect which columns need to be created or updated
     * @param factory
     * @param tableSchema
     * @param newNamesOfRenamedColumns the new names of renamed columns
     * @param result
     * @param context
     */
    private async _detectColumnsToUpdateOrCreate(
        factory: NodeFactory,
        tableSchema: TableDefinition,
        newNamesOfRenamedColumns: string[],
        result: UpgradeSchemaChanges,
        context: Context,
    ) {
        await asyncArray(factory.table.columns).forEach(async factoryColumn => {
            const { columnName, property } = factoryColumn;
            const colDef = tableSchema.columns?.find(col => columnName === col.name);
            if (!colDef) {
                // This column will need to be created
                if (newNamesOfRenamedColumns.includes(columnName)) {
                    // This column already exists but it was renamed.
                } else {
                    await this.upgradeContext.raiseErrorIfSchemaChangesAreForbidden(
                        `Cannot create column ${columnName} of table ${factory.table.name}.`,
                        this,
                    );
                    result.missingProperties.push(property);
                }
                return;
            }

            const inconsistencies = await this.checkColumn(context, factory, colDef, property);
            if (inconsistencies.length > 0) {
                await this.upgradeContext.raiseErrorIfSchemaChangesAreForbidden(
                    `Cannot update column ${colDef.name} of table ${
                        factory.table.name
                    }. Inconsistencies: ${inconsistencies.map(inconsistency => inconsistency.description).join()}`,
                    this,
                );
                result.columns.updated.push({ updateTypes: inconsistencies, old: colDef, new: property });
            }
        });
    }

    /**
     * Detect which columns need to be dropped
     * @param tableSchema
     * @param factory
     * @param oldNamesOfRenamedColumns the old names of renamed columns
     * @param result
     */
    private async _detectColumnsToDelete(
        tableSchema: TableDefinition,
        factory: NodeFactory,
        oldNamesOfRenamedColumns: string[],
        result: UpgradeSchemaChanges,
    ) {
        await asyncArray(tableSchema.columns || []).forEach(async colDef => {
            if (!factory.table.columnsByColumnName[colDef.name]) {
                // The columns shoud be deleted
                if (oldNamesOfRenamedColumns.includes(colDef.name)) {
                    // This column already exists but it was renamed.
                } else {
                    await this.upgradeContext.raiseErrorIfSchemaChangesAreForbidden(
                        `Cannot drop column ${colDef.name} of table ${factory.table.name}.`,
                        this,
                    );
                    result.columns.obsolete.push(colDef);
                }
            }
        });
    }

    /**
     * Check if schema changes are allowed.
     */
    public async checkSchemaChangesRestrictions() {
        const upgradeContext = this.upgradeContext;
        if (upgradeContext.schemaChangesRestriction === 'none') return;

        const actionAndVersions = await this.getActionsToExecute();
        await upgradeContext.withUncommittedContext(async context => {
            // Make sture the schema changes are up-to-date for all the factories of the package
            await this.getAllFactoryChanges(context);
        });
        await asyncArray(actionAndVersions).forEach(async actionAndVersion => {
            if (actionAndVersion.action.kind === 'schema')
                upgradeContext.issues.push({
                    message: await this.upgradeContext.getSchemaChangeViolationError(
                        `The action ${actionAndVersion.action.description} cannot be executed as it would cause schema changes.`,
                        this,
                    ),
                    severity: 'error',
                });
        });

        const prefix =
            upgradeContext.schemaChangesRestriction === 'checkSchema'
                ? 'Inconsitencies were found'
                : 'Could not upgrade schema';
        if (upgradeContext.issues.some(issue => issue.severity === 'error')) {
            const errors = upgradeContext.issues.map(issue => `\t- ${issue.severity}:${issue.message}\n`);
            throw new Error(`${prefix}:\n${errors}`);
        }
    }
}
