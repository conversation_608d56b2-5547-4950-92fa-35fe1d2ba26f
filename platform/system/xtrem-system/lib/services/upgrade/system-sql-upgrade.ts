import { Application, Logger, SchemaSqlContext } from '@sage/xtrem-core';
import { ConnectionPool } from '@sage/xtrem-postgres';

/**
 * The base class for a SQL system upgrade.
 * A system upgrade is a **SQL** action that will be executed at the very begining of the upgrade process and
 * is intented to upgrade a system table (sys-* table).
 *
 * SQL system upgrades are only allowed for the xtrem-system package.
 * System upgrades are located in the folder xtrem-system/lib/system-upgrades - one sub-folder per version.
 * - the subfolder must be named vX.Y.Z where X.Y.Z is the NEXT version of the xtrem-system package.
 *   before creating your PR, you will have to merge latest master to your branch and rename your vX.Y.Z folder
 *   to (version of xtrem-system)+1
 * - the subfolder must contain an index.ts file that must declare the list of actions as a 'systemUpgrades' variable.
 *
 * Note: The SQL system upgrades are not recorded to SQL files by the release-patch pipeline but are
 * executed on the cluster itself. For instance, they will be executed on cluster-ci/cluster-cu.
 *
 * Inheritors will have to implement the 'execute' function.
 * WARNING: as the system tables are not patched yet, only very basic operation can be used. For instance,
 * no context can be created. All the queries to the database must be done in SQL.
 *
 * To test your system upgrade : pnpm run xtrem upgrade --test
 */

/**
 * @internal
 */
export type SystemSqlUpgradeExecuteSql = (query: string, args: any[]) => Promise<void>;
/**
 * @internal
 */
export abstract class SystemSqlUpgrade {
    /**
     * The version of the system upgrade (its folder under the system-upgrades folder)
     */
    version: string;

    /**
     * @param description the description of the system SQL upgrade
     */
    constructor(readonly description: string) {}

    /**
     * Execution of the upgrade action (to be overridden)
     */
    abstract execute(
        application: Application,
        options: { logger: Logger; executeSql: SystemSqlUpgradeExecuteSql },
    ): Promise<void>;

    static executeSystemUpgrade(
        application: Application,
        systemUpgrade: SystemSqlUpgrade,
        options: {
            sysPool?: ConnectionPool;
            logger: Logger;
        },
    ): Promise<void> {
        const poolToUse = options.sysPool || new SchemaSqlContext(application).connectionPool;
        return poolToUse.withConnection(cnx =>
            systemUpgrade.execute(application, {
                logger: options.logger,
                executeSql: async (query: string, args: any[]) => {
                    try {
                        return await poolToUse.execute(cnx, query, args);
                    } catch (err) {
                        options.logger.error(
                            `Could not execute system upgrade ${systemUpgrade.description} (${systemUpgrade.version}). Failed query was ${query}, ${args}`,
                        );
                        options.logger.error(`System upgrade failed, ${err}`);
                        throw err;
                    }
                },
            }),
        );
    }
}
