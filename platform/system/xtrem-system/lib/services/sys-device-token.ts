import { ConfigManager, Context, CookieOptions, date, Logger, <PERSON><PERSON> } from '@sage/xtrem-core';
import { AxiosResponse } from 'axios';
import * as xtremSystem from '..';

const logger = Logger.getLogger(__filename, 'sys-device-token');

export const createDeviceToken = async (context: Context): Promise<xtremSystem.interfaces.XtremDeviceToken> => {
    if (context.isHttp()) {
        const tokenId = xtremSystem.functions.deviceTokenLib.generateTokenId();
        const expiration = xtremSystem.functions.deviceTokenLib.getTokenExpirationDate();
        const result = await createDeviceTokenApi(context, tokenId, expiration);

        const options: CookieOptions = {
            secure: context.isSecure(),
            domain: result.data.cookie.domain,
            sameSite: 'lax',
            httpOnly: result.data.cookie.httpOnly,
            expires: new Date(result.data.cookie.expires),
        };

        context.setDeviceTokenCookie(result.data.cookie.name, result.data.deviceToken, options);

        return { tokenId, expires: date.fromJsDate(new Date(result.data.cookie.expires)) };
    }

    throw new Error(`Context has not been created from an HTTP request`);
};

export const deleteDeviceToken = async (context: Context, tokenId: string) => {
    // require axios using Mocker.get, if this is executed from a unit test and axios is mocked
    // this will return the mocked axios module
    const axios = Mocker.get('axios', require);
    const interopUrl = ConfigManager.current.authentication?.interopUrl;

    const method = 'delete';
    const url = `${interopUrl}/api/device/${tokenId}`;

    const request = context.deviceTokenHttpRequest(method, url);

    const result = await axios(request);

    if (result.status === 401) {
        logger.error(
            `Authentication error: ${result.status} ${result.statusText} - ${JSON.stringify(result.data || 'no data')}`,
        );

        throw new Error(`Request failed with status code ${result.status} ${result.statusText}`);
    }
};

async function createDeviceTokenApi(
    context: Context,
    tokenId: string,
    expiration: number,
): Promise<AxiosResponse<xtremSystem.interfaces.DeviceToken>> {
    // require axios using Mocker.get, if this is executed from a unit test and axios is mocked
    // this will return the mocked axios module
    const axios = Mocker.get('axios', require);
    const interopUrl = ConfigManager.current.authentication?.interopUrl;

    const method = 'post';
    const url = `${interopUrl}/api/device`;
    const data = { tokenId, expiration };

    const request = context.deviceTokenHttpRequest(method, url, data);

    const result = await axios(request);

    if (result.status === 401) {
        logger.error(
            `Authentication error: ${result.status} ${result.statusText} - ${JSON.stringify(result.data || 'no data')}`,
        );

        throw new Error(`Request failed with status code ${result.status} ${result.statusText}`);
    }

    return result;
}

declare module '@sage/xtrem-core/lib/runtime/context' {
    export interface Context extends ContextInternal {}
}
