import {
    asyncArray,
    AsyncArray,
    Context,
    NodePayloadData,
    NotificationManager,
    UiBroadcastMessage,
} from '@sage/xtrem-core';
import { WebSocketNotificationManager } from '@sage/xtrem-infrastructure-adapter';
import {
    IdType,
    InitialNotification,
    Notification,
    NotificationPayload,
    PromisifyProperties,
} from '@sage/xtrem-shared';
import * as xtremSystem from '../../index';
import { SysClientNotification } from '../../nodes/sys-client-notification';
import _ = require('lodash');

export class SysNotificationManager implements NotificationManager {
    // eslint-disable-next-line class-methods-use-this
    private readonly readNotifications = async (
        context: Context,
    ): Promise<AsyncArray<xtremSystem.nodes.SysClientNotification>> => {
        return context.query(xtremSystem.nodes.SysClientNotification, {
            filter: { recipient: String((await context.user)?._id) },
            orderBy: { _createStamp: -1 },
        });
    };

    getUserNotifications = async (context: Context): Promise<Array<PromisifyProperties<Notification>>> => {
        return (await this.getNotifications(
            context,
        )) as unknown as Notification[] as unknown as PromisifyProperties<Notification>[];
    };

    getNotifications = async (context: Context): Promise<Notification[]> => {
        return (await this.readNotifications(context))
            .map(async n => {
                const payload = (await n.$.payload({ withIds: true, withTimeStamps: true })) as Required<
                    NodePayloadData<xtremSystem.nodes.SysClientNotification>
                >;
                const actions = (payload.actions || []) as Array<
                    Required<NodePayloadData<xtremSystem.nodes.SysClientNotificationAction>>
                >;

                const _createStamp = payload._createStamp?.toJsDate();

                return {
                    ...payload,
                    actions: actions.map(a => ({ ...a, _id: String(a._id) })),
                    _id: String(n._id),
                    _createStamp,
                };
            })
            .toArray();
    };

    // eslint-disable-next-line class-methods-use-this
    markRead = async (context: Context, _id: string): Promise<boolean> => {
        const notification = await context.read(xtremSystem.nodes.SysClientNotification, { _id }, { forUpdate: true });
        await notification.$.set({ isRead: true });
        await notification.$.save();

        await WebSocketNotificationManager.sendUserNotification(context, 'userNotification');

        return Promise.resolve(true);
    };

    // eslint-disable-next-line class-methods-use-this
    markUnread = async (context: Context, _id: string): Promise<boolean> => {
        const notification = await context.read(xtremSystem.nodes.SysClientNotification, { _id }, { forUpdate: true });
        await notification.$.set({ isRead: false });
        await notification.$.save();

        await WebSocketNotificationManager.sendUserNotification(context, 'userNotification');
        return Promise.resolve(true);
    };

    // eslint-disable-next-line class-methods-use-this
    markAllRead = async (context: Context): Promise<boolean> => {
        await context.bulkUpdate(xtremSystem.nodes.SysClientNotification, {
            set: { isRead: true },
            where: {
                recipient: String(context.userId),
            },
        });

        await WebSocketNotificationManager.sendUserNotification(context, 'userNotification');

        return Promise.resolve(true);
    };

    // eslint-disable-next-line class-methods-use-this
    delete = async (context: Context, _id: string): Promise<boolean> => {
        const notification = await context.read(xtremSystem.nodes.SysClientNotification, { _id }, { forUpdate: true });
        await notification.$.delete();
        await WebSocketNotificationManager.sendUserNotification(context, 'userNotification');
        return Promise.resolve(true);
    };

    // eslint-disable-next-line class-methods-use-this
    dispatchUserNotification = async (context: Context, notification: InitialNotification): Promise<void> => {
        // Should the notification be sent to the current user or to a specific user ?
        const getUserFromContext = notification.recipientsId == null || notification.recipientsId.length === 0;

        const getRecipients = async (): Promise<IdType[]> => {
            if (getUserFromContext) {
                const userFromContext = await context.user;
                if (userFromContext == null) throw new Error('No user found in context');
                return [userFromContext._id];
            }
            if (notification.recipientsId == null) throw new Error('No recipientsId found in notification');
            return notification.recipientsId;
        };

        const recipients = await getRecipients();

        await asyncArray(_.uniq(recipients)).forEach(async recipient => {
            if (!getUserFromContext) {
                // Ensure the user is valid (only required if the notification is not sent to the current user)
                const user = await context.tryRead(xtremSystem.nodes.User, { _id: recipient });
                if (user == null) {
                    context.logger.warn(`Could not send notification to user: ${recipient} (user does not exist)`);
                    return;
                }
            }
            const notificationNode = await context.create(SysClientNotification, {
                ...notification,
                _id: context.allocateTransientId(),
                level: notification?.level != null ? notification.level : 'info',
                actions: notification?.actions?.map(action => ({ ...action, _id: context.allocateTransientId() })),
                recipient,
            });
            await notificationNode.$.save();
        });

        await WebSocketNotificationManager.sendUserNotification(context, 'userNotification');
    };

    // eslint-disable-next-line class-methods-use-this
    dispatchAsyncMutationNotification = async (context: Context, payload: NotificationPayload): Promise<void> => {
        await WebSocketNotificationManager.sendUserNotification(context, 'asyncMutationComplete', payload);
    };

    // eslint-disable-next-line class-methods-use-this
    broadcast = async (message: UiBroadcastMessage): Promise<void> => {
        await WebSocketNotificationManager.broadcast(message);
    };
}
