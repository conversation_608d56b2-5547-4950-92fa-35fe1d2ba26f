/* eslint-disable class-methods-use-this */
import {
    AnyValue,
    Application,
    asyncArray,
    Context,
    DatabaseSqlContext,
    TenantInfo,
    TenantManager,
} from '@sage/xtrem-core';
import * as xtremSystem from '../index';
import { loggers } from '../loggers';

const logger = loggers.tenant;

type ExecSqlFunction = <T extends AnyValue = AnyValue[]>(sql: string, params: any[]) => Promise<T>;

class SysTenantManager implements TenantManager {
    initializeManager() {}

    async ensureTenantExists(
        context: Context,
        options: {
            customer: { id: string; name: string };
            tenant: { id: string; name: string };
        },
    ): Promise<void> {
        await context.application.asRoot.withCommittedContext(
            options.tenant.id,
            async createUserContext => {
                let customer = await createUserContext.tryRead(
                    xtremSystem.nodes.SysCustomer,
                    { customerId: options.customer.id },
                    { forUpdate: true },
                );
                if (!customer) {
                    logger.info(() => `Adds the customer ${options.customer.id}/${options.customer.name}`);
                    customer = await createUserContext.create(xtremSystem.nodes.SysCustomer, {
                        customerId: options.customer.id,
                        name: options.customer.name,
                    });
                    await customer.$.save();
                }

                let sysTenant = await createUserContext.tryRead(
                    xtremSystem.nodes.SysTenant,
                    { tenantId: options.tenant.id },
                    { forUpdate: true },
                );
                if (!sysTenant) {
                    logger.info(() => `Adds the tenant ${options.tenant.id}`);
                    sysTenant = await createUserContext.create(xtremSystem.nodes.SysTenant, {
                        tenantId: options.tenant.id,
                        name: options.tenant.name,
                        customer,
                    });
                    await sysTenant.$.save();
                }

                // Create initial required users for tenant
                await Context.accessRightsManager.createRequiredUsers(createUserContext);
            },
            { withoutTransactionUser: true },
        );
    }

    async listTenantsIds(context: Context): Promise<string[]> {
        const tenants = await context.query(xtremSystem.nodes.SysTenant).toArray();
        return asyncArray(tenants)
            .map(tenant => tenant.tenantId)
            .toArray();
    }

    async getTenantsInfo(context: Context, tenantId?: string): Promise<TenantInfo[]> {
        const queryOptions = tenantId ? { filter: { tenantId } } : undefined;
        const tenants = await context.query(xtremSystem.nodes.SysTenant, queryOptions as any).toArray();

        return asyncArray(tenants)
            .map(async tenant => {
                return {
                    id: await tenant.tenantId,
                    name: await tenant.name,
                    directoryName: await tenant.directoryName,
                    customer: {
                        id: await (await tenant.customer).customerId,
                        name: await (await tenant.customer).name,
                    },
                };
            })
            .toArray();
    }

    private withExecSql(
        tenantId: string,
        { withForeignKeyCheck }: { withForeignKeyCheck: boolean },
        body: (execSql: ExecSqlFunction) => Promise<void>,
    ): Promise<void> {
        // We need a DatabaseSqlContext to execute the SQL commands with the system user
        // Only the system user can set the session_replication_role to 'replica'
        const sqlContext = new DatabaseSqlContext();
        return sqlContext.withConnection(async connection => {
            const execSql: ExecSqlFunction = (sql, params) => sqlContext.execute(connection, sql, params);

            await execSql('BEGIN TRANSACTION ISOLATION LEVEL READ COMMITTED', []);
            // We need a transaction to isolate the set session_replication_role command
            // so that it won't be applied to the other connections
            // (BTW, we won't be allowed to use the LOCAL option if no transaction is open)
            try {
                try {
                    // Set the session_replication_role to 'replica' to bypass all the triggers and the foreign keys
                    // see https://www.postgresql.org/docs/devel/runtime-config-client.html#GUC-SESSION-REPLICATION-ROLE
                    // We don't need to check the constraints because we are going to delete all the records for the given tenant
                    if (!withForeignKeyCheck) await execSql('SET LOCAL session_replication_role = replica', []);
                } catch (err) {
                    throw new Error(
                        `Could not delete tenant ${tenantId} because you probably have insufficient privileges. Please update your configuration (storage/sql/sysUser)`,
                    );
                }

                await body(execSql);
                await execSql('COMMIT', []);
            } catch (err) {
                logger.error(`\t- Error while deleting tenant ${tenantId}: ${err.stack}`);
                await execSql('ROLLBACK', []);
                throw err;
            }
        });
    }

    /**
     * Deletes a tenant and all associated records from the database.
     *
     * @param application - The application instance containing the tenant.
     * @param tenantId - The id of the tenant to be deleted.
     * @returns A promise that resolves when the tenant has been deleted.
     *
     * @throws Will throw an error if the tenant cannot be deleted due to insufficient privileges or other issues.
     */
    async deleteTenant(application: Application, tenantId: string): Promise<void> {
        logger.warn(`\t- Deleting tenant ${tenantId} ...`);

        // Delete all the non-shared tables, starting from the leaves of the dependency tree
        const factories = application
            .getAllSortedFactories()
            .reverse()
            .filter(factory => {
                if (factory.isSharedByAllTenants) return false;
                if (factory.storage !== 'sql') return false;
                return true;
            });

        await asyncArray(factories).forEach(async factory => {
            await this.withExecSql(tenantId, { withForeignKeyCheck: false }, async execSql => {
                const t0 = Date.now();
                const { updateCount } = await execSql<{ updateCount: number }>(
                    `DELETE FROM ${application.schemaName}.${factory.table.name} WHERE _tenant_id=$1`,
                    [tenantId],
                );
                if (updateCount > 0) {
                    logger.info(
                        `\t-\t${factory.table.name}: deleted ${updateCount} records of tenant ${tenantId} in ${
                            Date.now() - t0
                        } ms`,
                    );
                } else {
                    logger.verbose(
                        () => `\t-\t${factory.table.name}: did not contain any record for the tenant ${tenantId}`,
                    );
                }
            });
        });

        // Some shared tables also have a tenant_id column, but no FK to sys_tenant table:
        const tableNames = ['sys_csv_checksum', 'sys_notification', 'sys_message'];
        await asyncArray(tableNames).forEach(async tableName => {
            await this.withExecSql(tenantId, { withForeignKeyCheck: true }, async execSql => {
                await execSql(
                    `DO $$
                        BEGIN
                            IF EXISTS (SELECT table_name FROM information_schema.tables WHERE table_name = '${tableName}' AND table_schema='${application.schemaName}') THEN
                                DELETE FROM ${application.schemaName}.${tableName} WHERE tenant_id='${tenantId}';
                            END IF;
                        END $$;`,
                    [],
                );
            });
        });

        await this.withExecSql(tenantId, { withForeignKeyCheck: true }, async execSql => {
            // Delete the sys_tenant entry
            // Cannot use withCommittedContext because
            const result = await execSql<{ updateCount: number }>(
                `DELETE FROM ${application.schemaName}.sys_tenant WHERE tenant_id=$1`,
                [tenantId],
            );
            if (result?.updateCount) {
                logger.warn('\t- tenant was deleted');
            } else {
                logger.warn('\t- tenant was not existing. Nothing was deleted.');
            }
        });
    }
}

export const sysTenantManager = new SysTenantManager();
