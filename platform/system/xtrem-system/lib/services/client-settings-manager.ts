import { ClientSettingsManager, ClientUserSettings, Context, CoreHooks, ValidationSeverity } from '@sage/xtrem-core';
import * as xtremSystem from '../index';

export const clientSettingsManager: ClientSettingsManager = {
    getActiveClientSettingsForArtifact: (context: Context, artifactId: string): Promise<ClientUserSettings[]> => {
        const result = context.query(xtremSystem.nodes.SysClientUserSettings, {
            filter: { user: context.userId, screenId: artifactId, isSelected: true },
        });

        return result.toArray();
    },
    getVariantsForElement: (context: Context, artifactId: string, elementId: string): Promise<ClientUserSettings[]> => {
        const result = context.query(xtremSystem.nodes.SysClientUserSettings, {
            filter: { user: context.userId, screenId: artifactId, elementId },
        });

        return result.toArray();
    },
    async updateClientSetting(
        context: Context,
        _id: string,
        content: any,
        title: string,
        description = '',
    ): Promise<ClientUserSettings> {
        const record = await context.read(xtremSystem.nodes.SysClientUserSettings, { _id }, { forUpdate: true });
        const owner = await record.user;

        if (owner._id !== context.userId) {
            throw new Error('Function not implemented.');
        }

        await record.$.set({ content, title, description });
        const saveResult = await record.$.trySave();

        if (!saveResult) {
            throw new Error(
                context.diagnoses
                    .filter(d => d.severity >= ValidationSeverity.error)
                    .map(d => d.message)
                    .join('/n'),
            );
        }

        return record;
    },
    async selectClientSetting(
        context: Context,
        artifactId: string,
        elementId: string,
        variantId: string,
    ): Promise<ClientUserSettings> {
        await context.bulkUpdate(xtremSystem.nodes.SysClientUserSettings, {
            where: { user: context.userId, screenId: artifactId, elementId },
            set: { isSelected: false },
        });

        const record = await context.read(
            xtremSystem.nodes.SysClientUserSettings,
            { _id: variantId },
            { forUpdate: true },
        );
        await record.$.set({ isSelected: true });
        await record.$.save();

        return record;
    },
    async createClientSetting(
        context: Context,
        artifactId: string,
        elementId: string,
        content: any,
        title: string,
        description = '',
    ): Promise<ClientUserSettings> {
        await context.bulkUpdate(xtremSystem.nodes.SysClientUserSettings, {
            where: { user: context.userId, screenId: artifactId, elementId },
            set: { isSelected: false },
        });

        const newRecord = await context.create(xtremSystem.nodes.SysClientUserSettings, {
            screenId: artifactId,
            elementId,
            isSelected: true,
            content,
            title,
            description,
            user: context.userId,
        });

        const saveResult = await newRecord.$.trySave();

        if (!saveResult) {
            throw new Error(
                context.diagnoses
                    .filter(d => d.severity >= ValidationSeverity.error)
                    .map(d => d.message)
                    .join('/n'),
            );
        }

        return newRecord;
    },
    async resetClientSettings(context: Context): Promise<boolean> {
        await context.deleteMany(xtremSystem.nodes.SysClientUserSettings, { user: context.userId });
        return true;
    },
    async deleteClientSetting(context: Context, _id: string): Promise<boolean> {
        await context.delete(xtremSystem.nodes.SysClientUserSettings, { _id });
        return true;
    },

    getCustomizationListPage: (): string => {
        return '@sage/xtrem-system/ClientUserSettingsList';
    },

    getCustomizationEditPage: (): string => {
        return '@sage/xtrem-system/ClientUserSettingsEdit';
    },

    async unselectedClientSetting(context: Context, artifactId: string, elementId: string): Promise<void> {
        await context.bulkUpdate(xtremSystem.nodes.SysClientUserSettings, {
            where: { user: context.userId, screenId: artifactId, elementId },
            set: { isSelected: false },
        });
    },
};

CoreHooks.createClientSettingsManager = () => clientSettingsManager;
