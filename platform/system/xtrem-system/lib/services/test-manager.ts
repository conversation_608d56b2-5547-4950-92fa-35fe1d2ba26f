import { loadLayersFromCsvFiles } from '@sage/xtrem-cli-layers';
import { Application, basicProfiler, ContextOptions, CoreHooks, Test, TestResetTables } from '@sage/xtrem-core';
import { loggers } from '../loggers';
import { SysPackageManager } from './sys-package-manager';

// This class provides the method overrides for Test.withContext. See CoreHooks.testManager
export class TestManager {
    /**
     * Initializes the pack allocation for system factories
     */
    static async initTestTenantActivePackages(application: Application): Promise<void> {
        const packageManager = SysPackageManager.fromApplication(application);
        await application.asRoot.withCommittedContext(null, async context => {
            await packageManager.createOrUpgradePackageVersions(context);
        });
        await application.asRoot.withCommittedContext(Test.defaultTenantId, async context => {
            await packageManager.createOrUpgradePackageAllocations(context);
        });
    }

    static async prepareLoadTestDatabase(application: Application, contextOptions: ContextOptions): Promise<void> {
        // Note: resetTestTables function will drop and re-create the schema.
        // Nothing must be written to the database before this function
        await TestResetTables.resetTestTables(application, contextOptions);
        await TestResetTables.resetTestTableSequences(application, contextOptions);
        const packageManager = SysPackageManager.fromApplication(application);
        await packageManager.updateAllPackageVersions();
        application.startListeners(['testListen']);
    }

    static async loadTestData(
        application: Application,
        options: { testLayers?: string[] },
        tenantId: string,
    ): Promise<void> {
        const layers = options.testLayers || ['setup', 'test'];
        await basicProfiler.measure('loadTestData', () => loadLayersFromCsvFiles(application, layers, tenantId));

        await basicProfiler.walk((path, item) => {
            if (path[0].startsWith('loadTestData')) loggers.tenant.info(`${path.join('/')}: ${item.duration} ms`);
        });

        // load service options in the database:
        await application.asRoot.withCommittedContext(Test.defaultTenantId, context =>
            application.serviceOptionManager.createOrUpgradeServiceOptionStates(context),
        );
    }
}

CoreHooks.testManager = {
    loadTestData: TestManager.loadTestData.bind(TestManager),
    initTestTenantActivePackages: TestManager.initTestTenantActivePackages.bind(TestManager),
};
