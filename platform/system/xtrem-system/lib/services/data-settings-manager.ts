/* eslint-disable class-methods-use-this */
import { Context, DataSettingsManager, Node, StaticThis } from '@sage/xtrem-core';
import { loggers } from '../loggers';
import { SysVendor } from '../nodes/sys-vendor';

const logger = loggers.application;

class SysDataSettingsManager implements DataSettingsManager {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    initializeManager(context: Context): void {
        logger.verbose(() => 'Data settings manager is loaded');
    }

    getSysVendorNode(): StaticThis<Node> {
        return SysVendor;
    }

    async sageVendorId(context: Context): Promise<number> {
        return (await context.read(SysVendor, { name: 'sage' }))._id;
    }
}

export const sysDataSettingsManager = new SysDataSettingsManager();
