// WAS IN xtrem-data-management before
/**
 * This scripts is only intended to be invoked from xrtem-cli
 */

import { loadLayersFromCsvFiles } from '@sage/xtrem-cli-layers';
import {
    Application,
    ApplicationCreateSqlSchema,
    Context,
    CoreHooks,
    CreateSchemaOptions,
    DatabaseSqlContext,
} from '@sage/xtrem-core';
import { loggers } from '../loggers';
import { SysPackageManager } from './sys-package-manager';

export abstract class SchemaManager {
    /**
     * Init database
     * @param config Pool Config
     */
    static async ensureSchemaExists(schemaName: string, options?: CreateSchemaOptions): Promise<void> {
        await new DatabaseSqlContext().createDatabaseIfNotExists();
        if (options?.resetSchema) {
            loggers.application.info(`Dropping schema ${schemaName}`);
            await new DatabaseSqlContext().dropSchemaIfExists(schemaName);
        }
        await new DatabaseSqlContext().createSchemaIfNotExists(schemaName);
    }

    /**
     * Load Activity data
     */
    static async loadActivityData(context: Context): Promise<void> {
        // Export activities setup in packages if the node Activity is defined
        await Context.accessRightsManager.createActivities(context);
        await Context.accessRightsManager.updateActivities(context);
        await Context.accessRightsManager.deleteActivities(context);
    }

    /**
     * Create the database schema for the application and all its dependencies
     * and load setup data.
     * Will return true if the creation was successful, and false if something went wrong
     */
    static async createSqlSchemaAndTables(
        application: Application,
        options: CreateSchemaOptions = {},
    ): Promise<boolean> {
        await this.ensureSchemaExists(application.schemaName, options);
        const packageManager = SysPackageManager.fromApplication(application);

        await application.createContextForDdl(context => packageManager.initPackageManagerTables(context));

        if (await application.asRoot.withReadonlyContext(null, context => packageManager.hasPackageVersions(context))) {
            loggers.upgrade.error('SysPackVersion table is not empty. Please drop the schema.');
            return false;
        }

        await application.createContextForDdl(
            context => ApplicationCreateSqlSchema.createTables(context, packageManager.getFactories()),
            {
                description: () => 'Create tables',
            },
        );

        await application.asRoot.withCommittedContext(
            null,
            async context => {
                await packageManager.createOrUpgradePackageVersions(context);
                await context.serviceOptionManager.createOrUpdateServiceOptions(context);
            },
            { withoutTransactionUser: true },
        );

        // Load the shared table data, passing in null for the tenantId will skip the tenant creation step
        await loadLayersFromCsvFiles(application, ['setup'], null, { onlySharedTables: true });

        // Initialize shared tables which are not marked as setup:
        await application.asRoot.withCommittedContext(
            null,
            async context => {
                await this.loadActivityData(context);
            },
            { withoutTransactionUser: true },
        );

        await CoreHooks.metadataManager.upgradeMetadata(application);
        return true;
    }
}
