import { Context } from '@sage/xtrem-core';
import { SysPackVersion } from '../../nodes/sys-pack-version';

export abstract class PackVersionManager {
    /**
     * Delete a packVersion
     * @param context
     * @param options
     */
    static async deletePackVersion(context: Context, packageName: string): Promise<void> {
        await (await context.read(SysPackVersion, { name: packageName }, { forUpdate: true })).$.delete();
    }
}
