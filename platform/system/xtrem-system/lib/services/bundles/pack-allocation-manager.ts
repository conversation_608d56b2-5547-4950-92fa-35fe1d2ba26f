import { BulkUpdateOptions, Context } from '@sage/xtrem-core';
import { SysPackAllocation } from '../../nodes/sys-pack-allocation';
import { SysPackVersion } from '../../nodes/sys-pack-version';

export abstract class PackAllocationManager {
    static bulkUpdatePackAllocations(context: Context, options: BulkUpdateOptions<SysPackAllocation>): Promise<number> {
        return context.bulkUpdate(SysPackAllocation, options);
    }

    /**
     * Delete some packAllocations from a bundle Id.
     * Returns the number of deleted packAllocations.
     * @param context
     * @param options
     */
    static async deletePackAllocation(context: Context, bundleId: string): Promise<number> {
        const packVersion = await context.tryRead(SysPackVersion, { name: bundleId });
        if (!packVersion) {
            throw new Error(`Package ${bundleId} does not exist`);
        }
        return context.deleteMany(SysPackAllocation, {
            package: packVersion._id,
        });
    }
}
