import { <PERSON><PERSON>, SystemError } from '@sage/xtrem-core';
import axios, {
    AxiosRequestConfig,
    AxiosResponse,
    AxiosResponseHeaders,
    InternalAxiosRequestConfig,
    Method,
    RawAxiosRequestHeaders,
} from 'axios';
import * as xtremSystem from '../../index';
import { RequestGenericResponse } from '../interfaces/request-generic';

export class RequestGeneric {
    public isLocalEnv = !process.env.XTREM_ENV || process.env.XTREM_ENV === 'local';

    /** To be able to delete some headers */
    protected authHeader: string[] = [];

    protected logger: Logger;

    protected headers: RawAxiosRequestHeaders = { 'Content-Type': 'application/json' };

    protected defaultUrl = '';

    constructor(public parameters?: xtremSystem.interfaces.RequestGenericConstructor) {
        this.logger = this.parameters?.logger || Logger.getLogger(__filename, 'req');
    }

    get urlParametersString(): string | undefined {
        return this.parameters?.urlParameters;
    }

    get urlParameters(): string {
        if (this.parameters?.urlParameters) {
            const paramToAdd = Object.entries(this.parameters.urlParameters).reduce(
                (accumulator: string, [key, value]: [string, string]) =>
                    `${accumulator}${accumulator ? '&' : ''}${key}=${value}`,
                '',
            );
            this.logger.debug(() => `Param of the ${paramToAdd}`);
            return `?${paramToAdd}`;
        }
        return '';
    }

    get url(): string {
        return `${this.parameters?.url ?? this.defaultUrl}${this.urlParameters}`;
    }

    get method(): Method {
        return this.parameters?.method ?? 'GET';
    }

    get data(): Method {
        return this.parameters?.data ?? undefined;
    }

    /** Generate the AxiosRequestConfig & launch the request
     *  we are returning a promise so we need await     */
    execute<T extends {}>(): Promise<RequestGenericResponse<T>> {
        if (!this.url) {
            throw new SystemError('No URL to request');
        }
        this.logger.debug(() => `requestGeneric:execute parameters ${JSON.stringify(this.parameters)}`);
        const config: AxiosRequestConfig = {
            headers: this.headers,
            url: this.url,
            method: this.method,
            data: this.data,
        };
        // If we are putting then we don't need any headers
        if (this.parameters?.noHeaders) {
            delete config.headers;
        }
        this.logger.debug(() => `requestGeneric:execute data ${JSON.stringify(this.data)}`);
        return this.request(config);
    }

    /** Axios request  */
    async request<T extends {}>(config: AxiosRequestConfig): Promise<RequestGenericResponse<T>> {
        this.logger.debug(
            () => `requestGeneric:request ${config.method} :${config.url} \n Data : ${JSON.stringify(
                config.data,
                null,
                4,
            )}
         ${JSON.stringify(config.headers, null, 4)}`,
        );

        try {
            let axiosResult: AxiosResponse;
            switch (this.method) {
                case 'GET':
                    this.logger.debug(() => `requestGeneric:request GET ${this.method}`);
                    axiosResult = await axios.get(config.url ?? '', config);
                    break;
                case 'PUT':
                    this.logger.debug(() => `requestGeneric:request PUT ${this.method}`);
                    axiosResult = await axios.put(config.url ?? '', config.data, config);
                    break;
                case 'POST':
                    this.logger.debug(() => `requestGeneric:request POST ${this.method}`);
                    axiosResult = await axios.post(config.url ?? '', config.data, config);
                    break;
                default:
                    throw new SystemError(`Invalid method (${this.method})`);
            }

            // Will only output debug if not status 202 (Accepted) as we will have to call again
            this.logAxiosResponse('requestGeneric:request', axiosResult);

            return {
                status: axiosResult.status,
                statusText: axiosResult.statusText,
                data: axiosResult.data,
                errors: [],
                headers: axiosResult.headers as AxiosResponseHeaders,
            };
        } catch (error) {
            this.logger.error(() => `requestGeneric:request Axios error ${JSON.stringify(error)}`);
            if (error.response) {
                throw new SystemError(
                    `Internal Server Error ${error.response.status} : ${error.response.statusText} ${JSON.stringify(
                        error.response.data.errors,
                    )}`,
                );
            }
            this.logger.debug(() => `requestGeneric:request Axios error stack : ${JSON.stringify(error.stack)} `);
            throw new SystemError(`Axios error - ${error.message}`);
        }
    }

    logAxiosResponse<T extends {}>(
        location: string,
        result: RequestGenericResponse<T> | AxiosResponse | InternalAxiosRequestConfig<any>,
    ): void {
        this.logger.debug(() => `${location} results.data=${JSON.stringify(result.data)}`);
        this.logger.debug(() => `${location} results.headers=${JSON.stringify(result.headers)}`);
        if ('status' in result && result.status !== 202) {
            this.logger.debug(() => `${location} results.status=${JSON.stringify(result.status)}`);
            this.logger.debug(() => `${location} results.statusText=${JSON.stringify(result.statusText)}`);
        }
        if ('errors' in result) {
            this.logger.debug(() => `${location} results.errors=${JSON.stringify(result.errors)}`);
        }
    }
}
