import { integer, NodeFromInterface, NodeStatus, ValidationContext } from '@sage/xtrem-core';
import { loggers } from '../../loggers';

const logger = loggers.events;

/**
 * Validates that isActive flag is on
 * @param nodeStatus - node status (control is done always on creation and on modification only if the current property was changed)
 * @param cx - validation context
 * @param oldId - previous value (this.$.old._id) in order to check if the value of the current property was changed
 * @param val - object being validated
 */
export async function isActive(
    nodeStatus: NodeStatus,
    cx: ValidationContext,
    oldId: integer | null,
    val: NodeFromInterface<{ isActive: boolean; id?: string }> | null,
): Promise<void> {
    if (!val) return;

    if ('isActive' in val) {
        switch (nodeStatus) {
            case NodeStatus.added: {
                if (!(await val.isActive)) {
                    cx.error.add(
                        cx.localize(
                            '@sage/xtrem-system/is-active/node-id-combination-not-active',
                            '{{nodeName}} {{dataId}} is not active',
                            {
                                nodeName: val.constructor.name,
                                dataId: val.$.hasProperty('id') ? await val.id : val._id,
                            },
                        ),
                    );
                }
                break;
            }
            case NodeStatus.modified: {
                if (val._id !== oldId && !(await val.isActive)) {
                    cx.error.add(
                        cx.localize(
                            '@sage/xtrem-system/is-active/node-id-combination-not-active',
                            '{{nodeName}} {{dataId}} is not active',
                            {
                                nodeName: val.constructor.name,
                                dataId: val.$.hasProperty('id') ? await val.id : val._id,
                            },
                        ),
                    );
                }
                break;
            }
            default: {
                break;
            }
        }
    } else {
        logger.warn(() => `${val} does not contain an isActive property`);
    }
}
