import { Activity } from '@sage/xtrem-core';
import { SysTenant } from '../nodes/sys-tenant';

export const tenant = new Activity({
    description: 'Tenant',
    node: () => SysTenant,
    __filename,
    permissions: ['read', 'update', 'resetTenantDocuments', 'getTenantInformation'],
    operationGrants: {
        resetTenantDocuments: [
            {
                operations: ['resetTenantDocuments'],
                on: [() => SysTenant],
            },
        ],
        getTenantInformation: [
            {
                operations: ['getTenantInformation'],
                on: [() => SysTenant],
            },
        ],
    },
});
