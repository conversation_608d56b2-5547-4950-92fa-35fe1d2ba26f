import { Activity } from '@sage/xtrem-core';
import { Site } from '../nodes/_index';

const commonOperations = [{ operations: ['read'], on: [() => Site] }];

export const site = new Activity({
    description: 'site',
    node: () => Site,
    __filename,
    permissions: ['read', 'create', 'update', 'delete'],
    operationGrants: {
        read: commonOperations,
        create: commonOperations,
        update: commonOperations,
        delete: commonOperations,
    },
});
