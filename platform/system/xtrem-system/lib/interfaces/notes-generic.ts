import { TextStream } from '@sage/xtrem-core';
import { MakeNodeType } from '@sage/xtrem-shared';

export interface SysNoteNodeInterface {
    _id: string | number;
    title: string;
    content: TextStream;
}

export interface SysNoteNode extends MakeNodeType<SysNoteNodeInterface> {}

export interface SysNoteAssociationInterface {
    _id: string | number;
    note: SysNoteNode;
}
export interface SysNoteAssociationNode extends MakeNodeType<SysNoteAssociationInterface> {}
