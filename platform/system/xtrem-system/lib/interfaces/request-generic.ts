import { Logger } from '@sage/xtrem-core';
import { AxiosResponseHeaders, Method } from 'axios';

export interface RequestGenericError {
    code: string;
    description: string;
    property?: string;
}

export interface RequestGenericConstructor {
    method?: Method;
    url?: string;
    data?: any;
    urlParameters?: string;
    logger?: Logger;
    noHeaders?: boolean;
}

export type RequestGenericResponse<T extends {}> = {
    data: T;
    error?: { message: string };
    errors: Array<RequestGenericError>;
    headers?: AxiosResponseHeaders;
    status: number;
    statusText: string;
};

export interface RequestGenericConfig {
    application: string;
    signingKey: string;
    authURL: string;
    apiURL: string;
    signatory: string;
    /** For id-validation-look-up */
    lookupURL: string;
    /** Current mode */
    apiMode: 'production' | 'sandbox';
    /** For v6 compliance services */
    v6ApiURL: string;
    v6Application: string;
    v6ApiKey: string;
}

export interface RequestGenericConfigFullPackage extends RequestGenericConfig {
    v6ApiKeySandbox: string;
    v6ApiKeyProd: string;
    /** All urls for testing and production */
    authURLSandbox: string;
    authURLProd: string;
    apiURLSandbox: string;
    apiURLProd: string;
    lookupURLSandbox: string;
    lookupURLProd: string;
    v6ApiURLProd: string;
    v6ApiURLSandbox: string;
}
