/* istanbul ignore file */

import * as axios from 'axios';
import * as fs from 'fs';
import * as fsp from 'path';
import { loggers } from '../loggers';

const logger = loggers.functions;

/**
 * WriteAxiosMocke Data to simplify the life of the dev
 * testAttributes: { createMockData: true }  as to be set to true scenario & directory as to be set ( graphql & mocha )
 * Create or update a json file which is the name of the scenario
 * @param request the request
 * @param response the answer made by
 * @param directory For mocha you can set directory with __dirname , for graphql you have to set it with the path of the graphql file
 * @param filename for mocha it's free for graphql it as to be the name of the scenario
 */
export function writeAxiosMockData(
    request: axios.AxiosRequestConfig,
    response: any,
    directory: string,
    filename: string,
): void {
    logger.debug(() => ` Writing axios : ${directory} ${filename} `);
    if (request.headers?.Authorization) delete request.headers.Authorization;
    let mockData;
    if (!fs.existsSync(fsp.join(directory, 'axios'))) fs.mkdirSync(fsp.join(directory, 'axios'));
    if (!fs.existsSync(fsp.join(directory, 'axios', `${filename}.json`))) {
        mockData = [];
    } else {
        logger.debug(() => `Try to open file : ${directory}/axios/${filename}.json`);
        mockData = JSON.parse(fs.readFileSync(fsp.join(directory, 'axios', `${filename}.json`), 'utf8').toString());
    }
    const { url, method, headers, timeout, params } = request;
    mockData.push({
        request: { url, method, headers, timeout, params, data: request.data },
        response: { headers: { isMock: true }, data: response.data },
    });
    fs.writeFileSync(fsp.join(directory, 'axios', `${filename}.json`), JSON.stringify(mockData, null, 4));
}
