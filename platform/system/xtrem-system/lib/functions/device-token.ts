import { nanoid } from 'nanoid';

class DeviceTokenLib {
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    private constructor() {}

    static instance: DeviceTokenLib = new DeviceTokenLib();

    // eslint-disable-next-line class-methods-use-this
    getTokenExpirationDate(): number {
        const d = new Date();
        const expirationTimeInMonths = 12;
        d.setMonth(d.getMonth() + expirationTimeInMonths);
        return Math.round(d.getTime() / 1000);
    }

    // eslint-disable-next-line class-methods-use-this
    generateTokenId(): string {
        return nanoid();
    }
}

export const deviceTokenLib = DeviceTokenLib.instance;
