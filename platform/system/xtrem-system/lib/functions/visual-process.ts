/* istanbul ignore file */
import { Uuid } from '@sage/xtrem-core';

// Configuration
const maxDocWidth = 800;
const cardWidth = 180;
const cardHeight = 60;
const cardPadding = 20;
const indent = 60;

const labelFontSize = 16;

const pictureOffset = 12;
const pictureSize = cardHeight - pictureOffset * 2;

/**
 * HTML encode a string to be displayed
 * @param str String to encode
 */
export function htmlEscape(str: string): string {
    return str
        .replace(/&/g, '&amp;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#39;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;');
}

/**
 * Returns an empty visual process document template.
 * @param docWidth Width of the document in pixels
 * @param docHeight Height of the document in pixels
 */
export const getEmptyDocument = (docWidth: number, docHeight: number) => ({
    acts: {},
    layersArr: [{ xpropsArr: [] as any[], alpha: 100, visible: true, lock: false, id: 0 }],
    reachedGroupNum: 1,
    contentSize: { xheight: docHeight, xwidth: docWidth },
    docDims: { xheight: docHeight, xwidth: docWidth, xtop: 0, xleft: 0 },
    currentLayerId: 1,
});

/**
 * Helper function that returns the width of the card based on the length of the text
 * @param titleLength Length of the text
 */
export function calcCardWidth(titleLength: number): number {
    // Use 2/3 of font size as an average for character width. Can be refactored to calculate actual rendered width later
    return titleLength < 12 ? cardWidth : cardWidth + (titleLength - 12) * ((2 / 3) * labelFontSize);
}

/**
 * Helper function that returns the longest width of the card based on the length of all the text to display
 * @param maxLength Longest length found
 * @param currentLength current evaluated length
 */
export function calcMaxWidth(maxLength: number, currentLength: number): number {
    return calcCardWidth(currentLength) + indent > maxLength ? calcCardWidth(currentLength) + indent : maxLength;
}

/**
 * Helper function that return a truncated text with 3 dots if the text length is greater than 25 characters
 * @param title text to be truncated if length is greater than 25
 */
export function truncateCardTitle(title: string): string {
    return title.length > 25 ? title.substring(0, 22).concat('...') : title;
}

/**
 *
 * @param title Title of the card
 * @param icon The icon which is displayed on the card, the icon is sourced from the DLS website:
 * https://designsystem.sage.com/foundations/icons/detailed-icons/
 * @param x Left position
 * @param y Top position
 */
export const createRectangle = (
    title: string,
    icon: string,
    x: number,
    y: number,
    current: boolean,
    width = cardWidth,
    height = cardHeight,
) => {
    const labelHeight = 20;
    const pictureX = x + pictureOffset;
    const pictureY = y + pictureOffset;
    const labelX = pictureX + pictureSize + 10;
    const labelY = y + 20;
    return [
        // Rectangle shape
        {
            xstrokeProps: {
                xend: { xtype: 'none' },
                xstart: { xtype: 'none' },
                xalpha: 100,
                xcolor: '#CCD6DB', // Slate 80
                xthickness: current ? 5 : 1,
                xtype: 'solidstroke',
            },
            xfillProps: {
                xgtype: 'linear',
                xalpha: 100,
                xcolor: '#E6EBED', // Slate 90
                xtype: 'solidfill',
            },
            xanchors: [
                { x, y },
                { x: x + width, y },
                { x: x + width, y: y + height },
                { x, y: y + height },
            ],
            xtextFormat: {
                letterSpacing: 0,
                display: 'block',
                blockIndent: 0,
                leading: 0,
                indent: 0,
                rightMargin: 5,
                leftMargin: 5,
                align: 'left',
                url: '',
                color: 0,
                size: labelFontSize,
                font: 'Arial',
            },
            xcaptionProps: {
                xsizingMode: 'fit',
                xvertAlignMode: 'middle',
            },
            xcaptionPos: {
                xleft: x,
                xtop: y + height / 2 - labelFontSize / 2,
            },
            xcaptionSize: {
                xwidth: width,
                xheight: height,
            },
            xcaptionDeltaPos: {
                x: 0,
                y: 0,
            },
            xshadowProps: { xtype: 'global' },
            xdrawBehaviorCode: 'K_API_RECT',
            xshapeType: 'apishape',
            uniqueID: Uuid.generate().toString(),
        },

        // label
        {
            xanchors: [
                { x: labelX, y: labelY },
                { x: labelX + width, y: labelY },
                { x: labelX + width, y: labelY + labelHeight },
                { x: labelX, y: labelY + labelHeight },
            ],
            xtextFormat: {
                letterSpacing: 0,
                display: 'block',
                blockIndent: 0,
                leading: 0,
                indent: 0,
                rightMargin: 5,
                leftMargin: 5,
                align: 'left',
                url: '',
                color: 0,
                size: labelFontSize,
                font: 'Arial',
            },
            xtext: title,
            xcaptionProps: {
                xsizingMode: 'fit',
                xvertAlignMode: 'middle',
            },
            xcaptionPos: {
                xleft: labelX,
                xtop: labelY + height / 2 - labelFontSize / 2,
            },
            xcaptionSize: {
                xwidth: width,
                xheight: height,
            },
            xcaptionDeltaPos: {
                x: 0,
                y: 0,
            },
            xshadowProps: { xtype: 'global' },
            xdrawBehaviorCode: 'K_API_RECT',
            xshapeType: 'apishape',
            uniqueID: Uuid.generate().toString(),
        },
        // Image shape
        {
            xsize: {
                xwidth: pictureSize,
                xheight: pictureSize,
            },
            xcenter: { x: 0, y: 0 },
            eltid: icon,
            bibid: 'business-icons',
            xactionProps: { xtype: 'none' },
            xlinkProps: { xtype: 'none' },
            xanchors: [
                { x: pictureX, y: pictureY },
                { x: pictureX + pictureSize, y: pictureY },
                { x: pictureX + pictureSize, y: pictureY + pictureSize },
                { x: pictureX, y: pictureY + pictureSize },
            ],
            xtext: '',
            xcaptionProps: { xsizingMode: 'fit', xvertAlignMode: 'middle' },
            xcaptionPos: { xleft: 0, xtop: 0 },
            xcaptionSize: { xwidth: 0, xheight: 0 },
            xcaptionDeltaPos: { x: 0, y: 0 },
            xshadowProps: { xtype: 'none' },
            xdrawBehaviorCode: null as any,
            xshapeType: 'fileshape',
            uniqueID: Uuid.generate().toString(),
        },
    ];
};

/**
 * Returns a document containing a hierarchy of versions for the supplied formula
 * @param formulaDescription string containing the description of a formula
 * @param formulaVersions array of formula versions
 * @param currentVersion string containing the version of the formula to highlight
 *
 * TO DO : move this function to the correct package
 */
export const generateFormulaHierarchyChart = (
    formulaDescription: string,
    formulaVersions: Partial<any>[],
    currentVersion: string,
): string => {
    const maxPerRow = Math.floor(maxDocWidth / (cardWidth + cardPadding));
    const numCardsWidth = formulaVersions.length > maxPerRow ? maxPerRow : formulaVersions.length;
    const emptyDocumentCardWidth = formulaVersions && formulaVersions.length > 1 ? 160 : cardWidth;
    const document = getEmptyDocument(
        cardPadding + (numCardsWidth > 0 ? numCardsWidth : 1) * (emptyDocumentCardWidth + cardPadding),
        cardHeight +
            cardPadding * 2 +
            (cardHeight + cardPadding) *
                (Math.floor(formulaVersions.length / maxPerRow) + (formulaVersions.length > 0 ? 1 : 0)),
    );

    const formulaText =
        formulaDescription.length > 14 ? formulaDescription.substring(0, 14).concat('...') : formulaDescription;

    if (formulaVersions && formulaVersions.length > 1) {
        // Generate a card for each version
        formulaVersions.forEach((formulaVersion, index: number) => {
            const row = Math.floor(index / maxPerRow);

            // Cards are drawn from the left per row
            const cardX = cardPadding + (index % maxPerRow) * (cardWidth + cardPadding);

            // We need to leave place for the formula card, so we need to leave the first line empty.
            const cardY = cardHeight + cardPadding * 2 + (cardHeight + cardPadding) * row;

            // Add the rectangle and the icon to the document's layer.
            document.layersArr[0].xpropsArr.push(
                ...createRectangle(
                    formulaVersion.version,
                    'chemical',
                    cardX,
                    cardY,
                    formulaVersion.version === currentVersion,
                    150,
                ),
            );
        });
    }

    const middlePoint = (numCardsWidth * (cardWidth + cardPadding)) / 2;
    const formulaX = cardPadding + (numCardsWidth > 0 ? middlePoint - cardWidth / 2 - cardPadding / 2 : 0);
    const formulaY = cardPadding;
    // Add the formula rectangle to the top
    document.layersArr[0].xpropsArr.push(...createRectangle(formulaText, 'lab', formulaX, formulaY, false));

    return JSON.stringify(document);
};
