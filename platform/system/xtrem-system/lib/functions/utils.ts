import { Node, NodeQueryFilter } from '@sage/xtrem-core';

// Function that generates a link to a filtered main list, taking advantage
// of the _filter parameter that can be used in the main list to filter the data.
// See https://github.com/Sage-ERP-X3/xtrem/blob/61c6bcde22e1c660adabb9602b6f60b77157f848/platform/front-end/xtrem-ui/lib/service/navigation-panel-service.ts#L90
export function linkToFilteredMainList<T extends Node>(targetPage: string, filter: NodeQueryFilter<T>) {
    return `${targetPage}/${btoa(JSON.stringify({ _filter: JSON.stringify(filter) }))}`;
}
