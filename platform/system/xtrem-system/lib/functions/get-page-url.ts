import { ConfigManager, Node } from '@sage/xtrem-core';

export function getBasePageUrl(page: string): string {
    const config = ConfigManager.current;
    const publicAppUrl = ConfigManager.current.publicAppUrl;
    if (config.deploymentMode === 'development' && !publicAppUrl) {
        const appUrl = config.apps?.[config.app || '']?.appUrl;
        const appPort = appUrl && parseInt(new URL(appUrl).port, 10);
        // If we are in a worker process allocate the port as 0 so that a dynamic port is allocated
        const port = appPort || config.server?.port;
        return `http://localhost:${port}/${page}`;
    }
    return `${publicAppUrl}/${page}`;
}

export function getRecordUrl(node: Node, page: string): string {
    const id = node._id;
    if (Number(id) <= 0) {
        return '';
    }
    const baseUrl = getBasePageUrl(page);
    return `${baseUrl}/${id}`;
}
