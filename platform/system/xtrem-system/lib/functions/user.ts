import { ConfigManager, Context } from '@sage/xtrem-core';
import * as crypto from 'crypto';
import { User } from '../nodes/_index';

// Match api user email like: <EMAIL>
const apiUserEmailRegexp = /^api-(\w[-\w]*)@localhost\.domain$/;

// Match operator user email like: <EMAIL>
const operatorUserEmailRegexp = /^operator-(\w[-\w]*)@localhost\.domain$/;

export const isApiEmail = (email: string) => apiUserEmailRegexp.test(email);

export const matchApiEmail = (email: string) => email.match(apiUserEmailRegexp);

export const isOperatorEmail = (email: string) => operatorUserEmailRegexp.test(email);

export const matchOperatorEmail = (email: string) => email.match(operatorUserEmailRegexp);

export const operatorIdHash = (context: Context, operatorId: string) => {
    if (ConfigManager.current.operatorUserHashSecret) {
        return crypto
            .createHmac('SHA256', ConfigManager.current.operatorUserHashSecret.toString())
            .update(`${context.tenantId}${context.app}${operatorId}`)
            .digest('base64');
    }

    throw new Error(`OperatorUserHashSecret is not defined in the configuration file`);
};

export const isOperatorCodeUnique = async (context: Context, operatorCode: string): Promise<boolean> => {
    const operatorId = operatorIdHash(context, operatorCode);
    const operatorIdCount = await context.queryCount(User, {
        filter: {
            operatorId,
        },
    });

    if (operatorIdCount !== 0) {
        return false;
    }

    return true;
};
