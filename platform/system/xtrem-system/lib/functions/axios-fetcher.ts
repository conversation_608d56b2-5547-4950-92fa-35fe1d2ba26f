import { Any<PERSON><PERSON><PERSON>, AnyValue, Context, Mocker, retry } from '@sage/xtrem-core';
import { AxiosRequestConfig, AxiosResponse } from 'axios';
import { XtremSystemConfig } from '../interfaces';
import { loggers } from '../loggers';
import { writeAxiosMockData } from './axios-mock';

const logger = loggers.functions;

/**
 * Axios call
 * @param axiosRequest AxiosRequestConfig
 * @param dataCleaner function to clean data send
 * @returns result
 */
export async function axiosFetcher<T extends AnyValue = AnyRecord>(
    context: Context,
    axiosRequest: AxiosRequestConfig,
    /** function to clean data before writing mocks  */ dataCleaner: (data: string) => string = data => data,
): Promise<AxiosResponse<T>> {
    // require axios using Mocker.get, if this is executed from a unit test and axios is mocked
    // this will return the mocked axios module
    const axios = Mocker.get('axios', require);

    const config = context?.configuration?.getPackageConfig<XtremSystemConfig>('@sage/xtrem-system');
    const { directory, testAttributes, mocks, scenario } = context?.testConfig || {};

    const maximumTries = config?.retry?.maximumTries;
    const timeoutBetweenTries = config?.retry?.timeoutBetweenTries;

    if (context?.testMode && context?.testConfig && mocks?.includes('axios')) {
        axiosRequest.data = dataCleaner(axiosRequest.data);
    }

    const response: any = await retry(() => axios(axiosRequest), {
        maxTries: maximumTries || 3,
        delayBeforeRetry: timeoutBetweenTries || 1000,
    });

    /**
     * Check if it is needed to create mock files :
     * As to be inserted in the platform side !
     */
    /* istanbul ignore next */
    if (context?.testMode && context?.testConfig) {
        if (testAttributes?.createMockData) {
            if (!directory || !scenario)
                throw new Error(
                    'Cannot generate axios mock data, both directory and scenario must be specified for test',
                );
            if (mocks?.includes('axios')) throw new Error('Cannot generate axios mock data, while mocking axios');
            /** To be able to clean data from passwords  */
            axiosRequest.data = dataCleaner(axiosRequest.data);
            writeAxiosMockData(axiosRequest, response, directory, scenario);
        }
        if (!mocks?.includes('axios')) {
            logger.error(() => 'The unit test has to pass by Axios Mock ! ');
        }
    }
    return response;
}

/**
 *  Fetcher for webService Calling
 * @param url : url used to call the webService
 */
export async function apiFetcher<T extends AnyValue>(url: string, context?: Context): Promise<T> {
    return (
        await axiosFetcher<T>(context || ({} as Context), { method: 'get', url, params: { limit: 1, format: 'json' } })
    ).data;
}
