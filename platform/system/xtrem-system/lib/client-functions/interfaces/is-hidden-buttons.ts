export interface BusinessButtonParameters<T> {
    parameters: T;
    recordId?: string;
    isDirty?: boolean;
}

export interface HeaderQuickActionButtonParameters<T> {
    parameters: T;
    recordId?: string;
}

export interface HeaderDropDownActionButtonParameters<T> {
    parameters?: T;
    recordId?: string;
}

export interface TableFieldActionsActionButtonParameters<T> {
    parameters?: T;
    recordId?: string;
}

export interface TableHeaderBusinessActions<T> {
    parameters?: T;
    recordId?: string;
}

export interface DeleteParameters<T> {
    parameters: T;
    recordId?: string;
}
