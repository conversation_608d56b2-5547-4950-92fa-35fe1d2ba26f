import type { ExtractEdges } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import { LocalizeLocale } from '@sage/xtrem-shared';
import * as ui from '@sage/xtrem-ui';
import { isString } from 'lodash';

export interface DynamicParameter {
    _id: string;
    type: string;
    name: string;
    title: string;
    node?: string;
    dataType?: {
        name: string;
        attributes: string | any;
    };
}

export interface DataTypeAttribute {
    type: string;
    bind: string;
    title: string;
    node?: string;
    enumType?: string;
    isMandatory?: boolean;
    dataType?: {
        name: string;
        attributes: string | any;
    };
}

export function addParametersToDynamicPod({
    dynamicPod,
    parameters,
    locale,
    isFullWidth,
}: {
    dynamicPod: ui.fields.DynamicPod;
    parameters: ExtractEdges<DynamicParameter>[];
    locale: LocalizeLocale;
    isFullWidth: boolean;
}) {
    dynamicPod.isDirty = false;

    const nestedFields = parameters.map(r => {
        const podField = (nestedFieldProps: DataTypeAttribute): ui.nestedFields.NestedField<any, any> => {
            let title = '';
            if (nestedFieldProps.title != null) {
                // It may happen that the title is not already set when we render the field
                title =
                    typeof nestedFieldProps.title === 'string'
                        ? nestedFieldProps.title
                        : nestedFieldProps.title[locale];
            }
            const parameterObject = {
                bind: nestedFieldProps.bind,
                title,
                isFullWidth: (nestedFieldProps as any).isFullWidth ?? isFullWidth ?? false,
                optionType: nestedFieldProps.enumType,
                isMandatory: nestedFieldProps.isMandatory,
            };

            const additionalFieldProperties: any = isString(nestedFieldProps.dataType?.attributes)
                ? JSON.parse(nestedFieldProps.dataType?.attributes || 'null')
                : nestedFieldProps.dataType?.attributes || {};

            switch (nestedFieldProps.type) {
                case 'boolean':
                    return ui.nestedFields.checkbox({
                        ...parameterObject,
                        ...additionalFieldProperties,
                    });
                case 'string':
                    return ui.nestedFields.text({
                        ...parameterObject,
                        ...additionalFieldProperties,
                    });
                case 'integer':
                case 'decimal':
                case 'double':
                case 'float':
                    return ui.nestedFields.numeric(parameterObject);
                case 'date':
                    return ui.nestedFields.date(parameterObject);
                case 'enum':
                    if (
                        !additionalFieldProperties.enumName &&
                        !additionalFieldProperties.optionType &&
                        !parameterObject.optionType &&
                        !additionalFieldProperties.options
                    ) {
                        throw new Error(`Enum based select field ${nestedFieldProps.bind} must have an option type.`);
                    }
                    if (
                        (!additionalFieldProperties.options || !additionalFieldProperties.optionType) &&
                        additionalFieldProperties.enumName
                    ) {
                        parameterObject.optionType = additionalFieldProperties.enumName;
                    }
                    return ui.nestedFields.select({
                        ...additionalFieldProperties,
                        ...parameterObject,
                    });
                case 'reference': {
                    if (!additionalFieldProperties.node) {
                        throw new Error(`Reference field ${nestedFieldProps.bind} must have a node property.`);
                    }

                    if (!additionalFieldProperties.value?.bind) {
                        throw new Error(`Reference field ${nestedFieldProps.bind} must have a value nested field.`);
                    }

                    const referenceParameterObject = {
                        ...parameterObject,
                        name: additionalFieldProperties.bind,
                        node: additionalFieldProperties.node,
                        valueField: additionalFieldProperties.value.bind,
                        columns: additionalFieldProperties.columns?.map(podField) || [],
                    };
                    return ui.nestedFields.reference(referenceParameterObject);
                }
                case 'dynamicSelect': {
                    return ui.nestedFields.dynamicSelect({
                        ...parameterObject,
                        ...additionalFieldProperties,
                    });
                }
                default:
                    return ui.nestedFields.text(parameterObject);
            }
        };
        return podField({ ...r, bind: r.name });
    });

    dynamicPod.setNestedFields(nestedFields);
}

export async function fillDynamicPod({
    graph,
    parameterNode,
    filter,
    dynamicPod,
    locale,
}: {
    graph: ui.GraphQLApi<any>;
    parameterNode: string;
    filter: unknown;
    dynamicPod: ui.fields.DynamicPod;
    locale: LocalizeLocale;
}): Promise<ExtractEdges<DynamicParameter>[]> {
    const parameters = extractEdges<DynamicParameter>(
        await graph.graph
            .node(parameterNode)
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        name: true,
                        type: true,
                        title: true,
                        isMandatory: true,
                        dataType: {
                            name: true,
                            attributes: true,
                        },
                    },
                    { filter },
                ),
            )
            .execute(),
    );

    addParametersToDynamicPod({ parameters, dynamicPod, locale, isFullWidth: true });

    return parameters;
}
