import { GraphApi, User } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';

export type CurrentUser = Pick<
    User,
    '_id' | 'displayName' | 'email' | 'firstName' | 'lastName' | 'isActive' | 'isAdministrator'
>;

export function getExecutionUser(page: ui.Page): Promise<CurrentUser | null> {
    const myPage = page as ui.Page<GraphApi>;
    if (!myPage.$.username) {
        return Promise.resolve(null);
    }
    return myPage.$.graph
        .node('@sage/xtrem-system/User')
        .read(
            {
                _id: true,
                email: true,
                firstName: true,
                lastName: true,
                isActive: true,
                isAdministrator: true,
                displayName: true,
            },
            myPage.$.username,
        )
        .execute();
}
