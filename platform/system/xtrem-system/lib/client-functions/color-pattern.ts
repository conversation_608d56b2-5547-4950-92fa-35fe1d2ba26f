import { Dict } from '@sage/xtrem-shared';
import { ColoredElement } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';

/**
 * This object maps statuses to their respective colors
 */
export const colorfulPillPattern: Dict<Required<ui.LabelFieldStyle>> = {
    filledNeutral: {
        backgroundColor: ui.tokens.colorsSemanticNeutral500,
        borderColor: ui.tokens.colorsSemanticNeutral500,
        textColor: ui.tokens.colorsSemanticNeutralYang100,
    },
    filledInformation: {
        backgroundColor: ui.tokens.colorsSemanticInfo500,
        borderColor: ui.tokens.colorsSemanticInfo500,
        textColor: ui.tokens.colorsSemanticInfoYang100,
    },
    filledPositive: {
        backgroundColor: ui.tokens.colorsSemanticPositive500,
        borderColor: ui.tokens.colorsSemanticPositive500,
        textColor: ui.tokens.colorsSemanticPositiveYang100,
    },
    filledNegative: {
        backgroundColor: ui.tokens.colorsSemanticNegative500,
        borderColor: ui.tokens.colorsSemanticNegative500,
        textColor: ui.tokens.colorsSemanticNegativeYang100,
    },
    filledCaution: {
        backgroundColor: ui.tokens.colorsSemanticCaution500,
        borderColor: ui.tokens.colorsSemanticCaution500,
        textColor: ui.tokens.colorsSemanticCautionYin090,
    },
    filledFocus: {
        backgroundColor: ui.tokens.colorsSemanticFocus500,
        borderColor: ui.tokens.colorsSemanticFocus500,
        textColor: ui.tokens.colorsSemanticNeutralYang100, // colorsSemanticFocusYang100 doesn't exist
    },
    filledClosing: {
        backgroundColor: ui.tokens.colorsGray1000,
        borderColor: ui.tokens.colorsGray1000,
        textColor: ui.tokens.colorsYang100,
    },
    outlinedNeutral: {
        backgroundColor: ui.tokens.colorsYang100,
        borderColor: ui.tokens.colorsSemanticNeutral500,
        textColor: ui.tokens.colorsSemanticNeutralYin090,
    },
    outlinedInformation: {
        backgroundColor: ui.tokens.colorsYang100,
        borderColor: ui.tokens.colorsSemanticInfo500,
        textColor: ui.tokens.colorsSemanticInfoYin090,
    },
    outlinedPositive: {
        backgroundColor: ui.tokens.colorsYang100,
        borderColor: ui.tokens.colorsSemanticPositive500,
        textColor: ui.tokens.colorsSemanticPositiveYin090,
    },
    outlinedNegative: {
        backgroundColor: ui.tokens.colorsYang100,
        borderColor: ui.tokens.colorsSemanticNegative500,
        textColor: ui.tokens.colorsSemanticNegativeYin090,
    },
    outlinedCaution: {
        backgroundColor: ui.tokens.colorsYang100,
        borderColor: ui.tokens.colorsSemanticCaution500,
        textColor: ui.tokens.colorsSemanticCautionYin090,
    },
    outlinedFocus: {
        backgroundColor: ui.tokens.colorsYang100,
        borderColor: ui.tokens.colorsSemanticFocus500,
        textColor: ui.tokens.colorsSemanticNeutralYin090, // colorsSemanticFocusYin090 doesn't exist
    },
    outlinedClosing: {
        backgroundColor: ui.tokens.colorsYang100,
        borderColor: ui.tokens.colorsGray1000,
        textColor: ui.tokens.colorsSemanticNeutralYin090, // colorsSemanticClosingYin090 doesn't exist
    },
    default: {
        backgroundColor: ui.tokens.colorsUtilityMajor200,
        borderColor: ui.tokens.colorsUtilityMajor200,
        textColor: ui.tokens.colorsSemanticNeutralYang100,
    },
    transparent: {
        backgroundColor: ui.tokens.colorsTransparent,
        borderColor: ui.tokens.colorsTransparent,
        textColor: ui.tokens.colorsTransparent,
    },
    textBox: {
        backgroundColor: ui.tokens.colorsTransparent,
        borderColor: ui.tokens.colorsTransparent,
        textColor: ui.tokens.colorsGray000,
    },
};

export function colorfulPillPatternDefaulted(coloredElement: ColoredElement): string {
    return colorfulPillPattern.default[coloredElement];
}

/**
 *  To have label fields as text fields
 * @param coloredElement  backgroundColor / borderColor / textColor
 * @returns
 */
export function labelAsText(coloredElement: ColoredElement) {
    return colorfulPillPattern.textBox[coloredElement];
}
