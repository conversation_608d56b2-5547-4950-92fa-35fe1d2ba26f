export function getApplicativePageTitle(
    displayRecordKey: string,
    recordKeySeparator: string = '/',
    ...otherDisplayKeys: string[]
) {
    let displayTitle = displayRecordKey ?? '';

    if (displayTitle && otherDisplayKeys) {
        displayTitle = displayTitle.concat(
            otherDisplayKeys.reduce((prev, curr) => prev.concat(curr ? `${recordKeySeparator}${curr}` : ''), ''),
        );
    }
    return displayTitle;
}
