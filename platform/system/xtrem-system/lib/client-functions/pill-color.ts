import { ServiceOptionStatus } from '@sage/xtrem-shared';
import { ColoredElement } from '@sage/xtrem-system-api';
import { LabelFieldStyle } from '@sage/xtrem-ui';
import { colorfulPillPattern, colorfulPillPatternDefaulted } from './color-pattern';

function ServiceOptionStatusColor(status: ServiceOptionStatus, coloredElement: ColoredElement): string {
    switch (status) {
        case 'workInProgress':
            return colorfulPillPattern.filledCaution[coloredElement];
        case 'experimental':
            return colorfulPillPattern.filledInformation[coloredElement];
        case 'released':
            return colorfulPillPattern.filledPositive[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

export function getLabelColorByStatus(enumEntry: string, status?: ServiceOptionStatus | null): LabelFieldStyle {
    const getColor = (coloredElement: ColoredElement): string => {
        switch (enumEntry) {
            case 'ServiceOptionStatus':
                return ServiceOptionStatusColor(status as ServiceOptionStatus, coloredElement);
            default:
                return colorfulPillPatternDefaulted(coloredElement);
        }
    };

    return {
        backgroundColor: getColor('backgroundColor'),
        borderColor: getColor('borderColor'),
        textColor: getColor('textColor'),
    };
}

export function setBooleanStatusColors(booleanEntry: string, status: boolean, coloredElement: ColoredElement): string {
    switch (booleanEntry) {
        default:
            return colorfulPillPatternDefaulted(coloredElement);
    }
}
