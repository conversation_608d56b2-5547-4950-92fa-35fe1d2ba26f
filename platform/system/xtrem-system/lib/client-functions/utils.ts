import * as ui from '@sage/xtrem-ui';
import { ClientDiagnoseSeverity } from '@sage/xtrem-client';

// Temporary function to get service option data while not implemented in the framework
export async function getServiceOptionsInfo(pageInstance: ui.Page | ui.Sticker) {
    try {
        const response = await pageInstance.$.graph.raw('{serviceOptions{name,isActive,status}}', false, true);
        return response.serviceOptions as Array<{ name: string; isActive: boolean; status: string }>;
    } catch (err) {
        // eslint-disable-next-line no-console
        console.error(err);
        return [];
    }
}

/**
 * Wraps confirm dialogue calls into a boolean promise
 * @param dialogPromise to be wrapped
 * @returns a boolean having false for exit or cancel dialog finish action.
 */
export function confirmDialogToBoolean(dialogPromise: Promise<any>): Promise<boolean> {
    return dialogPromise
        .then(() => true)
        .catch(err => {
            if (err) {
                throw err;
            }
            return false;
        });
}

export function formatError(page: ui.Page, error: string | (Error & { errors: Array<any> })): string {
    page.$.loader.isHidden = true;
    if (typeof error === 'string') {
        return error;
    }
    if (error.errors?.length) {
        const errorMessageLines: string[] = [];
        error.errors.forEach(e => {
            errorMessageLines.push(`**${e.message}**`);
            if (e.extensions?.diagnoses) {
                e.extensions.diagnoses.forEach((d: any) => {
                    if (
                        d.severity === ClientDiagnoseSeverity.error ||
                        d.severity === ClientDiagnoseSeverity.exception
                    ) {
                        errorMessageLines.push(` - ${d.message}`);
                    }
                });
            }
        });
        if (errorMessageLines.length === 1) {
            return `${errorMessageLines[0].replace(/\*/g, '')}`;
        }
        return errorMessageLines.join('\n');
    }

    return error.message;
}
