import * as ui from '@sage/xtrem-ui';
import * as SystemUtils from './utils';

export async function areDevToolActive(pageOrStickerInstance: ui.Page | ui.Sticker, update = false) {
    let areDevToolsActive: string | boolean | undefined = pageOrStickerInstance.$.storage.get(
        'are-dev-tools-active',
    ) as string;

    // check if the info are up to date
    if (areDevToolsActive && !update) {
        return JSON.parse(areDevToolsActive as string) as boolean;
    }

    // query options info
    const foundOptions = await SystemUtils.getServiceOptionsInfo(pageOrStickerInstance);
    // read info corresponding to the devTools option
    areDevToolsActive = !!foundOptions;

    areDevToolsActive = areDevToolsActive && foundOptions.length > 0;

    const devToolOption = foundOptions.find(option => option.name === 'devTools');

    areDevToolsActive = areDevToolsActive && devToolOption?.isActive;
    // save result
    pageOrStickerInstance.$.storage.set('are-dev-tools-active', JSON.stringify(areDevToolsActive));

    return areDevToolsActive;
}
