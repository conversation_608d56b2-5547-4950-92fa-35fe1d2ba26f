import * as ui from '@sage/xtrem-ui';
import { DeleteParameters } from './interfaces/common';
import type { PageOrWidgetType } from './interfaces/common';
import { GraphApi } from '@sage/xtrem-system-api';

export async function deleteRecord<K>(pageInstance: PageOrWidgetType, parameters: DeleteParameters<K>) {
    if (
        await confirmDialogWithAcceptButtonText(
            pageInstance,
            ui.localize('@sage/xtrem-system/pages__delete_page_dialog_title', 'Confirm delete'),
            ui.localize('@sage/xtrem-system/pages__delete_page_dialog_content', 'You are about to delete this record.'),
            ui.localize('@sage/xtrem-system/pages-confirm-delete', 'Delete'),
        )
    ) {
        if (pageInstance instanceof ui.widgets.TableWidget) {
            try {
                await pageInstance.$.graph.node(String(parameters.nodeName)).deleteById(parameters._id).execute();
                pageInstance.$.refreshWidget();
                successToast(pageInstance);
            } catch (error) {
                pageInstance.$.showToast(error.message, { type: 'error' });
            }
        } else {
            await pageInstance.$.graph.delete({
                _id: parameters._id,
                nodeName: parameters.nodeName as keyof GraphApi | undefined,
            });
            successToast(pageInstance);
        }
    }
}

function successToast(pageInstance: PageOrWidgetType) {
    pageInstance.$.showToast(ui.localize('@sage/xtrem-system/delete-confirmation', 'Record deleted'), {
        type: 'success',
    });
}

export function confirmDialogWithAcceptButtonText(
    pageInstance: PageOrWidgetType,
    title: string,
    message: string,
    acceptButtonText: string,
) {
    const options = {
        acceptButton: { text: acceptButtonText, isDestructive: true },
        cancelButton: { text: ui.localize('@sage/xtrem-system/pages-confirm-cancel', 'Cancel') },
    };
    return pageInstance.$.dialog
        .confirmation('warn', title, message, options)
        .then(() => true)
        .catch(() => false);
}
