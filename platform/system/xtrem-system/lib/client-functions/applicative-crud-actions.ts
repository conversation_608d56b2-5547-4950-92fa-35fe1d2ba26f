import * as ui from '@sage/xtrem-ui';

export type PageButtonActions<Context extends ui.Page> = Array<ui.PageAction<Context>>;
export type PageButtonActionsOrSeparators<Context extends ui.Page> = Array<
    ui.PageAction<Context> | ReturnType<typeof ui.menuSeparator>
>;

/**
 * Run the standard cancel action and close the page if it is opened in the sidebar
 * @param pageInstance the page where save is triggered
 */
export async function cancelAndClosePanel<PageInstance extends ui.Page>(pageInstance: PageInstance) {
    try {
        // If the page is in the sidebar, in case of error, the message is not well managed by the framework
        // => it's better to manage it here
        if (pageInstance.$.queryParameters.called) {
            pageInstance.$.finish(pageInstance.$.values);
        } else {
            await pageInstance.$standardCancelAction.execute(!pageInstance.$.queryParameters.called);
        }
    } catch (err) {
        pageInstance.$.showToast(err.message, { type: 'error' });
        pageInstance.$.loader.isHidden = true;
    }
}

/**
 * Run the standard save action and close the page if it is opened in the sidebar
 * @param pageInstance the page where save is triggered
 */
export async function saveAndClosePanel<PageInstance extends ui.Page>(pageInstance: PageInstance) {
    try {
        // If the page is in the sidebar, in case of error, the message is not well managed by the framework
        // => it's better to manage it here
        await pageInstance.$standardSaveAction.execute(!pageInstance.$.queryParameters.called);
        if (pageInstance.$.queryParameters.called) {
            pageInstance.$.finish(pageInstance.$.values);
        }
    } catch (err) {
        pageInstance.$.showToast(err.message, { type: 'error' });
        pageInstance.$.loader.isHidden = true;
    }
}

export interface AppPageCrudActionArgs<Context extends ui.Page = ui.Page> {
    page: Context;
    isDirty?: boolean;
    isNewRecord?: boolean;
    create?: Array<ui.PageAction<Context>> | ui.PageAction<Context>;
    cancel?: Array<ui.PageAction<Context>> | ui.PageAction<Context>;
    remove?: Array<ui.PageAction<Context>> | ui.PageAction<Context>;
    duplicate?: Array<ui.PageAction<Context>> | ui.PageAction<Context>;
    save?: Array<ui.PageAction<Context>> | ui.PageAction<Context>;
    actions?: Array<ui.PageAction<Context>>;
}
/**
Manage activation of CRUD buttons
If the page is opened in the sidebar, the buttons Save and New are hidden
@param page: the page to manage
@param values: default value = page.$.values
@param isDirty: default value = page.$.isDirty,
@param cb: callback
*/
export function setApplicativePageCrudActions<Context extends ui.Page = ui.Page>({
    page,
    isDirty = page.$.isDirty,
    isNewRecord = false,
    cancel = [],
    remove = [],
    duplicate = [],
    save = [],
    actions = [],
}: AppPageCrudActionArgs<Context>) {
    const cancelActions: Array<ui.PageAction<Context>> = Array.isArray(cancel) ? cancel : [cancel];
    const removeActions: Array<ui.PageAction<Context>> = Array.isArray(remove) ? remove : [remove];
    const duplicateActions: Array<ui.PageAction<Context>> = Array.isArray(duplicate) ? duplicate : [duplicate];
    const saveActions: Array<ui.PageAction<Context>> = Array.isArray(save) ? save : [save];

    const recordId = page.$.recordId;
    const pageAsPanel = Boolean(page.$.queryParameters?.called);

    // The cancel and save actions are only visible when the page is dirty
    if (!page.$.isInDialog) {
        [...cancelActions, ...saveActions].forEach(action => {
            if (isNewRecord) {
                action.isDisabled = !isDirty;
            } else {
                action.isHidden = !isDirty;
            }
        });
    } else {
        // In dialogs the cancel action is always available
        saveActions.forEach(action => {
            action.isDisabled = !isDirty;
        });
    }

    // The footer business actions are only visible when the page is not dirty
    actions.forEach(action => {
        if (!cancelActions.includes(action) && !saveActions.includes(action)) {
            action.isHidden = isDirty;
        }
    });

    // Duplicate and delete action are only available if the page is NOT dirty, the user should only duplicate or delete clean records
    [...duplicateActions, ...removeActions].forEach(action => {
        if (!recordId) {
            // Moreover only records that have already been saved to the database can be duplicated or deleted
            action.isHidden = true;
        } else {
            action.isDisabled = !!isDirty;
            action.isHidden = false;
        }
    });

    // Hide all actions except Save in panels, overrides previous logic from above, this condition must be at the end
    if (pageAsPanel) {
        [...removeActions, ...duplicateActions, ...cancelActions].forEach(action => {
            action.isHidden = true;
        });
    }
}

function getActions<Context extends ui.Page>(
    actions: PageButtonActionsOrSeparators<Context> | ui.PageAction<Context>,
): PageButtonActionsOrSeparators<Context> {
    return Array.isArray(actions) ? actions : [actions];
}

export interface DisplayOfCommonPageActions<Context extends ui.Page = ui.Page> {
    page: ui.Page;
    isDirty?: boolean;
    cancel?: PageButtonActions<Context> | ui.PageAction<Context>;
    remove?: PageButtonActions<Context> | ui.PageAction<Context>;
    duplicate?: PageButtonActions<Context> | ui.PageAction<Context>;
    save?: PageButtonActions<Context> | ui.PageAction<Context>;
    businessActions?: PageButtonActions<Context> | ui.PageAction<Context>;
}

/**
Manage default display of most page common actions
    @param page:  page
    @param isDirty: dirty state
    @param cancel: the cancel action
    @param remove: the remove action
    @param duplicate: the duplicate action
    @param save: the save action
    @param businessActions: all other business actions
*/
export function setDisplayOfCommonPageActions<Context extends ui.Page = ui.Page>({
    page,
    isDirty = page.$.isDirty,
    cancel = [],
    remove = [],
    duplicate = [],
    save = [],
    businessActions = [],
}: DisplayOfCommonPageActions<Context>) {
    const cancelActions = getActions(cancel) as PageButtonActions<Context>;
    const removeActions = getActions(remove) as PageButtonActions<Context>;
    const duplicateActions = getActions(duplicate) as PageButtonActions<Context>;
    const saveActions = getActions(save) as PageButtonActions<Context>;
    const otherActions = getActions(businessActions) as PageButtonActions<Context>;

    const isPageAsPanel = Boolean(page.$.queryParameters?.called);
    const isNewRecord = !page.$.recordId;

    // Cancel & Save management
    [...cancelActions, ...saveActions].forEach(action => {
        if (isDirty) {
            action.isHidden = false;
            action.isDisabled = false;
        } else if (isNewRecord) {
            action.isHidden = false;
            action.isDisabled = true;
        } else {
            action.isHidden = true;
            action.isDisabled = true;
        }
    });

    // Other footer BusinessActions management
    if (isDirty || isNewRecord) {
        otherActions.forEach(action => {
            action.isHidden = true;
            // action.isDisabled = true; // never manage isDisabled for other business actions: they are only displayed/hidden
        });
    }

    // Duplicate and delete action are only available if the page is NOT dirty, the user should only duplicate or delete clean records
    [...duplicateActions, ...removeActions].forEach(action => {
        if (isNewRecord) {
            // Moreover only records that have already been saved to the database can be duplicated or deleted
            action.isHidden = true;
        } else {
            action.isDisabled = !!isDirty;
            action.isHidden = false;
        }
    });

    // Hide all actions except Save in panels, overrides previous logic from above, this condition must be at the end
    if (isPageAsPanel) {
        [...removeActions, ...duplicateActions, ...cancelActions].forEach(action => {
            action.isHidden = true;
        });
    }
}

/**
Manage order of business action buttons
@param cancel: the cancel action
@param save: the save action
@param businessActions: all other business actions
*/
export function setOrderOfPageBusinessActions<Context extends ui.Page = ui.Page>({
    cancel = [],
    save = [],
    businessActions = [],
}: {
    cancel: PageButtonActions<Context> | ui.PageAction<Context>;
    save: PageButtonActions<Context> | ui.PageAction<Context>;
    businessActions: PageButtonActions<Context> | ui.PageAction<Context>;
}): PageButtonActions<Context> {
    const cancelActions = getActions(cancel) as PageButtonActions<Context>;
    const saveActions = getActions(save) as PageButtonActions<Context>;
    const otherActions = getActions(businessActions) as PageButtonActions<Context>;

    return [...otherActions.reverse(), ...cancelActions, ...saveActions];

    // intentionally left commented
    // const uxOrderRule: number = 1; // a rule defined by the UX team, default one is the 1st
    // switch (uxOrderRule) {
    //     default:
    //     case 1:
    //         return [...otherActions.reverse(), ...cancelActions, ...saveActions];
    //     case 2:
    //         return [...otherActions, ...cancelActions, ...saveActions];
    //     case 3:
    //         return [...saveActions, ...cancelActions, ...otherActions.reverse()];
    //     case 4:
    //         return [...saveActions, ...cancelActions, ...otherActions];
    // }
}

/**
Manage order of header quick action buttons
@param duplicate: the duplicate action
@param quickActions: all other quick actions
*/
export function setOrderOfPageHeaderQuickActions<Context extends ui.Page = ui.Page>({
    duplicate = [],
    quickActions = [],
}: {
    duplicate?: PageButtonActions<Context> | ui.PageAction<Context>;
    quickActions?: PageButtonActions<Context> | ui.PageAction<Context>;
}): PageButtonActions<Context> {
    const duplicateActions = getActions(duplicate) as PageButtonActions<Context>;
    const listActions = getActions(quickActions) as PageButtonActions<Context>;
    const returnedActions: PageButtonActions<Context> = [...duplicateActions, ...listActions];
    return [...returnedActions.reverse()];
}

/**
Manage order of header dropdown action buttons
@param remove: the delete action
@param dropDownBusinessActions: all other dropdown actions and separators
*/
export function setOrderOfPageHeaderDropDownActions<Context extends ui.Page = ui.Page>({
    remove = [],
    dropDownBusinessActions = [],
}: {
    remove?: PageButtonActions<Context> | ui.PageAction;
    dropDownBusinessActions?: PageButtonActionsOrSeparators<Context> | ui.PageAction;
}): PageButtonActionsOrSeparators<Context> {
    const removeActions = getActions(remove);
    const listActions = getActions(dropDownBusinessActions);
    if (removeActions.length > 0) {
        return [...removeActions, ui.menuSeparator(), ...listActions].reverse();
    }
    return [...listActions].reverse();
}

/**
Manage order of table action buttons
@param actions: all table actions
*/
export function setOrderOfPageTableFieldActions<Context extends ui.Page = ui.Page>({
    actions = [],
}: {
    actions: PageButtonActions<Context> | ui.PageAction<Context>;
}): PageButtonActions<Context> {
    const returnedActions = getActions(actions);
    return [...returnedActions] as PageButtonActions<Context>;
    // intentionally left commented
    // return [...returnedActions.reverse()];
}

/**
Manage order of table header business action buttons
@param actions: all table actions
*/
export function setOrderOfPageTableHeaderBusinessActions<Context extends ui.Page = ui.Page>({
    actions = [],
}: {
    actions: PageButtonActions<Context> | ui.PageAction<Context>;
}): PageButtonActions<Context> {
    const returnedActions = getActions(actions);
    return [...(returnedActions.reverse() as PageButtonActions<Context>)];
}
