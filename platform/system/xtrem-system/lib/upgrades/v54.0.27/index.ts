import { SysClientNotification } from '../../nodes/sys-client-notification';
import { SchemaRenamePropertyAction, UpgradeSuite } from '../../services/upgrade';

export const upgradeSuite = new UpgradeSuite({
    actions: [
        new SchemaRenamePropertyAction({
            node: () => SysClientNotification,
            oldPropertyName: 'receipient',
            newPropertyName: 'recipient',
        }),
    ],
});
