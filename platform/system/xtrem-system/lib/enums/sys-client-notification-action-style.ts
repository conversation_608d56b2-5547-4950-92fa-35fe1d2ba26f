import { EnumDataType } from '@sage/xtrem-core';

export enum SysClientNotificationActionStyleEnum {
    primary,
    secondary,
    tertiary,
    link,
}

export type SysClientNotificationActionStyle = keyof typeof SysClientNotificationActionStyleEnum;

export const sysClientNotificationActionStyleDataType = new EnumDataType<SysClientNotificationActionStyle>({
    enum: SysClientNotificationActionStyleEnum,
    filename: __filename,
});
