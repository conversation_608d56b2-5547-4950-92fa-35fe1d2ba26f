import { EnumDataType } from '@sage/xtrem-core';

export enum UserImportExportDateFormatEnum {
    isoDash, // yyyy-mm-dd
    isoSlash, // yyyy/mm/dd
    europeanDash, // dd-mm-yyyy
    europeanSlash, // dd/mm/yyyy
    usDash, // mm-dd-yyyy
    usSlash, // mm/dd/yyyy
}

export type UserImportExportDateFormat = keyof typeof UserImportExportDateFormatEnum;

export const userImportExportDateFormatDataType = new EnumDataType<UserImportExportDateFormat>({
    enum: UserImportExportDateFormatEnum,
    filename: __filename,
});
