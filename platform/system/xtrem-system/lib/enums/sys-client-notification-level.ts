import { EnumDataType } from '@sage/xtrem-core';

export enum SysClientNotificationLevelEnum {
    error,
    warning,
    info,
    success,
    approval,
}

export type SysClientNotificationLevel = keyof typeof SysClientNotificationLevelEnum;

export const sysClientNotificationLevelDataType = new EnumDataType<SysClientNotificationLevel>({
    enum: SysClientNotificationLevelEnum,
    filename: __filename,
});
