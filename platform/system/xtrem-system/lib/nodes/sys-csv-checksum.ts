/** @ignore */ /** */
import { Node, decorators, nanoIdDataType } from '@sage/xtrem-core';
import * as dataTypes from '../data-types/data-types';

/**
 * This node keeps track of the checksum of the last successfully reload CSV for a node
 *
 * Every time a CSV file is loaded, its checksum is stored. This allow
 * the system to skip the next loading of the same CSV file, if it was not changed.
 */

@decorators.node<SysCsvChecksum>({
    storage: 'sql',
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDeleteMany: false,
    isPlatformNode: true,
    /**
     * Note: This node is not sharedByAllTenant so it will not have any _tenantId property but will have a tenantId
     * property to store the tenant on which the CSV was reloaded (or '' when the CSV concerns a shared table)
     */
    isSharedByAllTenants: true,
    indexes: [
        {
            orderBy: { tenantId: +1, factoryName: +1 },
            isUnique: true,
        },
    ],
})
export class SysCsvChecksum extends Node {
    /**
     * the tenantId on which the CSV was reloaded.
     * Will be '' when the reloaded CSV stands for a sharedTable.
     */
    @decorators.stringProperty<SysCsvChecksum, 'tenantId'>({
        isStored: true,
        dataType: () => nanoIdDataType,
    })
    readonly tenantId: Promise<string>;

    /**
     * The name of the factory
     */
    @decorators.stringProperty<SysCsvChecksum, 'factoryName'>({
        isPublished: true,
        isStored: true,
        isFrozen: true,
        dataType: () => dataTypes.name,
    })
    readonly factoryName: Promise<string>;

    /**
     * The checksum of the CSV file
     */
    @decorators.stringProperty<SysCsvChecksum, 'checksum'>({
        isPublished: true,
        isStored: true,
        dataType: () => dataTypes.checksumDataType,
    })
    readonly checksum: Promise<string>;
}
