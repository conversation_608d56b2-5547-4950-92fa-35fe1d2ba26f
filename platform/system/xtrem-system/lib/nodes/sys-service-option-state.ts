/** @ignore */ /** */
import {
    BinaryStream,
    ConfigManager,
    Context,
    CoreHooks,
    decorators,
    Node,
    NodeStatus,
    Reference,
} from '@sage/xtrem-core';
import { loggers } from '../loggers';
import { SysServiceOptionManager } from '../services/service-option-manager';
import { SysServiceOption } from './sys-service-option';

const CLUSTER_CI_V2_ID = 'ci-v2';

const logger = loggers.application;
logger.logLevel = 'debug';

/** @internal */
@decorators.node<SysServiceOptionState>({
    isPublished: true,
    storage: 'sql',
    isCached: true,
    canCreate: false,
    canRead: true,
    canUpdate: true,
    canDeleteMany: false,
    isPlatformNode: true,
    isPlatformNodeExportable: true,
    indexes: [
        {
            orderBy: { serviceOption: +1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    async controlBegin(cx) {
        if (await this.isAvailable) {
            await cx.error.if(await this.isActivable).is.false();

            await cx.error
                .if(
                    ConfigManager.current.deploymentMode === 'production' &&
                        ConfigManager.current.clusterId !== CLUSTER_CI_V2_ID &&
                        (await (
                            await this.serviceOption
                        ).status),
                )
                .is.equal.to('workInProgress');
        }
    },

    async saveEnd() {
        const { context } = this.$;

        const serviceOptionName = await (await this.serviceOption).optionName;

        const serviceOption = context.application.findServiceOption(serviceOptionName);

        const isEnabled = await context.isServiceOptionEnabled(serviceOption, { noCache: true });
        await context.serviceOptionManager.setServiceOptionActive(context, serviceOption, isEnabled);

        // Service option states will always be created in the upgrade
        if (serviceOption.notifies && this.$.status !== NodeStatus.added) {
            const oldActiveFlag = await (await this.$.old).isActive;
            const currentActiveFlag = await this.isActive;
            if (oldActiveFlag !== currentActiveFlag) {
                if (currentActiveFlag) {
                    // If service option name is foo
                    // we notify on topic SysServiceOptionState/foo/activate
                    await CoreHooks.communicationManager.notify(
                        context,
                        `SysServiceOptionState/${serviceOptionName}/activate`,
                        { serviceOptionName },
                    );
                } else {
                    // If service option name is foo
                    // we notify on topic SysServiceOptionState/foo/deactivate
                    await CoreHooks.communicationManager.notify(
                        context,
                        `SysServiceOptionState/${serviceOptionName}/deactivate`,
                        { serviceOptionName },
                    );
                }
            }
        }

        // when the isDemoTenant service option is activated, we check if the demo admin persona exists and we create it if not.
        if (isEnabled && serviceOption.name === this.$.context.application.serviceOptionsByName.isDemoTenant?.name) {
            await Context.accessRightsManager.ensureAdminPersonaCreated(this.$.context);
        }
    },
})
export class SysServiceOptionState extends Node {
    /**
     * Can the option be activated for this tenant.
     */
    @decorators.booleanProperty<SysServiceOptionState, 'isActivable'>({
        isPublished: true,
        isStored: true,
    })
    readonly isActivable: Promise<boolean>;

    /**
     * Is the option available ?
     */
    @decorators.booleanProperty<SysServiceOptionState, 'isAvailable'>({
        isPublished: true,
        dependsOn: ['isActive'],
        async computeValue() {
            return (await this.isReadyToUse) ? this.isActive : false;
        },
    })
    readonly isAvailable: Promise<boolean>;

    @decorators.booleanProperty<SysServiceOptionState, 'isReadyToUse'>({
        isPublished: true,
        async computeValue() {
            return SysServiceOptionManager.isServiceOptionStatusEnabled(await (await this.serviceOption).status);
        },
    })
    readonly isReadyToUse: Promise<boolean>;

    /**
     * Did the tenant activate it (implies isActivable is true).
     */
    @decorators.booleanProperty<SysServiceOptionState, 'isActive'>({
        isPublished: true,
        isStored: true,
        async control(_cx, val) {
            const { context } = this.$;
            const serviceOption = context.application.findServiceOption(await (await this.serviceOption).optionName);
            const oldState = this.$.status === NodeStatus.modified ? await (await this.$.old).isActive : undefined;

            // If service option state changed, trigger relevant callback if any
            // Any service option specific business logic should be done in the service option callback and throw
            // an exception if the service option cannot be enabled/disabled.
            if (oldState !== undefined && oldState !== val) {
                if (val) {
                    if (serviceOption.onEnabled) {
                        await serviceOption.onEnabled(context);
                    }
                } else if (serviceOption.onDisabled) {
                    await serviceOption.onDisabled(context);
                }
            }
        },
    })
    readonly isActive: Promise<boolean>;

    @decorators.referenceProperty<SysServiceOptionState, 'serviceOption'>({
        isPublished: true,
        isStored: true,
        node: () => SysServiceOption,
    })
    readonly serviceOption: Reference<SysServiceOption>;

    @decorators.booleanProperty<SysServiceOptionState, 'isHiddenByConfig'>({
        isPublished: true,
        async getValue(): Promise<boolean> {
            return (
                (Context.getConfigurationValue('serviceOptionsLevel') === 'released' &&
                    (await (await this.serviceOption).status) !== 'released') ||
                (Context.getConfigurationValue('serviceOptionsLevel') === 'experimental' &&
                    (await (await this.serviceOption).status) === 'workInProgress')
            );
        },
    })
    readonly isHiddenByConfig: Promise<boolean>;

    /**
     * Is the package active
     * TODO: remove
     */
    @decorators.booleanProperty<SysServiceOptionState, 'isPackageActive'>({
        isPublished: true,
        getValue() {
            return true;
        },
    })
    readonly isPackageActive: Promise<boolean>;

    @decorators.binaryStreamProperty<SysServiceOptionState, 'imageStatus'>({
        isNullable: true,
        isPublished: true,
        async computeValue() {
            if ((await (await this.serviceOption).status) !== 'experimental')
                return BinaryStream.fromBuffer(
                    Buffer.from(
                        '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',
                        'base64',
                    ),
                );

            return BinaryStream.fromBuffer(
                Buffer.from(
                    '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**********************************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*******************************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',
                    'base64',
                ),
            );
        },
    })
    readonly imageStatus: Promise<BinaryStream | null>;
}
