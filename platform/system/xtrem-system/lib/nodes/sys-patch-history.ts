import { decorators, EnumDataType, Node, StringDataType } from '@sage/xtrem-core';

enum PatchHistoryResultEnum {
    success = 1,
    running,
    failure,
}

export type PatchHistoryResult = keyof typeof PatchHistoryResultEnum;

const patchHistoryResultEnumDataType = new EnumDataType<PatchHistoryResult>({
    enum: PatchHistoryResultEnum,
    filename: __filename,
});

const patchNameDataType = new StringDataType({ maxLength: 100 });
const versionDataType = new StringDataType({ maxLength: 100 });
const packageNameDataType = new StringDataType({ maxLength: 100 });

@decorators.node<SysPatchHistory>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canRead: true,
    canUpdate: true,
    isPlatformNode: true,
    indexes: [{ orderBy: { packageName: 1, version: 1, patchName: 1 }, isUnique: true }],
})
export class SysPatchHistory extends Node {
    /**
     * The name of the data-patch
     */
    @decorators.stringProperty<SysPatchHistory, 'patchName'>({
        isPublished: true,
        isStored: true,
        dataType: () => patchNameDataType,
    })
    readonly patchName: Promise<string>;

    /**
     * The version of the data patch
     */
    @decorators.stringProperty<SysPatchHistory, 'version'>({
        isPublished: true,
        isStored: true,
        dataType: () => versionDataType,
    })
    readonly version: Promise<string>;

    /**
     * The name of the package
     */
    @decorators.stringProperty<SysPatchHistory, 'packageName'>({
        isPublished: true,
        isStored: true,
        dataType: () => packageNameDataType,
    })
    readonly packageName: Promise<string>;

    /**
     * The result of the execution of the data-patch
     */
    @decorators.enumProperty<SysPatchHistory, 'result'>({
        isPublished: true,
        isStored: true,
        dataType: () => patchHistoryResultEnumDataType,
    })
    readonly result: Promise<PatchHistoryResult>;
}
