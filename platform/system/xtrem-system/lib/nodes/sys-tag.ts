import { Context, Node, UserAccess, decorators } from '@sage/xtrem-core';
import * as genericDataTypes from '../data-types/data-types';
import { sysTag } from '../service-options/sys-tag';

@decorators.node<SysTag>({
    isPublished: true,
    storage: 'sql',
    isCached: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    indexes: [{ orderBy: { name: 1 }, isUnique: true, isNaturalKey: true }],
    isSetupNode: true,
    serviceOptions: () => [sysTag],
    authorizedBy: (context, propertyOrOperation) => SysTag.getUserAccess(context, propertyOrOperation),
})
export class SysTag extends Node {
    @decorators.stringProperty<SysTag, 'name'>({
        isPublished: true,
        isNotEmpty: true,
        isStored: true,
        dataType: () => genericDataTypes.name,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<SysTag, 'description'>({
        isPublished: true,
        isStored: true,
        isOwnedByCustomer: true,
        dataType: () => genericDataTypes.localizedDescription,
    })
    readonly description: Promise<string>;

    // eslint-disable-next-line require-await
    static async getUserAccess(context: Context, propertyOrOperation: string): Promise<UserAccess> {
        if (['read', 'name', 'description'].includes(propertyOrOperation))
            return { sites: null, accessCodes: null, status: 'authorized' };

        return Context.accessRightsManager.getUserAccessFor(context, 'SysTag', propertyOrOperation);
    }
}

interface NodeExtension {
    readonly _tags: Promise<SysTag[]>;
}

declare module '@sage/xtrem-core/lib/ts-api/node' {
    interface Node extends NodeExtension {}
}
