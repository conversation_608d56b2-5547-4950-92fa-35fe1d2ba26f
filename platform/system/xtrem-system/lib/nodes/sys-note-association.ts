import { decorators, Node, Reference } from '@sage/xtrem-core';
import * as genericDataTypes from '../data-types/data-types';
import { SysNoteAssociationNode } from '../interfaces';
import { SysNote } from './sys-note';

@decorators.node<SysNoteAssociation>({
    isPublished: true,
    storage: 'sql',
    isCached: true,
    isPlatformNode: true,
    isPlatformNodeExportable: true,
    indexes: [{ orderBy: { sourceNodeName: +1, sourceNodeId: +1, note: +1 }, isUnique: true }],
    canExport: false,
    canRead: true,
})
export class SysNoteAssociation extends Node implements SysNoteAssociationNode {
    @decorators.stringProperty<SysNoteAssociation, 'sourceNodeName'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => genericDataTypes.name,
    })
    readonly sourceNodeName: Promise<string>;

    @decorators.integerProperty<SysNoteAssociation, 'sourceNodeId'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
    })
    readonly sourceNodeId: Promise<number>;

    @decorators.referenceProperty<SysNoteAssociation, 'note'>({
        isStored: true,
        isPublished: true,
        node: () => SysNote,
        isRequired: true,
        canLookup: false,
        lookupAccess: true,
    })
    readonly note: Reference<SysNote>;
}
