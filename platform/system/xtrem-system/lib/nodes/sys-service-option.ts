/** @ignore */ /** */
import { Collection, decorators, Node } from '@sage/xtrem-core';
import { ServiceOptionStatus } from '@sage/xtrem-shared';
import * as genericDataTypes from '../data-types/data-types';
import { ServiceOptionStatusDataType } from '../enums/service-option-status';
import * as xtremSystem from '../index';
import { loggers } from '../loggers';

const logger = loggers.application;

logger.logLevel = 'debug';

/** @internal */
@decorators.node<SysServiceOption>({
    isPublished: true,
    storage: 'sql',
    isCached: true,
    canCreate: false,
    canRead: true,
    canUpdate: true,
    canDeleteMany: false,
    isPlatformNode: true,
    isSharedByAllTenants: true,
    indexes: [
        {
            orderBy: { optionName: +1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
})
export class SysServiceOption extends Node {
    /**
     * the name of the package which defined the service option
     */
    @decorators.stringProperty<SysServiceOption, 'package'>({
        isPublished: true,
        isStored: true,
        dataType: () => genericDataTypes.name,
    })
    readonly package: Promise<string>;

    /**
     * the name of the option
     */
    @decorators.stringProperty<SysServiceOption, 'optionName'>({
        isPublished: true,
        isStored: true,
        dataType: () => genericDataTypes.name,
        lookupAccess: true,
    })
    readonly optionName: Promise<string>;

    /**
     * the description of the option
     */
    @decorators.stringProperty<SysServiceOption, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => genericDataTypes.name,
    })
    readonly description: Promise<string>;

    /**
     * Service Option's status
     */
    @decorators.enumProperty<SysServiceOption, 'status'>({
        isPublished: true,
        isStored: true,
        dataType: () => ServiceOptionStatusDataType,
    })
    readonly status: Promise<ServiceOptionStatus>;

    /**
     * is Low Level Service Option
     */
    @decorators.booleanProperty<SysServiceOption, 'isHidden'>({
        isPublished: true,
        isStored: true,
        defaultValue: true,
    })
    readonly isHidden: Promise<boolean>;

    @decorators.booleanProperty<SysServiceOption, 'isActiveByDefault'>({
        isPublished: true,
        isStored: true,
        defaultValue: false,
    })
    readonly isActiveByDefault: Promise<boolean>;

    @decorators.collectionProperty<SysServiceOption, 'childServiceOptions'>({
        isPublished: true,
        reverseReference: 'parentServiceOption',
        isVital: true,
        node: () => xtremSystem.nodes.SysServiceOptionToServiceOption,
    })
    readonly childServiceOptions: Collection<xtremSystem.nodes.SysServiceOptionToServiceOption>;

    @decorators.collectionProperty<SysServiceOption, 'parentServiceOptions'>({
        isPublished: true,
        reverseReference: 'childServiceOption',
        node: () => xtremSystem.nodes.SysServiceOptionToServiceOption,
    })
    readonly parentServiceOptions: Collection<xtremSystem.nodes.SysServiceOptionToServiceOption>;
}
