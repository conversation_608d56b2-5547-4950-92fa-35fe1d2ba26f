import { decorators, Node, Reference } from '@sage/xtrem-core';
import { User } from './user';

@decorators.node<UserNavigation>({
    storage: 'sql',
    isPublished: true,
    isVitalReferenceChild: true,
    canRead: true,
    isCached: true,
    indexes: [
        {
            orderBy: { user: +1 },
            isUnique: true,
        },
    ],
})
export class UserNavigation extends Node {
    @decorators.referenceProperty<UserNavigation, 'user'>({
        isPublished: true,
        isStored: true,
        isVitalParent: true,
        node: () => User,
    })
    readonly user: Reference<User>;

    @decorators.stringArrayProperty<UserNavigation, 'history'>({
        isStored: true,
        isPublished: false,
    })
    readonly history: Promise<string[]>;

    @decorators.stringArrayProperty<UserNavigation, 'bookmarks'>({
        isStored: true,
        isPublished: false,
    })
    readonly bookmarks: Promise<string[]>;
}
