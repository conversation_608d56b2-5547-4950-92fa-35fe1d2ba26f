/** @ignore */ /** */
import { decorators, Node } from '@sage/xtrem-core';
import * as dataTypes from '../data-types/data-types';

@decorators.node<SysVendor>({
    isPublished: true,
    storage: 'sql',
    isCached: true,
    canCreate: false,
    canRead: true,
    canUpdate: true,
    canDeleteMany: false,
    isPlatformNode: true,
    isSharedByAllTenants: true,
    isSetupNode: true,
    indexes: [
        {
            orderBy: { name: +1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
})
export class SysVendor extends Node {
    /**
     * the vendor's name
     */
    @decorators.stringProperty<SysVendor, 'name'>({
        isPublished: true,
        isStored: true,
        isFrozen: true,
        dataType: () => dataTypes.name,
    })
    readonly name: Promise<string>;

    /**
     * the vendor's description
     */
    @decorators.stringProperty<SysVendor, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => dataTypes.description,
    })
    readonly description: Promise<string>;
}
