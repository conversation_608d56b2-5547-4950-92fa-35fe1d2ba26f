import { Node, StringDataType, datetime, decorators } from '@sage/xtrem-core';
import * as xtremSystem from '../index';

const hashDataType = new StringDataType({ maxLength: 12 });
const messageDataType = new StringDataType({ maxLength: 512 });

@decorators.node<SysChangelog>({
    isPublished: true,
    storage: 'sql',
    isCached: true,
    canCreate: false,
    canRead: true,
    canUpdate: false,
    isSharedByAllTenants: true,
    serviceOptions: () => [xtremSystem.serviceOptions.Changelog],
    indexes: [{ orderBy: { hash: 1 }, isUnique: true, isNaturalKey: true }],
    isPlatformNode: true,
})
export class SysChangelog extends Node {
    @decorators.stringProperty<SysChangelog, 'hash'>({
        isPublished: true,
        isStored: true,
        isFrozen: true,
        dataType: () => hashDataType,
    })
    readonly hash: Promise<string>;

    @decorators.stringProperty<SysChangelog, 'message'>({
        isPublished: true,
        isStored: true,
        isFrozen: true,
        dataType: () => messageDataType,
    })
    readonly message: Promise<string>;

    @decorators.datetimeProperty<SysChangelog, 'changeDate'>({
        isPublished: true,
        isStored: true,
    })
    readonly changeDate: Promise<datetime>;
}
