/** @ignore */ /** */
import { decorators, Node } from '@sage/xtrem-core';
import { bundleIdDataType } from '../data-types/data-types';

@decorators.node<SysCustomRecord>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canDeleteMany: true,
    isPlatformNode: true,
    isPlatformNodeExportable: true,
})
export class SysCustomRecord extends Node {
    @decorators.stringProperty<SysCustomRecord, 'bundleId'>({
        isPublished: true,
        isStored: true,
        dataType: () => bundleIdDataType,
    })
    readonly bundleId: Promise<string>;

    @decorators.stringProperty<SysCustomRecord, 'factoryName'>({
        isPublished: true,
        isStored: true,
        dataType: () => bundleIdDataType,
    })
    readonly factoryName: Promise<string>;
}
