import { decorators, Node, TextStream } from '@sage/xtrem-core';
import * as genericDataTypes from '../data-types/data-types';
import { SysNoteNode } from '../interfaces';

@decorators.node<SysNote>({
    isPublished: true,
    storage: 'sql',
    isCached: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    hasTags: true,
    indexes: [{ orderBy: { _id: 1 }, isUnique: true, isNaturalKey: true }],
    isContentAddressable: true,
})
export class SysNote extends Node implements SysNoteNode {
    @decorators.stringProperty<SysNote, 'title'>({
        isPublished: true,
        isNotEmpty: true,
        isStored: true,
        dataType: () => genericDataTypes.title,
    })
    readonly title: Promise<string>;

    @decorators.textStreamProperty<SysNote, 'content'>({
        isPublished: true,
        isStored: true,
        dataType: () => genericDataTypes.noteContentDataType,
    })
    readonly content: Promise<TextStream>;
}
