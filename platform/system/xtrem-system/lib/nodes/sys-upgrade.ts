/** @ignore */ /** */
import { decorators, Node } from '@sage/xtrem-core';
import { bundleIdDataType } from '../data-types/data-types';
import { SysUpgradeManagedItems } from '../services/upgrade/sys-upgrade-managed-items';

/**
 * Provides some informations on installed upgrade bundles
 */
@decorators.node<SysUpgrade>({
    isPublished: true,
    storage: 'sql',
    canCreate: false,
    canRead: true,
    canUpdate: true,
    canDeleteMany: true,
    isPlatformNode: true,
    isSharedByAllTenants: true,
    indexes: [
        {
            orderBy: { bundleId: +1 },
            isUnique: true,
        },
    ],
})
export class SysUpgrade extends Node {
    /**
     * The id of the bundle
     */
    @decorators.stringProperty<SysUpgrade, 'bundleId'>({
        isPublished: true,
        isStored: true,
        dataType: () => bundleIdDataType,
    })
    readonly bundleId: Promise<string>;

    /**
     * information on items managed by the bundle (nodes, properties)
     */
    @decorators.jsonProperty<SysUpgrade, 'managedItems'>({
        isPublished: true,
        isStored: true,
    })
    readonly managedItems: Promise<SysUpgradeManagedItems>;
}
