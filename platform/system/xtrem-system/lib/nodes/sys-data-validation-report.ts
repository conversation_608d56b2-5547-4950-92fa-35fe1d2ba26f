/** @ignore */ /** */
import { Collection, Node, decorators } from '@sage/xtrem-core';
import { SysDataValidationReportLine } from './sys-data-validation-report-line';

@decorators.node<SysDataValidationReport>({
    storage: 'sql',
    canCreate: false,
    canRead: true,
    canUpdate: false,
    canDeleteMany: false,
    indexes: [
        {
            orderBy: { _createStamp: 1 },
        },
    ],
})
export class SysDataValidationReport extends Node {
    @decorators.collectionProperty<SysDataValidationReport, 'lines'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'report',
        node: () => SysDataValidationReportLine,
    })
    readonly lines: Collection<SysDataValidationReportLine>;
}
