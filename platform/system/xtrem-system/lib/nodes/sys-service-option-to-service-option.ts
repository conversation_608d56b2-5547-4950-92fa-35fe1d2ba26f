import { Reference, decorators, Node } from '@sage/xtrem-core';
import * as xtremSystem from '../index';

@decorators.node<SysServiceOptionToServiceOption>({
    package: 'xtrem-system',
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    isVitalCollectionChild: true,
    isSharedByAllTenants: true,
    isPlatformNode: true,
    indexes: [
        {
            orderBy: { childServiceOption: +1, parentServiceOption: +1 },
            isUnique: true,
        },
    ],
})
export class SysServiceOptionToServiceOption extends Node {
    @decorators.referenceProperty<SysServiceOptionToServiceOption, 'childServiceOption'>({
        isPublished: true,
        isStored: true,
        node: () => xtremSystem.nodes.SysServiceOption,
    })
    readonly childServiceOption: Reference<xtremSystem.nodes.SysServiceOption>;

    @decorators.referenceProperty<SysServiceOptionToServiceOption, 'parentServiceOption'>({
        isPublished: true,
        node: () => xtremSystem.nodes.SysServiceOption,
        isVitalParent: true,
        isStored: true,
    })
    readonly parentServiceOption: Reference<xtremSystem.nodes.SysServiceOption>;
}
