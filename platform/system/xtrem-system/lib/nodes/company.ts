import { Collection, decorators, Node } from '@sage/xtrem-core';
import * as dataTypes from '../data-types/data-types';
import * as xtremSystem from '../index';

@decorators.node<Company>({
    package: 'xtrem-system',
    storage: 'sql',
    isCached: true,
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    isCustomizable: true,
    indexes: [
        {
            orderBy: { id: 1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    notifies: ['created', 'updated', 'deleted'],
    defaultsToSingleMatch: true,
})
export class Company extends Node {
    @decorators.stringProperty<Company, 'id'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        dataSensitivityLevel: 'personal',
        anonymizeMethod: 'hashLimit', // Part of a unique index, hash to avoid collisions
        anonymizeValue: 16,
        dataType: () => dataTypes.id,
        lookupAccess: true,
    })
    readonly id: Promise<string>;

    @decorators.booleanProperty<Company, 'isActive'>({
        isStored: true,
        isPublished: true,
        defaultValue: true,
        lookupAccess: true,
    })
    readonly isActive: Promise<boolean>;

    /**
     * Associated sites of the Company
     */
    @decorators.collectionProperty<Company, 'sites'>({
        isPublished: true,
        reverseReference: 'legalCompany',
        node: () => xtremSystem.nodes.Site,
        lookupAccess: true,
    })
    readonly sites: Collection<xtremSystem.nodes.Site>;
}
