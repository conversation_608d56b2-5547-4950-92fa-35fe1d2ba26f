/** @ignore */ /** */
import { decorators, nanoIdDataType, Node } from '@sage/xtrem-core';
import * as dataTypes from '../data-types/data-types';

@decorators.node<SysCustomer>({
    isPublished: true,
    storage: 'sql',
    canCreate: false,
    canRead: true,
    canUpdate: true,
    canDeleteMany: false,
    isPlatformNode: true,
    isSharedByAllTenants: true,
    indexes: [
        {
            orderBy: { customerId: +1 },
            isUnique: true,
        },
    ],
})
export class SysCustomer extends Node {
    /**
     * the customer's id
     */
    @decorators.stringProperty<SysCustomer, 'customerId'>({
        isPublished: true,
        isStored: true,
        dataType: () => nanoIdDataType,
    })
    readonly customerId: Promise<string>;

    /**
     * the customer's name
     */
    @decorators.stringProperty<SysCustomer, 'name'>({
        isPublished: true,
        isStored: true,
        dataType: () => dataTypes.name,
    })
    readonly name: Promise<string>;
}
