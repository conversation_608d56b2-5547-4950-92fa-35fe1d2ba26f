import {
    AccessRights,
    BinaryStream,
    Collection,
    Context,
    CoreHooks,
    Node,
    NodeStatus,
    Reference,
    StringDataType,
    UserAccess,
    UserInfo,
    ValidationSeverity,
    adminDemoPersona,
    asyncArray,
    decorators,
    unsafeRandomizeCharacters,
    useDefaultValue,
} from '@sage/xtrem-core';
import { noop } from 'lodash';
import * as xtremSystem from '../../index';
import * as dataTypes from '../data-types/data-types';
import { UserType, UserTypeDataType } from '../enums/user-type';
import { isApiEmail, isOperatorEmail } from '../functions/user';
import * as serviceOptions from '../service-options/index';
import { UserEventsManager } from '../services/user-events/user-events-manager';
import * as sharedFunctions from '../shared-functions';
import { UserNavigation } from './user-navigation';
import { UserPreferences } from './user-preferences';

const firstNameDataType = new StringDataType({ maxLength: 30 });
const lastNameDataType = new StringDataType({ maxLength: 30 });

interface UpdateClientSettings {
    _id: number;
    screenId: string;
    elementId: string;
    title: string;
    description: string;
    content: string;
    _action?: 'update' | 'create' | 'delete';
}

@decorators.node<User>({
    package: 'xtrem-system',
    storage: 'sql',
    isCached: true,
    canRead: true,
    canCreate: true,
    canUpdate: true,
    canDelete: false,
    isPublished: true,
    canDuplicate: true,
    isSetupNode: true,
    hasVendorProperty: false,
    hasLayerProperty: false,
    denyReadOnLookupOnlyAccess: true,
    // TODO: enable this for workflow
    // notifies: ['created', 'updated', 'deleted'],
    indexes: [{ orderBy: { email: +1 }, isUnique: true, isNaturalKey: true }],
    async controlBegin(cx): Promise<void> {
        // System users cannot be updated once created
        if ((await this.userType) === 'system') {
            cx.addDiagnose(
                ValidationSeverity.error,
                this.$.context.localize(
                    '@sage/xtrem-system/system-user-forbidden',
                    'Cannot create/update a system user',
                ),
            );
        }
        if ((await this.isFirstAdminUser) && this.$.status !== NodeStatus.modified) {
            const users = this.$.context.query(User, {
                filter: {
                    isFirstAdminUser: true,
                },
            });
            await cx.error
                .withMessage(
                    '@sage/xtrem-system/duplicate_first_admin',
                    'A first admin user is already defined. You cannot create another one.',
                )
                .if(await users.length)
                .is.not.equal.to(0);
        }
        const isAdministrator = await this.isAdministrator;
        const isDemoPersona = await this.isDemoPersona;
        const email = await this.email;
        const isOperatorUser = await this.isOperatorUser;
        if (await this.isApiUser) {
            await cx.error
                .withMessage('@sage/xtrem-system/invalid-api-user-is-admin', 'An api user cannot be an admin')
                .if(isAdministrator)
                .is.true();
            await cx.error
                .withMessage(
                    '@sage/xtrem-system/invalid-api-user-is-demo-persona',
                    'An api user cannot be a demo persona',
                )
                .if(isDemoPersona)
                .is.true();
        }
        if (isDemoPersona) {
            await cx.error
                .withMessage(
                    '@sage/xtrem-system/invalid-admin-demo-persona',
                    'Cannot create an admin demo persona other than the default.',
                )
                .if(email !== adminDemoPersona.email && isAdministrator)
                .is.true();
            await cx.error
                .withMessage(
                    '@sage/xtrem-system/cannot-deactivate-admin-demo-persona',
                    'Cannot deactivate the Admin demo persona.',
                )
                .if(email === adminDemoPersona.email && !(await this.isActive))
                .is.true();
        }
        if (isOperatorUser) {
            await cx.error
                .withMessage('@sage/xtrem-system/invalid-operator-user-is-admin', 'An operator user cannot be an admin')
                .if(isAdministrator)
                .is.true();
            await cx.error
                .withMessage(
                    '@sage/xtrem-system/invalid-operator-user-is-demo-persona',
                    'An operator user cannot be a demo persona',
                )
                .if(isDemoPersona)
                .is.true();
        }

        if (
            !this.$.context.withoutTransactionUser &&
            (([NodeStatus.added].includes(this.$.status) && (await this.isAdministrator)) ||
                ([NodeStatus.modified].includes(this.$.status) &&
                    (await this.isAdministrator) !== (await (await this.$.old).isAdministrator)))
        ) {
            const user = await this.$.context.user;
            const isAdminUser = !!user?.isAdministrator;
            await cx.error
                .withMessage(
                    '@sage/xtrem-system/nodes__user__only_admin_can_add_admin',
                    "Only an administrator user can add or modify another user's administrator rights.",
                )
                .if(isAdminUser)
                .is.false();
            await cx.error
                .withMessage(
                    '@sage/xtrem-system/nodes__user__cannot_modify_own_rights',
                    'You are not allowed to modify your own administrator rights.',
                )
                .if(this._id === user?._id && isAdminUser)
                .is.true();
        }
    },
    async deleteEnd(): Promise<void> {
        if (!(await this.isFirstAdminUser)) {
            // Notify infra of the deletion
            await UserEventsManager.userDeleted(this.$.context, {
                userId: this._id,
                status: this.$.status,
                email: await this.email,
            });
        }
    },
    async saveBegin() {
        if (await this.isFirstAdminUser) {
            await this.$.set({ isAdministrator: true });
        }
    },
    async saveEnd(): Promise<void> {
        // No notification here, since it is done from the User extension in xtrem-authorization
        await Context.accessRightsManager.invalidateAuthorizationCache(this.$.context);
    },
    authorizedBy: (context, propertyOrOperation) => User.getUserAccess(context, propertyOrOperation),
})
export class User extends Node /* implements UserInfo */ {
    @decorators.stringProperty<User, 'email'>({
        isStored: true,
        isFrozen: true,
        isPublished: true,
        isNotEmpty: true,
        duplicateRequiresPrompt: true,
        duplicatedValue: '',
        lookupAccess: true,
        dataSensitivityLevel: 'personal',
        anonymizeMethod: 'custom',
        anonymizeValue(value) {
            return value.endsWith('@localhost.domain') ? value : unsafeRandomizeCharacters(value);
        },
        dataType: () => dataTypes.email,
        adaptValue: (val: string): string => val.toLowerCase(),
        async control(cx, value) {
            if (!sharedFunctions.validEmail(value)) {
                cx.error.add(
                    cx.localize('@sage/xtrem-system/invalid-email', 'Invalid email address ({{value}})', {
                        value,
                    }),
                );
            }
            const isApiUserEmail = isApiEmail(value);
            if ((await this.isApiUser) && !isApiUserEmail) {
                cx.error.add(
                    cx.localize(
                        '@sage/xtrem-system/invalid-api-user-email',
                        'Invalid api user email address. Expected: api-<appid>@localhost.domain',
                    ),
                );
            }
            if (!(await this.isApiUser) && isApiUserEmail) {
                cx.error.add(
                    cx.localize(
                        '@sage/xtrem-system/reserved-api-user-email',
                        'API Email address format is reserved for api users.',
                        {
                            value,
                        },
                    ),
                );
            }

            const isOperatorUserEmail = isOperatorEmail(value);
            if ((await this.isOperatorUser) && !isOperatorUserEmail) {
                cx.error.add(
                    cx.localize(
                        '@sage/xtrem-system/invalid-operator-user-email',
                        'Invalid operator user email address. Expected: operator-<name>@localhost.domain',
                    ),
                );
            }
            if (!(await this.isOperatorUser) && isOperatorUserEmail) {
                cx.error.add(
                    cx.localize(
                        '@sage/xtrem-system/reserved-operator-user-email',
                        'Operator Email address format is reserved for operator users.',
                        {
                            value,
                        },
                    ),
                );
            }
        },
    })
    readonly email: Promise<string>;

    @decorators.booleanProperty<User, 'isFirstAdminUser'>({ isStored: true })
    readonly isFirstAdminUser: Promise<boolean>;

    @decorators.stringProperty<User, 'firstName'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        duplicateRequiresPrompt: true,
        duplicatedValue: '',
        dataSensitivityLevel: 'personal',
        anonymizeMethod: 'perCharRandom',
        dataType: () => firstNameDataType,
        lookupAccess: true,
    })
    readonly firstName: Promise<string>;

    @decorators.stringProperty<User, 'lastName'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        duplicateRequiresPrompt: true,
        duplicatedValue: '',
        dataSensitivityLevel: 'personal',
        anonymizeMethod: 'perCharRandom',
        dataType: () => lastNameDataType,
        lookupAccess: true,
    })
    readonly lastName: Promise<string>;

    @decorators.booleanProperty<User, 'isActive'>({
        isStored: true,
        isPublished: true,
        defaultValue: true,
        provides: ['isActive'],
        duplicateRequiresPrompt: true,
    })
    readonly isActive: Promise<boolean>;

    @decorators.binaryStreamProperty<User, 'photo'>({
        isStored: true,
        isNullable: true,
        isPublished: true,
        lookupAccess: true,
        duplicatedValue: useDefaultValue,
        dataSensitivityLevel: 'personal',
        anonymizeMethod: 'binary',
        anonymizeValue: 'image',
    })
    readonly photo: Promise<BinaryStream | null>;

    @decorators.stringProperty<User, 'displayName'>({
        isPublished: true,
        lookupAccess: true,
        async getValue() {
            return `${await this.lastName}, ${await this.firstName}`;
        },
    })
    readonly displayName: Promise<string>;

    @decorators.enumProperty<User, 'userType'>({
        isPublished: true,
        isStored: true,
        dataType: () => UserTypeDataType,
        defaultValue: 'application',
    })
    readonly userType: Promise<UserType>;

    @decorators.booleanProperty<User, 'isAdministrator'>({
        isStored: true,
        isPublished: true,
        defaultValue: false,
    })
    readonly isAdministrator: Promise<boolean>;

    @decorators.booleanProperty<User, 'isDemoPersona'>({
        isStored: true,
        isPublished: true,
        defaultValue: false,
        serviceOptions: () => [serviceOptions.isDemoTenant],
    })
    readonly isDemoPersona: Promise<boolean>;

    @decorators.booleanProperty<User, 'isApiUser'>({
        isStored: true,
        isPublished: true,
        defaultValue: false,
        duplicateRequiresPrompt: true,
    })
    readonly isApiUser: Promise<boolean>;

    @decorators.booleanProperty<User, 'isOperatorUser'>({
        isPublished: true,
        dependsOn: ['operatorId'],
        duplicateRequiresPrompt: true,
        defaultValue: false,
        duplicatedValue: useDefaultValue,
        setValue(value) {
            noop(value);
        },
        async getValue() {
            return (await this.operatorId) !== '';
        },
        serviceOptions: () => [xtremSystem.serviceOptions.sysDeviceToken],
    })
    readonly isOperatorUser: Promise<boolean>;

    /**
     * The operatorId is a hash of tenantId, app and operatorCode
     */
    @decorators.stringProperty<User, 'operatorId'>({
        isStored: true,
        dependsOn: ['operatorCode'],
        dataType: () => xtremSystem.dataTypes.bigId,
        async defaultValue() {
            // If we have an operatorCode, we need to hash it and return the result as the operatorId
            if (await this.operatorCode) {
                return xtremSystem.functions.operatorIdHash(this.$.context, await this.operatorCode);
            }

            // If operatorCode is empty that means that the user has already
            // been created and we already have the operatorId so we return it directly
            const operatorId = await this.operatorId;
            if (operatorId) {
                return operatorId;
            }

            return '';
        },
        // Update the value with the defaultValue rule
        updatedValue: useDefaultValue,
        serviceOptions: () => [xtremSystem.serviceOptions.sysDeviceToken],
    })
    readonly operatorId: Promise<string>;

    @decorators.stringProperty<User, 'operatorCode'>({
        isPublished: true,
        isTransientInput: true,
        duplicateRequiresPrompt: true,
        duplicatedValue: '',
        dataType: () => xtremSystem.dataTypes.bigId,
        serviceOptions: () => [xtremSystem.serviceOptions.sysDeviceToken],
        async control(cx, value) {
            if (await this.isOperatorUser) {
                // operatorCode can be empty since it's transient and only gets populated from the UI
                if ((await this.operatorId) && !value) {
                    return;
                }

                if (value?.length < 4) {
                    cx.error.add(
                        cx.localize(
                            '@sage/xtrem-system/invalid-operator-pin-code',
                            'An operator PIN code needs to have a minimum of 4 characters.',
                        ),
                    );
                }

                const isOperatorCodeUnique = await xtremSystem.functions.isOperatorCodeUnique(this.$.context, value);

                if (!isOperatorCodeUnique) {
                    cx.error.add(
                        cx.localize(
                            '@sage/xtrem-system/invalid-operator-pincode-not-unique',
                            'An operator PIN code needs to be unique.',
                        ),
                    );
                }
            }
        },
    })
    readonly operatorCode: Promise<string>;

    // should be in User after moving UserPreferences as well
    @decorators.referenceProperty<User, 'preferences'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'user',
        node: () => UserPreferences,
        isNullable: true,
    })
    readonly preferences: Reference<UserPreferences | null>;

    // should be in User after moving UserNavigation as well
    @decorators.referenceProperty<User, 'navigation'>({
        isVital: true,
        reverseReference: 'user',
        node: () => UserNavigation,
        isNullable: true,
    })
    readonly navigation: Reference<UserNavigation | null>;

    @decorators.collectionProperty<User, 'clientSettings'>({
        isPublished: true,
        node: () => xtremSystem.nodes.SysClientUserSettings,
        isVital: true,
        reverseReference: 'user',
    })
    readonly clientSettings: Collection<xtremSystem.nodes.SysClientUserSettings>;

    // eslint-disable-next-line require-await
    static async getUserAccess(context: Context, propertyOrOperation: string): Promise<UserAccess> {
        if (['read', 'updateClientSettings', 'clientSettings'].includes(propertyOrOperation))
            return { sites: null, accessCodes: null, status: 'authorized' };

        return Context.accessRightsManager.getUserAccessFor(context, 'User', propertyOrOperation);
    }

    static async authorizePersonaOperation(context: Context, operationName: string): Promise<UserAccess> {
        if (await context.supportsPersona()) {
            return { sites: null, accessCodes: null, status: 'authorized' };
        }
        // Because we are in the authorizedBy check, bypass the authorizedBy check to avoid infinite loop
        return AccessRights.getUserAccessOnOperations(context, [
            { nodeName: 'User', operationName, bypassAuthorizedBy: true },
        ]);
    }

    static authorizeDemoPersonas(
        _options: { action: string; trackingId: string },
        context: Context,
    ): Promise<UserAccess> {
        return this.authorizePersonaOperation(context, 'demoPersonas');
    }

    @decorators.query<typeof User, 'demoPersonas'>({
        isPublished: true,
        parameters: [],
        serviceOptions: () => [serviceOptions.isDemoTenant],
        authorizedBy: 'authorizeDemoPersonas',
        return: {
            type: 'array',
            item: {
                type: 'object',
                properties: {
                    _id: 'string',
                    displayName: 'string',
                    email: 'string',
                    createdBy: 'string',
                    createStamp: 'string',
                    updatedBy: 'string',
                    updateStamp: 'string',
                },
            },
            isMandatory: true,
        },
    })
    static async demoPersonas(context: Context): Promise<User[]> {
        const loginUser = await context.loginUser;
        if (!loginUser) return [];

        return asyncArray(await Context.accessRightsManager.getDemoPersonas(context))
            .map(userInfo => User.fromUserInfo(context, userInfo))
            .toArray();
    }

    static authorizeSetDemoPersona(
        _options: { action: string; trackingId: string },
        context: Context,
    ): Promise<UserAccess> {
        return this.authorizePersonaOperation(context, 'setDemoPersona');
    }

    @decorators.mutation<typeof User, 'setDemoPersona'>({
        isPublished: true,
        parameters: [{ name: 'email', type: 'string', isMandatory: true }],
        serviceOptions: () => [serviceOptions.isDemoTenant],
        authorizedBy: 'authorizeSetDemoPersona',
        return: {
            type: 'boolean',
            isMandatory: true,
        },
    })
    static setDemoPersona(context: Context, email: string): Promise<boolean> {
        return context.setDemoPersona(email);
    }

    @decorators.mutation<typeof User, 'logPageVisit'>({
        isPublished: true,
        parameters: [{ name: 'path', type: 'string', isMandatory: true }],
        return: { type: 'array', item: 'string' },
    })
    static async logPageVisit(context: Context, path: string): Promise<string[]> {
        const userId = (await context.user)?._id;
        let userNavigation = await context.tryRead(UserNavigation, { user: userId }, { forUpdate: true });

        if (userNavigation) {
            // Object has to be cloned otherwise the runtime won't detect the change.
            const currentHistory = [...((await userNavigation?.history) || [])];
            const pathIndex = currentHistory.indexOf(path);

            // If the item is already in the history, we remove it first before re-adding it to the end.
            if (pathIndex !== -1) {
                currentHistory.splice(pathIndex, 1);
            }
            currentHistory.push(path);

            // The length of the history is capped at 50 items
            if (currentHistory.length > 50) {
                currentHistory.shift();
            }
            await userNavigation.$.set({ history: currentHistory });
        } else {
            userNavigation = await context.create(UserNavigation, { user: userId, history: [path] });
        }

        await userNavigation.$.save();
        return userNavigation.history;
    }

    @decorators.mutation<typeof User, 'updateClientSettings'>({
        isPublished: true,
        parameters: [
            {
                name: 'clientSettings',
                type: 'array',
                item: {
                    type: 'object',
                    properties: {
                        _id: 'integer',
                        screenId: 'string',
                        elementId: 'string',
                        title: 'string',
                        description: 'string',
                        content: 'string',
                        _action: 'string',
                    },
                },
                isMandatory: true,
            },
        ],
        return: { type: 'instance', node: () => User },
    })
    static async updateClientSettings(context: Context, clientSettings: UpdateClientSettings[]): Promise<User> {
        const userId = (await context.user)?._id;
        const user = await context.read(User, { _id: userId }, { forUpdate: true });
        await user.$.set({ clientSettings });
        await user.$.save();
        return user;
    }

    async toUserInfo(): Promise<UserInfo> {
        return {
            _id: this._id,
            email: await this.email,
            isActive: await this.isActive,
            userType: await this.userType,
            firstName: await this.firstName,
            lastName: await this.lastName,
            isDemoPersona: await this.isDemoPersona,
            isApiUser: await this.isApiUser,
            isFirstAdminUser: await this.isFirstAdminUser,
            isAdministrator: await this.isAdministrator,
        };
    }

    static fromUserInfo(context: Context, userInfo: UserInfo): Promise<User> {
        const userKey = { _id: userInfo._id };
        return context.read(User, userKey);
    }

    static async fromContext(context: Context): Promise<User> {
        const userInfo = await context.user;
        if (!userInfo) throw new Error('no user in context');
        return this.fromUserInfo(context, userInfo);
    }

    // /**
    //  * Sends users welcome email
    //  * @param context Context
    //  * @param array of users to send welcome email
    //  * @param indicator if it is a new user or new admin on tenant
    //  * @return If was successful
    //  */
    @decorators.mutation<typeof User, 'sendWelcomeMail'>({
        isPublished: true,
        parameters: [
            { name: 'users', type: 'array', isMandatory: true, item: { type: 'reference', node: () => User } },
            { name: 'isAdmin', type: 'boolean', isMandatory: true },
        ],
        return: { type: 'boolean' },
    })
    static async sendWelcomeMail(context: Context, users: User[], isAdmin: boolean): Promise<boolean> {
        await CoreHooks.communicationManager.notify(context, 'onboardingMail', { users, isAdmin });

        return true;
    }
}
