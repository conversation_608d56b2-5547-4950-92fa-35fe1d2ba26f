import { Context, Node, NodeStatus, Reference, decorators } from '@sage/xtrem-core';
import { singleCharacter } from '../data-types';
import {
    UserImportExportDateFormat,
    userImportExportDateFormatDataType,
} from '../enums/user-import-export-date-format';
import { User } from './user';

@decorators.node<UserPreferences>({
    package: 'xtrem-authorization',
    storage: 'sql',
    isPublished: true,
    isVitalReferenceChild: true,
    canRead: true,
    isCached: true,
    indexes: [
        {
            orderBy: { user: +1 },
            isUnique: true,
        },
    ],

    async saveEnd(): Promise<void> {
        // welcome email is sent only once for the first admin user during the init tenant process
        if (
            this.$.status === NodeStatus.added &&
            (await this.isWelcomeMailSent) &&
            !(await (
                await this.user
            ).isFirstAdminUser)
        ) {
            await User.sendWelcomeMail(this.$.context, [await this.user], false);
        }
    },
})
export class UserPreferences extends Node {
    @decorators.referenceProperty<UserPreferences, 'user'>({
        isPublished: true,
        isStored: true,
        isVitalParent: true,
        node: () => User,
    })
    readonly user: Reference<User>;

    @decorators.booleanProperty<UserPreferences, 'isWelcomeMailSent'>({
        isStored: true,
        isPublished: true,
        defaultValue: false,
    })
    readonly isWelcomeMailSent: Promise<boolean>;

    @decorators.booleanProperty<UserPreferences, 'isExternal'>({
        isStored: true,
        isPublished: true,
        defaultValue: false,
    })
    readonly isExternal: Promise<boolean>;

    @decorators.stringProperty<UserPreferences, 'importExportDelimiter'>({
        isStored: true,
        isPublished: true,
        dataType: () => singleCharacter,
        defaultValue: ';',
        isNotEmpty: true,
    })
    readonly importExportDelimiter: Promise<string>;

    @decorators.enumProperty<UserPreferences, 'importExportDateFormat'>({
        dataType: () => userImportExportDateFormatDataType,
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        defaultValue: 'isoDash',
    })
    readonly importExportDateFormat: Promise<UserImportExportDateFormat>;

    @decorators.query<typeof UserPreferences, 'activeServiceOptions'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'array',
            item: {
                type: 'object',
                properties: {
                    optionName: 'string',
                },
            },
        },
    })
    static async activeServiceOptions(context: Context): Promise<{ optionName: string }[] | []> {
        return (await context.activeServiceOptions).map(activeServiceOption => {
            return { optionName: activeServiceOption.name };
        });
    }
}
