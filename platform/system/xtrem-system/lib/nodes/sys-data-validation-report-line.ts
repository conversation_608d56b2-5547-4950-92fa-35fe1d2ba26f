/** @ignore */ /** */
import { Node, Reference, enums as coreEnums, decorators, integer } from '@sage/xtrem-core';
import { dataTypes } from '..';
import { SysDataValidationReport } from './sys-data-validation-report';

@decorators.node<SysDataValidationReportLine>({
    storage: 'sql',
    canCreate: false,
    canRead: true,
    canUpdate: false,
    canDeleteMany: false,
    isVitalCollectionChild: true,
    isFrozen: true,
    indexes: [
        {
            orderBy: { report: 1, nodeName: +1, nodeId: +1 },
            isUnique: false,
        },
    ],
})
export class SysDataValidationReportLine extends Node {
    @decorators.referenceProperty<SysDataValidationReportLine, 'report'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => SysDataValidationReport,
    })
    readonly report: Reference<SysDataValidationReport>;

    @decorators.stringProperty<SysDataValidationReportLine, 'nodeName'>({
        isPublished: true,
        isStored: true,
        dataType: () => dataTypes.name,
    })
    readonly nodeName: Promise<string>;

    @decorators.integerProperty<SysDataValidationReportLine, 'nodeId'>({
        isPublished: true,
        isStored: true,
    })
    readonly nodeId: Promise<integer>;

    @decorators.stringProperty<SysDataValidationReportLine, 'message'>({
        isPublished: true,
        isStored: true,
        dataType: () => dataTypes.description,
    })
    readonly message: Promise<string>;

    @decorators.stringProperty<SysDataValidationReportLine, 'path'>({
        isPublished: true,
        isStored: true,
        dataType: () => dataTypes.description,
    })
    readonly path: Promise<string>;

    @decorators.enumProperty<SysDataValidationReportLine, 'severity'>({
        isPublished: true,
        isStored: true,
        dataType: () => coreEnums.messageSeverityDataType,
    })
    readonly severity: Promise<coreEnums.MessageSeverity>;

    @decorators.jsonProperty<SysDataValidationReportLine, 'extraInfo'>({
        isPublished: true,
        isStored: true,
    })
    readonly extraInfo: Promise<any>;
}
