/** @ignore */ /** */

import { Context, decorators, EnumDataType, Node, Reference } from '@sage/xtrem-core';
import { loggers } from '../loggers';
import { SysPackageManager } from '../services/sys-package-manager';
import { SysPackVersion } from './sys-pack-version';

const logger = loggers.packageManager;

export enum PackAllocationStatusEnum {
    off = 1,
    preparing,
    on,
}

export type PackAllocationStatus = keyof typeof PackAllocationStatusEnum;

export const packAllocationStatusEnumDataType = new EnumDataType<PackAllocationStatus>({
    enum: PackAllocationStatusEnum,
    filename: __filename,
});

@decorators.node<SysPackAllocation>({
    isPublished: true,
    storage: 'sql',
    isCached: true,
    canCreate: true,
    canDeleteMany: true,
    isPlatformNode: true,
    isPlatformNodeExportable: true,
    indexes: [
        {
            orderBy: { package: +1 },
            isUnique: true,
        },
    ],
})
export class SysPackAllocation extends Node {
    @decorators.referenceProperty<SysPackAllocation, 'package'>({
        isPublished: true,
        isStored: true,
        node: () => SysPackVersion,
    })
    readonly package: Reference<SysPackVersion>;

    @decorators.booleanProperty<SysPackAllocation, 'isActivable'>({
        isPublished: true,
        isStored: true,
    })
    readonly isActivable: Promise<boolean>;

    @decorators.enumProperty<SysPackAllocation, 'status'>({
        isPublished: true,
        isStored: true,
        dataType: () => packAllocationStatusEnumDataType,
    })
    readonly status: Promise<PackAllocationStatus>;

    @decorators.booleanProperty<SysPackAllocation, 'isActive'>({
        isPublished: true,
        isStored: true,
    })
    readonly isActive: Promise<boolean>;

    /**
     * Activate a package for Xtrem services
     * @param context
     * @param packageId Name of the package
     * @returns Active state of the package
     */
    @decorators.mutation<typeof SysPackAllocation, 'activate'>({
        isPublished: true,
        parameters: [{ name: 'packageId', type: 'string' }],
        return: 'boolean',
    })
    static async activate(context: Context, packageId: string): Promise<boolean> {
        const isPackageEnabled = await context.isPackageEnabled(packageId);

        if (isPackageEnabled) {
            logger.info(`${packageId} package for tenantId ${context.tenantId} is already active`);
        }

        logger.info(`Activating package ${packageId} for tenantId ${context.tenantId}`);
        const packageManager = SysPackageManager.fromContext(context);
        await packageManager.activatePackages(context, [packageId]);

        return packageManager.isPackageEnabled(context, packageId);
    }

    /**
     * Deactivate a package for Xtrem services
     * @param context
     * @param packageId Name of the package
     * @returns Active state of the package
     */
    @decorators.mutation<typeof SysPackAllocation, 'deactivate'>({
        isPublished: true,
        parameters: [{ name: 'packageId', type: 'string' }],
        return: 'boolean',
    })
    static async deactivate(context: Context, packageId: string): Promise<boolean> {
        const isPackageEnabled = await context.isPackageEnabled(packageId);

        if (!isPackageEnabled) {
            logger.info(`${packageId} package for tenantId ${context.tenantId} is already deactivated`);
        }

        logger.info(`Deactivating package ${packageId} for tenantId ${context.tenantId}`);
        const packageManager = SysPackageManager.fromContext(context);
        await packageManager.deactivatePackages(context, [packageId]);

        return packageManager.isPackageEnabled(context, packageId);
    }
}
