import { asyncArray, decorators, getLanguageFromLocale, Node, StringDataType } from '@sage/xtrem-core';

const idDataType = new StringDataType({ maxLength: 5 });
const languageDataType = new StringDataType({ maxLength: 2 });

@decorators.node<Locale>({
    package: 'xtrem-system',
    storage: 'sql',
    isCached: true,
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    isSetupNode: true,
    indexes: [
        {
            orderBy: { id: 1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    async createEnd() {
        // Check that the locale is passed in format 'xx-XX'
        getLanguageFromLocale(await this.id);
        // The default locale should always be the master locale for that language
        if (await this.isDefaultLocale) await this.$.set({ isLanguageMasterLocale: true });

        // This will only be set the first time a locale is added for a tenant
        if (!(await this.isDefaultLocale)) {
            const checkDefault = await this.$.context
                .query(Locale, {
                    filter: { isDefaultLocale: true },
                })
                .toArray();
            if (!checkDefault.length) {
                await this.$.set({ isDefaultLocale: true });
                await this.$.set({ isLanguageMasterLocale: true });
            }
        }
        // This will be set every time a locale for a new language is added for a tenant
        if (!(await this.isLanguageMasterLocale)) {
            const checkMasterLocale = await this.$.context
                .query(Locale, {
                    filter: { isLanguageMasterLocale: true },
                })
                .toArray();
            const currentMaster = await asyncArray(checkMasterLocale)
                .filter(async locale => (await locale.language) === (await this.language))
                .toArray();
            if (!currentMaster.length) {
                await this.$.set({ isLanguageMasterLocale: true });
            }
        }

        if (
            !(await this.isDefaultLocale) &&
            (await this.isLanguageMasterLocale) &&
            this.$.context.defaultLanguage === (await this.language)
        ) {
            throw this.$.context.businessRuleError({
                key: '@sage/xtrem-system/language-master-locale-different-from-default',
                message: 'The language master locale must match the default locale for the same language.',
            });
        }
    },
    async saveEnd() {
        // Only do one save on old default record if both default and master are set
        if ((await this.isDefaultLocale) && (await this.isLanguageMasterLocale)) {
            const checkDefault = await this.$.context
                .query(Locale, {
                    filter: {
                        _and: [
                            { _or: [{ isDefaultLocale: true }, { isLanguageMasterLocale: true }] },
                            { _not: { id: await this.id } },
                        ],
                    },
                    forUpdate: true,
                })
                .toArray();
            await asyncArray(checkDefault).forEach(async oldDefault => {
                await oldDefault.$.set({ isDefaultLocale: false });
                if ((await oldDefault.language) === (await this.language))
                    await oldDefault.$.set({ isLanguageMasterLocale: false });
                await oldDefault.$.save();
            });
        } else {
            // Where not both flags are true, check for either default or master
            if (await this.isDefaultLocale) {
                const checkDefault = await this.$.context
                    .query(Locale, {
                        filter: { _and: [{ isDefaultLocale: true }, { _not: { id: await this.id } }] },
                        forUpdate: true,
                    })
                    .toArray();
                await asyncArray(checkDefault).forEach(async oldDefault => {
                    await oldDefault.$.set({ isDefaultLocale: false });
                    await oldDefault.$.save();
                });
            }
            if (await this.isLanguageMasterLocale) {
                const checkMasterLocale = await this.$.context
                    .query(Locale, {
                        filter: { _and: [{ isLanguageMasterLocale: true }, { _not: { id: await this.id } }] },
                        forUpdate: true,
                    })
                    .toArray();
                await asyncArray(checkMasterLocale)
                    .filter(async locale => (await locale.language) === (await this.language))
                    .forEach(async oldMaster => {
                        await oldMaster.$.set({ isLanguageMasterLocale: false });
                        await oldMaster.$.save();
                    });
            }
        }
    },
})
export class Locale extends Node {
    @decorators.stringProperty<Locale, 'id'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        dataType: () => idDataType,
    })
    readonly id: Promise<string>;

    @decorators.stringProperty<Locale, 'language'>({
        isPublished: true,
        dataType: () => languageDataType,
        async computeValue() {
            return getLanguageFromLocale(await this.id);
        },
    })
    readonly language: Promise<string>;

    @decorators.booleanProperty<Locale, 'isDefaultLocale'>({
        isStored: true,
        isPublished: true,
    })
    readonly isDefaultLocale: Promise<boolean>;

    @decorators.booleanProperty<Locale, 'isLanguageMasterLocale'>({
        isStored: true,
        isPublished: true,
    })
    readonly isLanguageMasterLocale: Promise<boolean>;
}
