import { Collection, decorators, Node, NodeStatus, Reference } from '@sage/xtrem-core';
import * as dataTypes from '../data-types/data-types';
import * as xtremSystem from '../index';

@decorators.node<Site>({
    package: 'xtrem-system',
    storage: 'sql',
    isCached: true,
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    isCustomizable: true,
    indexes: [{ orderBy: { id: +1 }, isUnique: true, isNaturalKey: true }],
    notifies: ['created', 'updated', 'deleted'],
    provides: ['site'],
    defaultsToSingleMatch: true,
})
export class Site extends Node {
    @decorators.stringProperty<Site, 'id'>({
        isStored: true,
        isPublished: true,
        dataSensitivityLevel: 'personal',
        anonymizeMethod: 'hashLimit', // Part of a unique index, hash to avoid collisions
        anonymizeValue: 16,
        dataType: () => dataTypes.id,
        lookupAccess: true,
    })
    readonly id: Promise<string>;

    @decorators.stringProperty<Site, 'name'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        dataSensitivityLevel: 'personal',
        anonymizeMethod: 'perCharRandom',
        dataType: () => xtremSystem.dataTypes.name,
        lookupAccess: true,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<Site, 'description'>({
        isStored: true,
        isPublished: true,
        dataSensitivityLevel: 'personal',
        anonymizeMethod: 'perCharRandom',
        dataType: () => xtremSystem.dataTypes.description,
        lookupAccess: true,
    })
    readonly description: Promise<string>;

    @decorators.booleanProperty<Site, 'isActive'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        defaultValue() {
            return true;
        },
        provides: ['isActive'],
    })
    readonly isActive: Promise<boolean>;

    @decorators.referenceProperty<Site, 'legalCompany'>({
        isStored: true,
        isPublished: true,
        node: () => xtremSystem.nodes.Company,
        lookupAccess: true,
        isFrozen: true,
        async control(cx, val) {
            await xtremSystem.events.control.isActive(
                this.$.status,
                cx,
                this.$.status === NodeStatus.modified ? (await this.$.old)._id : null,
                val,
            );
        },
    })
    readonly legalCompany: Reference<xtremSystem.nodes.Company>;

    @decorators.collectionProperty<Site, 'linkedSites'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremSystem.nodes.Site,
        async getFilter() {
            return {
                legalCompany: await this.legalCompany,
            };
        },
    })
    readonly linkedSites: Collection<Site>;
}
