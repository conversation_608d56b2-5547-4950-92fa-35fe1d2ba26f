import { Collection, decorators, Node, Reference } from '@sage/xtrem-core';
import type { IconType } from 'carbon-react/esm/components/icon/icon-type';
import * as dataTypes from '../data-types/data-types';
import * as xtremSystem from '../index';

@decorators.node<SysClientNotification>({
    storage: 'sql',
    isCached: true,
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    isPlatformNode: true,
})
export class SysClientNotification extends Node {
    @decorators.enumProperty<SysClientNotification, 'level'>({
        isPublished: true,
        isStored: true,
        defaultValue: 'info',
        dataType: () => xtremSystem.enums.sysClientNotificationLevelDataType,
    })
    readonly level: Promise<xtremSystem.enums.SysClientNotificationLevel>;

    @decorators.stringProperty<SysClientNotification, 'description'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        dataType: () => dataTypes.localizedDescription,
    })
    readonly description: Promise<string>;

    @decorators.stringProperty<SysClientNotification, 'title'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        dataType: () => dataTypes.localizedName,
    })
    readonly title: Promise<string>;

    @decorators.stringProperty<SysClientNotification, 'icon'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.icon,
    })
    readonly icon: Promise<IconType>;

    @decorators.booleanProperty<SysClientNotification, 'isRead'>({
        isStored: true,
        isPublished: true,
        defaultValue: false,
    })
    readonly isRead: Promise<boolean>;

    @decorators.booleanProperty<SysClientNotification, 'shouldDisplayToast'>({
        isStored: true,
        isPublished: true,
        defaultValue: false,
    })
    readonly shouldDisplayToast: Promise<boolean>;

    @decorators.collectionProperty<SysClientNotification, 'actions'>({
        node: () => xtremSystem.nodes.SysClientNotificationAction,
        isVital: true,
        reverseReference: 'clientNotifications',
    })
    readonly actions: Collection<xtremSystem.nodes.SysClientNotificationAction>;

    @decorators.referenceProperty<SysClientNotification, 'recipient'>({
        isStored: true,
        isPublished: true,
        node: () => xtremSystem.nodes.User,
    })
    readonly recipient: Reference<xtremSystem.nodes.User>;
}
