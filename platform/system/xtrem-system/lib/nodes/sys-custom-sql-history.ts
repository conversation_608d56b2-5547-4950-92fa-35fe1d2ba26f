import { JsonDataType, Node, StringDataType, datetime, decorators } from '@sage/xtrem-core';

export type SysCustomSqlHistoryResult = {
    error?: string;
    output?: any;
};

const scriptPathDataType = new StringDataType({ maxLength: 300 });

const scriptContentDataType = new StringDataType({ maxLength: 40000 });

const resultDataType = new JsonDataType<unknown, SysCustomSqlHistoryResult>();

/**
 * This node keeps track of the custom SQL scripts that were executed on a specific tenant
 */

@decorators.node<SysCustomSqlHistory>({
    storage: 'sql',
    canRead: true,
    isPlatformNode: true,
    indexes: [
        {
            orderBy: { scriptPath: +1, startDateTime: +1 },
            isUnique: true,
        },
    ],
})
export class SysCustomSqlHistory extends Node {
    /**
     * The path of the script that was applied
     */
    @decorators.stringProperty<SysCustomSqlHistory, 'scriptPath'>({
        isPublished: true,
        isStored: true,
        isFrozen: true,
        dataType: () => scriptPathDataType,
    })
    readonly scriptPath: Promise<string>;

    /**
     * The content of the script that was applied
     */
    @decorators.stringProperty<SysCustomSqlHistory, 'scriptContent'>({
        isPublished: true,
        isStored: true,
        isFrozen: true,
        dataType: () => scriptContentDataType,
    })
    readonly scriptContent: Promise<string>;

    /**
     * The start time of the script execution
     */
    @decorators.datetimeProperty<SysCustomSqlHistory, 'startDateTime'>({
        isPublished: true,
        isStored: true,
        isFrozen: true,
        isNullable: false,
    })
    readonly startDateTime: Promise<datetime | null>;

    /**
     * The end time of the script execution
     */
    @decorators.datetimeProperty<SysCustomSqlHistory, 'endDateTime'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly endDateTime: Promise<datetime | null>;

    @decorators.booleanProperty<SysCustomSqlHistory, 'dryRun'>({
        isStored: true,
        isPublished: true,
    })
    readonly dryRun: Promise<boolean>;

    /**
     * The result of the script
     */
    @decorators.jsonProperty<SysCustomSqlHistory, 'result'>({
        isPublished: true,
        isStored: true,
        dataType: () => resultDataType,
    })
    readonly result: Promise<SysCustomSqlHistoryResult>;
}
