import { ClientUserSettings, decorators, Node, NodeStatus, Reference, ValidationContext } from '@sage/xtrem-core';
import * as dataTypes from '../data-types/data-types';
import { User } from './user';

@decorators.node<SysClientUserSettings>({
    storage: 'sql',
    isPublished: true,
    isCached: true,
    canRead: true,
    canDelete: true,
    canDeleteMany: true,
    indexes: [
        {
            orderBy: {
                user: 1,
                screenId: 1,
                elementId: 1,
                title: 1,
            },
            isNaturalKey: true,
            isUnique: true,
        },
    ],
    isVitalCollectionChild: true,
})
export class SysClientUserSettings extends Node implements ClientUserSettings {
    @decorators.referenceProperty<SysClientUserSettings, 'user'>({
        isPublished: true,
        isStored: true,
        isVitalParent: true,
        node: () => User,
        async defaultValue() {
            return this.$.context.read(User, { email: (await this.$.context.user)?.email });
        },
    })
    readonly user: Reference<User>;

    @decorators.stringProperty<SysClientUserSettings, 'title'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.title,
    })
    readonly title: Promise<string>;

    @decorators.stringProperty<SysClientUserSettings, 'description'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.description,
    })
    readonly description: Promise<string>;

    @decorators.stringProperty<SysClientUserSettings, 'screenId'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        dataType: () => dataTypes.name,
    })
    readonly screenId: Promise<string>;

    @decorators.stringProperty<SysClientUserSettings, 'elementId'>({
        isPublished: true,
        isStored: true,
        isNotEmpty: true,
        dataType: () => dataTypes.name,
    })
    readonly elementId: Promise<string>;

    @decorators.booleanProperty<SysClientUserSettings, 'isSelected'>({
        isPublished: true,
        isStored: true,
        dependsOn: ['user', 'elementId', 'screenId'],
        defaultValue: false,
        async control(cx: ValidationContext) {
            if (
                (this.$.status === NodeStatus.added && (await this.isSelected)) ||
                (this.$.status === NodeStatus.modified &&
                    !(await (
                        await this.$.old
                    ).isSelected) &&
                    (await this.isSelected))
            ) {
                const settingSelect = await this.$.context.select(SysClientUserSettings, true, {
                    filter: {
                        user: await this.user,
                        screenId: await this.screenId,
                        elementId: await this.elementId,
                        isSelected: true,
                        _id: { _ne: this._id },
                    },
                });

                if (settingSelect.length) {
                    cx.error.addLocalized(
                        '@sage/xtrem-system/nodes__sys-client-user-settings',
                        'You can only select one.',
                    );
                }
            }
        },
    })
    readonly isSelected: Promise<boolean>;

    @decorators.jsonProperty<SysClientUserSettings, 'content'>({
        isStored: true,
        isPublished: true,
        isNullable: false,
    })
    readonly content: Promise<string>;
}
