import { decorators, Node, StringDataType } from '@sage/xtrem-core';
import * as xtremSystem from '../index';

const nameDataType = new StringDataType({ maxLength: 100 });

@decorators.node<SysPackVersion>({
    isPublished: true,
    storage: 'sql',
    isCached: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    isSharedByAllTenants: true,
    indexes: [{ orderBy: { name: 1 }, isUnique: true, isNaturalKey: true }],
    isPlatformNode: true,
})
export class SysPackVersion extends Node {
    @decorators.stringProperty<SysPackVersion, 'name'>({
        isPublished: true,
        isStored: true,
        isFrozen: true,
        dataType: () => nameDataType,
        lookupAccess: true,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<SysPackVersion, 'version'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremSystem.dataTypes.version,
        lookupAccess: true,
    })
    readonly version: Promise<string>;

    @decorators.booleanProperty<SysPackVersion, 'isBundle'>({
        isPublished: true,
        isStored: true,
    })
    readonly isBundle: Promise<boolean>;

    @decorators.booleanProperty<SysPackVersion, 'isHidden'>({
        isPublished: true,
        isStored: true,
    })
    readonly isHidden: Promise<boolean>;

    @decorators.booleanProperty<SysPackVersion, 'isReleased'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly isReleased: Promise<boolean>;

    @decorators.stringProperty<SysPackVersion, 'sqlSchemaVersion'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremSystem.dataTypes.version,
        defaultValue() {
            return this.version;
        },
        dependsOn: ['version'],
    })
    readonly sqlSchemaVersion: Promise<string>;

    @decorators.booleanProperty<SysPackVersion, 'isUpgradeBundle'>({
        isPublished: true,
        isStored: true,
    })
    readonly isUpgradeBundle: Promise<boolean>;
}
