import { decorators, Node, Reference } from '@sage/xtrem-core';
import * as dataTypes from '../data-types/data-types';
import * as xtremSystem from '../index';

@decorators.node<SysClientNotificationAction>({
    storage: 'sql',
    isCached: true,
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    canDeleteMany: true,
    isVitalCollectionChild: true,
    isPlatformNode: true,
})
export class SysClientNotificationAction extends Node {
    @decorators.stringProperty<SysClientNotificationAction, 'title'>({
        isStored: true,
        isPublished: true,
        isNotEmpty: true,
        dataType: () => dataTypes.name,
    })
    readonly title: Promise<string>;

    @decorators.enumProperty<SysClientNotificationAction, 'style'>({
        isPublished: true,
        isStored: true,
        defaultValue: 'primary',
        dataType: () => xtremSystem.enums.sysClientNotificationActionStyleDataType,
    })
    readonly style: Promise<xtremSystem.enums.SysClientNotificationActionStyle>;

    @decorators.stringProperty<SysClientNotificationAction, 'link'>({
        isStored: true,
        isPublished: true,
        dataType: () => dataTypes.bigUrl,
    })
    readonly link: Promise<string>;

    @decorators.referenceProperty<SysClientNotificationAction, 'clientNotifications'>({
        node: () => xtremSystem.nodes.SysClientNotification,
        isVitalParent: true,
        isStored: true,
    })
    readonly clientNotifications: Reference<xtremSystem.nodes.SysClientNotification>;
}
