/** @ignore */ /** */
import {
    ConfigManager,
    Context,
    Diagnose,
    Node,
    Reference,
    SystemError,
    ValidationSeverity,
    decorators,
    nanoIdDataType,
} from '@sage/xtrem-core';
import { TenantService } from '@sage/xtrem-data-management';
import {
    XtremSumologicFilterOptions,
    XtremSumologicHelper,
    XtremSumologicKind,
    XtremSumologicRelativeTimeBox,
} from '@sage/xtrem-sumologic-helper';
import * as dataTypes from '../data-types/data-types';
import * as xtremSystem from '../index';
import { XtremSystemConfig } from '../interfaces/system-config';
import { loggers } from '../loggers';
import _ = require('lodash');

const logger = loggers.application;

@decorators.node<SysTenant>({
    isPublished: true,
    storage: 'sql',
    canCreate: false,
    canRead: false,
    canUpdate: false,
    canDeleteMany: false,
    isPlatformNode: true,
    isSharedByAllTenants: true,
    isCached: true,
    indexes: [
        {
            orderBy: { tenantId: +1 },
            isUnique: true,
        },
        {
            orderBy: { directoryName: +1 },
            isUnique: true,
        },
    ],
})
export class SysTenant extends Node {
    /**
     * the tenant's id
     */
    @decorators.stringProperty<SysTenant, 'tenantId'>({
        isPublished: true,
        isStored: true,
        dataType: () => nanoIdDataType,
    })
    readonly tenantId: Promise<string>;

    /**
     * the tenant's regular name
     */
    @decorators.stringProperty<SysTenant, 'name'>({
        isPublished: true,
        isStored: true,
        dataType: () => dataTypes.name,
        control(cx, val) {
            if (!/[\u0020-\u017f]/.test(val)) {
                cx.error.addLocalized(
                    '@sage/xtrem-system/nodes__sys_tenant__tenant_name_characters_not_authorized',
                    "tenant's name characters not authorized",
                );
            }
        },
    })
    readonly name: Promise<string>;

    /**
     * the unique tenant's kebab name
     */
    @decorators.stringProperty<SysTenant, 'directoryName'>({
        isPublished: true,
        isStored: true,
        dataType: () => dataTypes.name,
        dependsOn: ['name'],
        control(cx, val) {
            if (!/^([a-z][a-z0-9]*)(-[a-z0-9]+)*$/.test(val)) {
                cx.error.addLocalized(
                    '@sage/xtrem-system/nodes__sys_tenant__tenant_directory_name_should_be_in_kebab_case',
                    "tenant's directory name should be in kabab-case format",
                );
            }
        },
        async defaultValue() {
            return _.kebabCase(await this.name);
        },
    })
    readonly directoryName: Promise<string>;

    /**
     * the customers that owns the tenant
     */
    @decorators.referenceProperty<SysTenant, 'customer'>({
        isPublished: true,
        isStored: true,
        node: () => xtremSystem.nodes.SysCustomer,
    })
    readonly customer: Reference<xtremSystem.nodes.SysCustomer>;

    /**
     * Reset the documents bound to a tenant (salesOrders, purchaseOrders, ...).
     * This will not reset the whole tenant (for instance, the items will be kept).
     * Only the factories flagged as 'isClearedByReset' will be reset.
     */
    @decorators.mutation<typeof SysTenant, 'resetTenantDocuments'>({
        isPublished: true,
        parameters: [{ name: 'tenantId', type: 'string', isMandatory: true }],
        return: 'boolean',
    })
    static async resetTenantDocuments(context: Context, tenantId = ''): Promise<boolean> {
        const user = await context.user;
        if (user == null) throw new SystemError('User not found');

        if (!(await Context.accessRightsManager.isAuthorizationServiceOptionEnabled(context))) {
            // Safeguard: when the authorization service option is enabled, only allow this
            // dangerous function for admins
            if (!user.isAdministrator) {
                // Add a log to track who tried to reset tenant data
                logger.error(
                    context.localize(
                        '@sage/xtrem-system/reset-tenant-data-not-admin-with-id',
                        'Reset tenant was attempted by user {{userId}}',
                        { userId: user._id },
                    ),
                );
                context.diagnoses.push(
                    new Diagnose(
                        ValidationSeverity.error,
                        [],
                        context.localize(
                            '@sage/xtrem-system/reset-tenant-data-not-admin',
                            'Only administrators can reset tenant data',
                        ),
                    ),
                );
                return false;
            }
        }

        if (tenantId !== context.tenantId) {
            context.diagnoses.push(
                new Diagnose(
                    ValidationSeverity.error,
                    [],
                    context.localize(
                        '@sage/xtrem-system/reset-tenant-data-tenant-id-mismatched',
                        "The tenant ID '{{tenantId}}'' does not match with the current tenant '{{currentId}}'",
                        { tenantId, currentId: context.tenantId },
                    ),
                ),
            );
            return false;
        }

        logger.warn(`CAUTION: resetTenantDocuments for tenant ${tenantId} was initiated by user ${user._id}`);
        await TenantService.resetTenantDocuments(context.application, {
            tenant: { id: context.tenantId as string },
        });
        return true;
    }

    @decorators.query<typeof SysTenant, 'getTenantInformation'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'object',
            properties: {
                version: 'string',
                tenantId: 'string',
                sumologicLink: 'string',
            },
        },
    })
    static getTenantInformation(
        context: Context,
    ): Promise<{ version: string; tenantId: string; sumologicLink: string }> {
        const config = context.configuration.getPackageConfig<XtremSystemConfig>('@sage/xtrem-system');
        let sumologicLink = '';
        if (config?.unsafeDevTroubleshooting) {
            const envName = process.env.XTREM_ENV;
            const cluster = ConfigManager.current.clusterId;
            if (envName && cluster && context.tenantId) {
                const sumologicHelper = XtremSumologicHelper.getInstance({
                    app: ConfigManager.current.app || '',
                    envName,
                    cluster,
                    infrequent: true,
                });

                const LAST_15_MINUTES_LOGS: XtremSumologicFilterOptions = {
                    timeBox: {
                        relativeTimeBox: XtremSumologicRelativeTimeBox.LAST_15_MINUTES,
                        kind: XtremSumologicKind.RELATIVE,
                    },
                };

                // get log for tenant on the last 15 minutes
                sumologicLink = sumologicHelper.getTenantLogsLink(context.tenantId, LAST_15_MINUTES_LOGS);
            }
        }
        return Promise.resolve({
            version: context.application.rootAbout.version,
            tenantId: context.tenantId || '',
            sumologicLink,
        });
    }
}
