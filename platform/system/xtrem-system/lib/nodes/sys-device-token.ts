import { BusinessRuleError, ConfigManager, Context, Node, date, decorators, nanoIdDataType } from '@sage/xtrem-core';
import * as xtremSystem from '..';

@decorators.node<SysDeviceToken>({
    isPublished: true,
    storage: 'sql',
    isCached: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    indexes: [
        {
            orderBy: { tokenId: +1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
    isSetupNode: true,
    serviceOptions: () => [xtremSystem.serviceOptions.sysDeviceToken],
})
export class SysDeviceToken extends Node {
    @decorators.stringProperty<SysDeviceToken, 'tokenId'>({
        isPublished: true,
        isNotEmpty: true,
        isStored: true,
        dataType: () => nanoIdDataType,
    })
    readonly tokenId: Promise<string>;

    @decorators.dateProperty<SysDeviceToken, 'expiration'>({
        isPublished: true,
        isStored: true,
    })
    readonly expiration: Promise<date>;

    @decorators.stringProperty<SysDeviceToken, 'name'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<SysDeviceToken, 'loginUrl'>({
        isPublished: true,
        isStored: false,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.url,
        computeValue() {
            return `${ConfigManager.current.security?.loginUrl}/device/login?tenantId=${this.$.context.tenantId}&app=${this.$.context.app}`;
        },
    })
    readonly loginUrl: Promise<string>;

    @decorators.stringProperty<SysDeviceToken, 'loginTestUrl'>({
        isPublished: true,
        isStored: false,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.url,
        computeValue() {
            return `${ConfigManager.current.security?.loginUrl}/device/login?tenantId=${this.$.context.tenantId}&app=${this.$.context.app}&test=true`;
        },
    })
    readonly loginTestUrl: Promise<string>;

    @decorators.mutation<typeof SysDeviceToken, 'createDeviceToken'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'object',
            properties: {
                tokenId: 'string',
                expires: 'date',
            },
        },
    })
    static createDeviceToken(context: Context): Promise<xtremSystem.interfaces.XtremDeviceToken | null> {
        if (context.hasDeviceToken) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-system/sys_device_token_device_token_already_created',
                    'Current device already has a token created.',
                ),
            );
        }

        return xtremSystem.services.createDeviceToken(context);
    }

    @decorators.mutation<typeof SysDeviceToken, 'deleteDeviceToken'>({
        isPublished: true,
        parameters: [{ name: 'tokenId', type: 'string' }],
        return: {
            type: 'boolean',
        },
    })
    static async deleteDeviceToken(context: Context, tokenId: string): Promise<boolean> {
        await xtremSystem.services.deleteDeviceToken(context, tokenId);

        return true;
    }
}
