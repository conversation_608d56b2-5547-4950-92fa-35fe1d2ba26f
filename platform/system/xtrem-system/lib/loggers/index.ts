import { Logger } from '@sage/xtrem-core';

export const loggers = {
    application: Logger.getLogger(__filename, 'application'),
    artifact: Logger.getLogger(__filename, 'artifact'),
    dataValidation: Logger.getLogger(__filename, 'dataValidation'),
    packageManager: Logger.getLogger(__filename, 'packageManager'),
    serviceOption: Logger.getLogger(__filename, 'serviceOption'),
    tenant: Logger.getLogger(__filename, 'tenant'),
    upgrade: Logger.getLogger(__filename, 'upgrade'),
    git: Logger.getLogger(__filename, 'git'),
    events: Logger.getLogger(__filename, 'events'),
    userEvents: Logger.getLogger(__filename, 'userEvents'),
    functions: Logger.getLogger(__filename, 'functions'),
};
