import { <PERSON><PERSON>Hooks } from '@sage/xtrem-cli';
import { Context, CoreHooks } from '@sage/xtrem-core';
import * as xtremSystem from './lib';
import { SysCsvChecksumManager } from './lib/services/csv-checksum-manager';
import { DataPatchEngine } from './lib/services/data-patch/data-patch-engine';
import { SysHotUpgradeManager } from './lib/services/hot-upgrade-manager';
import { SysNotificationManager } from './lib/services/notification/notification-manager';
import { SysServiceOptionManager } from './lib/services/service-option-manager';
import { SysDataValidationManager } from './lib/services/sys-data-validation-manager';
import { SysNoteManager } from './lib/services/sys-note-manager';
import { SysPackageManager } from './lib/services/sys-package-manager';
import { SysTagManager } from './lib/services/sys-tag-manager';
import { TestManager } from './lib/services/test-manager';
import { fixColumnOrder } from './lib/services/upgrade/fix-schema';
import { UpgradeExecuteCustomSqlScript } from './lib/services/upgrade/upgrade-execute-custom-sql-script';
import { UpgradeSqlSchema, upgradeSqlSchemaFromCli } from './lib/services/upgrade/upgrade-sql-schema';

export * from './lib/index';
export * as TestHelpers from './test/helpers';

export function updateContext() {
    Context.tenantManager = xtremSystem.services.sysTenantManager;
    CoreHooks.createCsvChecksumManager = () => new SysCsvChecksumManager();
    CoreHooks.getDataValidationManager = () => SysDataValidationManager.instance;
    CoreHooks.createPackageManager = application => new SysPackageManager(application);
    CoreHooks.createServiceOptionManager = application => new SysServiceOptionManager(application);
    CoreHooks.createHotUpgradeManager = () => new SysHotUpgradeManager();
    CoreHooks.sysManager = {
        getUserNode: () => xtremSystem.nodes.User,
    };
    CoreHooks.createNotificationManager = () => new SysNotificationManager();

    CoreHooks.getTagManager = () => new SysTagManager();
    CoreHooks.getNoteManager = () => SysNoteManager.instance;

    Context.localizationManager = xtremSystem.services.sysLocalizationManager;
    CliHooks.upgradeManager = {
        upgradeSqlSchemaFromCli,
        renameLatestFolders: UpgradeSqlSchema.renameLatestFolders.bind(UpgradeSqlSchema),
        executeCustomSqlScript: UpgradeExecuteCustomSqlScript.executeCustomSqlScript.bind(UpgradeSqlSchema),
        fixColumnOrder,
    };

    CliHooks.dataPatchManager = {
        execute: application => {
            DataPatchEngine.applyDataPatches(application);
        },
        postProcess: DataPatchEngine.postProcess.bind(DataPatchEngine),
    };
    CliHooks.testManager = {
        prepareLoadTestDatabase: TestManager.prepareLoadTestDatabase.bind(TestManager),
    };
    Context.dataSettingsManager = xtremSystem.services.sysDataSettingsManager;
}

updateContext();
