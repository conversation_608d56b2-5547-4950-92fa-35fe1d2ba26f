{"@sage/xtrem-system": [{"topic": "Company/asyncExport/start", "queue": "import-export", "sourceFileName": "company.ts"}, {"topic": "Locale/asyncExport/start", "queue": "import-export", "sourceFileName": "locale.ts"}, {"topic": "Site/asyncExport/start", "queue": "import-export", "sourceFileName": "site.ts"}, {"topic": "SysChangelog/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-changelog.ts"}, {"topic": "SysClientNotification/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-client-notification.ts"}, {"topic": "SysClientNotificationAction/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-client-notification-action.ts"}, {"topic": "SysClientUserSettings/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-client-user-settings.ts"}, {"topic": "SysCsvChecksum/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-csv-checksum.ts"}, {"topic": "SysCustomer/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-customer.ts"}, {"topic": "SysCustomRecord/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-custom-record.ts"}, {"topic": "SysCustomSqlHistory/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-custom-sql-history.ts"}, {"topic": "SysDataValidationReport/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-data-validation-report.ts"}, {"topic": "SysDataValidationReportLine/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-data-validation-report-line.ts"}, {"topic": "SysDeviceToken/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-device-token.ts"}, {"topic": "SysNote/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-note.ts"}, {"topic": "SysPackAllocation/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-pack-allocation.ts"}, {"topic": "SysPackVersion/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-pack-version.ts"}, {"topic": "SysPatchHistory/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-patch-history.ts"}, {"topic": "SysServiceOption/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-service-option.ts"}, {"topic": "SysServiceOptionState/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-service-option-state.ts"}, {"topic": "SysServiceOptionToServiceOption/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-service-option-to-service-option.ts"}, {"topic": "SysTag/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-tag.ts"}, {"topic": "SysTenant/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-tenant.ts"}, {"topic": "SysUpgrade/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-upgrade.ts"}, {"topic": "SysVendor/asyncExport/start", "queue": "import-export", "sourceFileName": "sys-vendor.ts"}, {"topic": "User/asyncExport/start", "queue": "import-export", "sourceFileName": "user.ts"}, {"topic": "UserNavigation/asyncExport/start", "queue": "import-export", "sourceFileName": "user-navigation.ts"}, {"topic": "UserPreferences/asyncExport/start", "queue": "import-export", "sourceFileName": "user-preferences.ts"}]}