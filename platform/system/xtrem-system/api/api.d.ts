declare module '@sage/xtrem-system-api-partial' {
    import type {
        AggregateQueryOperation,
        AggregateReadOperation,
        BinaryStream,
        ClientCollection,
        ClientNode,
        ClientNodeInput,
        CreateOperation,
        DeleteOperation,
        DuplicateOperation,
        GetDefaultsOperation,
        GetDuplicateOperation,
        Operation as Node$Operation,
        QueryOperation,
        ReadOperation,
        TextStream,
        UpdateByIdOperation,
        UpdateOperation,
        VitalClientNode,
        VitalClientNodeInput,
        integer,
    } from '@sage/xtrem-client';
    export interface ArtifactDataType$Enum {
        json: 1;
        js: 2;
        csv: 3;
        ts: 4;
        meta: 5;
    }
    export type ArtifactDataType = keyof ArtifactDataType$Enum;
    export interface ColoredElement$Enum {
        backgroundColor: 0;
        borderColor: 1;
        textColor: 2;
    }
    export type ColoredElement = keyof ColoredElement$Enum;
    export interface ServiceOptionStatus$Enum {
        workInProgress: 1;
        experimental: 2;
        released: 3;
    }
    export type ServiceOptionStatus = keyof ServiceOptionStatus$Enum;
    export interface SysClientNotificationActionStyle$Enum {
        primary: 0;
        secondary: 1;
        tertiary: 2;
        link: 3;
    }
    export type SysClientNotificationActionStyle = keyof SysClientNotificationActionStyle$Enum;
    export interface SysClientNotificationLevel$Enum {
        error: 0;
        warning: 1;
        info: 2;
        success: 3;
        approval: 4;
    }
    export type SysClientNotificationLevel = keyof SysClientNotificationLevel$Enum;
    export interface UserImportExportDateFormat$Enum {
        isoDash: 0;
        isoSlash: 1;
        europeanDash: 2;
        europeanSlash: 3;
        usDash: 4;
        usSlash: 5;
    }
    export type UserImportExportDateFormat = keyof UserImportExportDateFormat$Enum;
    export interface Company extends ClientNode {
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        sites: ClientCollection<Site>;
    }
    export interface CompanyInput extends ClientNodeInput {
        id?: string;
        isActive?: boolean | string;
    }
    export interface CompanyBinding extends ClientNode {
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        sites: ClientCollection<Site>;
    }
    export interface Company$Operations {
        query: QueryOperation<Company>;
        read: ReadOperation<Company>;
        aggregate: {
            read: AggregateReadOperation<Company>;
            query: AggregateQueryOperation<Company>;
        };
        create: CreateOperation<CompanyInput, Company>;
        getDuplicate: GetDuplicateOperation<Company>;
        update: UpdateOperation<CompanyInput, Company>;
        updateById: UpdateByIdOperation<CompanyInput, Company>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        getDefaults: GetDefaultsOperation<Company>;
    }
    export interface Locale extends ClientNode {
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        language: string;
        isDefaultLocale: boolean;
        isLanguageMasterLocale: boolean;
    }
    export interface LocaleInput extends ClientNodeInput {
        _vendor?: integer | string;
        id?: string;
        isDefaultLocale?: boolean | string;
        isLanguageMasterLocale?: boolean | string;
    }
    export interface LocaleBinding extends ClientNode {
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        language: string;
        isDefaultLocale: boolean;
        isLanguageMasterLocale: boolean;
    }
    export interface Locale$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface Locale$Operations {
        query: QueryOperation<Locale>;
        read: ReadOperation<Locale>;
        aggregate: {
            read: AggregateReadOperation<Locale>;
            query: AggregateQueryOperation<Locale>;
        };
        create: CreateOperation<LocaleInput, Locale>;
        getDuplicate: GetDuplicateOperation<Locale>;
        update: UpdateOperation<LocaleInput, Locale>;
        updateById: UpdateByIdOperation<LocaleInput, Locale>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        lookups(dataOrId: string | { data: LocaleInput }): Locale$Lookups;
        getDefaults: GetDefaultsOperation<Locale>;
    }
    export interface Site extends ClientNode {
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        description: string;
        isActive: boolean;
        legalCompany: Company;
        linkedSites: ClientCollection<Site>;
    }
    export interface SiteInput extends ClientNodeInput {
        id?: string;
        name?: string;
        description?: string;
        isActive?: boolean | string;
        legalCompany?: integer | string;
    }
    export interface SiteBinding extends ClientNode {
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        description: string;
        isActive: boolean;
        legalCompany: Company;
        linkedSites: ClientCollection<Site>;
    }
    export interface Site$Lookups {
        legalCompany: QueryOperation<Company>;
    }
    export interface Site$Operations {
        query: QueryOperation<Site>;
        read: ReadOperation<Site>;
        aggregate: {
            read: AggregateReadOperation<Site>;
            query: AggregateQueryOperation<Site>;
        };
        create: CreateOperation<SiteInput, Site>;
        getDuplicate: GetDuplicateOperation<Site>;
        update: UpdateOperation<SiteInput, Site>;
        updateById: UpdateByIdOperation<SiteInput, Site>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        lookups(dataOrId: string | { data: SiteInput }): Site$Lookups;
        getDefaults: GetDefaultsOperation<Site>;
    }
    export interface SysChangelog extends ClientNode {
        hash: string;
        message: string;
        changeDate: string;
    }
    export interface SysChangelogInput extends ClientNodeInput {
        hash?: string;
        message?: string;
        changeDate?: string;
    }
    export interface SysChangelogBinding extends ClientNode {
        hash: string;
        message: string;
        changeDate: string;
    }
    export interface SysChangelog$Operations {
        query: QueryOperation<SysChangelog>;
        read: ReadOperation<SysChangelog>;
        aggregate: {
            read: AggregateReadOperation<SysChangelog>;
            query: AggregateQueryOperation<SysChangelog>;
        };
        getDefaults: GetDefaultsOperation<SysChangelog>;
    }
    export interface SysClientNotification extends ClientNode {
        _updateUser: User;
        _createUser: User;
        level: SysClientNotificationLevel;
        description: string;
        title: string;
        icon: string;
        isRead: boolean;
        shouldDisplayToast: boolean;
        recipient: User;
    }
    export interface SysClientNotificationInput extends ClientNodeInput {
        level?: SysClientNotificationLevel;
        description?: string;
        title?: string;
        icon?: string;
        isRead?: boolean | string;
        shouldDisplayToast?: boolean | string;
        recipient?: integer | string;
    }
    export interface SysClientNotificationBinding extends ClientNode {
        _updateUser: User;
        _createUser: User;
        level: SysClientNotificationLevel;
        description: string;
        title: string;
        icon: string;
        isRead: boolean;
        shouldDisplayToast: boolean;
        recipient: User;
    }
    export interface SysClientNotification$Lookups {
        recipient: QueryOperation<User>;
    }
    export interface SysClientNotification$Operations {
        query: QueryOperation<SysClientNotification>;
        read: ReadOperation<SysClientNotification>;
        aggregate: {
            read: AggregateReadOperation<SysClientNotification>;
            query: AggregateQueryOperation<SysClientNotification>;
        };
        create: CreateOperation<SysClientNotificationInput, SysClientNotification>;
        getDuplicate: GetDuplicateOperation<SysClientNotification>;
        update: UpdateOperation<SysClientNotificationInput, SysClientNotification>;
        updateById: UpdateByIdOperation<SysClientNotificationInput, SysClientNotification>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        lookups(dataOrId: string | { data: SysClientNotificationInput }): SysClientNotification$Lookups;
        getDefaults: GetDefaultsOperation<SysClientNotification>;
    }
    export interface SysClientNotificationAction extends VitalClientNode {
        _updateUser: User;
        _createUser: User;
        title: string;
        style: SysClientNotificationActionStyle;
        link: string;
    }
    export interface SysClientNotificationActionInput extends VitalClientNodeInput {
        title?: string;
        style?: SysClientNotificationActionStyle;
        link?: string;
    }
    export interface SysClientNotificationActionBinding extends VitalClientNode {
        _updateUser: User;
        _createUser: User;
        title: string;
        style: SysClientNotificationActionStyle;
        link: string;
    }
    export interface SysClientNotificationAction$Operations {
        query: QueryOperation<SysClientNotificationAction>;
        read: ReadOperation<SysClientNotificationAction>;
        aggregate: {
            read: AggregateReadOperation<SysClientNotificationAction>;
            query: AggregateQueryOperation<SysClientNotificationAction>;
        };
        create: CreateOperation<SysClientNotificationActionInput, SysClientNotificationAction>;
        getDuplicate: GetDuplicateOperation<SysClientNotificationAction>;
        update: UpdateOperation<SysClientNotificationActionInput, SysClientNotificationAction>;
        updateById: UpdateByIdOperation<SysClientNotificationActionInput, SysClientNotificationAction>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        getDefaults: GetDefaultsOperation<SysClientNotificationAction>;
    }
    export interface SysClientUserSettings extends VitalClientNode {
        _updateUser: User;
        _createUser: User;
        user: User;
        title: string;
        description: string;
        screenId: string;
        elementId: string;
        isSelected: boolean;
        content: string;
    }
    export interface SysClientUserSettingsInput extends VitalClientNodeInput {
        title?: string;
        description?: string;
        screenId?: string;
        elementId?: string;
        isSelected?: boolean | string;
        content?: string;
    }
    export interface SysClientUserSettingsBinding extends VitalClientNode {
        _updateUser: User;
        _createUser: User;
        user: User;
        title: string;
        description: string;
        screenId: string;
        elementId: string;
        isSelected: boolean;
        content: any;
    }
    export interface SysClientUserSettings$Operations {
        query: QueryOperation<SysClientUserSettings>;
        read: ReadOperation<SysClientUserSettings>;
        aggregate: {
            read: AggregateReadOperation<SysClientUserSettings>;
            query: AggregateQueryOperation<SysClientUserSettings>;
        };
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        getDefaults: GetDefaultsOperation<SysClientUserSettings>;
    }
    export interface SysCustomRecord extends ClientNode {
        _updateUser: User;
        _createUser: User;
        bundleId: string;
        factoryName: string;
    }
    export interface SysCustomRecordInput extends ClientNodeInput {
        bundleId?: string;
        factoryName?: string;
    }
    export interface SysCustomRecordBinding extends ClientNode {
        _updateUser: User;
        _createUser: User;
        bundleId: string;
        factoryName: string;
    }
    export interface SysCustomRecord$Operations {
        create: CreateOperation<SysCustomRecordInput, SysCustomRecord>;
        getDuplicate: GetDuplicateOperation<SysCustomRecord>;
        getDefaults: GetDefaultsOperation<SysCustomRecord>;
    }
    export interface SysCustomer extends ClientNode {
        customerId: string;
        name: string;
    }
    export interface SysCustomerInput extends ClientNodeInput {
        customerId?: string;
        name?: string;
    }
    export interface SysCustomerBinding extends ClientNode {
        customerId: string;
        name: string;
    }
    export interface SysCustomer$Operations {
        query: QueryOperation<SysCustomer>;
        read: ReadOperation<SysCustomer>;
        aggregate: {
            read: AggregateReadOperation<SysCustomer>;
            query: AggregateQueryOperation<SysCustomer>;
        };
        update: UpdateOperation<SysCustomerInput, SysCustomer>;
        updateById: UpdateByIdOperation<SysCustomerInput, SysCustomer>;
        getDefaults: GetDefaultsOperation<SysCustomer>;
    }
    export interface SysDeviceToken extends ClientNode {
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        tokenId: string;
        expiration: string;
        name: string;
        loginUrl: string;
        loginTestUrl: string;
    }
    export interface SysDeviceTokenInput extends ClientNodeInput {
        _vendor?: integer | string;
        tokenId?: string;
        expiration?: string;
        name?: string;
    }
    export interface SysDeviceTokenBinding extends ClientNode {
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        tokenId: string;
        expiration: string;
        name: string;
        loginUrl: string;
        loginTestUrl: string;
    }
    export interface SysDeviceToken$Mutations {
        createDeviceToken: Node$Operation<
            {},
            {
                tokenId: string;
                expires: string;
            }
        >;
        deleteDeviceToken: Node$Operation<
            {
                tokenId?: string;
            },
            boolean
        >;
    }
    export interface SysDeviceToken$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface SysDeviceToken$Operations {
        query: QueryOperation<SysDeviceToken>;
        read: ReadOperation<SysDeviceToken>;
        aggregate: {
            read: AggregateReadOperation<SysDeviceToken>;
            query: AggregateQueryOperation<SysDeviceToken>;
        };
        create: CreateOperation<SysDeviceTokenInput, SysDeviceToken>;
        getDuplicate: GetDuplicateOperation<SysDeviceToken>;
        update: UpdateOperation<SysDeviceTokenInput, SysDeviceToken>;
        updateById: UpdateByIdOperation<SysDeviceTokenInput, SysDeviceToken>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: SysDeviceToken$Mutations;
        lookups(dataOrId: string | { data: SysDeviceTokenInput }): SysDeviceToken$Lookups;
        getDefaults: GetDefaultsOperation<SysDeviceToken>;
    }
    export interface SysNote extends ClientNode {
        _tags: SysTag[];
        _updateUser: User;
        _createUser: User;
        title: string;
        content: TextStream;
    }
    export interface SysNoteInput extends ClientNodeInput {
        _tags?: (integer | string)[];
        title?: string;
        content?: TextStream;
    }
    export interface SysNoteBinding extends ClientNode {
        _tags: SysTag[];
        _updateUser: User;
        _createUser: User;
        title: string;
        content: TextStream;
    }
    export interface SysNote$Operations {
        query: QueryOperation<SysNote>;
        read: ReadOperation<SysNote>;
        aggregate: {
            read: AggregateReadOperation<SysNote>;
            query: AggregateQueryOperation<SysNote>;
        };
        create: CreateOperation<SysNoteInput, SysNote>;
        getDuplicate: GetDuplicateOperation<SysNote>;
        update: UpdateOperation<SysNoteInput, SysNote>;
        updateById: UpdateByIdOperation<SysNoteInput, SysNote>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        getDefaults: GetDefaultsOperation<SysNote>;
    }
    export interface SysNoteAssociation extends ClientNode {
        _updateUser: User;
        _createUser: User;
        sourceNodeName: string;
        sourceNodeId: integer;
        note: SysNote;
    }
    export interface SysNoteAssociationInput extends ClientNodeInput {
        sourceNodeName?: string;
        sourceNodeId?: integer | string;
        note?: integer | string;
    }
    export interface SysNoteAssociationBinding extends ClientNode {
        _updateUser: User;
        _createUser: User;
        sourceNodeName: string;
        sourceNodeId: integer;
        note: SysNote;
    }
    export interface SysNoteAssociation$Operations {
        query: QueryOperation<SysNoteAssociation>;
        read: ReadOperation<SysNoteAssociation>;
        aggregate: {
            read: AggregateReadOperation<SysNoteAssociation>;
            query: AggregateQueryOperation<SysNoteAssociation>;
        };
        getDefaults: GetDefaultsOperation<SysNoteAssociation>;
    }
    export interface SysPackAllocation extends ClientNode {
        _updateUser: User;
        _createUser: User;
        package: SysPackVersion;
        isActivable: boolean;
        status: SysPackAllocation;
        isActive: boolean;
    }
    export interface SysPackAllocationInput extends ClientNodeInput {
        package?: integer | string;
        isActivable?: boolean | string;
        status?: SysPackAllocation;
        isActive?: boolean | string;
    }
    export interface SysPackAllocationBinding extends ClientNode {
        _updateUser: User;
        _createUser: User;
        package: SysPackVersion;
        isActivable: boolean;
        status: SysPackAllocation;
        isActive: boolean;
    }
    export interface SysPackAllocation$Mutations {
        activate: Node$Operation<
            {
                packageId?: string;
            },
            boolean
        >;
        deactivate: Node$Operation<
            {
                packageId?: string;
            },
            boolean
        >;
    }
    export interface SysPackAllocation$Lookups {
        package: QueryOperation<SysPackVersion>;
    }
    export interface SysPackAllocation$Operations {
        create: CreateOperation<SysPackAllocationInput, SysPackAllocation>;
        getDuplicate: GetDuplicateOperation<SysPackAllocation>;
        mutations: SysPackAllocation$Mutations;
        lookups(dataOrId: string | { data: SysPackAllocationInput }): SysPackAllocation$Lookups;
        getDefaults: GetDefaultsOperation<SysPackAllocation>;
    }
    export interface SysPackVersion extends ClientNode {
        name: string;
        version: string;
        isBundle: boolean;
        isHidden: boolean;
        isReleased: boolean;
        sqlSchemaVersion: string;
        isUpgradeBundle: boolean;
    }
    export interface SysPackVersionInput extends ClientNodeInput {
        name?: string;
        version?: string;
        isBundle?: boolean | string;
        isHidden?: boolean | string;
        isReleased?: boolean | string;
        sqlSchemaVersion?: string;
        isUpgradeBundle?: boolean | string;
    }
    export interface SysPackVersionBinding extends ClientNode {
        name: string;
        version: string;
        isBundle: boolean;
        isHidden: boolean;
        isReleased: boolean;
        sqlSchemaVersion: string;
        isUpgradeBundle: boolean;
    }
    export interface SysPackVersion$Operations {
        query: QueryOperation<SysPackVersion>;
        read: ReadOperation<SysPackVersion>;
        aggregate: {
            read: AggregateReadOperation<SysPackVersion>;
            query: AggregateQueryOperation<SysPackVersion>;
        };
        create: CreateOperation<SysPackVersionInput, SysPackVersion>;
        getDuplicate: GetDuplicateOperation<SysPackVersion>;
        update: UpdateOperation<SysPackVersionInput, SysPackVersion>;
        updateById: UpdateByIdOperation<SysPackVersionInput, SysPackVersion>;
        getDefaults: GetDefaultsOperation<SysPackVersion>;
    }
    export interface SysPatchHistory extends ClientNode {
        _updateUser: User;
        _createUser: User;
        patchName: string;
        version: string;
        packageName: string;
        result: SysPatchHistory;
    }
    export interface SysPatchHistoryInput extends ClientNodeInput {
        patchName?: string;
        version?: string;
        packageName?: string;
        result?: SysPatchHistory;
    }
    export interface SysPatchHistoryBinding extends ClientNode {
        _updateUser: User;
        _createUser: User;
        patchName: string;
        version: string;
        packageName: string;
        result: SysPatchHistory;
    }
    export interface SysPatchHistory$Operations {
        query: QueryOperation<SysPatchHistory>;
        read: ReadOperation<SysPatchHistory>;
        aggregate: {
            read: AggregateReadOperation<SysPatchHistory>;
            query: AggregateQueryOperation<SysPatchHistory>;
        };
        create: CreateOperation<SysPatchHistoryInput, SysPatchHistory>;
        getDuplicate: GetDuplicateOperation<SysPatchHistory>;
        update: UpdateOperation<SysPatchHistoryInput, SysPatchHistory>;
        updateById: UpdateByIdOperation<SysPatchHistoryInput, SysPatchHistory>;
        getDefaults: GetDefaultsOperation<SysPatchHistory>;
    }
    export interface SysServiceOption extends ClientNode {
        package: string;
        optionName: string;
        description: string;
        status: ServiceOptionStatus;
        isHidden: boolean;
        isActiveByDefault: boolean;
        childServiceOptions: ClientCollection<SysServiceOptionToServiceOption>;
        parentServiceOptions: ClientCollection<SysServiceOptionToServiceOption>;
    }
    export interface SysServiceOptionInput extends ClientNodeInput {
        package?: string;
        optionName?: string;
        description?: string;
        status?: ServiceOptionStatus;
        isHidden?: boolean | string;
        isActiveByDefault?: boolean | string;
        childServiceOptions?: Partial<SysServiceOptionToServiceOptionInput>[];
    }
    export interface SysServiceOptionBinding extends ClientNode {
        package: string;
        optionName: string;
        description: string;
        status: ServiceOptionStatus;
        isHidden: boolean;
        isActiveByDefault: boolean;
        childServiceOptions: ClientCollection<SysServiceOptionToServiceOptionBinding>;
        parentServiceOptions: ClientCollection<SysServiceOptionToServiceOption>;
    }
    export interface SysServiceOption$Operations {
        query: QueryOperation<SysServiceOption>;
        read: ReadOperation<SysServiceOption>;
        aggregate: {
            read: AggregateReadOperation<SysServiceOption>;
            query: AggregateQueryOperation<SysServiceOption>;
        };
        update: UpdateOperation<SysServiceOptionInput, SysServiceOption>;
        updateById: UpdateByIdOperation<SysServiceOptionInput, SysServiceOption>;
        getDefaults: GetDefaultsOperation<SysServiceOption>;
    }
    export interface SysServiceOptionState extends ClientNode {
        _updateUser: User;
        _createUser: User;
        isActivable: boolean;
        isReadyToUse: boolean;
        isActive: boolean;
        serviceOption: SysServiceOption;
        isHiddenByConfig: boolean;
        isPackageActive: boolean;
        imageStatus: BinaryStream;
        isAvailable: boolean;
    }
    export interface SysServiceOptionStateInput extends ClientNodeInput {
        isActivable?: boolean | string;
        isActive?: boolean | string;
        serviceOption?: integer | string;
    }
    export interface SysServiceOptionStateBinding extends ClientNode {
        _updateUser: User;
        _createUser: User;
        isActivable: boolean;
        isReadyToUse: boolean;
        isActive: boolean;
        serviceOption: SysServiceOption;
        isHiddenByConfig: boolean;
        isPackageActive: boolean;
        imageStatus: BinaryStream;
        isAvailable: boolean;
    }
    export interface SysServiceOptionState$Lookups {
        serviceOption: QueryOperation<SysServiceOption>;
    }
    export interface SysServiceOptionState$Operations {
        query: QueryOperation<SysServiceOptionState>;
        read: ReadOperation<SysServiceOptionState>;
        aggregate: {
            read: AggregateReadOperation<SysServiceOptionState>;
            query: AggregateQueryOperation<SysServiceOptionState>;
        };
        update: UpdateOperation<SysServiceOptionStateInput, SysServiceOptionState>;
        updateById: UpdateByIdOperation<SysServiceOptionStateInput, SysServiceOptionState>;
        lookups(dataOrId: string | { data: SysServiceOptionStateInput }): SysServiceOptionState$Lookups;
        getDefaults: GetDefaultsOperation<SysServiceOptionState>;
    }
    export interface SysServiceOptionToServiceOption extends VitalClientNode {
        childServiceOption: SysServiceOption;
        parentServiceOption: SysServiceOption;
    }
    export interface SysServiceOptionToServiceOptionInput extends VitalClientNodeInput {
        childServiceOption?: integer | string;
    }
    export interface SysServiceOptionToServiceOptionBinding extends VitalClientNode {
        childServiceOption: SysServiceOption;
        parentServiceOption: SysServiceOption;
    }
    export interface SysServiceOptionToServiceOption$Lookups {
        childServiceOption: QueryOperation<SysServiceOption>;
    }
    export interface SysServiceOptionToServiceOption$Operations {
        query: QueryOperation<SysServiceOptionToServiceOption>;
        read: ReadOperation<SysServiceOptionToServiceOption>;
        aggregate: {
            read: AggregateReadOperation<SysServiceOptionToServiceOption>;
            query: AggregateQueryOperation<SysServiceOptionToServiceOption>;
        };
        create: CreateOperation<SysServiceOptionToServiceOptionInput, SysServiceOptionToServiceOption>;
        getDuplicate: GetDuplicateOperation<SysServiceOptionToServiceOption>;
        update: UpdateOperation<SysServiceOptionToServiceOptionInput, SysServiceOptionToServiceOption>;
        updateById: UpdateByIdOperation<SysServiceOptionToServiceOptionInput, SysServiceOptionToServiceOption>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        lookups(
            dataOrId: string | { data: SysServiceOptionToServiceOptionInput },
        ): SysServiceOptionToServiceOption$Lookups;
        getDefaults: GetDefaultsOperation<SysServiceOptionToServiceOption>;
    }
    export interface SysTag extends ClientNode {
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        name: string;
        description: string;
    }
    export interface SysTagInput extends ClientNodeInput {
        _vendor?: integer | string;
        name?: string;
        description?: string;
    }
    export interface SysTagBinding extends ClientNode {
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        name: string;
        description: string;
    }
    export interface SysTag$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface SysTag$Operations {
        query: QueryOperation<SysTag>;
        read: ReadOperation<SysTag>;
        aggregate: {
            read: AggregateReadOperation<SysTag>;
            query: AggregateQueryOperation<SysTag>;
        };
        create: CreateOperation<SysTagInput, SysTag>;
        getDuplicate: GetDuplicateOperation<SysTag>;
        update: UpdateOperation<SysTagInput, SysTag>;
        updateById: UpdateByIdOperation<SysTagInput, SysTag>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        lookups(dataOrId: string | { data: SysTagInput }): SysTag$Lookups;
        getDefaults: GetDefaultsOperation<SysTag>;
    }
    export interface SysTenant extends ClientNode {
        tenantId: string;
        name: string;
        directoryName: string;
        customer: SysCustomer;
    }
    export interface SysTenantInput extends ClientNodeInput {
        tenantId?: string;
        name?: string;
        directoryName?: string;
        customer?: integer | string;
    }
    export interface SysTenantBinding extends ClientNode {
        tenantId: string;
        name: string;
        directoryName: string;
        customer: SysCustomer;
    }
    export interface SysTenant$Queries {
        getTenantInformation: Node$Operation<
            {},
            {
                version: string;
                tenantId: string;
                sumologicLink: string;
            }
        >;
    }
    export interface SysTenant$Mutations {
        resetTenantDocuments: Node$Operation<
            {
                tenantId: string;
            },
            boolean
        >;
    }
    export interface SysTenant$Lookups {
        customer: QueryOperation<SysCustomer>;
    }
    export interface SysTenant$Operations {
        queries: SysTenant$Queries;
        mutations: SysTenant$Mutations;
        lookups(dataOrId: string | { data: SysTenantInput }): SysTenant$Lookups;
        getDefaults: GetDefaultsOperation<SysTenant>;
    }
    export interface SysUpgrade extends ClientNode {
        bundleId: string;
        managedItems: string;
    }
    export interface SysUpgradeInput extends ClientNodeInput {
        bundleId?: string;
        managedItems?: string;
    }
    export interface SysUpgradeBinding extends ClientNode {
        bundleId: string;
        managedItems: any;
    }
    export interface SysUpgrade$Operations {
        query: QueryOperation<SysUpgrade>;
        read: ReadOperation<SysUpgrade>;
        aggregate: {
            read: AggregateReadOperation<SysUpgrade>;
            query: AggregateQueryOperation<SysUpgrade>;
        };
        update: UpdateOperation<SysUpgradeInput, SysUpgrade>;
        updateById: UpdateByIdOperation<SysUpgradeInput, SysUpgrade>;
        getDefaults: GetDefaultsOperation<SysUpgrade>;
    }
    export interface SysVendor extends ClientNode {
        name: string;
        description: string;
    }
    export interface SysVendorInput extends ClientNodeInput {
        name?: string;
        description?: string;
    }
    export interface SysVendorBinding extends ClientNode {
        name: string;
        description: string;
    }
    export interface SysVendor$Operations {
        query: QueryOperation<SysVendor>;
        read: ReadOperation<SysVendor>;
        aggregate: {
            read: AggregateReadOperation<SysVendor>;
            query: AggregateQueryOperation<SysVendor>;
        };
        update: UpdateOperation<SysVendorInput, SysVendor>;
        updateById: UpdateByIdOperation<SysVendorInput, SysVendor>;
        getDefaults: GetDefaultsOperation<SysVendor>;
    }
    export interface User extends ClientNode {
        _updateUser: User;
        _createUser: User;
        email: string;
        firstName: string;
        lastName: string;
        isActive: boolean;
        photo: BinaryStream;
        displayName: string;
        userType: UserType;
        isAdministrator: boolean;
        isDemoPersona: boolean;
        isApiUser: boolean;
        preferences: UserPreferences;
        clientSettings: ClientCollection<SysClientUserSettings>;
        isOperatorUser: boolean;
    }
    export interface UserInput extends ClientNodeInput {
        email?: string;
        firstName?: string;
        lastName?: string;
        isActive?: boolean | string;
        photo?: BinaryStream;
        userType?: UserType;
        isAdministrator?: boolean | string;
        isDemoPersona?: boolean | string;
        isApiUser?: boolean | string;
        operatorCode?: string;
        preferences?: UserPreferencesInput;
        clientSettings?: Partial<SysClientUserSettingsInput>[];
        isOperatorUser?: boolean | string;
    }
    export interface UserBinding extends ClientNode {
        _updateUser: User;
        _createUser: User;
        email: string;
        firstName: string;
        lastName: string;
        isActive: boolean;
        photo: BinaryStream;
        displayName: string;
        userType: UserType;
        isAdministrator: boolean;
        isDemoPersona: boolean;
        isApiUser: boolean;
        operatorCode: string;
        preferences: UserPreferencesBinding;
        clientSettings: ClientCollection<SysClientUserSettingsBinding>;
        isOperatorUser: boolean;
    }
    export interface User$Queries {
        demoPersonas: Node$Operation<
            {},
            {
                _id: string;
                displayName: string;
                email: string;
                createdBy: string;
                createStamp: string;
                updatedBy: string;
                updateStamp: string;
            }[]
        >;
    }
    export interface User$Mutations {
        setDemoPersona: Node$Operation<
            {
                email: string;
            },
            boolean
        >;
        logPageVisit: Node$Operation<
            {
                path: string;
            },
            string[]
        >;
        updateClientSettings: Node$Operation<
            {
                clientSettings: {
                    _id?: integer | string;
                    screenId?: string;
                    elementId?: string;
                    title?: string;
                    description?: string;
                    content?: string;
                    _action?: string;
                }[];
            },
            User
        >;
        sendWelcomeMail: Node$Operation<
            {
                users: string[];
                isAdmin: boolean | string;
            },
            boolean
        >;
    }
    export interface User$Operations {
        query: QueryOperation<User>;
        read: ReadOperation<User>;
        aggregate: {
            read: AggregateReadOperation<User>;
            query: AggregateQueryOperation<User>;
        };
        queries: User$Queries;
        create: CreateOperation<UserInput, User>;
        getDuplicate: GetDuplicateOperation<User>;
        duplicate: DuplicateOperation<string, UserInput, User>;
        update: UpdateOperation<UserInput, User>;
        updateById: UpdateByIdOperation<UserInput, User>;
        mutations: User$Mutations;
        getDefaults: GetDefaultsOperation<User>;
    }
    export interface UserNavigation extends VitalClientNode {
        _updateUser: User;
        _createUser: User;
        user: User;
    }
    export interface UserNavigationInput extends VitalClientNodeInput {}
    export interface UserNavigationBinding extends VitalClientNode {
        _updateUser: User;
        _createUser: User;
        user: User;
    }
    export interface UserNavigation$Operations {
        query: QueryOperation<UserNavigation>;
        read: ReadOperation<UserNavigation>;
        aggregate: {
            read: AggregateReadOperation<UserNavigation>;
            query: AggregateQueryOperation<UserNavigation>;
        };
        getDefaults: GetDefaultsOperation<UserNavigation>;
    }
    export interface UserPreferences extends VitalClientNode {
        _updateUser: User;
        _createUser: User;
        user: User;
        isWelcomeMailSent: boolean;
        isExternal: boolean;
        importExportDelimiter: string;
        importExportDateFormat: UserImportExportDateFormat;
    }
    export interface UserPreferencesInput extends VitalClientNodeInput {
        isWelcomeMailSent?: boolean | string;
        isExternal?: boolean | string;
        importExportDelimiter?: string;
        importExportDateFormat?: UserImportExportDateFormat;
    }
    export interface UserPreferencesBinding extends VitalClientNode {
        _updateUser: User;
        _createUser: User;
        user: User;
        isWelcomeMailSent: boolean;
        isExternal: boolean;
        importExportDelimiter: string;
        importExportDateFormat: UserImportExportDateFormat;
    }
    export interface UserPreferences$Queries {
        activeServiceOptions: Node$Operation<
            {},
            {
                optionName: string;
            }[]
        >;
    }
    export interface UserPreferences$Operations {
        query: QueryOperation<UserPreferences>;
        read: ReadOperation<UserPreferences>;
        aggregate: {
            read: AggregateReadOperation<UserPreferences>;
            query: AggregateQueryOperation<UserPreferences>;
        };
        queries: UserPreferences$Queries;
        getDefaults: GetDefaultsOperation<UserPreferences>;
    }
    export interface Package {
        '@sage/xtrem-system/Company': Company$Operations;
        '@sage/xtrem-system/Locale': Locale$Operations;
        '@sage/xtrem-system/Site': Site$Operations;
        '@sage/xtrem-system/SysChangelog': SysChangelog$Operations;
        '@sage/xtrem-system/SysClientNotification': SysClientNotification$Operations;
        '@sage/xtrem-system/SysClientNotificationAction': SysClientNotificationAction$Operations;
        '@sage/xtrem-system/SysClientUserSettings': SysClientUserSettings$Operations;
        '@sage/xtrem-system/SysCustomRecord': SysCustomRecord$Operations;
        '@sage/xtrem-system/SysCustomer': SysCustomer$Operations;
        '@sage/xtrem-system/SysDeviceToken': SysDeviceToken$Operations;
        '@sage/xtrem-system/SysNote': SysNote$Operations;
        '@sage/xtrem-system/SysNoteAssociation': SysNoteAssociation$Operations;
        '@sage/xtrem-system/SysPackAllocation': SysPackAllocation$Operations;
        '@sage/xtrem-system/SysPackVersion': SysPackVersion$Operations;
        '@sage/xtrem-system/SysPatchHistory': SysPatchHistory$Operations;
        '@sage/xtrem-system/SysServiceOption': SysServiceOption$Operations;
        '@sage/xtrem-system/SysServiceOptionState': SysServiceOptionState$Operations;
        '@sage/xtrem-system/SysServiceOptionToServiceOption': SysServiceOptionToServiceOption$Operations;
        '@sage/xtrem-system/SysTag': SysTag$Operations;
        '@sage/xtrem-system/SysTenant': SysTenant$Operations;
        '@sage/xtrem-system/SysUpgrade': SysUpgrade$Operations;
        '@sage/xtrem-system/SysVendor': SysVendor$Operations;
        '@sage/xtrem-system/User': User$Operations;
        '@sage/xtrem-system/UserNavigation': UserNavigation$Operations;
        '@sage/xtrem-system/UserPreferences': UserPreferences$Operations;
    }
    export interface GraphApi extends Package {}
}
declare module '@sage/xtrem-system-api' {
    export type * from '@sage/xtrem-system-api-partial';
}
