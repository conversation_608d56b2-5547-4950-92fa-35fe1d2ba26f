{"extends": "../../tsconfig-base.json", "compilerOptions": {"outDir": "build", "rootDir": ".", "baseUrl": "."}, "include": ["index.ts", "application.ts", "lib/**/*", "test/**/*.ts", "test/**/*.json", "api/api.d.ts"], "exclude": ["lib/pages/**/*", "lib/widgets/**/*", "lib/page-extensions/**/*", "lib/stickers/**/*", "lib/i18n/**/*", "**/*.feature", "**/*.png", "lib/client-functions/**/*"], "references": [{"path": "../xtrem-authorization"}, {"path": "../../front-end/xtrem-client"}, {"path": "../xtrem-communication"}, {"path": "../../back-end/xtrem-core"}, {"path": "../../shared/xtrem-date-time"}, {"path": "../../shared/xtrem-decimal"}, {"path": "../../shared/xtrem-shared"}, {"path": "../xtrem-system"}, {"path": "../../front-end/xtrem-ui"}, {"path": "../../back-end/eslint-plugin-xtrem"}, {"path": "../../cli/xtrem-cli"}]}