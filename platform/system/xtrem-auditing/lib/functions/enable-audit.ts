import { Application, asyncArray, Context, ContextOptions, Logger, LogicError, NodeFactory } from '@sage/xtrem-core';

const logger = Logger.getLogger(__filename, 'activation');

/**
 * Returns the SQL to update a record to trigger the audit trigger.
 * It recurses on the base factory if it exists.
 */
const getUpdateSqlToTriggerAudit = (schemaName: string, factory: NodeFactory): string => {
    return `${factory.baseFactory ? getUpdateSqlToTriggerAudit(schemaName, factory.baseFactory) : ''}
    UPDATE ${schemaName}.${factory.tableName} upd
    SET _id = upd._id
      WHERE upd._tenant_id = tenant_id AND upd._id = r._id;
`;
};

/**
 * Returns the SQL to create missing audit records, for a given concrete factory.
 *
 * The returned SQL loops over all records of the given factory that do not have an audit record yet.
 * For each record, it executes a fake update on its table(s) to trigger the audit trigger and fill the audit record.
 *
 * If factory has a base factory, we have to update all the tables of the hierarchy to ensure that all the columns
 * are copied to the audit record.
 */
const getAuditInitFunctionSql = (schemaName: string, factory: NodeFactory): string => {
    return `
CREATE OR REPLACE FUNCTION pg_temp.enable_audit(tenant_id TEXT, root TEXT) RETURNS INT8
LANGUAGE plpgsql
AS $$
DECLARE
	r RECORD;
    created INT8;
BEGIN
    RAISE NOTICE 'Enabling audit on %', root;
	created := 0;
  	FOR r in (
		SELECT src._id
		FROM ${schemaName}.${factory.tableName} src
		WHERE
			src._tenant_id = tenant_id
			AND NOT EXISTS (SELECT _id FROM ${schemaName}.sys_audit_log log
				WHERE log._tenant_id = src._tenant_id
				AND log.root_table_name = root
				AND log.record_id = src._id))
  	LOOP
        BEGIN
            ${getUpdateSqlToTriggerAudit(schemaName, factory)}
            RAISE NOTICE 'Audit record created for %:%', root, r._id;
            created := created + 1;
        END;
  	END LOOP;
    RETURN created;
END $$;`;
};

/**
 * Creates the missing audit records for a given factory.
 * It loops over all records of the given factory that do not have an audit record yet.
 * For each record, it executes a fake update on its table(s) to trigger the audit trigger and fill the audit record.
 */
const createMissingAuditRecords = async (context: Context, factory: NodeFactory): Promise<number> => {
    const schemaName = context.application.schemaName;
    const tenantId = context.tenantId;
    if (!tenantId) throw new LogicError('Tenant ID is required to enable auditing');

    // Service option change hasn't been committed yet so we need to set xtrem.is_audit_enabled
    await context.executeSql(`SELECT set_config('xtrem.is_audit_enabled', 'true', true);`, [], { allowUnsafe: true });

    // Create the function to enable auditing for the given factory
    const functionSql = getAuditInitFunctionSql(schemaName, factory);
    await context.executeSql(functionSql, [], { allowUnsafe: true });

    logger.info(`Creating missing audit records for ${factory.name} ...`);
    const t0 = performance.now();
    // Execute the function
    const created = await context.executeSql<{ enable_audit: number }[]>(`SELECT pg_temp.enable_audit($1, $2);`, [
        tenantId,
        factory.rootFactory.tableName,
    ]);
    const createdCount = created[0]?.enable_audit;
    logger.info(
        `Created missing audit records for ${factory.name}: ${createdCount} audit records created in ${performance.now() - t0} ms`,
    );
    return createdCount;
};

const notifyAuditInitStarted = (context: Context, factoryCount: number): Promise<void> =>
    context.notifyUser({
        title: context.localize('@sage/xtrem-auditing/audit_init_started_title', 'Initializing audit data'),
        description: context.localize(
            '@sage/xtrem-auditing/audit_init_started_description',
            `Initializing audit data for {{factoryCount}} entities`,
            { factoryCount },
        ),
        icon: 'info',
        level: 'info',
        shouldDisplayToast: true,
        actions: [],
    });

const notifyAuditInitSucceeded = (
    context: Context,
    factoryCount: number,
    recordsCreated: number,
    duration: number,
): Promise<void> =>
    context.notifyUser({
        title: context.localize('@sage/xtrem-auditing/audit_init_success_title', 'Audit successfully initialized'),
        description: context.localize(
            '@sage/xtrem-auditing/audit_init_success_description',
            'Audit successfully initialized for {{factoryCount}} entities. {{recordsCreated}} audit records created in {{duration}} ms.',
            { factoryCount, recordsCreated, duration },
        ),
        icon: 'tick',
        level: 'success',
        shouldDisplayToast: true,
        actions: [],
    });

const notifyAuditInitFailed = (
    context: Context,
    error: Error,
    recordsCreated: number,
    duration: number,
): Promise<void> =>
    context.notifyUser({
        title: context.localize('@sage/xtrem-auditing/audit_init_failed_title', 'Audit data initialization failed'),
        description: context.localize(
            '@sage/xtrem-auditing/audit_init_failed_description',
            'Audit initialization failed after {{duration}} ms with the following error: {{message}}. {{recordsCreated}} audit records created.',
            { message: error.message, recordsCreated, duration },
        ),
        icon: 'error',
        level: 'error',
        shouldDisplayToast: true,
        actions: [],
    });

const notifyAuditInitProgress = (
    context: Context,
    i: number,
    factoryCount: number,
    recordsCreated: number,
    duration: number,
): Promise<void> =>
    context.notifyUser({
        title: context.localize('@sage/xtrem-auditing/audit_init_progress_title', 'Audit initialization in progress'),
        description: context.localize(
            '@sage/xtrem-auditing/audit_init_progress_description',
            'Audit initialization in progress: {{i}} out of {{factoryCount}} entities processed. {{recordsCreated}} audit records created in {{duration}} ms.',
            { i, factoryCount, recordsCreated, duration },
        ),
        icon: 'info',
        level: 'info',
        shouldDisplayToast: true,
        actions: [],
    });

export const runAuditInitialization = (
    application: Application,
    tenantId: string | null,
    factories: NodeFactory[],
    contextOptions: ContextOptions,
): void => {
    const dt0 = performance.now();

    let lastProgressTime = dt0;
    let recordsCreated = 0;

    const duration = (): number => Math.round(performance.now() - dt0);

    // Use application.withCommittedContext rather than context.runInIsolatedContext because main context will be destroyed before child context
    asyncArray(factories)
        .forEach((factory, i) =>
            application.withCommittedContext(
                tenantId,
                async context => {
                    recordsCreated += await createMissingAuditRecords(context, factory);
                    const now = performance.now();
                    if (now - lastProgressTime < 10_000) return;
                    lastProgressTime = now;
                    logger.info(
                        `Audit initialization progress: ${i + 1} / ${factories.length} entities processed; ${recordsCreated} audit records created in ${duration()} ms, `,
                    );
                    await notifyAuditInitProgress(context, i + 1, factories.length, recordsCreated, duration());
                },
                contextOptions,
            ),
        )
        .then(() => {
            logger.info(
                `Audit initialization completed successfully; ${recordsCreated} audit records created in ${duration()} ms, `,
            );
            return application.withCommittedContext(
                tenantId,
                context => notifyAuditInitSucceeded(context, factories.length, recordsCreated, duration()),
                contextOptions,
            );
        })
        .catch(err => {
            logger.error(
                `Audit initialization failed after ${duration()} ms; ${recordsCreated} audit records created`,
                err,
            );
            return application.withCommittedContext(
                tenantId,
                context => notifyAuditInitFailed(context, err, recordsCreated, duration()),
                contextOptions,
            );
        })
        .catch(err => {
            logger.error(`Failed to notify audit after ${duration()} ms; ${recordsCreated} audit records created`, err);
        });
};
/**
 * Enables auditing for all factories that can be audited.
 */
export const enableAudit = async (context: Context): Promise<void> => {
    // Note: we process each factory in a separate transaction
    // because we generate the missing audit records by updating the factory's table(s)
    // and we want to release the update locks quickly.
    const factories = context.application.getAllFactories().filter(factory => !factory.isAbstract && factory.canAudit);

    logger.error('Audit initialization started');
    await notifyAuditInitStarted(context, factories.length);

    const userEmail = (await context.getUserInfo()).email;
    const locale = context.currentLocale;

    const contextOptions = {
        //
        userEmail,
        locale,
        auth: { login: userEmail },
        // Disable CRUD notifications to avoid triggering workflows for the fake updates
        disableTenantCrudNotifications: true,
    };

    runAuditInitialization(context.application, context.tenantId, factories, contextOptions);
};
