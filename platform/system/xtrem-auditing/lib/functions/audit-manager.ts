import { AuditManagerInterface, Context, TriggerBuilder } from '@sage/xtrem-core';
import { TableDefinition } from '@sage/xtrem-postgres';
import { pascalCase } from '@sage/xtrem-shared';
import { auditing } from '../service-options';

const getTriggerFunctionsSql = (schemaName: string): string => {
    const subInsertColumns = {
        node_name: 'constructor',
        root_table_name: 'p_root_table_name',
        // NEW is null on DELETE and OLD is null on INSERT so we have at least one of them and we can get
        // the _tenant_id and _id with COALESCE
        _tenant_id: 'tenant_id',
        record_id: 'rid',
        operation: `TG_OP::${schemaName}.audit_operation_enum`,
        login_email: 'login_email',
        timestamp: 'NOW()',
        transaction_id: 'pg_current_xact_id()',
        record_data: 'to_json(NEW)',
        old_update_tick: 'NULL',
        new_update_tick: 'NULL',
    };

    const rootInsertColumns = {
        ...subInsertColumns,
        _create_user: 'user_id::INT8',
        _update_user: 'user_id::INT8',
        old_update_tick: 'OLD._update_tick',
        new_update_tick: 'NEW._update_tick',
    };

    return `
CREATE OR REPLACE FUNCTION ${schemaName}.audit_table()
RETURNS TRIGGER
AS
$$
    DECLARE
        -- parameters
        p_root_table_name VARCHAR;
        p_constructor VARCHAR;

        -- audit variables
        is_audit_enabled VARCHAR;
        tenant_id VARCHAR;
        rid INT8;
        login_email VARCHAR;
        user_id VARCHAR;
        locale VARCHAR;
        log_record RECORD;

        -- notify variables
        origin_id VARCHAR;
        notify_all_disabled VARCHAR;
        notify_tenant_disabled VARCHAR;
        notification_id VARCHAR;
        user_email VARCHAR;
        constructor VARCHAR;
        event VARCHAR;
        topic VARCHAR;
        envelope VARCHAR;
        payload VARCHAR;
    BEGIN
        p_root_table_name := TG_ARGV[0];
        p_constructor := TG_ARGV[1];

        SELECT COALESCE(${schemaName}.get_config('xtrem.is_audit_enabled'), 'false') INTO is_audit_enabled;
        IF (is_audit_enabled <> 'true') THEN
            RETURN NEW;
        END IF;
        SELECT COALESCE(${schemaName}.get_config('xtrem.login_email'), '') INTO login_email;
        SELECT COALESCE(${schemaName}.get_config('xtrem.transaction_user_id'), '') INTO user_id;
        SELECT COALESCE(${schemaName}.get_config('xtrem.locale'), 'base') INTO locale;

        tenant_id := COALESCE(NEW._tenant_id, OLD._tenant_id);
        rid := COALESCE(NEW._id, OLD._id);

        IF p_constructor != '' THEN
            constructor := p_constructor;
        ELSE
            constructor := COALESCE(NEW._constructor, OLD._constructor);
        END IF;


        SELECT * FROM ${schemaName}.sys_audit_log
        WHERE root_table_name = p_root_table_name
            AND record_id = rid
            AND transaction_id::TEXT = pg_current_xact_id()::TEXT
        INTO log_record;

        IF log_record IS NULL THEN
            RAISE NOTICE 'Inserting new audit log record %:%', p_root_table_name, NEW._id;
            IF p_root_table_name = TG_TABLE_NAME THEN
                INSERT INTO ${schemaName}.sys_audit_log (${Object.keys(rootInsertColumns).join(', ')})
                    VALUES (${Object.values(rootInsertColumns).join(', ')});
	        RAISE NOTICE 'Inserted new audit log record root_table=%, table=%, _id=%, transaction_id=%, update_tick=%->%', p_root_table_name, TG_TABLE_NAME, NEW._id, pg_current_xact_id(), OLD._update_tick, NEW._update_tick;
            ELSE
                INSERT INTO ${schemaName}.sys_audit_log (${Object.keys(subInsertColumns).join(', ')})
                    VALUES (${Object.values(subInsertColumns).join(', ')});
	        RAISE NOTICE 'Inserted new audit log record root_table=%, table=%, _id=%, transaction_id=%', p_root_table_name, TG_TABLE_NAME, NEW._id, pg_current_xact_id();
            END IF;
        ELSE
            RAISE NOTICE 'Updating audit log record %:%', p_root_table_name, NEW._id;
            IF p_root_table_name = TG_TABLE_NAME THEN
                UPDATE ${schemaName}.sys_audit_log
                SET record_data = log_record.record_data || to_jsonb(NEW), new_update_tick = NEW._update_tick
                WHERE root_table_name = p_root_table_name
                    AND record_id = NEW._id
                    AND transaction_id = pg_current_xact_id()::TEXT;
	        RAISE NOTICE 'Updated audit log record %:%, transaction_id=%, updateTick=%->%', p_root_table_name, NEW._id, pg_current_xact_id(), log_record.old_update_tick, NEW._update_tick;
            ELSE
                UPDATE ${schemaName}.sys_audit_log
                SET record_data = log_record.record_data || to_jsonb(NEW)
                WHERE root_table_name = p_root_table_name
                    AND record_id = NEW._id
                    AND transaction_id = pg_current_xact_id()::TEXT;
	        RAISE NOTICE 'Updated audit log record %:%, transaction_id=%', p_root_table_name, NEW._id, pg_current_xact_id();
            END IF;
        END IF;

        -- do not send a notification for the update if the record was created in the same transaction
        -- this will happen with nodes with deferred saves (sales order for instance)
        IF (p_root_table_name = TG_TABLE_NAME) AND (log_record IS NULL) THEN
            SELECT COALESCE(${schemaName}.get_config('xtrem.notification.disable.ALL'), 'false') INTO notify_all_disabled;
            SELECT COALESCE(${schemaName}.get_config('xtrem.notification.disable.t_' || tenant_id), 'false') INTO notify_tenant_disabled;

            IF (notify_all_disabled <> 'true' and notify_tenant_disabled <> 'true') THEN
                SELECT COALESCE(${schemaName}.get_config('xtrem.origin_id'), '') INTO origin_id;
                SELECT COALESCE(${schemaName}.get_config('xtrem.user_email'), '') INTO user_email;
                SELECT ${schemaName}.nanoid() INTO notification_id;

                CASE TG_OP
                    WHEN 'INSERT' THEN event := 'created';
                    WHEN 'UPDATE' THEN event := 'updated';
                    WHEN 'DELETE' THEN event := 'deleted';
                END CASE;

                topic := constructor || '/' || event;
                payload := '{ "_id":' || rid || ', "_updateTick":' || COALESCE(NEW._update_tick, OLD._update_tick) || '}';

                RAISE NOTICE 'Inserted new notification %:%', topic, notification_id;
                INSERT INTO ${schemaName}.sys_notification
                    (tenant_id, origin_id, notification_id, reply_id, reply_topic, user_email, login, locale,
                    topic, payload, status, _source_id, _update_tick, _create_stamp, _update_stamp)
                VALUES (tenant_id, origin_id, notification_id, '', '', user_email, login_email, locale,
                    topic, payload, 'pending', '', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

                RAISE NOTICE 'Notifying %:%', TG_OP, event;
                PERFORM pg_notify('notification_queued', '{"data":"{\\"topic\\":\\"' || event || '\\"}"}');
            END IF;
        END IF;


        RETURN NEW;
    END;
$$
LANGUAGE plpgsql;
`;
};

const isAuditEnabled = (context: Context): Promise<boolean> => {
    if (!context.tenantId) return Promise.resolve(false);
    return context.isServiceOptionEnabled(auditing);
};

function getRootTablename(tableDef: TableDefinition): string {
    if (tableDef.baseDefinition == null) return tableDef.tableName;
    return getRootTablename(tableDef.baseDefinition);
}

/**
 * @internal
 */
export const auditTriggerBuilder = TriggerBuilder.createTableTriggerBuilder('audit_table', {
    when: 'AFTER',
    event: 'DELETE OR INSERT OR UPDATE',
    functionParametersProvider(tableDef: TableDefinition): string {
        const hasConstructor = tableDef.columns?.find(col => col.name === '_constructor');
        const constructorName = hasConstructor ? '' : pascalCase(tableDef.tableName);
        return `'${getRootTablename(tableDef)}', '${constructorName}'`;
    },
});

export const auditManager: AuditManagerInterface = {
    getTriggerFunctionsSql,
    isAuditEnabled,
    auditTriggerBuilder,
};
