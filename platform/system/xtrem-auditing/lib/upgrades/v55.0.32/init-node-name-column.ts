import { CustomSqlAction } from '@sage/xtrem-system';

export const initNodeNameColumn = new CustomSqlAction({
    description: 'Initialize value of node_name column in sys_audit_log table',
    body: async helper => {
        await helper.executeSql(`
        DO $$
        BEGIN
            UPDATE ${helper.schemaName}.sys_audit_log SET node_name =
                CASE
                    WHEN record_data->'_constructor' IS NOT NULL THEN (record_data::jsonb->>'_constructor')::text
                    ELSE REPLACE(INITCAP(REPLACE(root_table_name, '_', ' ')), ' ', '')
                END;
        END; $$;
        `);
    },
});
