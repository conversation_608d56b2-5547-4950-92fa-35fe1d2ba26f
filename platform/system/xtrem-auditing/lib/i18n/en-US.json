{"@sage/xtrem-auditing/audit_init_failed_description": "Audit initialization failed after {{duration}} ms with the following error: {{message}}. {{recordsCreated}} audit records created.", "@sage/xtrem-auditing/audit_init_failed_title": "Audit data initialization failed", "@sage/xtrem-auditing/audit_init_progress_description": "Audit initialization in progress: {{i}} out of {{factoryCount}} entities processed. {{recordsCreated}} audit records created in {{duration}} ms.", "@sage/xtrem-auditing/audit_init_progress_title": "Audit initialization in progress", "@sage/xtrem-auditing/audit_init_started_description": "Initializing audit data for {{factoryCount}} entities", "@sage/xtrem-auditing/audit_init_started_title": "Initializing audit data", "@sage/xtrem-auditing/audit_init_success_description": "Audit successfully initialized for {{factoryCount}} entities. {{recordsCreated}} audit records created in {{duration}} ms.", "@sage/xtrem-auditing/audit_init_success_title": "Audit successfully initialized", "@sage/xtrem-auditing/data_types__audit_operation_enum__name": "Audit operation enum", "@sage/xtrem-auditing/data_types__microsecond_stamp_data_type__name": "Microsecond stamp data type", "@sage/xtrem-auditing/data_types__transaction_id_data_type__name": "Transaction ID data type", "@sage/xtrem-auditing/enums__audit_operation__DELETE": "Delete", "@sage/xtrem-auditing/enums__audit_operation__INSERT": "Insert", "@sage/xtrem-auditing/enums__audit_operation__UPDATE": "Update", "@sage/xtrem-auditing/nodes__sys_audit_log__asyncMutation__asyncExport": "Export", "@sage/xtrem-auditing/nodes__sys_audit_log__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-auditing/nodes__sys_audit_log__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-auditing/nodes__sys_audit_log__node_name": "System audit log", "@sage/xtrem-auditing/nodes__sys_audit_log__property__loginEmail": "Login email", "@sage/xtrem-auditing/nodes__sys_audit_log__property__newUpdateTick": "New update tick", "@sage/xtrem-auditing/nodes__sys_audit_log__property__nodeName": "Node name", "@sage/xtrem-auditing/nodes__sys_audit_log__property__oldUpdateTick": "Old update tick", "@sage/xtrem-auditing/nodes__sys_audit_log__property__operation": "Operation", "@sage/xtrem-auditing/nodes__sys_audit_log__property__recordData": "Record data", "@sage/xtrem-auditing/nodes__sys_audit_log__property__recordId": "Record ID", "@sage/xtrem-auditing/nodes__sys_audit_log__property__rootTableName": "Root table name", "@sage/xtrem-auditing/nodes__sys_audit_log__property__timestamp": "Timestamp", "@sage/xtrem-auditing/nodes__sys_audit_log__property__transactionId": "Transaction ID", "@sage/xtrem-auditing/package__name": "Auditing", "@sage/xtrem-auditing/service_options__auditing__name": "Auditing", "@sage/xtrem-auditing/service_options__auditing_option__name": "Auditing option"}