{"@sage/xtrem-auditing/audit_init_failed_description": "Ha habido un error al inicializar la auditoría después de {{duration}} ms. {{message}}. Registros de auditoría creados: {{recordsCreated}}", "@sage/xtrem-auditing/audit_init_failed_title": "Ha habido un error al inicializar los datos de auditoría.", "@sage/xtrem-auditing/audit_init_progress_description": "Inicialización de auditoría en curso: {{i}} de {{factoryCount}} entidades procesadas. Registros de auditoría creados en {{duration}} ms: {{recordsCreated}}", "@sage/xtrem-auditing/audit_init_progress_title": "La inicialización de auditoría está en curso.", "@sage/xtrem-auditing/audit_init_started_description": "Los datos de auditoría se están inicializando para {{factoryCount}} entidades.", "@sage/xtrem-auditing/audit_init_started_title": "Los datos de auditoría se están inicializando.", "@sage/xtrem-auditing/audit_init_success_description": "La auditoría se ha inicializado para {{factoryCount}} entidades. Registros de auditoría creados en {{duration}} ms: {{recordsCreated}}", "@sage/xtrem-auditing/audit_init_success_title": "La auditoría se ha inicializado.", "@sage/xtrem-auditing/data_types__audit_operation_enum__name": "Audit operation enum", "@sage/xtrem-auditing/data_types__microsecond_stamp_data_type__name": "Microsecond stamp data type", "@sage/xtrem-auditing/data_types__transaction_id_data_type__name": "Transaction ID data type", "@sage/xtrem-auditing/enums__audit_operation__DELETE": "Eliminar", "@sage/xtrem-auditing/enums__audit_operation__INSERT": "Insertar", "@sage/xtrem-auditing/enums__audit_operation__UPDATE": "Actualizar", "@sage/xtrem-auditing/nodes__sys_audit_log__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-auditing/nodes__sys_audit_log__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-auditing/nodes__sys_audit_log__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-auditing/nodes__sys_audit_log__node_name": "Traza de auditoría de sistema", "@sage/xtrem-auditing/nodes__sys_audit_log__property__loginEmail": "E-mail de inicio de sesión", "@sage/xtrem-auditing/nodes__sys_audit_log__property__newUpdateTick": "Marca de actualización reciente", "@sage/xtrem-auditing/nodes__sys_audit_log__property__nodeName": "Nombre de nodo", "@sage/xtrem-auditing/nodes__sys_audit_log__property__oldUpdateTick": "Marca de actualización anterior", "@sage/xtrem-auditing/nodes__sys_audit_log__property__operation": "Operación", "@sage/xtrem-auditing/nodes__sys_audit_log__property__recordData": "Datos de registro", "@sage/xtrem-auditing/nodes__sys_audit_log__property__recordId": "Id. de registro", "@sage/xtrem-auditing/nodes__sys_audit_log__property__rootTableName": "Nombre de tabla raíz", "@sage/xtrem-auditing/nodes__sys_audit_log__property__timestamp": "Marca de tiempo", "@sage/xtrem-auditing/nodes__sys_audit_log__property__transactionId": "Id. de transacción", "@sage/xtrem-auditing/package__name": "Auditing", "@sage/xtrem-auditing/service_options__auditing__name": "Auditoría", "@sage/xtrem-auditing/service_options__auditing_option__name": "Opción de auditoría"}