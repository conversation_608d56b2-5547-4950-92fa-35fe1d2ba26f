{"@sage/xtrem-auditing/audit_init_failed_description": "Audit-Initialisierung nach {{duration}} ms mit dem folgenden Fehlern fehlgeschlagen: {{message}}. {{recordsCreated}} Audit-Datensätze erstellt.", "@sage/xtrem-auditing/audit_init_failed_title": "Initialisierung der Audit-Daten fehlgeschlagen", "@sage/xtrem-auditing/audit_init_progress_description": "Audit-Initialisierung läuft: {{i}} von {{factoryCount}} Entitäten verarbeitet. {{recordsCreated}} Audit-Datensätze erstellt in {{duration}} ms.", "@sage/xtrem-auditing/audit_init_progress_title": "Audit-Initialisierung läuft", "@sage/xtrem-auditing/audit_init_started_description": "Initialisierung der Audit-Daten für {{factoryCount}} Entitäten", "@sage/xtrem-auditing/audit_init_started_title": "Initialisierung Audit-Daten", "@sage/xtrem-auditing/audit_init_success_description": "Audit erfolgreich initialisiert für {{factoryCount}} Entitäten. {{recordsCreated}} Audit-Datensätze erstellt in {{duration}} ms.", "@sage/xtrem-auditing/audit_init_success_title": "Audit erfolgreich initialisiert", "@sage/xtrem-auditing/data_types__audit_operation_enum__name": "Enum Audit-Vorgang", "@sage/xtrem-auditing/data_types__microsecond_stamp_data_type__name": "Datentyp Stempel Mikrosekunde", "@sage/xtrem-auditing/data_types__transaction_id_data_type__name": "Datentyp Transaktions-ID", "@sage/xtrem-auditing/enums__audit_operation__DELETE": "Löschen", "@sage/xtrem-auditing/enums__audit_operation__INSERT": "Einfügen", "@sage/xtrem-auditing/enums__audit_operation__UPDATE": "Aktualisierung", "@sage/xtrem-auditing/nodes__sys_audit_log__asyncMutation__asyncExport": "Export", "@sage/xtrem-auditing/nodes__sys_audit_log__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-auditing/nodes__sys_audit_log__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-auditing/nodes__sys_audit_log__node_name": "Protokoll Audit System", "@sage/xtrem-auditing/nodes__sys_audit_log__property__loginEmail": "Login-E-Mail", "@sage/xtrem-auditing/nodes__sys_audit_log__property__newUpdateTick": "Aktualisierung neu", "@sage/xtrem-auditing/nodes__sys_audit_log__property__nodeName": "Node-Name", "@sage/xtrem-auditing/nodes__sys_audit_log__property__oldUpdateTick": "Aktualisierung alt", "@sage/xtrem-auditing/nodes__sys_audit_log__property__operation": "Vorgang", "@sage/xtrem-auditing/nodes__sys_audit_log__property__recordData": "Datensatz-Daten", "@sage/xtrem-auditing/nodes__sys_audit_log__property__recordId": "Datensatz-ID", "@sage/xtrem-auditing/nodes__sys_audit_log__property__rootTableName": "Name Stammtabelle", "@sage/xtrem-auditing/nodes__sys_audit_log__property__timestamp": "Zeitstempel", "@sage/xtrem-auditing/nodes__sys_audit_log__property__transactionId": "Transaktions-ID", "@sage/xtrem-auditing/package__name": "Auditing", "@sage/xtrem-auditing/service_options__auditing__name": "Auditing", "@sage/xtrem-auditing/service_options__auditing_option__name": "Auditing-Option"}