{"@sage/xtrem-auditing/audit_init_failed_description": "A inicialização da auditoria falhou após {{duração}} ms com o seguinte erro: {{mensagem}}. {{recordsCreated}} registos de auditoria criados.", "@sage/xtrem-auditing/audit_init_failed_title": "Falha na inicialização dos dados de auditoria", "@sage/xtrem-auditing/audit_init_progress_description": "Inicialização de auditoria em curso: {{i}} das {{factoryCount}} entidades processadas. Registos de auditoria {{recordsCreated}} criados em {{duration}} ms.", "@sage/xtrem-auditing/audit_init_progress_title": "Inicialização de auditoria em curso", "@sage/xtrem-auditing/audit_init_started_description": "Inicialização de dados de auditoria para entidades {{factoryCount}}", "@sage/xtrem-auditing/audit_init_started_title": "Inicialização dos dados de auditoria", "@sage/xtrem-auditing/audit_init_success_description": "Auditoria inicializada com êxito para as entidades {{factoryCount}}. Registos de auditoria {{recordsCreated}} criados em {{duration}} ms.", "@sage/xtrem-auditing/audit_init_success_title": "Auditoria inicializada com sucesso", "@sage/xtrem-auditing/data_types__audit_operation_enum__name": "Auditoria de operações enum", "@sage/xtrem-auditing/data_types__microsecond_stamp_data_type__name": "Tipo de dados do timestamp de microssegundos", "@sage/xtrem-auditing/data_types__transaction_id_data_type__name": "Tipo de dados do ID da transação", "@sage/xtrem-auditing/enums__audit_operation__DELETE": "Eliminar", "@sage/xtrem-auditing/enums__audit_operation__INSERT": "Inserir", "@sage/xtrem-auditing/enums__audit_operation__UPDATE": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-auditing/nodes__sys_audit_log__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-auditing/nodes__sys_audit_log__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-auditing/nodes__sys_audit_log__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-auditing/nodes__sys_audit_log__node_name": "Registo (log) de auditoria do sistema", "@sage/xtrem-auditing/nodes__sys_audit_log__property__loginEmail": "e-mail login", "@sage/xtrem-auditing/nodes__sys_audit_log__property__newUpdateTick": "Novo visto de atualização", "@sage/xtrem-auditing/nodes__sys_audit_log__property__nodeName": "Nome do nó (node)", "@sage/xtrem-auditing/nodes__sys_audit_log__property__oldUpdateTick": "Antigo visto de atualização", "@sage/xtrem-auditing/nodes__sys_audit_log__property__operation": "Operação", "@sage/xtrem-auditing/nodes__sys_audit_log__property__recordData": "Dados de registo", "@sage/xtrem-auditing/nodes__sys_audit_log__property__recordId": "ID do registo", "@sage/xtrem-auditing/nodes__sys_audit_log__property__rootTableName": "Nome da tabela raiz (root)", "@sage/xtrem-auditing/nodes__sys_audit_log__property__timestamp": "Timestamp", "@sage/xtrem-auditing/nodes__sys_audit_log__property__transactionId": "ID da transção", "@sage/xtrem-auditing/package__name": "Auditoria", "@sage/xtrem-auditing/service_options__auditing__name": "Auditoria", "@sage/xtrem-auditing/service_options__auditing_option__name": "Opção de auditoria"}