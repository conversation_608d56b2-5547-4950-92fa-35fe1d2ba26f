{"@sage/xtrem-auditing/audit_init_failed_description": "L'initialisation d'audit a échoué après {{duration}} min.; avec l'erreur suivante : {{message}}.{{recordsCreated}} enregistrements d'audit créés.", "@sage/xtrem-auditing/audit_init_failed_title": "L'initialisation des données d'audit a échoué.", "@sage/xtrem-auditing/audit_init_progress_description": "Initialisation d'audit en cours : {{i}} entités sur {{factoryCount}} traitées, {{recordsCreated}} enregistrements d'audit créés en {{duration}} min.", "@sage/xtrem-auditing/audit_init_progress_title": "Initialisation d'audit en cours", "@sage/xtrem-auditing/audit_init_started_description": "Initialisation des données d'audit pour {{factoryCount}} entités", "@sage/xtrem-auditing/audit_init_started_title": "Initialisation des données d'audit", "@sage/xtrem-auditing/audit_init_success_description": "L'audit a bien été initialisé pour {{factoryCount}} entités, {{recordsCreated}} enregistrements d'audit créés en {{duration}} min.", "@sage/xtrem-auditing/audit_init_success_title": "Audit correctement initialisé", "@sage/xtrem-auditing/data_types__audit_operation_enum__name": "Enum d'opération d'audit", "@sage/xtrem-auditing/data_types__microsecond_stamp_data_type__name": "Type de données d'horodatage en microseconde", "@sage/xtrem-auditing/data_types__transaction_id_data_type__name": "Type de données de code de transaction", "@sage/xtrem-auditing/enums__audit_operation__DELETE": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-auditing/enums__audit_operation__INSERT": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-auditing/enums__audit_operation__UPDATE": "Mettre à jour", "@sage/xtrem-auditing/nodes__sys_audit_log__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-auditing/nodes__sys_audit_log__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-auditing/nodes__sys_audit_log__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-auditing/nodes__sys_audit_log__node_name": "Trace d'audit système", "@sage/xtrem-auditing/nodes__sys_audit_log__property__loginEmail": "E-mail de connexion", "@sage/xtrem-auditing/nodes__sys_audit_log__property__newUpdateTick": "Nouvelle coche de mise à jour", "@sage/xtrem-auditing/nodes__sys_audit_log__property__nodeName": "Nom du node", "@sage/xtrem-auditing/nodes__sys_audit_log__property__oldUpdateTick": "Ancienne coche de mise à jour", "@sage/xtrem-auditing/nodes__sys_audit_log__property__operation": "Opération", "@sage/xtrem-auditing/nodes__sys_audit_log__property__recordData": "Données d'enregistrement", "@sage/xtrem-auditing/nodes__sys_audit_log__property__recordId": "Code d'enregistrement", "@sage/xtrem-auditing/nodes__sys_audit_log__property__rootTableName": "Nom de table racine", "@sage/xtrem-auditing/nodes__sys_audit_log__property__timestamp": "Horodatage", "@sage/xtrem-auditing/nodes__sys_audit_log__property__transactionId": "Code transaction", "@sage/xtrem-auditing/package__name": "Audit", "@sage/xtrem-auditing/service_options__auditing__name": "Audit", "@sage/xtrem-auditing/service_options__auditing_option__name": "Option d'audit"}