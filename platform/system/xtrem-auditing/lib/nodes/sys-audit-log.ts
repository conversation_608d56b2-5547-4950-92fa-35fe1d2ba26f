import { AnyValue, Dict, Node, datetime, decorators, integer } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremAuditing from '../index';

/**
 * Represents a single audit log entry.
 *
 * We store the table names rate
 */
@decorators.node<SysAuditLog>({
    storage: 'sql',
    canRead: true,
    isPublished: true,
    isPlatformNode: true,
    serviceOptions: () => [xtremAuditing.serviceOptions.auditing],
    indexes: [
        {
            orderBy: { rootTableName: 1, recordId: 1, newUpdateTick: 1 },
            isUnique: true,
        },
        {
            orderBy: { rootTableName: 1, transactionId: 1, recordId: 1 },
            isUnique: true,
        },
        {
            orderBy: { loginEmail: 1, timestamp: 1, _id: 1 },
            isUnique: true,
        },
    ],
})
export class SysAuditLog extends Node {
    @decorators.stringProperty<SysAuditLog, 'nodeName'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
        lookupAccess: true,
    })
    readonly nodeName: Promise<string>;

    @decorators.stringProperty<SysAuditLog, 'rootTableName'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
        lookupAccess: true,
    })
    readonly rootTableName: Promise<string>;

    @decorators.integerProperty<SysAuditLog, 'recordId'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
    })
    readonly recordId: Promise<integer>;

    @decorators.enumProperty<SysAuditLog, 'operation'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremAuditing.enums.AuditOperationDataType,
    })
    readonly operation: Promise<xtremAuditing.enums.AuditOperation>;

    @decorators.stringProperty<SysAuditLog, 'loginEmail'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.email,
        lookupAccess: true,
    })
    readonly loginEmail: Promise<string>;

    @decorators.datetimeProperty<SysAuditLog, 'timestamp'>({
        isStored: true,
        isPublished: true,
    })
    readonly timestamp: Promise<datetime>;

    @decorators.stringProperty<SysAuditLog, 'transactionId'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremAuditing.dataTypes.transactionIdDataType,
    })
    readonly transactionId: Promise<string>;

    @decorators.integerProperty<SysAuditLog, 'oldUpdateTick'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly oldUpdateTick: Promise<integer | null>;

    @decorators.integerProperty<SysAuditLog, 'newUpdateTick'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        allowedInUniqueIndex: true,
    })
    readonly newUpdateTick: Promise<integer | null>;

    @decorators.jsonProperty<SysAuditLog, 'recordData'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly recordData: Promise<Dict<AnyValue> | null>;
}
