import { Node, decorators } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { Test<PERSON>erson } from './test-person';

@decorators.node<TestPet>({
    storage: 'sql',
    isPublished: true,
    isAbstract: true,
    isVitalCollectionChild: true,
    notifies: ['created', 'updated', 'deleted'],
})
export class TestPet extends Node {
    @decorators.stringProperty<TestPet, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<TestPet, 'kind'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly kind: Promise<string>;

    @decorators.referenceProperty<TestPet, 'owner'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => Test<PERSON>erson,
    })
    readonly owner: Promise<TestPerson>;
}
