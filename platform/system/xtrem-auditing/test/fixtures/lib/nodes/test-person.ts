import { Collection, Node, decorators, integer } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { TestPet } from './test-pet';

@decorators.node<TestPerson>({
    storage: 'sql',
    isPublished: true,
    canDelete: true,
    notifies: ['created', 'updated', 'deleted'],
})
export class Test<PERSON>erson extends Node {
    @decorators.stringProperty<TestPerson, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly name: Promise<string>;

    @decorators.integerProperty<TestPerson, 'age'>({
        isStored: true,
        isPublished: true,
    })
    readonly age: Promise<integer>;

    @decorators.collectionProperty<TestPerson, 'pets'>({
        isVital: true,
        isPublished: true,
        node: () => TestPet,
        reverseReference: 'owner',
    })
    readonly pets: Collection<TestPet>;
}
