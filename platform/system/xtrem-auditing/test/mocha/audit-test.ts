import { asyncArray, Context, Node, NodeCreateData, Test } from '@sage/xtrem-core';
import { Datetime } from '@sage/xtrem-date-time';
import { assert } from 'chai';
import * as _ from 'lodash';
import * as xtremAuditing from '../../lib/index';
import { SysAuditLog } from '../../lib/nodes';
import * as xtremAuditingTest from '../fixtures/test-application';

function snakeCase(str: string): string {
    const snake = _.snakeCase(str);
    return str[0] === '_' ? `_${snake}` : snake;
}

// Truncate timestamp precision to milliseconds and change timezone format to 'Z'.
function fixValue(value: any): string {
    if (typeof value === 'string' && (value.endsWith('+00:00') || value.endsWith('+00'))) {
        let str = value.replace(' ', 'T').split('+')[0];
        if (str.length === 19) str += '.';
        return `${str.padEnd(23, '0')}Z`;
    }
    return value;
}

async function getSqlNow(context: Context): Promise<string> {
    return (await context.getSqlTimestamp()).toString();
}

async function getExpectedRecordData(context: Context, nodeConstructor: typeof Node, recordId: number): Promise<any> {
    const node = await context.tryRead(nodeConstructor, { _id: recordId });
    if (!node) return null;
    const factory = node.$.factory;
    const propertyNames = factory.properties.filter(p => p.isStored).map(p => p.name);
    const columnNames = propertyNames.map(snakeCase);
    const columnValues = await asyncArray(propertyNames)
        .map(async name => {
            const value = (await node.$.getValue(name)) as any;
            if (value == null) return null;
            if (value._id) return value._id;
            if (Datetime.isDatetime(value)) return value.toString();
            return value;
        })
        .toArray();
    const expectedRecordData = _.zipObject(columnNames, columnValues);
    expectedRecordData._tenant_id = node.$.context.tenantId;
    if (factory.baseFactory) expectedRecordData._constructor = factory.name;
    return expectedRecordData;
}

export async function mapAuditLog(log: SysAuditLog): Promise<any> {
    const payload = { ...(await log.$.payload({ withIds: true })) } as any;
    payload.recordData = payload.recordData && _.mapValues(payload.recordData, fixValue);
    payload.timestamp = payload.timestamp.toString();
    return payload;
}

export function getExpectedAuditLog({
    operation,
    nodeName,
    rootTableName,
    recordId,
    timestamp,
    recordData,
    transactionId,
    oldUpdateTick,
    newUpdateTick,
}: {
    operation: 'INSERT' | 'UPDATE' | 'DELETE';
    nodeName: string;
    rootTableName: string;
    recordId: number;
    timestamp: string;
    recordData: any;
    transactionId: string;
    oldUpdateTick: number | null;
    newUpdateTick: number | null;
}): any {
    return {
        _sourceId: '',
        operation,
        recordData,
        recordId,
        rootTableName,
        nodeName,
        transactionId,
        timestamp,
        oldUpdateTick,
        newUpdateTick,
        loginEmail: '<EMAIL>',
    };
}

export async function checkExpectedAuditLog(
    nodeConstructor: typeof Node,
    recordId: number,
    expected: {
        length: number;
        operation: 'INSERT' | 'UPDATE' | 'DELETE';
        timestamp: string;
        transactionId: string;
        oldUpdateTick: number | null;
        newUpdateTick: number | null;
    },
): Promise<void> {
    await Test.withReadonlyContext(async context => {
        const expectedData = await getExpectedRecordData(context, nodeConstructor, recordId);
        const rootTableName = snakeCase(context.application.getFactoryByConstructor(nodeConstructor).rootFactory.name);

        const expectedAuditLog = getExpectedAuditLog({
            nodeName: nodeConstructor.name,
            rootTableName,
            recordId,
            ...expected,
            recordData: expectedData,
        });

        const actualAuditLogs = await context.query(xtremAuditing.nodes.SysAuditLog, {}).map(mapAuditLog).toArray();
        assert.equal(actualAuditLogs.length, expected.length);
        assert.deepEqual(_.omit(actualAuditLogs[expected.length - 1], '_id'), expectedAuditLog);
    });
}

export function checkExpectedAuditLogs(
    afterId: number,
    entries: {
        nodeConstructor: typeof Node;
        recordId: number;
        operation: 'INSERT' | 'UPDATE' | 'DELETE';
        timestamp: string;
        transactionId: string;
        oldUpdateTick: number | null;
        newUpdateTick: number | null;
    }[],
): Promise<number> {
    return Test.withReadonlyContext(async context => {
        const expectedAuditLogs = await asyncArray(entries)
            .map(async entry => {
                const expectedData = await getExpectedRecordData(context, entry.nodeConstructor, entry.recordId);
                const rootTableName = snakeCase(
                    context.application.getFactoryByConstructor(entry.nodeConstructor).rootFactory.name,
                );

                return getExpectedAuditLog({
                    nodeName: entry.nodeConstructor.name,
                    rootTableName,
                    ..._.omit(entry, 'nodeConstructor'),
                    recordData: expectedData,
                });
            })
            .toArray();

        const actualAuditLogs = await context
            .query(xtremAuditing.nodes.SysAuditLog, { filter: { _id: { _gt: afterId } } })
            .map(mapAuditLog)
            .toArray();

        assert.deepEqual(
            actualAuditLogs.map(log => _.omit(log, '_id')),
            expectedAuditLogs,
        );
        return actualAuditLogs.length > 0 ? actualAuditLogs[actualAuditLogs.length - 1]._id : afterId;
    });
}

async function resetData(): Promise<void> {
    await Test.withCommittedContext(async context => {
        await context.bulkDeleteSql(xtremAuditingTest.nodes.TestPerson, {});
        await context.bulkDeleteSql(xtremAuditing.nodes.SysAuditLog, {});
    });
}

describe('Audit triggers', () => {
    beforeEach(resetData);

    it('can audit basic create -> update -> delete', async () => {
        // Create a person node instance
        const {
            personId,
            transactionId: insertTransactionId,
            timestamp: insertTimestamp,
        } = await Test.withCommittedContext(async context => {
            const person = await context.create(xtremAuditingTest.nodes.TestPerson, {
                name: 'John',
                age: 30,
            });
            await person.$.save();
            const timestamp = (await person._updateStamp).toString();
            return { personId: person._id, transactionId: String(await context.transaction.id), timestamp };
        });

        // Check the audit log
        await checkExpectedAuditLog(xtremAuditingTest.nodes.TestPerson, personId, {
            length: 1,
            operation: 'INSERT',
            timestamp: insertTimestamp,
            transactionId: insertTransactionId,
            oldUpdateTick: null,
            newUpdateTick: 1,
        });

        // Update the person node instance
        const { transactionId: updateTransactionId, timestamp: updateTimestamp } = await Test.withCommittedContext(
            async context => {
                const person = await context.read(
                    xtremAuditingTest.nodes.TestPerson,
                    { _id: personId },
                    { forUpdate: true },
                );
                await person.$.set({ age: 31 });
                await person.$.save();
                return {
                    transactionId: String(await context.transaction.id),
                    timestamp: (await person._updateStamp).toString(),
                };
            },
        );

        await checkExpectedAuditLog(xtremAuditingTest.nodes.TestPerson, personId, {
            length: 2,
            operation: 'UPDATE',
            timestamp: updateTimestamp,
            transactionId: updateTransactionId,
            oldUpdateTick: 1,
            newUpdateTick: 2,
        });

        // Delete the person node instance
        const { transactionId: deleteTransactionId, timestamp: deleteTimestamp } = await Test.withCommittedContext(
            async context => {
                await context.delete(xtremAuditingTest.nodes.TestPerson, { _id: personId });
                const timestamp = await getSqlNow(context);
                return { transactionId: String(await context.transaction.id), timestamp };
            },
        );

        await checkExpectedAuditLog(xtremAuditingTest.nodes.TestPerson, personId, {
            length: 3,
            operation: 'DELETE',
            timestamp: deleteTimestamp,
            transactionId: deleteTransactionId,
            oldUpdateTick: 2,
            newUpdateTick: null,
        });
    });

    it('can audit create -> update -> delete with vital children and inheritance', async () => {
        let lastLogId = 0;

        // Create a person node instance
        const {
            personId,
            catId,
            dogId,
            transactionId: insertTransactionId,
            timestamp: insertTimestamp,
        } = await Test.withCommittedContext(async context => {
            const person = await context.create(xtremAuditingTest.nodes.TestPerson, {
                name: 'John',
                age: 30,
                pets: [
                    {
                        _constructor: 'TestCat',
                        name: 'Fluffy',
                        catchesMice: true,
                    } as NodeCreateData<xtremAuditingTest.nodes.TestCat>,
                    {
                        _constructor: 'TestDog',
                        name: 'Snoopy',
                        isNiceDog: true,
                    } as NodeCreateData<xtremAuditingTest.nodes.TestDog>,
                ],
            });
            await person.$.save();
            const timestamp = (await person._updateStamp).toString();
            return {
                personId: person._id,
                catId: (await person.pets.elementAt(0))._id,
                dogId: (await person.pets.elementAt(1))._id,
                transactionId: String(await context.transaction.id),
                timestamp,
            };
        });

        // Check the audit log
        lastLogId = await checkExpectedAuditLogs(lastLogId, [
            {
                nodeConstructor: xtremAuditingTest.nodes.TestPerson,
                recordId: personId,
                operation: 'INSERT',
                timestamp: insertTimestamp,
                transactionId: insertTransactionId,
                oldUpdateTick: null,
                newUpdateTick: 1,
            },
            {
                nodeConstructor: xtremAuditingTest.nodes.TestCat,
                recordId: catId,
                operation: 'INSERT',
                timestamp: insertTimestamp,
                transactionId: insertTransactionId,
                oldUpdateTick: null,
                newUpdateTick: 1,
            },
            {
                nodeConstructor: xtremAuditingTest.nodes.TestDog,
                recordId: dogId,
                operation: 'INSERT',
                timestamp: insertTimestamp,
                transactionId: insertTransactionId,
                oldUpdateTick: null,
                newUpdateTick: 1,
            },
        ]);

        const {
            newDogId,
            transactionId: updateTransactionId,
            timestamp: updateTimestamp,
        } = await Test.withCommittedContext(async context => {
            const person = await context.read(
                xtremAuditingTest.nodes.TestPerson,
                { _id: personId },
                { forUpdate: true },
            );
            await person.$.set({
                name: 'John Doe',
                pets: [
                    {
                        _action: 'update',
                        _id: catId,
                        catchesMice: false,
                    } as NodeCreateData<xtremAuditingTest.nodes.TestCat>,
                    {
                        _action: 'create',
                        _constructor: 'TestDog',
                        name: 'Pluto',
                        isNiceDog: false,
                    } as NodeCreateData<xtremAuditingTest.nodes.TestDog>,
                ],
            });
            await person.$.save();

            const timestamp = (await person._updateStamp).toString();
            return {
                newDogId: (await person.pets.elementAt(2))._id,
                transactionId: String(await context.transaction.id),
                timestamp,
            };
        });

        // Check the audit log
        lastLogId = await checkExpectedAuditLogs(lastLogId, [
            {
                nodeConstructor: xtremAuditingTest.nodes.TestPerson,
                recordId: personId,
                operation: 'UPDATE',
                timestamp: updateTimestamp,
                transactionId: updateTransactionId,
                oldUpdateTick: 1,
                newUpdateTick: 2,
            },
            {
                nodeConstructor: xtremAuditingTest.nodes.TestCat,
                recordId: catId,
                operation: 'UPDATE',
                timestamp: updateTimestamp,
                transactionId: updateTransactionId,
                oldUpdateTick: 1,
                newUpdateTick: 2,
            },
            {
                nodeConstructor: xtremAuditingTest.nodes.TestDog,
                recordId: newDogId,
                operation: 'INSERT',
                timestamp: updateTimestamp,
                transactionId: updateTransactionId,
                oldUpdateTick: null,
                newUpdateTick: 1,
            },
        ]);

        const { transactionId: deleteTransactionId, timestamp: deleteTimestamp } = await Test.withCommittedContext(
            async context => {
                await context.delete(xtremAuditingTest.nodes.TestPerson, { _id: personId });
                const timestamp = await getSqlNow(context);
                return { transactionId: String(await context.transaction.id), timestamp };
            },
        );

        // Check the audit log
        await checkExpectedAuditLogs(lastLogId, [
            {
                nodeConstructor: xtremAuditingTest.nodes.TestPerson,
                recordId: personId,
                operation: 'DELETE',
                timestamp: deleteTimestamp,
                transactionId: deleteTransactionId,
                oldUpdateTick: 2,
                newUpdateTick: null,
            },
            {
                nodeConstructor: xtremAuditingTest.nodes.TestCat,
                recordId: catId,
                operation: 'DELETE',
                timestamp: deleteTimestamp,
                transactionId: deleteTransactionId,
                oldUpdateTick: 2,
                newUpdateTick: null,
            },
            {
                nodeConstructor: xtremAuditingTest.nodes.TestDog,
                recordId: dogId,
                operation: 'DELETE',
                timestamp: deleteTimestamp,
                transactionId: deleteTransactionId,
                oldUpdateTick: 1,
                newUpdateTick: null,
            },
            {
                nodeConstructor: xtremAuditingTest.nodes.TestDog,
                recordId: newDogId,
                operation: 'DELETE',
                timestamp: deleteTimestamp,
                transactionId: deleteTransactionId,
                oldUpdateTick: 1,
                newUpdateTick: null,
            },
        ]);
    });
});
