declare module '@sage/xtrem-auditing-api-partial' {
    import type { Package as SageXtremAuthorization$Package } from '@sage/xtrem-authorization-api';
    import type { Package as SageXtremCommunication$Package } from '@sage/xtrem-communication-api';
    import type { Package as SageXtremSystem$Package, User } from '@sage/xtrem-system-api';
    import type {
        AggregateQueryOperation,
        AggregateReadOperation,
        AsyncOperation,
        ClientNode,
        ClientNodeInput,
        GetDefaultsOperation,
        QueryOperation,
        ReadOperation,
        integer,
    } from '@sage/xtrem-client';
    export interface AuditOperation$Enum {
        INSERT: 1;
        UPDATE: 2;
        DELETE: 3;
    }
    export type AuditOperation = keyof AuditOperation$Enum;
    export interface SysAuditLog extends ClientNode {
        _updateUser: User;
        _createUser: User;
        nodeName: string;
        rootTableName: string;
        recordId: integer;
        operation: AuditOperation;
        loginEmail: string;
        timestamp: string;
        transactionId: string;
        oldUpdateTick: integer;
        newUpdateTick: integer;
        recordData: string;
    }
    export interface SysAuditLogInput extends ClientNodeInput {
        nodeName?: string;
        rootTableName?: string;
        recordId?: integer | string;
        operation?: AuditOperation;
        loginEmail?: string;
        timestamp?: string;
        transactionId?: string;
        oldUpdateTick?: integer | string;
        newUpdateTick?: integer | string;
        recordData?: string;
    }
    export interface SysAuditLogBinding extends ClientNode {
        _updateUser: User;
        _createUser: User;
        nodeName: string;
        rootTableName: string;
        recordId: integer;
        operation: AuditOperation;
        loginEmail: string;
        timestamp: string;
        transactionId: string;
        oldUpdateTick: integer;
        newUpdateTick: integer;
        recordData: any;
    }
    export interface SysAuditLog$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SysAuditLog$Operations {
        query: QueryOperation<SysAuditLog>;
        read: ReadOperation<SysAuditLog>;
        aggregate: {
            read: AggregateReadOperation<SysAuditLog>;
            query: AggregateQueryOperation<SysAuditLog>;
        };
        asyncOperations: SysAuditLog$AsyncOperations;
        getDefaults: GetDefaultsOperation<SysAuditLog>;
    }
    export interface Package {
        '@sage/xtrem-auditing/SysAuditLog': SysAuditLog$Operations;
    }
    export interface GraphApi
        extends Package,
            SageXtremAuthorization$Package,
            SageXtremCommunication$Package,
            SageXtremSystem$Package {}
}
declare module '@sage/xtrem-auditing-api' {
    export type * from '@sage/xtrem-auditing-api-partial';
}
declare module '@sage/xtrem-authorization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-auditing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-communication-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-auditing-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-auditing-api';
    export interface GraphApi extends GraphApiExtension {}
}
