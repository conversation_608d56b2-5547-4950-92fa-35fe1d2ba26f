PATH: XTREEM/Applicative+development/Xtrem+Communication+Package

# xtrem-communication

> communication

```ts
// ==================================================================================================
//                                    Architecture – Sending side
// ==================================================================================================
// +-------------------------------------------------------+                   +--------------------+
// |                    XTreeM Cloud                       |                   |                    |
// |                                                       |                   |  +--------------+  |
// |  +-----------------+  send message (immediate option) |                   |  |              |  |
// |  |                 | ------------------------------------------------------> |   External   |  |
// |  |                 |                                  |                   |  |   Message    |  |
// |  |     XTreeM      |         +---------------------+  |  send message     |  |   Queues     |  |
// |  |     Service     |         |                     | ----------------------> |              |  |
// |  |                 |         |       Routing       |  |                   |  |              |  |
// |  |                 |         |       Service       |  |                   |  +--------------+  |
// |  |                 |         |                     |  | send notification |  +--------------+  |
// |  |                 |         |                     | ----------------------> |      |  |
// |  +-----------------+         +---------------------+  |                   |  | Notification |  |
// |           |                      ^         ^          |                   |  |   Queues     |  |
// |   enqueue |             dequeue  |         | read     |                   |  +--------------+  |
// |           |                      |         |          |                   |                    |
// |  +--------|----------------------|---------|-------+  |                   |                    |
// |  |        v                      |         v       |  |                   |        SQS         |
// |  | +--------------------------------+ +---------+  |  |                   |                    |
// |  | | Notification and message tables| | Routing |  |  |                   +--------------------+
// |  | +--------------------------------+ |  Table  |  |  |
// |  |                                    +---------+  |  |
// |  |                   PostgreSQL                    |  |
// |  +-------------------------------------------------+  |
// +-------------------------------------------------------+
//
//  Credits:
//  - Bruno for the architecture
//  - [textik](https://textik.com/) for the diagram generation tool
```

## Data sets

# master-data

-
-

# setup

-

## Nodes list

-
-
-

## Functions list
