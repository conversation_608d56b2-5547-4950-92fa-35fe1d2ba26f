declare module '@sage/xtrem-communication-api-partial' {
    import type { Package as SageXtremSystem$Package, User } from '@sage/xtrem-system-api';
    import type {
        AggregateQueryOperation,
        AggregateReadOperation,
        AsyncOperation,
        ClientCollection,
        ClientNode,
        ClientNodeInput,
        CreateOperation,
        DeleteOperation,
        GetDefaultsOperation,
        GetDuplicateOperation,
        Operation as Node$Operation,
        QueryOperation,
        ReadOperation,
        TextStream,
        UpdateByIdOperation,
        UpdateOperation,
        VitalClientNode,
        VitalClientNodeInput,
        integer,
    } from '@sage/xtrem-client';
    export interface CommunicationState$Enum {
        notSent: 0;
        sent: 1;
        received: 2;
        success: 3;
        retry: 4;
        error: 5;
        stopRequested: 6;
        stopped: 7;
        interruptRequested: 8;
        interrupted: 9;
    }
    export type CommunicationState = keyof CommunicationState$Enum;
    export interface IntegrationState$Enum {
        not: 0;
        pending: 1;
        success: 2;
        error: 3;
        desynchronized: 4;
    }
    export type IntegrationState = keyof IntegrationState$Enum;
    export interface LogLevel$Enum {
        test: 0;
        info: 1;
        warning: 2;
        result: 3;
        error: 4;
        exception: 5;
    }
    export type LogLevel = keyof LogLevel$Enum;
    export interface NotificationStatus$Enum {
        pending: 0;
        running: 1;
        success: 2;
        error: 3;
        stopRequested: 4;
        stopped: 5;
        notResponding: 6;
        interruptRequested: 7;
        interrupted: 8;
    }
    export type NotificationStatus = keyof NotificationStatus$Enum;
    export interface SqsStatus$Enum {
        pending: 0;
        fail: 1;
    }
    export type SqsStatus = keyof SqsStatus$Enum;
    export interface SysMessage extends ClientNode {}
    export interface SysMessageInput extends ClientNodeInput {}
    export interface SysMessageBinding extends ClientNode {}
    export interface SysMessage$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SysMessage$Operations {
        query: QueryOperation<SysMessage>;
        read: ReadOperation<SysMessage>;
        aggregate: {
            read: AggregateReadOperation<SysMessage>;
            query: AggregateQueryOperation<SysMessage>;
        };
        update: UpdateOperation<SysMessageInput, SysMessage>;
        updateById: UpdateByIdOperation<SysMessageInput, SysMessage>;
        asyncOperations: SysMessage$AsyncOperations;
        getDefaults: GetDefaultsOperation<SysMessage>;
    }
    export interface SysMessageHistory extends ClientNode {
        _updateUser: User;
        _createUser: User;
        id: string;
        integrationSolution: string;
        status: CommunicationState;
        context: string;
        sentRequest: TextStream;
        receivedRequest: TextStream;
        attributes: string;
        user: string;
        sendStamp: string;
        receivedStamp: string;
        communicationDiagnoses: string;
        errorMessage: string;
    }
    export interface SysMessageHistoryInput extends ClientNodeInput {
        id?: string;
        integrationSolution?: string;
        status?: CommunicationState;
        context?: string;
        sentRequest?: TextStream;
        receivedRequest?: TextStream;
        attributes?: string;
        sendStamp?: string;
        receivedStamp?: string;
        communicationDiagnoses?: string;
        errorMessage?: string;
    }
    export interface SysMessageHistoryBinding extends ClientNode {
        _updateUser: User;
        _createUser: User;
        id: string;
        integrationSolution: string;
        status: CommunicationState;
        context: any;
        sentRequest: TextStream;
        receivedRequest: TextStream;
        attributes: any;
        user: any;
        sendStamp: string;
        receivedStamp: string;
        communicationDiagnoses: any;
        errorMessage: string;
    }
    export interface SysMessageHistory$Mutations {
        purge: Node$Operation<
            {
                integrationSolution: string;
            },
            boolean
        >;
    }
    export interface SysMessageHistory$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SysMessageHistory$Operations {
        query: QueryOperation<SysMessageHistory>;
        read: ReadOperation<SysMessageHistory>;
        aggregate: {
            read: AggregateReadOperation<SysMessageHistory>;
            query: AggregateQueryOperation<SysMessageHistory>;
        };
        update: UpdateOperation<SysMessageHistoryInput, SysMessageHistory>;
        updateById: UpdateByIdOperation<SysMessageHistoryInput, SysMessageHistory>;
        mutations: SysMessageHistory$Mutations;
        asyncOperations: SysMessageHistory$AsyncOperations;
        getDefaults: GetDefaultsOperation<SysMessageHistory>;
    }
    export interface SysNotification extends ClientNode {}
    export interface SysNotificationInput extends ClientNodeInput {}
    export interface SysNotificationBinding extends ClientNode {}
    export interface SysNotification$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SysNotification$Operations {
        query: QueryOperation<SysNotification>;
        read: ReadOperation<SysNotification>;
        aggregate: {
            read: AggregateReadOperation<SysNotification>;
            query: AggregateQueryOperation<SysNotification>;
        };
        update: UpdateOperation<SysNotificationInput, SysNotification>;
        updateById: UpdateByIdOperation<SysNotificationInput, SysNotification>;
        asyncOperations: SysNotification$AsyncOperations;
        getDefaults: GetDefaultsOperation<SysNotification>;
    }
    export interface SysNotificationHistory extends ClientNode {
        _updateUser: User;
        _createUser: User;
        errorMessage: string;
        status: CommunicationState;
        dateLogged: string;
        notificationContext: string;
    }
    export interface SysNotificationHistoryInput extends ClientNodeInput {
        errorMessage?: string;
        status?: CommunicationState;
        dateLogged?: string;
        notificationContext?: string;
    }
    export interface SysNotificationHistoryBinding extends ClientNode {
        _updateUser: User;
        _createUser: User;
        errorMessage: string;
        status: CommunicationState;
        dateLogged: string;
        notificationContext: any;
    }
    export interface SysNotificationHistory$Queries {
        all: Node$Operation<
            {},
            {
                notificationId: string;
                originId: string;
                listener: string;
                topic: string;
                replyId: string;
                replyTopic: string;
                locale: string;
                envelope: TextStream;
                errorStack: TextStream;
                errorMessage: string;
                status: string;
                dateLogged: string;
            }[]
        >;
    }
    export interface SysNotificationHistory$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SysNotificationHistory$Operations {
        query: QueryOperation<SysNotificationHistory>;
        read: ReadOperation<SysNotificationHistory>;
        aggregate: {
            read: AggregateReadOperation<SysNotificationHistory>;
            query: AggregateQueryOperation<SysNotificationHistory>;
        };
        queries: SysNotificationHistory$Queries;
        create: CreateOperation<SysNotificationHistoryInput, SysNotificationHistory>;
        getDuplicate: GetDuplicateOperation<SysNotificationHistory>;
        update: UpdateOperation<SysNotificationHistoryInput, SysNotificationHistory>;
        updateById: UpdateByIdOperation<SysNotificationHistoryInput, SysNotificationHistory>;
        asyncOperations: SysNotificationHistory$AsyncOperations;
        getDefaults: GetDefaultsOperation<SysNotificationHistory>;
    }
    export interface SysNotificationLogEntry extends VitalClientNode {
        _updateUser: User;
        _createUser: User;
        sysNotificationState: SysNotificationState;
        level: LogLevel;
        message: string;
        timestamp: string;
        data: string;
        spanContext: string;
    }
    export interface SysNotificationLogEntryInput extends VitalClientNodeInput {
        level?: LogLevel;
        message?: string;
        timestamp?: string;
        data?: string;
        spanContext?: string;
    }
    export interface SysNotificationLogEntryBinding extends VitalClientNode {
        _updateUser: User;
        _createUser: User;
        sysNotificationState: SysNotificationState;
        level: LogLevel;
        message: string;
        timestamp: string;
        data: any;
        spanContext: any;
    }
    export interface SysNotificationLogEntry$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SysNotificationLogEntry$Operations {
        query: QueryOperation<SysNotificationLogEntry>;
        read: ReadOperation<SysNotificationLogEntry>;
        aggregate: {
            read: AggregateReadOperation<SysNotificationLogEntry>;
            query: AggregateQueryOperation<SysNotificationLogEntry>;
        };
        update: UpdateOperation<SysNotificationLogEntryInput, SysNotificationLogEntry>;
        updateById: UpdateByIdOperation<SysNotificationLogEntryInput, SysNotificationLogEntry>;
        asyncOperations: SysNotificationLogEntry$AsyncOperations;
        getDefaults: GetDefaultsOperation<SysNotificationLogEntry>;
    }
    export interface SysNotificationState extends ClientNode {
        _updateUser: User;
        _createUser: User;
        notificationId: string;
        isRead: boolean;
        originId: string;
        operationName: string;
        parameterValues: string;
        topic: string;
        replyId: string;
        locale: string;
        status: NotificationStatus;
        result: string;
        message: string;
        progress: string;
        progressBarPercent: string;
        timeStarted: string;
        timeEnded: string;
        notificationContext: string;
        user: User;
        isUserNotificationRequested: boolean;
        logs: ClientCollection<SysNotificationLogEntry>;
    }
    export interface SysNotificationStateInput extends ClientNodeInput {
        notificationId?: string;
        isRead?: boolean | string;
        originId?: string;
        operationName?: string;
        parameterValues?: string;
        topic?: string;
        replyId?: string;
        locale?: string;
        status?: NotificationStatus;
        result?: string;
        message?: string;
        progress?: string;
        timeStarted?: string;
        timeEnded?: string;
        notificationContext?: string;
        user?: integer | string;
        isUserNotificationRequested?: boolean | string;
        logs?: Partial<SysNotificationLogEntryInput>[];
    }
    export interface SysNotificationStateBinding extends ClientNode {
        _updateUser: User;
        _createUser: User;
        notificationId: string;
        isRead: boolean;
        originId: string;
        operationName: string;
        parameterValues: any;
        topic: string;
        replyId: string;
        locale: string;
        status: NotificationStatus;
        result: any;
        message: string;
        progress: any;
        progressBarPercent: string;
        timeStarted: string;
        timeEnded: string;
        notificationContext: any;
        user: User;
        isUserNotificationRequested: boolean;
        logs: ClientCollection<SysNotificationLogEntryBinding>;
    }
    export interface SysNotificationState$AsyncOperations {
        bulkDelete: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SysNotificationState$Lookups {
        user: QueryOperation<User>;
    }
    export interface SysNotificationState$Operations {
        query: QueryOperation<SysNotificationState>;
        read: ReadOperation<SysNotificationState>;
        aggregate: {
            read: AggregateReadOperation<SysNotificationState>;
            query: AggregateQueryOperation<SysNotificationState>;
        };
        create: CreateOperation<SysNotificationStateInput, SysNotificationState>;
        getDuplicate: GetDuplicateOperation<SysNotificationState>;
        update: UpdateOperation<SysNotificationStateInput, SysNotificationState>;
        updateById: UpdateByIdOperation<SysNotificationStateInput, SysNotificationState>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: SysNotificationState$AsyncOperations;
        lookups(dataOrId: string | { data: SysNotificationStateInput }): SysNotificationState$Lookups;
        getDefaults: GetDefaultsOperation<SysNotificationState>;
    }
    export interface Package {
        '@sage/xtrem-communication/SysMessage': SysMessage$Operations;
        '@sage/xtrem-communication/SysMessageHistory': SysMessageHistory$Operations;
        '@sage/xtrem-communication/SysNotification': SysNotification$Operations;
        '@sage/xtrem-communication/SysNotificationHistory': SysNotificationHistory$Operations;
        '@sage/xtrem-communication/SysNotificationLogEntry': SysNotificationLogEntry$Operations;
        '@sage/xtrem-communication/SysNotificationState': SysNotificationState$Operations;
    }
    export interface GraphApi extends Package, SageXtremSystem$Package {}
}
declare module '@sage/xtrem-communication-api' {
    export type * from '@sage/xtrem-communication-api-partial';
}
declare module '@sage/xtrem-system-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-communication-api';
    export interface GraphApi extends GraphApiExtension {}
}
