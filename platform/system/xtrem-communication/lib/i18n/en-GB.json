{"@sage/xtrem-communication/activity__sys_notification_history__name": "System notification history", "@sage/xtrem-communication/activity__sys_notification_state__name": "System notification state", "@sage/xtrem-communication/completed_async_mutation_description": "Completed: {{node}} / {{mutation}}", "@sage/xtrem-communication/completed_async_mutation_title": "Async mutation completed.", "@sage/xtrem-communication/data_types__communication_notification_data_data_type__name": "Communication notification data data type", "@sage/xtrem-communication/data_types__communication_state_enum__name": "Communication state enum", "@sage/xtrem-communication/data_types__error_message_data_type__name": "Error message data type", "@sage/xtrem-communication/data_types__error_stack_data_type__name": "Error stack data type", "@sage/xtrem-communication/data_types__integration_state_enum__name": "Integration state enum", "@sage/xtrem-communication/data_types__listener_name_data_type__name": "Listener name data type", "@sage/xtrem-communication/data_types__log_level_enum__name": "Log level enum", "@sage/xtrem-communication/data_types__log_message__name": "Log message", "@sage/xtrem-communication/data_types__message_data_data_type__name": "Message data data type", "@sage/xtrem-communication/data_types__message_data_type__name": "Message data type", "@sage/xtrem-communication/data_types__notification_status_enum__name": "Notification status enum", "@sage/xtrem-communication/data_types__origin_id_data_type__name": "Origin ID data type", "@sage/xtrem-communication/data_types__request_data_data_type__name": "Request data data type", "@sage/xtrem-communication/data_types__sqs_status_enum__name": "SQS status enum", "@sage/xtrem-communication/enums__communication_state__error": "Error", "@sage/xtrem-communication/enums__communication_state__interrupted": "Interrupted", "@sage/xtrem-communication/enums__communication_state__interruptRequested": "Interrupt requested", "@sage/xtrem-communication/enums__communication_state__notSent": "Not sent", "@sage/xtrem-communication/enums__communication_state__received": "Received", "@sage/xtrem-communication/enums__communication_state__retry": "Retry", "@sage/xtrem-communication/enums__communication_state__sent": "<PERSON><PERSON>", "@sage/xtrem-communication/enums__communication_state__stopped": "Stopped", "@sage/xtrem-communication/enums__communication_state__stopRequested": "Stop requested", "@sage/xtrem-communication/enums__communication_state__success": "Success", "@sage/xtrem-communication/enums__integration_state__desynchronized": "Desynchronised", "@sage/xtrem-communication/enums__integration_state__error": "Error", "@sage/xtrem-communication/enums__integration_state__not": "Not integrated", "@sage/xtrem-communication/enums__integration_state__pending": "Pending", "@sage/xtrem-communication/enums__integration_state__success": "Success", "@sage/xtrem-communication/enums__log_level__error": "Error", "@sage/xtrem-communication/enums__log_level__exception": "Exception", "@sage/xtrem-communication/enums__log_level__info": "Info", "@sage/xtrem-communication/enums__log_level__result": "Result", "@sage/xtrem-communication/enums__log_level__test": "Test", "@sage/xtrem-communication/enums__log_level__warning": "Warning", "@sage/xtrem-communication/enums__notification_status__error": "Error", "@sage/xtrem-communication/enums__notification_status__interrupted": "Interrupted", "@sage/xtrem-communication/enums__notification_status__interruptRequested": "Interrupt requested", "@sage/xtrem-communication/enums__notification_status__notResponding": "Not responding", "@sage/xtrem-communication/enums__notification_status__pending": "Pending", "@sage/xtrem-communication/enums__notification_status__running": "Running", "@sage/xtrem-communication/enums__notification_status__stopped": "Stopped", "@sage/xtrem-communication/enums__notification_status__stopRequested": "Stop requested", "@sage/xtrem-communication/enums__notification_status__success": "Success", "@sage/xtrem-communication/enums__sqs_status__fail": "Error", "@sage/xtrem-communication/enums__sqs_status__pending": "Pending", "@sage/xtrem-communication/interrupt_requested": "Interrupt requested", "@sage/xtrem-communication/menu_item__communication": "Communication", "@sage/xtrem-communication/menu_item__scheduler": "Scheduler", "@sage/xtrem-communication/nodes__sys_dynamic_listener__node_name": "System dynamic listener", "@sage/xtrem-communication/nodes__sys_message__asyncMutation__asyncExport": "Export", "@sage/xtrem-communication/nodes__sys_message__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-communication/nodes__sys_message__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-communication/nodes__sys_message__node_name": "System message", "@sage/xtrem-communication/nodes__sys_message__property__attributes": "Attributes", "@sage/xtrem-communication/nodes__sys_message__property__messageId": "Message ID", "@sage/xtrem-communication/nodes__sys_message__property__payload": "Payload", "@sage/xtrem-communication/nodes__sys_message__property__queue": "Queue", "@sage/xtrem-communication/nodes__sys_message__property__status": "Status", "@sage/xtrem-communication/nodes__sys_message__property__tenantId": "Tenant ID", "@sage/xtrem-communication/nodes__sys_message_history__asyncMutation__asyncExport": "Export", "@sage/xtrem-communication/nodes__sys_message_history__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-communication/nodes__sys_message_history__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-communication/nodes__sys_message_history__mutation__purge": "Purge", "@sage/xtrem-communication/nodes__sys_message_history__mutation__purge__failed": "Purge failed.", "@sage/xtrem-communication/nodes__sys_message_history__mutation__purge__parameter__integrationSolution": "Integration solution", "@sage/xtrem-communication/nodes__sys_message_history__node_name": "System message history", "@sage/xtrem-communication/nodes__sys_message_history__property__attributes": "Attributes", "@sage/xtrem-communication/nodes__sys_message_history__property__communicationDiagnoses": "Communication diagnoses", "@sage/xtrem-communication/nodes__sys_message_history__property__context": "Context", "@sage/xtrem-communication/nodes__sys_message_history__property__errorMessage": "Error message", "@sage/xtrem-communication/nodes__sys_message_history__property__errorStack": "Error stack", "@sage/xtrem-communication/nodes__sys_message_history__property__id": "ID", "@sage/xtrem-communication/nodes__sys_message_history__property__integrationSolution": "Integration solution", "@sage/xtrem-communication/nodes__sys_message_history__property__receivedRequest": "Request received", "@sage/xtrem-communication/nodes__sys_message_history__property__receivedStamp": "Received time stamp", "@sage/xtrem-communication/nodes__sys_message_history__property__sendStamp": "Send time stamp", "@sage/xtrem-communication/nodes__sys_message_history__property__sentRequest": "Request sent", "@sage/xtrem-communication/nodes__sys_message_history__property__status": "Status", "@sage/xtrem-communication/nodes__sys_message_history__property__user": "User", "@sage/xtrem-communication/nodes__sys_notification__asyncMutation__asyncExport": "Export", "@sage/xtrem-communication/nodes__sys_notification__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-communication/nodes__sys_notification__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-communication/nodes__sys_notification__node_name": "System notification", "@sage/xtrem-communication/nodes__sys_notification__property__extraAttributes": "Extra attributes", "@sage/xtrem-communication/nodes__sys_notification__property__locale": "Locale", "@sage/xtrem-communication/nodes__sys_notification__property__login": "<PERSON><PERSON>", "@sage/xtrem-communication/nodes__sys_notification__property__notificationId": "Notification ID", "@sage/xtrem-communication/nodes__sys_notification__property__originId": "Origin ID", "@sage/xtrem-communication/nodes__sys_notification__property__payload": "Payload", "@sage/xtrem-communication/nodes__sys_notification__property__replyId": "Reply ID", "@sage/xtrem-communication/nodes__sys_notification__property__replyTopic": "Reply topic", "@sage/xtrem-communication/nodes__sys_notification__property__resumeToken": "Resume token", "@sage/xtrem-communication/nodes__sys_notification__property__status": "Status", "@sage/xtrem-communication/nodes__sys_notification__property__tenantId": "Tenant ID", "@sage/xtrem-communication/nodes__sys_notification__property__topic": "Topic", "@sage/xtrem-communication/nodes__sys_notification__property__userEmail": "User email", "@sage/xtrem-communication/nodes__sys_notification_history__asyncMutation__asyncExport": "Export", "@sage/xtrem-communication/nodes__sys_notification_history__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-communication/nodes__sys_notification_history__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-communication/nodes__sys_notification_history__node_name": "System notification history", "@sage/xtrem-communication/nodes__sys_notification_history__property__dateLogged": "Date notification history record created", "@sage/xtrem-communication/nodes__sys_notification_history__property__envelope": "Envelope", "@sage/xtrem-communication/nodes__sys_notification_history__property__errorMessage": "Error message", "@sage/xtrem-communication/nodes__sys_notification_history__property__errorStack": "Error stack", "@sage/xtrem-communication/nodes__sys_notification_history__property__listener": "Listener", "@sage/xtrem-communication/nodes__sys_notification_history__property__locale": "Locale", "@sage/xtrem-communication/nodes__sys_notification_history__property__notificationContext": "Notification context", "@sage/xtrem-communication/nodes__sys_notification_history__property__notificationId": "Notification ID", "@sage/xtrem-communication/nodes__sys_notification_history__property__originId": "Origin ID", "@sage/xtrem-communication/nodes__sys_notification_history__property__replyId": "Reply ID", "@sage/xtrem-communication/nodes__sys_notification_history__property__replyTopic": "Reply topic", "@sage/xtrem-communication/nodes__sys_notification_history__property__status": "Status", "@sage/xtrem-communication/nodes__sys_notification_history__property__topic": "Topic", "@sage/xtrem-communication/nodes__sys_notification_history__query__all": "All", "@sage/xtrem-communication/nodes__sys_notification_history__query__all__failed": "All failed.", "@sage/xtrem-communication/nodes__sys_notification_log_entry__asyncMutation__asyncExport": "Export", "@sage/xtrem-communication/nodes__sys_notification_log_entry__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-communication/nodes__sys_notification_log_entry__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-communication/nodes__sys_notification_log_entry__node_name": "System notification log entry", "@sage/xtrem-communication/nodes__sys_notification_log_entry__property__data": "Data", "@sage/xtrem-communication/nodes__sys_notification_log_entry__property__level": "Level", "@sage/xtrem-communication/nodes__sys_notification_log_entry__property__message": "Message", "@sage/xtrem-communication/nodes__sys_notification_log_entry__property__spanContext": "Span context", "@sage/xtrem-communication/nodes__sys_notification_log_entry__property__sysNotificationState": "System notification state", "@sage/xtrem-communication/nodes__sys_notification_log_entry__property__timestamp": "Timestamp", "@sage/xtrem-communication/nodes__sys_notification_state__asyncMutation__asyncExport": "Export", "@sage/xtrem-communication/nodes__sys_notification_state__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-communication/nodes__sys_notification_state__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-communication/nodes__sys_notification_state__bulkMutation__bulkDelete": "Bulk delete", "@sage/xtrem-communication/nodes__sys_notification_state__node_name": "System notification state", "@sage/xtrem-communication/nodes__sys_notification_state__property__envelope": "Envelope", "@sage/xtrem-communication/nodes__sys_notification_state__property__isRead": "Read", "@sage/xtrem-communication/nodes__sys_notification_state__property__isUserNotificationRequested": "Is user notification requested", "@sage/xtrem-communication/nodes__sys_notification_state__property__locale": "Locale", "@sage/xtrem-communication/nodes__sys_notification_state__property__logs": "Logs", "@sage/xtrem-communication/nodes__sys_notification_state__property__message": "Message", "@sage/xtrem-communication/nodes__sys_notification_state__property__notificationContext": "Notification context", "@sage/xtrem-communication/nodes__sys_notification_state__property__notificationId": "Notification ID", "@sage/xtrem-communication/nodes__sys_notification_state__property__operationName": "Operation name", "@sage/xtrem-communication/nodes__sys_notification_state__property__originId": "Origin ID", "@sage/xtrem-communication/nodes__sys_notification_state__property__parameterValues": "Parameter values", "@sage/xtrem-communication/nodes__sys_notification_state__property__progress": "Progress", "@sage/xtrem-communication/nodes__sys_notification_state__property__progressBarPercent": "Progress bar percent", "@sage/xtrem-communication/nodes__sys_notification_state__property__replyId": "Reply ID", "@sage/xtrem-communication/nodes__sys_notification_state__property__result": "Result", "@sage/xtrem-communication/nodes__sys_notification_state__property__status": "Status", "@sage/xtrem-communication/nodes__sys_notification_state__property__timeEnded": "Time ended", "@sage/xtrem-communication/nodes__sys_notification_state__property__timeStarted": "Time started", "@sage/xtrem-communication/nodes__sys_notification_state__property__topic": "Topic", "@sage/xtrem-communication/nodes__sys_notification_state__property__user": "User", "@sage/xtrem-communication/nodes__sys-notification-state__error-when-data-level-result": "You can set a data result only when the level is result.", "@sage/xtrem-communication/nodes__sys-notification-state__error-when-status-success": "You can set an error message only when the status is error.", "@sage/xtrem-communication/nodes__sys-notification-state__status_deletion": "You can only delete a history if the status is error, interrupted, notResponding, stopped or success.", "@sage/xtrem-communication/package__name": "Communication", "@sage/xtrem-communication/pages__sys_notification_history____title": "Notification history", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__dateLogged": "<PERSON><PERSON>", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__errorMessage": "Message", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__listener": "Listener", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__replyID": "Reply ID", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__replyTopic": "Reply topic", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__status": "Status", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__topic": "Topic", "@sage/xtrem-communication/pages__sys_notification_history__history____title": "Results", "@sage/xtrem-communication/pages__sys_notification_history__historyBlock____title": "System", "@sage/xtrem-communication/pages__sys_notification_history__historyCriteriaBlock____title": "Criteria", "@sage/xtrem-communication/pages__sys_notification_history__historyStatus____title": "Status", "@sage/xtrem-communication/pages__sys_notification_history__section____title": "System", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__bulkActions__title": "Delete", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__bulkActions__title__2": "Stop", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line_4__title": "Time end", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line_5__title": "Progress", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line1__title": "Schedule", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line2__title": "Operation", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line2Right__title": "ID", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line3__title": "Time started", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line4__title": "Time end", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line5__title": "Progress", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line6__title": "User", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line7__title": "Phase", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line8__title": "Detail", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__message__title": "Message", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__title__title": "Status", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-communication/pages__sys_notification_state____objectTypePlural": "Batch task history", "@sage/xtrem-communication/pages__sys_notification_state____objectTypeSingular": "Batch task history", "@sage/xtrem-communication/pages__sys_notification_state____title": "Batch task history", "@sage/xtrem-communication/pages__sys_notification_state__generalSection____title": "General", "@sage/xtrem-communication/pages__sys_notification_state__logs____columns__title__data": "Link", "@sage/xtrem-communication/pages__sys_notification_state__logs____columns__title__level": "Level", "@sage/xtrem-communication/pages__sys_notification_state__logs____columns__title__message": "Message", "@sage/xtrem-communication/pages__sys_notification_state__logs____columns__title__timestamp": "Time", "@sage/xtrem-communication/pages__sys_notification_state__logs____title": "Logs", "@sage/xtrem-communication/pages__sys_notification_state__logsSection____title": "Logs", "@sage/xtrem-communication/pages__sys_notification_state__message____title": "Message", "@sage/xtrem-communication/pages__sys_notification_state__notificationId____title": "Notification ID", "@sage/xtrem-communication/pages__sys_notification_state__operationName____title": "Operation", "@sage/xtrem-communication/pages__sys_notification_state__originId____title": "Origin ID", "@sage/xtrem-communication/pages__sys_notification_state__parameters____columns__title__name": "Name", "@sage/xtrem-communication/pages__sys_notification_state__parameters____columns__title__value": "Value", "@sage/xtrem-communication/pages__sys_notification_state__parameters____title": "Parameters", "@sage/xtrem-communication/pages__sys_notification_state__progressBarPercent____title": "Progress", "@sage/xtrem-communication/pages__sys_notification_state__result____title": "Result", "@sage/xtrem-communication/pages__sys_notification_state__status____title": "Status", "@sage/xtrem-communication/pages__sys_notification_state__timeEnded____title": "End time", "@sage/xtrem-communication/pages__sys_notification_state__timeStarted____title": "Start time", "@sage/xtrem-communication/pages__sys_notification_state__track_button_text": "Track", "@sage/xtrem-communication/permission__create__name": "Create", "@sage/xtrem-communication/permission__delete__name": "Delete", "@sage/xtrem-communication/permission__read__name": "Read", "@sage/xtrem-communication/permission__update__name": "Update", "@sage/xtrem-communication/service_options__notification_center__name": "Notification centre", "@sage/xtrem-communication/sys__notification_history__search": "Search"}