{"@sage/xtrem-communication/activity__sys_notification_history__name": "", "@sage/xtrem-communication/activity__sys_notification_state__name": "", "@sage/xtrem-communication/completed_async_mutation_description": "", "@sage/xtrem-communication/completed_async_mutation_title": "", "@sage/xtrem-communication/data_types__communication_notification_data_data_type__name": "", "@sage/xtrem-communication/data_types__communication_state_enum__name": "", "@sage/xtrem-communication/data_types__error_message_data_type__name": "", "@sage/xtrem-communication/data_types__error_stack_data_type__name": "", "@sage/xtrem-communication/data_types__integration_state_enum__name": "", "@sage/xtrem-communication/data_types__listener_name_data_type__name": "", "@sage/xtrem-communication/data_types__log_level_enum__name": "", "@sage/xtrem-communication/data_types__log_message__name": "", "@sage/xtrem-communication/data_types__message_data_data_type__name": "", "@sage/xtrem-communication/data_types__message_data_type__name": "", "@sage/xtrem-communication/data_types__notification_status_enum__name": "", "@sage/xtrem-communication/data_types__origin_id_data_type__name": "", "@sage/xtrem-communication/data_types__request_data_data_type__name": "", "@sage/xtrem-communication/data_types__sqs_status_enum__name": "", "@sage/xtrem-communication/enums__communication_state__error": "", "@sage/xtrem-communication/enums__communication_state__interrupted": "", "@sage/xtrem-communication/enums__communication_state__interruptRequested": "", "@sage/xtrem-communication/enums__communication_state__notSent": "", "@sage/xtrem-communication/enums__communication_state__received": "", "@sage/xtrem-communication/enums__communication_state__retry": "", "@sage/xtrem-communication/enums__communication_state__sent": "", "@sage/xtrem-communication/enums__communication_state__stopped": "", "@sage/xtrem-communication/enums__communication_state__stopRequested": "", "@sage/xtrem-communication/enums__communication_state__success": "", "@sage/xtrem-communication/enums__integration_state__desynchronized": "", "@sage/xtrem-communication/enums__integration_state__error": "", "@sage/xtrem-communication/enums__integration_state__not": "", "@sage/xtrem-communication/enums__integration_state__pending": "", "@sage/xtrem-communication/enums__integration_state__success": "", "@sage/xtrem-communication/enums__log_level__error": "", "@sage/xtrem-communication/enums__log_level__exception": "", "@sage/xtrem-communication/enums__log_level__info": "", "@sage/xtrem-communication/enums__log_level__result": "", "@sage/xtrem-communication/enums__log_level__test": "", "@sage/xtrem-communication/enums__log_level__warning": "", "@sage/xtrem-communication/enums__notification_status__error": "", "@sage/xtrem-communication/enums__notification_status__interrupted": "", "@sage/xtrem-communication/enums__notification_status__interruptRequested": "", "@sage/xtrem-communication/enums__notification_status__notResponding": "", "@sage/xtrem-communication/enums__notification_status__pending": "", "@sage/xtrem-communication/enums__notification_status__running": "", "@sage/xtrem-communication/enums__notification_status__stopped": "", "@sage/xtrem-communication/enums__notification_status__stopRequested": "", "@sage/xtrem-communication/enums__notification_status__success": "", "@sage/xtrem-communication/enums__sqs_status__fail": "", "@sage/xtrem-communication/enums__sqs_status__pending": "", "@sage/xtrem-communication/interrupt_requested": "", "@sage/xtrem-communication/menu_item__communication": "", "@sage/xtrem-communication/menu_item__scheduler": "", "@sage/xtrem-communication/nodes__sys_dynamic_listener__node_name": "", "@sage/xtrem-communication/nodes__sys_message__asyncMutation__asyncExport": "", "@sage/xtrem-communication/nodes__sys_message__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-communication/nodes__sys_message__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-communication/nodes__sys_message__node_name": "", "@sage/xtrem-communication/nodes__sys_message__property__attributes": "", "@sage/xtrem-communication/nodes__sys_message__property__messageId": "", "@sage/xtrem-communication/nodes__sys_message__property__payload": "", "@sage/xtrem-communication/nodes__sys_message__property__queue": "", "@sage/xtrem-communication/nodes__sys_message__property__status": "", "@sage/xtrem-communication/nodes__sys_message__property__tenantId": "", "@sage/xtrem-communication/nodes__sys_message_history__asyncMutation__asyncExport": "", "@sage/xtrem-communication/nodes__sys_message_history__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-communication/nodes__sys_message_history__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-communication/nodes__sys_message_history__mutation__purge": "", "@sage/xtrem-communication/nodes__sys_message_history__mutation__purge__failed": "", "@sage/xtrem-communication/nodes__sys_message_history__mutation__purge__parameter__integrationSolution": "", "@sage/xtrem-communication/nodes__sys_message_history__node_name": "", "@sage/xtrem-communication/nodes__sys_message_history__property__attributes": "", "@sage/xtrem-communication/nodes__sys_message_history__property__communicationDiagnoses": "", "@sage/xtrem-communication/nodes__sys_message_history__property__context": "", "@sage/xtrem-communication/nodes__sys_message_history__property__errorMessage": "", "@sage/xtrem-communication/nodes__sys_message_history__property__errorStack": "", "@sage/xtrem-communication/nodes__sys_message_history__property__id": "", "@sage/xtrem-communication/nodes__sys_message_history__property__integrationSolution": "", "@sage/xtrem-communication/nodes__sys_message_history__property__receivedRequest": "", "@sage/xtrem-communication/nodes__sys_message_history__property__receivedStamp": "", "@sage/xtrem-communication/nodes__sys_message_history__property__sendStamp": "", "@sage/xtrem-communication/nodes__sys_message_history__property__sentRequest": "", "@sage/xtrem-communication/nodes__sys_message_history__property__status": "", "@sage/xtrem-communication/nodes__sys_message_history__property__user": "", "@sage/xtrem-communication/nodes__sys_notification__asyncMutation__asyncExport": "", "@sage/xtrem-communication/nodes__sys_notification__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-communication/nodes__sys_notification__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-communication/nodes__sys_notification__node_name": "", "@sage/xtrem-communication/nodes__sys_notification__property__extraAttributes": "", "@sage/xtrem-communication/nodes__sys_notification__property__locale": "", "@sage/xtrem-communication/nodes__sys_notification__property__login": "", "@sage/xtrem-communication/nodes__sys_notification__property__notificationId": "", "@sage/xtrem-communication/nodes__sys_notification__property__originId": "", "@sage/xtrem-communication/nodes__sys_notification__property__payload": "", "@sage/xtrem-communication/nodes__sys_notification__property__replyId": "", "@sage/xtrem-communication/nodes__sys_notification__property__replyTopic": "", "@sage/xtrem-communication/nodes__sys_notification__property__resumeToken": "", "@sage/xtrem-communication/nodes__sys_notification__property__status": "", "@sage/xtrem-communication/nodes__sys_notification__property__tenantId": "", "@sage/xtrem-communication/nodes__sys_notification__property__topic": "", "@sage/xtrem-communication/nodes__sys_notification__property__userEmail": "", "@sage/xtrem-communication/nodes__sys_notification_history__asyncMutation__asyncExport": "", "@sage/xtrem-communication/nodes__sys_notification_history__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-communication/nodes__sys_notification_history__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-communication/nodes__sys_notification_history__node_name": "", "@sage/xtrem-communication/nodes__sys_notification_history__property__dateLogged": "", "@sage/xtrem-communication/nodes__sys_notification_history__property__envelope": "", "@sage/xtrem-communication/nodes__sys_notification_history__property__errorMessage": "", "@sage/xtrem-communication/nodes__sys_notification_history__property__errorStack": "", "@sage/xtrem-communication/nodes__sys_notification_history__property__listener": "", "@sage/xtrem-communication/nodes__sys_notification_history__property__locale": "", "@sage/xtrem-communication/nodes__sys_notification_history__property__notificationContext": "", "@sage/xtrem-communication/nodes__sys_notification_history__property__notificationId": "", "@sage/xtrem-communication/nodes__sys_notification_history__property__originId": "", "@sage/xtrem-communication/nodes__sys_notification_history__property__replyId": "", "@sage/xtrem-communication/nodes__sys_notification_history__property__replyTopic": "", "@sage/xtrem-communication/nodes__sys_notification_history__property__status": "", "@sage/xtrem-communication/nodes__sys_notification_history__property__topic": "", "@sage/xtrem-communication/nodes__sys_notification_history__query__all": "", "@sage/xtrem-communication/nodes__sys_notification_history__query__all__failed": "", "@sage/xtrem-communication/nodes__sys_notification_log_entry__asyncMutation__asyncExport": "", "@sage/xtrem-communication/nodes__sys_notification_log_entry__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-communication/nodes__sys_notification_log_entry__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-communication/nodes__sys_notification_log_entry__node_name": "", "@sage/xtrem-communication/nodes__sys_notification_log_entry__property__data": "", "@sage/xtrem-communication/nodes__sys_notification_log_entry__property__level": "", "@sage/xtrem-communication/nodes__sys_notification_log_entry__property__message": "", "@sage/xtrem-communication/nodes__sys_notification_log_entry__property__spanContext": "", "@sage/xtrem-communication/nodes__sys_notification_log_entry__property__sysNotificationState": "", "@sage/xtrem-communication/nodes__sys_notification_log_entry__property__timestamp": "", "@sage/xtrem-communication/nodes__sys_notification_state__asyncMutation__asyncExport": "", "@sage/xtrem-communication/nodes__sys_notification_state__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-communication/nodes__sys_notification_state__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-communication/nodes__sys_notification_state__bulkMutation__bulkDelete": "", "@sage/xtrem-communication/nodes__sys_notification_state__node_name": "", "@sage/xtrem-communication/nodes__sys_notification_state__property__envelope": "", "@sage/xtrem-communication/nodes__sys_notification_state__property__isRead": "", "@sage/xtrem-communication/nodes__sys_notification_state__property__isUserNotificationRequested": "", "@sage/xtrem-communication/nodes__sys_notification_state__property__locale": "", "@sage/xtrem-communication/nodes__sys_notification_state__property__logs": "", "@sage/xtrem-communication/nodes__sys_notification_state__property__message": "", "@sage/xtrem-communication/nodes__sys_notification_state__property__notificationContext": "", "@sage/xtrem-communication/nodes__sys_notification_state__property__notificationId": "", "@sage/xtrem-communication/nodes__sys_notification_state__property__operationName": "", "@sage/xtrem-communication/nodes__sys_notification_state__property__originId": "", "@sage/xtrem-communication/nodes__sys_notification_state__property__parameterValues": "", "@sage/xtrem-communication/nodes__sys_notification_state__property__progress": "", "@sage/xtrem-communication/nodes__sys_notification_state__property__progressBarPercent": "", "@sage/xtrem-communication/nodes__sys_notification_state__property__replyId": "", "@sage/xtrem-communication/nodes__sys_notification_state__property__result": "", "@sage/xtrem-communication/nodes__sys_notification_state__property__status": "", "@sage/xtrem-communication/nodes__sys_notification_state__property__timeEnded": "", "@sage/xtrem-communication/nodes__sys_notification_state__property__timeStarted": "", "@sage/xtrem-communication/nodes__sys_notification_state__property__topic": "", "@sage/xtrem-communication/nodes__sys_notification_state__property__user": "", "@sage/xtrem-communication/nodes__sys-notification-state__error-when-data-level-result": "", "@sage/xtrem-communication/nodes__sys-notification-state__error-when-status-success": "", "@sage/xtrem-communication/nodes__sys-notification-state__status_deletion": "", "@sage/xtrem-communication/package__name": "", "@sage/xtrem-communication/pages__sys_notification_history____title": "", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__dateLogged": "", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__errorMessage": "", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__status": "", "@sage/xtrem-communication/pages__sys_notification_history__history____title": "", "@sage/xtrem-communication/pages__sys_notification_history__historyBlock____title": "", "@sage/xtrem-communication/pages__sys_notification_history__historyCriteriaBlock____title": "", "@sage/xtrem-communication/pages__sys_notification_history__historyStatus____title": "", "@sage/xtrem-communication/pages__sys_notification_history__section____title": "", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__bulkActions__title": "", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__bulkActions__title__2": "", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line_4__title": "", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line_5__title": "", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line2__title": "", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line2Right__title": "", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line3__title": "", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line6__title": "", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line7__title": "", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line8__title": "", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__message__title": "", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__title__title": "", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__optionsMenu__title": "", "@sage/xtrem-communication/pages__sys_notification_state____objectTypePlural": "", "@sage/xtrem-communication/pages__sys_notification_state____objectTypeSingular": "", "@sage/xtrem-communication/pages__sys_notification_state____title": "", "@sage/xtrem-communication/pages__sys_notification_state__generalSection____title": "", "@sage/xtrem-communication/pages__sys_notification_state__logs____columns__title__data": "", "@sage/xtrem-communication/pages__sys_notification_state__logs____columns__title__level": "", "@sage/xtrem-communication/pages__sys_notification_state__logs____columns__title__message": "", "@sage/xtrem-communication/pages__sys_notification_state__logs____columns__title__timestamp": "", "@sage/xtrem-communication/pages__sys_notification_state__logs____title": "", "@sage/xtrem-communication/pages__sys_notification_state__logsSection____title": "", "@sage/xtrem-communication/pages__sys_notification_state__message____title": "", "@sage/xtrem-communication/pages__sys_notification_state__notificationId____title": "", "@sage/xtrem-communication/pages__sys_notification_state__operationName____title": "", "@sage/xtrem-communication/pages__sys_notification_state__originId____title": "", "@sage/xtrem-communication/pages__sys_notification_state__parameters____columns__title__name": "", "@sage/xtrem-communication/pages__sys_notification_state__parameters____columns__title__value": "", "@sage/xtrem-communication/pages__sys_notification_state__parameters____title": "", "@sage/xtrem-communication/pages__sys_notification_state__progressBarPercent____title": "", "@sage/xtrem-communication/pages__sys_notification_state__result____title": "", "@sage/xtrem-communication/pages__sys_notification_state__status____title": "", "@sage/xtrem-communication/pages__sys_notification_state__timeEnded____title": "", "@sage/xtrem-communication/pages__sys_notification_state__timeStarted____title": "", "@sage/xtrem-communication/pages__sys_notification_state__track_button_text": "", "@sage/xtrem-communication/permission__create__name": "", "@sage/xtrem-communication/permission__delete__name": "", "@sage/xtrem-communication/permission__read__name": "", "@sage/xtrem-communication/permission__update__name": "", "@sage/xtrem-communication/service_options__notification_center__name": "", "@sage/xtrem-communication/sys__notification_history__search": ""}