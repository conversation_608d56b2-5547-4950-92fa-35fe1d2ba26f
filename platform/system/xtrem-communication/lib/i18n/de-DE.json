{"@sage/xtrem-communication/activity__sys_notification_history__name": "Systembenachrichtigungsverlauf", "@sage/xtrem-communication/activity__sys_notification_state__name": "Systembenachrichtigungsstatus", "@sage/xtrem-communication/completed_async_mutation_description": "Abgeschlossen: {{node}} / {{mutation}}", "@sage/xtrem-communication/completed_async_mutation_title": "Asynchrone Mutation abgeschlossen.", "@sage/xtrem-communication/data_types__communication_notification_data_data_type__name": "Datentyp Daten Benachrichtigung Kommunikation", "@sage/xtrem-communication/data_types__communication_state_enum__name": "Enum Status Kommunikation", "@sage/xtrem-communication/data_types__error_message_data_type__name": "Datentyp Fehlermeldung", "@sage/xtrem-communication/data_types__error_stack_data_type__name": "Datentyp Fehlerstapel", "@sage/xtrem-communication/data_types__integration_state_enum__name": "Enum Status Integration", "@sage/xtrem-communication/data_types__listener_name_data_type__name": "<PERSON>nt<PERSON>p Name Listener", "@sage/xtrem-communication/data_types__log_level_enum__name": "Enum Log-Level", "@sage/xtrem-communication/data_types__log_message__name": "Log-Meldung", "@sage/xtrem-communication/data_types__message_data_data_type__name": "Datentyp <PERSON>", "@sage/xtrem-communication/data_types__message_data_type__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-communication/data_types__notification_status_enum__name": "Enum <PERSON>richtigungs<PERSON>tus", "@sage/xtrem-communication/data_types__origin_id_data_type__name": "<PERSON><PERSON><PERSON><PERSON>-ID", "@sage/xtrem-communication/data_types__request_data_data_type__name": "Datentyp Anforderung Daten", "@sage/xtrem-communication/data_types__sqs_status_enum__name": "Enum SQS-Status", "@sage/xtrem-communication/enums__communication_state__'error'": "<PERSON><PERSON>", "@sage/xtrem-communication/enums__communication_state__'notSent'": "<PERSON>cht gesendet", "@sage/xtrem-communication/enums__communication_state__'received'": "Empfangen", "@sage/xtrem-communication/enums__communication_state__'retry'": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-communication/enums__communication_state__'sent'": "Gesendet", "@sage/xtrem-communication/enums__communication_state__'success'": "Erfolgreich", "@sage/xtrem-communication/enums__communication_state__error": "<PERSON><PERSON>", "@sage/xtrem-communication/enums__communication_state__interrupted": "Unterbrochen", "@sage/xtrem-communication/enums__communication_state__interruptRequested": "Unterbrechen angefordert", "@sage/xtrem-communication/enums__communication_state__notSent": "<PERSON>cht gesendet", "@sage/xtrem-communication/enums__communication_state__received": "Empfangen", "@sage/xtrem-communication/enums__communication_state__retry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-communication/enums__communication_state__sent": "Gesendet", "@sage/xtrem-communication/enums__communication_state__stopped": "Ang<PERSON><PERSON><PERSON>", "@sage/xtrem-communication/enums__communication_state__stopRequested": "<PERSON><PERSON><PERSON> an<PERSON>", "@sage/xtrem-communication/enums__communication_state__success": "Erfolgreich", "@sage/xtrem-communication/enums__integration_state__'error'": "<PERSON><PERSON>", "@sage/xtrem-communication/enums__integration_state__'not'": "<PERSON>cht integriert", "@sage/xtrem-communication/enums__integration_state__'pending'": "Wird integriert", "@sage/xtrem-communication/enums__integration_state__'success'": "Integrier<PERSON>", "@sage/xtrem-communication/enums__integration_state__desynchronized": "Nicht synchronisiert", "@sage/xtrem-communication/enums__integration_state__error": "<PERSON><PERSON>", "@sage/xtrem-communication/enums__integration_state__not": "<PERSON>cht integriert", "@sage/xtrem-communication/enums__integration_state__pending": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-communication/enums__integration_state__success": "Erfolgreich", "@sage/xtrem-communication/enums__log_level__error": "<PERSON><PERSON>", "@sage/xtrem-communication/enums__log_level__exception": "Ausnahme", "@sage/xtrem-communication/enums__log_level__info": "Info", "@sage/xtrem-communication/enums__log_level__result": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-communication/enums__log_level__test": "Test", "@sage/xtrem-communication/enums__log_level__warning": "<PERSON><PERSON><PERSON>", "@sage/xtrem-communication/enums__notification_status__error": "<PERSON><PERSON>", "@sage/xtrem-communication/enums__notification_status__interrupted": "Unterbrochen", "@sage/xtrem-communication/enums__notification_status__interruptRequested": "Unterbrechen angefordert", "@sage/xtrem-communication/enums__notification_status__notResponding": "Antwortet nicht", "@sage/xtrem-communication/enums__notification_status__pending": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-communication/enums__notification_status__running": "Läuft", "@sage/xtrem-communication/enums__notification_status__stopped": "Ang<PERSON><PERSON><PERSON>", "@sage/xtrem-communication/enums__notification_status__stopRequested": "<PERSON><PERSON><PERSON> an<PERSON>", "@sage/xtrem-communication/enums__notification_status__success": "Erfolgreich", "@sage/xtrem-communication/enums__sqs_status__fail": "<PERSON><PERSON>", "@sage/xtrem-communication/enums__sqs_status__pending": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-communication/interrupt_requested": "Unterbrechen angefordert", "@sage/xtrem-communication/menu_item__communication": "Kommunikation", "@sage/xtrem-communication/menu_item__scheduler": "Planer", "@sage/xtrem-communication/nodes__sys_dynamic_listener__node_name": "Dynamischer Listener System", "@sage/xtrem-communication/nodes__sys_message__asyncMutation__asyncExport": "Export", "@sage/xtrem-communication/nodes__sys_message__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-communication/nodes__sys_message__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-communication/nodes__sys_message__node_name": "Systemmeldung", "@sage/xtrem-communication/nodes__sys_message__property__attributes": "Attribute", "@sage/xtrem-communication/nodes__sys_message__property__messageId": "Meldungs-ID", "@sage/xtrem-communication/nodes__sys_message__property__payload": "Payload", "@sage/xtrem-communication/nodes__sys_message__property__queue": "Warteschlange", "@sage/xtrem-communication/nodes__sys_message__property__status": "Status", "@sage/xtrem-communication/nodes__sys_message__property__tenantId": "Tenant-ID", "@sage/xtrem-communication/nodes__sys_message_history__asyncMutation__asyncExport": "Export", "@sage/xtrem-communication/nodes__sys_message_history__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-communication/nodes__sys_message_history__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-communication/nodes__sys_message_history__mutation__purge": "<PERSON><PERSON>", "@sage/xtrem-communication/nodes__sys_message_history__mutation__purge__failed": "<PERSON><PERSON> feh<PERSON>.", "@sage/xtrem-communication/nodes__sys_message_history__mutation__purge__parameter__integrationSolution": "Integrationslösung", "@sage/xtrem-communication/nodes__sys_message_history__node_name": "Systemmeldungsverlauf", "@sage/xtrem-communication/nodes__sys_message_history__property__attributes": "Attribute", "@sage/xtrem-communication/nodes__sys_message_history__property__communicationDiagnoses": "Diagnosen Kommunikation", "@sage/xtrem-communication/nodes__sys_message_history__property__context": "Kontext", "@sage/xtrem-communication/nodes__sys_message_history__property__errorMessage": "Fehlermeldung", "@sage/xtrem-communication/nodes__sys_message_history__property__errorStack": "Fehlerstapel", "@sage/xtrem-communication/nodes__sys_message_history__property__id": "ID", "@sage/xtrem-communication/nodes__sys_message_history__property__integrationSolution": "Integrationslösung", "@sage/xtrem-communication/nodes__sys_message_history__property__receivedRequest": "Anforderung erhalten", "@sage/xtrem-communication/nodes__sys_message_history__property__receivedStamp": "Zeitstempel Erhalten", "@sage/xtrem-communication/nodes__sys_message_history__property__sendStamp": "Zeitstempel Senden", "@sage/xtrem-communication/nodes__sys_message_history__property__sentRequest": "Anforderung gesendet", "@sage/xtrem-communication/nodes__sys_message_history__property__status": "Status", "@sage/xtrem-communication/nodes__sys_message_history__property__user": "<PERSON><PERSON><PERSON>", "@sage/xtrem-communication/nodes__sys_notification__asyncMutation__asyncExport": "Export", "@sage/xtrem-communication/nodes__sys_notification__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-communication/nodes__sys_notification__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-communication/nodes__sys_notification__node_name": "Systembenachrichtigung", "@sage/xtrem-communication/nodes__sys_notification__property__extraAttributes": "Zusätzliche Attribute", "@sage/xtrem-communication/nodes__sys_notification__property__locale": "Gebietsschema", "@sage/xtrem-communication/nodes__sys_notification__property__login": "Anmelden", "@sage/xtrem-communication/nodes__sys_notification__property__notificationId": "Benachrichtigungs-ID", "@sage/xtrem-communication/nodes__sys_notification__property__originId": "Ursprüngliche ID", "@sage/xtrem-communication/nodes__sys_notification__property__payload": "Payload", "@sage/xtrem-communication/nodes__sys_notification__property__replyId": "Antwort-ID", "@sage/xtrem-communication/nodes__sys_notification__property__replyTopic": "Antwort-Thema", "@sage/xtrem-communication/nodes__sys_notification__property__resumeToken": "Token fortsetzen", "@sage/xtrem-communication/nodes__sys_notification__property__status": "Status", "@sage/xtrem-communication/nodes__sys_notification__property__tenantId": "Tenant-ID", "@sage/xtrem-communication/nodes__sys_notification__property__topic": "<PERSON>a", "@sage/xtrem-communication/nodes__sys_notification__property__userEmail": "Benutzer-E-Mail", "@sage/xtrem-communication/nodes__sys_notification_history__asyncMutation__asyncExport": "Export", "@sage/xtrem-communication/nodes__sys_notification_history__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-communication/nodes__sys_notification_history__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-communication/nodes__sys_notification_history__node_name": "Systembenachrichtigungsverlauf", "@sage/xtrem-communication/nodes__sys_notification_history__property__dateLogged": "Datum Erstellung Benachrichtigungsverlaufsdatensatz", "@sage/xtrem-communication/nodes__sys_notification_history__property__envelope": "Umschlag", "@sage/xtrem-communication/nodes__sys_notification_history__property__errorMessage": "Fehlermeldung", "@sage/xtrem-communication/nodes__sys_notification_history__property__errorStack": "Fehlerstapel", "@sage/xtrem-communication/nodes__sys_notification_history__property__listener": "Listener", "@sage/xtrem-communication/nodes__sys_notification_history__property__locale": "Gebietsschema", "@sage/xtrem-communication/nodes__sys_notification_history__property__notificationContext": "Benachrichtigungskontext", "@sage/xtrem-communication/nodes__sys_notification_history__property__notificationId": "Benachrichtigungs-ID", "@sage/xtrem-communication/nodes__sys_notification_history__property__originId": "Ursprüngliche ID", "@sage/xtrem-communication/nodes__sys_notification_history__property__replyId": "Antwort-ID", "@sage/xtrem-communication/nodes__sys_notification_history__property__replyTopic": "Antwort-Thema", "@sage/xtrem-communication/nodes__sys_notification_history__property__status": "Status", "@sage/xtrem-communication/nodes__sys_notification_history__property__topic": "<PERSON>a", "@sage/xtrem-communication/nodes__sys_notification_history__query__all": "Alle", "@sage/xtrem-communication/nodes__sys_notification_history__query__all__failed": "Alle fehlgeschlagen.", "@sage/xtrem-communication/nodes__sys_notification_log_entry__asyncMutation__asyncExport": "Export", "@sage/xtrem-communication/nodes__sys_notification_log_entry__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-communication/nodes__sys_notification_log_entry__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-communication/nodes__sys_notification_log_entry__node_name": "Eingabe Benachrichtigungs-Protokoll System", "@sage/xtrem-communication/nodes__sys_notification_log_entry__property__data": "Daten", "@sage/xtrem-communication/nodes__sys_notification_log_entry__property__level": "Level", "@sage/xtrem-communication/nodes__sys_notification_log_entry__property__message": "<PERSON><PERSON><PERSON>", "@sage/xtrem-communication/nodes__sys_notification_log_entry__property__spanContext": "Span -Kontext", "@sage/xtrem-communication/nodes__sys_notification_log_entry__property__sysNotificationState": "Systembenachrichtigungsstatus", "@sage/xtrem-communication/nodes__sys_notification_log_entry__property__timestamp": "Zeitstempel", "@sage/xtrem-communication/nodes__sys_notification_state__asyncMutation__asyncExport": "Export", "@sage/xtrem-communication/nodes__sys_notification_state__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-communication/nodes__sys_notification_state__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-communication/nodes__sys_notification_state__bulkMutation__bulkDelete": "Massenlöschen", "@sage/xtrem-communication/nodes__sys_notification_state__node_name": "Systembenachrichtigungsstatus", "@sage/xtrem-communication/nodes__sys_notification_state__property__envelope": "Umschlag", "@sage/xtrem-communication/nodes__sys_notification_state__property__isRead": "<PERSON><PERSON>", "@sage/xtrem-communication/nodes__sys_notification_state__property__isUserNotificationRequested": "Ist Benutzerbenachrichtigung angefordert", "@sage/xtrem-communication/nodes__sys_notification_state__property__locale": "Gebietsschema", "@sage/xtrem-communication/nodes__sys_notification_state__property__logs": "Protokolle", "@sage/xtrem-communication/nodes__sys_notification_state__property__message": "<PERSON><PERSON><PERSON>", "@sage/xtrem-communication/nodes__sys_notification_state__property__notificationContext": "Benachrichtigungskontext", "@sage/xtrem-communication/nodes__sys_notification_state__property__notificationId": "Benachrichtigungs-ID", "@sage/xtrem-communication/nodes__sys_notification_state__property__operationName": "Vorgangsname", "@sage/xtrem-communication/nodes__sys_notification_state__property__originId": "Ursprüngliche ID", "@sage/xtrem-communication/nodes__sys_notification_state__property__parameterValues": "Parameterwerte", "@sage/xtrem-communication/nodes__sys_notification_state__property__progress": "Fort<PERSON><PERSON>t", "@sage/xtrem-communication/nodes__sys_notification_state__property__progressBarPercent": "Fortschrittsbalken Prozent", "@sage/xtrem-communication/nodes__sys_notification_state__property__replyId": "Antwort-ID", "@sage/xtrem-communication/nodes__sys_notification_state__property__result": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-communication/nodes__sys_notification_state__property__status": "Status", "@sage/xtrem-communication/nodes__sys_notification_state__property__timeEnded": "Zeit beendet", "@sage/xtrem-communication/nodes__sys_notification_state__property__timeStarted": "Zeit gestartet", "@sage/xtrem-communication/nodes__sys_notification_state__property__topic": "<PERSON>a", "@sage/xtrem-communication/nodes__sys_notification_state__property__user": "<PERSON><PERSON><PERSON>", "@sage/xtrem-communication/nodes__sys-notification-state__error-when-data-level-result": "<PERSON>e können das Datenergebnis nur setzen, wenn das Level Ergebnis ist.", "@sage/xtrem-communication/nodes__sys-notification-state__error-when-status-success": "<PERSON>e können eine Fehlermeldung nur setzen, wenn der Status Fehler ist.", "@sage/xtrem-communication/nodes__sys-notification-state__status_deletion": "<PERSON>e können einen Verlauf nur löschen, wenn der Status den Wert Fehler, Unterbrochen, <PERSON><PERSON><PERSON><PERSON><PERSON> nicht, Angehalten oder Erfolg hat.", "@sage/xtrem-communication/package__name": "Kommunikation", "@sage/xtrem-communication/pages__sys_notification_history____title": "Benachrichtigungsverlauf", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__dateLogged": "Gesendet", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__errorMessage": "<PERSON><PERSON><PERSON>", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__listener": "Listener", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__replyID": "Antwort-ID", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__replyTopic": "Antwort-Thema", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__status": "Status", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__topic": "<PERSON>a", "@sage/xtrem-communication/pages__sys_notification_history__history____title": "Ergebnisse", "@sage/xtrem-communication/pages__sys_notification_history__historyBlock____title": "System", "@sage/xtrem-communication/pages__sys_notification_history__historyCriteriaBlock____title": "Kriterien", "@sage/xtrem-communication/pages__sys_notification_history__historyStatus____title": "Status", "@sage/xtrem-communication/pages__sys_notification_history__section____title": "System", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__bulkActions__title": "Löschen", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__bulkActions__title__2": "<PERSON><PERSON><PERSON>", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line_4__title": "Zeit beendet", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line_5__title": "Fort<PERSON><PERSON>t", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line1__title": "Plan", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line2__title": "Vorgang", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line2Right__title": "ID", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line3__title": "Zeit gestartet", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line4__title": "Zeit beendet", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line5__title": "Fort<PERSON><PERSON>t", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line6__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line7__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line8__title": "Detail", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__message__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__title__title": "Status", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__optionsMenu__title": "Alle", "@sage/xtrem-communication/pages__sys_notification_state____objectTypePlural": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-communication/pages__sys_notification_state____objectTypeSingular": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-communication/pages__sys_notification_state____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-communication/pages__sys_notification_state__generalSection____title": "Allgemein", "@sage/xtrem-communication/pages__sys_notification_state__logs____columns__title__data": "Verknüpfung", "@sage/xtrem-communication/pages__sys_notification_state__logs____columns__title__level": "Level", "@sage/xtrem-communication/pages__sys_notification_state__logs____columns__title__message": "<PERSON><PERSON><PERSON>", "@sage/xtrem-communication/pages__sys_notification_state__logs____columns__title__timestamp": "Zeit", "@sage/xtrem-communication/pages__sys_notification_state__logs____title": "Protokolle", "@sage/xtrem-communication/pages__sys_notification_state__logsSection____title": "Protokolle", "@sage/xtrem-communication/pages__sys_notification_state__message____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-communication/pages__sys_notification_state__notificationId____title": "Benachrichtigungs-ID", "@sage/xtrem-communication/pages__sys_notification_state__operationName____title": "Vorgang", "@sage/xtrem-communication/pages__sys_notification_state__originId____title": "Ursprüngliche ID", "@sage/xtrem-communication/pages__sys_notification_state__parameters____columns__title__name": "Name", "@sage/xtrem-communication/pages__sys_notification_state__parameters____columns__title__value": "Wert", "@sage/xtrem-communication/pages__sys_notification_state__parameters____title": "Parameter", "@sage/xtrem-communication/pages__sys_notification_state__progressBarPercent____title": "Fort<PERSON><PERSON>t", "@sage/xtrem-communication/pages__sys_notification_state__result____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-communication/pages__sys_notification_state__status____title": "Status", "@sage/xtrem-communication/pages__sys_notification_state__timeEnded____title": "Endzeit", "@sage/xtrem-communication/pages__sys_notification_state__timeStarted____title": "Startzeit", "@sage/xtrem-communication/pages__sys_notification_state__track_button_text": "Rückmelden", "@sage/xtrem-communication/permission__create__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-communication/permission__delete__name": "Löschen", "@sage/xtrem-communication/permission__read__name": "<PERSON><PERSON>", "@sage/xtrem-communication/permission__update__name": "Aktualisieren", "@sage/xtrem-communication/service_options__notification_center__name": "Benachrichtigungs-Center", "@sage/xtrem-communication/sys__notification_history__search": "<PERSON><PERSON>"}