{"@sage/xtrem-communication/activity__sys_notification_history__name": "Histórico de notificações do sistema", "@sage/xtrem-communication/activity__sys_notification_state__name": "Status de notificação do sistema", "@sage/xtrem-communication/completed_async_mutation_description": "Concluído: {{node}} / {{mutation}}", "@sage/xtrem-communication/completed_async_mutation_title": "Mutação assíncrona concluída.", "@sage/xtrem-communication/data_types__communication_notification_data_data_type__name": "Tipo de dados dos dados da notificação de comunicação", "@sage/xtrem-communication/data_types__communication_state_enum__name": "Enum do estado da comunicação", "@sage/xtrem-communication/data_types__error_message_data_type__name": "Tipo de dados da mensagem de erro", "@sage/xtrem-communication/data_types__error_stack_data_type__name": "Tipo de dados da stack de erros", "@sage/xtrem-communication/data_types__integration_state_enum__name": "Estado de integração enum", "@sage/xtrem-communication/data_types__listener_name_data_type__name": "Tipo de dados do nome do Listener", "@sage/xtrem-communication/data_types__log_level_enum__name": "Enum nível de registo", "@sage/xtrem-communication/data_types__log_message__name": "Mensagem de registo", "@sage/xtrem-communication/data_types__message_data_data_type__name": "Tipo de dados da mensagem", "@sage/xtrem-communication/data_types__message_data_type__name": "Tipo de dados de mensagem", "@sage/xtrem-communication/data_types__notification_status_enum__name": "Enum de estado da notificação", "@sage/xtrem-communication/data_types__origin_id_data_type__name": "Tipo de dados ID origem", "@sage/xtrem-communication/data_types__request_data_data_type__name": "Tipo de dados do pedido", "@sage/xtrem-communication/data_types__sqs_status_enum__name": "Enum do estado do SQS", "@sage/xtrem-communication/enums__communication_state__'error'": "Erro", "@sage/xtrem-communication/enums__communication_state__'notSent'": "Não enviado", "@sage/xtrem-communication/enums__communication_state__'received'": "Recebido", "@sage/xtrem-communication/enums__communication_state__'retry'": "Tentativa", "@sage/xtrem-communication/enums__communication_state__'sent'": "Enviado", "@sage/xtrem-communication/enums__communication_state__'success'": "Sucesso", "@sage/xtrem-communication/enums__communication_state__error": "Erro", "@sage/xtrem-communication/enums__communication_state__interrupted": "Interrompido", "@sage/xtrem-communication/enums__communication_state__interruptRequested": "Interrupção solicitada", "@sage/xtrem-communication/enums__communication_state__notSent": "Não enviado", "@sage/xtrem-communication/enums__communication_state__received": "Recebido", "@sage/xtrem-communication/enums__communication_state__retry": "Tentativa", "@sage/xtrem-communication/enums__communication_state__sent": "Enviado", "@sage/xtrem-communication/enums__communication_state__stopped": "Parado", "@sage/xtrem-communication/enums__communication_state__stopRequested": "Paragem solicitada", "@sage/xtrem-communication/enums__communication_state__success": "Sucesso", "@sage/xtrem-communication/enums__integration_state__'error'": "Erro", "@sage/xtrem-communication/enums__integration_state__'not'": "Não integrado", "@sage/xtrem-communication/enums__integration_state__'pending'": "Sendo integrado", "@sage/xtrem-communication/enums__integration_state__'success'": "Integrado", "@sage/xtrem-communication/enums__integration_state__desynchronized": "Desincronizado", "@sage/xtrem-communication/enums__integration_state__error": "Erro", "@sage/xtrem-communication/enums__integration_state__not": "Não integrado", "@sage/xtrem-communication/enums__integration_state__pending": "Pendente", "@sage/xtrem-communication/enums__integration_state__success": "Sucesso", "@sage/xtrem-communication/enums__log_level__error": "Erro", "@sage/xtrem-communication/enums__log_level__exception": "Exceção", "@sage/xtrem-communication/enums__log_level__info": "Info", "@sage/xtrem-communication/enums__log_level__result": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-communication/enums__log_level__test": "<PERSON>e", "@sage/xtrem-communication/enums__log_level__warning": "Aviso", "@sage/xtrem-communication/enums__notification_status__error": "Erro", "@sage/xtrem-communication/enums__notification_status__interrupted": "Interrompido", "@sage/xtrem-communication/enums__notification_status__interruptRequested": "Interrupção solicitada", "@sage/xtrem-communication/enums__notification_status__notResponding": "Não responde", "@sage/xtrem-communication/enums__notification_status__pending": "Pendente", "@sage/xtrem-communication/enums__notification_status__running": "Em execução", "@sage/xtrem-communication/enums__notification_status__stopped": "Parado", "@sage/xtrem-communication/enums__notification_status__stopRequested": "Paragem solicitada", "@sage/xtrem-communication/enums__notification_status__success": "Sucesso", "@sage/xtrem-communication/enums__sqs_status__fail": "Erro", "@sage/xtrem-communication/enums__sqs_status__pending": "Pendente", "@sage/xtrem-communication/interrupt_requested": "Interrupção solicitada", "@sage/xtrem-communication/menu_item__communication": "Comunicação", "@sage/xtrem-communication/menu_item__scheduler": "Agendamento", "@sage/xtrem-communication/nodes__sys_dynamic_listener__node_name": "Monitorização dinâmica do sistema", "@sage/xtrem-communication/nodes__sys_message__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-communication/nodes__sys_message__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-communication/nodes__sys_message__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-communication/nodes__sys_message__node_name": "Mensagem de sistema", "@sage/xtrem-communication/nodes__sys_message__property__attributes": "Atributos", "@sage/xtrem-communication/nodes__sys_message__property__messageId": "ID da mensagem", "@sage/xtrem-communication/nodes__sys_message__property__payload": "Carga útil", "@sage/xtrem-communication/nodes__sys_message__property__queue": "<PERSON><PERSON>", "@sage/xtrem-communication/nodes__sys_message__property__status": "Status", "@sage/xtrem-communication/nodes__sys_message__property__tenantId": "ID do tenant", "@sage/xtrem-communication/nodes__sys_message_history__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-communication/nodes__sys_message_history__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-communication/nodes__sys_message_history__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-communication/nodes__sys_message_history__mutation__purge": "Expurgo", "@sage/xtrem-communication/nodes__sys_message_history__mutation__purge__failed": "O expurgo falhou.", "@sage/xtrem-communication/nodes__sys_message_history__mutation__purge__parameter__integrationSolution": "Solução de integração", "@sage/xtrem-communication/nodes__sys_message_history__node_name": "Histórico de mensagens do sistema", "@sage/xtrem-communication/nodes__sys_message_history__property__attributes": "Atributos", "@sage/xtrem-communication/nodes__sys_message_history__property__communicationDiagnoses": "Diagnósticos de comunicação", "@sage/xtrem-communication/nodes__sys_message_history__property__context": "Contexto", "@sage/xtrem-communication/nodes__sys_message_history__property__errorMessage": "Mensagem erro", "@sage/xtrem-communication/nodes__sys_message_history__property__errorStack": "<PERSON><PERSON><PERSON>", "@sage/xtrem-communication/nodes__sys_message_history__property__id": "ID", "@sage/xtrem-communication/nodes__sys_message_history__property__integrationSolution": "Solução de integração", "@sage/xtrem-communication/nodes__sys_message_history__property__receivedRequest": "Pedido recebido", "@sage/xtrem-communication/nodes__sys_message_history__property__receivedStamp": "\"Time stamp\" recebido", "@sage/xtrem-communication/nodes__sys_message_history__property__sendStamp": "Enviar \"Time stamp\"", "@sage/xtrem-communication/nodes__sys_message_history__property__sentRequest": "Pedido enviado", "@sage/xtrem-communication/nodes__sys_message_history__property__status": "Status", "@sage/xtrem-communication/nodes__sys_message_history__property__user": "Utilizador", "@sage/xtrem-communication/nodes__sys_notification__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-communication/nodes__sys_notification__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-communication/nodes__sys_notification__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-communication/nodes__sys_notification__node_name": "Notificação de sistema", "@sage/xtrem-communication/nodes__sys_notification__property__extraAttributes": "Atributos extra", "@sage/xtrem-communication/nodes__sys_notification__property__locale": "Parametros regionais", "@sage/xtrem-communication/nodes__sys_notification__property__login": "Conexão", "@sage/xtrem-communication/nodes__sys_notification__property__notificationId": "ID da Notificação", "@sage/xtrem-communication/nodes__sys_notification__property__originId": "ID origem", "@sage/xtrem-communication/nodes__sys_notification__property__payload": "Carga útil", "@sage/xtrem-communication/nodes__sys_notification__property__replyId": "ID de resposta", "@sage/xtrem-communication/nodes__sys_notification__property__replyTopic": "Tópico de resposta", "@sage/xtrem-communication/nodes__sys_notification__property__resumeToken": "Retomar token", "@sage/xtrem-communication/nodes__sys_notification__property__status": "Status", "@sage/xtrem-communication/nodes__sys_notification__property__tenantId": "ID do tenant", "@sage/xtrem-communication/nodes__sys_notification__property__topic": "Tópico", "@sage/xtrem-communication/nodes__sys_notification__property__userEmail": "Email do usuário", "@sage/xtrem-communication/nodes__sys_notification_history__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-communication/nodes__sys_notification_history__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-communication/nodes__sys_notification_history__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-communication/nodes__sys_notification_history__node_name": "Histórico de notificação", "@sage/xtrem-communication/nodes__sys_notification_history__property__dateLogged": "Data de criação do registo do histórico de notificação", "@sage/xtrem-communication/nodes__sys_notification_history__property__envelope": "Envelope", "@sage/xtrem-communication/nodes__sys_notification_history__property__errorMessage": "Mensagem erro", "@sage/xtrem-communication/nodes__sys_notification_history__property__errorStack": "<PERSON><PERSON><PERSON>", "@sage/xtrem-communication/nodes__sys_notification_history__property__listener": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-communication/nodes__sys_notification_history__property__locale": "Parametros regionais", "@sage/xtrem-communication/nodes__sys_notification_history__property__notificationContext": "Contexto da notificação", "@sage/xtrem-communication/nodes__sys_notification_history__property__notificationId": "ID da Notificação", "@sage/xtrem-communication/nodes__sys_notification_history__property__originId": "ID origem", "@sage/xtrem-communication/nodes__sys_notification_history__property__replyId": "ID de resposta", "@sage/xtrem-communication/nodes__sys_notification_history__property__replyTopic": "Tópico de resposta", "@sage/xtrem-communication/nodes__sys_notification_history__property__status": "Status", "@sage/xtrem-communication/nodes__sys_notification_history__property__topic": "Tópico", "@sage/xtrem-communication/nodes__sys_notification_history__query__all": "<PERSON><PERSON>", "@sage/xtrem-communication/nodes__sys_notification_history__query__all__failed": "<PERSON><PERSON> fal<PERSON>.", "@sage/xtrem-communication/nodes__sys_notification_log_entry__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-communication/nodes__sys_notification_log_entry__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-communication/nodes__sys_notification_log_entry__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-communication/nodes__sys_notification_log_entry__node_name": "Registo de notificação (log) do sistema", "@sage/xtrem-communication/nodes__sys_notification_log_entry__property__data": "<PERSON><PERSON>", "@sage/xtrem-communication/nodes__sys_notification_log_entry__property__level": "Nível", "@sage/xtrem-communication/nodes__sys_notification_log_entry__property__message": "Mensagem", "@sage/xtrem-communication/nodes__sys_notification_log_entry__property__spanContext": "Contexto do alcance (Span)", "@sage/xtrem-communication/nodes__sys_notification_log_entry__property__sysNotificationState": "Status de notificação do sistema", "@sage/xtrem-communication/nodes__sys_notification_log_entry__property__timestamp": "Data-Hora", "@sage/xtrem-communication/nodes__sys_notification_state__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-communication/nodes__sys_notification_state__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-communication/nodes__sys_notification_state__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-communication/nodes__sys_notification_state__bulkMutation__bulkDelete": "Eliminação em massa (bulk)", "@sage/xtrem-communication/nodes__sys_notification_state__node_name": "Status de notificação do sistema", "@sage/xtrem-communication/nodes__sys_notification_state__property__envelope": "Envelope", "@sage/xtrem-communication/nodes__sys_notification_state__property__isRead": "<PERSON>r", "@sage/xtrem-communication/nodes__sys_notification_state__property__isUserNotificationRequested": "A notificação do utilizador é solicitada", "@sage/xtrem-communication/nodes__sys_notification_state__property__locale": "Parametros regionais (Locale)", "@sage/xtrem-communication/nodes__sys_notification_state__property__logs": "Rastros (logs)", "@sage/xtrem-communication/nodes__sys_notification_state__property__message": "Mensagem", "@sage/xtrem-communication/nodes__sys_notification_state__property__notificationContext": "Contexto da notificação", "@sage/xtrem-communication/nodes__sys_notification_state__property__notificationId": "ID da Notificação", "@sage/xtrem-communication/nodes__sys_notification_state__property__operationName": "Nome da operação", "@sage/xtrem-communication/nodes__sys_notification_state__property__originId": "ID origem", "@sage/xtrem-communication/nodes__sys_notification_state__property__parameterValues": "Valores parâmetros", "@sage/xtrem-communication/nodes__sys_notification_state__property__progress": "Progresso", "@sage/xtrem-communication/nodes__sys_notification_state__property__progressBarPercent": "Percentagem da barra de progresso", "@sage/xtrem-communication/nodes__sys_notification_state__property__replyId": "ID de resposta", "@sage/xtrem-communication/nodes__sys_notification_state__property__result": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-communication/nodes__sys_notification_state__property__status": "Status", "@sage/xtrem-communication/nodes__sys_notification_state__property__timeEnded": "O tempo terminou", "@sage/xtrem-communication/nodes__sys_notification_state__property__timeStarted": "<PERSON>ra <PERSON>", "@sage/xtrem-communication/nodes__sys_notification_state__property__topic": "Tópico", "@sage/xtrem-communication/nodes__sys_notification_state__property__user": "Utilizador", "@sage/xtrem-communication/nodes__sys-notification-state__error-when-data-level-result": "Só se pode definir o resultado dos dados quando o nível é resultado.", "@sage/xtrem-communication/nodes__sys-notification-state__error-when-status-success": "Só se pode definir uma mensagem de erro quando o status é erro.", "@sage/xtrem-communication/nodes__sys-notification-state__status_deletion": "<PERSON><PERSON> <PERSON> possível eliminar um historial se o status for erro, interrompido, nãoResponder, parado ou sucesso.", "@sage/xtrem-communication/package__name": "Comunicação", "@sage/xtrem-communication/pages__sys_notification_history____title": "Histórico de notificação", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__dateLogged": "Enviado", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__errorMessage": "Mensagem", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__listener": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__replyID": "ID de resposta", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__replyTopic": "Tópico de resposta", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__status": "Status", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__topic": "Tópico", "@sage/xtrem-communication/pages__sys_notification_history__history____title": "Resul<PERSON><PERSON>", "@sage/xtrem-communication/pages__sys_notification_history__historyBlock____title": "Sistema", "@sage/xtrem-communication/pages__sys_notification_history__historyCriteriaBlock____title": "Crit<PERSON><PERSON><PERSON>", "@sage/xtrem-communication/pages__sys_notification_history__historyStatus____title": "Status", "@sage/xtrem-communication/pages__sys_notification_history__section____title": "Sistema", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__bulkActions__title": "Eliminar", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__bulkActions__title__2": "<PERSON><PERSON>", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line_4__title": "Tempo finalização", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line_5__title": "Progresso", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line1__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line2__title": "Operação", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line2Right__title": "ID", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line3__title": "<PERSON>ra <PERSON>", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line4__title": "Tempo finalização", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line5__title": "Progresso", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line6__title": "Utilizador", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line7__title": "Fase", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line8__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__message__title": "Mensagem", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__title__title": "Status", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__optionsMenu__title": "<PERSON><PERSON>", "@sage/xtrem-communication/pages__sys_notification_state____objectTypePlural": "Hist<PERSON><PERSON><PERSON> de tarefas em \"Batch\"", "@sage/xtrem-communication/pages__sys_notification_state____objectTypeSingular": "Hist<PERSON><PERSON><PERSON> de tarefas em \"Batch\"", "@sage/xtrem-communication/pages__sys_notification_state____title": "Hist<PERSON><PERSON><PERSON> de tarefas em \"Batch\"", "@sage/xtrem-communication/pages__sys_notification_state__generalSection____title": "G<PERSON>", "@sage/xtrem-communication/pages__sys_notification_state__logs____columns__title__data": "Link", "@sage/xtrem-communication/pages__sys_notification_state__logs____columns__title__level": "Nível", "@sage/xtrem-communication/pages__sys_notification_state__logs____columns__title__message": "Mensagem", "@sage/xtrem-communication/pages__sys_notification_state__logs____columns__title__timestamp": "<PERSON><PERSON>", "@sage/xtrem-communication/pages__sys_notification_state__logs____title": "Rastros (logs)", "@sage/xtrem-communication/pages__sys_notification_state__logsSection____title": "Rastros (logs)", "@sage/xtrem-communication/pages__sys_notification_state__message____title": "Mensagem", "@sage/xtrem-communication/pages__sys_notification_state__notificationId____title": "ID da Notificação", "@sage/xtrem-communication/pages__sys_notification_state__operationName____title": "Operação", "@sage/xtrem-communication/pages__sys_notification_state__originId____title": "ID origem", "@sage/xtrem-communication/pages__sys_notification_state__parameters____columns__title__name": "Nome", "@sage/xtrem-communication/pages__sys_notification_state__parameters____columns__title__value": "Valor", "@sage/xtrem-communication/pages__sys_notification_state__parameters____title": "Parâmetros", "@sage/xtrem-communication/pages__sys_notification_state__progressBarPercent____title": "Progresso", "@sage/xtrem-communication/pages__sys_notification_state__result____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-communication/pages__sys_notification_state__status____title": "Status", "@sage/xtrem-communication/pages__sys_notification_state__timeEnded____title": "Hora fim", "@sage/xtrem-communication/pages__sys_notification_state__timeStarted____title": "Hora iní<PERSON>", "@sage/xtrem-communication/pages__sys_notification_state__track_button_text": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-communication/permission__create__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-communication/permission__delete__name": "Eliminar", "@sage/xtrem-communication/permission__read__name": "<PERSON>r", "@sage/xtrem-communication/permission__update__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-communication/service_options__notification_center__name": "Centro de notificações", "@sage/xtrem-communication/sys__notification_history__search": "Procurar"}