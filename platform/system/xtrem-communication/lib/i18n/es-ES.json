{"@sage/xtrem-communication/activity__sys_notification_history__name": "Historial de notificaciones de sistema", "@sage/xtrem-communication/activity__sys_notification_state__name": "Estado de notificación de sistema", "@sage/xtrem-communication/completed_async_mutation_description": "Finalizado: {{node}}/{{mutation}}", "@sage/xtrem-communication/completed_async_mutation_title": "Mutación asincrónica finalizada", "@sage/xtrem-communication/data_types__communication_notification_data_data_type__name": "Communication notification data data type", "@sage/xtrem-communication/data_types__communication_state_enum__name": "Communication state enum", "@sage/xtrem-communication/data_types__error_message_data_type__name": "Error message data type", "@sage/xtrem-communication/data_types__error_stack_data_type__name": "Error stack data type", "@sage/xtrem-communication/data_types__integration_state_enum__name": "Integration state enum", "@sage/xtrem-communication/data_types__listener_name_data_type__name": "Listener name data type", "@sage/xtrem-communication/data_types__log_level_enum__name": "Log level enum", "@sage/xtrem-communication/data_types__log_message__name": "Mensaje de registro", "@sage/xtrem-communication/data_types__message_data_data_type__name": "Message data data type", "@sage/xtrem-communication/data_types__message_data_type__name": "Message data type", "@sage/xtrem-communication/data_types__notification_status_enum__name": "Notification status enum", "@sage/xtrem-communication/data_types__origin_id_data_type__name": "Origin ID data type", "@sage/xtrem-communication/data_types__request_data_data_type__name": "Request data data type", "@sage/xtrem-communication/data_types__sqs_status_enum__name": "SQS status enum", "@sage/xtrem-communication/enums__communication_state__'error'": "Error", "@sage/xtrem-communication/enums__communication_state__'notSent'": "No enviada", "@sage/xtrem-communication/enums__communication_state__'received'": "Recibida", "@sage/xtrem-communication/enums__communication_state__'retry'": "Reintentar", "@sage/xtrem-communication/enums__communication_state__'sent'": "Enviada", "@sage/xtrem-communication/enums__communication_state__'success'": "Confirmación", "@sage/xtrem-communication/enums__communication_state__error": "Error", "@sage/xtrem-communication/enums__communication_state__interrupted": "Interrumpida", "@sage/xtrem-communication/enums__communication_state__interruptRequested": "Solicitud de interrupción", "@sage/xtrem-communication/enums__communication_state__notSent": "Sin enviar", "@sage/xtrem-communication/enums__communication_state__received": "Recibida", "@sage/xtrem-communication/enums__communication_state__retry": "Reintentar", "@sage/xtrem-communication/enums__communication_state__sent": "Enviada", "@sage/xtrem-communication/enums__communication_state__stopped": "Cancelada", "@sage/xtrem-communication/enums__communication_state__stopRequested": "Solicitud de cancelación", "@sage/xtrem-communication/enums__communication_state__success": "<PERSON><PERSON>rma<PERSON>", "@sage/xtrem-communication/enums__integration_state__'error'": "Error", "@sage/xtrem-communication/enums__integration_state__'not'": "Integración no realizada", "@sage/xtrem-communication/enums__integration_state__'pending'": "Integración en curso", "@sage/xtrem-communication/enums__integration_state__'success'": "Integración realizada", "@sage/xtrem-communication/enums__integration_state__desynchronized": "Desincronizado", "@sage/xtrem-communication/enums__integration_state__error": "Error", "@sage/xtrem-communication/enums__integration_state__not": "Sin integrar", "@sage/xtrem-communication/enums__integration_state__pending": "Pendiente", "@sage/xtrem-communication/enums__integration_state__success": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-communication/enums__log_level__error": "Error", "@sage/xtrem-communication/enums__log_level__exception": "Excepción", "@sage/xtrem-communication/enums__log_level__info": "Información", "@sage/xtrem-communication/enums__log_level__result": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-communication/enums__log_level__test": "Prueba", "@sage/xtrem-communication/enums__log_level__warning": "Aviso", "@sage/xtrem-communication/enums__notification_status__error": "Con errores", "@sage/xtrem-communication/enums__notification_status__interrupted": "Interrumpida", "@sage/xtrem-communication/enums__notification_status__interruptRequested": "Solicitud de interrupción", "@sage/xtrem-communication/enums__notification_status__notResponding": "Sin respuesta", "@sage/xtrem-communication/enums__notification_status__pending": "Pendiente", "@sage/xtrem-communication/enums__notification_status__running": "En curso", "@sage/xtrem-communication/enums__notification_status__stopped": "Cancelada", "@sage/xtrem-communication/enums__notification_status__stopRequested": "Solicitud de cancelación", "@sage/xtrem-communication/enums__notification_status__success": "<PERSON><PERSON>rma<PERSON>", "@sage/xtrem-communication/enums__sqs_status__fail": "Error", "@sage/xtrem-communication/enums__sqs_status__pending": "Pendiente", "@sage/xtrem-communication/interrupt_requested": "Solicitud de interrupción", "@sage/xtrem-communication/menu_item__communication": "Comunicación", "@sage/xtrem-communication/menu_item__scheduler": "Programador", "@sage/xtrem-communication/nodes__sys_dynamic_listener__node_name": "Proceso de escucha dinámico de sistema", "@sage/xtrem-communication/nodes__sys_message__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-communication/nodes__sys_message__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-communication/nodes__sys_message__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-communication/nodes__sys_message__node_name": "Mensaje de siste<PERSON>", "@sage/xtrem-communication/nodes__sys_message__property__attributes": "Atributos", "@sage/xtrem-communication/nodes__sys_message__property__messageId": "<PERSON>d<PERSON> de mensaje", "@sage/xtrem-communication/nodes__sys_message__property__payload": "Payload", "@sage/xtrem-communication/nodes__sys_message__property__queue": "Cola", "@sage/xtrem-communication/nodes__sys_message__property__status": "Estado", "@sage/xtrem-communication/nodes__sys_message__property__tenantId": "Id. de instancia", "@sage/xtrem-communication/nodes__sys_message_history__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-communication/nodes__sys_message_history__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-communication/nodes__sys_message_history__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-communication/nodes__sys_message_history__mutation__purge": "<PERSON><PERSON><PERSON>", "@sage/xtrem-communication/nodes__sys_message_history__mutation__purge__failed": "Error de depuración", "@sage/xtrem-communication/nodes__sys_message_history__mutation__purge__parameter__integrationSolution": "Solución de integración", "@sage/xtrem-communication/nodes__sys_message_history__node_name": "Historial de mensajes de sistema", "@sage/xtrem-communication/nodes__sys_message_history__property__attributes": "Atributos", "@sage/xtrem-communication/nodes__sys_message_history__property__communicationDiagnoses": "Diagnóstico de comunicación", "@sage/xtrem-communication/nodes__sys_message_history__property__context": "Contexto", "@sage/xtrem-communication/nodes__sys_message_history__property__errorMessage": "<PERSON><PERSON><PERSON>", "@sage/xtrem-communication/nodes__sys_message_history__property__errorStack": "<PERSON><PERSON>", "@sage/xtrem-communication/nodes__sys_message_history__property__id": "Id.", "@sage/xtrem-communication/nodes__sys_message_history__property__integrationSolution": "Solución de integración", "@sage/xtrem-communication/nodes__sys_message_history__property__receivedRequest": "Solicitud recibida", "@sage/xtrem-communication/nodes__sys_message_history__property__receivedStamp": "Recepción", "@sage/xtrem-communication/nodes__sys_message_history__property__sendStamp": "Envío", "@sage/xtrem-communication/nodes__sys_message_history__property__sentRequest": "Solicitud enviada", "@sage/xtrem-communication/nodes__sys_message_history__property__status": "Estado", "@sage/xtrem-communication/nodes__sys_message_history__property__user": "Usuario", "@sage/xtrem-communication/nodes__sys_notification__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-communication/nodes__sys_notification__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-communication/nodes__sys_notification__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-communication/nodes__sys_notification__node_name": "Notificación de sistema", "@sage/xtrem-communication/nodes__sys_notification__property__extraAttributes": "Atributos adicionales", "@sage/xtrem-communication/nodes__sys_notification__property__locale": "Parámetros regionales", "@sage/xtrem-communication/nodes__sys_notification__property__login": "Inicio de sesión", "@sage/xtrem-communication/nodes__sys_notification__property__notificationId": "Id. de notificación", "@sage/xtrem-communication/nodes__sys_notification__property__originId": "Id. de origen", "@sage/xtrem-communication/nodes__sys_notification__property__payload": "Payload", "@sage/xtrem-communication/nodes__sys_notification__property__replyId": "Id. de respuesta", "@sage/xtrem-communication/nodes__sys_notification__property__replyTopic": "Tema de respuesta", "@sage/xtrem-communication/nodes__sys_notification__property__resumeToken": "Token de reanudación", "@sage/xtrem-communication/nodes__sys_notification__property__status": "Estado", "@sage/xtrem-communication/nodes__sys_notification__property__tenantId": "Id. de instancia", "@sage/xtrem-communication/nodes__sys_notification__property__topic": "<PERSON><PERSON>", "@sage/xtrem-communication/nodes__sys_notification__property__userEmail": "E-mail de usuario", "@sage/xtrem-communication/nodes__sys_notification_history__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-communication/nodes__sys_notification_history__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-communication/nodes__sys_notification_history__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-communication/nodes__sys_notification_history__node_name": "Historial de notificaciones de sistema", "@sage/xtrem-communication/nodes__sys_notification_history__property__dateLogged": "Fecha de creación de registro de historial de notificaciones", "@sage/xtrem-communication/nodes__sys_notification_history__property__envelope": "Sobre", "@sage/xtrem-communication/nodes__sys_notification_history__property__errorMessage": "<PERSON><PERSON><PERSON>", "@sage/xtrem-communication/nodes__sys_notification_history__property__errorStack": "<PERSON><PERSON>", "@sage/xtrem-communication/nodes__sys_notification_history__property__listener": "Proceso de escucha", "@sage/xtrem-communication/nodes__sys_notification_history__property__locale": "Parámetros regionales", "@sage/xtrem-communication/nodes__sys_notification_history__property__notificationContext": "Contexto de notificación", "@sage/xtrem-communication/nodes__sys_notification_history__property__notificationId": "Id. de notificación", "@sage/xtrem-communication/nodes__sys_notification_history__property__originId": "Id. de origen", "@sage/xtrem-communication/nodes__sys_notification_history__property__replyId": "Id. de respuesta", "@sage/xtrem-communication/nodes__sys_notification_history__property__replyTopic": "Tema de respuesta", "@sage/xtrem-communication/nodes__sys_notification_history__property__status": "Estado", "@sage/xtrem-communication/nodes__sys_notification_history__property__topic": "<PERSON><PERSON>", "@sage/xtrem-communication/nodes__sys_notification_history__query__all": "<PERSON><PERSON>", "@sage/xtrem-communication/nodes__sys_notification_history__query__all__failed": "<PERSON>rror al consultar todas", "@sage/xtrem-communication/nodes__sys_notification_log_entry__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-communication/nodes__sys_notification_log_entry__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-communication/nodes__sys_notification_log_entry__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-communication/nodes__sys_notification_log_entry__node_name": "Entrada de traza de notificación de sistema", "@sage/xtrem-communication/nodes__sys_notification_log_entry__property__data": "Datos", "@sage/xtrem-communication/nodes__sys_notification_log_entry__property__level": "<PERSON><PERSON>", "@sage/xtrem-communication/nodes__sys_notification_log_entry__property__message": "Men<PERSON><PERSON>", "@sage/xtrem-communication/nodes__sys_notification_log_entry__property__spanContext": "Contexto de intervalo", "@sage/xtrem-communication/nodes__sys_notification_log_entry__property__sysNotificationState": "Estado de notificación de sistema", "@sage/xtrem-communication/nodes__sys_notification_log_entry__property__timestamp": "<PERSON><PERSON>", "@sage/xtrem-communication/nodes__sys_notification_state__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-communication/nodes__sys_notification_state__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-communication/nodes__sys_notification_state__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-communication/nodes__sys_notification_state__bulkMutation__bulkDelete": "Eliminar en masa", "@sage/xtrem-communication/nodes__sys_notification_state__node_name": "Estado de notificación de sistema", "@sage/xtrem-communication/nodes__sys_notification_state__property__envelope": "Sobre", "@sage/xtrem-communication/nodes__sys_notification_state__property__isRead": "<PERSON><PERSON><PERSON>", "@sage/xtrem-communication/nodes__sys_notification_state__property__isUserNotificationRequested": "Solicitada", "@sage/xtrem-communication/nodes__sys_notification_state__property__locale": "Parámetros regionales", "@sage/xtrem-communication/nodes__sys_notification_state__property__logs": "<PERSON><PERSON><PERSON>", "@sage/xtrem-communication/nodes__sys_notification_state__property__message": "Men<PERSON><PERSON>", "@sage/xtrem-communication/nodes__sys_notification_state__property__notificationContext": "Contexto de notificación", "@sage/xtrem-communication/nodes__sys_notification_state__property__notificationId": "Id. de notificación", "@sage/xtrem-communication/nodes__sys_notification_state__property__operationName": "Nombre de operación", "@sage/xtrem-communication/nodes__sys_notification_state__property__originId": "Id. de origen", "@sage/xtrem-communication/nodes__sys_notification_state__property__parameterValues": "Valores de parámetro", "@sage/xtrem-communication/nodes__sys_notification_state__property__progress": "Progreso", "@sage/xtrem-communication/nodes__sys_notification_state__property__progressBarPercent": "Porcentaje de barra de progreso", "@sage/xtrem-communication/nodes__sys_notification_state__property__replyId": "Id. de respuesta", "@sage/xtrem-communication/nodes__sys_notification_state__property__result": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-communication/nodes__sys_notification_state__property__status": "Estado", "@sage/xtrem-communication/nodes__sys_notification_state__property__timeEnded": "Hora de fin", "@sage/xtrem-communication/nodes__sys_notification_state__property__timeStarted": "Hora de inicio", "@sage/xtrem-communication/nodes__sys_notification_state__property__topic": "<PERSON><PERSON>", "@sage/xtrem-communication/nodes__sys_notification_state__property__user": "Usuario", "@sage/xtrem-communication/nodes__sys-notification-state__error-when-data-level-result": "Solo puedes definir resultados de datos si el nivel es de resultado.", "@sage/xtrem-communication/nodes__sys-notification-state__error-when-status-success": "Solo puedes definir un mensaje de error si la notificación contiene errores.", "@sage/xtrem-communication/nodes__sys-notification-state__status_deletion": "Solo puedes eliminar un historial si su estado es \"Error\", \"Interrumpida\", \"Sin respuesta\", \"Cancelada\" o \"Confirmada\".", "@sage/xtrem-communication/package__name": "Comunicación", "@sage/xtrem-communication/pages__sys_notification_history____title": "Historial de notificaciones", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__dateLogged": "Envío", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__errorMessage": "Men<PERSON><PERSON>", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__listener": "Listener", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__replyID": "Id. de respuesta", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__replyTopic": "Tema de respuesta", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__status": "Estado", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__topic": "<PERSON><PERSON>", "@sage/xtrem-communication/pages__sys_notification_history__history____title": "Resul<PERSON><PERSON>", "@sage/xtrem-communication/pages__sys_notification_history__historyBlock____title": "Sistema", "@sage/xtrem-communication/pages__sys_notification_history__historyCriteriaBlock____title": "Criterios", "@sage/xtrem-communication/pages__sys_notification_history__historyStatus____title": "Estado", "@sage/xtrem-communication/pages__sys_notification_history__section____title": "Sistema", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__bulkActions__title": "Eliminar", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__bulkActions__title__2": "<PERSON><PERSON><PERSON>", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line_4__title": "Hora de fin", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line_5__title": "Progreso", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line1__title": "Programación", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line2__title": "Operación", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line2Right__title": "Id.", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line3__title": "Hora de inicio", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line4__title": "Hora de fin", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line5__title": "Progreso", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line6__title": "Usuario", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line7__title": "Fase", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line8__title": "Detalles", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__message__title": "Men<PERSON><PERSON>", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__title__title": "Estado", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__optionsMenu__title": "<PERSON><PERSON>", "@sage/xtrem-communication/pages__sys_notification_state____objectTypePlural": "Historial de tareas por lotes", "@sage/xtrem-communication/pages__sys_notification_state____objectTypeSingular": "Historial de tareas por lotes", "@sage/xtrem-communication/pages__sys_notification_state____title": "Historial de tareas por lotes", "@sage/xtrem-communication/pages__sys_notification_state__generalSection____title": "General", "@sage/xtrem-communication/pages__sys_notification_state__logs____columns__title__data": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-communication/pages__sys_notification_state__logs____columns__title__level": "<PERSON><PERSON>", "@sage/xtrem-communication/pages__sys_notification_state__logs____columns__title__message": "Men<PERSON><PERSON>", "@sage/xtrem-communication/pages__sys_notification_state__logs____columns__title__timestamp": "<PERSON><PERSON>", "@sage/xtrem-communication/pages__sys_notification_state__logs____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-communication/pages__sys_notification_state__logsSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-communication/pages__sys_notification_state__message____title": "Men<PERSON><PERSON>", "@sage/xtrem-communication/pages__sys_notification_state__notificationId____title": "Id. de notificación", "@sage/xtrem-communication/pages__sys_notification_state__operationName____title": "Operación", "@sage/xtrem-communication/pages__sys_notification_state__originId____title": "Id. de origen", "@sage/xtrem-communication/pages__sys_notification_state__parameters____columns__title__name": "Nombre", "@sage/xtrem-communication/pages__sys_notification_state__parameters____columns__title__value": "Valor", "@sage/xtrem-communication/pages__sys_notification_state__parameters____title": "Parámetros", "@sage/xtrem-communication/pages__sys_notification_state__progressBarPercent____title": "Progreso", "@sage/xtrem-communication/pages__sys_notification_state__result____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-communication/pages__sys_notification_state__status____title": "Estado", "@sage/xtrem-communication/pages__sys_notification_state__timeEnded____title": "Hora de fin", "@sage/xtrem-communication/pages__sys_notification_state__timeStarted____title": "Hora de inicio", "@sage/xtrem-communication/pages__sys_notification_state__track_button_text": "<PERSON><PERSON><PERSON>", "@sage/xtrem-communication/permission__create__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-communication/permission__delete__name": "Eliminar", "@sage/xtrem-communication/permission__read__name": "<PERSON><PERSON>", "@sage/xtrem-communication/permission__update__name": "Actualizar", "@sage/xtrem-communication/service_options__notification_center__name": "Centro de notificaciones", "@sage/xtrem-communication/sys__notification_history__search": "Buscar"}