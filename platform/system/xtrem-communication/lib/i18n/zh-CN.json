{"@sage/xtrem-communication/activity__sys_notification_history__name": "系统通知历史", "@sage/xtrem-communication/activity__sys_notification_state__name": "系统通知状态", "@sage/xtrem-communication/completed_async_mutation_description": "已完成：{{node}}/{{mutation}}", "@sage/xtrem-communication/completed_async_mutation_title": "同步变更已完成。", "@sage/xtrem-communication/data_types__communication_notification_data_data_type__name": "通讯通知数据的数据类型", "@sage/xtrem-communication/data_types__communication_state_enum__name": "通讯状态枚举", "@sage/xtrem-communication/data_types__error_message_data_type__name": "错误消息数据类型", "@sage/xtrem-communication/data_types__error_stack_data_type__name": "错误堆栈数据类型", "@sage/xtrem-communication/data_types__integration_state_enum__name": "集成状态枚举", "@sage/xtrem-communication/data_types__listener_name_data_type__name": "监听器名称数据类型", "@sage/xtrem-communication/data_types__log_level_enum__name": "日志级别枚举", "@sage/xtrem-communication/data_types__log_message__name": "日志消息", "@sage/xtrem-communication/data_types__message_data_data_type__name": "消息数据的数据类型", "@sage/xtrem-communication/data_types__message_data_type__name": "消息数据类型", "@sage/xtrem-communication/data_types__notification_status_enum__name": "通知状态枚举", "@sage/xtrem-communication/data_types__origin_id_data_type__name": "原始ID数据类型", "@sage/xtrem-communication/data_types__request_data_data_type__name": "请求数据的数据类型", "@sage/xtrem-communication/data_types__sqs_status_enum__name": "SQS状态枚举", "@sage/xtrem-communication/enums__communication_state__'error'": "错误", "@sage/xtrem-communication/enums__communication_state__'notSent'": "未发送", "@sage/xtrem-communication/enums__communication_state__'received'": "已接收", "@sage/xtrem-communication/enums__communication_state__'retry'": "重试", "@sage/xtrem-communication/enums__communication_state__'sent'": "已发送", "@sage/xtrem-communication/enums__communication_state__'success'": "成功", "@sage/xtrem-communication/enums__communication_state__error": "错误", "@sage/xtrem-communication/enums__communication_state__interrupted": "已中断", "@sage/xtrem-communication/enums__communication_state__interruptRequested": "中断请求", "@sage/xtrem-communication/enums__communication_state__notSent": "未发送", "@sage/xtrem-communication/enums__communication_state__received": "已接收", "@sage/xtrem-communication/enums__communication_state__retry": "重试", "@sage/xtrem-communication/enums__communication_state__sent": "已发送", "@sage/xtrem-communication/enums__communication_state__stopped": "已终止", "@sage/xtrem-communication/enums__communication_state__stopRequested": "终止请求", "@sage/xtrem-communication/enums__communication_state__success": "成功", "@sage/xtrem-communication/enums__integration_state__'error'": "错误", "@sage/xtrem-communication/enums__integration_state__'not'": "未集成", "@sage/xtrem-communication/enums__integration_state__'pending'": "集成中", "@sage/xtrem-communication/enums__integration_state__'success'": "已集成", "@sage/xtrem-communication/enums__integration_state__desynchronized": "已取消同步", "@sage/xtrem-communication/enums__integration_state__error": "错误", "@sage/xtrem-communication/enums__integration_state__not": "未集成", "@sage/xtrem-communication/enums__integration_state__pending": "待处理", "@sage/xtrem-communication/enums__integration_state__success": "成功", "@sage/xtrem-communication/enums__log_level__error": "错误", "@sage/xtrem-communication/enums__log_level__exception": "异常", "@sage/xtrem-communication/enums__log_level__info": "信息", "@sage/xtrem-communication/enums__log_level__result": "结果", "@sage/xtrem-communication/enums__log_level__test": "测试", "@sage/xtrem-communication/enums__log_level__warning": "警告", "@sage/xtrem-communication/enums__notification_status__error": "错误", "@sage/xtrem-communication/enums__notification_status__interrupted": "已中断", "@sage/xtrem-communication/enums__notification_status__interruptRequested": "中断请求", "@sage/xtrem-communication/enums__notification_status__notResponding": "无响应", "@sage/xtrem-communication/enums__notification_status__pending": "待处理", "@sage/xtrem-communication/enums__notification_status__running": "运行中", "@sage/xtrem-communication/enums__notification_status__stopped": "已终止", "@sage/xtrem-communication/enums__notification_status__stopRequested": "终止请求", "@sage/xtrem-communication/enums__notification_status__success": "成功", "@sage/xtrem-communication/enums__sqs_status__fail": "错误", "@sage/xtrem-communication/enums__sqs_status__pending": "待处理", "@sage/xtrem-communication/interrupt_requested": "中断请求", "@sage/xtrem-communication/menu_item__communication": "通讯", "@sage/xtrem-communication/menu_item__scheduler": "排程程序", "@sage/xtrem-communication/nodes__sys_dynamic_listener__node_name": "系统动态监听器", "@sage/xtrem-communication/nodes__sys_message__asyncMutation__asyncExport": "导出", "@sage/xtrem-communication/nodes__sys_message__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-communication/nodes__sys_message__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-communication/nodes__sys_message__node_name": "系统消息", "@sage/xtrem-communication/nodes__sys_message__property__attributes": "属性", "@sage/xtrem-communication/nodes__sys_message__property__messageId": "消息ID", "@sage/xtrem-communication/nodes__sys_message__property__payload": "有效负荷", "@sage/xtrem-communication/nodes__sys_message__property__queue": "队列", "@sage/xtrem-communication/nodes__sys_message__property__status": "状态", "@sage/xtrem-communication/nodes__sys_message__property__tenantId": "租户ID", "@sage/xtrem-communication/nodes__sys_message_history__asyncMutation__asyncExport": "导出", "@sage/xtrem-communication/nodes__sys_message_history__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-communication/nodes__sys_message_history__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-communication/nodes__sys_message_history__mutation__purge": "清除", "@sage/xtrem-communication/nodes__sys_message_history__mutation__purge__failed": "清除失败。", "@sage/xtrem-communication/nodes__sys_message_history__mutation__purge__parameter__integrationSolution": "整体解决方案", "@sage/xtrem-communication/nodes__sys_message_history__node_name": "系统消息历史", "@sage/xtrem-communication/nodes__sys_message_history__property__attributes": "属性", "@sage/xtrem-communication/nodes__sys_message_history__property__communicationDiagnoses": "通讯诊断", "@sage/xtrem-communication/nodes__sys_message_history__property__context": "关联背景", "@sage/xtrem-communication/nodes__sys_message_history__property__errorMessage": "报错消息", "@sage/xtrem-communication/nodes__sys_message_history__property__errorStack": "错误堆栈", "@sage/xtrem-communication/nodes__sys_message_history__property__id": "ID", "@sage/xtrem-communication/nodes__sys_message_history__property__integrationSolution": "整体解决方案", "@sage/xtrem-communication/nodes__sys_message_history__property__receivedRequest": "已接收请求", "@sage/xtrem-communication/nodes__sys_message_history__property__receivedStamp": "已接收时间标记", "@sage/xtrem-communication/nodes__sys_message_history__property__sendStamp": "发送时间标记", "@sage/xtrem-communication/nodes__sys_message_history__property__sentRequest": "发送请求", "@sage/xtrem-communication/nodes__sys_message_history__property__status": "状态", "@sage/xtrem-communication/nodes__sys_message_history__property__user": "用户", "@sage/xtrem-communication/nodes__sys_notification__asyncMutation__asyncExport": "导出", "@sage/xtrem-communication/nodes__sys_notification__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-communication/nodes__sys_notification__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-communication/nodes__sys_notification__node_name": "系统通知", "@sage/xtrem-communication/nodes__sys_notification__property__extraAttributes": "附加属性", "@sage/xtrem-communication/nodes__sys_notification__property__locale": "区域设置", "@sage/xtrem-communication/nodes__sys_notification__property__login": "登录", "@sage/xtrem-communication/nodes__sys_notification__property__notificationId": "通知ID", "@sage/xtrem-communication/nodes__sys_notification__property__originId": "原始ID", "@sage/xtrem-communication/nodes__sys_notification__property__payload": "有效负荷", "@sage/xtrem-communication/nodes__sys_notification__property__replyId": "回复ID", "@sage/xtrem-communication/nodes__sys_notification__property__replyTopic": "回复主题", "@sage/xtrem-communication/nodes__sys_notification__property__resumeToken": "恢复令牌", "@sage/xtrem-communication/nodes__sys_notification__property__status": "状态", "@sage/xtrem-communication/nodes__sys_notification__property__tenantId": "租户ID", "@sage/xtrem-communication/nodes__sys_notification__property__topic": "主题", "@sage/xtrem-communication/nodes__sys_notification__property__userEmail": "用户email", "@sage/xtrem-communication/nodes__sys_notification_history__asyncMutation__asyncExport": "导出", "@sage/xtrem-communication/nodes__sys_notification_history__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-communication/nodes__sys_notification_history__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-communication/nodes__sys_notification_history__node_name": "系统通知历史", "@sage/xtrem-communication/nodes__sys_notification_history__property__dateLogged": "已创建日期通知历史记录", "@sage/xtrem-communication/nodes__sys_notification_history__property__envelope": "拨款", "@sage/xtrem-communication/nodes__sys_notification_history__property__errorMessage": "报错消息", "@sage/xtrem-communication/nodes__sys_notification_history__property__errorStack": "错误堆栈", "@sage/xtrem-communication/nodes__sys_notification_history__property__listener": "监听器", "@sage/xtrem-communication/nodes__sys_notification_history__property__locale": "区域设置", "@sage/xtrem-communication/nodes__sys_notification_history__property__notificationContext": "通知关联背景", "@sage/xtrem-communication/nodes__sys_notification_history__property__notificationId": "通知ID", "@sage/xtrem-communication/nodes__sys_notification_history__property__originId": "原始ID", "@sage/xtrem-communication/nodes__sys_notification_history__property__replyId": "回复ID", "@sage/xtrem-communication/nodes__sys_notification_history__property__replyTopic": "回复主题", "@sage/xtrem-communication/nodes__sys_notification_history__property__status": "状态", "@sage/xtrem-communication/nodes__sys_notification_history__property__topic": "主题", "@sage/xtrem-communication/nodes__sys_notification_history__query__all": "全部", "@sage/xtrem-communication/nodes__sys_notification_history__query__all__failed": "全部失败。", "@sage/xtrem-communication/nodes__sys_notification_log_entry__asyncMutation__asyncExport": "导出", "@sage/xtrem-communication/nodes__sys_notification_log_entry__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-communication/nodes__sys_notification_log_entry__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-communication/nodes__sys_notification_log_entry__node_name": "系统通知日志录入", "@sage/xtrem-communication/nodes__sys_notification_log_entry__property__data": "数据", "@sage/xtrem-communication/nodes__sys_notification_log_entry__property__level": "层级", "@sage/xtrem-communication/nodes__sys_notification_log_entry__property__message": "消息", "@sage/xtrem-communication/nodes__sys_notification_log_entry__property__spanContext": "跨度关联背景", "@sage/xtrem-communication/nodes__sys_notification_log_entry__property__sysNotificationState": "系统通知状态", "@sage/xtrem-communication/nodes__sys_notification_log_entry__property__timestamp": "时间标记", "@sage/xtrem-communication/nodes__sys_notification_state__asyncMutation__asyncExport": "导出", "@sage/xtrem-communication/nodes__sys_notification_state__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-communication/nodes__sys_notification_state__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-communication/nodes__sys_notification_state__bulkMutation__bulkDelete": "批量删除", "@sage/xtrem-communication/nodes__sys_notification_state__node_name": "系统通知状态", "@sage/xtrem-communication/nodes__sys_notification_state__property__envelope": "信封", "@sage/xtrem-communication/nodes__sys_notification_state__property__isRead": "读取", "@sage/xtrem-communication/nodes__sys_notification_state__property__isUserNotificationRequested": "是否请求用户通知", "@sage/xtrem-communication/nodes__sys_notification_state__property__locale": "区域设置", "@sage/xtrem-communication/nodes__sys_notification_state__property__logs": "日志", "@sage/xtrem-communication/nodes__sys_notification_state__property__message": "消息", "@sage/xtrem-communication/nodes__sys_notification_state__property__notificationContext": "通知关联背景", "@sage/xtrem-communication/nodes__sys_notification_state__property__notificationId": "通知ID", "@sage/xtrem-communication/nodes__sys_notification_state__property__operationName": "工序名称", "@sage/xtrem-communication/nodes__sys_notification_state__property__originId": "原始ID", "@sage/xtrem-communication/nodes__sys_notification_state__property__parameterValues": "参数值", "@sage/xtrem-communication/nodes__sys_notification_state__property__progress": "进程", "@sage/xtrem-communication/nodes__sys_notification_state__property__progressBarPercent": "进度条百分比", "@sage/xtrem-communication/nodes__sys_notification_state__property__replyId": "回复ID", "@sage/xtrem-communication/nodes__sys_notification_state__property__result": "结果", "@sage/xtrem-communication/nodes__sys_notification_state__property__status": "状态", "@sage/xtrem-communication/nodes__sys_notification_state__property__timeEnded": "时间截止", "@sage/xtrem-communication/nodes__sys_notification_state__property__timeStarted": "时间开始", "@sage/xtrem-communication/nodes__sys_notification_state__property__topic": "主题", "@sage/xtrem-communication/nodes__sys_notification_state__property__user": "用户", "@sage/xtrem-communication/nodes__sys-notification-state__error-when-data-level-result": "只有此层级为结果时，您才可以设置数据结果。", "@sage/xtrem-communication/nodes__sys-notification-state__error-when-status-success": "只有当此状态错误时，您才可以设置报错消息。", "@sage/xtrem-communication/nodes__sys-notification-state__status_deletion": "只有状态为错误、中断、无响应、停止或成功时，您才能删除历史记录。", "@sage/xtrem-communication/package__name": "通讯", "@sage/xtrem-communication/pages__sys_notification_history____title": "通知历史", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__dateLogged": "已发送", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__errorMessage": "消息", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__listener": "监听者", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__replyID": "回复ID", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__replyTopic": "回复主题", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__status": "状态", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__topic": "主题", "@sage/xtrem-communication/pages__sys_notification_history__history____title": "结果", "@sage/xtrem-communication/pages__sys_notification_history__historyBlock____title": "系统", "@sage/xtrem-communication/pages__sys_notification_history__historyCriteriaBlock____title": "标准", "@sage/xtrem-communication/pages__sys_notification_history__historyStatus____title": "状态", "@sage/xtrem-communication/pages__sys_notification_history__section____title": "系统", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__bulkActions__title": "删除", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__bulkActions__title__2": "停止", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line_4__title": "时间结束", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line_5__title": "进程", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line1__title": "排程", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line2__title": "工序", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line2Right__title": "ID", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line3__title": "时间开始", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line4__title": "时间结束", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line5__title": "进程", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line6__title": "用户", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line7__title": "阶段", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line8__title": "明细", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__message__title": "消息", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__title__title": "状态", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__optionsMenu__title": "全部", "@sage/xtrem-communication/pages__sys_notification_state____objectTypePlural": "批次任务历史", "@sage/xtrem-communication/pages__sys_notification_state____objectTypeSingular": "批次任务历史", "@sage/xtrem-communication/pages__sys_notification_state____title": "批次任务历史", "@sage/xtrem-communication/pages__sys_notification_state__generalSection____title": "常规", "@sage/xtrem-communication/pages__sys_notification_state__logs____columns__title__data": "链接", "@sage/xtrem-communication/pages__sys_notification_state__logs____columns__title__level": "层级", "@sage/xtrem-communication/pages__sys_notification_state__logs____columns__title__message": "消息", "@sage/xtrem-communication/pages__sys_notification_state__logs____columns__title__timestamp": "时间", "@sage/xtrem-communication/pages__sys_notification_state__logs____title": "日志", "@sage/xtrem-communication/pages__sys_notification_state__logsSection____title": "日志", "@sage/xtrem-communication/pages__sys_notification_state__message____title": "消息", "@sage/xtrem-communication/pages__sys_notification_state__notificationId____title": "通知ID", "@sage/xtrem-communication/pages__sys_notification_state__operationName____title": "工序", "@sage/xtrem-communication/pages__sys_notification_state__originId____title": "原始ID", "@sage/xtrem-communication/pages__sys_notification_state__parameters____columns__title__name": "名称", "@sage/xtrem-communication/pages__sys_notification_state__parameters____columns__title__value": "值", "@sage/xtrem-communication/pages__sys_notification_state__parameters____title": "参数", "@sage/xtrem-communication/pages__sys_notification_state__progressBarPercent____title": "进程", "@sage/xtrem-communication/pages__sys_notification_state__result____title": "结果", "@sage/xtrem-communication/pages__sys_notification_state__status____title": "状态", "@sage/xtrem-communication/pages__sys_notification_state__timeEnded____title": "结束时间", "@sage/xtrem-communication/pages__sys_notification_state__timeStarted____title": "开始时间", "@sage/xtrem-communication/pages__sys_notification_state__track_button_text": "追踪", "@sage/xtrem-communication/permission__create__name": "创建", "@sage/xtrem-communication/permission__delete__name": "删除", "@sage/xtrem-communication/permission__read__name": "读取", "@sage/xtrem-communication/permission__update__name": "更新", "@sage/xtrem-communication/service_options__notification_center__name": "通知中心", "@sage/xtrem-communication/sys__notification_history__search": "搜索"}