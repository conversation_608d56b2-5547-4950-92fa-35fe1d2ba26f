{"@sage/xtrem-communication/activity__sys_notification_history__name": "Historique de notification système", "@sage/xtrem-communication/activity__sys_notification_state__name": "État de notification système", "@sage/xtrem-communication/completed_async_mutation_description": "Réalisée : {{node}} / {{mutation}}", "@sage/xtrem-communication/completed_async_mutation_title": "Mutation async réalisée.", "@sage/xtrem-communication/data_types__communication_notification_data_data_type__name": "Type de données des données de notification de communication", "@sage/xtrem-communication/data_types__communication_state_enum__name": "Enum du statut de communication", "@sage/xtrem-communication/data_types__error_message_data_type__name": "Type de données message d'erreur", "@sage/xtrem-communication/data_types__error_stack_data_type__name": "Type de données pile d'erreurs", "@sage/xtrem-communication/data_types__integration_state_enum__name": "Enum du statut d'intégration", "@sage/xtrem-communication/data_types__listener_name_data_type__name": "Type de données nom listener", "@sage/xtrem-communication/data_types__log_level_enum__name": "Enum niveau trace", "@sage/xtrem-communication/data_types__log_message__name": "Message trace", "@sage/xtrem-communication/data_types__message_data_data_type__name": "Type de données données message", "@sage/xtrem-communication/data_types__message_data_type__name": "Type de données message", "@sage/xtrem-communication/data_types__notification_status_enum__name": "Enum du statut de notification", "@sage/xtrem-communication/data_types__origin_id_data_type__name": "Type de données ID origine", "@sage/xtrem-communication/data_types__request_data_data_type__name": "Type de données données demande", "@sage/xtrem-communication/data_types__sqs_status_enum__name": "Enum du statut SQS", "@sage/xtrem-communication/enums__communication_state__'error'": "<PERSON><PERSON><PERSON>", "@sage/xtrem-communication/enums__communication_state__'notSent'": "Non défini", "@sage/xtrem-communication/enums__communication_state__'received'": "<PERSON><PERSON><PERSON>", "@sage/xtrem-communication/enums__communication_state__'retry'": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-communication/enums__communication_state__'sent'": "<PERSON><PERSON><PERSON>", "@sage/xtrem-communication/enums__communication_state__'success'": "Su<PERSON>ès", "@sage/xtrem-communication/enums__communication_state__error": "<PERSON><PERSON><PERSON>", "@sage/xtrem-communication/enums__communication_state__interrupted": "Interrompu", "@sage/xtrem-communication/enums__communication_state__interruptRequested": "Interruption requise", "@sage/xtrem-communication/enums__communication_state__notSent": "Non envoyé", "@sage/xtrem-communication/enums__communication_state__received": "<PERSON><PERSON><PERSON>", "@sage/xtrem-communication/enums__communication_state__retry": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-communication/enums__communication_state__sent": "<PERSON><PERSON><PERSON>", "@sage/xtrem-communication/enums__communication_state__stopped": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-communication/enums__communication_state__stopRequested": "<PERSON><PERSON><PERSON><PERSON> requis", "@sage/xtrem-communication/enums__communication_state__success": "Su<PERSON>ès", "@sage/xtrem-communication/enums__integration_state__'error'": "<PERSON><PERSON><PERSON>", "@sage/xtrem-communication/enums__integration_state__'not'": "Non intégrée", "@sage/xtrem-communication/enums__integration_state__'pending'": "En cours d'intégration", "@sage/xtrem-communication/enums__integration_state__'success'": "Intégrée", "@sage/xtrem-communication/enums__integration_state__desynchronized": "Désynchronisé", "@sage/xtrem-communication/enums__integration_state__error": "<PERSON><PERSON><PERSON>", "@sage/xtrem-communication/enums__integration_state__not": "Non intégré", "@sage/xtrem-communication/enums__integration_state__pending": "En attente", "@sage/xtrem-communication/enums__integration_state__success": "Su<PERSON>ès", "@sage/xtrem-communication/enums__log_level__error": "<PERSON><PERSON><PERSON>", "@sage/xtrem-communication/enums__log_level__exception": "Exception", "@sage/xtrem-communication/enums__log_level__info": "Infos", "@sage/xtrem-communication/enums__log_level__result": "Résultat", "@sage/xtrem-communication/enums__log_level__test": "Test", "@sage/xtrem-communication/enums__log_level__warning": "Avertissement", "@sage/xtrem-communication/enums__notification_status__error": "<PERSON><PERSON><PERSON>", "@sage/xtrem-communication/enums__notification_status__interrupted": "Interrompue", "@sage/xtrem-communication/enums__notification_status__interruptRequested": "Interruption demandée", "@sage/xtrem-communication/enums__notification_status__notResponding": "Pas de réponse", "@sage/xtrem-communication/enums__notification_status__pending": "En attente", "@sage/xtrem-communication/enums__notification_status__running": "En cours", "@sage/xtrem-communication/enums__notification_status__stopped": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-communication/enums__notification_status__stopRequested": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-communication/enums__notification_status__success": "Su<PERSON>ès", "@sage/xtrem-communication/enums__sqs_status__fail": "<PERSON><PERSON><PERSON>", "@sage/xtrem-communication/enums__sqs_status__pending": "En attente", "@sage/xtrem-communication/interrupt_requested": "Interruption requise", "@sage/xtrem-communication/menu_item__communication": "Communication", "@sage/xtrem-communication/menu_item__scheduler": "Planificateur", "@sage/xtrem-communication/nodes__sys_dynamic_listener__node_name": "Listener dynamique système", "@sage/xtrem-communication/nodes__sys_message__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-communication/nodes__sys_message__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-communication/nodes__sys_message__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-communication/nodes__sys_message__node_name": "Message système", "@sage/xtrem-communication/nodes__sys_message__property__attributes": "Attributs", "@sage/xtrem-communication/nodes__sys_message__property__messageId": "Code message", "@sage/xtrem-communication/nodes__sys_message__property__payload": "Charge", "@sage/xtrem-communication/nodes__sys_message__property__queue": "Queue", "@sage/xtrem-communication/nodes__sys_message__property__status": "Statut", "@sage/xtrem-communication/nodes__sys_message__property__tenantId": "Code tenant", "@sage/xtrem-communication/nodes__sys_message_history__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-communication/nodes__sys_message_history__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-communication/nodes__sys_message_history__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-communication/nodes__sys_message_history__mutation__purge": "Epuration", "@sage/xtrem-communication/nodes__sys_message_history__mutation__purge__failed": "La purge a échoué.", "@sage/xtrem-communication/nodes__sys_message_history__mutation__purge__parameter__integrationSolution": "Solution d'intégration", "@sage/xtrem-communication/nodes__sys_message_history__node_name": "Historique des messages système", "@sage/xtrem-communication/nodes__sys_message_history__property__attributes": "Attributs", "@sage/xtrem-communication/nodes__sys_message_history__property__communicationDiagnoses": "Diagnostics de communication", "@sage/xtrem-communication/nodes__sys_message_history__property__context": "Contexte", "@sage/xtrem-communication/nodes__sys_message_history__property__errorMessage": "Message d'erreur", "@sage/xtrem-communication/nodes__sys_message_history__property__errorStack": "<PERSON><PERSON> d'er<PERSON>", "@sage/xtrem-communication/nodes__sys_message_history__property__id": "Code", "@sage/xtrem-communication/nodes__sys_message_history__property__integrationSolution": "Solution d'intégration", "@sage/xtrem-communication/nodes__sys_message_history__property__receivedRequest": "<PERSON><PERSON><PERSON> re<PERSON>", "@sage/xtrem-communication/nodes__sys_message_history__property__receivedStamp": "Horodatage réception", "@sage/xtrem-communication/nodes__sys_message_history__property__sendStamp": "Horodatage de l'envoi", "@sage/xtrem-communication/nodes__sys_message_history__property__sentRequest": "<PERSON><PERSON><PERSON> envoy<PERSON>", "@sage/xtrem-communication/nodes__sys_message_history__property__status": "Statut", "@sage/xtrem-communication/nodes__sys_message_history__property__user": "Utilisa<PERSON>ur", "@sage/xtrem-communication/nodes__sys_notification__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-communication/nodes__sys_notification__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-communication/nodes__sys_notification__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-communication/nodes__sys_notification__node_name": "Notification système", "@sage/xtrem-communication/nodes__sys_notification__property__extraAttributes": "Attributs supplémentaires", "@sage/xtrem-communication/nodes__sys_notification__property__locale": "Paramètres régionaux", "@sage/xtrem-communication/nodes__sys_notification__property__login": "Identifiant de connexion", "@sage/xtrem-communication/nodes__sys_notification__property__notificationId": "Code de notification", "@sage/xtrem-communication/nodes__sys_notification__property__originId": "Code origine", "@sage/xtrem-communication/nodes__sys_notification__property__payload": "Charge", "@sage/xtrem-communication/nodes__sys_notification__property__replyId": "Code réponse", "@sage/xtrem-communication/nodes__sys_notification__property__replyTopic": "Sujet de réponse", "@sage/xtrem-communication/nodes__sys_notification__property__resumeToken": "<PERSON><PERSON><PERSON> jeton", "@sage/xtrem-communication/nodes__sys_notification__property__status": "Statut", "@sage/xtrem-communication/nodes__sys_notification__property__tenantId": "Code tenant", "@sage/xtrem-communication/nodes__sys_notification__property__topic": "Sujet", "@sage/xtrem-communication/nodes__sys_notification__property__userEmail": "E-mail utilisateur", "@sage/xtrem-communication/nodes__sys_notification_history__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-communication/nodes__sys_notification_history__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-communication/nodes__sys_notification_history__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-communication/nodes__sys_notification_history__node_name": "Historique de notification système", "@sage/xtrem-communication/nodes__sys_notification_history__property__dateLogged": "Enregistrement d'historique de notification de date créé", "@sage/xtrem-communication/nodes__sys_notification_history__property__envelope": "Enveloppe", "@sage/xtrem-communication/nodes__sys_notification_history__property__errorMessage": "Message d'erreur", "@sage/xtrem-communication/nodes__sys_notification_history__property__errorStack": "<PERSON><PERSON> d'er<PERSON>", "@sage/xtrem-communication/nodes__sys_notification_history__property__listener": "Listener", "@sage/xtrem-communication/nodes__sys_notification_history__property__locale": "Paramètres régionaux", "@sage/xtrem-communication/nodes__sys_notification_history__property__notificationContext": "Contexte de notification", "@sage/xtrem-communication/nodes__sys_notification_history__property__notificationId": "Code de notification", "@sage/xtrem-communication/nodes__sys_notification_history__property__originId": "Code origine", "@sage/xtrem-communication/nodes__sys_notification_history__property__replyId": "Code réponse", "@sage/xtrem-communication/nodes__sys_notification_history__property__replyTopic": "Sujet de réponse", "@sage/xtrem-communication/nodes__sys_notification_history__property__status": "Statut", "@sage/xtrem-communication/nodes__sys_notification_history__property__topic": "Sujet", "@sage/xtrem-communication/nodes__sys_notification_history__query__all": "<PERSON>ut", "@sage/xtrem-communication/nodes__sys_notification_history__query__all__failed": "Échec complet.", "@sage/xtrem-communication/nodes__sys_notification_log_entry__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-communication/nodes__sys_notification_log_entry__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-communication/nodes__sys_notification_log_entry__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-communication/nodes__sys_notification_log_entry__node_name": "Saisie trace notification système", "@sage/xtrem-communication/nodes__sys_notification_log_entry__property__data": "<PERSON><PERSON><PERSON>", "@sage/xtrem-communication/nodes__sys_notification_log_entry__property__level": "Niveau", "@sage/xtrem-communication/nodes__sys_notification_log_entry__property__message": "Message", "@sage/xtrem-communication/nodes__sys_notification_log_entry__property__spanContext": "Contexte espacement", "@sage/xtrem-communication/nodes__sys_notification_log_entry__property__sysNotificationState": "Statut de notification système", "@sage/xtrem-communication/nodes__sys_notification_log_entry__property__timestamp": "Horodatage", "@sage/xtrem-communication/nodes__sys_notification_state__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-communication/nodes__sys_notification_state__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-communication/nodes__sys_notification_state__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-communication/nodes__sys_notification_state__bulkMutation__bulkDelete": "Suppression de masse", "@sage/xtrem-communication/nodes__sys_notification_state__node_name": "Statut de notification système", "@sage/xtrem-communication/nodes__sys_notification_state__property__envelope": "Enveloppe", "@sage/xtrem-communication/nodes__sys_notification_state__property__isRead": "Lecture", "@sage/xtrem-communication/nodes__sys_notification_state__property__isUserNotificationRequested": "Notification utilisateur demandée", "@sage/xtrem-communication/nodes__sys_notification_state__property__locale": "Paramètres régionaux", "@sage/xtrem-communication/nodes__sys_notification_state__property__logs": "Traces", "@sage/xtrem-communication/nodes__sys_notification_state__property__message": "Message", "@sage/xtrem-communication/nodes__sys_notification_state__property__notificationContext": "Contexte de notification", "@sage/xtrem-communication/nodes__sys_notification_state__property__notificationId": "Code de notification", "@sage/xtrem-communication/nodes__sys_notification_state__property__operationName": "Nom de l'opération", "@sage/xtrem-communication/nodes__sys_notification_state__property__originId": "Code origine", "@sage/xtrem-communication/nodes__sys_notification_state__property__parameterValues": "Valeurs de paramètre", "@sage/xtrem-communication/nodes__sys_notification_state__property__progress": "Avancement", "@sage/xtrem-communication/nodes__sys_notification_state__property__progressBarPercent": "Pourcentage de la barre de progression", "@sage/xtrem-communication/nodes__sys_notification_state__property__replyId": "Code réponse", "@sage/xtrem-communication/nodes__sys_notification_state__property__result": "Résultat", "@sage/xtrem-communication/nodes__sys_notification_state__property__status": "Statut", "@sage/xtrem-communication/nodes__sys_notification_state__property__timeEnded": "Heure de fin", "@sage/xtrem-communication/nodes__sys_notification_state__property__timeStarted": "<PERSON><PERSON> d<PERSON>", "@sage/xtrem-communication/nodes__sys_notification_state__property__topic": "Sujet", "@sage/xtrem-communication/nodes__sys_notification_state__property__user": "Utilisa<PERSON>ur", "@sage/xtrem-communication/nodes__sys-notification-state__error-when-data-level-result": "Vous pouvez uniquement définir un résultat de données lorsque le niveau est Résultat.", "@sage/xtrem-communication/nodes__sys-notification-state__error-when-status-success": "Vous pouvez uniquement générer un message d'erreur lorsque le statut est Erreur.", "@sage/xtrem-communication/nodes__sys-notification-state__status_deletion": "Vous pouvez uniquement supprimer un historique si le statut est Erreur, Interrompue,<PERSON><PERSON>, arr<PERSON><PERSON><PERSON>, ou succès.", "@sage/xtrem-communication/package__name": "Communication", "@sage/xtrem-communication/pages__sys_notification_history____title": "Historique de notification", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__dateLogged": "Envoyée", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__errorMessage": "Message", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__listener": "Listener", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__replyID": "ID de réponse", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__replyTopic": "Sujet de réponse", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__status": "Statut", "@sage/xtrem-communication/pages__sys_notification_history__history____columns__title__topic": "Sujet", "@sage/xtrem-communication/pages__sys_notification_history__history____title": "Résultats", "@sage/xtrem-communication/pages__sys_notification_history__historyBlock____title": "Système", "@sage/xtrem-communication/pages__sys_notification_history__historyCriteriaBlock____title": "Critères", "@sage/xtrem-communication/pages__sys_notification_history__historyStatus____title": "Statut", "@sage/xtrem-communication/pages__sys_notification_history__section____title": "Système", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__bulkActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__bulkActions__title__2": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line_4__title": "Heure de fin", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line_5__title": "Avancement", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line1__title": "Planification", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line2__title": "Opération", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line2Right__title": "Code", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line3__title": "<PERSON><PERSON> d<PERSON>", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line4__title": "Heure de fin", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line5__title": "Avancement", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line6__title": "Utilisa<PERSON>ur", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line7__title": "Phase", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__line8__title": "Détail", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__message__title": "Message", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__listItem__title__title": "Statut", "@sage/xtrem-communication/pages__sys_notification_state____navigationPanel__optionsMenu__title": "Tous", "@sage/xtrem-communication/pages__sys_notification_state____objectTypePlural": "Historique tâches batch", "@sage/xtrem-communication/pages__sys_notification_state____objectTypeSingular": "Historique tâche batch", "@sage/xtrem-communication/pages__sys_notification_state____title": "Historique tâches batch", "@sage/xtrem-communication/pages__sys_notification_state__generalSection____title": "Général", "@sage/xtrem-communication/pages__sys_notification_state__logs____columns__title__data": "<PERSON><PERSON>", "@sage/xtrem-communication/pages__sys_notification_state__logs____columns__title__level": "Niveau", "@sage/xtrem-communication/pages__sys_notification_state__logs____columns__title__message": "Message", "@sage/xtrem-communication/pages__sys_notification_state__logs____columns__title__timestamp": "<PERSON><PERSON>", "@sage/xtrem-communication/pages__sys_notification_state__logs____title": "Traces", "@sage/xtrem-communication/pages__sys_notification_state__logsSection____title": "Traces", "@sage/xtrem-communication/pages__sys_notification_state__message____title": "Message", "@sage/xtrem-communication/pages__sys_notification_state__notificationId____title": "Code de notification", "@sage/xtrem-communication/pages__sys_notification_state__operationName____title": "Opération", "@sage/xtrem-communication/pages__sys_notification_state__originId____title": "Code origine", "@sage/xtrem-communication/pages__sys_notification_state__parameters____columns__title__name": "Nom", "@sage/xtrem-communication/pages__sys_notification_state__parameters____columns__title__value": "<PERSON><PERSON>", "@sage/xtrem-communication/pages__sys_notification_state__parameters____title": "Paramètres", "@sage/xtrem-communication/pages__sys_notification_state__progressBarPercent____title": "Avancement", "@sage/xtrem-communication/pages__sys_notification_state__result____title": "Résultat", "@sage/xtrem-communication/pages__sys_notification_state__status____title": "Statut", "@sage/xtrem-communication/pages__sys_notification_state__timeEnded____title": "Heure de fin", "@sage/xtrem-communication/pages__sys_notification_state__timeStarted____title": "<PERSON><PERSON> d<PERSON>", "@sage/xtrem-communication/pages__sys_notification_state__track_button_text": "<PERSON><PERSON><PERSON>", "@sage/xtrem-communication/permission__create__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-communication/permission__delete__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-communication/permission__read__name": "Lecture", "@sage/xtrem-communication/permission__update__name": "Mettre à jour", "@sage/xtrem-communication/service_options__notification_center__name": "Centre de notification", "@sage/xtrem-communication/sys__notification_history__search": "<PERSON><PERSON><PERSON>"}