import { datetime, decorators, Node, Reference } from '@sage/xtrem-core';
import * as xtremCommunication from '..';
import { dataOnlyWhenLevelIsResult } from '../events/controls/sys-notification-state';

@decorators.node<SysNotificationLogEntry>({
    isPublished: true,
    storage: 'sql',
    canCreate: false,
    canRead: true,
    canUpdate: true,
    canDeleteMany: true,
    isVitalCollectionChild: true,
    isPlatformNode: true,
})
export class SysNotificationLogEntry extends Node {
    @decorators.referenceProperty<SysNotificationLogEntry, 'sysNotificationState'>({
        isStored: true,
        isPublished: true,
        node: () => xtremCommunication.nodes.SysNotificationState,
        isVitalParent: true,
    })
    readonly sysNotificationState: Reference<xtremCommunication.nodes.SysNotificationState>;

    @decorators.enumProperty<SysNotificationLogEntry, 'level'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremCommunication.enums.LogLevelDataType,
    })
    readonly level: Promise<xtremCommunication.enums.LogLevel>;

    @decorators.stringProperty<SysNotificationLogEntry, 'message'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremCommunication.dataTypes.logMessage,
    })
    readonly message: Promise<string>;

    @decorators.datetimeProperty<SysNotificationLogEntry, 'timestamp'>({
        isStored: true,
        isPublished: true,
    })
    readonly timestamp: Promise<datetime>;

    @decorators.jsonProperty<SysNotificationLogEntry, 'data'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        async control(cx) {
            await dataOnlyWhenLevelIsResult(cx, this);
        },
    })
    readonly data: Promise<any | null>;

    @decorators.jsonProperty<SysNotificationLogEntry, 'spanContext'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly spanContext: Promise<any | null>;
}
