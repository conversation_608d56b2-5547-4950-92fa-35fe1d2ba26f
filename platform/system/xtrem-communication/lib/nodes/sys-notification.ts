/** @ignore */ /** */
import { decorators, Dict, nanoIdDataType, Node, TextStream } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { nanoid } from 'nanoid';
import * as xtremCommunication from '..';

@decorators.node<SysNotification>({
    isPublished: true,
    storage: 'sql',
    canCreate: false,
    canRead: true,
    canUpdate: true,
    canDeleteMany: false,
    isPlatformNode: true,
    isSharedByAllTenants: true,
    indexes: [
        {
            orderBy: { tenantId: +1 },
        },
    ],
})
export class SysNotification extends Node {
    /**
     * the tenant's id
     */
    @decorators.stringProperty<SysNotification, 'tenantId'>({
        isStored: true,
        dataType: () => nanoIdDataType,
    })
    readonly tenantId: Promise<string>;

    /**
     * the origin id
     */
    @decorators.stringProperty<SysNotification, 'originId'>({
        isStored: true,
        dataType: () => xtremCommunication.dataTypes.originIdDataType,
    })
    readonly originId: Promise<string>;

    /**
     * the workflow resume token
     */
    @decorators.stringProperty<SysNotification, 'resumeToken'>({
        isStored: true,
        dataType: () => xtremCommunication.dataTypes.listenerNameDataType,
    })
    readonly resumeToken: Promise<string>;

    /**
     * the notification id
     */
    @decorators.stringProperty<SysNotification, 'notificationId'>({
        isStored: true,
        dataType: () => nanoIdDataType,
        defaultValue: nanoid,
    })
    readonly notificationId: Promise<string>;

    /**
     * the reply id
     */
    @decorators.stringProperty<SysNotification, 'replyId'>({
        isStored: true,
        dataType: () => nanoIdDataType,
    })
    readonly replyId: Promise<string>;

    /**
     * the reply topic
     */
    @decorators.stringProperty<SysNotification, 'replyTopic'>({
        isStored: true,
        dataType: () => xtremSystem.dataTypes.name,
        defaultValue: '',
    })
    readonly replyTopic: Promise<string>;

    /**
     * the user, identified by his/her email
     */
    @decorators.stringProperty<SysNotification, 'userEmail'>({
        isStored: true,
        dataType: () => xtremSystem.dataTypes.email,
    })
    readonly userEmail: Promise<string>;

    /**
     * the login, user logged in when notify was done
     */
    @decorators.stringProperty<SysNotification, 'login'>({
        isStored: true,
        dataType: () => xtremSystem.dataTypes.email,
    })
    readonly login: Promise<string>;

    /**
     * the locale
     */
    @decorators.stringProperty<SysNotification, 'locale'>({
        isStored: true,
        dataType: () => xtremSystem.dataTypes.localeIdDataType,
    })
    readonly locale: Promise<string>;

    /**
     * the topic name
     */
    @decorators.stringProperty<SysNotification, 'topic'>({
        isStored: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly topic: Promise<string>;

    /**
     * the data limited to 8k
     */
    @decorators.textStreamProperty<SysNotification, 'payload'>({
        isStored: true,
        dataType: () => xtremCommunication.dataTypes.communicationNotificationDataDataType,
    })
    readonly payload: Promise<TextStream>;

    /**
     * the notification status
     */
    @decorators.enumProperty<SysNotification, 'status'>({
        isStored: true,
        dataType: () => xtremCommunication.enums.SqsStatusDataType,
        defaultValue: () => 'pending',
    })
    readonly status: Promise<xtremCommunication.enums.SqsStatus>;

    /**
     * Extra attributes, e.g. for filtering we could have { securityFilters: "{sites: ['1','2']}" }
     */
    @decorators.jsonProperty<SysNotification, 'extraAttributes'>({
        isStored: true,
        isNullable: true,
    })
    readonly extraAttributes: Promise<Dict<any> | null>;
}
