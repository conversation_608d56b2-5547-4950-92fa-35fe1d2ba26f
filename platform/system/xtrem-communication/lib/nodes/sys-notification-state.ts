import {
    AnyRecord,
    Collection,
    Context,
    datetime,
    decimal,
    decorators,
    nanoIdDataType,
    Node,
    Reference,
    supportReadonlyUserEmail,
    supportUserEmail,
} from '@sage/xtrem-core';
import { LogicError, NotificationProgress } from '@sage/xtrem-shared';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremCommunication from '..';
import '../core-extension';
import { controlStatusForDeletion } from '../events/controls/sys-notification-state';
import { createNotificationState, updateNotificationState } from '../functions/notification-state';

@decorators.node<SysNotificationState>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canBulkDelete: true,
    canDeleteMany: true,
    canDelete: true,
    isPlatformNode: true,
    indexes: [{ orderBy: { notificationId: 1 }, isUnique: true, isNaturalKey: true }, { orderBy: { user: 1 } }],
    async getFilters(context) {
        const currentUser = await context.user;
        return currentUser?.isAdministrator ||
            [supportReadonlyUserEmail, supportUserEmail].includes(currentUser?.email || '')
            ? []
            : [{ user: currentUser?._id }];
    },
    async controlDelete(cx) {
        await controlStatusForDeletion(cx, await this.status);
    },
})
export class SysNotificationState extends Node {
    @decorators.stringProperty<SysNotificationState, 'notificationId'>({
        isStored: true,
        isPublished: true,
        dataType: () => nanoIdDataType,
    })
    readonly notificationId: Promise<string>;

    @decorators.booleanProperty<SysNotificationState, 'isRead'>({
        isStored: true,
        isPublished: true,
        defaultValue: false,
        serviceOptions: () => [xtremCommunication.serviceOptions.notificationCenter],
    })
    readonly isRead: Promise<boolean>;

    /** Common to all the notifications in a given process. */
    @decorators.stringProperty<SysNotificationState, 'originId'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremCommunication.dataTypes.originIdDataType,
    })
    readonly originId: Promise<string>;

    /** format :  node name '.' method name
     *  Will become a reference to a SysOperation later */
    @decorators.stringProperty<SysNotificationState, 'operationName'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly operationName: Promise<string>;

    @decorators.jsonProperty<SysNotificationState, 'parameterValues'>({
        isStored: true,
        isPublished: true,
    })
    readonly parameterValues: Promise<AnyRecord>;

    @decorators.stringProperty<SysNotificationState, 'topic'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly topic: Promise<string>;

    @decorators.stringProperty<SysNotificationState, 'replyId'>({
        isStored: true,
        isPublished: true,
        defaultValue: '',
        dataType: () => nanoIdDataType,
    })
    readonly replyId: Promise<string>;

    @decorators.stringProperty<SysNotificationState, 'locale'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.localeDatatype,
        async defaultValue() {
            return (await this.$.context.user)?.locale || 'en-US';
        },
    })
    readonly locale: Promise<string>;

    @decorators.jsonProperty<SysNotificationState, 'envelope'>({
        isStored: true,
        isNullable: true,
    })
    readonly envelope: Promise<xtremCommunication.NotificationEnvelope | null>;

    @decorators.enumProperty<SysNotificationState, 'status'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        async isFrozen() {
            return ['error', 'notResponding', 'stopped', 'success'].includes(await this.status);
        },
        dataType: () => xtremCommunication.enums.NotificationStatusDataType,
    })
    readonly status: Promise<xtremCommunication.enums.NotificationStatus>;

    @decorators.jsonProperty<SysNotificationState, 'result'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        lookupAccess: true,
    })
    readonly result: Promise<any>;

    @decorators.stringProperty<SysNotificationState, 'message'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremCommunication.dataTypes.messageDataType,
        lookupAccess: true,
    })
    readonly message: Promise<string>;

    @decorators.jsonProperty<SysNotificationState, 'progress'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        defaultValue() {
            return {
                totalCount: 0,
                successCount: 0,
                errorCount: 0,
                phase: '',
                detail: '',
            };
        },
    })
    readonly progress: Promise<NotificationProgress>;

    @decorators.decimalProperty<SysNotificationState, 'progressBarPercent'>({
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.decimal,
        lookupAccess: true,
        async getValue() {
            const progress = await this.progress;
            const successCount = progress.successCount ?? 0;
            const errorCount = progress.errorCount ?? 0;
            const totalCount = progress.totalCount ?? 0;

            if (totalCount === 0) return 0;

            return ((successCount + errorCount) / totalCount) * 100;
        },
    })
    readonly progressBarPercent: Promise<decimal>;

    @decorators.datetimeProperty<SysNotificationState, 'timeStarted'>({
        isStored: true,
        isPublished: true,
    })
    readonly timeStarted: Promise<datetime>;

    @decorators.datetimeProperty<SysNotificationState, 'timeEnded'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        defaultValue: null,
    })
    readonly timeEnded: Promise<datetime | null>;

    /** Scratch object where the notification handler can store its own information */
    @decorators.jsonProperty<SysNotificationState, 'notificationContext'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly notificationContext: Promise<object>;

    @decorators.referenceProperty<SysNotificationState, 'user'>({
        isStored: true,
        isPublished: true,
        node: () => xtremSystem.nodes.User,
        async defaultValue() {
            return this.$.context.read(xtremSystem.nodes.User, { email: (await this.$.context.user)?.email });
        },
    })
    readonly user: Reference<xtremSystem.nodes.User>;

    @decorators.booleanProperty<SysNotificationState, 'isUserNotificationRequested'>({
        isStored: true,
        isPublished: true,
    })
    readonly isUserNotificationRequested: Promise<boolean>;

    @decorators.collectionProperty<SysNotificationState, 'logs'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'sysNotificationState',
        node: () => xtremCommunication.nodes.SysNotificationLogEntry,
    })
    readonly logs: Collection<xtremCommunication.nodes.SysNotificationLogEntry>;

    /**
     * Method to create/update a record in SysNotificationState.
     * @param context writable context
     * @param envelope notification envelope
     * @param status notification status
     * @param error Error where applicable
     * @param listener listener from which this log is created
     * @param notificationContext notification context from which this log is created
     * @returns _id of created/updated SysNotificationState
     */
    static async upsert(
        context: Context,
        notification: {
            operationName?: string;
            envelope: xtremCommunication.NotificationEnvelope;
            status: xtremCommunication.enums.NotificationStatus;
            error?: Error;
            result?: any;
        },
    ): Promise<number> {
        const { notificationId, topic } = notification.envelope.attributes;

        const existingNotificationState = await context.select(
            SysNotificationState,
            { _id: true },
            { filter: { notificationId }, first: 1 },
        );

        if (existingNotificationState.length && existingNotificationState[0]._id) {
            return updateNotificationState(context, { ...notification, _id: existingNotificationState[0]._id });
        }

        return createNotificationState(context, {
            operationName: notification.operationName || '',
            notificationId,
            parameterValues: notification.envelope.payload,
            envelope: notification.envelope,
            status: notification.status,
            message: notification.error?.message,
            topic,
            result: notification.result,
        });
    }

    async completeWithResult(result: any): Promise<void> {
        const status = await this.status;
        if (status === 'success' || status === 'error')
            throw new LogicError('Cannot complete a notification that is already completed');

        await (this as SysNotificationState).$.set({ status: 'success', result });
        await this.$.save();
    }

    async completeWithError(error: string): Promise<void> {
        const status = await this.status;
        if (status === 'success' || status === 'error')
            throw new LogicError('Cannot complete a notification that is already completed');

        const message = error.slice(0, xtremCommunication.dataTypes.messageDataType.maxLength);

        await (this as SysNotificationState).$.set({ status: 'error', message });
        await this.$.save();
    }
}
