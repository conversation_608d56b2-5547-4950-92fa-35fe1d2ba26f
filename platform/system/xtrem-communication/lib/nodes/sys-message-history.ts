/** @ignore */ /** */
import {
    AnyNode,
    Context,
    datetime,
    decorators,
    Node,
    NodeCreateData,
    NodeStatus,
    TextStream,
    UserInfo,
    Uuid,
} from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremCommunication from '..';

@decorators.node<SysMessageHistory>({
    isPublished: true,
    storage: 'sql',
    canCreate: false,
    canRead: true,
    canUpdate: true,
    canDeleteMany: true,
    isPlatformNode: true,
    indexes: [
        {
            orderBy: { integrationSolution: +1, id: +1 },
            isUnique: true,
        },
        {
            orderBy: { integrationSolution: +1 },
            isUnique: false,
        },
    ],

    async saveBegin() {
        /**
         * If the receivedRequest is set ( case on asynchronous call )
         * or if a receivedRequest is in the creation payload ( case on a request received from the third party app )
         * We set the right status
         */
        if (
            (this.$.status === NodeStatus.modified &&
                (await (await this.$.old).receivedRequest) !== (await this.receivedRequest) &&
                (await this.status) === (await (await this.$.old).status)) ||
            (this.$.status === NodeStatus.added && (await this.receivedRequest).value)
        ) {
            await this.$.set({ status: 'received' });
        }

        /**
         *   On status change
         *   there's no old status when a node is created/added  ,
         *   if the status is sent or received we set the dateTime
         */
        if (this.$.status === NodeStatus.added || (await (await this.$.old).status) !== (await this.status)) {
            switch (await this.status) {
                case 'sent':
                    await this.$.set({ sendStamp: datetime.now() });
                    break;
                case 'received':
                    await this.$.set({ receivedStamp: datetime.now() });
                    break;
                default:
                    break;
            }
        }
    },
})
export class SysMessageHistory extends Node {
    /**
     *  Integration solution name
     */
    @decorators.stringProperty<SysMessageHistory, 'id'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.uuid,
        defaultValue: () => Uuid.generate().toString(),
    })
    readonly id: Promise<string>;

    /**
     *  Integration solution name
     */
    @decorators.stringProperty<SysMessageHistory, 'integrationSolution'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly integrationSolution: Promise<string>;

    /**
     * the message status
     */
    @decorators.enumProperty<SysMessageHistory, 'status'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremCommunication.enums.CommunicationStateDataType,
        defaultValue: () => 'notSent',
    })
    readonly status: Promise<xtremCommunication.enums.CommunicationState>;

    /** context  request sent   */
    @decorators.jsonProperty<SysMessageHistory, 'context'>({
        isStored: true,
        isPublished: true,
    })
    readonly context: Promise<object>;

    /**
     *  request send to the integrations solution
     */
    @decorators.textStreamProperty<SysMessageHistory, 'sentRequest'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremCommunication.dataTypes.requestDataDataType,
    })
    readonly sentRequest: Promise<TextStream>;

    /**
     *  request receive from the integrations solution /  the data limited to 8k
     */
    @decorators.textStreamProperty<SysMessageHistory, 'receivedRequest'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremCommunication.dataTypes.requestDataDataType,
    })
    readonly receivedRequest: Promise<TextStream>;

    /** context  request sent   */
    @decorators.jsonProperty<SysMessageHistory, 'attributes'>({
        isStored: true,
        isPublished: true,
    })
    readonly attributes: Promise<object>;

    /**
     * the user
     */
    @decorators.jsonProperty<SysMessageHistory, 'user'>({
        isPublished: true,
        async computeValue() {
            // TODO: review this
            const user = (await this.$.createdBy) as AnyNode;
            return { _id: user._id, email: (await user.email) as string };
        },
    })
    readonly user: Promise<UserInfo>;

    /**
     * ( datetime send )( if not sent must be null )
     */
    @decorators.datetimeProperty<SysMessageHistory, 'sendStamp'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        defaultValue: null,
    })
    readonly sendStamp: Promise<datetime | null>;

    /**
     * Updated when received
     */
    @decorators.datetimeProperty<SysMessageHistory, 'receivedStamp'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        defaultValue: null,
    })
    readonly receivedStamp: Promise<datetime | null>;

    /** array containing error    */
    @decorators.jsonProperty<SysMessageHistory, 'communicationDiagnoses'>({
        isStored: true,
        isPublished: true,
    })
    readonly communicationDiagnoses: Promise<object>;

    /**
     *  Error stack
     */
    @decorators.textStreamProperty<SysMessageHistory, 'errorStack'>({
        isStored: true,
        dataType: () => xtremCommunication.dataTypes.errorStackDataType,
    })
    readonly errorStack: Promise<TextStream>;

    /**
     *  Error message
     */
    @decorators.stringProperty<SysMessageHistory, 'errorMessage'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremCommunication.dataTypes.errorMessageDataType,
    })
    readonly errorMessage: Promise<string>;

    @decorators.mutation<typeof SysMessageHistory, 'purge'>({
        isPublished: true,
        parameters: [
            {
                name: 'integrationSolution',
                type: 'string',
                isMandatory: true,
            },
        ],
        return: 'boolean',
    })
    static async purge(context: Context, integrationSolution: string): Promise<boolean> {
        await context.deleteMany(SysMessageHistory, { integrationSolution });
        return true;
    }

    /**
     * Method to create or update messgae history
     * @param context writable context
     * @param data data to create or update. If this is an update then a value for integrationSolution and id must be provided.
     * @returns SysMessageHistory node instance
     */
    static async createOrUpdateMessageHistory(
        context: Context,
        data: NodeCreateData<SysMessageHistory>,
    ): Promise<SysMessageHistory> {
        if (!data.integrationSolution) throw new Error('integrationSolution is required.');
        let messageHistory =
            data.id &&
            (await context.tryRead(
                SysMessageHistory,
                { id: data.id, integrationSolution: data.integrationSolution },
                { forUpdate: true },
            ));

        if (!messageHistory) {
            messageHistory = await context.create(SysMessageHistory, data);
        } else {
            await messageHistory.$.set(data);
        }
        await messageHistory.$.save();

        return messageHistory;
    }
}
