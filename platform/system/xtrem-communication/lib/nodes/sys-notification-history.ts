/** @ignore */ /** */
import { Context, Node, NodeStatus, TextStream, date, decorators, nanoIdDataType } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremCommunication from '..';

/**
 * This node is deprecated
 */
@decorators.node<SysNotificationHistory>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDeleteMany: false,
    isPlatformNode: true,
    // TODO: this index should be unique
    indexes: [{ orderBy: { notificationId: 1, replyId: 1 } }],
})
export class SysNotificationHistory extends Node {
    /**
     * the notification id
     */
    @decorators.stringProperty<SysNotificationHistory, 'notificationId'>({
        isStored: true,
        dataType: () => nanoIdDataType,
    })
    readonly notificationId: Promise<string>;

    /**
     * the origin id
     */
    @decorators.stringProperty<SysNotificationHistory, 'originId'>({
        isStored: true,
        dataType: () => xtremCommunication.dataTypes.originIdDataType,
    })
    readonly originId: Promise<string>;

    /**
     * the listener
     */
    @decorators.stringProperty<SysNotificationHistory, 'listener'>({
        isStored: true,
        dataType: () => xtremCommunication.dataTypes.listenerNameDataType,
    })
    readonly listener: Promise<string>;

    /**
     * the topic name
     */
    @decorators.stringProperty<SysNotificationHistory, 'topic'>({
        isStored: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly topic: Promise<string>;

    /**
     * the reply id
     */
    @decorators.stringProperty<SysNotificationHistory, 'replyId'>({
        isStored: true,
        dataType: () => nanoIdDataType,
    })
    readonly replyId: Promise<string>;

    /**
     * the reply topic
     */
    @decorators.stringProperty<SysNotificationHistory, 'replyTopic'>({
        isStored: true,
        dataType: () => xtremSystem.dataTypes.name,
        defaultValue: '',
    })
    readonly replyTopic: Promise<string>;

    /**
     * the locale
     */
    @decorators.stringProperty<SysNotificationHistory, 'locale'>({
        isStored: true,
        dataType: () => xtremSystem.dataTypes.localeIdDataType,
    })
    readonly locale: Promise<string>;

    /**
     * the data limited to 8k
     */
    @decorators.textStreamProperty<SysNotificationHistory, 'envelope'>({
        isStored: true,
        dataType: () => xtremCommunication.dataTypes.communicationNotificationDataDataType,
    })
    readonly envelope: Promise<TextStream>;

    /**
     *  Error stack
     */
    @decorators.textStreamProperty<SysNotificationHistory, 'errorStack'>({
        isStored: true,
        dataType: () => xtremCommunication.dataTypes.errorStackDataType,
    })
    readonly errorStack: Promise<TextStream>;

    /**
     *  Error message
     */
    @decorators.stringProperty<SysNotificationHistory, 'errorMessage'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremCommunication.dataTypes.errorMessageDataType,
    })
    readonly errorMessage: Promise<string>;

    /**
     * the message status
     */
    @decorators.enumProperty<SysNotificationHistory, 'status'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremCommunication.enums.CommunicationStateDataType,
        defaultValue: () => 'notSent',
    })
    readonly status: Promise<xtremCommunication.enums.CommunicationState>;

    /**
     * the date at which it was created
     */
    @decorators.dateProperty<SysNotificationHistory, 'dateLogged'>({
        isStored: true,
        isPublished: true,
        isFrozen() {
            return this.$.status === NodeStatus.modified;
        },
        defaultValue(): date {
            return date.today();
        },
    })
    readonly dateLogged: Promise<date>;

    @decorators.jsonProperty<SysNotificationHistory, 'notificationContext'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly notificationContext: Promise<object>;

    /**
     * Method to create a record in SysNotificationHistory.
     * @param context writable context
     * @param envelope notification envelope
     * @param status notification status
     * @param error Error where applicable
     * @param listener listener from which this log is created
     * @param notificationContext notification context from which this log is created
     * @returns SysNotificationHistory node instance
     */
    static async createNotificationHistory(
        context: Context,
        envelope: xtremCommunication.NotificationEnvelope,
        status: xtremCommunication.enums.CommunicationState,
        error?: Error,
        listener?: string,
        notificationContext?: object,
    ): Promise<SysNotificationHistory> {
        const notificationHistory = await context.create(SysNotificationHistory, {
            ...envelope.attributes,
            envelope: TextStream.fromJsonObject(envelope),
            errorStack: error && TextStream.fromString(error.stack || error.message),
            errorMessage: error && error.message,
            listener,
            status,
            notificationContext,
        });
        await notificationHistory.$.save();

        return notificationHistory;
    }

    /**
     *  get All Notification history
     * @param context
     * @returns
     */
    @decorators.query<typeof SysNotificationHistory, 'all'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'array',
            item: {
                type: 'object',
                properties: {
                    notificationId: 'string',
                    originId: 'string',
                    listener: 'string',
                    topic: 'string',
                    replyId: 'string',
                    replyTopic: 'string',
                    locale: 'string',
                    envelope: 'textStream',
                    errorStack: 'textStream',
                    errorMessage: 'string',
                    status: 'string',
                    dateLogged: 'string',
                },
            },
            isMandatory: true,
        },
    })
    static all(context: Context): Promise<xtremCommunication.sharedFunctions.interfaces.AllNotificationHistory[]> {
        return context
            .query(SysNotificationHistory, {})
            .map(historyLine =>
                historyLine.$.payloadAsAny<xtremCommunication.sharedFunctions.interfaces.AllNotificationHistory>(),
            )
            .toArray();
    }
}
