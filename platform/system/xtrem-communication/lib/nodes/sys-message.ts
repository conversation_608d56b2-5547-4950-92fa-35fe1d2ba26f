/** @ignore */ /** */
import { decorators, Dict, nanoIdDataType, Node, TextStream } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { nanoid } from 'nanoid';
import * as xtremCommunication from '..';

@decorators.node<SysMessage>({
    isPublished: true,
    storage: 'sql',
    canCreate: false,
    canRead: true,
    canUpdate: true,
    canDeleteMany: false,
    isPlatformNode: true,
    isSharedByAllTenants: true,
    indexes: [
        {
            orderBy: { tenantId: +1 },
        },
    ],
})

// data, data limited to 8k
export class SysMessage extends Node {
    /**
     * the tenant's id
     */
    @decorators.stringProperty<SysMessage, 'tenantId'>({
        isStored: true,
        dataType: () => nanoIdDataType,
    })
    readonly tenantId: Promise<string>;

    /**
     * the queue name
     */
    @decorators.stringProperty<SysMessage, 'queue'>({
        isStored: true,
        dataType: () => xtremSystem.dataTypes.name,
    })
    readonly queue: Promise<string>;

    /**
     * the data unlimited, but actually limited to 4TB by postgreSQL
     *
     * @see https://www.postgresql.org/docs/9.3/release-9-3.html#AEN119478
     */
    @decorators.textStreamProperty<SysMessage, 'payload'>({
        isStored: true,
        dataType: () => xtremCommunication.dataTypes.messageDataDataType,
    })
    readonly payload: Promise<TextStream>;

    /**
     * the data unlimited, but actually limited to 4TB by postgreSQL
     *
     * @see https://www.postgresql.org/docs/9.3/release-9-3.html#AEN119478
     */
    @decorators.jsonProperty<SysMessage, 'attributes'>({
        isStored: true,
    })
    readonly attributes: Promise<Dict<string>>;

    /**
     * the message id
     */
    @decorators.stringProperty<SysMessage, 'messageId'>({
        isStored: true,
        dataType: () => nanoIdDataType,
        defaultValue: nanoid,
    })
    readonly messageId: Promise<string>;

    /**
     * the message status
     */
    @decorators.enumProperty<SysMessage, 'status'>({
        isStored: true,
        dataType: () => xtremCommunication.enums.SqsStatusDataType,
        defaultValue: () => 'pending',
    })
    readonly status: Promise<xtremCommunication.enums.SqsStatus>;
}
