import { ValidationContext } from '@sage/xtrem-core';
import * as xtremCommunication from '../..';

/** You can set an error message only when the status is error. */
export async function errorMessageAndStackWhenStatusIsNotError(
    cx: ValidationContext,
    message: string,
    status: xtremCommunication.enums.NotificationStatus,
): Promise<void> {
    await cx.error
        .withMessage(
            '@sage/xtrem-communication/nodes__sys-notification-state__error-when-status-success',
            'You can set an error message only when the status is error.',
        )
        .if(status !== 'error' && message !== '')
        .is.true();
}

export async function dataOnlyWhenLevelIsResult(
    cx: ValidationContext,
    logEntry: xtremCommunication.nodes.SysNotificationLogEntry,
): Promise<void> {
    // We can only set data when the level is result or spanContext is not null
    if ((await logEntry.level) === 'result' || (await logEntry.spanContext) != null) {
        return;
    }
    await cx.error
        .withMessage(
            '@sage/xtrem-communication/nodes__sys-notification-state__error-when-data-level-result',
            'You can set data result only when the level is result.',
        )
        .if(await logEntry.data)
        .is.not.empty();
}

/** You can delete a history only if the status is error, interrupted, notResponding, stopped or success. */
export async function controlStatusForDeletion(
    cx: ValidationContext,
    status: xtremCommunication.enums.NotificationStatus,
): Promise<void> {
    const deletionStatus: xtremCommunication.enums.NotificationStatus[] = [
        'error',
        'interrupted',
        'notResponding',
        'stopped',
        'success',
    ];
    await cx.error
        .withMessage(
            '@sage/xtrem-communication/nodes__sys-notification-state__status_deletion',
            'You can only delete a history if the status is error, interrupted, notResponding, stopped, or success.',
        )
        .if(deletionStatus.includes(status))
        .is.false();
}
