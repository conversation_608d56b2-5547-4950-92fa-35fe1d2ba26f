import { Activity } from '@sage/xtrem-core';
import { SysNotificationHistory } from '../nodes/sys-notification-history';

export const sysNotificationHistory = new Activity({
    description: 'Notification history',
    node: () => SysNotificationHistory,
    __filename,
    permissions: ['read'],
    operationGrants: {
        read: [
            {
                operations: ['all'],
                on: [() => SysNotificationHistory],
            },
        ],
    },
});
