import { EnumDataType } from '@sage/xtrem-core';

export enum NotificationStatusEnum {
    /** (default) -> context.notify was called by the scheduler or by the asyncMutation */
    'pending',
    /** notification receive by the listener & in proccess in the listener */
    'running',
    /**  listener completed successfuly */
    'success',
    /** listener completed with error */
    'error',
    /** user requested stop but listener still running*/
    'stopRequested',
    /** -> listener completed after stop ( notification has been removed from sqs ) */
    'stopped',
    /**  listener is an unknown state and not responding to healthCheck */
    'notResponding',
    /** k<PERSON><PERSON><PERSON> requested shutdown of containers @deprecated  */
    'interruptRequested',
    /**  listener completed after interrupt ( notification is returned to sqs ) */
    'interrupted',
}

export type NotificationStatus = keyof typeof NotificationStatusEnum;

export const NotificationStatusDataType = new EnumDataType<NotificationStatus>({
    enum: NotificationStatusEnum,
    filename: __filename,
});
