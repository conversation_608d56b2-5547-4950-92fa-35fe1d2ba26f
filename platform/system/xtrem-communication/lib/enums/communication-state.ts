import { EnumDataType } from '@sage/xtrem-core';

export enum CommunicationStateEnum {
    'notSent',
    'sent',
    'received',
    'success',
    'retry',
    'error',
    'stopRequested',
    'stopped',
    'interruptRequested',
    'interrupted',
}

export type CommunicationState = keyof typeof CommunicationStateEnum;

export const CommunicationStateDataType = new EnumDataType<CommunicationState>({
    enum: CommunicationStateEnum,
    filename: __filename,
});
