import { EnumDataType } from '@sage/xtrem-core';

export enum IntegrationStateEnum {
    /** *  Nothing has been sent to the third party app */
    'not',
    /** *A request has been sent to the third party app - waiting for an answer */
    'pending',
    /** * Fully integrated */
    'success',
    /** * There was an error on the integration */
    'error',
    /** * a change as been done on the third party app, not replicated to  SDMO */
    'desynchronized',
}

export type IntegrationState = keyof typeof IntegrationStateEnum;

export const IntegrationStateDataType = new EnumDataType<IntegrationState>({
    enum: IntegrationStateEnum,
    filename: __filename,
});
