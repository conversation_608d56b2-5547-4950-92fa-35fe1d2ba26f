import {
    GraphApi,
    SysNotificationLogEntry,
    SysNotificationState as SysNotificationStateNode,
} from '@sage/xtrem-communication-api';
import { datetime } from '@sage/xtrem-date-time';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { Parameter, getParametersNameValueFromString } from '../client-functions/parameters';
import * as utils from '../client-functions/utils';
import { scheduler } from '../menu-items/scheduler';
@ui.decorators.page<SysNotificationState, SysNotificationStateNode>({
    title: 'Batch task history',
    objectTypeSingular: 'Batch task history',
    objectTypePlural: 'Batch task history',
    priority: 100,
    mode: 'tabs',
    menuItem: scheduler,
    node: '@sage/xtrem-communication/SysNotificationState',
    onError(error: string | (Error & { errors: Array<any> })) {
        return utils.formatError(this, error);
    },
    navigationPanel: {
        bulkActions: [
            {
                mutation: 'bulkDelete',
                title: 'Delete',
                buttonType: 'tertiary',
                icon: 'delete',
                isDestructive: true,
            },
            // It's not possible to define a bulkAction on page extension !
            {
                mutation: 'bulkStop',
                title: 'Stop',
                buttonType: 'secondary',
                icon: 'blocked_square',
            },
        ],
        listItem: {
            title: ui.nestedFields.label({
                bind: { status: true },
                title: 'Status',
                optionType: '@sage/xtrem-communication/NotificationStatus',
            }),
            message: ui.nestedFields.text({ bind: 'message', title: 'Message', isHiddenOnMainField: true }),
            line2: ui.nestedFields.text({
                bind: { operationName: true },
                title: 'Operation',
                isHiddenOnMainField: true,
            }),
            line2Right: ui.nestedFields.text({ bind: { _id: true }, title: 'ID' }),
            line3: ui.nestedFields.label({
                bind: { timeStarted: true },
                title: 'Time started',
                map(_id, row) {
                    if (row.timeStarted) {
                        return datetime
                            .parse(row.timeStarted)
                            .format(this.$.locale || undefined, 'YYYY-MM-DD HH:mm:ss');
                    }
                    return '';
                },
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
            }),

            line_4: ui.nestedFields.label({
                bind: { timeEnded: true },
                title: 'Time end',
                map(_id, row) {
                    if (row.timeEnded) {
                        return datetime.parse(row.timeEnded).format(this.$.locale || undefined, 'YYYY-MM-DD HH:mm:ss');
                    }
                    return '';
                },
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
            }),
            line_5: ui.nestedFields.progress({
                bind: { progressBarPercent: true },
                title: 'Progress',
                canFilter: false,
            }),
            line6: ui.nestedFields.text({ bind: { user: { email: true } }, title: 'User' }),
            line7: ui.nestedFields.label({
                bind: { progress: true },
                map(val) {
                    return JSON.parse(val).phase;
                },
                isHiddenOnMainField: true,
                title: 'Phase',
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
            }),
            line8: ui.nestedFields.label({
                bind: { progress: true },
                map(val) {
                    return JSON.parse(val).detail;
                },
                isHiddenOnMainField: true,
                title: 'Detail',
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
            }),
        },
        optionsMenu: [{ title: 'All', graphQLFilter: {} }],
        orderBy: { timeStarted: -1 },
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction],
        });
    },
    businessActions() {
        return [this.$standardCancelAction];
    },
    async onLoad() {
        this.parameters.value = getParametersNameValueFromString(this.parameterValues.value || '{}');

        const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

        this.result.isHidden = ['', '{}', null].includes(this.result.value);
        this.message.isHidden = ['', null].includes(this.message.value);

        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            cancel: this.$standardCancelAction,
        });
    },
})
export class SysNotificationState extends ui.Page<GraphApi> {
    @ui.decorators.textField<SysNotificationState>({
        isHidden: true,
        title: 'Notification ID',
    })
    notificationId: ui.fields.Text;

    @ui.decorators.textField<SysNotificationState>({
        isHidden: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.textField<SysNotificationState>({
        isHidden: true,
    })
    progress: ui.fields.Text;

    @ui.decorators.textField<SysNotificationState>({
        isHidden: true,
        title: 'Origin ID',
    })
    originId: ui.fields.Text;

    @ui.decorators.section<SysNotificationState>({
        title: 'General',
        isTitleHidden: true,
    })
    generalSection: ui.containers.Section;

    @ui.decorators.section<SysNotificationState>({
        title: 'Logs',
        isTitleHidden: true,
    })
    logsSection: ui.containers.Section;

    @ui.decorators.block<SysNotificationState>({
        parent() {
            return this.generalSection;
        },
    })
    generalBlock: ui.containers.Block;

    @ui.decorators.textField<SysNotificationState>({
        parent() {
            return this.generalBlock;
        },
        title: 'Operation',
        width: 'small',
        isReadOnly() {
            return !!this.notificationId.value;
        },
    })
    operationName: ui.fields.Text;

    @ui.decorators.referenceField<SysNotificationState>({
        node: '@sage/xtrem-system/User',
        valueField: '_id',
    })
    user: ui.fields.Reference;

    @ui.decorators.labelField<SysNotificationState>({
        parent() {
            return this.generalBlock;
        },
        title: 'Start time',
        width: 'small',
        map(_id, row) {
            if (this.timeStarted.value) {
                return datetime.parse(this.timeStarted.value).format(this.$.locale || undefined, 'YYYY-MM-DD HH:mm:ss');
            }
            return '';
        },
        backgroundColor: ui.tokens.colorsYang100,
        borderColor: ui.tokens.colorsYang100,
    })
    timeStarted: ui.fields.Label;

    @ui.decorators.labelField<SysNotificationState>({
        parent() {
            return this.generalBlock;
        },
        title: 'End time',
        width: 'small',
        map(_id, row) {
            if (this.timeEnded.value) {
                return datetime.parse(this.timeEnded.value).format(this.$.locale || undefined, 'YYYY-MM-DD HH:mm:ss');
            }
            return '';
        },
        backgroundColor: ui.tokens.colorsYang100,
        borderColor: ui.tokens.colorsYang100,
    })
    timeEnded: ui.fields.Label;

    @ui.decorators.labelField<SysNotificationState>({
        parent() {
            return this.generalBlock;
        },
        optionType: '@sage/xtrem-communication/NotificationStatus',
        title: 'Status',
    })
    status: ui.fields.Label;

    @ui.decorators.labelField<SysNotificationState>({
        isHidden: true,
    })
    parameterValues: ui.fields.Label;

    @ui.decorators.tableField<SysNotificationState, Parameter>({
        title: 'Parameters',
        canSelect: false,
        isTransient: true,
        isReadOnly() {
            return !!this.notificationId.value;
        },
        parent() {
            return this.generalSection;
        },
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'name', isReadOnly: true }),
            ui.nestedFields.text({ bind: 'value', title: 'value' }),
        ],
    })
    parameters: ui.fields.Table<Parameter>;

    getParameters(): ui.PartialCollectionValue<Parameter>[] {
        return this.parameters.value;
    }

    @ui.decorators.block<SysNotificationState>({
        parent() {
            return this.logsSection;
        },
    })
    logBlock: ui.containers.Block;

    @ui.decorators.buttonField<SysNotificationState>({
        isTransient: true,
        parent() {
            return this.logBlock;
        },
        map() {
            return ui.localize('@sage/xtrem-communication/pages__sys_notification_state__track_button_text', 'Track');
        },
        async onClick() {
            if (this.notificationId.value) {
                await this.status.refresh();
                await this.progressBarPercent.refresh();
                await this.logs.refresh();
            }
        },
    })
    executionTracking: ui.fields.Button;

    @ui.decorators.progressField<SysNotificationState>({
        parent() {
            return this.logBlock;
        },
        title: 'Progress',
    })
    progressBarPercent: ui.fields.Progress;

    @ui.decorators.tableField<SysNotificationState, SysNotificationLogEntry>({
        title: 'Logs',
        canResizeColumns: true,
        isReadOnly: true,
        displayMode: ui.fields.TableDisplayMode.compact,
        canSelect: false,
        canExport: true,
        parent() {
            return this.logsSection;
        },
        orderBy: {
            _id: +1,
        },
        columns: [
            ui.nestedFields.technical({ bind: '_id' }),
            ui.nestedFields.label({
                bind: 'timestamp',
                title: 'Time',
                map(_id, row) {
                    return datetime.parse(row.timestamp).format(this.$.locale || undefined, 'YYYY-MM-DD HH:mm:ss');
                },
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsYang100,
            }),
            ui.nestedFields.select({
                canFilter: true,
                bind: 'level',
                title: 'Level',
                optionType: '@sage/xtrem-communication/LogLevel',
            }),
            ui.nestedFields.text({ canFilter: true, bind: 'message', title: 'Message' }),
            ui.nestedFields.link({
                bind: 'data',
                title: 'Link',
                onClick(_id, row: any) {
                    if (row.data && JSON.parse(row.data).downloadUrl) {
                        this.$.router.goToExternal(JSON.parse(row.data).downloadUrl);
                    }
                },
                map(data) {
                    return data ? JSON.parse(data).filename : '';
                },
            }),
        ],
        onAllDataLoaded() {
            if (this.logs.value.some(line => (line.data ? JSON.parse(line.data).downloadUrl : false))) {
                this.logs.showColumn('data');
            } else {
                this.logs.hideColumn('data');
            }
        },
    })
    logs: ui.fields.Table<SysNotificationLogEntry>;

    @ui.decorators.labelField<SysNotificationState>({
        title: 'Result',
        parent() {
            return this.logBlock;
        },
        map(value) {
            return JSON.stringify(value);
        },
        isFullWidth: true,
    })
    result: ui.fields.Label;

    @ui.decorators.textAreaField<SysNotificationState>({
        title: 'message',
        parent() {
            return this.logBlock;
        },
        isReadOnly: true,
        isFullWidth: true,
        rows: 5,
    })
    message: ui.fields.TextArea;
}
