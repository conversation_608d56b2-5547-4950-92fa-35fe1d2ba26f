import { extractEdges, Filter } from '@sage/xtrem-client';
import { GraphApi, SysNotificationHistoryBinding as SysNotificationHistoryNode } from '@sage/xtrem-communication-api';
import * as ui from '@sage/xtrem-ui';
import * as PillColorCommunicationState from '../client-functions/pill-color-communication';
import { communication } from '../menu-items/communication';

@ui.decorators.page<SysNotificationHistory>({
    title: 'Notification history',
    module: 'system',
    mode: 'tabs',
    priority: 100,
    menuItem: communication,
    isTransient: true,
    access: { node: '@sage/xtrem-communication/SysNotificationHistory' },
    skipDirtyCheck: true,
    async onLoad() {
        this.historyStatus.value = ['error'];
        await this.fillHistoryTable();
    },
})
export class SysNotificationHistory extends ui.Page<GraphApi> {
    @ui.decorators.section<SysNotificationHistory>({ isTitleHidden: true, title: 'System' })
    section: ui.containers.Section;

    @ui.decorators.block<SysNotificationHistory>({
        parent() {
            return this.section;
        },
        title: 'Criteria',
        isTitleHidden: false,
    })
    historyCriteriaBlock: ui.containers.Block;

    @ui.decorators.block<SysNotificationHistory>({
        parent() {
            return this.section;
        },
        title: 'System',
        isTitleHidden: true,
    })
    historyBlock: ui.containers.Block;

    @ui.decorators.multiDropdownField<SysNotificationHistory>({
        parent() {
            return this.historyCriteriaBlock;
        },
        title: 'Status',
        bind: 'status',
        optionType: '@sage/xtrem-communication/CommunicationState',
    })
    historyStatus: ui.fields.MultiDropdown;

    @ui.decorators.buttonField<SysNotificationHistory>({
        parent() {
            return this.historyCriteriaBlock;
        },
        map() {
            return ui.localize('@sage/xtrem-communication/sys__notification_history__search', 'Search');
        },
        width: 'small',
        async onClick() {
            this.$.loader.isHidden = false;
            await this.fillHistoryTable();
            this.$.setPageClean();
            this.$.loader.isHidden = true;
        },
    })
    searchHistoryButton: ui.fields.Button;

    @ui.decorators.tableField<SysNotificationHistory, SysNotificationHistoryNode>({
        title: 'Results',
        isReadOnly: true,
        canSelect: false,
        canFilter: false,
        isChangeIndicatorDisabled: true,
        canResizeColumns: true,
        canExport: true,
        parent() {
            return this.historyBlock;
        },
        columns: [
            ui.nestedFields.label({
                bind: 'status',
                title: 'Status',
                optionType: '@sage/xtrem-communication/CommunicationState',
                style: (_id, rowValue) => PillColorCommunicationState.getLabelColorByStatus('CommunicationState', rowValue.status),
            }),
            ui.nestedFields.text({ bind: 'errorMessage', title: 'Message' }),

            ui.nestedFields.date({ title: 'Sent', bind: 'dateLogged' }),
        ],
    })
    history: ui.fields.Table<SysNotificationHistoryNode>;

    async fillHistoryTable() {
        this.history.value = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-communication/SysNotificationHistory')
                .query(
                    ui.queryUtils.edgesSelector(
                        { _id: true, errorMessage: true, status: true, dateLogged: true },
                        { filter: await this.getFilterNotificationHistory(), first: 500 },
                    ),
                )
                .execute(),
        );
    }

    getFilterNotificationHistory(): Filter<SysNotificationHistoryNode> {
        return this.historyStatus.value ? { status: { _in: this.historyStatus.value } } : {};
    }
}
