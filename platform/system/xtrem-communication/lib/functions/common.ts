import { Context, Datetime, datetime, SystemError } from '@sage/xtrem-core';
import { NotificationAttributes } from '../core-extension/queues';
import { SysNotification } from '../nodes/_index';

/**
 * Get context values notificationId topic & replyTopic
 * @param context
 * @returns
 */
export function getListenerInfoFromContext(context: Context): {
    notificationId: string;
    topic: string;
    replyTopic: string;
} {
    return {
        notificationId: context.getContextValue('notificationId') || '',
        topic: context.getContextValue('topic') || '',
        replyTopic: context.getContextValue('replyTopic') || '',
    };
}

export async function getAttributesFromContext(context: Context): Promise<NotificationAttributes> {
    const notificationId = context.getContextValue('notificationId') || '';
    const topic = context.getContextValue('topic') || '';
    const replyTopic = context.getContextValue('replyTopic') || '';
    const tenantId = context.tenantId || '';
    const user = await context.user;
    const userEmail = user?.email || '';
    const login = user?.email || '';
    const locale = user?.locale || '';
    const originId = context.getContextValue('originId') || '';
    const resumeToken = context.getContextValue('resumeToken') || '';

    return { tenantId, userEmail, login, locale, topic, notificationId, replyTopic, originId, resumeToken };
}

export async function getAttributesFromNotification(notification: SysNotification): Promise<NotificationAttributes> {
    return {
        tenantId: await notification.tenantId,
        userEmail: await notification.userEmail,
        login: await notification.login,
        locale: await notification.locale,
        topic: await notification.topic,
        notificationId: await notification.notificationId,
        replyId: await notification.replyId,
        replyTopic: await notification.replyTopic,
        originId: await notification.originId,
        resumeToken: await notification.resumeToken,
        extraAttributes: JSON.stringify(await notification.extraAttributes),
    };
}

export function dateAddUnit(duration: number, unit: Intl.RelativeTimeFormatUnit): Datetime {
    switch (unit) {
        case 'second':
        case 'seconds':
            return datetime.now().addSeconds(duration);
        case 'minute':
        case 'minutes':
            return datetime.now().addMinutes(duration);
        case 'hour':
        case 'hours':
            return datetime.now().addHours(duration);
        case 'day':
        case 'days':
            return datetime.now().addDays(duration);
        case 'week':
        case 'weeks':
            return datetime.now().addWeeks(duration);
        case 'month':
        case 'months':
            return datetime.now().addMonths(duration);
        default:
            throw new SystemError(`Not managed ${unit} `);
    }
}
