import { Context, NodeCreateData } from '@sage/xtrem-core';
import * as xtremCommunication from '..';

/**  error interrupted notResponding stopped success */
export const finalStatuses: xtremCommunication.enums.NotificationStatus[] = [
    'error',
    'interrupted',
    'notResponding',
    'stopped',
    'success',
];

export async function updateNotificationState(
    context: Context,
    notification: {
        _id: number;
        envelope: xtremCommunication.NotificationEnvelope;
        status: xtremCommunication.enums.NotificationStatus;
        error?: Error;
        result?: any;
    },
): Promise<number> {
    const message = notification.error?.message;

    await context.bulkUpdate(xtremCommunication.nodes.SysNotificationState, {
        set: {
            status: notification.status,
        },
        where: { _id: notification._id },
    });
    if (message) {
        await addNotificationLogEntry(context, {
            level: 'error',
            sysNotificationState: notification._id,
            message,
        });
    }
    if (notification.result) {
        await addNotificationLogEntry(context, {
            level: 'result',
            sysNotificationState: notification._id,
            data: notification.result,
        });
    }
    return notification._id;
}

async function addNotificationLogEntry(
    context: Context,
    log: NodeCreateData<xtremCommunication.nodes.SysNotificationLogEntry>,
): Promise<void> {
    await (await context.create(xtremCommunication.nodes.SysNotificationLogEntry, log)).$.save();
}

export async function createNotificationState(
    context: Context,
    notificationStatePayload: NodeCreateData<xtremCommunication.nodes.SysNotificationState>,
): Promise<number> {
    const notificationState = await context.create(
        xtremCommunication.nodes.SysNotificationState,
        notificationStatePayload,
    );
    await notificationState.$.save();
    return notificationState._id;
}
