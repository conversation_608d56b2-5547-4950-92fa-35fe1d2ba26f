export interface Parameter {
    _id: string;
    name: string;
    value: string;
}

export function getParametersNameValueFromString(parametersNameValue: string): Parameter[] {
    // JSON.stringify value of parameter, so it renders correctly on UI
    return Object.entries<string>(JSON.parse(parametersNameValue)).map(([key, value], i) => {
        return {
            _id: i.toString(),
            name: key,
            value: value != null && typeof value === 'object' ? JSON.stringify(value) : value,
        } as Parameter;
    });
}
