import { CommunicationState } from '@sage/xtrem-communication-api';
import { ColoredElement } from '@sage/xtrem-system-api';
import {
    colorfulPillPattern,
    colorfulPillPatternDefaulted,
} from '@sage/xtrem-system/build/lib/client-functions/color-pattern';

function communicationStateStatusColor(status: CommunicationState, coloredElement: ColoredElement) {
    switch (status) {
        case 'notSent':
            return colorfulPillPattern.filledCaution[coloredElement];
        case 'sent':
            return colorfulPillPattern.filledPositive[coloredElement];
        case 'received':
            return colorfulPillPattern.filledPositive[coloredElement];
        case 'success':
            return colorfulPillPattern.filledClosing[coloredElement];
        case 'retry':
            return colorfulPillPattern.filledCaution[coloredElement];
        case 'error':
            return colorfulPillPattern.filledNegative[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}



export function getLabelColorByStatus(enumEntry: string, status?: CommunicationState | null): {
    backgroundColor: string;
    borderColor: string;
    textColor: string;
} {
    const getColor = (coloredElement: ColoredElement) => {
        switch (enumEntry) {
            case 'CommunicationState':
                return communicationStateStatusColor(status as CommunicationState, coloredElement);

            default:
                return colorfulPillPatternDefaulted(coloredElement);
        }
    }

    return {
        backgroundColor: getColor('backgroundColor'),
        borderColor: getColor('borderColor'),
        textColor: getColor('textColor'),
    }
}

export function setBooleanStatusColors(booleanEntry: string, status: boolean, coloredElement: ColoredElement): string {
    switch (booleanEntry) {
        default:
            return colorfulPillPatternDefaulted(coloredElement);
    }
}
