import * as ui from '@sage/xtrem-ui';
import { ClientDiagnoseSeverity } from '@sage/xtrem-client';

export function formatError(page: ui.Page, error: string | (Error & { errors: Array<any> })): string {
    page.$.loader.isHidden = true;
    if (typeof error === 'string') {
        return error;
    }
    if (error.errors?.length) {
        const errorMessageLines: string[] = [];
        error.errors.forEach(e => {
            errorMessageLines.push(`**${e.message}**`);
            if (e.extensions?.diagnoses) {
                e.extensions.diagnoses.forEach((d: any) => {
                    if (
                        d.severity === ClientDiagnoseSeverity.error ||
                        d.severity === ClientDiagnoseSeverity.exception
                    ) {
                        errorMessageLines.push(` - ${d.message}`);
                    }
                });
            }
        });
        if (errorMessageLines.length === 1) {
            return `${errorMessageLines[0].replace(/\*/g, '')}`;
        }
        return errorMessageLines.join('\n');
    }

    return error.message;
}
