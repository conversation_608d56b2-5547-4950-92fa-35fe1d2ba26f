// This module provides the listener classes which invoke the listener static methods.
import {
    AnyValue,
    Application,
    AsyncResponse,
    Context,
    ContextOptions,
    InterruptException,
    IsolationOptions,
    LogicError,
    NodeFactory,
    Package,
    recordXtremEventToNewrelic,
    UserInfo,
} from '@sage/xtrem-core';
import { BaseListenerDecorator } from '../decorators';
import { Envelope } from '../queues';
import { logger } from '../utils';

function isUserInfo(user: string | UserInfo): user is UserInfo {
    if (user == null || typeof user !== 'object') return false;
    const props = Object.getOwnPropertyNames(user);
    return props.includes('_id') && props.includes('email');
}

/**
 * @internal
 * Extended decorator: name and definingPackage are set by NodeFactory initialization logic
 */
export interface InternalListenerDecorator extends BaseListenerDecorator {
    name: string;
    definingPackage: Package;
}

/** When running process we can extract some interesting attributes in the message that can be added to attributes of
 * the background transaction create in newrelic. Feel free to add more if available */
export interface RelevantAttributeForTracing {
    tenantId: string | 'unknown';
}

export interface ProcessMessageOptions {
    signal?: AbortSignal;
}

/**
 * @internal
 *  Base class for listener classes
 */
export abstract class BaseListener {
    /** The constructor of the node which carries the listener. This property is set by staticMemberDecorator */
    private readonly nodeConstructor: any;

    /** The factory which created the listener */
    protected _factory: NodeFactory;

    // name is magically set by staticMemberDecorator
    readonly name: string;

    // definingPackage is magically set by setOperationsDefiningPackage (in NodeFactoriesManager).
    definingPackage: Package;

    protected constructor(readonly decorator: BaseListenerDecorator) {}

    /** The factory which created the listener */
    get factory(): NodeFactory {
        if (!this._factory) throw new LogicError(`${this.name}: factory not registered`);
        return this._factory;
    }

    // eslint-disable-next-line class-methods-use-this
    protected validate(): void {
        // for now, nothing to validate
    }

    /** The full name of the listener */
    get fullName(): string {
        return `${this.nodeConstructor?.name || '<dynamic>'}.${this.name}`;
    }

    get isolationOptions(): IsolationOptions {
        return {
            isolationLevel: this.decorator.isolationLevel || 'low',
            isReadonly: false,
        };
    }

    /** The static method which handle the message */
    get staticMethod(): Function {
        const method = this.nodeConstructor[this.name];
        if (!method) throw new Error(`${this.fullName}: static method not found`);
        return method;
    }

    /** Registers the listener to its queue */
    abstract register(factory: NodeFactory): void;

    /** Abstract method which is called when a message is dequeued. It invokes the static method */
    abstract processMessage(
        application: Application,
        envelope: Envelope,
        options: ProcessMessageOptions,
    ): Promise<RelevantAttributeForTracing>;

    /** Registers the listener to its queue */
    abstract onError(application: Application, envelope: Envelope, error: Error): Promise<void>;

    async processError(application: Application, envelope: Envelope, error: Error): Promise<void> {
        logger.error(() => `${error} \n Message: ${JSON.stringify(envelope)}`);

        try {
            await this.onError(application, envelope, error);
        } catch (innerError) {
            if (error instanceof InterruptException) {
                throw error;
            }
            // we have to catch the error to stop the listener from stopping
            logger.error(() => `${innerError.stack} \n Message: ${JSON.stringify(envelope)}`);
        }
    }

    async process(application: Application, envelope: Envelope, options: ProcessMessageOptions): Promise<void> {
        const startedAt = Date.now();
        try {
            const processTracingAttributes = await this.processMessage(application, envelope, options);
            recordXtremEventToNewrelic({
                durationMs: Date.now() - startedAt,
                success: true,
                eventKind: this.fullName,
                tenantId: processTracingAttributes.tenantId,
            });
        } catch (error) {
            recordXtremEventToNewrelic({
                durationMs: Date.now() - startedAt,
                success: false,
                eventKind: this.fullName,
            });
            await this.processError(application, envelope, error);
        }
    }

    /**
     * Resolve the value of the startsReadOnly decorator attribute. The startsReadOnly decorator attribute can be either
     * a boolean or a callback that returns a boolean
     * @param application
     * @param tenantId
     * @param envelope
     * @returns
     */
    // eslint-disable-next-line require-await
    async getStartsReadOnly(application: Application, tenantId: string, envelope: Envelope): Promise<boolean> {
        const startsReadOnly = this.decorator.startsReadOnly;
        if (typeof startsReadOnly !== 'function') return !!this.decorator.startsReadOnly;

        return application.withReadonlyContext(tenantId, context =>
            startsReadOnly.call(this.factory.nodeConstructor, context, envelope),
        );
    }

    /**
     * Execute a body with either a writable and readonly context based on what the startsReadOnly attribute is resolved to.
     * @param tenantId
     * @param application
     * @param envelope
     * @param body
     * @param options
     * @returns
     */
    protected async withContext<T extends AnyValue | void>(
        tenantId: string,
        application: Application,
        envelope: Envelope,
        body: (ctx: Context) => AsyncResponse<T>,
        options: ContextOptions,
    ): Promise<T> {
        if (await this.getStartsReadOnly(application, tenantId, envelope)) {
            return application.withReadonlyContext(tenantId, context => body(context), options);
        }

        return application.requestFunnel(() =>
            application.withCommittedContext(tenantId, context => body(context), options),
        );
    }

    // eslint-disable-next-line require-await
    protected static async withWritableContext<T extends AnyValue>(
        context: Context,
        body: (ctx: Context) => AsyncResponse<T>,
    ): Promise<T> {
        // If the listener is configured to start with a readonly context then we need to run the logging of history in a writable context
        if (context.isWritable) {
            return body(context);
        }
        return context.runInWritableContext(writableContext => body(writableContext));
    }

    protected checkTenantAndUser(tenantId: string, user: string | UserInfo): void {
        if (!tenantId) {
            throw new Error(`${this.fullName}: cannot process message, no tenant id`);
        }
        if (tenantId.length !== 21) {
            throw new Error(`${this.fullName}: cannot process message, invalid tenant id: ${tenantId}`);
        }
        if (!user || (isUserInfo(user) && !user.email)) {
            throw new Error(`${this.fullName}: cannot process message: no user for tenant id ${tenantId}`);
        }
    }
}
