import {
    AnyRecord,
    AnyValue,
    Application,
    asyncArray,
    AsyncResponse,
    BatchLogEntry,
    Context,
    executeBulkAction,
    integer,
    InterruptException,
    LogicError,
    Node,
    NodeFactory,
    ObjectType,
    OperationError,
    OperationType,
    Parameter,
    PlainOperationDecorator,
    TextStream,
    TypeCache,
    UpdateSetFunctionSet,
    ValidationSeverity,
} from '@sage/xtrem-core';
import { Datetime } from '@sage/xtrem-date-time';
import { Diagnosis, SuspendException, withRethrow } from '@sage/xtrem-shared';
import { NotificationStatus } from '../../enums/notification-status';
import { SysNotificationState } from '../../nodes/sys-notification-state';
import { NotificationListenerDecorator } from '../decorators';
import { NotificationEnvelope } from '../queues';
import { parseOperationReturnValue } from '../utils';
import { ProcessMessageOptions, RelevantAttributeForTracing } from './base-listener';
import { BaseNotificationListener, ListenerContextOptions } from './base-notification-listener';

export interface AsyncMutationListenerDecorator extends NotificationListenerDecorator {
    mutationName: string;
    parameters: (Parameter<typeof Node, AnyValue> & { name: string })[];
    return: Parameter<typeof Node, AnyValue>;
    isBulk: boolean;
}

export type AsyncMutationStatus = NotificationStatus;
export interface AsyncMutationState<
    ResultT extends AnyValue = AnyValue,
    StatusT extends AsyncMutationStatus = AsyncMutationStatus,
> {
    status: StatusT;
    result?: ResultT;
    errorMessage?: string;
    errorStack?: TextStream;
    notificationContext?: { result: AnyValue };
    logMessages?: BatchLogEntry[];
}

/**
 * @internal
 * Listener which invokes a static method decorated with decorators.notificationListener
 */
export class AsyncMutationListener extends BaseNotificationListener {
    constructor(override readonly decorator: AsyncMutationListenerDecorator) {
        super(decorator);
        // mark as an operation listener
        this.isOperation = true;
    }

    get application(): Application {
        return this.factory.application;
    }

    static async trackAsyncMutation(
        context: Context,
        factory: NodeFactory,
        trackingId: string,
        op: PlainOperationDecorator,
    ): Promise<AsyncMutationState> {
        // We should be able to read the record with tryRead but we do not have a unique index yet.
        // SysNotificationHistory needs a review.
        // So, use a query for now
        const tracker = await context.tryRead(SysNotificationState, { notificationId: trackingId });
        // No tracker if the notification has not yet been routed and delivered to its listener.
        if (!tracker) return { status: 'pending' };
        const errorMessage = await tracker.message;
        const logMessages = await tracker.logs
            .map(async log => ({ level: await log.level, message: await log.message }))
            .toArray();
        const opReturn = op.return as unknown as { properties: { result: any } };
        const trackerStatus = await tracker.status;
        const result = parseOperationReturnValue(
            context,
            factory,
            op.name,
            opReturn.properties.result,
            await tracker.result,
        );
        switch (trackerStatus) {
            case 'success': {
                // static method was executed successfully and its transaction was committed
                return {
                    status: 'success',
                    result,
                    logMessages,
                };
            }
            case 'error': {
                // static method failed and its transaction was committed
                return { status: 'error', errorMessage, logMessages };
            }
            case 'stopRequested': {
                // stop was requested but mutation hasn't stopped yet
                return { status: 'stopRequested', errorMessage, logMessages };
            }
            case 'stopped': {
                // mutation has stopped
                return { status: 'stopped', errorMessage, result, logMessages };
            }
            default: {
                // transaction is still running
                return { status: 'running', logMessages };
            }
        }
    }

    private withWritableContext<T extends AnyValue>(
        body: (context: Context) => AsyncResponse<T>,
        options: ListenerContextOptions,
    ): Promise<T> {
        return this.application.withCommittedContext(options.tenantId, body, { ...options, isReadonly: false });
    }

    /**
     *
     * @param envelope
     * @param options
     * @returns _id of SysNotificationState created
     */
    private createTracker(envelope: NotificationEnvelope, options: ListenerContextOptions): Promise<integer> {
        // Create the notification history record in a separate transaction, so that
        // so that it can be queried while the mutation is running
        return this.withWritableContext(context => {
            return SysNotificationState.upsert(context, { envelope, status: 'running', operationName: this.fullName });
        }, options);
    }

    private runInWritableContext(
        context: Context,
        body: (context: Context) => Promise<any>,
        options: ListenerContextOptions,
    ): Promise<any> {
        if (context.isWritable) {
            return body(context);
        }
        return this.withWritableContext(subContext => body(subContext), options);
    }

    private async updateTracker(
        context: Context,
        sysId: integer,
        data: UpdateSetFunctionSet<SysNotificationState>,
        options: ListenerContextOptions,
    ): Promise<void> {
        // Update the notification history record in a separate transaction, so that
        // so that it can be queried while the mutation is running

        await this.runInWritableContext(
            context,
            subContext =>
                subContext.bulkUpdate(SysNotificationState, {
                    set: data,
                    where: { _id: sysId, status: 'running' },
                }),
            options,
        );
    }

    private async executeBulkMutation(context: Context, methodArgs: unknown[]): Promise<boolean> {
        const method = this.staticMethod;
        const nodeConstructor = this.factory.nodeConstructor;
        const filter = methodArgs[0] as string;
        const remainingArgs = methodArgs.slice(1);

        const onComplete = (
            this.factory.mutations.find(mutation => mutation.name === this.decorator.mutationName) as any
        )?.onComplete as Function;

        const numberProcessed = await executeBulkAction(context, filter, nodeConstructor, {
            body: (subContext: Context, instance: Node): Promise<any> => {
                return method.apply(nodeConstructor, [subContext, instance, ...remainingArgs]);
            },
            onComplete: onComplete
                ? (subContext: Context, results: any): Promise<void> =>
                      onComplete.apply(nodeConstructor, [subContext, results, ...remainingArgs])
                : undefined,
        });
        await context.batch.logMessage('info', `${nodeConstructor.name}: ${numberProcessed} records done.`);
        return true;
    }

    #operationProperty: any;

    get operationProperty(): any {
        if (this.#operationProperty) return this.#operationProperty;
        this.#operationProperty = OperationType.makeParameterProperty(
            new TypeCache(this.application),
            this.factory.name,
            OperationType.combineOperationParameters(this.decorator),
            this.name,
            'parameter',
        );
        return this.#operationProperty;
    }

    #operationFactory: any;

    get operationFactory(): any {
        if (this.#operationFactory) return this.#operationFactory;
        this.#operationFactory = OperationType.makeGraphQlFactory(
            new TypeCache(this.application),
            this.factory.name,
            this.operationProperty,
            'parameter',
        );
        return this.#operationFactory;
    }

    private async executeMutation(context: Context, envelope: NotificationEnvelope): Promise<AnyValue> {
        const argsObject = (await ObjectType.getInputValue(
            context,
            envelope.payload,
            this.operationFactory,
            this.operationProperty,
        )) as AnyRecord;

        const methodArgs = this.decorator.parameters.map(param => argsObject[param.name]);

        if (envelope.attributes.extraAttributes) {
            const extraAttributes = JSON.parse(envelope.attributes.extraAttributes);
            if (extraAttributes.securityFilters) context.setSecurityFilters(extraAttributes.securityFilters);
        }

        if (this.decorator.isBulk) return this.executeBulkMutation(context, methodArgs);
        return this.staticMethod.call(this.factory.nodeConstructor, context, ...methodArgs);
    }

    private async reply(
        context: Context,
        replyTopic: string,
        payload: AnyRecord,
        options: ListenerContextOptions,
    ): Promise<void> {
        await this.runInWritableContext(context, subContext => subContext.reply(replyTopic, payload), options);
    }

    private static mapDiagnoseToLogMessage(diagnose: Diagnosis): BatchLogEntry | null {
        switch (diagnose.severity) {
            case ValidationSeverity.exception:
                return { level: 'exception', message: diagnose.message };
            case ValidationSeverity.error:
                return { level: 'error', message: diagnose.message };
            case ValidationSeverity.warn:
                return { level: 'warning', message: diagnose.message };
            case ValidationSeverity.info:
                return { level: 'info', message: diagnose.message };
            default:
                return null;
        }
    }

    private async notifyCompletedMutation(
        context: Context,
        trackingId: string,
        status?: NotificationStatus,
        error?: string,
    ): Promise<void> {
        const title = context.localize(
            '@sage/xtrem-communication/completed_async_mutation_title',
            'Async mutation completed.',
        );
        const description = context.localize(
            '@sage/xtrem-communication/completed_async_mutation_description',
            'Completed: {{node}} / {{mutation}}',
            { node: (this as any).className, mutation: this.decorator.mutationName },
        );

        const topic = `${(this as any).className}/${this.decorator.mutationName}/completed`;
        await context.notifyUser({
            title,
            description,
            icon: 'info',
            level: 'info',
            shouldDisplayToast: false,
            actions: [],
            payload: {
                trackingId: trackingId.toString(),
                topic,
                status,
                error,
            },
        });
    }

    private async callMutationAndReply(
        context: Context,
        envelope: NotificationEnvelope,
        trackerSysId: integer,
        options: ListenerContextOptions,
    ): Promise<void> {
        const { replyTopic, replyId } = envelope.attributes;

        let mutationResult;
        try {
            mutationResult = await this.executeMutation(context, envelope);
        } catch (error) {
            const tracker = (
                await context.select(
                    SysNotificationState,
                    { status: true, notificationId: true },
                    { filter: { _id: trackerSysId } },
                )
            )[0];
            await this.notifyCompletedMutation(context, tracker.notificationId, 'error', error.message);

            // SuspendException means that an asyncMutation method did not complete the work but sent a notification instead.
            // Execution will proceed through one or more listeners.
            // The last listener will update the sysNotificationState with the status of the notification so
            // we just ignore the error here and let the transaction commit (so that the notification is sent).
            if (error instanceof SuspendException) return;
            throw error;
        }

        await asyncArray(context.diagnoses)
            .map(AsyncMutationListener.mapDiagnoseToLogMessage)
            .forEach(async logMessage => {
                // filter out the interop error diagnosis
                if (logMessage && !logMessage.message.includes('[InteropError]'))
                    await context.batch.logMessage(logMessage.level, logMessage.message);
            });

        const result = typeof mutationResult === 'string' ? JSON.stringify(mutationResult) : mutationResult;

        const tracker = (
            await context.select(
                SysNotificationState,
                { status: true, notificationId: true },
                { filter: { _id: trackerSysId } },
            )
        )[0];

        const defineStatus = async (currentStatus: NotificationStatus | undefined): Promise<NotificationStatus> => {
            switch (currentStatus) {
                case 'error':
                case 'stopped':
                case 'interrupted':
                    return currentStatus;
                case 'stopRequested':
                case 'running':
                    return 'success';
                default: {
                    const error = `invalid status: ${currentStatus}`;
                    await this.notifyCompletedMutation(context, tracker.notificationId, currentStatus, error);
                    throw new LogicError(error);
                }
            }
        };
        const status = await defineStatus(tracker?.status);

        await this.updateTracker(
            context,
            trackerSysId,
            {
                status,
                notificationContext: { result },
                result,
                timeEnded: Datetime.now(),
            },
            options,
        );

        await this.notifyCompletedMutation(context, tracker.notificationId, status);

        if (replyTopic) {
            await this.reply(context, replyTopic, { status, result, jobExecutionId: replyId }, options);
        }
    }

    private async handleMutationError(
        context: Context,
        envelope: NotificationEnvelope,
        trackerSysId: integer,
        error: OperationError,
        options: ListenerContextOptions,
    ): Promise<void> {
        if (error instanceof InterruptException) {
            throw error;
        }

        // Check if the asyncMutation has an onError method
        if (this.decorator.onError) {
            try {
                await this.decorator.onError.call(this.factory.nodeConstructor, context, envelope, error);
            } catch (errorFromOnError) {
                // There should not be an error in the onError method, but if there is, we log it and add it to the existing diagnoses.
                context.logger.error(
                    `Error thrown in asyncMutation onError method: ${this.decorator.mutationName} - ${errorFromOnError.message}`,
                );

                error.extensions.diagnoses = error.extensions.diagnoses ?? [];

                error.extensions.diagnoses.push({
                    message: errorFromOnError.message,
                    severity: ValidationSeverity.exception,
                    path: [],
                });
            }
        }

        const logMessages = error.extensions.diagnoses
            ?.map(AsyncMutationListener.mapDiagnoseToLogMessage)
            .filter(entry => !!entry) as BatchLogEntry[];

        const { replyTopic, replyId } = envelope.attributes;
        if (replyTopic) {
            await context.reply(replyTopic, {
                status: 'error',
                errorMessage: error.message.slice(0, 600),
                errorStack: new TextStream(error.stack || '', 'text/plain'),
                jobExecutionId: replyId,
            });
        }

        if (error.stack) {
            context.logger.error(`Error stack trace for notification ID: replyId=${replyId}\n${error.stack}`);
        }

        await asyncArray(logMessages).forEach(async logMessage => {
            // filter out the interop error diagnosis
            if (logMessage && !logMessage.message.includes('[InteropError]'))
                await context.batch.logMessage(logMessage.level, logMessage.message);
        });

        await this.updateTracker(
            context,
            trackerSysId,
            {
                status: 'error',
                message: error.message.slice(0, 600),
            },
            options,
        );
    }

    async processMessage(
        application: Application,
        envelope: NotificationEnvelope,
        processOptions: ProcessMessageOptions,
    ): Promise<RelevantAttributeForTracing> {
        const options = await this.getContextOptions(envelope, processOptions.signal);
        /** _id of sysNotificationState */
        const trackerSysId = await this.createTracker(envelope, options);

        const withContext = (body: (context: Context) => Promise<void>): Promise<void> =>
            this.withContext<void>(options.tenantId, application, envelope, body, options);

        try {
            await withContext(context =>
                withRethrow(
                    () => this.callMutationAndReply(context, envelope, trackerSysId, options),
                    OperationError.errorMapper(context, this.factory, this.decorator.mutationName),
                ),
            );
        } catch (error) {
            await this.withWritableContext(
                context => this.handleMutationError(context, envelope, trackerSysId, error, options),
                options,
            );
        }

        return { tenantId: options.tenantId };
    }
}
