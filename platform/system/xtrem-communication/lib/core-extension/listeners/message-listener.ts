// This module provides the listener classes which invoke the listener static methods.
import { AnyValue, Application, ContextOptions, datetime, Node, NodeFactory, TextStream } from '@sage/xtrem-core';
import { AsyncResponse, flat } from '@sage/xtrem-shared';
import { SysMessageHistory } from '../../nodes/sys-message-history';
import { ContextAttributes, MessageListenerDecorator } from '../decorators';
import { MessageEnvelope, SqsReceiveQueue } from '../queues';
import { QueueManager } from '../queues/queue-manager';
import { logger } from '../utils';
import { BaseListener, ProcessMessageOptions, RelevantAttributeForTracing } from './base-listener';

interface ParsedMessageEnvelope extends MessageEnvelope<string> {
    payload: any;
}

interface ParsedConfig {
    parsedEnvelope: ParsedMessageEnvelope;
    tenantId: string;
    contextOptions: ContextOptions;
}

/**
 * @internal
 * Listener which invokes a static method decorated with decorators.messageListener
 */
export class MessageListener<PayloadT = any> extends BaseListener {
    constructor(override readonly decorator: MessageListenerDecorator<typeof Node, PayloadT>) {
        super(decorator);
    }

    get queue(): SqsReceiveQueue {
        return this.decorator.queue();
    }

    register(factory: NodeFactory): void {
        this._factory = factory;
        this.validate();
        const queue = this.queue;
        if (queue.listeners.length > 0) {
            if (
                ['test', 'dev-tool'].includes(factory.application.applicationType ?? '') &&
                queue.listeners[0] === this
            ) {
                // This happens when mainly in unit tests that load more than 1 application
                logger.warn(`${this.fullName}: attempt register an already registered listener`);
                return;
            }
            throw new Error(`${this.fullName}: cannot have more than one listener on queue ${queue.name}`);
        }
        queue.listeners.push(this);
        QueueManager.addMessageListener(this);
    }

    private parsePayload(payload: string): any {
        return this.decorator.format === 'json' ? JSON.parse(payload) : payload;
    }

    private getTenantId(parsedEnvelope: ParsedMessageEnvelope): AsyncResponse<string> {
        return this.decorator.getTenantId.call(this.factory.nodeConstructor, parsedEnvelope);
    }

    /**
     * When we receive a messgae, we have cannot establish some key information required to create the context
     * to processe the inbound message. With this method we get the attributes relevant to create a context for the given tenant
     * using the getContextAttributes method on the listener decorator.
     * @param application
     * @param tenantId
     * @param parsedEnvelope
     * @returns
     */
    private getContextAttributes(
        application: Application,
        tenantId: string,
        parsedEnvelope: ParsedMessageEnvelope,
    ): Promise<ContextAttributes> {
        // Use the rootUser
        return application.asRoot.withReadonlyContext(
            tenantId,
            context =>
                this.decorator.getContextAttributes.call(
                    this.factory.nodeConstructor,
                    context,
                    parsedEnvelope,
                ) as ContextAttributes,
        );
    }

    /**
     * get the unique id of the message. If a getId method is provided on the listener decorator then use the values return from it
     * or use the deduplicationId of the message
     * @param application
     * @param tenantId
     * @param parsedEnvelope
     * @returns
     */
    private getId(
        application: Application,
        tenantId: string | null,
        parsedEnvelope: ParsedMessageEnvelope,
    ): Promise<string> {
        return application.asRoot.withReadonlyContext(tenantId, context => {
            return (
                this.decorator.getId
                    ? this.decorator.getId.call(this.factory.nodeConstructor, context, parsedEnvelope)
                    : parsedEnvelope.deduplicationId
            ) as string;
        });
    }

    private async getParsedConfig(application: Application, envelope: MessageEnvelope<string>): Promise<ParsedConfig> {
        // The SQS message Body is always a string therefore we can hard type the envelope payload passed in to string
        const parsedEnvelope = {
            ...envelope,
            payload: this.parsePayload(envelope.payload),
        } as ParsedMessageEnvelope;
        const tenantId = await this.getTenantId(parsedEnvelope);
        const { user, locale } = await this.getContextAttributes(application, tenantId, parsedEnvelope);

        this.checkTenantAndUser(tenantId, user);

        return {
            parsedEnvelope,
            tenantId,
            contextOptions: {
                auth: { login: user.email }, // If this is a demo tenant the persona will be defaulted to the users first persona
                source: 'listener',
                locale,
                contextValues: flat(envelope.attributes),
                ...this.isolationOptions,
            },
        };
    }

    async processMessage(
        application: Application,
        envelope: MessageEnvelope<string>,
        options: ProcessMessageOptions,
    ): Promise<RelevantAttributeForTracing> {
        const { parsedEnvelope, contextOptions, tenantId } = await this.getParsedConfig(application, envelope);
        const { signal } = options;
        await this.withContext(
            tenantId,
            application,
            parsedEnvelope,
            context =>
                this.staticMethod.call(this.factory.nodeConstructor, context, parsedEnvelope) as Promise<AnyValue>,
            { ...contextOptions, signal },
        );
        return { tenantId };
    }

    async onError(application: Application, envelope: MessageEnvelope<string>, error: Error): Promise<void> {
        const { parsedEnvelope, contextOptions, tenantId } = await this.getParsedConfig(application, envelope);
        await this.withContext(
            tenantId,
            application,
            parsedEnvelope,
            async context => {
                if (this.decorator.onError) {
                    await this.decorator.onError.call(this.factory.nodeConstructor, context, parsedEnvelope, error);
                } else {
                    const id = await this.getId(application, context.tenantId, parsedEnvelope);
                    const integrationSolution = this.decorator.integrationSolution || this.fullName;

                    await BaseListener.withWritableContext(context, writableContext =>
                        SysMessageHistory.createOrUpdateMessageHistory(writableContext, {
                            id,
                            integrationSolution,
                            receivedStamp: datetime.now(),
                            attributes: envelope.attributes,
                            receivedRequest: TextStream.fromJsonObject(envelope),
                            errorStack: error && TextStream.fromString(error.stack || error.message),
                            errorMessage: error && error.message,
                            status: 'error',
                        }),
                    );
                }
            },
            contextOptions,
        );
        Application.emitter.emit('messageOnError', { application });
    }
}
