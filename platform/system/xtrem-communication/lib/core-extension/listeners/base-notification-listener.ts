import {
    Application,
    ConfigManager,
    ContextOptions,
    InterruptException,
    LogicError,
    NodeFactory,
} from '@sage/xtrem-core';
import { flat } from '@sage/xtrem-shared';
import * as xtremSystem from '@sage/xtrem-system';
import { SysNotificationState } from '../../nodes/sys-notification-state';
import { NotificationListenerDecorator } from '../decorators';
import { NotificationEnvelope, NotificationQueue } from '../queues';
import { QueueManager } from '../queues/queue-manager';
import { getTopicPath } from '../utils';
import { BaseListener, ProcessMessageOptions, RelevantAttributeForTracing } from './base-listener';

export type ListenerContextOptions = ContextOptions & { tenantId: string };

/**
 * @internal
 * Listener which invokes a static method decorated with decorators.notificationListener
 */
export abstract class BaseNotificationListener extends BaseListener {
    private static readonly queues = new Map<string, NotificationQueue>();

    /**
     * Indicates if the notification listener is an operation listener
     */
    protected isOperation = false;

    constructor(override readonly decorator: NotificationListenerDecorator) {
        super(decorator);
    }

    get topic(): string {
        return getTopicPath(this.decorator.topic);
    }

    getQueueName(): string {
        const pack = this.definingPackage;
        const rawQueueName = this.decorator.queue ?? this.factory.nodeDecorator.queue ?? pack.queue;
        const config = ConfigManager.current;
        const app = config.app?.replace(/_/g, '-');
        const queueName =
            app && !rawQueueName.startsWith(`${app}--`) && this.definingPackage.application.applicationType !== 'test'
                ? `${app}--${rawQueueName}`
                : rawQueueName;
        return Application.limitSqsQueueName(queueName);
    }

    get queue(): NotificationQueue {
        const queue = BaseNotificationListener.queues.get(this.getQueueName());
        if (!queue) throw new LogicError(`${this.fullName}: notification queue not found`);
        return queue;
    }

    register(factory: NodeFactory): void {
        this._factory = factory;
        if (!this.definingPackage) this.definingPackage = factory.package;
        this.validate();
        const name = this.getQueueName();
        let queue = BaseNotificationListener.queues.get(name);
        if (!queue) {
            queue = new NotificationQueue({
                name,
                sqsName: factory.application.sqsQueueName(name),
                description: `notification queue for ${factory.package.name}`,
            });
            BaseNotificationListener.queues.set(name, queue);
            // Add new queue to package api, so that the start command will start reading from the queue
            if (!factory.package.api.queues) factory.package.api.queues = {};
            factory.package.api.queues[name] = queue;
        }
        queue.listeners.push(this);
        QueueManager.addNotificationListener(this);
    }

    protected async getContextOptions(
        envelope: NotificationEnvelope,
        signal: AbortSignal | undefined,
    ): Promise<ListenerContextOptions> {
        const { tenantId, userEmail, login, locale } = envelope.attributes;
        this.checkTenantAndUser(tenantId, userEmail);
        if (login) this.checkTenantAndUser(tenantId, userEmail);

        // At this point we have no context to determine if the tenant passed is demo
        // but we can infer that if the login and user are not the same the login user was impersonating a persona
        // and that persona is the userEmail.
        const persona = userEmail !== login ? userEmail : undefined;
        const apiId = xtremSystem.functions.matchApiEmail(login)?.[0];
        // We build the auth0 from the email in case of a api login because we check the auth0 in the creation of the context.
        // This as safe as trusting the login from the envelope.
        // An enhancment would be to use a signed JWT for the entire user properties (login, userEmail, auth0)
        const auth0 = apiId && `api|${apiId}`;
        const isReadonly = await this.getStartsReadOnly(this.factory.application, tenantId, envelope);
        return {
            auth: { auth0, login, persona },
            source: 'listener',
            tenantId,
            locale,
            contextValues: flat(envelope.attributes),
            ...this.isolationOptions,
            isReadonly,
            signal,
        };
    }

    abstract override processMessage(
        application: Application,
        envelope: NotificationEnvelope,
        options: ProcessMessageOptions,
    ): Promise<RelevantAttributeForTracing>;

    async onError(application: Application, envelope: NotificationEnvelope, error: Error): Promise<void> {
        if (error instanceof InterruptException) {
            throw error;
        }
        const options = await this.getContextOptions(envelope, undefined);
        await this.withContext(
            options.tenantId,
            application,
            envelope,
            async context => {
                if (this.decorator.onError) {
                    await this.decorator.onError.call(this.factory.nodeConstructor, context, envelope, error);
                } else {
                    await BaseListener.withWritableContext(context, writableContext =>
                        SysNotificationState.upsert(writableContext, {
                            envelope,
                            status: 'error',
                            error,
                            operationName: this.isOperation ? this.fullName : undefined,
                        }),
                    );
                }
            },
            options,
        );
        Application.emitter.emit('notificationOnError', {
            application,
            payload: envelope.payload,
        });
    }
}
