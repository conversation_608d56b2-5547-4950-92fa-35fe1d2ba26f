import { AnyValue, Application } from '@sage/xtrem-core';
import { NotificationEnvelope } from '../queues';
import { ProcessMessageOptions, RelevantAttributeForTracing } from './base-listener';
import { BaseNotificationListener } from './base-notification-listener';

/**
 * @internal
 * Listener which invokes a static method decorated with decorators.notificationListener
 */
export class NotificationListener extends BaseNotificationListener {
    async processMessage(
        application: Application,
        envelope: NotificationEnvelope,
        processOptions: ProcessMessageOptions,
    ): Promise<RelevantAttributeForTracing> {
        const options = await this.getContextOptions(envelope, processOptions.signal);
        await this.withContext(
            options.tenantId,
            application,
            envelope,
            context =>
                this.staticMethod.call(this.factory.nodeConstructor, context, envelope.payload) as Promise<AnyValue>,
            options,
        );
        return { tenantId: options.tenantId };
    }
}
