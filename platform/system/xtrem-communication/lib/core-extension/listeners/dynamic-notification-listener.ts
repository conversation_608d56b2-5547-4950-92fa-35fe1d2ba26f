import { AnyValue, Application, AsyncResponse, Context } from '@sage/xtrem-core';
import { SysDynamicListener } from '../../nodes/sys-dynamic-listener';
import { NotificationListenerDecorator } from '../decorators';
import { NotificationEnvelope } from '../queues';
import { RelevantAttributeForTracing } from './base-listener';
import { BaseNotificationListener } from './base-notification-listener';

export interface DynamicNotificationListenerDecorator extends NotificationListenerDecorator {
    application: Application;
    onBody(context: Context, envelope: NotificationEnvelope, payload: any): AsyncResponse<void>;
}

/**
 * @internal
 * Dynamic listener which invokes a callback
 */
export class DynamicNotificationListener extends BaseNotificationListener {
    async processMessage(
        application: Application,
        envelope: NotificationEnvelope,
    ): Promise<RelevantAttributeForTracing> {
        const decorator = this.decorator as DynamicNotificationListenerDecorator;
        const options = await this.getContextOptions(envelope, undefined);
        await this.withContext(
            options.tenantId,
            application,
            envelope,
            context => decorator.onBody.call(null, context, envelope, envelope.payload) as Promise<AnyValue>,
            options,
        );
        return { tenantId: options.tenantId };
    }
}

export function registerDynamicNotificationListener(options: DynamicNotificationListenerDecorator): void {
    const factory = options.application.getFactoryByConstructor(SysDynamicListener);
    new DynamicNotificationListener(options).register(factory);
}
