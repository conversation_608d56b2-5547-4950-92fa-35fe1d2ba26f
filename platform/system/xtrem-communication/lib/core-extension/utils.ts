import {
    Context,
    Logger,
    LogicError,
    Node,
    NodeFactory,
    NotificationTopic,
    OperationReturn,
    Parameter,
    parseTypeValue,
    TextStream,
    TypeName,
} from '@sage/xtrem-core';
import { AnyValue, Dict } from '@sage/xtrem-shared';
import { promisify } from 'util';

/** @internal */
export const logger = Logger.getLogger(__filename, 'communication');

/** @internal */
export function mergePrototypes(sourceClass: Function, targetClass: Function): void {
    const sourceProto = sourceClass.prototype as any;
    Object.entries(Object.getOwnPropertyDescriptors(sourceProto))
        .filter(([name]) => name !== 'constructor')
        .forEach(([name, desc]) => {
            const targetProto = targetClass.prototype as any;
            if (targetProto[name]) throw new Error(`${targetClass.name}.prototoype.${name}: invalid method override`);
            Object.defineProperty(targetProto, name, desc);
        });
}

export async function sleepMillis(ms: number): Promise<void> {
    await promisify(setTimeout)(ms);
}

function checkContentType(payload: string): string | undefined {
    // See https://docs.aws.amazon.com/AWSSimpleQueueService/latest/APIReference/API_SendMessage.html
    // We'll worry about surrogate pairs (above 0x10000) later
    if (/[^\t\r\n\u0020-\ud7ff\ue000-\ufffd]/.test(payload))
        throw new Error(`message payload contains invalid characters: ${payload}`);

    try {
        JSON.parse(payload);
        return 'application/json';
        // eslint-disable-next-line no-empty
    } catch (e) {}
    return undefined;
}

// Convert a message payload to a string
export function convertToTextStream(payload: any): TextStream {
    if (payload == null) return new TextStream('');
    return typeof payload === 'string'
        ? TextStream.fromString(payload, checkContentType(payload))
        : TextStream.fromJsonObject(payload);
}

/**
 * Utility function to extract the topic name from a string | NotificationTopic.
 */
export function getTopicPath(topic: string | NotificationTopic): string {
    if (topic instanceof NotificationTopic) {
        // Check that all topics that we use have been verified when the application is loaded.
        topic.checkVerified();
        return topic.name;
    }

    // Otherwise it is a string and we only allow relative topics
    if (topic.startsWith('/'))
        throw new LogicError('Absolute topic cannot be passed as a string; you must pass a NotificationTopic object');
    return topic;
}

/**
 * Ensures that the input is returned as a `Parameter` object.
 *
 * If a `TypeName` (string) is provided, it wraps it in a `Parameter` object with the `type` property set.
 * If a `Parameter` object is provided, it is returned as-is.
 *
 * @param parameterOrTypeName - The input value, which can be either a `Parameter` object or a `TypeName` (string).
 * @returns A `Parameter` object representing the input.
 */
export function makeParameter(parameterOrTypeName: Parameter | TypeName): Parameter {
    if (typeof parameterOrTypeName === 'string') return { type: parameterOrTypeName } as Parameter;
    return parameterOrTypeName;
}

/**
 * Parse the value returned on the API response to the format on the operation decorator `return` attribute of a custom operation
 * @param factory
 * @param operation
 * @param operationReturn
 * @param value
 * @returns
 */
export function parseOperationReturnValue(
    context: Context,
    factory: NodeFactory,
    operationName: string,
    operationReturn: OperationReturn<typeof Node, any>,
    value: any,
): AnyValue {
    if (value == null) return null;
    const returnParam = makeParameter(operationReturn);
    switch (returnParam.type) {
        case 'boolean':
        case 'decimal':
        case 'date':
        case 'integer':
        case 'string':
        case 'dateRange':
        case 'datetimeRange':
        case 'time':
        case 'datetime':
        case 'json':
        case 'enum':
            return parseTypeValue(returnParam.type, value, `result of ${factory.name}.${operationName}`);
        case 'textStream': {
            return TextStream.fromJsonObject(value); // Convert the value to a TextStream.
        }
        case 'object': {
            if (typeof value !== 'object')
                throw new LogicError(
                    `${factory.name}.${operationName}: invalid value for object return value. ${value}`,
                );

            if (returnParam.properties) {
                return Object.keys(value).reduce((r, k) => {
                    const param = returnParam.properties[k] as OperationReturn<typeof Node, any>;
                    if (!param) return r;
                    const v = value[k];
                    r[k] = parseOperationReturnValue(context, factory, operationName, param, v);
                    return r;
                }, {} as Dict<AnyValue>);
            }
            return value;
        }
        case 'array':
            if (!Array.isArray(value))
                throw new LogicError(`${factory.name}.${operationName}: invalid array return value`);

            if (returnParam.item) {
                const item = returnParam.item as OperationReturn<typeof Node, any>;
                return value.map(v => {
                    return parseOperationReturnValue(context, factory, operationName, item, v);
                });
            }
            return value;
        case 'reference':
        case 'instance': {
            const targetFactory = context.application.getFactoryByConstructor(returnParam.node());
            const data = parseNodeOutputData(context, targetFactory, operationName, value);
            return context.tryRead(targetFactory.nodeConstructor, data);
        }
        default:
            throw new LogicError(`${factory.name}.${operationName}: invalid return type ${returnParam.type}`);
    }
}

/**
 * Parses the output data from a node operation and converts its properties
 * according to the key properties defined in the target factory.
 *
 * @param context - The execution context in which the operation is performed.
 * @param targetFactory - The factory describing the node and its key properties.
 * @param operationName - The name of the operation being performed (e.g., 'delete', 'update').
 * @param outputData - The raw output data returned from the node operation.
 * @returns A dictionary mapping property names to their parsed values. If the operation is 'delete',
 *          returns the output data as-is.
 */
export function parseNodeOutputData(
    context: Context,
    targetFactory: NodeFactory,
    operationName: string,
    outputData: any,
): Dict<AnyValue> {
    if (operationName === 'delete') return outputData;
    return targetFactory.keyProperties.reduce((r, keyProp) => {
        r[keyProp.name] = parseTypeValue(
            keyProp.type,
            outputData[keyProp.name],
            `result of ${targetFactory.name}.${operationName}`,
        );
        return r;
    }, {} as Dict<AnyValue>);
}
