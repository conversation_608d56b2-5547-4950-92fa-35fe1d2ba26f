// This module provides the extension methods (send, notify) for the Context class
import { Context, Dict, LogicError, NotificationTopic, PubSub, SqsSendQueue, TextStream } from '@sage/xtrem-core';
import { SysMessage, SysNotification } from '../../nodes/_index';
import { MessageEnvelope } from '../queues';
import { convertToTextStream, getTopicPath } from '../utils';
import { BatchContext } from './batch-context';

// Utility to cast a ContextExtension to Context
function cast(context: ContextExtension): Context {
    return context as any as Context;
}

export interface NotificationOptions {
    replyTopic?: string;
    replyId?: string;
    extraAttributes?: Dict<any>;
}

/**
 * This class provides the `send` and `notify` methods which will be added to the Context class.
 *
 * This class is abstract and will never be instantiated.
 * Its methods will be merged into Context.prototype
 */
export abstract class ContextExtension {
    /**
     * Send an event to the notification middleware.
     *
     * @param constructor
     * @param event
     * @param payload
     */
    private async sendNotification<PayloadT extends object>(
        topic: string,
        payload: PayloadT,
        options?: NotificationOptions,
    ): Promise<string> {
        // The event is not directly sent to the notification queue(s).
        // It is written (enqueue) into the sys_notification table,
        // then the routing service will dequeue and send it to the queue(s)
        // see diagram in queue.ts

        const that = cast(this);
        if (!that.isWritable) throw new Error('notify is allowed only in the context of a mutation');

        const userEmail = (await that.user)!.email;

        const notification = await that.create(SysNotification, {
            tenantId: that.tenantId!,
            userEmail,
            login: (await that.loginUser)?.email || userEmail,
            locale: that.currentLocale,
            topic,
            payload: TextStream.fromJsonObject(payload),
            // cloudflare request- cloudflare:<cloudflare uid>
            // graphql request - graphql:<containerId>
            // container request - container:<containerId>
            originId: that.originId,
            // token to resume a suspended workflow action
            resumeToken: that.getContextValue('resumeToken') || '',
            // notificationId is set by the defaultValue on the property
            replyId: options?.replyId || '',
            replyTopic: options?.replyTopic || '',
            extraAttributes: { ...(options?.extraAttributes ?? {}), securityFilters: that.securityFilters ?? {} },
        });
        await notification.$.save();
        await PubSub.publish(that, 'notification_queued', { topic, tenantId: that.tenantId });

        return notification.notificationId;
    }

    /**
     * Send an event to the notification middleware.
     *
     * @param constructor
     * @param topic
     * @param payload
     */
    notify<PayloadT extends object>(
        topic: string | NotificationTopic,
        payload: PayloadT,
        options?: NotificationOptions,
    ): Promise<string> {
        return this.sendNotification<PayloadT>(getTopicPath(topic), payload, options);
    }

    /**
     * Send an event to the notification middleware as a reply to another event.
     *
     * @param constructor
     * @param event
     * @param payload
     */
    reply<PayloadT extends object>(
        topic: string | NotificationTopic,
        payload: PayloadT,
        options?: NotificationOptions,
    ): Promise<string> {
        const that = cast(this);
        const notificationId = that.getContextValue('notificationId');
        if (options?.replyId == null && notificationId == null)
            return Promise.reject(new LogicError('A reply notification requires a replyId'));
        return this.sendNotification<PayloadT>(getTopicPath(topic), payload, {
            ...options,
            replyId: options?.replyId || notificationId,
        });
    }

    /**
     * Send a message to an SQS queue
     *
     * @param queue
     * @param payload
     */
    async send<PayloadT>(queue: SqsSendQueue, envelope: MessageEnvelope<PayloadT>): Promise<void> {
        const that = cast(this);
        if (!that.isWritable) throw new Error('send is allowed only in the context of a mutation');

        const message = await that.create(SysMessage, {
            tenantId: that.tenantId!,
            queue: queue.name,
            payload: convertToTextStream(envelope.payload),
            attributes: envelope.attributes,
        });
        await message.$.save();
        await PubSub.publish(that, 'message_queued', { queue, tenantId: that.tenantId });
    }

    /**
     * Returns a batch object with additional API to log messages or stop the batch.
     */
    get batch(): BatchContext {
        const that = cast(this);
        const trackingId = that.getContextValue('notificationId');
        if (!trackingId) {
            that.logger.warn('invalid context.batch call: notificationId missing');
        }
        return new BatchContext(that, trackingId || '');
    }
}
