// This module merges the communication APIs (listeners, queues, context methods) into @sage/xtrem-core
import {
    Application,
    Context,
    CoreHooks,
    Dict,
    LogicError,
    Node,
    NodeFactory,
    decorators,
    gracefulShutdown,
} from '@sage/xtrem-core';
import { pascalCase } from '@sage/xtrem-shared';
import * as xtremCommunication from '../..';
import { CommunicationDecorators, asyncMutation, communicationDecorators } from '../decorators';
import { bulkMutation } from '../decorators/bulk-mutation-decorator';
import { AsyncMutationListener } from '../listeners/async-mutation-listener';
import {
    NotificationQueue as NotificationQueueInternal,
    Queue,
    SqsReceiveQueue as SqsReceiveQueueInternal,
    SqsSendQueue as SqsSendQueueInternal,
} from '../queues';
import { QueueManager } from '../queues/queue-manager';
import { logger, mergePrototypes } from '../utils';
import { ContextExtension } from './context-extension';

// Add the listeners to the `decorators` variable
Object.assign(decorators, communicationDecorators);

// Add the queue classes
Object.assign(require('@sage/xtrem-core'), {
    SqsSendQueue: SqsSendQueueInternal,
    SqsReceiveQueue: SqsReceiveQueueInternal,
    NotificationQueue: NotificationQueueInternal,
});

// Add `send` and `notify` methods to the Context class
mergePrototypes(ContextExtension, Context);

export function startService(application: Application): void {
    if (application.startOptions.channels.includes('listeners')) {
        // We need to start the listeners on all service packages started and all there service package dependencies
        const listenerPackages = new Set(application.servicePackagesStarted);
        application.servicePackagesStarted
            .filter(pkg => !pkg.isMain)
            .forEach(pkg => {
                pkg.allDependencies.filter(dep => dep.hasListeners).forEach(dep => listenerPackages.add(dep));
            });
        logger.info('Starting listeners');
        QueueManager.start(application, [...listenerPackages]);
    }
}

/**
 * start the queue manager and listener
 * Used for unit tests
 * @param application
 */
function startQueueManager(application: Application): void {
    QueueManager.messageListeners.forEach(listener => listener.queue.listen(application));
    QueueManager.notificationListeners.forEach(listener => listener.queue.listen(application));
}

const staticMutationFunctions = {
    async bulkDelete(_context: Context, instance: Node): Promise<void> {
        await instance.$.delete();
    },

    // see later for bulkUpdate which is also currently defined in platform/back-end/xtrem-core/lib/graphql/mutations/node-mutations.ts
    // async bulkUpdate(_context: Context, instance: Node, data: NodeCreateData<any>): Promise<void> {
    //     await instance.$.set(data);
    //     await instance.$.save();
    // },
} as Dict<Function>;

// Register the queue manager - used only in tests
Application.emitter.once('testListen', (application: Application) => {
    // start QueueManager first because it loads routing.json
    startQueueManager(application);
});

/**
 * SIGTERM received
 * To emulate this localy kill -15 **pidProcess**
 */
gracefulShutdown.once('f', async () => {
    // Allow graceful shutdown of message queue listeners
    QueueManager.stop();

    while (!QueueManager.stopped()) {
        logger.info('Waiting for listeners to stop');
        await new Promise<void>(resolve => {
            setTimeout(resolve, 5000);
        });
    }

    gracefulShutdown.emit('stopped');
});

// Register communication callbacks in CoreHooks
// This will be cleaned up after we fix the package dependencies.
Object.assign(CoreHooks.communicationManager, {
    // This method is incomplete here - xtrem-scheduler overwrites it with a complete implementation
    notify(context, topic, payload) {
        return context.notify(topic, payload);
    },
    // This method is incomplete here - xtrem-scheduler overwrites it with a complete implementation
    startAsyncMutation(context, factoryName, mutationName, parameterValues) {
        const topic = `${factoryName}/${mutationName}/start`;
        logger.warn(`${topic}: incomplete implementation of startAsyncMutation - jobExecution record not created`);
        return context.notify(topic, parameterValues);
    },
    async requestStop(context, trackingId, reason) {
        // For now we have to update both tables - Refactoring will fix this.
        await context.bulkUpdate(xtremCommunication.nodes.SysNotificationState, {
            set: {
                status: 'stopRequested',
                message: reason,
            },
            where: {
                notificationId: trackingId,
            },
        });
    },
    async requestUserNotification(context, trackingId) {
        // For now we have to update both tables - Refactoring will fix this.
        await context.bulkUpdate(xtremCommunication.nodes.SysNotificationState, {
            set: {
                isUserNotificationRequested: true,
            },
            where: {
                notificationId: trackingId,
            },
        });
    },
    async setStopped(context, trackingId) {
        // For now we have to update both tables - Refactoring will fix this.
        await context.bulkUpdate(xtremCommunication.nodes.SysNotificationState, {
            set: {
                status: 'stopped',
            },
            where: {
                notificationId: trackingId,
            },
        });
    },
    trackAsyncMutation(context, factory, trackingId, op) {
        return AsyncMutationListener.trackAsyncMutation(context, factory, trackingId, op);
    },
    isStopRequested(context) {
        return context.batch.isStopRequested();
    },

    extendClassDecorator(factory: NodeFactory) {
        Object.keys(staticMutationFunctions).forEach(mutationName => addBulkMutation(factory, mutationName));
        if (
            factory.canExport &&
            !factory.isAbstract &&
            (factory.nodeDecorator.storage === 'sql' || isSqlBasedFactoryDecorators(factory.decorators))
        )
            addAsyncExportMutation(factory);
    },

    async updateProgress(context, partialProgress) {
        await context.batch.updateProgress(partialProgress);
    },

    async logErrorBatchMessage(context, message) {
        await context.batch.logMessage('error', message);
    },
} as Partial<typeof CoreHooks.communicationManager>);

// Usage of any here is a work around for the build error if I use FactoryDecoration from xtrem-core run time
function isSqlBasedFactoryDecorators(factoryDecorators: any): boolean {
    if (factoryDecorators.superDecorators) return isSqlBasedFactoryDecorators(factoryDecorators.superDecorators);
    return factoryDecorators.storage === 'sql';
}

function addAsyncExportMutation(factory: NodeFactory): void {
    const { nodeConstructor, decorators: classDecorators } = factory;
    const mutationName = 'asyncExport';
    const ctor = nodeConstructor as any as Dict<Function>;
    if (ctor[mutationName]) {
        if (!decorators.subNode) throw new LogicError(`${nodeConstructor.name} already has a ${mutationName} method`);
        return;
    }
    const mutation = function asyncExport(context: Context, id: string, filter: string): Promise<string> {
        return CoreHooks.importExportManager.executeExport(context, id, 'csv', filter, '{}');
    };

    ctor[mutationName] = mutation.bind(nodeConstructor);
    classDecorators.mutations = classDecorators.mutations ?? [];
    asyncMutation({
        isPublished: true,
        parameters: [
            {
                name: 'id',
                type: 'string',
            },
            {
                name: 'filter',
                type: 'string',
            },
        ] as any,
        return: 'string' as any,
        queue: 'import-export',
        startsReadOnly: true,
    })(nodeConstructor, mutationName);
}

function addBulkMutation(factory: NodeFactory, mutationName: string): void {
    const { nodeConstructor, decorators: classDecorators } = factory;
    const mutation = staticMutationFunctions[mutationName];
    if (!mutation) {
        throw new LogicError(
            `${mutationName} is not a supported bulk function. Supported functions are: ${Object.keys(
                staticMutationFunctions,
            )}.`,
        );
    }
    const canBulkName = `can${pascalCase(mutationName)}`;
    if (classDecorators[canBulkName]) {
        const ctor = nodeConstructor as any as Dict<Function>;
        if (ctor[mutationName]) {
            // TODO: Review this throw: throw new LogicError(`${nodeConstructor.name} already has a ${mutationName} method`);
        }
        ctor[mutationName] = mutation.bind(nodeConstructor);
        classDecorators.mutations = classDecorators.mutations ?? [];
        bulkMutation({
            isPublished: true,
        })(nodeConstructor, mutationName);
    }
}

// Fix the type definitions exported by `@sage/xtrem-core`
declare module '@sage/xtrem-core' {
    export interface ContextAugmentation extends ContextExtension {}
    export interface Decorators extends CommunicationDecorators {}
    export interface PackageApi {
        queues?: Dict<Queue>;
    }
    export class SqsSendQueue extends SqsSendQueueInternal {}
    export class SqsReceiveQueue extends SqsReceiveQueueInternal {}
    export class NotificationQueue extends NotificationQueueInternal {}
}
