// This module provides the extension methods (send, notify) for the Context class
import { <PERSON><PERSON><PERSON>ogLevel, ConfigManager, Context, Datetime, InterruptException } from '@sage/xtrem-core';
import { NotificationProgress } from '@sage/xtrem-shared';
import * as xtremCommunication from '../..';
import { loggers } from '../../functions/logger';
import { QueueManager } from '../queues/queue-manager';

type Job = {
    status: xtremCommunication.enums.NotificationStatus;
    progress: NotificationProgress;
    timeStarted: Datetime;
    notificationId: string;
    isUserNotificationRequested?: boolean;
};

/**
 * Class for context.batch.
 * It provides extra API which is only available during batch mutations.
 */
export class BatchContext {
    /** When a tracking id is not defined we are not in a batch context */
    isBatch: boolean;

    private lastChecked = 0;

    constructor(
        readonly context: Context,
        readonly trackingId: string,
    ) {
        this.isBatch = !!trackingId;
    }

    getRunningJobs(): Promise<Job[]> {
        return this.context.application.asRoot.withReadonlyContext(this.context.tenantId, subContext => {
            return subContext.select(
                xtremCommunication.nodes.SysNotificationState,
                {
                    status: true,
                    progress: true,
                    timeStarted: true,
                    notificationId: true,
                    isUserNotificationRequested: true,
                },
                { filter: { status: { _nin: ['success', 'stopped', 'interrupted'] } } },
            );
        });
    }

    private async getJob(): Promise<Job | undefined> {
        if (!this.isBatch || !this.trackingId) {
            return undefined;
        }
        const runningJobs = await this.context.getCachedValue({
            category: 'JOBS',
            key: 'running-jobs',
            getValue: async () => ({ value: await this.getRunningJobs() }),
            cacheInMemory: true,
            ttlInSeconds: ConfigManager.current.interop?.runningJobListTtlInSeconds ?? 10,
        });

        const job = runningJobs.find(findJob => findJob.notificationId === this.trackingId);
        if (job) {
            return job;
        }

        const finishedJob = await this.context.application.asRoot.withReadonlyContext(
            this.context.tenantId,
            async subContext => {
                return (
                    await subContext.select(
                        xtremCommunication.nodes.SysNotificationState,
                        {
                            status: true,
                            progress: true,
                            timeStarted: true,
                            notificationId: true,
                            isUserNotificationRequested: true,
                        },
                        { filter: { notificationId: this.trackingId } },
                    )
                ).at(0);
            },
        );
        if (finishedJob) {
            return finishedJob;
        }
        return undefined;
    }

    /** Returns true if user requested to stop the batch operation */
    async isStopRequested(): Promise<boolean> {
        if (!this.isBatch) {
            return Promise.resolve(false);
        }

        if (QueueManager.isInterruptRequested) {
            await this.context.application.asRoot.withCommittedContext(this.context.tenantId, async subContext => {
                await subContext.bulkUpdate(xtremCommunication.nodes.SysNotificationState, {
                    set: {
                        status: 'interrupted',
                    },
                    where: {
                        notificationId: this.trackingId,
                    },
                });
            });
            await this.logMessage(
                'exception',
                this.context.localize('@sage/xtrem-communication/interrupt_requested', 'Interrupt requested'),
            );
            throw new InterruptException();
        }

        return (await this.getJob())?.status === 'stopRequested';
    }

    /** Returns true if user requested to a notification for the batch operation */
    async isUserNotificationRequested(): Promise<boolean> {
        if (!this.isBatch) {
            return Promise.resolve(false);
        }

        return (await this.getJob())?.isUserNotificationRequested ?? false;
    }

    async confirmStop(): Promise<void> {
        await this.context.application.asRoot.withCommittedContext(this.context.tenantId, async subContext => {
            await subContext.bulkUpdate(xtremCommunication.nodes.SysNotificationState, {
                set: {
                    status: 'stopped',
                    timeEnded: Datetime.now(),
                },
                where: {
                    notificationId: this.trackingId,
                },
            });
        });
    }

    /**
     *  Logs a message which will be stored in the sys_log_entry table
     *  If sysNotificationState don't exist log on basic logger
     * @param level  'test' | 'info' | 'warning' | 'result' | 'error' | 'exception'
     * @param messageIn  will be truncated to 4000 char
     * @param details json type
     * @returns
     */
    async logMessage(
        level: BatchLogLevel,
        messageIn: string,
        details?: { data?: any; spanContext?: any },
    ): Promise<void> {
        if (!this.isBatch) {
            this.context.logger.debug(() => `${messageIn}`);
            return;
        }
        if (
            await this.context.exists(xtremCommunication.nodes.SysNotificationState, {
                notificationId: this.trackingId,
            })
        ) {
            const { maxLength } = xtremCommunication.dataTypes.logMessage;
            const message = messageIn.slice(0, messageIn.length > maxLength ? maxLength : messageIn.length);
            await this.context.application.asRoot.withCommittedContext(this.context.tenantId, async subContext => {
                await (
                    await subContext.create(xtremCommunication.nodes.SysNotificationLogEntry, {
                        sysNotificationState: { notificationId: this.trackingId },
                        level,
                        message,
                        data: details?.data,
                        spanContext: details?.spanContext,
                    })
                ).$.save();
            });
        } else {
            this.context.logger.debug(() => `Batch: [${this.trackingId}] ${messageIn}`);
        }
    }

    async getProgress(): Promise<
        NotificationProgress & { startTime: Datetime; isUserNotificationRequested?: boolean }
    > {
        const job = (await this.getJob()) ?? {
            progress: { detail: '', errorCount: 0, phase: '', successCount: 0, totalCount: 0 },
            timeStarted: Datetime.now(),
            isUserNotificationRequested: false,
        };

        return {
            ...job.progress,
            startTime: job.timeStarted,
            isUserNotificationRequested: job.isUserNotificationRequested,
        };
    }

    /** Update the progression :
     *  NotificationProgress :
     *      - totalCount: number;
     *      - successCount: number;
     *      - errorCount: number;
     *      - phase: string;
     *      - detail: string;
     */
    async updateProgress(partialProgress: Partial<NotificationProgress>): Promise<void> {
        if (Date.now() < this.lastChecked + 100) {
            return;
        }
        this.lastChecked = Date.now();

        const job = await this.getJob();
        if (!job) {
            loggers.batchContext.info(() => `${this.trackingId} No job found : ${JSON.stringify(partialProgress)}`);
            return;
        }

        const currentProgress: NotificationProgress = job.progress ?? {
            detail: '',
            errorCount: 0,
            phase: '',
            successCount: 0,
            totalCount: 0,
        };
        const progress: NotificationProgress = {
            ...currentProgress,
            ...partialProgress,
        };

        await this.context.application.asRoot.withCommittedContext(this.context.tenantId, async subContext => {
            await subContext.bulkUpdate(xtremCommunication.nodes.SysNotificationState, {
                set: {
                    progress,
                },
                where: {
                    notificationId: this.trackingId,
                },
            });
        });
    }

    /** Set the timeEnded */
    async end(): Promise<void> {
        await this.context.application.asRoot.withCommittedContext(this.context.tenantId, async subContext => {
            await subContext.bulkUpdate(xtremCommunication.nodes.SysNotificationState, {
                set: {
                    timeEnded: Datetime.now(),
                },
                where: {
                    notificationId: this.trackingId,
                },
            });
        });
    }

    get notificationStateLink(): string {
        const base64Key = Buffer.from(JSON.stringify({ _id: `#${this.trackingId}` })).toString('base64');
        return `@sage/xtrem-communication/SysNotificationState/${base64Key}`;
    }
}
