// This module provides the listener decorators.
import {
    AnyValue,
    AsyncResponse,
    BaseMutationDecorator,
    Context,
    Node,
    Parameter,
    staticMemberDecorator,
    StaticMemberDecoratorFunction,
    StaticThis,
} from '@sage/xtrem-core';
import { AsyncMutationListener, AsyncMutationListenerDecorator } from '../listeners/async-mutation-listener';
import { NotificationEnvelope } from '../queues';

export interface AsyncMutationDecorator<This extends StaticThis<Node>, K extends keyof This>
    extends BaseMutationDecorator<This, K> {
    /** Can the mutation be scheduled as a batch job */
    isSchedulable?: boolean;
    /** This function is called when an error occurs in the asyncMutation. It should be used to update
     * record status or other information. A user notification will be sent automatically if the
     * user has requested it. The error should not be re-thrown as this is managed by the system.
     * @param context writable context
     * @param envelope notification envelope
     * @param error Error where applicable
     */
    onError?(this: typeof Node, context: Context, envelope: NotificationEnvelope, error: Error): AsyncResponse<void>;
    /**
     * The specific name of the queue used for notifications.
     * If not set, the queue of the package will be used.
     */
    queue?: string;
}

/**
 * registers an asyncMutation or bulkMutation into the node constructor
 * @internal
 */
export function anyAsyncMutation<This extends StaticThis<Node>, K extends keyof This>(
    arg: AsyncMutationDecorator<This, K>,
    options?: { isBulk?: boolean },
): StaticMemberDecoratorFunction<typeof Node> {
    // We have to register the async mutation three times in the node constructor:
    // - as a graphql mutation which will initiate the request by sending a notification
    // - as a graphql query which will track the progress of the operation
    // - as a notification listener which will catch the notification (coming from graphql or from the batch scheduler).
    // The static method is executed by the notification listener.
    const operationKind = options?.isBulk ? 'bulkMutation' : 'asyncMutation';
    const mutationFunction = staticMemberDecorator<typeof Node, any>('mutations', operationKind);
    const queryFunction = staticMemberDecorator<typeof Node, any>('queries', 'asyncTrackerQuery');
    const listenerFunction = staticMemberDecorator<typeof Node, any>('notificationListeners', 'asyncMutationListener');
    // Return a wrapper function that calls these functions
    return (nodeConstructor, name) => {
        const requestUserNotificationArg = {
            ...arg,
            startsReadOnly: false,
            parameters: [{ name: 'trackingId', type: 'string', isMandatory: true }],
            return: { type: 'boolean' },
            action: 'requestUserNotification',
        };
        mutationFunction(requestUserNotificationArg)(nodeConstructor, name);

        // The mutation's input parameters come from arg.
        // It returns an object with a trackingId property.
        const startArg = {
            ...arg,
            startsReadOnly: false,
            return: { type: 'object', properties: { trackingId: 'string' } },
            action: 'start',
        };
        mutationFunction(startArg)(nodeConstructor, name);

        const stopArg = {
            ...arg,
            startsReadOnly: false,
            parameters: [
                { name: 'trackingId', type: 'string', isMandatory: true },
                { name: 'reason', type: 'string' },
            ],
            return: { type: 'boolean' },
            action: 'stop',
        };
        mutationFunction(stopArg)(nodeConstructor, name);

        // The tracking query has a single parameter, the tracking id.
        // It returns a tracker object with status and either result or an error message.
        // The return type is given by arg.
        const trackArg = {
            ...arg,
            parameters: [{ name: 'trackingId', type: 'string', isMandatory: true }],
            return: {
                type: 'object',
                properties: {
                    status: 'string',
                    result: arg.return,
                    errorMessage: 'string',
                    logMessages: {
                        type: 'array',
                        item: {
                            type: 'object',
                            properties: {
                                level: 'string',
                                message: 'string',
                            },
                        },
                    },
                },
            },
            action: 'track',
        };
        queryFunction(trackArg)(nodeConstructor, name);

        // The listener decorator only contains the topic.
        const listenerArg = {
            topic: `${nodeConstructor.name}/${name}/start`,
            isBulk: !!options?.isBulk,
            mutationName: name,
            startsReadOnly: !!arg?.startsReadOnly,
            onError: arg.onError,
            parameters: arg.parameters as { [K: number]: Parameter<typeof Node, AnyValue> } as (Parameter<
                typeof Node,
                AnyValue
            > & { name: string })[],
            return: arg.return as Parameter<typeof Node, AnyValue>,
            queue: arg.queue,
        } as AsyncMutationListenerDecorator;
        listenerFunction(new AsyncMutationListener(listenerArg))(nodeConstructor, name);
    };
}

/** &#064;decorators.asyncMutation(arg) static method decorator */
export function asyncMutation<This extends StaticThis<Node>, K extends keyof This>(
    arg: AsyncMutationDecorator<This, K>,
): StaticMemberDecoratorFunction<typeof Node> {
    return anyAsyncMutation(arg);
}
