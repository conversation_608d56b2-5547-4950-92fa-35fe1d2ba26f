// This module provides the listener decorators.
import {
    AsyncResponse,
    Context,
    Node,
    SqsReceiveQueue,
    staticMemberDecorator,
    StaticMemberDecoratorFunction,
} from '@sage/xtrem-core';
import { MessageListener } from '../listeners';
import { MessageEnvelope } from '../queues';
import { BaseListenerDecorator, ContextAttributes } from './base-listener-decorator';

/** Parameter type for decorators.messageListener */
export interface MessageListenerDecorator<ConstructorType extends typeof Node, PayloadT> extends BaseListenerDecorator {
    format: 'json' | 'text';
    integrationSolution?: string;
    getTenantId(this: ConstructorType, envelope: MessageEnvelope<PayloadT>): AsyncResponse<string>;
    getContextAttributes(
        this: ConstructorType,
        context: Context,
        envelope: MessageEnvelope<PayloadT>,
    ): AsyncResponse<ContextAttributes>;
    getId?(this: ConstructorType, context: Context, envelope: MessageEnvelope<PayloadT>): AsyncResponse<string>;
    queue: () => SqsReceiveQueue;
    onError?(
        this: ConstructorType,
        context: Context,
        envelope: MessageEnvelope<PayloadT>,
        error: Error,
    ): AsyncResponse<void>;
}

/** &#064;decorators.messageListener(arg) static method decorator */
export function messageListener<ConstructorType extends typeof Node, PayloadT>(
    arg: MessageListenerDecorator<ConstructorType, PayloadT>,
): StaticMemberDecoratorFunction<ConstructorType> {
    return staticMemberDecorator<ConstructorType, any>('messageListeners', 'messageListener')(new MessageListener(arg));
}
