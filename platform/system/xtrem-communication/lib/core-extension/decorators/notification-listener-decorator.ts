// This module provides the listener decorators.
import {
    AsyncResponse,
    Context,
    Node,
    NotificationTopic,
    staticMemberDecorator,
    StaticMemberDecoratorFunction,
} from '@sage/xtrem-core';
import { NotificationListener } from '../listeners';
import { NotificationEnvelope } from '../queues';
import { BaseListenerDecorator } from './base-listener-decorator';

/** Parameter type for decorators.notificationListener */
export interface NotificationListenerDecorator extends BaseListenerDecorator {
    topic: string | NotificationTopic;
    onError?(this: typeof Node, context: Context, envelope: NotificationEnvelope, error: Error): AsyncResponse<void>;

    /**
     * The specific name of the queue used for notifications.
     * If not set, the queue of the package will be used.
     */
    queue?: string;
}

/** &#064;decorators.notificationListener(arg) static method decorator */
export function notificationListener<ConstructorType extends typeof Node>(
    arg: NotificationListenerDecorator,
): StaticMemberDecoratorFunction<ConstructorType> {
    return staticMemberDecorator<ConstructorType, any>(
        'notificationListeners',
        'notificationListener',
    )(new NotificationListener(arg));
}
