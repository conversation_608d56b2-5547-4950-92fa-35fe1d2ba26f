import { asyncMutation } from './async-mutation-decorator';
import { bulkMutation } from './bulk-mutation-decorator';
import { messageListener } from './message-listener-decorator';
import { notificationListener } from './notification-listener-decorator';

export * from './async-mutation-decorator';
export * from './base-listener-decorator';
export * from './message-listener-decorator';
export * from './notification-listener-decorator';

// Communication listeners which will be merged into the `decorators` variable.
export const communicationDecorators = {
    notificationListener,
    messageListener,
    asyncMutation,
    bulkMutation,
};

export type CommunicationDecorators = typeof communicationDecorators;
