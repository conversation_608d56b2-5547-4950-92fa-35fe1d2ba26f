import {
    BaseOperationDecorator,
    Context,
    MappedParameters,
    Node,
    StaticMemberDecoratorFunction,
    StaticThis,
    UnPromised,
} from '@sage/xtrem-core';
import { anyAsyncMutation } from './async-mutation-decorator';

export interface BulkMutationDecorator<ConstructorT extends StaticThis<Node>, K extends keyof ConstructorT>
    extends BaseOperationDecorator {
    isPublished?: boolean;
    isSchedulable?: boolean;

    /**
     * startsReadOnly is used to determine whether the static method of a bulk mutation is called with a readonly or writable context.
     * It can be set as a boolean or a callback that returns a boolean.
     */
    startsReadOnly?: boolean | ((this: ConstructorT, context: Context) => boolean);

    onComplete?(
        context: Context,
        results: ConstructorT[K] extends (context: Context, instance: Node, ...args: any[]) => any
            ? UnPromised<ReturnType<ConstructorT[K]>>[]
            : never,
    ): Promise<void>;
    parameters?: ConstructorT[K] extends (context: Context, instance: Node, ...args: any[]) => any
        ? MappedParameters<Parameters<ConstructorT[K]> extends [Context, Node, ...infer R1] ? R1 : never>
        : never;

    /**
     * The specific name of the queue used for notifications.
     * If not set, the queue of the package will be used.
     */
    queue?: string;
}

/** &#064;decorators.bulkMutation(arg) static method decorator */
export function bulkMutation<ConstructorT extends StaticThis<Node>, K extends keyof ConstructorT>(
    arg: BulkMutationDecorator<ConstructorT, K>,
): StaticMemberDecoratorFunction<typeof Node> {
    // We have to use `as any` casts here because mutation decorators use advanced typing to check consistency between
    // the arg.parameters and the types of the method parameters. We cannot handle that generically.
    return anyAsyncMutation(
        {
            ...arg,
            authorizedBy: undefined,
            parameters: [
                {
                    name: 'filter',
                    type: 'string',
                },
                ...((arg.parameters as any) || []),
            ] as any,
            return: 'boolean' as any,
            queue: arg.queue,
        },
        { isBulk: true },
    );
}
