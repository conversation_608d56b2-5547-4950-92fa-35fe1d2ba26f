// This module provides the listener decorators.
import { AsyncResponse, Context, IsolationLevel, Node, UserInfo } from '@sage/xtrem-core';
import { NotificationEnvelope } from '../queues';

export interface BaseListenerDecorator {
    isolationLevel?: IsolationLevel;
    /**
     * startsReadOnly is used to determine whether the static method of a listener is called with a readonly or writable context.
     * It can be set as a boolean or a callback that returns a boolean.
     */
    startsReadOnly?:
        | boolean
        | ((this: typeof Node, context: Context, envelope: NotificationEnvelope) => AsyncResponse<boolean>);
}

export interface ContextAttributes {
    user: UserInfo;
    locale: string;
}
