import { ConfigManager } from '@sage/xtrem-core';
import {
    AwsConfigOverride,
    getSQSMessageWrapper as cirrusGetSQSMessageWrapper,
    MessageWrapper,
} from '@sage/xtrem-messaging-wrapper';

/**
 * Returns a SQS message wrapper that can be used for real queues and SQS docker container
 * @param queueUrl
 * @param awsConfigOverride
 */
export function getSQSMessageWrapper(queueUrl: string, awsConfigOverride: AwsConfigOverride): MessageWrapper {
    const getConfigOverride = (): AwsConfigOverride => {
        if (ConfigManager.current.deploymentMode === 'production') return awsConfigOverride;
        // We are using a local SQS docker container (elasticmq).
        // It needs the endpoint to be set (as of aws-sdk v3)
        // In production mode (so with real AWS queues), we don't need to set
        // this endpoint (checked By Cirrus)
        return {
            ...awsConfigOverride,
            ...{
                endpoint: ConfigManager.current.interop?.devEndpoint ?? 'http://localhost:9324',
            },
        };
    };

    return cirrusGetSQSMessageWrapper(queueUrl, getConfigOverride());
}
