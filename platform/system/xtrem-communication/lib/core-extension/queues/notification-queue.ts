import { Application, asyncArray, Context } from '@sage/xtrem-core';
import { flat, Maybe } from '@sage/xtrem-shared';
import { NotificationListener, ProcessMessageOptions } from '../listeners';
import { logger, sleepMillis } from '../utils';
import { NotificationEnvelope } from './envelopes';
import { exitOnMissingQueue, isNoQueueUrlDefinedError, QueueDefinition } from './queue';
import { ListenerQueue, ListenerQueueOptions } from './queue-manager';
import { SqsQueue, SqsReceiveOptions } from './sqs-queue';

interface InFlightNotification {
    promise: Promise<NotificationEnvelope | undefined>;
    notification?: NotificationEnvelope;
}
/**
 *  A notification carries a topic. It will be distributed to all the handlers that listen to the topic
 */
export class NotificationQueue extends SqsQueue implements ListenerQueue {
    listeners: NotificationListener[] = [];

    // eslint-disable-next-line @typescript-eslint/no-useless-constructor
    constructor(definition: QueueDefinition) {
        super(definition);
    }

    /**
     * Send a notification
     *
     * @param context
     * @param message
     */
    send(context: Context, notification: NotificationEnvelope): Promise<Maybe<string>> {
        if (notification.payload == null) throw new Error('Cannot send null payload');
        const attributes = flat(notification.attributes);

        return this.rawSend(
            context,
            typeof notification.payload === 'string' ? notification.payload : JSON.stringify(notification.payload),
            attributes,
            notification.deduplicationId,
        );
    }

    /** @internal */
    private async receive(options?: SqsReceiveOptions): Promise<NotificationEnvelope[]> {
        const messages = await this.rawReceive(options);
        return messages
            .map(m => {
                try {
                    if (!m.Body) throw new Error('no body');
                    const payload = JSON.parse(m.Body);
                    const attributes = flat(m.Attributes);
                    if (!payload) throw new Error('notification payload missing');
                    if (!attributes.tenantId) throw new Error('notification tenantId missing');
                    if (!attributes.topic) throw new Error('notification topic missing');
                    const receiveHandle = m.ReceiptHandle;
                    const messageId = m.MessageId;
                    const extraAttributes = JSON.parse(attributes.extraAttributes || '{}');
                    return { payload, attributes: { ...attributes, receiveHandle, messageId }, extraAttributes };
                } catch (e) {
                    logger.error(`bad notification envelope for message ${JSON.stringify(m)}\n${e.stack}`);
                    return null as any;
                }
            })
            .filter(m => m != null);
    }

    private async processNotification(
        application: Application,
        notification: NotificationEnvelope,
        options: ProcessMessageOptions,
    ): Promise<NotificationEnvelope> {
        logger.info(`${this.name}: received notification: ${JSON.stringify(notification)}`);
        const listeners = this.listeners.filter(listener => listener.topic === notification.attributes.topic);
        if (listeners.length === 0) {
            // If there are no listeners for the topic then we print a warning and continue gracefully
            logger.warn(`${this.name}: no listeners for topic ${notification.attributes.topic}`);
        } else {
            await asyncArray(listeners).forEach(listener => listener.process(application, notification, options));
        }
        Application.emitter.emit('notificationProcessed', { application, payload: (await notification.payload).value });

        return notification;
    }

    private async listenLoop(application: Application): Promise<void> {
        if (this.listeners.length === 0) {
            throw new Error(`${this.name}: cannot start queue: no listeners`);
        }

        const inFlight: InFlightNotification[] = [];
        // Our lower bound for the messages that are in progress
        const inFlightLow = this.receiveLowerBound;
        // Our upper bound for the messages that are in progress
        const inFlightHigh = this.receiveUpperBound;

        while (this.isListening) {
            //  there should always be a dummy in the inFlight list
            if (!inFlight.find(entry => !entry.notification)) {
                logger.debug(() => `${this.name}: adding dummy to list`);
                inFlight.push({
                    promise: (async () => {
                        await sleepMillis(this.receivePollingSeconds * 1000);
                        return undefined;
                    })(),
                });
            }

            if (inFlight.length > 1) {
                logger.verbose(
                    () =>
                        `${this.name}: ${inFlight.length} messages in flight (including dummy one). [${inFlight.map(entry => entry.notification?.attributes.receiveHandle)}]`,
                );
            }
            // refill the inFlight list if we are below our inFlightLow threshold
            if (inFlight.length < inFlightLow) {
                // We need to receive a max number of messages that will not exceed our inFlightHigh cap
                const maxNumberOfMessages = inFlightHigh - inFlight.length;
                const received = await this.receive({ maxNumberOfMessages });
                if (received.length > 0) {
                    logger.verbose(() => `${this.name}: received ${received.length} messages.`);
                    if (this.isListening) {
                        this.updateInFlight(application, inFlight, received);
                    } else {
                        // received while it was in termination, let's update the visibility window to now
                        // Can happen since the "receive" method waits 10 seconds to get notifications if there is nothing in the queue
                        // in the meantime it's possible that we had the termination signal sent by the infrastructure
                        await this.handleMessageReceivedDuringShutdown(received);
                    }
                }
            }

            // inFlight will always have atleast 1 entry (dummyNotification)
            const processedNotification = await Promise.race(inFlight.map(entry => entry.promise));

            if (processedNotification != null) {
                // processedNotification will be undefined for the dummy message
                logger.verbose(
                    () =>
                        `${this.name}: processing complete receiveHandle=${processedNotification.attributes.receiveHandle} payload=${JSON.stringify(processedNotification?.payload)}`,
                );
            }

            // Remove the processed notification from the inFlight list
            inFlight.splice(
                inFlight.findIndex(entry => entry.notification === processedNotification),
                1,
            );
        }

        logger.info(`${this.name}: is no longer processing messages`);
        this.stop();
    }

    private updateInFlight(
        application: Application,
        inFlight: InFlightNotification[],
        received: NotificationEnvelope[],
    ): void {
        inFlight.push(
            ...received.map(notification => {
                // stop refreshing the message visibility timeout when the promise is settled
                return {
                    promise: this.processWithMonitoring<NotificationEnvelope>(
                        signal => this.processNotification(application, notification, { signal }),
                        notification,
                    ),
                    notification,
                };
            }),
        );
    }

    listen(application: Application, options?: ListenerQueueOptions): void {
        if (this.isListening) {
            return;
        }
        if (!options?.quiet) {
            logger.info(`${this.id}: start listening on notification queue...`);
        }
        this.startListening();
        this.listenLoop(application).catch(error => {
            const missingQueue = isNoQueueUrlDefinedError(error);
            if (!options?.quiet) {
                logger.info(`${this.id}: stop listening..., ${missingQueue} error: ${error.message}`);
            }
            logger.error(`Listening to ${this.id} failed: ${error.message}`);
            exitOnMissingQueue(this.id, error);
            this.stopListening();
            this.stop();
        });
    }
}
