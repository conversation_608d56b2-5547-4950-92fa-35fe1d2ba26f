// This module implements the queue manager which starts listening on the SQS queues

import { Application, ConfigManager, Context, Dict, Package, SystemError } from '@sage/xtrem-core';
import { NotificationQueue, SqsQueue, SqsReceiveQueue } from '.';
import { MessageListener, NotificationListener } from '../listeners';
import { logger } from '../utils';

export interface ListenerQueueOptions {
    quiet?: boolean;
}

export interface ListenerQueue {
    listen(application: Application, options?: ListenerQueueOptions): void;
}

export abstract class QueueManager {
    static readonly messageListeners: MessageListener[] = [];

    static readonly notificationListeners: NotificationListener[] = [];

    private static _isInterruptRequested = false;

    static get isInterruptRequested(): boolean {
        return this._isInterruptRequested;
    }

    static addMessageListener(listener: MessageListener): void {
        if (!this.messageListeners.includes(listener)) this.messageListeners.push(listener);
    }

    static addNotificationListener(listener: NotificationListener): void {
        if (!this.notificationListeners.includes(listener)) this.notificationListeners.push(listener);
    }

    // This method is called when the application is ready to process requests
    static start(application: Application, listenerPackages: Package[]): void {
        // Get the list of queues belonging to packages started in the current container
        const listenerPackageNames = listenerPackages.map(pack => pack.name);
        const listenerMonitoringSeconds = ConfigManager.current.interop?.listenerMonitoringSeconds ?? 1;
        const allQueueNames: string[] = [];

        // If the application was started with a --queues flag, then only the specified queues
        // must be started
        const specificQueuesToStart = application.startOptions.queues;
        if (specificQueuesToStart != null) {
            logger.warn(
                `The application was started with --queues=xx, only the following queues will be started: ${specificQueuesToStart.join(
                    ',',
                )}`,
            );
        }

        const collectQueues = (
            listeners: (MessageListener | NotificationListener)[],
        ): (NotificationQueue | SqsReceiveQueue)[] => {
            // We use a Dict to remove duplicates based on the queue names.
            const declaredQueues = listeners.reduce(
                (total, listener) => {
                    if (!listenerPackageNames.includes(listener.factory.package.name)) return total;
                    const queueName = listener.queue.name;
                    if (total[queueName] != null) return total;
                    total[queueName] = listener.queue;
                    allQueueNames.push(queueName);
                    return total;
                },
                {} as Dict<NotificationQueue | SqsReceiveQueue>,
            );
            if (specificQueuesToStart == null || specificQueuesToStart.length === 0)
                return Object.values(declaredQueues);

            // The application was started with the --queue=a,b,.. flag, so only these queues must be started
            return Object.values(declaredQueues).filter(queue => {
                if (specificQueuesToStart.includes(queue.name)) return true;
                logger.verbose(
                    () =>
                        `Queue ${queue.name} will not be started (not part of the queues provided by the --queues flag)`,
                );
                return false;
            });
        };
        const messageQueues = collectQueues(this.messageListeners);
        // Set the SQS queue name, prefix with app name when needed
        messageQueues.forEach(queue => {
            queue.sqsName = application.sqsQueueName(queue.name);
        });

        const notificationQueues = collectQueues(this.notificationListeners);
        if (specificQueuesToStart != null) {
            const invalidQueueNames = specificQueuesToStart.filter(queueName => !allQueueNames.includes(queueName));
            if (invalidQueueNames.length > 0) {
                throw new SystemError(
                    `The queues '${invalidQueueNames.join(
                        ',',
                    )}' specified in the --queue flag do not match any listener`,
                );
            }
        }

        const startQueues = (
            queues: (NotificationQueue | SqsReceiveQueue)[],
            queueType: string,
            options?: ListenerQueueOptions,
        ): void => {
            const queuesToStart = Object.values(queues).filter(queue => {
                return !queue.isListening;
            });
            if (queuesToStart.length === 0) return;
            const undefinedUrls = queuesToStart.filter(queue => !queue.config?.url);
            if (undefinedUrls.length > 0) {
                logger.error(
                    `The URL of the following queues must be configured: ${undefinedUrls.map(q => q.name).join(',')}`,
                );
                // defer the exit to allow the logger to flush
                setImmediate(() => process.exit(1));
                return;
            }
            logger.verbose(() => {
                // Only log the 10 first queues
                let queueNames = `- ${queuesToStart
                    .slice(0, 10)
                    .map(q => q.name)
                    .join(',')}`;
                if (queuesToStart.length > 10) queueNames = `${queueNames}, ...`;

                return `[QMan] Monitor ${queueType} listeners: ${queuesToStart.length} queues to start ${queueNames}`;
            });
            queuesToStart.forEach(queue => {
                queue.listen(application, options);
            });
        };

        const monitor = (options?: ListenerQueueOptions): void => {
            if (this._isInterruptRequested) {
                return;
            }
            const listeningOptions = { quiet: true, ...options };
            startQueues(messageQueues, 'message', listeningOptions);
            startQueues(notificationQueues, 'notification', listeningOptions);
            setTimeout(monitor, listenerMonitoringSeconds * 1000);
        };

        monitor({ quiet: false });
    }

    static stop(): void {
        this._isInterruptRequested = true;
        const isListening = (listeners: (MessageListener | NotificationListener)[]): boolean => {
            return listeners.map(listener => listener.queue).some(queue => queue.isListening);
        };

        const stopQueues = (listeners: (MessageListener | NotificationListener)[]): void => {
            // We use a Dict to remove duplicates based on the queue names.
            const queues: Dict<NotificationQueue | SqsReceiveQueue> = {};
            listeners
                .map(listener => listener.queue)
                .forEach(queue => {
                    queues[queue.name] ??= queue;
                });
            Object.values(queues).forEach(queue => {
                logger.info(`[QMan] Stop listening to queue ${queue.name}`);
                queue.stopListening();
            });
        };

        if (isListening(this.messageListeners)) {
            logger.info('[QMan] Stopping message listeners');
            stopQueues(this.messageListeners);
        }

        if (isListening(this.notificationListeners)) {
            logger.info('[QMan] Stopping notification listeners');
            stopQueues(this.notificationListeners);
        }
    }

    static stopped(): boolean {
        const isStopped = (listeners: (MessageListener | NotificationListener)[]): boolean => {
            return listeners.map(listener => listener.queue).every(queue => queue.isStopped);
        };

        return isStopped(this.messageListeners) && isStopped(this.notificationListeners);
    }

    static getQueue(context: Context): SqsQueue {
        const queueName = context.getContextValue('queueName');
        if (!queueName) throw new Error('queueName not set in context');
        const packages = context.application.getPackages();
        let queue: SqsQueue | undefined;

        for (let i = 0; i < packages.length; i += 1) {
            const pkg = packages[i];
            if (pkg.api.queues) {
                queue = pkg.api.queues[queueName] as SqsQueue;
                if (queue) break;
            }
        }

        if (!queue) throw new Error(`Queue ${queueName} does not exists`);
        return queue;
    }
}
