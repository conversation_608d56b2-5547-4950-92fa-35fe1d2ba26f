// This module provides the Queue classes
import { asyncArray, ConfigManager, Context, datetime, Dict, retry, SystemError } from '@sage/xtrem-core';
import { MessageAttribute, ReceivedMessage } from '@sage/xtrem-messaging-wrapper';
import { Maybe } from '@sage/xtrem-shared';
import * as crypto from 'crypto';
import * as _ from 'lodash';
import { nanoid } from 'nanoid';
import { logger } from '../utils';
import { Envelope, MessageEnvelope, NotificationEnvelope, TechnicalAttributes } from './envelopes';
import { exitOnMissingQueue, getSanitizedSentMessage, Queue, QueueDefinition } from './queue';

export interface SqsQueueDefinition extends QueueDefinition {}

export interface SqsReceiveOptions {
    waitTimeSeconds?: number;
    maxNumberOfMessages?: number;
}

/**
 * Object to handle the visibility state of monitored SQS messages.
 * We need to maintain:
 * - the timer to be able to clear the interval once we are done with the message
 * - the promise of the in-progress changeMessageVisibilityTimeout to be able to wait on it outside of the timer scope
 *   and manage a potential race condition.
 *   There is no guarantee that this promise wiil be resolved before the delete message.
 */
interface VisibilityState {
    receiveHandle: string;
    timer?: NodeJS.Timeout;
    promise?: Promise<void>;
}

export function normalizeAttributes(attributes: Dict<string> | undefined): Dict<string> | undefined {
    if (attributes?._technical) {
        _.merge(attributes, JSON.parse(attributes._technical));
        delete attributes._technical;
    }
    return attributes;
}

export function shrinkAttributes(attributes: Dict<string>, technical: TechnicalAttributes): Dict<string> {
    const { tenantId, userEmail, login, locale, customerRequestId } = attributes;

    // Single technical attribute to not exceed the 10 attributes limit
    attributes._technical = JSON.stringify({
        ...technical,
        tenantId,
        userEmail,
        login,
        locale,
        customerRequestId,
    });
    delete attributes.tenantId;
    delete attributes.customerRequestId;
    delete attributes.locale;
    delete attributes.login;
    delete attributes.userEmail;

    return attributes;
}

function makeMessageAttributes(attributes: Dict<string>): MessageAttribute[] {
    return Object.entries(attributes)
        .filter(([key, value]) => key !== 'tenantId' && value)
        .map(([key, value]) => ({
            attributeName: key,
            attributeStringValue: value,
        }));
}

export abstract class SqsQueue extends Queue {
    #visibilityStates = {} as Dict<VisibilityState>;

    private getVisibilityState(receiveHandle: string): VisibilityState {
        return this.#visibilityStates[receiveHandle];
    }

    private createVisibilityState(visibilityStateData: Omit<VisibilityState, 'promise'>): void {
        this.#visibilityStates[visibilityStateData.receiveHandle] = visibilityStateData;
    }

    /**
     * Send a message to the SQS queue using the notification wrapper
     *
     * @see https://confluence.sage.com/display/XTREEM/Queue+messaging+library
     * @see https://docs.aws.amazon.com/AWSSimpleQueueService/latest/SQSDeveloperGuide/sqs-basic-architecture.html
     * @see https://docs.aws.amazon.com/AWSSimpleQueueService/latest/SQSDeveloperGuide/quotas-messages.html
     *
     * @param queue
     * @param context
     * @param message
     */
    protected async rawSend(
        context: Context,
        message: string,
        attributes: Dict<string> = {},
        deduplicationId: string = nanoid(),
    ): Promise<Maybe<string>> {
        const wrapper = this.wrapper;
        const config = ConfigManager.current;

        const sendRetryCount = config.interop?.sendRetryCount || 3;
        const delayBeforeRetry = (config.interop?.sendRetrySeconds || 90) * 1000;
        const sqsGroupsPerTenantUser = config.interop?.sqsGroupsPerTenantUser || 5;

        if (message == null) throw new Error('Cannot send null payload');

        // Single technical attribute to not exceed the 10 attributes limit
        const technical = {
            /** queue URL to identify the original queue in the single dead letter queue */
            queueUrl: this.config?.url,
            tenantId: context.tenantId,
            customerRequestId: context.customerRequestId,
            locale: attributes.locale || context.currentLocale,
            login: attributes.login,
            userEmail: attributes.userEmail,
        } as TechnicalAttributes;

        const sqsGroupNumber = crypto.randomInt(1, sqsGroupsPerTenantUser);
        // Restriction on MessageBody:
        //   A message can include only XML, JSON, and unformatted text.
        //   The following Unicode characters are allowed: #x9 | #xA | #xD | #x20 to #xD7FF | #xE000 to #xFFFD | #x10000 to #x10FFFF
        //   Any characters not included in this list are rejected.
        //   For more information, see the W3C specification for characters at https://www.w3.org/TR/REC-xml/#charsets
        const sqsMessage = {
            MessageBody: message,
            MessageGroupId: `${context.tenantId}-${context.userId}-${sqsGroupNumber}`,
            MessageDeduplicationId: deduplicationId,
            MessageAttributes: makeMessageAttributes(shrinkAttributes(attributes, technical)),
        };

        if (logger.isActive('verbose')) {
            logger.verbose(() => `${this.id}: sending message ${JSON.stringify(sqsMessage)}`);
        } else {
            logger.info(`${this.id}: sending message ${JSON.stringify(getSanitizedSentMessage(sqsMessage))}`);
        }
        const messageSendId = await retry(() => wrapper.sendMessage(sqsMessage), {
            maxTries: sendRetryCount,
            delayBeforeRetry,
            message: `sending message to ${this.name}`,
            onError: (err: Error) => exitOnMissingQueue(this.id, err),
        });
        logger.info(`${this.id}: message sent: id=${messageSendId}, dedupId=${sqsMessage.MessageDeduplicationId}`);
        return messageSendId;
    }

    /** @internal */
    protected async rawReceive(options?: SqsReceiveOptions): Promise<ReceivedMessage[]> {
        const wrapper = this.wrapper;
        const config = ConfigManager.current;
        const receiveRetryCount = config.interop?.receiveRetryCount || 3;
        const delayBeforeRetry = (config.interop?.receiveRetrySeconds || 1) * 1000;

        const nowValue = datetime.now().value;

        const messages = await retry(
            () => {
                // must be < 10 if you pick a lot of message make sure you have enough time to process them during visibility window
                return wrapper.receiveMessage({
                    MaxNumberOfMessages: options?.maxNumberOfMessages || 5,
                    // you have 30 seconds to process the message otherwise it will become visible again
                    VisibilityTimeout: this.messageVisibilitySeconds,
                    ReceiveRequestAttemptId: nanoid(),
                    WaitTimeSeconds: options?.waitTimeSeconds,
                });
            },
            {
                maxTries: receiveRetryCount,
                delayBeforeRetry,
                message: `receive message from ${this.id}`,
                onError: (err: Error) => exitOnMissingQueue(this.id, err),
            },
        );
        return messages.map(message => ({
            ...message,
            Attributes: {
                ...normalizeAttributes(message.Attributes),
                visibilityStartMillis: String(nowValue),
                queueName: this.name,
            },
        }));
    }

    /** @internal */
    protected async delete(receiveHandle: string, messageId?: string): Promise<void> {
        const visibilityState = this.getVisibilityState(receiveHandle);
        this.unrefVisibleState(visibilityState);

        // visibility change is done in a timer, we need to await in progress one to prevent from a race condition errors
        if (visibilityState?.promise) {
            await SqsQueue.waitVisibilityCompletion(visibilityState);
        }
        logger.verbose(() => `${this.name}: deleting message=${messageId}, receiveHandle=${receiveHandle}`);
        const wrapper = this.wrapper;
        await wrapper
            .deleteMessage({ ReceiptHandle: receiveHandle, MessageId: messageId })
            .catch(err => logger.error(`Failed to delete receiveHandle ${receiveHandle}: ${err.stack}`));
    }

    /**
     * @internal
     * reset the messageVisibilityTimeout. Refer to: https://docs.aws.amazon.com/AWSSimpleQueueService/latest/SQSDeveloperGuide/sqs-visibility-timeout.html
     * @param receiveHandle
     * @param messageVisibilitySeconds
     */
    protected async changeMessageVisibilityTimeout(
        receiveHandle: string,
        messageVisibilitySeconds: number,
    ): Promise<void> {
        const visibilityState = this.getVisibilityState(receiveHandle) || {};

        if (!visibilityState.timer && messageVisibilitySeconds > 0) {
            logger.verbose(
                () =>
                    `${this.name}: skip updating message visibility receiveHandle=${receiveHandle}: monitoring is over`,
            );
            return;
        }
        logger.verbose(
            () =>
                `${
                    this.name
                }: updating message visibility receiveHandle=${receiveHandle}, promise=${!!visibilityState.promise}, expires in ${messageVisibilitySeconds} seconds`,
        );
        // In case another one is still in progress (should normally not happen)
        if (visibilityState?.promise) {
            await SqsQueue.waitVisibilityCompletion(visibilityState);
        }

        const wrapper = this.wrapper;
        visibilityState.promise = wrapper.changeMessageVisibilityTimeout({
            ReceiptHandle: receiveHandle,
            VisibilityTimeoutInSeconds: messageVisibilitySeconds,
        });
        await SqsQueue.waitVisibilityCompletion(visibilityState);
    }

    private static async waitVisibilityCompletion(visibilityState: VisibilityState): Promise<void> {
        const visibilityPromise = visibilityState?.promise;
        if (visibilityPromise) {
            await visibilityPromise.catch(err => {
                const message = `Failed to change visibility timeout of receiveHandle ${visibilityState.receiveHandle}`;
                logger.error(`${message}: ${err.stack}`);
                throw new SystemError(message, err);
            });
            delete visibilityState.promise;
        }
    }

    /**
     * @internal
     * make the visibility window to expire now so that the message comes back to the queue and can be picked again
     * @param receiveHandle
     */
    protected putBackMessageInQueue(receiveHandle: string): Promise<void> {
        return this.changeMessageVisibilityTimeout(receiveHandle, 0);
    }

    async clearQueue(): Promise<void> {
        if (ConfigManager.current.deploymentMode !== 'development') {
            logger.warn('Queue can only be cleared in developement mode.');
        }
        let messages = await this.rawReceive({ waitTimeSeconds: 1 });
        while (messages.length) {
            await asyncArray(messages).forEach(async message => {
                logger.info(`${this.name}: removing message ${JSON.stringify(message)}`);
                await this.delete(message.ReceiptHandle!, message.MessageId!);
            });

            messages = await this.rawReceive({ waitTimeSeconds: 1 });
        }
    }

    protected processWithMonitoring<T>(
        body: (signal: AbortSignal) => Promise<T>,
        message: Envelope,
    ): Promise<T | undefined> {
        const abortController = new AbortController();
        const { signal } = abortController;

        this.keepMessageInvisible(message, abortController);
        return body(signal).finally(async () => {
            // message has been processed, we can delete it
            await this.delete(message.attributes.receiveHandle || '', message.attributes.messageId);
            this.unrefMessage(message);
        });
    }

    private keepMessageInvisible(message: Envelope, abortController: AbortController): void {
        const { messageId, receiveHandle } = message.attributes;
        if (messageId == null) {
            throw new Error('Invalid message: no messageId');
        }
        logger.verbose(
            () =>
                `Set visibility timer of receiveHandle:${receiveHandle} messageId:${messageId} [${Object.keys(
                    this.#visibilityStates,
                )}]`,
        );
        const visibilityState = this.getVisibilityState(receiveHandle);
        if (visibilityState?.timer) {
            logger.verbose(
                () =>
                    `visibility timer already set for receiveHandle:${receiveHandle} messageId:${messageId} [${Object.keys(
                        this.#visibilityStates,
                    )}]`,
            );
            return;
        }
        const timer = global.setInterval(
            () => {
                const state = this.getVisibilityState(receiveHandle);
                if (state && !state.timer) {
                    logger.verbose(
                        () =>
                            `visibility timer has been cleared for receiveHandle:${receiveHandle} messageId:${messageId} [${Object.keys(
                                this.#visibilityStates,
                            )}]`,
                    );
                    return;
                }
                logger.verbose(
                    () =>
                        `Change visibility timeout of receiveHandle ${receiveHandle} [${Object.keys(
                            this.#visibilityStates,
                        )}]`,
                );

                this.changeMessageVisibilityTimeout(receiveHandle, this.messageVisibilitySeconds).catch(err => {
                    const errorMessage = `Failed to change visibility timeout of receiveHandle ${receiveHandle}`;
                    logger.error(`${errorMessage}: ${err.stack}`);
                    abortController.abort(new SystemError(errorMessage, err));
                });
            },
            // we should increase the message visibility once we reach the half life of the message, if this is too soon we can adjust later.
            (this.messageVisibilitySeconds / 2) * 1000,
        );
        this.createVisibilityState({ receiveHandle, timer });
    }

    protected unrefMessage(message: Envelope): void {
        const { receiveHandle } = message.attributes;
        const visibilityState = this.getVisibilityState(receiveHandle);
        this.unrefVisibleState(visibilityState);
    }

    private unrefVisibleState(visibilityState: Maybe<VisibilityState>): void {
        if (!visibilityState) {
            return;
        }
        const { receiveHandle, timer } = visibilityState;
        if (receiveHandle) {
            delete this.#visibilityStates[receiveHandle];
        }
        if (timer) {
            delete visibilityState.timer;
            global.clearInterval(timer);
        }
    }

    /**
     * @internal
     * Send back all messages to the queue, so it can be picked by another process or when listening again to the queue
     * @param messages The messages or notifications to put back in the queue
     */
    protected async handleMessageReceivedDuringShutdown(
        messages: MessageEnvelope[] | NotificationEnvelope[],
    ): Promise<void> {
        logger.verbose(
            () =>
                `${this.name}: Sending back ${messages.length} message(s) red during termination process to the queue`,
        );
        await Promise.all(
            messages.map(message => {
                return this.putBackMessageInQueue(message.attributes.receiveHandle || '');
            }),
        );
    }
}
