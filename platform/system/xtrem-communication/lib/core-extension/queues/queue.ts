// This module provides the Queue classes
import { ConfigManager } from '@sage/xtrem-core';
import { AwsConfigOverride, MessageWrapper, SendMessageParams } from '@sage/xtrem-messaging-wrapper';
import { Dict, Maybe, QueueConfig } from '@sage/xtrem-shared';
import * as handlebars from 'handlebars';
import { fromPairs, pick } from 'lodash';
import { logger } from '../utils';
import { MessageEnvelope } from './envelopes';
import { getSQSMessageWrapper } from './sqs-message-wrapper';

export interface QueueDefinition {
    name: string;
    sqsName?: string;
    description: string;
}

const noQueueUrlDefinedMessagePrefix = 'No Url defined for queue ';

export function isNoQueueUrlDefinedError(error: Error): boolean {
    return (
        error.message?.startsWith(noQueueUrlDefinedMessagePrefix) ||
        /The specified queue does not exist./.test(error.message)
    );
}

export function exitOnMissingQueue(id: string, error: Error): void {
    if (isNoQueueUrlDefinedError(error)) {
        logger.error(`Exiting process because ${id} queue is not defined: ${error.message}`);
        // defer the exit to allow the logger to flush
        setTimeout(() => process.exit(1), 100);
    }
}

export function getSanitizedMessageEnvelopeInfo(message: MessageEnvelope<any>): Partial<MessageEnvelope<any>> {
    const payload = typeof message.payload === 'string' ? message.payload : JSON.stringify(message.payload);
    return {
        payload: `<masked ${payload.length} bytes>`,
        attributes: message.attributes,
    };
}

export type SanitizedSendMessageParams = Partial<SendMessageParams & { MessageAttributesAsDict: Dict<string> }>;

export function getSanitizedSentMessage(message: SendMessageParams): SanitizedSendMessageParams {
    const sanitized: SanitizedSendMessageParams = pick(
        message,
        'MessageId',
        'MessageDeduplicationId',
        'MessageGroupId',
    );
    sanitized.MessageAttributesAsDict = fromPairs(
        message.MessageAttributes?.map(attr => [attr.attributeName, attr.attributeStringValue]),
    );
    if (ConfigManager.current.deploymentMode === 'production') {
        const body =
            typeof message.MessageBody === 'string' ? message.MessageBody : JSON.stringify(message.MessageBody);
        sanitized.MessageBody = `<masked ${body.length} bytes>`;
    } else {
        sanitized.MessageBody = message.MessageBody;
    }
    return sanitized;
}

export abstract class Queue {
    #messageVisibilitySeconds: number;

    #receivePollingSeconds: number;

    #receiveLowerBound: number;

    #receiveUpperBound: number;

    #isListening = false;

    #isStopped = true;

    constructor(readonly definition: QueueDefinition) {
        const interop = {
            ...{
                routingPollingSeconds: 1,
                routingReadCount: 3,
                messageVisibilitySeconds: 30,
                receivePollingSeconds: 1,
                receiveLowerBound: 6,
                receiveUpperBound: 11,
            },
            ...ConfigManager.current.interop,
        };
        this.#messageVisibilitySeconds = this.config?.messageVisibilitySeconds || interop.messageVisibilitySeconds;
        this.#receivePollingSeconds = this.config?.receivePollingSeconds || interop.receivePollingSeconds;
        this.#receiveLowerBound = this.config?.receiveLowerBound || interop.receiveLowerBound;
        this.#receiveUpperBound = this.config?.receiveUpperBound || interop.receiveUpperBound;
    }

    get id(): string {
        return `${this.name}@${this.config?.url.split('/').pop()}`;
    }

    get name(): string {
        return this.definition.name;
    }

    /**
     * The SQS name is the name of the queue in the SQS service.
     * It can be different from the queue name if the queue is prefixed with the application name.
     * This is useful to avoid conflicts between queues of different applications.
     *
     * @internal
     */
    get sqsName(): string {
        return this.definition.sqsName || this.name;
    }

    /**
     * @internal
     */
    set sqsName(newSqsName: string) {
        this.definition.sqsName = newSqsName;
    }

    get description(): string {
        return this.definition.description;
    }

    /** @internal */
    get config(): Maybe<QueueConfig> {
        // URL template is used in production cluster V2
        const template = ConfigManager.current.interop?.queueUrlTemplate;
        if (template) {
            const url = handlebars.compile(template)({ queueNameWithApp: this.sqsName });
            return { url };
        }

        let config = ConfigManager.current.interop?.queues?.[this.sqsName];
        // default rule for urls of notification queues
        if (!config && ConfigManager.current.deploymentMode === 'development') {
            const { queueUrlTemplate, devEndpoint } = ConfigManager.current.interop ?? {};
            // queueUrlTemplate is like: http://my-host:9324/queue/{{queueNameWithApp}}.fifo
            //   where {{queueNameWithApp}} is dynamically replaced by the queue name with the app name
            // devEndpoint is like: http://my-host:9324
            // queueUrlTemplateHost have precedence over devEndpoint which is kind of obsolete
            //   but kept for backward compatibility
            const queueUrlTemplateHost = queueUrlTemplate ? new URL(queueUrlTemplate).origin : undefined;
            const endpoint = queueUrlTemplateHost ?? devEndpoint ?? 'http://localhost:9324';
            config = {
                url: `${endpoint}/queue/${this.sqsName}.fifo`,
            };
        }
        return config;
    }

    #wrapper: MessageWrapper;

    /** @internal */
    get wrapper(): MessageWrapper {
        // Caching the wrapper avoids race conditions in the tests
        // TODO: test caching in prod and remove the deploymentMode check if it works
        if (!this.#wrapper || ConfigManager.current.deploymentMode === 'production')
            this.#wrapper = this.createWrapper();
        return this.#wrapper;
    }

    private createWrapper(): MessageWrapper {
        const queueConfig = this.config;

        if (!queueConfig?.url) {
            throw new Error(`${noQueueUrlDefinedMessagePrefix}${this.name}`);
        }

        // On cloud env, the aws config is injected by k8, so do not throw an error even if it seems incomplete at this stage

        const awsConfigOverride: AwsConfigOverride = {
            region: this.config?.region ?? process.env.AWS_REGION ?? 'eu-west-1',
        };

        return getSQSMessageWrapper(queueConfig.url, awsConfigOverride);
    }

    get messageVisibilitySeconds(): number {
        return this.#messageVisibilitySeconds;
    }

    get receivePollingSeconds(): number {
        return this.#receivePollingSeconds;
    }

    get receiveLowerBound(): number {
        return this.#receiveLowerBound;
    }

    get receiveUpperBound(): number {
        return this.#receiveUpperBound;
    }

    get isListening(): boolean {
        return this.#isListening;
    }

    startListening(): void {
        this.#isListening = true;
        this.#isStopped = false;
    }

    stopListening(): void {
        this.#isListening = false;
    }

    get isStopped(): boolean {
        return this.#isStopped;
    }

    stop(): void {
        this.#isStopped = true;
    }
}
