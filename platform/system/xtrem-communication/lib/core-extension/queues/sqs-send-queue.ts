import { Context } from '@sage/xtrem-core';
import { flat, Maybe } from '@sage/xtrem-shared';
import { MessageEnvelope } from './envelopes';
import { QueueDefinition } from './queue';
import { SqsQueue } from './sqs-queue';

export class SqsSendQueue extends SqsQueue {
    // eslint-disable-next-line @typescript-eslint/no-useless-constructor
    constructor(definition: QueueDefinition) {
        super(definition);
    }

    /**
     * Send a message to the SQS queue
     */
    send<PayloadT>(context: Context, message: MessageEnvelope<PayloadT>): Promise<Maybe<string>> {
        if (message.payload == null) throw new Error('Cannot send null payload');

        return this.rawSend(
            context,
            typeof message.payload === 'string' ? message.payload : JSON.stringify(message.payload),
            flat(message.attributes),
            message.deduplicationId,
        );
    }
}
