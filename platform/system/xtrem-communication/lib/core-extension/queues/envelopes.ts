// This module defines the envelopes for messages and notifications

import { Dict } from '@sage/xtrem-core';

export interface TechnicalAttributes {
    queueUrl?: string;
    tenantId: string;
    userEmail: string;
    login: string;
    locale: string;
    customerRequestId?: string;
}

// Attributes of a notification, we are limited to 10 attributes in SQS
export interface NotificationAttributes extends TechnicalAttributes {
    topic: string;
    notificationId: string;
    originId: string;
    resumeToken: string;
    replyId?: string;
    replyTopic?: string;
    receiveHandle?: string;
    messageId?: string;
    extraAttributes?: string;
}

export interface Envelope<PayloadT = any, T extends object = any> {
    readonly payload: PayloadT;
    readonly attributes: T;
    readonly deduplicationId?: string;
}

export interface NotificationEnvelope<PayloadT = any> extends Envelope<PayloadT, NotificationAttributes> {}

export interface MessageEnvelope<PayloadT = any> extends Envelope<PayloadT, Dict<string>> {}
