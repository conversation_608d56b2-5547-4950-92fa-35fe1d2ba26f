import { Application, asyncArray, InterruptException } from '@sage/xtrem-core';
import { flat } from '@sage/xtrem-shared';
import { BaseListener, ProcessMessageOptions } from '../listeners';
import { logger } from '../utils';
import { MessageEnvelope } from './envelopes';
import { exitOnMissingQueue, getSanitizedMessageEnvelopeInfo, QueueDefinition } from './queue';
import { ListenerQueue, ListenerQueueOptions } from './queue-manager';
import { SqsQueue } from './sqs-queue';

/**
 * A message will be sent to a specific queue (queuing).
 * It does not carry a topic (unless the topic is embedded in the message payload and used by a third-party service)
 */
export class SqsReceiveQueue extends SqsQueue implements ListenerQueue {
    /** @internal */
    listeners: BaseListener[] = [];

    // eslint-disable-next-line @typescript-eslint/no-useless-constructor
    constructor(definition: QueueDefinition) {
        super(definition);
    }

    private async receive(): Promise<MessageEnvelope<string>[]> {
        const rawMessages = await this.rawReceive();
        return rawMessages.map(m => {
            const payload = m.Body;
            if (!payload) throw new Error('message received without payload');
            return {
                payload,
                attributes: {
                    ...flat(m.Attributes),
                    receiveHandle: m.ReceiptHandle || '',
                    messageId: m.MessageId || '',
                },
            };
        });
    }

    private async processMessage(
        application: Application,
        message: MessageEnvelope,
        options: ProcessMessageOptions,
    ): Promise<void> {
        if (logger.isActive('verbose')) {
            logger.verbose(() => `${this.name}: received message: ${JSON.stringify(message)}`);
        } else {
            logger.info(`${this.name}: received message: ${JSON.stringify(getSanitizedMessageEnvelopeInfo(message))}`);
        }

        await asyncArray(this.listeners).forEach(listener => listener.process(application, message, options));
    }

    private async listenLoop(application: Application): Promise<void> {
        while (this.isListening) {
            const messages = await this.receive();
            if (this.isListening) {
                // Process each message received
                await asyncArray(messages).forEach(message =>
                    this.processWithMonitoring(
                        signal =>
                            this.processMessage(application, message, { signal }).catch(async error => {
                                if (error instanceof InterruptException) {
                                    logger.error(`InterruptException : ${error.message}`);
                                    await this.handleMessageReceivedDuringShutdown([message]);
                                    return;
                                }
                                throw error;
                            }),
                        message,
                    ),
                );
            } else {
                // received while it was in termination, let's update the visibility window to now
                // Can happen since the "receive" method waits 10 seconds to get messages if there is nothing in the queue
                // in the meantime it's possible that we had the termination signal sent by the infrastructure
                await this.handleMessageReceivedDuringShutdown(messages);
            }
        }

        logger.info(`${this.id}: is no longer processing messages`);
        this.stop();
    }

    listen(application: Application, options?: ListenerQueueOptions): void {
        if (this.isListening) {
            return;
        }
        if (this.listeners.length === 0) {
            throw new Error(`${this.id}: cannot start queue: no listeners`);
        }

        if (!options?.quiet) {
            logger.info(`${this.id}: start listening on message queue...`);
        }

        this.startListening();
        this.listenLoop(application).catch(err => {
            logger.error(`Listening to ${this.id} failed: ${err.message}`);
            exitOnMissingQueue(this.id, err);
            this.stopListening();
            this.stop();
        });
    }
}
