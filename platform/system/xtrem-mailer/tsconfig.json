{"extends": "../../tsconfig-base.json", "compilerOptions": {"outDir": "build", "rootDir": ".", "baseUrl": "."}, "include": ["index.ts", "application.ts", "lib/**/*.ts", "api/api.d.ts", "test/**/*.ts"], "exclude": ["lib/pages/**/*", "lib/widgets/**/*", "lib/page-extensions/**/*", "lib/page-fragments/**/*", "lib/stickers/**/*", "lib/i18n/**/*", "**/*.feature", "**/*.png", "lib/client-functions/**/*"], "references": [{"path": "../../shared/xtrem-async-helper"}, {"path": "../../front-end/xtrem-client"}, {"path": "../../back-end/xtrem-config"}, {"path": "../../back-end/xtrem-core"}, {"path": "../../shared/xtrem-date-time"}, {"path": "../../shared/xtrem-decimal"}, {"path": "../xtrem-infrastructure-adapter"}, {"path": "../xtrem-metadata"}, {"path": "../xtrem-reporting"}, {"path": "../../shared/xtrem-shared"}, {"path": "../xtrem-system"}, {"path": "../../front-end/xtrem-ui"}, {"path": "../../front-end/xtrem-ui-plugin-monaco"}, {"path": "../xtrem-upload"}, {"path": "../xtrem-workflow"}, {"path": "../../back-end/eslint-plugin-xtrem"}, {"path": "../../cli/xtrem-cli"}, {"path": "api"}, {"path": "../xtrem-reporting/api"}, {"path": "../xtrem-system/api"}]}