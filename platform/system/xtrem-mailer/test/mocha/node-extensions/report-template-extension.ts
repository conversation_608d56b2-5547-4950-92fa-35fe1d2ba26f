import { ConfigManager } from '@sage/xtrem-config';
import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremMailer from '../../../lib';
import * as fixtures from '../../fixtures';

const mailerConfig = ConfigManager?.current?.packages?.['@sage/xtrem-mailer'] as xtremMailer.interfaces.MailerConfig;
const reportTemplateName = '';
const subject = '';
const emailAddress = '';
const testData = '';

describe('SendTestEmail mutation', () => {
    it('Mailer config is missing ', async () => {
        Test.patchConfig({
            packages: {
                '@sage/xtrem-mailer': undefined,
            },
        });
        await Test.withContext(async context => {
            let isCatch = false;

            await xtremMailer.nodeExtensions.ReportTemplateExtension.sendTestEmail(
                context,
                reportTemplateName,
                subject,
                emailAddress,
                testData,
            ).catch(error => {
                isCatch = true;
                assert.equal(error, 'Error: The mailer configuration is missing.');
            });
            assert.isTrue(isCatch, 'No catch on sendTestEmail ');
        });
    });
    it("Template '' don't exist ", async () => {
        Test.patchConfig({
            packages: {
                '@sage/xtrem-mailer': {
                    service: 'SendGrid',
                    auth: { apiKey: mailerConfig.auth.apiKey },
                    from: fixtures.dummySender,
                    replyTo: mailerConfig.replyTo,
                    redirectUrl: fixtures.redirectUrl,
                    mailSetting: {
                        sandBoxMode: fixtures.isSandBoxMode,
                        footer: fixtures.dummyFooter,
                        checkSpam: {
                            enable: true,
                            threshold: 10,
                        },
                    },
                } as xtremMailer.interfaces.MailerConfig,
            },
        });
        await Test.withContext(async context => {
            let isCatch = false;
            await xtremMailer.nodeExtensions.ReportTemplateExtension.sendTestEmail(
                context,
                reportTemplateName,
                subject,
                emailAddress,
                testData,
            ).catch(error => {
                isCatch = true;
                assert.equal(error, 'Error: The  template could not be found.');
            });
            assert.isTrue(isCatch, 'No catch on sendTestEmail ');
        });
    });
});
