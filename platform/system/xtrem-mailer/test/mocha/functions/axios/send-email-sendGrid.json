[{"request": {"method": "POST", "url": "https://api.sendgrid.com/v3/mail/send", "headers": {"Content-Type": "application/json"}, "data": {"personalizations": [{"to": [{"email": "<EMAIL>", "name": "Test Receiver"}], "subject": "Test from SendGrid"}], "from": {"name": "Test Name", "email": "<EMAIL>"}, "content": [{"type": "text/plain", "value": "\n    \n    \n        \n            :root {\n                --background: #FFFFFF;\n                --black55: rgba(0, 0, 0, 0.55);\n                --black65: rgba(0, 0, 0, 0.65);\n                --black90: rgba(0, 0, 0, 0.90);\n                --error: #C7384F;\n                --gold: #FFB500;\n                --info: #0077C8;\n                --logo: #00DC00;\n                --slate: #003349;\n                --slate20: #335C6D;\n                --slate40: #668592;\n                --slate60: #99ADB6;\n                --slate80: #CCD6DB;\n                --slate90: #E5EAEC;\n                --slate95: #F2F5F6;\n                --success: #00B000;\n                --tableSeparator: #D9E0E4;\n                --textAndLabels: rgba(0, 0, 0, 0.85);\n                --themePrimary: #0073C2;\n                --themePrimaryHover: #005C9A;\n                --warning: #E96400;\n            }\n            \nbody {\n    font-size: 12pt;\n}\n\nh1,h2,h3,h4,th{\n    color: var(--themePrimary);\n}\n\n@page {\n    size: auto;\n    margin: 15mm 15mm 15mm 15mm;\n}\n\n#footer-header {\n    margin: 15mm 15mm 15mm 15mm;\n    font-size: 8pt;\n}\n\n.xtrem-page-break {\n    page-break-after: always;\n}\n\n            .header {color: #0073C2;} .detail-row {color: #E96400;}\n        \n    \n    Company: Site ID\n"}, {"type": "text/html", "value": "<!DOCTYPE html>\n    <html>\n    <head><meta charset=\"utf-8\">\n        <style>\n            :root {\n                --background: #FFFFFF;\n                --black55: rgba(0, 0, 0, 0.55);\n                --black65: rgba(0, 0, 0, 0.65);\n                --black90: rgba(0, 0, 0, 0.90);\n                --error: #C7384F;\n                --gold: #FFB500;\n                --info: #0077C8;\n                --logo: #00DC00;\n                --slate: #003349;\n                --slate20: #335C6D;\n                --slate40: #668592;\n                --slate60: #99ADB6;\n                --slate80: #CCD6DB;\n                --slate90: #E5EAEC;\n                --slate95: #F2F5F6;\n                --success: #00B000;\n                --tableSeparator: #D9E0E4;\n                --textAndLabels: rgba(0, 0, 0, 0.85);\n                --themePrimary: #0073C2;\n                --themePrimaryHover: #005C9A;\n                --warning: #E96400;\n            }\n            \nbody {\n    font-size: 12pt;\n}\n\nh1,h2,h3,h4,th{\n    color: var(--themePrimary);\n}\n\n@page {\n    size: auto;\n    margin: 15mm 15mm 15mm 15mm;\n}\n\n#footer-header {\n    margin: 15mm 15mm 15mm 15mm;\n    font-size: 8pt;\n}\n\n.xtrem-page-break {\n    page-break-after: always;\n}\n\n            .header {color: #0073C2;} .detail-row {color: #E96400;}\n        </style>\n    </head>\n    <body><div><h1 class=\"header\">Company: </h1><table><tr><th class=\"detail-row\">Site ID</th></tr></table></div></body>\n</html>"}], "mail_settings": {"bcc": {"enable": false}, "footer": {"enable": false}, "sandbox_mode": {"enable": false}, "check_spam": {"enable": false}}}}, "response": {"headers": {"isMock": true}, "data": ""}}]