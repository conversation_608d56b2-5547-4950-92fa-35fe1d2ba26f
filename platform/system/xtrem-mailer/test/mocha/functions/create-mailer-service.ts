import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremMailer from '../../../lib';

describe('create mailer service ', () => {
    it('Service sendGrid', async () => {
        Test.patchConfig({
            packages: {
                '@sage/xtrem-mailer': {
                    service: 'SendGrid',
                    auth: { apiKey: 'dummyApiKey' },
                    from: {
                        address: '<EMAIL>',
                        name: 'Test Name',
                    },
                },
            },
        });
        await Test.withContext(async context => {
            const sendGridConf = await xtremMailer.functions.createMailerService(context);
            assert.isTrue(sendGridConf instanceof xtremMailer.classes.SendGridMailService);
        });
    });
    it('Service NodeMailer', async () => {
        Test.patchConfig({
            packages: {
                '@sage/xtrem-mailer': {
                    service: 'NodeMailer',
                    host: 'smtp.ethereal.email',
                    from: {
                        address: '<EMAIL>',
                        name: 'Test Name',
                    },
                    auth: {
                        apiKey: 'dummy',
                    },
                },
            },
        });
        await Test.withContext(async context => {
            const sendGridConf = await xtremMailer.functions.createMailerService(context);
            assert.isTrue(sendGridConf instanceof xtremMailer.classes.NodeMailerService);
        });
    });
    it('Service throw - empty ', async () => {
        Test.patchConfig({
            packages: {
                '@sage/xtrem-mailer': {
                    service: '',
                },
            },
        });
        await Test.withContext(context => {
            assert.throw(() => xtremMailer.functions.createMailerService(context));
        });
    });
    it('Service throw - unknow ', async () => {
        Test.patchConfig({
            packages: {
                '@sage/xtrem-mailer': {
                    service: 'unknow',
                },
            },
        });
        await Test.withContext(context => {
            assert.throw(() => xtremMailer.functions.createMailerService(context));
        });
    });
});
