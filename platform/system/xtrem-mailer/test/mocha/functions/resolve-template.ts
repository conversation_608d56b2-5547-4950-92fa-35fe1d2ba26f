import { Test, htmlBody } from '@sage/xtrem-core';
import { expect } from 'chai';
import * as xtremMailer from '../../../lib/index';

describe('Templates', () => {
    it('should generate a report template', () =>
        Test.withContext(async context => {
            const populatedBodyContent = await xtremMailer.functions.renderTemplate(context, 'activeTemplateReport', {
                enabled: false,
            });
            expect(htmlBody(populatedBodyContent)).to.include(
                htmlBody(
                    '<body class="ck ck-content"><div><h1 class="header">someResult Company: </h1><h2 class="header">Active: </h2><table><tr><th class="detail-row">Sites:</th></tr></table></div></body>',
                ),
            );
        }));
});
