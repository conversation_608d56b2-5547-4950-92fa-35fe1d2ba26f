import { Test, TextStream, htmlFragment } from '@sage/xtrem-core';
import * as xtremReporting from '@sage/xtrem-reporting';
import { expect } from 'chai';
import * as nodemailer from 'nodemailer';
import * as sinon from 'sinon';
import * as xtremMailer from '../../../lib/index';
import { Recipients } from '../../../lib/interfaces/_index';

describe('Send emails with nodemailer', () => {
    let sendMailSpy: sinon.SinonSpy<any[], any>;
    beforeEach(() => {
        sendMailSpy = sinon.spy();
        sinon.stub(nodemailer, 'createTransport').returns({ sendMail: sendMailSpy } as any);
    });

    it('should send an email with node mailer', async () => {
        Test.patchConfig({
            packages: {
                '@sage/xtrem-mailer': {
                    service: 'NodeMailer',
                    host: 'smtp.ethereal.email',
                    port: 25,
                    secure: false,
                    from: {
                        address: '<EMAIL>',
                        name: 'Test Name',
                    },
                    auth: {
                        user: '<EMAIL>',
                        pass: 'pv7fNYtke3kKc15hG8',
                    },
                },
            },
        });

        await Test.withContext(async context => {
            const mailer = await xtremMailer.createMailerService(context);
            const recipients: Recipients = {
                to: [{ address: '<EMAIL>', name: 'Petra.Howell' }],
            };

            const variables = { who: 'world' };
            const html = '<h1>Hello {{who}}</h1>';
            const report = await context.create(xtremReporting.nodes.Report, {
                name: 'nodeMailer',
                description: 'DESCRIPTION',
                parentPackage: 'xtrem-mailing',
                reportType: 'email',
            });

            await report.$.save();

            const template = await context.create(xtremReporting.nodes.ReportTemplate, {
                name: 'nodeMailerTemplate',
                htmlTemplate: TextStream.fromString(html),
                report,
                isExpertDocument: true,
            });

            await template.$.save();

            await report.$.set({ activeTemplate: template });

            await report.$.save();

            const subject = 'Test from NodeMailer';
            expect(await mailer.send(recipients, subject, await report.name, variables)).to.be.an('undefined');
            const spyCallArgs = sendMailSpy.getCall(0).args[0];
            const actualMailHtml = htmlFragment(spyCallArgs.html);
            const expectedMailHtml = htmlFragment(
                '<!DOCTYPE html>\n' +
                    '    <html>\n' +
                    '    <head><meta charset="utf-8">\n' +
                    '        <style>\n' +
                    '            :root {\n' +
                    '                --background: #FFFFFF;\n' +
                    '                --black55: rgba(0, 0, 0, 0.55);\n' +
                    '                --black65: rgba(0, 0, 0, 0.65);\n' +
                    '                --black90: rgba(0, 0, 0, 0.90);\n' +
                    '                --error: #C7384F;\n' +
                    '                --gold: #FFB500;\n' +
                    '                --info: #0077C8;\n' +
                    '                --logo: #00DC00;\n' +
                    '                --slate: #003349;\n' +
                    '                --slate20: #335C6D;\n' +
                    '                --slate40: #668592;\n' +
                    '                --slate60: #99ADB6;\n' +
                    '                --slate80: #CCD6DB;\n' +
                    '                --slate90: #E5EAEC;\n' +
                    '                --slate95: #F2F5F6;\n' +
                    '                --success: #00B000;\n' +
                    '                --tableSeparator: #D9E0E4;\n' +
                    '                --textAndLabels: rgba(0, 0, 0, 0.85);\n' +
                    '                --themePrimary: #008A21;\n' +
                    '                --themePrimaryHover: #005C9A;\n' +
                    '                --warning: #E96400;\n' +
                    '            }\n' +
                    '\n' +
                    '            @media print { body { -webkit-print-color-adjust: exact; } }\n' +
                    '            body {\n' +
                    '                font-size: 12pt;\n' +
                    '                margin: 0;\n' +
                    '            }\n' +
                    '\n' +
                    '            h1,h2,h3,h4,th{\n' +
                    '                color: var(--themePrimary);\n' +
                    '            }\n' +
                    '\n' +
                    '            .xtrem-page-break {\n' +
                    '                page-break-after: always;\n' +
                    '            }\n' +
                    '\n' +
                    '            \n' +
                    '        </style>\n' +
                    '    </head>\n' +
                    '    <body class="ck ck-content"><h1>Hello world</h1></body>\n' +
                    '    </html>',
            );

            expect(actualMailHtml).eq(expectedMailHtml);

            const actualMailText = spyCallArgs.text;
            expect(typeof actualMailText).eq('string');
            const expectedMailText = 'Hello world\n';
            expect((actualMailText as string)?.trimEnd()).eq(expectedMailText.trimEnd());

            await report.$.set({ activeTemplate: null });
            await report.$.save();

            await template.$.delete();
            await report.$.delete();
        });
    });
});
