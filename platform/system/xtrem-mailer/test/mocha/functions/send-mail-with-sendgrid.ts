import { ConfigManager } from '@sage/xtrem-config';
import { Test } from '@sage/xtrem-core';
import * as xtremReporting from '@sage/xtrem-reporting';
import * as xtremSystem from '@sage/xtrem-system';
import { assert, expect } from 'chai';
import * as sinon from 'sinon';
import * as xtremMailer from '../../../lib/index';
import * as fixtures from '../../fixtures';

const mailerConfig = ConfigManager?.current?.packages?.['@sage/xtrem-mailer'];

const sandbox = sinon.createSandbox();

describe('Send emails with sendGrid', () => {
    if (!mailerConfig) {
        it.skip('No mailer config is provided');
        return;
    }
    if (mailerConfig.service !== 'SendGrid') {
        it.skip('No mailer config is provided for SendGrid');
        return;
    }
    if (!mailerConfig.auth?.apiKey) {
        it.skip('Missing sendGrid authentication api key');
        return;
    }

    Test.patchConfig({
        packages: {
            '@sage/xtrem-mailer': {
                service: 'SendGrid',
                auth: { apiKey: mailerConfig.auth.apiKey },
                from: fixtures.dummySender,
                mailSetting: {
                    sandBoxMode: fixtures.isSandBoxMode,
                    footer: fixtures.dummyFooter,
                    checkSpam: {
                        enable: true,
                        threshold: 10,
                    },
                },
            },
        },
    });

    it('should send an email with sendGrid', () =>
        Test.withContext(
            async context => {
                expect(
                    await fixtures.createDummyMailer(
                        context,
                        'a',
                        fixtures.dummyRecipients,
                        fixtures.dummySubject,
                        fixtures.dummyAttachmentFiles,
                    ),
                ).to.be.an('undefined');
            },
            {
                mocks: ['axios'],
                scenario: 'send-email-sendGrid-attachments',
                directory: __dirname,
            },
        ));

    it('should send an email without attachments', () =>
        Test.withContext(
            async context => {
                expect(
                    await fixtures.createDummyMailer(context, 'b', fixtures.dummyRecipients, fixtures.dummySubject),
                ).to.be.an('undefined');
            },
            {
                mocks: ['axios'],
                scenario: 'send-email-sendGrid',
                directory: __dirname,
            },
        ));

    it('Send an email related to the "user" Node of the system package', async () => {
        await Test.withContext(async context => {
            sandbox
                .stub(xtremMailer.classes.SendGridMailService.prototype, 'sendMail')
                // eslint-disable-next-line @typescript-eslint/no-unused-vars
                .callsFake((recipients, _subject, _messageBody) => {
                    assert.deepEqual(recipients, {
                        to: [{ address: '<EMAIL>', name: 'Petra.Howell' }],
                        replyTo: [{ address: 'system.admin', name: '' }],
                    });
                    return Promise.resolve();
                });

            const user = await context.read(xtremSystem.nodes.User, { email: '<EMAIL>' });
            const mailer = await xtremMailer.createMailerService(context, user);
            const recipients: xtremMailer.interfaces.Recipients = {
                to: [{ address: '<EMAIL>', name: 'Petra.Howell' }],
            };
            const variables = {};

            await mailer.send(recipients, 'Dummy subject', 'usersByType', variables);

            sandbox.restore();
            await Test.rollbackCache(context);
        });
    });

    it('Send an email related to the "report" Node of the reporting package', async () => {
        await Test.withContext(async context => {
            sandbox
                .stub(xtremMailer.classes.SendGridMailService.prototype, 'sendMail')
                // eslint-disable-next-line @typescript-eslint/no-unused-vars
                .callsFake((recipients, _subject, _messageBody) => {
                    assert.deepEqual(recipients, {
                        to: [{ address: '<EMAIL>', name: 'Petra.Howell' }],
                        replyTo: [{ address: 'reporting.admin', name: '' }],
                    });
                    return Promise.resolve();
                });

            const report = await context.read(xtremReporting.nodes.Report, { name: 'usersByType' });
            const mailer = await xtremMailer.createMailerService(context, report);
            const recipients: xtremMailer.interfaces.Recipients = {
                to: [{ address: '<EMAIL>', name: 'Petra.Howell' }],
            };
            const variables = {};

            await mailer.send(recipients, 'Dummy subject', 'usersByType', variables);

            sandbox.restore();
            await Test.rollbackCache(context);
        });
    });

    it('Send an email without node default to the no-reply replyTo', async () => {
        await Test.withContext(async context => {
            sandbox
                .stub(xtremMailer.classes.SendGridMailService.prototype, 'sendMail')
                // eslint-disable-next-line @typescript-eslint/no-unused-vars
                .callsFake((recipients, _subject, _messageBody) => {
                    assert.deepEqual(recipients, {
                        to: [{ address: '<EMAIL>', name: 'Petra.Howell' }],
                        replyTo: [{ address: 'no-reply', name: '' }],
                    });
                    return Promise.resolve();
                });

            const mailer = await xtremMailer.createMailerService(context);
            const recipients: xtremMailer.interfaces.Recipients = {
                to: [{ address: '<EMAIL>', name: 'Petra.Howell' }],
            };
            const variables = {};

            await mailer.send(recipients, 'Dummy subject', 'usersByType', variables);

            sandbox.restore();
            await Test.rollbackCache(context);
        });
    });
});
