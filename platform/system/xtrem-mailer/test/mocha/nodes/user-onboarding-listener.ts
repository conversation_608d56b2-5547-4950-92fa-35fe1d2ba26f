import { ConfigManager } from '@sage/xtrem-config';
import { Test } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremMailer from '../../../lib';
import { MessageBody, Recipients } from '../../../lib/interfaces/mailer';
import * as fixtures from '../../fixtures';

const sandbox = sinon.createSandbox();

const mailerConfig = ConfigManager?.current?.packages?.['@sage/xtrem-mailer'] as xtremMailer.interfaces.MailerConfig;

describe('UserOnboardingListener node', () => {
    it('Listener : sendOnboardingMail - throw - missing redirectUrl ', async () => {
        Test.patchConfig({
            packages: {
                '@sage/xtrem-mailer': {
                    service: 'SendGrid',
                    redirectUrl: undefined,
                },
            } as Partial<xtremMailer.interfaces.MailerConfig>,
        });
        await Test.withContext(async context => {
            const users = await context.query(xtremSystem.nodes.User, { first: 1 }).toArray();
            const payload: xtremMailer.interfaces.OnboardingMail = {
                users,
                isAdmin: true,
            };

            let isCatch = false;
            await xtremMailer.nodes.UserOnboardingListener.sendOnboardingMail(context, payload).catch(error => {
                isCatch = true;
                assert.equal(error, 'Error: No Mailer redirectUrl is provided in the config file.');
            });
            assert.isTrue(isCatch, 'No catch on sendOnboardingMail ');
        });
    });

    it('Listener : sendOnboardingMail  ', async () => {
        Test.patchConfig({
            packages: {
                '@sage/xtrem-mailer': {
                    service: 'SendGrid',
                    auth: { apiKey: mailerConfig.auth.apiKey },
                    from: fixtures.dummySender,
                    redirectUrl: 'http://xtreem.com',
                    mailSetting: {
                        sandBoxMode: fixtures.isSandBoxMode,
                        footer: fixtures.dummyFooter,
                        checkSpam: {
                            enable: true,
                            threshold: 10,
                        },
                    },
                } as Partial<xtremMailer.interfaces.MailerConfig>,
            },
        });
        await Test.withContext(async context => {
            const users = await context.query(xtremSystem.nodes.User, { first: 1 }).toArray();
            const payload: xtremMailer.interfaces.OnboardingMail = {
                users,
                isAdmin: true,
            };
            let isRequestSend = false;

            sandbox
                .stub(xtremMailer.classes.SendGridMailService.prototype, 'sendMail')
                .callsFake(async (recipients: Recipients, subject: string, messageBody: MessageBody) => {
                    await Promise.resolve();
                    assert.isNotNull(recipients);
                    assert.isArray(recipients.to);
                    assert.equal(recipients.to[0].address, '<EMAIL>');
                    assert.equal(subject, 'Welcome to Sage Distribution and Manufacturing Operations');
                    assert.isNotNull(messageBody);
                    isRequestSend = true;
                });

            await xtremMailer.nodes.UserOnboardingListener.sendOnboardingMail(context, payload);
            assert.isTrue(isRequestSend);

            sandbox.restore();
        });
    });
});
