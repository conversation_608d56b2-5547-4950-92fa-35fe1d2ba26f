import { Test } from '@sage/xtrem-core';
import * as xtremMetadata from '@sage/xtrem-metadata';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import * as xtremMailer from '../../../index';

describe('SenderEmailSetting node', () => {
    it('Reading SenderEmailSetting node', () =>
        Test.withContext(async context => {
            const senderEmailSetting = await context.read(xtremMailer.nodes.SenderEmailSetting, { _id: '#1|300' });
            assert.instanceOf(senderEmailSetting, xtremMailer.nodes.SenderEmailSetting);
            assert.strictEqual(await senderEmailSetting.fromPrefix, 'reporting.admin');
            assert.strictEqual((await senderEmailSetting.replyToPrefixes).at(0), 'reporting.admin');
            assert.strictEqual(await (await senderEmailSetting.metaPackage)?.name, '@sage/xtrem-reporting');
            assert.isNull(await senderEmailSetting.node);
            assert.isNull(await senderEmailSetting.site);
            assert.isNull(await senderEmailSetting.company);
        }));

    it('Create SenderEmailSetting node - metaPackage is defaulted from the node', () =>
        Test.withContext(async context => {
            const dashboardNode = await context.read(xtremMetadata.nodes.MetaNodeFactory, { name: 'Dashboard' });

            const senderEmailSetting = await context.create(xtremMailer.nodes.SenderEmailSetting, {
                _sortValue: 400,
                emailConfig: '#1',
                node: dashboardNode,
                replyToPrefixes: ['any.email.prefix'],
            });
            await senderEmailSetting.$.save();
            assert.strictEqual(await (await senderEmailSetting.metaPackage)?.name, '@sage/xtrem-dashboard');
        }));

    it('Create SenderEmailSetting node - Fail duplicate configuration exists', () =>
        Test.withContext(async context => {
            const senderEmailSetting = await context.create(xtremMailer.nodes.SenderEmailSetting, {
                _sortValue: 400,
                emailConfig: '#1',
                replyToPrefixes: ['any.email.prefix'],
            });
            await assert.isRejected(senderEmailSetting.$.save(), 'The record was not created.');
            assert.deepEqual(senderEmailSetting.$.context.diagnoses, [
                {
                    message: 'An email setting already exists for this configuration.',
                    path: [],
                    severity: 3,
                },
            ]);
        }));

    it('Update SenderEmailSetting node', () =>
        Test.withContext(async context => {
            const senderEmailSetting = await context.read(
                xtremMailer.nodes.SenderEmailSetting,
                { _id: '#1|300' },
                { forUpdate: true },
            );
            const node = await context.read(xtremMetadata.nodes.MetaNodeFactory, { name: 'Report' });
            const company = await context.read(xtremSystem.nodes.Company, { id: 'S1' });
            const site = await context.read(xtremSystem.nodes.Site, { id: 'ETS1-S01' });
            await senderEmailSetting.$.set({ node, site, company });
            await senderEmailSetting.$.save();

            assert.deepEqual(senderEmailSetting.$.context.diagnoses, []);
            assert.equal(await (await senderEmailSetting.node)?.name, 'Report');
            assert.equal(await (await senderEmailSetting.company)?.id, 'S1');
            assert.equal(await (await senderEmailSetting.site)?.id, 'ETS1-S01');
        }));

    it('Update SenderEmailSetting node - Fail Invalid reply-to prefix', () =>
        Test.withContext(async context => {
            const senderEmailSetting = await context.read(
                xtremMailer.nodes.SenderEmailSetting,
                { _id: '#1|300' },
                { forUpdate: true },
            );
            await senderEmailSetting.$.set({
                replyToPrefixes: ['invalid email', '<EMAIL>', 'invalid email'],
            });
            await assert.isRejected(senderEmailSetting.$.save(), 'The record was not updated.');
            assert.deepEqual(senderEmailSetting.$.context.diagnoses, [
                {
                    message:
                        'You need to change the reply-to prefix. It cannot be empty, have an @ character, or a space.',
                    path: [],
                    severity: 3,
                },
            ]);
        }));

    it('Update SenderEmailSetting node - Fail Invalid from prefix', () =>
        Test.withContext(async context => {
            const senderEmailSetting = await context.read(
                xtremMailer.nodes.SenderEmailSetting,
                { _id: '#1|300' },
                { forUpdate: true },
            );
            await senderEmailSetting.$.set({
                fromPrefix: 'invalid email prefix',
            });
            await assert.isRejected(senderEmailSetting.$.save(), 'The record was not updated.');
            assert.deepEqual(senderEmailSetting.$.context.diagnoses, [
                {
                    message: 'You need to change the from prefix. It cannot be empty, have an @ character, or a space.',
                    path: [],
                    severity: 3,
                },
            ]);
        }));

    it('Delete SenderEmailSetting node', () =>
        Test.withContext(async context => {
            assert.isTrue(
                await context.exists(xtremMailer.nodes.SenderEmailSetting, { emailConfig: '#1', _sortValue: 200 }),
            );

            const senderEmailSetting = await context.read(
                xtremMailer.nodes.SenderEmailSetting,
                { _id: '#1|200' },
                { forUpdate: true },
            );
            await senderEmailSetting.$.delete();

            assert.isFalse(
                await context.exists(xtremMailer.nodes.SenderEmailSetting, { emailConfig: '#1', _sortValue: 200 }),
            );
        }));

    it('Update SenderEmailSetting node - Fail because of inconsistency between metaPackage and node', () =>
        Test.withContext(async context => {
            const senderEmailSetting = await context.read(
                xtremMailer.nodes.SenderEmailSetting,
                { _id: '#1|300' },
                { forUpdate: true },
            );
            const node = await context.read(xtremMetadata.nodes.MetaNodeFactory, { name: 'SysCustomer' });
            await senderEmailSetting.$.set({ node });
            await assert.isRejected(senderEmailSetting.$.save(), 'The record was not updated.');
            assert.deepEqual(senderEmailSetting.$.context.diagnoses, [
                {
                    message: 'The record is not valid. You need to select a different record.',
                    path: ['metaPackage'],
                    severity: 3,
                },
            ]);
        }));

    it('Update SenderEmailSetting node - Fail because of inconsistency between company and site', () =>
        Test.withContext(async context => {
            const senderEmailSetting = await context.read(
                xtremMailer.nodes.SenderEmailSetting,
                { _id: '#1|300' },
                { forUpdate: true },
            );
            const company = await context.read(xtremSystem.nodes.Company, { id: 'US001' });
            const site = await context.read(xtremSystem.nodes.Site, { id: 'ETS1-S01' });
            await senderEmailSetting.$.set({ site, company });
            await assert.isRejected(senderEmailSetting.$.save(), 'The record was not updated.');
            assert.deepEqual(senderEmailSetting.$.context.diagnoses, [
                {
                    message: 'The record is not valid. You need to select a different record.',
                    path: ['site'],
                    severity: 3,
                },
            ]);
        }));
});
