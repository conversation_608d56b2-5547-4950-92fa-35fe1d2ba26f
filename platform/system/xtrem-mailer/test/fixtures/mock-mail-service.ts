import { WorkflowMockOptions, assertDeepPartialMatch } from '@sage/xtrem-core';
import { MailService } from '../../lib/classes/mail-service';
import * as xtremMailer from '../../lib/index';
import { MailerConfig } from '../../lib/interfaces/mailer';

interface MailerMockConfig extends MailerConfig {
    expectedCalls: WorkflowMockOptions['calls'];
}

export class MockMailService extends MailService {
    callIndex = 0;

    // eslint-disable-next-line class-methods-use-this
    validateConfig(): xtremMailer.interfaces.ValidateConfig {
        return { isValid: true };
    }

    // eslint-disable-next-line class-methods-use-this
    setDefaultConfig(): Partial<xtremMailer.interfaces.MailerConfig> {
        return {};
    }

    sendMail(
        recipients: xtremMailer.interfaces.Recipients,
        subject: string,
        messageBody: xtremMailer.interfaces.MessageBody,
        attachments: xtremMailer.interfaces.Attachments,
    ): Promise<void> {
        const config = this.config as MailerMockConfig;
        if (this.callIndex === config.expectedCalls.length) {
            throw new Error(`Too many calls to sendMail mocked method: ${this.callIndex}`);
        }
        const expectedCall = config.expectedCalls[this.callIndex];
        this.callIndex += 1;

        const expectedInput = expectedCall.input;
        const actualInput = { recipients, subject, messageBody, attachments };
        try {
            assertDeepPartialMatch(actualInput, expectedInput);
        } catch (error) {
            this.logger.error(error.message);
            throw error;
        }
        return Promise.resolve();
    }
}
