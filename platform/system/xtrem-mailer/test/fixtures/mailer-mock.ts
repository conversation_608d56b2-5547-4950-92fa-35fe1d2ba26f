import { Test, WorkflowMock } from '@sage/xtrem-core';
import { Logger } from '@sage/xtrem-log';
import * as sinon from 'sinon';
import * as xtremMailer from '../../lib/functions/create-mailer-service';
import { MailerConfig, MailerService } from '../../lib/interfaces/mailer';
import { MockMailService } from './mock-mail-service';

export const logger = Logger.getLogger(__filename, 'test-mailer-mock');

const sandbox = sinon.createSandbox();

export class MailerMock extends WorkflowMock {
    async execute<T>(body: () => Promise<T>): Promise<T> {
        Test.patchConfig({ packages: { '@sage/xtrem-mailer': { service: 'TestService' } } });

        const stub = sandbox.stub(xtremMailer, 'createMailerService').callsFake(context => {
            return new MockMailService(
                context,
                {
                    service: 'TestService' as unknown as MailerService,
                    auth: {},
                    from: { name: 'Test', address: '<EMAIL>' },
                    replyTo: { name: 'Nobody', address: '<EMAIL>' },
                    redirectUrl: 'test-url',
                    expectedCalls: this.options.calls,
                } as MailerConfig,
                logger,
            ).init();
        });
        try {
            return await body();
        } finally {
            stub.restore();
        }
    }
}
