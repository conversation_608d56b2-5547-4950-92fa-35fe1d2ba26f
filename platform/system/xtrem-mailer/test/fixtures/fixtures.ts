import { Context, TextStream } from '@sage/xtrem-core';
import * as xtremReporting from '@sage/xtrem-reporting';
import * as path from 'path';
import { createMailerService } from '../../lib';
import { Recipients } from '../../lib/interfaces/_index';

export const dummyRecipients = { to: [{ address: '<EMAIL>', name: 'Test Receiver' }] };
export const dummySender = {
    address: '<EMAIL>',
    name: 'Test Name',
};
export const dummyAttachmentFiles = [
    path.resolve('test/mocha/functions/dummy-files/dummy-txt.txt'),
    path.resolve('test/mocha/functions/dummy-files/dummy-pdf.pdf'),
];

export const isSandBoxMode = true;

export const dummyFooter = {
    enable: true,
    text: 'Footer Text',
    html: '<h4>Footer Text</h4>',
};

export const dummySubject = 'Test from SendGrid';

export const redirectUrl = 'http://localhost:8240';

export async function createDummyMailer(
    context: Context,
    prefix: string,
    recipients: Recipients,
    subject: string,
    attachmentFiles?: string[],
): Promise<void> {
    const mailer = await createMailerService(context);

    const variables = { who: 'world' };
    const html = '<h1>Hello {{who}}</h1>';
    const reportName = `${prefix}companyDetails`;
    await context.runInWritableContext(async writableContext => {
        const report = await writableContext.create(xtremReporting.nodes.Report, {
            name: reportName,
            description: 'DESCRIPTION',
            parentPackage: 'xtrem-mailing',
            reportType: 'email',
        });

        await report.$.save();

        const template = await writableContext.create(xtremReporting.nodes.ReportTemplate, {
            name: `${prefix}companyDetailsTemplate`,
            htmlTemplate: TextStream.fromString(html),
            report,
        });

        await template.$.save();

        await report.$.set({ activeTemplate: template });

        await report.$.save();
    });
    return mailer.send(recipients, subject, reportName, variables, attachmentFiles, false);
}
