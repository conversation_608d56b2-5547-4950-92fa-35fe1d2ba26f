envConfigs:
  testActiveServiceOptions:
    - workflow
scenarios:
  Can send an email (en-US):
    envConfigs:
      locale: 'en-US'
    workflow: 'sendEmailActionTest'
    mocks:
      - path: '@sage/xtrem-mailer/build/test/fixtures/mailer-mock/MailerMock'
        calls:
          - input:
              recipients: { to: [{ name: '<PERSON>', address: '<EMAIL>' }], cc: [], bcc: [] }
              subject: 'Hello <PERSON>'
              messageBody: { html: '~Hi <PERSON>,', text: '~Hi <PERSON>,' }
    startEvent:
      topic: 'WorkflowProcess/testStarted'
      payload:
        workflow: 'sendEmailActionTest'
        parameters: { userName: '<PERSON>', email: '<EMAIL>' }
    expectedResult:
      status: 'success'
      variables:
        done: true

  Can send an email (fr-FR):
    envConfigs:
      locale: 'fr-FR'
    workflow: 'sendEmailActionTest'
    mocks:
      - path: '@sage/xtrem-mailer/build/test/fixtures/mailer-mock/MailerMock'
        calls:
          - input:
              recipients: { to: [{ name: '<PERSON>', address: '<EMAIL>' }], cc: [], bcc: [] }
              subject: 'Bon<PERSON>r <PERSON>e'
              messageBody: { html: '~Bonjour John Doe,', text: '~Bonjour John Doe,' }
    startEvent:
      topic: 'WorkflowProcess/testStarted'
      payload:
        workflow: 'sendEmailActionTest'
        parameters: { userName: 'John Doe', email: '<EMAIL>' }
    expectedResult:
      status: 'success'
      variables:
        done: true
