import { asyncArray, BusinessRuleError, Context, decorators, Logger, Node, ValidationSeverity } from '@sage/xtrem-core';
import * as xtremUpload from '@sage/xtrem-upload';
import * as xtremWorkflow from '@sage/xtrem-workflow';
import * as fs from 'fs';
import { nanoid } from 'nanoid';
import * as os from 'os';
import * as path from 'path';
import * as xtremMailer from '../index';
import { Address, Recipients } from '../interfaces/mailer';

const logger = Logger.getLogger(__filename, 'mailer');

interface CopyAttachmentsResult {
    tempFolder: string;
    files: string[];
}
@decorators.node<Mailer>({
    isPublished: true,
    // TODO: This mutation is only published with the workflow option for now.
    // We may want to always publish it.
    serviceOptions: () => [xtremWorkflow.serviceOptions.workflow],
})
export class Mailer extends Node {
    @decorators.asyncMutation<typeof Mailer, 'sendMail'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'subject',
                type: 'string',
                isMandatory: true,
            },
            {
                name: 'recipients',
                type: 'object',
                isMandatory: true,
                properties: {
                    to: {
                        type: 'array',
                        item: { type: 'object', properties: { name: 'string', address: 'string' } },
                    },
                    cc: {
                        type: 'array',
                        item: { type: 'object', properties: { name: 'string', address: 'string' } },
                    },
                    bcc: {
                        type: 'array',
                        item: { type: 'object', properties: { name: 'string', address: 'string' } },
                    },
                },
            },
            {
                name: 'reportName',
                type: 'string',
                isMandatory: true,
            },
            {
                name: 'reportVariables',
                type: 'string',
                isMandatory: true,
            },
            {
                name: 'attachments',
                type: 'array',
                item: { type: 'reference', node: () => xtremUpload.nodes.UploadedFile },
            },
            {
                name: 'from',
                type: 'object',
                properties: { name: 'string', address: 'string' },
                isMandatory: false,
            },
            {
                name: 'replyTo',
                type: 'object',
                properties: { name: 'string', address: 'string' },
                isMandatory: false,
            },
        ],
        return: {
            type: 'boolean',
        },
    })
    static async sendMail(
        context: Context,
        subject: string,
        recipients: Recipients,
        reportName: string,
        reportVariables: string,
        attachments?: number[],
        from?: Address,
        replyTo?: Address,
    ): Promise<boolean> {
        logger.info(`Send mail using report ${reportName}`);
        if (!recipients.to?.length) throw new BusinessRuleError(`Cannot send mail: missing 'to' recipient`);
        const mailer = await xtremMailer.createMailerService(context);
        const configToRestore = mailer.mailerConfig;
        let copyResult: CopyAttachmentsResult | undefined;
        try {
            if (from != null || replyTo != null) {
                const configToSet = { ...mailer.mailerConfig };
                if (from != null) {
                    configToSet.from = from;
                }
                if (replyTo != null) {
                    configToSet.replyTo = replyTo;
                }
                mailer.mailerConfig = configToSet;
            }

            const config =
                context.configuration.getPackageConfig<xtremMailer.interfaces.MailerConfig>('@sage/xtrem-mailer');
            if (!config?.redirectUrl) {
                throw new BusinessRuleError('No Mailer redirectUrl is provided in the config file.');
            }

            if (attachments?.length) {
                copyResult = await Mailer._copyUploadedFilesToLocal(context, attachments);
            }

            // send the email
            await mailer.send(recipients, subject, reportName, JSON.parse(reportVariables), copyResult?.files);
        } finally {
            mailer.mailerConfig = configToRestore;
            if (copyResult != null) {
                const copyResultNotNull = copyResult;
                // Register a task to delete the temp folder.
                // Leave the time for the email to be sent.
                setTimeout(async () => {
                    logger.verbose(() => `Deleting temp folder: ${copyResultNotNull.tempFolder}`);
                    await fs.promises.rm(copyResultNotNull.tempFolder, { recursive: true });
                }, 30_000);
            }
        }

        return true;
    }

    private static async _copyUploadedFilesToLocal(
        context: Context,
        attachments: number[],
    ): Promise<CopyAttachmentsResult> {
        const tempFolder = await fs.promises.mkdtemp(path.join(os.tmpdir(), 'mailer-files-'));
        logger.info(() => `Downloading attachments to temp folder ${tempFolder}`);
        if (tempFolder == null) throw new BusinessRuleError('Failed to create temporary folder');
        return {
            tempFolder,
            files: (
                await asyncArray(attachments)
                    .map(async id => {
                        const uploadedFile = await context.read(xtremUpload.nodes.UploadedFile, { _id: id });
                        const extension = path.extname(await uploadedFile.filename);
                        const localFilename = path.join(tempFolder, `${nanoid()}${extension}`);
                        try {
                            await uploadedFile.downloadTo(context, localFilename);
                        } catch (error) {
                            logger.error(
                                () => `Failed to download attachment ${id} to ${localFilename}, error=${error.stack}`,
                            );
                            context.diagnoses.push({
                                severity: ValidationSeverity.error,
                                message: `Failed to download attachment ${id} to ${localFilename}`,
                                path: [],
                            });
                            return null;
                        }
                        return localFilename;
                    })
                    .toArray()
            ).filter(f => f) as string[],
        };
    }
}
