import { decorators, Node, Reference } from '@sage/xtrem-core';
import * as xtremMetadata from '@sage/xtrem-metadata';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremMailer from '../index';

@decorators.node<SenderEmailSetting>({
    isPublished: true,
    storage: 'sql',
    isVitalCollectionChild: true,
    canRead: true,
    canSearch: true,
    canDeleteMany: true,
    async controlBegin(cx) {
        const duplicateSenderEmailSetting = await (await this.emailConfig).senderEmailSettings
            .filter(line => line._id !== this._id)
            .some(
                async senderEmailSetting =>
                    (await senderEmailSetting.metaPackage)?._id === (await this.metaPackage)?._id &&
                    (await senderEmailSetting.node)?._id === (await this.node)?._id &&
                    (await senderEmailSetting.company)?._id === (await this.company)?._id &&
                    (await senderEmailSetting.site)?._id === (await this.site)?._id,
            );
        await cx.error
            .withMessage(
                '@sage/xtrem-mailer/nodes__sender_email_setting__duplicate_sender_email_setting',
                'An email setting already exists for this configuration.',
            )
            .if(duplicateSenderEmailSetting)
            .is.true();

        const invalidReplyToPrefixExists = (await this.replyToPrefixes).some(
            replyToPrefix => replyToPrefix.length === 0 || replyToPrefix.includes('@') || replyToPrefix.includes(' '),
        );
        await cx.error
            .withMessage(
                '@sage/xtrem-mailer/nodes__sender_email_setting__invalid_email_prefix',
                'You need to change the reply-to prefix. It cannot be empty, have an @ character, or a space.',
            )
            .if(invalidReplyToPrefixExists)
            .is.true();

        const fromPrefix = await this.fromPrefix;
        await cx.error
            .withMessage(
                '@sage/xtrem-mailer/nodes__sender_email_setting__invalid_from_prefix',
                'You need to change the from prefix. It cannot be empty, have an @ character, or a space.',
            )
            .if(fromPrefix.length === 0 || fromPrefix.includes('@') || fromPrefix.includes(' '))
            .is.true();
    },
})
export class SenderEmailSetting extends Node {
    @decorators.referenceProperty<SenderEmailSetting, 'emailConfig'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremMailer.nodes.SysEmailConfig,
        lookupAccess: true,
    })
    readonly emailConfig: Reference<xtremMailer.nodes.SysEmailConfig>;

    @decorators.stringProperty<SenderEmailSetting, 'fromPrefix'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
        defaultValue: 'no-reply',
    })
    readonly fromPrefix: Promise<string>;

    @decorators.referenceProperty<SenderEmailSetting, 'node'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMetadata.nodes.MetaNodeFactory,
        lookupAccess: true,
        isNullable: true,
        filters: {
            lookup: {
                async package() {
                    const metaPackage = await this.metaPackage;
                    if (!metaPackage) {
                        return {};
                    }
                    return { _eq: metaPackage._id };
                },
            },
        },
    })
    readonly node: Reference<xtremMetadata.nodes.MetaNodeFactory | null>;

    @decorators.referenceProperty<SenderEmailSetting, 'metaPackage'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMetadata.nodes.MetaPackage,
        lookupAccess: true,
        isNullable: true,
        dependsOn: ['node'],
        filters: {
            control: {
                async name() {
                    const node = await this.node;
                    if (!node) {
                        return {};
                    }
                    return { _eq: await (await node.package).name };
                },
            },
        },
        async defaultValue() {
            const node = await this.node;
            if (!node) {
                return null;
            }
            return node.package;
        },
    })
    readonly metaPackage: Reference<xtremMetadata.nodes.MetaPackage | null>;

    @decorators.referenceProperty<SenderEmailSetting, 'company'>({
        isStored: true,
        isPublished: true,
        node: () => xtremSystem.nodes.Company,
        lookupAccess: true,
        isNullable: true,
    })
    readonly company: Reference<xtremSystem.nodes.Company | null>;

    @decorators.referenceProperty<SenderEmailSetting, 'site'>({
        isStored: true,
        isPublished: true,
        node: () => xtremSystem.nodes.Site,
        lookupAccess: true,
        isNullable: true,
        dependsOn: ['company'],
        filters: {
            control: {
                legalCompany() {
                    return this.company;
                },
            },
        },
    })
    readonly site: Reference<xtremSystem.nodes.Site | null>;

    @decorators.stringArrayProperty<SenderEmailSetting, 'replyToPrefixes'>({
        isStored: true,
        isPublished: true,
    })
    readonly replyToPrefixes: Promise<string[]>;

    @decorators.stringProperty<SenderEmailSetting, 'displayReplyToPrefixes'>({
        isPublished: true,
        dependsOn: ['replyToPrefixes'],
        async computeValue() {
            return (await this.replyToPrefixes).join(', ');
        },
    })
    readonly displayReplyToPrefixes: Promise<string>;
}
