import { BusinessRuleError, Collection, Context, decorators, Node, SystemError } from '@sage/xtrem-core';
import * as xtremInfrastructureAdapter from '@sage/xtrem-infrastructure-adapter';
import * as xtremSystem from '@sage/xtrem-system';
import { SendGridMailService } from '../classes/sendgrid-mail-service';
import { EmailSystem, emailSystemDataType } from '../enums/email-system';
import { createMailerService } from '../functions/create-mailer-service';
import * as xtremMailer from '../index';
import { EmailSystemConfiguration } from '../interfaces/mailer';
import { ValidationResponse } from '../interfaces/send-grid';
import {
    DomainAuthentication,
    SanitizedEmailSystemConfiguration,
    SanitizedSysEmailConfig,
} from '../shared-functions/interfaces/email-config';

@decorators.node<SysEmailConfig>({
    isPublished: true,
    storage: 'sql',
    canRead: true,
    canUpdate: true,
    isSetupNode: true,
    indexes: [{ orderBy: { id: +1 }, isNaturalKey: true, isUnique: true }],
})
export class SysEmailConfig extends Node {
    @decorators.stringProperty<SysEmailConfig, 'id'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.id,
        isFrozen: true,
    })
    readonly id: Promise<string>;

    @decorators.enumProperty<SysEmailConfig, 'emailSystem'>({
        isStored: true,
        isPublished: true,
        dataType: () => emailSystemDataType,
        defaultValue: 'sage',
    })
    readonly emailSystem: Promise<EmailSystem>;

    @decorators.stringProperty<SysEmailConfig, 'domain'>({
        isPublished: true,
        isFrozen: true,
        dataType: () => xtremSystem.dataTypes.httpUrl,
        async computeValue() {
            // If the domain is set in the cloud configuration, return it
            let cloudConfigDomain: string | null = null;
            if (this.$.context.tenantId) {
                cloudConfigDomain = await xtremInfrastructureAdapter.InfrastructureHelper.getEmailDomainOfTenant(
                    this.$.context.tenantId,
                );
                if (cloudConfigDomain) return cloudConfigDomain;
            }
            // If the domain is not set in the cloud configuration, get it from the first admin user email
            const firstAdminUser = await this.$.context
                .query(xtremSystem.nodes.User, {
                    filter: { isFirstAdminUser: true },
                })
                .at(0);
            if (firstAdminUser) {
                const domain = getDomainFromEmail(await firstAdminUser.email);
                return domain;
            }
            return '';
        },
    })
    readonly domain: Promise<string>;

    @decorators.jsonProperty<SysEmailConfig, 'configuration'>({
        isStored: true,
        isNullable: true,
    })
    readonly configuration: Promise<EmailSystemConfiguration | null>;

    @decorators.collectionProperty<SysEmailConfig, 'senderEmailSettings'>({
        isPublished: true,
        node: () => xtremMailer.nodes.SenderEmailSetting,
        isVital: true,
        reverseReference: 'emailConfig',
        lookupAccess: true,
    })
    readonly senderEmailSettings: Collection<xtremMailer.nodes.SenderEmailSetting>;

    @decorators.jsonProperty<SysEmailConfig, 'sanitizedConfiguration'>({
        isPublished: true,
        computeValue() {
            return this.sanitizeEmailConfiguration();
        },
    })
    readonly sanitizedConfiguration: Promise<SanitizedEmailSystemConfiguration>;

    private async sanitizeEmailConfiguration(): Promise<SanitizedEmailSystemConfiguration> {
        const sendGrid = (await this.configuration)?.sendGrid;
        if (!sendGrid) return {};
        const { user_id, username, ...copySendGrid } = sendGrid;
        return { sendGrid: copySendGrid };
    }

    static async sanitizeSysEmailConfig(node: SysEmailConfig): Promise<SanitizedSysEmailConfig> {
        return {
            id: await node.id,
            emailSystem: await node.emailSystem,
            domain: await node.domain,
            sanitizedConfiguration: await node.sanitizedConfiguration,
        };
    }

    /**
     * This method updates the isActive flag on the SysEnumTransformation node for the given remote app'
     * @param context
     * @param parameters includes remoteAppId, active, and version
     *
     */
    @decorators.mutation<typeof SysEmailConfig, 'authenticateSendGridCustomDomain'>({
        isPublished: true,
        parameters: [
            {
                name: 'id',
                type: 'string',
            },
        ],
        return: {
            type: 'object',
            properties: {
                id: {
                    type: 'string',
                },
                emailSystem: {
                    type: 'enum',
                    dataType: () => emailSystemDataType,
                },
                domain: {
                    type: 'string',
                },
                sanitizedConfiguration: {
                    type: 'json',
                },
            },
        },
    })
    static async authenticateSendGridCustomDomain(context: Context, id: string): Promise<SanitizedSysEmailConfig> {
        const mailerService = (await createMailerService(context)) as SendGridMailService;
        const node = await context.read(SysEmailConfig, { id }, { forUpdate: true });
        const domain = await node.domain;
        if (!domain) throw new SystemError('Domain name missing. Please check the first admin user email');
        let authenticatedDomainResult = await mailerService.getAuthenticatedDomainByName(domain);
        if (authenticatedDomainResult) {
            await node.$.set({
                configuration: {
                    sendGrid: { ...authenticatedDomainResult },
                    hash: await createConfigurationHash(context, await node.domain, authenticatedDomainResult),
                },
            });
            await node.$.save();
            return this.validateSendGridDomainAuthentication(context, id);
        }
        authenticatedDomainResult = (await mailerService.authenticateDomain(domain)) as DomainAuthentication;
        await node.$.set({
            configuration: {
                sendGrid: { ...authenticatedDomainResult },
                hash: await createConfigurationHash(context, await node.domain, authenticatedDomainResult),
            },
        });
        await node.$.save();

        if (!(await node.configuration)) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-mailer/pages_sys_email_config_try_again',
                    'Failed to authenticate the domain: "{{domainValue}}". Check your configuration and try again.',
                    { domainValue: await node.domain },
                ),
            );
        }

        return SysEmailConfig.sanitizeSysEmailConfig(node);
    }

    /**
     * This method updates the isActive flag on the SysEnumTransformation node for the given remote app'
     * @param context
     * @param parameters includes remoteAppId, active, and version
     *
     */
    @decorators.mutation<typeof SysEmailConfig, 'validateSendGridDomainAuthentication'>({
        isPublished: true,
        parameters: [
            {
                name: 'id',
                type: 'string',
                isMandatory: true,
            },
        ],
        return: {
            type: 'object',
            properties: {
                id: {
                    type: 'string',
                },
                emailSystem: {
                    type: 'enum',
                    dataType: () => emailSystemDataType,
                },
                domain: {
                    type: 'string',
                },
                sanitizedConfiguration: {
                    type: 'json',
                },
            },
        },
    })
    static async validateSendGridDomainAuthentication(context: Context, id: string): Promise<SanitizedSysEmailConfig> {
        const node = await context.read(SysEmailConfig, { id }, { forUpdate: true });
        const domainId = (await node.configuration)?.sendGrid?.id;
        if (!domainId) throw new SystemError('Domain id missing. Please authenticate the domain first');

        const mailerService = (await createMailerService(context)) as SendGridMailService;

        const validationResult = (await mailerService.validateDomainAuthentication(
            domainId,
        )) as unknown as ValidationResponse;

        const authenticatedDomain = await mailerService.getAuthenticatedDomain(domainId);

        authenticatedDomain.valid = validationResult.valid;

        if (getValidationErrors(validationResult).length > 0) {
            authenticatedDomain.dns.mail_cname.reason = validationResult.validation_results.mail_cname.reason ?? null;
            authenticatedDomain.dns.dkim1.reason = validationResult.validation_results.dkim1.reason ?? null;
            authenticatedDomain.dns.dkim2.reason = validationResult.validation_results.dkim2.reason ?? null;
        }

        await node.$.set({
            configuration: {
                sendGrid: { ...authenticatedDomain },
                hash: await createConfigurationHash(context, await node.domain, authenticatedDomain),
            },
        });
        await node.$.save();

        if (!(await node.configuration)) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-mailer/pages_sys_email_config_could_not_validated',
                    'Your domain was not validated.',
                ),
            );
        }

        if (!(await node.configuration)?.sendGrid?.valid) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-mailer/pages_sys_email_config_dns_zone_validation_failed',
                    'Your domain was not validated. Resolve any errors in your DNS zone and try again.',
                ),
            );
        }

        return SysEmailConfig.sanitizeSysEmailConfig(node);
    }

    @decorators.mutation<typeof SysEmailConfig, 'setAsDefault'>({
        isPublished: true,
        parameters: [
            {
                name: 'id',
                type: 'string',
                isMandatory: true,
            },
            {
                name: 'emailSystem',
                type: 'string',
                isMandatory: true,
            },
        ],
        return: {
            type: 'object',
            properties: {
                id: {
                    type: 'string',
                },
                emailSystem: {
                    type: 'enum',
                    dataType: () => emailSystemDataType,
                },
                domain: {
                    type: 'string',
                },
                sanitizedConfiguration: {
                    type: 'json',
                },
            },
        },
    })
    static async setAsDefault(
        context: Context,
        id: string,
        emailSystem: EmailSystem,
    ): Promise<SanitizedSysEmailConfig> {
        try {
            const node = await context.read(SysEmailConfig, { id }, { forUpdate: true });
            if (emailSystem === 'custom') {
                if (!(await node.configuration)?.sendGrid?.id) {
                    throw new SystemError('Domain not authenticated. Please authenticate the domain first');
                }
                if (!(await node.configuration)?.sendGrid?.valid) {
                    throw new SystemError('Domain not validated. Please validate the domain first');
                }
            }
            await node.$.set({ emailSystem });
            await node.$.save();

            if (!(await node.configuration)) {
                throw new BusinessRuleError(
                    context.localize(
                        '@sage/xtrem-mailer/pages_sys_email_config_successfully_failed_to_set_domain',
                        'Failed to set the domain as the default: "{{emailSystemValue}}". Try again.',
                        { emailSystemValue: emailSystem },
                    ),
                );
            }
            return await SysEmailConfig.sanitizeSysEmailConfig(node);
        } catch (error) {
            throw new SystemError(error);
        }
    }

    @decorators.mutation<typeof SysEmailConfig, 'removeCustomDomainConfiguration'>({
        isPublished: true,
        parameters: [
            {
                name: 'sysEmailConfig',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => SysEmailConfig,
            },
        ],
        return: {
            type: 'boolean',
        },
    })
    static async removeCustomDomainConfiguration(context: Context, sysEmailConfig: SysEmailConfig): Promise<boolean> {
        await sysEmailConfig.$.set({
            configuration: null,
            emailSystem: 'sage',
        });
        await sysEmailConfig.$.save();
        return true;
    }
}

function getDomainFromEmail(email: string): string {
    return email.split('@')[1];
}

function getValidationErrors(validationResponse: ValidationResponse): string[] {
    return Object.entries(validationResponse.validation_results)
        .map(([key, result]) => (!result.valid ? `Validation error in ${key}: ${result.reason}` : null))
        .filter(error => error !== null) as string[];
}

export function createConfigurationHash(
    context: Context,
    domain: string,
    settings: DomainAuthentication,
): Promise<string> {
    const sanitizedSettings = {
        id: settings.id,
        user_id: settings.user_id,
        domain: settings.domain,
        username: settings.username,
        valid: settings.valid,
        dns: {
            dkim1: {
                data: settings.dns.dkim1.data,
                host: settings.dns.dkim1.host,
                type: settings.dns.dkim1.type,
                valid: settings.dns.dkim1.valid,
            },
            dkim2: {
                data: settings.dns.dkim2.data,
                host: settings.dns.dkim2.host,
                type: settings.dns.dkim2.type,
                valid: settings.dns.dkim2.valid,
            },
            mail_cname: {
                data: settings.dns.mail_cname.data,
                host: settings.dns.mail_cname.host,
                type: settings.dns.mail_cname.type,
                valid: settings.dns.mail_cname.valid,
            },
        },
    };
    return context.vault.createHmac(`${JSON.stringify(sanitizedSettings)}:${domain}`);
}
