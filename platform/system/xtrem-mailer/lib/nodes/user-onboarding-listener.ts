import { ConfigManager } from '@sage/xtrem-config';
import { asyncArray, BusinessRuleError, Context, decorators, Logger, Node } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremMailer from '../index';

const logger = Logger.getLogger(__filename, 'UserOnboardingListener');

@decorators.node<UserOnboardingListener>({
    package: 'xtrem-mailer',
})
export class UserOnboardingListener extends Node {
    @decorators.notificationListener({
        startsReadOnly: true,
        topic: 'onboardingMail',
    })
    static async sendOnboardingMail(context: Context, payload: xtremMailer.interfaces.OnboardingMail): Promise<void> {
        logger.debug(() => `Payload: ${JSON.stringify(payload)}`);

        const config =
            context.configuration.getPackageConfig<xtremMailer.interfaces.MailerConfig>('@sage/xtrem-mailer');
        if (!config || !config.redirectUrl) {
            throw new BusinessRuleError('No Mailer redirectUrl is provided in the config file.');
        }

        // create the mailer
        const mailer = await xtremMailer.createMailerService(context);
        const urlTenant = `${config.redirectUrl}`;

        let tenantName = '';

        if (context.tenantId) {
            const tenant = await Context.tenantManager.getTenantsInfo(context, context.tenantId);
            tenantName = tenant[0].name;
        } else {
            throw new BusinessRuleError('Tenant ID not set');
        }

        let productName: string = context.localize(
            '@sage/xtrem-mailer/user__onboarding_listener___product__name',
            'Sage Distribution and Manufacturing Operations',
        );
        let subject: string = context.localize(
            '@sage/xtrem-mailer/user__onboarding_listener___email__subject',
            'Welcome to Sage Distribution and Manufacturing Operations',
        );
        if (
            ConfigManager.current.productName &&
            ConfigManager.current.productName !== 'Sage Distribution and Manufacturing Operations'
        ) {
            productName = ConfigManager.current.productName;
            subject = `${context.localize(
                '@sage/xtrem-mailer/user__onboarding_listener___email__subject__welcome_to',
                'Welcome to',
            )} ${productName}`;
        }

        const emailTemplate = payload.isAdmin ? 'onboarding_tenant' : 'onboarding_user';

        await asyncArray(payload.users).forEach(async (user: xtremSystem.nodes.User) => {
            const userName = `${await user.firstName} ${await user.lastName}`;

            const data: xtremMailer.interfaces.OnboardingMailData = {
                userName,
                productName,
                tenantName,
                urlTenant,
            };

            const recipients: xtremMailer.interfaces.Recipients = {
                to: [{ address: await user.email, name: userName }],
            };

            await context.runInWritableContext(async writableContext => {
                const updateUserPref = await writableContext.tryRead(
                    xtremSystem.nodes.UserPreferences,
                    { user: user._id },
                    { forUpdate: true },
                );

                if (updateUserPref) {
                    await updateUserPref.$.set({ isWelcomeMailSent: true });
                    await updateUserPref.$.save();
                } else {
                    const newUserPref = await writableContext.create(xtremSystem.nodes.UserPreferences, {
                        user: user._id,
                        isWelcomeMailSent: true,
                    });
                    await newUserPref.$.save();
                }
            });

            // send the email
            await mailer.send(recipients, subject, emailTemplate, data);
        });
    }
}
