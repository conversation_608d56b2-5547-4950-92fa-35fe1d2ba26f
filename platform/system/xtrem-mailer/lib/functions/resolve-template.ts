import { Context } from '@sage/xtrem-core';
import * as xtremReporting from '@sage/xtrem-reporting';

export async function renderTemplate(context: Context, reportName: string, variables: any = {}): Promise<string> {
    const generatedReport = await xtremReporting.functions.generateReport(context, reportName, '', {
        variables,
        locale: context.currentLocale.replace('-', '_') as any,
        paperFormat: 'letter',
    });
    return generatedReport.populatedBodyContent;
}
