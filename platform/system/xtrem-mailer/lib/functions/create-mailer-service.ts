import { Context, Logger, Node, SystemError } from '@sage/xtrem-core';
import { MailService, NodeMailerService, SendGridMailService } from '../classes/_index';
import { MailerConfig } from '../interfaces/_index';

const logger = Logger.getLogger(__filename, 'mailer');

export function createMailerService(context: Context, node?: Node): Promise<MailService> {
    const config = context.configuration.getPackageConfig<MailerConfig>('@sage/xtrem-mailer');
    if (!config?.service) {
        throw new SystemError('No Mailer Service is Provided in the config file');
    }

    if (node) {
        config.node = node;
    }

    switch (config.service) {
        case 'SendGrid':
            return new SendGridMailService(context, config, logger).init();
        case 'NodeMailer':
            return new NodeMailerService(context, config, logger).init();
        default:
            throw new SystemError(`${config.service} is not a mailer service`);
    }
}
