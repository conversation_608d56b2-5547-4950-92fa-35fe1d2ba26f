import { CustomSqlAction } from '@sage/xtrem-system';

export const emailReplyToEmailConfigUpdate = new CustomSqlAction({
    description: 'Update EmailReplyTo node with the new vital emailConfig reference',
    fixes: {
        tables: ['email_reply_to'],
        notNullableColumns: [
            { table: 'email_reply_to', column: 'email_config' },
            { table: 'email_reply_to', column: '_sort_value' },
        ],
    },
    body: async helper => {
        await helper.executeSql(`
        DO $$
        DECLARE
            sys_email_config_id BIGINT;
            max_sort_value BIGINT;
            sTenant RECORD;
            sRecord RECORD;
        BEGIN
            FOR sTenant IN
                SELECT tenant_id
                FROM ${helper.schemaName}.sys_tenant
            LOOP
                SELECT sec._id
                FROM ${helper.schemaName}.sys_email_config sec
                WHERE sec._tenant_id = sTenant.tenant_id
                LIMIT 1
                INTO sys_email_config_id;

                IF sys_email_config_id IS NULL THEN
                    DELETE FROM ${helper.schemaName}.email_reply_to ert
                    WHERE ert._tenant_id = sTenant.tenant_id;
                ELSE
                    SELECT MAX(_sort_value)
                    FROM ${helper.schemaName}.email_reply_to max
                    WHERE max._tenant_id = sTenant.tenant_id
                    INTO max_sort_value;

                    FOR sRecord IN
                        SELECT _tenant_id, _id
                        FROM ${helper.schemaName}.email_reply_to
                        WHERE _tenant_id = sTenant.tenant_id
                        AND email_config IS NULL
                    LOOP
                        max_sort_value := max_sort_value + 100;

                        RAISE WARNING 'Found SysEmailConfigID % MaxSortValue, % ', sys_email_config_id, max_sort_value;

                        UPDATE ${helper.schemaName}.email_reply_to ert
                        SET email_config = sys_email_config_id,
                        _sort_value = max_sort_value
                        WHERE ert._tenant_id = sRecord._tenant_id
                        AND ert._id = sRecord._id;
                    END LOOP;
                END IF;
            END LOOP;
        END $$;
        `);
    },
});
