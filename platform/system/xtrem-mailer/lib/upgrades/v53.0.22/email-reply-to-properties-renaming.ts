import { SchemaRenamePropertyAction } from '@sage/xtrem-system';
import { SenderEmailSetting } from '../../nodes/sender-email-setting';

// Note:
// The EmailReplyTo node has been renamed to SenderEmailSetting after the upgrade to v53.0.22

export const renamePackageWithMetaPackageAction = new SchemaRenamePropertyAction({
    node: () => SenderEmailSetting,
    oldPropertyName: 'package',
    newPropertyName: 'metaPackage',
});

export const renameEmailsWithReplyToPrefixesAction = new SchemaRenamePropertyAction({
    node: () => SenderEmailSetting,
    oldPropertyName: 'emails',
    newPropertyName: 'replyToPrefixes',
});
