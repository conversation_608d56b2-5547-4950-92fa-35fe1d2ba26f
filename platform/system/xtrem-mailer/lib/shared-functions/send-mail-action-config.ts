import { LocalizedText } from '@sage/xtrem-shared';
import type { WorkflowGraphqlActionConfig, WorkflowParameter } from '@sage/xtrem-workflow';

export type AddressType = 'to' | 'cc' | 'bcc';

export interface AddressConfig {
    addressType: AddressType;
    isVariable: boolean;
    variable: string;
    value: string;
}

export interface AttachmentConfig {
    /**
     * the path of the variable
     */
    variable: string;

    /**
     * The tags to filter the attachments to transfer.
     */
    tags: string[];
    /**
     * Should the action fail if the attachment is not found?
     */
    failIfNotFound: boolean;
}

export interface SendEmailActionConfig extends WorkflowGraphqlActionConfig {
    localizedSubject: LocalizedText;
    addressList: AddressConfig[];
    attachmentList: AttachmentConfig[];
    report: string;
    reportParameters: WorkflowParameter[];
    from?: string;
    replyTo?: string;
}
