import { EmailSystem } from '@sage/xtrem-mailer-api';

export interface Dns {
    _id: string;
    valid: boolean;
    type: string;
    host: string;
    data: string;
    reason: string;
}

export type DnsRecord = {
    valid: boolean;
    type: string;
    host: string;
    data: string;
    reason?: string | null;
};

export type DomainAuthentication = {
    id: number;
    user_id: number;
    subdomain: string;
    domain: string;
    username: string;
    ips: string[];
    custom_spf: boolean;
    default: boolean;
    legacy: boolean;
    automatic_security: boolean;
    valid: boolean;
    dns: {
        mail_cname: DnsRecord;
        dkim1: DnsRecord;
        dkim2: DnsRecord;
    };
};

export type EmailSystemConfiguration = {
    sendGrid?: DomainAuthentication;
    hash?: string;
};

export type SanitizedDomainAuthentication = Omit<DomainAuthentication, 'username' | 'user_id'>;

export type SanitizedEmailSystemConfiguration = {
    sendGrid?: SanitizedDomainAuthentication;
};

export type SanitizedSysEmailConfig = {
    id: string;
    emailSystem: EmailSystem;
    domain: string;
    sanitizedConfiguration: SanitizedEmailSystemConfiguration;
};
