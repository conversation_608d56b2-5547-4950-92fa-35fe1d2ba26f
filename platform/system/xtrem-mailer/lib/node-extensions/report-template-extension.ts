import { asyncArray, BusinessRuleError, Context, decorators, NodeExtension } from '@sage/xtrem-core';
import * as xtremReporting from '@sage/xtrem-reporting';
import * as xtremMailer from '../index';

@decorators.nodeExtension<ReportTemplateExtension>({
    extends: () => xtremReporting.nodes.ReportTemplate,
})
export class ReportTemplateExtension extends NodeExtension<xtremReporting.nodes.ReportTemplate> {
    @decorators.mutation<typeof ReportTemplateExtension, 'sendTestEmail'>({
        isPublished: true,
        parameters: [
            { name: 'reportTemplateName', type: 'string', isMandatory: true },
            { name: 'subject', type: 'string', isMandatory: true },
            { name: 'emailAddress', type: 'string', isMandatory: true },
            { name: 'testData', type: 'string', isMandatory: true },
        ],
        return: {
            type: 'object',
            properties: {
                isSent: {
                    type: 'boolean',
                },
            },
        },
    })
    static async sendTestEmail(
        context: Context,
        reportTemplateName: string,
        subject: string,
        emailAddress: string,
        testData: string,
    ): Promise<{ isSent: boolean }> {
        const config =
            context.configuration.getPackageConfig<xtremMailer.interfaces.MailerConfig>('@sage/xtrem-mailer');

        if (!config)
            throw new BusinessRuleError(
                context.localize('@sage/xtrem-mailer/no-mailer-config', 'The mailer configuration is missing.', {
                    reportTemplateName,
                }),
            );

        const mailer = await xtremMailer.createMailerService(context);

        const template = await context.tryRead(xtremReporting.nodes.ReportTemplate, {
            name: reportTemplateName,
        });

        if (!template)
            throw new BusinessRuleError(
                context.localize('@sage/xtrem-mailer/no-template', 'The {{key}} template could not be found.', {
                    reportTemplateName,
                }),
            );

        const translatedStrings = await asyncArray(await template.translatableTexts.toArray())
            .map<xtremReporting.functions.TranslatableText>(
                async (segment): Promise<xtremReporting.functions.TranslatableText> => {
                    return {
                        hash: await segment.hash,
                        locale: await segment.locale,
                        text: await segment.text,
                    };
                },
            )
            .toArray();

        const translations = xtremReporting.functions.createDictionaryForRendering(
            context.currentLocale.replace('-', '_') as xtremReporting.enums.ReportLocale,
            await template.baseLocale,
            translatedStrings || [],
        );

        const recipients: xtremMailer.interfaces.Recipients = {
            to: [{ address: emailAddress, name: emailAddress }],
        };

        await mailer.sendEmailForPreview(recipients, subject, reportTemplateName, JSON.parse(testData), translations);

        return { isSent: true };
    }
}

declare module '@sage/xtrem-reporting/lib/nodes/report-template' {
    export interface ReportTemplate extends ReportTemplateExtension {}
}
