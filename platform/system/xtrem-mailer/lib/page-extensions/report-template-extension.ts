import { ReportTemplate } from '@sage/xtrem-reporting/build/lib/pages/report-template';
import { GraphApi } from '@sage/xtrem-mailer-api';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<ReportTemplateExtension>({
    extends: '@sage/xtrem-reporting/ReportTemplate',
    businessActions() {
        return [this.emailExample];
    },
})
export class ReportTemplateExtension extends ui.PageExtension<ReportTemplate, GraphApi> {
    @ui.decorators.pageAction<ReportTemplateExtension>({
        title: 'Send test email',
        onClick() {
            return this.$.dialog.page(
                '@sage/xtrem-mailer/TestEmailDialog',
                {
                    name: this.name.value,
                    reportId: this.report.value._id,
                },
                {
                    resolveOnCancel: true,
                },
            );
        },
        isHidden() {
            return this.reportType.value !== 'email' || this.$.isDirty || this.$.isNewPage;
        },
    })
    emailExample: ui.PageAction;
}

declare module '@sage/xtrem-reporting/build/lib/pages/report-template' {
    export interface ReportTemplate extends ReportTemplateExtension {}
}
