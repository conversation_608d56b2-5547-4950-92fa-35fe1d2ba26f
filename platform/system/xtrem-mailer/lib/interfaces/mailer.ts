import { Node } from '@sage/xtrem-core';
import { Attachment as Node<PERSON>ailerAttachment } from 'nodemailer/lib/mailer';
import { Options as NodeMailerOptions } from 'nodemailer/lib/smtp-transport';
import { DomainAuthentication } from '../shared-functions/interfaces/email-config';

export type MailerService = 'SendGrid' | 'NodeMailer';

export type Attachment = NodeMailerAttachment;
export type Attachments = Attachment[];

export interface ValidateConfig {
    isValid: boolean;
    missingKey?: string[];
}

export interface MessageBody {
    text: string;
    html: string;
}

export interface MailerConfig extends NodeMailerOptions {
    service: MailerService;
    auth: {
        user?: string;
        pass?: string;
        apiKey?: string;
        sysApiKey?: string;
    };
    from: Address;
    replyTo: Address;
    // for sendGrid
    mailSettings?: {
        checkSpam: {
            enable?: boolean;
            threshold?: number;
            post_to_url?: string;
        };
        automaticBcc?: {
            enable: boolean;
            email?: string;
        };
        footer?: {
            enable?: boolean;
            text?: string;
            html?: string;
        };
        sandBoxMode?: boolean;
    };
    redirectUrl: string;
    node?: Node;
    packageName?: string;
}

export interface Address {
    name: string;
    address: string;
}

export interface Recipients {
    to: Address[];
    cc?: Address[];
    bcc?: Address[];
    replyTo?: Address[];
}
export type EmailSystemConfiguration = {
    sendGrid?: DomainAuthentication;
    hash?: string;
};
