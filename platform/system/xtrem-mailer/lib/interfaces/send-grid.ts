import { Attachments } from './mailer';

// https://sendgrid.com/docs/api-reference/
export interface SendGridInterface {
    personalizations: {
        to: SendGridAddress[];
        cc?: SendGridAddress[];
        bcc?: SendGridAddress[];
        subject?: string;
        header?: object;
        substitutions?: object;
        custom_args?: object;
        send_at?: number;
    }[];
    from: SendGridAddress;
    reply_to_list?: SendGridAddress[];
    content: SendGridMessageContent[];
    attachments?: Attachments;
    sections?: object;
    categories?: string[];
    custom_args?: string;
    send_at?: number;
    batch_id?: string;
    sandbox_mode?: {
        enable?: boolean;
    };
    mail_settings?: {
        bcc?: {
            enable?: boolean;
            email?: string;
        };
        footer?: {
            enable?: boolean;
            text?: string;
            html?: string;
        };
        sandbox_mode?: {
            enable?: boolean;
        };
        check_spam?: {
            enable?: boolean;
            threshold?: number;
            post_to_url?: string;
        };
    };
}

export interface SendGridAddress {
    name?: string;
    email: string;
}

export interface SendGridMessageContent {
    type: string;
    value: string;
}

export interface ValidationResponse {
    id: number;
    valid: boolean;
    validation_results: Record<string, { valid: boolean; reason?: string }>;
}
