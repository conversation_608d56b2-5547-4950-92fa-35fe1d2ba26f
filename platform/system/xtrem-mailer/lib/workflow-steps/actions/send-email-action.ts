import { Context, Logger, Valida<PERSON>Context, WorkflowStepDescriptor, asyncArray } from '@sage/xtrem-core';
import { ValidationSeverity, getTextForLocale } from '@sage/xtrem-shared';
import * as xtremUpload from '@sage/xtrem-upload';
import { GraphqlAction, Request } from '@sage/xtrem-workflow';
import * as _ from 'lodash';
import { createMailerService } from '../../functions/create-mailer-service';
import { AddressConfig, AddressType, AttachmentConfig, SendEmailActionConfig } from '../../shared-functions';

const logger = Logger.getLogger(__filename, 'send-mail-action');
export class SendEmailAction extends GraphqlAction<SendEmailActionConfig> {
    private static _getNameFromEmail(email: string): string {
        return _.startCase(email.split('@')[0]);
    }

    private convertAddress(address: AddressConfig): string {
        if (!address.addressType) throw this.logicError(`invalid address: missing address type`);
        const email: string = address.isVariable ? this.getVariableValue(address.variable) : address.value;
        return `{ name: "${SendEmailAction._getNameFromEmail(email)}", address: "${email}" }`;
    }

    private convertAddresses(addressList: AddressConfig[], addressType: AddressType): string[] {
        if (!addressList?.length) return [];
        return addressList
            .filter(address => address.addressType === addressType)
            .map(address => this.convertAddress(address));
    }

    override async buildRequest(): Promise<Request> {
        const { addressList, reportParameters, report, localizedSubject, attachmentList, from, replyTo } =
            this.resolveConfig();

        const recipients = {
            to: this.convertAddresses(addressList, 'to'),
            cc: this.convertAddresses(addressList, 'cc'),
            bcc: this.convertAddresses(addressList, 'bcc'),
        };

        const reportVariables = _.fromPairs(
            reportParameters.map(param => [
                param.name,
                param.isVariable ? this.getVariableValue(param.value as string) : param.value,
            ]),
        );

        const locale = this.processWrapper.state.locale;

        const attachmentsIds: number[] = await this._getAttachmentIds(attachmentList || []);

        const fromArg = from ? `from: { name: "${SendEmailAction._getNameFromEmail(from)}", address: "${from}" },` : '';
        const replyToArg = replyTo
            ? `replyTo: { name: "${SendEmailAction._getNameFromEmail(replyTo)}", address: "${replyTo}" },`
            : '';

        const subject = getTextForLocale(localizedSubject, locale);
        const reportVariablesString = JSON.stringify(JSON.stringify(reportVariables));
        const attachmentsString = JSON.stringify(attachmentsIds);
        logger.info(
            `Sending mail to ${recipients.to[0]}, subject: ${subject}, attachments: ${attachmentsString}, report: ${report}, reportVariables: ${reportVariablesString}`,
        );

        return {
            operationType: 'asyncMutation',
            packageName: 'xtremMailer',
            nodeName: 'mailer',
            operationName: 'sendMail',
            args: `(
                subject: "${subject}",
                ${fromArg}
                ${replyToArg}
                recipients: {
                    to: [${recipients.to}],
                    cc: [${recipients.cc}],
                    bcc: [${recipients.bcc}]
                },
                reportName: "${report}",
                reportVariables: ${reportVariablesString},
                attachments: ${attachmentsString}
            )`,
            selector: '',
            variables: [],
            outputPath: 'mailSent',
        };
    }

    /**
     * Register (in the context) an error when the attachment is not found.
     * @param context
     * @param attachment
     */
    private static _registerNoAttachmentFoundError(context: Context, attachment: AttachmentConfig): void {
        context.diagnoses.push({
            message: context.localize(
                '@sage/xtrem-mailer/nodes__attachement__source-attachment-not-found',
                'Attachment not found {{source}}.',
                { source: attachment.variable },
            ),
            path: [],
            severity: ValidationSeverity.error,
        });
    }

    /**
     * Returns the ids of the attachments to send.
     * @param attachmentList the list of attachment configurations
     */
    private async _getAttachmentIds(attachmentList: AttachmentConfig[]): Promise<number[]> {
        const attachmentIds: number[] = [];
        if (attachmentList.length === 0) return [];
        await this.withReadonlyContext(async context => {
            await asyncArray(attachmentList).forEach(async attachment => {
                const fromNodeName = this.getVariableDefinition(attachment.variable).node || '';
                const fromId = this.getVariableValue(attachment.variable);

                if (fromNodeName === 'UploadedFile') {
                    await SendEmailAction._fillListFromUploadedFile(attachmentIds, context, fromId, attachment);
                    return;
                }
                // the variable describes a node with attachments
                await SendEmailAction._fillListFromNodeAssociations(
                    attachmentIds,
                    context,
                    fromNodeName,
                    fromId,
                    attachment,
                );
            });
        });
        return attachmentIds;
    }

    /**
     * Fill the list of attachmentIds from an uploaded file.
     * @param attachmentIds the list of attachment ids to fill
     * @param context the context
     * @param id id of the uploaded file
     * @param attachment the attachment configuration
     */
    private static async _fillListFromUploadedFile(
        attachmentIds: number[],
        context: Context,
        id: number,
        attachment: AttachmentConfig,
    ): Promise<void> {
        const uploadedFile = await context.tryRead(xtremUpload.nodes.UploadedFile, { _id: id });
        if (uploadedFile == null) {
            if (attachment.failIfNotFound) {
                SendEmailAction._registerNoAttachmentFoundError(context, attachment);
            }
            return;
        }
        attachmentIds.push(uploadedFile._id);
    }

    /**
     * Fill the list of attachmentIds from the associations of a node.
     * @param attachmentIds the list of attachment ids to fill
     * @param context the context
     * @param fromNodeName the name of the node
     * @param fromId the id of the node
     * @param attachment the attachment configuration
     */
    private static async _fillListFromNodeAssociations(
        attachmentIds: number[],
        context: Context,
        fromNodeName: string,
        fromId: number,
        attachment: AttachmentConfig,
    ): Promise<void> {
        const allAssociations = await context
            .query(xtremUpload.nodes.AttachmentAssociation, {
                filter: {
                    sourceNodeName: fromNodeName,
                    sourceNodeId: fromId,
                },
            })
            .toArray();
        const attachementTags = attachment.tags || [];
        const fromAttachements =
            attachementTags.length === 0
                ? // Select all the associations when no filter tag is provided
                  allAssociations
                : (
                      await asyncArray(allAssociations)
                          .filter(async asso => {
                              const uploadedFile = await asso.attachment;
                              const tags = await uploadedFile._tags;
                              // Build the list of natural keys of the tags
                              const tagsNks = await asyncArray(tags)
                                  .map(async tag => `#${await tag.name}` as string)
                                  .toArray();
                              // only keep the uploadedFile where tagsNks contains ALL the fromTagsNaturalKeys
                              return attachment.tags.every(tagNk => tagsNks.includes(tagNk));
                          })
                          .toArray()
                  ).filter(asso => asso);
        if (fromAttachements.length === 0) {
            if (attachment.failIfNotFound) {
                SendEmailAction._registerNoAttachmentFoundError(context, attachment);
            }
            return;
        }

        await asyncArray(fromAttachements).forEach(async fromAssociation => {
            attachmentIds.push((await fromAssociation.attachment)._id);
        });
    }

    private async _checkMailInDomain(cx: ValidationContext, email: string): Promise<void> {
        const mailer = await createMailerService(cx.context);
        const error = await mailer.isEmailAdressValid(email);
        if (error) this.addControlError(cx, error);
    }

    override async control(cx: ValidationContext): Promise<void> {
        await super.control(cx);
        const { addressList, from, replyTo } = this.rawConfig;

        if (addressList == null || addressList.length === 0)
            this.addControlError(
                cx,
                cx.localize('@sage/xtrem-mailer/workflow_error_no_recipient', 'You need to add a recipient.'),
            );
        if (!addressList.some(address => address.addressType === 'to'))
            this.addControlError(
                cx,
                cx.localize(
                    '@sage/xtrem-mailer/workflow_error_no_to_recipient',
                    'The address list needs to contain at least one "to" recipient.',
                ),
            );
        if (from) await this._checkMailInDomain(cx, from);
        if (replyTo) await this._checkMailInDomain(cx, replyTo);
    }

    static override readonly descriptor = {
        type: 'action',
        key: 'send-email',
        title: '',
        description: '',
        ui: {
            icon: 'mail',
            color: '#335b70ff',
            configurationPage: '@sage/xtrem-mailer/WorkflowActionSendEmail',
        },
    } as WorkflowStepDescriptor<SendEmailActionConfig>;
}
