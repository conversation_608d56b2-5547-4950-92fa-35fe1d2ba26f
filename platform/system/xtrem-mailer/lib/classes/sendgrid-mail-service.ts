/* eslint-disable class-methods-use-this */
import { <PERSON><PERSON>, <PERSON><PERSON>, retry, <PERSON>Error } from '@sage/xtrem-core';
import { AxiosRequestConfig, AxiosResponse } from 'axios';
import {
    Address,
    Attachments,
    MailerConfig,
    MessageBody,
    Recipients,
    SendGridInterface,
    ValidateConfig,
    ValidationResponse,
} from '../interfaces/_index';
import { DomainAuthentication } from '../shared-functions/interfaces/email-config';
import { MailService } from './mail-service';

export async function request(
    token: string,
    method: string,
    endPoint: string,
    data: object,
    logger: Logger,
): Promise<AxiosResponse> {
    const sendGridRequest: AxiosRequestConfig = {
        method,
        url: `https://api.sendgrid.com/v3/${endPoint}`,
        headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
        },
        data,
    };

    /**
     * Retry when error in request
     */
    const maxRetries = 3;
    const delayBeforeRetry = 500;
    // require axios using <PERSON><PERSON>.get, if this is executed from a unit test and axios is mocked
    // this will return the mocked axios module
    const axios = Mocker.get('axios', require);
    try {
        return await retry(() => axios(sendGridRequest), {
            maxTries: maxRetries || 3,
            delayBeforeRetry: delayBeforeRetry || 1000,
        });
    } catch (e) {
        const responseData = JSON.stringify(e.response?.data ?? null, null, 4);
        logger.error(`send-mail axios request failed: ${e.message}, response: ${responseData}`);
        throw new SystemError('E-mail could not be sent.');
    }

    /**
     * Check if it is needed to create mock files :
     * As to be inserted in the platform side !
     */
}

export class SendGridMailService extends MailService {
    validateConfig(config: MailerConfig): ValidateConfig {
        if (!config.auth.apiKey) {
            return {
                isValid: false,
                missingKey: ['apiKey'],
            };
        }
        if (!config.from) {
            return {
                isValid: false,
                missingKey: ['from'],
            };
        }
        return {
            isValid: true,
        };
    }

    validateSysConfig(config: MailerConfig): void {
        if (!config.auth.sysApiKey) {
            this.logger.error(`Missing config key value : sysApiKey`);
        }
    }

    setDefaultConfig(): Partial<MailerConfig> {
        return {
            mailSettings: {
                automaticBcc: {
                    enable: false,
                },
                footer: {
                    enable: false,
                },
                sandBoxMode: false,
                checkSpam: {
                    enable: false,
                },
            },
        };
    }

    async sendMail(
        recipients: Recipients,
        subject: string,
        messageBody: MessageBody,
        attachments?: Attachments,
    ): Promise<void> {
        const mapRecipient = (r: Address) => ({ email: r.address, name: r.name || undefined });
        const mapReplyTo = this.mailerConfig.replyTo
            ? this.mailerConfig.replyTo.address.split(',').map(email => ({ email }))
            : [];
        const data: SendGridInterface = {
            personalizations: [
                {
                    to: recipients.to?.map(mapRecipient),
                    cc: recipients.cc?.length ? recipients.cc?.map(mapRecipient) : undefined,
                    bcc: recipients.bcc?.length ? recipients.bcc?.map(mapRecipient) : undefined,
                    subject,
                },
            ],
            from: { name: this.mailerConfig.from.name, email: this.mailerConfig.from.address },
            reply_to_list: mapReplyTo,
            content: [
                {
                    type: 'text/plain',
                    value: messageBody.text,
                },
                {
                    type: 'text/html',
                    value: messageBody.html,
                },
            ],
            attachments,
            mail_settings: {
                bcc: {
                    ...this.mailerConfig.mailSettings?.automaticBcc,
                },
                footer: {
                    ...this.mailerConfig.mailSettings?.footer,
                },
                sandbox_mode: {
                    enable: this.mailerConfig.mailSettings?.sandBoxMode,
                },
                check_spam: {
                    ...this.mailerConfig.mailSettings?.checkSpam,
                },
            },
        };
        await request(this.mailerConfig.auth.apiKey ?? '', 'POST', 'mail/send', data, this.logger);
    }

    // 1. Authenticate a domain
    async authenticateDomain(domain: string): Promise<DomainAuthentication> {
        this.validateSysConfig(this.mailerConfig);
        const data = {
            domain,
        };
        const response = await request(
            this.mailerConfig.auth?.sysApiKey ?? '',
            'POST',
            'whitelabel/domains',
            data,
            this.logger,
        );
        return response.data;
    }

    async getAuthenticatedDomain(domainId: number): Promise<DomainAuthentication> {
        this.validateSysConfig(this.mailerConfig);
        const response = await request(
            this.mailerConfig.auth.sysApiKey ?? '',
            'GET',
            `whitelabel/domains/${domainId}`,
            {},
            this.logger,
        );
        return response.data;
    }

    async validateDomainAuthentication(domainId: number): Promise<ValidationResponse> {
        this.validateSysConfig(this.mailerConfig);
        const response: AxiosResponse<ValidationResponse> = await request(
            this.mailerConfig.auth.sysApiKey ?? '',
            'POST',
            `whitelabel/domains/${domainId}/validate`,
            {},
            this.logger,
        );

        return response.data;
    }

    /**
     * Fetch all domains from SendGrid with pagination.
     *
     * @param apiKey - The SendGrid API key.
     * @param limit - Number of records per page (default: 100).
     * @param domainFilter - Optional domain to filter.
     * @returns {Promise<DomainAuthentication[]>} - List of all authenticated domains.
     */
    async getAuthenticatedDomainByName(domainName: string): Promise<DomainAuthentication | null> {
        let authenticatedDomain: DomainAuthentication | null = null;
        const baseUrl = 'whitelabel/domains';
        const response = await request(
            this.mailerConfig.auth.sysApiKey ?? '',
            'GET',
            `${baseUrl}?domain=${domainName}`,
            {},
            this.logger,
        );
        const domains: DomainAuthentication[] = response.data;

        if (domains.length > 1) {
            this.logger.error('Multiple authenticated domains found. Only one domain should be authenticated.');
            throw new SystemError('Conflict: Multiple authenticated domains detected.');
        }

        if (domains.length > 0) {
            authenticatedDomain = domains[0];
        }

        return authenticatedDomain;
    }
}
