/* eslint-disable class-methods-use-this */
import { AnyValue, SystemError } from '@sage/xtrem-core';
import * as nodemailer from 'nodemailer';
import * as xtremMailer from '../index';
import { MailService } from './mail-service';

export class NodeMailerService extends MailService {
    validateConfig(config: xtremMailer.interfaces.MailerConfig): xtremMailer.interfaces.ValidateConfig {
        if (!config.host) {
            return {
                isValid: false,
                missingKey: ['host'],
            };
        }
        if (!config.from) {
            return {
                isValid: false,
                missingKey: ['from'],
            };
        }
        return {
            isValid: true,
        };
    }

    setDefaultConfig(): Partial<xtremMailer.interfaces.MailerConfig> {
        return {
            port: 25,
            secure: false,
            auth: undefined,
            debug: false,
            connectionTimeout: 10000, // 10 seconds
            requireTLS: false,
            name: 'X3',
            greetingTimeout: 5000, // 5 seconds
            authMethod: undefined,
            ignoreTLS: true, // TODO: enable TLS to avoid being classified as spam
            localAddress: undefined,
            socketTimeout: 5000, // 5 seconds
            opportunisticTLS: true,
            transactionLog: false,
        };
    }

    async sendMail(
        recipients: xtremMailer.interfaces.Recipients,
        subject: string,
        bodyMessage: xtremMailer.interfaces.MessageBody,
        attachments?: xtremMailer.interfaces.Attachments,
    ): Promise<void> {
        const transporter = nodemailer.createTransport<AnyValue>(this.mailerConfig);

        const { to, cc, bcc } = recipients;

        const message: nodemailer.SendMailOptions = {
            from: this.mailerConfig.from,
            to,
            cc,
            bcc,
            subject: 'Sage e-mail ✔',
            text: bodyMessage.text,
            html: bodyMessage.html,
            attachments,
        };
        try {
            await transporter.sendMail(message);
        } catch (err) {
            this.logger.error(err.stack);
            throw new SystemError('E-mail could not be sent.');
        }
    }
}
