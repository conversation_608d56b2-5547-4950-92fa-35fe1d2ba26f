import { Context, Logger, <PERSON><PERSON><PERSON><PERSON>y<PERSON>ilter, SystemError, TextStream, toHtmlText } from '@sage/xtrem-core';
import * as xtremReporting from '@sage/xtrem-reporting';
import * as fs from 'fs';
import * as handlebars from 'handlebars';
import * as _ from 'lodash';
import * as mimeTypes from 'mime-types';
import * as path from 'path';
import * as xtremMailer from '../index';
import { Attachments, MailerConfig, MessageBody, Recipients, ValidateConfig } from '../interfaces/_index';
import { createConfigurationHash } from '../nodes/sys-email-config';
import { DomainAuthentication } from '../shared-functions/interfaces/email-config';

export abstract class MailService {
    private _mailerConfig?: MailerConfig; // Backing property

    #customerDomain: string | undefined;

    constructor(
        private readonly context: Context,
        protected config: MailerConfig,
        protected logger: Logger,
    ) {}

    get mailerConfig(): MailerConfig {
        if (this._mailerConfig) {
            return this._mailerConfig;
        }
        throw new SystemError('Mailer config not initialized');
    }

    set mailerConfig(config: MailerConfig) {
        this._mailerConfig = config;
    }

    async init(): Promise<MailService> {
        this.mailerConfig = await this.getMailerConfig();
        return this;
    }

    async getMailerConfig(): Promise<MailerConfig> {
        const mailerConfig: MailerConfig = _.merge(this.setDefaultConfig(), this.config);

        const checkConfig = this.validateConfig(mailerConfig);

        if (!checkConfig.isValid) {
            checkConfig.missingKey!.forEach(key => {
                this.logger.error(`Missing config key value: ${key}`);
            });
            throw new SystemError('E-mail could not be sent.');
        }

        const customerDomain = await this.getCustomerDomain();
        if (customerDomain) {
            const { fromTo, replyTo } = await this.getReplyToAddresses();

            mailerConfig.from.name = '';
            mailerConfig.from.address =
                fromTo.length > 0 ? `${fromTo}@${customerDomain}` : `no-reply@${customerDomain}`;

            const replyToAddresses = replyTo.map(prefix => `${prefix}@${customerDomain}`).join(',');

            if (mailerConfig.replyTo) {
                mailerConfig.replyTo.address = replyToAddresses;
            } else {
                mailerConfig.replyTo = {
                    name: '',
                    address: replyToAddresses,
                };
            }
        }

        this._mailerConfig = mailerConfig; // Use backing property directly
        return mailerConfig;
    }

    abstract validateConfig(config: MailerConfig): ValidateConfig;

    abstract setDefaultConfig(): Partial<MailerConfig>;

    // eslint-disable-next-line class-methods-use-this
    protected renderSubject(subject: string, variables: any): string {
        const compiled = handlebars.compile(subject);
        return compiled(variables);
    }

    protected async renderMessageBody(reportName: string, variables: any): Promise<MessageBody> {
        const report = await this.context.read(xtremReporting.nodes.Report, { name: reportName });
        if (!report) {
            throw new Error('Email report not found.');
        }

        const template = await report.activeTemplate;
        if (!template) {
            throw new Error('Email template not found.');
        }

        const html = await xtremMailer.functions.renderTemplate(this.context, await report.name, variables);

        // Extract text from HTML content using jsdom
        const text = toHtmlText(html, { shrinkEmptyLines: true });
        return { html, text };
    }

    protected async renderPreviewMessageBody(
        reportTemplateName: string,
        variables: any,
        translations: any,
    ): Promise<MessageBody> {
        const template = await this.context.read(xtremReporting.nodes.ReportTemplate, { name: reportTemplateName });

        if (!template) {
            throw new Error('Email template not found.');
        }

        const { populatedBodyContent } = await xtremReporting.functions.generateReportFromTemplateContents({
            variables,
            context: this.context,
            reportName: reportTemplateName,
            bodyHtmlTemplate: (await template.htmlTemplate)?.value,
            headerHtmlTemplate: (await template.headerHtmlTemplate)?.value,
            footerHtmlTemplate: (await template.footerHtmlTemplate)?.value,
            query: (await template.query) || TextStream.fromString(''),
            code: (await template.code) || TextStream.fromString(''),
            styleSheet: (await template.styleSheet) || TextStream.fromString(''),
            locale: this.context.currentLocale.replace('-', '_') as any,
            paperFormat: 'a4',
            translations,
            baseLocale: await template.baseLocale,
            attachmentTemplate: (await template.attachmentTemplate) || TextStream.fromString(''),
        });

        return { html: populatedBodyContent, text: populatedBodyContent.replace(/<[^>]*>?/gm, '') };
    }

    // Added paramemter with paths to accomodate removal of path for unit tests
    // eslint-disable-next-line class-methods-use-this
    resolveAttachments(filesPath?: string[], withPaths = true): Attachments {
        return (filesPath || []).map(filePath => {
            const fileName = path.extname(filePath);
            return {
                filename: fileName,
                path: withPaths ? filePath : undefined,
                contentType: mimeTypes.contentType(fileName) || '',
                content: fs.readFileSync(filePath).toString('base64'),
            };
        });
    }

    addReplyToOnRecipients(recipients: Recipients): Recipients {
        return {
            ...recipients,
            replyTo: this.mailerConfig.replyTo ? [this.mailerConfig.replyTo] : undefined,
        };
    }

    abstract sendMail(
        recipients: Recipients,
        subject: string,
        messageBody: MessageBody,
        attachments?: Attachments,
    ): Promise<void>;

    async send(
        recipients: Recipients,
        subject: string,
        reportName: string,
        variables: any,
        files?: string[],
        withPaths = true,
    ): Promise<void> {
        const recipientsWithReplyTo = this.addReplyToOnRecipients(recipients);
        const formattedSubject = this.renderSubject(subject, variables);
        const messageBody = await this.renderMessageBody(reportName, variables);
        const messageAttachments = files ? this.resolveAttachments(files, withPaths) : undefined;
        return this.sendMail(recipientsWithReplyTo, formattedSubject, messageBody, messageAttachments);
    }

    async sendWithPDFs(
        recipients: Recipients,
        subject: string,
        reportName: string,
        variables: any,
        pdfs?: Attachments,
    ): Promise<void> {
        const recipientsWithReplyTo = this.addReplyToOnRecipients(recipients);
        const formattedSubject = this.renderSubject(subject, variables);
        const messageBody = await this.renderMessageBody(reportName, variables);
        return this.sendMail(recipientsWithReplyTo, formattedSubject, messageBody, pdfs);
    }

    async sendEmailForPreview(
        recipients: Recipients,
        subject: string,
        reportTemplateName: string,
        variables: any,
        translations: any,
    ): Promise<void> {
        const recipientsWithReplyTo = this.addReplyToOnRecipients(recipients);
        const formattedSubject = this.renderSubject(subject, variables);
        const messageBody = await this.renderPreviewMessageBody(reportTemplateName, variables, translations);
        return this.sendMail(recipientsWithReplyTo, formattedSubject, messageBody);
    }

    async getCustomerDomain(): Promise<string | undefined> {
        if (this.#customerDomain) return this.#customerDomain;
        const tenantEmailConfig = await this.context.query(xtremMailer.nodes.SysEmailConfig).at(0);
        if (!tenantEmailConfig) throw new SystemError('Email configuration not found');

        if (
            (await tenantEmailConfig.emailSystem) !== 'sage' &&
            (await tenantEmailConfig.configuration)?.sendGrid?.valid
        ) {
            const hash = (await tenantEmailConfig.configuration)?.hash;
            const checkHash = await createConfigurationHash(
                this.context,
                await tenantEmailConfig.domain,
                (await tenantEmailConfig.configuration)?.sendGrid as DomainAuthentication,
            );

            if ((await tenantEmailConfig.domain) !== (await tenantEmailConfig.configuration)?.sendGrid?.domain)
                throw new SystemError('Domain does not match the configuration');

            if (hash !== checkHash) throw new SystemError('Configuration hash does not match');

            this.#customerDomain = await tenantEmailConfig.domain;
        } else {
            this.#customerDomain = '';
        }
        return this.#customerDomain;
    }

    /**
     * Returns whether the email address is valid or not. If a customerDomain is set, it will also ensure that the email address is from the customer domain.
     * @param email The email address to validate
     *
     * @returns A string with the error message if the email address is not valid, null otherwise
     */
    async isEmailAdressValid(email: string): Promise<string | null> {
        const customerDomain = await this.getCustomerDomain();
        if (customerDomain) {
            if (!email.endsWith(`@${customerDomain}`))
                return `${email} does not match the customer domain ${customerDomain}`;
        }
        return null;
    }

    async getReplyToAddresses(): Promise<{ fromTo: string; replyTo: string[] }> {
        const filter: NodeQueryFilter<xtremMailer.nodes.SenderEmailSetting> = {};

        const factoryName = this.config.node?.$.factory.name;
        const packageName = this.config.node?.$.factory.package.name ?? this.config.packageName;
        const company = this.config.node?.$.factory.getTaggedProperty('company')?.name;
        const site = this.config.node?.$.factory.getTaggedProperty('site')?.name;

        if (factoryName) _.merge(filter, { node: { _or: [{ name: `${factoryName}` }, { _eq: null }] } });
        if (packageName) _.merge(filter, { metaPackage: { _or: [{ name: `${packageName}` }, { _eq: null }] } });
        if (company) _.merge(filter, { company: { _or: [{ name: `${company}` }, { _eq: null }] } });
        if (site) _.merge(filter, { site: { _or: [{ name: `${site}` }, { _eq: null }] } });

        const senderEmailSettings = this.context.query(xtremMailer.nodes.SenderEmailSetting, {
            filter,
            orderBy: { site: -1, company: -1, node: -1, metaPackage: -1 },
        });

        if (senderEmailSettings) {
            if (factoryName || packageName || company || site) {
                return {
                    fromTo: (await (await senderEmailSettings.at(0))?.fromPrefix) ?? '',
                    replyTo: (await (await senderEmailSettings.at(0))?.replyToPrefixes) ?? [],
                };
            }
            // In the case all criteria were empty
            // we return the last element of the array
            return {
                fromTo: (await (await senderEmailSettings.at(-1))?.fromPrefix) ?? '',
                replyTo: (await (await senderEmailSettings.at(-1))?.replyToPrefixes) ?? [],
            };
        }
        return { fromTo: '', replyTo: [] };
    }
}
