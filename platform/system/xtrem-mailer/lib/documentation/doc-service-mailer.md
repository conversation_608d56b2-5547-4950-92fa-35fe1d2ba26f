PATH: XTREEM/Development+documentation/xtrem-mailer

## Configuration

### SMTP Server

Add smtp server config to xtrem-config.yml

```yml
packages:
    '@sage/xtrem-mailer':
        service: NodeMailer
		host: 'smtp.host'
        port: smtpPortNumber
        from:
            address: email.address
            name: sender.name
		auth: {
			user: user
			pass: password
		},
```

### SendGrid

```yml
packages:
    '@sage/xtrem-mailer':
        service: 'SendGrid'
        auth:
            apiKey: sendgrid-api-key
        from:
            address: email.address
            name: sender.name
        mailSetting: # optional
            sandBoxMode: boolean
            footer:
                enable: boolean
                text: footer in text format
                html: footer in html format
            checkSpam:
                enable: true
                threshold: 1..10 # number from 1 to 10. 10 is the most restrictive
        },
```

## Send email

```typescript
import { TextStream } from '@sage/xtrem-core';
import * as xtremMailer from '@sage/xtrem-mailer';

// create the mailer
const mailer = xtremMailer.createMailerService(context);

// create a mail template
const html = '<h1>Email sent from {{company}}</h1>';
const template = context.create(xtremMailer.nodes.MailTemplate, {
    key: 'test_email',
    template: TextStream.fromString(html),
});

// create a dynamic template data
const message: DynamicTemplateData = {
    data,
    template,
};

// email recipients
const recipients: xtremMailer.interfaces.Recipients = {
    to: [{ address: '<EMAIL>', name: 'Test Receiver' }],
};

// email subject
const subject = 'Mail subject';

// email mail body content
const data = { company: 'Company' };

// attachments
export const attachmentFiles = [path.resolve('dummy-txt.txt'), path.resolve('dummy-pdf.pdf')];

// send the email
mailer.send(recipients, subject, message, attachmentFiles);
```
