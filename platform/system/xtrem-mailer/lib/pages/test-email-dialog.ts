import * as ui from '@sage/xtrem-ui';
import type { MonacoPluginProperties } from '@sage/xtrem-ui-plugin-monaco';

@ui.decorators.page<TestEmailDialog>({
    title: 'Send a test email',
    businessActions() {
        return [this.sendButton];
    },
    async onLoad() {
        const response = await this.$.graph.raw(
            `{
                xtremReporting {
                    report {
                        read(_id: "${this.$.queryParameters.reportId}"){
                            _id
                            variables {
                                query {
                                    edges {
                                        node {
                                            title
                                            name
                                            isMandatory
                                            type
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }`,
        );
        const cleanResponse = response?.xtremReporting?.report?.read?.variables?.query?.edges;
        const reportVariables = cleanResponse
            ? cleanResponse.map((reportVariable: any) => {
                  return reportVariable.node;
              })
            : [];
        const json = reportVariables.reduce((acc: any, reportVariable: any) => {
            const { name } = reportVariable;
            Object.defineProperty(acc, name, {
                value: '',
                enumerable: true,
            });
            return acc;
        }, {});
        this.testData.value = JSON.stringify(json, null, 2);
    },
})
export class TestEmailDialog extends ui.Page {
    @ui.decorators.section<TestEmailDialog>({
        isTitleHidden: true,
    })
    testSection: ui.containers.Section;

    @ui.decorators.block<TestEmailDialog>({
        parent() {
            return this.testSection;
        },
    })
    testBlock: ui.containers.Block;

    @ui.decorators.textField<TestEmailDialog>({
        parent() {
            return this.testBlock;
        },
        title: 'Address',
        helperText: 'A test email will be sent to this address.',
        isTransient: true,
        isFullWidth: true,
        // validation: /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
    })
    emailAddress: ui.fields.Text;

    @ui.decorators.pluginField<TestEmailDialog, MonacoPluginProperties>({
        parent() {
            return this.testBlock;
        },
        title: 'Test data',
        helperText: 'Data in JSON format that will be displayed on the test template above.',
        isTransient: true,
        isFullWidth: true,
        pluginPackage: '@sage/xtrem-ui-plugin-monaco',
        language: 'json',
        handlebarsSuggestions: null,
    })
    testData: ui.fields.Plugin<MonacoPluginProperties>;

    @ui.decorators.pageAction<TestEmailDialog>({
        title: 'Send',
        onError(err) {
            this.$.loader.isHidden = true;
            this.$.showToast(`Could not send your test email: ${err}`, { type: 'error' });
        },
        async onClick() {
            const emailAddressValidation = await this.emailAddress.validate();
            if (emailAddressValidation) {
                await this.$.dialog.message('error', 'Email address', emailAddressValidation);
                return;
            }

            const testDataValidation = await this.testData.validate();
            if (testDataValidation) {
                await this.$.dialog.message('error', 'Test data', testDataValidation);
                return;
            }

            const data: {
                reportTemplateName: string;
                subject: string;
                emailAddress: string;
                testData: string;
                attachmentsPaths?: string[];
            } = {
                reportTemplateName: String(this.$.queryParameters.name),
                subject: String(this.$.queryParameters.name),
                emailAddress: this.emailAddress.value,
                testData: this.testData.value,
            };

            this.$.loader.isHidden = false;
            const response = await this.$.graph
                .node('@sage/xtrem-reporting/ReportTemplate')
                .mutations.sendTestEmail(
                    {
                        isSent: true,
                    },
                    data,
                )
                .execute();
            this.$.loader.isHidden = true;
            if (response.isSent) {
                this.$.showToast('Email sent', { type: 'success' });
            } else {
                this.$.showToast('Could not send your test email', { type: 'error' });
            }
        },
    })
    sendButton: ui.PageAction;
}
