import {
    EmailSystem,
    GraphApi,
    SenderEmailSetting,
    SysEmailConfig as SysEmailConfigNode,
} from '@sage/xtrem-mailer-api';
import type { MetaNodeFactory, MetaPackage } from '@sage/xtrem-metadata-api';
import type { Company, Site } from '@sage/xtrem-system-api';
import { setApplicativePageCrudActions } from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import { applicationData } from '@sage/xtrem-system/build/lib/menu-items/application-data';
import * as ui from '@sage/xtrem-ui';
import { Dns, SanitizedEmailSystemConfiguration } from '../shared-functions/interfaces/email-config';

@ui.decorators.page<SysEmailConfig, SysEmailConfigNode>({
    title: 'Email Domain Sender',
    module: 'xtrem-mailer',
    node: '@sage/xtrem-mailer/SysEmailConfig',
    navigationPanel: undefined,
    mode: 'tabs',
    businessActions() {
        return [
            this.validateCustomDomain,
            this.authenticateCustomDomain,
            this.setAsDefault,
            this.$standardCancelAction,
            this.$standardSaveAction,
            this.resetEmailConfig,
        ];
    },
    async defaultEntry() {
        return (
            (
                await this.$.graph
                    .node('@sage/xtrem-mailer/SysEmailConfig')
                    .query(ui.queryUtils.edgesSelector({ _id: true }, {}))
                    .execute()
            ).edges?.at(0)?.node._id ?? null
        );
    },
    async onLoad() {
        this.configuration.value = this.sanitizedConfiguration.value;
        this.setAsDefault.isDisabled = true;
        if (this.emailSystem.value !== 'sage') this.loadSendGridConfig(JSON.parse(this.configuration.value ?? '{}'));
        this.$.setPageClean();
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
        });
    },
    onDirtyStateUpdated(isDirty: boolean) {
        if (this.setAsDefault.isHidden) {
            setApplicativePageCrudActions({
                page: this,
                isDirty,
                save: this.$standardSaveAction,
                cancel: this.$standardCancelAction,
            });
        }
    },

    menuItem: applicationData,
})
export class SysEmailConfig extends ui.Page<GraphApi, SysEmailConfigNode> {
    instance: any;

    @ui.decorators.section<SysEmailConfig>({
        title: 'Information',
        isTitleHidden: true,
        onActive() {
            this.validateCustomDomain.isHidden = this.hideShowValidateCustomDomainAction();
            this.authenticateCustomDomain.isHidden = this.hideShowAuthenticateCustomDomainAction();
            this.setAsDefault.isHidden = false;
        },
        onInactive() {
            this.validateCustomDomain.isHidden = true;
            this.authenticateCustomDomain.isHidden = true;
            this.setAsDefault.isHidden = true;
        },
    })
    informationSection: ui.containers.Section;

    @ui.decorators.block<SysEmailConfig>({
        parent() {
            return this.informationSection;
        },
    })
    sendGridBlock: ui.containers.Block;

    @ui.decorators.section<SysEmailConfig>({
        title: 'Email settings',
        isTitleHidden: true,
        isHidden() {
            return this.emailSystem.value !== 'custom' || this.dns.value.length === 0;
        },
        onActive() {
            setApplicativePageCrudActions({
                page: this,
                isDirty: this.$.isDirty,
                save: this.$standardSaveAction,
                cancel: this.$standardCancelAction,
            });
        },
        onInactive() {
            this.$standardSaveAction.isHidden = true;
            this.$standardCancelAction.isHidden = true;
        },
    })
    replyToSection: ui.containers.Section;

    @ui.decorators.block<SysEmailConfig>({
        parent() {
            return this.replyToSection;
        },
    })
    replyToBlock: ui.containers.Block;

    @ui.decorators.dropdownListField<SysEmailConfig>({
        parent() {
            return this.sendGridBlock;
        },
        title: 'Domain You Send From',
        options: ['sage', 'custom'],
        map(value) {
            return value === 'custom' && this.domain.value ? this.domain.value : 'sage.com';
        },
        onChange() {
            if (this.emailSystem.value === 'custom') {
                this.loadSendGridConfig(JSON.parse(this.configuration.value ?? '{}'));
            } else {
                this.setAsDefault.isDisabled = false;
            }
            this.authenticateCustomDomain.isHidden = this.hideShowAuthenticateCustomDomainAction();
            this.$.setPageClean();
        },
    })
    emailSystem: ui.fields.DropdownList<EmailSystem>;

    @ui.decorators.textField<SysEmailConfig>({
        isHidden: true,
        isTransient: true,
    })
    configuration: ui.fields.Text;

    @ui.decorators.textField<SysEmailConfig>({
        isHidden: true,
    })
    sanitizedConfiguration: ui.fields.Text;

    @ui.decorators.textField<SysEmailConfig>({
        isHidden: true,
    })
    domain: ui.fields.Text;

    @ui.decorators.textField<SysEmailConfig>({
        isHidden: true,
    })
    _id: ui.fields.Text;

    @ui.decorators.textField<SysEmailConfig>({
        isHidden: true,
    })
    id: ui.fields.Text;

    @ui.decorators.messageField<SysEmailConfig>({
        parent() {
            return this.sendGridBlock;
        },
        title: 'Domain You Send From',
        isTransient: true,
        isFullWidth: true,
        isDisabled: true,
        isTitleHidden: true,
        isHidden() {
            return this.emailSystem.value !== 'custom';
        },
    })
    instructionText: ui.fields.Message;

    @ui.decorators.tableField<SysEmailConfig, Dns>({
        title: 'Dns Configuration',
        bind: { sanitizedConfiguration: { sendgrid: { dns: true } } },
        emptyStateText: 'There are no dns for this domain.',
        canSelect: false,
        isHidden() {
            return this.emailSystem.value !== 'custom';
        },
        columns: [
            ui.nestedFields.checkbox({
                bind: 'valid',
                title: 'Valid',
            }),
            ui.nestedFields.text({
                bind: 'type',
                title: 'Type',
            }),
            ui.nestedFields.text({
                bind: 'host',
                title: 'Host',
            }),
            ui.nestedFields.text({
                bind: 'data',
                title: 'Value',
            }),
            ui.nestedFields.text({
                bind: 'reason',
                title: 'Validation failure reason',
            }),
        ],
        parent() {
            return this.sendGridBlock;
        },
    })
    dns: ui.fields.Table<Dns>;

    @ui.decorators.tableField<SysEmailConfig, SenderEmailSetting>({
        node: '@sage/xtrem-mailer/SenderEmailSetting',
        title: 'Email settings',
        isTitleHidden: true,
        canSelect: false,
        parent() {
            return this.replyToBlock;
        },
        columns: [
            ui.nestedFields.reference<SysEmailConfig, SenderEmailSetting, MetaNodeFactory>({
                title: 'Node',
                bind: 'node',
                node: '@sage/xtrem-metadata/MetaNodeFactory',
            }),
            ui.nestedFields.reference<SysEmailConfig, SenderEmailSetting, MetaPackage>({
                title: 'Package',
                bind: 'metaPackage',
                node: '@sage/xtrem-metadata/MetaPackage',
            }),
            ui.nestedFields.reference<SysEmailConfig, SenderEmailSetting, Company>({
                title: 'Company',
                bind: 'company',
                node: '@sage/xtrem-system/Company',
                valueField: 'id',
                minLookupCharacters: 1,
                columns: [ui.nestedFields.text({ bind: 'id' }), ui.nestedFields.checkbox({ bind: 'isActive' })],
            }),
            ui.nestedFields.reference<SysEmailConfig, SenderEmailSetting, Site>({
                title: 'Site',
                bind: 'site',
                node: '@sage/xtrem-system/Site',
                valueField: 'name',
                minLookupCharacters: 1,
                columns: [
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.checkbox({ bind: 'isActive' }),
                ],
            }),
            ui.nestedFields.text({
                title: 'From prefix',
                bind: 'fromPrefix',
            }),
            ui.nestedFields.text({
                title: 'Reply-to prefixes',
                bind: 'displayReplyToPrefixes',
                isReadOnly: true,
            }),
            ui.nestedFields.technical({ bind: 'replyToPrefixes' }),
        ],
        dropdownActions: [
            {
                icon: 'edit',
                title: 'Edit reply-to prefixes',
                async onClick(_rowId: string, rowData) {
                    const changedReplyToPrefixes = await this.$.dialog.page(
                        '@sage/xtrem-mailer/ReplyToPrefixesPanel',
                        {
                            emails: JSON.stringify(rowData.replyToPrefixes),
                        },
                        {
                            rightAligned: true,
                            size: 'medium',
                        },
                    );
                    if (changedReplyToPrefixes) {
                        rowData.replyToPrefixes = JSON.parse(changedReplyToPrefixes);
                        rowData.displayReplyToPrefixes = rowData.replyToPrefixes?.join('; ');
                        this.senderEmailSettings.addOrUpdateRecordValue(rowData);
                        await this.senderEmailSettings.redraw('displayReplyToPrefixes');
                    }
                },
            },
            {
                icon: 'bin',
                title: 'Delete',
                isDestructive: true,
                onClick(rowId: string) {
                    this.senderEmailSettings.removeRecord(rowId);
                },
            },
        ],
        fieldActions() {
            return this.$.recordId ? [this.addSenderEmailSetting] : [];
        },
    })
    senderEmailSettings: ui.fields.Table;

    @ui.decorators.pageAction<SysEmailConfig>({
        icon: 'add',
        title: 'Add',
        onClick() {
            this.senderEmailSettings.addRecord({});
        },
    })
    addSenderEmailSetting: ui.PageAction;

    @ui.decorators.pageAction<SysEmailConfig>({
        title: 'Authenticate your domain',
        isHidden() {
            return this.hideShowAuthenticateCustomDomainAction();
        },
        onError(err) {
            this.$.loader.isHidden = true;
            this.$.showToast(
                ui.localize(
                    '@sage/xtrem-mailer/pages_sys_email_config_could_not_authenticate',
                    'Your domain was not authenticated: {{errorMessage}}',
                    { domainValue: this.domain.value, errorMessage: err },
                ),
                { type: 'error' },
            );
        },
        async onClick() {
            const response = await this.$.graph
                .node('@sage/xtrem-mailer/SysEmailConfig')
                .mutations.authenticateSendGridCustomDomain(
                    {
                        sanitizedConfiguration: true,
                    },
                    {
                        id: '1',
                    },
                )
                .execute();
            this.$.loader.isHidden = true;
            if (response.sanitizedConfiguration) {
                this.$.showToast(`The domain "${this.domain.value}" has been successfully authenticated.`, {
                    type: 'success',
                });
                this.loadSendGridConfig(JSON.parse(response.sanitizedConfiguration ?? '{}'));
            } else {
                this.$.showToast(
                    `Failed to authenticate the domain "${this.domain.value}". Please check your configuration and try again.`,
                    { type: 'error' },
                );
            }
        },
    })
    authenticateCustomDomain: ui.PageAction;

    @ui.decorators.pageAction<SysEmailConfig>({
        title: 'Validate your domain',
        isHidden() {
            return this.hideShowValidateCustomDomainAction();
        },
        onError(err) {
            this.$.loader.isHidden = true;
            this.$.showToast(`Could not validate ${this.domain.value}: ${err}`, { type: 'error' });
        },
        async onClick() {
            const response = await this.$.graph
                .node('@sage/xtrem-mailer/SysEmailConfig')
                .mutations.validateSendGridDomainAuthentication(
                    {
                        sanitizedConfiguration: true,
                    },
                    {
                        id: '1',
                    },
                )
                .execute();
            this.$.loader.isHidden = true;
            if (response.sanitizedConfiguration) {
                this.configuration.value = response.sanitizedConfiguration;
                this.loadSendGridConfig(JSON.parse(response.sanitizedConfiguration ?? '{}'));
                if (!JSON.parse(response.sanitizedConfiguration)?.sendGrid?.valid) {
                    this.$.showToast(
                        `Validation failed for the domain "${this.domain.value}". Please review the errors in your Dns zone and correct them.`,
                        { type: 'error' },
                    );
                    this.setAsDefault.isDisabled = true;
                } else {
                    this.setAsDefault.isDisabled = false;
                    this.$.showToast(`The domain "${this.domain.value}" has been successfully validated.`, {
                        type: 'success',
                    });
                }
            } else {
                this.$.showToast(`Could not validate ${this.domain.value}`, { type: 'error' });
            }
        },
    })
    validateCustomDomain: ui.PageAction;

    @ui.decorators.pageAction<SysEmailConfig>({
        title: `Use this domain to send emails`,
        onError(err) {
            this.$.loader.isHidden = true;
            this.$.showToast(
                ui.localize(
                    '@sage/xtrem-mailer/pages_sys_email_config_could_not_set_email_as_default',
                    'Could not set {{emailSystemValue}} as default: {{errorMessage}}.',
                    { emailSystemValue: this.emailSystem.value, errorMessage: err },
                ),
                { type: 'error' },
            );
        },
        async onClick() {
            const response = await this.$.graph
                .node('@sage/xtrem-mailer/SysEmailConfig')
                .mutations.setAsDefault(
                    { sanitizedConfiguration: true, emailSystem: true },
                    {
                        emailSystem: this.emailSystem.value ?? 'sage',
                        id: '1',
                    },
                )
                .execute();
            this.$.loader.isHidden = true;
            if (this.emailSystem.value === 'sage' && response.emailSystem === 'sage') {
                this.setAsDefault.isDisabled = true;
                this.$.showToast(
                    `The domain "${this.emailSystem.value}" has been successfully set as the default for sending emails.`,
                    { type: 'success' },
                );
            } else if (this.emailSystem.value === 'custom' && response.sanitizedConfiguration) {
                this.configuration.value = response.sanitizedConfiguration;
                this.setAsDefault.isDisabled = true;
                this.loadSendGridConfig(JSON.parse(response.sanitizedConfiguration ?? '{}'));
                this.$.showToast(
                    `The domain "${this.emailSystem.value}" has been successfully set as the default for sending emails.`,
                    { type: 'success' },
                );
            } else {
                this.$.showToast(
                    `Failed to set the domain "${this.emailSystem.value}" as the default. Please try again.`,
                    { type: 'error' },
                );
            }
            this.$.setPageClean();
        },
    })
    setAsDefault: ui.PageAction;

    @ui.decorators.pageAction<SysEmailConfig>({
        title: `Remove custom domain`,
        isHidden() {
            return this.emailSystem.value === 'sage' || this.dns.value.length === 0;
        },
        async onClick() {
            const response = await this.$.graph
                .node('@sage/xtrem-mailer/SysEmailConfig')
                .mutations.removeCustomDomainConfiguration(true, {
                    sysEmailConfig: this._id.value ?? '',
                })
                .execute();
            this.$.loader.isHidden = true;
            if (response) {
                this.$.showToast(`The custom domain configuration was successfully removed.`, { type: 'success' });
            } else {
                this.$.showToast(`Failed to remove the custom domain configuration.`, { type: 'error' });
            }
            this.$.setPageClean();
            await this.$.router.refresh();
        },
    })
    resetEmailConfig: ui.PageAction;

    loadSendGridConfig(sanitizedConfiguration: SanitizedEmailSystemConfiguration): void {
        this.dns.value?.forEach(dns => this.dns.removeRecord(dns._id));
        Object.values((sanitizedConfiguration?.sendGrid?.dns as unknown as Dns) ?? {}).forEach((dnsValue, index) => {
            if (dnsValue) this.dns.addRecord({ _id: index, ...dnsValue });
        });

        if (this.dns.value.length === 0) {
            // No Dns records, domain not authenticated yet
            this.instructionText.value = ui.localize(
                '@sage/xtrem-mailer/pages_sys_email_config_authenticate_your_domain',
                'Authenticate your domain to enable sending emails from it.',
            );
            this.instructionText.variant = 'info';
        } else if (this.dns.value.some(dns => dns.reason)) {
            // Dns validation errors detected
            this.instructionText.value = ui.localize(
                '@sage/xtrem-mailer/pages_sys_email_config_domain_not_validated_check_dns',
                'Your domain is not validated. Resolve any errors in your DNS zone and try again.',
            );
            this.instructionText.variant = 'error';
        } else if (sanitizedConfiguration?.sendGrid?.valid) {
            // Domain successfully validated
            const defaultDomainMessage = !this.setAsDefault.isDisabled
                ? 'You can now set this domain as the default for sending emails.'
                : '';
            this.instructionText.value = ui.localize(
                '@sage/xtrem-mailer/pages_sys_email_config_domain_validated',
                'Your domain is validated. {{defaultDomainMessage}}.',
                { defaultDomainMessage },
            );
            this.instructionText.variant = 'success';
            this.setAsDefault.isDisabled = false;
            this.dns.hideColumn('reason');
        } else {
            // Dns configuration required to complete validation
            this.instructionText.value = ui.localize(
                '@sage/xtrem-mailer/pages_sys_email_config_update_dns_zone',
                'To ensure successful email delivery and domain authentication, update your DNS zone records with the necessary configurations. This enables your domain to send emails through our service.',
            );
            this.instructionText.variant = 'warning';
        }
    }

    hideShowValidateCustomDomainAction() {
        return (
            this.emailSystem.value !== 'custom' ||
            this.dns.value.length === 0 ||
            (this.dns.value.length > 0 && JSON.parse(this.configuration.value || '{}')?.sendGrid?.valid)
        );
    }

    hideShowAuthenticateCustomDomainAction() {
        return this.emailSystem.value !== 'custom' || this.dns.value.length > 0;
    }

    getSerializedValues() {
        const { values } = this.$;
        values.senderEmailSettings.forEach((replyTo: SenderEmailSetting) => {
            if (this.senderEmailSettings.value.find(({ _id }) => _id === replyTo._id)) {
                replyTo.replyToPrefixes = this.senderEmailSettings.value.find(
                    ({ _id }) => _id === replyTo._id,
                )?.replyToPrefixes;
            }
        });
        return values;
    }
}
