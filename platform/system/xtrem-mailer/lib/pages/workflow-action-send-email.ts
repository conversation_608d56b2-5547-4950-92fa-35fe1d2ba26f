import { asyncArray } from '@sage/xtrem-async-helper';
import { extractEdges } from '@sage/xtrem-client';
import { GraphApi, Report, ReportVariable } from '@sage/xtrem-reporting-api';
import { updateReportParameterColumns } from '@sage/xtrem-reporting/build/lib/client-functions/workflow-utils';
import { LocalizedText, WorkflowVariable, getTextForLocale, mergeLocalizedText } from '@sage/xtrem-shared';
import * as ui from '@sage/xtrem-ui';
import { WorkflowConfigPageData, WorkflowParameter } from '@sage/xtrem-workflow';
import { WorkflowDefinition as WorkflowDefinitionApi } from '@sage/xtrem-workflow-api';
import {
    NodeDetailsFetcher,
    convertDynamicPodValueToWorkflowParameters,
    convertWorkflowParametersToDynamicPodValue,
    getInputData,
    getUsedVariablePathsFromDynamicPod,
    resolveSysTagsFromNaturalKeys,
    variableIsReferenceToNodeWithAttachments,
} from '@sage/xtrem-workflow/build/lib/client-functions';
import { variableIsReferenceToUploadedFile } from '@sage/xtrem-workflow/lib/client-functions';
import {
    collectEmbeddedPaths,
    getDynamicSelectProperties,
    getNestedDynamicSelectProperties,
    getStepVariablesForSerialization,
    setOptionsForDynamicSelect,
    tidyVariables,
    transformVariablesFromSerialization,
    validateDynamicVariablesPickers,
} from '@sage/xtrem-workflow/lib/client-functions/variable-utils';
import { omit, pick } from 'lodash';
import { AddressConfig } from '../shared-functions';

interface AttachmentLine {
    _id: string;
    variable?: string;
    tags?: {
        _id: string;
        name: string;
        description: string;
    }[];
    failIfNotFound?: boolean;
}

interface WorkflowVariableEx extends WorkflowVariable {
    /**
     * Returns whether the variable is a reference to a node with attachments
     */
    hasAttachments?: boolean;
    /**
     * Returns whether the variable is an uploaded file
     */
    isUploadedFile?: boolean;
}

@ui.decorators.page<WorkflowActionSendEmail, WorkflowDefinitionApi>({
    title: 'Action configuration',
    subtitle: 'Send an email',
    mode: 'tabs',

    businessActions() {
        if (this.$.queryParameters.isReadOnly) {
            return [this.$standardCancelAction];
        }
        return [this.$standardDialogConfirmationAction];
    },
    async onLoad() {
        await this._onLoad();
    },
})
export class WorkflowActionSendEmail extends ui.Page<GraphApi> {
    @ui.decorators.section<WorkflowActionSendEmail>({
        isTitleHidden: true,
        title: 'General',
        isDisabled() {
            return !!this.$.queryParameters.isReadOnly;
        },
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<WorkflowActionSendEmail>({
        parent() {
            return this.mainSection;
        },
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.section<WorkflowActionSendEmail>({
        isTitleHidden: true,
        title: 'Body',
        isDisabled() {
            return !!this.$.queryParameters.isReadOnly;
        },
    })
    bodySection: ui.containers.Section;

    @ui.decorators.block<WorkflowActionSendEmail>({
        parent() {
            return this.bodySection;
        },
    })
    bodyBlock: ui.containers.Block;

    @ui.decorators.textField<WorkflowActionSendEmail>({
        title: 'Action title',
        helperText: 'Title displayed in the workflow diagram.',
        isFullWidth: true,
        isTransient: true,
        parent() {
            return this.mainBlock;
        },
    })
    title: ui.fields.Text;

    // Note: localizedTitle is a JSON object that contains the translations (something like { 'en-US': 'Hello', 'fr-FR': 'Bonjour' })
    // title is only editing the translation for the current locale
    @ui.decorators.technicalJsonField<WorkflowActionSendEmail>({})
    localizedTitle: ui.fields.TechnicalJson<LocalizedText>;

    @ui.decorators.textField<WorkflowActionSendEmail>({})
    subtitle: ui.fields.Text;

    @ui.decorators.technicalJsonField<WorkflowActionSendEmail>({})
    stepVariables: ui.fields.TechnicalJson<WorkflowVariable[]>;

    @ui.decorators.dynamicSelectField<WorkflowActionSendEmail>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
        isTransient: true,
        isMandatory: true,
        title: 'Subject',

        ...getDynamicSelectProperties<WorkflowActionSendEmail, WorkflowVariableEx>({
            mode: 'input',
            getVariables: page => page.currentVariables,
            selectableVariableFilter: () => true,
            getOldRootPaths: page => page.oldRootPaths,
            updateCurrentVariables: (page, variables) => page._updateCurrentVariables(variables),
        }),
    })
    subject: ui.fields.DynamicSelect;

    // Note: localizedSubject is a JSON object that contains the translations (something like { 'en-US': 'Hello', 'fr-FR': 'Bonjour' })
    // subject is only editing the translation for the current locale
    @ui.decorators.technicalJsonField<WorkflowActionSendEmail>({})
    localizedSubject: ui.fields.TechnicalJson<LocalizedText>;

    @ui.decorators.referenceField<WorkflowActionSendEmail, Report>({
        parent() {
            return this.bodyBlock;
        },
        minLookupCharacters: 0,
        title: 'Email template',
        node: '@sage/xtrem-reporting/Report',
        valueField: 'name',
        isMandatory: true,
        isFullWidth: true,
        filter: { reportType: { _eq: 'email' } },
        async onChange() {
            this.reportParametersPod.value = {};
            this.subtitle.value = this.report.value?.name || null;
            await this.loadReportVariables();
            this.updateReportParameterColumns();
        },
    })
    report: ui.fields.Reference;

    @ui.decorators.technicalJsonField<WorkflowActionSendEmail>({})
    reportParameters: ui.fields.TechnicalJson<WorkflowParameter[]>;

    @ui.decorators.dynamicPodField<WorkflowActionSendEmail>({
        title: 'Parameters',
        isFullWidth: true,
        isHidden: true,
        columns: [],
        async onChange() {
            this.updateReportParameterColumns();
        },
        parent() {
            return this.bodyBlock;
        },
    })
    reportParametersPod: ui.fields.DynamicPod;

    @ui.decorators.section<WorkflowActionSendEmail>({
        isTitleHidden: true,
        title: 'Recipients',
        isDisabled() {
            return !!this.$.queryParameters.isReadOnly;
        },
    })
    addressListSection: ui.containers.Section;

    @ui.decorators.block<WorkflowActionSendEmail>({
        parent() {
            return this.addressListSection;
        },
    })
    addressListBlock: ui.containers.Block;

    @ui.decorators.textField<WorkflowActionSendEmail>({
        title: 'From',
        isFullWidth: true,
        isMandatory: false,
        parent() {
            return this.addressListBlock;
        },
    })
    from: ui.fields.Text;

    @ui.decorators.textField<WorkflowActionSendEmail>({
        title: 'Reply to',
        isFullWidth: true,
        isMandatory: false,
        parent() {
            return this.addressListBlock;
        },
    })
    replyTo: ui.fields.Text;

    @ui.decorators.technicalJsonField<WorkflowActionSendEmail>({})
    addressList: ui.fields.TechnicalJson<AddressConfig[]>;

    @ui.decorators.podCollectionField<WorkflowActionSendEmail>({
        parent() {
            return this.addressListSection;
        },
        title: 'Addresses',
        addButtonText: 'Add a recipient',
        isTitleHidden: true,
        isFullWidth: true,
        recordWidth: 'large',
        validation() {
            if (this.addressListPod.value.length === 0) {
                return ui.localize('@sage/xtrem-mailer/workflow_error_no_recipient', 'You need to add a recipient.');
            }
            return undefined;
        },
        recordTitle() {
            return 'Email address';
        },
        columns: [
            ui.nestedFields.select({
                bind: 'addressType',
                title: 'Recipient type',
                options: ['to', 'cc', 'bcc'],
                width: 'medium',
            }),
            ui.nestedFields.dynamicSelect({
                title: 'Email address',
                bind: 'variable',
                width: 'extra-large',
                isHelperTextHidden: true,
                ...getNestedDynamicSelectProperties<WorkflowActionSendEmail>({
                    mode: 'select',
                    getVariables: page => page.currentVariables,
                    selectableVariableFilter: (_page, variable) => variable.type === 'String',
                    getOldRootPaths: page => page.oldRootPaths,
                    updateCurrentVariables: (page, variables) => page._updateCurrentVariables(variables),
                }),
                isHidden(_id, rowValue) {
                    return !rowValue?.isVariable;
                },
            }),
            ui.nestedFields.text({
                title: 'Email address',
                bind: 'value',
                width: 'extra-large',
                isHelperTextHidden: true,
                isHidden(_id, rowValue) {
                    return !!rowValue?.isVariable;
                },
            }),
            ui.nestedFields.checkbox({
                bind: 'isVariable',
                title: 'Enter manually',
                width: 'small',
                isValueReversed: true,
            }),
        ],
        canAddRecord: true,
        canRemoveRecord: true,
    })
    addressListPod: ui.fields.PodCollection<{
        _id: string;
        addressType: string;
        variable?: string;
        value?: string;
        isVariable: boolean;
    }>;

    @ui.decorators.section<WorkflowActionSendEmail>({
        isTitleHidden: true,
        title: 'Attachments',
        isDisabled() {
            return !!this.$.queryParameters.isReadOnly;
        },
    })
    attachmentListSection: ui.containers.Section;

    @ui.decorators.block<WorkflowActionSendEmail>({
        parent() {
            return this.attachmentListSection;
        },
    })
    attachmentListBlock: ui.containers.Block;

    @ui.decorators.podCollectionField<WorkflowActionSendEmail>({
        parent() {
            return this.attachmentListSection;
        },
        title: 'Attachments',
        addButtonText: 'Add an attachment',
        isTitleHidden: true,
        isFullWidth: true,
        recordWidth: 'large',
        recordTitle() {
            return 'Attachment';
        },
        columns: [
            ui.nestedFields.dynamicSelect({
                isMandatory: true,
                bind: 'variable',
                title: 'From',
                isTitleHidden: true,
                isFullWidth: true,
                ...getNestedDynamicSelectProperties<WorkflowActionSendEmail, WorkflowVariableEx>({
                    mode: 'select',
                    getVariables: page => page.currentVariables,
                    selectableVariableFilter: (_page, variable) =>
                        !!(variable.hasAttachments || variable.isUploadedFile),
                    getOldRootPaths: page => page.oldRootPaths,
                    updateCurrentVariables: (page, variables) => page._updateCurrentVariables(variables),
                }),
            }),
            ui.nestedFields.multiReference({
                node: '@sage/xtrem-system/SysTag',
                bind: 'tags',
                isTransient: true,
                title: 'Tags',
                helperText:
                    'Only attachments with all the selected tags will be attached (or all the attachments if none are selected).',
                placeholder: 'Select tags',
                minLookupCharacters: 0,
                lookupDialogTitle: 'Select tags',
                valueField: 'name',
                isFullWidth: true,
                helperTextField: 'description',
                isHidden(_id, rowValue) {
                    return this.hideTagsRelatedFields(rowValue as AttachmentLine);
                },
                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'description' })],
            }),
            ui.nestedFields.checkbox({
                bind: 'failIfNotFound',
                title: 'Fail if no attachments found',
                isFullWidth: true,
                isHidden(_id, rowValue) {
                    return this.hideTagsRelatedFields(rowValue as AttachmentLine);
                },
            }),
        ],
        canAddRecord: true,
        canRemoveRecord: true,
    })
    attachmentList: ui.fields.PodCollection<AttachmentLine>;

    @ui.decorators.textField<WorkflowActionSendEmail>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
        isMandatory: true,
        title: 'Output variable name',
    })
    outputVariableName: ui.fields.Text;

    // Private variables

    private reportVariables: ReportVariable[] = [];

    private currentVariables: WorkflowVariableEx[] = [];

    private oldRootPaths: string[];

    private _nodeDetailsFetcher = new NodeDetailsFetcher();

    // Methods

    private async _onLoad() {
        const { oldRootPaths } = getInputData(this);
        this.stepVariables.value = transformVariablesFromSerialization(this.stepVariables.value ?? []);
        this.oldRootPaths = oldRootPaths;
        await this._updateCurrentVariables([]);

        await this.restoreReportFieldFromString();
        this.reportParametersPod.value = convertWorkflowParametersToDynamicPodValue(this.reportParameters.value ?? []);

        if (this.attachmentList.value.length > 0) {
            this.attachmentList.value = await asyncArray(this.attachmentList.value)
                .map(async attachment => {
                    return {
                        ...attachment,
                        tags: await resolveSysTagsFromNaturalKeys(this, attachment.tags as any as string[]),
                    };
                })
                .toArray();
        }

        await this.loadReportVariables();
        this.updateReportParameterColumns();

        this.fillAddressListPod();

        this._setOptionsForDynamicSelects();

        if (!this.outputVariableName.value) {
            this.outputVariableName.value = 'sentEmail';
        }

        const locale = this.$.locale;
        this.title.value = getTextForLocale(this.localizedTitle.value ?? {}, locale);
        this.subject.value = getTextForLocale(this.localizedSubject.value ?? {}, locale);

        await this._validateFields();
    }

    /**
     * Validate the fields of the page.
     */
    private async _validateFields(): Promise<void> {
        await validateDynamicVariablesPickers([
            this.subject,
            this.reportParametersPod,
            this.attachmentList,
            this.addressListPod,
        ]);
    }

    private async _updateCurrentVariables(variables: WorkflowVariable[]): Promise<void> {
        const { inputVariables } = getInputData(this);
        this.currentVariables = tidyVariables([...inputVariables, ...this.stepVariables.value, ...variables]);
        await asyncArray(this.currentVariables).forEach(async variable => {
            variable.hasAttachments = await variableIsReferenceToNodeWithAttachments(
                variable,
                this._nodeDetailsFetcher,
            );
            variable.isUploadedFile = variableIsReferenceToUploadedFile(variable);
        });

        this._setOptionsForDynamicSelects();
        this.updateReportParameterColumns();

        this.addressListPod.value = [...(this.addressListPod.value ?? [])];
        this.attachmentList.value = [...(this.attachmentList.value ?? [])];
    }

    /**
     * Set the options for the dynamic select fields.
     */
    private _setOptionsForDynamicSelects(): void {
        setOptionsForDynamicSelect(this.subject, this.currentVariables);
    }

    private async restoreReportFieldFromString(): Promise<void> {
        const name = this.report.value;
        if (name) {
            const result = await this.$.graph
                .node('@sage/xtrem-reporting/Report')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            _id: true,
                            name: true,
                        },
                        {
                            filter: { name },
                        },
                    ),
                )
                .execute();

            if (result.edges.length === 0) {
                throw new Error(`The email template '${name}' does not exist.`);
            }
            this.report.value = result.edges[0].node;
        }
    }

    private fillAddressListPod() {
        const addressList = this.addressList.value;
        if (addressList?.length) {
            this.addressListPod.value = addressList.map((address, i) => ({ _id: i.toString(), ...address }));
        }
    }

    private async loadReportVariables() {
        if (!this.report.value?._id) return;

        this.reportVariables = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-reporting/ReportVariable')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            _id: true,
                            _sortValue: true,
                            name: true,
                            type: true,
                            title: true,
                            dataType: {
                                name: true,
                                attributes: true,
                            },
                            report: {
                                _id: true,
                            },
                        },
                        { filter: { report: this.report.value._id } },
                    ),
                )
                .execute(),
        ) as any;

        this.reportVariables.forEach(variable => {
            const isVariableFieldName = `${variable.name}/isVariable`;
            if (this.reportParametersPod.value[isVariableFieldName] === undefined) {
                this.reportParametersPod.value[isVariableFieldName] = true;
            }
        });
    }

    private updateReportParameterColumns(): void {
        this.reportParametersPod.isHidden = !this.report.value?._id || this.reportVariables.length === 0;
        updateReportParameterColumns({
            reportParametersPod: this.reportParametersPod,
            reportVariables: this.reportVariables,
            locale: this.$.locale ?? 'en-US',
            getPropertiesForDynamicSelects: (filter: (_page: ui.Page, variable: WorkflowVariable) => boolean) => {
                return getNestedDynamicSelectProperties<WorkflowActionSendEmail>({
                    mode: 'select',
                    getVariables: page => page.currentVariables,
                    selectableVariableFilter: filter,
                    getOldRootPaths: page => page.oldRootPaths,
                    updateCurrentVariables: (page, variables) => page._updateCurrentVariables(variables),
                });
            },
        });
    }

    /**
     * Returns the paths of the variables used by the action that should be serialized in the action's payload.
     */
    private _getUsedVariablePathsForSerialization(): string[] {
        const usedPaths = [] as string[];
        usedPaths.push(...collectEmbeddedPaths(this.subject.value ?? ''));
        this.addressListPod.value?.forEach(address => {
            if (address.isVariable && address.variable) {
                usedPaths.push(address.variable);
            }
        });
        const variableNamesToCheck = this.reportVariables.map(variable => variable.name);
        usedPaths.push(...getUsedVariablePathsFromDynamicPod(this.reportParametersPod, variableNamesToCheck));
        this.attachmentList.value?.forEach(attachment => {
            if (attachment.variable) usedPaths.push(attachment.variable);
        });
        return usedPaths;
    }

    getSerializedValues(): WorkflowConfigPageData<any> {
        const usedPaths = this._getUsedVariablePathsForSerialization();
        const stepVariables = [
            ...getStepVariablesForSerialization(this.currentVariables, usedPaths),
            {
                path: `${this.outputVariableName.value}.success`,
                title: 'Mail sent OK',
                type: 'Boolean',
            },
        ];

        const locale = this.$.locale;
        const localizedTitle = mergeLocalizedText(this.localizedTitle.value ?? {}, this.title.value ?? '', locale);
        const localizedSubject = mergeLocalizedText(
            this.localizedSubject.value ?? {},
            this.subject.value ?? '',
            locale,
        );

        const attachmentList = [...(this.attachmentList.value ?? [])].map(attachment => {
            return {
                variable: attachment.variable,
                tags: attachment.tags?.map(tag => `#${tag.name}`),
                failIfNotFound: attachment.failIfNotFound,
            };
        });

        return {
            ...pick(this.$.values, 'subtitle', 'outputVariableName', 'from', 'replyTo'),
            report: this.report.value?.name,
            reportParameters: convertDynamicPodValueToWorkflowParameters(this.reportParametersPod.value),
            stepVariables,
            addressList: this.addressListPod.value.map(address => omit(address, ['_id'])),
            localizedTitle,
            localizedSubject,
            attachmentList,
        };
    }

    /**
     * Should the tags related fields be hidden?
     * @returns
     */
    private hideTagsRelatedFields(line: AttachmentLine): boolean {
        const variable = this.currentVariables.find(v => v.path === line.variable);
        return !variable?.hasAttachments;
    }
}
