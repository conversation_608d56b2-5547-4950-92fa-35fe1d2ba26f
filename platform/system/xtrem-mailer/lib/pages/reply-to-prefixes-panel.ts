import type { GraphApi } from '@sage/xtrem-mailer-api';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<ReplyToPrefixesPanel>({
    title: 'Edit reply-to prefixes',
    mode: 'default',
    module: 'mailer',
    isTransient: true,
    businessActions() {
        return [this.cancel, this.confirm];
    },
    onLoad() {
        const emailsToEdit = this.$.queryParameters.emails
            ? (JSON.parse(String(this.$.queryParameters.emails)) as string[])
            : undefined;

        this.emails.value = [];
        if (emailsToEdit) {
            if (emailsToEdit.length > 0) {
                emailsToEdit.forEach(email => {
                    this.emails.addOrUpdateRecordValue({ email });
                });
            }
        }
        this.$.setPageClean();
        this.confirm.isDisabled = true;
    },
    onDirtyStateUpdated(isDirty: boolean) {
        this.confirm.isDisabled = !isDirty;
    },
})
export class ReplyToPrefixesPanel extends ui.Page<GraphApi> {
    @ui.decorators.pageAction<ReplyToPrefixesPanel>({
        title: 'OK',
        async onClick() {
            const validation = await this.$.page.validate();
            if (validation.length === 0) {
                this.$.finish(JSON.stringify(this.emails.value.map(email => email.email)));
            } else {
                this.$.showToast(validation.join('\n'), { type: 'error' });
            }
        },
    })
    confirm: ui.PageAction;

    @ui.decorators.pageAction<ReplyToPrefixesPanel>({
        title: 'Cancel',
        buttonType: 'tertiary',
        onClick() {
            this.$.finish();
        },
    })
    cancel: ui.PageAction;

    @ui.decorators.section<ReplyToPrefixesPanel>({ isTitleHidden: true, title: 'General' })
    mainSection: ui.containers.Section;

    @ui.decorators.block<ReplyToPrefixesPanel>({
        parent() {
            return this.mainSection;
        },
        title: 'Emails',
        isTitleHidden: true,
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.tableField<ReplyToPrefixesPanel>({
        title: 'Reply-to prefixes',
        isTitleHidden: true,
        canSelect: false,
        parent() {
            return this.mainBlock;
        },
        columns: [
            ui.nestedFields.text({
                title: 'Email prefixes',
                bind: 'email',
            }),
        ],
        dropdownActions: [
            {
                icon: 'bin',
                title: 'Delete',
                isDestructive: true,
                onClick(rowId: string) {
                    this.emails.removeRecord(rowId);
                },
            },
        ],
        fieldActions() {
            return [this.addEmail];
        },
    })
    emails: ui.fields.Table;

    @ui.decorators.pageAction<ReplyToPrefixesPanel>({
        icon: 'add',
        title: 'Add',
        onClick() {
            this.emails.addRecord({});
        },
    })
    addEmail: ui.PageAction;
}
