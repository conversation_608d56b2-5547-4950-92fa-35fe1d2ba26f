declare module '@sage/xtrem-mailer-api-partial' {
    import type { Package as SageXtremAuditing$Package } from '@sage/xtrem-auditing-api';
    import type { Package as SageXtremAuthorization$Package } from '@sage/xtrem-authorization-api';
    import type { Package as SageXtremCommunication$Package } from '@sage/xtrem-communication-api';
    import type { Package as SageXtremCustomization$Package } from '@sage/xtrem-customization-api';
    import type { Package as SageXtremDashboard$Package } from '@sage/xtrem-dashboard-api';
    import type { MetaNodeFactory, MetaPackage, Package as SageXtremMetadata$Package } from '@sage/xtrem-metadata-api';
    import type {
        Package as SageXtremReporting$Package,
        Report,
        ReportTranslatableText,
        ReportTranslatableTextBinding,
        ReportTranslatableTextInput,
        ReportWizard,
        ReportWizardBinding,
        ReportWizardInput,
    } from '@sage/xtrem-reporting-api';
    import type { Package as SageXtremRouting$Package } from '@sage/xtrem-routing-api';
    import type { Package as SageXtremScheduler$Package } from '@sage/xtrem-scheduler-api';
    import type { Company, Package as SageXtremSystem$Package, Site, SysVendor, User } from '@sage/xtrem-system-api';
    import type { Package as SageXtremUpload$Package } from '@sage/xtrem-upload-api';
    import type { Package as SageXtremWorkflow$Package } from '@sage/xtrem-workflow-api';
    import type {
        AggregateQueryOperation,
        AggregateReadOperation,
        AsyncOperation,
        ClientCollection,
        ClientNode,
        ClientNodeInput,
        GetDefaultsOperation,
        Operation as Node$Operation,
        QueryOperation,
        ReadOperation,
        TextStream,
        UpdateByIdOperation,
        UpdateOperation,
        VitalClientNode,
        VitalClientNodeInput,
        decimal,
        integer,
    } from '@sage/xtrem-client';
    export interface EmailSystem$Enum {
        sage: 1;
        custom: 2;
    }
    export type EmailSystem = keyof EmailSystem$Enum;
    export interface Mailer extends ClientNode {}
    export interface MailerInput extends ClientNodeInput {}
    export interface MailerBinding extends ClientNode {}
    export interface Mailer$AsyncOperations {
        sendMail: AsyncOperation<
            {
                subject: string;
                recipients: {
                    to?: {
                        name?: string;
                        address?: string;
                    }[];
                    cc?: {
                        name?: string;
                        address?: string;
                    }[];
                    bcc?: {
                        name?: string;
                        address?: string;
                    }[];
                };
                reportName: string;
                reportVariables: string;
                attachments?: string[];
                from?: {
                    name?: string;
                    address?: string;
                };
                replyTo?: {
                    name?: string;
                    address?: string;
                };
            },
            boolean
        >;
    }
    export interface Mailer$Operations {
        asyncOperations: Mailer$AsyncOperations;
    }
    export interface SenderEmailSetting extends VitalClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        emailConfig: SysEmailConfig;
        fromPrefix: string;
        node: MetaNodeFactory;
        metaPackage: MetaPackage;
        company: Company;
        site: Site;
        replyToPrefixes: string[];
        displayReplyToPrefixes: string;
    }
    export interface SenderEmailSettingInput extends VitalClientNodeInput {
        _vendor?: integer | string;
        fromPrefix?: string;
        node?: integer | string;
        metaPackage?: integer | string;
        company?: integer | string;
        site?: integer | string;
        replyToPrefixes?: string[];
    }
    export interface SenderEmailSettingBinding extends VitalClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        emailConfig: SysEmailConfig;
        fromPrefix: string;
        node: MetaNodeFactory;
        metaPackage: MetaPackage;
        company: Company;
        site: Site;
        replyToPrefixes: string[];
        displayReplyToPrefixes: string;
    }
    export interface SenderEmailSetting$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SenderEmailSetting$Lookups {
        _vendor: QueryOperation<SysVendor>;
        node: QueryOperation<MetaNodeFactory>;
        metaPackage: QueryOperation<MetaPackage>;
        company: QueryOperation<Company>;
        site: QueryOperation<Site>;
    }
    export interface SenderEmailSetting$Operations {
        query: QueryOperation<SenderEmailSetting>;
        read: ReadOperation<SenderEmailSetting>;
        aggregate: {
            read: AggregateReadOperation<SenderEmailSetting>;
            query: AggregateQueryOperation<SenderEmailSetting>;
        };
        asyncOperations: SenderEmailSetting$AsyncOperations;
        lookups(dataOrId: string | { data: SenderEmailSettingInput }): SenderEmailSetting$Lookups;
        getDefaults: GetDefaultsOperation<SenderEmailSetting>;
    }
    export interface SysEmailConfig extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        emailSystem: EmailSystem;
        domain: string;
        senderEmailSettings: ClientCollection<SenderEmailSetting>;
        sanitizedConfiguration: string;
    }
    export interface SysEmailConfigInput extends ClientNodeInput {
        _vendor?: integer | string;
        id?: string;
        emailSystem?: EmailSystem;
        senderEmailSettings?: Partial<SenderEmailSettingInput>[];
    }
    export interface SysEmailConfigBinding extends ClientNode {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        emailSystem: EmailSystem;
        domain: string;
        senderEmailSettings: ClientCollection<SenderEmailSettingBinding>;
        sanitizedConfiguration: any;
    }
    export interface SysEmailConfig$Mutations {
        authenticateSendGridCustomDomain: Node$Operation<
            {
                id?: string;
            },
            {
                id: string;
                emailSystem: EmailSystem;
                domain: string;
                sanitizedConfiguration: string;
            }
        >;
        validateSendGridDomainAuthentication: Node$Operation<
            {
                id: string;
            },
            {
                id: string;
                emailSystem: EmailSystem;
                domain: string;
                sanitizedConfiguration: string;
            }
        >;
        setAsDefault: Node$Operation<
            {
                id: string;
                emailSystem: string;
            },
            {
                id: string;
                emailSystem: EmailSystem;
                domain: string;
                sanitizedConfiguration: string;
            }
        >;
        removeCustomDomainConfiguration: Node$Operation<
            {
                sysEmailConfig: string;
            },
            boolean
        >;
    }
    export interface SysEmailConfig$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SysEmailConfig$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface SysEmailConfig$Operations {
        query: QueryOperation<SysEmailConfig>;
        read: ReadOperation<SysEmailConfig>;
        aggregate: {
            read: AggregateReadOperation<SysEmailConfig>;
            query: AggregateQueryOperation<SysEmailConfig>;
        };
        update: UpdateOperation<SysEmailConfigInput, SysEmailConfig>;
        updateById: UpdateByIdOperation<SysEmailConfigInput, SysEmailConfig>;
        mutations: SysEmailConfig$Mutations;
        asyncOperations: SysEmailConfig$AsyncOperations;
        lookups(dataOrId: string | { data: SysEmailConfigInput }): SysEmailConfig$Lookups;
        getDefaults: GetDefaultsOperation<SysEmailConfig>;
    }
    export interface ReportTemplateExtension {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        name: string;
        attachmentTemplate: TextStream;
        attachmentName: string;
        attachmentMimeType: string;
        styleSheet: TextStream;
        query: TextStream;
        code: TextStream;
        report: Report;
        reportWizard: ReportWizard;
        isFactory: boolean;
        isExpertDocument: boolean;
        reportType: ReportType;
        templateType: TemplateType;
        translatableTexts: ClientCollection<ReportTranslatableText>;
        baseLocale: ReportLocale;
        defaultPaperFormat: ReportPaperFormat;
        defaultPageOrientation: ReportPageOrientation;
        defaultLeftMargin: string;
        defaultRightMargin: string;
        defaultTopMargin: string;
        defaultBottomMargin: string;
        isDefaultHeaderFooter: boolean;
        externalHtmlTemplate: TextStream;
        externalHeaderHtmlTemplate: TextStream;
        externalFooterHtmlTemplate: TextStream;
    }
    export interface ReportTemplateInputExtension {
        _vendor?: integer | string;
        name?: string;
        attachmentTemplate?: TextStream;
        attachmentName?: string;
        attachmentMimeType?: string;
        styleSheet?: TextStream;
        query?: TextStream;
        code?: TextStream;
        report?: integer | string;
        reportWizard?: ReportWizardInput;
        isFactory?: boolean | string;
        isExpertDocument?: boolean | string;
        translatableTexts?: Partial<ReportTranslatableTextInput>[];
        baseLocale?: ReportLocale;
        defaultPaperFormat?: ReportPaperFormat;
        defaultPageOrientation?: ReportPageOrientation;
        defaultLeftMargin?: decimal | string;
        defaultRightMargin?: decimal | string;
        defaultTopMargin?: decimal | string;
        defaultBottomMargin?: decimal | string;
        isDefaultHeaderFooter?: boolean | string;
        externalHtmlTemplate?: TextStream;
        externalHeaderHtmlTemplate?: TextStream;
        externalFooterHtmlTemplate?: TextStream;
    }
    export interface ReportTemplateBindingExtension {
        _factory: MetaNodeFactory;
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        name: string;
        attachmentTemplate: TextStream;
        attachmentName: string;
        attachmentMimeType: string;
        styleSheet: TextStream;
        query: TextStream;
        code: TextStream;
        report: Report;
        reportWizard: ReportWizardBinding;
        isFactory: boolean;
        isExpertDocument: boolean;
        reportType: ReportType;
        templateType: TemplateType;
        translatableTexts: ClientCollection<ReportTranslatableTextBinding>;
        baseLocale: ReportLocale;
        defaultPaperFormat: ReportPaperFormat;
        defaultPageOrientation: ReportPageOrientation;
        defaultLeftMargin: string;
        defaultRightMargin: string;
        defaultTopMargin: string;
        defaultBottomMargin: string;
        isDefaultHeaderFooter: boolean;
        externalHtmlTemplate: TextStream;
        externalHeaderHtmlTemplate: TextStream;
        externalFooterHtmlTemplate: TextStream;
    }
    export interface ReportTemplateExtension$Mutations {
        sendTestEmail: Node$Operation<
            {
                reportTemplateName: string;
                subject: string;
                emailAddress: string;
                testData: string;
            },
            {
                isSent: boolean;
            }
        >;
    }
    export interface ReportTemplateExtension$Operations {
        mutations: ReportTemplateExtension$Mutations;
        getDefaults: GetDefaultsOperation<ReportTemplate>;
    }
    export interface Package {
        '@sage/xtrem-mailer/Mailer': Mailer$Operations;
        '@sage/xtrem-mailer/SenderEmailSetting': SenderEmailSetting$Operations;
        '@sage/xtrem-mailer/SysEmailConfig': SysEmailConfig$Operations;
    }
    export interface GraphApi
        extends Package,
            SageXtremAuditing$Package,
            SageXtremAuthorization$Package,
            SageXtremCommunication$Package,
            SageXtremCustomization$Package,
            SageXtremDashboard$Package,
            SageXtremMetadata$Package,
            SageXtremReporting$Package,
            SageXtremRouting$Package,
            SageXtremScheduler$Package,
            SageXtremSystem$Package,
            SageXtremUpload$Package,
            SageXtremWorkflow$Package {}
}
declare module '@sage/xtrem-mailer-api' {
    export type * from '@sage/xtrem-mailer-api-partial';
}
declare module '@sage/xtrem-auditing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-mailer-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-authorization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-mailer-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-communication-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-mailer-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-customization-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-mailer-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-dashboard-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-mailer-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-metadata-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-mailer-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-reporting-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-mailer-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-routing-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-mailer-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-scheduler-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-mailer-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-mailer-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-upload-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-mailer-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-workflow-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-mailer-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-reporting-api-partial' {
    import type {
        ReportTemplateBindingExtension,
        ReportTemplateExtension,
        ReportTemplateExtension$Mutations,
        ReportTemplateExtension$Operations,
        ReportTemplateInputExtension,
    } from '@sage/xtrem-mailer-api';
    export interface ReportTemplate extends ReportTemplateExtension {}
    export interface ReportTemplateBinding extends ReportTemplateBindingExtension {}
    export interface ReportTemplateInput extends ReportTemplateInputExtension {}
    export interface ReportTemplate$Mutations extends ReportTemplateExtension$Mutations {}
    export interface ReportTemplate$Operations extends ReportTemplateExtension$Operations {}
}
