{"edges": [{"id": "test-started-1--out-1", "source": "test-started-1", "target": "send-email-1", "sourceHandle": "out"}, {"id": "send-email-1--out-1", "source": "send-email-1", "target": "test-stub-1", "sourceHandle": "out"}], "nodes": [{"id": "test-started-1", "data": {"topic": "WorkflowProcess/testStarted", "parameters": [{"name": "userName", "type": "String"}, {"name": "email", "type": "String"}, {"name": "attachment1", "node": "UploadedFile", "type": "IntReference"}], "localizedTitle": {"en-US": "Start the test", "fr-FR": "<PERSON><PERSON><PERSON><PERSON> le test"}, "stepVariables": [{"path": "test.parameters.userName", "type": "String", "title": "User name", "hasAttachments": false, "isUploadedFile": false}, {"path": "test.parameters.email", "type": "String", "title": "Email", "hasAttachments": false, "isUploadedFile": false}, {"node": "UploadedFile", "path": "test.parameters.attachment1", "type": "IntReference", "title": "Attachment 1", "hasAttachments": false, "isUploadedFile": true}]}, "type": "test-started", "width": 250, "height": 80, "origin": [0.5, 0], "dragging": false, "position": {"x": 560, "y": 20}, "selected": false, "positionAbsolute": {"x": 560, "y": 20}}, {"id": "send-email-1", "data": {"report": "onboarding_user", "subtitle": "onboarding_user", "addressList": [{"_action": "create", "variable": "test.parameters.email", "isVariable": true, "addressType": "to"}], "stepVariables": [{"node": "UploadedFile", "path": "test.parameters.attachment1", "type": "IntReference", "title": "Attachment 1"}, {"path": "test.parameters.email", "type": "String", "title": "Email"}, {"path": "test.parameters.userName", "type": "String", "title": "User name"}, {"path": "sendEmail.success", "type": "Boolean", "title": "Mail sent OK"}], "attachmentList": [{"tags": [], "variable": "test.parameters.attachment1"}], "localizedTitle": {"en-US": "Send welcome email", "fr-FR": "Envoyer un email de bienvenue"}, "localizedSubject": {"en-US": "Hello {{test.parameters.userName}}", "fr-FR": "Bonjour {{test.parameters.userName}}"}, "reportParameters": [{"name": "userName", "value": "test.parameters.userName", "isVariable": true}], "outputVariableName": "sendEmail"}, "type": "send-email", "width": 250, "height": 101, "origin": [0.5, 0], "dragging": false, "position": {"x": 560, "y": 160}, "selected": false, "positionAbsolute": {"x": 560, "y": 160}}, {"id": "test-stub-1", "data": {"subtitle": null, "localizedTitle": {"en-US": "End the test", "fr-FR": "Te<PERSON><PERSON> le test"}, "stepVariables": [{"path": "done", "type": "Float", "title": "End test result ({End the test})"}], "outputVariableName": "done"}, "type": "test-stub", "width": 250, "height": 80, "origin": [0.5, 0], "dragging": false, "position": {"x": 560, "y": 300}, "selected": false, "positionAbsolute": {"x": 560, "y": 300}}]}