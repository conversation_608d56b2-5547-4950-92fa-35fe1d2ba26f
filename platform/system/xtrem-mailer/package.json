{"name": "@sage/xtrem-mailer", "description": "A Sage X3 Xtrem Package", "version": "58.0.2", "author": "sage", "license": "UNLICENSED", "xtrem": {"isPlatform": true, "isSealed": true, "hasListeners": true, "queue": "reporting"}, "keywords": ["xtrem-application-package"], "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/index.js", "typings": "build/package-definition.d.ts", "dependencies": {"@sage/xtrem-async-helper": "workspace:*", "@sage/xtrem-client": "workspace:*", "@sage/xtrem-config": "workspace:*", "@sage/xtrem-core": "workspace:*", "@sage/xtrem-date-time": "workspace:*", "@sage/xtrem-decimal": "workspace:*", "@sage/xtrem-infrastructure-adapter": "workspace:*", "@sage/xtrem-metadata": "workspace:*", "@sage/xtrem-reporting": "workspace:*", "@sage/xtrem-shared": "workspace:*", "@sage/xtrem-system": "workspace:*", "@sage/xtrem-ui": "workspace:*", "@sage/xtrem-ui-plugin-monaco": "workspace:*", "@sage/xtrem-upload": "workspace:*", "@sage/xtrem-workflow": "workspace:*", "axios": "^1.11.0", "handlebars": "^4.7.8", "lodash": "^4.17.21", "mime-types": "^3.0.0", "nanoid": "^3.3.8", "nodemailer": "7.0.5"}, "devDependencies": {"@sage/eslint-plugin-xtrem": "workspace:*", "@sage/xtrem-cli": "workspace:*", "@sage/xtrem-cli-bundle-dev": "workspace:*", "@sage/xtrem-mailer-api": "workspace:*", "@sage/xtrem-metadata-api": "workspace:*", "@sage/xtrem-reporting-api": "workspace:*", "@sage/xtrem-system-api": "workspace:*", "@sage/xtrem-workflow-api": "workspace:*", "@types/chai": "^4.3.6", "@types/chai-as-promised": "^7.1.6", "@types/lodash": "^4.14.198", "@types/mime-types": "^3.0.0", "@types/mocha": "^10.0.1", "@types/node": "^22.10.2", "@types/nodemailer": "^6.4.6", "@types/sinon": "^17.0.0", "chai": "^4.3.10", "chai-as-promised": "^7.1.1", "mocha": "^10.8.2", "sinon": "^21.0.0"}, "scripts": {"build": "xtrem compile", "build:api": "xtrem build --only-api-client", "build:binary": "xtrem compile --binary --prod", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build", "extract:test:data": "xtrem layers --extract test", "lint": "xtrem lint", "load:test:data": "xtrem layers --load setup,test", "qa:cucumber": "xtrem test test/cucumber/* --integration --noTimeout --layers=qa", "qa:cucumber:browser": "xtrem test test/cucumber/* --integration --browser --noTimeout --layers=qa", "sonarqube:scanner": "pnpm test:ci && pnpm dlx sonarqube-scanner && rm -rf coverage .nyc_output .scannerwork junit-report-*.xml", "start": "xtrem start", "test": "xtrem test --noTimeout  --unit --workflow --layers=test", "test:ci": "xtrem test --noTimeout  --unit --workflow --ci --layers=test", "xtrem": "xtrem"}, "c8": {"cache": false, "all": true, "extension": [".ts", ".tsx"], "sourceMap": true, "instrument": true, "reporter": ["text-summary", "clover", "json", "lcov"], "include": ["lib/**/*.ts"], "exclude": ["test/**/*", "data/**/*"]}}