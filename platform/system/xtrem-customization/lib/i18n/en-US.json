{"@sage/xtrem-customization/activity__custom_field__name": "Custom field", "@sage/xtrem-customization/data_types__custom_field_anchor_position_enum__name": "Custom field anchor position enum", "@sage/xtrem-customization/data_types__custom_field_component_type_enum__name": "Custom field component type enum", "@sage/xtrem-customization/data_types__custom_field_destination_type_enum__name": "Custom field destination type enum", "@sage/xtrem-customization/data_types__custom_field_id__name": "Custom field ID", "@sage/xtrem-customization/data_types__document_id__name": "Document ID", "@sage/xtrem-customization/data_types__quantity__name": "Quantity", "@sage/xtrem-customization/data_types__text__name": "Text", "@sage/xtrem-customization/enums__custom_field_anchor_position__after": "After", "@sage/xtrem-customization/enums__custom_field_anchor_position__before": "Before", "@sage/xtrem-customization/enums__custom_field_component_type__checkboxField": "Checkbox field", "@sage/xtrem-customization/enums__custom_field_component_type__dateField": "Date field", "@sage/xtrem-customization/enums__custom_field_component_type__numericField": "Numeric field", "@sage/xtrem-customization/enums__custom_field_component_type__selectField": "Select field", "@sage/xtrem-customization/enums__custom_field_component_type__switchField": "Switch field", "@sage/xtrem-customization/enums__custom_field_component_type__textField": "Text field", "@sage/xtrem-customization/enums__custom_field_destination_type__lookup": "Lookup", "@sage/xtrem-customization/enums__custom_field_destination_type__navigationBar": "Navigation bar", "@sage/xtrem-customization/enums__custom_field_destination_type__page": "Page", "@sage/xtrem-customization/field-not-valid": "Invalid technical name.", "@sage/xtrem-customization/max-cannot-be-less-than-min": "The maximum value cannot be less than the minimum value.", "@sage/xtrem-customization/max-date-cannot-be-less-than-min": "The latest date cannot be earlier than the earliest date.", "@sage/xtrem-customization/max-length-cannot-be-less-than-min": "The maximum length cannot be less than the minimum length.", "@sage/xtrem-customization/menu_item__customField": "Custom fields", "@sage/xtrem-customization/min-cannot-be-greater-than-max": "The minimum value cannot be greater than the maximum value.", "@sage/xtrem-customization/min-date-cannot-be-greater-than-max": "The earliest date cannot be later than the latest date.", "@sage/xtrem-customization/min-length-cannot-be-greater-than-max": "The minimum length cannot be greater than the maximum length.", "@sage/xtrem-customization/nodes__custom_field__asyncMutation__asyncExport": "Export", "@sage/xtrem-customization/nodes__custom_field__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-customization/nodes__custom_field__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-customization/nodes__custom_field__node_name": "Custom field", "@sage/xtrem-customization/nodes__custom_field__property__anchorPosition": "Anchor position", "@sage/xtrem-customization/nodes__custom_field__property__anchorProperty": "Anchor property", "@sage/xtrem-customization/nodes__custom_field__property__componentAttributes": "Component attributes", "@sage/xtrem-customization/nodes__custom_field__property__componentType": "Component type", "@sage/xtrem-customization/nodes__custom_field__property__dataType": "Data type", "@sage/xtrem-customization/nodes__custom_field__property__destinationTypes": "Destination types", "@sage/xtrem-customization/nodes__custom_field__property__enumValues": "Enum values", "@sage/xtrem-customization/nodes__custom_field__property__isActive": "Is active", "@sage/xtrem-customization/nodes__custom_field__property__name": "Name", "@sage/xtrem-customization/nodes__custom_field__property__node": "Node", "@sage/xtrem-customization/nodes__custom_field__property__targetNode": "Target node", "@sage/xtrem-customization/nodes__custom_field__property__title": "Title", "@sage/xtrem-customization/package__name": "Customization", "@sage/xtrem-customization/page__custom_field__componentType_checkboxField": "Checkbox", "@sage/xtrem-customization/page__custom_field__componentType_dateField": "Date", "@sage/xtrem-customization/page__custom_field__componentType_numericField": "Numeric field", "@sage/xtrem-customization/page__custom_field__componentType_selectField": "Drop-down list", "@sage/xtrem-customization/page__custom_field__componentType_switchField": "On/Off switch", "@sage/xtrem-customization/page__custom_field__componentType_textField": "Text field", "@sage/xtrem-customization/page__custom_field__display_name_mandatory": "The field cannot be empty", "@sage/xtrem-customization/page__custom_field__technical_name_unique": "The current value is used for another display name.", "@sage/xtrem-customization/pages__custom_field____navigationPanel__listItem__line2__title": "Technical name", "@sage/xtrem-customization/pages__custom_field____navigationPanel__listItem__title__title": "Record type", "@sage/xtrem-customization/pages__custom_field____navigationPanel__listItem__titleRight__title": "Package", "@sage/xtrem-customization/pages__custom_field____objectTypePlural": "Custom fields", "@sage/xtrem-customization/pages__custom_field____objectTypeSingular": "Custom field", "@sage/xtrem-customization/pages__custom_field____title": "Custom fields", "@sage/xtrem-customization/pages__custom_field__anchorPosition____helperText": "Define if the new field is inserted before or after the anchor field.", "@sage/xtrem-customization/pages__custom_field__anchorPosition____title": "Position relative to anchor field", "@sage/xtrem-customization/pages__custom_field__anchorProperty____columns__title__name": "Technical name", "@sage/xtrem-customization/pages__custom_field__anchorProperty____columns__title__title": "Technical name", "@sage/xtrem-customization/pages__custom_field__anchorProperty____columns__title__type": "Data type", "@sage/xtrem-customization/pages__custom_field__anchorProperty____helperText": "The custom field is inserted before the anchor field", "@sage/xtrem-customization/pages__custom_field__anchorProperty____title": "Anchor field", "@sage/xtrem-customization/pages__custom_field__componentType____helperText": "Graphical component that represents the value on the screen", "@sage/xtrem-customization/pages__custom_field__componentType____title": "Field type", "@sage/xtrem-customization/pages__custom_field__create____title": "Create", "@sage/xtrem-customization/pages__custom_field__dataType____helperText": "Type of the custom field in the database", "@sage/xtrem-customization/pages__custom_field__dataType____title": "Data type", "@sage/xtrem-customization/pages__custom_field__delete____title": "Delete", "@sage/xtrem-customization/pages__custom_field__destinationTypes____helperText": "Context types where the custom field is displayed in", "@sage/xtrem-customization/pages__custom_field__destinationTypes____title": "Di<PERSON>lay also on", "@sage/xtrem-customization/pages__custom_field__display_name_mandatory": "The field cannot be empty", "@sage/xtrem-customization/pages__custom_field__displayOptionsSection____title": "Display", "@sage/xtrem-customization/pages__custom_field__helperText____helperText": "Text displayed under the field.", "@sage/xtrem-customization/pages__custom_field__helperText____title": "Helper text", "@sage/xtrem-customization/pages__custom_field__infoMessage____helperText": "Additional information that is displayed in a tooltip", "@sage/xtrem-customization/pages__custom_field__infoMessage____title": "Info message", "@sage/xtrem-customization/pages__custom_field__isActive____title": "Active", "@sage/xtrem-customization/pages__custom_field__isMandatory____title": "Required", "@sage/xtrem-customization/pages__custom_field__isSortedAlphabetically____helperText": "Choose if drop-down list entries are sorted by name or by order of definition.", "@sage/xtrem-customization/pages__custom_field__isSortedAlphabetically____title": "Sort drop-down list by name", "@sage/xtrem-customization/pages__custom_field__max____title": "Maximum value", "@sage/xtrem-customization/pages__custom_field__maxDate____title": "Latest date", "@sage/xtrem-customization/pages__custom_field__maxLength____title": "Maximum length", "@sage/xtrem-customization/pages__custom_field__min____title": "Minimum value", "@sage/xtrem-customization/pages__custom_field__minDate____title": "Earliest date", "@sage/xtrem-customization/pages__custom_field__minLength____title": "Minimum length", "@sage/xtrem-customization/pages__custom_field__name____helperText": "Technical name of the new custom property", "@sage/xtrem-customization/pages__custom_field__name____title": "Technical name", "@sage/xtrem-customization/pages__custom_field__node____columns__title__name": "Record type", "@sage/xtrem-customization/pages__custom_field__node____columns__title__package__name": "Package", "@sage/xtrem-customization/pages__custom_field__node____columns__title__title": "Record type", "@sage/xtrem-customization/pages__custom_field__node____title": "Record type", "@sage/xtrem-customization/pages__custom_field__options____columns__title__displayedName": "Displayed label", "@sage/xtrem-customization/pages__custom_field__options____columns__title__isActive": "Active", "@sage/xtrem-customization/pages__custom_field__options____columns__title__technicalName": "Technical name", "@sage/xtrem-customization/pages__custom_field__options____dropdownActions__title": "Remove", "@sage/xtrem-customization/pages__custom_field__options____title": "Drop-down list details", "@sage/xtrem-customization/pages__custom_field__postfix____helperText": "Static text displayed after the value", "@sage/xtrem-customization/pages__custom_field__postfix____title": "Postfix", "@sage/xtrem-customization/pages__custom_field__prefix____helperText": "Static text displayed in front of the value", "@sage/xtrem-customization/pages__custom_field__prefix____title": "Prefix", "@sage/xtrem-customization/pages__custom_field__scale____title": "Scale", "@sage/xtrem-customization/pages__custom_field__technical_name_unique": "The current value is used for another display name.", "@sage/xtrem-customization/pages__custom_field__title____helperText": "Label displayed above the field", "@sage/xtrem-customization/pages__custom_field__title____title": "Title", "@sage/xtrem-customization/pages__custom_field__validationSection____title": "Validation", "@sage/xtrem-customization/pages__custom_field__valueOptionsSection____title": "Drop-down list details", "@sage/xtrem-customization/pages__custom_field__warningMessage____helperText": "Warning message that is displayed in a tooltip", "@sage/xtrem-customization/pages__custom_field__warningMessage____title": "Warning message", "@sage/xtrem-customization/pages__custom_field_delete_warning": "Confirm deletion", "@sage/xtrem-customization/pages__custom_field_delete_warning_message": "Deleting a custom field definition will not delete stored custom data. After deleting, the custom field will no longer be available on pages or through the API.", "@sage/xtrem-customization/pages__custom_field_wizard____title": "Create custom field", "@sage/xtrem-customization/pages__custom_field_wizard__anchorPosition____helperText": "Define if the new field is inserted before or after the anchor field.", "@sage/xtrem-customization/pages__custom_field_wizard__anchorPosition____title": "Position relative to anchor field", "@sage/xtrem-customization/pages__custom_field_wizard__anchorProperty____columns__title__name": "Name", "@sage/xtrem-customization/pages__custom_field_wizard__anchorProperty____columns__title__title": "Name", "@sage/xtrem-customization/pages__custom_field_wizard__anchorProperty____columns__title__type": "Data type", "@sage/xtrem-customization/pages__custom_field_wizard__anchorProperty____helperText": "The custom field is inserted relative to this field.", "@sage/xtrem-customization/pages__custom_field_wizard__anchorProperty____title": "Anchor field", "@sage/xtrem-customization/pages__custom_field_wizard__componentType____helperText": "Graphical component that represents the value on the screen.", "@sage/xtrem-customization/pages__custom_field_wizard__componentType____title": "Field type", "@sage/xtrem-customization/pages__custom_field_wizard__dataEntitySection____title": "Record type", "@sage/xtrem-customization/pages__custom_field_wizard__dataType____helperText": "Type of custom field in the database.", "@sage/xtrem-customization/pages__custom_field_wizard__dataType____title": "Data type", "@sage/xtrem-customization/pages__custom_field_wizard__destinationTypes____helperText": "Context types where the custom field is displayed.", "@sage/xtrem-customization/pages__custom_field_wizard__destinationTypes____title": "Display on", "@sage/xtrem-customization/pages__custom_field_wizard__displayOptionsSection____title": "Display", "@sage/xtrem-customization/pages__custom_field_wizard__duplicate_record_warning": "There is already an existing custom record with the same name for the current node.", "@sage/xtrem-customization/pages__custom_field_wizard__helperText____helperText": "Text displayed under the field.", "@sage/xtrem-customization/pages__custom_field_wizard__helperText____title": "Helper text", "@sage/xtrem-customization/pages__custom_field_wizard__infoMessage____helperText": "Additional information that is displayed in a tooltip", "@sage/xtrem-customization/pages__custom_field_wizard__infoMessage____title": "Info message", "@sage/xtrem-customization/pages__custom_field_wizard__isMandatory____title": "Required", "@sage/xtrem-customization/pages__custom_field_wizard__isSortedAlphabetically____helperText": "Choose if drop-down list entries are sorted by name or by order of definition.", "@sage/xtrem-customization/pages__custom_field_wizard__isSortedAlphabetically____title": "Sort drop-down list by name", "@sage/xtrem-customization/pages__custom_field_wizard__max____title": "Maximum value", "@sage/xtrem-customization/pages__custom_field_wizard__maxDate____title": "Latest possible date", "@sage/xtrem-customization/pages__custom_field_wizard__maxLength____title": "Maximum length", "@sage/xtrem-customization/pages__custom_field_wizard__min____title": "Minimum value", "@sage/xtrem-customization/pages__custom_field_wizard__minDate____title": "Earliest possible date", "@sage/xtrem-customization/pages__custom_field_wizard__minLength____title": "Minimum length", "@sage/xtrem-customization/pages__custom_field_wizard__name____helperText": "Name that is used on the data model and on the API.", "@sage/xtrem-customization/pages__custom_field_wizard__name____title": "Technical name", "@sage/xtrem-customization/pages__custom_field_wizard__node____columns__title__name": "Record type", "@sage/xtrem-customization/pages__custom_field_wizard__node____columns__title__package__name": "Package", "@sage/xtrem-customization/pages__custom_field_wizard__node____columns__title__title": "Record type", "@sage/xtrem-customization/pages__custom_field_wizard__node____title": "Record type", "@sage/xtrem-customization/pages__custom_field_wizard__options____columns__title__displayedName": "Drop-down list entry", "@sage/xtrem-customization/pages__custom_field_wizard__options____columns__title__technicalName": "Technical name", "@sage/xtrem-customization/pages__custom_field_wizard__options____dropdownActions__title": "Remove", "@sage/xtrem-customization/pages__custom_field_wizard__options____title": "Value options", "@sage/xtrem-customization/pages__custom_field_wizard__positionSection____title": "Position", "@sage/xtrem-customization/pages__custom_field_wizard__postfix____helperText": "Static text displayed after the value.", "@sage/xtrem-customization/pages__custom_field_wizard__postfix____title": "Postfix", "@sage/xtrem-customization/pages__custom_field_wizard__prefix____helperText": "Static text displayed in front of the value.", "@sage/xtrem-customization/pages__custom_field_wizard__prefix____title": "Prefix", "@sage/xtrem-customization/pages__custom_field_wizard__scale____helperText": "Allowed number of decimals.", "@sage/xtrem-customization/pages__custom_field_wizard__scale____title": "Scale", "@sage/xtrem-customization/pages__custom_field_wizard__title____helperText": "Field label that is diplayed above the field.", "@sage/xtrem-customization/pages__custom_field_wizard__title____title": "Field label", "@sage/xtrem-customization/pages__custom_field_wizard__titleSection____title": "Field details", "@sage/xtrem-customization/pages__custom_field_wizard__validationSection____title": "Validation", "@sage/xtrem-customization/pages__custom_field_wizard__valueOptionsSection____title": "Drop-down list details", "@sage/xtrem-customization/pages__custom_field_wizard__warningMessage____helperText": "Warning message that is displayed in a tooltip", "@sage/xtrem-customization/pages__custom_field_wizard__warningMessage____title": "Warning message", "@sage/xtrem-customization/permission__manage__name": "Manage", "@sage/xtrem-customization/permission__read__name": "Read"}